#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試FinMind API連接和基本功能
"""

import os
import requests
import pandas as pd
from datetime import datetime, timedelta
import json

class FinMindAPITester:
    """FinMind API測試器"""
    
    def __init__(self):
        self.api_token = os.getenv('FINMIND_API_TOKEN')
        self.base_url = "https://api.finmindtrade.com/api/v4/data"
        self.headers = {'Content-Type': 'application/json'}
        
    def test_api_connection(self):
        """測試API連接"""
        print("🔗 測試FinMind API連接...")
        
        if not self.api_token:
            print("❌ 環境變數 FINMIND_API_TOKEN 未設定")
            return False
        
        print(f"✅ API Token已設定: {self.api_token[:10]}...{self.api_token[-10:]}")
        
        # 測試簡單的API調用
        try:
            params = {
                "dataset": "TaiwanStockInfo",
                "token": self.api_token
            }
            
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    print(f"✅ API連接成功")
                    print(f"📊 獲取到 {len(data.get('data', []))} 筆股票基本資料")
                    return True
                else:
                    print(f"❌ API回應錯誤: {data.get('msg', 'Unknown error')}")
                    return False
            else:
                print(f"❌ HTTP錯誤: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            return False
    
    def test_financial_data(self, stock_id="2330"):
        """測試財務數據獲取"""
        print(f"\n📊 測試財務數據獲取 (股票: {stock_id})...")
        
        datasets_to_test = [
            ("TaiwanStockFinancialStatements", "財務報表"),
            ("TaiwanStockDividend", "股利數據"),
            ("TaiwanStockPER", "本益比數據"),
            ("TaiwanStockPBR", "股價淨值比"),
        ]
        
        results = {}
        
        for dataset, description in datasets_to_test:
            try:
                params = {
                    "dataset": dataset,
                    "data_id": stock_id,
                    "start_date": "2024-01-01",
                    "token": self.api_token
                }
                
                response = requests.get(self.base_url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 200:
                        records = data.get('data', [])
                        results[dataset] = {
                            'success': True,
                            'count': len(records),
                            'description': description,
                            'sample': records[:2] if records else []
                        }
                        print(f"  ✅ {description}: {len(records)} 筆記錄")
                    else:
                        results[dataset] = {
                            'success': False,
                            'error': data.get('msg', 'Unknown error'),
                            'description': description
                        }
                        print(f"  ❌ {description}: {data.get('msg', 'Unknown error')}")
                else:
                    results[dataset] = {
                        'success': False,
                        'error': f"HTTP {response.status_code}",
                        'description': description
                    }
                    print(f"  ❌ {description}: HTTP {response.status_code}")
                    
            except Exception as e:
                results[dataset] = {
                    'success': False,
                    'error': str(e),
                    'description': description
                }
                print(f"  ❌ {description}: {e}")
        
        return results
    
    def test_revenue_data(self, stock_id="2330"):
        """測試營收數據獲取"""
        print(f"\n💰 測試營收數據獲取 (股票: {stock_id})...")
        
        try:
            params = {
                "dataset": "TaiwanStockMonthRevenue",
                "data_id": stock_id,
                "start_date": "2024-01-01",
                "token": self.api_token
            }
            
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    records = data.get('data', [])
                    print(f"  ✅ 營收數據: {len(records)} 筆記錄")
                    
                    if records:
                        latest = records[-1]
                        print(f"  📈 最新營收: {latest.get('date')} - {latest.get('revenue', 0):,} 千元")
                        
                        # 計算營收成長率
                        if len(records) >= 2:
                            current = records[-1]['revenue']
                            previous = records[-2]['revenue']
                            growth = ((current - previous) / previous * 100) if previous > 0 else 0
                            print(f"  📊 月營收成長率: {growth:.2f}%")
                    
                    return True
                else:
                    print(f"  ❌ 營收數據錯誤: {data.get('msg', 'Unknown error')}")
                    return False
            else:
                print(f"  ❌ 營收數據HTTP錯誤: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ 營收數據獲取失敗: {e}")
            return False
    
    def test_institutional_data(self, stock_id="2330"):
        """測試法人數據獲取"""
        print(f"\n🏛️ 測試法人數據獲取 (股票: {stock_id})...")
        
        try:
            # 獲取最近一週的數據
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            params = {
                "dataset": "TaiwanStockInstitutionalInvestors",
                "data_id": stock_id,
                "start_date": start_date,
                "end_date": end_date,
                "token": self.api_token
            }
            
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    records = data.get('data', [])
                    print(f"  ✅ 法人數據: {len(records)} 筆記錄")
                    
                    if records:
                        latest = records[-1]
                        foreign_buy = latest.get('Foreign_Investor_Buy', 0)
                        foreign_sell = latest.get('Foreign_Investor_Sell', 0)
                        foreign_net = foreign_buy - foreign_sell
                        
                        print(f"  📈 外資最新淨買賣: {foreign_net:,} 張")
                        print(f"  📅 數據日期: {latest.get('date')}")
                    
                    return True
                else:
                    print(f"  ❌ 法人數據錯誤: {data.get('msg', 'Unknown error')}")
                    return False
            else:
                print(f"  ❌ 法人數據HTTP錯誤: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ 法人數據獲取失敗: {e}")
            return False
    
    def test_api_quota(self):
        """測試API配額使用情況"""
        print(f"\n📊 測試API配額使用情況...")
        
        try:
            # 連續發送幾個請求來測試配額
            test_requests = 5
            successful_requests = 0
            
            for i in range(test_requests):
                params = {
                    "dataset": "TaiwanStockInfo",
                    "token": self.api_token
                }
                
                response = requests.get(self.base_url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 200:
                        successful_requests += 1
                    else:
                        print(f"  ⚠️ 請求 {i+1} 失敗: {data.get('msg')}")
                        break
                else:
                    print(f"  ⚠️ 請求 {i+1} HTTP錯誤: {response.status_code}")
                    break
            
            print(f"  ✅ 成功請求: {successful_requests}/{test_requests}")
            
            if successful_requests == test_requests:
                print(f"  🎉 API配額正常，可以正常使用")
                return True
            else:
                print(f"  ⚠️ API配額可能有限制")
                return False
                
        except Exception as e:
            print(f"  ❌ 配額測試失敗: {e}")
            return False
    
    def run_full_test(self):
        """執行完整測試"""
        print("🧪 FinMind API 完整測試開始")
        print("=" * 50)
        
        results = {
            'connection': False,
            'financial': False,
            'revenue': False,
            'institutional': False,
            'quota': False
        }
        
        # 1. 測試連接
        results['connection'] = self.test_api_connection()
        
        if results['connection']:
            # 2. 測試財務數據
            financial_results = self.test_financial_data()
            results['financial'] = any(r.get('success', False) for r in financial_results.values())
            
            # 3. 測試營收數據
            results['revenue'] = self.test_revenue_data()
            
            # 4. 測試法人數據
            results['institutional'] = self.test_institutional_data()
            
            # 5. 測試配額
            results['quota'] = self.test_api_quota()
        
        # 總結
        print(f"\n🎯 測試結果總結")
        print("=" * 30)
        
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            print(f"{status} {test_name.upper()}: {'通過' if result else '失敗'}")
        
        success_rate = sum(results.values()) / len(results) * 100
        print(f"\n📊 整體成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 FinMind API 測試通過，可以開始數據整合！")
        elif success_rate >= 60:
            print("⚠️ FinMind API 部分功能可用，建議檢查失敗項目")
        else:
            print("❌ FinMind API 測試失敗，請檢查API Token和網路連接")
        
        return results

def main():
    """主函數"""
    tester = FinMindAPITester()
    results = tester.run_full_test()
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

if __name__ == '__main__':
    main()
