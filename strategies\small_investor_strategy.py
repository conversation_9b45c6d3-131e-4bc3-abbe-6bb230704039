#!/usr/bin/env python3
"""
小資族資優生策略模組 - 優化版
整合財務數據獲取功能並加上排名機制
"""
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from .base_strategy import BaseStrategy


class SmallInvestorEliteStrategy(BaseStrategy):
    """小資族資優生策略 - 基於財務指標和技術分析的綜合策略（優化版）"""

    def __init__(self):
        super().__init__(
            name="小資族資優生策略",
            description="專為小資族設計，基於市值、自由現金流、ROE、營業利益成長率等多重條件的量化選股策略（優化版）",
            strategy_type="quantitative_fundamental"
        )

        # 策略參數（對應原始程式碼）
        self.max_market_value = 1e10      # 市值上限100億
        self.min_free_cash_flow = 0       # 自由現金流下限
        self.min_roe = 0                  # ROE下限
        self.min_operating_growth = 0     # 營業利益成長率下限
        self.max_ps_ratio = 2             # 市值營收比上限
        self.min_volume = 200000          # 最小成交量20萬股
        self.rsv_period = 60              # RSV計算期間
        self.top_stocks = 10              # 選取前N檔股票
        self.rolling_periods = 4          # 自由現金流滾動期間

        # 初始化財務數據提供器
        self.finmind_provider = None
        self._init_data_provider()

    def _init_data_provider(self):
        """初始化財務數據提供器"""
        try:
            from finmind_data_provider import FinMindDataProvider
            self.finmind_provider = FinMindDataProvider()
            logging.info("✅ FinMind數據提供器已初始化")
        except ImportError:
            logging.warning("⚠️ FinMind數據提供器不可用，將使用代理指標")
            self.finmind_provider = None

    def calculate_rsv(self, df):
        """計算RSV指標 (相對強弱值)"""
        try:
            if len(df) < self.rsv_period:
                return None

            close = df['Close']
            high = df['High']
            low = df['Low']

            # 計算RSV = (收盤價 - 最低價) / (最高價 - 最低價)
            rolling_min = low.rolling(self.rsv_period).min()
            rolling_max = high.rolling(self.rsv_period).max()

            rsv = (close - rolling_min) / (rolling_max - rolling_min)
            return rsv.fillna(0)

        except Exception as e:
            logging.error(f"計算RSV失敗: {e}")
            return None

    def calculate_market_value(self, df, stock_id):
        """計算市值 = 股價 × 成交量作為代理指標"""
        try:
            current_price = df['Close'].iloc[-1]
            avg_volume = df['Volume'].rolling(20).mean().iloc[-1]  # 20日平均成交量

            # 使用股價×平均成交量作為市值代理指標
            market_value_proxy = current_price * avg_volume

            # 如果有FinMind數據，嘗試獲取真實市值
            if self.finmind_provider:
                try:
                    financial_data = self.finmind_provider.get_financial_statements(stock_id)
                    if financial_data and 'total_assets' in financial_data:
                        # 使用總資產作為市值參考
                        return financial_data['total_assets'], True
                except Exception as e:
                    logging.debug(f"獲取真實市值失敗: {e}")

            return market_value_proxy, False

        except Exception as e:
            logging.error(f"計算市值失敗: {e}")
            return None, False

    def calculate_financial_metrics(self, stock_id):
        """計算財務指標"""
        try:
            if not self.finmind_provider:
                return None

            # 獲取綜合財務數據
            comprehensive_data = self.finmind_provider.get_comprehensive_data(stock_id)

            if not comprehensive_data or not comprehensive_data.get('is_real_data', False):
                return None

            # 計算自由現金流（如果有現金流數據）
            free_cash_flow = None
            if 'operating_cash_flow' in comprehensive_data and 'investing_cash_flow' in comprehensive_data:
                operating_cf = comprehensive_data.get('operating_cash_flow', 0)
                investing_cf = comprehensive_data.get('investing_cash_flow', 0)
                free_cash_flow = operating_cf + investing_cf

            # 計算營業利益成長率（需要歷史數據）
            operating_growth = self._calculate_operating_growth(stock_id)

            return {
                'market_value': comprehensive_data.get('total_assets', 0),  # 使用總資產作為市值參考
                'free_cash_flow': free_cash_flow,
                'roe': comprehensive_data.get('roe', 0),
                'operating_growth': operating_growth,
                'revenue': comprehensive_data.get('revenue', 0),
                'net_income': comprehensive_data.get('net_income', 0),
                'is_real_data': True
            }

        except Exception as e:
            logging.error(f"計算財務指標失敗: {e}")
            return None

    def _calculate_operating_growth(self, stock_id):
        """計算營業利益成長率"""
        try:
            if not self.finmind_provider:
                return 0

            # 獲取營收數據來估算成長率
            revenue_data = self.finmind_provider.get_revenue_data(stock_id, months=12)
            if revenue_data and 'yoy_growth_rate' in revenue_data:
                return revenue_data['yoy_growth_rate']

            return 0

        except Exception as e:
            logging.debug(f"計算營業利益成長率失敗: {e}")
            return 0

    def check_market_value_condition(self, df, stock_id, financial_metrics=None):
        """檢查市值條件：市值 < 100億"""
        try:
            # 優先使用真實財務數據
            if financial_metrics and financial_metrics.get('is_real_data', False):
                market_value = financial_metrics.get('market_value', 0)
                if market_value > 0:
                    if market_value < self.max_market_value:
                        return True, f"✅ 市值 {market_value/1e8:.1f}億 < 100億 (真實數據)"
                    else:
                        return False, f"❌ 市值 {market_value/1e8:.1f}億 >= 100億 (真實數據)"

            # 使用代理指標
            market_value_proxy, is_real = self.calculate_market_value(df, stock_id)
            if market_value_proxy is None:
                return False, "❌ 無法計算市值"

            # 對代理指標使用較寬鬆的標準
            proxy_threshold = self.max_market_value * 0.1  # 代理指標閾值調整

            if market_value_proxy < proxy_threshold:
                data_type = "真實數據" if is_real else "代理指標"
                return True, f"✅ 市值代理 {market_value_proxy/1e6:.1f}萬 < {proxy_threshold/1e6:.1f}萬 ({data_type})"
            else:
                data_type = "真實數據" if is_real else "代理指標"
                return False, f"❌ 市值代理 {market_value_proxy/1e6:.1f}萬 >= {proxy_threshold/1e6:.1f}萬 ({data_type})"

        except Exception as e:
            return False, f"市值條件檢查失敗: {str(e)}"

    def check_free_cash_flow_condition(self, financial_metrics=None):
        """檢查自由現金流條件：自由現金流 > 0"""
        try:
            # 使用真實財務數據
            if financial_metrics and financial_metrics.get('is_real_data', False):
                fcf = financial_metrics.get('free_cash_flow')
                if fcf is not None:
                    if fcf > self.min_free_cash_flow:
                        return True, f"✅ 自由現金流 {fcf/1e8:.2f}億 > 0 (真實數據)"
                    else:
                        return False, f"❌ 自由現金流 {fcf/1e8:.2f}億 <= 0 (真實數據)"

            # 如果沒有真實數據，使用寬鬆標準通過
            # 因為自由現金流數據較難獲取，暫時放寬條件
            return True, "⚠️ 自由現金流條件通過 (缺少真實數據，使用寬鬆標準)"

        except Exception as e:
            return False, f"自由現金流條件檢查失敗: {str(e)}"

    def check_roe_condition(self, financial_metrics=None):
        """檢查ROE條件：股東權益報酬率 > 0"""
        try:
            # 使用真實財務數據
            if financial_metrics and financial_metrics.get('is_real_data', False):
                roe = financial_metrics.get('roe', 0)
                if roe > self.min_roe:
                    return True, f"✅ ROE {roe:.2f}% > 0 (真實數據)"
                else:
                    return False, f"❌ ROE {roe:.2f}% <= 0 (真實數據)"

            # 如果沒有真實數據，使用寬鬆標準通過
            return True, "⚠️ ROE條件通過 (缺少真實數據，使用寬鬆標準)"

        except Exception as e:
            return False, f"ROE條件檢查失敗: {str(e)}"

    def check_operating_growth_condition(self, financial_metrics=None):
        """檢查營業利益成長率條件：營業利益成長率 > 0"""
        try:
            # 使用真實財務數據
            if financial_metrics and financial_metrics.get('is_real_data', False):
                growth = financial_metrics.get('operating_growth', 0)
                if growth > self.min_operating_growth:
                    return True, f"✅ 營業利益成長率 {growth:.2f}% > 0 (真實數據)"
                else:
                    return False, f"❌ 營業利益成長率 {growth:.2f}% <= 0 (真實數據)"

            # 如果沒有真實數據，使用寬鬆標準通過
            return True, "⚠️ 營業利益成長率條件通過 (缺少真實數據，使用寬鬆標準)"

        except Exception as e:
            return False, f"營業利益成長率條件檢查失敗: {str(e)}"

    def check_ps_ratio_condition(self, df, stock_id, financial_metrics=None):
        """檢查市值營收比條件：市值營收比 < 2"""
        try:
            # 使用真實財務數據
            if financial_metrics and financial_metrics.get('is_real_data', False):
                market_value = financial_metrics.get('market_value', 0)
                revenue = financial_metrics.get('revenue', 0)

                if market_value > 0 and revenue > 0:
                    ps_ratio = market_value / (revenue * 4)  # 假設為季度營收，年化
                    if ps_ratio < self.max_ps_ratio:
                        return True, f"✅ 市值營收比 {ps_ratio:.2f} < 2 (真實數據)"
                    else:
                        return False, f"❌ 市值營收比 {ps_ratio:.2f} >= 2 (真實數據)"

            # 使用代理指標：股價相對於成交量
            try:
                current_price = df['Close'].iloc[-1]
                avg_volume = df['Volume'].rolling(20).mean().iloc[-1]

                # 使用股價/平均成交量作為市值營收比代理
                if avg_volume > 0:
                    ps_proxy = current_price / (avg_volume / 1000000)  # 標準化
                    if ps_proxy < self.max_ps_ratio * 10:  # 調整閾值
                        return True, f"✅ 市值營收比代理 {ps_proxy:.2f} < {self.max_ps_ratio * 10} (代理指標)"
                    else:
                        return False, f"❌ 市值營收比代理 {ps_proxy:.2f} >= {self.max_ps_ratio * 10} (代理指標)"

            except Exception:
                pass

            # 如果都無法計算，使用寬鬆標準通過
            return True, "⚠️ 市值營收比條件通過 (缺少數據，使用寬鬆標準)"

        except Exception as e:
            return False, f"市值營收比條件檢查失敗: {str(e)}"

    def check_volume_condition(self, df):
        """檢查成交量條件：成交量 > 20萬股"""
        try:
            current_volume = df['Volume'].iloc[-1]

            if current_volume > self.min_volume:
                return True, f"成交量 {current_volume/1000:.0f}張 > 200張"
            else:
                return False, f"成交量 {current_volume/1000:.0f}張 <= 200張"

        except Exception as e:
            return False, f"成交量檢查失敗: {str(e)}"

    def check_rsv_ranking_condition(self, df, stock_id=None):
        """檢查RSV排名條件：使用RSV進行排名篩選"""
        try:
            rsv = self.calculate_rsv(df)

            if rsv is None:
                return False, "❌ RSV計算失敗 - 數據不足"

            current_rsv = rsv.iloc[-1]

            # RSV技術指標評分
            if current_rsv > 0.8:
                score = "優秀"
                return True, f"✅ RSV值 {current_rsv:.3f} > 0.8 (技術面{score})"
            elif current_rsv > 0.6:
                score = "良好"
                return True, f"✅ RSV值 {current_rsv:.3f} > 0.6 (技術面{score})"
            elif current_rsv > 0.4:
                score = "普通"
                return True, f"⚠️ RSV值 {current_rsv:.3f} > 0.4 (技術面{score})"
            else:
                score = "較弱"
                return False, f"❌ RSV值 {current_rsv:.3f} <= 0.4 (技術面{score})"

        except Exception as e:
            return False, f"RSV排名檢查失敗: {str(e)}"

    def calculate_comprehensive_score(self, df, stock_id, financial_metrics=None):
        """計算綜合評分用於排名"""
        try:
            score = 0
            max_score = 100
            details = {}

            # 1. 市值條件 (20分)
            market_pass, market_reason = self.check_market_value_condition(df, stock_id, financial_metrics)
            market_score = 20 if market_pass else 0
            score += market_score
            details['市值'] = {'score': market_score, 'reason': market_reason}

            # 2. 自由現金流條件 (15分)
            fcf_pass, fcf_reason = self.check_free_cash_flow_condition(financial_metrics)
            fcf_score = 15 if fcf_pass else 0
            score += fcf_score
            details['自由現金流'] = {'score': fcf_score, 'reason': fcf_reason}

            # 3. ROE條件 (15分)
            roe_pass, roe_reason = self.check_roe_condition(financial_metrics)
            roe_score = 15 if roe_pass else 0
            score += roe_score
            details['ROE'] = {'score': roe_score, 'reason': roe_reason}

            # 4. 營業利益成長率條件 (15分)
            growth_pass, growth_reason = self.check_operating_growth_condition(financial_metrics)
            growth_score = 15 if growth_pass else 0
            score += growth_score
            details['營業利益成長率'] = {'score': growth_score, 'reason': growth_reason}

            # 5. 市值營收比條件 (15分)
            ps_pass, ps_reason = self.check_ps_ratio_condition(df, stock_id, financial_metrics)
            ps_score = 15 if ps_pass else 0
            score += ps_score
            details['市值營收比'] = {'score': ps_score, 'reason': ps_reason}

            # 6. 成交量條件 (10分)
            volume_pass, volume_reason = self.check_volume_condition(df)
            volume_score = 10 if volume_pass else 0
            score += volume_score
            details['成交量'] = {'score': volume_score, 'reason': volume_reason}

            # 7. RSV技術指標 (10分)
            rsv_pass, rsv_reason = self.check_rsv_ranking_condition(df, stock_id)
            rsv_score = 10 if rsv_pass else 0
            score += rsv_score
            details['RSV技術指標'] = {'score': rsv_score, 'reason': rsv_reason}

            return score, details

        except Exception as e:
            logging.error(f"計算綜合評分失敗: {e}")
            return 0, {}

    def analyze_stock(self, df, stock_id=None, market_data=None, financial_data=None, revenue_data=None):
        """分析單一股票是否符合小資族資優生策略（優化版）"""
        try:
            if df.empty or len(df) < self.rsv_period:
                return {
                    'suitable': False,
                    'reason': f'❌ 數據不足，需要至少{self.rsv_period}日價格數據',
                    'details': {},
                    'score': 0,
                    'comprehensive_score': 0,
                    'strategy_name': self.name
                }

            # 獲取財務指標
            financial_metrics = None
            if stock_id:
                financial_metrics = self.calculate_financial_metrics(stock_id)

            # 計算綜合評分
            comprehensive_score, score_details = self.calculate_comprehensive_score(df, stock_id, financial_metrics)

            # 統計通過的條件
            passed_conditions = []
            failed_conditions = []

            for condition, detail in score_details.items():
                if detail['score'] > 0:
                    passed_conditions.append(condition)
                else:
                    failed_conditions.append(condition)

            # 判斷是否適合（需要至少70分）
            min_score_threshold = 70
            suitable = comprehensive_score >= min_score_threshold

            # 生成詳細原因
            data_quality = "真實數據" if (financial_metrics and financial_metrics.get('is_real_data', False)) else "代理指標"

            reason = f"小資族資優生綜合評分: {comprehensive_score}/100 ({data_quality})"

            if passed_conditions:
                reason += f" | ✅通過: {', '.join(passed_conditions)}"

            if failed_conditions:
                reason += f" | ❌未通過: {', '.join(failed_conditions)}"

            if suitable:
                reason += f" | 🎯 評分達標，適合投資"
            else:
                reason += f" | ⚠️ 評分未達{min_score_threshold}分門檻"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': score_details,
                'score': len(passed_conditions),  # 傳統評分（通過條件數）
                'comprehensive_score': comprehensive_score,  # 新的綜合評分
                'strategy_name': self.name,
                'data_quality': data_quality,
                'financial_metrics': financial_metrics
            }

        except Exception as e:
            logging.error(f"小資族資優生策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'❌ 分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'comprehensive_score': 0,
                'strategy_name': self.name
            }

    def analyze_multiple_stocks(self, stock_data_dict, top_n=10):
        """
        批量分析多檔股票並進行排名

        Args:
            stock_data_dict: {stock_id: df} 格式的股票數據字典
            top_n: 返回前N檔股票

        Returns:
            排名後的股票分析結果列表
        """
        try:
            results = []

            logging.info(f"🔍 開始分析 {len(stock_data_dict)} 檔股票...")

            for stock_id, df in stock_data_dict.items():
                try:
                    result = self.analyze_stock(df, stock_id=stock_id)
                    result['stock_id'] = stock_id
                    results.append(result)

                    # 顯示進度
                    if len(results) % 10 == 0:
                        logging.info(f"📊 已分析 {len(results)}/{len(stock_data_dict)} 檔股票")

                except Exception as e:
                    logging.warning(f"⚠️ 分析股票 {stock_id} 失敗: {e}")
                    continue

            # 按綜合評分排序
            results.sort(key=lambda x: x['comprehensive_score'], reverse=True)

            # 返回前N檔
            top_results = results[:top_n]

            logging.info(f"🏆 完成分析，選出前 {len(top_results)} 檔優質股票")

            return top_results

        except Exception as e:
            logging.error(f"批量分析失敗: {e}")
            return []

    def generate_ranking_report(self, analysis_results):
        """生成排名報告"""
        try:
            if not analysis_results:
                return "❌ 沒有分析結果"

            report = []
            report.append("🏆 小資族資優生策略 - 股票排名報告")
            report.append("=" * 60)
            report.append(f"📊 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append(f"📈 分析股票數: {len(analysis_results)} 檔")
            report.append("")

            # 排名列表
            medals = ["🥇", "🥈", "🥉"] + [f"{i}️⃣" for i in range(4, 11)]

            for i, result in enumerate(analysis_results):
                medal = medals[i] if i < len(medals) else f"{i+1}."
                stock_id = result.get('stock_id', 'N/A')
                score = result.get('comprehensive_score', 0)
                suitable = "✅適合" if result.get('suitable', False) else "❌不適合"
                data_quality = result.get('data_quality', '未知')

                report.append(f"{medal} 股票代碼: {stock_id}")
                report.append(f"   💯 綜合評分: {score}/100")
                report.append(f"   🎯 投資建議: {suitable}")
                report.append(f"   📊 數據品質: {data_quality}")

                # 顯示各項評分
                details = result.get('details', {})
                if details:
                    report.append("   📋 詳細評分:")
                    for condition, detail in details.items():
                        condition_score = detail.get('score', 0)
                        reason = detail.get('reason', '')
                        report.append(f"      • {condition}: {condition_score}分 - {reason}")

                report.append("")

            # 統計摘要
            suitable_count = sum(1 for r in analysis_results if r.get('suitable', False))
            avg_score = sum(r.get('comprehensive_score', 0) for r in analysis_results) / len(analysis_results)

            report.append("📊 統計摘要")
            report.append("-" * 30)
            report.append(f"✅ 適合投資: {suitable_count} 檔 ({suitable_count/len(analysis_results)*100:.1f}%)")
            report.append(f"📈 平均評分: {avg_score:.1f}/100")
            report.append(f"🏆 最高評分: {analysis_results[0].get('comprehensive_score', 0)}/100")

            return "\n".join(report)

        except Exception as e:
            logging.error(f"生成排名報告失敗: {e}")
            return f"❌ 報告生成失敗: {str(e)}"