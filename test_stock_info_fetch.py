#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股票資訊獲取
"""

import requests
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_stock_info_fetch():
    """測試股票資訊獲取"""
    
    print("=" * 60)
    print("🧪 測試股票資訊獲取")
    print("=" * 60)
    
    try:
        # 獲取上市股票資訊
        url_twse = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=2"
        print(f"📡 請求 URL: {url_twse}")
        
        response = requests.get(url_twse, timeout=30, verify=False)
        response.encoding = 'big5'
        
        print(f"📊 回應狀態: {response.status_code}")
        print(f"📊 回應長度: {len(response.text)}")
        
        if response.status_code == 200:
            # 檢查回應內容
            lines = response.text.split('\n')
            print(f"📊 總行數: {len(lines)}")
            
            # 找到包含股票的行
            stock_lines = []
            for i, line in enumerate(lines[:50]):  # 只檢查前50行
                if '股票' in line:
                    stock_lines.append((i, line))
                    print(f"第{i}行包含'股票': {line[:100]}...")
            
            print(f"\n📊 找到 {len(stock_lines)} 行包含'股票'")
            
            # 嘗試解析第一個股票行
            if stock_lines:
                line = stock_lines[0][1]
                parts = line.split('\t')
                print(f"\n📋 第一個股票行解析:")
                print(f"   分割後部分數: {len(parts)}")
                for i, part in enumerate(parts[:10]):
                    print(f"   部分{i}: '{part}'")
                
                # 嘗試提取股票代碼和名稱
                if len(parts) > 0 and len(parts[0].split()) >= 2:
                    stock_code = parts[0].split()[0]
                    stock_name = parts[0].split()[1]
                    industry = parts[6] if len(parts) > 6 else 'ETF'
                    
                    print(f"\n✅ 解析結果:")
                    print(f"   股票代碼: {stock_code}")
                    print(f"   股票名稱: {stock_name}")
                    print(f"   產業別: {industry}")
                    print(f"   是否為數字: {stock_code.isdigit()}")
        else:
            print(f"❌ 請求失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stock_info_fetch()
