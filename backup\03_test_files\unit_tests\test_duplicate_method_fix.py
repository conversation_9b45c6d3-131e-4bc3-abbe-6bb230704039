#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試重複方法修復
"""

import ast
import sys

def test_duplicate_method_fix():
    """測試重複方法修復"""
    
    print("🔧 測試重複方法修復")
    print("=" * 50)
    
    # 1. 檢查是否還有重複的run_strategy方法
    print("1️⃣ 檢查是否還有重複的run_strategy方法...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST來查找方法定義
        tree = ast.parse(content)
        
        run_strategy_methods = []
        
        class MethodVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                if node.name == 'run_strategy':
                    run_strategy_methods.append({
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args]
                    })
                self.generic_visit(node)
        
        visitor = MethodVisitor()
        visitor.visit(tree)
        
        print(f"   📊 找到 {len(run_strategy_methods)} 個 run_strategy 方法:")
        for i, method in enumerate(run_strategy_methods):
            print(f"      {i+1}. 第 {method['line']} 行: {method['name']}({', '.join(method['args'])})")
        
        if len(run_strategy_methods) == 1:
            print(f"   ✅ 重複方法已成功刪除，只保留一個 run_strategy 方法")
        elif len(run_strategy_methods) > 1:
            print(f"   ❌ 仍然存在 {len(run_strategy_methods)} 個重複的 run_strategy 方法")
            return False
        else:
            print(f"   ❌ 沒有找到 run_strategy 方法")
            return False
        
    except Exception as e:
        print(f"   ❌ 檢查失敗: {e}")
        return False
    
    # 2. 檢查保留的方法是否包含詳細信息收集邏輯
    print("\n2️⃣ 檢查保留的方法是否包含詳細信息收集邏輯...")
    
    try:
        # 查找詳細信息收集的關鍵代碼
        key_patterns = [
            "detailed_info_dict = {}",
            "processed_conditions = []",
            "為高殖利率烏龜策略添加詳細信息",
            "detailed_info_dict.get('score', 0)",
            "detailed_info_dict.get('dividend_yield', 0)"
        ]
        
        found_patterns = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            for pattern in key_patterns:
                if pattern in line:
                    found_patterns.append({
                        'pattern': pattern,
                        'line': i + 1,
                        'content': line.strip()
                    })
        
        print(f"   📊 找到 {len(found_patterns)} 個關鍵模式:")
        for pattern_info in found_patterns:
            print(f"      ✓ 第 {pattern_info['line']} 行: {pattern_info['pattern']}")
        
        if len(found_patterns) >= 4:  # 至少要有4個關鍵模式
            print(f"   ✅ 保留的方法包含完整的詳細信息收集邏輯")
        else:
            print(f"   ❌ 保留的方法缺少詳細信息收集邏輯")
            return False
        
    except Exception as e:
        print(f"   ❌ 檢查失敗: {e}")
        return False
    
    # 3. 檢查是否有語法錯誤
    print("\n3️⃣ 檢查是否有語法錯誤...")
    
    try:
        # 嘗試編譯代碼
        compile(content, 'O3mh_gui_v21_optimized.py', 'exec')
        print(f"   ✅ 代碼語法正確，沒有語法錯誤")
    except SyntaxError as e:
        print(f"   ❌ 發現語法錯誤: 第 {e.lineno} 行: {e.msg}")
        return False
    except Exception as e:
        print(f"   ❌ 編譯檢查失敗: {e}")
        return False
    
    # 4. 檢查修復後的代碼結構
    print("\n4️⃣ 檢查修復後的代碼結構...")
    
    try:
        # 檢查類結構
        class ClassVisitor(ast.NodeVisitor):
            def __init__(self):
                self.classes = []
                self.methods_per_class = {}
            
            def visit_ClassDef(self, node):
                self.classes.append(node.name)
                methods = []
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods.append(item.name)
                self.methods_per_class[node.name] = methods
                self.generic_visit(node)
        
        visitor = ClassVisitor()
        visitor.visit(tree)
        
        print(f"   📊 找到 {len(visitor.classes)} 個類:")
        for class_name in visitor.classes:
            methods = visitor.methods_per_class[class_name]
            run_strategy_count = methods.count('run_strategy')
            print(f"      {class_name}: {len(methods)} 個方法 (run_strategy: {run_strategy_count})")
            
            if run_strategy_count > 1:
                print(f"         ❌ 類 {class_name} 中仍有 {run_strategy_count} 個重複的 run_strategy 方法")
                return False
            elif run_strategy_count == 1:
                print(f"         ✅ 類 {class_name} 中有 1 個 run_strategy 方法")
        
    except Exception as e:
        print(f"   ❌ 結構檢查失敗: {e}")
        return False
    
    print(f"\n" + "=" * 50)
    print(f"✅ 重複方法修復測試完成！")
    
    return True

if __name__ == "__main__":
    success = test_duplicate_method_fix()
    
    if success:
        print(f"\n🎉 修復成功總結:")
        print(f"✅ 重複的 run_strategy 方法已成功刪除")
        print(f"✅ 保留的方法包含完整的詳細信息收集邏輯")
        print(f"✅ 代碼語法正確，沒有錯誤")
        print(f"✅ 類結構正常，沒有重複方法")
        print(f"")
        print(f"🚀 現在請重新啟動GUI程序:")
        print(f"1. 關閉當前的GUI程序")
        print(f"2. 重新運行 python O3mh_gui_v21_optimized.py")
        print(f"3. 選擇「高殖利率烏龜策略」並執行篩選")
        print(f"4. 右側表格現在應該會顯示正確的詳細數據")
    else:
        print(f"\n❌ 修復存在問題，需要進一步檢查")
