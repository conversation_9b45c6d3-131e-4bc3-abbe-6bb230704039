#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正式測試爬蟲功能 - 從 2022-09-02 開始抓取 5 天資料
"""

import sys
import os
from datetime import datetime, timedelta
import sqlite3

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def formal_crawler_test():
    """正式測試爬蟲功能"""
    
    print("=" * 80)
    print("🚀 正式測試爬蟲功能 - 從 2022-09-02 開始抓取 5 天資料")
    print("=" * 80)
    
    # 設定測試參數
    start_date = datetime(2022, 9, 2)  # newprice.db 的最後一天
    test_days = 5
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    
    # 檢查起始狀態
    print("📊 檢查起始狀態...")
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    # 檢查最後一天的資料
    cursor.execute('''
        SELECT date, COUNT(*) as records, COUNT(DISTINCT stock_id) as stocks
        FROM stock_daily_data 
        WHERE date = "2022-09-02"
    ''')
    last_day_data = cursor.fetchone()
    
    if last_day_data and last_day_data[1] > 0:
        print(f"✅ 確認最後一天資料: {last_day_data[0]} - {last_day_data[1]:,} 筆記錄, {last_day_data[2]:,} 檔股票")
    else:
        print(f"❌ 找不到 2022-09-02 的資料")
        conn.close()
        return
    
    # 檢查是否已有後續資料
    cursor.execute('''
        SELECT date, COUNT(*) as records
        FROM stock_daily_data 
        WHERE date > "2022-09-02"
        ORDER BY date
    ''')
    future_data = cursor.fetchall()
    
    if future_data:
        print(f"⚠️ 發現 {len(future_data)} 天的後續資料:")
        for date, records in future_data:
            print(f"   {date}: {records:,} 筆記錄")
        
        confirm = input("\n是否要刪除這些資料重新測試？(輸入 'YES' 確認): ")
        if confirm == 'YES':
            cursor.execute('DELETE FROM stock_daily_data WHERE date > "2022-09-02"')
            conn.commit()
            print(f"✅ 已清除後續資料")
        else:
            print(f"❌ 取消測試")
            conn.close()
            return
    
    conn.close()
    
    # 開始測試爬取
    print(f"\n🔄 開始測試爬取 {test_days} 天資料...")
    print(f"📅 起始日期: {start_date.strftime('%Y-%m-%d')}")
    
    try:
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        success_days = 0
        failed_days = 0
        total_records = 0
        
        for i in range(1, test_days + 1):
            test_date = start_date + timedelta(days=i)
            print(f"\n📅 第 {i} 天: {test_date.strftime('%Y-%m-%d')} ({test_date.strftime('%A')})")
            
            try:
                # 爬取資料
                print(f"   🔄 爬取資料...")
                df = crawl_price(test_date)
                
                if df is not None and not df.empty:
                    records_count = len(df)
                    print(f"   ✅ 爬取成功: {records_count:,} 筆資料")
                    
                    # 檢查資料品質
                    df_reset = df.reset_index()
                    
                    # 檢查是否有權證
                    warrant_count = len(df_reset[df_reset['stock_id'].str.startswith('7')])
                    if warrant_count == 0:
                        print(f"   ✅ 權證過濾正常: 無權證資料")
                    else:
                        print(f"   ⚠️ 發現 {warrant_count} 筆權證資料")
                    
                    # 檢查股票資訊完整性
                    with_name = len(df_reset[df_reset['stock_name'] != ''])
                    with_status = len(df_reset[df_reset['listing_status'] != ''])
                    print(f"   📊 股票資訊: {with_name:,} 有名稱, {with_status:,} 有狀態")
                    
                    # 保存到資料庫
                    print(f"   💾 保存到資料庫...")
                    save_success = save_price_to_newprice_database(df, newprice_db)
                    
                    if save_success:
                        print(f"   ✅ 保存成功")
                        success_days += 1
                        total_records += records_count
                    else:
                        print(f"   ❌ 保存失敗")
                        failed_days += 1
                        
                else:
                    print(f"   ❌ 爬取失敗或無資料")
                    failed_days += 1
                    
            except Exception as e:
                print(f"   ❌ 處理失敗: {e}")
                failed_days += 1
        
        # 測試結果總結
        print(f"\n" + "=" * 60)
        print(f"📊 測試結果總結")
        print(f"=" * 60)
        print(f"✅ 成功天數: {success_days}/{test_days}")
        print(f"❌ 失敗天數: {failed_days}/{test_days}")
        print(f"📊 總記錄數: {total_records:,}")
        print(f"📈 成功率: {success_days/test_days*100:.1f}%")
        
        # 驗證資料庫狀態
        print(f"\n📊 驗證資料庫最終狀態...")
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 檢查新增的資料
        cursor.execute('''
            SELECT date, COUNT(*) as records, COUNT(DISTINCT stock_id) as stocks
            FROM stock_daily_data 
            WHERE date > "2022-09-02"
            ORDER BY date
        ''')
        new_data = cursor.fetchall()
        
        if new_data:
            print(f"✅ 新增資料驗證:")
            for date, records, stocks in new_data:
                print(f"   {date}: {records:,} 筆記錄, {stocks:,} 檔股票")
        else:
            print(f"❌ 沒有新增任何資料")
        
        # 檢查資料連續性
        cursor.execute('''
            SELECT date, COUNT(*) as records
            FROM stock_daily_data 
            WHERE date >= "2022-09-02"
            ORDER BY date
        ''')
        all_data = cursor.fetchall()
        
        print(f"\n📅 資料連續性檢查:")
        for i, (date, records) in enumerate(all_data):
            status = "✅" if records > 2000 else "⚠️" if records > 0 else "❌"
            day_type = "起始日" if i == 0 else f"第{i}天"
            print(f"   {date}: {records:,} 筆 {status} ({day_type})")
        
        # 檢查股票資訊品質
        if new_data:
            print(f"\n📊 股票資訊品質檢查:")
            cursor.execute('''
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN stock_name != '' THEN 1 ELSE 0 END) as with_name,
                    SUM(CASE WHEN listing_status != '' THEN 1 ELSE 0 END) as with_status,
                    SUM(CASE WHEN stock_id LIKE '7%' THEN 1 ELSE 0 END) as warrants
                FROM stock_daily_data 
                WHERE date > "2022-09-02"
            ''')
            quality_data = cursor.fetchone()
            
            if quality_data:
                total, with_name, with_status, warrants = quality_data
                print(f"   總記錄: {total:,}")
                print(f"   有股票名稱: {with_name:,} ({with_name/total*100:.1f}%)")
                print(f"   有上市狀態: {with_status:,} ({with_status/total*100:.1f}%)")
                print(f"   權證數量: {warrants:,} {'✅ 正常' if warrants == 0 else '❌ 異常'}")
        
        conn.close()
        
        # 最終評估
        print(f"\n🎯 最終評估:")
        if success_days == test_days:
            print(f"🎉 測試完全成功！爬蟲功能正常運作")
        elif success_days >= test_days * 0.8:
            print(f"✅ 測試大部分成功，爬蟲功能基本正常")
        else:
            print(f"⚠️ 測試成功率較低，需要檢查爬蟲問題")
        
        print(f"\n✅ 正式測試完成！")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    formal_crawler_test()
