#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 monthly_report 爬蟲功能
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

from crawler import crawl_monthly_report
import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_monthly_report():
    """測試 monthly_report 爬蟲"""
    print("🧪 測試 monthly_report 爬蟲")
    print("=" * 50)
    
    # 測試最近的月份 (月營收通常在次月10日發布)
    # 測試 2025年6月的資料 (應該在7月10日發布)
    test_date = datetime.datetime(2025, 7, 10)  # 2025年6月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 {test_date.month-1} 月營收)")
    
    try:
        print("🔄 開始爬取...")
        result = crawl_monthly_report(test_date)
        
        if result is not None and not result.empty:
            print(f"✅ 爬取成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            return True
        else:
            print("⚠️ 爬取成功但無資料 (可能該月份資料尚未發布)")
            return False
            
    except Exception as e:
        print(f"❌ 爬取失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_older_monthly_report():
    """測試較舊的月營收資料 (確定已發布)"""
    print(f"\n🧪 測試較舊的 monthly_report 資料")
    print("=" * 50)
    
    # 測試2024年12月的資料 (應該已經發布)
    test_date = datetime.datetime(2025, 1, 10)  # 2024年12月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年12月營收)")
    
    try:
        print("🔄 開始爬取...")
        result = crawl_monthly_report(test_date)
        
        if result is not None and not result.empty:
            print(f"✅ 爬取成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 檢查一些知名股票
            if '2330 台積電' in result.index.get_level_values('stock_id'):
                tsmc_data = result.loc['2330 台積電']
                print(f"   台積電營收: {tsmc_data['當月營收'].iloc[0]:,}")
            
            return True
        else:
            print("⚠️ 爬取成功但無資料")
            return False
            
    except Exception as e:
        print(f"❌ 爬取失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("🔧 Monthly Report 爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 測試月營收爬蟲是否正常工作")
    print("=" * 60)
    
    # 測試1: 最新月份
    recent_success = test_monthly_report()
    
    # 測試2: 較舊月份 (確定已發布)
    older_success = test_older_monthly_report()
    
    print(f"\n📊 測試結果:")
    print(f"   最新月份: {'✅ 成功' if recent_success else '❌ 失敗'}")
    print(f"   較舊月份: {'✅ 成功' if older_success else '❌ 失敗'}")
    
    if recent_success or older_success:
        print(f"\n🎉 monthly_report 爬蟲可以正常工作!")
        print(f"💡 可以在 auto_update.py 中安全使用")
    else:
        print(f"\n⚠️ monthly_report 爬蟲有問題")
        print(f"💡 可能需要檢查網路連接或 API 變更")

if __name__ == "__main__":
    main()
