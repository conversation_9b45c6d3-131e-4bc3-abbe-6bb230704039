#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的Yahoo股價測試 - 基於您提供的範例程式碼
"""

import requests
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor

def get_single_stock(stock_code):
    """獲取單一股票價格 - 基於您的範例"""
    try:
        url = f'https://tw.stock.yahoo.com/quote/{stock_code}'
        web = requests.get(url)
        soup = BeautifulSoup(web.text, "html.parser")
        
        title = soup.find('h1')
        a = soup.select('.Fz\\(32px\\)')[0]  # 價格 (轉義括號)
        b = soup.select('.Fz\\(20px\\)')[0]  # 漲跌 (轉義括號)
        
        s = ''  # 漲跌狀態
        try:
            if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C\\(\\$c-trend-down\\)')[0]:
                s = '-'
        except:
            try:
                if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C\\(\\$c-trend-up\\)')[0]:
                    s = '+'
            except:
                s = ''
        
        result = {
            'stock_code': stock_code,
            'stock_name': title.get_text().strip(),
            'current_price': a.get_text().strip(),
            'change_text': b.get_text().strip(),
            'trend': s,
            'full_text': f'{title.get_text()} : {a.get_text()} ( {s}{b.get_text()} )'
        }
        
        return result
        
    except Exception as e:
        print(f"❌ 獲取股票 {stock_code} 失敗: {e}")
        return None

def get_multiple_stocks(stock_codes):
    """獲取多支股票價格 - 基於您的範例"""
    
    def getStock(stock_code):
        return get_single_stock(stock_code)
    
    results = []
    with ThreadPoolExecutor() as executor:
        futures = {executor.submit(getStock, code): code for code in stock_codes}
        
        for future in futures:
            try:
                result = future.result()
                if result:
                    results.append(result)
                    print(result['full_text'])
            except Exception as e:
                stock_code = futures[future]
                print(f"❌ 股票 {stock_code} 處理失敗: {e}")
    
    return results

def test_yahoo_stock_crawler():
    """測試Yahoo股價爬蟲"""
    print("🧪 測試Yahoo股價爬蟲 - 基於您的範例程式碼")
    print("=" * 60)
    
    # 測試1: 單一股票
    print("\n1️⃣ 測試單一股票 (台積電 2330)")
    print("-" * 40)
    
    result = get_single_stock('2330')
    if result:
        print("✅ 成功獲取台積電資料:")
        print(f"  股票代碼: {result['stock_code']}")
        print(f"  股票名稱: {result['stock_name']}")
        print(f"  現在價格: {result['current_price']}")
        print(f"  漲跌資訊: {result['change_text']}")
        print(f"  趨勢符號: {result['trend']}")
        print(f"  完整資訊: {result['full_text']}")
    else:
        print("❌ 無法獲取台積電資料")
        return False
    
    # 測試2: 多支股票
    print("\n2️⃣ 測試多支股票")
    print("-" * 40)
    
    test_stocks = ['2330', '2317', '2454', '2412', '0050']
    print(f"測試股票: {', '.join(test_stocks)}")
    print()
    
    results = get_multiple_stocks(test_stocks)
    
    print(f"\n✅ 成功獲取 {len(results)}/{len(test_stocks)} 支股票資料")
    
    if len(results) >= len(test_stocks) * 0.8:  # 80%成功率
        print("🎉 測試通過！Yahoo股價爬蟲運作正常")
        return True
    else:
        print("⚠️ 成功率偏低，可能需要檢查網路或Yahoo網站狀態")
        return False

def test_detailed_parsing():
    """測試詳細的資料解析"""
    print("\n3️⃣ 測試詳細資料解析")
    print("-" * 40)
    
    try:
        stock_code = '2330'
        url = f'https://tw.stock.yahoo.com/quote/{stock_code}'
        
        print(f"📡 請求URL: {url}")
        
        web = requests.get(url)
        print(f"✅ 請求成功，狀態碼: {web.status_code}")
        print(f"📄 頁面內容長度: {len(web.text)}")
        
        soup = BeautifulSoup(web.text, "html.parser")
        
        # 檢查各個元素
        print("\n🔍 檢查頁面元素:")
        
        # 標題
        title = soup.find('h1')
        if title:
            print(f"  📊 標題: {title.get_text().strip()}")
        else:
            print("  ❌ 未找到標題")
        
        # 價格元素
        price_elements = soup.select('.Fz\\(32px\\)')
        if price_elements:
            print(f"  💰 價格元素: {price_elements[0].get_text().strip()}")
        else:
            print("  ❌ 未找到價格元素")

        # 漲跌元素
        change_elements = soup.select('.Fz\\(20px\\)')
        if change_elements:
            print(f"  📈 漲跌元素: {change_elements[0].get_text().strip()}")
        else:
            print("  ❌ 未找到漲跌元素")
        
        # 主要容器
        main_element = soup.select('#main-0-QuoteHeader-Proxy')
        if main_element:
            print(f"  🎯 主要容器: 找到")
            
            # 檢查趨勢
            down_elements = main_element[0].select('.C\\(\\$c-trend-down\\)')
            up_elements = main_element[0].select('.C\\(\\$c-trend-up\\)')
            
            if down_elements:
                print(f"  📉 趨勢: 下跌")
            elif up_elements:
                print(f"  📈 趨勢: 上漲")
            else:
                print(f"  ➡️ 趨勢: 平盤")
        else:
            print("  ❌ 未找到主要容器")
        
        return True
        
    except Exception as e:
        print(f"❌ 詳細解析失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 Yahoo股價爬蟲測試 - 基於範例程式碼")
    print(f"⏰ 測試時間: {requests.utils.default_headers()}")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # 執行測試
    if test_yahoo_stock_crawler():
        success_count += 1
    
    if test_detailed_parsing():
        success_count += 1
    
    # 額外測試：驗證範例程式碼
    print("\n4️⃣ 驗證原始範例程式碼")
    print("-" * 40)
    
    try:
        # 直接執行您的範例程式碼
        url = 'https://tw.stock.yahoo.com/quote/2330'
        web = requests.get(url)
        soup = BeautifulSoup(web.text, "html.parser")
        title = soup.find('h1')
        a = soup.select('.Fz\\(32px\\)')[0]
        b = soup.select('.Fz\\(20px\\)')[0]
        s = ''

        try:
            if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C\\(\\$c-trend-down\\)')[0]:
                s = '-'
        except:
            try:
                if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C\\(\\$c-trend-up\\)')[0]:
                    s = '+'
            except:
                s = ''
        
        result = f'{title.get_text()} : {a.get_text()} ( {s}{b.get_text()} )'
        print(f"✅ 原始範例程式碼執行成功:")
        print(f"  結果: {result}")
        success_count += 1
        
    except Exception as e:
        print(f"❌ 原始範例程式碼執行失敗: {e}")
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 測試結果總結")
    print("=" * 30)
    print(f"🎯 成功: {success_count}/{total_tests} 項測試")
    
    if success_count >= 2:
        print("🎉 Yahoo股價爬蟲技術驗證成功！")
        print("💡 可以開始建立即時股價監控系統")
        return True
    else:
        print("⚠️ 需要進一步檢查和調整")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 下一步: 啟動即時股價監控系統")
        print("📝 執行指令: python real_time_stock_monitor.py")
    
    input("\n按 Enter 鍵結束...")
