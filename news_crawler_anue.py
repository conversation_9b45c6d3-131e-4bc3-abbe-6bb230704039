#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉅亨網新聞爬蟲
基於 hsunAlfred/newsCrawler 的 news_anue.py 改寫
"""

from news_crawler_base import NewsCrawlerBase, NewsCrawlerException
from datetime import datetime
import time
from random import randint
import requests
from bs4 import BeautifulSoup
import json
import re
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

class AnueNewsCrawler(NewsCrawlerBase):
    """鉅亨網新聞爬蟲"""
    
    def __init__(self, db_path=None):
        super().__init__(db_path)
        
        # 設置請求標頭
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "origin": "https://news.cnyes.com",
            "referer": "https://news.cnyes.com/",
            "sec-ch-ua": '"Microsoft Edge";v="95", "Chromium";v="95", ";Not A Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "Windows",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "x-cnyes-app": "fe-desktop",
            "x-platform": "WEB",
            "x-system-kind": "NEWS_DESKTOP"
        }

    def extract(self, ndaysAgo, interval: str, stock_code: str = None) -> list:
        """
        提取鉅亨網新聞資料
        
        Args:
            ndaysAgo: n天前的datetime
            interval: 時間間隔 (HHMM格式)
            stock_code: 股票代碼 (暫時不使用，鉅亨網按分類爬取)
            
        Returns:
            list: [(news_id, 日期, 時間, 標題, 連結, 記者, 內文, tag, stock_code), ...]
        """
        try:
            # 先取得所有連結
            allLinks = self._get_all_links(ndaysAgo, interval)
            self.logger.info(f"📋 獲取到 {len(allLinks)} 個新聞連結")
            
            if not allLinks:
                return []
            
            # 再取得所有內文
            linkAndContent = self._get_all_content(allLinks, stock_code)
            self.logger.info(f"📄 成功獲取 {len(linkAndContent)} 篇新聞內容")
            
            return linkAndContent
            
        except Exception as e:
            self.logger.error(f"❌ 提取新聞資料失敗: {e}")
            raise NewsCrawlerException(f"鉅亨網新聞提取失敗: {e}")

    def _get_all_links(self, ndaysAgo, interval: str):
        """
        獲取所有新聞連結
        
        Returns:
            list: [(news_id, 日期, 時間, 標題, 連結), ...]
        """
        # 計算時間戳
        start = f'{ndaysAgo.year}/{ndaysAgo.month}/{ndaysAgo.day} {interval[0:2]}:{interval[2:4]}:00'
        struct_time = time.strptime(start, "%Y/%m/%d %H:%M:%S")
        start_stamp = int(time.mktime(struct_time))
        
        ended = f'{self.timeNow.year}/{self.timeNow.month}/{self.timeNow.day} {interval[0:2]}:{interval[2:4]}:00'
        struct_time = time.strptime(ended, "%Y/%m/%d %H:%M:%S")
        ended_stamp = int(time.mktime(struct_time))
        
        self.logger.info(f"📅 時間範圍: {start} ({start_stamp}) ~ {ended} ({ended_stamp})")
        
        datas = []
        page, last_page = 1, 1
        pattern_ID = re.compile(r'.*/(.*)\\?exp=a.*')
        
        while page <= last_page:
            try:
                # 使用台股分類的API
                url = f'https://api.cnyes.com/media/api/v1/newslist/category/tw_stock?startAt={start_stamp}&endAt={ended_stamp}&limit=30&page={page}'
                self.logger.info(f"🔍 正在爬取第 {page} 頁: {url}")
                
                response = requests.get(url, headers=self.headers, verify=False, timeout=30)
                response.raise_for_status()
                
                # 隨機延遲避免被封鎖
                time.sleep(0.7 + randint(10, 20) / 20)
                
                js = response.json()
                
                if js.get('statusCode') != 200:
                    raise NewsCrawlerException(f"鉅亨網API錯誤: {js.get('message', '未知錯誤')}")
                
                # 更新總頁數
                last_page = js['items']['last_page']
                page += 1
                
                # 處理當前頁面的新聞
                temp = js['items']['data']
                
                for item in temp:
                    try:
                        newsDateTime = datetime.fromtimestamp(item['publishAt'])
                        newsDate = newsDateTime.strftime("%Y%m%d")
                        newsTime = newsDateTime.strftime("%H%M%S")
                        link = f"https://news.cnyes.com/news/id/{item['newsId']}?exp=a"
                        
                        # 提取新聞ID
                        match = re.search(pattern_ID, link)
                        if match:
                            newsID = 'anue_' + match.group(1)
                        else:
                            newsID = f'anue_{item["newsId"]}'
                        
                        datas.append((newsID, newsDate, newsTime, item['title'], link))
                        
                    except Exception as e:
                        self.logger.warning(f"處理新聞項目失敗: {e}")
                        continue
                
            except Exception as e:
                self.logger.error(f"❌ 爬取第 {page} 頁失敗: {e}")
                break
        
        return datas

    def _get_all_content(self, datas, stock_code: str = None):
        """
        獲取所有新聞內容
        
        Args:
            datas: [(news_id, 日期, 時間, 標題, 連結), ...]
            stock_code: 股票代碼過濾
            
        Returns:
            list: [(news_id, 日期, 時間, 標題, 連結, 記者, 內文, tag, stock_code), ...]
        """
        result = []
        
        for i, data in enumerate(datas):
            try:
                self.logger.info(f"📖 正在獲取內容 {i+1}/{len(datas)}: {data[4]}")
                
                response = requests.get(data[4], headers=self.headers, verify=False, timeout=30)
                response.raise_for_status()
                
                # 增加延遲減少系統負擔
                time.sleep(1.5 + randint(10, 30) / 20)
                
                soup = BeautifulSoup(response.content.decode('utf-8'), 'html.parser')
                
                # 提取標籤
                tags = soup.select("#content > div > div > div._2hZZ.theme-app.theme-newsdetail > main > div._1S0A > article > section > nav > a")
                tagStr = " ".join([tag.text.strip() for tag in tags])
                
                # 提取記者
                reporters = soup.select("#content > div > div > div._2hZZ.theme-app.theme-newsdetail > main > div._uo1n > div._1R6L > span > span")
                reporterStr = " ".join([reporter.text.strip() for reporter in reporters])
                
                # 提取內容
                contents = soup.select("#content > div > div > div._2hZZ.theme-app.theme-newsdetail > main > div._1S0A > article > section._82F6 > div._1UuP")
                contentStr = " ".join([content.text.strip() for content in contents])
                
                # 如果指定了股票代碼，檢查新聞是否相關
                detected_stock_code = None
                if stock_code:
                    # 檢查標題、內容或標籤中是否包含股票代碼
                    full_text = f"{data[3]} {contentStr} {tagStr}".upper()
                    if stock_code.upper() in full_text:
                        detected_stock_code = stock_code
                    else:
                        continue  # 跳過不相關的新聞
                else:
                    # 嘗試從內容中提取股票代碼
                    stock_pattern = re.compile(r'[0-9]{4}')
                    matches = stock_pattern.findall(f"{data[3]} {contentStr}")
                    if matches:
                        detected_stock_code = matches[0]  # 取第一個匹配的代碼
                
                # 組合結果 (news_id, 日期, 時間, 標題, 連結, 記者, 內文, tag, stock_code)
                result.append((*data, reporterStr, contentStr, tagStr, detected_stock_code))
                
            except Exception as e:
                self.logger.warning(f"獲取新聞內容失敗 {data[4]}: {e}")
                continue
        
        return result

    def transform(self, rawDatas):
        """
        轉換資料格式
        
        Args:
            rawDatas: [(news_id, 日期, 時間, 標題, 連結, 記者, 內文, tag, stock_code), ...]
            
        Returns:
            tuple: (cleanDatas生成器, cleanTags生成器)
        """
        # 新聞內容: (news_id, date, time, source, title, reporter, link, content, stock_code)
        cleanDatas = (
            (rawData[0], rawData[1], rawData[2], 'anue', rawData[3], rawData[5], rawData[4], rawData[6], rawData[8])
            for rawData in rawDatas
        )
        
        # 標籤: (source, news_id, tag)
        cleanTags = (
            ('anue', rawData[0], rawData[7])
            for rawData in rawDatas
            if rawData[7]  # 只有當標籤不為空時才加入
        )
        
        return cleanDatas, cleanTags

if __name__ == '__main__':
    # 測試爬蟲
    crawler = AnueNewsCrawler()
    
    print("🚀 測試鉅亨網新聞爬蟲")
    print("=" * 50)
    
    # 爬取最近1天的新聞
    result = crawler.run(ndays=1, interval="0600")
    print(f"📊 爬取結果: {result}")
    
    # 測試獲取特定股票新聞
    news_list = crawler.get_news_by_stock("2330", days=7)
    print(f"📰 台積電相關新聞: {len(news_list)} 筆")
    
    if news_list:
        print("📋 最新一筆新聞:")
        latest = news_list[0]
        print(f"  標題: {latest['title']}")
        print(f"  日期: {latest['date']} {latest['time']}")
        print(f"  來源: {latest['source']}")
