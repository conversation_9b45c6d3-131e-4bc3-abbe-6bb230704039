# 現金流量表爬蟲使用指南

## 🎯 概述

現金流量表爬蟲已成功整合到 `auto_update.py` 和 `finlab/crawler.py` 中，可以自動從 MOPS 公開資訊觀測站爬取台股現金流量表資料。

## 📊 功能特色

✅ **完整整合**: 與現有爬蟲系統完全整合  
✅ **增量更新**: 支援智能增量更新，避免重複爬取  
✅ **自動判斷**: 根據日期自動判斷財報季別  
✅ **官方資料**: 直接從 MOPS 官方網站爬取  
✅ **豐富內容**: 包含 383 個詳細現金流量項目  
✅ **多格式支援**: 同時支援 DB 和 Pickle 格式  

## 🚀 使用方法

### 方法1: 單獨執行現金流量表爬蟲

```bash
python auto_update.py cash_flows
```

### 方法2: 執行完整自動更新 (包含現金流量表)

```bash
python auto_update.py
```

### 方法3: 在程式中調用

```python
import datetime
from finlab.crawler import crawl_cash_flows

# 爬取指定日期的現金流量表
date = datetime.datetime(2022, 5, 15)  # Q1 財報發布日
cash_flows_data = crawl_cash_flows(date)

print(f"獲取資料筆數: {len(cash_flows_data)}")
print(f"資料欄位數: {len(cash_flows_data.columns)}")
```

## 📁 資料儲存位置

- **DB 格式**: `history/tables/cash_flows.db`
- **Pickle 格式**: `history/cash_flows.pkl`

## 📅 財報季別對應

| 爬取日期範圍 | 財報季別 | 發布日期 |
|-------------|---------|---------|
| 1月-3月     | 前一年Q4 | 3月31日  |
| 4月-5月     | 當年Q1   | 5月15日  |
| 6月-8月     | 當年Q2   | 8月14日  |
| 9月-11月    | 當年Q3   | 11月14日 |

## 💰 資料內容

### 核心現金流量項目 (100% 覆蓋率)
- 本期稅前淨利（淨損）
- 營業活動之淨現金流入（流出）
- 投資活動之淨現金流入（流出）
- 籌資活動之淨現金流入（流出）
- 期初現金及約當現金餘額
- 期末現金及約當現金餘額

### 詳細項目 (依實際發生情況)
- 折舊費用
- 攤銷費用
- 應收帳款變動
- 存貨變動
- 應付帳款變動
- 固定資產處分損益
- 投資損益
- 借款增減
- 股利發放
- 等 383 個詳細項目...

## 🔧 配置說明

### auto_update.py 配置

現金流量表任務已添加到 `update_tasks` 中：

```python
update_tasks = [
    ('price', crawl_price, price_date_range),
    ('cash_flows', crawl_cash_flows, date_range),  # ← 現金流量表任務
    # 其他任務...
]
```

### finlab/crawler.py 新增函數

- `crawl_cash_flows(date)`: 主要爬蟲函數
- `get_financial_year_season(date)`: 日期轉財報季別
- `get_financial_report_date(year, season)`: 獲取財報發布日期
- `load_existing_cash_flows()`: 載入現有資料
- `should_update_cash_flows()`: 檢查是否需要更新

## 📊 資料結構

### 索引結構
```
MultiIndex: [stock_id, date]
- stock_id: 股票代碼 (如 '2330')
- date: 財報發布日期 (如 '2022-05-15')
```

### 欄位結構
```
- stock_id: 股票代碼
- stock_name: 股票名稱 (如果有的話)
- date: 財報日期
- 本期稅前淨利（淨損）: 數值
- 營業活動之淨現金流入（流出）: 數值
- 投資活動之淨現金流入（流出）: 數值
- 籌資活動之淨現金流入（流出）: 數值
- ... (其他 379 個現金流量項目)
```

## 🔍 資料查詢範例

### 查詢台積電現金流量
```python
import sqlite3
import pandas as pd

conn = sqlite3.connect('history/tables/cash_flows.db')
query = """
SELECT stock_id, stock_name, date, 
       `本期稅前淨利（淨損）`,
       `營業活動之淨現金流入（流出）`,
       `投資活動之淨現金流入（流出）`,
       `籌資活動之淨現金流入（流出）`
FROM cash_flows 
WHERE stock_id = '2330'
ORDER BY date
"""
tsmc_cash_flows = pd.read_sql_query(query, conn)
conn.close()
```

### 查詢 2022 年營業現金流量前 20 名
```python
query = """
SELECT stock_id, stock_name, `營業活動之淨現金流入（流出）`
FROM cash_flows 
WHERE date LIKE '2022%' 
  AND `營業活動之淨現金流入（流出）` IS NOT NULL
ORDER BY `營業活動之淨現金流入（流出）` DESC 
LIMIT 20
"""
top_cash_flows = pd.read_sql_query(query, conn)
```

## ⚠️ 注意事項

1. **網路連線**: 需要穩定的網路連線訪問 MOPS 網站
2. **爬取時間**: 完整爬取可能需要較長時間，建議分批執行
3. **資料更新**: 財報資料通常在季末後 1-2 個月發布
4. **空值處理**: 部分現金流量項目可能為空值（表示該項目未發生）
5. **股票代碼**: 僅包含有財報資料的股票

## 🐛 故障排除

### 問題1: 導入錯誤
```
ImportError: cannot import name 'crawl_cash_flows'
```
**解決方案**: 確保 `finlab/crawler.py` 包含 `crawl_cash_flows` 函數

### 問題2: 無法找到資料
```
未找到現金流量表資料
```
**解決方案**: 首次使用需要執行完整爬取，後續會進行增量更新

### 問題3: 網路連線問題
```
requests.exceptions.ConnectionError
```
**解決方案**: 檢查網路連線，或稍後重試

## 📈 效能優化

1. **批次處理**: 限制同時爬取的股票數量
2. **增量更新**: 只爬取新的財報季別
3. **錯誤重試**: 自動重試失敗的請求
4. **資料快取**: 避免重複下載相同資料

## 🎉 完成！

現金流量表爬蟲已完全整合到你的系統中，可以開始使用了！

執行以下命令開始爬取：
```bash
python auto_update.py cash_flows
```
