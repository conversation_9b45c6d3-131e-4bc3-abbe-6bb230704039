#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接檢查是否真的有新資料
"""

import pandas as pd
import datetime
import os

def check_real_update():
    """檢查是否真的有新資料"""
    print("🔍 檢查是否真的爬取到新資料")
    print("=" * 60)
    
    file_path = 'history/tables/income_sheet.pkl'
    
    try:
        # 讀取檔案
        df = pd.read_pickle(file_path)
        
        # 獲取所有日期
        dates = df.index.get_level_values('date')
        unique_dates = sorted(dates.unique())
        
        print(f"📊 檔案資訊:")
        print(f"   總筆數: {len(df):,}")
        print(f"   日期數量: {len(unique_dates)}")
        
        print(f"\n📅 最新的10個日期:")
        for i, date in enumerate(unique_dates[-10:], 1):
            date_data = df[df.index.get_level_values('date') == date]
            print(f"   {i}. {date}: {len(date_data)} 筆資料")
        
        # 檢查是否有2023年以後的資料
        latest_date = unique_dates[-1]
        print(f"\n🎯 關鍵問題:")
        print(f"   最新日期: {latest_date}")
        
        if latest_date.year >= 2023:
            print(f"   ✅ 有2023年或更新的資料")
            
            # 檢查2023年以後的資料
            recent_dates = [d for d in unique_dates if d.year >= 2023]
            print(f"   2023年後的日期: {len(recent_dates)} 個")
            for date in recent_dates:
                date_data = df[df.index.get_level_values('date') == date]
                print(f"     - {date}: {len(date_data)} 筆")
            
            return True
        else:
            print(f"   ❌ 沒有2023年以後的資料")
            print(f"   💡 更新可能失敗了")
            return False
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

def check_backup_vs_main():
    """比較備份檔案和主檔案"""
    print(f"\n🔍 比較備份檔案和主檔案")
    print("=" * 60)
    
    main_file = 'history/tables/income_sheet.pkl'
    backup_file = 'history/tables/income_sheet_backup_20250722_135854.pkl'
    
    try:
        if os.path.exists(backup_file):
            # 讀取兩個檔案
            main_df = pd.read_pickle(main_file)
            backup_df = pd.read_pickle(backup_file)
            
            print(f"📊 檔案比較:")
            print(f"   主檔案筆數: {len(main_df):,}")
            print(f"   備份檔案筆數: {len(backup_df):,}")
            
            # 比較日期範圍
            main_dates = main_df.index.get_level_values('date')
            backup_dates = backup_df.index.get_level_values('date')
            
            main_latest = main_dates.max()
            backup_latest = backup_dates.max()
            
            print(f"   主檔案最新日期: {main_latest}")
            print(f"   備份檔案最新日期: {backup_latest}")
            
            if main_latest > backup_latest:
                print(f"   ✅ 主檔案有更新的資料")
                return True
            elif main_latest == backup_latest:
                print(f"   ⚠️ 主檔案和備份檔案日期相同")
                
                # 檢查筆數
                if len(main_df) > len(backup_df):
                    print(f"   ✅ 主檔案筆數更多")
                    return True
                else:
                    print(f"   ❌ 主檔案沒有新增資料")
                    return False
            else:
                print(f"   ❌ 主檔案比備份檔案還舊？")
                return False
        else:
            print(f"❌ 備份檔案不存在")
            return False
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 真實更新檢查工具")
    print("=" * 60)
    print("🎯 目標: 確認是否真的爬取到新資料")
    print("=" * 60)
    
    # 檢查1: 是否有新資料
    has_new_data = check_real_update()
    
    # 檢查2: 比較備份和主檔案
    has_updates = check_backup_vs_main()
    
    print(f"\n📊 最終結論:")
    if has_new_data:
        print(f"✅ 確實有新的資料 (2023年以後)")
    else:
        print(f"❌ 沒有新的資料")
    
    if has_updates:
        print(f"✅ 主檔案相對於備份有更新")
    else:
        print(f"❌ 主檔案沒有相對於備份的更新")
    
    if has_new_data and has_updates:
        print(f"\n🎉 結論: 更新成功！確實爬取到新資料")
    elif has_new_data:
        print(f"\n💡 結論: 有新資料，但可能是之前就有的")
    else:
        print(f"\n❌ 結論: 更新失敗，沒有爬取到新資料")

if __name__ == "__main__":
    main()
