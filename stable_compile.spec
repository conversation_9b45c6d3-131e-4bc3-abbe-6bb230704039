# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

# 數據文件
datas = []

# 嘗試添加可能的數據文件
data_files = [
    'config',
    'strategies', 
    'data',
    'templates',
    '*.json',
    '*.yaml',
    '*.yml',
    '*.csv',
    '*.txt'
]

for pattern in data_files:
    if os.path.exists(pattern):
        if os.path.isdir(pattern):
            datas.append((pattern, pattern))
        else:
            datas.append((pattern, '.'))

# 隱藏導入 - 包含所有可能需要的模組
hiddenimports = [
    # 系統模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtPrintSupport',
    'PyQt6.sip',
    
    # 數據處理
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    'numpy._core',
    'numpy._core.numeric',
    
    # 圖表和可視化
    'pyqtgraph',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    
    # 網路和爬蟲
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    'selenium',
    'webdriver_manager',
    
    # Excel 處理
    'openpyxl',
    'xlsxwriter',
    'xlrd',
    'xlwt',
    
    # 金融數據 (排除有問題的 twstock)
    'yfinance',
    # 'twstock',  # 排除，因為有 CSV 文件問題
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    
    # 其他可能的依賴
    'schedule',
    'tqdm',
    'colorlog',
    'psutil',
    'cryptography',
    'sqlalchemy',
    'apscheduler',
    'python-dateutil',
    'pytz',
    'six',
    'setuptools',
    'pkg_resources',
]

# 排除模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    'twstock',  # 排除有問題的 twstock 模組
    'twstock.codes',
    'twstock.stock',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統_穩定版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
