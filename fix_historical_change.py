#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正 newprice.db 中歷史資料的 Change 欄位
從 price.db 複製正確的 Change 值
"""

import sqlite3
import pandas as pd
from datetime import datetime

def fix_historical_change():
    """修正 newprice.db 中歷史資料的 Change 欄位"""
    
    print("=" * 80)
    print("🔧 修正 newprice.db 中歷史資料的 Change 欄位")
    print("=" * 80)
    
    price_db = r'D:\Finlab\history\tables\price.db'
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        # 檢查兩個資料庫的日期範圍
        print("\n📊 檢查資料庫日期範圍:")
        
        # price.db 日期範圍
        conn_price = sqlite3.connect(price_db)
        query_price_range = "SELECT MIN(date) as min_date, MAX(date) as max_date FROM stock_daily_data"
        price_range = pd.read_sql_query(query_price_range, conn_price)
        print(f"   price.db: {price_range.iloc[0]['min_date']} ~ {price_range.iloc[0]['max_date']}")
        
        # newprice.db 日期範圍
        conn_newprice = sqlite3.connect(newprice_db)
        query_newprice_range = "SELECT MIN(date) as min_date, MAX(date) as max_date FROM stock_daily_data"
        newprice_range = pd.read_sql_query(query_newprice_range, conn_newprice)
        print(f"   newprice.db: {newprice_range.iloc[0]['min_date']} ~ {newprice_range.iloc[0]['max_date']}")
        
        # 找出重疊的日期範圍
        overlap_start = max(price_range.iloc[0]['min_date'], newprice_range.iloc[0]['min_date'])
        overlap_end = min(price_range.iloc[0]['max_date'], newprice_range.iloc[0]['max_date'])
        
        print(f"   重疊範圍: {overlap_start} ~ {overlap_end}")
        
        # 檢查需要修正的記錄數
        print(f"\n🔍 檢查需要修正的記錄:")
        
        query_need_fix = f'''
            SELECT COUNT(*) as count
            FROM stock_daily_data 
            WHERE [Change] = 0 AND date >= '{overlap_start}' AND date <= '{overlap_end}'
        '''
        
        need_fix = pd.read_sql_query(query_need_fix, conn_newprice)
        fix_count = need_fix.iloc[0]['count']
        
        print(f"   需要修正的記錄數: {fix_count:,}")
        
        if fix_count == 0:
            print(f"   ✅ 沒有需要修正的記錄")
            return True
        
        # 詢問是否繼續
        print(f"\n⚠️ 即將修正 {fix_count:,} 筆記錄的 Change 欄位")
        print(f"   這個操作會:")
        print(f"   1. 從 price.db 讀取正確的 Change 值")
        print(f"   2. 更新 newprice.db 中對應記錄的 Change 欄位")
        print(f"   3. 只處理 Change = 0 的記錄")
        
        response = input(f"\n是否繼續？(y/N): ").strip().lower()
        
        if response != 'y':
            print(f"❌ 操作已取消")
            return False
        
        # 開始修正
        print(f"\n🔧 開始修正 Change 欄位...")
        
        # 分批處理以避免記憶體問題
        batch_size = 10000
        total_updated = 0
        
        # 獲取需要修正的日期列表
        query_dates = f'''
            SELECT DISTINCT date 
            FROM stock_daily_data 
            WHERE [Change] = 0 AND date >= '{overlap_start}' AND date <= '{overlap_end}'
            ORDER BY date
        '''
        
        dates_to_fix = pd.read_sql_query(query_dates, conn_newprice)
        total_dates = len(dates_to_fix)
        
        print(f"   需要處理 {total_dates} 個日期")
        
        for i, row in dates_to_fix.iterrows():
            date = row['date']
            
            # 從 price.db 獲取該日期的 Change 值
            query_price_change = f'''
                SELECT stock_id, [Change]
                FROM stock_daily_data 
                WHERE date = '{date}'
            '''
            
            price_changes = pd.read_sql_query(query_price_change, conn_price)
            
            if not price_changes.empty:
                # 更新 newprice.db 中的 Change 值
                cursor_newprice = conn_newprice.cursor()
                
                updated_count = 0
                for _, change_row in price_changes.iterrows():
                    stock_id = change_row['stock_id']
                    change_value = change_row['Change']
                    
                    # 只更新 Change = 0 的記錄
                    cursor_newprice.execute('''
                        UPDATE stock_daily_data 
                        SET [Change] = ?
                        WHERE stock_id = ? AND date = ? AND [Change] = 0
                    ''', (change_value, stock_id, date))
                    
                    if cursor_newprice.rowcount > 0:
                        updated_count += 1
                
                conn_newprice.commit()
                total_updated += updated_count
                
                # 顯示進度
                progress = (i + 1) / total_dates * 100
                print(f"   [{i+1:4d}/{total_dates}] {progress:5.1f}% {date}: 更新 {updated_count:4d} 筆記錄")
        
        print(f"\n✅ 修正完成！")
        print(f"   總共更新了 {total_updated:,} 筆記錄")
        
        # 驗證修正結果
        print(f"\n🔍 驗證修正結果:")
        
        query_verify = f'''
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_count,
                COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_count,
                COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_count
            FROM stock_daily_data
            WHERE date >= '{overlap_start}' AND date <= '{overlap_end}'
        '''
        
        verify_result = pd.read_sql_query(query_verify, conn_newprice)
        
        total = verify_result.iloc[0]['total']
        zero = verify_result.iloc[0]['zero_count']
        positive = verify_result.iloc[0]['positive_count']
        negative = verify_result.iloc[0]['negative_count']
        
        print(f"   修正範圍內記錄統計:")
        print(f"   總數: {total:,}")
        print(f"   Change = 0: {zero:,} ({zero/total*100:.1f}%)")
        print(f"   Change > 0: {positive:,} ({positive/total*100:.1f}%)")
        print(f"   Change < 0: {negative:,} ({negative/total*100:.1f}%)")
        
        # 檢查 0050 的修正結果
        query_0050_verify = f'''
            SELECT date, [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050' AND date >= '{overlap_start}'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        df_0050_verify = pd.read_sql_query(query_0050_verify, conn_newprice)
        
        print(f"\n📊 0050 修正後的 Change 值範例:")
        for _, row in df_0050_verify.iterrows():
            print(f"   {row['date']}: Close={row['Close']}, Change={row['Change']}")
        
        conn_price.close()
        conn_newprice.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 修正失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_historical_change()
    
    if success:
        print(f"\n🎉 歷史資料 Change 欄位修正完成！")
        print(f"   現在 newprice.db 的 Change 欄位與 price.db 一致")
    else:
        print(f"\n❌ 修正失敗或已取消")
