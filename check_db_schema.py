#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 的資料庫結構
"""

import sqlite3
import pandas as pd

def check_db_schema():
    """檢查資料庫結構"""
    
    print("=" * 60)
    print("🔍 檢查 newprice.db 資料庫結構")
    print("=" * 60)
    
    db_file = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = cursor.fetchall()
        
        print("📊 stock_daily_data 表格結構:")
        for i, (cid, name, type_, notnull, default, pk) in enumerate(columns, 1):
            print(f"   {i:2d}. {name:<20} {type_:<10} {'NOT NULL' if notnull else 'NULL'}")
        
        # 檢查最新幾筆資料的欄位內容
        print(f"\n📋 最新資料範例:")
        query = '''
            SELECT stock_id, date, Volume, [Transaction], TradeValue, [Open], High, Low, [Close], stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 3
        '''
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            for _, row in df.iterrows():
                print(f"   {row['stock_id']} {row['date']}:")
                print(f"      Volume={row['Volume']}, Transaction={row['Transaction']}, TradeValue={row['TradeValue']}")
                print(f"      Open={row['Open']}, High={row['High']}, Low={row['Low']}, Close={row['Close']}")
                print(f"      Name={row['stock_name']}, Status={row['listing_status']}, Industry={row['industry']}")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == "__main__":
    check_db_schema()
