#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試除權息時間範圍查詢功能
"""

import sqlite3
import os

def test_date_range_query():
    """測試時間範圍查詢"""
    print("🧪 測試除權息時間範圍查詢...")
    
    try:
        dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
        
        if not os.path.exists(dividend_db_path):
            print(f"❌ 除權息資料庫不存在: {dividend_db_path}")
            return False
        
        conn = sqlite3.connect(dividend_db_path)
        cursor = conn.cursor()
        
        # 測試查詢2025-07-15到2025-07-22的資料
        start_date = "2025-07-15"
        end_date = "2025-07-22"
        
        print(f"📅 查詢期間: {start_date} 到 {end_date}")
        
        query = """
            SELECT stock_code, stock_name, year, ex_dividend_date,
                   MAX(cash_dividend) as cash_dividend,
                   MAX(stock_dividend) as stock_dividend,
                   MAX(total_dividend) as total_dividend,
                   MAX(dividend_yield) as dividend_yield,
                   MAX(eps) as eps,
                   MAX(data_source) as data_source
            FROM dividend_data
            WHERE ex_dividend_date IS NOT NULL
            AND ex_dividend_date != ''
            AND ex_dividend_date >= ?
            AND ex_dividend_date <= ?
            AND (data_source IS NULL OR data_source != 'sample_data')
            GROUP BY stock_code, ex_dividend_date
            ORDER BY ex_dividend_date, stock_code
        """
        
        cursor.execute(query, (start_date, end_date))
        results = cursor.fetchall()
        
        print(f"📊 查詢結果: 找到 {len(results)} 筆記錄")
        
        if results:
            print("\n📋 查詢結果詳細:")
            print("股票代碼 | 股票名稱 | 年份 | 除權息日 | 現金股利")
            print("-" * 60)
            
            for row in results[:10]:  # 只顯示前10筆
                stock_code = row[0]
                stock_name = row[1] or 'N/A'
                year = row[2]
                ex_date = row[3]
                cash_dividend = row[4] or 0
                
                print(f"{stock_code:<8} | {stock_name:<10} | {year} | {ex_date} | {cash_dividend:.2f}")
            
            if len(results) > 10:
                print(f"... 還有 {len(results) - 10} 筆記錄")
        else:
            print("❌ 沒有找到符合條件的資料")
            
            # 檢查是否有接近日期的資料
            print("\n🔍 檢查相近日期的資料...")
            cursor.execute("""
                SELECT stock_code, stock_name, year, ex_dividend_date
                FROM dividend_data
                WHERE ex_dividend_date LIKE '2025-07%'
                ORDER BY ex_dividend_date
                LIMIT 10
            """)
            
            nearby_results = cursor.fetchall()
            if nearby_results:
                print("📅 2025年7月的除權息資料:")
                for row in nearby_results:
                    print(f"  {row[0]} {row[1]} {row[2]} {row[3]}")
            else:
                print("❌ 沒有找到2025年7月的資料")
        
        conn.close()
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_stock_price_database():
    """測試股價資料庫"""
    print("\n🧪 測試股價資料庫...")
    
    try:
        price_db_path = "D:/Finlab/history/tables/price.db"
        
        if not os.path.exists(price_db_path):
            print(f"❌ 股價資料庫不存在: {price_db_path}")
            return False
        
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"📊 股價資料庫欄位: {columns}")
        
        # 測試獲取幾支股票的最新股價
        test_stocks = ['2330', '2317', '0050', '1264', '1301']
        
        print("\n💰 測試股票最新股價:")
        for stock_code in test_stocks:
            cursor.execute("""
                SELECT close, date FROM stock_daily_data 
                WHERE stock_id = ? 
                ORDER BY date DESC 
                LIMIT 1
            """, (stock_code,))
            
            result = cursor.fetchone()
            if result:
                price, date = result
                print(f"  {stock_code}: {price:.2f} ({date})")
            else:
                print(f"  {stock_code}: 無資料")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 股價資料庫測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 除權息查詢功能測試")
    print("=" * 50)
    
    # 測試時間範圍查詢
    date_range_ok = test_date_range_query()
    
    # 測試股價資料庫
    price_db_ok = test_stock_price_database()
    
    print("\n📊 測試結果摘要:")
    print("=" * 50)
    print(f"時間範圍查詢: {'✅ 通過' if date_range_ok else '❌ 失敗'}")
    print(f"股價資料庫: {'✅ 通過' if price_db_ok else '❌ 失敗'}")
    
    if date_range_ok and price_db_ok:
        print("\n🎉 所有測試通過！")
    else:
        print("\n⚠️ 部分測試失敗，請檢查相關功能。")

if __name__ == "__main__":
    main()
