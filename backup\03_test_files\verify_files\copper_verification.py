#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
銅期貨添加驗證
"""

def verify_copper_addition():
    """驗證銅期貨添加"""
    print("🥉 銅期貨添加驗證")
    print("=" * 40)
    
    # 檢查文件內容
    files_to_check = {
        'safe_market_scanner.py': ['銅期貨', "'銅期貨':"],
        'real_market_data_fetcher.py': ['銅期貨', 'HG=F', 'get_commodities_real'],
        'market_data_crawler_dialog.py': ['銅期貨', '白銀, 銅期貨']
    }
    
    all_passed = True
    
    for filename, keywords in files_to_check.items():
        print(f"\n📁 檢查 {filename}:")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_passed = True
            for keyword in keywords:
                if keyword in content:
                    print(f"   ✅ 包含: {keyword}")
                else:
                    print(f"   ❌ 缺少: {keyword}")
                    file_passed = False
            
            if file_passed:
                print(f"   🎉 {filename} 檢查通過")
            else:
                print(f"   ⚠️ {filename} 檢查失敗")
                all_passed = False
                
        except FileNotFoundError:
            print(f"   ❌ 文件不存在: {filename}")
            all_passed = False
        except Exception as e:
            print(f"   ❌ 檢查異常: {e}")
            all_passed = False
    
    # 總結
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 銅期貨添加驗證通過！")
        print("\n✅ 完成項目:")
        print("• 添加銅期貨到 safe_market_scanner.py")
        print("• 添加真實數據獲取功能到 real_market_data_fetcher.py")
        print("• 更新對話框說明文字")
        print("• 使用 yfinance 代碼: HG=F")
        print("• 支持真實數據和模擬數據")
        
        print("\n🚀 現在商品價格包含:")
        print("• WTI原油 (CL=F)")
        print("• 黃金期貨 (GC=F)")
        print("• 白銀期貨 (SI=F)")
        print("• 銅期貨 (HG=F) ← 新增")
        
        print("\n🎯 使用方法:")
        print("1. 啟動主程式")
        print("2. 點擊「🕷️ 爬蟲」→「🌍 全球市場數據爬蟲」")
        print("3. 點擊「🚀 開始爬取」")
        print("4. 在商品價格類別中查看銅期貨數據")
        
    else:
        print("❌ 銅期貨添加驗證失敗")
        print("請檢查上述錯誤並重新添加")
    
    return all_passed

if __name__ == "__main__":
    verify_copper_addition()
