{"name": "智能波段策略", "description": "適合波段操作的智能買賣策略，結合趨勢和反轉信號", "parameters": {"trend_period": 30, "short_ma": 5, "medium_ma": 20, "long_ma": 60, "rsi_period": 14, "bb_period": 20, "volume_ma": 10, "volatility_threshold": 0.02}, "entry_rules": {"trend_confirmation": {"type": "ma_alignment", "condition": "short > medium > long", "weight": 0.3}, "momentum_signal": {"type": "rsi_entry", "oversold": 35, "recovery": 45, "weight": 0.25}, "price_action": {"type": "bollinger_entry", "position": "lower_band_bounce", "weight": 0.2}, "volume_confirmation": {"type": "volume_surge", "threshold": 1.3, "weight": 0.25}}, "exit_rules": {"profit_target": {"type": "percentage_gain", "target": 12, "partial_exit": 50}, "stop_loss": {"type": "percentage_loss", "limit": 6, "trailing": true}, "technical_exit": {"type": "momentum_divergence", "rsi_threshold": 75, "weight": 0.4}, "time_exit": {"type": "holding_period", "max_days": 15}}, "position_sizing": {"base_position": 0.02, "max_position": 0.05, "volatility_adjustment": true, "correlation_limit": 0.7}}