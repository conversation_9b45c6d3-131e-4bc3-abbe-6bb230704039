#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收排名計算功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI

def test_revenue_cleaning():
    """測試營收數值清理功能"""
    print("🧹 測試營收數值清理功能")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    test_cases = [
        ('120,000,000', 120000000, '正常格式'),
        ('10,107,877', 10107877, '含逗號'),
        ('263708978', 263708978, '純數字'),
        ('N/A', 0, 'N/A值'),
        ('', 0, '空字串'),
        (None, 0, 'None值'),
        ('abc', 0, '非數字'),
        ('-1000', -1000, '負數'),
    ]
    
    print("測試案例:")
    for i, (input_val, expected, description) in enumerate(test_cases, 1):
        result = gui.clean_revenue_value(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{i}. {description}: '{input_val}' -> {result} {status}")
        if result != expected:
            print(f"   預期: {expected}, 實際: {result}")
    
    app.quit()

def test_monthly_revenue_data_access():
    """測試月營收資料存取"""
    print("\n📊 測試月營收資料存取")
    
    # 檢查PKL檔案
    pkl_files = [
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl'
    ]
    
    for pkl_path in pkl_files:
        print(f"\n📁 檢查檔案: {pkl_path}")
        
        if not os.path.exists(pkl_path):
            print(f"❌ 檔案不存在")
            continue
        
        print(f"✅ 檔案存在")
        
        try:
            df = pd.read_pickle(pkl_path)
            print(f"📊 資料形狀: {df.shape}")
            
            # 重置索引
            if isinstance(df.index, pd.MultiIndex):
                df_reset = df.reset_index()
                print(f"📋 多層索引已重置")
            else:
                df_reset = df.copy()
                print(f"📋 單層索引")
            
            print(f"📋 欄位: {list(df_reset.columns)}")
            
            # 檢查關鍵欄位
            required_columns = ['當月營收', 'stock_id']
            missing_columns = [col for col in required_columns if col not in df_reset.columns]
            
            if missing_columns:
                print(f"❌ 缺少必要欄位: {missing_columns}")
                continue
            else:
                print(f"✅ 包含必要欄位: {required_columns}")
            
            # 檢查資料品質
            total_records = len(df_reset)
            valid_revenue_records = len(df_reset[df_reset['當月營收'].notna()])
            
            print(f"📊 總記錄數: {total_records:,}")
            print(f"📊 有效營收記錄: {valid_revenue_records:,}")
            print(f"📊 有效率: {valid_revenue_records/total_records*100:.1f}%")
            
            # 檢查日期資訊
            if 'date' in df_reset.columns:
                unique_dates = df_reset['date'].nunique()
                latest_date = df_reset['date'].max()
                print(f"📅 日期範圍: {unique_dates} 個不同日期")
                print(f"📅 最新日期: {latest_date}")
                
                # 檢查最新月份的資料
                latest_month_data = df_reset[df_reset['date'] == latest_date]
                print(f"📅 最新月份記錄數: {len(latest_month_data):,}")
            
            # 顯示營收樣本
            print(f"\n📊 營收資料樣本:")
            revenue_sample = df_reset['當月營收'].dropna().head(5)
            for i, revenue in enumerate(revenue_sample, 1):
                print(f"  {i}. {revenue}")
            
            # 只分析第一個找到的檔案
            return df_reset
            
        except Exception as e:
            print(f"❌ 讀取失敗: {e}")
            continue
    
    return None

def test_ranking_calculation():
    """測試排名計算功能"""
    print("\n🏆 測試排名計算功能")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 測試股票和營收
    test_cases = [
        ('2330', '263,708,978', '台積電 - 超高營收'),
        ('2317', '120,000,000', '鴻海 - 高營收'),
        ('8021', '25,000', '尖點 - 低營收'),
        ('1234', '50,000,000', '測試股票 - 中等營收'),
    ]
    
    print("測試案例:")
    for i, (stock_code, revenue, description) in enumerate(test_cases, 1):
        print(f"\n{i}. {description}")
        print(f"   股票代碼: {stock_code}")
        print(f"   當月營收: {revenue}")
        
        try:
            ranking = gui.calculate_monthly_revenue_ranking(stock_code, revenue)
            if ranking:
                print(f"   ✅ 計算排名: 第 {ranking} 名")
            else:
                print(f"   ❌ 無法計算排名")
        except Exception as e:
            print(f"   ❌ 計算失敗: {e}")
    
    app.quit()

def test_complete_process():
    """測試完整的資料處理流程"""
    print("\n🔄 測試完整的資料處理流程")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 模擬從資料庫查詢的結果
    test_result = ('2330', '台積電', '2025-07-01', '263708978', '221850000', '207870000')
    
    print("模擬資料庫查詢結果:")
    print(f"  股票ID: {test_result[0]}")
    print(f"  股票名稱: {test_result[1]}")
    print(f"  日期: {test_result[2]}")
    print(f"  當月營收: {test_result[3]}")
    print(f"  上月營收: {test_result[4]}")
    print(f"  去年同月營收: {test_result[5]}")
    
    try:
        stock_data = gui._process_monthly_revenue_result(test_result, '2330', '台積電')
        
        if stock_data:
            print("\n✅ 處理結果:")
            for key, value in stock_data.items():
                print(f"  {key}: {value}")
            
            # 特別檢查排名
            ranking = stock_data.get('排名', 'N/A')
            if ranking != 'N/A':
                print(f"\n🏆 排名計算成功: 第 {ranking} 名")
            else:
                print(f"\n❌ 排名計算失敗")
        else:
            print("\n❌ 資料處理失敗")
            
    except Exception as e:
        print(f"\n❌ 處理過程發生錯誤: {e}")
    
    app.quit()

def main():
    """主函數"""
    print("🚀 月營收排名計算功能測試")
    print("=" * 60)
    
    # 測試營收清理
    test_revenue_cleaning()
    
    # 測試資料存取
    df = test_monthly_revenue_data_access()
    
    if df is not None:
        # 測試排名計算
        test_ranking_calculation()
        
        # 測試完整流程
        test_complete_process()
    else:
        print("\n❌ 無法存取月營收資料，跳過排名計算測試")
    
    print("\n" + "=" * 60)
    print("🎉 測試完成！")
    
    print("\n修復內容總結:")
    print("✅ 1. 月營收排名計算：基於實際營收資料排序")
    print("✅ 2. 營收數值清理：處理各種格式的營收資料")
    print("✅ 3. 動態排名計算：即使不在前100名也顯示實際排名")
    print("✅ 4. 多資料來源：支援新舊PKL檔案")
    print("✅ 5. 錯誤處理：完善的異常處理機制")
    
    print("\n預期效果:")
    print("🏆 台積電：應該顯示第 1 名或前幾名")
    print("🏆 鴻海：應該顯示前 10 名內")
    print("🏆 其他股票：根據實際營收顯示真實排名")
    print("🏆 即使營收較低：也會顯示具體排名（如第 500 名）")
    
    print("\n請啟動主程式測試實際效果：")
    print("python O3mh_gui_v21_optimized.py")

if __name__ == "__main__":
    main()
