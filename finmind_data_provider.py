#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FinMind數據提供器
整合FinMind API，為策略提供真實基本面數據
"""

import os
import requests
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List
import time
import json
import sqlite3
import numpy as np
from finmind_quota_manager import FinMindQuotaManager

class FinMindDataProvider:
    """FinMind數據提供器 - 智能配額管理版本"""

    def __init__(self, api_token=None, cache_db="finmind_cache.db"):
        """
        初始化FinMind數據提供器

        Args:
            api_token: FinMind API Token，如果為None則從環境變數獲取
            cache_db: 緩存數據庫路徑
        """
        self.api_token = api_token or os.getenv('FINMIND_API_TOKEN')
        self.base_url = "https://api.finmindtrade.com/api/v4/data"
        self.headers = {'Content-Type': 'application/json'}
        self.logger = logging.getLogger(__name__)
        self.cache_db = cache_db

        # 初始化配額管理器
        self.quota_manager = FinMindQuotaManager(cache_db)

        # 緩存設定（延長緩存時間以節省API）
        self.cache_duration = {
            'financial': 30*24*60*60,  # 財務數據緩存30天
            'revenue': 7*24*60*60,     # 營收數據緩存7天
            'dividend': 30*24*60*60,   # 股利數據緩存30天
            'per': 4*60*60,            # 本益比數據緩存4小時
            'stock_info': 7*24*60*60,  # 股票基本資料緩存7天
        }

        if not self.api_token:
            self.logger.warning("⚠️ FinMind API Token未設定")
    
    def _check_cache(self, cache_key: str, dataset: str) -> Optional[Dict]:
        """檢查數據庫緩存"""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT data, created_at, expires_at FROM data_cache
                WHERE cache_key = ? AND expires_at > datetime('now')
            ''', (cache_key,))

            result = cursor.fetchone()
            conn.close()

            if result:
                data_json, created_at, expires_at = result
                self.logger.debug(f"📦 緩存命中: {cache_key}")
                return json.loads(data_json)

            return None

        except Exception as e:
            self.logger.error(f"❌ 緩存檢查失敗: {e}")
            return None

    def _save_cache(self, cache_key: str, dataset: str, stock_id: str, data: Dict, priority: int = 2):
        """保存到數據庫緩存"""
        try:
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()

            expires_at = datetime.now() + timedelta(seconds=self.cache_duration.get(dataset, 3600))

            # 處理numpy數據類型的JSON序列化問題
            def convert_numpy_types(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return obj

            # 遞歸轉換數據中的numpy類型
            def clean_data(data):
                if isinstance(data, dict):
                    return {k: clean_data(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [clean_data(v) for v in data]
                else:
                    return convert_numpy_types(data)

            cleaned_data = clean_data(data)

            cursor.execute('''
                INSERT OR REPLACE INTO data_cache
                (cache_key, dataset, stock_id, data, expires_at, priority)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (cache_key, dataset, stock_id, json.dumps(cleaned_data), expires_at, priority))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 數據已緩存: {cache_key}")

        except Exception as e:
            self.logger.error(f"❌ 緩存保存失敗: {e}")

    def _check_api_quota(self):
        """檢查API配額"""
        usage = self.quota_manager.get_current_usage()
        return usage['can_make_request']
    
    def _make_api_request(self, dataset, stock_id="", **params):
        """發送API請求（智能配額管理）"""
        if not self.api_token:
            self.logger.error("❌ API Token未設定")
            return None

        # 檢查配額
        if not self._check_api_quota():
            self.logger.warning("⚠️ API配額不足，跳過請求")
            return None

        try:
            # 準備參數
            request_params = {
                "dataset": dataset,
                "token": self.api_token,
                **params
            }

            # 發送請求
            start_time = time.time()
            response = requests.get(self.base_url, params=request_params)
            request_time = time.time() - start_time

            success = False
            response_size = 0
            data = None

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('status') == 200:
                    data = response_data.get('data', [])
                    response_size = len(data)
                    success = True
                    self.logger.debug(f"✅ API請求成功: {dataset}, 數據量: {response_size}")
                else:
                    self.logger.error(f"❌ API錯誤: {response_data.get('msg', 'Unknown error')}")
            else:
                self.logger.error(f"❌ HTTP錯誤: {response.status_code}")

            # 記錄API使用
            self.quota_manager.record_api_call(dataset, stock_id, success, response_size)

            # 如果請求太快，稍微延遲以避免觸發限制
            if request_time < 0.1:
                time.sleep(0.1 - request_time)

            return data

        except Exception as e:
            self.logger.error(f"❌ API請求失敗: {e}")
            # 記錄失敗的API調用
            self.quota_manager.record_api_call(dataset, stock_id, False, 0)
            return None
    
    def get_financial_statements(self, stock_id: str, year: int = None, force_update: bool = False) -> Optional[Dict]:
        """
        獲取財務報表數據（智能緩存版本）

        Args:
            stock_id: 股票代碼
            year: 年份，如果為None則獲取最新數據
            force_update: 是否強制更新

        Returns:
            財務報表數據字典
        """
        cache_key = f"financial_{stock_id}_{year or 'latest'}"

        # 檢查數據庫緩存
        if not force_update:
            cached_data = self._check_cache(cache_key, 'financial')
            if cached_data:
                self.logger.debug(f"📦 使用緩存財務數據: {stock_id}")
                return cached_data
        
        try:
            params = {"data_id": stock_id}
            if year:
                params["start_date"] = f"{year}-01-01"
                params["end_date"] = f"{year}-12-31"
            else:
                # 獲取最近2年的數據
                current_year = datetime.now().year
                params["start_date"] = f"{current_year-1}-01-01"
            
            data = self._make_api_request("TaiwanStockFinancialStatements", stock_id=stock_id, **params)
            
            if data:
                # 處理財務數據
                df = pd.DataFrame(data)
                if not df.empty:
                    # 獲取最新的財務數據
                    latest = df.iloc[-1].to_dict()
                    
                    # 標準化欄位名稱
                    result = {
                        'stock_id': stock_id,
                        'date': latest.get('date'),
                        'total_assets': latest.get('TotalAssets', 0),
                        'total_equity': latest.get('TotalEquity', 0),
                        'revenue': latest.get('Revenue', 0),
                        'net_income': latest.get('NetIncome', 0),
                        'eps': latest.get('EarningsPerShare', 0),
                        'roe': latest.get('ReturnOnEquity', 0),
                        'debt_ratio': latest.get('DebtRatio', 0),
                        'is_real_data': True
                    }
                    
                    # 保存到數據庫緩存
                    self._save_cache(cache_key, 'financial', stock_id, result, priority=1)

                    self.logger.info(f"✅ 獲取財務數據: {stock_id}")
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 獲取財務數據失敗 {stock_id}: {e}")
            return None
    
    def get_dividend_data(self, stock_id: str, year: int = None, force_update: bool = False) -> Optional[Dict]:
        """
        獲取股利數據（智能緩存版本）

        Args:
            stock_id: 股票代碼
            year: 年份，如果為None則獲取最新數據
            force_update: 是否強制更新

        Returns:
            股利數據字典
        """
        cache_key = f"dividend_{stock_id}_{year or 'latest'}"

        # 檢查數據庫緩存
        if not force_update:
            cached_data = self._check_cache(cache_key, 'dividend')
            if cached_data:
                self.logger.debug(f"📦 使用緩存股利數據: {stock_id}")
                return cached_data
        
        try:
            params = {"data_id": stock_id}
            if year:
                params["start_date"] = f"{year}-01-01"
                params["end_date"] = f"{year}-12-31"
            else:
                # 獲取最近3年的數據
                current_year = datetime.now().year
                params["start_date"] = f"{current_year-2}-01-01"
            
            data = self._make_api_request("TaiwanStockDividend", stock_id=stock_id, **params)
            
            if data:
                df = pd.DataFrame(data)
                if not df.empty:
                    # 獲取最新的股利數據
                    latest = df.iloc[-1].to_dict()
                    
                    # 計算殖利率（需要股價數據）
                    cash_dividend = latest.get('CashDividend', 0)
                    stock_dividend = latest.get('StockDividend', 0)
                    
                    result = {
                        'stock_id': stock_id,
                        'date': latest.get('date'),
                        'cash_dividend': cash_dividend,
                        'stock_dividend': stock_dividend,
                        'total_dividend': cash_dividend + stock_dividend,
                        'ex_dividend_date': latest.get('ExDividendDate'),
                        'is_real_data': True
                    }
                    
                    # 保存到數據庫緩存
                    self._save_cache(cache_key, 'dividend', stock_id, result, priority=2)

                    self.logger.info(f"✅ 獲取股利數據: {stock_id}")
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 獲取股利數據失敗 {stock_id}: {e}")
            return None
    
    def get_per_data(self, stock_id: str, days: int = 30, force_update: bool = False) -> Optional[Dict]:
        """
        獲取本益比數據（智能緩存版本）

        Args:
            stock_id: 股票代碼
            days: 獲取最近幾天的數據
            force_update: 是否強制更新

        Returns:
            本益比數據字典
        """
        cache_key = f"per_{stock_id}_{days}"

        # 檢查數據庫緩存
        if not force_update:
            cached_data = self._check_cache(cache_key, 'per')
            if cached_data:
                self.logger.debug(f"📦 使用緩存本益比數據: {stock_id}")
                return cached_data
        
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            params = {
                "data_id": stock_id,
                "start_date": start_date,
                "end_date": end_date
            }
            
            data = self._make_api_request("TaiwanStockPER", stock_id=stock_id, **params)
            
            if data:
                df = pd.DataFrame(data)
                if not df.empty:
                    # 獲取最新的本益比數據
                    latest = df.iloc[-1].to_dict()
                    
                    result = {
                        'stock_id': stock_id,
                        'date': latest.get('date'),
                        'pe_ratio': latest.get('PER', 0),
                        'price': latest.get('price', 0),
                        'is_real_data': True
                    }
                    
                    # 保存到數據庫緩存
                    self._save_cache(cache_key, 'per', stock_id, result, priority=3)

                    self.logger.info(f"✅ 獲取本益比數據: {stock_id}")
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 獲取本益比數據失敗 {stock_id}: {e}")
            return None
    
    def get_revenue_data(self, stock_id: str, months: int = 12, force_update: bool = False) -> Optional[Dict]:
        """
        獲取營收數據（智能緩存版本）

        Args:
            stock_id: 股票代碼
            months: 獲取最近幾個月的數據
            force_update: 是否強制更新

        Returns:
            營收數據字典，包含營收成長率
        """
        cache_key = f"revenue_{stock_id}_{months}"

        # 檢查數據庫緩存
        if not force_update:
            cached_data = self._check_cache(cache_key, 'revenue')
            if cached_data:
                self.logger.debug(f"📦 使用緩存營收數據: {stock_id}")
                return cached_data
        
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=months*30)).strftime('%Y-%m-%d')
            
            params = {
                "data_id": stock_id,
                "start_date": start_date,
                "end_date": end_date
            }
            
            data = self._make_api_request("TaiwanStockMonthRevenue", stock_id=stock_id, **params)
            
            if data:
                df = pd.DataFrame(data)
                if not df.empty and len(df) >= 2:
                    # 計算營收成長率
                    latest = df.iloc[-1]
                    previous = df.iloc[-2]
                    
                    current_revenue = latest.get('revenue', 0)
                    previous_revenue = previous.get('revenue', 0)
                    
                    growth_rate = 0
                    if previous_revenue > 0:
                        growth_rate = (current_revenue - previous_revenue) / previous_revenue * 100
                    
                    # 計算年營收成長率（如果有12個月數據）
                    yoy_growth_rate = 0
                    if len(df) >= 12:
                        year_ago = df.iloc[-12]
                        year_ago_revenue = year_ago.get('revenue', 0)
                        if year_ago_revenue > 0:
                            yoy_growth_rate = (current_revenue - year_ago_revenue) / year_ago_revenue * 100
                    
                    result = {
                        'stock_id': stock_id,
                        'date': latest.get('date'),
                        'revenue': current_revenue,
                        'revenue_growth_rate': growth_rate,
                        'yoy_growth_rate': yoy_growth_rate,
                        'revenue_trend': 'up' if growth_rate > 0 else 'down',
                        'is_real_data': True
                    }
                    
                    # 保存到數據庫緩存
                    self._save_cache(cache_key, 'revenue', stock_id, result, priority=2)

                    self.logger.info(f"✅ 獲取營收數據: {stock_id}, 成長率: {growth_rate:.2f}%")
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 獲取營收數據失敗 {stock_id}: {e}")
            return None
    
    def get_comprehensive_data(self, stock_id: str) -> Dict:
        """
        獲取股票的綜合基本面數據
        
        Args:
            stock_id: 股票代碼
            
        Returns:
            綜合數據字典
        """
        result = {
            'stock_id': stock_id,
            'timestamp': datetime.now().isoformat(),
            'data_sources': [],
            'is_real_data': False
        }
        
        # 獲取各種數據
        financial = self.get_financial_statements(stock_id)
        dividend = self.get_dividend_data(stock_id)
        per = self.get_per_data(stock_id)
        revenue = self.get_revenue_data(stock_id)
        
        # 整合數據
        if financial:
            result.update(financial)
            result['data_sources'].append('財務報表')
        
        if dividend:
            result.update(dividend)
            result['data_sources'].append('股利數據')
        
        if per:
            result.update(per)
            result['data_sources'].append('本益比數據')
        
        if revenue:
            result.update(revenue)
            result['data_sources'].append('營收數據')
        
        # 如果有任何真實數據，標記為真實數據
        if result['data_sources']:
            result['is_real_data'] = True
        
        return result
    
    def get_api_usage_stats(self) -> Dict:
        """獲取API使用統計"""
        return self.quota_manager.get_current_usage()
