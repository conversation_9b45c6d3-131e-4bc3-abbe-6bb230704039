#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
輕量級新聞爬蟲 - 最小系統負擔
專為減少Loading負擔而設計
"""

from news_crawler_base import NewsCrawlerBase, NewsCrawlerException
from datetime import datetime
import time
from random import randint
import requests
import json
import re
import ssl
import urllib3
import gc
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

class LightweightNewsCrawler(NewsCrawlerBase):
    """輕量級新聞爬蟲 - 最小系統負擔"""
    
    def __init__(self, db_path=None):
        super().__init__(db_path)
        
        # 極度輕量化設定
        self.max_news_per_batch = 5  # 每批最多5篇新聞
        self.delay_between_requests = 1.0  # 減少延遲到1秒
        self.timeout = 8  # 超時時間8秒
        self.max_api_calls = 3  # 最多3次API調用
        
        # 簡化的請求標頭
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }

    def extract(self, ndaysAgo, interval: str, stock_code: str = None) -> list:
        """
        輕量級新聞提取 - 只獲取標題和連結，不獲取完整內容
        """
        try:
            self.logger.info("🚀 開始輕量級新聞爬取（僅標題模式）...")
            
            # 只使用鉅亨網API，不獲取完整內容
            news_links = self._get_news_links_only(ndaysAgo, interval, stock_code)
            
            if not news_links:
                return []
            
            # 限制數量
            if len(news_links) > self.max_news_per_batch:
                news_links = news_links[:self.max_news_per_batch]
                self.logger.info(f"⚡ 限制新聞數量至 {self.max_news_per_batch} 篇")
            
            return news_links
            
        except Exception as e:
            self.logger.error(f"❌ 輕量級新聞提取失敗: {e}")
            raise NewsCrawlerException(f"輕量級新聞爬取失敗: {e}")

    def _get_news_links_only(self, ndaysAgo, interval: str, stock_code: str = None):
        """只獲取新聞連結和標題，不獲取內容"""
        try:
            # 計算時間戳
            start = f'{ndaysAgo.year}/{ndaysAgo.month}/{ndaysAgo.day} {interval[0:2]}:{interval[2:4]}:00'
            struct_time = time.strptime(start, "%Y/%m/%d %H:%M:%S")
            start_stamp = int(time.mktime(struct_time))
            
            ended = f'{self.timeNow.year}/{self.timeNow.month}/{self.timeNow.day} {interval[0:2]}:{interval[2:4]}:00'
            struct_time = time.strptime(ended, "%Y/%m/%d %H:%M:%S")
            ended_stamp = int(time.mktime(struct_time))
            
            # 只獲取少量新聞
            url = f'https://api.cnyes.com/media/api/v1/newslist/category/tw_stock?startAt={start_stamp}&endAt={ended_stamp}&limit=8&page=1'
            
            self.logger.info(f"🔍 輕量級API調用: 限制8篇新聞")
            
            response = requests.get(url, headers=self.headers, verify=False, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if not data.get('items', {}).get('data'):
                return []
            
            news_items = data['items']['data']
            self.logger.info(f"📋 獲取 {len(news_items)} 個新聞標題")
            
            # 只處理標題和連結，不獲取內容
            result = []
            for i, item in enumerate(news_items):
                try:
                    newsDateTime = datetime.fromtimestamp(item['publishAt'])
                    newsDate = newsDateTime.strftime("%Y%m%d")
                    newsTime = newsDateTime.strftime("%H%M%S")
                    link = f"https://news.cnyes.com/news/id/{item['newsId']}?exp=a"
                    newsID = f'anue_{item["newsId"]}'
                    title = item['title']
                    
                    # 檢測股票代碼（只在標題中檢查）
                    detected_stock_code = None
                    if stock_code:
                        if stock_code.upper() in title.upper():
                            detected_stock_code = stock_code
                    else:
                        stock_pattern = re.compile(r'[0-9]{4}')
                        matches = stock_pattern.findall(title)
                        if matches:
                            detected_stock_code = matches[0]
                    
                    # 如果指定了股票代碼但不匹配，跳過
                    if stock_code and not detected_stock_code:
                        continue
                    
                    # 不獲取完整內容，只保存標題和連結
                    result.append((
                        newsID, newsDate, newsTime, title, link, 
                        "", "輕量級模式：點擊連結查看完整內容", "", detected_stock_code
                    ))
                    
                    # 立即垃圾回收
                    if i % 2 == 0:
                        gc.collect()
                    
                except Exception as e:
                    self.logger.warning(f"處理新聞項目失敗: {e}")
                    continue
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 獲取新聞連結失敗: {e}")
            return []

    def transform(self, rawDatas):
        """轉換資料格式"""
        # 新聞內容: (news_id, date, time, source, title, reporter, link, content, stock_code)
        cleanDatas = []
        cleanTags = []
        
        for rawData in rawDatas:
            cleanDatas.append((
                rawData[0], rawData[1], rawData[2], 'anue_lite', rawData[3], 
                rawData[5], rawData[4], rawData[6], rawData[8]
            ))
            
            # 輕量級模式不處理標籤
        
        return iter(cleanDatas), iter(cleanTags)

    def run_lightweight(self, ndays: int = 1, stock_code: str = None):
        """
        輕量級執行模式
        """
        from datetime import timedelta
        ndaysAgo = self.timeNow - timedelta(days=ndays)
        
        try:
            self.logger.info(f"🚀 輕量級模式：爬取 {ndays} 天新聞")
            if stock_code:
                self.logger.info(f"🎯 目標股票: {stock_code}")
            
            # 提取新聞連結
            rawDatas = self.extract(ndaysAgo, "0600", stock_code)
            self.logger.info(f"📊 提取到 {len(rawDatas)} 筆新聞連結")
            
            if not rawDatas:
                return "⚠️ 未找到相關新聞"
            
            # 轉換資料格式
            cleanDatas, cleanTags = self.transform(rawDatas)
            
            # 載入到資料庫
            status = self.load(cleanDatas, cleanTags)
            self.logger.info(f"✅ 輕量級新聞爬取完成: {status}")
            
            return status
            
        except Exception as e:
            error_msg = self.errLog(e)
            self.logger.error(f"❌ 輕量級新聞爬取失敗: {error_msg}")
            return error_msg

class QuickNewsCrawler:
    """
    極速新聞爬蟲 - 不使用資料庫，直接返回結果
    專為即時查詢設計
    """
    
    def __init__(self):
        self.timeout = 5
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
    
    def quick_search(self, stock_code: str, days: int = 1):
        """
        極速搜尋股票新聞 - 不存資料庫
        """
        try:
            from datetime import datetime, timedelta
            
            # 計算時間範圍
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            start_stamp = int(start_time.timestamp())
            end_stamp = int(end_time.timestamp())
            
            # API調用
            url = f'https://api.cnyes.com/media/api/v1/newslist/category/tw_stock?startAt={start_stamp}&endAt={end_stamp}&limit=5&page=1'
            
            response = requests.get(url, headers=self.headers, verify=False, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if not data.get('items', {}).get('data'):
                return []
            
            # 處理結果
            results = []
            for item in data['items']['data']:
                title = item['title']
                
                # 檢查是否包含股票代碼
                if stock_code.upper() in title.upper():
                    newsDateTime = datetime.fromtimestamp(item['publishAt'])
                    
                    results.append({
                        'date': newsDateTime.strftime("%Y%m%d"),
                        'time': newsDateTime.strftime("%H%M%S"),
                        'source': 'anue_quick',
                        'title': title,
                        'link': f"https://news.cnyes.com/news/id/{item['newsId']}?exp=a"
                    })
            
            return results
            
        except Exception as e:
            print(f"極速搜尋失敗: {e}")
            return []

if __name__ == '__main__':
    # 測試輕量級爬蟲
    print("🚀 測試輕量級新聞爬蟲")
    print("=" * 50)
    
    # 測試1: 輕量級爬蟲
    crawler = LightweightNewsCrawler()
    result = crawler.run_lightweight(ndays=1)
    print(f"📊 輕量級結果: {result}")
    
    # 測試2: 極速爬蟲
    print("\n🚀 測試極速新聞爬蟲")
    quick_crawler = QuickNewsCrawler()
    quick_results = quick_crawler.quick_search("2330", days=1)
    print(f"📊 極速結果: {len(quick_results)} 筆新聞")
    
    for news in quick_results:
        print(f"  📰 {news['title'][:50]}...")
