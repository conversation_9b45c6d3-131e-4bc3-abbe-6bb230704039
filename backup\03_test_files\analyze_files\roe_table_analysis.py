#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ROE表格結構分析
基於用戶提供的實際界面截圖分析
"""

def analyze_roe_table_structure():
    """分析ROE表格結構"""
    print("🔍 ROE表格結構分析")
    print("=" * 50)
    
    # 從截圖中提取的表格結構
    table_structure = {
        "標題": "熱門排行 - 年度ROE最高 (共計1895筆)",
        "篩選條件": {
            "資料顯示依據": "獲利能力-年度",
            "股票顯示範圍": "1-300",
            "報表匯出功能": ["匯出XLS", "匯出CSV", "匯出HTML"]
        },
        "表格欄位": [
            {"欄位名": "排名", "範例": "1, 2, 3, 4, 5, 6"},
            {"欄位名": "代號", "範例": "1213, 5314, 2718, 3293, 1446, 4763"},
            {"欄位名": "名稱", "範例": "大飲, 世紀*, 全心投控, 鈞象, 宏和, 材料*-KY"},
            {"欄位名": "成交", "範例": "13, 68.7, 50.7, 815, 23.15, 81"},
            {"欄位名": "財報年度", "範例": "2024, 2024, 2024, 2024, 2024, 2024"},
            {"欄位名": "營收(億)", "範例": "1.91, 6.28, 42.5, 185, 36.8, 157"},
            {"欄位名": "營收成長(%)", "範例": "+57.9, +636, +95.4, +30.6, +541, +42.5"},
            {"欄位名": "毛利(億)", "範例": "-0.42, 3.82, 34.5, 179, 18.4, 99.4"},
            {"欄位名": "毛利成長(%)", "範例": "+55, +2225, +222, +31.2, +662, +44.3"},
            {"欄位名": "淨利(億)", "範例": "4.93, 2.68, 30.9, 90.6, 13.5, 83.4"},
            {"欄位名": "淨利成長(%)", "範例": "+456, +764, +354, +41, +1130, +61.1"},
            {"欄位名": "毛利率(%)", "範例": "-22.3, 60.9, 81.1, 96.5, 49.9, 63.3"},
            {"欄位名": "毛率變化", "範例": "+56, +41.6, +31.9, +0.45, +7.91, +0.78"},
            {"欄位名": "淨利率(%)", "範例": "259, 42.7, 72.8, 48.9, 36.8, 53.4"},
            {"欄位名": "淨率變化", "範例": "+374, +6.52, +41.4, +3.59, +17.6, +6"},
            {"欄位名": "EPS(元)", "範例": "8.71, 18.25, 35.33, 32.14, 9.82, 84.36"},
            {"欄位名": "EPS增減(元)", "範例": "+11.16, +16.14, +27.43, -13.47, +9.02, +21.84"},
            {"欄位名": "ROE(%)", "範例": "104, 84.6, 81.7, 63.4, 63, 62.8"},
            {"欄位名": "ROE變化", "範例": "+151, +64.4, +46.8, +7.16, +55.6, -12"}
        ]
    }
    
    print("📊 表格基本資訊:")
    print(f"  標題: {table_structure['標題']}")
    print(f"  總筆數: 1895筆")
    print(f"  顯示範圍: 1-300")
    
    print("\n📋 重要欄位:")
    key_fields = ["代號", "名稱", "ROE(%)", "ROE變化", "EPS(元)", "財報年度"]
    for field_info in table_structure["表格欄位"]:
        if field_info["欄位名"] in key_fields:
            print(f"  ✅ {field_info['欄位名']}: {field_info['範例']}")
    
    print("\n🎯 抓取目標:")
    print("  • 股票代號 (如: 1213, 5314, 2718)")
    print("  • 股票名稱 (如: 大飲, 世紀*, 全心投控)")
    print("  • ROE數值 (如: 104, 84.6, 81.7)")
    print("  • ROE變化 (如: +151, +64.4, +46.8)")
    print("  • EPS數值 (如: 8.71, 18.25, 35.33)")
    print("  • 財報年度 (如: 2024)")
    
    return table_structure

def create_roe_scraper():
    """創建ROE抓取器"""
    print("\n🚀 創建ROE抓取器")
    print("=" * 50)
    
    scraper_code = '''
import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class GoodinfoROEScraper:
    def __init__(self):
        self.base_url = "https://goodinfo.tw/tw2/StockList.asp"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def scrape_roe_data_selenium(self):
        """使用Selenium抓取ROE資料"""
        try:
            # 設置Chrome選項
            options = webdriver.ChromeOptions()
            options.add_argument('--headless')  # 無頭模式
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            driver = webdriver.Chrome(options=options)
            
            # 訪問ROE頁面
            url = f"{self.base_url}?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
            driver.get(url)
            
            # 等待表格載入
            wait = WebDriverWait(driver, 20)
            table = wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            
            # 獲取頁面HTML
            html = driver.page_source
            driver.quit()
            
            # 解析HTML
            return self.parse_roe_html(html)
            
        except Exception as e:
            print(f"Selenium抓取失敗: {e}")
            return None
    
    def parse_roe_html(self, html):
        """解析ROE HTML資料"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 尋找資料表格
            tables = soup.find_all('table')
            
            for table in tables:
                # 檢查是否為ROE資料表格
                if self.is_roe_table(table):
                    return self.extract_roe_data(table)
            
            return None
            
        except Exception as e:
            print(f"HTML解析失敗: {e}")
            return None
    
    def is_roe_table(self, table):
        """判斷是否為ROE資料表格"""
        try:
            # 檢查表格標題行
            header_row = table.find('tr')
            if header_row:
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
                # 檢查是否包含ROE相關欄位
                roe_keywords = ['ROE', '代號', '名稱', 'EPS']
                return any(keyword in ''.join(headers) for keyword in roe_keywords)
            return False
        except:
            return False
    
    def extract_roe_data(self, table):
        """提取ROE資料"""
        try:
            data = []
            rows = table.find_all('tr')
            
            # 獲取標題行
            header_row = rows[0] if rows else None
            if not header_row:
                return None
            
            headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
            
            # 處理資料行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= len(headers):
                    row_data = {}
                    for i, cell in enumerate(cells[:len(headers)]):
                        if i < len(headers):
                            row_data[headers[i]] = cell.get_text().strip()
                    
                    # 驗證是否為有效的股票資料
                    if self.is_valid_stock_row(row_data):
                        data.append(row_data)
            
            return data
            
        except Exception as e:
            print(f"資料提取失敗: {e}")
            return None
    
    def is_valid_stock_row(self, row_data):
        """驗證是否為有效的股票資料行"""
        try:
            # 檢查是否有股票代號
            for key, value in row_data.items():
                if '代號' in key and value.isdigit() and len(value) == 4:
                    return True
            return False
        except:
            return False
    
    def save_to_database(self, data, db_path="D:/Finlab/history/tables/roe_data.db"):
        """儲存資料到數據庫"""
        try:
            import sqlite3
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 創建表格
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS roe_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT,
                    stock_name TEXT,
                    roe_value REAL,
                    roe_change REAL,
                    eps_value REAL,
                    report_year INTEGER,
                    crawl_date TEXT,
                    UNIQUE(stock_code, report_year)
                )
            """)
            
            # 插入資料
            from datetime import datetime
            crawl_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            for item in data:
                try:
                    stock_code = self.extract_stock_code(item)
                    stock_name = self.extract_stock_name(item)
                    roe_value = self.extract_roe_value(item)
                    roe_change = self.extract_roe_change(item)
                    eps_value = self.extract_eps_value(item)
                    report_year = self.extract_report_year(item)
                    
                    cursor.execute("""
                        INSERT OR REPLACE INTO roe_data 
                        (stock_code, stock_name, roe_value, roe_change, eps_value, report_year, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (stock_code, stock_name, roe_value, roe_change, eps_value, report_year, crawl_date))
                    
                except Exception as e:
                    print(f"插入資料失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            print(f"✅ 成功儲存 {len(data)} 筆ROE資料到數據庫")
            return True
            
        except Exception as e:
            print(f"❌ 儲存數據庫失敗: {e}")
            return False
    
    def extract_stock_code(self, row_data):
        """提取股票代號"""
        for key, value in row_data.items():
            if '代號' in key:
                return value
        return None
    
    def extract_stock_name(self, row_data):
        """提取股票名稱"""
        for key, value in row_data.items():
            if '名稱' in key:
                return value.replace('*', '').replace('-KY', '')
        return None
    
    def extract_roe_value(self, row_data):
        """提取ROE數值"""
        for key, value in row_data.items():
            if 'ROE' in key and '%' in key:
                try:
                    return float(value.replace('%', '').replace('+', '').replace(',', ''))
                except:
                    return None
        return None
    
    def extract_roe_change(self, row_data):
        """提取ROE變化"""
        for key, value in row_data.items():
            if 'ROE' in key and '變化' in key:
                try:
                    return float(value.replace('+', '').replace(',', ''))
                except:
                    return None
        return None
    
    def extract_eps_value(self, row_data):
        """提取EPS數值"""
        for key, value in row_data.items():
            if 'EPS' in key and '元' in key:
                try:
                    return float(value.replace('+', '').replace(',', ''))
                except:
                    return None
        return None
    
    def extract_report_year(self, row_data):
        """提取財報年度"""
        for key, value in row_data.items():
            if '年度' in key:
                try:
                    return int(value)
                except:
                    return 2024  # 預設值
        return 2024

# 使用範例
if __name__ == "__main__":
    scraper = GoodinfoROEScraper()
    
    print("🚀 開始抓取ROE資料...")
    data = scraper.scrape_roe_data_selenium()
    
    if data:
        print(f"✅ 成功抓取 {len(data)} 筆資料")
        
        # 儲存到數據庫
        scraper.save_to_database(data)
        
        # 顯示前5筆資料
        print("\\n📊 前5筆資料:")
        for i, item in enumerate(data[:5]):
            print(f"{i+1}. {item}")
    else:
        print("❌ 抓取失敗")
'''
    
    print("📋 ROE抓取器特點:")
    print("  ✅ 使用Selenium處理JavaScript動態載入")
    print("  ✅ 智能識別ROE資料表格")
    print("  ✅ 提取關鍵欄位: 代號、名稱、ROE、EPS")
    print("  ✅ 自動儲存到SQLite數據庫")
    print("  ✅ 支援資料驗證和清理")
    
    return scraper_code

def integration_plan():
    """整合計劃"""
    print("\n🔧 整合到現有系統")
    print("=" * 50)
    
    print("📋 整合步驟:")
    print("1. 將ROE抓取器加入爬蟲選單")
    print("2. 創建ROE資料庫表格")
    print("3. 添加ROE資料下載功能")
    print("4. 整合到除權息交易系統")
    print("5. 提供ROE資料查詢介面")
    
    print("\n🎯 預期效果:")
    print("  ✅ 自動抓取最新ROE排行資料")
    print("  ✅ 儲存到本地數據庫供分析使用")
    print("  ✅ 支援歷史資料追蹤")
    print("  ✅ 整合到投資分析工具中")
    
    return True

def main():
    """主函數"""
    print("🎯 ROE表格分析與抓取器開發")
    print("=" * 60)
    
    # 分析表格結構
    table_structure = analyze_roe_table_structure()
    
    # 創建抓取器
    scraper_code = create_roe_scraper()
    
    # 整合計劃
    integration_plan()
    
    print("\n" + "=" * 60)
    print("📋 總結")
    print("=" * 60)
    print("基於您提供的ROE表格界面，我已經:")
    print("✅ 分析了完整的表格結構")
    print("✅ 識別了所有重要欄位")
    print("✅ 設計了專用的ROE抓取器")
    print("✅ 規劃了系統整合方案")
    print("\n🚀 下一步:")
    print("我可以立即實現這個ROE抓取功能並整合到您的系統中！")
    
    return True

if __name__ == "__main__":
    main()
