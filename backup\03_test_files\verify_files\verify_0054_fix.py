#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速驗證0054修正效果
"""

import sys
import logging

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def verify_stock_filter():
    """驗證股票篩選器功能"""
    print("🔍 驗證股票篩選器功能")
    print("=" * 40)
    
    try:
        from stock_filter import StockFilter
        
        filter = StockFilter()
        
        # 測試關鍵股票代碼
        test_cases = [
            ('0050', True, '元大台灣50'),
            ('0054', False, '元大台商50（已下市）'),
            ('2330', True, '台積電'),
            ('1724', False, '台硝（已下市）'),
        ]
        
        all_passed = True
        for code, expected, name in test_cases:
            result = filter.is_valid_stock_code(code)
            status = "✅" if result == expected else "❌"
            action = "保留" if result else "排除"
            print(f"{code}: {action} - {name} {status}")
            
            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 股票篩選器測試失敗: {e}")
        return False


def verify_main_gui_methods():
    """驗證主GUI方法"""
    print("\n🔍 驗證主GUI方法")
    print("=" * 40)
    
    try:
        # 檢查主GUI文件中的關鍵方法
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('def filter_stock_codes', 'filter_stock_codes方法'),
            ('from stock_filter import StockFilter', '股票篩選器導入'),
            ('self.stock_filter = StockFilter()', '股票篩選器初始化'),
            ('valid_codes = self.filter_stock_codes', '篩選方法調用'),
            ("'0054'", '0054在已下市清單中'),
        ]
        
        all_passed = True
        for pattern, description in checks:
            found = pattern in content
            status = "✅" if found else "❌"
            print(f"{description}: {status}")
            
            if not found:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 主GUI方法檢查失敗: {e}")
        return False


def verify_delisted_stocks():
    """驗證已下市股票清單"""
    print("\n🔍 驗證已下市股票清單")
    print("=" * 40)
    
    try:
        from stock_filter import StockFilter
        
        filter = StockFilter()
        delisted_stocks = filter.get_delisted_stocks()
        
        print(f"已下市股票數量: {len(delisted_stocks)}")
        
        # 檢查關鍵已下市股票
        key_delisted = ['0054', '1724', '5820']
        found_count = 0
        
        for stock in key_delisted:
            if stock in delisted_stocks:
                print(f"✅ {stock} 在已下市清單中")
                found_count += 1
            else:
                print(f"❌ {stock} 不在已下市清單中")
        
        print(f"關鍵已下市股票檢查: {found_count}/{len(key_delisted)}")
        
        return found_count == len(key_delisted)
        
    except Exception as e:
        print(f"❌ 已下市股票清單檢查失敗: {e}")
        return False


def main():
    """主驗證函數"""
    print("🚀 0054修正效果快速驗證")
    print("=" * 50)
    
    tests = [
        ("股票篩選器功能", verify_stock_filter),
        ("主GUI方法", verify_main_gui_methods),
        ("已下市股票清單", verify_delisted_stocks),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        result = test_func()
        if result:
            passed += 1
    
    print(f"\n📊 驗證結果摘要")
    print("=" * 50)
    print(f"通過測試: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有驗證通過！")
        print("\n✅ 修正完成確認:")
        print("• 股票篩選器正常工作")
        print("• 主GUI已整合篩選功能")
        print("• 0054已在已下市清單中")
        print("• filter_stock_codes方法已添加")
        print("• load_stock_list方法已修正")
        print("\n🔄 請重新啟動主GUI以使修正生效")
        return True
    else:
        print("⚠️ 部分驗證失敗，請檢查修正內容")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
