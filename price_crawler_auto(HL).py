# price_crawler_auto.py

import requests
import pandas as pd
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import os
from datetime import datetime, timedelta
from io import StringIO
import logging
import smtplib
from email.mime.text import MIMEText
import time
import random
import sqlite3
from pydantic import BaseModel, field_validator
from tqdm import tqdm
import holidays
import sys

# ================================
# 設定日誌
# ================================

logging.basicConfig(
    filename='price_crawler_auto.log',
    level=logging.INFO,
    format='%(asctime)s:%(levelname)s:%(message)s'
)
logger = logging.getLogger(__name__)

# ================================
# 電子郵件通知函數
# ================================

def send_email(subject, body, to_email):
    from_email = "<EMAIL>"  # 替換為您的電子郵件
    password = "your_password"              # 替換為您的電子郵件密碼

    msg = MIMEText(body, 'plain', 'utf-8')
    msg['Subject'] = subject
    msg['From'] = from_email
    msg['To'] = to_email

    try:
        server = smtplib.SMTP_SSL('smtp.example.com', 465)  # 替換為您的SMTP伺服器
        server.login(from_email, password)
        server.sendmail(from_email, [to_email], msg.as_string())
        server.quit()
        logger.info("成功發送電子郵件通知。")
        print("成功發送電子郵件通知。")
    except Exception as e:
        logger.error(f"發送電子郵件時出錯: {e}")
        print(f"發送電子郵件時出錯: {e}")

# ================================
# 程式A: 股票清單抓取與處理
# ================================

def fetch_twse_stock_list(market_type=2):
    """
    抓取台灣證券交易所（TWSE）或櫃買中心（OTC）上市股票清單。

    參數:
        market_type (int): 市場類型，2 表示上市，4 表示上櫃。預設為2。

    回傳:
        pd.DataFrame: 包含股票清單的資料框。
    """
    url = f"https://isin.twse.com.tw/isin/C_public.jsp?strMode={market_type}"

    # 設定重試策略
    retry_strategy = Retry(
        total=5,  # 總共重試次數
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1  # 重試間隔時間
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    http = requests.Session()
    http.mount("https://", adapter)
    http.mount("http://", adapter)

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                      "AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/58.0.3029.110 Safari/537.3"
    }

    try:
        print(f"正在抓取市場類型 {market_type} 的股票清單...")
        response = http.get(url, headers=headers, timeout=20)
        response.raise_for_status()
        logger.info(f"成功取得HTTP回應（市場類型 {market_type}）。")
        print(f"成功取得市場類型 {market_type} 的股票清單。")
    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP請求錯誤（市場類型 {market_type}）: {e}")
        send_email(
            subject="抓取股票資料錯誤通知",
            body=f"抓取市場類型 {market_type} 時發生錯誤：{e}",
            to_email="<EMAIL>"  # 替換為您的電子郵件
        )
        return pd.DataFrame()

    try:
        tables = pd.read_html(StringIO(response.text))
        if not tables:
            logger.warning(f"未找到任何表格資料（市場類型 {market_type}）。")
            print(f"未找到任何表格資料（市場類型 {market_type}）。")
            return pd.DataFrame()
        df = tables[0]
    except ValueError as e:
        logger.error(f"解析HTML表格時出錯（市場類型 {market_type}）: {e}")
        send_email(
            subject="抓取股票資料錯誤通知",
            body=f"解析市場類型 {market_type} 的股票資料時發生錯誤：{e}",
            to_email="<EMAIL>"  # 替換為您的電子郵件
        )
        return pd.DataFrame()

    # 設定欄位名稱並移除前兩行無用資料
    df.columns = df.iloc[0]
    df = df.iloc[2:].reset_index(drop=True)

    # 移除股票代號為 NaN 的行
    filtered_df = df[df["有價證券代號及名稱"].notna()].reset_index(drop=True)

    # 分割「有價證券代號及名稱」為「股票代碼」和「公司名稱」
    split_columns = filtered_df["有價證券代號及名稱"].str.extract(r'(\d+)\s+(.*)')
    split_columns.columns = ["股票代碼", "公司名稱"]

    # 移除股票代碼為 NaN 的行
    split_columns = split_columns.dropna(subset=['股票代碼'])

    # 確保股票代碼只包含數字
    split_columns = split_columns[split_columns['股票代碼'].str.isdigit()]

    # 合併新的欄位到 DataFrame
    final_df = pd.concat([split_columns, filtered_df[["市場別", "產業別"]]], axis=1)

    print(f"市場類型 {market_type} 的股票清單抓取完成，共 {len(final_df)} 筆資料。")
    logger.info(f"市場類型 {market_type} 的股票清單抓取完成，共 {len(final_df)} 筆資料。")

    return final_df

def save_to_excel(df, directory, filename):
    """
    將 DataFrame 儲存為 Excel 檔案。

    參數:
        df (pd.DataFrame): 要儲存的資料框。
        directory (str): 儲存目錄的路徑。
        filename (str): 儲存的檔案名稱。
    """
    # 確認目錄存在，若不存在則建立
    if not os.path.exists(directory):
        try:
            os.makedirs(directory)
            logger.info(f"已建立目錄：{directory}")
            print(f"已建立目錄：{directory}")
        except Exception as e:
            logger.error(f"建立目錄時出錯: {e}")
            print(f"建立目錄時出錯: {e}")
            return

    # 完整的檔案路徑
    file_path = os.path.join(directory, filename)

    try:
        df.to_excel(file_path, index=False, engine='openpyxl')
        logger.info(f"資料已成功儲存到 {file_path}")
        print(f"資料已成功儲存到 {file_path}")
    except Exception as e:
        logger.error(f"儲存 Excel 檔案時出錯: {e}")
        print(f"儲存 Excel 檔案時出錯: {e}")

def process_stock_list():
    """
    執行程式A的主要流程：抓取、處理股票清單，並儲存為Excel檔案。
    回傳：
        pd.DataFrame: 包含股票清單的資料框。
    """
    # 定義要抓取的市場類型
    market_types = [2, 4]  # 2 表示上市，4 表示上櫃

    combined_df = pd.DataFrame()

    for market_type in market_types:
        logger.info(f"開始抓取市場類型 {market_type} 的資料。")
        stock_df = fetch_twse_stock_list(market_type=market_type)
        
        if not stock_df.empty:
            logger.info(f"成功抓取市場類型 {market_type} 的股票資料。")
            combined_df = pd.concat([combined_df, stock_df], ignore_index=True)
        else:
            logger.warning(f"未能抓取市場類型 {market_type} 的股票資料。")
            print(f"未能抓取市場類型 {market_type} 的股票資料。")

    if not combined_df.empty:
        # 去除可能的重複資料（如果有）
        combined_df.drop_duplicates(subset=["股票代碼"], inplace=True)

        # 確保股票代碼為字串並保留前導零
        combined_df['股票代碼'] = combined_df['股票代碼'].astype(str).str.zfill(4)

        # 過濾出股票代碼為4碼或前兩碼為00的行，且只包含數字
        filtered_combined_df = combined_df[
            (combined_df['股票代碼'].str.len() == 4) |
            (combined_df['股票代碼'].str.startswith('00'))
        ]

        # 確保股票代碼只包含數字
        filtered_combined_df = filtered_combined_df[filtered_combined_df['股票代碼'].str.isdigit()]

        # 定義排序優先級
        filtered_combined_df['sort_priority'] = filtered_combined_df['股票代碼'].apply(
            lambda x: 1 if len(x) == 4 else (2 + len(x))
        )

        # 定義數值股票代碼供排序
        filtered_combined_df['股票代碼_numeric'] = filtered_combined_df['股票代碼'].astype(int)

        # 根據「sort_priority」和「股票代碼_numeric」進行排序
        filtered_combined_df.sort_values(
            by=['sort_priority', '股票代碼_numeric'],
            inplace=True
        )

        # 重置索引
        filtered_combined_df.reset_index(drop=True, inplace=True)

        # 刪除輔助排序欄位
        filtered_combined_df.drop(columns=['sort_priority', '股票代碼_numeric'], inplace=True)

        # 統一欄位名稱
        filtered_combined_df.rename(columns={
            "股票代碼": "stock_id",
            "公司名稱": "stock_name",
            "市場別": "listing_status",
            "產業別": "industry"
        }, inplace=True)

        # 填入空值 "industry" 為 "ETF"
        filtered_combined_df['industry'] = filtered_combined_df['industry'].fillna('ETF')

        # 檢查是否成功重命名
        if 'stock_id' not in filtered_combined_df.columns:
            logger.error("重命名後缺少 'stock_id' 欄位。")
            print("錯誤：重命名後缺少 'stock_id' 欄位。")
            return pd.DataFrame()

        # 設定檔案名稱
        current_date = datetime.now().strftime("%Y%m%d")  # 取得當前日期
        filename_filtered = f"twse_and_otc_stock_list_filtered_{current_date}.xlsx"

        # 設定儲存目錄
        save_directory = r"D:\Finlab\history\summaries"  # 使用原始字串避免反斜線問題

        # 儲存過濾後並排序的資料
        save_to_excel(filtered_combined_df, save_directory, filename_filtered)

        # 將過濾後的資料另存為 D:\Finlab\history\holidays\stock_info.xlsx
        copy_directory = r"D:/Finlab/history/holidays"  # 使用正斜線避免反斜線逃逸問題
        copy_filename = "stock_info.xlsx"
        save_to_excel(filtered_combined_df, copy_directory, copy_filename)

        logger.info("股票清單已成功儲存。")
        print("股票清單已成功儲存。")
    else:
        logger.warning("未能抓取到任何市場的股票資料。")
        print("未能抓取到任何市場的股票資料。")

    return filtered_combined_df  # 回傳經過重命名和過濾的資料框

# ================================
# 程式B: 股票價格爬蟲功能
# ================================

# 定義 User-Agent 列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko)",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko)",
    # ... 添加更多 User-Agent
]

# 定義 TWSE 的 Pydantic 模型
class TWSEStockPrice(BaseModel):
    stock_id: str
    stock_name: str
    listing_status: str  # '上市'
    industry: str = ""    # 預設為空字串
    Volume: int
    Transaction: int
    TradeValue: int
    Open: float
    High: float
    Low: float
    Close: float
    Change: float
    date: str

    @field_validator('Volume', 'Transaction', 'TradeValue', 'Open', 'High', 'Low', 'Close', 'Change', mode='before')
    def convert_empty_to_zero(cls, v):
        if v in ['', ' ', 'X', '----', '---', '--']:
            return 0
        return v

    @field_validator('industry', mode='before')
    def default_industry(cls, v):
        return v or ""

# 定義 TPEX 的 Pydantic 模型
class TPEXStockPrice(BaseModel):
    stock_id: str
    stock_name: str
    listing_status: str  # '上櫃'
    industry: str = ""    # 預設為空字串
    Volume: int
    Transaction: int
    TradeValue: int
    Open: float
    High: float
    Low: float
    Close: float
    Change: float
    date: str

    @field_validator('Volume', 'Transaction', 'TradeValue', 'Open', 'High', 'Low', 'Close', 'Change', mode='before')
    def convert_empty_to_zero(cls, v):
        if v in ['', ' ', 'X', '----', '---', '--']:
            return 0
        return v

    @field_validator('industry', mode='before')
    def default_industry(cls, v):
        return v or ""

# 定義 Header 函數，使用隨機選擇的 User-Agent
def twse_header():
    return {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Connection": "keep-alive",
        "Host": "www.twse.com.tw",
        "Referer": "https://www.twse.com.tw/zh/page/trading/exchange/MI_INDEX.html",
        "User-Agent": random.choice(USER_AGENTS),  # 隨機選擇 User-Agent
        "X-Requested-With": "XMLHttpRequest",
    }

def tpex_header_custom():
    return {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Connection": "keep-alive",
        "Host": "www.tpex.org.tw",
        "Referer": "https://www.tpex.org.tw/web/stock/exright/exright.php",
        "User-Agent": random.choice(USER_AGENTS),  # 隨機選擇 User-Agent
        "X-Requested-With": "XMLHttpRequest",
    }

# 隨機延遲函數
def random_sleep(min_seconds=5, max_seconds=10):
    sleep_time = random.uniform(min_seconds, max_seconds)
    logger.debug(f"Sleeping for {sleep_time:.2f} seconds.")
    print(f"正在等待 {sleep_time:.2f} 秒以避免被封鎖...")
    time.sleep(sleep_time)

# 將 Gregorian 日期轉換為 ROC 日期格式 (YYY/MM/DD)
def convert_date(date: str) -> str:
    try:
        dt = datetime.strptime(date, "%Y-%m-%d")
        roc_year = dt.year - 1911
        roc_date = f"{roc_year}/{dt.month:02d}/{dt.day:02d}"
        return roc_date
    except Exception as e:
        logger.error(f"Failed to convert date: {str(e)}")
        print(f"日期轉換失敗：{str(e)}")
        return ""

# 生成日期範圍列表
def generate_date_range(start_date: str, end_date: str) -> list:
    """
    生成從 start_date 到 end_date 的日期列表，格式為 'YYYY-MM-DD'。
    """
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d").date()
        end = datetime.strptime(end_date, "%Y-%m-%d").date()
        delta = end - start
        return [(start + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(delta.days + 1)]
    except Exception as e:
        logger.error(f"生成日期範圍時出錯：{e}")
        print(f"生成日期範圍時出錯：{e}")
        return []

# Colname 轉換函數（TWSE）
def colname_zh2en_twse(df: pd.DataFrame, colname: list) -> pd.DataFrame:
    """
    將 TWSE 的中文欄位名稱轉換為英文欄位名稱，並包含 'stock_name' 和 'industry'。
    """
    col_map = {
        "證券代號": "stock_id",
        "證券名稱": "stock_name",
        "成交股數": "Volume",
        "成交筆數": "Transaction",
        "成交金額": "TradeValue",
        "開盤價": "Open",
        "最高價": "High",
        "最低價": "Low",
        "收盤價": "Close",
        "漲跌(+/-)": "Dir",
        "漲跌價差": "Change",
        # 如果有其他欄位，可以根據需要添加
    }
    new_columns = [col_map.get(col, "") for col in colname]
    df.columns = new_columns
    df = df.loc[:, df.columns != ""]  # 移除空白欄位

    required_cols = ["stock_id", "stock_name", "Volume", "Transaction", "TradeValue", "Open", "High", "Low", "Close", "Change"]
    missing_cols = [c for c in required_cols if c not in df.columns]
    if missing_cols:
        logger.error(f"TWSE missing required columns: {missing_cols}")
        print(f"TWSE 缺少必要的欄位：{missing_cols}")
        return pd.DataFrame()

    df['industry'] = ""
    df['listing_status'] = "上市"

    return df

# 清理 TWSE 資料
def clear_data_twse(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    df = df.fillna("")

    if "Dir" in df.columns:
        if df["Dir"].str.contains(">").any() and df["Dir"].str.contains("<").any():
            df["Dir"] = df["Dir"].str.split(">").str[1].str.split("<").str[0]
        else:
            df["Dir"] = ""
    else:
        df["Dir"] = ""

    df["Change"] = df["Dir"] + df["Change"].astype(str)
    df["Change"] = df["Change"].str.replace(" ", "", regex=False).str.replace("X", "", regex=False)

    df = df.drop(["Dir"], axis=1)

    numeric_cols = ["Volume", "Transaction", "TradeValue", "Open", "High", "Low", "Close"]
    for col in numeric_cols:
        df[col] = (
            df[col]
            .astype(str)
            .str.replace(",", "", regex=False)
            .str.replace("X", "", regex=False)
            .str.replace("+", "", regex=False)
            .replace(["----", "---", "--"], "0")
        )
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

    df["Change"] = pd.to_numeric(df["Change"], errors='coerce').fillna(0)

    return df

# Colname 轉換函數（TPEX）
def colname_zh2en_tpex(df: pd.DataFrame) -> pd.DataFrame:
    """
    將 TPEX 的中文欄位名稱轉換為英文欄位名稱，並包含 'stock_name' 和 'industry'。
    """
    col_map = {
        "代號": "stock_id",
        "名稱": "stock_name",
        "收盤": "Close",
        "漲跌": "Change",
        "開盤": "Open",
        "最高": "High",
        "最低": "Low",
        "成交股數": "Volume",
        "成交金額(元)": "TradeValue",
        "成交筆數": "Transaction"
    }

    # 移除欄位名稱中的空格
    df.columns = [col.strip() for col in df.columns]

    # 重命名欄位
    df = df.rename(columns=col_map)

    # 檢查是否缺少必要欄位
    required_cols = ["stock_id", "stock_name", "Volume", "Transaction", "TradeValue", "Open", "High", "Low", "Close", "Change"]
    missing_cols = [c for c in required_cols if c not in df.columns]
    if missing_cols:
        logger.error(f"TPEX missing required columns: {missing_cols}")
        print(f"TPEX 缺少必要的欄位：{missing_cols}")
        return pd.DataFrame()

    df['industry'] = ""
    df['listing_status'] = "上櫃"

    return df

# 清理 TPEX 資料
def clear_data_tpex(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    df = df.fillna("")

    numeric_cols = [
        "Volume", "Transaction", "TradeValue",
        "Open", "High", "Low", "Close", "Change"
    ]
    for col in numeric_cols:
        df[col] = (
            df[col].astype(str)
            .str.replace(",", "", regex=False)
            .str.replace("X", "", regex=False)
            .str.replace("+", "", regex=False)
            .str.replace("----", "0", regex=False)
            .str.replace("---", "0", regex=False)
            .str.replace("--", "0", regex=False)
            .str.replace(" ", "", regex=False)
            .str.replace("除權息", "0", regex=False)
            .str.replace("除息", "0", regex=False)
            .str.replace("除權", "0", regex=False)
        )
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    return df

# 檢查 TWSE schema
def check_twse_schema(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    try:
        df_dict = df.to_dict("records")
        df_schema = [TWSEStockPrice(**record).__dict__ for record in df_dict]
        return pd.DataFrame(df_schema)
    except Exception as e:
        logger.error(f"TWSE schema validation failed: {e}")
        print(f"TWSE schema 驗證失敗：{e}")
        return pd.DataFrame()

# 檢查 TPEX schema
def check_tpex_schema(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    try:
        df_dict = df.to_dict("records")
        df_schema = [TPEXStockPrice(**record).__dict__ for record in df_dict]
        return pd.DataFrame(df_schema)
    except Exception as e:
        logger.error(f"TPEX schema validation failed: {e}")
        print(f"TPEX schema 驗證失敗：{e}")
        return pd.DataFrame()

# 篩選符合條件的股票
def filter_stocks(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    # 條件一：股票代碼為4位數
    condition1 = df['stock_id'].str.fullmatch(r'\d{4}')

    # 條件二：股票代碼以'00'開頭，且超過4位數
    condition2 = df['stock_id'].str.match(r'^00\d{2,}$')

    # 總條件
    total_condition = condition1 | condition2

    filtered_df = df[total_condition].copy()
    logger.info(f"Filtered stocks count: {len(filtered_df)}")
    print(f"篩選後的股票數量：{len(filtered_df)}")
    return filtered_df

# 預處理函數
def preprocess_stock_data(df: pd.DataFrame, source: str, date: str, stock_list_df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    if source == 'twse':
        df = clear_data_twse(df.copy())
        df = check_twse_schema(df.copy())
    elif source == 'tpex':
        df = clear_data_tpex(df.copy())
        df = check_tpex_schema(df.copy())

    if not df.empty:
        df = filter_stocks(df)  # 應用過濾條件
        df["date"] = date  # 確保 'date' 欄位存在
        if 'date' not in df.columns:
            logger.error("'date' column was not added correctly.")
            print("'date' 欄位未正確添加。")
            raise KeyError("'date' column was not added correctly.")

        # 檢查 stock_list_df 是否包含 'stock_id'
        if 'stock_id' not in stock_list_df.columns:
            logger.error("'stock_id' 欄位不存在於 stock_list_df。")
            print("錯誤：'stock_id' 欄位不存在於股票清單資料框中。")
            raise KeyError("'stock_id' 欄位不存在於股票清單資料框中。")

        # 根據 stock_list_df 過濾並填充 'industry'
        # 確保 stock_list_df 的股票代號與抓取的資料相匹配，並獲取 'industry' 欄位
        df = df.merge(stock_list_df[['stock_id', 'industry']], how='left', on='stock_id')

        # 將 'industry' 填充到 'industry' 欄位
        df['industry'] = df['industry_y'].fillna(df['industry_x'])
        df = df.drop(['industry_x', 'industry_y'], axis=1)

        logger.info(f"根據股票清單過濾並填充 'industry' 後，剩餘 {len(df)} 支股票。")
        print(f"根據股票清單過濾並填充 'industry' 後，剩餘 {len(df)} 支股票。")
    return df

# 初始化資料庫
def initialize_database(db_path: str):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建 stock_daily_data 表格，轉義 Transaction
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                stock_name TEXT,
                listing_status TEXT,
                industry TEXT,
                Volume INTEGER,
                [Transaction] INTEGER,
                TradeValue INTEGER,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Change REAL,
                date TEXT
            );
        """)

        # 確認 'industry' 欄位存在於 'stock_daily_data' 表格中
        cursor.execute("PRAGMA table_info(stock_daily_data);")
        columns = [info[1] for info in cursor.fetchall()]
        if 'industry' not in columns:
            logger.warning("表格 'stock_daily_data' 缺少 'industry' 欄位，正在新增該欄位。")
            print("表格 'stock_daily_data' 缺少 'industry' 欄位，正在新增該欄位。")
            try:
                cursor.execute("ALTER TABLE stock_daily_data ADD COLUMN industry TEXT DEFAULT 'ETF';")
                logger.info("成功新增 'industry' 欄位到 'stock_daily_data' 表格。")
                print("成功新增 'industry' 欄位到 'stock_daily_data' 表格。")
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    logger.info("'industry' 欄位已存在，無需新增。")
                    print("'industry' 欄位已存在，無需新增。")
                else:
                    logger.error(f"新增 'industry' 欄位時出錯：{e}")
                    print(f"新增 'industry' 欄位時出錯：{e}")
                    conn.close()
                    raise

        conn.commit()
        conn.close()
        logger.info(f"資料庫初始化完成，表格 'stock_daily_data' 已建立或已存在。")
        print(f"資料庫初始化完成，表格 'stock_daily_data' 已建立或已存在。")
    except Exception as e:
        logger.error(f"初始化資料庫時發生錯誤：{e}")
        print(f"初始化資料庫時發生錯誤：{e}")
        raise

# TWSE 爬蟲函數
def crawler_twse(date: str, retry_count: int = 3) -> pd.DataFrame:
    logger.info(f"開始抓取 TWSE 資料，日期: {date}")
    print(f"開始抓取 TWSE 資料，日期: {date}")
    url = "https://www.twse.com.tw/exchangeReport/MI_INDEX"
    params = {
        "response": "json",
        "date": date.replace("-", ""),  # e.g., 2024-12-11 -> 20241211
        "type": "ALL"
    }

    for attempt in range(retry_count):
        try:
            random_sleep(5, 10)
            res = requests.get(url, params=params, headers=twse_header(), timeout=30)

            if res.status_code == 429:
                logger.warning(f"TWSE 回應 429 Too Many Requests。等待 60 秒後重試。")
                print("TWSE 回應 429 Too Many Requests。等待 60 秒後重試。")
                time.sleep(60)
                continue

            res.raise_for_status()

            response_json = res.json()
            logger.debug(f"TWSE 回應 JSON（日期: {date}）：{response_json}")

            if response_json.get("stat") == "很抱歉，沒有符合條件的資料!":
                logger.warning(f"{date} TWSE 沒有交易資料。")
                print(f"{date} TWSE 沒有交易資料。")
                return pd.DataFrame()

            df = pd.DataFrame()
            colname = []

            # 處理不同的回應結構
            if "tables" in response_json:
                for table in response_json["tables"]:
                    title = table.get("title", "")
                    if "每日收盤行情" in title or "報價行情" in title:
                        df = pd.DataFrame(table.get("data", []))
                        colname = table.get("fields", [])
                        break
                else:
                    logger.warning(f"{date} TWSE 回應中未找到相關表格。")
                    print(f"{date} TWSE 回應中未找到相關表格。")
                    return pd.DataFrame()
            elif "data9" in response_json and "fields9" in response_json:
                df = pd.DataFrame(response_json["data9"])
                colname = response_json["fields9"]
            elif "data8" in response_json and "fields8" in response_json:
                df = pd.DataFrame(response_json["data8"])
                colname = response_json["fields8"]
            else:
                logger.warning(f"{date} TWSE 回應格式無法識別：{response_json.get('stat')}")
                print(f"{date} TWSE 回應格式無法識別：{response_json.get('stat')}")
                return pd.DataFrame()

            if not df.empty:
                df = colname_zh2en_twse(df.copy(), colname)
                if df.empty:
                    logger.warning(f"{date} TWSE 資料經欄位轉換後為空。")
                    print(f"{date} TWSE 資料經欄位轉換後為空。")
                    return pd.DataFrame()
                df["date"] = date
                logger.info(f"{date} TWSE 資料抓取成功，共 {len(df)} 筆資料。")
                print(f"{date} TWSE 資料抓取成功，共 {len(df)} 筆資料。")
                return df
        except requests.exceptions.Timeout:
            logger.error(f"{date} TWSE 請求超時（Attempt {attempt + 1}/{retry_count}）。")
            print(f"{date} TWSE 請求超時（Attempt {attempt + 1}/{retry_count}）。")
        except requests.exceptions.RequestException as e:
            logger.error(f"{date} TWSE 請求失敗（Attempt {attempt + 1}/{retry_count}）：{e}")
            print(f"{date} TWSE 請求失敗（Attempt {attempt + 1}/{retry_count}）：{e}")
        except Exception as e:
            logger.error(f"{date} TWSE 處理錯誤（Attempt {attempt + 1}/{retry_count}）：{e}")
            print(f"{date} TWSE 處理錯誤（Attempt {attempt + 1}/{retry_count}）：{e}")

        # 等待後重試
        logger.info(f"{date} TWSE 第 {attempt + 1} 次重試。")
        print(f"{date} TWSE 第 {attempt + 1} 次重試。")
        time.sleep(10)

    logger.error(f"{date} TWSE 抓取失敗，已達最大重試次數。")
    print(f"{date} TWSE 抓取失敗，已達最大重試次數。")
    return pd.DataFrame()

# TPEX 爬蟲函數
def crawler_tpex_custom(date: str, retry_count: int = 3) -> pd.DataFrame:
    logger.info(f"開始抓取 TPEX 資料，日期: {date}")
    print(f"開始抓取 TPEX 資料，日期: {date}")
    roc_date = convert_date(date)
    if not roc_date:
        logger.error(f"Failed to convert date for TPEX: {date}")
        print(f"日期轉換失敗，無法抓取 TPEX 資料。")
        return pd.DataFrame()

    url = (
        "https://www.tpex.org.tw/web/stock/aftertrading/"
        "otc_quotes_no1430/stk_wn1430_result.php?"
        f"l=zh-tw&d={roc_date}&se=AL"
    )

    for attempt in range(retry_count):
        try:
            random_sleep(5, 10)
            res = requests.get(url, headers=tpex_header_custom(), timeout=30, allow_redirects=False)

            if res.status_code == 302:
                logger.warning(f"TPEX 回應 302 redirect（可能無該日期資料），日期: {date}")
                print(f"TPEX 回應 302 redirect（可能無該日期資料），日期: {date}")
                return pd.DataFrame()

            if res.status_code == 429:
                logger.warning(f"TPEX 回應 429 Too Many Requests。等待 60 秒後重試。")
                print("TPEX 回應 429 Too Many Requests。等待 60 秒後重試。")
                time.sleep(60)
                continue

            res.raise_for_status()

            response_json = res.json()
            logger.debug(f"TPEX 回應 JSON（日期: {date}）：{response_json}")

            tables = response_json.get("tables", [])
            if not tables:
                logger.warning(f"{date} TPEX 回應中未找到任何表格。")
                print(f"{date} TPEX 回應中未找到任何表格。")
                return pd.DataFrame()

            table_data = None
            for t in tables:
                if "上櫃股票每日收盤行情" in t.get("title", ""):
                    table_data = t
                    break

            if table_data is None:
                logger.warning(f"{date} TPEX 回應中未找到 '上櫃股票每日收盤行情' 表格。")
                print(f"{date} TPEX 回應中未找到 '上櫃股票每日收盤行情' 表格。")
                return pd.DataFrame()

            data = table_data.get("data", [])
            fields = table_data.get("fields", [])

            if not data or not fields:
                logger.warning(f"{date} TPEX 資料或欄位為空。")
                print(f"{date} TPEX 資料或欄位為空。")
                return pd.DataFrame()

            df = pd.DataFrame(data, columns=fields)
            logger.debug(f"TPEX 原始欄位（日期: {date}）：{df.columns.tolist()}")
            logger.debug(f"TPEX 資料前5筆（日期: {date}）：\n{df.head()}")

            df = colname_zh2en_tpex(df.copy())
            if df.empty:
                logger.warning(f"{date} TPEX 資料經欄位轉換後為空。")
                print(f"{date} TPEX 資料經欄位轉換後為空。")
                return pd.DataFrame()

            df["date"] = date
            logger.info(f"{date} TPEX 資料抓取成功，共 {len(df)} 筆資料。")
            print(f"{date} TPEX 資料抓取成功，共 {len(df)} 筆資料。")
            return df

        except requests.exceptions.Timeout:
            logger.error(f"{date} TPEX 請求超時（Attempt {attempt + 1}/{retry_count}）。")
            print(f"{date} TPEX 請求超時（Attempt {attempt + 1}/{retry_count}）。")
        except requests.exceptions.RequestException as e:
            logger.error(f"{date} TPEX 請求失敗（Attempt {attempt + 1}/{retry_count}）：{e}")
            print(f"{date} TPEX 請求失敗（Attempt {attempt + 1}/{retry_count}）：{e}")
        except Exception as e:
            logger.error(f"{date} TPEX 處理錯誤（Attempt {attempt + 1}/{retry_count}）：{e}")
            print(f"{date} TPEX 處理錯誤（Attempt {attempt + 1}/{retry_count}）：{e}")

        # 等待後重試
        logger.info(f"{date} TPEX 第 {attempt + 1} 次重試。")
        print(f"{date} TPEX 第 {attempt + 1} 次重試。")
        time.sleep(10)

    logger.error(f"{date} TPEX 抓取失敗，已達最大重試次數。")
    print(f"{date} TPEX 抓取失敗，已達最大重試次數。")
    return pd.DataFrame()

# Crawl price data
def crawl_price(date: str, conn: sqlite3.Connection, stock_list_df: pd.DataFrame) -> pd.DataFrame:
    print(f"開始抓取日期 {date} 的股票價格資料...")
    logger.info(f"開始抓取日期 {date} 的股票價格資料。")
    
    df_twse = crawler_twse(date)
    df_twse = preprocess_stock_data(df_twse, 'twse', date, stock_list_df)

    df_tpex = crawler_tpex_custom(date)
    df_tpex = preprocess_stock_data(df_tpex, 'tpex', date, stock_list_df)

    combined_df = pd.concat([df_twse, df_tpex], sort=False)

    # 插入資料到英文欄位表格
    if not combined_df.empty:
        try:
            combined_df.to_sql('stock_daily_data', conn, if_exists='append', index=False)
            logger.info(f"Inserted {len(combined_df)} records for date {date} into 'stock_daily_data'.")
            print(f"已將 {len(combined_df)} 筆資料插入到 'stock_daily_data' 表格。")
        except Exception as e:
            logger.error(f"Failed to insert records for date {date} into 'stock_daily_data': {e}")
            print(f"插入 'stock_daily_data' 表格時發生錯誤：{e}")

    print(f"完成抓取日期 {date} 的股票價格資料。\n")
    logger.info(f"完成抓取日期 {date} 的股票價格資料。")

    return combined_df

# ================================
# 程式C: 爬蟲主流程
# ================================

# 載入假期資料
def load_holidays(csv_path: str) -> list:
    try:
        # 先讀取 CSV，不立即解析日期
        df = pd.read_csv(csv_path)

        # 檢查是否存在 'date' 欄位
        if 'date' not in df.columns:
            raise ValueError("CSV 檔案中缺少 'date' 欄位。")

        # 將 'date' 欄位轉換為 datetime 類型，錯誤則轉為 NaT
        df['date'] = pd.to_datetime(df['date'], format='%Y-%m-%d', errors='coerce')

        # 移除無法解析的日期
        holidays_list = df['date'].dropna().dt.strftime('%Y-%m-%d').tolist()

        logger.info(f"Successfully loaded {len(holidays_list)} holidays from CSV.")
        print(f"成功載入 {len(holidays_list)} 個假期日期。")
        return holidays_list
    except Exception as e:
        logger.error(f"Error loading holidays from CSV: {e}")
        print(f"載入假期資料時發生錯誤：{e}")
        return []

# 使用 holidays 套件獲取台灣的公共假期
def get_taiwan_public_holidays(years=None) -> list:
    try:
        if years:
            taiwan_holidays = holidays.TW(years=years)
        else:
            taiwan_holidays = holidays.TW()
        holidays_list = [date.strftime('%Y-%m-%d') for date in taiwan_holidays]
        logger.info(f"Fetched {len(holidays_list)} public holidays using holidays package.")
        print(f"使用 holidays 套件獲取了 {len(holidays_list)} 個公共假期。")
        return holidays_list
    except AttributeError:
        logger.warning("holidays package may not support Taiwan. Unable to fetch public holidays automatically.")
        print("holidays 套件可能不支援台灣，無法自動獲取公共假期。")
        return []

# 更新資料庫表格
def update_table(table_name: str, crawl_function, dates: list, conn: sqlite3.Connection, stock_list_df: pd.DataFrame, max_attempts: int = 7):
    if table_name == 'stock_daily_data':
        with tqdm(total=len(dates), desc="Crawling TWSE & TPEX") as progress:
            for d in dates:
                progress.set_description(f"Crawling TWSE & TPEX for {d}")
                logger.info(f"Starting crawl for date: {d}")
                print(f"正在抓取日期 {d} 的資料...")

                try:
                    crawl_function(d, conn, stock_list_df)
                    logger.info(f"Data for {d} crawled and inserted into database.")
                    print(f"已成功抓取並插入日期 {d} 的資料。")
                except Exception as e:
                    logger.error(f"Failed to crawl data for {d}: {e}")
                    print(f"抓取日期 {d} 的資料時發生錯誤：{e}")
                    # 計算下一個交易日
                    try:
                        next_date_dt = datetime.strptime(d, "%Y-%m-%d").date() + timedelta(days=1)
                        next_date = next_date_dt.strftime("%Y-%m-%d")
                        if next_date not in dates:
                            dates.append(next_date)
                            progress.total += 1
                            progress.refresh()
                            logger.info(f"Added next trading day {next_date} to crawl queue due to error.")
                            print(f"已將下一個交易日 {next_date} 加入爬取隊列。")
                            max_attempts -= 1
                            if max_attempts <= 0:
                                logger.warning(f"Reached maximum attempts ({max_attempts}) for crawling dates.")
                                print(f"已達到最大重試次數 ({max_attempts})，停止爬取。")
                                break
                    except Exception as convert_e:
                        logger.error(f"Failed to calculate next trading day after error for {d}: {convert_e}")
                        print(f"計算下一個交易日時發生錯誤：{convert_e}")

                progress.update(1)

                # 每10個請求後增加額外延遲
                if progress.n % 10 == 0 and progress.n != 0:
                    extra_delay = random.uniform(60, 120)
                    logger.info(f"Completed {progress.n} requests. Sleeping for {extra_delay:.2f} seconds.")
                    print(f"已完成 {progress.n} 次請求。正在等待 {extra_delay:.2f} 秒以避免被封鎖...")
                    time.sleep(extra_delay)
        if max_attempts <= 0:
            logger.warning(f"Reached maximum attempts ({max_attempts}) for crawling dates.")
            print(f"已達到最大重試次數 ({max_attempts})，停止爬取。")
    else:
        logger.warning(f"Unknown table name: {table_name}")
        print(f"未知的表格名稱：{table_name}")

# 刪除中文資料表的函數
def delete_chinese_table(db_path: str):
    """
    刪除 SQLite 資料庫中的 '股票每日資料' 表格。
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("DROP TABLE IF EXISTS 股票每日資料;")
        conn.commit()
        conn.close()
        logger.info("成功刪除 '股票每日資料' 表格。")
        print("成功刪除 '股票每日資料' 表格。")
    except Exception as e:
        logger.error(f"刪除 '股票每日資料' 表格時出錯：{e}")
        print(f"刪除 '股票每日資料' 表格時出錯：{e}")

# 爬蟲主函數
def run_crawl(stock_list_df: pd.DataFrame, db_path: str, holidays_csv_path: str):
    """
    根據股票清單抓取價格資料並儲存到資料庫。

    參數:
        stock_list_df (pd.DataFrame): 包含股票清單的資料框。
        db_path (str): 資料庫檔案路徑。
        holidays_csv_path (str): 假期 CSV 文件路徑。
    """
    logger.info("=== 爬蟲開始執行 ===")
    print("=== 爬蟲開始執行 ===")

    # 檢查資料庫是否存在
    db_exists = os.path.exists(db_path)
    if db_exists:
        print(f"資料庫 {db_path} 已存在。")
        logger.info(f"資料庫 {db_path} 已存在。")
    else:
        print(f"資料庫 {db_path} 不存在，將建立新資料庫。")
        logger.info(f"資料庫 {db_path} 不存在，將建立新資料庫。")

    # 載入假期資料
    taiwan_public_holidays = get_taiwan_public_holidays(years=[datetime.now().year])
    holidays_list = load_holidays(holidays_csv_path)  # 從 CSV 載入
    if taiwan_public_holidays:
        holidays_set = set(taiwan_public_holidays)
    else:
        holidays_set = set()

    if holidays_list:
        holidays_set.update(holidays_list)
        logger.info(f"已載入 {len(holidays_set)} 個假期日期。")
        print(f"已載入 {len(holidays_set)} 個假期日期。")
    else:
        logger.warning("假期資料載入失敗或為空，將僅過濾週末。")
        print("假期資料載入失敗或為空，將僅過濾週末。")

    # 初始化資料庫，確保表格存在
    initialize_database(db_path)

    # 刪除中文資料表（如果存在）
    delete_chinese_table(db_path)

    # 連接到 price.db
    try:
        conn = sqlite3.connect(db_path)
        logger.info(f"成功連接到資料庫 {db_path}")
        print(f"成功連接到資料庫 {db_path}")

        # 顯示資料庫資訊
        cursor = conn.cursor()

        # 獲取表格名稱
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        print(f"資料庫中有 {len(table_names)} 張表格：{', '.join(table_names)}")
        logger.info(f"資料庫中有 {len(table_names)} 張表格：{', '.join(table_names)}")

        # 獲取資料存儲的起始日期和結束日期
        if 'stock_daily_data' in table_names:
            cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data;")
            min_date, max_date = cursor.fetchone()
            print(f"stock_daily_data 表格中存儲的資料日期範圍：{min_date} 至 {max_date}")
            logger.info(f"stock_daily_data 表格中存儲的資料日期範圍：{min_date} 至 {max_date}")
        else:
            print("stock_daily_data 表格不存在於資料庫中。")
            logger.info("stock_daily_data 表格不存在於資料庫中。")

    except Exception as e:
        logger.error(f"無法連接到資料庫 {db_path}：{e}")
        print(f"無法連接到資料庫 {db_path}。")
        sys.exit(1)

    # 定義自動下載的起始日期和結束日期
    start_date_str = "2024-11-01"
    end_date_dt = datetime.now().date()

    # 找出最近的交易日作為結束日期
    while True:
        formatted_end_date = end_date_dt.strftime("%Y-%m-%d")
        weekday = end_date_dt.weekday()
        if weekday >= 5 or formatted_end_date in holidays_set:
            # 如果是週末或假期，往前一天
            end_date_dt -= timedelta(days=1)
        else:
            break
    end_date_str = end_date_dt.strftime("%Y-%m-%d")

    logger.info(f"自動設定的日期範圍：從 {start_date_str} 到 {end_date_str}")
    print(f"自動設定的日期範圍：從 {start_date_str} 到 {end_date_str}\n")

    # 生成日期列表
    dates = generate_date_range(start_date_str, end_date_str)
    logger.info(f"生成的日期列表：{dates}")
    print(f"生成的日期列表，共 {len(dates)} 天。")

    # 過濾掉假期和週末
    filtered_dates = []
    for d in dates:
        if d in holidays_set:
            logger.info(f"日期 {d} 為假期，已過濾。")
            print(f"日期 {d} 為假期，已過濾。")
            continue
        try:
            weekday = datetime.strptime(d, "%Y-%m-%d").weekday()
        except ValueError as e:
            logger.error(f"日期格式錯誤 {d}: {e}")
            print(f"日期格式錯誤 {d}: {e}")
            continue
        if weekday >= 5:  # 5 = Saturday, 6 = Sunday
            logger.info(f"日期 {d} 為週末，已過濾。")
            print(f"日期 {d} 為週末，已過濾。")
            continue
        filtered_dates.append(d)
        logger.debug(f"日期 {d} 是工作日，將進行抓取。")
        print(f"日期 {d} 是工作日，將進行抓取。")

    dates = filtered_dates
    logger.info(f"過濾假期和週末後的日期列表：{dates}")
    print(f"過濾假期和週末後的日期列表，共 {len(dates)} 天。\n")

    if not dates:
        logger.info("沒有需要更新的日期範圍。")
        print("沒有需要更新的日期範圍。")
        conn.close()
        return

    # 檢查重疊的日期
    try:
        query = f"""
            SELECT DISTINCT date FROM stock_daily_data
            WHERE date BETWEEN '{start_date_str}' AND '{end_date_str}'
        """
        df_overlap = pd.read_sql_query(query, conn)
        overlapping_dates = df_overlap['date'].tolist()
        logger.info(f"重疊的日期：{overlapping_dates}")
    except Exception as e:
        logger.error(f"檢查重疊日期時發生錯誤：{e}")
        overlapping_dates = []

    if overlapping_dates:
        print("\n發現與 price.db 中現有資料有重覆的日期，將跳過這些日期：")
        print(", ".join(overlapping_dates))
        logger.info(f"重覆的日期：{overlapping_dates}")

        # 移除重覆的日期，僅抓取不重覆的日期
        dates = [d for d in dates if d not in overlapping_dates]
        print("已跳過重覆的日期。\n")
        logger.info("已跳過重覆的日期。")

    if not dates:
        logger.info("沒有需要更新的日期範圍。")
        print("沒有需要更新的日期範圍。")
        conn.close()
        return

    logger.info(f"即將抓取 {len(dates)} 天的資料，從 {start_date_str} 到 {end_date_str}。\n")
    print(f"即將抓取 {len(dates)} 天的資料，從 {start_date_str} 到 {end_date_str}。\n")

    # 更新資料庫
    update_table('stock_daily_data', crawl_price, dates, conn, stock_list_df)

    # 顯示資料庫更新後的資訊
    try:
        cursor = conn.cursor()

        # 獲取表格名稱
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        print(f"更新後，資料庫中有 {len(table_names)} 張表格：{', '.join(table_names)}")
        logger.info(f"更新後，資料庫中有 {len(table_names)} 張表格：{', '.join(table_names)}")

        # 獲取資料存儲的起始日期和結束日期
        if 'stock_daily_data' in table_names:
            cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data;")
            min_date, max_date = cursor.fetchone()
            print(f"stock_daily_data 表格中存儲的資料日期範圍：{min_date} 至 {max_date}")
            logger.info(f"stock_daily_data 表格中存儲的資料日期範圍：{min_date} 至 {max_date}")
        else:
            print("stock_daily_data 表格不存在於資料庫中。")
            logger.info("stock_daily_data 表格不存在於資料庫中。")

    except Exception as e:
        logger.error(f"更新後檢查資料庫資訊時發生錯誤：{e}")
        print(f"更新後檢查資料庫資訊時發生錯誤：{e}")

    # 關閉資料庫連接
    conn.close()

    # 在此處新增一行日誌，顯示 price.db 的存放位置
    logger.info(f"price.db 已更新至 {db_path}")
    print(f"price.db 已更新至 {db_path}")

    # 最後僅顯示一次「資料抓取完成。」
    logger.info("\n資料抓取完成。")
    print("\n資料抓取完成。")

# ================================
# 主函數
# ================================

def main():
    """
    主函數：整合程式A、B、C的功能。
    """
    # 程式A：抓取並處理股票清單
    stock_list_df = process_stock_list()

    if stock_list_df.empty:
        logger.error("股票清單為空，無法進行後續的價格抓取。")
        print("股票清單為空，無法進行後續的價格抓取。")
        return

    # 程式C：根據股票清單抓取價格資料並儲存到資料庫
    db_path = r"D:\Finlab\history\tables\price.db"  # 資料庫路徑，已更改為 price.db
    holidays_csv_path = r"D:/Finlab/history/holidays/taiwan_holidays.csv"  # 假期 CSV 文件路徑

    run_crawl(stock_list_df, db_path, holidays_csv_path)

    print("整合流程已完成。")
    logger.info("整合流程已完成。")

if __name__ == "__main__":
    main()
