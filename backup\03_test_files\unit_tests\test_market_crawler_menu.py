#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試市場數據爬蟲選單功能
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_market_crawler_dialog():
    """測試市場數據爬蟲對話框"""
    print("測試市場數據爬蟲對話框")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 導入對話框類
        from O3mh_gui_v21_optimized import MarketDataCrawlerDialog
        
        print("✅ 成功導入 MarketDataCrawlerDialog")
        
        # 創建對話框
        dialog = MarketDataCrawlerDialog()
        print("✅ 成功創建對話框")
        
        # 顯示對話框
        dialog.show()
        print("✅ 對話框已顯示")
        
        # 設置自動關閉
        def auto_close():
            print("⏰ 自動關閉對話框")
            dialog.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(auto_close)
        timer.start(3000)  # 3秒後自動關閉
        
        # 運行應用程式
        app.exec()
        
        print("✅ 對話框測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 對話框測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_safe_scanner():
    """測試安全掃描器"""
    print("\n測試安全掃描器")
    print("=" * 50)
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        print("✅ 成功創建安全掃描器")
        
        # 執行掃描
        print("🚀 執行掃描...")
        results = scanner.run_full_scan()
        
        if results:
            print("✅ 掃描成功")
            
            # 統計結果
            total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
            print(f"📊 總計 {total_items} 項數據")
            
            # 顯示各類別
            categories = [
                ('us_indices', '美股指數'),
                ('asia_indices', '亞洲指數'),
                ('taiwan_futures', '台股數據'),
                ('commodities', '商品價格'),
                ('fx_rates', '外匯匯率'),
                ('crypto', '加密貨幣')
            ]
            
            for key, display_name in categories:
                if key in results and results[key]:
                    count = len(results[key])
                    print(f"   • {display_name}: {count} 項")
                    
                    # 顯示前2項數據
                    for i, (name, data) in enumerate(results[key].items()):
                        if i >= 2:
                            break
                        if isinstance(data, dict):
                            price = data.get('price', data.get('rate', 0))
                            change_pct = data.get('change_pct', 0)
                            print(f"     - {name}: {price} ({change_pct:+.2f}%)")
            
            return True
        else:
            print("❌ 掃描失敗")
            return False
            
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_integration():
    """測試選單整合"""
    print("\n測試選單整合")
    print("=" * 50)
    
    try:
        # 檢查主程式中是否有選單項目
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        print("✅ 成功導入主程式")
        
        # 檢查是否有市場爬蟲方法
        if hasattr(StockScreenerGUI, 'open_market_data_crawler'):
            print("✅ 找到 open_market_data_crawler 方法")
        else:
            print("❌ 未找到 open_market_data_crawler 方法")
            return False
        
        # 檢查是否有對話框類
        from O3mh_gui_v21_optimized import MarketDataCrawlerDialog
        print("✅ 找到 MarketDataCrawlerDialog 類")
        
        return True
        
    except Exception as e:
        print(f"❌ 選單整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("市場數據爬蟲選單功能測試")
    print("=" * 60)
    print("測試內容:")
    print("  1. 安全掃描器功能")
    print("  2. 選單整合檢查")
    print("  3. 對話框界面測試")
    print()
    
    # 1. 測試安全掃描器
    scanner_ok = test_safe_scanner()
    
    # 2. 測試選單整合
    menu_ok = test_menu_integration()
    
    # 3. 測試對話框（如果前面都成功）
    dialog_ok = False
    if scanner_ok and menu_ok:
        print("\n前置測試通過，開始對話框測試...")
        dialog_ok = test_market_crawler_dialog()
    else:
        print("\n前置測試失敗，跳過對話框測試")
    
    # 總結
    print(f"\n🎯 測試結果總結:")
    print(f"   安全掃描器: {'✅ 通過' if scanner_ok else '❌ 失敗'}")
    print(f"   選單整合: {'✅ 通過' if menu_ok else '❌ 失敗'}")
    print(f"   對話框界面: {'✅ 通過' if dialog_ok else '❌ 失敗'}")
    
    if scanner_ok and menu_ok and dialog_ok:
        print("\n🎉 市場數據爬蟲選單功能完全正常！")
        print("   使用方法:")
        print("   1. 啟動主程式")
        print("   2. 點擊選單 🕷️ 爬蟲 → 🌍 全球市場數據爬蟲")
        print("   3. 在對話框中點擊「🚀 開始爬取」")
        print("   4. 查看爬取的全球市場數據")
    elif scanner_ok and menu_ok:
        print("\n⚠️ 核心功能正常，對話框可能需要調整")
    else:
        print("\n❌ 部分功能需要修復")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 測試結束")
