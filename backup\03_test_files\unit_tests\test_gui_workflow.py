#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試GUI工作流程
"""

import sys
import traceback
from datetime import datetime

def simulate_analysis_workflow():
    """模擬分析工作流程"""
    print("🔄 模擬分析工作流程...")
    
    try:
        # 步驟1: 初始化分析器
        print("1. 初始化分析器...")
        from trading_rule_miner import TradingRuleMiner
        
        rule_miner = TradingRuleMiner()
        rule_miner.min_success_rate = 0.65
        rule_miner.min_avg_return = 0.02
        print("   ✅ TradingRuleMiner 初始化成功")
        
        # 步驟2: 執行分析
        print("2. 執行分析...")
        stock_limit = 500  # 使用預設值
        combinations = rule_miner.run_mass_analysis(stock_limit)
        print(f"   ✅ 分析完成，找到 {len(combinations)} 個組合")
        
        # 步驟3: 提取規則
        print("3. 提取交易規則...")
        rules = rule_miner.extract_trading_rules(combinations)
        print(f"   ✅ 規則提取完成，找到 {len(rules)} 條規則")
        
        # 步驟4: 生成口訣
        print("4. 生成交易口訣...")
        from trading_mantra_generator import TradingMantraGenerator
        
        mantra_generator = TradingMantraGenerator()
        mantras = mantra_generator.generate_all_mantras(rules)
        print(f"   ✅ 口訣生成完成")
        print(f"      買入口訣: {len(mantras['buy_mantras'])}")
        print(f"      賣出口訣: {len(mantras['sell_mantras'])}")
        print(f"      風險口訣: {len(mantras['risk_mantras'])}")
        
        # 步驟5: 生成策略組合口訣
        print("5. 生成策略組合口訣...")
        from strategy_combination_generator import StrategyCombinationGenerator
        
        generator = StrategyCombinationGenerator()
        strategy_mantras = generator.generate_all_combinations()
        print(f"   ✅ 策略組合口訣生成完成")
        print(f"      買入策略: {len(strategy_mantras['buy'])}")
        print(f"      賣出策略: {len(strategy_mantras['sell'])}")
        
        # 步驟6: 股票信號掃描
        print("6. 股票信號掃描...")
        from stock_signal_scanner import StockSignalScanner
        
        scanner = StockSignalScanner()
        
        # 模擬掃描幾檔股票
        test_stocks = ["2330", "1101", "2454"]
        all_signals = []
        
        for stock_id in test_stocks:
            try:
                df = scanner.get_latest_stock_data(stock_id, days=50)
                if df.empty:
                    continue
                
                df = scanner.calculate_technical_indicators(df)
                if df.empty:
                    continue
                
                # 檢查各種信號
                has_rsi_macd, _ = scanner.check_rsi_macd_signal(df)
                has_macd_bb, _ = scanner.check_macd_bb_signal(df)
                
                if has_rsi_macd or has_macd_bb:
                    all_signals.append(f"{stock_id}: 有信號")
                    
            except Exception as e:
                print(f"      股票 {stock_id} 掃描失敗: {e}")
        
        print(f"   ✅ 股票信號掃描完成，找到 {len(all_signals)} 個信號")
        for signal in all_signals:
            print(f"      {signal}")
        
        # 步驟7: 股票篩選
        print("7. 股票篩選...")
        
        # 測試一個策略口訣
        test_mantra = {
            'mantra_text': '【MACD+BB】MACD金叉配布林下軌反彈，雙重確認買',
            'category': 'buy',
            'success_rate': 0.69,
            'avg_profit': 0.062
        }
        
        signals = scanner.scan_stocks_by_mantra(test_mantra)
        print(f"   ✅ 股票篩選完成，找到 {len(signals)} 個符合條件的股票")
        
        if signals:
            print("      符合條件的股票:")
            for i, signal in enumerate(signals[:5]):
                print(f"        {i+1}. {signal.stock_id} {signal.stock_name} - 價格:{signal.current_price:.2f}")
        
        print("\n🎉 完整工作流程測試成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 工作流程測試失敗: {e}")
        traceback.print_exc()
        return False

def test_gui_components():
    """測試GUI組件"""
    print("\n🖥️ 測試GUI組件...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QThread, pyqtSignal
        
        # 測試QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   ✅ QApplication 創建成功")
        
        # 測試信號槽
        class TestWorker(QThread):
            test_signal = pyqtSignal(str)
            
            def run(self):
                self.test_signal.emit("測試信號")
        
        def handle_signal(message):
            print(f"   ✅ 信號接收成功: {message}")
        
        worker = TestWorker()
        worker.test_signal.connect(handle_signal)
        worker.start()
        worker.wait()  # 等待線程完成
        
        print("   ✅ 線程和信號槽測試成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GUI組件測試失敗: {e}")
        traceback.print_exc()
        return False

def test_memory_usage():
    """測試記憶體使用"""
    print("\n💾 測試記憶體使用...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        print(f"   記憶體使用: {memory_info.rss / 1024 / 1024:.1f} MB")
        print(f"   虛擬記憶體: {memory_info.vms / 1024 / 1024:.1f} MB")
        
        # 執行一些操作後再檢查
        simulate_analysis_workflow()
        
        memory_info_after = process.memory_info()
        print(f"   操作後記憶體: {memory_info_after.rss / 1024 / 1024:.1f} MB")
        
        memory_increase = (memory_info_after.rss - memory_info.rss) / 1024 / 1024
        print(f"   記憶體增加: {memory_increase:.1f} MB")
        
        if memory_increase > 500:  # 如果增加超過500MB
            print("   ⚠️ 記憶體使用量較大，可能有記憶體洩漏")
        else:
            print("   ✅ 記憶體使用正常")
        
        return True
        
    except ImportError:
        print("   ⚠️ psutil 未安裝，跳過記憶體測試")
        return True
    except Exception as e:
        print(f"   ❌ 記憶體測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 GUI工作流程測試")
    print("="*50)
    
    tests = [
        ("完整工作流程", simulate_analysis_workflow),
        ("GUI組件", test_gui_components),
        ("記憶體使用", test_memory_usage),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # 總結
    print(f"\n" + "="*50)
    print("🎯 測試結果總結:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！")
        print("💡 如果GUI仍然閃退，可能的原因:")
        print("   1. PyQt6版本兼容性問題")
        print("   2. 線程同步問題")
        print("   3. UI更新時機問題")
        print("   4. 異常處理不完整")
    else:
        print("⚠️ 發現問題，需要進一步調試")

if __name__ == "__main__":
    main()
