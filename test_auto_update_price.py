#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的 auto_update.py 價格更新功能
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

def test_auto_update_price():
    """測試 auto_update.py 的價格更新功能"""
    
    print("=" * 60)
    print("🧪 測試 auto_update.py 價格更新功能")
    print("=" * 60)
    
    # 檢查當前 newprice.db 狀態
    db_file = r'D:\Finlab\history\tables\newprice.db'
    
    if not os.path.exists(db_file):
        print(f"❌ newprice.db 不存在: {db_file}")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查最新日期
        query = 'SELECT MAX(date) as latest_date, COUNT(*) as total_records FROM stock_daily_data'
        result = pd.read_sql_query(query, conn)
        latest_date = result.iloc[0]['latest_date']
        total_records = result.iloc[0]['total_records']
        
        print(f"📊 當前 newprice.db 狀態:")
        print(f"   最新日期: {latest_date}")
        print(f"   總記錄數: {total_records:,}")
        
        # 檢查 0050 最新資料
        query_0050 = '''
            SELECT date, [Close], Volume, stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 5
        '''
        
        df_0050 = pd.read_sql_query(query_0050, conn)
        
        if not df_0050.empty:
            print(f"\n📈 0050 最新5筆資料:")
            for _, row in df_0050.iterrows():
                close_val = row['Close'] if pd.notna(row['Close']) else 'NULL'
                volume_val = row['Volume'] if pd.notna(row['Volume']) else 'NULL'
                print(f"   {row['date']}: 收盤={close_val}, 成交量={volume_val}")
        
        conn.close()
        
        # 測試 get_newprice_incremental_date_range 函數
        print(f"\n🔍 測試增量日期範圍函數:")
        
        # 導入 auto_update 模組
        try:
            from auto_update import get_newprice_incremental_date_range
            
            date_range = get_newprice_incremental_date_range()
            
            if date_range is None:
                print(f"❌ 獲取日期範圍失敗")
                return False
            elif len(date_range) == 0:
                print(f"✅ 資料已是最新，無需更新")
                return True
            else:
                print(f"📅 需要更新的日期:")
                for i, date in enumerate(date_range[:10], 1):  # 只顯示前10個
                    print(f"   {i}. {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
                
                if len(date_range) > 10:
                    print(f"   ... 還有 {len(date_range) - 10} 個日期")
                
                print(f"\n📊 總共需要更新: {len(date_range)} 個工作日")
                return True
        
        except ImportError as e:
            print(f"❌ 導入 auto_update 失敗: {e}")
            return False
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 檢查資料庫失敗: {e}")
        return False

def show_usage():
    """顯示使用說明"""
    
    print("\n" + "=" * 60)
    print("📋 auto_update.py 使用說明")
    print("=" * 60)
    
    print("🚀 執行完整價格更新:")
    print("   python auto_update.py")
    print("   或")
    print("   python auto_update.py price")
    
    print("\n📊 功能說明:")
    print("   - 自動檢測 newprice.db 的最新日期")
    print("   - 從最新日期的下一個工作日開始更新")
    print("   - 更新到今天為止的所有工作日")
    print("   - 自動過濾週末和非交易日")
    print("   - 即時保存每日資料到 newprice.db")
    
    print("\n📂 資料庫位置:")
    print("   D:\\Finlab\\history\\tables\\newprice.db")
    
    print("\n⚠️ 注意事項:")
    print("   - 確保網路連線正常")
    print("   - 爬取過程中會有延遲以避免被封鎖")
    print("   - 建議在非交易時間執行以獲得完整資料")

if __name__ == "__main__":
    success = test_auto_update_price()
    
    if success:
        show_usage()
        print(f"\n✅ 測試通過！auto_update.py 已準備好使用")
    else:
        print(f"\n❌ 測試失敗！請檢查配置")
