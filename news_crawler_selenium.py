#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於 Selenium 的新聞爬蟲 - 支援動態載入內容
整合用戶提供的爬蟲代碼並優化
"""

from news_crawler_base import NewsCrawlerBase, NewsCrawlerException
from datetime import datetime, timedelta
import time
import requests
from bs4 import BeautifulSoup
import pandas as pd
import logging
import gc
import os

# Selenium 相關導入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class SeleniumNewsCrawler(NewsCrawlerBase):
    """基於 Selenium 的新聞爬蟲"""
    
    def __init__(self, db_path=None):
        super().__init__(db_path)
        
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium 未安裝，請執行: pip install selenium")
        
        # 優化設定
        self.max_scroll_attempts = 3  # 最多滾動3次
        self.scroll_pause_time = 1    # 滾動等待時間縮短
        self.max_news_per_stock = 10  # 每支股票最多10篇新聞
        self.timeout = 10             # 頁面載入超時
        
        # 設置 Chrome 選項
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')  # 無頭模式
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--disable-images')  # 不載入圖片
        self.chrome_options.add_argument('--disable-javascript')  # 部分情況下可以禁用JS
        self.chrome_options.add_argument('--window-size=1920,1080')
        
        # 禁用一些不必要的功能以提升速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.media_stream": 2,
        }
        self.chrome_options.add_experimental_option("prefs", prefs)

    def extract(self, ndaysAgo, interval: str, stock_code: str = None) -> list:
        """
        使用 Selenium 提取新聞資料
        """
        if not stock_code:
            raise NewsCrawlerException("Selenium 爬蟲需要指定股票代碼")
        
        try:
            self.logger.info(f"🚀 使用 Selenium 爬取 {stock_code} 新聞...")
            
            # 創建 WebDriver
            driver = webdriver.Chrome(options=self.chrome_options)
            driver.set_page_load_timeout(self.timeout)
            
            try:
                news_data = self._crawl_stock_news_selenium(driver, stock_code)
                return news_data
            finally:
                driver.quit()  # 確保關閉瀏覽器
                gc.collect()   # 強制垃圾回收
                
        except Exception as e:
            self.logger.error(f"❌ Selenium 爬取失敗: {e}")
            raise NewsCrawlerException(f"Selenium 新聞爬取失敗: {e}")

    def _crawl_stock_news_selenium(self, driver, stock_code):
        """使用 Selenium 爬取特定股票新聞"""
        data = []
        
        try:
            # 目標網址 - 使用用戶提供的URL格式
            url = f"https://www.cnyes.com/search/news?keyword={stock_code}"
            self.logger.info(f"🔍 訪問: {url}")
            
            driver.get(url)
            
            # 等待頁面載入
            WebDriverWait(driver, self.timeout).until(
                EC.presence_of_element_located((By.ID, "_SearchAll"))
            )
            
            # 優化的滾動策略 - 限制滾動次數
            self._smart_scroll(driver)
            
            # 擷取新聞元素
            elements = driver.find_elements(By.XPATH, '//*[@id="_SearchAll"]/section/div/a')
            
            self.logger.info(f"📋 找到 {len(elements)} 個新聞元素")
            
            # 限制處理數量以減少系統負擔
            elements = elements[:self.max_news_per_stock]
            
            # 擷取網址和標題
            for i, element in enumerate(elements):
                try:
                    link = element.get_attribute("href")
                    title_text = element.text
                    
                    if title_text and link:
                        title_parts = title_text.split('\n')
                        if len(title_parts) >= 2:
                            date_str = title_parts[0]
                            title = title_parts[1]
                        else:
                            date_str = datetime.now().strftime('%Y-%m-%d')
                            title = title_text
                        
                        # 生成新聞ID
                        news_id = f'selenium_{stock_code}_{int(time.time())}_{i}'
                        
                        # 轉換日期格式
                        try:
                            news_date = datetime.strptime(date_str, '%Y-%m-%d').strftime('%Y%m%d')
                        except:
                            news_date = datetime.now().strftime('%Y%m%d')
                        
                        news_time = datetime.now().strftime('%H%M%S')
                        
                        data.append([
                            news_id, news_date, news_time, title, link, 
                            "", "", "", stock_code  # 內容稍後獲取
                        ])
                        
                        self.logger.info(f"📰 {i+1}/{len(elements)}: {title[:50]}...")
                        
                except Exception as e:
                    self.logger.warning(f"處理新聞元素失敗: {e}")
                    continue
            
            # 獲取新聞內容（限制數量）
            self._fetch_news_content(data[:5])  # 只獲取前5篇的完整內容
            
            return data
            
        except Exception as e:
            self.logger.error(f"❌ Selenium 爬取過程失敗: {e}")
            return []

    def _smart_scroll(self, driver):
        """智能滾動策略 - 限制滾動次數和時間"""
        scroll_attempts = 0
        last_height = driver.execute_script("return document.body.scrollHeight")
        
        while scroll_attempts < self.max_scroll_attempts:
            # 滾動到底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(self.scroll_pause_time)
            
            # 檢查是否有新內容載入
            new_height = driver.execute_script("return document.body.scrollHeight")
            
            if new_height == last_height:
                break
                
            last_height = new_height
            scroll_attempts += 1
            
            self.logger.info(f"📜 滾動 {scroll_attempts}/{self.max_scroll_attempts}")
        
        self.logger.info(f"✅ 滾動完成，共滾動 {scroll_attempts} 次")

    def _fetch_news_content(self, news_data):
        """獲取新聞內容 - 使用 requests（更快）"""
        for i, news_item in enumerate(news_data):
            try:
                if len(news_item) > 4 and news_item[4]:  # 確保有連結
                    self.logger.info(f"📖 獲取內容 {i+1}/{len(news_data)}")
                    
                    # 使用 requests 獲取內容（比 Selenium 快）
                    response = requests.get(news_item[4], timeout=5)
                    response.raise_for_status()
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    article = soup.find('article')
                    
                    if article:
                        content = article.get_text(strip=True)
                        # 限制內容長度
                        news_item[6] = content[:1000] + "..." if len(content) > 1000 else content
                    else:
                        news_item[6] = "無法獲取內容"
                    
                    # 短暫延遲
                    time.sleep(0.5)
                    
            except Exception as e:
                self.logger.warning(f"獲取新聞內容失敗: {e}")
                if len(news_item) > 6:
                    news_item[6] = "內容獲取失敗"

    def transform(self, rawDatas):
        """轉換資料格式"""
        # 新聞內容: (news_id, date, time, source, title, reporter, link, content, stock_code)
        cleanDatas = []
        cleanTags = []
        
        for rawData in rawDatas:
            if len(rawData) >= 9:
                cleanDatas.append((
                    rawData[0], rawData[1], rawData[2], 'selenium_cnyes', rawData[3], 
                    rawData[5], rawData[4], rawData[6], rawData[8]
                ))
        
        return iter(cleanDatas), iter(cleanTags)

    def quick_search_selenium(self, stock_code: str):
        """快速搜尋模式 - 只獲取標題和連結"""
        try:
            self.logger.info(f"🚀 Selenium 快速搜尋: {stock_code}")
            
            # 創建輕量級 WebDriver
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-images')
            
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(5)
            
            try:
                url = f"https://www.cnyes.com/search/news?keyword={stock_code}"
                driver.get(url)
                
                # 等待元素載入
                WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.ID, "_SearchAll"))
                )
                
                # 只獲取前幾個新聞，不滾動
                elements = driver.find_elements(By.XPATH, '//*[@id="_SearchAll"]/section/div/a')[:5]
                
                results = []
                for element in elements:
                    try:
                        link = element.get_attribute("href")
                        title_text = element.text
                        
                        if title_text and link:
                            title_parts = title_text.split('\n')
                            title = title_parts[1] if len(title_parts) >= 2 else title_text
                            date_str = title_parts[0] if len(title_parts) >= 2 else datetime.now().strftime('%Y-%m-%d')
                            
                            results.append({
                                'date': date_str.replace('-', ''),
                                'time': datetime.now().strftime('%H%M%S'),
                                'source': 'selenium_quick',
                                'title': title,
                                'link': link
                            })
                    except:
                        continue
                
                return results
                
            finally:
                driver.quit()
                gc.collect()
                
        except Exception as e:
            self.logger.error(f"❌ Selenium 快速搜尋失敗: {e}")
            return []

if __name__ == '__main__':
    # 測試 Selenium 爬蟲
    if SELENIUM_AVAILABLE:
        print("🚀 測試 Selenium 新聞爬蟲")
        print("=" * 50)
        
        try:
            crawler = SeleniumNewsCrawler()
            
            # 測試快速搜尋
            print("🔍 測試快速搜尋...")
            quick_results = crawler.quick_search_selenium("2330")
            print(f"📊 快速搜尋結果: {len(quick_results)} 筆")
            
            for news in quick_results:
                print(f"  📰 {news['title'][:50]}...")
            
            # 測試完整爬取
            print("\n🔍 測試完整爬取...")
            result = crawler.run(ndays=1, stock_code="2330")
            print(f"📊 完整爬取結果: {result}")
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
    else:
        print("❌ Selenium 未安裝，請執行: pip install selenium")
        print("💡 並下載 ChromeDriver: https://chromedriver.chromium.org/")
