#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 price.db 中的 stock_info 表結構
"""

import sqlite3
import pandas as pd

def check_stock_info():
    """檢查 stock_info 表"""
    
    price_db_path = "D:/Finlab/history/tables/price.db"
    
    try:
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_info'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ stock_info 表存在")
            
            # 檢查表結構
            cursor.execute("PRAGMA table_info(stock_info)")
            columns = cursor.fetchall()
            print(f"\n📋 表結構:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 檢查數據量
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            count = cursor.fetchone()[0]
            print(f"\n📊 數據量: {count:,} 筆")
            
            # 檢查前10筆數據
            cursor.execute("SELECT * FROM stock_info LIMIT 10")
            rows = cursor.fetchall()
            print(f"\n🔍 前10筆數據:")
            for row in rows:
                print(f"  {row}")
            
            # 檢查產業分布
            cursor.execute("SELECT industry, COUNT(*) as count FROM stock_info GROUP BY industry ORDER BY count DESC")
            industries = cursor.fetchall()
            print(f"\n🏭 產業分布:")
            for industry, count in industries:
                print(f"  {industry}: {count:,} 家")
                
        else:
            print("❌ stock_info 表不存在")

            # 檢查有哪些表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"\n📋 現有表格:")
            for table in tables:
                print(f"  {table[0]}")

            # 檢查 stock_daily_data 表結構
            if any(t[0] == 'stock_daily_data' for t in tables):
                print(f"\n🔍 檢查 stock_daily_data 表結構:")
                cursor.execute("PRAGMA table_info(stock_daily_data)")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  {col[1]} ({col[2]})")

                # 檢查數據量
                cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
                count = cursor.fetchone()[0]
                print(f"\n📊 數據量: {count:,} 筆")

                # 檢查股票代號
                cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data ORDER BY stock_id LIMIT 20")
                stocks = cursor.fetchall()
                print(f"\n📈 前20支股票:")
                for stock in stocks:
                    print(f"  {stock[0]}")

        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_stock_info()
