#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版導出對話框功能
"""

import sys
import os
import tempfile
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QPushButton, QTextEdit, QMessageBox, QFileDialog, QHBoxLayout
)
from PyQt6.QtCore import Qt

class EnhancedExportTestWindow(QMainWindow):
    """測試增強版導出功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎨 增強版導出對話框測試")
        self.setGeometry(100, 100, 900, 700)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_text = QTextEdit()
        title_text.setMaximumHeight(200)
        title_text.setHtml("""
        <h2>🎨 增強版導出對話框測試</h2>
        <p><b>新功能特色：</b></p>
        <ul>
            <li>🎯 <b>自定義對話框</b> - 更美觀的圖示和格式</li>
            <li>🚀 <b>自定義按鈕</b> - "開啟檔案" 和 "稍後開啟"</li>
            <li>📊 <b>詳細資訊</b> - 檔案位置、記錄數量、檔案格式</li>
            <li>💡 <b>智能提示</b> - 成功/失敗的詳細說明</li>
            <li>🔧 <b>錯誤處理</b> - 完整的異常處理和解決方案</li>
        </ul>
        <p><b>測試目標：</b>驗證所有新功能是否正常工作</p>
        """)
        layout.addWidget(title_text)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 測試成功導出
        success_btn = QPushButton("✅ 測試成功導出")
        success_btn.clicked.connect(self.test_successful_export)
        success_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(success_btn)
        
        # 測試開啟失敗
        fail_btn = QPushButton("❌ 測試開啟失敗")
        fail_btn.clicked.connect(self.test_open_failure)
        fail_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        button_layout.addWidget(fail_btn)
        
        # 測試稍後開啟
        later_btn = QPushButton("📋 測試稍後開啟")
        later_btn.clicked.connect(self.test_open_later)
        later_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        button_layout.addWidget(later_btn)
        
        layout.addLayout(button_layout)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🚀 增強版導出對話框測試程式已啟動")
        self.log("📋 請選擇要測試的功能")
    
    def log(self, message):
        """添加日誌訊息"""
        self.log_text.append(f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}")
    
    def create_test_data(self):
        """創建測試數據"""
        return pd.DataFrame({
            '日期': ['1140725', '1140726', '1140727'],
            '指數': ['台灣50指數', '發行量加權股價指數', '中小型100指數'],
            '收盤指數': ['16234.56', '22456.78', '8765.43'],
            '漲跌': ['+', '+', '-'],
            '漲跌點數': ['23.45', '156.23', '12.34'],
            '漲跌百分比': ['+0.14%', '+0.70%', '-0.14%'],
            'data_type': ['市場指數資訊', '市場指數資訊', '市場指數資訊'],
            'crawl_time': ['2025-07-27 14:30:00', '2025-07-27 14:30:00', '2025-07-27 14:30:00']
        })
    
    def show_enhanced_export_dialog(self, file_path, df, simulate_open_success=True):
        """顯示增強版導出對話框"""
        # 模擬新的增強版對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("導出成功")
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setText("✅ 數據導出成功！")
        msg_box.setInformativeText(
            f"📁 檔案位置：{file_path}\n"
            f"📊 記錄數量：{len(df)} 筆\n"
            f"📄 檔案格式：CSV (UTF-8 with BOM)\n\n"
            f"❓ 是否要立即開啟導出的檔案？"
        )
        
        # 自定義按鈕文字
        yes_btn = msg_box.addButton("🚀 開啟檔案", QMessageBox.ButtonRole.YesRole)
        no_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        msg_box.setDefaultButton(yes_btn)
        
        reply = msg_box.exec()
        
        # 判斷用戶選擇
        if msg_box.clickedButton() == yes_btn:
            self.log("👆 用戶選擇「🚀 開啟檔案」")
            
            if simulate_open_success:
                # 模擬成功開啟
                try:
                    import platform
                    system = platform.system()
                    if system == "Windows":
                        os.startfile(file_path)
                    
                    self.log("✅ 檔案已成功開啟")
                    
                    # 成功開啟的提示
                    QMessageBox.information(
                        self,
                        "開啟成功",
                        f"📂 檔案已在預設應用程式中開啟\n\n"
                        f"💡 提示：如果檔案沒有自動開啟，請檢查預設的CSV檔案關聯程式"
                    )
                    
                except Exception as e:
                    self.log(f"❌ 開啟失敗: {e}")
            else:
                # 模擬開啟失敗
                error_msg = (
                    f"❌ 無法自動開啟檔案\n\n"
                    f"錯誤原因：模擬的開啟失敗\n\n"
                    f"📁 檔案位置：\n{file_path}\n\n"
                    f"💡 解決方案：\n"
                    f"1. 手動瀏覽到上述位置開啟檔案\n"
                    f"2. 檢查是否安裝了CSV檔案的預設開啟程式\n"
                    f"3. 嘗試用Excel、記事本或其他程式開啟"
                )
                
                QMessageBox.warning(self, "開啟失敗", error_msg)
                self.log("❌ 模擬開啟失敗情況")
        else:
            self.log("👆 用戶選擇「📋 稍後開啟」")
            
            # 顯示檔案位置提示
            QMessageBox.information(
                self,
                "導出完成",
                f"📋 數據已成功保存\n\n"
                f"📁 檔案位置：\n{file_path}\n\n"
                f"💡 您可以稍後手動開啟此檔案"
            )
    
    def test_successful_export(self):
        """測試成功導出和開啟"""
        self.log("🔍 測試成功導出和開啟...")
        
        df = self.create_test_data()
        
        # 創建臨時檔案
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
            df.to_csv(f.name, index=False, encoding='utf-8-sig')
            file_path = f.name
        
        self.log(f"📁 測試檔案已創建: {file_path}")
        
        # 顯示增強版對話框（模擬成功開啟）
        self.show_enhanced_export_dialog(file_path, df, simulate_open_success=True)
    
    def test_open_failure(self):
        """測試開啟失敗情況"""
        self.log("🔍 測試開啟失敗情況...")
        
        df = self.create_test_data()
        file_path = "/invalid/path/test_file.csv"  # 無效路徑
        
        self.log(f"📁 使用無效路徑測試: {file_path}")
        
        # 顯示增強版對話框（模擬開啟失敗）
        self.show_enhanced_export_dialog(file_path, df, simulate_open_success=False)
    
    def test_open_later(self):
        """測試稍後開啟選項"""
        self.log("🔍 測試稍後開啟選項...")
        
        df = self.create_test_data()
        
        # 創建臨時檔案
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
            df.to_csv(f.name, index=False, encoding='utf-8-sig')
            file_path = f.name
        
        self.log(f"📁 測試檔案已創建: {file_path}")
        self.log("💡 請在對話框中選擇「📋 稍後開啟」來測試此功能")
        
        # 顯示增強版對話框
        self.show_enhanced_export_dialog(file_path, df, simulate_open_success=True)

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = EnhancedExportTestWindow()
    window.show()
    
    print("🎨 增強版導出對話框測試程式已啟動")
    print("📋 測試功能：")
    print("1. ✅ 測試成功導出 - 驗證正常的導出和開啟流程")
    print("2. ❌ 測試開啟失敗 - 驗證錯誤處理和提示")
    print("3. 📋 測試稍後開啟 - 驗證用戶選擇不開啟的情況")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
