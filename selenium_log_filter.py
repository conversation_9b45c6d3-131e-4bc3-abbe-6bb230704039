#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Selenium 日誌過濾器
用於過濾和抑制 Selenium 和 Chrome 瀏覽器的不必要警告和錯誤訊息
特別針對 GPU、WebGL、ANGLE 等相關的錯誤進行過濾
"""

import logging
import sys
import os
import warnings
from contextlib import contextmanager

class SeleniumLogFilter:
    """Selenium 日誌過濾器類"""
    
    # 需要過濾的錯誤訊息關鍵字
    FILTERED_KEYWORDS = [
        'gpu\\command_buffer\\service\\webgpu_decoder_impl.cc',
        'Failed to query ID3D11Device from ANGLE',
        'Automatic fallback to software WebGL has been deprecated',
        'enable-unsafe-swiftshader',
        'GPU process',
        'WebGL',
        'ANGLE',
        'D3D11',
        'SwiftShader',
        'VizDisplayCompositor',
        'GPU memory buffer',
        'Hardware acceleration',
        'OpenGL',
        'DirectX'
    ]
    
    # 需要過濾的日誌級別
    FILTERED_LEVELS = [
        'WARNING',
        'INFO'
    ]
    
    def __init__(self):
        self.original_stderr = sys.stderr
        self.original_stdout = sys.stdout
        self.filtered_lines = []
        
    def should_filter_message(self, message):
        """判斷是否應該過濾該訊息"""
        message_str = str(message).strip()
        
        # 檢查是否包含需要過濾的關鍵字
        for keyword in self.FILTERED_KEYWORDS:
            if keyword.lower() in message_str.lower():
                return True
        
        # 檢查是否為空行或只包含時間戳
        if not message_str or message_str.startswith('[') and ']:' in message_str and len(message_str.split(']:')) > 1:
            content = message_str.split(']:')[-1].strip()
            if not content:
                return True
        
        return False
    
    def filter_chrome_logs(self):
        """設置 Chrome 相關的環境變數來減少日誌輸出"""
        # 設置 Chrome 日誌級別
        os.environ['CHROME_LOG_FILE'] = 'NUL' if os.name == 'nt' else '/dev/null'
        os.environ['CHROME_LOG_LEVEL'] = '3'  # 只顯示嚴重錯誤
        
        # 禁用 Chrome 的各種日誌
        os.environ['CHROME_DISABLE_LOGGING'] = '1'
        os.environ['CHROME_DISABLE_GPU_LOGGING'] = '1'
        os.environ['CHROME_DISABLE_WEBGL_LOGGING'] = '1'
        
        # WebDriver Manager 日誌設定
        os.environ['WDM_LOG_LEVEL'] = '0'
        os.environ['WDM_PRINT_FIRST_LINE'] = 'False'
        
    def setup_logging_filters(self):
        """設置日誌過濾器"""
        # 過濾 Selenium 相關日誌
        selenium_logger = logging.getLogger('selenium')
        selenium_logger.setLevel(logging.ERROR)
        selenium_logger.addFilter(self._create_log_filter())
        
        # 過濾 urllib3 日誌
        urllib3_logger = logging.getLogger('urllib3')
        urllib3_logger.setLevel(logging.ERROR)
        
        # 過濾 requests 日誌
        requests_logger = logging.getLogger('requests')
        requests_logger.setLevel(logging.ERROR)
        
        # 過濾 webdriver_manager 日誌
        wdm_logger = logging.getLogger('webdriver_manager')
        wdm_logger.setLevel(logging.ERROR)
        
        # 抑制 Python 警告
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=FutureWarning)
        
    def _create_log_filter(self):
        """創建自定義日誌過濾器"""
        class CustomLogFilter(logging.Filter):
            def __init__(self, parent_filter):
                super().__init__()
                self.parent_filter = parent_filter
                
            def filter(self, record):
                message = record.getMessage()
                return not self.parent_filter.should_filter_message(message)
        
        return CustomLogFilter(self)
    
    @contextmanager
    def suppress_output(self):
        """上下文管理器：暫時抑制標準輸出和錯誤輸出"""
        class FilteredWriter:
            def __init__(self, original_writer, filter_func):
                self.original_writer = original_writer
                self.filter_func = filter_func
                
            def write(self, message):
                if not self.filter_func(message):
                    self.original_writer.write(message)
                    
            def flush(self):
                self.original_writer.flush()
                
            def __getattr__(self, name):
                return getattr(self.original_writer, name)
        
        # 創建過濾後的寫入器
        filtered_stderr = FilteredWriter(self.original_stderr, self.should_filter_message)
        filtered_stdout = FilteredWriter(self.original_stdout, self.should_filter_message)
        
        # 替換標準輸出
        sys.stderr = filtered_stderr
        sys.stdout = filtered_stdout
        
        try:
            yield
        finally:
            # 恢復原始輸出
            sys.stderr = self.original_stderr
            sys.stdout = self.original_stdout
    
    def apply_all_filters(self):
        """應用所有過濾器"""
        self.filter_chrome_logs()
        self.setup_logging_filters()
        
    @staticmethod
    def create_clean_logger(name, level=logging.INFO):
        """創建一個乾淨的日誌記錄器"""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 清除現有的處理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 創建新的處理器
        handler = logging.StreamHandler()
        handler.setLevel(level)
        
        # 設置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        logger.addHandler(handler)
        logger.propagate = False
        
        return logger

# 全域過濾器實例
_global_filter = None

def initialize_selenium_log_filter():
    """初始化全域 Selenium 日誌過濾器"""
    global _global_filter
    if _global_filter is None:
        _global_filter = SeleniumLogFilter()
        _global_filter.apply_all_filters()
    return _global_filter

def get_filtered_logger(name, level=logging.INFO):
    """獲取過濾後的日誌記錄器"""
    initialize_selenium_log_filter()
    return SeleniumLogFilter.create_clean_logger(name, level)

@contextmanager
def suppress_selenium_output():
    """上下文管理器：抑制 Selenium 相關輸出"""
    filter_instance = initialize_selenium_log_filter()
    with filter_instance.suppress_output():
        yield

# 便捷函數
def setup_clean_selenium_environment():
    """設置乾淨的 Selenium 環境"""
    filter_instance = initialize_selenium_log_filter()
    
    print("🔧 已設置 Selenium 日誌過濾器")
    print("✅ GPU 和 WebGL 相關錯誤訊息已被過濾")
    print("✅ 不必要的警告和資訊已被抑制")
    
    return filter_instance

# 使用範例
if __name__ == "__main__":
    # 設置過濾器
    log_filter = setup_clean_selenium_environment()
    
    # 創建測試日誌
    logger = get_filtered_logger(__name__)
    
    # 測試日誌輸出
    logger.info("這是一條正常的資訊")
    logger.warning("這是一條警告")
    logger.error("這是一條錯誤")
    
    # 測試過濾功能
    test_messages = [
        "gpu\\command_buffer\\service\\webgpu_decoder_impl.cc:1735] Failed to query ID3D11Device from ANGLE.",
        "Automatic fallback to software WebGL has been deprecated.",
        "這是一條正常的訊息，不應該被過濾",
        "[73056:44524:0717/132910.440:ERROR:gpu\\command_buffer\\service\\webgpu_decoder_impl.cc(1735)] Failed to query ID3D11Device from ANGLE."
    ]
    
    print("\n🧪 測試過濾功能:")
    for message in test_messages:
        should_filter = log_filter.should_filter_message(message)
        status = "🚫 過濾" if should_filter else "✅ 保留"
        print(f"{status}: {message[:50]}...")
    
    print("\n✅ 日誌過濾器測試完成")
