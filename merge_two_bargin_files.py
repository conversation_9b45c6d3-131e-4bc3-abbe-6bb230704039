#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併兩個不同目錄的 bargin_report.pkl 檔案
O3mh_strategy2AA: 2012-05-02 至 2022-09-13
O3mh_strategy8AA: 2022-08-30 至 2025-07-18
"""

import pandas as pd
import os
import shutil
from datetime import datetime
import gc

def merge_two_bargin_files():
    """合併兩個 bargin_report.pkl 檔案"""
    print("🔄 合併兩個 bargin_report.pkl 檔案")
    print("=" * 60)
    
    # 檔案路徑
    file1_path = r"D:\Finlab\backup\O3mh_strategy2AA\history\tables\bargin_report.pkl"
    file2_path = r"D:\Finlab\backup\O3mh_strategy8AA\history\tables\bargin_report.pkl"
    target_path = r"D:\Finlab\backup\O3mh_strategy2AA\history\tables\bargin_report.pkl"
    
    print(f"📁 檔案1 (舊資料): {file1_path}")
    print(f"📁 檔案2 (新資料): {file2_path}")
    print(f"📁 目標檔案: {target_path}")
    
    # 檢查檔案存在
    if not os.path.exists(file1_path):
        print(f"❌ 檔案1不存在: {file1_path}")
        return False
    
    if not os.path.exists(file2_path):
        print(f"❌ 檔案2不存在: {file2_path}")
        return False
    
    try:
        # 創建備份
        backup_file = f"{target_path}_before_merge_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        shutil.copy2(file1_path, backup_file)
        print(f"✅ 備份創建: {backup_file}")
        
        # 讀取檔案1 (舊資料)
        print("\n📖 讀取檔案1 (舊資料)...")
        data1 = pd.read_pickle(file1_path)
        dates1 = data1.index.get_level_values('date')
        
        file1_size = os.path.getsize(file1_path) / 1024 / 1024
        print(f"   檔案大小: {file1_size:.1f} MB")
        print(f"   資料筆數: {len(data1):,}")
        print(f"   日期範圍: {dates1.min()} 至 {dates1.max()}")
        print(f"   唯一日期: {len(dates1.unique())}")
        
        # 讀取檔案2 (新資料)
        print("\n📖 讀取檔案2 (新資料)...")
        data2 = pd.read_pickle(file2_path)
        dates2 = data2.index.get_level_values('date')
        
        file2_size = os.path.getsize(file2_path) / 1024 / 1024
        print(f"   檔案大小: {file2_size:.1f} MB")
        print(f"   資料筆數: {len(data2):,}")
        print(f"   日期範圍: {dates2.min()} 至 {dates2.max()}")
        print(f"   唯一日期: {len(dates2.unique())}")
        
        # 分析重疊情況
        print("\n🔍 分析重疊情況...")
        dates1_set = set(dates1)
        dates2_set = set(dates2)
        overlap_dates = dates1_set.intersection(dates2_set)
        
        if overlap_dates:
            print(f"   ⚠️ 發現重疊日期: {len(overlap_dates)} 個")
            print(f"   重疊範圍: {min(overlap_dates)} 至 {max(overlap_dates)}")
            
            # 移除檔案1中的重疊日期，保留檔案2的資料（較新）
            print("   🔄 移除檔案1中的重疊日期...")
            data1_filtered = data1[~data1.index.get_level_values('date').isin(overlap_dates)]
            print(f"   檔案1過濾後: {len(data1_filtered):,} 筆")
        else:
            print("   ✅ 沒有重疊日期")
            data1_filtered = data1
        
        # 合併資料
        print("\n🔄 合併資料...")
        merged_data = pd.concat([data1_filtered, data2])
        print(f"   初步合併: {len(merged_data):,} 筆")
        
        # 清理記憶體
        del data1, data2, data1_filtered
        gc.collect()
        
        # 最終處理
        print("🔄 最終處理...")
        
        # 去除可能的重複
        original_count = len(merged_data)
        merged_data = merged_data[~merged_data.index.duplicated(keep='last')]
        dedupe_count = len(merged_data)
        
        if original_count != dedupe_count:
            print(f"   去重: {original_count:,} → {dedupe_count:,} 筆")
        
        # 排序
        merged_data = merged_data.sort_index()
        
        # 最終統計
        final_dates = merged_data.index.get_level_values('date')
        print(f"\n✅ 合併完成:")
        print(f"   最終筆數: {len(merged_data):,}")
        print(f"   日期範圍: {final_dates.min()} 至 {final_dates.max()}")
        print(f"   唯一日期: {len(final_dates.unique())}")
        print(f"   時間跨度: {final_dates.max().year - final_dates.min().year + 1} 年")
        
        # 預估檔案大小
        expected_size = file1_size + file2_size - (len(overlap_dates) * file1_size / len(dates1_set) if overlap_dates else 0)
        print(f"   預估大小: {expected_size:.1f} MB")
        
        # 保存合併結果
        print("\n💾 保存合併結果...")
        merged_data.to_pickle(target_path)
        
        # 驗證結果
        actual_size = os.path.getsize(target_path) / 1024 / 1024
        print(f"✅ 保存完成:")
        print(f"   檔案大小: {actual_size:.1f} MB")
        print(f"   檔案位置: {target_path}")
        
        # 驗證讀取
        print("\n🔍 驗證合併結果...")
        verify_data = pd.read_pickle(target_path)
        verify_dates = verify_data.index.get_level_values('date')
        
        print(f"✅ 驗證成功:")
        print(f"   可讀取筆數: {len(verify_data):,}")
        print(f"   驗證日期範圍: {verify_dates.min()} 至 {verify_dates.max()}")
        
        # 更新日期範圍記錄
        print("\n🔄 更新日期範圍記錄...")
        date_range_file = r"D:\Finlab\backup\O3mh_strategy2AA\history\date_range.pickle"
        
        try:
            if os.path.exists(date_range_file):
                import pickle
                with open(date_range_file, 'rb') as f:
                    date_ranges = pickle.load(f)
            else:
                date_ranges = {}
            
            date_ranges['bargin_report'] = (verify_dates.min(), verify_dates.max())
            
            with open(date_range_file, 'wb') as f:
                pickle.dump(date_ranges, f)
            
            print(f"✅ 日期範圍記錄已更新")
            
        except Exception as e:
            print(f"⚠️ 更新日期範圍記錄失敗: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("🔧 兩個 Bargin Report 檔案合併工具")
    print("=" * 60)
    print("🎯 目標: 合併兩個不同目錄的 bargin_report.pkl")
    print("📊 預期結果:")
    print("   • 時間範圍: 2012-05-02 至 2025-07-18")
    print("   • 檔案大小: 約 280MB")
    print("   • 完整的融資融券歷史資料")
    print("=" * 60)
    
    response = input("是否開始合併？(y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    success = merge_two_bargin_files()
    
    if success:
        print("\n🎉 合併完成！")
        print("💡 現在您有完整的 bargin_report 資料:")
        print("   • 從 2012 年到 2025 年的完整資料")
        print("   • 可以在 auto_update.py 中繼續更新")
        print("   • 備份檔案已自動創建")
    else:
        print("\n❌ 合併失敗")
        print("💡 請檢查錯誤訊息並重試")

if __name__ == "__main__":
    main()
