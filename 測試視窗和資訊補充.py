#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試視窗標題欄和資訊補充功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from O3mh_gui_v21_optimized import FinlabGUI

def test_window_controls():
    """測試視窗控制按鈕"""
    print("🪟 測試視窗控制按鈕")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 測試資料
    test_data = {
        '排名': 'N/A',
        '股票代碼': '2330',
        '股票名稱': '台積電',
        '西元年月': '202507',
        '當月營收': '263,708,978',
        '上個月營收': '221,850,000',
        '去年同月營收': '207,870,000',
        'YoY%': '+26.86%',
        'MoM%': '-17.72%',
        '殖利率': 'N/A',
        '本益比': 'N/A',
        '股價淨值比': 'N/A',
        'EPS': 'N/A',
        '綜合評分': 'N/A'
    }
    
    print("✅ 創建對話框，檢查視窗標題欄...")
    
    # 創建對話框但不顯示（只測試創建）
    try:
        # 模擬 show_monthly_revenue_assessment 的視窗創建部分
        from PyQt6.QtWidgets import QDialog
        
        dialog = QDialog(gui)
        dialog.setWindowTitle(f"📊 {test_data['股票代碼']} {test_data['股票名稱']} - 月營收綜合評估")
        dialog.setMinimumSize(900, 600)
        
        # 設置視窗標誌
        dialog.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )
        
        print("✅ 視窗標誌設置成功")
        print("  - 最小化按鈕：已啟用")
        print("  - 最大化按鈕：已啟用")
        print("  - 關閉按鈕：已啟用")
        
        # 檢查視窗標誌
        flags = dialog.windowFlags()
        has_minimize = bool(flags & Qt.WindowType.WindowMinimizeButtonHint)
        has_maximize = bool(flags & Qt.WindowType.WindowMaximizeButtonHint)
        has_close = bool(flags & Qt.WindowType.WindowCloseButtonHint)
        
        print(f"  - 最小化標誌：{'✅' if has_minimize else '❌'}")
        print(f"  - 最大化標誌：{'✅' if has_maximize else '❌'}")
        print(f"  - 關閉標誌：{'✅' if has_close else '❌'}")
        
    except Exception as e:
        print(f"❌ 視窗創建失敗: {e}")
    
    app.quit()

def test_additional_info_logic():
    """測試額外資訊補充邏輯"""
    print("\n📊 測試額外資訊補充邏輯")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 測試股票代碼
    test_stocks = ['2330', '2317', '2454', '8021', '1234']
    
    print("測試市場排名估算:")
    for stock_code in test_stocks:
        try:
            ranking = gui.estimate_market_ranking(stock_code)
            print(f"  {stock_code}: {ranking if ranking else 'N/A'}")
        except Exception as e:
            print(f"  {stock_code}: 錯誤 - {e}")
    
    print("\n測試額外資訊獲取:")
    for stock_code in test_stocks:
        try:
            additional_info = gui.get_additional_stock_info(stock_code)
            print(f"  {stock_code}:")
            for key, value in additional_info.items():
                print(f"    {key}: {value}")
            if not additional_info:
                print(f"    無額外資訊")
        except Exception as e:
            print(f"  {stock_code}: 錯誤 - {e}")
    
    app.quit()

def test_pe_database_access():
    """測試PE資料庫存取"""
    print("\n💎 測試PE資料庫存取")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 檢查PE檔案
    pe_paths = [
        'pe.pkl',
        'history/pe.pkl',
        'D:/Finlab/history/pe.pkl'
    ]
    
    print("檢查PE檔案:")
    for pe_path in pe_paths:
        exists = os.path.exists(pe_path)
        print(f"  {pe_path}: {'✅ 存在' if exists else '❌ 不存在'}")
        
        if exists:
            try:
                import pandas as pd
                pe_df = pd.read_pickle(pe_path)
                print(f"    資料形狀: {pe_df.shape}")
                
                if isinstance(pe_df.index, pd.MultiIndex):
                    pe_df_reset = pe_df.reset_index()
                else:
                    pe_df_reset = pe_df.copy()
                
                print(f"    欄位: {list(pe_df_reset.columns)[:5]}...")  # 只顯示前5個欄位
                
                # 測試查詢2330
                if 'stock_id' in pe_df_reset.columns:
                    stock_pe = pe_df_reset[pe_df_reset['stock_id'].astype(str).str.contains('2330', na=False)]
                    print(f"    2330記錄數: {len(stock_pe)}")
                    
                    if not stock_pe.empty:
                        latest_pe = stock_pe.iloc[-1]
                        pe_ratio = latest_pe.get('本益比', 'N/A')
                        print(f"    2330本益比: {pe_ratio}")
                
                break  # 找到第一個可用檔案就停止
                
            except Exception as e:
                print(f"    ❌ 讀取失敗: {e}")
    
    # 測試PE資訊獲取
    print("\n測試PE資訊獲取:")
    test_stocks = ['2330', '2317', '2454']
    
    for stock_code in test_stocks:
        try:
            pe_info = gui.get_pe_info_from_database(stock_code)
            print(f"  {stock_code}: {pe_info if pe_info else '無PE資訊'}")
        except Exception as e:
            print(f"  {stock_code}: 錯誤 - {e}")
    
    app.quit()

def main():
    """主函數"""
    print("🚀 視窗和資訊補充功能測試")
    print("=" * 60)
    
    # 測試視窗控制按鈕
    test_window_controls()
    
    # 測試額外資訊邏輯
    test_additional_info_logic()
    
    # 測試PE資料庫存取
    test_pe_database_access()
    
    print("\n" + "=" * 60)
    print("🎉 測試完成！")
    
    print("\n修復內容總結:")
    print("✅ 1. 視窗標題欄：添加最大化、最小化、關閉按鈕")
    print("✅ 2. 市場排名估算：為知名股票提供排名資訊")
    print("✅ 3. 額外資訊補充：嘗試從多個來源獲取財務資訊")
    print("✅ 4. PE資料庫整合：從PE檔案獲取本益比和EPS")
    print("✅ 5. 智能資訊合併：優先使用表格資料，再補充資料庫資料")
    
    print("\n預期改善效果:")
    print("🪟 視窗可以最大化、最小化、關閉")
    print("🏆 台積電等知名股票會顯示估算排名")
    print("💎 如果有PE資料，會顯示本益比資訊")
    print("📊 資訊更完整，減少N/A顯示")
    
    print("\n請啟動主程式測試實際效果：")
    print("python O3mh_gui_v21_optimized.py")

if __name__ == "__main__":
    main()
