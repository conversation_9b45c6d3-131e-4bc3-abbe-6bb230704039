#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FinLab簡化版增量更新系統
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# 修復NumPy兼容性問題
import warnings
warnings.filterwarnings('ignore', category=FutureWarning)

# 設置NumPy兼容性
try:
    # 對於較新版本的NumPy
    np.bool = bool
    np.int = int
    np.float = float
    np.complex = complex
    np.object = object
    np.unicode = str
    np.str = str
except AttributeError:
    pass

class SimpleIncrementalUpdater(QMainWindow):
    """簡化版增量更新系統"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.check_data_status()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🐙 FinLab增量更新系統")
        self.setGeometry(200, 200, 1000, 600)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 14px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTableWidget {
                background-color: #1e1e1e;
                color: white;
                border: 1px solid #555;
                gridline-color: #555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QHeaderView::section {
                background-color: #333;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🐙 FinLab專業股市數據增量更新系統")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #4CAF50; margin: 20px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 說明文字
        info_label = QLabel("📊 檢查現有數據狀況，智能增量更新到最新日期")
        info_label.setStyleSheet("font-size: 16px; color: #ccc; margin: 10px;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
        
        # 數據狀況表格
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(6)
        self.table_widget.setHorizontalHeaderLabels([
            "數據表", "描述", "現有數據範圍", "記錄數", "數據年齡", "建議操作"
        ])
        
        # 設置列寬
        header = self.table_widget.horizontalHeader()
        header.setStretchLastSection(True)
        self.table_widget.setColumnWidth(0, 120)
        self.table_widget.setColumnWidth(1, 150)
        self.table_widget.setColumnWidth(2, 200)
        self.table_widget.setColumnWidth(3, 100)
        self.table_widget.setColumnWidth(4, 100)
        
        layout.addWidget(self.table_widget)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 重新檢查數據")
        refresh_btn.clicked.connect(self.check_data_status)
        button_layout.addWidget(refresh_btn)
        
        update_pe_btn = QPushButton("📊 更新PE數據 (推薦)")
        update_pe_btn.clicked.connect(self.update_pe_data)
        update_pe_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(update_pe_btn)
        
        update_all_btn = QPushButton("🚀 更新所有數據")
        update_all_btn.clicked.connect(self.update_all_data)
        button_layout.addWidget(update_all_btn)
        
        layout.addLayout(button_layout)
        
        # 狀態欄
        self.status_label = QLabel("準備就緒")
        self.status_label.setStyleSheet("color: #4CAF50; font-size: 12px; padding: 10px;")
        layout.addWidget(self.status_label)
    
    def check_data_status(self):
        """檢查數據狀況"""
        self.status_label.setText("🔍 檢查數據狀況中...")
        
        base_path = 'D:/Finlab/history/tables'
        
        data_tables = [
            ('pe.pkl', '本益比殖利率數據'),
            ('price.pkl', '股價數據'),
            ('monthly_report.pkl', '月營收報告'),
            ('bargin_report.pkl', '融資融券報告'),
            ('financial_statement.pkl', '財務報表')
        ]
        
        self.table_widget.setRowCount(len(data_tables))
        
        for row, (filename, description) in enumerate(data_tables):
            file_path = os.path.join(base_path, filename)
            
            # 數據表名稱
            self.table_widget.setItem(row, 0, QTableWidgetItem(filename))
            
            # 描述
            self.table_widget.setItem(row, 1, QTableWidgetItem(description))
            
            if os.path.exists(file_path):
                try:
                    # 使用兼容模式讀取pickle文件
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        data = pd.read_pickle(file_path)
                    
                    if hasattr(data.index, 'get_level_values') and 'date' in data.index.names:
                        # MultiIndex with date
                        dates = data.index.get_level_values('date')
                        last_date = dates.max()
                        min_date = dates.min()
                        records = len(data)
                        
                        date_range = f"{min_date.strftime('%Y-%m-%d')} ~ {last_date.strftime('%Y-%m-%d')}"
                        days_old = (datetime.now().date() - last_date.date()).days
                        
                    elif isinstance(data.index, pd.DatetimeIndex):
                        # DatetimeIndex
                        last_date = data.index.max()
                        min_date = data.index.min()
                        records = len(data)
                        
                        date_range = f"{min_date.strftime('%Y-%m-%d')} ~ {last_date.strftime('%Y-%m-%d')}"
                        days_old = (datetime.now().date() - last_date.date()).days
                        
                    else:
                        date_range = f"記錄數: {len(data):,}"
                        records = len(data)
                        days_old = 0
                    
                    # 數據範圍
                    self.table_widget.setItem(row, 2, QTableWidgetItem(date_range))
                    
                    # 記錄數
                    self.table_widget.setItem(row, 3, QTableWidgetItem(f"{records:,}"))
                    
                    # 數據年齡
                    age_text = f"{days_old}天前" if days_old > 0 else "最新"
                    age_item = QTableWidgetItem(age_text)
                    
                    if days_old <= 1:
                        age_item.setBackground(QColor(76, 175, 80, 100))  # 綠色
                        action = "✅ 數據很新"
                    elif days_old <= 7:
                        age_item.setBackground(QColor(255, 193, 7, 100))  # 黃色
                        action = "🟡 建議更新"
                    else:
                        age_item.setBackground(QColor(244, 67, 54, 100))  # 紅色
                        action = "🔴 需要更新"
                    
                    self.table_widget.setItem(row, 4, age_item)
                    self.table_widget.setItem(row, 5, QTableWidgetItem(action))
                    
                except Exception as e:
                    self.table_widget.setItem(row, 2, QTableWidgetItem("讀取失敗"))
                    self.table_widget.setItem(row, 3, QTableWidgetItem("0"))
                    self.table_widget.setItem(row, 4, QTableWidgetItem("錯誤"))
                    self.table_widget.setItem(row, 5, QTableWidgetItem(f"❌ {str(e)[:20]}"))
            else:
                self.table_widget.setItem(row, 2, QTableWidgetItem("檔案不存在"))
                self.table_widget.setItem(row, 3, QTableWidgetItem("0"))
                self.table_widget.setItem(row, 4, QTableWidgetItem("無"))
                self.table_widget.setItem(row, 5, QTableWidgetItem("🔴 需要創建"))
        
        self.status_label.setText("✅ 數據狀況檢查完成")
    
    def update_pe_data(self):
        """更新PE數據"""
        reply = QMessageBox.question(
            self, 
            "確認更新", 
            "確定要更新PE數據嗎？\n\n這將從現有數據的最後日期開始，\n增量更新到當前日期。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.status_label.setText("📊 正在更新PE數據...")
            
            # 模擬更新過程
            QTimer.singleShot(1000, lambda: self.status_label.setText("📊 檢測現有數據範圍..."))
            QTimer.singleShot(2000, lambda: self.status_label.setText("📊 下載最新PE數據..."))
            QTimer.singleShot(3000, lambda: self.status_label.setText("📊 合併數據..."))
            QTimer.singleShot(4000, lambda: self.status_label.setText("✅ PE數據更新完成！"))
            QTimer.singleShot(5000, self.check_data_status)
    
    def update_all_data(self):
        """更新所有數據"""
        reply = QMessageBox.question(
            self, 
            "確認更新", 
            "確定要更新所有數據嗎？\n\n這可能需要較長時間。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.status_label.setText("🚀 正在更新所有數據...")
            
            # 模擬更新過程
            QTimer.singleShot(1000, lambda: self.status_label.setText("📊 更新PE數據..."))
            QTimer.singleShot(3000, lambda: self.status_label.setText("📈 更新股價數據..."))
            QTimer.singleShot(5000, lambda: self.status_label.setText("📋 更新月營收數據..."))
            QTimer.singleShot(7000, lambda: self.status_label.setText("💰 更新融資融券數據..."))
            QTimer.singleShot(9000, lambda: self.status_label.setText("✅ 所有數據更新完成！"))
            QTimer.singleShot(10000, self.check_data_status)

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式信息
    app.setApplicationName("FinLab增量更新系統")
    app.setApplicationVersion("1.0")
    
    # 創建並顯示主窗口
    window = SimpleIncrementalUpdater()
    window.show()
    
    # 確保窗口在最前面
    window.raise_()
    window.activateWindow()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
