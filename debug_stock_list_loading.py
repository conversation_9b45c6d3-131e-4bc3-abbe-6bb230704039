#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試股票清單載入問題
"""

import sqlite3
import pandas as pd
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_database_connection():
    """檢查資料庫連接"""
    print("🔍 檢查資料庫連接...")
    
    db_path = 'D:/Finlab/history/tables/price.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📊 找到表格: {tables}")
        
        if 'stock_daily_data' in tables:
            # 檢查表結構
            cursor.execute("PRAGMA table_info(stock_daily_data)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"📋 stock_daily_data 欄位: {columns}")
            
            # 檢查資料數量
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_count = cursor.fetchone()[0]
            print(f"📈 總資料筆數: {total_count}")
            
            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
            stock_count = cursor.fetchone()[0]
            print(f"📊 股票數量: {stock_count}")
            
            # 檢查最新日期
            cursor.execute("SELECT MAX(date) FROM stock_daily_data")
            latest_date = cursor.fetchone()[0]
            print(f"📅 最新日期: {latest_date}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return False

def test_stock_list_query():
    """測試股票清單查詢"""
    print("\n🔍 測試股票清單查詢...")
    
    db_path = 'D:/Finlab/history/tables/price.db'
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 測試基本查詢
        query = """
            SELECT DISTINCT stock_id, '' as stock_name, '' as listing_status, '' as industry
            FROM stock_daily_data
            WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
              AND LENGTH(stock_id) BETWEEN 4 AND 6
            ORDER BY
                CASE
                    WHEN LENGTH(stock_id) = 4 THEN 1
                    WHEN LENGTH(stock_id) = 5 THEN 2
                    WHEN LENGTH(stock_id) = 6 THEN 3
                END,
                CAST(stock_id AS INTEGER)
            LIMIT 20
        """
        
        print(f"📝 執行查詢: {query}")
        df = pd.read_sql_query(query, conn)
        
        print(f"✅ 查詢成功，返回 {len(df)} 筆資料")
        print("📋 前20筆股票:")
        for _, row in df.iterrows():
            print(f"   {row['stock_id']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")
        return False

def test_stock_filter():
    """測試股票篩選"""
    print("\n🔍 測試股票篩選...")
    
    try:
        from stock_filter import StockFilter
        
        stock_filter = StockFilter()
        print(f"✅ 股票篩選器載入成功")
        
        # 測試一些股票代碼
        test_stocks = ['2330', '0050', '0054', '00625K', '1234', '9999']
        
        for stock in test_stocks:
            is_valid = stock_filter.is_valid_stock(stock)
            print(f"   {stock}: {'✅ 有效' if is_valid else '❌ 無效'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票篩選器測試失敗: {e}")
        return False

def test_stock_name_mapping():
    """測試股票名稱映射"""
    print("\n🔍 測試股票名稱映射...")
    
    try:
        from stock_name_mapping import get_stock_name, get_cached_stock_info
        
        # 測試獲取股票資訊
        stock_info = get_cached_stock_info()
        print(f"✅ 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 測試一些股票名稱
        test_stocks = ['2330', '0050', '00694B', '1234']
        
        for stock in test_stocks:
            name = get_stock_name(stock)
            print(f"   {stock}: {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票名稱映射測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 股票清單載入問題調試工具")
    print("=" * 50)
    
    # 檢查資料庫連接
    if not check_database_connection():
        return
    
    # 測試股票清單查詢
    if not test_stock_list_query():
        return
    
    # 測試股票篩選
    test_stock_filter()
    
    # 測試股票名稱映射
    test_stock_name_mapping()
    
    print("\n🎉 調試完成！")

if __name__ == "__main__":
    main()
