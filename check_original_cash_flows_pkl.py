#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查原始 cash_flows.pkl 檔案中是否有股票名稱資訊
"""

import pandas as pd
import os

def check_original_cash_flows_pkl():
    """檢查原始 cash_flows.pkl 檔案"""
    
    print("=" * 80)
    print("🔍 檢查原始 cash_flows.pkl 檔案")
    print("=" * 80)
    
    pkl_file = r'D:\Finlab\history\tables\cash_flows.pkl'
    
    try:
        # 檢查檔案是否存在
        if not os.path.exists(pkl_file):
            print(f"❌ 檔案不存在: {pkl_file}")
            return False
        
        # 讀取 pkl 檔案
        print(f"📖 讀取 pkl 檔案...")
        df = pd.read_pickle(pkl_file)
        
        print(f"✅ 成功讀取 pkl 檔案")
        print(f"📊 資料形狀: {df.shape}")
        print(f"📋 索引結構: {df.index.names}")
        print(f"📋 欄位數: {len(df.columns)}")
        
        # 重置索引以便檢查
        df_reset = df.reset_index()
        print(f"📋 重置索引後的欄位: {list(df_reset.columns[:10])}")
        
        # 檢查是否有股票名稱相關的欄位
        name_columns = [col for col in df_reset.columns if 'name' in col.lower() or '名稱' in col or '公司' in col]
        print(f"📋 可能的股票名稱欄位: {name_columns}")
        
        # 檢查 stock_id 的情況
        if 'stock_id' in df_reset.columns:
            print(f"\n📊 stock_id 分析:")
            unique_stocks = df_reset['stock_id'].nunique()
            print(f"   唯一股票數: {unique_stocks}")
            
            # 檢查以 000 開頭的股票（ETF）
            etf_stocks = df_reset[df_reset['stock_id'].str.startswith('000', na=False)]['stock_id'].unique()
            print(f"   ETF股票數 (000開頭): {len(etf_stocks)}")
            print(f"   ETF範例: {list(etf_stocks[:10])}")
            
            # 檢查一般股票
            normal_stocks = df_reset[~df_reset['stock_id'].str.startswith('000', na=False)]['stock_id'].unique()
            print(f"   一般股票數: {len(normal_stocks)}")
            print(f"   一般股票範例: {list(normal_stocks[:10])}")
        
        # 顯示前幾筆資料
        print(f"\n📊 前5筆資料:")
        print(df_reset.head())
        
        # 檢查特定股票的資料
        if 'stock_id' in df_reset.columns:
            print(f"\n📊 檢查特定股票 (000116, 1101) 的資料:")
            
            # 檢查 000116
            stock_000116 = df_reset[df_reset['stock_id'] == '000116']
            if not stock_000116.empty:
                print(f"   000116: {len(stock_000116)} 筆記錄")
                print(f"   000116 範例: {stock_000116.iloc[0].to_dict()}")
            
            # 檢查 1101
            stock_1101 = df_reset[df_reset['stock_id'] == '1101']
            if not stock_1101.empty:
                print(f"   1101: {len(stock_1101)} 筆記錄")
                print(f"   1101 範例: {stock_1101.iloc[0].to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_original_cash_flows_pkl()
