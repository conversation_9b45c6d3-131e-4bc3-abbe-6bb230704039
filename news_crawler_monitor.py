#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新聞爬蟲系統監控器 - 使用 psutil
"""

import psutil
import time
import threading
from datetime import datetime

class NewscrawlerMonitor:
    """新聞爬蟲系統監控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.monitoring = False
        self.monitor_data = []
        
    def start_monitoring(self):
        """開始監控"""
        self.monitoring = True
        self.monitor_data = []
        
        def monitor_loop():
            while self.monitoring:
                try:
                    # 獲取系統資源使用情況
                    cpu_percent = self.process.cpu_percent()
                    memory_info = self.process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    # 獲取系統整體資源
                    system_memory = psutil.virtual_memory()
                    system_cpu = psutil.cpu_percent()
                    
                    data_point = {
                        'timestamp': datetime.now(),
                        'process_cpu': cpu_percent,
                        'process_memory_mb': memory_mb,
                        'system_cpu': system_cpu,
                        'system_memory_percent': system_memory.percent,
                        'available_memory_gb': system_memory.available / 1024**3
                    }
                    
                    self.monitor_data.append(data_point)
                    
                    # 即時顯示（可選）
                    print(f"⏱️  {data_point['timestamp'].strftime('%H:%M:%S')} | "
                          f"💾 {memory_mb:.1f}MB | "
                          f"🖥️  {cpu_percent:.1f}% CPU | "
                          f"🌐 系統CPU: {system_cpu:.1f}%")
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"監控錯誤: {e}")
                    break
        
        # 在背景執行緒中運行監控
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        
    def get_summary(self):
        """獲取監控摘要"""
        if not self.monitor_data:
            return "無監控資料"
        
        # 計算統計數據
        memory_values = [d['process_memory_mb'] for d in self.monitor_data]
        cpu_values = [d['process_cpu'] for d in self.monitor_data]
        
        max_memory = max(memory_values)
        avg_memory = sum(memory_values) / len(memory_values)
        max_cpu = max(cpu_values)
        avg_cpu = sum(cpu_values) / len(cpu_values)
        
        duration = (self.monitor_data[-1]['timestamp'] - self.monitor_data[0]['timestamp']).total_seconds()
        
        summary = f"""
📊 系統資源使用摘要
{'='*40}
⏱️  監控時間: {duration:.1f} 秒
💾 記憶體使用:
   • 最大: {max_memory:.1f} MB
   • 平均: {avg_memory:.1f} MB
🖥️  CPU使用:
   • 最大: {max_cpu:.1f}%
   • 平均: {avg_cpu:.1f}%
📈 效能評級: {self._get_performance_grade(max_memory, avg_cpu)}
        """
        
        return summary
    
    def _get_performance_grade(self, max_memory, avg_cpu):
        """評估效能等級"""
        if max_memory < 50 and avg_cpu < 10:
            return "🚀 優秀 (輕量級)"
        elif max_memory < 100 and avg_cpu < 25:
            return "✅ 良好 (中等負擔)"
        elif max_memory < 200 and avg_cpu < 50:
            return "⚠️ 一般 (較重負擔)"
        else:
            return "❌ 需要優化 (重負擔)"

def test_with_monitoring():
    """使用監控測試新聞爬蟲"""
    print("🚀 帶監控的新聞爬蟲測試")
    print("=" * 50)
    
    monitor = NewscrawlerMonitor()
    
    # 測試輕量級爬蟲
    print("\n🔍 測試輕量級爬蟲...")
    monitor.start_monitoring()
    
    try:
        from news_crawler_lightweight import LightweightNewsCrawler
        crawler = LightweightNewsCrawler()
        
        start_time = time.time()
        result = crawler.run_lightweight(ndays=1)
        end_time = time.time()
        
        print(f"✅ 輕量級爬蟲完成: {result}")
        print(f"⏱️  執行時間: {end_time - start_time:.2f} 秒")
        
    except Exception as e:
        print(f"❌ 輕量級爬蟲失敗: {e}")
    
    time.sleep(2)  # 讓監控收集更多數據
    monitor.stop_monitoring()
    
    print(monitor.get_summary())
    
    # 測試優化版爬蟲（如果時間允許）
    print("\n🔍 測試優化版爬蟲...")
    monitor = NewscrawlerMonitor()  # 重新建立監控器
    monitor.start_monitoring()
    
    try:
        from news_crawler_optimized import OptimizedNewsCrawler
        crawler = OptimizedNewsCrawler()
        
        start_time = time.time()
        result = crawler.run(ndays=1)
        end_time = time.time()
        
        print(f"✅ 優化版爬蟲完成: {result}")
        print(f"⏱️  執行時間: {end_time - start_time:.2f} 秒")
        
    except Exception as e:
        print(f"❌ 優化版爬蟲失敗: {e}")
    
    time.sleep(2)
    monitor.stop_monitoring()
    
    print(monitor.get_summary())

def check_system_resources():
    """檢查系統資源狀況"""
    print("🖥️  系統資源檢查")
    print("=" * 30)
    
    # CPU資訊
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 記憶體資訊
    memory = psutil.virtual_memory()
    
    # 磁碟資訊
    disk = psutil.disk_usage('/')
    
    print(f"🖥️  CPU: {cpu_count} 核心, 使用率: {cpu_percent}%")
    print(f"💾 記憶體: {memory.total/1024**3:.1f}GB 總量, {memory.percent}% 使用中")
    print(f"💿 磁碟: {disk.total/1024**3:.1f}GB 總量, {disk.percent}% 使用中")
    
    # 建議
    if memory.percent > 80:
        print("⚠️ 記憶體使用率過高，建議關閉其他程式")
    elif memory.percent > 60:
        print("💡 記憶體使用率中等，適合運行輕量級爬蟲")
    else:
        print("✅ 記憶體充足，可以運行任何版本的爬蟲")
    
    if cpu_percent > 80:
        print("⚠️ CPU使用率過高，建議等待系統空閒時再運行爬蟲")
    else:
        print("✅ CPU使用率正常")

if __name__ == "__main__":
    # 檢查系統資源
    check_system_resources()
    
    print("\n" + "="*50)
    
    # 進行監控測試
    test_with_monitoring()
    
    print("\n💡 psutil 庫的好處:")
    print("1. 🔍 即時監控系統資源使用")
    print("2. 📊 量化爬蟲效能改善效果")
    print("3. ⚠️ 及早發現記憶體洩漏問題")
    print("4. 🎯 找出系統瓶頸所在")
    print("5. 📈 為進一步優化提供數據支持")
