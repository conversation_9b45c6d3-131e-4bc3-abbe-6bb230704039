#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修復 stock_info 表缺失問題
創建 stock_info 表並填入台股基本資料
"""

import sqlite3
import os
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_stock_info_table():
    """創建 stock_info 表並填入基本股票資料"""
    
    # 台灣股票完整名稱對照表
    taiwan_stocks = {
        # 權值股 & 科技股
        "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2308": "台達電", "2382": "廣達",
        "2303": "聯電", "2357": "華碩", "2409": "友達", "3008": "大立光", "3711": "日月光投控",
        "2379": "瑞昱", "3034": "聯詠", "3481": "群創", "4938": "和碩", "2327": "國巨",
        "2474": "可成", "6669": "緯穎", "2376": "技嘉", "2395": "研華", "3037": "欣興",
        "2408": "南亞科", "3443": "創意", "6415": "矽力-KY", "3661": "世芯-KY", "6770": "力積電",
        "2449": "京元電子", "2458": "義隆", "2301": "光寶科", "2324": "仁寶", "2356": "英業達",
        "2377": "微星", "2412": "中華電", "3045": "台灣大", "4904": "遠傳", "6505": "台塑化",
        
        # 金融股
        "2881": "富邦金", "2882": "國泰金", "2883": "開發金", "2884": "玉山金", "2885": "元大金",
        "2886": "兆豐金", "2887": "台新金", "2888": "新光金", "2889": "國票金", "2890": "永豐金",
        "2891": "中信金", "2892": "第一金", "2893": "王道銀行", "5880": "合庫金", "2801": "彰銀",
        "2823": "中壽", "2880": "華南金", "2834": "臺企銀", "2836": "高雄銀", "2845": "遠東銀",
        
        # 傳統產業
        "1101": "台泥", "1102": "亞泥", "1103": "嘉泥", "1216": "統一", "1301": "台塑",
        "1303": "南亞", "1326": "台化", "1402": "遠東新", "2002": "中鋼", "2105": "正新",
        "2207": "和泰車", "2227": "裕日車", "2311": "日月光", "2312": "金寶", "2313": "華通",
        "2314": "台揚", "2324": "仁寶", "2328": "廣宇", "2329": "華立", "2603": "長榮",
        "2609": "陽明", "2610": "華航", "2633": "台灣高鐵", "2912": "統一超",
        
        # ETF
        "0050": "元大台灣50", "0051": "元大中型100", "0052": "富邦科技", "0053": "元大電子",
        "0054": "元大台商50", "0055": "元大MSCI金融", "0056": "元大高股息", "0057": "富邦摩台",
        "00692": "富邦公司治理", "00701": "國泰股利精選30", "00713": "元大台灣高息低波",
        "00878": "國泰永續高股息", "00881": "國泰台灣5G+", "00885": "富邦越南",
        "00891": "中信關鍵半導體", "00893": "國泰智能電動車", "00895": "富邦未來車",
        "00900": "富邦特選高股息30", "00919": "群益台灣精選高息", "00929": "復華台灣科技優息",
        "00934": "中信成長高股息", "00936": "台新永續高息中小", "00939": "統一台灣高息動能",
        
        # 其他常見股票
        "1815": "富喬", "3017": "奇鋐", "4991": "環宇-KY", "8358": "金居",
        "6290": "良維", "6196": "帆宣", "6667": "信紘科", "4414": "如興", "2414": "精技",
        "8499": "鼎炫-KY", "3231": "緯創", "2492": "華新科", "2498": "宏達電",
        "5871": "中租-KY", "5876": "上海商銀", "6505": "台塑化", "8454": "富邦媒",
        "8996": "高力", "3045": "台灣大", "4904": "遠傳", "2474": "可成"
    }
    
    # 數據庫文件路徑
    db_files = [
        'db/price.db',
        'db/pe_data.db',
        'db/stock_data.db'
    ]
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            # 創建目錄
            os.makedirs(os.path.dirname(db_file), exist_ok=True)
            logger.info(f"📁 創建目錄: {os.path.dirname(db_file)}")
        
        try:
            logger.info(f"🔧 處理數據庫: {db_file}")
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 創建 stock_info 表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_id TEXT PRIMARY KEY,
                    stock_name TEXT NOT NULL,
                    industry TEXT DEFAULT '科技業',
                    market TEXT DEFAULT '上市',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 清空現有數據（如果有的話）
            cursor.execute('DELETE FROM stock_info')
            
            # 插入股票資料
            insert_count = 0
            for stock_id, stock_name in taiwan_stocks.items():
                # 根據股票代碼判斷產業類別
                if stock_id.startswith('00'):
                    industry = 'ETF'
                elif stock_id in ['2881', '2882', '2883', '2884', '2885', '2886', '2887', '2888', '2889', '2890', '2891', '2892', '2893', '5880', '2801', '2823', '2880']:
                    industry = '金融業'
                elif stock_id in ['1101', '1102', '1103', '1301', '1303', '1326', '2002']:
                    industry = '傳統產業'
                elif stock_id in ['2330', '2317', '2454', '2308', '2382', '2303', '2357', '2409', '3008']:
                    industry = '半導體業'
                elif stock_id in ['2412', '3045', '4904']:
                    industry = '電信業'
                else:
                    industry = '科技業'
                
                # 判斷市場別
                market = '上櫃' if stock_id.startswith('6') or stock_id.startswith('8') else '上市'
                
                cursor.execute('''
                    INSERT INTO stock_info (stock_id, stock_name, industry, market)
                    VALUES (?, ?, ?, ?)
                ''', (stock_id, stock_name, industry, market))
                insert_count += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ {db_file}: 成功創建 stock_info 表，插入 {insert_count} 筆股票資料")
            
        except Exception as e:
            logger.error(f"❌ 處理 {db_file} 失敗: {e}")
            continue

def verify_stock_info_table():
    """驗證 stock_info 表是否正確創建"""
    db_files = ['db/price.db', 'db/pe_data.db', 'db/stock_data.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 檢查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_info'")
                if cursor.fetchone():
                    # 檢查數據數量
                    cursor.execute("SELECT COUNT(*) FROM stock_info")
                    count = cursor.fetchone()[0]
                    
                    # 顯示幾個範例
                    cursor.execute("SELECT stock_id, stock_name, industry FROM stock_info LIMIT 5")
                    samples = cursor.fetchall()
                    
                    logger.info(f"✅ {db_file}: stock_info 表包含 {count} 筆資料")
                    logger.info("📊 範例資料:")
                    for stock_id, stock_name, industry in samples:
                        logger.info(f"   {stock_id}: {stock_name} ({industry})")
                else:
                    logger.warning(f"⚠️ {db_file}: stock_info 表不存在")
                
                conn.close()
                
            except Exception as e:
                logger.error(f"❌ 驗證 {db_file} 失敗: {e}")

if __name__ == "__main__":
    logger.info("🚀 開始修復 stock_info 表...")
    
    # 創建表並填入數據
    create_stock_info_table()
    
    # 驗證結果
    logger.info("🔍 驗證修復結果...")
    verify_stock_info_table()
    
    logger.info("✅ stock_info 表修復完成！")
    print("\n" + "="*60)
    print("🎉 修復完成！現在可以重新啟動主程式，應該不會再出現警告訊息。")
    print("="*60)
