"""
每日交易資料爬蟲GUI界面
基於tkinter實現，參考月營收下載器的設計模式
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import datetime
import os
import pandas as pd
import logging
from taiwan_stock_price_crawler import (
    gen_task_parameter_list,
    crawler,
    DailyTradingDatabase
)

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DailyTradingCrawlerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📊 台股每日交易資料爬蟲")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 設定視窗圖標和樣式
        self.setup_style()
        
        # 初始化變數
        self.is_running = False
        self.db = DailyTradingDatabase()  # 使用預設路徑 D:\Finlab\history\tables\daily_trading.db

        # 🎯 初始化股票篩選器
        self.stock_filter = None
        self.init_stock_filter()

        # 建立GUI元件
        self.create_widgets()

        # 更新統計資訊
        self.update_stats()

    def init_stock_filter(self):
        """初始化股票篩選器"""
        try:
            from stock_filter import StockFilter
            self.stock_filter = StockFilter()
            logger.info("✅ 股票篩選器初始化完成")
        except ImportError as e:
            logger.warning(f"⚠️ 股票篩選器模組載入失敗: {e}")
            self.stock_filter = None

    def set_stock_filter(self, filter_rules):
        """
        設定股票篩選規則（供外部調用）

        Args:
            filter_rules (dict): 篩選規則字典
        """
        if self.stock_filter:
            logger.info("📊 已設定股票篩選規則：4碼、5碼及前綴為00的6碼ETF")
        else:
            logger.warning("⚠️ 股票篩選器未初始化，無法設定篩選規則")

    def filter_stock_codes(self, stock_codes):
        """
        篩選股票代碼清單

        Args:
            stock_codes (list): 原始股票代碼清單

        Returns:
            list: 篩選後的股票代碼清單
        """
        if not self.stock_filter or not stock_codes:
            return stock_codes

        filtered_codes = self.stock_filter.filter_stock_list(stock_codes)
        logger.info(f"📊 股票篩選：原始 {len(stock_codes)} 支 → 篩選後 {len(filtered_codes)} 支")
        return filtered_codes

    def setup_style(self):
        """設定GUI樣式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 設定顏色主題
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'), foreground='#2E86AB')
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'), foreground='#A23B72')
        style.configure('Success.TLabel', foreground='#28A745')
        style.configure('Error.TLabel', foreground='#DC3545')
        style.configure('Warning.TLabel', foreground='#FFC107')
    
    def create_widgets(self):
        """建立GUI元件"""
        # 主標題
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(title_frame, text="📊 台股每日交易資料爬蟲", 
                 style='Title.TLabel').pack()
        ttk.Label(title_frame, text="支援證交所(TWSE)和櫃買中心(TPEX)資料", 
                 font=('Arial', 9)).pack()
        
        # 分隔線
        ttk.Separator(self.root, orient='horizontal').pack(fill='x', padx=10, pady=5)
        
        # 日期選擇區域
        date_frame = ttk.LabelFrame(self.root, text="📅 日期範圍設定", padding=10)
        date_frame.pack(fill='x', padx=10, pady=5)
        
        # 開始日期
        start_frame = ttk.Frame(date_frame)
        start_frame.pack(fill='x', pady=2)
        ttk.Label(start_frame, text="開始日期:", width=10).pack(side='left')
        self.start_date_var = tk.StringVar(value=(datetime.date.today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))
        self.start_date_entry = ttk.Entry(start_frame, textvariable=self.start_date_var, width=12)
        self.start_date_entry.pack(side='left', padx=5)
        ttk.Button(start_frame, text="選擇", command=self.select_start_date, width=6).pack(side='left')
        
        # 結束日期
        end_frame = ttk.Frame(date_frame)
        end_frame.pack(fill='x', pady=2)
        ttk.Label(end_frame, text="結束日期:", width=10).pack(side='left')
        self.end_date_var = tk.StringVar(value=datetime.date.today().strftime('%Y-%m-%d'))
        self.end_date_entry = ttk.Entry(end_frame, textvariable=self.end_date_var, width=12)
        self.end_date_entry.pack(side='left', padx=5)
        ttk.Button(end_frame, text="選擇", command=self.select_end_date, width=6).pack(side='left')
        
        # 快速日期選擇
        quick_frame = ttk.Frame(date_frame)
        quick_frame.pack(fill='x', pady=5)
        ttk.Label(quick_frame, text="快速選擇:").pack(side='left')
        ttk.Button(quick_frame, text="最近7天", command=lambda: self.set_quick_date(7), width=8).pack(side='left', padx=2)
        ttk.Button(quick_frame, text="最近30天", command=lambda: self.set_quick_date(30), width=8).pack(side='left', padx=2)
        ttk.Button(quick_frame, text="本月", command=self.set_current_month, width=8).pack(side='left', padx=2)
        
        # 控制按鈕區域
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="🚀 開始爬取", 
                                      command=self.start_crawling, width=12)
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="⏹️ 停止", 
                                     command=self.stop_crawling, width=12, state='disabled')
        self.stop_button.pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="📁 開啟資料庫", 
                  command=self.open_database_folder, width=12).pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="📊 匯出CSV", 
                  command=self.export_csv, width=12).pack(side='left', padx=5)
        
        # 統計資訊區域
        stats_frame = ttk.LabelFrame(self.root, text="📈 統計資訊", padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        self.stats_label = ttk.Label(stats_frame, text="載入中...", font=('Arial', 9))
        self.stats_label.pack()
        
        # 進度條
        progress_frame = ttk.Frame(self.root)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(side='left', fill='x', expand=True)
        
        self.progress_label = ttk.Label(progress_frame, text="就緒", width=20)
        self.progress_label.pack(side='right', padx=5)
        
        # 日誌顯示區域
        log_frame = ttk.LabelFrame(self.root, text="📝 執行日誌", padding=5)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 建立文字框和滾動條
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill='both', expand=True)
        
        self.log_text = tk.Text(text_frame, height=15, wrap='word', font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 清除日誌按鈕
        ttk.Button(log_frame, text="🗑️ 清除日誌", 
                  command=self.clear_log, width=12).pack(pady=2)
    
    def select_start_date(self):
        """選擇開始日期"""
        self.select_date(self.start_date_var)
    
    def select_end_date(self):
        """選擇結束日期"""
        self.select_date(self.end_date_var)
    
    def select_date(self, date_var):
        """日期選擇對話框"""
        try:
            from tkinter import simpledialog
            date_str = simpledialog.askstring("日期選擇", "請輸入日期 (YYYY-MM-DD):", 
                                            initialvalue=date_var.get())
            if date_str:
                # 驗證日期格式
                datetime.datetime.strptime(date_str, '%Y-%m-%d')
                date_var.set(date_str)
        except ValueError:
            messagebox.showerror("錯誤", "日期格式錯誤，請使用 YYYY-MM-DD 格式")
        except ImportError:
            messagebox.showinfo("提示", "請手動輸入日期 (YYYY-MM-DD 格式)")
    
    def set_quick_date(self, days):
        """設定快速日期"""
        end_date = datetime.date.today()
        start_date = end_date - datetime.timedelta(days=days)
        
        self.start_date_var.set(start_date.strftime('%Y-%m-%d'))
        self.end_date_var.set(end_date.strftime('%Y-%m-%d'))
    
    def set_current_month(self):
        """設定為本月"""
        today = datetime.date.today()
        start_date = today.replace(day=1)
        
        self.start_date_var.set(start_date.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
    
    def update_stats(self):
        """更新統計資訊"""
        try:
            total_count = self.db.get_data_count()
            self.stats_label.config(text=f"資料庫總筆數: {total_count:,} 筆")
        except Exception as e:
            self.stats_label.config(text=f"統計資訊載入失敗: {e}")
    
    def log_message(self, message, level='INFO'):
        """記錄日誌訊息"""
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert('end', log_entry)
        self.log_text.see('end')
        self.root.update_idletasks()
        
        # 同時記錄到logger
        if level == 'ERROR':
            logger.error(message)
        elif level == 'WARNING':
            logger.warning(message)
        else:
            logger.info(message)
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete('1.0', 'end')
    
    def validate_dates(self):
        """驗證日期"""
        try:
            start_date = datetime.datetime.strptime(self.start_date_var.get(), '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(self.end_date_var.get(), '%Y-%m-%d').date()
            
            if start_date > end_date:
                raise ValueError("開始日期不能晚於結束日期")
            
            if end_date > datetime.date.today():
                raise ValueError("結束日期不能超過今天")
            
            return start_date, end_date
            
        except ValueError as e:
            messagebox.showerror("日期錯誤", str(e))
            return None, None
    
    def start_crawling(self):
        """開始爬取"""
        start_date, end_date = self.validate_dates()
        if not start_date or not end_date:
            return
        
        self.is_running = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # 在新線程中執行爬取
        thread = threading.Thread(target=self.crawl_worker, 
                                 args=(start_date.strftime('%Y-%m-%d'), 
                                      end_date.strftime('%Y-%m-%d')))
        thread.daemon = True
        thread.start()
    
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.log_message("使用者要求停止爬取", 'WARNING')
    
    def crawl_worker(self, start_date, end_date):
        """爬取工作線程"""
        try:
            self.log_message(f"開始爬取 {start_date} 到 {end_date} 的資料")
            
            # 生成任務列表
            task_list = gen_task_parameter_list(start_date, end_date)
            total_tasks = len(task_list)
            
            self.log_message(f"共有 {total_tasks} 個爬取任務")
            
            success_count = 0
            error_count = 0
            
            for i, task in enumerate(task_list):
                if not self.is_running:
                    self.log_message("爬取已停止", 'WARNING')
                    break
                
                # 更新進度
                progress = (i / total_tasks) * 100
                self.progress_var.set(progress)
                self.progress_label.config(text=f"{i+1}/{total_tasks}")
                
                # 執行爬取
                try:
                    df = crawler(task)
                    if not df.empty:
                        # 🎯 應用股票篩選功能
                        original_count = len(df)
                        if self.stock_filter and '股票代碼' in df.columns:
                            # 篩選股票代碼
                            valid_codes = self.filter_stock_codes(df['股票代碼'].tolist())
                            df = df[df['股票代碼'].isin(valid_codes)]
                            filtered_count = len(df)

                            if filtered_count < original_count:
                                self.log_message(
                                    f"📊 {task['data_source'].upper()} {task['date']}: "
                                    f"篩選 {original_count} → {filtered_count} 筆"
                                )

                        if not df.empty:
                            saved_count = self.db.save_data(df)
                            if saved_count > 0:
                                success_count += 1
                                self.log_message(f"✅ {task['data_source'].upper()} {task['date']}: {saved_count} 筆")
                            else:
                                error_count += 1
                                self.log_message(f"❌ {task['data_source'].upper()} {task['date']}: 儲存失敗", 'ERROR')
                        else:
                            self.log_message(f"⚠️ {task['data_source'].upper()} {task['date']}: 篩選後無有效資料", 'WARNING')
                    else:
                        self.log_message(f"⚠️ {task['data_source'].upper()} {task['date']}: 無資料", 'WARNING')
                        
                except Exception as e:
                    error_count += 1
                    self.log_message(f"❌ {task['data_source'].upper()} {task['date']}: {e}", 'ERROR')
            
            # 完成
            self.progress_var.set(100)
            self.progress_label.config(text="完成")
            self.log_message(f"爬取完成！成功: {success_count}, 失敗: {error_count}")
            
            # 更新統計
            self.update_stats()
            
        except Exception as e:
            self.log_message(f"爬取過程發生錯誤: {e}", 'ERROR')
        
        finally:
            # 恢復按鈕狀態
            self.is_running = False
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
    
    def open_database_folder(self):
        """開啟資料庫資料夾"""
        try:
            db_path = os.path.abspath(self.db.db_path)
            folder_path = os.path.dirname(db_path)
            
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                subprocess.run(f'explorer "{folder_path}"', shell=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
                
        except Exception as e:
            messagebox.showerror("錯誤", f"無法開啟資料夾: {e}")
    
    def export_csv(self):
        """匯出CSV"""
        try:
            # 選擇儲存位置
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="匯出每日交易資料"
            )
            
            if filename:
                # 從資料庫讀取資料
                import sqlite3
                conn = sqlite3.connect(self.db.db_path)
                df = pd.read_sql_query("SELECT * FROM daily_trading_data ORDER BY Date, StockID", conn)
                conn.close()
                
                # 匯出CSV
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                self.log_message(f"✅ 成功匯出 {len(df)} 筆資料到 {filename}")
                messagebox.showinfo("成功", f"已匯出 {len(df)} 筆資料")
                
        except Exception as e:
            self.log_message(f"匯出失敗: {e}", 'ERROR')
            messagebox.showerror("錯誤", f"匯出失敗: {e}")
    
    def run(self):
        """執行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = DailyTradingCrawlerGUI()
    app.run()
