#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用修復後的 benchmark 爬蟲進行安全更新
每10筆存檔，降低風險
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

from crawler import table_date_range, to_pickle, date_range
import datetime
import pandas as pd
import time
import random
import urllib3
import requests
from io import StringIO

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def crawl_benchmark_fixed(date):
    """修復版本的 benchmark 爬蟲"""
    date_str = date.strftime('%Y%m%d')
    
    url = "https://www.twse.com.tw/rwd/zh/TAIEX/MI_5MINS_INDEX"
    params = {
        "response": "csv",
        "date": date_str,
        "_": "1544020420045"
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for attempt in range(3):
        try:
            time.sleep(random.uniform(2, 5))
            res = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            
            if res.status_code == 429:
                print("⚠️ 請求過於頻繁，等待60秒...")
                time.sleep(60)
                continue
            
            res.raise_for_status()
            
            if len(res.text) < 10:
                return pd.DataFrame()
            
            df = pd.read_csv(StringIO(res.text.replace("=","")), header=1, index_col='時間')
            df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
            df.columns = df.columns.str.replace(' ', '')
            df.index = pd.to_datetime(date.strftime('%Y %m %d ') + pd.Series(df.index))
            df = df.apply(lambda s: s.astype(str).str.replace(",", "").astype(float))
            df = df.reset_index().rename(columns={'時間':'date'})
            df['stock_id'] = '台股指數'
            return df.set_index(['stock_id', 'date'])
            
        except Exception as e:
            if attempt < 2:
                time.sleep((attempt + 1) * 10)
    
    return pd.DataFrame()

def update_benchmark_safe():
    """安全更新 benchmark - 每10筆存檔"""
    print("🔧 安全更新 Benchmark (修復版)")
    print("=" * 60)
    
    try:
        # 獲取需要更新的日期範圍
        first_date, last_date = table_date_range('benchmark')
        print(f"📅 當前範圍: {first_date} 至 {last_date}")
        
        if last_date:
            dates = date_range(last_date, datetime.datetime.now())
            
            if dates:
                # 過濾工作日
                trading_dates = [d for d in dates if d.weekday() < 5]
                
                if trading_dates:
                    print(f"📈 需要更新 {len(trading_dates)} 個交易日")
                    print(f"   範圍: {trading_dates[0]} 至 {trading_dates[-1]}")
                    
                    # 確認更新
                    response = input(f"\n是否開始更新？(y/N): ").strip().lower()
                    if response != 'y':
                        print("❌ 更新已取消")
                        return False
                    
                    # 執行更新
                    return execute_safe_update(trading_dates)
                else:
                    print("✅ 沒有需要更新的交易日")
                    return True
            else:
                print("✅ 已是最新")
                return True
        else:
            print("❌ 無法獲取日期範圍")
            return False
            
    except Exception as e:
        print(f"❌ 更新準備失敗: {str(e)}")
        return False

def execute_safe_update(dates):
    """執行安全更新 - 每10筆存檔"""
    print(f"\n🚀 開始安全更新 Benchmark")
    print(f"   總日期數: {len(dates)}")
    print(f"   存檔頻率: 每10筆")
    
    # 創建備份
    backup_file = f"history/tables/benchmark_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    original_file = "history/tables/benchmark.pkl"
    
    if os.path.exists(original_file):
        import shutil
        shutil.copy2(original_file, backup_file)
        print(f"✅ 備份創建: {backup_file}")
    
    df_list = []
    success_count = 0
    save_count = 0
    
    for i, date in enumerate(dates):
        try:
            print(f"📅 {date.strftime('%Y-%m-%d')}: {i+1:3d}/{len(dates):3d} [{(i+1)/len(dates)*100:5.1f}%]", end="")
            
            # 使用修復版爬蟲
            df = crawl_benchmark_fixed(date)
            
            if df is not None and not df.empty:
                df_list.append(df)
                success_count += 1
                print("  ✅")
                
                # 每10筆存檔一次
                if len(df_list) % 10 == 0:
                    save_count += 1
                    print(f"  💾 第 {save_count} 次存檔 ({len(df_list)} 筆)...")
                    temp_df = pd.concat(df_list, ignore_index=True)
                    to_pickle(temp_df, 'benchmark')
                    print(f"  ✅ 存檔完成")
                    
            else:
                print("  ⚠️ (無資料)")
            
            # 隨機延遲 (與 auto_update.py 相同)
            if i < len(dates) - 1:
                delay = random.uniform(5, 10)
                print(f"  ⏳ 等待 {delay:.1f} 秒...")
                time.sleep(delay)
                
        except Exception as e:
            print(f"  ❌ 錯誤: {str(e)[:50]}...")
            continue
    
    # 最終保存
    if df_list:
        print(f"\n💾 最終保存...")
        combined_df = pd.concat(df_list, ignore_index=True)
        to_pickle(combined_df, 'benchmark')
        
        print(f"✅ 更新完成:")
        print(f"   成功日期: {success_count}/{len(dates)}")
        print(f"   總資料筆數: {len(combined_df):,}")
        print(f"   存檔次數: {save_count + 1}")
        
        return True
    else:
        print(f"\n❌ 沒有成功爬取任何資料")
        return False

def main():
    """主函數"""
    print("🔧 Benchmark 安全更新工具")
    print("=" * 60)
    print("🎯 特色:")
    print("   • 使用修復後的爬蟲 (解決 IP 封鎖問題)")
    print("   • 每10筆自動存檔 (降低風險)")
    print("   • 5-10秒隨機延遲 (避免被封鎖)")
    print("   • 完整錯誤處理和重試機制")
    print("=" * 60)
    
    success = update_benchmark_safe()
    
    if success:
        print(f"\n🎉 Benchmark 更新成功！")
        print(f"💡 現在可以在 auto_update.py 中正常使用:")
        print(f"   ('benchmark', crawl_benchmark, date_range),")
        print(f"🔧 建議將修復應用到 crawler.py 中的 crawl_benchmark 函數")
    else:
        print(f"\n⚠️ 更新失敗或被取消")

if __name__ == "__main__":
    main()
