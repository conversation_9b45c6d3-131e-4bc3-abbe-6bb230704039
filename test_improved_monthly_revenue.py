#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試改進後的月營收爬蟲
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_month_revenue_single():
    """測試單一月份的月營收爬蟲"""
    print("🧪 測試改進後的月營收爬蟲...")
    
    try:
        from crawler import month_revenue
        
        # 測試日期 - 選擇一個應該有資料的月份
        test_date = datetime(2023, 5, 10)  # 2023年5月
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 測試上市公司
        print("\n1️⃣ 測試上市公司 (sii)...")
        sii_data = month_revenue('sii', test_date)
        
        if len(sii_data) > 0:
            print(f"✅ 上市公司資料: {len(sii_data)} 筆")
            print(f"   欄位: {list(sii_data.columns)[:5]}...")
            print(f"   樣本資料:")
            print(sii_data.head(3))
        else:
            print("❌ 上市公司資料為空")
        
        # 測試上櫃公司
        print("\n2️⃣ 測試上櫃公司 (otc)...")
        otc_data = month_revenue('otc', test_date)
        
        if len(otc_data) > 0:
            print(f"✅ 上櫃公司資料: {len(otc_data)} 筆")
            print(f"   欄位: {list(otc_data.columns)[:5]}...")
            print(f"   樣本資料:")
            print(otc_data.head(3))
        else:
            print("❌ 上櫃公司資料為空")
        
        return len(sii_data) > 0 or len(otc_data) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_crawl_monthly_report():
    """測試完整的 crawl_monthly_report 函數"""
    print("\n🧪 測試完整的 crawl_monthly_report 函數...")
    
    try:
        from crawler import crawl_monthly_report
        
        # 測試日期
        test_date = datetime(2023, 5, 10)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 執行爬取
        result = crawl_monthly_report(test_date)
        
        if len(result) > 0:
            print(f"✅ 合併資料成功: {len(result)} 筆")
            print(f"   欄位: {list(result.columns)}")
            print(f"   索引: {result.index.names}")
            print(f"   樣本資料:")
            print(result.head(3))
            return True
        else:
            print("❌ 合併資料為空")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_multiple_dates():
    """測試多個日期"""
    print("\n🧪 測試多個日期的月營收爬蟲...")
    
    try:
        from crawler import crawl_monthly_report
        
        # 測試最近幾個月
        test_dates = [
            datetime(2023, 3, 10),
            datetime(2023, 4, 10),
            datetime(2023, 5, 10),
        ]
        
        success_count = 0
        
        for date in test_dates:
            print(f"\n📅 測試 {date.strftime('%Y-%m')}...")
            
            result = crawl_monthly_report(date)
            
            if len(result) > 0:
                print(f"✅ 成功: {len(result)} 筆資料")
                success_count += 1
            else:
                print("❌ 失敗: 無資料")
        
        print(f"\n📊 測試結果: {success_count}/{len(test_dates)} 個月份成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("🔧 改進後的月營收爬蟲測試")
    print("=" * 60)
    print("🎯 測試多重備援爬蟲方案")
    print("=" * 60)
    
    # 測試1: 單一月份
    test1 = test_month_revenue_single()
    
    # 測試2: 完整函數
    test2 = test_crawl_monthly_report()
    
    # 測試3: 多個日期
    test3 = test_multiple_dates()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    print(f"單一月份測試: {'✅ 成功' if test1 else '❌ 失敗'}")
    print(f"完整函數測試: {'✅ 成功' if test2 else '❌ 失敗'}")
    print(f"多日期測試: {'✅ 成功' if test3 else '❌ 失敗'}")
    
    if test1 or test2 or test3:
        print("\n🎉 改進後的爬蟲至少部分成功！")
        print("💡 優勢:")
        print("   • 多重備援方案")
        print("   • 更好的錯誤處理")
        print("   • 自動嘗試不同URL格式")
        print("   • 詳細的執行日誌")
    else:
        print("\n⚠️ 所有測試都失敗")
        print("💡 可能原因:")
        print("   • 網路連接問題")
        print("   • MOPS網站維護")
        print("   • 需要調整URL格式")
    
    return test1 or test2 or test3

if __name__ == "__main__":
    main()
