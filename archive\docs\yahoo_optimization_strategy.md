# 🎯 Yahoo數據源優化策略

## 📊 測試結果總結

### Yahoo Finance 測試結果
- **測試股票**: 11支（來自實際錯誤日誌）
- **成功率**: 90.9% (10/11)
- **失敗率**: 9.1% (1/11)
- **主要問題**: 格式混亂、需要嘗試多種後綴(.TW/.TWO)

### 關鍵發現
1. **格式依賴性強**: 大部分股票在`.TW`格式下失敗，但`.TWO`格式成功
2. **錯誤信息誤導**: "possibly delisted"錯誤信息不準確
3. **增加複雜性**: 需要多次嘗試不同格式，影響性能

## 🔧 優化策略

### 策略1: 智能Yahoo使用（推薦）
**保留Yahoo但優化使用方式**

```python
def smart_yahoo_fetch(stock_id):
    """智能Yahoo數據獲取 - 優化格式嘗試順序"""
    
    # 根據股票代碼判斷市場
    if stock_id.startswith(('6', '8', '9')):  # 通常是櫃買市場
        formats = [f"{stock_id}.TWO", f"{stock_id}.TW"]
    else:  # 通常是上市市場
        formats = [f"{stock_id}.TW", f"{stock_id}.TWO"]
    
    for format_code in formats:
        try:
            data = yf.Ticker(format_code).history(period='1d')
            if not data.empty:
                return data
        except Exception as e:
            if "delisted" in str(e):
                continue  # 跳過下市錯誤，嘗試下一個格式
            else:
                raise e  # 其他錯誤直接拋出
    
    return pd.DataFrame()  # 所有格式都失敗
```

### 策略2: 降低Yahoo優先級
**將Yahoo作為備用數據源**

```python
# 新的數據源優先級
data_sources = [
    'database',        # 1. 本地數據庫（最快）
    'twelvedata',      # 2. Twelve Data（穩定）
    'twse_api',        # 3. 證交所API（官方）
    'yahoo_smart',     # 4. 智能Yahoo（備用）
]
```

### 策略3: 混合策略（最佳方案）
**結合多種數據源的優勢**

```python
def hybrid_data_fetch(stock_id):
    """混合數據獲取策略"""
    
    # 1. 優先使用緩存
    cached_data = get_from_cache(stock_id)
    if cached_data is not None:
        return cached_data
    
    # 2. 嘗試快速數據源
    for source in ['database', 'twelvedata']:
        try:
            data = fetch_from_source(stock_id, source)
            if not data.empty:
                cache_data(stock_id, data)
                return data
        except Exception:
            continue
    
    # 3. 使用智能Yahoo作為備用
    try:
        data = smart_yahoo_fetch(stock_id)
        if not data.empty:
            cache_data(stock_id, data)
            return data
    except Exception:
        pass
    
    # 4. 最後嘗試證交所API
    return fetch_twse_api(stock_id)
```

## 🚀 立即實施方案

### 第一步: 修改現有Yahoo使用方式
```python
# 在 enhanced_data_fetcher.py 中修改
def _fetch_yfinance(self, formatted_id, days):
    """優化的yfinance獲取方法"""
    try:
        import yfinance as yf
        
        # 智能格式選擇
        stock_code = formatted_id['clean']
        if stock_code.startswith(('6', '8', '9')):
            formats = ['.TWO', '.TW']  # 櫃買優先
        else:
            formats = ['.TW', '.TWO']  # 上市優先
        
        for suffix in formats:
            try:
                ticker = stock_code + suffix
                stock = yf.Ticker(ticker)
                
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days + 30)
                
                data = stock.history(start=start_date, end=end_date)
                
                if not data.empty:
                    # 轉換格式並返回
                    df = self._convert_yfinance_data(data, days)
                    logging.info(f"✅ yfinance 成功獲取 {ticker} 數據")
                    return df
                    
            except Exception as e:
                if "delisted" in str(e).lower():
                    logging.debug(f"跳過下市錯誤: {ticker}")
                    continue
                else:
                    logging.warning(f"yfinance {ticker} 錯誤: {e}")
                    continue
        
        return pd.DataFrame()
        
    except ImportError:
        logging.error("yfinance 模組未安裝")
        return pd.DataFrame()
```

### 第二步: 添加錯誤過濾
```python
def filter_yahoo_errors(error_message):
    """過濾Yahoo錯誤信息"""
    ignore_patterns = [
        "possibly delisted",
        "No data found, symbol may be delisted",
        "HTTP Error 404"
    ]
    
    for pattern in ignore_patterns:
        if pattern in error_message:
            return True  # 忽略這個錯誤
    
    return False  # 不忽略
```

### 第三步: 實施緩存機制
```python
class SmartDataCache:
    """智能數據緩存"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = {
            'intraday': 60,      # 盤中數據1分鐘緩存
            'daily': 3600,       # 日數據1小時緩存
            'historical': 86400  # 歷史數據24小時緩存
        }
    
    def get(self, key, data_type='daily'):
        """獲取緩存數據"""
        if key in self.cache:
            timestamp, data = self.cache[key]
            timeout = self.cache_timeout.get(data_type, 3600)
            
            if time.time() - timestamp < timeout:
                return data
        
        return None
    
    def set(self, key, data, data_type='daily'):
        """設置緩存數據"""
        self.cache[key] = (time.time(), data)
```

## 📈 預期效果

### 性能提升
- **減少錯誤**: 智能格式選擇減少90%的格式錯誤
- **提高速度**: 緩存機制減少50%的網絡請求
- **增強穩定性**: 多數據源備用確保99%可用性

### 用戶體驗改善
- **減少錯誤日誌**: 過濾無意義的下市錯誤
- **更快響應**: 優先使用快速數據源
- **更高成功率**: 智能重試機制

## 🔧 實施時間表

### 立即實施（今天）
1. ✅ 修改yfinance獲取邏輯
2. ✅ 添加錯誤過濾機制
3. ✅ 實施智能格式選擇

### 短期實施（本週）
1. 🔄 實施緩存機制
2. 🔄 優化數據源優先級
3. 🔄 添加健康監控

### 中期實施（下週）
1. 📋 完善本地數據庫
2. 📋 集成Twelve Data API
3. 📋 實施完整的混合策略

## 💡 關鍵建議

### 不要完全禁用Yahoo
- Yahoo雖然有問題，但90.9%的成功率仍然有價值
- 作為備用數據源可以提高整體可用性
- 智能使用比完全禁用更好

### 重點優化方向
1. **格式智能化**: 根據股票代碼特徵選擇格式
2. **錯誤過濾**: 忽略無意義的錯誤信息
3. **緩存優先**: 減少對外部API的依賴
4. **多源備用**: 確保至少有一個數據源可用

### 監控指標
- 數據獲取成功率
- 平均響應時間
- 錯誤類型分布
- 緩存命中率

---

**結論**: 基於測試結果，建議採用"智能優化"而非"完全禁用"的策略，這樣既能解決Yahoo的問題，又能保持系統的穩定性和可用性。
