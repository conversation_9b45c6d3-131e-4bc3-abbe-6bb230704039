#!/usr/bin/env python3
"""
測試信號強度提示功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """創建測試數據"""
    # 生成6個月的測試數據
    dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
    dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 生成價格數據
    np.random.seed(42)
    base_price = 100
    prices = []
    
    for i in range(len(dates)):
        # 模擬價格波動
        if i == 0:
            price = base_price
        else:
            # 添加趨勢和隨機波動
            trend = 0.001 * np.sin(i * 0.1)  # 長期趨勢
            noise = np.random.normal(0, 0.02)  # 隨機波動
            price = prices[-1] * (1 + trend + noise)
        
        prices.append(price)
    
    # 生成OHLCV數據
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成開高低價
        volatility = 0.02
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
        
        # 確保價格邏輯正確
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # 生成成交量
        volume = np.random.randint(100000, 1000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df

def test_signal_strength_analyzer():
    """測試信號強度分析器"""
    print("🧪 測試信號強度分析器...")
    
    try:
        from signal_strength_analyzer import signal_analyzer
        
        # 創建測試數據
        test_data = create_test_data()
        
        # 添加RSI指標
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        test_data['RSI'] = calculate_rsi(test_data['close'])
        
        # 測試不同策略的信號強度分析
        strategies = ['RSI策略', 'MACD策略', '布林通道策略', '移動平均交叉策略']
        
        for strategy in strategies:
            print(f"\n📊 測試 {strategy}...")
            
            # 測試幾個隨機點
            test_indices = [50, 100, 150]
            
            for idx in test_indices:
                if idx < len(test_data):
                    # 測試買入信號
                    buy_text, buy_color = signal_analyzer.get_signal_strength_text(
                        strategy, test_data, idx, "買入"
                    )
                    print(f"   索引 {idx} 買入: {buy_text} (顏色: {buy_color})")
                    
                    # 測試賣出信號
                    sell_text, sell_color = signal_analyzer.get_signal_strength_text(
                        strategy, test_data, idx, "賣出"
                    )
                    print(f"   索引 {idx} 賣出: {sell_text} (顏色: {sell_color})")
        
        print("✅ 信號強度分析器測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 信號強度分析器測試失敗: {e}")
        return False

def test_ai_technical_backtest():
    """測試AI技術指標回測的信號強度功能"""
    print("\n🤖 測試AI技術指標回測信號強度功能...")
    
    try:
        from ai_technical_backtest import RSIStrategy, MACDStrategy, BollingerBandsStrategy, MovingAverageCrossStrategy
        
        # 創建測試數據
        test_data = create_test_data()
        
        # 測試各種策略
        strategies = [
            ('RSI策略', RSIStrategy()),
            ('MACD策略', MACDStrategy()),
            ('布林通道策略', BollingerBandsStrategy()),
            ('移動平均交叉策略', MovingAverageCrossStrategy())
        ]
        
        for strategy_name, strategy in strategies:
            print(f"\n📈 測試 {strategy_name}...")
            
            try:
                # 生成信號
                signals = strategy.generate_signals(test_data)
                
                # 檢查是否有信號強度信息
                if 'signal_text' in signals.columns:
                    # 找到有信號的點
                    buy_signals = signals[signals['signal'] == 1]
                    sell_signals = signals[signals['signal'] == -1]
                    
                    print(f"   買入信號數量: {len(buy_signals)}")
                    if len(buy_signals) > 0:
                        # 顯示前3個買入信號的強度文字
                        for i, (idx, row) in enumerate(buy_signals.head(3).iterrows()):
                            signal_text = row.get('signal_text', '無文字')
                            strength = row.get('signal_strength', 0)
                            print(f"     買入 {i+1}: {signal_text} (強度: {strength:.2f})")
                    
                    print(f"   賣出信號數量: {len(sell_signals)}")
                    if len(sell_signals) > 0:
                        # 顯示前3個賣出信號的強度文字
                        for i, (idx, row) in enumerate(sell_signals.head(3).iterrows()):
                            signal_text = row.get('signal_text', '無文字')
                            strength = row.get('signal_strength', 0)
                            print(f"     賣出 {i+1}: {signal_text} (強度: {strength:.2f})")
                else:
                    print(f"   ⚠️ {strategy_name} 沒有信號強度信息")
                
            except Exception as e:
                print(f"   ❌ {strategy_name} 測試失敗: {e}")
        
        print("✅ AI技術指標回測信號強度功能測試完成")
        return True
        
    except Exception as e:
        print(f"❌ AI技術指標回測測試失敗: {e}")
        return False

def test_main_gui_integration():
    """測試主GUI的信號強度整合"""
    print("\n🖥️ 測試主GUI信號強度整合...")
    
    try:
        # 檢查主GUI文件是否已更新
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否包含信號強度相關代碼
        if 'signal_strength_analyzer' in content:
            print("✅ 主GUI已整合信號強度分析器")
        else:
            print("⚠️ 主GUI尚未完全整合信號強度分析器")
        
        if 'signal_text' in content:
            print("✅ 主GUI已添加信號強度文字顯示")
        else:
            print("⚠️ 主GUI尚未添加信號強度文字顯示")
        
        return True
        
    except Exception as e:
        print(f"❌ 主GUI整合測試失敗: {e}")
        return False

def demonstrate_signal_strength_examples():
    """展示信號強度示例"""
    print("\n💡 信號強度提示示例:")
    print("=" * 50)
    
    examples = [
        ("RSI策略", [
            "🟢 嚴重超賣 - RSI < 20，強烈買入信號",
            "🟢 強力超賣 - RSI 20-25，較強買入信號", 
            "🟢 超賣反彈 - RSI 25-30，一般買入信號",
            "🔴 嚴重過熱 - RSI > 80，強烈賣出信號",
            "🔴 強力過熱 - RSI 75-80，較強賣出信號",
            "🔴 過熱回調 - RSI 70-75，一般賣出信號"
        ]),
        ("MACD策略", [
            "🟢 強勢金叉 - 零軸上方大幅金叉",
            "🟢 零軸金叉 - 零軸上方金叉",
            "🟢 急速金叉 - 快速交叉",
            "🔴 強勢死叉 - 零軸下方大幅死叉",
            "🔴 零軸死叉 - 零軸下方死叉",
            "🔴 急速死叉 - 快速交叉"
        ]),
        ("布林通道策略", [
            "🟢 跌破下軌 - 價格跌破下軌超過2%",
            "🟢 觸及下軌 - 價格觸及下軌",
            "🟢 下軌支撐 - 下軌提供支撐",
            "🔴 突破上軌 - 價格突破上軌超過2%",
            "🔴 觸及上軌 - 價格觸及上軌",
            "🔴 上軌阻力 - 上軌形成阻力"
        ]),
        ("移動平均交叉策略", [
            "🟢 強勢金叉 - 價格在均線上方，大幅金叉",
            "🟢 金叉放量 - 金叉伴隨成交量放大",
            "🟢 急速金叉 - 均線快速上升金叉",
            "🔴 強勢死叉 - 價格在均線下方，大幅死叉",
            "🔴 死叉放量 - 死叉伴隨成交量放大",
            "🔴 急速死叉 - 均線快速下降死叉"
        ])
    ]
    
    for strategy_name, signals in examples:
        print(f"\n📊 {strategy_name}:")
        for signal in signals:
            print(f"   {signal}")

def main():
    """主函數"""
    print("🚀 信號強度提示功能測試程式")
    print("=" * 60)
    
    # 展示示例
    demonstrate_signal_strength_examples()
    
    # 運行測試
    tests = [
        test_signal_strength_analyzer,
        test_ai_technical_backtest,
        test_main_gui_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試執行失敗: {e}")
            results.append(False)
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通過測試: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有測試通過！信號強度提示功能已成功實現")
        print("\n💡 新功能特色:")
        print("• 🎯 智能信號強度分析：根據技術指標數值和市場條件判斷信號強度")
        print("• 📝 直觀提示文字：如'嚴重超賣'、'強勢金叉'、'跌破下軌'等")
        print("• 🎨 顏色編碼：不同強度使用不同顏色提示")
        print("• 🔄 多策略支援：RSI、MACD、布林通道、移動平均交叉")
        print("• 📊 圖表整合：信號強度文字直接顯示在K線圖上")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    print("\n🚀 使用方式:")
    print("1. 啟動AI技術指標回測: python ai_technical_backtest.py")
    print("2. 啟動主程式: python O3mh_gui_v21_optimized.py")
    print("3. 查看圖表上的信號強度提示文字")

if __name__ == '__main__':
    main()
