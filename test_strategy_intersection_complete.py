#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試策略交集功能是否完整
驗證編譯後的執行檔中策略交集分析功能
"""

import sys
import os
import pandas as pd
import logging

def test_strategy_intersection_analyzer():
    """測試策略交集分析器是否可用"""
    print("🧪 測試策略交集分析器")
    print("=" * 50)
    
    try:
        # 測試導入策略交集分析器
        from strategy_intersection_analyzer import StrategyIntersectionAnalyzer
        print("✅ 策略交集分析器導入成功")
        
        # 創建分析器實例
        analyzer = StrategyIntersectionAnalyzer()
        print("✅ 策略交集分析器實例創建成功")
        
        # 創建測試數據
        test_strategies = {
            "CANSLIM量價齊升": pd.DataFrame({
                '股票代碼': ['2330', '2454', '3008', '2317', '2382'],
                '股票名稱': ['台積電', '聯發科', '大立光', '鴻海', '廣達'],
                '評分': [95, 88, 92, 85, 80]
            }),
            "藏獒": pd.DataFrame({
                '股票代碼': ['2330', '2317', '2412', '2881', '1301'],
                '股票名稱': ['台積電', '鴻海', '中華電', '富邦金', '台塑'],
                '動能分數': [92, 88, 85, 80, 75]
            }),
            "二次創高股票": pd.DataFrame({
                '股票代碼': ['2330', '2454', '2317', '6505', '3711'],
                '股票名稱': ['台積電', '聯發科', '鴻海', '台塑化', '日月光'],
                '創高強度': [95, 90, 85, 80, 75]
            }),
            "勝率73.45%": pd.DataFrame({
                '股票代碼': ['2330', '2382', '3008', '2327', '2449'],
                '股票名稱': ['台積電', '廣達', '大立光', '國巨', '京元電子'],
                '勝率分數': [94, 86, 91, 83, 88]
            }),
            "阿水一式": pd.DataFrame({
                '股票代碼': ['2330', '2317', '2454', '1590', '2603'],
                '股票名稱': ['台積電', '鴻海', '聯發科', '亞德客', '長榮'],
                '阿水分數': [96, 89, 87, 84, 82]
            })
        }
        
        # 添加策略結果到分析器
        print(f"\n📊 添加 {len(test_strategies)} 個測試策略:")
        for strategy_name, df in test_strategies.items():
            analyzer.add_strategy_result(strategy_name, df, '股票代碼')
            print(f"  ✅ {strategy_name}: {len(df)} 支股票")
        
        # 測試兩兩交集
        print(f"\n🎯 測試兩兩交集:")
        try:
            result = analyzer.calculate_intersection(["CANSLIM量價齊升", "藏獒"])
            intersection_stocks = result.get('intersection_stocks', set())
            print(f"  CANSLIM量價齊升 + 藏獒: {len(intersection_stocks)} 支共同股票")
            print(f"  共同股票: {', '.join(sorted(intersection_stocks))}")
        except Exception as e:
            print(f"  ❌ 兩兩交集測試失敗: {e}")
            return False
        
        # 測試三策略交集
        print(f"\n🎯 測試三策略交集:")
        try:
            result = analyzer.calculate_intersection(["CANSLIM量價齊升", "藏獒", "二次創高股票"])
            intersection_stocks = result.get('intersection_stocks', set())
            print(f"  三策略交集: {len(intersection_stocks)} 支共同股票")
            print(f"  共同股票: {', '.join(sorted(intersection_stocks))}")
        except Exception as e:
            print(f"  ❌ 三策略交集測試失敗: {e}")
            return False
        
        # 測試所有組合分析
        print(f"\n🔍 測試所有組合分析:")
        try:
            all_results = analyzer.analyze_all_combinations()
            top_combinations = analyzer.get_top_intersection_combinations()
            
            print(f"  ✅ 找到 {len(all_results)} 個組合")
            print(f"  ✅ 其中 {len(top_combinations)} 個有交集")
            
            # 顯示前5個最佳組合
            print(f"\n🏆 前5個最佳組合:")
            for i, (combo_name, count) in enumerate(top_combinations[:5], 1):
                print(f"    {i}. {combo_name}: {count} 支共同股票")
                if combo_name in all_results:
                    stocks = all_results[combo_name]['intersection_stocks']
                    print(f"       股票: {', '.join(sorted(stocks))}")
        except Exception as e:
            print(f"  ❌ 所有組合分析測試失敗: {e}")
            return False
        
        # 測試導出功能
        print(f"\n📁 測試導出功能:")
        try:
            filename = analyzer.export_intersection_results(result)
            if filename and os.path.exists(filename):
                print(f"  ✅ 導出成功: {filename}")
                # 檢查文件大小
                size = os.path.getsize(filename)
                print(f"  📊 文件大小: {size / 1024:.2f} KB")
            else:
                print(f"  ⚠️ 導出文件未找到")
        except Exception as e:
            print(f"  ❌ 導出功能測試失敗: {e}")
        
        print(f"\n✅ 策略交集分析器測試完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 策略交集分析器導入失敗: {e}")
        print("   這表示編譯時沒有包含 strategy_intersection_analyzer.py")
        return False
    except Exception as e:
        print(f"❌ 策略交集分析器測試失敗: {e}")
        return False

def test_main_gui_integration():
    """測試主程序GUI整合"""
    print(f"\n🖥️ 測試主程序GUI整合")
    print("=" * 50)
    
    try:
        # 這裡我們只能測試導入，不能實際運行GUI
        print("📝 檢查主程序文件...")
        
        if os.path.exists('O3mh_gui_v21_optimized.py'):
            print("✅ 主程序文件存在")
            
            # 檢查是否包含策略交集相關代碼
            with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            checks = [
                ('intersection_analyzer', '策略交集分析器'),
                ('calculate_strategy_intersection', '計算策略交集方法'),
                ('analyze_all_strategy_combinations', '分析所有組合方法'),
                ('export_intersection_results', '導出交集結果方法'),
                ('strategy_results_cache', '策略結果緩存')
            ]
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"  ✅ {description}: 已包含")
                else:
                    print(f"  ❌ {description}: 未找到")
                    
        else:
            print("❌ 主程序文件不存在")
            return False
            
        print(f"\n✅ 主程序GUI整合檢查完成！")
        return True
        
    except Exception as e:
        print(f"❌ 主程序GUI整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🔧 策略交集功能完整性測試")
    print("=" * 60)
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 測試結果
    results = []
    
    # 測試1: 策略交集分析器
    print("🧪 測試1: 策略交集分析器功能")
    result1 = test_strategy_intersection_analyzer()
    results.append(("策略交集分析器", result1))
    
    # 測試2: 主程序GUI整合
    print("🧪 測試2: 主程序GUI整合")
    result2 = test_main_gui_integration()
    results.append(("主程序GUI整合", result2))
    
    # 總結
    print(f"\n📋 測試結果總結")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通過" if passed else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 總體結果: {'✅ 全部通過' if all_passed else '❌ 有測試失敗'}")
    
    if all_passed:
        print(f"\n🎉 策略交集功能完整性驗證成功！")
        print(f"   編譯後的執行檔應該包含完整的策略交集分析功能")
    else:
        print(f"\n⚠️ 發現問題，需要修復後重新編譯")
    
    return all_passed

if __name__ == "__main__":
    main()
