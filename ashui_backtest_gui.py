#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿水一式回測系統GUI界面
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QTabWidget, QTableWidget, QTableWidgetItem,
                            QPushButton, QLabel, QDateEdit, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QProgressBar, QGroupBox, QGridLayout,
                            QMessageBox, QFileDialog, QComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QPixmap
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
try:
    import seaborn as sns
    sns.set_style("whitegrid")
except ImportError:
    print("⚠️ seaborn未安裝，將使用matplotlib默認樣式")
    sns = None

# 導入回測系統
from ashui_backtest_system import AshiuBacktestSystem
from ashui_regression_optimizer import AshiuRegressionOptimizer

class BacktestWorker(QThread):
    """回測工作線程"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, backtest_system, start_date, end_date):
        super().__init__()
        self.backtest_system = backtest_system
        self.start_date = start_date
        self.end_date = end_date
    
    def run(self):
        try:
            self.progress.emit(10)
            results = self.backtest_system.run_backtest(self.start_date, self.end_date)
            self.progress.emit(50)
            
            # 進行因子分析
            factor_analysis = self.backtest_system.analyze_factors()
            results['factor_analysis'] = factor_analysis
            self.progress.emit(80)
            
            # 迴歸優化
            optimizer = AshiuRegressionOptimizer()
            trades = [r for r in self.backtest_system.results if 'trade_return' in r]
            if trades:
                X, y = optimizer.prepare_data(self.backtest_system.results)
                model_results = optimizer.train_models(X, y)
                optimized_weights = optimizer.optimize_scoring_weights(X, y)
                
                results['regression_analysis'] = {
                    'model_results': model_results,
                    'optimized_weights': optimized_weights,
                    'optimizer': optimizer
                }
            
            self.progress.emit(100)
            self.finished.emit(results)
            
        except Exception as e:
            self.error.emit(str(e))

class MatplotlibWidget(QWidget):
    """Matplotlib圖表組件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        
        layout = QVBoxLayout()
        layout.addWidget(self.canvas)
        self.setLayout(layout)
    
    def plot_performance(self, results):
        """繪製績效圖表"""
        self.figure.clear()
        
        # 提取組合價值數據
        portfolio_data = [r for r in results['results'] if 'portfolio_value' in r]
        if not portfolio_data:
            return
        
        dates = [r['date'] for r in portfolio_data]
        values = [r['portfolio_value'] for r in portfolio_data]
        
        # 創建子圖
        ax1 = self.figure.add_subplot(2, 2, 1)
        ax2 = self.figure.add_subplot(2, 2, 2)
        ax3 = self.figure.add_subplot(2, 2, 3)
        ax4 = self.figure.add_subplot(2, 2, 4)
        
        # 1. 組合價值曲線
        ax1.plot(dates[::10], values[::10])  # 每10個點取一個，避免過密
        ax1.set_title('組合價值變化')
        ax1.set_ylabel('價值')
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 報酬分布
        trades = [r for r in results['results'] if 'trade_return' in r]
        if trades:
            returns = [t['trade_return'] for t in trades]
            ax2.hist(returns, bins=30, alpha=0.7, edgecolor='black')
            ax2.set_title('報酬率分布')
            ax2.set_xlabel('報酬率')
            ax2.set_ylabel('頻率')
        
        # 3. 績效指標
        perf = results['performance']
        metrics = ['勝率', '平均報酬', '夏普比率', '最大回撤']
        values_perf = [
            perf.get('win_rate', 0) * 100,
            perf.get('avg_return', 0) * 100,
            perf.get('sharpe_ratio', 0),
            perf.get('max_drawdown', 0) * 100
        ]
        
        bars = ax3.bar(metrics, values_perf)
        ax3.set_title('關鍵績效指標')
        ax3.set_ylabel('數值')
        
        # 為每個柱子添加數值標籤
        for bar, value in zip(bars, values_perf):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.2f}', ha='center', va='bottom')
        
        # 4. 因子相關性
        if 'factor_analysis' in results and 'correlations' in results['factor_analysis']:
            corr = results['factor_analysis']['correlations']
            factors = list(corr.keys())
            corr_values = list(corr.values())
            
            bars = ax4.bar(factors, corr_values)
            ax4.set_title('因子與報酬相關性')
            ax4.set_ylabel('相關係數')
            ax4.tick_params(axis='x', rotation=45)
            
            # 添加零線
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        self.figure.tight_layout()
        self.canvas.draw()

class AshiuBacktestGUI(QMainWindow):
    """阿水一式回測系統主界面"""
    
    def __init__(self):
        super().__init__()
        self.backtest_system = None
        self.results = None
        self.db_path = self.auto_detect_database()
        self.init_ui()

    def auto_detect_database(self):
        """自動檢測數據庫路徑，與主程序保持一致"""
        import os
        import json

        # 嘗試讀取主程序的配置文件
        config_file = "app_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    db_path = config.get('database', {}).get('price_db_path')
                    if db_path and os.path.exists(db_path):
                        return db_path
            except Exception as e:
                print(f"讀取配置文件失敗: {e}")

        # 使用與主程序相同的檢測邏輯
        auto_detect_paths = [
            "D:/Finlab/history/tables/price.db",  # 主程序默認路徑
            "./data/price_data.db",               # 回測系統默認路徑
            "./db/price.db",                      # 備用路徑
            "data/price_data.db"                  # 相對路徑
        ]

        for path in auto_detect_paths:
            if os.path.exists(path):
                print(f"🔍 自動檢測到數據庫: {path}")
                return path

        # 如果都不存在，返回主程序的默認路徑
        default_path = "D:/Finlab/history/tables/price.db"
        print(f"⚠️ 未找到現有數據庫，將使用默認路徑: {default_path}")
        return default_path

    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle('阿水一式策略回測系統')
        self.setGeometry(100, 100, 1400, 900)
        
        # 創建中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QVBoxLayout(central_widget)
        
        # 參數設置區域
        params_group = self.create_params_group()
        main_layout.addWidget(params_group)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 標籤頁
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 創建各個標籤頁
        self.create_tabs()

        # 更新數據庫狀態顯示
        self.update_database_status()

        # 狀態欄
        self.statusBar().showMessage('準備就緒')
    
    def create_params_group(self):
        """創建參數設置組"""
        group = QGroupBox('回測參數設置')
        layout = QGridLayout(group)
        
        # 日期範圍
        layout.addWidget(QLabel('開始日期:'), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate(2020, 1, 1))
        self.start_date_edit.setCalendarPopup(True)
        layout.addWidget(self.start_date_edit, 0, 1)
        
        layout.addWidget(QLabel('結束日期:'), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate(2024, 12, 31))
        self.end_date_edit.setCalendarPopup(True)
        layout.addWidget(self.end_date_edit, 0, 3)
        
        # 其他參數
        layout.addWidget(QLabel('持有天數:'), 1, 0)
        self.holding_period_spin = QSpinBox()
        self.holding_period_spin.setRange(1, 30)
        self.holding_period_spin.setValue(10)
        layout.addWidget(self.holding_period_spin, 1, 1)
        
        layout.addWidget(QLabel('最大持股數:'), 1, 2)
        self.max_positions_spin = QSpinBox()
        self.max_positions_spin.setRange(1, 20)
        self.max_positions_spin.setValue(10)
        layout.addWidget(self.max_positions_spin, 1, 3)
        
        layout.addWidget(QLabel('初始資金(萬):'), 2, 0)
        self.initial_capital_spin = QDoubleSpinBox()
        self.initial_capital_spin.setRange(10, 10000)
        self.initial_capital_spin.setValue(100)
        self.initial_capital_spin.setSuffix(' 萬')
        layout.addWidget(self.initial_capital_spin, 2, 1)
        
        # 數據庫信息顯示
        db_group = QGroupBox("數據庫信息")
        db_layout = QVBoxLayout(db_group)

        self.db_path_label = QLabel('路徑: 檢測中...')
        self.db_path_label.setWordWrap(True)
        db_layout.addWidget(self.db_path_label)

        self.db_status_label = QLabel('狀態: 自動檢測中...')
        self.db_status_label.setStyleSheet("color: blue; font-weight: bold;")
        db_layout.addWidget(self.db_status_label)

        self.db_info_label = QLabel('詳情: 等待檢測...')
        self.db_info_label.setWordWrap(True)
        db_layout.addWidget(self.db_info_label)

        layout.addWidget(db_group, 3, 0, 1, 4)
        
        # 按鈕區域
        button_layout = QHBoxLayout()

        # 執行按鈕
        self.run_btn = QPushButton('開始回測')
        self.run_btn.clicked.connect(self.run_backtest)
        self.run_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.run_btn)

        # 圖表解讀按鈕
        chart_help_btn = QPushButton('📖 圖表解讀')
        chart_help_btn.clicked.connect(self.show_chart_interpretation_guide)
        chart_help_btn.setStyleSheet("""
            QPushButton {
                background-color: #2E8B57;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #3CB371;
            }
        """)
        button_layout.addWidget(chart_help_btn)

        # 信號分析按鈕
        signal_analysis_btn = QPushButton('🔍 信號分析')
        signal_analysis_btn.clicked.connect(self.show_signal_analysis)
        signal_analysis_btn.setStyleSheet("""
            QPushButton {
                background-color: #4169E1;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #6495ED;
            }
        """)
        button_layout.addWidget(signal_analysis_btn)

        layout.addLayout(button_layout, 4, 0, 1, 4)
        
        return group
    
    def create_tabs(self):
        """創建標籤頁"""
        # 1. 績效總覽
        self.performance_tab = QWidget()
        self.tab_widget.addTab(self.performance_tab, '績效總覽')
        
        perf_layout = QVBoxLayout(self.performance_tab)
        self.performance_table = QTableWidget()
        perf_layout.addWidget(self.performance_table)
        
        # 2. 圖表分析
        self.chart_tab = QWidget()
        self.tab_widget.addTab(self.chart_tab, '圖表分析')
        
        chart_layout = QVBoxLayout(self.chart_tab)
        self.chart_widget = MatplotlibWidget()
        chart_layout.addWidget(self.chart_widget)
        
        # 3. 交易記錄
        self.trades_tab = QWidget()
        self.tab_widget.addTab(self.trades_tab, '交易記錄')
        
        trades_layout = QVBoxLayout(self.trades_tab)
        self.trades_table = QTableWidget()
        trades_layout.addWidget(self.trades_table)
        
        # 4. 因子分析
        self.factor_tab = QWidget()
        self.tab_widget.addTab(self.factor_tab, '因子分析')
        
        factor_layout = QVBoxLayout(self.factor_tab)
        self.factor_text = QTextEdit()
        self.factor_text.setReadOnly(True)
        factor_layout.addWidget(self.factor_text)
        
        # 5. 迴歸優化
        self.regression_tab = QWidget()
        self.tab_widget.addTab(self.regression_tab, '迴歸優化')
        
        regression_layout = QVBoxLayout(self.regression_tab)
        self.regression_text = QTextEdit()
        self.regression_text.setReadOnly(True)
        regression_layout.addWidget(self.regression_text)
        
        # 導出優化代碼按鈕
        self.export_code_btn = QPushButton('導出優化後的評分函數')
        self.export_code_btn.clicked.connect(self.export_optimized_code)
        self.export_code_btn.setEnabled(False)
        regression_layout.addWidget(self.export_code_btn)

    def update_database_status(self):
        """更新數據庫狀態顯示"""
        import os

        # 更新路徑顯示
        self.db_path_label.setText(f'路徑: {self.db_path}')

        if os.path.exists(self.db_path):
            # 檢查數據庫是否可以連接
            try:
                import sqlite3
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # 檢查所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # 尋找股價相關表格
                stock_tables = [t for t in tables if 'stock' in t.lower() or 'price' in t.lower()]

                if stock_tables:
                    table_name = stock_tables[0]

                    # 檢查表結構
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]

                    # 檢查數據量
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    total_count = cursor.fetchone()[0]

                    if total_count > 0:
                        # 檢查股票數量
                        stock_cols = [col for col in columns if 'stock' in col.lower() or 'code' in col.lower()]
                        if stock_cols:
                            stock_col = stock_cols[0]
                            cursor.execute(f"SELECT COUNT(DISTINCT {stock_col}) FROM {table_name}")
                            stock_count = cursor.fetchone()[0]
                        else:
                            stock_count = "未知"

                        # 檢查日期範圍
                        date_cols = [col for col in columns if 'date' in col.lower()]
                        if date_cols:
                            date_col = date_cols[0]
                            cursor.execute(f"SELECT MIN({date_col}), MAX({date_col}) FROM {table_name}")
                            min_date, max_date = cursor.fetchone()
                            date_range = f"{min_date} 到 {max_date}"
                        else:
                            date_range = "未知"

                        self.db_status_label.setText('✅ 連接成功')
                        self.db_status_label.setStyleSheet("color: green; font-weight: bold;")

                        self.db_info_label.setText(
                            f'表格: {table_name} | '
                            f'股票數: {stock_count} | '
                            f'記錄數: {total_count:,} | '
                            f'日期範圍: {date_range}'
                        )

                    else:
                        self.db_status_label.setText('⚠️ 數據庫為空')
                        self.db_status_label.setStyleSheet("color: orange; font-weight: bold;")
                        self.db_info_label.setText(f'表格: {table_name} | 無數據')

                else:
                    self.db_status_label.setText('❌ 無股價數據表')
                    self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
                    self.db_info_label.setText(f'可用表格: {", ".join(tables) if tables else "無"}')

                conn.close()

            except Exception as e:
                self.db_status_label.setText(f'❌ 連接失敗')
                self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
                self.db_info_label.setText(f'錯誤: {str(e)}')
        else:
            self.db_status_label.setText('❌ 文件不存在')
            self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.db_info_label.setText('請檢查數據庫路徑是否正確')

        # 更新狀態欄
        db_name = os.path.basename(self.db_path)
        self.statusBar().showMessage(f'數據庫: {db_name}')
    
    def run_backtest(self):
        """執行回測"""
        try:
            # 獲取參數
            start_date = self.start_date_edit.date().toString('yyyy-MM-dd')
            end_date = self.end_date_edit.date().toString('yyyy-MM-dd')
            
            # 初始化回測系統
            self.backtest_system = AshiuBacktestSystem(self.db_path)
            
            # 設置參數
            self.backtest_system.holding_period = self.holding_period_spin.value()
            self.backtest_system.max_positions = self.max_positions_spin.value()
            self.backtest_system.initial_capital = self.initial_capital_spin.value() * 10000
            
            # 顯示進度條
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.run_btn.setEnabled(False)
            
            # 創建工作線程
            self.worker = BacktestWorker(self.backtest_system, start_date, end_date)
            self.worker.progress.connect(self.progress_bar.setValue)
            self.worker.finished.connect(self.on_backtest_finished)
            self.worker.error.connect(self.on_backtest_error)
            self.worker.start()
            
            self.statusBar().showMessage('回測進行中...')
            
        except Exception as e:
            QMessageBox.critical(self, '錯誤', f'回測啟動失敗: {str(e)}')
    
    def on_backtest_finished(self, results):
        """回測完成處理"""
        self.results = results
        self.progress_bar.setVisible(False)
        self.run_btn.setEnabled(True)
        
        # 更新各個標籤頁
        self.update_performance_tab()
        self.update_chart_tab()
        self.update_trades_tab()
        self.update_factor_tab()
        self.update_regression_tab()
        
        self.statusBar().showMessage('回測完成')
        QMessageBox.information(self, '完成', '回測分析完成！')
    
    def on_backtest_error(self, error_msg):
        """回測錯誤處理"""
        self.progress_bar.setVisible(False)
        self.run_btn.setEnabled(True)
        self.statusBar().showMessage('回測失敗')
        QMessageBox.critical(self, '錯誤', f'回測失敗: {error_msg}')

    def update_performance_tab(self):
        """更新績效總覽標籤頁"""
        if not self.results or 'performance' not in self.results:
            return

        perf = self.results['performance']

        # 設置表格
        self.performance_table.setRowCount(10)
        self.performance_table.setColumnCount(2)
        self.performance_table.setHorizontalHeaderLabels(['指標', '數值'])

        # 填充數據
        metrics = [
            ('總交易次數', f"{perf.get('total_trades', 0)}"),
            ('獲利交易', f"{perf.get('winning_trades', 0)}"),
            ('虧損交易', f"{perf.get('losing_trades', 0)}"),
            ('勝率', f"{perf.get('win_rate', 0)*100:.2f}%"),
            ('平均報酬', f"{perf.get('avg_return', 0)*100:.2f}%"),
            ('累積報酬', f"{perf.get('cumulative_return', 0)*100:.2f}%"),
            ('報酬標準差', f"{perf.get('std_return', 0)*100:.2f}%"),
            ('夏普比率', f"{perf.get('sharpe_ratio', 0):.4f}"),
            ('最大回撤', f"{perf.get('max_drawdown', 0)*100:.2f}%"),
            ('最佳交易', f"{perf.get('best_trade', 0)*100:.2f}%"),
        ]

        for i, (metric, value) in enumerate(metrics):
            self.performance_table.setItem(i, 0, QTableWidgetItem(metric))
            self.performance_table.setItem(i, 1, QTableWidgetItem(value))

        self.performance_table.resizeColumnsToContents()

    def update_chart_tab(self):
        """更新圖表分析標籤頁"""
        if not self.results:
            return

        self.chart_widget.plot_performance(self.results)

    def update_trades_tab(self):
        """更新交易記錄標籤頁"""
        if not self.results:
            return

        trades = [r for r in self.results['results'] if 'trade_return' in r]

        if not trades:
            return

        # 設置表格
        self.trades_table.setRowCount(len(trades))
        self.trades_table.setColumnCount(8)
        self.trades_table.setHorizontalHeaderLabels([
            '股票代碼', '進場日期', '出場日期', '進場價格', '出場價格',
            '報酬率', '評分', '成交金額'
        ])

        # 填充數據
        for i, trade in enumerate(trades):
            self.trades_table.setItem(i, 0, QTableWidgetItem(trade['stock_id']))
            self.trades_table.setItem(i, 1, QTableWidgetItem(trade['entry_date']))
            self.trades_table.setItem(i, 2, QTableWidgetItem(trade['exit_date']))
            self.trades_table.setItem(i, 3, QTableWidgetItem(f"{trade['entry_price']:.2f}"))
            self.trades_table.setItem(i, 4, QTableWidgetItem(f"{trade['exit_price']:.2f}"))
            self.trades_table.setItem(i, 5, QTableWidgetItem(f"{trade['trade_return']*100:.2f}%"))
            self.trades_table.setItem(i, 6, QTableWidgetItem(f"{trade['score']:.1f}"))

            turnover = trade['indicators'].get('turnover', 0)
            self.trades_table.setItem(i, 7, QTableWidgetItem(f"{turnover:.0f}萬"))

        self.trades_table.resizeColumnsToContents()

    def update_factor_tab(self):
        """更新因子分析標籤頁"""
        if not self.results or 'factor_analysis' not in self.results:
            return

        factor_analysis = self.results['factor_analysis']

        report = "# 因子分析報告\n\n"

        # 相關性分析
        if 'correlations' in factor_analysis:
            report += "## 因子與報酬相關性\n\n"
            for factor, corr in factor_analysis['correlations'].items():
                report += f"- {factor}: {corr:.4f}\n"
            report += "\n"

        # 評分組別分析
        if 'score_groups' in factor_analysis:
            report += "## 評分組別表現\n\n"
            for group, stats in factor_analysis['score_groups'].items():
                report += f"### {group}\n"
                report += f"- 交易次數: {stats['count']}\n"
                report += f"- 平均報酬: {stats['avg_return']*100:.2f}%\n"
                report += f"- 勝率: {stats['win_rate']*100:.2f}%\n"
                report += f"- 報酬標準差: {stats['std_return']*100:.2f}%\n\n"

        # 因子統計
        if 'factor_stats' in factor_analysis:
            report += "## 因子統計數據\n\n"
            for factor, stats in factor_analysis['factor_stats'].items():
                report += f"### {factor}\n"
                report += f"- 平均值: {stats['mean']:.4f}\n"
                report += f"- 標準差: {stats['std']:.4f}\n"
                report += f"- 最小值: {stats['min']:.4f}\n"
                report += f"- 最大值: {stats['max']:.4f}\n"
                report += f"- 中位數: {stats['median']:.4f}\n\n"

        self.factor_text.setPlainText(report)

    def update_regression_tab(self):
        """更新迴歸優化標籤頁"""
        if not self.results or 'regression_analysis' not in self.results:
            return

        regression = self.results['regression_analysis']

        report = "# 迴歸分析與評分優化報告\n\n"

        # 模型比較
        if 'model_results' in regression:
            report += "## 模型比較結果\n\n"
            for name, result in regression['model_results'].items():
                report += f"### {name.upper()} 模型\n"
                report += f"- 交叉驗證 R²: {result['cv_mean']:.4f} ± {result['cv_std']:.4f}\n"
                report += f"- 訓練集 R²: {result['r2']:.4f}\n"
                report += f"- 均方誤差: {result['mse']:.6f}\n\n"

        # 優化權重
        if 'optimized_weights' in regression:
            report += "## 優化後的評分權重\n\n"
            weights = regression['optimized_weights']
            total_weight = sum(weights.values())

            report += "| 因子 | 權重 | 百分比 |\n"
            report += "|------|------|--------|\n"
            for factor, weight in weights.items():
                percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
                report += f"| {factor} | {weight:.1f}分 | {percentage:.1f}% |\n"
            report += "\n"

        # 建議
        report += "## 優化建議\n\n"
        report += "1. 使用優化後的權重更新評分函數\n"
        report += "2. 定期重新進行回測驗證模型效果\n"
        report += "3. 考慮加入更多技術指標作為特徵\n"
        report += "4. 監控模型在新數據上的表現\n"
        report += "5. 可以考慮使用機器學習模型進一步優化\n\n"

        # 生成優化代碼
        if 'optimizer' in regression:
            optimizer = regression['optimizer']
            optimized_code = optimizer.generate_optimized_scoring_function()
            report += "## 優化後的評分函數代碼\n\n"
            report += "```python\n"
            report += optimized_code
            report += "\n```\n"

        self.regression_text.setPlainText(report)
        self.export_code_btn.setEnabled(True)

    def show_chart_interpretation_guide(self):
        """顯示圖表解讀指南"""
        dialog = QDialog(self)
        dialog.setWindowTitle('📖 技術指標策略解讀指南')
        dialog.setModal(True)
        dialog.resize(1000, 800)

        layout = QVBoxLayout(dialog)

        # 策略選擇區域
        strategy_layout = QHBoxLayout()
        strategy_layout.addWidget(QLabel('選擇策略:'))

        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            'RSI策略', 'MACD策略', '布林通道策略', '移動平均交叉策略'
        ])
        self.strategy_combo.currentTextChanged.connect(self.update_strategy_content)
        strategy_layout.addWidget(self.strategy_combo)

        strategy_layout.addStretch()
        layout.addLayout(strategy_layout)

        # 創建標籤頁
        self.strategy_tab_widget = QTabWidget()
        layout.addWidget(self.strategy_tab_widget)

        # 初始化內容
        self.update_strategy_content('RSI策略')

        # 關閉按鈕
        close_btn = QPushButton('關閉')
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        dialog.exec()

    def update_strategy_content(self, strategy_name):
        """更新策略內容"""
        # 清除現有標籤頁
        self.strategy_tab_widget.clear()

        # 基本概念標籤頁
        basic_tab = QWidget()
        self.strategy_tab_widget.addTab(basic_tab, '📚 基本概念')

        basic_layout = QVBoxLayout(basic_tab)
        basic_text = QTextEdit()
        basic_text.setReadOnly(True)
        basic_text.setHtml(self.get_strategy_basic_concepts_html(strategy_name))
        basic_layout.addWidget(basic_text)

        # 信號解讀標籤頁
        signal_tab = QWidget()
        self.strategy_tab_widget.addTab(signal_tab, '🎯 信號解讀')

        signal_layout = QVBoxLayout(signal_tab)
        signal_text = QTextEdit()
        signal_text.setReadOnly(True)
        signal_text.setHtml(self.get_strategy_signal_interpretation_html(strategy_name))
        signal_layout.addWidget(signal_text)

        # 實戰應用標籤頁
        practical_tab = QWidget()
        self.strategy_tab_widget.addTab(practical_tab, '💡 實戰應用')

        practical_layout = QVBoxLayout(practical_tab)
        practical_text = QTextEdit()
        practical_text.setReadOnly(True)
        practical_text.setHtml(self.get_strategy_practical_application_html(strategy_name))
        practical_layout.addWidget(practical_text)

    def show_signal_analysis(self):
        """顯示當前回測的信號分析"""
        if not hasattr(self, 'results') or not self.results:
            QMessageBox.warning(self, '警告', '請先執行回測分析')
            return

        dialog = QDialog(self)
        dialog.setWindowTitle('🔍 信號分析報告')
        dialog.setModal(True)
        dialog.resize(800, 600)

        layout = QVBoxLayout(dialog)

        # 分析結果文本
        analysis_text = QTextEdit()
        analysis_text.setReadOnly(True)
        analysis_text.setHtml(self.generate_signal_analysis_html())
        layout.addWidget(analysis_text)

        # 關閉按鈕
        close_btn = QPushButton('關閉')
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        dialog.exec()

    def export_optimized_code(self):
        """導出優化後的評分函數代碼"""
        if not self.results or 'regression_analysis' not in self.results:
            return

        regression = self.results['regression_analysis']
        if 'optimizer' not in regression:
            return

        optimizer = regression['optimizer']
        optimized_code = optimizer.generate_optimized_scoring_function()

        # 選擇保存路徑
        file_path, _ = QFileDialog.getSaveFileName(
            self, '保存優化後的評分函數', 'optimized_ashui_score.py',
            'Python files (*.py);;All files (*)')

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('#!/usr/bin/env python3\n')
                    f.write('# -*- coding: utf-8 -*-\n')
                    f.write('"""\n')
                    f.write('阿水一式優化後的評分函數\n')
                    f.write('基於回測數據的迴歸分析結果生成\n')
                    f.write('"""\n\n')
                    f.write('from typing import Dict\n\n')
                    f.write(optimized_code)

                QMessageBox.information(self, '成功', f'優化後的評分函數已保存到: {file_path}')

            except Exception as e:
                QMessageBox.critical(self, '錯誤', f'保存失敗: {str(e)}')

    def get_strategy_basic_concepts_html(self, strategy_name):
        """獲取特定策略的基本概念HTML內容"""

        if strategy_name == 'RSI策略':
            return """
            <h2>📚 RSI策略基本概念</h2>

            <h3>🎯 什麼是RSI？</h3>
            <p><strong>相對強弱指標（RSI）</strong>是衡量價格變動速度和幅度的動量振盪器，數值範圍在0-100之間。</p>

            <h3>📊 RSI的計算原理</h3>
            <ul>
                <li><strong>計算期間</strong>：通常使用14天</li>
                <li><strong>上漲平均</strong>：期間內上漲日的平均漲幅</li>
                <li><strong>下跌平均</strong>：期間內下跌日的平均跌幅</li>
                <li><strong>RSI公式</strong>：RSI = 100 - (100 / (1 + RS))，其中RS = 上漲平均/下跌平均</li>
            </ul>

            <h3>🎨 圖表中的關鍵元素</h3>
            <ul>
                <li><strong>🟢 綠色三角（買入信號）</strong>：RSI從超賣區域（<30）回升時</li>
                <li><strong>🔴 紅色三角（賣出信號）</strong>：RSI進入超買區域（>70）時</li>
                <li><strong>📈 RSI曲線</strong>：顯示動量變化</li>
                <li><strong>📏 30/70水平線</strong>：超賣/超買的參考線</li>
            </ul>

            <h3>⚠️ RSI策略特性</h3>
            <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107;">
                <p><strong>震盪指標特性</strong></p>
                <ul>
                    <li>在趨勢市場中可能產生假信號</li>
                    <li>適合震盪整理的市場環境</li>
                    <li>需要結合趨勢分析使用</li>
                </ul>
            </div>
            """

        elif strategy_name == 'MACD策略':
            return """
            <h2>📚 MACD策略基本概念</h2>

            <h3>🎯 什麼是MACD？</h3>
            <p><strong>指數平滑異同移動平均線（MACD）</strong>是趨勢跟隨型指標，由快線、慢線和柱狀圖組成。</p>

            <h3>📊 MACD的組成要素</h3>
            <ul>
                <li><strong>MACD線（快線）</strong>：12日EMA - 26日EMA</li>
                <li><strong>信號線（慢線）</strong>：MACD線的9日EMA</li>
                <li><strong>柱狀圖</strong>：MACD線 - 信號線</li>
                <li><strong>零軸</strong>：多空分界線</li>
            </ul>

            <h3>🎨 圖表中的關鍵元素</h3>
            <ul>
                <li><strong>🟢 綠色三角（買入信號）</strong>：MACD線上穿信號線（金叉）</li>
                <li><strong>🔴 紅色三角（賣出信號）</strong>：MACD線下穿信號線（死叉）</li>
                <li><strong>📈 MACD雙線</strong>：快線和慢線的交叉</li>
                <li><strong>📊 柱狀圖</strong>：動能強弱的視覺化</li>
            </ul>

            <h3>⚠️ MACD策略特性</h3>
            <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745;">
                <p><strong>趨勢跟隨特性</strong></p>
                <ul>
                    <li>適合趨勢明確的市場</li>
                    <li>在震盪市場中容易產生假信號</li>
                    <li>信號相對滯後但較為可靠</li>
                </ul>
            </div>
            """

        elif strategy_name == '布林通道策略':
            return """
            <h2>📚 布林通道策略基本概念</h2>

            <h3>🎯 什麼是布林通道？</h3>
            <p><strong>布林通道（Bollinger Bands）</strong>由移動平均線和標準差組成的通道，用於判斷價格的相對高低。</p>

            <h3>📊 布林通道的組成</h3>
            <ul>
                <li><strong>中軌</strong>：20日簡單移動平均線</li>
                <li><strong>上軌</strong>：中軌 + 2倍標準差</li>
                <li><strong>下軌</strong>：中軌 - 2倍標準差</li>
                <li><strong>通道寬度</strong>：反映市場波動性</li>
            </ul>

            <h3>🎨 圖表中的關鍵元素</h3>
            <ul>
                <li><strong>🟢 綠色三角（買入信號）</strong>：價格觸及下軌後反彈</li>
                <li><strong>🔴 紅色三角（賣出信號）</strong>：價格觸及上軌後回落</li>
                <li><strong>📈 三條軌道線</strong>：上軌、中軌、下軌</li>
                <li><strong>🌊 通道寬窄</strong>：市場波動性的指標</li>
            </ul>

            <h3>⚠️ 布林通道策略特性</h3>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; border-left: 4px solid #17a2b8;">
                <p><strong>均值回歸特性</strong></p>
                <ul>
                    <li>假設價格會回歸到平均值</li>
                    <li>適合震盪整理的市場</li>
                    <li>在強趨勢中可能失效</li>
                </ul>
            </div>
            """

        elif strategy_name == '移動平均交叉策略':
            return """
            <h2>📚 移動平均交叉策略基本概念</h2>

            <h3>🎯 什麼是移動平均交叉？</h3>
            <p><strong>移動平均交叉策略</strong>使用兩條不同週期的移動平均線，通過它們的交叉來產生買賣信號。</p>

            <h3>📊 雙均線系統</h3>
            <ul>
                <li><strong>短期均線</strong>：通常使用10日移動平均</li>
                <li><strong>長期均線</strong>：通常使用30日移動平均</li>
                <li><strong>金叉</strong>：短期均線上穿長期均線</li>
                <li><strong>死叉</strong>：短期均線下穿長期均線</li>
            </ul>

            <h3>🎨 圖表中的關鍵元素</h3>
            <ul>
                <li><strong>🟢 綠色三角（買入信號）</strong>：短期均線上穿長期均線（金叉）</li>
                <li><strong>🔴 紅色三角（賣出信號）</strong>：短期均線下穿長期均線（死叉）</li>
                <li><strong>📈 雙均線</strong>：短期和長期移動平均線</li>
                <li><strong>🔄 交叉點</strong>：信號產生的關鍵位置</li>
            </ul>

            <h3>⚠️ 移動平均策略特性</h3>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; border-left: 4px solid #dc3545;">
                <p><strong>趨勢跟隨特性</strong></p>
                <ul>
                    <li>信號相對滯後但較為穩定</li>
                    <li>適合中長期趨勢交易</li>
                    <li>在震盪市場中容易產生假信號</li>
                </ul>
            </div>
            """

        return "<p>策略資訊載入中...</p>"

    def get_strategy_signal_interpretation_html(self, strategy_name):
        """獲取特定策略的信號解讀HTML內容"""

        if strategy_name == 'RSI策略':
            return """
            <h2>🎯 RSI策略信號解讀詳解</h2>

            <h3>🟢 買入信號的含義</h3>
            <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>RSI策略中的買入信號：</strong></p>
                <ul>
                    <li>RSI從超賣區域（通常<30）回升</li>
                    <li>表示短期下跌動能減弱</li>
                    <li>可能出現技術性反彈</li>
                    <li>適合短期交易操作</li>
                </ul>
            </div>

            <h3>🔴 賣出信號的含義</h3>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>RSI策略中的賣出信號：</strong></p>
                <ul>
                    <li>RSI進入超買區域（通常>70）</li>
                    <li>表示短期上漲動能過熱</li>
                    <li>可能面臨技術性回調</li>
                    <li>適合獲利了結操作</li>
                </ul>
            </div>

            <h3>🤔 常見問題解析</h3>
            <div style="background-color: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h4>為什麼會有多個買入信號？</h4>
                <p><strong>例：第一個綠三角1000元，最後一個800元</strong></p>
                <ul>
                    <li>每個信號都是基於當時的RSI狀態</li>
                    <li>1000元時：短期超賣反彈機會</li>
                    <li>800元時：更深度超賣的反彈機會</li>
                    <li><strong>關鍵</strong>：這是獨立的短期交易信號</li>
                </ul>

                <h4>為什麼賣出信號會持續？</h4>
                <p><strong>例：950元賣出信號，1100元仍有賣出信號</strong></p>
                <ul>
                    <li>950元時：RSI剛進入超買區</li>
                    <li>1100元時：RSI可能仍在超買區</li>
                    <li><strong>重點</strong>：RSI策略關注短期動能，不是長期趨勢</li>
                </ul>
            </div>
            """

        elif strategy_name == 'MACD策略':
            return """
            <h2>🎯 MACD策略信號解讀詳解</h2>

            <h3>🟢 買入信號（金叉）</h3>
            <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>MACD線上穿信號線時：</strong></p>
                <ul>
                    <li>短期動能開始超越長期動能</li>
                    <li>表示上升趨勢可能開始</li>
                    <li>適合趨勢跟隨操作</li>
                    <li>在零軸上方的金叉更為可靠</li>
                </ul>
            </div>

            <h3>🔴 賣出信號（死叉）</h3>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>MACD線下穿信號線時：</strong></p>
                <ul>
                    <li>短期動能開始弱於長期動能</li>
                    <li>表示下降趨勢可能開始</li>
                    <li>適合趨勢轉換操作</li>
                    <li>在零軸下方的死叉更為可靠</li>
                </ul>
            </div>

            <h3>🔍 信號強度判斷</h3>
            <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ul>
                    <li><strong>零軸位置</strong>：零軸上方的信號通常更強</li>
                    <li><strong>柱狀圖</strong>：柱狀圖的變化預示交叉</li>
                    <li><strong>背離現象</strong>：價格與MACD的背離值得關注</li>
                    <li><strong>交叉角度</strong>：急劇交叉比緩慢交叉更可靠</li>
                </ul>
            </div>
            """

        elif strategy_name == '布林通道策略':
            return """
            <h2>🎯 布林通道策略信號解讀詳解</h2>

            <h3>🟢 買入信號（下軌反彈）</h3>
            <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>價格觸及下軌後反彈：</strong></p>
                <ul>
                    <li>價格被認為過度下跌</li>
                    <li>下軌提供支撐作用</li>
                    <li>期待價格回歸中軌</li>
                    <li>適合逆勢反彈操作</li>
                </ul>
            </div>

            <h3>🔴 賣出信號（上軌回落）</h3>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>價格觸及上軌後回落：</strong></p>
                <ul>
                    <li>價格被認為過度上漲</li>
                    <li>上軌提供阻力作用</li>
                    <li>期待價格回歸中軌</li>
                    <li>適合高點獲利了結</li>
                </ul>
            </div>

            <h3>🌊 通道特性分析</h3>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ul>
                    <li><strong>通道收縮</strong>：波動性降低，可能醞釀大行情</li>
                    <li><strong>通道擴張</strong>：波動性增加，趨勢可能形成</li>
                    <li><strong>中軌作用</strong>：動態支撐或阻力線</li>
                    <li><strong>突破信號</strong>：價格突破通道可能是趨勢開始</li>
                </ul>
            </div>
            """

        elif strategy_name == '移動平均交叉策略':
            return """
            <h2>🎯 移動平均交叉策略信號解讀詳解</h2>

            <h3>🟢 買入信號（金叉）</h3>
            <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>短期均線上穿長期均線：</strong></p>
                <ul>
                    <li>短期價格動能超越長期動能</li>
                    <li>表示上升趨勢可能開始</li>
                    <li>適合趨勢跟隨操作</li>
                    <li>信號相對滯後但較為可靠</li>
                </ul>
            </div>

            <h3>🔴 賣出信號（死叉）</h3>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <p><strong>短期均線下穿長期均線：</strong></p>
                <ul>
                    <li>短期價格動能弱於長期動能</li>
                    <li>表示下降趨勢可能開始</li>
                    <li>適合趨勢轉換操作</li>
                    <li>應及時止損或減倉</li>
                </ul>
            </div>

            <h3>📏 均線系統分析</h3>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ul>
                    <li><strong>均線排列</strong>：多頭排列（短>長）看漲，空頭排列看跌</li>
                    <li><strong>價格位置</strong>：價格在均線上方通常較強勢</li>
                    <li><strong>交叉角度</strong>：急劇交叉比緩慢交叉信號更強</li>
                    <li><strong>假突破</strong>：短暫交叉後又回到原位置需謹慎</li>
                </ul>
            </div>
            """

        return "<p>信號解讀載入中...</p>"

    def get_strategy_practical_application_html(self, strategy_name):
        """獲取特定策略的實戰應用HTML內容"""

        if strategy_name == 'RSI策略':
            return """
            <h2>💡 RSI策略實戰應用指南</h2>

            <h3>🎯 RSI策略操作要點</h3>

            <h4>📈 買入信號操作建議：</h4>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>確認大趨勢</strong>：RSI買入信號在上升趨勢中更有效</li>
                    <li><strong>分批進場</strong>：RSI<30時可分批買入，不要一次全倉</li>
                    <li><strong>設定停損</strong>：買入後設定5-8%停損點</li>
                    <li><strong>短期操作</strong>：RSI適合3-10天的短期交易</li>
                </ol>
            </div>

            <h4>📉 賣出信號操作建議：</h4>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>分批出場</strong>：RSI>70時可分批賣出</li>
                    <li><strong>保留核心</strong>：如果基本面良好，可保留部分持股</li>
                    <li><strong>快速決策</strong>：RSI信號時效性強，需快速執行</li>
                    <li><strong>避免貪婪</strong>：超買區域要有獲利了結的紀律</li>
                </ol>
            </div>

            <h3>⚖️ RSI策略風險控制</h3>
            <ul>
                <li><strong>趨勢過濾</strong>：在強勢下跌趨勢中避免接刀</li>
                <li><strong>成交量確認</strong>：配合成交量變化確認信號</li>
                <li><strong>時間停損</strong>：持有超過預期時間要考慮出場</li>
                <li><strong>資金比例</strong>：單次RSI交易不超過總資金20%</li>
            </ul>
            """

        elif strategy_name == 'MACD策略':
            return """
            <h2>💡 MACD策略實戰應用指南</h2>

            <h3>🎯 MACD策略操作要點</h3>

            <h4>📈 金叉買入操作：</h4>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>零軸位置</strong>：零軸上方的金叉更可靠</li>
                    <li><strong>柱狀圖確認</strong>：柱狀圖由負轉正時進場</li>
                    <li><strong>趨勢跟隨</strong>：適合中期趨勢跟隨操作</li>
                    <li><strong>分批建倉</strong>：金叉後可分2-3次建倉</li>
                </ol>
            </div>

            <h4>📉 死叉賣出操作：</h4>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>及時止損</strong>：死叉出現要及時減倉</li>
                    <li><strong>柱狀圖預警</strong>：柱狀圖縮短時要提高警覺</li>
                    <li><strong>趨勢確認</strong>：零軸下方的死叉要更謹慎</li>
                    <li><strong>分批出場</strong>：可分批賣出降低風險</li>
                </ol>
            </div>

            <h3>⚖️ MACD策略風險控制</h3>
            <ul>
                <li><strong>避免震盪</strong>：在震盪市場中減少MACD交易</li>
                <li><strong>背離警示</strong>：注意價格與MACD的背離現象</li>
                <li><strong>持有週期</strong>：MACD適合中期持有（2-8週）</li>
                <li><strong>止損設定</strong>：死叉後設定10-15%止損</li>
            </ul>
            """

        elif strategy_name == '布林通道策略':
            return """
            <h2>💡 布林通道策略實戰應用指南</h2>

            <h3>🎯 布林通道策略操作要點</h3>

            <h4>📈 下軌買入操作：</h4>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>觸底反彈</strong>：價格觸及下軌後出現反彈跡象</li>
                    <li><strong>成交量配合</strong>：下軌附近出現放量更可靠</li>
                    <li><strong>分批進場</strong>：可在下軌附近分批買入</li>
                    <li><strong>目標中軌</strong>：以中軌作為初步獲利目標</li>
                </ol>
            </div>

            <h4>📉 上軌賣出操作：</h4>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>高點獲利</strong>：價格觸及上軌時考慮獲利了結</li>
                    <li><strong>量價背離</strong>：上軌附近出現量價背離要警惕</li>
                    <li><strong>分批出場</strong>：可在上軌附近分批賣出</li>
                    <li><strong>保留底倉</strong>：強勢股可保留部分底倉</li>
                </ol>
            </div>

            <h3>⚖️ 布林通道策略風險控制</h3>
            <ul>
                <li><strong>通道突破</strong>：價格突破通道要調整策略</li>
                <li><strong>趨勢判斷</strong>：強趨勢中布林策略可能失效</li>
                <li><strong>通道寬度</strong>：通道過窄時要謹慎操作</li>
                <li><strong>中軌支撐</strong>：跌破中軌要考慮止損</li>
            </ul>
            """

        elif strategy_name == '移動平均交叉策略':
            return """
            <h2>💡 移動平均交叉策略實戰應用指南</h2>

            <h3>🎯 移動平均交叉策略操作要點</h3>

            <h4>📈 金叉買入操作：</h4>
            <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>確認金叉</strong>：短期均線明確上穿長期均線</li>
                    <li><strong>成交量配合</strong>：金叉伴隨成交量放大更可靠</li>
                    <li><strong>趨勢跟隨</strong>：適合中長期趨勢跟隨</li>
                    <li><strong>分批建倉</strong>：金叉後可分批建立倉位</li>
                </ol>
            </div>

            <h4>📉 死叉賣出操作：</h4>
            <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <ol>
                    <li><strong>及時止損</strong>：死叉出現要及時止損</li>
                    <li><strong>趨勢轉換</strong>：死叉通常表示趨勢轉換</li>
                    <li><strong>分批出場</strong>：可分批賣出降低衝擊</li>
                    <li><strong>重新評估</strong>：死叉後重新評估基本面</li>
                </ol>
            </div>

            <h3>⚖️ 移動平均策略風險控制</h3>
            <ul>
                <li><strong>假突破</strong>：注意短暫交叉後又回到原位</li>
                <li><strong>震盪過濾</strong>：震盪市場中減少交易頻率</li>
                <li><strong>持有週期</strong>：適合中長期持有（1-6個月）</li>
                <li><strong>均線支撐</strong>：跌破長期均線要考慮止損</li>
            </ul>
            """

        # 通用風險管理和免責聲明
        common_footer = """
        <h3>🔄 策略組合建議</h3>
        <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <p><strong>不要只依賴單一指標！</strong></p>
            <ul>
                <li>結合多種技術指標確認信號</li>
                <li>觀察成交量變化</li>
                <li>關注基本面消息</li>
                <li>考慮市場整體環境</li>
            </ul>
        </div>

        <h3>📊 回測結果的應用</h3>
        <ol>
            <li><strong>勝率分析</strong>：了解策略的成功機率</li>
            <li><strong>風險評估</strong>：評估最大回撤和風險</li>
            <li><strong>參數優化</strong>：根據回測結果調整參數</li>
            <li><strong>實盤驗證</strong>：小資金實盤測試策略效果</li>
        </ol>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #6c757d;">
            <h4>⚠️ 免責聲明</h4>
            <p>本分析僅供參考，不構成投資建議。投資有風險，請根據自身風險承受能力謹慎投資。</p>
        </div>
        """

        if strategy_name in ['RSI策略', 'MACD策略', '布林通道策略', '移動平均交叉策略']:
            return self.get_strategy_practical_application_html(strategy_name) + common_footer

        return "<p>實戰應用載入中...</p>" + common_footer

    def generate_signal_analysis_html(self):
        """生成當前回測的信號分析HTML"""
        if not self.results:
            return "<p>無回測數據可分析</p>"

        html = "<h2>🔍 當前回測信號分析報告</h2>"

        # 基本統計
        if 'performance_metrics' in self.results:
            metrics = self.results['performance_metrics']
            html += f"""
            <h3>📊 基本統計</h3>
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <tr><td><strong>總交易次數</strong></td><td>{metrics.get('總交易次數', 'N/A')}</td></tr>
                <tr><td><strong>獲利交易</strong></td><td>{metrics.get('獲利交易', 'N/A')}</td></tr>
                <tr><td><strong>虧損交易</strong></td><td>{metrics.get('虧損交易', 'N/A')}</td></tr>
                <tr><td><strong>勝率</strong></td><td>{metrics.get('勝率', 'N/A')}</td></tr>
                <tr><td><strong>平均報酬率</strong></td><td>{metrics.get('平均報酬率', 'N/A')}</td></tr>
            </table>
            """

        # 信號分析
        html += """
        <h3>🎯 信號特性分析</h3>
        <div style="background-color: #e9ecef; padding: 10px; border-radius: 5px;">
            <h4>買入信號特點：</h4>
            <ul>
                <li>基於RSI超賣反彈邏輯</li>
                <li>適合短期交易操作</li>
                <li>需要配合趨勢判斷</li>
            </ul>

            <h4>賣出信號特點：</h4>
            <ul>
                <li>基於RSI超買回調邏輯</li>
                <li>適合獲利了結操作</li>
                <li>可分批執行降低風險</li>
            </ul>
        </div>

        <h3>💡 改進建議</h3>
        <ol>
            <li><strong>參數調整</strong>：可嘗試不同的RSI參數設定</li>
            <li><strong>過濾條件</strong>：加入成交量或趨勢過濾</li>
            <li><strong>止損機制</strong>：設定明確的停損停利點</li>
            <li><strong>資金管理</strong>：控制單筆交易的資金比例</li>
        </ol>
        """

        return html

def main():
    """主函數"""
    app = QApplication(sys.argv)

    # 設置應用程式樣式
    app.setStyle('Fusion')

    # 創建主窗口
    window = AshiuBacktestGUI()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
