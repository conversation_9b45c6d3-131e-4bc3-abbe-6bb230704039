#!/usr/bin/env python3
"""
測試信號顯示功能
驗證所有策略的K線圖信號顯示是否正常工作
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_enhanced_data_fetcher():
    """測試增強版數據獲取器"""
    print("🧪 測試增強版數據獲取器")
    print("=" * 60)
    
    try:
        from enhanced_data_fetcher import get_stock_data_enhanced
        
        # 測試幾支熱門股票
        test_stocks = ['2330', '2317', '2454', '3008', '2412']
        
        for stock_id in test_stocks:
            print(f"\n📊 測試股票: {stock_id}")
            
            df = get_stock_data_enhanced(stock_id, 30)
            
            if not df.empty:
                print(f"✅ 成功獲取 {len(df)} 天數據")
                print(f"   日期範圍: {df['date'].min()} 到 {df['date'].max()}")
                print(f"   最新收盤價: {df['Close'].iloc[-1]:.2f}")
                print(f"   平均成交量: {df['Volume'].mean():.0f}")
            else:
                print(f"❌ 獲取數據失敗")
        
        print(f"\n✅ 增強版數據獲取器測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 增強版數據獲取器測試失敗: {e}")
        return False

def test_strategy_signal_functions():
    """測試策略信號函數"""
    print("\n🎯 測試策略信號函數")
    print("=" * 60)
    
    # 創建模擬數據
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)
    
    # 生成模擬股價數據
    base_price = 100
    returns = np.random.normal(0.001, 0.02, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 創建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'Open': prices,
        'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'Close': prices,
        'Volume': np.random.randint(1000000, 10000000, len(dates))
    })
    
    # 計算技術指標
    df = calculate_test_indicators(df)
    
    print(f"📊 創建模擬數據: {len(df)} 天")
    print(f"   價格範圍: {df['Close'].min():.2f} - {df['Close'].max():.2f}")
    
    # 測試各種策略信號檢測
    strategies = [
        ('二次創高', test_second_high_signals),
        ('勝率73%', test_win_rate_signals),
        ('破底反彈', test_bottom_rebound_signals),
        ('阿水一式', test_ashui_signals),
        ('藏獒', test_tibetan_mastiff_signals),
        ('CANSLIM', test_canslim_signals),
        ('膽小貓', test_timid_cat_signals)
    ]
    
    results = {}
    
    for strategy_name, test_func in strategies:
        try:
            print(f"\n🎯 測試 {strategy_name} 策略信號...")
            buy_signals, sell_signals = test_func(df)
            
            results[strategy_name] = {
                'buy_count': len(buy_signals),
                'sell_count': len(sell_signals),
                'success': True
            }
            
            print(f"   ✅ 買入信號: {len(buy_signals)} 個")
            print(f"   ✅ 賣出信號: {len(sell_signals)} 個")
            
        except Exception as e:
            print(f"   ❌ 測試失敗: {e}")
            results[strategy_name] = {
                'buy_count': 0,
                'sell_count': 0,
                'success': False,
                'error': str(e)
            }
    
    # 總結測試結果
    print(f"\n📊 策略信號測試總結")
    print("-" * 60)
    
    success_count = 0
    total_buy_signals = 0
    total_sell_signals = 0
    
    for strategy, result in results.items():
        status = "✅" if result['success'] else "❌"
        print(f"{status} {strategy}: 買入{result['buy_count']}個, 賣出{result['sell_count']}個")
        
        if result['success']:
            success_count += 1
            total_buy_signals += result['buy_count']
            total_sell_signals += result['sell_count']
    
    print(f"\n🎉 測試完成: {success_count}/{len(strategies)} 個策略成功")
    print(f"📊 總信號數: 買入{total_buy_signals}個, 賣出{total_sell_signals}個")
    
    return success_count == len(strategies)

def calculate_test_indicators(df):
    """計算測試用技術指標"""
    # 移動平均線
    for period in [5, 10, 20, 60, 120, 240]:
        df[f'MA{period}'] = df['Close'].rolling(window=period).mean()
    
    # RSI
    for period in [6, 13, 14, 20, 60, 120]:
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        rs = avg_gain / avg_loss
        df[f'RSI{period}'] = 100 - (100 / (1 + rs))
    
    # 布林帶
    df['BB_middle'] = df['MA20']
    std = df['Close'].rolling(window=20).std()
    df['BB_upper'] = df['BB_middle'] + (std * 2)
    df['BB_lower'] = df['BB_middle'] - (std * 2)
    
    return df

def test_second_high_signals(df):
    """測試二次創高策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(120, len(df)):
        # 檢查是否創60日新高
        recent_60_high = df['Close'].iloc[i-60:i+1].max()
        current_close = df['Close'].iloc[i]
        
        if current_close >= recent_60_high:
            # 檢查前期是否有整理
            prev_30_max = df['Close'].iloc[i-60:i-30].max()
            if current_close > prev_30_max * 1.02:
                buy_signals.append(i)
        
        # 賣出信號：MA20下彎
        if i > 0 and 'MA20' in df.columns:
            if df['MA20'].iloc[i] < df['MA20'].iloc[i-1]:
                sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_win_rate_signals(df):
    """測試勝率73.45%策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(240, len(df)):
        conditions_met = 0
        
        # MA240趨勢向上
        if i > 0 and 'MA240' in df.columns:
            if df['MA240'].iloc[i] > df['MA240'].iloc[i-1]:
                conditions_met += 1
        
        # MA20趨勢向上
        if i > 0 and 'MA20' in df.columns:
            if df['MA20'].iloc[i] > df['MA20'].iloc[i-1]:
                conditions_met += 1
        
        # RSI條件
        if 'RSI13' in df.columns and 'RSI6' in df.columns:
            if df['RSI13'].iloc[i] > 50 and df['RSI6'].iloc[i] > 70:
                conditions_met += 1
        
        # 成交量條件
        if df['Volume'].iloc[i] * df['Close'].iloc[i] > 10000000:
            conditions_met += 1
        
        if conditions_met >= 3:
            buy_signals.append(i)
        
        # 賣出信號
        if 'RSI6' in df.columns and df['RSI6'].iloc[i] > 80:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_bottom_rebound_signals(df):
    """測試破底反彈策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(20, len(df)):
        lookback = 10
        if i >= lookback:
            # 檢查是否創新低
            recent_low = df['Low'].iloc[i-lookback:i].min()
            current_low = df['Low'].iloc[i]
            
            # 檢查反彈幅度
            if current_low <= recent_low and df['Close'].iloc[i] > df['Close'].iloc[i-1] * 1.05:
                # 檢查成交量放大
                volume_ratio = df['Volume'].iloc[i] / df['Volume'].iloc[i-10:i].mean()
                if volume_ratio > 2.0:
                    buy_signals.append(i)
        
        # 賣出條件
        if 'MA10' in df.columns and df['Close'].iloc[i] < df['MA10'].iloc[i]:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_ashui_signals(df):
    """測試阿水一式策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(60, len(df)):
        # 買入條件：突破布林上軌
        if 'BB_upper' in df.columns and df['Close'].iloc[i] > df['BB_upper'].iloc[i]:
            buy_signals.append(i)
        
        # 賣出條件：跌破布林中軌
        if 'BB_middle' in df.columns and df['Close'].iloc[i] < df['BB_middle'].iloc[i]:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_tibetan_mastiff_signals(df):
    """測試藏獒策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(50, len(df)):
        conditions_met = 0
        
        # 價格突破MA20
        if 'MA20' in df.columns and df['Close'].iloc[i] > df['MA20'].iloc[i] * 1.02:
            conditions_met += 1
        
        # 成交量爆量
        if i >= 10:
            volume_ratio = df['Volume'].iloc[i] / df['Volume'].iloc[i-10:i].mean()
            if volume_ratio > 2.0:
                conditions_met += 1
        
        if conditions_met >= 2:
            buy_signals.append(i)
        
        # 賣出條件
        if 'MA10' in df.columns and df['Close'].iloc[i] < df['MA10'].iloc[i]:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_canslim_signals(df):
    """測試CANSLIM策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(60, len(df)):
        # 創新高條件
        if df['Close'].iloc[i] == df['Close'].iloc[i-50:i+1].max():
            buy_signals.append(i)
        
        # 賣出條件
        if 'MA20' in df.columns and df['Close'].iloc[i] < df['MA20'].iloc[i] * 0.92:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def test_timid_cat_signals(df):
    """測試膽小貓策略信號"""
    buy_signals = []
    sell_signals = []
    
    for i in range(100, len(df)):
        # 低價股創新高（模擬）
        if df['Close'].iloc[i] < 30 and df['Close'].iloc[i] == df['Close'].iloc[i-100:i+1].max():
            buy_signals.append(i)
        
        # 3%停損
        if i > 0 and df['Close'].iloc[i] < df['Close'].iloc[i-1] * 0.97:
            sell_signals.append(i)
    
    return buy_signals, sell_signals

def main():
    """主測試函數"""
    print("🚀 開始測試信號顯示功能")
    print("=" * 80)
    
    # 測試1: 增強版數據獲取器
    data_test_success = test_enhanced_data_fetcher()
    
    # 測試2: 策略信號函數
    signal_test_success = test_strategy_signal_functions()
    
    # 總結
    print(f"\n🎉 測試總結")
    print("=" * 80)
    
    if data_test_success:
        print("✅ 增強版數據獲取器: 正常工作")
    else:
        print("❌ 增強版數據獲取器: 存在問題")
    
    if signal_test_success:
        print("✅ 策略信號函數: 正常工作")
    else:
        print("❌ 策略信號函數: 存在問題")
    
    if data_test_success and signal_test_success:
        print("\n🎊 所有測試通過！信號顯示功能應該正常工作")
        print("💡 建議：現在可以啟動主程式測試K線圖信號顯示")
    else:
        print("\n⚠️ 部分測試失敗，請檢查相關模組")
    
    return data_test_success and signal_test_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
