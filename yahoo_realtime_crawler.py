#!/usr/bin/env python3
"""
Yahoo即時報價爬蟲模組
整合Goodinfo反爬蟲機制，提供穩定的即時股價數據
無API限制，適合盤中監控使用
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
import random
import re
from datetime import datetime
from typing import Optional, Dict, Any

class YahooRealtimeCrawler:
    """Yahoo即時報價爬蟲 - 強化反爬蟲機制"""
    
    def __init__(self):
        """初始化爬蟲"""
        self.session = requests.Session()
        
        # 🛡️ 多個User-Agent輪換（參考Goodinfo機制）
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/111.25 (KHTML, like Gecko) Chrome/99.0.2345.81 Safari/123.36'
        ]
        
        # 🛡️ 完整的請求標頭（參考Goodinfo反爬蟲機制）
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://tw.stock.yahoo.com/'
        }
        
        # 🛡️ 請求頻率控制
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 最小請求間隔1秒
        self.failed_stocks = set()  # 記錄失敗的股票
        self.retry_count = {}  # 重試計數
        
        # 緩存機制
        self.cache = {}
        self.cache_timeout = 60  # 1分鐘緩存
        
        logging.info("✅ Yahoo即時報價爬蟲初始化完成（強化反爬蟲機制）")
    
    def get_random_headers(self) -> Dict[str, str]:
        """🛡️ 獲取隨機請求標頭"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = self.user_agents[np.random.randint(0, len(self.user_agents))]
        return headers
    
    def make_safe_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """🛡️ 安全請求方法，包含完整反爬蟲機制"""
        # 檢查請求頻率
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            logging.debug(f"🛡️ 請求頻率控制，等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)
        
        # 隨機延遲（1-3秒）
        random_delay = random.uniform(1, 3)
        logging.debug(f"🛡️ 隨機延遲 {random_delay:.2f} 秒")
        time.sleep(random_delay)
        
        # 獲取隨機標頭
        headers = self.get_random_headers()
        
        try:
            response = self.session.get(url, headers=headers, timeout=15, **kwargs)
            
            # 更新最後請求時間
            self.last_request_time = time.time()
            
            # 設定正確的編碼
            if 'charset' in response.headers.get('content-type', ''):
                response.encoding = response.apparent_encoding
            else:
                response.encoding = 'utf-8'
            
            return response
            
        except Exception as e:
            logging.error(f"❌ 安全請求失敗: {e}")
            return None
    
    def get_realtime_data(self, stock_id: str) -> pd.DataFrame:
        """
        獲取股票即時報價數據
        
        Args:
            stock_id: 股票代碼
            
        Returns:
            pandas.DataFrame: 即時報價數據
        """
        # 清理股票代碼
        clean_id = re.sub(r'[^\d]', '', str(stock_id))
        
        # 檢查緩存
        cache_key = f"realtime_{clean_id}"
        if cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_timeout:
                logging.debug(f"📦 使用緩存的即時數據: {clean_id}")
                return data
        
        # 檢查是否為已知失敗股票
        if clean_id in self.failed_stocks:
            retry_count = self.retry_count.get(clean_id, 0)
            if retry_count < 3:  # 最多重試3次
                self.retry_count[clean_id] = retry_count + 1
                logging.info(f"🔄 重試失敗股票 {clean_id} (第{retry_count + 1}次)")
            else:
                logging.debug(f"🛡️ 跳過已知失敗股票: {clean_id}")
                return pd.DataFrame()
        
        try:
            # 構建Yahoo爬蟲URL（添加隨機參數避免緩存）
            timestamp = int(time.time() * 1000)
            callback_id = f"jQuery{random.randint(111111111111111111, 999999999999999999)}_{timestamp}"
            url = f"https://tw.quote.finance.yahoo.net/quote/q?type=ta&perd=d&mkt=10&sym={clean_id}&v=1&callback={callback_id}&_={timestamp}"
            
            logging.info(f"🛡️ 使用強化Yahoo爬蟲獲取 {clean_id} 即時數據")
            
            # 使用安全請求方法
            response = self.make_safe_request(url)
            
            if response and response.status_code == 200:
                # 解析響應數據
                text = response.text
                
                # 檢查是否被反爬蟲機制阻擋
                if "初始化中" in text or "請稍候" in text or "驗證中" in text:
                    logging.warning(f"🛡️ {clean_id} 觸發反爬蟲機制，等待後重試")
                    time.sleep(5)
                    # 重新請求一次
                    response = self.make_safe_request(url)
                    if response:
                        text = response.text
                    else:
                        self.failed_stocks.add(clean_id)
                        return pd.DataFrame()
                
                # 解析數據
                df = self._parse_yahoo_response(text, clean_id)
                
                if not df.empty:
                    # 緩存數據
                    self.cache[cache_key] = (time.time(), df)
                    # 從失敗列表中移除
                    self.failed_stocks.discard(clean_id)
                    self.retry_count.pop(clean_id, None)
                    logging.info(f"✅ 強化Yahoo爬蟲成功獲取 {clean_id} 即時數據")
                    return df
                else:
                    self.failed_stocks.add(clean_id)
                    return pd.DataFrame()
            else:
                status_code = response.status_code if response else "無回應"
                logging.warning(f"⚠️ Yahoo爬蟲請求失敗 {clean_id}: {status_code}")
                self.failed_stocks.add(clean_id)
                return pd.DataFrame()
                
        except Exception as e:
            logging.error(f"❌ Yahoo爬蟲獲取失敗 {clean_id}: {e}")
            self.failed_stocks.add(clean_id)
            return pd.DataFrame()
    
    def _parse_yahoo_response(self, text: str, stock_id: str) -> pd.DataFrame:
        """解析Yahoo回應數據"""
        try:
            # 最新價格數據
            current_data = [l for l in text.split('{') if len(l) >= 60]
            if len(current_data) >= 2:
                current = current_data[-1].replace('"', '').split(',')
                
                # 昨日價格
                yday_match = re.search(':.*', current_data[-2].split(',')[4])
                if yday_match:
                    yday = float(yday_match.group()[1:])
                    
                    # 解析當前數據
                    open_match = re.search(':.*', current[1])
                    high_match = re.search(':.*', current[2])
                    low_match = re.search(':.*', current[3])
                    close_match = re.search(':.*', current[4])
                    volume_match = re.search(':.*', current[5].replace('}]', ''))
                    
                    if all([open_match, high_match, low_match, close_match, volume_match]):
                        open_price = float(open_match.group()[1:])
                        high_price = float(high_match.group()[1:])
                        low_price = float(low_match.group()[1:])
                        close_price = float(close_match.group()[1:])
                        volume = float(volume_match.group()[1:])
                        
                        # 計算漲跌幅
                        pct_change = round((close_price / yday - 1) * 100, 2)
                        
                        # 創建DataFrame
                        df_data = [{
                            'datetime': datetime.now(),
                            'stock_id': stock_id,
                            'open': open_price,
                            'high': high_price,
                            'low': low_price,
                            'close': close_price,
                            'volume': volume,
                            'pct_change': pct_change,
                            'prev_close': yday,
                            'source': 'yahoo_crawler'
                        }]
                        
                        return pd.DataFrame(df_data)
            
            logging.warning(f"⚠️ Yahoo爬蟲數據解析失敗 {stock_id}")
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"❌ Yahoo回應解析錯誤 {stock_id}: {e}")
            return pd.DataFrame()
    
    def get_multiple_stocks_data(self, stock_list: list) -> Dict[str, pd.DataFrame]:
        """批量獲取多支股票的即時數據"""
        results = {}
        
        for stock_id in stock_list:
            try:
                logging.info(f"📊 獲取 {stock_id} 即時數據...")
                data = self.get_realtime_data(stock_id)
                
                if not data.empty:
                    results[stock_id] = data
                    logging.info(f"✅ {stock_id} 即時數據獲取成功")
                else:
                    logging.warning(f"⚠️ {stock_id} 即時數據獲取失敗")
                
                # 避免請求過快
                time.sleep(0.5)
                
            except Exception as e:
                logging.error(f"❌ {stock_id} 即時數據獲取錯誤: {e}")
                continue
        
        return results
    
    def clear_cache(self):
        """清除緩存"""
        self.cache.clear()
        logging.info("🗑️ Yahoo爬蟲緩存已清除")
    
    def reset_failed_stocks(self):
        """重置失敗股票列表"""
        self.failed_stocks.clear()
        self.retry_count.clear()
        logging.info("🔄 失敗股票列表已重置")

# 創建全局實例
yahoo_crawler = YahooRealtimeCrawler()

def get_realtime_quote(stock_id: str) -> pd.DataFrame:
    """便捷函數：獲取即時報價"""
    return yahoo_crawler.get_realtime_data(stock_id)

def get_multiple_quotes(stock_list: list) -> Dict[str, pd.DataFrame]:
    """便捷函數：批量獲取即時報價"""
    return yahoo_crawler.get_multiple_stocks_data(stock_list)

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試Yahoo即時報價爬蟲...")
    
    # 測試單支股票
    test_stock = "2330"
    print(f"📊 測試股票: {test_stock}")
    
    df = get_realtime_quote(test_stock)
    if not df.empty:
        print("✅ 單支股票測試成功")
        print(df.to_string())
    else:
        print("❌ 單支股票測試失敗")
    
    print("\n" + "="*50)
    
    # 測試多支股票
    test_stocks = ["2330", "2317", "2454"]
    print(f"📊 測試股票列表: {test_stocks}")
    
    results = get_multiple_quotes(test_stocks)
    print(f"✅ 成功獲取 {len(results)} 支股票數據")
    
    for stock_id, data in results.items():
        if not data.empty:
            price = data.iloc[0]['close']
            change = data.iloc[0]['pct_change']
            print(f"   {stock_id}: ${price:.2f} ({change:+.2f}%)")
    
    print("🎉 測試完成！")
