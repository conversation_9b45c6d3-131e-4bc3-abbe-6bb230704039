#!/usr/bin/env python3
"""
AI技術指標回測系統GUI窗口
提供用戶友好的回測界面
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QGridLayout, QLabel, QLineEdit,
                            QPushButton, QComboBox, QDateEdit, QTextEdit,
                            QProgressBar, QTabWidget, QGroupBox, QSpinBox,
                            QDoubleSpinBox, QMessageBox, QSplitter, QFrame,
                            QScrollArea)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
import pandas as pd
from datetime import datetime, timedelta
import logging

from ai_technical_backtest import (BacktestEngine, AVAILABLE_STRATEGIES,
                                  get_stock_data, BacktestResultsWidget)
import requests
import json

# 台股完整股票代碼與名稱對應
TAIWAN_STOCKS = {
    # 權值股 & 科技股
    "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2308": "台達電", "2382": "廣達",
    "2303": "聯電", "2357": "華碩", "2409": "友達", "3008": "大立光", "3711": "日月光投控",
    "2379": "瑞昱", "3034": "聯詠", "3481": "群創", "4938": "和碩", "2327": "國巨",
    "2474": "可成", "6669": "緯穎", "2376": "技嘉", "2395": "研華", "3037": "欣興",
    "2408": "南亞科", "3443": "創意", "6415": "矽力-KY", "3661": "世芯-KY", "6770": "力積電",
    "2449": "京元電子", "2458": "義隆", "2301": "光寶科", "2324": "仁寶", "2356": "英業達",
    "2377": "微星", "2412": "中華電", "3045": "台灣大", "4904": "遠傳", "6505": "台塑化",

    # 金融股
    "2881": "富邦金", "2882": "國泰金", "2883": "開發金", "2884": "玉山金", "2885": "元大金",
    "2886": "兆豐金", "2887": "台新金", "2891": "中信金", "2892": "第一金", "5880": "合庫金",
    "2880": "華南金", "2888": "新光金", "2890": "永豐金", "5876": "上海商銀", "2834": "臺企銀",
    "2845": "遠東銀", "2849": "安泰銀", "2850": "新產", "2851": "中再保", "2852": "第一保",
    "2855": "統一證", "2856": "元富證", "6005": "群益證", "6015": "宏遠證",

    # 傳統產業
    "1101": "台泥", "1102": "亞泥", "1216": "統一", "1301": "台塑", "1303": "南亞",
    "1326": "台化", "1402": "遠東新", "1476": "儒鴻", "1590": "亞德客-KY", "1605": "華新",
    "2002": "中鋼", "2207": "和泰車", "2227": "裕日車", "2912": "統一超", "2915": "潤泰全",
    "9904": "寶成", "9910": "豐泰", "9921": "巨大", "9933": "中鼎", "9939": "宏全",

    # 航運股
    "2603": "長榮", "2609": "陽明", "2615": "萬海", "2618": "長榮航", "2633": "台灣高鐵",
    "2637": "慧洋-KY", "5608": "四維航", "2606": "裕民", "2634": "漢翔",

    # 生技醫療
    "4000": "南茂", "6446": "藥華藥", "6547": "高端疫苗", "4174": "浩鼎", "6472": "保瑞",
    "1789": "神隆", "1777": "生泰", "4745": "合富-KY", "6535": "順藥",

    # 食品股
    "1229": "聯華", "1234": "黑松", "1235": "興泰", "1262": "綠悅-KY", "1319": "東陽",
    "1702": "南僑", "1717": "長興", "1722": "台肥", "1723": "中碳", "1730": "花仙子",

    # 營建股
    "2501": "國建", "2505": "國揚", "2509": "全坤建", "2511": "太子", "2515": "中工",
    "2516": "新建", "2520": "冠德", "2524": "京城", "2527": "宏璟", "2535": "達欣工",
    "2542": "興富發", "2545": "皇翔", "2547": "日勝生", "2548": "華固", "5522": "遠雄",

    # 觀光股
    "2702": "華園", "2704": "國賓", "2705": "六福", "2706": "第一店", "2707": "晶華",
    "2712": "遠雄來", "5703": "亞都", "9902": "台火", "9905": "大華", "9906": "欣巴巴",

    # 汽車股
    "2201": "裕隆", "2204": "中華", "2206": "三陽工業", "2208": "台船", "2231": "為升",
    "2239": "英利-KY", "2243": "宏旭-KY", "2247": "汎德永業", "2250": "IKKA-KY",

    # ETF
    "0050": "元大台灣50", "0056": "元大高股息", "006208": "富邦台50", "00878": "國泰永續高股息",
    "00881": "國泰台灣5G+", "00692": "富邦公司治理", "00713": "元大台灣高息低波", "00757": "統一FANG+",
    "00679B": "元大美債20年", "00687B": "國泰20年美債", "00751B": "元大AAA至A公司債",
    "00772B": "中信高評級公司債", "00830": "國泰費城半導體", "00850": "元大臺灣ESG永續",
    "00851": "台新臺灣永續", "00852": "中信臺灣ESG", "00853": "統一臺灣高息", "00854": "國泰臺灣5G+",
    "00855": "國泰臺灣智慧", "00856": "國泰臺灣永續", "00857": "統一臺灣高息", "00858": "富邦臺灣優質高息",
    "00859": "永豐臺灣加權", "00860": "富邦臺灣半導體", "00861": "元大臺灣高息低波", "00862": "中信臺灣智慧50",
    "00863": "中信臺灣ESG", "00864": "富邦臺灣核心半導體", "00865": "富邦臺灣摩根", "00866": "中信臺灣綠能及電動車",
    "00867": "永豐優息存股", "00868": "國泰臺灣5G+", "00869": "富邦臺灣優質高息", "00870": "中信關鍵半導體",
    "00871": "統一臺灣高息", "00872": "中信中國高股息", "00873": "統一臺灣高息", "00874": "中信臺灣智慧城市",
    "00875": "國泰臺灣5G+", "00876": "元大臺灣高息低波", "00877": "中信臺灣ESG", "00879": "街口臺灣ESG",
    "00880": "國泰臺灣ESG永續", "00882": "中信臺灣ESG", "00883": "中信臺灣智慧", "00884": "富邦臺灣優質高息",
    "00885": "富邦臺灣核心半導體", "00886": "中信臺灣綠能及電動車", "00887": "永豐優息存股", "00888": "永豐臺灣ESG",
    "00889": "富邦臺灣優質高息", "00890": "永豐臺灣加權", "00891": "中信臺灣智慧50", "00892": "富邦臺灣半導體",
    "00893": "國泰智慧電動車", "00894": "中信小資臺灣高息", "00895": "富邦未來車", "00896": "中信綠能及電動車",
    "00897": "富邦臺灣半導體", "00898": "中信臺灣ESG", "00899": "富邦特選高股息30", "00900": "富邦特選小型股",
    "00901": "永豐智能車供應鏈", "00902": "中信臺灣智慧50", "00903": "富邦臺灣優質高息", "00904": "新光臺灣半導體30",
    "00905": "FT臺灣Smart", "00906": "中信臺灣ESG", "00907": "永豐優息存股", "00908": "富邦臺灣優質高息",
    "00909": "富邦臺灣優質高息", "00910": "永豐臺灣ESG"
}

def get_stocks_from_main_system():
    """從主系統獲取股票清單"""
    try:
        # 嘗試從主系統的數據庫獲取股票清單
        import sqlite3
        import os
        import json

        # 首先嘗試讀取配置文件獲取數據庫路徑
        config_path = "app_config.json"
        db_path = None

        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    db_config = config.get('database', {})
                    db_path = db_config.get('price_db_path')
                    print(f"從配置文件獲取數據庫路徑: {db_path}")
            except Exception as e:
                print(f"讀取配置文件失敗: {e}")

        # 如果配置文件中沒有路徑，使用預設路徑
        if not db_path or not os.path.exists(db_path):
            possible_db_paths = [
                "db/price.db",
                "data/price.db",
                "stock_data.db",
                "price_data.db"
            ]

            for path in possible_db_paths:
                if os.path.exists(path):
                    db_path = path
                    break

        if not db_path or not os.path.exists(db_path):
            print("找不到數據庫文件")
            return TAIWAN_STOCKS

        print(f"使用數據庫: {db_path}")

        # 連接數據庫並獲取股票清單
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 使用與主系統相同的查詢邏輯
        query = """
            SELECT DISTINCT stock_id,
                   CASE
                       WHEN stock_id = '2330' THEN '台積電'
                       WHEN stock_id = '2317' THEN '鴻海'
                       WHEN stock_id = '2454' THEN '聯發科'
                       WHEN stock_id = '2308' THEN '台達電'
                       WHEN stock_id = '2382' THEN '廣達'
                       WHEN stock_id = '2303' THEN '聯電'
                       WHEN stock_id = '2002' THEN '中鋼'
                       WHEN stock_id = '1303' THEN '南亞'
                       WHEN stock_id = '1301' THEN '台塑'
                       WHEN stock_id = '2881' THEN '富邦金'
                       WHEN stock_id = '2882' THEN '國泰金'
                       WHEN stock_id = '0050' THEN '元大台灣50'
                       WHEN stock_id = '0056' THEN '元大高股息'
                       ELSE '股票' || stock_id
                   END as stock_name
            FROM stock_daily_data
            WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
              AND LENGTH(stock_id) BETWEEN 4 AND 6
            ORDER BY
                CASE
                    WHEN LENGTH(stock_id) = 4 THEN 1
                    WHEN LENGTH(stock_id) = 5 THEN 2
                    WHEN LENGTH(stock_id) = 6 THEN 3
                END,
                CAST(stock_id AS INTEGER)
        """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            stocks_dict = {}
            for row in results:
                code, name = row[0], row[1]
                if code and name:
                    stocks_dict[code] = name

            print(f"成功從主系統數據庫獲取 {len(stocks_dict)} 支股票")
            return stocks_dict
        else:
            print("數據庫中沒有股票數據")

    except Exception as e:
        print(f"從主系統獲取股票清單失敗: {e}")

    # 如果無法從主系統獲取，使用備用清單
    print("使用備用股票清單")
    return TAIWAN_STOCKS

class BacktestWorker(QThread):
    """回測工作線程"""

    finished = pyqtSignal(object, object, object)  # result, data, signals
    error = pyqtSignal(str)
    progress = pyqtSignal(str)

    def __init__(self, symbol, start_date, end_date, strategy_name, strategy_params, db_path=None):
        super().__init__()
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.strategy_name = strategy_name
        self.strategy_params = strategy_params
        self.db_path = db_path
    
    def run(self):
        try:
            self.progress.emit("正在獲取股票數據...")

            # 獲取股票數據，傳入資料庫路徑
            data = get_stock_data(self.symbol, self.start_date, self.end_date, self.db_path)

            if data.empty:
                self.error.emit(f"無法獲取 {self.symbol} 的數據")
                return
            
            self.progress.emit(f"成功獲取 {len(data)} 筆數據，正在執行回測...")
            
            # 創建策略實例
            strategy_class = AVAILABLE_STRATEGIES[self.strategy_name]
            strategy = strategy_class(**self.strategy_params)
            
            # 執行回測
            engine = BacktestEngine()
            result = engine.run_backtest(data, strategy)
            
            if result:
                # 獲取交易信號
                signals = strategy.generate_signals(data)
                self.progress.emit("回測完成！")
                self.finished.emit(result, data, signals)
            else:
                self.error.emit("回測執行失敗")
                
        except Exception as e:
            self.error.emit(f"回測過程發生錯誤: {str(e)}")

class BacktestWindow(QMainWindow):
    """AI技術指標回測系統主窗口"""
    
    def __init__(self, db_path=None):
        super().__init__()
        self.setWindowTitle("🤖 AI技術指標回測系統")
        self.setGeometry(100, 100, 1400, 900)

        # 設置資料庫路徑
        self.db_path = db_path

        # 程序啟動時自動最大化
        self.showMaximized()
        
        # 設置深藍色主題樣式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a2e;
                color: #ffffff;
            }
            QWidget {
                background-color: #1a1a2e;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #0f3460;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: #16213e;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #64b5f6;
                font-size: 14px;
                font-weight: bold;
                background-color: #16213e;
            }
            QLabel {
                color: #ffffff;
                font-weight: 600;
                background-color: transparent;
                font-size: 12px;
            }
            QLineEdit, QComboBox, QDateEdit {
                background-color: #0e1b3c;
                border: 2px solid #0f3460;
                border-radius: 6px;
                padding: 8px 12px;
                color: #ffffff;
                font-size: 12px;
                font-weight: 500;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #64b5f6;
                background-color: #1e2a4a;
                outline: none;
            }
            QSpinBox, QDoubleSpinBox {
                background-color: #0e1b3c;
                border: 2px solid #0f3460;
                border-radius: 6px;
                padding: 6px 8px;
                color: #ffffff;
                font-size: 11px;
                font-weight: 500;
                min-height: 22px;
            }
            QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #64b5f6;
                background-color: #1e2a4a;
            }
            QSpinBox::up-button, QSpinBox::down-button,
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                background-color: #0f3460;
                border: 1px solid #64b5f6;
                border-radius: 3px;
                width: 18px;
                height: 12px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover,
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #64b5f6;
            }
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-bottom: 6px solid #ffffff;
                width: 0px;
                height: 0px;
            }
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #ffffff;
                width: 0px;
                height: 0px;
            }
            QPushButton {
                background-color: #0f3460;
                color: #ffffff;
                border: 2px solid #64b5f6;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #64b5f6;
                border-color: #90caf9;
            }
            QPushButton:pressed {
                background-color: #1976d2;
                border-color: #1976d2;
            }
            QPushButton:disabled {
                background-color: #424242;
                border-color: #616161;
                color: #9e9e9e;
            }
            QTextEdit {
                background-color: #0a0a0a;
                border: 2px solid #0f3460;
                border-radius: 6px;
                padding: 10px;
                color: #00ff41;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
                font-weight: 500;
            }
            QProgressBar {
                border: 2px solid #0f3460;
                border-radius: 6px;
                background-color: #16213e;
                text-align: center;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
            }
            QProgressBar::chunk {
                background-color: #64b5f6;
                border-radius: 4px;
            }
            QFrame {
                background-color: #16213e;
                border: 2px solid #0f3460;
                border-radius: 8px;
                color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #0f3460;
                border-radius: 3px;
                width: 20px;
                margin-right: 3px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid #ffffff;
                width: 0px;
                height: 0px;
                margin: 2px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #64b5f6;
            }
            QDateEdit::drop-down {
                border: none;
                background-color: #0f3460;
                border-radius: 3px;
                width: 20px;
                margin-right: 3px;
            }
            QDateEdit::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid #ffffff;
                width: 0px;
                height: 0px;
                margin: 2px;
            }
            QDateEdit::down-arrow:hover {
                border-top-color: #64b5f6;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #0e1b3c;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #2a4a7a;
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #64b5f6;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background-color: transparent;
            }
            QComboBox QAbstractItemView {
                background-color: #0e1b3c;
                border: 2px solid #0f3460;
                border-radius: 6px;
                color: #ffffff;
                selection-background-color: #2a4a7a;
                selection-color: #ffffff;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                min-height: 20px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #1e3a5f;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #2a4a7a;
            }
        """)
        
        self.init_ui()
        self.worker = None
        
    def init_ui(self):
        """初始化用戶界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QHBoxLayout(central_widget)
        
        # 創建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左側控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右側結果顯示
        results_panel = self.create_results_panel()
        splitter.addWidget(results_panel)
        
        # 設置分割比例 (左側控制面板更寬一些，方便查看股票選項)
        splitter.setSizes([600, 1100])
    
    def create_control_panel(self):
        """創建控制面板"""
        # 創建主面板
        main_panel = QFrame()
        main_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        main_panel.setMaximumWidth(450)

        # 創建滾動區域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 創建滾動內容容器
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        
        # 標題
        title_label = QLabel("🤖 AI技術指標回測系統")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Microsoft JhengHei", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            color: #ffffff;
            background-color: #0a0a0a;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            border: 3px solid #64b5f6;
            font-weight: bold;
        """)
        layout.addWidget(title_label)
        
        # 基本設置
        basic_group = QGroupBox("📊 基本設置")
        basic_layout = QGridLayout(basic_group)
        
        # 股票代碼
        basic_layout.addWidget(QLabel("股票代碼:"), 0, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.setEditable(True)  # 允許手動輸入

        # 獲取完整股票清單並添加到下拉選單
        print("正在從主系統載入股票清單...")
        all_stocks = get_stocks_from_main_system()

        # 按股票代碼排序
        sorted_stocks = dict(sorted(all_stocks.items()))

        # 添加股票選項（代碼 - 名稱格式）
        stock_items = []
        for code, name in sorted_stocks.items():
            stock_items.append(f"{code} - {name}")

        self.symbol_combo.addItems(stock_items)
        self.symbol_combo.setCurrentText("2330 - 台積電")  # 預設選擇台積電
        self.symbol_combo.setMaxVisibleItems(len(sorted_stocks))  # 顯示全部股票選項

        # 啟用自動完成功能
        self.symbol_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        self.symbol_combo.completer().setCompletionMode(self.symbol_combo.completer().CompletionMode.PopupCompletion)
        self.symbol_combo.completer().setFilterMode(Qt.MatchFlag.MatchContains)

        basic_layout.addWidget(self.symbol_combo, 0, 1)
        
        # 開始日期
        basic_layout.addWidget(QLabel("開始日期:"), 1, 0)
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        basic_layout.addWidget(self.start_date, 1, 1)

        # 結束日期
        basic_layout.addWidget(QLabel("結束日期:"), 2, 0)
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        basic_layout.addWidget(self.end_date, 2, 1)

        # 設定預設日期區間（先設定預設值，稍後會根據策略調整）
        end_date = QDate.currentDate()
        start_date = end_date.addMonths(-6)  # 預設6個月
        self.end_date.setDate(end_date)
        self.start_date.setDate(start_date)

        # 快速日期選擇按鈕
        date_buttons_layout = QHBoxLayout()

        quick_dates = [
            ("6個月", 6),
            ("1年", 12),
            ("2年", 24),
            ("3年", 36)
        ]

        for text, months in quick_dates:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, m=months: self._set_quick_date_range(m))
            btn.setMinimumWidth(80)  # 增加最小寬度以顯示完整文字
            btn.setMaximumWidth(100)  # 增加最大寬度
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #1e3a5f;
                    border: 1px solid #0f3460;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #ffffff;
                    font-size: 11px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #2a4a7a;
                    border-color: #64b5f6;
                }
                QPushButton:pressed {
                    background-color: #0f3460;
                }
            """)
            date_buttons_layout.addWidget(btn)

        basic_layout.addLayout(date_buttons_layout, 3, 0, 1, 2)

        layout.addWidget(basic_group)
        
        # 策略設置
        strategy_group = QGroupBox("🎯 策略設置")
        strategy_layout = QGridLayout(strategy_group)
        
        # 策略選擇
        strategy_layout.addWidget(QLabel("選擇策略:"), 0, 0)
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems(list(AVAILABLE_STRATEGIES.keys()))
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        strategy_layout.addWidget(self.strategy_combo, 0, 1)
        
        # 策略參數區域
        self.params_group = QGroupBox("⚙️ 策略參數")
        self.params_layout = QGridLayout(self.params_group)
        strategy_layout.addWidget(self.params_group, 1, 0, 1, 2)
        
        layout.addWidget(strategy_group)
        
        # 初始化參數界面
        self.on_strategy_changed()
        
        # 執行按鈕
        self.run_button = QPushButton("🚀 開始回測")
        self.run_button.clicked.connect(self.run_backtest)
        self.run_button.setMinimumHeight(40)
        layout.addWidget(self.run_button)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 狀態顯示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setPlaceholderText("回測狀態將在這裡顯示...")
        layout.addWidget(self.status_text)
        
        layout.addStretch()

        # 設置滾動區域
        scroll_area.setWidget(scroll_content)

        # 創建主面板佈局
        main_layout = QVBoxLayout(main_panel)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        return main_panel
    
    def create_results_panel(self):
        """創建結果顯示面板"""
        # 創建主面板
        main_panel = QFrame()
        main_panel.setFrameStyle(QFrame.Shape.StyledPanel)

        # 創建滾動區域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 創建滾動內容容器
        scroll_content = QWidget()
        scroll_content.setMinimumHeight(720)  # 調整為更緊湊的高度
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(3, 3, 3, 3)  # 減少邊距
        
        # 標題和按鈕的水平佈局
        title_button_layout = QHBoxLayout()
        title_button_layout.setContentsMargins(8, 2, 8, 2)  # 進一步減少上下邊距
        title_button_layout.setSpacing(5)  # 減少間距

        # 結果標題
        title_label = QLabel("📈 回測結果")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        title_label.setFont(QFont("Microsoft JhengHei", 12, QFont.Weight.Bold))  # 減小字體
        title_label.setStyleSheet("""
            color: #ffffff;
            background-color: #1e1e1e;
            padding: 4px 10px;  /* 減少內邊距 */
            margin: 0px;
            border-radius: 3px;  /* 減小圓角 */
            border: 1px solid #4CAF50;  /* 減小邊框 */
            font-weight: bold;
            min-height: 18px;  /* 減小最小高度 */
            max-height: 22px;  /* 限制最大高度 */
        """)
        title_button_layout.addWidget(title_label)

        # 圖表操作按鈕容器
        buttons_container = QWidget()
        buttons_container.setStyleSheet("background-color: transparent;")
        chart_buttons_layout = QHBoxLayout(buttons_container)
        chart_buttons_layout.setContentsMargins(0, 0, 0, 0)
        chart_buttons_layout.setSpacing(8)
        
        # 複製圖片按鈕
        self.copy_chart_btn = QPushButton("📋 複製圖片")
        self.copy_chart_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 10px;  /* 減少內邊距 */
                border-radius: 3px;  /* 減小圓角 */
                font-weight: bold;
                font-size: 10px;  /* 減小字體 */
                min-width: 70px;  /* 減小最小寬度 */
                min-height: 22px;  /* 減小最小高度 */
                max-height: 24px;  /* 限制最大高度 */
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        self.copy_chart_btn.clicked.connect(self.copy_chart)
        self.copy_chart_btn.setEnabled(False)  # 初始禁用
        chart_buttons_layout.addWidget(self.copy_chart_btn)

        # 保存圖片按鈕
        self.save_chart_btn = QPushButton("💾 另存圖片")
        self.save_chart_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 4px 10px;  /* 減少內邊距 */
                border-radius: 3px;  /* 減小圓角 */
                font-weight: bold;
                font-size: 10px;  /* 減小字體 */
                min-width: 70px;  /* 減小最小寬度 */
                min-height: 22px;  /* 減小最小高度 */
                max-height: 24px;  /* 限制最大高度 */
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
        """)
        self.save_chart_btn.clicked.connect(self.save_chart)
        self.save_chart_btn.setEnabled(False)  # 初始禁用
        chart_buttons_layout.addWidget(self.save_chart_btn)

        # 將按鈕容器添加到標題佈局
        title_button_layout.addWidget(buttons_container)

        # 將標題和按鈕佈局添加到主佈局
        layout.addLayout(title_button_layout)

        # 結果顯示組件
        self.results_widget = BacktestResultsWidget()
        layout.addWidget(self.results_widget.canvas)

        # 設置滾動區域
        scroll_area.setWidget(scroll_content)

        # 創建主面板佈局
        main_layout = QVBoxLayout(main_panel)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        return main_panel
    
    def on_strategy_changed(self):
        """策略改變時更新參數界面"""
        # 清除現有參數
        for i in reversed(range(self.params_layout.count())):
            self.params_layout.itemAt(i).widget().setParent(None)

        # 根據新策略調整日期區間
        self._set_default_date_range()
        
        strategy_name = self.strategy_combo.currentText()
        
        # 根據策略類型創建參數界面
        if strategy_name == "RSI策略":
            self.create_rsi_params()
        elif strategy_name == "MACD策略":
            self.create_macd_params()
        elif strategy_name == "布林通道策略":
            self.create_bollinger_params()
        elif strategy_name == "移動平均交叉策略":
            self.create_ma_cross_params()
    
    def create_rsi_params(self):
        """創建RSI策略參數"""
        self.params_layout.addWidget(QLabel("RSI週期:"), 0, 0)
        self.rsi_period = QSpinBox()
        self.rsi_period.setRange(5, 50)
        self.rsi_period.setValue(14)
        self.params_layout.addWidget(self.rsi_period, 0, 1)
        
        self.params_layout.addWidget(QLabel("超賣線:"), 1, 0)
        self.oversold = QSpinBox()
        self.oversold.setRange(10, 40)
        self.oversold.setValue(30)
        self.params_layout.addWidget(self.oversold, 1, 1)
        
        self.params_layout.addWidget(QLabel("超買線:"), 2, 0)
        self.overbought = QSpinBox()
        self.overbought.setRange(60, 90)
        self.overbought.setValue(70)
        self.params_layout.addWidget(self.overbought, 2, 1)
    
    def create_macd_params(self):
        """創建MACD策略參數"""
        self.params_layout.addWidget(QLabel("快線週期:"), 0, 0)
        self.macd_fast = QSpinBox()
        self.macd_fast.setRange(5, 30)
        self.macd_fast.setValue(12)
        self.params_layout.addWidget(self.macd_fast, 0, 1)
        
        self.params_layout.addWidget(QLabel("慢線週期:"), 1, 0)
        self.macd_slow = QSpinBox()
        self.macd_slow.setRange(15, 50)
        self.macd_slow.setValue(26)
        self.params_layout.addWidget(self.macd_slow, 1, 1)
        
        self.params_layout.addWidget(QLabel("信號線週期:"), 2, 0)
        self.macd_signal = QSpinBox()
        self.macd_signal.setRange(5, 20)
        self.macd_signal.setValue(9)
        self.params_layout.addWidget(self.macd_signal, 2, 1)
    
    def create_bollinger_params(self):
        """創建布林通道策略參數"""
        self.params_layout.addWidget(QLabel("移動平均週期:"), 0, 0)
        self.bb_period = QSpinBox()
        self.bb_period.setRange(10, 50)
        self.bb_period.setValue(20)
        self.params_layout.addWidget(self.bb_period, 0, 1)
        
        self.params_layout.addWidget(QLabel("標準差倍數:"), 1, 0)
        self.bb_std = QDoubleSpinBox()
        self.bb_std.setRange(1.0, 3.0)
        self.bb_std.setSingleStep(0.1)
        self.bb_std.setValue(2.0)
        self.params_layout.addWidget(self.bb_std, 1, 1)
    
    def create_ma_cross_params(self):
        """創建移動平均交叉策略參數"""
        self.params_layout.addWidget(QLabel("短期均線:"), 0, 0)
        self.ma_short = QSpinBox()
        self.ma_short.setRange(5, 30)
        self.ma_short.setValue(10)
        self.params_layout.addWidget(self.ma_short, 0, 1)
        
        self.params_layout.addWidget(QLabel("長期均線:"), 1, 0)
        self.ma_long = QSpinBox()
        self.ma_long.setRange(20, 100)
        self.ma_long.setValue(30)
        self.params_layout.addWidget(self.ma_long, 1, 1)
    
    def get_strategy_params(self):
        """獲取當前策略參數"""
        strategy_name = self.strategy_combo.currentText()
        
        if strategy_name == "RSI策略":
            return {
                'rsi_period': self.rsi_period.value(),
                'oversold': self.oversold.value(),
                'overbought': self.overbought.value()
            }
        elif strategy_name == "MACD策略":
            return {
                'fast': self.macd_fast.value(),
                'slow': self.macd_slow.value(),
                'signal': self.macd_signal.value()
            }
        elif strategy_name == "布林通道策略":
            return {
                'period': self.bb_period.value(),
                'std_dev': self.bb_std.value()
            }
        elif strategy_name == "移動平均交叉策略":
            return {
                'short_period': self.ma_short.value(),
                'long_period': self.ma_long.value()
            }
        
        return {}
    
    def run_backtest(self):
        """執行回測"""
        try:
            # 驗證輸入
            symbol_text = self.symbol_combo.currentText().strip()
            if not symbol_text:
                QMessageBox.warning(self, "警告", "請選擇股票代碼")
                return

            # 從選擇的文字中提取股票代碼（格式：代碼 - 名稱）
            if " - " in symbol_text:
                symbol = symbol_text.split(" - ")[0].strip()
            else:
                symbol = symbol_text.strip()  # 如果是手動輸入的純代碼
            
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            
            if self.start_date.date() >= self.end_date.date():
                QMessageBox.warning(self, "警告", "開始日期必須早於結束日期")
                return
            
            strategy_name = self.strategy_combo.currentText()
            strategy_params = self.get_strategy_params()
            
            # 禁用按鈕，顯示進度條
            self.run_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 無限進度條
            
            # 清空狀態
            self.status_text.clear()
            self.add_status(f"🚀 開始回測 {symbol} ({start_date} ~ {end_date})")
            self.add_status(f"📊 使用策略: {strategy_name}")
            self.add_status(f"⚙️ 策略參數: {strategy_params}")
            
            # 創建工作線程，傳入資料庫路徑
            self.worker = BacktestWorker(symbol, start_date, end_date, strategy_name, strategy_params, self.db_path)
            self.worker.finished.connect(self.on_backtest_finished)
            self.worker.error.connect(self.on_backtest_error)
            self.worker.progress.connect(self.add_status)
            self.worker.start()
            
        except Exception as e:
            self.on_backtest_error(f"啟動回測失敗: {str(e)}")
    
    def on_backtest_finished(self, result, data, signals):
        """回測完成處理"""
        try:
            # 恢復UI
            self.run_button.setEnabled(True)
            self.progress_bar.setVisible(False)
            
            # 顯示結果
            self.add_status("✅ 回測完成！")
            self.add_status(f"📊 總收益率: {result.total_return:.2%}")
            self.add_status(f"📈 年化收益率: {result.annual_return:.2%}")
            self.add_status(f"📉 最大回撤: {result.max_drawdown:.2%}")
            self.add_status(f"⚡ 夏普比率: {result.sharpe_ratio:.2f}")
            self.add_status(f"🎯 勝率: {result.win_rate:.2%}")
            self.add_status(f"🔄 總交易次數: {result.total_trades}")
            
            # 準備股票信息
            symbol_text = self.symbol_combo.currentText().strip()
            if " - " in symbol_text:
                code, name = symbol_text.split(" - ", 1)
                symbol_info = {'code': code.strip(), 'name': name.strip()}
            else:
                symbol_info = {'code': symbol_text, 'name': ''}

            # 繪製結果圖表
            self.results_widget.plot_results(data, signals, result, symbol_info)

            # 啟用圖表操作按鈕
            self.copy_chart_btn.setEnabled(True)
            self.save_chart_btn.setEnabled(True)

        except Exception as e:
            self.on_backtest_error(f"顯示結果失敗: {str(e)}")
    
    def on_backtest_error(self, error_msg):
        """回測錯誤處理"""
        self.run_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.add_status(f"❌ {error_msg}")
        QMessageBox.critical(self, "錯誤", error_msg)
    
    def add_status(self, message):
        """添加狀態信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        self.status_text.ensureCursorVisible()

    def _set_default_date_range(self, strategy_name=None):
        """根據策略設定合適的預設日期區間"""
        # 策略對應的建議回測期間（月數）
        strategy_periods = {
            'RSI策略': 6,       # 6個月 - RSI短期效果更明顯
            'MACD策略': 6,      # 6個月 - MACD中短期趨勢分析
            'KD策略': 6,        # 6個月 - KD短期超買超賣指標
            '布林通道策略': 6,    # 6個月 - 布林通道適合中短期分析
            '移動平均策略': 6,    # 6個月 - 移動平均中短期趨勢
            '威廉指標策略': 6,    # 6個月 - 威廉指標短期分析
            'CCI策略': 6,       # 6個月 - CCI中短期分析
            'ROC策略': 6,       # 6個月 - 動量指標中短期分析
            'DMI策略': 6,       # 6個月 - 趨勢指標中短期分析
            'TRIX策略': 6       # 6個月 - 三重指數平滑中短期分析
        }

        # 如果沒有指定策略，使用當前選擇的策略
        if strategy_name is None:
            strategy_name = self.strategy_combo.currentText()

        # 獲取建議的回測期間（預設6個月）
        months = strategy_periods.get(strategy_name, 6)

        # 設定結束日期為今天
        end_date = QDate.currentDate()
        # 設定開始日期為建議期間前
        start_date = end_date.addMonths(-months)

        self.end_date.setDate(end_date)
        self.start_date.setDate(start_date)

    def _set_quick_date_range(self, months):
        """快速設定日期區間"""
        end_date = QDate.currentDate()
        start_date = end_date.addMonths(-months)

        self.end_date.setDate(end_date)
        self.start_date.setDate(start_date)

    def copy_chart(self):
        """複製圖表到剪貼簿"""
        try:
            if self.results_widget.copy_chart_to_clipboard():
                self.add_status("📋 圖表已複製到剪貼簿")
                QMessageBox.information(self, "成功", "圖表已複製到剪貼簿！")
            else:
                QMessageBox.warning(self, "錯誤", "複製圖表失敗")
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"複製圖表時發生錯誤：{str(e)}")

    def save_chart(self):
        """保存圖表到文件"""
        try:
            strategy_name = self.strategy_combo.currentText()
            filepath = self.results_widget.save_chart_to_file(strategy_name)

            if filepath:
                self.add_status(f"💾 圖表已保存至: {filepath}")
                QMessageBox.information(self, "成功", f"圖表已保存至：\n{filepath}")
            else:
                QMessageBox.warning(self, "錯誤", "保存圖表失敗")
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"保存圖表時發生錯誤：{str(e)}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式樣式
    app.setStyle('Fusion')
    
    window = BacktestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
