# Finlab財務數據管理系統依賴包

# 核心數據處理
pandas>=1.5.0
numpy>=1.21.0
sqlite3  # Python內建模組

# GUI界面
tkinter  # Python內建模組（Windows/Linux可能需要額外安裝）

# 圖表和視覺化
matplotlib>=3.5.0
seaborn>=0.11.0

# 網路請求和API
requests>=2.28.0
urllib3>=1.26.0

# 配置文件處理
pyyaml>=6.0

# 任務排程
schedule>=1.2.0

# 日期時間處理
python-dateutil>=2.8.0

# 測試框架
unittest2>=1.1.0  # Python內建unittest的增強版本

# 數據驗證
jsonschema>=4.0.0

# 環境變數管理
python-dotenv>=0.19.0

# 進度條顯示
tqdm>=4.64.0

# 日誌處理
colorlog>=6.6.0

# 文件處理
openpyxl>=3.0.0  # Excel文件支援
xlsxwriter>=3.0.0  # Excel寫入支援

# 數據庫工具
sqlalchemy>=1.4.0  # 可選：更強大的數據庫ORM

# 加密和安全
cryptography>=3.4.0  # 可選：數據加密

# 性能優化
numba>=0.56.0  # 可選：數值計算加速

# 記憶體優化
psutil>=5.9.0  # 系統資源監控

# 開發和測試工具（可選）
pytest>=7.0.0
pytest-cov>=3.0.0
black>=22.0.0  # 代碼格式化
flake8>=4.0.0  # 代碼檢查

# 文檔生成（可選）
sphinx>=4.5.0
sphinx-rtd-theme>=1.0.0
