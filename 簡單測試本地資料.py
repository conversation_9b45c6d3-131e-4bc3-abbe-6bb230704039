#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試本地資料庫讀取功能
"""

import os
import sqlite3
import pandas as pd

def test_price_db():
    """測試price.db"""
    print("💾 測試price.db...")
    
    price_db_paths = [
        "D:/Finlab/history/tables/price.db",
        "db/price.db",
        "history/tables/price.db",
        "price.db"
    ]
    
    for db_path in price_db_paths:
        print(f"  檢查: {db_path}")
        if os.path.exists(db_path):
            print(f"  ✅ 找到: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]
                print(f"  📋 表格: {tables}")
                
                if 'stock_daily_data' in tables:
                    cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
                    count = cursor.fetchone()[0]
                    print(f"  📊 記錄數: {count:,}")
                    
                    # 測試查詢2330
                    cursor.execute("""
                        SELECT date, "Close"
                        FROM stock_daily_data 
                        WHERE stock_id = '2330' 
                        ORDER BY date DESC 
                        LIMIT 3
                    """)
                    results = cursor.fetchall()
                    if results:
                        print(f"  📈 2330最近3天:")
                        for date, close in results:
                            print(f"    {date}: {close}")
                    else:
                        print(f"  ⚠️ 找不到2330資料")
                
                conn.close()
                return True
                
            except Exception as e:
                print(f"  ❌ 讀取失敗: {e}")
        else:
            print(f"  ❌ 不存在")
    
    return False

def test_pkl_files():
    """測試PKL檔案"""
    print("\n📦 測試PKL檔案...")
    
    pkl_files = [
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl',
        'history/tables/pe.pkl'
    ]
    
    for pkl_path in pkl_files:
        print(f"  檢查: {pkl_path}")
        if os.path.exists(pkl_path):
            print(f"  ✅ 找到: {pkl_path}")
            
            try:
                df = pd.read_pickle(pkl_path)
                print(f"  📊 形狀: {df.shape}")
                
                if isinstance(df.index, pd.MultiIndex):
                    df_reset = df.reset_index()
                else:
                    df_reset = df.copy()
                
                if 'stock_id' in df_reset.columns:
                    # 查找2330
                    stock_2330 = df_reset[df_reset['stock_id'].astype(str).str.contains('2330', na=False)]
                    if not stock_2330.empty:
                        print(f"  📈 找到2330: {len(stock_2330)}筆")
                    else:
                        print(f"  ⚠️ 找不到2330")
                
            except Exception as e:
                print(f"  ❌ 讀取失敗: {e}")
        else:
            print(f"  ❌ 不存在")

def test_estimated_data():
    """測試估算資料"""
    print("\n💎 測試估算財務資料...")
    
    estimated_data = {
        '2330': {'殖利率': '2.1%', '本益比': '15.8', '股價淨值比': '2.3', 'EPS': '36.7'},
        '2317': {'殖利率': '4.2%', '本益比': '12.5', '股價淨值比': '1.8', 'EPS': '8.4'},
        '1101': {'殖利率': '5.5%', '本益比': '8.9', '股價淨值比': '1.2', 'EPS': '5.1'},
        '8021': {'殖利率': '0.8%', '本益比': '25.3', '股價淨值比': '4.2', 'EPS': '0.99'},
    }
    
    for stock_code, data in estimated_data.items():
        print(f"  📊 {stock_code}: {data}")
    
    print("  ✅ 估算資料可用作備用")

def main():
    """主函數"""
    print("🔍 簡單本地資料測試")
    print("=" * 50)
    
    # 測試price.db
    price_ok = test_price_db()
    
    # 測試PKL檔案
    test_pkl_files()
    
    # 測試估算資料
    test_estimated_data()
    
    print("\n" + "=" * 50)
    print("🎯 結論:")
    if price_ok:
        print("  ✅ 本地資料庫可用")
        print("  ✅ 可以避免網路API請求")
        print("  ✅ 不會有請求頻率限制問題")
    else:
        print("  ⚠️ 本地資料庫不完整")
        print("  💡 但有估算資料作為備用")
        print("  💡 財務指標仍可正常顯示")

if __name__ == "__main__":
    main()
