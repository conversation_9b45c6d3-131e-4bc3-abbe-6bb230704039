import datetime
import requests
import pandas as pd
import pickle
import time
import urllib
import os
import json
import io
from io import StringIO
try:
    import numpy as np
    # 嘗試訪問可能有問題的模組
    try:
        _ = np.core.numeric
    except AttributeError:
        # 如果 numpy._core.numeric 不存在，創建一個替代
        import types
        if not hasattr(np, '_core'):
            np._core = types.ModuleType('_core')
        if not hasattr(np._core, 'numeric'):
            np._core.numeric = types.ModuleType('numeric')
except ImportError:
    # 如果 numpy 導入失敗，創建一個簡單的替代
    class NumpyFallback:
        @staticmethod
        def where(condition):
            return [i for i, x in enumerate(condition) if x]

        @staticmethod
        def nan():
            return float('nan')

        nan = float('nan')

    np = NumpyFallback()
import warnings
import os
import datetime
import time
# 使用標準 tqdm 避免 ipywidgets 依賴問題
try:
    from tqdm import tqdm
    # 為了向後相容，創建別名
    tqdm_notebook = tqdm
    tnrange = range
except ImportError:
    # 如果沒有 tqdm，創建簡單的替代品
    class tqdm:
        def __init__(self, iterable=None, desc=None, total=None, **kwargs):
            self.iterable = iterable or []
            self.desc = desc or ""
            self.total = total or len(self.iterable) if hasattr(self.iterable, '__len__') else 0
            self.n = 0

        def __iter__(self):
            for item in self.iterable:
                yield item
                self.update(1)

        def update(self, n=1):
            self.n += n
            if self.total > 0:
                percent = (self.n / self.total) * 100
                print(f"\r{self.desc} [{self.n:>3}/{self.total}] {percent:>5.1f}%", end="", flush=True)

        def set_description(self, desc):
            self.desc = desc

        def close(self):
            print()  # 換行

    tqdm_notebook = tqdm
    tnrange = range

from requests.exceptions import ConnectionError, ReadTimeout, ChunkedEncodingError

# 移除 ipywidgets 依賴，使用 mock 替代
try:
    import ipywidgets as widgets
except ImportError:
    # 創建 mock widgets
    class MockWidgets:
        class Output:
            def __init__(self, **kwargs):
                pass
            def capture(self):
                def decorator(func):
                    return func
                return decorator
    widgets = MockWidgets()
import pip
import pandas
import gc
import shutil
import sqlite3
import random


def save_bargin_report_to_database(df, db_path='bargin_report.db'):
    """將 bargin_report 數據保存到SQLite數據庫"""
    try:
        # 檢查原始索引結構
        if hasattr(df.index, 'nlevels') and df.index.nlevels > 1:
            # 重置索引，將MultiIndex轉換為普通列
            df_reset = df.reset_index()
        else:
            df_reset = df.copy()

        # 處理索引欄位
        if len(df_reset.columns) >= 2:
            first_col = df_reset.columns[0]  # 第一個重置的索引欄位
            second_col = df_reset.columns[1]  # 第二個重置的索引欄位

            # 重新命名為標準格式
            df_reset = df_reset.rename(columns={
                first_col: 'stock_id_raw',
                second_col: 'date'
            })

        # 處理 stock_id，分離代碼和名稱
        if 'stock_id_raw' in df_reset.columns:
            def split_stock_info(stock_str):
                if not stock_str or pd.isna(stock_str):
                    return None, None

                stock_str = str(stock_str).strip()

                # 如果包含空格，按空格分割（格式：'代碼 名稱'）
                if ' ' in stock_str:
                    parts = stock_str.split(' ', 1)
                    stock_code = parts[0]
                    stock_name = parts[1] if len(parts) > 1 else ''
                else:
                    # 如果只有代碼，名稱為空
                    stock_code = stock_str
                    stock_name = ''

                return stock_code, stock_name

            # 分離股票代碼和名稱
            split_result = df_reset['stock_id_raw'].apply(lambda x: pd.Series(split_stock_info(x)))
            df_reset['stock_id'] = split_result[0]  # 股票代碼
            df_reset['stock_name'] = split_result[1]  # 股票名稱

            # 移除原始欄位
            df_reset = df_reset.drop('stock_id_raw', axis=1)

        # 確保日期格式正確
        if 'date' in df_reset.columns:
            df_reset['date'] = pd.to_datetime(df_reset['date']).dt.strftime('%Y-%m-%d')

        # 重新排列欄位順序（確保欄位存在）
        available_cols = list(df_reset.columns)
        ordered_cols = []

        # 先添加主要欄位（如果存在）
        for col in ['stock_id', 'stock_name', 'date']:
            if col in available_cols:
                ordered_cols.append(col)

        # 再添加其他欄位
        for col in available_cols:
            if col not in ordered_cols:
                ordered_cols.append(col)

        df_reset = df_reset[ordered_cols]

        # 創建數據庫連接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建表格（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bargin_report (
                stock_id TEXT,
                stock_name TEXT,
                date TEXT,
                "外陸資買進股數(不含外資自營商)" TEXT,
                "外陸資賣出股數(不含外資自營商)" TEXT,
                "外陸資買賣超股數(不含外資自營商)" TEXT,
                "外資自營商買進股數" TEXT,
                "外資自營商賣出股數" TEXT,
                "外資自營商買賣超股數" TEXT,
                "投信買進股數" TEXT,
                "投信賣出股數" TEXT,
                "投信買賣超股數" TEXT,
                "自營商買進股數(自行買賣)" TEXT,
                "自營商賣出股數(自行買賣)" TEXT,
                "自營商買賣超股數(自行買賣)" TEXT,
                "自營商買進股數(避險)" TEXT,
                "自營商賣出股數(避險)" TEXT,
                "自營商買賣超股數(避險)" TEXT
            )
        ''')

        # 增量更新：先刪除相同日期的資料，再插入新資料
        dates_to_update = df_reset['date'].unique()
        for date in dates_to_update:
            cursor.execute('DELETE FROM bargin_report WHERE date = ?', (date,))

        # 插入新資料
        df_reset.to_sql('bargin_report', conn, if_exists='append', index=False)

        # 創建索引以提高查詢性能
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON bargin_report(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON bargin_report(stock_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON bargin_report(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON bargin_report(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_stock ON bargin_report(date, stock_id)')
        except:
            pass  # 索引可能已存在

        conn.commit()

        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM bargin_report')
        record_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM bargin_report')
        unique_stocks = cursor.fetchone()[0]

        cursor.execute('SELECT MIN(date), MAX(date) FROM bargin_report WHERE date IS NOT NULL')
        date_range_result = cursor.fetchone()
        min_date, max_date = date_range_result if date_range_result and date_range_result[0] else (None, None)

        conn.close()

        # 計算文件大小
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB

        print(f"💾 已增量更新三大法人數據庫: {db_path}")
        print(f"   新增日期: {list(dates_to_update)}")
        print(f"   總記錄數: {record_count:,} 筆")
        print(f"   股票數: {unique_stocks:,} 檔")
        print(f"   日期範圍: {min_date} ~ {max_date}")
        print(f"   文件大小: {db_size:.1f} MB")

        return True

    except Exception as e:
        print(f"⚠️ 三大法人數據庫更新失敗: {str(e)[:50]}...")
        return False


def get_bargin_db_date_range(db_path='D:/Finlab/history/tables/bargin_report.db'):
    """獲取 bargin_report DB 檔案的日期範圍"""
    if not os.path.exists(db_path):
        return None, None

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT MIN(date), MAX(date) FROM bargin_report WHERE date IS NOT NULL')
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            min_date = pd.to_datetime(result[0])
            max_date = pd.to_datetime(result[1])
            return min_date, max_date
        else:
            return None, None
    except Exception as e:
        print(f"⚠️ 讀取bargin_report.db失敗: {e}")
        return None, None


def update_bargin_report_incremental(start_date, end_date, db_path='D:/Finlab/history/tables/bargin_report.db'):
    """增量更新 bargin_report 從指定日期範圍"""
    print(f"🔄 增量更新 bargin_report: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

    # 生成日期範圍（只包含工作日）
    dates = date_range(start_date, end_date)
    trading_dates = [d for d in dates if d.weekday() < 5]

    if not trading_dates:
        print("📅 沒有需要更新的交易日")
        return True

    print(f"📊 需要更新 {len(trading_dates)} 個交易日")

    success_count = 0
    total_records = 0

    for i, date in enumerate(trading_dates):
        try:
            progress = f"[{i+1:4d}/{len(trading_dates):4d}] {(i+1)/len(trading_dates)*100:5.1f}%"
            print(f"\r{progress} {date.strftime('%Y-%m-%d')}", end="", flush=True)

            # 調用爬蟲函數
            df = crawl_bargin(date)

            if df is not None and not df.empty:
                # 保存到 DB
                if save_bargin_report_to_database(df, db_path):
                    total_records += len(df)
                    success_count += 1

            # 增加延遲避免被封鎖
            if i < len(trading_dates) - 1:
                delay = random.uniform(2, 4)
                time.sleep(delay)

        except Exception as e:
            print(f"\n⚠️ 處理 {date.strftime('%Y-%m-%d')} 失敗: {str(e)[:50]}...")
            continue

    print(f"\n📊 更新完成: {success_count}/{len(trading_dates)} 成功")
    print(f"   總記錄數: {total_records:,} 筆")

    return success_count > 0


def import_or_install(package):
    try:
        __import__(package)
    except ImportError:
        print('Please install lxml(pip install lxml)')
        
    # try:
    #     versions = [int(i) for i in pandas.__version__.split('.')]
    #     assert (versions[0] == 1) & (versions[1] >= 1) & (versions[2] >= 4)
    # except:
    #     v = pandas.__version__
    #     raise Exception(f'Your pandas version is {v}. Please install pandas >= 1.1.4.')

import_or_install("lxml")


import random
import copy
def generate_random_header():
    random_user_agents = {'chrome': ['Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2228.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.1 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2226.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.4; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2224.3 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.93 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2049.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 4.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2049.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.67 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.67 Safari/537.36',
      'Mozilla/5.0 (X11; OpenBSD i386) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.125 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1944.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.3319.102 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.2309.372 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.2117.157 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.47 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1866.237 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.137 Safari/4E423F',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.116 Safari/537.36 Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.10',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.517 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1667.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1664.3 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1664.3 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.16 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1623.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.17 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.62 Safari/537.36',
      'Mozilla/5.0 (X11; CrOS i686 4319.74.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.57 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.2 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1468.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1467.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1464.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1500.55 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Safari/537.36',
      'Mozilla/5.0 (X11; NetBSD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.116 Safari/537.36',
      'Mozilla/5.0 (X11; CrOS i686 3912.101.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.116 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.60 Safari/537.17',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1309.0 Safari/537.17',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.15 (KHTML, like Gecko) Chrome/24.0.1295.0 Safari/537.15',
      'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.14 (KHTML, like Gecko) Chrome/24.0.1292.0 Safari/537.14'],
     'opera': ['Opera/9.80 (X11; Linux i686; Ubuntu/14.10) Presto/2.12.388 Version/12.16',
      'Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.14',
      'Mozilla/5.0 (Windows NT 6.0; rv:2.0) Gecko/20100101 Firefox/4.0 Opera 12.14',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0) Opera 12.14',
      'Opera/12.80 (Windows NT 5.1; U; en) Presto/2.10.289 Version/12.02',
      'Opera/9.80 (Windows NT 6.1; U; es-ES) Presto/2.9.181 Version/12.00',
      'Opera/9.80 (Windows NT 5.1; U; zh-sg) Presto/2.9.181 Version/12.00',
      'Opera/12.0(Windows NT 5.2;U;en)Presto/22.9.168 Version/12.00',
      'Opera/12.0(Windows NT 5.1;U;en)Presto/22.9.168 Version/12.00',
      'Mozilla/5.0 (Windows NT 5.1) Gecko/20100101 Firefox/14.0 Opera/12.0',
      'Opera/9.80 (Windows NT 6.1; WOW64; U; pt) Presto/2.10.229 Version/11.62',
      'Opera/9.80 (Windows NT 6.0; U; pl) Presto/2.10.229 Version/11.62',
      'Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; fr) Presto/2.9.168 Version/11.52',
      'Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; de) Presto/2.9.168 Version/11.52',
      'Opera/9.80 (Windows NT 5.1; U; en) Presto/2.9.168 Version/11.51',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; de) Opera 11.51',
      'Opera/9.80 (X11; Linux x86_64; U; fr) Presto/2.9.168 Version/11.50',
      'Opera/9.80 (X11; Linux i686; U; hu) Presto/2.9.168 Version/11.50',
      'Opera/9.80 (X11; Linux i686; U; ru) Presto/2.8.131 Version/11.11',
      'Opera/9.80 (X11; Linux i686; U; es-ES) Presto/2.8.131 Version/11.11',
      'Mozilla/5.0 (Windows NT 5.1; U; en; rv:1.8.1) Gecko/20061208 Firefox/5.0 Opera 11.11',
      'Opera/9.80 (X11; Linux x86_64; U; bg) Presto/2.8.131 Version/11.10',
      'Opera/9.80 (Windows NT 6.0; U; en) Presto/2.8.99 Version/11.10',
      'Opera/9.80 (Windows NT 5.1; U; zh-tw) Presto/2.8.131 Version/11.10',
      'Opera/9.80 (Windows NT 6.1; Opera Tablet/15165; U; en) Presto/2.8.149 Version/11.1',
      'Opera/9.80 (X11; Linux x86_64; U; Ubuntu/10.10 (maverick); pl) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (X11; Linux i686; U; ja) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (X11; Linux i686; U; fr) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.1; U; zh-tw) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.1; U; sv) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.1; U; en-US) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.1; U; cs) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 6.0; U; pl) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 5.2; U; ru) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 5.1; U;) Presto/2.7.62 Version/11.01',
      'Opera/9.80 (Windows NT 5.1; U; cs) Presto/2.7.62 Version/11.01',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.13) Gecko/20101213 Opera/9.80 (Windows NT 6.1; U; zh-tw) Presto/2.7.62 Version/11.01',
      'Mozilla/5.0 (Windows NT 6.1; U; nl; rv:1.9.1.6) Gecko/20091201 Firefox/3.5.6 Opera 11.01',
      'Mozilla/5.0 (Windows NT 6.1; U; de; rv:1.9.1.6) Gecko/20091201 Firefox/3.5.6 Opera 11.01',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; de) Opera 11.01',
      'Opera/9.80 (X11; Linux x86_64; U; pl) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (X11; Linux i686; U; it) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.6.37 Version/11.00',
      'Opera/9.80 (Windows NT 6.1; U; pl) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.1; U; ko) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.1; U; fi) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.1; U; en-GB) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.1 x64; U; en) Presto/2.7.62 Version/11.00',
      'Opera/9.80 (Windows NT 6.0; U; en) Presto/2.7.39 Version/11.00'],
     'firefox': ['Mozilla/5.0 (Windows NT 6.1; WOW64; rv:40.0) Gecko/20100101 Firefox/40.1',
      'Mozilla/5.0 (Windows NT 6.3; rv:36.0) Gecko/20100101 Firefox/36.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10; rv:33.0) Gecko/20100101 Firefox/33.0',
      'Mozilla/5.0 (X11; Linux i586; rv:31.0) Gecko/20100101 Firefox/31.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:31.0) Gecko/20130401 Firefox/31.0',
      'Mozilla/5.0 (Windows NT 5.1; rv:31.0) Gecko/20100101 Firefox/31.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:29.0) Gecko/20120101 Firefox/29.0',
      'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:25.0) Gecko/20100101 Firefox/29.0',
      'Mozilla/5.0 (X11; OpenBSD amd64; rv:28.0) Gecko/20100101 Firefox/28.0',
      'Mozilla/5.0 (X11; Linux x86_64; rv:28.0) Gecko/20100101  Firefox/28.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:27.3) Gecko/20130101 Firefox/27.3',
      'Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:27.0) Gecko/20121011 Firefox/27.0',
      'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:25.0) Gecko/20100101 Firefox/25.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:25.0) Gecko/20100101 Firefox/25.0',
      'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:24.0) Gecko/20100101 Firefox/24.0',
      'Mozilla/5.0 (Windows NT 6.0; WOW64; rv:24.0) Gecko/20100101 Firefox/24.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:24.0) Gecko/20100101 Firefox/24.0',
      'Mozilla/5.0 (Windows NT 6.2; rv:22.0) Gecko/20130405 Firefox/23.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:23.0) Gecko/20130406 Firefox/23.0',
      'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:23.0) Gecko/20131011 Firefox/23.0',
      'Mozilla/5.0 (Windows NT 6.2; rv:22.0) Gecko/20130405 Firefox/22.0',
      'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:22.0) Gecko/20130328 Firefox/22.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:22.0) Gecko/20130405 Firefox/22.0',
      'Mozilla/5.0 (Microsoft Windows NT 6.2.9200.0); rv:22.0) Gecko/20130405 Firefox/22.0',
      'Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:16.0.1) Gecko/20121011 Firefox/21.0.1',
      'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:16.0.1) Gecko/20121011 Firefox/21.0.1',
      'Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:21.0.0) Gecko/20121011 Firefox/21.0.0',
      'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:21.0) Gecko/20130331 Firefox/21.0',
      'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (X11; Linux i686; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.2; WOW64; rv:21.0) Gecko/20130514 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.2; rv:21.0) Gecko/20130326 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:21.0) Gecko/20130401 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:21.0) Gecko/20130331 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:21.0) Gecko/20130330 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:21.0) Gecko/20130401 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:21.0) Gecko/20130328 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 5.1; rv:21.0) Gecko/20130401 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 5.1; rv:21.0) Gecko/20130331 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 5.1; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 5.0; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:21.0) Gecko/20100101 Firefox/21.0',
      'Mozilla/5.0 (Windows NT 6.2; Win64; x64;) Gecko/20100101 Firefox/20.0',
      'Mozilla/5.0 (Windows x86; rv:19.0) Gecko/20100101 Firefox/19.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:6.0) Gecko/20100101 Firefox/19.0',
      'Mozilla/5.0 (Windows NT 6.1; rv:14.0) Gecko/20100101 Firefox/18.0.1',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:18.0)  Gecko/20100101 Firefox/18.0',
      'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:17.0) Gecko/20100101 Firefox/17.0.6'],
     'internetexplorer': ['Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; AS; rv:11.0) like Gecko',
      'Mozilla/5.0 (compatible, MSIE 11, Windows NT 6.3; Trident/7.0;  rv:11.0) like Gecko',
      'Mozilla/5.0 (compatible; MSIE 10.6; Windows NT 6.1; Trident/5.0; InfoPath.2; SLCC1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727) 3gpp-gba UNTRUSTED/1.0',
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 7.0; InfoPath.3; .NET CLR 3.1.40767; Trident/6.0; en-IN)',
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)',
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)',
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/5.0)',
      'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/4.0; InfoPath.2; SV1; .NET CLR 2.0.50727; WOW64)',
      'Mozilla/5.0 (compatible; MSIE 10.0; Macintosh; Intel Mac OS X 10_7_3; Trident/6.0)',
      'Mozilla/4.0 (Compatible; MSIE 8.0; Windows NT 5.2; Trident/6.0)',
      'Mozilla/4.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/5.0)',
      'Mozilla/1.22 (compatible; MSIE 10.0; Windows 3.1)',
      'Mozilla/5.0 (Windows; U; MSIE 9.0; WIndows NT 9.0; en-US))',
      'Mozilla/5.0 (Windows; U; MSIE 9.0; Windows NT 9.0; en-US)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 7.1; Trident/5.0)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; Media Center PC 6.0; InfoPath.3; MS-RTC LM 8; Zune 4.7)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; Media Center PC 6.0; InfoPath.3; MS-RTC LM 8; Zune 4.7',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Zune 4.0; InfoPath.3; MS-RTC LM 8; .NET4.0C; .NET4.0E)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; chromeframe/12.0.742.112)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Zune 4.0; Tablet PC 2.0; InfoPath.3; .NET4.0C; .NET4.0E)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; yie8)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; .NET CLR 1.1.4322; .NET4.0C; Tablet PC 2.0)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; FunWebProducts)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; chromeframe/13.0.782.215)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; chromeframe/11.0.696.57)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0) chromeframe/10.0.648.205',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/4.0; GTB7.4; InfoPath.1; SV1; .NET CLR 2.8.52393; WOW64; en-US)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; Trident/5.0; chromeframe/11.0.696.57)',
      'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; Trident/4.0; GTB7.4; InfoPath.3; SV1; .NET CLR 3.1.76908; WOW64; en-US)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; GTB7.4; InfoPath.2; SV1; .NET CLR 3.3.69573; WOW64; en-US)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 1.0.3705; .NET CLR 1.1.4322)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; InfoPath.1; SV1; .NET CLR 3.8.36217; WOW64; en-US)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; .NET CLR 2.7.58687; SLCC2; Media Center PC 5.0; Zune 3.4; Tablet PC 3.6; InfoPath.3)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.2; Trident/4.0; Media Center PC 4.0; SLCC1; .NET CLR 3.0.04320)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; SLCC1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 1.1.4322)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; InfoPath.2; SLCC1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.1; SLCC1; .NET CLR 1.1.4322)',
      'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 5.0; Trident/4.0; InfoPath.1; SV1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 3.0.04506.30)',
      'Mozilla/5.0 (compatible; MSIE 7.0; Windows NT 5.0; Trident/4.0; FBSMTWB; .NET CLR 2.0.34861; .NET CLR 3.0.3746.3218; .NET CLR 3.5.33652; msn OptimizedIE8;ENUS)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.2; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; Media Center PC 6.0; InfoPath.2; MS-RTC LM 8)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; Media Center PC 6.0; InfoPath.2; MS-RTC LM 8',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; Media Center PC 6.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; InfoPath.3; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; .NET CLR 3.0.30729; MS-RTC LM 8)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; InfoPath.2)',
      'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Zune 3.0)'],
     'safari': ['Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.75.14 (KHTML, like Gecko) Version/7.0.3 Safari/7046A194A',
      'Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/537.13+ (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_3) AppleWebKit/534.55.3 (KHTML, like Gecko) Version/5.1.3 Safari/534.53.10',
      'Mozilla/5.0 (iPad; CPU OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko ) Version/5.1 Mobile/9B176 Safari/7534.48.3',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_8; de-at) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_7; da-dk) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; tr-TR) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; ko-KR) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; fr-FR) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; cs-CZ) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; ja-JP) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10_5_8; zh-cn) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10_5_8; ja-jp) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_7; ja-jp) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; zh-cn) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; sv-se) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; ko-kr) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; ja-jp) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; it-it) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; fr-fr) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; es-es) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; en-us) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; en-gb) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; de-de) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; sv-SE) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; ja-JP) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; de-DE) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; hu-HU) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; de-DE) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; ru-RU) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; ja-JP) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; it-IT) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_7; en-us) AppleWebKit/534.16+ (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; fr-ch) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_5; de-de) AppleWebKit/534.15+ (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_5; ar) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Android 2.2; Windows; U; Windows NT 6.1; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; tr-TR) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; nb-NO) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 6.0; fr-FR) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-TW) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Windows; U; Windows NT 5.1; ru-RU) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5',
      'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_8; zh-cn) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5']}
    random_headers = [
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (X11; Linux i686 on x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.67 Safari/537.36 OPR/56.0.3051.104'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.62 Safari/537.36 OPR/54.0.2952.64'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:58.0.2) Gecko/20100101 Firefox/58.0.2'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.67 Safari/537.36 OPR/56.0.3051.104'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (X11; Linux i686 on x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.80 Safari/537.36 OPR/57.0.3098.116'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (X11; Linux i686 on x86_64; rv:51.0) Gecko/20100101 Firefox/51.0'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Windows NT 6.0; WOW64; rv:65.0) Gecko/20100101 Firefox/65.0'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36'},
    {'Accept': '*/*', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1; rv:52.1.0) Gecko/20100101 Firefox/52.1.0'},
    ]
    browser = random.choice(list(random_user_agents.keys()))
    user_agent = random.choice(random_user_agents[browser])
    header = copy.copy(random.choice(random_headers))
    header['User-Agent'] = user_agent
    return header


def find_best_session():

    for i in range(10):
        try:
            # print('獲取新的Session 第', i, '回合')
            headers = generate_random_header()
            ses = requests.Session()
            ses.get('https://www.twse.com.tw/zh/', headers=headers, timeout=10, verify=False)
            ses.headers.update(headers)
            # print('成功！')
            return ses
        except (ConnectionError, ReadTimeout) as error:
            # print(error)
            # print('失敗，10秒後重試')
            time.sleep(10)

    print('您的網頁IP已經被證交所封鎖，請更新IP來獲取解鎖')
    print("　手機：開啟飛航模式，再關閉，即可獲得新的IP")
    print("數據機：關閉然後重新打開數據機的電源")

date_range_record_file = os.path.join('history', 'date_range.pickle')

ses = None
def requests_get(*args1, **args2):
    """簡化的網路請求函數 - 直接使用 requests，避免 session 問題"""
    
    import requests
    import random
    import time
    
    # 生成隨機請求頭
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ]
    
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # 合併請求頭
    if 'headers' in args2:
        headers.update(args2['headers'])
    args2['headers'] = headers

    # 設置默認參數，避免衝突
    args2.setdefault('timeout', 30)
    args2.setdefault('verify', False)

    # 隨機延遲
    delay = random.uniform(2, 5)
    time.sleep(delay)

    # 重試機制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 直接使用 requests.get，避免參數衝突
            response = requests.get(*args1, **args2)
            
            # 檢查響應狀態
            if response.status_code == 200:
                return response
            elif response.status_code == 429:  # Too Many Requests
                print(f"⚠️ 請求過於頻繁，等待60秒...")
                time.sleep(60)
                continue
            elif response.status_code in [403, 404]:
                print(f"⚠️ 訪問被拒絕或頁面不存在 (狀態碼: {response.status_code})")
                return None  # 不重試這類錯誤
            else:
                print(f"⚠️ HTTP錯誤 {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(10)
                    continue
                
        except requests.exceptions.Timeout:
            print(f"🔄 請求超時 (嘗試 {attempt+1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(15)
                continue
                
        except requests.exceptions.ConnectionError as error:
            print(f"🔄 連接錯誤 (嘗試 {attempt+1}/{max_retries}): {str(error)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(20)
                continue
                
        except Exception as error:
            print(f"❌ 未知錯誤 (嘗試 {attempt+1}/{max_retries}): {str(error)[:50]}...")
            if attempt < max_retries - 1:
                time.sleep(10)
                continue
    
    print("❌ 所有重試都失敗")
    return None

### ----------
###   Helper
### ----------

def otc_date_str(date):
    """將datetime.date轉換成民國曆

    Args:
        date (datetime.date): 西元歷的日期

    Returns:
        str: 民國歷日期 ex: 109/01/01
    """
    return str(date.year - 1911) + date.strftime('%Y/%m/%d')[4:]


def combine_index(df, n1, n2):

    """將dataframe df中的股票代號與股票名稱合併

    Keyword arguments:

    Args:
        df (pandas.DataFrame): 此dataframe含有column n1, n2
        n1 (str): 股票代號
        n2 (str): 股票名稱

    Returns:
        df (pandas.DataFrame): 此dataframe的index為「股票代號+股票名稱」
    """

    return df.set_index(df[n1].astype(str).str.replace(' ', '') + \
        ' ' + df[n2].astype(str).str.replace(' ', '')).drop([n1, n2], axis=1)

def crawl_benchmark(date):
    """
    爬取台股指數benchmark資料 - 增強版本
    支援完整的時間戳格式和錯誤處理
    """
    date_str = date.strftime('%Y%m%d')
    url = f"https://www.twse.com.tw/rwd/zh/TAIEX/MI_5MINS_INDEX?response=csv&date={date_str}&_=1544020420045"

    try:
        res = requests_get(url)

        # 檢查回應是否有效
        if res is None or not hasattr(res, 'text') or len(res.text) < 100:
            print(f"⚠️ {date.strftime('%Y-%m-%d')} 無有效回應")
            return pd.DataFrame()

        # 檢查是否有資料
        if "很抱歉" in res.text or "查無資料" in res.text:
            print(f"⚠️ {date.strftime('%Y-%m-%d')} 查無資料")
            return pd.DataFrame()

        # 解析CSV資料
        df = pd.read_csv(StringIO(res.text.replace("=","")), header=1, index_col='時間')

        # 資料處理
        df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
        df.columns = df.columns.str.replace(' ', '')

        # 處理時間索引 - 建立完整的時間戳
        df.index = pd.to_datetime(date.strftime('%Y %m %d ') + pd.Series(df.index))

        # 數值處理 - 移除逗號並轉換為浮點數
        df = df.apply(lambda s: s.astype(str).str.replace(",", "").astype(float))

        # 重置索引並添加stock_id
        df = df.reset_index().rename(columns={'時間':'date'})
        df['stock_id'] = '台股指數'

        # 設置MultiIndex
        result_df = df.set_index(['stock_id', 'date'])

        print(f"✅ {date.strftime('%Y-%m-%d')} 成功爬取 {len(result_df)} 筆資料，{len(result_df.columns)} 個指數欄位")
        return result_df

    except Exception as e:
        print(f"❌ {date.strftime('%Y-%m-%d')} 爬取失敗: {str(e)[:50]}...")
        return pd.DataFrame()

def crawl_capital():
    res = requests_get('https://dts.twse.com.tw/opendata/t187ap03_L.csv', )
    res.encoding = 'utf-8'
    df = pd.read_csv(StringIO(res.text))
    time.sleep(10)
    res = requests_get('https://dts.twse.com.tw/opendata/t187ap03_O.csv')
    res.encoding = 'utf-8'
    df = pd.concat([df, pd.read_csv(StringIO(res.text))])

    df.columns = df.columns.str.replace(' ', '')

    df['date'] = pd.to_datetime(str(datetime.datetime.now().year) + df['出表日期'].str[3:])
    df.set_index([df['公司代號'].astype(str) + ' ' + df['公司簡稱'].astype(str), 'date'], inplace=True)
    df.index.levels[0].name = '股票名稱'
    return df


def interest():
    res = requests_get('https://www.twse.com.tw/exchangeReport/TWT48U_ALL?response=open_data')
    res.encoding = 'utf-8'
    df = pd.read_csv(StringIO(res.text))

    time.sleep(10)

    res = requests_get('https://www.tpex.org.tw/web/stock/exright/preAnnounce/prepost_result.php?l=zh-tw&o=data')
    res.encoding = 'utf-8'
    df = pd.concat([df, pd.read_csv(StringIO(res.text))])
    df.columns = df.columns.str.replace(' ', '')

    df['date'] = df['除權息日期'].str.replace('年', '/').str.replace('月', '/').str.replace('日', '')
    df['date'] = pd.to_datetime(str(datetime.datetime.now().year) + df['date'].str[3:])
    df = df.set_index([df['股票代號'].astype(str) + ' ' + df['名稱'].astype(str), 'date'])
    return df


def preprocess(df, date):
    df = df.dropna(axis=1, how='all').dropna(axis=0, how='all')
    df.columns = df.columns.str.replace(' ', '')

    # 設置索引名稱，如果還沒有的話
    if df.index.name is None:
        df.index.name = 'stock_id'

    df.columns.name = ''
    df['date'] = pd.to_datetime(date)

    # 重置索引並設置新的多重索引
    df = df.reset_index()

    # 確保有正確的欄位名稱
    index_col_name = df.columns[0]  # 第一個欄位應該是股票相關的
    if index_col_name != 'stock_id':
        df = df.rename(columns={index_col_name: 'stock_id'})

    df = df.set_index(['stock_id', 'date'])
    df = df.apply(lambda s: s.astype(str).str.replace(',',''))

    return df



def bargin_twe(date):
    datestr = date.strftime('%Y%m%d')

    res = requests_get('https://www.twse.com.tw/rwd/zh/fund/T86?response=csv&date='\
                       +datestr+'&selectType=ALLBUT0999')

    # 檢查請求是否成功
    if res is None:
        return pd.DataFrame()

    try:
        df = pd.read_csv(StringIO(res.text.replace('=','')), header=1)
    except:
        return pd.DataFrame()

    df.columns = df.columns.str.replace(' ', '')
    df = combine_index(df, '證券代號', '證券名稱')
    df = preprocess(df, date)
    return df

def bargin_otc(date):
    datestr = otc_date_str(date)

    url = 'https://www.tpex.org.tw/web/stock/3insti/daily_trade/3itrade_hedge_result.php?l=zh-tw&o=csv&se=EW&t=D&d='+datestr+'&s=0,asc'
    res = requests_get(url)

    # 檢查請求是否成功
    if res is None:
        return pd.DataFrame()

    try:
        df = pd.read_csv(StringIO(res.text), header=1)
    except:
        return pd.DataFrame()

    df.columns = df.columns.str.replace(' ', '')
    df = combine_index(df, '代號', '名稱')
    df = preprocess(df, date)
    return df

def price_twe(date):
    """證交所股價資料 - 直接使用 requests.get() (參考 price_crawler_auto(HL).py)"""
    import requests
    import random
    import urllib3

    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    date_str = date.strftime('%Y%m%d')

    # 使用 JSON API (參考 price_crawler_auto(HL).py)
    url = "https://www.twse.com.tw/exchangeReport/MI_INDEX"
    params = {
        "response": "json",
        "date": date_str,
        "type": "ALLBUT0999"  # 保持原有的類型
    }

    # 設置 headers (參考成功爬蟲)
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    for attempt in range(3):  # 重試3次
        try:
            # 隨機延遲 (參考成功爬蟲)
            time.sleep(random.uniform(2, 5))

            # 直接使用 requests.get (參考成功爬蟲)
            res = requests.get(url, params=params, headers=headers, timeout=30, verify=False)

            if res.status_code == 429:
                print("⚠️ TWSE 回應 429 Too Many Requests，等待60秒後重試")
                time.sleep(60)
                continue

            res.raise_for_status()

            try:
                response_json = res.json()
            except Exception as json_e:
                if attempt == 2:  # 只在最後一次失敗時顯示
                    print(f"⚠️ JSON 解析失敗: {str(json_e)[:50]}...")
                continue

            # 檢查回應狀態 (參考成功爬蟲)
            if response_json.get("stat") == "很抱歉，沒有符合條件的資料!":
                return pd.DataFrame()

            df = pd.DataFrame()
            colname = []

            # 處理不同的回應結構 (參考成功爬蟲)
            if "tables" in response_json:
                for table in response_json["tables"]:
                    title = table.get("title", "")
                    if "每日收盤行情" in title or "報價行情" in title:
                        df = pd.DataFrame(table.get("data", []))
                        colname = table.get("fields", [])
                        break
            elif "data9" in response_json and "fields9" in response_json:
                df = pd.DataFrame(response_json["data9"])
                colname = response_json["fields9"]
            elif "data8" in response_json and "fields8" in response_json:
                df = pd.DataFrame(response_json["data8"])
                colname = response_json["fields8"]
            else:
                continue  # 重試

            if not df.empty and colname:
                # 設置欄位名稱
                df.columns = colname
                # 保持原有的處理邏輯
                df.columns = df.columns.str.replace(' ', '')
                df = df.apply(lambda s: s.str.replace(',','') if hasattr(s, 'str') else s)
                df = combine_index(df, '證券代號', '證券名稱')
                df = preprocess(df, date)
                return df

        except Exception as e:
            if attempt == 2:  # 只在最後一次失敗時顯示
                print(f"⚠️ price_twe 嘗試失敗: {str(e)[:50]}...")
            if attempt < 2:
                time.sleep(10)

    # 如果 JSON 失敗，回退到原來的 CSV 方式
    return price_twe_csv_fallback(date)

def price_twe_csv_fallback(date):
    """CSV 回退方案 - 原有的處理邏輯"""
    date_str = date.strftime('%Y%m%d')
    res = requests_get('https://www.twse.com.tw/exchangeReport/MI_INDEX?response=csv&date='+date_str+'&type=ALLBUT0999')

    if res is None:
        return pd.DataFrame()

    if not hasattr(res, 'text') or not res.text or len(res.text.strip()) < 50:
        return pd.DataFrame()

    if "查詢日期小於" in res.text or "沒有符合條件的資料" in res.text:
        return pd.DataFrame()

    lines = res.text.strip().split('\n')
    if len(lines) < 5:
        return pd.DataFrame()

    try:
        # 找到包含 '證券代號' 的行號
        lines = res.text.split('\n')[:500]
        header = next((i for i, line in enumerate(lines) if '證券代號' in line), 0)
        df = pd.read_csv(StringIO(res.text.replace('=','')), header=header-1)
        df.columns = df.columns.str.replace(' ', '')
        df = df.apply(lambda s: s.str.replace(',','') if hasattr(s, 'str') else s)
        df = combine_index(df, '證券代號', '證券名稱')
        df = preprocess(df, date)
        return df
    except Exception as e:
        print(f"⚠️ CSV 回退也失敗: {str(e)[:50]}...")
        return pd.DataFrame()

    # 找到包含 '證券代號' 的行號
    lines = res.text.split('\n')[:500]
    header = next((i for i, line in enumerate(lines) if '證券代號' in line), 0)
    df = pd.read_csv(StringIO(res.text.replace('=', '')), header=header - 1)
    df.columns = df.columns.str.replace(' ', '')

    # check if df[x] is str then remove ' '
    df = df.apply(lambda s: s.str.replace(',','') if hasattr(s, 'str') else s)
    # 找到包含 '證券代號' 的行號
    lines = res.text.split('\n')[:500]
    header = next((i for i, line in enumerate(lines) if '證券代號' in line), 0)

    df = pd.read_csv(StringIO(res.text.replace('=','')), header=header-1)
    df.columns = df.columns.str.replace(' ', '')
    df = combine_index(df, '證券代號', '證券名稱')
    df = preprocess(df, date)
    return df

def price_otc(date):
    """櫃買中心股價資料 - 直接使用 requests.get() (參考 price_crawler_auto(HL).py)"""
    import requests
    import random
    import urllib3

    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 轉換為民國年格式 (參考成功爬蟲)
    roc_year = date.year - 1911
    roc_date = f"{roc_year}/{date.month:02d}/{date.day:02d}"

    # 使用 JSON API (參考成功爬蟲)
    url = (
        "https://www.tpex.org.tw/web/stock/aftertrading/"
        "otc_quotes_no1430/stk_wn1430_result.php?"
        f"l=zh-tw&d={roc_date}&se=AL"
    )

    # 設置 headers (參考成功爬蟲)
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    for attempt in range(3):  # 重試3次
        try:
            # 對 TPEX 使用更長的延遲 (因為它比較不穩定)
            time.sleep(random.uniform(3, 8))

            # 直接使用 requests.get (參考成功爬蟲)
            res = requests.get(url, headers=headers, timeout=30, allow_redirects=False, verify=False)

            if res.status_code == 302:
                print(f"⚠️ TPEX 回應 302 redirect (可能無該日期資料)")
                return pd.DataFrame()

            if res.status_code == 429:
                print("⚠️ TPEX 回應 429 Too Many Requests，等待60秒後重試")
                time.sleep(60)
                continue

            res.raise_for_status()

            try:
                response_json = res.json()
            except Exception as json_e:
                if attempt == 2:  # 只在最後一次失敗時顯示
                    print(f"⚠️ JSON 解析失敗: {str(json_e)[:50]}...")
                continue

            # 處理回應結構 (參考成功爬蟲)
            tables = response_json.get("tables", [])
            if not tables:
                continue

            table_data = None
            for t in tables:
                if "上櫃股票每日收盤行情" in t.get("title", ""):
                    table_data = t
                    break

            if table_data is None:
                continue

            data = table_data.get("data", [])
            fields = table_data.get("fields", [])

            if not data or not fields:
                continue

            df = pd.DataFrame(data, columns=fields)

            if not df.empty:
                # 保持原有的處理邏輯和欄位名稱
                df.columns = df.columns.str.replace(' ', '')
                # 將 JSON API 的欄位名稱轉換為原有格式
                if '代號' in df.columns and '名稱' in df.columns:
                    df = combine_index(df, '代號', '名稱')
                    df = preprocess(df, date)
                    return df

        except Exception as e:
            if attempt == 2:  # 只在最後一次失敗時顯示
                print(f"⚠️ price_otc JSON 失敗: {str(e)[:50]}...")
            if attempt < 2:
                # TPEX 需要更長的等待時間
                time.sleep(15)

    # 如果 JSON 失敗，回退到原來的 CSV 方式
    return price_otc_csv_fallback(date)

def price_otc_csv_fallback(date):
    """CSV 回退方案 - 原有的處理邏輯"""
    datestr = otc_date_str(date)
    link = 'https://www.tpex.org.tw/web/stock/aftertrading/daily_close_quotes/stk_quote_download.php?l=zh-tw&d='+datestr+'&s=0,asc,0'
    res = requests_get(link)

    if res is None:
        return pd.DataFrame()

    if not hasattr(res, 'text') or not res.text or len(res.text.strip()) < 50:
        return pd.DataFrame()

    lines = res.text.strip().split('\n')
    if len(lines) < 5:
        return pd.DataFrame()

    try:
        df = pd.read_csv(StringIO(res.text), header=2)
        if len(df) < 30:
            return pd.DataFrame()
        df.columns = df.columns.str.replace(' ', '')
        df = combine_index(df, '代號', '名稱')
        df = preprocess(df, date)
        return df
    except Exception as e:
        print(f"⚠️ CSV 回退也失敗: {str(e)[:50]}...")
        # 嘗試不同的 header 設定
        try:
            df = pd.read_csv(StringIO(res.text), header=1)
            df.columns = df.columns.str.replace(' ', '')
            df = combine_index(df, '代號', '名稱')
            df = preprocess(df, date)
            return df
        except:
            return pd.DataFrame()

    except Exception as e:
        # 如果 JSON 失敗，回退到原來的 CSV 方式
        link = 'https://www.tpex.org.tw/web/stock/aftertrading/daily_close_quotes/stk_quote_download.php?l=zh-tw&d='+datestr+'&s=0,asc,0'
        res = requests_get(link)

        if res is None:
            return pd.DataFrame()

        # 檢查返回的資料是否有效
        if not hasattr(res, 'text') or not res.text or len(res.text.strip()) < 50:
            return pd.DataFrame()

        # 檢查資料行數
        lines = res.text.strip().split('\n')
        if len(lines) < 5:
            return pd.DataFrame()

        try:
            df = pd.read_csv(StringIO(res.text), header=2)
        except Exception as csv_e:
            # 嘗試不同的 header 設定
            try:
                df = pd.read_csv(StringIO(res.text), header=1)
            except:
                try:
                    df = pd.read_csv(StringIO(res.text), header=0)
                except:
                    return pd.DataFrame()

    if len(df) < 30:
        return pd.DataFrame()

    df.columns = df.columns.str.replace(' ', '')
    df = combine_index(df, '代號', '名稱')
    df = preprocess(df, date)
    df = df[df['成交筆數'].str.replace(' ', '') != '成交筆數']
    return df

def pe_twe(date):
    datestr = date.strftime('%Y%m%d')
    res = requests_get('https://www.twse.com.tw/exchangeReport/BWIBBU_d?response=csv&date='+datestr+'&selectType=ALL')

    # 檢查請求是否成功
    if res is None:
        return pd.DataFrame()

    try:
        df = pd.read_csv(StringIO(res.text), header=1)
    except:
        return pd.DataFrame()

    df.columns = df.columns.str.replace(' ', '')
    df = combine_index(df, '證券代號', '證券名稱')
    df = preprocess(df, date)
    return df

def pe_otc(date):
    datestr = otc_date_str(date)
    res = requests_get('https://www.tpex.org.tw/web/stock/aftertrading/peratio_analysis/pera_result.php?l=zh-tw&o=csv&charset=UTF-8&d='+datestr+'&c=&s=0,asc')

    # 檢查請求是否成功
    if res is None:
        return pd.DataFrame()

    try:
        df = pd.read_csv(StringIO(res.text), header=3)
        df.columns = df.columns.str.replace(' ', '')
        df = combine_index(df, '股票代號', '公司名稱')
        df = preprocess(df, date)
    except Exception as e:
        print(f"❌ pe_otc 資料處理錯誤: {e}")
        print(f"📋 回應內容預覽: {res.text[:200]}...")
        return pd.DataFrame()

    return df

def month_revenue(name, date):
    """改進的月營收爬蟲 - 直接使用方案3（MOPS備用格式）"""

    year = date.year - 1911
    month = (date.month+10)%12+1
    if month == 12:
        year -= 1

    # 直接使用方案3: 備用URL格式（最穩定的方案）
    result = month_revenue_mops_alternative(name, year, month, date)
    if len(result) > 0:
        return result

    return pd.DataFrame()

def month_revenue_mops_direct(name, year, month, date):
    """方案1: 原始 MOPS 直接URL"""
    url = 'https://mops.twse.com.tw/nas/t21/%s/t21sc03_%d_%d.html' % (name, year, month)
    print(f'   方案1: {url}')

    res = requests_get(url, verify=False)
    if res is None:
        return pd.DataFrame()

    res.encoding = 'big5'

    try:
        dfs = pd.read_html(StringIO(res.text), encoding='big-5')
        return process_month_revenue_data(dfs, name, date)
    except Exception as e:
        print(f'   方案1解析失敗: {str(e)[:50]}...')
        return pd.DataFrame()

def month_revenue_mops_post(name, year, month, date):
    """方案2: MOPS POST 查詢方式"""
    try:
        # POST 查詢URL
        url = 'https://mops.twse.com.tw/nas/t21/sii/t21sc03.html'
        print(f'   方案2: POST查詢 {url}')

        # POST 參數
        data = {
            'encodeURIComponent': '1',
            'step': '1',
            'firstin': '1',
            'off': '1',
            'year': str(year),
            'month': str(month),
            'keyword4': '',
            'code1': '',
            'TYPEK2': '',
            'checkbtn': '',
            'queryName': 'co_id',
            'inpuType': 'co_id',
            'TYPEK': name,
            'isnew': 'false'
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://mops.twse.com.tw/nas/t21/sii/t21sc03.html'
        }

        res = requests.post(url, data=data, headers=headers, verify=False, timeout=30)
        if res is None or res.status_code != 200:
            return pd.DataFrame()

        res.encoding = 'big5'
        dfs = pd.read_html(StringIO(res.text), encoding='big-5')
        return process_month_revenue_data(dfs, name, date)

    except Exception as e:
        print(f'   方案2失敗: {str(e)[:50]}...')
        return pd.DataFrame()

def month_revenue_mops_alternative(name, year, month, date):
    """方案3: 備用URL格式 - 優化版本，優先使用最穩定的URL"""
    try:
        # 優化的URL順序，優先使用最穩定的格式
        urls = [
            f'https://mopsov.twse.com.tw/nas/t21/{name}/t21sc03_{year}_{month}.html',  # 最穩定
            f'https://mops.twse.com.tw/nas/t21/{name}/t21sc03_{year}_{month}_0.html',
            f'https://mops.twse.com.tw/server-java/t21/{name}/t21sc03_{year}_{month}.html'
        ]

        for i, url in enumerate(urls, 1):
            res = requests_get(url, verify=False)

            if res is None or res.status_code != 200:
                continue

            res.encoding = 'big5'
            try:
                dfs = pd.read_html(StringIO(res.text), encoding='big-5')
                result = process_month_revenue_data(dfs, name, date)
                if len(result) > 0:
                    return result
            except Exception:
                continue

        return pd.DataFrame()

    except Exception:
        return pd.DataFrame()

def is_valid_stock_id_for_crawler(stock_id):
    """判斷是否為有效的台股代碼（爬蟲專用）"""
    if not stock_id or pd.isna(stock_id):
        return False

    stock_str = str(stock_id).strip()

    # 排除明顯無效的記錄
    invalid_patterns = [
        '總計', '合計', '小計', 'NULL', 'None', '',
        '上市', '上櫃', '興櫃', '公開發行'
    ]

    if stock_str in invalid_patterns:
        return False

    # 提取股票代碼部分（如果包含名稱，取空格前的部分）
    code_part = stock_str.split(' ')[0] if ' ' in stock_str else stock_str

    # 檢查是否為純數字
    if not code_part.isdigit():
        return False

    code_len = len(code_part)

    # 4位數股票代碼：1000-9999（上市上櫃）
    if code_len == 4:
        code_int = int(code_part)
        return 1000 <= code_int <= 9999

    # 5位數ETF代碼：00xxx格式（如 00878, 00919）
    elif code_len == 5 and code_part.startswith('00'):
        code_int = int(code_part)
        return 1 <= code_int <= 99999  # ETF代碼範圍

    # 6位數股票代碼：700000-899999（興櫃）
    elif code_len == 6:
        code_int = int(code_part)
        return 700000 <= code_int <= 899999

    # 其他長度都不是有效的台股代碼
    return False

def process_month_revenue_data(dfs, name, date):
    """處理月營收資料的通用函數"""
    try:
        df = pd.concat([df for df in dfs if df.shape[1] <= 11 and df.shape[1] > 5])
        df = df.rename(columns={'公司 代號':'公司代號'})

        if 'levels' in dir(df.columns):
            df.columns = df.columns.get_level_values(1)
            df.columns = df.columns.str.replace(' ', '')
        else:
            df = df[list(range(0,10))]
            column_index = df.index[(df[0] == '公司代號')][0]
            df.columns = df.iloc[column_index]

        df = df.loc[:,~df.columns.isnull()]
        df = df.loc[~pd.to_numeric(df['當月營收'], errors='coerce').isnull()]
        df = df[df['公司代號'] != '合計']

        # 過濾掉無效的股票代碼
        if '公司代號' in df.columns:
            df = df[df['公司代號'].apply(is_valid_stock_id_for_crawler)]

        df = combine_index(df, '公司代號', '公司名稱')
        df = preprocess(df, datetime.date(date.year, date.month, 10))
        return df.drop_duplicates()

    except Exception as e:
        return pd.DataFrame()

def crawl_split_twe():

    res = requests_get('https://www.twse.com.tw/exchangeReport/TWTAVU?response=csv&_=1537824706232')

    df = pd.read_csv(StringIO(res.text),header=1)
    df = df.dropna(how='all', axis=1).dropna(thresh=3, axis=0)

    def process_date(s):
        return pd.to_datetime(str(datetime.datetime.now().year) + s.str[3:])

    df.columns = df.columns.str.replace(' ', '')
    df['停止買賣日期'] = process_date(df['停止買賣日期'])
    df['恢復買賣日期'] = process_date(df['恢復買賣日期'])
    df['股票代號'] = df['股票代號'].astype(int).astype(str)
    df['stock_id'] = df['股票代號'] + ' ' + df['名稱']
    df['date'] = df['恢復買賣日期']
    df = df.set_index(['stock_id', 'date'])

    return df


def crawl_split_otc():
    res = requests_get("https://www.tpex.org.tw/web/stock/exright/decap/decap_download.php?l=zh-tw&d=107/09/21&s=0,asc,0")
    df = pd.read_csv(StringIO(res.text), header=1)
    df = df.dropna(thresh=5, axis=0)
    df.columns = df.columns.str.replace(' ', '')
    df['stock_id'] = df['代號'] + ' ' + df['名稱']
    def process_date(s):
        ss = s.astype(int).astype(str)
        return pd.to_datetime(str(datetime.datetime.now().year) + '/' + ss.str[3:5] + '/' + ss.str[5:])

    df['停止買賣日期'] = process_date(df['停止買賣日期'])
    df['恢復買賣日期'] = process_date(df['恢復買賣日期'])
    df['date'] = df['恢復買賣日期']
    df = df.rename(columns={'代號':'股票代號'})
    df = df.set_index(['stock_id', 'date'])
    return df

import io
import json
import requests
import datetime
import pandas as pd

def crawl_twse_divide_ratio():
    """
    爬取台股除權息資料 - 支援增量更新，保存為DB格式 (保留向後相容性)

    注意: 此函數已整合到 crawl_divide_ratio，建議使用統一的除權息爬蟲

    Returns:
        pandas.DataFrame: 完整的除權息資料 (包含現有資料 + 新資料)
    """
    print("⚠️ crawl_twse_divide_ratio 已整合到 crawl_divide_ratio，建議使用統一函數")
    return _crawl_twse_divide_ratio_internal()



def crawl_divide_ratio():
    """
    統一爬取台股除權息資料 (上市 + 上櫃) - 支援增量更新，保存為DB格式

    Returns:
        pandas.DataFrame: 完整的除權息資料 (包含現有資料 + 新資料)
    """

    print("=" * 80)
    print("📊 統一除權息資料爬蟲 (上市 + 上櫃)")
    print("=" * 80)

    # 爬取上市公司除權息資料
    print("🏛️ 爬取上市公司除權息資料...")
    twse_df = _crawl_twse_divide_ratio_internal()

    # 爬取上櫃公司除權息資料
    print("\n🏪 爬取上櫃公司除權息資料...")
    otc_df = _crawl_otc_divide_ratio_internal()

    # 合併資料
    print("\n🔄 合併上市和上櫃除權息資料...")
    combined_df = _merge_divide_ratio_data(twse_df, otc_df)

    # 儲存統一的除權息資料
    if combined_df is not None and not combined_df.empty:
        _save_unified_divide_ratio(combined_df)
        print(f"✅ 統一除權息資料爬取完成: {len(combined_df)} 筆")
        return combined_df
    else:
        print("⚠️ 無法獲取除權息資料")
        return pd.DataFrame()

def _crawl_twse_divide_ratio_internal():
    """內部函數: 爬取上市公司除權息資料"""

    # 讀取現有資料
    existing_df = None
    db_file = 'D:/Finlab/history/tables/divide_ratio.db'

    # 嘗試從統一DB檔案讀取上市公司資料
    if os.path.exists(db_file):
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            # 檢查是否有 market 欄位來區分上市上櫃
            try:
                existing_df = pd.read_sql("SELECT * FROM divide_ratio WHERE market = 'TWSE'", conn)
            except:
                # 如果沒有 market 欄位，讀取所有資料
                existing_df = pd.read_sql('SELECT * FROM divide_ratio', conn)
            conn.close()

            if not existing_df.empty:
                existing_df['date'] = pd.to_datetime(existing_df['date'])
                existing_df = existing_df.set_index(['stock_id', 'date'])
                print(f"📂 從統一DB讀取上市除權息資料: {len(existing_df)} 筆")

                # 獲取最後日期
                last_date = existing_df.index.get_level_values('date').max()
                print(f"📅 現有上市資料最後日期: {last_date.strftime('%Y-%m-%d')}")

                # 從最後日期的下一天開始爬取
                start_date = last_date + pd.Timedelta(days=1)
                start_datestr = start_date.strftime('%Y%m%d')
            else:
                existing_df = None
                start_datestr = "20180101"
        except Exception as e:
            print(f"⚠️ 讀取統一DB檔案失敗: {e}")
            existing_df = None
            start_datestr = "20180101"
    else:
        print(f"ℹ️ 統一DB檔案不存在，將爬取完整上市資料")
        start_datestr = "20180101"


    # 爬取上市公司除權息資料的邏輯 (基於原 crawl_twse_divide_ratio)
    end_datestr = datetime.datetime.now().strftime('%Y%m%d')
    print(f"🔄 爬取上市除權息期間: {start_datestr} 至 {end_datestr}")

    # 使用原有的上市除權息爬取邏輯 (基於 finlab_analysis/crawler.py)
    try:
        res = requests_get(f"https://www.twse.com.tw/rwd/zh/exRight/TWT49U?response=csv&startDate={start_datestr}&endDate={end_datestr}")

        if res is None or res.status_code != 200:
            print(f"❌ 爬取失敗: HTTP狀態碼 {res.status_code if res else 'None'}")
            return existing_df if existing_df is not None else pd.DataFrame()

        # 檢查回應內容
        if not res.text or len(res.text.strip()) == 0:
            print(f"ℹ️ 該期間無新的除權息資料 (回應內容為空)")
            return existing_df if existing_df is not None else pd.DataFrame()

        # 檢查是否有有效的CSV內容
        text_content = res.text.replace("=", "")
        if "資料日期" not in text_content:
            print(f"⚠️ 回應內容不包含預期的資料格式")
            return existing_df if existing_df is not None else pd.DataFrame()

        try:
            df = pd.read_csv(io.StringIO(text_content), header=1)
            df.columns = df.columns.str.replace(' ', '')
            df = df.dropna(thresh=5).dropna(how='all', axis=1)
            df = df[~df['資料日期'].isnull()]

            if len(df) == 0:
                print(f"✅ 沒有新的上市除權息資料需要更新")
                return existing_df if existing_df is not None else pd.DataFrame()
        except Exception as e:
            print(f"❌ 解析CSV資料失敗: {e}")
            return existing_df if existing_df is not None else pd.DataFrame()

        print(f"📊 爬取到新的上市除權息資料: {len(df)} 筆")

        # 設置股票代號和名稱 (分開存儲)
        df['stock_id'] = df['股票代號']
        df['stock_name'] = df['股票名稱']

        # 設置日期 (只保留西元年月日，不含時分秒)
        df = df[~df['資料日期'].isnull()]
        years = df['資料日期'].str.split('年').str[0].astype(int) + 1911
        years.loc[df['資料日期'].str.find('年') == -1] = np.nan
        years.loc[years > datetime.datetime.now().year] = np.nan
        years.ffill(inplace=True)
        dates = years.astype(int).astype(str) +'/'+ df['資料日期'].str.split('年').str[1].str.replace('月', '/').str.replace('日', '')
        df['date'] = pd.to_datetime(dates, errors='coerce').dt.strftime('%Y-%m-%d')  # 只保留日期字串格式

        # 轉換欄位名稱為英文並移除重複欄位
        column_mapping = {
            '除權息前收盤價': 'close_price_before_ex',
            '除權息參考價': 'ex_dividend_reference_price',
            '權值+息值': 'rights_dividend_value',
            '權/息': 'rights_dividend_type',
            '漲停價格': 'limit_up_price',
            '跌停價格': 'limit_down_price',
            '開盤競價基準': 'opening_reference_price',
            '減除股利參考價': 'dividend_deducted_reference_price',
            '詳細資料': 'detail_info',
            '最近一次申報資料季別/日期': 'latest_report_period',
            '最近一次申報每股(單位)淨值': 'latest_book_value_per_share_text',
            '最近一次申報每股(單位)盈餘': 'latest_earnings_per_share_text'
        }

        # 重新命名欄位
        df = df.rename(columns=column_mapping)

        # 移除重複和不需要的欄位
        columns_to_remove = ['資料日期', '股票代號', '股票名稱']
        df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])

        # 設置數值欄位
        df['latest_book_value_per_share'] = np.nan
        df['latest_earnings_per_share'] = np.nan

        # 轉換為浮點數
        float_columns = [
            'close_price_before_ex', 'ex_dividend_reference_price', 'rights_dividend_value',
            'limit_up_price', 'limit_down_price', 'opening_reference_price',
            'dividend_deducted_reference_price', 'latest_book_value_per_share',
            'latest_earnings_per_share'
        ]

        for col in float_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace(',', '').astype(float)

        # 計算除權息比率
        df['divide_ratio'] = df['close_price_before_ex'] / df['opening_reference_price']

        # 添加市場標識
        df['market'] = 'TWSE'

        # 重新排列欄位順序 (stock_name 放在 stock_id 右側)
        column_order = ['stock_id', 'stock_name'] + [col for col in df.columns if col not in ['stock_id', 'stock_name']]
        df = df[column_order]

        new_df = df.set_index(['stock_id', 'date'])

        # 合併現有資料和新資料
        if existing_df is not None and not existing_df.empty:
            # 合併資料
            combined_df = pd.concat([existing_df, new_df])
            # 去除重複 (保留最新的)
            combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
            # 排序
            combined_df = combined_df.sort_index()
            print(f"📊 合併後上市除權息資料: {len(combined_df)} 筆 (原有 {len(existing_df)} + 新增 {len(new_df)})")
            return combined_df
        else:
            print(f"📊 新建上市除權息資料: {len(new_df)} 筆")
            return new_df

    except Exception as e:
        print(f"❌ 爬取上市除權息資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return existing_df if existing_df is not None else pd.DataFrame()

def _crawl_otc_divide_ratio_internal():
    """內部函數: 爬取上櫃公司除權息資料"""

    # 讀取現有上櫃資料
    existing_df = None
    db_file = 'D:/Finlab/history/tables/divide_ratio.db'

    if os.path.exists(db_file):
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            try:
                existing_df = pd.read_sql("SELECT * FROM divide_ratio WHERE market = 'OTC'", conn)
            except:
                existing_df = pd.DataFrame()
            conn.close()

            if not existing_df.empty:
                existing_df['date'] = pd.to_datetime(existing_df['date'])
                existing_df = existing_df.set_index(['stock_id', 'date'])
                print(f"📂 從統一DB讀取上櫃除權息資料: {len(existing_df)} 筆")

                last_date = existing_df.index.get_level_values('date').max()
                start_year = last_date.year - 1911 + 1
            else:
                start_year = datetime.datetime.now().year - 1911 - 2
        except Exception as e:
            print(f"⚠️ 讀取統一DB檔案失敗: {e}")
            start_year = datetime.datetime.now().year - 1911 - 2
    else:
        start_year = datetime.datetime.now().year - 1911 - 2


    # 爬取上櫃公司除權息資料的邏輯 (基於原 crawl_otc_divide_ratio)
    y = datetime.datetime.now().year
    m = datetime.datetime.now().month
    d = datetime.datetime.now().day

    y_str = str(y-1911)
    m_str = str(m) if m > 9 else '0' + str(m)
    d_str = str(d) if d > 9 else '0' + str(d)

    datestr = '%s/%s/%s' % (y_str, m_str, d_str)
    start_datestr = f'{start_year}/01/02'

    print(f"🔄 爬取上櫃除權息期間: {start_datestr} 至 {datestr}")

    try:
        # 使用原有的上櫃除權息爬取邏輯 (基於 finlab_analysis/crawler.py)
        url = f'https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php?l=zh-tw&d={start_datestr}&ed={datestr}'
        res_otc = requests_get(url)

        if res_otc is None or res_otc.status_code != 200:
            print(f"❌ 櫃買中心API請求失敗")
            return existing_df if existing_df is not None else pd.DataFrame()

        try:
            # 解析JSON回應
            json_data = json.loads(res_otc.text)

            if 'aaData' not in json_data or not json_data['aaData']:
                print(f"✅ 沒有新的上櫃除權息資料需要更新")
                return existing_df if existing_df is not None else pd.DataFrame()

            df = pd.DataFrame(json_data['aaData'])

        except Exception as e:
            print(f"❌ 解析上櫃除權息JSON資料失敗: {e}")
            return existing_df if existing_df is not None else pd.DataFrame()

        print(f"📊 爬取到新的上櫃除權息資料: {len(df)} 筆")

        # 設置欄位名稱 (英文)
        df.columns = [
            'ex_dividend_date_raw', 'stock_code_raw', 'stock_name_raw', 'close_price_before_ex', 'ex_dividend_reference_price',
            'rights_value', 'dividend_value', 'rights_dividend_value', 'rights_dividend_type', 'limit_up_price', 'limit_down_price', 'opening_reference_price',
            'dividend_deducted_reference_price', 'cash_dividend', 'stock_dividend_per_thousand', 'cash_capital_increase_shares', 'cash_capital_increase_price',
            'public_offering_shares', 'employee_subscription_shares', 'existing_shareholder_subscription', 'subscription_ratio_per_thousand'
        ]

        # 設置股票代號和名稱 (分開存儲)
        df['stock_id'] = df['stock_code_raw']
        df['stock_name'] = df['stock_name_raw']

        # 設置日期 (只保留西元年月日，不含時分秒)
        dates = df['ex_dividend_date_raw'].str.split('/')
        dates = (dates.str[0].astype(int) + 1911).astype(str) + '/' + dates.str[1] + '/' + dates.str[2]
        df['date'] = pd.to_datetime(dates).dt.strftime('%Y-%m-%d')  # 只保留日期字串格式

        # 移除重複和不需要的欄位
        columns_to_remove = ['ex_dividend_date_raw', 'stock_code_raw', 'stock_name_raw']
        df = df.drop(columns=columns_to_remove)

        # 轉換數值欄位
        float_columns = [
            'close_price_before_ex', 'ex_dividend_reference_price', 'rights_value', 'dividend_value',
            'rights_dividend_value', 'limit_up_price', 'limit_down_price', 'opening_reference_price',
            'dividend_deducted_reference_price', 'cash_dividend', 'stock_dividend_per_thousand',
            'cash_capital_increase_shares', 'cash_capital_increase_price', 'public_offering_shares',
            'employee_subscription_shares', 'existing_shareholder_subscription', 'subscription_ratio_per_thousand'
        ]

        for col in float_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace(',', '').astype(float)

        # 計算除權息比率
        df['divide_ratio'] = df['close_price_before_ex'] / df['opening_reference_price']

        # 添加市場標識
        df['market'] = 'OTC'

        # 重新排列欄位順序 (stock_name 放在 stock_id 右側)
        column_order = ['stock_id', 'stock_name'] + [col for col in df.columns if col not in ['stock_id', 'stock_name']]
        df = df[column_order]

        new_df = df.set_index(['stock_id', 'date'])

        # 合併現有資料和新資料
        if existing_df is not None and not existing_df.empty:
            # 合併資料
            combined_df = pd.concat([existing_df, new_df])
            # 去除重複 (保留最新的)
            combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
            # 排序
            combined_df = combined_df.sort_index()
            print(f"📊 合併後上櫃除權息資料: {len(combined_df)} 筆 (原有 {len(existing_df)} + 新增 {len(new_df)})")
            return combined_df
        else:
            print(f"📊 新建上櫃除權息資料: {len(new_df)} 筆")
            return new_df

    except Exception as e:
        print(f"❌ 爬取上櫃除權息資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return existing_df if existing_df is not None else pd.DataFrame()

def _merge_divide_ratio_data(twse_df, otc_df):
    """合併上市和上櫃除權息資料"""

    combined_dfs = []

    if twse_df is not None and not twse_df.empty:
        twse_reset = twse_df.reset_index()
        combined_dfs.append(twse_reset)
        print(f"📊 上市除權息資料: {len(twse_reset)} 筆")

    if otc_df is not None and not otc_df.empty:
        otc_reset = otc_df.reset_index()
        combined_dfs.append(otc_reset)
        print(f"📊 上櫃除權息資料: {len(otc_reset)} 筆")

    if combined_dfs:
        combined_df = pd.concat(combined_dfs, ignore_index=True)
        combined_df = combined_df.drop_duplicates(subset=['stock_id', 'date'], keep='last')
        combined_df['date'] = pd.to_datetime(combined_df['date'])
        combined_df = combined_df.set_index(['stock_id', 'date'])
        print(f"📊 合併後總除權息資料: {len(combined_df)} 筆")
        return combined_df
    else:
        print("⚠️ 無除權息資料可合併")
        return pd.DataFrame()

def _save_unified_divide_ratio(df):
    """儲存統一的除權息資料"""

    try:
        import sqlite3

        # 確保目錄存在
        target_dir = 'D:/Finlab/history/tables'
        os.makedirs(target_dir, exist_ok=True)

        # 儲存到統一的除權息資料檔案
        db_path = os.path.join(target_dir, 'divide_ratio.db')

        # 重設索引以便儲存
        df_to_save = df.reset_index()

        # 動態更新股票名稱
        try:
            # 嘗試導入股票名稱映射模組
            import sys
            sys.path.append('..')  # 添加上級目錄到路徑
            from stock_name_mapping import update_stock_names_in_dataframe

            # 為資料添加最新的股票名稱
            df_to_save = update_stock_names_in_dataframe(df_to_save, 'stock_id')
            print("✅ 已動態更新股票名稱")

        except ImportError:
            print("⚠️ 股票名稱映射模組未找到，使用現有名稱")
        except Exception as e:
            print(f"⚠️ 動態更新股票名稱失敗: {e}")

        # 確保日期格式為純日期字串 (YYYY-MM-DD)
        if 'date' in df_to_save.columns:
            df_to_save['date'] = pd.to_datetime(df_to_save['date']).dt.strftime('%Y-%m-%d')

        conn = sqlite3.connect(db_path)
        df_to_save.to_sql('divide_ratio', conn, if_exists='replace', index=False)
        conn.close()

        print(f"💾 統一除權息資料已儲存: {db_path}")
        print(f"📊 總資料筆數: {len(df_to_save):,}")

        # 顯示統計資訊
        if 'market' in df_to_save.columns:
            market_stats = df_to_save['market'].value_counts()
            print(f"📋 市場分布:")
            for market, count in market_stats.items():
                market_name = '上市' if market == 'TWSE' else '上櫃'
                print(f"   {market_name} ({market}): {count:,} 筆")

        return True

    except Exception as e:
        print(f"❌ 儲存統一除權息資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def crawl_otc_divide_ratio():
    """
    爬取櫃買中心除權息資料 - 支援增量更新 (保留向後相容性)

    Returns:
        pandas.DataFrame: 完整的除權息資料 (包含現有資料 + 新資料)
    """
    print("⚠️ crawl_otc_divide_ratio 已整合到 crawl_divide_ratio，建議使用統一函數")
    return _crawl_otc_divide_ratio_internal()

    # 爬取資料
    url = f'https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php?l=zh-tw&d={start_datestr}&ed={datestr}'
    res_otc = requests_get(url)

    if res_otc is None or res_otc.status_code != 200:
        print(f"❌ 櫃買中心API請求失敗")
        return existing_df if existing_df is not None else pd.DataFrame()

    try:
        # 解析JSON回應
        json_data = json.loads(res_otc.text)

        # 處理新的API格式
        if 'tables' in json_data and json_data['tables']:
            # 新格式：使用 tables 鍵
            table_data = json_data['tables'][0]  # 取第一個表格
            if 'data' in table_data and table_data['data']:
                df = pd.DataFrame(table_data['data'])
                print(f"📊 使用新格式 (tables): 爬取到新資料 {len(df)} 筆")
            else:
                print(f"✅ 沒有新的櫃買除權息資料需要更新 (tables格式)")
                return existing_df if existing_df is not None else pd.DataFrame()
        elif 'aaData' in json_data and json_data['aaData']:
            # 舊格式：使用 aaData 鍵
            df = pd.DataFrame(json_data['aaData'])
            print(f"📊 使用舊格式 (aaData): 爬取到新資料 {len(df)} 筆")
        else:
            print(f"❌ 無法找到資料，可用鍵: {list(json_data.keys())}")
            if 'tables' in json_data:
                print(f"   tables內容: {json_data['tables']}")
            return existing_df if existing_df is not None else pd.DataFrame()

    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失敗: {e}")
        print(f"回應內容前200字元: {res_otc.text[:200]}")
        return existing_df if existing_df is not None else pd.DataFrame()
    except Exception as e:
        print(f"❌ 資料處理失敗: {e}")
        return existing_df if existing_df is not None else pd.DataFrame()

    if len(df) == 0:
        print(f"✅ 沒有新的櫃買除權息資料需要更新")
        return existing_df if existing_df is not None else pd.DataFrame()

    # 設置欄位名稱
    df.columns = ['除權息日期', '代號', '名稱', '除權息前收盤價', '除權息參考價',
                  '權值', '息值',"權+息值","權/息","漲停價格","跌停價格","開盤競價基準",
                  "減除股利參考價","現金股利", "每千股無償配股", "現金增資股數", "現金增資認購價",
                  "公開承銷股數", "員工認購股數","原股東認購數", "按持股比例千股認購"]

    # 轉換數值欄位
    float_name_list = ['除權息前收盤價', '除權息參考價',
                      '權值', '息值',"權+息值","漲停價格","跌停價格","開盤競價基準",
                      "減除股利參考價","現金股利", "每千股無償配股", "現金增資股數", "現金增資認購價",
                      "公開承銷股數", "員工認購股數","原股東認購數", "按持股比例千股認購"]

    df[float_name_list] = df[float_name_list].astype(str).apply(lambda s:s.str.replace(',', '')).astype(float)

    # 設置股票代號
    df['stock_id'] = df['代號'] + ' ' + df['名稱']

    # 設置日期
    dates = df['除權息日期'].str.split('/')
    dates = (dates.str[0].astype(int) + 1911).astype(str) + '/' + dates.str[1] + '/' + dates.str[2]
    df['date'] = pd.to_datetime(dates)

    # 計算比率
    df['otc_divide_ratio'] = df['除權息前收盤價'] / df['開盤競價基準']
    new_df = df.set_index(['stock_id', 'date'])

    # 合併新舊資料
    if existing_df is not None:
        # 合併資料
        combined_df = pd.concat([existing_df, new_df])

        # 去除重複 (保留最新的)
        combined_df = combined_df[~combined_df.index.duplicated(keep='last')]

        # 排序
        combined_df = combined_df.sort_index()

        print(f"📊 合併後資料: {len(combined_df)} 筆 (原有 {len(existing_df)} + 新增 {len(new_df)})")
        return combined_df
    else:
        print(f"📊 新建資料: {len(new_df)} 筆")
        return new_df


def crawl_twse_cap_reduction():
    """
    爬取台股減資資料 - 支援增量更新 (保留向後相容性)

    注意: 此函數已整合到 crawl_cap_reduction，建議使用統一的減資爬蟲

    Returns:
        pandas.DataFrame: 完整的減資資料 (包含現有資料 + 新資料)
    """
    print("⚠️ crawl_twse_cap_reduction 已整合到 crawl_cap_reduction，建議使用統一函數")
    return _crawl_twse_cap_reduction_internal()



def crawl_cap_reduction():
    """
    統一爬取台股減資資料 (上市 + 上櫃) - 支援增量更新，保存為DB格式

    Returns:
        pandas.DataFrame: 完整的減資資料 (包含現有資料 + 新資料)
    """

    print("=" * 80)
    print("📊 統一減資資料爬蟲 (上市 + 上櫃)")
    print("=" * 80)

    # 爬取上市公司減資資料
    print("🏛️ 爬取上市公司減資資料...")
    twse_df = _crawl_twse_cap_reduction_internal()

    # 爬取上櫃公司減資資料
    print("\n🏪 爬取上櫃公司減資資料...")
    otc_df = _crawl_otc_cap_reduction_internal()

    # 合併資料
    print("\n🔄 合併上市和上櫃減資資料...")
    combined_df = _merge_cap_reduction_data(twse_df, otc_df)

    # 儲存統一的減資資料
    if combined_df is not None and not combined_df.empty:
        _save_unified_cap_reduction(combined_df)
        print(f"✅ 統一減資資料爬取完成: {len(combined_df)} 筆")
        return combined_df
    else:
        print("⚠️ 無法獲取減資資料")
        return pd.DataFrame()

def _crawl_twse_cap_reduction_internal():
    """內部函數: 爬取上市公司減資資料"""

    # 讀取現有資料
    existing_df = None
    db_file = 'D:/Finlab/history/tables/cap_reduction.db'

    # 嘗試從統一DB檔案讀取上市公司資料
    if os.path.exists(db_file):
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            # 檢查是否有 market 欄位來區分上市上櫃
            try:
                existing_df = pd.read_sql("SELECT * FROM cap_reduction WHERE market = 'TWSE'", conn)
            except:
                # 如果沒有 market 欄位，讀取所有資料
                existing_df = pd.read_sql('SELECT * FROM cap_reduction', conn)
            conn.close()

            if not existing_df.empty:
                existing_df['date'] = pd.to_datetime(existing_df['date'])
                existing_df = existing_df.set_index(['stock_id', 'date'])
                print(f"📂 從統一DB讀取上市減資資料: {len(existing_df)} 筆")

                # 獲取最後日期
                last_date = existing_df.index.get_level_values('date').max()
                print(f"📅 現有上市資料最後日期: {last_date.strftime('%Y-%m-%d')}")

                # 從最後日期的下一天開始爬取
                start_date = last_date + pd.Timedelta(days=1)
                start_datestr = start_date.strftime('%Y%m%d')
            else:
                existing_df = None
                start_datestr = "20200101"
        except Exception as e:
            print(f"⚠️ 讀取統一DB檔案失敗: {e}")
            existing_df = None
            start_datestr = "20200101"
    else:
        print(f"ℹ️ 統一DB檔案不存在，將爬取完整上市資料")
        start_datestr = "20200101"


    # 爬取上市公司減資資料的邏輯 (基於原 crawl_twse_cap_reduction)
    end_datestr = datetime.datetime.now().strftime('%Y%m%d')
    print(f"🔄 爬取上市減資期間: {start_datestr} 至 {end_datestr}")

    try:
        # 使用正確的上市減資爬取邏輯 (基於 finlab_analysis/crawler.py)
        res = requests_get(f"https://www.twse.com.tw/rwd/zh/reducation/TWTAUU?response=csv&startDate={start_datestr}&endDate={end_datestr}&_=1551597854043")

        if res is None or res.status_code != 200:
            print(f"❌ 爬取失敗: HTTP狀態碼 {res.status_code if res else 'None'}")
            return existing_df if existing_df is not None else pd.DataFrame()

        # 檢查回應內容
        if not res.text or len(res.text.strip()) == 0:
            print(f"ℹ️ 該期間無新的減資資料 (回應內容為空)")
            return existing_df if existing_df is not None else pd.DataFrame()

        # 檢查是否有有效的CSV內容
        text_content = res.text.replace("=", "")
        if "恢復買賣日期" not in text_content:
            print(f"⚠️ 回應內容不包含預期的資料格式")
            return existing_df if existing_df is not None else pd.DataFrame()

        try:
            df = pd.read_csv(io.StringIO(text_content), header=1)
            df.columns = df.columns.str.replace(' ', '')
            df = df.dropna(thresh=5).dropna(how='all', axis=1)

            if len(df) == 0:
                print(f"✅ 沒有新的上市減資資料需要更新")
                return existing_df if existing_df is not None else pd.DataFrame()
        except Exception as e:
            print(f"❌ 解析CSV資料失敗: {e}")
            return existing_df if existing_df is not None else pd.DataFrame()

        print(f"📊 爬取到新的上市減資資料: {len(df)} 筆")

        # 設置股票代號和名稱 (分開存儲) - 使用正確的欄位名稱
        df['stock_id'] = df['股票代號'].astype(int).astype(str)
        df['stock_name'] = df['名稱']

        # 設置日期 (只保留西元年月日，不含時分秒) - 使用正確的欄位名稱
        dates = (df['恢復買賣日期'].str.split('/').str[0].astype(int) + 1911).astype(str) + df['恢復買賣日期'].str[3:]
        df['date'] = pd.to_datetime(dates, errors='coerce').dt.strftime('%Y-%m-%d')

        # 轉換欄位名稱為英文並移除重複欄位
        column_mapping = {
            '停止買賣前收盤價格': 'closing_price_before_suspension',
            '開盤競價基準': 'opening_reference_price',
            '漲停價格': 'limit_up_price',
            '跌停價格': 'limit_down_price',
            '減資恢復買賣開始日參考價格': 'resumption_reference_price',
            '恢復買賣參考價': 'resumption_reference_price',
            '除權參考價': 'ex_rights_reference_price',
            '減資原因': 'reduction_reason',
            '詳細資料': 'detail_info'
        }

        # 重新命名欄位
        df = df.rename(columns=column_mapping)

        # 移除重複和不需要的欄位
        columns_to_remove = ['恢復買賣日期', '股票代號', '名稱']
        df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])

        # 轉換數值欄位
        numeric_columns = [
            'closing_price_before_suspension', 'opening_reference_price', 'limit_up_price',
            'limit_down_price', 'resumption_reference_price', 'ex_rights_reference_price'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace(',', '').astype(float, errors='ignore')

        # 計算減資比率
        df['cap_reduction_ratio'] = df['closing_price_before_suspension'] / df['opening_reference_price']

        # 添加市場標識
        df['market'] = 'TWSE'

        # 重新排列欄位順序 (stock_name 放在 stock_id 右側)
        column_order = ['stock_id', 'stock_name'] + [col for col in df.columns if col not in ['stock_id', 'stock_name']]
        df = df[column_order]

        new_df = df.set_index(['stock_id', 'date'])

        # 合併現有資料和新資料
        if existing_df is not None and not existing_df.empty:
            # 合併資料
            combined_df = pd.concat([existing_df, new_df])
            # 去除重複 (保留最新的)
            combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
            # 排序
            combined_df = combined_df.sort_index()
            print(f"📊 合併後上市減資資料: {len(combined_df)} 筆 (原有 {len(existing_df)} + 新增 {len(new_df)})")
            return combined_df
        else:
            print(f"📊 新建上市減資資料: {len(new_df)} 筆")
            return new_df

    except Exception as e:
        print(f"❌ 爬取上市減資資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return existing_df if existing_df is not None else pd.DataFrame()

def _crawl_otc_cap_reduction_internal():
    """內部函數: 爬取上櫃公司減資資料"""

    # 讀取現有上櫃資料
    existing_df = None
    db_file = 'D:/Finlab/history/tables/cap_reduction.db'

    if os.path.exists(db_file):
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            try:
                existing_df = pd.read_sql("SELECT * FROM cap_reduction WHERE market = 'OTC'", conn)
            except:
                existing_df = pd.DataFrame()
            conn.close()

            if not existing_df.empty:
                existing_df['date'] = pd.to_datetime(existing_df['date'])
                existing_df = existing_df.set_index(['stock_id', 'date'])
                print(f"📂 從統一DB讀取上櫃減資資料: {len(existing_df)} 筆")

                last_date = existing_df.index.get_level_values('date').max()
                start_date = last_date + pd.Timedelta(days=1)
                start_year = start_date.year - 1911
                start_month = start_date.month
                start_day = start_date.day
                start_datestr = f"{start_year:02d}/{start_month:02d}/{start_day:02d}"
            else:
                start_datestr = "109/01/01"
        except Exception as e:
            print(f"⚠️ 讀取統一DB檔案失敗: {e}")
            start_datestr = "109/01/01"
    else:
        start_datestr = "109/01/01"


    # 爬取上櫃公司減資資料的邏輯 (基於原 crawl_otc_cap_reduction)
    y = datetime.datetime.now().year
    m = datetime.datetime.now().month
    d = datetime.datetime.now().day

    y_str = str(y-1911)
    m_str = str(m) if m > 9 else '0' + str(m)
    d_str = str(d) if d > 9 else '0' + str(d)

    datestr = '%s/%s/%s' % (y_str, m_str, d_str)

    print(f"🔄 爬取上櫃減資期間: {start_datestr} 至 {datestr}")

    try:
        # 使用原有的上櫃減資爬取邏輯
        url = f'https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php?l=zh-tw&d={start_datestr}&ed={datestr}'
        res_otc = requests_get(url)

        if res_otc is None or res_otc.status_code != 200:
            print(f"❌ 櫃買中心API請求失敗")
            return existing_df if existing_df is not None else pd.DataFrame()

        try:
            # 解析JSON回應
            json_data = json.loads(res_otc.text)

            if 'aaData' not in json_data or not json_data['aaData']:
                print(f"✅ 沒有新的上櫃減資資料需要更新")
                return existing_df if existing_df is not None else pd.DataFrame()

            df = pd.DataFrame(json_data['aaData'])

        except Exception as e:
            print(f"❌ 解析上櫃減資JSON資料失敗: {e}")
            return existing_df if existing_df is not None else pd.DataFrame()

        print(f"📊 爬取到新的上櫃減資資料: {len(df)} 筆")

        # 設置欄位名稱 (英文) - 基於原有的上櫃減資欄位
        df.columns = [
            'resumption_date_raw', 'stock_code_raw', 'stock_name_raw', 'closing_price_before_suspension',
            'resumption_reference_price', 'limit_up_price', 'limit_down_price', 'opening_reference_price',
            'ex_rights_reference_price', 'reduction_reason', 'detail_info'
        ]

        # 設置股票代號和名稱 (分開存儲)
        df['stock_id'] = df['stock_code_raw']
        df['stock_name'] = df['stock_name_raw']

        # 設置日期 (只保留西元年月日，不含時分秒)
        dates = (df['resumption_date_raw'].astype(str).str[:-4].astype(int) + 1911).astype(str) + df['resumption_date_raw'].astype(str).str[-4:]
        df['date'] = pd.to_datetime(dates, errors='coerce').dt.strftime('%Y-%m-%d')

        # 移除重複和不需要的欄位
        columns_to_remove = ['resumption_date_raw', 'stock_code_raw', 'stock_name_raw']
        df = df.drop(columns=columns_to_remove)

        # 轉換數值欄位
        numeric_columns = [
            'closing_price_before_suspension', 'resumption_reference_price', 'limit_up_price',
            'limit_down_price', 'opening_reference_price', 'ex_rights_reference_price'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace(',', '').astype(float, errors='ignore')

        # 計算減資比率
        price = df['opening_reference_price'].copy()
        price[price == 0] = np.nan
        price.fillna(df['resumption_reference_price'], inplace=True)
        df['cap_reduction_ratio'] = df['closing_price_before_suspension'] / price

        # 添加市場標識
        df['market'] = 'OTC'

        # 重新排列欄位順序 (stock_name 放在 stock_id 右側)
        column_order = ['stock_id', 'stock_name'] + [col for col in df.columns if col not in ['stock_id', 'stock_name']]
        df = df[column_order]

        new_df = df.set_index(['stock_id', 'date'])

        # 合併現有資料和新資料
        if existing_df is not None and not existing_df.empty:
            # 合併資料
            combined_df = pd.concat([existing_df, new_df])
            # 去除重複 (保留最新的)
            combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
            # 排序
            combined_df = combined_df.sort_index()
            print(f"📊 合併後上櫃減資資料: {len(combined_df)} 筆 (原有 {len(existing_df)} + 新增 {len(new_df)})")
            return combined_df
        else:
            print(f"📊 新建上櫃減資資料: {len(new_df)} 筆")
            return new_df

    except Exception as e:
        print(f"❌ 爬取上櫃減資資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return existing_df if existing_df is not None else pd.DataFrame()

def _merge_cap_reduction_data(twse_df, otc_df):
    """合併上市和上櫃減資資料"""

    combined_dfs = []

    if twse_df is not None and not twse_df.empty:
        twse_reset = twse_df.reset_index()
        combined_dfs.append(twse_reset)
        print(f"📊 上市減資資料: {len(twse_reset)} 筆")

    if otc_df is not None and not otc_df.empty:
        otc_reset = otc_df.reset_index()
        combined_dfs.append(otc_reset)
        print(f"📊 上櫃減資資料: {len(otc_reset)} 筆")

    if combined_dfs:
        combined_df = pd.concat(combined_dfs, ignore_index=True)
        combined_df = combined_df.drop_duplicates(subset=['stock_id', 'date'], keep='last')
        combined_df['date'] = pd.to_datetime(combined_df['date'])
        combined_df = combined_df.set_index(['stock_id', 'date'])
        print(f"📊 合併後總減資資料: {len(combined_df)} 筆")
        return combined_df
    else:
        print("⚠️ 無減資資料可合併")
        return pd.DataFrame()

def _save_unified_cap_reduction(df):
    """儲存統一的減資資料"""

    try:
        import sqlite3

        # 確保目錄存在
        target_dir = 'D:/Finlab/history/tables'
        os.makedirs(target_dir, exist_ok=True)

        # 儲存到統一的減資資料檔案
        db_path = os.path.join(target_dir, 'cap_reduction.db')

        # 重設索引以便儲存
        df_to_save = df.reset_index()

        # 確保日期格式為純日期字串 (YYYY-MM-DD)
        if 'date' in df_to_save.columns:
            df_to_save['date'] = pd.to_datetime(df_to_save['date']).dt.strftime('%Y-%m-%d')

        conn = sqlite3.connect(db_path)
        df_to_save.to_sql('cap_reduction', conn, if_exists='replace', index=False)
        conn.close()

        print(f"💾 統一減資資料已儲存: {db_path}")
        print(f"📊 總資料筆數: {len(df_to_save):,}")

        # 顯示統計資訊
        if 'market' in df_to_save.columns:
            market_stats = df_to_save['market'].value_counts()
            print(f"📋 市場分布:")
            for market, count in market_stats.items():
                market_name = '上市' if market == 'TWSE' else '上櫃'
                print(f"   {market_name} ({market}): {count:,} 筆")

        return True

    except Exception as e:
        print(f"❌ 儲存統一減資資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def crawl_otc_cap_reduction():
    """
    爬取櫃買中心減資資料 - 支援增量更新 (保留向後相容性)

    Returns:
        pandas.DataFrame: 完整的減資資料 (包含現有資料 + 新資料)
    """
    print("⚠️ crawl_otc_cap_reduction 已整合到 crawl_cap_reduction，建議使用統一函數")
    return _crawl_otc_cap_reduction_internal()






o2tp = {'成交股數':'成交股數',
        '成交筆數':'成交筆數',
        '成交金額(元)':'成交金額',
        '收盤':'收盤價',
        '開盤':'開盤價',
        '最低':'最低價',
        '最高':'最高價',
        '最後買價':'最後揭示買價',
        '最後賣價':'最後揭示賣價',
        '漲跌':'漲跌價差',  # 添加上櫃的漲跌欄位映射
      }

o2tpe = {
    '殖利率(%)':'殖利率(%)',
    '本益比':'本益比',
    '股利年度':'股利年度',
    '股價淨值比':'股價淨值比',
}


o2tb = {
    '外資及陸資(不含外資自營商)-買進股數': '外陸資買進股數(不含外資自營商)',
    '外資及陸資(不含外資自營商)-賣出股數': '外陸資賣出股數(不含外資自營商)',
    '外資及陸資(不含外資自營商)-買賣超股數': '外陸資買賣超股數(不含外資自營商)',
    '外資自營商-買進股數': '外資自營商買進股數',
    '外資自營商-賣出股數': '外資自營商賣出股數',
    '外資自營商-買賣超股數': '外資自營商買賣超股數',
    '投信-買進股數': '投信買進股數',
    '投信-賣出股數': '投信賣出股數',
    '投信-買賣超股數': '投信買賣超股數',
    '自營商(自行買賣)-買進股數': '自營商買進股數(自行買賣)',
    '自營商(自行買賣)-賣出股數': '自營商賣出股數(自行買賣)',
    '自營商(自行買賣)-買賣超股數': '自營商買賣超股數(自行買賣)',
    '自營商(避險)-買進股數': '自營商買進股數(避險)',
    '自營商(避險)-賣出股數': '自營商賣出股數(避險)',
    '自營商(避險)-買賣超股數': '自營商買賣超股數(避險)',
}

# o2tb = {
#     '外資及陸資(不含外資自營商)-買進股數':'外陸資買進股數(不含外資自營商)',
#     'X外資及陸資買股數': '外陸資買進股數(不含外資自營商)',

#     '外資及陸資(不含外資自營商)-賣出股數':'外陸資賣出股數(不含外資自營商)',
#     'X外資及陸資賣股數': '外陸資賣出股數(不含外資自營商)',

#     '外資及陸資(不含外資自營商)-買賣超股數':'外陸資買賣超股數(不含外資自營商)',
#     '外資及陸資淨買股數': '外陸資買賣超股數(不含外資自營商)',

#     '外資自營商-買進股數':'外資自營商買進股數',
#     '外資自營商-賣出股數':'外資自營商賣出股數',
#     '外資自營商-買賣超股數':'外資自營商買賣超股數',
#     '投信-買進股數':'投信買進股數',
#     '投信買進股數': '投信買進股數',
#     '投信-賣出股數': '投信賣出股數',
#     '投信賣股數': '投信賣出股數',

#     '投信-買賣超股數':'投信買賣超股數',
#     '投信淨買股數': '投信買賣超股數',

#     '自營商(自行買賣)-買進股數':'自營商買進股數(自行買賣)',
#     '自營商(自行買賣)買股數':'自營商買進股數(自行買賣)',

#     '自營商(自行買賣)-賣出股數':'自營商賣出股數(自行買賣)',
#     '自營商(自行買賣)賣股數':'自營商賣出股數(自行買賣)',

#     '自營商(自行買賣)-買賣超股數': '自營商買賣超股數(自行買賣)',
#     '自營商(自行買賣)淨買股數': '自營商買賣超股數(自行買賣)',

#     '自營商(避險)-買進股數':'自營商買進股數(避險)',
#     '自營商(避險)買股數': '自營商買進股數(避險)',
#     '自營商(避險)-賣出股數':'自營商賣出股數(避險)',
#     '自營商(避險)賣股數': '自營商賣出股數(避險)',
#     '自營商(避險)-買賣超股數': '自營商買賣超股數(避險)',
#     '自營商(避險)淨買股數': '自營商買賣超股數(避險)',

# }

o2tm = {n:n for n in ['當月營收', '上月營收', '去年當月營收', '上月比較增減(%)', '去年同月增減(%)', '當月累計營收', '去年累計營收',
       '前期比較增減(%)']}

def merge(twe, otc, t2o):
    t2o2 = {k: v for k, v in t2o.items() if k in otc.columns}
    otc = otc[list(t2o2.keys())]
    otc = otc.rename(columns=t2o2)
    twe = twe[otc.columns.intersection(twe.columns)]

    return pd.concat([twe, otc])


def fetch_stock_info_for_price():
    """獲取股票的 listing_status 和 industry 資訊 - 參照 HL.py 的頻率控制機制"""
    import requests
    import re
    import urllib3
    import time
    import random
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry

    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 隨機延遲函數（參照 HL.py）
    def random_sleep(min_seconds=5, max_seconds=10):
        sleep_time = random.uniform(min_seconds, max_seconds)
        print(f"⏳ 正在等待 {sleep_time:.1f} 秒以避免被封鎖...")
        time.sleep(sleep_time)

    # 設定重試策略（參照 HL.py）
    retry_strategy = Retry(
        total=3,  # 總共重試次數
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1  # 重試間隔時間
    )

    # 創建 session 並設定適配器
    session = requests.Session()
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    session.mount("http://", adapter)

    stock_info = {}

    try:
        # 獲取上市股票資訊
        print("📡 獲取上市股票資訊...")
        random_sleep(3, 6)  # 初始延遲

        url_twse = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=2"

        for attempt in range(3):  # 最多重試 3 次
            try:
                response = session.get(url_twse, timeout=30, verify=False)

                if response.status_code == 429:
                    print("⚠️ 遇到 429 Too Many Requests，等待 60 秒後重試...")
                    time.sleep(60)
                    continue

                response.raise_for_status()
                response.encoding = 'big5'

                if response.status_code == 200:
                    # 使用正則表達式解析 HTML 中的股票資訊
                    pattern = r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上市</td><td[^>]*>([^<]*)</td>'
                    matches = re.findall(pattern, response.text)

                    for stock_code, stock_name, industry in matches:
                        if stock_code.isdigit():
                            stock_info[stock_code] = {
                                'stock_name': stock_name,
                                'listing_status': '上市',
                                'industry': industry.strip() if industry.strip() else 'ETF'
                            }
                    print(f"✅ 上市股票: {len([k for k, v in stock_info.items() if v['listing_status'] == '上市'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上市股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)  # 失敗後等待 10 秒重試

        # 獲取上櫃股票資訊
        print("📡 獲取上櫃股票資訊...")
        random_sleep(5, 10)  # 兩次請求間的延遲

        url_tpex = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=4"

        for attempt in range(3):  # 最多重試 3 次
            try:
                response = session.get(url_tpex, timeout=30, verify=False)

                if response.status_code == 429:
                    print("⚠️ 遇到 429 Too Many Requests，等待 60 秒後重試...")
                    time.sleep(60)
                    continue

                response.raise_for_status()
                response.encoding = 'big5'

                if response.status_code == 200:
                    # 使用正則表達式解析 HTML 中的股票資訊
                    pattern = r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上櫃</td><td[^>]*>([^<]*)</td>'
                    matches = re.findall(pattern, response.text)

                    for stock_code, stock_name, industry in matches:
                        if stock_code.isdigit():
                            stock_info[stock_code] = {
                                'stock_name': stock_name,
                                'listing_status': '上櫃',
                                'industry': industry.strip() if industry.strip() else 'ETF'
                            }
                    print(f"✅ 上櫃股票: {len([k for k, v in stock_info.items() if v['listing_status'] == '上櫃'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上櫃股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)  # 失敗後等待 10 秒重試

        print(f"📊 總計獲取股票資訊: {len(stock_info)} 檔股票")
        return stock_info

    except Exception as e:
        print(f"⚠️ 獲取股票資訊失敗: {e}")
        return {}
    finally:
        session.close()

# 股票資訊緩存（避免重複獲取）
_stock_info_cache = None
_cache_timestamp = None

def get_cached_stock_info():
    """獲取緩存的股票資訊，如果緩存過期則重新獲取"""
    global _stock_info_cache, _cache_timestamp
    import time

    # 緩存有效期：24 小時
    cache_duration = 24 * 60 * 60  # 24 hours in seconds
    current_time = time.time()

    # 檢查緩存是否有效
    if (_stock_info_cache is None or
        _cache_timestamp is None or
        current_time - _cache_timestamp > cache_duration):

        print("📊 股票資訊緩存已過期，重新獲取...")
        _stock_info_cache = fetch_stock_info_for_price()
        _cache_timestamp = current_time
    else:
        print(f"📊 使用緩存的股票資訊: {len(_stock_info_cache)} 檔股票")

    return _stock_info_cache

def crawl_price(date):
    dftwe = price_twe(date)
    time.sleep(5)
    dfotc = price_otc(date)
    if len(dftwe) != 0 and len(dfotc) != 0:
        df = merge(dftwe, dfotc, o2tp)

        # 獲取股票資訊並添加到 DataFrame（使用緩存）
        stock_info = get_cached_stock_info()

        if stock_info:
            # 重置索引以便操作
            df_reset = df.reset_index()

            # 提取純股票代碼（去除名稱部分）
            def extract_stock_code(stock_id_with_name):
                """從 '0050 元大台灣50' 格式中提取 '0050'"""
                stock_str = str(stock_id_with_name)
                # 取第一個空格之前的部分作為股票代碼
                return stock_str.split()[0] if ' ' in stock_str else stock_str

            df_reset['pure_stock_id'] = df_reset['stock_id'].map(extract_stock_code)

            # 🗑️ 過濾權證資料 (7xxx 開頭的股票代碼)
            before_filter = len(df_reset)
            df_reset = df_reset[~df_reset['pure_stock_id'].str.startswith('7')]
            after_filter = len(df_reset)

            if before_filter > after_filter:
                filtered_count = before_filter - after_filter
                print(f"🗑️ 已過濾 {filtered_count} 筆權證資料")

            # 添加 stock_name, listing_status, industry 欄位
            df_reset['stock_name'] = df_reset['pure_stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('stock_name', '')
            )
            df_reset['listing_status'] = df_reset['pure_stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('listing_status', '')
            )
            df_reset['industry'] = df_reset['pure_stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('industry', 'ETF')
            )

            # 🗑️ 過濾興櫃股票 - 只保留上市、上櫃及ETF股票
            before_filter_rotc = len(df_reset)
            # 保留上市、上櫃股票，以及沒有 listing_status 資訊的股票（通常是ETF）
            df_reset = df_reset[
                (df_reset['listing_status'] == '上市') |
                (df_reset['listing_status'] == '上櫃') |
                (df_reset['listing_status'] == '') |  # ETF 通常沒有 listing_status 或為空
                (df_reset['pure_stock_id'].str.startswith('00'))  # 確保 00 開頭的 ETF 被保留
            ]
            after_filter_rotc = len(df_reset)

            if before_filter_rotc > after_filter_rotc:
                filtered_rotc_count = before_filter_rotc - after_filter_rotc
                print(f"🗑️ 已過濾 {filtered_rotc_count} 筆興櫃股票資料")

            # 更新 stock_id 為純代碼
            df_reset['stock_id'] = df_reset['pure_stock_id']
            df_reset = df_reset.drop('pure_stock_id', axis=1)

            # 🔄 將中文欄位名稱轉換為英文（與 price.db 和 newprice.db 一致）
            column_mapping = {
                '成交股數': 'Volume',
                '成交筆數': 'Transaction',
                '成交金額': 'TradeValue',
                '開盤價': 'Open',
                '最高價': 'High',
                '最低價': 'Low',
                '收盤價': 'Close',
                '漲跌價差': 'Change'  # 添加漲跌價差欄位映射
                # 注意：不包含 '最後揭示買價' 和 '最後揭示賣價'，因為標準資料庫結構中沒有這些欄位
            }

            # 重新命名欄位
            for old_name, new_name in column_mapping.items():
                if old_name in df_reset.columns:
                    df_reset = df_reset.rename(columns={old_name: new_name})

            # 移除不需要的欄位（不在標準資料庫結構中的欄位）
            columns_to_remove = ['最後揭示買價', '最後揭示賣價']
            for col in columns_to_remove:
                if col in df_reset.columns:
                    df_reset = df_reset.drop(col, axis=1)

            # 恢復索引
            df = df_reset.set_index(['stock_id', 'date'])

            print(f"✅ 已添加股票資訊到 {len(df)} 筆資料（已排除權證及興櫃股票）")
            print(f"📊 保留股票類型：上市、上櫃、ETF")
            print(f"🔄 已轉換欄位名稱為英文格式")

        return df
    else:
        return pd.DataFrame()


def crawl_bargin(date):
    dftwe = bargin_twe(date)
    dfotc = bargin_otc(date)
    if len(dftwe) != 0 and len(dfotc) != 0:
        return merge(dftwe, dfotc, o2tb)
    else:
        return pd.DataFrame()


def crawl_monthly_report(date):
    print(f"🔄 開始爬取月營收資料 - {date.strftime('%Y-%m-%d')}")

    print(f"📊 爬取上市公司月營收...")
    dftwe = month_revenue('sii', date)
    print(f"✅ 上市公司完成: {len(dftwe)} 筆資料")

    print(f"⏱️ 等待5秒避免被封鎖...")
    time.sleep(5)

    print(f"📊 爬取上櫃公司月營收...")
    dfotc = month_revenue('otc', date)
    print(f"✅ 上櫃公司完成: {len(dfotc)} 筆資料")

    if len(dftwe) != 0 and len(dfotc) != 0:
        print(f"🔄 合併上市和上櫃資料...")
        result = merge(dftwe, dfotc, o2tm)
        print(f"✅ 月營收資料合併完成: {len(result)} 筆資料")
        return result
    else:
        print(f"⚠️ 部分資料為空，返回空 DataFrame")
        return pd.DataFrame()

def crawl_pe(date):
    """爬取PE資料 - 相容原始pe.pkl格式"""
    dftwe = pe_twe(date)
    dfotc = pe_otc(date)
    if len(dftwe) != 0 and len(dfotc) != 0:
        df = merge(dftwe, dfotc, o2tpe)

        # 轉換為相容原始pe.pkl的格式
        df = convert_pe_to_compatible_format(df, date)
        return df
    else:
        return pd.DataFrame()

def convert_pe_to_compatible_format(df, date):
    """將PE資料轉換為相容原始pe.pkl的格式"""

    # 根據實際的原始pe.pkl格式，保持中文欄位名稱
    # 原始格式: ['殖利率(%)', '本益比', '股利年度', '股價淨值比']

    # 確保所有必要欄位都存在，按原始pe.pkl的順序
    required_columns = ['殖利率(%)', '本益比', '股利年度', '股價淨值比']

    # 檢查現有欄位
    available_columns = [col for col in required_columns if col in df.columns]
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        print(f"⚠️ 警告: 缺少欄位 {missing_columns}")
        # 為缺少的欄位填入適當的值
        for col in missing_columns:
            if col == '股利年度':
                # 股利年度使用民國年
                current_year = date.year
                minguo_year = current_year - 1911
                df[col] = str(minguo_year)
            else:
                df[col] = pd.NA

    # 只保留需要的欄位
    df_final = df[required_columns].copy()

    # 轉換資料類型 - 與原始資料保持一致 (object類型)
    # 保持所有欄位為object類型，與原始data\pe.pkl一致
    for col in required_columns:
        df_final[col] = df_final[col].astype(object)

    # 處理索引 - 確保格式正確
    if hasattr(df_final.index, 'names'):
        # 如果已經是MultiIndex，重置
        df_final = df_final.reset_index()
    else:
        # 如果不是MultiIndex，重置索引
        df_final = df_final.reset_index()

    # 確保有stock_id欄位
    if 'stock_id' not in df_final.columns:
        if 'index' in df_final.columns:
            df_final['stock_id'] = df_final['index']
            df_final = df_final.drop('index', axis=1)
        else:
            # 使用原始索引作為stock_id
            df_final['stock_id'] = df_final.index

    # 添加日期欄位
    df_final['date'] = pd.to_datetime(date)

    # 設置MultiIndex (stock_id, date) - 與原始pe.pkl格式一致
    df_final = df_final.set_index(['stock_id', 'date'])

    # 確保欄位順序與原始pe.pkl一致
    df_final = df_final[required_columns]

    return df_final

def crawl_monthly_revenue_single_market(market, year, month, date, foreign=False):
    """
    爬取單一市場的月營收資料

    Args:
        market: 'sii' (上市) 或 'otc' (上櫃)
        year: 民國年
        month: 月份
        date: 日期物件
        foreign: False=國內公司(_0), True=國外公司(_1)

    Returns:
        pandas.DataFrame: 月營收資料
    """
    suffix = '1' if foreign else '0'
    type_name = '國外' if foreign else '國內'
    market_name = '上市' if market == 'sii' else '上櫃'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    # 構建 URL (優先使用新網站)
    url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}_{suffix}.html'

    # 對於較舊的年份，使用舊格式
    if year <= 98:
        url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}.html'

    try:
        # 使用統一的 requests_get 函數
        response = requests_get(url, headers=headers, timeout=30)

        if response is None or response.status_code != 200:
            return pd.DataFrame()

        # 檢查是否被封鎖
        if '您的網頁IP已經被證交所封鎖' in response.text:
            return pd.DataFrame()

        response.encoding = 'big5'

        # 使用 pandas 解析表格
        try:
            from io import StringIO
            dfs = pd.read_html(StringIO(response.text), encoding='big5')
        except:
            # 如果編碼失敗，嘗試不指定編碼
            dfs = pd.read_html(StringIO(response.text))

        if not dfs:
            return pd.DataFrame()

        # 篩選有效表格
        valid_dfs = [df for df in dfs if df.shape[1] <= 11 and df.shape[1] > 5]

        if not valid_dfs:
            return pd.DataFrame()

        df = pd.concat(valid_dfs)

        # 處理欄位名稱
        if hasattr(df.columns, 'levels'):
            df.columns = df.columns.get_level_values(1)
            df.columns = df.columns.str.replace(' ', '')
        else:
            df = df[list(range(0, min(10, len(df.columns))))]
            # 尋找包含 '公司代號' 的行作為欄位名稱
            column_indices = df.index[df.iloc[:, 0] == '公司代號']
            if len(column_indices) > 0:
                column_index = column_indices[0]
                df.columns = df.iloc[column_index]
                df = df.drop(df.index[column_index])

        # 清理資料
        df = df.dropna(how='all')
        df = df[df.iloc[:, 0] != '公司代號']  # 移除重複的標題行

        # 設置索引和日期
        if len(df.columns) > 0 and len(df) > 0:
            # 添加日期欄位
            df['date'] = pd.to_datetime(date)

            # 設置索引 (假設第一欄是公司代號)
            if df.columns[0] in ['公司代號', '公司代號']:
                df = df.set_index([df.columns[0], 'date'])
            else:
                df = df.set_index(['date'])

            return df
        else:
            return pd.DataFrame()

    except Exception as e:
        return pd.DataFrame()

def crawl_monthly_revenue(date):
    """
    爬取完整月營收資料 (上市+上櫃，國內+國外)

    Args:
        date: 日期物件

    Returns:
        pandas.DataFrame: 完整的月營收資料
    """
    # 轉換為民國年月
    year = date.year - 1911
    month = date.month

    all_data = []

    # 爬取順序: 上市國內 -> 上市國外 -> 上櫃國內 -> 上櫃國外
    tasks = [
        ('sii', False, '上市國內'),
        ('sii', True, '上市國外'),
        ('otc', False, '上櫃國內'),
        ('otc', True, '上櫃國外')
    ]

    total_tasks = len(tasks)

    for i, (market, foreign, desc) in enumerate(tasks, 1):
        df = crawl_monthly_revenue_single_market(market, year, month, date, foreign)

        # 子進度顯示
        sub_progress = (i / total_tasks) * 100
        if len(df) > 0:
            all_data.append(df)
            status_icon = "✅"
            status_text = f"({len(df)} 筆)"
        else:
            status_icon = "❌"
            status_text = "(無資料)"

        print(f"    └─ {desc}: {i}/{total_tasks} [{sub_progress:5.1f}%] {status_icon} {status_text}")

        # 延遲避免被封鎖
        if i < total_tasks:  # 最後一個任務不需要延遲
            import time, random
            time.sleep(random.uniform(2, 4))

    # 合併所有資料
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=False)
        return combined_df
    else:
        return pd.DataFrame()

out = widgets.Output(layout={'border': '1px solid black'})

@out.capture()
def update_table(table_name, crawl_function, dates):

    if dates:
        if len(dates) == 0:
            print("該時間段沒有可以爬取之資料")
            return
        print('start crawl ' + table_name + ' from ', dates[0] , 'to', dates[-1])
    else:
        print('起始、結束日期有點怪怪的，請重新選擇一下喔，下載財報時，可以用以下的選法')
        print('第一季：該年 5/1~5/31')
        print('第二季：該年 8/1~8/31')
        print('第三季：該年 11/1~11/30')
        print('第四季：隔年 3/1~4/31')
        return


    df = pd.DataFrame()
    dfs = {}
    batch_size = 10  # 每 10 筆存檔一次
    batch_count = 0
    batch_counts = {}  # 為多個 DataFrame 分別計數

    # 使用普通的 tqdm 而不是 tqdm_notebook 來避免 ipywidgets 依賴
    try:
        from tqdm import tqdm
        progress = tqdm(dates, desc=f'crawl_{table_name}')
    except ImportError:
        # 如果沒有 tqdm，使用簡單的進度顯示
        progress = dates

    for d in progress:

        print('crawling', d, end="")
        # 只有當 progress 是 tqdm 對象時才設置描述
        if hasattr(progress, 'set_description'):
            progress.set_description('crawl' + table_name + str(d))

        try:
            data = crawl_function(d)

            if data is None or len(data) == 0:
                print('  ❌')

            # update multiple dataframes
            elif isinstance(data, dict):
                if len(dfs) == 0:
                    dfs = {i:pd.DataFrame() for i in data.keys()}
                    batch_counts = {i:0 for i in data.keys()}

                for i, d in data.items():
                    dfs[i] = pd.concat([dfs[i], d])
                    batch_counts[i] += 1

                    # 每個 DataFrame 分別檢查是否需要存檔
                    if batch_counts[i] >= batch_size:
                        print(f'\n💾 分批存檔 {i} (已處理 {batch_counts[i]} 筆)...')
                        if len(dfs[i]) > 0:
                            try:
                                to_pickle(dfs[i], i)
                                print(f'✅ 已保存 {len(dfs[i])} 筆資料到 {i}.pkl')
                                dfs[i] = pd.DataFrame()  # 清空累積的資料
                                batch_counts[i] = 0  # 只有存檔成功才重置計數
                                print('🔄 繼續爬取...\n')
                            except Exception as save_error:
                                print(f'❌ {i} 存檔失敗: {str(save_error)[:50]}...')
                                print('⚠️ 將在下次嘗試存檔，不重置計數')
                        else:
                            batch_counts[i] = 0  # 如果沒有資料，重置計數

                print('  ✅')

            # update single dataframe
            else:
                df = pd.concat([df, data])
                print('  ✅')
                batch_count += 1

                # 每 10 筆存檔一次
                if batch_count >= batch_size:
                    print(f'\n💾 分批存檔 (已處理 {batch_count} 筆)...')
                    if len(df) > 0:
                        try:
                            to_pickle(df, table_name)
                            print(f'✅ 已保存 {len(df)} 筆資料到 {table_name}.pkl')
                            df = pd.DataFrame()  # 清空累積的資料
                            batch_count = 0  # 只有存檔成功才重置計數
                            print('🔄 繼續爬取...\n')
                        except Exception as save_error:
                            print(f'❌ 存檔失敗: {str(save_error)[:50]}...')
                            print('⚠️ 將在下次嘗試存檔，不重置計數')
                            # 不重置 batch_count，下次還會嘗試存檔
                    else:
                        batch_count = 0  # 如果沒有資料，重置計數

        except Exception as e:
            print(f'  ❌ 錯誤: {str(e)[:50]}...')
            # 繼續處理下一個日期，不中斷整個更新過程

        time.sleep(10)



    # 處理剩餘的資料 (不足 10 筆的部分)
    if df is not None and len(df) != 0:
        print(f'\n💾 保存剩餘資料 ({len(df)} 筆)...')
        to_pickle(df, table_name)
        print(f'✅ 最終保存完成')

    # 處理剩餘的多個 DataFrame (不足 10 筆的部分)
    if len(dfs) != 0:
        for i, d in dfs.items():
            if len(d) != 0:
                print(f'\n💾 保存剩餘 {i} 資料 ({len(d)} 筆)...')
                to_pickle(d, i)
                print(f'✅ {i} 最終保存完成')

import datetime
from dateutil.relativedelta import relativedelta

def check_monthly_revenue():

    df = pd.read_pickle("history/tables/monthly_report.pkl")

    if df.loc['1101 台泥', '2017-10-10']['當月營收'] == '8387381':
        print("fix monthly report errors")
        df = df.reset_index()
        df['date'] = [d + relativedelta(months=1) for d in df['date']]
        df.set_index(['stock_id', 'date'], inplace=True)
        df.to_pickle("history/tables/monthly_report.pkl")
        print("done")
        commit("monthly_report")

import pickle
def to_pickle(df, name):
    # 修復 numpy._core.numeric 問題
    try:
        import numpy as np
        if not hasattr(np, '_core'):
            import types
            np._core = types.ModuleType('_core')
        if not hasattr(np._core, 'numeric'):
            np._core.numeric = types.ModuleType('numeric')
    except:
        pass

    if not os.path.isdir('history'):
        os.mkdir('history')

    if not os.path.isdir(os.path.join('history', 'tables')):
        os.mkdir(os.path.join('history', 'tables'))

    # 使用帶時間戳的檔案名稱來避免衝突
    import datetime
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    fname = os.path.join('history', 'tables', name + '.pkl')
    newfname = os.path.join('history', 'tables', f'new{name}_{timestamp}.pkl')

    # 如果是 price 表格，直接使用新檔案避免兼容性問題
    if name == 'price':
        backup_fname = os.path.join('history', 'tables', f'price_v2.pkl')
        print(f'🔄 price 表格使用新檔案策略: {backup_fname}')
        fname = backup_fname  # 直接使用新檔案

    # refine patch for monthly revenue

    if name == 'monthly_report' :
        check_monthly_revenue()

    if os.path.isfile(fname):

        print('read pickle')
        try:
            old_df = pd.read_pickle(fname)
        except Exception as pickle_error:
            print(f'⚠️ 讀取舊檔案失敗: {str(pickle_error)[:50]}...')

            # 如果是 price 檔案，嘗試使用新的檔案名稱
            if name == 'price':
                new_price_file = os.path.join('history', 'tables', 'price_v2.pkl')
                print(f'🔄 嘗試使用新檔案: {new_price_file}')

                try:
                    if os.path.exists(new_price_file):
                        old_df = pd.read_pickle(new_price_file)
                        print('✅ 新檔案讀取成功')
                        # 更新檔案名稱
                        fname = new_price_file
                    else:
                        raise FileNotFoundError("新檔案不存在")
                except Exception:
                    print('🆕 創建全新的 price_v2.pkl 檔案...')
                    # 創建新檔案
                    df = df[~df.index.duplicated(keep='last')]
                    df.to_pickle(new_price_file)

                    # 更新日期記錄
                    if not os.path.isfile(date_range_record_file):
                        pickle.dump({}, open(date_range_record_file, 'wb'))

                    print('save date')
                    dates = pickle.load(open(date_range_record_file, 'rb'))
                    dates[name] = (df.index.levels[1][0], df.index.levels[1][-1])
                    pickle.dump(dates, open(date_range_record_file, 'wb'))
                    print(f'✅ 新檔案創建成功: {new_price_file}')
                    return
            else:
                # 非 price 檔案的處理
                print('🆕 創建新檔案...')
                df = df[~df.index.duplicated(keep='last')]
                df.to_pickle(fname)

                # 更新日期記錄
                if not os.path.isfile(date_range_record_file):
                    pickle.dump({}, open(date_range_record_file, 'wb'))

                print('save date')
                dates = pickle.load(open(date_range_record_file, 'rb'))
                dates[name] = (df.index.levels[1][0], df.index.levels[1][-1])
                pickle.dump(dates, open(date_range_record_file, 'wb'))
                return

        gc.collect()
        
        print('append pickle')
        old_df = pd.concat([old_df, df], sort=False)
        gc.collect()
        
        print('remove duplicates')
        old_df.reset_index(inplace=True)
        old_df.drop_duplicates(['stock_id', 'date'], inplace=True)
        old_df.set_index(['stock_id', 'date'], inplace=True)
        gc.collect()
        
        print('sort index')
        old_df.sort_index(inplace=True)
        gc.collect()
        
        print('save pickle')
        old_df.to_pickle(newfname)

        # 處理檔案鎖定問題
        max_retries = 3
        for attempt in range(max_retries):
            try:
                os.remove(fname)
                os.rename(newfname, fname)
                break
            except PermissionError as e:
                if attempt < max_retries - 1:
                    print(f'⚠️ 檔案被鎖定，等待 {attempt+1} 秒後重試...')
                    time.sleep(attempt + 1)
                else:
                    print(f'❌ 檔案鎖定，無法更新 {fname}')
                    # 清理臨時檔案
                    if os.path.exists(newfname):
                        try:
                            os.remove(newfname)
                        except:
                            pass
                    raise e
    else:
        df = df[~df.index.duplicated(keep='last')]
        df.to_pickle(fname)
        old_df = df

    if not os.path.isfile(date_range_record_file):
        pickle.dump({}, open(date_range_record_file, 'wb'))
    
    print('save date')
    dates = pickle.load(open(date_range_record_file, 'rb'))

    # 檢查 index 類型並相應處理
    try:
        if hasattr(old_df.index, 'levels') and len(old_df.index.levels) > 1:
            # MultiIndex 情況
            dates[name] = (old_df.index.levels[1][0], old_df.index.levels[1][-1])
        elif 'date' in old_df.columns:
            # 如果有 date 欄位，使用該欄位的最小和最大值
            dates[name] = (old_df['date'].min(), old_df['date'].max())
        else:
            # 使用當前日期範圍作為備用
            import datetime
            today = datetime.datetime.now()
            dates[name] = (today - datetime.timedelta(days=30), today)
            print(f"⚠️ 無法確定日期範圍，使用預設範圍")
    except Exception as e:
        print(f"⚠️ 保存日期範圍時發生錯誤: {e}")
        # 使用當前日期作為備用
        import datetime
        today = datetime.datetime.now()
        dates[name] = (today - datetime.timedelta(days=30), today)

    pickle.dump(dates, open(date_range_record_file, 'wb'))
    del old_df
    gc.collect()

    commit(name)


from datetime import date
from dateutil.rrule import rrule, DAILY, MONTHLY

def date_range(start_date, end_date):
    return [dt.date() for dt in rrule(DAILY, dtstart=start_date, until=end_date)]

def month_range(start_date, end_date):
    return [dt.date() for dt in rrule(MONTHLY, dtstart=start_date, until=end_date)]

def season_range(start_date, end_date):

    if isinstance(start_date, datetime.datetime):
        start_date = start_date.date()

    if isinstance(end_date, datetime.datetime):
        end_date = end_date.date()

    ret = []
    for year in range(start_date.year-1, end_date.year+1):
        ret += [  datetime.date(year, 5, 15),
                datetime.date(year, 8, 14),
                datetime.date(year, 11, 14),
                datetime.date(year+1, 3, 31)]
    ret = [r for r in ret if start_date < r < end_date]

    return ret

import ipywidgets as widgets
from IPython.display import display

def table_date_range(table_name):
    """獲取表格的實際日期範圍 - 直接從檔案讀取"""

    # PE資料特殊處理 - 直接讀取data/pe.pkl
    if table_name == 'pe':
        pe_file = 'data/pe.pkl'
        if os.path.exists(pe_file):
            try:
                df = pd.read_pickle(pe_file)
                if hasattr(df.index, 'get_level_values') and 'date' in df.index.names:
                    dates = df.index.get_level_values('date')
                    min_date = dates.min()
                    max_date = dates.max()

                    # 確保返回 datetime 對象，避免類型比較問題
                    if hasattr(min_date, 'to_pydatetime'):
                        min_date = min_date.to_pydatetime()
                    if hasattr(max_date, 'to_pydatetime'):
                        max_date = max_date.to_pydatetime()

                    return min_date, max_date
                else:
                    print(f"⚠️ PE檔案索引結構異常: {df.index.names}")
                    return [None, None]
            except Exception as e:
                print(f"⚠️ 讀取PE檔案失敗: {e}")
                return [None, None]
        else:
            print(f"⚠️ PE檔案不存在: {pe_file}")
            return [None, None]

    # 其他表格使用原來的邏輯
    if os.path.isfile(date_range_record_file):
        with open(date_range_record_file, 'rb') as f:
            dates = pickle.load(f)
            if table_name in dates:
                return dates[table_name]
            else:
                return [None, None]
    else:
        return [None, None]

from inspect import signature


def widget(table_name, crawl_func, range_date=None):


    sig = signature(crawl_func)

    if len(sig.parameters) == 0:
        @out.capture()
        def onupdate(x):
            print('updating ', table_name)
            df = crawl_func()
            to_pickle(df, table_name)
            print('done')

        btn = widgets.Button(description='update ')
        btn.on_click(onupdate)

        first_date, last_date = table_date_range(table_name)
        label = widgets.Label(table_name + ' | ' + str(first_date) + ' ~ ' + str(last_date))
        items = [btn]
        display(widgets.VBox([label, widgets.HBox(items)]))

    else:

        date_picker_from = widgets.DatePicker(
            description='from',
            disabled=False,
        )

        first_date, last_date = table_date_range(table_name)

        if last_date:
            date_picker_from.value = last_date

        date_picker_to = widgets.DatePicker(
            description='to',
            disabled=False,
        )

        date_picker_to.value = datetime.datetime.now().date()

        btn = widgets.Button(description='update ')

        def onupdate(x):
            dates = range_date(date_picker_from.value, date_picker_to.value)

            if len(dates) == 0:
                print('no data to parse')

            update_table(table_name, crawl_func, dates)

        btn.on_click(onupdate)


        label = widgets.Label(table_name + ' | ' + str(first_date) + ' ~ ' + str(last_date))

        items = [date_picker_from, date_picker_to, btn]
        display(widgets.VBox([label, widgets.HBox(items)]))

import requests
from io import StringIO
import pandas as pd
try:
    import numpy as np
except ImportError:
    # 使用已定義的 fallback
    pass
# tqdm 已在文件開頭導入，這裡不需要重複導入
import os
import pickle
import datetime
import random

def afterIFRS(year, season):
    season2date = [ datetime.datetime(year, 5, 15),
                    datetime.datetime(year, 8, 14),
                    datetime.datetime(year, 11, 14),
                    datetime.datetime(year+1, 3, 31)]

    return pd.to_datetime(season2date[season-1].date())

def clean(year, season, balance_sheet):

    if len(balance_sheet) == 0:
        return balance_sheet
    balance_sheet = balance_sheet.transpose().reset_index().rename(columns={'index':'stock_id'})


    if '會計項目' in balance_sheet:
        s = balance_sheet['會計項目']
        balance_sheet = balance_sheet.drop('會計項目', axis=1).apply(pd.to_numeric)
        balance_sheet['會計項目'] = s.astype(str)

    balance_sheet['date'] = afterIFRS(year, season)

    balance_sheet['stock_id'] = balance_sheet['stock_id'].astype(str)
    balance = balance_sheet.set_index(['stock_id', 'date'])
    return balance

def download_html(year, season, stock_ids, report_type='C'):

    directory = os.path.join('history', 'financial_statement', str(year) + str(season))
    if not os.path.exists(directory):
        os.makedirs(directory)

    files = [os.path.join(directory, str(i) + '.html') for i in stock_ids]
    pbar = tqdm([sid for file, sid in zip(files, stock_ids) if not os.path.exists(file) or os.stat(file).st_size < 10000])

    for sid in pbar:

        pbar.set_description('downloading stock %s in report type %s' % (sid, report_type))

        file = os.path.join(directory, str(sid) + '.html')

        # start parsing
        if int(year) >= 2013:
            url = ('https://mops.twse.com.tw/server-java/t164sb01?step=1&CO_ID=' + str(sid) + '&SYEAR=' + str(year) + '&SSEASON='+str(season)+'&REPORT_ID=' + str(report_type))
        else:
            url = ('https://mops.twse.com.tw/server-java/t147sb02?t203sb01Form=t203sb01Form&step=0&comp_id='+str(sid)+'&YEAR1='+str(year)+'&SEASON1='+str(season)+'&R_TYPE1=B')

        try:
            r = requests_get(url)
        except:
            print('**WARRN: requests cannot get stock:')
            print(url)
            continue

        r.encoding = 'big5'

        # write files
        f = open(file, 'w', encoding='utf-8')

        f.write('<meta charset="UTF-8">\n')
        f.write(r.text)
        f.close()

        # finish
        # print(percentage, i, 'end')

        # sleep a while
        time.sleep(random.uniform(5, 10))

import requests
import os
import time
import requests
import datetime
import random
import requests
import io
import shutil
import zipfile
import sys
import urllib.request



def crawl_finance_statement2019(year, season):

    def ifrs_url(year, season):
        url = "https://mops.twse.com.tw/server-java/FileDownLoad?step=9&fileName=tifrs-"+str(year)+"Q"+str(season)\
                +".zip&filePath=/home/<USER>/nas/ifrs/"+str(year)+"/"
        print(url)
        return url



    from tqdm import tqdm
    class DownloadProgressBar(tqdm):
        def update_to(self, b=1, bsize=1, tsize=None):
            if tsize is not None:
                self.total = tsize
            self.update(b * bsize - self.n)


    def download_url(url, output_path):
        with DownloadProgressBar(unit='B', unit_scale=True,
                                 miniters=1, desc=url.split('/')[-1]) as t:
            urllib.request.urlretrieve(url, filename=output_path, reporthook=t.update_to)

    def download_file(url, filename):
        """
        Helper method handling downloading large files from `url` to `filename`. Returns a pointer to `filename`.
        """
        chunkSize = 1024
        r = requests.get(url, stream=True, verify=True)
        with open(filename, 'wb') as f:
            # pbar = tqdm( unit="B", total=int( r.headers['content-length'] ) )
            for chunk in r.iter_content(chunk_size=chunkSize):
                if chunk: # filter out keep-alive new chunks
                    # pbar.update (len(chunk))
                    f.write(chunk)
        return r

    def ifrs_url(year, season):
        url = "https://mops.twse.com.tw/server-java/FileDownLoad?step=9&fileName=tifrs-"+str(year)+"Q"+str(season)\
                +".zip&filePath=/home/<USER>/nas/ifrs/"+str(year)+"/"
        print(url)
        return url

    url = ifrs_url(year,season)
    print('start download zip file from ', url)
    download_file(url, 'temp.zip')
    print('finished!')


    path = os.path.join('history', 'financial_statement', str(year) + str(season))

    if os.path.isdir(path):
        shutil.rmtree(path)

    zipfiles = zipfile.ZipFile(open('temp.zip', 'rb'))
    zipfiles.extractall(path=path)

    fnames = [f for f in os.listdir(path) if f[-5:] == '.html']
    fnames = sorted(fnames)

    newfnames = [f.split("-")[5] + '.html' for f in fnames]

    for fold, fnew in zip(fnames, newfnames):
        if len(fnew) != 9:
            os.remove(os.path.join(path, fold))
            continue

        if not os.path.exists(os.path.join(path, fnew)):
            os.rename(os.path.join(path, fold), os.path.join(path, fnew))
        else:
            os.remove(os.path.join(path, fold))


def crawl_finance_statement(year, season, stock_ids):

    directory = os.path.join('history', 'financial_statement', str(year) + str(season))
    if not os.path.exists(directory):
        os.makedirs(directory)

    if year >= 2013:
        download_html(year, season, stock_ids, 'C')
    download_html(year, season, stock_ids, 'B')
    download_html(year, season, stock_ids, 'A')

def remove_english(s):
    result = re.sub(r'[a-zA-Z()]', "", s)
    return result

def patch2019(df):
    df = df.copy()
    dfname = df.columns.levels[0][0]

    df = df.iloc[:,1:].rename(columns={'會計項目Accounting Title':'會計項目'})


    refined_name = df[(dfname,'會計項目')].str.split(" ").str[0].str.replace("　", "").apply(remove_english)

    subdf = df[dfname].copy()
    subdf['會計項目'] = refined_name
    df[dfname] = subdf

    df.columns = pd.MultiIndex(levels=[df.columns.levels[1], df.columns.levels[0]],codes=[df.columns.codes[1], df.columns.codes[0]])

    def neg(s):

        if isinstance(s, float):
            return s

        if str(s) == 'nan':
            return np.nan

        s = s.replace(",", "")
        if s[0] == '(':
            return -float(s[1:-1])
        else:
            return float(s)

    df.iloc[:,1:] = df.iloc[:,1:].applymap(neg)
    return df

def read_html2019(file):
    dfs = pd.read_html(file)
    return [pd.DataFrame(), patch2019(dfs[0]), patch2019(dfs[1]), patch2019(dfs[2])]


import re
def pack_htmls(year, season, directory):
    balance_sheet = {}
    income_sheet = {}
    cash_flows = {}
    income_sheet_cumulate = {}
    pbar = tqdm(os.listdir(directory))

    for i in pbar:

        # 將檔案路徑建立好
        file = os.path.join(directory, i)

        # 假如檔案不是html結尾，或是太小，代表不是正常的檔案，略過
        if file[-4:] != 'html' or os.stat(file).st_size < 10000:
            continue

        # 顯示目前運行的狀況
        stock_id = i.split('.')[0]
        pbar.set_description('parse htmls %d season %d stock %s' % (year, season, stock_id))

        # 讀取html
        if year < 2019:
            dfs = pd.read_html(file)
        else:
            try:
                dfs = read_html2019(file)
            except Exception as e:
                print("**ERROR: fail to parse ", file)
                print(e)
                continue

        # 處理pandas0.24.1以上，會把columns parse好的問題
        for df in dfs:
            if 'levels' in dir(df.columns):
                df.columns = list(range(df.values.shape[1]))

        # 假如html不完整，則略過
        if len(dfs) < 4:
            print('**WARRN html file broken', year, season, i)
            continue

        if year <= 2012:
            df = dfs[1]
            category = (df[0] == '會計科目').cumsum()
            df[category == 1]
            dfs = {
                1: df[category == 0],
                2: df[category == 1],
                3: df[category == 2],
            }

        # 取得 balance sheet
        df = dfs[1].copy().drop_duplicates(subset=0, keep='last')
        df = df.set_index(0)
        balance_sheet[stock_id] = df[1].dropna()
        #balance_sheet = combine(balance_sheet, df[1].dropna(), stock_id)

        # 取得 income statement
        df = dfs[2].copy().drop_duplicates(subset=0, keep='last')
        df = df.set_index(0)

        # 假如有4個columns，則第1與第3條column是單季跟累計的income statement
        if len(df.columns) == 4:
            income_sheet[stock_id] = df[1].dropna()
            income_sheet_cumulate[stock_id] = df[3].dropna()
        # 假如有2個columns，則代表第3條column為累計的income statement，單季的從缺
        elif len(df.columns) == 2:
            income_sheet_cumulate[stock_id] = df[1].dropna()

            # 假如是第一季財報 累計 跟單季 的數值是一樣的
            if season == 1:
                income_sheet[stock_id] = df[1].dropna()

        # 取得 cash_flows
        df = dfs[3].copy().drop_duplicates(subset=0, keep='last')
        df = df.set_index(0)
        cash_flows[stock_id] = df[1].dropna()

    # 將dictionary整理成dataframe
    balance_sheet = pd.DataFrame(balance_sheet)
    income_sheet = pd.DataFrame(income_sheet)
    income_sheet_cumulate = pd.DataFrame(income_sheet_cumulate)
    cash_flows = pd.DataFrame(cash_flows)

    # 做清理
    ret = {'balance_sheet':clean(year, season, balance_sheet), 'income_sheet':clean(year, season, income_sheet),
            'income_sheet_cumulate':clean(year, season, income_sheet_cumulate), 'cash_flows':clean(year, season, cash_flows)}

    # 假如是第一季的話，則 單季 跟 累計 是一樣的
    if season == 1:
        ret['income_sheet'] = ret['income_sheet_cumulate'].copy()

    ret['income_sheet_cumulate'].columns = '累計' + ret['income_sheet_cumulate'].columns

    pickle.dump(ret, open(os.path.join('history', 'financial_statement', 'pack' + str(year) + str(season) + '.pickle'), 'wb'))

    return ret

def get_all_pickles(directory):
    ret = {}
    for i in os.listdir(directory):
        if i[:4] != 'pack':
            continue
        ret[i[4:9]] = pd.read_pickle(os.path.join(directory, i))
    return ret

def combine(d):

    tnames = ['balance_sheet',
            'cash_flows',
            'income_sheet',
            'income_sheet_cumulate']

    tbs = {t:pd.DataFrame() for t in tnames}

    for i, dfs in d.items():
        for tname in tnames:
            tbs[tname] = pd.concat([tbs[tname], dfs[tname]])
    return tbs


def fill_season4(tbs):
    # copy income sheet (will modify it later)
    income_sheet = tbs['income_sheet'].copy()

    # calculate the overlap columns
    c1 = set(tbs['income_sheet'].columns)
    c2 = set(tbs['income_sheet_cumulate'].columns)

    overlap_columns = []
    for i in c1:
        if '累計' + i in c2:
            overlap_columns.append('累計' + i)

    # get all years
    years = set(tbs['income_sheet_cumulate'].index.levels[1].year)

    for y in years:

        # get rows of the dataframe that is season 4
        ys = tbs['income_sheet_cumulate'].reset_index('stock_id').index.year == y
        ds4 = tbs['income_sheet_cumulate'].reset_index('stock_id').index.month == 3
        df4 = tbs['income_sheet_cumulate'][ds4 & ys].apply(lambda s: pd.to_numeric(s, errors='coerce')).reset_index('date')

        # get rows of the dataframe that is season 3
        yps = tbs['income_sheet_cumulate'].reset_index('stock_id').index.year == y - 1
        ds3 = tbs['income_sheet_cumulate'].reset_index('stock_id').index.month == 11
        df3 = tbs['income_sheet_cumulate'][ds3 & yps].apply(lambda s: pd.to_numeric(s, errors='coerce')).reset_index('date')

        if len(df3) == 0:
            continue
            
        # calculate the differences of income_sheet_cumulate to get income_sheet single season
        diff = df4 - df3
        diff = diff.drop(['date'], axis=1)[overlap_columns]

        # remove 累計
        diff.columns = diff.columns.str[2:]

        # 加上第四季的日期
        diff['date'] = pd.to_datetime(str(y) + '-03-31')
        diff = diff[list(c1) + ['date']].reset_index().set_index(['stock_id','date'])

        # 新增資料於income_sheet尾部
        income_sheet = pd.concat([income_sheet, diff])

    # 排序好並更新tbs
    income_sheet.reset_index(inplace=True)
    income_sheet.sort_values(['stock_id', 'date'], inplace=True)
    income_sheet.set_index(['stock_id', 'date'], inplace=True)
    return income_sheet

def to_db(tbs):

    for i, df in tbs.items():
        (df.reset_index()
             .sort_values(['stock_id', 'date'])
             .drop_duplicates(['stock_id', 'date'])
             .set_index(['stock_id', 'date'])
             .to_pickle(os.path.join('history', 'tables', i + '.pkl')))

    if not os.path.isfile(date_range_record_file):
        pickle.dump({}, open(date_range_record_file, 'wb'))

    dates = pickle.load(open(date_range_record_file, 'rb'))
    dates['financial_statement'] = (df.index.levels[1][0], df.index.levels[1][-1])
    pickle.dump(dates, open(date_range_record_file, 'wb'))


def html2db(year, season):
 
    pack_htmls(year, season, os.path.join('history', 'financial_statement', str(year) + str(season)))
    gc.collect()
    d = get_all_pickles(os.path.join('history', 'financial_statement'))
    gc.collect()

    tbs = combine(d)
    del d
    gc.collect()
    
    tbs['income_sheet'] = fill_season4(tbs)
    gc.collect()
    
    to_db(tbs)
    gc.collect()

    return {}

def crawl_finance_statement_by_date(date):
    year = date.year
    if date.month == 3:
        season = 4
        year = year - 1
        month = 11
    elif date.month == 5:
        season = 1
        month = 2
    elif date.month == 8:
        season = 2
        month = 5
    elif date.month == 11:
        season = 3
        month = 8
    else:
        return None

    if year < 2019:
        df = crawl_monthly_report(datetime.datetime(year, month, 1))
        crawl_finance_statement(year, season, df.index.levels[0].str.split(' ').str[0])
    else:
        crawl_finance_statement2019(year, season)

    html2db(year, season)
    commit()
    return {}



def commit(*commit_tables):

    ftables = os.path.join('history', 'tables')
    fitems = os.path.join('history', 'items')

    fnames = [os.path.join(ftables, f) for f in os.listdir(ftables)]
    tnames = [f[:-4] for f in os.listdir(ftables)]

    if not os.path.isdir(fitems):
        os.mkdir(fitems)

    if len(commit_tables) == 0:
        commit_tables = tnames

    for fname, tname in zip(fnames, tnames):
        
        gc.collect()

        if tname not in commit_tables:
            continue

        if fname[-4:] != '.pkl':
            continue

        fdir = os.path.join(fitems, tname)

        if (os.path.isdir(fdir) and 
            os.path.getmtime(fname) < os.path.getmtime(fdir) and 
            len(os.listdir(fdir)) != 0):
            
            print("已經成功commit過", tname, "了，跳過！")
            continue

        if os.path.isdir(fdir):
            shutil.rmtree(fdir)
            os.mkdir(fdir)
        else:
            os.mkdir(fdir)

        try:
            df = pd.read_pickle(fname)
        except:
            print("**檔案過大，無法成功commit", fname)
            continue

        # remove stock name
        df.reset_index(inplace=True)

        # 檢查是否有 stock_id 欄位
        if 'stock_id' in df.columns and sum(df['stock_id'].str.find(' ') >= 0) > 0:
            cond = df.stock_id.str[4] == ' '
            df = df[cond]
            gc.collect()
            df['stock_id'] = df['stock_id'].apply(lambda s:s[:s.index(' ')])

        print(tname)

        # 設定索引，根據可用欄位
        if 'stock_id' in df.columns and 'date' in df.columns:
            df.set_index(['stock_id', 'date'], inplace=True)
        elif 'date' in df.columns:
            df.set_index(['date'], inplace=True)
        else:
            # 如果沒有適當的欄位，保持原始索引
            pass

        # select 4 digit stock ids
        if tname == 'price':
            sids = df.index.get_level_values(0)
            df = df[sids.str.len()==4]
            gc.collect()

        if tname == 'monthly_report':
            check_monthly_revenue()
        
        if str(set(df.dtypes)) != "{dtype('float64')}":
            df = df.apply(lambda s: pd.to_numeric(s, errors='coerce'))
        gc.collect()
        
        if tname != 'benchmark':
            df[df == 0] = np.nan
        gc.collect()

        df = df[~df.index.duplicated(keep='first')]
        gc.collect()

        items = list(df.columns)
        df.reset_index(inplace=True)
        
        if tname != 'benchmark':
        
            df = df.pivot(index="date", columns="stock_id")
            gc.collect()

            for name, (_, series) in zip(items, df.items()):

                print(tname, '--', name)
                fitem = os.path.join(fdir, name.replace('+', '_').replace('/', '_'))
                #series.reset_index()\
                #    .pivot("date", "stock_id")[name].to_pickle(fitem + '.pkl')
                df[name].to_pickle(fitem + '.pkl')
        else:
            for name in items:
                print(tname, '--', name)
                fitem = os.path.join(fdir, name.replace('+', '_').replace('/', '_'))
                df[["date", "stock_id", name]].reset_index().pivot(index="date", columns="stock_id")[name].to_pickle(fitem + '.pkl')

import urllib.request
import pickle
import requests
from io import BytesIO


def crawl_cash_flows(date):
    """
    爬取現金流量表資料 - 支援增量更新，保存為DB格式

    Args:
        date: 日期物件，用於確定財報季別

    Returns:
        pandas.DataFrame: 現金流量表資料
    """
    print(f"🔄 開始爬取現金流量表資料 ({date.strftime('%Y-%m-%d')})...")

    try:
        # 根據日期確定財報年度和季別
        year, season = get_financial_year_season(date)

        print(f"📅 財報期間: {year}年第{season}季")

        # 檢查是否已有現有資料
        existing_df = load_existing_cash_flows()

        # 檢查是否需要更新
        if should_update_cash_flows(existing_df, year, season):
            print(f"🔄 需要更新 {year}年第{season}季 現金流量表資料...")

            # 爬取新的現金流量表資料
            new_data = crawl_financial_statements_for_cash_flows(year, season)

            if new_data is not None and len(new_data) > 0:
                # 合併新舊資料
                if existing_df is not None and len(existing_df) > 0:
                    # 移除重複的年季資料
                    target_date = get_financial_report_date(year, season)
                    existing_df = existing_df[existing_df.index.get_level_values('date') != target_date]

                    # 合併資料
                    combined_df = pd.concat([existing_df, new_data])
                    combined_df = combined_df.sort_index()

                    print(f"✅ 合併資料: 原有 {len(existing_df)} + 新增 {len(new_data)} = 總計 {len(combined_df)} 筆")
                    return combined_df
                else:
                    print(f"✅ 新建現金流量表資料: {len(new_data)} 筆")
                    return new_data
            else:
                print(f"⚠️ 未能獲取新的現金流量表資料")
                return existing_df if existing_df is not None else pd.DataFrame()
        else:
            print(f"✅ 現金流量表資料已是最新，無需更新")
            return existing_df if existing_df is not None else pd.DataFrame()

    except Exception as e:
        print(f"❌ 爬取現金流量表失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def get_financial_year_season(date):
    """根據日期確定財報年度和季別"""
    year = date.year
    month = date.month

    if month <= 3:
        # Q4 財報 (前一年)
        return year - 1, 4
    elif month <= 5:
        # Q1 財報
        return year, 1
    elif month <= 8:
        # Q2 財報
        return year, 2
    else:
        # Q3 財報
        return year, 3

def get_financial_report_date(year, season):
    """獲取財報發布日期"""
    if season == 1:
        return f"{year}-05-15"
    elif season == 2:
        return f"{year}-08-14"
    elif season == 3:
        return f"{year}-11-14"
    else:  # season == 4
        return f"{year+1}-03-31"

def load_existing_cash_flows():
    """載入現有的現金流量表資料"""
    try:
        import os

        # 優先從 DB 檔案載入
        db_path = os.path.join('history', 'tables', 'cash_flows.db')
        if os.path.exists(db_path):
            import sqlite3
            conn = sqlite3.connect(db_path)
            df = pd.read_sql_query("SELECT * FROM cash_flows", conn)
            conn.close()

            # 設置 MultiIndex
            df = df.set_index(['stock_id', 'date'])
            print(f"📖 從 DB 載入現有現金流量表資料: {len(df)} 筆")
            return df

        # 備用：從 pickle 檔案載入
        pickle_path = os.path.join('history', 'cash_flows.pkl')
        if os.path.exists(pickle_path):
            df = pd.read_pickle(pickle_path)
            print(f"📖 從 pickle 載入現有現金流量表資料: {len(df)} 筆")
            return df

        print(f"📖 未找到現有現金流量表資料")
        return None

    except Exception as e:
        print(f"⚠️ 載入現有現金流量表資料失敗: {e}")
        return None

def should_update_cash_flows(existing_df, year, season):
    """檢查是否需要更新現金流量表資料"""
    if existing_df is None or len(existing_df) == 0:
        return True

    # 檢查是否已有該年季的資料
    target_date = get_financial_report_date(year, season)

    if hasattr(existing_df.index, 'get_level_values'):
        existing_dates = existing_df.index.get_level_values('date').unique()
    else:
        existing_dates = existing_df.index.unique()

    return target_date not in existing_dates

def crawl_financial_statements_for_cash_flows(year, season):
    """爬取指定年季的財務報表現金流量表部分"""
    try:
        print(f"🔍 爬取 {year}年第{season}季 財務報表...")

        # 使用現有的財務報表爬蟲邏輯
        # 這裡重用 crawl_finance_statement_by_date 的核心邏輯

        # 獲取所有股票代碼
        stock_ids = get_all_stock_ids()

        if not stock_ids:
            print(f"⚠️ 未能獲取股票代碼列表")
            return None

        print(f"📊 準備爬取 {len(stock_ids)} 支股票的現金流量表...")

        # 下載財務報表 HTML
        download_html(year, season, stock_ids, report_type='C')

        # 解析 HTML 並提取現金流量表
        directory = os.path.join('history', 'financial_statement', str(year) + str(season))

        if os.path.exists(directory):
            # 使用現有的 pack_htmls 函數解析
            financial_data = pack_htmls(year, season, directory)

            if 'cash_flows' in financial_data:
                cash_flows_df = financial_data['cash_flows']

                if len(cash_flows_df) > 0:
                    print(f"✅ 成功解析現金流量表: {len(cash_flows_df)} 筆資料")
                    return cash_flows_df
                else:
                    print(f"⚠️ 現金流量表資料為空")
                    return None
            else:
                print(f"⚠️ 財務報表中未找到現金流量表")
                return None
        else:
            print(f"⚠️ 財務報表目錄不存在: {directory}")
            return None

    except Exception as e:
        print(f"❌ 爬取財務報表現金流量表失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_all_stock_ids():
    """獲取所有股票代碼"""
    try:
        # 方法1: 嘗試從現有的 newprice.db 獲取股票代碼
        try:
            import sqlite3
            newprice_db = os.path.join('history', 'tables', 'newprice.db')
            if os.path.exists(newprice_db):
                conn = sqlite3.connect(newprice_db)
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data LIMIT 50")
                stock_ids = [row[0].split()[0] for row in cursor.fetchall() if row[0].split()[0].isdigit()]
                conn.close()

                if stock_ids:
                    print(f"📊 從 newprice.db 獲取到 {len(stock_ids)} 個股票代碼")
                    return stock_ids[:50]  # 限制數量避免過載
        except Exception as e:
            print(f"⚠️ 從 newprice.db 獲取股票代碼失敗: {e}")

        # 方法2: 嘗試從現有的 cash_flows.db 獲取股票代碼
        try:
            cash_flows_db = os.path.join('history', 'tables', 'cash_flows.db')
            if os.path.exists(cash_flows_db):
                conn = sqlite3.connect(cash_flows_db)
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT stock_id FROM cash_flows LIMIT 30")
                stock_ids = [row[0] for row in cursor.fetchall() if row[0].isdigit()]
                conn.close()

                if stock_ids:
                    print(f"📊 從 cash_flows.db 獲取到 {len(stock_ids)} 個股票代碼")
                    return stock_ids[:30]
        except Exception as e:
            print(f"⚠️ 從 cash_flows.db 獲取股票代碼失敗: {e}")

        # 方法3: 備用 - 使用固定的主要股票代碼
        print(f"📊 使用備用股票代碼列表")
        return ['2330', '2317', '2454', '6505', '2881', '2882', '2883', '2884', '2885', '2886',
                '1101', '1102', '1216', '1301', '1303', '2002', '2105', '2207', '2301', '2303']

    except Exception as e:
        print(f"⚠️ 獲取股票代碼失敗: {e}")
        # 最終備用：使用固定的主要股票代碼
        return ['2330', '2317', '2454', '6505', '2881']

def convert_roc_date_to_standard(roc_date_str):
    """
    轉換民國年日期為標準日期格式

    Args:
        roc_date_str: 民國年日期字串，如 '1140726'

    Returns:
        str: 標準日期格式，如 '2025-07-26'
    """
    try:
        if not roc_date_str or len(str(roc_date_str)) != 7:
            return None

        roc_date_str = str(roc_date_str)
        roc_year = int(roc_date_str[:3])  # 前3位是民國年
        month = roc_date_str[3:5]         # 中間2位是月
        day = roc_date_str[5:7]           # 後2位是日

        # 轉換為西元年
        western_year = roc_year + 1911

        return f"{western_year}-{month}-{day}"

    except Exception as e:
        print(f"⚠️ 日期轉換失敗: {roc_date_str} -> {e}")
        return None

def standardize_financial_data(df, data_type):
    """標準化財務資料格式"""

    # 標準化欄位名稱
    if '公司代號' in df.columns:
        df = df.rename(columns={'公司代號': 'stock_id'})
    if '公司名稱' in df.columns:
        df = df.rename(columns={'公司名稱': 'stock_name'})

    # 轉換民國年為西元年
    if '年度' in df.columns:
        df['year'] = df['年度'].astype(int) + 1911
        df = df.drop(columns=['年度'])  # 移除原始民國年欄位

    if '季別' in df.columns:
        df['quarter'] = df['季別'].astype(int)
        df = df.drop(columns=['季別'])  # 移除原始季別欄位

    # 轉換出表日期為標準日期格式
    if '出表日期' in df.columns:
        df['report_date'] = df['出表日期'].apply(convert_roc_date_to_standard)
        df = df.drop(columns=['出表日期'])  # 移除原始出表日期欄位

    # 添加統一的日期欄位 (類似 price.db 的格式)
    if 'year' in df.columns and 'quarter' in df.columns:
        # 根據年度和季別生成統一的日期
        df['date'] = df.apply(lambda row: f"{int(row['year'])}-{int(row['quarter']*3):02d}-01", axis=1)
    else:
        from datetime import datetime
        df['date'] = datetime.now().strftime('%Y-%m-%d')

    # 添加資料類型
    df['data_type'] = data_type

    # 確保 stock_id 為字串
    if 'stock_id' in df.columns:
        df['stock_id'] = df['stock_id'].astype(str)

    return df

def crawl_unified_financial_data(date):
    """
    使用 TWSE OpenAPI 爬取統一財務資料 (綜合損益表和資產負債表合併)

    Args:
        date: 日期物件

    Returns:
        pandas.DataFrame: 統一的財務資料
    """
    print(f"🔄 開始爬取統一財務資料 ({date.strftime('%Y-%m-%d')})...")

    try:
        import requests
        import time

        # TWSE OpenAPI 客戶端設定
        BASE_URL = "https://openapi.twse.com.tw/v1"
        USER_AGENT = "finlab-crawler/1.0"

        def get_twse_data(endpoint, timeout=30.0):
            """獲取 TWSE API 資料"""
            url = f"{BASE_URL}{endpoint}"
            print(f"🔍 獲取資料: {endpoint}")

            try:
                resp = requests.get(
                    url,
                    headers={
                        "User-Agent": USER_AGENT,
                        "Accept": "application/json"
                    },
                    verify=False,
                    timeout=timeout
                )
                resp.raise_for_status()
                resp.encoding = 'utf-8'
                data = resp.json()

                return data if isinstance(data, list) else [data] if data else []

            except Exception as e:
                print(f"❌ 獲取資料失敗 {url}: {e}")
                return []

        # 爬取統一財務資料
        all_financial_data = []

        # 1. 爬取綜合損益表
        print("📊 爬取綜合損益表...")
        income_data = get_twse_data("/opendata/t187ap06_L_ci")

        if income_data:
            income_df = pd.DataFrame(income_data)
            income_df = standardize_financial_data(income_df, 'income_statement')

            # 添加表格類型前綴到財務項目欄位
            financial_columns = [col for col in income_df.columns
                               if col not in ['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date', 'data_type']]

            rename_dict = {col: f"income_{col}" for col in financial_columns}
            income_df = income_df.rename(columns=rename_dict)

            all_financial_data.append(income_df)
            print(f"✅ 綜合損益表: {len(income_df)} 筆資料")
        else:
            print("⚠️ 未獲取到綜合損益表資料")

        # 等待避免 API 限制
        time.sleep(2)

        # 2. 爬取資產負債表
        print("📊 爬取資產負債表...")
        balance_data = get_twse_data("/opendata/t187ap07_L_ci")

        if balance_data:
            balance_df = pd.DataFrame(balance_data)
            balance_df = standardize_financial_data(balance_df, 'balance_sheet')

            # 添加表格類型前綴到財務項目欄位
            financial_columns = [col for col in balance_df.columns
                               if col not in ['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date', 'data_type']]

            rename_dict = {col: f"balance_{col}" for col in financial_columns}
            balance_df = balance_df.rename(columns=rename_dict)

            all_financial_data.append(balance_df)
            print(f"✅ 資產負債表: {len(balance_df)} 筆資料")
        else:
            print("⚠️ 未獲取到資產負債表資料")

        # 3. 合併資料
        if len(all_financial_data) > 0:
            print("🔄 合併財務資料...")

            # 使用 stock_id 和 date 作為合併鍵
            merged_df = all_financial_data[0]

            for i in range(1, len(all_financial_data)):
                merged_df = pd.merge(
                    merged_df,
                    all_financial_data[i],
                    on=['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date'],
                    how='outer',
                    suffixes=('', '_dup')
                )

                # 移除重複的 data_type 欄位
                if 'data_type_dup' in merged_df.columns:
                    merged_df = merged_df.drop(columns=['data_type_dup'])

            # 設置統一的 data_type
            merged_df['data_type'] = 'financial_statements'

            print(f"✅ 合併完成: {len(merged_df)} 筆資料，{len(merged_df.columns)} 個欄位")

            # 儲存統一財務資料
            save_unified_financial_data(merged_df)

            return merged_df
        else:
            print("❌ 無法獲取任何財務資料")
            return pd.DataFrame()

    except Exception as e:
        print(f"❌ 爬取統一財務資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def save_unified_financial_data(df):
    """儲存統一的財務資料到單一檔案"""

    print(f"\n💾 儲存統一財務資料...")

    try:
        import sqlite3

        # 確保目錄存在 - 使用絕對路徑
        target_dir = r'D:\Finlab\history\tables'
        os.makedirs(target_dir, exist_ok=True)

        if not df.empty:
            # 調整欄位順序：stock_id 第1位，stock_name 第2位，report_date 第3位，date 第4位
            columns = list(df.columns)

            # 確保關鍵欄位存在
            required_cols = ['stock_id', 'stock_name', 'report_date', 'date']
            missing_cols = [col for col in required_cols if col not in columns]
            if not missing_cols:
                # 移除關鍵欄位從原位置
                for col in required_cols:
                    if col in columns:
                        columns.remove(col)

                # 按正確順序重新插入
                new_columns = ['stock_id', 'stock_name', 'report_date', 'date'] + columns

                # 重新排列 DataFrame
                df = df[new_columns]
                print(f"✅ 已調整欄位順序: stock_id(1) → stock_name(2) → report_date(3) → date(4) → 其他欄位")
            else:
                print(f"⚠️ 缺少必要欄位，跳過欄位調整: {missing_cols}")

            # 儲存到統一的財務資料檔案
            unified_db_path = os.path.join(target_dir, 'financial_data.db')
            conn = sqlite3.connect(unified_db_path)
            df.to_sql('financial_data', conn, if_exists='replace', index=False)
            conn.close()

            print(f"✅ 統一財務資料已儲存: {unified_db_path}")
            print(f"📊 資料筆數: {len(df):,}")
            print(f"📋 總欄位數: {len(df.columns)}")

            # 顯示主要欄位
            key_columns = ['stock_id', 'stock_name', 'year', 'quarter', 'date', 'report_date']
            income_columns = [col for col in df.columns if col.startswith('income_')][:3]
            balance_columns = [col for col in df.columns if col.startswith('balance_')][:3]

            print(f"📋 主要欄位:")
            print(f"   基本資訊: {key_columns}")
            print(f"   損益項目: {income_columns}...")
            print(f"   資產負債: {balance_columns}...")

            return True
        else:
            print("⚠️ 無資料可儲存")
            return False

    except Exception as e:
        print(f"❌ 儲存統一財務資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_twse_financial_data(income_df, balance_df):
    """儲存 TWSE 財務資料到資料庫"""

    try:
        import sqlite3

        # 確保目錄存在 - 使用絕對路徑
        target_dir = r'D:\Finlab\history\tables'
        os.makedirs(target_dir, exist_ok=True)

        # 儲存綜合損益表
        if not income_df.empty:
            income_db_path = os.path.join(target_dir, 'income_statements.db')
            conn = sqlite3.connect(income_db_path)
            income_df.to_sql('income_statements', conn, if_exists='replace', index=False)
            conn.close()
            print(f"💾 綜合損益表已儲存: {income_db_path}")

        # 儲存資產負債表
        if not balance_df.empty:
            balance_db_path = os.path.join(target_dir, 'balance_sheets.db')
            conn = sqlite3.connect(balance_db_path)
            balance_df.to_sql('balance_sheets', conn, if_exists='replace', index=False)
            conn.close()
            print(f"💾 資產負債表已儲存: {balance_db_path}")

        # 儲存合併的財務資料
        if not income_df.empty or not balance_df.empty:
            combined_db_path = os.path.join(target_dir, 'financial_statements.db')
            conn = sqlite3.connect(combined_db_path)

            if not income_df.empty:
                income_df.to_sql('income_statements', conn, if_exists='replace', index=False)

            if not balance_df.empty:
                balance_df.to_sql('balance_sheets', conn, if_exists='replace', index=False)

            conn.close()
            print(f"💾 合併財務資料已儲存: {combined_db_path}")

    except Exception as e:
        print(f"❌ 儲存 TWSE 財務資料失敗: {e}")
        import traceback
        traceback.print_exc()

def crawl_gitlab_backup(target: str, date=None):
    crawlers_once_list = ['twse_divide_ratio', 'otc_divide_ratio', 'twse_cap_reduction', 'otc_cap_reduction']
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko)'
                      ' Chrome/39.0.2171.95 Safari/537.36'
    }

    if date is not None:
        date_str = date.strftime('%Y%m%d')
        if target == 'monthly_report':
            date_str = date.strftime('%Y%m')

    if target in crawlers_once_list:
        url = f'https://github.com/finlab-python/tw_stock_class/raw/master/data/{target}/{target}.pickle?inline=false'
    else:
        url = f'https://github.com/finlab-python/tw_stock_class/raw/master/data/{target}/{date_str}.pickle?inline=false'
    try:
        res = requests.get(url, headers=headers)
        df = pd.read_pickle(BytesIO(res.content))
        if 'stock_id' in df.columns and 'date' in df.columns:
            df.set_index(['stock_id', 'date'], inplace=True)
        return df
    except Exception as e:
        return None


# ==================== 資產負債表爬蟲 (TWSEMCPServer方法) ====================

def crawl_balance_sheet():
    """
    爬取資產負債表 - 使用台灣證交所OpenAPI
    基於TWSEMCPServer專案的實現

    ⚠️ 重要：由於TWSE OpenAPI與歷史資料使用不同的欄位結構，
    此函數將分別保存新資料，避免欄位名稱不一致的問題

    Returns:
        pandas.DataFrame: 僅返回歷史資料，新資料保存為獨立檔案
    """
    import urllib3
    from typing import List, Dict, Any, Optional
    import os

    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    class TWSEOpenAPIClient:
        """台灣證交所OpenAPI客戶端"""

        BASE_URL = "https://openapi.twse.com.tw/v1"
        USER_AGENT = "finlab-crawler/1.0"

        @classmethod
        def get_data(cls, endpoint: str, timeout: float = 30.0) -> List[Dict[str, Any]]:
            """獲取API資料"""
            url = f"{cls.BASE_URL}{endpoint}"

            try:
                resp = requests.get(
                    url,
                    headers={
                        "User-Agent": cls.USER_AGENT,
                        "Accept": "application/json"
                    },
                    verify=False,
                    timeout=timeout
                )
                resp.raise_for_status()
                resp.encoding = 'utf-8'
                data = resp.json()

                return data if isinstance(data, list) else [data] if data else []

            except Exception as e:
                print(f"❌ TWSE API請求失敗: {e}")
                return []

    print("🔄 開始使用TWSE OpenAPI爬取資產負債表...")

    client = TWSEOpenAPIClient()

    # 獲取所有可用的資產負債表資料
    print("📊 獲取所有可用的資產負債表資料...")
    raw_data = client.get_data("/opendata/t187ap07_L_ci")

    if not raw_data:
        print("❌ 無法獲取資產負債表資料")
        return pd.DataFrame()

    print(f"✅ 獲取到 {len(raw_data)} 筆資產負債表資料")

    # 轉換為DataFrame
    try:
        new_df = pd.DataFrame(raw_data)

        # 標準化欄位名稱和格式
        if '公司代號' in new_df.columns:
            new_df['stock_id'] = new_df['公司代號']

        # 添加日期資訊
        current_date = datetime.datetime.now()
        new_df['date'] = current_date

        # 設定MultiIndex (stock_id, date) 以符合finlab格式
        if 'stock_id' in new_df.columns:
            new_df = new_df.set_index(['stock_id', 'date'])

        print(f"✅ 成功轉換新資料為DataFrame: {new_df.shape}")
        print(f"📊 新資料包含欄位: {len(new_df.columns)} 個")
        print(f"📊 新資料包含股票: {len(new_df.index.get_level_values('stock_id').unique())} 支")

        # 保存新資料到獨立檔案
        new_data_file = 'data/balance_sheet_twse_api.pkl'
        try:
            new_df.to_pickle(new_data_file)
            print(f"✅ 新資料已保存到: {new_data_file}")
            print(f"📊 新資料: {new_df.shape}")
        except Exception as e:
            print(f"⚠️ 保存新資料失敗: {e}")

        # 讀取並返回現有的歷史資料（不進行合併）
        existing_file = 'data/balance_sheet.pkl'
        if os.path.exists(existing_file):
            try:
                print(f"\n📂 讀取現有歷史資料...")
                existing_df = pd.read_pickle(existing_file)
                print(f"📊 歷史資料: {existing_df.shape}")
                print(f"   - 股票數量: {len(existing_df.index.get_level_values('stock_id').unique())}")
                print(f"   - 日期範圍: {existing_df.index.get_level_values('date').min()} 至 {existing_df.index.get_level_values('date').max()}")

                print(f"\n💡 說明:")
                print(f"   - 歷史資料保持原格式: {existing_file}")
                print(f"   - 新資料獨立保存: {new_data_file}")
                print(f"   - 避免欄位名稱不一致問題")

                return existing_df

            except Exception as e:
                print(f"⚠️ 讀取歷史資料失敗: {e}")
                print(f"💡 返回新資料...")
                return new_df
        else:
            print(f"ℹ️ 歷史檔案不存在，返回新資料")
            return new_df

    except Exception as e:
        print(f"❌ 轉換DataFrame失敗: {e}")
        return pd.DataFrame()
