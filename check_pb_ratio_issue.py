#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查股價淨值比數據承襲問題
"""

import os
import sqlite3
from datetime import datetime, timedelta

def check_pb_ratio_data():
    """檢查PE資料庫中的股價淨值比數據"""
    print("🔍 檢查股價淨值比數據承襲問題")
    print("=" * 60)
    
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    
    if not os.path.exists(pe_db_path):
        print(f"❌ PE資料庫不存在: {pe_db_path}")
        return
    
    try:
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 查詢2429銘旺科的歷史數據
        print("📊 查詢2429銘旺科的歷史PE資料:")
        print("-" * 40)
        
        cursor.execute("""
            SELECT date, stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
            FROM pe_data
            WHERE stock_id = '2429'
            ORDER BY date DESC
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("日期\t\t股票代碼\t股票名稱\t殖利率\t本益比\t股價淨值比")
            print("-" * 80)
            for result in results:
                date, stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio = result
                print(f"{date}\t{stock_id}\t{stock_name}\t{dividend_yield}\t{pe_ratio}\t{pb_ratio}")
        else:
            print("❌ 找不到2429的PE資料")
        
        print("\n" + "=" * 60)
        print("🔍 檢查最新日期的所有股票PB資料分布:")
        print("-" * 40)
        
        # 查詢最新日期
        cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
        latest_date = cursor.fetchone()
        
        if latest_date:
            latest_date = latest_date[0]
            print(f"最新日期: {latest_date}")
            
            # 查詢該日期的PB資料分布
            cursor.execute("""
                SELECT pb_ratio, COUNT(*) as count
                FROM pe_data
                WHERE date = ? AND pb_ratio IS NOT NULL
                GROUP BY pb_ratio
                ORDER BY count DESC
                LIMIT 20
            """, (latest_date,))
            
            pb_distribution = cursor.fetchall()
            
            print("\nPB值\t出現次數")
            print("-" * 20)
            for pb_ratio, count in pb_distribution:
                print(f"{pb_ratio}\t{count}")
            
            # 檢查是否有異常的重複值
            suspicious_values = [pb for pb, count in pb_distribution if count > 50]
            if suspicious_values:
                print(f"\n⚠️ 可疑的重複PB值 (出現超過50次): {suspicious_values}")
                
                # 查詢具體的股票
                for pb_value in suspicious_values[:3]:  # 只檢查前3個
                    print(f"\n📋 PB值 {pb_value} 的股票列表:")
                    cursor.execute("""
                        SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                        FROM pe_data
                        WHERE date = ? AND pb_ratio = ?
                        LIMIT 10
                    """, (latest_date, pb_value))
                    
                    stocks = cursor.fetchall()
                    for stock in stocks:
                        print(f"  {stock[0]} {stock[1]} - 殖利率:{stock[2]}, 本益比:{stock[3]}, PB:{stock[4]}")
        
        print("\n" + "=" * 60)
        print("🔍 檢查2429在不同日期的數據變化:")
        print("-" * 40)
        
        # 查詢2429在最近幾個日期的數據
        cursor.execute("""
            SELECT DISTINCT date
            FROM pe_data
            ORDER BY date DESC
            LIMIT 5
        """)
        
        recent_dates = cursor.fetchall()
        
        for date_tuple in recent_dates:
            date = date_tuple[0]
            cursor.execute("""
                SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                FROM pe_data
                WHERE date = ? AND stock_id = '2429'
            """, (date,))
            
            result = cursor.fetchone()
            if result:
                _, stock_name, dividend_yield, pe_ratio, pb_ratio = result
                print(f"{date}: 殖利率={dividend_yield}, 本益比={pe_ratio}, PB={pb_ratio}")
            else:
                print(f"{date}: 無資料")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("📋 分析結果:")
        print("1. 檢查2429的歷史數據是否有異常重複")
        print("2. 檢查PB值6.72是否在多個股票中重複出現")
        print("3. 確認數據是否有承襲上一筆資料的問題")
        
    except Exception as e:
        print(f"❌ 檢查過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def check_data_freshness():
    """檢查數據新鮮度"""
    print("\n" + "=" * 60)
    print("🕒 檢查數據新鮮度")
    print("=" * 60)
    
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    try:
        # 檢查PE資料庫
        if os.path.exists(pe_db_path):
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
            latest_pe_date = cursor.fetchone()
            
            if latest_pe_date:
                latest_pe_date = latest_pe_date[0]
                print(f"PE資料庫最新日期: {latest_pe_date}")
                
                # 計算距離今天的天數
                today = datetime.now().strftime('%Y-%m-%d')
                pe_date_obj = datetime.strptime(latest_pe_date, '%Y-%m-%d')
                today_obj = datetime.strptime(today, '%Y-%m-%d')
                days_diff = (today_obj - pe_date_obj).days
                
                print(f"距離今天: {days_diff} 天")
                
                if days_diff > 7:
                    print("⚠️ PE資料可能過舊，建議更新")
                else:
                    print("✅ PE資料相對新鮮")
            
            conn.close()
        
        # 檢查股價資料庫
        if os.path.exists(price_db_path):
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT DISTINCT date FROM stock_daily_data ORDER BY date DESC LIMIT 1")
            latest_price_date = cursor.fetchone()
            
            if latest_price_date:
                latest_price_date = latest_price_date[0]
                print(f"股價資料庫最新日期: {latest_price_date}")
                
                # 計算距離今天的天數
                price_date_obj = datetime.strptime(latest_price_date, '%Y-%m-%d')
                days_diff = (today_obj - price_date_obj).days
                
                print(f"距離今天: {days_diff} 天")
                
                if days_diff > 3:
                    print("⚠️ 股價資料可能過舊，建議更新")
                else:
                    print("✅ 股價資料相對新鮮")
            
            conn.close()
            
    except Exception as e:
        print(f"❌ 檢查數據新鮮度失敗: {e}")

if __name__ == "__main__":
    check_pb_ratio_data()
    check_data_freshness()
