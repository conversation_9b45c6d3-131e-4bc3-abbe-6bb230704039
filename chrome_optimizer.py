#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Chrome 瀏覽器優化器
專門用於配置 Chrome 瀏覽器選項，消除常見的錯誤訊息和警告
特別針對 Selenium 自動化和無頭模式進行優化
"""

import os
import logging
from selenium.webdriver.chrome.options import Options

class ChromeOptimizer:
    """Chrome 瀏覽器優化器類"""
    
    def __init__(self, headless=True, download_dir=None):
        self.headless = headless
        self.download_dir = download_dir or os.getcwd()
        self.logger = logging.getLogger(__name__)
        
    def get_optimized_options(self):
        """獲取優化後的 Chrome 選項"""
        options = Options()
        
        # 基本設定
        self._add_basic_options(options)
        
        # 下載設定
        self._add_download_options(options)
        
        # GPU 和渲染優化
        self._add_gpu_options(options)
        
        # 無頭模式設定
        if self.headless:
            self._add_headless_options(options)
        
        # 性能優化
        self._add_performance_options(options)
        
        # 安全和隱私設定
        self._add_security_options(options)
        
        # 日誌和錯誤抑制
        self._add_logging_options(options)
        
        return options
    
    def _add_basic_options(self, options):
        """添加基本選項"""
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
    def _add_download_options(self, options):
        """添加下載相關選項"""
        prefs = {
            "download.default_directory": os.path.abspath(self.download_dir),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.default_content_setting_values.automatic_downloads": 1
        }
        options.add_experimental_option("prefs", prefs)
        
    def _add_gpu_options(self, options):
        """添加 GPU 和渲染相關選項（消除 GPU 錯誤訊息）"""
        # 基本 GPU 禁用
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-gpu-sandbox')
        options.add_argument('--disable-software-rasterizer')
        
        # WebGL 和 3D API 禁用
        options.add_argument('--disable-webgl')
        options.add_argument('--disable-webgl2')
        options.add_argument('--disable-3d-apis')
        
        # 硬體加速禁用
        options.add_argument('--disable-accelerated-2d-canvas')
        options.add_argument('--disable-accelerated-jpeg-decoding')
        options.add_argument('--disable-accelerated-mjpeg-decode')
        options.add_argument('--disable-accelerated-video-decode')
        options.add_argument('--disable-accelerated-video-encode')
        
        # GPU 記憶體緩衝區禁用
        options.add_argument('--disable-gpu-memory-buffer-compositor-resources')
        options.add_argument('--disable-gpu-memory-buffer-video-frames')
        
        # ANGLE 相關設定（解決 D3D11 錯誤）
        options.add_argument('--disable-angle-features=d3d11')
        options.add_argument('--use-angle=swiftshader')
        
        # Viz 顯示合成器禁用
        options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        
    def _add_headless_options(self, options):
        """添加無頭模式選項"""
        options.add_argument('--headless=new')  # 使用新版無頭模式
        options.add_argument('--disable-gpu-sandbox')  # 無頭模式下的額外設定
        
    def _add_performance_options(self, options):
        """添加性能優化選項"""
        # 禁用不必要的功能
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript-harmony-shipping')
        
        # 背景處理優化
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-background-mode')
        
        # 組件和服務禁用
        options.add_argument('--disable-component-extensions-with-background-pages')
        options.add_argument('--disable-component-update')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-hang-monitor')
        
    def _add_security_options(self, options):
        """添加安全和隱私選項"""
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-field-trial-config')
        
    def _add_logging_options(self, options):
        """添加日誌和錯誤抑制選項"""
        # 基本日誌設定
        options.add_argument('--log-level=3')  # 只顯示嚴重錯誤
        options.add_argument('--silent')
        options.add_argument('--disable-logging')
        
        # 開發工具和除錯禁用
        options.add_argument('--disable-dev-tools')
        options.add_argument('--disable-breakpad')
        options.add_argument('--disable-crash-reporter')
        
        # 通知和提示禁用
        options.add_argument('--disable-notifications')
        options.add_argument('--no-first-run')
        options.add_argument('--safebrowsing-disable-auto-update')
        
        # 自動化相關設定
        options.add_argument('--enable-automation')
        options.add_argument('--password-store=basic')
        options.add_argument('--use-mock-keychain')
        options.add_argument('--metrics-recording-only')
        
        # 實驗性選項
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.add_experimental_option('useAutomationExtension', False)
        
    def create_driver(self, driver_path=None):
        """創建優化後的 Chrome WebDriver"""
        from selenium import webdriver
        
        options = self.get_optimized_options()
        
        try:
            if driver_path:
                driver = webdriver.Chrome(executable_path=driver_path, options=options)
            else:
                driver = webdriver.Chrome(options=options)
            
            # 執行 JavaScript 來隱藏自動化檢測
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("✅ 優化版 Chrome 瀏覽器已啟動")
            return driver
            
        except Exception as e:
            self.logger.error(f"❌ Chrome 瀏覽器啟動失敗: {e}")
            return None
    
    @staticmethod
    def suppress_selenium_logs():
        """抑制 Selenium 相關的日誌輸出"""
        # 設定 Selenium 日誌級別
        selenium_logger = logging.getLogger('selenium')
        selenium_logger.setLevel(logging.WARNING)
        
        # 設定 urllib3 日誌級別
        urllib3_logger = logging.getLogger('urllib3')
        urllib3_logger.setLevel(logging.WARNING)
        
        # 抑制 Chrome 相關警告
        os.environ['WDM_LOG_LEVEL'] = '0'
        
    @staticmethod
    def get_chrome_version():
        """獲取系統中的 Chrome 版本"""
        try:
            import subprocess
            import re
            
            # Windows
            if os.name == 'nt':
                result = subprocess.run([
                    'reg', 'query', 
                    'HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon',
                    '/v', 'version'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    version_match = re.search(r'version\s+REG_SZ\s+(\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if version_match:
                        return version_match.group(1)
            
            # Linux/Mac
            else:
                result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    version_match = re.search(r'Google Chrome (\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if version_match:
                        return version_match.group(1)
            
        except Exception:
            pass
        
        return "未知版本"

def create_optimized_chrome_driver(headless=True, download_dir=None, driver_path=None):
    """便捷函數：創建優化後的 Chrome WebDriver"""
    optimizer = ChromeOptimizer(headless=headless, download_dir=download_dir)
    
    # 抑制日誌
    ChromeOptimizer.suppress_selenium_logs()
    
    return optimizer.create_driver(driver_path=driver_path)

# 使用範例
if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO)
    
    print("🔧 Chrome 瀏覽器優化器測試")
    print(f"Chrome 版本: {ChromeOptimizer.get_chrome_version()}")
    
    # 創建優化後的瀏覽器
    driver = create_optimized_chrome_driver(
        headless=True,
        download_dir="./downloads"
    )
    
    if driver:
        print("✅ 優化版瀏覽器創建成功")
        
        # 簡單測試
        try:
            driver.get("https://www.google.com")
            print(f"✅ 測試頁面載入成功: {driver.title}")
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        finally:
            driver.quit()
            print("🔧 瀏覽器已關閉")
    else:
        print("❌ 優化版瀏覽器創建失敗")
