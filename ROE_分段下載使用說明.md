# ROE分段下載器使用說明

## 概述

本系統已升級支援**分段下載**功能，可以完整下載GoodInfo網站上的**全部1898筆ROE資料**，並自動處理CSV檔案中的重複標題行問題。

## 主要改進

### 1. 分段下載功能
- **問題解決**: 原本只能下載1-300筆，現在支援完整1898筆
- **分段策略**: 自動分為7個分段下載
  - 第1段: 1-300筆
  - 第2段: 301-600筆  
  - 第3段: 601-900筆
  - 第4段: 901-1200筆
  - 第5段: 1201-1500筆
  - 第6段: 1501-1800筆
  - 第7段: 1801-1898筆

### 2. CSV資料清理
- **問題解決**: 自動移除CSV中每隔幾列出現的重複標題行
- **智能識別**: 自動識別包含"代號"、"名稱"、"ROE"等關鍵字的標題行
- **資料完整性**: 保留所有有效資料行，確保資料不遺失

### 3. 自動數據庫匯入
- **即時處理**: 每個分段下載完成後立即清理並匯入數據庫
- **避免重複**: 使用INSERT OR REPLACE避免重複資料
- **統計報告**: 提供詳細的匯入統計資訊

## 使用方法

### 方法1: 使用GUI界面
```bash
python roe_data_downloader_gui.py
```
- 選擇年份和資料範圍
- 點擊"開始下載"
- 系統會自動執行分段下載和匯入

### 方法2: 使用命令行
```python
from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

# 創建下載器
downloader = GoodinfoROECSVDownloader()

# 完整分段下載並匯入數據庫
success = downloader.download_and_import_all_segments(year=2023)

if success:
    print("✅ 完整下載和匯入成功")
else:
    print("❌ 下載失敗")
```

### 方法3: 單一分段測試
```python
# 測試單一分段下載
csv_file = downloader.download_roe_csv_segment(
    year=2023, 
    start_row=1, 
    end_row=300
)

if csv_file:
    # 匯入數據庫
    downloader.import_csv_to_database(csv_file)
```

## 新增功能說明

### 1. `download_roe_csv_segments(year, segments)`
- 執行多個分段的下載
- 可自定義分段範圍
- 返回所有成功下載的CSV檔案列表

### 2. `download_roe_csv_segment(year, start_row, end_row)`
- 下載指定範圍的資料
- 自動設置網頁顯示範圍
- 檔案自動重命名包含分段資訊

### 3. `clean_csv_data(csv_file)`
- 清理CSV中的重複標題行
- 智能識別標題行
- 保留所有有效資料

### 4. `import_multiple_csv_to_database(csv_files)`
- 批量匯入多個CSV檔案
- 自動清理每個檔案
- 提供詳細統計報告

### 5. `download_and_import_all_segments(year)`
- 一鍵完成完整流程
- 分段下載 → 清理資料 → 匯入數據庫
- 自動清理臨時檔案

## 測試功能

### 執行完整測試
```bash
python test_segment_download.py
```

測試項目包括:
1. CSV清理功能測試
2. 分段下載功能測試  
3. 數據庫匯入功能測試

### 執行原有測試
```bash
python goodinfo_roe_csv_downloader.py
```
選擇測試模式:
- 模式1: 單一分段測試
- 模式2: 完整分段下載測試

## 資料庫結構

資料會儲存在SQLite數據庫中，表格結構如下:

```sql
CREATE TABLE roe_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code TEXT NOT NULL,           -- 股票代號
    stock_name TEXT NOT NULL,           -- 股票名稱  
    roe_value REAL,                     -- ROE值(%)
    roe_change REAL,                    -- ROE變化
    eps_value REAL,                     -- EPS值(元)
    report_year INTEGER,                -- 財報年度
    rank_position INTEGER,              -- 排名
    crawl_date TEXT,                    -- 爬取時間
    data_source TEXT,                   -- 資料來源
    UNIQUE(stock_code, report_year)     -- 避免重複
);
```

## 檔案說明

- `goodinfo_roe_csv_downloader.py`: 主要下載器(已升級)
- `roe_data_downloader_gui.py`: GUI界面(已升級)  
- `test_segment_download.py`: 測試腳本(新增)
- `ROE_分段下載使用說明.md`: 本說明文檔(新增)

## 注意事項

1. **網路穩定性**: 分段下載需要穩定的網路連線
2. **下載間隔**: 各分段間會自動等待3秒，避免被網站封鎖
3. **瀏覽器需求**: 需要Chrome瀏覽器和對應的ChromeDriver
4. **資料完整性**: 系統會自動驗證下載的資料完整性
5. **錯誤處理**: 如果某個分段失敗，會繼續下載其他分段

## 故障排除

### 常見問題

1. **分段下載失敗**
   - 檢查網路連線
   - 確認ChromeDriver版本
   - 查看日誌中的詳細錯誤訊息

2. **CSV清理失敗**  
   - 檢查CSV檔案是否存在
   - 確認檔案編碼為UTF-8-BOM
   - 查看是否有檔案權限問題

3. **數據庫匯入失敗**
   - 檢查數據庫檔案權限
   - 確認資料格式正確
   - 查看是否有重複資料衝突

### 日誌查看
系統會產生詳細的日誌訊息，包括:
- 每個分段的下載狀態
- CSV清理過程
- 數據庫匯入統計
- 錯誤詳情

## 效能提升

相比原版本的改進:
- ✅ 資料完整性: 從300筆提升到1898筆(100%完整)
- ✅ 資料品質: 自動清理重複標題行
- ✅ 處理效率: 自動化分段下載和匯入
- ✅ 錯誤處理: 更強健的錯誤恢復機制
- ✅ 使用體驗: GUI和命令行雙重支援

## 更新日誌

**v2.0 (2024-01-01)**
- 新增分段下載功能，支援完整1898筆資料
- 新增CSV自動清理功能
- 改進數據庫匯入流程
- 新增完整測試套件
- 更新GUI界面支援新功能
