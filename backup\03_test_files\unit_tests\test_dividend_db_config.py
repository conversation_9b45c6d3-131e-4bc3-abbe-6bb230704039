#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息資料庫配置功能
"""

import sys
import os
import json
import sqlite3
from datetime import datetime

def test_dividend_database():
    """測試除權息資料庫"""
    print("📊 測試除權息資料庫...")
    
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 資料庫表格: {tables}")
        
        if 'dividend_data' in tables:
            # 獲取統計資訊
            cursor.execute("SELECT COUNT(*) FROM dividend_data")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM dividend_data")
            stock_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE ex_dividend_date IS NOT NULL")
            with_dates = cursor.fetchone()[0]
            
            print(f"✅ 除權息資料統計:")
            print(f"   • 總記錄數: {total_count:,}")
            print(f"   • 股票數量: {stock_count:,}")
            print(f"   • 有日期記錄: {with_dates:,} ({with_dates/total_count*100:.1f}%)")
            
            # 顯示範例資料
            cursor.execute("SELECT stock_code, stock_name, year, ex_dividend_date, cash_dividend, total_dividend FROM dividend_data WHERE ex_dividend_date IS NOT NULL LIMIT 5")
            samples = cursor.fetchall()
            
            print(f"📋 資料範例:")
            for sample in samples:
                print(f"   • {sample[0]} {sample[1]}: {sample[3]} 股利{sample[5]:.2f}元")
            
            conn.close()
            return True
        else:
            print("❌ dividend_data 表格不存在")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 資料庫測試失敗: {e}")
        return False

def test_config_manager():
    """測試配置管理器"""
    print("\n⚙️ 測試配置管理器...")
    
    try:
        from config.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.config  # 使用 config 屬性而不是 get_config() 方法

        print("✅ 配置管理器載入成功")

        # 檢查除權息資料庫配置
        db_config = config.get('database', {})
        dividend_path = db_config.get('dividend_db_path', '')
        dividend_enabled = db_config.get('dividend_enabled', False)

        print(f"📊 除權息資料庫配置:")
        print(f"   • 路徑: {dividend_path}")
        print(f"   • 啟用: {dividend_enabled}")

        return True
        
    except Exception as e:
        print(f"❌ 配置管理器測試失敗: {e}")
        return False

def test_dividend_crawler():
    """測試除權息爬蟲"""
    print("\n🕷️ 測試除權息爬蟲...")
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        crawler = EnhancedDividendCrawler()
        
        print("✅ 除權息爬蟲初始化成功")
        
        # 測試資料獲取
        test_data = crawler.get_dividend_data_by_year(2025)
        print(f"✅ 獲取2025年資料: {len(test_data)} 筆")
        
        if test_data:
            print("📋 資料範例:")
            for i, record in enumerate(test_data[:3]):
                print(f"   • {record['stock_code']} {record['stock_name']}: {record.get('ex_dividend_date', 'N/A')} 股利{record['total_dividend']:.2f}元")
        
        return True
        
    except Exception as e:
        print(f"❌ 除權息爬蟲測試失敗: {e}")
        return False

def update_app_config():
    """更新應用程式配置"""
    print("\n📝 更新應用程式配置...")
    
    try:
        config_file = "app_config.json"
        
        # 讀取現有配置
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # 更新資料庫配置
        if 'database' not in config:
            config['database'] = {}
        
        config['database'].update({
            'dividend_db_path': 'D:/Finlab/history/tables/dividend_data.db',
            'dividend_enabled': True
        })
        
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已更新: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔧 除權息資料庫配置測試")
    print("=" * 50)
    
    # 執行測試
    tests = [
        ("除權息資料庫", test_dividend_database),
        ("配置管理器", test_config_manager),
        ("除權息爬蟲", test_dividend_crawler),
        ("更新配置", update_app_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 顯示結果
    print("\n📊 測試結果:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總結: {passed}/{len(results)} 項測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！除權息資料庫整合成功！")
        print("\n💡 現在可以：")
        print("   1. 在主程式中點擊「資料庫」→「資料庫設定」")
        print("   2. 查看除權息資料庫標籤")
        print("   3. 使用除權息交易系統")
    else:
        print("⚠️ 部分測試失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
