#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新5天資料
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_5_days_update():
    """測試更新5天資料"""
    
    print("=" * 60)
    print("📈 測試更新5天資料")
    print("=" * 60)
    
    try:
        from crawler import crawl_price
        
        # 獲取最近5個交易日
        # 從 2022-09-08 開始更新5天
        start_date = datetime(2022, 9, 8)
        dates_to_update = []
        
        current_date = start_date
        for i in range(10):  # 檢查10天，找出5個工作日
            if current_date.weekday() < 5:  # 週一到週五
                dates_to_update.append(current_date)
                if len(dates_to_update) >= 5:
                    break
            current_date += timedelta(days=1)
        
        print(f"📅 準備更新的日期:")
        for i, date in enumerate(dates_to_update, 1):
            print(f"   {i}. {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
        
        # 逐日更新
        for i, date in enumerate(dates_to_update, 1):
            print(f"\n🔄 更新第 {i} 天: {date.strftime('%Y-%m-%d')}")
            
            try:
                df = crawl_price(date)
                
                if df is not None and not df.empty:
                    print(f"   ✅ 成功爬取 {len(df)} 筆資料")

                    # 檢查 0050 的資料
                    df_reset = df.reset_index()
                    etf_0050 = df_reset[df_reset['stock_id'] == '0050']

                    if not etf_0050.empty:
                        row = etf_0050.iloc[0]
                        # 檢查可用的列名
                        available_cols = list(df_reset.columns)
                        print(f"   📋 可用欄位: {available_cols}")

                        # 嘗試找到收盤價欄位
                        close_col = None
                        for col in ['Close', 'close', '收盤價', 'close_price']:
                            if col in available_cols:
                                close_col = col
                                break

                        volume_col = None
                        for col in ['Volume', 'volume', '成交量', 'trade_volume']:
                            if col in available_cols:
                                volume_col = col
                                break

                        close_val = row[close_col] if close_col else 'N/A'
                        volume_val = row[volume_col] if volume_col else 'N/A'
                        listing_status = row.get('listing_status', 'N/A')
                        industry = row.get('industry', 'N/A')

                        print(f"   📊 0050 資料: 收盤價={close_val}, 成交量={volume_val}, 上市狀態={listing_status}, 產業={industry}")
                    else:
                        print(f"   ⚠️ 未找到 0050 資料")
                        
                else:
                    print(f"   ❌ 爬取失敗或無資料")
                    
            except Exception as e:
                print(f"   ❌ 爬取失敗: {e}")
        
        # 檢查資料庫中的最新資料
        print(f"\n📊 檢查資料庫中的最新資料:")
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        
        # 查詢最新的5天 0050 資料
        query = '''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close], Volume, [Transaction], TradeValue
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            print(f"   最新 10 筆 0050 資料:")
            for _, row in df.iterrows():
                status = row['listing_status'] if pd.notna(row['listing_status']) else 'NULL'
                industry = row['industry'] if pd.notna(row['industry']) else 'NULL'
                print(f"   {row['date']}: 收盤={row['Close']}, 成交量={row['Volume']}, 狀態={status}, 產業={industry}")
        else:
            print(f"   ❌ 未找到 0050 資料")
        
        # 檢查其他 ETF 的最新資料
        print(f"\n📋 檢查其他 ETF 的最新資料:")
        query2 = '''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close]
            FROM stock_daily_data 
            WHERE stock_id LIKE '00%' AND date = (SELECT MAX(date) FROM stock_daily_data)
            ORDER BY stock_id
            LIMIT 10
        '''
        
        df2 = pd.read_sql_query(query2, conn)
        
        if not df2.empty:
            latest_date = df2.iloc[0]['date']
            print(f"   最新日期 {latest_date} 的 ETF 資料:")
            for _, row in df2.iterrows():
                status = row['listing_status'] if pd.notna(row['listing_status']) else 'NULL'
                industry = row['industry'] if pd.notna(row['industry']) else 'NULL'
                print(f"   {row['stock_id']}: {row['stock_name']} | {status} | {industry} | 收盤={row['Close']}")
        else:
            print(f"   ❌ 未找到 ETF 資料")
        
        conn.close()
        
        print(f"\n✅ 5天資料更新測試完成！")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_5_days_update()
