#!/usr/bin/env python3
"""
測試圖表解讀功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ashui_backtest_gui import AshiuBacktestGUI
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保 ashui_backtest_gui.py 文件存在且可正常導入")
    sys.exit(1)

class TestWindow(QMainWindow):
    """測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle('圖表解讀功能測試')
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 測試按鈕
        test_btn1 = QPushButton('📖 測試圖表解讀指南')
        test_btn1.clicked.connect(self.test_chart_guide)
        layout.addWidget(test_btn1)
        
        test_btn2 = QPushButton('🔍 測試信號分析（無數據）')
        test_btn2.clicked.connect(self.test_signal_analysis)
        layout.addWidget(test_btn2)
        
        test_btn3 = QPushButton('🚀 啟動完整回測系統')
        test_btn3.clicked.connect(self.launch_full_system)
        layout.addWidget(test_btn3)
        
        # 創建回測GUI實例用於測試
        self.backtest_gui = AshiuBacktestGUI()
        
    def test_chart_guide(self):
        """測試圖表解讀指南"""
        try:
            self.backtest_gui.show_chart_interpretation_guide()
            print("✅ 圖表解讀指南測試成功")
        except Exception as e:
            print(f"❌ 圖表解讀指南測試失敗: {e}")
    
    def test_signal_analysis(self):
        """測試信號分析（無數據狀態）"""
        try:
            self.backtest_gui.show_signal_analysis()
            print("✅ 信號分析測試成功（應該顯示警告）")
        except Exception as e:
            print(f"❌ 信號分析測試失敗: {e}")
    
    def launch_full_system(self):
        """啟動完整的回測系統"""
        try:
            self.backtest_gui.show()
            print("✅ 完整回測系統啟動成功")
        except Exception as e:
            print(f"❌ 完整回測系統啟動失敗: {e}")

def main():
    """主函數"""
    print("🧪 開始測試圖表解讀功能...")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 創建測試窗口
    test_window = TestWindow()
    test_window.show()
    
    print("📋 測試說明:")
    print("1. 點擊 '📖 測試圖表解讀指南' 查看解讀指南")
    print("2. 點擊 '🔍 測試信號分析' 測試無數據狀態")
    print("3. 點擊 '🚀 啟動完整回測系統' 查看完整功能")
    print("\n💡 圖表解讀功能說明:")
    print("- 📚 基本概念：解釋回測和信號的基本概念")
    print("- 🎯 信號解讀：詳細解釋買賣信號的含義")
    print("- 💡 實戰應用：提供實際操作建議")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
