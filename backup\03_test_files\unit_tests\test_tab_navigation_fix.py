#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試活頁跳轉修復和阿水一式數據顯示問題
"""
import sys
import os

def test_left_tab_navigation():
    """測試左側活頁跳轉修復"""
    print("🚀 測試左側活頁跳轉修復...")
    
    try:
        # 檢查修復後的代碼
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否使用了正確的左側活頁引用
        fixes_found = []
        
        # 檢查實例變量設置
        if 'self.stock_tabs = QTabWidget()' in content:
            fixes_found.append("✅ 左側活頁保存為實例變量")
        else:
            print("❌ 左側活頁未保存為實例變量")
            return False
        
        # 檢查跳轉邏輯修復
        if 'self.stock_tabs.setCurrentIndex(1)' in content:
            fixes_found.append("✅ 使用正確的左側活頁跳轉")
        else:
            print("❌ 左側活頁跳轉邏輯未修復")
            return False
        
        # 檢查是否移除了錯誤的 findChildren 方法
        if 'findChildren(QTabWidget)[0]' not in content:
            fixes_found.append("✅ 移除了錯誤的 findChildren 方法")
        else:
            print("⚠️ 仍然使用 findChildren 方法（可能有問題）")
        
        for fix in fixes_found:
            print(f"  {fix}")
        
        print("✅ 左側活頁跳轉修復檢查通過")
        return True
        
    except Exception as e:
        print(f"❌ 左側活頁跳轉修復檢查失敗: {e}")
        return False

def test_ashui_strategy_data_issue():
    """測試阿水一式策略數據顯示問題"""
    print("\n🚀 測試阿水一式策略數據顯示問題...")
    
    try:
        # 檢查阿水一式表格設置方法
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找阿水一式表格設置方法
        if 'def setup_ashui_strategy_table(self, results, matching_stocks):' in content:
            print("✅ 找到阿水一式表格設置方法")
        else:
            print("❌ 未找到阿水一式表格設置方法")
            return False
        
        # 檢查表格欄位設置
        expected_columns = [
            "股票代碼", "股票名稱", "收盤價", "成交金額(萬)", "布林帶寬",
            "壓縮天數", "成交量倍數", "突破幅度", "飆股評分", "策略狀態"
        ]
        
        columns_found = True
        for col in expected_columns:
            if col not in content:
                print(f"❌ 缺少欄位: {col}")
                columns_found = False
        
        if columns_found:
            print("✅ 表格欄位設置正確")
        
        # 檢查數據填充邏輯
        data_issues = []
        
        # 檢查是否依賴條件結果解析
        if '條件結果' in content and 'message.split' in content:
            data_issues.append("⚠️ 依賴條件結果解析數據（可能導致空值）")
        
        # 檢查是否有備用數據計算
        if 'close_price * volume' in content:
            print("✅ 有成交金額計算邏輯")
        else:
            data_issues.append("❌ 缺少成交金額計算邏輯")
        
        if data_issues:
            print("🔍 發現的數據問題:")
            for issue in data_issues:
                print(f"  {issue}")
            return False
        else:
            print("✅ 數據填充邏輯檢查通過")
            return True
        
    except Exception as e:
        print(f"❌ 阿水一式策略數據檢查失敗: {e}")
        return False

def analyze_ashui_data_problem():
    """分析阿水一式數據問題的根本原因"""
    print("\n🔍 分析阿水一式數據問題...")
    
    print("📋 問題分析:")
    print("1. 表格中間5欄顯示為 '--'")
    print("   - 成交金額(萬)")
    print("   - 布林帶寬") 
    print("   - 壓縮天數")
    print("   - 成交量倍數")
    print("   - 突破幅度")
    
    print("\n🔍 可能原因:")
    print("1. 條件結果中沒有包含詳細數值信息")
    print("2. 數據解析邏輯依賴特定的消息格式")
    print("3. 原始股票數據中缺少必要字段")
    print("4. 計算邏輯有錯誤或異常")
    
    print("\n💡 解決方案:")
    print("1. 直接從股票數據計算技術指標")
    print("2. 不依賴條件結果的消息解析")
    print("3. 添加錯誤處理和默認值")
    print("4. 使用實際的技術分析計算")

def generate_fix_recommendations():
    """生成修復建議"""
    print("\n🛠️ 修復建議:")
    
    print("\n1. 左側活頁跳轉修復:")
    print("   ✅ 已修復：使用 self.stock_tabs.setCurrentIndex(1)")
    print("   ✅ 已修復：保存左側活頁為實例變量")
    print("   ✅ 已修復：移除錯誤的 findChildren 方法")
    
    print("\n2. 阿水一式數據顯示修復:")
    print("   🔧 需要修復：直接計算技術指標")
    print("   🔧 需要修復：不依賴條件結果解析")
    print("   🔧 需要修復：添加實際的布林帶計算")
    print("   🔧 需要修復：添加成交量比例計算")
    
    print("\n3. 優先級:")
    print("   🔥 高優先級：修復阿水一式數據顯示")
    print("   ✅ 已完成：修復左側活頁跳轉")

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 活頁跳轉修復和阿水一式數據問題測試")
    print("=" * 60)
    
    success1 = test_left_tab_navigation()
    success2 = test_ashui_strategy_data_issue()
    
    analyze_ashui_data_problem()
    generate_fix_recommendations()
    
    print("\n" + "=" * 60)
    if success1:
        print("🎉 左側活頁跳轉修復成功！")
    else:
        print("❌ 左側活頁跳轉仍有問題")
    
    if success2:
        print("🎉 阿水一式數據顯示正常！")
    else:
        print("❌ 阿水一式數據顯示需要修復")
        print("📝 下一步：修復阿水一式策略的數據計算邏輯")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
