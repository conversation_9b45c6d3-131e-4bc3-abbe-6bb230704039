#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 financial_data.db 的結構和內容
"""

import os
import sqlite3
import pandas as pd

def check_financial_data_db():
    """檢查 financial_data.db 的結構和內容"""
    
    print("=" * 80)
    print("🔍 檢查 financial_data.db 結構")
    print("=" * 80)
    
    db_path = 'D:/Finlab/history/tables/financial_data.db'
    
    if not os.path.exists(db_path):
        print(f"❌ financial_data.db 不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 資料庫中的表格:")
        for table in tables:
            print(f"   - {table[0]}")
        
        if not tables:
            print(f"⚠️ 資料庫中沒有任何表格")
            conn.close()
            return False
        
        # 檢查 financial_data 表格
        table_name = 'financial_data'
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print(f"\n📊 表格: {table_name}")
            
            # 檢查表格結構
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"   欄位結構:")
            for i, col in enumerate(columns, 1):
                print(f"     {i:2d}. {col[1]} ({col[2]})")
            
            # 檢查資料筆數
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   資料筆數: {count:,}")
            
            # 檢查日期範圍
            try:
                cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table_name} WHERE date IS NOT NULL")
                date_range = cursor.fetchone()
                if date_range and date_range[0]:
                    print(f"   日期範圍: {date_range[0]} 至 {date_range[1]}")
                else:
                    print(f"   日期範圍: 無有效日期")
            except Exception as e:
                print(f"   日期範圍檢查失敗: {e}")
            
            # 檢查 report_date 範圍
            try:
                cursor.execute(f"SELECT MIN(report_date), MAX(report_date) FROM {table_name} WHERE report_date IS NOT NULL")
                report_date_range = cursor.fetchone()
                if report_date_range and report_date_range[0]:
                    print(f"   報告日期範圍: {report_date_range[0]} 至 {report_date_range[1]}")
                else:
                    print(f"   報告日期範圍: 無有效報告日期")
            except Exception as e:
                print(f"   報告日期範圍檢查失敗: {e}")
            
            # 顯示前幾筆資料
            try:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = cursor.fetchall()
                if sample_data:
                    print(f"   前3筆資料:")
                    for i, row in enumerate(sample_data, 1):
                        print(f"     {i}. {row}")
            except Exception as e:
                print(f"   資料預覽失敗: {e}")
        else:
            print(f"⚠️ 找不到 {table_name} 表格")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查 financial_data.db 失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🔍 financial_data.db 結構檢查")
    
    # 檢查資料庫結構
    db_success = check_financial_data_db()
    
    if db_success:
        print(f"\n✅ financial_data.db 檢查完成")
    else:
        print(f"\n❌ financial_data.db 檢查失敗")

if __name__ == "__main__":
    main()
