#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試API回應內容
"""

import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_twse_response():
    """調試證交所API回應"""
    print("🔍 調試證交所API回應...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試我們爬蟲中使用的確切參數
    url = "https://www.twse.com.tw/exchangeReport/TWT49U"
    params = {
        'response': 'json',
        'date': '20240701',  # 2024年7月
        'selectType': 'ALL'
    }
    
    try:
        print(f"📡 請求URL: {url}")
        print(f"📋 參數: {params}")
        
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        print(f"📊 狀態碼: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"📏 回應長度: {len(response.text)}")
        
        if response.status_code == 200:
            print(f"📝 回應內容前1000字元:")
            print(response.text[:1000])
            print("=" * 50)
            
            try:
                data = response.json()
                print(f"✅ JSON解析成功")
                print(f"🔑 主要鍵值: {list(data.keys())}")
                
                if 'data' in data:
                    if data['data']:
                        print(f"📊 data欄位有 {len(data['data'])} 筆資料")
                        print(f"📋 第一筆資料: {data['data'][0]}")
                    else:
                        print(f"⚠️ data欄位為空")
                else:
                    print(f"⚠️ 無data欄位")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失敗: {e}")
                print(f"📝 原始回應: {response.text}")
        else:
            print(f"❌ HTTP錯誤: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 請求失敗: {e}")

def debug_tpex_response():
    """調試櫃買中心API回應"""
    print("\n\n🔍 調試櫃買中心API回應...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試我們爬蟲中使用的確切參數
    url = "https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php"
    params = {
        'l': 'zh-tw',
        'd': '113/07/01',  # 民國113年 = 西元2024年
        'stkno': '',
        'o': 'json'
    }
    
    try:
        print(f"📡 請求URL: {url}")
        print(f"📋 參數: {params}")
        
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        print(f"📊 狀態碼: {response.status_code}")
        print(f"📄 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"📏 回應長度: {len(response.text)}")
        
        if response.status_code == 200:
            print(f"📝 回應內容前1000字元:")
            print(response.text[:1000])
            print("=" * 50)
            
            try:
                data = response.json()
                print(f"✅ JSON解析成功")
                print(f"🔑 主要鍵值: {list(data.keys())}")
                
                if 'aaData' in data:
                    if data['aaData']:
                        print(f"📊 aaData欄位有 {len(data['aaData'])} 筆資料")
                        print(f"📋 第一筆資料: {data['aaData'][0]}")
                    else:
                        print(f"⚠️ aaData欄位為空")
                else:
                    print(f"⚠️ 無aaData欄位")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失敗: {e}")
                print(f"📝 原始回應: {response.text}")
        else:
            print(f"❌ HTTP錯誤: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 請求失敗: {e}")

if __name__ == "__main__":
    debug_twse_response()
    debug_tpex_response()
