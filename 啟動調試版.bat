@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 調試版

echo.
echo ========================================
echo    🔍 台股智能選股系統 - 調試版 🔍
echo ========================================
echo.

if exist "dist\StockAnalyzer_Debug.exe" (
    echo ✅ 找到調試版
    echo 🔍 正在啟動調試模式...
    echo.
    echo 💡 調試版特點：
    echo    ✓ 顯示詳細的啟動信息
    echo    ✓ 顯示所有錯誤和警告
    echo    ✓ 幫助診斷問題
    echo    ✓ 控制台視窗會保持開啟
    echo.
    
    cd /d "dist"
    echo 🚀 啟動程式...
    "StockAnalyzer_Debug.exe"
    
    echo.
    echo 📋 如果程式沒有啟動，請檢查上方的錯誤信息
    echo.
    
) else (
    echo ❌ 錯誤：找不到調試版
    echo.
    echo 請重新編譯：
    echo    python debug_compile.py
    echo.
)

echo 按任意鍵退出...
pause >nul
