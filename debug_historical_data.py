#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試歷史指數數據
"""

import sqlite3
import pandas as pd

def debug_historical_data():
    """調試歷史指數數據"""
    print("=" * 60)
    print("🔍 調試歷史指數數據")
    print("=" * 60)
    
    try:
        db_path = "D:/Finlab/history/tables/market_historical.db"
        conn = sqlite3.connect(db_path)
        
        # 檢查表結構
        cursor = conn.execute("PRAGMA table_info(market_historical_index)")
        columns = cursor.fetchall()
        
        print("📋 表格欄位:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查數據樣本
        df = pd.read_sql_query("SELECT * FROM market_historical_index LIMIT 20", conn)
        print(f"\n📊 數據樣本 (前20筆):")
        print(df.head(10))
        
        print(f"\n📊 數據形狀: {df.shape}")
        print(f"📊 欄位: {list(df.columns)}")
        
        # 檢查日期欄位
        if 'Date' in df.columns:
            print(f"\n📅 Date欄位樣本:")
            print(df['Date'].head(10))
            
            # 轉換日期
            df['Date'] = pd.to_datetime(df['Date'], format='%Y%m%d', errors='coerce')
            print(f"\n📅 轉換後的日期:")
            print(df['Date'].head(10))
        
        # 檢查收盤指數欄位
        price_cols = ['ClosingPrice', 'ClosingIndex', '收盤指數']
        found_col = None
        for col in price_cols:
            if col in df.columns:
                found_col = col
                break
        
        if found_col:
            print(f"\n📈 找到價格欄位: {found_col}")
            print(f"📈 價格數據樣本:")
            print(df[found_col].head(10))
            
            # 轉換為數值
            df[found_col] = pd.to_numeric(df[found_col], errors='coerce')
            print(f"\n📈 轉換後的價格數據:")
            print(df[found_col].head(10))
            
            print(f"\n📈 價格統計:")
            print(f"   最小值: {df[found_col].min()}")
            print(f"   最大值: {df[found_col].max()}")
            print(f"   平均值: {df[found_col].mean():.2f}")
            print(f"   唯一值數量: {df[found_col].nunique()}")
            
            if df[found_col].nunique() <= 5:
                print(f"   所有唯一值: {df[found_col].unique()}")
        else:
            print("❌ 找不到價格欄位")
            print(f"可用欄位: {list(df.columns)}")
        
        # 檢查是否有重複數據
        print(f"\n🔍 數據重複檢查:")
        print(f"   總行數: {len(df)}")
        print(f"   唯一行數: {df.drop_duplicates().shape[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_historical_data()
