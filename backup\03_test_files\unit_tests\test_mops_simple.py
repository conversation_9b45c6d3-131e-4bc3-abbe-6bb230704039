"""
簡單測試MOPS爬蟲功能
"""

def test_crawler():
    """測試爬蟲功能"""
    try:
        print('🔍 測試上市櫃基本資料爬蟲...')
        
        # 避免導入tkinter相關模組
        import sys
        import os
        sys.path.append('.')
        
        # 只導入爬蟲核心功能
        from stock_basic_info_crawler import crawler_stock_basic_info, StockBasicInfoDatabase
        
        print('✅ 模組導入成功')
        
        # 測試資料庫
        db = StockBasicInfoDatabase('test_simple.db')
        print('✅ 資料庫初始化成功')
        
        # 測試爬取上市公司資料
        print('🚀 開始測試爬取上市公司資料...')
        df = crawler_stock_basic_info('sii')
        
        if not df.empty:
            print(f'✅ 成功爬取 {len(df)} 筆上市公司資料')
            print(f'📊 欄位: {list(df.columns)}')
            
            # 顯示前幾筆資料
            print('📋 範例資料:')
            for i, row in df.head(3).iterrows():
                print(f'  {i+1}. {row.get("company_code", "N/A")} - {row.get("company_name", "N/A")}')
            
            # 儲存到資料庫
            saved_count = db.save_data(df)
            print(f'✅ 成功儲存 {saved_count} 筆資料到資料庫')
            
        else:
            print('❌ 未能爬取到資料')
            return False
        
        return True
        
    except Exception as e:
        print(f'❌ 測試失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_crawler()
    if success:
        print('\n🎉 測試成功！')
    else:
        print('\n❌ 測試失敗！')
