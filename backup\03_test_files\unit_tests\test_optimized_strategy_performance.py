#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化版本策略的性能
"""

import sys
import time
import pandas as pd
from datetime import datetime

def test_optimized_strategy_performance():
    """測試優化版本策略的性能"""
    
    print("🚀 測試優化版本策略性能")
    print("=" * 50)
    
    # 1. 載入原始版本和優化版本
    print("1️⃣ 載入策略...")
    sys.path.append('strategies')
    
    try:
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        from strategies.high_yield_turtle_strategy_optimized import HighYieldTurtleStrategyOptimized
        
        print("   📊 載入原始版本...")
        start_time = time.time()
        original_strategy = HighYieldTurtleStrategy()
        original_load_time = time.time() - start_time
        
        print("   🚀 載入優化版本...")
        start_time = time.time()
        optimized_strategy = HighYieldTurtleStrategyOptimized()
        optimized_load_time = time.time() - start_time
        
        print(f"   ✅ 策略載入完成")
        print(f"      原始版本載入時間: {original_load_time:.3f}秒")
        print(f"      優化版本載入時間: {optimized_load_time:.3f}秒")
        
    except Exception as e:
        print(f"   ❌ 策略載入失敗: {e}")
        return
    
    # 2. 創建測試數據
    print("\n2️⃣ 創建測試數據...")
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    print(f"   ✅ 測試數據準備完成")
    
    # 3. 性能對比測試
    print("\n3️⃣ 性能對比測試...")
    test_stocks = ['2881', '2882', '1108', '2330', '1101']
    
    # 測試原始版本
    print("   📊 測試原始版本...")
    start_time = time.time()
    original_results = []
    for stock_id in test_stocks:
        result = original_strategy.analyze_stock(test_df, stock_id=stock_id)
        original_results.append((stock_id, result))
    original_total_time = time.time() - start_time
    original_avg_time = original_total_time / len(test_stocks)
    
    print(f"      總時間: {original_total_time:.3f}秒")
    print(f"      平均時間: {original_avg_time:.3f}秒/股票")
    
    # 測試優化版本
    print("   🚀 測試優化版本...")
    start_time = time.time()
    optimized_results = []
    for stock_id in test_stocks:
        result = optimized_strategy.analyze_stock(test_df, stock_id=stock_id)
        optimized_results.append((stock_id, result))
    optimized_total_time = time.time() - start_time
    optimized_avg_time = optimized_total_time / len(test_stocks)
    
    print(f"      總時間: {optimized_total_time:.3f}秒")
    print(f"      平均時間: {optimized_avg_time:.3f}秒/股票")
    
    # 4. 性能提升分析
    print("\n4️⃣ 性能提升分析...")
    
    if optimized_avg_time > 0:
        speedup = original_avg_time / optimized_avg_time
        improvement = ((original_avg_time - optimized_avg_time) / original_avg_time) * 100
        
        print(f"   📈 性能提升:")
        print(f"      加速倍數: {speedup:.1f}倍")
        print(f"      性能改善: {improvement:.1f}%")
        print(f"      時間節省: {original_total_time - optimized_total_time:.3f}秒")
        
        if speedup >= 10:
            print(f"      🎉 性能提升顯著！")
        elif speedup >= 3:
            print(f"      ✅ 性能提升良好")
        elif speedup >= 1.5:
            print(f"      👍 性能有所改善")
        else:
            print(f"      ⚠️ 性能提升有限")
    
    # 5. 結果一致性檢查
    print("\n5️⃣ 結果一致性檢查...")
    
    consistent_count = 0
    for i, stock_id in enumerate(test_stocks):
        original_result = original_results[i][1]
        optimized_result = optimized_results[i][1]
        
        original_suitable = original_result['suitable']
        optimized_suitable = optimized_result['suitable']
        original_score = original_result['score']
        optimized_score = optimized_result['score']
        
        if original_suitable == optimized_suitable and abs(original_score - optimized_score) <= 1:
            consistent_count += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"   {status} {stock_id}: 原始({original_suitable}, {original_score}) vs 優化({optimized_suitable}, {optimized_score})")
    
    consistency_rate = (consistent_count / len(test_stocks)) * 100
    print(f"   📊 結果一致性: {consistency_rate:.1f}% ({consistent_count}/{len(test_stocks)})")
    
    # 6. 記憶體使用分析
    print("\n6️⃣ 記憶體使用分析...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        print(f"   📊 當前記憶體使用: {memory_mb:.1f} MB")
        
        # 檢查快取大小
        if hasattr(optimized_strategy, 'latest_pe_data'):
            cache_size = len(optimized_strategy.latest_pe_data)
            print(f"   🗄️ PE數據快取: {cache_size} 支股票")
        
        if hasattr(optimized_strategy, 'stock_id_mapping'):
            mapping_size = len(optimized_strategy.stock_id_mapping)
            print(f"   🔗 股票代碼映射: {mapping_size} 個映射")
            
    except ImportError:
        print(f"   ⚠️ 無法獲取記憶體信息 (需要psutil)")
    except Exception as e:
        print(f"   ❌ 記憶體分析失敗: {e}")
    
    # 7. 大量股票測試
    print("\n7️⃣ 大量股票測試...")
    
    # 生成更多測試股票
    extended_test_stocks = ['2881', '2882', '1108', '2330', '1101', '2317', '2454', '3008', '2412', '2303']
    
    print(f"   📊 測試 {len(extended_test_stocks)} 支股票...")
    
    # 優化版本大量測試
    start_time = time.time()
    for stock_id in extended_test_stocks:
        optimized_strategy.analyze_stock(test_df, stock_id=stock_id)
    extended_time = time.time() - start_time
    extended_avg_time = extended_time / len(extended_test_stocks)
    
    print(f"      總時間: {extended_time:.3f}秒")
    print(f"      平均時間: {extended_avg_time:.3f}秒/股票")
    print(f"      處理速度: {len(extended_test_stocks)/extended_time:.1f}股票/秒")
    
    # 8. 總結
    print(f"\n" + "=" * 50)
    print(f"✅ 優化版本性能測試完成！")
    
    if optimized_avg_time > 0 and original_avg_time > 0:
        speedup = original_avg_time / optimized_avg_time
        
        print(f"\n🎯 優化效果總結:")
        print(f"✅ 性能提升: {speedup:.1f}倍")
        print(f"✅ 結果一致性: {consistency_rate:.1f}%")
        print(f"✅ 平均處理時間: {optimized_avg_time:.3f}秒/股票")
        
        if speedup >= 5:
            print(f"🎉 優化效果顯著！建議使用優化版本")
        elif speedup >= 2:
            print(f"👍 優化效果良好，值得使用")
        else:
            print(f"⚠️ 優化效果有限，需要進一步改進")
    
    return optimized_avg_time, original_avg_time

if __name__ == "__main__":
    optimized_time, original_time = test_optimized_strategy_performance()
    
    if optimized_time and original_time:
        speedup = original_time / optimized_time
        print(f"\n🚀 最終結論:")
        print(f"📊 原始版本: {original_time:.3f}秒/股票")
        print(f"🚀 優化版本: {optimized_time:.3f}秒/股票")
        print(f"⚡ 性能提升: {speedup:.1f}倍")
        
        if speedup >= 10:
            print(f"🎉 優化非常成功！計算時間大幅縮短")
        elif speedup >= 3:
            print(f"✅ 優化成功！計算時間明顯改善")
        else:
            print(f"⚠️ 優化效果有限，可能需要其他優化策略")
