#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速分析 Change 值的計算方式
"""

import sqlite3
import pandas as pd

def quick_change_analysis():
    """快速分析 Change 值的計算方式"""
    
    print("🔍 Change 值計算方式分析")
    print("=" * 50)
    
    price_db = r'D:\Finlab\history\tables\price.db'
    
    try:
        conn = sqlite3.connect(price_db)
        
        # 檢查 0050 最近幾天的資料
        query = '''
            SELECT date, [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        df = pd.read_sql_query(query, conn)
        
        print("0050 最近10天資料:")
        print("日期        收盤價   Change")
        print("-" * 30)
        
        for _, row in df.iterrows():
            print(f"{row['date']} {row['Close']:7.2f} {row['Change']:7.2f}")
        
        # 驗證計算方式
        print(f"\n驗證 Change = 今日收盤 - 昨日收盤:")
        df_sorted = df.sort_values('date')
        
        for i in range(1, len(df_sorted)):
            today = df_sorted.iloc[i]
            yesterday = df_sorted.iloc[i-1]
            
            calculated_change = today['Close'] - yesterday['Close']
            actual_change = today['Change']
            
            print(f"{today['date']}: {today['Close']:.2f} - {yesterday['Close']:.2f} = {calculated_change:.2f} (實際: {actual_change:.2f})")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

if __name__ == "__main__":
    quick_change_analysis()
