#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google股票新聞爬蟲GUI
透過Google RSS搜尋9個主要財經媒體的新聞
"""

import sys
import os
import logging
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QSpinBox, QPushButton, QTextEdit,
                            QProgressBar, QMessageBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QGroupBox, QCheckBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QDesktopServices
from PyQt6.QtCore import QUrl

# 導入Google股票新聞爬蟲
try:
    from google_real_time_news import GoogleStockNewsCrawler
except ImportError:
    GoogleStockNewsCrawler = None

class GoogleNewsWorker(QThread):
    """Google新聞爬取工作執行緒"""
    progress_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, stock_code, days, db_path=None, fast_mode=True):
        super().__init__()
        self.stock_code = stock_code
        self.days = days
        self.db_path = db_path
        self.fast_mode = fast_mode
        
    def run(self):
        try:
            self.progress_updated.emit(f"🔍 開始搜尋 {self.stock_code} 的Google新聞...")

            # 創建進度回調函數
            def progress_callback(message):
                self.progress_updated.emit(message)

            # 根據模式選擇爬蟲
            if self.fast_mode:
                from google_news_fast import GoogleNewsFastCrawler
                crawler = GoogleNewsFastCrawler()
                crawler.set_progress_callback(progress_callback)
                self.progress_updated.emit("⚡ 使用快速模式 - 並發處理，優先最新新聞")
            else:
                crawler = GoogleStockNewsCrawler(db_path=self.db_path, progress_callback=progress_callback)
                self.progress_updated.emit("🐌 使用標準模式 - 下載所有新聞完整內容")

            self.progress_updated.emit("🌐 正在搜尋9個財經媒體...")
            self.progress_updated.emit("📰 聯合新聞網、自由財經、Yahoo股市、經濟日報、中時新聞網")
            self.progress_updated.emit("📰 工商時報、鉅亨網、ETtoday、EBC東森財經新聞")

            # 搜尋新聞
            news_list = crawler.search_stock_news(self.stock_code, days=self.days)

            if news_list:
                self.progress_updated.emit(f"✅ 找到 {len(news_list)} 筆新聞")

                # 儲存到資料庫
                if self.fast_mode:
                    # 快速模式：確保資料庫路徑正確
                    if not self.db_path:
                        self.db_path = "./news.db"  # 使用預設路徑
                    crawler.save_to_database(news_list, self.db_path)
                    self.progress_updated.emit(f"💾 已儲存 {len(news_list)} 筆新聞到資料庫")
                else:
                    saved_count = crawler.save_to_database(news_list, self.stock_code)
                    self.progress_updated.emit(f"💾 儲存了 {saved_count} 筆新聞到資料庫")

                self.finished_signal.emit(news_list)
            else:
                self.progress_updated.emit("❌ 沒有找到相關新聞")
                self.finished_signal.emit([])

        except Exception as e:
            self.error_signal.emit(str(e))

class GoogleStockNewsDialog(QDialog):
    """Google股票新聞爬蟲對話框"""
    
    def __init__(self, parent=None, stock_code=""):
        super().__init__(parent)
        self.stock_code = stock_code
        self.crawler_thread = None
        self.init_stock_name_mapping()
        self.setup_ui()

    def init_stock_name_mapping(self):
        """初始化股票名稱對應表"""
        self.stock_name_mapping = {
            # 權值股
            '台積電': '2330', 'TSMC': '2330', '台積': '2330',
            '鴻海': '2317', '鴻海精密': '2317',
            '聯發科': '2454', 'MediaTek': '2454', '聯發': '2454',
            '台達電': '2308', '廣達': '2382', '聯電': '2303',
            '大立光': '3008', '國巨': '2327', '研華': '2395',
            '中華電': '2412', '中華電信': '2412',
            '台塑': '1301', '南亞': '1303', '台泥': '1101',
            '中鋼': '2002', '統一': '1216',

            # 金融股
            '富邦金': '2881', '國泰金': '2882', '兆豐金': '2886',
            '中信金': '2891', '第一金': '2892', '合庫金': '5880',
            '玉山金': '2884', '永豐金': '2890', '華南金': '2880',

            # 科技股
            '華碩': '2357', '友達': '2409', '南亞科': '2408',
            '和碩': '4938', '緯創': '3231', '仁寶': '2324',
            '英業達': '2356', '宏碁': '2353', '微星': '2377',

            # 傳產股
            '和泰車': '2207', '裕隆': '2201', '中華': '2204',
            '台塑化': '6505', '長榮': '2603', '陽明': '2609',
            '萬海': '2615', '台船': '2208',

            # ETF
            '台灣50': '0050', '元大台灣50': '0050',
            '中型100': '0051', '元大中型100': '0051',
            '富邦科技': '0052', '電子': '0053',
            '台商50': '0054', '高股息': '0056',
            '元大高股息': '0056', '永豐ESG': '00930',
            '富邦元宇宙': '00903',
        }

    def lookup_stock_code_by_name(self, input_text):
        """根據輸入文字查詢股票代碼"""
        input_text = input_text.strip()

        # 如果輸入的已經是數字代碼，直接返回
        if input_text.isdigit() or (input_text.startswith('00') and input_text[2:].isdigit()):
            return input_text

        # 直接匹配
        if input_text in self.stock_name_mapping:
            return self.stock_name_mapping[input_text]

        # 模糊匹配（包含關係）
        for name, code in self.stock_name_mapping.items():
            if input_text in name or name in input_text:
                return code

        # 如果都找不到，返回原輸入
        return input_text

    def setup_ui(self):
        """設置UI"""
        # 首先設定視窗標誌，確保有完整的標題欄控制按鈕
        self.setWindowFlags(Qt.WindowType.Window |
                           Qt.WindowType.WindowMinimizeButtonHint |
                           Qt.WindowType.WindowMaximizeButtonHint |
                           Qt.WindowType.WindowCloseButtonHint |
                           Qt.WindowType.WindowSystemMenuHint)

        self.setWindowTitle("🌐 Google股票新聞爬蟲")
        self.setMinimumSize(900, 650)

        # 確保視窗可以調整大小
        self.setSizeGripEnabled(True)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 標題 - 緊湊版本
        title_label = QLabel("🌐 Google股票新聞爬蟲")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1a73e8;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 6px;
            margin: 5px;
            border: 2px solid #1a73e8;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 說明文字 - 更緊湊版本
        info_label = QLabel("🔍 搜尋9個財經媒體 | 📰 聯合、自由、Yahoo、經濟日報、中時、工商、鉅亨、ETtoday、EBC | 💾 存入news.db")
        info_label.setStyleSheet("""
            margin: 3px;
            color: #1a1a1a;
            background-color: #e3f2fd;
            padding: 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #1976d2;
            border-left: 3px solid #1976d2;
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 設定區域 - 緊湊版本
        settings_group = QGroupBox("爬取設定")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 2px solid #1976d2;
                border-radius: 6px;
                margin-top: 3px;
                padding-top: 5px;
                background-color: #fafafa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #1976d2;
                background-color: #fafafa;
            }
        """)
        settings_layout = QHBoxLayout(settings_group)
        
        # 股票代碼/名稱輸入
        stock_label = QLabel("股票代碼/名稱:")
        stock_label.setStyleSheet("color: #1a1a1a; font-weight: bold; font-size: 13px;")
        settings_layout.addWidget(stock_label)

        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("例如: 2330, 台積電, 元大台灣50")
        self.stock_input.setText(self.stock_code if self.stock_code else "2330")
        self.stock_input.setFont(QFont("Microsoft JhengHei", 12))
        self.stock_input.setMaximumWidth(180)
        self.stock_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #90a4ae;
                border-radius: 5px;
                background-color: white;
                color: #1a1a1a;
                font-size: 12px;
                font-weight: 500;
            }
            QLineEdit:focus {
                border-color: #1976d2;
                background-color: #f3f8ff;
            }
            QLineEdit:hover {
                border-color: #42a5f5;
            }
        """)
        settings_layout.addWidget(self.stock_input)

        # 天數設定
        days_label = QLabel("搜尋天數:")
        days_label.setStyleSheet("color: #1a1a1a; font-weight: bold; font-size: 13px;")
        settings_layout.addWidget(days_label)

        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)  # 移除30天限制，最多可搜尋一年
        self.days_spinbox.setValue(365)  # 預設改為365天，一次性獲取完整資料
        self.days_spinbox.setMaximumWidth(90)  # 增加寬度以適應三位數
        self.days_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #90a4ae;
                border-radius: 5px;
                background-color: white;
                color: #1a1a1a;
                font-size: 12px;
                font-weight: 500;
            }
            QSpinBox:focus {
                border-color: #1976d2;
                background-color: #f3f8ff;
            }
            QSpinBox:hover {
                border-color: #42a5f5;
            }
        """)
        settings_layout.addWidget(self.days_spinbox)

        # 快速模式選項
        self.fast_mode_checkbox = QCheckBox("⚡ 快速模式")
        self.fast_mode_checkbox.setChecked(True)  # 預設開啟
        self.fast_mode_checkbox.setToolTip("開啟後只下載前15篇新聞的完整內容，其餘使用摘要，大幅提升速度")
        self.fast_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: #1a1a1a;
                font-weight: bold;
                font-size: 13px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #1976d2;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #1976d2;
                background-color: #1976d2;
                border-radius: 3px;
            }
        """)
        settings_layout.addWidget(self.fast_mode_checkbox)

        settings_layout.addStretch()
        layout.addWidget(settings_group)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 開始爬取按鈕
        self.start_button = QPushButton("🚀 開始爬取")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QPushButton:disabled {
                background-color: #bdbdbd;
                color: #757575;
            }
        """)
        self.start_button.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.start_button)
        
        # 查看資料庫按鈕
        self.view_db_button = QPushButton("📊 查看資料庫")
        self.view_db_button.setStyleSheet("""
            QPushButton {
                background-color: #388e3c;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #2e7d32;
            }
            QPushButton:pressed {
                background-color: #1b5e20;
            }
        """)
        self.view_db_button.clicked.connect(self.view_database)
        button_layout.addWidget(self.view_db_button)
        
        button_layout.addStretch()
        
        # 關閉按鈕
        close_button = QPushButton("❌ 關閉")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #c62828;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #1976d2;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 13px;
                color: #1a1a1a;
                background-color: #f5f5f5;
                min-height: 25px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1976d2, stop:1 #42a5f5);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 狀態顯示區域 - 增加高度
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(350)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #263238;
                color: #eceff1;
                border: 3px solid #1976d2;
                border-radius: 10px;
                padding: 15px;
                font-family: 'Consolas', 'Microsoft JhengHei', monospace;
                font-size: 13px;
                line-height: 1.5;
                selection-background-color: #1976d2;
                selection-color: white;
            }
            QTextEdit:focus {
                border-color: #1565c0;
                background-color: #37474f;
            }
        """)
        layout.addWidget(self.status_text)
        
        # 添加歡迎訊息
        self.add_status_message("🎉 Google股票新聞爬蟲已準備就緒")
        self.add_status_message("🌐 將搜尋9個主要財經媒體的新聞")
        self.add_status_message("💡 支援股票代碼或股票名稱輸入")
        self.add_status_message("📅 預設搜尋30天新聞，支援最多365天")
        
    def add_status_message(self, message):
        """添加狀態訊息"""
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        
        # 根據訊息類型設定顏色 - 高對比度版本
        if "✅" in message or "成功" in message:
            color_message = f'<span style="color: #66bb6a; font-weight: bold;">{timestamp} {message}</span>'
        elif "❌" in message or "失敗" in message or "錯誤" in message:
            color_message = f'<span style="color: #ef5350; font-weight: bold;">{timestamp} {message}</span>'
        elif "🔍" in message or "搜尋" in message:
            color_message = f'<span style="color: #42a5f5; font-weight: 500;">{timestamp} {message}</span>'
        elif "💾" in message or "儲存" in message:
            color_message = f'<span style="color: #ffa726; font-weight: 500;">{timestamp} {message}</span>'
        elif "🚀" in message or "開始" in message:
            color_message = f'<span style="color: #ab47bc; font-weight: bold;">{timestamp} {message}</span>'
        elif "🌐" in message:
            color_message = f'<span style="color: #29b6f6; font-weight: 500;">{timestamp} {message}</span>'
        elif "📰" in message:
            color_message = f'<span style="color: #26c6da; font-weight: 400;">{timestamp} {message}</span>'
        elif "💡" in message:
            color_message = f'<span style="color: #ffee58; font-weight: 500;">{timestamp} {message}</span>'
        elif "🎉" in message:
            color_message = f'<span style="color: #81c784; font-weight: bold;">{timestamp} {message}</span>'
        else:
            color_message = f'<span style="color: #eceff1; font-weight: 400;">{timestamp} {message}</span>'
        
        self.status_text.append(color_message)
        
        # 自動滾動到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
        # 處理事件以更新UI
        QApplication.processEvents()
    
    def start_crawling(self):
        """開始爬取新聞"""
        user_input = self.stock_input.text().strip()
        if not user_input:
            QMessageBox.warning(self, "輸入錯誤", "請輸入股票代碼或股票名稱")
            return

        # 查詢股票代碼
        stock_code = self.lookup_stock_code_by_name(user_input)

        # 如果輸入的是股票名稱，顯示轉換結果
        if user_input != stock_code:
            self.add_status_message(f"💡 股票名稱轉換: '{user_input}' → '{stock_code}'")

        days = self.days_spinbox.value()
        
        # 禁用按鈕並顯示進度條
        self.start_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不確定進度
        
        self.add_status_message(f"🚀 開始爬取 {stock_code} 最近 {days} 天的Google新聞...")
        self.add_status_message("🌐 目標媒體: 聯合新聞網、自由財經、Yahoo股市、經濟日報、中時新聞網")
        self.add_status_message("🌐 目標媒體: 工商時報、鉅亨網、ETtoday、EBC東森財經新聞")
        
        # 創建並啟動爬取執行緒
        db_path = self.get_news_database_path()
        fast_mode = self.fast_mode_checkbox.isChecked()

        if fast_mode:
            self.add_status_message("⚡ 快速模式已啟用 - 並發處理，優先最新新聞")
        else:
            self.add_status_message("🐌 標準模式 - 下載所有新聞完整內容")

        self.crawler_thread = GoogleNewsWorker(stock_code, days, db_path, fast_mode)
        self.crawler_thread.progress_updated.connect(self.add_status_message)
        self.crawler_thread.finished_signal.connect(self.on_crawling_finished)
        self.crawler_thread.error_signal.connect(self.on_crawling_error)
        self.crawler_thread.start()
    
    def on_crawling_finished(self, news_list):
        """爬取完成"""
        self.start_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if news_list and len(news_list) > 0:
            self.add_status_message(f"✅ 爬取完成！共找到 {len(news_list)} 筆新聞")
            self.add_status_message(f"📊 股票代碼: {self.stock_input.text()}")
            self.add_status_message(f"📅 搜尋天數: {self.days_spinbox.value()} 天")
            self.add_status_message(f"🗂️ 資料來源: Google RSS (9個財經媒體)")
            self.add_status_message(f"💾 新聞已儲存到資料庫中")
            
            # 顯示新聞預覽
            self.show_news_preview(news_list)
        else:
            self.add_status_message("❌ 沒有找到相關新聞")
            self.add_status_message("💡 建議:")
            self.add_status_message("  • 檢查股票代碼/名稱是否正確")
            self.add_status_message("  • 嘗試增加搜尋天數")
            self.add_status_message("  • 該股票可能沒有最近的新聞")
    
    def on_crawling_error(self, error_message):
        """爬取錯誤"""
        self.start_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.add_status_message(f"❌ 爬取失敗: {error_message}")
        QMessageBox.critical(self, "爬取失敗", f"Google股票新聞爬取失敗：\n{error_message}")
    
    def show_news_preview(self, news_list):
        """顯示新聞預覽"""
        self.add_status_message("\n📰 新聞預覽:")
        for i, news in enumerate(news_list[:5], 1):
            title = news.get('title', '無標題')[:50]
            source = news.get('source', '未知來源')
            self.add_status_message(f"  {i}. [{source}] {title}...")
        
        if len(news_list) > 5:
            self.add_status_message(f"  ... 還有 {len(news_list) - 5} 筆新聞")
    
    def view_database(self):
        """查看資料庫中的新聞"""
        user_input = self.stock_input.text().strip()
        if not user_input:
            self.add_status_message("📊 請先輸入股票代碼或股票名稱")
            return

        # 查詢股票代碼
        stock_code = self.lookup_stock_code_by_name(user_input)

        # 如果輸入的是股票名稱，顯示轉換結果
        if user_input != stock_code:
            self.add_status_message(f"💡 股票名稱轉換: '{user_input}' → '{stock_code}'")
        
        try:
            # 獲取資料庫路徑
            db_path = self.get_news_database_path()
            
            self.add_status_message(f"📊 正在查詢 {stock_code} 的Google新聞資料...")
            
            # 查詢資料庫
            import sqlite3
            if not os.path.exists(db_path):
                self.add_status_message(f"❌ 新聞資料庫不存在: {db_path}")
                return
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 首先檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            if 'news' in tables:
                # 使用新的news表格結構
                cursor.execute('''
                    SELECT id, search_time, search_keyword, title, source,
                           pub_date, description, link, content, created_at
                    FROM news
                    WHERE stock_code = ?
                    ORDER BY
                        CASE
                            WHEN pub_date IS NOT NULL AND pub_date != '' THEN pub_date
                            ELSE created_at
                        END DESC,
                        created_at DESC
                    LIMIT 50
                ''', (stock_code,))
            elif 'news_content' in tables:
                # 使用舊的news_content表格結構
                cursor.execute('''
                    SELECT news_id, date, time, source, title, reporter, link,
                           search_keyword, news_summary, crawl_time
                    FROM news_content
                    WHERE stock_code = ? AND news_id LIKE 'google_%'
                    ORDER BY
                        CASE
                            WHEN date IS NOT NULL AND date != '' THEN date
                            ELSE crawl_time
                        END DESC,
                        CASE
                            WHEN time IS NOT NULL AND time != '' THEN time
                            ELSE '23:59:59'
                        END DESC
                    LIMIT 50
                ''', (stock_code,))
            else:
                self.add_status_message(f"❌ 找不到新聞表格，可用表格: {tables}")
                conn.close()
                return
            
            results = cursor.fetchall()
            conn.close()
            
            if results:
                self.add_status_message(f"📊 找到 {len(results)} 筆 {stock_code} 的Google新聞")

                # 檢查使用的表格結構
                table_type = 'news' if 'news' in tables else 'news_content'

                # 顯示新聞對話框
                self.show_news_dialog(stock_code, results, table_type)
            else:
                self.add_status_message(f"📊 資料庫中沒有 {stock_code} 的Google新聞")
                self.add_status_message("💡 建議先執行爬取功能")
                
        except Exception as e:
            self.add_status_message(f"❌ 查看資料庫失敗: {e}")
    
    def show_news_dialog(self, stock_code, news_data, table_type='news'):
        """顯示新聞對話框"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"🌐 {stock_code} Google股票新聞")
        dialog.setMinimumSize(1000, 700)

        # 添加視窗控制按鈕 - 確保有最大化、最小化、關閉按鈕
        dialog.setWindowFlags(Qt.WindowType.Window |
                             Qt.WindowType.WindowMinimizeButtonHint |
                             Qt.WindowType.WindowMaximizeButtonHint |
                             Qt.WindowType.WindowCloseButtonHint |
                             Qt.WindowType.WindowSystemMenuHint)

        # 確保對話框可以調整大小
        dialog.setSizeGripEnabled(True)
        
        layout = QVBoxLayout(dialog)
        
        # 標題
        title_label = QLabel(f"🌐 {stock_code} Google股票新聞 ({len(news_data)} 筆)")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a1a1a;
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 12px;
            margin: 15px;
            border: 3px solid #1976d2;
            border-left: 8px solid #1976d2;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 新聞表格
        table = QTableWidget()
        table.setColumnCount(3)  # 恢復為3欄：日期、來源、標題
        table.setHorizontalHeaderLabels(['日期', '來源', '標題 (點擊開啟)'])
        table.setRowCount(len(news_data))

        # 設置表格樣式
        table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 標題欄位自動調整
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 日期欄位固定寬度
        table.setColumnWidth(0, 120)  # 設置日期欄位寬度
        table.setAlternatingRowColors(True)

        # 啟用垂直標題欄（行號欄）並設定樣式
        table.verticalHeader().setVisible(True)  # 顯示垂直標題欄
        table.verticalHeader().setFixedWidth(50)  # 設定行號欄寬度

        # 禁用編輯功能，防止雙擊時字形變形
        table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # 設定統一字體
        table_font = QFont("Microsoft JhengHei", 12)
        table.setFont(table_font)
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                gridline-color: #e0e0e0;
                color: #1a1a1a;
                border: 2px solid #1976d2;
                border-radius: 8px;
                font-family: "Microsoft JhengHei";
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #e0e0e0;
                border-right: 1px solid #e0e0e0;
                font-family: "Microsoft JhengHei";
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #1976d2;
                color: white;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #e3f2fd;
                color: #1a1a1a;
            }
            /* 確保選中項目的文字顏色對比度 */
            QTableWidget::item:selected {
                background-color: #1976d2 !important;
                color: #ffffff !important;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #1976d2;
                color: white;
                padding: 15px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
            QHeaderView::section:hover {
                background-color: #1565c0;
            }
            /* 垂直標題欄（行號欄）樣式 */
            QTableWidget QHeaderView::section:vertical {
                background-color: #f0f0f0;
                color: #333333;
                border: 1px solid #d0d0d0;
                padding: 8px;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
            }
        """)
        
        # 填入資料
        for row, news in enumerate(news_data):
            # 設定垂直標題欄的標籤（行號）
            table.setVerticalHeaderItem(row, QTableWidgetItem(str(row + 1)))

            # 根據表格類型解析資料
            if table_type == 'news':
                # 新的news表格結構
                # 查詢結果順序：id, search_time, search_keyword, title, source, pub_date, description, link, content, created_at
                pub_date = news[5] if len(news) > 5 else ''     # pub_date欄位
                source = news[4] if len(news) > 4 else ''       # source欄位
                title = news[3] if len(news) > 3 else ''        # title欄位
                link = news[7] if len(news) > 7 else ''         # link欄位
                description = news[6] if len(news) > 6 else ''  # description欄位
            else:
                # 舊的news_content表格結構
                # 查詢結果順序：news_id, date, time, source, title, reporter, link, search_keyword, news_summary, crawl_time
                pub_date = news[1] if len(news) > 1 else ''     # date欄位
                source = news[3] if len(news) > 3 else ''       # source欄位
                title = news[4] if len(news) > 4 else ''        # title欄位
                link = news[6] if len(news) > 6 else ''         # link欄位
                description = news[8] if len(news) > 8 else ''  # news_summary欄位

            # 格式化日期 - 確保最新日期在最上面
            if isinstance(pub_date, str) and pub_date:
                if len(pub_date) == 10 and '-' in pub_date:  # YYYY-MM-DD 格式
                    formatted_date = pub_date
                elif len(pub_date) == 8:  # YYYYMMDD 格式
                    formatted_date = f"{pub_date[:4]}-{pub_date[4:6]}-{pub_date[6:8]}"
                elif len(pub_date) >= 10:  # 包含時間的格式
                    formatted_date = pub_date[:10]  # 只取日期部分
                else:
                    formatted_date = pub_date
            else:
                # 如果沒有發布日期，使用今天的日期
                from datetime import datetime
                formatted_date = datetime.now().strftime('%Y-%m-%d')

            # 日期項目
            date_item = QTableWidgetItem(formatted_date)
            date_item.setFont(table_font)
            date_item.setFlags(date_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 設為不可編輯
            table.setItem(row, 0, date_item)

            # 來源項目
            source_item = QTableWidgetItem(source)
            source_item.setFont(table_font)
            source_item.setFlags(source_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 設為不可編輯
            table.setItem(row, 1, source_item)

            # 標題項目 (可點擊)
            title_item = QTableWidgetItem(title)
            title_item.setFont(table_font)
            title_item.setFlags(title_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 設為不可編輯
            title_item.setToolTip(f"標題: {title}\n摘要: {description or '無摘要'}\n點擊開啟連結")
            title_item.setData(Qt.ItemDataRole.UserRole, link)  # 儲存連結
            # 設置標題樣式，讓它看起來像連結
            from PyQt6.QtGui import QColor
            title_item.setForeground(QColor(25, 118, 210))  # 使用藍色
            table.setItem(row, 2, title_item)
        
        # 點擊標題開啟連結
        def open_link(row, col):
            if col == 2:  # 標題欄位 (第3欄，索引為2)
                link = table.item(row, col).data(Qt.ItemDataRole.UserRole)
                if link:
                    QDesktopServices.openUrl(QUrl(link))
        
        table.cellClicked.connect(open_link)  # 改為單擊
        
        layout.addWidget(table)
        
        # 關閉按鈕
        close_btn = QPushButton("❌ 關閉")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #c62828;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.exec()
    
    def get_news_database_path(self):
        """獲取新聞資料庫路徑"""
        # 嘗試從配置獲取路徑
        possible_paths = [
            "D:/Finlab/history/tables/news.db",
            "./news.db",
            "../news.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 使用預設路徑
        return "D:/Finlab/history/tables/news.db"

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 檢查依賴
    if GoogleStockNewsCrawler is None:
        QMessageBox.critical(None, "錯誤", "無法導入Google股票新聞爬蟲模組")
        sys.exit(1)
    
    dialog = GoogleStockNewsDialog()
    dialog.show()
    
    sys.exit(app.exec())
