#!/usr/bin/env python3
"""
策略说明对话框测试脚本
"""

import sys
try:
    from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout,
                                 QTextEdit, QPushButton, QTabWidget, QWidget, QMainWindow)
    from PyQt6.QtCore import Qt
except ImportError:
    try:
        from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout,
                                     QTextEdit, QPushButton, QTabWidget, QWidget, QMainWindow)
        from PyQt5.QtCore import Qt
    except ImportError:
        print("❌ 需要安装 PyQt6 或 PyQt5")
        sys.exit(1)

class StrategyInfoDialog(QDialog):
    """策略說明對話框"""
    
    def __init__(self, strategy_name, parent=None):
        super().__init__(parent)
        self.strategy_name = strategy_name
        self.setWindowTitle(f"📖 {strategy_name} - 策略說明")
        self.setModal(True)
        self.resize(800, 600)
        
        # 設置對話框樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.5;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #3c3c3c;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
        """)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 創建標籤頁
        tab_widget = QTabWidget()
        
        # 策略概述標籤頁
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        
        overview_text = QTextEdit()
        overview_text.setReadOnly(True)
        overview_text.setHtml(self.get_strategy_overview())
        overview_layout.addWidget(overview_text)
        
        tab_widget.addTab(overview_tab, "📋 策略概述")
        
        # 詳細說明標籤頁
        details_tab = QWidget()
        details_layout = QVBoxLayout(details_tab)
        
        details_text = QTextEdit()
        details_text.setReadOnly(True)
        details_text.setHtml(self.get_strategy_details())
        details_layout.addWidget(details_text)
        
        tab_widget.addTab(details_tab, "📖 詳細說明")
        
        # 使用建議標籤頁
        usage_tab = QWidget()
        usage_layout = QVBoxLayout(usage_tab)
        
        usage_text = QTextEdit()
        usage_text.setReadOnly(True)
        usage_text.setHtml(self.get_usage_guide())
        usage_layout.addWidget(usage_text)
        
        tab_widget.addTab(usage_tab, "💡 使用建議")
        
        layout.addWidget(tab_widget)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("關閉")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def get_strategy_overview(self):
        """獲取策略概述"""
        if self.strategy_name == "阿水一式":
            return """
                <h2>🌟 阿水一式 - 飆股捕捉策略</h2>
                <p><strong>策略類型：</strong>多方策略（適用於大盤綠燈時）</p>
                <p><strong>核心理念：</strong>壓縮、帶量、起漲 - 專門捕捉飆股信號</p>
                <p><strong>適用時機：</strong>大盤處於上升趨勢，20MA向上</p>
                <p><strong>預期勝率：</strong>中高（需配合大盤環境）</p>
                
                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>基本篩選：</strong>成交額 > 1000萬，成交量 > 5日均量2倍</li>
                    <li><strong>布林突破：</strong>突破2.1倍標準差的布林上通道</li>
                    <li><strong>技術確認：</strong>多頭排列 + 20MA翻揚</li>
                    <li><strong>創新高：</strong>創20日或60日新高（加分項）</li>
                </ul>
                
                <h3>⚡ 策略特色</h3>
                <ul>
                    <li>專門尋找即將飆漲的股票</li>
                    <li>嚴格的風險控制機制</li>
                    <li>量化評分系統（0-100分）</li>
                    <li>避免追高風險</li>
                </ul>
            """
        else:
            return f"""
                <h2>📊 {self.strategy_name}</h2>
                <p>這是一個策略說明示例。</p>
            """
    
    def get_strategy_details(self):
        """獲取策略詳細說明"""
        if self.strategy_name == "阿水一式":
            return """
                <h2>🔍 阿水一式 - 詳細技術說明</h2>
                
                <h3>📊 大盤綠燈判斷標準</h3>
                <ul>
                    <li><strong>大盤20MA上揚：</strong>大盤日線20日移動平均線向上</li>
                    <li><strong>布林帶表現：</strong>即使股價碰到布林下通道，也能立即脫離</li>
                    <li><strong>籌碼指標：</strong>Put/Call Ratio > 1</li>
                    <li><strong>市場狀態：</strong>在綠燈時，大盤安全，適合持續做多</li>
                </ul>
                
                <h3>🎯 選股心法與原則</h3>
                <h4>阿水愛股特性：</h4>
                <ol>
                    <li><strong>成交量條件：</strong>當日買盤量能 > 5日均量（最好2-5倍，避免超過10倍）</li>
                    <li><strong>價格突破：</strong>突破2.1倍標準差的布林上通道</li>
                    <li><strong>成交額門檻：</strong>總成交額 > 1000萬</li>
                    <li><strong>題材性：</strong>有話題性、財報或基本面表現良好</li>
                    <li><strong>創新高：</strong>當天創新高的前段班（注意超級歷史新高風險）</li>
                </ol>
                
                <h3>🔧 技術指標計算</h3>
                <ul>
                    <li><strong>布林帶：</strong>20MA ± 2.1 × STD(20)</li>
                    <li><strong>多頭排列：</strong>20MA > 60MA > 120MA</li>
                    <li><strong>20MA翻揚：</strong>連續3日20MA向上</li>
                    <li><strong>成交量比：</strong>當日量 ÷ 5日均量</li>
                </ul>
                
                <h3>📈 評分機制（0-100分）</h3>
                <ul>
                    <li><strong>基礎分：</strong>60分</li>
                    <li><strong>布林突破：</strong>+20分</li>
                    <li><strong>成交量品質：</strong>理想(2-5倍)+20分，可接受(5-10倍)+15分</li>
                    <li><strong>技術面：</strong>多頭排列+10分，3日連揚+5分</li>
                    <li><strong>創新高：</strong>60日新高+5分，20日新高+3分</li>
                </ul>
            """
        else:
            return f"<h2>{self.strategy_name} - 詳細說明</h2><p>詳細說明內容...</p>"
    
    def get_usage_guide(self):
        """獲取使用建議"""
        if self.strategy_name == "阿水一式":
            return """
                <h2>💡 阿水一式 - 實戰使用指南</h2>
                
                <h3>🎯 最佳使用時機</h3>
                <ul>
                    <li><strong>大盤環境：</strong>大盤處於綠燈狀態（20MA向上）</li>
                    <li><strong>市場情緒：</strong>投資人信心較佳，成交量活絡</li>
                    <li><strong>避免時機：</strong>大盤轉為黃燈或紅燈時暫停使用</li>
                </ul>
                
                <h3>📈 操作建議</h3>
                <h4>🔍 選股流程：</h4>
                <ol>
                    <li><strong>篩選：</strong>使用系統篩選出符合條件的股票</li>
                    <li><strong>評分：</strong>優先關注評分 ≥ 90分的標的</li>
                    <li><strong>確認：</strong>人工確認是否有題材或基本面支撐</li>
                    <li><strong>時機：</strong>在突破當日或隔日開盤買入</li>
                </ol>
                
                <h3>🛡️ 停利停損策略</h3>
                <h4>📈 停利方法：</h4>
                <ul>
                    <li><strong>主要停利：</strong>若大盤沒有翻成紅燈，個股跌破20MA時賣出</li>
                    <li><strong>獲利了結：</strong>漲幅達20-30%時分批獲利了結</li>
                </ul>
                
                <h4>🛑 停損控制：</h4>
                <ul>
                    <li><strong>快速停損：</strong>買進後隔天中尾盤跌破入手價5%先出場</li>
                    <li><strong>大盤轉弱：</strong>大盤翻成黃燈或紅燈時，賣出轉弱個股</li>
                </ul>
                
                <h3>⚠️ 風險提醒</h3>
                <ul>
                    <li><strong>市場風險：</strong>大盤轉弱時策略效果會下降</li>
                    <li><strong>個股風險：</strong>避免追漲停板或爆量股票</li>
                    <li><strong>時機風險：</strong>突破後若無法持續上漲要及時停損</li>
                </ul>
            """
        else:
            return f"<h2>{self.strategy_name} - 使用建議</h2><p>使用建議內容...</p>"

class TestMainWindow(QMainWindow):
    """測試主視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略說明對話框測試")
        self.setGeometry(100, 100, 400, 200)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 測試按鈕
        test_btn = QPushButton("📖 查看阿水一式策略說明")
        test_btn.clicked.connect(self.show_strategy_info)
        layout.addWidget(test_btn)
    
    def show_strategy_info(self):
        """顯示策略說明"""
        dialog = StrategyInfoDialog("阿水一式", self)
        dialog.exec()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 設置深色主題
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
    """)
    
    window = TestMainWindow()
    window.show()
    
    print("🚀 策略說明對話框測試啟動")
    print("點擊按鈕查看阿水一式策略說明")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
