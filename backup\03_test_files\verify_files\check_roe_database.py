#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查ROE數據庫狀況
"""

import sqlite3
import os

def check_roe_database():
    """檢查ROE數據庫"""
    db_path = 'D:/Finlab/history/tables/roe_data.db'
    print(f'🔍 檢查ROE數據庫: {db_path}')
    print(f'📁 文件存在: {os.path.exists(db_path)}')
    
    if not os.path.exists(db_path):
        print('⚠️ ROE數據庫文件不存在')
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roe_data'")
        table_exists = cursor.fetchone()
        print(f'📊 roe_data表格存在: {table_exists is not None}')
        
        if not table_exists:
            print('⚠️ roe_data表格不存在')
            conn.close()
            return False
        
        # 檢查表格結構
        cursor.execute('PRAGMA table_info(roe_data)')
        columns = cursor.fetchall()
        print(f'\n📋 表格結構:')
        for col in columns:
            print(f'  • {col[1]} ({col[2]})')
        
        # 檢查資料筆數
        cursor.execute('SELECT COUNT(*) FROM roe_data')
        count = cursor.fetchone()[0]
        print(f'\n📈 資料筆數: {count}')
        
        if count == 0:
            print('⚠️ 數據庫為空，沒有ROE資料')
            conn.close()
            return False
        
        # 顯示前5筆資料
        cursor.execute('''
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year, rank_position
            FROM roe_data 
            ORDER BY rank_position 
            LIMIT 5
        ''')
        rows = cursor.fetchall()
        
        print(f'\n📋 前5筆ROE資料:')
        for i, row in enumerate(rows, 1):
            stock_code, stock_name, roe_value, roe_change, eps_value, report_year, rank_position = row
            print(f'  {i}. {stock_code} {stock_name} - ROE: {roe_value}% (變化: {roe_change}) EPS: {eps_value} 年度: {report_year} 排名: {rank_position}')
        
        # 檢查年度分布
        cursor.execute('SELECT report_year, COUNT(*) FROM roe_data GROUP BY report_year ORDER BY report_year')
        year_counts = cursor.fetchall()
        print(f'\n📅 年度分布:')
        for year, count in year_counts:
            print(f'  • {year}年: {count}筆')
        
        conn.close()
        print(f'\n✅ ROE數據庫檢查完成，共有 {count} 筆資料')
        return True
        
    except Exception as e:
        print(f'❌ 檢查ROE數據庫時發生錯誤: {e}')
        return False

def fix_roe_viewer_query():
    """修正ROE資料查看器的查詢問題"""
    print(f'\n🔧 診斷ROE資料查看器問題...')
    
    db_path = 'D:/Finlab/history/tables/roe_data.db'
    if not os.path.exists(db_path):
        print('❌ ROE數據庫不存在')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試原始查詢
        print('🔍 測試原始查詢...')
        query = """
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, 
                   report_year, rank_position, crawl_date
            FROM roe_data 
            ORDER BY rank_position, roe_value DESC 
            LIMIT 50
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        if results:
            print(f'✅ 查詢成功，找到 {len(results)} 筆資料')
            print('📋 前3筆查詢結果:')
            for i, row in enumerate(results[:3], 1):
                print(f'  {i}. {row[0]} {row[1]} - ROE: {row[2]}%')
        else:
            print('⚠️ 查詢結果為空')
        
        # 測試簡化查詢
        print('\n🔍 測試簡化查詢...')
        cursor.execute('SELECT * FROM roe_data LIMIT 3')
        simple_results = cursor.fetchall()
        
        if simple_results:
            print(f'✅ 簡化查詢成功，找到 {len(simple_results)} 筆資料')
            for i, row in enumerate(simple_results, 1):
                print(f'  {i}. 完整記錄: {row}')
        else:
            print('⚠️ 簡化查詢也為空')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 診斷查詢時發生錯誤: {e}')

if __name__ == "__main__":
    print("🔍 ROE數據庫診斷工具")
    print("=" * 50)
    
    # 檢查數據庫
    success = check_roe_database()
    
    if success:
        # 診斷查詢問題
        fix_roe_viewer_query()
    
    print("\n🎉 診斷完成")
