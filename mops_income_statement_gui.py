#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MOPS綜合損益表下載器GUI
提供批次下載綜合損益表數據的用戶界面，包含EPS等財務資訊
基於tkinter的用戶界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import time
from datetime import datetime
import sqlite3
import pandas as pd
import logging

# 導入綜合損益表下載器
try:
    from mops_income_statement_downloader import MopsIncomeStatementDownloader
    DOWNLOADER_AVAILABLE = True
except ImportError:
    DOWNLOADER_AVAILABLE = False

class MopsIncomeStatementGUI:
    def __init__(self, parent=None):
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()
        
        self.setup_window()
        self.create_widgets()
        
        # 初始化下載器
        if DOWNLOADER_AVAILABLE:
            self.downloader = MopsIncomeStatementDownloader()
        else:
            self.downloader = None
            
    def setup_window(self):
        """設置視窗"""
        self.window.title("📊 MOPS綜合損益表下載器")
        self.window.geometry("1000x800")
        self.window.resizable(True, True)
        
        # 設置圖標
        try:
            self.window.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """創建界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 MOPS綜合損益表批量下載器",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 說明文字
        info_text = """
此工具從公開資訊觀測站(MOPS)批量下載綜合損益表數據，包含：
• 營業收入、營業利益、稅前淨利、本期淨利
• 基本每股盈餘(EPS)、稀釋每股盈餘
• 營業利益率、淨利率等財務比率
• 支援上市、上櫃公司
• 支援年季範圍批量下載
• 自動存入SQLite資料庫
        """
        
        info_label = ttk.Label(main_frame, text=info_text.strip(),
                              font=('Arial', 9), foreground='gray')
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 20), sticky=tk.W)

        # 檢查下載器可用性
        if not DOWNLOADER_AVAILABLE:
            error_label = ttk.Label(main_frame, 
                                   text="❌ 錯誤：找不到 mops_income_statement_downloader.py 模組",
                                   font=('Arial', 10, 'bold'), foreground='red')
            error_label.grid(row=2, column=0, columnspan=3, pady=10)
            return

        # 創建筆記本（分頁）
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        main_frame.rowconfigure(2, weight=1)

        # 下載頁面
        self.create_download_tab(notebook)
        
        # 統計頁面
        self.create_statistics_tab(notebook)
        
        # 設定頁面
        self.create_settings_tab(notebook)

    def create_download_tab(self, notebook):
        """創建下載頁面"""
        download_frame = ttk.Frame(notebook, padding="10")
        notebook.add(download_frame, text="📥 批量下載")
        
        # 時間範圍設定
        time_frame = ttk.LabelFrame(download_frame, text="📅 時間範圍設定", padding="10")
        time_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        download_frame.columnconfigure(0, weight=1)
        download_frame.columnconfigure(1, weight=1)
        
        # 開始時間
        ttk.Label(time_frame, text="開始時間:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        start_frame = ttk.Frame(time_frame)
        start_frame.grid(row=0, column=1, sticky=tk.W)
        
        self.start_year_var = tk.StringVar(value=str(datetime.now().year - 1))
        start_year_combo = ttk.Combobox(start_frame, textvariable=self.start_year_var, 
                                       values=[str(y) for y in range(2010, datetime.now().year + 1)],
                                       width=8, state="readonly")
        start_year_combo.grid(row=0, column=0, padx=(0, 5))
        
        ttk.Label(start_frame, text="年").grid(row=0, column=1, padx=(0, 10))
        
        self.start_quarter_var = tk.StringVar(value="1")
        start_quarter_combo = ttk.Combobox(start_frame, textvariable=self.start_quarter_var,
                                          values=["1", "2", "3", "4"], width=5, state="readonly")
        start_quarter_combo.grid(row=0, column=2, padx=(0, 5))
        
        ttk.Label(start_frame, text="季").grid(row=0, column=3)
        
        # 結束時間
        ttk.Label(time_frame, text="結束時間:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        end_frame = ttk.Frame(time_frame)
        end_frame.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        current_year = datetime.now().year
        current_quarter = (datetime.now().month - 1) // 3 + 1
        
        self.end_year_var = tk.StringVar(value=str(current_year))
        end_year_combo = ttk.Combobox(end_frame, textvariable=self.end_year_var,
                                     values=[str(y) for y in range(2010, current_year + 1)],
                                     width=8, state="readonly")
        end_year_combo.grid(row=0, column=0, padx=(0, 5))
        
        ttk.Label(end_frame, text="年").grid(row=0, column=1, padx=(0, 10))
        
        self.end_quarter_var = tk.StringVar(value=str(current_quarter - 1 if current_quarter > 1 else 4))
        end_quarter_combo = ttk.Combobox(end_frame, textvariable=self.end_quarter_var,
                                        values=["1", "2", "3", "4"], width=5, state="readonly")
        end_quarter_combo.grid(row=0, column=2, padx=(0, 5))
        
        ttk.Label(end_frame, text="季").grid(row=0, column=3)
        
        # 市場選擇
        market_frame = ttk.LabelFrame(download_frame, text="🏢 市場選擇", padding="10")
        market_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.market_vars = {}
        markets = [("上市", "sii"), ("上櫃", "otc")]
        
        for i, (name, code) in enumerate(markets):
            var = tk.BooleanVar(value=True)
            self.market_vars[code] = var
            ttk.Checkbutton(market_frame, text=name, variable=var).grid(row=0, column=i, padx=10, sticky=tk.W)
        
        # 下載控制
        control_frame = ttk.LabelFrame(download_frame, text="🎮 下載控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 下載按鈕
        self.download_btn = ttk.Button(control_frame, text="🚀 開始下載", 
                                      command=self.start_download, style="Accent.TButton")
        self.download_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 停止按鈕
        self.stop_btn = ttk.Button(control_frame, text="⏹️ 停止下載", 
                                  command=self.stop_download, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 清除日誌按鈕
        clear_btn = ttk.Button(control_frame, text="🗑️ 清除日誌", command=self.clear_log)
        clear_btn.grid(row=0, column=2)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(download_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 狀態標籤
        self.status_var = tk.StringVar(value="準備就緒")
        status_label = ttk.Label(download_frame, textvariable=self.status_var, font=('Arial', 9))
        status_label.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        # 日誌區域
        log_frame = ttk.LabelFrame(download_frame, text="📋 下載日誌", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        download_frame.rowconfigure(5, weight=1)
        
        # 創建日誌文本框和滾動條
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_text_frame, height=15, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)
        
        # 初始化變數
        self.download_thread = None
        self.stop_flag = False

    def create_statistics_tab(self, notebook):
        """創建統計頁面"""
        stats_frame = ttk.Frame(notebook, padding="10")
        notebook.add(stats_frame, text="📊 數據統計")

        # 統計信息框架
        info_frame = ttk.LabelFrame(stats_frame, text="📈 資料庫統計", padding="10")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure(0, weight=1)

        # 統計標籤
        self.stats_text = tk.Text(info_frame, height=10, width=80, wrap=tk.WORD,
                                 font=('Consolas', 10), state='disabled')
        stats_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        stats_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)

        # 刷新按鈕
        refresh_btn = ttk.Button(stats_frame, text="🔄 刷新統計", command=self.refresh_statistics)
        refresh_btn.grid(row=1, column=0, pady=10)

        # 匯出按鈕
        export_btn = ttk.Button(stats_frame, text="📤 匯出數據", command=self.export_data)
        export_btn.grid(row=2, column=0, pady=5)

        # 初始載入統計
        self.refresh_statistics()

    def create_settings_tab(self, notebook):
        """創建設定頁面"""
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="⚙️ 設定")

        # 資料庫設定
        db_frame = ttk.LabelFrame(settings_frame, text="🗄️ 資料庫設定", padding="10")
        db_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(0, weight=1)

        ttk.Label(db_frame, text="資料庫路徑:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.db_path_var = tk.StringVar(value="D:/Finlab/history/tables/income_statement.db")
        db_path_entry = ttk.Entry(db_frame, textvariable=self.db_path_var, width=50)
        db_path_entry.grid(row=0, column=1, padx=(0, 10), sticky=(tk.W, tk.E))
        db_frame.columnconfigure(1, weight=1)

        browse_btn = ttk.Button(db_frame, text="瀏覽", command=self.browse_db_path)
        browse_btn.grid(row=0, column=2)

        # 下載設定
        download_settings_frame = ttk.LabelFrame(settings_frame, text="⚡ 下載設定", padding="10")
        download_settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(download_settings_frame, text="請求間隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.delay_var = tk.StringVar(value="2")
        delay_entry = ttk.Entry(download_settings_frame, textvariable=self.delay_var, width=10)
        delay_entry.grid(row=0, column=1, sticky=tk.W)

        ttk.Label(download_settings_frame, text="(避免過於頻繁請求)").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

        # 更新界面
        self.window.update_idletasks()

    def clear_log(self):
        """清除日誌"""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def update_progress(self, current, total):
        """更新進度條"""
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)
            self.status_var.set(f"進度: {current}/{total} ({progress:.1f}%)")
        else:
            self.progress_var.set(0)
            self.status_var.set("準備就緒")

    def start_download(self):
        """開始下載"""
        if not DOWNLOADER_AVAILABLE:
            messagebox.showerror("錯誤", "下載器模組不可用")
            return

        # 驗證輸入
        try:
            start_year = int(self.start_year_var.get())
            start_quarter = int(self.start_quarter_var.get())
            end_year = int(self.end_year_var.get())
            end_quarter = int(self.end_quarter_var.get())
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的年份和季度")
            return

        # 檢查時間範圍
        if start_year > end_year or (start_year == end_year and start_quarter > end_quarter):
            messagebox.showerror("錯誤", "開始時間不能晚於結束時間")
            return

        # 檢查市場選擇
        selected_markets = [code for code, var in self.market_vars.items() if var.get()]
        if not selected_markets:
            messagebox.showerror("錯誤", "請至少選擇一個市場")
            return

        # 確認下載
        periods_count = 0
        current_year, current_quarter = start_year, start_quarter
        while current_year < end_year or (current_year == end_year and current_quarter <= end_quarter):
            periods_count += 1
            current_quarter += 1
            if current_quarter > 4:
                current_quarter = 1
                current_year += 1

        total_downloads = periods_count * len(selected_markets)

        confirm_msg = f"""
即將下載綜合損益表數據：

時間範圍: {start_year}Q{start_quarter} - {end_year}Q{end_quarter}
市場: {', '.join([self.get_market_name(m) for m in selected_markets])}
預計下載次數: {total_downloads}

確定要開始下載嗎？
        """

        if not messagebox.askyesno("確認下載", confirm_msg.strip()):
            return

        # 重置狀態
        self.stop_flag = False
        self.clear_log()
        self.update_progress(0, total_downloads)

        # 更新按鈕狀態
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")

        # 開始下載線程
        self.download_thread = threading.Thread(
            target=self.download_worker,
            args=(start_year, start_quarter, end_year, end_quarter, selected_markets),
            daemon=True
        )
        self.download_thread.start()

    def get_market_name(self, code):
        """獲取市場名稱"""
        market_names = {'sii': '上市', 'otc': '上櫃', 'rotc': '興櫃', 'pub': '公開發行'}
        return market_names.get(code, code)

    def download_worker(self, start_year, start_quarter, end_year, end_quarter, markets):
        """下載工作線程"""
        try:
            self.log_message("🚀 開始批量下載綜合損益表數據...")

            # 更新下載器的資料庫路徑
            if hasattr(self.downloader, 'db_path'):
                self.downloader.db_path = self.db_path_var.get()

            total_saved = 0

            # 生成年季組合
            periods = []
            current_year = start_year
            current_quarter = start_quarter

            while current_year < end_year or (current_year == end_year and current_quarter <= end_quarter):
                periods.append((current_year, current_quarter))

                current_quarter += 1
                if current_quarter > 4:
                    current_quarter = 1
                    current_year += 1

            total_periods = len(periods) * len(markets)
            current_progress = 0

            for year, quarter in periods:
                if self.stop_flag:
                    self.log_message("⏹️ 用戶停止下載")
                    break

                for market in markets:
                    if self.stop_flag:
                        break

                    current_progress += 1
                    market_name = self.get_market_name(market)

                    self.log_message(f"📊 下載 {market_name} {year}Q{quarter} 數據...")
                    self.update_progress(current_progress, total_periods)

                    try:
                        # 下載數據
                        income_data = self.downloader.download_market_income_statement(market, year, quarter)

                        if income_data:
                            # 保存到資料庫
                            saved_count = self.downloader.save_to_database(income_data)
                            total_saved += saved_count

                            self.log_message(f"✅ {market_name} {year}Q{quarter} 完成，保存 {saved_count} 筆數據")
                        else:
                            self.log_message(f"⚠️ {market_name} {year}Q{quarter} 沒有獲取到數據")

                        # 延遲避免過於頻繁請求
                        delay = float(self.delay_var.get())
                        if delay > 0:
                            time.sleep(delay)

                    except Exception as e:
                        self.log_message(f"❌ {market_name} {year}Q{quarter} 下載失敗: {e}")
                        continue

            if not self.stop_flag:
                self.log_message(f"🎉 批量下載完成！總共保存 {total_saved} 筆綜合損益表數據")
                self.update_progress(total_periods, total_periods)

                # 刷新統計
                self.refresh_statistics()

                # 顯示完成對話框
                self.window.after(0, lambda: messagebox.showinfo(
                    "下載完成",
                    f"批量下載完成！\n總共保存 {total_saved} 筆數據"
                ))

        except Exception as e:
            error_msg = f"❌ 下載過程中發生錯誤: {e}"
            self.log_message(error_msg)
            self.window.after(0, lambda: messagebox.showerror("下載錯誤", error_msg))

        finally:
            # 恢復按鈕狀態
            self.window.after(0, self.download_finished)

    def download_finished(self):
        """下載完成後的清理工作"""
        self.download_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_var.set("下載完成")

    def stop_download(self):
        """停止下載"""
        self.stop_flag = True
        self.log_message("⏹️ 正在停止下載...")
        self.status_var.set("正在停止...")

    def refresh_statistics(self):
        """刷新統計資訊"""
        if not DOWNLOADER_AVAILABLE:
            return

        try:
            stats = self.downloader.get_statistics()

            if stats:
                stats_text = f"""
📊 綜合損益表資料庫統計

📈 總記錄數: {stats['total_count']:,} 筆

📅 最新數據: {stats['latest_data'][0] if stats['latest_data'][0] else 'N/A'}年第{stats['latest_data'][1] if stats['latest_data'][1] else 'N/A'}季

📊 年份分布:
"""

                for year, count in stats['year_stats']:
                    stats_text += f"  {year}年: {count:,} 筆\n"

                stats_text += "\n🏢 市場分布:\n"
                for market, count in stats['market_stats']:
                    stats_text += f"  {market}: {count:,} 筆\n"

                # 更新統計文本
                self.stats_text.config(state='normal')
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(1.0, stats_text.strip())
                self.stats_text.config(state='disabled')

            else:
                self.stats_text.config(state='normal')
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(1.0, "❌ 無法獲取統計資訊")
                self.stats_text.config(state='disabled')

        except Exception as e:
            self.stats_text.config(state='normal')
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, f"❌ 統計資訊獲取失敗: {e}")
            self.stats_text.config(state='disabled')

    def browse_db_path(self):
        """瀏覽資料庫路徑"""
        filename = filedialog.asksaveasfilename(
            title="選擇資料庫檔案",
            defaultextension=".db",
            filetypes=[("SQLite資料庫", "*.db"), ("所有檔案", "*.*")]
        )
        if filename:
            self.db_path_var.set(filename)

    def export_data(self):
        """匯出數據"""
        if not DOWNLOADER_AVAILABLE:
            messagebox.showerror("錯誤", "下載器模組不可用")
            return

        try:
            # 選擇匯出檔案
            filename = filedialog.asksaveasfilename(
                title="匯出綜合損益表數據",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel檔案", "*.xlsx"),
                    ("CSV檔案", "*.csv"),
                    ("所有檔案", "*.*")
                ]
            )

            if not filename:
                return

            # 從資料庫讀取數據
            conn = sqlite3.connect(self.db_path_var.get())

            query = """
                SELECT stock_id, stock_name, industry, year, quarter, market,
                       revenue, operating_income, net_income_before_tax, net_income,
                       eps, diluted_eps, operating_margin, net_margin, roe, roa,
                       created_at, updated_at
                FROM income_statement
                ORDER BY year DESC, quarter DESC, stock_id
            """

            df = pd.read_sql_query(query, conn)
            conn.close()

            if df.empty:
                messagebox.showwarning("警告", "沒有數據可以匯出")
                return

            # 匯出檔案
            if filename.endswith('.xlsx'):
                df.to_excel(filename, index=False, engine='openpyxl')
            else:
                df.to_csv(filename, index=False, encoding='utf-8-sig')

            messagebox.showinfo("匯出成功", f"已成功匯出 {len(df)} 筆數據到:\n{filename}")

        except Exception as e:
            messagebox.showerror("匯出失敗", f"匯出數據時發生錯誤:\n{e}")

    def run(self):
        """運行GUI"""
        try:
            self.window.mainloop()
        except KeyboardInterrupt:
            pass

if __name__ == "__main__":
    app = MopsIncomeStatementGUI()
    app.run()
