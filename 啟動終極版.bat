@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 終極穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        終極穩定版 - 完全修復版
echo ========================================
echo.

if exist "dist\StockAnalyzer_Ultimate.exe" (
    echo ✅ 找到終極穩定版
    echo 🚀 正在啟動...
    echo.
    echo 💡 終極穩定版特點：
    echo    ✓ 修復所有 PyQt5/PyQt6 衝突
    echo    ✓ 排除所有有問題的模組
    echo    ✓ 完全獨立，無外部依賴
    echo    ✓ 最高穩定性保證
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Ultimate.exe"
    
    echo ✅ 終極穩定版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到終極穩定版
    echo.
    echo 請重新編譯：
    echo    python ultimate_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 功能說明：
echo    ✓ 核心股票分析功能
echo    ✓ 數據查詢和篩選
echo    ✓ Excel 報告導出
echo    ✓ 完整用戶界面
echo.
echo ⚠️ 注意：
echo    - 已排除有問題的圖表模組
echo    - 核心選股功能完全保留
echo    - 如有問題請檢查防毒軟體
echo.

timeout /t 8 >nul
