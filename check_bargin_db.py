#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_bargin_db():
    """檢查 bargin_report.db 的結構"""
    db_path = 'D:/Finlab/history/tables/bargin_report.db'
    
    if not os.path.exists(db_path):
        print('❌ bargin_report.db 不存在')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表結構
        cursor.execute('PRAGMA table_info(bargin_report)')
        columns = cursor.fetchall()
        
        print('📋 bargin_report 表結構:')
        for col in columns:
            print(f'  {col[1]} ({col[2]})')
        
        # 檢查樣本資料
        cursor.execute('SELECT * FROM bargin_report LIMIT 3')
        samples = cursor.fetchall()
        
        print('\n📊 樣本資料:')
        for i, sample in enumerate(samples):
            print(f'  記錄 {i+1}: {sample[:8]}...')  # 只顯示前8個欄位
        
        # 檢查特定股票的資料
        cursor.execute('SELECT * FROM bargin_report WHERE stock_id = "2330" ORDER BY date DESC LIMIT 3')
        tsmc_data = cursor.fetchall()
        
        print('\n📊 2330台積電樣本資料:')
        for i, sample in enumerate(tsmc_data):
            print(f'  記錄 {i+1}: {sample[:8]}...')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 檢查失敗: {e}')

if __name__ == '__main__':
    check_bargin_db()
