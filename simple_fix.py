#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化修復腳本 - 直接修復策略問題
"""

def fix_strategy_list():
    """修復策略列表"""
    print("修復策略交集分析的策略列表...")
    
    with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修復策略列表 - 添加更多策略
    old_list = '''        real_data_strategies = [
            "勝率73.45%", "破底反彈高量", "阿水一式", "阿水二式",
            "藏獒", "CANSLIM量價齊升", "膽小貓", "低價股策略",
            "高殖利率烏龜",  # 新增：已整合PKL真實數據
            "二次創高股票", "三頻率RSI策略", "小資族資優生策略"
        ]'''
    
    new_list = '''        real_data_strategies = [
            "勝率73.45%", "破底反彈高量", "阿水一式", "阿水二式",
            "藏獒", "CANSLIM量價齊升", "膽小貓", "低價股策略",
            "高殖利率烏龜", "二次創高股票", "三頻率RSI策略", 
            "小資族資優生策略", "監獄兔", "超級績效台股版",
            "台股總體經濟ETF", "小蝦米跟大鯨魚", "研發魔人",
            "台灣十五小市值", "本益成長比", "多產業價投",
            "低波動本益成長比", "財務指標計分選股法",
            "營收股價雙渦輪", "景氣燈號加減碼", "現金流價值成長"
        ]'''
    
    if old_list in content:
        content = content.replace(old_list, new_list)
        print("✅ 策略列表已修復")
    else:
        print("⚠️ 未找到原始策略列表")
    
    # 修復結果顯示函數
    old_show = '''    def show_results(self, results):
        self.result_table.setRowCount(0)
        if not results:
            return

        self.result_table.setSortingEnabled(False)  # 暫時禁用排序功能以提高性能
        self.result_table.setRowCount(len(results))'''
    
    new_show = '''    def show_results(self, results):
        """顯示策略執行結果"""
        try:
            # 清空表格和列表
            self.result_table.setRowCount(0)
            if hasattr(self, 'filtered_stocks_list'):
                self.filtered_stocks_list.clear()
            
            if not results:
                self.show_status("策略執行完成，但沒有找到符合條件的股票")
                return

            self.result_table.setSortingEnabled(False)  # 暫時禁用排序功能以提高性能
            self.result_table.setRowCount(len(results))
            
            # 緩存策略結果
            current_strategy = self.strategy_combo.currentText()
            if current_strategy and results:
                import pandas as pd
                result_data = []
                for stock in results:
                    result_data.append({
                        '股票代碼': stock.get('股票代碼', ''),
                        '股票名稱': stock.get('股票名稱', ''),
                        '收盤價': stock.get('收盤價', 0),
                        '評分': stock.get('評分', 0)
                    })
                
                if result_data:
                    result_df = pd.DataFrame(result_data)
                    if not hasattr(self, 'strategy_results_cache'):
                        self.strategy_results_cache = {}
                    self.strategy_results_cache[current_strategy] = result_df
                    print(f"策略結果已緩存: {current_strategy}, 共 {len(result_data)} 支股票")
            
        except Exception as e:
            print(f"顯示結果時發生錯誤: {e}")'''
    
    if old_show in content:
        content = content.replace(old_show, new_show)
        print("✅ 結果顯示函數已修復")
    else:
        print("⚠️ 未找到原始結果顯示函數")
    
    # 寫回文件
    with open('O3mh_gui_v21_optimized.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 策略問題修復完成")

def create_simple_compile():
    """創建簡化編譯腳本"""
    print("創建簡化編譯腳本...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

hiddenimports = [
    'inspect', 'pydoc', 'doctest', 'difflib', 'importlib', 'importlib.util',
    'sqlite3', 'json', 'csv', 'datetime', 'calendar', 'time', 'threading',
    'concurrent.futures', 'logging', 'traceback', 'os', 'sys', 'random', 'warnings',
    'PyQt6', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'PyQt6.sip',
    'pandas', 'numpy', 'numpy.core', 'numpy.core.numeric',
    'requests', 'urllib3', 'bs4', 'beautifulsoup4', 'openpyxl',
    'setuptools', 'pkg_resources', 'itertools',
]

excludes = [
    'tkinter', 'test', 'tests', 'unittest', 'pdb', 'PyQt5', 'PySide2', 'PySide6',
    'IPython', 'jupyter', 'notebook', 'twstock', 'yfinance', 'finlab', 'finmind',
    'talib', 'mplfinance', 'matplotlib', 'seaborn', 'pyqtgraph', 'xlsxwriter',
    'selenium', 'webdriver_manager', 'apscheduler', 'charts', 'config',
    'enhanced_dividend_crawler', 'integrated_strategy_help_dialog',
    'unified_monitor_manager', 'smart_trading_strategies', 'strategies', 'monitoring',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[], binaries=[], datas=[], hiddenimports=hiddenimports,
    hookspath=[], hooksconfig={}, runtime_hooks=[], excludes=excludes,
    win_no_prefer_redirects=False, win_private_assemblies=False,
    cipher=block_cipher, noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='StockAnalyzer_Fixed', debug=False, bootloader_ignore_signals=False,
    strip=False, upx=True, upx_exclude=[], runtime_tmpdir=None, console=False,
    disable_windowed_traceback=False, argv_emulation=False,
    target_arch=None, codesign_identity=None, entitlements_file=None,
)
'''
    
    with open('fixed_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    compile_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import shutil
import time

def main():
    print("開始編譯修復版本...")
    
    # 清理環境
    if os.path.exists('build'):
        shutil.rmtree('build')
    time.sleep(2)
    
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'fixed_compile.spec']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            exe_path = 'dist/StockAnalyzer_Fixed.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"編譯成功！")
                print(f"檔案: {exe_path}")
                print(f"大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                launcher = """@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 修復版

echo.
echo ========================================
echo    台股智能選股系統 v22.1 - 修復版
echo ========================================
echo.

if exist "dist\\\\StockAnalyzer_Fixed.exe" (
    echo 找到修復版
    echo 正在啟動...
    echo.
    echo 修復版特點：
    echo    修復選股結果顯示問題
    echo    完整的策略交集分析
    echo    改善的用戶體驗
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Fixed.exe"
    
    echo 修復版已啟動！
    echo.
    
) else (
    echo 錯誤：找不到修復版
    echo.
    pause
    exit /b 1
)

timeout /t 3 >nul
"""
                
                with open('啟動修復版.bat', 'w', encoding='utf-8') as f:
                    f.write(launcher)
                
                print("創建啟動腳本: 啟動修復版.bat")
                return True
            else:
                print("找不到編譯後的檔案")
                return False
        else:
            print("編譯失敗！")
            if result.stderr:
                print("錯誤:", result.stderr[-1000:])
            return False
            
    except Exception as e:
        print(f"編譯過程錯誤: {e}")
        return False

if __name__ == "__main__":
    main()
'''
    
    with open('compile_fixed.py', 'w', encoding='utf-8') as f:
        f.write(compile_script)
    
    print("✅ 編譯腳本已創建: compile_fixed.py")

def main():
    print("台股智能選股系統 - 策略問題修復工具")
    print("=" * 50)
    
    # 修復策略問題
    fix_strategy_list()
    print()
    
    # 創建編譯腳本
    create_simple_compile()
    print()
    
    print("修復完成！")
    print()
    print("下一步:")
    print("1. 執行: python compile_fixed.py")
    print("2. 使用: 啟動修復版.bat")
    print()
    print("這將解決選股結果不顯示和策略交集不完整的問題！")

if __name__ == "__main__":
    main()
