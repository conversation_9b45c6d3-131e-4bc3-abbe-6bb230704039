#!/usr/bin/env python3
"""
測試 Twelve Data API 計劃和可用功能
"""

import requests
import time
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

API_KEY = "42c14f2a63c1420798627dce49fd682a"
BASE_URL = "https://api.twelvedata.com"

def test_api_usage():
    """測試API使用情況和計劃詳情"""
    print("📊 檢查API計劃和使用情況...")
    
    url = f"{BASE_URL}/api_usage"
    params = {
        'apikey': API_KEY,
        'format': 'JSON'
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API使用情況:")
            print(f"   計劃類別: {data.get('plan_category', 'unknown')}")
            print(f"   每分鐘限制: {data.get('plan_limit', 'unknown')}")
            print(f"   每日限制: {data.get('plan_daily_limit', 'unknown')}")
            print(f"   當前使用: {data.get('current_usage', 'unknown')}")
            print(f"   今日使用: {data.get('daily_usage', 'unknown')}")
            return data
        else:
            print(f"❌ 無法獲取API使用情況: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ API使用情況查詢失敗: {e}")
        return None

def test_us_stocks():
    """測試美股數據（免費版應該支援）"""
    print(f"\n📈 測試美股數據...")
    
    # 測試一些知名美股
    us_stocks = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]
    
    for symbol in us_stocks:
        print(f"\n🔍 測試美股: {symbol}")
        
        url = f"{BASE_URL}/time_series"
        params = {
            'symbol': symbol,
            'interval': '1day',
            'outputsize': 5,
            'apikey': API_KEY,
            'format': 'JSON'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'status' in data and data['status'] == 'error':
                    print(f"   ❌ 錯誤: {data.get('message', '未知錯誤')}")
                elif 'values' in data and data['values']:
                    print(f"   ✅ 成功獲取 {len(data['values'])} 筆數據")
                    latest = data['values'][0]
                    print(f"   最新價格: ${latest['close']}")
                else:
                    print(f"   ❌ 無數據返回")
            else:
                print(f"   ❌ HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")
        
        # 避免API速率限制
        time.sleep(8)

def test_taiwan_stock_with_correct_format():
    """使用正確格式測試台股（預期會失敗，因為需要Pro計劃）"""
    print(f"\n🇹🇼 測試台股（使用正確格式）...")
    
    # 根據文檔，台股格式應該是 股票代碼:TWSE
    taiwan_stocks = ["2330:TWSE", "2317:TWSE", "2454:TWSE"]
    
    for symbol in taiwan_stocks:
        print(f"\n🔍 測試台股: {symbol}")
        
        url = f"{BASE_URL}/time_series"
        params = {
            'symbol': symbol,
            'interval': '1day',
            'outputsize': 5,
            'apikey': API_KEY,
            'format': 'JSON'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'status' in data and data['status'] == 'error':
                    print(f"   ❌ 錯誤: {data.get('message', '未知錯誤')}")
                elif 'values' in data and data['values']:
                    print(f"   ✅ 成功獲取 {len(data['values'])} 筆數據")
                    latest = data['values'][0]
                    print(f"   最新價格: {latest['close']} TWD")
                else:
                    print(f"   ❌ 無數據返回")
            else:
                print(f"   ❌ HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")
        
        # 避免API速率限制
        time.sleep(8)

def test_forex():
    """測試外匯數據（免費版應該支援）"""
    print(f"\n💱 測試外匯數據...")
    
    # 測試一些主要貨幣對
    forex_pairs = ["EUR/USD", "GBP/USD", "USD/JPY", "USD/TWD"]
    
    for pair in forex_pairs:
        print(f"\n🔍 測試貨幣對: {pair}")
        
        url = f"{BASE_URL}/time_series"
        params = {
            'symbol': pair,
            'interval': '1day',
            'outputsize': 5,
            'apikey': API_KEY,
            'format': 'JSON'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'status' in data and data['status'] == 'error':
                    print(f"   ❌ 錯誤: {data.get('message', '未知錯誤')}")
                elif 'values' in data and data['values']:
                    print(f"   ✅ 成功獲取 {len(data['values'])} 筆數據")
                    latest = data['values'][0]
                    print(f"   最新匯率: {latest['close']}")
                else:
                    print(f"   ❌ 無數據返回")
            else:
                print(f"   ❌ HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")
        
        # 避免API速率限制
        time.sleep(8)

def test_crypto():
    """測試加密貨幣數據（免費版應該支援）"""
    print(f"\n₿ 測試加密貨幣數據...")
    
    # 測試一些主要加密貨幣
    crypto_symbols = ["BTC/USD", "ETH/USD", "BNB/USD"]
    
    for symbol in crypto_symbols:
        print(f"\n🔍 測試加密貨幣: {symbol}")
        
        url = f"{BASE_URL}/time_series"
        params = {
            'symbol': symbol,
            'interval': '1day',
            'outputsize': 5,
            'apikey': API_KEY,
            'format': 'JSON'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'status' in data and data['status'] == 'error':
                    print(f"   ❌ 錯誤: {data.get('message', '未知錯誤')}")
                elif 'values' in data and data['values']:
                    print(f"   ✅ 成功獲取 {len(data['values'])} 筆數據")
                    latest = data['values'][0]
                    print(f"   最新價格: ${latest['close']}")
                else:
                    print(f"   ❌ 無數據返回")
            else:
                print(f"   ❌ HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")
        
        # 避免API速率限制
        time.sleep(8)

if __name__ == "__main__":
    print("🚀 開始測試 Twelve Data API 計劃和功能...")
    
    # 檢查API計劃
    api_usage = test_api_usage()
    
    # 測試美股（免費版支援）
    test_us_stocks()
    
    # 測試台股（需要Pro計劃）
    test_taiwan_stock_with_correct_format()
    
    # 測試外匯
    test_forex()
    
    # 測試加密貨幣
    test_crypto()
    
    print(f"\n🎉 測試完成！")
    print(f"\n📋 總結:")
    print(f"   - 您的API密鑰是免費版 (basic plan)")
    print(f"   - 台股數據需要Pro計劃才能使用")
    print(f"   - 建議升級到Pro計劃以獲取台股數據")
    print(f"   - 或者繼續使用現有的yfinance等數據源")
