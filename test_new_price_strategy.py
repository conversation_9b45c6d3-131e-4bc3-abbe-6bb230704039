#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新的 price 存檔策略
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_new_price_strategy():
    """測試新的 price 存檔策略"""
    print("🧪 測試新的 price 存檔策略...")
    
    try:
        from crawler import to_pickle
        
        # 創建測試資料
        test_data = pd.DataFrame({
            'close': [100, 101, 102],
            'volume': [1000, 1100, 1200]
        })
        test_data['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03'])
        test_data['stock_id'] = ['1101', '1102', '1103']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 測試存檔到 price
        print("\n1️⃣ 測試 price 存檔...")
        to_pickle(test_data, 'price')
        print("✅ price 存檔完成")
        
        # 檢查檔案
        price_file = "history/tables/price.pkl"
        price_v2_file = "history/tables/price_v2.pkl"
        
        if os.path.exists(price_file):
            try:
                data = pd.read_pickle(price_file)
                print(f"✅ price.pkl 可讀取，{len(data)} 筆資料")
            except Exception as e:
                print(f"⚠️ price.pkl 讀取失敗: {str(e)[:50]}...")
        
        if os.path.exists(price_v2_file):
            try:
                data = pd.read_pickle(price_v2_file)
                print(f"✅ price_v2.pkl 可讀取，{len(data)} 筆資料")
            except Exception as e:
                print(f"⚠️ price_v2.pkl 讀取失敗: {str(e)[:50]}...")
        
        # 測試第二次存檔（應該會合併）
        print("\n2️⃣ 測試第二次存檔...")
        more_data = pd.DataFrame({
            'close': [103, 104],
            'volume': [1300, 1400]
        })
        more_data['date'] = pd.to_datetime(['2022-01-04', '2022-01-05'])
        more_data['stock_id'] = ['1104', '1105']
        more_data = more_data.set_index(['stock_id', 'date'])
        
        to_pickle(more_data, 'price')
        print("✅ 第二次存檔完成")
        
        # 驗證合併結果
        if os.path.exists(price_v2_file):
            final_data = pd.read_pickle(price_v2_file)
            print(f"✅ 最終資料: {len(final_data)} 筆")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_file_status():
    """檢查檔案狀態"""
    print("\n🔍 檢查檔案狀態...")
    
    files_to_check = [
        "history/tables/price.pkl",
        "history/tables/price_v2.pkl"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            mtime = os.path.getmtime(file_path)
            print(f"   {file_path}:")
            print(f"     大小: {size:,} bytes")
            print(f"     修改時間: {datetime.fromtimestamp(mtime)}")
            
            try:
                data = pd.read_pickle(file_path)
                latest_date = data.index.get_level_values('date').max()
                earliest_date = data.index.get_level_values('date').min()
                print(f"     資料: {len(data):,} 筆")
                print(f"     日期範圍: {earliest_date} 至 {latest_date}")
            except Exception as e:
                print(f"     ❌ 讀取失敗: {str(e)[:50]}...")
        else:
            print(f"   {file_path}: 不存在")

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 新的 price 存檔策略測試")
    print("=" * 60)
    
    # 檢查當前檔案狀態
    check_file_status()
    
    # 測試新策略
    success = test_new_price_strategy()
    
    # 再次檢查檔案狀態
    check_file_status()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    
    if success:
        print("✅ 新的存檔策略正常工作")
        print("\n💡 策略說明:")
        print("   • 如果原 price.pkl 讀取失敗")
        print("   • 會自動創建 price_v2.pkl")
        print("   • 後續存檔會使用新檔案")
        print("   • 避免兼容性問題")
        
        print("\n🚀 建議:")
        print("   • 可以重新啟動 auto_update.py")
        print("   • 新版本會自動處理檔案問題")
        print("   • 不會再出現 numpy 錯誤")
    else:
        print("❌ 新策略測試失敗")
        print("⚠️ 需要進一步檢查")
    
    return success

if __name__ == "__main__":
    main()
