#!/usr/bin/env python3
"""
策略交集功能演示腳本
展示如何使用新整合的策略交集分析功能
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def demo_intersection_functionality():
    """演示策略交集功能"""
    print("🎬 策略交集功能演示")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        window.show()
        
        # 模擬策略結果數據
        setup_demo_data(window)
        
        # 顯示使用指南
        show_usage_guide(window)
        
        # 自動切換到策略交集標籤頁
        switch_to_intersection_tab(window)
        
        print("✅ 演示環境已準備完成")
        print("🎯 請按照彈出的指南操作")
        
        # 運行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 演示失敗: {e}")
        import traceback
        traceback.print_exc()

def setup_demo_data(window):
    """設置演示數據"""
    try:
        import pandas as pd
        
        print("📊 設置演示數據...")
        
        # 模擬CANSLIM策略結果
        canslim_data = pd.DataFrame({
            '股票代碼': ['2330', '2317', '2454', '2327', '2603'],
            '股票名稱': ['台積電', '鴻海', '聯發科', '國巨', '長榮'],
            '收盤價': [500, 100, 800, 300, 150],
            '成交量': [50000, 30000, 20000, 15000, 40000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 模擬藏獒策略結果
        mastiff_data = pd.DataFrame({
            '股票代碼': ['2330', '2454', '2327', '3008', '1590'],
            '股票名稱': ['台積電', '聯發科', '國巨', '大立光', '亞德客'],
            '收盤價': [500, 800, 300, 2000, 250],
            '成交量': [50000, 20000, 15000, 10000, 12000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 模擬二次創高策略結果
        second_high_data = pd.DataFrame({
            '股票代碼': ['2330', '2317', '3008', '2412', '2881'],
            '股票名稱': ['台積電', '鴻海', '大立光', '中華電', '富邦金'],
            '收盤價': [500, 100, 2000, 120, 60],
            '成交量': [50000, 30000, 10000, 25000, 35000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 模擬膽小貓策略結果
        timid_cat_data = pd.DataFrame({
            '股票代碼': ['2330', '2412', '2881', '1216', '2002'],
            '股票名稱': ['台積電', '中華電', '富邦金', '統一', '中鋼'],
            '收盤價': [500, 120, 60, 80, 25],
            '成交量': [50000, 25000, 35000, 20000, 45000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 保存到緩存
        window.strategy_results_cache['CANSLIM量價齊升'] = canslim_data
        window.strategy_results_cache['藏獒'] = mastiff_data
        window.strategy_results_cache['二次創高股票'] = second_high_data
        window.strategy_results_cache['膽小貓'] = timid_cat_data
        
        print("✅ 演示數據設置完成")
        print(f"  📈 CANSLIM量價齊升: {len(canslim_data)} 支股票")
        print(f"  🐕 藏獒: {len(mastiff_data)} 支股票")
        print(f"  📊 二次創高股票: {len(second_high_data)} 支股票")
        print(f"  🐱 膽小貓: {len(timid_cat_data)} 支股票")
        
        return True
        
    except Exception as e:
        print(f"❌ 設置演示數據失敗: {e}")
        return False

def show_usage_guide(window):
    """顯示使用指南"""
    guide_text = """
🎯 策略交集分析功能演示

📊 已為您準備了4個策略的模擬數據：
• CANSLIM量價齊升 (5支股票)
• 藏獒 (5支股票)  
• 二次創高股票 (5支股票)
• 膽小貓 (4支股票)

🔍 演示步驟：
1. 切換到「🔗 策略交集」標籤頁
2. 勾選要分析的策略（建議選2-3個）
3. 點擊「🎯 計算交集」查看共同股票
4. 點擊「🔍 分析所有組合」查看所有組合
5. 點擊「📁 導出結果」保存分析結果

💡 推薦組合：
• 積極型：CANSLIM + 藏獒 + 二次創高
• 穩健型：膽小貓 + CANSLIM
• 動能型：藏獒 + 二次創高

🎯 預期結果：
三策略交集應該會找到台積電(2330)，
這是被多個策略同時看好的優質股票！

點擊「確定」開始體驗！
"""
    
    QMessageBox.information(window, "🎬 策略交集功能演示", guide_text)

def switch_to_intersection_tab(window):
    """自動切換到策略交集標籤頁"""
    try:
        # 使用QTimer延遲執行，確保GUI完全載入
        def switch_tab():
            tab_widget = window.tab_widget
            for i in range(tab_widget.count()):
                if "策略交集" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    print(f"✅ 已切換到策略交集標籤頁")
                    
                    # 預選一些策略
                    if hasattr(window, 'intersection_strategy_vars'):
                        # 預選CANSLIM和藏獒
                        if 'CANSLIM量價齊升' in window.intersection_strategy_vars:
                            window.intersection_strategy_vars['CANSLIM量價齊升'].setChecked(True)
                        if '藏獒' in window.intersection_strategy_vars:
                            window.intersection_strategy_vars['藏獒'].setChecked(True)
                        print("✅ 已預選推薦的策略組合")
                    break
        
        # 延遲500毫秒執行
        QTimer.singleShot(500, switch_tab)
        
    except Exception as e:
        print(f"⚠️ 切換標籤頁失敗: {e}")

def create_demo_script():
    """創建演示腳本說明"""
    script_info = """
# 🎬 策略交集功能演示腳本

## 功能說明
這個腳本會：
1. 啟動主程式
2. 自動載入模擬的策略結果數據
3. 顯示使用指南
4. 切換到策略交集標籤頁
5. 預選推薦的策略組合

## 使用方法
```bash
python 策略交集功能演示.py
```

## 演示數據
- CANSLIM量價齊升: 台積電、鴻海、聯發科、國巨、長榮
- 藏獒: 台積電、聯發科、國巨、大立光、亞德客
- 二次創高股票: 台積電、鴻海、大立光、中華電、富邦金
- 膽小貓: 台積電、中華電、富邦金、統一、中鋼

## 預期交集結果
- 三策略交集: 台積電(2330)
- CANSLIM + 藏獒: 台積電、聯發科、國巨
- 其他組合也會有不同的交集結果

## 體驗重點
1. 觀察不同策略組合的交集差異
2. 理解多策略認同股票的價值
3. 學習如何解讀交集分析報告
4. 體驗導出功能的便利性
"""
    
    with open("演示腳本說明.md", "w", encoding="utf-8") as f:
        f.write(script_info)
    
    print("📖 演示腳本說明已保存到: 演示腳本說明.md")

def main():
    """主函數"""
    print("🚀 啟動策略交集功能演示")
    print("=" * 50)
    
    # 創建演示腳本說明
    create_demo_script()
    
    # 啟動演示
    demo_intersection_functionality()

if __name__ == "__main__":
    main()
