#!/usr/bin/env python3
"""
檢查股票代碼 8249 的正確名稱
"""

import sqlite3
import os

def check_stock_8249():
    """檢查 8249 的股票名稱"""
    db_path = "D:/Finlab/history/tables/price.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查詢 8249 的所有記錄
        cursor.execute("""
            SELECT DISTINCT stock_id, stock_name, listing_status, industry, date
            FROM stock_daily_data 
            WHERE stock_id = '8249'
            ORDER BY date DESC
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        if records:
            print(f"🔍 股票代碼 8249 的資料:")
            print("=" * 60)
            for i, (stock_id, stock_name, listing_status, industry, date) in enumerate(records):
                print(f"第{i+1}筆: {stock_id} | {stock_name} | {listing_status} | {industry} | {date}")
            
            # 統計不同名稱的出現次數
            cursor.execute("""
                SELECT stock_name, COUNT(*) as count
                FROM stock_daily_data 
                WHERE stock_id = '8249'
                GROUP BY stock_name
                ORDER BY count DESC
            """)
            
            name_counts = cursor.fetchall()
            print(f"\n📊 名稱統計:")
            for name, count in name_counts:
                print(f"   {name}: {count} 筆")
                
            # 查詢最新的記錄
            cursor.execute("""
                SELECT stock_name, date
                FROM stock_daily_data 
                WHERE stock_id = '8249'
                ORDER BY date DESC
                LIMIT 1
            """)
            
            latest = cursor.fetchone()
            if latest:
                print(f"\n🎯 最新記錄: {latest[0]} ({latest[1]})")
        else:
            print("❌ 找不到股票代碼 8249 的資料")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == "__main__":
    check_stock_8249()
