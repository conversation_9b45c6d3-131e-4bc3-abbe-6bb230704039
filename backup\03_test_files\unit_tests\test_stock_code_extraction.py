#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股票代碼解析功能
"""

def extract_stock_code(stock_text):
    """從股票文字中提取股票代碼"""
    try:
        # 移除前後空白
        stock_text = stock_text.strip()
        
        # 情況1: 排行榜格式 "1. 6215 和碩 110.00 +10.00%"
        if stock_text and stock_text[0].isdigit() and '. ' in stock_text:
            # 找到序號後的第一個股票代碼
            parts = stock_text.split('. ', 1)
            if len(parts) > 1:
                remaining = parts[1].strip()
                # 取第一個空格前的部分作為股票代碼
                code_parts = remaining.split()
                if code_parts and code_parts[0].isdigit():
                    return code_parts[0]
        
        # 情況2: 一般格式 "6215 和碩" 或 "6215"
        elif ' ' in stock_text:
            parts = stock_text.split()
            if parts and parts[0].isdigit():
                return parts[0]
        
        # 情況3: 純股票代碼
        elif stock_text.isdigit():
            return stock_text
        
        # 情況4: 包含 " - " 分隔符的格式
        elif ' - ' in stock_text:
            return stock_text.split(' - ')[0].strip()
        
        return None
        
    except Exception as e:
        print(f"解析股票代碼失敗: {e}")
        return None

def test_stock_code_extraction():
    """測試股票代碼解析功能"""
    print("🔍 測試股票代碼解析功能")
    print("=" * 50)
    
    # 測試案例
    test_cases = [
        # 排行榜格式
        ("1. 6215 和碩 110.00 +10.00%", "6215"),
        ("2. 4903 聯光通 28.65 +9.98%", "4903"),
        ("3. 5227 立凱-KY 29.20 +9.98%", "5227"),
        ("10. 3494 誠研 13.40 +9.84%", "3494"),
        ("15. 4760 勤凱 136.50 +9.64%", "4760"),
        
        # 一般格式
        ("6215 和碩", "6215"),
        ("2330 台積電", "2330"),
        ("2317 鴻海", "2317"),
        
        # 純代碼
        ("6215", "6215"),
        ("2330", "2330"),
        
        # 分隔符格式
        ("6215 - 和碩", "6215"),
        ("2330 - 台積電", "2330"),
        
        # 錯誤格式
        ("", None),
        ("和碩", None),
        ("abc", None),
        ("1.", None),
    ]
    
    print("\n📋 測試結果:")
    print("-" * 50)
    
    passed = 0
    failed = 0
    
    for i, (input_text, expected) in enumerate(test_cases, 1):
        result = extract_stock_code(input_text)
        status = "✅" if result == expected else "❌"
        
        if result == expected:
            passed += 1
        else:
            failed += 1
        
        print(f"{status} 測試 {i:2d}: '{input_text}' → '{result}' (期望: '{expected}')")
    
    print("-" * 50)
    print(f"📊 測試統計: 通過 {passed} 個，失敗 {failed} 個")
    
    if failed == 0:
        print("🎉 所有測試通過！")
    else:
        print(f"⚠️  有 {failed} 個測試失敗")
    
    print("\n" + "=" * 50)
    print("✅ 股票代碼解析功能測試完成！")
    
    print("\n📋 功能說明:")
    print("🎯 **支援格式**:")
    print("• 排行榜格式：'1. 6215 和碩 110.00 +10.00%'")
    print("• 一般格式：'6215 和碩'")
    print("• 純代碼：'6215'")
    print("• 分隔符格式：'6215 - 和碩'")
    
    print("\n🔧 **解析邏輯**:")
    print("1. 檢查是否為排行榜格式（數字開頭 + '. '）")
    print("2. 提取序號後的第一個數字作為股票代碼")
    print("3. 處理其他常見格式")
    print("4. 返回純數字的股票代碼")
    
    print("\n🎯 **修正效果**:")
    print("• 原本：'1. 6215 和碩...' → '1.' (錯誤)")
    print("• 修正：'1. 6215 和碩...' → '6215' (正確)")
    print("• 現在可以正常點擊排行榜項目查看K線圖")
    
    return failed == 0

if __name__ == "__main__":
    test_stock_code_extraction()
