#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 auto_update.py 中更新後的月營收爬蟲
"""

import sys
import os
import datetime
import pandas as pd

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 導入更新後的函數
from auto_update import month_revenue_mops_direct_fixed

def test_updated_monthly_crawler():
    """測試更新後的月營收爬蟲"""
    print("🧪 測試 auto_update.py 中更新後的月營收爬蟲")
    print("=" * 60)
    
    # 測試參數: 2024年11月 (民國113年)
    test_date = datetime.date(2024, 11, 1)
    year = 113  # 民國年
    month = 11
    
    print(f"📅 測試日期: {test_date} (民國{year}年{month}月)")
    
    # 測試上市
    print(f"\n📊 測試上市...")
    sii_result = month_revenue_mops_direct_fixed('sii', year, month, test_date)
    print(f"   上市結果: {len(sii_result)} 筆資料")
    
    if len(sii_result) > 0:
        print(f"   欄位: {list(sii_result.columns)}")
        print(f"   索引類型: {type(sii_result.index)}")
        print(f"   前3筆:")
        print(sii_result.head(3))
        
        # 保存結果
        sii_result.to_csv('test_auto_update_sii.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_auto_update_sii.csv")
    
    # 延遲
    import time
    time.sleep(5)
    
    # 測試上櫃
    print(f"\n📊 測試上櫃...")
    otc_result = month_revenue_mops_direct_fixed('otc', year, month, test_date)
    print(f"   上櫃結果: {len(otc_result)} 筆資料")
    
    if len(otc_result) > 0:
        print(f"   欄位: {list(otc_result.columns)}")
        print(f"   索引類型: {type(otc_result.index)}")
        print(f"   前3筆:")
        print(otc_result.head(3))
        
        # 保存結果
        otc_result.to_csv('test_auto_update_otc.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_auto_update_otc.csv")
    
    # 合併結果
    if len(sii_result) > 0 and len(otc_result) > 0:
        print(f"\n📊 合併上市+上櫃...")
        combined_result = pd.concat([sii_result, otc_result], ignore_index=False)
        print(f"   總計: {len(combined_result)} 筆資料")
        
        # 保存最終結果
        combined_result.to_csv('test_auto_update_combined.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_auto_update_combined.csv")
        
        # 統計分析
        print(f"\n📈 統計分析:")
        if '當月營收' in combined_result.columns:
            revenue_stats = combined_result['當月營收'].describe()
            print(f"   當月營收統計:")
            print(f"     總筆數: {revenue_stats['count']:.0f}")
            print(f"     平均值: {revenue_stats['mean']:,.0f} 千元")
            print(f"     中位數: {revenue_stats['50%']:,.0f} 千元")
            print(f"     最大值: {revenue_stats['max']:,.0f} 千元")
            print(f"     最小值: {revenue_stats['min']:,.0f} 千元")
        
        print(f"\n✅ 測試成功！月營收爬蟲已成功整合到 auto_update.py")
        return True
    else:
        print(f"\n❌ 測試失敗")
        return False

if __name__ == "__main__":
    success = test_updated_monthly_crawler()
    
    if success:
        print(f"\n🎉 結論:")
        print(f"   ✅ FinLab 風格的月營收爬蟲已成功整合")
        print(f"   ✅ 支援新網站格式 (mopsov.twse.com.tw)")
        print(f"   ✅ 支援國內外公司")
        print(f"   ✅ 中文編碼正常")
        print(f"   ✅ 資料格式符合 auto_update.py 要求")
        print(f"\n💡 現在可以在 auto_update.py 中啟用月營收更新了！")
    else:
        print(f"\n❌ 需要進一步調試")
