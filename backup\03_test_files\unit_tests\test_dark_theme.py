#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試黑色主題的即時股價監控系統
驗證配色是否正確，無白底白字問題
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def test_dark_theme():
    """測試黑色主題"""
    try:
        # 導入修改後的監控系統
        from real_time_stock_monitor import RealTimeStockMonitor
        
        print("✅ 成功導入黑色主題監控系統")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建監控視窗
        monitor = RealTimeStockMonitor()
        
        # 檢查界面配色
        print("🎨 檢查界面配色...")
        
        # 顯示視窗
        monitor.show()
        print("✅ 黑色主題監控視窗已顯示")
        
        # 設置自動關閉計時器（8秒後關閉，給更多時間觀察配色）
        def close_app():
            print("🔄 配色測試完成，關閉應用程式")
            monitor.close()
            app.quit()
            
        timer = QTimer()
        timer.timeout.connect(close_app)
        timer.start(8000)  # 8秒後關閉
        
        # 顯示配色測試結果對話框
        QMessageBox.information(
            monitor,
            "黑色主題測試",
            f"🎨 即時股價監控系統 - 黑色主題測試\n\n"
            f"✅ 配色改進:\n"
            f"• 主背景: 深灰色 (#1e1e1e)\n"
            f"• 表格背景: 深灰色，交替行更深\n"
            f"• 表頭: 深灰色 (#2d2d2d)，高度統一\n"
            f"• 文字: 白色 (#ffffff)\n"
            f"• 漲跌顏色: 亮紅/亮綠，適合黑背景\n"
            f"• 控制面板: 深灰色主題\n"
            f"• 狀態列: 深灰色背景\n\n"
            f"🔍 請檢查:\n"
            f"• 是否還有白底白字問題\n"
            f"• 表頭高度是否與其他列一致\n"
            f"• 整體配色是否協調\n\n"
            f"⏰ 視窗將在8秒後自動關閉"
        )
        
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return 1
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return 1

if __name__ == "__main__":
    print("🎨 開始測試黑色主題即時股價監控系統...")
    print("=" * 50)
    
    result = test_dark_theme()
    
    print("=" * 50)
    if result == 0:
        print("✅ 黑色主題測試完成")
    else:
        print("❌ 黑色主題測試失敗")
    
    sys.exit(result)
