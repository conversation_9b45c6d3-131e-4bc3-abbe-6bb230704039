#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查爬取的股票類型分布
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def check_crawled_stocks():
    """檢查爬取的股票類型分布"""
    
    print("=" * 60)
    print("🔍 檢查爬取的股票類型分布")
    print("=" * 60)
    
    try:
        from crawler import fetch_stock_info_for_price, crawl_price
        
        # 獲取股票資訊
        print("📡 獲取股票資訊...")
        stock_info = fetch_stock_info_for_price()
        
        # 測試爬取一天的資料
        print(f"\n📅 測試爬取 2022-08-31 的資料...")
        test_date = datetime(2022, 8, 31)
        df = crawl_price(test_date)
        
        if df is not None and not df.empty:
            df_reset = df.reset_index()
            print(f"📊 爬取到 {len(df_reset)} 筆資料")
            
            # 分析股票代碼分布
            print(f"\n📊 股票代碼分布:")
            
            # 按第一個字符分類
            code_types = {}
            for _, row in df_reset.iterrows():
                stock_id = str(row['stock_id'])
                first_char = stock_id[0] if stock_id else 'Unknown'
                code_types[first_char] = code_types.get(first_char, 0) + 1
            
            for char, count in sorted(code_types.items()):
                print(f"   {char}開頭: {count} 檔")
            
            # 檢查一般股票 (1-9開頭)
            print(f"\n📋 一般股票範例 (1-9開頭):")
            general_stocks = []
            for _, row in df_reset.iterrows():
                stock_id = str(row['stock_id'])
                if stock_id[0].isdigit() and stock_id[0] != '0':
                    general_stocks.append(row)
                    if len(general_stocks) >= 10:
                        break
            
            for row in general_stocks:
                stock_id = str(row['stock_id'])
                stock_name = row.get('stock_name', '')
                listing_status = row.get('listing_status', '')
                industry = row.get('industry', '')
                close_price = row.get('收盤價', '')
                
                print(f"   {stock_id}: {stock_name} | {listing_status} | {industry} | 收盤:{close_price}")
                
                # 檢查映射
                if stock_id in stock_info:
                    expected_info = stock_info[stock_id]
                    print(f"     ✅ 映射成功: {expected_info['stock_name']} | {expected_info['listing_status']} | {expected_info['industry']}")
                else:
                    print(f"     ❌ 映射失敗: 在 stock_info 中未找到")
            
            # 檢查 ETF (0開頭)
            print(f"\n📋 ETF 範例 (0開頭):")
            etf_stocks = []
            for _, row in df_reset.iterrows():
                stock_id = str(row['stock_id'])
                if stock_id.startswith('0'):
                    etf_stocks.append(row)
                    if len(etf_stocks) >= 5:
                        break
            
            for row in etf_stocks:
                stock_id = str(row['stock_id'])
                stock_name = row.get('stock_name', '')
                listing_status = row.get('listing_status', '')
                industry = row.get('industry', '')
                close_price = row.get('收盤價', '')
                
                print(f"   {stock_id}: {stock_name} | {listing_status} | {industry} | 收盤:{close_price}")
                
                # 檢查映射
                if stock_id in stock_info:
                    expected_info = stock_info[stock_id]
                    print(f"     ✅ 映射成功: {expected_info['stock_name']} | {expected_info['listing_status']} | {expected_info['industry']}")
                else:
                    print(f"     ❌ 映射失敗: 在 stock_info 中未找到")
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_crawled_stocks()
