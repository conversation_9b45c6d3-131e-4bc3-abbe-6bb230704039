@echo off
chcp 65001 >nul
title 台股智能選股系統 - 緊急修復版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         緊急修復版啟動器
echo ========================================
echo.

echo 🔧 正在應用模組修復...
python wrapper_launcher.py

if errorlevel 1 (
    echo.
    echo ❌ 啟動失敗！
    echo.
    echo 💡 備用方案：
    echo    1. 檢查 Python 環境是否正常
    echo    2. 嘗試直接執行: dist\台股智能選股系統_修復版.exe
    echo    3. 重新安裝相關依賴
    echo.
    pause
) else (
    echo.
    echo ✅ 啟動成功！
    echo 💡 如果程式無法正常運行，請檢查系統環境
    echo.
    timeout /t 3 >nul
)
