#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試右鍵選單功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QMenu
from PyQt6.QtCore import Qt, QPoint

class TestRightClickTable(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("測試右鍵選單")
        self.setGeometry(100, 100, 600, 400)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價"])
        
        # 添加測試資料
        test_data = [
            ("2330", "台積電", "580.00"),
            ("2317", "鴻海", "105.50"),
            ("2454", "聯發科", "890.00")
        ]
        
        self.table.setRowCount(len(test_data))
        for row, (code, name, price) in enumerate(test_data):
            self.table.setItem(row, 0, QTableWidgetItem(code))
            self.table.setItem(row, 1, QTableWidgetItem(name))
            self.table.setItem(row, 2, QTableWidgetItem(price))
        
        # 設置右鍵選單
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.table)
        
        print("✅ 測試表格已創建，請在股票上按右鍵測試")
    
    def show_context_menu(self, position):
        """顯示右鍵選單"""
        print(f"🖱️ 右鍵點擊位置: {position}")
        
        # 獲取點擊的項目
        item = self.table.itemAt(position)
        if not item:
            print("❌ 沒有點擊到有效項目")
            return
        
        # 獲取股票資訊
        row = item.row()
        stock_code_item = self.table.item(row, 0)
        stock_name_item = self.table.item(row, 1)
        
        if not stock_code_item:
            print("❌ 無法獲取股票代碼")
            return
        
        stock_code = stock_code_item.text().strip()
        stock_name = stock_name_item.text().strip() if stock_name_item else "未知"
        
        print(f"📊 選中股票: {stock_code} {stock_name}")
        
        # 創建右鍵選單
        context_menu = QMenu(self)
        
        # 添加選單項目
        news_action = context_menu.addAction(f"📰 爬取 {stock_code} {stock_name} 新聞")
        news_action.triggered.connect(lambda: self.test_news_crawl(stock_code, stock_name))
        
        context_menu.addSeparator()
        
        chart_action = context_menu.addAction(f"📈 查看 {stock_code} K線圖")
        chart_action.triggered.connect(lambda: self.test_chart(stock_code, stock_name))
        
        # 顯示選單
        global_pos = self.table.mapToGlobal(position)
        print(f"🌐 全域位置: {global_pos}")
        context_menu.exec(global_pos)
    
    def test_news_crawl(self, stock_code, stock_name):
        """測試新聞爬取"""
        print(f"📰 測試新聞爬取: {stock_code} {stock_name}")
        
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(
            self,
            "新聞爬取測試",
            f"📰 {stock_code} {stock_name}\n\n"
            f"右鍵選單功能正常！\n"
            f"這裡會執行新聞爬取功能。"
        )
    
    def test_chart(self, stock_code, stock_name):
        """測試K線圖"""
        print(f"📈 測試K線圖: {stock_code} {stock_name}")
        
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(
            self,
            "K線圖測試",
            f"📈 {stock_code} {stock_name}\n\n"
            f"右鍵選單功能正常！\n"
            f"這裡會顯示K線圖。"
        )

def test_main_program_right_click():
    """測試主程式的右鍵功能"""
    print("🔍 測試主程式右鍵功能...")
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        # 檢查右鍵設定
        policy = gui.result_table.contextMenuPolicy()
        print(f"📋 右鍵政策: {policy}")
        
        if policy == Qt.ContextMenuPolicy.CustomContextMenu:
            print("✅ 右鍵政策設定正確")
        else:
            print("❌ 右鍵政策設定錯誤")
        
        # 檢查信號連接
        receivers = gui.result_table.receivers(gui.result_table.customContextMenuRequested)
        print(f"📡 信號接收器數量: {receivers}")
        
        if receivers > 0:
            print("✅ 右鍵信號已連接")
        else:
            print("❌ 右鍵信號未連接")
        
        # 檢查函數是否存在
        if hasattr(gui, 'show_stock_context_menu'):
            print("✅ show_stock_context_menu 函數存在")
        else:
            print("❌ show_stock_context_menu 函數不存在")
        
        # 添加測試資料
        gui.result_table.setRowCount(3)
        test_stocks = [
            ("2330", "台積電", "580.00"),
            ("2317", "鴻海", "105.50"),
            ("2454", "聯發科", "890.00")
        ]
        
        for row, (code, name, price) in enumerate(test_stocks):
            gui.result_table.setItem(row, 0, QTableWidgetItem(code))
            gui.result_table.setItem(row, 1, QTableWidgetItem(name))
            gui.result_table.setItem(row, 2, QTableWidgetItem(price))
        
        print("✅ 測試資料已添加")
        
        # 顯示主程式
        gui.show()
        return gui
        
    except Exception as e:
        print(f"❌ 主程式測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    print("🚀 右鍵選單調試工具")
    print("=" * 50)
    
    # 選擇測試模式
    print("選擇測試模式:")
    print("1. 簡單測試表格")
    print("2. 主程式右鍵功能")
    
    choice = input("請輸入選擇 (1 或 2): ").strip()
    
    if choice == "1":
        print("\n🔍 啟動簡單測試表格...")
        window = TestRightClickTable()
        window.show()
        
        print("💡 請在表格中的股票上按右鍵測試")
        
    elif choice == "2":
        print("\n🔍 測試主程式右鍵功能...")
        gui = test_main_program_right_click()
        
        if gui:
            print("💡 主程式已啟動，請在結果表格中的股票上按右鍵測試")
        else:
            print("❌ 主程式啟動失敗")
            sys.exit(1)
    
    else:
        print("❌ 無效選擇")
        sys.exit(1)
    
    # 運行應用程式
    sys.exit(app.exec())
