#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試數據範圍顯示功能
"""

import sys
import os
from datetime import datetime, timedelta

def test_data_range_display():
    """測試數據範圍顯示功能"""
    
    print("📅 測試數據範圍顯示功能")
    print("=" * 60)
    
    # 1. 測試策略數據時間獲取
    print("1️⃣ 測試策略數據時間獲取...")
    
    try:
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
        
        strategy = HighYieldTurtleStrategyOptimized()
        print("   ✅ 策略載入成功")
        
        # 檢查數據時間範圍
        if hasattr(strategy, 'data_date_range'):
            date_range = strategy.data_date_range
            print(f"   📊 數據時間範圍: {date_range}")
            
            min_date = date_range.get('min_date')
            max_date = date_range.get('max_date')
            last_updated = date_range.get('last_updated')
            
            print(f"   📅 最早數據: {min_date}")
            print(f"   📅 最新數據: {max_date}")
            print(f"   📅 最後更新: {last_updated}")
            
            # 計算數據年齡
            if max_date:
                try:
                    if hasattr(max_date, 'date'):
                        last_date = max_date.date()
                    else:
                        last_date = datetime.strptime(str(max_date)[:10], '%Y-%m-%d').date()
                    
                    days_old = (datetime.now().date() - last_date).days
                    years_old = days_old / 365.25
                    
                    print(f"   ⏰ 數據年齡: {days_old} 天 ({years_old:.1f} 年)")
                    
                    if days_old > 365:
                        print(f"   ⚠️ 數據已過時，建議更新")
                        color_status = "紅色 (過時)"
                    elif days_old > 30:
                        print(f"   ⚠️ 數據稍舊，可考慮更新")
                        color_status = "橙色 (稍舊)"
                    else:
                        print(f"   ✅ 數據很新")
                        color_status = "綠色 (新鮮)"
                    
                    print(f"   🎨 GUI顯示顏色: {color_status}")
                    
                except Exception as e:
                    print(f"   ❌ 計算數據年齡失敗: {e}")
            
        else:
            print("   ❌ 未找到數據時間範圍信息")
            return False
        
    except Exception as e:
        print(f"   ❌ 策略載入失敗: {e}")
        return False
    
    # 2. 測試GUI標籤格式化
    print(f"\n2️⃣ 測試GUI標籤格式化...")
    
    try:
        # 模擬GUI標籤更新邏輯
        if hasattr(strategy, 'data_date_range'):
            date_range = strategy.data_date_range
            min_date = date_range.get('min_date')
            max_date = date_range.get('max_date')
            
            # 格式化日期顯示
            if hasattr(min_date, 'strftime'):
                min_str = min_date.strftime('%Y-%m-%d')
            else:
                min_str = str(min_date)[:10]
            
            if hasattr(max_date, 'strftime'):
                max_str = max_date.strftime('%Y-%m-%d')
            else:
                max_str = str(max_date)[:10]
            
            # 生成標籤文字
            label_text = f"數據範圍: {min_str} ~ {max_str}"
            print(f"   📋 標籤文字: {label_text}")
            
            # 生成工具提示
            try:
                if hasattr(max_date, 'date'):
                    last_date = max_date.date()
                else:
                    last_date = datetime.strptime(max_str, '%Y-%m-%d').date()
                
                days_old = (datetime.now().date() - last_date).days
                
                if days_old > 365:
                    tooltip = f"⚠️ 數據已過時 {days_old} 天，建議更新數據源"
                    style = "color: #ff6b6b; font-size: 10px; font-weight: bold;"
                else:
                    tooltip = "數據時效性良好"
                    style = "color: #4CAF50; font-size: 10px;"
                
                print(f"   💡 工具提示: {tooltip}")
                print(f"   🎨 CSS樣式: {style}")
                
            except Exception as e:
                print(f"   ❌ 生成工具提示失敗: {e}")
        
    except Exception as e:
        print(f"   ❌ GUI標籤格式化測試失敗: {e}")
        return False
    
    # 3. 測試GUI代碼檢查
    print(f"\n3️⃣ 測試GUI代碼檢查...")
    
    try:
        # 檢查GUI文件中是否有數據範圍標籤
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            gui_content = f.read()
        
        # 檢查關鍵代碼
        checks = [
            ('data_range_label', '數據範圍標籤'),
            ('數據範圍:', '標籤文字'),
            ('載入中...', '載入狀態'),
            ('過時', '過時檢查'),
            ('setToolTip', '工具提示')
        ]
        
        found_features = []
        
        for keyword, description in checks:
            if keyword in gui_content:
                found_features.append(description)
                print(f"   ✅ 找到: {description}")
            else:
                print(f"   ❌ 缺少: {description}")
        
        print(f"   📊 功能完整度: {len(found_features)}/{len(checks)} ({len(found_features)/len(checks)*100:.0f}%)")
        
    except Exception as e:
        print(f"   ❌ GUI代碼檢查失敗: {e}")
        return False
    
    # 4. 模擬完整的顯示效果
    print(f"\n4️⃣ 模擬完整的顯示效果...")
    
    print(f"   📊 GUI界面預期效果:")
    print(f"   ┌─────────────────────────────────────────────────────────┐")
    print(f"   │ 計算日期: [2025-01-20] [最近交易日] 數據範圍: 2010-01-04 ~ 2022-08-24 │")
    print(f"   │ 策略: [高殖利率烏龜策略 ▼]                                    │")
    print(f"   └─────────────────────────────────────────────────────────┘")
    print(f"")
    print(f"   🎨 顏色效果:")
    if hasattr(strategy, 'data_date_range'):
        max_date = strategy.data_date_range.get('max_date')
        if max_date:
            try:
                if hasattr(max_date, 'date'):
                    last_date = max_date.date()
                else:
                    last_date = datetime.strptime(str(max_date)[:10], '%Y-%m-%d').date()
                
                days_old = (datetime.now().date() - last_date).days
                
                if days_old > 365:
                    print(f"   🔴 紅色文字 - 數據已過時 {days_old} 天")
                    print(f"   💡 滑鼠懸停顯示: ⚠️ 數據已過時 {days_old} 天，建議更新數據源")
                else:
                    print(f"   🟢 綠色文字 - 數據時效性良好")
                    print(f"   💡 滑鼠懸停顯示: 數據時效性良好")
            except:
                pass
    
    # 5. 提供使用指南
    print(f"\n5️⃣ 使用指南...")
    
    print(f"   📋 重新啟動GUI查看效果:")
    print(f"   1. 關閉當前的GUI程序")
    print(f"   2. 重新運行: python O3mh_gui_v21_optimized.py")
    print(f"   3. 查看右上角「計算日期」旁邊的數據範圍顯示")
    print(f"   4. 滑鼠懸停在數據範圍上查看詳細信息")
    print(f"   5. 選擇「高殖利率烏龜策略」並執行篩選")
    
    print(f"\n   🔧 如需更新數據:")
    print(f"   1. 運行: python update_financial_data.py")
    print(f"   2. 等待數據更新完成")
    print(f"   3. 重新啟動GUI程序")
    print(f"   4. 數據範圍標籤會顯示為綠色（新鮮數據）")
    
    print(f"\n" + "=" * 60)
    print(f"✅ 數據範圍顯示功能測試完成！")
    
    return True

if __name__ == "__main__":
    success = test_data_range_display()
    
    if success:
        print(f"\n🎉 測試結果總結:")
        print(f"✅ 數據時間獲取功能正常")
        print(f"✅ GUI標籤格式化邏輯正確")
        print(f"✅ 過時數據檢查機制有效")
        print(f"✅ 顏色和工具提示功能完整")
        print(f"")
        print(f"🚀 現在請:")
        print(f"1. 重新啟動GUI程序")
        print(f"2. 查看計算日期旁邊的數據範圍顯示")
        print(f"3. 如需更新數據，運行 update_financial_data.py")
    else:
        print(f"\n❌ 測試失敗，需要進一步檢查")
