#!/usr/bin/env python3
"""
測試策略匯入修復功能
模擬問題場景並驗證修復效果
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MockStrategyImporter:
    """模擬策略匯入器"""
    
    def __init__(self):
        self.result_table = None
        
    def _clean_stock_code(self, code: str) -> str:
        """清理股票代碼"""
        import re
        if not code:
            return ""
        
        # 移除所有非數字字符，只保留數字
        cleaned = re.sub(r'[^\d]', '', code)
        
        # 如果是4位數字，直接返回
        if len(cleaned) == 4:
            return cleaned
        
        # 嘗試從字符串中提取4位數字
        match = re.search(r'\b(\d{4})\b', code)
        if match:
            return match.group(1)
        
        return ""

    def _clean_stock_name(self, name: str) -> str:
        """清理股票名稱"""
        import re
        if not name:
            return "未知股票"
        
        # 移除問題字符，保留中文、英文、數字和基本符號
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff\(\)\-\*]', '', name)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()  # 合併多個空格
        
        if not cleaned:
            return "未知股票"
        
        return cleaned

    def _is_valid_stock_code(self, code: str) -> bool:
        """驗證股票代碼是否有效"""
        import re
        if not code:
            return False
        
        # 台股代碼必須是4位數字
        return bool(re.match(r'^\d{4}$', code.strip()))

    def get_strategy_filtered_stocks(self):
        """獲取當前策略篩選出的股票列表 - 修復版"""
        try:
            strategy_stocks = []

            # 從結果表格中獲取股票
            if self.result_table and self.result_table.rowCount() > 0:
                for row in range(self.result_table.rowCount()):
                    stock_code_item = self.result_table.item(row, 0)
                    stock_name_item = self.result_table.item(row, 1)

                    if stock_code_item and stock_name_item:
                        stock_code = stock_code_item.text().strip()
                        stock_name = stock_name_item.text().strip()
                        
                        # 修復：檢查並清理問題字符
                        stock_code = self._clean_stock_code(stock_code)
                        stock_name = self._clean_stock_name(stock_name)
                        
                        # 只添加有效的股票代碼
                        if self._is_valid_stock_code(stock_code):
                            strategy_stocks.append({
                                'code': stock_code,
                                'name': stock_name,
                                'strategy': '測試策略'
                            })
                            logging.info(f"添加股票: {stock_code} - {stock_name}")
                        else:
                            logging.warning(f"跳過無效股票代碼: '{stock_code}' (原始: '{stock_code_item.text()}')")

            logging.info(f"從策略結果中提取到 {len(strategy_stocks)} 支有效股票")
            return strategy_stocks

        except Exception as e:
            logging.error(f"獲取策略篩選股票失敗: {e}")
            return []

def create_problem_table():
    """創建有問題的表格數據（模擬您遇到的問題）"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    table = QTableWidget(5, 3)
    table.setHorizontalHeaderLabels(['股票代碼', '股票名稱', '推薦度'])
    
    # 模擬問題數據
    problem_data = [
        ('、、、2330、、、', '、、、台積電、、、', '73.45%'),
        ('、、、2317、、、', '、、、鴻海、、、', '68.20%'),
        ('、、、、、、', '、、、、、、', '、、、'),
        ('6218、、、', '、、、青蛙撞奶、、、', '65.30%'),
        ('8277', '商丞、、、、、', '62.10%'),
    ]
    
    for row, (code, name, rate) in enumerate(problem_data):
        table.setItem(row, 0, QTableWidgetItem(code))
        table.setItem(row, 1, QTableWidgetItem(name))
        table.setItem(row, 2, QTableWidgetItem(rate))
    
    return table

def create_normal_table():
    """創建正常的表格數據"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    table = QTableWidget(3, 3)
    table.setHorizontalHeaderLabels(['股票代碼', '股票名稱', '推薦度'])
    
    # 正常數據
    normal_data = [
        ('2330', '台積電', '73.45%'),
        ('2317', '鴻海', '68.20%'),
        ('2454', '聯發科', '65.30%'),
    ]
    
    for row, (code, name, rate) in enumerate(normal_data):
        table.setItem(row, 0, QTableWidgetItem(code))
        table.setItem(row, 1, QTableWidgetItem(name))
        table.setItem(row, 2, QTableWidgetItem(rate))
    
    return table

def test_problem_table():
    """測試問題表格的修復效果"""
    print("🧪 測試1: 問題表格修復")
    print("=" * 60)
    
    # 創建問題表格
    problem_table = create_problem_table()
    
    # 創建模擬匯入器
    importer = MockStrategyImporter()
    importer.result_table = problem_table
    
    print("📊 原始問題數據:")
    for row in range(problem_table.rowCount()):
        code_item = problem_table.item(row, 0)
        name_item = problem_table.item(row, 1)
        code = code_item.text() if code_item else "None"
        name = name_item.text() if name_item else "None"
        print(f"  第{row}行: '{code}' - '{name}'")
    
    # 測試修復功能
    print(f"\n🔧 執行修復...")
    strategy_stocks = importer.get_strategy_filtered_stocks()
    
    print(f"\n✅ 修復結果:")
    print(f"  • 提取到 {len(strategy_stocks)} 支有效股票")
    for stock in strategy_stocks:
        print(f"  • {stock['code']} - {stock['name']}")
    
    return len(strategy_stocks)

def test_normal_table():
    """測試正常表格"""
    print("\n🧪 測試2: 正常表格處理")
    print("=" * 60)
    
    # 創建正常表格
    normal_table = create_normal_table()
    
    # 創建模擬匯入器
    importer = MockStrategyImporter()
    importer.result_table = normal_table
    
    print("📊 正常數據:")
    for row in range(normal_table.rowCount()):
        code_item = normal_table.item(row, 0)
        name_item = normal_table.item(row, 1)
        code = code_item.text() if code_item else "None"
        name = name_item.text() if name_item else "None"
        print(f"  第{row}行: '{code}' - '{name}'")
    
    # 測試處理功能
    print(f"\n🔧 執行處理...")
    strategy_stocks = importer.get_strategy_filtered_stocks()
    
    print(f"\n✅ 處理結果:")
    print(f"  • 提取到 {len(strategy_stocks)} 支有效股票")
    for stock in strategy_stocks:
        print(f"  • {stock['code']} - {stock['name']}")
    
    return len(strategy_stocks)

def test_edge_cases():
    """測試邊界情況"""
    print("\n🧪 測試3: 邊界情況")
    print("=" * 60)
    
    importer = MockStrategyImporter()
    
    # 測試各種問題字符
    test_cases = [
        ('、、、2330、、、', '應該提取到2330'),
        ('abc2317def', '應該提取到2317'),
        ('、、、、、、', '應該返回空'),
        ('12345', '5位數字，應該返回空'),
        ('123', '3位數字，應該返回空'),
        ('', '空字符串，應該返回空'),
        ('2454', '正常代碼，應該返回2454'),
    ]
    
    print("🔍 股票代碼清理測試:")
    for test_input, expected in test_cases:
        result = importer._clean_stock_code(test_input)
        is_valid = importer._is_valid_stock_code(result)
        status = "✅" if is_valid else "❌"
        print(f"  {status} '{test_input}' -> '{result}' ({expected})")
    
    # 測試股票名稱清理
    name_test_cases = [
        ('、、、台積電、、、', '台積電'),
        ('鴻海、、、、、', '鴻海'),
        ('、、、、、、', '未知股票'),
        ('', '未知股票'),
        ('聯發科', '聯發科'),
    ]
    
    print(f"\n🔍 股票名稱清理測試:")
    for test_input, expected in name_test_cases:
        result = importer._clean_stock_name(test_input)
        print(f"  • '{test_input}' -> '{result}' (期望: {expected})")

def main():
    """主測試函數"""
    print("🚀 策略匯入修復功能測試")
    print("=" * 80)
    
    try:
        # 初始化Qt應用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 執行測試
        problem_count = test_problem_table()
        normal_count = test_normal_table()
        test_edge_cases()
        
        # 總結
        print("\n" + "=" * 80)
        print("📊 測試總結")
        print("=" * 80)
        print(f"✅ 問題表格修復: 提取到 {problem_count} 支股票")
        print(f"✅ 正常表格處理: 提取到 {normal_count} 支股票")
        print(f"✅ 邊界情況測試: 完成")
        
        if problem_count > 0:
            print(f"\n🎉 修復功能正常！能夠從問題數據中提取有效股票。")
        else:
            print(f"\n⚠️ 修復功能可能有問題，無法從問題數據中提取股票。")
        
        print(f"\n💡 建議:")
        print(f"  1. 將修復後的代碼部署到主程式")
        print(f"  2. 重新測試策略匯入功能")
        print(f"  3. 檢查日誌輸出以確認修復效果")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
