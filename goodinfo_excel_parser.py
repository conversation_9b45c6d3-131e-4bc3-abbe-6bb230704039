#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoodInfo Excel 月營收數據解析器
專門處理從 GoodInfo 下載的 Excel 文件
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime
import logging

class GoodInfoExcelParser:
    """GoodInfo Excel 月營收數據解析器"""
    
    def __init__(self, db_path="db/monthly_revenue.db"):
        self.db_path = db_path
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化數據庫
        self.init_database()
        
    def init_database(self):
        """初始化數據庫"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,                    -- 單月營收(億)
                    revenue_mom REAL,               -- 月增率(%)
                    revenue_yoy REAL,               -- 年增率(%)
                    cumulative_revenue REAL,        -- 累計營收(億)
                    cumulative_yoy REAL,            -- 累計年增率(%)
                    
                    -- 股價資訊
                    open_price REAL,                -- 開盤價
                    close_price REAL,               -- 收盤價
                    high_price REAL,                -- 最高價
                    low_price REAL,                 -- 最低價
                    price_change REAL,              -- 漲跌(元)
                    price_change_pct REAL,          -- 漲跌(%)
                    
                    data_source TEXT DEFAULT 'goodinfo_excel',
                    crawl_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_year_month ON monthly_revenue(stock_id, year, month)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
    
    def create_sample_excel(self, filename="sample_2330_revenue.xlsx"):
        """創建示例 Excel 文件用於測試"""
        try:
            # 基於您提供的實際數據創建示例
            sample_data = {
                '月別': ['Jun-25', 'May-25', 'Apr-25', 'Mar-25', 'Feb-25', 'Jan-25'],
                '開盤': [958, 938, 929, 1000, 1065, 1070],
                '收盤': [1060, 967, 908, 910, 1040, 1135],
                '最高': [1080, 1000, 952, 1030, 1125, 1160],
                '最低': [946, 911, 780, 910, 1040, 1055],
                '漲跌(元)': [93, 59, -2, -130, -95, 60],
                '漲跌(%)': [9.62, 6.5, -0.22, -12.5, -8.37, 5.58],
                '單月營收(億)': [2637, 3205, 3496, 2860, 2600, 2933],
                '月增(%)': [-17.7, -8.31, 22.2, 9.97, -11.3, 5.43],
                '年增(%)': [26.9, 39.6, 48.1, 46.5, 43.1, 35.9],
                '累計營收(億)': [17730, 15093, 11888, 8393, 5533, 2933],
                '累計年增(%)': [40, 42.6, 43.5, 41.6, 39.2, 35.9]
            }
            
            df = pd.DataFrame(sample_data)
            df.to_excel(filename, index=False)
            
            self.logger.info(f"✅ 創建示例 Excel 文件: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"❌ 創建示例文件失敗: {e}")
            return None
    
    def parse_excel_file(self, excel_file, stock_id):
        """解析 Excel 文件"""
        try:
            self.logger.info(f"📊 解析 Excel 文件: {excel_file}")
            
            # 嘗試不同的讀取方式
            df = None
            
            # 方法1: 直接讀取
            try:
                df = pd.read_excel(excel_file)
                self.logger.info("✅ 使用默認方式讀取 Excel")
            except:
                pass
            
            # 方法2: 跳過標題行
            if df is None or df.empty:
                try:
                    df = pd.read_excel(excel_file, header=2)
                    self.logger.info("✅ 跳過前2行讀取 Excel")
                except:
                    pass
            
            # 方法3: 手動指定列名
            if df is None or df.empty:
                try:
                    df = pd.read_excel(excel_file, header=None)
                    # 手動設置列名
                    expected_columns = ['月別', '開盤', '收盤', '最高', '最低', '漲跌(元)', '漲跌(%)', 
                                      '單月營收(億)', '月增(%)', '年增(%)', '累計營收(億)', '累計年增(%)']
                    if len(df.columns) >= len(expected_columns):
                        df.columns = expected_columns + [f'col_{i}' for i in range(len(expected_columns), len(df.columns))]
                    self.logger.info("✅ 使用手動列名讀取 Excel")
                except:
                    pass
            
            if df is None or df.empty:
                self.logger.error("❌ 無法讀取 Excel 文件")
                return None
            
            self.logger.info(f"📋 Excel 文件包含 {len(df)} 行數據")
            self.logger.info(f"📋 列名: {list(df.columns)}")
            
            # 獲取股票名稱
            stock_name = self.get_stock_name(stock_id)
            
            revenue_records = []
            
            for index, row in df.iterrows():
                try:
                    # 解析月別
                    month_str = str(row.iloc[0]).strip()
                    if pd.isna(month_str) or month_str == 'nan' or month_str == '':
                        continue
                    
                    year, month = self.parse_month_string(month_str)
                    if not year or not month:
                        self.logger.warning(f"⚠️ 無法解析月份: {month_str}")
                        continue
                    
                    # 解析數據
                    record = {
                        'stock_id': stock_id,
                        'stock_name': stock_name,
                        'year': year,
                        'month': month,
                        
                        # 股價資訊
                        'open_price': self.safe_float(row.iloc[1]),      # 開盤
                        'close_price': self.safe_float(row.iloc[2]),     # 收盤
                        'high_price': self.safe_float(row.iloc[3]),      # 最高
                        'low_price': self.safe_float(row.iloc[4]),       # 最低
                        'price_change': self.safe_float(row.iloc[5]),    # 漲跌(元)
                        'price_change_pct': self.safe_float(row.iloc[6]), # 漲跌(%)
                        
                        # 營收資訊
                        'revenue': self.safe_float(row.iloc[7]),         # 單月營收(億)
                        'revenue_mom': self.safe_float(row.iloc[8]),     # 月增(%)
                        'revenue_yoy': self.safe_float(row.iloc[9]),     # 年增(%)
                        'cumulative_revenue': self.safe_float(row.iloc[10]), # 累計營收(億)
                        'cumulative_yoy': self.safe_float(row.iloc[11]), # 累計年增(%)
                        
                        'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    revenue_records.append(record)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue
            
            self.logger.info(f"✅ 成功解析 {len(revenue_records)} 筆月營收數據")
            return revenue_records
            
        except Exception as e:
            self.logger.error(f"❌ 解析 Excel 文件失敗: {e}")
            return None
    
    def parse_month_string(self, month_str):
        """解析月份字符串"""
        try:
            # 處理 "Jun-25" 格式
            if '-' in month_str:
                month_part, year_part = month_str.split('-')
                
                # 月份對照表
                month_mapping = {
                    'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                    'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                }
                
                month = month_mapping.get(month_part)
                if not month:
                    return None, None
                
                year = int(year_part)
                # 處理兩位數年份
                if year < 50:
                    year += 2000
                elif year < 100:
                    year += 1900
                
                return year, month
            
            return None, None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 解析月份失敗: {month_str}, {e}")
            return None, None
    
    def safe_float(self, value):
        """安全轉換為浮點數"""
        try:
            if pd.isna(value) or value == '' or str(value).strip() == '':
                return None
            
            # 移除逗號和其他字符
            cleaned = str(value).replace(',', '').strip()
            return float(cleaned)
            
        except (ValueError, TypeError):
            return None
    
    def get_stock_name(self, stock_id):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
            '2412': '中華電', '2881': '富邦金', '2882': '國泰金'
        }
        return stock_names.get(stock_id, f'股票{stock_id}')
    
    def save_to_database(self, revenue_data):
        """儲存數據到數據庫"""
        try:
            if not revenue_data:
                return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            for record in revenue_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue 
                        (stock_id, stock_name, year, month, revenue, revenue_mom, 
                         revenue_yoy, cumulative_revenue, cumulative_yoy,
                         open_price, close_price, high_price, low_price, 
                         price_change, price_change_pct, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_id'], record['stock_name'], record['year'], 
                        record['month'], record['revenue'], record['revenue_mom'],
                        record['revenue_yoy'], record['cumulative_revenue'], 
                        record['cumulative_yoy'], record['open_price'],
                        record['close_price'], record['high_price'], record['low_price'],
                        record['price_change'], record['price_change_pct'], record['crawl_date']
                    ))
                    saved_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 儲存記錄失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 成功儲存 {saved_count} 筆數據到數據庫")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 儲存數據庫失敗: {e}")
            return False
    
    def query_revenue_data(self, stock_id=None, year=None, limit=10):
        """查詢月營收數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT * FROM monthly_revenue WHERE 1=1"
            params = []
            
            if stock_id:
                query += " AND stock_id = ?"
                params.append(stock_id)
            
            if year:
                query += " AND year = ?"
                params.append(year)
            
            query += " ORDER BY year DESC, month DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # 獲取列名
            columns = [description[0] for description in cursor.description]
            
            conn.close()
            
            # 轉換為字典列表
            data = []
            for row in results:
                data.append(dict(zip(columns, row)))
            
            return data
            
        except Exception as e:
            self.logger.error(f"❌ 查詢數據失敗: {e}")
            return []

def test_excel_parser():
    """測試 Excel 解析器"""
    print("🚀 測試 GoodInfo Excel 解析器")
    print("=" * 60)
    
    parser = GoodInfoExcelParser()
    
    # 創建示例 Excel 文件
    print("\n📊 創建示例 Excel 文件...")
    sample_file = parser.create_sample_excel()
    
    if sample_file:
        print(f"✅ 示例文件創建成功: {sample_file}")
        
        # 解析 Excel 文件
        print(f"\n📋 解析 Excel 文件...")
        revenue_data = parser.parse_excel_file(sample_file, '2330')
        
        if revenue_data:
            print(f"✅ 成功解析 {len(revenue_data)} 筆數據")
            
            # 顯示前3筆數據
            print("\n📋 前3筆數據:")
            for i, record in enumerate(revenue_data[:3], 1):
                print(f"{i}. {record['year']}/{record['month']:02d} - "
                      f"營收: {record['revenue']:.0f}億, "
                      f"年增率: {record['revenue_yoy']:.1f}%, "
                      f"收盤價: {record['close_price']:.0f}元")
            
            # 儲存到數據庫
            if parser.save_to_database(revenue_data):
                print("✅ 數據已儲存到數據庫")
                
                # 查詢驗證
                print("\n🔍 查詢驗證...")
                query_results = parser.query_revenue_data(stock_id='2330', limit=3)
                
                if query_results:
                    print(f"✅ 查詢到 {len(query_results)} 筆數據")
                    for result in query_results:
                        print(f"   {result['year']}/{result['month']:02d} - "
                              f"營收: {result['revenue']:.0f}億")
                else:
                    print("❌ 查詢失敗")
            
            # 清理示例文件
            try:
                os.remove(sample_file)
                print("🗑️ 清理示例文件")
            except:
                pass
            
            return True
        else:
            print("❌ 解析失敗")
            return False
    else:
        print("❌ 示例文件創建失敗")
        return False

if __name__ == "__main__":
    test_excel_parser()
