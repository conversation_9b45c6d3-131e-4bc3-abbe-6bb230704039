#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新聞顯示功能
"""

import sys
from PyQt6.QtWidgets import QApplication

def test_news_display():
    """測試新聞顯示功能"""
    print("🚀 測試新聞顯示功能")
    print("=" * 50)
    
    try:
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建主視窗實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 創建模擬新聞資料
        mock_news_data = [
            {
                'date': '20250712',
                'time': '143000',
                'source': 'cnyes',
                'title': '台積電Q2財報超預期，AI需求強勁推動營收成長',
                'link': 'https://news.cnyes.com/news/id/test1'
            },
            {
                'date': '20250712',
                'time': '120000',
                'source': 'cnyes',
                'title': '台積電先進製程產能滿載，下半年展望樂觀',
                'link': 'https://news.cnyes.com/news/id/test2'
            },
            {
                'date': '20250711',
                'time': '160000',
                'source': 'cnyes',
                'title': '台積電3奈米製程技術領先，獲蘋果大單',
                'link': 'https://news.cnyes.com/news/id/test3'
            },
            {
                'date': '20250710',
                'time': '140000',
                'source': 'cnyes',
                'title': '台積電ADR上漲2.5%，市場看好AI晶片需求',
                'link': 'https://news.cnyes.com/news/id/test4'
            },
            {
                'date': '20250709',
                'time': '100000',
                'source': 'cnyes',
                'title': '台積電董事長：2024年將是AI元年',
                'link': 'https://news.cnyes.com/news/id/test5'
            }
        ]
        
        print(f"📰 模擬新聞資料: {len(mock_news_data)} 筆")
        
        # 顯示新聞資料內容
        for i, news in enumerate(mock_news_data):
            print(f"  {i+1}. 日期: {news['date']}, 標題: {news['title'][:30]}...")
        
        # 測試新聞顯示視窗
        print("\n🔍 測試新聞顯示視窗...")
        gui.show_stock_news_results("2330", "台積電", mock_news_data)
        
        print("✅ 新聞顯示視窗已開啟")
        print("💡 請檢查視窗是否有：")
        print("  • 正確的視窗標題")
        print("  • 最大化、最小化、關閉按鈕")
        print("  • 完整的新聞標題顯示")
        print("  • 正確的日期時間格式")
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_cnyes_crawler():
    """測試鉅亨網爬蟲資料格式"""
    print("\n🔍 測試鉅亨網爬蟲資料格式")
    print("-" * 40)
    
    try:
        from news_crawler_cnyes_only import CnyesNewsCrawler
        crawler = CnyesNewsCrawler()
        
        print("🚀 執行快速搜尋...")
        news_list = crawler.quick_search_cnyes("2330", days=3)
        
        print(f"📊 找到 {len(news_list)} 筆新聞")
        
        if news_list:
            print("\n📰 新聞資料格式檢查:")
            for i, news in enumerate(news_list[:3]):  # 只檢查前3筆
                print(f"  第{i+1}筆:")
                print(f"    日期: '{news.get('date', 'N/A')}'")
                print(f"    時間: '{news.get('time', 'N/A')}'")
                print(f"    來源: '{news.get('source', 'N/A')}'")
                print(f"    標題: '{news.get('title', 'N/A')}'")
                print(f"    連結: '{news.get('link', 'N/A')[:50]}...'")
                print()
            
            # 檢查是否有空標題
            empty_titles = [i for i, news in enumerate(news_list) if not news.get('title', '').strip()]
            if empty_titles:
                print(f"⚠️ 發現 {len(empty_titles)} 筆新聞沒有標題")
                print(f"   位置: {empty_titles[:5]}...")  # 只顯示前5個
            else:
                print("✅ 所有新聞都有標題")
        else:
            print("⚠️ 沒有找到新聞資料")
            
    except Exception as e:
        print(f"❌ 爬蟲測試失敗: {e}")

if __name__ == "__main__":
    print("🔍 新聞顯示功能測試")
    print(f"⏰ 測試時間: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 先測試爬蟲資料格式
    test_cnyes_crawler()
    
    # 再測試顯示功能
    print("\n" + "=" * 50)
    result = test_news_display()
    
    sys.exit(result)
