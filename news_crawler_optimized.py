#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
優化版新聞爬蟲 - 減少系統負擔
支援鉅亨網和經濟日報
"""

from news_crawler_base import NewsCrawlerBase, NewsCrawlerException
from datetime import datetime
import time
from random import randint
import requests
from bs4 import BeautifulSoup
import json
import re
import ssl
import urllib3
import threading
import gc

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

class OptimizedNewsCrawler(NewsCrawlerBase):
    """優化版新聞爬蟲 - 減少系統負擔"""
    
    def __init__(self, db_path=None):
        super().__init__(db_path)
        
        # 優化設定 - 進一步減少系統負擔
        self.max_news_per_batch = 10  # 減少到每批最多10篇新聞
        self.delay_between_requests = 3.0  # 增加延遲到3秒
        self.timeout = 10  # 減少超時時間到10秒
        self.max_content_requests = 5  # 最多只獲取5篇新聞的完整內容
        
        # 設置請求標頭
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }

    def extract(self, ndaysAgo, interval: str, stock_code: str = None) -> list:
        """
        提取新聞資料 - 優化版
        
        Args:
            ndaysAgo: n天前的datetime
            interval: 時間間隔
            stock_code: 股票代碼
            
        Returns:
            list: 新聞資料列表
        """
        try:
            self.logger.info("🚀 開始優化版新聞爬取...")
            
            # 優先使用鉅亨網API（最快最穩定）
            anue_news = self._crawl_anue_optimized(ndaysAgo, interval, stock_code)
            
            if anue_news and len(anue_news) >= 10:
                self.logger.info(f"✅ 鉅亨網獲取足夠新聞: {len(anue_news)} 篇")
                return anue_news
            
            # 如果鉅亨網新聞不足，補充經濟日報
            self.logger.info("📰 補充經濟日報新聞...")
            edn_news = self._crawl_economic_daily(stock_code)
            
            # 合併結果
            all_news = anue_news + edn_news
            
            # 限制總數量以減少系統負擔
            if len(all_news) > self.max_news_per_batch:
                all_news = all_news[:self.max_news_per_batch]
                self.logger.info(f"⚡ 限制新聞數量至 {self.max_news_per_batch} 篇以減少系統負擔")
            
            return all_news
            
        except Exception as e:
            self.logger.error(f"❌ 新聞提取失敗: {e}")
            raise NewsCrawlerException(f"優化版新聞爬取失敗: {e}")

    def _crawl_anue_optimized(self, ndaysAgo, interval: str, stock_code: str = None):
        """優化版鉅亨網爬蟲"""
        try:
            # 計算時間戳
            start = f'{ndaysAgo.year}/{ndaysAgo.month}/{ndaysAgo.day} {interval[0:2]}:{interval[2:4]}:00'
            struct_time = time.strptime(start, "%Y/%m/%d %H:%M:%S")
            start_stamp = int(time.mktime(struct_time))
            
            ended = f'{self.timeNow.year}/{self.timeNow.month}/{self.timeNow.day} {interval[0:2]}:{interval[2:4]}:00'
            struct_time = time.strptime(ended, "%Y/%m/%d %H:%M:%S")
            ended_stamp = int(time.mktime(struct_time))
            
            # 只爬取第一頁，減少系統負擔
            url = f'https://api.cnyes.com/media/api/v1/newslist/category/tw_stock?startAt={start_stamp}&endAt={ended_stamp}&limit=15&page=1'
            
            self.logger.info(f"🔍 鉅亨網API: {url}")
            
            response = requests.get(url, headers=self.headers, verify=False, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if not data.get('items', {}).get('data'):
                return []
            
            news_items = data['items']['data']
            self.logger.info(f"📋 鉅亨網獲取 {len(news_items)} 個新聞連結")
            
            # 處理新聞內容（大幅限制數量以減少系統負擔）
            result = []
            for i, item in enumerate(news_items[:self.max_content_requests]):  # 只處理前5篇
                try:
                    self.logger.info(f"📖 處理鉅亨網新聞 {i+1}/10")
                    
                    newsDateTime = datetime.fromtimestamp(item['publishAt'])
                    newsDate = newsDateTime.strftime("%Y%m%d")
                    newsTime = newsDateTime.strftime("%H%M%S")
                    link = f"https://news.cnyes.com/news/id/{item['newsId']}?exp=a"
                    newsID = f'anue_{item["newsId"]}'
                    
                    # 獲取新聞內容
                    content_response = requests.get(link, headers=self.headers, verify=False, timeout=self.timeout)
                    
                    if content_response.status_code == 200:
                        soup = BeautifulSoup(content_response.content.decode('utf-8'), 'html.parser')
                        
                        # 提取內容
                        contents = soup.select("div._1UuP")
                        contentStr = " ".join([content.text.strip() for content in contents])
                        
                        # 檢測股票代碼
                        detected_stock_code = None
                        if stock_code:
                            if stock_code.upper() in f"{item['title']} {contentStr}".upper():
                                detected_stock_code = stock_code
                        else:
                            stock_pattern = re.compile(r'[0-9]{4}')
                            matches = stock_pattern.findall(f"{item['title']} {contentStr}")
                            if matches:
                                detected_stock_code = matches[0]
                        
                        result.append((
                            newsID, newsDate, newsTime, item['title'], link, 
                            "", contentStr, "", detected_stock_code
                        ))
                    
                    # 增加延遲減少系統負擔
                    time.sleep(self.delay_between_requests)
                    
                    # 強制垃圾回收
                    if i % 5 == 0:
                        gc.collect()
                    
                except Exception as e:
                    self.logger.warning(f"處理鉅亨網新聞失敗: {e}")
                    continue
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 鉅亨網爬取失敗: {e}")
            return []

    def _crawl_economic_daily(self, stock_code: str = None):
        """爬取經濟日報新聞"""
        try:
            url = "https://money.udn.com/money/cate/5590"
            
            self.logger.info(f"🔍 經濟日報: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取新聞連結
            news_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '/story/' in href and 'money.udn.com' in href:
                    title = link.text.strip()
                    if title and len(title) > 10:  # 過濾太短的標題
                        news_links.append((href, title))
            
            # 限制數量
            news_links = news_links[:5]  # 只取前5篇
            
            result = []
            for i, (link, title) in enumerate(news_links):
                try:
                    self.logger.info(f"📖 處理經濟日報新聞 {i+1}/5")
                    
                    # 獲取新聞內容
                    content_response = requests.get(link, headers=self.headers, timeout=self.timeout)
                    
                    if content_response.status_code == 200:
                        content_soup = BeautifulSoup(content_response.text, 'html.parser')
                        
                        # 提取內容
                        content_divs = content_soup.find_all('p')
                        contentStr = " ".join([p.text.strip() for p in content_divs[:10]])  # 只取前10段
                        
                        # 生成新聞ID
                        newsID = f'edn_{int(time.time())}_{i}'
                        newsDate = datetime.now().strftime("%Y%m%d")
                        newsTime = datetime.now().strftime("%H%M%S")
                        
                        # 檢測股票代碼
                        detected_stock_code = None
                        if stock_code:
                            if stock_code.upper() in f"{title} {contentStr}".upper():
                                detected_stock_code = stock_code
                        else:
                            stock_pattern = re.compile(r'[0-9]{4}')
                            matches = stock_pattern.findall(f"{title} {contentStr}")
                            if matches:
                                detected_stock_code = matches[0]
                        
                        result.append((
                            newsID, newsDate, newsTime, title, link,
                            "", contentStr, "", detected_stock_code
                        ))
                    
                    # 增加延遲
                    time.sleep(self.delay_between_requests)
                    
                except Exception as e:
                    self.logger.warning(f"處理經濟日報新聞失敗: {e}")
                    continue
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 經濟日報爬取失敗: {e}")
            return []

    def transform(self, rawDatas):
        """轉換資料格式"""
        # 新聞內容: (news_id, date, time, source, title, reporter, link, content, stock_code)
        cleanDatas = []
        cleanTags = []
        
        for rawData in rawDatas:
            # 判斷來源
            source = 'anue' if rawData[0].startswith('anue_') else 'economic_daily'
            
            cleanDatas.append((
                rawData[0], rawData[1], rawData[2], source, rawData[3], 
                rawData[5], rawData[4], rawData[6], rawData[8]
            ))
            
            # 標籤處理（如果有的話）
            if rawData[7]:
                cleanTags.append((source, rawData[0], rawData[7]))
        
        return iter(cleanDatas), iter(cleanTags)

if __name__ == '__main__':
    # 測試優化版爬蟲
    crawler = OptimizedNewsCrawler()
    
    print("🚀 測試優化版新聞爬蟲")
    print("=" * 50)
    
    # 爬取最近1天的新聞
    result = crawler.run(ndays=1, interval="0600")
    print(f"📊 爬取結果: {result}")
    
    # 測試特定股票
    print("\n🎯 測試台積電新聞...")
    tsmc_result = crawler.run(ndays=1, stock_code="2330")
    print(f"📊 台積電結果: {tsmc_result}")
