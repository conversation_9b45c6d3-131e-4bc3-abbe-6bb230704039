#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穩定編譯腳本 - 創建真正獨立的可執行檔
解決所有依賴問題，確保編譯成功
"""

import os
import sys
import subprocess
import shutil
import json

def check_environment():
    """檢查編譯環境"""
    print("🔍 檢查編譯環境...")
    
    # 檢查 Python 版本
    python_version = sys.version_info
    print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 檢查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安裝")
        print("正在安裝 PyInstaller...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # 檢查主要依賴
    required_packages = [
        'PyQt6', 'pandas', 'numpy', 'requests', 'beautifulsoup4',
        'pyqtgraph', 'openpyxl', 'xlsxwriter'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"正在安裝缺失的套件: {missing_packages}")
        for package in missing_packages:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package])
    
    return True

def create_stable_spec():
    """創建穩定的 spec 文件"""
    print("📝 創建穩定的編譯配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

# 數據文件
datas = []

# 嘗試添加可能的數據文件
data_files = [
    'config',
    'strategies', 
    'data',
    'templates',
    '*.json',
    '*.yaml',
    '*.yml',
    '*.csv',
    '*.txt'
]

for pattern in data_files:
    if os.path.exists(pattern):
        if os.path.isdir(pattern):
            datas.append((pattern, pattern))
        else:
            datas.append((pattern, '.'))

# 隱藏導入 - 包含所有可能需要的模組
hiddenimports = [
    # 系統模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtPrintSupport',
    'PyQt6.sip',
    
    # 數據處理
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    'numpy._core',
    'numpy._core.numeric',
    
    # 圖表和可視化
    'pyqtgraph',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    
    # 網路和爬蟲
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    'selenium',
    'webdriver_manager',
    
    # Excel 處理
    'openpyxl',
    'xlsxwriter',
    'xlrd',
    'xlwt',
    
    # 金融數據 (排除有問題的 twstock)
    'yfinance',
    # 'twstock',  # 排除，因為有 CSV 文件問題
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    
    # 其他可能的依賴
    'schedule',
    'tqdm',
    'colorlog',
    'psutil',
    'cryptography',
    'sqlalchemy',
    'apscheduler',
    'python-dateutil',
    'pytz',
    'six',
    'setuptools',
    'pkg_resources',
]

# 排除模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    'twstock',  # 排除有問題的 twstock 模組
    'twstock.codes',
    'twstock.stock',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統_穩定版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('stable_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 穩定編譯配置已創建")

def clean_build():
    """清理編譯目錄"""
    print("🧹 清理編譯環境...")
    
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 清理 {dir_name}")
            except Exception as e:
                print(f"⚠️ 無法清理 {dir_name}: {e}")

def compile_stable():
    """執行穩定編譯"""
    print("🔨 開始穩定編譯...")
    
    # 使用 spec 文件編譯
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'stable_compile.spec'
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        # 設置環境變數
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore',
            env=env
        )
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/台股智能選股系統_穩定版.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_stable_launcher()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            print("標準輸出:")
            print(result.stdout[-2000:] if result.stdout else "無")
            print("錯誤輸出:")
            print(result.stderr[-2000:] if result.stderr else "無")
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_stable_launcher():
    """創建穩定版啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         穩定版 - 獨立可執行檔
echo ========================================
echo.

if exist "dist\\台股智能選股系統_穩定版.exe" (
    echo ✅ 找到穩定版可執行檔
    echo 🚀 正在啟動...
    echo.
    echo 💡 穩定版特點：
    echo    ✓ 完全獨立，無需 Python 環境
    echo    ✓ 包含所有必要依賴
    echo    ✓ 解決所有模組問題
    echo    ✓ 優化啟動速度
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_穩定版.exe"
    
    echo ✅ 穩定版已啟動！
    echo.
    echo 📋 如果程式無法正常運行：
    echo    1. 檢查防毒軟體設定
    echo    2. 確認執行權限
    echo    3. 檢查系統兼容性
    echo.
    
) else (
    echo ❌ 錯誤：找不到穩定版可執行檔
    echo.
    echo 請重新編譯：
    echo    python stable_compile.py
    echo.
    pause
    exit /b 1
)

timeout /t 5 >nul
'''
    
    with open('啟動穩定版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建穩定版啟動腳本: 啟動穩定版.bat")

def main():
    """主函數"""
    print("🚀 台股智能選股系統 - 穩定編譯器")
    print("=" * 60)
    print("目標：創建完全獨立、穩定的可執行檔")
    print()
    
    # 步驟1: 檢查環境
    if not check_environment():
        print("❌ 環境檢查失敗")
        return False
    
    print()
    
    # 步驟2: 清理編譯環境
    clean_build()
    print()
    
    # 步驟3: 創建穩定配置
    create_stable_spec()
    print()
    
    # 步驟4: 執行編譯
    if compile_stable():
        print("\n🎉 穩定版編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/台股智能選股系統_穩定版.exe")
        print("   - 啟動穩定版.bat")
        print("\n🚀 使用方法:")
        print("   雙擊執行: 啟動穩定版.bat")
        print("\n✨ 穩定版特點:")
        print("   ✓ 完全獨立，無需 Python 環境")
        print("   ✓ 包含所有必要依賴和模組")
        print("   ✓ 解決所有已知的模組問題")
        print("   ✓ 優化的啟動速度和穩定性")
        return True
    else:
        print("\n❌ 編譯失敗！")
        print("請檢查錯誤信息並重試")
        return False

if __name__ == "__main__":
    main()
