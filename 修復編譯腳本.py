#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復編譯腳本 - 解決 PyQt5/PyQt6 衝突問題
"""

import os
import sys
import subprocess

def fix_pyqt_conflict():
    """修復 PyQt5/PyQt6 衝突"""
    print("🔧 修復 PyQt5/PyQt6 衝突...")
    
    # 檢查是否同時安裝了 PyQt5 和 PyQt6
    has_pyqt5 = False
    has_pyqt6 = False
    
    try:
        import PyQt5
        has_pyqt5 = True
        print("⚠️ 檢測到 PyQt5")
    except ImportError:
        pass
    
    try:
        import PyQt6
        has_pyqt6 = True
        print("✅ 檢測到 PyQt6")
    except ImportError:
        pass
    
    if has_pyqt5 and has_pyqt6:
        print("⚠️ 同時檢測到 PyQt5 和 PyQt6，這會導致編譯失敗")
        print("💡 建議：暫時卸載 PyQt5 或使用排除參數")
        return True  # 需要特殊處理
    
    return False  # 無衝突

def compile_with_exclusions():
    """使用排除參數編譯"""
    print("🚀 使用排除參數編譯...")
    
    # 檢查主程式檔案
    if not os.path.exists('O3mh_gui_v21_optimized.py'):
        print("❌ 找不到主程式檔案")
        return False
    
    # 編譯命令 - 排除 PyQt5
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 單一執行檔
        '--windowed',  # 無控制台視窗
        '--name=台股智能選股系統',
        '--exclude-module=PyQt5',  # 排除 PyQt5
        '--exclude-module=tkinter',  # 排除 tkinter
        '--exclude-module=matplotlib.tests',  # 排除測試模組
        '--exclude-module=numpy.tests',
        '--exclude-module=pandas.tests',
        '--exclude-module=test',
        '--exclude-module=tests',
        '--exclude-module=unittest',
        '--add-data=stock_name_mapping.py;.',  # 包含股票名稱映射
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=sqlite3',
        '--hidden-import=pyqtgraph',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=yaml',
        '--hidden-import=json',
        '--hidden-import=logging',
        '--hidden-import=threading',
        '--hidden-import=datetime',
        '--hidden-import=concurrent.futures',
        '--hidden-import=unified_monitor_manager',
        '--clean',  # 清理之前的編譯檔案
        '--noconfirm',  # 不詢問覆蓋
        'O3mh_gui_v21_optimized.py'
    ]
    
    print("執行編譯命令...")
    print("⏳ 這可能需要幾分鐘時間，請耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            print("📁 執行檔位置: dist/台股智能選股系統.exe")
            
            # 檢查檔案大小
            exe_path = "dist/台股智能選股系統.exe"
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📊 執行檔大小: {size_mb:.1f} MB")
            
            return True
        else:
            print("❌ 編譯失敗")
            print("錯誤訊息:")
            print(result.stderr)
            
            # 檢查常見錯誤
            if "PyQt5" in result.stderr and "PyQt6" in result.stderr:
                print("\n💡 建議解決方案:")
                print("1. 卸載 PyQt5: pip uninstall PyQt5")
                print("2. 或者使用虛擬環境只安裝 PyQt6")
            
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_simple_spec():
    """創建簡化的 spec 檔案"""
    print("📝 創建簡化的 spec 檔案...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('stock_name_mapping.py', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'pandas',
        'numpy',
        'sqlite3',
        'pyqtgraph',
        'requests',
        'bs4',
        'yaml',
        'json',
        'logging',
        'threading',
        'datetime',
        'concurrent.futures',
        'unified_monitor_manager',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'tkinter',
        'matplotlib.tests',
        'numpy.tests',
        'pandas.tests',
        'test',
        'tests',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('simple_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ spec 檔案創建完成")

def compile_with_spec():
    """使用 spec 檔案編譯"""
    print("🚀 使用 spec 檔案編譯...")
    
    create_simple_spec()
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'simple_build.spec'
    ]
    
    print("執行編譯命令...")
    print("⏳ 這可能需要幾分鐘時間，請耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            print("📁 執行檔位置: dist/台股智能選股系統.exe")
            return True
        else:
            print("❌ 編譯失敗")
            print("錯誤訊息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def main():
    """主函數"""
    print("🔧 台股智能選股系統 - 修復編譯工具")
    print("=" * 50)
    
    # 檢查 PyQt 衝突
    has_conflict = fix_pyqt_conflict()
    
    if has_conflict:
        print("\n方法1: 使用排除參數編譯")
        if compile_with_exclusions():
            print("\n🎉 編譯完成！")
            return True
        
        print("\n方法2: 使用 spec 檔案編譯")
        if compile_with_spec():
            print("\n🎉 編譯完成！")
            return True
    else:
        # 無衝突，直接編譯
        if compile_with_exclusions():
            print("\n🎉 編譯完成！")
            return True
    
    print("\n❌ 所有編譯方法都失敗了")
    print("💡 建議:")
    print("1. 卸載 PyQt5: pip uninstall PyQt5")
    print("2. 重新安裝 PyQt6: pip install PyQt6")
    print("3. 使用虛擬環境進行編譯")
    
    return False

if __name__ == "__main__":
    main()
