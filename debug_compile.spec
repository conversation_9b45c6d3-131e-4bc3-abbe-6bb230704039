# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 隱藏導入 - 只包含最基本的模組
hiddenimports = [
    # 系統核心模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6 最小集合
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 數據處理最小集合
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    
    # 網路最小集合
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    
    # 其他必要
    'setuptools',
    'pkg_resources',
]

# 排除所有可能產生錯誤的模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    
    # 金融數據模組
    'twstock',
    'twstock.codes',
    'twstock.stock',
    'yfinance',
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    
    # 圖表模組
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    'pyqtgraph',
    'pyqtgraph.Qt',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    
    # 其他可能有問題的模組
    'xlsxwriter',
    'selenium',
    'webdriver_manager',
    'apscheduler',
    'apscheduler.schedulers',
    'apscheduler.schedulers.background',
    
    # 自定義模組
    'charts',
    'charts.candlestick',
    'config',
    'config.strategy_config',
    'enhanced_dividend_crawler',
    'integrated_strategy_help_dialog',
    'unified_monitor_manager',
    'smart_trading_strategies',
    'strategies',
    'strategies.triple_rsi_strategy',
    'strategies.small_investor_strategy',
    'strategies.second_high_strategy',
    'strategies.timid_cat_strategy',
    'strategies.canslim_strategy',
    'strategies.intraday_strategy',
    'monitoring',
    'monitoring.pre_market_monitor',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StockAnalyzer_Debug',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 啟用控制台以顯示調試信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
