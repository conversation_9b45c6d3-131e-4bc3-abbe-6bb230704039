#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 MOPS t164sb05 端點的回應內容
"""

import requests
from bs4 import BeautifulSoup
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_mops_response():
    """調試 MOPS 回應內容"""
    
    print("=" * 80)
    print("🔍 調試 MOPS t164sb05 端點回應")
    print("=" * 80)
    
    url = "https://mops.twse.com.tw/mops/web/t164sb05"
    
    # 測試不同的參數組合
    test_cases = [
        {
            'name': '台積電 2024Q1',
            'payload': {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',
                'co_id': '2330',
                'year': '113',  # 2024年
                'season': '1'
            }
        },
        {
            'name': '台積電 2023Q4',
            'payload': {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',
                'co_id': '2330',
                'year': '112',  # 2023年
                'season': '4'
            }
        },
        {
            'name': '鴻海 2023Q4',
            'payload': {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',
                'co_id': '2317',
                'year': '112',  # 2023年
                'season': '4'
            }
        }
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Referer': 'https://mops.twse.com.tw/mops/web/t164sb05'
    })
    session.verify = False
    
    for test_case in test_cases:
        print(f"\n🔍 測試: {test_case['name']}")
        print(f"📊 參數: {test_case['payload']}")
        
        try:
            response = session.post(url, data=test_case['payload'], timeout=30)
            response.encoding = 'utf-8'
            
            print(f"   狀態碼: {response.status_code}")
            print(f"   內容長度: {len(response.text)} 字符")
            
            if response.status_code == 200:
                # 檢查回應內容
                content = response.text
                
                # 檢查是否有錯誤訊息
                if '查無資料' in content:
                    print(f"   ⚠️ 查無資料")
                elif '無此資料' in content:
                    print(f"   ⚠️ 無此資料")
                elif 'error' in content.lower():
                    print(f"   ❌ 包含錯誤訊息")
                else:
                    print(f"   📄 正常回應")
                
                # 解析 HTML 結構
                soup = BeautifulSoup(content, 'html.parser')
                
                # 檢查所有表格
                tables = soup.find_all('table')
                print(f"   📊 找到 {len(tables)} 個表格")
                
                for i, table in enumerate(tables):
                    table_class = table.get('class', ['無類別'])
                    table_text = table.get_text()[:100].replace('\n', ' ').strip()
                    print(f"      表格 {i+1}: 類別={table_class}, 內容預覽='{table_text}...'")
                
                # 檢查是否包含現金流量相關關鍵字
                cash_flow_keywords = ['現金流量', '營業活動', '投資活動', '籌資活動', '現金及約當現金']
                found_keywords = [kw for kw in cash_flow_keywords if kw in content]
                
                if found_keywords:
                    print(f"   💰 包含現金流量關鍵字: {found_keywords}")
                else:
                    print(f"   ⚠️ 未包含現金流量關鍵字")
                
                # 顯示 HTML 結構的前500個字符
                print(f"   📄 HTML 內容預覽:")
                print(f"      {content[:500]}...")
                
                # 檢查是否需要額外的步驟
                if 'step=2' in content or 'step=3' in content:
                    print(f"   🔄 可能需要多步驟查詢")
                
                # 檢查表單
                forms = soup.find_all('form')
                if forms:
                    print(f"   📝 找到 {len(forms)} 個表單")
                    for i, form in enumerate(forms):
                        action = form.get('action', '無action')
                        method = form.get('method', '無method')
                        print(f"      表單 {i+1}: action={action}, method={method}")
            
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")

def test_alternative_parameters():
    """測試其他可能的參數組合"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試其他參數組合")
    print("=" * 80)
    
    url = "https://mops.twse.com.tw/mops/web/t164sb05"
    
    # 嘗試不同的參數組合
    alternative_payloads = [
        {
            'name': '簡化參數',
            'payload': {
                'co_id': '2330',
                'year': '112',
                'season': '4'
            }
        },
        {
            'name': '不同 TYPEK',
            'payload': {
                'step': '1',
                'TYPEK': 'all',
                'co_id': '2330',
                'year': '112',
                'season': '4'
            }
        },
        {
            'name': '完整參數 + 不同編碼',
            'payload': {
                'encodeURIComponent': '0',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',
                'co_id': '2330',
                'year': '112',
                'season': '4'
            }
        }
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    session.verify = False
    
    for test in alternative_payloads:
        print(f"\n🔍 測試: {test['name']}")
        
        try:
            response = session.post(url, data=test['payload'], timeout=30)
            response.encoding = 'utf-8'
            
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                soup = BeautifulSoup(content, 'html.parser')
                tables = soup.find_all('table')
                
                print(f"   📊 表格數: {len(tables)}")
                
                # 檢查現金流量關鍵字
                cash_flow_keywords = ['現金流量', '營業活動', '投資活動']
                found = any(kw in content for kw in cash_flow_keywords)
                print(f"   💰 包含現金流量資料: {'是' if found else '否'}")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")

def main():
    """主函數"""
    
    print("🔍 MOPS t164sb05 端點調試")
    
    # 調試基本回應
    debug_mops_response()
    
    # 測試其他參數
    test_alternative_parameters()
    
    print(f"\n" + "=" * 80)
    print("📊 調試總結")
    print("=" * 80)
    print("🎯 基於調試結果:")
    print("   1. 檢查 t164sb05 端點是否正確")
    print("   2. 確認參數格式是否正確")
    print("   3. 檢查是否需要多步驟查詢")
    print("   4. 確認表格解析邏輯")

if __name__ == "__main__":
    main()
