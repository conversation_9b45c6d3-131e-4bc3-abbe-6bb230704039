#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab GUI - 修復版本
專注解決：捲軸顯示、卡頓問題、pe.pkl更新
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import threading
import os
import pandas as pd
import sys

class FinlabGUI:
    def __init__(self, root):
        self.root = root
        self.data_info = {}
        self.date_entries = {}
        self.buttons = {}
        self.crawler_funcs = {}
        
        self.setup_ui()
        self.init_crawlers()
        self.check_existing_data()
    
    def setup_ui(self):
        """設置界面"""
        self.root.title("Finlab 爬蟲系統 - 修復版")
        self.root.geometry("1200x700")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 標題
        title = ttk.Label(main_frame, text="Finlab 爬蟲系統", font=('Arial', 16, 'bold'))
        title.pack(pady=(0, 20))
        
        # 資料狀態區域
        self.create_data_area(main_frame)
        
        # 日誌區域
        self.create_log_area(main_frame)
    
    def create_data_area(self, parent):
        """創建資料狀態區域"""
        data_frame = ttk.LabelFrame(parent, text="資料狀態", padding="10")
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 表格框架
        self.table_frame = ttk.Frame(data_frame)
        self.table_frame.pack(fill=tk.X)
        
        # 表格標題
        headers = ["檔案名稱", "最後日期", "開始日期", "結束日期", "操作"]
        for i, header in enumerate(headers):
            label = ttk.Label(self.table_frame, text=header, font=('Arial', 10, 'bold'))
            label.grid(row=0, column=i, padx=5, pady=5, sticky=tk.W)
    
    def create_log_area(self, parent):
        """創建日誌區域 - 最簡單有效的方式"""
        log_frame = ttk.LabelFrame(parent, text="執行日誌", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 使用 pack 而不是 grid，更簡單
        container = ttk.Frame(log_frame)
        container.pack(fill=tk.BOTH, expand=True)
        
        # 文本框
        self.log_text = tk.Text(container, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 垂直捲軸
        scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 按鈕框架
        btn_frame = ttk.Frame(log_frame)
        btn_frame.pack(fill=tk.X, pady=(5, 0))
        
        clear_btn = ttk.Button(btn_frame, text="清除日誌", command=self.clear_log)
        clear_btn.pack(side=tk.LEFT)
        
        # 初始日誌
        self.add_log("Finlab 爬蟲系統已啟動")
    
    def add_log(self, message):
        """添加日誌 - 最簡單直接的方式"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制行數
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 200:
            self.log_text.delete('1.0', '50.0')
        
        self.root.update()  # 立即更新
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("日誌已清除")
    
    def init_crawlers(self):
        """初始化爬蟲"""
        try:
            # 添加AI_finlab目錄到Python路徑
            ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
            if ai_finlab_path not in sys.path:
                sys.path.insert(0, ai_finlab_path)
            
            from crawler import crawl_pe, crawl_price, crawl_bargin, crawl_monthly_report
            
            self.crawler_funcs = {
                'crawl_pe': crawl_pe,
                'crawl_price': crawl_price,
                'crawl_bargin': crawl_bargin,
                'crawl_monthly_report': crawl_monthly_report,
            }
            
            self.add_log("✅ 爬蟲模組載入成功")
            
        except ImportError as e:
            self.add_log(f"❌ 爬蟲模組載入失敗: {e}")
            self.crawler_funcs = {}
        except Exception as e:
            self.add_log(f"❌ 初始化錯誤: {e}")
            self.crawler_funcs = {}
    
    def check_existing_data(self):
        """檢查現有資料檔案"""
        self.add_log("🔍 檢查現有資料檔案...")
        
        # 清除現有表格行
        for widget in self.table_frame.winfo_children():
            if int(widget.grid_info()["row"]) > 0:
                widget.destroy()
        
        data_files = {
            'pe.pkl': '本益比',
            'price.pkl': '股價資料',
            'bargin_report.pkl': '融資融券',
            'monthly_report.pkl': '月報'
        }
        
        row = 1
        for filename, display_name in data_files.items():
            self.add_data_row(row, filename, display_name)
            row += 1
    
    def add_data_row(self, row, filename, display_name):
        """添加資料行"""
        # 檔案名稱
        name_label = ttk.Label(self.table_frame, text=display_name)
        name_label.grid(row=row, column=0, padx=5, pady=2, sticky=tk.W)
        
        # 檢查檔案狀態
        last_date = "檔案不存在"
        if os.path.exists(filename):
            try:
                data = pd.read_pickle(filename)
                if hasattr(data, 'index') and len(data.index) > 0:
                    if hasattr(data.index, 'levels'):
                        dates = data.index.get_level_values(0)
                        last_date = dates.max().strftime('%Y-%m-%d')
                    else:
                        last_date = data.index.max().strftime('%Y-%m-%d')
                else:
                    last_date = "無日期資訊"
            except Exception as e:
                last_date = f"讀取錯誤: {str(e)[:20]}"
        
        # 最後日期
        date_label = ttk.Label(self.table_frame, text=last_date)
        date_label.grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
        
        # 開始日期輸入
        start_entry = ttk.Entry(self.table_frame, width=12)
        if last_date != "檔案不存在" and "錯誤" not in last_date and "無日期" not in last_date:
            try:
                next_date = (datetime.strptime(last_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
                start_entry.insert(0, next_date)
            except:
                start_entry.insert(0, "2025-01-01")
        else:
            start_entry.insert(0, "2025-01-01")
        start_entry.grid(row=row, column=2, padx=5, pady=2)
        
        # 結束日期輸入
        end_entry = ttk.Entry(self.table_frame, width=12)
        end_entry.insert(0, datetime.now().strftime('%Y-%m-%d'))
        end_entry.grid(row=row, column=3, padx=5, pady=2)
        
        # 更新按鈕
        update_btn = ttk.Button(self.table_frame, text="更新", 
                               command=lambda f=filename: self.update_data(f))
        update_btn.grid(row=row, column=4, padx=5, pady=2)
        
        # 儲存引用
        self.date_entries[filename] = {'start': start_entry, 'end': end_entry}
        self.buttons[filename] = update_btn
        self.data_info[filename] = {'display_name': display_name, 'last_date': last_date}
    
    def update_data(self, filename):
        """更新資料"""
        if filename not in self.date_entries:
            return
        
        start_date = self.date_entries[filename]['start'].get()
        end_date = self.date_entries[filename]['end'].get()
        display_name = self.data_info[filename]['display_name']
        
        self.add_log(f"🔄 開始更新 {display_name} ({start_date} 至 {end_date})")
        
        # 檢查爬蟲函數
        crawler_map = {
            'pe.pkl': 'crawl_pe',
            'price.pkl': 'crawl_price',
            'bargin_report.pkl': 'crawl_bargin',
            'monthly_report.pkl': 'crawl_monthly_report'
        }
        
        if filename not in crawler_map:
            self.add_log(f"❌ 不支援的檔案類型: {filename}")
            return
        
        func_name = crawler_map[filename]
        if func_name not in self.crawler_funcs:
            self.add_log(f"❌ 找不到爬蟲函數: {func_name}")
            return
        
        # 在新線程中執行更新
        def update_thread():
            try:
                crawler_func = self.crawler_funcs[func_name]
                
                # 轉換日期格式
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                
                all_data = []
                current_date = start_dt
                
                while current_date <= end_dt:
                    try:
                        self.add_log(f"   爬取 {current_date.strftime('%Y-%m-%d')} 的資料...")
                        daily_data = crawler_func(current_date)
                        
                        if daily_data is not None and not daily_data.empty:
                            all_data.append(daily_data)
                            self.add_log(f"   ✅ {current_date.strftime('%Y-%m-%d')} 完成")
                        else:
                            self.add_log(f"   ⚠️ {current_date.strftime('%Y-%m-%d')} 無資料")
                        
                        current_date += timedelta(days=1)
                        
                        # 避免請求過於頻繁
                        import time
                        time.sleep(1)
                        
                    except Exception as e:
                        self.add_log(f"   ❌ {current_date.strftime('%Y-%m-%d')} 失敗: {str(e)}")
                        current_date += timedelta(days=1)
                        continue
                
                if all_data:
                    result = pd.concat(all_data, ignore_index=True)
                    self.add_log(f"✅ {display_name} 更新完成，獲取 {len(result)} 筆資料")
                else:
                    self.add_log(f"⚠️ {display_name} 更新完成，但無新資料")
                
                # 重新檢查資料狀態
                self.root.after(0, self.check_existing_data)
                
            except Exception as e:
                self.add_log(f"❌ 更新 {display_name} 時發生錯誤: {str(e)}")
        
        threading.Thread(target=update_thread, daemon=True).start()

def main():
    root = tk.Tk()
    app = FinlabGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
