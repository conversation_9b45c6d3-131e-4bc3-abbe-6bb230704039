#!/usr/bin/env python3
"""
配置管理模組
從原始程式中提取的ConfigManager類別
"""
import os
import json
import logging


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file="app_config.json"):
        self.config_file = config_file
        self.default_config = {
            "database": {
                "price_db_path": "D:/Finlab/history/tables/price.db",
                "pe_db_path": "D:/Finlab/history/tables/pe_data.db",
                "pe_enabled": True,
                "news_db_path": "D:/Finlab/history/tables/news.db",
                "news_enabled": True,
                "dividend_db_path": "D:/Finlab/history/tables/dividend_data.db",
                "dividend_enabled": True,
                "monthly_revenue_db_path": "D:/Finlab/history/tables/monthly_revenue.db",
                "monthly_revenue_enabled": True,
                "auto_detect": True
            },
            "ui": {
                "theme": "dark",
                "font_size": 10,
                "window_size": [1200, 800],
                "auto_save_layout": True
            },
            "trading": {
                "default_strategy": "阿水一式",
                "risk_level": "medium",
                "auto_update_data": True
            },
            "performance": {
                "max_stocks_per_batch": 100,
                "cache_size": 1000,
                "log_level": "INFO"
            },
            "monitor": {
                "stocks": "2330,2317,2454,3008,2412",
                "update_interval": 30,
                "auto_start": False,
                "last_updated": ""
            }
        }
        self.config = self.load_config()

    def load_config(self):
        """載入配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合併默認配置
                return self._merge_config(self.default_config, config)
            else:
                logging.info("配置文件不存在，使用默認配置")
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            logging.error(f"載入配置失敗: {e}")
            return self.default_config.copy()

    def save_config(self, config=None):
        """保存配置"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
            logging.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logging.error(f"保存配置失敗: {e}")

    def get(self, key_path, default=None):
        """獲取配置值，支援點號分隔的路徑"""
        try:
            keys = key_path.split('.')
            value = self.config
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key_path, value):
        """設置配置值"""
        try:
            keys = key_path.split('.')
            config = self.config
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[keys[-1]] = value
            self.save_config()
        except Exception as e:
            logging.error(f"設置配置失敗: {e}")

    def _merge_config(self, default, user):
        """合併配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result


# 全局配置管理器實例
config_manager = ConfigManager()