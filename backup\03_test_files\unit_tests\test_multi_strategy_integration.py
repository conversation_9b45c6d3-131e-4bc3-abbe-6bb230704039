#!/usr/bin/env python3
"""
測試多策略整合功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """創建測試數據"""
    # 生成6個月的測試數據
    dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
    dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 生成價格數據
    np.random.seed(42)
    base_price = 100
    prices = []
    
    for i in range(len(dates)):
        if i == 0:
            price = base_price
        else:
            # 添加趨勢和隨機波動
            trend = 0.001 * np.sin(i * 0.1)  # 長期趨勢
            noise = np.random.normal(0, 0.02)  # 隨機波動
            price = prices[-1] * (1 + trend + noise)
        
        prices.append(price)
    
    # 生成OHLCV數據
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        volatility = 0.02
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
        
        # 確保價格邏輯正確
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # 生成成交量
        volume = np.random.randint(100000, 1000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df

def test_multi_strategy_analyzer():
    """測試多策略分析器"""
    print("🧪 測試多策略分析器...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 創建測試數據
        test_data = create_test_data()
        print(f"✅ 測試數據創建成功，共 {len(test_data)} 個交易日")
        
        # 創建分析器
        analyzer = MultiStrategySignalAnalyzer()
        print("✅ 多策略分析器創建成功")
        
        # 分析所有策略
        all_signals = analyzer.analyze_all_strategies(test_data)
        print("✅ 所有策略分析完成")
        
        # 顯示結果統計
        print("\n📊 各策略信號統計:")
        for strategy_name, signals in all_signals.items():
            buy_count = len(signals['buy'])
            sell_count = len(signals['sell'])
            
            # 計算強信號數量
            strong_buy = len([s for s in signals['buy'] if s['strength'] >= 0.7])
            strong_sell = len([s for s in signals['sell'] if s['strength'] >= 0.7])
            
            print(f"   {strategy_name}:")
            print(f"     買入信號: {buy_count} (強信號: {strong_buy})")
            print(f"     賣出信號: {sell_count} (強信號: {strong_sell})")
            
            # 顯示部分信號文字
            if signals['buy']:
                sample_buy = signals['buy'][0]
                print(f"     買入示例: {sample_buy['text']} (強度: {sample_buy['strength']:.2f})")
            
            if signals['sell']:
                sample_sell = signals['sell'][0]
                print(f"     賣出示例: {sample_sell['text']} (強度: {sample_sell['strength']:.2f})")
        
        return True, all_signals, test_data
        
    except Exception as e:
        print(f"❌ 多策略分析器測試失敗: {e}")
        return False, None, None

def test_signal_consensus():
    """測試信號一致性分析"""
    print("\n🎯 測試信號一致性分析...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 使用之前的測試結果
        success, all_signals, test_data = test_multi_strategy_analyzer()
        if not success:
            return False
        
        analyzer = MultiStrategySignalAnalyzer()
        
        # 分析信號一致性
        consensus_points = analyzer.analyze_signal_consensus(all_signals, test_data, window=5)
        
        print(f"✅ 一致性分析完成，發現 {len(consensus_points)} 個高一致性點")
        
        # 顯示前5個一致性點
        if consensus_points:
            print("\n📋 高一致性信號點 (前5個):")
            for i, point in enumerate(consensus_points[:5]):
                date = test_data.index[point['index']].strftime('%Y-%m-%d')
                signal_type = "買入" if point['type'] == 'buy' else "賣出"
                emoji = "🟢" if point['type'] == 'buy' else "🔴"
                
                print(f"   {i+1}. {emoji} {date} - {signal_type}信號")
                print(f"      一致性: {point['consensus']}/4, 價格: {point['price']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信號一致性分析測試失敗: {e}")
        return False

def test_chart_generation():
    """測試圖表生成"""
    print("\n📈 測試多策略圖表生成...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 使用測試數據
        success, all_signals, test_data = test_multi_strategy_analyzer()
        if not success:
            return False
        
        analyzer = MultiStrategySignalAnalyzer()
        
        # 生成圖表
        fig = analyzer.plot_multi_strategy_chart(
            test_data, 
            all_signals,
            "多策略整合測試圖表"
        )
        
        print("✅ 圖表生成成功")
        
        # 保存圖表
        filename = f"multi_strategy_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        fig.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 圖表已保存為: {filename}")
        
        plt.close(fig)  # 關閉圖表避免內存洩漏
        
        return True
        
    except Exception as e:
        print(f"❌ 圖表生成測試失敗: {e}")
        return False

def test_signal_summary_table():
    """測試信號摘要表"""
    print("\n📋 測試信號摘要表生成...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 使用測試數據
        success, all_signals, test_data = test_multi_strategy_analyzer()
        if not success:
            return False
        
        analyzer = MultiStrategySignalAnalyzer()
        
        # 生成摘要表
        summary_df = analyzer.create_signal_summary_table(all_signals)
        
        print("✅ 信號摘要表生成成功")
        print("\n📊 信號摘要表:")
        print(summary_df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 信號摘要表測試失敗: {e}")
        return False

def test_gui_launch():
    """測試GUI啟動"""
    print("\n🖥️ 測試多策略GUI啟動...")
    
    try:
        # 檢查GUI文件是否存在
        if os.path.exists('multi_strategy_gui.py'):
            print("✅ 多策略GUI文件存在")
            
            # 嘗試導入GUI模組
            from multi_strategy_gui import MultiStrategyGUI
            print("✅ 多策略GUI模組導入成功")
            
            print("💡 可以運行: python multi_strategy_gui.py")
            return True
        else:
            print("❌ 多策略GUI文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

def demonstrate_multi_strategy_features():
    """展示多策略功能特色"""
    print("\n💡 多策略整合功能特色:")
    print("=" * 60)
    
    features = [
        ("🎯 四策略整合", "RSI、MACD、布林通道、移動平均交叉同圖顯示"),
        ("📊 智能信號強度", "每個信號都有強度評級和描述文字"),
        ("🔍 一致性分析", "找出多個策略同時發出信號的高可信度點"),
        ("🎨 視覺化設計", "不同策略使用不同形狀和顏色標記"),
        ("📋 統計報告", "詳細的信號統計和摘要表"),
        ("🖥️ 互動界面", "可選擇顯示特定策略，調整分析參數"),
        ("💾 結果導出", "支援圖表和數據導出功能"),
        ("⚡ 實時分析", "快速分析大量歷史數據")
    ]
    
    for title, description in features:
        print(f"{title}: {description}")
    
    print("\n🎨 圖表標記說明:")
    markers = [
        ("🔴 RSI策略", "三角形標記 (^ v)，紅色系"),
        ("🔵 MACD策略", "方形標記 (■)，藍色系"),
        ("🟡 布林通道策略", "圓形標記 (●)，黃色系"),
        ("🟢 移動平均交叉策略", "菱形標記 (♦)，綠色系")
    ]
    
    for strategy, marker in markers:
        print(f"   {strategy}: {marker}")
    
    print("\n💡 使用建議:")
    tips = [
        "多個策略同時發出信號時，可信度更高",
        "關注有文字標註的高強度信號",
        "利用一致性分析找出最佳進出點",
        "可單獨查看某個策略避免信號過多",
        "結合基本面分析和風險管理使用"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"   {i}. {tip}")

def main():
    """主函數"""
    print("🚀 多策略整合功能測試程式")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("多策略分析器", test_multi_strategy_analyzer),
        ("信號一致性分析", test_signal_consensus),
        ("圖表生成", test_chart_generation),
        ("信號摘要表", test_signal_summary_table),
        ("GUI啟動", test_gui_launch)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            if test_name == "多策略分析器":
                result, _, _ = test_func()
            else:
                result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 展示功能特色
    demonstrate_multi_strategy_features()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！多策略整合功能已成功實現")
        print("\n🚀 啟動方式:")
        print("   python multi_strategy_gui.py  # 啟動多策略GUI")
        print("   python test_multi_strategy_integration.py  # 運行測試")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")

if __name__ == '__main__':
    main()
