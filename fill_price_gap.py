#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
填補 price.pkl 中的空檔資料
填補區間：2022-09-16 到 2022-12-01
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3
import pandas as pd

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def generate_gap_dates():
    """生成空檔期間的日期列表"""
    print("📅 生成空檔期間的日期列表...")
    
    start_date = datetime(2022, 9, 16)  # 舊資料結束後的第一天
    end_date = datetime(2022, 12, 1)    # 新資料開始前的最後一天
    
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        # 只包含工作日（週一到週五）
        if current_date.weekday() < 5:
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    print(f"✅ 生成 {len(dates)} 個工作日")
    print(f"   日期範圍: {dates[0].strftime('%Y-%m-%d')} 到 {dates[-1].strftime('%Y-%m-%d')}")
    
    return dates

def check_current_price_data():
    """檢查當前 price.pkl 的資料狀況"""
    print("🔍 檢查當前 price.pkl 資料狀況...")
    
    price_file = "history/tables/price.pkl"
    
    if not os.path.exists(price_file):
        print("❌ price.pkl 不存在")
        return None, None
    
    try:
        data = pd.read_pickle(price_file)
        date_range = data.index.get_level_values('date')
        
        print(f"✅ 當前資料:")
        print(f"   總筆數: {len(data):,}")
        print(f"   日期範圍: {date_range.min()} 至 {date_range.max()}")
        
        # 檢查空檔
        unique_dates = sorted(date_range.unique())
        gap_start = datetime(2022, 9, 16)
        gap_end = datetime(2022, 12, 1)
        
        gap_dates_in_data = [d for d in unique_dates if gap_start <= d <= gap_end]
        print(f"   空檔期間已有資料: {len(gap_dates_in_data)} 個日期")
        
        if gap_dates_in_data:
            print(f"   已有日期範圍: {min(gap_dates_in_data)} 至 {max(gap_dates_in_data)}")
        
        return data, unique_dates
        
    except Exception as e:
        print(f"❌ 讀取失敗: {str(e)}")
        return None, None

def fill_price_gap():
    """填補價格資料空檔"""
    print("=" * 60)
    print("🔄 開始填補 price 資料空檔")
    print("=" * 60)
    
    # 1. 檢查當前資料
    current_data, existing_dates = check_current_price_data()
    if current_data is None:
        return False
    
    # 2. 生成需要填補的日期
    gap_dates = generate_gap_dates()
    
    # 3. 找出還沒有的日期
    existing_gap_dates = set(d for d in existing_dates if datetime(2022, 9, 16) <= d <= datetime(2022, 12, 1))
    missing_dates = [d for d in gap_dates if d not in existing_gap_dates]
    
    print(f"\n📊 空檔分析:")
    print(f"   需要填補的總日期: {len(gap_dates)}")
    print(f"   已存在的日期: {len(existing_gap_dates)}")
    print(f"   需要爬取的日期: {len(missing_dates)}")
    
    if not missing_dates:
        print("✅ 空檔已經完全填補，無需額外處理")
        return True
    
    print(f"   缺失日期範圍: {missing_dates[0].strftime('%Y-%m-%d')} 到 {missing_dates[-1].strftime('%Y-%m-%d')}")
    
    # 4. 確認是否繼續
    response = input(f"\n是否開始爬取 {len(missing_dates)} 個缺失日期的資料？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return False
    
    # 5. 開始爬取
    try:
        from crawler import update_table, crawl_price
        
        print(f"\n🚀 開始爬取 {len(missing_dates)} 個日期的資料...")
        print("⚠️ 注意：這個過程可能需要較長時間")
        
        # 使用 update_table 函數進行爬取
        update_table('price', crawl_price, missing_dates)
        
        print("✅ 空檔填補完成")
        return True
        
    except Exception as e:
        print(f"❌ 爬取過程發生錯誤: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def verify_gap_filled():
    """驗證空檔是否已填補"""
    print("\n🔍 驗證空檔填補結果...")
    
    data, unique_dates = check_current_price_data()
    if data is None:
        return False
    
    # 檢查空檔期間
    gap_start = datetime(2022, 9, 16)
    gap_end = datetime(2022, 12, 1)
    
    gap_dates_in_data = [d for d in unique_dates if gap_start <= d <= gap_end]
    expected_dates = generate_gap_dates()
    
    print(f"✅ 驗證結果:")
    print(f"   期望日期數: {len(expected_dates)}")
    print(f"   實際日期數: {len(gap_dates_in_data)}")
    print(f"   填補完成度: {len(gap_dates_in_data)/len(expected_dates)*100:.1f}%")
    
    if len(gap_dates_in_data) == len(expected_dates):
        print("🎉 空檔完全填補成功！")
        return True
    else:
        missing_count = len(expected_dates) - len(gap_dates_in_data)
        print(f"⚠️ 還有 {missing_count} 個日期未填補")
        return False

def main():
    """主函數"""
    print("🔧 Price 資料空檔填補工具")
    print("=" * 60)
    print("📋 填補區間: 2022-09-16 到 2022-12-01")
    print("🎯 目標: 填補合併後 price.pkl 中的空檔資料")
    print()
    
    # 執行填補
    success = fill_price_gap()
    
    if success:
        # 驗證結果
        verify_gap_filled()
        
        print("\n💡 後續建議:")
        print("   • 檢查 price.pkl 檔案的完整性")
        print("   • 確認日期連續性")
        print("   • 可以重新啟動 auto_update.py 繼續更新最新資料")
    else:
        print("\n❌ 填補過程失敗")
        print("💡 可能的解決方案:")
        print("   • 檢查網路連接")
        print("   • 稍後重試")
        print("   • 檢查爬蟲函數是否正常")

if __name__ == "__main__":
    main()
