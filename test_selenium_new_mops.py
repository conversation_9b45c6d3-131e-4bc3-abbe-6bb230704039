#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 Selenium 測試新 MOPS 財報功能
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
import time

def test_selenium_income_statement():
    """使用 Selenium 測試綜合損益表"""
    print("🔍 使用 Selenium 測試新 MOPS 綜合損益表")
    print("=" * 60)
    
    try:
        # 設置 Chrome 選項
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 反爬蟲設置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 隨機 User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # 創建 WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        
        # 執行反檢測腳本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 訪問綜合損益表頁面
        income_url = "https://mopsov.twse.com.tw/mops/web/t164sb04"
        
        print(f"🌐 訪問: {income_url}")
        driver.get(income_url)
        
        # 等待頁面載入
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        time.sleep(3)  # 額外等待
        
        page_title = driver.title
        print(f"   頁面標題: {page_title}")
        
        # 保存頁面內容
        page_source = driver.page_source
        with open('selenium_income_statement.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"   💾 頁面已保存: selenium_income_statement.html")
        
        # 尋找表單元素
        try:
            # 尋找公司代號輸入框
            co_id_input = None
            possible_selectors = [
                'input[name="co_id"]',
                'input[name="COMPANY_ID"]', 
                'input[name="company_id"]',
                'input[id="co_id"]',
                'input[placeholder*="公司代號"]',
                'input[placeholder*="代號"]'
            ]
            
            for selector in possible_selectors:
                try:
                    co_id_input = driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"   ✅ 找到公司代號輸入框: {selector}")
                    break
                except:
                    continue
            
            # 尋找年度輸入框 (可能被隱藏)
            year_input = None
            year_selectors = [
                'input[name="year"]',
                'input[name="YEAR"]',
                'input[id="year"]',
                'select[name="year"]',
                'select[name="YEAR"]',
                'select[id="year"]'
            ]

            for selector in year_selectors:
                try:
                    year_input = driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"   ✅ 找到年度欄位: {selector}")

                    # 檢查是否被隱藏
                    if year_input.get_attribute('style') and 'hidden' in year_input.get_attribute('style'):
                        print(f"     ⚠️ 年度欄位被隱藏，嘗試顯示...")
                        # 使用 JavaScript 顯示欄位
                        driver.execute_script("arguments[0].style.visibility = 'visible';", year_input)

                    break
                except:
                    continue
            
            # 尋找季別選擇框
            season_select = None
            season_selectors = [
                'select[name="season"]',
                'select[name="SEASON"]',
                'select[id="season"]'
            ]
            
            for selector in season_selectors:
                try:
                    season_select = driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"   ✅ 找到季別選擇框: {selector}")
                    break
                except:
                    continue
            
            # 如果找到了表單元素，嘗試查詢台積電
            if co_id_input and year_input and season_select:
                print(f"\n🧪 嘗試查詢台積電 (2330) 2023年Q4財報...")
                
                # 輸入公司代號
                co_id_input.clear()
                co_id_input.send_keys("2330")
                print(f"   ✅ 輸入公司代號: 2330")
                
                # 輸入年度 (2023年 = 民國112年)
                if year_input.tag_name == 'select':
                    # 如果是選擇框
                    year_dropdown = Select(year_input)
                    try:
                        year_dropdown.select_by_value("112")
                        print(f"   ✅ 選擇年度: 112 (2023年)")
                    except:
                        # 如果沒有112年，選擇最新的年度
                        options = year_dropdown.options
                        if options:
                            latest_year = options[-1].get_attribute('value')
                            year_dropdown.select_by_value(latest_year)
                            print(f"   ✅ 選擇年度: {latest_year}")
                else:
                    # 如果是輸入框
                    year_input.clear()
                    year_input.send_keys("112")
                    print(f"   ✅ 輸入年度: 112 (2023年)")
                
                # 選擇季別
                season_dropdown = Select(season_select)
                season_dropdown.select_by_value("4")
                print(f"   ✅ 選擇季別: 4 (Q4)")
                
                # 尋找提交按鈕
                submit_button = None
                submit_selectors = [
                    'input[type="submit"]',
                    'button[type="submit"]',
                    'input[value*="查詢"]',
                    'button[value*="查詢"]',
                    'input[value*="送出"]',
                    'button[value*="送出"]'
                ]
                
                for selector in submit_selectors:
                    try:
                        submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                        print(f"   ✅ 找到提交按鈕: {selector}")
                        break
                    except:
                        continue
                
                if submit_button:
                    # 點擊提交按鈕
                    submit_button.click()
                    print(f"   🔄 提交查詢...")
                    
                    # 等待結果載入
                    time.sleep(5)
                    
                    # 檢查結果
                    result_source = driver.page_source
                    
                    if '台積電' in result_source or '2330' in result_source:
                        print(f"   🎉 成功獲取台積電財報資料!")
                        
                        # 保存結果
                        with open('tsmc_financial_result.html', 'w', encoding='utf-8') as f:
                            f.write(result_source)
                        print(f"   💾 台積電財報結果已保存: tsmc_financial_result.html")
                        
                        # 嘗試解析表格資料
                        try:
                            tables = driver.find_elements(By.TAG_NAME, "table")
                            print(f"   📊 找到 {len(tables)} 個表格")
                            
                            for i, table in enumerate(tables[:3], 1):  # 只檢查前3個表格
                                rows = table.find_elements(By.TAG_NAME, "tr")
                                if len(rows) > 5:  # 有足夠的資料行
                                    print(f"     表格 {i}: {len(rows)} 行")
                                    
                                    # 顯示前幾行的內容
                                    for j, row in enumerate(rows[:3], 1):
                                        cells = row.find_elements(By.TAG_NAME, "td")
                                        if cells:
                                            cell_texts = [cell.text.strip() for cell in cells[:3]]
                                            print(f"       行 {j}: {cell_texts}")
                            
                            return True
                            
                        except Exception as e:
                            print(f"   ⚠️ 解析表格時出錯: {str(e)}")
                            return True  # 雖然解析失敗，但查詢成功了
                        
                    else:
                        print(f"   ⚠️ 查詢結果中未找到台積電資料")
                        
                        # 保存結果用於調試
                        with open('query_result_debug.html', 'w', encoding='utf-8') as f:
                            f.write(result_source)
                        print(f"   💾 查詢結果已保存用於調試: query_result_debug.html")
                        
                        return False
                else:
                    print(f"   ❌ 未找到提交按鈕")
                    return False
            else:
                print(f"   ❌ 未找到完整的表單元素")
                print(f"     公司代號輸入框: {'✅' if co_id_input else '❌'}")
                print(f"     年度欄位: {'✅' if year_input else '❌'}")
                print(f"     季別選擇框: {'✅' if season_select else '❌'}")
                return False
                
        except Exception as e:
            print(f"   ❌ 表單操作失敗: {str(e)}")
            return False
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Selenium 測試失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 Selenium 新 MOPS 財報測試工具")
    print("=" * 60)
    print("🎯 目標: 使用 Selenium 測試新 MOPS 網站的財報查詢")
    print("=" * 60)
    
    success = test_selenium_income_statement()
    
    if success:
        print(f"\n🎉 測試成功!")
        print(f"💡 新 MOPS 網站的財報查詢功能可以使用")
        print(f"📋 下一步: 基於這個結果建立新的財報爬蟲")
    else:
        print(f"\n⚠️ 測試遇到問題")
        print(f"💡 需要進一步分析保存的 HTML 檔案")

if __name__ == "__main__":
    main()
