#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試導出功能的"是否開啟"選項
"""

import sys
import os
import tempfile
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QPushButton, QTextEdit, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt

class TestExportWindow(QMainWindow):
    """測試導出功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 導出功能測試 - 是否開啟選項")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 說明文字
        info_text = QTextEdit()
        info_text.setMaximumHeight(150)
        info_text.setHtml("""
        <h3>🎯 導出功能測試</h3>
        <p><b>測試目標：</b>驗證導出CSV檔案後的"是否開啟"選項功能</p>
        <p><b>測試步驟：</b></p>
        <ol>
            <li>點擊「測試導出功能」按鈕</li>
            <li>選擇保存位置</li>
            <li>查看是否出現"是否要開啟導出的檔案？"詢問對話框</li>
            <li>測試「是」和「否」兩個選項的行為</li>
        </ol>
        <p><b>預期結果：</b>選擇「是」時應該自動開啟CSV檔案，選擇「否」時不開啟</p>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 測試導出功能")
        test_btn.clicked.connect(self.test_export_function)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🚀 導出功能測試程式已啟動")
        self.log("📋 準備測試導出CSV檔案的「是否開啟」選項")
    
    def log(self, message):
        """添加日誌訊息"""
        self.log_text.append(f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}")
    
    def test_export_function(self):
        """測試導出功能"""
        try:
            self.log("🔍 開始測試導出功能...")
            
            # 創建測試數據
            test_data = {
                '日期': ['1140725', '1140726', '1140727'],
                '指數': ['台灣50指數', '發行量加權股價指數', '中小型100指數'],
                '收盤指數': ['16234.56', '22456.78', '8765.43'],
                '漲跌': ['+', '+', '-'],
                '漲跌點數': ['23.45', '156.23', '12.34'],
                '漲跌百分比': ['+0.14%', '+0.70%', '-0.14%'],
                'crawl_time': ['2025-07-27 14:30:00', '2025-07-27 14:30:00', '2025-07-27 14:30:00']
            }
            
            df = pd.DataFrame(test_data)
            self.log(f"✅ 已創建測試數據: {len(df)} 筆記錄")
            
            # 選擇保存位置
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存測試CSV檔案",
                "twse_market_data_test.csv",
                "CSV檔案 (*.csv)"
            )
            
            if not file_path:
                self.log("❌ 用戶取消了檔案選擇")
                return
            
            self.log(f"📁 選擇的保存路徑: {file_path}")
            
            # 保存CSV檔案
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            self.log(f"💾 CSV檔案已保存")
            
            # 模擬原來的導出成功對話框（詢問是否開啟）
            reply = QMessageBox.question(
                self,
                "導出成功",
                f"✅ 數據已成功導出到：\n{file_path}\n\n📊 總共 {len(df)} 筆記錄\n\n❓ 是否要開啟導出的檔案？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.log("👆 用戶選擇「是」- 嘗試開啟檔案")
                try:
                    import subprocess
                    import platform
                    
                    # 根據作業系統選擇開啟方式
                    if platform.system() == "Windows":
                        os.startfile(file_path)
                        self.log("🪟 使用 Windows 方式開啟檔案")
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", file_path])
                        self.log("🍎 使用 macOS 方式開啟檔案")
                    else:  # Linux
                        subprocess.run(["xdg-open", file_path])
                        self.log("🐧 使用 Linux 方式開啟檔案")
                    
                    self.log("✅ 檔案已成功開啟")
                    
                except Exception as e:
                    self.log(f"❌ 開啟檔案失敗: {e}")
                    QMessageBox.warning(
                        self,
                        "開啟失敗",
                        f"無法開啟檔案：\n{str(e)}\n\n請手動開啟：\n{file_path}"
                    )
            else:
                self.log("👆 用戶選擇「否」- 不開啟檔案")
            
            self.log("🎉 導出功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 測試過程發生錯誤: {e}")
            QMessageBox.critical(self, "測試失敗", f"測試過程發生錯誤：\n{str(e)}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = TestExportWindow()
    window.show()
    
    print("🚀 導出功能測試程式已啟動")
    print("📋 測試重點：")
    print("1. 導出成功後是否出現「是否要開啟檔案？」詢問")
    print("2. 選擇「是」時是否能正確開啟CSV檔案")
    print("3. 選擇「否」時是否不開啟檔案")
    print("4. 錯誤處理是否正常工作")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
