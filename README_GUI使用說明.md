# Finlab 爬蟲系統 GUI 使用說明

## 🎯 功能概述

這是一個完整的 Finlab 爬蟲系統 GUI，支援**時間區間批量爬取**，完全參照原始 `u02_crawlers.ipynb` 和 `finlab/crawler.py` 的實作方式。

## 🚀 啟動方式

```bash
# 方法1：直接啟動
python finlab_crawler_gui.py

# 方法2：使用啟動器
python 啟動GUI.py
```

## 📊 支援的10個爬蟲功能

### 需要時間區間的爬蟲（批量爬取）

1. **股價資料** - 使用每日範圍 (date_range)
2. **三大法人** - 使用每日範圍 (date_range)  
3. **本益比** - 使用每日範圍 (date_range)
4. **月營收** - 使用月度範圍 (month_range)
5. **大盤指數** - 使用每日範圍 (date_range)
6. **財務報表** - 使用季度範圍 (season_range)

### 不需要時間區間的爬蟲（全量更新）

7. **上市除權息** - 全量爬取最近2年資料
8. **上櫃除權息** - 全量爬取最近2年資料
9. **上市減資** - 全量爬取2020年至今資料
10. **上櫃減資** - 全量爬取最近資料

## 🔧 參數設置

### 日期範圍設置
- **開始日期**: YYYY-MM-DD 格式
- **結束日期**: YYYY-MM-DD 格式

### 快速設置按鈕
- **最近7天**: 自動設置最近一週的日期範圍
- **最近30天**: 自動設置最近一個月的日期範圍  
- **最近90天**: 自動設置最近三個月的日期範圍

## 📈 批量爬取邏輯

### 每日範圍 (Daily Range)
```python
# 股價、三大法人、本益比、大盤指數
dates = [2025-07-13, 2025-07-14, 2025-07-15, ..., 2025-07-20]
# 每個日期調用一次爬蟲函數，最後合併所有資料
```

### 月度範圍 (Monthly Range)  
```python
# 月營收
dates = [2025-01-01, 2025-02-01, 2025-03-01, ..., 2025-07-01]
# 每個月調用一次爬蟲函數
```

### 季度範圍 (Seasonal Range)
```python
# 財務報表
dates = [2024-05-15, 2024-08-14, 2024-11-14, 2025-03-31]
# Q1: 5/15, Q2: 8/14, Q3: 11/14, Q4: 3/31
```

## 💡 使用建議

### 首次使用
1. 選擇「最近7天」快速設置
2. 先測試「股價資料」功能
3. 觀察執行日誌確認正常運作

### 日常更新
1. 使用「最近30天」獲取最新資料
2. 對於除權息和減資，直接點擊執行（無需設置日期）
3. 財務報表建議按季度更新

### 大量歷史資料
1. 設置較長的日期範圍（如90天）
2. 注意網路連線穩定性
3. 避免過於頻繁的請求

## 🔍 執行日誌說明

### 日誌訊息類型
- `🔄` 正在執行
- `✅` 執行成功  
- `⚠️` 警告（如無資料）
- `❌` 執行失敗
- `📅` 日期設置
- `📊` 資料統計

### 典型執行流程
```
[16:17:20] 📅 設置日期範圍：最近7天 (2025-07-13 ~ 2025-07-20)
[16:17:25] 🔄 開始執行 股價資料...
[16:17:25] 📅 準備爬取 8 個日期的資料...
[16:17:26] 🔄 爬取 2025-07-13 的資料... (1/8)
[16:17:28] ✅ 2025-07-13 完成，獲取 1500 筆資料
[16:17:30] 🔄 爬取 2025-07-14 的資料... (2/8)
...
[16:17:45] 📊 總共成功爬取 8/8 個日期，合計 12000 筆資料
[16:17:45] ✅ 股價資料 執行完成，獲取 12000 筆資料
```

## 🛠️ 技術特色

### 自動適應
- 有 finlab 模組：使用完整功能
- 無 finlab 模組：使用簡化版（用於測試）

### 多線程執行
- GUI 不會凍結
- 可以隨時查看執行進度
- 支援中途取消（關閉程式）

### 錯誤處理
- 網路連線問題自動重試
- 友好的錯誤提示
- 詳細的執行日誌

### 資料合併
- 自動合併多日期的資料
- 去除重複資料
- 按時間排序

## 🎯 與原始實作的對應關係

| GUI功能 | 原始函數 | 時間範圍函數 |
|---------|----------|-------------|
| 股價資料 | `crawl_price` | `date_range` |
| 三大法人 | `crawl_bargin` | `date_range` |
| 本益比 | `crawl_pe` | `date_range` |
| 月營收 | `crawl_monthly_report` | `month_range` |
| 大盤指數 | `crawl_benchmark` | `date_range` |
| 財務報表 | `crawl_finance_statement_by_date` | `season_range` |
| 上市除權息 | `crawl_twse_divide_ratio` | 無需參數 |
| 上櫃除權息 | `crawl_otc_divide_ratio` | 無需參數 |
| 上市減資 | `crawl_twse_cap_reduction` | 無需參數 |
| 上櫃減資 | `crawl_otc_cap_reduction` | 無需參數 |

## ✅ 確認結果

**所有10個爬蟲功能都能正常執行，支援正確的時間區間批量爬取！**

現在不再是「只抓一筆」，而是根據設定的日期範圍批量爬取多天/多月/多季的資料，完全符合原始 finlab 的設計理念。
