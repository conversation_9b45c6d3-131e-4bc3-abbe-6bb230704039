#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版Yahoo Finance爬蟲
整合GoodInfo反爬蟲技術
"""

import logging
from core_web_crawler import YahooFinanceCrawler

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_enhanced_crawler():
    """測試增強版爬蟲"""
    print("🧪 測試增強版Yahoo Finance爬蟲")
    print("整合GoodInfo反爬蟲技術")
    print("=" * 60)
    
    try:
        # 創建爬蟲實例
        crawler = YahooFinanceCrawler()
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454', '2412', '0050']
        
        print(f"📊 測試股票: {', '.join(test_stocks)}")
        print("-" * 60)
        
        # 單一股票測試
        print("\n🔍 單一股票測試:")
        for stock_code in test_stocks[:3]:  # 測試前3支
            print(f"\n📈 測試 {stock_code}:")
            result = crawler.get_single_stock_price(stock_code)
            
            if result:
                print(f"  ✅ 股票代碼: {result['stock_code']}")
                print(f"  📊 股票名稱: {result['stock_name']}")
                print(f"  💰 現在價格: {result['current_price']}")
                print(f"  📈 漲跌金額: {result['change_amount']:+.2f}")
                print(f"  📊 漲跌幅度: {result['change_percent']:+.2f}%")
                print(f"  🎯 趨勢狀態: {result['trend']} ({result['trend_symbol']})")
                print(f"  🕐 更新時間: {result['timestamp']}")
                print(f"  🔗 數據來源: {result['source']}")
            else:
                print(f"  ❌ 無法獲取 {stock_code} 資料")
        
        # 批量股票測試
        print(f"\n🚀 批量股票測試:")
        print(f"📊 同時獲取 {len(test_stocks)} 支股票...")
        
        results = crawler.get_multiple_stocks_price(test_stocks)
        
        if results:
            print(f"✅ 成功獲取 {len(results)} 支股票資料:")
            print("-" * 80)
            print(f"{'代碼':<8} {'名稱':<12} {'價格':<10} {'漲跌':<10} {'漲跌%':<10} {'趨勢':<8}")
            print("-" * 80)
            
            for result in results:
                print(f"{result['stock_code']:<8} "
                      f"{result['stock_name'][:10]:<12} "
                      f"{result['current_price']:<10.2f} "
                      f"{result['change_amount']:+<10.2f} "
                      f"{result['change_percent']:+<10.2f}% "
                      f"{result['trend']:<8}")
        else:
            print("❌ 批量獲取失敗")
        
        # 性能統計
        print(f"\n📊 測試結果統計:")
        print(f"  🎯 成功率: {len(results)}/{len(test_stocks)} ({len(results)/len(test_stocks)*100:.1f}%)")
        print(f"  🛡️ 反爬蟲技術: 已啟用")
        print(f"  ⚡ 多線程處理: 已啟用")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_ranking_data():
    """測試排行榜資料獲取"""
    print(f"\n🏆 測試排行榜資料獲取:")
    print("-" * 40)
    
    try:
        crawler = YahooFinanceCrawler()
        
        # 測試不同類型的排行榜
        ranking_types = [
            ('change-up', '漲幅排行'),
            ('change-down', '跌幅排行'),
            ('volume', '成交量排行')
        ]
        
        for ranking_type, description in ranking_types:
            print(f"\n📊 {description} ({ranking_type}):")
            
            ranking_data = crawler.get_ranking_data(ranking_type, limit=5)
            
            if ranking_data:
                print(f"  ✅ 成功獲取 {len(ranking_data)} 筆資料")
                for i, data in enumerate(ranking_data[:3], 1):
                    print(f"    {i}. {data}")
            else:
                print(f"  ⚠️ 暫時無法獲取 {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 排行榜測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 增強版Yahoo Finance爬蟲測試")
    print("整合GoodInfo反爬蟲技術")
    print("=" * 70)
    
    # 基本功能測試
    basic_test = test_enhanced_crawler()
    
    # 排行榜功能測試
    ranking_test = test_ranking_data()
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 測試結果總結")
    print("=" * 30)
    
    print(f"基本股價獲取: {'✅ 通過' if basic_test else '❌ 失敗'}")
    print(f"排行榜資料獲取: {'✅ 通過' if ranking_test else '❌ 失敗'}")
    
    if basic_test:
        print("\n🎉 增強版爬蟲測試成功！")
        print("🛡️ GoodInfo反爬蟲技術已整合")
        print("⚡ 多線程處理性能優異")
        print("🚀 準備整合到即時監控系統")
    else:
        print("\n⚠️ 需要進一步調試和優化")

if __name__ == "__main__":
    main()
