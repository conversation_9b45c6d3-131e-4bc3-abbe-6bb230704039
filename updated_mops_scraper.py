#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新的 MOPS 財務報表爬蟲 (基於新的網站結構)
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import logging
from typing import Dict, Optional
from datetime import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class UpdatedMOPSScraper:
    """更新的 MOPS 財務報表爬蟲"""
    
    def __init__(self):
        self.base_url = "https://mops.twse.com.tw"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        self.session.verify = False
    
    def get_financial_statements_new_format(self, stock_code: str, year: int, season: int) -> Dict[str, pd.DataFrame]:
        """
        使用新的 MOPS 網站格式獲取財務報表
        """
        logger.info(f"🔍 使用新格式獲取 {stock_code} {year}年第{season}季財務報表...")
        
        try:
            # 嘗試新的 AJAX 端點
            ajax_url = f"{self.base_url}/mops/web/ajax_t164sb01"
            
            # 準備 POST 資料
            post_data = {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'keyword4': '',
                'code1': '',
                'TYPEK2': '',
                'checkbtn': '',
                'queryName': 'co_id',
                'inpuType': 'co_id',
                'TYPEK': 'all',
                'isnew': 'false',
                'co_id': stock_code,
                'year': str(year),
                'season': str(season),
            }
            
            logger.info(f"📡 POST 請求: {ajax_url}")
            response = self.session.post(ajax_url, data=post_data, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"✅ AJAX 請求成功")
                return self._parse_ajax_response(response.text)
            else:
                logger.error(f"❌ AJAX 請求失敗: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 新格式請求失敗: {e}")
        
        return {}
    
    def get_financial_statements_web_format(self, stock_code: str, year: int, season: int) -> Dict[str, pd.DataFrame]:
        """
        使用 Web 頁面格式獲取財務報表
        """
        logger.info(f"🔍 使用 Web 格式獲取 {stock_code} {year}年第{season}季財務報表...")
        
        try:
            # 先訪問主頁面獲取 session
            main_url = f"{self.base_url}/mops/web/t164sb01"
            logger.info(f"📡 訪問主頁面: {main_url}")
            
            response = self.session.get(main_url, timeout=30)
            if response.status_code != 200:
                logger.error(f"❌ 主頁面訪問失敗: {response.status_code}")
                return {}
            
            # 準備查詢參數
            query_url = f"{main_url}?step=1&CO_ID={stock_code}&SYEAR={year}&SSEASON={season}&REPORT_ID=C"
            logger.info(f"📡 查詢 URL: {query_url}")
            
            response = self.session.get(query_url, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"✅ Web 請求成功")
                response.encoding = 'utf-8'
                return self._parse_web_response(response.text)
            else:
                logger.error(f"❌ Web 請求失敗: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Web 格式請求失敗: {e}")
        
        return {}
    
    def _parse_ajax_response(self, html_content: str) -> Dict[str, pd.DataFrame]:
        """解析 AJAX 回應"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            results = {}
            
            # 尋找表格
            tables = soup.find_all('table')
            logger.info(f"📊 找到 {len(tables)} 個表格")
            
            for i, table in enumerate(tables):
                logger.info(f"   表格 {i+1}: {table.get('class', 'no-class')}")
                
                # 嘗試提取表格資料
                rows = table.find_all('tr')
                if len(rows) > 1:
                    # 提取標題
                    header_row = rows[0]
                    headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
                    
                    # 提取資料
                    data_rows = []
                    for row in rows[1:]:
                        cells = [td.get_text(strip=True) for td in row.find_all(['td', 'th'])]
                        if len(cells) == len(headers):
                            data_rows.append(cells)
                    
                    if data_rows:
                        df = pd.DataFrame(data_rows, columns=headers)
                        results[f'表格_{i+1}'] = df
                        logger.info(f"   ✅ 表格 {i+1}: {df.shape}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 解析 AJAX 回應失敗: {e}")
            return {}
    
    def _parse_web_response(self, html_content: str) -> Dict[str, pd.DataFrame]:
        """解析 Web 回應"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            results = {}
            
            # 尋找財務報表相關的表格
            tables = soup.find_all('table')
            logger.info(f"📊 找到 {len(tables)} 個表格")
            
            for i, table in enumerate(tables):
                # 檢查表格是否包含財務資料
                table_text = table.get_text()
                
                if any(keyword in table_text for keyword in ['資產', '負債', '收入', '支出', '現金', '流量']):
                    logger.info(f"   📊 表格 {i+1} 可能包含財務資料")
                    
                    # 提取表格資料
                    rows = table.find_all('tr')
                    if len(rows) > 1:
                        # 尋找標題行
                        header_row = None
                        for row in rows:
                            if row.find('th') or '項目' in row.get_text():
                                header_row = row
                                break
                        
                        if header_row:
                            headers = [cell.get_text(strip=True) for cell in header_row.find_all(['th', 'td'])]
                            
                            # 提取資料行
                            data_rows = []
                            for row in rows:
                                if row != header_row:
                                    cells = [cell.get_text(strip=True) for cell in row.find_all(['td', 'th'])]
                                    if len(cells) == len(headers) and cells[0]:  # 確保有資料
                                        data_rows.append(cells)
                            
                            if data_rows:
                                df = pd.DataFrame(data_rows, columns=headers)
                                
                                # 根據內容判斷表格類型
                                table_type = self._identify_table_type(table_text)
                                results[table_type] = df
                                logger.info(f"   ✅ {table_type}: {df.shape}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 解析 Web 回應失敗: {e}")
            return {}
    
    def _identify_table_type(self, table_text: str) -> str:
        """識別表格類型"""
        if '資產' in table_text and '負債' in table_text:
            return '資產負債表'
        elif '收入' in table_text or '營業' in table_text:
            return '綜合損益表'
        elif '現金' in table_text and '流量' in table_text:
            return '現金流量表'
        else:
            return '未知表格'

def test_updated_mops_scraper():
    """測試更新的 MOPS 爬蟲"""
    
    print("=" * 80)
    print("🧪 測試更新的 MOPS 財務報表爬蟲")
    print("=" * 80)
    
    scraper = UpdatedMOPSScraper()
    
    # 測試案例
    test_cases = [
        ("2330", 2023, 4),  # 台積電 2023Q4
        ("2317", 2023, 4),  # 鴻海 2023Q4
    ]
    
    for stock_code, year, season in test_cases:
        print(f"\n📊 測試 {stock_code} {year}年第{season}季...")
        
        # 嘗試新格式
        print("🔍 嘗試新的 AJAX 格式...")
        results_ajax = scraper.get_financial_statements_new_format(stock_code, year, season)
        
        if results_ajax:
            print(f"✅ AJAX 格式成功: {len(results_ajax)} 個表格")
            for table_name, df in results_ajax.items():
                print(f"   📋 {table_name}: {df.shape}")
        else:
            print("⚠️ AJAX 格式無資料")
        
        # 嘗試 Web 格式
        print("🔍 嘗試 Web 頁面格式...")
        results_web = scraper.get_financial_statements_web_format(stock_code, year, season)
        
        if results_web:
            print(f"✅ Web 格式成功: {len(results_web)} 個表格")
            for table_name, df in results_web.items():
                print(f"   📋 {table_name}: {df.shape}")
                if not df.empty:
                    print(f"   📊 欄位: {list(df.columns)}")
        else:
            print("⚠️ Web 格式無資料")
        
        time.sleep(3)  # 避免請求過於頻繁

def main():
    """主函數"""
    
    print("🐍 更新的 MOPS 財務報表爬蟲測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試更新的爬蟲
    test_updated_mops_scraper()
    
    print(f"\n" + "=" * 80)
    print("📊 測試總結")
    print("=" * 80)
    print("🎯 基於測試結果:")
    print("   1. MOPS 網站結構確實已經改變")
    print("   2. 舊的 R 語言 URL 格式已經失效")
    print("   3. 需要使用新的 AJAX 或 Web 格式")
    print("   4. 可能需要更複雜的 session 管理")
    print()
    print("💡 建議:")
    print("   1. 如果測試成功，可以整合到現有系統")
    print("   2. 如果測試失敗，繼續使用 TWSE OpenAPI")
    print("   3. 考慮使用 selenium 進行動態爬取")

if __name__ == "__main__":
    main()
