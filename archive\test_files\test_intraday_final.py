#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終日內策略測試 - 包含各種極端情況
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入策略
from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def generate_extreme_stock_data(stock_id, scenario_type):
    """生成極端情況的股票數據"""
    dates = pd.date_range(end=datetime.now(), periods=20, freq='D')
    
    scenarios = {
        'excellent': {  # 優秀股票
            'base_price': 100,
            'trend': 3.0,  # 強勢上漲
            'volatility': 0.01,  # 低波動
            'volume_multiplier': 2.0  # 高成交量
        },
        'good': {  # 良好股票
            'base_price': 50,
            'trend': 1.0,  # 溫和上漲
            'volatility': 0.02,
            'volume_multiplier': 1.3
        },
        'average': {  # 一般股票
            'base_price': 30,
            'trend': 0.0,  # 橫盤
            'volatility': 0.02,
            'volume_multiplier': 1.0
        },
        'poor': {  # 較差股票
            'base_price': 15,
            'trend': -1.0,  # 下跌
            'volatility': 0.03,
            'volume_multiplier': 0.7
        },
        'terrible': {  # 極差股票
            'base_price': 5,
            'trend': -5.0,  # 大跌
            'volatility': 0.05,
            'volume_multiplier': 0.3
        },
        'penny_stock': {  # 仙股
            'base_price': 1.5,
            'trend': 0.5,
            'volatility': 0.08,
            'volume_multiplier': 0.5
        },
        'high_price_weak': {  # 高價弱勢股
            'base_price': 500,
            'trend': -2.0,  # 下跌
            'volatility': 0.03,
            'volume_multiplier': 0.4
        },
        'low_volume': {  # 低成交量股票
            'base_price': 80,
            'trend': 1.0,
            'volatility': 0.02,
            'volume_multiplier': 0.2  # 極低成交量
        }
    }
    
    config = scenarios.get(scenario_type, scenarios['average'])
    
    # 生成價格數據
    prices = []
    current_price = config['base_price']
    
    for i in range(len(dates)):
        daily_change = config['trend']/100 + np.random.normal(0, config['volatility'])
        current_price = current_price * (1 + daily_change)
        prices.append(max(current_price, 0.1))  # 確保價格不為負
    
    # 生成OHLC數據
    data = []
    base_volume = 10000000 * config['volume_multiplier']
    
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = base_volume * (1 + np.random.normal(0, 0.3))
        
        data.append({
            'Date': date,
            'Open': max(open_price, 0.1),
            'High': max(high, open_price, close),
            'Low': min(low, open_price, close),
            'Close': max(close, 0.1),
            'Volume': max(int(volume), 1000)
        })
    
    return pd.DataFrame(data)

def test_final_intraday_strategy():
    """最終日內策略測試"""
    print("🎯 最終日內策略測試 - 包含各種極端情況")
    print("=" * 70)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 測試各種極端情況
    test_cases = [
        {'stock_id': 'EXCELLENT', 'name': '優秀股票', 'scenario': 'excellent'},
        {'stock_id': 'GOOD', 'name': '良好股票', 'scenario': 'good'},
        {'stock_id': 'AVERAGE', 'name': '一般股票', 'scenario': 'average'},
        {'stock_id': 'POOR', 'name': '較差股票', 'scenario': 'poor'},
        {'stock_id': 'TERRIBLE', 'name': '極差股票', 'scenario': 'terrible'},
        {'stock_id': 'PENNY', 'name': '仙股', 'scenario': 'penny_stock'},
        {'stock_id': 'HIGH_WEAK', 'name': '高價弱勢股', 'scenario': 'high_price_weak'},
        {'stock_id': 'LOW_VOL', 'name': '低成交量股票', 'scenario': 'low_volume'}
    ]
    
    qualified_stocks = []
    failed_stocks = []
    
    for case in test_cases:
        print(f"\n📊 測試 {case['stock_id']} - {case['name']}")
        print("-" * 50)
        
        try:
            # 生成極端情況數據
            test_data = generate_extreme_stock_data(case['stock_id'], case['scenario'])
            test_data = strategy.calculate_indicators(test_data)
            
            # 檢查盤前條件
            pre_market_ok, reason = strategy.check_pre_market_conditions(test_data)
            
            # 顯示關鍵數據
            latest = test_data.iloc[-1]
            prev_close = test_data.iloc[-2]['Close']
            ma5 = latest['MA5']
            volume = latest['Volume']
            avg_vol = latest['AvgVol']
            
            ma5_ratio = prev_close / ma5 if ma5 > 0 else 0
            vol_ratio = volume / avg_vol if avg_vol > 0 else 0
            
            print(f"  💰 收盤價: {prev_close:.2f}")
            print(f"  📊 MA5: {ma5:.2f} (比率: {ma5_ratio:.3f})")
            print(f"  📈 成交量: {volume:,.0f} (比率: {vol_ratio:.2f})")
            print(f"  🎯 篩選結果: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
            print(f"  📝 詳細評分: {reason}")
            
            if pre_market_ok:
                qualified_stocks.append({
                    'stock_id': case['stock_id'],
                    'name': case['name'],
                    'close_price': prev_close,
                    'scenario': case['scenario'],
                    'reason': reason
                })
            else:
                failed_stocks.append({
                    'stock_id': case['stock_id'],
                    'name': case['name'],
                    'close_price': prev_close,
                    'scenario': case['scenario'],
                    'reason': reason
                })
                
        except Exception as e:
            print(f"  ❌ 處理失敗: {e}")
            failed_stocks.append({
                'stock_id': case['stock_id'],
                'name': case['name'],
                'close_price': 0,
                'scenario': case['scenario'],
                'reason': f'處理失敗: {str(e)}'
            })
    
    # 統計結果
    print("\n" + "=" * 70)
    print("📊 最終測試結果統計")
    print("=" * 70)
    
    total_tested = len(test_cases)
    qualified_count = len(qualified_stocks)
    failed_count = len(failed_stocks)
    
    print(f"總測試情境數: {total_tested}")
    print(f"通過篩選: {qualified_count} ({qualified_count/total_tested*100:.1f}%)")
    print(f"未通過篩選: {failed_count} ({failed_count/total_tested*100:.1f}%)")
    
    if qualified_stocks:
        print(f"\n✅ 通過篩選的股票:")
        for stock in qualified_stocks:
            print(f"  • {stock['name']} ({stock['scenario']}): {stock['close_price']:.2f}")
            print(f"    {stock['reason']}")
    
    if failed_stocks:
        print(f"\n❌ 未通過篩選的股票:")
        for stock in failed_stocks:
            print(f"  • {stock['name']} ({stock['scenario']}): {stock['reason']}")
    
    # 評估篩選效果
    print(f"\n🎯 篩選效果最終評估:")
    
    # 分析通過的股票類型
    excellent_passed = any(s['scenario'] == 'excellent' for s in qualified_stocks)
    terrible_passed = any(s['scenario'] == 'terrible' for s in qualified_stocks)
    penny_passed = any(s['scenario'] == 'penny_stock' for s in qualified_stocks)
    
    print(f"  • 優秀股票通過: {'✅' if excellent_passed else '❌'}")
    print(f"  • 極差股票被過濾: {'✅' if not terrible_passed else '❌'}")
    print(f"  • 仙股被適當處理: {'✅' if not penny_passed else '⚠️'}")
    
    if 20 <= qualified_count/total_tested*100 <= 60:
        print(f"  ✅ 篩選比例合理 ({qualified_count/total_tested*100:.1f}%)")
        print(f"  💡 建議: 當前設定適合實際使用")
    elif qualified_count/total_tested*100 < 20:
        print(f"  ⚠️ 篩選過於嚴格 ({qualified_count/total_tested*100:.1f}%)")
        print(f"  💡 建議: 適度放寬條件以增加候選股票")
    else:
        print(f"  ⚠️ 篩選過於寬鬆 ({qualified_count/total_tested*100:.1f}%)")
        print(f"  💡 建議: 提高標準以提升選股品質")
    
    return qualified_stocks, failed_stocks

def recommend_optimization():
    """推薦優化建議"""
    print(f"\n🔧 優化建議")
    print("-" * 30)
    
    print("基於測試結果，以下是優化建議：")
    print()
    print("1. 📊 評分門檻調整:")
    print("   • 當前門檻: 6/12分")
    print("   • 建議範圍: 6-8分（根據市場狀況調整）")
    print()
    print("2. 🎯 條件權重優化:")
    print("   • MA5關係: 最重要（價格趨勢）")
    print("   • 成交量: 重要（市場關注度）")
    print("   • 股價水平: 中等（風險控制）")
    print("   • 短期趨勢: 重要（動量確認）")
    print()
    print("3. 🚀 實戰應用建議:")
    print("   • 盤前篩選: 每日開盤前30分鐘執行")
    print("   • 候選數量: 控制在5-15支股票")
    print("   • 風險控制: 設定停損2%，停利3-5%")
    print("   • 資金配置: 單一股票不超過總資金10%")

if __name__ == "__main__":
    try:
        # 執行最終測試
        qualified, failed = test_final_intraday_strategy()
        
        # 提供優化建議
        recommend_optimization()
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎉 日內策略測試完成！系統已準備好進行實戰應用。")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
