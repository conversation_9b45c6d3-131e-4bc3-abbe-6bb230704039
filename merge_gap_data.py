#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併空檔填補後的資料
將 price_v2.pkl 中的新資料合併到 price.pkl
"""

import pandas as pd
import os
import shutil
from datetime import datetime
import gc

def merge_gap_data():
    """合併空檔填補的資料"""
    
    print("=" * 60)
    print("🔄 合併空檔填補資料")
    print("=" * 60)
    
    price_file = "history/tables/price.pkl"
    price_v2_file = "history/tables/price_v2.pkl"
    backup_file = "history/tables/price_backup_after_gap_fill.pkl"
    
    # 檢查檔案
    if not os.path.exists(price_file):
        print(f"❌ {price_file} 不存在")
        return False
    
    if not os.path.exists(price_v2_file):
        print(f"❌ {price_v2_file} 不存在")
        return False
    
    try:
        # 1. 創建備份
        print("1️⃣ 創建備份...")
        shutil.copy2(price_file, backup_file)
        print(f"✅ 備份創建: {backup_file}")
        
        # 2. 讀取主檔案
        print("2️⃣ 讀取主檔案...")
        main_data = pd.read_pickle(price_file)
        main_dates = main_data.index.get_level_values('date')
        print(f"✅ 主檔案: {len(main_data):,} 筆")
        print(f"   日期範圍: {main_dates.min()} 至 {main_dates.max()}")
        
        # 3. 讀取新資料
        print("3️⃣ 讀取新填補資料...")
        new_data = pd.read_pickle(price_v2_file)
        new_dates = new_data.index.get_level_values('date')
        print(f"✅ 新資料: {len(new_data):,} 筆")
        print(f"   日期範圍: {new_dates.min()} 至 {new_dates.max()}")
        
        # 4. 檢查空檔期間的資料
        gap_start = datetime(2022, 9, 16)
        gap_end = datetime(2022, 12, 1)
        
        gap_data_in_new = new_data[
            (new_data.index.get_level_values('date') >= gap_start) & 
            (new_data.index.get_level_values('date') <= gap_end)
        ]
        
        print(f"4️⃣ 空檔期間新資料: {len(gap_data_in_new):,} 筆")
        
        if len(gap_data_in_new) == 0:
            print("⚠️ 沒有找到空檔期間的資料")
            return False
        
        gap_dates_new = gap_data_in_new.index.get_level_values('date').unique()
        print(f"   空檔日期數: {len(gap_dates_new)}")
        print(f"   日期範圍: {gap_dates_new.min()} 至 {gap_dates_new.max()}")
        
        # 5. 合併資料
        print("5️⃣ 合併資料...")
        
        # 移除主檔案中可能重疊的日期
        main_dates_set = set(main_data.index.get_level_values('date'))
        gap_dates_set = set(gap_dates_new)
        overlap = main_dates_set.intersection(gap_dates_set)
        
        if overlap:
            print(f"   移除重疊日期: {len(overlap)} 個")
            main_data_filtered = main_data[~main_data.index.get_level_values('date').isin(overlap)]
        else:
            print("   沒有重疊日期")
            main_data_filtered = main_data
        
        # 合併
        merged_data = pd.concat([main_data_filtered, gap_data_in_new])
        print(f"✅ 合併完成: {len(merged_data):,} 筆")
        
        # 清理記憶體
        del main_data, new_data, main_data_filtered, gap_data_in_new
        gc.collect()
        
        # 6. 去重並排序
        print("6️⃣ 去重並排序...")
        merged_data = merged_data[~merged_data.index.duplicated(keep='last')]
        merged_data = merged_data.sort_index()
        
        final_dates = merged_data.index.get_level_values('date')
        print(f"✅ 最終資料: {len(merged_data):,} 筆")
        print(f"   日期範圍: {final_dates.min()} 至 {final_dates.max()}")
        
        # 7. 保存
        print("7️⃣ 保存合併資料...")
        merged_data.to_pickle(price_file)
        print(f"✅ 已保存到: {price_file}")
        
        # 8. 驗證空檔
        print("8️⃣ 驗證空檔填補...")
        gap_dates_in_final = [d for d in final_dates.unique() if gap_start <= d <= gap_end]
        print(f"✅ 空檔期間資料: {len(gap_dates_in_final)} 個日期")
        
        if len(gap_dates_in_final) > 0:
            print(f"   填補日期範圍: {min(gap_dates_in_final)} 至 {max(gap_dates_in_final)}")
            print("🎉 空檔填補成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        
        # 恢復備份
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, price_file)
            print("✅ 已恢復備份")
        
        return False

def main():
    """主函數"""
    print("🔧 空檔資料合併工具")
    print("=" * 60)
    
    success = merge_gap_data()
    
    if success:
        print("\n🎉 合併成功！")
        print("💡 現在 price.pkl 包含完整的連續資料")
        print("✨ 可以重新啟動 auto_update.py 繼續更新")
    else:
        print("\n❌ 合併失敗")

if __name__ == "__main__":
    main()
