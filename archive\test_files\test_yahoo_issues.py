#!/usr/bin/env python3
"""
測試Yahoo數據源問題並展示優化方案
"""

import logging
import time
import sys
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_yahoo_finance_issues():
    """測試Yahoo Finance的問題"""
    print("🔍 測試Yahoo Finance數據源問題")
    print("=" * 60)
    
    # 從您的錯誤日誌中提取的問題股票
    problem_stocks = [
        '6218', '8277', '8358', '9951', '5343', 
        '4760', '6290', '3213', '4971', '5536', '6877'
    ]
    
    try:
        import yfinance as yf
        print("✅ yfinance 模組已安裝")
        
        failed_stocks = []
        success_stocks = []
        
        for stock_id in problem_stocks:
            print(f"\n📊 測試股票: {stock_id}")
            
            # 測試不同的股票代碼格式
            formats = [f"{stock_id}.TW", f"{stock_id}.TWO"]
            
            stock_success = False
            for format_code in formats:
                try:
                    print(f"  🔄 嘗試格式: {format_code}")
                    
                    ticker = yf.Ticker(format_code)
                    data = ticker.history(period='1d')
                    
                    if not data.empty:
                        print(f"  ✅ 成功獲取數據: {len(data)} 條記錄")
                        success_stocks.append(stock_id)
                        stock_success = True
                        break
                    else:
                        print(f"  ⚠️ 數據為空")
                        
                except Exception as e:
                    print(f"  ❌ 錯誤: {e}")
            
            if not stock_success:
                failed_stocks.append(stock_id)
                print(f"  💥 股票 {stock_id} 完全失敗")
        
        # 統計結果
        print(f"\n📊 測試結果統計:")
        print(f"  • 測試股票總數: {len(problem_stocks)}")
        print(f"  • 成功獲取: {len(success_stocks)}")
        print(f"  • 失敗股票: {len(failed_stocks)}")
        print(f"  • 失敗率: {len(failed_stocks)/len(problem_stocks)*100:.1f}%")
        
        if failed_stocks:
            print(f"\n❌ 失敗的股票代碼:")
            for stock in failed_stocks:
                print(f"  • {stock}")
        
        if success_stocks:
            print(f"\n✅ 成功的股票代碼:")
            for stock in success_stocks:
                print(f"  • {stock}")
        
        return len(failed_stocks) / len(problem_stocks)
        
    except ImportError:
        print("❌ yfinance 模組未安裝")
        return 1.0

def test_alternative_sources():
    """測試替代數據源"""
    print("\n🔄 測試替代數據源")
    print("=" * 60)
    
    test_stocks = ['2330', '2317', '2454']  # 使用常見的大型股測試
    
    # 測試證交所API
    print("\n🏛️ 測試證交所即時API:")
    try:
        import requests
        
        for stock_id in test_stocks:
            url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"
            params = {
                'ex_ch': f'tse_{stock_id}.tw',
                'json': '1',
                'delay': '0'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'msgArray' in data and data['msgArray']:
                    print(f"  ✅ {stock_id}: 證交所API正常")
                else:
                    print(f"  ⚠️ {stock_id}: 證交所API無數據")
            else:
                print(f"  ❌ {stock_id}: 證交所API請求失敗")
                
    except Exception as e:
        print(f"  ❌ 證交所API測試失敗: {e}")
    
    # 測試twstock
    print("\n📈 測試twstock庫:")
    try:
        import twstock
        
        for stock_id in test_stocks:
            try:
                stock = twstock.Stock(stock_id)
                if stock:
                    print(f"  ✅ {stock_id}: twstock正常")
                else:
                    print(f"  ⚠️ {stock_id}: twstock無數據")
            except Exception as e:
                print(f"  ❌ {stock_id}: twstock錯誤 - {e}")
                
    except ImportError:
        print("  ⚠️ twstock 模組未安裝")
    except Exception as e:
        print(f"  ❌ twstock測試失敗: {e}")

def demonstrate_optimization():
    """展示優化方案"""
    print("\n💡 Yahoo數據源問題分析與優化建議")
    print("=" * 60)
    
    print("\n❌ Yahoo Finance 的主要問題:")
    print("  1. 台股支援不完整 - 很多股票被誤判為下市")
    print("  2. 數據格式不一致 - 經常返回無法解析的數據")
    print("  3. 連接不穩定 - 頻繁出現連接中斷")
    print("  4. 反爬蟲限制 - 請求過多時被封鎖")
    print("  5. 更新延遲 - 台股數據更新不及時")
    
    print("\n✅ 推薦的替代方案:")
    print("  1. 🏛️ 證交所官方API - 最權威、最準確")
    print("     • 即時數據: https://mis.twse.com.tw/stock/api/getStockInfo.jsp")
    print("     • 歷史數據: 證交所日報表API")
    print("     • 優點: 官方數據、免費、穩定")
    
    print("\n  2. 📊 Twelve Data API - 專業金融數據")
    print("     • 支援台股數據")
    print("     • 免費版每天800次請求")
    print("     • 數據格式標準化")
    
    print("\n  3. 📈 twstock 台股專用庫")
    print("     • 專為台股設計")
    print("     • 支援即時和歷史數據")
    print("     • 無API限制")
    
    print("\n  4. 💾 本地數據庫緩存")
    print("     • 減少外部API依賴")
    print("     • 提高響應速度")
    print("     • 離線可用")
    
    print("\n🔧 優化策略:")
    print("  1. 禁用所有Yahoo相關數據源")
    print("  2. 設置數據源優先級: 證交所 > Twelve Data > twstock > 數據庫")
    print("  3. 實現智能容錯和自動切換")
    print("  4. 添加數據緩存機制")
    print("  5. 監控數據源健康狀態")

def show_optimization_files():
    """展示優化文件"""
    print("\n📁 已創建的優化文件:")
    print("=" * 60)
    
    files = [
        {
            'name': 'optimized_data_fetcher.py',
            'description': '優化版數據獲取器 - 移除Yahoo，專注可靠數據源'
        },
        {
            'name': 'data_source_config.py', 
            'description': '數據源配置管理 - 禁用Yahoo，配置優先級'
        },
        {
            'name': 'remove_yahoo_patch.py',
            'description': 'Yahoo移除修補程序 - 自動修改現有代碼'
        },
        {
            'name': 'test_yahoo_issues.py',
            'description': '測試程序 - 驗證Yahoo問題並展示優化方案'
        }
    ]
    
    for i, file_info in enumerate(files, 1):
        print(f"  {i}. {file_info['name']}")
        print(f"     {file_info['description']}")
        print()

def main():
    """主函數"""
    print("🚀 Yahoo數據源問題分析與優化方案")
    print("=" * 80)
    
    # 1. 測試Yahoo Finance問題
    failure_rate = test_yahoo_finance_issues()
    
    # 2. 測試替代數據源
    test_alternative_sources()
    
    # 3. 展示優化方案
    demonstrate_optimization()
    
    # 4. 展示優化文件
    show_optimization_files()
    
    # 5. 總結建議
    print("\n🎯 總結與建議:")
    print("=" * 60)
    
    if failure_rate > 0.5:
        print(f"❌ Yahoo Finance 失敗率高達 {failure_rate*100:.1f}%，強烈建議禁用")
    elif failure_rate > 0.2:
        print(f"⚠️ Yahoo Finance 失敗率 {failure_rate*100:.1f}%，建議禁用")
    else:
        print(f"✅ Yahoo Finance 失敗率 {failure_rate*100:.1f}%，可考慮保留但降低優先級")
    
    print("\n📋 立即行動建議:")
    print("  1. 運行 remove_yahoo_patch.py 自動禁用Yahoo數據源")
    print("  2. 使用 optimized_data_fetcher.py 替換現有獲取器")
    print("  3. 配置 data_source_config.py 設置數據源優先級")
    print("  4. 測試系統確保數據獲取正常")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 分析完成！建議立即實施優化方案。")

if __name__ == "__main__":
    main()
