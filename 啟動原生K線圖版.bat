@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 原生K線圖版

echo.
echo ========================================
echo    台股智能選股系統 v22.3 - 原生K線圖版
echo ========================================
echo.

if exist "dist\\StockAnalyzer_NativeKLine.exe" (
    echo ✅ 找到原生K線圖版
    echo 🚀 正在啟動...
    echo.
    echo 💡 原生K線圖版特點：
    echo    ✓ 使用PyQt6原生繪圖引擎
    echo    ✓ 不依賴pyqtgraph模組
    echo    ✓ 包含雙週線MA10指標
    echo    ✓ 完整的K線圖和成交量顯示
    echo    ✓ 穩定的系統運行
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_NativeKLine.exe"
    
    echo ✅ 原生K線圖版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到原生K線圖版
    echo.
    echo 請重新編譯：
    echo    python compile_native_kline.py
    echo.
    pause
    exit /b 1
)

echo 📋 原生K線圖功能：
echo    ✓ 完整的K線蠟燭圖顯示
echo    ✓ 6條移動平均線（含雙週線MA10）
echo    ✓ 成交量柱狀圖
echo    ✓ 價格軸和網格線
echo    ✓ 股票信息顯示
echo.

timeout /t 3 >nul
