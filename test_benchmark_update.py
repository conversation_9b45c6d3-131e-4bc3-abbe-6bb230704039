#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 benchmark 更新功能
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def check_benchmark_status():
    """檢查 benchmark 檔案狀況"""
    print("🔍 檢查 benchmark 檔案狀況")
    print("=" * 50)
    
    try:
        import pickle
        import pandas as pd
        
        # 檢查 date_range.pickle
        date_range_file = "history/date_range.pickle"
        benchmark_file = "history/tables/benchmark.pkl"
        
        if os.path.exists(date_range_file):
            with open(date_range_file, 'rb') as f:
                date_ranges = pickle.load(f)
            
            if 'benchmark' in date_ranges:
                start_date, end_date = date_ranges['benchmark']
                print(f"📅 記錄的日期範圍: {start_date} 至 {end_date}")
                
                # 計算需要更新的天數
                today = datetime.now().date()
                end_date_only = end_date.date() if hasattr(end_date, 'date') else end_date
                days_behind = (today - end_date_only).days
                
                print(f"📊 落後天數: {days_behind} 天")
                return end_date, days_behind > 0
            else:
                print("❌ 沒有 benchmark 記錄")
                return None, True
        else:
            print("❌ date_range.pickle 不存在")
            return None, True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return None, True

def test_benchmark_crawl():
    """測試 benchmark 爬蟲功能"""
    print("\n🧪 測試 benchmark 爬蟲功能")
    print("=" * 50)
    
    try:
        from crawler import crawl_benchmark
        
        # 測試爬取最近一個交易日
        test_date = datetime.now() - timedelta(days=1)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("🔄 開始爬取...")
        result = crawl_benchmark(test_date)
        
        if result is not None and len(result) > 0:
            print(f"✅ 爬取成功: {len(result)} 筆資料")
            print(f"   欄位: {list(result.columns)[:5]}...")
            print(f"   索引: {result.index.names}")
            
            # 檢查台股指數
            if '發行量加權股價指數' in result.columns:
                taiex = result['發行量加權股價指數'].iloc[0]
                print(f"   台股指數: {taiex:.2f}")
            
            return True
        else:
            print("❌ 爬取失敗或無資料")
            return False
            
    except Exception as e:
        print(f"❌ 爬蟲測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_benchmark_update_with_batch():
    """測試帶分批存檔的 benchmark 更新"""
    print("\n🧪 測試分批更新 benchmark")
    print("=" * 50)
    
    try:
        from crawler import update_table, crawl_benchmark
        import pandas as pd
        
        # 獲取需要更新的日期範圍
        end_date, needs_update = check_benchmark_status()
        
        if not needs_update:
            print("✅ benchmark 已是最新，無需更新")
            return True
        
        # 計算更新日期範圍（只測試最近10個交易日）
        if end_date:
            start_date = end_date + timedelta(days=1)
        else:
            start_date = datetime.now() - timedelta(days=15)
        
        end_test_date = datetime.now()
        
        # 生成日期範圍
        date_range = pd.date_range(start=start_date, end=end_test_date, freq='D')
        # 只取工作日
        date_range = [d for d in date_range if d.weekday() < 5]
        
        # 限制為最多10個日期進行測試
        test_dates = date_range[-10:] if len(date_range) > 10 else date_range
        
        print(f"📅 測試更新日期: {len(test_dates)} 個交易日")
        print(f"   從 {test_dates[0].strftime('%Y-%m-%d')} 到 {test_dates[-1].strftime('%Y-%m-%d')}")
        
        if len(test_dates) == 0:
            print("✅ 沒有需要更新的日期")
            return True
        
        # 確認是否繼續
        response = input(f"\n是否開始測試更新 {len(test_dates)} 個日期？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 測試已取消")
            return False
        
        # 執行更新（會自動每10筆存檔）
        print(f"\n🚀 開始更新...")
        update_table('benchmark', crawl_benchmark, test_dates)
        
        print("✅ 測試更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("🔧 Benchmark 更新測試工具")
    print("=" * 60)
    print("🎯 目標: 測試 benchmark 更新功能並設置分批存檔")
    print("=" * 60)
    
    # 測試1: 檢查檔案狀況
    end_date, needs_update = check_benchmark_status()
    
    # 測試2: 測試爬蟲功能
    crawl_ok = test_benchmark_crawl()
    
    if crawl_ok:
        # 測試3: 測試分批更新
        update_ok = test_benchmark_update_with_batch()
        
        if update_ok:
            print("\n🎉 所有測試通過！")
            print("💡 現在可以在 auto_update.py 中啟用 benchmark 更新:")
            print("   ('benchmark', crawl_benchmark, date_range),")
            print("✨ 系統會自動每10筆存檔，降低風險")
        else:
            print("\n⚠️ 更新測試失敗")
    else:
        print("\n❌ 爬蟲測試失敗，無法進行更新")

if __name__ == "__main__":
    main()
