#!/usr/bin/env python3
"""
測試看盤重點跑馬燈功能
"""

import sys
import logging
from datetime import datetime, time
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QTextEdit, QStatusBar, QMenuBar, QMessageBox
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QFont

# 設置日誌
logging.basicConfig(level=logging.INFO)

class TradingTipsTestWindow(QMainWindow):
    """測試看盤重點跑馬燈的主視窗"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("📊 看盤重點跑馬燈測試")
        self.setGeometry(100, 100, 1000, 700)
        
        self.init_ui()
        self.init_trading_ticker()
        
    def init_ui(self):
        """初始化界面"""
        # 中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📊 看盤重點跑馬燈功能測試")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 說明
        desc = QLabel("測試台股盤中五大時段的自動提醒功能")
        desc.setStyleSheet("font-size: 12px; color: #666; margin: 5px;")
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(desc)
        
        # 當前時間顯示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #28a745; margin: 10px;")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.time_label)
        
        # 看盤重點內容
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h3 style="color: #2E86AB;">📊 台股看盤重點 - 五大時段</h3>
        
        <h4 style="color: #dc3545;">🌅 1. 盤前集合競價（08:30–09:00）</h4>
        <p><strong>重點</strong>：國際盤勢連動、籌碼變動、模擬撮合參考</p>
        
        <h4 style="color: #28a745;">🌄 2. 早盤時段（09:00–10:30）</h4>
        <p><strong>重點</strong>：開盤跳空反應、主力動態、飆股機會</p>
        
        <h4 style="color: #fd7e14;">☀️ 3. 中盤時段（10:30–12:00）</h4>
        <p><strong>重點</strong>：盤整延續、消息面影響、套利機會</p>
        
        <h4 style="color: #6f42c1;">🌇 4. 午盤時段（12:00–13:30）</h4>
        <p><strong>重點</strong>：午盤變勢、期指影響、資金移轉</p>
        
        <h4 style="color: #e83e8c;">🌆 5. 收盤前試撮（13:25–13:30）</h4>
        <p><strong>重點</strong>：收盤價博弈、價格穩定措施、盤後零股</p>
        """)
        layout.addWidget(content)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        
        self.enable_btn = QPushButton("🎯 啟用跑馬燈")
        self.enable_btn.clicked.connect(self.enable_trading_ticker)
        self.enable_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; }")
        
        self.disable_btn = QPushButton("⏹️ 停用跑馬燈")
        self.disable_btn.clicked.connect(self.disable_trading_ticker)
        self.disable_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #dc3545; color: white; border: none; border-radius: 4px; }")
        self.disable_btn.setEnabled(False)
        
        test_btn = QPushButton("🧪 測試所有時段")
        test_btn.clicked.connect(self.test_all_periods)
        test_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; }")
        
        button_layout.addWidget(self.enable_btn)
        button_layout.addWidget(self.disable_btn)
        button_layout.addWidget(test_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 狀態欄
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("📊 看盤重點跑馬燈測試系統已就緒")
        
        # 時間更新定時器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # 每秒更新時間
        
    def init_trading_ticker(self):
        """初始化看盤重點跑馬燈"""
        self.trading_ticker_enabled = False
        self.trading_ticker_timer = QTimer()
        self.trading_ticker_timer.timeout.connect(self.check_trading_time)
        
        # 看盤重點訊息
        self.trading_messages = {
            'pre_market': "🌅 盤前集合競價 (8:30-9:00)：關注國際盤勢、籌碼變動、模擬撮合僅供參考",
            'early_session': "🌄 早盤時段 (9:00-10:30)：注意開盤跳空、主力動態、挑選飆股機會",
            'mid_session': "☀️ 中盤時段 (10:30-12:00)：觀察盤整延續、消息面影響、套利機會",
            'afternoon': "🌇 午盤時段 (12:00-13:30)：午盤變勢、期指影響、資金移轉關鍵期",
            'closing': "🌆 收盤前試撮 (13:25-13:30)：收盤價博弈、價格穩定措施、盤後零股準備",
            'after_hours': "📊 盤後時段：檢視當日表現、準備明日策略、關注國際市場動向"
        }
    
    def update_time_display(self):
        """更新時間顯示"""
        now = datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 判斷當前時段
        current_time = now.time()
        period = self.get_current_period(current_time)
        
        self.time_label.setText(f"⏰ 當前時間：{time_str} | 📊 當前時段：{period}")
    
    def get_current_period(self, current_time):
        """獲取當前時段名稱"""
        if time(8, 30) <= current_time < time(9, 0):
            return "盤前集合競價"
        elif time(9, 0) <= current_time < time(10, 30):
            return "早盤時段"
        elif time(10, 30) <= current_time < time(12, 0):
            return "中盤時段"
        elif time(12, 0) <= current_time < time(13, 30):
            return "午盤時段"
        elif time(13, 25) <= current_time < time(13, 30):
            return "收盤前試撮"
        else:
            return "盤後時段"
    
    def enable_trading_ticker(self):
        """啟用看盤重點跑馬燈"""
        if self.trading_ticker_enabled:
            QMessageBox.information(self, "跑馬燈", "📊 看盤重點跑馬燈已經啟用！")
            return
        
        self.trading_ticker_enabled = True
        self.trading_ticker_timer.start(5000)  # 每5秒檢查一次（測試用）
        self.enable_btn.setEnabled(False)
        self.disable_btn.setEnabled(True)
        
        QMessageBox.information(
            self, 
            "跑馬燈啟用", 
            "🎯 看盤重點跑馬燈已啟用！\n\n"
            "系統將根據當前時間自動顯示相應的看盤重點提醒。\n"
            "提醒將在狀態欄以跑馬燈形式顯示。"
        )
        
        # 立即檢查一次
        self.check_trading_time()
    
    def disable_trading_ticker(self):
        """停用看盤重點跑馬燈"""
        self.trading_ticker_enabled = False
        self.trading_ticker_timer.stop()
        self.enable_btn.setEnabled(True)
        self.disable_btn.setEnabled(False)
        
        self.status_bar.showMessage("📊 看盤重點跑馬燈已停用")
        QMessageBox.information(self, "跑馬燈", "⏹️ 看盤重點跑馬燈已停用")
    
    def check_trading_time(self):
        """檢查交易時間並顯示相應提醒"""
        if not self.trading_ticker_enabled:
            return
            
        now = datetime.now()
        current_time = now.time()
        
        # 判斷當前時段
        message = ""
        if time(8, 30) <= current_time < time(9, 0):
            message = self.trading_messages['pre_market']
        elif time(9, 0) <= current_time < time(10, 30):
            message = self.trading_messages['early_session']
        elif time(10, 30) <= current_time < time(12, 0):
            message = self.trading_messages['mid_session']
        elif time(12, 0) <= current_time < time(13, 30):
            message = self.trading_messages['afternoon']
        elif time(13, 25) <= current_time < time(13, 30):
            message = self.trading_messages['closing']
        else:
            message = self.trading_messages['after_hours']
        
        # 在狀態欄顯示跑馬燈
        if message:
            self.status_bar.showMessage(message)
    
    def test_all_periods(self):
        """測試所有時段的訊息"""
        messages = [
            ("🌅 盤前集合競價", self.trading_messages['pre_market']),
            ("🌄 早盤時段", self.trading_messages['early_session']),
            ("☀️ 中盤時段", self.trading_messages['mid_session']),
            ("🌇 午盤時段", self.trading_messages['afternoon']),
            ("🌆 收盤前試撮", self.trading_messages['closing']),
            ("📊 盤後時段", self.trading_messages['after_hours'])
        ]
        
        # 創建測試對話框
        dialog = QMessageBox(self)
        dialog.setWindowTitle("🧪 所有時段訊息測試")
        dialog.setIcon(QMessageBox.Icon.Information)
        
        content = "📊 看盤重點跑馬燈訊息測試：\n\n"
        for period, message in messages:
            content += f"{period}：\n{message}\n\n"
        
        dialog.setText(content)
        dialog.exec()

def main():
    """主函數"""
    print("📊 測試看盤重點跑馬燈功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示主視窗
    window = TradingTipsTestWindow()
    window.show()
    
    print("✅ 看盤重點跑馬燈測試視窗已開啟")
    print("🔍 測試項目:")
    print("  1. ✅ 看盤重點選單項目")
    print("  2. ✅ 跑馬燈啟用/停用功能")
    print("  3. ✅ 時段自動判斷")
    print("  4. ✅ 狀態欄跑馬燈顯示")
    print("  5. ✅ 五大時段訊息內容")
    print("\n📝 測試步驟:")
    print("  1. 點擊 '🎯 啟用跑馬燈' 按鈕")
    print("  2. 觀察狀態欄的跑馬燈訊息")
    print("  3. 點擊 '🧪 測試所有時段' 查看所有訊息")
    print("  4. 點擊 '⏹️ 停用跑馬燈' 停止功能")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
