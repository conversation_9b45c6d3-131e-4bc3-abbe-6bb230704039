#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略組合生成器
自動計算四策略、三策略、二策略的各種組合及對應口訣
"""

from itertools import combinations
from dataclasses import dataclass
from typing import List, Dict, Tuple
import json

@dataclass
class StrategyMantra:
    """策略口訣數據類"""
    mantra_id: str
    category: str  # buy/sell
    strategy_combination: List[str]
    mantra_text: str
    success_rate: float
    avg_profit: float
    risk_level: str
    market_condition: str
    signal_conditions: List[str]
    enabled: bool = True  # 勾選狀態

class StrategyCombinationGenerator:
    """策略組合生成器"""
    
    def __init__(self):
        # 四大策略
        self.strategies = ['RSI', 'MACD', '布林通道', '移動平均']
        
        # 策略簡稱對照
        self.strategy_codes = {
            'RSI': 'RSI',
            'MACD': 'MACD', 
            '布林通道': 'BB',
            '移動平均': 'MA'
        }
        
        # 各策略的術語
        self.strategy_terms = {
            'RSI': {
                'buy': ['RSI超賣反彈', 'RSI黃金交叉', 'RSI背離買點', 'RSI突破50'],
                'sell': ['RSI超買回落', 'RSI死亡交叉', 'RSI背離賣點', 'RSI跌破50']
            },
            'MACD': {
                'buy': ['MACD金叉', 'MACD零軸突破', 'MACD背離買點', 'MACD柱狀轉正'],
                'sell': ['MACD死叉', 'MACD零軸跌破', 'MACD背離賣點', 'MACD柱狀轉負']
            },
            '布林通道': {
                'buy': ['布林下軌反彈', '布林中軌突破', '布林收縮後擴張', '布林%B回升'],
                'sell': ['布林上軌回落', '布林中軌跌破', '布林收縮警示', '布林%B下降']
            },
            '移動平均': {
                'buy': ['MA金叉', 'MA多頭排列', 'MA支撐確認', 'MA趨勢轉強'],
                'sell': ['MA死叉', 'MA空頭排列', 'MA壓力確認', 'MA趨勢轉弱']
            }
        }
        
        # 基礎成功率（會根據組合數量調整）
        self.base_success_rates = {
            'RSI': 0.65,
            'MACD': 0.68,
            '布林通道': 0.63,
            '移動平均': 0.66
        }
        
        # 基礎獲利率
        self.base_profit_rates = {
            'RSI': 0.045,
            'MACD': 0.052,
            '布林通道': 0.048,
            '移動平均': 0.041
        }
    
    def calculate_combination_metrics(self, strategies: List[str]) -> Tuple[float, float, str, str]:
        """計算組合的成功率、獲利率、風險等級、市場條件"""
        # 成功率：多策略確認會提高成功率
        base_rate = sum(self.base_success_rates[s] for s in strategies) / len(strategies)
        
        # 組合加成效果
        combination_bonus = {
            4: 0.12,  # 四策略確認加成12%
            3: 0.08,  # 三策略確認加成8%
            2: 0.04,  # 雙策略確認加成4%
            1: 0.00   # 單策略無加成
        }
        
        success_rate = min(0.85, base_rate + combination_bonus.get(len(strategies), 0))
        
        # 獲利率：多策略確認會提高獲利率
        base_profit = sum(self.base_profit_rates[s] for s in strategies) / len(strategies)
        profit_bonus = combination_bonus.get(len(strategies), 0) * 0.3
        avg_profit = base_profit + profit_bonus
        
        # 風險等級
        if success_rate >= 0.75:
            risk_level = "低"
        elif success_rate >= 0.65:
            risk_level = "中"
        else:
            risk_level = "高"
        
        # 市場條件
        if len(strategies) >= 3:
            market_condition = "適用各種市場環境"
        elif len(strategies) == 2:
            market_condition = "適用正常波動市場"
        else:
            market_condition = "適用特定市場條件"
        
        return success_rate, avg_profit, risk_level, market_condition
    
    def generate_mantra_text(self, strategies: List[str], category: str, term_combination: List[str]) -> str:
        """生成口訣文本"""
        # 策略代碼
        strategy_codes = [self.strategy_codes[s] for s in strategies]
        code_text = "+".join(strategy_codes)
        
        # 根據術語組合生成口訣
        if category == 'buy':
            if len(term_combination) == 4:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，{term_combination[2]}加{term_combination[3]}四重確認買"
            elif len(term_combination) == 3:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，{term_combination[2]}三重確認買"
            elif len(term_combination) == 2:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，雙重確認買"
            else:
                mantra = f"【{code_text}】{term_combination[0]}，單一確認買"
        else:  # sell
            if len(term_combination) == 4:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，{term_combination[2]}加{term_combination[3]}四重確認賣"
            elif len(term_combination) == 3:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，{term_combination[2]}三重確認賣"
            elif len(term_combination) == 2:
                mantra = f"【{code_text}】{term_combination[0]}配{term_combination[1]}，雙重確認賣"
            else:
                mantra = f"【{code_text}】{term_combination[0]}，單一確認賣"
        
        return mantra
    
    def generate_all_combinations(self) -> Dict[str, List[StrategyMantra]]:
        """生成所有策略組合的口訣"""
        all_mantras = {'buy': [], 'sell': []}
        
        # 生成四策略、三策略、二策略組合
        for combo_size in [4, 3, 2]:
            for strategy_combo in combinations(self.strategies, combo_size):
                strategy_list = list(strategy_combo)
                
                # 計算組合指標
                success_rate, avg_profit, risk_level, market_condition = self.calculate_combination_metrics(strategy_list)
                
                # 為每個類別生成口訣
                for category in ['buy', 'sell']:
                    # 獲取各策略的術語
                    strategy_terms = [self.strategy_terms[s][category] for s in strategy_list]
                    
                    # 生成術語組合（每個策略選一個主要術語）
                    main_terms = [terms[0] for terms in strategy_terms]  # 選擇每個策略的主要術語
                    
                    # 生成口訣
                    mantra_text = self.generate_mantra_text(strategy_list, category, main_terms)
                    
                    # 創建策略口訣對象
                    mantra_id = f"{category}_{combo_size}_{'+'.join([self.strategy_codes[s] for s in strategy_list])}"
                    
                    mantra = StrategyMantra(
                        mantra_id=mantra_id,
                        category=category,
                        strategy_combination=strategy_list,
                        mantra_text=mantra_text,
                        success_rate=success_rate,
                        avg_profit=avg_profit,
                        risk_level=risk_level,
                        market_condition=market_condition,
                        signal_conditions=main_terms,
                        enabled=True  # 預設啟用
                    )
                    
                    all_mantras[category].append(mantra)
        
        # 按成功率排序
        all_mantras['buy'].sort(key=lambda x: x.success_rate, reverse=True)
        all_mantras['sell'].sort(key=lambda x: x.success_rate, reverse=True)
        
        return all_mantras
    
    def save_mantras_to_json(self, mantras: Dict[str, List[StrategyMantra]], filename: str = "strategy_mantras.json"):
        """保存口訣到JSON文件"""
        data = {}
        for category, mantra_list in mantras.items():
            data[category] = []
            for mantra in mantra_list:
                data[category].append({
                    'mantra_id': mantra.mantra_id,
                    'category': mantra.category,
                    'strategy_combination': mantra.strategy_combination,
                    'mantra_text': mantra.mantra_text,
                    'success_rate': mantra.success_rate,
                    'avg_profit': mantra.avg_profit,
                    'risk_level': mantra.risk_level,
                    'market_condition': mantra.market_condition,
                    'signal_conditions': mantra.signal_conditions,
                    'enabled': mantra.enabled
                })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 策略口訣已保存到 {filename}")

def test_generator():
    """測試策略組合生成器"""
    print("🔍 測試策略組合生成器")
    print("="*60)
    
    generator = StrategyCombinationGenerator()
    mantras = generator.generate_all_combinations()
    
    print(f"📊 生成結果統計:")
    print(f"• 買入口訣: {len(mantras['buy'])} 條")
    print(f"• 賣出口訣: {len(mantras['sell'])} 條")
    print(f"• 總計: {len(mantras['buy']) + len(mantras['sell'])} 條")
    
    print(f"\n🟢 買入口訣範例（前10條）:")
    print(f"{'序號':<4} {'策略組合':<20} {'成功率':<8} {'獲利率':<8} {'風險':<6} {'口訣'}")
    print("-" * 100)
    
    for i, mantra in enumerate(mantras['buy'][:10], 1):
        combo_text = "+".join([generator.strategy_codes[s] for s in mantra.strategy_combination])
        print(f"{i:<4} {combo_text:<20} {mantra.success_rate:<8.1%} {mantra.avg_profit:<8.1%} "
              f"{mantra.risk_level:<6} {mantra.mantra_text}")
    
    print(f"\n🔴 賣出口訣範例（前10條）:")
    print(f"{'序號':<4} {'策略組合':<20} {'成功率':<8} {'獲利率':<8} {'風險':<6} {'口訣'}")
    print("-" * 100)
    
    for i, mantra in enumerate(mantras['sell'][:10], 1):
        combo_text = "+".join([generator.strategy_codes[s] for s in mantra.strategy_combination])
        print(f"{i:<4} {combo_text:<20} {mantra.success_rate:<8.1%} {mantra.avg_profit:<8.1%} "
              f"{mantra.risk_level:<6} {mantra.mantra_text}")
    
    # 保存到文件
    generator.save_mantras_to_json(mantras)
    
    print(f"\n💡 策略組合分析:")
    print(f"• 四策略組合: 1種 (最高成功率)")
    print(f"• 三策略組合: 4種 (高成功率)")
    print(f"• 二策略組合: 6種 (中等成功率)")
    print(f"• 總計: 11種買入 + 11種賣出 = 22條口訣")
    
    return mantras

if __name__ == "__main__":
    test_generator()
