#!/usr/bin/env python3
import os
import sys
import json
import logging
import traceback
import sqlite3
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
import pyqtgraph as pg
import talib

from PyQt6.QtCore import Qt, QDate, QPointF, QRectF, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QColor, QBrush, QPainter, QPicture, QPen, QPixmap
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QTabWidget, QGroupBox, QScrollArea, QMessageBox, QDialog, QLineEdit, QFileDialog,
    QListWidget, QComboBox, QCalendarWidget, QTableWidget, QTableWidgetItem,
    QStatusBar, QGridLayout, QSpinBox, QListWidgetItem, QProgressBar, QFormLayout, QDialogButtonBox, QSplashScreen, QCheckBox
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_screener.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# ===================== 完整CandlestickItem實現 =====================
class CandlestickItem(pg.GraphicsObject):
    def __init__(self, data):
        super().__init__()
        self.data = data  # 數據格式：[ [時間戳, 開, 高, 低, 收], ... ]
        self.generatePicture()
        
    def generatePicture(self):
        self.picture = QPicture()
        p = QPainter(self.picture)
        p.setPen(pg.mkPen('k'))
        w = (60*60*24)*0.8  # 1天的0.8倍寬度
        
        for t, o, h, l, c in self.data:
            t = int(t)  # 確保時間戳為整數
            # 繪製影線
            p.drawLine(QPointF(t, l), QPointF(t, h))
            # 繪製實體
            if o > c:
                p.setBrush(pg.mkBrush('g'))
            else:
                p.setBrush(pg.mkBrush('r'))
            p.drawRect(QRectF(t-w/2, o, w, c-o))
        p.end()
        
    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)
        
    def boundingRect(self):
        return QRectF(self.picture.boundingRect())

# ===================== 策略參數與策略類別 =====================
class StrategyParams:
    def __init__(self, name="Default", description="",
                 ma240_days=240, trend_days=30,
                 ma60_days=60, ma20_days=20,
                 volume_short=3, volume_long=18, cross_days=3,
                 rsi_long=13, rsi_long_threshold=50,
                 rsi_short=6, rsi_short_threshold=70):
        self.name = name
        self.description = description
        self.ma240_days = ma240_days
        self.trend_days = trend_days
        self.ma60_days = ma60_days
        self.ma20_days = ma20_days
        self.volume_short = volume_short
        self.volume_long = volume_long
        self.cross_days = cross_days
        self.rsi_long = rsi_long
        self.rsi_long_threshold = rsi_long_threshold
        self.rsi_short = rsi_short
        self.rsi_short_threshold = rsi_short_threshold

    @staticmethod
    def default():
        return StrategyParams()

    def save(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.__dict__, f, ensure_ascii=False, indent=4)

    @classmethod
    def load(cls, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if "parameters" in data:
                params = data["parameters"]
            else:
                params = data
            return cls(
                name=data.get("name", "Default"),
                description=data.get("description", ""),
                ma240_days=params.get("ma240_days", 240),
                trend_days=params.get("trend_days", 30),
                ma60_days=params.get("ma60_days", 60),
                ma20_days=params.get("ma20_days", 20),
                volume_short=params.get("volume_short", 3),
                volume_long=params.get("volume_long", 18),
                cross_days=params.get("cross_days", 3),
                rsi_long=params.get("rsi_long", 13),
                rsi_long_threshold=params.get("rsi_long_threshold", 50),
                rsi_short=params.get("rsi_short", 6),
                rsi_short_threshold=params.get("rsi_short_threshold", 70)
            )
        except Exception as e:
            logging.error(f"載入策略參數時出錯: {e}")
            logging.error(traceback.format_exc())
            return cls.default()

class Strategy:
    def __init__(self, params):
        self.params = params

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        df['Signal'] = 0
        for i in range(1, len(df)):
            if df['MA5'].iloc[i] > df['MA20'].iloc[i] and df['MA5'].iloc[i-1] <= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = 1
            elif df['MA5'].iloc[i] < df['MA20'].iloc[i] and df['MA5'].iloc[i-1] >= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = -1
        return df

# ===================== 策略設定對話框 =====================
class StrategyDialog(QDialog):
    def __init__(self, params: StrategyParams = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("策略設置")
        self.setModal(True)
        layout = QVBoxLayout(self)
        
        # 策略基本資訊
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)
        self.name_edit = QLineEdit()
        self.desc_edit = QLineEdit()
        basic_layout.addRow("策略名稱:", self.name_edit)
        basic_layout.addRow("策略描述:", self.desc_edit)
        layout.addWidget(basic_group)

        # 核心參數群組
        params_group = QGroupBox("核心條件參數 (依優先順序)")
        params_layout = QFormLayout(params_group)
        
        # 條件一：年線設定
        self.ma240_spin = QSpinBox()
        self.ma240_spin.setRange(100, 300)
        self.ma240_spin.setSingleStep(5)
        params_layout.addRow("1. 年線天數 (MA240):", self.ma240_spin)

        # 條件二：趨勢持續性
        self.trend_days_spin = QSpinBox()
        self.trend_days_spin.setRange(20, 60)
        params_layout.addRow("2. 趨勢持續天數 (未來N日):", self.trend_days_spin)

        # 條件三：雙均線設定
        ma_group = QHBoxLayout()
        self.ma60_spin = QSpinBox()
        self.ma60_spin.setRange(40, 80)
        self.ma20_spin = QSpinBox()
        self.ma20_spin.setRange(10, 30)
        ma_group.addWidget(QLabel("季線(MA60):"))
        ma_group.addWidget(self.ma60_spin)
        ma_group.addWidget(QLabel("月線(MA20):"))
        ma_group.addWidget(self.ma20_spin)
        params_layout.addRow("3. 雙均線天數:", ma_group)

        # 條件四：量能交叉
        volume_group = QHBoxLayout()
        self.vol_short_spin = QSpinBox()
        self.vol_short_spin.setRange(2, 5)
        self.vol_long_spin = QSpinBox()
        self.vol_long_spin.setRange(15, 20)
        self.cross_days_spin = QSpinBox()
        self.cross_days_spin.setRange(1, 5)
        volume_group.addWidget(QLabel("短期量能(3日):"))
        volume_group.addWidget(self.vol_short_spin)
        volume_group.addWidget(QLabel("長期量能(18日):"))
        volume_group.addWidget(self.vol_long_spin)
        volume_group.addWidget(QLabel("有效天數:"))
        volume_group.addWidget(self.cross_days_spin)
        params_layout.addRow("4. 量能黃金交叉:", volume_group)

        # 條件五：RSI設定
        rsi_group = QHBoxLayout()
        self.rsi_long_spin = QSpinBox()
        self.rsi_long_spin.setRange(10, 20)
        self.rsi_long_threshold = QSpinBox()
        self.rsi_long_threshold.setRange(30, 70)
        self.rsi_short_spin = QSpinBox()
        self.rsi_short_spin.setRange(3, 9)
        self.rsi_short_threshold = QSpinBox()
        self.rsi_short_threshold.setRange(60, 80)
        rsi_group.addWidget(QLabel("長期RSI(13日):"))
        rsi_group.addWidget(self.rsi_long_spin)
        rsi_group.addWidget(QLabel(">"))
        rsi_group.addWidget(self.rsi_long_threshold)
        rsi_group.addWidget(QLabel("短期RSI(6日):"))
        rsi_group.addWidget(self.rsi_short_spin)
        rsi_group.addWidget(QLabel(">"))
        rsi_group.addWidget(self.rsi_short_threshold)
        params_layout.addRow("5. RSI條件:", rsi_group)

        layout.addWidget(params_group)

        # 按鈕列
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 初始化值
        params = params or StrategyParams()
        self.name_edit.setText(params.name)
        self.desc_edit.setText(params.description)
        self.ma240_spin.setValue(params.ma240_days)
        self.trend_days_spin.setValue(params.trend_days)
        self.ma60_spin.setValue(params.ma60_days)
        self.ma20_spin.setValue(params.ma20_days)
        self.vol_short_spin.setValue(params.volume_short)
        self.vol_long_spin.setValue(params.volume_long)
        self.cross_days_spin.setValue(params.cross_days)
        self.rsi_long_spin.setValue(params.rsi_long)
        self.rsi_long_threshold.setValue(params.rsi_long_threshold)
        self.rsi_short_spin.setValue(params.rsi_short)
        self.rsi_short_threshold.setValue(params.rsi_short_threshold)

    def get_strategy_params(self) -> StrategyParams:
        return StrategyParams(
            name=self.name_edit.text(),
            description=self.desc_edit.text(),
            ma240_days=self.ma240_spin.value(),
            trend_days=self.trend_days_spin.value(),
            ma60_days=self.ma60_spin.value(),
            ma20_days=self.ma20_spin.value(),
            volume_short=self.vol_short_spin.value(),
            volume_long=self.vol_long_spin.value(),
            cross_days=self.cross_days_spin.value(),
            rsi_long=self.rsi_long_spin.value(),
            rsi_long_threshold=self.rsi_long_threshold.value(),
            rsi_short=self.rsi_short_spin.value(),
            rsi_short_threshold=self.rsi_short_threshold.value()
        )

# ===================== 數據庫工作線程 =====================
class DatabaseWorker(QThread):
    initialized = pyqtSignal(object, str)  # 連接對象, 表名
    error = pyqtSignal(str)
    progress = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress.emit("正在連接數據庫...")
            conn = sqlite3.connect(
                self.db_path,
                timeout=15,
                check_same_thread=False,
                detect_types=sqlite3.PARSE_DECLTYPES
            )
            conn.execute("PRAGMA journal_mode = WAL")  # 寫入模式優化
            
            # 自動檢測數據表
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            main_table = 'stock_daily_data' if not tables else tables[0][0]
            
            self.initialized.emit(conn, main_table)
        except Exception as e:
            self.error.emit(f"數據庫連接失敗: {str(e)}")
            logging.error(traceback.format_exc())

# ===================== 主 GUI 類別 =====================
class StockScreenerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.price_conn = None
        self.price_table = None
        self.setWindowTitle("台股智能选股系统")
        self.setGeometry(100, 100, 1200, 800)
        
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.status_label = QLabel("就緒")
        self.statusBar.addWidget(self.status_label)
        
        self.db_worker = None
        self._uninitialized_components = True  # 延迟初始化标记

        # 初始化策略和信號顯示設置
        self.current_strategy = "二次創高股票"  # 默認策略
        self.max_signals_display = None  # None表示顯示所有信號
        self.current_chart_stock = None  # 當前顯示的股票

        self.initUI()  # 初始化所有GUI元件
        
        # 次要延遲來源 (約佔30%啟動時間)
        self.db_worker = DatabaseWorker(":memory:")  # 預設記憶體資料庫
        self.db_worker.initialized.connect(self.on_db_initialized)
        self.db_worker.error.connect(self.show_error)
        self.db_worker.start()  # 啟動資料庫工作線程
        
        logging.info("GUI 初始化完成")

        # 在主窗口顯示前添加
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)  # 禁用硬體加速
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    def initUI(self):
        # 先初始化可見元件
        self.lazy_init_phase1()
        
        # 延遲加載後台元件
        QTimer.singleShot(100, self.lazy_init_phase2)

    def lazy_init_phase1(self):
        """第一阶段初始化：核心布局"""
        # 设置基本布局
        main_widget = QWidget()
        self.main_layout = QHBoxLayout(main_widget)
        self.setCentralWidget(main_widget)
        
        # 左侧控制面板基础框架
        self.left_panel = QWidget()
        self.left_panel.setMaximumWidth(300)
        self.left_layout = QVBoxLayout(self.left_panel)
        self.main_layout.addWidget(self.left_panel)
        
        # 右侧Tab基础框架
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)
        
        # 显示基础框架
        self._create_basic_controls()

    def lazy_init_phase2(self):
        """第二阶段初始化：完整功能"""
        if not self._uninitialized_components:
            return
            
        # 完整初始化剩余组件
        self._init_left_panel_details()
        self._init_right_tabs()
        self._start_background_tasks()
        self._uninitialized_components = False

    def _create_basic_controls(self):
        """创建立即需要的控件"""
        # 基础控制项
        self.run_btn = QPushButton("开始选股", self)
        self.left_layout.addWidget(self.run_btn)
        
        # 状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)

    def _init_left_panel_details(self):
        """初始化左側面板詳細內容"""
        # 條件設定群組
        condition_group = QGroupBox("選股條件設定")
        condition_layout = QVBoxLayout(condition_group)
        
        # 策略選擇
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            "二次創高股票",
            "勝率73.45%",
            "破底反彈高量",
            "阿水一式",
            "阿水二式",
            "藏獒",
            "CANSLIM量價齊升",
            "膽小貓",
            "三頻率RSI策略"
        ])
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        condition_layout.addWidget(QLabel("選擇策略:"))
        condition_layout.addWidget(self.strategy_combo)

        # K線圖信號顯示控制
        signal_control_layout = QHBoxLayout()
        self.show_all_signals_checkbox = QCheckBox("顯示所有信號")
        self.show_all_signals_checkbox.setChecked(True)  # 默認顯示所有信號
        self.show_all_signals_checkbox.stateChanged.connect(self.on_signal_display_changed)
        signal_control_layout.addWidget(self.show_all_signals_checkbox)

        self.max_signals_spinbox = QSpinBox()
        self.max_signals_spinbox.setRange(1, 100)
        self.max_signals_spinbox.setValue(20)
        self.max_signals_spinbox.setEnabled(False)  # 默認禁用，因為顯示所有信號
        self.max_signals_spinbox.valueChanged.connect(self.on_signal_display_changed)
        signal_control_layout.addWidget(QLabel("最多顯示:"))
        signal_control_layout.addWidget(self.max_signals_spinbox)
        signal_control_layout.addWidget(QLabel("個信號"))

        condition_layout.addLayout(signal_control_layout)
        
        # 執行按鈕
        self.run_btn = QPushButton("開始選股")
        self.run_btn.clicked.connect(self.run_screening)
        condition_layout.addWidget(self.run_btn)
        
        self.left_layout.addWidget(condition_group)
        
        # 分頁控件
        self.left_tabs = QTabWidget()
        self.left_tabs.setStyleSheet("""
            QListWidget { font-size: 9pt; }
            QTabWidget::pane { border: 0; }
        """)
        
        # 全部股票分頁
        self.all_stocks_list = QListWidget()
        self.all_stocks_list.setVerticalScrollMode(QListWidget.ScrollMode.ScrollPerPixel)
        self.all_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        self.left_tabs.addTab(self.all_stocks_list, "全部股票")
        
        # 選股結果分頁
        self.filtered_stocks_list = QListWidget()
        self.filtered_stocks_list.setVerticalScrollMode(QListWidget.ScrollMode.ScrollPerPixel)
        self.filtered_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        self.left_tabs.addTab(self.filtered_stocks_list, "選股結果 (0)")
        
        self.left_layout.addWidget(self.left_tabs, stretch=3)
        self.left_layout.addStretch(1)

    def _init_right_tabs(self):
        """初始化右側分頁內容"""
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)
        
        # K線圖分頁
        self.chart_tab = QWidget()
        self.chart_layout = QVBoxLayout(self.chart_tab)
        self.tabs.addTab(self.chart_tab, "K線圖")
        
        # 回測結果分頁
        self.backtest_tab = QWidget()
        self.backtest_layout = QVBoxLayout(self.backtest_tab)
        self.tabs.addTab(self.backtest_tab, "回測結果")

    def _start_background_tasks(self):
        """启动后台任务"""
        # 异步加载股票列表
        QTimer.singleShot(100, self.load_stock_list_async)
        
        # 启动数据库工作线程
        self.db_worker.start()

    def get_db_path(self):
        """獲取資料庫路徑，增加路徑檢查"""
        default_path = r"D:\Finlab\history\tables\price.db"
        if os.path.exists(default_path):
            return default_path
        return QFileDialog.getOpenFileName(self, "選擇價格數據庫", "", "Database files (*.db)")[0]

    def on_db_initialized(self, conn, table_name):
        """資料庫準備完成後的處理"""
        self.price_conn = conn
        self.price_table = table_name
        logging.info(f"使用資料表: {self.price_table}")

        # 啟動股票列表載入
        self.status_label.setText("載入股票列表中...")
        QTimer.singleShot(0, self.load_stock_list)  # 使用定時器確保在主線程執行

    def show_error(self, msg):
        """顯示資料庫錯誤"""
        QMessageBox.critical(self, "嚴重錯誤", msg)
        self.status_label.setText("資料庫初始化失敗")
        logging.error(msg)

    def closeEvent(self, event):
        """視窗關閉時的安全清理"""
        if self.db_worker and self.db_worker.isRunning():
            self.db_worker.cancel()
            self.db_worker.wait(3000)  # 最多等待3秒
        if self.price_conn:
            self.price_conn.close()
        super().closeEvent(event)

    def load_stock_list(self):
        if not self.price_conn:
            logging.warning("嘗試載入股票列表時資料庫未連接")
            return
        try:
            self.all_stocks_list.clear()
            cursor = self.price_conn.cursor()
            cursor.execute(f"""
                SELECT DISTINCT stock_id, stock_name, industry, listing_status 
                FROM {self.price_table} 
                WHERE listing_status = '上市'
                ORDER BY stock_id
            """)
            rows = cursor.fetchall()
            self.stock_info_map = {}
            for r in rows:
                sid, sname, sind, lstatus = r
                if sid not in self.stock_info_map:
                    self.stock_info_map[sid] = {"name": sname, "industry": sind, "listing_status": lstatus}
            for sid in sorted(self.stock_info_map.keys()):
                display_text = f"{sid} - {self.stock_info_map[sid]['name']}"
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, sid)
                self.all_stocks_list.addItem(item)
            self.status_label.setText("股票列表載入完成")
        except sqlite3.Error as e:
            self.show_database_error(f"資料庫查詢失敗: {str(e)}")

    def search_stocks(self, text):
        self.all_stocks_list.clear()
        text_lower = text.lower()
        for sid, info in self.stock_info_map.items():
            sname = info['name']
            if text_lower in sid.lower() or text_lower in sname.lower():
                display_text = f"{sid} - {sname}"
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, sid)
                self.all_stocks_list.addItem(item)

    def on_stock_double_clicked(self, item):
        stock_id = item.data(Qt.ItemDataRole.UserRole)
        self.update_chart(stock_id)

    def update_chart(self, stock_id, period='2M'):
        try:
            # 設置當前圖表股票
            self.current_chart_stock = stock_id

            # 獲取數據
            df = self.get_stock_data(stock_id, period)
            if df.empty:
                raise ValueError("無有效數據")

            # 清除舊圖表
            if hasattr(self, 'chart_widget'):
                self.chart_layout.removeWidget(self.chart_widget)
                self.chart_widget.deleteLater()

            # 創建新圖表
            self.chart_widget = pg.PlotWidget()
            self.chart_layout.addWidget(self.chart_widget)

            # 設置日期軸
            date_axis = pg.DateAxisItem(orientation='bottom')
            self.chart_widget.setAxisItems({'bottom': date_axis})

            # 轉換時間戳
            df['date_ts'] = df['date'].astype(np.int64) // 10**9

            # 準備K線數據
            ohlc = df[['date_ts', 'Open', 'High', 'Low', 'Close']].values

            # 添加K線圖
            candlestick = CandlestickItem(ohlc)
            self.chart_widget.addItem(candlestick)

            # 設置座標範圍
            self.chart_widget.setXRange(ohlc[:,0].min(), ohlc[:,0].max())
            self.chart_widget.setYRange(df['Low'].min()*0.95, df['High'].max()*1.05)

            # 添加策略信號（根據當前選擇的策略）
            self.add_strategy_signals(df, stock_id)

            # 即時數據提示
            self.crosshair = pg.InfiniteLine(angle=90, movable=False)
            self.chart_widget.addItem(self.crosshair)
            self.label = pg.TextItem(anchor=(0,1), color='k')
            self.chart_widget.addItem(self.label)

            # 滑鼠移動事件
            def mouseMoved(evt):
                pos = evt
                if self.chart_widget.sceneBoundingRect().contains(pos):
                    mousePoint = self.chart_widget.getViewBox().mapSceneToView(pos)
                    x = mousePoint.x()
                    idx = np.abs(df['date_ts'] - x).argmin()
                    self.crosshair.setPos(df['date_ts'].iloc[idx])
                    self.label.setHtml(
                        f"<span style='font-size: 12pt'>日期: {df['date'].iloc[idx].strftime('%Y-%m-%d')}<br/>"
                        f"開: {df['Open'].iloc[idx]:.2f}<br/>高: {df['High'].iloc[idx]:.2f}<br/>"
                        f"低: {df['Low'].iloc[idx]:.2f}<br/>收: {df['Close'].iloc[idx]:.2f}</span>"
                    )
                    self.label.setPos(df['date_ts'].iloc[idx], df['High'].iloc[idx])

            self.chart_widget.scene().sigMouseMoved.connect(mouseMoved)

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"繪圖失敗: {str(e)}")
            logging.error(f"繪圖錯誤: {str(e)}")

    def add_strategy_signals(self, df, stock_id):
        """為當前選擇的策略添加買賣信號到K線圖"""
        try:
            if len(df) < 50:  # 需要足夠的歷史數據
                return

            # 獲取當前選擇的策略
            current_strategy = getattr(self, 'current_strategy', '二次創高股票')

            # 計算基本技術指標
            df = df.copy()
            df = self.calculate_technical_indicators(df)

            buy_signals = []
            sell_signals = []

            # 根據策略類型檢查信號
            if current_strategy == "勝率73.45%":
                buy_signals, sell_signals = self.check_high_win_rate_signals(df)
                strategy_label = "勝率73%"
                strategy_color = "blue"
            elif current_strategy == "破底反彈高量":
                buy_signals, sell_signals = self.check_bottom_rebound_signals(df)
                strategy_label = "破底反彈"
                strategy_color = "purple"
            elif current_strategy == "阿水一式":
                buy_signals, sell_signals = self.check_ashui_signals(df)
                strategy_label = "阿水一式"
                strategy_color = "orange"
            elif current_strategy == "阿水二式":
                buy_signals, sell_signals = self.check_ashui_short_signals(df)
                strategy_label = "阿水二式"
                strategy_color = "brown"
            elif current_strategy == "藏獒":
                buy_signals, sell_signals = self.check_tibetan_mastiff_signals(df)
                strategy_label = "藏獒"
                strategy_color = "gold"
            elif current_strategy == "CANSLIM量價齊升":
                buy_signals, sell_signals = self.check_canslim_signals(df)
                strategy_label = "CANSLIM"
                strategy_color = "cyan"
            elif current_strategy == "膽小貓":
                buy_signals, sell_signals = self.check_timid_cat_signals(df)
                strategy_label = "膽小貓"
                strategy_color = "pink"
            elif current_strategy == "三頻率RSI策略":
                buy_signals, sell_signals = self.check_triple_rsi_signals(df)
                strategy_label = "三頻RSI"
                strategy_color = "teal"
            else:  # 默認為二次創高策略
                buy_signals, sell_signals = self.check_second_high_signals(df)
                strategy_label = "二次創高"
                strategy_color = "green"

            # 顯示所有信號，讓用戶可以評估策略的整體表現
            # 可以通過設置來控制顯示數量，默認顯示所有
            max_signals = getattr(self, 'max_signals_display', None)  # None表示顯示所有

            if max_signals and max_signals > 0:
                display_buy_signals = buy_signals[-max_signals:] if len(buy_signals) > max_signals else buy_signals
                display_sell_signals = sell_signals[-max_signals:] if len(sell_signals) > max_signals else sell_signals
                display_note = f"(顯示最近{max_signals}次)"
            else:
                display_buy_signals = buy_signals
                display_sell_signals = sell_signals
                display_note = "(顯示全部)"

            # 添加買入信號標記
            self.add_buy_signal_markers(display_buy_signals, strategy_label, strategy_color)

            # 添加賣出信號標記
            self.add_sell_signal_markers(display_sell_signals, strategy_label, strategy_color)

            # 更新狀態顯示
            total_buy = len(buy_signals)
            total_sell = len(sell_signals)
            displayed_buy = len(display_buy_signals)
            displayed_sell = len(display_sell_signals)

            self.status_label.setText(
                f"{stock_id} - {strategy_label}策略信號: 買入{total_buy}次, 賣出{total_sell}次 {display_note}"
            )

        except Exception as e:
            logging.error(f"添加策略信號失敗: {str(e)}")

    def calculate_technical_indicators(self, df):
        """計算技術指標"""
        try:
            # 移動平均線
            df['MA5'] = df['Close'].rolling(5).mean()
            df['MA10'] = df['Close'].rolling(10).mean()
            df['MA20'] = df['Close'].rolling(20).mean()
            df['MA60'] = df['Close'].rolling(60).mean()
            df['MA120'] = df['Close'].rolling(120).mean()
            df['MA240'] = df['Close'].rolling(240).mean()

            # 成交量移動平均
            df['Volume_MA3'] = df['Volume'].rolling(3).mean()
            df['Volume_MA5'] = df['Volume'].rolling(5).mean()
            df['Volume_MA10'] = df['Volume'].rolling(10).mean()
            df['Volume_MA18'] = df['Volume'].rolling(18).mean()
            df['Volume_MA20'] = df['Volume'].rolling(20).mean()

            # RSI指標
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0).rolling(14).mean()
            loss = -delta.where(delta < 0, 0).rolling(14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # RSI6和RSI13
            gain6 = delta.where(delta > 0, 0).rolling(6).mean()
            loss6 = -delta.where(delta < 0, 0).rolling(6).mean()
            rs6 = gain6 / loss6
            df['RSI6'] = 100 - (100 / (1 + rs6))

            gain13 = delta.where(delta > 0, 0).rolling(13).mean()
            loss13 = -delta.where(delta < 0, 0).rolling(13).mean()
            rs13 = gain13 / loss13
            df['RSI13'] = 100 - (100 / (1 + rs13))

            # 布林帶
            bb_period = 20
            bb_std = 2
            df['BB_Middle'] = df['Close'].rolling(bb_period).mean()
            bb_std_dev = df['Close'].rolling(bb_period).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std_dev * bb_std)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std_dev * bb_std)

            return df

        except Exception as e:
            logging.error(f"計算技術指標失敗: {str(e)}")
            return df

    def add_buy_signal_markers(self, signals, strategy_label, color):
        """添加買入信號標記"""
        for signal in signals:
            # 向上箭頭
            buy_arrow = pg.ArrowItem(
                angle=90, headLen=15, tailLen=15,
                tailWidth=8, pen=color, brush=color
            )
            buy_arrow.setPos(signal['date_ts'], signal['price'] * 0.98)
            self.chart_widget.addItem(buy_arrow)

            # 買入標籤
            buy_label = pg.TextItem(
                html=f'<div style="background-color:rgba(0,255,0,200);padding:3px;color:white;font-weight:bold;">{strategy_label}買入</div>',
                anchor=(0.5, 0)
            )
            buy_label.setPos(signal['date_ts'], signal['price'] * 0.96)
            self.chart_widget.addItem(buy_label)

    def add_sell_signal_markers(self, signals, strategy_label, color):
        """添加賣出信號標記"""
        for signal in signals:
            # 向下箭頭
            sell_arrow = pg.ArrowItem(
                angle=-90, headLen=15, tailLen=15,
                tailWidth=8, pen='red', brush='red'
            )
            sell_arrow.setPos(signal['date_ts'], signal['price'] * 1.02)
            self.chart_widget.addItem(sell_arrow)

            # 賣出標籤
            sell_label = pg.TextItem(
                html=f'<div style="background-color:rgba(255,0,0,200);padding:3px;color:white;font-weight:bold;">{strategy_label}賣出</div>',
                anchor=(0.5, 1)
            )
            sell_label.setPos(signal['date_ts'], signal['price'] * 1.04)
            self.chart_widget.addItem(sell_label)

    def check_second_high_buy_signal(self, df, current_idx):
        """檢查二次創高買入信號的8個條件"""
        try:
            if current_idx < 120:  # 需要足夠的歷史數據
                return False

            close = df['Close']
            volume = df['Volume']

            # 條件1: 近日創新高 (近一日收盤價創近60日新高)
            newhigh_60 = close.rolling(60, min_periods=1).max()
            cond1 = close.iloc[current_idx] == newhigh_60.iloc[current_idx]

            # 條件2: 前期非新高 (前30日有至少一日未創新高)
            if current_idx < 30:
                return False
            newhigh_check = (newhigh_60.shift(1) == close.shift(1)).iloc[current_idx-30:current_idx]
            cond2 = not newhigh_check.all()

            # 條件3: 早期創新高 (第30-55日前有至少一日創60日新高)
            if current_idx < 55:
                return False
            early_period = slice(current_idx-55, current_idx-30)
            early_newhigh = (close.iloc[early_period] == newhigh_60.iloc[early_period])
            cond3 = early_newhigh.any()

            # 條件4: 價格突破 (前30-55日最高價小於近日收盤價)
            early_high = df['High'].iloc[early_period].max()
            cond4 = close.iloc[current_idx] > early_high

            # 條件5: 120日趨勢 (近日收盤價大於120日前收盤價)
            if current_idx < 120:
                return False
            cond5 = close.iloc[current_idx] > close.iloc[current_idx-120]

            # 條件6: 60日趨勢 (近日收盤價大於60日前收盤價)
            cond6 = close.iloc[current_idx] > close.iloc[current_idx-60]

            # 條件7: 營收成長 (簡化版：假設滿足，實際需要營收數據)
            cond7 = True  # 在實際應用中需要連接營收數據庫

            # 條件8: 成交量確認 (近5日平均成交量大於近20日平均成交量)
            vol_ma5 = volume.rolling(5).mean().iloc[current_idx]
            vol_ma20 = volume.rolling(20).mean().iloc[current_idx]
            cond8 = vol_ma5 > vol_ma20

            # 所有條件都必須滿足
            all_conditions = [cond1, cond2, cond3, cond4, cond5, cond6, cond7, cond8]
            return all(all_conditions)

        except Exception as e:
            logging.error(f"檢查二次創高信號失敗: {str(e)}")
            return False

    def get_signal_conditions(self, df, current_idx):
        """獲取信號條件詳情（用於顯示）"""
        try:
            close = df['Close']
            volume = df['Volume']

            # 重新計算各條件
            newhigh_60 = close.rolling(60, min_periods=1).max()
            cond1 = close.iloc[current_idx] == newhigh_60.iloc[current_idx]

            newhigh_check = (newhigh_60.shift(1) == close.shift(1)).iloc[current_idx-30:current_idx]
            cond2 = not newhigh_check.all()

            early_period = slice(current_idx-55, current_idx-30)
            early_newhigh = (close.iloc[early_period] == newhigh_60.iloc[early_period])
            cond3 = early_newhigh.any()

            early_high = df['High'].iloc[early_period].max()
            cond4 = close.iloc[current_idx] > early_high

            cond5 = close.iloc[current_idx] > close.iloc[current_idx-120]
            cond6 = close.iloc[current_idx] > close.iloc[current_idx-60]
            cond7 = True

            vol_ma5 = volume.rolling(5).mean().iloc[current_idx]
            vol_ma20 = volume.rolling(20).mean().iloc[current_idx]
            cond8 = vol_ma5 > vol_ma20

            conditions = [cond1, cond2, cond3, cond4, cond5, cond6, cond7, cond8]
            satisfied_count = sum(conditions)

            return f"滿足{satisfied_count}/8個條件"

        except Exception as e:
            return "條件檢查失敗"

    def check_second_high_signals(self, df):
        """檢查二次創高策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(120, len(df)):
                # 檢查買入信號
                if self.check_second_high_buy_signal(df.iloc[:i+1], i):
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 檢查賣出信號（20MA下彎）
                if i > 0 and df['MA20'].iloc[i] < df['MA20'].iloc[i-1]:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })
        except Exception as e:
            logging.error(f"檢查二次創高信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_high_win_rate_signals(self, df):
        """檢查勝率73.45%策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(240, len(df)):
                # 買入條件檢查
                conditions_met = 0

                # 1. MA240趨勢向上
                if i > 0 and df['MA240'].iloc[i] > df['MA240'].iloc[i-1]:
                    conditions_met += 1

                # 2. MA240未來趨勢（20天前比較）
                if i >= 20 and df['MA240'].iloc[i] > df['MA240'].iloc[i-20]:
                    conditions_met += 1

                # 3. MA20趨勢向上（5天內）
                ma20_up = False
                for j in range(max(0, i-5), i):
                    if df['MA20'].iloc[j+1] > df['MA20'].iloc[j]:
                        ma20_up = True
                        break
                if ma20_up:
                    conditions_met += 1

                # 4. 成交量均線交叉（3日vs18日）
                volume_cross = False
                for j in range(max(0, i-3), i):
                    if (df['Volume_MA3'].iloc[j] > df['Volume_MA18'].iloc[j] and
                        df['Volume_MA3'].iloc[j-1] <= df['Volume_MA18'].iloc[j-1]):
                        volume_cross = True
                        break
                if volume_cross:
                    conditions_met += 1

                # 5. RSI組合條件
                if df['RSI13'].iloc[i] > 50 and df['RSI6'].iloc[i] > 70:
                    conditions_met += 1

                # 6. 成交量條件（1000萬以上）
                if df['Volume'].iloc[i] * df['Close'].iloc[i] > 10000000:
                    conditions_met += 1

                # 買入信號：6個條件全部滿足
                if conditions_met >= 6:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出信號：RSI過高或MA20下彎
                if df['RSI6'].iloc[i] > 80 or (i > 0 and df['MA20'].iloc[i] < df['MA20'].iloc[i-1]):
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查勝率73%信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_bottom_rebound_signals(self, df):
        """檢查破底反彈高量策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(20, len(df)):
                # 破底反彈買入條件
                lookback = 10
                if i >= lookback:
                    # 檢查是否創新低
                    recent_low = df['Low'].iloc[i-lookback:i].min()
                    current_low = df['Low'].iloc[i]

                    # 檢查反彈幅度
                    if current_low <= recent_low and df['Close'].iloc[i] > df['Close'].iloc[i-1] * 1.05:
                        # 檢查成交量放大
                        volume_ratio = df['Volume'].iloc[i] / df['Volume'].iloc[i-10:i].mean()
                        if volume_ratio > 2.0:
                            buy_signals.append({
                                'index': i,
                                'date_ts': df['date_ts'].iloc[i],
                                'price': df['Close'].iloc[i]
                            })

                # 賣出條件：跌破MA10或反彈過度
                if (i > 0 and df['Close'].iloc[i] < df['MA10'].iloc[i]) or df['RSI'].iloc[i] > 75:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查破底反彈信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_ashui_signals(self, df):
        """檢查阿水一式策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(60, len(df)):
                # 阿水一式買入條件
                conditions_met = 0

                # 1. 布林帶突破
                if df['Close'].iloc[i] > df['BB_Upper'].iloc[i]:
                    conditions_met += 1

                # 2. 成交量放大
                if df['Volume'].iloc[i] > df['Volume_MA20'].iloc[i] * 1.5:
                    conditions_met += 1

                # 3. MA20向上
                if i > 0 and df['MA20'].iloc[i] > df['MA20'].iloc[i-1]:
                    conditions_met += 1

                # 4. RSI適中
                if 30 < df['RSI'].iloc[i] < 80:
                    conditions_met += 1

                if conditions_met >= 3:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出條件：跌破布林中軌
                if df['Close'].iloc[i] < df['BB_Middle'].iloc[i]:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查阿水一式信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_ashui_short_signals(self, df):
        """檢查阿水二式策略信號（空方策略）"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(60, len(df)):
                # 阿水二式（空方）條件
                conditions_met = 0

                # 1. 高檔整理
                if df['Close'].iloc[i] > df['MA60'].iloc[i] * 1.2:
                    conditions_met += 1

                # 2. 成交量萎縮
                if df['Volume'].iloc[i] < df['Volume_MA20'].iloc[i] * 0.8:
                    conditions_met += 1

                # 3. RSI過高
                if df['RSI'].iloc[i] > 70:
                    conditions_met += 1

                # 空方信號作為賣出信號
                if conditions_met >= 2:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 買入條件：跌破後反彈
                if (df['Close'].iloc[i] < df['MA20'].iloc[i] and
                    df['Close'].iloc[i] > df['Close'].iloc[i-1] * 1.03):
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查阿水二式信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_tibetan_mastiff_signals(self, df):
        """檢查藏獒策略信號（動能策略）"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(50, len(df)):
                # 藏獒策略買入條件（動能突破）
                conditions_met = 0

                # 1. 價格突破MA20
                if df['Close'].iloc[i] > df['MA20'].iloc[i] * 1.02:
                    conditions_met += 1

                # 2. 成交量爆量
                if df['Volume'].iloc[i] > df['Volume_MA10'].iloc[i] * 2.0:
                    conditions_met += 1

                # 3. RSI強勢
                if df['RSI'].iloc[i] > 60:
                    conditions_met += 1

                # 4. 均線多頭排列
                if (df['MA5'].iloc[i] > df['MA10'].iloc[i] > df['MA20'].iloc[i]):
                    conditions_met += 1

                if conditions_met >= 3:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出條件：動能衰退
                if df['RSI'].iloc[i] < 40 or df['Close'].iloc[i] < df['MA10'].iloc[i]:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查藏獒策略信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_canslim_signals(self, df):
        """檢查CANSLIM量價齊升策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(60, len(df)):
                # CANSLIM買入條件
                conditions_met = 0

                # 1. 價格創新高
                if df['Close'].iloc[i] == df['Close'].iloc[i-50:i+1].max():
                    conditions_met += 1

                # 2. 成交量放大
                if df['Volume'].iloc[i] > df['Volume_MA20'].iloc[i] * 1.5:
                    conditions_met += 1

                # 3. 相對強度（簡化版）
                if df['RSI'].iloc[i] > 70:
                    conditions_met += 1

                # 4. 均線支撐
                if df['Close'].iloc[i] > df['MA20'].iloc[i]:
                    conditions_met += 1

                if conditions_met >= 3:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出條件：跌破關鍵支撐
                if df['Close'].iloc[i] < df['MA20'].iloc[i] * 0.92:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查CANSLIM信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_timid_cat_signals(self, df):
        """檢查膽小貓策略信號（低風險策略）"""
        buy_signals = []
        sell_signals = []

        try:
            for i in range(100, len(df)):
                # 膽小貓買入條件
                conditions_met = 0

                # 1. 低價股（30元以下）
                if df['Close'].iloc[i] < 30:
                    conditions_met += 1

                # 2. 低波動
                volatility = df['Close'].iloc[i-20:i].std() / df['Close'].iloc[i-20:i].mean()
                if volatility < 0.08:
                    conditions_met += 1

                # 3. 創新高
                if df['Close'].iloc[i] == df['Close'].iloc[i-100:i+1].max():
                    conditions_met += 1

                # 4. 成交量適中
                if df['Volume'].iloc[i] > 100000:
                    conditions_met += 1

                if conditions_met >= 3:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出條件：3%停損
                if i > 0 and df['Close'].iloc[i] < df['Close'].iloc[i-1] * 0.97:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查膽小貓信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def check_triple_rsi_signals(self, df):
        """檢查三頻率RSI策略信號"""
        buy_signals = []
        sell_signals = []

        try:
            # 計算三頻率RSI
            for period in [20, 60, 120]:
                delta = df['Close'].diff()
                gain = delta.where(delta > 0, 0).rolling(period).mean()
                loss = -delta.where(delta < 0, 0).rolling(period).mean()
                rs = gain / loss
                df[f'RSI{period}'] = 100 - (100 / (1 + rs))

            for i in range(120, len(df)):
                # 三頻率RSI買入條件
                conditions_met = 0

                # 1. RSI120 > 55
                if df['RSI120'].iloc[i] > 55:
                    conditions_met += 1

                # 2. RSI60 < 75
                if df['RSI60'].iloc[i] < 75:
                    conditions_met += 1

                # 3. RSI20上漲
                if i >= 3 and df['RSI20'].iloc[i] > df['RSI20'].iloc[i-3] * 1.02:
                    conditions_met += 1

                # 4. RSI20高檔頓化
                rsi20_high = True
                for j in range(max(0, i-3), i):
                    if df['RSI20'].iloc[j] <= 75:
                        rsi20_high = False
                        break
                if rsi20_high:
                    conditions_met += 1

                if conditions_met >= 4:
                    buy_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

                # 賣出條件：跌破季線
                if df['Close'].iloc[i] < df['MA60'].iloc[i]:
                    sell_signals.append({
                        'index': i,
                        'date_ts': df['date_ts'].iloc[i],
                        'price': df['Close'].iloc[i]
                    })

        except Exception as e:
            logging.error(f"檢查三頻率RSI信號失敗: {str(e)}")

        return buy_signals, sell_signals

    def on_strategy_changed(self):
        """當策略選擇改變時的處理"""
        self.current_strategy = self.strategy_combo.currentText()
        # 如果當前有顯示的K線圖，重新繪製信號
        if hasattr(self, 'current_chart_stock') and self.current_chart_stock:
            self.update_chart(self.current_chart_stock)

    def on_signal_display_changed(self):
        """當信號顯示設置改變時的處理"""
        if self.show_all_signals_checkbox.isChecked():
            self.max_signals_display = None  # 顯示所有信號
            self.max_signals_spinbox.setEnabled(False)
        else:
            self.max_signals_display = self.max_signals_spinbox.value()
            self.max_signals_spinbox.setEnabled(True)

        # 如果當前有顯示的K線圖，重新繪製信號
        if hasattr(self, 'current_chart_stock') and self.current_chart_stock:
            self.update_chart(self.current_chart_stock)

    def apply_chart_date_range(self):
        if self.current_chart_stock:
            range_text = self.chart_date_range_combo.currentText()
            self.update_chart(self.current_chart_stock, range_text)
            self.status_label.setText(f"已更新 {self.current_chart_stock} 圖表日期範圍: {range_text}")
        else:
            self.status_label.setText("請先選擇股票")

    def run_backtest_for_stock(self, stock_id):
        self.status_label.setText(f"{stock_id} 回測中...")
        # 回測邏輯可根據需求擴充 (此處僅示範)
        self.status_label.setText(f"{stock_id} 回測完成")

    # ===== 策略操作 =====
    def create_strategy(self):
        dialog = StrategyDialog(parent=self)
        if dialog.exec():
            params = dialog.get_strategy_params()
            strategy_file = f"strategies/{params.name}.json"
            if os.path.exists(strategy_file):
                QMessageBox.warning(self, "警告", "已存在同名策略，請使用其他名稱")
                return
            if not os.path.exists("strategies"):
                os.makedirs("strategies")
            params.save(strategy_file)
            self.strategy_combo.addItem(params.name)
            self.strategy_combo.setCurrentText(params.name)
            QMessageBox.information(self, "成功", "策略創建成功")

    def edit_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            QMessageBox.warning(self, "警告", "請先選擇要編輯的策略")
            return
        strategy_file = f"strategies/{current_strategy}.json"
        if not os.path.exists(strategy_file):
            QMessageBox.warning(self, "警告", "策略文件不存在")
            return
        params = StrategyParams.load(strategy_file)
        dialog = StrategyDialog(params, parent=self)
        if dialog.exec():
            new_params = dialog.get_strategy_params()
            if new_params.name != current_strategy:
                new_file = f"strategies/{new_params.name}.json"
                if os.path.exists(new_file):
                    QMessageBox.warning(self, "警告", "已存在同名策略，請使用其他名稱")
                    return
                os.remove(strategy_file)
                idx = self.strategy_combo.findText(current_strategy)
                self.strategy_combo.removeItem(idx)
                self.strategy_combo.addItem(new_params.name)
                self.strategy_combo.setCurrentText(new_params.name)
            new_params.save(f"strategies/{new_params.name}.json")
            QMessageBox.information(self, "成功", "策略更新成功")

    def delete_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            QMessageBox.warning(self, "警告", "請先選擇要刪除的策略")
            return
        if current_strategy == "MA趨勢與RSI動能":
            QMessageBox.warning(self, "警告", "不能刪除預設策略")
            return
        reply = QMessageBox.question(self, "確認", f"確定要刪除策略 '{current_strategy}' 嗎？",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            strategy_file = f"strategies/{current_strategy}.json"
            if os.path.exists(strategy_file):
                os.remove(strategy_file)
            idx = self.strategy_combo.findText(current_strategy)
            self.strategy_combo.removeItem(idx)
            QMessageBox.information(self, "成功", "策略已刪除")

    def update_strategy_list(self):
        if not os.path.exists("strategies"):
            os.makedirs("strategies")
        default_strategy_file = "strategies/MA趨勢與RSI動能.json"
        if not os.path.exists(default_strategy_file):
            self.create_default_strategy()
        
        current_strategy = self.strategy_combo.currentText()
        self.strategy_combo.clear()
        self.strategy_combo.addItem("MA趨勢與RSI動能")
        for f in os.listdir("strategies"):
            if f.endswith(".json") and f != "MA趨勢與RSI動能.json":
                strategy_name = f[:-5]
                self.strategy_combo.addItem(strategy_name)
        idx = self.strategy_combo.findText(current_strategy)
        if idx >= 0:
            self.strategy_combo.setCurrentIndex(idx)
        else:
            self.strategy_combo.setCurrentIndex(0)

    def create_default_strategy(self):
        default_strategy = {
            "name": "MA趨勢與RSI動能",
            "description": "使用均線趨勢與RSI動能的選股策略",
            "parameters": {
                "ma240_days": 240,
                "trend_days": 30,
                "ma60_days": 60,
                "ma20_days": 20,
                "volume_short": 3,
                "volume_long": 18,
                "cross_days": 3,
                "rsi_long": 13,
                "rsi_long_threshold": 50,
                "rsi_short": 6,
                "rsi_short_threshold": 70
            }
        }
        if not os.path.exists("strategies"):
            os.makedirs("strategies")
        with open("strategies/MA趨勢與RSI動能.json", "w", encoding="utf-8") as f:
            json.dump(default_strategy, f, ensure_ascii=False, indent=4)

    # ===== 核心：執行選股 =====
    def run_screening(self):
        """
        依照以下 5 個條件進行選股：
         1) 年線(MA240)向上：今日 MA240 > 昨日 MA240
         2) 年線持續向上（暫定：今日 MA240 > 30 天前 MA240）
         3) 季線(MA60)或月線(MA20)至少有一條向上：今日 MA60 > 昨日 MA60 或 今日 MA20 > 昨日 MA20
         4) 3日均量與18日均量黃金交叉的 3 日內
         5) 13日 RSI > 50 且 6日 RSI > 70
        """
        if not self.price_conn:
            QMessageBox.warning(self, "警告", "尚未連接資料庫，無法選股")
            return
        
        # 禁用按鈕避免重複點擊
        self.setEnabled(False)
        self.status_label.setText("準備選股資料中...")
        QApplication.processEvents()  # 強制更新UI

        mode = self.date_mode_combo.currentText()
        if mode == "單日選股":
            latest_date_str = self.single_date_input.text().strip()
        else:
            latest_date_str = self.end_date_input.text().strip()
        
        try:
            latest_date = pd.to_datetime(latest_date_str)
        except:
            QMessageBox.warning(self, "警告", f"無效的日期格式: {latest_date_str}")
            self.setEnabled(True)
            return
        
        if latest_date < pd.to_datetime('2020-01-10'):
            QMessageBox.warning(self, "警告", "資料起始時間不足(需早於 2020-01-10)")
            self.setEnabled(True)
            return
        
        query = f"""
            SELECT date,
                   stock_id,
                   stock_name,
                   industry,
                   Open,
                   High,
                   Low,
                   Close,
                   Volume,
                   Change
            FROM {self.price_table}
            WHERE date <= '{latest_date.strftime("%Y-%m-%d")}'
            ORDER BY date
        """
        
        try:
            df_all = pd.read_sql(query, self.price_conn, parse_dates=['date'])
        except Exception as e:
            QMessageBox.warning(self, "警告", f"SQL 查詢失敗: {e}")
            self.setEnabled(True)
            return
        
        if df_all.empty:
            QMessageBox.information(self, "提示", "查無資料")
            self.setEnabled(True)
            return
        
        detailed_results = []
        stock_ids = df_all['stock_id'].unique()
        total_stocks = len(stock_ids)
        processed = 0
        
        # 加入進度提示
        progress_msg = QLabel()
        progress_bar = QProgressBar()
        progress_bar.setMaximum(total_stocks)
        self.statusBar.addWidget(progress_msg)
        self.statusBar.addWidget(progress_bar)

        for sid in stock_ids:
            processed += 1
            progress_msg.setText(f"正在分析 {processed}/{total_stocks} - {sid}")
            progress_bar.setValue(processed)
            QApplication.processEvents()  # 保持UI響應
            
            df = df_all[df_all['stock_id'] == sid].copy()
            df = df.sort_values('date').reset_index(drop=True)
            if len(df) < 240:
                continue
            
            # 計算技術指標：使用 talib（若無，則用 rolling）
            try:
                import talib
                df['MA240'] = talib.SMA(df['Close'], timeperiod=240)
                df['MA60']  = talib.SMA(df['Close'], timeperiod=60)
                df['MA20']  = talib.SMA(df['Close'], timeperiod=20)
                df['Volume_MA3']  = talib.SMA(df['Volume'], timeperiod=3)
                df['Volume_MA18'] = talib.SMA(df['Volume'], timeperiod=18)
                df['RSI6']  = talib.RSI(df['Close'], timeperiod=6)
                df['RSI13'] = talib.RSI(df['Close'], timeperiod=13)
            except ImportError:
                df['MA240'] = df['Close'].rolling(240).mean()
                df['MA60']  = df['Close'].rolling(60).mean()
                df['MA20']  = df['Close'].rolling(20).mean()
                df['Volume_MA3']  = df['Volume'].rolling(3).mean()
                df['Volume_MA18'] = df['Volume'].rolling(18).mean()
                delta = df['Close'].diff()
                gain6 = delta.where(delta > 0, 0).rolling(6).mean()
                loss6 = -delta.where(delta < 0, 0).rolling(6).mean()
                rs6 = gain6 / loss6
                df['RSI6'] = 100 - (100 / (1 + rs6))
                gain13 = delta.where(delta > 0, 0).rolling(13).mean()
                loss13 = -delta.where(delta < 0, 0).rolling(13).mean()
                rs13 = gain13 / loss13
                df['RSI13'] = 100 - (100 / (1 + rs13))
            
            df = df.dropna(subset=['MA240','MA60','MA20','Volume_MA3','Volume_MA18','RSI6','RSI13'])
            if len(df) < 240:
                continue
            
            latest_data = df.iloc[-1]
            prev_data = df.iloc[-2] if len(df) >= 2 else None
            
            dt_30 = latest_data['date'] - timedelta(days=30)
            df_30 = df[df['date'] <= dt_30]
            month_ago_data = df_30.iloc[-1] if not df_30.empty else None
            
            change_percent = latest_data['Change']
            
            # 條件判斷
            cond1 = prev_data is not None and (latest_data['MA240'] > prev_data['MA240'])
            cond2 = month_ago_data is not None and (latest_data['MA240'] > month_ago_data['MA240'])
            cond3 = False
            if prev_data is not None:
                cond3 = (latest_data['MA60'] > prev_data['MA60']) or (latest_data['MA20'] > prev_data['MA20'])
            cond4 = False
            lookback = min(3, len(df))
            for i in range(1, lookback+1):
                idx = -i
                if idx - 1 < -len(df):
                    break
                if (df['Volume_MA3'].iloc[idx] > df['Volume_MA18'].iloc[idx] and
                    df['Volume_MA3'].iloc[idx-1] <= df['Volume_MA18'].iloc[idx-1]):
                    cond4 = True
                    break
            cond5 = (latest_data['RSI13'] > 50) and (latest_data['RSI6'] > 70)
            
            total_cond = sum([cond1, cond2, cond3, cond4, cond5])
            
            detailed_results.append({
                'stock_id': sid,
                'stock_name': latest_data['stock_name'],
                'industry': latest_data['industry'],
                'close': latest_data['Close'],
                'change': round(change_percent, 2),
                'volume': round(latest_data['Volume']/1000),
                'rsi6': round(latest_data['RSI6'], 2),
                'rsi13': round(latest_data['RSI13'], 2),
                'cond1': cond1,
                'cond2': cond2,
                'cond3': cond3,
                'cond4': cond4,
                'cond5': cond5,
                'total_conditions': total_cond
            })
        
        # 清理進度顯示
        self.statusBar.removeWidget(progress_msg)
        self.statusBar.removeWidget(progress_bar)
        self.setEnabled(True)
        
        self.create_detailed_result_table(detailed_results)
        msg = f"選股完成，共分析 {len(detailed_results)} 檔股票，其中 {len([r for r in detailed_results if r['total_conditions'] == 5])} 檔完全符合 5 個條件"
        self.status_label.setText(msg)
        logging.info(msg)

        # 更新選股結果列表
        self.filtered_stocks_list.clear()
        fully_matched = [r for r in detailed_results if r['total_conditions'] == 5]
        for res in sorted(fully_matched, key=lambda x: (-x['total_conditions'], x['stock_id'])):
            item = QListWidgetItem(f"{res['stock_id']} {res['stock_name']}")
            item.setData(Qt.ItemDataRole.UserRole, res['stock_id'])
            self.filtered_stocks_list.addItem(item)
        
        # 更新分頁標籤顯示數量
        self.left_tabs.setTabText(1, f"選股結果 ({len(fully_matched)})")
        
        # 自動切換到選股結果分頁
        self.left_tabs.setCurrentIndex(1)

    def create_detailed_result_table(self, results):
        for i in reversed(range(self.result_layout.count())):
            w = self.result_layout.itemAt(i).widget()
            if w:
                w.setParent(None)
        
        title_label = QLabel("選股結果（含 5 個條件符合情況）")
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        self.result_layout.addWidget(title_label)
        
        table = QTableWidget()
        headers = [
            "股票代碼", "名稱", "產業", "收盤價", "漲跌幅(數值)", "成交量(張)",
            "RSI6", "RSI13",
            "條件1", "條件2", "條件3", "條件4", "條件5", "符合條件數"
        ]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setRowCount(len(results))
        
        # 設定表格樣式
        table.setStyleSheet("""
            QTableWidget {
                font-size: 11pt;
                selection-background-color: #444;
            }
            QTableWidgetItem[conditionMet="true"] {
                background-color: #1B5E20;  /* 深綠色 */
                color: white;
            }
            QTableWidgetItem[conditionMet="false"] {
                background-color: #B71C1C;  /* 深紅色 */
                color: white;
            }
        """)
        
        for row_idx, res in enumerate(results):
            table.setItem(row_idx, 0, QTableWidgetItem(str(res['stock_id'])))
            table.setItem(row_idx, 1, QTableWidgetItem(res['stock_name'] or ""))
            table.setItem(row_idx, 2, QTableWidgetItem(res['industry'] or ""))
            table.setItem(row_idx, 3, QTableWidgetItem(f"{res['close']:.2f}"))
            table.setItem(row_idx, 4, QTableWidgetItem(str(res['change'])))
            table.setItem(row_idx, 5, QTableWidgetItem(str(res['volume'])))
            table.setItem(row_idx, 6, QTableWidgetItem(str(res['rsi6'])))
            table.setItem(row_idx, 7, QTableWidgetItem(str(res['rsi13'])))
            table.setItem(row_idx, 8, QTableWidgetItem("Yes" if res['cond1'] else "No"))
            table.setItem(row_idx, 9, QTableWidgetItem("Yes" if res['cond2'] else "No"))
            table.setItem(row_idx, 10, QTableWidgetItem("Yes" if res['cond3'] else "No"))
            table.setItem(row_idx, 11, QTableWidgetItem("Yes" if res['cond4'] else "No"))
            table.setItem(row_idx, 12, QTableWidgetItem("Yes" if res['cond5'] else "No"))
            table.setItem(row_idx, 13, QTableWidgetItem(str(res['total_conditions'])))
            
            # 條件欄位顏色標記
            for col in range(8, 13):  # 條件1~5的欄位
                item = table.item(row_idx, col)
                condition_met = res[f'cond{col-7}']
                item.setData(Qt.ItemDataRole.UserRole, {"conditionMet": str(condition_met).lower()})
            
            # 符合條件數顏色漸層
            cond_count = res['total_conditions']
            cond_item = table.item(row_idx, 13)
            intensity = int(255 * (cond_count/5))
            cond_item.setBackground(QColor(intensity, 255-intensity, 0))
            
            # 漲跌幅顏色
            change_item = table.item(row_idx, 4)
            if res['change'] > 0:
                change_item.setForeground(QColor(255, 0, 0))  # 紅漲
            else:
                change_item.setForeground(QColor(0, 255, 0))  # 綠跌
        
        table.resizeColumnsToContents()
        self.result_layout.addWidget(table)

    def load_stock_list_async(self):
        """異步加載股票列表"""
        def fetch_stocks():
            try:
                conn = sqlite3.connect('tw_stock.db')  # 基本資訊數據庫
                df = pd.read_sql("""
                    SELECT s.stock_id, s.stock_name, i.name as industry 
                    FROM listed_stocks s
                    LEFT JOIN industries i ON s.industry_id = i.id
                """, conn)
                conn.close()
                return df
            except Exception as e:
                logging.error(f"加載股票列表失敗: {str(e)}")
                return pd.DataFrame()

        self.thread_pool.start(
            QThread.create(fetch_stocks), 
            self.update_stock_list
        )

    def init_database_connection(self):
        """新增的數據庫初始化方法"""
        if not hasattr(self, 'db_worker'):
            self.db_worker = DatabaseWorker(":memory:")
            self.db_worker.initialized.connect(self.on_db_initialized)
            self.db_worker.error.connect(self.show_error)
            self.db_worker.start()

# ===================== main 入口修正 =====================
if __name__ == "__main__":
    # 必須在創建QApplication之前設置的參數
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 預加載資源
    pg.setConfigOptions(useOpenGL=True, antialias=True)
    
    # 啟動畫面
    splash = QSplashScreen(QPixmap('splash.png'))
    splash.show()
    app.processEvents()
    
    # 主窗口初始化
    window = StockScreenerGUI()
    
    # 分階段初始化
    splash.showMessage("初始化核心組件...")
    window.lazy_init_phase1()
    app.processEvents()
    
    splash.showMessage("加載數據庫...")
    window.init_database_connection()  # 使用正確的方法名稱
    app.processEvents()
    
    splash.showMessage("準備界面...")
    window.lazy_init_phase2()
    app.processEvents()
    
    window.show()
    splash.finish(window)
    sys.exit(app.exec())
