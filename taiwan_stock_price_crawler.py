"""
台股每日交易資料爬蟲模組
基於原始參考程式改寫，支援證交所(TWSE)和櫃買中心(TPEX)資料爬取
"""

import datetime
import time
import typing
import sqlite3
import os
import pandas as pd
import requests
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def is_weekend(day: int) -> bool:
    """檢查是否為週末"""
    return day in [5, 6]


def gen_task_parameter_list(start_date: str, end_date: str) -> typing.List[dict]:
    """生成任務參數列表"""
    start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
    end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
    days = (end_date - start_date).days + 1
    
    date_list = [
        start_date + datetime.timedelta(days=day)
        for day in range(days)
    ]
    
    # 排除掉周末非交易日
    date_list = [
        dict(
            date=str(d),
            data_source=data_source,
        )
        for d in date_list
        for data_source in ["twse", "tpex"]
        if not is_weekend(d.weekday())
    ]
    return date_list


def clear_data(df: pd.DataFrame) -> pd.DataFrame:
    """資料清理, 將文字轉成數字"""
    for col in [
        "TradeVolume",
        "TransactionCount",
        "TradeValue",
        "Open",
        "Max",
        "Min",
        "Close",
        "Change",
    ]:
        if col in df.columns:
            df[col] = (
                df[col]
                .astype(str)
                .str.replace(",", "")
                .str.replace("X", "")
                .str.replace("+", "")
                .str.replace("----", "0")
                .str.replace("---", "0")
                .str.replace("--", "0")
                .str.replace(" ", "")
                .str.replace("除權息", "0")
                .str.replace("除息", "0")
                .str.replace("除權", "0")
            )
    return df


def colname_zh2en(df: pd.DataFrame, colname: typing.List[str]) -> pd.DataFrame:
    """資料欄位轉換, 英文有助於接下來存入資料庫"""
    taiwan_stock_price = {
        "證券代號": "StockID",
        "證券名稱": "",
        "成交股數": "TradeVolume",
        "成交筆數": "TransactionCount",
        "成交金額": "TradeValue",
        "開盤價": "Open",
        "最高價": "Max",
        "最低價": "Min",
        "收盤價": "Close",
        "漲跌(+/-)": "Dir",
        "漲跌價差": "Change",
        "最後揭示買價": "",
        "最後揭示買量": "",
        "最後揭示賣價": "",
        "最後揭示賣量": "",
        "本益比": "",
    }
    df.columns = [taiwan_stock_price[col] for col in colname]
    df = df.drop([""], axis=1, errors='ignore')
    return df


def twse_header():
    """網頁瀏覽時, 所帶的 request header 參數, 模仿瀏覽器發送 request"""
    return {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Connection": "keep-alive",
        "Host": "www.twse.com.tw",
        "Referer": "https://www.twse.com.tw/zh/page/trading/exchange/MI_INDEX.html",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
    }


def tpex_header():
    """網頁瀏覽時, 所帶的 request header 參數, 模仿瀏覽器發送 request"""
    return {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Connection": "keep-alive",
        "Host": "www.tpex.org.tw",
        "Referer": "https://www.tpex.org.tw/zh-tw/mainboard/trading/info/pricing.html",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
    }


def set_column(df: pd.DataFrame) -> pd.DataFrame:
    """設定資料欄位名稱"""
    df.columns = [
        "StockID",
        "Close",
        "Change",
        "Open",
        "Max",
        "Min",
        "TradeVolume",
        "TradeValue",
        "TransactionCount",
    ]
    return df


def crawler_tpex(date: str) -> pd.DataFrame:
    """
    櫃買中心網址
    https://www.tpex.org.tw/zh-tw/mainboard/trading/info/pricing.html
    """
    logger.info(f"crawler_tpex: {date}")
    # headers 中的 Request url
    url = "https://www.tpex.org.tw/www/zh-tw/afterTrading/dailyQuotes"
    # 避免被櫃買中心 ban ip, 在每次爬蟲時, 先 sleep 5 秒
    time.sleep(5)
    
    try:
        # request method
        res = requests.post(
            url,
            headers=tpex_header(),
            data={
                "date": date.replace("-", "/"),
                "response": "json",
            },
            timeout=30,
            verify=False  # 跳過SSL驗證
        )
        
        data = res.json().get("tables", [{}])
        total_count = data[0].get("totalCount", 0) if data else 0
        
        if total_count == 0:
            logger.warning(f"TPEX {date}: 無資料")
            return pd.DataFrame()
            
        df = pd.DataFrame(data[0]["data"])
        # 櫃買中心回傳的資料, 並無資料欄位, 因此這裡我們直接用 index 取特定欄位
        df = df[[0, 2, 3, 4, 5, 6, 7, 8, 9]]
        # 欄位中英轉換
        df = set_column(df.copy())
        df["Date"] = date
        df["DataSource"] = "tpex"
        df = clear_data(df.copy())
        
        logger.info(f"TPEX {date}: 成功取得 {len(df)} 筆資料")
        return df
        
    except Exception as e:
        logger.error(f"TPEX {date} 爬取失敗: {e}")
        return pd.DataFrame()


def crawler_twse(date: str) -> pd.DataFrame:
    """
    證交所網址
    https://www.twse.com.tw/zh/page/trading/exchange/MI_INDEX.html
    """
    logger.info(f"crawler_twse: {date}")
    # headers 中的 Request url
    url = "https://www.twse.com.tw/exchangeReport/MI_INDEX?response=json&date={date}&type=ALL"
    url = url.format(date=date.replace("-", ""))
    # 避免被證交所 ban ip, 在每次爬蟲時, 先 sleep 5 秒
    time.sleep(5)

    try:
        # request method
        res = requests.get(url, headers=twse_header(), timeout=30, verify=False)

        # 2009 年以後的資料, 股價在 response 中的 data9
        # 2009 年以前的資料, 股價在 response 中的 data8
        # 不同格式, 在證交所的資料中, 是很常見的,
        # 沒資料的情境也要考慮進去，例如現在週六沒有交易，但在 2007 年週六是有交易的
        df = pd.DataFrame()

        response_data = res.json()

        # 檢查新的API結構 (tables格式)
        if "tables" in response_data and response_data["tables"]:
            # 尋找包含股票資料的表格
            for table in response_data["tables"]:
                fields = table.get("fields", [])
                if "證券代號" in fields and table.get("data"):
                    df = pd.DataFrame(table["data"])
                    colname = fields
                    break
        # 檢查舊的API結構
        elif "data9" in response_data:
            df = pd.DataFrame(response_data["data9"])
            colname = response_data["fields9"]
        elif "data8" in response_data:
            df = pd.DataFrame(response_data["data8"])
            colname = response_data["fields8"]
        elif response_data.get("stat") in [
            "查詢日期小於93年2月11日，請重新查詢!",
            "很抱歉，沒有符合條件的資料!",
        ]:
            logger.warning(f"TWSE {date}: {response_data['stat']}")
            return pd.DataFrame()
        else:
            logger.warning(f"TWSE {date}: 未知的回應格式")
            return pd.DataFrame()

        if len(df) == 0:
            logger.warning(f"TWSE {date}: 無資料")
            return pd.DataFrame()

        # 欄位中英轉換
        df = colname_zh2en(df.copy(), colname)
        df["Date"] = date
        df["DataSource"] = "twse"
        df = convert_change(df.copy())
        df = clear_data(df.copy())

        logger.info(f"TWSE {date}: 成功取得 {len(df)} 筆資料")
        return df

    except Exception as e:
        logger.error(f"TWSE {date} 爬取失敗: {e}")
        return pd.DataFrame()


def convert_change(df: pd.DataFrame) -> pd.DataFrame:
    """轉換漲跌幅資料"""
    logger.info("convert_change")
    if "Dir" in df.columns and "Change" in df.columns:
        df["Dir"] = (
            df["Dir"]
            .str.split(">")
            .str[1]
            .str.split("<")
            .str[0]
        )
        df["Change"] = (df["Dir"] + df["Change"])
        df["Change"] = (
            df["Change"]
            .str.replace(" ", "")
            .str.replace("X", "")
            .astype(float)
        )
        df = df.fillna("")
        df = df.drop(["Dir"], axis=1)
    return df


def crawler(parameter: typing.Dict[str, typing.Union[str, int, float]]) -> pd.DataFrame:
    """主要爬蟲函數"""
    logger.info(f"開始爬取: {parameter}")
    date = parameter.get("date", "")
    data_source = parameter.get("data_source", "")
    
    if data_source == "twse":
        df = crawler_twse(date)
    elif data_source == "tpex":
        df = crawler_tpex(date)
    else:
        logger.error(f"未知的資料源: {data_source}")
        return pd.DataFrame()
    
    return df


class DailyTradingDatabase:
    """每日交易資料庫管理類"""

    def __init__(self, db_path: str = None):
        if db_path is None:
            # 預設路徑：與price.db相同目錄
            default_dir = r"D:\Finlab\history\tables"
            os.makedirs(default_dir, exist_ok=True)  # 確保目錄存在
            self.db_path = os.path.join(default_dir, "daily_trading.db")
        else:
            self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化資料庫"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 創建每日交易資料表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS daily_trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                StockID TEXT NOT NULL,
                Date TEXT NOT NULL,
                DataSource TEXT NOT NULL,
                Open REAL,
                Max REAL,
                Min REAL,
                Close REAL,
                Change REAL,
                TradeVolume INTEGER,
                TradeValue INTEGER,
                TransactionCount INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(StockID, Date, DataSource)
            )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_date ON daily_trading_data(StockID, Date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_date ON daily_trading_data(Date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_source ON daily_trading_data(DataSource)")
        
        conn.commit()
        conn.close()
        logger.info(f"資料庫初始化完成: {self.db_path}")
    
    def save_data(self, df: pd.DataFrame) -> int:
        """儲存資料到資料庫"""
        if df.empty:
            return 0

        conn = sqlite3.connect(self.db_path)

        try:
            # 先嘗試刪除相同日期和資料源的資料，避免重複
            cursor = conn.cursor()
            for _, row in df.iterrows():
                cursor.execute("""
                    DELETE FROM daily_trading_data
                    WHERE StockID = ? AND Date = ? AND DataSource = ?
                """, (row['StockID'], row['Date'], row['DataSource']))

            # 插入新資料
            df.to_sql('daily_trading_data', conn, if_exists='append', index=False)
            saved_count = len(df)
            conn.commit()
            logger.info(f"成功儲存 {saved_count} 筆資料到資料庫")
            return saved_count

        except Exception as e:
            logger.error(f"儲存資料失敗: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def get_existing_dates(self, start_date: str, end_date: str) -> set:
        """取得已存在的日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT Date FROM daily_trading_data 
            WHERE Date BETWEEN ? AND ?
        """, (start_date, end_date))
        
        existing_dates = {row[0] for row in cursor.fetchall()}
        conn.close()
        return existing_dates
    
    def get_data_count(self) -> int:
        """取得資料總數"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM daily_trading_data")
        count = cursor.fetchone()[0]
        conn.close()
        return count
