#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試台指期真實數據獲取功能
"""

import sys
import os
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_taiwan_futures_data():
    """測試台指期數據獲取"""
    print("🧪 測試台指期真實數據獲取")
    print("=" * 40)
    
    try:
        from real_data_sources import RealDataSources
        
        # 創建數據源實例
        data_source = RealDataSources()
        
        print("📊 測試各種台指期數據源...")
        print("-" * 30)
        
        # 測試1: Yahoo Finance台指期
        print("1️⃣ 測試Yahoo Finance台指期數據:")
        tw_data_yahoo = data_source.get_yahoo_finance_data('TXF=F')
        if tw_data_yahoo:
            print(f"   ✅ 成功獲取台指期數據")
            print(f"   📈 價格: {tw_data_yahoo['price']}")
            print(f"   📊 變化: {tw_data_yahoo['change']:+.2f} ({tw_data_yahoo['change_pct']:+.2f}%)")
        else:
            print("   ❌ Yahoo Finance台指期數據獲取失敗")
        
        # 測試2: Yahoo Finance台股加權指數
        print("\n2️⃣ 測試Yahoo Finance台股加權指數:")
        tw_index_yahoo = data_source.get_yahoo_finance_data('^TWII')
        if tw_index_yahoo:
            print(f"   ✅ 成功獲取台股加權指數")
            print(f"   📈 價格: {tw_index_yahoo['price']}")
            print(f"   📊 變化: {tw_index_yahoo['change']:+.2f} ({tw_index_yahoo['change_pct']:+.2f}%)")
        else:
            print("   ❌ Yahoo Finance台股加權指數獲取失敗")
        
        # 測試3: 台灣證交所API
        print("\n3️⃣ 測試台灣證交所API:")
        twse_data = data_source.get_twse_index_data()
        if twse_data:
            print(f"   ✅ 成功獲取證交所數據")
            for name, data in twse_data.items():
                print(f"   📈 {name}: {data['price']}")
                print(f"   📊 變化: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
        else:
            print("   ❌ 台灣證交所API獲取失敗")
        
        # 測試4: 台灣期交所API
        print("\n4️⃣ 測試台灣期交所API:")
        taifex_data = data_source.get_taifex_data()
        if taifex_data:
            print(f"   ✅ 成功獲取期交所數據")
            for name, data in taifex_data.items():
                print(f"   📈 {name}: {data['price']}")
                print(f"   📊 變化: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
        else:
            print("   ❌ 台灣期交所API獲取失敗")
        
        # 測試5: Investing.com台指期
        print("\n5️⃣ 測試Investing.com台指期:")
        investing_data = data_source.get_investing_com_taiwan_data()
        if investing_data:
            print(f"   ✅ 成功獲取Investing.com數據")
            for name, data in investing_data.items():
                print(f"   📈 {name}: {data['price']}")
                print(f"   📊 變化: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
        else:
            print("   ❌ Investing.com台指期獲取失敗")
        
        # 測試6: 綜合台指期數據獲取
        print("\n6️⃣ 測試綜合台指期數據獲取:")
        taiwan_futures = data_source.get_taiwan_futures_data()
        if taiwan_futures:
            print(f"   ✅ 成功獲取台指期相關數據")
            for name, data in taiwan_futures.items():
                source = data.get('status', '未知來源')
                print(f"   📈 {name}: {data['price']}")
                print(f"   📊 變化: {data['change']:+.2f} ({data['change_pct']:+.2f}%)")
                print(f"   🔗 來源: {source}")
        else:
            print("   ❌ 綜合台指期數據獲取失敗")
        
        return taiwan_futures is not None
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_scan_with_taiwan():
    """測試包含台指期的完整掃描"""
    print(f"\n🔍 測試包含台指期的完整掃描")
    print("=" * 35)
    
    try:
        from real_data_sources import RealDataSources
        
        data_source = RealDataSources()
        
        # 執行完整掃描
        scan_results = data_source.run_full_scan()
        
        if scan_results:
            print("✅ 完整掃描成功")
            print(f"📅 掃描時間: {scan_results['timestamp']}")
            print(f"🔗 數據來源: {scan_results['data_sources_used']}")
            print()
            
            # 顯示各類數據統計
            categories = [
                ('us_indices', '🇺🇸 美股指數'),
                ('taiwan_futures', '🇹🇼 台指期'),
                ('commodities', '🛢️ 商品'),
                ('fx_rates', '💱 外匯'),
                ('crypto', '₿ 加密貨幣')
            ]
            
            total_items = 0
            for key, name in categories:
                data = scan_results.get(key, {})
                count = len(data)
                total_items += count
                print(f"{name}: {count} 項")
                
                # 顯示台指期詳細信息
                if key == 'taiwan_futures' and data:
                    for item_name, item_data in data.items():
                        source = item_data.get('status', '未知')
                        change_pct = item_data.get('change_pct', 0)
                        trend = "📈" if change_pct > 0 else "📉" if change_pct < 0 else "➡️"
                        print(f"  {trend} {item_name}: {change_pct:+.2f}% (來源: {source})")
            
            print(f"\n📊 總計: {total_items} 個數據項目")
            
            return True
        else:
            print("❌ 完整掃描失敗")
            return False
            
    except Exception as e:
        print(f"❌ 完整掃描測試失敗: {e}")
        return False

def show_taiwan_data_sources():
    """顯示台指期數據源說明"""
    print(f"\n📋 台指期數據源說明")
    print("=" * 25)
    
    data_sources = [
        {
            "來源": "Yahoo Finance (^TWII)",
            "說明": "台股加權指數，作為台指期參考",
            "優點": "免費、穩定、即時",
            "缺點": "不是直接的期貨數據"
        },
        {
            "來源": "台灣證交所API",
            "說明": "官方台股加權指數數據",
            "優點": "官方數據、權威",
            "缺點": "可能有延遲、格式複雜"
        },
        {
            "來源": "台灣期交所API",
            "說明": "官方台指期貨數據",
            "優點": "直接期貨數據、官方",
            "缺點": "API格式複雜、可能限制"
        },
        {
            "來源": "Investing.com",
            "說明": "第三方台指期數據",
            "優點": "專業財經網站",
            "缺點": "需要HTML解析、可能反爬蟲"
        }
    ]
    
    for i, source in enumerate(data_sources, 1):
        print(f"{i}. {source['來源']}")
        print(f"   說明: {source['說明']}")
        print(f"   優點: {source['優點']}")
        print(f"   缺點: {source['缺點']}")
        print()

def main():
    """主函數"""
    print("🚀 台指期真實數據獲取測試")
    print("=" * 30)
    
    try:
        # 測試台指期數據獲取
        taiwan_success = test_taiwan_futures_data()
        
        # 測試完整掃描
        scan_success = test_full_scan_with_taiwan()
        
        # 顯示數據源說明
        show_taiwan_data_sources()
        
        # 總結
        print(f"🎯 測試總結")
        print("=" * 15)
        
        if taiwan_success and scan_success:
            print("✅ 台指期數據獲取功能測試成功！")
            print()
            print("🎉 主要成果:")
            print("  • ✅ 成功獲取台股相關真實數據")
            print("  • ✅ 多數據源備用機制")
            print("  • ✅ 透明的數據來源標示")
            print("  • ✅ 完整的錯誤處理")
            print()
            print("🚀 台指期功能現在已經完全實現！")
            print("✨ 使用真實數據，絕不欺騙用戶！")
        else:
            print("❌ 部分功能測試失敗")
            print("💡 建議檢查網路連接和數據源可用性")
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
