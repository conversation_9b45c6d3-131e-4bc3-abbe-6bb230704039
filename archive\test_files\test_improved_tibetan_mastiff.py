#!/usr/bin/env python3
"""
改進版藏獒策略测试脚本
测试降低数据要求后的藏獒策略
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImprovedTibetanMastiffTester:
    """改进版藏獒策略测试器"""
    
    def __init__(self):
        self.name = "改进版藏獒策略测试"
    
    def generate_test_data(self, days=80):
        """生成测试数据 - 较短期间的数据"""
        # 创建日期范围
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # 生成基础价格数据
        np.random.seed(42)
        
        base_price = 60
        prices = []
        volumes = []
        
        for i in range(days):
            if i < 50:  # 前50天盘整
                price_change = np.random.normal(0, 0.8)
                volume = np.random.normal(300000, 50000)
            else:  # 后30天突破
                if i == days - 1:  # 最后一天强力突破
                    price_change = 4.0
                    volume = np.random.normal(400000, 50000)
                else:
                    price_change = np.random.normal(1.0, 0.8)
                    volume = np.random.normal(350000, 40000)
            
            if i == 0:
                price = base_price
            else:
                price = max(prices[-1] + price_change, 30)
            
            prices.append(price)
            volumes.append(max(volume, 100000))
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
            'Close': prices,
            'Volume': volumes
        })
        
        # 确保OHLC逻辑正确
        for i in range(len(df)):
            high = max(df.iloc[i]['Open'], df.iloc[i]['Close'])
            low = min(df.iloc[i]['Open'], df.iloc[i]['Close'])
            df.at[i, 'High'] = max(df.iloc[i]['High'], high)
            df.at[i, 'Low'] = min(df.iloc[i]['Low'], low)
        
        return df
    
    def calculate_tibetan_mastiff_indicators(self, df):
        """计算改进版藏獒策略指标"""
        # 10日成交量移动平均
        df['Volume_MA10'] = df['Volume'].rolling(window=10).mean()
        
        # 动态计算最高价期间
        available_days = len(df)
        if available_days >= 250:
            high_period = 250
        elif available_days >= 120:
            high_period = 120
        else:
            high_period = max(60, available_days - 1)
        
        df['High_Period'] = df['High'].rolling(window=high_period).max()
        
        # 营收代理指标
        df['Revenue_Proxy'] = df['Close'].rolling(window=10).mean()
        
        # 动态计算营收底部期间
        revenue_period = min(60, available_days - 1)
        df['Revenue_Min'] = df['Revenue_Proxy'].rolling(window=revenue_period).min()
        
        # 简化的营收增长率
        shift_period = min(20, available_days // 3)
        df['Revenue_Growth'] = ((df['Revenue_Proxy'] / df['Revenue_Proxy'].shift(shift_period)) - 1) * 100
        
        return df
    
    def check_yearly_high(self, df):
        """检查创新高条件"""
        latest = df.iloc[-1]
        current_high = latest['High']
        period_high = latest['High_Period']
        
        if current_high >= period_high * 0.995:
            available_days = len(df)
            if available_days >= 250:
                period_desc = "年新高"
            elif available_days >= 120:
                period_desc = "半年新高"
            else:
                period_desc = f"{available_days}日新高"
            return True, f"创{period_desc}✓({current_high:.2f})"
        else:
            distance_pct = ((period_high - current_high) / period_high) * 100
            return False, f"距新高{distance_pct:.1f}%"
    
    def check_liquidity_condition(self, df):
        """检查流动性条件"""
        latest = df.iloc[-1]
        volume_ma10 = latest['Volume_MA10']
        min_volume = 200 * 1000
        
        if volume_ma10 >= min_volume:
            return True, f"流动性✓({volume_ma10/1000:.0f}K股)"
        else:
            return False, f"流动性不足：{volume_ma10/1000:.0f}K < 200K股"
    
    def check_revenue_bottom(self, df):
        """检查营收底部确认"""
        latest = df.iloc[-1]
        current_price = latest['Revenue_Proxy']
        min_price = latest['Revenue_Min']
        
        if min_price > 0:
            recovery_ratio = current_price / min_price
            if recovery_ratio >= 1.1:
                return True, f"营收底部确认✓(回升{((recovery_ratio-1)*100):.1f}%)"
            else:
                return False, f"营收底部未确认：仅回升{((recovery_ratio-1)*100):.1f}%"
        else:
            return False, "营收数据异常"
    
    def check_revenue_decline_exclusion(self, df):
        """检查营收衰退排除条件"""
        latest = df.iloc[-1]
        revenue_growth = latest.get('Revenue_Growth', 0)
        
        if revenue_growth < -20:
            return False, f"营收衰退严重：{revenue_growth:.1f}%"
        else:
            return True, f"营收衰退检查通过✓({revenue_growth:.1f}%)"
    
    def check_growth_trend_exclusion(self, df):
        """检查成长趋势过老排除条件"""
        latest = df.iloc[-1]
        revenue_growth = latest.get('Revenue_Growth', 0)
        
        if revenue_growth > 100:
            return False, f"成长趋势过老：{revenue_growth:.1f}%过高"
        else:
            return True, f"成长趋势检查通过✓"
    
    def check_monthly_growth_condition(self, df):
        """检查月增率条件"""
        latest = df.iloc[-1]
        revenue_growth = latest.get('Revenue_Growth', 0)
        
        if revenue_growth > -30:
            return True, f"月增率条件✓({revenue_growth:.1f}%)"
        else:
            return False, f"月增率条件未满足：{revenue_growth:.1f}%"
    
    def test_improved_strategy(self, df):
        """测试改进版藏獒策略"""
        try:
            print("=" * 60)
            print("🐕 改进版藏獒策略测试")
            print("=" * 60)
            
            # 计算指标
            df = self.calculate_tibetan_mastiff_indicators(df)
            
            # 显示数据概况
            latest = df.iloc[-1]
            print(f"📊 数据概况：")
            print(f"   数据天数: {len(df)}天")
            print(f"   收盘价: {latest['Close']:.2f}")
            print(f"   10日均量: {latest['Volume_MA10']:,.0f}")
            print(f"   期间最高: {latest['High_Period']:.2f}")
            print(f"   营收代理: {latest['Revenue_Proxy']:.2f}")
            print()
            
            # 逐步检查条件
            print("🔍 条件检查：")
            
            # 1. 创新高
            yearly_high_result = self.check_yearly_high(df)
            print(f"   1. 创新高: {'✅' if yearly_high_result[0] else '❌'} {yearly_high_result[1]}")
            
            # 2. 流动性
            liquidity_result = self.check_liquidity_condition(df)
            print(f"   2. 流动性: {'✅' if liquidity_result[0] else '❌'} {liquidity_result[1]}")
            
            # 3. 营收底部
            revenue_bottom_result = self.check_revenue_bottom(df)
            print(f"   3. 营收底部: {'✅' if revenue_bottom_result[0] else '❌'} {revenue_bottom_result[1]}")
            
            # 4. 营收衰退排除
            revenue_decline_result = self.check_revenue_decline_exclusion(df)
            print(f"   4. 营收衰退排除: {'✅' if revenue_decline_result[0] else '❌'} {revenue_decline_result[1]}")
            
            # 5. 成长趋势排除
            growth_trend_result = self.check_growth_trend_exclusion(df)
            print(f"   5. 成长趋势排除: {'✅' if growth_trend_result[0] else '❌'} {growth_trend_result[1]}")
            
            # 6. 月增率条件
            monthly_growth_result = self.check_monthly_growth_condition(df)
            print(f"   6. 月增率条件: {'✅' if monthly_growth_result[0] else '❌'} {monthly_growth_result[1]}")
            
            # 综合判断
            core_conditions = all([
                yearly_high_result[0],
                liquidity_result[0], 
                revenue_bottom_result[0],
                revenue_decline_result[0],
                growth_trend_result[0],
                monthly_growth_result[0]
            ])
            
            print()
            print("📈 最终结果：")
            if core_conditions:
                print("🎉 ✅ 符合改进版藏獒策略！")
                print("🚀 特色：降低数据要求，更容易找到符合条件的股票")
                print("💡 建议：8%停损，月底换股")
            else:
                print("❌ 不符合改进版藏獒策略")
                failed_conditions = []
                if not yearly_high_result[0]: failed_conditions.append("创新高")
                if not liquidity_result[0]: failed_conditions.append("流动性")
                if not revenue_bottom_result[0]: failed_conditions.append("营收底部")
                if not revenue_decline_result[0]: failed_conditions.append("营收衰退")
                if not growth_trend_result[0]: failed_conditions.append("成长趋势")
                if not monthly_growth_result[0]: failed_conditions.append("月增率")
                print(f"   未满足条件: {', '.join(failed_conditions)}")
            
            print("=" * 60)
            return core_conditions
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 启动改进版藏獒策略测试")
    print("💡 主要改进：降低数据要求，简化营收条件")
    
    tester = ImprovedTibetanMastiffTester()
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_df = tester.generate_test_data(80)  # 只需要80天数据
    
    # 运行测试
    result = tester.test_improved_strategy(test_df)
    
    print(f"\n🏁 测试完成，结果: {'成功' if result else '失败'}")
    
    if result:
        print("\n✨ 改进效果：")
        print("   - 数据要求从250天降低到60天")
        print("   - 营收条件简化，更容易满足")
        print("   - 动态调整期间，适应不同数据长度")
        print("   - 应该能找到更多符合条件的股票")

if __name__ == "__main__":
    main()
