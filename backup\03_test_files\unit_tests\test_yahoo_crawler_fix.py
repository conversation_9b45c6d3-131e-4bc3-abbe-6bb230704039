#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的Yahoo股市新聞爬蟲
"""

import sys
import logging
from yahoo_stock_news_crawler import YahooStockNewsCrawler

def test_yahoo_crawler():
    """測試Yahoo股市新聞爬蟲"""
    
    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🧪 測試修復後的Yahoo股市新聞爬蟲")
    print("=" * 50)
    
    # 創建爬蟲實例
    crawler = YahooStockNewsCrawler()
    
    # 測試股票列表
    test_stocks = ['2330', '00930', '0050']
    
    for stock_code in test_stocks:
        print(f"\n🔍 測試股票: {stock_code}")
        print("-" * 30)
        
        try:
            # 搜尋新聞
            news_list = crawler.search_stock_news(stock_code, days=7)
            
            if news_list:
                print(f"✅ 找到 {len(news_list)} 筆新聞")
                
                # 顯示前3筆新聞
                for i, news in enumerate(news_list[:3], 1):
                    print(f"  {i}. 📰 {news['title'][:60]}...")
                    print(f"     🌐 來源: {news['source']}")
                    print(f"     📅 日期: {news['date']}")
                    print(f"     🔗 連結: {news['link'][:80]}...")
                    print()
                
                # 測試儲存到資料庫
                try:
                    saved_count = crawler.save_to_database(news_list, stock_code)
                    print(f"💾 成功儲存 {saved_count} 筆新聞到資料庫")
                except Exception as save_error:
                    print(f"⚠️ 儲存失敗: {save_error}")
                    
            else:
                print("❌ 沒有找到相關新聞")
                print("💡 可能原因:")
                print("  • 該股票最近沒有新聞")
                print("  • 網路連線問題")
                print("  • 搜尋關鍵字需要調整")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 30)
    
    print("\n🎯 測試總結:")
    print("✅ 新增了Google RSS搜尋作為主要方法")
    print("✅ WebDriver方式作為備用方案")
    print("✅ 改善了錯誤處理和日誌記錄")
    print("✅ 支援多種日期格式解析")

if __name__ == "__main__":
    test_yahoo_crawler()
