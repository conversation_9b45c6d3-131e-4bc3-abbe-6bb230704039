#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試監控系統自動啟動功能
驗證即時股價監控和備用監控在啟動時自動開始監控
"""

import sys
import time
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_realtime_monitor_auto_start():
    """測試即時股價監控自動啟動"""
    print("🔍 測試即時股價監控自動啟動")
    print("="*50)
    
    try:
        from real_time_stock_monitor import RealTimeStockMonitor
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ 即時監控模組載入成功")
        
        # 創建監控實例
        monitor = RealTimeStockMonitor()
        print("✅ 即時監控實例創建成功")
        
        # 檢查初始狀態
        initial_monitoring = monitor.monitoring
        print(f"📊 初始監控狀態: {initial_monitoring}")
        
        # 顯示窗口
        monitor.show()
        print("✅ 監控窗口已顯示")
        
        # 等待自動啟動（延遲1秒）
        print("⏳ 等待自動啟動監控...")
        
        # 使用QTimer來檢查狀態
        def check_auto_start():
            current_monitoring = monitor.monitoring
            button_text = monitor.start_stop_btn.text()
            
            print(f"📊 1秒後監控狀態: {current_monitoring}")
            print(f"🔘 按鈕文字: {button_text}")
            
            if current_monitoring and "停止" in button_text:
                print("🎉 即時監控自動啟動成功！")
                print(f"   ✅ 監控狀態: {current_monitoring}")
                print(f"   ✅ 按鈕已變為: {button_text}")
                print(f"   ✅ 定時器已啟動: {monitor.update_timer.isActive()}")
            else:
                print("❌ 即時監控自動啟動失敗")
                print(f"   監控狀態: {current_monitoring}")
                print(f"   按鈕文字: {button_text}")
            
            # 關閉窗口
            monitor.close()
            app.quit()
        
        # 設置檢查定時器
        QTimer.singleShot(2000, check_auto_start)  # 2秒後檢查
        
        # 運行事件循環
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ 即時監控測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_monitor_auto_start():
    """測試備用監控自動啟動"""
    print("\n🔍 測試備用監控自動啟動")
    print("="*40)
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView
        from PyQt6.QtCore import QTimer
        from PyQt6.QtGui import QColor
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ 備用監控模組載入成功")
        
        # 模擬TSRTCMonitorDialog類
        class TestTSRTCMonitorDialog(QDialog):
            def __init__(self):
                super().__init__()
                self.tsrtc_monitor = TSRTCBackupMonitor()
                self.monitored_stocks = ['2330', '2317', '2454', '2412', '2881']
                self.stock_data_cache = {}
                
                self.setup_ui()
                self.setup_timer()
                
                # 🚀 啟動時自動開始監控
                QTimer.singleShot(1000, self.start_monitoring)  # 延遲1秒後自動開始監控
            
            def setup_ui(self):
                self.setWindowTitle("🔄 測試備用監控")
                layout = QVBoxLayout()
                
                # 狀態標籤
                self.status_label = QLabel("🔄 準備就緒")
                layout.addWidget(self.status_label)
                
                # 按鈕
                button_layout = QHBoxLayout()
                self.start_btn = QPushButton("▶️ 開始監控")
                self.start_btn.clicked.connect(self.start_monitoring)
                self.stop_btn = QPushButton("⏸️ 停止監控")
                self.stop_btn.clicked.connect(self.stop_monitoring)
                self.stop_btn.setEnabled(False)
                
                button_layout.addWidget(self.start_btn)
                button_layout.addWidget(self.stop_btn)
                layout.addLayout(button_layout)
                
                # 表格
                self.table = QTableWidget()
                self.table.setColumnCount(8)
                self.table.setHorizontalHeaderLabels([
                    '股票代碼', '股票名稱', '現價', '漲跌', '漲跌幅', '成交量', '時間', '來源'
                ])
                layout.addWidget(self.table)
                
                self.setLayout(layout)
            
            def setup_timer(self):
                self.timer = QTimer()
                self.timer.timeout.connect(self.refresh_data)
            
            def start_monitoring(self):
                self.timer.start(5000)  # 5秒更新一次
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.status_label.setText("🚀 監控中...")
                self.refresh_data()
            
            def stop_monitoring(self):
                self.timer.stop()
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)
                self.status_label.setText("⏹️ 監控已停止")
            
            def refresh_data(self):
                # 模擬數據刷新
                pass
        
        # 創建測試對話框
        dialog = TestTSRTCMonitorDialog()
        print("✅ 備用監控實例創建成功")
        
        # 檢查初始狀態
        initial_timer_active = dialog.timer.isActive()
        initial_start_enabled = dialog.start_btn.isEnabled()
        initial_stop_enabled = dialog.stop_btn.isEnabled()
        
        print(f"📊 初始狀態:")
        print(f"   定時器活動: {initial_timer_active}")
        print(f"   開始按鈕啟用: {initial_start_enabled}")
        print(f"   停止按鈕啟用: {initial_stop_enabled}")
        
        # 顯示對話框
        dialog.show()
        print("✅ 備用監控窗口已顯示")
        
        # 等待自動啟動
        print("⏳ 等待自動啟動監控...")
        
        def check_auto_start():
            timer_active = dialog.timer.isActive()
            start_enabled = dialog.start_btn.isEnabled()
            stop_enabled = dialog.stop_btn.isEnabled()
            status_text = dialog.status_label.text()
            
            print(f"📊 1秒後狀態:")
            print(f"   定時器活動: {timer_active}")
            print(f"   開始按鈕啟用: {start_enabled}")
            print(f"   停止按鈕啟用: {stop_enabled}")
            print(f"   狀態文字: {status_text}")
            
            if timer_active and not start_enabled and stop_enabled and "監控中" in status_text:
                print("🎉 備用監控自動啟動成功！")
                print(f"   ✅ 定時器已啟動: {timer_active}")
                print(f"   ✅ 按鈕狀態正確切換")
                print(f"   ✅ 狀態顯示: {status_text}")
            else:
                print("❌ 備用監控自動啟動失敗")
            
            # 關閉對話框
            dialog.close()
            app.quit()
        
        # 設置檢查定時器
        QTimer.singleShot(2000, check_auto_start)  # 2秒後檢查
        
        # 運行事件循環
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ 備用監控測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    try:
        print("🧪 監控系統自動啟動功能測試")
        print("="*60)
        
        # 測試即時監控自動啟動
        realtime_ok = test_realtime_monitor_auto_start()
        
        # 測試備用監控自動啟動
        backup_ok = test_backup_monitor_auto_start()
        
        if realtime_ok and backup_ok:
            print(f"\n🎉 監控系統自動啟動功能測試通過！")
            print(f"   ✅ 即時股價監控: 啟動時自動開始監控")
            print(f"   ✅ 備用監控: 啟動時自動開始監控")
            print(f"   ✅ 用戶不需要再手動點擊「開始監控」按鈕")
        else:
            print(f"\n❌ 監控系統自動啟動功能測試失敗！")
            if not realtime_ok:
                print(f"   ❌ 即時股價監控自動啟動失敗")
            if not backup_ok:
                print(f"   ❌ 備用監控自動啟動失敗")
        
        return 0 if (realtime_ok and backup_ok) else 1
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
