#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收匯入功能
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime

def test_import_monthly_revenue():
    """測試匯入月營收數據"""
    
    print("🧪 測試月營收匯入功能")
    print("=" * 50)
    
    # CSV檔案路徑
    csv_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.csv"
    db_file = "D:/Finlab/history/tables/monthly_revenue.db"
    
    # 檢查CSV檔案是否存在
    if not os.path.exists(csv_file):
        print(f"❌ CSV檔案不存在: {csv_file}")
        return False
    
    print(f"✅ 找到CSV檔案: {csv_file}")
    
    # 讀取CSV檔案
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"📊 CSV數據: {len(df)} 筆")
        print(f"📋 欄位: {list(df.columns)}")
        
        # 顯示前3筆數據
        print(f"\n🔍 前3筆數據:")
        for i, row in df.head(3).iterrows():
            print(f"  {i+1}. {row['股票代號']} {row['股票名稱']} [{row['產業別']}]")
            print(f"     營收: {row['營收(千元)']:,} | 月增率: {row['營收月增率(%)']}%")
        
    except Exception as e:
        print(f"❌ 讀取CSV失敗: {e}")
        return False
    
    # 創建測試資料庫
    try:
        print(f"\n🗄️ 創建測試資料庫: {db_file}")
        
        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 創建表格（使用更新的結構）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monthly_revenue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_id TEXT NOT NULL,
                stock_name TEXT,
                industry TEXT,
                market TEXT,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                revenue REAL,
                revenue_mom REAL,
                revenue_yoy REAL,
                cumulative_revenue REAL,
                cumulative_revenue_yoy REAL,
                remark TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_id, year, month)
            )
        ''')
        
        # 創建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_stock_year_month ON monthly_revenue(stock_id, year, month)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_industry ON monthly_revenue(industry)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_market ON monthly_revenue(market)')
        
        print("✅ 資料庫表格創建完成")
        
        # 匯入數據
        success_count = 0
        error_count = 0
        
        print(f"\n📥 開始匯入數據...")
        
        for index, row in df.iterrows():
            try:
                # 準備數據
                stock_id = str(row['股票代號']).strip()
                stock_name = str(row['股票名稱']).strip()
                industry = str(row.get('產業別', '')).strip()
                market = str(row.get('市場別', '')).strip()
                
                # 營收數據
                revenue = parse_number(row.get('營收(千元)', 0))
                revenue_mom = parse_number(row.get('營收月增率(%)', None))
                revenue_yoy = parse_number(row.get('營收年增率(%)', None))
                cumulative_revenue = parse_number(row.get('累計營收(千元)', None))
                cumulative_revenue_yoy = parse_number(row.get('累計營收年增率(%)', None))
                remark = str(row.get('備註', '')).strip()
                
                # 插入數據
                cursor.execute('''
                    INSERT OR REPLACE INTO monthly_revenue 
                    (stock_id, stock_name, industry, market, year, month, 
                     revenue, revenue_mom, revenue_yoy, cumulative_revenue, 
                     cumulative_revenue_yoy, remark, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (stock_id, stock_name, industry, market, 2024, 6,
                      revenue, revenue_mom, revenue_yoy, cumulative_revenue, 
                      cumulative_revenue_yoy, remark))
                
                success_count += 1
                
                # 每500筆顯示進度
                if success_count % 500 == 0:
                    print(f"📊 已匯入 {success_count} 筆...")
                    
            except Exception as e:
                error_count += 1
                if error_count <= 5:  # 只顯示前5個錯誤
                    print(f"❌ 第{index+1}行匯入失敗: {str(e)}")
                continue
        
        # 提交事務
        conn.commit()
        
        print(f"\n✅ 匯入完成！")
        print(f"✅ 成功: {success_count:,} 筆")
        print(f"❌ 失敗: {error_count:,} 筆")
        
        # 驗證數據
        print(f"\n🔍 驗證匯入結果:")
        
        # 總記錄數
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
        total_count = cursor.fetchone()[0]
        print(f"  總記錄數: {total_count:,}")
        
        # 按市場統計
        cursor.execute("SELECT market, COUNT(*) FROM monthly_revenue GROUP BY market")
        market_stats = cursor.fetchall()
        print(f"  市場分布:")
        for market, count in market_stats:
            print(f"    {market}: {count:,} 家")
        
        # 按產業統計（前10名）
        cursor.execute("SELECT industry, COUNT(*) FROM monthly_revenue GROUP BY industry ORDER BY COUNT(*) DESC LIMIT 10")
        industry_stats = cursor.fetchall()
        print(f"  產業分布 (前10名):")
        for industry, count in industry_stats:
            print(f"    {industry}: {count:,} 家")
        
        # 檢查數據完整性
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue WHERE revenue IS NOT NULL")
        revenue_count = cursor.fetchone()[0]
        print(f"  有營收數據: {revenue_count:,} / {total_count:,} ({revenue_count/total_count*100:.1f}%)")
        
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue WHERE industry IS NOT NULL AND industry != ''")
        industry_count = cursor.fetchone()[0]
        print(f"  有產業資訊: {industry_count:,} / {total_count:,} ({industry_count/total_count*100:.1f}%)")
        
        # 顯示範例數據
        print(f"\n📋 範例數據:")
        cursor.execute("SELECT stock_id, stock_name, industry, market, revenue, revenue_yoy FROM monthly_revenue LIMIT 5")
        sample_data = cursor.fetchall()
        for row in sample_data:
            stock_id, stock_name, industry, market, revenue, revenue_yoy = row
            print(f"  {stock_id} {stock_name} [{industry}] [{market}] 營收:{revenue:,.0f} 年增率:{revenue_yoy}%")
        
        conn.close()
        
        print(f"\n🎉 測試完成！資料庫檔案: {db_file}")
        return True
        
    except Exception as e:
        print(f"❌ 資料庫操作失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def parse_number(value):
    """解析數字，處理各種格式"""
    if pd.isna(value) or value == '' or value == '-':
        return None
    
    try:
        # 移除逗號和百分號
        if isinstance(value, str):
            value = value.replace(',', '').replace('%', '').strip()
        
        return float(value)
    except:
        return None

if __name__ == "__main__":
    test_import_monthly_revenue()
