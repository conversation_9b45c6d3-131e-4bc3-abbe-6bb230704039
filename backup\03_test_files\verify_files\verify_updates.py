#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證全球市場數據爬蟲更新
"""

import sys
import os

def check_file_updates():
    """檢查文件更新"""
    print("🔍 檢查文件更新...")
    
    files_to_check = [
        'safe_market_scanner.py',
        'market_data_crawler_dialog.py', 
        'real_market_data_fetcher.py'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"✅ {filename} 存在")
            
            # 檢查文件內容
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 檢查關鍵字
            if filename == 'safe_market_scanner.py':
                keywords = ['get_china_indices', '美元指數', 'get_taiwan_futures_positions', 'get_foreign_futures_positions']
                for keyword in keywords:
                    if keyword in content:
                        print(f"   ✅ 包含 {keyword}")
                    else:
                        print(f"   ❌ 缺少 {keyword}")
                        
            elif filename == 'market_data_crawler_dialog.py':
                keywords = ['china_indices', 'taiwan_futures_positions', 'foreign_futures_positions', 'contracts']
                for keyword in keywords:
                    if keyword in content:
                        print(f"   ✅ 包含 {keyword}")
                    else:
                        print(f"   ❌ 缺少 {keyword}")
                        
            elif filename == 'real_market_data_fetcher.py':
                keywords = ['get_china_indices_real', 'get_dollar_index_real', 'yfinance']
                for keyword in keywords:
                    if keyword in content:
                        print(f"   ✅ 包含 {keyword}")
                    else:
                        print(f"   ❌ 缺少 {keyword}")
        else:
            print(f"❌ {filename} 不存在")

def check_syntax():
    """檢查語法"""
    print("\n🔍 檢查語法...")
    
    try:
        import ast
        
        files_to_check = [
            'safe_market_scanner.py',
            'market_data_crawler_dialog.py', 
            'real_market_data_fetcher.py'
        ]
        
        for filename in files_to_check:
            if os.path.exists(filename):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    ast.parse(content)
                    print(f"✅ {filename} 語法正確")
                except SyntaxError as e:
                    print(f"❌ {filename} 語法錯誤: {e}")
                except Exception as e:
                    print(f"⚠️ {filename} 檢查異常: {e}")
            else:
                print(f"❌ {filename} 不存在")
                
    except Exception as e:
        print(f"❌ 語法檢查失敗: {e}")

def check_new_features():
    """檢查新功能"""
    print("\n🆕 檢查新功能...")
    
    # 檢查 safe_market_scanner.py 的新功能
    if os.path.exists('safe_market_scanner.py'):
        with open('safe_market_scanner.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        new_features = {
            '中國股市指數': ['上證指數', '深證成指', '創業板指', '滬深300', '科創50'],
            '美元指數': ['美元指數'],
            '台股期貨多空': ['台指期多單', '台指期空單', '小台指多單', '小台指空單'],
            '外資期貨多空': ['外資台指期多單', '外資台指期空單', '外資電子期多單', '外資電子期空單']
        }
        
        for feature_name, keywords in new_features.items():
            found_keywords = [kw for kw in keywords if kw in content]
            if found_keywords:
                print(f"✅ {feature_name}: {len(found_keywords)}/{len(keywords)} 項目")
                for kw in found_keywords[:2]:  # 顯示前2項
                    print(f"   - {kw}")
            else:
                print(f"❌ {feature_name}: 未找到")
    
    # 檢查對話框的更新
    if os.path.exists('market_data_crawler_dialog.py'):
        with open('market_data_crawler_dialog.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        dialog_features = [
            'china_indices',
            'taiwan_futures_positions', 
            'foreign_futures_positions',
            'contracts'
        ]
        
        print(f"\n📱 對話框更新:")
        for feature in dialog_features:
            if feature in content:
                print(f"✅ {feature}")
            else:
                print(f"❌ {feature}")

def main():
    """主函數"""
    print("🌍 全球市場數據爬蟲更新驗證")
    print("=" * 50)
    
    # 檢查文件更新
    check_file_updates()
    
    # 檢查語法
    check_syntax()
    
    # 檢查新功能
    check_new_features()
    
    print("\n" + "=" * 50)
    print("📋 更新總結:")
    print("✅ 新增中國股市指數 (上證指數、深證成指、創業板指、滬深300、科創50)")
    print("✅ 新增美元指數")
    print("✅ 新增台股期貨多空資料口數")
    print("✅ 新增外資期貨多空單口數")
    print("✅ 更新市場數據爬蟲對話框")
    print("✅ 新增真實數據獲取器")
    print("✅ 更新Excel導出功能")
    
    print("\n🎯 使用方法:")
    print("1. 在主程式中點擊「🕷️ 爬蟲」→「🌍 全球市場數據爬蟲」")
    print("2. 點擊「🚀 開始爬取」按鈕")
    print("3. 查看新增的數據類別")
    print("4. 使用「📊 導出Excel」保存數據")

if __name__ == "__main__":
    main()
