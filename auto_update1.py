# 修復 ipywidgets 依賴問題
import sys
import types

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass

    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

print(f"🔍 finlab 路徑: {finlab_path}")
print(f"🔍 路徑存在: {os.path.exists(finlab_path)}")

# 現在可以安全導入 crawler 模組
from crawler import (
    # 爬蟲函數
    crawl_price,
    crawl_bargin,
    crawl_pe,
    crawl_monthly_report,
    crawl_finance_statement_by_date,
    crawl_benchmark,
    crawl_twse_divide_ratio,
    crawl_otc_divide_ratio,
    crawl_twse_cap_reduction,
    crawl_otc_cap_reduction,
    # 核心函數
    table_date_range,
    update_table,
    to_pickle,
    date_range,
    month_range,
    season_range,
    commit,
)


import datetime
from inspect import signature
import urllib3
import pandas as pd
import time
import random

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def is_trading_day(date):
    """檢查是否為交易日 (簡單版本 - 排除週末)"""
    return date.weekday() < 5  # 0-4 是週一到週五

def filter_trading_days(dates):
    """過濾出交易日"""
    return [date for date in dates if is_trading_day(date)]

def auto_update(table_name, crawl_function, time_range=None):
    """自動更新資料表 - 增強版本"""
    try:
        print(f"\n🔄 開始更新 {table_name}...")

        sig = signature(crawl_function)

        if len(sig.parameters) != 0:
            # 需要日期參數的爬蟲函數
            print(f"📅 獲取 {table_name} 表格日期範圍...")
            first_date, last_date = table_date_range(table_name)
            print(f"   表格日期範圍: {first_date} 至 {last_date}")

            if time_range and last_date:
                dates = time_range(last_date, datetime.datetime.now())
                if dates:
                    # 過濾出交易日
                    trading_dates = filter_trading_days(dates)

                    if trading_dates:
                        print(f"📈 需要更新 {len(trading_dates)} 個交易日 (原始 {len(dates)} 個日期)")
                        print(f"   更新範圍: {trading_dates[0]} 至 {trading_dates[-1]}")

                        # 使用改進的更新函數
                        improved_update_table(table_name, crawl_function, trading_dates)
                        print(f"✅ {table_name} 更新完成")
                    else:
                        print(f"✅ {table_name} 沒有需要更新的交易日")
                else:
                    print(f"✅ {table_name} 已是最新，無需更新")
            else:
                print(f"⚠️ {table_name} 無法獲取日期範圍或時間範圍函數")
        else:
            # 不需要日期參數的爬蟲函數
            print(f"🔄 執行完整爬取 {table_name}...")
            df = crawl_function()
            to_pickle(df, table_name)
            print(f"✅ {table_name} 完整爬取完成")

    except Exception as e:
        print(f"❌ 更新 {table_name} 失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")

def improved_update_table(table_name, crawl_function, dates):
    """改進的表格更新函數 - 增加錯誤處理和延遲"""

    if not dates:
        print("該時間段沒有可以爬取之資料")
        return

    print(f'start crawl {table_name} from {dates[0]} to {dates[-1]}')

    df_list = []
    success_count = 0

    for i, date in enumerate(dates):
        try:
            print(f"crawl{table_name}{date.strftime('%Y-%m-%d')}: {i+1:3d}/{len(dates):3d} [{(i+1)/len(dates)*100:5.1f}%]", end="")

            # 調用爬蟲函數
            df = crawl_function(date)

            if df is not None and not df.empty:
                df_list.append(df)
                success_count += 1
                print("  ✅")
            else:
                print("  ❌")

            # 增加延遲避免被封鎖
            if i < len(dates) - 1:  # 不是最後一個
                delay = random.uniform(5, 10)  # 5-10秒隨機延遲
                time.sleep(delay)

        except Exception as e:
            print(f"  ❌ 錯誤: {str(e)[:50]}...")
            continue

    # 合併和保存資料
    if df_list:
        combined_df = pd.concat(df_list, ignore_index=True)
        to_pickle(combined_df, table_name)
        print(f"\n✅ 成功爬取 {success_count}/{len(dates)} 個日期，共 {len(combined_df)} 筆資料")
    else:
        print(f"\n❌ 沒有成功爬取任何資料")

def main():
    """主函數 - 更新所有資料檔"""
    print("=" * 60)
    print("🚀 Finlab 自動更新系統")
    print("=" * 60)

    # 定義所有要更新的資料表
    update_tasks = [
    #    ('price', crawl_price, date_range),
    #    ('bargin_report', crawl_bargin, date_range),
    #    ('pe', crawl_pe, date_range),
    #    ('benchmark', crawl_benchmark, date_range),
    #    ('monthly_report', crawl_monthly_report, month_range),
        ('twse_divide_ratio', crawl_twse_divide_ratio, None),
        ('otc_divide_ratio', crawl_otc_divide_ratio, None),
        ('twse_cap_reduction', crawl_twse_cap_reduction, None),
        ('otc_cap_reduction', crawl_otc_cap_reduction, None),
    ]

    success_count = 0
    total_count = len(update_tasks)

    for table_name, crawl_func, time_range_func in update_tasks:
        try:
            auto_update(table_name, crawl_func, time_range_func)
            success_count += 1
        except Exception as e:
            print(f"❌ {table_name} 更新過程發生錯誤: {e}")

    print("\n" + "=" * 60)
    print("📊 更新結果總結")
    print("=" * 60)
    print(f"成功更新: {success_count}/{total_count} 個資料表")

    if success_count > 0:
        print("\n💾 提交更新...")
        try:
            commit()
            print("✅ 所有更新已提交保存")
        except Exception as e:
            print(f"❌ 提交更新失敗: {e}")

    print("\n🎉 自動更新完成！")

if __name__ == "__main__":
    main()
