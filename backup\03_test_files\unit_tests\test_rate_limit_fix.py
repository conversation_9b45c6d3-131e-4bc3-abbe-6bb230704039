#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試頻率限制修復
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_rate_limit_handling():
    """測試頻率限制處理"""
    print("🚀 測試頻率限制處理改善")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試美股指數（最容易觸發頻率限制）
        print("\n📈 測試美股指數（頻率限制處理）...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({us_time:.1f}秒):")
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
        
        # 測試商品價格
        print("\n🛢️ 測試商品價格（頻率限制處理）...")
        commodity_start = time.time()
        commodity_data = fetcher.get_commodities_real()
        commodity_time = time.time() - commodity_start
        
        if commodity_data:
            print(f"✅ 商品價格獲取成功 ({commodity_time:.1f}秒):")
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: ${price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 商品價格獲取失敗 ({commodity_time:.1f}秒)")
        
        # 測試外匯匯率
        print("\n💱 測試外匯匯率（頻率限制處理）...")
        fx_start = time.time()
        fx_data = fetcher.get_enhanced_fx_rates()
        fx_time = time.time() - fx_start
        
        if fx_data:
            print(f"✅ 外匯匯率獲取成功 ({fx_time:.1f}秒):")
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 外匯匯率獲取失敗 ({fx_time:.1f}秒)")
        
        # 測試中國股市
        print("\n🇨🇳 測試中國股市指數（頻率限制處理）...")
        china_start = time.time()
        china_data = fetcher.get_china_indices_real()
        china_time = time.time() - china_start
        
        if china_data:
            print(f"✅ 中國股市指數獲取成功 ({china_time:.1f}秒):")
            for name, data in china_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 中國股市指數獲取失敗 ({china_time:.1f}秒)")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        # 檢查頻率限制狀態
        if fetcher.rate_limit_detected:
            print("⚠️ 檢測到頻率限制，已自動調整延遲時間")
        else:
            print("✅ 未檢測到頻率限制")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner_with_rate_limit():
    """測試安全掃描器的頻率限制處理"""
    print("\n🔍 測試安全掃描器頻率限制處理...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 測試完整掃描
        print("🌍 執行完整市場掃描...")
        start_time = time.time()
        
        # 獲取所有數據
        results = {
            'us_indices': scanner.get_us_indices(),
            'commodities': scanner.get_commodities(),
            'fx_rates': scanner.get_fx_rates(),
            'china_indices': scanner.get_china_indices()
        }
        
        scan_time = time.time() - start_time
        
        # 統計結果
        total_items = 0
        successful_items = 0
        
        for category, data in results.items():
            if data:
                category_count = len(data)
                total_items += category_count
                
                # 計算成功獲取的項目
                for item_name, item_data in data.items():
                    status = item_data.get('status', '')
                    if '真實數據' in status:
                        successful_items += 1
                
                print(f"  • {category}: {category_count} 項")
        
        success_rate = (successful_items / total_items * 100) if total_items > 0 else 0
        
        print(f"\n📊 掃描結果:")
        print(f"  • 總項目: {total_items}")
        print(f"  • 成功獲取: {successful_items}")
        print(f"  • 成功率: {success_rate:.1f}%")
        print(f"  • 掃描耗時: {scan_time:.1f}秒")
        
        return success_rate > 50  # 成功率超過50%視為通過
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 頻率限制修復測試")
    print("=" * 60)
    
    tests = [
        ("頻率限制處理", test_rate_limit_handling),
        ("安全掃描器頻率限制", test_safe_scanner_with_rate_limit)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 頻率限制修復總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🔧 修復內容:")
    print("• 請求間隔：增加到2秒避免頻率限制")
    print("• 動態延遲：檢測到限制時延遲3-6秒")
    print("• 重試機制：最多重試3次，遞增等待時間")
    print("• 錯誤處理：專門處理'Too Many Requests'錯誤")
    print("• 安全請求：統一的_safe_yfinance_request方法")
    
    print("\n💡 改善策略:")
    print("• 頻率限制檢測：自動識別429錯誤和限制訊息")
    print("• 自適應延遲：根據限制狀態調整延遲時間")
    print("• 優雅降級：即時數據失敗時使用歷史數據")
    print("• 多重備援：每個指標都有多個代碼選擇")
    
    if passed > 0:
        print("\n🎉 頻率限制處理改善成功！")
        print("現在應該能更穩定地獲取市場數據。")
    else:
        print("\n⚠️ 需要進一步調整反爬蟲策略")

if __name__ == "__main__":
    main()
