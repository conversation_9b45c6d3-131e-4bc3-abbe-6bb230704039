#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新聞爬蟲功能
"""

import logging
from google_real_time_news import GoogleStockNewsCrawler

def test_crawler():
    """測試爬蟲功能"""
    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    def progress_callback(message):
        print(f"[進度] {message}")
    
    # 創建爬蟲實例
    crawler = GoogleStockNewsCrawler(progress_callback=progress_callback)
    
    # 測試爬取台積電新聞
    print("開始測試爬取台積電(2330)新聞...")
    try:
        news_list = crawler.search_stock_news("2330", days=3)  # 只爬取3天的新聞進行測試
        
        print(f"\n✅ 成功爬取 {len(news_list)} 筆新聞")
        
        # 顯示前3筆新聞的基本資訊
        for i, news in enumerate(news_list[:3]):
            print(f"\n--- 新聞 {i+1} ---")
            print(f"標題: {news.get('title', 'N/A')}")
            print(f"來源: {news.get('source', 'N/A')}")
            print(f"發布日期: {news.get('pub_date', 'N/A')}")
            print(f"連結: {news.get('news_url', 'N/A')[:80]}...")
            print(f"內容長度: {len(news.get('content', ''))}")
            
        # 測試儲存到資料庫
        if news_list:
            saved_count = crawler.save_to_database(news_list, "2330")
            print(f"\n✅ 成功儲存 {saved_count} 筆新聞到資料庫")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_crawler()
