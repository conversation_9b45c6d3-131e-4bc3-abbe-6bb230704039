#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版導出對話框測試
"""

import sys
import os
import tempfile
import json
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                            QVBoxLayout, QWidget, QTextEdit, QMessageBox)
from PyQt6.QtCore import Qt
import platform
import subprocess

class SimpleDialogTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略交集導出對話框測試")
        self.setGeometry(100, 100, 500, 300)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 說明文字
        info_text = QTextEdit()
        info_text.setPlainText(
            "🧪 策略交集導出對話框測試\n\n"
            "測試新的增強版導出對話框功能：\n"
            "• 詢問用戶是否要開啟存檔位置\n"
            "• 提供多種開啟選項\n"
            "• 處理開啟失敗的情況"
        )
        info_text.setMaximumHeight(120)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🎯 測試完整導出對話框")
        test_btn.clicked.connect(self.test_complete_dialog)
        layout.addWidget(test_btn)
        
        test2_btn = QPushButton("⚠️ 測試部分導出對話框")
        test2_btn.clicked.connect(self.test_partial_dialog)
        layout.addWidget(test2_btn)
        
        # 創建測試文件
        self.setup_test_files()
    
    def setup_test_files(self):
        """創建測試文件"""
        self.temp_dir = tempfile.mkdtemp(prefix="test_export_")
        
        # JSON文件
        self.json_file = os.path.join(self.temp_dir, "策略交集_測試_20250730.json")
        test_data = {
            "strategies": ["CANSLIM量價齊升", "藏獒"],
            "intersection_stocks": ["2330", "2317"],
            "intersection_count": 2
        }
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 報告文件
        self.report_file = os.path.join(self.temp_dir, "策略交集_測試_20250730_詳細報告.txt")
        with open(self.report_file, 'w', encoding='utf-8') as f:
            f.write("策略交集分析詳細報告\n共同股票：2330, 2317\n")
    
    def test_complete_dialog(self):
        """測試完整導出對話框"""
        self.show_enhanced_dialog(self.json_file, self.report_file, 2)
    
    def test_partial_dialog(self):
        """測試部分導出對話框"""
        self.show_simple_dialog(self.json_file, 2, "報告文件寫入失敗")
    
    def show_enhanced_dialog(self, json_filename, report_filename, stock_count):
        """增強版導出對話框"""
        # 獲取檔案資訊
        json_size = os.path.getsize(json_filename) if os.path.exists(json_filename) else 0
        report_size = os.path.getsize(report_filename) if os.path.exists(report_filename) else 0
        total_size_mb = (json_size + report_size) / (1024 * 1024)
        
        # 創建對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("導出完成")
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setText("✅ 交集結果已成功導出！")
        
        # 詳細信息
        detail_info = f"📊 數據文件：{os.path.basename(json_filename)}\n"
        detail_info += f"📝 詳細報告：{os.path.basename(report_filename)}\n"
        detail_info += f"📁 存放位置：{os.path.dirname(json_filename)}\n"
        detail_info += f"📈 共同股票數量：{stock_count} 支\n"
        detail_info += f"💾 檔案大小：{total_size_mb:.2f} MB\n\n"
        detail_info += f"❓ 是否要立即開啟存檔位置？"
        
        msg_box.setInformativeText(detail_info)
        
        # 自定義按鈕
        open_btn = msg_box.addButton("📂 開啟位置", QMessageBox.ButtonRole.YesRole)
        view_btn = msg_box.addButton("📄 查看報告", QMessageBox.ButtonRole.ActionRole)
        later_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        
        msg_box.setDefaultButton(open_btn)
        
        # 顯示對話框
        reply = msg_box.exec()
        clicked_button = msg_box.clickedButton()
        
        try:
            if clicked_button == open_btn:
                # 開啟資料夾
                folder_path = os.path.dirname(json_filename)
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":
                    subprocess.run(["open", folder_path])
                else:
                    subprocess.run(["xdg-open", folder_path])
                
                QMessageBox.information(self, "成功", f"✅ 已開啟存檔位置：{folder_path}")
                
            elif clicked_button == view_btn:
                # 開啟報告文件
                if platform.system() == "Windows":
                    os.startfile(report_filename)
                elif platform.system() == "Darwin":
                    subprocess.run(["open", report_filename])
                else:
                    subprocess.run(["xdg-open", report_filename])
                
                QMessageBox.information(self, "成功", f"✅ 已開啟詳細報告")
                
            else:
                # 稍後開啟
                QMessageBox.information(
                    self,
                    "導出完成",
                    f"📋 交集結果已成功保存\n\n"
                    f"📁 檔案位置：\n{json_filename}\n{report_filename}\n\n"
                    f"💡 您可以稍後手動開啟這些檔案"
                )
                
        except Exception as e:
            QMessageBox.warning(
                self,
                "開啟失敗",
                f"❌ 無法開啟檔案或資料夾\n\n"
                f"錯誤信息：{str(e)}\n\n"
                f"📁 請手動開啟：{os.path.dirname(json_filename)}"
            )
    
    def show_simple_dialog(self, json_filename, stock_count, error_msg):
        """簡化版導出對話框"""
        json_size = os.path.getsize(json_filename) if os.path.exists(json_filename) else 0
        json_size_mb = json_size / (1024 * 1024)
        
        # 創建對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("部分導出完成")
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setText("⚠️ 數據文件導出成功，但詳細報告導出失敗")
        
        # 詳細信息
        detail_info = f"✅ 數據文件：{os.path.basename(json_filename)}\n"
        detail_info += f"📁 存放位置：{os.path.dirname(json_filename)}\n"
        detail_info += f"📈 共同股票數量：{stock_count} 支\n"
        detail_info += f"💾 檔案大小：{json_size_mb:.2f} MB\n\n"
        detail_info += f"❌ 詳細報告導出失敗：{error_msg}\n\n"
        detail_info += f"❓ 是否要開啟數據文件位置？"
        
        msg_box.setInformativeText(detail_info)
        
        # 自定義按鈕
        open_btn = msg_box.addButton("📂 開啟位置", QMessageBox.ButtonRole.YesRole)
        later_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        
        msg_box.setDefaultButton(open_btn)
        
        # 顯示對話框
        reply = msg_box.exec()
        clicked_button = msg_box.clickedButton()
        
        try:
            if clicked_button == open_btn:
                # 開啟資料夾
                folder_path = os.path.dirname(json_filename)
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":
                    subprocess.run(["open", folder_path])
                else:
                    subprocess.run(["xdg-open", folder_path])
                
                QMessageBox.information(self, "成功", f"✅ 已開啟存檔位置：{folder_path}")
                
            else:
                # 稍後開啟
                QMessageBox.information(
                    self,
                    "導出完成",
                    f"📋 數據文件已成功保存\n\n"
                    f"📁 檔案位置：\n{json_filename}\n\n"
                    f"💡 您可以稍後手動開啟此檔案"
                )
                
        except Exception as e:
            QMessageBox.warning(
                self,
                "開啟失敗",
                f"❌ 無法開啟檔案或資料夾\n\n"
                f"錯誤信息：{str(e)}\n\n"
                f"📁 請手動開啟：{os.path.dirname(json_filename)}"
            )

def main():
    app = QApplication(sys.argv)
    window = SimpleDialogTest()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
