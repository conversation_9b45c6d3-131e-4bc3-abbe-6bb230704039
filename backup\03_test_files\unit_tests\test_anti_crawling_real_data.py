#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試反爬蟲機制的真實數據獲取
"""

import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_real_data_fetcher():
    """測試真實數據獲取器（含反爬蟲機制）"""
    print("🛡️ 測試反爬蟲機制的真實數據獲取")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試美元指數
        print("\n💵 測試美元指數...")
        dollar_data = fetcher.get_dollar_index_real()
        if dollar_data:
            for name, data in dollar_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 美元指數獲取失敗")
        
        # 測試外匯匯率
        print("\n💱 測試外匯匯率...")
        fx_data = fetcher.get_enhanced_fx_rates()
        if fx_data:
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 外匯匯率獲取失敗")
        
        # 測試商品價格
        print("\n🛢️ 測試商品價格...")
        commodity_data = fetcher.get_commodities_real()
        if commodity_data:
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: ${price} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 商品價格獲取失敗")
        
        # 測試美股指數
        print("\n📈 測試美股指數...")
        us_data = fetcher.get_us_indices_real()
        if us_data:
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 美股指數獲取失敗")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner():
    """測試安全掃描器"""
    print("\n🔍 測試安全掃描器...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 測試外匯數據
        print("💱 測試外匯數據...")
        fx_data = scanner.get_fx_rates()
        if fx_data:
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                
                if status == '❌ 無法獲取數據':
                    print(f"⚠️ {name}: {status}")
                else:
                    print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 外匯數據為空")
        
        # 測試商品數據
        print("\n🛢️ 測試商品數據...")
        commodity_data = scanner.get_commodities()
        if commodity_data:
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                
                if status == '❌ 無法獲取數據':
                    print(f"⚠️ {name}: {status}")
                else:
                    print(f"✅ {name}: ${price} ({change_pct:+.2f}%) - {status}")
        else:
            print("❌ 商品數據為空")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🛡️ 反爬蟲機制真實數據獲取測試")
    print("=" * 60)
    
    tests = [
        ("真實數據獲取器", test_real_data_fetcher),
        ("安全掃描器", test_safe_scanner)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🛡️ 反爬蟲機制特點:")
    print("• 多User-Agent輪換（8種瀏覽器標識）")
    print("• 智能請求頻率控制（最小間隔1.5秒）")
    print("• 隨機延遲機制（1-3秒隨機延遲）")
    print("• 完整HTTP標頭（模擬真實瀏覽器）")
    print("• 每5次請求更新User-Agent")
    
    print("\n💡 數據狀態說明:")
    print("• 真實數據：從yfinance成功獲取的實際市場數據")
    print("• ❌ 無法獲取數據：網路問題或數據源不可用")
    print("• 不再顯示模擬數據：確保數據真實性")
    
    if passed > 0:
        print("\n🎉 反爬蟲機制運作正常！")
        print("系統會誠實顯示數據獲取狀態，不會用假數據欺騙用戶。")
    else:
        print("\n⚠️ 需要檢查網路連接或yfinance版本")

if __name__ == "__main__":
    main()
