#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 pure_stock_id 提取問題
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_pure_stock_id():
    """調試 pure_stock_id 提取問題"""
    
    print("=" * 60)
    print("🔍 調試 pure_stock_id 提取問題")
    print("=" * 60)
    
    try:
        from crawler import get_cached_stock_info, crawl_price
        
        # 獲取股票資訊
        print("📡 獲取股票資訊...")
        stock_info = get_cached_stock_info()
        
        # 檢查 0050 在 stock_info 中的狀況
        print(f"\n🔍 檢查 0050 在 stock_info 中的狀況:")
        if '0050' in stock_info:
            info = stock_info['0050']
            print(f"   ✅ 找到 0050: {info}")
        else:
            print(f"   ❌ 未找到 0050")
        
        # 檢查資料庫中的原始資料
        print(f"\n📊 檢查資料庫中的原始資料:")
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close]
            FROM stock_daily_data 
            WHERE stock_id LIKE '%0050%'
            ORDER BY date DESC
            LIMIT 5
        ''')
        raw_data = cursor.fetchall()
        
        print(f"   資料庫中包含 0050 的記錄:")
        for stock_id, name, status, industry, date, close in raw_data:
            print(f"   {date}: '{stock_id}' | {name} | {status} | {industry}")
        
        conn.close()
        
        # 測試 extract_stock_code 函數
        print(f"\n🧪 測試 extract_stock_code 函數:")
        
        def extract_stock_code(stock_id_with_name):
            """從 '0050 元大台灣50' 格式中提取 '0050'"""
            stock_str = str(stock_id_with_name)
            # 取第一個空格之前的部分作為股票代碼
            return stock_str.split()[0] if ' ' in stock_str else stock_str
        
        test_cases = [
            '0050',
            '0050 元大台灣50',
            '0051 元大中型100',
            '1101 台泥',
            '2330 台積電'
        ]
        
        for test_case in test_cases:
            extracted = extract_stock_code(test_case)
            print(f"   '{test_case}' -> '{extracted}'")
            
            # 檢查提取的代碼是否在 stock_info 中
            if extracted in stock_info:
                info = stock_info[extracted]
                print(f"     ✅ 在 stock_info 中找到: {info}")
            else:
                print(f"     ❌ 在 stock_info 中未找到")
        
        # 嘗試實際爬取一天的資料來測試
        print(f"\n🕷️ 測試實際爬取資料:")
        try:
            test_date = datetime(2022, 9, 7)  # 使用已知有資料的日期
            print(f"   爬取日期: {test_date}")
            
            df = crawl_price(test_date)
            
            if df is not None and not df.empty:
                df_reset = df.reset_index()
                print(f"   爬取到 {len(df_reset)} 筆資料")
                
                # 檢查 0050 的資料
                etf_data = df_reset[df_reset['stock_id'] == '0050']
                if not etf_data.empty:
                    print(f"   ✅ 找到 0050 資料:")
                    for _, row in etf_data.iterrows():
                        print(f"     stock_id: '{row['stock_id']}'")
                        print(f"     stock_name: '{row.get('stock_name', 'N/A')}'")
                        print(f"     listing_status: '{row.get('listing_status', 'N/A')}'")
                        print(f"     industry: '{row.get('industry', 'N/A')}'")
                else:
                    print(f"   ❌ 未找到 0050 資料")
                    
                    # 檢查所有 ETF 資料
                    etf_data = df_reset[df_reset['stock_id'].str.startswith('00')]
                    print(f"   ETF 資料 ({len(etf_data)} 筆):")
                    for _, row in etf_data.head(5).iterrows():
                        print(f"     {row['stock_id']}: {row.get('stock_name', 'N/A')} | {row.get('listing_status', 'N/A')} | {row.get('industry', 'N/A')}")
            else:
                print(f"   ❌ 爬取失敗或無資料")
                
        except Exception as e:
            print(f"   ❌ 爬取測試失敗: {e}")
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pure_stock_id()
