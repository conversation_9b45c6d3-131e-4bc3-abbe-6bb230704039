#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試表格數據修復
"""

import sys
import pandas as pd
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

def test_table_data_fix():
    """測試表格數據修復"""
    
    print("🔧 測試表格數據修復")
    print("=" * 50)
    
    # 1. 模擬修復後的GUI邏輯
    print("1️⃣ 模擬修復後的GUI邏輯...")
    
    class MockGUIFixed:
        def __init__(self):
            sys.path.append('strategies')
            # 使用優化版本策略
            from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
            self._turtle_strategy_instance = HighYieldTurtleStrategyOptimized()
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬修復後的高殖利率烏龜策略檢查"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據"
                
                turtle_strategy = self._turtle_strategy_instance
                
                # 執行高殖利率烏龜分析
                result = turtle_strategy.analyze_stock(df, stock_id=stock_id)

                # 提取詳細信息用於表格顯示
                details = result.get('details', {})
                
                # 構建詳細的返回信息
                detailed_info = {
                    'suitable': result['suitable'],
                    'reason': result['reason'],
                    'score': result['score'],
                    'dividend_yield': details.get('dividend_yield', 0),
                    'pe_ratio': details.get('pe_ratio', 0),
                    'pb_ratio': details.get('pb_ratio', 0),
                    'close_price': details.get('close_price', 0),
                    'volume': details.get('volume', 0),
                    'data_source': details.get('data_source', 'FinLab PKL')
                }

                # 返回三個值：matched, message, detailed_info
                if result['suitable']:
                    return True, result['reason'], detailed_info
                else:
                    return False, result['reason'], detailed_info
                    
            except Exception as e:
                return False, f"策略檢查錯誤: {str(e)}", {}
        
        def check_condition(self, df, condition):
            """模擬修復後的check_condition方法"""
            try:
                condition_type = condition.get("type", "")
                
                if condition_type == "high_yield_turtle_strategy":
                    # 從條件中獲取股票代碼
                    stock_id = condition.get('stock_id')
                    
                    if stock_id is None:
                        print(f"⚠️ 高殖利率烏龜策略: 股票代碼為None")
                        return False, "股票代碼為None"
                    else:
                        print(f"✅ 高殖利率烏龜策略: 分析股票 {stock_id}")

                    # 調用策略檢查並處理詳細返回信息
                    strategy_result = self.check_high_yield_turtle_strategy(df, stock_id=stock_id)
                    
                    # 處理返回值格式
                    if len(strategy_result) == 3:
                        matched, message, detailed_info = strategy_result
                        # 將詳細信息存儲到條件中，供後續表格使用
                        condition['detailed_info'] = detailed_info
                        return matched, message
                    else:
                        # 兼容舊格式
                        return strategy_result
                else:
                    return False, "未知條件類型"
                    
            except Exception as e:
                print(f"❌ 條件檢查失敗: {e}")
                return False, f"錯誤: {str(e)}"
        
        def simulate_stock_analysis_fixed(self, df, stock_id, stock_name):
            """模擬修復後的完整股票分析流程"""
            print(f"   📊 分析股票: {stock_id} {stock_name}")
            
            # 定義策略條件
            conditions = [{"type": "high_yield_turtle_strategy"}]
            condition_results = []
            all_matched = True
            detailed_info_dict = {}  # 存儲詳細信息
            processed_conditions = []  # 存儲處理後的條件
            
            for condition in conditions:
                # 為高殖利率烏龜策略添加股票代碼
                processed_condition = condition.copy()  # 避免修改原始條件
                if condition.get("type") == "high_yield_turtle_strategy":
                    processed_condition['stock_id'] = stock_id

                matched, message = self.check_condition(df, processed_condition)
                condition_results.append({"matched": matched, "message": message})
                if not matched:
                    all_matched = False
                
                # 收集詳細信息
                if processed_condition.get("type") == "high_yield_turtle_strategy" and 'detailed_info' in processed_condition:
                    detailed_info_dict = processed_condition['detailed_info']
                
                processed_conditions.append(processed_condition)
            
            close_price = df.iloc[-1]['Close'] if 'Close' in df.columns else None
            result = {
                "股票代碼": stock_id,
                "股票名稱": stock_name,
                "收盤價": close_price,
                "條件結果": condition_results,
                "raw_data": df
            }
            
            # 為高殖利率烏龜策略添加詳細信息
            if detailed_info_dict:
                result.update({
                    "烏龜評分": detailed_info_dict.get('score', 0),
                    "殖利率": f"{detailed_info_dict.get('dividend_yield', 0):.2f}%",
                    "營收成長": "N/A",  # 暫時使用N/A
                    "營業利益率": "N/A",
                    "董監持股": "N/A",
                    "成交量": detailed_info_dict.get('volume', 0),
                    "本益比": detailed_info_dict.get('pe_ratio', 0),
                    "股價淨值比": detailed_info_dict.get('pb_ratio', 0)
                })
                print(f"      ✅ 詳細信息已添加: 評分={detailed_info_dict.get('score', 0)}, 殖利率={detailed_info_dict.get('dividend_yield', 0):.2f}%")
            else:
                print(f"      ⚠️ 詳細信息字典為空")
            
            return result, all_matched
    
    # 2. 創建測試數據
    print("2️⃣ 創建測試數據...")
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    print(f"   ✅ 測試數據準備完成")
    
    # 3. 測試修復後的流程
    print("3️⃣ 測試修復後的流程...")
    
    mock_gui = MockGUIFixed()
    
    # 測試股票列表
    test_stocks = [
        ('2881', '富邦金'),
        ('2882', '國泰金'),
        ('1108', '幸福'),
        ('2330', '台積電'),
        ('1101', '台泥')
    ]
    
    results = []
    matching_stocks = []
    
    for stock_id, stock_name in test_stocks:
        try:
            result, all_matched = mock_gui.simulate_stock_analysis_fixed(test_df, stock_id, stock_name)
            results.append(result)
            
            if all_matched:
                matching_stocks.append(stock_id)
            
        except Exception as e:
            print(f"      ❌ {stock_id}: 分析失敗 - {e}")
    
    print(f"\n   📊 分析結果:")
    print(f"      總股票數: {len(results)}")
    print(f"      符合策略: {len(matching_stocks)} 支")
    print(f"      符合股票: {matching_stocks}")
    
    # 4. 檢查結果數據結構
    print("\n4️⃣ 檢查結果數據結構...")
    
    if results:
        print(f"   📋 檢查每個結果的字段:")
        for i, result in enumerate(results):
            stock_id = result.get("股票代碼", "")
            stock_name = result.get("股票名稱", "")
            score = result.get("烏龜評分", "缺少")
            dividend = result.get("殖利率", "缺少")
            
            print(f"      {i+1}. {stock_id} {stock_name}: 評分={score}, 殖利率={dividend}")
        
        # 檢查是否包含表格需要的字段
        required_fields = ["烏龜評分", "殖利率", "營收成長", "營業利益率", "董監持股", "成交量"]
        
        all_have_fields = True
        for result in results:
            missing_fields = []
            for field in required_fields:
                if field not in result:
                    missing_fields.append(field)
            
            if missing_fields:
                stock_id = result.get("股票代碼", "")
                print(f"      ⚠️ {stock_id} 缺少字段: {missing_fields}")
                all_have_fields = False
        
        if all_have_fields:
            print(f"   ✅ 所有結果都包含必要字段")
        else:
            print(f"   ❌ 部分結果缺少必要字段")
    
    # 5. 模擬表格顯示
    print("\n5️⃣ 模擬表格顯示...")
    
    print(f"   📊 高殖利率烏龜策略結果表格:")
    print(f"   {'股票代碼':<8} {'股票名稱':<10} {'收盤價':<8} {'烏龜評分':<10} {'殖利率':<10} {'策略狀態':<10}")
    print(f"   {'-'*8} {'-'*10} {'-'*8} {'-'*10} {'-'*10} {'-'*10}")
    
    for result in results:
        stock_id = result.get("股票代碼", "")
        stock_name = result.get("股票名稱", "")
        close_price = result.get("收盤價", 0)
        score = result.get("烏龜評分", 0)
        dividend = result.get("殖利率", "N/A")
        status = "🐢 符合" if stock_id in matching_stocks else "不符合"
        
        print(f"   {stock_id:<8} {stock_name:<10} {close_price:<8.2f} {score:<10} {dividend:<10} {status:<10}")
    
    print(f"\n" + "=" * 50)
    print(f"✅ 表格數據修復測試完成！")
    
    return len(results), len(matching_stocks), all_have_fields

if __name__ == "__main__":
    total_stocks, matching_stocks, fields_complete = test_table_data_fix()
    
    print(f"\n🎉 修復測試總結:")
    print(f"✅ 成功分析 {total_stocks} 支股票")
    print(f"✅ 找到 {matching_stocks} 支符合策略的股票")
    
    if fields_complete:
        print(f"✅ 所有結果數據都包含表格顯示所需的字段")
        print(f"✅ 右側表格現在應該能正常顯示結果了")
        print(f"🚀 GUI中的高殖利率烏龜策略表格顯示問題已完全修復！")
    else:
        print(f"❌ 部分結果數據缺少必要字段，需要進一步檢查")
