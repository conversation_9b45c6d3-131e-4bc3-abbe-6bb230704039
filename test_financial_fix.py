#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財務指標修復效果
驗證：
1. 股價是否正確顯示
2. EPS計算是否正確
3. 是否移除了估值數據
"""

import sys
import os
import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_financial_info_function():
    """測試 get_real_financial_info 函數"""
    print("=" * 60)
    print("🧪 測試 get_real_financial_info 函數")
    print("=" * 60)
    
    # 導入主程序類
    sys.path.append('.')
    from O3mh_gui_v21_optimized import O3mhGUI
    
    # 創建實例
    app_instance = O3mhGUI()
    
    # 測試股票列表
    test_stocks = ['1101', '2330', '2317', '2454']
    
    for stock_code in test_stocks:
        print(f"\n📊 測試股票: {stock_code}")
        print("-" * 40)
        
        # 獲取財務資訊
        financial_info = app_instance.get_real_financial_info(stock_code)
        
        print(f"股價: {financial_info.get('股價', 'N/A')}")
        print(f"殖利率: {financial_info.get('殖利率', 'N/A')}")
        print(f"本益比: {financial_info.get('本益比', 'N/A')}")
        print(f"股價淨值比: {financial_info.get('股價淨值比', 'N/A')}")
        print(f"EPS: {financial_info.get('EPS', 'N/A')}")
        
        # 驗證EPS計算
        if (financial_info.get('股價', 'N/A') != 'N/A' and 
            financial_info.get('本益比', 'N/A') != 'N/A'):
            try:
                stock_price = float(financial_info['股價'])
                pe_ratio = float(financial_info['本益比'])
                calculated_eps = stock_price / pe_ratio
                print(f"✅ EPS驗證: {stock_price} ÷ {pe_ratio} = {calculated_eps:.2f}")
                
                if financial_info.get('EPS', 'N/A') != 'N/A':
                    reported_eps = float(financial_info['EPS'])
                    if abs(calculated_eps - reported_eps) < 0.01:
                        print("✅ EPS計算正確")
                    else:
                        print(f"❌ EPS計算錯誤: 預期 {calculated_eps:.2f}, 實際 {reported_eps:.2f}")
            except Exception as e:
                print(f"❌ EPS驗證失敗: {e}")

def test_database_connection():
    """測試資料庫連接"""
    print("\n" + "=" * 60)
    print("🔍 測試資料庫連接")
    print("=" * 60)
    
    # 測試PE資料庫
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    if os.path.exists(pe_db_path):
        try:
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            # 查詢最新日期
            cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
            latest_date = cursor.fetchone()
            print(f"✅ PE資料庫連接成功，最新日期: {latest_date[0] if latest_date else 'N/A'}")
            
            # 查詢1101的資料
            cursor.execute("""
                SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                FROM pe_data
                WHERE stock_id = '1101' AND date = ?
            """, (latest_date[0],))
            
            result = cursor.fetchone()
            if result:
                print(f"✅ 1101台泥資料: 殖利率={result[2]}, 本益比={result[3]}, 股淨比={result[4]}")
            else:
                print("❌ 找不到1101的PE資料")
                
            conn.close()
        except Exception as e:
            print(f"❌ PE資料庫連接失敗: {e}")
    else:
        print(f"❌ PE資料庫不存在: {pe_db_path}")
    
    # 測試股價資料庫
    price_db_path = 'D:/Finlab/history/tables/price.db'
    if os.path.exists(price_db_path):
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 查詢最新日期
            cursor.execute("SELECT DISTINCT date FROM stock_daily_data ORDER BY date DESC LIMIT 1")
            latest_date = cursor.fetchone()
            print(f"✅ 股價資料庫連接成功，最新日期: {latest_date[0] if latest_date else 'N/A'}")
            
            # 查詢1101的股價
            cursor.execute("""
                SELECT stock_id, stock_name, Close
                FROM stock_daily_data
                WHERE stock_id = '1101' AND date = ?
            """, (latest_date[0],))
            
            result = cursor.fetchone()
            if result:
                print(f"✅ 1101台泥股價: {result[2]}元")
            else:
                print("❌ 找不到1101的股價資料")
                
            conn.close()
        except Exception as e:
            print(f"❌ 股價資料庫連接失敗: {e}")
    else:
        print(f"❌ 股價資料庫不存在: {price_db_path}")

def test_eps_calculation():
    """測試EPS計算函數"""
    print("\n" + "=" * 60)
    print("🧮 測試EPS計算函數")
    print("=" * 60)
    
    # 導入主程序類
    sys.path.append('.')
    from O3mh_gui_v21_optimized import O3mhGUI
    
    # 創建實例
    app_instance = O3mhGUI()
    
    # 測試案例
    test_cases = [
        (24.20, 19.06, "1.27"),  # 台泥
        (40.55, 12.71, "3.19"),  # 亞泥
        (62.30, 14.62, "4.26"),  # 大成
        (0, 15.0, "N/A"),        # 股價為0
        (25.0, 0, "N/A"),        # 本益比為0
        (25.0, -5.0, "N/A"),     # 本益比為負
    ]
    
    for stock_price, pe_ratio, expected in test_cases:
        result = app_instance._calculate_eps(stock_price, pe_ratio)
        status = "✅" if result == expected else "❌"
        print(f"{status} EPS({stock_price}, {pe_ratio}) = {result} (預期: {expected})")

if __name__ == "__main__":
    print("🚀 開始測試財務指標修復效果")
    
    try:
        # 測試資料庫連接
        test_database_connection()
        
        # 測試EPS計算函數
        test_eps_calculation()
        
        # 測試財務資訊獲取函數
        test_financial_info_function()
        
        print("\n" + "=" * 60)
        print("✅ 測試完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
