#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股票資訊緩存機制
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_cache_mechanism():
    """測試緩存機制"""
    
    print("=" * 60)
    print("🧪 測試股票資訊緩存機制")
    print("=" * 60)
    
    try:
        from crawler import get_cached_stock_info
        
        # 第一次調用（應該重新獲取）
        print("📊 第一次調用 get_cached_stock_info...")
        start_time = datetime.now()
        stock_info_1 = get_cached_stock_info()
        end_time = datetime.now()
        duration_1 = (end_time - start_time).total_seconds()
        
        print(f"   獲取到 {len(stock_info_1)} 檔股票")
        print(f"   耗時: {duration_1:.1f} 秒")
        
        # 第二次調用（應該使用緩存）
        print(f"\n📊 第二次調用 get_cached_stock_info...")
        start_time = datetime.now()
        stock_info_2 = get_cached_stock_info()
        end_time = datetime.now()
        duration_2 = (end_time - start_time).total_seconds()
        
        print(f"   獲取到 {len(stock_info_2)} 檔股票")
        print(f"   耗時: {duration_2:.1f} 秒")
        
        # 比較結果
        print(f"\n📊 緩存效果分析:")
        print(f"   第一次耗時: {duration_1:.1f} 秒")
        print(f"   第二次耗時: {duration_2:.1f} 秒")
        print(f"   時間節省: {duration_1 - duration_2:.1f} 秒")
        print(f"   資料一致性: {'✅ 一致' if len(stock_info_1) == len(stock_info_2) else '❌ 不一致'}")
        
        if duration_2 < 1.0:
            print(f"   ✅ 緩存機制正常工作")
        else:
            print(f"   ⚠️ 緩存機制可能有問題")
        
        # 第三次調用（再次驗證緩存）
        print(f"\n📊 第三次調用 get_cached_stock_info...")
        start_time = datetime.now()
        stock_info_3 = get_cached_stock_info()
        end_time = datetime.now()
        duration_3 = (end_time - start_time).total_seconds()
        
        print(f"   獲取到 {len(stock_info_3)} 檔股票")
        print(f"   耗時: {duration_3:.1f} 秒")
        
        # 檢查緩存內容
        print(f"\n📋 緩存內容範例:")
        count = 0
        for stock_id, info in stock_info_3.items():
            if count < 5:
                print(f"   {stock_id}: {info['stock_name']} | {info['listing_status']} | {info['industry']}")
                count += 1
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cache_mechanism()
