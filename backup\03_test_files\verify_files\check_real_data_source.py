#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查真實的資料來源和結構
"""

import requests
from bs4 import BeautifulSoup
import re
import pandas as pd

def check_real_data_source():
    """檢查真實的資料來源"""
    
    print("🔍 檢查公開資訊觀測站真實資料結構")
    print("=" * 60)
    
    # 測試URL - 2024年6月上市公司
    url = "https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_6_0.html"
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        response.encoding = 'utf-8'
        
        print(f"📡 請求URL: {url}")
        print(f"📊 回應狀態: {response.status_code}")
        print(f"📄 內容長度: {len(response.text)} 字元")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尋找包含資料的表格
            tables = soup.find_all('table')
            print(f"📋 找到 {len(tables)} 個表格")
            
            # 檢查每個表格
            for i, table in enumerate(tables):
                rows = table.find_all('tr')
                if len(rows) > 10:  # 只檢查有足夠資料的表格
                    print(f"\n🔍 檢查表格 {i+1} (共{len(rows)}行):")
                    
                    # 檢查表頭
                    header_row = rows[0] if rows else None
                    if header_row:
                        headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
                        print(f"  📋 表頭 ({len(headers)}欄): {headers[:10]}...")  # 只顯示前10欄
                    
                    # 檢查資料行
                    data_rows = []
                    for row in rows[1:]:  # 跳過表頭
                        cells = row.find_all('td')
                        if cells:
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            # 檢查是否為股票資料行（第一欄是4位數字）
                            if len(cell_texts) > 0 and re.match(r'^\d{4}$', cell_texts[0]):
                                data_rows.append(cell_texts)
                                if len(data_rows) <= 3:  # 只顯示前3筆
                                    print(f"  📊 資料行{len(data_rows)} ({len(cell_texts)}欄): {cell_texts}")
                    
                    if data_rows:
                        print(f"  ✅ 找到 {len(data_rows)} 筆股票資料")
                        
                        # 分析第一筆完整資料
                        sample = data_rows[0]
                        print(f"\n  🔬 第一筆資料分析 (共{len(sample)}欄):")
                        for j, value in enumerate(sample):
                            print(f"    [{j}] {value}")
                        
                        # 嘗試對應到預期的欄位
                        if len(sample) >= 8:
                            print(f"\n  📋 欄位對應分析:")
                            print(f"    股票代號: {sample[0]}")
                            print(f"    股票名稱: {sample[1]}")
                            print(f"    產業別: {sample[2] if len(sample) > 2 else 'N/A'}")
                            print(f"    當月營收: {sample[3] if len(sample) > 3 else 'N/A'}")
                            print(f"    上月營收: {sample[4] if len(sample) > 4 else 'N/A'}")
                            print(f"    去年同月: {sample[5] if len(sample) > 5 else 'N/A'}")
                            print(f"    月增率: {sample[6] if len(sample) > 6 else 'N/A'}")
                            print(f"    年增率: {sample[7] if len(sample) > 7 else 'N/A'}")
                            if len(sample) > 8:
                                print(f"    累計營收: {sample[8] if len(sample) > 8 else 'N/A'}")
                            if len(sample) > 9:
                                print(f"    累計去年: {sample[9] if len(sample) > 9 else 'N/A'}")
                            if len(sample) > 10:
                                print(f"    累計增率: {sample[10] if len(sample) > 10 else 'N/A'}")
                            if len(sample) > 11:
                                print(f"    備註: {sample[11] if len(sample) > 11 else 'N/A'}")
                        
                        break  # 找到資料表格就停止
            
            # 檢查是否有其他格式的資料
            print(f"\n🔍 檢查其他可能的資料格式...")
            
            # 尋找所有包含4位數字的文字（可能是股票代號）
            all_text = soup.get_text()
            stock_codes = re.findall(r'\b\d{4}\b', all_text)
            unique_codes = list(set(stock_codes))[:10]  # 前10個不重複的
            print(f"📊 找到可能的股票代號: {unique_codes}")
            
        else:
            print(f"❌ 無法取得資料，HTTP狀態碼: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_real_data_source()
