#!/usr/bin/env python3
"""
Yahoo Finance 真實數據爬蟲
專門用於獲取真實的Yahoo Finance排行榜數據
"""

import requests
import re
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import time
import random

class YahooRealDataCrawler:
    """Yahoo Finance 真實數據爬蟲"""
    
    def __init__(self):
        self.base_url = "https://tw.stock.yahoo.com"
        self.session = requests.Session()
        self.setup_session()
        
        # 排行榜URL映射
        self.ranking_urls = {
            'volume': f"{self.base_url}/rank/volume",
            'change-up': f"{self.base_url}/rank/change-up", 
            'change-down': f"{self.base_url}/rank/change-down"
        }
        
        logging.info("✅ Yahoo真實數據爬蟲初始化完成")
    
    def setup_session(self):
        """設置session"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://tw.stock.yahoo.com/',
            'Origin': 'https://tw.stock.yahoo.com',
        }
        self.session.headers.update(headers)
        
        # 設置cookies
        self.session.cookies.update({
            'B': 'dummy_cookie_value',
            'GUC': 'dummy_guc_value'
        })
    
    def get_real_ranking_data(self, ranking_type, limit=20):
        """獲取真實的排行榜數據"""
        try:
            if ranking_type not in self.ranking_urls:
                logging.error(f"❌ 不支援的排行榜類型: {ranking_type}")
                return []
            
            url = self.ranking_urls[ranking_type]
            logging.info(f"🔍 獲取真實 {ranking_type} 排行榜數據: {url}")
            
            # 添加隨機延遲避免被封
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                # 檢查響應是否被壓縮
                content = response.text
                if len(content) < 1000 or content.startswith('\x1f\x8b'):
                    logging.warning(f"⚠️ 響應可能被壓縮或內容異常，長度: {len(content)}")
                    # 嘗試使用不同的headers重新請求
                    return self._retry_with_different_headers(url, ranking_type, limit)

                return self._parse_yahoo_ranking_page(content, ranking_type, limit)
            else:
                logging.warning(f"⚠️ HTTP狀態碼: {response.status_code}")
                return []
                
        except Exception as e:
            logging.error(f"❌ 獲取真實 {ranking_type} 排行榜失敗: {e}")
            return []

    def _retry_with_different_headers(self, url, ranking_type, limit):
        """使用不同的headers重試請求"""
        try:
            # 嘗試不同的User-Agent和headers
            alternative_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'identity',  # 不要壓縮
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(url, headers=alternative_headers, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                content = response.text
                logging.info(f"✅ 重試成功，內容長度: {len(content)}")

                # 保存調試文件
                with open(f'debug_retry_{ranking_type}.html', 'w', encoding='utf-8') as f:
                    f.write(content)

                return self._parse_yahoo_ranking_page(content, ranking_type, limit)
            else:
                logging.warning(f"⚠️ 重試失敗，HTTP狀態碼: {response.status_code}")
                return []

        except Exception as e:
            logging.error(f"❌ 重試請求失敗: {e}")
            return []
    
    def _parse_yahoo_ranking_page(self, html_content, ranking_type, limit):
        """解析Yahoo Finance排行榜頁面 - 2025年最新結構"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            ranking_data = []

            # 先保存HTML到文件以便調試
            with open(f'debug_{ranking_type}.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            logging.info(f"📝 已保存HTML到 debug_{ranking_type}.html")

            # 方法1: 尋找包含股票資料的li元素 - 更精確的選擇器
            selectors_to_try = [
                'li',  # 通用li元素
                'tr',  # 表格行
                'div[class*="rank"]',  # 包含rank的div
                'div[class*="stock"]',  # 包含stock的div
                '[data-symbol]',  # 有data-symbol屬性的元素
                'a[href*="/quote/"]'  # 股票連結
            ]

            for selector in selectors_to_try:
                elements = soup.select(selector)
                logging.info(f"🔍 使用選擇器 '{selector}' 找到 {len(elements)} 個元素")

                if len(elements) > 10:  # 如果找到足夠多的元素
                    rank = 1
                    for element in elements:
                        if rank > limit:
                            break

                        try:
                            # 尋找股票連結
                            stock_link = element.find('a', href=True) if element.name != 'a' else element
                            if not stock_link:
                                continue

                            href = stock_link.get('href', '')

                            # 從href中提取股票代碼
                            if '/quote/' not in href:
                                continue

                            stock_code_with_suffix = href.split('/quote/')[1].split('/')[0]
                            stock_code = stock_code_with_suffix.split('.')[0]

                            if not stock_code.isdigit() or len(stock_code) != 4:
                                continue

                            # 獲取股票名稱
                            stock_name = stock_link.get_text().strip()
                            if not stock_name or stock_name == stock_code:
                                stock_name = f'股票{stock_code}'

                            # 獲取父元素的所有文本
                            parent_element = element.parent if element.parent else element
                            item_text = parent_element.get_text()

                            # 解析數據
                            stock_info = self._extract_stock_data(item_text, stock_code, stock_name, ranking_type, rank)

                            if stock_info:
                                ranking_data.append(stock_info)
                                rank += 1

                        except Exception as e:
                            continue

                    if ranking_data:
                        logging.info(f"✅ 使用選擇器 '{selector}' 成功解析 {len(ranking_data)} 筆數據")
                        break

            # 方法2: 如果方法1失敗，嘗試正則表達式直接從HTML中提取
            if not ranking_data:
                ranking_data = self._extract_with_regex(html_content, ranking_type, limit)

            if ranking_data:
                logging.info(f"✅ 成功解析 {len(ranking_data)} 筆真實 {ranking_type} 排行榜數據")
            else:
                logging.warning(f"⚠️ 未能解析到 {ranking_type} 排行榜數據")

            return ranking_data

        except Exception as e:
            logging.error(f"❌ 解析Yahoo排行榜頁面失敗: {e}")
            return []

    def _extract_with_regex(self, html_content, ranking_type, limit):
        """使用正則表達式直接從HTML中提取股票數據"""
        try:
            ranking_data = []

            # 尋找所有股票連結的模式
            stock_link_pattern = r'<a[^>]*href="[^"]*\/quote\/(\d{4})\.TW[^"]*"[^>]*>([^<]+)</a>'
            matches = re.findall(stock_link_pattern, html_content)

            logging.info(f"🔍 正則表達式找到 {len(matches)} 個股票連結")

            for i, (stock_code, stock_name) in enumerate(matches[:limit]):
                try:
                    # 在股票連結附近尋找數據
                    # 構建更大的搜索區域
                    search_pattern = rf'<a[^>]*href="[^"]*\/quote\/{stock_code}\.TW[^"]*".*?</li>'
                    section_match = re.search(search_pattern, html_content, re.DOTALL)

                    if section_match:
                        section_html = section_match.group(0)
                        # 從這個區域提取數據
                        stock_info = self._extract_stock_data_from_section(section_html, stock_code, stock_name, ranking_type, i + 1)
                        if stock_info:
                            ranking_data.append(stock_info)

                except Exception as e:
                    continue

            return ranking_data

        except Exception as e:
            logging.error(f"❌ 正則表達式提取失敗: {e}")
            return []

    def _extract_stock_data_from_section(self, section_html, stock_code, stock_name, ranking_type, rank):
        """從HTML區段中提取股票數據"""
        try:
            # 移除HTML標籤，只保留文本
            text = re.sub(r'<[^>]+>', ' ', section_html)
            text = text.replace('&nbsp;', ' ').replace('\n', ' ').replace('\t', ' ')

            # 清理股票名稱
            stock_name = stock_name.strip()
            if not stock_name or stock_name == stock_code:
                stock_name = f'股票{stock_code}'

            return self._extract_stock_data(text, stock_code, stock_name, ranking_type, rank)

        except Exception as e:
            logging.debug(f"從區段提取數據失敗 {stock_code}: {e}")
            return None

    def _extract_stock_data(self, text, stock_code, stock_name, ranking_type, rank):
        """從文本中提取股票數據"""
        try:
            # 清理文本
            text = text.replace(',', '').replace('\n', ' ').replace('\t', ' ')
            
            # 提取價格 (第一個小數點數字)
            price_pattern = r'(\d+\.?\d*)'
            price_matches = re.findall(price_pattern, text)
            current_price = float(price_matches[0]) if price_matches else 0
            
            # 提取漲跌幅 (包含%符號)
            percent_pattern = r'([+-]?\d+\.?\d*)%'
            percent_match = re.search(percent_pattern, text)
            change_percent = float(percent_match.group(1)) if percent_match else 0
            
            # 提取漲跌金額 (帶正負號的數字)
            change_pattern = r'([+-]\d+\.?\d*)'
            change_match = re.search(change_pattern, text)
            change_amount = float(change_match.group(1)) if change_match else 0
            
            # 提取成交量 (最大的整數，通常在後面)
            volume_pattern = r'(\d{1,6})'
            volume_matches = re.findall(volume_pattern, text)
            volume = max(volume_matches, key=lambda x: int(x)) if volume_matches else '0'
            
            # 如果成交量太小，可能是錯誤的，設為0
            if int(volume) < 100:
                volume = '0'
            
            stock_info = {
                'rank': rank,
                'stock_code': stock_code,
                'stock_name': stock_name,
                'current_price': current_price,
                'change_amount': change_amount,
                'change_percent': change_percent,
                'volume': volume,
                'ranking_type': ranking_type,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'source': 'yahoo_real_data'
            }
            
            return stock_info
            
        except Exception as e:
            logging.debug(f"提取股票數據失敗 {stock_code}: {e}")
            return None
    
    def _parse_table_structure(self, soup, ranking_type, limit):
        """解析表格結構 - 備用方法"""
        try:
            ranking_data = []
            
            # 尋找表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                if len(rows) < 5:  # 太少行，可能不是排行榜
                    continue
                
                rank = 1
                for row in rows[1:]:  # 跳過標題行
                    if rank > limit:
                        break
                        
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 6:
                        continue
                    
                    try:
                        # 嘗試從表格中提取數據
                        cell_texts = [cell.get_text().strip() for cell in cells]
                        
                        # 尋找股票代碼
                        stock_code = None
                        for text in cell_texts:
                            if text.isdigit() and len(text) == 4:
                                stock_code = text
                                break
                        
                        if not stock_code:
                            continue
                        
                        # 基本股票資訊
                        stock_info = {
                            'rank': rank,
                            'stock_code': stock_code,
                            'stock_name': cell_texts[1] if len(cell_texts) > 1 else f'股票{stock_code}',
                            'current_price': self._safe_float(cell_texts[2]) if len(cell_texts) > 2 else 0,
                            'change_amount': self._safe_float(cell_texts[3]) if len(cell_texts) > 3 else 0,
                            'change_percent': self._safe_float(cell_texts[4].replace('%', '')) if len(cell_texts) > 4 else 0,
                            'volume': cell_texts[5] if len(cell_texts) > 5 else '0',
                            'ranking_type': ranking_type,
                            'timestamp': datetime.now().strftime('%H:%M:%S'),
                            'source': 'yahoo_table_parse'
                        }
                        
                        ranking_data.append(stock_info)
                        rank += 1
                        
                    except Exception as e:
                        continue
                
                if ranking_data:
                    break
            
            return ranking_data
            
        except Exception as e:
            logging.error(f"❌ 表格結構解析失敗: {e}")
            return []
    
    def _safe_float(self, value):
        """安全轉換為浮點數"""
        try:
            if value is None or value == '' or value == '-':
                return 0.0
            # 移除逗號和百分號
            clean_value = str(value).replace(',', '').replace('%', '').strip()
            return float(clean_value)
        except (ValueError, TypeError):
            return 0.0

def test_yahoo_real_data():
    """測試Yahoo真實數據爬蟲"""
    print("🧪 測試Yahoo真實數據爬蟲")
    print("=" * 50)
    
    crawler = YahooRealDataCrawler()
    
    # 測試三種排行榜
    ranking_types = [
        ('volume', '成交量排行'),
        ('change-up', '漲幅排行'),
        ('change-down', '跌幅排行')
    ]
    
    for ranking_type, chinese_name in ranking_types:
        print(f"\n📊 測試 {chinese_name} ({ranking_type})...")
        
        data = crawler.get_real_ranking_data(ranking_type, limit=5)
        
        if data:
            print(f"✅ 成功獲取 {len(data)} 筆真實數據")
            
            # 顯示前3筆數據
            for i, stock in enumerate(data[:3]):
                rank = stock.get('rank', i+1)
                code = stock.get('stock_code', 'N/A')
                name = stock.get('stock_name', 'N/A')
                price = stock.get('current_price', 0)
                change_pct = stock.get('change_percent', 0)
                volume = stock.get('volume', '0')
                source = stock.get('source', 'unknown')
                
                print(f"   {rank}. {code} {name}")
                print(f"      價格: {price:.2f}, 漲跌幅: {change_pct:+.2f}%, 成交量: {volume}")
                print(f"      數據源: {source}")
        else:
            print(f"❌ 無法獲取 {chinese_name} 真實數據")

if __name__ == '__main__':
    test_yahoo_real_data()
