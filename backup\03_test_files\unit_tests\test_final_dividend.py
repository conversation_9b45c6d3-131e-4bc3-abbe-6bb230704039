#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終測試除權息資料下載功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

def test_final_dividend():
    """最終測試除權息功能"""
    print("🚀 最終測試除權息資料下載功能")
    print("=" * 60)
    
    try:
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建主視窗實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 測試真實除權息資料獲取
        print("\n🔍 測試1: 真實除權息資料獲取")
        real_data = gui.fetch_real_dividend_data()
        
        if real_data:
            print(f"✅ 成功獲取真實除權息資料: {len(real_data)} 筆")
            
            # 顯示前5筆真實資料
            print("📋 前5筆真實除權息資料:")
            for i, data in enumerate(real_data[:5]):
                stock_code = data.get('股票代碼', 'N/A')
                stock_name = data.get('股票名稱', 'N/A')
                ex_date = data.get('除權息日', 'N/A')
                cash_div = data.get('現金股利', 'N/A')
                stock_div = data.get('股票股利', 'N/A')
                remark = data.get('備註', 'N/A')
                print(f"  {i+1}. {stock_code} {stock_name} | 除權息日:{ex_date} | 現金:{cash_div} 股票:{stock_div} | {remark}")
            
            # 檢查資料品質
            real_names = 0
            for data in real_data[:20]:
                stock_name = data.get('股票名稱', '')
                if not any(keyword in stock_name for keyword in ['股票', '示例', '模擬']):
                    real_names += 1
            
            print(f"📊 前20筆中真實股票名稱: {real_names}/20")
            
            # 保存真實資料
            import csv
            filename = f"real_dividend_test_{len(real_data)}_records.csv"
            with open(filename, 'w', newline='', encoding='utf-8-sig') as file:
                if real_data:
                    fieldnames = real_data[0].keys()
                    writer = csv.DictWriter(file, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(real_data)
            
            print(f"💾 真實資料已保存至: {filename}")
            
        else:
            print("❌ 未獲取到真實除權息資料")
        
        # 測試完整除權息下載流程
        print("\n🔍 測試2: 完整除權息下載流程")
        
        # 模擬完整下載流程（不顯示進度對話框）
        try:
            # 獲取用戶股票清單
            user_stocks = gui.get_user_stock_list()
            print(f"📊 用戶股票清單: {len(user_stocks) if user_stocks else 0} 支")
            
            # 嘗試真實資料
            dividend_data = []
            if real_data:
                dividend_data = real_data
                print(f"✅ 使用真實資料: {len(dividend_data)} 筆")
            else:
                # 使用示例資料
                dividend_data = gui.generate_comprehensive_sample_data()
                print(f"⚠️ 使用示例資料: {len(dividend_data)} 筆")
            
            # 保存最終資料
            final_filename = "final_dividend_data.csv"
            with open(final_filename, 'w', newline='', encoding='utf-8-sig') as file:
                if dividend_data:
                    fieldnames = dividend_data[0].keys()
                    writer = csv.DictWriter(file, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(dividend_data)
            
            print(f"💾 最終資料已保存至: {final_filename}")
            
            # 檢查資料來源
            data_sources = {}
            for data in dividend_data:
                source = data.get('備註', '未知')
                data_sources[source] = data_sources.get(source, 0) + 1
            
            print("📡 資料來源分布:")
            for source, count in data_sources.items():
                print(f"  - {source}: {count} 筆")
            
        except Exception as e:
            print(f"❌ 完整流程測試失敗: {e}")
        
        print("\n" + "=" * 60)
        print("🎯 測試結果總結:")
        
        if real_data:
            print("✅ 真實除權息資料獲取: 成功")
            print(f"📊 真實資料筆數: {len(real_data)}")
            print("🏷️ 資料來源: 櫃買中心API")
            print("💡 建議: 程式已可獲取真實除權息資料！")
        else:
            print("❌ 真實除權息資料獲取: 失敗")
            print("⚠️ 將使用示例資料作為備用方案")
            print("💡 建議: 檢查網路連線或API狀態")
        
        return real_data is not None and len(real_data) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 除權息資料下載功能最終測試")
    print(f"⏰ 測試時間: {os.popen('date /t').read().strip()}")
    
    success = test_final_dividend()
    
    if success:
        print("\n🎉 測試成功！程式可以獲取真實除權息資料！")
        print("💡 您現在可以使用主程式的「資料下載」→「除權息」功能")
    else:
        print("\n⚠️ 真實資料獲取失敗，但程式仍可使用示例資料")
        print("💡 建議檢查網路連線後重試")
    
    print("\n按 Enter 鍵結束...")
    input()
