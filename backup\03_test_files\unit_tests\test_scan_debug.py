#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修改後的掃描調試信息
"""

import sys
import time
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_scan_worker():
    """測試 ScanWorker 的調試信息"""
    print("🔍 測試 ScanWorker 調試信息")
    print("=" * 80)
    
    try:
        # 導入必要的模組
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        # 定義回調函數
        def success_callback(results):
            print(f"🎉 SUCCESS CALLBACK 被調用!")
            print(f"   結果類型: {type(results)}")
            if isinstance(results, dict):
                print(f"   結果鍵: {list(results.keys())}")
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"   總數據項: {total_items}")
        
        def error_callback(error_msg):
            print(f"❌ ERROR CALLBACK 被調用!")
            print(f"   錯誤信息: {error_msg}")
        
        # 創建 ScanWorker
        print("📊 創建 ScanWorker...")
        scan_worker = ScanWorker(
            monitor,
            callback_success=success_callback,
            callback_error=error_callback
        )
        
        print("🚀 啟動掃描...")
        scan_worker.start_scan()
        
        # 等待掃描完成
        print("⏳ 等待掃描完成...")
        time.sleep(10)  # 等待10秒
        
        print("✅ 測試完成")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_direct_scan():
    """直接測試掃描功能"""
    print(f"\n🔍 直接測試掃描功能")
    print("=" * 80)
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        print("📊 執行直接掃描...")
        results = monitor.run_full_scan()
        
        print(f"📋 掃描結果分析:")
        print(f"   類型: {type(results)}")
        print(f"   布爾值: {bool(results)}")
        print(f"   是否為 None: {results is None}")
        
        if isinstance(results, dict):
            print(f"   鍵: {list(results.keys())}")
            print(f"   長度: {len(results)}")
            
            # 檢查實際數據
            total_items = 0
            for key, value in results.items():
                if isinstance(value, dict) and value:
                    item_count = len(value)
                    total_items += item_count
                    print(f"   {key}: {item_count} 項")
                elif isinstance(value, list) and value:
                    item_count = len(value)
                    total_items += item_count
                    print(f"   {key}: {item_count} 項")
                else:
                    print(f"   {key}: 空或非字典/列表")
            
            print(f"   總數據項: {total_items}")
            
            # 模擬 ScanWorker 的判斷邏輯
            print(f"\n🔍 模擬 ScanWorker 判斷:")
            print(f"   if results: {bool(results)}")
            print(f"   if results is not None: {results is not None}")
            
            if results:
                print("   ✅ 原始判斷: 會調用成功回調")
            else:
                print("   ❌ 原始判斷: 會調用錯誤回調")
            
            if results is not None:
                print("   ✅ 修改後判斷: 會調用成功回調")
            else:
                print("   ❌ 修改後判斷: 會調用錯誤回調")
        
        return results
        
    except Exception as e:
        print(f"❌ 直接掃描失敗: {e}")
        return None

def main():
    """主測試函數"""
    print("🧪 掃描調試信息測試")
    print("=" * 90)
    print(f"📅 測試時間: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 直接測試掃描
    results = test_direct_scan()
    
    # 2. 測試 ScanWorker
    if results is not None:
        print(f"\n" + "="*80)
        test_scan_worker()
    else:
        print("\n❌ 直接掃描失敗，跳過 ScanWorker 測試")
    
    print(f"\n🎯 測試總結")
    print("=" * 90)
    
    if results is not None:
        print("✅ 掃描功能正常")
        print("💡 如果界面仍然沒有顯示，問題可能在於:")
        print("   1. 回調函數沒有被正確調用")
        print("   2. 界面組件更新有問題")
        print("   3. 線程安全問題")
        print("   4. 異常被捕獲但沒有記錄")
        
        print("\n🔧 建議檢查:")
        print("   • 查看控制台是否有 'SUCCESS CALLBACK 被調用!' 信息")
        print("   • 查看是否有 'DEBUG: 執行成功回調' 信息")
        print("   • 檢查界面組件是否正確更新")
    else:
        print("❌ 掃描功能有問題")
        print("🔧 需要先修復掃描功能")
    
    print("=" * 90)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
    
    print(f"\n👋 測試結束")
