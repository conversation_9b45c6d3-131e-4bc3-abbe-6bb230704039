#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試左側面板股票列表的右鍵選單功能
"""

import sys
import os
import logging
from datetime import datetime, date

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_left_panel_context_menu():
    """測試左側面板右鍵選單功能"""
    print("=" * 60)
    print("🖱️ 測試左側面板股票列表右鍵選單功能")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QTableWidgetItem, QListWidgetItem
        from PyQt6.QtCore import QPoint
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 1. 首先設置右側表格為月營收排行榜
        print("\n📊 設置右側表格為月營收排行榜...")
        gui.result_table.setColumnCount(14)
        gui.result_table.setHorizontalHeaderLabels([
            "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
            "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
            "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
        ])
        
        # 填入測試資料到右側表格
        gui.result_table.setRowCount(3)
        test_data = [
            ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
             "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
            ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
             "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"],
            ["3", "2454", "聯發科", "202507", "85,000,000", "80,000,000", "75,000,000", 
             "+13.33%", "*****%", "2.80", "18.20", "3.20", "46.70", "82.1"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                gui.result_table.setItem(row, col, item)
        
        print("✅ 右側表格設置完成")
        
        # 2. 設置左側股票列表
        print("\n📋 設置左側股票列表...")
        
        # 清空並添加測試股票到全部股票列表
        gui.all_stocks_list.clear()
        test_stocks = [
            "2330 台積電",
            "2317 鴻海",
            "2454 聯發科",
            "2412 中華電",
            "1301 台塑"
        ]
        
        for stock_text in test_stocks:
            item = QListWidgetItem(stock_text)
            gui.all_stocks_list.addItem(item)
        
        # 也添加到選股結果列表
        gui.filtered_stocks_list.clear()
        for stock_text in test_stocks[:3]:  # 只添加前3個
            item = QListWidgetItem(stock_text)
            gui.filtered_stocks_list.addItem(item)
        
        print("✅ 左側股票列表設置完成")
        
        # 3. 測試月營收排行榜檢測
        print("\n🔍 測試月營收排行榜檢測...")
        is_monthly = gui.is_monthly_revenue_ranking()
        print(f"檢測結果: {'✅ 是月營收排行榜' if is_monthly else '❌ 不是月營收排行榜'}")
        
        # 4. 測試股票搜尋功能
        print("\n🔍 測試股票搜尋功能...")
        test_codes = ["2330", "2317", "2454", "9999"]
        
        for code in test_codes:
            row = gui.find_stock_in_result_table(code)
            if row is not None:
                print(f"  ✅ 找到股票 {code} 在第 {row} 行")
            else:
                print(f"  ❌ 未找到股票 {code}")
        
        # 5. 測試左側列表右鍵選單
        print("\n🖱️ 測試左側列表右鍵選單...")
        
        # 模擬右鍵點擊全部股票列表中的第一個項目
        position = QPoint(50, 20)
        
        print("  測試全部股票列表右鍵選單...")
        try:
            # 設置sender為all_stocks_list
            gui.all_stocks_list.customContextMenuRequested.emit(position)
            print("  ✅ 全部股票列表右鍵選單信號發送成功")
        except Exception as e:
            print(f"  ❌ 全部股票列表右鍵選單測試失敗: {e}")
        
        print("  測試選股結果列表右鍵選單...")
        try:
            # 設置sender為filtered_stocks_list
            gui.filtered_stocks_list.customContextMenuRequested.emit(position)
            print("  ✅ 選股結果列表右鍵選單信號發送成功")
        except Exception as e:
            print(f"  ❌ 選股結果列表右鍵選單測試失敗: {e}")
        
        # 6. 手動測試股票代碼解析
        print("\n🔍 測試股票代碼解析...")
        test_texts = [
            "2330 台積電",
            "2317 鴻海",
            "2454 聯發科",
            "1. 2330 台積電 營收:120,000,000 YoY:+20.00%",
            "無效格式"
        ]
        
        for text in test_texts:
            try:
                code = gui.extract_stock_code(text)
                name = gui.extract_stock_name(text, code) if code else "N/A"
                print(f"  '{text}' -> 代碼: {code}, 名稱: {name}")
            except Exception as e:
                print(f"  '{text}' -> 解析失敗: {e}")
        
        # 7. 顯示GUI進行手動測試
        print(f"\n🖥️ 顯示GUI進行手動測試...")
        print("請按照以下步驟測試:")
        print("1. 執行月營收排行榜查詢")
        print("2. 在左側「全部股票」或「選股結果」列表中右鍵點擊股票")
        print("3. 檢查是否出現「月營收綜合評估」選項")
        print("4. 點擊該選項查看是否正常顯示評估對話框")
        
        gui.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_list_test():
    """創建簡單的列表右鍵選單測試"""
    print("\n" + "=" * 60)
    print("🧪 創建簡單的列表右鍵選單測試")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QHBoxLayout, 
                                   QVBoxLayout, QWidget, QListWidget, 
                                   QListWidgetItem, QTableWidget, QTableWidgetItem,
                                   QMenu, QLabel)
        from PyQt6.QtCore import Qt
        
        class SimpleListTestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("簡單列表右鍵選單測試")
                self.setGeometry(100, 100, 1200, 400)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                main_layout = QHBoxLayout(central_widget)
                
                # 左側面板
                left_panel = QWidget()
                left_layout = QVBoxLayout(left_panel)
                
                # 說明標籤
                label = QLabel("左側股票列表 - 請右鍵點擊股票")
                left_layout.addWidget(label)
                
                # 創建股票列表
                self.stock_list = QListWidget()
                self.stock_list.setMinimumWidth(200)
                
                # 添加測試股票
                test_stocks = [
                    "2330 台積電",
                    "2317 鴻海", 
                    "2454 聯發科",
                    "2412 中華電",
                    "1301 台塑"
                ]
                
                for stock_text in test_stocks:
                    item = QListWidgetItem(stock_text)
                    self.stock_list.addItem(item)
                
                # 設置右鍵選單
                self.stock_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.stock_list.customContextMenuRequested.connect(self.show_list_context_menu)
                
                left_layout.addWidget(self.stock_list)
                main_layout.addWidget(left_panel)
                
                # 右側表格（模擬月營收排行榜）
                right_panel = QWidget()
                right_layout = QVBoxLayout(right_panel)
                
                table_label = QLabel("右側月營收排行榜表格")
                right_layout.addWidget(table_label)
                
                self.result_table = QTableWidget()
                self.result_table.setColumnCount(14)
                self.result_table.setRowCount(3)
                self.result_table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                    "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                    "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
                ])
                
                # 填入測試資料
                test_data = [
                    ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
                     "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
                    ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
                     "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"],
                    ["3", "2454", "聯發科", "202507", "85,000,000", "80,000,000", "75,000,000", 
                     "+13.33%", "*****%", "2.80", "18.20", "3.20", "46.70", "82.1"]
                ]
                
                for row, row_data in enumerate(test_data):
                    for col, cell_data in enumerate(row_data):
                        item = QTableWidgetItem(str(cell_data))
                        self.result_table.setItem(row, col, item)
                
                right_layout.addWidget(self.result_table)
                main_layout.addWidget(right_panel)
                
                print("✅ 簡單列表測試視窗創建完成")
            
            def show_list_context_menu(self, position):
                """顯示列表右鍵選單"""
                print(f"🖱️ 列表右鍵點擊位置: {position}")
                
                # 獲取點擊的項目
                item = self.stock_list.itemAt(position)
                if not item:
                    print("❌ 沒有點擊到有效項目")
                    return
                
                item_text = item.text().strip()
                print(f"📈 點擊項目: {item_text}")
                
                # 解析股票代碼和名稱
                parts = item_text.split(' ', 1)
                if len(parts) >= 2:
                    stock_code = parts[0]
                    stock_name = parts[1]
                else:
                    stock_code = parts[0]
                    stock_name = "未知"
                
                print(f"📊 解析結果: {stock_code} {stock_name}")
                
                # 檢查是否在表格中找到對應股票
                stock_row = self.find_stock_in_table(stock_code)
                
                # 創建右鍵選單
                context_menu = QMenu(self)
                context_menu.setStyleSheet("""
                    QMenu {
                        background-color: #2b2b2b;
                        color: white;
                        border: 1px solid #555;
                        border-radius: 5px;
                        padding: 5px;
                    }
                    QMenu::item {
                        padding: 8px 20px;
                        border-radius: 3px;
                    }
                    QMenu::item:selected {
                        background-color: #4CAF50;
                    }
                    QMenu::separator {
                        height: 1px;
                        background-color: #555;
                        margin: 5px 0px;
                    }
                """)
                
                # 如果在表格中找到對應股票，添加月營收綜合評估選項
                if stock_row is not None:
                    assessment_action = context_menu.addAction(f"📊 {stock_code} {stock_name} 月營收綜合評估")
                    assessment_action.triggered.connect(lambda: self.show_assessment(stock_code, stock_name, stock_row))
                    context_menu.addSeparator()
                
                # 添加其他選項
                news_action = context_menu.addAction(f"📰 爬取 {stock_code} {stock_name} 新聞")
                news_action.triggered.connect(lambda: print(f"📰 爬取 {stock_code} {stock_name} 新聞"))
                
                chart_action = context_menu.addAction(f"📈 查看 {stock_code} K線圖")
                chart_action.triggered.connect(lambda: print(f"📈 查看 {stock_code} K線圖"))
                
                info_action = context_menu.addAction(f"ℹ️ 查看 {stock_code} 基本資料")
                info_action.triggered.connect(lambda: print(f"ℹ️ 查看 {stock_code} 基本資料"))
                
                context_menu.addSeparator()
                
                monitor_action = context_menu.addAction(f"📊 加入監控清單")
                monitor_action.triggered.connect(lambda: print(f"📊 加入 {stock_code} 到監控清單"))
                
                # 顯示選單
                context_menu.exec(self.stock_list.mapToGlobal(position))
                print("✅ 列表右鍵選單顯示完成")
            
            def find_stock_in_table(self, stock_code):
                """在表格中尋找股票"""
                for row in range(self.result_table.rowCount()):
                    item = self.result_table.item(row, 1)  # 股票代碼在第1欄
                    if item and item.text().strip() == stock_code:
                        print(f"✅ 在表格第{row}行找到股票 {stock_code}")
                        return row
                print(f"❌ 在表格中未找到股票 {stock_code}")
                return None
            
            def show_assessment(self, stock_code, stock_name, row):
                """顯示評估"""
                print(f"🎯 顯示 {stock_code} {stock_name} 的月營收綜合評估（第{row}行）")
                
                # 獲取該行的所有資料
                data = {}
                headers = ["排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                          "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                          "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"]
                
                for col, header in enumerate(headers):
                    item = self.result_table.item(row, col)
                    data[header] = item.text() if item else "N/A"
                
                print("📊 股票資料:")
                for key, value in data.items():
                    print(f"   {key}: {value}")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建測試視窗
        test_window = SimpleListTestWindow()
        test_window.show()
        
        print("🖥️ 簡單列表測試視窗已顯示")
        print("請在左側股票列表中右鍵點擊股票，檢查是否有「月營收綜合評估」選項")
        
        return True
        
    except Exception as e:
        print(f"❌ 簡單列表測試創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始測試左側面板股票列表右鍵選單功能...")
    
    # 主要測試
    success1 = test_left_panel_context_menu()
    
    # 簡單測試
    success2 = create_simple_list_test()
    
    if success1 and success2:
        print("\n🎉 測試完成！")
        print("\n📋 如果左側列表右鍵選單仍未正常顯示，請檢查:")
        print("  1. 是否正確執行了月營收排行榜查詢")
        print("  2. 右側表格是否有對應的股票資料")
        print("  3. 左側列表中的股票格式是否正確")
        print("  4. 右鍵點擊是否在有效的列表項目上")
    else:
        print("\n❌ 測試過程中發現問題。")
