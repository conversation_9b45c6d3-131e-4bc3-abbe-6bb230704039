#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試右鍵選單和財務指標修復效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_info():
    """測試財務指標獲取"""
    print("💎 測試財務指標獲取...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 測試財務指標獲取
        test_stocks = ['2330', '2317', '1101', '8021']
        
        print("測試股票財務指標:")
        for stock_code in test_stocks:
            print(f"\n📊 {stock_code}:")
            
            # 測試估算財務資訊
            estimated_info = gui.get_estimated_financial_info(stock_code)
            for key, value in estimated_info.items():
                print(f"  {key}: {value}")
            
            # 測試基本財務資訊
            basic_info = gui.get_basic_financial_info(stock_code)
            if basic_info:
                print(f"  基本資訊: {basic_info}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 財務指標測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_performance_test():
    """創建性能測試"""
    print("\n⚡ 創建右鍵選單性能測試...")
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, 
                                   QTableWidgetItem, QMenu, QMessageBox, QVBoxLayout, QWidget)
        from PyQt6.QtCore import Qt, QTimer
        import time
        
        class PerformanceTest(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("🚀 右鍵選單性能測試")
                self.setGeometry(200, 200, 800, 600)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # 創建表格
                self.table = QTableWidget(10, 6)
                self.table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "當月營收", "YoY%", "MoM%"
                ])
                
                # 添加測試資料（模擬月營收排行榜）
                test_data = [
                    ("1", "2330", "台積電", "263,708,978", "+26.86%", "-17.72%"),
                    ("2", "2317", "鴻海", "120,000,000", "+15.00%", "+5.00%"),
                    ("3", "2454", "聯發科", "85,000,000", "+20.50%", "+8.30%"),
                    ("4", "1101", "台泥", "10,107,877", "-26.00%", "-19.90%"),
                    ("5", "8021", "尖點", "25,000", "+80.00%", "+50.00%"),
                    ("6", "6505", "台塑化", "45,000,000", "+12.30%", "-3.40%"),
                    ("7", "2881", "富邦金", "35,000,000", "+8.50%", "+2.10%"),
                    ("8", "2882", "國泰金", "32,000,000", "+6.20%", "-1.50%"),
                    ("9", "2303", "聯電", "28,000,000", "+18.70%", "+12.30%"),
                    ("10", "2002", "中鋼", "22,000,000", "-5.20%", "-8.90%")
                ]
                
                for row, (rank, code, name, revenue, yoy, mom) in enumerate(test_data):
                    self.table.setItem(row, 0, QTableWidgetItem(rank))
                    self.table.setItem(row, 1, QTableWidgetItem(code))
                    self.table.setItem(row, 2, QTableWidgetItem(name))
                    self.table.setItem(row, 3, QTableWidgetItem(revenue))
                    self.table.setItem(row, 4, QTableWidgetItem(yoy))
                    self.table.setItem(row, 5, QTableWidgetItem(mom))
                
                # 設置右鍵選單
                self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.table.customContextMenuRequested.connect(self.show_context_menu)
                
                layout.addWidget(self.table)
                
                # 性能計時器
                self.last_click_time = 0
                
                print("✅ 性能測試GUI創建成功")
            
            def show_context_menu(self, position):
                """顯示右鍵選單（性能優化版本）"""
                start_time = time.time()
                
                try:
                    print(f"🖱️ 右鍵選單觸發: {position}")
                    
                    # 快速獲取項目
                    item = self.table.itemAt(position)
                    if not item:
                        print("❌ 沒有點擊到有效項目")
                        return
                    
                    row = item.row()
                    
                    # 快速獲取股票資訊
                    rank = self.table.item(row, 0).text()
                    stock_code = self.table.item(row, 1).text()
                    stock_name = self.table.item(row, 2).text()
                    yoy = self.table.item(row, 4).text()
                    mom = self.table.item(row, 5).text()
                    
                    print(f"📊 選中股票: 第{rank}名 {stock_code} {stock_name}")
                    
                    # 創建選單（優化版本）
                    menu = QMenu(self)
                    menu.setStyleSheet("""
                        QMenu {
                            background-color: #2b2b2b;
                            color: white;
                            border: 1px solid #555;
                            border-radius: 5px;
                            padding: 5px;
                        }
                        QMenu::item {
                            padding: 8px 16px;
                            border-radius: 3px;
                        }
                        QMenu::item:selected {
                            background-color: #3daee9;
                        }
                    """)
                    
                    # 添加選項
                    assessment_action = menu.addAction(f"📊 {stock_code} {stock_name} 月營收綜合評估")
                    assessment_action.triggered.connect(
                        lambda: self.show_assessment(stock_code, stock_name, rank, yoy, mom)
                    )
                    
                    ranking_action = menu.addAction(f"🏆 查看YoY+MoM綜合排名")
                    ranking_action.triggered.connect(
                        lambda: self.show_ranking(stock_code, stock_name, yoy, mom)
                    )
                    
                    news_action = menu.addAction(f"📰 {stock_code} 股票新聞")
                    news_action.triggered.connect(
                        lambda: self.show_news(stock_code, stock_name)
                    )
                    
                    monitor_action = menu.addAction(f"📊 加入監控清單")
                    monitor_action.triggered.connect(
                        lambda: self.add_monitor(stock_code, stock_name)
                    )
                    
                    # 計算性能
                    menu_create_time = time.time()
                    create_duration = (menu_create_time - start_time) * 1000
                    
                    # 顯示選單
                    menu.exec(self.table.mapToGlobal(position))
                    
                    # 計算總時間
                    end_time = time.time()
                    total_duration = (end_time - start_time) * 1000
                    
                    print(f"⚡ 性能統計:")
                    print(f"  選單創建時間: {create_duration:.2f}ms")
                    print(f"  總響應時間: {total_duration:.2f}ms")
                    
                    if total_duration < 100:
                        print("✅ 性能優秀 (<100ms)")
                    elif total_duration < 300:
                        print("⚠️ 性能一般 (100-300ms)")
                    else:
                        print("❌ 性能較差 (>300ms)")
                    
                except Exception as e:
                    print(f"❌ 右鍵選單錯誤: {e}")
                    import traceback
                    traceback.print_exc()
            
            def show_assessment(self, stock_code, stock_name, rank, yoy, mom):
                """顯示評估（包含財務指標）"""
                print(f"📊 顯示 {stock_code} {stock_name} 評估")
                
                # 模擬財務指標
                financial_data = {
                    '2330': {'殖利率': '2.1%', '本益比': '15.8', '股價淨值比': '2.3', 'EPS': '36.7'},
                    '2317': {'殖利率': '4.2%', '本益比': '12.5', '股價淨值比': '1.8', 'EPS': '8.4'},
                    '2454': {'殖利率': '1.8%', '本益比': '18.2', '股價淨值比': '3.1', 'EPS': '41.2'},
                    '1101': {'殖利率': '5.5%', '本益比': '8.9', '股價淨值比': '1.2', 'EPS': '5.1'},
                    '8021': {'殖利率': '0.8%', '本益比': '25.3', '股價淨值比': '4.2', 'EPS': '0.99'},
                }.get(stock_code, {'殖利率': '3.0%', '本益比': '15.0', '股價淨值比': '2.0', 'EPS': '2.5'})
                
                QMessageBox.information(
                    self, "月營收綜合評估", 
                    f"📊 {stock_code} {stock_name} - 月營收綜合評估\n\n"
                    f"🏆 基本資訊\n"
                    f"├── 排名：第 {rank} 名\n"
                    f"├── 股票代碼：{stock_code}\n"
                    f"├── 股票名稱：{stock_name}\n"
                    f"└── 西元年月：202507\n\n"
                    f"📈 成長率分析\n"
                    f"├── 年增率 (YoY%)：{yoy} (權重60%)\n"
                    f"├── 月增率 (MoM%)：{mom} (權重40%)\n"
                    f"└── 綜合評分：基於YoY+MoM計算\n\n"
                    f"💎 財務指標\n"
                    f"├── 殖利率：{financial_data['殖利率']}\n"
                    f"├── 本益比：{financial_data['本益比']}\n"
                    f"├── 股價淨值比：{financial_data['股價淨值比']}\n"
                    f"└── 每股盈餘 (EPS)：{financial_data['EPS']} 元\n\n"
                    f"✅ 右鍵選單和財務指標功能正常！"
                )
            
            def show_ranking(self, stock_code, stock_name, yoy, mom):
                """顯示排名"""
                QMessageBox.information(self, "綜合排名", f"🏆 {stock_code} {stock_name} 綜合排名\n\nYoY: {yoy}, MoM: {mom}")
            
            def show_news(self, stock_code, stock_name):
                """顯示新聞"""
                QMessageBox.information(self, "股票新聞", f"📰 {stock_code} {stock_name} 股票新聞")
            
            def add_monitor(self, stock_code, stock_name):
                """加入監控"""
                QMessageBox.information(self, "監控清單", f"📊 {stock_code} {stock_name} 已加入監控清單")
        
        app = QApplication(sys.argv)
        window = PerformanceTest()
        window.show()
        
        print("\n📋 測試說明:")
        print("✅ 右鍵選單性能測試已啟動")
        print("🖱️ 請在表格中的任意股票行上右鍵點擊")
        print("⚡ 觀察控制台顯示的性能統計")
        print("📊 測試月營收綜合評估功能")
        print("💎 檢查財務指標是否正常顯示")
        print("🚪 關閉視窗結束測試")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 性能測試創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 右鍵選單和財務指標修復驗證")
    print("=" * 60)
    
    # 測試財務指標
    financial_ok = test_financial_info()
    
    print("\n" + "=" * 60)
    print("🎯 測試結果:")
    print(f"  財務指標獲取: {'✅ 正常' if financial_ok else '❌ 異常'}")
    
    print("\n🎯 啟動性能測試...")
    create_performance_test()

if __name__ == "__main__":
    main()
