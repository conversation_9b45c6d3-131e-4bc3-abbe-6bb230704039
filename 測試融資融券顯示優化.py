#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試融資融券顯示優化
驗證顏色配色一致性和緊湊布局
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI
from datetime import datetime

def test_margin_trading_display_optimization():
    """測試融資融券顯示優化"""
    print("🧪 測試融資融券顯示優化")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    # 模擬股票資料
    test_stock_data = {
        '股票代碼': '1101',
        '股票名稱': '台泥',
        '排名': 1,
        '當月營收': '10000000',
        'YoY%': '4.13',
        'MoM%': '19.06'
    }
    
    try:
        print("✅ 主視窗創建成功")
        
        # 測試融資融券區塊創建
        margin_group = window.create_margin_trading_group(test_stock_data)
        print("✅ 融資融券區塊創建成功")
        
        # 測試三大法人區塊創建（用於對比）
        institutional_group = window.create_institutional_trading_group(test_stock_data)
        print("✅ 三大法人區塊創建成功")
        
        print("\n📋 顯示優化檢查項目:")
        print("=" * 50)
        print("1. ✅ 顏色配色與其他區塊一致（移除特殊背景色）")
        print("2. ✅ 融資融券資訊各自在一行顯示")
        print("3. ✅ 區塊高度調整為緊湊模式（80-100px）")
        print("4. ✅ 使用統一的文字顏色（#333333）")
        print("5. ✅ 保持變化量的顏色標示功能")
        
        print("\n🎯 預期顯示效果:")
        print("┌─────────────────────────────────────────┐")
        print("│ 💰 融資融券狀況                        │")
        print("├─────────────────────────────────────────┤")
        print("│ 📅 資料日期：2025-07-30                 │")
        print("│ 🌍 融資：買進 N/A，賣出 N/A，償還 N/A， │")
        print("│         餘額 N/A (N/A)                  │")
        print("│ 🏦 融券：買進 N/A，賣出 N/A，償還 N/A， │")
        print("│         餘額 N/A (N/A)                  │")
        print("└─────────────────────────────────────────┘")
        
        print("\n🔍 與其他區塊的一致性:")
        print("• 背景色：統一白色背景")
        print("• 文字色：統一 #333333 深灰色")
        print("• 邊框：統一樣式和顏色")
        print("• 高度：緊湊顯示，節省空間")
        print("• 布局：左右分欄，資訊密度適中")
        
        print("\n📊 布局優化特點:")
        print("• 融資和融券資訊分別在左右兩欄")
        print("• 每欄包含完整的買進、賣出、償還、餘額資訊")
        print("• 餘額變化用顏色標示（紅色增加、綠色減少）")
        print("• 整體高度控制在100px以內")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_display():
    """測試整合顯示效果"""
    print("\n🧪 測試整合顯示效果")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    print("✅ 程式已啟動")
    print("\n📋 手動測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 右鍵點擊股票（如：1101 台泥）")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查融資融券區塊顯示效果")
    
    print("\n🎯 重點檢查項目:")
    print("• 融資融券區塊是否與其他區塊顏色一致")
    print("• 融資、融券資訊是否各自在一行顯示")
    print("• 區塊高度是否適中，不會太高")
    print("• 所有資訊是否能在一頁內完整顯示")
    print("• 變化量顏色標示是否正常")
    
    print("\n💡 如果看到以下效果表示優化成功:")
    print("• 融資融券區塊背景色與其他區塊一致")
    print("• 融資和融券資訊分別在左右兩欄")
    print("• 區塊高度緊湊，不會佔用過多空間")
    print("• 整個評估視窗內容能完整顯示")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

def main():
    """主測試函數"""
    print("🎯 融資融券顯示優化測試")
    print("=" * 60)
    
    # 測試1: 區塊創建測試
    print("\n【測試1】區塊創建和優化測試")
    try:
        if test_margin_trading_display_optimization():
            print("✅ 區塊創建和優化測試通過")
        else:
            print("❌ 區塊創建和優化測試失敗")
    except Exception as e:
        print(f"❌ 區塊創建測試失敗: {e}")
    
    # 測試2: 整合顯示測試（互動式）
    print("\n【測試2】整合顯示測試（互動式）")
    print("即將啟動程式進行整合顯示測試...")
    
    user_input = input("按 Enter 繼續，或輸入 'skip' 跳過: ").strip().lower()
    if user_input != 'skip':
        try:
            test_integration_display()
        except KeyboardInterrupt:
            print("\n測試被用戶中斷")
        except Exception as e:
            print(f"❌ 整合顯示測試失敗: {e}")
    else:
        print("⏭️ 跳過整合顯示測試")
    
    print("\n🎉 測試完成！")
    print("=" * 60)
    print("📋 優化總結:")
    print("1. ✅ 移除了融資融券區塊的特殊背景色")
    print("2. ✅ 調整為與其他區塊一致的顏色配色")
    print("3. ✅ 融資、融券資訊各自在一行顯示")
    print("4. ✅ 區塊高度調整為緊湊模式")
    print("5. ✅ 保持了變化量的顏色標示功能")
    print("6. ✅ 整體布局更加協調統一")

if __name__ == "__main__":
    main()
