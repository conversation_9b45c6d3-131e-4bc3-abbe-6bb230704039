#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的ROE資料查看功能
"""

import sqlite3
import pandas as pd
import os

def test_roe_query():
    """測試ROE查詢邏輯"""
    db_path = "D:/Finlab/history/tables/roe_data.db"
    
    print("🔍 測試修正後的ROE查詢...")
    
    if not os.path.exists(db_path):
        print("❌ ROE數據庫不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 使用修正後的查詢
        query = """
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, 
                   report_year, rank_position, crawl_date
            FROM roe_data 
            WHERE stock_code IS NOT NULL 
              AND stock_code != '' 
              AND stock_code != 'nan'
              AND roe_value IS NOT NULL
            ORDER BY CASE WHEN rank_position IS NULL THEN 999999 ELSE rank_position END,
                     roe_value DESC 
            LIMIT 10
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f"📊 查詢結果: {len(df)} 筆資料")
        
        if len(df) == 0:
            print("❌ 沒有找到有效資料")
            return False
        
        print("\n📋 前10筆ROE資料:")
        for i, row in df.iterrows():
            rank_pos = row.get('rank_position')
            stock_code = row.get('stock_code', '')
            stock_name = row.get('stock_name', '')
            roe_value = row.get('roe_value')
            roe_change = row.get('roe_change')
            eps_value = row.get('eps_value')
            report_year = row.get('report_year')
            
            # 格式化顯示
            rank_str = str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '?'
            code_str = str(stock_code) if stock_code and str(stock_code) != 'nan' else '?'
            name_str = str(stock_name) if stock_name and str(stock_name) != 'nan' else '?'
            roe_str = f"{float(roe_value):.1f}%" if roe_value is not None and pd.notna(roe_value) else '?'
            change_str = f"{float(roe_change):+.1f}" if roe_change is not None and pd.notna(roe_change) else '?'
            eps_str = f"{float(eps_value):.2f}" if eps_value is not None and pd.notna(eps_value) else '?'
            year_str = str(int(report_year)) if report_year is not None and pd.notna(report_year) else '?'
            
            print(f"  {i+1:2d}. 排名:{rank_str} {code_str} {name_str} - ROE:{roe_str} 變化:{change_str} EPS:{eps_str} 年度:{year_str}")
        
        print("\n✅ ROE查詢測試成功")
        return True
        
    except Exception as e:
        print(f"❌ 查詢測試失敗: {e}")
        return False

def test_gui_display_logic():
    """測試GUI顯示邏輯"""
    print("\n🖥️ 測試GUI顯示邏輯...")
    
    # 模擬一些測試數據
    test_data = [
        {
            'rank_position': 1,
            'stock_code': '1213',
            'stock_name': '大飲',
            'roe_value': 104.0,
            'roe_change': 151.0,
            'eps_value': 8.71,
            'report_year': 2024,
            'crawl_date': '2025-07-16 00:27:21'
        },
        {
            'rank_position': None,  # 測試NULL值
            'stock_code': '5314',
            'stock_name': '世紀*',
            'roe_value': 84.6,
            'roe_change': 64.4,
            'eps_value': 18.25,
            'report_year': 2024,
            'crawl_date': '2025-07-16 00:27:21'
        }
    ]
    
    df = pd.DataFrame(test_data)
    
    print("📋 測試GUI數據格式化:")
    for _, row in df.iterrows():
        # 使用修正後的邏輯
        rank_pos = row.get('rank_position')
        stock_code = row.get('stock_code', '')
        stock_name = row.get('stock_name', '')
        roe_value = row.get('roe_value')
        roe_change = row.get('roe_change')
        eps_value = row.get('eps_value')
        report_year = row.get('report_year')
        crawl_date = row.get('crawl_date', '')
        
        values = [
            str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '',
            str(stock_code) if stock_code and str(stock_code) != 'nan' else '',
            str(stock_name) if stock_name and str(stock_name) != 'nan' else '',
            f"{float(roe_value):.1f}" if roe_value is not None and pd.notna(roe_value) else '',
            f"{float(roe_change):+.1f}" if roe_change is not None and pd.notna(roe_change) else '',
            f"{float(eps_value):.2f}" if eps_value is not None and pd.notna(eps_value) else '',
            str(int(report_year)) if report_year is not None and pd.notna(report_year) else '',
            str(crawl_date)[:16] if crawl_date and str(crawl_date) != 'nan' else ''
        ]
        
        print(f"  格式化結果: {values}")
    
    print("✅ GUI顯示邏輯測試成功")
    return True

def main():
    """主函數"""
    print("🔧 ROE資料查看功能修正測試")
    print("=" * 50)
    
    # 測試查詢邏輯
    query_success = test_roe_query()
    
    # 測試GUI顯示邏輯
    gui_success = test_gui_display_logic()
    
    if query_success and gui_success:
        print(f"\n🎉 ROE資料查看功能修正完成！")
        print(f"💡 現在ROE資料查看窗口應該能正確顯示股票資料了")
    else:
        print(f"\n❌ 還有問題需要解決")

if __name__ == "__main__":
    main()
