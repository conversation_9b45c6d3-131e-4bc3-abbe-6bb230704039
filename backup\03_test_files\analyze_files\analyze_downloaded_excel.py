#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析下載的Excel文件結構
"""

import pandas as pd
import os
import glob

def analyze_excel_files():
    """分析下載的Excel文件"""
    print("🔍 分析下載的Excel文件結構")
    print("=" * 50)
    
    # 尋找下載的Excel文件
    download_dir = "D:/Finlab/history/tables/monthly_revenue"
    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
    
    if not excel_files:
        print("❌ 未找到下載的Excel文件")
        return False
    
    # 分析最新的文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📊 分析文件: {latest_file}")
    
    try:
        # 嘗試不同的讀取方式
        print("\n📋 嘗試不同的讀取方式...")
        
        # 方法1: 直接讀取
        try:
            df1 = pd.read_excel(latest_file)
            print(f"✅ 方法1 (直接讀取): {len(df1)} 行, {len(df1.columns)} 列")
            print(f"   列名: {list(df1.columns)[:5]}...")  # 顯示前5個列名
            
            # 顯示前幾行
            print("   前3行數據:")
            for i, row in df1.head(3).iterrows():
                print(f"     行{i}: {list(row)[:5]}...")  # 顯示前5個值
                
        except Exception as e:
            print(f"❌ 方法1失敗: {e}")
        
        # 方法2: 跳過標題行
        try:
            df2 = pd.read_excel(latest_file, header=2)
            print(f"✅ 方法2 (跳過2行): {len(df2)} 行, {len(df2.columns)} 列")
            print(f"   列名: {list(df2.columns)[:5]}...")
            
            # 顯示前幾行
            print("   前3行數據:")
            for i, row in df2.head(3).iterrows():
                print(f"     行{i}: {list(row)[:5]}...")
                
        except Exception as e:
            print(f"❌ 方法2失敗: {e}")
        
        # 方法3: 無標題讀取
        try:
            df3 = pd.read_excel(latest_file, header=None)
            print(f"✅ 方法3 (無標題): {len(df3)} 行, {len(df3.columns)} 列")
            
            # 顯示前10行來理解結構
            print("   前10行數據:")
            for i, row in df3.head(10).iterrows():
                row_data = [str(x)[:20] for x in row[:8]]  # 前8列，每個值最多20字符
                print(f"     行{i}: {row_data}")
                
        except Exception as e:
            print(f"❌ 方法3失敗: {e}")
        
        # 尋找包含月份信息的行
        print("\n🔍 尋找月份信息...")
        
        try:
            df = pd.read_excel(latest_file, header=None)
            
            month_patterns = ['Jan-', 'Feb-', 'Mar-', 'Apr-', 'May-', 'Jun-', 
                            'Jul-', 'Aug-', 'Sep-', 'Oct-', 'Nov-', 'Dec-']
            
            month_rows = []
            for i, row in df.iterrows():
                for cell in row:
                    cell_str = str(cell)
                    if any(pattern in cell_str for pattern in month_patterns):
                        month_rows.append((i, cell_str))
                        break
            
            if month_rows:
                print(f"✅ 找到 {len(month_rows)} 行包含月份信息:")
                for row_idx, month_str in month_rows[:5]:  # 顯示前5行
                    print(f"   行{row_idx}: {month_str}")
                    
                    # 顯示該行的完整數據
                    full_row = df.iloc[row_idx]
                    row_data = [str(x) for x in full_row[:12]]  # 前12列
                    print(f"     完整行: {row_data}")
            else:
                print("❌ 未找到月份信息")
                
                # 搜索可能的數字模式
                print("\n🔍 搜索數字模式...")
                for i, row in df.head(20).iterrows():
                    for j, cell in enumerate(row):
                        cell_str = str(cell)
                        if cell_str.replace(',', '').replace('.', '').replace('-', '').isdigit():
                            if len(cell_str) >= 3:  # 可能是營收數據
                                print(f"   行{i}列{j}: {cell_str}")
                                break
                
        except Exception as e:
            print(f"❌ 月份搜索失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析Excel文件失敗: {e}")
        return False

def create_fixed_parser():
    """基於分析結果創建修復的解析器"""
    print("\n🔧 創建修復的解析器...")
    
    # 這裡會根據分析結果來修復解析邏輯
    print("📋 修復建議:")
    print("1. 檢查Excel文件的實際結構")
    print("2. 確定月份數據的起始行")
    print("3. 確定各欄位的正確位置")
    print("4. 修改parse_excel_file方法")

def main():
    """主函數"""
    success = analyze_excel_files()
    
    if success:
        create_fixed_parser()
        print("\n🎉 Excel文件分析完成！")
        print("現在可以根據分析結果修復解析邏輯")
    else:
        print("\n❌ Excel文件分析失敗")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
