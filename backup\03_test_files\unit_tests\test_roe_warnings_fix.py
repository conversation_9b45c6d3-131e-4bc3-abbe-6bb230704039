#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試ROE下載器警告修正效果
"""

import os
import sys
import logging
from datetime import datetime

def test_roe_downloader_warnings():
    """測試ROE下載器警告修正"""
    try:
        print("🔧 測試ROE下載器警告修正")
        print("=" * 60)
        
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        
        # 創建下載器
        downloader = GoodinfoROECSVDownloader()
        
        print(f"📁 下載目錄: {downloader.download_dir}")
        print(f"💾 數據庫路徑: {downloader.db_path}")
        
        # 測試年份選擇功能
        print(f"\n📅 測試年份選擇功能...")
        
        # 設置瀏覽器
        driver = downloader.setup_driver()
        if not driver:
            print("❌ 瀏覽器設置失敗")
            return False
        
        try:
            # 訪問ROE頁面
            url = f"{downloader.base_url}?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
            print(f"🌐 訪問頁面: {url}")
            driver.get(url)
            
            # 等待頁面載入
            import time
            time.sleep(5)
            
            # 測試年份選擇
            current_year = datetime.now().year
            print(f"🔍 測試選擇年份: {current_year}")
            
            result = downloader.select_year(driver, current_year)
            if result:
                print("✅ 年份選擇成功")
            else:
                print("⚠️ 年份選擇失敗，但這可能是正常的（頁面可能已是當前年度）")
            
            # 檢查頁面標題
            title = driver.title
            print(f"📄 頁面標題: {title}")
            
            # 檢查是否有表格
            from selenium.webdriver.common.by import By
            tables = driver.find_elements(By.TAG_NAME, "table")
            print(f"📊 找到 {len(tables)} 個表格")
            
            if tables:
                # 檢查第一個表格的行數
                rows = tables[0].find_elements(By.TAG_NAME, "tr")
                print(f"📋 第一個表格有 {len(rows)} 行")
                
                if len(rows) > 1:
                    # 顯示前幾行的內容
                    print("📝 表格前3行內容:")
                    for i, row in enumerate(rows[:3]):
                        cells = row.find_elements(By.TAG_NAME, "td")
                        if cells:
                            cell_texts = [cell.text[:20] for cell in cells[:5]]  # 只顯示前5列，每列最多20字符
                            print(f"  第{i+1}行: {cell_texts}")
            
            return True
            
        finally:
            driver.quit()
            print("🔚 瀏覽器已關閉")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_csv_download():
    """測試CSV下載功能"""
    try:
        print(f"\n📥 測試CSV下載功能...")
        
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        
        downloader = GoodinfoROECSVDownloader()
        
        # 下載當年度資料
        current_year = datetime.now().year
        print(f"🚀 開始下載 {current_year} 年度ROE資料...")
        
        csv_file = downloader.download_roe_csv(year=current_year)
        
        if csv_file and os.path.exists(csv_file):
            print(f"✅ CSV下載成功: {os.path.basename(csv_file)}")
            
            # 檢查文件大小
            file_size = os.path.getsize(csv_file)
            print(f"📊 文件大小: {file_size} bytes")
            
            # 檢查文件內容
            try:
                import pandas as pd
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                print(f"📋 資料筆數: {len(df)}")
                print(f"📝 欄位數量: {len(df.columns)}")
                print(f"📄 欄位名稱: {list(df.columns)[:5]}...")  # 只顯示前5個欄位
                
                if len(df) > 0:
                    print("📊 前3筆資料:")
                    for i, row in df.head(3).iterrows():
                        stock_code = row.get('stock_code', 'N/A')
                        stock_name = row.get('stock_name', 'N/A')
                        roe_value = row.get('ROE (%)', 'N/A')
                        print(f"  {i+1}. {stock_code} {stock_name} - ROE: {roe_value}")
                
                return True
                
            except Exception as e:
                print(f"⚠️ 讀取CSV失敗: {e}")
                return False
        else:
            print("❌ CSV下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ CSV下載測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def main():
    """主測試流程"""
    print("🧪 ROE下載器警告修正測試")
    print("=" * 80)
    
    # 設置日誌級別以減少不必要的輸出
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    success_count = 0
    total_tests = 2
    
    # 測試1: 年份選擇功能
    print("🔬 測試1: 年份選擇功能")
    if test_roe_downloader_warnings():
        success_count += 1
        print("✅ 測試1通過")
    else:
        print("❌ 測試1失敗")
    
    # 測試2: CSV下載功能
    print("\n🔬 測試2: CSV下載功能")
    if test_csv_download():
        success_count += 1
        print("✅ 測試2通過")
    else:
        print("❌ 測試2失敗")
    
    # 總結
    print(f"\n📊 測試結果: {success_count}/{total_tests} 通過")
    
    if success_count == total_tests:
        print("🎉 所有測試通過！ROE下載器警告修正成功")
        return True
    else:
        print("⚠️ 部分測試失敗，需要進一步調整")
        return False

if __name__ == "__main__":
    main()
