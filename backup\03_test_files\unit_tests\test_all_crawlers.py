#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所有新聞爬蟲方法的效能比較
"""

import time
import gc
from datetime import datetime

def test_crawler_performance(crawler_func, name, *args, **kwargs):
    """測試爬蟲效能"""
    print(f"\n🔍 測試 {name}")
    print("-" * 50)
    
    gc.collect()  # 清理記憶體
    start_time = time.time()
    
    try:
        print(f"⏰ 開始時間: {datetime.now().strftime('%H:%M:%S')}")
        result = crawler_func(*args, **kwargs)
        success = True
        
        if isinstance(result, list):
            result_summary = f"找到 {len(result)} 筆新聞"
        else:
            result_summary = str(result)
            
        print(f"✅ 執行成功: {result_summary}")
        
    except Exception as e:
        success = False
        result_summary = f"錯誤: {str(e)}"
        print(f"❌ 執行失敗: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"⏱️  執行時間: {duration:.2f} 秒")
    
    # 效能評級
    if duration < 1:
        grade = "🚀 極速"
    elif duration < 5:
        grade = "⚡ 快速"
    elif duration < 15:
        grade = "🐢 中等"
    else:
        grade = "🐌 較慢"
    
    print(f"📈 效能評級: {grade}")
    
    gc.collect()
    
    return {
        'name': name,
        'success': success,
        'duration': duration,
        'result': result_summary,
        'grade': grade
    }

def test_quick_crawler():
    """測試極速爬蟲"""
    from news_crawler_lightweight import QuickNewsCrawler
    crawler = QuickNewsCrawler()
    return crawler.quick_search("2330", days=1)

def test_lightweight_crawler():
    """測試輕量級爬蟲"""
    from news_crawler_lightweight import LightweightNewsCrawler
    crawler = LightweightNewsCrawler()
    return crawler.run_lightweight(ndays=1, stock_code="2330")

def test_selenium_quick():
    """測試 Selenium 快速搜尋"""
    from news_crawler_selenium import SeleniumNewsCrawler
    crawler = SeleniumNewsCrawler()
    return crawler.quick_search_selenium("2330")

def test_selenium_full():
    """測試 Selenium 完整爬取"""
    from news_crawler_selenium import SeleniumNewsCrawler
    crawler = SeleniumNewsCrawler()
    return crawler.run(ndays=1, stock_code="2330")

def test_optimized_crawler():
    """測試優化版爬蟲"""
    from news_crawler_optimized import OptimizedNewsCrawler
    crawler = OptimizedNewsCrawler()
    return crawler.run(ndays=1, stock_code="2330")

def main():
    """主測試函數"""
    print("🚀 全方位新聞爬蟲效能測試")
    print("=" * 60)
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 測試目標: 台積電 (2330) 新聞")
    
    results = []
    
    # 測試順序：從最快到最慢
    test_cases = [
        (test_quick_crawler, "極速爬蟲 (API)"),
        (test_lightweight_crawler, "輕量級爬蟲"),
        (test_selenium_quick, "Selenium 快速"),
        (test_selenium_full, "Selenium 完整"),
        (test_optimized_crawler, "優化版爬蟲"),
    ]
    
    for test_func, name in test_cases:
        try:
            result = test_crawler_performance(test_func, name)
            results.append(result)
            
            # 讓系統休息一下
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ {name} 測試失敗: {e}")
            results.append({
                'name': name,
                'success': False,
                'duration': 0,
                'result': f"測試失敗: {e}",
                'grade': "❌ 失敗"
            })
    
    # 顯示比較結果
    print("\n" + "=" * 60)
    print("📊 效能比較結果")
    print("=" * 60)
    
    print(f"{'爬蟲方法':<20} {'狀態':<6} {'時間(秒)':<10} {'評級':<12} {'結果'}")
    print("-" * 80)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        duration_str = f"{result['duration']:.2f}" if result['success'] else "N/A"
        result_str = str(result['result'])[:30] + "..." if len(str(result['result'])) > 30 else str(result['result'])
        
        print(f"{result['name']:<20} {status:<6} {duration_str:<10} {result['grade']:<12} {result_str}")
    
    # 分析結果
    successful_results = [r for r in results if r['success']]
    
    if successful_results:
        print(f"\n🏆 效能分析:")
        
        # 找出最快的
        fastest = min(successful_results, key=lambda x: x['duration'])
        print(f"  ⚡ 最快方法: {fastest['name']} ({fastest['duration']:.2f}秒)")
        
        # 按速度分類
        speed_categories = {
            "🚀 極速 (<1秒)": [],
            "⚡ 快速 (1-5秒)": [],
            "🐢 中等 (5-15秒)": [],
            "🐌 較慢 (>15秒)": []
        }
        
        for result in successful_results:
            duration = result['duration']
            if duration < 1:
                speed_categories["🚀 極速 (<1秒)"].append(result)
            elif duration < 5:
                speed_categories["⚡ 快速 (1-5秒)"].append(result)
            elif duration < 15:
                speed_categories["🐢 中等 (5-15秒)"].append(result)
            else:
                speed_categories["🐌 較慢 (>15秒)"].append(result)
        
        for category, crawlers in speed_categories.items():
            if crawlers:
                print(f"\n  {category}:")
                for crawler in crawlers:
                    print(f"    • {crawler['name']}")
        
        # GUI 使用建議
        print(f"\n💡 GUI 使用建議:")
        
        ultra_fast = [r for r in successful_results if r['duration'] < 1]
        fast = [r for r in successful_results if 1 <= r['duration'] < 5]
        
        if ultra_fast:
            print(f"  🎯 主要推薦: {ultra_fast[0]['name']} - 用戶幾乎感覺不到延遲")
            if len(ultra_fast) > 1:
                print(f"  🔄 備用方案: {ultra_fast[1]['name']}")
        
        if fast:
            print(f"  ⚡ 次要選擇: {fast[0]['name']} - 可接受的等待時間")
        
        # 系統負擔評估
        print(f"\n📱 系統負擔評估:")
        for result in successful_results:
            if "selenium" in result['name'].lower():
                print(f"  ⚠️ {result['name']}: 需要 Chrome 瀏覽器，記憶體使用較高")
            elif "輕量" in result['name']:
                print(f"  ✅ {result['name']}: 系統負擔最小，推薦用於 GUI")
            elif "極速" in result['name']:
                print(f"  🚀 {result['name']}: 網路請求最少，速度最快")
    
    else:
        print(f"\n❌ 所有測試都失敗了")
        print(f"💡 可能原因:")
        print(f"  • 網路連線問題")
        print(f"  • 相關模組未安裝 (selenium, requests, beautifulsoup4)")
        print(f"  • ChromeDriver 未安裝或路徑問題")
        print(f"  • API 服務暫時不可用")
    
    # 安裝建議
    print(f"\n🔧 環境需求:")
    print(f"  • 基本: pip install requests beautifulsoup4")
    print(f"  • Selenium: pip install selenium + ChromeDriver")
    print(f"  • 監控: pip install psutil")

if __name__ == "__main__":
    main()
    
    print(f"\n" + "=" * 60)
    print("🎯 總結:")
    print("1. 極速爬蟲適合即時查詢，系統負擔最小")
    print("2. 輕量級爬蟲平衡速度和功能，適合 GUI 使用")
    print("3. Selenium 爬蟲能處理動態內容，但速度較慢")
    print("4. 建議在 GUI 中實作多層級搜尋策略")
    print("5. 優先使用快速方法，失敗時自動降級")
    
    print("\n按 Enter 鍵結束...")
    input()
