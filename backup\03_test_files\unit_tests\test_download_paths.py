#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試除權息下載功能的目錄設定
驗證所有下載功能都使用相同的目錄 D:/Finlab/history/tables
"""

import os
import sys
import tempfile
import shutil

def test_enhanced_dividend_crawler():
    """測試增強版除權息爬蟲的輸出目錄"""
    print("🧪 測試增強版除權息爬蟲...")
    
    try:
        # 模擬測試，不實際執行下載
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        # 創建測試實例
        crawler = EnhancedDividendCrawler()
        
        # 檢查 export_to_csv 方法中的目錄設定
        import inspect
        source = inspect.getsource(crawler.export_to_csv)
        
        if 'D:/Finlab/history/tables' in source:
            print("✅ 增強版爬蟲使用正確目錄: D:/Finlab/history/tables")
            return True
        else:
            print("❌ 增強版爬蟲目錄設定錯誤")
            return False
            
    except Exception as e:
        print(f"❌ 測試增強版爬蟲失敗: {e}")
        return False

def test_fixed_dividend_downloader():
    """測試固定版除權息下載器的輸出目錄"""
    print("🧪 測試固定版除權息下載器...")
    
    try:
        # 檢查 fixed_dividend_downloader.py 的源碼
        with open('fixed_dividend_downloader.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        if 'D:/Finlab/history/tables' in source and 'os.path.join(output_dir' in source:
            print("✅ 固定版下載器使用正確目錄: D:/Finlab/history/tables")
            return True
        else:
            print("❌ 固定版下載器目錄設定錯誤")
            return False
            
    except Exception as e:
        print(f"❌ 測試固定版下載器失敗: {e}")
        return False

def test_final_dividend_crawler():
    """測試最終版除權息爬蟲的輸出目錄"""
    print("🧪 測試最終版除權息爬蟲...")
    
    try:
        # 檢查 final_dividend_crawler_with_dates.py 的源碼
        with open('final_dividend_crawler_with_dates.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        if 'D:/Finlab/history/tables' in source and 'output_dir = "D:/Finlab/history/tables"' in source:
            print("✅ 最終版爬蟲使用正確目錄: D:/Finlab/history/tables")
            return True
        else:
            print("❌ 最終版爬蟲目錄設定錯誤")
            return False
            
    except Exception as e:
        print(f"❌ 測試最終版爬蟲失敗: {e}")
        return False

def test_database_paths():
    """測試資料庫路徑設定"""
    print("🧪 測試資料庫路徑設定...")
    
    try:
        # 檢查各種配置文件和模組中的資料庫路徑
        test_files = [
            'dividend_data_importer.py',
            'import_dividend_csv.py',
            'auto_dividend_cli.py',
            'auto_dividend_downloader.py'
        ]
        
        all_correct = True
        
        for file_name in test_files:
            if os.path.exists(file_name):
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'D:/Finlab/history/tables/dividend_data.db' in content:
                    print(f"  ✅ {file_name}: 資料庫路徑正確")
                else:
                    print(f"  ❌ {file_name}: 資料庫路徑可能錯誤")
                    all_correct = False
            else:
                print(f"  ⚠️ {file_name}: 檔案不存在")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 測試資料庫路徑失敗: {e}")
        return False

def test_directory_consistency():
    """測試目錄一致性"""
    print("🧪 測試目錄一致性...")
    
    target_dir = "D:/Finlab/history/tables"
    
    print(f"📁 目標目錄: {target_dir}")
    
    # 檢查目錄是否存在
    if os.path.exists(target_dir):
        print("✅ 目標目錄存在")
        
        # 列出目錄內容
        try:
            files = os.listdir(target_dir)
            db_files = [f for f in files if f.endswith('.db')]
            csv_files = [f for f in files if f.endswith('.csv')]
            
            print(f"📊 目錄統計:")
            print(f"  - 資料庫檔案: {len(db_files)} 個")
            print(f"  - CSV檔案: {len(csv_files)} 個")
            print(f"  - 總檔案數: {len(files)} 個")
            
            # 檢查除權息資料庫是否存在
            dividend_db = "dividend_data.db"
            if dividend_db in files:
                print(f"✅ 除權息資料庫存在: {dividend_db}")
            else:
                print(f"⚠️ 除權息資料庫不存在: {dividend_db}")
            
            return True
            
        except Exception as e:
            print(f"❌ 讀取目錄內容失敗: {e}")
            return False
    else:
        print("⚠️ 目標目錄不存在，但下載時會自動創建")
        return True

def create_directory_structure():
    """創建目錄結構（如果不存在）"""
    print("🔧 檢查並創建目錄結構...")
    
    target_dir = "D:/Finlab/history/tables"
    
    try:
        os.makedirs(target_dir, exist_ok=True)
        print(f"✅ 目錄已確保存在: {target_dir}")
        return True
    except Exception as e:
        print(f"❌ 創建目錄失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 除權息下載目錄一致性測試")
    print("=" * 60)
    
    # 測試結果
    results = []
    
    # 1. 測試各個下載器的目錄設定
    results.append(("增強版爬蟲", test_enhanced_dividend_crawler()))
    results.append(("固定版下載器", test_fixed_dividend_downloader()))
    results.append(("最終版爬蟲", test_final_dividend_crawler()))
    
    # 2. 測試資料庫路徑設定
    results.append(("資料庫路徑", test_database_paths()))
    
    # 3. 測試目錄一致性
    results.append(("目錄一致性", test_directory_consistency()))
    
    # 4. 創建目錄結構
    results.append(("目錄結構", create_directory_structure()))
    
    print("\n📊 測試結果摘要:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<15}: {status}")
        if not result:
            all_passed = False
    
    print("\n🎯 總結:")
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有測試通過！")
        print("✅ 除權息下載功能已統一使用目錄: D:/Finlab/history/tables")
        print("✅ 與除權息資料庫放在相同目錄")
        print("✅ 符合用戶需求")
    else:
        print("⚠️ 部分測試失敗，請檢查相關設定")
    
    print(f"\n📁 統一目錄: D:/Finlab/history/tables")
    print("📋 包含內容:")
    print("  - dividend_data.db (除權息資料庫)")
    print("  - price.db (股價資料庫)")
    print("  - news.db (新聞資料庫)")
    print("  - pe_data.db (本益比資料庫)")
    print("  - dividend_data_*.csv (下載的除權息CSV檔案)")

if __name__ == "__main__":
    main()
