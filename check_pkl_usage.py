#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查系統中PKL檔案的使用情況
"""

import os
import sqlite3

def check_files():
    """檢查檔案存在性"""
    print("📊 檢查檔案存在性")
    print("=" * 50)
    
    # 檢查資料庫檔案
    db_files = [
        'D:/Finlab/history/tables/pe_data.db',
        'D:/Finlab/history/tables/monthly_report.db',
        'D:/Finlab/history/tables/price.db'
    ]
    
    print("🗄️ 資料庫檔案:")
    for db_path in db_files:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path) / (1024*1024)  # MB
            print(f"  ✅ {os.path.basename(db_path)}: {size:.1f} MB")
        else:
            print(f"  ❌ {os.path.basename(db_path)}: 不存在")
    
    # 檢查PKL檔案
    pkl_files = [
        'D:/Finlab/history/tables/pe.pkl',
        'D:/Finlab/history/tables/monthly_report.pkl'
    ]
    
    print("\n📦 PKL檔案:")
    for pkl_path in pkl_files:
        if os.path.exists(pkl_path):
            size = os.path.getsize(pkl_path) / (1024*1024)  # MB
            print(f"  ✅ {os.path.basename(pkl_path)}: {size:.1f} MB")
        else:
            print(f"  ❌ {os.path.basename(pkl_path)}: 不存在")

def check_pe_data_db():
    """檢查 pe_data.db 的結構"""
    print("\n📊 檢查 pe_data.db 結構")
    print("=" * 50)
    
    db_path = 'D:/Finlab/history/tables/pe_data.db'
    
    if not os.path.exists(db_path):
        print("❌ pe_data.db 不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 表格列表:")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 檢查表格結構
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"    欄位: {[col[1] for col in columns]}")
            
            # 檢查資料筆數
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"    筆數: {count:,}")
            
            # 檢查是否有殖利率相關欄位
            column_names = [col[1] for col in columns]
            relevant_columns = [col for col in column_names if any(keyword in col for keyword in ['殖利率', '本益比', '股價淨值比', 'dividend', 'pe', 'pb'])]
            if relevant_columns:
                print(f"    相關欄位: {relevant_columns}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def main():
    """主函數"""
    print("🔍 PKL檔案使用情況檢查")
    print("=" * 60)
    
    check_files()
    check_pe_data_db()
    
    print("\n📋 總結")
    print("=" * 50)
    print("目前仍使用PKL檔案的功能:")
    print("1. 高殖利率烏龜策略 - 使用 pe.pkl")
    print("2. 月營收排行榜 - 使用 monthly_report.pkl (備用)")
    print("\n建議:")
    print("- 如果 pe_data.db 包含相同資料，可以將高殖利率烏龜策略改為使用DB")
    print("- 月營收排行榜已優先使用DB，PKL僅作備用")

if __name__ == "__main__":
    main()
