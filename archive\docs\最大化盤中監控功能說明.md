# 🔍 最大化盤中監控功能說明

## 📋 功能概述

我已經成功為您的台股智能選股系統實現了**最大化盤中監控視窗**功能，解決了原本監控區域可視範圍不夠大的問題，並實現了與策略的完整聯結。

## 🎯 核心功能特色

### 1. 🔍 最大化顯示
- **專用彈出視窗**：1400x900像素的大型監控視窗
- **可視範圍擴大**：相比原本的小區域，監控內容一目了然
- **專業界面設計**：深色主題，適合長時間監控使用

### 2. 📊 策略聯結
- **自動獲取策略股票**：從當前策略篩選結果中自動提取股票列表
- **策略標識**：每支股票都顯示對應的策略名稱
- **動態更新**：當策略篩選結果改變時，監控列表自動更新

### 3. 🌐 即時數據獲取
- **TWSE官方API**：使用台灣證券交易所官方API獲取即時數據
- **備用機制**：當API失敗時自動切換到備用數據源
- **數據完整性**：包含股價、漲跌、成交量、開高低價等完整信息

## 🚀 使用方法

### 步驟1：執行策略選股
1. 在主程式中選擇任一策略（如"監獄兔"）
2. 設定篩選條件和日期
3. 點擊"執行選股"獲得篩選結果

### 步驟2：打開最大化監控
1. 在盤中監控區域找到**"🔍 最大化監控"**按鈕
2. 點擊按鈕打開專用監控視窗

### 步驟3：開始監控
1. 在彈出視窗中設定更新間隔（5-300秒）
2. 點擊**"🚀 開始監控"**按鈕
3. 系統開始即時更新股票數據

### 步驟4：監控管理
- **⏹️ 停止監控**：暫停數據更新
- **🔄 立即更新**：手動觸發一次數據更新
- **調整間隔**：隨時修改更新頻率

## 📈 監控表格說明

### 表格欄位
| 欄位 | 說明 | 數據來源 |
|------|------|----------|
| 股票代碼 | 4位數股票代碼 | 策略篩選結果 |
| 股票名稱 | 股票中文名稱 | 策略篩選結果 |
| **策略** | **對應的策略名稱** | **策略聯結** |
| 現價 | 最新成交價 | TWSE API |
| 漲跌 | 相對昨收的漲跌金額 | TWSE API |
| 漲跌幅% | 漲跌百分比 | TWSE API |
| 成交量 | 當日累計成交量 | TWSE API |
| 開盤價 | 當日開盤價 | TWSE API |
| 最高價 | 當日最高價 | TWSE API |
| 最低價 | 當日最低價 | TWSE API |
| 更新時間 | 最後更新時間 | 系統時間 |
| 狀態 | 數據來源狀態 | 系統狀態 |

### 狀態說明
- **✅ TWSE**：成功從證交所API獲取數據
- **⚠️ 模擬**：使用備用模擬數據
- **❌ 失敗**：數據獲取完全失敗

## 🔧 技術實現

### TWSE API集成
基於您提供的範例程式碼，我們實現了完整的TWSE API集成：

```python
# API端點
url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"

# 參數設置
params = {
    'ex_ch': f'tse_{stock_code}.tw',
    'json': '1',
    'delay': '0'
}

# 發送請求
response = requests.get(url, params=params, timeout=10, verify=False)
```

### 數據解析
```python
if data.get('stat') == 'OK' and 'msgArray' in data:
    stock_info = data['msgArray'][0]
    current_price = float(stock_info.get('z', 0))
    volume = int(stock_info.get('v', 0))
    # ... 其他數據解析
```

### 策略聯結機制
```python
def get_strategy_filtered_stocks(self):
    """從結果表格中獲取策略篩選的股票"""
    strategy_stocks = []
    for row in range(self.result_table.rowCount()):
        stock_code = self.result_table.item(row, 0).text()
        stock_name = self.result_table.item(row, 1).text()
        strategy_stocks.append({
            'code': stock_code,
            'name': stock_name,
            'strategy': self.strategy_combo.currentText()
        })
    return strategy_stocks
```

## 🎨 界面設計

### 視覺特色
- **深色主題**：減少眼部疲勞，適合長時間使用
- **顏色編碼**：紅色表示上漲，綠色表示下跌
- **清晰布局**：控制面板、數據表格、狀態面板分層設計
- **響應式設計**：表格可排序、可調整列寬

### 控制面板
- **策略信息顯示**：當前策略名稱和股票數量
- **更新間隔控制**：5-300秒可調
- **監控控制按鈕**：開始/停止/立即更新

## ⚠️ 注意事項

### 使用限制
1. **交易時間**：建議在台股交易時間內使用（9:00-13:30）
2. **網路連接**：需要穩定的網路連接訪問TWSE API
3. **更新頻率**：建議設定30秒以上間隔，避免過於頻繁的API請求

### 數據準確性
1. **即時性**：TWSE API有輕微延遲（通常1-2秒）
2. **可靠性**：API偶爾可能不穩定，系統會自動切換到備用數據
3. **完整性**：非交易時間可能無法獲取完整數據

## 🔮 未來擴展

### 可能的增強功能
1. **警報系統**：價格突破、成交量異常等警報
2. **圖表顯示**：內嵌小型K線圖
3. **數據導出**：監控數據匯出到Excel
4. **多策略監控**：同時監控多個策略的股票
5. **自定義欄位**：用戶可選擇顯示的數據欄位

## 📞 技術支援

如果在使用過程中遇到問題：
1. 檢查網路連接是否正常
2. 確認是否在交易時間內
3. 查看系統日誌獲取詳細錯誤信息
4. 嘗試調整更新間隔

---

**版本**：v1.0  
**更新日期**：2024-07-08  
**開發者**：O3mh 台股智能選股系統
