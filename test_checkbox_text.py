#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試複選框文字顯示修復
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QPushButton, QTextEdit, QLabel, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class CheckboxTextTestWindow(QMainWindow):
    """測試複選框文字顯示的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📝 複選框文字顯示測試")
        self.setGeometry(100, 100, 1200, 800)
        
        # 設置黑底主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("📝 複選框文字顯示修復測試")
        title_font = QFont()
        title_font.setPointSize(20)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 20px; padding: 20px;")
        layout.addWidget(title_label)
        
        # 修復說明
        fix_info = QTextEdit()
        fix_info.setMaximumHeight(250)
        fix_info.setStyleSheet("""
            QTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
            }
        """)
        fix_info.setHtml("""
        <h3 style="color: #00d4ff;">📝 複選框文字顯示問題修復</h3>
        
        <p><b>問題描述：</b></p>
        <ul>
            <li>複選框只顯示藍色方塊，看不到文字內容</li>
            <li>用戶無法識別各個數據類型選項</li>
        </ul>
        
        <p><b>修復方案：</b></p>
        <ul>
            <li><b>增強樣式優先級</b> - 使用 !important 確保文字顏色生效</li>
            <li><b>增大字體大小</b> - 從 14px 增加到 16px 提高可讀性</li>
            <li><b>加粗字體</b> - 使用 font-weight: bold 增加文字清晰度</li>
            <li><b>增加間距</b> - 設置 spacing 和 padding 改善布局</li>
            <li><b>設置最小高度</b> - min-height: 30px 確保足夠顯示空間</li>
            <li><b>優化指示器</b> - 重新設計複選框指示器樣式</li>
        </ul>
        
        <p><b>技術細節：</b></p>
        <ul>
            <li>文字顏色：#ffffff !important</li>
            <li>字體大小：16px</li>
            <li>字體粗細：bold</li>
            <li>指示器大小：20x20px</li>
            <li>選中顏色：#00d4ff</li>
        </ul>
        """)
        layout.addWidget(fix_info)
        
        # 測試複選框區域
        test_group = QGroupBox("📋 複選框文字顯示測試")
        test_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #3c3c3c;
                color: #ffffff;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #ffffff;
                font-weight: bold;
                font-size: 16px;
                background-color: #3c3c3c;
            }
        """)
        test_layout = QVBoxLayout(test_group)
        
        # 測試複選框 1
        test_cb1 = QCheckBox("📈 市場指數資訊 (即時更新)")
        test_cb1.setChecked(True)
        test_cb1.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                padding: 10px;
                color: #ffffff !important;
                font-weight: bold;
                background-color: transparent;
                spacing: 10px;
                min-height: 30px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #888888;
                border-radius: 4px;
                background-color: #2b2b2b;
                margin-right: 8px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        test_layout.addWidget(test_cb1)
        
        # 測試複選框 2
        test_cb2 = QCheckBox("💰 融資融券統計 (每日更新)")
        test_cb2.setChecked(True)
        test_cb2.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                padding: 10px;
                color: #ffffff !important;
                font-weight: bold;
                background-color: transparent;
                spacing: 10px;
                min-height: 30px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #888888;
                border-radius: 4px;
                background-color: #2b2b2b;
                margin-right: 8px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        test_layout.addWidget(test_cb2)
        
        # 測試複選框 3
        test_cb3 = QCheckBox("📊 發行量加權股價指數歷史資料 (歷史數據)")
        test_cb3.setChecked(False)
        test_cb3.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                padding: 10px;
                color: #ffffff !important;
                font-weight: bold;
                background-color: transparent;
                spacing: 10px;
                min-height: 30px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #888888;
                border-radius: 4px;
                background-color: #2b2b2b;
                margin-right: 8px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        test_layout.addWidget(test_cb3)
        
        # 檢查說明
        check_label = QLabel("✅ 如果您能清楚看到上面的複選框文字，說明修復成功！")
        check_label.setStyleSheet("color: #00ff00; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #2d4a2d; border: 1px solid #4a7c4a; border-radius: 6px;")
        test_layout.addWidget(check_label)
        
        layout.addWidget(test_group)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟修復後的爬蟲界面")
        test_btn.clicked.connect(self.test_main_interface)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #00d4ff;
                color: #000000;
                border: none;
                padding: 18px 40px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #00b8e6;
            }
        """)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #00d4ff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("📝 複選框文字顯示測試程式已啟動")
        self.log("✅ 使用 !important 確保文字顏色生效")
        self.log("✅ 增大字體大小到 16px")
        self.log("✅ 設置字體粗細為 bold")
        self.log("✅ 優化間距和布局")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_main_interface(self):
        """測試主界面"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟修復後的台灣證交所爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 界面已成功創建")
            self.log("🎯 請檢查複選框文字是否清晰可見")
            self.log("📋 應該能看到：")
            self.log("1. 📈 市場指數資訊 (即時更新)")
            self.log("2. 💰 融資融券統計 (每日更新)")
            self.log("3. 📊 發行量加權股價指數歷史資料")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = CheckboxTextTestWindow()
    window.show()
    
    print("📝 複選框文字顯示測試程式已啟動")
    print("✅ 主要修復：")
    print("1. 使用 !important 確保文字顏色")
    print("2. 增大字體大小到 16px")
    print("3. 設置字體粗細為 bold")
    print("4. 優化間距和布局")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
