#!/usr/bin/env python3
"""
簡單的語法測試
"""

try:
    print("測試語法...")
    
    # 嘗試編譯文件
    with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    compile(content, 'O3mh_gui_v21_optimized.py', 'exec')
    print("✅ 語法檢查通過")
    
    # 嘗試導入CANSLIMStrategy
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from O3mh_gui_v21_optimized import CANSLIMStrategy
    print("✅ CANSLIMStrategy 導入成功")
    
    # 測試創建實例
    canslim = CANSLIMStrategy()
    print(f"✅ 策略實例創建成功: {canslim.name}")
    
except SyntaxError as e:
    print(f"❌ 語法錯誤: {e}")
    print(f"   文件: {e.filename}")
    print(f"   行號: {e.lineno}")
    print(f"   位置: {e.offset}")
    print(f"   內容: {e.text}")
except Exception as e:
    print(f"❌ 其他錯誤: {e}")
    import traceback
    traceback.print_exc()
