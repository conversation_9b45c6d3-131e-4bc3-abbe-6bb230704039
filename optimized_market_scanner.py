#!/usr/bin/env python3
"""
優化的市場掃描器
專為智能掃描功能設計，使用Yahoo爬蟲和多線程技術
解決UI卡頓問題
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
import random
import re
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

class OptimizedMarketScanner:
    """優化的市場掃描器 - 專為智能掃描設計"""
    
    def __init__(self):
        """初始化掃描器"""
        self.session = requests.Session()
        
        # 🛡️ 反爬蟲機制
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0'
        }
        
        # 緩存機制
        self.cache = {}
        self.cache_timeout = 300  # 5分鐘緩存
        
        # 線程控制
        self.max_workers = 3  # 最大並發線程數
        self.request_delay = 0.5  # 請求間隔
        
        logging.info("✅ 優化市場掃描器初始化完成")
    
    def get_random_headers(self) -> Dict[str, str]:
        """獲取隨機請求標頭"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)
        return headers
    
    def make_safe_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """安全請求方法"""
        try:
            headers = self.get_random_headers()
            response = self.session.get(url, headers=headers, timeout=10, **kwargs)
            
            # 隨機延遲
            time.sleep(random.uniform(0.3, 0.8))
            
            return response
        except Exception as e:
            logging.warning(f"請求失敗 {url}: {e}")
            return None
    
    def get_yahoo_market_data(self, symbol: str) -> Optional[Dict]:
        """獲取Yahoo市場數據"""
        try:
            # 檢查緩存
            cache_key = f"yahoo_{symbol}"
            if cache_key in self.cache:
                cache_time, data = self.cache[cache_key]
                if time.time() - cache_time < self.cache_timeout:
                    return data
            
            # 構建Yahoo URL
            timestamp = int(time.time() * 1000)
            callback_id = f"jQuery{random.randint(111111111111111111, 999999999999999999)}_{timestamp}"
            url = f"https://tw.quote.finance.yahoo.net/quote/q?type=ta&perd=d&mkt=10&sym={symbol}&v=1&callback={callback_id}&_={timestamp}"
            
            response = self.make_safe_request(url)
            
            if response and response.status_code == 200:
                # 解析數據
                data = self._parse_yahoo_response(response.text, symbol)
                if data:
                    # 緩存數據
                    self.cache[cache_key] = (time.time(), data)
                    return data
            
            return None
            
        except Exception as e:
            logging.error(f"獲取Yahoo數據失敗 {symbol}: {e}")
            return None
    
    def _parse_yahoo_response(self, text: str, symbol: str) -> Optional[Dict]:
        """解析Yahoo回應數據"""
        try:
            # 解析邏輯（與yahoo_realtime_crawler.py相同）
            current_data = [l for l in text.split('{') if len(l) >= 60]
            if len(current_data) >= 2:
                current = current_data[-1].replace('"', '').split(',')
                
                # 昨日價格
                yday_match = re.search(':.*', current_data[-2].split(',')[4])
                if yday_match:
                    yday = float(yday_match.group()[1:])
                    
                    # 解析當前數據
                    open_match = re.search(':.*', current[1])
                    high_match = re.search(':.*', current[2])
                    low_match = re.search(':.*', current[3])
                    close_match = re.search(':.*', current[4])
                    volume_match = re.search(':.*', current[5].replace('}]', ''))
                    
                    if all([open_match, high_match, low_match, close_match, volume_match]):
                        close_price = float(close_match.group()[1:])
                        pct_change = round((close_price / yday - 1) * 100, 2)
                        
                        return {
                            'symbol': symbol,
                            'open': float(open_match.group()[1:]),
                            'high': float(high_match.group()[1:]),
                            'low': float(low_match.group()[1:]),
                            'close': close_price,
                            'volume': float(volume_match.group()[1:]),
                            'pct_change': pct_change,
                            'prev_close': yday,
                            'timestamp': datetime.now()
                        }
            
            return None
            
        except Exception as e:
            logging.error(f"解析Yahoo數據失敗 {symbol}: {e}")
            return None
    
    def scan_market_indices(self) -> Dict[str, Any]:
        """掃描市場指數 - 並行處理"""
        indices = {
            '^TWII': '台股加權指數',
            '0050': '台灣50ETF',
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科'
        }
        
        results = {}
        
        # 使用線程池並行獲取數據
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任務
            future_to_symbol = {
                executor.submit(self.get_yahoo_market_data, symbol): (symbol, name)
                for symbol, name in indices.items()
            }
            
            # 收集結果
            for future in as_completed(future_to_symbol):
                symbol, name = future_to_symbol[future]
                try:
                    data = future.result(timeout=10)
                    if data:
                        results[symbol] = {
                            'name': name,
                            'data': data
                        }
                        logging.info(f"✅ 獲取 {name} 數據成功")
                    else:
                        logging.warning(f"⚠️ 獲取 {name} 數據失敗")
                except Exception as e:
                    logging.error(f"❌ 獲取 {name} 數據異常: {e}")
        
        return results
    
    def scan_hot_stocks(self, stock_list: List[str] = None) -> Dict[str, Any]:
        """掃描熱門股票 - 並行處理"""
        if not stock_list:
            stock_list = ['2330', '2317', '2454', '3008', '2412', '2881', '2882', '6505', '2303', '1301']
        
        results = {}
        
        # 使用線程池並行獲取數據
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任務
            future_to_stock = {
                executor.submit(self.get_yahoo_market_data, stock): stock
                for stock in stock_list
            }
            
            # 收集結果
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                try:
                    data = future.result(timeout=10)
                    if data:
                        results[stock] = data
                        logging.debug(f"✅ 獲取 {stock} 數據成功")
                except Exception as e:
                    logging.error(f"❌ 獲取 {stock} 數據異常: {e}")
        
        return results
    
    def run_full_scan(self) -> Dict[str, Any]:
        """執行完整市場掃描"""
        try:
            logging.info("🚀 開始執行優化市場掃描...")
            start_time = time.time()
            
            scan_results = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_indices': {},
                'hot_stocks': {},
                'market_summary': {},
                'scan_duration': 0
            }
            
            # 並行執行掃描任務
            with ThreadPoolExecutor(max_workers=2) as executor:
                # 提交掃描任務
                indices_future = executor.submit(self.scan_market_indices)
                stocks_future = executor.submit(self.scan_hot_stocks)
                
                # 獲取結果
                try:
                    scan_results['market_indices'] = indices_future.result(timeout=30)
                    logging.info(f"✅ 市場指數掃描完成，獲取 {len(scan_results['market_indices'])} 項數據")
                except Exception as e:
                    logging.error(f"❌ 市場指數掃描失敗: {e}")
                
                try:
                    scan_results['hot_stocks'] = stocks_future.result(timeout=30)
                    logging.info(f"✅ 熱門股票掃描完成，獲取 {len(scan_results['hot_stocks'])} 項數據")
                except Exception as e:
                    logging.error(f"❌ 熱門股票掃描失敗: {e}")
            
            # 生成市場摘要
            scan_results['market_summary'] = self._generate_market_summary(scan_results)
            
            # 計算掃描耗時
            scan_results['scan_duration'] = round(time.time() - start_time, 2)
            
            logging.info(f"✅ 優化市場掃描完成，耗時 {scan_results['scan_duration']} 秒")
            return scan_results
            
        except Exception as e:
            logging.error(f"❌ 市場掃描失敗: {e}")
            return None
    
    def _generate_market_summary(self, scan_results: Dict) -> Dict[str, Any]:
        """生成市場摘要"""
        try:
            summary = {
                'total_items': 0,
                'positive_count': 0,
                'negative_count': 0,
                'avg_change': 0,
                'market_sentiment': '中性'
            }
            
            all_changes = []
            
            # 統計市場指數
            for symbol, data in scan_results.get('market_indices', {}).items():
                if 'data' in data and 'pct_change' in data['data']:
                    change = data['data']['pct_change']
                    all_changes.append(change)
                    if change > 0:
                        summary['positive_count'] += 1
                    elif change < 0:
                        summary['negative_count'] += 1
            
            # 統計熱門股票
            for symbol, data in scan_results.get('hot_stocks', {}).items():
                if 'pct_change' in data:
                    change = data['pct_change']
                    all_changes.append(change)
                    if change > 0:
                        summary['positive_count'] += 1
                    elif change < 0:
                        summary['negative_count'] += 1
            
            summary['total_items'] = len(all_changes)
            
            if all_changes:
                summary['avg_change'] = round(np.mean(all_changes), 2)
                
                # 判斷市場情緒
                if summary['avg_change'] > 1:
                    summary['market_sentiment'] = '樂觀'
                elif summary['avg_change'] < -1:
                    summary['market_sentiment'] = '悲觀'
                else:
                    summary['market_sentiment'] = '中性'
            
            return summary
            
        except Exception as e:
            logging.error(f"生成市場摘要失敗: {e}")
            return {}
    
    def clear_cache(self):
        """清除緩存"""
        self.cache.clear()
        logging.info("🗑️ 市場掃描器緩存已清除")

# 創建全局實例
optimized_scanner = OptimizedMarketScanner()

def run_optimized_scan() -> Dict[str, Any]:
    """便捷函數：執行優化掃描"""
    return optimized_scanner.run_full_scan()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試優化市場掃描器...")
    
    scanner = OptimizedMarketScanner()
    results = scanner.run_full_scan()
    
    if results:
        print("✅ 掃描成功")
        print(f"⏱️ 耗時: {results['scan_duration']} 秒")
        print(f"📊 市場指數: {len(results['market_indices'])} 項")
        print(f"📈 熱門股票: {len(results['hot_stocks'])} 項")
        print(f"💭 市場情緒: {results['market_summary'].get('market_sentiment', '未知')}")
    else:
        print("❌ 掃描失敗")
    
    print("🎉 測試完成！")
