# 🚀 台股智能選股系統 - 獨立版使用說明

## 📋 版本資訊

- **版本**: v21.0 優化增強版 (獨立執行檔)
- **編譯日期**: 2025-07-30
- **檔案大小**: 101MB
- **執行檔**: `台股智能選股系統.exe`

---

## 🎯 系統特色

### ✨ 獨立執行
- **免安裝**: 無需安裝 Python 或任何依賴套件
- **便攜性**: 可在任何 Windows 10/11 電腦上直接執行
- **完整功能**: 包含所有原版功能，無功能縮減

### 🔧 核心功能
- ✅ **多策略智能選股** - 5種內建策略
- ✅ **實時K線圖表分析** - 專業級圖表顯示
- ✅ **月營收綜合評估** - 包含三大法人買賣狀況
- ✅ **開盤前市場監控** - 自動掃描市場動態
- ✅ **Excel報告導出** - 一鍵生成分析報告

---

## 🚀 快速開始

### 步驟1: 下載與執行
1. **下載檔案**: 確保已下載 `台股智能選股系統.exe`
2. **直接執行**: 雙擊執行檔即可啟動
3. **首次啟動**: 程式會自動創建必要的資料庫和配置檔案

### 步驟2: 基本操作
1. **選擇策略**: 在左側選擇想要的選股策略
2. **設定參數**: 調整策略參數（可選）
3. **執行選股**: 點擊「執行排行」開始分析
4. **查看結果**: 在右側表格查看選股結果

### 步驟3: 進階功能
1. **月營收排行榜**: 選擇月營收相關排行榜
2. **右鍵選單**: 在股票上右鍵查看詳細評估
3. **K線圖分析**: 切換到「K線圖」標籤查看技術分析
4. **導出報告**: 使用「導出Excel」功能保存結果

---

## 📊 主要功能詳解

### 1. 智能選股策略

#### 🎯 內建策略
- **三頻率RSI策略**: 多時間框架RSI分析
- **小資族資優生策略**: 適合小額投資的穩健策略
- **二次創高策略**: 捕捉突破新高的股票
- **膽小貓策略**: 保守型投資策略
- **CANSLIM策略**: 成長股選股經典策略

#### 📈 月營收排行榜
- **YoY排序**: 按年增率排序
- **MoM排序**: 按月增率排序
- **綜合評分**: 結合多項指標的綜合排名
- **擴展到2000名**: 涵蓋更多股票選擇

### 2. 月營收綜合評估

#### 🏆 基本資訊
- 股票代碼、名稱、排名
- 當月營收、上月營收、去年同月營收
- YoY%、MoM% 成長率

#### 💎 財務指標
- **股價** (含計算日期): 精確的股價資訊
- **殖利率**: 股息收益率
- **本益比**: 股價與每股盈餘比值
- **股價淨值比**: 股價與每股淨值比值
- **EPS**: 每股盈餘

#### 🏛️ 三大法人買賣狀況
- **外資**: 買進、賣出、買賣超 (千股)
- **投信**: 買進、賣出、買賣超 (千股)
- **自營商**: 買進、賣出、買賣超 (千股)
- **顏色標示**: 買超紅色、賣超綠色

### 3. K線圖表分析

#### 📊 技術指標
- **移動平均線**: MA5, MA10, MA20
- **成交量**: 量價關係分析
- **技術形態**: 支撐阻力位識別

#### 🔍 互動功能
- **縮放**: 滑鼠滾輪縮放圖表
- **拖拽**: 左鍵拖拽移動圖表
- **十字線**: 精確查看價格和時間

---

## 🔧 系統需求

### 最低需求
- **作業系統**: Windows 10 (64位元)
- **記憶體**: 4GB RAM
- **硬碟空間**: 2GB 可用空間
- **網路連線**: 需要網路連線獲取股票資料

### 建議配置
- **作業系統**: Windows 11 (64位元)
- **記憶體**: 8GB RAM 或以上
- **硬碟空間**: 5GB 可用空間
- **網路連線**: 寬頻網路連線

---

## 🛠️ 故障排除

### 程式無法啟動

#### 問題1: 防毒軟體阻擋
**解決方案**:
1. 將執行檔加入防毒軟體白名單
2. 暫時關閉即時防護
3. 以系統管理員身分執行

#### 問題2: 系統相容性
**解決方案**:
1. 確認 Windows 版本為 10/11
2. 安裝最新的 Visual C++ 可轉散發套件
3. 更新 Windows 系統

#### 問題3: 權限不足
**解決方案**:
1. 右鍵點擊執行檔
2. 選擇「以系統管理員身分執行」
3. 確認有寫入權限

### 資料無法更新

#### 問題1: 網路連線
**解決方案**:
1. 檢查網路連線狀態
2. 確認防火牆設定
3. 嘗試使用VPN

#### 問題2: 資料來源問題
**解決方案**:
1. 重新啟動程式
2. 等待一段時間後重試
3. 檢查資料來源網站狀態

### 功能異常

#### 問題1: 圖表顯示異常
**解決方案**:
1. 重新啟動程式
2. 檢查顯示卡驅動程式
3. 調整系統顯示設定

#### 問題2: 選股結果為空
**解決方案**:
1. 調整策略參數
2. 選擇不同的策略
3. 檢查資料更新狀態

---

## 📁 檔案結構

### 自動創建的檔案
程式首次執行時會自動創建以下檔案和目錄：

```
台股智能選股系統.exe          # 主執行檔
├── history/                  # 歷史資料目錄
│   └── tables/              # 資料庫檔案
├── logs/                    # 日誌檔案
├── reports/                 # 導出報告
└── cache/                   # 快取檔案
```

### 重要檔案說明
- **price.db**: 股價資料庫
- **pe_data.db**: 本益比資料庫
- **bargin_report.db**: 三大法人資料庫
- **daily_trading.db**: 日交易資料庫

---

## 💡 使用技巧

### 1. 提升選股效率
- **組合策略**: 同時使用多個策略交叉驗證
- **參數調整**: 根據市場狀況調整策略參數
- **定期更新**: 定期執行資料更新

### 2. 分析技巧
- **月營收評估**: 重點關注三大法人動向
- **技術分析**: 結合K線圖判斷進出場時機
- **基本面分析**: 注意財務指標的合理性

### 3. 風險控制
- **分散投資**: 不要集中投資單一股票
- **停損設定**: 設定合理的停損點
- **資金管理**: 控制單筆投資金額

---

## 📞 技術支援

### 常見問題
如遇到問題，請先參考本說明文檔的故障排除章節。

### 功能建議
如有功能改進建議，歡迎提供回饋。

### 版本更新
定期檢查是否有新版本發布。

---

## 📝 版本歷史

### v21.0 (2025-07-30)
- ✅ 編譯為獨立執行檔
- ✅ 修復財務指標顯示問題
- ✅ 移除投資建議區塊，優化三大法人顯示
- ✅ 補齊所有未知股票名稱
- ✅ 優化使用者介面和體驗

### 主要改進
- **效能優化**: 提升程式執行速度
- **穩定性**: 修復多項已知問題
- **易用性**: 簡化操作流程
- **功能完整**: 保留所有核心功能

---

## ⚠️ 重要提醒

### 投資風險警告
- 本系統僅供參考，不構成投資建議
- 投資有風險，請謹慎評估自身風險承受能力
- 過去績效不代表未來表現

### 資料準確性
- 資料來源為公開資訊，力求準確但不保證完全無誤
- 建議搭配其他資訊來源進行驗證
- 重要決策前請查證官方資料

---

**🎉 感謝使用台股智能選股系統！祝您投資順利！**
