#!/usr/bin/env python3
"""
測試爬蟲函數修復
"""

def test_crawler_functions():
    """測試爬蟲函數訪問方式"""
    print("=== 測試爬蟲函數訪問方式 ===\n")
    
    # 模擬簡化版爬蟲函數字典
    def simple_crawler(*args, **kwargs):
        import pandas as pd
        return pd.DataFrame({'message': ['簡化版功能測試成功']})
    
    crawler_funcs = {
        'crawl_price': simple_crawler,
        'crawl_bargin': simple_crawler,
        'crawl_pe': simple_crawler,
        'crawl_monthly_report': simple_crawler,
    }
    
    # 測試檔案映射
    crawler_map = {
        'price.pkl': 'crawl_price',
        'bargin_report.pkl': 'crawl_bargin',
        'pe.pkl': 'crawl_pe',
        'monthly_report.pkl': 'crawl_monthly_report',
    }
    
    print("1. 測試舊方式（hasattr + getattr）：")
    for filename, func_name in crawler_map.items():
        try:
            # 舊方式：hasattr + getattr（會失敗）
            if hasattr(crawler_funcs, func_name):
                crawler_func = getattr(crawler_funcs, func_name)
                print(f"✓ {filename:20} -> {func_name} (舊方式成功)")
            else:
                print(f"✗ {filename:20} -> {func_name} (舊方式失敗 - hasattr)")
        except Exception as e:
            print(f"✗ {filename:20} -> {func_name} (舊方式錯誤: {e})")
    
    print("\n2. 測試新方式（字典訪問）：")
    for filename, func_name in crawler_map.items():
        try:
            # 新方式：字典訪問（會成功）
            if func_name in crawler_funcs:
                crawler_func = crawler_funcs[func_name]
                result = crawler_func()
                print(f"✓ {filename:20} -> {func_name} (新方式成功)")
            else:
                print(f"✗ {filename:20} -> {func_name} (新方式失敗 - 不在字典中)")
        except Exception as e:
            print(f"✗ {filename:20} -> {func_name} (新方式錯誤: {e})")
    
    print("\n3. 解釋差異：")
    print("舊方式：hasattr(crawler_funcs, 'crawl_pe')")
    print("  - crawler_funcs 是字典，不是物件")
    print("  - hasattr 檢查物件屬性，不適用於字典")
    print("  - 結果：False（找不到屬性）")
    
    print("\n新方式：'crawl_pe' in crawler_funcs")
    print("  - 直接檢查字典中是否有該key")
    print("  - 適用於字典結構")
    print("  - 結果：True（找到key）")

def test_pe_specific():
    """專門測試pe.pkl的修復"""
    print("\n=== 專門測試 pe.pkl 修復 ===\n")
    
    # 模擬簡化版爬蟲
    def simple_pe_crawler(*args, **kwargs):
        import pandas as pd
        from datetime import datetime, timedelta
        
        # 創建假的pe資料
        dates = pd.date_range(start='2025-07-14', end='2025-07-20', freq='D')
        pe_data = pd.DataFrame({
            'pe_ratio': [15.5, 16.2, 15.8, 16.0, 15.9, 16.1, 15.7],
            'stock_id': ['2330'] * 7
        }, index=dates)
        
        return pe_data
    
    crawler_funcs = {
        'crawl_pe': simple_pe_crawler
    }
    
    filename = 'pe.pkl'
    func_name = 'crawl_pe'
    
    print(f"測試檔案：{filename}")
    print(f"對應函數：{func_name}")
    
    # 測試新的訪問方式
    if func_name in crawler_funcs:
        print("✓ 函數存在於字典中")
        
        try:
            crawler_func = crawler_funcs[func_name]
            print("✓ 成功獲取函數引用")
            
            result = crawler_func('2025-07-14', '2025-07-20')
            print(f"✓ 函數執行成功，獲取 {len(result)} 筆資料")
            print(f"  資料範圍：{result.index.min()} 至 {result.index.max()}")
            
        except Exception as e:
            print(f"✗ 函數執行失敗：{e}")
    else:
        print("✗ 函數不存在於字典中")

if __name__ == "__main__":
    test_crawler_functions()
    test_pe_specific()
    
    print("\n=== 修復總結 ===")
    print("問題：✗ 找不到對應的爬蟲函數: crawl_pe")
    print("原因：使用 hasattr() 檢查字典，應該使用 'key' in dict")
    print("修復：將 hasattr(self.crawler_funcs, func_name) 改為 func_name in self.crawler_funcs")
    print("結果：✓ 現在可以正確找到並執行爬蟲函數")
    
    print("\n現在請重新測試 pe.pkl 更新功能！")
