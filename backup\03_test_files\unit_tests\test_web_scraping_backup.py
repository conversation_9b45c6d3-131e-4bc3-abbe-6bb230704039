#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試網頁爬取備援功能
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_original_web_scraping():
    """測試原始的網頁爬取方法"""
    print("🌐 測試原始網頁爬取方法")
    print("=" * 50)
    
    import urllib.request
    import re
    
    def get_stock_index(url, symbol):
        try:
            # 發送請求獲取網頁內容
            with urllib.request.urlopen(url) as response:
                html = response.read().decode('utf-8')
            
            # 提取當前值 (regularMarketPrice)
            price_pattern = r'data-field="regularMarketPrice"[^>]*>([^<]+)<'
            price_match = re.search(price_pattern, html)
            price = price_match.group(1) if price_match else 'Not found'
            
            # 提取變化值 (regularMarketChange)
            change_pattern = r'data-field="regularMarketChange"[^>]*><span[^>]*>([^<]+)<'
            change_match = re.search(change_pattern, html)
            change = change_match.group(1) if change_match else 'Not found'
            
            # 提取變化百分比 (regularMarketChangePercent)
            percent_pattern = r'data-field="regularMarketChangePercent"[^>]*>([^<]+)<'
            percent_match = re.search(percent_pattern, html)
            percent = percent_match.group(1) if percent_match else 'Not found'
            
            return {
                'price': price,
                'change': change,
                'percent_change': percent
            }
        except Exception as e:
            return {'error': str(e)}
    
    # 測試三大指數
    indices = [
        ('道瓊工業平均指數 (DJI)', 'https://finance.yahoo.com/quote/%5EDJI', '^DJI'),
        ('S&P 500 (SPX)', 'https://finance.yahoo.com/quote/%5EGSPC', '^GSPC'),
        ('納斯達克綜合指數 (IXIC)', 'https://finance.yahoo.com/quote/%5EIXIC', '^IXIC')
    ]
    
    results = []
    for name, url, symbol in indices:
        print(f"\n📈 測試 {name}...")
        start_time = time.time()
        
        result = get_stock_index(url, symbol)
        elapsed = time.time() - start_time
        
        if 'error' not in result:
            print(f"✅ 成功 ({elapsed:.1f}秒):")
            print(f"  當前值: {result['price']}")
            print(f"  變化值: {result['change']}")
            print(f"  變化百分比: {result['percent_change']}")
            results.append((name, True, result))
        else:
            print(f"❌ 失敗 ({elapsed:.1f}秒): {result['error']}")
            results.append((name, False, result))
    
    return results

def test_integrated_web_scraping():
    """測試整合後的網頁爬取功能"""
    print("\n🔧 測試整合後的網頁爬取功能")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試單個網頁爬取方法
        symbols = ['^DJI', '^GSPC', '^IXIC']
        names = ['Dow Jones', 'S&P500', 'Nasdaq']
        
        results = []
        for symbol, name in zip(symbols, names):
            print(f"\n📈 測試網頁爬取 {name} ({symbol})...")
            start_time = time.time()
            
            web_data = fetcher._get_stock_index_from_web(symbol)
            elapsed = time.time() - start_time
            
            if web_data:
                price = web_data['price']
                change_pct = web_data['change_pct']
                print(f"✅ 成功 ({elapsed:.1f}秒): {price} ({change_pct:+.2f}%)")
                results.append((name, True, web_data))
            else:
                print(f"❌ 失敗 ({elapsed:.1f}秒)")
                results.append((name, False, None))
        
        return results
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return []
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return []

def test_full_us_indices_with_backup():
    """測試完整的美股指數獲取（包含備援）"""
    print("\n🚀 測試完整美股指數獲取（yfinance + 網頁備援）")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試完整的美股指數獲取
        print("\n📊 執行完整美股指數獲取...")
        start_time = time.time()
        
        us_data = fetcher.get_us_indices_real()
        elapsed = time.time() - start_time
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({elapsed:.1f}秒):")
            
            success_count = 0
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
                success_count += 1
            
            print(f"\n📊 成功獲取: {success_count}/{len(us_data)} 項")
            return True
        else:
            print(f"❌ 美股指數獲取失敗 ({elapsed:.1f}秒)")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def compare_methods():
    """比較不同方法的效果"""
    print("\n📊 比較不同獲取方法")
    print("=" * 50)
    
    # 測試原始方法
    print("1️⃣ 原始網頁爬取方法:")
    original_results = test_original_web_scraping()
    original_success = sum(1 for _, success, _ in original_results if success)
    
    # 測試整合方法
    print("\n2️⃣ 整合網頁爬取方法:")
    integrated_results = test_integrated_web_scraping()
    integrated_success = sum(1 for _, success, _ in integrated_results if success)
    
    # 測試完整方法
    print("\n3️⃣ 完整備援方法:")
    full_success = test_full_us_indices_with_backup()
    
    # 總結比較
    print("\n" + "=" * 50)
    print("📋 方法比較總結:")
    print(f"• 原始網頁爬取: {original_success}/3 成功")
    print(f"• 整合網頁爬取: {integrated_success}/3 成功")
    print(f"• 完整備援方法: {'✅ 成功' if full_success else '❌ 失敗'}")
    
    return {
        'original': original_success,
        'integrated': integrated_success,
        'full': full_success
    }

def main():
    """主函數"""
    print("🚀 網頁爬取備援功能測試")
    print("=" * 60)
    
    try:
        # 執行比較測試
        results = compare_methods()
        
        print("\n" + "=" * 60)
        print("🎯 測試總結:")
        
        print("\n🔧 技術改善:")
        print("• 新增網頁爬取備援方案")
        print("• 使用正則表達式解析Yahoo Finance頁面")
        print("• 整合到現有的安全請求機制")
        print("• 自動在yfinance失敗時切換到網頁爬取")
        
        print("\n💡 備援策略:")
        print("• 第一優先：yfinance API (ticker.info)")
        print("• 第二備援：yfinance API (ticker.history)")
        print("• 第三備援：直接網頁爬取Yahoo Finance")
        
        print("\n📈 預期效果:")
        print("• 提高數據獲取成功率")
        print("• 減少因API限制導致的失敗")
        print("• 提供多重數據源保障")
        print("• 保持數據的即時性和準確性")
        
        # 判斷整體成功
        if results['full'] or results['integrated'] >= 2:
            print("\n🎉 網頁爬取備援功能測試成功！")
            print("現在系統具備了更強的數據獲取能力。")
        else:
            print("\n⚠️ 需要進一步調整網頁爬取策略")
            
    except Exception as e:
        print(f"\n❌ 測試過程發生錯誤: {e}")

if __name__ == "__main__":
    main()
