#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台股智能選股系統 - 帶進度條啟動器
"""

import sys
import os
import time
import threading
import subprocess
from PyQt6.QtWidgets import (QApplication, QSplashScreen, QProgressBar, 
                            QVBoxLayout, QLabel, QWidget, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QThread
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush

class LoadingWorker(QThread):
    """載入工作線程"""
    progress_updated = pyqtSignal(int, str)
    loading_completed = pyqtSignal()
    loading_failed = pyqtSignal(str)
    
    def __init__(self, exe_path):
        super().__init__()
        self.exe_path = exe_path
        self.process = None
    
    def run(self):
        """執行載入過程"""
        try:
            # 模擬載入步驟
            steps = [
                (10, "正在檢查系統環境..."),
                (20, "正在載入核心模組..."),
                (30, "正在初始化資料庫..."),
                (40, "正在載入股票資料..."),
                (50, "正在初始化策略引擎..."),
                (60, "正在載入圖表組件..."),
                (70, "正在準備用戶界面..."),
                (80, "正在連接資料源..."),
                (90, "正在完成最後設定..."),
                (95, "正在啟動主程式..."),
            ]
            
            for progress, message in steps:
                if self.isInterruptionRequested():
                    return
                self.progress_updated.emit(progress, message)
                self.msleep(300)  # 300ms 延遲
            
            # 啟動實際程式
            self.progress_updated.emit(98, "正在啟動主程式...")
            
            # 使用 subprocess 啟動主程式
            try:
                self.process = subprocess.Popen([self.exe_path], 
                                              creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == 'win32' else 0)
                
                self.progress_updated.emit(100, "啟動完成！")
                self.msleep(500)
                self.loading_completed.emit()
                
            except Exception as e:
                self.loading_failed.emit(f"啟動主程式失敗: {str(e)}")
                
        except Exception as e:
            self.loading_failed.emit(f"載入過程發生錯誤: {str(e)}")

class CustomSplashScreen(QSplashScreen):
    """自定義啟動畫面"""
    
    def __init__(self):
        # 創建啟動畫面
        pixmap = QPixmap(700, 450)
        pixmap.fill(QColor(30, 30, 30))
        
        # 繪製內容
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 背景漸層
        gradient = QBrush(QColor(45, 45, 45))
        painter.fillRect(pixmap.rect(), gradient)
        
        # 標題
        painter.setPen(QColor(255, 255, 255))
        title_font = QFont("Microsoft YaHei", 28, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.drawText(50, 120, "🚀 台股智能選股系統")
        
        # 版本信息
        version_font = QFont("Microsoft YaHei", 14)
        painter.setFont(version_font)
        painter.setPen(QColor(200, 200, 200))
        painter.drawText(50, 160, "版本 v22.0 - 優化增強版 (修復版)")
        
        # 功能介紹
        feature_font = QFont("Microsoft YaHei", 11)
        painter.setFont(feature_font)
        painter.setPen(QColor(180, 180, 180))
        
        features = [
            "✓ 多策略智能選股",
            "✓ 實時K線圖表分析", 
            "✓ 開盤前市場監控",
            "✓ 技術指標分析",
            "✓ Excel報告導出"
        ]
        
        for i, feature in enumerate(features):
            painter.drawText(50, 200 + i * 25, feature)
        
        # 載入提示
        painter.setPen(QColor(100, 200, 100))
        painter.drawText(50, 340, "正在載入系統組件，請稍候...")
        painter.drawText(50, 365, "首次啟動可能需要較長時間，感謝您的耐心等待")
        
        painter.end()
        
        super().__init__(pixmap)
        
        # 設置進度條
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(50, 390, 600, 30)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 8px;
                text-align: center;
                background-color: #333;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 6px;
            }
        """)
        
        # 狀態標籤
        self.status_label = QLabel("正在初始化...", self)
        self.status_label.setGeometry(50, 350, 600, 30)
        self.status_label.setStyleSheet("""
            color: #4CAF50; 
            font-size: 13px; 
            font-weight: bold;
            background: transparent;
        """)
        
    def update_progress(self, value, message):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.repaint()
        QApplication.processEvents()

class ProgressLauncher:
    """進度啟動器"""
    
    def __init__(self, exe_path):
        self.exe_path = exe_path
        self.splash = None
        self.worker = None
        
    def launch(self):
        """啟動程式"""
        app = QApplication(sys.argv)
        app.setApplicationName("台股智能選股系統啟動器")
        
        # 檢查主程式是否存在
        if not os.path.exists(self.exe_path):
            QMessageBox.critical(None, "錯誤", 
                f"找不到主程式文件:\n{self.exe_path}\n\n請確認文件是否存在。")
            return
        
        # 顯示啟動畫面
        self.splash = CustomSplashScreen()
        self.splash.show()
        
        # 創建載入工作線程
        self.worker = LoadingWorker(self.exe_path)
        self.worker.progress_updated.connect(self.splash.update_progress)
        self.worker.loading_completed.connect(self.on_loading_completed)
        self.worker.loading_failed.connect(self.on_loading_failed)
        
        # 開始載入
        self.worker.start()
        
        # 運行應用程式
        app.exec()
    
    def on_loading_completed(self):
        """載入完成"""
        if self.splash:
            # 延遲關閉啟動畫面
            QTimer.singleShot(1000, self.splash.close)
        
        # 退出啟動器
        QTimer.singleShot(1500, QApplication.quit)
    
    def on_loading_failed(self, error_message):
        """載入失敗"""
        if self.splash:
            self.splash.close()
        
        QMessageBox.critical(None, "啟動失敗", 
            f"系統啟動失敗:\n{error_message}\n\n請檢查系統環境或聯繫技術支援。")
        QApplication.quit()

def main():
    """主函數"""
    # 確定主程式路徑
    exe_path = os.path.join(os.path.dirname(__file__), "台股智能選股系統_修復版.exe")
    
    # 創建並啟動進度啟動器
    launcher = ProgressLauncher(exe_path)
    launcher.launch()

if __name__ == "__main__":
    main()
