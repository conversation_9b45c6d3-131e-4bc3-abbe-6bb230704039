#!/usr/bin/env python3
"""
測試回測選單整合功能
驗證主程式中新增的回測選單是否正常工作
"""

import sys
import os
import subprocess
from datetime import datetime

def test_file_existence():
    """測試相關文件是否存在"""
    print("🔍 檢查回測相關文件...")
    
    required_files = [
        'O3mh_gui_v21_optimized.py',  # 主程式
        'ai_technical_backtest.py',   # AI技術指標回測
        'multi_strategy_gui.py',      # 多策略整合GUI
        'multi_strategy_chart.py',    # 多策略圖表
        'signal_strength_analyzer.py', # 信號強度分析器
        'test_signal_strength_display.py', # 信號強度測試
        'ashui_backtest_gui.py'       # 阿水一式回測GUI (可選)
    ]
    
    results = {}
    for file in required_files:
        exists = os.path.exists(file)
        results[file] = exists
        status = "✅" if exists else "❌"
        print(f"   {status} {file}")
    
    return results

def test_import_modules():
    """測試模組導入"""
    print("\n📦 測試模組導入...")
    
    modules_to_test = [
        ('multi_strategy_gui', 'MultiStrategyGUI'),
        ('multi_strategy_chart', 'MultiStrategySignalAnalyzer'),
        ('signal_strength_analyzer', 'signal_analyzer'),
    ]
    
    results = {}
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, class_name):
                print(f"   ✅ {module_name}.{class_name}")
                results[module_name] = True
            else:
                print(f"   ❌ {module_name}.{class_name} (類別不存在)")
                results[module_name] = False
        except ImportError as e:
            print(f"   ❌ {module_name} (導入失敗: {e})")
            results[module_name] = False
        except Exception as e:
            print(f"   ⚠️ {module_name} (其他錯誤: {e})")
            results[module_name] = False
    
    return results

def test_main_gui_menu_structure():
    """測試主GUI選單結構"""
    print("\n🖥️ 檢查主GUI選單結構...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查回測選單相關代碼
        menu_checks = [
            ("回測選單創建", "backtest_menu = menubar.addMenu('📊 回測')"),
            ("阿水一式回測", "ashui_backtest_action"),
            ("AI技術指標回測", "ai_backtest_action"),
            ("多策略整合分析", "multi_strategy_action"),
            ("策略解讀指南", "strategy_guide_action"),
            ("信號強度測試", "signal_test_action"),
            ("阿水一式回測方法", "def open_ashui_backtest_window"),
            ("AI技術指標回測方法", "def open_ai_technical_backtest"),
            ("多策略整合方法", "def open_multi_strategy_analysis"),
            ("策略解讀指南方法", "def open_strategy_interpretation_guide"),
            ("信號強度測試方法", "def open_signal_strength_test")
        ]
        
        results = {}
        for check_name, search_text in menu_checks:
            found = search_text in content
            results[check_name] = found
            status = "✅" if found else "❌"
            print(f"   {status} {check_name}")
        
        return results
        
    except Exception as e:
        print(f"   ❌ 檢查主GUI失敗: {e}")
        return {}

def test_backtest_tools_functionality():
    """測試回測工具功能"""
    print("\n🧪 測試回測工具功能...")
    
    tests = []
    
    # 測試AI技術指標回測
    print("   🤖 測試AI技術指標回測...")
    try:
        # 檢查是否可以導入
        import ai_technical_backtest
        print("      ✅ AI技術指標回測模組導入成功")
        tests.append(("AI技術指標回測導入", True))
    except Exception as e:
        print(f"      ❌ AI技術指標回測模組導入失敗: {e}")
        tests.append(("AI技術指標回測導入", False))
    
    # 測試多策略整合
    print("   🎨 測試多策略整合...")
    try:
        from multi_strategy_gui import MultiStrategyGUI
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        print("      ✅ 多策略整合模組導入成功")
        tests.append(("多策略整合導入", True))
    except Exception as e:
        print(f"      ❌ 多策略整合模組導入失敗: {e}")
        tests.append(("多策略整合導入", False))
    
    # 測試信號強度分析器
    print("   🔍 測試信號強度分析器...")
    try:
        from signal_strength_analyzer import signal_analyzer
        print("      ✅ 信號強度分析器導入成功")
        tests.append(("信號強度分析器導入", True))
    except Exception as e:
        print(f"      ❌ 信號強度分析器導入失敗: {e}")
        tests.append(("信號強度分析器導入", False))
    
    return tests

def test_menu_integration_completeness():
    """測試選單整合完整性"""
    print("\n📋 檢查選單整合完整性...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否移除了工具選單中的重複項目
        old_backtest_in_tools = "tools_menu.addAction('📊 阿水一式回測分析')" in content
        
        # 檢查新的回測選單是否存在
        new_backtest_menu = "menubar.addMenu('📊 回測')" in content
        
        # 檢查所有回測功能是否都在新選單中
        backtest_functions = [
            "ashui_backtest_action = backtest_menu.addAction",
            "ai_backtest_action = backtest_menu.addAction", 
            "multi_strategy_action = backtest_menu.addAction",
            "strategy_guide_action = backtest_menu.addAction",
            "signal_test_action = backtest_menu.addAction"
        ]
        
        all_functions_present = all(func in content for func in backtest_functions)
        
        print(f"   {'✅' if not old_backtest_in_tools else '❌'} 已移除工具選單中的重複回測項目")
        print(f"   {'✅' if new_backtest_menu else '❌'} 新回測選單已創建")
        print(f"   {'✅' if all_functions_present else '❌'} 所有回測功能已整合到新選單")
        
        return {
            "移除重複項目": not old_backtest_in_tools,
            "新選單創建": new_backtest_menu,
            "功能完整整合": all_functions_present
        }
        
    except Exception as e:
        print(f"   ❌ 檢查選單整合失敗: {e}")
        return {}

def demonstrate_backtest_menu_features():
    """展示回測選單功能特色"""
    print("\n💡 回測選單功能特色:")
    print("=" * 60)
    
    features = [
        ("📊 頂層回測選單", "獨立的回測功能選單，不再隱藏在工具選單中"),
        ("🎯 阿水一式回測分析", "專業的阿水一式策略回測系統"),
        ("🤖 AI技術指標回測", "四種技術指標策略的智能回測和解讀"),
        ("🎨 多策略整合分析", "四策略同圖顯示，信號一致性分析"),
        ("📖 策略解讀指南", "詳細的技術指標信號解讀說明"),
        ("🧪 信號強度測試", "測試和驗證信號強度提示功能"),
        ("🔗 無縫整合", "與主程式完美整合，共享數據庫連接"),
        ("💡 智能提示", "每個功能都有詳細的使用說明和功能介紹")
    ]
    
    for title, description in features:
        print(f"{title}: {description}")
    
    print("\n🎨 選單結構:")
    menu_structure = """
    📊 回測
    ├── 🎯 阿水一式回測分析
    ├── 🤖 AI技術指標回測  
    ├── 🎨 多策略整合分析
    ├── ──────────────
    ├── 📖 策略解讀指南
    └── 🧪 信號強度測試
    """
    print(menu_structure)
    
    print("\n💡 使用建議:")
    tips = [
        "從「AI技術指標回測」開始，熟悉基本的技術指標策略",
        "使用「多策略整合分析」查看四種策略的綜合信號",
        "參考「策略解讀指南」理解各種信號強度的含義",
        "運行「信號強度測試」驗證功能是否正常工作",
        "最後使用「阿水一式回測分析」進行專業策略回測"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"   {i}. {tip}")

def generate_integration_report():
    """生成整合報告"""
    print("\n📊 生成整合報告...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"backtest_menu_integration_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("回測選單整合報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("📋 整合內容:\n")
            f.write("• 在主程式頂層添加「📊 回測」選單\n")
            f.write("• 整合五個回測相關功能\n")
            f.write("• 移除工具選單中的重複項目\n")
            f.write("• 添加詳細的功能說明和使用指南\n\n")
            
            f.write("🎯 功能列表:\n")
            f.write("1. 🎯 阿水一式回測分析\n")
            f.write("2. 🤖 AI技術指標回測\n")
            f.write("3. 🎨 多策略整合分析\n")
            f.write("4. 📖 策略解讀指南\n")
            f.write("5. 🧪 信號強度測試\n\n")
            
            f.write("💡 使用方式:\n")
            f.write("1. 啟動主程式: python O3mh_gui_v21_optimized.py\n")
            f.write("2. 點擊頂部選單欄的「📊 回測」\n")
            f.write("3. 選擇需要的回測功能\n")
            f.write("4. 按照提示使用各項功能\n\n")
            
            f.write("🔧 技術實現:\n")
            f.write("• 修改 init_menu_bar() 方法添加回測選單\n")
            f.write("• 添加對應的選單處理方法\n")
            f.write("• 整合現有的回測工具和新功能\n")
            f.write("• 提供完整的錯誤處理和用戶提示\n")
        
        print(f"   ✅ 報告已生成: {report_filename}")
        return report_filename
        
    except Exception as e:
        print(f"   ❌ 生成報告失敗: {e}")
        return None

def main():
    """主函數"""
    print("🚀 回測選單整合測試程式")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("文件存在性檢查", test_file_existence),
        ("模組導入測試", test_import_modules),
        ("主GUI選單結構檢查", test_main_gui_menu_structure),
        ("回測工具功能測試", test_backtest_tools_functionality),
        ("選單整合完整性檢查", test_menu_integration_completeness)
    ]
    
    all_results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            all_results[test_name] = result
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            all_results[test_name] = {}
    
    # 展示功能特色
    demonstrate_backtest_menu_features()
    
    # 生成報告
    report_file = generate_integration_report()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    total_checks = 0
    passed_checks = 0
    
    for test_name, results in all_results.items():
        if isinstance(results, dict):
            test_total = len(results)
            test_passed = sum(1 for v in results.values() if v)
            total_checks += test_total
            passed_checks += test_passed
            print(f"   {test_name}: {test_passed}/{test_total} 通過")
        elif isinstance(results, list):
            test_total = len(results)
            test_passed = sum(1 for _, v in results if v)
            total_checks += test_total
            passed_checks += test_passed
            print(f"   {test_name}: {test_passed}/{test_total} 通過")
    
    print(f"\n🎯 總體結果: {passed_checks}/{total_checks} 檢查通過")
    
    if passed_checks == total_checks:
        print("🎉 所有檢查通過！回測選單整合成功")
        print("\n🚀 啟動方式:")
        print("   python O3mh_gui_v21_optimized.py")
        print("   然後點擊選單欄的「📊 回測」")
    else:
        print("⚠️ 部分檢查未通過，請檢查相關功能")
    
    if report_file:
        print(f"\n📊 詳細報告已保存至: {report_file}")

if __name__ == '__main__':
    main()
