#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷 monthly_report 執行卡住的問題
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'finlab'))

def test_monthly_report_steps():
    """逐步測試 monthly_report 的各個階段"""
    print("=" * 80)
    print("🔍 診斷 monthly_report 執行卡住問題")
    print("=" * 80)
    
    try:
        from crawler import crawl_monthly_report, month_revenue
        
        test_date = datetime(2025, 7, 1)  # 使用一個測試日期
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 階段1: 測試上市月營收爬取
        print(f"\n1️⃣ 測試上市月營收爬取...")
        start_time = time.time()
        
        def timeout_handler():
            time.sleep(60)  # 60秒超時
            print(f"\n⏰ 上市月營收爬取超時 (>60秒)")
            return None
        
        timeout_thread = threading.Thread(target=timeout_handler)
        timeout_thread.daemon = True
        timeout_thread.start()
        
        try:
            dftwe = month_revenue('sii', test_date)
            elapsed = time.time() - start_time
            print(f"   ✅ 上市月營收完成: {len(dftwe)} 筆資料 ({elapsed:.1f}秒)")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ❌ 上市月營收失敗: {e} ({elapsed:.1f}秒)")
            dftwe = None
        
        # 階段2: 測試上櫃月營收爬取
        print(f"\n2️⃣ 測試上櫃月營收爬取...")
        start_time = time.time()
        
        try:
            dfotc = month_revenue('otc', test_date)
            elapsed = time.time() - start_time
            print(f"   ✅ 上櫃月營收完成: {len(dfotc)} 筆資料 ({elapsed:.1f}秒)")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ❌ 上櫃月營收失敗: {e} ({elapsed:.1f}秒)")
            dfotc = None
        
        # 階段3: 測試合併操作
        if dftwe is not None and dfotc is not None and len(dftwe) > 0 and len(dfotc) > 0:
            print(f"\n3️⃣ 測試資料合併...")
            start_time = time.time()
            
            try:
                from crawler import merge, o2tm
                result = merge(dftwe, dfotc, o2tm)
                elapsed = time.time() - start_time
                print(f"   ✅ 資料合併完成: {len(result)} 筆資料 ({elapsed:.1f}秒)")
                
                # 檢查結果
                if len(result) > 0:
                    print(f"   📊 合併結果:")
                    print(f"      索引: {result.index.names}")
                    print(f"      欄位: {list(result.columns)}")
                    print(f"      形狀: {result.shape}")
                    
                    # 顯示前幾筆資料
                    print(f"   📋 前3筆資料:")
                    print(result.head(3))
                
            except Exception as e:
                elapsed = time.time() - start_time
                print(f"   ❌ 資料合併失敗: {e} ({elapsed:.1f}秒)")
                import traceback
                traceback.print_exc()
        
        # 階段4: 測試完整的 crawl_monthly_report
        print(f"\n4️⃣ 測試完整的 crawl_monthly_report...")
        start_time = time.time()
        
        def monitor_progress():
            """監控進度"""
            for i in range(120):  # 監控2分鐘
                time.sleep(1)
                if i % 10 == 0:
                    print(f"   ⏱️ 執行中... ({i}秒)")
        
        monitor_thread = threading.Thread(target=monitor_progress)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            result = crawl_monthly_report(test_date)
            elapsed = time.time() - start_time
            print(f"\n   ✅ 完整爬蟲完成: {len(result)} 筆資料 ({elapsed:.1f}秒)")
            
            if len(result) > 0:
                print(f"   📊 最終結果:")
                print(f"      索引: {result.index.names}")
                print(f"      欄位: {list(result.columns)}")
                print(f"      形狀: {result.shape}")
        
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"\n   ❌ 完整爬蟲失敗: {e} ({elapsed:.1f}秒)")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 診斷過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def test_preprocess_performance():
    """測試 preprocess 函數的效能"""
    print(f"\n" + "=" * 80)
    print("🔧 測試 preprocess 函數效能")
    print("=" * 80)
    
    try:
        import pandas as pd
        from crawler import preprocess
        
        # 創建測試資料
        test_data = {
            'stock_id': [f'{1000+i} 測試股票{i}' for i in range(1000)],
            '當月營收': [f'{1000000+i*1000:,}' for i in range(1000)],
            '上月營收': [f'{900000+i*900:,}' for i in range(1000)],
            '去年當月營收': [f'{800000+i*800:,}' for i in range(1000)]
        }
        
        df = pd.DataFrame(test_data)
        df = df.set_index('stock_id')
        
        print(f"📊 測試資料: {df.shape}")
        print(f"   前3筆資料:")
        print(df.head(3))
        
        # 測試 preprocess 函數
        test_date = datetime(2025, 7, 1)
        
        print(f"\n🔄 執行 preprocess...")
        start_time = time.time()
        
        try:
            result = preprocess(df, test_date)
            elapsed = time.time() - start_time
            print(f"✅ preprocess 完成: {result.shape} ({elapsed:.1f}秒)")
            
            if len(result) > 0:
                print(f"   📊 處理結果:")
                print(f"      索引: {result.index.names}")
                print(f"      欄位: {list(result.columns)}")
                print(f"   📋 前3筆結果:")
                print(result.head(3))
        
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ preprocess 失敗: {e} ({elapsed:.1f}秒)")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 效能測試失敗: {e}")
        import traceback
        traceback.print_exc()

def check_system_resources():
    """檢查系統資源使用情況"""
    print(f"\n" + "=" * 80)
    print("💻 檢查系統資源")
    print("=" * 80)
    
    try:
        import psutil
        
        # CPU 使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"🖥️ CPU 使用率: {cpu_percent}%")
        
        # 記憶體使用情況
        memory = psutil.virtual_memory()
        print(f"💾 記憶體使用率: {memory.percent}%")
        print(f"   總記憶體: {memory.total / 1024**3:.1f} GB")
        print(f"   可用記憶體: {memory.available / 1024**3:.1f} GB")
        
        # 磁碟使用情況
        disk = psutil.disk_usage('D:/')
        print(f"💿 D: 磁碟使用率: {disk.percent}%")
        print(f"   總空間: {disk.total / 1024**3:.1f} GB")
        print(f"   可用空間: {disk.free / 1024**3:.1f} GB")
        
    except ImportError:
        print("⚠️ psutil 模組未安裝，無法檢查系統資源")
    except Exception as e:
        print(f"❌ 系統資源檢查失敗: {e}")

def main():
    """主函數"""
    print("🚀 開始診斷 monthly_report 卡住問題")
    
    # 檢查系統資源
    check_system_resources()
    
    # 測試 preprocess 效能
    test_preprocess_performance()
    
    # 逐步測試 monthly_report
    test_monthly_report_steps()
    
    print(f"\n" + "=" * 80)
    print("📋 診斷建議")
    print("=" * 80)
    print("如果發現以下情況，可能是卡住的原因：")
    print("1. preprocess 函數執行時間過長 (>30秒)")
    print("2. 上市或上櫃月營收爬取超時")
    print("3. 資料合併操作耗時過長")
    print("4. 系統記憶體不足")
    print("5. 網路連接不穩定")
    
    print(f"\n💡 解決方案：")
    print("1. 優化 preprocess 函數中的字串處理邏輯")
    print("2. 增加超時控制和進度顯示")
    print("3. 分批處理大量資料")
    print("4. 增加記憶體或優化資料結構")

if __name__ == "__main__":
    main()
