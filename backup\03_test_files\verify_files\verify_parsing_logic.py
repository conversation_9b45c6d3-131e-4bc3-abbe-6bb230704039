#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證解析邏輯是否正確
"""

import pandas as pd
import sqlite3

def verify_parsing_logic():
    """驗證解析邏輯"""
    
    print("🔍 驗證我的解析邏輯是否正確")
    print("=" * 60)
    
    # 讀取官方數據
    official_df = pd.read_csv("D:/Finlab/backup/O3mh_strategy2AA/official_revenue_2024_06.csv", encoding='utf-8-sig')
    print(f"📊 官方數據: {len(official_df)} 筆")
    
    # 讀取我的資料庫數據
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 查詢我的上市數據
        query = """
            SELECT stock_id, stock_name, revenue, revenue_mom, revenue_yoy, 
                   cumulative_revenue, cumulative_revenue_yoy, remark
            FROM monthly_revenue 
            WHERE year = 2024 AND month = 6 AND market = '上市'
            ORDER BY stock_id
        """
        
        my_df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f"📊 我的數據: {len(my_df)} 筆")
        
        # 比較前10筆數據
        print(f"\n🔍 詳細比較前10筆數據:")
        print("-" * 100)
        
        for i in range(min(10, len(official_df), len(my_df))):
            official = official_df.iloc[i]
            my_data = my_df.iloc[i]
            
            print(f"\n第{i+1}筆 - {official['股票代號']} {official['股票名稱']}:")
            
            # 營收比較
            official_revenue = str(official['當月營收(千元)']).replace(',', '')
            my_revenue = str(int(my_data['revenue']))
            revenue_match = official_revenue == my_revenue
            
            print(f"  營收: 官方={official['當月營收(千元)']} | 我的={my_data['revenue']:,.0f} | {'✅' if revenue_match else '❌'}")
            
            # 月增率比較
            official_mom = str(official['營收月增率(%)'])
            my_mom = str(my_data['revenue_mom']) if my_data['revenue_mom'] is not None else 'None'
            mom_match = official_mom == my_mom
            
            print(f"  月增率: 官方={official['營收月增率(%)']}% | 我的={my_mom}% | {'✅' if mom_match else '❌'}")
            
            # 年增率比較
            official_yoy = str(official['營收年增率(%)'])
            my_yoy = str(my_data['revenue_yoy']) if my_data['revenue_yoy'] is not None else 'None'
            yoy_match = official_yoy == my_yoy
            
            print(f"  年增率: 官方={official['營收年增率(%)']}% | 我的={my_yoy}% | {'✅' if yoy_match else '❌'}")
            
            # 累計營收比較
            official_cum = str(official['累計營收(千元)']).replace(',', '')
            my_cum = str(int(my_data['cumulative_revenue'])) if my_data['cumulative_revenue'] else '0'
            cum_match = official_cum == my_cum
            
            print(f"  累計營收: 官方={official['累計營收(千元)']} | 我的={my_data['cumulative_revenue']:,.0f} | {'✅' if cum_match else '❌'}")
            
            # 累計年增率比較
            official_cum_yoy = str(official['累計年增率(%)'])
            my_cum_yoy = str(my_data['cumulative_revenue_yoy']) if my_data['cumulative_revenue_yoy'] is not None else 'None'
            cum_yoy_match = official_cum_yoy == my_cum_yoy
            
            print(f"  累計年增率: 官方={official['累計年增率(%)']}% | 我的={my_cum_yoy}% | {'✅' if cum_yoy_match else '❌'}")
            
            # 總體匹配度
            total_matches = sum([revenue_match, mom_match, yoy_match, cum_match, cum_yoy_match])
            print(f"  匹配度: {total_matches}/5 {'✅' if total_matches >= 4 else '❌'}")
        
        # 統計整體匹配度
        print(f"\n📊 整體統計:")
        
        # 檢查營收匹配度
        revenue_matches = 0
        for i in range(min(len(official_df), len(my_df))):
            official_revenue = str(official_df.iloc[i]['當月營收(千元)']).replace(',', '')
            my_revenue = str(int(my_df.iloc[i]['revenue']))
            if official_revenue == my_revenue:
                revenue_matches += 1
        
        print(f"  營收匹配: {revenue_matches}/{min(len(official_df), len(my_df))} ({revenue_matches/min(len(official_df), len(my_df))*100:.1f}%)")
        
        if revenue_matches < min(len(official_df), len(my_df)) * 0.9:
            print("❌ 營收匹配度過低，解析邏輯需要修正！")
        else:
            print("✅ 營收匹配度良好！")
            
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_parsing_logic()
