import sqlite3
import os

db_path = "D:/Finlab/history/tables/price.db"
print(f"檢查數據庫: {db_path}")
print(f"文件大小: {os.path.getsize(db_path)/1024/1024:.2f} MB")

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 檢查表格
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print(f"表格: {[t[0] for t in tables]}")

if tables:
    table_name = tables[0][0]
    cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name}")
    stock_count = cursor.fetchone()[0]
    print(f"不同股票數量: {stock_count}")
    
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    total_records = cursor.fetchone()[0]
    print(f"總記錄數: {total_records}")
    
    cursor.execute(f"SELECT stock_id FROM {table_name} LIMIT 10")
    sample_stocks = cursor.fetchall()
    print(f"樣本股票: {[s[0] for s in sample_stocks]}")
    
    # 檢查最新日期
    cursor.execute(f"SELECT MAX(date) FROM {table_name}")
    latest_date = cursor.fetchone()[0]
    print(f"最新日期: {latest_date}")

conn.close() 