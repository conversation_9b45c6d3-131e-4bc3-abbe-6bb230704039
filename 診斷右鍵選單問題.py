#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷右鍵選單問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QMenu
from PyQt6.QtCore import Qt
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

class TestRightClickMenu(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("測試右鍵選單")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建測試表格
        self.table = QTableWidget(5, 3)
        self.table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "價格"])
        
        # 添加測試資料
        test_data = [
            ("2330", "台積電", "580"),
            ("2317", "鴻海", "105"),
            ("2454", "聯發科", "750"),
            ("1101", "台泥", "45"),
            ("8021", "尖點", "25")
        ]
        
        for row, (code, name, price) in enumerate(test_data):
            self.table.setItem(row, 0, QTableWidgetItem(code))
            self.table.setItem(row, 1, QTableWidgetItem(name))
            self.table.setItem(row, 2, QTableWidgetItem(price))
        
        # 設置右鍵選單
        print("🔧 設置右鍵選單...")
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        self.setCentralWidget(self.table)
        
        print("✅ 測試表格創建完成")
        print("📋 測試資料:")
        for i, (code, name, price) in enumerate(test_data):
            print(f"  {i+1}. {code} {name} - {price}")
        print("\n🖱️ 請在表格上右鍵點擊測試選單功能")
    
    def show_context_menu(self, position):
        """顯示右鍵選單"""
        try:
            print(f"\n🖱️ 右鍵選單被觸發，位置: {position}")
            
            # 獲取點擊的項目
            item = self.table.itemAt(position)
            if not item:
                print("❌ 沒有點擊到有效項目")
                return
            
            row = item.row()
            col = item.column()
            print(f"📊 點擊位置: 第 {row+1} 行，第 {col+1} 列")
            
            # 獲取股票資訊
            stock_code_item = self.table.item(row, 0)
            stock_name_item = self.table.item(row, 1)
            
            if not stock_code_item:
                print("❌ 無法獲取股票代碼")
                return
            
            stock_code = stock_code_item.text()
            stock_name = stock_name_item.text() if stock_name_item else "未知"
            
            print(f"📈 股票資訊: {stock_code} {stock_name}")
            
            # 創建右鍵選單
            context_menu = QMenu(self)
            context_menu.setStyleSheet("""
                QMenu {
                    background-color: #2b2b2b;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 5px;
                    padding: 5px;
                }
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 3px;
                }
                QMenu::item:selected {
                    background-color: #3daee9;
                }
            """)
            
            # 添加選單項目
            assessment_action = context_menu.addAction(f"📊 {stock_code} {stock_name} 月營收綜合評估")
            assessment_action.triggered.connect(lambda: self.show_assessment(stock_code, stock_name))
            
            news_action = context_menu.addAction(f"📰 {stock_code} 股票新聞")
            news_action.triggered.connect(lambda: self.show_news(stock_code, stock_name))
            
            monitor_action = context_menu.addAction(f"📊 加入監控清單")
            monitor_action.triggered.connect(lambda: self.add_monitor(stock_code, stock_name))
            
            print("✅ 右鍵選單創建完成，準備顯示...")
            
            # 顯示選單
            context_menu.exec(self.table.mapToGlobal(position))
            print("✅ 右鍵選單已顯示")
            
        except Exception as e:
            print(f"❌ 顯示右鍵選單失敗: {e}")
            import traceback
            traceback.print_exc()
    
    def show_assessment(self, stock_code, stock_name):
        """顯示評估"""
        print(f"📊 顯示 {stock_code} {stock_name} 的月營收綜合評估")
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "月營收評估", f"這裡會顯示 {stock_code} {stock_name} 的月營收綜合評估")
    
    def show_news(self, stock_code, stock_name):
        """顯示新聞"""
        print(f"📰 顯示 {stock_code} {stock_name} 的股票新聞")
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "股票新聞", f"這裡會顯示 {stock_code} {stock_name} 的股票新聞")
    
    def add_monitor(self, stock_code, stock_name):
        """加入監控"""
        print(f"📊 將 {stock_code} {stock_name} 加入監控清單")
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "監控清單", f"{stock_code} {stock_name} 已加入監控清單")

def test_right_click_menu():
    """測試右鍵選單功能"""
    print("🚀 開始測試右鍵選單功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建測試視窗
    window = TestRightClickMenu()
    window.show()
    
    print("\n📋 測試步驟:")
    print("1. 在表格的任意股票行上右鍵點擊")
    print("2. 檢查是否出現右鍵選單")
    print("3. 點擊選單項目測試功能")
    print("4. 關閉視窗結束測試")
    
    print("\n🔍 如果右鍵選單沒有出現，可能的原因:")
    print("- Qt版本兼容性問題")
    print("- 事件處理被阻塞")
    print("- 選單策略設置錯誤")
    print("- 信號連接失敗")
    
    # 運行應用程式
    sys.exit(app.exec())

def check_main_gui_right_click():
    """檢查主GUI的右鍵選單設置"""
    print("\n🔍 檢查主GUI的右鍵選單設置")
    print("=" * 50)
    
    try:
        from O3mh_gui_v21_optimized import FinlabGUI
        
        app = QApplication(sys.argv)
        gui = FinlabGUI()
        
        # 檢查表格的右鍵選單設置
        table = gui.result_table
        
        print(f"📊 表格物件: {type(table)}")
        print(f"🔧 右鍵選單策略: {table.contextMenuPolicy()}")
        print(f"📡 信號連接: {table.receivers(table.customContextMenuRequested)}")
        
        # 檢查是否正確設置
        expected_policy = Qt.ContextMenuPolicy.CustomContextMenu
        actual_policy = table.contextMenuPolicy()
        
        if actual_policy == expected_policy:
            print("✅ 右鍵選單策略設置正確")
        else:
            print(f"❌ 右鍵選單策略設置錯誤: 預期 {expected_policy}, 實際 {actual_policy}")
        
        # 檢查信號連接
        receivers_count = table.receivers(table.customContextMenuRequested)
        if receivers_count > 0:
            print(f"✅ 信號連接正常，接收者數量: {receivers_count}")
        else:
            print("❌ 信號連接失敗，沒有接收者")
        
        app.quit()
        
    except Exception as e:
        print(f"❌ 檢查主GUI失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🚀 右鍵選單診斷工具")
    print("=" * 60)
    
    print("選擇測試模式:")
    print("1. 測試獨立右鍵選單功能")
    print("2. 檢查主GUI右鍵選單設置")
    print("3. 兩者都測試")
    
    try:
        choice = input("\n請輸入選擇 (1/2/3): ").strip()
        
        if choice == "1":
            test_right_click_menu()
        elif choice == "2":
            check_main_gui_right_click()
        elif choice == "3":
            check_main_gui_right_click()
            print("\n" + "="*60)
            test_right_click_menu()
        else:
            print("❌ 無效選擇，執行完整測試")
            check_main_gui_right_click()
            print("\n" + "="*60)
            test_right_click_menu()
            
    except KeyboardInterrupt:
        print("\n\n👋 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
