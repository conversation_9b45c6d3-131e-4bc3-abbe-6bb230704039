#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試matplotlib版本圖表修復
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class MatplotlibChartTestWindow(QMainWindow):
    """matplotlib版本圖表測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📈 Matplotlib版本圖表修復測試")
        self.setGeometry(100, 100, 700, 500)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow { background-color: #1a1a1a; color: white; }
            QPushButton { 
                background-color: #ff6b35; 
                color: white; 
                border: none; 
                padding: 15px; 
                font-size: 16px; 
                border-radius: 8px; 
                margin: 10px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #e55a2b; }
            QLabel { color: white; font-size: 14px; margin: 10px; }
        """)
        
        # 創建界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📈 Matplotlib版本圖表修復測試")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff6b35; margin: 20px;")
        layout.addWidget(title)
        
        # 說明
        info = QLabel("""
🔥 終極解決方案：換用Matplotlib！

❌ PyQtGraph問題：
• DateAxisItem兼容性問題
• setTicks()方法不穩定
• 軸設置經常失效

✅ Matplotlib優勢：
• 成熟穩定的日期軸支持
• 自動日期格式化
• 完美的日期標籤顯示
• 無兼容性問題

📊 新的實現：
• 使用matplotlib.dates.DateFormatter
• 自動日期間隔調整
• 完美的日期軸顯示
• 支援線圖、柱狀圖、面積圖

🧪 測試步驟：
1. 點擊下方按鈕開啟爬蟲
2. 切換到「圖表分析」分頁
3. 選擇「歷史指數資料」
4. 點擊「生成圖表」
5. 現在應該能看到完美的日期軸了！
        """)
        info.setStyleSheet("""
            background-color: #2a2a2a; 
            padding: 20px; 
            border-radius: 10px; 
            border: 2px solid #ff6b35;
            line-height: 1.4;
        """)
        layout.addWidget(info)
        
        # 測試按鈕
        test_btn = QPushButton("📈 開啟台股爬蟲 (測試Matplotlib版本)")
        test_btn.clicked.connect(self.open_crawler)
        layout.addWidget(test_btn)
        
        # 結果提示
        result = QLabel("🎉 現在用Matplotlib，日期軸肯定能正常顯示了！")
        result.setAlignment(Qt.AlignmentFlag.AlignCenter)
        result.setStyleSheet("""
            color: #ff6b35; 
            font-weight: bold; 
            font-size: 18px; 
            background-color: #2a2a2a; 
            padding: 15px; 
            border-radius: 8px;
            border: 1px solid #ff6b35;
        """)
        layout.addWidget(result)
    
    def open_crawler(self):
        """開啟爬蟲"""
        try:
            print("📈 開啟台股爬蟲 (Matplotlib版本)...")
            dialog = TWSEMarketDataDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"❌ 錯誤: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    print("📈 Matplotlib版本圖表修復測試")
    print("🔥 終極解決方案：直接換用Matplotlib！")
    print("✅ 成熟穩定的日期軸支持")
    print("✅ 自動日期格式化")
    print("✅ 完美的日期標籤顯示")
    print("🎉 現在日期軸肯定能正常顯示了！")
    
    window = MatplotlibChartTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
