#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除 newprice.db 中 stock_id 為 6 碼且第 1 碼為 7 的資料
"""

import sqlite3
import os
import shutil

def remove_7_prefix_stocks_from_newprice_db():
    """移除 stock_id 為 6 碼且第 1 碼為 7 的資料"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    backup_file = 'D:/Finlab/history/tables/newprice_remove7_backup.db'
    
    print("=" * 80)
    print("🔄 移除 newprice.db 中 6 碼且第 1 碼為 7 的股票資料")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查原始檔案大小
    original_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
    print(f"📁 原始檔案: {db_file}")
    print(f"📊 原始檔案大小: {original_size:.2f} MB")
    
    try:
        # 創建備份
        print(f"\n💾 創建備份檔案...")
        shutil.copy2(db_file, backup_file)
        backup_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
        print(f"✅ 備份完成: {backup_file} ({backup_size:.2f} MB)")
        
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查原始資料統計
        print(f"\n📊 原始資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        original_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {original_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        original_stocks = cursor.fetchone()[0]
        print(f"   不重複股票數: {original_stocks:,}")
        
        # 檢查 6 碼且第 1 碼為 7 的股票
        print(f"\n🔍 分析 6 碼且第 1 碼為 7 的股票:")
        cursor.execute("""
            SELECT COUNT(DISTINCT stock_id) 
            FROM price 
            WHERE LENGTH(stock_id) = 6 AND stock_id LIKE '7%'
        """)
        target_stocks_count = cursor.fetchone()[0]
        print(f"   符合條件的股票數: {target_stocks_count:,}")
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM price 
            WHERE LENGTH(stock_id) = 6 AND stock_id LIKE '7%'
        """)
        target_records_count = cursor.fetchone()[0]
        print(f"   符合條件的記錄數: {target_records_count:,}")
        
        # 顯示一些範例
        print(f"\n📋 符合條件的股票範例:")
        cursor.execute("""
            SELECT DISTINCT stock_id, stock_name 
            FROM price 
            WHERE LENGTH(stock_id) = 6 AND stock_id LIKE '7%'
            LIMIT 10
        """)
        sample_stocks = cursor.fetchall()
        for stock_id, stock_name in sample_stocks:
            print(f"   {stock_id} {stock_name}")
        
        # 檢查各種股票代碼長度分布
        print(f"\n📊 股票代碼長度分布:")
        cursor.execute("""
            SELECT LENGTH(stock_id) as length, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
            FROM price 
            GROUP BY LENGTH(stock_id) 
            ORDER BY length
        """)
        length_stats = cursor.fetchall()
        for length, stock_count, record_count in length_stats:
            print(f"   {length} 碼: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
        
        # 檢查 6 碼股票的第一碼分布
        print(f"\n📊 6 碼股票第一碼分布:")
        cursor.execute("""
            SELECT SUBSTR(stock_id, 1, 1) as first_digit, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
            FROM price 
            WHERE LENGTH(stock_id) = 6
            GROUP BY SUBSTR(stock_id, 1, 1) 
            ORDER BY first_digit
        """)
        first_digit_stats = cursor.fetchall()
        for first_digit, stock_count, record_count in first_digit_stats:
            print(f"   第一碼 {first_digit}: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
        
        if target_records_count == 0:
            print(f"\n✅ 沒有符合條件的資料需要移除")
            conn.close()
            return True
        
        # 確認刪除
        print(f"\n⚠️ 即將刪除:")
        print(f"   股票數: {target_stocks_count:,} 檔")
        print(f"   記錄數: {target_records_count:,} 筆")
        print(f"   預計節省空間: {(target_records_count / original_count) * 100:.1f}%")
        
        # 執行刪除
        print(f"\n🗑️ 開始刪除 6 碼且第 1 碼為 7 的股票資料...")
        cursor.execute("""
            DELETE FROM price 
            WHERE LENGTH(stock_id) = 6 AND stock_id LIKE '7%'
        """)
        deleted_count = cursor.rowcount
        print(f"✅ 已刪除 {deleted_count:,} 筆資料")
        
        # 提交事務
        conn.commit()
        print(f"✅ 事務已提交")
        
        # 檢查刪除後的統計
        print(f"\n📊 刪除後資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {final_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        final_stocks = cursor.fetchone()[0]
        print(f"   不重複股票數: {final_stocks:,}")
        
        # 驗證是否還有 6 碼且第 1 碼為 7 的股票
        cursor.execute("""
            SELECT COUNT(*) 
            FROM price 
            WHERE LENGTH(stock_id) = 6 AND stock_id LIKE '7%'
        """)
        remaining_count = cursor.fetchone()[0]
        print(f"   剩餘 6 碼且第 1 碼為 7 的記錄: {remaining_count}")
        
        # 檢查刪除後的股票代碼長度分布
        print(f"\n📊 刪除後股票代碼長度分布:")
        cursor.execute("""
            SELECT LENGTH(stock_id) as length, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
            FROM price 
            GROUP BY LENGTH(stock_id) 
            ORDER BY length
        """)
        new_length_stats = cursor.fetchall()
        for length, stock_count, record_count in new_length_stats:
            print(f"   {length} 碼: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
        
        # 檢查刪除後 6 碼股票的第一碼分布
        print(f"\n📊 刪除後 6 碼股票第一碼分布:")
        cursor.execute("""
            SELECT SUBSTR(stock_id, 1, 1) as first_digit, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
            FROM price 
            WHERE LENGTH(stock_id) = 6
            GROUP BY SUBSTR(stock_id, 1, 1) 
            ORDER BY first_digit
        """)
        new_first_digit_stats = cursor.fetchall()
        for first_digit, stock_count, record_count in new_first_digit_stats:
            print(f"   第一碼 {first_digit}: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
        
        # 檢查日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        date_range = cursor.fetchone()
        print(f"\n📅 日期範圍: {date_range[0]} ~ {date_range[1]}")
        
        # 關閉連接後重新開啟進行 VACUUM
        conn.close()
        
        # 優化資料庫
        print(f"\n🔧 優化資料庫...")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 最終驗證
        print(f"\n📊 最終驗證:")
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   最終資料筆數: {final_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        final_unique = cursor.fetchone()[0]
        print(f"   最終不重複股票數: {final_unique:,}")
        
        # 檢查一些重要股票是否還存在
        important_stocks = ['2330', '2317', '2454', '1301', '2412']
        print(f"\n📊 重要股票驗證:")
        for stock in important_stocks:
            cursor.execute("SELECT COUNT(*) FROM price WHERE stock_id = ?", (stock,))
            count = cursor.fetchone()[0]
            if count > 0:
                cursor.execute("SELECT stock_name FROM price WHERE stock_id = ? LIMIT 1", (stock,))
                name = cursor.fetchone()[0]
                print(f"   {stock} {name}: {count:,} 筆 ✅")
            else:
                print(f"   {stock}: 無資料 ❌")
        
        conn.close()
        
        # 檢查最終檔案大小
        final_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
        space_saved = original_size - final_size
        space_saved_percent = (space_saved / original_size) * 100
        
        print(f"\n💾 檔案大小變化:")
        print(f"   原始大小: {original_size:.2f} MB")
        print(f"   最終大小: {final_size:.2f} MB")
        print(f"   節省空間: {space_saved:.2f} MB ({space_saved_percent:.1f}%)")
        
        print(f"\n✅ 6 碼且第 1 碼為 7 的股票移除完成！")
        print(f"📁 主檔案: {db_file}")
        print(f"📁 備份檔案: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 移除過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出錯，嘗試從備份恢復
        if os.path.exists(backup_file):
            print(f"\n🔄 嘗試從備份恢復...")
            try:
                shutil.copy2(backup_file, db_file)
                print(f"✅ 已從備份恢復")
            except Exception as restore_error:
                print(f"❌ 恢復失敗: {restore_error}")
        
        return False

if __name__ == "__main__":
    remove_7_prefix_stocks_from_newprice_db()
