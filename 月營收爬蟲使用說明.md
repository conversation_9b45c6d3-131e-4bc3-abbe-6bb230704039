# 獨立月營收爬蟲程式使用說明

## 📋 程式簡介

`monthly_revenue_crawler.py` 是一個獨立的月營收爬蟲程式，專門用於更新 `monthly_report.pkl` 檔案。

### ✨ 主要特色

- 🎯 **專一功能**: 只負責月營收資料爬取和更新
- 🔄 **完整覆蓋**: 支援上市+上櫃，國內+國外公司
- 🛡️ **安全備份**: 每次更新前自動備份原始檔案
- 🧪 **測試模式**: 可以先測試不保存，確認資料正確
- 📅 **靈活更新**: 支援指定月份或自動更新最新月份

## 🚀 使用方法

### 1. 基本使用 (更新最新月份)

```bash
python monthly_revenue_crawler.py
```

### 2. 指定年月更新

```bash
# 更新 2024年12月資料
python monthly_revenue_crawler.py --year 2024 --month 12
```

### 3. 批量更新多個月份

```bash
# 更新最近3個月的資料
python monthly_revenue_crawler.py --months 3
```

### 4. 測試模式 (不保存檔案)

```bash
# 測試爬取但不保存
python monthly_revenue_crawler.py --test-only

# 測試指定月份
python monthly_revenue_crawler.py --year 2024 --month 11 --test-only
```

## 📊 參數說明

| 參數 | 說明 | 範例 |
|------|------|------|
| `--year` | 指定年份 (西元年) | `--year 2024` |
| `--month` | 指定月份 (1-12) | `--month 12` |
| `--months` | 要更新的月數 | `--months 3` |
| `--test-only` | 僅測試不保存 | `--test-only` |

## 📁 檔案結構

```
D:\Finlab\backup\O3mh_strategy2AA\
├── monthly_revenue_crawler.py          # 主程式
├── history/tables/
│   ├── monthly_report.pkl              # 主要資料檔案
│   └── monthly_report.pkl.backup_*     # 自動備份檔案
└── finlab/                             # 依賴模組
```

## 🔍 程式執行流程

1. **備份現有檔案** - 自動建立 `.backup_YYYYMMDD_HHMMSS` 備份
2. **載入現有資料** - 讀取 `monthly_report.pkl` 中的現有資料
3. **計算更新月份** - 根據參數或自動計算需要更新的月份
4. **爬取新資料** - 依序爬取上市國內→上市國外→上櫃國內→上櫃國外
5. **合併資料** - 將新資料與現有資料合併，移除重複
6. **保存檔案** - 更新 `monthly_report.pkl` 檔案

## 📈 爬取資料範圍

### 市場覆蓋
- ✅ **上市公司** (TSE)
  - 國內公司 (`_0.html`)
  - 國外公司 (`_1.html`)
- ✅ **上櫃公司** (OTC)
  - 國內公司 (`_0.html`)
  - 國外公司 (`_1.html`)

### 資料欄位
1. `當月營收` - 當月營業收入
2. `上月營收` - 上個月營業收入
3. `去年當月營收` - 去年同月營業收入
4. `上月比較增減(%)` - 與上月比較增減百分比
5. `去年同月增減(%)` - 與去年同月比較增減百分比
6. `當月累計營收` - 年初至當月累計營收
7. `去年累計營收` - 去年年初至同月累計營收
8. `前期比較增減(%)` - 累計營收與去年同期比較
9. `備註` - 備註資訊

## 🛡️ 安全機制

### 自動備份
- 每次更新前自動備份原始檔案
- 備份檔名格式: `monthly_report.pkl.backup_YYYYMMDD_HHMMSS`
- 可手動還原: `cp backup_file monthly_report.pkl`

### 重複資料處理
- 自動檢測並跳過已存在的月份資料
- 合併時移除重複索引，保留最新資料

### 錯誤處理
- 網路請求失敗自動重試
- 編碼問題自動處理
- 詳細的錯誤訊息和進度顯示

## 📊 執行結果範例

```
📊 獨立月營收爬蟲程式
======================================
🎯 目標: 更新 monthly_report.pkl 檔案
======================================
🚀 開始更新月營收資料
======================================
✅ 已備份: history/tables/monthly_report.pkl.backup_20250722_223045
📖 載入現有資料: 330,661 筆
📅 計劃更新月份: ['2024-12-01']
📊 爬取月營收 - 民國113年12月 (2024-12-01)
   📋 上市國內
   🔄 國內上市: https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_12_0.html
     ✅ 成功: 958 筆
   📋 上市國外
   🔄 國外上市: https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_12_1.html
     ✅ 成功: 91 筆
   📋 上櫃國內
   🔄 國內上櫃: https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_113_12_0.html
     ✅ 成功: 825 筆
   📋 上櫃國外
   🔄 國外上櫃: https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_113_12_1.html
     ✅ 成功: 29 筆
   🎉 總計: 1903 筆資料
✅ 2024-12-01: 1903 筆

📊 新爬取資料: 1,903 筆
📈 合併後總計: 332,564 筆
   原有: 330,661 筆
   新增: 1,903 筆
💾 已保存: history/tables/monthly_report.pkl (26.2 MB)
🎉 更新完成！

✅ 程式執行成功
```

## ⚠️ 注意事項

1. **網路連線**: 需要穩定的網路連線訪問證交所網站
2. **執行頻率**: 建議不要過於頻繁執行，避免被網站封鎖IP
3. **資料時效**: 月營收資料通常在每月10日後公布
4. **備份管理**: 定期清理舊的備份檔案以節省空間

## 🔧 故障排除

### 常見問題

1. **IP被封鎖**
   ```
   ⚠️ IP被封鎖
   ```
   **解決方案**: 等待一段時間後重試，或更換網路環境

2. **編碼錯誤**
   ```
   ❌ 失敗: unknown encoding: big-5
   ```
   **解決方案**: 程式已自動處理，如仍有問題請檢查網路連線

3. **無資料**
   ```
   ❌ 無新資料可更新
   ```
   **解決方案**: 檢查指定月份是否正確，或該月份資料是否已公布

### 手動還原備份

如果更新後發現問題，可以手動還原備份：

```bash
# 查看備份檔案
ls -la history/tables/monthly_report.pkl.backup_*

# 還原最新備份 (請替換為實際檔名)
cp history/tables/monthly_report.pkl.backup_20250722_223045 history/tables/monthly_report.pkl
```

## 🎯 總結

這個獨立的月營收爬蟲程式提供了：

- ✅ **簡單易用**: 一行指令即可更新
- ✅ **功能完整**: 涵蓋所有市場和公司類型
- ✅ **安全可靠**: 自動備份和錯誤處理
- ✅ **靈活配置**: 支援多種更新模式

現在您可以輕鬆地維護最新的月營收資料！🎉
