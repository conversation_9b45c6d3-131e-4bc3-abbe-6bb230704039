#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試現金流量表爬蟲功能
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def direct_test_cash_flows():
    """直接測試現金流量表爬蟲"""
    
    print("=" * 80)
    print("🚀 直接測試現金流量表爬蟲功能")
    print("=" * 80)
    
    try:
        # 導入必要模組
        from crawler import crawl_cash_flows
        
        # 測試日期 - 使用一個確定有資料的日期
        test_date = datetime.datetime(2022, 5, 15)  # Q1 財報發布日
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"🔄 開始執行現金流量表爬蟲...")
        print(f"⚠️ 注意: 這將進行實際的網路爬取，可能需要幾分鐘時間")
        
        # 詢問用戶確認
        choice = input("\n是否繼續執行實際爬取？(y/n): ").strip().lower()
        
        if choice != 'y':
            print("⏹️ 用戶取消執行")
            return False
        
        print("\n🚀 開始爬取...")
        print("-" * 60)
        
        # 執行爬蟲
        result = crawl_cash_flows(test_date)
        
        print("-" * 60)
        
        # 檢查結果
        if result is not None and len(result) > 0:
            print(f"✅ 爬取成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns):,}")
            
            # 檢查索引結構
            if hasattr(result.index, 'names'):
                print(f"🔍 索引結構: {result.index.names}")
            
            # 顯示資料形狀
            print(f"📐 資料形狀: {result.shape}")
            
            # 檢查主要現金流量項目
            main_items = [
                '本期稅前淨利（淨損）',
                '營業活動之淨現金流入（流出）',
                '投資活動之淨現金流入（流出）',
                '籌資活動之淨現金流入（流出）',
                '期末現金及約當現金餘額'
            ]
            
            print(f"\n💰 主要現金流量項目檢查:")
            for item in main_items:
                if item in result.columns:
                    non_null_count = result[item].notna().sum()
                    percentage = (non_null_count / len(result)) * 100
                    print(f"   ✅ {item:<30} {non_null_count:>6,} ({percentage:>5.1f}%)")
                else:
                    print(f"   ❌ {item:<30} 欄位不存在")
            
            # 顯示範例資料
            print(f"\n📊 範例資料 (前3筆):")
            try:
                # 只顯示主要欄位
                display_columns = ['本期稅前淨利（淨損）', '營業活動之淨現金流入（流出）', '期末現金及約當現金餘額']
                available_columns = [col for col in display_columns if col in result.columns]
                
                if available_columns:
                    sample_data = result[available_columns].head(3)
                    print(sample_data)
                else:
                    print("   無法顯示範例資料 (主要欄位不存在)")
                    print(f"   可用欄位範例: {list(result.columns[:5])}")
            except Exception as e:
                print(f"   顯示範例資料時出錯: {e}")
            
            # 檢查資料儲存
            print(f"\n💾 檢查資料儲存:")
            
            # 檢查 DB 檔案
            db_path = os.path.join('history', 'tables', 'cash_flows.db')
            if os.path.exists(db_path):
                print(f"   ✅ DB 檔案已創建: {db_path}")
                
                # 檢查 DB 內容
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM cash_flows")
                    db_count = cursor.fetchone()[0]
                    print(f"   📊 DB 中記錄數: {db_count:,}")
                    conn.close()
                except Exception as e:
                    print(f"   ⚠️ 檢查 DB 內容失敗: {e}")
            else:
                print(f"   ⚠️ DB 檔案未創建: {db_path}")
            
            # 檢查 pickle 檔案
            pickle_path = os.path.join('history', 'cash_flows.pkl')
            if os.path.exists(pickle_path):
                print(f"   ✅ Pickle 檔案已創建: {pickle_path}")
            else:
                print(f"   ⚠️ Pickle 檔案未創建: {pickle_path}")
            
            return True
            
        elif result is not None:
            print(f"⚠️ 爬取完成但無資料")
            print(f"📊 返回資料形狀: {result.shape}")
            return False
        else:
            print(f"❌ 爬取失敗，返回 None")
            return False
            
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """檢查相依性"""
    
    print("\n" + "=" * 80)
    print("🔍 檢查相依性")
    print("=" * 80)
    
    dependencies = [
        ('pandas', 'pandas'),
        ('requests', 'requests'),
        ('sqlite3', 'sqlite3'),
        ('tqdm', 'tqdm'),
    ]

    all_ok = True

    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"   ✅ {dep_name}")
        except ImportError:
            print(f"   ❌ {dep_name} (缺少)")
            all_ok = False
    
    # 檢查目錄結構
    print(f"\n📁 檢查目錄結構:")
    
    required_dirs = [
        'history',
        'history/tables',
        'history/financial_statement',
        'finlab'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ⚠️ {dir_path} (不存在，將自動創建)")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"      ✅ 已創建 {dir_path}")
            except Exception as e:
                print(f"      ❌ 創建失敗: {e}")
                all_ok = False
    
    return all_ok

def main():
    """主函數"""
    
    print("🧪 現金流量表爬蟲直接測試")
    
    # 檢查相依性
    deps_ok = check_dependencies()
    
    if not deps_ok:
        print("\n❌ 相依性檢查失敗，請先解決相關問題")
        return False
    
    # 執行直接測試
    test_result = direct_test_cash_flows()
    
    # 總結
    print("\n" + "=" * 80)
    print("📊 測試總結")
    print("=" * 80)
    
    if test_result:
        print("🎉 現金流量表爬蟲測試成功！")
        print("\n📝 後續建議:")
        print("   1. 檢查生成的資料檔案")
        print("   2. 驗證資料內容的正確性")
        print("   3. 設定定期自動更新")
        print("   4. 整合到你的分析流程中")
    else:
        print("⚠️ 現金流量表爬蟲測試未完全成功")
        print("\n🔧 可能的解決方案:")
        print("   1. 檢查網路連線")
        print("   2. 確認 MOPS 網站可訪問")
        print("   3. 調整爬取參數")
        print("   4. 檢查錯誤訊息並修復")
    
    return test_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
