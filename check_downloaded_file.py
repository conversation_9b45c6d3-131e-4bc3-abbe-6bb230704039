#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查下載的檔案內容
"""

import os

def check_downloaded_file():
    """檢查下載的檔案"""
    print("🔍 檢查下載的檔案")
    print("=" * 60)
    
    temp_file = 'temp.zip'
    
    if os.path.exists(temp_file):
        file_size = os.path.getsize(temp_file)
        print(f"📁 檔案存在: {temp_file}")
        print(f"   檔案大小: {file_size} bytes")
        
        # 讀取檔案前幾個字節
        try:
            with open(temp_file, 'rb') as f:
                first_bytes = f.read(100)
            
            print(f"   前100字節 (hex): {first_bytes.hex()}")
            
            # 嘗試解讀為文字
            try:
                text_content = first_bytes.decode('utf-8', errors='ignore')
                print(f"   前100字元 (text): {text_content}")
            except:
                pass
            
            # 檢查是否為 ZIP 檔案
            if first_bytes.startswith(b'PK'):
                print(f"   ✅ 這是一個 ZIP 檔案")
            elif b'<html' in first_bytes.lower() or b'<!doctype' in first_bytes.lower():
                print(f"   ❌ 這是一個 HTML 頁面，不是 ZIP 檔案")
            elif b'error' in first_bytes.lower() or b'404' in first_bytes:
                print(f"   ❌ 這是一個錯誤頁面")
            else:
                print(f"   ❓ 未知的檔案格式")
                
        except Exception as e:
            print(f"   ❌ 讀取檔案失敗: {str(e)}")
    else:
        print(f"❌ 檔案不存在: {temp_file}")

def test_mops_url_directly():
    """直接測試 MOPS URL"""
    print(f"\n🔍 直接測試 MOPS URL")
    print("=" * 60)
    
    import requests
    import urllib3
    
    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    test_url = "https://mops.twse.com.tw/server-java/FileDownLoad?step=9&fileName=tifrs-2023Q1.zip&filePath=/home/<USER>/nas/ifrs/2023/"
    
    print(f"🌐 測試 URL: {test_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(test_url, headers=headers, verify=False, timeout=30)
        
        print(f"   HTTP 狀態: {response.status_code}")
        print(f"   回應長度: {len(response.content)} bytes")
        print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
        
        # 檢查前100字節
        first_bytes = response.content[:100]
        print(f"   前100字節 (hex): {first_bytes.hex()}")
        
        # 嘗試解讀為文字
        try:
            text_content = first_bytes.decode('utf-8', errors='ignore')
            print(f"   前100字元 (text): {text_content}")
        except:
            pass
        
        # 檢查檔案類型
        if first_bytes.startswith(b'PK'):
            print(f"   ✅ 回應是 ZIP 檔案")
        elif b'<html' in first_bytes.lower():
            print(f"   ❌ 回應是 HTML 頁面")
        elif b'error' in first_bytes.lower():
            print(f"   ❌ 回應包含錯誤訊息")
        else:
            print(f"   ❓ 未知的回應格式")
            
    except Exception as e:
        print(f"   ❌ 請求失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 下載檔案檢查工具")
    print("=" * 60)
    print("🎯 目標: 了解為什麼下載的不是 ZIP 檔案")
    print("=" * 60)
    
    # 檢查1: 本地下載的檔案
    check_downloaded_file()
    
    # 檢查2: 直接測試 URL
    test_mops_url_directly()

if __name__ == "__main__":
    main()
