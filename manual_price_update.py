#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動執行 price 增量更新測試
"""

import sys
import os

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def manual_price_update():
    """手動執行 price 增量更新"""
    
    print("=" * 60)
    print("🚀 手動測試 price 增量更新")
    print("=" * 60)
    
    try:
        # 導入必要模組
        from auto_update import get_newprice_incremental_date_range, auto_update
        from crawler import crawl_price
        
        print("✅ 模組導入成功")
        
        # 獲取日期範圍
        date_range = get_newprice_incremental_date_range()
        print(f"獲取到的日期範圍: {date_range}")
        
        if date_range:
            print(f"準備更新 {len(date_range)} 天的資料")
            
            # 顯示日期列表
            for i, date in enumerate(date_range, 1):
                print(f"  {i}. {date.strftime('%Y-%m-%d')}")
            
            # 執行更新
            print("\n開始執行更新...")
            auto_update('price', crawl_price, date_range)
            print("✅ 更新完成")
            
        else:
            print("❌ 無法獲取日期範圍")
            
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()

def check_update_result():
    """檢查更新結果"""
    
    print("\n" + "=" * 60)
    print("📊 檢查更新結果")
    print("=" * 60)
    
    try:
        import sqlite3
        
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        
        if os.path.exists(newprice_db):
            conn = sqlite3.connect(newprice_db)
            cursor = conn.cursor()
            
            # 檢查最後日期
            cursor.execute('SELECT MAX(date) FROM stock_daily_data')
            last_date = cursor.fetchone()[0]
            
            # 檢查總資料筆數
            cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
            total_count = cursor.fetchone()[0]
            
            # 檢查最近幾天的資料
            cursor.execute('''
                SELECT date, COUNT(*) 
                FROM stock_daily_data 
                WHERE date >= "2022-08-30" 
                GROUP BY date 
                ORDER BY date DESC 
                LIMIT 10
            ''')
            recent_data = cursor.fetchall()
            
            conn.close()
            
            print(f"newprice.db 當前狀態:")
            print(f"  最後日期: {last_date}")
            print(f"  總資料筆數: {total_count:,}")
            print(f"  最近日期的資料:")
            for date, count in recent_data:
                print(f"    {date}: {count:,} 筆")
                
        else:
            print(f"❌ newprice.db 不存在: {newprice_db}")
            
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    manual_price_update()
    check_update_result()
