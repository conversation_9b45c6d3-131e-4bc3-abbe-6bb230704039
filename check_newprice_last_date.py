#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 的最後日期
"""

import sqlite3
from datetime import datetime, timedelta

# 檢查 newprice.db 的最後日期
db_file = 'D:/Finlab/history/tables/newprice.db'
conn = sqlite3.connect(db_file)
cursor = conn.cursor()

cursor.execute('SELECT MAX(date) FROM stock_daily_data')
last_date = cursor.fetchone()[0]
print(f'newprice.db 最後日期: {last_date}')

# 計算需要更新的日期範圍 (從最後日期的下一天開始，更新5天)
if last_date:
    last_dt = datetime.strptime(last_date, '%Y-%m-%d')
    start_date = last_dt + timedelta(days=1)
    end_date = start_date + timedelta(days=4)  # 5天
    
    print(f'建議更新範圍: {start_date.strftime("%Y-%m-%d")} 到 {end_date.strftime("%Y-%m-%d")}')
    print(f'今天日期: {datetime.now().strftime("%Y-%m-%d")}')

conn.close()
