#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 income_sheet.pkl 的最終更新狀態
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import pandas as pd
import datetime
import os

def check_income_sheet_final():
    """檢查 income_sheet.pkl 的最終狀態"""
    print("🔍 檢查 income_sheet.pkl 最終更新狀態")
    print("=" * 60)
    
    file_path = 'history/tables/income_sheet.pkl'
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 獲取檔案資訊
        file_stat = os.stat(file_path)
        file_size_mb = file_stat.st_size / 1024 / 1024
        modification_time = datetime.datetime.fromtimestamp(file_stat.st_mtime)
        
        print(f"📁 檔案資訊:")
        print(f"   檔案路徑: {os.path.abspath(file_path)}")
        print(f"   檔案大小: {file_size_mb:.2f} MB")
        print(f"   最後修改: {modification_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 讀取檔案
        print(f"\n📊 讀取資料中...")
        df = pd.read_pickle(file_path)
        
        print(f"📊 資料內容:")
        print(f"   資料筆數: {len(df):,}")
        print(f"   資料形狀: {df.shape}")
        print(f"   索引: {df.index.names}")
        print(f"   欄位數: {len(df.columns)}")
        
        # 檢查日期範圍
        if 'date' in df.index.names:
            dates = df.index.get_level_values('date')
            print(f"\n📅 日期資訊:")
            print(f"   最早日期: {dates.min()}")
            print(f"   最新日期: {dates.max()}")
            print(f"   日期數量: {len(dates.unique())} 個")
            
            # 計算更新狀態
            latest_date = dates.max()
            today = datetime.datetime.now()
            
            if hasattr(latest_date, 'to_pydatetime'):
                latest_date = latest_date.to_pydatetime()
            
            days_behind = (today - latest_date).days
            print(f"   落後天數: {days_behind} 天")
            
            # 判斷更新狀態
            if days_behind <= 90:  # 3個月內
                print(f"   ✅ 資料很新")
            elif days_behind <= 365:  # 1年內
                print(f"   💡 資料較新")
            elif days_behind <= 1095:  # 3年內
                print(f"   ⚠️ 資料稍舊")
            else:
                print(f"   ❌ 資料很舊")
        
        # 檢查股票數量
        if 'stock_id' in df.index.names:
            stock_ids = df.index.get_level_values('stock_id')
            unique_stocks = stock_ids.unique()
            print(f"\n📈 股票資訊:")
            print(f"   股票數量: {len(unique_stocks)} 檔")
            
            # 顯示一些樣本股票
            sample_stocks = unique_stocks[:5]
            print(f"   樣本股票: {list(sample_stocks)}")
        
        # 檢查最新季度的資料
        if 'date' in df.index.names:
            latest_dates = sorted(dates.unique(), reverse=True)[:3]
            print(f"\n📊 最新3個季度:")
            for i, date in enumerate(latest_dates, 1):
                date_data = df[df.index.get_level_values('date') == date]
                print(f"   {i}. {date.strftime('%Y-%m-%d')}: {len(date_data)} 筆資料")
        
        # 檢查欄位
        print(f"\n📋 欄位資訊:")
        print(f"   總欄位數: {len(df.columns)}")
        sample_columns = list(df.columns)[:10]
        print(f"   樣本欄位: {sample_columns}")
        
        return True
        
    except Exception as e:
        print(f"❌ 讀取檔案失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_backup_files():
    """檢查備份檔案"""
    print(f"\n🔍 檢查備份檔案")
    print("=" * 60)
    
    backup_dir = 'history/tables'
    backup_files = []
    
    try:
        for file in os.listdir(backup_dir):
            if file.startswith('income_sheet_backup_') and file.endswith('.pkl'):
                file_path = os.path.join(backup_dir, file)
                file_stat = os.stat(file_path)
                file_size_mb = file_stat.st_size / 1024 / 1024
                modification_time = datetime.datetime.fromtimestamp(file_stat.st_mtime)
                
                backup_files.append({
                    'name': file,
                    'size_mb': file_size_mb,
                    'modified': modification_time
                })
        
        # 按修改時間排序
        backup_files.sort(key=lambda x: x['modified'], reverse=True)
        
        print(f"📁 找到 {len(backup_files)} 個備份檔案:")
        for i, backup in enumerate(backup_files, 1):
            print(f"   {i}. {backup['name']}")
            print(f"      大小: {backup['size_mb']:.2f} MB")
            print(f"      時間: {backup['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
    except Exception as e:
        print(f"❌ 檢查備份檔案失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 Income Sheet 最終狀態檢查")
    print("=" * 60)
    print("🎯 目標: 確認 income_sheet.pkl 更新完成狀態")
    print("=" * 60)
    
    # 檢查主檔案
    success = check_income_sheet_final()
    
    # 檢查備份檔案
    check_backup_files()
    
    if success:
        print(f"\n🎉 income_sheet.pkl 檢查完成！")
        print(f"📁 主檔案位置: {os.path.abspath('history/tables/income_sheet.pkl')}")
        print(f"💾 備份檔案位置: history/tables/income_sheet_backup_*.pkl")
    else:
        print(f"\n⚠️ 檢查過程中遇到問題")

if __name__ == "__main__":
    main()
