#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yahoo Finance 排行榜專用爬蟲
專門針對成交量、漲幅、跌幅排行榜的數據獲取
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
import pandas as pd
import re

# Selenium imports
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("⚠️ Selenium未安裝，將使用基本爬蟲功能")

class YahooRankingCrawler:
    """Yahoo Finance 排行榜專用爬蟲"""
    
    def __init__(self):
        self.base_url = "https://tw.stock.yahoo.com"
        self.session = requests.Session()
        self.setup_session()

        # 排行榜URL映射
        self.ranking_urls = {
            'volume': f"{self.base_url}/rank/volume",
            'change-up': f"{self.base_url}/rank/change-up",
            'change-down': f"{self.base_url}/rank/change-down"
        }

        # Selenium driver
        self.driver = None
        self.use_selenium = SELENIUM_AVAILABLE

        logging.info("✅ Yahoo排行榜爬蟲初始化完成")
    
    def setup_session(self):
        """設置session"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://tw.stock.yahoo.com/',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(headers)
    
    def get_ranking_data(self, ranking_type: str, limit: int = 30) -> List[Dict]:
        """
        獲取排行榜數據

        Args:
            ranking_type: 排行榜類型 ('volume', 'change-up', 'change-down')
            limit: 獲取數量限制

        Returns:
            List[Dict]: 排行榜數據列表
        """
        try:
            if ranking_type not in self.ranking_urls:
                logging.error(f"❌ 不支援的排行榜類型: {ranking_type}")
                return []

            url = self.ranking_urls[ranking_type]
            logging.info(f"🔍 獲取 {ranking_type} 排行榜數據: {url}")

            # 優先使用Selenium
            if self.use_selenium:
                ranking_data = self._get_ranking_with_selenium(url, ranking_type, limit)
                if ranking_data:
                    logging.info(f"✅ Selenium成功獲取 {len(ranking_data)} 筆 {ranking_type} 排行榜數據")
                    return ranking_data
                else:
                    logging.warning(f"⚠️ Selenium未獲取到數據，嘗試基本爬蟲")

            # 備用方案：使用基本requests
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # 解析數據
            ranking_data = self._parse_ranking_page(response.text, ranking_type, limit)

            if ranking_data:
                logging.info(f"✅ 基本爬蟲成功獲取 {len(ranking_data)} 筆 {ranking_type} 排行榜數據")
            else:
                logging.warning(f"⚠️ 未獲取到 {ranking_type} 排行榜數據")

            return ranking_data

        except Exception as e:
            logging.error(f"❌ 獲取 {ranking_type} 排行榜失敗: {e}")
            return []

    def _get_ranking_with_selenium(self, url: str, ranking_type: str, limit: int) -> List[Dict]:
        """使用Selenium獲取排行榜數據"""
        if not SELENIUM_AVAILABLE:
            return []

        try:
            # 初始化driver
            if not self.driver:
                self._init_selenium_driver()

            if not self.driver:
                return []

            logging.info(f"🌐 使用Selenium訪問: {url}")
            self.driver.get(url)

            # 等待頁面加載
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )

            # 等待數據加載
            time.sleep(3)

            # 獲取頁面源碼
            page_source = self.driver.page_source

            # 解析數據
            return self._parse_ranking_page(page_source, ranking_type, limit)

        except Exception as e:
            logging.error(f"❌ Selenium獲取排行榜失敗: {e}")
            return []

    def _init_selenium_driver(self):
        """初始化Selenium driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 無頭模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            logging.info("✅ Selenium Chrome driver初始化成功")

        except Exception as e:
            logging.error(f"❌ Selenium driver初始化失敗: {e}")
            self.driver = None
            self.use_selenium = False
    
    def _parse_ranking_page(self, html_content: str, ranking_type: str, limit: int) -> List[Dict]:
        """解析排行榜頁面"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            ranking_data = []

            # 方法1: 尋找表格結構 - 改進版
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 5:  # 降低門檻
                    data = self._parse_table_rows(rows, ranking_type, limit)
                    if data:
                        ranking_data.extend(data)
                        break

            # 方法2: 尋找特定的Yahoo Finance結構
            if not ranking_data:
                # Yahoo Finance可能使用的CSS選擇器
                selectors = [
                    'tr[data-symbol]',
                    'div[data-symbol]',
                    'li[data-symbol]',
                    '.rank-table tr',
                    '.ranking-list li',
                    '[class*="rank"] [class*="row"]'
                ]

                for selector in selectors:
                    elements = soup.select(selector)
                    if len(elements) > 5:
                        data = self._parse_elements_by_selector(elements, ranking_type, limit)
                        if data:
                            ranking_data.extend(data)
                            break

            # 方法3: 尋找JSON數據
            if not ranking_data:
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and ('rank' in script.string.lower() or 'stock' in script.string.lower()):
                        data = self._extract_json_data(script.string, ranking_type, limit)
                        if data:
                            ranking_data.extend(data)
                            break

            # 方法4: 通用文本解析
            if not ranking_data:
                data = self._parse_text_content(html_content, ranking_type, limit)
                if data:
                    ranking_data.extend(data)

            return ranking_data[:limit]

        except Exception as e:
            logging.error(f"❌ 解析排行榜頁面失敗: {e}")
            return []

    def _parse_elements_by_selector(self, elements: List, ranking_type: str, limit: int) -> List[Dict]:
        """根據CSS選擇器解析元素"""
        ranking_data = []

        try:
            for i, element in enumerate(elements[:limit]):
                # 提取data-symbol屬性
                stock_code = element.get('data-symbol', '')
                if not stock_code:
                    # 從文本中提取
                    text = element.get_text()
                    stock_match = re.search(r'\b(\d{4})\b', text)
                    if stock_match:
                        stock_code = stock_match.group(1)

                if not stock_code:
                    continue

                # 提取其他資訊
                text = element.get_text()
                stock_name = self._extract_name_from_text(text, stock_code)

                # 提取價格
                price_match = re.search(r'(\d+\.?\d*)', text.replace(',', ''))
                current_price = float(price_match.group(1)) if price_match else 0

                stock_info = {
                    'rank': i + 1,
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'current_price': current_price,
                    'ranking_type': ranking_type,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                }

                # 根據排行榜類型添加特定欄位
                if ranking_type == 'volume':
                    volume_match = re.search(r'(\d{1,3}(?:,\d{3})*)', text)
                    stock_info['volume'] = volume_match.group(1) if volume_match else '0'
                elif ranking_type in ['change-up', 'change-down']:
                    change_match = re.search(r'([+-]?\d+\.?\d*)', text)
                    percent_match = re.search(r'([+-]?\d+\.?\d*)%', text)
                    stock_info['change_amount'] = float(change_match.group(1)) if change_match else 0
                    stock_info['change_percent'] = float(percent_match.group(1)) if percent_match else 0

                ranking_data.append(stock_info)

        except Exception as e:
            logging.error(f"❌ 解析選擇器元素失敗: {e}")

        return ranking_data

    def _parse_text_content(self, html_content: str, ranking_type: str, limit: int) -> List[Dict]:
        """通用文本內容解析"""
        ranking_data = []

        try:
            # 使用正則表達式尋找股票代碼模式
            stock_pattern = r'\b(\d{4})\b'
            matches = re.finditer(stock_pattern, html_content)

            found_codes = []
            for match in matches:
                stock_code = match.group(1)
                if stock_code not in found_codes and len(found_codes) < limit:
                    found_codes.append(stock_code)

                    stock_info = {
                        'rank': len(found_codes),
                        'stock_code': stock_code,
                        'stock_name': f'股票{stock_code}',  # 預設名稱
                        'current_price': 0,
                        'ranking_type': ranking_type,
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }

                    if ranking_type == 'volume':
                        stock_info['volume'] = '0'
                    elif ranking_type in ['change-up', 'change-down']:
                        stock_info['change_amount'] = 0
                        stock_info['change_percent'] = 0

                    ranking_data.append(stock_info)

        except Exception as e:
            logging.error(f"❌ 通用文本解析失敗: {e}")

        return ranking_data

    def cleanup(self):
        """清理資源"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("✅ Selenium driver已關閉")
            except:
                pass
            self.driver = None
    
    def _parse_table_rows(self, rows: List, ranking_type: str, limit: int) -> List[Dict]:
        """解析表格行"""
        ranking_data = []
        
        try:
            for i, row in enumerate(rows[1:limit+1]):  # 跳過標題行
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 6:  # 至少需要6個欄位
                    
                    # 提取基本資訊
                    stock_code = self._extract_stock_code(cells)
                    stock_name = self._extract_stock_name(cells)
                    current_price = self._extract_price(cells, 'current')
                    
                    if not stock_code:
                        continue
                    
                    stock_info = {
                        'rank': i + 1,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'current_price': current_price,
                        'ranking_type': ranking_type,
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    
                    # 根據排行榜類型添加特定欄位
                    if ranking_type == 'volume':
                        stock_info['volume'] = self._extract_volume(cells)
                    elif ranking_type in ['change-up', 'change-down']:
                        stock_info['change_amount'] = self._extract_change_amount(cells)
                        stock_info['change_percent'] = self._extract_change_percent(cells)
                    
                    ranking_data.append(stock_info)
                    
        except Exception as e:
            logging.error(f"❌ 解析表格行失敗: {e}")
        
        return ranking_data
    
    def _parse_list_items(self, items: List, ranking_type: str, limit: int) -> List[Dict]:
        """解析列表項目"""
        ranking_data = []
        
        try:
            for i, item in enumerate(items[:limit]):
                text = item.get_text()
                
                # 使用正則表達式提取股票代碼
                stock_code_match = re.search(r'\b(\d{4})\b', text)
                if not stock_code_match:
                    continue
                
                stock_code = stock_code_match.group(1)
                
                # 提取股票名稱
                stock_name = self._extract_name_from_text(text, stock_code)
                
                # 提取價格資訊
                price_match = re.search(r'(\d+\.?\d*)', text)
                current_price = float(price_match.group(1)) if price_match else 0
                
                stock_info = {
                    'rank': i + 1,
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'current_price': current_price,
                    'ranking_type': ranking_type,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                }
                
                ranking_data.append(stock_info)
                
        except Exception as e:
            logging.error(f"❌ 解析列表項目失敗: {e}")
        
        return ranking_data
    
    def _extract_json_data(self, script_content: str, ranking_type: str, limit: int) -> List[Dict]:
        """從JavaScript中提取JSON數據"""
        try:
            # 尋找JSON數據模式
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.App\s*=\s*({.+?});',
                r'var\s+rankData\s*=\s*(\[.+?\]);'
            ]
            
            for pattern in json_patterns:
                match = re.search(pattern, script_content, re.DOTALL)
                if match:
                    try:
                        data = json.loads(match.group(1))
                        return self._process_json_ranking_data(data, ranking_type, limit)
                    except:
                        continue
            
        except Exception as e:
            logging.error(f"❌ 提取JSON數據失敗: {e}")
        
        return []
    
    def _process_json_ranking_data(self, data: Dict, ranking_type: str, limit: int) -> List[Dict]:
        """處理JSON排行榜數據"""
        ranking_data = []
        
        try:
            # 根據不同的JSON結構處理數據
            if isinstance(data, dict):
                # 尋找排行榜數據
                for key, value in data.items():
                    if isinstance(value, list) and len(value) > 10:
                        for i, item in enumerate(value[:limit]):
                            if isinstance(item, dict) and 'symbol' in item:
                                stock_info = {
                                    'rank': i + 1,
                                    'stock_code': item.get('symbol', ''),
                                    'stock_name': item.get('name', ''),
                                    'current_price': float(item.get('price', 0)),
                                    'ranking_type': ranking_type,
                                    'timestamp': datetime.now().strftime('%H:%M:%S')
                                }
                                
                                if ranking_type == 'volume':
                                    stock_info['volume'] = item.get('volume', '')
                                elif ranking_type in ['change-up', 'change-down']:
                                    stock_info['change_amount'] = float(item.get('change', 0))
                                    stock_info['change_percent'] = float(item.get('changePercent', 0))
                                
                                ranking_data.append(stock_info)
                        break
                        
        except Exception as e:
            logging.error(f"❌ 處理JSON排行榜數據失敗: {e}")
        
        return ranking_data
    
    def _extract_stock_code(self, cells) -> str:
        """提取股票代碼"""
        for cell in cells:
            text = cell.get_text().strip()
            if text.isdigit() and len(text) == 4:
                return text
        return ""
    
    def _extract_stock_name(self, cells) -> str:
        """提取股票名稱"""
        for cell in cells:
            text = cell.get_text().strip()
            if text and not text.isdigit() and not '%' in text and not ',' in text:
                if len(text) > 1 and len(text) < 20:
                    return text
        return ""
    
    def _extract_price(self, cells, price_type='current') -> float:
        """提取價格"""
        for cell in cells:
            text = cell.get_text().strip().replace(',', '')
            try:
                if '.' in text or text.isdigit():
                    return float(text)
            except:
                continue
        return 0.0
    
    def _extract_volume(self, cells) -> str:
        """提取成交量"""
        for cell in cells:
            text = cell.get_text().strip()
            if ',' in text and text.replace(',', '').isdigit():
                return text
        return "0"
    
    def _extract_change_amount(self, cells) -> float:
        """提取漲跌金額"""
        for cell in cells:
            text = cell.get_text().strip()
            if '+' in text or '-' in text:
                try:
                    return float(text.replace('+', '').replace('-', ''))
                except:
                    continue
        return 0.0
    
    def _extract_change_percent(self, cells) -> float:
        """提取漲跌幅"""
        for cell in cells:
            text = cell.get_text().strip()
            if '%' in text:
                try:
                    return float(text.replace('%', '').replace('+', '').replace('-', ''))
                except:
                    continue
        return 0.0
    
    def _extract_name_from_text(self, text: str, stock_code: str) -> str:
        """從文本中提取股票名稱"""
        # 移除股票代碼後的文本可能是股票名稱
        parts = text.split(stock_code)
        if len(parts) > 1:
            name_part = parts[1].strip()
            # 取第一個非數字、非符號的部分
            name_match = re.search(r'([^\d\+\-\%\,\.]+)', name_part)
            if name_match:
                return name_match.group(1).strip()[:10]  # 限制長度
        return ""

# 創建全局實例
yahoo_ranking_crawler = YahooRankingCrawler()

# 便捷函數
def get_volume_ranking(limit=30):
    """獲取成交量排行榜"""
    return yahoo_ranking_crawler.get_ranking_data('volume', limit)

def get_gainers_ranking(limit=30):
    """獲取漲幅排行榜"""
    return yahoo_ranking_crawler.get_ranking_data('change-up', limit)

def get_losers_ranking(limit=30):
    """獲取跌幅排行榜"""
    return yahoo_ranking_crawler.get_ranking_data('change-down', limit)

if __name__ == "__main__":
    # 測試程式
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🧪 Yahoo排行榜爬蟲測試")
    print("=" * 50)

    crawler = None
    try:
        # 測試成交量排行
        print("\n📊 測試成交量排行榜...")
        volume_data = get_volume_ranking(10)
        if volume_data:
            print(f"✅ 獲取到 {len(volume_data)} 筆成交量排行數據")
            for item in volume_data[:3]:
                print(f"   {item['rank']}. {item['stock_code']} {item['stock_name']} - {item['current_price']}")
        else:
            print("❌ 未獲取到成交量排行數據")

        # 測試漲幅排行
        print("\n📈 測試漲幅排行榜...")
        gainers_data = get_gainers_ranking(10)
        if gainers_data:
            print(f"✅ 獲取到 {len(gainers_data)} 筆漲幅排行數據")
            for item in gainers_data[:3]:
                print(f"   {item['rank']}. {item['stock_code']} {item['stock_name']} - {item['current_price']}")
        else:
            print("❌ 未獲取到漲幅排行數據")

        # 測試跌幅排行
        print("\n📉 測試跌幅排行榜...")
        losers_data = get_losers_ranking(10)
        if losers_data:
            print(f"✅ 獲取到 {len(losers_data)} 筆跌幅排行數據")
            for item in losers_data[:3]:
                print(f"   {item['rank']}. {item['stock_code']} {item['stock_name']} - {item['current_price']}")
        else:
            print("❌ 未獲取到跌幅排行數據")

        print(f"\n📊 測試總結:")
        print(f"   成交量排行: {'✅' if volume_data else '❌'}")
        print(f"   漲幅排行: {'✅' if gainers_data else '❌'}")
        print(f"   跌幅排行: {'✅' if losers_data else '❌'}")

    finally:
        # 清理資源
        if yahoo_ranking_crawler:
            yahoo_ranking_crawler.cleanup()
