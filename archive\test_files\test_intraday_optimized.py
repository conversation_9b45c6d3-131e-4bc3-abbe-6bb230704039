#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化後的日內策略
"""

import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入策略
from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def test_optimized_intraday():
    """測試優化後的日內策略"""
    print("🚀 測試優化後的日內策略")
    print("=" * 50)
    
    # 初始化策略
    strategy = IntradayOpeningRangeStrategy()
    print(f"✅ 策略初始化成功: {strategy.name}")
    print(f"📊 數據源: {strategy.data_source}")
    
    # 測試股票列表（包含不同類型的股票）
    test_stocks = [
        '2330',  # 台積電 - 高價股
        '2317',  # 鴻海 - 中價股
        '2454',  # 聯發科 - 高價股
        '1301',  # 台塑 - 中價股
        '2303',  # 聯電 - 低價股
        '2412',  # 中華電 - 中價股
        '1216',  # 統一 - 中價股
        '2881',  # 富邦金 - 中價股
        '2002',  # 中鋼 - 低價股
        '3008'   # 大立光 - 高價股
    ]
    
    print(f"\n📈 測試 {len(test_stocks)} 支股票的盤前篩選...")
    print("-" * 50)
    
    qualified_stocks = []
    failed_stocks = []
    
    for i, stock_id in enumerate(test_stocks):
        print(f"\n[{i+1}/{len(test_stocks)}] 測試 {stock_id}:")
        
        try:
            # 獲取數據
            ticker = strategy.convert_stock_symbol(stock_id)
            daily_data = strategy.fetch_daily_data(ticker, 10)
            
            if not daily_data.empty and len(daily_data) >= 5:
                # 計算指標
                daily_data = strategy.calculate_indicators(daily_data)
                
                # 檢查盤前條件
                pre_market_ok, reason = strategy.check_pre_market_conditions(daily_data)
                
                latest_close = daily_data['Close'].iloc[-1]
                latest_volume = daily_data['Volume'].iloc[-1]
                ma5 = daily_data['MA5'].iloc[-1]
                
                print(f"  💰 收盤價: {latest_close:.2f}")
                print(f"  📊 MA5: {ma5:.2f}")
                print(f"  📈 成交量: {latest_volume:,.0f}")
                print(f"  🎯 篩選結果: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
                print(f"  📝 詳細原因: {reason}")
                
                if pre_market_ok:
                    qualified_stocks.append({
                        'stock_id': stock_id,
                        'close_price': latest_close,
                        'reason': reason
                    })
                else:
                    failed_stocks.append({
                        'stock_id': stock_id,
                        'close_price': latest_close,
                        'reason': reason
                    })
            else:
                print(f"  ❌ 數據不足或無法獲取")
                failed_stocks.append({
                    'stock_id': stock_id,
                    'close_price': 0,
                    'reason': '數據不足'
                })
                
        except Exception as e:
            print(f"  ❌ 處理失敗: {e}")
            failed_stocks.append({
                'stock_id': stock_id,
                'close_price': 0,
                'reason': f'處理失敗: {str(e)}'
            })
    
    # 統計結果
    print("\n" + "=" * 50)
    print("📊 篩選結果統計")
    print("=" * 50)
    
    total_tested = len(test_stocks)
    qualified_count = len(qualified_stocks)
    failed_count = len(failed_stocks)
    
    print(f"總測試股票數: {total_tested}")
    print(f"通過篩選: {qualified_count} ({qualified_count/total_tested*100:.1f}%)")
    print(f"未通過篩選: {failed_count} ({failed_count/total_tested*100:.1f}%)")
    
    if qualified_stocks:
        print(f"\n✅ 通過篩選的股票:")
        for stock in qualified_stocks:
            print(f"  • {stock['stock_id']}: {stock['close_price']:.2f} - {stock['reason']}")
    
    if failed_stocks:
        print(f"\n❌ 未通過篩選的股票:")
        for stock in failed_stocks[:5]:  # 只顯示前5個
            print(f"  • {stock['stock_id']}: {stock['reason']}")
        if len(failed_stocks) > 5:
            print(f"  ... 還有 {len(failed_stocks)-5} 支股票未通過")
    
    # 分析篩選條件效果
    print(f"\n🔍 篩選條件分析:")
    print(f"  • 當前篩選通過率: {qualified_count/total_tested*100:.1f}%")
    
    if qualified_count == 0:
        print("  ⚠️ 沒有股票通過篩選，建議進一步放寬條件")
    elif qualified_count < 2:
        print("  ⚠️ 通過股票數量較少，可考慮適度放寬條件")
    elif qualified_count > 5:
        print("  ✅ 通過股票數量適中，篩選條件合理")
    else:
        print("  ✅ 篩選效果良好")
    
    return qualified_stocks

def analyze_scoring_system():
    """分析評分系統的效果"""
    print("\n🎯 評分系統分析")
    print("-" * 30)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 模擬不同類型股票的評分
    test_cases = [
        {
            'name': '理想股票',
            'ma5_ratio': 1.02,
            'vol_ratio': 1.5,
            'price': 100,
            'trend': 3.0
        },
        {
            'name': '一般股票',
            'ma5_ratio': 0.98,
            'vol_ratio': 1.0,
            'price': 50,
            'trend': 1.0
        },
        {
            'name': '邊緣股票',
            'ma5_ratio': 0.95,
            'vol_ratio': 0.8,
            'price': 10,
            'trend': -2.0
        },
        {
            'name': '低品質股票',
            'ma5_ratio': 0.85,
            'vol_ratio': 0.3,
            'price': 2,
            'trend': -8.0
        }
    ]
    
    for case in test_cases:
        score = 0
        details = []
        
        # MA5條件評分
        if case['ma5_ratio'] >= 1.05:
            score += 3
            details.append("MA5評分: 3分")
        elif case['ma5_ratio'] >= 0.95:
            score += 2
            details.append("MA5評分: 2分")
        elif case['ma5_ratio'] >= 0.90:
            score += 1
            details.append("MA5評分: 1分")
        else:
            details.append("MA5評分: 0分")
        
        # 成交量條件評分
        if case['vol_ratio'] >= 1.5:
            score += 3
            details.append("成交量評分: 3分")
        elif case['vol_ratio'] >= 1.0:
            score += 2
            details.append("成交量評分: 2分")
        elif case['vol_ratio'] >= 0.5:
            score += 1
            details.append("成交量評分: 1分")
        else:
            details.append("成交量評分: 0分")
        
        # 價格條件評分
        if case['price'] >= 50:
            score += 3
            details.append("價格評分: 3分")
        elif case['price'] >= 10:
            score += 2
            details.append("價格評分: 2分")
        elif case['price'] >= 3:
            score += 1
            details.append("價格評分: 1分")
        else:
            details.append("價格評分: 0分")
        
        # 趨勢條件評分
        if case['trend'] >= 5:
            score += 3
            details.append("趨勢評分: 3分")
        elif case['trend'] >= 0:
            score += 2
            details.append("趨勢評分: 2分")
        elif case['trend'] >= -5:
            score += 1
            details.append("趨勢評分: 1分")
        else:
            details.append("趨勢評分: 0分")
        
        passed = score >= 3
        
        print(f"\n{case['name']}:")
        print(f"  總分: {score}/12 ({'✅ 通過' if passed else '❌ 未通過'})")
        for detail in details:
            print(f"  • {detail}")

if __name__ == "__main__":
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(levelname)s: %(message)s'
    )
    
    try:
        # 執行測試
        qualified_stocks = test_optimized_intraday()
        
        # 分析評分系統
        analyze_scoring_system()
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
