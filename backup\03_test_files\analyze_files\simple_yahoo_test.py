#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試Yahoo股市新聞爬蟲修復
"""

import logging
import sys
import os

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def test_google_rss_search():
    """測試Google RSS搜尋功能"""
    try:
        print("🧪 測試Google RSS搜尋功能")
        
        # 導入爬蟲
        from yahoo_stock_news_crawler import YahooStockNewsCrawler
        
        # 創建爬蟲實例
        crawler = YahooStockNewsCrawler()
        
        # 測試Google RSS搜尋方法
        print("🔍 測試2330台積電...")
        news_list = crawler._search_via_google_rss("2330", days=7)
        
        if news_list:
            print(f"✅ Google RSS找到 {len(news_list)} 筆新聞")
            for i, news in enumerate(news_list[:3], 1):
                print(f"  {i}. {news['title'][:50]}...")
                print(f"     來源: {news['source']}")
        else:
            print("❌ Google RSS沒有找到新聞")
        
        return len(news_list) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_name_mapping():
    """測試股票名稱對應"""
    try:
        print("\n🧪 測試股票名稱對應")
        
        from yahoo_stock_news_crawler import YahooStockNewsCrawler
        crawler = YahooStockNewsCrawler()
        
        test_codes = ['2330', '00930', '0050', '2317']
        for code in test_codes:
            name = crawler._get_stock_name(code)
            print(f"  {code} -> {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 Yahoo股市新聞爬蟲修復測試")
    print("=" * 50)
    
    # 測試1: 股票名稱對應
    success1 = test_stock_name_mapping()
    
    # 測試2: Google RSS搜尋
    success2 = test_google_rss_search()
    
    print("\n" + "=" * 50)
    print("📊 測試結果:")
    print(f"  股票名稱對應: {'✅' if success1 else '❌'}")
    print(f"  Google RSS搜尋: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 修復成功！Yahoo股市新聞爬蟲已增強")
        print("💡 主要改進:")
        print("  • 新增Google RSS搜尋作為主要方法")
        print("  • WebDriver方式作為備用方案")
        print("  • 改善錯誤處理和日誌記錄")
    else:
        print("\n⚠️ 部分功能需要進一步調整")

if __name__ == "__main__":
    main()
