#!/usr/bin/env python3
"""
增強版yfinance數據獲取器
專為日內交易策略設計，增強反爬蟲策略
解決yfinance連接問題，提供多重備用方案
"""

import yfinance as yf
import pandas as pd
import numpy as np
import requests
import time
import logging
import random
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

class EnhancedYFinanceFetcher:
    """增強版yfinance數據獲取器"""
    
    def __init__(self):
        """初始化獲取器"""
        self.session = requests.Session()
        
        # 🛡️ 增強反爬蟲策略
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # 設置yfinance的session
        self._setup_yfinance_session()
        
        # 緩存機制
        self.cache = {}
        self.cache_timeout = 300  # 5分鐘緩存
        
        # 請求控制
        self.request_delay = 0.5
        self.max_retries = 3
        self.timeout = 10
        
        # 線程鎖
        self.lock = threading.Lock()
        
        logging.info("✅ 增強版yfinance獲取器初始化完成")
    
    def _setup_yfinance_session(self):
        """設置yfinance的session以增強反爬蟲"""
        try:
            # 設置隨機User-Agent
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            self.session.headers.update(headers)

            # 🛡️ 設置yfinance使用我們的session
            import yfinance as yf
            yf.utils.get_json = self._patched_get_json

        except Exception as e:
            logging.warning(f"設置yfinance session失敗: {e}")

    def _patched_get_json(self, url, user_agent_headers=None, params=None, proxies=None, timeout=30):
        """修補yfinance的get_json方法"""
        try:
            # 使用我們的session
            response = self.session.get(url, params=params, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.debug(f"yfinance請求失敗: {e}")
            raise
    
    def _get_ticker_symbol(self, stock_id: str) -> List[str]:
        """獲取可能的ticker符號列表"""
        symbols = []
        
        # 處理指數
        if stock_id.startswith('^'):
            symbols.append(stock_id)
            return symbols
        
        # 台股代碼處理
        if stock_id.isdigit() and len(stock_id) == 4:
            # 根據代碼判斷市場
            if stock_id.startswith(('6', '8', '9')):
                # 櫃買市場優先
                symbols.extend([f"{stock_id}.TWO", f"{stock_id}.TW"])
            else:
                # 上市市場優先
                symbols.extend([f"{stock_id}.TW", f"{stock_id}.TWO"])
        else:
            # 其他格式
            symbols.append(f"{stock_id}.TW")
        
        return symbols
    
    def _make_safe_request(self, ticker_symbol: str, **kwargs) -> Optional[yf.Ticker]:
        """安全創建yfinance Ticker對象"""
        try:
            # 隨機延遲
            time.sleep(random.uniform(0.3, 0.8))
            
            # 更新User-Agent
            self._setup_yfinance_session()
            
            # 創建Ticker對象
            ticker = yf.Ticker(ticker_symbol, session=self.session)
            
            return ticker
            
        except Exception as e:
            logging.debug(f"創建Ticker {ticker_symbol} 失敗: {e}")
            return None
    
    def get_stock_data(self, stock_id: str, period: str = '1mo') -> Optional[Dict[str, Any]]:
        """獲取股票數據 - 簡化版本，更可靠"""
        try:
            # 檢查緩存
            cache_key = f"{stock_id}_{period}"
            with self.lock:
                if cache_key in self.cache:
                    cache_time, data = self.cache[cache_key]
                    if time.time() - cache_time < self.cache_timeout:
                        return data

            # 🛡️ 簡化ticker符號處理
            if stock_id.startswith('^'):
                ticker_symbol = stock_id
            else:
                ticker_symbol = f"{stock_id}.TW"

            try:
                logging.debug(f"🔄 獲取 {ticker_symbol} 數據")

                # 直接使用yfinance，不修改session
                ticker = yf.Ticker(ticker_symbol)

                # 獲取歷史數據
                hist = ticker.history(period=period)

                if not hist.empty:
                    # 處理數據
                    latest = hist.iloc[-1]
                    prev = hist.iloc[-2] if len(hist) > 1 else latest

                    current_price = float(latest['Close'])
                    prev_close = float(prev['Close'])
                    pct_change = ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0

                    data = {
                        'symbol': stock_id,
                        'ticker_symbol': ticker_symbol,
                        'name': stock_id,  # 簡化名稱處理
                        'open': float(latest['Open']),
                        'high': float(latest['High']),
                        'low': float(latest['Low']),
                        'close': current_price,
                        'volume': int(latest['Volume']),
                        'pct_change': round(pct_change, 2),
                        'prev_close': prev_close,
                        'timestamp': datetime.now(),
                        'source': 'yfinance_simple',
                        'data_points': len(hist)
                    }

                    # 緩存數據
                    with self.lock:
                        self.cache[cache_key] = (time.time(), data)

                    logging.info(f"✅ 成功獲取 {ticker_symbol} 數據")
                    return data
                else:
                    # 嘗試.TWO後綴
                    if ticker_symbol.endswith('.TW'):
                        ticker_symbol_two = f"{stock_id}.TWO"
                        ticker_two = yf.Ticker(ticker_symbol_two)
                        hist_two = ticker_two.history(period=period)

                        if not hist_two.empty:
                            latest = hist_two.iloc[-1]
                            prev = hist_two.iloc[-2] if len(hist_two) > 1 else latest

                            current_price = float(latest['Close'])
                            prev_close = float(prev['Close'])
                            pct_change = ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0

                            data = {
                                'symbol': stock_id,
                                'ticker_symbol': ticker_symbol_two,
                                'name': stock_id,
                                'open': float(latest['Open']),
                                'high': float(latest['High']),
                                'low': float(latest['Low']),
                                'close': current_price,
                                'volume': int(latest['Volume']),
                                'pct_change': round(pct_change, 2),
                                'prev_close': prev_close,
                                'timestamp': datetime.now(),
                                'source': 'yfinance_simple',
                                'data_points': len(hist_two)
                            }

                            with self.lock:
                                self.cache[cache_key] = (time.time(), data)

                            logging.info(f"✅ 成功獲取 {ticker_symbol_two} 數據")
                            return data

            except Exception as e:
                logging.debug(f"獲取 {ticker_symbol} 失敗: {e}")

            logging.warning(f"⚠️ 無法獲取 {stock_id} 數據")
            return None

        except Exception as e:
            logging.error(f"❌ 獲取 {stock_id} 數據失敗: {e}")
            return None
    
    def get_intraday_data(self, stock_id: str, interval: str = '5m', period: str = '1d') -> Optional[pd.DataFrame]:
        """獲取盤中數據 - 增強版"""
        try:
            # 檢查緩存
            cache_key = f"intraday_{stock_id}_{interval}_{period}"
            with self.lock:
                if cache_key in self.cache:
                    cache_time, data = self.cache[cache_key]
                    if time.time() - cache_time < 60:  # 1分鐘緩存
                        return data
            
            # 獲取可能的ticker符號
            ticker_symbols = self._get_ticker_symbol(stock_id)
            
            # yfinance支持的間隔
            valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
            if interval not in valid_intervals:
                interval = '5m'  # 默認5分鐘
            
            for ticker_symbol in ticker_symbols:
                for attempt in range(self.max_retries):
                    try:
                        logging.debug(f"🔄 獲取 {ticker_symbol} 盤中數據 {interval} (第{attempt+1}次)")
                        
                        # 創建Ticker對象
                        ticker = self._make_safe_request(ticker_symbol)
                        if not ticker:
                            continue
                        
                        # 獲取盤中數據
                        data = ticker.history(period=period, interval=interval, timeout=self.timeout)
                        
                        if not data.empty:
                            # 清理數據
                            df = data.reset_index()
                            df = df.dropna()
                            
                            # 重命名列
                            if 'Datetime' in df.columns:
                                df = df.rename(columns={'Datetime': 'datetime'})
                            elif 'Date' in df.columns:
                                df = df.rename(columns={'Date': 'datetime'})
                            
                            df = df.rename(columns={
                                'Open': 'open',
                                'High': 'high',
                                'Low': 'low',
                                'Close': 'close',
                                'Volume': 'volume'
                            })
                            
                            # 確保有必要的列
                            required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                            if all(col in df.columns for col in required_cols):
                                df = df[required_cols]
                                
                                # 緩存數據
                                with self.lock:
                                    self.cache[cache_key] = (time.time(), df)
                                
                                logging.info(f"✅ 成功獲取 {ticker_symbol} 盤中數據 ({len(df)} 筆)")
                                return df
                        
                    except Exception as e:
                        logging.debug(f"獲取 {ticker_symbol} 盤中數據失敗 (第{attempt+1}次): {e}")
                        if attempt < self.max_retries - 1:
                            time.sleep(random.uniform(1, 2))
                        continue
            
            logging.warning(f"⚠️ 無法獲取 {stock_id} 盤中數據")
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"❌ 獲取 {stock_id} 盤中數據失敗: {e}")
            return pd.DataFrame()
    
    def get_batch_data(self, stock_list: List[str], period: str = '1mo') -> Dict[str, Any]:
        """批量獲取股票數據"""
        results = {}
        
        # 使用線程池並行獲取
        with ThreadPoolExecutor(max_workers=3) as executor:
            # 提交所有任務
            future_to_stock = {
                executor.submit(self.get_stock_data, stock, period): stock
                for stock in stock_list
            }
            
            # 收集結果
            for future in as_completed(future_to_stock, timeout=60):
                stock = future_to_stock[future]
                try:
                    data = future.result(timeout=10)
                    if data:
                        results[stock] = data
                        logging.debug(f"✅ 批量獲取 {stock} 成功")
                    else:
                        logging.warning(f"⚠️ 批量獲取 {stock} 失敗")
                except Exception as e:
                    logging.error(f"❌ 批量獲取 {stock} 異常: {e}")
        
        return results
    
    def test_connection(self) -> bool:
        """測試yfinance連接"""
        try:
            logging.info("🧪 測試yfinance連接...")
            
            # 測試獲取台積電數據
            data = self.get_stock_data('2330')
            
            if data:
                logging.info(f"✅ yfinance連接正常，成功獲取數據: {data['name']}")
                return True
            else:
                logging.warning("⚠️ yfinance連接異常，無法獲取數據")
                return False
                
        except Exception as e:
            logging.error(f"❌ yfinance連接測試失敗: {e}")
            return False
    
    def clear_cache(self):
        """清除緩存"""
        with self.lock:
            self.cache.clear()
        logging.info("🗑️ yfinance緩存已清除")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """獲取緩存統計"""
        with self.lock:
            return {
                'cache_size': len(self.cache),
                'cache_keys': list(self.cache.keys())
            }

# 創建全局實例
enhanced_yfinance = EnhancedYFinanceFetcher()

# 便捷函數
def get_stock_data(stock_id: str, period: str = '1mo') -> Optional[Dict[str, Any]]:
    """便捷函數：獲取股票數據"""
    return enhanced_yfinance.get_stock_data(stock_id, period)

def get_intraday_data(stock_id: str, interval: str = '5m', period: str = '1d') -> Optional[pd.DataFrame]:
    """便捷函數：獲取盤中數據"""
    return enhanced_yfinance.get_intraday_data(stock_id, interval, period)

def get_batch_data(stock_list: List[str], period: str = '1mo') -> Dict[str, Any]:
    """便捷函數：批量獲取數據"""
    return enhanced_yfinance.get_batch_data(stock_list, period)

def test_yfinance_connection() -> bool:
    """便捷函數：測試連接"""
    return enhanced_yfinance.test_connection()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試增強版yfinance獲取器...")
    
    fetcher = EnhancedYFinanceFetcher()
    
    # 測試連接
    if fetcher.test_connection():
        print("✅ yfinance連接正常")
        
        # 測試獲取股票數據
        print("\n📊 測試股票數據獲取:")
        test_stocks = ['2330', '2317', '0050']
        for stock in test_stocks:
            data = fetcher.get_stock_data(stock)
            if data:
                print(f"  ✅ {stock}: {data['name']} - ${data['close']:.2f} ({data['pct_change']:+.2f}%)")
            else:
                print(f"  ❌ {stock}: 獲取失敗")
        
        # 測試盤中數據
        print("\n📈 測試盤中數據獲取:")
        intraday_data = fetcher.get_intraday_data('2330', '5m')
        if not intraday_data.empty:
            print(f"  ✅ 台積電盤中數據: {len(intraday_data)} 筆")
        else:
            print("  ❌ 盤中數據獲取失敗")
    else:
        print("❌ yfinance連接失敗")
    
    print("🎉 測試完成！")
