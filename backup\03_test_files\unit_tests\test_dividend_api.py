#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息API回應
"""

import requests
import pandas as pd
import ssl
import urllib3
from datetime import datetime, timedelta

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def test_twse_api():
    """測試證交所API"""
    print("🔍 測試證交所除權息API...")
    
    # 測試2024年的資料（更可能有除權息）
    start_date = "20240101"
    end_date = "20241231"
    
    url = f"https://www.twse.com.tw/exchangeReport/TWT49U?response=html&strDate={start_date}&endDate={end_date}"
    print(f"📡 URL: {url}")
    
    try:
        # 直接用requests測試
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        print(f"📊 HTTP狀態碼: {response.status_code}")
        print(f"📄 回應長度: {len(response.text)} 字元")
        
        # 檢查回應內容
        if "除權息" in response.text or "股利" in response.text:
            print("✅ 回應包含除權息相關內容")
        else:
            print("⚠️ 回應不包含除權息相關內容")
        
        # 嘗試用pandas解析
        try:
            tables = pd.read_html(response.text)
            print(f"📋 找到 {len(tables)} 個表格")
            
            if tables:
                df = tables[0]
                print(f"📊 第一個表格大小: {df.shape}")
                print("📋 表格前5行:")
                print(df.head())
                return df
            else:
                print("❌ 沒有找到表格")
                
        except Exception as e:
            print(f"❌ pandas解析失敗: {e}")
            
        # 保存回應內容以供檢查
        with open("twse_response.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("💾 回應內容已保存至 twse_response.html")
        
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    return None

def test_tpex_api():
    """測試櫃買中心API"""
    print("\n🔍 測試櫃買中心除權息API...")
    
    # 測試2024年的資料
    start_date_roc = "113/01/01"
    end_date_roc = "113/12/31"
    
    url = f"https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php?l=zh-tw&d={start_date_roc}&ed={end_date_roc}"
    print(f"📡 URL: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        print(f"📊 HTTP狀態碼: {response.status_code}")
        print(f"📄 回應長度: {len(response.text)} 字元")
        
        # 嘗試解析JSON
        try:
            data = response.json()
            print("✅ 成功解析JSON")
            print(f"📋 JSON鍵值: {list(data.keys())}")
            
            if "aaData" in data:
                records = data["aaData"]
                print(f"📊 找到 {len(records)} 筆記錄")
                
                if records:
                    print("📋 前3筆記錄:")
                    for i, record in enumerate(records[:3]):
                        print(f"  {i+1}. {record}")
                    return records
                else:
                    print("⚠️ aaData為空")
            else:
                print("❌ 沒有找到aaData")
                print(f"📄 完整回應: {data}")
                
        except Exception as e:
            print(f"❌ JSON解析失敗: {e}")
            print(f"📄 回應內容前500字元: {response.text[:500]}")
            
        # 保存回應內容
        with open("tpex_response.json", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("💾 回應內容已保存至 tpex_response.json")
        
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    return None

def test_recent_data():
    """測試最近的除權息資料"""
    print("\n🔍 測試最近期間的除權息資料...")
    
    # 測試最近6個月
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)
    
    start_str = start_date.strftime("%Y%m%d")
    end_str = end_date.strftime("%Y%m%d")
    
    print(f"📅 測試日期範圍: {start_str} ~ {end_str}")
    
    # 測試證交所
    twse_url = f"https://www.twse.com.tw/exchangeReport/TWT49U?response=html&strDate={start_str}&endDate={end_str}"
    print(f"📡 證交所URL: {twse_url}")
    
    try:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        response = requests.get(twse_url, headers=headers, verify=False, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 證交所回應成功 ({len(response.text)} 字元)")
            
            # 檢查是否有表格
            if "<table" in response.text.lower():
                print("📋 回應包含表格")
                try:
                    tables = pd.read_html(response.text)
                    print(f"📊 找到 {len(tables)} 個表格")
                    if tables and len(tables[0]) > 0:
                        print(f"✅ 第一個表格有 {len(tables[0])} 行資料")
                        return True
                except:
                    pass
            else:
                print("⚠️ 回應不包含表格")
        else:
            print(f"❌ 證交所回應失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 證交所請求失敗: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 除權息API測試")
    print("=" * 50)
    
    # 測試2024年資料
    twse_data = test_twse_api()
    tpex_data = test_tpex_api()
    
    # 測試最近資料
    recent_success = test_recent_data()
    
    print("\n" + "=" * 50)
    print("🎯 測試結果總結:")
    print(f"📊 證交所2024年資料: {'✅ 成功' if twse_data is not None else '❌ 失敗'}")
    print(f"📊 櫃買中心2024年資料: {'✅ 成功' if tpex_data is not None else '❌ 失敗'}")
    print(f"📊 最近期間資料: {'✅ 成功' if recent_success else '❌ 失敗'}")
    
    if twse_data is None and tpex_data is None:
        print("\n💡 建議:")
        print("1. 檢查網路連線")
        print("2. 確認API網址是否正確")
        print("3. 嘗試不同的日期範圍")
        print("4. 檢查保存的回應檔案")
    
    print("\n按 Enter 鍵結束...")
    input()
