#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試分段下載功能
"""

import os
import sys
import time
from datetime import datetime
from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

def test_csv_cleaning():
    """測試CSV清理功能"""
    print("🧹 測試CSV清理功能")
    print("=" * 50)
    
    downloader = GoodinfoROECSVDownloader()
    
    # 創建測試CSV檔案（模擬有重複標題行的情況）
    test_csv_content = """代號,名稱,ROE (%),EPS (元),財報 年度
2330,台積電,25.5,22.0,2023
2317,鴻海,15.2,8.5,2023
代號,名稱,ROE (%),EPS (元),財報 年度
2454,聯發科,20.1,35.2,2023
1301,台塑,12.8,4.1,2023
代號,名稱,ROE (%),EPS (元),財報 年度
2412,中華電,18.5,4.8,2023
2881,富邦金,16.2,12.3,2023"""
    
    test_file = os.path.join(downloader.download_dir, "test_roe_data_with_headers.csv")
    
    try:
        # 寫入測試檔案
        with open(test_file, 'w', encoding='utf-8-sig') as f:
            f.write(test_csv_content)
        
        print(f"📝 創建測試檔案: {test_file}")
        print("原始內容:")
        print(test_csv_content)
        print("\n" + "="*50)
        
        # 清理CSV
        cleaned_file = downloader.clean_csv_data(test_file)
        
        if cleaned_file:
            print(f"✅ CSV清理成功: {cleaned_file}")
            
            # 讀取清理後的檔案
            with open(cleaned_file, 'r', encoding='utf-8-sig') as f:
                cleaned_content = f.read()
            
            print("清理後的內容:")
            print(cleaned_content)
            
            # 驗證清理結果
            lines = cleaned_content.strip().split('\n')
            header_count = sum(1 for line in lines if '代號' in line and '名稱' in line)
            data_count = len(lines) - header_count
            
            print(f"\n📊 清理結果統計:")
            print(f"   標題行數: {header_count} (應該為1)")
            print(f"   資料行數: {data_count}")
            print(f"   總行數: {len(lines)}")
            
            if header_count == 1:
                print("✅ 標題行清理成功")
            else:
                print("❌ 標題行清理失敗")
            
            # 清理測試檔案
            os.remove(test_file)
            os.remove(cleaned_file)
            
            return header_count == 1
        else:
            print("❌ CSV清理失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_segment_download():
    """測試分段下載功能"""
    print("📥 測試分段下載功能")
    print("=" * 50)
    
    downloader = GoodinfoROECSVDownloader()
    
    # 測試小範圍分段下載
    print("🔧 測試小範圍分段下載 (1-50筆)...")
    
    try:
        # 使用較小的範圍進行測試
        csv_file = downloader.download_roe_csv_segment(
            year=datetime.now().year, 
            start_row=1, 
            end_row=50
        )
        
        if csv_file and os.path.exists(csv_file):
            print(f"✅ 分段下載成功: {csv_file}")
            
            # 檢查檔案內容
            try:
                import pandas as pd
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                print(f"📊 下載資料統計: {len(df)} 筆")
                print(f"📋 欄位: {list(df.columns)}")
                
                if len(df) > 0:
                    print("📝 前3筆資料預覽:")
                    print(df.head(3).to_string())
                
                # 測試CSV清理
                print("\n🧹 測試CSV清理...")
                cleaned_file = downloader.clean_csv_data(csv_file)
                
                if cleaned_file:
                    df_cleaned = pd.read_csv(cleaned_file, encoding='utf-8-sig')
                    print(f"✅ 清理後資料: {len(df_cleaned)} 筆")
                    
                    # 清理測試檔案
                    os.remove(cleaned_file)
                else:
                    print("⚠️ CSV清理測試失敗")
                
                # 清理下載檔案
                os.remove(csv_file)
                
                return True
                
            except Exception as e:
                print(f"⚠️ 檔案分析失敗: {e}")
                return False
        else:
            print("❌ 分段下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ 分段下載測試失敗: {e}")
        return False

def test_database_import():
    """測試數據庫匯入功能"""
    print("💾 測試數據庫匯入功能")
    print("=" * 50)
    
    downloader = GoodinfoROECSVDownloader()
    
    # 創建測試資料
    test_data = """stock_code,stock_name,roe_value,roe_change,eps_value,report_year,rank_position,crawl_date,data_source
2330,台積電,25.5,2.1,22.0,2023,1,2024-01-01 12:00:00,test
2317,鴻海,15.2,-1.5,8.5,2023,2,2024-01-01 12:00:00,test
2454,聯發科,20.1,3.2,35.2,2023,3,2024-01-01 12:00:00,test"""
    
    test_file = os.path.join(downloader.download_dir, "test_import_data.csv")
    
    try:
        # 寫入測試檔案
        with open(test_file, 'w', encoding='utf-8-sig') as f:
            f.write(test_data)
        
        print(f"📝 創建測試資料: {test_file}")
        
        # 測試匯入
        success = downloader.import_csv_to_database(test_file)
        
        if success:
            print("✅ 數據庫匯入成功")
            
            # 驗證匯入結果
            import sqlite3
            conn = sqlite3.connect(downloader.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM roe_data WHERE data_source = 'test'")
            count = cursor.fetchone()[0]
            
            cursor.execute("SELECT stock_code, stock_name, roe_value FROM roe_data WHERE data_source = 'test' ORDER BY rank_position")
            test_records = cursor.fetchall()
            
            conn.close()
            
            print(f"📊 匯入驗證: {count} 筆測試資料")
            for record in test_records:
                print(f"   {record[0]} {record[1]} ROE:{record[2]}%")
            
            # 清理測試資料
            conn = sqlite3.connect(downloader.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM roe_data WHERE data_source = 'test'")
            conn.commit()
            conn.close()
            
            print("🧹 已清理測試資料")
            
        else:
            print("❌ 數據庫匯入失敗")
        
        # 清理測試檔案
        os.remove(test_file)
        
        return success
        
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 ROE分段下載器測試套件")
    print("=" * 60)
    
    tests = [
        ("CSV清理功能", test_csv_cleaning),
        ("分段下載功能", test_segment_download),
        ("數據庫匯入功能", test_database_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
                
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
        
        print("-" * 40)
        time.sleep(1)  # 短暫暫停
    
    # 總結
    print(f"\n📊 測試結果總結")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！分段下載功能準備就緒。")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")
    
    return passed == total

if __name__ == "__main__":
    main()
