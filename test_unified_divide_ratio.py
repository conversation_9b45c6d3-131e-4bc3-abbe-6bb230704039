#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統一的除權息爬蟲
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_unified_divide_ratio_crawler():
    """測試統一除權息爬蟲"""
    
    print("=" * 80)
    print("🧪 測試統一除權息爬蟲")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_divide_ratio
        print("✅ 成功導入 crawl_divide_ratio 函數")
        
        # 執行爬蟲
        print("🔄 開始執行統一除權息爬蟲...")
        result = crawl_divide_ratio()
        
        if result is not None and not result.empty:
            print(f"✅ 爬蟲執行成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 檢查市場分布
            if 'market' in result.columns:
                market_stats = result['market'].value_counts()
                print(f"📊 市場分布:")
                for market, count in market_stats.items():
                    market_name = '上市' if market == 'TWSE' else '上櫃'
                    print(f"   {market_name} ({market}): {count:,} 筆")
            
            # 顯示欄位資訊
            print(f"📋 主要欄位: {list(result.columns)}")
            
            # 顯示範例資料
            print(f"\n📊 前5筆資料:")
            print(result.head())
            
            return True
        else:
            print(f"⚠️ 爬蟲執行返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_unified_divide_ratio_database():
    """檢查統一除權息資料庫"""
    
    print(f"\n" + "=" * 80)
    print("📊 檢查統一除權息資料庫")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            
            # 檢查表格結構
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(divide_ratio)")
            columns = cursor.fetchall()
            
            print(f"📋 資料庫表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM divide_ratio")
            total_count = cursor.fetchone()[0]
            print(f"\n📊 總資料筆數: {total_count:,}")
            
            # 檢查市場分布
            try:
                cursor.execute("SELECT market, COUNT(*) FROM divide_ratio GROUP BY market")
                market_stats = cursor.fetchall()
                print(f"📊 市場分布:")
                for market, count in market_stats:
                    market_name = '上市' if market == 'TWSE' else '上櫃'
                    print(f"   {market_name} ({market}): {count:,} 筆")
            except:
                print(f"⚠️ 無法獲取市場分布 (可能沒有 market 欄位)")
            
            # 檢查日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM divide_ratio")
            date_range = cursor.fetchone()
            print(f"📅 日期範圍: {date_range[0]} 至 {date_range[1]}")
            
            # 檢查台積電資料
            cursor.execute("SELECT * FROM divide_ratio WHERE stock_id LIKE '2330%' ORDER BY date DESC LIMIT 3")
            tsmc_data = cursor.fetchall()
            
            if tsmc_data:
                print(f"\n📈 台積電最近3筆除權息資料:")
                for row in tsmc_data:
                    print(f"   日期: {row[1]}, 股票: {row[0]}")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 檢查資料庫失敗: {e}")
            return False
    else:
        print(f"❌ 資料庫檔案不存在: {db_file}")
        return False

def test_auto_update_integration():
    """測試 auto_update 整合"""
    
    print(f"\n" + "=" * 80)
    print("🔧 測試 auto_update 整合")
    print("=" * 80)
    
    try:
        # 檢查 auto_update.py 配置
        auto_update_path = 'auto_update.py'
        
        if os.path.exists(auto_update_path):
            with open(auto_update_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查導入
            if 'crawl_divide_ratio' in content:
                print(f"   ✅ auto_update.py 包含 crawl_divide_ratio 導入")
            else:
                print(f"   ❌ auto_update.py 缺少 crawl_divide_ratio 導入")
            
            # 檢查任務配置
            if "'divide_ratio'" in content and 'crawl_divide_ratio' in content:
                print(f"   ✅ divide_ratio 任務已配置")
            else:
                print(f"   ❌ divide_ratio 任務未配置")
            
            # 檢查舊任務是否已移除
            old_tasks = ['twse_divide_ratio', 'otc_divide_ratio']
            for task in old_tasks:
                if f"('{task}'" in content and not f"#    ('{task}'" in content:
                    print(f"   ⚠️ 仍包含舊的 {task} 任務")
                else:
                    print(f"   ✅ 已移除/註解舊的 {task} 任務")
            
            return True
        else:
            print(f"   ❌ 未找到 auto_update.py 檔案")
            return False
            
    except Exception as e:
        print(f"❌ 檢查 auto_update 整合失敗: {e}")
        return False

def show_usage_examples():
    """顯示使用範例"""
    
    print(f"\n" + "=" * 80)
    print("📝 統一除權息資料使用範例")
    print("=" * 80)
    
    print("🚀 **執行方式**:")
    print("```bash")
    print("# 執行統一除權息爬蟲")
    print("python auto_update.py divide_ratio")
    print()
    print("# 執行完整自動更新 (包含統一除權息)")
    print("python auto_update.py")
    print("```")
    print()
    
    print("🔍 **SQL 查詢範例**:")
    print("```sql")
    print("-- 查詢台積電除權息資料")
    print("SELECT * FROM divide_ratio WHERE stock_id LIKE '2330%' ORDER BY date DESC;")
    print()
    print("-- 查詢上市公司除權息資料")
    print("SELECT * FROM divide_ratio WHERE market = 'TWSE' ORDER BY date DESC;")
    print()
    print("-- 查詢上櫃公司除權息資料")
    print("SELECT * FROM divide_ratio WHERE market = 'OTC' ORDER BY date DESC;")
    print()
    print("-- 統計各市場除權息筆數")
    print("SELECT market, COUNT(*) as count FROM divide_ratio GROUP BY market;")
    print("```")

def main():
    """主函數"""
    
    print("🧪 統一除權息爬蟲測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: 爬蟲功能
    crawler_success = test_unified_divide_ratio_crawler()
    
    # 測試2: 資料庫檢查
    database_success = check_unified_divide_ratio_database()
    
    # 測試3: auto_update 整合
    integration_success = test_auto_update_integration()
    
    # 顯示使用範例
    show_usage_examples()
    
    print(f"\n" + "=" * 80)
    print("📊 測試結果總結")
    print("=" * 80)
    
    print(f"🧪 爬蟲功能: {'✅ 成功' if crawler_success else '❌ 失敗'}")
    print(f"📊 資料庫檢查: {'✅ 成功' if database_success else '❌ 失敗'}")
    print(f"🔧 auto_update 整合: {'✅ 成功' if integration_success else '❌ 失敗'}")
    
    if crawler_success and database_success and integration_success:
        print(f"\n🎉 統一除權息爬蟲測試全部通過！")
        print(f"💡 現在可以使用: python auto_update.py divide_ratio")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
