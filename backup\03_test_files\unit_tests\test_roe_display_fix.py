#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的ROE資料顯示
"""

import sqlite3
import pandas as pd
import os

def test_full_data_query():
    """測試完整資料查詢"""
    db_path = "D:/Finlab/history/tables/roe_data.db"
    
    print("🔍 測試完整ROE資料查詢...")
    
    if not os.path.exists(db_path):
        print("❌ ROE數據庫不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 使用修正後的查詢 - 無LIMIT限制
        query = """
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, 
                   report_year, rank_position, crawl_date
            FROM roe_data 
            WHERE stock_code IS NOT NULL 
              AND stock_code != '' 
              AND stock_code != 'nan'
              AND roe_value IS NOT NULL
            ORDER BY CASE WHEN rank_position IS NULL THEN 999999 ELSE rank_position END,
                     roe_value DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f"📊 查詢結果: {len(df)} 筆資料")
        
        if len(df) == 0:
            print("❌ 沒有找到有效資料")
            return False
        
        # 顯示前5筆和後5筆
        print("\n📋 前5筆ROE資料:")
        for i, row in df.head(5).iterrows():
            rank_pos = row.get('rank_position')
            stock_code = row.get('stock_code', '')
            stock_name = row.get('stock_name', '')
            roe_value = row.get('roe_value')
            
            rank_str = str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '?'
            print(f"  {i+1}. 排名:{rank_str} {stock_code} {stock_name} - ROE:{roe_value:.1f}%")
        
        print(f"\n📋 後5筆ROE資料:")
        for i, row in df.tail(5).iterrows():
            rank_pos = row.get('rank_position')
            stock_code = row.get('stock_code', '')
            stock_name = row.get('stock_name', '')
            roe_value = row.get('roe_value')
            
            rank_str = str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '?'
            print(f"  {len(df)-4+i-len(df)+5}. 排名:{rank_str} {stock_code} {stock_name} - ROE:{roe_value:.1f}%")
        
        print(f"\n✅ 完整資料查詢測試成功 - 共{len(df)}筆")
        return True
        
    except Exception as e:
        print(f"❌ 查詢測試失敗: {e}")
        return False

def test_column_widths():
    """測試欄位寬度設定"""
    print("\n🖥️ 測試欄位寬度設定...")
    
    column_widths = {
        '排名': 60,
        '代號': 80, 
        '名稱': 120,
        'ROE(%)': 80,
        'ROE變化': 80,
        'EPS(元)': 80,
        '年度': 60,
        '更新時間': 150
    }
    
    total_width = sum(column_widths.values())
    print(f"📏 總欄位寬度: {total_width}px")
    print(f"📐 窗口寬度: 1200px")
    print(f"📊 寬度比例: {total_width/1200:.1%}")
    
    print("📋 各欄位寬度:")
    for col, width in column_widths.items():
        print(f"  • {col}: {width}px")
    
    if total_width <= 1200:
        print("✅ 欄位寬度設定合理")
        return True
    else:
        print("⚠️ 欄位寬度可能過寬")
        return False

def simulate_display_format():
    """模擬顯示格式"""
    print("\n📱 模擬資料顯示格式...")
    
    # 模擬一些測試數據
    test_data = [
        {
            'rank_position': 1,
            'stock_code': '1213',
            'stock_name': '大飲',
            'roe_value': 104.0,
            'roe_change': 151.0,
            'eps_value': 8.71,
            'report_year': 2024,
            'crawl_date': '2025-07-16 00:27:21'
        },
        {
            'rank_position': 2,
            'stock_code': '5314',
            'stock_name': '世紀*',
            'roe_value': 84.6,
            'roe_change': 64.4,
            'eps_value': 18.25,
            'report_year': 2024,
            'crawl_date': '2025-07-16 00:27:21'
        }
    ]
    
    print("📋 模擬顯示效果:")
    print("排名  代號    名稱      ROE(%)  ROE變化  EPS(元)  年度  更新時間")
    print("-" * 70)
    
    for row_data in test_data:
        rank_pos = row_data.get('rank_position')
        stock_code = row_data.get('stock_code', '')
        stock_name = row_data.get('stock_name', '')
        roe_value = row_data.get('roe_value')
        roe_change = row_data.get('roe_change')
        eps_value = row_data.get('eps_value')
        report_year = row_data.get('report_year')
        crawl_date = row_data.get('crawl_date', '')
        
        values = [
            str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '',
            str(stock_code) if stock_code and str(stock_code) != 'nan' else '',
            str(stock_name) if stock_name and str(stock_name) != 'nan' else '',
            f"{float(roe_value):.1f}" if roe_value is not None and pd.notna(roe_value) else '',
            f"{float(roe_change):+.1f}" if roe_change is not None and pd.notna(roe_change) else '',
            f"{float(eps_value):.2f}" if eps_value is not None and pd.notna(eps_value) else '',
            str(int(report_year)) if report_year is not None and pd.notna(report_year) else '',
            str(crawl_date)[:16] if crawl_date and str(crawl_date) != 'nan' else ''
        ]
        
        print(f"{values[0]:<4} {values[1]:<6} {values[2]:<8} {values[3]:<7} {values[4]:<7} {values[5]:<7} {values[6]:<4} {values[7]}")
    
    print("✅ 顯示格式測試完成")
    return True

def main():
    """主函數"""
    print("🔧 ROE資料顯示修正測試")
    print("=" * 50)
    
    # 測試完整資料查詢
    query_success = test_full_data_query()
    
    # 測試欄位寬度
    width_success = test_column_widths()
    
    # 測試顯示格式
    format_success = simulate_display_format()
    
    if query_success and width_success and format_success:
        print(f"\n🎉 ROE資料顯示修正完成！")
        print(f"💡 現在ROE資料查看窗口應該能顯示所有資料且字體清晰")
        print(f"📊 窗口尺寸: 1200x700")
        print(f"📋 顯示筆數: 無限制(顯示所有有效資料)")
        print(f"🔤 字體顯示: 優化欄位寬度，避免壓縮")
    else:
        print(f"\n❌ 還有問題需要解決")

if __name__ == "__main__":
    main()
