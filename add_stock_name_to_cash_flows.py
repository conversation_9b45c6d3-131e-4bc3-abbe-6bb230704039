#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
為 cash_flows.db 添加 stock_name 欄位，參考 newprice.db 的 stock_id 與 stock_name 對應關係
"""

import sqlite3
import pandas as pd

def add_stock_name_to_cash_flows():
    """為 cash_flows.db 添加 stock_name 欄位"""
    
    print("=" * 80)
    print("🔄 為 cash_flows.db 添加 stock_name 欄位")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        # 1. 從 newprice.db 獲取 stock_id 與 stock_name 的對應關係
        print("📖 從 newprice.db 讀取股票名稱對應表...")
        
        conn_newprice = sqlite3.connect(newprice_db)
        
        # 獲取唯一的 stock_id 和 stock_name 對應關係
        query = """
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name IS NOT NULL 
        ORDER BY stock_id
        """
        
        stock_mapping = pd.read_sql_query(query, conn_newprice)
        conn_newprice.close()
        
        print(f"✅ 成功讀取 {len(stock_mapping)} 個股票的名稱對應關係")
        print(f"📊 範例對應:")
        for i, row in stock_mapping.head(10).iterrows():
            print(f"   {row['stock_id']} -> {row['stock_name']}")
        
        # 2. 檢查 cash_flows.db 的現有結構
        print(f"\n📖 檢查 cash_flows.db 的現有結構...")
        
        conn_cash = sqlite3.connect(cash_flows_db)
        cursor = conn_cash.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(cash_flows)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 現有欄位數: {len(column_names)}")
        print(f"📋 前10個欄位: {column_names[:10]}")
        
        # 檢查是否已經有 stock_name 欄位
        if 'stock_name' in column_names:
            print("⚠️  stock_name 欄位已存在，將會更新現有資料")
        else:
            print("➕ 將添加新的 stock_name 欄位")
            # 添加 stock_name 欄位
            cursor.execute("ALTER TABLE cash_flows ADD COLUMN stock_name TEXT")
            print("✅ 成功添加 stock_name 欄位")
        
        # 3. 檢查 cash_flows.db 中的股票數量
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM cash_flows")
        cash_flows_stocks = cursor.fetchone()[0]
        print(f"📊 cash_flows.db 中的唯一股票數: {cash_flows_stocks}")
        
        # 檢查有多少股票可以匹配到名稱
        cursor.execute("""
        SELECT COUNT(DISTINCT cf.stock_id) 
        FROM cash_flows cf
        WHERE cf.stock_id IN (
            SELECT stock_id FROM (
                SELECT ? as stock_id, ? as stock_name
                UNION ALL SELECT ?, ?
            ) 
            WHERE stock_id = cf.stock_id
        )
        """, (stock_mapping.iloc[0]['stock_id'], stock_mapping.iloc[0]['stock_name'],
              stock_mapping.iloc[1]['stock_id'], stock_mapping.iloc[1]['stock_name']))
        
        # 更簡單的方法：直接檢查匹配數量
        stock_ids_in_mapping = set(stock_mapping['stock_id'].tolist())
        cursor.execute("SELECT DISTINCT stock_id FROM cash_flows")
        stock_ids_in_cash_flows = set([row[0] for row in cursor.fetchall()])
        
        matched_stocks = stock_ids_in_cash_flows.intersection(stock_ids_in_mapping)
        unmatched_stocks = stock_ids_in_cash_flows - stock_ids_in_mapping
        
        print(f"📊 可以匹配到名稱的股票數: {len(matched_stocks)}")
        print(f"📊 無法匹配到名稱的股票數: {len(unmatched_stocks)}")
        
        if unmatched_stocks:
            print(f"⚠️  無法匹配的股票範例: {list(unmatched_stocks)[:10]}")
        
        # 4. 更新 stock_name
        print(f"\n🔄 開始更新 stock_name...")
        
        # 創建股票名稱字典
        stock_name_dict = dict(zip(stock_mapping['stock_id'], stock_mapping['stock_name']))
        
        # 批量更新
        update_count = 0
        batch_size = 1000
        
        for i, (stock_id, stock_name) in enumerate(stock_name_dict.items()):
            cursor.execute("""
            UPDATE cash_flows 
            SET stock_name = ? 
            WHERE stock_id = ?
            """, (stock_name, stock_id))
            
            update_count += cursor.rowcount
            
            if (i + 1) % batch_size == 0:
                conn_cash.commit()
                print(f"   已處理 {i + 1}/{len(stock_name_dict)} 個股票...")
        
        # 最終提交
        conn_cash.commit()
        
        print(f"✅ 成功更新 {update_count} 筆記錄的 stock_name")
        
        # 5. 驗證結果
        print(f"\n🔍 驗證更新結果...")
        
        cursor.execute("""
        SELECT COUNT(*) as total_records,
               COUNT(stock_name) as records_with_name,
               COUNT(DISTINCT stock_id) as unique_stocks,
               COUNT(DISTINCT stock_name) as unique_names
        FROM cash_flows
        """)
        
        stats = cursor.fetchone()
        print(f"📊 總記錄數: {stats[0]:,}")
        print(f"📊 有股票名稱的記錄數: {stats[1]:,}")
        print(f"📊 唯一股票代碼數: {stats[2]:,}")
        print(f"📊 唯一股票名稱數: {stats[3]:,}")
        
        # 顯示範例資料
        cursor.execute("""
        SELECT stock_id, stock_name, date, `本期稅前淨利（淨損）`, `營業活動之淨現金流入（流出）`
        FROM cash_flows 
        WHERE stock_name IS NOT NULL 
        ORDER BY stock_id, date 
        LIMIT 10
        """)
        
        sample_data = cursor.fetchall()
        print(f"\n📊 範例資料:")
        for row in sample_data:
            stock_id, stock_name, date, net_income, operating_cf = row
            print(f"   {stock_id} ({stock_name}) {date}: 稅前淨利={net_income}, 營業現金流={operating_cf}")
        
        conn_cash.close()
        
        print(f"\n✅ cash_flows.db 的 stock_name 欄位更新完成！")
        return True
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_stock_name_to_cash_flows()
    
    if success:
        print(f"\n🎉 cash_flows.db 已成功添加 stock_name 欄位")
    else:
        print(f"\n❌ 添加 stock_name 欄位失敗")
