#!/usr/bin/env python3
"""
測試跑馬燈在狀態欄的顯示功能
"""

import sys
import logging
from datetime import datetime, time
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QTextEdit, QStatusBar, QProgressBar
)
from PyQt6.QtCore import QTimer, Qt

# 設置日誌
logging.basicConfig(level=logging.INFO)

class TickerStatusBarTestWindow(QMainWindow):
    """測試跑馬燈狀態欄顯示的主視窗"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("📊 跑馬燈狀態欄測試 - 看盤重點時段提醒")
        self.setGeometry(100, 100, 1200, 700)
        
        self.init_ui()
        self.init_trading_ticker()
        
    def init_ui(self):
        """初始化界面"""
        # 中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📊 跑馬燈狀態欄測試")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 當前時間顯示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #28a745; margin: 10px;")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.time_label)
        
        # 說明文字
        desc = QTextEdit()
        desc.setReadOnly(True)
        desc.setMaximumHeight(200)
        desc.setHtml("""
        <h3 style="color: #2E86AB;">📊 跑馬燈功能測試</h3>
        <p><strong>功能說明</strong>：跑馬燈會根據當前時間自動在狀態欄顯示相應的看盤重點提醒。</p>
        <ul>
            <li><strong>自動啟動</strong>：程式啟動時自動開始運作</li>
            <li><strong>時段判斷</strong>：根據當前時間顯示對應的操作重點</li>
            <li><strong>狀態欄顯示</strong>：訊息顯示在視窗底部的狀態欄</li>
            <li><strong>手動控制</strong>：可透過狀態欄按鈕開關跑馬燈</li>
            <li><strong>定時更新</strong>：每30秒自動檢查並更新訊息</li>
        </ul>
        <p style="color: #666;"><strong>注意</strong>：請觀察視窗底部狀態欄的跑馬燈訊息變化。</p>
        """)
        layout.addWidget(desc)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        
        manual_check_btn = QPushButton("🔄 立即檢查時段")
        manual_check_btn.clicked.connect(self.check_trading_time)
        manual_check_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; }")
        
        toggle_btn = QPushButton("🎯 切換跑馬燈")
        toggle_btn.clicked.connect(self.toggle_trading_ticker)
        toggle_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; }")
        
        test_all_btn = QPushButton("🧪 測試所有時段")
        test_all_btn.clicked.connect(self.test_all_periods)
        test_all_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #6f42c1; color: white; border: none; border-radius: 4px; }")
        
        button_layout.addWidget(manual_check_btn)
        button_layout.addWidget(toggle_btn)
        button_layout.addWidget(test_all_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 初始化狀態欄
        self.init_status_bar()
        
        # 時間更新定時器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # 每秒更新時間
        
    def init_status_bar(self):
        """初始化狀態欄"""
        self.status_bar = self.statusBar()
        
        # 跑馬燈控制按鈕
        self.ticker_btn = QPushButton("📊 跑馬燈: 開")
        self.ticker_btn.clicked.connect(self.toggle_trading_ticker)
        self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #28a745; color: white; border: none; border-radius: 3px; }")
        self.ticker_btn.setToolTip("點擊切換看盤重點跑馬燈")
        self.status_bar.addPermanentWidget(self.ticker_btn)
        
        # 進度條（模擬其他狀態欄元素）
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 初始狀態訊息
        self.status_bar.showMessage("📊 跑馬燈狀態欄測試系統已就緒")
        
    def init_trading_ticker(self):
        """初始化看盤重點跑馬燈"""
        self.trading_ticker_enabled = True  # 預設啟用
        self.trading_ticker_timer = QTimer()
        self.trading_ticker_timer.timeout.connect(self.check_trading_time)
        
        # 看盤重點訊息 - 整合實戰技巧
        self.trading_messages = {
            'pre_market': "🌅 盤前集合競價 (8:30-9:00)：🎯留意成交金額>1000萬個股、📰消息驅動股異動、👑族群龍頭搶籌情況",
            'early_session': "🌄 早盤時段 (9:00-10:30)：🚀跳空續航股、💪主力標的量價齊揚、📈技術面強勢股KD鈍化高檔",
            'mid_session': "☀️ 中盤時段 (10:30-12:00)：🔄族群輪動補漲股、📊支撐反彈股逢低承接、💰ETF套利折價機會",
            'afternoon': "🌇 午盤時段 (12:00-13:25)：⚡反彈先鋒長下影線、🛡️防禦性高股息ETF、📉權值股破線避開",
            'closing': "🌆 收盤前試撮 (13:25-13:30)：💎大宗交易熱股、🔄ETF尾盤換股、⚠️暫緩撮合股次日留意",
            'after_hours': "📊 盤後時段：檢視當日表現、準備明日策略、關注國際市場動向、零股交易13:40-14:30"
        }
        
        # 自動啟動跑馬燈
        self.trading_ticker_timer.start(5000)  # 每5秒檢查一次（測試用）
        # 立即檢查一次當前時段
        self.check_trading_time()
    
    def update_time_display(self):
        """更新時間顯示"""
        now = datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 判斷當前時段
        current_time = now.time()
        period = self.get_current_period(current_time)
        
        status = "開" if getattr(self, 'trading_ticker_enabled', False) else "關"
        self.time_label.setText(f"⏰ 當前時間：{time_str} | 📊 當前時段：{period} | 🎯 跑馬燈：{status}")
    
    def get_current_period(self, current_time):
        """獲取當前時段名稱"""
        if time(8, 30) <= current_time < time(9, 0):
            return "盤前集合競價"
        elif time(9, 0) <= current_time < time(10, 30):
            return "早盤時段"
        elif time(10, 30) <= current_time < time(12, 0):
            return "中盤時段"
        elif time(12, 0) <= current_time < time(13, 30):
            return "午盤時段"
        elif time(13, 25) <= current_time < time(13, 30):
            return "收盤前試撮"
        else:
            return "盤後時段"
    
    def check_trading_time(self):
        """檢查交易時間並顯示相應提醒"""
        if not getattr(self, 'trading_ticker_enabled', False):
            return
            
        now = datetime.now()
        current_time = now.time()
        
        # 判斷當前時段
        message = ""
        if time(8, 30) <= current_time < time(9, 0):
            message = self.trading_messages['pre_market']
        elif time(9, 0) <= current_time < time(10, 30):
            message = self.trading_messages['early_session']
        elif time(10, 30) <= current_time < time(12, 0):
            message = self.trading_messages['mid_session']
        elif time(12, 0) <= current_time < time(13, 30):
            message = self.trading_messages['afternoon']
        elif time(13, 25) <= current_time < time(13, 30):
            message = self.trading_messages['closing']
        else:
            message = self.trading_messages['after_hours']
        
        # 在狀態欄顯示跑馬燈
        if message:
            self.show_status(message)
    
    def show_status(self, message, timeout=0):
        """在狀態欄顯示訊息"""
        self.status_bar.showMessage(message, timeout)
        logging.info(f"狀態欄訊息: {message}")
    
    def toggle_trading_ticker(self):
        """切換跑馬燈開關"""
        if getattr(self, 'trading_ticker_enabled', False):
            # 停用跑馬燈
            self.trading_ticker_enabled = False
            self.trading_ticker_timer.stop()
            self.ticker_btn.setText("📊 跑馬燈: 關")
            self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #dc3545; color: white; border: none; border-radius: 3px; }")
            self.show_status("📊 看盤重點跑馬燈已停用")
        else:
            # 啟用跑馬燈
            self.trading_ticker_enabled = True
            self.trading_ticker_timer.start(5000)  # 每5秒檢查一次（測試用）
            self.ticker_btn.setText("📊 跑馬燈: 開")
            self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #28a745; color: white; border: none; border-radius: 3px; }")
            self.show_status("🎯 看盤重點跑馬燈已啟用")
            # 立即檢查一次
            self.check_trading_time()
    
    def test_all_periods(self):
        """依序測試所有時段的訊息"""
        messages = [
            ("🌅 盤前集合競價", self.trading_messages['pre_market']),
            ("🌄 早盤時段", self.trading_messages['early_session']),
            ("☀️ 中盤時段", self.trading_messages['mid_session']),
            ("🌇 午盤時段", self.trading_messages['afternoon']),
            ("🌆 收盤前試撮", self.trading_messages['closing']),
            ("📊 盤後時段", self.trading_messages['after_hours'])
        ]
        
        # 依序顯示每個時段的訊息
        for i, (period, message) in enumerate(messages):
            QTimer.singleShot(i * 3000, lambda msg=message: self.show_status(msg, 2500))

def main():
    """主函數"""
    print("📊 測試跑馬燈狀態欄顯示功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示主視窗
    window = TickerStatusBarTestWindow()
    window.show()
    
    print("✅ 跑馬燈狀態欄測試視窗已開啟")
    print("🔍 測試重點:")
    print("  1. ✅ 跑馬燈自動啟動")
    print("  2. ✅ 狀態欄訊息顯示")
    print("  3. ✅ 時段自動判斷")
    print("  4. ✅ 手動開關控制")
    print("  5. ✅ 定時更新機制")
    print("\n💡 觀察要點:")
    print("  📊 狀態欄底部的跑馬燈訊息")
    print("  🎯 右下角的跑馬燈控制按鈕")
    print("  ⏰ 時間和時段的即時更新")
    print("  🔄 每5秒自動檢查時段變化")
    print("\n🎯 測試方式:")
    print("  1. 觀察狀態欄的跑馬燈訊息")
    print("  2. 點擊 '🎯 切換跑馬燈' 測試開關")
    print("  3. 點擊 '🧪 測試所有時段' 查看所有訊息")
    print("  4. 點擊狀態欄的 '📊 跑馬燈' 按鈕控制")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
