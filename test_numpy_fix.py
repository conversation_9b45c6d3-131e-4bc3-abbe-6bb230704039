#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 numpy 修復
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_numpy_import():
    """測試 numpy 導入"""
    print("🧪 測試 numpy 導入...")
    
    try:
        from crawler import np
        print(f"✅ numpy 導入成功: {type(np)}")
        
        # 測試 np.nan
        test_nan = np.nan
        print(f"✅ np.nan 正常: {test_nan}")
        
        # 測試 np.where (如果是 fallback 會有不同行為)
        if hasattr(np, 'where'):
            result = np.where([True, False, True])
            print(f"✅ np.where 正常: {result}")
        else:
            print("⚠️ 使用 fallback numpy")
        
        return True
        
    except Exception as e:
        print(f"❌ numpy 測試失敗: {str(e)}")
        return False

def test_to_pickle():
    """測試 to_pickle 函數"""
    print("\n🧪 測試 to_pickle 函數...")
    
    try:
        from crawler import to_pickle
        import pandas as pd
        
        # 創建測試資料
        test_data = pd.DataFrame({
            'value': [1, 2, 3]
        })
        test_data['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03'])
        test_data['stock_id'] = ['1101', '1102', '1103']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 測試存檔
        to_pickle(test_data, 'test_numpy_fix')
        print("✅ to_pickle 函數正常")
        
        return True
        
    except Exception as e:
        print(f"❌ to_pickle 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 numpy 修復測試")
    print("=" * 60)
    
    # 測試1: numpy 導入
    test1 = test_numpy_import()
    
    # 測試2: to_pickle 函數
    test2 = test_to_pickle()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    print(f"numpy 導入: {'✅ 成功' if test1 else '❌ 失敗'}")
    print(f"to_pickle 函數: {'✅ 成功' if test2 else '❌ 失敗'}")
    
    if test1 and test2:
        print("\n🎉 numpy 修復成功！")
        print("✨ 分批存檔功能應該可以正常工作了")
    else:
        print("\n⚠️ 還需要進一步檢查")
    
    return test1 and test2

if __name__ == "__main__":
    main()
