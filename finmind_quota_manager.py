#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FinMind API配額管理器
智能管理600次/小時的API限制，最大化數據獲取效率
"""

import os
import json
import time
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

class FinMindQuotaManager:
    """FinMind API配額管理器"""
    
    def __init__(self, db_path="finmind_cache.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # API限制設定
        self.HOURLY_LIMIT = 600
        self.DAILY_LIMIT = 14400  # 600 * 24
        self.SAFETY_BUFFER = 50   # 保留50次作為緩衝
        
        # 數據更新頻率設定（小時）
        self.UPDATE_FREQUENCIES = {
            'stock_info': 24*7,      # 股票基本資料：每週更新
            'financial': 24*30,      # 財務報表：每月更新
            'dividend': 24*7,        # 股利：每週更新
            'per': 2,                # 本益比：每2小時更新
            'revenue': 24,           # 營收：每日更新
            'institutional': 4,      # 法人：每4小時更新
        }
        
        # 股票優先級設定
        self.STOCK_PRIORITIES = {
            'high': [],      # 高優先級股票（每日更新）
            'medium': [],    # 中優先級股票（每2-3日更新）
            'low': []        # 低優先級股票（每週更新）
        }
        
        # 初始化數據庫
        self.init_cache_db()
        
        # 載入配置
        self.load_config()
    
    def init_cache_db(self):
        """初始化緩存數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建API使用記錄表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    dataset TEXT,
                    stock_id TEXT,
                    success BOOLEAN,
                    response_size INTEGER
                )
            ''')
            
            # 創建數據緩存表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS data_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cache_key TEXT UNIQUE,
                    dataset TEXT,
                    stock_id TEXT,
                    data TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    priority INTEGER DEFAULT 2
                )
            ''')
            
            # 創建更新計劃表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS update_schedule (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT,
                    dataset TEXT,
                    priority INTEGER,
                    last_updated DATETIME,
                    next_update DATETIME,
                    update_frequency INTEGER
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("✅ 緩存數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 緩存數據庫初始化失敗: {e}")
    
    def load_config(self):
        """載入配置"""
        try:
            # 載入股票優先級配置
            config_file = "finmind_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.STOCK_PRIORITIES = config.get('stock_priorities', self.STOCK_PRIORITIES)
                    self.logger.info("✅ 配置載入完成")
            else:
                # 創建默認配置
                self.create_default_config()
                
        except Exception as e:
            self.logger.error(f"❌ 配置載入失敗: {e}")
    
    def create_default_config(self):
        """創建默認配置"""
        # 台股市值前50大作為高優先級
        high_priority = [
            "2330", "2317", "2454", "2412", "2882", "2308", "2303", "2002", 
            "2881", "2886", "2207", "2379", "2395", "2408", "2409", "2474",
            "2891", "2892", "3008", "2357", "2382", "2883", "2912", "2885"
        ]
        
        # 中型股作為中優先級
        medium_priority = [
            "2301", "2324", "2327", "2344", "2347", "2354", "2356", "2360",
            "2362", "2367", "2368", "2371", "2376", "2377", "2380", "2384"
        ]
        
        self.STOCK_PRIORITIES = {
            'high': high_priority,
            'medium': medium_priority,
            'low': []  # 其他股票
        }
        
        # 保存配置
        config = {
            'stock_priorities': self.STOCK_PRIORITIES,
            'update_frequencies': self.UPDATE_FREQUENCIES,
            'api_limits': {
                'hourly': self.HOURLY_LIMIT,
                'daily': self.DAILY_LIMIT,
                'safety_buffer': self.SAFETY_BUFFER
            }
        }
        
        with open("finmind_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.logger.info("✅ 默認配置已創建")
    
    def get_current_usage(self) -> Dict:
        """獲取當前API使用情況"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取過去1小時的使用量
            one_hour_ago = datetime.now() - timedelta(hours=1)
            cursor.execute('''
                SELECT COUNT(*) FROM api_usage 
                WHERE timestamp > ? AND success = 1
            ''', (one_hour_ago,))
            hourly_usage = cursor.fetchone()[0]
            
            # 獲取今日使用量
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cursor.execute('''
                SELECT COUNT(*) FROM api_usage 
                WHERE timestamp > ? AND success = 1
            ''', (today_start,))
            daily_usage = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'hourly_usage': hourly_usage,
                'hourly_limit': self.HOURLY_LIMIT,
                'hourly_remaining': self.HOURLY_LIMIT - hourly_usage,
                'daily_usage': daily_usage,
                'daily_limit': self.DAILY_LIMIT,
                'daily_remaining': self.DAILY_LIMIT - daily_usage,
                'can_make_request': hourly_usage < (self.HOURLY_LIMIT - self.SAFETY_BUFFER)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 獲取使用情況失敗: {e}")
            return {
                'hourly_usage': 999,
                'hourly_remaining': 0,
                'daily_usage': 999,
                'daily_remaining': 0,
                'can_make_request': False
            }
    
    def record_api_call(self, dataset: str, stock_id: str, success: bool, response_size: int = 0):
        """記錄API調用"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO api_usage (dataset, stock_id, success, response_size)
                VALUES (?, ?, ?, ?)
            ''', (dataset, stock_id, success, response_size))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 記錄API調用失敗: {e}")
    
    def get_update_priority_list(self, max_requests: int = 100) -> List[Dict]:
        """
        獲取更新優先級列表
        
        Args:
            max_requests: 最大請求數量
            
        Returns:
            優先級排序的更新列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取需要更新的數據
            cursor.execute('''
                SELECT stock_id, dataset, priority, last_updated, next_update
                FROM update_schedule
                WHERE next_update <= datetime('now')
                ORDER BY priority DESC, next_update ASC
                LIMIT ?
            ''', (max_requests,))
            
            results = cursor.fetchall()
            conn.close()
            
            update_list = []
            for row in results:
                update_list.append({
                    'stock_id': row[0],
                    'dataset': row[1],
                    'priority': row[2],
                    'last_updated': row[3],
                    'next_update': row[4]
                })
            
            return update_list
            
        except Exception as e:
            self.logger.error(f"❌ 獲取更新列表失敗: {e}")
            return []
    
    def create_daily_update_plan(self) -> Dict:
        """創建每日更新計劃"""
        usage = self.get_current_usage()
        available_requests = min(
            usage['hourly_remaining'],
            usage['daily_remaining']
        )
        
        # 分配API配額
        quota_allocation = {
            'high_priority_stocks': int(available_requests * 0.6),    # 60% 給高優先級
            'medium_priority_stocks': int(available_requests * 0.25), # 25% 給中優先級
            'low_priority_stocks': int(available_requests * 0.10),    # 10% 給低優先級
            'emergency_buffer': int(available_requests * 0.05)        # 5% 緊急緩衝
        }
        
        # 數據類型優先級
        data_priority = {
            'per': 3,           # 本益比最重要（影響CANSLIM等策略）
            'dividend': 2,      # 股利數據（影響高殖利率策略）
            'revenue': 2,       # 營收數據（影響成長股策略）
            'financial': 1,     # 財務報表（季度更新即可）
            'institutional': 1  # 法人數據（不是每個策略都需要）
        }
        
        plan = {
            'total_available': available_requests,
            'quota_allocation': quota_allocation,
            'data_priority': data_priority,
            'recommended_updates': self.get_update_priority_list(available_requests),
            'estimated_completion_time': self._estimate_completion_time(available_requests)
        }
        
        return plan
    
    def _estimate_completion_time(self, requests: int) -> str:
        """估算完成時間"""
        # 假設每個請求平均需要1秒，加上API限制
        if requests <= self.HOURLY_LIMIT:
            # 可以在1小時內完成
            minutes = (requests * 60) // self.HOURLY_LIMIT
            return f"{minutes} 分鐘"
        else:
            # 需要多個小時
            hours = requests // self.HOURLY_LIMIT
            remaining = requests % self.HOURLY_LIMIT
            if remaining > 0:
                hours += 1
            return f"{hours} 小時"
    
    def optimize_for_strategies(self, active_strategies: List[str]) -> Dict:
        """
        根據啟用的策略優化數據獲取
        
        Args:
            active_strategies: 啟用的策略列表
            
        Returns:
            優化後的數據獲取計劃
        """
        # 策略數據需求映射
        strategy_data_needs = {
            'CANSLIM': ['per', 'dividend', 'financial'],
            '高殖利率烏龜': ['dividend', 'per'],
            '超級績效台股版': ['revenue', 'financial'],
            '小蝦米跟大鯨魚': ['institutional'],
            '本益成長比': ['per', 'financial', 'revenue'],
            '研發魔人': ['financial', 'revenue']
        }
        
        # 計算數據需求權重
        data_weights = {}
        for strategy in active_strategies:
            if strategy in strategy_data_needs:
                for data_type in strategy_data_needs[strategy]:
                    data_weights[data_type] = data_weights.get(data_type, 0) + 1
        
        # 根據權重調整優先級
        usage = self.get_current_usage()
        available = usage['hourly_remaining']
        
        optimized_plan = {
            'active_strategies': active_strategies,
            'data_weights': data_weights,
            'recommended_allocation': {},
            'total_requests': 0
        }
        
        # 按權重分配API配額
        total_weight = sum(data_weights.values())
        if total_weight > 0:
            for data_type, weight in data_weights.items():
                allocation = int((weight / total_weight) * available * 0.9)  # 90%用於數據獲取
                optimized_plan['recommended_allocation'][data_type] = allocation
                optimized_plan['total_requests'] += allocation
        
        return optimized_plan
    
    def get_smart_test_plan(self) -> Dict:
        """獲取智能測試計劃（節省配額）"""
        return {
            'test_stocks': ["2330", "2317"],  # 只測試2支股票
            'test_datasets': ["TaiwanStockPER", "TaiwanStockDividend"],  # 只測試2種數據
            'estimated_cost': 4,  # 2股票 × 2數據 = 4次API調用
            'safety_level': 'high',
            'recommendation': '建議在非交易時間進行測試，避免影響正常數據更新'
        }
    
    def get_daily_maintenance_plan(self) -> Dict:
        """獲取每日維護計劃"""
        current_hour = datetime.now().hour
        
        # 根據時間段分配不同的維護任務
        if 0 <= current_hour < 6:
            # 凌晨：大量數據更新
            plan_type = "overnight_batch"
            max_requests = 400
        elif 6 <= current_hour < 9:
            # 早晨：準備開盤數據
            plan_type = "morning_prep"
            max_requests = 200
        elif 9 <= current_hour < 15:
            # 交易時間：即時數據更新
            plan_type = "trading_hours"
            max_requests = 100
        elif 15 <= current_hour < 18:
            # 收盤後：數據整理
            plan_type = "post_market"
            max_requests = 150
        else:
            # 晚間：策略分析準備
            plan_type = "evening_analysis"
            max_requests = 200
        
        return {
            'plan_type': plan_type,
            'max_requests': max_requests,
            'current_hour': current_hour,
            'description': self._get_plan_description(plan_type)
        }
    
    def _get_plan_description(self, plan_type: str) -> str:
        """獲取計劃描述"""
        descriptions = {
            "overnight_batch": "凌晨大批量數據更新，更新財務報表、股利等低頻數據",
            "morning_prep": "早晨準備開盤數據，更新本益比、法人數據",
            "trading_hours": "交易時間保守更新，只更新最重要的即時數據",
            "post_market": "收盤後數據整理，更新當日營收、法人數據",
            "evening_analysis": "晚間策略分析準備，更新分析所需的各類數據"
        }
        return descriptions.get(plan_type, "標準數據更新計劃")
