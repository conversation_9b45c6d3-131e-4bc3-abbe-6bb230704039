#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將 cash_flows.pkl 轉換為 db 檔，並只保留 2018-01-01 以後的資料
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime

def convert_cash_flows_to_db():
    """將 cash_flows.pkl 轉換為 db 檔"""
    
    print("=" * 80)
    print("🔄 將 cash_flows.pkl 轉換為 db 檔")
    print("=" * 80)
    
    pkl_file = r'D:\Finlab\history\tables\cash_flows.pkl'
    db_file = r'D:\Finlab\history\tables\cash_flows.db'
    
    try:
        # 檢查 pkl 檔案是否存在
        if not os.path.exists(pkl_file):
            print(f"❌ 檔案不存在: {pkl_file}")
            return False
        
        # 檢查檔案大小
        file_size = os.path.getsize(pkl_file) / (1024 * 1024)
        print(f"📂 pkl 檔案大小: {file_size:.2f} MB")
        
        # 讀取 pkl 檔案
        print(f"📖 讀取 pkl 檔案...")
        df = pd.read_pickle(pkl_file)
        
        print(f"✅ 成功讀取 pkl 檔案")
        print(f"📊 原始資料形狀: {df.shape}")
        print(f"📋 欄位: {list(df.columns)}")
        
        # 檢查索引結構
        print(f"📊 索引資訊:")
        print(f"   索引名稱: {df.index.names}")
        print(f"   索引層級: {df.index.nlevels}")
        
        if df.index.nlevels > 1:
            print(f"   多層索引結構:")
            for i, name in enumerate(df.index.names):
                level_values = df.index.get_level_values(i)
                print(f"      層級 {i} ({name}): {len(level_values.unique())} 個唯一值")
                if name and 'date' in name.lower():
                    print(f"         日期範圍: {level_values.min()} ~ {level_values.max()}")
                elif i < 2:  # 只顯示前兩層的範例
                    unique_vals = level_values.unique()
                    print(f"         範例: {list(unique_vals[:5])}")
        
        # 顯示前幾筆資料
        print(f"\n📊 前5筆資料:")
        print(df.head())
        
        # 檢查日期相關的欄位或索引
        date_column = None
        date_index_level = None
        
        # 檢查是否有日期相關的欄位
        for col in df.columns:
            if 'date' in col.lower() or 'time' in col.lower():
                date_column = col
                print(f"🗓️ 找到日期欄位: {col}")
                break
        
        # 檢查是否有日期相關的索引
        if df.index.nlevels > 1:
            for i, name in enumerate(df.index.names):
                if name and ('date' in name.lower() or 'time' in name.lower()):
                    date_index_level = i
                    print(f"🗓️ 找到日期索引層級: {i} ({name})")
                    break
        
        # 如果沒有明確的日期欄位，檢查索引是否包含日期
        if date_column is None and date_index_level is None:
            # 嘗試解析索引的第一層或第二層是否為日期
            for i in range(min(2, df.index.nlevels)):
                try:
                    level_values = df.index.get_level_values(i)
                    # 嘗試轉換為日期
                    pd.to_datetime(level_values[:10])  # 測試前10個值
                    date_index_level = i
                    print(f"🗓️ 推測日期索引層級: {i}")
                    break
                except:
                    continue
        
        # 重置索引以便處理
        df_reset = df.reset_index()
        print(f"\n📊 重置索引後的欄位: {list(df_reset.columns)}")
        
        # 確定日期欄位
        if date_column:
            date_col_name = date_column
        elif date_index_level is not None:
            date_col_name = df.index.names[date_index_level] or f'level_{date_index_level}'
        else:
            # 嘗試找到看起來像日期的欄位
            for col in df_reset.columns:
                if df_reset[col].dtype == 'datetime64[ns]' or 'date' in str(col).lower():
                    date_col_name = col
                    break
            else:
                print("❌ 無法找到日期欄位")
                return False
        
        print(f"🗓️ 使用日期欄位: {date_col_name}")
        
        # 轉換日期欄位
        if date_col_name in df_reset.columns:
            df_reset[date_col_name] = pd.to_datetime(df_reset[date_col_name])
            
            # 檢查日期範圍
            min_date = df_reset[date_col_name].min()
            max_date = df_reset[date_col_name].max()
            print(f"📅 原始日期範圍: {min_date} ~ {max_date}")
            
            # 過濾 2018-01-01 以後的資料
            cutoff_date = datetime(2018, 1, 1)
            df_filtered = df_reset[df_reset[date_col_name] >= cutoff_date].copy()
            
            print(f"📊 過濾後資料形狀: {df_filtered.shape}")
            print(f"📅 過濾後日期範圍: {df_filtered[date_col_name].min()} ~ {df_filtered[date_col_name].max()}")
            
            if df_filtered.empty:
                print("❌ 過濾後沒有資料")
                return False
            
            # 轉換日期為字串格式（YYYY-MM-DD）
            df_filtered[date_col_name] = df_filtered[date_col_name].dt.strftime('%Y-%m-%d')
            
            # 創建資料庫
            print(f"\n💾 創建資料庫: {db_file}")
            
            # 如果資料庫已存在，先備份
            if os.path.exists(db_file):
                backup_file = db_file.replace('.db', '_backup.db')
                print(f"📋 備份現有資料庫到: {backup_file}")
                import shutil
                shutil.copy2(db_file, backup_file)
            
            # 連接資料庫
            conn = sqlite3.connect(db_file)
            
            # 將資料寫入資料庫
            table_name = 'cash_flows'
            df_filtered.to_sql(table_name, conn, if_exists='replace', index=False)
            
            print(f"✅ 資料已寫入資料庫表格: {table_name}")
            
            # 驗證寫入結果
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"📊 資料庫記錄數: {count:,}")
            
            # 檢查表格結構
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"📋 資料庫表格結構:")
            for i, (cid, name, type_, notnull, default, pk) in enumerate(columns, 1):
                print(f"   {i:2d}. {name:<20} {type_:<10}")
            
            # 檢查日期範圍
            cursor.execute(f"SELECT MIN({date_col_name}), MAX({date_col_name}) FROM {table_name}")
            date_range = cursor.fetchone()
            print(f"📅 資料庫日期範圍: {date_range[0]} ~ {date_range[1]}")
            
            # 顯示範例資料
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
            sample_data = cursor.fetchall()
            column_names = [desc[0] for desc in cursor.description]
            
            print(f"\n📊 範例資料:")
            for i, row in enumerate(sample_data, 1):
                print(f"   第{i}筆: {dict(zip(column_names, row))}")
            
            conn.close()
            
            # 檢查檔案大小
            db_size = os.path.getsize(db_file) / (1024 * 1024)
            print(f"\n📂 db 檔案大小: {db_size:.2f} MB")
            
            print(f"\n✅ 轉換完成！")
            print(f"   原始檔案: {pkl_file}")
            print(f"   新資料庫: {db_file}")
            print(f"   表格名稱: {table_name}")
            print(f"   日期過濾: >= 2018-01-01")
            
            return True
            
        else:
            print(f"❌ 找不到日期欄位: {date_col_name}")
            return False
            
    except Exception as e:
        print(f"❌ 轉換失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = convert_cash_flows_to_db()
    
    if success:
        print(f"\n🎉 cash_flows.pkl 已成功轉換為 cash_flows.db")
    else:
        print(f"\n❌ 轉換失敗")
