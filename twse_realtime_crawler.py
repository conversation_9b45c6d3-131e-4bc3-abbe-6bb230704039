#!/usr/bin/env python3
"""
台灣證券交易所（TWSE）即時資料擷取與5分鐘彙總系統
基於TWSE官方API，每5秒擷取即時交易資訊，彙總為5分鐘K線數據
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
import json
import csv
import os
from datetime import datetime, timedelta
import threading
from queue import Queue
import sqlite3
from collections import defaultdict
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TWSERealtimeCrawler:
    """TWSE即時資料擷取器"""
    
    def __init__(self, data_dir="twse_data", db_path="twse_realtime.db"):
        """
        初始化TWSE即時資料擷取器
        
        Args:
            data_dir: 資料存儲目錄
            db_path: 資料庫路徑
        """
        self.data_dir = data_dir
        self.db_path = db_path
        
        # 創建資料目錄
        os.makedirs(data_dir, exist_ok=True)
        
        # 設置HTTP會話
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': 'https://mis.twse.com.tw/'
        })
        self.session.verify = False
        
        # TWSE API端點
        self.api_endpoints = {
            'realtime': 'https://mis.twse.com.tw/stock/api/getStockInfo.jsp',
            'index': 'https://mis.twse.com.tw/stock/api/getStockIndex.jsp',
            'odd_lot': 'https://mis.twse.com.tw/stock/api/getOddInfo.jsp'
        }
        
        # 資料緩存
        self.realtime_buffer = defaultdict(list)
        self.data_queue = Queue()
        
        # 控制變數
        self.is_running = False
        self.crawler_thread = None
        self.aggregator_thread = None
        
        # 初始化資料庫
        self.init_database()
        
        logging.info("📊 TWSE即時資料擷取器初始化完成")
    
    def init_database(self):
        """初始化資料庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建即時資料表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS realtime_ticks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    price REAL,
                    volume INTEGER,
                    bid_price REAL,
                    ask_price REAL,
                    bid_volume INTEGER,
                    ask_volume INTEGER,
                    high REAL,
                    low REAL,
                    open REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 創建5分鐘K線表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kline_5min (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    datetime TIMESTAMP NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    tick_count INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, datetime)
                )
            ''')
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_realtime_symbol_time ON realtime_ticks(symbol, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_kline_symbol_datetime ON kline_5min(symbol, datetime)')
            
            conn.commit()
            conn.close()
            
            logging.info("✅ 資料庫初始化完成")
            
        except Exception as e:
            logging.error(f"❌ 資料庫初始化失敗: {e}")
    
    def get_stock_realtime_data(self, symbols):
        """
        獲取股票即時資料
        
        Args:
            symbols: 股票代碼列表
        
        Returns:
            dict: 即時資料
        """
        try:
            # 構建查詢字符串
            ex_ch_list = []
            for symbol in symbols:
                ex_ch_list.append(f"tse_{symbol}.tw")
                ex_ch_list.append(f"otc_{symbol}.tw")  # 也查詢櫃買
            
            params = {
                'ex_ch': '|'.join(ex_ch_list),
                'json': '1',
                'delay': '0'
            }
            
            response = self.session.get(
                self.api_endpoints['realtime'], 
                params=params, 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'msgArray' in data and data['msgArray']:
                    parsed_data = {}
                    
                    for stock_data in data['msgArray']:
                        symbol = stock_data.get('c', '').replace('.tw', '')
                        
                        if symbol:
                            parsed_data[symbol] = {
                                'symbol': symbol,
                                'timestamp': datetime.now(),
                                'price': self._safe_float(stock_data.get('z')),  # 成交價
                                'volume': self._safe_int(stock_data.get('tv')),  # 總成交量
                                'bid_price': self._safe_float(stock_data.get('b')),  # 最佳買價
                                'ask_price': self._safe_float(stock_data.get('a')),  # 最佳賣價
                                'bid_volume': self._safe_int(stock_data.get('g')),  # 最佳買量
                                'ask_volume': self._safe_int(stock_data.get('f')),  # 最佳賣量
                                'high': self._safe_float(stock_data.get('h')),  # 最高價
                                'low': self._safe_float(stock_data.get('l')),   # 最低價
                                'open': self._safe_float(stock_data.get('o')),  # 開盤價
                                'change': self._safe_float(stock_data.get('f')), # 漲跌
                                'change_percent': self._safe_float(stock_data.get('r')) # 漲跌幅
                            }
                    
                    return parsed_data
            
            return {}
            
        except Exception as e:
            logging.error(f"❌ 獲取即時資料失敗: {e}")
            return {}
    
    def _safe_float(self, value):
        """安全轉換為浮點數"""
        try:
            if value is None or value == '' or value == '-':
                return 0.0
            return float(str(value).replace(',', ''))
        except:
            return 0.0
    
    def _safe_int(self, value):
        """安全轉換為整數"""
        try:
            if value is None or value == '' or value == '-':
                return 0
            return int(str(value).replace(',', ''))
        except:
            return 0
    
    def save_realtime_to_csv(self, data, date_str=None):
        """
        保存即時資料到CSV文件
        
        Args:
            data: 即時資料字典
            date_str: 日期字符串，默認為今天
        """
        try:
            if not date_str:
                date_str = datetime.now().strftime('%Y%m%d')
            
            csv_file = os.path.join(self.data_dir, f"realtime_{date_str}.csv")
            
            # 檢查文件是否存在，決定是否寫入標題
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                fieldnames = [
                    'symbol', 'timestamp', 'price', 'volume', 
                    'bid_price', 'ask_price', 'bid_volume', 'ask_volume',
                    'high', 'low', 'open', 'change', 'change_percent'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                for symbol, stock_data in data.items():
                    writer.writerow(stock_data)
            
            logging.debug(f"✅ 保存即時資料到 {csv_file}")
            
        except Exception as e:
            logging.error(f"❌ 保存CSV失敗: {e}")
    
    def save_realtime_to_db(self, data):
        """
        保存即時資料到資料庫
        
        Args:
            data: 即時資料字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, stock_data in data.items():
                cursor.execute('''
                    INSERT INTO realtime_ticks 
                    (symbol, timestamp, price, volume, bid_price, ask_price, 
                     bid_volume, ask_volume, high, low, open)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    stock_data['symbol'],
                    stock_data['timestamp'],
                    stock_data['price'],
                    stock_data['volume'],
                    stock_data['bid_price'],
                    stock_data['ask_price'],
                    stock_data['bid_volume'],
                    stock_data['ask_volume'],
                    stock_data['high'],
                    stock_data['low'],
                    stock_data['open']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logging.error(f"❌ 保存資料庫失敗: {e}")
    
    def aggregate_to_5min_kline(self, symbol, start_time, end_time):
        """
        將即時資料彙總為5分鐘K線
        
        Args:
            symbol: 股票代碼
            start_time: 開始時間
            end_time: 結束時間
        
        Returns:
            dict: 5分鐘K線數據
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT price, volume, timestamp
                FROM realtime_ticks
                WHERE symbol = ? AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol, start_time, end_time))
            conn.close()
            
            if df.empty:
                return None
            
            # 計算OHLC
            open_price = df['price'].iloc[0]
            high_price = df['price'].max()
            low_price = df['price'].min()
            close_price = df['price'].iloc[-1]
            
            # 計算成交量（取最後一筆的累計量減去第一筆的累計量）
            volume = df['volume'].iloc[-1] - df['volume'].iloc[0] if len(df) > 1 else df['volume'].iloc[0]
            
            return {
                'symbol': symbol,
                'datetime': start_time,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': max(0, volume),  # 確保成交量不為負
                'tick_count': len(df)
            }
            
        except Exception as e:
            logging.error(f"❌ 彙總5分鐘K線失敗: {e}")
            return None
    
    def save_5min_kline_to_db(self, kline_data):
        """
        保存5分鐘K線到資料庫
        
        Args:
            kline_data: K線數據
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO kline_5min 
                (symbol, datetime, open, high, low, close, volume, tick_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                kline_data['symbol'],
                kline_data['datetime'],
                kline_data['open'],
                kline_data['high'],
                kline_data['low'],
                kline_data['close'],
                kline_data['volume'],
                kline_data['tick_count']
            ))
            
            conn.commit()
            conn.close()
            
            logging.info(f"✅ 保存5分鐘K線: {kline_data['symbol']} {kline_data['datetime']}")
            
        except Exception as e:
            logging.error(f"❌ 保存5分鐘K線失敗: {e}")
    
    def start_realtime_crawling(self, symbols, interval=5):
        """
        開始即時資料擷取
        
        Args:
            symbols: 股票代碼列表
            interval: 擷取間隔（秒）
        """
        if self.is_running:
            logging.warning("⚠️ 即時擷取已在運行中")
            return
        
        self.is_running = True
        
        def crawler_worker():
            """擷取工作線程"""
            logging.info(f"🚀 開始即時資料擷取，監控 {len(symbols)} 支股票")
            
            while self.is_running:
                try:
                    # 獲取即時資料
                    realtime_data = self.get_stock_realtime_data(symbols)
                    
                    if realtime_data:
                        # 保存到CSV和資料庫
                        self.save_realtime_to_csv(realtime_data)
                        self.save_realtime_to_db(realtime_data)
                        
                        # 添加到緩存用於彙總
                        for symbol, data in realtime_data.items():
                            self.realtime_buffer[symbol].append(data)
                        
                        logging.debug(f"📊 擷取到 {len(realtime_data)} 支股票的即時資料")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logging.error(f"❌ 擷取線程錯誤: {e}")
                    time.sleep(10)
        
        def aggregator_worker():
            """彙總工作線程"""
            logging.info("🔄 開始5分鐘K線彙總")
            
            while self.is_running:
                try:
                    current_time = datetime.now()
                    
                    # 每5分鐘執行一次彙總
                    if current_time.minute % 5 == 0 and current_time.second < 10:
                        # 計算5分鐘區間
                        end_time = current_time.replace(second=0, microsecond=0)
                        start_time = end_time - timedelta(minutes=5)
                        
                        for symbol in symbols:
                            kline_data = self.aggregate_to_5min_kline(symbol, start_time, end_time)
                            if kline_data:
                                self.save_5min_kline_to_db(kline_data)
                        
                        logging.info(f"📈 完成 {end_time} 的5分鐘K線彙總")
                    
                    time.sleep(30)  # 每30秒檢查一次
                    
                except Exception as e:
                    logging.error(f"❌ 彙總線程錯誤: {e}")
                    time.sleep(60)
        
        # 啟動工作線程
        self.crawler_thread = threading.Thread(target=crawler_worker, daemon=True)
        self.aggregator_thread = threading.Thread(target=aggregator_worker, daemon=True)
        
        self.crawler_thread.start()
        self.aggregator_thread.start()
        
        logging.info("✅ 即時資料擷取系統已啟動")
    
    def stop_realtime_crawling(self):
        """停止即時資料擷取"""
        self.is_running = False
        
        if self.crawler_thread:
            self.crawler_thread.join(timeout=5)
        if self.aggregator_thread:
            self.aggregator_thread.join(timeout=5)
        
        logging.info("⏹️ 即時資料擷取系統已停止")
    
    def get_5min_kline_data(self, symbol, start_date=None, end_date=None):
        """
        獲取5分鐘K線數據
        
        Args:
            symbol: 股票代碼
            start_date: 開始日期
            end_date: 結束日期
        
        Returns:
            pandas.DataFrame: 5分鐘K線數據
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            if start_date and end_date:
                query = '''
                    SELECT datetime, open, high, low, close, volume, tick_count
                    FROM kline_5min
                    WHERE symbol = ? AND datetime >= ? AND datetime <= ?
                    ORDER BY datetime
                '''
                df = pd.read_sql_query(query, conn, params=(symbol, start_date, end_date))
            else:
                query = '''
                    SELECT datetime, open, high, low, close, volume, tick_count
                    FROM kline_5min
                    WHERE symbol = ?
                    ORDER BY datetime
                '''
                df = pd.read_sql_query(query, conn, params=(symbol,))
            
            conn.close()
            
            if not df.empty:
                df['datetime'] = pd.to_datetime(df['datetime'])
            
            return df
            
        except Exception as e:
            logging.error(f"❌ 獲取5分鐘K線數據失敗: {e}")
            return pd.DataFrame()

# 創建全局實例
twse_crawler = TWSERealtimeCrawler()

# 便捷函數
def start_twse_monitoring(symbols, interval=5):
    """便捷函數：開始TWSE即時監控"""
    return twse_crawler.start_realtime_crawling(symbols, interval)

def stop_twse_monitoring():
    """便捷函數：停止TWSE即時監控"""
    return twse_crawler.stop_realtime_crawling()

def get_twse_5min_data(symbol, start_date=None, end_date=None):
    """便捷函數：獲取TWSE 5分鐘數據"""
    return twse_crawler.get_5min_kline_data(symbol, start_date, end_date)
