#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫整合配置
統一管理本專案中所有數據庫的路徑和配置
"""

import os
import json
import logging
from pathlib import Path

class DatabaseConfig:
    """數據庫配置管理器"""
    
    def __init__(self):
        # 統一的數據庫根目錄
        self.DB_ROOT_DIR = "D:/Finlab/history/tables"
        
        # 確保目錄存在
        os.makedirs(self.DB_ROOT_DIR, exist_ok=True)
        
        # 數據庫路徑配置
        self.DATABASE_PATHS = {
            # 主要交易數據庫
            'price': os.path.join(self.DB_ROOT_DIR, 'price.db'),
            'new_price': os.path.join(self.DB_ROOT_DIR, 'new_price.db'),
            'unified_stock_data': os.path.join(self.DB_ROOT_DIR, 'unified_stock_data.db'),
            'unified_stock_data_new': os.path.join(self.DB_ROOT_DIR, 'unified_stock_data_new.db'),
            
            # 基本面數據庫
            'pe_data': os.path.join(self.DB_ROOT_DIR, 'pe_data.db'),
            'dividend_data': os.path.join(self.DB_ROOT_DIR, 'dividend_data.db'),
            'monthly_revenue': os.path.join(self.DB_ROOT_DIR, 'monthly_revenue.db'),
            
            # 新聞和資訊數據庫
            'news': os.path.join(self.DB_ROOT_DIR, 'news.db'),
            
            # 股票基本資料數據庫
            'stock_basic_info': os.path.join(self.DB_ROOT_DIR, 'stock_basic_info.db'),
            
            # Finlab財務數據庫（新增）
            'finlab_financial_data': os.path.join(self.DB_ROOT_DIR, 'finlab_financial_data.db'),
            
            # 緩存和臨時數據庫
            'finmind_cache': os.path.join(self.DB_ROOT_DIR, 'finmind_cache.db'),
            'cache': os.path.join(self.DB_ROOT_DIR, 'cache.db'),
        }
        
        # 數據庫描述
        self.DATABASE_DESCRIPTIONS = {
            'price': '主要股價數據庫 - 包含OHLCV歷史數據',
            'new_price': '新版股價數據庫 - 優化版本',
            'unified_stock_data': '統一股票數據庫 - 整合多種數據源',
            'unified_stock_data_new': '新版統一股票數據庫',
            'pe_data': 'PE數據庫 - 本益比、殖利率等估值數據',
            'dividend_data': '股利數據庫 - 現金股利、股票股利歷史',
            'monthly_revenue': '月營收數據庫 - 每月營業收入數據',
            'news': '新聞數據庫 - 財經新聞和公告',
            'stock_basic_info': '股票基本資料數據庫 - 公司基本信息',
            'finlab_financial_data': 'Finlab財務數據庫 - 完整財務報表數據',
            'finmind_cache': 'FinMind API緩存數據庫',
            'cache': '系統緩存數據庫',
        }
        
        # 數據庫優先級（用於自動選擇）
        self.DATABASE_PRIORITY = {
            'price': ['new_price', 'price', 'unified_stock_data_new', 'unified_stock_data'],
            'pe': ['pe_data'],
            'dividend': ['dividend_data'],
            'revenue': ['monthly_revenue'],
            'news': ['news'],
            'basic_info': ['stock_basic_info'],
            'financial': ['finlab_financial_data'],
        }
    
    def get_database_path(self, db_type):
        """獲取數據庫路徑"""
        if db_type in self.DATABASE_PATHS:
            return self.DATABASE_PATHS[db_type]
        else:
            # 如果是類型查詢，返回優先級最高的可用數據庫
            if db_type in self.DATABASE_PRIORITY:
                for db_name in self.DATABASE_PRIORITY[db_type]:
                    db_path = self.DATABASE_PATHS.get(db_name)
                    if db_path and os.path.exists(db_path):
                        return db_path
                # 如果沒有找到現有的，返回第一個作為默認
                return self.DATABASE_PATHS.get(self.DATABASE_PRIORITY[db_type][0])
        
        return None
    
    def get_available_databases(self):
        """獲取所有可用的數據庫"""
        available = {}
        for db_name, db_path in self.DATABASE_PATHS.items():
            if os.path.exists(db_path):
                try:
                    # 檢查文件大小
                    size = os.path.getsize(db_path)
                    available[db_name] = {
                        'path': db_path,
                        'size': size,
                        'size_mb': round(size / 1024 / 1024, 2),
                        'description': self.DATABASE_DESCRIPTIONS.get(db_name, '未知數據庫')
                    }
                except Exception as e:
                    logging.warning(f"檢查數據庫 {db_name} 時出錯: {e}")
        
        return available
    
    def create_database_summary(self):
        """創建數據庫摘要報告"""
        available_dbs = self.get_available_databases()
        
        summary = {
            'database_root': self.DB_ROOT_DIR,
            'total_databases': len(available_dbs),
            'total_size_mb': sum(db['size_mb'] for db in available_dbs.values()),
            'databases': available_dbs
        }
        
        return summary
    
    def update_app_config(self, config_file="app_config.json"):
        """更新主程序配置文件"""
        try:
            # 讀取現有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # 更新數據庫配置
            if 'database' not in config:
                config['database'] = {}
            
            # 設置主要數據庫路徑
            config['database'].update({
                'price_db_path': self.get_database_path('price'),
                'pe_db_path': self.get_database_path('pe'),
                'news_db_path': self.get_database_path('news'),
                'dividend_db_path': self.get_database_path('dividend'),
                'monthly_revenue_db_path': self.get_database_path('revenue'),
                'finlab_financial_db_path': self.get_database_path('financial'),
                'pe_enabled': True,
                'news_enabled': True,
                'dividend_enabled': True,
                'monthly_revenue_enabled': True,
                'finlab_financial_enabled': True,
            })
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            logging.info(f"✅ 已更新配置文件: {config_file}")
            return True
            
        except Exception as e:
            logging.error(f"❌ 更新配置文件失敗: {e}")
            return False
    
    def create_database_status_report(self):
        """創建數據庫狀態報告"""
        summary = self.create_database_summary()
        
        report = f"""# 📊 數據庫狀態報告

## 🎯 概覽
- **數據庫根目錄**: {summary['database_root']}
- **可用數據庫數量**: {summary['total_databases']}
- **總大小**: {summary['total_size_mb']:.2f} MB

## 📋 數據庫詳情

"""
        
        for db_name, db_info in summary['databases'].items():
            report += f"""### 📊 {db_name}
- **路徑**: `{db_info['path']}`
- **大小**: {db_info['size_mb']} MB
- **描述**: {db_info['description']}

"""
        
        # 保存報告
        report_path = os.path.join(self.DB_ROOT_DIR, "database_status_report.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report_path
    
    def migrate_old_databases(self):
        """遷移舊的數據庫到統一目錄"""
        old_paths = [
            "data/finlab_tables/finlab_financial_data.db",
            "db/price.db",
            "data/price_data.db",
            "./finmind_cache.db",
        ]
        
        migrated = []
        
        for old_path in old_paths:
            if os.path.exists(old_path):
                # 確定新路徑
                filename = os.path.basename(old_path)
                new_path = os.path.join(self.DB_ROOT_DIR, filename)
                
                try:
                    # 如果新路徑不存在，則移動文件
                    if not os.path.exists(new_path):
                        import shutil
                        shutil.move(old_path, new_path)
                        migrated.append((old_path, new_path))
                        logging.info(f"✅ 遷移數據庫: {old_path} -> {new_path}")
                    else:
                        logging.info(f"ℹ️ 數據庫已存在，跳過: {new_path}")
                        
                except Exception as e:
                    logging.error(f"❌ 遷移數據庫失敗 {old_path}: {e}")
        
        return migrated

def main():
    """主函數 - 執行數據庫整合配置"""
    print("🗄️ 數據庫整合配置工具")
    print("=" * 60)
    
    # 創建配置管理器
    db_config = DatabaseConfig()
    
    print(f"📁 數據庫根目錄: {db_config.DB_ROOT_DIR}")
    
    # 遷移舊數據庫
    print("\n🚀 檢查並遷移舊數據庫...")
    migrated = db_config.migrate_old_databases()
    if migrated:
        print(f"✅ 成功遷移 {len(migrated)} 個數據庫")
        for old_path, new_path in migrated:
            print(f"  📊 {os.path.basename(old_path)}: {old_path} -> {new_path}")
    else:
        print("ℹ️ 沒有需要遷移的數據庫")
    
    # 檢查可用數據庫
    print("\n📊 檢查可用數據庫...")
    available = db_config.get_available_databases()
    print(f"✅ 發現 {len(available)} 個可用數據庫:")
    
    total_size = 0
    for db_name, db_info in available.items():
        print(f"  📊 {db_name}: {db_info['size_mb']} MB - {db_info['description']}")
        total_size += db_info['size_mb']
    
    print(f"\n💾 總大小: {total_size:.2f} MB")
    
    # 更新主程序配置
    print("\n⚙️ 更新主程序配置...")
    if db_config.update_app_config():
        print("✅ 配置文件更新成功")
    else:
        print("❌ 配置文件更新失敗")
    
    # 生成狀態報告
    print("\n📋 生成數據庫狀態報告...")
    report_path = db_config.create_database_status_report()
    print(f"✅ 報告已生成: {report_path}")
    
    print("\n🎉 數據庫整合配置完成！")
    print(f"📁 所有數據庫現在統一存放在: {db_config.DB_ROOT_DIR}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
