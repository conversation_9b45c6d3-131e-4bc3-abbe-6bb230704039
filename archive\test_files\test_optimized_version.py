#!/usr/bin/env python3
"""
測試優化版本的基本功能
"""
import sys
import os
import logging

def test_import():
    """測試模組導入"""
    try:
        # 嘗試導入優化版本
        import O3mh_gui_v21_optimized
        print("✅ 成功導入優化版本模組")
        return True
    except Exception as e:
        print(f"❌ 導入模組失敗: {e}")
        return False

def test_classes():
    """測試主要類別"""
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI, StrategyParams, Strategy
        
        # 測試策略參數類
        params = StrategyParams()
        print(f"✅ StrategyParams 創建成功: {params.name}")
        
        # 測試策略類
        strategy = Strategy(params)
        print("✅ Strategy 類創建成功")
        
        return True
    except Exception as e:
        print(f"❌ 類別測試失敗: {e}")
        return False

def test_basic_functionality():
    """測試基本功能（不啟動GUI）"""
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        # 創建應用（但不顯示）
        app = QApplication([])
        gui = StockScreenerGUI()
        
        # 測試基本屬性
        assert hasattr(gui, 'strategies'), "GUI應該有strategies屬性"
        assert hasattr(gui, 'db_connections'), "GUI應該有db_connections屬性"
        
        print("✅ GUI基本功能測試通過")
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 基本功能測試失敗: {e}")
        return False

def main():
    print("🧪 測試優化版本 O3mh_gui_v21_optimized.py")
    print("=" * 50)
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 執行測試
    tests = [
        ("模組導入", test_import),
        ("類別創建", test_classes),
        ("基本功能", test_basic_functionality)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n🔍 測試: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{len(tests)} 通過")
    
    if passed == len(tests):
        print("🎉 所有測試通過！優化版本準備就緒。")
        print("💡 可以執行以下指令啟動優化版本：")
        print("   python O3mh_gui_v21_optimized.py")
    else:
        print("⚠️  部分測試失敗，請檢查代碼。")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 