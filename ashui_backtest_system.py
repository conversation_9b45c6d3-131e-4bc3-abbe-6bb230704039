#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿水一式策略回測系統
用於驗證和優化評分邏輯的回測框架
"""

import pandas as pd
import numpy as np
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class AshiuBacktestSystem:
    """阿水一式策略回測系統"""
    
    def __init__(self, db_path: str = "data/price_data.db"):
        """
        初始化回測系統
        
        Args:
            db_path: 價格數據庫路徑
        """
        self.db_path = db_path
        self.results = []
        self.performance_metrics = {}
        self.factor_analysis = {}
        
        # 回測參數
        self.start_date = "2020-01-01"
        self.end_date = "2024-12-31"
        self.holding_period = 10  # 持有天數
        self.max_positions = 10   # 最大持股數
        self.initial_capital = 1000000  # 初始資金100萬
        
        # 技術指標參數
        self.bb_period = 20
        self.bb_std = 2.1
        self.volume_ma_period = 5
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_stock_list(self) -> List[str]:
        """獲取股票列表"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 檢查表結構，適應不同的表名和欄位名
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            # 尋找股價數據表
            table_name = None
            for table in tables:
                if 'stock' in table.lower() and ('price' in table.lower() or 'daily' in table.lower()):
                    table_name = table
                    break

            if not table_name:
                # 如果沒找到，使用第一個包含stock的表
                for table in tables:
                    if 'stock' in table.lower():
                        table_name = table
                        break

            if not table_name:
                self.logger.error("未找到股價數據表")
                return []

            # 檢查欄位名稱
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]

            # 確定股票代碼欄位名稱
            stock_id_col = 'stock_id'
            if 'stock_id' not in columns:
                for col in ['code', 'symbol', 'stock_code', 'ticker']:
                    if col in columns:
                        stock_id_col = col
                        break

            # 確定日期欄位名稱
            date_col = 'date'
            if 'date' not in columns:
                for col in ['trading_date', 'trade_date', 'dt']:
                    if col in columns:
                        date_col = col
                        break

            query = f"""
            SELECT DISTINCT {stock_id_col}
            FROM {table_name}
            WHERE {date_col} >= ? AND {date_col} <= ?
            ORDER BY {stock_id_col}
            """
            df = pd.read_sql_query(query, conn, params=[self.start_date, self.end_date])
            conn.close()

            # 保存表結構信息供後續使用
            self.table_name = table_name
            self.stock_id_col = stock_id_col
            self.date_col = date_col

            return df[stock_id_col].tolist()

        except Exception as e:
            self.logger.error(f"獲取股票列表失敗: {e}")
            return []
    
    def get_stock_data(self, stock_id: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        獲取單一股票的歷史數據

        Args:
            stock_id: 股票代碼
            start_date: 開始日期
            end_date: 結束日期

        Returns:
            包含價格和技術指標的DataFrame
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 使用之前檢測到的表結構信息
            table_name = getattr(self, 'table_name', 'stock_daily_data')
            stock_id_col = getattr(self, 'stock_id_col', 'stock_id')
            date_col = getattr(self, 'date_col', 'date')

            # 檢查欄位名稱
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]

            # 確定OHLCV欄位名稱
            ohlcv_mapping = {
                'open': ['open', 'Open', 'opening_price'],
                'high': ['high', 'High', 'highest_price'],
                'low': ['low', 'Low', 'lowest_price'],
                'close': ['close', 'Close', 'closing_price'],
                'volume': ['volume', 'Volume', 'trading_volume']
            }

            selected_cols = [date_col, stock_id_col]
            col_aliases = [date_col, stock_id_col]

            for target_col, possible_names in ohlcv_mapping.items():
                found_col = None
                for name in possible_names:
                    if name in columns:
                        found_col = name
                        break

                if found_col:
                    selected_cols.append(found_col)
                    col_aliases.append(target_col)
                else:
                    self.logger.warning(f"未找到 {target_col} 欄位")

            # 構建查詢
            select_clause = ', '.join([f"{col} as {alias}" if col != alias else col
                                     for col, alias in zip(selected_cols, col_aliases)])

            query = f"""
            SELECT {select_clause}
            FROM {table_name}
            WHERE {stock_id_col} = ? AND {date_col} >= ? AND {date_col} <= ?
            ORDER BY {date_col}
            """

            df = pd.read_sql_query(query, conn, params=[stock_id, start_date, end_date])
            conn.close()

            if df.empty:
                return pd.DataFrame()

            # 確保數據類型正確
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 計算技術指標
            df = self.calculate_technical_indicators(df)
            return df

        except Exception as e:
            self.logger.error(f"獲取股票數據失敗 {stock_id}: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        計算技術指標
        
        Args:
            df: 包含OHLCV數據的DataFrame
            
        Returns:
            添加技術指標的DataFrame
        """
        if len(df) < self.bb_period:
            return df
        
        # 移動平均線
        df['MA5'] = df['close'].rolling(window=5).mean()
        df['MA10'] = df['close'].rolling(window=10).mean()
        df['MA20'] = df['close'].rolling(window=20).mean()
        df['MA60'] = df['close'].rolling(window=60).mean()
        
        # 布林帶
        df['BB_middle'] = df['close'].rolling(window=self.bb_period).mean()
        bb_std = df['close'].rolling(window=self.bb_period).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * self.bb_std)
        df['BB_lower'] = df['BB_middle'] - (bb_std * self.bb_std)
        df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']
        
        # 成交量指標
        df['Volume_MA5'] = df['volume'].rolling(window=self.volume_ma_period).mean()
        df['Volume_ratio'] = df['volume'] / df['Volume_MA5']
        
        # 突破幅度
        df['Breakout_pct'] = ((df['close'] - df['BB_upper']) / df['BB_upper'] * 100).clip(lower=0)
        
        # 成交金額
        df['Turnover'] = df['close'] * df['volume'] / 10000  # 萬元
        
        # 壓縮天數計算
        df['Compression_days'] = self.calculate_compression_days(df)
        
        return df
    
    def calculate_compression_days(self, df: pd.DataFrame) -> pd.Series:
        """
        計算布林帶壓縮天數
        
        Args:
            df: 包含布林帶寬的DataFrame
            
        Returns:
            壓縮天數序列
        """
        compression_days = pd.Series(0, index=df.index)
        
        for i in range(len(df)):
            if i < 20:  # 需要足夠的歷史數據
                continue
                
            # 計算最近20天的布林帶寬度
            recent_widths = df['BB_width'].iloc[i-19:i+1].dropna()
            
            if len(recent_widths) < 2:
                continue
            
            # 計算連續收縮天數（排除最後一天）
            days = 0
            for j in range(len(recent_widths) - 2, 0, -1):
                if recent_widths.iloc[j] < recent_widths.iloc[j-1]:
                    days += 1
                else:
                    break
            
            compression_days.iloc[i] = days
        
        return compression_days
    
    def check_ashui_signal(self, df: pd.DataFrame, date_idx: int) -> Tuple[bool, Dict]:
        """
        檢查阿水一式信號
        
        Args:
            df: 股票數據
            date_idx: 日期索引
            
        Returns:
            (是否符合條件, 技術指標數據)
        """
        if date_idx < 60:  # 需要足夠的歷史數據
            return False, {}
        
        current = df.iloc[date_idx]
        
        # 基本條件檢查
        conditions = {
            'turnover': current['Turnover'],
            'bb_width': current['BB_width'],
            'compression_days': current['Compression_days'],
            'volume_ratio': current['Volume_ratio'],
            'breakout_pct': current['Breakout_pct'],
            'close': current['close']
        }
        
        # 阿水一式核心條件
        signal_conditions = [
            current['Turnover'] >= 1000,  # 成交金額 >= 1000萬
            current['close'] > current['BB_upper'],  # 突破布林上軌
            current['Volume_ratio'] >= 2.0,  # 成交量 >= 2倍
            current['MA20'] > df.iloc[date_idx-1]['MA20'],  # 20MA向上
        ]
        
        is_signal = all(signal_conditions)
        
        return is_signal, conditions
    
    def calculate_ashui_score(self, indicators: Dict) -> float:
        """
        計算阿水一式評分（當前版本）
        
        Args:
            indicators: 技術指標字典
            
        Returns:
            評分 (0-100)
        """
        score = 0
        
        # 成交金額評分 (25分)
        turnover = indicators.get('turnover', 0)
        if turnover >= 100000:
            score += 25
        elif turnover >= 50000:
            score += 22
        elif turnover >= 20000:
            score += 18
        elif turnover >= 10000:
            score += 15
        elif turnover >= 5000:
            score += 12
        elif turnover >= 1000:
            score += 8
        elif turnover >= 500:
            score += 5
        
        # 布林帶寬評分 (20分)
        bb_width = indicators.get('bb_width', 0)
        if 0.08 <= bb_width <= 0.15:
            score += 20
        elif 0.06 <= bb_width <= 0.20:
            score += 15
        elif 0.04 <= bb_width <= 0.25:
            score += 10
        elif bb_width > 0:
            score += 5
        
        # 壓縮天數評分 (15分)
        compression_days = indicators.get('compression_days', 0)
        if 5 <= compression_days <= 15:
            score += 15
        elif 3 <= compression_days <= 20:
            score += 12
        elif 1 <= compression_days <= 25:
            score += 8
        elif compression_days > 0:
            score += 5
        
        # 成交量倍數評分 (20分)
        volume_ratio = indicators.get('volume_ratio', 0)
        if volume_ratio >= 5.0:
            score += 20
        elif volume_ratio >= 3.0:
            score += 18
        elif volume_ratio >= 2.5:
            score += 15
        elif volume_ratio >= 2.0:
            score += 12
        elif volume_ratio >= 1.5:
            score += 8
        elif volume_ratio >= 1.0:
            score += 5
        
        # 突破幅度評分 (15分)
        breakout_pct = indicators.get('breakout_pct', 0)
        if breakout_pct >= 8.0:
            score += 15
        elif breakout_pct >= 5.0:
            score += 12
        elif breakout_pct >= 3.0:
            score += 10
        elif breakout_pct >= 1.0:
            score += 7
        elif breakout_pct > 0:
            score += 5
        
        # 策略匹配加分 (5分) - 在回測中總是給予
        score += 5
        
        return min(max(score, 0), 100)

    def run_backtest(self, start_date: str = None, end_date: str = None) -> Dict:
        """
        執行回測

        Args:
            start_date: 回測開始日期
            end_date: 回測結束日期

        Returns:
            回測結果字典
        """
        if start_date:
            self.start_date = start_date
        if end_date:
            self.end_date = end_date

        self.logger.info(f"開始回測: {self.start_date} 到 {self.end_date}")

        # 獲取股票列表
        stock_list = self.get_stock_list()
        self.logger.info(f"回測股票數量: {len(stock_list)}")

        # 生成交易日期列表
        trade_dates = self.get_trade_dates()

        # 初始化結果記錄
        self.results = []
        portfolio_value = self.initial_capital
        positions = {}  # {stock_id: {'entry_date': date, 'entry_price': price, 'shares': shares}}

        for date in trade_dates:
            # 檢查現有持倉是否到期
            positions = self.check_position_exit(positions, date)

            # 尋找新的交易信號
            if len(positions) < self.max_positions:
                signals = self.find_signals_on_date(stock_list, date)

                # 按評分排序，選擇最佳信號
                signals.sort(key=lambda x: x['score'], reverse=True)

                # 開新倉位
                available_slots = self.max_positions - len(positions)
                for signal in signals[:available_slots]:
                    positions = self.open_position(positions, signal, date, portfolio_value)

            # 記錄當日組合價值
            daily_value = self.calculate_portfolio_value(positions, date)
            self.results.append({
                'date': date,
                'portfolio_value': daily_value,
                'positions_count': len(positions),
                'cash': portfolio_value - sum(pos['shares'] * pos['entry_price'] for pos in positions.values())
            })

        # 計算績效指標
        self.performance_metrics = self.calculate_performance_metrics()

        self.logger.info("回測完成")
        return {
            'results': self.results,
            'performance': self.performance_metrics,
            'total_trades': len([r for r in self.results if 'trade_return' in r])
        }

    def get_trade_dates(self) -> List[str]:
        """獲取交易日期列表"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 使用之前檢測到的表結構信息
            table_name = getattr(self, 'table_name', 'stock_daily_data')
            date_col = getattr(self, 'date_col', 'date')

            query = f"""
            SELECT DISTINCT {date_col} as date
            FROM {table_name}
            WHERE {date_col} >= ? AND {date_col} <= ?
            ORDER BY {date_col}
            """
            df = pd.read_sql_query(query, conn, params=[self.start_date, self.end_date])
            conn.close()
            return df['date'].tolist()
        except Exception as e:
            self.logger.error(f"獲取交易日期失敗: {e}")
            return []

    def find_signals_on_date(self, stock_list: List[str], date: str) -> List[Dict]:
        """
        在指定日期尋找交易信號

        Args:
            stock_list: 股票列表
            date: 目標日期

        Returns:
            信號列表
        """
        signals = []

        for stock_id in stock_list:
            # 獲取足夠的歷史數據
            start_data_date = (datetime.strptime(date, '%Y-%m-%d') - timedelta(days=365)).strftime('%Y-%m-%d')
            df = self.get_stock_data(stock_id, start_data_date, date)

            if df.empty or len(df) < 60:
                continue

            # 找到目標日期的索引
            date_mask = df['date'] == date
            if not date_mask.any():
                continue

            date_idx = df[date_mask].index[0]

            # 檢查信號
            is_signal, indicators = self.check_ashui_signal(df, date_idx)

            if is_signal:
                score = self.calculate_ashui_score(indicators)
                signals.append({
                    'stock_id': stock_id,
                    'date': date,
                    'price': indicators['close'],
                    'score': score,
                    'indicators': indicators
                })

        return signals

    def open_position(self, positions: Dict, signal: Dict, date: str, portfolio_value: float) -> Dict:
        """
        開新倉位

        Args:
            positions: 當前持倉
            signal: 交易信號
            date: 交易日期
            portfolio_value: 組合價值

        Returns:
            更新後的持倉
        """
        # 計算倉位大小（等權重）
        position_size = portfolio_value / self.max_positions * 0.95  # 保留5%現金
        shares = int(position_size / signal['price'])

        if shares > 0:
            positions[signal['stock_id']] = {
                'entry_date': date,
                'entry_price': signal['price'],
                'shares': shares,
                'score': signal['score'],
                'indicators': signal['indicators']
            }

        return positions

    def check_position_exit(self, positions: Dict, current_date: str) -> Dict:
        """
        檢查持倉是否需要退出

        Args:
            positions: 當前持倉
            current_date: 當前日期

        Returns:
            更新後的持倉
        """
        to_remove = []

        for stock_id, position in positions.items():
            entry_date = datetime.strptime(position['entry_date'], '%Y-%m-%d')
            current = datetime.strptime(current_date, '%Y-%m-%d')

            # 檢查是否達到持有期限
            if (current - entry_date).days >= self.holding_period:
                # 獲取退出價格
                exit_price = self.get_stock_price_on_date(stock_id, current_date)

                if exit_price:
                    # 計算收益
                    trade_return = (exit_price - position['entry_price']) / position['entry_price']

                    # 記錄交易結果
                    self.results.append({
                        'trade_type': 'exit',
                        'stock_id': stock_id,
                        'entry_date': position['entry_date'],
                        'exit_date': current_date,
                        'entry_price': position['entry_price'],
                        'exit_price': exit_price,
                        'shares': position['shares'],
                        'trade_return': trade_return,
                        'score': position['score'],
                        'indicators': position['indicators']
                    })

                to_remove.append(stock_id)

        # 移除已退出的持倉
        for stock_id in to_remove:
            del positions[stock_id]

        return positions

    def get_stock_price_on_date(self, stock_id: str, date: str) -> Optional[float]:
        """獲取指定日期的股票價格"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 使用之前檢測到的表結構信息
            table_name = getattr(self, 'table_name', 'stock_daily_data')
            stock_id_col = getattr(self, 'stock_id_col', 'stock_id')
            date_col = getattr(self, 'date_col', 'date')

            # 檢查收盤價欄位名稱
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]

            close_col = 'close'
            for col in ['close', 'Close', 'closing_price']:
                if col in columns:
                    close_col = col
                    break

            query = f"""
            SELECT {close_col} as close
            FROM {table_name}
            WHERE {stock_id_col} = ? AND {date_col} = ?
            """
            result = pd.read_sql_query(query, conn, params=[stock_id, date])
            conn.close()

            if not result.empty:
                return float(result['close'].iloc[0])
            return None
        except Exception as e:
            self.logger.error(f"獲取股價失敗 {stock_id} {date}: {e}")
            return None

    def calculate_portfolio_value(self, positions: Dict, date: str) -> float:
        """計算組合價值"""
        total_value = 0

        for stock_id, position in positions.items():
            current_price = self.get_stock_price_on_date(stock_id, date)
            if current_price:
                total_value += position['shares'] * current_price

        return total_value

    def calculate_performance_metrics(self) -> Dict:
        """計算績效指標"""
        # 提取交易記錄
        trades = [r for r in self.results if 'trade_return' in r]

        if not trades:
            return {}

        returns = [trade['trade_return'] for trade in trades]

        # 基本統計
        total_trades = len(trades)
        winning_trades = len([r for r in returns if r > 0])
        losing_trades = len([r for r in returns if r < 0])

        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_return = np.mean(returns)
        std_return = np.std(returns)

        # 累積報酬
        cumulative_return = np.prod([1 + r for r in returns]) - 1

        # 夏普比率 (假設無風險利率為2%)
        risk_free_rate = 0.02 / 252 * self.holding_period  # 調整為持有期間
        sharpe_ratio = (avg_return - risk_free_rate) / std_return if std_return > 0 else 0

        # 最大回撤
        portfolio_values = [r['portfolio_value'] for r in self.results if 'portfolio_value' in r]
        if portfolio_values:
            peak = portfolio_values[0]
            max_drawdown = 0
            for value in portfolio_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                max_drawdown = max(max_drawdown, drawdown)
        else:
            max_drawdown = 0

        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'std_return': std_return,
            'cumulative_return': cumulative_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'best_trade': max(returns) if returns else 0,
            'worst_trade': min(returns) if returns else 0
        }

    def analyze_factors(self) -> Dict:
        """
        分析各因子與報酬的關係

        Returns:
            因子分析結果
        """
        trades = [r for r in self.results if 'trade_return' in r]

        if not trades:
            return {}

        # 準備數據
        data = []
        for trade in trades:
            indicators = trade['indicators']
            data.append({
                'return': trade['trade_return'],
                'score': trade['score'],
                'turnover': indicators.get('turnover', 0),
                'bb_width': indicators.get('bb_width', 0),
                'compression_days': indicators.get('compression_days', 0),
                'volume_ratio': indicators.get('volume_ratio', 0),
                'breakout_pct': indicators.get('breakout_pct', 0)
            })

        df = pd.DataFrame(data)

        # 計算相關係數
        correlations = {}
        factors = ['score', 'turnover', 'bb_width', 'compression_days', 'volume_ratio', 'breakout_pct']

        for factor in factors:
            if factor in df.columns:
                corr = df['return'].corr(df[factor])
                correlations[factor] = corr

        # 分組分析
        score_groups = self.analyze_score_groups(df)

        return {
            'correlations': correlations,
            'score_groups': score_groups,
            'factor_stats': self.calculate_factor_stats(df)
        }

    def analyze_score_groups(self, df: pd.DataFrame) -> Dict:
        """分析不同評分組別的表現"""
        # 將評分分組
        df['score_group'] = pd.cut(df['score'], bins=[0, 60, 75, 85, 100],
                                  labels=['低分(0-60)', '中分(60-75)', '高分(75-85)', '超高分(85-100)'])

        group_stats = {}
        for group in df['score_group'].unique():
            if pd.isna(group):
                continue

            group_data = df[df['score_group'] == group]
            group_stats[str(group)] = {
                'count': len(group_data),
                'avg_return': group_data['return'].mean(),
                'win_rate': (group_data['return'] > 0).mean(),
                'std_return': group_data['return'].std()
            }

        return group_stats

    def calculate_factor_stats(self, df: pd.DataFrame) -> Dict:
        """計算各因子的統計數據"""
        factors = ['turnover', 'bb_width', 'compression_days', 'volume_ratio', 'breakout_pct']
        stats = {}

        for factor in factors:
            if factor in df.columns:
                stats[factor] = {
                    'mean': df[factor].mean(),
                    'std': df[factor].std(),
                    'min': df[factor].min(),
                    'max': df[factor].max(),
                    'median': df[factor].median()
                }

        return stats
