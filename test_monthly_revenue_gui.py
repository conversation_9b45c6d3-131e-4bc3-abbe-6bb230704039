#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試月營收排行榜GUI功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, date
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import QDate

# 模擬GUI中的月營收排行榜功能
class TestMonthlyRevenueRanking:
    def __init__(self):
        self.strategy_date = None
        
    def set_date(self, year, month, day):
        """設置測試日期"""
        self.strategy_date = QDate(year, month, day)
        
    def get_monthly_revenue_ranking(self, ranking_type):
        """獲取月營收排行榜數據"""
        try:
            # 獲取選定的計算日期
            qdate = self.strategy_date
            selected_date = datetime(qdate.year(), qdate.month(), qdate.day()).date()
            print(f"📅 選定日期: {selected_date}")

            # 判斷排序類型
            if "YoY" in ranking_type:
                sort_column = "YoY%"
            elif "MoM" in ranking_type:
                sort_column = "MoM%"
            else:  # 綜合評分
                sort_column = "綜合評分"
            
            print(f"📊 排序類型: {sort_column}")

            # 從資料庫讀取月營收資料
            monthly_data = self.fetch_monthly_revenue_from_db(selected_date)

            if monthly_data is None or monthly_data.empty:
                print("❌ 無法獲取月營收資料")
                return []

            # 計算YoY和MoM
            processed_data = self.calculate_revenue_growth(monthly_data, selected_date)

            # 排序並取前100名
            if sort_column in processed_data.columns:
                # 移除無效值並排序
                valid_data = processed_data.dropna(subset=[sort_column])
                sorted_data = valid_data.sort_values(sort_column, ascending=False).head(100)

                # 轉換為排行榜格式
                ranking_data = []
                for idx, (_, row) in enumerate(sorted_data.iterrows(), 1):
                    data_item = {
                        '排名': idx,
                        '股票代碼': row['stock_id'],
                        '股票名稱': row['stock_name'],
                        '當月營收': f"{row.get('當月營收', 0):,.0f}",
                        'YoY%': f"{row.get('YoY%', 0):.2f}%",
                        'MoM%': f"{row.get('MoM%', 0):.2f}%",
                        '日期': selected_date.strftime('%Y-%m-%d')
                    }

                    # 如果是綜合評分排行榜，添加綜合評分欄位
                    if sort_column == "綜合評分":
                        data_item['綜合評分'] = f"{row.get('綜合評分', 0):.1f}"

                    ranking_data.append(data_item)

                print(f"✅ 成功獲取{len(ranking_data)}筆月營收排行榜資料")
                return ranking_data
            else:
                print(f"❌ 資料中缺少{sort_column}欄位")
                return []

        except Exception as e:
            print(f"❌ 獲取月營收排行榜失敗: {e}")
            import traceback
            traceback.print_exc()
            return []

    def fetch_monthly_revenue_from_db(self, selected_date):
        """從資料庫讀取月營收資料"""
        try:
            # 嘗試從 .pkl 檔案讀取
            monthly_pkl_path = 'history/tables/monthly_report.pkl'
            if os.path.exists(monthly_pkl_path):
                print(f"📂 從 PKL 檔案讀取月營收資料: {monthly_pkl_path}")
                df = pd.read_pickle(monthly_pkl_path)

                # 重置索引，將 stock_id 和 date 變成欄位
                df = df.reset_index()
                
                # 篩選指定月份的資料
                year_month = selected_date.strftime('%Y-%m')
                if 'date' in df.columns:
                    # 轉換日期格式進行篩選
                    df['date_str'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m')
                    filtered_df = df[df['date_str'] == year_month].copy()

                    if not filtered_df.empty:
                        # 添加股票名稱欄位（從索引中提取）
                        if 'stock_id' in filtered_df.columns:
                            # 提取股票代碼和名稱
                            filtered_df['stock_name'] = filtered_df['stock_id'].apply(
                                lambda x: x.split(' ')[1] if ' ' in str(x) and len(x.split(' ')) > 1 else '未知'
                            )
                            filtered_df['stock_id'] = filtered_df['stock_id'].apply(
                                lambda x: x.split(' ')[0] if ' ' in str(x) else str(x)
                            )
                        
                        print(f"✅ 從 PKL 檔案讀取到 {len(filtered_df)} 筆月營收資料")
                        return filtered_df

            print(f"❌ 沒有找到 {selected_date.strftime('%Y-%m')} 的月營收資料")
            return None

        except Exception as e:
            print(f"❌ 從資料庫讀取月營收資料失敗: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_revenue_growth(self, df, selected_date):
        """計算月營收的YoY和MoM成長率"""
        try:
            # 確保必要的欄位存在
            required_columns = ['stock_id', 'stock_name', '當月營收', '上月營收', '去年當月營收']
            for col in required_columns:
                if col not in df.columns:
                    print(f"❌ 資料中缺少必要欄位: {col}")
                    return pd.DataFrame()

            # 轉換數值欄位
            df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
            df['上月營收'] = pd.to_numeric(df['上月營收'], errors='coerce')
            df['去年當月營收'] = pd.to_numeric(df['去年當月營收'], errors='coerce')

            # 計算YoY (Year over Year) 成長率
            df['YoY%'] = ((df['當月營收'] - df['去年當月營收']) / df['去年當月營收'] * 100).round(2)

            # 計算MoM (Month over Month) 成長率
            df['MoM%'] = ((df['當月營收'] - df['上月營收']) / df['上月營收'] * 100).round(2)

            # 處理無限值和NaN
            df['YoY%'] = df['YoY%'].replace([np.inf, -np.inf], np.nan)
            df['MoM%'] = df['MoM%'].replace([np.inf, -np.inf], np.nan)

            # 計算綜合評分
            df['綜合評分'] = df.apply(lambda row: self.calculate_comprehensive_score(
                row['YoY%'], row['MoM%']), axis=1)

            # 移除營收為0或負數的記錄
            df = df[df['當月營收'] > 0]

            print(f"✅ 計算完成，有效資料筆數: {len(df)}")
            return df

        except Exception as e:
            print(f"❌ 計算營收成長率失敗: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()

    def calculate_comprehensive_score(self, yoy: float, mom: float) -> float:
        """計算月營收綜合評分"""
        if pd.isna(yoy) or pd.isna(mom):
            return 0
        
        # YoY權重60%，MoM權重40%
        yoy_score = min(max(yoy, -100), 200) / 2  # 標準化到-50到100
        mom_score = min(max(mom, -100), 200) / 2  # 標準化到-50到100
        
        return yoy_score * 0.6 + mom_score * 0.4

def test_monthly_revenue_ranking():
    """測試月營收排行榜功能"""
    print("🚀 開始測試月營收排行榜功能...")
    
    # 創建測試實例
    tester = TestMonthlyRevenueRanking()
    
    # 設置測試日期為2025年6月（資料庫中最新的月份）
    tester.set_date(2025, 6, 15)
    
    # 測試綜合評分排行榜
    print("\n" + "="*60)
    print("🏆 測試綜合評分排行榜")
    print("="*60)
    
    ranking_data = tester.get_monthly_revenue_ranking("月營收排行榜(綜合評分)")
    
    if ranking_data:
        print(f"\n✅ 成功獲取 {len(ranking_data)} 筆排行榜資料")
        print("\n🏆 綜合評分排行榜前10名:")
        for i, data in enumerate(ranking_data[:10]):
            print(f"{data['排名']:2d}. {data['股票代碼']} {data['股票名稱']} "
                  f"營收:{data['當月營收']} YoY:{data['YoY%']} MoM:{data['MoM%']} "
                  f"評分:{data.get('綜合評分', 'N/A')}")
        
        print("\n🎉 月營收排行榜功能測試成功！")
        return True
    else:
        print("\n❌ 月營收排行榜功能測試失敗！")
        return False

if __name__ == "__main__":
    success = test_monthly_revenue_ranking()
    if success:
        print("\n✅ 所有測試通過！月營收排行榜功能正常運作。")
    else:
        print("\n❌ 測試失敗！需要進一步檢查。")
