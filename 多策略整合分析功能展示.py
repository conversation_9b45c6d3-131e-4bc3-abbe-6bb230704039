#!/usr/bin/env python3
"""
多策略整合分析系統功能展示
展示完善後的系統功能，包括信號顯示、圖表完善和導出功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """創建演示數據"""
    print("📊 創建演示數據...")
    
    # 生成更有趣的測試數據，包含明顯的技術指標信號
    dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
    dates = dates[dates.weekday < 5]  # 只保留工作日
    
    np.random.seed(123)  # 固定種子確保可重現
    
    # 創建有趣的價格模式
    base_price = 100
    prices = []
    
    for i in range(len(dates)):
        if i == 0:
            price = base_price
        else:
            # 創建多種模式
            if i < 30:  # 上升趨勢
                trend = 0.003
            elif i < 60:  # 震盪整理
                trend = 0.0005 * np.sin(i * 0.5)
            elif i < 90:  # 下跌趨勢
                trend = -0.002
            else:  # 反彈
                trend = 0.004
            
            # 添加週期性和隨機性
            cycle = 0.015 * np.sin(i * 0.2)
            noise = np.random.normal(0, 0.01)
            
            price = prices[-1] * (1 + trend + cycle + noise)
            price = max(price, 50)  # 設置最低價格
        
        prices.append(price)
    
    # 生成OHLCV數據
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        volatility = 0.025
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
        
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        volume = np.random.randint(500000, 2000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    print(f"✅ 演示數據創建完成，共 {len(df)} 個交易日")
    print(f"   價格範圍: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df

def demonstrate_signal_analysis():
    """展示信號分析功能"""
    print("\n🎯 展示多策略信號分析...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 創建演示數據
        demo_data = create_demo_data()
        
        # 創建分析器
        analyzer = MultiStrategySignalAnalyzer()
        
        # 分析所有策略
        all_signals = analyzer.analyze_all_strategies(demo_data)
        
        print("\n📋 各策略信號分析結果:")
        print("=" * 60)
        
        total_buy_signals = 0
        total_sell_signals = 0
        
        for strategy_name, signals in all_signals.items():
            buy_count = len(signals['buy'])
            sell_count = len(signals['sell'])
            total_buy_signals += buy_count
            total_sell_signals += sell_count
            
            # 計算強度統計
            buy_strengths = [s['strength'] for s in signals['buy']]
            sell_strengths = [s['strength'] for s in signals['sell']]
            
            avg_buy_strength = np.mean(buy_strengths) if buy_strengths else 0
            avg_sell_strength = np.mean(sell_strengths) if sell_strengths else 0
            
            strong_buy = len([s for s in buy_strengths if s >= 0.7])
            strong_sell = len([s for s in sell_strengths if s >= 0.7])
            
            print(f"\n🔸 {strategy_name}")
            print(f"   📈 買入信號: {buy_count:2d} 個 | 平均強度: {avg_buy_strength:.2f} | 強信號: {strong_buy} 個")
            print(f"   📉 賣出信號: {sell_count:2d} 個 | 平均強度: {avg_sell_strength:.2f} | 強信號: {strong_sell} 個")
            
            # 顯示最強的信號
            if signals['buy']:
                strongest_buy = max(signals['buy'], key=lambda x: x['strength'])
                date = demo_data.index[strongest_buy['index']].strftime('%Y-%m-%d')
                print(f"   🌟 最強買入: {date} (強度: {strongest_buy['strength']:.2f}, 價格: {strongest_buy['price']:.2f})")
            
            if signals['sell']:
                strongest_sell = max(signals['sell'], key=lambda x: x['strength'])
                date = demo_data.index[strongest_sell['index']].strftime('%Y-%m-%d')
                print(f"   ⭐ 最強賣出: {date} (強度: {strongest_sell['strength']:.2f}, 價格: {strongest_sell['price']:.2f})")
        
        print(f"\n📊 總計: {total_buy_signals} 個買入信號, {total_sell_signals} 個賣出信號")
        
        return all_signals, demo_data
        
    except Exception as e:
        print(f"❌ 信號分析展示失敗: {e}")
        return None, None

def demonstrate_consensus_analysis(all_signals, demo_data):
    """展示一致性分析功能"""
    print("\n🎯 展示信號一致性分析...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        analyzer = MultiStrategySignalAnalyzer()
        
        # 分析不同窗口大小的一致性
        windows = [3, 5, 7]
        
        for window in windows:
            consensus_points = analyzer.analyze_signal_consensus(all_signals, demo_data, window)
            
            print(f"\n📊 窗口大小 {window} 天的一致性分析:")
            print(f"   發現 {len(consensus_points)} 個高一致性點")
            
            if consensus_points:
                # 按一致性排序
                consensus_points.sort(key=lambda x: x['consensus'], reverse=True)
                
                print("   🏆 前5個最高一致性點:")
                for i, point in enumerate(consensus_points[:5]):
                    date = demo_data.index[point['index']].strftime('%Y-%m-%d')
                    signal_type = "買入" if point['type'] == 'buy' else "賣出"
                    emoji = "🟢" if point['type'] == 'buy' else "🔴"
                    
                    print(f"      {i+1}. {emoji} {date} - {signal_type} (一致性: {point['consensus']}/4, 價格: {point['price']:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 一致性分析展示失敗: {e}")
        return False

def demonstrate_chart_generation(all_signals, demo_data):
    """展示圖表生成功能"""
    print("\n📈 展示專業圖表生成...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        analyzer = MultiStrategySignalAnalyzer()
        
        # 生成專業圖表
        fig = analyzer.plot_multi_strategy_chart(
            demo_data, 
            all_signals,
            "多策略整合分析演示 - 台積電 (2330)"
        )
        
        # 保存圖表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"多策略整合分析演示_{timestamp}.png"
        fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        
        print(f"✅ 專業圖表已生成並保存為: {filename}")
        print("   📊 圖表特色:")
        print("      • 上圖: 價格走勢 + 四策略信號標記")
        print("      • 下圖: 各策略信號數量統計")
        print("      • 不同顏色和形狀區分策略")
        print("      • 信號強度影響標記大小")
        print("      • 高強度信號有文字標註")
        
        plt.close(fig)  # 關閉圖表避免內存洩漏
        
        return True
        
    except Exception as e:
        print(f"❌ 圖表生成展示失敗: {e}")
        return False

def demonstrate_export_functionality(all_signals, demo_data):
    """展示導出功能"""
    print("\n💾 展示完整導出功能...")
    
    try:
        import pandas as pd
        from datetime import datetime
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 創建詳細的導出數據
        export_data = {}
        
        # 1. 各策略詳細信號
        for strategy_name, signals in all_signals.items():
            strategy_data = []
            
            # 買入信號
            for signal in signals['buy']:
                strategy_data.append({
                    '日期': demo_data.index[signal['index']].strftime('%Y-%m-%d'),
                    '信號類型': '買入',
                    '價格': round(signal['price'], 2),
                    '強度': round(signal['strength'], 2),
                    '強度等級': '強' if signal['strength'] >= 0.7 else '中' if signal['strength'] >= 0.5 else '弱',
                    '描述': signal.get('text', ''),
                    '開盤價': round(demo_data.iloc[signal['index']]['open'], 2),
                    '最高價': round(demo_data.iloc[signal['index']]['high'], 2),
                    '最低價': round(demo_data.iloc[signal['index']]['low'], 2),
                    '成交量': demo_data.iloc[signal['index']]['volume']
                })
            
            # 賣出信號
            for signal in signals['sell']:
                strategy_data.append({
                    '日期': demo_data.index[signal['index']].strftime('%Y-%m-%d'),
                    '信號類型': '賣出',
                    '價格': round(signal['price'], 2),
                    '強度': round(signal['strength'], 2),
                    '強度等級': '強' if signal['strength'] >= 0.7 else '中' if signal['strength'] >= 0.5 else '弱',
                    '描述': signal.get('text', ''),
                    '開盤價': round(demo_data.iloc[signal['index']]['open'], 2),
                    '最高價': round(demo_data.iloc[signal['index']]['high'], 2),
                    '最低價': round(demo_data.iloc[signal['index']]['low'], 2),
                    '成交量': demo_data.iloc[signal['index']]['volume']
                })
            
            if strategy_data:
                df = pd.DataFrame(strategy_data)
                df = df.sort_values('日期')
                export_data[strategy_name] = df
        
        # 2. 策略統計摘要
        summary_data = []
        for strategy_name, signals in all_signals.items():
            buy_count = len(signals['buy'])
            sell_count = len(signals['sell'])
            
            buy_strengths = [s['strength'] for s in signals['buy']]
            sell_strengths = [s['strength'] for s in signals['sell']]
            
            strong_buy = len([s for s in buy_strengths if s >= 0.7])
            strong_sell = len([s for s in sell_strengths if s >= 0.7])
            
            summary_data.append({
                '策略名稱': strategy_name,
                '買入信號數': buy_count,
                '賣出信號數': sell_count,
                '總信號數': buy_count + sell_count,
                '強買入信號': strong_buy,
                '強賣出信號': strong_sell,
                '買入平均強度': round(np.mean(buy_strengths), 2) if buy_strengths else 0,
                '賣出平均強度': round(np.mean(sell_strengths), 2) if sell_strengths else 0,
                '信號活躍度': '高' if (buy_count + sell_count) > 10 else '中' if (buy_count + sell_count) > 5 else '低'
            })
        
        export_data['策略統計摘要'] = pd.DataFrame(summary_data)
        
        # 3. 一致性分析
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        analyzer = MultiStrategySignalAnalyzer()
        consensus_points = analyzer.analyze_signal_consensus(all_signals, demo_data, window=5)
        
        if consensus_points:
            consensus_data = []
            for point in consensus_points:
                consensus_data.append({
                    '日期': demo_data.index[point['index']].strftime('%Y-%m-%d'),
                    '信號類型': '買入' if point['type'] == 'buy' else '賣出',
                    '一致性': point['consensus'],
                    '可信度': '極高' if point['consensus'] == 4 else '高' if point['consensus'] == 3 else '中',
                    '價格': round(point['price'], 2),
                    '建議': '強烈建議' if point['consensus'] == 4 else '建議' if point['consensus'] == 3 else '參考'
                })
            
            export_data['一致性分析'] = pd.DataFrame(consensus_data)
        
        # 導出到Excel
        excel_filename = f"多策略分析完整報告_{timestamp}.xlsx"
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            for sheet_name, df in export_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ 完整分析報告已導出為: {excel_filename}")
        print("   📋 包含工作表:")
        for sheet_name in export_data.keys():
            print(f"      • {sheet_name}")
        
        # 顯示統計信息
        total_signals = sum(len(signals['buy']) + len(signals['sell']) for signals in all_signals.values())
        total_consensus = len(consensus_points) if consensus_points else 0
        
        print(f"\n📊 導出數據統計:")
        print(f"   • 總信號數: {total_signals}")
        print(f"   • 一致性點: {total_consensus}")
        print(f"   • 數據期間: {demo_data.index[0].strftime('%Y-%m-%d')} 至 {demo_data.index[-1].strftime('%Y-%m-%d')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 導出功能展示失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 多策略整合分析系統功能展示")
    print("=" * 60)
    print("📝 本展示將演示完善後的系統功能:")
    print("   • 🎯 四策略信號分析")
    print("   • 🔍 信號一致性分析")
    print("   • 📈 專業圖表生成")
    print("   • 💾 完整導出功能")
    print("=" * 60)
    
    # 1. 信號分析展示
    all_signals, demo_data = demonstrate_signal_analysis()
    if all_signals is None:
        print("❌ 信號分析失敗，無法繼續展示")
        return False
    
    # 2. 一致性分析展示
    demonstrate_consensus_analysis(all_signals, demo_data)
    
    # 3. 圖表生成展示
    demonstrate_chart_generation(all_signals, demo_data)
    
    # 4. 導出功能展示
    demonstrate_export_functionality(all_signals, demo_data)
    
    print("\n" + "=" * 60)
    print("🎉 多策略整合分析系統功能展示完成！")
    print("\n💡 系統特色總結:")
    print("   🎯 四策略整合: RSI、MACD、布林通道、移動平均交叉")
    print("   📊 智能強度評級: 每個信號都有0-1的強度評級")
    print("   🔍 一致性分析: 找出多策略確認的高可信度點")
    print("   🎨 專業視覺化: 不同顏色形狀標記，強度影響大小")
    print("   💾 完整導出: 支援圖表PNG和數據Excel導出")
    print("   🖥️ 友好界面: 直觀的GUI操作界面")
    
    print("\n🎯 使用建議:")
    print("   • 重點關注一致性≥3的信號點")
    print("   • 優先考慮強度≥0.7的高強度信號")
    print("   • 結合多個策略確認降低風險")
    print("   • 定期導出分析結果進行回顧")
    
    print("\n✨ 系統現在已完全可用，可為投資決策提供專業級技術分析支援！")
    
    return True

if __name__ == '__main__':
    success = main()
    print(f"\n{'🎉 展示成功完成' if success else '❌ 展示過程中出現問題'}")
