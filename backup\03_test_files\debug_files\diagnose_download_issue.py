#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷月營收下載問題
"""

import os
import sys
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('download_diagnosis.log', encoding='utf-8')
    ]
)

def check_environment():
    """檢查環境設置"""
    print("🔍 檢查環境設置...")
    
    # 檢查Python版本
    print(f"Python版本: {sys.version}")
    
    # 檢查必要的模組
    required_modules = ['selenium', 'pandas', 'requests', 'openpyxl']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 已安裝")
        except ImportError:
            print(f"❌ {module} 未安裝")
    
    # 檢查Chrome瀏覽器
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✅ Chrome WebDriver 可用")
    except Exception as e:
        print(f"❌ Chrome WebDriver 問題: {e}")

def test_goodinfo_access():
    """測試GoodInfo網站訪問"""
    print("\n🌐 測試GoodInfo網站訪問...")
    
    try:
        import requests
        
        # 測試基本連接
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 網站可訪問")
            
            # 檢查內容
            if '營收' in response.text or '月營收' in response.text:
                print("✅ 頁面包含營收數據")
            else:
                print("⚠️ 頁面可能不包含營收數據")
                
            # 檢查是否有匯出功能
            if 'Excel' in response.text or '匯出' in response.text:
                print("✅ 頁面包含匯出功能")
            else:
                print("⚠️ 頁面可能不包含匯出功能")
                
        else:
            print(f"❌ 網站訪問失敗，狀態碼: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 網站訪問測試失敗: {e}")

def test_downloader():
    """測試下載器功能"""
    print("\n🔧 測試下載器功能...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器實例創建成功")
        
        # 測試簡單的網頁抓取
        print("🧪 測試網頁抓取功能...")
        result = downloader.download_with_scraper("2330")
        
        if result:
            print(f"✅ 網頁抓取成功: {result}")
        else:
            print("❌ 網頁抓取失敗")
            
    except Exception as e:
        print(f"❌ 下載器測試失敗: {e}")
        import traceback
        traceback.print_exc()

def check_download_directory():
    """檢查下載目錄"""
    print("\n📁 檢查下載目錄...")
    
    possible_dirs = [
        "D:/Finlab/history/tables/monthly_revenue",
        "C:/Users/<USER>/Downloads",
        "./downloads",
        "./data"
    ]
    
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目錄存在: {dir_path}")
            try:
                files = os.listdir(dir_path)
                excel_files = [f for f in files if f.endswith(('.xls', '.xlsx'))]
                print(f"   Excel檔案數量: {len(excel_files)}")
                if excel_files:
                    print(f"   最新檔案: {excel_files[-1]}")
            except Exception as e:
                print(f"   ⚠️ 無法讀取目錄: {e}")
        else:
            print(f"❌ 目錄不存在: {dir_path}")

def main():
    """主函數"""
    print("🚀 開始診斷月營收下載問題...")
    print("=" * 50)
    
    check_environment()
    test_goodinfo_access()
    check_download_directory()
    test_downloader()
    
    print("\n" + "=" * 50)
    print("📋 診斷完成！請查看上述結果找出問題所在。")
    print("📄 詳細日誌已保存到 download_diagnosis.log")

if __name__ == "__main__":
    main()
