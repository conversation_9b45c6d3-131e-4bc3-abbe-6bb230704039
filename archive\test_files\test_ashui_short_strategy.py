#!/usr/bin/env python3
"""
阿水二式策略测试脚本
测试新实现的阿水二式空方策略是否能正常工作
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AshuiShortStrategyTester:
    """阿水二式策略测试器"""
    
    def __init__(self):
        self.name = "阿水二式策略测试"
    
    def generate_test_data(self, days=150):
        """生成测试数据 - 模拟空方市场"""
        # 创建日期范围
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # 生成基础价格数据（模拟股票从高位下跌的走势）
        np.random.seed(42)  # 固定随机种子以便重现
        
        # 模拟一个从高位下跌的股票
        base_price = 100  # 更高的起始价格，确保最终价格 > 30元
        prices = []
        volumes = []
        
        for i in range(days):
            if i < 50:  # 前50天高位盘整
                # 高位盘整期
                price_change = np.random.normal(0, 0.8)
                volume = np.random.normal(150000, 30000)
            elif i < 100:  # 开始下跌
                # 下跌初期：MA开始下弯
                price_change = np.random.normal(-0.5, 0.8)
                volume = np.random.normal(120000, 25000)
            elif i < 130:  # 反弹失败期
                # 反弹失败期：碰下通道后反弹过不了20MA
                if i % 5 == 0:  # 偶尔反弹
                    price_change = np.random.normal(1.0, 0.5)
                else:
                    price_change = np.random.normal(-0.3, 0.6)
                volume = np.random.normal(100000, 20000)
            else:  # 最后阶段：完美的空方信号
                # 空方信号期：持续下跌 + 空头排列
                if i == days - 1:  # 最后一天：完美的阿水二式信号
                    price_change = -2.0  # 强力下跌
                    volume = np.random.normal(200000, 30000)  # 放量下跌
                else:
                    price_change = np.random.normal(-0.8, 0.5)
                    volume = np.random.normal(130000, 25000)
            
            if i == 0:
                price = base_price
            else:
                price = max(prices[-1] + price_change, 35)  # 确保价格不低于35元
            
            prices.append(price)
            volumes.append(max(volume, 10000))  # 确保成交量不为负
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
            'Close': prices,
            'Volume': volumes
        })
        
        # 确保OHLC逻辑正确
        for i in range(len(df)):
            high = max(df.iloc[i]['Open'], df.iloc[i]['Close'])
            low = min(df.iloc[i]['Open'], df.iloc[i]['Close'])
            df.at[i, 'High'] = max(df.iloc[i]['High'], high)
            df.at[i, 'Low'] = min(df.iloc[i]['Low'], low)
        
        return df
    
    def calculate_ashui_short_indicators(self, df):
        """计算阿水二式所需的技术指标"""
        # 移动平均线系统
        df['MA20'] = df['Close'].rolling(window=20).mean()
        df['MA60'] = df['Close'].rolling(window=60).mean()
        df['MA120'] = df['Close'].rolling(window=120).mean()
        
        # 20日标准差
        df['STD20'] = df['Close'].rolling(window=20).std()
        
        # 布林带（使用2.0倍标准差）
        df['BB_Upper'] = df['MA20'] + (df['STD20'] * 2.0)
        df['BB_Lower'] = df['MA20'] - (df['STD20'] * 2.0)
        
        # MA趋势检查（20MA下弯）
        df['MA20_Falling'] = df['MA20'] < df['MA20'].shift(1)
        df['MA20_Falling_3Days'] = (
            (df['MA20'] < df['MA20'].shift(1)) & 
            (df['MA20'].shift(1) < df['MA20'].shift(2)) &
            (df['MA20'].shift(2) < df['MA20'].shift(3))
        )
        
        # 新低检查
        df['Low_20'] = df['Low'].rolling(window=20).min()
        df['Low_60'] = df['Low'].rolling(window=60).min()
        
        # 高点检查（用于判断高档）
        df['High_60'] = df['High'].rolling(window=60).max()
        df['High_120'] = df['High'].rolling(window=120).max()
        
        return df
    
    def check_ashui_short_basic_filter(self, df):
        """基本筛选"""
        latest = df.iloc[-1]
        close_price = latest['Close']
        volume = latest['Volume']
        
        # 股价要有一定高度
        if close_price < 30:
            return False, f"股价{close_price:.2f} < 30元（获利空间不足）"
        
        # 成交额 > 500万
        turnover = close_price * volume / 10000
        if turnover < 500:
            return False, f"成交额{turnover:.0f}万 < 500万"
        
        # 避免过低价股
        if close_price < 10:
            return False, f"股价{close_price:.2f} < 10元"
        
        return True, f"基本筛选✓(价{close_price:.2f}, 额{turnover:.0f}万)"
    
    def check_ma20_downtrend(self, df):
        """检查20MA下弯"""
        latest = df.iloc[-1]
        ma20_falling = latest['MA20_Falling']
        ma20_falling_3days = latest['MA20_Falling_3Days']
        
        if not ma20_falling:
            return False, "20MA未下弯"
        
        if ma20_falling_3days:
            return True, "20MA下弯✓(3日连跌)"
        else:
            return True, "20MA下弯✓"
    
    def check_rebound_failure(self, df):
        """检查反弹失败"""
        # 检查最近10天内是否有碰到下通道
        recent_10_days = df.iloc[-10:]
        touched_lower = (recent_10_days['Low'] <= recent_10_days['BB_Lower']).any()
        
        if not touched_lower:
            return False, "近10日未碰下通道"
        
        # 检查反弹是否过不了20MA
        recent_5_days = df.iloc[-5:]
        latest = df.iloc[-1]
        ma20 = latest['MA20']
        
        rebound_high = recent_5_days['High'].max()
        if rebound_high >= ma20:
            return False, f"反弹高点{rebound_high:.2f}已过20MA{ma20:.2f}"
        
        close_price = latest['Close']
        if close_price >= ma20:
            return False, f"收盘价{close_price:.2f}已站上20MA{ma20:.2f}"
        
        distance_pct = ((ma20 - close_price) / ma20) * 100
        return True, f"反弹失败✓(距20MA {distance_pct:.1f}%)"
    
    def check_bear_alignment(self, df):
        """检查空头排列"""
        latest = df.iloc[-1]
        ma20 = latest['MA20']
        ma60 = latest['MA60']
        ma120 = latest['MA120']
        
        if ma20 < ma60 < ma120:
            return True, "空头排列✓(完整空头排列)"
        elif ma20 < ma60:
            return True, "空头排列✓(短期空头)"
        else:
            return False, f"均线排列不佳：MA20({ma20:.2f}) >= MA60({ma60:.2f})"
    
    def check_high_level_distribution(self, df):
        """检查高档盘头"""
        latest = df.iloc[-1]
        current_price = latest['Close']
        high_60 = latest['High_60']
        high_120 = latest['High_120']
        
        if current_price >= high_60 * 0.8:
            if current_price >= high_120 * 0.9:
                return True, "高档盘头✓(近120日高点)"
            else:
                return True, "高档盘头✓(近60日高点)"
        
        return False, "非高档区域"
    
    def check_new_low(self, df):
        """检查创新低"""
        latest = df.iloc[-1]
        current_low = latest['Low']
        low_20 = latest['Low_20']
        low_60 = latest['Low_60']
        
        if current_low <= low_20:
            if current_low <= low_60:
                return True, "创60日新低"
            else:
                return True, "创20日新低"
        
        return False, "未创新低"
    
    def test_ashui_short_strategy(self, df):
        """测试阿水二式策略"""
        try:
            print("=" * 60)
            print("🔴 阿水二式策略测试")
            print("=" * 60)
            
            # 计算指标
            df = self.calculate_ashui_short_indicators(df)
            
            # 显示最新数据
            latest = df.iloc[-1]
            print(f"📊 最新数据：")
            print(f"   收盘价: {latest['Close']:.2f}")
            print(f"   成交量: {latest['Volume']:,.0f}")
            print(f"   20MA: {latest['MA20']:.2f}")
            print(f"   60MA: {latest['MA60']:.2f}")
            print(f"   120MA: {latest['MA120']:.2f}")
            print()
            
            # 逐步检查条件
            print("🔍 条件检查：")
            
            # 1. 基本筛选
            basic_result = self.check_ashui_short_basic_filter(df)
            print(f"   1. 基本筛选: {'✅' if basic_result[0] else '❌'} {basic_result[1]}")
            
            # 2. 20MA下弯
            ma_down_result = self.check_ma20_downtrend(df)
            print(f"   2. 20MA下弯: {'✅' if ma_down_result[0] else '❌'} {ma_down_result[1]}")
            
            # 3. 反弹失败
            rebound_fail_result = self.check_rebound_failure(df)
            print(f"   3. 反弹失败: {'✅' if rebound_fail_result[0] else '❌'} {rebound_fail_result[1]}")
            
            # 4. 空头排列
            bear_alignment_result = self.check_bear_alignment(df)
            print(f"   4. 空头排列: {'✅' if bear_alignment_result[0] else '❌'} {bear_alignment_result[1]}")
            
            # 5. 高档盘头
            high_level_result = self.check_high_level_distribution(df)
            print(f"   5. 高档盘头: {'✅' if high_level_result[0] else '❌'} {high_level_result[1]}")
            
            # 6. 创新低
            new_low_result = self.check_new_low(df)
            print(f"   6. 创新低: {'✅' if new_low_result[0] else '❌'} {new_low_result[1]}")
            
            # 综合判断
            core_conditions = all([
                basic_result[0],
                ma_down_result[0], 
                rebound_fail_result[0],
                bear_alignment_result[0]
            ])
            
            print()
            print("📈 最终结果：")
            if core_conditions:
                print("🎉 ✅ 符合阿水二式策略！这是一个潜在的空方信号！")
                if high_level_result[0]:
                    print(f"🚀 额外加分：{high_level_result[1]}")
                if new_low_result[0]:
                    print(f"🚀 额外加分：{new_low_result[1]}")
            else:
                print("❌ 不符合阿水二式策略")
                failed_conditions = []
                if not basic_result[0]: failed_conditions.append("基本筛选")
                if not ma_down_result[0]: failed_conditions.append("20MA下弯")
                if not rebound_fail_result[0]: failed_conditions.append("反弹失败")
                if not bear_alignment_result[0]: failed_conditions.append("空头排列")
                print(f"   未满足条件: {', '.join(failed_conditions)}")
            
            print("=" * 60)
            return core_conditions
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 启动阿水二式策略测试")
    
    tester = AshuiShortStrategyTester()
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_df = tester.generate_test_data(150)
    
    # 运行测试
    result = tester.test_ashui_short_strategy(test_df)
    
    print(f"\n🏁 测试完成，结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()
