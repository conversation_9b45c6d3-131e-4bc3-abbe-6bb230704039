#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試主程序調用PKL策略的方式
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 設置日誌級別
logging.basicConfig(level=logging.INFO)

# 添加strategies目錄到路徑
sys.path.append('strategies')

def create_test_price_data(stock_id, days=100):
    """創建測試用股價數據"""
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 模擬股價數據
    np.random.seed(42)
    base_price = 50
    prices = []
    current_price = base_price
    
    for i in range(days):
        change = np.random.normal(0, 0.02)  # 2%標準差
        current_price *= (1 + change)
        prices.append(current_price)
    
    # 創建OHLCV數據
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p * np.random.uniform(0.99, 1.01) for p in prices],
        'High': [p * np.random.uniform(1.00, 1.05) for p in prices],
        'Low': [p * np.random.uniform(0.95, 1.00) for p in prices],
        'Close': prices,
        'Volume': [np.random.randint(100000, 1000000) for _ in range(days)]
    })
    
    df.set_index('Date', inplace=True)
    return df

def test_main_program_style_call():
    """測試主程序風格的調用"""
    print("🧪 測試主程序風格的PKL策略調用")
    print("=" * 60)
    
    # 模擬主程序的調用方式
    class MockMainProgram:
        def __init__(self):
            self._turtle_strategy_instance = None
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬主程序的策略調用方法"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據"

                # 使用單例模式避免重複載入PKL數據
                if not hasattr(self, '_turtle_strategy_instance') or self._turtle_strategy_instance is None:
                    from high_yield_turtle_strategy import HighYieldTurtleStrategy
                    self._turtle_strategy_instance = HighYieldTurtleStrategy()

                turtle_strategy = self._turtle_strategy_instance

                # 模擬FinMind數據（空）
                finmind_data = {}

                # 執行高殖利率烏龜分析
                result = turtle_strategy.analyze_stock(df, finmind_data=finmind_data, stock_id=stock_id)

                # 轉換為舊格式的返回值
                if result['suitable']:
                    return True, result['reason']
                else:
                    return False, result['reason']

            except Exception as e:
                logging.error(f"策略檢查失敗: {e}")
                return False, f"策略檢查錯誤: {str(e)}"
    
    # 創建模擬主程序實例
    mock_main = MockMainProgram()
    
    # 測試多支股票
    test_stocks = ['1101', '1102', '1108', '1109', '2330']
    
    print("📊 測試結果:")
    print("-" * 50)
    
    success_count = 0
    for stock_id in test_stocks:
        try:
            # 創建測試數據
            test_df = create_test_price_data(stock_id)
            
            # 調用策略
            result, message = mock_main.check_high_yield_turtle_strategy(test_df, stock_id=stock_id)
            
            # 顯示結果
            status = "✅ 符合" if result else "❌ 不符合"
            print(f"{status} {stock_id}: {message[:120]}...")
            
            if result:
                success_count += 1
                
        except Exception as e:
            print(f"❌ {stock_id}: 測試失敗 - {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 測試結果: {success_count}/{len(test_stocks)} 支股票符合策略")
    
    # 檢查策略實例狀態
    if mock_main._turtle_strategy_instance:
        strategy = mock_main._turtle_strategy_instance
        print(f"\n🔍 策略實例狀態:")
        print(f"  PKL數據快取: {'✅ 已載入' if strategy.pe_data_cache is not None else '❌ 未載入'}")
        if strategy.pe_data_cache is not None:
            print(f"  PKL數據大小: {strategy.pe_data_cache.shape}")
            
            # 測試直接PKL數據獲取
            print(f"\n🧪 直接PKL數據獲取測試:")
            for stock_id in ['1101', '1108', '2330']:
                pkl_data = strategy.get_stock_pkl_data(stock_id)
                if pkl_data:
                    print(f"  ✅ {stock_id}: {pkl_data['stock_name']} - 殖利率{pkl_data['dividend_yield']:.1f}%")
                else:
                    print(f"  ❌ {stock_id}: 無PKL數據")
    
    return True

def test_direct_strategy_call():
    """測試直接策略調用"""
    print("\n🔧 測試直接策略調用")
    print("=" * 60)
    
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        # 創建策略實例
        strategy = HighYieldTurtleStrategy()
        
        # 測試股票
        test_stock = '1108'
        test_df = create_test_price_data(test_stock)
        
        print(f"📊 測試股票: {test_stock}")
        print(f"📊 數據形狀: {test_df.shape}")
        
        # 直接調用分析方法
        result = strategy.analyze_stock(test_df, stock_id=test_stock)
        
        print(f"📋 分析結果:")
        print(f"  符合策略: {result['suitable']}")
        print(f"  評分: {result.get('score', 0)}/100")
        print(f"  原因: {result['reason']}")
        
        details = result.get('details', {})
        if details:
            print(f"  數據來源: {details.get('data_source', '未知')}")
            print(f"  殖利率: {details.get('dividend_yield', 0):.1f}%")
            print(f"  PE比: {details.get('pe_ratio', 0):.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接策略調用失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 主程序調用測試套件")
    print("=" * 80)
    
    # 測試主程序風格調用
    main_test = test_main_program_style_call()
    
    # 測試直接策略調用
    direct_test = test_direct_strategy_call()
    
    print("\n" + "=" * 80)
    print("📊 測試總結")
    print("=" * 80)
    
    results = [
        ("主程序風格調用", main_test),
        ("直接策略調用", direct_test)
    ]
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 總體結果: {passed_count}/{total_count} 測試通過")
    
    if passed_count == total_count:
        print("🎉 主程序調用測試成功！")
        print("\n💡 建議:")
        print("  1. 檢查主程序中的日誌級別設置")
        print("  2. 確認股票代碼正確傳遞")
        print("  3. 驗證PKL數據路徑")
    else:
        print("⚠️ 部分測試失敗，需要進一步調試")

if __name__ == "__main__":
    main()
