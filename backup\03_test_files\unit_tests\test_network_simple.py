"""
簡單的網路連線測試
"""

import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_basic_connection():
    """測試基本連線"""
    try:
        print('🔍 測試基本網路連線...')
        
        # 測試Google
        response = requests.get('https://www.google.com', timeout=10, verify=False)
        print(f'✅ Google連線成功: {response.status_code}')
        
        # 測試MOPS基本頁面
        mops_url = 'https://mopsov.twse.com.tw/mops/web/t51sb01'
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(mops_url, headers=headers, timeout=30, verify=False)
        print(f'✅ MOPS基本頁面連線成功: {response.status_code}')
        
        if '基本資料查詢彙總表' in response.text:
            print('✅ MOPS頁面內容正確')
        else:
            print('⚠️ MOPS頁面內容可能有問題')
        
        return True
        
    except Exception as e:
        print(f'❌ 基本連線測試失敗: {e}')
        return False

def test_mops_api():
    """測試MOPS API"""
    try:
        print('\\n🔍 測試MOPS API...')
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Referer': 'https://mopsov.twse.com.tw/mops/web/t51sb01'
        }
        
        # 測試查詢API
        api_url = 'https://mopsov.twse.com.tw/mops/web/ajax_t51sb01'
        params = {
            'encodeURIComponent': '1',
            'step': '1',
            'firstin': '1',
            'TYPEK': 'sii',
            'code': ''
        }
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(api_url, params=params, timeout=30, verify=False)
        print(f'✅ MOPS API連線成功: {response.status_code}')
        print(f'📏 回應長度: {len(response.text)} 字元')
        
        # 檢查回應內容
        if 'form' in response.text.lower():
            print('✅ 回應包含form標籤')
        
        if 'filename' in response.text.lower():
            print('✅ 回應包含filename')
        
        if 'csv' in response.text.lower():
            print('✅ 回應包含CSV相關內容')
        
        # 顯示部分內容
        print('\\n📄 回應內容片段:')
        print(response.text[:500] + '...' if len(response.text) > 500 else response.text)
        
        return True
        
    except Exception as e:
        print(f'❌ MOPS API測試失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始網路連線測試")
    print("=" * 50)
    
    success1 = test_basic_connection()
    success2 = test_mops_api()
    
    print("\\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有網路測試通過")
    else:
        print("❌ 部分網路測試失敗")
