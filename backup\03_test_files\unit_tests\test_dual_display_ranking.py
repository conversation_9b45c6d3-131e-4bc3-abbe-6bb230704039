#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試排行榜雙側顯示功能
"""

def test_dual_display_functionality():
    """測試排行榜雙側顯示功能"""
    print("🔍 測試排行榜雙側顯示功能")
    print("=" * 50)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查1: 新的display_ranking_results方法
        print("\n1. 檢查display_ranking_results方法...")
        if 'def display_ranking_results(self, ranking_data, ranking_type):' in content:
            if '同時更新右側表格和左側列表' in content:
                print("   ✅ display_ranking_results已修改為雙側顯示")
            else:
                print("   ❌ display_ranking_results未包含雙側顯示邏輯")
        else:
            print("   ❌ 找不到display_ranking_results方法")
        
        # 檢查2: update_right_side_table方法
        print("\n2. 檢查update_right_side_table方法...")
        if 'def update_right_side_table(self, ranking_data, ranking_type):' in content:
            print("   ✅ update_right_side_table方法已添加")
        else:
            print("   ❌ 找不到update_right_side_table方法")
        
        # 檢查3: update_left_side_list方法
        print("\n3. 檢查update_left_side_list方法...")
        if 'def update_left_side_list(self, ranking_data, ranking_type):' in content:
            print("   ✅ update_left_side_list方法已添加")
        else:
            print("   ❌ 找不到update_left_side_list方法")
        
        # 檢查4: 左側列表更新邏輯
        print("\n4. 檢查左側列表更新邏輯...")
        if 'self.filtered_stocks_list.clear()' in content:
            print("   ✅ 包含清空左側列表邏輯")
        else:
            print("   ❌ 缺少清空左側列表邏輯")
        
        if 'self.filtered_stocks_list.addItem(item)' in content:
            print("   ✅ 包含添加項目到左側列表邏輯")
        else:
            print("   ❌ 缺少添加項目到左側列表邏輯")
        
        # 檢查5: 顏色設置
        print("\n5. 檢查顏色設置...")
        if 'QColor(255, 80, 80)' in content and 'QColor(80, 200, 80)' in content:
            print("   ✅ 包含左側列表顏色設置（紅色漲、綠色跌）")
        else:
            print("   ❌ 缺少左側列表顏色設置")
        
        # 檢查6: 格式化顯示
        print("\n6. 檢查格式化顯示...")
        if 'item_text = f"{i:2d}. {stock_id} {stock_name} {close_price:.2f}' in content:
            print("   ✅ 包含排名格式化顯示")
        else:
            print("   ❌ 缺少排名格式化顯示")
        
        # 檢查7: 成交量特殊處理
        print("\n7. 檢查成交量排行榜特殊處理...")
        if '量:{volume_text}' in content:
            print("   ✅ 包含成交量排行榜特殊顯示格式")
        else:
            print("   ❌ 缺少成交量排行榜特殊顯示格式")
        
        # 檢查8: switch_to_result_tab改進
        print("\n8. 檢查switch_to_result_tab改進...")
        if '跳轉到選股結果活頁（左側和右側）' in content:
            print("   ✅ switch_to_result_tab已改進為雙側跳轉")
        else:
            print("   ❌ switch_to_result_tab未改進")
        
        print("\n" + "=" * 50)
        print("✅ 雙側顯示功能檢查完成！")
        
        print("\n📋 功能摘要:")
        print("🎯 **雙側顯示特色**:")
        print("• 右側表格: 完整的排行榜數據表格，包含所有欄位")
        print("• 左側列表: 簡潔的排行榜列表，帶排名序號")
        print("• 顏色標示: 漲跌幅用紅綠色區分")
        print("• 格式化顯示: 成交量用K/M單位，價格保留2位小數")
        print("• 自動跳轉: 同時跳轉左右兩側的選股結果標籤")
        
        print("\n🎨 **左側列表格式**:")
        print("• 漲停/跌停排行榜: '序號. 代碼 名稱 價格 ±漲跌幅%'")
        print("• 成交量排行榜: '序號. 代碼 名稱 價格 量:成交量'")
        print("• 顏色: 紅色(漲) / 綠色(跌) / 灰色(平盤)")
        
        print("\n🔧 **技術實現**:")
        print("• display_ranking_results(): 主控制方法")
        print("• update_right_side_table(): 更新右側表格")
        print("• update_left_side_list(): 更新左側列表")
        print("• switch_to_result_tab(): 智能標籤跳轉")
        
        print("\n💡 **使用優勢**:")
        print("• 用戶可以在右側查看K線圖時，左側仍能看到排行榜")
        print("• 左側列表提供快速瀏覽，右側表格提供詳細信息")
        print("• 雙重顯示確保信息不會遺失")
        
        return True
        
    except FileNotFoundError:
        print("❌ 找不到 O3mh_gui_v21_optimized.py 檔案")
        return False
    except Exception as e:
        print(f"❌ 檢查過程發生錯誤: {e}")
        return False

if __name__ == "__main__":
    test_dual_display_functionality()
