#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 price 和 bargin_report 修正結果
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime

def test_price_date_range():
    """測試 price 任務的日期範圍獲取"""
    
    print("=" * 80)
    print("🔍 測試 price 任務日期範圍獲取")
    print("=" * 80)
    
    # 檢查 newprice.db 是否存在
    price_db_path = 'D:/Finlab/history/tables/newprice.db'
    
    if os.path.exists(price_db_path):
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='price'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                # 獲取日期範圍
                cursor.execute('SELECT MIN(date), MAX(date) FROM price WHERE date IS NOT NULL')
                date_range_result = cursor.fetchone()
                
                if date_range_result and date_range_result[0]:
                    first_date = pd.to_datetime(date_range_result[0])
                    last_date = pd.to_datetime(date_range_result[1])
                    print(f"✅ newprice.db 日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                    
                    # 檢查資料筆數
                    cursor.execute('SELECT COUNT(*) FROM price')
                    count = cursor.fetchone()[0]
                    print(f"📊 總資料筆數: {count:,}")
                    
                    # 檢查最近幾天的資料
                    cursor.execute('SELECT date, COUNT(*) FROM price WHERE date >= ? GROUP BY date ORDER BY date DESC LIMIT 5', 
                                 (last_date - pd.Timedelta(days=10),))
                    recent_data = cursor.fetchall()
                    print(f"📅 最近資料:")
                    for date, count in recent_data:
                        print(f"   {date}: {count} 筆")
                    
                    return True
                else:
                    print(f"⚠️ newprice.db 中沒有有效的日期資料")
                    return False
            else:
                print(f"⚠️ newprice.db 中沒有 price 表格")
                return False
                
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取 newprice.db 失敗: {e}")
            return False
    else:
        print(f"⚠️ newprice.db 不存在: {price_db_path}")
        return False

def test_bargin_report_date_range():
    """測試 bargin_report 任務的日期範圍獲取"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試 bargin_report 任務日期範圍獲取")
    print("=" * 80)
    
    # 檢查 bargin_report.db 是否存在
    bargin_db_path = 'D:/Finlab/history/tables/bargin_report.db'
    
    if os.path.exists(bargin_db_path):
        try:
            conn = sqlite3.connect(bargin_db_path)
            cursor = conn.cursor()
            
            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bargin_report'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                # 獲取日期範圍
                cursor.execute('SELECT MIN(date), MAX(date) FROM bargin_report WHERE date IS NOT NULL')
                date_range_result = cursor.fetchone()
                
                if date_range_result and date_range_result[0]:
                    first_date = pd.to_datetime(date_range_result[0])
                    last_date = pd.to_datetime(date_range_result[1])
                    print(f"✅ bargin_report.db 日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                    
                    # 計算應該更新的天數
                    today = datetime.now()
                    if last_date.date() < today.date():
                        days_to_update = (today.date() - last_date.date()).days
                        print(f"📊 需要更新的天數: {days_to_update} 天")
                        print(f"   從 {(last_date + pd.Timedelta(days=1)).strftime('%Y-%m-%d')} 到 {today.strftime('%Y-%m-%d')}")
                        
                        if days_to_update <= 10:
                            print(f"✅ 更新天數合理 (≤10天)")
                        else:
                            print(f"⚠️ 更新天數較多 ({days_to_update}天)")
                    else:
                        print(f"✅ 資料已是最新，無需更新")
                    
                    # 檢查資料筆數
                    cursor.execute('SELECT COUNT(*) FROM bargin_report')
                    count = cursor.fetchone()[0]
                    print(f"📊 總資料筆數: {count:,}")
                    
                    return True
                else:
                    print(f"⚠️ bargin_report.db 中沒有有效的日期資料")
                    return False
            else:
                print(f"⚠️ bargin_report.db 中沒有 bargin_report 表格")
                return False
                
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取 bargin_report.db 失敗: {e}")
            return False
    else:
        print(f"⚠️ bargin_report.db 不存在: {price_db_path}")
        return False

def test_auto_update_logic():
    """測試 auto_update.py 中的邏輯修正"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試 auto_update.py 邏輯修正")
    print("=" * 80)
    
    try:
        # 檢查修正的邏輯是否存在
        with open('auto_update.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查 price 的特殊處理是否添加
        if "elif table_name == 'price':" in content and "newprice.db" in content:
            print(f"✅ price 任務的特殊處理已添加")
        else:
            print(f"❌ price 任務的特殊處理未找到")
            return False
        
        # 檢查 bargin_report 的邏輯是否修正
        if "if not last_date:" in content and "從2011年開始（三大法人資料較早）" in content:
            print(f"✅ bargin_report 的邏輯已修正")
        else:
            print(f"❌ bargin_report 的邏輯修正未找到")
            return False
        
        # 檢查是否移除了錯誤的邏輯
        if "start_update = datetime.datetime(2011, 1, 1)" in content:
            # 檢查這行是否在正確的條件下
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "start_update = datetime.datetime(2011, 1, 1)" in line:
                    # 檢查前面幾行是否有正確的條件
                    context = '\n'.join(lines[max(0, i-5):i+1])
                    if "if not last_date:" in context:
                        print(f"✅ 2011年開始的邏輯在正確的條件下")
                    else:
                        print(f"⚠️ 2011年開始的邏輯可能仍有問題")
                        return False
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查 auto_update.py 失敗: {e}")
        return False

def simulate_date_range_calculation():
    """模擬日期範圍計算"""
    
    print(f"\n" + "=" * 80)
    print("🧮 模擬日期範圍計算")
    print("=" * 80)
    
    # 模擬 bargin_report 的情況
    print(f"📊 模擬 bargin_report 增量更新:")
    
    # 假設最後日期是 2025-07-24
    last_date = pd.to_datetime('2025-07-24')
    today = datetime.now()
    
    # 計算增量更新範圍
    start_update = last_date + pd.Timedelta(days=1)
    end_update = today
    
    print(f"   最後日期: {last_date.strftime('%Y-%m-%d')}")
    print(f"   開始更新: {start_update.strftime('%Y-%m-%d')}")
    print(f"   結束更新: {end_update.strftime('%Y-%m-%d')}")
    
    days_to_update = (end_update.date() - start_update.date()).days + 1
    print(f"   預計更新天數: {days_to_update} 天")
    
    if days_to_update <= 10:
        print(f"   ✅ 更新天數合理")
        return True
    else:
        print(f"   ⚠️ 更新天數過多")
        return False

def main():
    """主函數"""
    
    print("🧪 price 和 bargin_report 修正驗證")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: price 日期範圍
    price_success = test_price_date_range()
    
    # 測試2: bargin_report 日期範圍
    bargin_success = test_bargin_report_date_range()
    
    # 測試3: auto_update.py 邏輯
    logic_success = test_auto_update_logic()
    
    # 測試4: 日期範圍計算模擬
    calc_success = simulate_date_range_calculation()
    
    print(f"\n" + "=" * 80)
    print("📊 修正驗證結果總結")
    print("=" * 80)
    
    print(f"🔍 price 日期範圍: {'✅ 成功' if price_success else '❌ 失敗'}")
    print(f"📊 bargin_report 日期範圍: {'✅ 成功' if bargin_success else '❌ 失敗'}")
    print(f"🔧 auto_update.py 邏輯: {'✅ 成功' if logic_success else '❌ 失敗'}")
    print(f"🧮 日期範圍計算: {'✅ 成功' if calc_success else '❌ 失敗'}")
    
    if all([price_success, bargin_success, logic_success, calc_success]):
        print(f"\n🎉 所有修正驗證通過！")
        print(f"💡 修正內容:")
        print(f"   ✅ 添加了 price 任務的 newprice.db 日期範圍獲取")
        print(f"   ✅ 修正了 bargin_report 的增量更新邏輯")
        print(f"   ✅ 移除了錯誤的2011年開始邏輯")
        print(f"   ✅ 確保只在沒有現有資料時才完整爬取")
        
        print(f"\n🚀 現在可以安全執行:")
        print(f"   python auto_update.py price")
        print(f"   python auto_update.py bargin_report")
        print(f"   python auto_update.py")
    else:
        print(f"\n⚠️ 部分修正驗證失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
