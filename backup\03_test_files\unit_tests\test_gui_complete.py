#!/usr/bin/env python3
"""
完整測試GUI的股票篩選流程
"""

import sqlite3
import pandas as pd
import sys
import os
import logging

# 添加當前目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_flow():
    """測試完整的股票篩選流程"""
    
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🔍 測試完整的GUI股票篩選流程...")
    
    # 1. 初始化股票篩選器
    stock_filter = None
    try:
        from stock_filter import StockFilter
        stock_filter = StockFilter()
        logging.info("✅ 股票篩選器初始化完成")
        print("✅ 股票篩選器初始化完成")
        
        # 測試篩選功能
        test_result = stock_filter.is_valid_stock_code('0054')
        logging.info(f"🧪 測試0054篩選結果: {test_result} (False表示被正確篩選掉)")
        print(f"🧪 測試0054篩選結果: {test_result} (False表示被正確篩選掉)")
        
    except ImportError as e:
        logging.warning(f"⚠️ 股票篩選器模組載入失敗: {e}")
        print(f"⚠️ 股票篩選器模組載入失敗: {e}")
        stock_filter = None
    except Exception as e:
        logging.error(f"❌ 股票篩選器初始化失敗: {e}")
        print(f"❌ 股票篩選器初始化失敗: {e}")
        stock_filter = None
    
    # 2. 連接資料庫並載入股票清單
    db_path = "D:/Finlab/history/tables/unified_stock_data_new.db"
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        table = 'stock_daily_data'
        
        # 模擬GUI的查詢邏輯
        try:
            query = f"""
                SELECT DISTINCT s.stock_id,
                       COALESCE(i.stock_name, '') as stock_name,
                       COALESCE(i.listing_status, '') as listing_status,
                       COALESCE(i.industry, '') as industry
                FROM {table} s
                LEFT JOIN stock_info i ON s.stock_id = i.stock_id
                WHERE s.stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                  AND LENGTH(s.stock_id) BETWEEN 4 AND 6
                ORDER BY
                    CASE
                        WHEN LENGTH(s.stock_id) = 4 THEN 1
                        WHEN LENGTH(s.stock_id) = 5 THEN 2
                        WHEN LENGTH(s.stock_id) = 6 THEN 3
                    END,
                    CAST(s.stock_id AS INTEGER)
            """
            logging.info(f"執行股票列表查詢（含基本資料）")
            df = pd.read_sql_query(query, conn)
            
            if df.empty:
                raise Exception("查詢結果為空，嘗試基本查詢")
                
        except Exception as e:
            # 如果上述查詢失敗，回退到基本查詢
            logging.warning(f"完整查詢失敗，使用基本查詢: {e}")
            query = f"""
                SELECT DISTINCT stock_id, '' as stock_name, '' as listing_status, '' as industry
                FROM {table}
                WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                  AND LENGTH(stock_id) BETWEEN 4 AND 6
                ORDER BY
                    CASE
                        WHEN LENGTH(stock_id) = 4 THEN 1
                        WHEN LENGTH(stock_id) = 5 THEN 2
                        WHEN LENGTH(stock_id) = 6 THEN 3
                    END,
                    CAST(stock_id AS INTEGER)
            """
            df = pd.read_sql_query(query, conn)
        
        logging.info(f"查詢到 {len(df)} 支股票")
        print(f"📊 查詢到 {len(df)} 支股票")
        
        if df.empty:
            print("❌ 未找到股票資料")
            return
        
        # 3. 應用股票篩選功能
        original_count = len(df)
        logging.info(f"🔍 檢查股票篩選器狀態: stock_filter={stock_filter is not None}")
        print(f"🔍 檢查股票篩選器狀態: stock_filter={stock_filter is not None}")
        
        if stock_filter:
            logging.info("🎯 開始執行股票篩選...")
            print("🎯 開始執行股票篩選...")
            
            # 篩選股票代碼
            valid_codes = filter_stock_codes(stock_filter, df['stock_id'].tolist())
            df = df[df['stock_id'].isin(valid_codes)]
            filtered_count = len(df)
            
            if filtered_count < original_count:
                logging.info(f"📊 股票清單篩選：原始 {original_count} 支 → 篩選後 {filtered_count} 支")
                print(f"📊 股票清單篩選：原始 {original_count} 支 → 篩選後 {filtered_count} 支")
            else:
                logging.info(f"📊 股票清單篩選：無股票被排除（{original_count} 支）")
                print(f"📊 股票清單篩選：無股票被排除（{original_count} 支）")
        else:
            logging.warning("⚠️ 股票篩選器未啟用或未初始化")
            print("⚠️ 股票篩選器未啟用或未初始化")
        
        # 4. 檢查0054是否在最終清單中
        final_stocks = df['stock_id'].tolist()
        if '0054' in final_stocks:
            print("❌ 0054仍在最終清單中！")
            # 找出0054在清單中的位置
            idx = final_stocks.index('0054')
            print(f"   位置: {idx}")
            print(f"   資料: {df.iloc[idx].to_dict()}")
        else:
            print("✅ 0054已被成功篩選掉")
        
        # 顯示前10支股票
        print(f"📊 前10支股票:")
        for i, row in df.head(10).iterrows():
            stock_name = row['stock_name'] if row['stock_name'] else '未知'
            print(f"   {row['stock_id']} {stock_name}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def filter_stock_codes(stock_filter, stock_codes):
    """
    篩選股票代碼清單（模擬GUI方法）
    """
    if not stock_codes:
        return []

    if stock_filter:
        return stock_filter.filter_stock_list(stock_codes)

    # 備用邏輯：如果股票篩選器未載入，返回原始清單
    return stock_codes

if __name__ == "__main__":
    test_complete_flow()
