#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略說明功能測試腳本
測試 CANSLIM 策略說明是否能正常顯示
"""
import sys
import os

def test_strategy_info():
    """測試策略說明功能"""
    print("🚀 測試策略說明功能...")
    
    try:
        # 設置環境變量以避免實際顯示 GUI
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入必要的模組
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 導入主程式
        import O3mh_gui_v21_optimized
        
        # 創建主視窗
        main_window = O3mh_gui_v21_optimized.StockScreenerGUI()
        
        print("✅ 主視窗創建成功")
        
        # 檢查策略說明相關方法是否存在
        methods_to_check = [
            'show_strategy_info',
            'show_builtin_strategy_info',
            'get_strategy_overview',
            'get_strategy_details',
            'get_usage_guide'
        ]
        
        print("\n🔍 檢查策略說明方法:")
        for method_name in methods_to_check:
            if hasattr(main_window, method_name):
                print(f"  ✅ {method_name} - 存在")
            else:
                print(f"  ❌ {method_name} - 不存在")
                return False
        
        # 測試策略說明內容生成
        print("\n🔍 測試策略說明內容生成:")
        
        strategy_name = "CANSLIM量價齊升"
        
        # 測試策略概述
        try:
            overview = main_window.get_strategy_overview(strategy_name)
            if "CANSLIM" in overview and "威廉·歐尼爾" in overview:
                print(f"  ✅ 策略概述生成成功，長度: {len(overview)} 字符")
            else:
                print(f"  ❌ 策略概述內容不完整")
                return False
        except Exception as e:
            print(f"  ❌ 策略概述生成失敗: {e}")
            return False
        
        # 測試策略詳細說明
        try:
            details = main_window.get_strategy_details(strategy_name)
            if "七大要素" in details and "評分系統" in details:
                print(f"  ✅ 策略詳細說明生成成功，長度: {len(details)} 字符")
            else:
                print(f"  ❌ 策略詳細說明內容不完整")
                return False
        except Exception as e:
            print(f"  ❌ 策略詳細說明生成失敗: {e}")
            return False
        
        # 測試使用建議
        try:
            usage = main_window.get_usage_guide(strategy_name)
            if "實戰使用指南" in usage and "風險控制" in usage:
                print(f"  ✅ 使用建議生成成功，長度: {len(usage)} 字符")
            else:
                print(f"  ❌ 使用建議內容不完整")
                return False
        except Exception as e:
            print(f"  ❌ 使用建議生成失敗: {e}")
            return False
        
        # 測試其他策略的默認處理
        try:
            other_overview = main_window.get_strategy_overview("其他策略")
            if "策略說明正在準備中" in other_overview:
                print(f"  ✅ 其他策略默認處理正常")
            else:
                print(f"  ❌ 其他策略默認處理異常")
                return False
        except Exception as e:
            print(f"  ❌ 其他策略默認處理失敗: {e}")
            return False
        
        # 清理
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 策略說明功能測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 策略說明功能測試")
    print("=" * 60)
    
    success = test_strategy_info()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 策略說明功能測試通過！")
        print("✅ CANSLIM 策略說明完整")
        print("✅ 策略概述內容豐富")
        print("✅ 詳細說明涵蓋七大要素")
        print("✅ 使用建議實用完整")
        print("✅ 其他策略默認處理正常")
    else:
        print("❌ 策略說明功能測試失敗")
        print("需要進一步檢查問題")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
