#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
除權息查詢系統增強功能測試
測試新增的股價欄位和開啟除權息目錄功能
"""

import sys
import os
import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """測試除權息資料庫連接"""
    print("🧪 測試除權息資料庫連接...")
    
    try:
        dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
        
        if not os.path.exists(dividend_db_path):
            print(f"❌ 除權息資料庫不存在: {dividend_db_path}")
            return False
        
        conn = sqlite3.connect(dividend_db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(dividend_data)")
        columns = cursor.fetchall()
        
        print("✅ 資料庫連接成功")
        print("📊 表格結構:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查資料數量
        cursor.execute("SELECT COUNT(*) FROM dividend_data")
        total_count = cursor.fetchone()[0]
        print(f"📈 總記錄數: {total_count}")
        
        # 檢查殖利率和EPS資料
        cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE dividend_yield > 0")
        yield_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE eps > 0")
        eps_count = cursor.fetchone()[0]
        
        print(f"💰 有殖利率資料: {yield_count} 筆 ({yield_count/total_count*100:.1f}%)")
        print(f"📊 有EPS資料: {eps_count} 筆 ({eps_count/total_count*100:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接測試失敗: {e}")
        return False

def test_stock_price_fetching():
    """測試股價獲取功能"""
    print("\n🧪 測試股價獲取功能...")
    
    try:
        # 測試從資料庫獲取股價
        price_db_path = "D:/Finlab/history/tables/price.db"
        
        if os.path.exists(price_db_path):
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 檢查股價資料庫結構
            cursor.execute("PRAGMA table_info(stock_daily_data)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"📊 股價資料庫欄位: {columns}")
            
            # 測試獲取台積電股價
            cursor.execute("""
                SELECT close FROM stock_daily_data 
                WHERE stock_id = '2330' 
                ORDER BY date DESC 
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            if result:
                print(f"✅ 台積電最近股價: {result[0]}")
            else:
                print("⚠️ 未找到台積電股價資料")
            
            conn.close()
        else:
            print(f"⚠️ 股價資料庫不存在: {price_db_path}")
        
        # 測試即時股價獲取
        print("🌐 測試即時股價獲取...")
        try:
            from core_web_crawler import YahooFinanceCrawler
            
            crawler = YahooFinanceCrawler()
            result = crawler.get_single_stock_price("2330")
            
            if result and 'current_price' in result:
                print(f"✅ 台積電即時股價: {result['current_price']}")
            else:
                print("⚠️ 無法獲取即時股價")
                
        except Exception as e:
            print(f"⚠️ 即時股價獲取失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股價獲取測試失敗: {e}")
        return False

def test_dividend_yield_calculation():
    """測試殖利率計算功能"""
    print("\n🧪 測試殖利率計算功能...")
    
    try:
        # 模擬計算殖利率
        test_cases = [
            {"stock_code": "2330", "cash_dividend": 3.0, "price": 600, "expected_yield": 0.5},
            {"stock_code": "2317", "cash_dividend": 4.5, "price": 100, "expected_yield": 4.5},
            {"stock_code": "0050", "cash_dividend": 2.8, "price": 140, "expected_yield": 2.0},
        ]
        
        for case in test_cases:
            calculated_yield = (case["cash_dividend"] / case["price"]) * 100
            print(f"📊 {case['stock_code']}: 股利 {case['cash_dividend']}, 股價 {case['price']}, 殖利率 {calculated_yield:.2f}%")
            
            # 檢查計算是否合理
            if abs(calculated_yield - case["expected_yield"]) < 1.0:
                print(f"   ✅ 計算結果合理")
            else:
                print(f"   ⚠️ 計算結果可能需要檢查")
        
        return True
        
    except Exception as e:
        print(f"❌ 殖利率計算測試失敗: {e}")
        return False

def test_folder_operations():
    """測試資料夾操作功能"""
    print("\n🧪 測試資料夾操作功能...")
    
    try:
        dividend_folder = "D:/Finlab/history/tables"
        
        if os.path.exists(dividend_folder):
            print(f"✅ 除權息目錄存在: {dividend_folder}")
            
            # 列出目錄內容
            files = os.listdir(dividend_folder)
            db_files = [f for f in files if f.endswith('.db')]
            
            print(f"📁 目錄內容 ({len(files)} 個檔案):")
            for db_file in db_files:
                file_path = os.path.join(dividend_folder, db_file)
                file_size = os.path.getsize(file_path)
                print(f"   📊 {db_file} ({file_size:,} bytes)")
            
            return True
        else:
            print(f"❌ 除權息目錄不存在: {dividend_folder}")
            return False
            
    except Exception as e:
        print(f"❌ 資料夾操作測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 除權息查詢系統增強功能測試")
    print("=" * 50)
    
    tests = [
        ("資料庫連接", test_database_connection),
        ("股價獲取", test_stock_price_fetching),
        ("殖利率計算", test_dividend_yield_calculation),
        ("資料夾操作", test_folder_operations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(results)} 項測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！除權息查詢系統增強功能準備就緒。")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")

if __name__ == "__main__":
    main()
