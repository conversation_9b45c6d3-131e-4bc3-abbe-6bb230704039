#!/usr/bin/env python3
"""
測試新策略的有效性
檢查是否能找到符合條件的股票
"""

import sys
import os
import sqlite3
import pandas as pd
import json
import traceback
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append('.')

def test_strategy_effectiveness():
    """測試策略有效性"""
    print("🚀 開始測試策略有效性...")
    
    # 載入策略配置
    try:
        with open('strategies.json', 'r', encoding='utf-8') as f:
            strategies = json.load(f)
        print(f"✅ 載入 {len(strategies)} 個策略")
    except Exception as e:
        print(f"❌ 載入策略失敗: {e}")
        return
    
    # 連接數據庫
    try:
        db_path = "db/price.db"
        if not os.path.exists(db_path):
            print(f"❌ 數據庫文件不存在: {db_path}")
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 獲取股票列表
        cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data LIMIT 100")
        stock_list = [row[0] for row in cursor.fetchall()]
        print(f"📊 測試股票數量: {len(stock_list)}")
        
        if len(stock_list) == 0:
            print("❌ 沒有找到股票數據")
            return
            
    except Exception as e:
        print(f"❌ 數據庫連接失敗: {e}")
        return
    
    # 測試每個策略
    for strategy_name, conditions in strategies.items():
        print(f"\n🎯 測試策略: {strategy_name}")
        print(f"   條件數量: {len(conditions)}")
        
        matching_count = 0
        error_count = 0
        
        # 測試前10支股票
        test_stocks = stock_list[:10]
        
        for stock_id in test_stocks:
            try:
                # 獲取股票數據
                query = """
                    SELECT date, Open, High, Low, Close, Volume
                    FROM stock_daily_data 
                    WHERE stock_id = ? 
                    ORDER BY date DESC 
                    LIMIT 100
                """
                cursor.execute(query, (stock_id,))
                data = cursor.fetchall()
                
                if len(data) < 20:
                    continue
                
                # 轉換為DataFrame
                df = pd.DataFrame(data, columns=['date', 'Open', 'High', 'Low', 'Close', 'Volume'])
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date').reset_index(drop=True)
                
                # 計算基本指標
                df = calculate_basic_indicators(df)
                
                # 檢查所有條件
                all_matched = True
                condition_results = []
                
                for condition in conditions:
                    matched, message = check_simple_condition(df, condition)
                    condition_results.append((matched, message))
                    if not matched:
                        all_matched = False
                
                if all_matched:
                    matching_count += 1
                    print(f"   ✅ {stock_id}: 所有條件符合")
                    for matched, msg in condition_results:
                        print(f"      {'✓' if matched else '✗'} {msg}")
                
            except Exception as e:
                error_count += 1
                print(f"   ❌ {stock_id}: 處理錯誤 - {str(e)}")
        
        print(f"   📈 結果: {matching_count}/{len(test_stocks)} 股票符合條件 ({matching_count/len(test_stocks)*100:.1f}%)")
        if error_count > 0:
            print(f"   ⚠️  錯誤: {error_count} 支股票處理失敗")
    
    conn.close()
    print("\n🎉 測試完成！")

def calculate_basic_indicators(df):
    """計算基本技術指標"""
    # 移動平均線
    df['MA5'] = df['Close'].rolling(window=5).mean()
    df['MA10'] = df['Close'].rolling(window=10).mean()
    df['MA20'] = df['Close'].rolling(window=20).mean()
    df['MA60'] = df['Close'].rolling(window=60).mean()
    df['MA240'] = df['Close'].rolling(window=240).mean()
    
    # RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI14'] = 100 - (100 / (1 + rs))
    
    # 成交量移動平均
    df['Volume_MA5'] = df['Volume'].rolling(window=5).mean()
    df['Volume_MA10'] = df['Volume'].rolling(window=10).mean()
    
    return df

def check_simple_condition(df, condition):
    """簡化的條件檢查"""
    try:
        condition_type = condition.get("type", "")
        
        if condition_type == "ma_trend":
            ma = condition.get("ma", "MA20")
            trend = condition.get("trend", "up")
            days = condition.get("days", 1)
            
            if ma not in df.columns or len(df) < days + 1:
                return False, f"{ma}數據不足"
            
            current_ma = df.iloc[-1][ma]
            prev_ma = df.iloc[-1-days][ma]
            
            if trend == "up":
                matched = current_ma > prev_ma
                return matched, f"{ma} {'上漲' if matched else '下跌'} ({current_ma:.2f} vs {prev_ma:.2f})"
            else:
                matched = current_ma < prev_ma
                return matched, f"{ma} {'下跌' if matched else '上漲'} ({current_ma:.2f} vs {prev_ma:.2f})"
                
        elif condition_type == "volume_surge":
            min_multiplier = condition.get("min_multiplier", 1.3)
            days = condition.get("days", 2)
            
            if len(df) < days + 5:
                return False, "數據不足"
            
            current_volume = df.iloc[-1]['Volume']
            avg_volume = df.iloc[-days-5:-days]['Volume'].mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            matched = volume_ratio >= min_multiplier
            return matched, f"成交量比例 {volume_ratio:.2f}x ({'≥' if matched else '<'} {min_multiplier}x)"
            
        elif condition_type == "oversold_bounce":
            rsi_threshold = condition.get("rsi_threshold", 35)
            bounce_strength = condition.get("bounce_strength", 3)
            
            if 'RSI14' not in df.columns or len(df) < 20:
                return False, "RSI數據不足"
            
            current_rsi = df.iloc[-1]['RSI14']
            recent_low = df.iloc[-10:]['Low'].min()
            current_price = df.iloc[-1]['Close']
            bounce_percent = ((current_price - recent_low) / recent_low * 100) if recent_low > 0 else 0
            
            rsi_ok = current_rsi <= rsi_threshold
            bounce_ok = bounce_percent >= bounce_strength
            matched = rsi_ok and bounce_ok
            
            return matched, f"RSI {current_rsi:.1f} ({'≤' if rsi_ok else '>'} {rsi_threshold}), 反彈 {bounce_percent:.1f}% ({'≥' if bounce_ok else '<'} {bounce_strength}%)"
            
        elif condition_type == "ma_support":
            ma_period = condition.get("ma_period", 20)
            support_threshold = condition.get("support_threshold", 3)
            
            ma_col = f'MA{ma_period}'
            if ma_col not in df.columns:
                return False, f"{ma_col}不存在"
            
            current_price = df.iloc[-1]['Close']
            ma_value = df.iloc[-1][ma_col]
            distance_percent = abs((current_price - ma_value) / ma_value * 100) if ma_value > 0 else 100
            
            matched = distance_percent <= support_threshold
            return matched, f"距離{ma_period}日均線 {distance_percent:.2f}% ({'≤' if matched else '>'} {support_threshold}%)"
            
        else:
            return True, f"條件 {condition_type} 暫時跳過"
            
    except Exception as e:
        return False, f"檢查錯誤: {str(e)}"

if __name__ == "__main__":
    test_strategy_effectiveness() 