#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的掃描測試
修復 Segmentation fault 和遞迴重繪問題
"""

import sys
import time
import logging
import signal
import os

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def signal_handler(signum, frame):
    """信號處理器，安全退出"""
    print(f"\n收到信號 {signum}，安全退出...")
    sys.exit(0)

# 註冊信號處理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def test_safe_scan():
    """安全的掃描測試"""
    print("安全掃描測試")
    print("=" * 50)
    
    try:
        # 設置環境變數避免Qt問題
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
        os.environ['QT_SCALE_FACTOR'] = '1'
        
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        # 創建應用程式
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        
        print("✅ QApplication 創建成功")
        
        # 導入必要模組
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        print("✅ 模組導入成功")
        
        # 創建監控器
        monitor = PreMarketMonitor()
        print("✅ 監控器創建成功")
        
        # 結果收集
        results = {'success': None, 'error': None, 'progress': []}
        
        def success_callback(scan_results):
            print(f"🎉 掃描成功！")
            results['success'] = scan_results
            if isinstance(scan_results, dict):
                total_items = sum(len(v) for v in scan_results.values() if isinstance(v, dict))
                print(f"   獲取 {total_items} 項數據")
            app.quit()  # 安全退出
        
        def error_callback(error_msg):
            print(f"❌ 掃描失敗: {error_msg}")
            results['error'] = error_msg
            app.quit()  # 安全退出
        
        def progress_callback(value, message):
            print(f"📊 進度: {value}% - {message}")
            results['progress'].append((value, message))
            # 不調用 processEvents 避免遞迴重繪
        
        # 創建 ScanWorker
        print("📊 創建 ScanWorker...")
        scan_worker = ScanWorker(
            monitor,
            callback_success=success_callback,
            callback_error=error_callback,
            progress_callback=progress_callback
        )
        
        print("🚀 啟動掃描...")
        scan_worker.start_scan()
        
        # 設置超時退出
        def timeout_exit():
            print("⏰ 測試超時，退出")
            app.quit()
        
        timeout_timer = QTimer()
        timeout_timer.timeout.connect(timeout_exit)
        timeout_timer.start(30000)  # 30秒超時
        
        print("⏳ 等待掃描完成（最多30秒）...")
        
        # 運行事件循環
        exit_code = app.exec()
        
        # 分析結果
        print(f"\n📊 測試結果分析:")
        print(f"   退出代碼: {exit_code}")
        print(f"   成功結果: {'有' if results['success'] else '無'}")
        print(f"   錯誤信息: {'有' if results['error'] else '無'}")
        print(f"   進度更新: {len(results['progress'])} 次")
        
        if results['success']:
            print("✅ 掃描測試成功")
            return True
        elif results['error']:
            print(f"⚠️ 掃描測試有錯誤: {results['error']}")
            return False
        else:
            print("⚠️ 掃描測試超時或無結果")
            return False
        
    except Exception as e:
        print(f"❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_safety():
    """測試記憶體安全性"""
    print("\n記憶體安全性測試")
    print("=" * 50)
    
    try:
        # 多次創建和銷毀對象測試記憶體洩漏
        for i in range(3):
            print(f"第 {i+1} 次測試...")
            
            from monitoring.pre_market_monitor import PreMarketMonitor
            monitor = PreMarketMonitor()
            
            # 簡單測試
            results = monitor.run_full_scan()
            
            if results:
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"   ✅ 獲取 {total_items} 項數據")
            else:
                print(f"   ❌ 無數據")
            
            # 清理
            del monitor
            del results
            
            time.sleep(1)  # 短暫等待
        
        print("✅ 記憶體安全性測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 記憶體安全性測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("修復驗證測試")
    print("=" * 60)
    print("修復內容:")
    print("  1. 移除遞迴 processEvents 調用")
    print("  2. 改善異常處理")
    print("  3. 修復 PE 數據庫警告")
    print("  4. 添加記憶體安全檢查")
    print()
    
    # 1. 記憶體安全性測試
    memory_safe = test_memory_safety()
    
    if memory_safe:
        # 2. 安全掃描測試
        scan_safe = test_safe_scan()
        
        print(f"\n🎯 最終測試結果:")
        print(f"   記憶體安全: {'✅ 通過' if memory_safe else '❌ 失敗'}")
        print(f"   掃描安全: {'✅ 通過' if scan_safe else '❌ 失敗'}")
        
        if memory_safe and scan_safe:
            print("\n🎉 所有修復驗證通過！")
            print("   • 遞迴重繪問題已解決")
            print("   • 記憶體安全性良好")
            print("   • 掃描功能正常")
            print("   • 不會再出現 Segmentation fault")
        else:
            print("\n⚠️ 部分問題仍需調整")
    else:
        print("\n❌ 記憶體安全性測試失敗，跳過掃描測試")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 測試結束")
