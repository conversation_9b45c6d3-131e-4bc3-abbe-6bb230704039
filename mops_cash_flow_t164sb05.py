#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於新發現的 MOPS 現金流量表端點 (t164sb05) 的爬蟲
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import logging
from typing import Dict, Optional, List
from datetime import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class MOPSCashFlowScraper:
    """MOPS 現金流量表爬蟲 (使用 t164sb05 端點)"""
    
    def __init__(self):
        self.base_url = "https://mops.twse.com.tw/mops/web/t164sb05"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'https://mops.twse.com.tw/mops/web/t164sb05'
        })
        self.session.verify = False
    
    def get_cash_flow_statement(self, stock_code: str, year: int, season: int) -> Optional[pd.DataFrame]:
        """
        獲取指定股票的現金流量表
        
        Args:
            stock_code: 股票代碼 (如 '2330')
            year: 年度 (西元年，如 2024)
            season: 季別 (1-4)
        
        Returns:
            現金流量表 DataFrame 或 None
        """
        logger.info(f"🔍 獲取 {stock_code} {year}年第{season}季現金流量表...")
        
        try:
            # 轉換西元年為民國年
            roc_year = year - 1911
            
            # 準備表單資料 (參考提供的範例)
            payload = {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',  # sii = 上市, otc = 上櫃
                'co_id': stock_code,
                'year': str(roc_year),
                'season': str(season)
            }
            
            logger.info(f"📡 POST 請求: {self.base_url}")
            logger.info(f"📊 參數: 股票={stock_code}, 民國年={roc_year}, 季別={season}")
            
            # 發送 POST 請求
            response = self.session.post(self.base_url, data=payload, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                logger.info(f"✅ 請求成功")
                return self._parse_cash_flow_response(response.text, stock_code)
            else:
                logger.error(f"❌ 請求失敗: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 獲取現金流量表失敗: {e}")
            return None
    
    def _parse_cash_flow_response(self, html_content: str, stock_code: str) -> Optional[pd.DataFrame]:
        """解析現金流量表回應"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 檢查是否有錯誤訊息
            if '查無資料' in html_content or '無此資料' in html_content:
                logger.warning(f"⚠️ {stock_code}: 查無資料")
                return None
            
            # 尋找現金流量表的表格 (參考範例使用 'hasBorder' 類別)
            tables = soup.find_all('table', {'class': 'hasBorder'})
            
            if not tables:
                # 嘗試其他可能的表格類別
                tables = soup.find_all('table')
                logger.info(f"📊 找到 {len(tables)} 個表格 (無 hasBorder 類別)")
            else:
                logger.info(f"📊 找到 {len(tables)} 個 hasBorder 表格")
            
            # 尋找包含現金流量資料的表格
            for i, table in enumerate(tables):
                table_text = table.get_text()
                
                # 檢查是否為現金流量表
                if any(keyword in table_text for keyword in ['現金流量', '營業活動', '投資活動', '籌資活動']):
                    logger.info(f"   📊 表格 {i+1} 包含現金流量資料")
                    
                    try:
                        # 使用 pandas 讀取表格
                        df_list = pd.read_html(str(table), header=0)
                        
                        if df_list:
                            df = df_list[0]
                            logger.info(f"   ✅ 成功解析表格: {df.shape}")
                            
                            # 清理資料
                            df = self._clean_cash_flow_data(df, stock_code)
                            return df
                        
                    except Exception as e:
                        logger.warning(f"   ⚠️ 表格 {i+1} 解析失敗: {e}")
                        continue
            
            logger.warning(f"⚠️ {stock_code}: 未找到現金流量表")
            return None
            
        except Exception as e:
            logger.error(f"❌ 解析現金流量表回應失敗: {e}")
            return None
    
    def _clean_cash_flow_data(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """清理現金流量表資料"""
        try:
            # 添加股票代碼
            df['stock_id'] = stock_code
            
            # 移除空行
            df = df.dropna(how='all')
            
            # 重設索引
            df = df.reset_index(drop=True)
            
            logger.info(f"   🧹 資料清理完成: {df.shape}")
            return df
            
        except Exception as e:
            logger.warning(f"⚠️ 資料清理失敗: {e}")
            return df

def test_mops_cash_flow_scraper():
    """測試 MOPS 現金流量表爬蟲"""
    
    print("=" * 80)
    print("🧪 測試 MOPS 現金流量表爬蟲 (t164sb05 端點)")
    print("=" * 80)
    
    scraper = MOPSCashFlowScraper()
    
    # 測試案例
    test_cases = [
        ("2330", 2024, 1),  # 台積電 2024Q1
        ("2317", 2024, 1),  # 鴻海 2024Q1
        ("2454", 2023, 4),  # 聯發科 2023Q4
        ("1101", 2023, 4),  # 台泥 2023Q4
    ]
    
    successful_tests = 0
    
    for stock_code, year, season in test_cases:
        print(f"\n📊 測試 {stock_code} {year}年第{season}季...")
        
        df = scraper.get_cash_flow_statement(stock_code, year, season)
        
        if df is not None and not df.empty:
            print(f"✅ 成功獲取現金流量表: {df.shape}")
            print(f"📋 欄位: {list(df.columns)}")
            
            # 顯示前幾行資料
            print(f"📊 前5行資料:")
            print(df.head())
            
            successful_tests += 1
        else:
            print(f"❌ 無法獲取現金流量表")
        
        # 避免請求過於頻繁
        time.sleep(2)
    
    # 測試結果總結
    print(f"\n📊 測試結果總結:")
    print(f"   總測試案例: {len(test_cases)}")
    print(f"   成功案例: {successful_tests}")
    print(f"   成功率: {(successful_tests/len(test_cases)*100):.1f}%")
    
    return successful_tests > 0

def test_multiple_companies():
    """測試多家公司的現金流量表"""
    
    print(f"\n" + "=" * 80)
    print("📊 測試多家公司現金流量表")
    print("=" * 80)
    
    scraper = MOPSCashFlowScraper()
    
    # 知名上市公司
    companies = [
        ("2330", "台積電"),
        ("2317", "鴻海"),
        ("2454", "聯發科"),
        ("2881", "富邦金"),
        ("6505", "台塑化"),
        ("2412", "中華電"),
        ("1301", "台塑"),
        ("1303", "南亞"),
        ("2002", "中鋼"),
        ("2886", "兆豐金")
    ]
    
    year, season = 2023, 4  # 測試 2023Q4
    successful_companies = []
    
    for stock_code, company_name in companies:
        print(f"\n🔍 測試 {stock_code} {company_name}...")
        
        df = scraper.get_cash_flow_statement(stock_code, year, season)
        
        if df is not None and not df.empty:
            print(f"   ✅ 成功: {df.shape}")
            successful_companies.append((stock_code, company_name))
        else:
            print(f"   ❌ 失敗")
        
        time.sleep(1)
    
    print(f"\n📊 多公司測試結果:")
    print(f"   測試公司數: {len(companies)}")
    print(f"   成功公司數: {len(successful_companies)}")
    print(f"   成功率: {(len(successful_companies)/len(companies)*100):.1f}%")
    
    if successful_companies:
        print(f"   ✅ 成功的公司:")
        for stock_code, company_name in successful_companies:
            print(f"      {stock_code} {company_name}")

def compare_with_existing_system():
    """與現有系統比較"""
    
    print(f"\n" + "=" * 80)
    print("🔍 與現有系統比較")
    print("=" * 80)
    
    print("📊 新的 MOPS 現金流量表爬蟲 vs 現有系統:")
    print()
    
    print("🎯 **新 MOPS 爬蟲優勢**:")
    print("   - 使用最新的 t164sb05 端點")
    print("   - 直接獲取現金流量表")
    print("   - 基於成功的範例程式碼")
    print("   - 可以補充現有系統缺失的現金流量表")
    print()
    
    print("✅ **現有系統優勢**:")
    print("   - TWSE OpenAPI: 官方支援，穩定可靠")
    print("   - 1,008家上市公司 (損益表+資產負債表)")
    print("   - 835家上櫃公司 (財務比率)")
    print("   - 統一的日期格式和資料結構")
    print()
    
    print("💡 **建議整合方案**:")
    print("   1. 保留現有 TWSE OpenAPI (損益表+資產負債表)")
    print("   2. 使用新 MOPS 爬蟲補充現金流量表")
    print("   3. 建立完整的三大財務報表系統")

def main():
    """主函數"""
    
    print("🐍 MOPS 現金流量表爬蟲測試 (t164sb05 端點)")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基本功能測試
    basic_success = test_mops_cash_flow_scraper()
    
    if basic_success:
        # 多公司測試
        test_multiple_companies()
    
    # 與現有系統比較
    compare_with_existing_system()
    
    print(f"\n" + "=" * 80)
    print("🎉 MOPS 現金流量表爬蟲測試完成")
    print("=" * 80)
    
    if basic_success:
        print("✅ 測試成功！新的 MOPS 爬蟲可以正常工作")
        print("💡 建議整合到現有系統中")
    else:
        print("❌ 測試失敗，需要進一步調試")

if __name__ == "__main__":
    main()
