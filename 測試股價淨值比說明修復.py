#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股價淨值比說明修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pb_ratio_explanation():
    """測試股價淨值比說明修復"""
    print("🧪 測試股價淨值比說明修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試股價淨值比說明修復")
        dialog.setFixedSize(700, 600)
        
        layout = QVBoxLayout(dialog)
        
        # 測試財務指標區塊
        financial_group = QGroupBox("💎 財務指標")
        financial_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        financial_layout = QVBoxLayout(financial_group)

        financial_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 8px;">
            <table style="width: 100%; font-family: 'Microsoft JhengHei'; color: #333333;">
                <tr><td style="padding: 6px; font-weight: bold; width: 50%; color: #2c3e50;">殖利率：</td>
                    <td style="padding: 6px; color: #27ae60; font-weight: bold;">2.69%</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">本益比：</td>
                    <td style="padding: 6px; color: #3498db; font-weight: bold;">1.46</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">股價淨值比：</td>
                    <td style="padding: 6px; color: #9b59b6; font-weight: bold;">1.04</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">每股盈餘 (EPS)：</td>
                    <td style="padding: 6px; color: #e74c3c; font-weight: bold;">8.90 元</td></tr>
            </table>
            <div style="margin: 12px 0; padding: 8px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #3498db;">
                <p style="margin: 2px 0; font-size: 10px; color: #555;">
                    💡 <strong style="color: #2c3e50;">殖利率：</strong><span style="color: #666;">股息收益率，數值越高表示股息回報越好</span><br>
                    💡 <strong style="color: #2c3e50;">本益比：</strong><span style="color: #666;">股價相對盈餘的倍數，可用於評估估值水準</span><br>
                    💡 <strong style="color: #2c3e50;">股價淨值比：</strong><span style="color: #666;">股價相對每股淨值的倍數，反映市場對公司資產的評價</span><br>
                    💡 <strong style="color: #2c3e50;">EPS：</strong><span style="color: #666;">每股盈餘，反映公司獲利能力</span>
                </p>
            </div>
        </div>
        """

        financial_label = QLabel()
        financial_label.setTextFormat(Qt.TextFormat.RichText)
        financial_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        financial_label.setWordWrap(True)
        financial_label.setText(financial_text)
        financial_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px;
                color: #333333;
            }
        """)
        financial_layout.addWidget(financial_label)
        layout.addWidget(financial_group)
        
        # 添加詳細說明
        explanation_group = QGroupBox("📚 股價淨值比詳細說明")
        explanation_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        explanation_layout = QVBoxLayout(explanation_group)

        explanation_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h4 style="color: #2c3e50; margin: 0 0 10px 0;">股價淨值比 (P/B Ratio) 說明：</h4>
            
            <p style="margin: 8px 0; font-size: 12px; color: #34495e;">
                <strong style="color: #e74c3c;">定義：</strong>
                股價淨值比 = 股價 ÷ 每股淨值
            </p>
            
            <p style="margin: 8px 0; font-size: 12px; color: #34495e;">
                <strong style="color: #e74c3c;">意義：</strong>
                反映市場對公司資產的評價，數值越低表示股價相對於公司淨資產越便宜
            </p>
            
            <div style="margin: 12px 0; padding: 10px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #27ae60;">
                <p style="margin: 0; font-size: 11px; color: #2d5a2d;">
                    <strong>📈 投資參考：</strong><br>
                    • P/B < 1：股價低於淨值，可能被低估<br>
                    • P/B = 1：股價等於淨值，合理估值<br>
                    • P/B > 1：股價高於淨值，市場給予溢價<br>
                    • 一般而言，P/B 在 1-3 之間較為合理
                </p>
            </div>
            
            <div style="margin: 12px 0; padding: 10px; background-color: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
                <p style="margin: 0; font-size: 11px; color: #856404;">
                    <strong>⚠️ 注意事項：</strong><br>
                    • 不同行業的合理P/B範圍不同<br>
                    • 需搭配其他指標綜合判斷<br>
                    • 資產品質和獲利能力同樣重要
                </p>
            </div>
        </div>
        """

        explanation_label = QLabel()
        explanation_label.setTextFormat(Qt.TextFormat.RichText)
        explanation_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        explanation_label.setWordWrap(True)
        explanation_label.setText(explanation_text)
        explanation_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        explanation_layout.addWidget(explanation_label)
        layout.addWidget(explanation_group)
        
        # 添加測試說明
        test_info_label = QLabel()
        test_info_label.setText("""
        <div style="padding: 10px; background-color: #e3f2fd; border-radius: 4px; border: 1px solid #bbdefb;">
            <p style="margin: 0; font-size: 11px; color: #0d47a1;">
                <strong>🧪 測試確認項目：</strong><br>
                ✅ 股價淨值比說明已從「股價相對淨值的倍數」<br>
                ✅ 更新為「股價相對每股淨值的倍數，反映市場對公司資產的評價」<br>
                ✅ 所有文字都可以選取和複製<br>
                ✅ 顏色對比度清晰可見
            </p>
        </div>
        """)
        test_info_label.setTextFormat(Qt.TextFormat.RichText)
        test_info_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        test_info_label.setWordWrap(True)
        layout.addWidget(test_info_label)
        
        print("✅ 對話框創建成功")
        print("📋 修復內容：")
        print("  1. 股價淨值比說明更加詳細")
        print("  2. 添加了「反映市場對公司資產的評價」說明")
        print("  3. 文字可選取複製")
        print("  4. 顏色對比度優化")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 股價淨值比說明修復測試")
    print("=" * 50)
    
    result = test_pb_ratio_explanation()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 股價淨值比說明已成功修復")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
