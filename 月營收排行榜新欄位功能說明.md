# 月營收排行榜新欄位功能說明

## 🎯 功能概述

根據用戶需求，在月營收排行榜中新增了3個重要欄位，讓用戶能更好地驗證和理解YoY（年增率）和MoM（月增率）的計算結果。

## 📋 新增欄位

### 1. 西元年月
- **格式**: YYYYMM (例如: 202507)
- **說明**: 顯示資料所屬的年月，方便用戶快速識別資料期間
- **範例**: 2025年7月的資料顯示為 "202507"

### 2. 上個月營收(千元)
- **格式**: 千分位逗號分隔的數字
- **說明**: 顯示上個月的營收數據，便於用戶驗證MoM計算是否正確
- **用途**: 用戶可以手動計算 (當月營收 - 上月營收) / 上月營收 × 100% 來驗證MoM%

### 3. 去年同月營收(千元)
- **格式**: 千分位逗號分隔的數字
- **說明**: 顯示去年同月的營收數據，便於用戶驗證YoY計算是否正確
- **用途**: 用戶可以手動計算 (當月營收 - 去年同月營收) / 去年同月營收 × 100% 來驗證YoY%

## 📊 新的表格結構

### 一般排行榜 (YoY/MoM)
| 排名 | 股票代碼 | 股票名稱 | 西元年月 | 當月營收(千元) | 上個月營收(千元) | 去年同月營收(千元) | YoY% | MoM% |
|------|----------|----------|----------|----------------|------------------|-------------------|------|------|
| 1    | 2330     | 台積電   | 202507   | 120,000,000    | 115,000,000      | 100,000,000       | +20.00% | +4.35% |

### 綜合評分排行榜
| 排名 | 股票代碼 | 股票名稱 | 西元年月 | 當月營收(千元) | 上個月營收(千元) | 去年同月營收(千元) | YoY% | MoM% | 綜合評分 |
|------|----------|----------|----------|----------------|------------------|-------------------|------|------|----------|
| 1    | 2330     | 台積電   | 202507   | 120,000,000    | 115,000,000      | 100,000,000       | +20.00% | +4.35% | 85.5 |

## 🔧 技術實現

### 修改的檔案
- `O3mh_gui_v21_optimized.py`

### 修改的函數
1. **表格設置部分** (第21953-21966行)
   - 增加欄位數量：一般排行榜從6欄增加到9欄，綜合評分排行榜從7欄增加到10欄
   - 更新欄位標題

2. **數據填入部分** (第21976-22039行)
   - 調整各欄位的位置索引
   - 新增西元年月、上個月營收、去年同月營收的數據填入

3. **數據生成部分** (第21548-21571行)
   - 在 `get_monthly_revenue_ranking` 函數中添加新欄位的數據生成
   - 計算西元年月格式 (YYYYMM)
   - 格式化營收數據為千分位逗號分隔

### 關鍵程式碼片段

```python
# 計算西元年月
year_month = selected_date.strftime('%Y%m')

data_item = {
    '排名': idx,
    '股票代碼': row['stock_id'],
    '股票名稱': row['stock_name'],
    '西元年月': year_month,
    '當月營收': f"{row.get('當月營收', 0):,.0f}",
    '上個月營收': f"{row.get('上月營收', 0):,.0f}",
    '去年同月營收': f"{row.get('去年當月營收', 0):,.0f}",
    'YoY%': f"{row.get('YoY%', 0):.2f}%",
    'MoM%': f"{row.get('MoM%', 0):.2f}%",
    '日期': selected_date.strftime('%Y-%m-%d')
}
```

## 🧪 測試結果

### 測試腳本
創建了 `test_new_columns.py` 測試腳本來驗證新功能。

### 測試結果
```
✅ 找到 10 筆測試資料
📊 新的月營收排行榜格式測試:
排名   代碼     名稱         西元年月     當月營收         上月營收         去年同月         YoY%     MoM%
1    1110   東泥         202507      222,403    261,492    192,948   15.3%  -14.9%
2    1108   幸福         202507      361,327    388,571    354,661    1.9%   -7.0%
...
🎉 所有測試通過！月營收排行榜新欄位功能正常。
```

## 🚀 使用方法

1. **啟動程式**: 運行 `python O3mh_gui_v21_optimized.py`
2. **選擇日期**: 在「計算日期」欄位選擇要查詢的日期
3. **選擇排行榜**: 在下拉選單中選擇月營收排行榜類型
4. **執行查詢**: 點擊「執行排行」按鈕
5. **查看結果**: 在右側表格中查看包含新欄位的詳細排行榜

## 💡 用戶價值

### 1. 透明度提升
- 用戶可以直接看到計算YoY和MoM所使用的原始數據
- 提高了數據的可信度和透明度

### 2. 驗證便利性
- 用戶可以手動驗證YoY和MoM的計算是否正確
- 發現異常數據時可以快速定位問題

### 3. 分析深度
- 西元年月欄位讓用戶快速識別資料期間
- 三個營收數據的對比讓用戶更好理解公司營收趨勢

## ✅ 功能狀態

- ✅ 新欄位已成功添加
- ✅ 表格顯示正常
- ✅ 數據格式正確
- ✅ 測試通過
- ✅ 功能可正常使用

## 📝 注意事項

1. **數據來源**: 新欄位的數據來自現有的資料庫，不需要額外的數據獲取
2. **格式一致**: 所有營收數據都使用千分位逗號分隔格式
3. **相容性**: 修改不影響其他功能的正常運作
4. **效能**: 新欄位不會顯著影響查詢和顯示效能
