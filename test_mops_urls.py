#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 MOPS 網站 URL 的可用性
檢查月營收資料的 URL 格式是否正確
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3
import requests

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_mops_url_format():
    """測試 MOPS URL 格式"""
    print("🔍 測試 MOPS 月營收 URL 格式...")
    
    # 測試最近幾個月的 URL
    test_dates = [
        datetime(2023, 4, 10),  # 2023年4月
        datetime(2023, 5, 10),  # 2023年5月
        datetime(2023, 6, 10),  # 2023年6月
        datetime(2023, 7, 10),  # 2023年7月
    ]
    
    for date in test_dates:
        print(f"\n📅 測試日期: {date.strftime('%Y-%m-%d')}")
        
        # 計算民國年和月份
        year = date.year - 1911
        month = (date.month + 10) % 12 + 1
        if month == 12:
            year -= 1
        
        print(f"   民國年: {year}, 月份: {month}")
        
        # 測試兩種市場
        for market in ['sii', 'otc']:
            url = f'https://mops.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}.html'
            print(f"   {market.upper()}: {url}")
            
            try:
                response = requests.get(url, verify=False, timeout=10)
                print(f"      狀態碼: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"      ✅ 可訪問")
                    # 檢查內容長度
                    content_length = len(response.text)
                    print(f"      內容長度: {content_length} 字元")
                    
                    # 檢查是否包含營收資料的關鍵字
                    if '營收' in response.text or '當月營收' in response.text:
                        print(f"      ✅ 包含營收資料")
                    else:
                        print(f"      ⚠️ 可能不包含營收資料")
                        
                elif response.status_code == 404:
                    print(f"      ❌ 頁面不存在 (404)")
                else:
                    print(f"      ⚠️ 其他狀態碼: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"      ❌ 請求超時")
            except requests.exceptions.RequestException as e:
                print(f"      ❌ 請求錯誤: {str(e)[:50]}...")

def test_alternative_urls():
    """測試替代的 URL 格式"""
    print("\n🔍 測試替代的 URL 格式...")
    
    # 測試不同的 URL 格式
    date = datetime(2023, 6, 10)
    year = date.year - 1911
    month = (date.month + 10) % 12 + 1
    if month == 12:
        year -= 1
    
    alternative_formats = [
        f'https://mops.twse.com.tw/nas/t21/sii/t21sc03_{year}_{month:02d}.html',  # 月份補零
        f'https://mops.twse.com.tw/server-java/t164sb01?step=1&CO_ID=&SYEAR={year}&SMONTH={month}&EMONTH={month}&keyword4=&code1=&TYPEK2=&checkbtn=&queryName=co_id&inpuType=co_id&TYPEK=all&isnew=false',  # 查詢格式
        f'https://mops.twse.com.tw/nas/t21/sii/t21sc03_{year-1911}_{month}.html',  # 西元年
    ]
    
    for i, url in enumerate(alternative_formats, 1):
        print(f"\n{i}. 測試格式 {i}:")
        print(f"   URL: {url[:80]}...")
        
        try:
            response = requests.get(url, verify=False, timeout=10)
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 可訪問")
            else:
                print(f"   ❌ 不可訪問")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {str(e)[:50]}...")

def check_mops_main_site():
    """檢查 MOPS 主站是否正常"""
    print("\n🔍 檢查 MOPS 主站狀態...")
    
    main_urls = [
        'https://mops.twse.com.tw/',
        'https://mops.twse.com.tw/mops/web/index',
    ]
    
    for url in main_urls:
        print(f"\n📡 測試: {url}")
        try:
            response = requests.get(url, verify=False, timeout=10)
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 主站正常")
            else:
                print(f"   ⚠️ 主站可能有問題")
                
        except Exception as e:
            print(f"   ❌ 主站訪問失敗: {str(e)[:50]}...")

def main():
    """主函數"""
    print("🔧 MOPS 網站 URL 測試工具")
    print("=" * 60)
    print("🎯 目標: 檢查月營收資料 URL 的可用性")
    print("=" * 60)
    
    # 1. 檢查主站
    check_mops_main_site()
    
    # 2. 測試標準格式
    test_mops_url_format()
    
    # 3. 測試替代格式
    test_alternative_urls()
    
    print("\n" + "=" * 60)
    print("📊 測試完成")
    print("=" * 60)
    print("💡 建議:")
    print("   • 如果大部分 URL 都返回 404，可能是 MOPS 更改了格式")
    print("   • 如果主站無法訪問，可能是網路問題")
    print("   • 可以手動訪問 MOPS 網站確認資料是否存在")

if __name__ == "__main__":
    main()
