#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
月營收資料下載器GUI
基於ROE下載器GUI的設計，專門用於下載GoodInfo月營收資料
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
from datetime import datetime
import sqlite3
import pandas as pd
from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader

class MonthlyRevenueDownloaderGUI:
    def __init__(self, parent=None):
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()
        
        self.setup_window()
        self.create_widgets()
        self.downloader = GoodinfoMonthlyRevenueDownloader()
        
    def setup_window(self):
        """設置視窗"""
        self.window.title("📊 月營收資料下載器")
        self.window.geometry("900x750")
        self.window.resizable(True, True)
        
        # 設置圖標
        try:
            self.window.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """創建界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 GoodInfo 月營收資料下載器", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 說明文字
        info_text = """
此工具可以從 GoodInfo 網站自動下載股票月營收資料
支援單一股票或全股票下載，資料將自動儲存到本地數據庫
        """
        info_label = ttk.Label(main_frame, text=info_text.strip(), 
                              font=('Arial', 10), foreground='gray')
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 下載模式選擇區域
        mode_frame = ttk.LabelFrame(main_frame, text="下載模式設定", padding="10")
        mode_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        mode_frame.columnconfigure(1, weight=1)

        # 下載模式選擇
        self.download_mode = tk.StringVar(value="all_stocks")

        # 全股票模式
        all_stocks_radio = ttk.Radiobutton(mode_frame, text="📊 全股票營收資料 (推薦)",
                                         variable=self.download_mode, value="all_stocks",
                                         command=self.on_mode_change)
        all_stocks_radio.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))

        # 全股票設定區域
        self.all_stocks_frame = ttk.LabelFrame(mode_frame, text="全股票下載設定", padding="10")
        self.all_stocks_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=(20, 0), pady=(0, 10))
        self.all_stocks_frame.columnconfigure(1, weight=1)

        # 說明文字
        all_stocks_info = ttk.Label(self.all_stocks_frame, text="下載全部1754支上市上櫃股票的歷史營收資料",
                                   font=('Arial', 9), foreground='gray')
        all_stocks_info.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))

        # 全股票日期範圍設定
        self.all_use_date_range_var = tk.BooleanVar(value=False)
        all_date_range_check = ttk.Checkbutton(self.all_stocks_frame, text="📅 指定日期範圍",
                                             variable=self.all_use_date_range_var,
                                             command=self.toggle_all_date_range)
        all_date_range_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))

        # 全股票日期輸入框
        self.all_date_frame = ttk.Frame(self.all_stocks_frame)
        self.all_date_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(self.all_date_frame, text="開始月份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.all_start_date_var = tk.StringVar(value="2022-01")
        self.all_start_date_entry = ttk.Entry(self.all_date_frame, textvariable=self.all_start_date_var, width=10, state='disabled')
        self.all_start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(self.all_date_frame, text="結束月份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.all_end_date_var = tk.StringVar(value="2025-07")
        self.all_end_date_entry = ttk.Entry(self.all_date_frame, textvariable=self.all_end_date_var, width=10, state='disabled')
        self.all_end_date_entry.grid(row=0, column=3, sticky=tk.W)

        ttk.Label(self.all_date_frame, text="(格式: YYYY-MM，如: 2022-01)",
                 font=('Arial', 8), foreground='gray').grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(2, 0))

        # 單一股票模式
        single_stock_radio = ttk.Radiobutton(mode_frame, text="🎯 單一股票歷史資料",
                                           variable=self.download_mode, value="single_stock",
                                           command=self.on_mode_change)
        single_stock_radio.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))

        # 單一股票輸入框
        self.single_stock_frame = ttk.LabelFrame(mode_frame, text="單一股票設定", padding="10")
        self.single_stock_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=(20, 0), pady=(0, 10))
        self.single_stock_frame.columnconfigure(1, weight=1)

        # 股票代號輸入
        stock_input_frame = ttk.Frame(self.single_stock_frame)
        stock_input_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(stock_input_frame, text="股票代號:").pack(side=tk.LEFT, padx=(0, 10))
        self.single_stock_var = tk.StringVar(value="2330")
        single_stock_entry = ttk.Entry(stock_input_frame, textvariable=self.single_stock_var, width=10)
        single_stock_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(stock_input_frame, text="(例如: 2330, 2317, 2454)",
                 font=('Arial', 9), foreground='gray').pack(side=tk.LEFT)

        # 日期範圍設定
        self.use_date_range_var = tk.BooleanVar(value=False)
        date_range_check = ttk.Checkbutton(self.single_stock_frame, text="📅 指定日期範圍",
                                         variable=self.use_date_range_var,
                                         command=self.toggle_date_range)
        date_range_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))

        # 日期輸入框
        self.date_frame = ttk.Frame(self.single_stock_frame)
        self.date_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(self.date_frame, text="開始月份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.start_date_var = tk.StringVar(value="2022-01")
        self.start_date_entry = ttk.Entry(self.date_frame, textvariable=self.start_date_var, width=10, state='disabled')
        self.start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(self.date_frame, text="結束月份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.end_date_var = tk.StringVar(value="2025-07")
        self.end_date_entry = ttk.Entry(self.date_frame, textvariable=self.end_date_var, width=10, state='disabled')
        self.end_date_entry.grid(row=0, column=3, sticky=tk.W)

        ttk.Label(self.date_frame, text="(格式: YYYY-MM，如: 2022-01)",
                 font=('Arial', 8), foreground='gray').grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(2, 0))

        # 下載按鈕區域
        download_btn_frame = ttk.Frame(mode_frame)
        download_btn_frame.grid(row=4, column=0, columnspan=3, pady=(15, 0))

        # 主要下載按鈕
        self.download_btn = ttk.Button(download_btn_frame, text="🚀 開始下載",
                                     command=self.start_download, style="Accent.TButton")
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 更新至最新月份按鈕
        self.update_latest_btn = ttk.Button(download_btn_frame, text="🔄 更新至最新月份",
                                          command=self.update_to_latest_month)
        self.update_latest_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 快速更新說明
        update_info = ttk.Label(download_btn_frame, text="(快速更新當月最新資料)",
                               font=('Arial', 8), foreground='gray')
        update_info.pack(side=tk.LEFT, padx=(5, 0))

        # 初始化界面狀態
        self.on_mode_change()
        
        # 進度顯示區域
        progress_frame = ttk.LabelFrame(main_frame, text="下載進度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 狀態標籤
        self.status_var = tk.StringVar(value="準備就緒")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日誌顯示區域
        log_frame = ttk.LabelFrame(main_frame, text="執行日誌", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 日誌文字框
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 自動更新設定區域
        auto_update_frame = ttk.LabelFrame(main_frame, text="🔄 自動更新設定", padding="10")
        auto_update_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 自動更新選項
        self.auto_update_var = tk.BooleanVar(value=False)
        auto_update_check = ttk.Checkbutton(auto_update_frame,
                                          text="啟用每日自動更新 (每天早上9點自動更新最新營收資料)",
                                          variable=self.auto_update_var,
                                          command=self.toggle_auto_update)
        auto_update_check.pack(side=tk.LEFT)

        # 最後更新時間顯示
        self.last_update_var = tk.StringVar(value="尚未更新")
        last_update_label = ttk.Label(auto_update_frame, text="最後更新:")
        last_update_label.pack(side=tk.LEFT, padx=(20, 5))

        last_update_time = ttk.Label(auto_update_frame, textvariable=self.last_update_var,
                                   font=('Arial', 9), foreground='blue')
        last_update_time.pack(side=tk.LEFT)

        # 控制按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))

        ttk.Button(button_frame, text="清除日誌",
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="查看數據庫",
                  command=self.view_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="匯出CSV",
                  command=self.export_csv).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="查看全股票資料",
                  command=self.view_all_stocks_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="檢查更新狀態",
                  command=self.check_update_status).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="關閉",
                  command=self.window.destroy).pack(side=tk.RIGHT)
    
    def toggle_date_range(self):
        """切換單一股票日期範圍輸入框的啟用狀態"""
        if self.use_date_range_var.get():
            self.start_date_entry.configure(state='normal')
            self.end_date_entry.configure(state='normal')
        else:
            self.start_date_entry.configure(state='disabled')
            self.end_date_entry.configure(state='disabled')

    def toggle_all_date_range(self):
        """切換全股票日期範圍輸入框的啟用狀態"""
        if self.all_use_date_range_var.get():
            self.all_start_date_entry.configure(state='normal')
            self.all_end_date_entry.configure(state='normal')
        else:
            self.all_start_date_entry.configure(state='disabled')
            self.all_end_date_entry.configure(state='disabled')

    def on_mode_change(self):
        """處理下載模式變更"""
        mode = self.download_mode.get()

        # 根據模式顯示/隱藏相關控件
        if mode == "all_stocks":
            # 啟用全股票設定，禁用單一股票設定
            self._set_frame_state(self.all_stocks_frame, 'normal')
            self._set_frame_state(self.single_stock_frame, 'disabled')
            self.download_btn.configure(text="🚀 下載全股票營收資料")
            # 重新檢查全股票日期範圍設定
            self.toggle_all_date_range()

        elif mode == "single_stock":
            # 啟用單一股票設定，禁用全股票設定
            self._set_frame_state(self.all_stocks_frame, 'disabled')
            self._set_frame_state(self.single_stock_frame, 'normal')
            self.download_btn.configure(text="🎯 下載單一股票資料")
            # 重新檢查單一股票日期範圍設定
            self.toggle_date_range()

    def _set_frame_state(self, frame, state):
        """安全地設置框架內控件的狀態"""
        for widget in frame.winfo_children():
            try:
                # 只對支援state選項的控件設置狀態
                if hasattr(widget, 'configure') and 'state' in widget.configure():
                    widget.configure(state=state)
            except:
                # 忽略不支援state選項的控件
                pass

    def start_download(self):
        """根據選擇的模式開始下載"""
        mode = self.download_mode.get()

        if mode == "all_stocks":
            self.download_all_stocks()
        elif mode == "single_stock":
            self.download_single_stock()

    def update_to_latest_month(self):
        """更新至最新月份"""
        if not messagebox.askyesno("確認更新",
                                 "即將更新全股票至最新月份營收資料\n\n"
                                 "這將下載當月最新的營收數據\n"
                                 "並更新數據庫中的資料\n\n"
                                 "是否繼續？"):
            return

        # 在新線程中執行更新
        thread = threading.Thread(target=self._update_latest_thread, daemon=True)
        thread.start()

    def _update_latest_thread(self):
        """更新至最新月份的線程"""
        try:
            self.log_message("🔄 開始更新至最新月份...")
            self.update_progress(0, "正在獲取最新營收資料...")

            # 禁用按鈕
            self.download_btn.configure(state='disabled')
            self.update_latest_btn.configure(state='disabled')

            # 使用全股票下載功能獲取最新資料
            result = self.downloader.download_stock_revenue('2330')  # 觸發全股票下載

            if result:
                self.log_message("✅ 最新資料下載完成")
                self.update_progress(50, "正在處理最新資料...")

                # 處理全股票Excel文件
                if self.process_all_stocks_excel():
                    self.update_progress(100, "更新完成")
                    self.log_message("🎉 全股票資料已更新至最新月份")

                    # 顯示更新統計
                    self.show_update_statistics()

                    messagebox.showinfo("更新完成",
                                      "全股票營收資料已成功更新至最新月份！\n\n"
                                      "請點擊「查看全股票資料」查看最新結果")
                else:
                    self.log_message("❌ 處理最新資料失敗")
                    self.update_progress(0, "更新失敗")
                    messagebox.showerror("失敗", "處理最新資料時發生錯誤")
            else:
                self.log_message("❌ 獲取最新資料失敗")
                self.update_progress(0, "更新失敗")
                messagebox.showerror("失敗", "無法獲取最新營收資料")

        except Exception as e:
            self.log_message(f"❌ 更新至最新月份時發生錯誤: {e}")
            self.update_progress(0, "更新失敗")
            messagebox.showerror("錯誤", f"更新過程中發生錯誤：\n{str(e)}")

        finally:
            # 重新啟用按鈕
            self.download_btn.configure(state='normal')
            self.update_latest_btn.configure(state='normal')

    def show_update_statistics(self):
        """顯示更新統計資訊"""
        try:
            import sqlite3

            all_stocks_db = os.path.join(self.downloader.download_dir, "all_stocks_revenue.db")
            if not os.path.exists(all_stocks_db):
                return

            conn = sqlite3.connect(all_stocks_db)
            cursor = conn.cursor()

            # 獲取統計資訊
            cursor.execute("SELECT COUNT(*) FROM all_stocks_revenue")
            total_stocks = cursor.fetchone()[0]

            cursor.execute("SELECT data_month FROM all_stocks_revenue LIMIT 1")
            result = cursor.fetchone()
            data_month = result[0] if result else "未知"

            # 獲取成長率統計
            cursor.execute("""
                SELECT
                    COUNT(CASE WHEN yoy_change_pct > 0 THEN 1 END) as positive_growth,
                    COUNT(CASE WHEN yoy_change_pct < 0 THEN 1 END) as negative_growth,
                    AVG(yoy_change_pct) as avg_growth
                FROM all_stocks_revenue
                WHERE yoy_change_pct IS NOT NULL
            """)

            growth_stats = cursor.fetchone()
            conn.close()

            if growth_stats:
                positive, negative, avg_growth = growth_stats

                self.log_message(f"📊 更新統計 - 資料月份: {data_month}")
                self.log_message(f"📈 總股票數: {total_stocks} 支")
                self.log_message(f"📊 正成長: {positive} 支, 負成長: {negative} 支")
                self.log_message(f"📊 平均年增率: {avg_growth:.1f}%" if avg_growth else "📊 平均年增率: N/A")

        except Exception as e:
            self.log_message(f"⚠️ 統計資訊獲取失敗: {e}")

    def download_all_stocks(self):
        """下載全股票營收資料"""
        # 檢查日期範圍設定
        start_date = None
        end_date = None
        date_info = ""

        if self.all_use_date_range_var.get():
            start_date = self.all_start_date_var.get().strip()
            end_date = self.all_end_date_var.get().strip()

            # 驗證日期格式
            if not self.validate_date_format(start_date) or not self.validate_date_format(end_date):
                messagebox.showerror("錯誤", "日期格式錯誤！\n請使用 YYYY-MM 格式，如: 2022-01")
                return

            date_info = f"\n日期範圍: {start_date} 到 {end_date}"

        if not messagebox.askyesno("確認", f"即將下載全部1754支股票的營收資料{date_info}\n\n這將獲得完整的市場營收數據\n\n是否繼續？"):
            return

        # 在新線程中執行下載
        thread = threading.Thread(target=self._download_all_stocks_thread,
                                args=(start_date, end_date), daemon=True)
        thread.start()

    def _download_all_stocks_thread(self, start_date=None, end_date=None):
        """全股票下載線程"""
        try:
            date_info = f" ({start_date} 到 {end_date})" if start_date and end_date else ""
            self.log_message(f"🚀 開始下載全股票營收資料{date_info}...")
            self.update_progress(0, "正在下載全股票資料...")

            # 禁用下載按鈕
            self.download_btn.configure(state='disabled')

            # 使用任意股票代號觸發全股票下載（基於我們的發現）
            result = self.downloader.download_stock_revenue('2330', start_date, end_date)

            if result:
                self.log_message(f"✅ 下載完成，獲得數據")

                # 處理全股票Excel文件
                self.process_all_stocks_excel()

                self.update_progress(100, "全股票資料下載完成")
                messagebox.showinfo("成功", "全股票營收資料下載完成！\n\n請點擊「查看全股票資料」查看結果")
            else:
                self.log_message("❌ 全股票資料下載失敗")
                self.update_progress(0, "下載失敗")
                messagebox.showerror("失敗", "全股票營收資料下載失敗")

        except Exception as e:
            self.log_message(f"❌ 下載全股票資料時發生錯誤: {e}")
            self.update_progress(0, "下載失敗")
            messagebox.showerror("錯誤", f"下載過程中發生錯誤：\n{str(e)}")
        finally:
            # 重新啟用下載按鈕
            self.download_btn.configure(state='normal')

    def process_all_stocks_excel(self):
        """處理全股票Excel文件"""
        try:
            self.log_message("📊 處理全股票Excel文件...")

            # 導入全股票處理器
            from all_stocks_revenue_processor import AllStocksRevenueProcessor

            processor = AllStocksRevenueProcessor()

            # 尋找最新下載的Excel文件
            import glob
            excel_files = glob.glob(os.path.join(self.downloader.download_dir, "*.xls*"))

            if excel_files:
                latest_file = max(excel_files, key=os.path.getctime)
                self.log_message(f"📋 處理文件: {os.path.basename(latest_file)}")

                # 處理Excel文件
                revenue_data = processor.process_excel_file(latest_file)

                if revenue_data:
                    self.log_message(f"✅ 成功解析 {len(revenue_data)} 支股票資料")

                    # 儲存到數據庫
                    if processor.save_to_database(revenue_data):
                        self.log_message("✅ 全股票資料已儲存到數據庫")

                        # 簡單分析
                        self.log_message("📊 數據分析...")
                        processor.analyze_revenue_data()

                        return True
                else:
                    self.log_message("❌ Excel文件解析失敗")
            else:
                self.log_message("❌ 未找到下載的Excel文件")

        except Exception as e:
            self.log_message(f"❌ 處理全股票Excel失敗: {e}")

        return False


    
    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.window.update_idletasks()
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
    
    def update_progress(self, value, status=""):
        """更新進度"""
        self.progress_var.set(value)
        if status:
            self.status_var.set(status)
        self.window.update_idletasks()
    
    def download_single_stock(self):
        """下載單一股票"""
        stock_id = self.single_stock_var.get().strip()
        if not stock_id:
            messagebox.showwarning("警告", "請輸入股票代號")
            return

        # 檢查日期範圍設定
        start_date = None
        end_date = None
        date_info = ""

        if self.use_date_range_var.get():
            start_date = self.start_date_var.get().strip()
            end_date = self.end_date_var.get().strip()

            # 驗證日期格式
            if not self.validate_date_format(start_date) or not self.validate_date_format(end_date):
                messagebox.showerror("錯誤", "日期格式錯誤！\n請使用 YYYY-MM 格式，如: 2022-01")
                return

            date_info = f" ({start_date} 到 {end_date})"

        # 確認下載
        if not messagebox.askyesno("確認下載",
                                 f"即將下載 {stock_id} 的月營收資料{date_info}\n\n"
                                 f"這將獲取該股票的歷史營收數據\n"
                                 f"並儲存到數據庫中\n\n"
                                 f"是否繼續？"):
            return

        # 在新線程中執行下載
        thread = threading.Thread(target=self._download_single_stock_thread,
                                args=(stock_id, start_date, end_date), daemon=True)
        thread.start()

    def validate_date_format(self, date_str):
        """驗證日期格式 YYYY-MM"""
        try:
            if len(date_str) != 7 or date_str[4] != '-':
                return False
            year = int(date_str[:4])
            month = int(date_str[5:7])
            return 1900 <= year <= 2100 and 1 <= month <= 12
        except:
            return False
    
    def _download_single_stock_thread(self, stock_id, start_date=None, end_date=None):
        """單一股票下載線程"""
        try:
            date_info = f" ({start_date} 到 {end_date})" if start_date and end_date else ""
            self.log_message(f"🚀 開始下載 {stock_id} 月營收資料{date_info}...")
            self.update_progress(0, f"正在下載 {stock_id}...")

            # 禁用下載按鈕
            self.download_btn.configure(state='disabled')

            # 執行下載（支援日期範圍）
            result_file = self.downloader.download_stock_revenue(stock_id, start_date, end_date)

            if result_file:
                self.log_message(f"✅ {stock_id} 下載完成")
                self.log_message(f"📄 檔案位置: {result_file}")
                self.update_progress(100, f"{stock_id} 下載完成")

                # 嘗試讀取Excel檔案獲取記錄數量
                try:
                    import pandas as pd
                    df = pd.read_excel(result_file)
                    record_count = len(df)
                    messagebox.showinfo("成功", f"{stock_id} 月營收資料下載完成{date_info}\n"
                                              f"檔案: {os.path.basename(result_file)}\n"
                                              f"獲得 {record_count} 筆數據")
                except Exception as e:
                    self.log_message(f"⚠️ 無法讀取Excel檔案統計數據: {e}")
                    messagebox.showinfo("成功", f"{stock_id} 月營收資料下載完成{date_info}\n"
                                              f"檔案: {os.path.basename(result_file)}")
            else:
                self.log_message(f"❌ {stock_id} 下載失敗")
                self.update_progress(0, f"{stock_id} 下載失敗")
                messagebox.showerror("失敗", f"{stock_id} 月營收資料下載失敗")
                
        except Exception as e:
            self.log_message(f"❌ 下載 {stock_id} 時發生錯誤: {e}")
            self.update_progress(0, "下載失敗")
            messagebox.showerror("錯誤", f"下載過程中發生錯誤：\n{str(e)}")
        finally:
            # 重新啟用下載按鈕
            self.download_btn.configure(state='normal')
    

    
    def view_database(self):
        """查看數據庫內容"""
        try:
            conn = sqlite3.connect(self.downloader.db_path)
            
            # 查詢統計資訊
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM monthly_revenue")
            total_stocks = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT stock_id, stock_name, COUNT(*) as record_count, 
                       MAX(year || '-' || printf('%02d', month)) as latest_month
                FROM monthly_revenue 
                GROUP BY stock_id, stock_name 
                ORDER BY stock_id
            """)
            stock_summary = cursor.fetchall()
            
            conn.close()
            
            # 顯示統計資訊
            summary_text = f"數據庫統計資訊：\n\n"
            summary_text += f"總記錄數: {total_records}\n"
            summary_text += f"股票數量: {total_stocks}\n\n"
            summary_text += "各股票資料概況：\n"
            summary_text += "-" * 50 + "\n"
            
            for stock_id, stock_name, count, latest in stock_summary:
                summary_text += f"{stock_id} {stock_name}: {count} 筆 (最新: {latest})\n"
            
            # 創建新視窗顯示
            info_window = tk.Toplevel(self.window)
            info_window.title("數據庫內容")
            info_window.geometry("500x400")
            
            text_widget = tk.Text(info_window, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(info_window, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(tk.END, summary_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("錯誤", f"查看數據庫失敗：\n{str(e)}")
    
    def export_csv(self):
        """匯出CSV文件"""
        try:
            # 選擇儲存位置
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="匯出月營收資料"
            )
            
            if not filename:
                return
            
            # 從數據庫讀取資料
            conn = sqlite3.connect(self.downloader.db_path)
            df = pd.read_sql_query("""
                SELECT stock_id, stock_name, year, month, revenue, revenue_mom, 
                       revenue_yoy, cumulative_revenue, cumulative_yoy,
                       open_price, close_price, high_price, low_price,
                       price_change, price_change_pct, crawl_date
                FROM monthly_revenue 
                ORDER BY stock_id, year DESC, month DESC
            """, conn)
            conn.close()
            
            # 匯出CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            self.log_message(f"✅ 資料已匯出到: {filename}")
            messagebox.showinfo("成功", f"資料已成功匯出到：\n{filename}")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"匯出CSV失敗：\n{str(e)}")

    def view_all_stocks_data(self):
        """查看全股票資料"""
        try:
            # 檢查全股票數據庫是否存在
            all_stocks_db = os.path.join(self.downloader.download_dir, "all_stocks_revenue.db")

            if not os.path.exists(all_stocks_db):
                messagebox.showwarning("提示", "尚未下載全股票資料\n\n請先選擇「全股票營收資料」模式並下載")
                return

            # 創建新窗口顯示全股票資料
            self.show_all_stocks_window()

        except Exception as e:
            self.log_message(f"❌ 查看全股票資料失敗: {e}")
            messagebox.showerror("錯誤", f"查看全股票資料時發生錯誤：\n{str(e)}")

    def show_all_stocks_window(self):
        """顯示全股票資料窗口"""
        try:
            import sqlite3

            # 創建新窗口
            stocks_window = tk.Toplevel(self.window)
            stocks_window.title("全股票營收資料")
            stocks_window.geometry("1000x600")

            # 創建主框架
            main_frame = ttk.Frame(stocks_window, padding="10")
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            stocks_window.columnconfigure(0, weight=1)
            stocks_window.rowconfigure(0, weight=1)
            main_frame.columnconfigure(0, weight=1)
            main_frame.rowconfigure(1, weight=1)

            # 標題
            title_label = ttk.Label(main_frame, text="📊 全股票營收資料", font=('Arial', 14, 'bold'))
            title_label.grid(row=0, column=0, pady=(0, 10))

            # 創建Treeview顯示數據
            columns = ('股票代號', '股票名稱', '當月營收(萬)', '年增率(%)', '市場別')
            tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=20)

            # 設置列標題
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120, anchor='center')

            # 添加滾動條
            scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            # 佈局
            tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

            # 從數據庫讀取數據
            all_stocks_db = os.path.join(self.downloader.download_dir, "all_stocks_revenue.db")
            conn = sqlite3.connect(all_stocks_db)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT stock_id, stock_name, current_month_revenue, yoy_change_pct, market_type
                FROM all_stocks_revenue
                ORDER BY current_month_revenue DESC
            """)

            rows = cursor.fetchall()

            # 插入數據到Treeview
            for row in rows:
                stock_id, stock_name, revenue, yoy_change, market_type = row

                # 格式化數據
                revenue_str = f"{revenue/1000:.0f}" if revenue else "N/A"
                yoy_str = f"{yoy_change:+.1f}" if yoy_change else "N/A"

                tree.insert('', tk.END, values=(
                    stock_id,
                    stock_name or "",
                    revenue_str,
                    yoy_str,
                    market_type or ""
                ))

            conn.close()

            # 統計信息
            stats_frame = ttk.Frame(main_frame)
            stats_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))

            ttk.Label(stats_frame, text=f"總計: {len(rows)} 支股票",
                     font=('Arial', 10, 'bold')).pack(side=tk.LEFT, padx=(0, 20))

            # 關閉按鈕
            ttk.Button(stats_frame, text="關閉",
                      command=stocks_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("錯誤", f"顯示全股票資料時發生錯誤：\n{str(e)}")

    def toggle_auto_update(self):
        """切換自動更新設定"""
        if self.auto_update_var.get():
            self.log_message("🔄 已啟用每日自動更新")
            self.log_message("💡 系統將在每天早上9點自動更新最新營收資料")
            # 這裡可以添加定時任務設定
            messagebox.showinfo("自動更新", "自動更新功能已啟用\n\n系統將在每天早上9點自動更新最新營收資料")
        else:
            self.log_message("⏹️ 已停用每日自動更新")

    def check_update_status(self):
        """檢查更新狀態"""
        try:
            import sqlite3

            # 檢查全股票數據庫
            all_stocks_db = os.path.join(self.downloader.download_dir, "all_stocks_revenue.db")

            if not os.path.exists(all_stocks_db):
                messagebox.showinfo("更新狀態", "尚未下載全股票資料\n\n請先下載全股票營收資料")
                return

            conn = sqlite3.connect(all_stocks_db)
            cursor = conn.cursor()

            # 獲取最新資料資訊
            cursor.execute("""
                SELECT data_month, crawl_date, COUNT(*) as stock_count
                FROM all_stocks_revenue
                GROUP BY data_month, crawl_date
                ORDER BY crawl_date DESC
                LIMIT 1
            """)

            result = cursor.fetchone()

            if result:
                data_month, crawl_date, stock_count = result

                # 更新最後更新時間顯示
                self.last_update_var.set(crawl_date)

                # 檢查是否需要更新
                from datetime import datetime, timedelta

                try:
                    last_update = datetime.strptime(crawl_date, '%Y-%m-%d %H:%M:%S')
                    now = datetime.now()
                    days_since_update = (now - last_update).days

                    if days_since_update == 0:
                        status_msg = f"✅ 資料是最新的\n\n"
                    elif days_since_update == 1:
                        status_msg = f"⚠️ 資料已1天未更新\n\n"
                    else:
                        status_msg = f"⚠️ 資料已{days_since_update}天未更新\n\n"

                    status_msg += f"資料月份: {data_month}\n"
                    status_msg += f"股票數量: {stock_count} 支\n"
                    status_msg += f"最後更新: {crawl_date}"

                    if days_since_update > 0:
                        status_msg += f"\n\n建議點擊「更新至最新月份」獲取最新資料"

                    messagebox.showinfo("更新狀態", status_msg)

                except ValueError:
                    messagebox.showinfo("更新狀態", f"資料月份: {data_month}\n股票數量: {stock_count} 支\n最後更新: {crawl_date}")
            else:
                messagebox.showinfo("更新狀態", "數據庫中沒有資料\n\n請先下載全股票營收資料")

            conn.close()

        except Exception as e:
            self.log_message(f"❌ 檢查更新狀態失敗: {e}")
            messagebox.showerror("錯誤", f"檢查更新狀態時發生錯誤：\n{str(e)}")

    def run(self):
        """運行GUI"""
        self.log_message("📊 月營收資料下載器已啟動")
        self.log_message("💡 提示：可以下載單一股票或全股票營收資料")
        self.log_message("📅 新功能：支援日期範圍選擇，可指定下載特定時間區間的資料")
        self.window.mainloop()

def main():
    """主函數"""
    app = MonthlyRevenueDownloaderGUI()
    app.run()

if __name__ == "__main__":
    main()
