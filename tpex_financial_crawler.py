#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TPEX (櫃買中心) 上櫃公司財務報表爬蟲
補充 TWSE API 未涵蓋的上櫃公司資料
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TPEXOpenAPIClient:
    """TPEX (櫃買中心) OpenAPI 客戶端"""
    
    BASE_URL = "https://www.tpex.org.tw/openapi/v1"
    USER_AGENT = "finlab-crawler/1.0"
    
    @classmethod
    def get_data(cls, endpoint: str, timeout: float = 30.0) -> List[Dict[str, Any]]:
        """
        從 TPEX API 端點獲取資料
        
        Args:
            endpoint: API 端點路徑
            timeout: 請求超時時間
            
        Returns:
            包含 API 回應資料的字典列表
        """
        url = f"{cls.BASE_URL}{endpoint}"
        logger.info(f"🔍 獲取 TPEX 資料: {url}")
        
        try:
            resp = requests.get(
                url,
                headers={
                    "User-Agent": cls.USER_AGENT,
                    "Accept": "application/json"
                },
                verify=False,
                timeout=timeout
            )
            resp.raise_for_status()
            
            # 設定正確的編碼
            resp.encoding = 'utf-8'
            data = resp.json()
            
            return data if isinstance(data, list) else [data] if data else []
            
        except Exception as e:
            logger.error(f"❌ 獲取資料失敗 {url}: {e}")
            raise

def test_tpex_endpoints():
    """測試 TPEX API 端點"""
    
    print("=" * 80)
    print("🔍 測試 TPEX API 端點")
    print("=" * 80)
    
    client = TPEXOpenAPIClient()
    
    # 測試一些可能包含財務資料的端點
    test_endpoints = [
        "/tpex_securities",  # 上櫃股票現股當沖交易標的資訊
        "/tpex_mainboard_daily_close_quotes",  # 上櫃股票行情
        "/tpex_mainboard_peratio_analysis",  # 上櫃股票個股本益比、殖利率、股價淨值比
    ]
    
    for endpoint in test_endpoints:
        try:
            print(f"\n📡 測試端點: {endpoint}")
            data = client.get_data(endpoint)
            
            if data and len(data) > 0:
                print(f"   ✅ 成功獲取資料: {len(data)} 筆")
                
                # 檢查第一筆資料的欄位
                first_record = data[0]
                print(f"   📋 欄位範例: {list(first_record.keys())[:5]}...")
                
                # 檢查是否有公司代號
                for key in ['公司代號', 'Code', '證券代號', '股票代號']:
                    if key in first_record:
                        print(f"   🏢 第一家公司: {first_record[key]} {first_record.get('公司名稱', first_record.get('Name', 'N/A'))}")
                        break
            else:
                print(f"   ⚠️ 無資料")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {e}")

def crawl_tpex_stock_info():
    """爬取上櫃股票基本資訊"""
    
    print("\n" + "=" * 80)
    print("📊 爬取上櫃股票基本資訊")
    print("=" * 80)
    
    try:
        client = TPEXOpenAPIClient()
        
        # 獲取上櫃股票資訊
        print("🔍 獲取上櫃股票資訊...")
        raw_data = client.get_data("/tpex_securities")
        
        if not raw_data:
            print("❌ 無法獲取上櫃股票資訊")
            return pd.DataFrame()
        
        print(f"✅ 獲取到 {len(raw_data)} 筆上櫃股票資訊")
        
        # 轉換為 DataFrame
        df = pd.DataFrame(raw_data)
        
        # 資料清理和標準化
        if '公司代號' in df.columns:
            df = df.rename(columns={'公司代號': 'stock_id'})
        elif 'Code' in df.columns:
            df = df.rename(columns={'Code': 'stock_id'})
        elif '證券代號' in df.columns:
            df = df.rename(columns={'證券代號': 'stock_id'})
        
        # 添加股票名稱
        if '公司名稱' in df.columns:
            df = df.rename(columns={'公司名稱': 'stock_name'})
        elif 'Name' in df.columns:
            df = df.rename(columns={'Name': 'stock_name'})
        
        # 添加資料日期和市場類型
        df['date'] = datetime.now().strftime('%Y-%m-%d')
        df['market_type'] = 'OTC'  # Over-The-Counter (上櫃)
        
        # 確保 stock_id 為字串
        if 'stock_id' in df.columns:
            df['stock_id'] = df['stock_id'].astype(str)
        
        print(f"📊 處理後資料形狀: {df.shape}")
        print(f"📋 主要欄位: {list(df.columns[:10])}")
        
        return df
        
    except Exception as e:
        print(f"❌ 爬取上櫃股票資訊失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def crawl_tpex_financial_ratios():
    """爬取上櫃股票財務比率"""
    
    print("\n" + "=" * 80)
    print("📊 爬取上櫃股票財務比率")
    print("=" * 80)
    
    try:
        client = TPEXOpenAPIClient()
        
        # 獲取上櫃股票財務比率
        print("🔍 獲取上櫃股票財務比率...")
        raw_data = client.get_data("/tpex_mainboard_peratio_analysis")
        
        if not raw_data:
            print("❌ 無法獲取上櫃股票財務比率")
            return pd.DataFrame()
        
        print(f"✅ 獲取到 {len(raw_data)} 筆上櫃股票財務比率")
        
        # 轉換為 DataFrame
        df = pd.DataFrame(raw_data)
        
        # 資料清理和標準化
        if '公司代號' in df.columns:
            df = df.rename(columns={'公司代號': 'stock_id'})
        elif 'SecuritiesCompanyCode' in df.columns:
            df = df.rename(columns={'SecuritiesCompanyCode': 'stock_id'})
        elif 'Code' in df.columns:
            df = df.rename(columns={'Code': 'stock_id'})
        elif '證券代號' in df.columns:
            df = df.rename(columns={'證券代號': 'stock_id'})

        # 添加股票名稱
        if '公司名稱' in df.columns:
            df = df.rename(columns={'公司名稱': 'stock_name'})
        elif 'CompanyName' in df.columns:
            df = df.rename(columns={'CompanyName': 'stock_name'})
        elif 'Name' in df.columns:
            df = df.rename(columns={'Name': 'stock_name'})

        # 移除可能重複的 Date 欄位
        if 'Date' in df.columns:
            df = df.drop(columns=['Date'])

        # 添加資料日期和市場類型
        df['date'] = datetime.now().strftime('%Y-%m-%d')
        df['market_type'] = 'OTC'  # Over-The-Counter (上櫃)
        df['data_type'] = 'financial_ratios'
        
        # 確保 stock_id 為字串
        if 'stock_id' in df.columns:
            df['stock_id'] = df['stock_id'].astype(str)
        
        print(f"📊 處理後資料形狀: {df.shape}")
        print(f"📋 主要欄位: {list(df.columns[:10])}")
        
        return df
        
    except Exception as e:
        print(f"❌ 爬取上櫃股票財務比率失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def save_tpex_data_to_db(stock_info_df, financial_ratios_df):
    """將 TPEX 資料儲存到資料庫"""

    print(f"\n💾 儲存 TPEX 資料到資料庫...")

    try:
        # 確保目錄存在 - 使用絕對路徑
        target_dir = r'D:\Finlab\history\tables'
        os.makedirs(target_dir, exist_ok=True)

        # 儲存上櫃股票資訊
        if not stock_info_df.empty:
            stock_db_path = os.path.join(target_dir, 'tpex_stock_info.db')
            conn = sqlite3.connect(stock_db_path)
            stock_info_df.to_sql('tpex_stock_info', conn, if_exists='replace', index=False)
            conn.close()
            print(f"✅ 上櫃股票資訊已儲存: {stock_db_path} ({len(stock_info_df)} 筆)")

        # 儲存上櫃財務比率
        if not financial_ratios_df.empty:
            ratios_db_path = os.path.join(target_dir, 'tpex_financial_ratios.db')
            conn = sqlite3.connect(ratios_db_path)
            financial_ratios_df.to_sql('tpex_financial_ratios', conn, if_exists='replace', index=False)
            conn.close()
            print(f"✅ 上櫃財務比率已儲存: {ratios_db_path} ({len(financial_ratios_df)} 筆)")

        # 儲存合併的 TPEX 資料
        if not stock_info_df.empty or not financial_ratios_df.empty:
            combined_db_path = os.path.join(target_dir, 'tpex_data.db')
            conn = sqlite3.connect(combined_db_path)

            if not stock_info_df.empty:
                stock_info_df.to_sql('tpex_stock_info', conn, if_exists='replace', index=False)

            if not financial_ratios_df.empty:
                financial_ratios_df.to_sql('tpex_financial_ratios', conn, if_exists='replace', index=False)

            conn.close()
            print(f"✅ 合併 TPEX 資料已儲存: {combined_db_path}")

        return True

    except Exception as e:
        print(f"❌ 儲存 TPEX 資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_tpex_coverage():
    """分析 TPEX 資料覆蓋範圍"""

    print(f"\n📊 分析 TPEX 資料覆蓋範圍...")

    try:
        # 檢查 TPEX 資料庫 - 使用絕對路徑
        db_path = os.path.join(r'D:\Finlab\history\tables', 'tpex_data.db')
        if not os.path.exists(db_path):
            print(f"⚠️ TPEX 資料庫不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        
        # 檢查上櫃股票資訊
        try:
            query = "SELECT COUNT(*) as count FROM tpex_stock_info"
            result = pd.read_sql_query(query, conn)
            stock_count = result.iloc[0]['count']
            print(f"📈 上櫃股票數量: {stock_count:,}")
            
            # 顯示範例股票
            query2 = "SELECT stock_id, stock_name FROM tpex_stock_info LIMIT 10"
            sample_stocks = pd.read_sql_query(query2, conn)
            print(f"📋 上櫃股票範例:")
            for _, row in sample_stocks.iterrows():
                print(f"   {row['stock_id']} {row['stock_name']}")
                
        except Exception as e:
            print(f"⚠️ 檢查上櫃股票資訊失敗: {e}")
        
        # 檢查財務比率
        try:
            query3 = "SELECT COUNT(*) as count FROM tpex_financial_ratios"
            result = pd.read_sql_query(query3, conn)
            ratio_count = result.iloc[0]['count']
            print(f"\n📊 上櫃財務比率記錄數: {ratio_count:,}")
            
        except Exception as e:
            print(f"⚠️ 檢查財務比率失敗: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析 TPEX 覆蓋範圍失敗: {e}")

def main():
    """主函數"""
    
    print("🚀 TPEX (櫃買中心) 上櫃公司資料爬蟲")
    print("補充 TWSE API 未涵蓋的上櫃公司資料")
    
    # 測試 API 端點
    test_tpex_endpoints()
    
    # 爬取上櫃股票資訊
    stock_info_df = crawl_tpex_stock_info()
    
    # 等待一下避免 API 限制
    time.sleep(2)
    
    # 爬取上櫃財務比率
    financial_ratios_df = crawl_tpex_financial_ratios()
    
    # 儲存資料
    save_success = save_tpex_data_to_db(stock_info_df, financial_ratios_df)
    
    # 分析覆蓋範圍
    if save_success:
        analyze_tpex_coverage()
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 TPEX 爬取總結")
    print(f"=" * 80)
    
    if save_success:
        print(f"🎉 TPEX 資料爬取成功！")
        print(f"📊 上櫃股票資訊: {len(stock_info_df)} 筆")
        print(f"📊 上櫃財務比率: {len(financial_ratios_df)} 筆")
        print(f"💾 資料已儲存到 history/tables/ 目錄")
        
        print(f"\n💡 建議:")
        print(f"   1. TPEX API 主要提供交易和基本資訊")
        print(f"   2. 詳細財務報表可能需要其他來源")
        print(f"   3. 可與 TWSE API 資料合併使用")
        print(f"   4. 或繼續使用 MOPS 爬蟲獲取完整財務報表")
        
        return True
    else:
        print(f"❌ TPEX 資料爬取失敗")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
