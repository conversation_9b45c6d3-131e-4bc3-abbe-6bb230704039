#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試不同成功率設定的效果
"""

from trading_rule_miner import TradingRuleMiner
from trading_mantra_generator import TradingMantraGenerator

def test_success_rate(min_success_rate, min_avg_return=0.01):
    """測試特定成功率設定"""
    print(f"\n🧪 測試成功率 {min_success_rate*100:.0f}%")
    print("-" * 40)
    
    try:
        # 創建規則挖掘器
        rule_miner = TradingRuleMiner()
        rule_miner.min_success_rate = min_success_rate
        rule_miner.min_avg_return = min_avg_return
        
        # 執行分析（使用所有64檔股票）
        combinations = rule_miner.run_mass_analysis(64)
        
        # 提取規則
        rules = rule_miner.extract_trading_rules(combinations)
        
        print(f"📊 發現組合: {len(combinations)} 種")
        print(f"📋 提取規則: {len(rules)} 條")
        
        if len(rules) > 0:
            # 統計成功率分布
            success_rates = [rule.success_rate for rule in rules]
            avg_success_rate = sum(success_rates) / len(success_rates)
            max_success_rate = max(success_rates)
            min_success_rate_actual = min(success_rates)
            
            print(f"✅ 平均成功率: {avg_success_rate:.1%}")
            print(f"🏆 最高成功率: {max_success_rate:.1%}")
            print(f"📉 最低成功率: {min_success_rate_actual:.1%}")
            
            # 顯示前3條最佳規則
            rules.sort(key=lambda x: x.success_rate, reverse=True)
            print(f"\n🏆 前3條最佳規則:")
            for i, rule in enumerate(rules[:3], 1):
                print(f"  {i}. {rule.rule_name}")
                print(f"     成功率: {rule.success_rate:.1%}, 平均獲利: {rule.avg_profit:.1%}")
            
            return len(rules), avg_success_rate, max_success_rate
        else:
            print("❌ 沒有發現符合條件的規則")
            return 0, 0, 0
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return 0, 0, 0

def compare_success_rates():
    """比較不同成功率設定的效果"""
    print("🔍 比較不同成功率設定的效果")
    print("=" * 60)
    
    # 測試不同的成功率設定
    test_rates = [0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.85]
    
    results = []
    
    for rate in test_rates:
        rule_count, avg_rate, max_rate = test_success_rate(rate)
        results.append({
            'min_rate': rate,
            'rule_count': rule_count,
            'avg_rate': avg_rate,
            'max_rate': max_rate
        })
    
    # 顯示比較結果
    print(f"\n📊 成功率設定比較結果:")
    print(f"{'最低成功率':<10} {'規則數量':<8} {'平均成功率':<12} {'最高成功率':<12}")
    print("-" * 50)
    
    for result in results:
        if result['rule_count'] > 0:
            print(f"{result['min_rate']*100:>8.0f}%   {result['rule_count']:>6}   "
                  f"{result['avg_rate']:>10.1%}   {result['max_rate']:>10.1%}")
        else:
            print(f"{result['min_rate']*100:>8.0f}%   {result['rule_count']:>6}   {'無規則':>10}   {'無規則':>10}")
    
    # 建議最佳設定
    print(f"\n💡 建議:")
    
    # 找到有規則的最高成功率設定
    valid_results = [r for r in results if r['rule_count'] > 0]
    
    if valid_results:
        # 推薦平衡點：有足夠規則數量且成功率較高
        balanced_choice = None
        for result in valid_results:
            if result['rule_count'] >= 5:  # 至少5條規則
                balanced_choice = result
        
        if balanced_choice:
            print(f"🎯 平衡選擇: {balanced_choice['min_rate']*100:.0f}% "
                  f"(規則數量: {balanced_choice['rule_count']}, "
                  f"平均成功率: {balanced_choice['avg_rate']:.1%})")
        
        # 推薦高品質選擇
        high_quality = valid_results[-1]  # 最高成功率且有規則的設定
        print(f"🏆 高品質選擇: {high_quality['min_rate']*100:.0f}% "
              f"(規則數量: {high_quality['rule_count']}, "
              f"平均成功率: {high_quality['avg_rate']:.1%})")
    
    return results

def generate_high_quality_mantras():
    """生成高品質交易口訣"""
    print(f"\n🎭 生成高品質交易口訣")
    print("=" * 40)
    
    try:
        # 使用較高的成功率設定
        rule_miner = TradingRuleMiner()
        rule_miner.min_success_rate = 0.70  # 70%成功率
        rule_miner.min_avg_return = 0.015   # 1.5%平均報酬
        
        # 執行分析
        combinations = rule_miner.run_mass_analysis(64)
        rules = rule_miner.extract_trading_rules(combinations)
        
        if len(rules) > 0:
            # 生成口訣
            mantra_generator = TradingMantraGenerator()
            mantras = mantra_generator.generate_all_mantras(rules)
            
            print(f"✅ 生成 {len(rules)} 條高品質規則")
            
            # 顯示高品質買入口訣
            print(f"\n🟢 高品質買入口訣:")
            for i, mantra in enumerate(mantras['buy_mantras'][:5], 1):
                print(f"  {i}. {mantra.mantra_text}")
            
            # 顯示高品質賣出口訣
            print(f"\n🔴 高品質賣出口訣:")
            for i, mantra in enumerate(mantras['sell_mantras'][:5], 1):
                print(f"  {i}. {mantra.mantra_text}")
            
            return True
        else:
            print("❌ 沒有發現高品質規則，建議降低成功率門檻")
            return False
            
    except Exception as e:
        print(f"❌ 生成高品質口訣失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 提高最低成功率測試")
    print("=" * 60)
    
    # 1. 比較不同成功率設定
    results = compare_success_rates()
    
    # 2. 生成高品質口訣
    generate_high_quality_mantras()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 如何在GUI中提高最低成功率:")
    print(f"1. 啟動GUI: python auto_rule_discovery_gui.py")
    print(f"2. 在左側面板找到「最低成功率」設定")
    print(f"3. 將數值從60%調整到70%或更高")
    print(f"4. 點擊「🚀 開始分析」")
    print(f"5. 查看結果中的高品質規則")
    
    print(f"\n⚠️ 注意:")
    print(f"• 成功率越高 = 規則越少但品質越好")
    print(f"• 成功率越低 = 規則越多但品質參差不齊")
    print(f"• 建議在65-75%之間找到平衡點")

if __name__ == "__main__":
    main()
