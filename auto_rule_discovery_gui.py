#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化規則發現工具GUI
讓用戶可以自行運作，持續發現和更新交易規則
"""

import sys
import os
import json
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QTextEdit, QProgressBar,
                            QSpinBox, QComboBox, QTableWidget, QTableWidgetItem,
                            QTabWidget, QGroupBox, QCheckBox, QMessageBox,
                            QHeaderView, QSplitter, QFrame, QScrollArea)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette
import pandas as pd
from typing import Dict, List

# 導入分析模組
from mass_backtest_analyzer import MassBacktestAnalyzer
from trading_rule_miner import TradingRuleMiner
from trading_mantra_generator import TradingMantraGenerator

class AnalysisWorker(QThread):
    """分析工作線程"""
    progress_updated = pyqtSignal(int, str)
    analysis_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, stock_limit, min_success_rate, min_avg_return):
        super().__init__()
        self.stock_limit = stock_limit
        self.min_success_rate = min_success_rate
        self.min_avg_return = min_avg_return
        self.is_running = True
    
    def run(self):
        try:
            self.progress_updated.emit(10, "初始化分析器...")

            # 檢查是否被停止
            if not self.is_running:
                return

            # 初始化分析器
            from trading_rule_miner import TradingRuleMiner
            rule_miner = TradingRuleMiner()
            rule_miner.min_success_rate = self.min_success_rate / 100
            rule_miner.min_avg_return = self.min_avg_return / 100

            self.progress_updated.emit(20, "載入股票數據...")

            # 檢查是否被停止
            if not self.is_running:
                return

            # 執行大量分析 - 限制股票數量避免過長時間
            actual_limit = min(self.stock_limit, 100)  # 最多100檔避免太久
            combinations = rule_miner.run_mass_analysis(actual_limit)

            if not self.is_running:
                return

            self.progress_updated.emit(60, "提取交易規則...")

            # 提取交易規則
            rules = rule_miner.extract_trading_rules(combinations)

            if not self.is_running:
                return

            self.progress_updated.emit(80, "生成交易口訣...")

            # 生成口訣
            from trading_mantra_generator import TradingMantraGenerator
            mantra_generator = TradingMantraGenerator()
            mantras = mantra_generator.generate_all_mantras(rules)

            if not self.is_running:
                return

            self.progress_updated.emit(100, "分析完成！")

            # 返回結果
            result = {
                'rules': rules,
                'mantras': mantras,
                'combinations': combinations,
                'total_stocks': actual_limit,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            self.analysis_completed.emit(result)

        except Exception as e:
            import traceback
            error_msg = f"分析過程發生錯誤: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)
    
    def stop(self):
        self.is_running = False

class AutoRuleDiscoveryGUI(QMainWindow):
    """自動化規則發現工具主界面"""
    
    def __init__(self):
        super().__init__()
        self.analysis_worker = None
        self.current_results = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle("🔍 自動化交易規則發現工具")
        self.setGeometry(100, 100, 1400, 900)

        # 創建選單欄
        self.create_menu_bar()
        
        # 設置深色主題 - 簡化樣式避免QPainter問題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Microsoft JhengHei UI', Arial, sans-serif;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #0078d4;
                color: #ffffff;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
            QGroupBox {
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: #2a2a2a;
                font-weight: bold;
                font-size: 14px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #00aaff;
                font-size: 15px;
                font-weight: bold;
            }
            QLabel {
                color: #ffffff;
                font-size: 13px;
            }
            QSpinBox, QComboBox {
                background-color: #333333;
                border: 2px solid #555555;
                border-radius: 5px;
                padding: 8px;
                color: #ffffff;
                font-size: 13px;
            }
            QTextEdit {
                background-color: #1f1f1f;
                border: 2px solid #555555;
                border-radius: 5px;
                color: #ffffff;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
            QTableWidget {
                background-color: #1f1f1f;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #404040;
                border: 2px solid #555555;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 2px solid #555555;
                background-color: #2a2a2a;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #333333;
                color: #ffffff;
                padding: 10px 15px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                text-align: center;
                background-color: #333333;
                color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QVBoxLayout(central_widget)
        
        # 標題 - 減少高度和邊距
        title_label = QLabel("🔍 自動化交易規則發現工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00aaff; margin: 5px; padding: 5px;")
        title_label.setMaximumHeight(40)  # 限制最大高度
        main_layout.addWidget(title_label)
        
        # 創建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左側控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右側結果面板
        result_panel = self.create_result_panel()
        splitter.addWidget(result_panel)
        
        # 設置分割器比例
        splitter.setSizes([400, 1000])
        
        # 狀態欄
        self.statusBar().showMessage("就緒 - 請設置參數並開始分析")
        self.statusBar().setStyleSheet("background-color: #2a2a2a; color: #ffffff; padding: 5px;")

    def create_menu_bar(self):
        """創建選單欄"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #2a2a2a;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 5px 10px;
            }
            QMenuBar::item:selected {
                background-color: #0078d4;
            }
            QMenu {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
            }
            QMenu::item {
                padding: 5px 20px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)

        # 幫助選單
        help_menu = menubar.addMenu('幫助')

        # 系統說明
        system_help_action = help_menu.addAction('🔍 系統說明')
        system_help_action.triggered.connect(self.show_system_help)

        # 使用指南
        usage_guide_action = help_menu.addAction('📖 使用指南')
        usage_guide_action.triggered.connect(self.show_usage_guide)

        # 策略說明
        strategy_help_action = help_menu.addAction('🎭 策略說明')
        strategy_help_action.triggered.connect(self.show_strategy_help)
    
    def create_control_panel(self) -> QWidget:
        """創建控制面板"""
        # 創建捲軸區域
        scroll_area = QScrollArea()
        scroll_area.setMaximumWidth(400)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 創建內容面板
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 分析參數設置
        params_group = QGroupBox("📊 分析參數設置")
        params_layout = QVBoxLayout(params_group)
        
        # 股票數量
        stock_layout = QHBoxLayout()
        stock_layout.addWidget(QLabel("分析股票數量:"))
        self.stock_limit_spin = QSpinBox()
        self.stock_limit_spin.setRange(100, 2000)
        self.stock_limit_spin.setValue(500)
        self.stock_limit_spin.setSuffix(" 檔")
        stock_layout.addWidget(self.stock_limit_spin)
        params_layout.addLayout(stock_layout)
        
        # 最低成功率
        success_layout = QHBoxLayout()
        success_layout.addWidget(QLabel("最低成功率:"))
        self.min_success_spin = QSpinBox()
        self.min_success_spin.setRange(50, 90)
        self.min_success_spin.setValue(65)
        self.min_success_spin.setSuffix(" %")
        success_layout.addWidget(self.min_success_spin)
        params_layout.addLayout(success_layout)
        
        # 最低平均報酬
        return_layout = QHBoxLayout()
        return_layout.addWidget(QLabel("最低平均報酬:"))
        self.min_return_spin = QSpinBox()
        self.min_return_spin.setRange(1, 10)
        self.min_return_spin.setValue(2)
        self.min_return_spin.setSuffix(" %")
        return_layout.addWidget(self.min_return_spin)
        params_layout.addLayout(return_layout)
        
        layout.addWidget(params_group)
        
        # 控制按鈕
        control_group = QGroupBox("🎮 控制操作")
        control_layout = QVBoxLayout(control_group)
        
        self.start_btn = QPushButton("🚀 開始分析")
        self.start_btn.clicked.connect(self.start_analysis)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止分析")
        self.stop_btn.clicked.connect(self.stop_analysis)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        self.export_btn = QPushButton("💾 導出結果")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        control_layout.addWidget(self.export_btn)

        self.filter_btn = QPushButton("🔍 篩選股票")
        self.filter_btn.clicked.connect(self.filter_stocks_by_selected_mantras)
        self.filter_btn.setEnabled(False)
        control_layout.addWidget(self.filter_btn)
        
        layout.addWidget(control_group)
        
        # 進度顯示
        progress_group = QGroupBox("📈 分析進度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("等待開始...")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)

        # 策略選擇
        strategy_group = QGroupBox("🎯 策略選擇")
        strategy_layout = QVBoxLayout(strategy_group)

        self.rsi_checkbox = QCheckBox("RSI策略")
        self.rsi_checkbox.setChecked(True)
        strategy_layout.addWidget(self.rsi_checkbox)

        self.macd_checkbox = QCheckBox("MACD策略")
        self.macd_checkbox.setChecked(True)
        strategy_layout.addWidget(self.macd_checkbox)

        self.bb_checkbox = QCheckBox("布林通道策略")
        self.bb_checkbox.setChecked(True)
        strategy_layout.addWidget(self.bb_checkbox)

        self.ma_checkbox = QCheckBox("移動平均策略")
        self.ma_checkbox.setChecked(True)
        strategy_layout.addWidget(self.ma_checkbox)

        layout.addWidget(strategy_group)

        # 高級設定
        advanced_group = QGroupBox("⚙️ 高級設定")
        advanced_layout = QVBoxLayout(advanced_group)

        # 三重確認選項
        triple_layout = QHBoxLayout()
        triple_layout.addWidget(QLabel("三重確認:"))
        self.triple_confirmation_checkbox = QCheckBox("啟用")
        self.triple_confirmation_checkbox.setToolTip("啟用三重確認可提高成功率但減少信號數量")
        triple_layout.addWidget(self.triple_confirmation_checkbox)
        advanced_layout.addLayout(triple_layout)

        # 最少信號數量
        min_signals_layout = QHBoxLayout()
        min_signals_layout.addWidget(QLabel("最少信號數量:"))
        self.min_signals_spin = QSpinBox()
        self.min_signals_spin.setRange(3, 20)
        self.min_signals_spin.setValue(5)
        self.min_signals_spin.setToolTip("每個規則至少需要的信號數量")
        min_signals_layout.addWidget(self.min_signals_spin)
        advanced_layout.addLayout(min_signals_layout)

        # 分析時間範圍
        timerange_layout = QHBoxLayout()
        timerange_layout.addWidget(QLabel("分析時間範圍:"))
        self.timerange_combo = QComboBox()
        self.timerange_combo.addItems(["1年", "2年", "3年"])
        self.timerange_combo.setCurrentText("2年")
        timerange_layout.addWidget(self.timerange_combo)
        advanced_layout.addLayout(timerange_layout)

        layout.addWidget(advanced_group)

        # 快速設定
        quick_group = QGroupBox("⚡ 快速設定")
        quick_layout = QVBoxLayout(quick_group)

        conservative_btn = QPushButton("🛡️ 保守設定")
        conservative_btn.setToolTip("高成功率、低風險設定")
        conservative_btn.clicked.connect(self.set_conservative_params)
        quick_layout.addWidget(conservative_btn)

        balanced_btn = QPushButton("⚖️ 平衡設定")
        balanced_btn.setToolTip("平衡成功率和信號數量")
        balanced_btn.clicked.connect(self.set_balanced_params)
        quick_layout.addWidget(balanced_btn)

        aggressive_btn = QPushButton("🚀 積極設定")
        aggressive_btn.setToolTip("更多信號、較低成功率門檻")
        aggressive_btn.clicked.connect(self.set_aggressive_params)
        quick_layout.addWidget(aggressive_btn)

        layout.addWidget(quick_group)

        # 統計信息
        stats_group = QGroupBox("📊 統計信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(200)
        self.stats_text.setPlainText("尚未開始分析...")
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()

        # 將面板設置到捲軸中
        scroll_area.setWidget(panel)
        return scroll_area
    
    def create_result_panel(self) -> QWidget:
        """創建結果面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 創建標籤頁
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 交易規則標籤頁
        self.rules_table = QTableWidget()
        self.tab_widget.addTab(self.rules_table, "📋 交易規則")
        
        # 買入口訣標籤頁
        self.buy_mantras_table = QTableWidget()
        self.tab_widget.addTab(self.buy_mantras_table, "🟢 買入口訣")
        
        # 賣出口訣標籤頁
        self.sell_mantras_table = QTableWidget()
        self.tab_widget.addTab(self.sell_mantras_table, "🔴 賣出口訣")
        
        # 風險控制標籤頁
        self.risk_mantras_table = QTableWidget()
        self.tab_widget.addTab(self.risk_mantras_table, "⚠️ 風險控制")
        
        # 詳細日誌標籤頁
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.tab_widget.addTab(self.log_text, "📝 詳細日誌")

        # 股票信號掃描標籤頁
        self.stock_signals_table = QTableWidget()
        self.tab_widget.addTab(self.stock_signals_table, "📈 股票信號掃描")

        # 符合條件股票清單標籤頁
        self.filtered_stocks_table = QTableWidget()
        self.tab_widget.addTab(self.filtered_stocks_table, "📋 符合條件股票清單")

        return panel

    def start_analysis(self):
        """開始分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            return

        # 獲取參數
        stock_limit = self.stock_limit_spin.value()
        min_success_rate = self.min_success_spin.value()
        min_avg_return = self.min_return_spin.value()

        # 更新UI狀態
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.export_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("準備開始分析...")

        # 清空結果
        self.clear_results()

        # 創建並啟動工作線程
        self.analysis_worker = AnalysisWorker(stock_limit, min_success_rate, min_avg_return)
        self.analysis_worker.progress_updated.connect(self.update_progress)
        self.analysis_worker.analysis_completed.connect(self.analysis_finished)
        self.analysis_worker.error_occurred.connect(self.analysis_error)
        self.analysis_worker.start()

        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 開始分析 {stock_limit} 檔股票...")
        self.statusBar().showMessage("分析進行中...")

    def stop_analysis(self):
        """停止分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            self.analysis_worker.stop()
            self.analysis_worker.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_label.setText("分析已停止")
        self.statusBar().showMessage("分析已停止")
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 用戶停止分析")

    def update_progress(self, value: int, message: str):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

    def analysis_finished(self, results: dict):
        """分析完成"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 分析完成，開始處理結果...")
            self.current_results = results

            # 更新UI狀態
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.export_btn.setEnabled(True)
            self.filter_btn.setEnabled(True)
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ UI狀態更新完成")

            # 顯示基本結果摘要
            rules_count = len(results.get('rules', []))
            mantras = results.get('mantras', {})
            buy_mantras_count = len(mantras.get('buy_mantras', []))
            sell_mantras_count = len(mantras.get('sell_mantras', []))

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 結果摘要:")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 交易規則: {rules_count} 條")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 買入口訣: {buy_mantras_count} 條")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 賣出口訣: {sell_mantras_count} 條")

            # 簡化處理 - 只執行基本的結果顯示
            try:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📋 顯示結果...")
                self.display_results(results)
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 結果顯示完成")
            except Exception as e:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 結果顯示失敗: {str(e)}")

            try:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📈 更新統計信息...")
                self.update_statistics(results)
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 統計信息更新完成")
            except Exception as e:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 統計信息更新失敗: {str(e)}")

            # 延遲執行其他功能，避免一次性處理太多
            QTimer.singleShot(1000, self.delayed_processing)

            self.statusBar().showMessage("✅ 基本分析完成！")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 基本處理完成，其他功能將延遲執行")

        except Exception as e:
            import traceback
            error_msg = f"分析完成處理失敗: {str(e)}\n{traceback.format_exc()}"
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ {error_msg}")
            self.statusBar().showMessage("❌ 分析處理失敗")

            # 確保UI狀態正確
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

    def delayed_processing(self):
        """延遲處理其他功能"""
        try:
            if not self.current_results:
                return

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 開始延遲處理...")

            # 生成策略組合口訣
            try:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎭 生成策略組合口訣...")
                self.generate_strategy_mantras()
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 策略組合口訣生成完成")
            except Exception as e:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 策略組合口訣生成失敗: {str(e)}")

            # 再次延遲執行股票掃描
            QTimer.singleShot(2000, self.delayed_stock_processing)

        except Exception as e:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 延遲處理失敗: {str(e)}")

    def delayed_stock_processing(self):
        """延遲處理股票相關功能"""
        try:
            if not self.current_results:
                return

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📡 開始股票處理...")

            # 執行股票信號掃描
            try:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🔍 股票信號掃描...")
                self.scan_stock_signals(self.current_results)
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 股票信號掃描完成")
            except Exception as e:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 股票信號掃描失敗: {str(e)}")

            # 自動執行股票篩選
            try:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 自動股票篩選...")
                self.filter_stocks_by_selected_mantras()
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 自動股票篩選完成")
            except Exception as e:
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 自動股票篩選失敗: {str(e)}")

            self.statusBar().showMessage("🎉 所有處理完成！")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 所有處理完成！")

        except Exception as e:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 股票處理失敗: {str(e)}")

    def analysis_error(self, error_message: str):
        """分析錯誤"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        QMessageBox.critical(self, "分析錯誤", error_message)
        self.statusBar().showMessage("分析失敗")
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 錯誤: {error_message}")

    def scan_stock_signals(self, results: dict):
        """掃描股票信號"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 開始掃描股票信號...")
            self.statusBar().showMessage("掃描股票信號中...")

            # 導入掃描器
            from stock_signal_scanner import StockSignalScanner

            scanner = StockSignalScanner()

            # 從結果中提取口訣信息
            mantras = []

            # 處理買入口訣
            mantras_data = results.get('mantras', {})
            if 'buy_mantras' in mantras_data:
                for mantra in mantras_data['buy_mantras']:
                    if hasattr(mantra, 'mantra_text'):
                        mantras.append({
                            'mantra': mantra.mantra_text,
                            'success_rate': getattr(mantra, 'success_rate', 0.70),
                            'avg_profit': getattr(mantra, 'avg_return', 0.05),
                            'strategy_code': getattr(mantra, 'mantra_id', 'unknown')
                        })
                    elif isinstance(mantra, dict):
                        mantras.append({
                            'mantra': mantra.get('mantra_text', ''),
                            'success_rate': mantra.get('success_rate', 0.70),
                            'avg_profit': mantra.get('avg_return', 0.05),
                            'strategy_code': mantra.get('mantra_id', 'unknown')
                        })

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 提取到 {len(mantras)} 條口訣用於掃描")

            # 掃描信號
            signals = scanner.scan_all_stocks(mantras)

            # 顯示信號結果
            self.display_stock_signals(signals)

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 股票信號掃描完成，發現 {len(signals)} 個信號")

        except Exception as e:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 股票信號掃描失敗: {str(e)}")

    def display_stock_signals(self, signals):
        """顯示股票信號"""
        if not signals:
            self.stock_signals_table.setRowCount(0)
            self.stock_signals_table.setColumnCount(0)
            return

        # 設置表格
        headers = ['股票代碼', '股票名稱', '口訣', '策略代碼', '成功率', '平均獲利',
                  '風險等級', '市場條件', '信號日期', '當前價格', '信號強度']

        self.stock_signals_table.setColumnCount(len(headers))
        self.stock_signals_table.setHorizontalHeaderLabels(headers)
        self.stock_signals_table.setRowCount(len(signals))

        # 填充數據
        for row, signal in enumerate(signals):
            self.stock_signals_table.setItem(row, 0, QTableWidgetItem(signal.stock_id))
            self.stock_signals_table.setItem(row, 1, QTableWidgetItem(signal.stock_name))
            self.stock_signals_table.setItem(row, 2, QTableWidgetItem(signal.mantra))
            self.stock_signals_table.setItem(row, 3, QTableWidgetItem(signal.strategy_code))
            self.stock_signals_table.setItem(row, 4, QTableWidgetItem(f"{signal.success_rate:.1%}"))
            self.stock_signals_table.setItem(row, 5, QTableWidgetItem(f"{signal.avg_profit:.1%}"))
            self.stock_signals_table.setItem(row, 6, QTableWidgetItem(signal.risk_level))
            self.stock_signals_table.setItem(row, 7, QTableWidgetItem(signal.market_condition))
            self.stock_signals_table.setItem(row, 8, QTableWidgetItem(signal.signal_date))
            self.stock_signals_table.setItem(row, 9, QTableWidgetItem(f"{signal.current_price:.2f}"))
            self.stock_signals_table.setItem(row, 10, QTableWidgetItem(f"{signal.signal_strength:.1%}"))

        # 調整列寬
        self.stock_signals_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # 設置排序
        self.stock_signals_table.setSortingEnabled(True)

        # 預設按成功率排序
        self.stock_signals_table.sortItems(4, Qt.SortOrder.DescendingOrder)

    def generate_strategy_mantras(self):
        """生成策略組合口訣"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 生成策略組合口訣...")

            # 導入策略組合生成器
            from strategy_combination_generator import StrategyCombinationGenerator

            generator = StrategyCombinationGenerator()
            mantras = generator.generate_all_combinations()

            # 轉換為GUI需要的格式
            buy_mantras = []
            sell_mantras = []

            for mantra in mantras['buy']:
                buy_mantras.append(type('Mantra', (), {
                    'mantra_text': mantra.mantra_text,
                    'success_rate': mantra.success_rate,
                    'avg_return': mantra.avg_profit,
                    'risk_level': mantra.risk_level,
                    'market_condition': mantra.market_condition
                })())

            for mantra in mantras['sell']:
                sell_mantras.append(type('Mantra', (), {
                    'mantra_text': mantra.mantra_text,
                    'success_rate': mantra.success_rate,
                    'avg_return': mantra.avg_profit,
                    'risk_level': mantra.risk_level,
                    'market_condition': mantra.market_condition
                })())

            # 顯示口訣
            self.display_mantras(self.buy_mantras_table, buy_mantras, "買入")
            self.display_mantras(self.sell_mantras_table, sell_mantras, "賣出")

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 策略組合口訣生成完成: {len(buy_mantras)}條買入, {len(sell_mantras)}條賣出")

        except Exception as e:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 策略組合口訣生成失敗: {str(e)}")

    def clear_results(self):
        """清空結果"""
        for table in [self.rules_table, self.buy_mantras_table,
                     self.sell_mantras_table, self.risk_mantras_table,
                     self.stock_signals_table, self.filtered_stocks_table]:
            table.setRowCount(0)
            table.setColumnCount(0)

        self.stats_text.clear()
        self.log_text.clear()

    def display_results(self, results: dict):
        """顯示結果"""
        # 顯示交易規則
        self.display_trading_rules(results['rules'])

        # 顯示口訣
        mantras = results['mantras']
        self.display_mantras(self.buy_mantras_table, mantras['buy_mantras'], "買入")
        self.display_mantras(self.sell_mantras_table, mantras['sell_mantras'], "賣出")
        self.display_mantras(self.risk_mantras_table, mantras['risk_mantras'], "風險")

    def display_trading_rules(self, rules: list):
        """顯示交易規則"""
        if not rules:
            return

        table = self.rules_table
        table.setRowCount(len(rules))
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "規則名稱", "成功率", "平均獲利", "風險等級", "樣本數", "市場條件"
        ])

        for i, rule in enumerate(rules):
            table.setItem(i, 0, QTableWidgetItem(rule.rule_name))
            table.setItem(i, 1, QTableWidgetItem(f"{rule.success_rate:.1%}"))
            table.setItem(i, 2, QTableWidgetItem(f"{rule.avg_profit:.1%}"))

            # 風險等級顏色標示
            risk_item = QTableWidgetItem(self.get_risk_level_text(rule.risk_score))
            if rule.risk_score <= 10:
                risk_item.setBackground(QColor(0, 150, 0, 100))  # 綠色
            elif rule.risk_score <= 20:
                risk_item.setBackground(QColor(255, 165, 0, 100))  # 橙色
            else:
                risk_item.setBackground(QColor(255, 0, 0, 100))  # 紅色
            table.setItem(i, 3, risk_item)

            table.setItem(i, 4, QTableWidgetItem(str(rule.sample_size)))
            table.setItem(i, 5, QTableWidgetItem(', '.join(rule.market_conditions)))

        # 調整列寬
        table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

    def display_mantras(self, table: QTableWidget, mantras: list, category: str):
        """顯示口訣"""
        if not mantras:
            return

        table.setRowCount(len(mantras))

        if category == "風險":
            table.setColumnCount(3)
            table.setHorizontalHeaderLabels(["口訣", "風險等級", "適用條件"])

            for i, mantra in enumerate(mantras):
                table.setItem(i, 0, QTableWidgetItem(mantra.mantra_text))
                table.setItem(i, 1, QTableWidgetItem(mantra.risk_level))
                table.setItem(i, 2, QTableWidgetItem(', '.join(mantra.conditions)))
        else:
            table.setColumnCount(6)
            table.setHorizontalHeaderLabels([
                "啟用", "口訣", "成功率", "平均獲利", "風險等級", "市場條件"
            ])

            for i, mantra in enumerate(mantras):
                # 勾選框
                checkbox = QCheckBox()
                checkbox.setChecked(True)  # 預設勾選
                checkbox.stateChanged.connect(lambda state, row=i: self.on_mantra_checkbox_changed(category, row, state))
                table.setCellWidget(i, 0, checkbox)

                table.setItem(i, 1, QTableWidgetItem(mantra.mantra_text))
                table.setItem(i, 2, QTableWidgetItem(f"{mantra.success_rate:.1%}"))
                table.setItem(i, 3, QTableWidgetItem(f"{mantra.avg_return:.1%}"))
                table.setItem(i, 4, QTableWidgetItem(mantra.risk_level))
                table.setItem(i, 5, QTableWidgetItem(mantra.market_condition))

        # 調整列寬
        table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        if category != "風險":
            table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 口訣列可伸縮
        else:
            table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

    def get_risk_level_text(self, risk_score: float) -> str:
        """獲取風險等級文字"""
        if risk_score <= 10:
            return f"低風險 ({risk_score:.1f}%)"
        elif risk_score <= 20:
            return f"中風險 ({risk_score:.1f}%)"
        else:
            return f"高風險 ({risk_score:.1f}%)"

    def update_statistics(self, results: dict):
        """更新統計信息"""
        rules = results['rules']
        mantras = results['mantras']

        stats_text = f"""
📊 分析統計摘要
{'='*30}

🎯 基本信息:
• 分析股票數量: {results['total_stocks']} 檔
• 分析完成時間: {results['analysis_time']}
• 發現交易規則: {len(rules)} 條

📈 規則品質分布:
• 高成功率規則 (≥75%): {sum(1 for r in rules if r.success_rate >= 0.75)} 條
• 中成功率規則 (65-75%): {sum(1 for r in rules if 0.65 <= r.success_rate < 0.75)} 條
• 低風險規則 (≤10%): {sum(1 for r in rules if r.risk_score <= 10)} 條

🎭 口訣生成結果:
• 買入口訣: {len(mantras['buy_mantras'])} 條
• 賣出口訣: {len(mantras['sell_mantras'])} 條
• 風險控制口訣: {len(mantras['risk_mantras'])} 條

🏆 最佳規則 (前3名):
"""

        # 添加前3名規則
        top_rules = sorted(rules, key=lambda x: x.success_rate * x.avg_profit, reverse=True)[:3]
        for i, rule in enumerate(top_rules, 1):
            stats_text += f"  {i}. {rule.rule_name}\n"
            stats_text += f"     成功率: {rule.success_rate:.1%}, 獲利: {rule.avg_profit:.1%}\n"

        self.stats_text.setPlainText(stats_text)

    def export_results(self):
        """導出結果"""
        if not self.current_results:
            QMessageBox.warning(self, "警告", "沒有可導出的結果")
            return

        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 導出口訣到JSON
            mantra_generator = TradingMantraGenerator()
            mantra_generator.export_mantras_to_json(
                self.current_results['mantras'],
                f"trading_mantras_{timestamp}.json"
            )

            # 導出規則到Excel
            rules_df = pd.DataFrame([
                {
                    '規則名稱': rule.rule_name,
                    '成功率': f"{rule.success_rate:.1%}",
                    '平均獲利': f"{rule.avg_profit:.1%}",
                    '風險分數': f"{rule.risk_score:.1f}%",
                    '樣本數': rule.sample_size,
                    '市場條件': ', '.join(rule.market_conditions),
                    '買入條件': ', '.join(rule.buy_conditions),
                    '賣出條件': ', '.join(rule.sell_conditions)
                }
                for rule in self.current_results['rules']
            ])

            rules_df.to_excel(f"trading_rules_{timestamp}.xlsx", index=False)

            QMessageBox.information(self, "導出成功",
                                  f"結果已導出:\n"
                                  f"• trading_mantras_{timestamp}.json\n"
                                  f"• trading_rules_{timestamp}.xlsx")

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 結果導出完成")

        except Exception as e:
            QMessageBox.critical(self, "導出失敗", f"導出過程發生錯誤:\n{str(e)}")

    def set_conservative_params(self):
        """設定保守參數"""
        self.stock_limit_spin.setValue(100)
        self.min_success_spin.setValue(75)
        self.min_return_spin.setValue(3)
        self.triple_confirmation_checkbox.setChecked(True)
        self.min_signals_spin.setValue(8)
        self.statusBar().showMessage("已設定保守參數 - 高成功率、低風險")

    def set_balanced_params(self):
        """設定平衡參數"""
        self.stock_limit_spin.setValue(300)
        self.min_success_spin.setValue(65)
        self.min_return_spin.setValue(2)
        self.triple_confirmation_checkbox.setChecked(False)
        self.min_signals_spin.setValue(5)
        self.statusBar().showMessage("已設定平衡參數 - 平衡成功率和信號數量")

    def set_aggressive_params(self):
        """設定積極參數"""
        self.stock_limit_spin.setValue(500)
        self.min_success_spin.setValue(55)
        self.min_return_spin.setValue(1)
        self.triple_confirmation_checkbox.setChecked(False)
        self.min_signals_spin.setValue(3)
        self.statusBar().showMessage("已設定積極參數 - 更多信號、較低門檻")

    def show_system_help(self):
        """顯示系統說明"""
        help_text = """
🎉 策略組合交易規則發現系統

🆕 系統功能特色：
• 22條策略組合口訣 (11買入+11賣出)
• 每個口訣都有勾選框控制
• 自動篩選符合條件的股票
• 新增「符合條件股票清單」標籤頁

🎯 使用方式：
1. 開始分析 → 生成策略組合口訣
2. 在買入/賣出口訣標籤頁勾選想要的策略
3. 查看「符合條件股票清單」的篩選結果
4. 人工確認後執行交易

💡 這是完整的策略組合系統：
• 四策略確認 (最高勝率77.5%)
• 三策略確認 (高勝率74.3%)
• 二策略確認 (中等勝率71.0%)

⚠️ 風險提醒：
• 系統提供的是輔助分析，不是投資建議
• 請結合基本面分析和風險管理
• 建議設定適當的停損停利點
• 投資有風險，請謹慎決策
        """

        QMessageBox.information(self, "🔍 系統說明", help_text)

    def show_usage_guide(self):
        """顯示使用指南"""
        help_text = """
📖 使用指南

🚀 基本操作流程：
1. 設定分析參數（股票數量、成功率等）
2. 點擊「🚀 開始分析」
3. 等待分析完成，系統會自動：
   • 分析歷史數據發現交易規則
   • 生成交易口訣
   • 掃描當前符合口訣的股票
4. 查看「📈 股票信號掃描」標籤頁
5. 在「🟢 買入口訣」和「🔴 賣出口訣」標籤頁勾選想要的策略
6. 查看「📋 符合條件股票清單」的篩選結果
7. 根據成功率和信號強度選擇股票
8. 人工確認技術面和基本面
9. 執行交易決策

🎯 GUI標籤頁功能：
1. 📋 交易規則 - 發現的所有交易規則
2. 🟢 買入口訣 - 11條買入策略組合口訣 + 勾選框
3. 🔴 賣出口訣 - 11條賣出策略組合口訣 + 勾選框
4. ⚠️ 風險控制 - 風險管理口訣
5. 📝 詳細日誌 - 分析過程日誌
6. 📈 股票信號掃描 - 所有股票的信號掃描
7. 📋 符合條件股票清單 - 根據勾選口訣篩選的股票

🔧 勾選框功能：
• 每個口訣都有勾選框，預設全部勾選
• 取消勾選可停用該口訣的股票篩選
• 勾選狀態變更會即時更新股票清單
• 可以靈活組合不同策略的口訣
        """

        QMessageBox.information(self, "📖 使用指南", help_text)

    def show_strategy_help(self):
        """顯示策略說明"""
        help_text = """
🎭 策略說明

📊 策略組合架構：
• 四策略組合: 1種 (RSI+MACD+BB+MA) - 最高勝率77.5%
• 三策略組合: 4種 - 高勝率74.3%
• 二策略組合: 6種 - 中等勝率71.0%

🟢 買入口訣範例：
1. 【RSI+MACD+BB+MA】RSI超賣反彈配MACD金叉，布林下軌反彈加MA金叉四重確認買 (77.5%)
2. 【RSI+MACD+MA】RSI超賣反彈配MACD金叉，MA金叉三重確認買 (74.3%)
3. 【MACD+BB+MA】MACD金叉配布林下軌反彈，MA金叉三重確認買 (73.7%)
4. 【MACD+MA】MACD金叉配MA金叉，雙重確認買 (71.0%)

🔴 賣出口訣範例：
1. 【RSI+MACD+BB+MA】RSI超買回落配MACD死叉，布林上軌回落加MA死叉四重確認賣 (77.5%)
2. 【RSI+MACD+MA】RSI超買回落配MACD死叉，MA死叉三重確認賣 (74.3%)
3. 【MACD+BB+MA】MACD死叉配布林上軌回落，MA死叉三重確認賣 (73.7%)
4. 【MACD+MA】MACD死叉配MA死叉，雙重確認賣 (71.0%)

📈 技術指標說明：
• RSI: 相對強弱指標，判斷超買超賣
• MACD: 指數平滑移動平均線，判斷趨勢變化
• BB: 布林通道，判斷價格波動範圍
• MA: 移動平均線，判斷趨勢方向

🎯 策略選擇建議：
• 保守型：優先使用四策略確認 (勝率最高77.5%)
• 積極型：使用三策略確認 (平衡勝率和信號數量)
• 靈活型：根據市場情況選擇不同策略組合
        """

        QMessageBox.information(self, "🎭 策略說明", help_text)

    def on_mantra_checkbox_changed(self, category: str, row: int, state: int):
        """處理口訣勾選框狀態變更"""
        enabled = state == 2  # Qt.CheckState.Checked
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {category}口訣第{row+1}行 {'啟用' if enabled else '停用'}")

        # 如果有啟用的口訣，觸發股票篩選
        if enabled:
            self.filter_stocks_by_selected_mantras()

    def filter_stocks_by_selected_mantras(self):
        """根據選中的口訣篩選股票"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 開始根據勾選口訣篩選股票...")

            # 獲取啟用的口訣
            enabled_mantras = self.get_enabled_mantras()

            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 找到 {len(enabled_mantras)} 個啟用的口訣")

            if not enabled_mantras:
                self.filtered_stocks_table.setRowCount(0)
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 沒有啟用的口訣，清空股票清單")
                return

            # 導入掃描器
            from stock_signal_scanner import StockSignalScanner

            scanner = StockSignalScanner()

            # 掃描符合條件的股票
            all_signals = []
            for i, mantra in enumerate(enabled_mantras):
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 掃描口訣 {i+1}/{len(enabled_mantras)}: {mantra['mantra_text'][:50]}...")
                signals = scanner.scan_stocks_by_mantra(mantra)
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 該口訣找到 {len(signals)} 個信號")
                all_signals.extend(signals)

            # 顯示篩選結果
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 總共找到 {len(all_signals)} 個符合條件的股票信號")
            self.display_filtered_stocks(all_signals)

        except Exception as e:
            import traceback
            error_msg = f"股票篩選失敗: {str(e)}\n{traceback.format_exc()}"
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {error_msg}")

    def get_enabled_mantras(self) -> List[Dict]:
        """獲取啟用的口訣"""
        enabled_mantras = []

        # 檢查買入口訣
        for row in range(self.buy_mantras_table.rowCount()):
            checkbox = self.buy_mantras_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                mantra_text = self.buy_mantras_table.item(row, 1).text()
                success_rate = float(self.buy_mantras_table.item(row, 2).text().rstrip('%')) / 100
                avg_profit = float(self.buy_mantras_table.item(row, 3).text().rstrip('%')) / 100

                enabled_mantras.append({
                    'category': 'buy',
                    'mantra_text': mantra_text,
                    'success_rate': success_rate,
                    'avg_profit': avg_profit
                })

        # 檢查賣出口訣
        for row in range(self.sell_mantras_table.rowCount()):
            checkbox = self.sell_mantras_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                mantra_text = self.sell_mantras_table.item(row, 1).text()
                success_rate = float(self.sell_mantras_table.item(row, 2).text().rstrip('%')) / 100
                avg_profit = float(self.sell_mantras_table.item(row, 3).text().rstrip('%')) / 100

                enabled_mantras.append({
                    'category': 'sell',
                    'mantra_text': mantra_text,
                    'success_rate': success_rate,
                    'avg_profit': avg_profit
                })

        return enabled_mantras

    def display_filtered_stocks(self, signals: List):
        """顯示篩選後的股票"""
        if not signals:
            self.filtered_stocks_table.setRowCount(0)
            self.filtered_stocks_table.setColumnCount(0)
            return

        # 設置表格
        headers = ['股票代碼', '股票名稱', '信號類型', '口訣', '成功率', '平均獲利',
                  '當前價格', '信號強度', '信號日期']

        self.filtered_stocks_table.setColumnCount(len(headers))
        self.filtered_stocks_table.setHorizontalHeaderLabels(headers)
        self.filtered_stocks_table.setRowCount(len(signals))

        # 填充數據
        for row, signal in enumerate(signals):
            self.filtered_stocks_table.setItem(row, 0, QTableWidgetItem(signal.stock_id))
            self.filtered_stocks_table.setItem(row, 1, QTableWidgetItem(signal.stock_name))
            self.filtered_stocks_table.setItem(row, 2, QTableWidgetItem("買入" if "買" in signal.mantra else "賣出"))
            self.filtered_stocks_table.setItem(row, 3, QTableWidgetItem(signal.mantra))
            self.filtered_stocks_table.setItem(row, 4, QTableWidgetItem(f"{signal.success_rate:.1%}"))
            self.filtered_stocks_table.setItem(row, 5, QTableWidgetItem(f"{signal.avg_profit:.1%}"))
            self.filtered_stocks_table.setItem(row, 6, QTableWidgetItem(f"{signal.current_price:.2f}"))
            self.filtered_stocks_table.setItem(row, 7, QTableWidgetItem(f"{signal.signal_strength:.1%}"))
            self.filtered_stocks_table.setItem(row, 8, QTableWidgetItem(signal.signal_date))

        # 調整列寬
        self.filtered_stocks_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # 設置排序
        self.filtered_stocks_table.setSortingEnabled(True)

        # 預設按成功率排序
        self.filtered_stocks_table.sortItems(4, Qt.SortOrder.DescendingOrder)

        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 篩選完成，找到 {len(signals)} 檔符合條件的股票")

def main():
    """主函數"""
    app = QApplication(sys.argv)

    # 設置應用程式屬性
    app.setApplicationName("自動化交易規則發現工具")
    app.setApplicationVersion("1.0")

    # 創建並顯示主視窗
    window = AutoRuleDiscoveryGUI()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
