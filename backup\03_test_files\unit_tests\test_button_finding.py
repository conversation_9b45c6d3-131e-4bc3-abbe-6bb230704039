#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試按鈕查找邏輯
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def test_button_finding():
    """測試按鈕查找"""
    driver = None
    try:
        print("🚀 啟動Chrome瀏覽器...")
        
        # 設置Chrome選項
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 設置下載目錄
        prefs = {
            "download.default_directory": "C:/Users/<USER>/Downloads",
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=options)
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        
        driver.get(url)
        
        # 等待頁面載入
        print("⏳ 等待頁面載入...")
        time.sleep(10)
        
        # 模擬下載器的按鈕查找邏輯
        print("\n🔍 開始查找按鈕...")
        
        # 列出所有input元素
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"📋 找到 {len(all_inputs)} 個input元素")
        
        xls_button = None
        
        for i, input_elem in enumerate(all_inputs):
            try:
                input_type = input_elem.get_attribute("type")
                input_value = input_elem.get_attribute("value")
                input_onclick = input_elem.get_attribute("onclick")
                is_displayed = input_elem.is_displayed()
                is_enabled = input_elem.is_enabled()
                
                print(f"   Input {i+1}: type='{input_type}', value='{input_value}'")
                print(f"            displayed={is_displayed}, enabled={is_enabled}")
                if input_onclick:
                    print(f"            onclick='{input_onclick[:100]}...'")
                
                # 尋找XLS相關按鈕
                if input_value and ("XLS" in input_value or "匯出" in input_value):
                    if is_displayed and is_enabled:
                        print(f"✅ 找到可用的匯出按鈕: {input_value}")
                        xls_button = input_elem
                        break
                    else:
                        print(f"⚠️ 找到按鈕但不可用: {input_value}")
                
            except Exception as e:
                print(f"   Input {i+1}: 讀取失敗 - {e}")
                continue
        
        if xls_button:
            print(f"\n🎯 準備點擊XLS按鈕...")
            
            # 嘗試點擊按鈕
            try:
                # 滾動到按鈕位置
                driver.execute_script("arguments[0].scrollIntoView();", xls_button)
                time.sleep(1)
                
                # 點擊按鈕
                xls_button.click()
                print("✅ 按鈕點擊成功")
                
                # 等待下載開始
                print("⏳ 等待下載開始...")
                time.sleep(5)
                
                print("🎉 測試完成！請檢查下載目錄是否有新文件")
                
            except Exception as e:
                print(f"❌ 點擊按鈕失敗: {e}")
                
        else:
            print("❌ 未找到可用的XLS按鈕")
            
            # 嘗試XPath選擇器
            print("\n🔍 嘗試XPath選擇器...")
            selectors = [
                '//input[@value="XLS"]',
                '//input[contains(@value, "XLS")]',
                '//input[contains(@onclick, "export2xls")]'
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✅ XPath找到按鈕: {selector}")
                        for elem in elements:
                            value = elem.get_attribute("value")
                            onclick = elem.get_attribute("onclick")
                            print(f"   按鈕值: {value}, 點擊事件: {onclick[:50]}...")
                            
                            if elem.is_displayed() and elem.is_enabled():
                                print("   按鈕可用，嘗試點擊...")
                                elem.click()
                                time.sleep(5)
                                print("✅ XPath按鈕點擊成功")
                                return True
                    else:
                        print(f"❌ XPath未找到: {selector}")
                except Exception as e:
                    print(f"❌ XPath選擇器錯誤: {selector} - {e}")
        
        return xls_button is not None
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        if driver:
            input("按Enter鍵關閉瀏覽器...")
            driver.quit()

if __name__ == "__main__":
    test_button_finding()
