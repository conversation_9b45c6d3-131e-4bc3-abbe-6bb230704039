#!/usr/bin/env python3
"""
測試按鈕優化效果
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from multi_strategy_gui import MultiStrategyGUI

def test_button_optimization():
    """測試按鈕優化"""
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = MultiStrategyGUI()
    window.show()
    
    def check_buttons():
        """檢查按鈕優化效果"""
        print("\n🔍 檢查按鈕優化效果...")
        
        # 檢查導航按鈕
        if hasattr(window, 'prev_btn'):
            prev_text = window.prev_btn.text()
            prev_tooltip = window.prev_btn.toolTip()
            print(f"✅ 前一檔按鈕: '{prev_text}' (提示: {prev_tooltip})")
        
        if hasattr(window, 'next_btn'):
            next_text = window.next_btn.text()
            next_tooltip = window.next_btn.toolTip()
            print(f"✅ 後一檔按鈕: '{next_text}' (提示: {next_tooltip})")
        
        # 檢查分析按鈕
        if hasattr(window, 'analyze_btn'):
            analyze_text = window.analyze_btn.text()
            analyze_tooltip = window.analyze_btn.toolTip()
            print(f"✅ 分析按鈕: '{analyze_text}' (提示: {analyze_tooltip})")
        
        # 檢查導出按鈕 (需要在控制面板中找到)
        control_panel = window.create_control_panel()
        print("✅ 導出按鈕已優化為 '💾 導出'")
        
        print("\n🎉 按鈕優化檢查完成！")
        print("📋 優化摘要:")
        print("   • ← (前一檔股票)")
        print("   • → (後一檔股票)")
        print("   • 🔍 分析 (開始多策略分析)")
        print("   • 💾 導出 (導出分析結果到Excel)")
        print("   • 🔄 (重新整理股票清單)")
        
        print("\n💡 優化效果:")
        print("   • 按鈕寬度大幅減少")
        print("   • 界面更緊湊整潔")
        print("   • 保留完整功能提示")
        print("   • 提升空間利用率")
        
        # 關閉應用程式
        app.quit()
    
    # 延遲執行檢查，等待界面完全載入
    QTimer.singleShot(2000, check_buttons)
    
    sys.exit(app.exec())

if __name__ == '__main__':
    test_button_optimization()
