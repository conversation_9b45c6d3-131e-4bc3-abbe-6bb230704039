#!/usr/bin/env python3
"""
盤中監控模組
整合5分鐘數據，提供即時策略信號監控
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from threading import Thread, Event
import queue
from intraday_data_fetcher import get_intraday_data, get_market_status

# 🛡️ 導入強化Yahoo爬蟲
try:
    from yahoo_realtime_crawler import YahooRealtimeCrawler
    YAHOO_CRAWLER_AVAILABLE = True
    logging.info("✅ Yahoo即時報價爬蟲已載入到盤中監控")
except ImportError:
    YAHOO_CRAWLER_AVAILABLE = False
    logging.warning("⚠️ Yahoo即時報價爬蟲不可用")

class IntradayMonitor:
    """盤中監控器"""
    
    def __init__(self, stock_list=None, update_interval=60):
        """
        初始化盤中監控器

        Args:
            stock_list: 監控的股票列表
            update_interval: 更新間隔（秒）
        """
        self.stock_list = stock_list or ['2330', '2317', '2454', '3008', '2412']
        self.update_interval = update_interval

        # 監控狀態
        self.is_monitoring = False
        self.stop_event = Event()
        self.monitor_thread = None

        # 數據存儲
        self.current_data = {}
        self.signal_history = {}
        self.alerts = queue.Queue()

        # 🛡️ 初始化Yahoo爬蟲（優先使用，無API限制）
        if YAHOO_CRAWLER_AVAILABLE:
            self.yahoo_crawler = YahooRealtimeCrawler()
            self.use_yahoo_crawler = True
            logging.info("✅ 盤中監控將優先使用Yahoo爬蟲（無API限制）")
        else:
            self.yahoo_crawler = None
            self.use_yahoo_crawler = False
            logging.warning("⚠️ Yahoo爬蟲不可用，將使用傳統數據源")

        # 策略配置
        self.strategies = {
            '突破監控': self._check_breakout_signals,
            '量價異常': self._check_volume_price_signals,
            '技術指標': self._check_technical_signals,
            '支撐阻力': self._check_support_resistance
        }

        logging.info(f"📊 盤中監控器初始化完成，監控 {len(self.stock_list)} 支股票")
    
    def start_monitoring(self):
        """開始監控"""
        if self.is_monitoring:
            logging.warning("⚠️ 監控已在運行中")
            return
        
        # 檢查市場狀態
        market_status = get_market_status()
        if not market_status['is_open']:
            logging.warning(f"⚠️ 市場未開盤，狀態: {market_status['status']}")
            return False
        
        self.is_monitoring = True
        self.stop_event.clear()
        
        # 啟動監控線程
        self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logging.info("🚀 盤中監控已啟動")
        return True
    
    def stop_monitoring(self):
        """停止監控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logging.info("⏹️ 盤中監控已停止")
    
    def _monitor_loop(self):
        """監控主循環"""
        while not self.stop_event.is_set():
            try:
                # 檢查市場狀態
                market_status = get_market_status()
                if not market_status['is_open']:
                    logging.info(f"📴 市場已收盤，停止監控")
                    break
                
                # 更新數據並檢查信號
                self._update_all_data()
                self._check_all_signals()
                
                # 等待下次更新
                if not self.stop_event.wait(self.update_interval):
                    continue
                else:
                    break
                    
            except Exception as e:
                logging.error(f"❌ 監控循環錯誤: {e}")
                time.sleep(10)  # 錯誤後等待10秒再繼續
        
        self.is_monitoring = False
    
    def _update_all_data(self):
        """更新所有股票數據 - 優先使用Yahoo爬蟲"""
        logging.info(f"🔄 更新 {len(self.stock_list)} 支股票的盤中數據...")

        if self.use_yahoo_crawler and self.yahoo_crawler:
            # 🛡️ 使用Yahoo爬蟲批量獲取（無API限制）
            try:
                logging.info("🛡️ 使用Yahoo爬蟲批量獲取即時數據...")
                crawler_results = self.yahoo_crawler.get_multiple_stocks_data(self.stock_list)

                for stock_id, df in crawler_results.items():
                    if not df.empty:
                        # 轉換為5分鐘格式並計算技術指標
                        df_processed = self._process_realtime_to_intraday(df)
                        if not df_processed.empty:
                            df_processed = self._calculate_intraday_indicators(df_processed)
                            self.current_data[stock_id] = df_processed
                            logging.debug(f"✅ {stock_id} Yahoo爬蟲數據更新完成")
                        else:
                            logging.warning(f"⚠️ {stock_id} 數據處理失敗")
                    else:
                        logging.warning(f"⚠️ {stock_id} Yahoo爬蟲數據獲取失敗")

                # 對於Yahoo爬蟲失敗的股票，使用傳統方法
                failed_stocks = [s for s in self.stock_list if s not in crawler_results or crawler_results[s].empty]
                if failed_stocks:
                    logging.info(f"🔄 使用傳統方法獲取失敗股票: {failed_stocks}")
                    self._update_data_traditional(failed_stocks)

            except Exception as e:
                logging.error(f"❌ Yahoo爬蟲批量獲取失敗: {e}")
                # 回退到傳統方法
                self._update_data_traditional(self.stock_list)
        else:
            # 使用傳統方法
            self._update_data_traditional(self.stock_list)

    def _update_data_traditional(self, stock_list):
        """使用傳統方法更新數據"""
        for stock_id in stock_list:
            try:
                # 獲取5分鐘數據
                df = get_intraday_data(stock_id, '5m')

                if not df.empty:
                    # 計算技術指標
                    df = self._calculate_intraday_indicators(df)
                    self.current_data[stock_id] = df

                    logging.debug(f"✅ {stock_id} 傳統方法數據更新完成 ({len(df)} 筆)")
                else:
                    logging.warning(f"⚠️ {stock_id} 傳統方法數據獲取失敗")

                # 避免請求過快
                time.sleep(0.2)

            except Exception as e:
                logging.error(f"❌ {stock_id} 傳統方法數據更新錯誤: {e}")

    def _process_realtime_to_intraday(self, realtime_df):
        """將即時數據轉換為盤中格式"""
        try:
            if realtime_df.empty:
                return pd.DataFrame()

            # 取最新的即時數據
            latest = realtime_df.iloc[-1]

            # 轉換為標準盤中格式
            intraday_data = {
                'datetime': latest['datetime'],
                'open': latest['open'],
                'high': latest['high'],
                'low': latest['low'],
                'close': latest['close'],
                'volume': latest['volume']
            }

            return pd.DataFrame([intraday_data])

        except Exception as e:
            logging.error(f"❌ 即時數據轉換失敗: {e}")
            return pd.DataFrame()
    
    def _calculate_intraday_indicators(self, df):
        """計算盤中技術指標"""
        if len(df) < 2:
            return df
        
        # 移動平均線
        for period in [5, 10, 20]:
            if len(df) >= period:
                df[f'MA{period}'] = df['close'].rolling(window=period).mean()
        
        # RSI
        if len(df) >= 14:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            df['RSI'] = 100 - (100 / (1 + rs))
        
        # 成交量移動平均
        if len(df) >= 10:
            df['Volume_MA'] = df['volume'].rolling(window=10).mean()
            df['Volume_Ratio'] = df['volume'] / df['Volume_MA']
        
        # 價格變化率
        df['Price_Change'] = df['close'].pct_change()
        df['Price_Change_5m'] = df['close'].pct_change(periods=5)
        
        return df
    
    def _check_all_signals(self):
        """檢查所有策略信號"""
        for stock_id, df in self.current_data.items():
            if df.empty or len(df) < 10:
                continue
            
            for strategy_name, strategy_func in self.strategies.items():
                try:
                    signals = strategy_func(stock_id, df)
                    
                    if signals:
                        # 記錄信號
                        timestamp = datetime.now()
                        for signal in signals:
                            signal['timestamp'] = timestamp
                            signal['stock_id'] = stock_id
                            signal['strategy'] = strategy_name
                            
                            # 添加到警報隊列
                            self.alerts.put(signal)
                            
                            # 記錄到歷史
                            if stock_id not in self.signal_history:
                                self.signal_history[stock_id] = []
                            self.signal_history[stock_id].append(signal)
                            
                            logging.info(f"🚨 {strategy_name} 信號: {stock_id} - {signal['message']}")
                
                except Exception as e:
                    logging.error(f"❌ {strategy_name} 策略檢查錯誤: {e}")
    
    def _check_breakout_signals(self, stock_id, df):
        """檢查突破信號"""
        signals = []
        
        if len(df) < 20:
            return signals
        
        latest = df.iloc[-1]
        
        # 檢查價格突破
        if 'MA20' in df.columns:
            ma20 = latest['MA20']
            current_price = latest['close']
            
            # 向上突破MA20
            if current_price > ma20 * 1.02:  # 突破2%
                prev_price = df.iloc[-2]['close']
                if prev_price <= df.iloc[-2]['MA20']:  # 前一根K線在MA20下方
                    signals.append({
                        'type': 'breakout_up',
                        'message': f"突破MA20 {current_price:.2f} > {ma20:.2f}",
                        'price': current_price,
                        'strength': 'medium'
                    })
        
        # 檢查成交量突破
        if 'Volume_Ratio' in df.columns:
            volume_ratio = latest['Volume_Ratio']
            if volume_ratio > 3.0:  # 成交量爆量3倍
                signals.append({
                    'type': 'volume_breakout',
                    'message': f"成交量爆量 {volume_ratio:.1f}倍",
                    'price': latest['close'],
                    'strength': 'high'
                })
        
        return signals
    
    def _check_volume_price_signals(self, stock_id, df):
        """檢查量價異常信號"""
        signals = []
        
        if len(df) < 5:
            return signals
        
        latest = df.iloc[-1]
        
        # 量價背離
        price_change_5m = latest.get('Price_Change_5m', 0)
        volume_ratio = latest.get('Volume_Ratio', 1)
        
        # 價漲量縮
        if price_change_5m > 0.02 and volume_ratio < 0.5:
            signals.append({
                'type': 'price_up_volume_down',
                'message': f"價漲量縮 價格+{price_change_5m*100:.1f}% 量縮{(1-volume_ratio)*100:.0f}%",
                'price': latest['close'],
                'strength': 'medium'
            })
        
        # 價跌量增
        elif price_change_5m < -0.02 and volume_ratio > 2.0:
            signals.append({
                'type': 'price_down_volume_up',
                'message': f"價跌量增 價格{price_change_5m*100:.1f}% 量增{(volume_ratio-1)*100:.0f}%",
                'price': latest['close'],
                'strength': 'high'
            })
        
        return signals
    
    def _check_technical_signals(self, stock_id, df):
        """檢查技術指標信號"""
        signals = []
        
        if len(df) < 14:
            return signals
        
        latest = df.iloc[-1]
        
        # RSI超買超賣
        if 'RSI' in df.columns:
            rsi = latest['RSI']
            
            if rsi > 80:
                signals.append({
                    'type': 'rsi_overbought',
                    'message': f"RSI超買 {rsi:.1f}",
                    'price': latest['close'],
                    'strength': 'medium'
                })
            elif rsi < 20:
                signals.append({
                    'type': 'rsi_oversold',
                    'message': f"RSI超賣 {rsi:.1f}",
                    'price': latest['close'],
                    'strength': 'medium'
                })
        
        return signals
    
    def _check_support_resistance(self, stock_id, df):
        """檢查支撐阻力信號"""
        signals = []
        
        if len(df) < 20:
            return signals
        
        # 簡化的支撐阻力檢查
        recent_high = df['high'].tail(20).max()
        recent_low = df['low'].tail(20).min()
        current_price = df.iloc[-1]['close']
        
        # 接近阻力位
        if current_price > recent_high * 0.98:
            signals.append({
                'type': 'near_resistance',
                'message': f"接近阻力位 {current_price:.2f} 近 {recent_high:.2f}",
                'price': current_price,
                'strength': 'low'
            })
        
        # 接近支撐位
        elif current_price < recent_low * 1.02:
            signals.append({
                'type': 'near_support',
                'message': f"接近支撐位 {current_price:.2f} 近 {recent_low:.2f}",
                'price': current_price,
                'strength': 'low'
            })
        
        return signals
    
    def get_latest_alerts(self, max_count=10):
        """獲取最新警報"""
        alerts = []
        count = 0
        
        while not self.alerts.empty() and count < max_count:
            try:
                alert = self.alerts.get_nowait()
                alerts.append(alert)
                count += 1
            except queue.Empty:
                break
        
        return alerts
    
    def get_current_status(self):
        """獲取當前監控狀態"""
        return {
            'is_monitoring': self.is_monitoring,
            'stock_count': len(self.stock_list),
            'data_count': len(self.current_data),
            'alert_count': self.alerts.qsize(),
            'market_status': get_market_status()
        }

# 創建全局實例
intraday_monitor = IntradayMonitor()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試盤中監控功能...")
    
    # 創建監控器
    monitor = IntradayMonitor(['2330', '2317'], update_interval=30)
    
    # 檢查市場狀態
    status = monitor.get_current_status()
    print(f"📊 市場狀態: {status['market_status']}")
    
    if status['market_status']['is_open']:
        print("🚀 開始監控...")
        monitor.start_monitoring()
        
        # 運行1分鐘
        time.sleep(60)
        
        # 檢查警報
        alerts = monitor.get_latest_alerts()
        print(f"🚨 收到 {len(alerts)} 個警報")
        
        for alert in alerts:
            print(f"   {alert['stock_id']} - {alert['message']}")
        
        monitor.stop_monitoring()
    else:
        print("📴 市場未開盤，無法測試監控功能")
    
    print("🎉 測試完成！")
