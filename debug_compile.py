#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試編譯腳本 - 創建帶有調試信息的版本
"""

import os
import sys
import subprocess
import shutil
import time

def create_debug_spec():
    """創建調試版的 spec 文件"""
    print("📝 創建調試編譯配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 隱藏導入 - 只包含最基本的模組
hiddenimports = [
    # 系統核心模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6 最小集合
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 數據處理最小集合
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    
    # 網路最小集合
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    
    # 其他必要
    'setuptools',
    'pkg_resources',
]

# 排除所有可能產生錯誤的模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    
    # 金融數據模組
    'twstock',
    'twstock.codes',
    'twstock.stock',
    'yfinance',
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    
    # 圖表模組
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    'pyqtgraph',
    'pyqtgraph.Qt',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    
    # 其他可能有問題的模組
    'xlsxwriter',
    'selenium',
    'webdriver_manager',
    'apscheduler',
    'apscheduler.schedulers',
    'apscheduler.schedulers.background',
    
    # 自定義模組
    'charts',
    'charts.candlestick',
    'config',
    'config.strategy_config',
    'enhanced_dividend_crawler',
    'integrated_strategy_help_dialog',
    'unified_monitor_manager',
    'smart_trading_strategies',
    'strategies',
    'strategies.triple_rsi_strategy',
    'strategies.small_investor_strategy',
    'strategies.second_high_strategy',
    'strategies.timid_cat_strategy',
    'strategies.canslim_strategy',
    'strategies.intraday_strategy',
    'monitoring',
    'monitoring.pre_market_monitor',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StockAnalyzer_Debug',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 啟用控制台以顯示調試信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('debug_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 調試編譯配置已創建")

def clean_and_compile():
    """清理並編譯"""
    print("🧹 清理編譯環境...")
    
    # 清理目錄
    if os.path.exists('build'):
        try:
            shutil.rmtree('build')
            print("✅ 清理 build")
        except Exception as e:
            print(f"⚠️ 無法清理 build: {e}")
    
    time.sleep(3)
    
    print("🔨 開始調試編譯...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'debug_compile.spec'
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/StockAnalyzer_Debug.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_debug_launcher()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            if result.stderr:
                print("錯誤輸出:")
                print(result.stderr[-1500:])
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_debug_launcher():
    """創建調試版啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 調試版

echo.
echo ========================================
echo    🔍 台股智能選股系統 - 調試版 🔍
echo ========================================
echo.

if exist "dist\\StockAnalyzer_Debug.exe" (
    echo ✅ 找到調試版
    echo 🔍 正在啟動調試模式...
    echo.
    echo 💡 調試版特點：
    echo    ✓ 顯示詳細的啟動信息
    echo    ✓ 顯示所有錯誤和警告
    echo    ✓ 幫助診斷問題
    echo    ✓ 控制台視窗會保持開啟
    echo.
    
    cd /d "dist"
    echo 🚀 啟動程式...
    "StockAnalyzer_Debug.exe"
    
    echo.
    echo 📋 如果程式沒有啟動，請檢查上方的錯誤信息
    echo.
    
) else (
    echo ❌ 錯誤：找不到調試版
    echo.
    echo 請重新編譯：
    echo    python debug_compile.py
    echo.
)

echo 按任意鍵退出...
pause >nul
'''
    
    with open('啟動調試版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建調試版啟動腳本: 啟動調試版.bat")

def main():
    """主函數"""
    print("🔍 台股智能選股系統 - 調試編譯器")
    print("=" * 60)
    print("目標：創建帶有調試信息的版本來診斷問題")
    print()
    
    # 步驟1: 創建配置
    create_debug_spec()
    print()
    
    # 步驟2: 清理並編譯
    if clean_and_compile():
        print("\n🎉 調試版編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/StockAnalyzer_Debug.exe")
        print("   - 啟動調試版.bat")
        print("\n🔍 使用方法:")
        print("   雙擊執行: 啟動調試版.bat")
        print("\n✨ 調試版特點:")
        print("   ✓ 顯示詳細的啟動信息")
        print("   ✓ 顯示所有錯誤和警告")
        print("   ✓ 幫助診斷啟動問題")
        print("   ✓ 控制台視窗保持開啟")
        print("\n🎯 這將幫助我們找出問題所在！")
        return True
    else:
        print("\n❌ 編譯失敗！")
        return False

if __name__ == "__main__":
    main()
