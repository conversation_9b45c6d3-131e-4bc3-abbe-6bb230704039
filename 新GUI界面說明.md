# Finlab 增量更新GUI - 新界面設計

## 🎯 按照您的要求重新設計

### ✅ 已實現的功能

1. **顯示檔案名稱**：直接顯示 `.pkl` 檔案名稱（如 `price.pkl`）
2. **顯示更新日期範圍**：從最後日期+1天到今天
3. **移除狀態和操作欄位**：簡化界面
4. **每個檔案獨立更新按鈕**：每行都有自己的更新按鈕

## 📊 新界面佈局

```
┌─────────────────────────────────────────────────────────────────┐
│                Finlab 爬蟲系統 - 增量更新版                        │
├─────────────────────────────────────────────────────────────────┤
│  資料檔案狀態                                                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ 檔案名稱          │ 最後日期   │ 建議更新範圍        │ 更新 │ │
│  ├─────────────────────────────────────────────────────────────┤ │
│  │ price.pkl         │ 2025-07-13 │ 2025-07-14至2025-07-20│[更新]│ │
│  │ bargin_report.pkl │ 2025-07-13 │ 2025-07-14至2025-07-20│[更新]│ │
│  │ pe.pkl            │ 2025-07-13 │ 2025-07-14至2025-07-20│[更新]│ │
│  │ monthly_report.pkl│ 2025-07-13 │ 2025-07-14至2025-07-20│[更新]│ │
│  │ balance_sheet.pkl │ 檔案不存在 │ 2023-01-01至2025-07-20│[更新]│ │
│  │ income_sheet.pkl  │ 檔案不存在 │ 2023-01-01至2025-07-20│[更新]│ │
│  │ cash_flows.pkl    │ 檔案不存在 │ 2023-01-01至2025-07-20│[更新]│ │
│  └─────────────────────────────────────────────────────────────┘ │
│  [全部更新] [重新檢查]                                            │
├─────────────────────────────────────────────────────────────────┤
│  執行日誌                                                        │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │[16:30:15] Finlab 爬蟲系統 - 增量更新版已啟動                  │ │
│  │[16:30:16] 檢測到 price.pkl，最後日期：2025-07-13             │ │
│  │[16:30:16] 建議更新範圍：2025-07-14 至 2025-07-20             │ │
│  │[16:30:20] 開始更新 price.pkl (2025-07-14 至 2025-07-20)     │ │
│  │[16:30:25] ✓ price.pkl 更新完成，獲取 7 筆資料                │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  [清除日誌]                                                      │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 界面特點

### 表格設計
- **檔案名稱**：顯示實際的 `.pkl` 檔案名稱
- **最後日期**：檔案中資料的最後日期
- **建議更新範圍**：從最後日期+1天到今天的日期範圍
- **更新按鈕**：每個檔案都有獨立的更新按鈕

### 支援的檔案
```
price.pkl              - 股價資料
bargin_report.pkl       - 融資融券
pe.pkl                  - 本益比
monthly_report.pkl      - 月報
balance_sheet.pkl       - 資產負債表
income_sheet.pkl        - 損益表
cash_flows.pkl          - 現金流量表
twse_divide_ratio.pkl   - 上市除權息
otc_divide_ratio.pkl    - 上櫃除權息
twse_cap_reduction.pkl  - 上市減資
otc_cap_reduction.pkl   - 上櫃減資
```

## 🎮 操作方式

### 1. 單個檔案更新
- 點擊任一行的「更新」按鈕
- 系統會自動計算該檔案的更新範圍
- 只爬取缺失的日期資料

### 2. 批量更新
- 點擊「全部更新」按鈕
- 系統會逐個更新所有檔案
- 每個檔案都使用各自的更新範圍

### 3. 重新檢查
- 點擊「重新檢查」按鈕
- 重新掃描檔案狀態
- 更新表格顯示

## 📝 更新邏輯示例

### 現有檔案更新
```
檔案：price.pkl
最後日期：2025-07-13
建議範圍：2025-07-14 至 2025-07-20
動作：只爬取 7 天的缺失資料
```

### 新檔案創建
```
檔案：balance_sheet.pkl
狀態：檔案不存在
建議範圍：2023-01-01 至 2025-07-20
動作：完整爬取所有資料
```

## 🚀 技術實現

### 表格創建
```python
# 創建表格標題
headers = ['檔案名稱', '最後日期', '建議更新範圍', '更新']

# 為每個檔案創建一行
for filename, info in self.data_info.items():
    # 檔案名稱
    file_label = ttk.Label(text=filename)
    
    # 最後日期
    date_label = ttk.Label(text=info['last_date'])
    
    # 建議更新範圍
    range_label = ttk.Label(text=update_range)
    
    # 獨立更新按鈕
    update_btn = ttk.Button(text="更新", 
                          command=lambda f=filename: self.update_single_data(f))
```

### 更新範圍計算
```python
if info['exists'] and info['last_date'] != "檔案不存在":
    # 現有檔案：從最後日期+1天開始
    last_date = datetime.strptime(info['last_date'], '%Y-%m-%d')
    next_date = (last_date + timedelta(days=1)).strftime('%Y-%m-%d')
    update_range = f"{next_date} 至 {today}"
else:
    # 新檔案：從2023年開始
    update_range = f"2023-01-01 至 {today}"
```

## ✨ 優勢

1. **清楚明確**：直接顯示檔案名稱，不會混淆
2. **操作簡單**：每個檔案都有獨立的更新按鈕
3. **範圍明確**：清楚顯示每個檔案的更新日期範圍
4. **界面簡潔**：移除不必要的狀態和操作欄位
5. **獨立更新**：每個檔案的更新完全獨立

這個新設計完全符合您的要求：
- ✅ 顯示檔案名稱（.pkl檔案）
- ✅ 顯示更新日期範圍
- ✅ 移除狀態和操作欄位
- ✅ 每個檔案獨立更新按鈕

現在的GUI界面更加清楚和實用！
