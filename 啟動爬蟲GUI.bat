@echo off
chcp 65001 >nul
title Finlab 爬蟲系統 GUI 啟動器

echo.
echo ========================================
echo 🚀 Finlab 爬蟲系統 GUI 啟動器
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：未找到 Python
    echo 💡 請先安裝 Python 3.7 或更高版本
    echo 📥 下載地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python 已安裝
python --version

echo.
echo 🔄 正在啟動 GUI 界面...
echo.

REM 啟動GUI程式
python start_crawler_gui.py

REM 如果程式正常結束，顯示完成訊息
if errorlevel 0 (
    echo.
    echo ✅ 程式已正常結束
) else (
    echo.
    echo ❌ 程式執行時發生錯誤
)

echo.
pause
