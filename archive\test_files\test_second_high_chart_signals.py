#!/usr/bin/env python3
"""
測試二次創高策略K線圖信號功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_second_high_chart_signals():
    """測試二次創高策略K線圖信號功能"""
    print("🧪 測試二次創高策略K線圖信號功能")
    print("=" * 60)
    
    # 1. 測試信號檢測邏輯
    print("\n📊 1. 測試信號檢測邏輯")
    test_signal_detection()
    
    # 2. 測試K線圖標記
    print("\n🎨 2. 測試K線圖標記功能")
    test_chart_markers()
    
    # 3. 測試條件檢查
    print("\n🔍 3. 測試條件檢查功能")
    test_condition_check()
    
    print("\n🎉 二次創高策略K線圖信號功能測試完成！")

def test_signal_detection():
    """測試信號檢測邏輯"""
    print("   📈 測試二次創高買入信號檢測...")
    
    # 創建模擬數據
    dates = pd.date_range(start='2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # 模擬股價數據（創造二次創高模式）
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(200):
        if i < 50:
            # 第一階段：上漲到第一個高點
            price = base_price + i * 0.5 + np.random.normal(0, 1)
        elif i < 100:
            # 第二階段：整理期間
            price = base_price + 25 + np.random.normal(0, 2)
        else:
            # 第三階段：二次創高
            price = base_price + 25 + (i - 100) * 0.8 + np.random.normal(0, 1)
        
        prices.append(max(price, 50))  # 確保價格不會太低
        volumes.append(np.random.randint(100000, 1000000))
    
    # 創建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'Open': prices,
        'High': [p * (1 + np.random.uniform(0, 0.02)) for p in prices],
        'Low': [p * (1 - np.random.uniform(0, 0.02)) for p in prices],
        'Close': prices,
        'Volume': volumes
    })
    
    # 添加時間戳
    df['date_ts'] = df['date'].astype(np.int64) // 10**9
    
    print(f"   ✅ 創建了{len(df)}天的模擬數據")
    print(f"   📊 價格範圍: {df['Close'].min():.2f} - {df['Close'].max():.2f}")
    
    # 測試信號檢測
    buy_signals = []
    sell_signals = []
    
    # 計算技術指標
    df['MA20'] = df['Close'].rolling(20).mean()
    df['Volume_MA5'] = df['Volume'].rolling(5).mean()
    df['Volume_MA20'] = df['Volume'].rolling(20).mean()
    
    # 檢測信號
    for i in range(120, len(df)):
        # 檢查買入信號（簡化版）
        if check_simplified_buy_signal(df, i):
            buy_signals.append(i)
        
        # 檢查賣出信號
        if i > 0 and df['MA20'].iloc[i] < df['MA20'].iloc[i-1]:
            sell_signals.append(i)
    
    print(f"   🟢 檢測到 {len(buy_signals)} 個買入信號")
    print(f"   🔴 檢測到 {len(sell_signals)} 個賣出信號")
    
    if buy_signals:
        print(f"   📅 買入信號日期: {[df['date'].iloc[i].strftime('%Y-%m-%d') for i in buy_signals[:3]]}")
    
    return len(buy_signals) > 0 and len(sell_signals) > 0

def check_simplified_buy_signal(df, idx):
    """簡化版的二次創高買入信號檢查"""
    try:
        close = df['Close']
        
        # 條件1: 創60日新高
        newhigh_60 = close.rolling(60, min_periods=1).max()
        cond1 = close.iloc[idx] == newhigh_60.iloc[idx]
        
        # 條件2: 價格趨勢向上
        cond2 = close.iloc[idx] > close.iloc[idx-30] if idx >= 30 else False
        
        # 條件3: 成交量放大
        vol_ma5 = df['Volume'].rolling(5).mean().iloc[idx]
        vol_ma20 = df['Volume'].rolling(20).mean().iloc[idx]
        cond3 = vol_ma5 > vol_ma20
        
        return cond1 and cond2 and cond3
        
    except:
        return False

def test_chart_markers():
    """測試K線圖標記功能"""
    print("   🎨 測試買賣信號標記...")
    
    # 測試標記配置
    markers = {
        'buy_arrow': {
            'angle': 90,
            'color': 'green',
            'size': 15,
            'label': '二次創高買入'
        },
        'sell_arrow': {
            'angle': -90,
            'color': 'red', 
            'size': 15,
            'label': '20MA下彎賣出'
        }
    }
    
    print("   ✅ 買入標記配置:")
    print(f"      - 箭頭角度: {markers['buy_arrow']['angle']}°")
    print(f"      - 顏色: {markers['buy_arrow']['color']}")
    print(f"      - 標籤: {markers['buy_arrow']['label']}")
    
    print("   ✅ 賣出標記配置:")
    print(f"      - 箭頭角度: {markers['sell_arrow']['angle']}°")
    print(f"      - 顏色: {markers['sell_arrow']['color']}")
    print(f"      - 標籤: {markers['sell_arrow']['label']}")
    
    return True

def test_condition_check():
    """測試條件檢查功能"""
    print("   🔍 測試二次創高8個條件...")
    
    conditions = [
        "近日創新高：近一日收盤價創近60日新高",
        "前期非新高：前30日有至少一日未創新高",
        "早期創新高：第30-55日前有至少一日創60日新高",
        "價格突破：前30-55日最高價小於近日收盤價",
        "120日趨勢：近日收盤價大於120日前收盤價",
        "60日趨勢：近日收盤價大於60日前收盤價",
        "營收成長：近3月平均營收大於12月平均營收",
        "成交量確認：近5日平均成交量大於近20日平均成交量"
    ]
    
    print("   📋 二次創高策略完整條件列表:")
    for i, condition in enumerate(conditions, 1):
        print(f"      {i}. {condition}")
    
    print(f"\n   ⚡ 買入信號觸發條件: 所有{len(conditions)}個條件必須同時滿足")
    print("   🔴 賣出信號觸發條件: 20日均線開始下彎")
    
    return True

def test_strategy_summary():
    """測試策略總結"""
    print("\n📈 二次創高策略K線圖信號功能總結")
    print("-" * 50)
    
    features = [
        "✅ 自動檢測二次創高買入信號（8個條件）",
        "✅ 自動檢測20MA下彎賣出信號",
        "✅ 綠色向上箭頭標示買入點",
        "✅ 紅色向下箭頭標示賣出點",
        "✅ 信號標籤顯示策略名稱",
        "✅ 只顯示最近5個信號（避免圖表擁擠）",
        "✅ 狀態欄顯示信號統計",
        "✅ 支援歷史信號回顧"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print(f"\n🎯 策略特色:")
    print("   - 專門捕捉二次創高的強勢突破")
    print("   - 多重技術指標確認")
    print("   - 明確的進出場信號")
    print("   - 視覺化的K線圖標註")

if __name__ == "__main__":
    try:
        success = test_second_high_chart_signals()
        test_strategy_summary()
        
        print(f"\n🚀 測試結果: {'成功' if success else '失敗'}")
        print("💡 提示: 在主程式中雙擊股票即可查看K線圖信號")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
