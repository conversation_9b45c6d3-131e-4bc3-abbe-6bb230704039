# 月營收排行榜PE整合功能說明

## 🎯 功能概述

在原有的月營收排行榜基礎上，新增了PE資料庫中的重要財務指標，包括殖利率、本益比、股價淨值比和EPS，讓用戶能夠在查看營收成長的同時，也能評估股票的估值水準。

## 📋 新增的PE相關欄位

### 1. 殖利率(%)
- **來源**: PE資料庫的 `dividend_yield` 欄位
- **格式**: 保留兩位小數 (例如: 3.25)
- **說明**: 顯示股票的股息殖利率，幫助評估股息收益

### 2. 本益比
- **來源**: PE資料庫的 `pe_ratio` 欄位
- **格式**: 保留兩位小數 (例如: 15.80)
- **說明**: 顯示股票的本益比，用於評估股票估值水準

### 3. 股價淨值比
- **來源**: PE資料庫的 `pb_ratio` 欄位
- **格式**: 保留兩位小數 (例如: 2.45)
- **說明**: 顯示股票的股價淨值比，評估股票相對於淨值的估值

### 4. EPS (每股盈餘)
- **計算方式**: 理論上為 股價 / 本益比
- **目前狀態**: 暫時顯示 "N/A" (因缺乏即時股價資料)
- **未來改進**: 可結合股價資料計算實際EPS

## 📊 完整的表格結構

### 一般排行榜 (13欄)
| 欄位 | 說明 | 範例 |
|------|------|------|
| 排名 | 排行榜名次 | 1, 2, 3... |
| 股票代碼 | 4位數股票代碼 | 2330, 2317 |
| 股票名稱 | 公司名稱 | 台積電, 鴻海 |
| 西元年月 | 資料年月 | 202507 |
| 當月營收(千元) | 當月營業收入 | 120,000,000 |
| 上個月營收(千元) | 上月營業收入 | 115,000,000 |
| 去年同月營收(千元) | 去年同月營收 | 100,000,000 |
| YoY% | 年增率 | +20.00% |
| MoM% | 月增率 | +4.35% |
| 殖利率(%) | 股息殖利率 | 3.25 |
| 本益比 | 本益比 | 15.80 |
| 股價淨值比 | PB比 | 2.45 |
| EPS | 每股盈餘 | N/A |

### 綜合評分排行榜 (14欄)
在上述13欄的基礎上，再加上：
| 欄位 | 說明 | 範例 |
|------|------|------|
| 綜合評分 | 營收成長綜合評分 | 85.5 |

## 🔧 技術實現

### 核心修改
1. **新增 `merge_pe_data` 函數**
   - 從PE資料庫獲取財務指標
   - 自動尋找最接近的可用日期
   - 處理資料缺失情況

2. **修改表格結構**
   - 一般排行榜：從9欄增加到13欄
   - 綜合評分排行榜：從10欄增加到14欄

3. **資料合併邏輯**
   - 在計算完營收成長率後合併PE資料
   - 使用 `stock_id` 作為合併鍵
   - 處理PE資料缺失的情況

### 關鍵程式碼

```python
def merge_pe_data(self, revenue_data, selected_date):
    """合併PE資料庫中的財務指標"""
    # 連接PE資料庫
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    conn = sqlite3.connect(pe_db_path)
    
    # 找到最接近的可用日期
    target_date = selected_date.strftime('%Y-%m-%d')
    
    # 獲取PE資料並合併
    merged_data = revenue_data.merge(
        pe_df[['stock_id', 'dividend_yield', 'pe_ratio', 'pb_ratio']], 
        on='stock_id', 
        how='left'
    )
    
    return merged_data
```

## 🧪 測試結果

### 資料覆蓋率
- **總股票數**: 測試樣本10支股票
- **本益比覆蓋率**: 10/10 (100.0%)
- **殖利率覆蓋率**: 10/10 (100.0%)
- **股價淨值比覆蓋率**: 10/10 (100.0%)

### 測試樣本
```
排名 代碼   名稱     西元年月 當月營收    上月營收    去年同月    YoY%   MoM%   殖利率 本益比 股淨比
1    1110   東泥     202507   222,403    261,492    192,948   15.3%  -14.9%  1.67  28.13  1.09
2    1108   幸福     202507   361,327    388,571    354,661    1.9%   -7.0%  5.30  11.41  1.14
3    1210   大成     202507 7,980,681  8,229,515  8,207,756   -2.8%   -3.0%  4.54  14.62  2.14
```

## 💡 用戶價值

### 1. 全面的投資分析
- **營收成長**: YoY和MoM顯示公司營收趨勢
- **估值水準**: 本益比和股價淨值比評估估值合理性
- **股息收益**: 殖利率顯示股息投資價值

### 2. 一站式資訊
- 不需要在多個系統間切換
- 營收和估值資訊同時呈現
- 便於快速篩選投資標的

### 3. 投資決策支援
- **成長股篩選**: 高YoY/MoM + 合理本益比
- **價值股發現**: 低本益比 + 高殖利率
- **綜合評估**: 營收成長與估值的平衡

## 🚀 使用方法

1. **啟動程式**: 運行 `python O3mh_gui_v21_optimized.py`
2. **選擇日期**: 在「計算日期」欄位選擇要查詢的日期
3. **選擇排行榜**: 在下拉選單中選擇月營收排行榜類型
4. **執行查詢**: 點擊「執行排行」按鈕
5. **查看結果**: 在右側表格中查看包含PE指標的完整排行榜

## 📝 注意事項

### 1. 資料時效性
- PE資料會自動尋找最接近的可用日期
- 通常使用查詢日期前7天內的最新PE資料

### 2. 資料完整性
- 如果某支股票沒有PE資料，相關欄位會顯示 "N/A"
- 不影響營收排行榜的正常顯示

### 3. EPS計算
- 目前EPS欄位顯示 "N/A"
- 未來可結合即時股價資料計算實際EPS

## ✅ 功能狀態

- ✅ PE資料庫整合完成
- ✅ 新欄位顯示正常
- ✅ 資料合併邏輯正確
- ✅ 測試通過
- ✅ 功能可正常使用

## 🔮 未來改進

1. **EPS計算**: 結合股價資料計算實際EPS
2. **更多指標**: 可考慮加入ROE、ROA等指標
3. **顏色標示**: 為PE指標添加顏色標示（如高殖利率用紅色）
4. **排序功能**: 支援按PE指標排序
