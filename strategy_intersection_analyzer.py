#!/usr/bin/env python3
"""
策略交集分析器
分析多個策略的交集，找出同時被多個策略選中的股票
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Set, Tuple
import logging
from datetime import datetime
import json

class StrategyIntersectionAnalyzer:
    """策略交集分析器"""
    
    def __init__(self):
        self.strategy_results = {}
        self.intersection_results = {}
        
    def add_strategy_result(self, strategy_name: str, result_df: pd.DataFrame, stock_code_column: str = '股票代碼'):
        """
        添加策略結果
        
        Args:
            strategy_name: 策略名稱
            result_df: 策略篩選結果DataFrame
            stock_code_column: 股票代碼欄位名稱
        """
        if result_df.empty:
            logging.warning(f"⚠️ 策略 {strategy_name} 沒有篩選出任何股票")
            self.strategy_results[strategy_name] = set()
        else:
            # 提取股票代碼
            stock_codes = set(result_df[stock_code_column].astype(str))
            self.strategy_results[strategy_name] = stock_codes
            logging.info(f"✅ 策略 {strategy_name} 篩選出 {len(stock_codes)} 支股票")
    
    def calculate_intersection(self, strategy_names: List[str]) -> Dict:
        """
        計算多個策略的交集
        
        Args:
            strategy_names: 要計算交集的策略名稱列表
            
        Returns:
            交集分析結果
        """
        if len(strategy_names) < 2:
            raise ValueError("至少需要2個策略才能計算交集")
        
        # 檢查策略是否存在
        missing_strategies = [name for name in strategy_names if name not in self.strategy_results]
        if missing_strategies:
            raise ValueError(f"以下策略結果不存在: {missing_strategies}")
        
        # 計算交集
        intersection_stocks = self.strategy_results[strategy_names[0]].copy()
        for strategy_name in strategy_names[1:]:
            intersection_stocks = intersection_stocks.intersection(self.strategy_results[strategy_name])
        
        # 計算各種統計信息
        result = {
            'strategies': strategy_names,
            'intersection_stocks': list(intersection_stocks),
            'intersection_count': len(intersection_stocks),
            'strategy_counts': {name: len(self.strategy_results[name]) for name in strategy_names},
            'intersection_ratio': {},
            'unique_to_strategy': {},
            'pairwise_intersections': {}
        }
        
        # 計算交集比例
        for strategy_name in strategy_names:
            strategy_count = len(self.strategy_results[strategy_name])
            if strategy_count > 0:
                result['intersection_ratio'][strategy_name] = len(intersection_stocks) / strategy_count
            else:
                result['intersection_ratio'][strategy_name] = 0
        
        # 計算每個策略獨有的股票
        for strategy_name in strategy_names:
            other_strategies = [s for s in strategy_names if s != strategy_name]
            other_stocks = set()
            for other in other_strategies:
                other_stocks = other_stocks.union(self.strategy_results[other])
            
            unique_stocks = self.strategy_results[strategy_name] - other_stocks
            result['unique_to_strategy'][strategy_name] = list(unique_stocks)
        
        # 計算兩兩交集
        for i, strategy1 in enumerate(strategy_names):
            for j, strategy2 in enumerate(strategy_names[i+1:], i+1):
                pair_intersection = self.strategy_results[strategy1].intersection(self.strategy_results[strategy2])
                result['pairwise_intersections'][f"{strategy1} ∩ {strategy2}"] = {
                    'stocks': list(pair_intersection),
                    'count': len(pair_intersection)
                }
        
        return result
    
    def analyze_all_combinations(self) -> Dict:
        """分析所有可能的策略組合"""
        strategy_names = list(self.strategy_results.keys())
        all_results = {}
        
        # 兩兩組合
        for i, strategy1 in enumerate(strategy_names):
            for j, strategy2 in enumerate(strategy_names[i+1:], i+1):
                combo_name = f"{strategy1} + {strategy2}"
                try:
                    result = self.calculate_intersection([strategy1, strategy2])
                    all_results[combo_name] = result
                except Exception as e:
                    logging.error(f"❌ 計算 {combo_name} 交集失敗: {e}")
        
        # 三個組合（如果有足夠的策略）
        if len(strategy_names) >= 3:
            for i, strategy1 in enumerate(strategy_names):
                for j, strategy2 in enumerate(strategy_names[i+1:], i+1):
                    for k, strategy3 in enumerate(strategy_names[j+1:], j+1):
                        combo_name = f"{strategy1} + {strategy2} + {strategy3}"
                        try:
                            result = self.calculate_intersection([strategy1, strategy2, strategy3])
                            all_results[combo_name] = result
                        except Exception as e:
                            logging.error(f"❌ 計算 {combo_name} 交集失敗: {e}")
        
        return all_results
    
    def create_intersection_report(self, intersection_result: Dict) -> str:
        """創建交集分析報告"""
        report = []
        report.append("📊 策略交集分析報告")
        report.append("=" * 50)
        
        # 基本信息
        strategies = intersection_result['strategies']
        report.append(f"\n🎯 分析策略: {' + '.join(strategies)}")
        report.append(f"📅 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 交集結果
        intersection_stocks = intersection_result['intersection_stocks']
        report.append(f"\n🎪 交集結果:")
        report.append(f"  📈 共同選中股票: {len(intersection_stocks)} 支")
        
        if intersection_stocks:
            report.append(f"  📋 股票清單: {', '.join(intersection_stocks)}")
        else:
            report.append(f"  ⚠️ 沒有共同選中的股票")
        
        # 各策略統計
        report.append(f"\n📊 各策略統計:")
        for strategy in strategies:
            count = intersection_result['strategy_counts'][strategy]
            ratio = intersection_result['intersection_ratio'][strategy]
            report.append(f"  • {strategy}: {count} 支股票 (交集比例: {ratio:.1%})")
        
        # 兩兩交集
        if intersection_result['pairwise_intersections']:
            report.append(f"\n🔗 兩兩交集:")
            for pair, data in intersection_result['pairwise_intersections'].items():
                report.append(f"  • {pair}: {data['count']} 支股票")
                if data['stocks']:
                    report.append(f"    股票: {', '.join(data['stocks'])}")
        
        # 獨有股票
        report.append(f"\n🎯 各策略獨有股票:")
        for strategy in strategies:
            unique_stocks = intersection_result['unique_to_strategy'][strategy]
            report.append(f"  • {strategy} 獨有: {len(unique_stocks)} 支")
            if unique_stocks:
                report.append(f"    股票: {', '.join(unique_stocks[:10])}{'...' if len(unique_stocks) > 10 else ''}")
        
        return "\n".join(report)
    
    def export_intersection_results(self, intersection_result: Dict, filename: str = None):
        """導出交集結果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            strategies = intersection_result.get('strategies', [])

            # 根據分析類型生成不同的文件名
            analysis_type = intersection_result.get('analysis_type', 'single_intersection')
            if analysis_type == 'all_combinations':
                strategies_name = "_".join(strategies[:3])  # 最多取前3個策略名稱
                if len(strategies) > 3:
                    strategies_name += f"_等{len(strategies)}個策略"
                filename = f"策略交集_所有組合_{strategies_name}_{timestamp}.json"
            else:
                strategies_name = "_".join(strategies)
                filename = f"策略交集_{strategies_name}_{timestamp}.json"

        try:
            # 創建可序列化的副本
            serializable_result = self._make_serializable(intersection_result)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)
            logging.info(f"✅ 交集結果已導出到: {filename}")
            return filename
        except Exception as e:
            logging.error(f"❌ 導出失敗: {e}")
            return None

    def _make_serializable(self, obj):
        """遞歸地將對象轉換為可JSON序列化的格式"""
        if isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)
        else:
            return obj
    
    def get_top_intersection_combinations(self, min_intersection_count: int = 1) -> List[Tuple[str, int]]:
        """獲取交集數量最多的策略組合"""
        all_results = self.analyze_all_combinations()
        
        # 按交集數量排序
        sorted_combinations = []
        for combo_name, result in all_results.items():
            intersection_count = result['intersection_count']
            if intersection_count >= min_intersection_count:
                sorted_combinations.append((combo_name, intersection_count))
        
        sorted_combinations.sort(key=lambda x: x[1], reverse=True)
        return sorted_combinations

def demo_usage():
    """示範使用方法"""
    print("🧪 策略交集分析器示範")
    print("=" * 50)
    
    # 創建分析器
    analyzer = StrategyIntersectionAnalyzer()
    
    # 模擬策略結果
    print("\n📊 模擬策略結果:")
    
    # CANSLIM策略結果
    canslim_data = pd.DataFrame({
        '股票代碼': ['2330', '2317', '2454', '2327', '2603'],
        '股票名稱': ['台積電', '鴻海', '聯發科', '國巨', '長榮'],
        '評分': [85, 78, 82, 75, 70]
    })
    analyzer.add_strategy_result("CANSLIM量價齊升", canslim_data)
    
    # 藏獒策略結果
    mastiff_data = pd.DataFrame({
        '股票代碼': ['2330', '2454', '2327', '3008', '1590'],
        '股票名稱': ['台積電', '聯發科', '國巨', '大立光', '亞德客'],
        '動能分數': [92, 88, 85, 80, 75]
    })
    analyzer.add_strategy_result("藏獒", mastiff_data)
    
    # 二次創高策略結果
    second_high_data = pd.DataFrame({
        '股票代碼': ['2330', '2317', '3008', '2412', '2881'],
        '股票名稱': ['台積電', '鴻海', '大立光', '中華電', '富邦金'],
        '創高強度': [95, 88, 85, 80, 75]
    })
    analyzer.add_strategy_result("二次創高股票", second_high_data)
    
    # 計算三個策略的交集
    print("\n🎯 計算三策略交集:")
    try:
        intersection_result = analyzer.calculate_intersection(["CANSLIM量價齊升", "藏獒", "二次創高股票"])
        
        # 顯示報告
        report = analyzer.create_intersection_report(intersection_result)
        print(report)
        
        # 導出結果
        filename = analyzer.export_intersection_results(intersection_result)
        if filename:
            print(f"\n📁 結果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 計算交集失敗: {e}")
    
    # 分析所有組合
    print("\n🔍 分析所有策略組合:")
    top_combinations = analyzer.get_top_intersection_combinations()
    
    for i, (combo_name, count) in enumerate(top_combinations, 1):
        print(f"  {i}. {combo_name}: {count} 支共同股票")
    
    print("\n✅ 示範完成！")

if __name__ == "__main__":
    # 設置日誌
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 運行示範
    demo_usage()
