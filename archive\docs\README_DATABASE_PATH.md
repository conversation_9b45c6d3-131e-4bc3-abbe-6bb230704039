# 🔥🔥🔥 重要：數據庫路徑配置提醒 🔥🔥🔥

## ⚠️ 每次修改程式前必須檢查！⚠️

### 📍 真實數據庫路徑
```
D:/Finlab/history/tables/price.db
```

### 📊 數據庫資訊
- **文件大小**: 304.69 MB
- **股票數量**: 2135支
- **記錄總數**: 2,430,849條
- **最新日期**: 2025-06-24

### 🚫 錯誤路徑（不要使用）
```
db/price.db  ← 這個是空的！只有0條記錄
```

### 📝 配置位置
在 `app_config.json` 文件中：
```json
{
  "db_paths": {
    "price": "D:/Finlab/history/tables/price.db"  ← 必須是這個路徑！
  }
}
```

### 🔧 檢查方法
```python
# 快速檢查數據庫路徑是否正確
import sqlite3
import os

db_path = "D:/Finlab/history/tables/price.db"
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
    count = cursor.fetchone()[0]
    print(f"數據庫記錄數：{count}")
    if count > 2000000:
        print("✅ 正確的數據庫！")
    else:
        print("❌ 錯誤的數據庫！")
    conn.close()
else:
    print("❌ 數據庫文件不存在！")
```

### 🎯 常見錯誤
1. **使用本地空數據庫**: `db/price.db` (0條記錄)
2. **路徑不存在**: 文件路徑錯誤
3. **忘記更新配置**: `app_config.json` 中的路徑沒有更新

### 💡 提醒
- 每次創建新版本程式時，第一件事就是檢查數據庫路徑
- 每次選股結果為空時，第一個要檢查的就是數據庫路徑
- 把這個文件放在最醒目的位置！ 