#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試開盤前監控系統的真實數據獲取功能
"""

import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入開盤前監控系統
from O3mh_gui_v21_optimized import PreMarketMonitor

def test_pre_market_monitor():
    """測試開盤前監控系統"""
    print("=" * 60)
    print("🔍 開盤前監控系統測試")
    print("=" * 60)
    
    # 初始化監控系統
    monitor = PreMarketMonitor()
    print(f"✅ 監控系統初始化成功: {monitor.name} v{monitor.version}")
    
    print("\n📊 開始獲取市場數據...")
    print("-" * 40)
    
    # 測試美股指數
    print("\n🇺🇸 美股三大指數:")
    us_data = monitor.get_us_indices()
    for name, data in us_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        print(f"  {status_icon} {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) [{data['status']}]")
    
    # 測試亞洲指數
    print("\n🌏 亞洲主要指數:")
    asia_data = monitor.get_asia_indices()
    for name, data in asia_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        print(f"  {status_icon} {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) [{data['status']}]")
    
    # 測試商品價格
    print("\n🛢️ 大宗商品:")
    commodity_data = monitor.get_commodity_prices()
    for name, data in commodity_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        print(f"  {status_icon} {name}: ${data['price']:.2f} ({data['change_pct']:+.2f}%) [{data['status']}]")
    
    # 測試匯率
    print("\n💱 主要匯率:")
    fx_data = monitor.get_fx_rates()
    for name, data in fx_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        print(f"  {status_icon} {name}: {data['rate']:.4f} ({data['change_pct']:+.2f}%) [{data['status']}]")
    
    # 測試台灣市場
    print("\n🇹🇼 台灣市場:")
    taiwan_data = monitor.get_taiwan_futures()
    for name, data in taiwan_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        volume_str = f" (成交量: {data['volume']:,})" if data.get('volume', 0) > 0 else ""
        print(f"  {status_icon} {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) [{data['status']}]{volume_str}")
    
    # 測試加密貨幣
    print("\n₿ 加密貨幣:")
    crypto_data = monitor.get_crypto_prices()
    for name, data in crypto_data.items():
        status_icon = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
        print(f"  {status_icon} {name}: ${data['price']:,.2f} ({data['change_pct']:+.2f}%) [{data['status']}]")
    
    print("\n" + "=" * 60)
    print("🔍 執行完整掃描測試...")
    print("=" * 60)
    
    # 執行完整掃描
    scan_results = monitor.run_full_scan()
    
    if scan_results:
        print(f"✅ 完整掃描成功完成")
        print(f"📅 掃描時間: {scan_results['timestamp']}")
        
        # 統計數據狀態
        total_items = 0
        real_data_items = 0
        simulated_items = 0
        
        for category, data in scan_results.items():
            if category in ['timestamp']:
                continue
            if isinstance(data, dict):
                for item_name, item_data in data.items():
                    if isinstance(item_data, dict) and 'status' in item_data:
                        total_items += 1
                        if '真實數據' in item_data['status']:
                            real_data_items += 1
                        else:
                            simulated_items += 1
        
        print(f"📊 數據統計:")
        print(f"  總項目數: {total_items}")
        print(f"  真實數據: {real_data_items} ({real_data_items/total_items*100:.1f}%)")
        print(f"  模擬數據: {simulated_items} ({simulated_items/total_items*100:.1f}%)")
        
        # 分析市場情緒
        market_sentiment = monitor.get_market_sentiment()
        print(f"📈 市場情緒: {market_sentiment}")
        
    else:
        print("❌ 完整掃描失敗")
    
    print("\n" + "=" * 60)
    print("🎯 測試完成")
    print("=" * 60)

def test_data_quality():
    """測試數據品質"""
    print("\n🔬 數據品質測試...")
    print("-" * 40)
    
    monitor = PreMarketMonitor()
    
    # 多次獲取數據，檢查一致性
    print("📊 檢查數據一致性 (連續3次獲取):")
    
    for i in range(3):
        print(f"\n第 {i+1} 次獲取:")
        us_data = monitor.get_us_indices()
        
        for name, data in us_data.items():
            print(f"  {name}: {data['price']:.2f} [{data['status']}]")
    
    print("\n✅ 數據品質測試完成")

if __name__ == "__main__":
    # 設置日誌級別
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 執行主要測試
        test_pre_market_monitor()
        
        # 執行數據品質測試
        test_data_quality()
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
