#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def create_stock_info():
    """創建 stock_info 表"""
    
    # 基本股票資料
    stocks = {
        "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2412": "中華電",
        "2881": "富邦金", "2882": "國泰金", "2884": "玉山金", "2885": "元大金",
        "2308": "台達電", "2382": "廣達", "2303": "聯電", "2357": "華碩",
        "3008": "大立光", "2409": "友達", "2327": "國巨", "2379": "瑞昱",
        "0050": "元大台灣50", "0056": "元大高股息", "0051": "元大中型100"
    }
    
    # 確保目錄存在
    os.makedirs('db', exist_ok=True)
    
    # 處理多個數據庫
    db_files = ['db/price.db', 'db/pe_data.db', 'db/stock_data.db']
    
    for db_file in db_files:
        try:
            print(f"處理 {db_file}...")
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 創建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_id TEXT PRIMARY KEY,
                    stock_name TEXT NOT NULL,
                    industry TEXT DEFAULT '科技業',
                    market TEXT DEFAULT '上市'
                )
            ''')
            
            # 清空並插入數據
            cursor.execute('DELETE FROM stock_info')
            for stock_id, stock_name in stocks.items():
                cursor.execute('''
                    INSERT INTO stock_info (stock_id, stock_name, industry, market)
                    VALUES (?, ?, ?, ?)
                ''', (stock_id, stock_name, '科技業', '上市'))
            
            conn.commit()
            conn.close()
            print(f"✅ {db_file} 完成")
            
        except Exception as e:
            print(f"❌ {db_file} 失敗: {e}")

if __name__ == "__main__":
    print("🚀 創建 stock_info 表...")
    create_stock_info()
    print("✅ 完成！")
