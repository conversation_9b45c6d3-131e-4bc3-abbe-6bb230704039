#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證 price 增量更新結果
"""

import sqlite3

def verify_update_result():
    """驗證更新結果"""
    
    print("=" * 60)
    print("📊 驗證 price 增量更新結果")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    # 檢查新增資料的範例
    print("📊 新增資料範例 (2022-09-01):")
    cursor.execute('''
        SELECT stock_id, stock_name, Volume, [Open], High, Low, [Close], date 
        FROM stock_daily_data 
        WHERE date = "2022-09-01" 
        ORDER BY stock_id 
        LIMIT 10
    ''')
    samples = cursor.fetchall()
    
    for row in samples:
        stock_id, stock_name, volume, open_p, high, low, close, date = row
        print(f"  {stock_id} | 量:{volume:,} | 開:{open_p} 高:{high} 低:{low} 收:{close}")
    
    # 檢查資料完整性
    print(f"\n📊 資料完整性檢查:")
    cursor.execute('SELECT COUNT(*) FROM stock_daily_data WHERE [Close] = 0 AND date >= "2022-08-31"')
    zero_close = cursor.fetchone()[0]
    print(f"  收盤價為0的記錄: {zero_close} 筆")
    
    cursor.execute('SELECT COUNT(*) FROM stock_daily_data WHERE Volume = 0 AND date >= "2022-08-31"')
    zero_volume = cursor.fetchone()[0]
    print(f"  成交量為0的記錄: {zero_volume} 筆")
    
    # 檢查重要股票
    print(f"\n📊 重要股票驗證:")
    important_stocks = ['2330', '2317', '2454']
    for stock_id in important_stocks:
        cursor.execute('''
            SELECT stock_id, [Close], Volume, date 
            FROM stock_daily_data 
            WHERE stock_id = ? AND date >= "2022-08-31" 
            ORDER BY date
        ''', (stock_id,))
        stock_data = cursor.fetchall()
        
        if stock_data:
            print(f"  {stock_id}:")
            for sid, close, volume, date in stock_data:
                print(f"    {date}: 收盤 {close}, 量 {volume:,}")
        else:
            print(f"  {stock_id}: 無新增資料")
    
    # 檢查日期分布
    print(f"\n📊 新增日期分布:")
    cursor.execute('''
        SELECT date, COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31" 
        GROUP BY date 
        ORDER BY date
    ''')
    date_dist = cursor.fetchall()
    
    for date, count in date_dist:
        print(f"  {date}: {count:,} 筆")
    
    conn.close()
    
    print(f"\n✅ 驗證完成！")

if __name__ == "__main__":
    verify_update_result()
