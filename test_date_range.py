#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試日期範圍功能
"""

from datetime import datetime, timedelta
from dateutil.rrule import rrule, DAILY, MONTHLY

def test_date_ranges():
    """測試各種日期範圍生成"""
    print("🧪 測試日期範圍功能")
    print("=" * 50)
    
    # 設置測試日期
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=7)
    
    print(f"📅 測試範圍: {start_date} ~ {end_date}")
    
    # 測試每日範圍
    daily_dates = [dt.date() for dt in rrule(DAILY, dtstart=start_date, until=end_date)]
    print(f"\n📊 每日範圍 ({len(daily_dates)} 天):")
    for date in daily_dates[:5]:  # 只顯示前5個
        print(f"  - {date}")
    if len(daily_dates) > 5:
        print(f"  ... 還有 {len(daily_dates)-5} 個日期")
    
    # 測試月度範圍
    monthly_start = datetime(2024, 1, 1).date()
    monthly_end = datetime(2024, 6, 30).date()
    monthly_dates = [dt.date() for dt in rrule(MONTHLY, dtstart=monthly_start, until=monthly_end)]
    print(f"\n📊 月度範圍 ({len(monthly_dates)} 個月):")
    for date in monthly_dates:
        print(f"  - {date}")
    
    # 測試季度範圍
    def generate_season_range(start_date, end_date):
        import datetime
        ret = []
        for year in range(start_date.year-1, end_date.year+1):
            ret += [
                datetime.date(year, 5, 15),    # Q1
                datetime.date(year, 8, 14),    # Q2  
                datetime.date(year, 11, 14),   # Q3
                datetime.date(year+1, 3, 31)  # Q4
            ]
        ret = [r for r in ret if start_date < r < end_date]
        return ret
    
    season_start = datetime(2023, 1, 1).date()
    season_end = datetime(2024, 12, 31).date()
    season_dates = generate_season_range(season_start, season_end)
    print(f"\n📊 季度範圍 ({len(season_dates)} 個季度):")
    for date in season_dates:
        print(f"  - {date}")
    
    print(f"\n✅ 日期範圍功能測試完成")
    print(f"💡 GUI現在支援正確的時間區間批量爬取")

def test_simple_crawler_with_range():
    """測試簡化版爬蟲的時間區間功能"""
    print(f"\n🔧 測試簡化版爬蟲時間區間功能")
    print("=" * 50)
    
    import pandas as pd
    
    def simple_crawler(date):
        """簡化版爬蟲函數"""
        return pd.DataFrame({
            'date': [date],
            'stock_id': ['測試股票'],
            'price': [100.0],
            'volume': [1000]
        })
    
    # 測試批量爬取
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=3)
    
    dates = [dt.date() for dt in rrule(DAILY, dtstart=start_date, until=end_date)]
    
    print(f"📅 測試日期: {dates}")
    
    all_data = []
    for date in dates:
        data = simple_crawler(date)
        all_data.append(data)
        print(f"✅ {date}: 獲取 {len(data)} 筆資料")
    
    # 合併資料
    if all_data:
        result = pd.concat(all_data, ignore_index=True)
        print(f"\n📊 合併結果:")
        print(result)
        print(f"📈 總計: {len(result)} 筆資料")
    
    print(f"\n✅ 簡化版爬蟲時間區間測試完成")

if __name__ == "__main__":
    test_date_ranges()
    test_simple_crawler_with_range()
