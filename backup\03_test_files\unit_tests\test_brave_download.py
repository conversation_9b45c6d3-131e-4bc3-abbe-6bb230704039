#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用Brave瀏覽器測試GoodInfo下載
檢查多個可能的下載位置
"""

import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from datetime import datetime

def get_default_download_paths():
    """獲取可能的下載路徑"""
    username = os.getenv('USERNAME')
    paths = [
        f"C:/Users/<USER>/Downloads",
        f"C:/Users/<USER>/AppData/Local/BraveSoftware/Brave-Browser/User Data/Default/Downloads",
        os.path.join(os.getcwd(), "downloads"),
        "D:/Downloads",
        "D:/Finlab/history/tables/monthly_revenue"
    ]
    return paths

def setup_brave_driver():
    """設置Brave瀏覽器"""
    options = Options()
    
    # 使用用戶的默認下載目錄
    username = os.getenv('USERNAME')
    download_dir = f"C:/Users/<USER>/Downloads"
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": False,
        "safebrowsing.disable_download_protection": True,
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.default_content_setting_values.automatic_downloads": 1
    }
    options.add_experimental_option("prefs", prefs)
    
    # 隱藏自動化提示
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option('excludeSwitches', ['enable-automation'])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 基本設定
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--log-level=3')
    
    # Brave瀏覽器路徑
    brave_paths = [
        "C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe",
        "C:/Program Files (x86)/BraveSoftware/Brave-Browser/Application/brave.exe",
        f"C:/Users/<USER>/AppData/Local/BraveSoftware/Brave-Browser/Application/brave.exe",
    ]
    
    brave_path = None
    for path in brave_paths:
        if os.path.exists(path):
            brave_path = path
            break
    
    if brave_path:
        options.binary_location = brave_path
        print(f"🦁 使用Brave瀏覽器: {brave_path}")
    else:
        print("⚠️ 未找到Brave瀏覽器，將使用Chrome")
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver, download_dir

def monitor_all_download_locations(initial_files_dict):
    """監控所有可能的下載位置"""
    download_paths = get_default_download_paths()
    
    for path in download_paths:
        if os.path.exists(path):
            current_files = set(glob.glob(os.path.join(path, "*.*")))
            initial_files = initial_files_dict.get(path, set())
            new_files = current_files - initial_files
            
            if new_files:
                for new_file in new_files:
                    print(f"🆕 在 {path} 發現新文件: {os.path.basename(new_file)}")
                    if new_file.lower().endswith(('.xls', '.xlsx')):
                        return new_file, path
                    elif new_file.lower().endswith(('.crdownload', '.tmp', '.part')):
                        print(f"⏳ 下載中: {os.path.basename(new_file)}")
    
    return None, None

def test_download_with_monitoring(stock_id="2330"):
    """測試下載並監控多個位置"""
    driver, primary_download_dir = setup_brave_driver()
    
    try:
        print(f"🚀 測試下載 {stock_id} 的月營收資料...")
        print(f"📁 主要下載目錄: {primary_download_dir}")
        
        # 記錄所有可能位置的初始文件
        download_paths = get_default_download_paths()
        initial_files_dict = {}
        
        for path in download_paths:
            if os.path.exists(path):
                initial_files_dict[path] = set(glob.glob(os.path.join(path, "*.*")))
                print(f"📋 {path}: {len(initial_files_dict[path])} 個初始文件")
        
        # 訪問頁面
        url = f"https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID={stock_id}"
        print(f"📱 訪問: {url}")
        driver.get(url)
        
        time.sleep(5)
        print(f"📄 頁面標題: {driver.title}")
        
        # 尋找並點擊XLS按鈕
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        export_button = None
        
        for input_elem in all_inputs:
            try:
                input_value = input_elem.get_attribute("value")
                if input_value and "XLS" in input_value:
                    if input_elem.is_displayed() and input_elem.is_enabled():
                        export_button = input_elem
                        print(f"✅ 找到匯出按鈕: {input_value}")
                        break
            except:
                continue
        
        if not export_button:
            print("❌ 未找到匯出按鈕")
            return False
        
        print("📥 點擊匯出按鈕...")
        driver.execute_script("arguments[0].click();", export_button)
        print("✅ 按鈕點擊完成")
        
        # 監控下載
        print("⏳ 監控所有下載位置...")
        for i in range(60):
            downloaded_file, download_path = monitor_all_download_locations(initial_files_dict)
            
            if downloaded_file:
                print(f"✅ 下載成功!")
                print(f"📁 文件位置: {download_path}")
                print(f"📄 文件名: {os.path.basename(downloaded_file)}")
                return True
            
            time.sleep(2)
            print(f"⏳ 等待中... ({i+1}/60)")
        
        print("❌ 下載超時")
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    success = test_download_with_monitoring("2330")
    if success:
        print("🎉 測試成功！")
    else:
        print("💥 測試失敗！")
