#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 tsrtc 台灣股票即時爬蟲功能
"""

import os
import json
import csv
import time
import ssl
from datetime import date, datetime
import requests
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class TsrtcTester:
    """TSRTC 爬蟲測試器"""
    
    def __init__(self):
        self.endpoint = 'http://mis.twse.com.tw/stock/api/getStockInfo.jsp'
        self.session = requests.Session()
        # 設置SSL驗證為False來避免證書問題
        self.session.verify = False
        
    def test_single_stock(self, stock_id='2330'):
        """測試單一股票的即時報價"""
        print(f"🧪 測試股票 {stock_id} 的即時報價...")
        
        try:
            # 先訪問主頁獲取session
            print("📡 正在建立連接...")
            response = self.session.get(
                'http://mis.twse.com.tw/stock/index.jsp',
                headers={'Accept-Language': 'zh-TW'},
                timeout=10
            )
            print(f"✅ 主頁連接成功，狀態碼: {response.status_code}")
            
            # 構建查詢URL
            timestamp = int(time.time() * 1000 + 1000000)
            channel = f'tse_{stock_id}.tw'
            query_url = f'{self.endpoint}?_={timestamp}&ex_ch={channel}'
            
            print(f"🔍 查詢URL: {query_url}")
            
            # 獲取股票數據
            print("📊 正在獲取股票數據...")
            response = self.session.get(query_url, timeout=10)
            
            if response.status_code == 200:
                content = json.loads(response.text)
                print(f"✅ API回應成功，狀態碼: {response.status_code}")
                
                # 分析回應內容
                self.analyze_response(content, stock_id)
                return content
            else:
                print(f"❌ API回應失敗，狀態碼: {response.status_code}")
                print(f"回應內容: {response.text[:200]}...")
                return None
                
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL錯誤: {e}")
            return None
        except requests.exceptions.Timeout as e:
            print(f"❌ 連接超時: {e}")
            return None
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 連接錯誤: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析錯誤: {e}")
            print(f"原始回應: {response.text[:200]}...")
            return None
        except Exception as e:
            print(f"❌ 未知錯誤: {e}")
            return None
    
    def analyze_response(self, content, stock_id):
        """分析API回應內容"""
        print("\n📋 API回應分析:")
        print(f"  - rtcode: {content.get('rtcode', 'N/A')}")
        print(f"  - rtmessage: {content.get('rtmessage', 'N/A')}")
        print(f"  - queryTime: {content.get('queryTime', 'N/A')}")
        print(f"  - userDelay: {content.get('userDelay', 'N/A')}")
        
        msg_array = content.get('msgArray', [])
        print(f"  - msgArray 項目數: {len(msg_array)}")
        
        if msg_array:
            stock_data = msg_array[0]
            print(f"\n📈 股票 {stock_id} 即時資訊:")
            
            # 基本資訊
            print(f"  股票代號: {stock_data.get('c', 'N/A')}")
            print(f"  股票名稱: {stock_data.get('n', 'N/A')}")
            print(f"  全名: {stock_data.get('nf', 'N/A')}")
            print(f"  交易所: {stock_data.get('ex', 'N/A')}")
            
            # 價格資訊
            print(f"  最近成交價: {stock_data.get('z', 'N/A')}")
            print(f"  開盤價: {stock_data.get('o', 'N/A')}")
            print(f"  最高價: {stock_data.get('h', 'N/A')}")
            print(f"  最低價: {stock_data.get('l', 'N/A')}")
            print(f"  昨收價: {stock_data.get('y', 'N/A')}")
            
            # 成交量資訊
            print(f"  當盤成交量: {stock_data.get('tv', 'N/A')}")
            print(f"  累積成交量: {stock_data.get('v', 'N/A')}")
            
            # 五檔資訊
            print(f"  賣出五檔價格: {stock_data.get('a', 'N/A')}")
            print(f"  賣出五檔數量: {stock_data.get('f', 'N/A')}")
            print(f"  買入五檔價格: {stock_data.get('b', 'N/A')}")
            print(f"  買入五檔數量: {stock_data.get('g', 'N/A')}")
            
            # 時間資訊
            print(f"  資料時間: {stock_data.get('t', 'N/A')}")
            print(f"  日期: {stock_data.get('d', 'N/A')}")
            
            # 漲跌停資訊
            print(f"  漲停價: {stock_data.get('u', 'N/A')}")
            print(f"  跌停價: {stock_data.get('w', 'N/A')}")
            
            return True
        else:
            print("❌ 沒有獲取到股票數據")
            return False
    
    def test_multiple_stocks(self, stock_list=['2330', '2317', '2454']):
        """測試多支股票的即時報價"""
        print(f"\n🧪 測試多支股票即時報價: {stock_list}")
        
        try:
            # 先訪問主頁
            self.session.get(
                'http://mis.twse.com.tw/stock/index.jsp',
                headers={'Accept-Language': 'zh-TW'},
                timeout=10
            )
            
            # 構建查詢URL
            timestamp = int(time.time() * 1000 + 1000000)
            channels = '|'.join(f'tse_{stock}.tw' for stock in stock_list)
            query_url = f'{self.endpoint}?_={timestamp}&ex_ch={channels}'
            
            print(f"🔍 查詢URL: {query_url}")
            
            # 獲取數據
            response = self.session.get(query_url, timeout=10)
            
            if response.status_code == 200:
                content = json.loads(response.text)
                msg_array = content.get('msgArray', [])
                
                print(f"✅ 成功獲取 {len(msg_array)} 支股票數據")
                
                for i, stock_data in enumerate(msg_array):
                    stock_id = stock_data.get('c', 'N/A')
                    stock_name = stock_data.get('n', 'N/A')
                    price = stock_data.get('z', 'N/A')
                    volume = stock_data.get('v', 'N/A')
                    time_str = stock_data.get('t', 'N/A')
                    
                    print(f"  {i+1}. {stock_id} {stock_name}: 價格={price}, 成交量={volume}, 時間={time_str}")
                
                return content
            else:
                print(f"❌ 多股票查詢失敗，狀態碼: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 多股票測試錯誤: {e}")
            return None
    
    def test_api_availability(self):
        """測試API可用性"""
        print("🔍 測試API可用性...")
        
        try:
            # 測試主頁連接
            response = self.session.get(
                'http://mis.twse.com.tw/stock/index.jsp',
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 主頁連接正常")
                
                # 測試API端點
                timestamp = int(time.time() * 1000)
                test_url = f'{self.endpoint}?_={timestamp}&ex_ch=tse_2330.tw'
                
                api_response = self.session.get(test_url, timeout=10)
                
                if api_response.status_code == 200:
                    try:
                        content = json.loads(api_response.text)
                        rtcode = content.get('rtcode', 'N/A')
                        print(f"✅ API端點正常，rtcode: {rtcode}")
                        return True
                    except json.JSONDecodeError:
                        print("❌ API回應不是有效的JSON")
                        return False
                else:
                    print(f"❌ API端點異常，狀態碼: {api_response.status_code}")
                    return False
            else:
                print(f"❌ 主頁連接異常，狀態碼: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API可用性測試失敗: {e}")
            return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🚀 TSRTC 台灣股票即時爬蟲測試")
    print("=" * 60)
    
    tester = TsrtcTester()
    
    # 1. 測試API可用性
    print("\n1️⃣ 測試API可用性")
    api_available = tester.test_api_availability()
    
    if not api_available:
        print("❌ API不可用，無法進行後續測試")
        return
    
    # 2. 測試單一股票（台積電 2330）
    print("\n2️⃣ 測試單一股票即時報價")
    single_result = tester.test_single_stock('2330')
    
    # 3. 測試多支股票
    print("\n3️⃣ 測試多支股票即時報價")
    multi_result = tester.test_multiple_stocks(['2330', '2317', '2454', '1301', '2382'])
    
    # 4. 測試原始爬蟲清單中的股票
    print("\n4️⃣ 測試原始清單中的股票")
    original_stocks = ['3380', '3019', '2349', '2609', '2324']
    original_result = tester.test_multiple_stocks(original_stocks)
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    print(f"✅ API可用性: {'正常' if api_available else '異常'}")
    print(f"✅ 單一股票查詢: {'成功' if single_result else '失敗'}")
    print(f"✅ 多股票查詢: {'成功' if multi_result else '失敗'}")
    print(f"✅ 原始清單查詢: {'成功' if original_result else '失敗'}")
    
    if api_available and (single_result or multi_result):
        print("\n🎉 TSRTC爬蟲功能基本正常！")
        print("📝 建議:")
        print("  • 可以獲取即時股價數據")
        print("  • 支援單一和批量查詢")
        print("  • 數據包含完整的五檔資訊")
        print("  • 適合用於即時監控和數據收集")
    else:
        print("\n⚠️ TSRTC爬蟲功能存在問題")
        print("📝 可能原因:")
        print("  • 證交所API政策變更")
        print("  • 網路連接問題")
        print("  • SSL證書驗證問題")
        print("  • 請求頻率限制")

if __name__ == "__main__":
    main()
