#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉅亨網新聞API爬蟲 - 基於您提供的程式碼改良
資料來源：https://blog.jiatool.com/posts/cnyes_news_spider/
"""

import time
import random
import requests
import sqlite3
import json
from datetime import datetime, timedelta
import logging
import os

class CnyesNewsSpider:
    """鉅亨網新聞爬蟲 - 使用API方式"""
    
    def __init__(self, db_path="news.db"):
        self.db_path = db_path
        self.setup_logging()
        self.setup_database()
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_database(self):
        """設置資料庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建新聞表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cnyes_news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    news_id TEXT UNIQUE,
                    title TEXT,
                    summary TEXT,
                    content TEXT,
                    keyword TEXT,
                    publish_time TEXT,
                    category_name TEXT,
                    category_id INTEGER,
                    url TEXT,
                    stock_codes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_id ON cnyes_news(news_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_publish_time ON cnyes_news(publish_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_codes ON cnyes_news(stock_codes)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 資料庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")
            
    def get_newslist_info(self, page=1, limit=30):
        """獲取新聞列表
        
        :param page: 頁數
        :param limit: 一頁新聞數量
        :return newslist_info: 新聞資料
        """
        headers = {
            'Origin': 'https://news.cnyes.com/',
            'Referer': 'https://news.cnyes.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
        }
        
        try:
            url = f"https://api.cnyes.com/media/api/v1/newslist/category/headline?page={page}&limit={limit}"
            self.logger.info(f"🔍 請求第 {page} 頁新聞，每頁 {limit} 筆")
            
            r = requests.get(url, headers=headers, timeout=10)
            if r.status_code != requests.codes.ok:
                self.logger.error(f'❌ 請求失敗，狀態碼: {r.status_code}')
                return None
                
            newslist_info = r.json()
            self.logger.info(f"✅ 成功獲取第 {page} 頁新聞")
            return newslist_info
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 網路請求錯誤: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON解析錯誤: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ 未知錯誤: {e}")
            return None
            
    def get_news_detail(self, news_id, max_retries=3):
        """獲取新聞詳細內容 - 改進版本"""
        headers = {
            'Origin': 'https://news.cnyes.com/',
            'Referer': f'https://news.cnyes.com/news/id/{news_id}',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        }

        for attempt in range(max_retries):
            try:
                url = f"https://api.cnyes.com/media/api/v1/news/detail/{news_id}"
                r = requests.get(url, headers=headers, timeout=15)

                if r.status_code == 200:
                    try:
                        detail = r.json()
                        content = detail.get('content', '')
                        if content:
                            return content
                        else:
                            self.logger.warning(f"⚠️ 新聞 {news_id} 內容為空")
                            return ""
                    except ValueError as e:
                        self.logger.warning(f"⚠️ 新聞 {news_id} JSON解析失敗: {e}")
                        return ""

                elif r.status_code == 404:
                    self.logger.warning(f"⚠️ 新聞 {news_id} 不存在 (404)")
                    return ""

                elif r.status_code == 429:
                    self.logger.warning(f"⚠️ 請求頻率限制 {news_id} (429) - 等待重試")
                    time.sleep(2 ** attempt)  # 指數退避
                    continue

                else:
                    self.logger.warning(f"⚠️ 新聞 {news_id} HTTP錯誤: {r.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(1)
                        continue
                    return ""

            except requests.exceptions.Timeout:
                self.logger.warning(f"⚠️ 新聞 {news_id} 請求超時 (嘗試 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                return ""

            except requests.exceptions.ConnectionError:
                self.logger.warning(f"⚠️ 新聞 {news_id} 連接錯誤 (嘗試 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return ""

            except Exception as e:
                self.logger.error(f"❌ 新聞 {news_id} 獲取失敗: {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                return ""

        return ""
            
    def extract_stock_codes(self, text):
        """從文字中提取股票代碼"""
        import re
        
        # 台股代碼模式 (4位數字)
        stock_pattern = r'\b\d{4}\b'
        matches = re.findall(stock_pattern, text)
        
        # 過濾掉明顯不是股票代碼的數字 (如年份等)
        valid_codes = []
        for code in matches:
            if 1000 <= int(code) <= 9999:  # 台股代碼範圍
                valid_codes.append(code)
                
        return list(set(valid_codes))  # 去重
        
    def save_news_to_db(self, news_data):
        """儲存新聞到資料庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 提取股票代碼
            text_for_analysis = f"{news_data['title']} {news_data['summary']} {news_data.get('content', '')}"
            stock_codes = self.extract_stock_codes(text_for_analysis)
            
            cursor.execute('''
                INSERT OR REPLACE INTO cnyes_news 
                (news_id, title, summary, content, keyword, publish_time, 
                 category_name, category_id, url, stock_codes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                news_data['news_id'],
                news_data['title'],
                news_data['summary'],
                news_data.get('content', ''),
                news_data['keyword'],
                news_data['publish_time'],
                news_data['category_name'],
                news_data['category_id'],
                news_data['url'],
                ','.join(stock_codes)
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 儲存新聞失敗: {e}")
            return False
            
    def crawl_news(self, max_pages=5, stock_code=None, days=7):
        """爬取新聞
        
        :param max_pages: 最大爬取頁數
        :param stock_code: 指定股票代碼 (可選)
        :param days: 爬取天數
        :return: 爬取的新聞數量
        """
        total_news = 0
        target_date = datetime.now() - timedelta(days=days)
        
        self.logger.info(f"🚀 開始爬取鉅亨網新聞")
        self.logger.info(f"📅 目標時間範圍: {days} 天內")
        if stock_code:
            self.logger.info(f"🎯 目標股票: {stock_code}")
            
        for page in range(1, max_pages + 1):
            self.logger.info(f"📄 爬取第 {page} 頁...")
            
            newslist_info = self.get_newslist_info(page=page, limit=30)
            if not newslist_info or 'items' not in newslist_info:
                self.logger.warning(f"⚠️ 第 {page} 頁無資料，跳過")
                continue
                
            items = newslist_info['items']
            if 'data' not in items:
                self.logger.warning(f"⚠️ 第 {page} 頁格式異常，跳過")
                continue
                
            page_news_count = 0
            should_stop = False
            
            for news in items['data']:
                try:
                    # 檢查發布時間
                    publish_timestamp = news['publishAt']
                    publish_time = datetime.fromtimestamp(publish_timestamp)
                    
                    if publish_time < target_date:
                        self.logger.info(f"⏰ 新聞時間超出範圍，停止爬取")
                        should_stop = True
                        break
                        
                    # 準備新聞資料
                    news_data = {
                        'news_id': str(news['newsId']),
                        'title': news['title'],
                        'summary': news['summary'],
                        'keyword': news['keyword'],
                        'publish_time': publish_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'category_name': news['categoryName'],
                        'category_id': news['categoryId'],
                        'url': f"https://news.cnyes.com/news/id/{news['newsId']}"
                    }
                    
                    # 獲取詳細內容
                    content = self.get_news_detail(news['newsId'])
                    news_data['content'] = content
                    
                    # 如果指定了股票代碼，檢查是否相關
                    if stock_code:
                        text_for_check = f"{news_data['title']} {news_data['summary']} {content}"
                        if stock_code not in text_for_check:
                            continue
                            
                    # 儲存新聞
                    if self.save_news_to_db(news_data):
                        page_news_count += 1
                        total_news += 1
                        self.logger.info(f"✅ 儲存新聞: {news_data['title'][:50]}...")
                        
                    # 隨機延遲避免被反爬蟲
                    time.sleep(random.uniform(0.5, 1.5))
                    
                except Exception as e:
                    self.logger.error(f"❌ 處理新聞失敗: {e}")
                    continue
                    
            self.logger.info(f"📊 第 {page} 頁完成，儲存 {page_news_count} 筆新聞")
            
            if should_stop:
                break
                
            # 頁面間隨機延遲
            time.sleep(random.uniform(2, 5))
            
        self.logger.info(f"🎉 爬取完成！總共儲存 {total_news} 筆新聞")
        return total_news
        
    def get_news_by_stock(self, stock_code, days=30):
        """根據股票代碼查詢新聞"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            target_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            cursor.execute('''
                SELECT * FROM cnyes_news 
                WHERE (stock_codes LIKE ? OR title LIKE ? OR summary LIKE ? OR content LIKE ?)
                AND publish_time >= ?
                ORDER BY publish_time DESC
            ''', (f'%{stock_code}%', f'%{stock_code}%', f'%{stock_code}%', f'%{stock_code}%', target_date))
            
            results = cursor.fetchall()
            conn.close()
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 查詢新聞失敗: {e}")
            return []

if __name__ == "__main__":
    # 測試爬蟲
    spider = CnyesNewsSpider()
    
    print("🔍 測試鉅亨網新聞API爬蟲")
    print("=" * 50)
    
    # 爬取最新新聞
    news_count = spider.crawl_news(max_pages=3, days=1)
    print(f"\n✅ 爬取完成，共 {news_count} 筆新聞")
    
    # 測試查詢特定股票
    stock_code = "2330"
    news_list = spider.get_news_by_stock(stock_code, days=7)
    print(f"\n📊 {stock_code} 相關新聞: {len(news_list)} 筆")
