#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
診斷無頭模式下年份選擇器問題
比較有頭模式和無頭模式的差異
"""

import os
import sys
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def test_year_selector_with_headless(headless=True):
    """測試年份選擇器（可選擇有頭或無頭模式）"""
    driver = None
    try:
        mode_name = "無頭模式" if headless else "有頭模式"
        print(f"\n🔍 測試年份選擇器 - {mode_name}")
        print("=" * 60)
        
        # 設置Chrome選項
        options = Options()
        
        # 基本選項
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 減少警告
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        options.add_argument('--silent')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 設置無頭模式
        if headless:
            options.add_argument('--headless')
            print("🔧 啟用無頭模式")
        else:
            print("🔧 啟用有頭模式")
        
        # 創建WebDriver
        driver = webdriver.Chrome(options=options)
        print(f"✅ Chrome瀏覽器已啟動 ({mode_name})")
        
        # 訪問GoodInfo ROE頁面
        url = "https://goodinfo.tw/tw2/StockList.asp?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
        print(f"🌐 訪問頁面: {url}")
        driver.get(url)
        
        # 等待頁面載入
        print("⏳ 等待頁面載入...")
        time.sleep(8)  # 增加等待時間
        
        # 檢查頁面標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 檢查頁面是否正確載入
        if "goodinfo" not in title.lower():
            print("⚠️ 頁面可能載入異常")
        
        # 獲取頁面HTML長度
        html_length = len(driver.page_source)
        print(f"📊 頁面HTML長度: {html_length} 字符")
        
        # 檢查是否有表格
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"📋 找到 {len(tables)} 個表格")
        
        # 年份選擇器測試
        print(f"\n🔍 開始搜尋年份選擇器...")
        
        year_selectors = [
            "select[name='YEAR']",
            "select[name='year']", 
            "select[id*='year']",
            "select[id*='Year']",
            "select[class*='year']",
            "select[class*='Year']",
            "select",  # 所有select元素
            "input[name*='year']",
            "input[id*='year']"
        ]
        
        found_selectors = []
        
        for i, selector in enumerate(year_selectors):
            try:
                print(f"🔍 測試選擇器 {i+1}/{len(year_selectors)}: {selector}")
                
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ 找到 {len(elements)} 個元素")
                    
                    for j, elem in enumerate(elements):
                        try:
                            tag = elem.tag_name
                            name = elem.get_attribute('name') or 'N/A'
                            id_attr = elem.get_attribute('id') or 'N/A'
                            class_attr = elem.get_attribute('class') or 'N/A'
                            text = elem.text[:30] if elem.text else 'N/A'
                            visible = elem.is_displayed()
                            enabled = elem.is_enabled()
                            
                            print(f"    元素{j+1}: <{tag}> name='{name}' id='{id_attr}' class='{class_attr}'")
                            print(f"           text='{text}' visible={visible} enabled={enabled}")
                            
                            # 如果是select元素，檢查選項
                            if tag == 'select':
                                try:
                                    from selenium.webdriver.support.ui import Select
                                    select_obj = Select(elem)
                                    options = [opt.text for opt in select_obj.options]
                                    print(f"           選項: {options[:10]}...")  # 只顯示前10個選項
                                    found_selectors.append((selector, elem, options))
                                except Exception as e:
                                    print(f"           選項讀取失敗: {e}")
                            
                        except Exception as e:
                            print(f"    元素{j+1}: 讀取失敗 - {e}")
                else:
                    print(f"  ❌ 未找到元素")
                    
            except Exception as e:
                print(f"  ⚠️ 選擇器失敗: {e}")
        
        # 搜尋所有包含年份的元素
        print(f"\n🔍 搜尋所有年份相關元素...")
        try:
            year_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '年') or contains(@name, 'year') or contains(@id, 'year') or contains(@class, 'year')]")
            print(f"📋 找到 {len(year_elements)} 個年份相關元素")
            
            for i, elem in enumerate(year_elements[:10]):  # 只顯示前10個
                try:
                    tag = elem.tag_name
                    name = elem.get_attribute('name') or ''
                    id_attr = elem.get_attribute('id') or ''
                    class_attr = elem.get_attribute('class') or ''
                    text = elem.text[:50] if elem.text else ''
                    
                    print(f"  {i+1}. <{tag}> '{text}' name='{name}' id='{id_attr}' class='{class_attr}'")
                except Exception as e:
                    print(f"  {i+1}. 讀取失敗: {e}")
                    
        except Exception as e:
            print(f"⚠️ 年份元素搜尋失敗: {e}")
        
        # 檢查頁面URL是否包含年份參數
        current_url = driver.current_url
        print(f"\n🌐 當前URL: {current_url}")
        
        # 檢查是否有JavaScript錯誤
        try:
            logs = driver.get_log('browser')
            if logs:
                print(f"\n📝 瀏覽器日誌 (前5條):")
                for log in logs[:5]:
                    print(f"  {log['level']}: {log['message'][:100]}...")
        except:
            print("📝 無法獲取瀏覽器日誌")
        
        # 總結
        print(f"\n📊 {mode_name} 測試總結:")
        print(f"  • 頁面載入: {'✅ 成功' if html_length > 1000 else '❌ 可能失敗'}")
        print(f"  • 表格數量: {len(tables)}")
        print(f"  • 年份選擇器: {'✅ 找到' if found_selectors else '❌ 未找到'}")
        print(f"  • 年份相關元素: {len(year_elements) if 'year_elements' in locals() else 0}")
        
        return {
            'mode': mode_name,
            'page_loaded': html_length > 1000,
            'tables_count': len(tables),
            'year_selectors_found': len(found_selectors),
            'year_elements_count': len(year_elements) if 'year_elements' in locals() else 0,
            'found_selectors': found_selectors
        }
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return None
        
    finally:
        if driver:
            driver.quit()
            print(f"🔚 {mode_name} 瀏覽器已關閉")

def compare_headless_vs_headed():
    """比較無頭模式和有頭模式的差異"""
    print("🧪 年份選擇器無頭模式診斷工具")
    print("=" * 80)
    
    # 測試有頭模式
    headed_result = test_year_selector_with_headless(headless=False)
    
    # 等待一下
    time.sleep(2)
    
    # 測試無頭模式
    headless_result = test_year_selector_with_headless(headless=True)
    
    # 比較結果
    print(f"\n📊 比較結果")
    print("=" * 80)
    
    if headed_result and headless_result:
        print(f"{'項目':<20} {'有頭模式':<15} {'無頭模式':<15} {'差異'}")
        print("-" * 70)
        
        # 頁面載入
        headed_loaded = headed_result['page_loaded']
        headless_loaded = headless_result['page_loaded']
        load_diff = "✅ 相同" if headed_loaded == headless_loaded else "❌ 不同"
        print(f"{'頁面載入':<20} {headed_loaded:<15} {headless_loaded:<15} {load_diff}")
        
        # 表格數量
        headed_tables = headed_result['tables_count']
        headless_tables = headless_result['tables_count']
        table_diff = "✅ 相同" if headed_tables == headless_tables else "❌ 不同"
        print(f"{'表格數量':<20} {headed_tables:<15} {headless_tables:<15} {table_diff}")
        
        # 年份選擇器
        headed_selectors = headed_result['year_selectors_found']
        headless_selectors = headless_result['year_selectors_found']
        selector_diff = "✅ 相同" if headed_selectors == headless_selectors else "❌ 不同"
        print(f"{'年份選擇器':<20} {headed_selectors:<15} {headless_selectors:<15} {selector_diff}")
        
        # 年份元素
        headed_elements = headed_result['year_elements_count']
        headless_elements = headless_result['year_elements_count']
        element_diff = "✅ 相同" if headed_elements == headless_elements else "❌ 不同"
        print(f"{'年份元素':<20} {headed_elements:<15} {headless_elements:<15} {element_diff}")
        
        # 結論
        print(f"\n💡 結論:")
        if headed_selectors == 0 and headless_selectors == 0:
            print("  • 兩種模式都未找到年份選擇器")
            print("  • 這可能表示GoodInfo網站沒有年份選擇功能")
            print("  • 或者年份選擇器使用了特殊的實現方式")
        elif headed_selectors > 0 and headless_selectors == 0:
            print("  • 有頭模式找到年份選擇器，無頭模式未找到")
            print("  • 可能是JavaScript載入問題或元素渲染問題")
        elif headed_selectors == 0 and headless_selectors > 0:
            print("  • 無頭模式找到年份選擇器，有頭模式未找到")
            print("  • 這種情況比較少見")
        else:
            print("  • 兩種模式都找到年份選擇器")
            print("  • 無頭模式對年份選擇器沒有影響")
    
    else:
        print("❌ 測試結果不完整，無法進行比較")

if __name__ == "__main__":
    compare_headless_vs_headed()
