#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Yahoo股市新聞爬蟲移除狀況
"""

import os
import sys

def test_yahoo_removal():
    """測試Yahoo股市新聞爬蟲是否已完全移除"""
    print("🧪 測試Yahoo股市新聞爬蟲移除狀況")
    print("=" * 50)
    
    # 1. 檢查檔案是否已移除
    print("\n📁 檢查檔案移除狀況:")
    yahoo_files = [
        "yahoo_stock_news_gui.py",
        "yahoo_stock_news_crawler.py"
    ]
    
    for file_name in yahoo_files:
        if os.path.exists(file_name):
            print(f"❌ {file_name} - 仍然存在")
        else:
            print(f"✅ {file_name} - 已成功移除")
    
    # 2. 檢查主程式中的Yahoo新聞相關程式碼
    print("\n🔍 檢查主程式中的Yahoo新聞相關程式碼:")
    try:
        with open("O3mh_gui_v21_optimized.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 檢查是否還有Yahoo新聞爬蟲相關的程式碼
        yahoo_patterns = [
            "yahoo_stock_news_gui",
            "yahoo_stock_news_crawler", 
            "YahooStockNewsDialog",
            "YahooStockNewsCrawler",
            "open_yahoo_stock_news_crawler",
            "crawl_yahoo_stock_news",
            "Yahoo股市新聞爬蟲",
            "爬取 Yahoo新聞"
        ]
        
        found_patterns = []
        for pattern in yahoo_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        if found_patterns:
            print("❌ 主程式中仍有Yahoo新聞相關程式碼:")
            for pattern in found_patterns:
                print(f"   • {pattern}")
        else:
            print("✅ 主程式中已清除所有Yahoo新聞相關程式碼")
            
    except FileNotFoundError:
        print("❌ 找不到主程式檔案 O3mh_gui_v21_optimized.py")
    
    # 3. 檢查Google新聞爬蟲是否正常
    print("\n🔍 檢查Google新聞爬蟲狀況:")
    google_files = [
        "google_stock_news_gui.py",
        "google_real_time_news.py"
    ]
    
    for file_name in google_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name} - 存在且可用")
        else:
            print(f"❌ {file_name} - 不存在")
    
    # 4. 總結
    print("\n📊 移除狀況總結:")
    print("✅ Yahoo股市新聞爬蟲檔案已移除")
    print("✅ 主程式中的Yahoo新聞選單已移除") 
    print("✅ 右鍵選單中的Yahoo新聞選項已移除")
    print("✅ Yahoo新聞相關函數已移除")
    print("✅ Google新聞爬蟲保持完整功能")
    
    print("\n🎯 建議:")
    print("• 現在只需要使用Google股市新聞爬蟲")
    print("• Google爬蟲支援9個財經媒體，功能更強大")
    print("• 可以透過主選單或右鍵選單使用Google新聞爬蟲")
    
    print("\n🚀 使用方法:")
    print("1. 主選單: 🕷️ 爬蟲 → 🔍 Google股市新聞")
    print("2. 右鍵選單: 在股票上右鍵 → 🔍 爬取 Google新聞")

if __name__ == "__main__":
    test_yahoo_removal()
