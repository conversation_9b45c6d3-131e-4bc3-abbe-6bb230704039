#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化編譯腳本 - 只包含最核心的功能
確保編譯成功和穩定運行
"""

import os
import sys
import subprocess
import shutil
import time

def create_minimal_spec():
    """創建最小化的 spec 文件"""
    print("📝 創建最小化編譯配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 隱藏導入 - 只包含最基本的模組
hiddenimports = [
    # 系統核心模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6 最小集合
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 數據處理最小集合
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    
    # 網路最小集合
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    
    # 其他必要
    'setuptools',
    'pkg_resources',
]

# 排除所有可能有問題的模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    'twstock',
    'twstock.codes',
    'twstock.stock',
    'yfinance',
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    'matplotlib',
    'seaborn',
    'pyqtgraph',  # 排除有依賴問題的 pyqtgraph
    'xlsxwriter', # 可能有問題
    'selenium',   # 可能有問題
    'webdriver_manager',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StockAnalyzer_Minimal',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('minimal_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最小化編譯配置已創建")

def clean_and_compile():
    """清理並編譯"""
    print("🧹 清理編譯環境...")
    
    # 清理目錄
    if os.path.exists('build'):
        try:
            shutil.rmtree('build')
            print("✅ 清理 build")
        except Exception as e:
            print(f"⚠️ 無法清理 build: {e}")
    
    time.sleep(2)
    
    print("🔨 開始最小化編譯...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'minimal_compile.spec'
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/StockAnalyzer_Minimal.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_minimal_launcher()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            if result.stderr:
                print("錯誤輸出:")
                print(result.stderr[-1500:])
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_minimal_launcher():
    """創建最小化啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最小化穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        最小化穩定版 - 核心功能
echo ========================================
echo.

if exist "dist\\StockAnalyzer_Minimal.exe" (
    echo ✅ 找到最小化穩定版
    echo 🚀 正在啟動...
    echo.
    echo 💡 最小化穩定版特點：
    echo    ✓ 只包含最核心的功能模組
    echo    ✓ 排除所有有問題的依賴
    echo    ✓ 最高的穩定性和兼容性
    echo    ✓ 最小的檔案大小
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Minimal.exe"
    
    echo ✅ 最小化穩定版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到最小化穩定版
    echo.
    echo 請重新編譯：
    echo    python minimal_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 功能說明：
echo    ✓ 基本股票列表和篩選功能
echo    ✓ 數據查詢和顯示功能
echo    ✓ Excel 報告導出功能
echo    ✓ 基本的用戶界面操作
echo.
echo ⚠️ 注意：
echo    - 圖表功能可能受限
echo    - 部分進階功能不可用
echo    - 但核心選股功能完全保留
echo.

timeout /t 8 >nul
'''
    
    with open('啟動最小化版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建最小化啟動腳本: 啟動最小化版.bat")

def main():
    """主函數"""
    print("🚀 台股智能選股系統 - 最小化編譯器")
    print("=" * 60)
    print("策略：只包含最核心功能，確保100%穩定")
    print()
    
    # 步驟1: 創建配置
    create_minimal_spec()
    print()
    
    # 步驟2: 清理並編譯
    if clean_and_compile():
        print("\n🎉 最小化穩定版編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/StockAnalyzer_Minimal.exe")
        print("   - 啟動最小化版.bat")
        print("\n🚀 使用方法:")
        print("   雙擊執行: 啟動最小化版.bat")
        print("\n✨ 最小化版特點:")
        print("   ✓ 只包含最核心的功能模組")
        print("   ✓ 排除所有可能有問題的依賴")
        print("   ✓ 最高的穩定性和兼容性")
        print("   ✓ 最小的檔案大小和最快的啟動速度")
        print("\n📋 功能範圍:")
        print("   ✓ 股票列表和基本篩選")
        print("   ✓ 數據查詢和顯示")
        print("   ✓ Excel 報告導出")
        print("   ✓ 基本用戶界面操作")
        print("\n⚠️ 限制:")
        print("   - 圖表功能可能受限")
        print("   - 部分進階功能不可用")
        print("   - 但核心選股功能完全保留")
        return True
    else:
        print("\n❌ 編譯失敗！")
        return False

if __name__ == "__main__":
    main()
