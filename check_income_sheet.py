#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 income_sheet.pkl 的狀態
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import pandas as pd
import datetime
import os

def check_income_sheet():
    """檢查 income_sheet.pkl 的狀態"""
    print("🔍 檢查 income_sheet.pkl 狀態")
    print("=" * 60)
    
    file_path = 'history/tables/income_sheet.pkl'
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 讀取檔案
        df = pd.read_pickle(file_path)
        
        print(f"📊 基本資訊:")
        print(f"   檔案大小: {os.path.getsize(file_path) / 1024 / 1024:.2f} MB")
        print(f"   資料筆數: {len(df):,}")
        print(f"   資料形狀: {df.shape}")
        print(f"   索引: {df.index.names}")
        print(f"   欄位數: {len(df.columns)}")
        
        # 檢查日期範圍
        if 'date' in df.index.names:
            dates = df.index.get_level_values('date')
            print(f"\n📅 日期資訊:")
            print(f"   最早日期: {dates.min()}")
            print(f"   最新日期: {dates.max()}")
            print(f"   日期數量: {len(dates.unique())} 個")
            
            # 計算需要更新的時間
            latest_date = dates.max()
            today = datetime.datetime.now()
            
            if hasattr(latest_date, 'to_pydatetime'):
                latest_date = latest_date.to_pydatetime()
            
            days_behind = (today - latest_date).days
            print(f"   落後天數: {days_behind} 天")
            
            # 檢查是否需要更新
            if days_behind > 90:  # 超過3個月
                print(f"   ⚠️ 資料較舊，建議更新")
            elif days_behind > 30:  # 超過1個月
                print(f"   💡 可考慮更新")
            else:
                print(f"   ✅ 資料較新")
        
        # 檢查股票數量
        if 'stock_id' in df.index.names:
            stock_ids = df.index.get_level_values('stock_id')
            print(f"\n📈 股票資訊:")
            print(f"   股票數量: {len(stock_ids.unique())} 檔")
            
            # 顯示一些樣本股票
            sample_stocks = stock_ids.unique()[:5]
            print(f"   樣本股票: {list(sample_stocks)}")
        
        # 檢查欄位
        print(f"\n📋 欄位資訊:")
        print(f"   欄位列表: {list(df.columns)[:10]}...")  # 只顯示前10個
        
        # 顯示樣本資料
        print(f"\n📊 樣本資料:")
        print(df.head(3))
        
        return True
        
    except Exception as e:
        print(f"❌ 讀取檔案失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_crawler_function():
    """檢查對應的爬蟲函數"""
    print(f"\n🔍 檢查對應的爬蟲函數")
    print("=" * 60)
    
    try:
        # 添加 finlab 路徑
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        finlab_path = os.path.join(current_dir, 'finlab')
        if finlab_path not in sys.path:
            sys.path.insert(0, finlab_path)
        
        # 檢查可用的爬蟲函數
        import crawler
        
        # 尋找與 income_sheet 相關的函數
        income_functions = []
        for attr_name in dir(crawler):
            if 'income' in attr_name.lower() and callable(getattr(crawler, attr_name)):
                income_functions.append(attr_name)
        
        print(f"找到的 income 相關函數: {income_functions}")
        
        # 檢查是否有 crawl_finance_statement_by_date
        if hasattr(crawler, 'crawl_finance_statement_by_date'):
            print(f"✅ 找到 crawl_finance_statement_by_date 函數")
            return 'crawl_finance_statement_by_date'
        elif hasattr(crawler, 'crawl_income_sheet'):
            print(f"✅ 找到 crawl_income_sheet 函數")
            return 'crawl_income_sheet'
        else:
            print(f"⚠️ 未找到明確的 income_sheet 爬蟲函數")
            return None
            
    except Exception as e:
        print(f"❌ 檢查爬蟲函數失敗: {str(e)}")
        return None

def main():
    """主函數"""
    print("🔧 Income Sheet 檢查工具")
    print("=" * 60)
    print("🎯 目標: 檢查 income_sheet.pkl 狀態並準備更新")
    print("=" * 60)
    
    # 檢查檔案狀態
    file_ok = check_income_sheet()
    
    # 檢查爬蟲函數
    crawler_func = check_crawler_function()
    
    if file_ok and crawler_func:
        print(f"\n🎉 準備就緒!")
        print(f"💡 可以使用 auto_update.py 更新 income_sheet:")
        print(f"   1. 在 auto_update.py 中啟用對應的資料表")
        print(f"   2. 運行: python auto_update.py")
        print(f"   3. 爬蟲函數: {crawler_func}")
    else:
        print(f"\n⚠️ 需要進一步檢查")

if __name__ == "__main__":
    main()
