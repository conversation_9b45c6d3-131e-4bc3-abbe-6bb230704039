
# 🔬 研發魔人策略 - 添加完成總結

## 📋 策略概述

### 🎯 策略特色
- **策略類型**: 科技股短線策略
- **核心理念**: 鎖定專注研發的 top 科技公司
- **操作方式**: 操作股價創新高的強勢股，做短線操作
- **換股週期**: 每兩週換股

### 🔬 研發導向邏輯
- **研發費用率**: 排名前20%的高研發投入公司
- **科技創新**: 專注於具有創新能力的科技公司
- **成長潛力**: 營收連2月年增確保成長動能

### 📈 短線操作機制
- **進場條件**: 6個條件同時滿足
- **賣出條件**: 股價連2日落於月線下
- **風險控制**: 10%停損，15%停利
- **持股限制**: 最多持有10檔股票

## ✅ 實現功能

### 🔧 技術實現
- ✅ 策略檢查方法 (`check_rd_wizard_strategy`)
- ✅ 研發投入強度計算 (`calculate_rd_intensity_proxy`)
- ✅ 營收成長檢查 (`check_revenue_growth_2months`)
- ✅ 10日新高檢查 (`check_10_day_new_high`)
- ✅ 多頭排列檢查 (`check_bullish_alignment`)
- ✅ 專用表格設置 (`setup_rd_wizard_strategy_table`)

### 📊 評分系統 (100分制)
- **研發投入** (30分): 當期研發費用率排名前20%
- **營收成長** (20分): 營收連2月年增
- **10日新高** (15分): 股價創10日新高
- **多頭排列** (15分): 股價多頭排列
- **價格合理** (10分): 股價小於200
- **成交量** (10分): 近3日均量>200張

### 📋 表格顯示
- 股票代碼、股票名稱、收盤價（<200標綠）
- 研發魔人評分（彩色標示）
- 研發投入、營收成長、10日新高
- 多頭排列、3日均量（>200張標綠）、策略狀態

### 📖 說明文檔
- ✅ 策略概述（含模擬數據警告）
- ✅ 詳細技術說明（6個核心條件）
- ✅ 實戰使用指南

## ⚠️ 模擬數據警告

### 🚨 目前使用模擬數據的項目
1. **研發費用率** → 使用技術指標強度模擬
2. **營收成長** → 使用價格趨勢模擬

### 🔗 建議真實數據源
- **公開資訊觀測站**: 財務報表中的研發費用
- **FinMind財報API**: 研究發展費、營業收入淨額
- **月營收數據**: 公開資訊觀測站月營收公告

## 🚀 使用指南

### 📋 操作步驟
1. **選擇策略**: 在下拉選單選擇「研發魔人」
2. **執行篩選**: 點擊執行策略
3. **查看評分**: 重點關注80分以上股票
4. **基本面確認**: 檢查研發投入和營收成長
5. **技術面驗證**: 確認10日新高和多頭排列
6. **短線操作**: 每兩週重新評估持股

### 💡 投資建議
- **適合對象**: 重視科技創新、偏好短線操作的積極型投資者
- **持股期間**: 建議短期持有（2週-1個月）
- **風險控制**: 10%停損，15%停利
- **持股分散**: 最多持有10檔股票

## 🎊 策略優勢

### 🌟 核心優勢
1. **科技導向**: 專注高研發投入的科技公司
2. **短線操作**: 每兩週換股，快速捕捉機會
3. **雙重篩選**: 結合基本面和技術面
4. **風險控制**: 明確的進出場條件

### 🎯 適用情境
- **科技股投資**: 專注於科技創新領域
- **短線交易**: 適合2週-1個月的操作週期
- **強勢股操作**: 重視股價創新高的突破
- **研發導向**: 看重公司的研發投入

## 📊 原始策略邏輯

### 🔍 FinLab原始程式碼邏輯
```python
# 基本面條件
research_ratio_rank >= 0.8  # 研發費用率排名前20%
(month_rev_growth > 0).sustain(2)  # 營收連2月年增

# 技術面條件
close == close.rolling(10).max()  # 股價創10日新高
close > sma20 > sma60  # 股價多頭排列
close < 200  # 股價小於200
vol.average(3) > 200000  # 近3日均量>200張

# 賣出條件
(close < sma20).sustain(2)  # 股價連2日落於月線下
```

### 📈 交易參數
- **重新平衡**: 每兩週 (resample='2W')
- **持股限制**: 最多10檔 (nstocks_limit=10)
- **部位限制**: 每檔10% (position_limit=0.1)
- **停損**: 10% (stop_loss=0.1)
- **停利**: 15% (take_profit=0.15)
- **手續費**: 1.425/1000/3

---

**🔬 研發魔人策略已成功添加到系統中！**

**現在您可以使用這個專門針對高研發投入科技公司設計的短線操作策略，捕捉科技創新帶來的投資機會！**
