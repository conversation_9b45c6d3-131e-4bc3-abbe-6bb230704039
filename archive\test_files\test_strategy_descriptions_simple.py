#!/usr/bin/env python3
"""
簡化版5分鐘策略說明測試
只測試文本內容，不涉及GUI
"""

import sys
import os

def test_strategy_descriptions_content():
    """測試策略說明內容"""
    print("🧪 測試5分鐘策略說明內容...")
    
    try:
        # 添加當前目錄到Python路徑
        sys.path.insert(0, os.getcwd())
        
        # 導入主程式模組
        import O3mh_gui_v21_optimized as main_module
        
        print("✅ 主程式模組載入成功")
        
        # 創建一個臨時類來測試策略說明內容
        class TempStrategyInfo:
            def __init__(self, strategy_name):
                self.strategy_name = strategy_name
            
            def get_usage_guide(self):
                """獲取使用建議 - 複製自主程式"""
                usage_guides = {
                    "5分鐘突破策略": """
                        <h2>⚡ 5分鐘突破策略 - 短線突破交易專家</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>基於價格突破關鍵阻力/支撐位的短線交易策略</strong></p>
                        <ul>
                            <li><strong>突破邏輯：</strong>監控20期高低點突破</li>
                            <li><strong>量能確認：</strong>成交量必須放大1.5倍以上</li>
                            <li><strong>時效性：</strong>適合5分鐘級別的快速決策</li>
                        </ul>
                    """,
                    "5分鐘均值回歸": """
                        <h2>🔄 5分鐘均值回歸 - 震盪市場獲利專家</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>利用價格偏離均線的回歸特性進行短線交易</strong></p>
                        <ul>
                            <li><strong>回歸理論：</strong>價格偏離均線後會回歸</li>
                            <li><strong>超買超賣：</strong>結合RSI指標判斷極值</li>
                            <li><strong>震盪獲利：</strong>適合橫盤整理的市場環境</li>
                        </ul>
                    """,
                    "5分鐘動量策略": """
                        <h2>🚀 5分鐘動量策略 - 趨勢追蹤利器</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>捕捉價格動量和趨勢變化的短線策略</strong></p>
                        <ul>
                            <li><strong>動量追蹤：</strong>跟隨價格動量方向</li>
                            <li><strong>MACD信號：</strong>利用MACD金叉死叉確認</li>
                            <li><strong>成交量確認：</strong>動量必須有成交量支撐</li>
                        </ul>
                    """,
                    "5分鐘量價策略": """
                        <h2>📊 5分鐘量價策略 - 量價關係分析專家</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>分析成交量與價格的配合關係，發現交易機會</strong></p>
                        <ul>
                            <li><strong>量價配合：</strong>價格變化必須有成交量支撐</li>
                            <li><strong>量價背離：</strong>量價不配合時的反轉機會</li>
                            <li><strong>爆量分析：</strong>異常成交量的市場含義</li>
                        </ul>
                    """,
                    "5分鐘剝頭皮": """
                        <h2>⚡ 5分鐘剝頭皮 - 超短線交易專家</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>超短線快進快出，賺取微小價差的交易策略</strong></p>
                        <ul>
                            <li><strong>快進快出：</strong>持倉時間通常1-3分鐘</li>
                            <li><strong>微利累積：</strong>每次賺取0.3-0.8%的小利潤</li>
                            <li><strong>低風險：</strong>嚴格控制單次虧損</li>
                        </ul>
                    """,
                    "5分鐘趨勢跟隨": """
                        <h2>📈 5分鐘趨勢跟隨 - 短期趨勢捕捉專家</h2>
                        
                        <h3>🎯 策略核心理念</h3>
                        <p><strong>跟隨短期趨勢方向，在趨勢中獲利</strong></p>
                        <ul>
                            <li><strong>趨勢識別：</strong>通過均線排列判斷趨勢</li>
                            <li><strong>順勢操作：</strong>只做順勢交易，不逆勢</li>
                            <li><strong>趨勢強度：</strong>評估趨勢的持續性</li>
                        </ul>
                    """
                }
                
                return usage_guides.get(self.strategy_name, f"""
                    <h2>💡 {self.strategy_name} - 使用建議</h2>
                    <p>這是一個自定義策略，建議您：</p>
                    <ul>
                        <li>仔細研究策略的具體條件</li>
                        <li>在小資金上先測試策略效果</li>
                        <li>根據市場環境調整使用頻率</li>
                        <li>設定合理的停利停損點</li>
                        <li>定期檢討策略績效</li>
                    </ul>
                """)
        
        # 測試的5分鐘策略列表
        five_min_strategies = [
            "5分鐘突破策略",
            "5分鐘均值回歸", 
            "5分鐘動量策略",
            "5分鐘量價策略",
            "5分鐘剝頭皮",
            "5分鐘趨勢跟隨"
        ]
        
        print(f"\n📊 測試策略說明內容:")
        
        success_count = 0
        
        for strategy_name in five_min_strategies:
            try:
                # 創建策略說明實例
                strategy_info = TempStrategyInfo(strategy_name)
                
                # 獲取策略說明內容
                usage_guide = strategy_info.get_usage_guide()
                
                # 檢查說明內容
                if strategy_name in usage_guide and "這是一個自定義策略" not in usage_guide:
                    print(f"   ✅ {strategy_name}: 自定義說明已添加")
                    
                    # 檢查關鍵內容
                    key_elements = ["策略核心理念", "突破", "動量", "量價", "剝頭皮", "趨勢", "回歸"]
                    found_elements = [elem for elem in key_elements if elem in usage_guide]
                    
                    if len(found_elements) >= 2:
                        print(f"      ✅ 包含關鍵元素: {', '.join(found_elements[:3])}...")
                    else:
                        print(f"      ⚠️ 關鍵元素較少")
                        
                    # 檢查內容長度
                    content_length = len(usage_guide)
                    if content_length > 500:
                        print(f"      ✅ 內容豐富 ({content_length} 字符)")
                    else:
                        print(f"      ⚠️ 內容較少 ({content_length} 字符)")
                    
                    success_count += 1
                        
                else:
                    print(f"   ❌ {strategy_name}: 使用預設說明")
                    
            except Exception as e:
                print(f"   ❌ {strategy_name}: 測試失敗 - {e}")
        
        print(f"\n📈 測試結果:")
        print(f"   成功添加說明: {success_count}/{len(five_min_strategies)}")
        print(f"   成功率: {success_count/len(five_min_strategies)*100:.1f}%")
        
        return success_count == len(five_min_strategies)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_strategy_features():
    """顯示策略特色說明"""
    print(f"\n🎯 5分鐘策略特色說明:")
    
    strategies = {
        "5分鐘突破策略": {
            "emoji": "⚡",
            "核心": "捕捉價格突破關鍵位",
            "適用": "開盤突破、技術位突破",
            "特點": "快速決策、量能確認"
        },
        "5分鐘均值回歸": {
            "emoji": "🔄", 
            "核心": "利用價格回歸特性",
            "適用": "震盪市場、超買超賣",
            "特點": "反向操作、風險較低"
        },
        "5分鐘動量策略": {
            "emoji": "🚀",
            "核心": "追蹤價格動量變化", 
            "適用": "趨勢市場、MACD信號",
            "特點": "順勢而為、移動止盈"
        },
        "5分鐘量價策略": {
            "emoji": "📊",
            "核心": "分析量價配合關係",
            "適用": "爆量突破、量價背離", 
            "特點": "成交量確認、主力動向"
        },
        "5分鐘剝頭皮": {
            "emoji": "⚡",
            "核心": "超短線快進快出",
            "適用": "低波動、流動性好",
            "特點": "微利累積、嚴格止損"
        },
        "5分鐘趨勢跟隨": {
            "emoji": "📈",
            "核心": "跟隨短期趨勢方向",
            "適用": "趨勢明確、均線排列",
            "特點": "順勢操作、趨勢確認"
        }
    }
    
    for name, info in strategies.items():
        print(f"\n{info['emoji']} {name}:")
        print(f"   核心理念: {info['核心']}")
        print(f"   適用場景: {info['適用']}")
        print(f"   策略特點: {info['特點']}")

def show_usage_recommendations():
    """顯示使用建議"""
    print(f"\n💡 5分鐘策略使用建議:")
    
    recommendations = {
        "新手入門": [
            "建議從「5分鐘均值回歸」開始，風險相對較低",
            "先在模擬環境中練習，熟悉策略邏輯",
            "每種策略都要詳讀說明，理解操作要點"
        ],
        "進階應用": [
            "可以組合使用多種策略，提高成功率",
            "根據市場環境選擇合適的策略",
            "建立自己的交易日誌，記錄策略效果"
        ],
        "風險控制": [
            "嚴格執行止損止盈，不可心存僥倖",
            "控制單次交易規模，不超過總資金5%",
            "避免在重大消息前後使用短線策略"
        ],
        "市場環境": [
            "趨勢市場：優先使用動量策略、趨勢跟隨",
            "震盪市場：優先使用均值回歸、量價策略", 
            "低波動：可以考慮剝頭皮策略",
            "突破行情：重點關注突破策略"
        ]
    }
    
    for category, tips in recommendations.items():
        print(f"\n🎯 {category}:")
        for tip in tips:
            print(f"   • {tip}")

if __name__ == "__main__":
    print("🚀 開始測試5分鐘策略說明功能...")
    
    # 執行測試
    result = test_strategy_descriptions_content()
    
    # 顯示策略特色
    show_strategy_features()
    
    # 顯示使用建議
    show_usage_recommendations()
    
    # 總結
    print(f"\n🎉 測試完成！")
    
    if result:
        print("✅ 5分鐘策略說明功能測試通過！")
    else:
        print("⚠️ 部分功能需要檢查")
    
    print(f"\n📖 如何查看策略說明:")
    print(f"   1. 啟動主程式: python O3mh_gui_v21_optimized.py")
    print(f"   2. 在策略選單中選擇任一5分鐘策略")
    print(f"   3. 點擊「📖 策略說明」按鈕")
    print(f"   4. 在彈出的對話框中查看詳細說明")
    
    print(f"\n🎯 策略說明包含的內容:")
    print(f"   📊 策略核心理念與邏輯")
    print(f"   🎯 最佳使用時機與場景")
    print(f"   🔧 詳細的操作要點")
    print(f"   🛡️ 完整的風險控制方法")
    print(f"   💡 實戰操作技巧")
    print(f"   ⚠️ 重要注意事項")
    
    print(f"\n🚀 現在您可以充分理解和善用每種5分鐘策略了！")
    print(f"每種策略都有詳細的使用指南，幫助您在不同市場環境下選擇最適合的策略。")
