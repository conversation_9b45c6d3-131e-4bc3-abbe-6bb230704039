@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         最終穩定版 - 核心功能版
echo ========================================
echo.

if exist "dist\StockAnalyzer_Final.exe" (
    echo ✅ 找到最終穩定版
    echo 🚀 正在啟動...
    echo.
    echo 💡 最終穩定版特點：
    echo    ✓ 排除有問題的模組，確保穩定
    echo    ✓ 包含核心股票分析功能
    echo    ✓ 完全獨立，無外部依賴
    echo    ✓ 優化啟動速度
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Final.exe"
    
    echo ✅ 最終穩定版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到最終穩定版
    echo.
    echo 請重新編譯：
    echo    python final_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 使用提示：
echo    - 如果程式沒有顯示，請檢查工作列
echo    - 部分進階功能可能需要網路連接
echo    - 如有問題，請檢查防毒軟體設定
echo.

timeout /t 5 >nul
