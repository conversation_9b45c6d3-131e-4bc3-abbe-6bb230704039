#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保留 twse_divide_ratio.db 中 2018-01-01 以後的資料
移除之前的資料，檔名保持不變
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime

def filter_data_from_2018():
    """保留2018-01-01以後的資料"""
    
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    backup_file = 'D:/Finlab/history/tables/twse_divide_ratio_full_backup.db'
    
    print("🔄 開始過濾 twse_divide_ratio.db 資料")
    print("=" * 60)
    print(f"📂 處理檔案: {db_file}")
    print(f"🎯 保留條件: 2018-01-01 以後的資料")
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ DB 檔案不存在: {db_file}")
        return False
    
    try:
        # 備份原始檔案
        print(f"\n💾 備份原始檔案到: {backup_file}")
        import shutil
        shutil.copy2(db_file, backup_file)
        print(f"✅ 備份完成")
        
        # 讀取現有資料
        print(f"\n📂 讀取現有資料...")
        conn = sqlite3.connect(db_file)
        df = pd.read_sql('SELECT * FROM twse_divide_ratio', conn)
        conn.close()
        
        print(f"✅ 成功讀取資料")
        print(f"   原始資料筆數: {len(df):,}")
        
        # 檢查日期欄位
        if 'date' not in df.columns:
            print(f"❌ 找不到 'date' 欄位")
            return False
        
        # 轉換日期格式
        df['date'] = pd.to_datetime(df['date'])
        
        # 檢查原始日期範圍
        min_date = df['date'].min()
        max_date = df['date'].max()
        print(f"   原始日期範圍: {min_date.strftime('%Y-%m-%d')} ~ {max_date.strftime('%Y-%m-%d')}")
        
        # 過濾資料：保留 2018-01-01 以後的資料
        cutoff_date = pd.Timestamp('2018-01-01')
        df_filtered = df[df['date'] >= cutoff_date].copy()
        
        print(f"\n🔍 過濾結果:")
        print(f"   過濾前: {len(df):,} 筆")
        print(f"   過濾後: {len(df_filtered):,} 筆")
        print(f"   移除筆數: {len(df) - len(df_filtered):,} 筆")
        
        if len(df_filtered) == 0:
            print(f"⚠️ 過濾後沒有資料，請檢查日期條件")
            return False
        
        # 檢查過濾後的日期範圍
        filtered_min_date = df_filtered['date'].min()
        filtered_max_date = df_filtered['date'].max()
        print(f"   過濾後日期範圍: {filtered_min_date.strftime('%Y-%m-%d')} ~ {filtered_max_date.strftime('%Y-%m-%d')}")
        
        # 檢查股票數量
        original_stocks = df['stock_id'].nunique()
        filtered_stocks = df_filtered['stock_id'].nunique()
        print(f"   原始股票數: {original_stocks:,} 檔")
        print(f"   過濾後股票數: {filtered_stocks:,} 檔")
        
        # 顯示按年份的資料分布
        print(f"\n📊 按年份的資料分布:")
        year_counts = df_filtered.groupby(df_filtered['date'].dt.year).size()
        for year, count in year_counts.items():
            print(f"   {year}: {count:,} 筆")
        
        # 保存過濾後的資料到原檔案
        print(f"\n💾 保存過濾後的資料...")
        conn = sqlite3.connect(db_file)
        
        # 刪除原表格
        cursor = conn.cursor()
        cursor.execute('DROP TABLE IF EXISTS twse_divide_ratio')
        
        # 保存新資料
        df_filtered.to_sql('twse_divide_ratio', conn, if_exists='replace', index=False)
        
        # 重新創建索引
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON twse_divide_ratio(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON twse_divide_ratio(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON twse_divide_ratio(stock_id, date)')
            print("✅ 已重新創建資料庫索引")
        except Exception as e:
            print(f"⚠️ 創建索引時出現警告: {e}")
        
        conn.commit()
        
        # 獲取最終統計
        cursor.execute('SELECT COUNT(*) FROM twse_divide_ratio')
        final_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM twse_divide_ratio')
        final_stocks = cursor.fetchone()[0]
        
        cursor.execute('SELECT MIN(date), MAX(date) FROM twse_divide_ratio WHERE date IS NOT NULL')
        date_range_result = cursor.fetchone()
        final_min_date, final_max_date = date_range_result if date_range_result and date_range_result[0] else (None, None)
        
        conn.close()
        
        # 計算檔案大小變化
        original_size = os.path.getsize(backup_file) / 1024 / 1024  # MB
        new_size = os.path.getsize(db_file) / 1024 / 1024  # MB
        
        print(f"\n✅ 過濾完成！")
        print(f"📊 最終統計:")
        print(f"   檔案位置: {db_file}")
        print(f"   記錄數: {final_count:,} 筆")
        print(f"   股票數: {final_stocks:,} 檔")
        print(f"   日期範圍: {final_min_date} ~ {final_max_date}")
        print(f"   原始檔案大小: {original_size:.1f} MB")
        print(f"   新檔案大小: {new_size:.1f} MB")
        print(f"   節省空間: {original_size - new_size:.1f} MB ({(original_size - new_size)/original_size*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 過濾過程失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_filtered_data():
    """驗證過濾後的資料"""
    
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    
    print(f"\n🔍 驗證過濾後的資料...")
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查是否有2018年之前的資料
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM twse_divide_ratio WHERE date < '2018-01-01'")
        before_2018_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM twse_divide_ratio WHERE date >= '2018-01-01'")
        after_2018_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM twse_divide_ratio")
        min_date, max_date = cursor.fetchone()
        
        conn.close()
        
        print(f"📊 驗證結果:")
        print(f"   2018年之前的資料: {before_2018_count:,} 筆")
        print(f"   2018年之後的資料: {after_2018_count:,} 筆")
        print(f"   最早日期: {min_date}")
        print(f"   最晚日期: {max_date}")
        
        if before_2018_count == 0:
            print(f"✅ 驗證通過：已成功移除2018年之前的資料")
            return True
        else:
            print(f"❌ 驗證失敗：仍有{before_2018_count}筆2018年之前的資料")
            return False
            
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 twse_divide_ratio 資料過濾工具")
    print("保留 2018-01-01 以後的資料")
    print("=" * 60)
    
    # 執行過濾
    if filter_data_from_2018():
        # 驗證結果
        if verify_filtered_data():
            print("\n🎉 資料過濾和驗證都成功完成！")
            print("💡 原始完整資料已備份為 twse_divide_ratio_full_backup.db")
        else:
            print("\n⚠️ 過濾完成但驗證失敗，請檢查資料")
    else:
        print("\n❌ 資料過濾失敗")

if __name__ == "__main__":
    main()
