#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建台股智能選股系統便攜版套件
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def create_portable_package():
    """創建便攜版套件"""
    print("📦 創建台股智能選股系統便攜版套件...")
    
    # 創建便攜版目錄
    portable_dir = Path("台股智能選股系統_v21.0_便攜版")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    portable_dir.mkdir()
    print(f"✅ 創建目錄: {portable_dir}")
    
    # 複製執行檔
    exe_path = Path("dist/台股智能選股系統.exe")
    if exe_path.exists():
        shutil.copy2(exe_path, portable_dir / "台股智能選股系統.exe")
        print("✅ 複製執行檔")
    else:
        print("❌ 找不到執行檔")
        return False
    
    # 複製說明文檔
    docs = [
        '台股智能選股系統_獨立版使用說明.md',
        '股票名稱補齊完成報告.md',
        '修復完成總結報告.md',
        '月營收綜合評估增強功能完成總結.md',
        'README.md'
    ]
    
    docs_dir = portable_dir / "說明文檔"
    docs_dir.mkdir()
    
    for doc in docs:
        if os.path.exists(doc):
            shutil.copy2(doc, docs_dir / doc)
            print(f"✅ 複製文檔: {doc}")
    
    # 創建啟動腳本
    startup_script = f'''@echo off
chcp 65001 > nul
title 台股智能選股系統 v21.0

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 台股智能選股系統 🚀                    ║
echo ║                                                              ║
echo ║  版本: v21.0 優化增強版 (獨立執行檔)                        ║
echo ║  編譯日期: 2025-07-30                                        ║
echo ║  檔案大小: 101MB                                             ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📍 系統資訊:
echo    • 免安裝獨立執行檔
echo    • 支援 Windows 10/11 (64位元)
echo    • 包含完整功能，無功能縮減
echo    • 自動創建資料庫和配置檔案
echo.
echo 🔄 正在啟動程式...
echo.

REM 檢查執行檔是否存在
if not exist "台股智能選股系統.exe" (
    echo ❌ 錯誤: 找不到執行檔 "台股智能選股系統.exe"
    echo.
    echo 💡 請確認:
    echo    1. 檔案是否在正確位置
    echo    2. 檔案是否被防毒軟體隔離
    echo    3. 是否有足夠的檔案權限
    echo.
    pause
    exit /b 1
)

REM 啟動程式
start "" "台股智能選股系統.exe"

REM 檢查啟動結果
if errorlevel 1 (
    echo.
    echo ❌ 程式啟動失敗
    echo.
    echo 💡 可能的解決方案:
    echo    1. 以系統管理員身分執行此腳本
    echo    2. 將程式加入防毒軟體白名單
    echo    3. 檢查 Windows 版本是否為 10/11
    echo    4. 安裝最新的 Visual C++ 可轉散發套件
    echo.
    echo 📖 詳細說明請參考 "說明文檔" 資料夾中的使用說明
    echo.
    pause
) else (
    echo ✅ 程式啟動成功！
    echo.
    echo 💡 使用提示:
    echo    • 首次啟動會自動創建資料庫檔案
    echo    • 需要網路連線以獲取最新股票資料
    echo    • 詳細使用說明請參考說明文檔
    echo.
    timeout /t 3 /nobreak > nul
)
'''
    
    with open(portable_dir / "啟動程式.bat", 'w', encoding='utf-8') as f:
        f.write(startup_script)
    print("✅ 創建啟動腳本")
    
    # 創建快速說明
    quick_readme = f'''# 🚀 台股智能選股系統 v21.0 便攜版

## ⚡ 快速開始

1. **啟動程式**: 雙擊 `啟動程式.bat` 或直接執行 `台股智能選股系統.exe`
2. **首次使用**: 程式會自動創建必要的資料庫和配置檔案
3. **開始選股**: 選擇策略 → 執行排行 → 查看結果

## 📋 系統需求

- **作業系統**: Windows 10/11 (64位元)
- **記憶體**: 建議 4GB 以上
- **硬碟空間**: 建議 2GB 以上
- **網路連線**: 需要網路連線獲取股票資料

## 🎯 主要功能

- ✅ **多策略智能選股** - 5種內建策略
- ✅ **月營收綜合評估** - 包含三大法人買賣狀況
- ✅ **實時K線圖表** - 專業級技術分析
- ✅ **Excel報告導出** - 一鍵生成分析報告

## 🔧 故障排除

### 程式無法啟動
1. 以系統管理員身分執行
2. 將程式加入防毒軟體白名單
3. 檢查 Windows 版本是否支援

### 資料無法更新
1. 檢查網路連線
2. 重新啟動程式
3. 檢查防火牆設定

## 📖 詳細說明

完整的使用說明請參考 `說明文檔` 資料夾中的文檔。

---

**版本**: v21.0 優化增強版  
**編譯日期**: {datetime.now().strftime('%Y-%m-%d')}  
**檔案大小**: 101MB  
**支援系統**: Windows 10/11 (64位元)

🎉 **感謝使用台股智能選股系統！**
'''
    
    with open(portable_dir / "快速說明.txt", 'w', encoding='utf-8') as f:
        f.write(quick_readme)
    print("✅ 創建快速說明")
    
    # 創建版本資訊檔案
    version_info = f'''台股智能選股系統 版本資訊
================================

版本號: v21.0 優化增強版
編譯日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
檔案大小: 101MB
編譯環境: Python 3.13 + PyInstaller 6.14.2

主要特色:
• 免安裝獨立執行檔
• 完整功能無縮減
• 支援 Windows 10/11
• 自動資料庫管理

核心功能:
• 多策略智能選股
• 月營收綜合評估
• 三大法人買賣狀況
• K線圖技術分析
• Excel報告導出

最新改進:
• 修復財務指標顯示問題
• 優化三大法人資訊顯示
• 補齊所有未知股票名稱
• 提升程式穩定性和效能

技術規格:
• 基於 PyQt6 GUI框架
• 整合 pandas 數據處理
• 使用 sqlite3 資料庫
• 支援多線程處理
• 內建反爬蟲機制

編譯資訊:
• 使用 PyInstaller 打包
• 排除 PyQt5 避免衝突
• 包含所有必要依賴
• 優化檔案大小
• 支援 UPX 壓縮

檔案完整性:
• 主執行檔: 台股智能選股系統.exe
• 啟動腳本: 啟動程式.bat
• 說明文檔: 說明文檔/ 資料夾
• 快速說明: 快速說明.txt
• 版本資訊: 本檔案

---
© 2025 台股智能選股系統開發團隊
'''
    
    with open(portable_dir / "版本資訊.txt", 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("✅ 創建版本資訊")
    
    # 計算總大小
    total_size = 0
    for root, dirs, files in os.walk(portable_dir):
        for file in files:
            file_path = os.path.join(root, file)
            total_size += os.path.getsize(file_path)
    
    total_size_mb = total_size / (1024 * 1024)
    
    print(f"\n🎉 便攜版套件創建完成！")
    print(f"📁 套件位置: {portable_dir}")
    print(f"📊 套件大小: {total_size_mb:.1f} MB")
    print(f"📋 包含檔案:")
    
    for root, dirs, files in os.walk(portable_dir):
        level = root.replace(str(portable_dir), '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_size = os.path.getsize(os.path.join(root, file)) / (1024 * 1024)
            print(f"{subindent}{file} ({file_size:.1f} MB)")
    
    return True

def main():
    """主函數"""
    print("📦 台股智能選股系統 - 便攜版套件創建工具")
    print("=" * 60)
    
    if create_portable_package():
        print("\n✅ 便攜版套件創建成功！")
        print("\n💡 使用方法:")
        print("1. 將整個資料夾複製到目標電腦")
        print("2. 雙擊 '啟動程式.bat' 或直接執行 exe 檔案")
        print("3. 首次啟動會自動創建必要檔案")
        print("\n🎯 適用場景:")
        print("• 無網路環境的電腦")
        print("• 不想安裝 Python 的使用者")
        print("• 需要快速部署的情況")
        print("• 演示和教學用途")
    else:
        print("\n❌ 便攜版套件創建失敗")

if __name__ == "__main__":
    main()
