#!/usr/bin/env python3
"""
智能交易策略模組
提供多種專業級交易策略，包括策略分析、買賣點判斷和風險控制
"""

import pandas as pd
import numpy as np
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

@dataclass
class TradingSignal:
    """交易信號數據類"""
    signal_type: str = "HOLD"           # BUY, SELL, HOLD
    strength: float = 0.0               # 信號強度 0-1
    price_target: float = 0.0           # 目標價位
    stop_loss: float = 0.0              # 停損價位
    take_profit: float = 0.0            # 停利價位
    confidence: float = 0.0             # 信心度
    reasoning: str = ""                 # 分析原因
    entry_timing: str = ""              # 進場時機
    risk_level: str = "中"              # 風險等級
    market_context: str = ""            # 市場環境

class BreakoutPullbackStrategy:
    """突破回踩策略"""
    
    def __init__(self):
        self.name = "突破回踩策略"
        self.description = "適合多頭市場，捕捉盤整後突破並回踩確認的機會"
        self.suitable_market = "多頭市場、盤整後突破"
        self.time_frame = "1-4週"
        self.success_rate = "65-75%"
        
    def analyze(self, df: pd.DataFrame) -> TradingSignal:
        """分析突破回踩信號"""
        try:
            if len(df) < 50:
                return TradingSignal(reasoning="數據不足")
            
            # 計算技術指標
            df = self._calculate_indicators(df)
            
            current_price = df['Close'].iloc[-1]
            ma20 = df['MA20'].iloc[-1]
            ma60 = df['MA60'].iloc[-1]
            volume_ratio = df['Volume_Ratio'].iloc[-1] if 'Volume_Ratio' in df else 1.0
            
            # 突破回踩邏輯
            signal_strength = 0.0
            reasoning_parts = []
            
            # 1. 價格突破均線
            if current_price > ma20 and ma20 > ma60:
                signal_strength += 0.3
                reasoning_parts.append("價格突破均線")
            
            # 2. 成交量確認
            if volume_ratio > 1.2:
                signal_strength += 0.25
                reasoning_parts.append("成交量放大")
            
            # 3. 回踩確認
            recent_low = df['Low'].tail(5).min()
            if recent_low > ma20 * 0.98:  # 回踩未跌破均線
                signal_strength += 0.25
                reasoning_parts.append("回踩確認支撐")
            
            # 4. 趨勢確認
            if ma20 > df['MA20'].iloc[-10]:  # 均線上升
                signal_strength += 0.2
                reasoning_parts.append("趨勢向上")
            
            # 決定信號類型
            if signal_strength >= 0.7:
                signal_type = "BUY"
                entry_timing = "突破回踩確認後立即進場"
            elif signal_strength >= 0.5:
                signal_type = "WEAK_BUY"
                entry_timing = "等待更明確的確認信號"
            else:
                signal_type = "HOLD"
                entry_timing = "等待突破機會"
            
            # 計算目標價位
            stop_loss = max(ma20 * 0.95, recent_low * 0.98)
            take_profit = current_price * 1.12
            
            return TradingSignal(
                signal_type=signal_type,
                strength=signal_strength,
                price_target=take_profit,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=signal_strength,
                reasoning=f"突破回踩策略: {', '.join(reasoning_parts)}",
                entry_timing=entry_timing,
                risk_level="中",
                market_context=self.suitable_market
            )
            
        except Exception as e:
            logging.error(f"突破回踩策略分析失敗: {e}")
            return TradingSignal(reasoning="分析失敗")
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        # 移動平均線
        df['MA20'] = df['Close'].rolling(20).mean()
        df['MA60'] = df['Close'].rolling(60).mean()
        
        # 成交量比率
        if 'Volume' in df.columns:
            df['Volume_MA10'] = df['Volume'].rolling(10).mean()
            df['Volume_Ratio'] = df['Volume'] / df['Volume_MA10']
        else:
            df['Volume_Ratio'] = 1.0
            
        return df

class BottomReboundStrategy:
    """底部反彈策略"""
    
    def __init__(self):
        self.name = "底部反彈策略"
        self.description = "適合空頭末期，捕捉個股超跌反彈機會"
        self.suitable_market = "空頭末期、個股超跌"
        self.time_frame = "1-6週"
        self.success_rate = "55-70%"
    
    def analyze(self, df: pd.DataFrame) -> TradingSignal:
        """分析底部反彈信號"""
        try:
            if len(df) < 60:
                return TradingSignal(reasoning="數據不足")
                
            # 計算技術指標
            df = self._calculate_indicators(df)
            
            current_price = df['Close'].iloc[-1]
            rsi = df['RSI'].iloc[-1]
            bb_lower = df['BB_Lower'].iloc[-1]
            ma60 = df['MA60'].iloc[-1]
            
            signal_strength = 0.0
            reasoning_parts = []
            
            # 1. RSI超賣
            if rsi < 30:
                signal_strength += 0.35
                reasoning_parts.append(f"RSI超賣({rsi:.1f})")
            elif rsi < 40:
                signal_strength += 0.2
                reasoning_parts.append(f"RSI偏低({rsi:.1f})")
            
            # 2. 布林通道下軌
            if current_price < bb_lower:
                signal_strength += 0.25
                reasoning_parts.append("跌破布林下軌")
            elif current_price < bb_lower * 1.02:
                signal_strength += 0.15
                reasoning_parts.append("接近布林下軌")
            
            # 3. 相對低點
            recent_low = df['Low'].tail(20).min()
            if current_price <= recent_low * 1.02:
                signal_strength += 0.2
                reasoning_parts.append("接近近期低點")
            
            # 4. 反彈跡象
            if current_price > df['Close'].iloc[-2]:
                signal_strength += 0.2
                reasoning_parts.append("出現反彈")
            
            # 決定信號類型
            if signal_strength >= 0.65:
                signal_type = "BUY"
                entry_timing = "超跌反彈，分批進場"
            elif signal_strength >= 0.45:
                signal_type = "WEAK_BUY"
                entry_timing = "觀察反彈確認"
            else:
                signal_type = "HOLD"
                entry_timing = "等待更深度超跌"
            
            # 計算目標價位
            stop_loss = current_price * 0.92
            take_profit = min(current_price * 1.15, ma60)
            
            return TradingSignal(
                signal_type=signal_type,
                strength=signal_strength,
                price_target=take_profit,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=signal_strength,
                reasoning=f"底部反彈策略: {', '.join(reasoning_parts)}",
                entry_timing=entry_timing,
                risk_level="高",
                market_context=self.suitable_market
            )
            
        except Exception as e:
            logging.error(f"底部反彈策略分析失敗: {e}")
            return TradingSignal(reasoning="分析失敗")
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # 布林通道
        df['MA20'] = df['Close'].rolling(20).mean()
        df['MA60'] = df['Close'].rolling(60).mean()
        bb_std = df['Close'].rolling(20).std()
        df['BB_Upper'] = df['MA20'] + (bb_std * 2)
        df['BB_Lower'] = df['MA20'] - (bb_std * 2)
        
        return df

class TrendFollowingStrategy:
    """均線趨勢策略"""
    
    def __init__(self):
        self.name = "均線趨勢策略"
        self.description = "適合多頭市場，跟隨長期趨勢操作"
        self.suitable_market = "多頭市場、趨勢明確"
        self.time_frame = "1-3個月"
        self.success_rate = "70-80%"
    
    def analyze(self, df: pd.DataFrame) -> TradingSignal:
        """分析趨勢跟隨信號"""
        try:
            if len(df) < 100:
                return TradingSignal(reasoning="數據不足")
                
            # 計算技術指標
            df = self._calculate_indicators(df)
            
            current_price = df['Close'].iloc[-1]
            ma10 = df['MA10'].iloc[-1]
            ma20 = df['MA20'].iloc[-1]
            ma60 = df['MA60'].iloc[-1]
            macd = df['MACD'].iloc[-1]
            macd_signal = df['MACD_Signal'].iloc[-1]
            
            signal_strength = 0.0
            reasoning_parts = []
            
            # 1. 均線排列
            if ma10 > ma20 > ma60:
                signal_strength += 0.3
                reasoning_parts.append("多頭排列")
            elif ma10 > ma20:
                signal_strength += 0.15
                reasoning_parts.append("短期均線向上")
            
            # 2. 價格位置
            if current_price > ma20:
                signal_strength += 0.25
                reasoning_parts.append("價格在均線之上")
            
            # 3. MACD確認
            if macd > macd_signal and macd > 0:
                signal_strength += 0.25
                reasoning_parts.append("MACD多頭")
            elif macd > macd_signal:
                signal_strength += 0.15
                reasoning_parts.append("MACD轉強")
            
            # 4. 趨勢確認
            ma20_slope = (ma20 - df['MA20'].iloc[-10]) / df['MA20'].iloc[-10]
            if ma20_slope > 0.02:
                signal_strength += 0.2
                reasoning_parts.append("趨勢強勁")
            elif ma20_slope > 0:
                signal_strength += 0.1
                reasoning_parts.append("趨勢向上")
            
            # 決定信號類型
            if signal_strength >= 0.7:
                signal_type = "BUY"
                entry_timing = "趨勢確認，適合長期持有"
            elif signal_strength >= 0.5:
                signal_type = "WEAK_BUY"
                entry_timing = "等待回調至均線附近"
            else:
                signal_type = "HOLD"
                entry_timing = "等待趨勢明確"
            
            # 計算目標價位
            stop_loss = ma20 * 0.95
            take_profit = current_price * 1.2
            
            return TradingSignal(
                signal_type=signal_type,
                strength=signal_strength,
                price_target=take_profit,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=signal_strength,
                reasoning=f"趨勢跟隨策略: {', '.join(reasoning_parts)}",
                entry_timing=entry_timing,
                risk_level="低",
                market_context=self.suitable_market
            )
            
        except Exception as e:
            logging.error(f"趨勢跟隨策略分析失敗: {e}")
            return TradingSignal(reasoning="分析失敗")
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        # 移動平均線
        df['MA10'] = df['Close'].rolling(10).mean()
        df['MA20'] = df['Close'].rolling(20).mean()
        df['MA60'] = df['Close'].rolling(60).mean()
        
        # MACD
        exp12 = df['Close'].ewm(span=12).mean()
        exp26 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp12 - exp26
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        return df

class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies = {
            "突破回踩策略": BreakoutPullbackStrategy(),
            "底部反彈策略": BottomReboundStrategy(),
            "均線趨勢策略": TrendFollowingStrategy()
        }
    
    def get_best_strategy_recommendation(self, df: pd.DataFrame) -> Tuple[str, TradingSignal]:
        """獲取最佳策略建議"""
        try:
            best_strategy = "均線趨勢策略"
            best_signal = TradingSignal()
            best_score = 0.0
            
            # 分析每個策略
            for strategy_name, strategy in self.strategies.items():
                signal = strategy.analyze(df)
                
                # 綜合評分（信號強度 + 信心度）
                score = signal.strength * 0.7 + signal.confidence * 0.3
                
                if score > best_score:
                    best_score = score
                    best_strategy = strategy_name
                    best_signal = signal
            
            return best_strategy, best_signal
            
        except Exception as e:
            logging.error(f"策略推薦失敗: {e}")
            return "均線趨勢策略", TradingSignal(reasoning="分析失敗")
    
    def get_strategy_guide(self) -> str:
        """獲取策略使用指南"""
        guide = "📚 智能交易策略使用指南\n\n"
        
        for name, strategy in self.strategies.items():
            guide += f"🎯 {strategy.name}\n"
            guide += f"   適用場景: {strategy.suitable_market}\n"
            guide += f"   操作週期: {strategy.time_frame}\n"
            guide += f"   歷史勝率: {strategy.success_rate}\n"
            guide += f"   策略說明: {strategy.description}\n\n"
        
        guide += "💡 使用建議:\n"
        guide += "• 系統會自動選擇最適合當前市況的策略\n"
        guide += "• 建議結合多個策略信號進行決策\n"
        guide += "• 嚴格執行停損停利點\n"
        guide += "• 注意資金管理和風險控制\n"
        
        return guide
    
    def format_trading_recommendation(self, strategy_name: str, signal: TradingSignal, 
                                     current_price: float) -> str:
        """格式化交易建議"""
        if signal.signal_type in ["BUY", "WEAK_BUY"]:
            recommendation = f"🟢 {signal.signal_type}\n"
            recommendation += f"💰 當前價位: {current_price:.2f}\n"
            recommendation += f"🎯 目標價位: {signal.price_target:.2f}\n"
            recommendation += f"🛡️ 建議停損: {signal.stop_loss:.2f}\n"
            recommendation += f"💪 信號強度: {signal.strength:.1%}\n"
            recommendation += f"📊 分析原因: {signal.reasoning}\n"
            recommendation += f"⏰ 進場時機: {signal.entry_timing}"
        elif signal.signal_type == "SELL":
            recommendation = f"🔴 {signal.signal_type}\n"
            recommendation += f"💰 當前價位: {current_price:.2f}\n"
            recommendation += f"🎯 目標價位: {signal.price_target:.2f}\n"
            recommendation += f"💪 信號強度: {signal.strength:.1%}\n"
            recommendation += f"📊 分析原因: {signal.reasoning}"
        else:
            recommendation = f"🟡 {signal.signal_type}\n"
            recommendation += f"💰 當前價位: {current_price:.2f}\n"
            recommendation += f"📊 分析原因: {signal.reasoning}\n"
            recommendation += f"⏰ 建議: {signal.entry_timing}"
        
        return recommendation 