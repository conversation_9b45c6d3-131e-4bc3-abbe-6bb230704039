#!/usr/bin/env python3
"""
CANSLIM策略模組 - 威廉·歐尼爾經典選股法
完整實現量價齊升核心邏輯
"""
import logging
import pandas as pd
import numpy as np
from .base_strategy import BaseStrategy


class CANSLIMStrategy(BaseStrategy):
    """CANSLIM量價齊升策略 - 威廉·歐尼爾經典選股法"""

    def __init__(self):
        super().__init__(
            name="CANSLIM量價齊升策略",
            description="威廉·歐尼爾經典選股法：高成交量、高成交金額、股價上漲的成長股",
            strategy_type="growth_momentum"
        )
        
        # 策略參數
        self.min_volume_ratio = 1.5  # 最小成交量倍數
        self.min_price_change = 0.02  # 最小漲幅2%
        self.min_amount = 10000000  # 最小成交金額1000萬
        self.lookback_days = 20  # 回看天數
        self.rps_period = 250  # RPS相對強度期間
        
        # 評分權重
        self.weights = {
            'current_earnings': 25,  # C - 當季盈餘
            'annual_earnings': 20,   # A - 年度盈餘
            'new_products': 15,      # N - 新產品/服務
            'supply_demand': 20,     # S - 供需關係(量價)
            'leader_laggard': 10,    # L - 領導股vs落後股
            'institutional': 5,      # I - 機構認同度
            'market_direction': 5    # M - 市場方向
        }
    
    def calculate_rps(self, df, market_df=None):
        """計算相對強度指標 (Relative Price Strength)"""
        try:
            if len(df) < self.rps_period:
                return 50  # 數據不足時返回中性值
            
            # 計算股票的價格表現
            current_price = df['Close'].iloc[-1]
            past_price = df['Close'].iloc[-self.rps_period]

            # 防止除零錯誤
            if past_price <= 0:
                return 50  # 數據異常時返回中性值

            stock_performance = (current_price - past_price) / past_price
            
            # 如果沒有市場數據，使用歷史分位數
            if market_df is None:
                # 計算歷史滾動表現的分位數
                rolling_returns = df['Close'].pct_change(self.rps_period).dropna()
                if len(rolling_returns) > 0:
                    current_return = rolling_returns.iloc[-1]
                    percentile = (rolling_returns <= current_return).mean() * 100
                    return min(99, max(1, percentile))
                else:
                    return 50
            
            # 如果有市場數據，計算相對表現
            market_current = market_df['Close'].iloc[-1]
            market_past = market_df['Close'].iloc[-self.rps_period]

            # 防止除零錯誤
            if market_past <= 0:
                # 市場數據異常，使用歷史分位數方法
                rolling_returns = df['Close'].pct_change(self.rps_period).dropna()
                if len(rolling_returns) > 0:
                    current_return = rolling_returns.iloc[-1]
                    percentile = (rolling_returns <= current_return).mean() * 100
                    return min(99, max(1, percentile))
                else:
                    return 50

            market_performance = (market_current - market_past) / market_past
            
            relative_performance = stock_performance - market_performance
            
            # 轉換為0-100的分數
            if relative_performance > 0.5:
                return 99
            elif relative_performance > 0.3:
                return 90
            elif relative_performance > 0.1:
                return 80
            elif relative_performance > 0:
                return 70
            elif relative_performance > -0.1:
                return 60
            elif relative_performance > -0.2:
                return 40
            else:
                return 20
                
        except Exception as e:
            logging.warning(f"RPS計算失敗: {e}")
            return 50
    
    def check_current_earnings(self, df, pe_data=None):
        """檢查當季盈餘 (C - Current Earnings)"""
        try:
            # 優先使用PE數據庫中的真實數據
            if pe_data and not pe_data.get('is_simulated', False):
                pe_ratio = pe_data.get('pe_ratio')
                if pe_ratio is not None:
                    # 使用本益比判斷盈餘品質
                    if 8 <= pe_ratio <= 20:  # 合理本益比範圍
                        return True, 25, f"✅ 本益比合理 {pe_ratio:.1f} (真實數據)"
                    elif 20 < pe_ratio <= 30:  # 稍高但可接受
                        return True, 15, f"⚠️ 本益比偏高 {pe_ratio:.1f} (真實數據)"
                    else:
                        return False, 0, f"❌ 本益比異常 {pe_ratio:.1f} (真實數據)"

            # 如果沒有真實PE數據，使用近3個月股價表現代理盈餘增長
            if len(df) < 60:
                return False, 0, "數據不足"

            recent_3m = df.tail(60)  # 約3個月

            # 防止除零錯誤
            start_price = recent_3m['Close'].iloc[0]
            end_price = recent_3m['Close'].iloc[-1]

            if start_price <= 0:
                return False, 0, "近3月數據異常(起始價格為零)"

            price_change = (end_price - start_price) / start_price

            if price_change > 0.15:  # 強勢
                return True, 25, f"📈 近3月強勢上漲 {price_change:.1%} (技術分析)"
            elif price_change > 0.05:  # 穩健
                return True, 15, f"📈 近3月穩定上漲 {price_change:.1%} (技術分析)"
            else:
                return False, 0, f"📉 近3月表現不佳 {price_change:.1%} (技術分析)"

        except Exception as e:
            return False, 0, f"當季盈餘檢查失敗: {str(e)}"
    
    def check_annual_earnings(self, df, pe_data=None):
        """檢查年度盈餘增長 (A - Annual Earnings)"""
        try:
            # 優先使用PE數據庫中的真實數據
            if pe_data and not pe_data.get('is_simulated', False):
                dividend_yield = pe_data.get('dividend_yield')
                pb_ratio = pe_data.get('pb_ratio')

                # 使用殖利率和股價淨值比評估年度盈餘品質
                score = 0
                reasons = []

                if dividend_yield is not None:
                    if dividend_yield >= 3.0:  # 高殖利率
                        score += 12
                        reasons.append(f"高殖利率 {dividend_yield:.1f}%")
                    elif dividend_yield >= 1.5:  # 中等殖利率
                        score += 8
                        reasons.append(f"中等殖利率 {dividend_yield:.1f}%")

                if pb_ratio is not None:
                    if 0.8 <= pb_ratio <= 2.0:  # 合理股價淨值比
                        score += 8
                        reasons.append(f"合理PB {pb_ratio:.1f}")
                    elif pb_ratio < 0.8:  # 低估值
                        score += 12
                        reasons.append(f"低估值PB {pb_ratio:.1f}")

                if score >= 15:
                    return True, 20, f"✅ 年度盈餘優質: {', '.join(reasons)} (真實數據)"
                elif score >= 8:
                    return True, 12, f"⚠️ 年度盈餘尚可: {', '.join(reasons)} (真實數據)"
                else:
                    return False, 0, f"❌ 年度盈餘不佳: {', '.join(reasons)} (真實數據)"

            # 如果沒有真實PE數據，使用年度股價表現代理盈餘增長
            if len(df) < 250:
                return False, 0, "數據不足"

            annual_data = df.tail(250)  # 約1年

            # 防止除零錯誤
            start_price = annual_data['Close'].iloc[0]
            end_price = annual_data['Close'].iloc[-1]

            if start_price <= 0:
                return False, 0, "年度數據異常(起始價格為零)"

            price_change = (end_price - start_price) / start_price

            if price_change > 0.30:  # 強勢
                return True, 20, f"📈 年度強勢表現 {price_change:.1%} (技術分析)"
            elif price_change > 0.15:  # 穩健
                return True, 12, f"📈 年度穩健表現 {price_change:.1%} (技術分析)"
            else:
                return False, 0, f"📉 年度表現不佳 {price_change:.1%} (技術分析)"

        except Exception as e:
            return False, 0, f"年度盈餘檢查失敗: {str(e)}"
    
    def check_new_products(self, df):
        """檢查新產品/服務 (N - New Products/Services)"""
        try:
            # 檢查是否創新高（可能反映新題材）
            if len(df) < 60:
                return False, 0, "數據不足"
            
            recent_20d_high = df.tail(20)['High'].max()
            recent_60d_high = df.tail(60)['High'].max()
            
            if recent_20d_high >= recent_60d_high:
                return True, 15, "近期創新高，可能有新題材"
            else:
                return False, 0, "未創新高"
                
        except Exception as e:
            return False, 0, f"新產品檢查失敗: {str(e)}"
    
    def check_supply_demand(self, df):
        """檢查供需關係 (S - Supply & Demand) - 核心項目"""
        try:
            if len(df) < 20:
                return False, 0, "數據不足"
            
            # 獲取最新數據
            latest = df.iloc[-1]
            recent_20d = df.tail(20)
            
            # 計算成交量比率
            avg_volume = recent_20d['Volume'].mean()
            volume_ratio = latest['Volume'] / avg_volume if avg_volume > 0 else 0
            
            # 計算價格變化
            prev_close = df.iloc[-2]['Close'] if len(df) > 1 else latest['Close']
            price_change = (latest['Close'] - prev_close) / prev_close if prev_close > 0 else 0
            
            # 計算成交金額
            amount = latest['Close'] * latest['Volume']
            
            score = 0
            reasons = []
            
            # 量價配合評分
            if volume_ratio >= 2.0 and price_change >= 0.02:
                score += 20
                reasons.append(f"爆量上漲(量{volume_ratio:.1f}倍,漲{price_change:.1%})")
            elif volume_ratio >= 1.5 and price_change >= 0.01:
                score += 15
                reasons.append(f"放量上漲(量{volume_ratio:.1f}倍,漲{price_change:.1%})")
            elif volume_ratio >= 1.2 and price_change > 0:
                score += 10
                reasons.append(f"溫和放量(量{volume_ratio:.1f}倍,漲{price_change:.1%})")
            
            # 成交金額評分
            if amount >= 20000000:  # 2000萬
                score += 5
                reasons.append(f"高成交金額({amount/100000000:.1f}億)")
            elif amount >= 10000000:  # 1000萬
                score += 3
                reasons.append(f"充足成交金額({amount/10000000:.0f}千萬)")
            
            # 判斷是否通過（至少要有量價配合）
            volume_price_pass = volume_ratio >= 1.2 and price_change > 0 and amount >= self.min_amount
            
            reason = " | ".join(reasons) if reasons else "量價配合不足"
            
            return volume_price_pass, score, reason
            
        except Exception as e:
            return False, 0, f"供需關係檢查失敗: {str(e)}"
    
    def check_leader_laggard(self, df):
        """檢查領導股地位 (L - Leader/Laggard)"""
        try:
            rps = self.calculate_rps(df)
            
            if rps >= 80:
                return True, 10, f"市場領導股(RPS:{rps})"
            elif rps >= 70:
                return True, 7, f"強勢股(RPS:{rps})"
            elif rps >= 60:
                return True, 5, f"中等強勢(RPS:{rps})"
            else:
                return False, 0, f"相對弱勢(RPS:{rps})"
                
        except Exception as e:
            return False, 0, f"領導股檢查失敗: {str(e)}"
    
    def check_institutional_sponsorship(self, df, pe_data=None):
        """檢查機構認同度 (I - Institutional Sponsorship)"""
        try:
            # 優先使用PE數據庫中的真實數據
            if pe_data and not pe_data.get('is_simulated', False):
                market_cap = pe_data.get('market_cap')
                pe_ratio = pe_data.get('pe_ratio')

                # 使用市值和本益比評估機構認同度
                score = 0
                reasons = []

                if market_cap is not None:
                    if market_cap >= 1000:  # 大型股，機構偏好
                        score += 3
                        reasons.append(f"大型股 {market_cap}億")
                    elif market_cap >= 300:  # 中型股
                        score += 2
                        reasons.append(f"中型股 {market_cap}億")
                    else:  # 小型股
                        score += 1
                        reasons.append(f"小型股 {market_cap}億")

                if pe_ratio is not None:
                    if 10 <= pe_ratio <= 25:  # 機構偏好的合理本益比
                        score += 2
                        reasons.append(f"機構偏好PE {pe_ratio:.1f}")

                if score >= 4:
                    return True, 5, f"✅ 機構認同度高: {', '.join(reasons)} (真實數據)"
                elif score >= 2:
                    return True, 3, f"⚠️ 機構認同度中等: {', '.join(reasons)} (真實數據)"
                else:
                    return False, 0, f"❌ 機構認同度低: {', '.join(reasons)} (真實數據)"

            # 如果沒有真實PE數據，使用成交量穩定性代理機構參與度
            if len(df) < 20:
                return False, 0, "數據不足"

            recent_volumes = df.tail(20)['Volume']
            volume_cv = recent_volumes.std() / recent_volumes.mean() if recent_volumes.mean() > 0 else 1

            if volume_cv < 0.5:  # 變異係數小，表示穩定
                return True, 5, f"📊 成交量穩定(CV:{volume_cv:.2f}) (技術分析)"
            elif volume_cv < 0.8:
                return True, 3, f"📊 成交量中等穩定(CV:{volume_cv:.2f}) (技術分析)"
            else:
                return False, 0, f"📊 成交量不穩定(CV:{volume_cv:.2f}) (技術分析)"

        except Exception as e:
            return False, 0, f"機構認同檢查失敗: {str(e)}"
    
    def check_market_direction(self, df, market_df=None):
        """檢查市場方向 (M - Market Direction)"""
        try:
            if len(df) < 20:
                return False, 0, "數據不足"
            
            # 計算20日均線
            ma20 = df.tail(20)['Close'].mean()
            current_price = df.iloc[-1]['Close']
            
            if current_price > ma20 * 1.02:  # 高於均線2%
                return True, 5, f"強勢多頭趨勢"
            elif current_price > ma20:
                return True, 3, f"個股多頭趨勢"
            else:
                return False, 0, f"趨勢不明或偏空"
                
        except Exception as e:
            return False, 0, f"市場方向檢查失敗: {str(e)}"

    def analyze_stock(self, df, financial_data=None, market_data=None, **kwargs):
        """分析單一股票是否符合CANSLIM策略"""
        try:
            if df.empty or len(df) < 60:
                return {
                    'suitable': False,
                    'reason': '數據不足，需要至少60日數據',
                    'details': {},
                    'score': 0,
                    'total_score': 0,
                    'strategy_name': self.name
                }

            results = {}
            total_score = 0

            # 獲取PE數據（如果可用）
            pe_data = kwargs.get('pe_data', None)
            stock_id = kwargs.get('stock_id', 'Unknown')
            data_sources = []

            # C - Current Earnings (當季盈餘)
            c_pass, c_score, c_reason = self.check_current_earnings(df, pe_data)
            results['C'] = {'pass': c_pass, 'score': c_score, 'reason': c_reason}
            total_score += c_score
            if pe_data and not pe_data.get('is_simulated', False):
                data_sources.append("C-真實數據")
            else:
                data_sources.append("C-技術分析")

            # A - Annual Earnings (年度盈餘)
            a_pass, a_score, a_reason = self.check_annual_earnings(df, pe_data)
            results['A'] = {'pass': a_pass, 'score': a_score, 'reason': a_reason}
            total_score += a_score
            if pe_data and not pe_data.get('is_simulated', False):
                data_sources.append("A-真實數據")
            else:
                data_sources.append("A-技術分析")

            # N - New Products/Services (新產品/服務)
            n_pass, n_score, n_reason = self.check_new_products(df)
            results['N'] = {'pass': n_pass, 'score': n_score, 'reason': n_reason}
            total_score += n_score

            # S - Supply & Demand (供需關係) - 核心項目
            s_pass, s_score, s_reason = self.check_supply_demand(df)
            results['S'] = {'pass': s_pass, 'score': s_score, 'reason': s_reason}
            total_score += s_score

            # L - 領導股地位
            l_pass, l_score, l_reason = self.check_leader_laggard(df)
            results['L'] = {'pass': l_pass, 'score': l_score, 'reason': l_reason}
            total_score += l_score

            # I - 機構認同
            i_pass, i_score, i_reason = self.check_institutional_sponsorship(df, pe_data)
            results['I'] = {'pass': i_pass, 'score': i_score, 'reason': i_reason}
            total_score += i_score
            if pe_data and not pe_data.get('is_simulated', False):
                data_sources.append("I-真實數據")
            else:
                data_sources.append("I-技術分析")

            # M - 市場方向
            m_pass, m_score, m_reason = self.check_market_direction(df, market_data)
            results['M'] = {'pass': m_pass, 'score': m_score, 'reason': m_reason}
            total_score += m_score

            # 判斷是否符合條件（總分>=60分，且S項目必須通過）
            suitable = total_score >= 60 and s_pass

            # 生成總結
            passed_items = [k for k, v in results.items() if v['pass']]
            real_data_count = len([s for s in data_sources if "真實數據" in s])
            tech_data_count = len([s for s in data_sources if "技術分析" in s])

            reason = f"CANSLIM評分: {total_score}/100 (通過: {','.join(passed_items)})"

            # 添加數據來源信息
            if real_data_count > 0:
                reason += f" | 📊 真實數據: {real_data_count}項, 技術分析: {tech_data_count}項"
            else:
                reason += f" | ⚠️ 全部使用技術分析 ({tech_data_count}項)"

            if suitable:
                reason += f" | ✅ 符合CANSLIM策略條件"
            else:
                if not s_pass:
                    reason += f" | ❌ S項目(量價齊升)未通過，為必要條件"
                else:
                    reason += f" | ❌ 總分不足60分"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'total_score': total_score,  # 兼容性
                'strategy_name': self.name,
                'data_sources': data_sources,  # 新增數據來源信息
                'real_data_ratio': real_data_count / len(data_sources) if data_sources else 0
            }

        except Exception as e:
            logging.error(f"CANSLIM策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'策略分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'total_score': 0,
                'strategy_name': self.name
            }
