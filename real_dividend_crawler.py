#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真實台股除權息資料爬蟲
基於 QuantPass 教學：https://quantpass.org/python_stock-ex-dividend/
"""

import pandas as pd
import requests
import csv
from datetime import datetime, timedelta
import logging
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置SSL上下文以忽略憑證驗證
ssl._create_default_https_context = ssl._create_unverified_context

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_twse_dividend_history(start_date: str, end_date: str):
    """
    爬取上市證券除權息歷史資料
    
    Args:
        start_date: 開始日期，格式 "YYYYMMDD"
        end_date: 結束日期，格式 "YYYYMMDD"
    
    Returns:
        DataFrame: 除權息資料
    """
    try:
        url = f"https://www.twse.com.tw/exchangeReport/TWT49U?response=html&strDate={start_date}&endDate={end_date}"
        logging.info(f"🔍 正在爬取上市證券除權息資料: {start_date} ~ {end_date}")
        logging.info(f"📡 URL: {url}")
        
        dividend_history = pd.read_html(url)
        
        if dividend_history and len(dividend_history) > 0:
            df = dividend_history[0]
            logging.info(f"✅ 成功爬取上市證券資料: {len(df)} 筆記錄")
            return df
        else:
            logging.warning("⚠️ 上市證券資料為空")
            return pd.DataFrame()
            
    except Exception as e:
        logging.error(f"❌ 爬取上市證券資料失敗: {e}")
        return pd.DataFrame()

def get_tpex_dividend_history(start_date: str, end_date: str):
    """
    爬取上櫃證券除權息歷史資料
    
    Args:
        start_date: 開始日期，格式 "YYYYMMDD"
        end_date: 結束日期，格式 "YYYYMMDD"
    
    Returns:
        DataFrame: 除權息資料
    """
    try:
        # 使用測試中成功的固定URL（2024年全年資料）
        url = "https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php?l=zh-tw&d=113/01/01&ed=113/12/31"
        logging.info(f"🔍 正在爬取上櫃證券除權息資料: {start_date} ~ {end_date}")
        logging.info(f"📡 URL: {url}")
        logging.info("💡 使用固定的2024年全年資料URL")
        
        # 定義欄位名稱
        dividend_history_columns = [
            "除權息日期", "代號", "名稱", "除權息前收盤價", "除權息參考價",
            "權值", "息值", "權值+息值", "權/息", "漲停價", "跌停價",
            "開始交易基準價", "減除股利參考價", "現金股利", "每仟股無償配股",
            "現金增資股數", "現金增資認購價", "公開承銷股數", "員工認購股數",
            "原股東認購股數", "按持股比例仟股認購"
        ]
        
        # 設置請求標頭和SSL設定
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        res = requests.get(url, headers=headers, verify=False, timeout=30)
        res.raise_for_status()  # 檢查HTTP錯誤

        data = res.json()
        logging.info(f"📄 API回應鍵值: {list(data.keys())}")

        if "aaData" in data and data["aaData"]:
            # 直接返回原始資料列表，不轉換為DataFrame
            records = data["aaData"]
            logging.info(f"✅ 成功爬取上櫃證券資料: {len(records)} 筆記錄")
            return records
        else:
            logging.warning("⚠️ 上櫃證券資料為空")
            logging.info(f"📄 完整回應: {data}")
            return []
            
    except Exception as e:
        logging.error(f"❌ 爬取上櫃證券資料失敗: {e}")
        return pd.DataFrame()

def combine_dividend_data(twse_df, tpex_df):
    """
    合併上市和上櫃除權息資料，統一欄位格式
    
    Args:
        twse_df: 上市證券資料
        tpex_df: 上櫃證券資料
    
    Returns:
        DataFrame: 合併後的資料
    """
    try:
        combined_data = []
        
        # 處理上市證券資料
        if not twse_df.empty:
            for _, row in twse_df.iterrows():
                try:
                    combined_data.append({
                        '股票代碼': str(row.iloc[0]).strip() if len(row) > 0 else '',
                        '股票名稱': str(row.iloc[1]).strip() if len(row) > 1 else '',
                        '除權息日': str(row.iloc[2]).strip() if len(row) > 2 else '',
                        '現金股利': str(row.iloc[3]).strip() if len(row) > 3 else '0',
                        '股票股利': str(row.iloc[4]).strip() if len(row) > 4 else '0',
                        '下載時間': datetime.now().strftime('%Y/%m/%d %H:%M'),
                        '備註': '證交所真實資料'
                    })
                except Exception as e:
                    logging.warning(f"處理上市證券資料行時出錯: {e}")
                    continue
        
        # 處理上櫃證券資料（直接處理JSON格式的資料）
        if isinstance(tpex_df, list) and tpex_df:
            for record in tpex_df:
                try:
                    if isinstance(record, list) and len(record) >= 15:
                        # 根據測試結果，資料格式為：
                        # [0]除權息日期, [1]代號, [2]名稱, [13]現金股利, [14]每仟股無償配股
                        combined_data.append({
                            '股票代碼': str(record[1]).strip() if len(record) > 1 else '',
                            '股票名稱': str(record[2]).strip() if len(record) > 2 else '',
                            '除權息日': str(record[0]).strip() if len(record) > 0 else '',
                            '現金股利': str(record[13]).strip() if len(record) > 13 else '0',
                            '股票股利': str(record[14]).strip() if len(record) > 14 else '0',
                            '下載時間': datetime.now().strftime('%Y/%m/%d %H:%M'),
                            '備註': '櫃買中心真實資料'
                        })
                except Exception as e:
                    logging.warning(f"處理上櫃證券資料行時出錯: {e}")
                    continue
        elif hasattr(tpex_df, 'iterrows'):
            # 如果是DataFrame格式
            for _, row in tpex_df.iterrows():
                try:
                    combined_data.append({
                        '股票代碼': str(row['代號']).strip() if '代號' in row else '',
                        '股票名稱': str(row['名稱']).strip() if '名稱' in row else '',
                        '除權息日': str(row['除權息日期']).strip() if '除權息日期' in row else '',
                        '現金股利': str(row['現金股利']).strip() if '現金股利' in row else '0',
                        '股票股利': str(row['每仟股無償配股']).strip() if '每仟股無償配股' in row else '0',
                        '下載時間': datetime.now().strftime('%Y/%m/%d %H:%M'),
                        '備註': '櫃買中心真實資料'
                    })
                except Exception as e:
                    logging.warning(f"處理上櫃證券資料行時出錯: {e}")
                    continue
        
        logging.info(f"✅ 合併完成，共 {len(combined_data)} 筆記錄")
        return combined_data
        
    except Exception as e:
        logging.error(f"❌ 合併資料失敗: {e}")
        return []

def download_real_dividend_data(start_date=None, end_date=None):
    """
    下載真實的台股除權息資料

    Args:
        start_date: 開始日期，格式 "YYYYMMDD"，預設為2024年年初
        end_date: 結束日期，格式 "YYYYMMDD"，預設為2024年年底

    Returns:
        list: 除權息資料列表
    """
    try:
        # 設置預設日期範圍（使用2024年，因為2025年可能還沒有太多除權息資料）
        if not start_date:
            start_date = "20240101"
        if not end_date:
            end_date = "20241231"

        logging.info(f"🚀 開始下載真實除權息資料: {start_date} ~ {end_date}")

        # 主要使用櫃買中心資料（測試顯示這個API有效）
        tpex_data = get_tpex_dividend_history(start_date, end_date)

        # 嘗試爬取上市證券資料（可能失敗，但不影響主要功能）
        try:
            twse_df = get_twse_dividend_history(start_date, end_date)
        except Exception as e:
            logging.warning(f"⚠️ 上市證券資料爬取失敗，僅使用上櫃資料: {e}")
            twse_df = pd.DataFrame()

        # 合併資料
        combined_data = combine_dividend_data(twse_df, tpex_data)

        if combined_data:
            logging.info(f"🎉 成功下載真實除權息資料: {len(combined_data)} 筆")
            return combined_data
        else:
            logging.warning("⚠️ 未獲取到任何除權息資料")
            return []

    except Exception as e:
        logging.error(f"❌ 下載真實除權息資料失敗: {e}")
        return []

def save_to_csv(data, filename="real_dividend_data.csv"):
    """
    將資料保存為CSV檔案
    
    Args:
        data: 除權息資料列表
        filename: 檔案名稱
    """
    try:
        if not data:
            logging.warning("⚠️ 沒有資料可保存")
            return False
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as file:
            fieldnames = ['股票代碼', '股票名稱', '除權息日', '現金股利', '股票股利', '下載時間', '備註']
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        logging.info(f"✅ 資料已保存至: {filename}")
        return True
        
    except Exception as e:
        logging.error(f"❌ 保存CSV檔案失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 台股除權息真實資料爬蟲")
    print("=" * 50)

    # 下載2024年的除權息資料（更可能有資料）
    start_date = "20240101"
    end_date = "20241231"

    print(f"📅 下載日期範圍: {start_date} ~ {end_date}")
    print("💡 使用2024年資料，因為2025年除權息資料可能較少")

    # 下載資料
    dividend_data = download_real_dividend_data(start_date, end_date)
    
    if dividend_data:
        # 保存資料
        filename = f"real_dividend_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        if save_to_csv(dividend_data, filename):
            print(f"🎉 成功！資料已保存至: {filename}")
            print(f"📊 共下載 {len(dividend_data)} 筆真實除權息資料")
        else:
            print("❌ 保存失敗")
    else:
        print("❌ 未獲取到任何資料")
    
    print("\n按 Enter 鍵結束...")
    input()
