#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證 DB 檔案的欄位名稱是否與原始 PKL 檔案完全一致
"""

import pandas as pd
import sqlite3
import os

def verify_column_consistency():
    """驗證欄位一致性"""
    
    pkl_file = 'D:/Finlab/history/tables/twse_divide_ratio.pkl'
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    
    print("🔍 驗證 PKL 和 DB 檔案的欄位一致性")
    print("=" * 60)
    
    try:
        # 讀取 PKL 檔案
        print(f"📂 讀取 PKL 檔案...")
        df_pkl = pd.read_pickle(pkl_file)
        
        # PKL 檔案的完整欄位（索引 + 資料欄位）
        pkl_index_names = [name for name in df_pkl.index.names if name is not None]
        pkl_column_names = list(df_pkl.columns)
        pkl_all_columns = pkl_index_names + pkl_column_names
        
        print(f"📋 PKL 檔案欄位結構:")
        print(f"   索引欄位 ({len(pkl_index_names)} 個): {pkl_index_names}")
        print(f"   資料欄位 ({len(pkl_column_names)} 個):")
        for i, col in enumerate(pkl_column_names, 1):
            print(f"      {i:2d}. '{col}'")
        print(f"   總欄位數: {len(pkl_all_columns)}")
        
        # 讀取 DB 檔案
        print(f"\n📂 讀取 DB 檔案...")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(twse_divide_ratio);")
        db_columns_info = cursor.fetchall()
        conn.close()
        
        db_column_names = [col[1] for col in db_columns_info]
        
        print(f"📋 DB 檔案欄位結構:")
        print(f"   總欄位數: {len(db_column_names)}")
        for i, col in enumerate(db_column_names, 1):
            print(f"      {i:2d}. '{col}'")
        
        # 詳細比較
        print(f"\n🔍 詳細比較結果:")
        print(f"   PKL 總欄位數: {len(pkl_all_columns)}")
        print(f"   DB 總欄位數:  {len(db_column_names)}")
        
        # 檢查每個 PKL 欄位是否在 DB 中
        print(f"\n✅ PKL 欄位在 DB 中的對應:")
        all_match = True
        for i, pkl_col in enumerate(pkl_all_columns, 1):
            if pkl_col in db_column_names:
                print(f"   {i:2d}. ✓ '{pkl_col}' -> 存在於 DB")
            else:
                print(f"   {i:2d}. ❌ '{pkl_col}' -> 不存在於 DB")
                all_match = False
        
        # 檢查 DB 中是否有額外欄位
        extra_db_columns = set(db_column_names) - set(pkl_all_columns)
        if extra_db_columns:
            print(f"\n⚠️ DB 中的額外欄位:")
            for col in extra_db_columns:
                print(f"   + '{col}'")
            all_match = False
        
        # 檢查欄位順序
        print(f"\n🔍 欄位順序比較:")
        if len(pkl_all_columns) == len(db_column_names):
            order_match = True
            for i, (pkl_col, db_col) in enumerate(zip(pkl_all_columns, db_column_names), 1):
                if pkl_col == db_col:
                    print(f"   {i:2d}. ✓ '{pkl_col}' == '{db_col}'")
                else:
                    print(f"   {i:2d}. ❌ '{pkl_col}' != '{db_col}'")
                    order_match = False
            
            if order_match:
                print(f"\n🎉 欄位順序完全一致！")
            else:
                print(f"\n⚠️ 欄位順序不一致")
        else:
            print(f"\n⚠️ 欄位數量不同，無法比較順序")
            order_match = False
        
        # 最終結論
        print(f"\n" + "=" * 60)
        if all_match and order_match:
            print(f"🎉 結論: PKL 和 DB 檔案的欄位完全一致！")
            return True
        else:
            print(f"⚠️ 結論: PKL 和 DB 檔案的欄位存在差異")
            return False
            
    except Exception as e:
        print(f"❌ 驗證過程失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_sample_consistency():
    """檢查資料樣本的一致性"""
    
    pkl_file = 'D:/Finlab/history/tables/twse_divide_ratio.pkl'
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    
    print(f"\n" + "=" * 60)
    print(f"🔍 檢查資料樣本一致性")
    print("=" * 60)
    
    try:
        # 讀取 PKL 檔案的第一筆資料
        df_pkl = pd.read_pickle(pkl_file)
        pkl_sample = df_pkl.iloc[0]
        
        # 讀取 DB 檔案的第一筆資料
        conn = sqlite3.connect(db_file)
        df_db = pd.read_sql("SELECT * FROM twse_divide_ratio LIMIT 1", conn)
        conn.close()
        
        if df_db.empty:
            print("❌ DB 檔案中沒有資料")
            return False
        
        db_sample = df_db.iloc[0]
        
        print(f"📝 比較第一筆資料的內容:")
        
        # 比較共同欄位的值
        pkl_index = df_pkl.index[0]
        stock_id = pkl_index[0] if isinstance(pkl_index, tuple) else str(pkl_index)
        date_val = pkl_index[1] if isinstance(pkl_index, tuple) else None
        
        print(f"\n🔍 索引欄位比較:")
        print(f"   stock_id: PKL='{stock_id}' vs DB='{db_sample['stock_id']}'")
        if date_val:
            print(f"   date: PKL='{date_val}' vs DB='{db_sample['date']}'")
        
        print(f"\n🔍 資料欄位比較 (前5個):")
        common_columns = ['資料日期', '股票代號', '股票名稱', '除權息前收盤價', '除權息參考價']
        
        for col in common_columns:
            if col in pkl_sample.index and col in db_sample.index:
                pkl_val = pkl_sample[col]
                db_val = db_sample[col]
                match = "✓" if pkl_val == db_val else "❌"
                print(f"   {match} {col}: PKL='{pkl_val}' vs DB='{db_val}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料樣本比較失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    column_consistent = verify_column_consistency()
    data_consistent = check_data_sample_consistency()
    
    print(f"\n" + "=" * 60)
    print(f"📊 最終驗證結果")
    print("=" * 60)
    print(f"欄位結構一致性: {'✅ 通過' if column_consistent else '❌ 失敗'}")
    print(f"資料內容一致性: {'✅ 通過' if data_consistent else '❌ 失敗'}")
    
    if column_consistent and data_consistent:
        print(f"\n🎉 PKL 和 DB 檔案完全一致！")
    else:
        print(f"\n⚠️ 發現不一致，需要進一步檢查")

if __name__ == "__main__":
    main()
