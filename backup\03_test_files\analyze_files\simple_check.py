import sqlite3
import os

print("🔍 檢查Google新聞資料庫...")

# 檢查資料庫
db_path = "D:/Finlab/history/tables/news.db"
if os.path.exists(db_path):
    print(f"✅ 找到資料庫: {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 檢查表格
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"📊 表格: {tables}")
    
    if 'news' in tables:
        cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = '1101'")
        count = cursor.fetchone()[0]
        print(f"📰 1101新聞數量: {count}")
        
        if count > 0:
            cursor.execute("SELECT title, source, pub_date FROM news WHERE stock_code = '1101' ORDER BY created_at DESC LIMIT 3")
            news = cursor.fetchall()
            print("📋 最新新聞:")
            for i, (title, source, date) in enumerate(news):
                print(f"{i+1}. [{source}] {title} ({date})")
        else:
            print("❌ 沒有1101的新聞")
    else:
        print("❌ 沒有news表格")
    
    conn.close()
else:
    print(f"❌ 資料庫不存在: {db_path}")

print("✅ 檢查完成")
