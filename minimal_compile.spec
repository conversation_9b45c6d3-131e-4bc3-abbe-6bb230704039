# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 隱藏導入 - 只包含最基本的模組
hiddenimports = [
    # 系統核心模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6 最小集合
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 數據處理最小集合
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    
    # 網路最小集合
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    
    # 其他必要
    'setuptools',
    'pkg_resources',
]

# 排除所有可能有問題的模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    'twstock',
    'twstock.codes',
    'twstock.stock',
    'yfinance',
    'finlab',
    'finmind',
    'talib',
    'mplfinance',
    'matplotlib',
    'seaborn',
    'pyqtgraph',  # 排除有依賴問題的 pyqtgraph
    'xlsxwriter', # 可能有問題
    'selenium',   # 可能有問題
    'webdriver_manager',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StockAnalyzer_Minimal',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
