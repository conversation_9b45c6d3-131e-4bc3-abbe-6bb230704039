#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Google新聞爬蟲修正
"""

import sys
import os
import sqlite3
from google_news_fast import GoogleNewsFastCrawler

def test_google_news_crawler():
    """測試Google新聞爬蟲"""
    print("🚀 開始測試Google新聞爬蟲修正...")
    
    # 創建爬蟲實例
    crawler = GoogleNewsFastCrawler()
    
    # 設置進度回調
    def progress_callback(message):
        print(f"[進度] {message}")
    
    crawler.set_progress_callback(progress_callback)
    
    # 測試爬取6667新聞
    stock_code = "6667"
    print(f"🔍 測試爬取 {stock_code} 的新聞...")
    
    try:
        # 爬取新聞
        news_list = crawler.search_stock_news(stock_code, days=7)
        
        if news_list:
            print(f"✅ 成功爬取到 {len(news_list)} 筆新聞")
            
            # 顯示前3筆新聞標題
            print("\n📰 前3筆新聞:")
            for i, news in enumerate(news_list[:3]):
                print(f"{i+1}. {news['title']}")
                print(f"   來源: {news['source']}")
                print(f"   日期: {news['pub_date']}")
                print()
            
            # 測試儲存到資料庫
            db_path = "./test_news.db"
            print(f"💾 測試儲存到資料庫: {db_path}")
            
            crawler.save_to_database(news_list, db_path)
            
            # 驗證資料庫內容
            print("🔍 驗證資料庫內容...")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = ?", (stock_code,))
            count = cursor.fetchone()[0]
            print(f"✅ 資料庫中有 {count} 筆 {stock_code} 的新聞")
            
            # 顯示最新的3筆新聞標題
            cursor.execute("""
                SELECT title, source, pub_date 
                FROM news 
                WHERE stock_code = ? 
                ORDER BY created_at DESC 
                LIMIT 3
            """, (stock_code,))
            
            db_news = cursor.fetchall()
            print("\n📊 資料庫中的最新3筆新聞:")
            for i, (title, source, pub_date) in enumerate(db_news):
                print(f"{i+1}. {title}")
                print(f"   來源: {source}")
                print(f"   日期: {pub_date}")
                print()
            
            conn.close()
            
            print("🎉 測試完成！新聞爬取和儲存功能正常")
            return True
            
        else:
            print("❌ 沒有爬取到新聞")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_news_db():
    """檢查現有的新聞資料庫"""
    print("\n🔍 檢查現有的新聞資料庫...")
    
    db_paths = ["./news.db", "D:/Finlab/history/tables/news.db"]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"📂 找到資料庫: {db_path}")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"   表格: {tables}")
                
                # 檢查6667的新聞
                if 'news' in tables:
                    cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = '6667'")
                    count = cursor.fetchone()[0]
                    print(f"   6667新聞數量: {count}")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 讀取失敗: {e}")
        else:
            print(f"❌ 資料庫不存在: {db_path}")

if __name__ == "__main__":
    print("=" * 60)
    print("Google新聞爬蟲修正測試")
    print("=" * 60)
    
    # 檢查現有資料庫
    check_existing_news_db()
    
    # 測試爬蟲
    success = test_google_news_crawler()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 所有測試通過！修正成功")
    else:
        print("❌ 測試失敗，需要進一步檢查")
    print("=" * 60)
