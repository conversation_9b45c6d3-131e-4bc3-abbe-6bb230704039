#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 benchmark.pkl 檔案的儲存區間
"""

import pandas as pd
import os
from datetime import datetime

def check_benchmark_period():
    """檢查 benchmark.pkl 的儲存區間"""
    file_path = r"D:\Finlab\backup\O3mh_strategy6AA\history\tables\benchmark.pkl"
    
    print("🔍 檢查 Benchmark 檔案儲存區間")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f"📁 檔案資訊:")
        print(f"   路徑: {file_path}")
        print(f"   大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
        print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
        
        # 讀取資料
        print(f"\n📖 正在讀取資料...")
        data = pd.read_pickle(file_path)
        
        print(f"✅ 讀取成功:")
        print(f"   資料筆數: {len(data):,}")
        print(f"   資料類型: {type(data)}")
        
        # 檢查索引
        if hasattr(data, 'index'):
            print(f"   索引類型: {type(data.index)}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
        
        # 檢查欄位
        if hasattr(data, 'columns'):
            print(f"   欄位數量: {len(data.columns)}")
            print(f"   欄位名稱: {list(data.columns)}")
        
        # 方法1: 檢查 MultiIndex 中的 date
        if hasattr(data, 'index') and hasattr(data.index, 'names') and 'date' in data.index.names:
            print(f"\n📅 日期分析 (MultiIndex):")
            dates = data.index.get_level_values('date')
            
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f"   ✅ 儲存區間: {start_date} 至 {end_date}")
            print(f"   時間跨度: {end_date.year - start_date.year + 1} 年")
            print(f"   唯一日期數: {unique_dates}")
            
        # 方法2: 檢查索引是否直接是日期
        elif hasattr(data, 'index') and pd.api.types.is_datetime64_any_dtype(data.index):
            print(f"\n📅 日期分析 (DatetimeIndex):")
            start_date = data.index.min()
            end_date = data.index.max()
            unique_dates = len(data.index.unique())
            
            print(f"   ✅ 儲存區間: {start_date} 至 {end_date}")
            print(f"   時間跨度: {end_date.year - start_date.year + 1} 年")
            print(f"   唯一日期數: {unique_dates}")
            
        # 方法3: 在欄位中尋找日期
        else:
            print(f"\n🔍 在欄位中尋找日期...")
            date_found = False
            
            if hasattr(data, 'columns'):
                # 尋找可能的日期欄位
                date_cols = [col for col in data.columns if 
                           any(keyword in str(col).lower() for keyword in 
                               ['date', 'time', '日期', '時間'])]
                
                if date_cols:
                    print(f"   找到可能的日期欄位: {date_cols}")
                    
                    for col in date_cols:
                        try:
                            dates = pd.to_datetime(data[col])
                            start_date = dates.min()
                            end_date = dates.max()
                            unique_dates = len(dates.unique())
                            
                            print(f"   ✅ {col} 儲存區間: {start_date} 至 {end_date}")
                            print(f"   時間跨度: {end_date.year - start_date.year + 1} 年")
                            print(f"   唯一日期數: {unique_dates}")
                            date_found = True
                            break
                            
                        except Exception as e:
                            print(f"   ⚠️ {col} 無法解析為日期: {str(e)[:50]}...")
            
            if not date_found:
                print(f"   ⚠️ 未找到標準的日期資訊")
                print(f"   索引預覽: {data.index[:3]}")
                if hasattr(data, 'columns'):
                    print(f"   欄位預覽: {list(data.columns)}")
        
        # 顯示一些樣本資料
        print(f"\n📊 樣本資料:")
        print(data.head(3))
        
        return True
        
    except Exception as e:
        print(f"❌ 讀取失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    check_benchmark_period()
