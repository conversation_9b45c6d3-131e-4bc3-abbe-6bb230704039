#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的月營收下載功能
"""

import os
import sys

def test_mops_bulk_downloader():
    """測試MOPS批量下載器"""
    
    print("🧪 測試修復後的MOPS批量下載器")
    print("=" * 50)
    
    try:
        from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
        
        # 初始化下載器
        db_path = "D:/Finlab/history/tables/monthly_revenue.db"
        downloader = MopsBulkRevenueDownloader(db_path)
        
        print("✅ 下載器初始化成功")
        
        # 測試下載單月數據（2024年7月）
        print(f"\n🔄 測試下載2024年7月數據...")
        
        # 下載上市數據
        sii_data = downloader.download_market_revenue('sii', 2024, 7)
        print(f"📊 上市數據: {len(sii_data)} 筆")
        
        if len(sii_data) > 0:
            # 顯示前3筆數據
            print(f"🔍 前3筆上市數據:")
            for i, data in enumerate(sii_data[:3]):
                print(f"  {i+1}. {data['stock_id']} {data['stock_name']}")
                print(f"     營收: {data['revenue']:,} | 年增率: {data.get('revenue_yoy', 'N/A')}%")
                print(f"     市場: {data.get('market', 'N/A')}")
            
            # 測試保存到資料庫
            print(f"\n💾 測試保存到資料庫...")
            saved_count = downloader.save_to_database(sii_data[:10])  # 只保存前10筆測試
            print(f"✅ 成功保存 {saved_count} 筆數據")
            
            # 驗證保存結果
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stock_id, stock_name, revenue, revenue_yoy, market, updated_at
                FROM monthly_revenue 
                WHERE year = 2024 AND month = 7
                LIMIT 5
            ''')
            
            results = cursor.fetchall()
            print(f"\n🔍 資料庫驗證結果:")
            for row in results:
                stock_id, stock_name, revenue, revenue_yoy, market, updated_at = row
                print(f"  {stock_id} {stock_name} [{market}]")
                print(f"    營收: {revenue:,} | 年增率: {revenue_yoy}% | 更新: {updated_at}")
            
            conn.close()
            
        else:
            print("⚠️ 沒有獲取到數據，可能是網路問題或網站結構變更")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_gui():
    """測試統一GUI功能"""
    
    print(f"\n🧪 測試統一GUI功能")
    print("=" * 30)
    
    try:
        from unified_monthly_revenue_gui import UnifiedMonthlyRevenueGUI
        
        print("✅ GUI模組導入成功")
        print("💡 GUI功能已修復，現在支援:")
        print("  • 🆕 新建資料庫（包含完整欄位結構）")
        print("  • 📥 匯入CSV（支援產業別等完整資訊）")
        print("  • 📊 查看數據庫（顯示詳細統計）")
        print("  • 🔄 傳統下載功能")
        print("  • 📅 MOPS批量下載（已修復 updated_at 問題）")
        
        # 檢查資料庫狀態
        db_path = "D:/Finlab/history/tables/monthly_revenue.db"
        if os.path.exists(db_path):
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT year || '-' || month) FROM monthly_revenue")
            month_count = cursor.fetchone()[0]
            
            print(f"\n📊 當前資料庫狀態:")
            print(f"  總記錄數: {total_count:,}")
            print(f"  涵蓋月份: {month_count} 個月")
            
            conn.close()
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    
    print("🎯 月營收功能修復測試")
    print("=" * 60)
    
    # 檢查資料庫是否存在
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    if not os.path.exists(db_path):
        print(f"❌ 資料庫不存在: {db_path}")
        print("請先運行 fix_monthly_revenue_db.py 或使用GUI創建資料庫")
        return
    
    print(f"✅ 找到資料庫: {db_path}")
    
    # 測試1: MOPS批量下載器
    print(f"\n" + "="*60)
    success1 = test_mops_bulk_downloader()
    
    # 測試2: 統一GUI
    print(f"\n" + "="*60)
    success2 = test_unified_gui()
    
    # 總結
    print(f"\n" + "="*60)
    print(f"🎉 測試總結:")
    print(f"  MOPS下載器: {'✅ 通過' if success1 else '❌ 失敗'}")
    print(f"  統一GUI: {'✅ 通過' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print(f"\n🎊 所有功能測試通過！")
        print(f"💡 現在可以正常使用月營收功能了：")
        print(f"  1. 開啟主程式 O3mh_gui_v21_optimized.py")
        print(f"  2. 點擊選單：爬蟲 → 📈 月營收資料下載器")
        print(f"  3. 使用各種功能下載和管理月營收數據")
    else:
        print(f"\n⚠️ 部分功能測試失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
