#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 income_sheet.pkl 的所有欄位名稱
"""

import pandas as pd

def check_income_columns():
    """檢查 income_sheet.pkl 的欄位"""
    print("📊 檢查 income_sheet.pkl 的所有欄位")
    print("=" * 80)
    
    try:
        df = pd.read_pickle('history/tables/income_sheet.pkl')
        
        print(f"📋 基本資訊:")
        print(f"   總欄位數: {len(df.columns)}")
        print(f"   資料筆數: {len(df):,}")
        print(f"   索引: {df.index.names}")
        
        print(f"\n📝 所有欄位名稱:")
        print("=" * 80)
        
        # 按字母順序排列欄位
        sorted_columns = sorted(df.columns)
        
        for i, col in enumerate(sorted_columns, 1):
            print(f"{i:4d}. {col}")
        
        print(f"\n🔍 常見的重要欄位 (如果存在):")
        important_columns = [
            '營業收入', '營收', '收入', 'revenue',
            '營業成本', '成本', 'cost',
            '營業毛利', '毛利', 'gross',
            '營業費用', '費用', 'expense',
            '營業利益', '營業淨利', 'operating',
            '稅前淨利', '稅前', 'pretax',
            '本期淨利', '淨利', '淨收益', 'net',
            '每股盈餘', 'EPS', 'earnings'
        ]
        
        found_important = []
        for col in df.columns:
            for keyword in important_columns:
                if keyword in col:
                    found_important.append(col)
                    break
        
        if found_important:
            print("   找到的重要欄位:")
            for col in found_important[:20]:  # 只顯示前20個
                print(f"     - {col}")
            if len(found_important) > 20:
                print(f"     ... 還有 {len(found_important)-20} 個相關欄位")
        else:
            print("   未找到明顯的重要欄位")
        
        # 檢查樣本資料
        print(f"\n📊 樣本資料 (前3筆):")
        print("=" * 80)
        sample_data = df.head(3)
        
        # 只顯示非空的欄位
        non_empty_cols = []
        for col in df.columns:
            if not sample_data[col].isna().all():
                non_empty_cols.append(col)
        
        if non_empty_cols:
            print(f"   有資料的欄位數: {len(non_empty_cols)}")
            print(f"   前10個有資料的欄位:")
            for col in non_empty_cols[:10]:
                values = sample_data[col].dropna()
                if len(values) > 0:
                    print(f"     {col}: {values.iloc[0]}")
        
        # 保存欄位清單到檔案
        with open('income_sheet_columns.txt', 'w', encoding='utf-8') as f:
            f.write("income_sheet.pkl 所有欄位清單\n")
            f.write("=" * 50 + "\n\n")
            for i, col in enumerate(sorted_columns, 1):
                f.write(f"{i:4d}. {col}\n")

        print(f"\n💾 完整欄位清單已保存到: income_sheet_columns.txt")

        return df.columns.tolist()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return []

def main():
    """主函數"""
    print("🔧 Income Sheet 欄位檢查工具")
    print("=" * 80)
    print("🎯 目標: 了解 income_sheet.pkl 包含哪些財報欄位")
    print("=" * 80)
    
    columns = check_income_columns()
    
    if columns:
        print(f"\n💾 欄位清單已準備好，共 {len(columns)} 個欄位")
        print(f"💡 這些欄位代表各種財報項目，包括收入、成本、費用、利潤等")
    else:
        print(f"\n❌ 無法獲取欄位資訊")

if __name__ == "__main__":
    main()
