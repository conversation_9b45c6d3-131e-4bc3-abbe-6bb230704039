#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試當前問題 - 表格顯示但數據為空
"""

import sys
import pandas as pd
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

def debug_current_issue():
    """調試當前問題"""
    
    print("🔍 調試當前問題 - 表格顯示但數據為空")
    print("=" * 60)
    
    # 1. 檢查策略是否能正常工作
    print("1️⃣ 檢查策略是否能正常工作...")
    
    try:
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
        
        strategy = HighYieldTurtleStrategyOptimized()
        print("   ✅ 策略導入成功")
        
        # 創建測試數據
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        prices = [100 * (1.005 ** i) for i in range(100)]
        test_df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': [100000] * 100
        }, index=dates)
        
        # 測試策略分析
        result = strategy.analyze_stock(test_df, stock_id='2330')
        print(f"   📊 策略分析結果: {result['suitable']}")
        print(f"   📊 評分: {result['score']}")
        print(f"   📊 詳細信息: {result.get('details', {})}")
        
    except Exception as e:
        print(f"   ❌ 策略測試失敗: {e}")
        return False
    
    # 2. 檢查GUI中的數據流
    print("\n2️⃣ 檢查GUI中的數據流...")
    
    # 模擬GUI的數據處理流程
    class MockGUI:
        def __init__(self):
            sys.path.append('strategies')
            from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
            self._turtle_strategy_instance = HighYieldTurtleStrategyOptimized()
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """檢查高殖利率烏龜策略"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據", {}
                
                turtle_strategy = self._turtle_strategy_instance
                result = turtle_strategy.analyze_stock(df, stock_id=stock_id)
                details = result.get('details', {})
                
                detailed_info = {
                    'suitable': result['suitable'],
                    'reason': result['reason'],
                    'score': result['score'],
                    'dividend_yield': details.get('dividend_yield', 0),
                    'pe_ratio': details.get('pe_ratio', 0),
                    'pb_ratio': details.get('pb_ratio', 0),
                    'close_price': details.get('close_price', 0),
                    'volume': details.get('volume', 0),
                    'data_source': details.get('data_source', 'FinLab PKL')
                }

                if result['suitable']:
                    return True, result['reason'], detailed_info
                else:
                    return False, result['reason'], detailed_info
                    
            except Exception as e:
                print(f"   ❌ 策略檢查錯誤: {e}")
                return False, f"策略檢查錯誤: {str(e)}", {}
        
        def simulate_data_flow(self, stock_id, stock_name):
            """模擬數據流"""
            print(f"   📊 模擬股票 {stock_id} {stock_name} 的數據流...")
            
            # 創建測試數據
            dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
            prices = [100 * (1.005 ** i) for i in range(100)]
            df = pd.DataFrame({
                'Open': prices,
                'High': [p * 1.02 for p in prices],
                'Low': [p * 0.98 for p in prices],
                'Close': prices,
                'Volume': [100000] * 100
            }, index=dates)
            
            # 步驟1: 檢查條件
            condition = {"type": "high_yield_turtle_strategy", "stock_id": stock_id}
            matched, message, detailed_info = self.check_high_yield_turtle_strategy(df, stock_id=stock_id)
            
            print(f"      條件檢查結果: {matched}")
            print(f"      詳細信息: {detailed_info}")
            
            # 步驟2: 構建結果
            close_price = df.iloc[-1]['Close']
            result = {
                "股票代碼": stock_id,
                "股票名稱": stock_name,
                "收盤價": close_price,
                "條件結果": [{"matched": matched, "message": message}],
                "raw_data": df
            }
            
            # 步驟3: 添加詳細信息
            if detailed_info:
                result.update({
                    "烏龜評分": detailed_info.get('score', 0),
                    "殖利率": f"{detailed_info.get('dividend_yield', 0):.2f}%",
                    "營收成長": "N/A",
                    "營業利益率": "N/A", 
                    "董監持股": "N/A",
                    "成交量": detailed_info.get('volume', 0),
                    "本益比": detailed_info.get('pe_ratio', 0),
                    "股價淨值比": detailed_info.get('pb_ratio', 0)
                })
                print(f"      ✅ 詳細信息已添加")
            else:
                print(f"      ❌ 詳細信息為空")
            
            return result, matched
    
    # 測試數據流
    mock_gui = MockGUI()
    
    test_stocks = [
        ('2330', '台積電'),
        ('2881', '富邦金'),
        ('1101', '台泥')
    ]
    
    results = []
    matching_stocks = []
    
    for stock_id, stock_name in test_stocks:
        try:
            result, matched = mock_gui.simulate_data_flow(stock_id, stock_name)
            results.append(result)
            if matched:
                matching_stocks.append(stock_id)
        except Exception as e:
            print(f"      ❌ {stock_id} 處理失敗: {e}")
    
    # 3. 檢查結果數據
    print(f"\n3️⃣ 檢查結果數據...")
    print(f"   總結果數: {len(results)}")
    print(f"   符合條件: {len(matching_stocks)} 支")
    
    for i, result in enumerate(results):
        stock_id = result.get("股票代碼", "")
        score = result.get("烏龜評分", "缺少")
        dividend = result.get("殖利率", "缺少")
        volume = result.get("成交量", "缺少")
        
        print(f"   {i+1}. {stock_id}: 評分={score}, 殖利率={dividend}, 成交量={volume}")
    
    # 4. 檢查可能的問題
    print(f"\n4️⃣ 檢查可能的問題...")
    
    # 檢查是否所有結果都有必要的字段
    required_fields = ["烏龜評分", "殖利率", "成交量"]
    all_good = True
    
    for result in results:
        missing = []
        for field in required_fields:
            if field not in result or result[field] in [0, "0", "缺少", "N/A"]:
                missing.append(field)
        
        if missing:
            stock_id = result.get("股票代碼", "")
            print(f"   ⚠️ {stock_id} 問題字段: {missing}")
            all_good = False
    
    if all_good:
        print(f"   ✅ 所有結果數據都正常")
    else:
        print(f"   ❌ 部分結果數據有問題")
    
    # 5. 模擬表格填充
    print(f"\n5️⃣ 模擬表格填充...")
    
    print(f"   表格數據預覽:")
    print(f"   {'股票代碼':<8} {'股票名稱':<8} {'收盤價':<8} {'烏龜評分':<8} {'殖利率':<10} {'成交量':<10}")
    print(f"   {'-'*8} {'-'*8} {'-'*8} {'-'*8} {'-'*10} {'-'*10}")
    
    for result in results:
        stock_id = result.get("股票代碼", "")
        stock_name = result.get("股票名稱", "")
        close_price = result.get("收盤價", 0)
        score = result.get("烏龜評分", 0)
        dividend = result.get("殖利率", "0%")
        volume = result.get("成交量", 0)
        
        print(f"   {stock_id:<8} {stock_name:<8} {close_price:<8.2f} {score:<8} {dividend:<10} {volume:<10}")
    
    print(f"\n" + "=" * 60)
    
    if all_good:
        print(f"✅ 數據流測試成功！問題可能在於GUI程序需要重新啟動")
        print(f"💡 建議: 請重新啟動GUI程序以應用修復")
    else:
        print(f"❌ 數據流存在問題，需要進一步調試")
    
    return all_good

if __name__ == "__main__":
    success = debug_current_issue()
    
    if success:
        print(f"\n🎯 解決方案:")
        print(f"1. 關閉當前的GUI程序")
        print(f"2. 重新運行 python O3mh_gui_v21_optimized.py")
        print(f"3. 選擇「高殖利率烏龜策略」並執行篩選")
        print(f"4. 右側表格應該會顯示正確的數據")
    else:
        print(f"\n🔧 需要進一步調試數據流問題")
