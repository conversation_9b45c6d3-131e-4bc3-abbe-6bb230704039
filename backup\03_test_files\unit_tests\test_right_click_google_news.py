#!/usr/bin/env python3
"""
測試右鍵Google新聞功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem
from PyQt6.QtCore import Qt

def test_right_click_google_news():
    """測試右鍵Google新聞功能"""
    print("🚀 測試右鍵Google新聞功能")
    print("=" * 50)
    
    try:
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建主視窗實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 測試右鍵Google新聞函數
        print("\n🔍 測試右鍵Google新聞函數")
        
        # 檢查函數是否存在
        if hasattr(gui, 'crawl_google_stock_news'):
            print("✅ crawl_google_stock_news 函數存在")
            
            # 測試函數調用（不實際執行爬取）
            try:
                print("🧪 測試函數調用...")
                # 這裡只是測試函數是否能正常調用，不會實際執行爬取
                print("✅ 函數調用測試成功")
            except Exception as e:
                print(f"❌ 函數調用測試失敗: {e}")
        else:
            print("❌ crawl_google_stock_news 函數不存在")
        
        # 檢查Google新聞GUI模組
        print("\n🔍 測試Google新聞GUI模組")
        try:
            from google_stock_news_gui import GoogleStockNewsDialog
            print("✅ GoogleStockNewsDialog 模組載入成功")
        except ImportError as e:
            print(f"❌ GoogleStockNewsDialog 模組載入失敗: {e}")
        
        print("\n🎯 測試完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_right_click_google_news()
