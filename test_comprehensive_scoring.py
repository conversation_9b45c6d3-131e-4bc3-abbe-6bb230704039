#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收綜合評分功能
"""

import os
import sys
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, date

def calculate_comprehensive_score(yoy: float, mom: float) -> float:
    """
    計算月營收綜合評分（複製自主程式的邏輯）
    
    Args:
        yoy: 年增率 (%)
        mom: 月增率 (%)
    
    Returns:
        float: 綜合評分 (0-100)
    """
    try:
        # 處理NaN值
        if pd.isna(yoy) or pd.isna(mom):
            return 0.0
        
        # 1. 加權平均 (30%)
        weighted_score = yoy * 0.7 + mom * 0.3
        weighted_normalized = max(0, min(100, weighted_score + 50))
        
        # 2. 四象限分析 (30%)
        if yoy > 0 and mom > 0:
            quadrant_score = 90 + min(yoy + mom, 10)
        elif yoy > 0 and mom <= 0:
            quadrant_score = 70 + min(yoy, 20) + max(mom, -10)
        elif yoy <= 0 and mom > 0:
            quadrant_score = 50 + min(mom, 20) + max(yoy, -10)
        else:
            quadrant_score = 30 + max(yoy + mom, -30)
        quadrant_score = max(0, min(100, quadrant_score))
        
        # 3. 動能評分 (20%)
        monthly_yoy = yoy / 12
        acceleration = mom - monthly_yoy
        base_momentum = yoy * 0.6 + mom * 0.4
        if acceleration > 2:
            momentum_bonus = 10
        elif acceleration > 0:
            momentum_bonus = 5
        elif acceleration > -2:
            momentum_bonus = 0
        else:
            momentum_bonus = -5
        momentum_score = max(0, min(100, base_momentum + momentum_bonus + 50))
        
        # 4. 穩定性評分 (20%)
        volatility = abs(mom - monthly_yoy)
        base_stability = yoy * 0.8 + mom * 0.2
        if volatility <= 2:
            stability_bonus = 5
        elif volatility <= 5:
            stability_bonus = 2
        elif volatility <= 10:
            stability_bonus = 0
        else:
            stability_bonus = -3
        stability_score = max(0, min(100, base_stability + stability_bonus + 50))
        
        # 綜合計算
        comprehensive = (
            weighted_normalized * 0.3 +
            quadrant_score * 0.3 +
            momentum_score * 0.2 +
            stability_score * 0.2
        )
        
        return round(comprehensive, 1)
        
    except Exception as e:
        print(f"計算綜合評分失敗: {e}")
        return 0.0

def get_score_grade(score):
    """根據評分獲取等級"""
    if score >= 85:
        return "A+ (卓越)"
    elif score >= 75:
        return "A (優秀)"
    elif score >= 65:
        return "B+ (良好)"
    elif score >= 55:
        return "B (普通)"
    elif score >= 45:
        return "C (偏弱)"
    else:
        return "D (疲弱)"

def test_comprehensive_scoring():
    """測試綜合評分功能"""
    print("=" * 80)
    print("🎯 月營收綜合評分功能測試")
    print("=" * 80)
    
    # 測試案例
    test_cases = [
        ("台積電", 15.5, 8.2),    # 雙重成長，高YoY
        ("鴻海", 12.3, -2.1),     # 長期成長，短期調整
        ("聯發科", -5.2, 15.6),   # 短期復甦，長期待觀察
        ("某公司A", -8.5, -3.2),  # 雙重衰退
        ("穩定股", 8.0, 0.7),     # 穩定成長
        ("高成長股", 25.0, 12.0), # 高成長
        ("波動股", 5.0, -8.0),    # 高波動
        ("復甦股", -2.0, 18.0),   # 強勁復甦
    ]
    
    print("📊 綜合評分測試結果:")
    print("-" * 80)
    print(f"{'公司名稱':<10} {'YoY%':<8} {'MoM%':<8} {'綜合評分':<10} {'等級':<15} {'象限分析'}")
    print("-" * 80)
    
    results = []
    for company, yoy, mom in test_cases:
        score = calculate_comprehensive_score(yoy, mom)
        grade = get_score_grade(score)
        
        # 象限分析
        if yoy > 0 and mom > 0:
            quadrant = "🟢 雙重成長"
        elif yoy > 0 and mom <= 0:
            quadrant = "🟡 長期成長"
        elif yoy <= 0 and mom > 0:
            quadrant = "🟡 短期復甦"
        else:
            quadrant = "🔴 雙重衰退"
        
        print(f"{company:<10} {yoy:>+6.1f}% {mom:>+6.1f}% {score:>8.1f} {grade:<15} {quadrant}")
        results.append((company, yoy, mom, score, grade))
    
    # 按綜合評分排序
    results.sort(key=lambda x: x[3], reverse=True)
    
    print(f"\n🏆 綜合評分排行榜 (前5名):")
    print("-" * 50)
    for i, (company, yoy, mom, score, grade) in enumerate(results[:5], 1):
        print(f"{i:2d}. {company:<10} 評分:{score:>5.1f} {grade}")
    
    return results

def test_with_real_data():
    """使用真實資料測試"""
    print(f"\n" + "=" * 80)
    print("📊 使用真實月營收資料測試")
    print("=" * 80)
    
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 月營收資料庫不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 獲取2025年7月的資料樣本
        query = """
            SELECT stock_id, stock_name, 當月營收, 上月營收, 去年當月營收
            FROM monthly_report 
            WHERE date LIKE '2025-07%'
            AND 當月營收 != '' AND 上月營收 != '' AND 去年當月營收 != ''
            LIMIT 20
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 沒有找到有效的月營收資料")
            return
        
        # 轉換數值並計算成長率
        df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
        df['上月營收'] = pd.to_numeric(df['上月營收'], errors='coerce')
        df['去年當月營收'] = pd.to_numeric(df['去年當月營收'], errors='coerce')
        
        # 計算YoY和MoM
        df['YoY%'] = ((df['當月營收'] - df['去年當月營收']) / df['去年當月營收'] * 100).round(2)
        df['MoM%'] = ((df['當月營收'] - df['上月營收']) / df['上月營收'] * 100).round(2)
        
        # 計算綜合評分
        df['綜合評分'] = df.apply(lambda row: calculate_comprehensive_score(
            row['YoY%'], row['MoM%']), axis=1)
        
        # 移除無效資料
        df = df.dropna(subset=['YoY%', 'MoM%', '綜合評分'])
        df = df[df['當月營收'] > 0]
        
        # 按綜合評分排序
        df_sorted = df.sort_values('綜合評分', ascending=False)
        
        print(f"📋 真實資料綜合評分排行榜 (前10名):")
        print("-" * 90)
        print(f"{'排名':<4} {'股票代碼':<8} {'股票名稱':<12} {'YoY%':<8} {'MoM%':<8} {'綜合評分':<10} {'等級'}")
        print("-" * 90)
        
        for i, (_, row) in enumerate(df_sorted.head(10).iterrows(), 1):
            grade = get_score_grade(row['綜合評分'])
            print(f"{i:<4} {row['stock_id']:<8} {row['stock_name']:<12} "
                  f"{row['YoY%']:>+6.1f}% {row['MoM%']:>+6.1f}% "
                  f"{row['綜合評分']:>8.1f} {grade}")
        
        print(f"\n📊 統計資訊:")
        print(f"   有效資料筆數: {len(df)}")
        print(f"   平均綜合評分: {df['綜合評分'].mean():.1f}")
        print(f"   最高綜合評分: {df['綜合評分'].max():.1f}")
        print(f"   最低綜合評分: {df['綜合評分'].min():.1f}")
        
        # 評分分布
        grade_counts = {}
        for score in df['綜合評分']:
            grade = get_score_grade(score)
            grade_key = grade.split()[0]  # 只取等級部分
            grade_counts[grade_key] = grade_counts.get(grade_key, 0) + 1
        
        print(f"\n📈 評分分布:")
        for grade, count in sorted(grade_counts.items()):
            percentage = count / len(df) * 100
            print(f"   {grade}: {count} 家 ({percentage:.1f}%)")
        
    except Exception as e:
        print(f"❌ 真實資料測試失敗: {e}")

def main():
    """主測試函數"""
    print("🚀 月營收綜合評分系統測試")
    
    # 基本功能測試
    test_results = test_comprehensive_scoring()
    
    # 真實資料測試
    test_with_real_data()
    
    print(f"\n" + "=" * 80)
    print("✅ 綜合評分功能測試完成")
    print("=" * 80)
    print("💡 功能特色:")
    print("   1. 綜合考量 YoY (70%) 和 MoM (30%) 權重")
    print("   2. 四象限分析：雙重成長 > 長期成長 > 短期復甦 > 雙重衰退")
    print("   3. 動能評分：獎勵成長加速，懲罰成長減速")
    print("   4. 穩定性評分：偏好穩定成長，懲罰高波動")
    print("   5. 評分範圍：0-100分，分為A+、A、B+、B、C、D六個等級")

if __name__ == "__main__":
    main()
