#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的除權息資料格式 (stock_id 和 stock_name 分開)
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def clean_existing_database():
    """清理現有資料庫以測試新格式"""
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if os.path.exists(db_file):
        try:
            os.remove(db_file)
            print(f"🗑️ 已清理現有資料庫: {db_file}")
            return True
        except Exception as e:
            print(f"❌ 清理資料庫失敗: {e}")
            return False
    else:
        print(f"ℹ️ 資料庫檔案不存在，無需清理")
        return True

def test_new_format_crawler():
    """測試新格式的除權息爬蟲"""
    
    print("=" * 80)
    print("🧪 測試新格式除權息爬蟲 (stock_id 和 stock_name 分開)")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_divide_ratio
        print("✅ 成功導入 crawl_divide_ratio 函數")
        
        # 執行爬蟲
        print("🔄 開始執行統一除權息爬蟲...")
        result = crawl_divide_ratio()
        
        if result is not None and not result.empty:
            print(f"✅ 爬蟲執行成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 檢查新的資料格式
            print(f"\n📋 資料格式檢查:")
            
            # 檢查是否有 stock_name 欄位
            if 'stock_name' in result.columns:
                print(f"   ✅ 包含 stock_name 欄位")
            else:
                print(f"   ❌ 缺少 stock_name 欄位")
            
            # 檢查索引結構
            index_names = result.index.names
            print(f"   📊 索引結構: {index_names}")
            
            if 'stock_id' in index_names and 'date' in index_names:
                print(f"   ✅ 索引結構正確 (stock_id, date)")
            else:
                print(f"   ❌ 索引結構異常")
            
            # 檢查 stock_id 格式 (應該只包含代碼，不包含名稱)
            sample_stock_ids = result.index.get_level_values('stock_id').unique()[:5]
            print(f"\n📊 前5個 stock_id 範例:")
            for stock_id in sample_stock_ids:
                print(f"   {stock_id}")
            
            # 檢查是否有空格 (新格式不應該有空格)
            has_space = any(' ' in str(sid) for sid in sample_stock_ids)
            if has_space:
                print(f"   ⚠️ stock_id 中仍包含空格，可能格式未正確修正")
            else:
                print(f"   ✅ stock_id 格式正確 (不包含空格)")
            
            # 檢查市場分布
            if 'market' in result.columns:
                market_stats = result['market'].value_counts()
                print(f"\n📊 市場分布:")
                for market, count in market_stats.items():
                    market_name = '上市' if market == 'TWSE' else '上櫃'
                    print(f"   {market_name} ({market}): {count:,} 筆")
            
            # 顯示範例資料
            print(f"\n📊 前3筆資料範例:")
            sample_df = result.head(3)
            
            # 重設索引以便顯示
            sample_display = sample_df.reset_index()
            
            # 只顯示關鍵欄位
            key_columns = ['stock_id', 'stock_name', 'date', 'market', '除權息前收盤價', '開盤競價基準']
            available_columns = [col for col in key_columns if col in sample_display.columns]
            
            print(sample_display[available_columns].to_string(index=False))
            
            return True
        else:
            print(f"⚠️ 爬蟲執行返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_format():
    """檢查資料庫中的資料格式"""
    
    print(f"\n" + "=" * 80)
    print("📊 檢查資料庫中的資料格式")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            
            # 檢查表格結構
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(divide_ratio)")
            columns = cursor.fetchall()
            
            print(f"📋 資料庫表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查是否有 stock_name 欄位
            column_names = [col[1] for col in columns]
            if 'stock_name' in column_names:
                print(f"\n✅ 資料庫包含 stock_name 欄位")
            else:
                print(f"\n❌ 資料庫缺少 stock_name 欄位")
            
            # 檢查資料範例
            cursor.execute("SELECT stock_id, stock_name, date, market FROM divide_ratio LIMIT 5")
            sample_data = cursor.fetchall()
            
            print(f"\n📊 前5筆資料範例:")
            print(f"{'stock_id':<10} {'stock_name':<15} {'date':<12} {'market':<6}")
            print("-" * 50)
            for row in sample_data:
                stock_id = str(row[0])[:10]
                stock_name = str(row[1])[:15] if row[1] else 'N/A'
                date = str(row[2])[:10]
                market = str(row[3])
                print(f"{stock_id:<10} {stock_name:<15} {date:<12} {market:<6}")
            
            # 檢查 stock_id 格式
            cursor.execute("SELECT DISTINCT stock_id FROM divide_ratio LIMIT 10")
            stock_ids = [row[0] for row in cursor.fetchall()]
            
            print(f"\n📊 stock_id 格式檢查:")
            has_space = any(' ' in str(sid) for sid in stock_ids)
            if has_space:
                print(f"   ⚠️ 發現包含空格的 stock_id:")
                for sid in stock_ids:
                    if ' ' in str(sid):
                        print(f"      {sid}")
            else:
                print(f"   ✅ 所有 stock_id 格式正確 (不包含空格)")
            
            # 檢查台積電資料
            cursor.execute("SELECT stock_id, stock_name, date FROM divide_ratio WHERE stock_id = '2330' ORDER BY date DESC LIMIT 3")
            tsmc_data = cursor.fetchall()
            
            if tsmc_data:
                print(f"\n📈 台積電最近3筆除權息資料:")
                for row in tsmc_data:
                    print(f"   代碼: {row[0]}, 名稱: {row[1]}, 日期: {row[2]}")
            else:
                print(f"\n⚠️ 未找到台積電 (2330) 的資料")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 檢查資料庫失敗: {e}")
            return False
    else:
        print(f"❌ 資料庫檔案不存在: {db_file}")
        return False

def test_sql_queries():
    """測試 SQL 查詢"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試 SQL 查詢")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if not os.path.exists(db_file):
        print(f"❌ 資料庫檔案不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 測試查詢1: 按股票代碼查詢
        print(f"🔍 測試查詢1: 查詢台積電 (2330) 除權息資料")
        query1 = "SELECT stock_id, stock_name, date, 除權息前收盤價, 開盤競價基準 FROM divide_ratio WHERE stock_id = '2330' ORDER BY date DESC LIMIT 3"
        
        cursor = conn.cursor()
        cursor.execute(query1)
        results1 = cursor.fetchall()
        
        if results1:
            print(f"   ✅ 查詢成功，找到 {len(results1)} 筆資料")
            for row in results1:
                print(f"      {row[0]} {row[1]} {row[2]} 收盤價:{row[3]} 基準價:{row[4]}")
        else:
            print(f"   ⚠️ 未找到台積電資料")
        
        # 測試查詢2: 按市場查詢
        print(f"\n🔍 測試查詢2: 統計各市場除權息筆數")
        query2 = "SELECT market, COUNT(*) as count FROM divide_ratio GROUP BY market"
        
        cursor.execute(query2)
        results2 = cursor.fetchall()
        
        if results2:
            print(f"   ✅ 查詢成功")
            for row in results2:
                market_name = '上市' if row[0] == 'TWSE' else '上櫃'
                print(f"      {market_name} ({row[0]}): {row[1]:,} 筆")
        else:
            print(f"   ⚠️ 未找到市場統計資料")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL 查詢測試失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🧪 除權息資料格式修正測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步驟1: 清理現有資料庫
    clean_success = clean_existing_database()
    
    # 步驟2: 測試新格式爬蟲
    crawler_success = test_new_format_crawler()
    
    # 步驟3: 檢查資料庫格式
    database_success = check_database_format()
    
    # 步驟4: 測試 SQL 查詢
    query_success = test_sql_queries()
    
    print(f"\n" + "=" * 80)
    print("📊 測試結果總結")
    print("=" * 80)
    
    print(f"🗑️ 資料庫清理: {'✅ 成功' if clean_success else '❌ 失敗'}")
    print(f"🧪 爬蟲功能: {'✅ 成功' if crawler_success else '❌ 失敗'}")
    print(f"📊 資料庫格式: {'✅ 成功' if database_success else '❌ 失敗'}")
    print(f"🔍 SQL 查詢: {'✅ 成功' if query_success else '❌ 失敗'}")
    
    if all([clean_success, crawler_success, database_success, query_success]):
        print(f"\n🎉 除權息資料格式修正測試全部通過！")
        print(f"💡 新格式特點:")
        print(f"   - stock_id: 只包含股票代碼 (如 '2330')")
        print(f"   - stock_name: 單獨的股票名稱欄位 (如 '台積電')")
        print(f"   - 支援按代碼精確查詢")
        print(f"   - 資料結構更清晰")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
