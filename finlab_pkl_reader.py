#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab PKL檔案讀取器
專門用於讀取和分析Finlab課程中的pkl財務數據
"""

import pandas as pd
import pickle
import os
import sqlite3
from datetime import datetime

class FinlabPklReader:
    """Finlab PKL檔案讀取器"""
    
    def __init__(self):
        self.supported_files = {
            'balance_sheet.pkl': '資產負債表',
            'income_sheet.pkl': '綜合損益表', 
            'income_sheet_cumulate.pkl': '累計損益表',
            'cash_flows.pkl': '現金流量表',
            'monthly_report.pkl': '月營收報告',
            'pe.pkl': '本益比數據',
            'price.pkl': '股價數據',
            'twse_divide_ratio.pkl': '上市除權息',
            'otc_divide_ratio.pkl': '上櫃除權息',
            'bargin_report.pkl': '融資融券',
            'benchmark.pkl': '基準指數',
            'forign_hold_ratio.pkl': '外資持股比例'
        }
    
    def read_pkl_file(self, file_path):
        """讀取pkl檔案"""
        print(f"📊 讀取檔案: {os.path.basename(file_path)}")
        
        if not os.path.exists(file_path):
            print(f"❌ 檔案不存在: {file_path}")
            return None
        
        try:
            # 嘗試使用pandas讀取
            data = pd.read_pickle(file_path)
            print(f"✅ 成功讀取 (pandas)")
            return data
            
        except Exception as e:
            print(f"⚠️ pandas讀取失敗: {e}")
            
            try:
                # 嘗試使用pickle讀取
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                print(f"✅ 成功讀取 (pickle)")
                return data
                
            except Exception as e2:
                print(f"❌ pickle讀取失敗: {e2}")
                return None
    
    def analyze_data_structure(self, data, filename):
        """分析數據結構"""
        print(f"\n📋 {filename} 數據分析:")
        print("=" * 50)
        
        file_desc = self.supported_files.get(filename, "未知檔案")
        print(f"📝 檔案描述: {file_desc}")
        print(f"🔍 數據類型: {type(data)}")
        
        if isinstance(data, pd.DataFrame):
            self._analyze_dataframe(data)
        elif isinstance(data, dict):
            self._analyze_dict(data)
        elif isinstance(data, pd.Series):
            self._analyze_series(data)
        else:
            print(f"❓ 未知數據格式: {type(data)}")
    
    def _analyze_dataframe(self, df):
        """分析DataFrame"""
        print(f"📊 DataFrame 分析:")
        print(f"   形狀: {df.shape[0]:,} 行 × {df.shape[1]:,} 列")
        print(f"   記憶體使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        # 索引分析
        print(f"   索引類型: {type(df.index).__name__}")
        if hasattr(df.index, 'min') and hasattr(df.index, 'max'):
            try:
                print(f"   索引範圍: {df.index.min()} ~ {df.index.max()}")
            except:
                print(f"   索引範圍: 無法確定")
        
        # 列分析
        print(f"   列數量: {len(df.columns)}")
        if len(df.columns) <= 10:
            print(f"   列名: {list(df.columns)}")
        else:
            print(f"   前10列: {list(df.columns[:10])}")
            print(f"   後5列: {list(df.columns[-5:])}")
        
        # 數據類型
        print(f"   數據類型:")
        for dtype, count in df.dtypes.value_counts().items():
            print(f"     {dtype}: {count} 列")
        
        # 缺失值
        missing_count = df.isnull().sum().sum()
        total_cells = df.shape[0] * df.shape[1]
        missing_pct = (missing_count / total_cells) * 100 if total_cells > 0 else 0
        print(f"   缺失值: {missing_count:,} ({missing_pct:.2f}%)")
        
        # 樣本數據
        if len(df) > 0:
            print(f"\n📄 樣本數據 (前3行):")
            print(df.head(3).to_string())
            
            if len(df) > 3:
                print(f"\n📄 樣本數據 (後3行):")
                print(df.tail(3).to_string())
    
    def _analyze_dict(self, data_dict):
        """分析字典數據"""
        print(f"📚 字典分析:")
        print(f"   鍵數量: {len(data_dict)}")
        
        if len(data_dict) <= 10:
            print(f"   所有鍵: {list(data_dict.keys())}")
        else:
            print(f"   前10個鍵: {list(data_dict.keys())[:10]}")
        
        # 分析值的類型
        if data_dict:
            first_key = list(data_dict.keys())[0]
            first_value = data_dict[first_key]
            print(f"   值的類型: {type(first_value)}")
            
            if isinstance(first_value, pd.DataFrame):
                print(f"   內部DataFrame形狀: {first_value.shape}")
            elif isinstance(first_value, (int, float)):
                print(f"   數值範例: {first_value}")
            elif isinstance(first_value, str):
                print(f"   字串範例: '{first_value[:50]}{'...' if len(first_value) > 50 else ''}'")
    
    def _analyze_series(self, series):
        """分析Series數據"""
        print(f"📈 Series分析:")
        print(f"   長度: {len(series)}")
        print(f"   數據類型: {series.dtype}")
        print(f"   索引類型: {type(series.index).__name__}")
        
        if len(series) > 0:
            print(f"   範圍: {series.min()} ~ {series.max()}")
            print(f"   樣本值: {series.head(3).to_list()}")
    
    def convert_to_sqlite(self, data, output_db_path, table_name):
        """將pkl數據轉換為SQLite"""
        print(f"\n🔄 轉換為SQLite: {table_name}")
        
        try:
            conn = sqlite3.connect(output_db_path)
            
            if isinstance(data, pd.DataFrame):
                # DataFrame直接轉換
                data.to_sql(table_name, conn, if_exists='replace', index=True)
                print(f"✅ DataFrame轉換成功: {data.shape}")
                
            elif isinstance(data, dict):
                # 字典轉換
                if all(isinstance(v, (int, float, str)) for v in data.values()):
                    # 簡單字典轉為單行DataFrame
                    df = pd.DataFrame([data])
                    df.to_sql(table_name, conn, if_exists='replace', index=False)
                    print(f"✅ 字典轉換成功: {len(data)} 個欄位")
                else:
                    # 複雜字典嘗試轉為DataFrame
                    try:
                        df = pd.DataFrame(data)
                        df.to_sql(table_name, conn, if_exists='replace', index=True)
                        print(f"✅ 複雜字典轉換成功: {df.shape}")
                    except:
                        # 無法轉換，保存為JSON
                        import json
                        json_data = pd.DataFrame([{'data': json.dumps(data, default=str)}])
                        json_data.to_sql(f"{table_name}_json", conn, if_exists='replace', index=False)
                        print(f"✅ 字典轉JSON格式成功")
                        
            elif isinstance(data, pd.Series):
                # Series轉換
                df = data.to_frame()
                df.to_sql(table_name, conn, if_exists='replace', index=True)
                print(f"✅ Series轉換成功: {len(data)} 行")
                
            else:
                print(f"❌ 不支援的數據類型: {type(data)}")
                return False
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 轉換失敗: {e}")
            return False
    
    def compare_formats(self, pkl_path, db_path, table_name):
        """比較PKL和SQLite格式"""
        print(f"\n📊 格式比較: {os.path.basename(pkl_path)}")
        print("=" * 50)
        
        # PKL檔案資訊
        if os.path.exists(pkl_path):
            pkl_size = os.path.getsize(pkl_path)
            print(f"📦 PKL檔案:")
            print(f"   大小: {pkl_size:,} bytes ({pkl_size/1024:.1f} KB)")
            
            # 讀取速度測試
            import time
            start_time = time.time()
            data = self.read_pkl_file(pkl_path)
            pkl_read_time = (time.time() - start_time) * 1000
            print(f"   讀取時間: {pkl_read_time:.2f} ms")
        
        # SQLite檔案資訊
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path)
            print(f"\n🗄️ SQLite檔案:")
            print(f"   大小: {db_size:,} bytes ({db_size/1024:.1f} KB)")
            print(f"   大小比例: {db_size/pkl_size:.2f}x" if 'pkl_size' in locals() else "")
            
            # SQLite查詢測試
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                print(f"   記錄數: {row_count:,}")
                
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print(f"   欄位數: {len(columns)}")
                
                conn.close()
                
            except Exception as e:
                print(f"   查詢失敗: {e}")

def demo_finlab_pkl_reading():
    """演示Finlab PKL檔案讀取"""
    print("📚 Finlab PKL檔案讀取演示")
    print("=" * 80)
    
    reader = FinlabPklReader()
    
    # 創建示例數據（模擬Finlab格式）
    print("🔧 創建示例Finlab數據...")
    
    # 1. 資產負債表示例
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='Q')
    stocks = ['2330', '2317', '2454', '2881', '2882']
    
    import numpy as np
    np.random.seed(42)
    
    # 資產負債表數據
    balance_data = {}
    for stock in stocks:
        # 模擬總資產數據（單位：千元）
        base_assets = np.random.randint(1000000, 10000000)
        growth = np.random.uniform(1.02, 1.08)
        assets = [int(base_assets * (growth ** i)) for i in range(len(dates))]
        balance_data[stock] = assets
    
    balance_df = pd.DataFrame(balance_data, index=dates)
    balance_df.to_pickle('demo_balance_sheet.pkl')
    
    # 2. 月營收示例
    monthly_dates = pd.date_range('2020-01-01', '2023-12-31', freq='M')
    revenue_data = {}
    for stock in stocks:
        base_revenue = np.random.randint(100000, 1000000)
        seasonal = [1 + 0.2 * np.sin(2 * np.pi * i / 12) for i in range(len(monthly_dates))]
        revenues = [int(base_revenue * s * np.random.uniform(0.8, 1.2)) for s in seasonal]
        revenue_data[stock] = revenues
    
    revenue_df = pd.DataFrame(revenue_data, index=monthly_dates)
    revenue_df.to_pickle('demo_monthly_report.pkl')
    
    # 3. 本益比示例
    pe_data = {}
    for stock in stocks:
        base_pe = np.random.uniform(10, 30)
        pe_values = [base_pe * np.random.uniform(0.7, 1.3) for _ in range(100)]
        pe_data[stock] = pe_values
    
    pe_dates = pd.date_range('2023-01-01', periods=100, freq='D')
    pe_df = pd.DataFrame(pe_data, index=pe_dates)
    pe_df.to_pickle('demo_pe.pkl')
    
    # 演示讀取和分析
    demo_files = [
        'demo_balance_sheet.pkl',
        'demo_monthly_report.pkl', 
        'demo_pe.pkl'
    ]
    
    for pkl_file in demo_files:
        if os.path.exists(pkl_file):
            print(f"\n" + "="*80)
            
            # 讀取數據
            data = reader.read_pkl_file(pkl_file)
            
            if data is not None:
                # 分析結構
                reader.analyze_data_structure(data, os.path.basename(pkl_file))
                
                # 轉換為SQLite
                db_file = pkl_file.replace('.pkl', '.db')
                table_name = os.path.basename(pkl_file).replace('.pkl', '')
                
                if reader.convert_to_sqlite(data, db_file, table_name):
                    # 比較格式
                    reader.compare_formats(pkl_file, db_file, table_name)
    
    # 保留示例檔案供測試使用
    print(f"\n📁 保留示例檔案供測試:")
    for pkl_file in demo_files:
        if os.path.exists(pkl_file):
            print(f"   📊 {pkl_file} - 可用於轉換測試")

if __name__ == "__main__":
    demo_finlab_pkl_reading()
