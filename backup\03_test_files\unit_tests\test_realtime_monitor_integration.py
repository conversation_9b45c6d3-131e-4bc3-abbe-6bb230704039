#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試即時股價監控的統一管理器整合
"""

import sys
import os
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class RealtimeMonitorIntegrationTest(QMainWindow):
    """即時監控統一管理器整合測試"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("即時股價監控統一管理器整合測試")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🎯 即時股價監控統一管理器整合測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196f3; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 測試按鈕區域
        button_layout = QHBoxLayout()
        
        self.test_manager_btn = QPushButton("🧪 測試統一管理器")
        self.test_manager_btn.clicked.connect(self.test_unified_manager)
        button_layout.addWidget(self.test_manager_btn)
        
        self.test_monitor_btn = QPushButton("📊 測試即時監控")
        self.test_monitor_btn.clicked.connect(self.test_realtime_monitor)
        button_layout.addWidget(self.test_monitor_btn)
        
        self.test_integration_btn = QPushButton("🔗 測試整合功能")
        self.test_integration_btn.clicked.connect(self.test_integration)
        button_layout.addWidget(self.test_integration_btn)
        
        self.test_sync_btn = QPushButton("🔄 測試同步功能")
        self.test_sync_btn.clicked.connect(self.test_sync_functionality)
        button_layout.addWidget(self.test_sync_btn)
        
        layout.addLayout(button_layout)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        self.log("🚀 即時監控統一管理器整合測試系統已啟動")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def test_unified_manager(self):
        """測試統一管理器"""
        self.log("🧪 開始測試統一管理器...")
        
        try:
            from unified_monitor_manager import get_monitor_manager
            monitor_manager = get_monitor_manager()
            
            self.log("✅ 統一監控管理器載入成功")
            
            # 測試基本功能
            stocks = monitor_manager.get_stocks()
            self.log(f"📊 當前監控股票: {len(stocks)} 支 - {', '.join(stocks)}")
            
            stats = monitor_manager.get_stats()
            self.log(f"📈 統計資訊: {stats}")
            
            self.log("🎉 統一管理器測試完成")
            
        except Exception as e:
            self.log(f"❌ 統一管理器測試失敗: {e}")
    
    def test_realtime_monitor(self):
        """測試即時監控"""
        self.log("📊 開始測試即時監控...")
        
        try:
            from real_time_stock_monitor import RealTimeStockMonitor
            
            self.log("✅ 即時監控模組載入成功")
            
            # 創建即時監控實例（不顯示視窗）
            monitor = RealTimeStockMonitor()
            
            # 檢查統一管理器整合
            if hasattr(monitor, 'monitor_manager') and monitor.monitor_manager:
                self.log("✅ 統一管理器已整合到即時監控")
                
                # 檢查自選股清單
                watchlist = monitor.watchlist
                self.log(f"📊 即時監控自選股: {len(watchlist)} 支 - {', '.join(watchlist)}")
                
                # 檢查信號連接
                if hasattr(monitor, 'on_watchlist_updated'):
                    self.log("✅ 股票更新回調方法存在")
                else:
                    self.log("❌ 股票更新回調方法不存在")
                
            else:
                self.log("❌ 統一管理器未整合到即時監控")
            
            # 清理
            monitor.close()
            
            self.log("🎉 即時監控測試完成")
            
        except Exception as e:
            self.log(f"❌ 即時監控測試失敗: {e}")
    
    def test_integration(self):
        """測試整合功能"""
        self.log("🔗 開始測試整合功能...")
        
        try:
            # 測試統一管理器和即時監控的整合
            from unified_monitor_manager import get_monitor_manager
            from real_time_stock_monitor import RealTimeStockMonitor
            
            monitor_manager = get_monitor_manager()
            monitor = RealTimeStockMonitor()
            
            # 檢查初始狀態
            manager_stocks = monitor_manager.get_stocks()
            monitor_stocks = monitor.watchlist
            
            self.log(f"📊 管理器股票: {', '.join(manager_stocks)}")
            self.log(f"📊 監控股票: {', '.join(monitor_stocks)}")
            
            if manager_stocks == monitor_stocks:
                self.log("✅ 初始狀態同步正確")
            else:
                self.log("❌ 初始狀態同步異常")
            
            # 清理
            monitor.close()
            
            self.log("🎉 整合功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 整合功能測試失敗: {e}")
    
    def test_sync_functionality(self):
        """測試同步功能"""
        self.log("🔄 開始測試同步功能...")
        
        try:
            from unified_monitor_manager import get_monitor_manager
            from real_time_stock_monitor import RealTimeStockMonitor
            
            monitor_manager = get_monitor_manager()
            monitor = RealTimeStockMonitor()
            
            # 記錄初始狀態
            initial_stocks = monitor_manager.get_stocks().copy()
            self.log(f"📊 初始股票清單: {', '.join(initial_stocks)}")
            
            # 添加測試股票
            test_stock = "1234"
            self.log(f"➕ 添加測試股票: {test_stock}")
            
            success = monitor_manager.add_stock(test_stock)
            if success:
                self.log("✅ 測試股票添加成功")
                
                # 檢查即時監控是否同步
                updated_stocks = monitor.watchlist
                if test_stock in updated_stocks:
                    self.log("✅ 即時監控已同步更新")
                else:
                    self.log("❌ 即時監控未同步更新")
                
                # 移除測試股票
                self.log(f"➖ 移除測試股票: {test_stock}")
                success = monitor_manager.remove_stock(test_stock)
                if success:
                    self.log("✅ 測試股票移除成功")
                else:
                    self.log("❌ 測試股票移除失敗")
            else:
                self.log("❌ 測試股票添加失敗")
            
            # 清理
            monitor.close()
            
            self.log("🎉 同步功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 同步功能測試失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("即時監控整合測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = RealtimeMonitorIntegrationTest()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
