#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium測試GoodInfo頁面載入
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time

def test_selenium_access():
    """測試Selenium訪問GoodInfo"""
    driver = None
    try:
        print("🚀 啟動Chrome瀏覽器...")
        
        # 設置Chrome選項
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 設置下載目錄
        prefs = {
            "download.default_directory": "C:/Users/<USER>/Downloads",
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=options)
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        
        driver.get(url)
        
        # 等待並檢查頁面載入狀態
        print("⏳ 等待頁面載入...")
        
        for i in range(30):  # 最多等待30秒
            try:
                page_source = driver.page_source
                title = driver.title
                
                print(f"第{i+1}秒 - 標題: {title[:50]}...")
                
                if "初始化中" in page_source or "請稍候" in page_source:
                    print(f"第{i+1}秒 - 頁面仍在初始化...")
                    time.sleep(1)
                    continue
                else:
                    print(f"第{i+1}秒 - ✅ 頁面初始化完成")
                    break
                    
            except Exception as e:
                print(f"第{i+1}秒 - 檢查頁面時出錯: {e}")
                time.sleep(1)
        
        # 檢查最終頁面內容
        final_source = driver.page_source
        final_title = driver.title
        
        print(f"\n📄 最終標題: {final_title}")
        print(f"📄 頁面內容長度: {len(final_source)}")
        
        # 檢查營收相關內容
        revenue_keywords = ['營收', '月營收', '單月營收', '累計營收']
        found_revenue = []
        
        for keyword in revenue_keywords:
            if keyword in final_source:
                found_revenue.append(keyword)
        
        print(f"📊 找到的營收關鍵字: {found_revenue}")
        
        # 檢查匯出按鈕
        try:
            # 尋找可能的匯出按鈕
            export_buttons = driver.find_elements(By.XPATH, "//input[@type='button' and contains(@value, 'Excel')]")
            if not export_buttons:
                export_buttons = driver.find_elements(By.XPATH, "//input[@type='button' and contains(@value, '匯出')]")
            if not export_buttons:
                export_buttons = driver.find_elements(By.XPATH, "//input[@type='button']")
            
            print(f"🔘 找到 {len(export_buttons)} 個按鈕")
            
            for i, btn in enumerate(export_buttons[:5]):  # 只顯示前5個
                try:
                    value = btn.get_attribute("value")
                    onclick = btn.get_attribute("onclick")
                    print(f"   按鈕{i+1}: 值='{value}', 點擊事件='{onclick}'")
                except:
                    pass
                    
        except Exception as e:
            print(f"❌ 尋找按鈕時出錯: {e}")
        
        # 保存頁面截圖
        try:
            driver.save_screenshot("goodinfo_page_screenshot.png")
            print("📸 頁面截圖已保存到 goodinfo_page_screenshot.png")
        except:
            pass
        
        # 保存頁面源碼
        with open("goodinfo_selenium_source.html", "w", encoding="utf-8") as f:
            f.write(final_source)
        print("💾 頁面源碼已保存到 goodinfo_selenium_source.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    test_selenium_access()
