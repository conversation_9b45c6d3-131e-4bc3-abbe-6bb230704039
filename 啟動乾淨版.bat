@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 乾淨版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        乾淨版 - 無錯誤訊息版
echo ========================================
echo.

if exist "dist\StockAnalyzer_Clean.exe" (
    echo ✅ 找到乾淨版
    echo 🚀 正在啟動...
    echo.
    echo 💡 乾淨版特點：
    echo    ✓ 減少錯誤訊息顯示
    echo    ✓ 優化用戶體驗
    echo    ✓ 保持所有核心功能
    echo    ✓ 最高穩定性
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Clean.exe"
    
    echo ✅ 乾淨版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到乾淨版
    echo.
    echo 請重新編譯：
    echo    python clean_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 功能說明：
echo    ✓ 核心股票分析功能
echo    ✓ 數據查詢和篩選
echo    ✓ Excel 報告導出
echo    ✓ 完整用戶界面
echo    ✓ 減少錯誤訊息干擾
echo.

timeout /t 5 >nul
