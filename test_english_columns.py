#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的爬蟲是否輸出英文欄位名稱
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_english_columns():
    """測試爬蟲是否輸出英文欄位名稱"""
    
    print("=" * 60)
    print("🧪 測試爬蟲英文欄位名稱輸出")
    print("=" * 60)
    
    try:
        from crawler import crawl_price
        
        # 測試一個已知有資料的日期
        test_date = datetime(2023, 6, 30)  # 週五，應該有資料
        
        print(f"🔍 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        df = crawl_price(test_date)
        
        if df is not None and not df.empty:
            print(f"✅ 成功爬取 {len(df)} 筆資料")
            
            # 檢查欄位名稱
            df_reset = df.reset_index()
            columns = list(df_reset.columns)
            
            print(f"\n📋 爬蟲輸出的欄位名稱:")
            for i, col in enumerate(columns, 1):
                print(f"   {i:2d}. {col}")
            
            # 檢查是否包含預期的英文欄位（Change 欄位在原始資料中不存在，會在存儲時設為 0）
            expected_english_columns = [
                'Volume', 'Transaction', 'TradeValue',
                'Open', 'High', 'Low', 'Close'
            ]
            
            print(f"\n🔍 檢查英文欄位:")
            all_english = True
            for col in expected_english_columns:
                if col in columns:
                    print(f"   ✅ {col}: 存在")
                else:
                    print(f"   ❌ {col}: 不存在")
                    all_english = False
            
            # 檢查是否還有中文欄位
            chinese_columns = [
                '成交股數', '成交筆數', '成交金額', 
                '開盤價', '最高價', '最低價', '收盤價', '漲跌價差'
            ]
            
            print(f"\n🔍 檢查中文欄位（應該不存在）:")
            has_chinese = False
            for col in chinese_columns:
                if col in columns:
                    print(f"   ⚠️ {col}: 仍存在（需要修正）")
                    has_chinese = True
                else:
                    print(f"   ✅ {col}: 已移除")
            
            # 檢查 0050 的資料範例
            etf_0050 = df_reset[df_reset['stock_id'] == '0050']
            
            if not etf_0050.empty:
                row = etf_0050.iloc[0]
                print(f"\n📊 0050 資料範例:")
                print(f"   股票代碼: {row['stock_id']}")
                print(f"   日期: {row['date']}")
                print(f"   股票名稱: {row.get('stock_name', 'N/A')}")
                print(f"   上市狀態: {row.get('listing_status', 'N/A')}")
                print(f"   產業: {row.get('industry', 'N/A')}")
                
                if 'Volume' in row:
                    print(f"   成交量 (Volume): {row['Volume']}")
                if 'Close' in row:
                    print(f"   收盤價 (Close): {row['Close']}")
                if 'Open' in row:
                    print(f"   開盤價 (Open): {row['Open']}")
            
            # 總結
            print(f"\n📊 測試結果:")
            if all_english and not has_chinese:
                print(f"   ✅ 完全成功！爬蟲已輸出英文欄位名稱")
                return True
            elif all_english and has_chinese:
                print(f"   ⚠️ 部分成功：英文欄位存在，但中文欄位未完全移除")
                return False
            else:
                print(f"   ❌ 失敗：英文欄位不完整")
                return False
                
        else:
            print(f"❌ 爬取失敗或無資料")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_english_columns()
    
    if success:
        print(f"\n🎉 修正成功！現在可以執行 auto_update.py 進行更新")
    else:
        print(f"\n⚠️ 需要進一步修正")
