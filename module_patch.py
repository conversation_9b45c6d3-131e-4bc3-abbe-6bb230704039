
# 模組補丁 - 修復缺失的模組
import sys

# 修復 inspect 模組
if 'inspect' not in sys.modules:
    try:
        import inspect
    except ImportError:
        class MockInspect:
            @staticmethod
            def signature(func):
                class MockSignature:
                    def __init__(self):
                        self.parameters = {}
                return MockSignature()
            
            @staticmethod
            def getmembers(obj, predicate=None):
                return []
            
            @staticmethod
            def isfunction(obj):
                return callable(obj)
        
        sys.modules['inspect'] = MockInspect()

# 修復 pydoc 模組
if 'pydoc' not in sys.modules:
    try:
        import pydoc
    except ImportError:
        class MockPydoc:
            @staticmethod
            def help(obj):
                return f"Help for {obj}"
            
            @staticmethod
            def doc(obj):
                return getattr(obj, '__doc__', 'No documentation available')
        
        sys.modules['pydoc'] = MockPydoc()

# 修復 doctest 模組
if 'doctest' not in sys.modules:
    try:
        import doctest
    except ImportError:
        class MockDoctest:
            @staticmethod
            def testmod(*args, **kwargs):
                return None
        
        sys.modules['doctest'] = MockDoctest()

# 修復 difflib 模組
if 'difflib' not in sys.modules:
    try:
        import difflib
    except ImportError:
        class MockDifflib:
            @staticmethod
            def unified_diff(*args, **kwargs):
                return []
        
        sys.modules['difflib'] = MockDifflib()

print("✅ 模組補丁已載入")
