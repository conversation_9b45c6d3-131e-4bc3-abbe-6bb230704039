#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試右鍵選單修復
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_right_click_fix():
    """測試右鍵選單修復"""
    print("=" * 60)
    print("🔧 測試右鍵選單修復")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QTableWidgetItem
        from PyQt6.QtCore import QPoint
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 設置月營收排行榜表格
        print("\n📊 設置月營收排行榜表格...")
        gui.result_table.setColumnCount(14)
        gui.result_table.setHorizontalHeaderLabels([
            "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
            "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
            "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
        ])
        
        # 填入測試資料
        gui.result_table.setRowCount(2)
        test_data = [
            ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
             "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
            ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
             "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                gui.result_table.setItem(row, col, item)
        
        print("✅ 測試資料填入完成")
        
        # 測試月營收排行榜檢測
        print("\n🔍 測試月營收排行榜檢測...")
        is_monthly = gui.is_monthly_revenue_ranking()
        print(f"檢測結果: {'✅ 是月營收排行榜' if is_monthly else '❌ 不是月營收排行榜'}")
        
        # 如果檢測成功，測試右鍵選單
        if is_monthly:
            print("\n🖱️ 測試右鍵選單...")
            
            # 模擬右鍵點擊
            position = QPoint(100, 50)
            
            # 直接調用右鍵選單函數
            print("正在調用 show_stock_context_menu...")
            gui.show_stock_context_menu(position)
            print("✅ 右鍵選單函數調用完成")
            
        else:
            print("❌ 月營收排行榜檢測失敗，無法測試右鍵選單")
        
        # 顯示GUI
        print("\n🖥️ 顯示GUI進行手動測試...")
        print("請執行以下步驟:")
        print("1. 選擇日期 2025-07-29")
        print("2. 選擇「月營收排行榜 (YoY排序) + 綜合評分」")
        print("3. 點擊「執行排行」")
        print("4. 在結果表格中右鍵點擊任一股票")
        print("5. 檢查是否有「月營收綜合評估」選項")
        
        gui.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_test():
    """創建最小化測試"""
    print("\n" + "=" * 60)
    print("🧪 創建最小化右鍵選單測試")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, 
                                   QTableWidgetItem, QVBoxLayout, QWidget, 
                                   QMenu, QLabel)
        from PyQt6.QtCore import Qt
        
        class MinimalTestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("最小化右鍵選單測試")
                self.setGeometry(100, 100, 1000, 300)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # 添加說明標籤
                label = QLabel("這是月營收排行榜右鍵選單測試。請在表格中右鍵點擊股票。")
                layout.addWidget(label)
                
                # 創建表格
                self.table = QTableWidget()
                self.table.setColumnCount(14)
                self.table.setRowCount(3)
                
                # 設置標題（完全模擬月營收排行榜）
                self.table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                    "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                    "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
                ])
                
                # 填入測試資料
                test_data = [
                    ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
                     "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
                    ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
                     "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"],
                    ["3", "2454", "聯發科", "202507", "85,000,000", "80,000,000", "75,000,000", 
                     "+13.33%", "*****%", "2.80", "18.20", "3.20", "46.70", "82.1"]
                ]
                
                for row, row_data in enumerate(test_data):
                    for col, cell_data in enumerate(row_data):
                        item = QTableWidgetItem(str(cell_data))
                        self.table.setItem(row, col, item)
                
                # 設置右鍵選單
                self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.table.customContextMenuRequested.connect(self.show_context_menu)
                
                layout.addWidget(self.table)
                
                print("✅ 最小化測試視窗創建完成")
            
            def show_context_menu(self, position):
                """顯示右鍵選單"""
                print(f"🖱️ 右鍵點擊位置: {position}")
                
                # 獲取點擊的項目
                item = self.table.itemAt(position)
                if not item:
                    print("❌ 沒有點擊到有效項目")
                    return
                
                row = item.row()
                stock_code = self.table.item(row, 1).text()
                stock_name = self.table.item(row, 2).text()
                
                print(f"📈 股票: {stock_code} {stock_name}")
                
                # 創建右鍵選單
                context_menu = QMenu(self)
                context_menu.setStyleSheet("""
                    QMenu {
                        background-color: #2b2b2b;
                        color: white;
                        border: 1px solid #555;
                        border-radius: 5px;
                        padding: 5px;
                    }
                    QMenu::item {
                        padding: 8px 20px;
                        border-radius: 3px;
                    }
                    QMenu::item:selected {
                        background-color: #4CAF50;
                    }
                    QMenu::separator {
                        height: 1px;
                        background-color: #555;
                        margin: 5px 0px;
                    }
                """)
                
                # 🎯 添加月營收綜合評估選項
                assessment_action = context_menu.addAction(f"📊 {stock_code} {stock_name} 月營收綜合評估")
                assessment_action.triggered.connect(lambda: self.show_assessment(stock_code, stock_name, row))
                
                context_menu.addSeparator()
                
                # 添加其他選項
                news_action = context_menu.addAction(f"📰 爬取 {stock_code} {stock_name} 新聞")
                news_action.triggered.connect(lambda: print(f"📰 爬取 {stock_code} {stock_name} 新聞"))
                
                chart_action = context_menu.addAction(f"📈 查看 {stock_code} K線圖")
                chart_action.triggered.connect(lambda: print(f"📈 查看 {stock_code} K線圖"))
                
                info_action = context_menu.addAction(f"ℹ️ 查看 {stock_code} 基本資料")
                info_action.triggered.connect(lambda: print(f"ℹ️ 查看 {stock_code} 基本資料"))
                
                context_menu.addSeparator()
                
                monitor_action = context_menu.addAction(f"📊 加入監控清單")
                monitor_action.triggered.connect(lambda: print(f"📊 加入 {stock_code} 到監控清單"))
                
                # 顯示選單
                context_menu.exec(self.table.mapToGlobal(position))
                print("✅ 右鍵選單顯示完成")
            
            def show_assessment(self, stock_code, stock_name, row):
                """顯示評估"""
                print(f"🎯 顯示 {stock_code} {stock_name} 的月營收綜合評估")
                
                # 獲取該行的所有資料
                data = {}
                headers = ["排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                          "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                          "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"]
                
                for col, header in enumerate(headers):
                    item = self.table.item(row, col)
                    data[header] = item.text() if item else "N/A"
                
                print("📊 股票資料:")
                for key, value in data.items():
                    print(f"   {key}: {value}")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建測試視窗
        test_window = MinimalTestWindow()
        test_window.show()
        
        print("🖥️ 最小化測試視窗已顯示")
        print("請右鍵點擊表格中的股票，檢查是否有「月營收綜合評估」選項")
        
        return True
        
    except Exception as e:
        print(f"❌ 最小化測試創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始測試右鍵選單修復...")
    
    # 主要測試
    success1 = test_right_click_fix()
    
    # 最小化測試
    success2 = create_minimal_test()
    
    if success1 and success2:
        print("\n🎉 測試完成！")
        print("\n📋 如果右鍵選單仍未正常顯示，請檢查:")
        print("  1. 是否正確執行了月營收排行榜查詢")
        print("  2. 表格是否有資料")
        print("  3. 右鍵點擊是否在有效的表格項目上")
        print("  4. 查看控制台日誌輸出")
    else:
        print("\n❌ 測試過程中發現問題。")
