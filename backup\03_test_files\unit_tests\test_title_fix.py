#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試標題修正
"""

def test_title_parsing():
    """測試標題解析邏輯"""
    print("🚀 測試標題解析邏輯")
    print("=" * 50)
    
    # 模擬不同的標題文字格式
    test_cases = [
        "2025/07/11\n台積電Q2財報超預期，AI需求強勁推動營收成長",
        "台積電先進製程產能滿載\n2025/07/10",
        "台積電3奈米製程技術領先，獲蘋果大單",
        "2025/07/09\n",
        "\n台積電ADR上漲2.5%，市場看好AI晶片需求"
    ]
    
    stock_code = "2330"
    
    for i, title_text in enumerate(test_cases):
        print(f"\n測試案例 {i+1}: '{title_text}'")
        
        title_parts = title_text.split('\n')
        
        # 智能識別標題和日期
        if len(title_parts) >= 2:
            first_part = title_parts[0].strip()
            second_part = title_parts[1].strip()
            
            # 如果第一部分包含日期格式，則它是日期
            if '/' in first_part and len(first_part) <= 15:
                date_str = first_part
                title = second_part
            else:
                # 否則第一部分是標題
                title = first_part
                date_str = second_part if '/' in second_part else "2025-07-13"
        else:
            title = title_text
            date_str = "2025-07-13"
        
        # 確保標題不為空且不是日期
        if not title.strip() or '/' in title:
            title = f"鉅亨網新聞 - {stock_code}"
        
        # 清理標題
        title = title.strip().replace('\n', ' ').replace('\r', ' ')
        while '  ' in title:
            title = title.replace('  ', ' ')
        
        print(f"  解析結果:")
        print(f"    日期: '{date_str}'")
        print(f"    標題: '{title}'")

def test_quick_crawl():
    """快速測試爬蟲"""
    print("\n🔍 快速測試爬蟲標題提取")
    print("-" * 40)
    
    try:
        from news_crawler_cnyes_only import CnyesNewsCrawler
        
        crawler = CnyesNewsCrawler()
        news_list = crawler.quick_search_cnyes("2330", days=1)
        
        print(f"📊 找到 {len(news_list)} 筆新聞")
        
        if news_list:
            print("\n📰 前5筆新聞標題:")
            for i, news in enumerate(news_list[:5]):
                title = news.get('title', '')
                date = news.get('date', '')
                
                # 檢查標題是否是日期格式
                is_date_title = '/' in title and len(title) <= 15
                
                status = "❌ 標題是日期" if is_date_title else "✅ 標題正常"
                
                print(f"  第{i+1}筆 {status}")
                print(f"    日期: {date}")
                print(f"    標題: {title[:80]}...")
                print()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    print("🔍 標題修正測試")
    print(f"⏰ 測試時間: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試解析邏輯
    test_title_parsing()
    
    # 測試實際爬蟲
    test_quick_crawl()
    
    print("\n🎉 測試完成！")
    print("💡 如果標題仍顯示為日期，請檢查網站結構是否有變化")
