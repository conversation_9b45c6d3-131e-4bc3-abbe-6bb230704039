#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高殖利率烏龜策略 - 適配原始FinLab PKL結構
基於原始FinLab數據結構的投資策略
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime, timedelta

class HighYieldTurtleStrategy:
    """
    高殖利率烏龜策略 - 原始FinLab版本
    
    策略特點:
    1. 使用原始FinLab PKL數據結構 (MultiIndex: stock_id, date)
    2. 股票代碼格式: "1101 台泥" (包含代碼和公司名稱)
    3. 尋找高殖利率、低估值的穩健股票
    4. 適合長期持有的價值投資策略
    """
    
    def __init__(self):
        self.name = "高殖利率烏龜策略"
        self.description = "尋找高殖利率、低估值的穩健股票，適合長期持有"
        self.version = "2.0 - 原始FinLab版本"
        
        # 設置日誌
        self.logger = logging.getLogger(__name__)
        
        # PKL數據路徑 - 使用統一的資料庫目錄
        self.pe_pkl_path = 'D:/Finlab/history/tables/pe.pkl'
        self.pe_data_cache = None
        self.load_pkl_data()
    
    def load_pkl_data(self):
        """載入原始FinLab PKL數據"""
        try:
            self.logger.info(f"🔄 載入原始FinLab PKL數據: {self.pe_pkl_path}")
            
            if os.path.exists(self.pe_pkl_path):
                # 讀取原始FinLab PKL數據
                raw_data = pd.read_pickle(self.pe_pkl_path)
                
                # 處理原始數據結構 (MultiIndex: stock_id, date)
                self.pe_data_cache = raw_data.copy()
                
                # 轉換數據類型 - 原始數據都是object類型
                numeric_columns = ['殖利率(%)', '本益比', '股價淨值比']
                for col in numeric_columns:
                    if col in self.pe_data_cache.columns:
                        self.pe_data_cache[col] = pd.to_numeric(self.pe_data_cache[col], errors='coerce')
                
                # 獲取統計信息
                if hasattr(self.pe_data_cache.index, 'get_level_values'):
                    stock_ids = self.pe_data_cache.index.get_level_values('stock_id')
                    unique_stocks = stock_ids.nunique()
                    sample_stocks = stock_ids.unique()[:5].tolist()
                    date_range = self.pe_data_cache.index.get_level_values('date')
                    min_date = date_range.min()
                    max_date = date_range.max()
                else:
                    unique_stocks = 0
                    sample_stocks = ['無法提取']
                    min_date = max_date = 'N/A'
                
                self.logger.info(f"✅ 原始FinLab PKL數據載入成功")
                self.logger.info(f"📊 數據筆數: {len(self.pe_data_cache):,}")
                self.logger.info(f"📈 涵蓋股票數: {unique_stocks:,} 支")
                self.logger.info(f"📅 時間範圍: {min_date} ~ {max_date}")
                self.logger.info(f"📋 樣本股票: {sample_stocks}")
                
                print(f"✅ 原始FinLab PKL數據載入成功")
                print(f"📊 數據筆數: {len(self.pe_data_cache):,}")
                print(f"📈 涵蓋股票數: {unique_stocks:,} 支")
                
            else:
                self.logger.warning(f"⚠️ PKL檔案不存在: {self.pe_pkl_path}")
                print(f"⚠️ PKL檔案不存在: {self.pe_pkl_path}")
                self.pe_data_cache = None
                
        except Exception as e:
            self.logger.error(f"❌ 載入PKL數據失敗: {e}")
            print(f"❌ 載入PKL數據失敗: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self.pe_data_cache = None
    
    def extract_stock_code(self, stock_id_with_name):
        """從 '1101 台泥' 格式中提取股票代碼 '1101'"""
        if isinstance(stock_id_with_name, str):
            parts = stock_id_with_name.split(' ')
            if len(parts) >= 1:
                return parts[0]
        return str(stock_id_with_name)
    
    def find_stock_in_pkl(self, target_stock_id):
        """在PKL數據中尋找股票"""
        if self.pe_data_cache is None:
            return None
        
        try:
            # 獲取所有股票代碼
            if hasattr(self.pe_data_cache.index, 'get_level_values'):
                stock_ids = self.pe_data_cache.index.get_level_values('stock_id')
                
                # 尋找匹配的股票
                for stock_id_with_name in stock_ids.unique():
                    stock_code = self.extract_stock_code(stock_id_with_name)
                    if stock_code == target_stock_id:
                        return stock_id_with_name
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 尋找股票失敗: {e}")
            return None
    
    def get_stock_pkl_data(self, stock_id):
        """從原始FinLab PKL數據中獲取股票資料"""
        self.logger.debug(f"🔍 查找PKL數據: {stock_id}")
        
        if self.pe_data_cache is None:
            self.logger.warning(f"⚠️ PKL數據快取為空: {stock_id}")
            return None
        
        try:
            # 尋找完整的股票代碼 (包含公司名稱)
            full_stock_id = self.find_stock_in_pkl(stock_id)
            if full_stock_id is None:
                self.logger.debug(f"⚠️ 未找到PKL數據: {stock_id}")
                return None
            
            # 獲取該股票的所有數據
            stock_data = self.pe_data_cache.loc[full_stock_id]
            
            if stock_data.empty:
                self.logger.debug(f"⚠️ 股票數據為空: {stock_id}")
                return None
            
            # 取最新數據
            if isinstance(stock_data, pd.Series):
                latest = stock_data
            else:
                latest = stock_data.iloc[-1]
            
            # 構建返回數據
            pkl_info = {
                '股票代號': stock_id,
                '完整代號': full_stock_id,
                '殖利率(%)': latest.get('殖利率(%)', 0),
                '本益比': latest.get('本益比', 0),
                '股價淨值比': latest.get('股價淨值比', 0),
                '股利年度': latest.get('股利年度', ''),
                '數據來源': 'FinLab原始PKL'
            }
            
            self.logger.debug(f"✅ 找到PKL數據: {stock_id} -> {pkl_info}")
            return pkl_info
            
        except Exception as e:
            self.logger.error(f"❌ PKL數據分析失敗 {stock_id}: {e}")
            return None
    
    def calculate_dividend_score(self, dividend_yield):
        """計算殖利率評分"""
        try:
            dividend = float(dividend_yield) if dividend_yield else 0
            
            if dividend >= 8.0:
                return 50  # 極高殖利率
            elif dividend >= 6.0:
                return 40  # 高殖利率
            elif dividend >= 4.0:
                return 30  # 中等殖利率
            elif dividend >= 2.0:
                return 20  # 低殖利率
            else:
                return 0   # 極低殖利率
                
        except (ValueError, TypeError):
            return 0
    
    def calculate_valuation_score(self, pe_ratio, pb_ratio):
        """計算估值評分"""
        score = 0
        
        try:
            # 本益比評分
            pe = float(pe_ratio) if pe_ratio else 999
            if 0 < pe <= 10:
                score += 25  # 極低本益比
            elif pe <= 15:
                score += 20  # 低本益比
            elif pe <= 20:
                score += 15  # 合理本益比
            elif pe <= 30:
                score += 10  # 偏高本益比
            
            # 股價淨值比評分
            pb = float(pb_ratio) if pb_ratio else 999
            if 0 < pb <= 1.0:
                score += 15  # 極低股價淨值比
            elif pb <= 1.5:
                score += 12  # 低股價淨值比
            elif pb <= 2.0:
                score += 8   # 合理股價淨值比
            elif pb <= 3.0:
                score += 5   # 偏高股價淨值比
                
        except (ValueError, TypeError):
            pass
        
        return score
    
    def calculate_technical_score(self, stock_df):
        """計算技術面評分"""
        if stock_df is None or len(stock_df) < 20:
            return 0
        
        score = 0
        
        try:
            # 價格趨勢評分
            recent_prices = stock_df['Close'].tail(20)
            price_trend = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0] * 100
            
            if -5 <= price_trend <= 15:  # 溫和上漲或小幅下跌
                score += 20
            elif -10 <= price_trend < -5:  # 適度回調
                score += 15
            elif price_trend > 15:  # 漲幅過大
                score += 10
            
            # 成交量穩定性評分
            recent_volumes = stock_df['Volume'].tail(20)
            volume_cv = recent_volumes.std() / recent_volumes.mean() if recent_volumes.mean() > 0 else 999
            
            if volume_cv < 0.5:  # 成交量穩定
                score += 10
            elif volume_cv < 1.0:  # 成交量適中
                score += 5
            
        except Exception as e:
            self.logger.debug(f"技術面評分計算失敗: {e}")
        
        return score
    
    def analyze_stock(self, stock_df, stock_id=None):
        """分析股票是否符合高殖利率烏龜策略"""
        
        if stock_id is None:
            return {
                'suitable': False,
                'score': 0,
                'reason': '未提供股票代碼',
                'details': {'data_source': '未知'}
            }
        
        try:
            # 獲取PKL數據
            pkl_info = self.get_stock_pkl_data(stock_id)
            
            if pkl_info is None:
                return {
                    'suitable': False,
                    'score': 0,
                    'reason': f'無法獲取 {stock_id} 的PE數據',
                    'details': {'data_source': '未知'}
                }
            
            # 提取關鍵指標
            dividend_yield = pkl_info.get('殖利率(%)', 0)
            pe_ratio = pkl_info.get('本益比', 0)
            pb_ratio = pkl_info.get('股價淨值比', 0)
            
            # 計算各項評分
            dividend_score = self.calculate_dividend_score(dividend_yield)
            valuation_score = self.calculate_valuation_score(pe_ratio, pb_ratio)
            technical_score = self.calculate_technical_score(stock_df)
            
            # 計算總分
            total_score = dividend_score + valuation_score + technical_score
            
            # 判斷是否符合策略
            suitable = (
                dividend_score >= 30 and  # 殖利率至少4%
                valuation_score >= 20 and  # 估值合理
                total_score >= 70  # 總分至少70
            )
            
            # 構建詳細信息
            details = {
                'dividend_yield': dividend_yield,
                'dividend_score': dividend_score,
                'pe_ratio': pe_ratio,
                'pb_ratio': pb_ratio,
                'valuation_score': valuation_score,
                'technical_score': technical_score,
                'total_score': total_score,
                'pe_info': pkl_info,
                'data_source': 'FinLab原始PKL數據',
                'full_stock_id': pkl_info.get('完整代號', stock_id)
            }
            
            # 添加股價信息
            if stock_df is not None and len(stock_df) > 0:
                details['close_price'] = stock_df['Close'].iloc[-1]
                details['volume'] = stock_df['Volume'].iloc[-1]
                details['price_trend'] = ((stock_df['Close'].iloc[-1] - stock_df['Close'].iloc[0]) / stock_df['Close'].iloc[0] * 100) if len(stock_df) > 1 else 0
            
            return {
                'suitable': suitable,
                'score': total_score,
                'reason': f'殖利率{dividend_yield}%, 本益比{pe_ratio}, 總分{total_score}',
                'details': details
            }
            
        except Exception as e:
            self.logger.error(f"❌ 股票分析失敗 {stock_id}: {e}")
            return {
                'suitable': False,
                'score': 0,
                'reason': f'分析過程發生錯誤: {str(e)}',
                'details': {'data_source': '錯誤'}
            }
    
    def get_strategy_info(self):
        """獲取策略信息"""
        return {
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'parameters': {
                'min_dividend_yield': '4.0%',
                'max_pe_ratio': '30',
                'max_pb_ratio': '3.0',
                'min_total_score': '70'
            },
            'data_source': 'FinLab原始PKL數據',
            'data_path': self.pe_pkl_path,
            'data_loaded': self.pe_data_cache is not None
        }
