#!/usr/bin/env python3
"""
測試盤中數據獲取優化
驗證請求頻率控制和錯誤處理改進
"""

import time
import logging
from typing import List, Dict, Any

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_rate_limit_optimization():
    """測試請求頻率限制優化"""
    print("🚀 測試盤中數據獲取優化")
    print("=" * 80)
    
    try:
        from optimized_intraday_manager import optimized_intraday_manager, get_stock_intraday_data
        
        print("✅ 優化的盤中數據管理器已載入")
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454', '4979', '6290']
        
        print(f"\n📊 測試股票: {', '.join(test_stocks)}")
        print("-" * 40)
        
        # 測試單個股票獲取
        print("\n🔍 測試單個股票數據獲取:")
        for stock_id in test_stocks[:3]:  # 只測試前3支
            print(f"\n📈 測試 {stock_id}:")
            
            start_time = time.time()
            data = get_stock_intraday_data(stock_id)
            end_time = time.time()
            
            if data:
                print(f"  ✅ 成功獲取數據")
                print(f"  📊 價格: {data.get('price', 'N/A')}")
                print(f"  📊 成交量: {data.get('volume', 'N/A'):,}")
                print(f"  📊 數據源: {data.get('source', 'N/A')}")
                print(f"  ⏱️ 耗時: {end_time - start_time:.2f}秒")
            else:
                print(f"  ❌ 獲取失敗")
                print(f"  ⏱️ 耗時: {end_time - start_time:.2f}秒")
        
        # 測試批量獲取
        print(f"\n🔍 測試批量數據獲取:")
        start_time = time.time()
        batch_data = optimized_intraday_manager.get_batch_data(test_stocks)
        end_time = time.time()
        
        print(f"  📊 成功獲取: {len(batch_data)}/{len(test_stocks)} 支股票")
        print(f"  ⏱️ 總耗時: {end_time - start_time:.2f}秒")
        print(f"  ⏱️ 平均耗時: {(end_time - start_time)/len(test_stocks):.2f}秒/股票")
        
        # 顯示統計信息
        print(f"\n📊 管理器統計信息:")
        stats = optimized_intraday_manager.get_manager_stats()
        
        print(f"  🔧 請求延遲設置:")
        for source, delay in stats['delays'].items():
            print(f"    {source}: {delay:.2f}秒")
        
        print(f"  📈 請求次數:")
        for source, count in stats['request_counts'].items():
            print(f"    {source}: {count}次")
        
        print(f"  ❌ 錯誤次數:")
        for source, count in stats['error_counts'].items():
            print(f"    {source}: {count}次")
        
        print(f"  🚫 失敗股票數:")
        for source, count in stats['failed_stocks_count'].items():
            print(f"    {source}: {count}支")
        
        print(f"  💾 緩存大小: {stats['cache_size']}項")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """測試錯誤處理改進"""
    print(f"\n🔧 測試錯誤處理改進")
    print("-" * 40)
    
    improvements = [
        {
            "問題": "Too Many Requests 錯誤",
            "原因": "請求頻率過高導致API限制",
            "解決方案": [
                "動態調整請求間隔（0.3s → 1.5s → 3.0s）",
                "智能識別錯誤類型",
                "自動標記失敗股票避免重複請求",
                "添加User-Agent避免被識別為爬蟲"
            ]
        },
        {
            "問題": "could not convert string to float: '-'",
            "原因": "證交所API返回'-'字符無法轉換為數字",
            "解決方案": [
                "安全解析數據，檢查'-'和空值",
                "使用try-catch包裝數據轉換",
                "提供詳細的錯誤信息",
                "自動切換到備用數據源"
            ]
        },
        {
            "問題": "possibly delisted 錯誤",
            "原因": "Yahoo Finance認為股票可能下市",
            "解決方案": [
                "智能錯誤過濾，忽略已知無害錯誤",
                "多數據源備援機制",
                "優化股票代碼格式識別",
                "緩存機制減少重複請求"
            ]
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{i}. 🎯 {improvement['問題']}")
        print(f"   原因: {improvement['原因']}")
        print(f"   解決方案:")
        for j, solution in enumerate(improvement['解決方案'], 1):
            print(f"     {j}. {solution}")

def test_optimization_features():
    """測試優化功能特色"""
    print(f"\n💡 優化功能特色")
    print("-" * 40)
    
    features = [
        {
            "功能": "🎯 統一請求頻率控制",
            "描述": "所有數據源使用統一的頻率限制管理器",
            "優勢": ["避免請求衝突", "動態調整延遲", "智能錯誤恢復"]
        },
        {
            "功能": "🔄 多數據源備援",
            "描述": "TWSE → Smart Yahoo → twstock → 備用數據",
            "優勢": ["提高成功率", "降低依賴風險", "保證數據可用性"]
        },
        {
            "功能": "💾 智能緩存機制",
            "描述": "1分鐘緩存，避免重複請求",
            "優勢": ["減少API調用", "提升響應速度", "降低錯誤率"]
        },
        {
            "功能": "📊 詳細統計監控",
            "描述": "請求次數、錯誤統計、延遲調整記錄",
            "優勢": ["便於問題診斷", "性能監控", "優化決策支持"]
        },
        {
            "功能": "🛡️ 智能錯誤處理",
            "描述": "分類錯誤類型，採取不同處理策略",
            "優勢": ["精準錯誤處理", "避免誤判", "提高穩定性"]
        }
    ]
    
    for feature in features:
        print(f"\n{feature['功能']}")
        print(f"  描述: {feature['描述']}")
        print(f"  優勢:")
        for advantage in feature['優勢']:
            print(f"    • {advantage}")

def test_usage_recommendations():
    """測試使用建議"""
    print(f"\n📋 使用建議")
    print("-" * 40)
    
    recommendations = [
        {
            "場景": "🕘 開盤時段 (09:00-09:30)",
            "建議": [
                "使用較長的請求間隔（1-2秒）",
                "優先使用TWSE官方API",
                "避免大量並發請求",
                "監控錯誤率，及時調整策略"
            ]
        },
        {
            "場景": "📈 盤中交易時段 (09:30-13:30)",
            "建議": [
                "使用標準請求間隔（0.5秒）",
                "啟用緩存機制減少重複請求",
                "使用批量獲取提高效率",
                "定期檢查統計信息"
            ]
        },
        {
            "場景": "🔍 大量股票監控",
            "建議": [
                "分批處理，避免一次性請求過多",
                "使用優化的批量獲取接口",
                "定期清理失敗股票列表",
                "監控各數據源的成功率"
            ]
        },
        {
            "場景": "❌ 遇到頻率限制錯誤",
            "建議": [
                "檢查統計信息了解錯誤分布",
                "手動重置延遲設置",
                "清除失敗股票列表",
                "考慮減少監控股票數量"
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['場景']}")
        for suggestion in rec['建議']:
            print(f"  • {suggestion}")

def main():
    """主函數"""
    print("🚀 盤中數據獲取優化測試")
    print("=" * 80)
    
    try:
        # 測試優化效果
        success = test_rate_limit_optimization()
        
        # 測試錯誤處理
        test_error_handling()
        
        # 測試功能特色
        test_optimization_features()
        
        # 測試使用建議
        test_usage_recommendations()
        
        print(f"\n" + "=" * 80)
        print(f"🎯 優化總結")
        print(f"=" * 80)
        
        if success:
            print(f"✅ 盤中數據獲取優化測試成功")
            
            summary = [
                "🎯 統一的請求頻率控制管理",
                "🔄 多數據源智能備援機制", 
                "💾 高效的緩存系統",
                "🛡️ 智能錯誤分類和處理",
                "📊 詳細的統計監控功能",
                "⚡ 批量數據獲取優化",
                "🔧 動態延遲調整機制"
            ]
            
            print(f"\n💡 主要改進:")
            for improvement in summary:
                print(f"  {improvement}")
            
            print(f"\n🚀 現在您可以:")
            next_steps = [
                "使用優化的盤中監控功能",
                "享受更穩定的數據獲取體驗", 
                "通過統計信息監控系統健康狀態",
                "在遇到問題時快速診斷和恢復",
                "批量監控更多股票而不擔心頻率限制"
            ]
            
            for i, step in enumerate(next_steps, 1):
                print(f"  {i}. {step}")
        else:
            print(f"❌ 測試失敗，請檢查依賴模組")
        
        print(f"\n💡 如果仍遇到頻率限制問題，請:")
        troubleshooting = [
            "檢查網路連接狀態",
            "減少同時監控的股票數量",
            "增加請求間隔時間",
            "使用統計信息診斷問題",
            "考慮在非交易時間進行測試"
        ]
        
        for i, tip in enumerate(troubleshooting, 1):
            print(f"  {i}. {tip}")
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
