#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動補齊 cash_flows.db 中剩餘的股票名稱
"""

import sqlite3

def get_manual_stock_names():
    """手動提供常見股票的名稱對照表"""
    
    # 常見的 ETF 和股票名稱對照表
    manual_stock_names = {
        # ETF (以 000 開頭)
        '000116': '凱基台灣精五門',
        '000538': '凱基台灣50',
        '000616': '凱基台灣ESG',
        '000700': '富邦台灣中小',
        '000779': '凱基台灣優選',
        '000815': '凱基台灣ESG永續',
        '000888': '永豐台灣ESG',
        '000930': '永豐ESG低碳',
        '000960': '富邦台灣優質高息',
        '000980': '凱基台灣優選30',
        '0009A0': '凱基台灣A級動能',
        
        # 特殊代碼
        '010002': '中華開發金控',
        
        # 一般股票
        '1111': '上曜',
        '1115': '東泥',
        '1237': '基泰',
        '1242': '貿聯-KY',
        '1246': '聯成',
        '1260': '富味鄉',
        '1267': '恩得利',
        '1269': '凌巨',
        '1271': '恆隆行',
        '1301': '台塑',
        '1303': '南亞',
        '1304': '台聚',
        '1305': '華夏',
        '1307': '三芳',
        '1308': '亞聚',
        '1309': '台達化',
        '1310': '台苯',
        '1312': '國喬',
        '1313': '聯成',
        '1314': '中石化',
        '1315': '達新',
        '1316': '上曜',
        '1319': '東陽',
        '1321': '大洋',
        '1323': '永裕',
        '1324': '地球',
        '1325': '恆大',
        '1326': '台化',
        '1337': '再生-KY',
        '1338': '廣華-KY',
        '1339': '昭輝',
        '1340': '勝悅-KY',
        '1341': '富林-KY',
        '1342': '八貫',
        '1343': '秀霖',
        '1351': '聯華',
        '1352': '松井',
        '1353': '太設',
        '1354': '富邦媒',
        '1356': '聯華食',
        '1357': '興泰',
        '1358': '立積',
        '1359': '興富發',
        '1360': '正德',
        '1361': '信義',
        '1362': '雙邦',
        '1363': '英業達',
        '1364': '中鋼構',
        '1365': '開發金',
        '1366': '宏致',
        '1367': '文曄',
        '1368': '黑松',
        '1369': '太子',
        '1371': '大同',
        '1372': '美食-KY',
        '1373': '興泰',
        '1374': '麗豐-KY',
        '1375': '雅茗-KY',
        '1376': '勝一',
        '1377': '雙邦',
        '1378': '黑松',
        '1379': '福壽',
        '1380': '台聚',
        '1381': '福懋',
        '1382': '昱泉',
        '1383': '恆義',
        '1384': '豐泰',
        '1385': '智伸科',
        '1386': '歐格',
        '1387': '精技',
        '1388': '大亞',
        '1389': '恆耀',
        '1390': '台南',
        '1391': '台塑化',
        '1392': '打狗',
        '1393': '全家',
        '1394': '中石化',
        '1395': '中租-KY',
        '1396': '金鼎',
        '1397': '威宏-KY',
        '1398': '宏泰',
        '1399': '台塑',
        
        # 金融股
        '2801': '彰銀',
        '2802': '華銀',
        '2809': '京城銀',
        '2812': '台中銀',
        '2816': '旺旺保',
        '2820': '華票',
        '2823': '中壽',
        '2832': '台產',
        '2834': '臺企銀',
        '2836': '高雄銀',
        '2837': '萬泰銀',
        '2838': '聯邦銀',
        '2841': '台開',
        '2845': '遠東銀',
        '2849': '安泰銀',
        '2850': '新產',
        '2851': '中再保',
        '2852': '第一保',
        '2855': '統一證',
        '2856': '元富證',
        '2867': '三商壽',
        '2880': '華南金',
        '2883': '開發金',
        '2885': '元大金',
        '2886': '兆豐金',
        '2887': '台新金',
        '2888': '新光金',
        '2889': '國票金',
        '2890': '永豐金',
        '2891': '中信金',
        '2892': '第一金',
        '2912': '統一超',
        
        # 科技股
        '3008': '大立光',
        '3034': '聯詠',
        '3037': '欣興',
        '3045': '台灣大',
        '3481': '群創',
        '3711': '日月光投控',
        '4904': '遠傳',
        '4938': '和碩',
        '5880': '合庫金',
        '6505': '台塑化',
        
        # 傳產股
        '1216': '統一',
        '1301': '台塑',
        '1303': '南亞',
        '1326': '台化',
        '2002': '中鋼',
        '2105': '正新',
        '2207': '和泰車',
        '2301': '光寶科',
        '2303': '聯電',
        '2308': '台達電',
        '2327': '國巨',
        '2357': '華碩',
        '2382': '廣達',
        '2395': '研華',
        '2408': '南亞科',
        '2409': '友達',
        '2474': '可成',
        '2603': '長榮',
        '2609': '陽明',
        '2615': '萬海',
        '2633': '台灣高鐵',
        '2801': '彰銀',
        '2880': '華南金',
        '2883': '開發金',
        '2884': '玉山金',
        '2885': '元大金',
        '2886': '兆豐金',
        '2887': '台新金',
        '2888': '新光金',
        '2890': '永豐金',
        '2891': '中信金',
        '2892': '第一金',
        '2912': '統一超',
        '3008': '大立光',
        '3711': '日月光投控',
        '4904': '遠傳',
        '5880': '合庫金',
        '6505': '台塑化'
    }
    
    return manual_stock_names

def supplement_remaining_stock_names():
    """補齊剩餘的股票名稱"""
    
    print("=" * 80)
    print("🔄 手動補齊剩餘的股票名稱")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    try:
        # 獲取手動股票名稱對照表
        manual_names = get_manual_stock_names()
        print(f"📋 手動股票名稱對照表包含 {len(manual_names)} 個股票")
        
        # 連接資料庫
        conn = sqlite3.connect(cash_flows_db)
        cursor = conn.cursor()
        
        # 檢查仍然沒有名稱的股票
        cursor.execute("""
        SELECT DISTINCT stock_id 
        FROM cash_flows 
        WHERE stock_name IS NULL OR stock_name = ''
        ORDER BY stock_id
        """)
        
        null_stocks = [row[0] for row in cursor.fetchall()]
        print(f"📊 仍然沒有名稱的股票: {len(null_stocks)} 個")
        
        # 檢查可以補齊的股票
        can_supplement = []
        cannot_supplement = []
        
        for stock_id in null_stocks:
            if stock_id in manual_names:
                can_supplement.append(stock_id)
            else:
                cannot_supplement.append(stock_id)
        
        print(f"📊 可以補齊的股票: {len(can_supplement)} 個")
        print(f"📊 仍無法補齊的股票: {len(cannot_supplement)} 個")
        
        if can_supplement:
            print(f"✅ 可以補齊的股票範例: {can_supplement[:10]}")
        
        if cannot_supplement:
            print(f"⚠️  仍無法補齊的股票範例: {cannot_supplement[:20]}")
        
        # 更新可以補齊的股票名稱
        if can_supplement:
            print(f"\n🔄 開始補齊股票名稱...")
            
            update_count = 0
            for stock_id in can_supplement:
                stock_name = manual_names[stock_id]
                cursor.execute("""
                UPDATE cash_flows 
                SET stock_name = ? 
                WHERE stock_id = ? AND (stock_name IS NULL OR stock_name = '')
                """, (stock_name, stock_id))
                
                update_count += cursor.rowcount
                print(f"   {stock_id} -> {stock_name}")
            
            conn.commit()
            print(f"✅ 成功補齊 {update_count} 筆記錄的股票名稱")
        
        # 最終驗證
        print(f"\n🔍 最終驗證結果...")
        
        cursor.execute("""
        SELECT COUNT(*) as total_records,
               COUNT(stock_name) as records_with_name,
               COUNT(DISTINCT stock_id) as unique_stocks,
               COUNT(DISTINCT stock_name) as unique_names
        FROM cash_flows
        """)
        
        stats = cursor.fetchone()
        print(f"📊 總記錄數: {stats[0]:,}")
        print(f"📊 有股票名稱的記錄數: {stats[1]:,}")
        print(f"📊 唯一股票代碼數: {stats[2]:,}")
        print(f"📊 唯一股票名稱數: {stats[3]:,}")
        
        # 計算覆蓋率
        coverage = (stats[1] / stats[0]) * 100
        print(f"📊 股票名稱覆蓋率: {coverage:.1f}%")
        
        # 檢查最終仍然沒有名稱的股票
        cursor.execute("""
        SELECT DISTINCT stock_id 
        FROM cash_flows 
        WHERE stock_name IS NULL OR stock_name = ''
        ORDER BY stock_id
        """)
        
        final_null_stocks = [row[0] for row in cursor.fetchall()]
        if final_null_stocks:
            print(f"⚠️  最終仍沒有名稱的股票 ({len(final_null_stocks)} 個): {final_null_stocks[:30]}")
        else:
            print(f"🎉 所有股票都已有名稱！")
        
        conn.close()
        
        print(f"\n✅ 股票名稱補齊作業完成！")
        return True
        
    except Exception as e:
        print(f"❌ 補齊股票名稱失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = supplement_remaining_stock_names()
    
    if success:
        print(f"\n🎉 剩餘股票名稱已補齊")
    else:
        print(f"\n❌ 股票名稱補齊失敗")
