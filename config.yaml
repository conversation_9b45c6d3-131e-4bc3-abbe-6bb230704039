# Finlab財務數據管理系統配置文件

# 數據源設置
data_sources:
  finlab_tables_path: "D:/□Finlab學習/用 Python 理財：打造自己的 AI 股票理專_原始檔案/finlab_ml_course/finlab_ml_course/history/tables"
  local_data_path: "D:/Finlab/history/tables"
  database_path: "D:/Finlab/history/tables/finlab_financial_data.db"

# API設置
api:
  base_url: "https://api.finlab.tw/v1"
  timeout: 30
  retry_count: 3
  rate_limit: 100  # 每分鐘請求數限制

# 數據表格映射
table_mappings:
  balance_sheet:
    name: "資產負債表"
    description: "公司資產、負債和股東權益數據"
    update_frequency: "quarterly"  # 季度更新
    
  income_statement:
    name: "綜合損益表"
    description: "公司營收、成本、費用和獲利數據"
    update_frequency: "quarterly"
    
  income_statement_cumulative:
    name: "累計損益表"
    description: "累計至當季的損益數據"
    update_frequency: "quarterly"
    
  cash_flows:
    name: "現金流量表"
    description: "營業、投資、融資活動現金流"
    update_frequency: "quarterly"
    
  monthly_revenue:
    name: "月營收報告"
    description: "每月營業收入數據"
    update_frequency: "monthly"
    
  pe_ratio:
    name: "本益比數據"
    description: "股價本益比歷史數據"
    update_frequency: "daily"
    
  dividend:
    name: "股利發放"
    description: "現金股利和股票股利數據"
    update_frequency: "yearly"
    
  margin_trading:
    name: "融資融券"
    description: "融資融券餘額數據"
    update_frequency: "daily"
    
  benchmark:
    name: "基準指數"
    description: "大盤指數和基準數據"
    update_frequency: "daily"
    
  foreign_holding:
    name: "外資持股比例"
    description: "外資持股比例數據"
    update_frequency: "daily"

# 爬取設置
crawler:
  enabled: true
  schedule:
    daily_update_time: "08:00"
    weekend_update: false
  data_retention_days: 365  # 數據保留天數
  
# GUI設置
gui:
  window_size: "1200x800"
  theme: "clam"
  default_chart_type: "line"
  max_display_rows: 1000
  
# 日誌設置
logging:
  level: "INFO"
  log_dir: "logs"
  max_log_files: 30
  log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
# 數據庫設置
database:
  backup_enabled: true
  backup_dir: "backups"
  backup_frequency: "weekly"
  vacuum_frequency: "monthly"
  
# 報告設置
reports:
  output_dir: "reports"
  formats: ["markdown", "html", "pdf"]
  include_charts: true
  
# 安全設置
security:
  api_key_file: ".env"
  encrypt_database: false
  backup_encryption: false
