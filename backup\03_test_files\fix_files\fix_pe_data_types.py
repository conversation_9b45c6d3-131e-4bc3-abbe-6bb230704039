#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復PE數據的數據類型問題
"""

import pandas as pd
import numpy as np
from datetime import datetime

def fix_pe_data_types():
    """修復PE數據的數據類型問題"""
    
    print("🔧 修復PE數據類型問題")
    print("=" * 50)
    
    pe_path = 'D:/Finlab/pe.pkl'
    backup_path = f'D:/Finlab/pe_before_type_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
    
    # 1. 讀取現有數據
    print("📖 讀取現有PE數據...")
    try:
        pe_data = pd.read_pickle(pe_path)
        print(f"   📊 數據筆數: {len(pe_data):,}")
        print(f"   📋 原始欄位類型:")
        for col in pe_data.columns:
            print(f"      {col}: {pe_data[col].dtype}")
    except Exception as e:
        print(f"❌ 讀取PE數據失敗: {e}")
        return False
    
    # 2. 備份原始數據
    print(f"\n📦 備份原始數據到: {backup_path}")
    pe_data.to_pickle(backup_path)
    
    # 3. 修復數據類型
    print("\n🔧 修復數據類型...")
    
    # 複製數據以避免修改原始數據
    fixed_data = pe_data.copy()
    
    # 修復數值欄位
    numeric_columns = ['殖利率(%)', '本益比', '股價淨值比', '股利年度']
    
    for col in numeric_columns:
        if col in fixed_data.columns:
            print(f"   🔧 修復 {col}...")
            try:
                # 轉換為數值類型，無法轉換的設為NaN
                original_count = len(fixed_data[col].dropna())
                fixed_data[col] = pd.to_numeric(fixed_data[col], errors='coerce')
                valid_count = len(fixed_data[col].dropna())
                
                print(f"      ✅ {col}: {original_count} -> {valid_count} 有效值")
                
                # 處理異常值
                if col == '殖利率(%)':
                    # 殖利率應該在合理範圍內 (0-50%)
                    mask = (fixed_data[col] < 0) | (fixed_data[col] > 50)
                    outlier_count = mask.sum()
                    if outlier_count > 0:
                        fixed_data.loc[mask, col] = np.nan
                        print(f"      🧹 移除 {outlier_count} 個異常殖利率值")
                
                elif col == '本益比':
                    # 本益比應該在合理範圍內 (0-1000)
                    mask = (fixed_data[col] <= 0) | (fixed_data[col] > 1000)
                    outlier_count = mask.sum()
                    if outlier_count > 0:
                        fixed_data.loc[mask, col] = np.nan
                        print(f"      🧹 移除 {outlier_count} 個異常本益比值")
                
                elif col == '股價淨值比':
                    # 股價淨值比應該在合理範圍內 (0-50)
                    mask = (fixed_data[col] <= 0) | (fixed_data[col] > 50)
                    outlier_count = mask.sum()
                    if outlier_count > 0:
                        fixed_data.loc[mask, col] = np.nan
                        print(f"      🧹 移除 {outlier_count} 個異常股價淨值比值")
                
            except Exception as e:
                print(f"      ❌ 修復 {col} 失敗: {e}")
    
    # 確保股票代號是字符串
    if '股票代號' in fixed_data.columns:
        print("   🔧 確保股票代號為字符串格式...")
        fixed_data['股票代號'] = fixed_data['股票代號'].astype(str)
        # 移除可能的空格
        fixed_data['股票代號'] = fixed_data['股票代號'].str.strip()
    
    # 確保日期格式正確
    if 'date' in fixed_data.columns:
        print("   🔧 確保日期格式正確...")
        fixed_data['date'] = pd.to_datetime(fixed_data['date'])
    
    # 4. 顯示修復後的統計信息
    print("\n📊 修復後數據統計:")
    print(f"   📈 總數據筆數: {len(fixed_data):,}")
    print(f"   📈 涵蓋股票數: {fixed_data['股票代號'].nunique():,} 支")
    
    for col in numeric_columns:
        if col in fixed_data.columns:
            valid_data = fixed_data[col].dropna()
            if len(valid_data) > 0:
                print(f"   📊 {col}: {len(valid_data):,} 有效值, 範圍 {valid_data.min():.2f} ~ {valid_data.max():.2f}")
    
    # 5. 保存修復後的數據
    print(f"\n💾 保存修復後的數據...")
    try:
        fixed_data.to_pickle(pe_path)
        print(f"   ✅ 數據已保存到: {pe_path}")
        
        # 驗證保存的數據
        verification_data = pd.read_pickle(pe_path)
        print(f"   ✅ 驗證成功，數據筆數: {len(verification_data):,}")
        
    except Exception as e:
        print(f"❌ 保存數據失敗: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ PE數據類型修復完成！")
    
    return True

def test_fixed_data():
    """測試修復後的數據"""
    
    print("\n🧪 測試修復後的數據")
    print("=" * 30)
    
    try:
        pe_data = pd.read_pickle('D:/Finlab/pe.pkl')
        
        print("📋 修復後欄位類型:")
        for col in pe_data.columns:
            print(f"   {col}: {pe_data[col].dtype}")
        
        # 測試數值運算
        print("\n🧮 測試數值運算:")
        
        if '殖利率(%)' in pe_data.columns:
            high_dividend = pe_data[pe_data['殖利率(%)'] >= 6.0]
            print(f"   ✅ 高殖利率股票 (≥6%): {len(high_dividend):,} 筆")
        
        if '本益比' in pe_data.columns:
            low_pe = pe_data[(pe_data['本益比'] > 0) & (pe_data['本益比'] <= 15)]
            print(f"   ✅ 低本益比股票 (≤15): {len(low_pe):,} 筆")
        
        # 顯示樣本數據
        print("\n📄 樣本數據:")
        sample = pe_data.head(3)
        for idx, row in sample.iterrows():
            stock_id = row.get('股票代號', 'N/A')
            dividend = row.get('殖利率(%)', 'N/A')
            pe_ratio = row.get('本益比', 'N/A')
            print(f"   {stock_id}: 殖利率{dividend}%, 本益比{pe_ratio}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    # 修復數據類型
    success = fix_pe_data_types()
    
    if success:
        # 測試修復結果
        test_fixed_data()
        
        print("\n🎉 PE數據類型修復完成！現在可以正常使用高殖利率烏龜策略了。")
    else:
        print("\n❌ PE數據類型修復失敗，請檢查錯誤信息。")
