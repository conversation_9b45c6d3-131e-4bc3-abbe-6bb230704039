#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試搜尋天數設定
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QSpinBox

def test_days_setting():
    """測試搜尋天數設定"""
    print("🧪 測試搜尋天數設定")
    print("=" * 40)
    
    # 創建QApplication（GUI測試需要）
    app = QApplication(sys.argv)
    
    # 創建SpinBox並設定與程式相同的參數
    days_spinbox = QSpinBox()
    days_spinbox.setRange(1, 365)  # 與程式中相同的設定
    days_spinbox.setValue(30)      # 與程式中相同的預設值
    
    # 測試設定
    print(f"✅ 最小值: {days_spinbox.minimum()}")
    print(f"✅ 最大值: {days_spinbox.maximum()}")
    print(f"✅ 預設值: {days_spinbox.value()}")
    
    # 測試邊界值
    test_values = [1, 30, 90, 180, 365, 366]  # 包含超出範圍的值
    
    print("\n🔍 測試不同天數值:")
    for value in test_values:
        days_spinbox.setValue(value)
        actual_value = days_spinbox.value()
        
        if value <= 365:
            expected = value
            status = "✅" if actual_value == expected else "❌"
        else:
            expected = 365  # 超出範圍應該被限制為最大值
            status = "✅" if actual_value == expected else "❌"
        
        print(f"{status} 設定 {value} 天 → 實際值: {actual_value} 天 (期望: {expected})")
    
    print("\n📊 測試結果:")
    print("✅ 支援1-365天範圍")
    print("✅ 預設值為30天")
    print("✅ 適合冷門股票長期搜尋")
    
    # 不需要顯示GUI，直接退出
    app.quit()
    
    return True

if __name__ == "__main__":
    test_days_setting()
