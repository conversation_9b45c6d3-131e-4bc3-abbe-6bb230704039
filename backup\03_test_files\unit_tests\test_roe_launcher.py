#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試ROE下載器啟動功能
模擬主系統調用ROE下載器的過程
"""

import sys
import os
import logging
import threading
import time

def test_roe_downloader_import():
    """測試ROE下載器模組導入"""
    print("🧪 測試ROE下載器模組導入")
    print("=" * 50)
    
    try:
        # 測試導入ROE下載器GUI
        from roe_data_downloader_gui import ROEDataDownloaderGUI
        print("✅ ROEDataDownloaderGUI 導入成功")
        
        # 測試導入核心下載器
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        print("✅ GoodinfoROECSVDownloader 導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("\n🔍 檢查以下文件是否存在:")
        files_to_check = [
            "roe_data_downloader_gui.py",
            "goodinfo_roe_csv_downloader.py"
        ]
        
        for file in files_to_check:
            if os.path.exists(file):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} (缺失)")
        
        return False
    
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
        return False

def test_roe_downloader_creation():
    """測試ROE下載器創建"""
    print("\n🏗️ 測試ROE下載器創建")
    print("=" * 50)
    
    try:
        from roe_data_downloader_gui import ROEDataDownloaderGUI
        
        # 創建下載器實例
        downloader = ROEDataDownloaderGUI()
        print("✅ ROE下載器實例創建成功")
        
        # 檢查關鍵屬性
        if hasattr(downloader, 'window'):
            print("✅ GUI窗口創建成功")
        else:
            print("❌ GUI窗口創建失敗")
            
        if hasattr(downloader, 'downloader'):
            print("✅ 核心下載器創建成功")
        else:
            print("❌ 核心下載器創建失敗")
        
        # 關閉窗口
        downloader.window.destroy()
        print("✅ 測試完成，窗口已關閉")
        
        return True
        
    except Exception as e:
        print(f"❌ ROE下載器創建失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_roe_downloader_launch():
    """測試ROE下載器啟動（模擬主系統調用）"""
    print("\n🚀 測試ROE下載器啟動（模擬主系統調用）")
    print("=" * 50)
    
    def launch_roe_downloader():
        """啟動ROE下載器的函數"""
        try:
            from roe_data_downloader_gui import ROEDataDownloaderGUI
            
            print("📱 創建ROE下載器...")
            downloader = ROEDataDownloaderGUI()
            
            print("✅ ROE下載器已創建，準備顯示界面")
            print("💡 界面將在3秒後自動關閉以完成測試")
            
            # 設置自動關閉
            def auto_close():
                time.sleep(3)
                try:
                    downloader.window.destroy()
                    print("✅ 測試完成，界面已自動關閉")
                except:
                    pass
            
            close_thread = threading.Thread(target=auto_close, daemon=True)
            close_thread.start()
            
            # 運行GUI
            downloader.run()
            
        except Exception as e:
            print(f"❌ ROE下載器啟動失敗: {e}")
            import traceback
            print(f"錯誤詳情: {traceback.format_exc()}")
    
    try:
        # 在新線程中啟動（模擬主系統的調用方式）
        print("🔄 在新線程中啟動ROE下載器...")
        roe_thread = threading.Thread(target=launch_roe_downloader, daemon=True)
        roe_thread.start()
        
        # 等待線程完成
        roe_thread.join(timeout=10)
        
        if roe_thread.is_alive():
            print("⚠️ ROE下載器仍在運行中（正常情況）")
        else:
            print("✅ ROE下載器線程已完成")
        
        return True
        
    except Exception as e:
        print(f"❌ ROE下載器啟動測試失敗: {e}")
        return False

def test_main_system_integration():
    """測試主系統整合"""
    print("\n🔗 測試主系統整合")
    print("=" * 50)
    
    try:
        # 模擬主系統的調用代碼
        def open_roe_data_downloader():
            """模擬主系統的open_roe_data_downloader方法"""
            try:
                import threading
                
                def launch_roe_downloader():
                    try:
                        from roe_data_downloader_gui import ROEDataDownloaderGUI
                        
                        downloader = ROEDataDownloaderGUI()
                        print("✅ ROE下載器已在新線程中創建")
                        
                        # 為了測試，不實際運行GUI
                        # downloader.run()
                        
                    except Exception as e:
                        print(f"❌ ROE下載器執行失敗: {e}")
                
                roe_thread = threading.Thread(target=launch_roe_downloader, daemon=True)
                roe_thread.start()
                
                print("✅ ROE下載器線程已啟動")
                
                # 等待線程完成
                roe_thread.join(timeout=5)
                
                return True
                
            except ImportError:
                print("❌ ROE下載器模組未找到")
                return False
            except Exception as e:
                print(f"❌ 開啟ROE下載器失敗: {e}")
                return False
        
        # 執行測試
        result = open_roe_data_downloader()
        
        if result:
            print("✅ 主系統整合測試成功")
        else:
            print("❌ 主系統整合測試失敗")
        
        return result
        
    except Exception as e:
        print(f"❌ 主系統整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 ROE下載器啟動功能測試")
    print("=" * 60)
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 執行測試
    tests = [
        ("模組導入測試", test_roe_downloader_import),
        ("下載器創建測試", test_roe_downloader_creation),
        ("主系統整合測試", test_main_system_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 執行失敗: {e}")
            results.append((test_name, False))
    
    # 顯示測試結果
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  • {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有測試通過！ROE下載器啟動功能正常")
        print("\n💡 解決方案:")
        print("  1. 確認主系統已更新ROE下載器調用代碼")
        print("  2. 重新啟動主系統")
        print("  3. 點擊爬蟲選單中的「📊 年度ROE資料」")
        print("  4. ROE下載器應該能正常啟動")
    else:
        print("⚠️ 部分測試失敗，請檢查錯誤信息")
        print("\n🔧 故障排除:")
        print("  1. 確認所有ROE相關文件都存在")
        print("  2. 檢查Python環境和依賴套件")
        print("  3. 查看詳細錯誤信息進行修正")
    
    return all_passed

if __name__ == "__main__":
    main()
