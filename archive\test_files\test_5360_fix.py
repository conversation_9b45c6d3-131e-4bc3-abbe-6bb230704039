#!/usr/bin/env python3
"""
測試5360股票數據獲取修復
"""

import logging
import sys

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_5360_data_fetch():
    """測試5360數據獲取"""
    print("🧪 測試5360股票數據獲取修復")
    print("=" * 50)
    
    try:
        from intraday_data_fetcher import get_intraday_data
        
        print("✅ 成功導入盤中數據獲取模組")
        
        # 測試5360數據獲取
        print("\n📊 測試5360數據獲取:")
        df = get_intraday_data('5360', '5m')
        
        if not df.empty:
            print(f"✅ 成功獲取5360數據: {len(df)}筆")
            print("\n📋 數據預覽:")
            print(df.head())
            
            # 檢查數據完整性
            if 'close' in df.columns and df['close'].iloc[-1] > 0:
                print(f"\n💰 最新價格: {df['close'].iloc[-1]}")
                print("✅ 數據有效")
            else:
                print("\n⚠️ 數據可能無效（價格為0）")
        else:
            print("❌ 5360數據獲取失敗")
            
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_network_manager():
    """測試網路管理器"""
    print("\n🌐 測試增強網路管理器:")
    print("-" * 30)
    
    try:
        from enhanced_network_manager import make_enhanced_request, get_network_stats
        
        print("✅ 增強網路管理器可用")
        
        # 測試基本請求
        response = make_enhanced_request('https://httpbin.org/ip')
        if response:
            print("✅ 網路請求測試成功")
            
            # 顯示統計
            stats = get_network_stats()
            print(f"📊 成功率: {stats['success_rate']:.1f}%")
        else:
            print("❌ 網路請求測試失敗")
            
    except ImportError:
        print("⚠️ 增強網路管理器不可用")
    except Exception as e:
        print(f"❌ 網路管理器測試失敗: {e}")

def test_otc_classification():
    """測試櫃買股票分類"""
    print("\n🏪 測試櫃買股票分類:")
    print("-" * 30)
    
    def is_otc_stock(stock_code):
        """判斷是否為櫃買中心股票"""
        try:
            if not stock_code or len(stock_code) < 4:
                return False
            first_digit = stock_code[0]
            return first_digit in ['5', '6', '7', '8']
        except Exception:
            return False
    
    test_stocks = [
        ('2330', '台積電', False),
        ('2317', '鴻海', False),
        ('5360', '欣興', True),
        ('6290', '良維', True),
        ('7533', '協易機', True),
        ('8299', '群聯', True),
    ]
    
    print("股票分類測試結果:")
    for code, name, expected in test_stocks:
        predicted = is_otc_stock(code)
        status = "✅" if predicted == expected else "❌"
        market = "櫃買" if predicted else "上市"
        print(f"  {status} {code} ({name}): {market}")

def test_api_integration():
    """測試API整合"""
    print("\n🔗 測試API整合:")
    print("-" * 30)
    
    try:
        # 測試證交所API
        print("🏢 測試證交所API:")
        from enhanced_network_manager import make_enhanced_request
        
        url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"
        params = {'ex_ch': 'tse_2330.tw', 'json': '1', 'delay': '0'}
        
        response = make_enhanced_request(url, params=params)
        if response and response.status_code == 200:
            data = response.json()
            if data.get('stat') == 'OK':
                print("  ✅ 證交所API連接正常")
            else:
                print("  ⚠️ 證交所API無數據")
        else:
            print("  ❌ 證交所API連接失敗")
            
    except Exception as e:
        print(f"  ❌ API測試失敗: {e}")

def main():
    """主函數"""
    print("🚀 5360股票數據獲取修復測試")
    print("=" * 50)
    
    # 測試櫃買股票分類
    test_otc_classification()
    
    # 測試網路管理器
    test_network_manager()
    
    # 測試API整合
    test_api_integration()
    
    # 測試5360數據獲取
    test_5360_data_fetch()
    
    print("\n" + "=" * 50)
    print("🎯 測試總結")
    print("=" * 50)
    
    print("✅ 主要改進:")
    improvements = [
        "🔍 智能股票分類（5360 → 櫃買股票）",
        "🌐 增強網路管理器（重試+緩存+優化）",
        "🏪 櫃買中心API支援",
        "🛡️ 安全數據解析（處理'-'字符）",
        "🔄 多重備援機制（TPEX → TWSE → 備用）"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print("\n💡 使用建議:")
    suggestions = [
        "重新啟動主程式以載入修復",
        "添加5360到監控列表測試",
        "觀察日誌中的詳細診斷信息",
        "如需更高穩定性可配置付費代理"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")

if __name__ == "__main__":
    main()
