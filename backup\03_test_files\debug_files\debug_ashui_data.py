#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試阿水一式策略數據顯示問題
"""
import sys
import os

def check_ashui_table_setup():
    """檢查阿水一式表格設置方法"""
    print("🔍 檢查阿水一式表格設置方法...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找表格設置方法
        setup_method_start = content.find('def setup_ashui_strategy_table(self, results, matching_stocks):')
        if setup_method_start == -1:
            print("❌ 未找到 setup_ashui_strategy_table 方法")
            return False
        
        # 提取方法內容
        method_end = content.find('\n    def ', setup_method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[setup_method_start:method_end]
        
        print("✅ 找到阿水一式表格設置方法")
        
        # 檢查關鍵修復點
        checks = [
            ("fetch_strategy_data 調用", "fetch_strategy_data(stock_id,"),
            ("日期參數修復", "current_date"),
            ("數據框檢查", "not df.empty and len(df) >= 20"),
            ("成交金額計算", "turnover = close_price * volume / 10000"),
            ("布林帶計算", "ma20 + (std20 * 2.1)"),
            ("成交量比例計算", "current_volume / volume_ma5"),
            ("壓縮天數計算", "計算連續收縮天數"),
            ("錯誤處理", "except Exception as e:")
        ]
        
        for check_name, check_pattern in checks:
            if check_pattern in method_content:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

def analyze_potential_issues():
    """分析潛在問題"""
    print("\n🔍 分析潛在問題...")
    
    print("📋 可能的問題原因:")
    print("1. 數據庫連接問題 - fetch_strategy_data 無法獲取數據")
    print("2. 日期參數問題 - 傳入的日期格式不正確")
    print("3. 數據格式問題 - DataFrame 結構不符合預期")
    print("4. 計算異常 - 技術指標計算過程中出錯")
    print("5. 表格更新問題 - 計算完成但未正確更新到表格")
    
    print("\n🔧 調試建議:")
    print("1. 檢查日誌文件中的錯誤信息")
    print("2. 驗證數據庫中是否有股票價格數據")
    print("3. 測試 fetch_strategy_data 方法是否正常工作")
    print("4. 確認 DataFrame 的欄位結構")
    print("5. 添加更多調試輸出")

def create_debug_version():
    """創建調試版本的建議"""
    print("\n💡 創建調試版本建議:")
    
    debug_code = '''
# 在 setup_ashui_strategy_table 方法中添加調試輸出
print(f"🔍 調試: 處理股票 {stock_id}")
print(f"🔍 調試: 收盤價 {close_price}")

try:
    df = self.fetch_strategy_data(stock_id, current_date)
    print(f"🔍 調試: 獲取到 {len(df)} 行數據")
    if not df.empty:
        print(f"🔍 調試: 數據欄位: {df.columns.tolist()}")
        print(f"🔍 調試: 最新數據: {df.iloc[-1].to_dict()}")
    
    # ... 計算邏輯 ...
    
    print(f"🔍 調試: 計算結果 - 成交金額:{turnover}, 布林帶寬:{bb_width}")
    
except Exception as e:
    print(f"🔍 調試: 計算失敗 {e}")
    import traceback
    traceback.print_exc()
'''
    
    print("建議在表格設置方法中添加以上調試代碼")

def suggest_immediate_fixes():
    """建議立即修復方案"""
    print("\n🚀 立即修復方案:")
    
    print("1. 簡化數據獲取:")
    print("   - 直接從 result 中獲取基本數據")
    print("   - 暫時使用模擬計算")
    print("   - 確保至少顯示成交金額")
    
    print("\n2. 添加備用邏輯:")
    print("   - 如果技術指標計算失敗，使用簡單計算")
    print("   - 提供默認值而不是 '--'")
    print("   - 確保表格至少有部分數據")
    
    print("\n3. 錯誤處理改進:")
    print("   - 捕獲具體的異常類型")
    print("   - 記錄詳細的錯誤信息")
    print("   - 提供用戶友好的錯誤提示")

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 阿水一式策略數據顯示問題調試")
    print("=" * 60)
    
    success = check_ashui_table_setup()
    
    analyze_potential_issues()
    create_debug_version()
    suggest_immediate_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 表格設置方法檢查通過")
        print("📝 建議：添加調試輸出來定位具體問題")
    else:
        print("❌ 表格設置方法有問題")
        print("📝 建議：檢查方法實現")
    
    print("\n🎯 下一步行動:")
    print("1. 執行阿水一式策略並檢查控制台輸出")
    print("2. 查看是否有錯誤日誌")
    print("3. 如果仍然顯示 '--'，添加調試代碼")
    print("4. 考慮使用簡化的計算邏輯")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
