#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試除權息下載功能
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入主程式的除權息下載功能
from O3mh_gui_v21_optimized import StockScreenerGUI

def debug_dividend_download():
    """調試除權息下載功能"""
    print("🔍 調試除權息下載功能")
    print("=" * 60)
    
    try:
        # 創建主程式實例（不顯示GUI）
        from PyQt6.QtWidgets import QApplication
        app = QApplication([])
        
        main_window = StockScreenerGUI()
        
        # 調試用戶股票清單獲取
        print("📊 調試用戶股票清單獲取...")
        user_stocks = main_window.get_user_stock_list()
        print(f"用戶股票清單: {len(user_stocks) if user_stocks else 0} 筆")
        if user_stocks:
            print(f"前5筆: {user_stocks[:5]}")
        
        # 調試完整台股清單生成
        print("\n📊 調試完整台股清單生成...")
        comprehensive_data = main_window.generate_comprehensive_sample_data(None)
        print(f"完整台股資料: {len(comprehensive_data)} 筆")
        if comprehensive_data:
            print(f"前3筆: {comprehensive_data[:3]}")
        
        # 調試API獲取
        print("\n📊 調試API獲取...")
        api_data = main_window.fetch_dividend_from_api()
        print(f"API資料: {len(api_data) if api_data else 0} 筆")
        
        # 調試網頁爬取
        print("\n📊 調試網頁爬取...")
        web_data = main_window.fetch_dividend_from_web()
        print(f"網頁資料: {len(web_data) if web_data else 0} 筆")
        
        # 調試完整流程
        print("\n📊 調試完整流程...")
        dividend_data = main_window.fetch_dividend_data()
        print(f"最終資料: {len(dividend_data)} 筆")
        
        return True
            
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_dividend_download()
