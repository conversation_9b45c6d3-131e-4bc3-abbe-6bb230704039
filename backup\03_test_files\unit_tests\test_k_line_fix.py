#!/usr/bin/env python3
"""
K線圖測試工具
測試修復後的K線圖顯示效果
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
import pyqtgraph as pg

# 導入修復版的CandlestickItem
try:
    from charts.candlestick import CandlestickItem
    print("✅ 成功導入修復版CandlestickItem")
except ImportError as e:
    print(f"❌ 導入CandlestickItem失敗: {e}")
    sys.exit(1)

def create_test_data(days=60):
    """創建測試數據"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # 生成交易日
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
    
    data_list = []
    base_price = 1000
    
    for i, date in enumerate(date_range):
        # 模擬價格波動
        price_change = np.random.normal(0, 20)
        base_price += price_change
        
        open_price = base_price + np.random.normal(0, 5)
        high_price = max(open_price, base_price) + abs(np.random.normal(0, 10))
        low_price = min(open_price, base_price) - abs(np.random.normal(0, 10))
        close_price = base_price + np.random.normal(0, 5)
        volume = np.random.randint(10000, 100000) * 1000
        
        # 模擬一些數據問題
        if i % 20 == 0:  # 每20天有一個潛在問題
            if np.random.random() < 0.3:  # 30%機率有問題
                if np.random.random() < 0.5:
                    # 模擬NaN值
                    open_price = np.nan
                else:
                    # 模擬邏輯錯誤
                    high_price = low_price - 10
        
        data_list.append({
            'date': date,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    return pd.DataFrame(data_list)

class KLineTestWindow(QMainWindow):
    """K線圖測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("K線圖修復測試")
        self.setGeometry(100, 100, 1200, 800)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建圖表視圖
        self.chart_view = pg.PlotWidget()
        self.chart_view.setBackground('k')
        self.chart_view.setTitle("K線圖修復測試", color='w', size='16pt')
        layout.addWidget(self.chart_view)
        
        # 載入測試數據
        self.load_test_data()
    
    def load_test_data(self):
        """載入測試數據"""
        print("📊 載入測試數據...")
        
        # 創建測試數據
        df = create_test_data(60)
        print(f"✅ 創建了 {len(df)} 天的測試數據")
        
        # 創建K線圖
        candlestick = CandlestickItem(df)
        self.chart_view.addItem(candlestick)
        
        # 獲取統計信息
        stats = candlestick.get_statistics()
        print(f"📊 K線圖統計:")
        print(f"   總數據量: {stats['total_data']}")
        print(f"   成功繪製: {stats['valid_candlesticks']}")
        print(f"   跳過數量: {stats['skipped_candlesticks']}")
        print(f"   成功率: {stats['success_rate']:.1f}%")
        
        # 更新標題
        title = f"K線圖修復測試 - 成功率: {stats['success_rate']:.1f}% ({stats['valid_candlesticks']}/{stats['total_data']})"
        self.chart_view.setTitle(title, color='w', size='14pt')

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 創建測試窗口
    window = KLineTestWindow()
    window.show()
    
    print("🎯 K線圖測試窗口已開啟")
    print("請檢查K線圖是否正常顯示，特別注意是否有缺失的日期")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
