#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import shutil
import time

def main():
    print("開始編譯修復版本...")
    
    # 清理環境
    if os.path.exists('build'):
        shutil.rmtree('build')
    time.sleep(2)
    
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'fixed_compile.spec']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            exe_path = 'dist/StockAnalyzer_Fixed.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"編譯成功！")
                print(f"檔案: {exe_path}")
                print(f"大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                launcher = """@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 修復版

echo.
echo ========================================
echo    台股智能選股系統 v22.1 - 修復版
echo ========================================
echo.

if exist "dist\\StockAnalyzer_Fixed.exe" (
    echo 找到修復版
    echo 正在啟動...
    echo.
    echo 修復版特點：
    echo    修復選股結果顯示問題
    echo    完整的策略交集分析
    echo    改善的用戶體驗
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Fixed.exe"
    
    echo 修復版已啟動！
    echo.
    
) else (
    echo 錯誤：找不到修復版
    echo.
    pause
    exit /b 1
)

timeout /t 3 >nul
"""
                
                with open('啟動修復版.bat', 'w', encoding='utf-8') as f:
                    f.write(launcher)
                
                print("創建啟動腳本: 啟動修復版.bat")
                return True
            else:
                print("找不到編譯後的檔案")
                return False
        else:
            print("編譯失敗！")
            if result.stderr:
                print("錯誤:", result.stderr[-1000:])
            return False
            
    except Exception as e:
        print(f"編譯過程錯誤: {e}")
        return False

if __name__ == "__main__":
    main()
