#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 numpy._core.numeric 修復
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_numpy_core_fix():
    """測試 numpy._core.numeric 修復"""
    print("🧪 測試 numpy._core.numeric 修復...")
    
    try:
        # 測試 numpy 導入和修復
        import numpy as np
        print(f"✅ numpy 版本: {np.__version__}")
        
        # 檢查 _core.numeric
        if hasattr(np, '_core') and hasattr(np._core, 'numeric'):
            print("✅ np._core.numeric 存在")
        else:
            print("⚠️ np._core.numeric 不存在，嘗試修復...")
            
            # 應用修復
            if not hasattr(np, '_core'):
                np._core = types.ModuleType('_core')
            if not hasattr(np._core, 'numeric'):
                np._core.numeric = types.ModuleType('numeric')
            
            print("✅ np._core.numeric 修復完成")
        
        # 測試基本 numpy 功能
        test_array = np.array([1, 2, 3])
        print(f"✅ numpy 基本功能正常: {test_array}")
        
        return True
        
    except Exception as e:
        print(f"❌ numpy 測試失敗: {str(e)}")
        return False

def test_to_pickle_with_fix():
    """測試修復後的 to_pickle 函數"""
    print("\n🧪 測試修復後的 to_pickle 函數...")
    
    try:
        from crawler import to_pickle
        
        # 創建測試資料
        test_data = pd.DataFrame({
            'value': [1, 2, 3, 4, 5]
        })
        test_data['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03', '2022-01-04', '2022-01-05'])
        test_data['stock_id'] = ['TEST1', 'TEST2', 'TEST3', 'TEST4', 'TEST5']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 測試存檔
        test_table_name = 'test_numpy_core_fix'
        
        print("執行 to_pickle...")
        to_pickle(test_data, test_table_name)
        print("✅ to_pickle 執行成功")
        
        # 驗證檔案
        test_file = f"history/tables/{test_table_name}.pkl"
        if os.path.exists(test_file):
            saved_data = pd.read_pickle(test_file)
            print(f"✅ 檔案驗證成功，共 {len(saved_data)} 筆資料")
            
            # 清理
            try:
                os.remove(test_file)
                print("🧹 清理測試檔案")
            except:
                pass
            
            return True
        else:
            print("❌ 檔案未創建")
            return False
        
    except Exception as e:
        print(f"❌ to_pickle 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_pandas_operations():
    """測試 pandas 相關操作"""
    print("\n🧪 測試 pandas 相關操作...")
    
    try:
        # 創建測試資料
        df1 = pd.DataFrame({
            'value': [1, 2, 3]
        })
        df1['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03'])
        df1['stock_id'] = ['A', 'B', 'C']
        df1 = df1.set_index(['stock_id', 'date'])
        
        df2 = pd.DataFrame({
            'value': [4, 5, 6]
        })
        df2['date'] = pd.to_datetime(['2022-01-04', '2022-01-05', '2022-01-06'])
        df2['stock_id'] = ['D', 'E', 'F']
        df2 = df2.set_index(['stock_id', 'date'])
        
        print("✅ 創建測試 DataFrame")
        
        # 測試 concat
        combined = pd.concat([df1, df2])
        print(f"✅ pd.concat 成功，共 {len(combined)} 筆")
        
        # 測試去重
        combined = combined.drop_duplicates()
        print(f"✅ drop_duplicates 成功，共 {len(combined)} 筆")
        
        # 測試排序
        combined = combined.sort_index()
        print("✅ sort_index 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ pandas 操作失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 numpy._core.numeric 修復測試")
    print("=" * 60)
    
    # 測試1: numpy 修復
    test1 = test_numpy_core_fix()
    
    # 測試2: pandas 操作
    test2 = test_pandas_operations()
    
    # 測試3: to_pickle 函數
    test3 = test_to_pickle_with_fix()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    print(f"numpy 修復: {'✅ 成功' if test1 else '❌ 失敗'}")
    print(f"pandas 操作: {'✅ 成功' if test2 else '❌ 失敗'}")
    print(f"to_pickle 函數: {'✅ 成功' if test3 else '❌ 失敗'}")
    
    if test1 and test2 and test3:
        print("\n🎉 numpy._core.numeric 修復成功！")
        print("✨ 分批存檔應該可以正常工作了")
        print("🚀 建議重新啟動 auto_update.py 使用修復版本")
    else:
        print("\n⚠️ 還需要進一步檢查")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    main()
