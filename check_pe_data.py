#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查PE資料庫結構和資料
"""

import sqlite3
import pandas as pd
from datetime import datetime

def check_pe_database():
    """檢查PE資料庫"""
    try:
        conn = sqlite3.connect('D:/Finlab/history/tables/pe_data.db')
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(pe_data)")
        columns = cursor.fetchall()
        print("📊 PE資料庫欄位結構:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查2025年7月資料
        cursor.execute("SELECT COUNT(*) FROM pe_data WHERE date LIKE '2025-07%'")
        count_2025_07 = cursor.fetchone()[0]
        print(f"\n📅 2025年7月資料筆數: {count_2025_07:,}")
        
        # 檢查最新資料日期
        cursor.execute("SELECT MAX(date) FROM pe_data")
        latest_date = cursor.fetchone()[0]
        print(f"📅 最新資料日期: {latest_date}")
        
        # 檢查樣本資料
        cursor.execute("""
            SELECT stock_id, stock_name, date, dividend_yield, pe_ratio, pb_ratio 
            FROM pe_data 
            WHERE date LIKE '2025-07%' 
            LIMIT 5
        """)
        samples = cursor.fetchall()
        print(f"\n📋 2025年7月樣本資料:")
        for sample in samples:
            print(f"   {sample}")
        
        # 檢查可用日期
        cursor.execute("""
            SELECT DISTINCT date
            FROM pe_data
            WHERE date LIKE '2025-07%'
            ORDER BY date DESC
            LIMIT 5
        """)
        available_dates = cursor.fetchall()
        print(f"\n📅 2025年7月可用日期:")
        for date_row in available_dates:
            print(f"   {date_row[0]}")

        # 檢查最新日期的資料
        if available_dates:
            latest_july_date = available_dates[0][0]
            cursor.execute("""
                SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                FROM pe_data
                WHERE date = ?
                LIMIT 10
            """, (latest_july_date,))
            specific_date_data = cursor.fetchall()
            print(f"\n📋 {latest_july_date}的資料樣本:")
            for data in specific_date_data:
                print(f"   {data}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查PE資料庫失敗: {e}")
        return False

def test_eps_calculation():
    """測試EPS計算"""
    print("\n" + "=" * 60)
    print("🧮 測試EPS計算")
    print("=" * 60)
    
    # 模擬資料
    test_data = [
        ("2330", "台積電", 25.5, 15.2),  # 股價25.5, PE 15.2
        ("2317", "鴻海", 120.0, 12.8),   # 股價120.0, PE 12.8
        ("2454", "聯發科", 850.0, 18.5), # 股價850.0, PE 18.5
    ]
    
    print("股票代碼 | 股票名稱 | 假設股價 | 本益比 | 計算EPS")
    print("-" * 50)
    
    for stock_id, stock_name, price, pe_ratio in test_data:
        if pe_ratio and pe_ratio > 0:
            eps = price / pe_ratio
            print(f"{stock_id:<8} | {stock_name:<8} | {price:>8.1f} | {pe_ratio:>6.1f} | {eps:>7.2f}")
        else:
            print(f"{stock_id:<8} | {stock_name:<8} | {price:>8.1f} | {pe_ratio:>6.1f} | {'N/A':>7}")
    
    print("\n✅ EPS計算公式: EPS = 股價 / 本益比")
    return True

if __name__ == "__main__":
    print("🔍 檢查PE資料庫...")
    
    success1 = check_pe_database()
    success2 = test_eps_calculation()
    
    if success1 and success2:
        print("\n🎉 PE資料庫檢查完成！")
    else:
        print("\n❌ 檢查過程中發生錯誤。")
