# 🖱️ 月營收排行榜右鍵選單功能完整指南

## 🎯 功能說明

已為月營收排行榜添加專門的右鍵選單功能，包含「月營收綜合評估」選項，提供詳細的股票分析資訊。

## 📋 完整使用步驟

### 步驟1: 啟動程式
```bash
python O3mh_gui_v21_optimized.py
```

### 步驟2: 執行月營收排行榜查詢
1. **選擇日期**: 在「計算日期」欄位選擇 `2025-07-29`
2. **選擇排行榜**: 在「查詢排行榜」下拉選單中選擇以下任一選項：
   - ✅ `月營收排行榜 (YoY排序)`
   - ✅ `月營收排行榜 (MoM排序)`
   - ✅ `月營收排行榜 (YoY排序) + 綜合評分`
   - ✅ `月營收排行榜 (MoM排序) + 綜合評分`
3. **執行查詢**: 點擊「執行排行」按鈕
4. **等待完成**: 等待查詢完成，右側表格顯示結果

### 步驟3: 確認表格結構
查詢完成後，右側表格應顯示以下欄位：
```
排名 | 股票代碼 | 股票名稱 | 西元年月 | 當月營收(千元) | 上個月營收(千元) | 去年同月營收(千元) | YoY% | MoM% | 殖利率(%) | 本益比 | 股價淨值比 | EPS | [綜合評分]
```

### 步驟4: 使用右鍵選單
1. **定位股票**: 在右側表格中找到感興趣的股票
2. **右鍵點擊**: 在該股票的任一欄位上右鍵點擊
3. **查看選單**: 應該出現包含以下選項的右鍵選單：
   - 📊 **[股票代碼] [股票名稱] 月營收綜合評估** ← 新功能
   - 📰 爬取 [股票代碼] [股票名稱] 新聞 (鉅亨網)
   - 🔍 爬取 [股票代碼] [股票名稱] Google新聞
   - 📈 查看 [股票代碼] K線圖
   - ℹ️ 查看 [股票代碼] 基本資料
   - 📊 加入監控清單

### 步驟5: 查看綜合評估
1. **點擊評估選項**: 選擇「月營收綜合評估」
2. **查看報告**: 彈出詳細的評估對話框
3. **分析資訊**: 報告包含排行榜資訊、營收表現、成長率分析、財務指標等

## 🔧 技術實現細節

### 核心函數
1. **`show_stock_context_menu(position)`**: 主要右鍵選單函數
2. **`is_monthly_revenue_ranking()`**: 檢測是否為月營收排行榜
3. **`show_monthly_revenue_context_menu(position, row)`**: 月營收專用右鍵選單
4. **`get_monthly_revenue_stock_data(row)`**: 提取股票資料
5. **`show_monthly_revenue_assessment(stock_data)`**: 顯示評估對話框

### 識別邏輯
程式通過以下條件識別月營收排行榜：
- 表格欄位數量 ≥ 13欄
- 第一欄標題為「排名」
- 包含月營收相關欄位（股票代碼、股票名稱、當月營收、YoY%、MoM%）

## 🐛 故障排除

### 問題1: 右鍵選單沒有出現
**症狀**: 右鍵點擊後沒有任何反應

**可能原因**:
- 沒有點擊在有效的表格項目上
- 表格沒有資料
- 右鍵選單設置有問題

**解決方法**:
1. 確保點擊在表格的資料行上（不是空白區域或標題行）
2. 確保表格有資料顯示
3. 重新啟動程式

### 問題2: 沒有「月營收綜合評估」選項
**症狀**: 右鍵選單出現，但沒有月營收綜合評估選項

**可能原因**:
- 程式沒有識別為月營收排行榜
- 表格結構不正確
- 選擇了錯誤的排行榜類型

**解決方法**:
1. 確認選擇的是月營收排行榜（不是其他排行榜）
2. 檢查表格第一欄是否為「排名」
3. 確認表格有13欄或14欄
4. 重新執行月營收排行榜查詢

### 問題3: 評估對話框沒有彈出
**症狀**: 點擊「月營收綜合評估」後沒有反應

**可能原因**:
- 股票資料提取失敗
- 對話框被其他視窗遮擋
- 程式錯誤

**解決方法**:
1. 檢查控制台是否有錯誤訊息
2. 查看工作列是否有新視窗
3. 重新嘗試點擊
4. 重新啟動程式

### 問題4: 表格沒有資料
**症狀**: 執行排行榜查詢後表格是空的

**可能原因**:
- 資料庫連接問題
- 選擇的日期沒有資料
- 網路連接問題

**解決方法**:
1. 檢查資料庫檔案是否存在
2. 嘗試選擇不同的日期
3. 檢查網路連接
4. 查看控制台錯誤訊息

## 📊 日誌檢查

如果功能不正常，可以查看控制台日誌訊息：

### 正常流程的日誌
```
🖱️ 右鍵選單被觸發，位置: QPoint(100, 50)
📊 點擊行數: 0
🔍 表格欄位數量: 14
🔍 第一欄標題: '排名'
✅ 確認為月營收排行榜
🔍 是否為月營收排行榜: True
🎯 調用月營收排行榜右鍵選單
📊 顯示月營收排行榜右鍵選單，行數: 0
📈 股票資訊: 2330 台積電
📊 添加月營收綜合評估選項
📰 添加一般功能選項
🖱️ 顯示月營收排行榜右鍵選單
```

### 異常情況的日誌
```
❌ 沒有點擊到有效項目
❌ 不是月營收排行榜
❌ 無法獲取股票資料
```

## 🧪 測試方法

### 方法1: 使用測試腳本
```bash
python test_right_click_fix.py
```

### 方法2: 手動測試
1. 啟動主程式
2. 執行月營收排行榜查詢
3. 右鍵點擊股票
4. 檢查選單內容

### 方法3: 最小化測試
運行測試腳本會創建一個最小化的測試視窗，可以直接測試右鍵選單功能。

## ✅ 成功標準

功能正常時應該看到：
- ✅ 右鍵選單正常彈出
- ✅ 包含「月營收綜合評估」選項
- ✅ 評估對話框正常顯示
- ✅ 資料完整且格式正確
- ✅ 成長率有顏色標示

## 📞 問題回報

如果問題仍然存在，請提供：
1. **操作步驟**: 詳細的操作過程
2. **預期結果**: 應該看到什麼
3. **實際結果**: 實際看到什麼
4. **錯誤訊息**: 控制台的錯誤訊息
5. **螢幕截圖**: 如果可能請提供截圖
6. **系統資訊**: 作業系統版本、Python版本等

## 🔮 進階功能

### 評估報告內容
- 🏆 排行榜資訊
- 💰 營收表現
- 📈 成長率分析（有顏色標示）
- 💎 財務指標
- 🎯 綜合評分（如果有）
- 📝 投資建議

### 快捷操作
- 右鍵選單中的其他功能仍然可用
- 可以直接從評估對話框查看K線圖
- 可以直接加入監控清單

## 🎉 總結

這個功能讓月營收排行榜不僅提供排行資訊，還能通過右鍵選單提供深度的個股分析，大大提升了投資研究的效率和便利性。

如果按照以上步驟操作仍有問題，請檢查是否正確執行了月營收排行榜查詢，並確保表格有資料顯示。
