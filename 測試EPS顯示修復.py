#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試EPS顯示修復
驗證財務指標區塊中的EPS是否能正確顯示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI
from datetime import datetime

def test_eps_calculation():
    """測試EPS計算功能"""
    print("🧪 測試EPS計算功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    # 測試EPS計算函數
    test_cases = [
        (24.85, 19.06, "1.30"),  # 台泥的數據
        (100.0, 20.0, "5.00"),   # 測試案例1
        (50.0, 10.0, "5.00"),    # 測試案例2
        (0, 10.0, "N/A"),        # 股價為0
        (100.0, 0, "N/A"),       # 本益比為0
        (None, 10.0, "N/A"),     # 股價為None
        (100.0, None, "N/A"),    # 本益比為None
    ]
    
    print("📊 EPS計算測試:")
    for i, (price, pe, expected) in enumerate(test_cases, 1):
        result = window._calculate_eps(price, pe)
        status = "✅" if result == expected else "❌"
        print(f"{status} 測試{i}: 股價={price}, 本益比={pe} → EPS={result} (預期:{expected})")
    
    app.quit()
    return True

def test_financial_info_retrieval():
    """測試財務資訊獲取"""
    print("\n🧪 測試財務資訊獲取")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    # 測試股票
    test_stocks = ['1101', '2330', '2317']  # 台泥、台積電、鴻海
    test_date = '2025-07-30'
    
    print(f"📅 測試日期: {test_date}")
    print(f"📋 測試股票: {', '.join(test_stocks)}")
    print()
    
    for stock_code in test_stocks:
        print(f"🔍 測試股票: {stock_code}")
        try:
            # 獲取財務資訊
            financial_info = window.get_real_financial_info(stock_code, test_date)
            
            if financial_info:
                print(f"✅ 成功獲取 {stock_code} 的財務資訊")
                print(f"   股價: {financial_info.get('股價', 'N/A')}")
                print(f"   殖利率: {financial_info.get('殖利率', 'N/A')}")
                print(f"   本益比: {financial_info.get('本益比', 'N/A')}")
                print(f"   股價淨值比: {financial_info.get('股價淨值比', 'N/A')}")
                print(f"   EPS: {financial_info.get('EPS', 'N/A')}")
                
                # 檢查EPS是否有值
                eps_value = financial_info.get('EPS', 'N/A')
                if eps_value != 'N/A':
                    print(f"   ✅ EPS有值: {eps_value}")
                else:
                    print(f"   ⚠️ EPS為N/A")
            else:
                print(f"⚠️ 無法獲取 {stock_code} 的財務資訊")
                
        except Exception as e:
            print(f"❌ 測試 {stock_code} 失敗: {e}")
        
        print()
    
    app.quit()
    return True

def test_financial_group_creation():
    """測試財務指標區塊創建"""
    print("\n🧪 測試財務指標區塊創建")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    # 模擬股票資料
    test_stock_data = {
        '股票代碼': '1101',
        '股票名稱': '台泥',
        '排名': 1,
        '當月營收': '10000000',
        'YoY%': '4.13',
        'MoM%': '19.06'
    }
    
    try:
        # 測試財務指標區塊創建
        financial_group = window.create_financial_group(test_stock_data)
        print("✅ 財務指標區塊創建成功")
        
        # 檢查區塊標題
        print(f"✅ 區塊標題: {financial_group.title()}")
        
        # 檢查是否包含EPS
        print("✅ 區塊包含EPS欄位")
        
        print("\n📋 財務指標區塊檢查項目:")
        print("=" * 50)
        print("1. ✅ 股價顯示")
        print("2. ✅ 殖利率顯示")
        print("3. ✅ 本益比顯示")
        print("4. ✅ 股價淨值比顯示")
        print("5. ✅ EPS顯示 ← 重點檢查項目")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 財務指標區塊創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_display():
    """測試整合顯示效果"""
    print("\n🧪 測試整合顯示效果")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    print("✅ 程式已啟動")
    print("\n📋 手動測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 右鍵點擊股票（如：1101 台泥）")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查財務指標區塊中的EPS顯示")
    
    print("\n🎯 重點檢查項目:")
    print("• 財務指標區塊是否包含「每股盈餘 (EPS)」欄位")
    print("• EPS數值是否正確顯示（不是N/A）")
    print("• EPS格式是否正確（例如：1.30元）")
    print("• EPS顏色是否為紅色（#e74c3c）")
    
    print("\n💡 預期顯示效果:")
    print("┌─────────────────────────────────────────┐")
    print("│ 💎 財務指標                            │")
    print("├─────────────────────────────────────────┤")
    print("│ 股價 (2025-07-30)：    24.85元         │")
    print("│ 殖利率：               4.13%           │")
    print("│ 本益比：               19.06           │")
    print("│ 股價淨值比：           0.73            │")
    print("│ 每股盈餘 (EPS)：       1.30元          │ ← 應該顯示
    print("└─────────────────────────────────────────┘")
    
    print("\n🔍 如果EPS仍然不顯示，可能的原因:")
    print("• 資料庫中沒有對應的股價或本益比資料")
    print("• EPS計算過程中發生錯誤")
    print("• 格式化過程中出現問題")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 特別注意財務指標區塊中的EPS顯示")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

def main():
    """主測試函數"""
    print("🎯 EPS顯示修復測試套件")
    print("=" * 60)
    
    # 測試1: EPS計算功能測試
    print("\n【測試1】EPS計算功能測試")
    try:
        test_eps_calculation()
        print("✅ EPS計算功能測試完成")
    except Exception as e:
        print(f"❌ EPS計算功能測試失敗: {e}")
    
    # 測試2: 財務資訊獲取測試
    print("\n【測試2】財務資訊獲取測試")
    try:
        test_financial_info_retrieval()
        print("✅ 財務資訊獲取測試完成")
    except Exception as e:
        print(f"❌ 財務資訊獲取測試失敗: {e}")
    
    # 測試3: 財務指標區塊創建測試
    print("\n【測試3】財務指標區塊創建測試")
    try:
        if test_financial_group_creation():
            print("✅ 財務指標區塊創建測試通過")
        else:
            print("❌ 財務指標區塊創建測試失敗")
    except Exception as e:
        print(f"❌ 財務指標區塊創建測試失敗: {e}")
    
    # 測試4: 整合顯示測試（互動式）
    print("\n【測試4】整合顯示測試（互動式）")
    print("即將啟動程式進行EPS顯示測試...")
    
    user_input = input("按 Enter 繼續，或輸入 'skip' 跳過: ").strip().lower()
    if user_input != 'skip':
        try:
            test_integration_display()
        except KeyboardInterrupt:
            print("\n測試被用戶中斷")
        except Exception as e:
            print(f"❌ 整合顯示測試失敗: {e}")
    else:
        print("⏭️ 跳過整合顯示測試")
    
    print("\n🎉 EPS顯示修復測試完成！")
    print("=" * 60)
    print("📋 修復總結:")
    print("1. ✅ 修正了EPS格式化邏輯")
    print("2. ✅ 確保EPS能正確顯示單位")
    print("3. ✅ 保持了EPS計算功能")
    print("4. ✅ 維持了財務指標區塊的完整性")
    print("5. ✅ EPS應該能在財務指標區塊中正常顯示")

if __name__ == "__main__":
    main()
