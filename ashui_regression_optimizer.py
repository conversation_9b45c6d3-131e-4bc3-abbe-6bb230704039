#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿水一式評分迴歸優化器
用於找出最佳的評分權重和參數
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class AshiuRegressionOptimizer:
    """阿水一式評分迴歸優化器"""
    
    def __init__(self):
        """初始化優化器"""
        self.models = {}
        self.best_model = None
        self.feature_importance = {}
        self.optimized_weights = {}
        self.scaler = StandardScaler()
        
    def prepare_data(self, backtest_results: List[Dict]) -> <PERSON><PERSON>[pd.DataFrame, pd.Series]:
        """
        準備迴歸分析數據
        
        Args:
            backtest_results: 回測結果列表
            
        Returns:
            (特徵DataFrame, 目標變數Series)
        """
        # 提取交易記錄
        trades = [r for r in backtest_results if 'trade_return' in r]
        
        if not trades:
            raise ValueError("沒有交易記錄可供分析")
        
        # 準備特徵數據
        features_data = []
        returns = []
        
        for trade in trades:
            indicators = trade['indicators']
            
            # 原始特徵
            features = {
                'turnover': indicators.get('turnover', 0),
                'bb_width': indicators.get('bb_width', 0),
                'compression_days': indicators.get('compression_days', 0),
                'volume_ratio': indicators.get('volume_ratio', 0),
                'breakout_pct': indicators.get('breakout_pct', 0)
            }
            
            # 添加衍生特徵
            features.update(self.create_derived_features(features))
            
            features_data.append(features)
            returns.append(trade['trade_return'])
        
        X = pd.DataFrame(features_data)
        y = pd.Series(returns)
        
        return X, y
    
    def create_derived_features(self, features: Dict) -> Dict:
        """
        創建衍生特徵
        
        Args:
            features: 原始特徵字典
            
        Returns:
            衍生特徵字典
        """
        derived = {}
        
        # 對數變換（處理偏態分布）
        if features['turnover'] > 0:
            derived['log_turnover'] = np.log(features['turnover'])
        else:
            derived['log_turnover'] = 0
        
        # 平方項（捕捉非線性關係）
        derived['bb_width_squared'] = features['bb_width'] ** 2
        derived['volume_ratio_squared'] = features['volume_ratio'] ** 2
        
        # 交互項
        derived['turnover_volume_interaction'] = features['turnover'] * features['volume_ratio']
        derived['bb_width_compression_interaction'] = features['bb_width'] * features['compression_days']
        
        # 分組特徵
        derived['high_turnover'] = 1 if features['turnover'] >= 10000 else 0
        derived['ideal_bb_width'] = 1 if 0.08 <= features['bb_width'] <= 0.15 else 0
        derived['good_compression'] = 1 if 5 <= features['compression_days'] <= 15 else 0
        derived['strong_volume'] = 1 if features['volume_ratio'] >= 3.0 else 0
        derived['strong_breakout'] = 1 if features['breakout_pct'] >= 5.0 else 0
        
        return derived
    
    def train_models(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """
        訓練多種迴歸模型
        
        Args:
            X: 特徵數據
            y: 目標變數
            
        Returns:
            模型結果字典
        """
        # 標準化特徵
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns)
        
        models_to_train = {
            'linear': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=0.01),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42)
        }
        
        results = {}
        
        for name, model in models_to_train.items():
            print(f"訓練 {name} 模型...")
            
            # 交叉驗證
            cv_scores = cross_val_score(model, X_scaled_df, y, cv=5, scoring='r2')
            
            # 訓練完整模型
            model.fit(X_scaled_df, y)
            
            # 預測
            y_pred = model.predict(X_scaled_df)
            
            # 計算指標
            r2 = r2_score(y, y_pred)
            mse = mean_squared_error(y, y_pred)
            
            results[name] = {
                'model': model,
                'cv_scores': cv_scores,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'r2': r2,
                'mse': mse,
                'predictions': y_pred
            }
            
            # 特徵重要性
            if hasattr(model, 'feature_importances_'):
                results[name]['feature_importance'] = dict(zip(X.columns, model.feature_importances_))
            elif hasattr(model, 'coef_'):
                results[name]['coefficients'] = dict(zip(X.columns, model.coef_))
        
        self.models = results
        
        # 選擇最佳模型
        best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
        self.best_model = results[best_model_name]
        
        print(f"最佳模型: {best_model_name} (CV R² = {self.best_model['cv_mean']:.4f})")
        
        return results
    
    def optimize_scoring_weights(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """
        優化評分權重
        
        Args:
            X: 特徵數據
            y: 目標變數
            
        Returns:
            優化後的權重字典
        """
        # 使用最佳模型的係數或重要性
        if 'coefficients' in self.best_model:
            # 線性模型使用係數
            weights = self.best_model['coefficients']
        elif 'feature_importance' in self.best_model:
            # 樹模型使用特徵重要性
            weights = self.best_model['feature_importance']
        else:
            # 默認等權重
            weights = {col: 1.0 for col in X.columns}
        
        # 只保留原始特徵的權重
        original_features = ['turnover', 'bb_width', 'compression_days', 'volume_ratio', 'breakout_pct']
        original_weights = {k: abs(weights.get(k, 0)) for k in original_features}
        
        # 正規化權重到100分
        total_weight = sum(original_weights.values())
        if total_weight > 0:
            normalized_weights = {k: (v / total_weight) * 100 for k, v in original_weights.items()}
        else:
            # 如果所有權重都是0，使用默認權重
            normalized_weights = {
                'turnover': 25,
                'bb_width': 20,
                'compression_days': 15,
                'volume_ratio': 20,
                'breakout_pct': 15
            }
        
        self.optimized_weights = normalized_weights
        
        return normalized_weights
    
    def generate_optimized_scoring_function(self) -> str:
        """
        生成優化後的評分函數代碼
        
        Returns:
            評分函數的Python代碼字符串
        """
        weights = self.optimized_weights
        
        code = f"""
def calculate_optimized_ashui_score(indicators: Dict) -> float:
    \"\"\"
    優化後的阿水一式評分函數
    基於回測數據的迴歸分析結果
    \"\"\"
    score = 0
    
    # 成交金額評分 ({weights['turnover']:.1f}分)
    turnover = indicators.get('turnover', 0)
    if turnover >= 100000:
        score += {weights['turnover']:.1f}
    elif turnover >= 50000:
        score += {weights['turnover'] * 0.88:.1f}
    elif turnover >= 20000:
        score += {weights['turnover'] * 0.72:.1f}
    elif turnover >= 10000:
        score += {weights['turnover'] * 0.60:.1f}
    elif turnover >= 5000:
        score += {weights['turnover'] * 0.48:.1f}
    elif turnover >= 1000:
        score += {weights['turnover'] * 0.32:.1f}
    elif turnover >= 500:
        score += {weights['turnover'] * 0.20:.1f}
    
    # 布林帶寬評分 ({weights['bb_width']:.1f}分)
    bb_width = indicators.get('bb_width', 0)
    if 0.08 <= bb_width <= 0.15:
        score += {weights['bb_width']:.1f}
    elif 0.06 <= bb_width <= 0.20:
        score += {weights['bb_width'] * 0.75:.1f}
    elif 0.04 <= bb_width <= 0.25:
        score += {weights['bb_width'] * 0.50:.1f}
    elif bb_width > 0:
        score += {weights['bb_width'] * 0.25:.1f}
    
    # 壓縮天數評分 ({weights['compression_days']:.1f}分)
    compression_days = indicators.get('compression_days', 0)
    if 5 <= compression_days <= 15:
        score += {weights['compression_days']:.1f}
    elif 3 <= compression_days <= 20:
        score += {weights['compression_days'] * 0.80:.1f}
    elif 1 <= compression_days <= 25:
        score += {weights['compression_days'] * 0.53:.1f}
    elif compression_days > 0:
        score += {weights['compression_days'] * 0.33:.1f}
    
    # 成交量倍數評分 ({weights['volume_ratio']:.1f}分)
    volume_ratio = indicators.get('volume_ratio', 0)
    if volume_ratio >= 5.0:
        score += {weights['volume_ratio']:.1f}
    elif volume_ratio >= 3.0:
        score += {weights['volume_ratio'] * 0.90:.1f}
    elif volume_ratio >= 2.5:
        score += {weights['volume_ratio'] * 0.75:.1f}
    elif volume_ratio >= 2.0:
        score += {weights['volume_ratio'] * 0.60:.1f}
    elif volume_ratio >= 1.5:
        score += {weights['volume_ratio'] * 0.40:.1f}
    elif volume_ratio >= 1.0:
        score += {weights['volume_ratio'] * 0.25:.1f}
    
    # 突破幅度評分 ({weights['breakout_pct']:.1f}分)
    breakout_pct = indicators.get('breakout_pct', 0)
    if breakout_pct >= 8.0:
        score += {weights['breakout_pct']:.1f}
    elif breakout_pct >= 5.0:
        score += {weights['breakout_pct'] * 0.80:.1f}
    elif breakout_pct >= 3.0:
        score += {weights['breakout_pct'] * 0.67:.1f}
    elif breakout_pct >= 1.0:
        score += {weights['breakout_pct'] * 0.47:.1f}
    elif breakout_pct > 0:
        score += {weights['breakout_pct'] * 0.33:.1f}
    
    # 策略匹配加分 (5分)
    score += 5
    
    return min(max(score, 0), 100)
"""
        
        return code
    
    def plot_analysis_results(self, X: pd.DataFrame, y: pd.Series, save_path: str = None):
        """
        繪製分析結果圖表
        
        Args:
            X: 特徵數據
            y: 目標變數
            save_path: 保存路徑
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('阿水一式因子分析結果', fontsize=16)
        
        # 1. 特徵與報酬的相關性
        correlations = X.corrwith(y)
        axes[0, 0].bar(correlations.index, correlations.values)
        axes[0, 0].set_title('特徵與報酬相關性')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 模型比較
        model_names = list(self.models.keys())
        cv_scores = [self.models[name]['cv_mean'] for name in model_names]
        axes[0, 1].bar(model_names, cv_scores)
        axes[0, 1].set_title('模型交叉驗證R²比較')
        axes[0, 1].set_ylabel('R² Score')
        
        # 3. 最佳模型預測 vs 實際
        best_pred = self.best_model['predictions']
        axes[0, 2].scatter(y, best_pred, alpha=0.6)
        axes[0, 2].plot([y.min(), y.max()], [y.min(), y.max()], 'r--', lw=2)
        axes[0, 2].set_xlabel('實際報酬')
        axes[0, 2].set_ylabel('預測報酬')
        axes[0, 2].set_title('預測 vs 實際報酬')
        
        # 4. 優化後的權重
        weights = self.optimized_weights
        axes[1, 0].pie(weights.values(), labels=weights.keys(), autopct='%1.1f%%')
        axes[1, 0].set_title('優化後的評分權重')
        
        # 5. 報酬分布
        axes[1, 1].hist(y, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('報酬率')
        axes[1, 1].set_ylabel('頻率')
        axes[1, 1].set_title('報酬率分布')
        
        # 6. 特徵重要性（如果有的話）
        if 'feature_importance' in self.best_model:
            importance = self.best_model['feature_importance']
            axes[1, 2].bar(importance.keys(), importance.values())
            axes[1, 2].set_title('特徵重要性')
            axes[1, 2].tick_params(axis='x', rotation=45)
        elif 'coefficients' in self.best_model:
            coefs = self.best_model['coefficients']
            axes[1, 2].bar(coefs.keys(), [abs(v) for v in coefs.values()])
            axes[1, 2].set_title('特徵係數絕對值')
            axes[1, 2].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def generate_analysis_report(self) -> str:
        """生成分析報告"""
        if not self.models or not self.optimized_weights:
            return "尚未進行分析，請先執行 train_models 和 optimize_scoring_weights"
        
        report = f"""
# 阿水一式評分優化分析報告

## 模型比較結果
"""
        
        for name, result in self.models.items():
            report += f"""
### {name.upper()} 模型
- 交叉驗證 R²: {result['cv_mean']:.4f} ± {result['cv_std']:.4f}
- 訓練集 R²: {result['r2']:.4f}
- 均方誤差: {result['mse']:.6f}
"""
        
        report += f"""
## 最佳模型
- 模型類型: {type(self.best_model['model']).__name__}
- 交叉驗證 R²: {self.best_model['cv_mean']:.4f}

## 優化後的評分權重
"""
        
        for factor, weight in self.optimized_weights.items():
            report += f"- {factor}: {weight:.1f}分\n"
        
        report += f"""
## 建議
1. 使用優化後的權重更新評分函數
2. 定期重新進行回測驗證
3. 考慮加入更多技術指標作為特徵
4. 監控模型在新數據上的表現
"""
        
        return report
