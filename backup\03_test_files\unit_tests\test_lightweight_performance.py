#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試輕量級新聞爬蟲效能
"""

import time
import psutil
import os
from datetime import datetime

def monitor_performance(func, *args, **kwargs):
    """監控函數執行的效能"""
    process = psutil.Process(os.getpid())
    
    # 記錄開始狀態
    start_time = time.time()
    start_memory = process.memory_info().rss / 1024 / 1024  # MB
    start_cpu = process.cpu_percent()
    
    print(f"🚀 開始執行: {func.__name__}")
    print(f"📊 初始記憶體: {start_memory:.1f} MB")
    
    # 執行函數
    try:
        result = func(*args, **kwargs)
        success = True
    except Exception as e:
        result = str(e)
        success = False
    
    # 記錄結束狀態
    end_time = time.time()
    end_memory = process.memory_info().rss / 1024 / 1024  # MB
    end_cpu = process.cpu_percent()
    
    # 計算差異
    duration = end_time - start_time
    memory_diff = end_memory - start_memory
    
    print(f"⏱️  執行時間: {duration:.2f} 秒")
    print(f"💾 記憶體變化: {memory_diff:+.1f} MB (結束: {end_memory:.1f} MB)")
    print(f"🖥️  CPU使用: {end_cpu:.1f}%")
    print(f"✅ 執行結果: {'成功' if success else '失敗'}")
    print(f"📄 返回值: {result}")
    
    return {
        'success': success,
        'duration': duration,
        'memory_diff': memory_diff,
        'end_memory': end_memory,
        'cpu_usage': end_cpu,
        'result': result
    }

def test_original_crawler():
    """測試原版爬蟲"""
    print("\n" + "="*60)
    print("🔍 測試原版新聞爬蟲")
    print("="*60)
    
    def run_original():
        from news_crawler_anue import AnueNewsCrawler
        crawler = AnueNewsCrawler()
        return crawler.run(ndays=1)
    
    return monitor_performance(run_original)

def test_optimized_crawler():
    """測試優化版爬蟲"""
    print("\n" + "="*60)
    print("🔍 測試優化版新聞爬蟲")
    print("="*60)
    
    def run_optimized():
        from news_crawler_optimized import OptimizedNewsCrawler
        crawler = OptimizedNewsCrawler()
        return crawler.run(ndays=1)
    
    return monitor_performance(run_optimized)

def test_lightweight_crawler():
    """測試輕量級爬蟲"""
    print("\n" + "="*60)
    print("🔍 測試輕量級新聞爬蟲")
    print("="*60)
    
    def run_lightweight():
        from news_crawler_lightweight import LightweightNewsCrawler
        crawler = LightweightNewsCrawler()
        return crawler.run_lightweight(ndays=1)
    
    return monitor_performance(run_lightweight)

def test_quick_crawler():
    """測試極速爬蟲"""
    print("\n" + "="*60)
    print("🔍 測試極速新聞爬蟲")
    print("="*60)
    
    def run_quick():
        from news_crawler_lightweight import QuickNewsCrawler
        crawler = QuickNewsCrawler()
        results = crawler.quick_search("2330", days=1)
        return f"找到 {len(results)} 筆新聞"
    
    return monitor_performance(run_quick)

def compare_results(results):
    """比較測試結果"""
    print("\n" + "="*60)
    print("📊 效能比較結果")
    print("="*60)
    
    # 表格標題
    print(f"{'爬蟲類型':<15} {'時間(秒)':<10} {'記憶體(MB)':<12} {'CPU%':<8} {'狀態':<6}")
    print("-" * 60)
    
    # 顯示結果
    for name, result in results.items():
        status = "✅" if result['success'] else "❌"
        print(f"{name:<15} {result['duration']:<10.2f} {result['memory_diff']:+<12.1f} {result['cpu_usage']:<8.1f} {status:<6}")
    
    # 找出最佳選項
    successful_results = {k: v for k, v in results.items() if v['success']}
    
    if successful_results:
        # 按時間排序
        fastest = min(successful_results.items(), key=lambda x: x[1]['duration'])
        # 按記憶體使用排序
        lowest_memory = min(successful_results.items(), key=lambda x: x[1]['memory_diff'])
        
        print(f"\n🏆 效能冠軍:")
        print(f"  ⚡ 最快速度: {fastest[0]} ({fastest[1]['duration']:.2f}秒)")
        print(f"  💾 最省記憶體: {lowest_memory[0]} ({lowest_memory[1]['memory_diff']:+.1f}MB)")
        
        # 計算改善幅度
        if '原版爬蟲' in results and results['原版爬蟲']['success']:
            original_time = results['原版爬蟲']['duration']
            
            for name, result in successful_results.items():
                if name != '原版爬蟲':
                    time_improvement = (original_time - result['duration']) / original_time * 100
                    print(f"  📈 {name} 速度改善: {time_improvement:+.1f}%")

def main():
    """主測試函數"""
    print("🚀 新聞爬蟲效能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🖥️  系統: {psutil.virtual_memory().total / 1024**3:.1f}GB RAM")
    
    results = {}
    
    # 測試順序：從輕量到重量
    try:
        results['極速爬蟲'] = test_quick_crawler()
    except Exception as e:
        print(f"❌ 極速爬蟲測試失敗: {e}")
        results['極速爬蟲'] = {'success': False, 'duration': 0, 'memory_diff': 0, 'cpu_usage': 0}
    
    time.sleep(2)  # 讓系統穩定
    
    try:
        results['輕量級爬蟲'] = test_lightweight_crawler()
    except Exception as e:
        print(f"❌ 輕量級爬蟲測試失敗: {e}")
        results['輕量級爬蟲'] = {'success': False, 'duration': 0, 'memory_diff': 0, 'cpu_usage': 0}
    
    time.sleep(2)
    
    try:
        results['優化版爬蟲'] = test_optimized_crawler()
    except Exception as e:
        print(f"❌ 優化版爬蟲測試失敗: {e}")
        results['優化版爬蟲'] = {'success': False, 'duration': 0, 'memory_diff': 0, 'cpu_usage': 0}
    
    time.sleep(2)
    
    # 原版爬蟲可能會很慢，最後測試
    try:
        results['原版爬蟲'] = test_original_crawler()
    except Exception as e:
        print(f"❌ 原版爬蟲測試失敗: {e}")
        results['原版爬蟲'] = {'success': False, 'duration': 0, 'memory_diff': 0, 'cpu_usage': 0}
    
    # 比較結果
    compare_results(results)
    
    # 建議
    print(f"\n💡 建議:")
    successful_count = sum(1 for r in results.values() if r['success'])
    
    if successful_count == 0:
        print("  ❌ 所有爬蟲都失敗了，請檢查網路連線和相關模組")
    elif successful_count == 1:
        working_crawler = [name for name, result in results.items() if result['success']][0]
        print(f"  ✅ 建議使用: {working_crawler}")
    else:
        fastest_working = min(
            [(name, result) for name, result in results.items() if result['success']], 
            key=lambda x: x[1]['duration']
        )
        print(f"  🚀 建議使用: {fastest_working[0]} (最快且穩定)")
        print(f"  ⚡ 如果系統負擔過重，優先使用極速爬蟲或輕量級爬蟲")

if __name__ == "__main__":
    main()
    
    print("\n按 Enter 鍵結束...")
    input()
