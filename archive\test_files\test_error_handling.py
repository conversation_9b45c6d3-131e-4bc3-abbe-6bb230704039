#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試錯誤處理和請求限制機制
"""

import sys
import os
import time
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def test_error_handling():
    """測試錯誤處理機制"""
    print("🛡️ 錯誤處理和請求限制測試")
    print("=" * 60)
    
    # 創建策略實例
    strategy = IntradayOpeningRangeStrategy()
    
    # 🔧 調整參數以測試錯誤處理
    strategy.request_delay = 0.2  # 增加請求間隔
    strategy.max_retries = 2      # 減少重試次數以加快測試
    strategy.rate_limit_delay = 2 # 減少等待時間
    
    print(f"📊 測試參數:")
    print(f"• 請求間隔: {strategy.request_delay} 秒")
    print(f"• 最大重試: {strategy.max_retries} 次")
    print(f"• 限制等待: {strategy.rate_limit_delay} 秒")
    
    # 測試股票列表（包含一些可能有問題的股票代碼）
    test_stocks = [
        '2330', '2317', '2454',  # 正常股票
        '1701', '1704', '1715',  # 可能有問題的股票
        '1724', '1742', '9999',  # 更多測試股票
        '0000', '1234', '5678'   # 無效股票代碼
    ]
    
    print(f"\n🧪 測試1: 單個股票錯誤處理")
    print("-" * 40)
    
    for i, stock_id in enumerate(test_stocks[:6]):  # 只測試前6個
        print(f"[{i+1}/6] 測試 {stock_id}...", end=" ")
        
        start_time = time.time()
        
        try:
            data = strategy.fetch_daily_data(stock_id, 10)
            elapsed = time.time() - start_time
            
            if not data.empty:
                print(f"✅ 成功 ({len(data)} 筆, {elapsed:.2f}s)")
            else:
                print(f"❌ 無數據 ({elapsed:.2f}s)")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"⚠️ 錯誤 ({elapsed:.2f}s): {str(e)[:30]}...")
    
    print(f"\n🧪 測試2: 批量獲取錯誤處理")
    print("-" * 40)
    
    # 重置失敗列表以重新測試
    strategy.failed_stocks.clear()
    
    start_time = time.time()
    
    try:
        # 使用較少的線程數以避免過度請求
        batch_results = strategy.batch_fetch_data(test_stocks, max_workers=3)
        
        elapsed = time.time() - start_time
        
        print(f"✅ 批量獲取完成")
        print(f"⏱️  總耗時: {elapsed:.2f} 秒")
        print(f"📊 成功獲取: {len(batch_results)} 支股票")
        print(f"❌ 失敗股票: {len(strategy.failed_stocks)} 支")
        
        if strategy.failed_stocks:
            print(f"失敗列表: {', '.join(list(strategy.failed_stocks)[:5])}")
        
    except Exception as e:
        print(f"❌ 批量獲取失敗: {e}")
    
    print(f"\n🧪 測試3: 超快速分析錯誤處理")
    print("-" * 40)
    
    # 測試超快速分析的錯誤處理
    start_time = time.time()
    
    try:
        ultra_results = strategy.ultra_fast_analysis(test_stocks)
        
        elapsed = time.time() - start_time
        
        print(f"✅ 超快速分析完成")
        print(f"⏱️  總耗時: {elapsed:.2f} 秒")
        print(f"🎯 找到信號: {len(ultra_results)} 支")
        
        if ultra_results:
            print(f"信號股票: {[r['stock_id'] for r in ultra_results[:3]]}")
        
    except Exception as e:
        print(f"❌ 超快速分析失敗: {e}")
    
    print(f"\n📊 錯誤統計:")
    print(f"• 總失敗股票: {len(strategy.failed_stocks)}")
    print(f"• 緩存命中: {len(strategy.data_cache)}")
    
    # 測試緩存效果
    print(f"\n🗄️  測試4: 緩存效果驗證")
    print("-" * 40)
    
    # 第二次獲取相同數據（應該使用緩存）
    cache_test_stocks = ['2330', '2317', '2454']
    
    start_time = time.time()
    cached_count = 0
    
    for stock_id in cache_test_stocks:
        data = strategy.fetch_daily_data(stock_id, 10)
        if not data.empty:
            cached_count += 1
    
    cached_time = time.time() - start_time
    
    print(f"• 緩存測試: {cached_count}/{len(cache_test_stocks)} 成功")
    print(f"• 緩存耗時: {cached_time:.3f} 秒")
    print(f"• 平均耗時: {cached_time/len(cache_test_stocks):.3f} 秒/股票")

def test_rate_limiting():
    """測試請求限制機制"""
    print(f"\n⏱️  測試5: 請求限制機制")
    print("-" * 40)
    
    strategy = IntradayOpeningRangeStrategy()
    strategy.request_delay = 0.5  # 設置較長的請求間隔
    
    test_stocks = ['2330', '2317', '2454']
    
    print(f"設置請求間隔: {strategy.request_delay} 秒")
    
    total_start = time.time()
    
    for i, stock_id in enumerate(test_stocks):
        start_time = time.time()
        
        # 模擬請求
        strategy._wait_for_rate_limit()
        
        elapsed = time.time() - start_time
        print(f"[{i+1}] {stock_id}: 等待 {elapsed:.3f} 秒")
    
    total_elapsed = time.time() - total_start
    expected_time = len(test_stocks) * strategy.request_delay
    
    print(f"\n📊 限制效果:")
    print(f"• 實際耗時: {total_elapsed:.2f} 秒")
    print(f"• 預期耗時: {expected_time:.2f} 秒")
    print(f"• 限制效果: {'✅ 正常' if total_elapsed >= expected_time * 0.8 else '⚠️ 可能有問題'}")

def test_recovery_mechanism():
    """測試恢復機制"""
    print(f"\n🔄 測試6: 失敗恢復機制")
    print("-" * 40)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 手動添加一些失敗股票
    strategy.failed_stocks.update(['9999', '0000', '1234'])
    print(f"手動添加失敗股票: {strategy.failed_stocks}")
    
    # 測試是否會跳過失敗股票
    test_stocks = ['2330', '9999', '2317', '0000', '2454']
    
    start_time = time.time()
    results = strategy.batch_fetch_data(test_stocks, max_workers=2)
    elapsed = time.time() - start_time
    
    print(f"批量獲取結果:")
    print(f"• 輸入股票: {len(test_stocks)} 支")
    print(f"• 成功獲取: {len(results)} 支")
    print(f"• 耗時: {elapsed:.2f} 秒")
    print(f"• 跳過效果: {'✅ 正常' if len(results) <= 3 else '⚠️ 可能沒有跳過失敗股票'}")
    
    # 清理失敗列表
    strategy.failed_stocks.clear()
    print(f"清理失敗列表: {len(strategy.failed_stocks)} 支")

def main():
    """主測試函數"""
    try:
        test_error_handling()
        test_rate_limiting()
        test_recovery_mechanism()
        
        print(f"\n" + "=" * 60)
        print("🎉 錯誤處理測試完成")
        print("=" * 60)
        
        print(f"\n💡 優化建議:")
        print(f"• 如果遇到大量請求限制，增加 request_delay")
        print(f"• 如果網絡不穩定，增加 max_retries")
        print(f"• 如果處理大量股票，減少 max_workers")
        print(f"• 定期清理 failed_stocks 以重新嘗試")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
