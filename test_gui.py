#!/usr/bin/env python3
"""
測試新的增量更新GUI
"""

import os
import pickle
import pandas as pd
from datetime import datetime, timedelta

def create_test_data():
    """創建測試用的pkl檔案"""
    
    # 確保目錄存在
    data_dir = os.path.join('finlab_ml_course', 'history', 'tables')
    os.makedirs(data_dir, exist_ok=True)
    
    # 創建測試資料
    test_files = {
        'price.pkl': '股價資料',
        'bargin_report.pkl': '融資融券',
        'pe.pkl': '本益比',
        'monthly_report.pkl': '月報',
        'balance_sheet.pkl': '資產負債表'
    }
    
    for filename, description in test_files.items():
        filepath = os.path.join(data_dir, filename)
        
        # 創建假資料，最後日期為一週前
        last_date = datetime.now() - timedelta(days=7)
        dates = pd.date_range(start=last_date - timedelta(days=30), end=last_date, freq='D')
        
        # 創建測試DataFrame
        test_data = pd.DataFrame({
            'date': dates,
            'value': range(len(dates)),
            'description': [description] * len(dates)
        })
        test_data.set_index('date', inplace=True)
        
        # 保存為pkl檔案
        with open(filepath, 'wb') as f:
            pickle.dump(test_data, f)
        
        print(f"✓ 創建測試檔案: {filename} (最後日期: {last_date.strftime('%Y-%m-%d')})")

def check_existing_files():
    """檢查現有檔案"""
    data_dir = os.path.join('finlab_ml_course', 'history', 'tables')
    
    if not os.path.exists(data_dir):
        print("資料目錄不存在")
        return
    
    print("\n現有檔案狀態:")
    print("-" * 50)
    
    for filename in os.listdir(data_dir):
        if filename.endswith('.pkl'):
            filepath = os.path.join(data_dir, filename)
            try:
                with open(filepath, 'rb') as f:
                    data = pickle.load(f)
                
                if hasattr(data, 'index') and len(data.index) > 0:
                    last_date = data.index.max()
                    if hasattr(last_date, 'strftime'):
                        last_date_str = last_date.strftime('%Y-%m-%d')
                    else:
                        last_date_str = str(last_date)
                    
                    print(f"{filename:25} | 最後日期: {last_date_str} | 資料筆數: {len(data)}")
                else:
                    print(f"{filename:25} | 無法確定日期")
                    
            except Exception as e:
                print(f"{filename:25} | 讀取錯誤: {str(e)}")

if __name__ == "__main__":
    print("=== Finlab GUI 測試工具 ===")
    
    choice = input("\n選擇操作:\n1. 創建測試資料\n2. 檢查現有檔案\n3. 啟動GUI\n請輸入選項 (1-3): ")
    
    if choice == "1":
        create_test_data()
        print("\n測試資料創建完成！")
        
    elif choice == "2":
        check_existing_files()
        
    elif choice == "3":
        print("啟動GUI...")
        os.system("python finlab_crawler_gui.py")
        
    else:
        print("無效選項")
