#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
統一股票名稱映射模組
參考 price 爬蟲的動態更新機制，為所有爬蟲提供統一的股票名稱服務
"""

import os
import time
import requests
import re
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 股票資訊緩存（避免重複獲取）
_stock_info_cache = None
_cache_timestamp = None

def random_sleep(min_seconds, max_seconds):
    """隨機延遲，參照 HL.py 的頻率控制機制"""
    import random
    sleep_time = random.uniform(min_seconds, max_seconds)
    time.sleep(sleep_time)

def fetch_stock_info():
    """獲取股票的完整資訊 - 參照 price 爬蟲的機制"""
    print("📡 開始獲取股票資訊...")
    
    # 設置重試策略
    try:
        # 新版本 urllib3
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            backoff_factor=1
        )
    except TypeError:
        # 舊版本 urllib3
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=1
        )
    
    session = requests.Session()
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    session.mount("http://", adapter)

    stock_info = {}

    try:
        # 獲取上市股票資訊
        print("📡 獲取上市股票資訊...")
        random_sleep(3, 6)  # 初始延遲
        
        for attempt in range(3):
            try:
                url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=2"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
                
                response = session.get(url, headers=headers, timeout=30, verify=False)
                response.encoding = 'big5'
                
                if response.status_code == 200:
                    # 解析上市股票資訊 - 包含股票、ETF和債券
                    # 修改正則表達式以捕獲更多類型的證券
                    patterns = [
                        # 一般股票格式
                        r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上市</td><td[^>]*>([^<]*)</td>',
                        # ETF格式 (包含字母)
                        r'<td bgcolor=#FAFAD2>(\d{4}[A-Z]?)　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上市</td><td[^>]*>([^<]*)</td>',
                        # 債券格式 (包含B)
                        r'<td bgcolor=#FAFAD2>(\d{5}B)　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上市</td><td[^>]*>([^<]*)</td>'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, response.text)
                        for stock_code, stock_name, industry in matches:
                            if stock_code:  # 移除只檢查數字的限制
                                # 清理股票名稱
                                clean_name = stock_name.strip()
                                # 判斷證券類型
                                if stock_code.endswith('B'):
                                    security_type = '債券'
                                elif len(stock_code) == 4 and stock_code.startswith('00'):
                                    security_type = 'ETF'
                                else:
                                    security_type = '股票'

                                stock_info[stock_code] = {
                                    'stock_name': clean_name,
                                    'listing_status': '上市',
                                    'industry': industry.strip() if industry.strip() else security_type,
                                    'security_type': security_type
                                }

                    print(f"✅ 上市證券: {len([k for k, v in stock_info.items() if v['listing_status'] == '上市'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上市股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    random_sleep(10, 15)  # 失敗後等待重試

        # 獲取上櫃股票資訊
        print("📡 獲取上櫃股票資訊...")
        random_sleep(5, 8)  # 請求間隔
        
        for attempt in range(3):
            try:
                url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=4"
                response = session.get(url, headers=headers, timeout=30, verify=False)
                response.encoding = 'big5'
                
                if response.status_code == 200:
                    # 解析上櫃股票資訊 - 包含股票、ETF和債券
                    patterns = [
                        # 一般股票格式
                        r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上櫃</td><td[^>]*>([^<]*)</td>',
                        # ETF格式 (包含字母)
                        r'<td bgcolor=#FAFAD2>(\d{4}[A-Z]?)　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上櫃</td><td[^>]*>([^<]*)</td>',
                        # 債券格式 (包含B)
                        r'<td bgcolor=#FAFAD2>(\d{5}B)　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上櫃</td><td[^>]*>([^<]*)</td>'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, response.text)
                        for stock_code, stock_name, industry in matches:
                            if stock_code:  # 移除只檢查數字的限制
                                # 清理股票名稱
                                clean_name = stock_name.strip()
                                # 判斷證券類型
                                if stock_code.endswith('B'):
                                    security_type = '債券'
                                elif len(stock_code) == 4 and stock_code.startswith('00'):
                                    security_type = 'ETF'
                                else:
                                    security_type = '股票'

                                stock_info[stock_code] = {
                                    'stock_name': clean_name,
                                    'listing_status': '上櫃',
                                    'industry': industry.strip() if industry.strip() else security_type,
                                    'security_type': security_type
                                }

                    print(f"✅ 上櫃證券: {len([k for k, v in stock_info.items() if v['listing_status'] == '上櫃'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上櫃股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)  # 失敗後等待 10 秒重試

        print(f"📊 總計獲取股票資訊: {len(stock_info)} 檔股票")
        return stock_info

    except Exception as e:
        print(f"⚠️ 獲取股票資訊失敗: {e}")
        return {}
    finally:
        session.close()

def get_cached_stock_info():
    """獲取緩存的股票資訊，如果緩存過期則重新獲取"""
    global _stock_info_cache, _cache_timestamp
    
    # 緩存有效期：24 小時
    cache_duration = 24 * 60 * 60  # 24 hours in seconds
    current_time = time.time()
    
    # 檢查緩存是否有效
    if (_stock_info_cache is None or
        _cache_timestamp is None or
        current_time - _cache_timestamp > cache_duration):
        
        print("📊 股票資訊緩存已過期，重新獲取...")
        _stock_info_cache = fetch_stock_info()
        _cache_timestamp = current_time
    else:
        print(f"📊 使用緩存的股票資訊: {len(_stock_info_cache)} 檔股票")
    
    return _stock_info_cache

def get_stock_name(stock_id):
    """獲取股票名稱"""
    stock_info = get_cached_stock_info()

    # 清理股票代碼（移除可能的名稱部分）
    clean_stock_id = str(stock_id).split()[0] if stock_id else ''

    if clean_stock_id in stock_info:
        return stock_info[clean_stock_id]['stock_name']

    # 如果網路獲取失敗，嘗試從本地資料庫獲取
    db_name = get_stock_name_from_database(clean_stock_id)
    if db_name and db_name != f'股票{clean_stock_id}':
        return db_name

    # 使用內建映射表作為備用
    builtin_name = get_builtin_stock_name(clean_stock_id)
    if builtin_name != f'股票{clean_stock_id}':
        return builtin_name

    # 最後的預設格式
    return f'股票{clean_stock_id}'

def get_full_stock_id(stock_id):
    """獲取完整的股票代碼（代碼 + 名稱）"""
    stock_name = get_stock_name(stock_id)
    clean_stock_id = str(stock_id).split()[0] if stock_id else ''
    return f'{clean_stock_id} {stock_name}'

def get_stock_info(stock_id):
    """獲取股票的完整資訊"""
    stock_info = get_cached_stock_info()
    clean_stock_id = str(stock_id).split()[0] if stock_id else ''
    
    return stock_info.get(clean_stock_id, {
        'stock_name': f'股票{clean_stock_id}',
        'listing_status': '',
        'industry': ''
    })

def update_stock_names_in_dataframe(df, stock_id_column='stock_id'):
    """為 DataFrame 添加股票名稱欄位"""
    if stock_id_column not in df.columns:
        print(f"⚠️ DataFrame 中找不到 {stock_id_column} 欄位")
        return df
    
    stock_info = get_cached_stock_info()
    
    # 添加股票名稱欄位
    df['stock_name'] = df[stock_id_column].apply(
        lambda x: stock_info.get(str(x).split()[0], {}).get('stock_name', f'股票{x}')
    )
    
    print(f"✅ 已為 {len(df)} 筆資料添加股票名稱")
    return df

# 內建股票名稱映射表（作為備用）
BUILTIN_STOCK_NAMES = {
    # 主要股票
    '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
    '2412': '中華電', '2881': '富邦金', '2882': '國泰金',
    '2886': '兆豐金', '2891': '中信金', '2892': '第一金',
    '2303': '聯電', '2308': '台達電', '2002': '中鋼',
    '1301': '台塑', '1303': '南亞', '2207': '和泰車',
    '1104': '環泥', '1108': '幸福', '1109': '信大',
    '1203': '味王', '9958': '世紀鋼', '9962': '有益',

    # ETF
    '0050': '元大台灣50', '0051': '元大中型100', '0052': '富邦科技',
    '0053': '元大電子', '0054': '元大台商50', '0055': '元大MSCI金融',
    '0056': '元大高股息', '0057': '富邦摩台', '0058': '富邦發達',
    '0059': '富邦金融', '0061': '元大寶滬深', '00662': '富邦NASDAQ',
    '00703': '台新MSCI中國', '00735': '國泰台灣5G+',
    '006201': '元大富櫃50', '006203': '元大MSCI台灣',
    '006204': '永豐台灣加權', '006205': '富邦上証', '006206': '元大上証50',
    '006207': 'FH滬深', '006208': '富邦台50',

    # 債券ETF (常見的)
    '00679B': '元大美債20年', '00687B': '國泰20年美債',
    '00694B': '富邦美債1-3', '00696B': '國泰A級公司債',
    '00697B': '元大美債7-10', '00698B': '元大美債3-7',
    '00699B': '群益15年IG債', '00700B': '富邦美債7-10',
    '00720B': '元大20年美債', '00723B': '群益15年美債',
    '00726B': '國泰A級公司債', '00740B': '富邦15年美債',
    '00746B': '富邦A級公司債', '00751B': '元大AAA公司債',
    '00756B': '元大BBB公司債', '00761B': '國泰15年美債',
    '00768B': '復華15年美債', '00772B': '中信15年美債',
    '00773B': '中信BBB公司債'
}

def get_stock_name_from_database(stock_id):
    """從本地資料庫獲取股票名稱（備用方案）"""
    import sqlite3
    import os

    # 可能的資料庫路徑
    db_paths = [
        'D:/Finlab/history/tables/price.db',
        'D:/Finlab/history/tables/newprice.db',
        'D:/Finlab/history/tables/pe_data.db'
    ]

    clean_stock_id = str(stock_id).split()[0] if stock_id else ''

    for db_path in db_paths:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # 檢查可能的表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                for table in tables:
                    try:
                        # 檢查表格結構
                        cursor.execute(f"PRAGMA table_info({table})")
                        columns = [col[1] for col in cursor.fetchall()]

                        if 'stock_id' in columns and 'stock_name' in columns:
                            cursor.execute(f"""
                                SELECT stock_name FROM {table}
                                WHERE stock_id = ? AND stock_name IS NOT NULL
                                AND stock_name != '' AND stock_name NOT LIKE 'ETF%'
                                AND stock_name NOT LIKE '股票%'
                                LIMIT 1
                            """, (clean_stock_id,))

                            result = cursor.fetchone()
                            if result:
                                conn.close()
                                return result[0]
                    except:
                        continue

                conn.close()
            except:
                continue

    return f'股票{clean_stock_id}'

def get_builtin_stock_name(stock_id):
    """使用內建映射表獲取股票名稱（備用方案）"""
    clean_stock_id = str(stock_id).split()[0] if stock_id else ''
    return BUILTIN_STOCK_NAMES.get(clean_stock_id, f'股票{clean_stock_id}')

if __name__ == "__main__":
    # 測試功能
    print("🧪 測試股票名稱映射模組")
    
    # 測試獲取股票資訊
    stock_info = get_cached_stock_info()
    print(f"📊 獲取到 {len(stock_info)} 檔股票資訊")
    
    # 測試幾個股票
    test_stocks = ['2330', '2317', '2454', '1234']
    for stock_id in test_stocks:
        name = get_stock_name(stock_id)
        full_id = get_full_stock_id(stock_id)
        print(f"  {stock_id} → {name} → {full_id}")
