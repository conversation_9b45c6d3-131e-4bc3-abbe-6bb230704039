#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版圖表橫軸日期顯示修復
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class EnhancedChartTestWindow(QMainWindow):
    """增強版圖表橫軸日期顯示修復測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📈 增強版圖表橫軸日期顯示修復測試")
        self.setGeometry(100, 100, 950, 750)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("📈 增強版圖表橫軸日期顯示修復測試")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff6b35; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 功能說明
        info_text = QTextEdit()
        info_text.setMaximumHeight(320)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3 style="color: #ffffff;">🔧 增強版修復內容</h3>
        
        <h4 style="color: #cccccc;">🚀 第二輪修復重點</h4>
        <ul>
            <li><b>強制日期軸設置</b> - 完全重新創建圖表確保日期軸正常工作</li>
            <li><b>軸類型驗證</b> - 驗證日期軸是否正確設置為 DateAxisItem</li>
            <li><b>強制標籤顯示</b> - 無論日期軸是否成功都強制設置日期標籤</li>
            <li><b>軸高度調整</b> - 增加軸高度以容納完整的日期標籤</li>
            <li><b>強制重繪</b> - 確保圖表更新顯示最新設置</li>
        </ul>
        
        <h4 style="color: #cccccc;">⚡ 技術改進</h4>
        <ul>
            <li><b>圖表重建</b> - chart_widget.clear() + setAxisItems({})</li>
            <li><b>軸驗證</b> - isinstance(bottom_axis, pg.DateAxisItem)</li>
            <li><b>標籤強化</b> - 清除舊刻度 + 設置新標籤 + 強制顯示</li>
            <li><b>樣式增強</b> - setHeight(60) + setPen + setTextPen</li>
            <li><b>更新機制</b> - chart_widget.update() 強制重繪</li>
        </ul>
        
        <h4 style="color: #cccccc;">🎯 預期效果</h4>
        <ul>
            <li><b>歷史指數圖表</b> - 橫軸必須顯示日期 (2025-07-01 格式)</li>
            <li><b>日期標籤密度</b> - 根據數據量智能調整 (8-15個標籤)</li>
            <li><b>標籤清晰度</b> - 白色字體 + 增加軸高度避免重疊</li>
            <li><b>容錯機制</b> - 即使 DateAxisItem 失敗也能顯示日期</li>
            <li><b>強制顯示</b> - 所有設置都有強制執行機制</li>
        </ul>
        
        <h3 style="color: #ffffff;">🧪 重點測試項目</h3>
        
        <p style="color: #ff6b35; font-weight: bold;">
        <b>關鍵測試步驟：</b><br>
        1. 開啟台股爬蟲界面<br>
        2. 切換到「圖表分析」分頁<br>
        3. 選擇「歷史指數資料」<br>
        4. 點擊「生成圖表」<br>
        5. <span style="color: #00ff00;">重點觀察橫軸是否顯示日期標籤</span><br>
        6. 檢查控制台輸出的日期設置信息<br>
        7. 測試不同圖表類型 (線圖/柱狀圖/面積圖)
        </p>
        
        <h4 style="color: #cccccc;">🔍 調試信息</h4>
        <p style="color: #cccccc;">
        修復後的系統會在控制台輸出詳細的調試信息：<br>
        • "✅ 日期軸設置成功並驗證" 或 "❌ 日期軸設置失敗"<br>
        • "✅ 強制設置了 X 個日期標籤"<br>
        • "📅 日期範圍: YYYY-MM-DD 到 YYYY-MM-DD"<br>
        • "✅ 底部軸顯示設置完成"
        </p>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟台股爬蟲 (測試增強版日期顯示修復)")
        test_btn.clicked.connect(self.open_crawler_dialog)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 成功提示
        success_label = QLabel("🔥 增強版修復已實施！強制日期軸設置 + 備用標籤機制 + 軸高度調整")
        success_label.setStyleSheet("color: #ff6b35; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #4a2d1a; border: 1px solid #ff6b35; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 初始化日誌
        self.log("🔥 增強版圖表橫軸日期顯示修復測試已啟動")
        self.log("⚡ 強制日期軸設置 - 完全重建圖表確保日期軸工作")
        self.log("⚡ 軸類型驗證 - 確認 DateAxisItem 正確設置")
        self.log("⚡ 強制標籤顯示 - 無論如何都要顯示日期標籤")
        self.log("⚡ 軸高度調整 - 增加到60px容納完整日期")
        self.log("⚡ 強制重繪機制 - 確保所有設置生效")
    
    def log(self, message):
        """添加日誌"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
    
    def get_timestamp(self):
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def open_crawler_dialog(self):
        """開啟爬蟲對話框"""
        try:
            self.log("🚀 正在開啟台股爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("🔥 請重點測試以下功能：")
            self.log("   • 切換到「圖表分析」分頁")
            self.log("   • 選擇「歷史指數資料」")
            self.log("   • 點擊「生成圖表」按鈕")
            self.log("   • 🔍 重點觀察橫軸是否顯示日期")
            self.log("   • 📊 檢查控制台的調試輸出")
            self.log("   • 🎯 測試不同的圖表類型")
            
            # 顯示對話框
            dialog.exec()
            
            self.log("✅ 爬蟲對話框已關閉")
            
        except Exception as e:
            self.log(f"❌ 開啟爬蟲界面失敗: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("增強版圖表橫軸日期顯示修復測試")
    app.setApplicationVersion("2.0")
    
    print("🔥 增強版圖表橫軸日期顯示修復測試已啟動")
    print("⚡ 強制日期軸設置 - 完全重建圖表")
    print("⚡ 軸類型驗證 - 確認 DateAxisItem")
    print("⚡ 強制標籤顯示 - 無論如何都顯示日期")
    print("⚡ 軸高度調整 - 60px 容納完整日期")
    print("⚡ 強制重繪機制 - 確保設置生效")
    print("🎯 現在橫軸應該能正確顯示日期了！")
    
    # 創建主窗口
    window = EnhancedChartTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
