#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置 financial_data.db 並從 2018-01-01 開始重新爬取
"""

import os
import sys
import datetime

def reset_financial_data():
    """重置 financial_data.db"""
    
    print("=" * 80)
    print("🔄 重置 financial_data.db")
    print("=" * 80)
    
    db_path = 'D:/Finlab/history/tables/financial_data.db'
    
    # 刪除現有的 financial_data.db
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            print(f"✅ 已刪除現有的 financial_data.db: {db_path}")
        except Exception as e:
            print(f"❌ 刪除 financial_data.db 失敗: {e}")
            return False
    else:
        print(f"ℹ️ financial_data.db 不存在，無需刪除")
    
    # 檢查並刪除 date_range.pickle 中的 financial_data 記錄
    date_range_file = 'history/date_range.pickle'
    if os.path.exists(date_range_file):
        try:
            import pickle
            
            # 讀取現有記錄
            with open(date_range_file, 'rb') as f:
                dates_record = pickle.load(f)
            
            # 移除 financial_data 記錄
            if 'financial_data' in dates_record:
                del dates_record['financial_data']
                print(f"✅ 已移除 financial_data 的日期範圍記錄")
                
                # 保存更新後的記錄
                with open(date_range_file, 'wb') as f:
                    pickle.dump(dates_record, f)
                print(f"✅ 已更新日期範圍記錄檔案")
            else:
                print(f"ℹ️ 日期範圍記錄中沒有 financial_data")
                
        except Exception as e:
            print(f"⚠️ 處理日期範圍記錄失敗: {e}")
    else:
        print(f"ℹ️ 日期範圍記錄檔案不存在")
    
    print(f"\n✅ financial_data 重置完成")
    print(f"💡 現在可以執行: python auto_update.py financial_data")
    print(f"💡 系統將從 2018-01-01 開始完整爬取財務資料")
    
    return True

def main():
    """主函數"""
    
    print("🔄 financial_data 重置工具")
    
    # 確認操作
    response = input("\n⚠️ 這將刪除現有的 financial_data.db 檔案，確定要繼續嗎？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 執行重置
    success = reset_financial_data()
    
    if success:
        print(f"\n🎉 重置完成！")
        
        # 詢問是否立即開始爬取
        response = input("\n💡 是否立即開始從 2018-01-01 爬取財務資料？(y/N): ")
        if response.lower() == 'y':
            print(f"\n🚀 開始執行財務資料爬蟲...")
            os.system("python auto_update.py financial_data")
    else:
        print(f"\n❌ 重置失敗")

if __name__ == "__main__":
    main()
