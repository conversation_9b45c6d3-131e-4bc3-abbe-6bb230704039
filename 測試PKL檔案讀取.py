#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試PKL檔案讀取功能
"""

import pandas as pd
import os

def test_pkl_file_reading():
    """測試PKL檔案讀取"""
    print("=" * 60)
    print("🔍 測試月營收PKL檔案讀取")
    print("=" * 60)
    
    # 檢查PKL檔案
    pkl_files = [
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl'
    ]
    
    for pkl_path in pkl_files:
        print(f"\n📁 檢查檔案: {pkl_path}")
        
        if not os.path.exists(pkl_path):
            print(f"❌ 檔案不存在")
            continue
        
        print(f"✅ 檔案存在")
        
        try:
            # 讀取PKL檔案
            df = pd.read_pickle(pkl_path)
            print(f"✅ 成功讀取PKL檔案")
            print(f"📊 資料形狀: {df.shape}")
            print(f"📋 索引類型: {type(df.index)}")
            
            # 檢查索引結構
            if isinstance(df.index, pd.MultiIndex):
                print(f"📋 多層索引: {df.index.names}")
                df_reset = df.reset_index()
            else:
                print(f"📋 單層索引: {df.index.name}")
                df_reset = df.copy()
            
            print(f"📋 欄位: {list(df_reset.columns)}")
            
            # 檢查前幾筆資料
            print(f"\n📊 前3筆資料:")
            print(df_reset.head(3).to_string())
            
            # 測試特定股票查詢
            print(f"\n🔍 測試股票查詢:")
            test_stocks = ['2330', '2317', '8021', '2429']
            
            for stock_code in test_stocks:
                print(f"\n  🔍 查詢 {stock_code}:")
                
                if 'stock_id' in df_reset.columns:
                    # 查找包含股票代碼的記錄
                    stock_mask = df_reset['stock_id'].astype(str).str.contains(stock_code, na=False)
                    stock_df = df_reset[stock_mask].copy()
                    
                    if not stock_df.empty:
                        print(f"    ✅ 找到 {len(stock_df)} 筆記錄")
                        
                        # 按日期排序，取最新的資料
                        if 'date' in stock_df.columns:
                            stock_df = stock_df.sort_values('date', ascending=False)
                        
                        latest_row = stock_df.iloc[0]
                        
                        print(f"    股票ID: {latest_row.get('stock_id', 'N/A')}")
                        print(f"    日期: {latest_row.get('date', 'N/A')}")
                        print(f"    當月營收: {latest_row.get('當月營收', 'N/A')}")
                        print(f"    上月營收: {latest_row.get('上月營收', 'N/A')}")
                        print(f"    去年同月營收: {latest_row.get('去年當月營收', 'N/A')}")
                        
                        # 計算成長率
                        try:
                            current = latest_row.get('當月營收')
                            last_year = latest_row.get('去年當月營收')
                            last_month = latest_row.get('上月營收')
                            
                            if current and last_year and float(last_year) != 0:
                                yoy_rate = ((float(current) - float(last_year)) / float(last_year) * 100)
                                print(f"    YoY%: {yoy_rate:+.2f}%")
                            
                            if current and last_month and float(last_month) != 0:
                                mom_rate = ((float(current) - float(last_month)) / float(last_month) * 100)
                                print(f"    MoM%: {mom_rate:+.2f}%")
                        except (ValueError, TypeError) as e:
                            print(f"    計算成長率失敗: {e}")
                    else:
                        print(f"    ❌ 未找到記錄")
                else:
                    print(f"    ❌ 沒有 stock_id 欄位")
            
            # 如果成功讀取到資料，就不需要檢查其他檔案了
            print(f"\n✅ {pkl_path} 讀取成功，資料完整")
            return True
            
        except Exception as e:
            print(f"❌ 讀取PKL檔案失敗: {e}")
            continue
    
    print(f"\n❌ 所有PKL檔案都無法讀取")
    return False

def test_stock_data_query_function():
    """測試股票資料查詢函數（模擬主程式中的函數）"""
    print("\n" + "=" * 60)
    print("🧪 測試股票資料查詢函數")
    print("=" * 60)
    
    def get_stock_data_from_database(stock_code, stock_name):
        """從PKL檔案查詢特定股票的月營收資料"""
        try:
            print(f"🔍 查詢股票 {stock_code} {stock_name}")
            
            # 嘗試從PKL檔案讀取
            pkl_files = [
                'history/tables/monthly_report_new.pkl',
                'history/tables/monthly_report.pkl'
            ]
            
            for pkl_path in pkl_files:
                if os.path.exists(pkl_path):
                    print(f"  📁 嘗試讀取: {pkl_path}")
                    try:
                        df = pd.read_pickle(pkl_path)
                        
                        # 重置索引
                        if isinstance(df.index, pd.MultiIndex):
                            df = df.reset_index()
                        
                        # 查找指定股票的最新資料
                        if 'stock_id' in df.columns:
                            stock_mask = df['stock_id'].astype(str).str.contains(stock_code, na=False)
                            stock_df = df[stock_mask].copy()
                            
                            if not stock_df.empty:
                                # 按日期排序，取最新的資料
                                if 'date' in stock_df.columns:
                                    stock_df = stock_df.sort_values('date', ascending=False)
                                
                                latest_row = stock_df.iloc[0]
                                
                                # 提取資料
                                stock_id = latest_row.get('stock_id', stock_code)
                                date = latest_row.get('date', 'N/A')
                                current_revenue = latest_row.get('當月營收', None)
                                last_month_revenue = latest_row.get('上月營收', None)
                                last_year_revenue = latest_row.get('去年當月營收', None)
                                
                                # 處理股票名稱
                                if ' ' in str(stock_id):
                                    parts = str(stock_id).split(' ')
                                    db_stock_name = parts[1] if len(parts) > 1 else stock_name
                                else:
                                    db_stock_name = stock_name
                                
                                # 計算成長率
                                yoy_rate = "N/A"
                                mom_rate = "N/A"
                                
                                try:
                                    if current_revenue and last_year_revenue and float(last_year_revenue) != 0:
                                        yoy_rate = f"{((float(current_revenue) - float(last_year_revenue)) / float(last_year_revenue) * 100):+.2f}%"
                                except (ValueError, TypeError):
                                    pass
                                
                                try:
                                    if current_revenue and last_month_revenue and float(last_month_revenue) != 0:
                                        mom_rate = f"{((float(current_revenue) - float(last_month_revenue)) / float(last_month_revenue) * 100):+.2f}%"
                                except (ValueError, TypeError):
                                    pass
                                
                                # 構建股票資料
                                stock_data = {
                                    '排名': 'N/A',
                                    '股票代碼': stock_code,
                                    '股票名稱': db_stock_name,
                                    '西元年月': str(date)[:7].replace('-', '') if date and str(date) != 'N/A' else 'N/A',
                                    '當月營收': f"{float(current_revenue):,.0f}" if current_revenue and str(current_revenue) != 'nan' else "N/A",
                                    '上個月營收': f"{float(last_month_revenue):,.0f}" if last_month_revenue and str(last_month_revenue) != 'nan' else "N/A",
                                    '去年同月營收': f"{float(last_year_revenue):,.0f}" if last_year_revenue and str(last_year_revenue) != 'nan' else "N/A",
                                    'YoY%': yoy_rate,
                                    'MoM%': mom_rate,
                                    '殖利率': 'N/A',
                                    '本益比': 'N/A',
                                    '股價淨值比': 'N/A',
                                    'EPS': 'N/A',
                                    '綜合評分': 'N/A'
                                }
                                
                                print(f"  ✅ 成功獲取資料")
                                return stock_data
                    
                    except Exception as e:
                        print(f"  ❌ 讀取失敗: {e}")
                        continue
            
            print(f"  ❌ 未找到資料")
            return None
            
        except Exception as e:
            print(f"  ❌ 查詢失敗: {e}")
            return None
    
    # 測試多個股票
    test_stocks = [
        ('2330', '台積電'),
        ('2317', '鴻海'),
        ('8021', '尖點'),
        ('2429', '銘旺科')
    ]
    
    for stock_code, stock_name in test_stocks:
        print(f"\n🔍 測試 {stock_code} {stock_name}:")
        stock_data = get_stock_data_from_database(stock_code, stock_name)
        
        if stock_data:
            print(f"  股票代碼: {stock_data['股票代碼']}")
            print(f"  股票名稱: {stock_data['股票名稱']}")
            print(f"  西元年月: {stock_data['西元年月']}")
            print(f"  當月營收: {stock_data['當月營收']} 千元")
            print(f"  上個月營收: {stock_data['上個月營收']} 千元")
            print(f"  去年同月營收: {stock_data['去年同月營收']} 千元")
            print(f"  YoY%: {stock_data['YoY%']}")
            print(f"  MoM%: {stock_data['MoM%']}")
            
            # 檢查是否有N/A值（除了預期的欄位）
            na_fields = []
            for key, value in stock_data.items():
                if key not in ['排名', '殖利率', '本益比', '股價淨值比', 'EPS', '綜合評分'] and str(value) == 'N/A':
                    na_fields.append(key)
            
            if na_fields:
                print(f"  ⚠️ 有N/A值的欄位: {na_fields}")
            else:
                print(f"  ✅ 營收資料完整，無N/A值")
        else:
            print(f"  ❌ 查詢失敗")

def main():
    """主函數"""
    print("🚀 月營收PKL檔案讀取測試")
    
    # 測試PKL檔案讀取
    if test_pkl_file_reading():
        print("\n✅ PKL檔案讀取測試通過")
        
        # 測試查詢函數
        test_stock_data_query_function()
        
        print("\n" + "=" * 60)
        print("🎉 測試完成！")
        print("✅ PKL檔案讀取功能正常")
        print("✅ 可以獲取真實的月營收資料")
        print("✅ 成長率計算正確")
        print("=" * 60)
        
    else:
        print("\n❌ PKL檔案讀取測試失敗")
        print("請檢查PKL檔案是否存在和資料是否完整")

if __name__ == "__main__":
    main()
