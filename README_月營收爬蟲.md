# 月營收爬蟲使用說明

## 🎯 功能
獨立的月營收爬蟲程式，專門更新 `monthly_report.pkl` 檔案

## 🚀 快速使用

### 1. 自動更新最新月份
```bash
python monthly_revenue_crawler.py
```

### 2. 指定月份更新
```bash
python monthly_revenue_crawler.py --year 2024 --month 6
```

### 3. 測試模式（不保存）
```bash
python monthly_revenue_crawler.py --test-only
```

## 📊 程式特色

✅ **自動判斷月份** - 根據當前日期自動選擇應該更新的月份
- 每月10日前：更新上上個月資料
- 每月10日後：更新上個月資料

✅ **完整覆蓋** - 爬取所有類型公司
- 上市國內 + 上市國外
- 上櫃國內 + 上櫃國外

✅ **安全機制**
- 自動備份原始檔案
- 智能跳過已存在資料
- 詳細進度顯示

## 📈 執行結果範例

```
📊 獨立月營收爬蟲程式
🎯 目標: 更新 monthly_report.pkl 檔案
🚀 開始更新月營收資料
✅ 已備份: monthly_report.pkl.backup_20250722_224500
📖 載入現有資料: 332,564 筆
📅 計劃更新月份: ['2025-06-01']
📊 爬取月營收 - 民國114年6月 (2025-06-01)
   📋 上市國內
   🔄 國內上市: https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_114_6_0.html
     ✅ 成功: 955 筆
   📋 上市國外
   🔄 國外上市: https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_114_6_1.html
     ✅ 成功: 86 筆
   📋 上櫃國內
   🔄 國內上櫃: https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_114_6_0.html
     ✅ 成功: 825 筆
   📋 上櫃國外
   🔄 國外上櫃: https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_114_6_1.html
     ✅ 成功: 29 筆
   🎉 總計: 1895 筆資料
✅ 2025-06-01: 1895 筆

📊 新爬取資料: 1,895 筆
📈 合併後總計: 334,459 筆
   原有: 332,564 筆
   新增: 1,895 筆
💾 已保存: history/tables/monthly_report.pkl (26.3 MB)
🎉 更新完成！

✅ 程式執行成功
```

## 🔧 問題排除

### 常見問題

1. **numpy 警告**
   ```
   DeprecationWarning: numpy.core is deprecated
   ```
   **說明**: 這是警告訊息，不影響程式運行

2. **找不到表格**
   ```
   ❌ 失敗: No tables found
   ```
   **原因**: 該月份資料可能還沒公布
   **解決**: 使用較早的月份，如 `--year 2024 --month 6`

3. **IP被封鎖**
   ```
   ⚠️ IP被封鎖
   ```
   **解決**: 等待一段時間後重試

## 📁 檔案說明

- `monthly_revenue_crawler.py` - 主程式
- `history/tables/monthly_report.pkl` - 主要資料檔案
- `history/tables/monthly_report.pkl.backup_*` - 自動備份檔案

## 💡 使用建議

1. **定期執行**: 建議每月15日左右執行一次
2. **先測試**: 使用 `--test-only` 先確認資料正確
3. **檢查備份**: 定期清理舊的備份檔案

現在您可以輕鬆維護最新的月營收資料！🎉
