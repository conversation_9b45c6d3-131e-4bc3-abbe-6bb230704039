#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試主GUI股票篩選功能
"""

import sys
import logging
import pandas as pd
from unittest.mock import Mock, patch

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_stock_filter_integration():
    """測試主GUI中股票篩選功能的整合"""
    print("🔍 測試主GUI股票篩選功能整合")
    print("=" * 50)
    
    try:
        # 模擬股票資料（包含0054等已下市股票）
        mock_stock_data = pd.DataFrame({
            'stock_id': [
                '0050', '0051', '0052', '0053', '0054', '0055', '0056',  # ETF
                '2330', '2317', '2454', '2412',  # 正常股票
                '1724', '5820',  # 已下市股票
                '030001', '020011'  # 權證、ETN
            ],
            'stock_name': [
                '元大台灣50', '元大中型100', '富邦科技', '元大電子', '元大台商50', '元大MSCI金融', '元大高股息',
                '台積電', '鴻海', '聯發科', '中華電',
                '台硝', '日盛金',
                '權證001', 'ETN011'
            ],
            'listing_status': [
                '上市', '上市', '上市', '上市', '已下市', '上市', '上市',
                '上市', '上市', '上市', '上市',
                '已下市', '已下市',
                '權證', 'ETN'
            ],
            'industry': [
                'ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF',
                '半導體業', '電子業', '半導體業', '通信網路業',
                '化學工業', '金融保險業',
                '權證', 'ETN'
            ]
        })
        
        # 測試股票篩選器
        from stock_filter import StockFilter
        filter = StockFilter()
        
        print("📊 測試股票清單篩選:")
        print("-" * 50)
        
        # 模擬篩選過程
        original_stocks = mock_stock_data['stock_id'].tolist()
        valid_stocks = filter.filter_stock_list(original_stocks)
        
        print(f"原始股票數量: {len(original_stocks)}")
        print(f"篩選後數量: {len(valid_stocks)}")
        print(f"排除數量: {len(original_stocks) - len(valid_stocks)}")
        
        # 檢查特定股票的篩選結果
        test_cases = [
            ('0050', True, '元大台灣50 - 正常ETF'),
            ('0054', False, '元大台商50 - 已下市ETF'),
            ('2330', True, '台積電 - 正常股票'),
            ('1724', False, '台硝 - 已下市股票'),
            ('5820', False, '日盛金 - 已下市股票'),
            ('030001', False, '權證 - 衍生性商品'),
            ('020011', False, 'ETN - 衍生性商品')
        ]
        
        print(f"\n🔍 個別股票篩選測試:")
        print("-" * 50)
        
        all_passed = True
        for stock_code, expected, description in test_cases:
            is_valid = filter.is_valid_stock_code(stock_code)
            status = "✅ 通過" if is_valid == expected else "❌ 失敗"
            result = "保留" if is_valid else "排除"
            
            print(f"{stock_code:<8}: {result:<4} - {description:<25} {status}")
            
            if is_valid != expected:
                all_passed = False
        
        # 檢查篩選後的清單
        print(f"\n📋 篩選後的股票清單:")
        print("-" * 50)
        
        filtered_data = mock_stock_data[mock_stock_data['stock_id'].isin(valid_stocks)]
        for _, row in filtered_data.iterrows():
            print(f"{row['stock_id']} {row['stock_name']} ({row['listing_status']}/{row['industry']})")
        
        # 檢查被排除的股票
        excluded_stocks = [stock for stock in original_stocks if stock not in valid_stocks]
        print(f"\n🚫 被排除的股票:")
        print("-" * 50)
        
        excluded_data = mock_stock_data[mock_stock_data['stock_id'].isin(excluded_stocks)]
        for _, row in excluded_data.iterrows():
            print(f"{row['stock_id']} {row['stock_name']} ({row['listing_status']}/{row['industry']})")
        
        # 驗證關鍵排除項目
        key_exclusions = ['0054', '1724', '5820', '030001', '020011']
        excluded_key_items = [stock for stock in key_exclusions if stock in excluded_stocks]
        
        print(f"\n🎯 關鍵排除項目檢查:")
        print("-" * 50)
        print(f"應排除: {key_exclusions}")
        print(f"實際排除: {excluded_key_items}")
        print(f"排除率: {len(excluded_key_items)}/{len(key_exclusions)} ({len(excluded_key_items)/len(key_exclusions)*100:.1f}%)")
        
        success = all_passed and len(excluded_key_items) == len(key_exclusions)
        
        if success:
            print(f"\n🎉 測試通過！主GUI股票篩選功能正常工作。")
            print(f"• 0054等已下市ETF已被正確排除")
            print(f"• 權證、ETN等衍生性商品已被正確排除")
            print(f"• 正常股票和ETF已被正確保留")
            return True
        else:
            print(f"\n⚠️ 測試未完全通過，請檢查篩選邏輯。")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_gui_load_stock_list_simulation():
    """模擬主GUI載入股票清單的過程"""
    print("\n🔍 模擬主GUI載入股票清單")
    print("=" * 50)
    
    try:
        # 模擬主GUI的load_stock_list方法的邏輯
        from stock_filter import StockFilter
        
        # 創建股票篩選器
        stock_filter = StockFilter()
        
        # 模擬從資料庫查詢的股票資料
        mock_df = pd.DataFrame({
            'stock_id': ['0050', '0051', '0052', '0053', '0054', '0055', '0056', '2330', '2317', '1724', '5820'],
            'stock_name': ['元大台灣50', '元大中型100', '富邦科技', '元大電子', '元大台商50', '元大MSCI金融', '元大高股息', '台積電', '鴻海', '台硝', '日盛金'],
            'listing_status': ['上市', '上市', '上市', '上市', '已下市', '上市', '上市', '上市', '上市', '已下市', '已下市'],
            'industry': ['ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF', '半導體業', '電子業', '化學工業', '金融保險業']
        })
        
        print(f"📊 模擬資料庫查詢結果:")
        print(f"原始股票數量: {len(mock_df)}")
        
        # 應用股票篩選（模擬主GUI中的邏輯）
        original_count = len(mock_df)
        valid_codes = stock_filter.filter_stock_list(mock_df['stock_id'].tolist())
        filtered_df = mock_df[mock_df['stock_id'].isin(valid_codes)]
        filtered_count = len(filtered_df)
        
        print(f"篩選後數量: {filtered_count}")
        print(f"排除數量: {original_count - filtered_count}")
        
        # 模擬all_stocks_list的填充過程
        print(f"\n📋 模擬「全部股票」清單內容:")
        print("-" * 50)
        
        stock_list_items = []
        for _, row in filtered_df.iterrows():
            stock_name = row['stock_name'] if row['stock_name'] else '未知'
            listing_status = row['listing_status'] if row['listing_status'] else ''
            industry = row['industry'] if row['industry'] else ''

            if listing_status and industry:
                item_text = f"{row['stock_id']} {stock_name} ({listing_status}/{industry})"
            elif listing_status:
                item_text = f"{row['stock_id']} {stock_name} ({listing_status})"
            elif stock_name != '未知':
                item_text = f"{row['stock_id']} {stock_name}"
            else:
                item_text = f"{row['stock_id']}"
            
            stock_list_items.append(item_text)
            print(item_text)
        
        # 檢查0054是否被排除
        has_0054 = any('0054' in item for item in stock_list_items)
        
        print(f"\n🔍 關鍵檢查:")
        print("-" * 50)
        print(f"0054是否出現在清單中: {'❌ 是（錯誤）' if has_0054 else '✅ 否（正確）'}")
        
        # 檢查其他已下市股票
        delisted_stocks = ['1724', '5820']
        delisted_in_list = [stock for stock in delisted_stocks if any(stock in item for item in stock_list_items)]
        
        print(f"已下市股票在清單中: {delisted_in_list if delisted_in_list else '✅ 無（正確）'}")
        
        success = not has_0054 and not delisted_in_list
        
        if success:
            print(f"\n🎉 模擬測試通過！")
            print(f"• 0054已下市ETF已被正確排除")
            print(f"• 其他已下市股票也已被正確排除")
            print(f"• 「全部股票」清單將只顯示有效的投資標的")
            return True
        else:
            print(f"\n⚠️ 模擬測試失敗，篩選功能可能未正確應用。")
            return False
        
    except Exception as e:
        print(f"❌ 模擬測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🚀 主GUI股票篩選功能測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("股票篩選整合", test_stock_filter_integration()))
    test_results.append(("GUI載入模擬", test_gui_load_stock_list_simulation()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！主GUI股票篩選功能已成功修正。")
        print("\n📋 修正說明:")
        print("• 主GUI的load_stock_list()方法已整合股票篩選功能")
        print("• 0054等已下市ETF將不再出現在「全部股票」清單中")
        print("• 權證、ETN等衍生性商品也將被自動排除")
        print("• 用戶將看到更乾淨、更專業的股票清單")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能的實現。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
