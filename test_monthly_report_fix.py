#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 monthly_report 修正結果
"""

import os
import sys
import datetime
import pandas as pd

def test_datetime_comparison():
    """測試日期時間比較邏輯"""
    
    print("=" * 80)
    print("🧪 測試日期時間比較邏輯")
    print("=" * 80)
    
    try:
        # 模擬 monthly_report 的邏輯
        last_date = datetime.datetime(2025, 7, 10)  # 模擬最後日期
        
        # 計算下個月開始日期
        start_update = last_date + datetime.timedelta(days=32)  # 確保跳到下個月
        start_update = start_update.replace(day=1)  # 設為該月1號
        
        print(f"📅 原始 start_update 類型: {type(start_update)}")
        print(f"📅 原始 start_update 值: {start_update}")
        
        # 確保是 datetime 類型
        if isinstance(start_update, datetime.date) and not isinstance(start_update, datetime.datetime):
            start_update = datetime.datetime.combine(start_update, datetime.time())
            print(f"✅ 轉換後 start_update 類型: {type(start_update)}")
        
        end_update = datetime.datetime.now()
        print(f"📅 end_update 類型: {type(end_update)}")
        print(f"📅 end_update 值: {end_update}")
        
        # 測試比較
        if start_update <= end_update:
            print(f"✅ 日期比較成功: {start_update} <= {end_update}")
            days_diff = (end_update - start_update).days
            print(f"📊 相差天數: {days_diff}")
            return True
        else:
            print(f"⚠️ 開始日期晚於結束日期")
            return False
            
    except Exception as e:
        print(f"❌ 日期比較測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_report_date_logic():
    """測試 monthly_report 的日期邏輯"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試 monthly_report 日期邏輯")
    print("=" * 80)
    
    try:
        # 模擬不同的最後日期情況
        test_cases = [
            datetime.datetime(2025, 7, 10),   # 7月10日
            datetime.datetime(2025, 6, 30),   # 6月30日
            datetime.datetime(2025, 12, 31),  # 12月31日
            None                              # 沒有現有資料
        ]
        
        for i, last_date in enumerate(test_cases, 1):
            print(f"\n📋 測試案例 {i}:")
            
            if last_date:
                print(f"   最後日期: {last_date.strftime('%Y-%m-%d')}")
                
                # 計算開始更新日期
                start_update = last_date + datetime.timedelta(days=32)
                start_update = start_update.replace(day=1)
                
                # 確保是 datetime 類型
                if isinstance(start_update, datetime.date) and not isinstance(start_update, datetime.datetime):
                    start_update = datetime.datetime.combine(start_update, datetime.time())
                
                end_update = datetime.datetime.now()
                
                print(f"   開始更新: {start_update.strftime('%Y-%m-%d')}")
                print(f"   結束更新: {end_update.strftime('%Y-%m-%d')}")
                
                # 測試比較
                if start_update <= end_update:
                    print(f"   ✅ 日期比較正常")
                else:
                    print(f"   ⚠️ 開始日期晚於結束日期")
            else:
                print(f"   沒有現有資料")
                start_update = datetime.datetime(2018, 1, 1)
                end_update = datetime.datetime.now()
                
                print(f"   開始更新: {start_update.strftime('%Y-%m-%d')}")
                print(f"   結束更新: {end_update.strftime('%Y-%m-%d')}")
                
                if start_update <= end_update:
                    print(f"   ✅ 日期比較正常")
                else:
                    print(f"   ❌ 日期比較異常")
        
        return True
        
    except Exception as e:
        print(f"❌ monthly_report 日期邏輯測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_update_fix():
    """測試 auto_update.py 中的修正"""
    
    print(f"\n" + "=" * 80)
    print("🔧 測試 auto_update.py 修正")
    print("=" * 80)
    
    try:
        # 檢查修正的邏輯是否存在
        with open('auto_update.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查日期類型轉換邏輯
        if "isinstance(start_update, datetime.date)" in content and "datetime.datetime.combine" in content:
            print(f"✅ 日期類型轉換邏輯已添加")
        else:
            print(f"❌ 日期類型轉換邏輯未找到")
            return False
        
        # 檢查 monthly_report 命令行支援
        if "elif sys.argv[1] == \"monthly_report\":" in content:
            print(f"✅ monthly_report 命令行支援已添加")
        else:
            print(f"❌ monthly_report 命令行支援未找到")
            return False
        
        # 檢查幫助訊息
        if "monthly_report - 執行月營收資料爬蟲" in content:
            print(f"✅ 幫助訊息已更新")
        else:
            print(f"❌ 幫助訊息未更新")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查 auto_update.py 修正失敗: {e}")
        return False

def simulate_monthly_report_update():
    """模擬 monthly_report 更新過程"""
    
    print(f"\n" + "=" * 80)
    print("🎭 模擬 monthly_report 更新過程")
    print("=" * 80)
    
    try:
        # 模擬從輸出中解析的最後日期
        last_date_str = "2025-07-10"
        last_date = datetime.datetime.strptime(last_date_str, "%Y-%m-%d")
        
        print(f"📅 模擬最後日期: {last_date.strftime('%Y-%m-%d')}")
        
        # 應用修正後的邏輯
        start_update = last_date + datetime.timedelta(days=32)
        start_update = start_update.replace(day=1)
        
        # 確保是 datetime 類型
        if isinstance(start_update, datetime.date) and not isinstance(start_update, datetime.datetime):
            start_update = datetime.datetime.combine(start_update, datetime.time())
        
        end_update = datetime.datetime.now()
        
        print(f"📅 計算的更新範圍: {start_update.strftime('%Y-%m-%d')} 至 {end_update.strftime('%Y-%m-%d')}")
        
        # 檢查是否會發生錯誤
        if start_update <= end_update:
            print(f"✅ 不會發生 'can't compare datetime.datetime to datetime.date' 錯誤")
            
            # 計算更新月數
            months_diff = (end_update.year - start_update.year) * 12 + (end_update.month - start_update.month)
            print(f"📊 需要更新的月數: {months_diff}")
            
            if months_diff <= 12:
                print(f"✅ 更新月數合理")
                return True
            else:
                print(f"⚠️ 更新月數較多")
                return True
        else:
            print(f"⚠️ 開始日期晚於結束日期")
            return False
            
    except Exception as e:
        print(f"❌ 模擬更新過程失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🧪 monthly_report 修正驗證")
    print(f"測試時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: 日期時間比較
    comparison_success = test_datetime_comparison()
    
    # 測試2: monthly_report 日期邏輯
    date_logic_success = test_monthly_report_date_logic()
    
    # 測試3: auto_update.py 修正
    fix_success = test_auto_update_fix()
    
    # 測試4: 模擬更新過程
    simulation_success = simulate_monthly_report_update()
    
    print(f"\n" + "=" * 80)
    print("📊 monthly_report 修正驗證結果")
    print("=" * 80)
    
    print(f"🧪 日期時間比較: {'✅ 成功' if comparison_success else '❌ 失敗'}")
    print(f"🔍 日期邏輯測試: {'✅ 成功' if date_logic_success else '❌ 失敗'}")
    print(f"🔧 auto_update 修正: {'✅ 成功' if fix_success else '❌ 失敗'}")
    print(f"🎭 更新過程模擬: {'✅ 成功' if simulation_success else '❌ 失敗'}")
    
    if all([comparison_success, date_logic_success, fix_success, simulation_success]):
        print(f"\n🎉 monthly_report 修正驗證全部通過！")
        print(f"💡 修正效果:")
        print(f"   ✅ 修正了日期類型比較錯誤")
        print(f"   ✅ 確保 start_update 是 datetime.datetime 類型")
        print(f"   ✅ 添加了命令行支援")
        print(f"   ✅ 更新了幫助訊息")
        
        print(f"\n🚀 現在可以安全執行:")
        print(f"   python auto_update.py monthly_report")
        print(f"   python auto_update.py")
    else:
        print(f"\n⚠️ 部分修正驗證失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
