#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試股票代碼映射問題
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_stock_mapping():
    """調試股票代碼映射"""
    
    print("=" * 60)
    print("🔍 調試股票代碼映射問題")
    print("=" * 60)
    
    try:
        from crawler import fetch_stock_info_for_price, crawl_price
        
        # 獲取股票資訊
        print("📡 獲取股票資訊...")
        stock_info = fetch_stock_info_for_price()
        
        print(f"📊 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 顯示前10個股票資訊
        print(f"\n📋 前10個股票資訊:")
        count = 0
        for stock_id, info in stock_info.items():
            if count < 10:
                print(f"   {stock_id}: {info['stock_name']} | {info['listing_status']} | {info['industry']}")
                count += 1
        
        # 檢查一些重要股票是否存在
        important_stocks = ['2330', '2317', '0050', '0056']
        print(f"\n🔍 檢查重要股票:")
        for stock_id in important_stocks:
            if stock_id in stock_info:
                info = stock_info[stock_id]
                print(f"   {stock_id}: {info['stock_name']} | {info['listing_status']} | {info['industry']} ✅")
            else:
                print(f"   {stock_id}: 未找到 ❌")
        
        # 測試爬取一天的資料
        print(f"\n📅 測試爬取 2022-08-31 的資料...")
        test_date = datetime(2022, 8, 31)
        df = crawl_price(test_date)
        
        if df is not None and not df.empty:
            df_reset = df.reset_index()
            print(f"📊 爬取到 {len(df_reset)} 筆資料")
            
            # 檢查前10筆資料的股票代碼
            print(f"\n📋 前10筆資料的股票代碼:")
            for i, row in df_reset.head(10).iterrows():
                stock_id = str(row['stock_id'])
                stock_name = row.get('stock_name', '')
                listing_status = row.get('listing_status', '')
                industry = row.get('industry', '')
                
                print(f"   {stock_id}: {stock_name} | {listing_status} | {industry}")
                
                # 檢查這個股票代碼是否在 stock_info 中
                if stock_id in stock_info:
                    expected_info = stock_info[stock_id]
                    print(f"     期望: {expected_info['stock_name']} | {expected_info['listing_status']} | {expected_info['industry']}")
                else:
                    print(f"     在 stock_info 中未找到")
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_stock_mapping()
