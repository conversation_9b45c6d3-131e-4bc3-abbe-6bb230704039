#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Google新聞資料庫查詢修正
"""

import sqlite3
import os
import sys

def test_news_database_query():
    """測試新聞資料庫查詢功能"""
    print("🔍 Google新聞資料庫查詢修正測試")
    print("=" * 60)
    
    # 測試資料庫路徑
    db_paths = [
        "D:/Finlab/history/tables/news.db",
        "./news.db",
        "news.db"
    ]
    
    for db_path in db_paths:
        print(f"\n📂 檢查資料庫: {db_path}")
        
        if not os.path.exists(db_path):
            print(f"❌ 資料庫不存在")
            continue
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📊 可用表格: {tables}")
            
            # 檢查news表格
            if 'news' in tables:
                print("\n✅ 找到news表格，檢查結構...")
                cursor.execute("PRAGMA table_info(news)")
                columns = cursor.fetchall()
                print("📋 news表格欄位:")
                for col in columns:
                    print(f"   {col[1]} ({col[2]})")
                
                # 檢查1101的新聞
                cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = '1101'")
                count = cursor.fetchone()[0]
                print(f"\n📰 1101新聞數量: {count}")
                
                if count > 0:
                    # 顯示最新的新聞
                    cursor.execute("""
                        SELECT id, search_time, search_keyword, title, source, 
                               pub_date, description, link, content, created_at
                        FROM news
                        WHERE stock_code = '1101'
                        ORDER BY created_at DESC
                        LIMIT 3
                    """)
                    
                    news_list = cursor.fetchall()
                    print("\n📋 最新3筆1101新聞:")
                    for i, news in enumerate(news_list):
                        print(f"{i+1}. 標題: {news[3]}")
                        print(f"   來源: {news[4]}")
                        print(f"   日期: {news[5]}")
                        print(f"   儲存時間: {news[9]}")
                        print()
                
                # 檢查所有股票的新聞
                cursor.execute("SELECT DISTINCT stock_code FROM news")
                stock_codes = [row[0] for row in cursor.fetchall()]
                print(f"📊 資料庫中的股票代碼: {stock_codes}")
            
            # 檢查news_content表格
            if 'news_content' in tables:
                print("\n✅ 找到news_content表格，檢查結構...")
                cursor.execute("PRAGMA table_info(news_content)")
                columns = cursor.fetchall()
                print("📋 news_content表格欄位:")
                for col in columns:
                    print(f"   {col[1]} ({col[2]})")
                
                # 檢查1101的Google新聞
                cursor.execute("SELECT COUNT(*) FROM news_content WHERE stock_code = '1101' AND news_id LIKE 'google_%'")
                count = cursor.fetchone()[0]
                print(f"\n📰 1101 Google新聞數量: {count}")
                
                if count > 0:
                    # 顯示最新的新聞
                    cursor.execute("""
                        SELECT news_id, date, time, source, title, reporter, link, 
                               search_keyword, news_summary, crawl_time
                        FROM news_content
                        WHERE stock_code = '1101' AND news_id LIKE 'google_%'
                        ORDER BY date DESC, time DESC
                        LIMIT 3
                    """)
                    
                    news_list = cursor.fetchall()
                    print("\n📋 最新3筆1101 Google新聞:")
                    for i, news in enumerate(news_list):
                        print(f"{i+1}. 標題: {news[4]}")
                        print(f"   來源: {news[3]}")
                        print(f"   日期: {news[1]}")
                        print(f"   時間: {news[2]}")
                        print()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 資料庫查詢失敗: {e}")
            import traceback
            traceback.print_exc()

def simulate_query_function():
    """模擬修正後的查詢功能"""
    print("\n🔧 模擬修正後的查詢功能...")
    
    stock_code = "1101"
    db_path = "D:/Finlab/history/tables/news.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📊 可用表格: {tables}")
        
        if 'news' in tables:
            print("✅ 使用news表格查詢...")
            # 使用新的news表格結構
            cursor.execute('''
                SELECT id, search_time, search_keyword, title, source, 
                       pub_date, description, link, content, created_at
                FROM news
                WHERE stock_code = ?
                ORDER BY created_at DESC
                LIMIT 50
            ''', (stock_code,))
            
            results = cursor.fetchall()
            
            if results:
                print(f"✅ 找到 {len(results)} 筆 {stock_code} 的Google新聞")
                
                # 顯示前3筆
                for i, news in enumerate(results[:3]):
                    print(f"\n{i+1}. 標題: {news[3]}")
                    print(f"   來源: {news[4]}")
                    print(f"   發布日期: {news[5]}")
                    print(f"   連結: {news[7]}")
                    print(f"   摘要: {news[6][:100]}..." if news[6] else "   摘要: 無")
                
            else:
                print(f"❌ 資料庫中沒有 {stock_code} 的新聞")
                
        elif 'news_content' in tables:
            print("✅ 使用news_content表格查詢...")
            # 使用舊的news_content表格結構
            cursor.execute('''
                SELECT news_id, date, time, source, title, reporter, link, 
                       search_keyword, news_summary, crawl_time
                FROM news_content
                WHERE stock_code = ? AND news_id LIKE 'google_%'
                ORDER BY date DESC, time DESC
                LIMIT 50
            ''', (stock_code,))
            
            results = cursor.fetchall()
            
            if results:
                print(f"✅ 找到 {len(results)} 筆 {stock_code} 的Google新聞")
                
                # 顯示前3筆
                for i, news in enumerate(results[:3]):
                    print(f"\n{i+1}. 標題: {news[4]}")
                    print(f"   來源: {news[3]}")
                    print(f"   日期: {news[1]}")
                    print(f"   連結: {news[6]}")
                    print(f"   摘要: {news[8][:100]}..." if news[8] else "   摘要: 無")
                    
            else:
                print(f"❌ 資料庫中沒有 {stock_code} 的Google新聞")
        else:
            print(f"❌ 找不到新聞表格，可用表格: {tables}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 Google新聞資料庫查詢修正測試")
    print("=" * 80)
    
    # 測試資料庫查詢
    test_news_database_query()
    
    # 模擬修正後的查詢功能
    simulate_query_function()
    
    print("\n" + "=" * 80)
    print("📋 修正總結:")
    print("✅ 支援新舊兩種表格結構 (news 和 news_content)")
    print("✅ 自動檢測表格類型並使用對應的查詢語句")
    print("✅ 修正欄位對應關係")
    print("✅ 改善錯誤處理和調試訊息")
    print("\n💡 現在爬取完成後應該能正確查看資料庫中的新聞了！")
    print("=" * 80)
