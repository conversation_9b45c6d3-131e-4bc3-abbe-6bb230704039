#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試語法
"""

import ast
import sys

def check_syntax(filename):
    """檢查Python文件語法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 嘗試解析語法
        ast.parse(source)
        print(f"✅ {filename} 語法檢查通過")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 語法錯誤:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   錯誤: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename} 檢查失敗: {e}")
        return False

if __name__ == "__main__":
    filename = "O3mh_gui_v21_optimized.py"
    check_syntax(filename)
