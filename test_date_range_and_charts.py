#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試日期範圍爬取和圖表功能
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QPushButton, QTextEdit, QHBoxLayout, QLabel, QDateEdit
)
from PyQt6.QtCore import Qt, QDate

class DateRangeChartTestWindow(QMainWindow):
    """測試日期範圍和圖表功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📅📈 日期範圍爬取和圖表功能測試")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_text = QTextEdit()
        title_text.setMaximumHeight(180)
        title_text.setHtml("""
        <h2>📅📈 日期範圍爬取和圖表功能測試</h2>
        <p><b>測試目標：</b></p>
        <ul>
            <li>🗓️ <b>日期範圍爬取</b> - 驗證是否能按指定日期範圍爬取數據</li>
            <li>📊 <b>圖表顯示功能</b> - 驗證圖表生成和顯示功能</li>
            <li>📈 <b>多種圖表類型</b> - 線圖、柱狀圖、面積圖</li>
            <li>🔍 <b>數據完整性</b> - 檢查爬取的數據是否涵蓋指定日期範圍</li>
        </ul>
        """)
        layout.addWidget(title_text)
        
        # 日期範圍設定
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("測試日期範圍:"))
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))  # 7天前
        self.start_date.setCalendarPopup(True)
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        date_layout.addWidget(self.start_date)
        
        date_layout.addWidget(QLabel("至"))
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())  # 今天
        self.end_date.setCalendarPopup(True)
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        date_layout.addWidget(self.end_date)
        
        date_layout.addStretch()
        layout.addLayout(date_layout)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 檢查現有數據
        check_btn = QPushButton("🔍 檢查現有數據")
        check_btn.clicked.connect(self.check_existing_data)
        check_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        button_layout.addWidget(check_btn)
        
        # 開啟台灣證交所爬蟲
        open_crawler_btn = QPushButton("🚀 開啟台灣證交所爬蟲")
        open_crawler_btn.clicked.connect(self.open_twse_crawler)
        open_crawler_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        button_layout.addWidget(open_crawler_btn)
        
        # 測試圖表功能
        chart_btn = QPushButton("📈 測試圖表功能")
        chart_btn.clicked.connect(self.test_chart_function)
        chart_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(chart_btn)
        
        layout.addLayout(button_layout)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🚀 日期範圍爬取和圖表功能測試程式已啟動")
        self.log("📋 請按照以下步驟進行測試：")
        self.log("1. 設定測試日期範圍")
        self.log("2. 點擊「檢查現有數據」查看當前數據狀況")
        self.log("3. 點擊「開啟台灣證交所爬蟲」進行爬取測試")
        self.log("4. 點擊「測試圖表功能」驗證圖表顯示")
    
    def log(self, message):
        """添加日誌訊息"""
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def check_existing_data(self):
        """檢查現有數據"""
        self.log("🔍 檢查現有數據...")
        
        db_files = {
            'market_index': ('market_index.db', 'market_index_info'),
            'margin_trading': ('margin_trading.db', 'margin_trading_info'),
            'historical_index': ('market_historical.db', 'market_historical_index')
        }
        
        for data_type, (db_file, table_name) in db_files.items():
            db_path = f"D:/Finlab/history/tables/{db_file}"
            
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    
                    # 檢查資料筆數
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    
                    # 檢查日期範圍
                    if data_type == 'historical_index' and count > 0:
                        cursor.execute(f"SELECT MIN(Date), MAX(Date) FROM {table_name}")
                        date_range = cursor.fetchone()
                        self.log(f"✅ {data_type}: {count} 筆資料 (日期範圍: {date_range[0]} - {date_range[1]})")
                    elif count > 0:
                        cursor.execute(f"SELECT MIN(crawl_time), MAX(crawl_time) FROM {table_name}")
                        time_range = cursor.fetchone()
                        self.log(f"✅ {data_type}: {count} 筆資料 (時間範圍: {time_range[0]} - {time_range[1]})")
                    else:
                        self.log(f"⚠️ {data_type}: 無資料")
                    
                    conn.close()
                    
                except Exception as e:
                    self.log(f"❌ {data_type} 檢查失敗: {e}")
            else:
                self.log(f"❌ {data_type}: 資料庫檔案不存在")
    
    def open_twse_crawler(self):
        """開啟台灣證交所爬蟲"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 開啟台灣證交所市場數據爬蟲...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            # 設定測試日期範圍
            start_date = self.start_date.date()
            end_date = self.end_date.date()
            dialog.start_date.setDate(start_date)
            dialog.end_date.setDate(end_date)
            
            self.log(f"📅 已設定日期範圍: {start_date.toString('yyyy-MM-dd')} 至 {end_date.toString('yyyy-MM-dd')}")
            self.log("💡 請在對話框中選擇要爬取的數據類型並開始爬取")
            self.log("🎯 重點測試：檢查爬取的數據是否涵蓋指定的日期範圍")
            
            dialog.show()
            
        except ImportError as e:
            self.log(f"❌ 導入錯誤: {e}")
        except Exception as e:
            self.log(f"❌ 開啟爬蟲失敗: {e}")
    
    def test_chart_function(self):
        """測試圖表功能"""
        self.log("📈 測試圖表功能...")
        
        try:
            import pyqtgraph as pg
            self.log("✅ pyqtgraph 套件已安裝")
            
            # 檢查是否有數據可以繪製圖表
            has_data = False
            
            db_files = {
                'market_index': ('market_index.db', 'market_index_info'),
                'margin_trading': ('margin_trading.db', 'margin_trading_info'),
                'historical_index': ('market_historical.db', 'market_historical_index')
            }
            
            for data_type, (db_file, table_name) in db_files.items():
                db_path = f"D:/Finlab/history/tables/{db_file}"
                
                if os.path.exists(db_path):
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        
                        if count > 0:
                            has_data = True
                            self.log(f"✅ {data_type}: 有 {count} 筆數據可用於圖表")
                        
                        conn.close()
                        
                    except Exception as e:
                        self.log(f"❌ {data_type} 檢查失敗: {e}")
            
            if has_data:
                self.log("🎯 建議測試步驟：")
                self.log("1. 開啟台灣證交所爬蟲對話框")
                self.log("2. 切換到「📈 圖表分析」分頁")
                self.log("3. 選擇數據類型和圖表類型")
                self.log("4. 點擊「📈 生成圖表」按鈕")
                self.log("5. 驗證圖表是否正確顯示")
            else:
                self.log("⚠️ 沒有可用的數據來生成圖表")
                self.log("💡 請先爬取一些數據，然後再測試圖表功能")
            
        except ImportError:
            self.log("❌ pyqtgraph 套件未安裝")
            self.log("💡 請執行: pip install pyqtgraph")
        except Exception as e:
            self.log(f"❌ 圖表功能測試失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = DateRangeChartTestWindow()
    window.show()
    
    print("📅📈 日期範圍爬取和圖表功能測試程式已啟動")
    print("🎯 測試重點：")
    print("1. 日期範圍設定是否正確傳遞給爬蟲")
    print("2. 爬取的數據是否涵蓋指定日期範圍")
    print("3. 圖表功能是否正常工作")
    print("4. 不同圖表類型是否都能正確顯示")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
