#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試三大法人買賣狀況區塊高度調整效果
確保自營商買賣超資訊可以完整顯示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI

def test_institutional_height_adjustment():
    """測試三大法人區塊高度調整"""
    print("🧪 測試三大法人買賣狀況區塊高度調整")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    print("✅ 主視窗創建成功")
    print("\n📋 測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 在股票列表中右鍵點擊股票")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查三大法人買賣狀況區塊")
    
    print("\n🎯 檢查重點:")
    print("• 外資買賣超資訊是否完整顯示")
    print("• 投信買賣超資訊是否完整顯示")
    print("• 自營商買賣超資訊是否完整顯示")
    print("• 區塊高度是否適中（不會太高或太低）")
    print("• 所有文字是否清晰可讀")
    
    print("\n📏 高度調整詳情:")
    print("• 原始高度: 200px")
    print("• 第一次調整: 120px (太小，自營商被截斷)")
    print("• 最終調整: 160px (確保完整顯示)")
    
    print("\n🔍 預期顯示格式:")
    print("┌─────────────────────────────────┐")
    print("│ 📅 資料日期：2025-07-30         │")
    print("│ 🌍 外資：    買賣超 -2.1千股    │")
    print("│ 🏦 投信：    買賣超 +27.8千股   │")
    print("│ 🏢 自營商：  買賣超 持平        │")
    print("└─────────────────────────────────┘")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 特別注意自營商資訊是否完整顯示")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    test_institutional_height_adjustment()
