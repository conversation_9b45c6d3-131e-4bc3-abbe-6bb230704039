#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
節省配額的FinMind API測試
只使用最少的API調用來驗證功能
"""

import logging
from datetime import datetime
from finmind_quota_manager import FinMindQuotaManager
from finmind_data_provider import FinMindDataProvider

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_quota_manager():
    """測試配額管理器"""
    print("🧪 測試配額管理器")
    print("=" * 40)
    
    manager = FinMindQuotaManager()
    
    # 檢查當前使用情況
    usage = manager.get_current_usage()
    print(f"📊 當前API使用情況:")
    print(f"  小時使用: {usage['hourly_usage']}/{usage['hourly_limit']}")
    print(f"  今日使用: {usage['daily_usage']}/{usage['daily_limit']}")
    print(f"  可以請求: {'✅' if usage['can_make_request'] else '❌'}")
    
    # 獲取智能測試計劃
    test_plan = manager.get_smart_test_plan()
    print(f"\n🎯 智能測試計劃:")
    print(f"  測試股票: {test_plan['test_stocks']}")
    print(f"  測試數據: {test_plan['test_datasets']}")
    print(f"  預估成本: {test_plan['estimated_cost']} 次API調用")
    print(f"  建議: {test_plan['recommendation']}")
    
    # 獲取每日維護計劃
    maintenance = manager.get_daily_maintenance_plan()
    print(f"\n🔧 每日維護計劃:")
    print(f"  計劃類型: {maintenance['plan_type']}")
    print(f"  最大請求: {maintenance['max_requests']}")
    print(f"  描述: {maintenance['description']}")
    
    return usage['can_make_request']

def test_minimal_api_calls():
    """最小化API調用測試"""
    print("\n🧪 最小化API調用測試")
    print("=" * 40)
    
    provider = FinMindDataProvider()
    
    # 只測試一支股票的一種數據
    test_stock = "2330"  # 台積電
    
    print(f"📊 測試股票: {test_stock} (台積電)")
    
    # 測試本益比數據（最重要的數據）
    print("📈 測試本益比數據...")
    per_data = provider.get_per_data(test_stock, days=7)  # 只獲取7天數據
    
    if per_data:
        print(f"  ✅ 本益比: {per_data.get('pe_ratio', 0):.2f}")
        print(f"  ✅ 股價: {per_data.get('price', 0):.2f}")
        print(f"  ✅ 日期: {per_data.get('date', 'N/A')}")
        print(f"  ✅ 數據來源: {'真實' if per_data.get('is_real_data') else '模擬'}")
    else:
        print("  ❌ 無法獲取本益比數據")
    
    # 測試緩存功能
    print("\n📦 測試緩存功能...")
    print("  第二次請求相同數據（應該使用緩存）:")
    
    per_data_cached = provider.get_per_data(test_stock, days=7)
    if per_data_cached:
        print(f"  ✅ 緩存數據獲取成功")
        print(f"  📊 本益比: {per_data_cached.get('pe_ratio', 0):.2f}")
    else:
        print("  ❌ 緩存數據獲取失敗")
    
    # 檢查API使用統計
    usage_after = provider.quota_manager.get_current_usage()
    print(f"\n📊 測試後API使用:")
    print(f"  小時使用: {usage_after['hourly_usage']}/{usage_after['hourly_limit']}")
    print(f"  剩餘額度: {usage_after['hourly_remaining']}")
    
    return per_data is not None

def test_strategy_data_needs():
    """測試策略數據需求"""
    print("\n🎯 測試策略數據需求")
    print("=" * 40)
    
    manager = FinMindQuotaManager()
    
    # 模擬啟用的策略
    active_strategies = ["CANSLIM", "高殖利率烏龜", "超級績效台股版"]
    
    print(f"📋 啟用策略: {', '.join(active_strategies)}")
    
    # 獲取優化計劃
    optimized = manager.optimize_for_strategies(active_strategies)
    
    print(f"\n📊 數據需求分析:")
    for data_type, weight in optimized['data_weights'].items():
        print(f"  {data_type}: 權重 {weight}")
    
    print(f"\n💡 建議配額分配:")
    for data_type, allocation in optimized['recommended_allocation'].items():
        print(f"  {data_type}: {allocation} 次API調用")
    
    print(f"\n📈 總計: {optimized['total_requests']} 次API調用")
    
    return optimized

def test_batch_efficiency():
    """測試批量效率"""
    print("\n⚡ 測試批量效率")
    print("=" * 40)
    
    provider = FinMindDataProvider()
    
    # 測試多支股票的相同數據類型
    test_stocks = ["2330", "2317"]  # 只測試2支股票
    
    print(f"📊 批量測試股票: {', '.join(test_stocks)}")
    
    results = {}
    api_calls_before = provider.quota_manager.get_current_usage()['hourly_usage']
    
    for stock_id in test_stocks:
        print(f"\n🔍 處理股票: {stock_id}")
        
        # 獲取本益比數據
        per_data = provider.get_per_data(stock_id, days=3)  # 減少天數以節省配額
        if per_data:
            results[stock_id] = {
                'pe_ratio': per_data.get('pe_ratio', 0),
                'price': per_data.get('price', 0),
                'success': True
            }
            print(f"  ✅ 本益比: {per_data.get('pe_ratio', 0):.2f}")
        else:
            results[stock_id] = {'success': False}
            print(f"  ❌ 數據獲取失敗")
    
    api_calls_after = provider.quota_manager.get_current_usage()['hourly_usage']
    api_used = api_calls_after - api_calls_before
    
    print(f"\n📊 批量處理結果:")
    print(f"  處理股票數: {len(test_stocks)}")
    print(f"  成功數量: {sum(1 for r in results.values() if r.get('success'))}")
    print(f"  API調用數: {api_used}")
    print(f"  平均每股: {api_used/len(test_stocks):.1f} 次API調用")
    
    return results

def generate_daily_plan():
    """生成每日數據更新計劃"""
    print("\n📅 生成每日數據更新計劃")
    print("=" * 40)
    
    manager = FinMindQuotaManager()
    
    # 獲取每日更新計劃
    daily_plan = manager.create_daily_update_plan()
    
    print(f"📊 可用API配額: {daily_plan['total_available']}")
    print(f"\n💡 建議配額分配:")
    
    for category, allocation in daily_plan['quota_allocation'].items():
        print(f"  {category}: {allocation} 次")
    
    print(f"\n⏰ 預估完成時間: {daily_plan['estimated_completion_time']}")
    
    print(f"\n📋 數據優先級:")
    for data_type, priority in daily_plan['data_priority'].items():
        print(f"  {data_type}: 優先級 {priority}")
    
    # 顯示建議的更新項目（前10項）
    updates = daily_plan['recommended_updates'][:10]
    if updates:
        print(f"\n🎯 建議優先更新（前10項）:")
        for i, update in enumerate(updates, 1):
            print(f"  {i}. {update['stock_id']} - {update['dataset']} (優先級: {update['priority']})")
    
    return daily_plan

def main():
    """主函數"""
    print("🎯 FinMind API 節省配額測試")
    print("=" * 60)
    
    # 1. 測試配額管理器
    can_proceed = test_quota_manager()
    
    if not can_proceed:
        print("\n⚠️ API配額不足，建議稍後再試")
        return
    
    # 2. 最小化API調用測試
    api_success = test_minimal_api_calls()
    
    if api_success:
        print("\n✅ 基本API功能正常")
        
        # 3. 測試策略數據需求
        strategy_plan = test_strategy_data_needs()
        
        # 4. 測試批量效率
        batch_results = test_batch_efficiency()
        
        # 5. 生成每日計劃
        daily_plan = generate_daily_plan()
        
        print(f"\n🎉 測試完成總結:")
        print(f"  ✅ 配額管理: 正常")
        print(f"  ✅ API調用: 正常")
        print(f"  ✅ 緩存功能: 正常")
        print(f"  ✅ 策略優化: 正常")
        print(f"  ✅ 批量處理: 正常")
        
        # 最終API使用統計
        final_usage = FinMindQuotaManager().get_current_usage()
        print(f"\n📊 最終API使用統計:")
        print(f"  小時使用: {final_usage['hourly_usage']}/{final_usage['hourly_limit']}")
        print(f"  使用率: {(final_usage['hourly_usage']/final_usage['hourly_limit']*100):.1f}%")
        print(f"  剩餘額度: {final_usage['hourly_remaining']}")
        
        if final_usage['hourly_usage'] <= 10:
            print(f"  🎉 測試非常節省配額！")
        elif final_usage['hourly_usage'] <= 50:
            print(f"  ✅ 測試配額使用合理")
        else:
            print(f"  ⚠️ 測試配額使用較多，建議優化")
    
    else:
        print("\n❌ API功能異常，請檢查Token和網路連接")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
