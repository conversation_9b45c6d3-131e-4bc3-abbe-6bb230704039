#!/usr/bin/env python3
"""
測試自建資料擷取系統
"""

import pandas as pd
import logging
import time
import os
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO)

def test_custom_crawler():
    """測試自建資料擷取器"""
    print("🧪 測試自建資料擷取系統...")
    
    try:
        from custom_data_crawler import CustomDataCrawler
        
        # 創建擷取器實例
        crawler = CustomDataCrawler("test_stock_data.db")
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454', '3008', '2412']
        
        print(f"\n📊 測試日K線數據獲取:")
        for stock in test_stocks:
            print(f"\n🔍 測試 {stock}:")
            
            # 測試日K線數據
            df_daily = crawler.get_daily_data(stock, days=30)
            
            if not df_daily.empty:
                print(f"   ✅ 日K線: {len(df_daily)} 筆數據")
                print(f"   最新價格: {df_daily['Close'].iloc[-1]:.2f}")
                print(f"   數據範圍: {df_daily['date'].min()} 到 {df_daily['date'].max()}")
            else:
                print(f"   ❌ 無法獲取日K線數據")
            
            time.sleep(2)  # 避免請求過快
        
        print(f"\n📈 測試盤中數據獲取:")
        for stock in test_stocks[:2]:  # 只測試前兩支
            print(f"\n🔍 測試 {stock} 盤中數據:")
            
            # 測試盤中數據
            df_intraday = crawler.get_intraday_data(stock, '5m')
            
            if not df_intraday.empty:
                print(f"   ✅ 盤中數據: {len(df_intraday)} 筆5分鐘數據")
                print(f"   最新價格: {df_intraday['close'].iloc[-1]:.2f}")
                print(f"   時間範圍: {df_intraday['datetime'].min()} 到 {df_intraday['datetime'].max()}")
            else:
                print(f"   ❌ 無法獲取盤中數據")
            
            time.sleep(2)
        
        print(f"\n💰 測試即時報價:")
        for stock in test_stocks[:3]:  # 只測試前三支
            print(f"\n🔍 測試 {stock} 即時報價:")
            
            # 測試即時報價
            quote = crawler.get_realtime_quote(stock)
            
            if quote:
                print(f"   ✅ 即時報價: {quote['price']:.2f}")
                print(f"   漲跌: {quote['change_amount']:+.2f} ({quote['change_percent']:+.2f}%)")
                print(f"   成交量: {quote['volume']:,}")
                print(f"   更新時間: {quote['timestamp']}")
            else:
                print(f"   ❌ 無法獲取即時報價")
            
            time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 自建資料擷取器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """測試資料庫操作"""
    print(f"\n🧪 測試資料庫操作...")
    
    try:
        from custom_data_crawler import CustomDataCrawler
        import sqlite3
        
        crawler = CustomDataCrawler("test_stock_data.db")
        
        # 檢查資料庫表是否創建成功
        conn = sqlite3.connect("test_stock_data.db")
        cursor = conn.cursor()
        
        # 檢查表結構
        tables = ['daily_data', 'intraday_data', 'realtime_quotes']
        
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            
            if result:
                print(f"   ✅ 表 {table} 創建成功")
                
                # 檢查數據數量
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"      數據筆數: {count}")
            else:
                print(f"   ❌ 表 {table} 不存在")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 資料庫操作測試失敗: {e}")
        return False

def test_data_sources():
    """測試各個數據源"""
    print(f"\n🧪 測試各個數據源...")
    
    try:
        from custom_data_crawler import CustomDataCrawler
        
        crawler = CustomDataCrawler("test_stock_data.db")
        test_symbol = '2330'
        
        # 測試證交所官方API
        print(f"\n📊 測試證交所官方API:")
        try:
            df_twse = crawler._fetch_twse_daily(test_symbol, 10)
            if not df_twse.empty:
                print(f"   ✅ 證交所API: {len(df_twse)} 筆數據")
            else:
                print(f"   ❌ 證交所API: 無數據")
        except Exception as e:
            print(f"   ❌ 證交所API: {e}")
        
        # 測試Yahoo台股
        print(f"\n📈 測試Yahoo台股:")
        try:
            df_yahoo = crawler._fetch_yahoo_daily(test_symbol, 10)
            if not df_yahoo.empty:
                print(f"   ✅ Yahoo台股: {len(df_yahoo)} 筆數據")
            else:
                print(f"   ❌ Yahoo台股: 無數據")
        except Exception as e:
            print(f"   ❌ Yahoo台股: {e}")
        
        # 測試鉅亨網
        print(f"\n💹 測試鉅亨網:")
        try:
            df_cnyes = crawler._fetch_cnyes_daily(test_symbol, 10)
            if not df_cnyes.empty:
                print(f"   ✅ 鉅亨網: {len(df_cnyes)} 筆數據")
            else:
                print(f"   ❌ 鉅亨網: 無數據")
        except Exception as e:
            print(f"   ❌ 鉅亨網: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 數據源測試失敗: {e}")
        return False

def test_convenience_functions():
    """測試便捷函數"""
    print(f"\n🧪 測試便捷函數...")
    
    try:
        from custom_data_crawler import (
            get_stock_data_custom,
            get_intraday_data_custom,
            get_realtime_quote_custom
        )
        
        test_symbol = '2330'
        
        # 測試日K線便捷函數
        print(f"\n📊 測試日K線便捷函數:")
        df_daily = get_stock_data_custom(test_symbol, 10)
        if not df_daily.empty:
            print(f"   ✅ 成功獲取 {len(df_daily)} 筆日K線數據")
        else:
            print(f"   ❌ 無法獲取日K線數據")
        
        # 測試盤中數據便捷函數
        print(f"\n📈 測試盤中數據便捷函數:")
        df_intraday = get_intraday_data_custom(test_symbol, '5m')
        if not df_intraday.empty:
            print(f"   ✅ 成功獲取 {len(df_intraday)} 筆盤中數據")
        else:
            print(f"   ❌ 無法獲取盤中數據")
        
        # 測試即時報價便捷函數
        print(f"\n💰 測試即時報價便捷函數:")
        quote = get_realtime_quote_custom(test_symbol)
        if quote:
            print(f"   ✅ 成功獲取即時報價: {quote['price']:.2f}")
        else:
            print(f"   ❌ 無法獲取即時報價")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷函數測試失敗: {e}")
        return False

def cleanup_test_files():
    """清理測試文件"""
    try:
        if os.path.exists("test_stock_data.db"):
            os.remove("test_stock_data.db")
            print("🗑️ 清理測試資料庫文件")
    except:
        pass

if __name__ == "__main__":
    print("🚀 開始測試自建資料擷取系統...")
    
    results = []
    
    # 執行各項測試
    results.append(("自建資料擷取器", test_custom_crawler()))
    results.append(("資料庫操作", test_database_operations()))
    results.append(("數據源測試", test_data_sources()))
    results.append(("便捷函數", test_convenience_functions()))
    
    # 總結結果
    print(f"\n🎉 測試完成！")
    print(f"\n📋 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎊 所有測試都通過！自建資料擷取系統運行正常！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n💡 自建資料擷取系統特色:")
    print(f"   ✅ 多數據源自動切換")
    print(f"   ✅ 本地資料庫緩存")
    print(f"   ✅ 智能更新機制")
    print(f"   ✅ 即時報價監控")
    print(f"   ✅ 完整的錯誤處理")
    print(f"   ✅ 支援日K線和盤中數據")
    
    # 清理測試文件
    cleanup_test_files()
    
    print(f"\n🎯 下一步建議:")
    print(f"   1. 整合到主程式中")
    print(f"   2. 添加更多數據源")
    print(f"   3. 實現WebSocket即時連接")
    print(f"   4. 優化數據更新策略")
    print(f"   5. 添加數據品質檢查")
