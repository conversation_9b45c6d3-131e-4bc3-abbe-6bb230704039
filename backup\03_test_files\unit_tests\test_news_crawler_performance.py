#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新聞爬蟲效能和穩定性
"""

import time
import threading
import psutil
import os
from datetime import datetime
from news_crawler_anue import AnueNewsCrawler

def monitor_system_resources():
    """監控系統資源使用情況"""
    process = psutil.Process(os.getpid())
    
    print("🔍 系統資源監控開始...")
    print("=" * 50)
    
    start_time = time.time()
    
    while True:
        try:
            # CPU 使用率
            cpu_percent = process.cpu_percent()
            
            # 記憶體使用
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # 執行時間
            elapsed = time.time() - start_time
            
            print(f"⏱️  {elapsed:6.1f}s | 💾 {memory_mb:6.1f}MB | 🖥️  {cpu_percent:5.1f}% CPU")
            
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 監控錯誤: {e}")
            break

def test_lightweight_crawling():
    """測試輕量化爬取"""
    print("🚀 測試輕量化新聞爬取")
    print("=" * 50)
    
    try:
        # 啟動資源監控
        monitor_thread = threading.Thread(target=monitor_system_resources, daemon=True)
        monitor_thread.start()
        
        # 建立爬蟲實例
        print("📰 建立鉅亨網爬蟲實例...")
        crawler = AnueNewsCrawler()
        
        # 測試小量爬取（1天，限制數量）
        print("🔍 開始爬取最近1天的新聞（輕量化模式）...")
        start_time = time.time()
        
        result = crawler.run(ndays=1)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"✅ 爬取完成！")
        print(f"⏱️  總耗時: {elapsed:.1f} 秒")
        print(f"📊 結果: {result}")
        
        # 檢查資料庫
        news_list = crawler.get_news_by_stock("2330", days=1)
        print(f"🎯 台積電相關新聞: {len(news_list)} 筆")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_stock_crawling():
    """測試特定股票新聞爬取"""
    print("\n🎯 測試特定股票新聞爬取")
    print("=" * 50)
    
    try:
        crawler = AnueNewsCrawler()
        
        # 測試台積電新聞
        print("🔍 爬取台積電(2330)相關新聞...")
        start_time = time.time()
        
        result = crawler.run(ndays=1, stock_code="2330")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"✅ 特定股票爬取完成！")
        print(f"⏱️  耗時: {elapsed:.1f} 秒")
        print(f"📊 結果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定股票爬取失敗: {e}")
        return False

def check_database_performance():
    """檢查資料庫效能"""
    print("\n💾 檢查資料庫效能")
    print("=" * 50)
    
    try:
        import sqlite3
        
        db_path = "D:/Finlab/history/tables/news.db"
        if not os.path.exists(db_path):
            print("❌ 資料庫不存在")
            return False
        
        start_time = time.time()
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 測試查詢效能
            cursor.execute("SELECT COUNT(*) FROM news_content")
            count = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT title, date, source 
                FROM news_content 
                ORDER BY date DESC, time DESC 
                LIMIT 10
            """)
            recent_news = cursor.fetchall()
            
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"📊 資料庫記錄數: {count}")
        print(f"⏱️  查詢耗時: {query_time:.3f} 秒")
        print(f"📋 最新新聞: {len(recent_news)} 筆")
        
        if query_time < 1.0:
            print("✅ 資料庫效能良好")
        else:
            print("⚠️ 資料庫查詢較慢，建議優化")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料庫檢查失敗: {e}")
        return False

if __name__ == "__main__":
    print("🔍 新聞爬蟲效能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🖥️  Python PID: {os.getpid()}")
    print()
    
    # 測試1: 輕量化爬取
    test1_success = test_lightweight_crawling()
    
    # 等待一下讓系統穩定
    time.sleep(3)
    
    # 測試2: 特定股票爬取
    test2_success = test_specific_stock_crawling()
    
    # 測試3: 資料庫效能
    test3_success = check_database_performance()
    
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    print(f"  🔍 輕量化爬取: {'✅ 成功' if test1_success else '❌ 失敗'}")
    print(f"  🎯 特定股票爬取: {'✅ 成功' if test2_success else '❌ 失敗'}")
    print(f"  💾 資料庫效能: {'✅ 良好' if test3_success else '❌ 需優化'}")
    
    if all([test1_success, test2_success, test3_success]):
        print("\n🎉 所有測試通過！新聞爬蟲運作正常")
    else:
        print("\n⚠️ 部分測試失敗，需要進一步優化")
    
    print("\n💡 建議:")
    print("  1. 如果爬取過程中系統負擔過重，可以增加延遲時間")
    print("  2. 考慮分批爬取，避免一次處理太多新聞")
    print("  3. 使用多執行緒時要注意資源管理")
    print("  4. 定期清理舊的新聞資料以維持效能")
