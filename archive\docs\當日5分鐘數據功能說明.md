# 📊 當日5分鐘數據功能完整說明

## 🎯 功能概述

我已經為您的專案添加了完整的**當日5分鐘數據獲取和盤中監控功能**，解決了您提到的"本專案目前缺的是當日5分鐘的數據"問題。

## 🚀 新增功能模組

### 1️⃣ **增強版數據獲取器** (`enhanced_data_fetcher.py`)
- **多數據源支持**: yfinance、證交所API、Yahoo台股等
- **自動容錯**: 一個數據源失敗自動切換到下一個
- **數據緩存**: 避免重複請求，提高效率
- **格式統一**: 統一的數據格式輸出

### 2️⃣ **盤中數據獲取器** (`intraday_data_fetcher.py`)
- **5分鐘K線**: 獲取當日5分鐘級別的OHLCV數據
- **即時更新**: 支持盤中即時數據更新
- **市場狀態**: 自動檢測開盤/收盤狀態
- **多股票支持**: 批量獲取多支股票數據

### 3️⃣ **盤中監控系統** (`intraday_monitor.py`)
- **即時監控**: 自動監控指定股票的盤中表現
- **策略信號**: 即時檢測突破、量價異常等信號
- **警報系統**: 重要信號即時提醒
- **多線程**: 不影響主程式運行

### 4️⃣ **主程式整合** (新增"盤中監控"標籤頁)
- **圖形界面**: 直觀的盤中監控操作界面
- **即時顯示**: 股票價格、漲跌、成交量即時更新
- **警報管理**: 警報列表、聲音提醒、清除功能
- **靈活配置**: 可自定義監控股票和更新頻率

## 📈 數據獲取能力

### 🎯 支持的數據類型
- **歷史日K線**: 支援90天以上的歷史數據
- **當日5分鐘K線**: 開盤到當前時間的5分鐘數據
- **即時報價**: 最新價格、成交量、漲跌幅
- **技術指標**: 自動計算MA、RSI、布林帶等指標

### 🔄 數據源優先級
1. **yfinance** - 最穩定的國際數據源
2. **證交所API** - 官方數據，準確可靠
3. **Yahoo台股** - 備用數據源
4. **本地數據庫** - 歷史數據緩存

### ⚡ 性能特色
- **智能緩存**: 1分鐘緩存避免重複請求
- **容錯機制**: 自動重試和數據源切換
- **批量處理**: 支援多股票並行獲取
- **格式統一**: 所有數據源輸出統一格式

## 🖥️ 使用方法

### 📊 盤中監控操作步驟

1. **啟動程式**: 運行 `O3mh_gui_v21_optimized.py`

2. **進入監控頁面**: 點擊"盤中監控"標籤頁

3. **設置監控股票**: 
   - 在"監控股票"輸入框中輸入股票代碼
   - 格式: `2330,2317,2454,3008,2412` (用逗號分隔)
   - 點擊"更新列表"確認

4. **配置監控參數**:
   - 設置更新間隔 (30-300秒)
   - 選擇是否開啟聲音提醒

5. **開始監控**: 點擊"🚀 開始監控"按鈕

6. **觀察結果**:
   - 查看即時價格表格
   - 監控警報列表
   - 注意市場狀態顯示

### 🔧 程式化調用

```python
# 獲取5分鐘數據
from intraday_data_fetcher import get_intraday_data

# 獲取台積電當日5分鐘數據
df = get_intraday_data('2330', '5m')
print(f"獲取到 {len(df)} 筆5分鐘數據")

# 獲取市場狀態
from intraday_data_fetcher import get_market_status
status = get_market_status()
print(f"市場狀態: {status['status']}")
```

## 🚨 監控策略

### 1️⃣ **突破監控**
- **價格突破**: 突破MA20線2%以上
- **成交量突破**: 成交量爆量3倍以上
- **強度分級**: 高/中/低三個等級

### 2️⃣ **量價異常**
- **價漲量縮**: 價格上漲2%但成交量萎縮50%
- **價跌量增**: 價格下跌2%但成交量放大2倍
- **背離警告**: 量價背離的潛在風險

### 3️⃣ **技術指標**
- **RSI超買**: RSI > 80，注意回調風險
- **RSI超賣**: RSI < 20，注意反彈機會
- **指標確認**: 多重技術指標驗證

### 4️⃣ **支撐阻力**
- **接近阻力**: 價格接近20日高點98%
- **接近支撐**: 價格接近20日低點102%
- **關鍵位置**: 重要支撐阻力位提醒

## 📊 數據格式

### 🎯 5分鐘數據結構
```python
DataFrame columns:
- datetime: 時間戳 (pandas.Timestamp)
- open: 開盤價 (float)
- high: 最高價 (float)
- low: 最低價 (float)
- close: 收盤價 (float)
- volume: 成交量 (int)
```

### 📈 技術指標
```python
自動計算指標:
- MA5, MA10, MA20: 移動平均線
- RSI: 相對強弱指標
- Volume_MA: 成交量移動平均
- Volume_Ratio: 成交量比率
- Price_Change: 價格變化率
```

## ⚙️ 配置選項

### 🔧 監控參數
- **更新間隔**: 30-300秒可調
- **監控股票**: 支援任意數量股票
- **警報設置**: 可開關聲音提醒
- **數據緩存**: 1分鐘自動緩存

### 📱 界面設置
- **表格顯示**: 即時價格、漲跌、成交量
- **警報列表**: 最多顯示50條警報
- **狀態指示**: 監控狀態、市場狀態
- **顏色標識**: 漲跌用紅綠色區分

## 🎉 實際應用場景

### 📊 **日內交易**
- 監控關鍵股票的5分鐘走勢
- 即時捕捉突破信號
- 量價配合確認進出場時機

### 🎯 **風險控制**
- 監控持倉股票的異常波動
- 及時發現量價背離風險
- 支撐阻力位風險提醒

### 📈 **策略驗證**
- 觀察策略信號的即時表現
- 驗證技術指標的有效性
- 累積實戰經驗數據

### 💡 **學習研究**
- 觀察盤中股票行為模式
- 研究量價關係
- 分析技術指標效果

## 🔍 故障排除

### ❌ **常見問題**

1. **無法獲取數據**
   - 檢查網路連接
   - 確認股票代碼正確
   - 查看是否在交易時間

2. **監控無法啟動**
   - 確認市場已開盤
   - 檢查股票列表格式
   - 查看錯誤日誌信息

3. **數據更新緩慢**
   - 調整更新間隔
   - 減少監控股票數量
   - 檢查網路速度

### 🛠️ **解決方案**
- 查看程式日誌獲取詳細錯誤信息
- 嘗試重新啟動監控功能
- 檢查防火牆和網路設置
- 確認相關Python模組已安裝

## 🎊 總結

現在您的專案已經具備完整的**當日5分鐘數據獲取和盤中監控能力**：

✅ **多數據源**: 確保數據獲取的穩定性
✅ **即時監控**: 盤中股票表現即時追蹤
✅ **智能警報**: 重要信號自動提醒
✅ **圖形界面**: 直觀易用的操作界面
✅ **策略整合**: 與現有策略系統完美結合

**🚀 您現在可以進行完整的盤中監控和即時策略分析了！**

---

*💡 提示: 建議在交易時間內測試監控功能，以獲得最佳體驗效果*
