#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 price.db 中股票資訊的完整性
"""

import os
import sqlite3
import pandas as pd

def check_price_db_stock_info():
    """檢查 price.db 中股票資訊的完整性"""
    
    print("=" * 80)
    print("🔍 檢查 price.db 中股票資訊的完整性")
    print("=" * 80)
    
    # 檢查 GUI 程式使用的價格資料庫
    target_db = "D:/Finlab/history/tables/price.db"

    if not os.path.exists(target_db):
        print(f"❌ 找不到價格資料庫檔案: {target_db}")
        return False

    if os.path.getsize(target_db) == 0:
        print(f"❌ 價格資料庫檔案為空: {target_db}")
        return False

    print(f"✅ 使用資料庫: {target_db}")
    print(f"📊 檔案大小: {os.path.getsize(target_db) / (1024*1024):.1f} MB")
    
    try:
        conn = sqlite3.connect(target_db)
        
        # 檢查表格結構
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        print(f"📋 資料庫中的表格:")
        for table in tables:
            print(f"   - {table[0]}")

        if not tables:
            print(f"❌ 資料庫中沒有任何表格")
            conn.close()
            return False

        # 尋找 stock_daily_data 表格（GUI 程式使用的表格）
        table_name = None
        for table in tables:
            if table[0] == 'stock_daily_data':
                table_name = table[0]
                break

        if not table_name:
            # 如果沒有找到，使用第一個表格
            table_name = tables[0][0]
            print(f"⚠️ 未找到 stock_daily_data 表格，使用: {table_name}")
        else:
            print(f"✅ 使用表格: {table_name}")
        
        # 檢查表格結構
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        print(f"\n📊 {table_name} 表格結構:")
        for i, col in enumerate(columns, 1):
            print(f"   {i:2d}. {col[1]} ({col[2]})")
        
        # 檢查資料筆數
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 總資料筆數: {total_count:,}")
        
        # 檢查唯一股票數量
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name}")
        unique_stocks = cursor.fetchone()[0]
        print(f"📊 唯一股票數量: {unique_stocks:,}")
        
        # 檢查股票名稱完整性
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE stock_name IS NULL OR stock_name = ''")
        missing_names = cursor.fetchone()[0]
        print(f"⚠️ 缺少股票名稱的股票: {missing_names:,}")
        
        # 檢查上市狀態完整性
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE listing_status IS NULL OR listing_status = ''")
        missing_listing = cursor.fetchone()[0]
        print(f"⚠️ 缺少上市狀態的股票: {missing_listing:,}")
        
        # 檢查產業資訊完整性
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE industry IS NULL OR industry = ''")
        missing_industry = cursor.fetchone()[0]
        print(f"⚠️ 缺少產業資訊的股票: {missing_industry:,}")
        
        # 顯示缺少資訊的股票範例
        print(f"\n🔍 缺少資訊的股票範例:")
        
        # 缺少股票名稱的股票
        cursor.execute(f"""
        SELECT DISTINCT stock_id 
        FROM {table_name} 
        WHERE stock_name IS NULL OR stock_name = '' 
        LIMIT 10
        """)
        missing_name_stocks = cursor.fetchall()
        if missing_name_stocks:
            print(f"   缺少股票名稱: {[stock[0] for stock in missing_name_stocks]}")
        
        # 缺少上市狀態的股票
        cursor.execute(f"""
        SELECT DISTINCT stock_id 
        FROM {table_name} 
        WHERE listing_status IS NULL OR listing_status = '' 
        LIMIT 10
        """)
        missing_listing_stocks = cursor.fetchall()
        if missing_listing_stocks:
            print(f"   缺少上市狀態: {[stock[0] for stock in missing_listing_stocks]}")
        
        # 缺少產業資訊的股票
        cursor.execute(f"""
        SELECT DISTINCT stock_id 
        FROM {table_name} 
        WHERE industry IS NULL OR industry = '' 
        LIMIT 10
        """)
        missing_industry_stocks = cursor.fetchall()
        if missing_industry_stocks:
            print(f"   缺少產業資訊: {[stock[0] for stock in missing_industry_stocks]}")
        
        # 檢查完整資訊的股票比例
        cursor.execute(f"""
        SELECT COUNT(DISTINCT stock_id) 
        FROM {table_name} 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        AND listing_status IS NOT NULL AND listing_status != ''
        AND industry IS NOT NULL AND industry != ''
        """)
        complete_stocks = cursor.fetchone()[0]
        complete_percentage = (complete_stocks / unique_stocks) * 100 if unique_stocks > 0 else 0
        
        print(f"\n📊 資訊完整性統計:")
        print(f"   完整資訊的股票: {complete_stocks:,} / {unique_stocks:,} ({complete_percentage:.1f}%)")
        print(f"   缺少資訊的股票: {unique_stocks - complete_stocks:,} ({100 - complete_percentage:.1f}%)")
        
        # 檢查最新日期的資料
        cursor.execute(f"SELECT MAX(date) FROM {table_name}")
        latest_date = cursor.fetchone()[0]
        print(f"\n📅 最新資料日期: {latest_date}")
        
        # 檢查最新日期的股票資訊完整性
        cursor.execute(f"""
        SELECT COUNT(DISTINCT stock_id) 
        FROM {table_name} 
        WHERE date = '{latest_date}'
        """)
        latest_stocks = cursor.fetchone()[0]
        
        cursor.execute(f"""
        SELECT COUNT(DISTINCT stock_id) 
        FROM {table_name} 
        WHERE date = '{latest_date}'
        AND (stock_name IS NULL OR stock_name = ''
        OR listing_status IS NULL OR listing_status = ''
        OR industry IS NULL OR industry = '')
        """)
        latest_incomplete = cursor.fetchone()[0]
        
        print(f"📊 最新日期 ({latest_date}) 資訊完整性:")
        print(f"   總股票數: {latest_stocks:,}")
        print(f"   缺少資訊: {latest_incomplete:,} ({(latest_incomplete/latest_stocks)*100:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🔍 price.db 股票資訊完整性檢查工具")
    
    # 檢查資料庫
    success = check_price_db_stock_info()
    
    if success:
        print(f"\n✅ 檢查完成")
        print(f"\n💡 建議:")
        print(f"   1. 如果有大量股票缺少資訊，建議重新執行 price 爬蟲")
        print(f"   2. 確保 stock_name_mapping.py 模組正常工作")
        print(f"   3. 檢查 price 爬蟲是否正確獲取股票資訊")
    else:
        print(f"\n❌ 檢查失敗")

if __name__ == "__main__":
    main()
