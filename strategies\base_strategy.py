#!/usr/bin/env python3
"""
策略基類模組
定義所有策略的共同接口和基礎功能
"""
import logging
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple


class BaseStrategy(ABC):
    """策略基類 - 所有策略都應該繼承此類"""

    def __init__(self, name: str, description: str, strategy_type: str):
        self.name = name
        self.description = description
        self.strategy_type = strategy_type

    @abstractmethod
    def analyze_stock(self, df: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        分析單一股票是否符合策略條件

        Args:
            df: 股票價格數據
            **kwargs: 其他可選參數（如財務數據、市場數據等）

        Returns:
            Dict包含:
            - suitable: bool, 是否符合條件
            - reason: str, 分析原因
            - details: dict, 詳細分析結果
            - score: float, 評分
            - strategy_name: str, 策略名稱
        """
        pass

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """計算RSI指標"""
        try:
            if len(prices) < period + 1:
                return pd.Series([np.nan] * len(prices), index=prices.index)

            delta = prices.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)

            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()

            # 避免除零錯誤
            avg_loss = avg_loss.replace(0, 0.0001)

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return rsi.fillna(0)

        except Exception as e:
            logging.error(f"計算RSI失敗: {e}")
            return pd.Series([np.nan] * len(prices), index=prices.index)

    def calculate_ma(self, prices: pd.Series, period: int) -> pd.Series:
        """計算移動平均線"""
        try:
            return prices.rolling(window=period).mean()
        except Exception as e:
            logging.error(f"計算MA失敗: {e}")
            return pd.Series([np.nan] * len(prices), index=prices.index)

    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """計算布林通道"""
        try:
            ma = self.calculate_ma(prices, period)
            std = prices.rolling(window=period).std()

            upper_band = ma + (std * std_dev)
            lower_band = ma - (std * std_dev)

            return upper_band, ma, lower_band

        except Exception as e:
            logging.error(f"計算布林通道失敗: {e}")
            return (pd.Series([np.nan] * len(prices), index=prices.index),
                   pd.Series([np.nan] * len(prices), index=prices.index),
                   pd.Series([np.nan] * len(prices), index=prices.index))

    def check_volume_condition(self, df: pd.DataFrame, min_volume: int = 100000,
                              volume_ratio: float = 1.5, period: int = 20) -> Tuple[bool, str]:
        """檢查成交量條件"""
        try:
            current_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume'].iloc[-period:].mean()

            volume_ok = current_volume >= min_volume
            ratio_ok = current_volume >= avg_volume * volume_ratio

            if volume_ok and ratio_ok:
                return True, f"成交量{current_volume/1000:.0f}張 >= {min_volume/1000:.0f}張, 比率{current_volume/avg_volume:.1f}倍"
            elif volume_ok:
                return False, f"成交量{current_volume/1000:.0f}張足夠，但比率{current_volume/avg_volume:.1f}倍不足"
            else:
                return False, f"成交量{current_volume/1000:.0f}張不足{min_volume/1000:.0f}張"

        except Exception as e:
            return False, f"成交量檢查失敗: {str(e)}"

    def check_price_trend(self, df: pd.DataFrame, ma_period: int = 20) -> Tuple[bool, str]:
        """檢查價格趨勢"""
        try:
            close = df['Close']
            ma = self.calculate_ma(close, ma_period)

            current_price = close.iloc[-1]
            current_ma = ma.iloc[-1]
            prev_ma = ma.iloc[-2] if len(ma) >= 2 else current_ma

            above_ma = current_price > current_ma
            ma_rising = current_ma > prev_ma

            if above_ma and ma_rising:
                return True, f"價格{current_price:.2f} > MA{ma_period}({current_ma:.2f}), MA上升"
            elif above_ma:
                return True, f"價格{current_price:.2f} > MA{ma_period}({current_ma:.2f}), MA持平"
            else:
                return False, f"價格{current_price:.2f} <= MA{ma_period}({current_ma:.2f})"

        except Exception as e:
            return False, f"趨勢檢查失敗: {str(e)}"

    def validate_data(self, df: pd.DataFrame, min_days: int = 20) -> Tuple[bool, str]:
        """驗證數據完整性"""
        if df.empty:
            return False, "數據為空"

        if len(df) < min_days:
            return False, f"數據不足，需要至少{min_days}日數據，目前只有{len(df)}日"

        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return False, f"缺少必要欄位: {missing_columns}"

        return True, "數據驗證通過"