#!/usr/bin/env python3
"""
策略移除测试脚本
验证不成熟的策略是否已成功移除
"""

import sys
import re

def test_strategy_removal():
    """测试策略移除"""
    print("🔍 检查策略移除情况")
    print("=" * 60)
    
    # 读取文件内容
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 文件未找到")
        return False
    
    # 检查已移除的策略
    removed_strategies = [
        "智能突破策略",
        "超賣反彈策略", 
        "簡單趨勢策略"
    ]
    
    # 检查已移除的条件类型
    removed_conditions = [
        "smart_breakout_pullback",
        "oversold_bounce"
    ]
    
    # 保留的策略
    remaining_strategies = [
        "勝率73.45%",
        "破底反彈高量",
        "阿水一式",
        "阿水二式"
    ]
    
    print("📋 检查结果：")
    print()
    
    # 1. 检查策略定义是否已移除
    print("1. 策略定义检查：")
    strategies_found = []
    for strategy in removed_strategies:
        if f'"{strategy}"' in content:
            strategies_found.append(strategy)
            print(f"   ❌ {strategy} - 仍存在")
        else:
            print(f"   ✅ {strategy} - 已移除")
    
    print()
    
    # 2. 检查条件类型是否已移除
    print("2. 条件类型检查：")
    conditions_found = []
    for condition in removed_conditions:
        # 检查条件定义
        if f'condition_type == "{condition}"' in content:
            conditions_found.append(condition)
            print(f"   ❌ {condition} - 仍存在于条件检查中")
        else:
            print(f"   ✅ {condition} - 已从条件检查中移除")
        
        # 检查支持列表
        if f"'{condition}'" in content:
            print(f"   ⚠️  {condition} - 可能仍在支持列表中")
    
    print()
    
    # 3. 检查保留的策略
    print("3. 保留策略检查：")
    for strategy in remaining_strategies:
        if f'"{strategy}"' in content:
            print(f"   ✅ {strategy} - 已保留")
        else:
            print(f"   ❌ {strategy} - 意外丢失")
    
    print()
    
    # 4. 检查默认策略设置
    print("4. 默认策略检查：")
    if '"default_strategy": "阿水一式"' in content:
        print("   ✅ 默认策略已更新为阿水一式")
    else:
        print("   ❌ 默认策略未正确更新")
    
    print()
    
    # 5. 统计当前策略数量
    print("5. 策略统计：")
    strategy_pattern = r'"([^"]+)":\s*\['
    strategy_matches = re.findall(strategy_pattern, content)
    
    # 过滤出真正的策略（排除其他配置项）
    actual_strategies = []
    for match in strategy_matches:
        if any(keyword in match for keyword in ["勝率", "反彈", "阿水", "策略"]):
            actual_strategies.append(match)
    
    print(f"   📊 当前策略数量: {len(actual_strategies)}")
    print(f"   📋 策略列表:")
    for strategy in actual_strategies:
        print(f"      - {strategy}")
    
    print()
    
    # 6. 总结
    print("📈 总结：")
    if not strategies_found and not conditions_found:
        print("   🎉 ✅ 所有不成熟的策略和条件已成功移除！")
        print("   🎯 系统现在只包含成熟、有详细说明的策略")
        return True
    else:
        print("   ⚠️  仍有部分内容需要清理：")
        if strategies_found:
            print(f"      - 策略: {', '.join(strategies_found)}")
        if conditions_found:
            print(f"      - 条件: {', '.join(conditions_found)}")
        return False

def main():
    """主函数"""
    print("🚀 启动策略移除验证")
    
    success = test_strategy_removal()
    
    print("=" * 60)
    if success:
        print("🏁 验证完成：策略清理成功！")
        print("✨ 系统现在更加精简和专业")
    else:
        print("🏁 验证完成：仍需进一步清理")
    
    return success

if __name__ == "__main__":
    main()
