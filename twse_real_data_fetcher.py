#!/usr/bin/env python3
"""
台灣證券交易所真實數據獲取器
使用官方API獲取真實的股票排行榜數據
"""

import requests
import json
import logging
from datetime import datetime, timedelta
import time
import random

class TWSERealDataFetcher:
    """台灣證券交易所真實數據獲取器"""
    
    def __init__(self):
        self.base_url = "https://www.twse.com.tw"
        self.session = requests.Session()
        self.setup_session()
        
        # 股票名稱映射（常見股票）
        self.stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科', '2412': '中華電',
            '2881': '富邦金', '1301': '台塑', '2382': '廣達', '3008': '大立光',
            '2002': '中鋼', '2886': '兆豐金', '2891': '中信金', '2892': '第一金',
            '2883': '開發金', '2884': '玉山金', '2885': '元大金', '2887': '台新金',
            '2888': '新光金', '2889': '國票金', '2890': '永豐金', '2303': '聯電',
            '2308': '台達電', '2311': '日月光', '2327': '國巨', '2357': '華碩',
            '2379': '瑞昱', '2395': '研華', '2408': '南亞科', '2409': '友達',
            '2474': '可成', '2492': '華新科', '2498': '宏達電', '2603': '長榮'
        }
        
        logging.info("✅ TWSE真實數據獲取器初始化完成")
    
    def setup_session(self):
        """設置session"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.twse.com.tw/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.session.headers.update(headers)

        # 禁用SSL驗證以避免證書問題
        self.session.verify = False

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    def get_market_data(self, limit=20):
        """獲取市場數據"""
        try:
            # 獲取今天的日期
            today = datetime.now()
            date_str = today.strftime('%Y%m%d')
            
            # TWSE API URL
            url = f"{self.base_url}/exchangeReport/MI_INDEX"
            params = {
                'response': 'json',
                'date': date_str,
                'type': 'ALL'
            }
            
            logging.info(f"🔍 獲取TWSE市場數據: {date_str}")
            
            # 添加隨機延遲
            time.sleep(random.uniform(1, 2))
            
            response = self.session.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            if response.status_code == 200:
                data = response.json()
                return self._parse_market_data(data, limit)
            else:
                logging.warning(f"⚠️ TWSE API狀態碼: {response.status_code}")
                return []
                
        except Exception as e:
            logging.error(f"❌ 獲取TWSE市場數據失敗: {e}")
            return []
    
    def _parse_market_data(self, data, limit):
        """解析市場數據"""
        try:
            if 'data9' not in data:
                logging.warning("⚠️ TWSE API響應中沒有data9字段")
                return []
            
            stock_data = data['data9']
            ranking_data = []
            
            for i, stock in enumerate(stock_data[:limit]):
                try:
                    if len(stock) < 9:
                        continue
                    
                    stock_code = stock[0].strip()
                    stock_name = stock[1].strip()
                    
                    # 只處理4位數的股票代碼
                    if not stock_code.isdigit() or len(stock_code) != 4:
                        continue
                    
                    # 解析價格數據
                    current_price = self._safe_float(stock[8])  # 收盤價
                    change_amount = self._safe_float(stock[9]) if len(stock) > 9 else 0  # 漲跌
                    volume = stock[2] if len(stock) > 2 else '0'  # 成交股數
                    
                    # 計算漲跌幅
                    if current_price > 0 and change_amount != 0:
                        change_percent = (change_amount / (current_price - change_amount)) * 100
                    else:
                        change_percent = 0
                    
                    stock_info = {
                        'rank': i + 1,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'current_price': current_price,
                        'change_amount': change_amount,
                        'change_percent': round(change_percent, 2),
                        'volume': volume,
                        'ranking_type': 'market_data',
                        'timestamp': datetime.now().strftime('%H:%M:%S'),
                        'source': 'twse_official'
                    }
                    
                    ranking_data.append(stock_info)
                    
                except Exception as e:
                    continue
            
            if ranking_data:
                logging.info(f"✅ 成功解析 {len(ranking_data)} 筆TWSE官方數據")
            
            return ranking_data
            
        except Exception as e:
            logging.error(f"❌ 解析TWSE市場數據失敗: {e}")
            return []
    
    def get_ranking_by_type(self, ranking_type, limit=20):
        """根據類型獲取排行榜"""
        try:
            # 先獲取基礎市場數據
            market_data = self.get_market_data(100)  # 獲取更多數據用於排序
            
            if not market_data:
                return []
            
            # 根據排行榜類型排序
            if ranking_type == 'volume':
                # 成交量排行 - 按成交量排序
                sorted_data = sorted(market_data, 
                                   key=lambda x: self._parse_volume(x.get('volume', '0')), 
                                   reverse=True)
            elif ranking_type == 'change-up':
                # 漲幅排行 - 只取正漲幅，按漲跌幅排序
                positive_data = [x for x in market_data if x.get('change_percent', 0) > 0]
                sorted_data = sorted(positive_data, 
                                   key=lambda x: x.get('change_percent', 0), 
                                   reverse=True)
            elif ranking_type == 'change-down':
                # 跌幅排行 - 只取負跌幅，按漲跌幅排序
                negative_data = [x for x in market_data if x.get('change_percent', 0) < 0]
                sorted_data = sorted(negative_data, 
                                   key=lambda x: x.get('change_percent', 0))
            else:
                # 默認按成交量排序
                sorted_data = sorted(market_data, 
                                   key=lambda x: self._parse_volume(x.get('volume', '0')), 
                                   reverse=True)
            
            # 重新設置排名並限制數量
            result = []
            for i, stock in enumerate(sorted_data[:limit]):
                stock['rank'] = i + 1
                stock['ranking_type'] = ranking_type
                result.append(stock)
            
            logging.info(f"✅ 成功獲取 {len(result)} 筆 {ranking_type} 排行榜數據")
            return result
            
        except Exception as e:
            logging.error(f"❌ 獲取 {ranking_type} 排行榜失敗: {e}")
            return []
    
    def _parse_volume(self, volume_str):
        """解析成交量字符串為數字"""
        try:
            if not volume_str or volume_str == '--':
                return 0
            # 移除逗號並轉換為整數
            return int(str(volume_str).replace(',', ''))
        except (ValueError, TypeError):
            return 0
    
    def _safe_float(self, value):
        """安全轉換為浮點數"""
        try:
            if value is None or value == '' or value == '--':
                return 0.0
            # 移除逗號
            clean_value = str(value).replace(',', '').strip()
            return float(clean_value)
        except (ValueError, TypeError):
            return 0.0

def test_twse_real_data():
    """測試TWSE真實數據獲取器"""
    print("🧪 測試TWSE真實數據獲取器")
    print("=" * 50)
    
    fetcher = TWSERealDataFetcher()
    
    # 測試三種排行榜
    ranking_types = [
        ('volume', '成交量排行'),
        ('change-up', '漲幅排行'),
        ('change-down', '跌幅排行')
    ]
    
    for ranking_type, chinese_name in ranking_types:
        print(f"\n📊 測試 {chinese_name} ({ranking_type})...")
        
        data = fetcher.get_ranking_by_type(ranking_type, limit=5)
        
        if data:
            print(f"✅ 成功獲取 {len(data)} 筆真實數據")
            
            # 顯示前3筆數據
            for i, stock in enumerate(data[:3]):
                rank = stock.get('rank', i+1)
                code = stock.get('stock_code', 'N/A')
                name = stock.get('stock_name', 'N/A')
                price = stock.get('current_price', 0)
                change_pct = stock.get('change_percent', 0)
                volume = stock.get('volume', '0')
                source = stock.get('source', 'unknown')
                
                print(f"   {rank}. {code} {name}")
                print(f"      價格: {price:.2f}, 漲跌幅: {change_pct:+.2f}%, 成交量: {volume}")
                print(f"      數據源: {source}")
        else:
            print(f"❌ 無法獲取 {chinese_name} 真實數據")

if __name__ == '__main__':
    test_twse_real_data()
