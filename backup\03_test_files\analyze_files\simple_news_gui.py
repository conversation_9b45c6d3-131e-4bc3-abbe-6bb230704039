#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡潔版財經新聞爬蟲GUI - 大字體高對比度
"""

import sys
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from datetime import datetime
import threading

class SimpleNewsCrawlerDialog(QDialog):
    """簡潔版新聞爬蟲對話框"""
    
    def __init__(self, parent=None, stock_code=""):
        super().__init__(parent)
        self.stock_code = stock_code
        self.setup_ui()
        
    def setup_ui(self):
        """設置UI"""
        self.setWindowTitle("📰 財經新聞爬蟲")
        
        # 設置視窗大小
        self.setMinimumSize(1000, 800)
        self.resize(1200, 900)
        
        # 設置視窗標誌
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )
        
        # 超大字體高對比度樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #000000;
                color: #ffffff;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                font-size: 20px;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
                font-weight: bold;
                font-size: 20px;
                padding: 8px;
            }
            QLineEdit {
                background-color: #333333;
                color: #ffffff;
                border: 4px solid #666666;
                padding: 20px;
                border-radius: 10px;
                font-size: 20px;
                font-weight: bold;
                min-height: 30px;
            }
            QLineEdit:focus {
                border: 4px solid #00ffff;
                background-color: #444444;
            }
            QSpinBox {
                background-color: #333333;
                color: #ffffff;
                border: 4px solid #666666;
                padding: 20px;
                border-radius: 10px;
                font-size: 20px;
                font-weight: bold;
                min-height: 30px;
            }
            QSpinBox:focus {
                border: 4px solid #00ffff;
            }
            QTextEdit {
                background-color: #111111;
                color: #00ff00;
                border: 4px solid #666666;
                border-radius: 10px;
                font-family: 'Consolas', monospace;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                line-height: 1.8;
            }
            QPushButton {
                background-color: #0066cc;
                color: #ffffff;
                font-weight: bold;
                padding: 25px 40px;
                border: none;
                border-radius: 12px;
                font-size: 22px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #0088ff;
            }
            QPushButton:pressed {
                background-color: #004499;
            }
            QFrame {
                background-color: #222222;
                border: 3px solid #555555;
                border-radius: 15px;
                padding: 25px;
                margin: 15px;
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("📰 財經新聞爬蟲")
        title_label.setFont(QFont("Microsoft JhengHei", 36, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            color: #00ffff;
            font-size: 36px;
            font-weight: bold;
            padding: 20px;
            margin: 20px;
        """)
        layout.addWidget(title_label)
        
        # 設定區域
        settings_frame = QFrame()
        settings_layout = QVBoxLayout(settings_frame)
        
        # 股票代碼
        stock_layout = QHBoxLayout()
        stock_label = QLabel("股票代碼:")
        stock_label.setMinimumWidth(150)
        stock_layout.addWidget(stock_label)
        
        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("輸入4位數股票代碼，例如: 2330")
        if self.stock_code:
            self.stock_input.setText(self.stock_code)
        stock_layout.addWidget(self.stock_input)
        settings_layout.addLayout(stock_layout)
        
        # 爬取天數
        days_layout = QHBoxLayout()
        days_label = QLabel("爬取天數:")
        days_label.setMinimumWidth(150)
        days_layout.addWidget(days_label)
        
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 30)
        self.days_spinbox.setValue(3)
        self.days_spinbox.setSuffix(" 天")
        days_layout.addWidget(self.days_spinbox)
        settings_layout.addLayout(days_layout)
        
        layout.addWidget(settings_frame)
        
        # 狀態顯示
        status_label = QLabel("執行狀態:")
        status_label.setStyleSheet("color: #ffff00; font-size: 24px; font-weight: bold;")
        layout.addWidget(status_label)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 開始爬取按鈕
        self.start_btn = QPushButton("🚀 開始爬取")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #00aa00;
                color: #ffffff;
                font-weight: bold;
                padding: 25px 40px;
                border: none;
                border-radius: 12px;
                font-size: 22px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #00cc00;
            }
            QPushButton:pressed {
                background-color: #008800;
            }
        """)
        self.start_btn.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.start_btn)
        
        # 查看資料庫按鈕
        self.view_btn = QPushButton("📊 查看資料庫")
        self.view_btn.clicked.connect(self.view_database)
        button_layout.addWidget(self.view_btn)
        
        # 關閉按鈕
        self.close_btn = QPushButton("❌ 關閉")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #cc0000;
                color: #ffffff;
                font-weight: bold;
                padding: 25px 40px;
                border: none;
                border-radius: 12px;
                font-size: 22px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #ff0000;
            }
            QPushButton:pressed {
                background-color: #990000;
            }
        """)
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化狀態
        self.add_status_message("💡 系統已準備就緒，請設定參數後開始爬取")
        
    def add_status_message(self, message):
        """添加狀態訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
    def start_crawling(self):
        """開始爬取新聞"""
        stock_code = self.stock_input.text().strip()
        if not stock_code:
            QMessageBox.warning(self, "輸入錯誤", "請輸入股票代碼")
            return
            
        if not stock_code.isdigit() or len(stock_code) != 4:
            QMessageBox.warning(self, "格式錯誤", "股票代碼應為4位數字")
            return
        
        days = self.days_spinbox.value()
        
        self.add_status_message(f"🚀 開始爬取 {stock_code} 最近 {days} 天的新聞...")
        
        # 在背景執行爬取
        def crawl_worker():
            try:
                from cnyes_news_api_crawler import CnyesNewsSpider
                spider = CnyesNewsSpider()
                
                self.add_status_message("🔍 正在連接鉅亨網API...")
                
                # 爬取新聞
                max_pages = max(1, days // 2)
                news_count = spider.crawl_news(
                    max_pages=max_pages,
                    stock_code=stock_code,
                    days=days
                )
                
                self.add_status_message(f"✅ 爬取完成！共處理 {news_count} 筆新聞")
                
                # 查詢結果
                news_list = spider.get_news_by_stock(stock_code, days=days)
                self.add_status_message(f"📊 找到 {len(news_list)} 筆 {stock_code} 相關新聞")
                
            except Exception as e:
                self.add_status_message(f"❌ 爬取失敗: {str(e)}")
                
        # 啟動背景執行緒
        thread = threading.Thread(target=crawl_worker)
        thread.daemon = True
        thread.start()
        
    def view_database(self):
        """查看資料庫"""
        try:
            from cnyes_news_api_crawler import CnyesNewsSpider
            spider = CnyesNewsSpider()
            
            stock_code = self.stock_input.text().strip()
            if stock_code:
                news_list = spider.get_news_by_stock(stock_code, days=30)
                if news_list:
                    self.add_status_message(f"📊 資料庫中有 {len(news_list)} 筆 {stock_code} 的新聞")
                    
                    # 顯示最新新聞
                    for i, news in enumerate(news_list[:3]):
                        title = news[2][:80] + "..." if len(news[2]) > 80 else news[2]
                        self.add_status_message(f"  📰 {title}")
                        
                else:
                    self.add_status_message(f"📊 資料庫中沒有 {stock_code} 的新聞")
            else:
                self.add_status_message("📊 請先輸入股票代碼")
                
        except Exception as e:
            self.add_status_message(f"❌ 查看資料庫失敗: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 測試對話框
    dialog = SimpleNewsCrawlerDialog(stock_code="2330")
    dialog.show()
    
    sys.exit(app.exec())
