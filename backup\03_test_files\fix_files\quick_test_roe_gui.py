#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速測試ROE下載器GUI
直接啟動ROE下載器界面進行測試
"""

def test_roe_gui():
    """測試ROE下載器GUI"""
    print("🚀 啟動ROE下載器GUI測試")
    print("=" * 50)
    
    try:
        from roe_data_downloader_gui import ROEDataDownloaderGUI
        
        print("📱 創建ROE下載器界面...")
        downloader = ROEDataDownloaderGUI()
        
        print("✅ ROE下載器界面已創建")
        print("💡 界面即將顯示，您可以測試所有功能")
        print("🔧 測試項目:")
        print("  • 年份選擇")
        print("  • 資料範圍設定")
        print("  • 儲存選項")
        print("  • 開始下載功能")
        print("  • 查看資料功能")
        print("  • 開啟資料夾功能")
        
        print("\n⚠️ 注意: 關閉界面後測試將結束")
        
        # 運行GUI
        downloader.run()
        
        print("✅ ROE下載器測試完成")
        return True
        
    except Exception as e:
        print(f"❌ ROE下載器啟動失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_roe_gui()
