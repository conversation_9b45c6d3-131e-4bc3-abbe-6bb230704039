#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試YoY和MoM綜合排名計算功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI

def test_growth_rate_parsing():
    """測試成長率解析功能"""
    print("📊 測試成長率解析功能")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    test_cases = [
        ('+26.86%', 26.86, '正成長率'),
        ('-17.72%', -17.72, '負成長率'),
        ('0.00%', 0.00, '零成長率'),
        ('N/A', None, 'N/A值'),
        ('', None, '空字串'),
        (None, None, 'None值'),
        ('abc%', None, '非數字'),
        ('15.5%', 15.5, '小數成長率'),
    ]
    
    print("測試案例:")
    for i, (input_val, expected, description) in enumerate(test_cases, 1):
        result = gui.parse_growth_rate(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{i}. {description}: '{input_val}' -> {result} {status}")
        if result != expected:
            print(f"   預期: {expected}, 實際: {result}")
    
    app.quit()

def test_comprehensive_score_calculation():
    """測試綜合評分計算"""
    print("\n🎯 測試綜合評分計算")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    test_cases = [
        (26.86, -17.72, '台積電類型：高YoY，負MoM'),
        (50.0, 20.0, '高成長股：雙正成長'),
        (-10.0, -5.0, '衰退股：雙負成長'),
        (0.0, 0.0, '平穩股：零成長'),
        (100.0, -50.0, '極端案例：超高YoY，超低MoM'),
    ]
    
    print("測試案例 (YoY權重60%, MoM權重40%):")
    for i, (yoy, mom, description) in enumerate(test_cases, 1):
        score = gui.calculate_single_comprehensive_score(yoy, mom)
        expected = yoy * 0.6 + mom * 0.4
        status = "✅" if abs(score - expected) < 0.01 else "❌"
        print(f"{i}. {description}")
        print(f"   YoY: {yoy:+.2f}%, MoM: {mom:+.2f}%")
        print(f"   綜合評分: {score:.2f} (預期: {expected:.2f}) {status}")
    
    app.quit()

def test_monthly_revenue_data_processing():
    """測試月營收資料處理"""
    print("\n📊 測試月營收資料處理")
    
    # 檢查PKL檔案
    pkl_files = [
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl'
    ]
    
    for pkl_path in pkl_files:
        print(f"\n📁 檢查檔案: {pkl_path}")
        
        if not os.path.exists(pkl_path):
            print(f"❌ 檔案不存在")
            continue
        
        print(f"✅ 檔案存在")
        
        try:
            df = pd.read_pickle(pkl_path)
            print(f"📊 資料形狀: {df.shape}")
            
            # 重置索引
            if isinstance(df.index, pd.MultiIndex):
                df_reset = df.reset_index()
                print(f"📋 多層索引已重置")
            else:
                df_reset = df.copy()
                print(f"📋 單層索引")
            
            print(f"📋 欄位: {list(df_reset.columns)}")
            
            # 檢查關鍵欄位
            required_columns = ['當月營收', '上月營收', '去年當月營收', 'stock_id']
            missing_columns = [col for col in required_columns if col not in df_reset.columns]
            
            if missing_columns:
                print(f"❌ 缺少必要欄位: {missing_columns}")
                continue
            else:
                print(f"✅ 包含必要欄位: {required_columns}")
            
            # 測試成長率計算
            app = QApplication(sys.argv)
            gui = FinlabGUI()
            
            print(f"\n🧮 測試成長率計算:")
            sample_data = df_reset.head(5).copy()
            processed_data = gui.calculate_growth_rates_for_ranking(sample_data)
            
            for i, row in processed_data.iterrows():
                stock_id = row.get('stock_id', 'N/A')
                yoy = row.get('YoY_數值', 'N/A')
                mom = row.get('MoM_數值', 'N/A')
                print(f"  {stock_id}: YoY={yoy:.2f}%, MoM={mom:.2f}%" if isinstance(yoy, (int, float)) and isinstance(mom, (int, float)) else f"  {stock_id}: YoY={yoy}, MoM={mom}")
            
            app.quit()
            
            # 只處理第一個找到的檔案
            return processed_data
            
        except Exception as e:
            print(f"❌ 處理失敗: {e}")
            continue
    
    return None

def test_comprehensive_ranking():
    """測試綜合排名計算"""
    print("\n🏆 測試綜合排名計算")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 測試股票和成長率
    test_cases = [
        ('2330', '+26.86%', '-17.72%', '台積電：高YoY，負MoM'),
        ('2317', '+15.00%', '+5.00%', '鴻海：中等雙正成長'),
        ('8021', '+50.00%', '+25.00%', '尖點：高雙正成長'),
        ('1234', '-10.00%', '-5.00%', '測試股票：雙負成長'),
    ]
    
    print("測試案例:")
    for i, (stock_code, yoy, mom, description) in enumerate(test_cases, 1):
        print(f"\n{i}. {description}")
        print(f"   股票代碼: {stock_code}")
        print(f"   YoY: {yoy}, MoM: {mom}")
        
        # 計算預期綜合評分
        yoy_val = gui.parse_growth_rate(yoy)
        mom_val = gui.parse_growth_rate(mom)
        expected_score = gui.calculate_single_comprehensive_score(yoy_val, mom_val)
        print(f"   預期綜合評分: {expected_score:.2f}")
        
        try:
            ranking = gui.calculate_monthly_revenue_ranking(stock_code, yoy, mom)
            if ranking:
                print(f"   ✅ 計算排名: 第 {ranking} 名")
            else:
                print(f"   ❌ 無法計算排名")
        except Exception as e:
            print(f"   ❌ 計算失敗: {e}")
    
    app.quit()

def test_complete_process():
    """測試完整的資料處理流程"""
    print("\n🔄 測試完整的資料處理流程")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    # 模擬從資料庫查詢的結果
    test_result = ('2330', '台積電', '2025-07-01', '263708978', '221850000', '207870000')
    
    print("模擬資料庫查詢結果:")
    print(f"  股票ID: {test_result[0]}")
    print(f"  股票名稱: {test_result[1]}")
    print(f"  日期: {test_result[2]}")
    print(f"  當月營收: {test_result[3]}")
    print(f"  上月營收: {test_result[4]}")
    print(f"  去年同月營收: {test_result[5]}")
    
    try:
        stock_data = gui._process_monthly_revenue_result(test_result, '2330', '台積電')
        
        if stock_data:
            print("\n✅ 處理結果:")
            for key, value in stock_data.items():
                print(f"  {key}: {value}")
            
            # 特別檢查排名和成長率
            ranking = stock_data.get('排名', 'N/A')
            yoy = stock_data.get('YoY%', 'N/A')
            mom = stock_data.get('MoM%', 'N/A')
            
            print(f"\n🎯 關鍵指標:")
            print(f"  YoY%: {yoy}")
            print(f"  MoM%: {mom}")
            print(f"  綜合排名: 第 {ranking} 名" if ranking != 'N/A' else "  綜合排名: 未計算")
            
            if ranking != 'N/A':
                print(f"🏆 排名計算成功！基於YoY和MoM的綜合評分")
            else:
                print(f"❌ 排名計算失敗")
        else:
            print("\n❌ 資料處理失敗")
            
    except Exception as e:
        print(f"\n❌ 處理過程發生錯誤: {e}")
    
    app.quit()

def main():
    """主函數"""
    print("🚀 YoY和MoM綜合排名計算功能測試")
    print("=" * 60)
    
    # 測試成長率解析
    test_growth_rate_parsing()
    
    # 測試綜合評分計算
    test_comprehensive_score_calculation()
    
    # 測試資料處理
    test_monthly_revenue_data_processing()
    
    # 測試綜合排名
    test_comprehensive_ranking()
    
    # 測試完整流程
    test_complete_process()
    
    print("\n" + "=" * 60)
    print("🎉 測試完成！")
    
    print("\n修復內容總結:")
    print("✅ 1. YoY和MoM綜合排名：基於成長率的綜合評分排序")
    print("✅ 2. 綜合評分公式：YoY權重60% + MoM權重40%")
    print("✅ 3. 成長率解析：處理各種格式的成長率字串")
    print("✅ 4. 動態排名計算：即使不在前100名也顯示實際排名")
    print("✅ 5. 完整資料流：從資料庫到排名的完整處理")
    
    print("\n預期效果:")
    print("🏆 高YoY+高MoM：排名靠前（如第1-10名）")
    print("🏆 高YoY+負MoM：中等排名（如第20-50名）")
    print("🏆 負YoY+負MoM：排名靠後（如第200-500名）")
    print("🏆 排名反映成長性：不是營收規模，而是成長表現")
    
    print("\n請啟動主程式測試實際效果：")
    print("python O3mh_gui_v21_optimized.py")

if __name__ == "__main__":
    main()
