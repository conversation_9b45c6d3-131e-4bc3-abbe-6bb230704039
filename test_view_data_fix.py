#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試查看數據功能的修復
"""

import sqlite3
import pandas as pd
import os

def test_view_data_logic():
    """測試查看數據的邏輯"""
    
    print("🔍 測試查看數據功能修復")
    print("=" * 60)
    
    # 模擬查看數據的邏輯
    db_files = {
        'market_index': 'market_index.db',
        'margin_trading': 'margin_trading.db', 
        'historical_index': 'market_historical.db'
    }
    
    available_data = {}
    
    for data_type, db_file in db_files.items():
        db_path = f"D:/Finlab/history/tables/{db_file}"
        
        print(f"\n📋 檢查 {data_type} ({db_file})")
        
        if not os.path.exists(db_path):
            print(f"❌ 檔案不存在: {db_path}")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            
            # 使用正確的表格名稱
            if data_type == 'market_index':
                df = pd.read_sql_query("SELECT * FROM market_index_info ORDER BY crawl_time DESC LIMIT 100", conn)
                table_name = "market_index_info"
            elif data_type == 'margin_trading':
                df = pd.read_sql_query("SELECT * FROM margin_trading_info ORDER BY crawl_time DESC LIMIT 100", conn)
                table_name = "margin_trading_info"
            elif data_type == 'historical_index':
                df = pd.read_sql_query("SELECT * FROM market_historical_index ORDER BY crawl_time DESC LIMIT 100", conn)
                table_name = "market_historical_index"
            
            print(f"✅ 成功連接資料庫")
            print(f"📊 表格名稱: {table_name}")
            print(f"📊 資料筆數: {len(df)}")
            
            if not df.empty:
                available_data[data_type] = df
                print(f"✅ 成功讀取 {len(df)} 筆資料")
                print(f"📋 欄位: {list(df.columns)}")
                
                # 顯示前3筆資料的摘要
                print(f"📋 前3筆資料摘要:")
                for i in range(min(3, len(df))):
                    row_summary = str(df.iloc[i].to_dict())[:100] + "..."
                    print(f"   {i+1}. {row_summary}")
            else:
                print(f"⚠️ 表格為空")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取失敗: {e}")
            continue
    
    print(f"\n📊 總結:")
    print(f"可用數據集: {len(available_data)}")
    
    if available_data:
        print(f"✅ 查看數據功能應該正常工作")
        for data_type in available_data.keys():
            data_names = {
                'market_index': '市場指數資訊',
                'margin_trading': '融資融券統計',
                'historical_index': '歷史指數資料'
            }
            print(f"   - {data_names.get(data_type, data_type)}: {len(available_data[data_type])} 筆")
    else:
        print(f"❌ 沒有可用的數據")
        
        # 提供診斷信息
        print(f"\n🔍 診斷信息:")
        for data_type, db_file in db_files.items():
            db_path = f"D:/Finlab/history/tables/{db_file}"
            if os.path.exists(db_path):
                print(f"✅ {db_file} 檔案存在")
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [t[0] for t in cursor.fetchall()]
                    print(f"   表格: {', '.join(tables)}")
                    
                    # 檢查每個表格的資料筆數
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"   {table}: {count} 筆")
                    
                    conn.close()
                except Exception as e:
                    print(f"   錯誤: {e}")
            else:
                print(f"❌ {db_file} 檔案不存在")

if __name__ == "__main__":
    test_view_data_logic()
