#!/usr/bin/env python3
"""
測試數據庫配置對話框的自定義標題欄功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_config_titlebar():
    """測試數據庫配置對話框的標題欄功能"""
    try:
        print("🧪 測試數據庫配置對話框標題欄功能...")
        
        # 導入對話框
        from dialogs.database_config_dialog import DatabaseConfigDialog
        
        # 創建應用程式
        app = QApplication([])
        
        # 創建對話框實例
        dialog = DatabaseConfigDialog()
        print("✅ 數據庫配置對話框創建成功")
        
        # 檢查自定義標題欄功能
        required_methods = [
            'create_custom_title_bar',
            'create_window_controls',
            'toggle_maximize',
            'title_bar_mouse_press',
            'title_bar_mouse_move'
        ]
        
        for method in required_methods:
            if hasattr(dialog, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        # 檢查UI元素
        ui_elements = [
            ('title_label', '標題標籤'),
            ('maximize_btn', '最大化按鈕'),
            ('main_layout', '主佈局'),
            ('tab_widget', '標籤頁組件')
        ]
        
        for attr, desc in ui_elements:
            if hasattr(dialog, attr):
                print(f"✅ {desc} 存在")
            else:
                print(f"❌ {desc} 不存在")
                return False
        
        # 檢查窗口標誌
        flags = dialog.windowFlags()
        if flags & dialog.windowFlags().FramelessWindowHint:
            print("✅ 無邊框窗口設置正確")
        else:
            print("⚠️ 窗口可能仍有系統標題欄")
        
        # 顯示對話框進行視覺測試
        print("\n📋 功能說明:")
        print("   • ✅ 自定義標題欄已添加")
        print("   • ✅ 最小化按鈕 (🗕)")
        print("   • ✅ 最大化/還原按鈕 (🗖/🗗)")
        print("   • ✅ 關閉按鈕 (🗙)")
        print("   • ✅ 標題欄可拖拽移動窗口")
        print("   • ✅ 深色主題設計")
        
        print("\n🎯 使用說明:")
        print("   1. 點擊 🗕 最小化窗口")
        print("   2. 點擊 🗖 最大化窗口，再點擊 🗗 還原")
        print("   3. 拖拽標題欄移動窗口")
        print("   4. 點擊 🗙 關閉窗口")
        
        # 顯示對話框
        dialog.show()
        print("\n✅ 對話框已顯示，請測試標題欄功能")
        
        # 運行應用程式
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_window_controls():
    """測試窗口控制功能"""
    print("\n📊 窗口控制功能測試:")
    print("=" * 50)
    
    try:
        from dialogs.database_config_dialog import DatabaseConfigDialog
        
        # 創建對話框實例（不顯示）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        dialog = DatabaseConfigDialog()
        
        # 測試最大化切換
        print("🔍 測試最大化功能...")
        initial_state = dialog.isMaximized()
        print(f"   初始狀態: {'最大化' if initial_state else '正常'}")
        
        # 模擬切換
        dialog.toggle_maximize()
        after_toggle = dialog.isMaximized()
        print(f"   切換後狀態: {'最大化' if after_toggle else '正常'}")
        
        if initial_state != after_toggle:
            print("✅ 最大化切換功能正常")
        else:
            print("⚠️ 最大化切換可能需要窗口顯示後才能正常工作")
        
        # 檢查按鈕文字
        if hasattr(dialog, 'maximize_btn'):
            btn_text = dialog.maximize_btn.text()
            print(f"   最大化按鈕文字: {btn_text}")
            
            if btn_text in ["🗖", "🗗"]:
                print("✅ 按鈕文字設置正確")
            else:
                print("⚠️ 按鈕文字可能有問題")
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口控制測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🎯 數據庫配置對話框標題欄測試")
    print("=" * 50)
    
    tests = [
        ("標題欄功能測試", test_database_config_titlebar),
        ("窗口控制測試", test_window_controls)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通過")
        else:
            print(f"❌ {test_name} 失敗")
    
    print("\n" + "=" * 50)
    print(f"🎉 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎊 數據庫配置對話框標題欄功能完成！")
        print("\n📋 新增功能:")
        print("   • ✅ 自定義標題欄設計")
        print("   • ✅ 最小化、最大化、關閉按鈕")
        print("   • ✅ 標題欄拖拽功能")
        print("   • ✅ 深色主題整合")
        print("   • ✅ 響應式按鈕狀態")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")

if __name__ == '__main__':
    main()
