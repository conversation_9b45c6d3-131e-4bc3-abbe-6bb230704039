#!/usr/bin/env python3
"""
測試策略說明是否正確匹配
"""

def test_strategy_descriptions():
    """測試策略說明匹配"""
    try:
        print("🧪 測試策略說明匹配")
        print("=" * 50)
        
        # 模擬策略說明字典（從主程式中提取關鍵部分）
        strategy_details = {
            "阿水一式": "阿水一式策略說明...",
            "阿水二式": "阿水二式策略說明...", 
            "藏獒": "藏獒策略說明...",
            "CANSLIM量價齊升": "CANSLIM策略說明...",
            "膽小貓": "膽小貓策略說明..."  # 修正後的鍵名
        }
        
        usage_guides = {
            "阿水一式": "阿水一式使用指南...",
            "阿水二式": "阿水二式使用指南...",
            "藏獒": "藏獒使用指南...", 
            "CANSLIM量價齊升": "CANSLIM使用指南...",
            "膽小貓": "膽小貓使用指南..."  # 修正後的鍵名
        }
        
        # 策略列表（從主程式中提取）
        strategies = {
            "勝率73.45%": [{"type": "ma_trend"}],
            "破底反彈高量": [{"type": "break_bottom_rebound"}],
            "阿水一式": [{"type": "ashui_strategy"}],
            "阿水二式": [{"type": "ashui_short_strategy"}],
            "藏獒": [{"type": "tibetan_mastiff_strategy"}],
            "CANSLIM量價齊升": [{"type": "canslim_strategy"}],
            "膽小貓": [{"type": "timid_cat_strategy"}]  # 策略列表中的鍵名
        }
        
        print("📊 策略名稱匹配檢查:")
        
        all_match = True
        
        for strategy_name in strategies.keys():
            has_details = strategy_name in strategy_details
            has_usage = strategy_name in usage_guides
            
            status_details = "✅" if has_details else "❌"
            status_usage = "✅" if has_usage else "❌"
            
            print(f"{strategy_name}:")
            print(f"  策略說明: {status_details}")
            print(f"  使用指南: {status_usage}")
            
            if not has_details or not has_usage:
                all_match = False
        
        print(f"\n🎯 測試結果:")
        if all_match:
            print("✅ 所有策略名稱都正確匹配")
            print("✅ 策略說明和使用指南都有對應內容")
        else:
            print("❌ 部分策略名稱不匹配")
        
        # 特別檢查膽小貓策略
        print(f"\n🐱 膽小貓策略特別檢查:")
        timid_cat_name = "膽小貓"
        
        if timid_cat_name in strategies:
            print(f"✅ 策略列表中找到: {timid_cat_name}")
        else:
            print(f"❌ 策略列表中未找到: {timid_cat_name}")
            
        if timid_cat_name in strategy_details:
            print(f"✅ 策略說明中找到: {timid_cat_name}")
        else:
            print(f"❌ 策略說明中未找到: {timid_cat_name}")
            
        if timid_cat_name in usage_guides:
            print(f"✅ 使用指南中找到: {timid_cat_name}")
        else:
            print(f"❌ 使用指南中未找到: {timid_cat_name}")
        
        # 檢查策略條件類型
        print(f"\n🔧 策略條件類型檢查:")
        for strategy_name, conditions in strategies.items():
            condition_type = conditions[0]['type']
            print(f"{strategy_name}: {condition_type}")
        
        return all_match
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_strategy_descriptions()
    if success:
        print(f"\n🎉 策略說明匹配測試通過！")
        print(f"現在膽小貓策略說明應該能正確顯示了！")
    else:
        print(f"\n💥 策略說明匹配測試失敗！")
