#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
無GUI測試 - 完全避免Qt相關問題
只測試核心掃描功能
"""

import sys
import time
import logging
import os

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_direct_monitor():
    """直接測試監控器，不使用任何Qt組件"""
    print("直接監控器測試 - 完全無GUI")
    print("=" * 50)
    
    try:
        # 只導入監控器，不導入主程式
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        print("✅ 創建監控器...")
        monitor = PreMarketMonitor()
        
        print("🚀 執行掃描...")
        start_time = time.time()
        
        # 直接調用掃描方法
        results = monitor.run_full_scan()
        
        scan_time = time.time() - start_time
        
        if results:
            print(f"✅ 掃描成功，耗時 {scan_time:.2f}秒")
            
            # 詳細分析結果
            if isinstance(results, dict):
                print(f"📊 結果類型: dict")
                print(f"📊 結果鍵: {list(results.keys())}")
                
                total_items = 0
                for category, data in results.items():
                    if isinstance(data, dict) and data:
                        item_count = len(data)
                        total_items += item_count
                        print(f"   • {category}: {item_count} 項")
                        
                        # 顯示前2項數據
                        for i, (name, item_data) in enumerate(data.items()):
                            if i >= 2:
                                break
                            if isinstance(item_data, dict):
                                price = item_data.get('price', item_data.get('rate', 0))
                                change_pct = item_data.get('change_pct', 0)
                                print(f"     - {name}: {price} ({change_pct:+.2f}%)")
                
                print(f"📊 總計: {total_items} 項數據")
                
                # 檢查數據品質
                valid_items = 0
                for category, data in results.items():
                    if isinstance(data, dict):
                        for item_name, item_data in data.items():
                            if isinstance(item_data, dict):
                                price = item_data.get('price', item_data.get('rate', 0))
                                if price > 0:
                                    valid_items += 1
                
                print(f"📊 有效數據: {valid_items}/{total_items} 項 ({valid_items/total_items*100:.1f}%)")
                
                return True, total_items, valid_items
            else:
                print(f"⚠️ 結果格式異常: {type(results)}")
                return False, 0, 0
        else:
            print(f"❌ 掃描失敗，耗時 {scan_time:.2f}秒")
            return False, 0, 0
            
    except Exception as e:
        print(f"❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0

def test_multiple_runs():
    """測試多次運行，檢查穩定性"""
    print("\n多次運行穩定性測試")
    print("=" * 50)
    
    success_count = 0
    total_runs = 3
    
    for i in range(total_runs):
        print(f"\n第 {i+1} 次運行:")
        try:
            success, total_items, valid_items = test_direct_monitor()
            if success and valid_items > 0:
                success_count += 1
                print(f"   ✅ 成功 - {valid_items} 項有效數據")
            else:
                print(f"   ❌ 失敗")
        except Exception as e:
            print(f"   ❌ 異常: {e}")
        
        # 短暫等待
        if i < total_runs - 1:
            time.sleep(2)
    
    print(f"\n穩定性測試結果: {success_count}/{total_runs} 次成功")
    return success_count == total_runs

def main():
    """主測試函數"""
    print("無GUI核心功能測試")
    print("=" * 60)
    print("目標:")
    print("  • 完全避免Qt/GUI相關問題")
    print("  • 只測試核心掃描功能")
    print("  • 驗證數據獲取穩定性")
    print("  • 不使用任何UI組件")
    print()
    
    # 1. 單次測試
    success, total_items, valid_items = test_direct_monitor()
    
    if success and valid_items > 0:
        print(f"\n✅ 單次測試成功")
        
        # 2. 多次測試
        stability_ok = test_multiple_runs()
        
        print(f"\n🎯 最終測試結果:")
        print(f"   單次測試: {'✅ 成功' if success else '❌ 失敗'}")
        print(f"   穩定性測試: {'✅ 通過' if stability_ok else '❌ 失敗'}")
        print(f"   數據項目: {total_items}")
        print(f"   有效數據: {valid_items}")
        
        if success and stability_ok:
            print("\n🎉 核心功能完全正常！")
            print("   問題確實在於Qt/GUI部分")
            print("   建議解決方案:")
            print("   • 暫時禁用進度條和圖表功能")
            print("   • 只在掃描完成後更新界面")
            print("   • 避免在掃描過程中的UI操作")
            print("   • 檢查pyqtgraph相關的QPainter問題")
        else:
            print("\n⚠️ 需要進一步調試")
    else:
        print("\n❌ 核心功能有問題")
        print("   需要檢查監控器本身的實現")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 測試結束")
