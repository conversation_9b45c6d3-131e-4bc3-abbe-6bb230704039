#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium測試Yahoo Finance頁面
檢查是否需要JavaScript渲染
"""

def test_selenium_yahoo():
    """使用Selenium測試Yahoo頁面"""
    print("🚀 使用Selenium測試Yahoo Finance")
    print("=" * 50)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from bs4 import BeautifulSoup
        
        # 設置Chrome選項
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 無頭模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        print("🔧 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            stock_code = "2330"
            url = f'https://tw.stock.yahoo.com/quote/{stock_code}'
            print(f"📡 訪問URL: {url}")
            
            driver.get(url)
            
            # 等待頁面載入
            print("⏳ 等待頁面載入...")
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 額外等待JavaScript執行
            import time
            time.sleep(5)
            
            # 獲取頁面源碼
            page_source = driver.page_source
            print(f"✅ 頁面載入完成，內容長度: {len(page_source)}")
            
            # 檢查內容
            print(f"\n🔍 內容檢查:")
            print(f"  包含 '{stock_code}': {'✅' if stock_code in page_source else '❌'}")
            print(f"  包含 '台積電': {'✅' if '台積電' in page_source else '❌'}")
            print(f"  包含 'TSM': {'✅' if 'TSM' in page_source else '❌'}")
            
            # 解析HTML
            soup = BeautifulSoup(page_source, "html.parser")
            
            # 檢查標題
            title = soup.find('title')
            print(f"\n📊 頁面標題: {title.get_text() if title else '未找到'}")
            
            # 檢查CSS選擇器
            print(f"\n🎯 CSS選擇器檢查:")
            
            # 價格元素
            price_elements = soup.select('.Fz\\(32px\\)')
            print(f"  .Fz(32px) 元素: {len(price_elements)} 個")
            if price_elements:
                for i, elem in enumerate(price_elements[:3]):
                    text = elem.get_text().strip()
                    print(f"    {i+1}. '{text}' (class: {elem.get('class', [])})")
            
            # 漲跌元素
            change_elements = soup.select('.Fz\\(20px\\)')
            print(f"  .Fz(20px) 元素: {len(change_elements)} 個")
            if change_elements:
                for i, elem in enumerate(change_elements[:3]):
                    text = elem.get_text().strip()
                    print(f"    {i+1}. '{text}' (class: {elem.get('class', [])})")
            
            # 主容器
            main_container = soup.select('#main-0-QuoteHeader-Proxy')
            print(f"  #main-0-QuoteHeader-Proxy: {len(main_container)} 個")
            
            # 趨勢元素
            trend_up = soup.select('.C\\(\\$c-trend-up\\)')
            trend_down = soup.select('.C\\(\\$c-trend-down\\)')
            print(f"  趨勢上漲元素: {len(trend_up)} 個")
            print(f"  趨勢下跌元素: {len(trend_down)} 個")
            
            # 嘗試直接使用Selenium查找元素
            print(f"\n🔍 Selenium直接查找:")
            try:
                # 嘗試不同的選擇器
                selectors = [
                    '[data-symbol="2330"]',
                    '[class*="price"]',
                    '[class*="Price"]',
                    'span[class*="Fz"]',
                    'div[class*="quote"]',
                    'div[class*="Quote"]'
                ]
                
                for selector in selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        print(f"    {selector}: {len(elements)} 個元素")
                        if elements:
                            for i, elem in enumerate(elements[:2]):
                                try:
                                    text = elem.text.strip()
                                    if text:
                                        print(f"      {i+1}. '{text}'")
                                except:
                                    pass
                    except Exception as e:
                        print(f"    {selector}: 錯誤 - {e}")
                        
            except Exception as e:
                print(f"❌ Selenium查找失敗: {e}")
            
            # 保存頁面內容
            with open('selenium_yahoo_2330.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            print(f"\n💾 Selenium頁面內容已保存到: selenium_yahoo_2330.html")
            
            # 檢查是否成功獲取股票資訊
            success = (stock_code in page_source or '台積電' in page_source) and len(price_elements) > 0
            
            return success
            
        finally:
            driver.quit()
            print("🔧 瀏覽器已關閉")
            
    except ImportError:
        print("❌ Selenium未安裝，請執行: pip install selenium")
        return False
    except Exception as e:
        print(f"❌ Selenium測試失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_alternative_methods():
    """測試其他方法"""
    print("\n🔄 測試其他方法")
    print("=" * 30)
    
    # 方法1: 嘗試不同的URL格式
    print("📡 方法1: 測試不同URL格式")
    
    import requests
    
    urls = [
        "https://tw.stock.yahoo.com/quote/2330.TW",
        "https://finance.yahoo.com/quote/2330.TW",
        "https://tw.stock.yahoo.com/d/s/major_2330.html",
        "https://tw.stock.yahoo.com/quote/2330/profile"
    ]
    
    for url in urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"  {url}: {response.status_code} ({len(response.text)} chars)")
            if '2330' in response.text or '台積電' in response.text:
                print(f"    ✅ 包含股票資訊")
            else:
                print(f"    ❌ 不包含股票資訊")
        except Exception as e:
            print(f"  {url}: 錯誤 - {e}")
    
    # 方法2: 檢查是否有API接口
    print(f"\n📊 方法2: 檢查API接口")
    
    api_urls = [
        "https://tw.stock.yahoo.com/api/v1/quote/2330",
        "https://query1.finance.yahoo.com/v8/finance/chart/2330.TW",
        "https://query2.finance.yahoo.com/v1/finance/search?q=2330"
    ]
    
    for url in api_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"  {url}: {response.status_code}")
            if response.status_code == 200:
                content = response.text[:200] + "..." if len(response.text) > 200 else response.text
                print(f"    內容預覽: {content}")
        except Exception as e:
            print(f"  {url}: 錯誤 - {e}")

def main():
    """主函數"""
    print("🔍 Yahoo Finance深度測試")
    print("=" * 60)
    
    # Selenium測試
    selenium_success = test_selenium_yahoo()
    
    # 其他方法測試
    test_alternative_methods()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 30)
    
    print(f"Selenium測試: {'✅ 成功' if selenium_success else '❌ 失敗'}")
    
    if selenium_success:
        print("\n💡 建議:")
        print("  1. 使用Selenium進行動態內容抓取")
        print("  2. 整合到即時監控系統中")
        print("  3. 考慮使用無頭瀏覽器提升性能")
    else:
        print("\n💡 建議:")
        print("  1. 檢查Yahoo Finance是否更改了頁面結構")
        print("  2. 考慮使用其他數據源")
        print("  3. 檢查是否有反爬蟲機制")

if __name__ == "__main__":
    main()
