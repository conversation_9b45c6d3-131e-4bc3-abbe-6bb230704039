# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 完整的隱藏導入列表
hiddenimports = [
    # 系統核心
    'inspect', 'pydoc', 'doctest', 'difflib', 'importlib', 'importlib.util',
    'sqlite3', 'json', 'csv', 'datetime', 'calendar', 'time', 'threading',
    'concurrent.futures', 'logging', 'traceback', 'os', 'sys', 'random', 'warnings',
    
    # PyQt6 完整支援
    'PyQt6', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'PyQt6.sip',
    
    # 數據處理
    'pandas', 'numpy', 'numpy.core', 'numpy.core.numeric',
    
    # 網路和文件處理
    'requests', 'urllib3', 'bs4', 'beautifulsoup4', 'openpyxl',
    
    # 其他必要模組
    'setuptools', 'pkg_resources', 'itertools',
]

# 排除問題模組（包含 pyqtgraph）
excludes = [
    'tkinter', 'test', 'tests', 'unittest', 'pdb', 'PyQt5', 'PySide2', 'PySide6',
    'IPython', 'jupyter', 'notebook', 'twstock', 'yfinance', 'finlab', 'finmind',
    'talib', 'mplfinance', 'matplotlib', 'seaborn', 'pyqtgraph', 'xlsxwriter',
    'selenium', 'webdriver_manager', 'apscheduler', 'charts', 'config',
    'enhanced_dividend_crawler', 'integrated_strategy_help_dialog',
    'unified_monitor_manager', 'smart_trading_strategies', 'strategies', 'monitoring',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[], binaries=[], datas=[], hiddenimports=hiddenimports,
    hookspath=[], hooksconfig={}, runtime_hooks=[], excludes=excludes,
    win_no_prefer_redirects=False, win_private_assemblies=False,
    cipher=block_cipher, noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='StockAnalyzer_Final_Fixed', debug=False, bootloader_ignore_signals=False,
    strip=False, upx=True, upx_exclude=[], runtime_tmpdir=None, console=False,
    disable_windowed_traceback=False, argv_emulation=False,
    target_arch=None, codesign_identity=None, entitlements_file=None,
)
