#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 newprice.db 增量更新邏輯
"""

import sys
import os
from datetime import datetime, timedelta

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_newprice_incremental_logic():
    """測試 newprice 增量更新邏輯"""
    
    print("=" * 60)
    print("🧪 測試 newprice.db 增量更新邏輯")
    print("=" * 60)
    
    # 測試獲取增量日期範圍
    import sqlite3
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    
    if not os.path.exists(newprice_db):
        print(f"❌ newprice.db 不存在: {newprice_db}")
        return False
    
    try:
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 獲取最後日期
        cursor.execute('SELECT MAX(date) FROM stock_daily_data')
        last_date_str = cursor.fetchone()[0]
        
        # 獲取資料統計
        cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
        total_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data')
        stock_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 newprice.db 當前狀態:")
        print(f"   最後日期: {last_date_str}")
        print(f"   總資料筆數: {total_count:,}")
        print(f"   股票數量: {stock_count:,}")
        
        if last_date_str:
            last_date = datetime.strptime(last_date_str, '%Y-%m-%d')
            start_date = last_date + timedelta(days=1)
            end_date = start_date + timedelta(days=4)  # 5天測試
            
            print(f"\n🎯 計劃增量更新:")
            print(f"   開始日期: {start_date.strftime('%Y-%m-%d')}")
            print(f"   結束日期: {end_date.strftime('%Y-%m-%d')}")
            print(f"   更新天數: 5 天 (測試)")
            
            # 生成日期範圍
            dates = []
            current = start_date
            while current <= end_date:
                dates.append(current)
                current += timedelta(days=1)
            
            print(f"\n📅 生成的日期列表:")
            for i, date in enumerate(dates, 1):
                print(f"   {i}. {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
            
            print(f"\n✅ 增量更新邏輯測試通過")
            return True
        else:
            print("❌ 無法獲取最後日期")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_update_import():
    """測試 auto_update 模組導入"""
    
    print("\n" + "=" * 60)
    print("🧪 測試 auto_update 模組導入")
    print("=" * 60)
    
    try:
        # 測試導入 auto_update 中的函數
        from auto_update import get_newprice_incremental_date_range
        
        print("✅ 成功導入 get_newprice_incremental_date_range")
        
        # 測試函數執行
        date_range = get_newprice_incremental_date_range()
        
        if date_range:
            print(f"✅ 成功獲取日期範圍: {len(date_range)} 天")
            for i, date in enumerate(date_range, 1):
                print(f"   {i}. {date.strftime('%Y-%m-%d')}")
        else:
            print("❌ 獲取日期範圍失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_newprice_incremental_logic()
    success2 = test_auto_update_import()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    print(f"增量邏輯測試: {'✅ 通過' if success1 else '❌ 失敗'}")
    print(f"模組導入測試: {'✅ 通過' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print("\n🎉 所有測試通過！可以開始執行 price 增量更新")
    else:
        print("\n⚠️ 部分測試失敗，請檢查問題後再執行更新")
