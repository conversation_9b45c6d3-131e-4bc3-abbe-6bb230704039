#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試本專案中現有的月營收爬蟲
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

import datetime
import pandas as pd
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_original_monthly_crawler():
    """測試原始的月營收爬蟲"""
    print("🔍 測試本專案原始月營收爬蟲")
    print("=" * 60)
    
    try:
        # 導入原始爬蟲
        from crawler import crawl_monthly_report
        
        # 測試較舊的月份 (確定已發布)
        test_date = datetime.datetime(2024, 12, 10)  # 2024年11月營收
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年11月營收)")
        print("🔄 開始測試原始爬蟲...")
        
        result = crawl_monthly_report(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ 原始爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 檢查資料格式
            print(f"\n🔍 資料格式檢查:")
            
            # 檢查索引
            if result.index.names == ['stock_id', 'date']:
                print(f"   ✅ 索引格式正確")
            else:
                print(f"   ⚠️ 索引格式: {result.index.names}")
            
            # 檢查股票代碼
            stock_ids = result.index.get_level_values('stock_id')
            sample_ids = stock_ids[:3].tolist()
            print(f"   股票代碼樣本: {sample_ids}")
            
            # 檢查台積電
            tsmc_candidates = [sid for sid in stock_ids if '2330' in str(sid)]
            if tsmc_candidates:
                tsmc_id = tsmc_candidates[0]
                print(f"   找到台積電: {tsmc_id}")
                
                if '當月營收' in result.columns:
                    tsmc_data = result.loc[tsmc_id]
                    revenue = tsmc_data['當月營收'].iloc[0] if hasattr(tsmc_data['當月營收'], 'iloc') else tsmc_data['當月營收']
                    print(f"   台積電營收: {revenue:,}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('monthly_original_test.pkl')
            print(f"\n💾 測試資料已保存: monthly_original_test.pkl")
            
            return True, result
        else:
            print(f"\n⚠️ 原始爬蟲無資料")
            return False, None
            
    except Exception as e:
        print(f"\n❌ 原始爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False, None

def test_individual_functions():
    """測試個別的月營收函數"""
    print(f"\n🔍 測試個別月營收函數")
    print("=" * 60)
    
    try:
        # 導入個別函數
        from crawler import month_revenue
        
        test_date = datetime.datetime(2024, 12, 10)
        
        print("🧪 測試 month_revenue('sii', date)...")
        try:
            sii_result = month_revenue('sii', test_date)
            if sii_result is not None and not sii_result.empty:
                print(f"   ✅ SII成功: {len(sii_result)} 筆")
            else:
                print(f"   ⚠️ SII無資料")
        except Exception as e:
            print(f"   ❌ SII失敗: {str(e)[:50]}...")
        
        print("🧪 測試 month_revenue('otc', date)...")
        try:
            otc_result = month_revenue('otc', test_date)
            if otc_result is not None and not otc_result.empty:
                print(f"   ✅ OTC成功: {len(otc_result)} 筆")
            else:
                print(f"   ⚠️ OTC無資料")
        except Exception as e:
            print(f"   ❌ OTC失敗: {str(e)[:50]}...")
            
    except ImportError as e:
        print(f"❌ 無法導入個別函數: {str(e)}")
    except Exception as e:
        print(f"❌ 個別函數測試失敗: {str(e)}")

def test_alternative_monthly_crawlers():
    """測試其他可能的月營收爬蟲"""
    print(f"\n🔍 尋找其他月營收爬蟲實現")
    print("=" * 60)
    
    try:
        # 檢查是否有其他月營收相關函數
        import crawler
        
        # 列出所有包含 'month' 或 'revenue' 的函數
        monthly_functions = []
        for attr_name in dir(crawler):
            if ('month' in attr_name.lower() or 'revenue' in attr_name.lower()) and callable(getattr(crawler, attr_name)):
                monthly_functions.append(attr_name)
        
        print(f"找到的月營收相關函數: {monthly_functions}")
        
        # 測試每個函數
        test_date = datetime.datetime(2024, 12, 10)
        
        for func_name in monthly_functions:
            if func_name.startswith('_'):  # 跳過私有函數
                continue
                
            print(f"\n🧪 測試 {func_name}...")
            try:
                func = getattr(crawler, func_name)
                
                # 嘗試不同的參數組合
                if 'crawl' in func_name:
                    result = func(test_date)
                elif func_name == 'month_revenue':
                    result = func('sii', test_date)
                else:
                    continue
                
                if result is not None and not result.empty:
                    print(f"   ✅ {func_name} 成功: {len(result)} 筆")
                else:
                    print(f"   ⚠️ {func_name} 無資料")
                    
            except Exception as e:
                print(f"   ❌ {func_name} 失敗: {str(e)[:50]}...")
                
    except Exception as e:
        print(f"❌ 搜尋失敗: {str(e)}")

def compare_with_existing_data():
    """與現有資料比較"""
    print(f"\n🔍 與現有 monthly_report.pkl 比較")
    print("=" * 60)
    
    try:
        existing_file = 'history/tables/monthly_report.pkl'
        test_file = 'monthly_original_test.pkl'
        
        if os.path.exists(existing_file):
            existing_data = pd.read_pickle(existing_file)
            print(f"📊 現有資料:")
            print(f"   筆數: {len(existing_data):,}")
            
            dates = existing_data.index.get_level_values('date')
            print(f"   日期範圍: {dates.min()} 至 {dates.max()}")
            print(f"   索引: {existing_data.index.names}")
            print(f"   欄位: {list(existing_data.columns)}")
            
            if os.path.exists(test_file):
                test_data = pd.read_pickle(test_file)
                print(f"\n📊 測試資料:")
                print(f"   筆數: {len(test_data):,}")
                print(f"   索引: {test_data.index.names}")
                print(f"   欄位: {list(test_data.columns)}")
                
                # 格式相容性檢查
                if existing_data.index.names == test_data.index.names:
                    print(f"   ✅ 索引格式相容")
                else:
                    print(f"   ❌ 索引格式不相容")
                
                common_columns = set(existing_data.columns).intersection(set(test_data.columns))
                print(f"   共同欄位: {len(common_columns)} 個")
                
                if len(common_columns) >= 3:
                    print(f"   ✅ 可以直接更新現有檔案")
                else:
                    print(f"   ❌ 格式差異太大")
            else:
                print(f"⚠️ 沒有測試資料可比較")
        else:
            print(f"❌ 現有檔案不存在")
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 本專案月營收爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 測試現有的月營收爬蟲是否正常工作")
    print("=" * 60)
    
    # 測試1: 原始爬蟲
    success, result = test_original_monthly_crawler()
    
    # 測試2: 個別函數
    test_individual_functions()
    
    # 測試3: 其他實現
    test_alternative_monthly_crawlers()
    
    # 測試4: 格式比較
    if success:
        compare_with_existing_data()
    
    print(f"\n📊 測試總結:")
    if success:
        print(f"✅ 本專案的月營收爬蟲可以正常工作!")
        print(f"💡 可以直接在 auto_update.py 中使用:")
        print(f"   ('monthly_report', crawl_monthly_report, month_range),")
        print(f"🔧 不需要修復版，原始版本就能工作")
    else:
        print(f"❌ 本專案的月營收爬蟲有問題")
        print(f"💡 可能需要:")
        print(f"   • 檢查網路連接")
        print(f"   • 等待 IP 解封")
        print(f"   • 或使用其他資料來源")

if __name__ == "__main__":
    main()
