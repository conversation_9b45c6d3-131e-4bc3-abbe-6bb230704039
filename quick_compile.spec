# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['O3mh_gui_v21_optimized_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('strategies', 'strategies'),
        ('*.json', '.'),
        ('*.yaml', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'pyqtgraph',
        'pandas',
        'numpy',
        'sqlite3',
        'requests',
        'bs4',
        'selenium',
        'matplotlib',
        'openpyxl',
        'xlsxwriter',
        'inspect',
        'importlib',
        'importlib.util',
        'json',
        'csv',
        'datetime',
        'threading',
        'concurrent.futures',
        'logging',
        'traceback',
        'calendar',
        'stock_filter',
        'enhanced_market_scanner',
        'real_market_data_fetcher',
        'smart_trading_strategies',
        'roe_data_crawler',
        'market_data_crawler',
        'unified_monitor_manager',
        'auto_update',
        'enhanced_data_fetcher',
        'finmind_data_provider',
        'pe_data_provider',
        'real_data_sources',
        'safe_market_scanner',
        'optimized_market_scanner',
        'intraday_data_fetcher',
        'smart_yahoo_fetcher',
        'twelvedata_fetcher',
        'enhanced_yfinance_fetcher',
        'finmind_quota_manager',
        'stock_name_mapping',
        'strategy_intersection_analyzer',
        'multi_strategy_chart',
        'stock_basic_info_crawler',
        'google_stock_news_gui',
        'improved_roe_downloader',
        'roe_data_downloader_gui',
        'twse_market_data_dialog',
        'market_data_crawler_dialog',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'difflib',
        'pydoc',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統_修復版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
