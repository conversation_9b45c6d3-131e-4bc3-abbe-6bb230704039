#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PE爬蟲測試腳本
測試PE資料爬取和資料庫保存功能
"""

import os
import sys
import datetime
import pandas as pd
import sqlite3

# 添加當前目錄到路徑
sys.path.append('.')

def test_pe_crawler():
    """測試PE爬蟲功能"""
    print("🧪 開始測試PE爬蟲功能...")
    
    try:
        # 導入PE爬蟲函數
        from finlab.crawler import crawl_pe
        print("✅ 成功導入crawl_pe函數")
        
        # 測試爬取最近一個交易日的PE資料
        test_date = datetime.datetime(2024, 12, 20)  # 使用一個已知的交易日
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 爬取PE資料
        print("🔄 開始爬取PE資料...")
        pe_data = crawl_pe(test_date)
        
        if pe_data.empty:
            print("⚠️ 爬取的PE資料為空")
            return False
        
        print(f"✅ 成功爬取PE資料: {len(pe_data)} 筆")
        print(f"📊 資料欄位: {list(pe_data.columns)}")
        print(f"📋 索引結構: {pe_data.index.names}")
        
        # 顯示前幾筆資料
        print("\n📋 前5筆資料預覽:")
        print(pe_data.head())
        
        return pe_data
        
    except Exception as e:
        print(f"❌ PE爬蟲測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_pe_database_save(pe_data):
    """測試PE資料庫保存功能"""
    print("\n🧪 開始測試PE資料庫保存功能...")
    
    try:
        # 導入資料庫保存函數
        from auto_update import save_pe_to_database
        print("✅ 成功導入save_pe_to_database函數")
        
        # 測試保存到資料庫
        test_db_path = 'test_pe.db'
        print(f"💾 測試保存到: {test_db_path}")
        
        success = save_pe_to_database(pe_data, test_db_path)
        
        if success:
            print("✅ PE資料庫保存成功")
            
            # 驗證資料庫內容
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            # 檢查表格結構
            cursor.execute("PRAGMA table_info(pe_data)")
            columns = cursor.fetchall()
            print(f"📊 資料庫表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查資料數量
            cursor.execute("SELECT COUNT(*) FROM pe_data")
            count = cursor.fetchone()[0]
            print(f"📈 資料庫記錄數: {count}")
            
            # 檢查樣本資料
            cursor.execute("SELECT * FROM pe_data LIMIT 5")
            sample_data = cursor.fetchall()
            print(f"📋 樣本資料:")
            for row in sample_data:
                print(f"   {row}")
            
            conn.close()
            
            # 清理測試檔案
            if os.path.exists(test_db_path):
                os.remove(test_db_path)
                print(f"🗑️ 清理測試檔案: {test_db_path}")
            
            return True
        else:
            print("❌ PE資料庫保存失敗")
            return False
            
    except Exception as e:
        print(f"❌ PE資料庫保存測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pe_database_read():
    """測試從PE資料庫讀取功能"""
    print("\n🧪 開始測試PE資料庫讀取功能...")
    
    try:
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        
        if not os.path.exists(pe_db_path):
            print(f"⚠️ PE資料庫檔案不存在: {pe_db_path}")
            return False
        
        # 讀取資料庫
        conn = sqlite3.connect(pe_db_path)
        df = pd.read_sql('SELECT * FROM pe_data LIMIT 10', conn)
        conn.close()
        
        print(f"✅ 成功從資料庫讀取資料: {len(df)} 筆")
        print(f"📊 資料欄位: {list(df.columns)}")
        print("\n📋 資料預覽:")
        print(df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ PE資料庫讀取測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 PE爬蟲和資料庫功能測試")
    print("=" * 50)
    
    # 測試1: PE爬蟲功能
    pe_data = test_pe_crawler()
    if pe_data is None:
        print("❌ PE爬蟲測試失敗，停止後續測試")
        return
    
    # 測試2: PE資料庫保存功能
    if not test_pe_database_save(pe_data):
        print("❌ PE資料庫保存測試失敗")
    
    # 測試3: PE資料庫讀取功能
    if not test_pe_database_read():
        print("⚠️ PE資料庫讀取測試失敗（可能是因為資料庫檔案不存在）")
    
    print("\n🏁 測試完成!")

if __name__ == "__main__":
    main()
