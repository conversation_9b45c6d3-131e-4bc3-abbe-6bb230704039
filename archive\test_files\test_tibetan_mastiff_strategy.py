#!/usr/bin/env python3
"""
藏獒策略测试脚本
测试新实现的藏獒动能策略是否能正常工作
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TibetanMastiffStrategyTester:
    """藏獒策略测试器"""
    
    def __init__(self):
        self.name = "藏獒策略测试"
    
    def generate_test_data(self, days=300):
        """生成测试数据 - 模拟动能突破股票"""
        # 创建日期范围
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # 生成基础价格数据（模拟股票从底部突破的走势）
        np.random.seed(42)  # 固定随机种子以便重现
        
        # 模拟一个从底部突破创新高的股票
        base_price = 50  # 起始价格
        prices = []
        volumes = []
        
        for i in range(days):
            if i < 200:  # 前200天底部盘整
                # 底部盘整期：小幅波动
                price_change = np.random.normal(0, 0.5)
                volume = np.random.normal(300000, 50000)  # 较高的成交量
            elif i < 280:  # 开始上涨
                # 上涨期：逐步上涨
                price_change = np.random.normal(0.8, 0.6)
                volume = np.random.normal(350000, 60000)
            else:  # 最后阶段：突破创新高
                # 突破期：强力上涨创年新高
                if i == days - 1:  # 最后一天：完美的藏獒信号
                    price_change = 5.0  # 更强力的突破，确保创新高
                    volume = np.random.normal(400000, 50000)  # 大量突破
                else:
                    price_change = np.random.normal(1.5, 0.8)
                    volume = np.random.normal(380000, 40000)
            
            if i == 0:
                price = base_price
            else:
                price = max(prices[-1] + price_change, 20)  # 确保价格不低于20
            
            prices.append(price)
            volumes.append(max(volume, 50000))  # 确保成交量不为负
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
            'Close': prices,
            'Volume': volumes
        })
        
        # 确保OHLC逻辑正确
        for i in range(len(df)):
            high = max(df.iloc[i]['Open'], df.iloc[i]['Close'])
            low = min(df.iloc[i]['Open'], df.iloc[i]['Close'])
            df.at[i, 'High'] = max(df.iloc[i]['High'], high)
            df.at[i, 'Low'] = min(df.iloc[i]['Low'], low)
        
        return df
    
    def calculate_tibetan_mastiff_indicators(self, df):
        """计算藏獒策略所需的技术指标"""
        # 10日成交量移动平均
        df['Volume_MA10'] = df['Volume'].rolling(window=10).mean()
        
        # 250日最高价（年新高）
        df['High_250'] = df['High'].rolling(window=250).max()
        
        # 12个月最低营收（模拟，实际需要营收数据）
        df['Revenue_Proxy'] = df['Close'].rolling(window=20).mean()  # 用20日均价模拟月营收
        df['Revenue_12M_Min'] = df['Revenue_Proxy'].rolling(window=240).min()  # 12个月最低
        
        # 营收年增率模拟（用价格变化模拟，但限制增长率避免过度增长）
        raw_yoy_growth = ((df['Revenue_Proxy'] / df['Revenue_Proxy'].shift(240)) - 1) * 100
        # 限制年增率在合理范围内，避免过度增长
        df['Revenue_YoY_Growth'] = np.clip(raw_yoy_growth, -50, 50)

        # 营收月增率模拟（也限制在合理范围）
        raw_mom_growth = ((df['Revenue_Proxy'] / df['Revenue_Proxy'].shift(20)) - 1) * 100
        df['Revenue_MoM_Growth'] = np.clip(raw_mom_growth, -60, 60)
        
        return df
    
    def check_yearly_high(self, df):
        """检查股价创年新高"""
        latest = df.iloc[-1]
        current_close = latest['Close']
        current_high = latest['High']  # 使用当日最高价
        yearly_high = latest['High_250']

        # 检查当日最高价是否创年新高
        if current_high >= yearly_high * 0.999:  # 允许0.1%的误差
            return True, f"创年新高✓({current_high:.2f})"
        else:
            distance_pct = ((yearly_high - current_high) / yearly_high) * 100
            return False, f"距年高{distance_pct:.1f}%"
    
    def check_liquidity_condition(self, df):
        """检查流动性条件"""
        latest = df.iloc[-1]
        volume_ma10 = latest['Volume_MA10']
        min_volume = 200 * 1000  # 20万股
        
        if volume_ma10 >= min_volume:
            return True, f"流动性✓({volume_ma10/1000:.0f}K股)"
        else:
            return False, f"流动性不足：{volume_ma10/1000:.0f}K < 200K股"
    
    def check_revenue_bottom(self, df):
        """检查营收底部确认"""
        recent_3_months = df.iloc[-3:]
        conditions_met = 0
        
        for i in range(len(recent_3_months)):
            row = recent_3_months.iloc[i]
            revenue_proxy = row['Revenue_Proxy']
            revenue_12m_min = row['Revenue_12M_Min']
            
            if revenue_12m_min > 0:
                ratio = revenue_12m_min / revenue_proxy
                if ratio < 0.8:
                    conditions_met += 1
        
        if conditions_met >= 3:
            return True, f"营收底部确认✓({conditions_met}/3月)"
        else:
            return False, f"营收底部未确认：{conditions_met}/3月"
    
    def check_revenue_decline_exclusion(self, df):
        """检查营收衰退排除条件"""
        recent_3_months = df.iloc[-3:]
        decline_months = 0
        
        for i in range(len(recent_3_months)):
            row = recent_3_months.iloc[i]
            revenue_yoy_growth = row['Revenue_YoY_Growth']
            
            if revenue_yoy_growth < -10:
                decline_months += 1
        
        if decline_months >= 3:
            return False, f"营收连3月衰退10%以上"
        else:
            return True, f"营收衰退检查通过✓"
    
    def check_growth_trend_exclusion(self, df):
        """检查成长趋势过老排除条件"""
        past_12_months = df.iloc[-12:]
        high_growth_months = 0
        
        for i in range(len(past_12_months)):
            row = past_12_months.iloc[i]
            revenue_yoy_growth = row['Revenue_YoY_Growth']
            
            if revenue_yoy_growth > 60:
                high_growth_months += 1
        
        if high_growth_months >= 8:
            return False, f"成长趋势过老：{high_growth_months}/12月>60%"
        else:
            return True, f"成长趋势检查通过✓"
    
    def check_monthly_growth_condition(self, df):
        """检查月增率条件"""
        recent_3_months = df.iloc[-3:]
        valid_months = 0
        
        for i in range(len(recent_3_months)):
            row = recent_3_months.iloc[i]
            revenue_mom_growth = row['Revenue_MoM_Growth']
            
            if revenue_mom_growth > -40:
                valid_months += 1
        
        if valid_months >= 3:
            return True, f"月增率条件✓({valid_months}/3月>-40%)"
        else:
            return False, f"月增率条件未满足：{valid_months}/3月>-40%"
    
    def test_tibetan_mastiff_strategy(self, df):
        """测试藏獒策略"""
        try:
            print("=" * 60)
            print("🐕 藏獒策略测试")
            print("=" * 60)
            
            # 计算指标
            df = self.calculate_tibetan_mastiff_indicators(df)
            
            # 显示最新数据
            latest = df.iloc[-1]
            print(f"📊 最新数据：")
            print(f"   收盘价: {latest['Close']:.2f}")
            print(f"   10日均量: {latest['Volume_MA10']:,.0f}")
            print(f"   250日最高: {latest['High_250']:.2f}")
            print(f"   营收代理: {latest['Revenue_Proxy']:.2f}")
            print()
            
            # 逐步检查条件
            print("🔍 条件检查：")
            
            # 1. 股价创年新高
            yearly_high_result = self.check_yearly_high(df)
            print(f"   1. 股价创年新高: {'✅' if yearly_high_result[0] else '❌'} {yearly_high_result[1]}")
            
            # 2. 流动性条件
            liquidity_result = self.check_liquidity_condition(df)
            print(f"   2. 流动性条件: {'✅' if liquidity_result[0] else '❌'} {liquidity_result[1]}")
            
            # 3. 营收底部确认
            revenue_bottom_result = self.check_revenue_bottom(df)
            print(f"   3. 营收底部确认: {'✅' if revenue_bottom_result[0] else '❌'} {revenue_bottom_result[1]}")
            
            # 4. 营收衰退排除
            revenue_decline_result = self.check_revenue_decline_exclusion(df)
            print(f"   4. 营收衰退排除: {'✅' if revenue_decline_result[0] else '❌'} {revenue_decline_result[1]}")
            
            # 5. 成长趋势排除
            growth_trend_result = self.check_growth_trend_exclusion(df)
            print(f"   5. 成长趋势排除: {'✅' if growth_trend_result[0] else '❌'} {growth_trend_result[1]}")
            
            # 6. 月增率条件
            monthly_growth_result = self.check_monthly_growth_condition(df)
            print(f"   6. 月增率条件: {'✅' if monthly_growth_result[0] else '❌'} {monthly_growth_result[1]}")
            
            # 综合判断
            core_conditions = all([
                yearly_high_result[0],
                liquidity_result[0], 
                revenue_bottom_result[0],
                revenue_decline_result[0],
                growth_trend_result[0],
                monthly_growth_result[0]
            ])
            
            print()
            print("📈 最终结果：")
            if core_conditions:
                print("🎉 ✅ 符合藏獒策略！这是一个潜在的动能信号！")
                print("🚀 特色：大多头会打出兇悍的一波流")
                print("💡 建议：8%停损，月底换股")
            else:
                print("❌ 不符合藏獒策略")
                failed_conditions = []
                if not yearly_high_result[0]: failed_conditions.append("年新高")
                if not liquidity_result[0]: failed_conditions.append("流动性")
                if not revenue_bottom_result[0]: failed_conditions.append("营收底部")
                if not revenue_decline_result[0]: failed_conditions.append("营收衰退")
                if not growth_trend_result[0]: failed_conditions.append("成长趋势")
                if not monthly_growth_result[0]: failed_conditions.append("月增率")
                print(f"   未满足条件: {', '.join(failed_conditions)}")
            
            print("=" * 60)
            return core_conditions
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 启动藏獒策略测试")
    
    tester = TibetanMastiffStrategyTester()
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_df = tester.generate_test_data(300)
    
    # 运行测试
    result = tester.test_tibetan_mastiff_strategy(test_df)
    
    print(f"\n🏁 测试完成，结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()
