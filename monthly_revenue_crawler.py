#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
獨立月營收爬蟲程式
專門用於更新 monthly_report.pkl 檔案

使用方法:
1. 更新最新月份: python monthly_revenue_crawler.py
2. 更新指定月份: python monthly_revenue_crawler.py --year 2024 --month 11
3. 更新多個月份: python monthly_revenue_crawler.py --months 3
4. 僅測試不保存: python monthly_revenue_crawler.py --test-only
"""

import sys
import os
import datetime
import pandas as pd
import time
import random
import requests
from io import StringIO
import urllib3
import argparse
import shutil

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 修復 numpy 依賴問題
try:
    import numpy as np
    # 嘗試訪問可能有問題的模組
    try:
        _ = np.core.numeric
    except AttributeError:
        # 如果 numpy._core.numeric 不存在，創建一個替代
        import types
        if not hasattr(np, '_core'):
            np._core = types.ModuleType('_core')
        if not hasattr(np._core, 'numeric'):
            np._core.numeric = types.ModuleType('numeric')
except ImportError:
    # 如果 numpy 導入失敗，創建一個簡單的替代
    class NumpyFallback:
        @staticmethod
        def where(condition):
            return [i for i, x in enumerate(condition) if x]

        @staticmethod
        def nan():
            return float('nan')

        nan = float('nan')

    np = NumpyFallback()

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

class MonthlyRevenueCrawler:
    """月營收爬蟲類"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.pkl_file = "history/tables/monthly_report.pkl"
    
    def crawl_single_market(self, market, year, month, date, foreign=False):
        """
        爬取單一市場的月營收資料
        
        Args:
            market: 'sii' (上市) 或 'otc' (上櫃)
            year: 民國年
            month: 月份
            date: 日期物件
            foreign: False=國內公司(_0), True=國外公司(_1)
        
        Returns:
            pandas.DataFrame: 月營收資料
        """
        suffix = '1' if foreign else '0'
        type_name = '國外' if foreign else '國內'
        market_name = '上市' if market == 'sii' else '上櫃'
        
        # 構建 URL (優先使用新網站)
        url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}_{suffix}.html'
        
        # 對於較舊的年份，使用舊格式
        if year <= 98:
            url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}.html'
        
        try:
            # 發送請求
            response = requests.get(url, headers=self.headers, timeout=30, verify=False)

            if response.status_code != 200:
                return pd.DataFrame()

            # 檢查是否被封鎖
            if '您的網頁IP已經被證交所封鎖' in response.text:
                return pd.DataFrame()

            response.encoding = 'big5'

            # 使用 FinLab 方法解析表格
            try:
                dfs = pd.read_html(StringIO(response.text), encoding='big5')
            except:
                # 如果編碼失敗，嘗試不指定編碼
                dfs = pd.read_html(StringIO(response.text))

            if not dfs:
                return pd.DataFrame()

            # 篩選有效表格
            valid_dfs = [df for df in dfs if df.shape[1] <= 11 and df.shape[1] > 5]

            if not valid_dfs:
                return pd.DataFrame()

            df = pd.concat(valid_dfs)

            # 處理欄位名稱
            if 'levels' in dir(df.columns):
                df.columns = df.columns.get_level_values(1)
                df.columns = df.columns.str.replace(' ', '')
            else:
                df = df[list(range(0, min(10, len(df.columns))))]
                # 尋找包含 '公司代號' 的行作為欄位名稱
                column_indices = df.index[df.iloc[:, 0] == '公司代號']
                if len(column_indices) > 0:
                    column_index = column_indices[0]
                    df.columns = df.iloc[column_index]
                    df = df.drop(df.index[column_index])

            # 清理資料
            df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
            df = df[df.iloc[:, 0] != '公司代號']  # 移除重複的表頭行

            # 確保有必要的欄位
            if '當月營收' not in df.columns or '公司代號' not in df.columns:
                return pd.DataFrame()

            # 數值處理
            df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
            df = df[~df['當月營收'].isnull()]
            df = df[df['公司代號'] != '合計']
            df = df[df['公司代號'].notna()]

            # 處理其他數值欄位
            numeric_columns = ['上月營收', '去年當月營收', '上月比較增減(%)', '去年同月增減(%)']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 添加日期和股票ID
            df['date'] = date
            if '公司名稱' in df.columns:
                df['stock_id'] = df['公司代號'].astype(str) + ' ' + df['公司名稱'].astype(str)
            else:
                df['stock_id'] = df['公司代號'].astype(str)

            # 設置索引
            df = df.set_index(['stock_id', 'date'])

            # 移除不需要的欄位
            if '公司代號' in df.columns:
                df = df.drop('公司代號', axis=1)
            if '公司名稱' in df.columns:
                df = df.drop('公司名稱', axis=1)

            return df

        except Exception as e:
            return pd.DataFrame()
    
    def crawl_monthly_revenue(self, year, month, date):
        """
        爬取完整月營收資料 (上市+上櫃，國內+國外)

        Args:
            year: 民國年
            month: 月份
            date: 日期物件

        Returns:
            pandas.DataFrame: 完整的月營收資料
        """
        all_data = []

        # 爬取順序: 上市國內 -> 上市國外 -> 上櫃國內 -> 上櫃國外
        tasks = [
            ('sii', False, '上市國內'),
            ('sii', True, '上市國外'),
            ('otc', False, '上櫃國內'),
            ('otc', True, '上櫃國外')
        ]

        total_tasks = len(tasks)

        for i, (market, foreign, desc) in enumerate(tasks, 1):
            df = self.crawl_single_market(market, year, month, date, foreign)

            # 子進度顯示
            sub_progress = (i / total_tasks) * 100
            if len(df) > 0:
                all_data.append(df)
                status_icon = "✅"
                status_text = f"({len(df)} 筆)"
            else:
                status_icon = "❌"
                status_text = "(無資料)"

            print(f"    └─ {desc}: {i}/{total_tasks} [{sub_progress:5.1f}%] {status_icon} {status_text}")

            # 延遲避免被封鎖
            if i < total_tasks:  # 最後一個任務不需要延遲
                time.sleep(random.uniform(2, 4))

        # 合併所有資料
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=False)
            return combined_df
        else:
            return pd.DataFrame()
    
    def backup_file(self):
        """備份現有檔案"""
        if os.path.exists(self.pkl_file):
            backup_file = f"{self.pkl_file}.backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.pkl_file, backup_file)
            print(f"✅ 已備份: {backup_file}")
            return backup_file
        return None
    
    def load_existing_data(self):
        """載入現有資料"""
        if os.path.exists(self.pkl_file):
            try:
                # 嘗試多種方式載入 pickle 檔案
                try:
                    df = pd.read_pickle(self.pkl_file)
                except Exception as e1:
                    print(f"⚠️ 標準載入失敗，嘗試替代方法: {str(e1)[:50]}...")
                    try:
                        # 嘗試使用 pickle 模組直接載入
                        import pickle
                        with open(self.pkl_file, 'rb') as f:
                            df = pickle.load(f)
                    except Exception as e2:
                        print(f"⚠️ pickle 載入失敗，嘗試重建索引: {str(e2)[:50]}...")
                        # 如果還是失敗，創建空的 DataFrame
                        df = pd.DataFrame()

                if len(df) > 0:
                    print(f"📖 載入現有資料: {len(df):,} 筆")
                    return df
                else:
                    print(f"📝 現有檔案為空，建立新資料")
                    return pd.DataFrame()

            except Exception as e:
                print(f"❌ 載入現有資料失敗: {str(e)[:50]}...")
                print(f"📝 將建立新檔案")
                return pd.DataFrame()
        else:
            print(f"📝 建立新檔案")
            return pd.DataFrame()
    
    def save_data(self, df):
        """保存資料"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.pkl_file), exist_ok=True)

            # 嘗試多種方式保存
            try:
                # 標準方式保存
                df.to_pickle(self.pkl_file)
            except Exception as e1:
                print(f"⚠️ 標準保存失敗，嘗試替代方法: {str(e1)[:50]}...")
                try:
                    # 使用 pickle 模組直接保存
                    import pickle
                    with open(self.pkl_file, 'wb') as f:
                        pickle.dump(df, f, protocol=pickle.HIGHEST_PROTOCOL)
                except Exception as e2:
                    print(f"❌ 所有保存方法都失敗: {str(e2)[:50]}...")
                    return False

            file_size = os.path.getsize(self.pkl_file) / (1024 * 1024)
            print(f"💾 已保存: {self.pkl_file} ({file_size:.1f} MB)")
            return True

        except Exception as e:
            print(f"❌ 保存失敗: {str(e)}")
            return False
    
    def get_missing_months(self, existing_df):
        """
        計算需要更新的月份 (從最後一個月份到最新可用月份)

        Args:
            existing_df: 現有資料

        Returns:
            list: 需要更新的日期列表
        """
        today = datetime.date.today()

        # 計算最新可用月份 (月營收每月10日公佈)
        if today.day < 10:
            latest_available = today.replace(day=1)
            # 往前推一個月
            if latest_available.month == 1:
                latest_available = latest_available.replace(year=latest_available.year-1, month=12)
            else:
                latest_available = latest_available.replace(month=latest_available.month-1)
        else:
            latest_available = today.replace(day=1)

        # 如果沒有現有資料，從最新可用月份開始
        if len(existing_df) == 0:
            print(f"� 無現有資料，將更新最新月份: {latest_available}")
            return [latest_available]

        # 找到現有資料的最後日期
        try:
            dates = existing_df.index.get_level_values('date')

            # 轉換為統一的日期格式
            if len(dates) > 0:
                # 獲取最後日期
                last_date = max(dates)

                # 轉換為 date 物件
                if hasattr(last_date, 'date'):
                    last_date = last_date.date()
                elif isinstance(last_date, str):
                    last_date = datetime.datetime.strptime(last_date, '%Y-%m-%d').date()

                # 轉換為月份的第一天
                last_month = last_date.replace(day=1)

                print(f"📅 現有資料最後月份: {last_month}")
                print(f"📅 最新可用月份: {latest_available}")

                # 計算需要更新的月份
                update_dates = []
                current_date = last_month

                while current_date <= latest_available:
                    # 下一個月
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year+1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month+1)

                    if current_date <= latest_available:
                        update_dates.append(current_date)

                if update_dates:
                    print(f"📊 需要更新 {len(update_dates)} 個月份")
                    return update_dates
                else:
                    print(f"✅ 資料已是最新，無需更新")
                    return []
            else:
                print(f"📝 現有資料為空，將更新最新月份")
                return [latest_available]

        except Exception as e:
            print(f"⚠️ 分析現有資料失敗: {str(e)[:50]}...")
            print(f"📝 將更新最新月份")
            return [latest_available]

    def update_monthly_revenue(self, target_year=None, target_month=None, months_to_update=1, test_only=False):
        """
        更新月營收資料

        Args:
            target_year: 目標年份 (西元年，None=自動計算)
            target_month: 目標月份 (None=自動計算)
            months_to_update: 要更新的月數 (僅在手動模式下使用)
            test_only: 僅測試不保存
        """
        print(f"🚀 開始更新月營收資料")
        print("=" * 60)

        # 備份現有檔案
        if not test_only:
            self.backup_file()

        # 載入現有資料
        existing_df = self.load_existing_data()

        # 計算要更新的月份
        if target_year and target_month:
            # 手動指定模式
            print(f"🎯 手動指定模式")
            update_dates = [datetime.date(target_year, target_month, 1)]
        else:
            # 自動更新模式 - 從最後日期更新到最新
            print(f"🔄 自動更新模式 - 從最後日期更新到最新")
            update_dates = self.get_missing_months(existing_df)

        if not update_dates:
            print(f"✅ 無需更新")
            return True

        print(f"📅 計劃更新月份: {[str(d) for d in update_dates]}")
        
        # 爬取新資料
        new_data = []
        total_months = len(update_dates)
        successful_months = 0

        print(f"\n🚀 開始爬取 {total_months} 個月份的資料")
        print("=" * 80)

        for i, date in enumerate(update_dates, 1):
            # 轉換為民國年
            year = date.year - 1911
            month = date.month

            # 檢查是否已存在該月份資料
            if len(existing_df) > 0:
                existing_dates = existing_df.index.get_level_values('date')
                if date in existing_dates.values:
                    # 進度條顯示 - 跳過
                    progress_percent = (i / total_months) * 100
                    status_icon = "⏭️"
                    print(f"crawl{date.strftime('%Y-%m-%d')}: {i:3d}/{total_months} [{progress_percent:5.1f}%] {status_icon} (已存在)")
                    continue

            # 爬取該月份資料
            monthly_data = self.crawl_monthly_revenue(year, month, date)

            # 進度條顯示
            progress_percent = (i / total_months) * 100

            if len(monthly_data) > 0:
                new_data.append(monthly_data)
                successful_months += 1
                status_icon = "✅"
                status_text = f"({len(monthly_data)} 筆)"
            else:
                status_icon = "❌"
                status_text = "(無資料)"

            print(f"crawl{date.strftime('%Y-%m-%d')}: {i:3d}/{total_months} [{progress_percent:5.1f}%] {status_icon} {status_text}")

            # 延遲避免被封鎖
            if i < total_months:  # 最後一個月份不需要延遲
                time.sleep(random.uniform(3, 6))
        
        # 合併資料
        if new_data:
            new_df = pd.concat(new_data, ignore_index=False)
            print(f"\n📊 新爬取資料: {len(new_df):,} 筆")
            
            if len(existing_df) > 0:
                # 合併新舊資料
                combined_df = pd.concat([existing_df, new_df], ignore_index=False)
                
                # 移除重複資料 (保留最新的)
                combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
                
                print(f"📈 合併後總計: {len(combined_df):,} 筆")
                print(f"   原有: {len(existing_df):,} 筆")
                print(f"   新增: {len(combined_df) - len(existing_df):,} 筆")
            else:
                combined_df = new_df
                print(f"📝 建立新資料: {len(combined_df):,} 筆")
            
            # 保存資料
            if test_only:
                print(f"🧪 測試模式: 不保存資料")
                print(f"   預覽前5筆:")
                print(combined_df.head())
                return True
            else:
                success = self.save_data(combined_df)
                if success:
                    print(f"🎉 更新完成！")
                    return True
                else:
                    return False
        else:
            print(f"❌ 無新資料可更新")
            return False

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='獨立月營收爬蟲程式')
    parser.add_argument('--year', type=int, help='指定年份 (西元年)')
    parser.add_argument('--month', type=int, help='指定月份')
    parser.add_argument('--months', type=int, default=1, help='要更新的月數 (預設: 1)')
    parser.add_argument('--test-only', action='store_true', help='僅測試不保存')
    
    args = parser.parse_args()
    
    print("📊 獨立月營收爬蟲程式")
    print("=" * 70)
    print("🎯 目標: 更新 monthly_report.pkl 檔案")
    print("=" * 70)
    
    # 建立爬蟲實例
    crawler = MonthlyRevenueCrawler()
    
    # 執行更新
    success = crawler.update_monthly_revenue(
        target_year=args.year,
        target_month=args.month,
        months_to_update=args.months,
        test_only=args.test_only
    )
    
    if success:
        print(f"\n✅ 程式執行成功")
    else:
        print(f"\n❌ 程式執行失敗")
        sys.exit(1)

if __name__ == "__main__":
    main()
