#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試GUI中高殖利率烏龜策略的修復
"""

import sys
import pandas as pd
from datetime import datetime
sys.path.append('strategies')

def test_gui_strategy_fix():
    """測試GUI中策略調用的修復"""
    
    print("🧪 測試GUI中高殖利率烏龜策略修復")
    print("=" * 50)
    
    # 1. 模擬GUI環境
    print("1️⃣ 模擬GUI環境...")
    
    class MockGUI:
        def __init__(self):
            # 初始化策略實例
            from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
            self._turtle_strategy_instance = HighYieldTurtleStrategy()
            
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬GUI中的策略檢查方法"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據"
                
                turtle_strategy = self._turtle_strategy_instance
                
                # 新版策略使用原始FinLab PKL數據，不需要額外的FinMind數據
                print(f"📊 使用原始FinLab PKL數據分析股票: {stock_id}")
                
                # 執行高殖利率烏龜分析（新版策略只需要df和stock_id）
                result = turtle_strategy.analyze_stock(df, stock_id=stock_id)
                
                # 轉換為舊格式的返回值
                if result['suitable']:
                    return True, result['reason']
                else:
                    return False, result['reason']
                    
            except Exception as e:
                print(f"❌ 策略檢查失敗: {e}")
                return False, f"策略檢查錯誤: {str(e)}"
    
    # 2. 創建測試數據
    print("2️⃣ 創建測試數據...")
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    
    print(f"   ✅ 測試數據創建完成，{len(test_df)} 天數據")
    
    # 3. 測試修復後的策略調用
    print("3️⃣ 測試修復後的策略調用...")
    
    mock_gui = MockGUI()
    test_stocks = ['2881', '2882', '1108', '2330', '1101']
    
    success_count = 0
    
    for stock_id in test_stocks:
        try:
            print(f"\n   📊 測試股票: {stock_id}")
            
            # 模擬GUI調用
            suitable, reason = mock_gui.check_high_yield_turtle_strategy(test_df, stock_id=stock_id)
            
            status = "✅ 符合" if suitable else "❌ 不符合"
            print(f"   {status} {stock_id}: {reason}")
            
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ {stock_id}: 測試失敗 - {e}")
    
    print(f"\n   📊 測試結果: {success_count}/{len(test_stocks)} 支股票測試成功")
    
    # 4. 測試None股票代碼的處理
    print("\n4️⃣ 測試None股票代碼的處理...")
    try:
        suitable, reason = mock_gui.check_high_yield_turtle_strategy(test_df, stock_id=None)
        print(f"   None股票代碼處理: {reason}")
    except Exception as e:
        print(f"   None股票代碼處理失敗: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"✅ GUI策略修復測試完成！")
    
    return success_count == len(test_stocks)

def test_strategy_parameters():
    """測試策略參數兼容性"""
    
    print("\n🔧 測試策略參數兼容性")
    print("=" * 30)
    
    try:
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        strategy = HighYieldTurtleStrategy()
        
        # 創建測試數據
        dates = pd.date_range(end=datetime.now(), periods=50, freq='D')
        prices = [100] * 50
        test_df = pd.DataFrame({
            'Open': prices, 'High': prices, 'Low': prices, 'Close': prices, 'Volume': [100000] * 50
        }, index=dates)
        
        # 測試正確的參數調用
        print("1️⃣ 測試正確參數調用...")
        result = strategy.analyze_stock(test_df, stock_id='2881')
        print(f"   ✅ 正確調用成功: 評分{result['score']}/100")
        
        # 測試錯誤的參數調用（應該會失敗）
        print("2️⃣ 測試錯誤參數調用...")
        try:
            result = strategy.analyze_stock(test_df, finmind_data={}, stock_id='2881')
            print(f"   ❌ 錯誤調用竟然成功了！這不應該發生")
        except TypeError as e:
            print(f"   ✅ 錯誤調用正確被拒絕: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 參數兼容性測試失敗: {e}")
        return False

if __name__ == "__main__":
    # 測試GUI修復
    gui_success = test_gui_strategy_fix()
    
    # 測試參數兼容性
    param_success = test_strategy_parameters()
    
    if gui_success and param_success:
        print(f"\n🎉 所有測試通過！")
        print(f"✅ GUI中的高殖利率烏龜策略調用已修復")
        print(f"✅ 不再傳遞不支援的finmind_data參數")
        print(f"✅ 股票代碼正確傳遞給策略")
        print(f"🚀 策略現在可以在GUI中正常使用了！")
    else:
        print(f"\n❌ 部分測試失敗，請檢查上述錯誤信息")
