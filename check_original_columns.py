#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查原始爬蟲資料的欄位名稱
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def check_original_columns():
    """檢查原始爬蟲資料的欄位名稱"""
    
    print("=" * 60)
    print("🔍 檢查原始爬蟲資料的欄位名稱")
    print("=" * 60)
    
    try:
        from crawler import price_twe, price_otc, merge, o2tp
        
        # 測試一個已知有資料的日期
        test_date = datetime(2023, 6, 30)
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 測試上市資料
        print(f"\n🏢 測試上市資料 (price_twe):")
        dftwe = price_twe(test_date)
        
        if not dftwe.empty:
            print(f"   ✅ 成功獲取 {len(dftwe)} 筆上市資料")
            print(f"   📋 上市資料欄位: {list(dftwe.columns)}")
            
            # 顯示第一筆資料
            if len(dftwe) > 0:
                first_row = dftwe.iloc[0]
                print(f"   📊 第一筆資料範例:")
                for col in dftwe.columns:
                    print(f"      {col}: {first_row[col]}")
        else:
            print(f"   ❌ 上市資料為空")
        
        # 等待一下
        import time
        time.sleep(5)
        
        # 測試上櫃資料
        print(f"\n🏪 測試上櫃資料 (price_otc):")
        dfotc = price_otc(test_date)
        
        if not dfotc.empty:
            print(f"   ✅ 成功獲取 {len(dfotc)} 筆上櫃資料")
            print(f"   📋 上櫃資料欄位: {list(dfotc.columns)}")
            
            # 顯示第一筆資料
            if len(dfotc) > 0:
                first_row = dfotc.iloc[0]
                print(f"   📊 第一筆資料範例:")
                for col in dfotc.columns:
                    print(f"      {col}: {first_row[col]}")
        else:
            print(f"   ❌ 上櫃資料為空")
        
        # 測試合併後的資料
        if not dftwe.empty and not dfotc.empty:
            print(f"\n🔗 測試合併資料 (merge):")
            df_merged = merge(dftwe, dfotc, o2tp)
            
            if not df_merged.empty:
                print(f"   ✅ 成功合併 {len(df_merged)} 筆資料")
                print(f"   📋 合併後欄位: {list(df_merged.columns)}")
                
                # 重置索引查看完整結構
                df_reset = df_merged.reset_index()
                print(f"   📋 重置索引後欄位: {list(df_reset.columns)}")
                
                # 檢查是否有 0050
                if 'stock_id' in df_reset.columns:
                    etf_0050 = df_reset[df_reset['stock_id'].str.contains('0050', na=False)]
                    if not etf_0050.empty:
                        row = etf_0050.iloc[0]
                        print(f"   📊 0050 資料範例:")
                        for col in df_reset.columns:
                            print(f"      {col}: {row[col]}")
            else:
                print(f"   ❌ 合併後資料為空")
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_original_columns()
