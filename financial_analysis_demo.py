#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TWSE 財務報表資料分析演示
"""

import sqlite3
import pandas as pd
import os

def connect_financial_db():
    """連接財務資料庫"""
    db_path = os.path.join('history', 'tables', 'financial_statements.db')
    if not os.path.exists(db_path):
        print(f"❌ 財務資料庫不存在: {db_path}")
        print(f"請先執行: python auto_update.py twse_financial_statements")
        return None
    
    return sqlite3.connect(db_path)

def analyze_top_revenue_companies():
    """分析營收前20名公司"""
    
    print("=" * 80)
    print("📊 營收前20名公司分析")
    print("=" * 80)
    
    conn = connect_financial_db()
    if not conn:
        return
    
    try:
        query = """
        SELECT 
            stock_id,
            stock_name,
            CAST(營業收入 AS REAL) as revenue,
            CAST(營業毛利（毛損） AS REAL) as gross_profit,
            ROUND(CAST(營業毛利（毛損） AS REAL) / CAST(營業收入 AS REAL) * 100, 2) as gross_margin
        FROM income_statements 
        WHERE 營業收入 IS NOT NULL 
          AND 營業收入 != ''
          AND CAST(營業收入 AS REAL) > 0
        ORDER BY CAST(營業收入 AS REAL) DESC 
        LIMIT 20
        """
        
        df = pd.read_sql_query(query, conn)
        
        print(f"🏆 營收前20名公司:")
        print(f"{'排名':<4} {'股票代碼':<8} {'公司名稱':<12} {'營業收入(千元)':<15} {'毛利率(%)':<10}")
        print("-" * 70)
        
        for i, row in df.iterrows():
            print(f"{i+1:<4} {row['stock_id']:<8} {row['stock_name']:<12} {row['revenue']:>13,.0f} {row['gross_margin']:>8.1f}")
        
        # 統計分析
        print(f"\n📈 統計分析:")
        print(f"   平均營收: {df['revenue'].mean():,.0f} 千元")
        print(f"   平均毛利率: {df['gross_margin'].mean():.1f}%")
        print(f"   最高毛利率: {df['gross_margin'].max():.1f}% ({df.loc[df['gross_margin'].idxmax(), 'stock_name']})")
        print(f"   最低毛利率: {df['gross_margin'].min():.1f}% ({df.loc[df['gross_margin'].idxmin(), 'stock_name']})")
        
    except Exception as e:
        print(f"❌ 分析營收前20名失敗: {e}")
    finally:
        conn.close()

def analyze_balance_sheet_strength():
    """分析資產負債表強度"""
    
    print("\n" + "=" * 80)
    print("💪 資產負債表強度分析")
    print("=" * 80)
    
    conn = connect_financial_db()
    if not conn:
        return
    
    try:
        query = """
        SELECT 
            stock_id,
            stock_name,
            CAST(資產總額 AS REAL) as total_assets,
            CAST(負債總額 AS REAL) as total_liabilities,
            CAST(權益總額 AS REAL) as total_equity,
            ROUND((CAST(負債總額 AS REAL) / CAST(資產總額 AS REAL)) * 100, 2) as debt_ratio,
            ROUND((CAST(權益總額 AS REAL) / CAST(資產總額 AS REAL)) * 100, 2) as equity_ratio
        FROM balance_sheets 
        WHERE 資產總額 IS NOT NULL 
          AND 資產總額 != ''
          AND CAST(資產總額 AS REAL) > 0
        ORDER BY CAST(資產總額 AS REAL) DESC 
        LIMIT 15
        """
        
        df = pd.read_sql_query(query, conn)
        
        print(f"🏦 資產規模前15名公司:")
        print(f"{'排名':<4} {'股票代碼':<8} {'公司名稱':<12} {'總資產(千元)':<15} {'負債比(%)':<10} {'權益比(%)':<10}")
        print("-" * 80)
        
        for i, row in df.iterrows():
            print(f"{i+1:<4} {row['stock_id']:<8} {row['stock_name']:<12} {row['total_assets']:>13,.0f} {row['debt_ratio']:>8.1f} {row['equity_ratio']:>8.1f}")
        
        # 財務健康度分析
        print(f"\n💊 財務健康度分析:")
        healthy_companies = df[df['debt_ratio'] < 50]  # 負債比低於50%
        print(f"   負債比 < 50% 的公司: {len(healthy_companies)} 家")
        
        if len(healthy_companies) > 0:
            print(f"   財務最健康的公司:")
            for _, row in healthy_companies.head(5).iterrows():
                print(f"      {row['stock_id']} {row['stock_name']}: 負債比 {row['debt_ratio']:.1f}%")
        
    except Exception as e:
        print(f"❌ 分析資產負債表強度失敗: {e}")
    finally:
        conn.close()

def analyze_profitability():
    """分析獲利能力"""
    
    print("\n" + "=" * 80)
    print("💰 獲利能力分析")
    print("=" * 80)
    
    conn = connect_financial_db()
    if not conn:
        return
    
    try:
        query = """
        SELECT 
            i.stock_id,
            i.stock_name,
            CAST(i.營業收入 AS REAL) as revenue,
            CAST(i.營業毛利（毛損） AS REAL) as gross_profit,
            CAST(i.營業利益（損失） AS REAL) as operating_profit,
            CAST(i.本期淨利（淨損） AS REAL) as net_profit,
            CAST(b.資產總額 AS REAL) as total_assets,
            ROUND(CAST(i.營業毛利（毛損） AS REAL) / CAST(i.營業收入 AS REAL) * 100, 2) as gross_margin,
            ROUND(CAST(i.營業利益（損失） AS REAL) / CAST(i.營業收入 AS REAL) * 100, 2) as operating_margin,
            ROUND(CAST(i.本期淨利（淨損） AS REAL) / CAST(i.營業收入 AS REAL) * 100, 2) as net_margin,
            ROUND(CAST(i.本期淨利（淨損） AS REAL) / CAST(b.資產總額 AS REAL) * 100, 2) as roa
        FROM income_statements i
        JOIN balance_sheets b ON i.stock_id = b.stock_id
        WHERE i.營業收入 IS NOT NULL 
          AND i.營業收入 != ''
          AND CAST(i.營業收入 AS REAL) > 0
          AND i.本期淨利（淨損） IS NOT NULL
          AND i.本期淨利（淨損） != ''
          AND CAST(i.本期淨利（淨損） AS REAL) > 0
        ORDER BY CAST(i.本期淨利（淨損） AS REAL) DESC 
        LIMIT 15
        """
        
        df = pd.read_sql_query(query, conn)
        
        print(f"🎯 獲利能力前15名公司:")
        print(f"{'排名':<4} {'股票代碼':<8} {'公司名稱':<12} {'淨利(千元)':<13} {'毛利率%':<8} {'營益率%':<8} {'淨利率%':<8} {'ROA%':<6}")
        print("-" * 85)
        
        for i, row in df.iterrows():
            print(f"{i+1:<4} {row['stock_id']:<8} {row['stock_name']:<12} {row['net_profit']:>11,.0f} {row['gross_margin']:>6.1f} {row['operating_margin']:>6.1f} {row['net_margin']:>6.1f} {row['roa']:>4.1f}")
        
        # 獲利能力統計
        print(f"\n📊 獲利能力統計:")
        print(f"   平均毛利率: {df['gross_margin'].mean():.1f}%")
        print(f"   平均營業利益率: {df['operating_margin'].mean():.1f}%")
        print(f"   平均淨利率: {df['net_margin'].mean():.1f}%")
        print(f"   平均 ROA: {df['roa'].mean():.1f}%")
        
        # 找出高獲利率公司
        high_margin_companies = df[df['net_margin'] > 10]  # 淨利率 > 10%
        print(f"\n🌟 高獲利率公司 (淨利率 > 10%):")
        for _, row in high_margin_companies.iterrows():
            print(f"   {row['stock_id']} {row['stock_name']}: 淨利率 {row['net_margin']:.1f}%, ROA {row['roa']:.1f}%")
        
    except Exception as e:
        print(f"❌ 分析獲利能力失敗: {e}")
    finally:
        conn.close()

def analyze_specific_company(stock_id="2330"):
    """分析特定公司的財務狀況"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 {stock_id} 公司財務分析")
    print("=" * 80)
    
    conn = connect_financial_db()
    if not conn:
        return
    
    try:
        # 綜合損益表分析
        income_query = """
        SELECT * FROM income_statements WHERE stock_id = ?
        """
        
        income_df = pd.read_sql_query(income_query, conn, params=[stock_id])
        
        if len(income_df) > 0:
            row = income_df.iloc[0]
            print(f"📊 {row['stock_name']} ({stock_id}) 綜合損益表:")
            print(f"   營業收入: {float(row['營業收入']):,.0f} 千元")
            print(f"   營業成本: {float(row['營業成本']):,.0f} 千元")
            print(f"   營業毛利: {float(row['營業毛利（毛損）']):,.0f} 千元")
            print(f"   營業利益: {float(row['營業利益（損失）']):,.0f} 千元")
            print(f"   本期淨利: {float(row['本期淨利（淨損）']):,.0f} 千元")
            
            # 計算比率
            revenue = float(row['營業收入'])
            gross_profit = float(row['營業毛利（毛損）'])
            operating_profit = float(row['營業利益（損失）'])
            net_profit = float(row['本期淨利（淨損）'])
            
            print(f"\n📈 獲利率分析:")
            print(f"   毛利率: {(gross_profit/revenue)*100:.1f}%")
            print(f"   營業利益率: {(operating_profit/revenue)*100:.1f}%")
            print(f"   淨利率: {(net_profit/revenue)*100:.1f}%")
        
        # 資產負債表分析
        balance_query = """
        SELECT * FROM balance_sheets WHERE stock_id = ?
        """
        
        balance_df = pd.read_sql_query(balance_query, conn, params=[stock_id])
        
        if len(balance_df) > 0:
            row = balance_df.iloc[0]
            print(f"\n🏦 {row['stock_name']} ({stock_id}) 資產負債表:")
            print(f"   流動資產: {float(row['流動資產']):,.0f} 千元")
            print(f"   非流動資產: {float(row['非流動資產']):,.0f} 千元")
            print(f"   資產總額: {float(row['資產總額']):,.0f} 千元")
            print(f"   流動負債: {float(row['流動負債']):,.0f} 千元")
            print(f"   非流動負債: {float(row['非流動負債']):,.0f} 千元")
            print(f"   負債總額: {float(row['負債總額']):,.0f} 千元")
            print(f"   權益總額: {float(row['權益總額']):,.0f} 千元")
            
            # 計算財務比率
            total_assets = float(row['資產總額'])
            total_liabilities = float(row['負債總額'])
            total_equity = float(row['權益總額'])
            current_assets = float(row['流動資產'])
            current_liabilities = float(row['流動負債'])
            
            print(f"\n📊 財務比率分析:")
            print(f"   負債比率: {(total_liabilities/total_assets)*100:.1f}%")
            print(f"   權益比率: {(total_equity/total_assets)*100:.1f}%")
            print(f"   流動比率: {(current_assets/current_liabilities):.2f}")
            
            # ROA 計算
            if len(income_df) > 0:
                net_profit = float(income_df.iloc[0]['本期淨利（淨損）'])
                roa = (net_profit / total_assets) * 100
                print(f"   ROA (資產報酬率): {roa:.1f}%")
        
    except Exception as e:
        print(f"❌ 分析 {stock_id} 公司失敗: {e}")
    finally:
        conn.close()

def main():
    """主函數"""
    
    print("📊 TWSE 財務報表資料分析演示")
    print("基於台灣證交所 OpenAPI 資料")
    
    # 檢查資料庫是否存在
    db_path = os.path.join('history', 'tables', 'financial_statements.db')
    if not os.path.exists(db_path):
        print(f"\n❌ 財務資料庫不存在: {db_path}")
        print(f"請先執行: python auto_update.py twse_financial_statements")
        return False
    
    # 執行各種分析
    analyze_top_revenue_companies()
    analyze_balance_sheet_strength()
    analyze_profitability()
    analyze_specific_company("2330")  # 台積電
    analyze_specific_company("2317")  # 鴻海
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"🎉 財務分析演示完成")
    print(f"=" * 80)
    print(f"💡 你可以:")
    print(f"   1. 修改 analyze_specific_company() 中的股票代碼")
    print(f"   2. 自定義 SQL 查詢進行更深入的分析")
    print(f"   3. 結合其他資料來源進行綜合分析")
    print(f"   4. 建立投資策略篩選條件")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
