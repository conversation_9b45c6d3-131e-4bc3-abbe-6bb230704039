#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單啟動器 - 直接啟動台股智能選股系統
避免複雜的模組修復，直接啟動程式
"""

import os
import sys
import subprocess
import time

def show_banner():
    """顯示啟動橫幅"""
    print("=" * 50)
    print("    🚀 台股智能選股系統 v22.0 🚀")
    print("         簡單啟動器 - 直接啟動")
    print("=" * 50)
    print()

def find_executable():
    """尋找可執行檔"""
    exe_candidates = [
        "dist/台股智能選股系統_修復版.exe",
        "dist/台股智能選股系統.exe",
        "台股智能選股系統_修復版.exe",
        "台股智能選股系統.exe"
    ]
    
    for exe_path in exe_candidates:
        if os.path.exists(exe_path):
            return exe_path
    
    return None

def launch_program(exe_path):
    """啟動程式"""
    try:
        print(f"🚀 正在啟動: {exe_path}")
        print("⏳ 請稍候...")
        
        # 使用 subprocess 啟動程式
        process = subprocess.Popen([exe_path])
        
        print("✅ 程式已啟動！")
        print()
        print("💡 提示:")
        print("   - 程式正在後台運行")
        print("   - 如果沒有看到視窗，請檢查工作列")
        print("   - 首次啟動可能需要較長時間")
        
        return True
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        return False

def show_troubleshooting():
    """顯示故障排除信息"""
    print()
    print("🆘 故障排除:")
    print("   1. 檢查防毒軟體是否阻擋程式")
    print("   2. 嘗試以管理員身份運行")
    print("   3. 檢查 Windows 版本兼容性")
    print("   4. 重新下載或編譯程式")
    print()
    print("🔧 其他啟動方式:")
    print("   - python 啟動_台股智能選股系統_修復版.py")
    print("   - python wrapper_launcher.py")
    print("   - 直接雙擊 .exe 文件")

def main():
    """主函數"""
    show_banner()
    
    # 尋找可執行檔
    print("🔍 正在尋找程式文件...")
    exe_path = find_executable()
    
    if not exe_path:
        print("❌ 找不到程式文件！")
        print()
        print("📁 請確認以下文件之一存在:")
        print("   - dist/台股智能選股系統_修復版.exe")
        print("   - dist/台股智能選股系統.exe")
        print()
        print("🔧 如果文件不存在，請執行:")
        print("   python quick_recompile.py")
        
        input("\n按 Enter 鍵退出...")
        return
    
    print(f"✅ 找到程式: {exe_path}")
    
    # 啟動程式
    success = launch_program(exe_path)
    
    if success:
        print()
        print("🎉 啟動成功！")
        print("📋 程式功能:")
        print("   ✓ 多策略智能選股")
        print("   ✓ 實時K線圖表分析")
        print("   ✓ 開盤前市場監控")
        print("   ✓ 技術指標分析")
        print("   ✓ Excel報告導出")
        
        # 等待一下讓用戶看到信息
        print()
        print("⏰ 啟動器將在 10 秒後關閉...")
        for i in range(10, 0, -1):
            print(f"   {i} 秒", end='\r')
            time.sleep(1)
        print("   完成！")
        
    else:
        show_troubleshooting()
        input("\n按 Enter 鍵退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用戶取消啟動")
    except Exception as e:
        print(f"\n❌ 啟動器發生錯誤: {e}")
        input("按 Enter 鍵退出...")
