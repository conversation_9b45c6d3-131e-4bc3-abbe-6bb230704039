#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試核心爬蟲技術
驗證GoodInfo和Yahoo Finance爬蟲的核心功能
"""

import sys
import os
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_core_goodinfo_crawler():
    """測試核心GoodInfo爬蟲"""
    print("🧪 測試核心GoodInfo爬蟲技術")
    print("=" * 50)
    
    try:
        from core_web_crawler import GoodInfoCrawler
        
        # 創建爬蟲實例
        crawler = GoodInfoCrawler()
        print("✅ 核心GoodInfo爬蟲實例創建成功")
        
        # 測試1: 獲取除權息時間表
        print("\n📊 測試1: 獲取除權息時間表")
        dividend_schedule = crawler.get_dividend_schedule()
        
        if not dividend_schedule.empty:
            print(f"✅ 成功獲取除權息時間表: {dividend_schedule.shape}")
            print(f"📋 資料欄位數: {len(dividend_schedule.columns)}")
            print(f"📊 資料筆數: {len(dividend_schedule)}")
            
            # 分析資料內容
            print("\n📋 資料樣本分析:")
            for i in range(min(3, len(dividend_schedule))):
                row = dividend_schedule.iloc[i]
                print(f"  第{i+1}行: {row.tolist()[:5]}...")
            
            # 儲存測試結果
            filename = f"goodinfo_dividend_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            dividend_schedule.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"💾 測試結果已儲存: {filename}")
            
        else:
            print("❌ 未獲取到除權息時間表")
            return False
        
        # 測試2: 獲取個股資料
        print("\n📈 測試2: 獲取個股除權息資料")
        test_stocks = ["2330", "2317"]  # 台積電、鴻海
        
        for stock_id in test_stocks:
            print(f"\n🔍 測試股票: {stock_id}")
            stock_data = crawler.get_stock_dividend_detail(stock_id)
            
            if not stock_data.empty:
                print(f"✅ 成功獲取股票 {stock_id} 資料: {stock_data.shape}")
                
                # 儲存個股測試結果
                filename = f"goodinfo_stock_{stock_id}_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                stock_data.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"💾 股票 {stock_id} 資料已儲存: {filename}")
            else:
                print(f"❌ 未獲取到股票 {stock_id} 資料")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心GoodInfo爬蟲測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_dividend_integration():
    """測試增強版除權息爬蟲整合"""
    print("\n🔧 測試增強版除權息爬蟲整合")
    print("=" * 50)
    
    try:
        # 避免GUI初始化問題，直接測試核心功能
        import sqlite3
        import pandas as pd
        from core_web_crawler import GoodInfoCrawler
        
        # 創建核心爬蟲
        crawler = GoodInfoCrawler()
        print("✅ 核心爬蟲創建成功")
        
        # 獲取除權息資料
        dividend_data = crawler.get_dividend_schedule()
        
        if not dividend_data.empty:
            print(f"✅ 成功獲取除權息資料: {dividend_data.shape}")
            
            # 模擬資料處理邏輯
            processed_records = []
            
            for index, row in dividend_data.iterrows():
                try:
                    # 提取股票代碼（通常在第2欄）
                    stock_code = str(row.iloc[1]).strip() if len(row) > 1 else ''
                    
                    # 檢查是否為有效的股票代碼
                    if not stock_code or not stock_code.isdigit() or len(stock_code) != 4:
                        continue
                    
                    # 提取股票名稱（通常在第3欄）
                    stock_name = str(row.iloc[2]).strip() if len(row) > 2 else ''
                    
                    # 提取除權息日期
                    ex_dividend_date = None
                    for col_idx in range(3, min(10, len(row))):
                        cell_value = str(row.iloc[col_idx]).strip()
                        if ('/' in cell_value or '-' in cell_value) and any(c.isdigit() for c in cell_value):
                            if '即將' in cell_value:
                                date_part = cell_value.split()[0].replace("'", "")
                                if '/' in date_part:
                                    parts = date_part.split('/')
                                    if len(parts) == 3 and len(parts[0]) == 2:
                                        ex_dividend_date = f"20{parts[0]}/{parts[1]}/{parts[2]}"
                                    else:
                                        ex_dividend_date = date_part
                            else:
                                ex_dividend_date = cell_value
                            break
                    
                    # 提取現金股利
                    cash_dividend = 0
                    for col_idx in range(len(row)-10, len(row)):
                        if col_idx >= 0:
                            try:
                                cell_value = str(row.iloc[col_idx]).strip()
                                if cell_value and cell_value != 'nan' and cell_value != '-':
                                    value = float(cell_value)
                                    if value > 0:
                                        cash_dividend = value
                                        break
                            except (ValueError, TypeError):
                                continue
                    
                    # 只保存有效資料
                    if cash_dividend > 0:
                        record = {
                            'stock_code': stock_code,
                            'stock_name': stock_name,
                            'ex_dividend_date': ex_dividend_date,
                            'cash_dividend': cash_dividend,
                            'data_source': 'goodinfo_core'
                        }
                        processed_records.append(record)
                        
                except Exception as e:
                    continue
            
            print(f"✅ 成功處理 {len(processed_records)} 筆有效除權息資料")
            
            # 顯示處理結果
            if processed_records:
                print("\n📋 處理結果樣本:")
                for i, record in enumerate(processed_records[:5]):
                    ex_date = record.get('ex_dividend_date', '無')
                    print(f"  {i+1}. {record['stock_code']} {record['stock_name']}: 現金股利={record['cash_dividend']}, 除權息日期={ex_date}")
                
                # 儲存處理結果
                df_result = pd.DataFrame(processed_records)
                filename = f"processed_dividend_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df_result.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"\n💾 處理結果已儲存: {filename}")
            
            return True
        else:
            print("❌ 未獲取到除權息資料")
            return False
            
    except Exception as e:
        print(f"❌ 增強版除權息爬蟲整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 核心爬蟲技術綜合測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    test_results = []
    
    # 執行測試
    print("1️⃣ 核心GoodInfo爬蟲測試")
    result1 = test_core_goodinfo_crawler()
    test_results.append(("核心GoodInfo爬蟲", result1))
    
    print("\n2️⃣ 增強版除權息整合測試")
    result2 = test_enhanced_dividend_integration()
    test_results.append(("增強版除權息整合", result2))
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 測試結果總結")
    print("=" * 30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(test_results)} 項測試通過")
    
    if passed == len(test_results):
        print("🎉 所有測試通過！核心爬蟲技術運作正常")
        print("💡 核心技術特點:")
        print("   • 穩定的GoodInfo資料爬取")
        print("   • 完整的除權息日期解析")
        print("   • 可靠的反爬蟲機制")
        print("   • 模組化的技術架構")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 核心爬蟲技術是專案的重要基礎設施，請持續維護和改進")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 70)
    if success:
        print("🚀 核心技術測試成功！可以作為專案的重要技術基礎")
        print("💡 這些技術將支援後續的GoodInfo和Yahoo資料爬取需求")
    else:
        print("🔧 請根據測試結果修正相關問題")
    
    input("\n按 Enter 鍵結束...")
