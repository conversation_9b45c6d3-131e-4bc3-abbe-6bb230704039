#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
年度ROE資料下載器 - GUI界面
提供年度ROE資料的下載、匯入和管理功能
"""

import os
import sys
import logging
import subprocess
from datetime import datetime
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

class CustomMessageBox(QMessageBox):
    """自定義訊息框，確保良好的顏色對比度"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
                color: #333333;
            }
            QMessageBox QLabel {
                color: #333333;
                background-color: transparent;
                font-size: 12px;
            }
            QMessageBox QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
                margin: 2px;
            }
            QMessageBox QPushButton:hover {
                background-color: #45a049;
            }
            QMessageBox QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

    @staticmethod
    def information(parent, title, text):
        """顯示資訊對話框"""
        msg_box = CustomMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(text)
        msg_box.setIcon(QMessageBox.Icon.Information)
        return msg_box.exec()

    @staticmethod
    def warning(parent, title, text):
        """顯示警告對話框"""
        msg_box = CustomMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(text)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        # 警告按鈕使用橙色
        msg_box.setStyleSheet(msg_box.styleSheet() + """
            QMessageBox QPushButton {
                background-color: #ff9800;
            }
            QMessageBox QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        return msg_box.exec()

    @staticmethod
    def critical(parent, title, text):
        """顯示錯誤對話框"""
        msg_box = CustomMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(text)
        msg_box.setIcon(QMessageBox.Icon.Critical)
        # 錯誤按鈕使用紅色
        msg_box.setStyleSheet(msg_box.styleSheet() + """
            QMessageBox QPushButton {
                background-color: #f44336;
            }
            QMessageBox QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        return msg_box.exec()

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ROEDownloadWorker(QThread):
    """ROE下載工作線程"""
    progress_updated = pyqtSignal(int, str)
    completed = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, year):
        super().__init__()
        self.year = year
    
    def run(self):
        """執行自動化流程"""
        try:
            # 步驟1: 下載ROE資料
            self.progress_updated.emit(10, "🚀 正在下載年度ROE資料...")
            csv_file = self.download_roe_data()
            
            if not csv_file:
                self.error_signal.emit("年度ROE資料下載失敗")
                return
            
            self.progress_updated.emit(50, f"✅ 下載完成: {os.path.basename(csv_file)}")
            
            # 步驟2: 自動匯入資料庫
            self.progress_updated.emit(60, "💾 正在匯入資料庫...")
            import_result = self.import_to_database(csv_file)
            
            if not import_result['success']:
                self.error_signal.emit(f"資料庫匯入失敗: {import_result['error']}")
                return
            
            self.progress_updated.emit(90, f"✅ 匯入完成: {import_result.get('success_count', 0)} 筆")
            
            # 步驟3: 完成
            self.progress_updated.emit(100, "🎉 自動化流程完成")
            
            # 準備結果
            result_info = {
                'csv_file': csv_file,
                'download_count': import_result.get('total_processed', 0),
                'import_stats': import_result
            }
            
            success_message = f"🎉 年度ROE資料下載完成！\n\n"
            success_message += f"📥 下載檔案: {os.path.basename(csv_file)}\n"
            success_message += f"📊 處理資料: {import_result.get('total_processed', 0)} 筆\n"
            success_message += f"💾 新增資料: {import_result.get('success_count', 0)} 筆\n"
            success_message += f"🔄 更新資料: {import_result.get('updated_count', 0)} 筆\n"
            success_message += f"📍 現在可以使用ROE資料進行分析"
            
            self.completed.emit({
                'success': True,
                'message': success_message,
                'result_info': result_info
            })
            
        except Exception as e:
            logger.error(f"❌ 自動化流程失敗: {e}")
            self.error_signal.emit(f"自動化流程失敗: {e}")
    
    def download_roe_data(self):
        """下載ROE資料"""
        try:
            from roe_data_crawler import ROEDataCrawler
            
            crawler = ROEDataCrawler()
            records = crawler.fetch_roe_data(self.year)
            
            if records:
                # 儲存到資料庫
                crawler.save_to_database(records, overwrite=True)
                
                # 匯出CSV
                csv_file = crawler.export_to_csv(self.year)
                return csv_file
            else:
                return None
                
        except Exception as e:
            logger.error(f"下載ROE資料失敗: {e}")
            return None
    
    def import_to_database(self, csv_file):
        """匯入資料到資料庫"""
        try:
            from roe_data_crawler import ROEDataCrawler
            
            # 創建爬蟲實例
            db_path = "D:/Finlab/history/tables/roe_data.db"
            crawler = ROEDataCrawler(db_path)
            
            # 從CSV讀取資料
            import csv
            records = []
            
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    records.append(row)
            
            # 執行匯入
            success = crawler.save_to_database(records, overwrite=True)
            
            if success:
                # 獲取詳細統計
                return {
                    'success': True,
                    'total_processed': len(records),
                    'success_count': len(records),
                    'updated_count': 0,
                    'error_count': 0
                }
            else:
                return {'success': False, 'error': '匯入失敗'}
                
        except Exception as e:
            logger.error(f"匯入資料庫失敗: {e}")
            return {'success': False, 'error': str(e)}

class ROEDataDownloader(QMainWindow):
    """年度ROE資料下載器主視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📊 年度ROE資料下載器")
        self.setGeometry(100, 100, 600, 500)
        
        # 設置主題 - 確保良好的顏色對比度
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 12px;
                color: #333333;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #333333;
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #333333;
            }
            QLabel {
                color: #333333;
                background-color: transparent;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #dddddd;
                border-radius: 4px;
                color: #333333;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
                color: #333333;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
                margin: 0.5px;
            }
            QComboBox, QSpinBox {
                background-color: #ffffff;
                border: 1px solid #dddddd;
                border-radius: 4px;
                padding: 4px;
                color: #333333;
            }
            QCheckBox {
                color: #333333;
                background-color: transparent;
            }
            /* 修正QMessageBox樣式 */
            QMessageBox {
                background-color: #ffffff;
                color: #333333;
            }
            QMessageBox QLabel {
                color: #333333;
                background-color: transparent;
            }
            QMessageBox QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.init_ui()
        
        # 初始化工作線程
        self.download_worker = None
    
    def init_ui(self):
        """初始化用戶界面"""
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 標題
        title_label = QLabel("📈 年度ROE資料下載")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 說明文字
        description = QLabel("從GoodInfo網站下載年度ROE資料，支援多年度資料抓取和資料庫儲存。")
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # 設定區域
        settings_group = QGroupBox("⚙️ 下載設定")
        settings_layout = QVBoxLayout(settings_group)
        
        # 年份設定
        year_layout = QHBoxLayout()
        year_layout.addWidget(QLabel("下載年份:"))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2010, 2030)
        self.year_spin.setValue(datetime.now().year - 1)  # 預設去年
        year_layout.addWidget(self.year_spin)
        year_layout.addStretch()
        settings_layout.addLayout(year_layout)
        
        # 自動開啟選項
        self.auto_open_check = QCheckBox("完成後自動開啟資料分析")
        self.auto_open_check.setChecked(True)
        settings_layout.addWidget(self.auto_open_check)
        
        layout.addWidget(settings_group)
        
        # 進度區域
        progress_group = QGroupBox("📊 執行進度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("準備就緒")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 日誌區域
        log_group = QGroupBox("📝 執行日誌")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.download_btn = QPushButton("🚀 開始下載")
        self.download_btn.clicked.connect(self.start_download)
        button_layout.addWidget(self.download_btn)
        
        self.open_folder_btn = QPushButton("📁 開啟資料夾")
        self.open_folder_btn.clicked.connect(self.open_data_folder)
        button_layout.addWidget(self.open_folder_btn)
        
        self.close_btn = QPushButton("❌ 關閉")
        self.close_btn.clicked.connect(self.close)
        self.close_btn.setStyleSheet("background-color: #f44336;")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 版權資訊
        copyright_label = QLabel("© 2025 台股智能選股系統 - ROE資料下載器")
        copyright_label.setStyleSheet("color: #888888; font-size: 10px;")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(copyright_label)
        
        # 初始日誌
        self.log("✅ 年度ROE資料下載器已啟動")
        self.log(f"📅 預設下載 {self.year_spin.value()} 年度ROE資料")
    
    def log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        # 滾動到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def start_download(self):
        """開始下載ROE資料"""
        if self.download_worker and self.download_worker.isRunning():
            CustomMessageBox.warning(self, "警告", "下載任務正在進行中")
            return
        
        year = self.year_spin.value()
        self.log(f"🚀 開始下載 {year} 年度ROE資料...")
        
        # 禁用按鈕
        self.download_btn.setEnabled(False)
        
        # 顯示進度條
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
        
        # 創建工作線程
        self.download_worker = ROEDownloadWorker(year)
        self.download_worker.progress_updated.connect(self.update_progress)
        self.download_worker.completed.connect(self.on_download_completed)
        self.download_worker.error_signal.connect(self.on_process_error)
        
        # 啟動線程
        self.download_worker.start()
    
    def update_progress(self, value, message):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log(message)
    
    def on_download_completed(self, result):
        """下載完成處理"""
        # 恢復按鈕
        self.download_btn.setEnabled(True)
        
        message = result.get('message', '')
        
        if result.get('success', False):
            self.log("✅ 下載流程成功完成")

            # 顯示成功對話框
            msg_box = CustomMessageBox(self)
            msg_box.setWindowTitle("下載成功")
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Icon.Information)

            # 添加按鈕
            open_btn = msg_box.addButton("📂 開啟資料夾", QMessageBox.ButtonRole.ActionRole)
            file_btn = msg_box.addButton("📄 開啟檔案", QMessageBox.ButtonRole.ActionRole)
            trading_btn = msg_box.addButton("📊 開啟分析", QMessageBox.ButtonRole.ActionRole)
            msg_box.addButton("關閉", QMessageBox.ButtonRole.RejectRole)

            msg_box.exec()

            # 處理按鈕點擊
            clicked_button = msg_box.clickedButton()

            if clicked_button == trading_btn or self.auto_open_check.isChecked():
                self.open_analysis_system()
            elif clicked_button == file_btn:
                self.open_csv_file(result.get('result_info', {}).get('csv_file'))
            elif clicked_button == open_btn:
                self.open_data_folder()
        else:
            self.log("❌ 下載流程失敗")
            CustomMessageBox.critical(self, "下載失敗", message)
    
    def on_process_error(self, error_message):
        """處理錯誤"""
        self.log(f"❌ 錯誤: {error_message}")
        self.download_btn.setEnabled(True)
        CustomMessageBox.critical(self, "錯誤", error_message)
    
    def open_data_folder(self):
        """開啟資料夾"""
        try:
            folder_path = "D:/Finlab/history/tables"
            
            if not os.path.exists(folder_path):
                os.makedirs(folder_path, exist_ok=True)
            
            if sys.platform.startswith('win'):
                os.startfile(folder_path)
            elif sys.platform.startswith('darwin'):
                subprocess.run(['open', folder_path])
            else:
                subprocess.run(['xdg-open', folder_path])
                
            self.log(f"📂 已開啟資料夾: {folder_path}")
            
        except Exception as e:
            self.log(f"❌ 開啟資料夾失敗: {e}")
            CustomMessageBox.critical(self, "錯誤", f"開啟資料夾失敗: {str(e)}")
    
    def open_csv_file(self, csv_file):
        """開啟CSV檔案"""
        try:
            if csv_file and os.path.exists(csv_file):
                if sys.platform.startswith('win'):
                    os.startfile(csv_file)
                elif sys.platform.startswith('darwin'):
                    subprocess.run(['open', csv_file])
                else:
                    subprocess.run(['xdg-open', csv_file])
                    
                self.log(f"📂 已開啟檔案: {os.path.basename(csv_file)}")
            else:
                CustomMessageBox.warning(self, "警告", "CSV檔案不存在")
                
        except Exception as e:
            self.log(f"❌ 開啟檔案失敗: {e}")
    
    def open_analysis_system(self):
        """開啟分析系統"""
        try:
            # 這裡可以啟動ROE分析系統
            self.log("📊 開啟ROE分析系統...")
            CustomMessageBox.information(self, "功能開發中", "ROE分析系統功能正在開發中")

        except Exception as e:
            self.log(f"❌ 啟動分析系統失敗: {e}")
            CustomMessageBox.critical(self, "錯誤", f"啟動分析系統失敗: {str(e)}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    window = ROEDataDownloader()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
