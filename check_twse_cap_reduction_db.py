#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 twse_cap_reduction.db 檔案的內容和結構
"""

import sqlite3
import pandas as pd
import os

def check_twse_cap_reduction_db():
    """檢查 twse_cap_reduction.db 檔案"""
    
    db_file = 'D:/Finlab/history/tables/twse_cap_reduction.db'
    
    print("=" * 80)
    print("🔍 檢查 twse_cap_reduction.db 檔案")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查檔案大小
    file_size = os.path.getsize(db_file) / 1024  # KB
    print(f"📁 檔案位置: {db_file}")
    print(f"📊 檔案大小: {file_size:.2f} KB")
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查表格列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 資料庫表格: {[table[0] for table in tables]}")
        
        # 檢查 twse_cap_reduction 表格結構
        cursor.execute("PRAGMA table_info(twse_cap_reduction)")
        columns = cursor.fetchall()
        print(f"\n📊 表格結構 (twse_cap_reduction):")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM twse_cap_reduction")
        total_count = cursor.fetchone()[0]
        print(f"\n📈 總資料筆數: {total_count:,}")
        
        # 檢查股票數量
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM twse_cap_reduction")
        unique_stocks = cursor.fetchone()[0]
        print(f"📈 不重複股票數: {unique_stocks:,}")
        
        # 檢查日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM twse_cap_reduction")
        date_range = cursor.fetchone()
        print(f"📅 日期範圍: {date_range[0]} ~ {date_range[1]}")
        
        # 顯示前10筆資料
        print(f"\n📋 前10筆資料:")
        cursor.execute("SELECT * FROM twse_cap_reduction LIMIT 10")
        rows = cursor.fetchall()
        
        # 取得欄位名稱
        column_names = [description[0] for description in cursor.description]
        
        # 建立 DataFrame 以便更好地顯示
        df_sample = pd.DataFrame(rows, columns=column_names)
        print(df_sample.to_string(index=False))
        
        # 檢查各股票的減資次數
        print(f"\n📊 各股票減資次數統計:")
        cursor.execute("""
            SELECT stock_id, COUNT(*) as reduction_count 
            FROM twse_cap_reduction 
            GROUP BY stock_id 
            ORDER BY reduction_count DESC 
            LIMIT 10
        """)
        reduction_stats = cursor.fetchall()
        
        for stock_id, count in reduction_stats:
            print(f"   {stock_id}: {count} 次")
        
        # 檢查減資原因分布
        print(f"\n📊 減資原因分布:")
        cursor.execute("""
            SELECT 減資原因, COUNT(*) as count 
            FROM twse_cap_reduction 
            GROUP BY 減資原因 
            ORDER BY count DESC
        """)
        reason_stats = cursor.fetchall()
        
        for reason, count in reason_stats:
            print(f"   {reason}: {count} 次")
        
        # 檢查年度分布
        print(f"\n📊 年度減資分布:")
        cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM twse_cap_reduction 
            GROUP BY year 
            ORDER BY year DESC
        """)
        year_stats = cursor.fetchall()
        
        for year, count in year_stats:
            print(f"   {year}: {count} 次")
        
        conn.close()
        
        print(f"\n✅ 檢查完成！twse_cap_reduction.db 檔案結構正常")
        return True
        
    except Exception as e:
        print(f"❌ 檢查過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_twse_cap_reduction_db()
