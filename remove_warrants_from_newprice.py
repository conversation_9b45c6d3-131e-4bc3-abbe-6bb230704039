#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
從 newprice.db 中移除權證資料 (7xxx 開頭的股票)
"""

import sqlite3
import time

def remove_warrants_from_newprice():
    """移除 newprice.db 中的權證資料"""
    
    print("=" * 60)
    print("🗑️ 從 newprice.db 移除權證資料")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    
    try:
        # 連接資料庫
        print("📡 連接 newprice.db...")
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 檢查移除前的狀態
        print("📊 移除前狀態檢查...")
        
        # 總記錄數
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        total_before = cursor.fetchone()[0]
        
        # 權證記錄數
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        warrant_count = cursor.fetchone()[0]
        
        # 權證股票數
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        warrant_stocks = cursor.fetchone()[0]
        
        # 非權證記錄數
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id NOT LIKE '7%'")
        non_warrant_count = cursor.fetchone()[0]
        
        print(f"  總記錄數: {total_before:,}")
        print(f"  權證記錄數: {warrant_count:,}")
        print(f"  權證股票數: {warrant_stocks:,}")
        print(f"  非權證記錄數: {non_warrant_count:,}")
        print(f"  權證佔比: {warrant_count/total_before*100:.1f}%")
        
        # 顯示一些權證範例
        print(f"\n📋 權證範例:")
        cursor.execute("""
            SELECT DISTINCT stock_id 
            FROM stock_daily_data 
            WHERE stock_id LIKE '7%' 
            ORDER BY stock_id 
            LIMIT 10
        """)
        warrant_examples = cursor.fetchall()
        for (stock_id,) in warrant_examples:
            print(f"    {stock_id}")
        
        # 確認是否繼續
        print(f"\n⚠️ 即將刪除 {warrant_count:,} 筆權證記錄 ({warrant_stocks:,} 檔權證)")
        print(f"   這將釋放約 {warrant_count/total_before*100:.1f}% 的資料庫空間")
        
        confirm = input("\n是否確定要刪除權證資料？(輸入 'YES' 確認): ")
        
        if confirm != 'YES':
            print("❌ 取消刪除操作")
            conn.close()
            return
        
        # 開始刪除
        print(f"\n🗑️ 開始刪除權證資料...")
        start_time = time.time()
        
        # 刪除權證資料
        cursor.execute("DELETE FROM stock_daily_data WHERE stock_id LIKE '7%'")
        deleted_count = cursor.rowcount
        
        # 提交變更
        conn.commit()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 刪除完成！")
        print(f"   刪除記錄數: {deleted_count:,}")
        print(f"   耗時: {duration:.1f} 秒")
        
        # 檢查刪除後的狀態
        print(f"\n📊 刪除後狀態檢查...")
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        total_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stocks_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        remaining_warrants = cursor.fetchone()[0]
        
        print(f"  總記錄數: {total_after:,}")
        print(f"  總股票數: {stocks_after:,}")
        print(f"  剩餘權證: {remaining_warrants:,}")
        print(f"  減少記錄: {total_before - total_after:,}")
        print(f"  減少比例: {(total_before - total_after)/total_before*100:.1f}%")
        
        # 優化資料庫
        print(f"\n🔧 優化資料庫...")
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 檢查最新日期的資料
        print(f"\n📅 最新日期資料檢查:")
        cursor.execute("""
            SELECT date, COUNT(*) as count, COUNT(DISTINCT stock_id) as stocks
            FROM stock_daily_data 
            GROUP BY date 
            ORDER BY date DESC 
            LIMIT 5
        """)
        latest_data = cursor.fetchall()
        
        for date, count, stocks in latest_data:
            print(f"  {date}: {count:,} 筆記錄, {stocks:,} 檔股票")
        
        # 檢查股票族群分布
        print(f"\n📊 剩餘股票族群分布:")
        
        stock_groups = {
            'ETF (00xx)': "stock_id LIKE '00__'",
            'ETF (其他)': "stock_id LIKE '0%' AND stock_id NOT LIKE '00__'",
            '傳統產業 (1xxx)': "stock_id LIKE '1%'",
            '電子股 (2xxx)': "stock_id LIKE '2%'",
            '電子股 (3xxx)': "stock_id LIKE '3%'",
            '傳統產業 (4xxx)': "stock_id LIKE '4%'",
            '電子股 (5xxx)': "stock_id LIKE '5%'",
            '電子股 (6xxx)': "stock_id LIKE '6%'",
            '電子股 (8xxx)': "stock_id LIKE '8%'",
            '其他 (9xxx)': "stock_id LIKE '9%'"
        }
        
        for group_name, condition in stock_groups.items():
            cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data WHERE {condition}")
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"  {group_name}: {count:,} 檔")
        
        conn.close()
        
        print(f"\n✅ 權證移除完成！")
        print(f"📊 資料庫已從 {total_before:,} 筆記錄減少到 {total_after:,} 筆記錄")
        print(f"🎯 現在 newprice.db 只包含 {stocks_after:,} 檔非權證股票")
        
    except Exception as e:
        print(f"❌ 移除失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    remove_warrants_from_newprice()
