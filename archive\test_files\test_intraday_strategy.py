#!/usr/bin/env python3
"""
測試日內交易策略
"""

import sys
import os
sys.path.append('.')

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy
import datetime

def test_intraday_strategy():
    """測試日內交易策略"""
    print("🧪 測試日內交易策略...")
    
    try:
        # 初始化策略
        strategy = IntradayOpeningRangeStrategy()
        print(f"✅ 策略初始化成功: {strategy.name}")
        
        # 測試股票代碼轉換
        print("\n📝 測試股票代碼轉換:")
        test_stocks = ['2330', '2317', '2454', '1301', '2303']
        for stock in test_stocks:
            ticker = strategy.convert_stock_symbol(stock)
            print(f"  {stock} -> {ticker}")
        
        # 測試數據獲取
        print("\n📊 測試數據獲取:")
        for stock in test_stocks[:3]:  # 只測試前3支
            print(f"\n測試 {stock}:")
            
            # 獲取日線數據
            daily_data = strategy.fetch_daily_data(stock, 10)
            if not daily_data.empty:
                print(f"  ✅ 日線數據: {len(daily_data)} 筆記錄")
                
                # 計算指標
                daily_data = strategy.calculate_indicators(daily_data)
                
                # 檢查盤前條件
                pre_market_ok, reason = strategy.check_pre_market_conditions(daily_data)
                print(f"  盤前篩選: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
                print(f"  原因: {reason}")
                
                if pre_market_ok:
                    # 測試盤中數據模擬
                    intraday_data = strategy.fetch_intraday_data(stock, datetime.datetime.now())
                    if not intraday_data.empty:
                        print(f"  ✅ 盤中數據: {len(intraday_data)} 筆記錄")
                        
                        # 測試開盤區間計算
                        range_high, range_low, range_info = strategy.opening_range_breakout(intraday_data)
                        if range_high is not None:
                            print(f"  ✅ {range_info}")
                            
                            # 測試突破信號分析
                            signal = strategy.analyze_breakout_signal(intraday_data, range_high, range_low)
                            if signal:
                                print(f"  信號類型: {signal['breakout_type']}")
                                print(f"  信心度: {signal['confidence']}%")
                        else:
                            print(f"  ❌ 開盤區間計算失敗: {range_info}")
                    else:
                        print("  ❌ 無法獲取盤中數據")
            else:
                print(f"  ❌ 無法獲取日線數據")
        
        # 測試完整策略流程
        print("\n🚀 測試完整策略流程:")
        for stock in ['2330', '2317']:  # 測試2支股票
            print(f"\n完整測試 {stock}:")
            result = strategy.simulate_breakout_trading(stock, datetime.datetime.now())
            
            if result:
                print(f"  結果: {result.get('result', 'unknown')}")
                print(f"  階段: {result.get('stage', 'unknown')}")
                print(f"  原因: {result.get('reason', 'unknown')}")
                
                if 'signal_details' in result:
                    signal = result['signal_details']
                    print(f"  信號詳情: {signal.get('breakout_type', 'none')} (信心度{signal.get('confidence', 0)}%)")
            else:
                print("  ❌ 無結果")
        
        print("\n✅ 日內策略測試完成!")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_intraday_strategy()
    if success:
        print("\n🎉 所有測試通過!")
    else:
        print("\n💥 測試失敗!")
