#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試 GoodInfo 網站回應
"""

import requests
import time

def test_goodinfo_simple():
    """簡單測試 GoodInfo 網站"""
    print("🔍 簡單測試 GoodInfo 網站...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    # 測試不同的URL
    test_urls = [
        "https://goodinfo.tw/tw/index.asp",
        "https://goodinfo.tw/tw/StockDividendScheduleList.asp?MARKET_CAT=全部&INDUSTRY_CAT=全部&YEAR=即將除權息",
        "https://goodinfo.tw/StockInfo/StockDetail.asp?STOCK_ID=2330"
    ]
    
    session = requests.Session()
    
    for i, url in enumerate(test_urls):
        print(f"\n📊 測試 URL {i+1}: {url}")
        
        try:
            response = session.get(url, headers=headers, timeout=30)
            
            print(f"狀態碼: {response.status_code}")
            print(f"內容類型: {response.headers.get('content-type', 'unknown')}")
            print(f"內容長度: {len(response.text)}")
            
            # 儲存回應內容
            filename = f"goodinfo_test_{i+1}.html"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(response.text)
            print(f"已儲存到: {filename}")
            
            # 顯示前500字元
            print(f"內容預覽:")
            print(response.text[:500])
            print("=" * 50)
            
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_goodinfo_simple()
