#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試舊減資任務移除結果
"""

import os
import sys
from datetime import datetime

def check_auto_update_file():
    """檢查 auto_update.py 檔案中是否還有舊減資任務的痕跡"""
    
    print("=" * 80)
    print("🔍 檢查 auto_update.py 中的舊減資任務移除情況")
    print("=" * 80)
    
    auto_update_path = 'auto_update.py'
    
    if not os.path.exists(auto_update_path):
        print(f"❌ 未找到 auto_update.py 檔案")
        return False
    
    try:
        with open(auto_update_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否還有舊任務的痕跡
        old_task_patterns = [
            "('twse_cap_reduction'",
            "('otc_cap_reduction'",
            "== 'twse_cap_reduction'",
            "== 'otc_cap_reduction'",
            "convert_twse_cap_reduction",
            "convert_otc_cap_reduction",
            "convert_all_cap_reduction"
        ]
        
        found_patterns = []
        for pattern in old_task_patterns:
            if pattern in content:
                # 檢查是否在註解中
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if pattern in line and not line.strip().startswith('#'):
                        found_patterns.append(f"第{i}行: {line.strip()}")
        
        if found_patterns:
            print(f"⚠️ 仍發現舊減資任務的痕跡:")
            for pattern in found_patterns:
                print(f"   {pattern}")
            return False
        else:
            print(f"✅ 已成功移除所有舊減資任務")
        
        # 檢查是否保留了統一任務
        if "('cap_reduction'" in content:
            print(f"✅ 統一減資任務 (cap_reduction) 已保留")
        else:
            print(f"❌ 統一減資任務 (cap_reduction) 未找到")
            return False
        
        # 檢查任務列表的完整性
        expected_tasks = [
            "('price'",
            "('benchmark'", 
            "('divide_ratio'",
            "('cap_reduction'"
        ]
        
        missing_tasks = []
        for task in expected_tasks:
            if task not in content:
                missing_tasks.append(task)
        
        if missing_tasks:
            print(f"⚠️ 缺少預期的任務:")
            for task in missing_tasks:
                print(f"   {task}")
        else:
            print(f"✅ 所有預期任務都存在")
        
        return len(found_patterns) == 0 and len(missing_tasks) == 0
        
    except Exception as e:
        print(f"❌ 檢查檔案失敗: {e}")
        return False

def test_auto_update_help():
    """測試 auto_update.py 的幫助訊息"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試 auto_update.py 幫助訊息")
    print("=" * 80)
    
    try:
        # 檢查幫助訊息是否正確更新
        auto_update_path = 'auto_update.py'
        
        with open(auto_update_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查幫助訊息中是否移除了舊的減資轉換選項
        help_section_start = content.find('print("💡 可用參數:")')
        if help_section_start == -1:
            print(f"❌ 未找到幫助訊息區段")
            return False
        
        help_section = content[help_section_start:help_section_start + 500]
        
        # 檢查是否移除了舊選項
        old_help_options = [
            "convert_twse_cap_reduction",
            "convert_otc_cap_reduction", 
            "convert_all_cap_reduction"
        ]
        
        found_old_options = [opt for opt in old_help_options if opt in help_section]
        
        if found_old_options:
            print(f"⚠️ 幫助訊息中仍包含舊選項:")
            for opt in found_old_options:
                print(f"   {opt}")
            return False
        else:
            print(f"✅ 幫助訊息已正確更新，移除了舊選項")
        
        # 檢查是否保留了新選項
        new_options = [
            "divide_ratio",
            "cap_reduction",
            "convert_price",
            "convert_all"
        ]
        
        missing_new_options = [opt for opt in new_options if opt not in help_section]
        
        if missing_new_options:
            print(f"⚠️ 幫助訊息中缺少新選項:")
            for opt in missing_new_options:
                print(f"   {opt}")
            return False
        else:
            print(f"✅ 幫助訊息包含所有新選項")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試幫助訊息失敗: {e}")
        return False

def test_unified_tasks_functionality():
    """測試統一任務的功能性"""
    
    print(f"\n" + "=" * 80)
    print("🧪 測試統一任務功能")
    print("=" * 80)
    
    try:
        # 檢查統一任務是否可以正常導入
        sys.path.insert(0, 'finlab')
        
        try:
            from crawler import crawl_cap_reduction, crawl_divide_ratio
            print(f"✅ 成功導入統一爬蟲函數:")
            print(f"   - crawl_cap_reduction")
            print(f"   - crawl_divide_ratio")
        except ImportError as e:
            print(f"❌ 導入統一爬蟲函數失敗: {e}")
            return False
        
        # 檢查資料庫檔案路徑
        expected_db_files = [
            'D:/Finlab/history/tables/cap_reduction.db',
            'D:/Finlab/history/tables/divide_ratio.db'
        ]
        
        for db_file in expected_db_files:
            if os.path.exists(db_file):
                print(f"✅ 統一資料庫檔案存在: {os.path.basename(db_file)}")
            else:
                print(f"ℹ️ 統一資料庫檔案尚未創建: {os.path.basename(db_file)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試統一任務功能失敗: {e}")
        return False

def show_migration_summary():
    """顯示遷移總結"""
    
    print(f"\n" + "=" * 80)
    print("📊 舊減資任務移除總結")
    print("=" * 80)
    
    print("✅ **已移除的舊任務**:")
    print("   - twse_cap_reduction (上市減資)")
    print("   - otc_cap_reduction (上櫃減資)")
    print()
    
    print("✅ **已移除的舊轉換功能**:")
    print("   - convert_twse_cap_reduction")
    print("   - convert_otc_cap_reduction")
    print("   - convert_all_cap_reduction")
    print()
    
    print("✅ **保留的統一任務**:")
    print("   - cap_reduction (統一減資爬蟲)")
    print("   - divide_ratio (統一除權息爬蟲)")
    print()
    
    print("🚀 **現在可用的命令**:")
    print("```bash")
    print("# 執行統一減資爬蟲")
    print("python auto_update.py cap_reduction")
    print()
    print("# 執行統一除權息爬蟲")
    print("python auto_update.py divide_ratio")
    print()
    print("# 執行完整自動更新")
    print("python auto_update.py")
    print("```")
    print()
    
    print("💡 **優勢**:")
    print("   ✅ 簡化的任務結構")
    print("   ✅ 統一的資料格式")
    print("   ✅ 英文欄位名稱")
    print("   ✅ 減少維護成本")
    print("   ✅ 提高系統一致性")

def main():
    """主函數"""
    
    print("🧪 舊減資任務移除驗證")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: 檢查檔案內容
    file_check_success = check_auto_update_file()
    
    # 測試2: 檢查幫助訊息
    help_check_success = test_auto_update_help()
    
    # 測試3: 檢查統一任務功能
    unified_task_success = test_unified_tasks_functionality()
    
    # 顯示遷移總結
    show_migration_summary()
    
    print(f"\n" + "=" * 80)
    print("📊 移除驗證結果總結")
    print("=" * 80)
    
    print(f"🔍 檔案內容檢查: {'✅ 成功' if file_check_success else '❌ 失敗'}")
    print(f"📝 幫助訊息檢查: {'✅ 成功' if help_check_success else '❌ 失敗'}")
    print(f"🧪 統一任務功能: {'✅ 成功' if unified_task_success else '❌ 失敗'}")
    
    if all([file_check_success, help_check_success, unified_task_success]):
        print(f"\n🎉 舊減資任務移除驗證全部通過！")
        print(f"💡 系統已成功簡化:")
        print(f"   - 移除了 2 個舊減資任務")
        print(f"   - 移除了 3 個舊轉換功能")
        print(f"   - 保留了 2 個統一任務")
        print(f"   - 系統更加簡潔和一致")
    else:
        print(f"\n⚠️ 部分驗證失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
