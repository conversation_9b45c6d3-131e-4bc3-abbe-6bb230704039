#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試只顯示真實數據的開盤前監控
"""

import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入開盤前監控系統
from O3mh_gui_v21_optimized import PreMarketMonitor

def test_real_data_only():
    """測試只顯示真實數據的功能"""
    print("🔍 測試只顯示真實數據的開盤前監控")
    print("=" * 50)
    
    # 初始化監控系統
    monitor = PreMarketMonitor()
    print(f"✅ 監控系統初始化: {monitor.name} v{monitor.version}")
    
    print("\n📊 測試數據獲取...")
    print("-" * 30)
    
    # 測試美股數據
    print("\n🇺🇸 測試美股指數數據:")
    us_data = monitor.get_us_indices()
    
    real_data_count = 0
    simulated_data_count = 0
    
    for name, data in us_data.items():
        status = data.get('status', '')
        if '真實數據' in status:
            real_data_count += 1
            print(f"  ✅ {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) - 真實數據")
        else:
            simulated_data_count += 1
            print(f"  🟡 {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) - {status}")
    
    print(f"\n📊 美股數據統計:")
    print(f"  真實數據: {real_data_count}")
    print(f"  模擬數據: {simulated_data_count}")
    
    # 測試完整掃描
    print(f"\n🔍 測試完整掃描功能:")
    print("-" * 30)
    
    scan_results = monitor.run_full_scan()
    
    if scan_results is None:
        print("✅ 正確行為: 由於缺乏真實數據，系統跳過了開盤前監控")
        print("💡 這避免了顯示誤導性的模擬數據")
    else:
        print("📊 掃描成功，分析數據品質:")
        
        total_items = 0
        real_items = 0
        
        for category, data in scan_results.items():
            if category in ['timestamp', 'economic_events']:
                continue
            if isinstance(data, dict):
                for item_data in data.values():
                    if isinstance(item_data, dict) and 'status' in item_data:
                        total_items += 1
                        if '真實數據' in item_data['status']:
                            real_items += 1
        
        real_ratio = real_items / total_items if total_items > 0 else 0
        print(f"  總項目: {total_items}")
        print(f"  真實數據: {real_items} ({real_ratio:.1%})")
        
        if real_ratio >= 0.5:
            print("  ✅ 真實數據比例足夠，顯示監控結果")
        else:
            print("  ⚠️ 真實數據比例不足，應該跳過顯示")
    
    print(f"\n🎯 測試結論:")
    print("-" * 20)
    
    if scan_results is None:
        print("✅ 系統正確地拒絕顯示模擬數據")
        print("✅ 避免了用戶被誤導")
        print("✅ 只有在有真實數據時才會顯示開盤前監控")
    else:
        print("⚠️ 系統顯示了監控結果")
        print("💡 請檢查數據品質是否符合標準")

def test_data_quality_threshold():
    """測試數據品質門檻"""
    print(f"\n🔬 測試數據品質門檻")
    print("-" * 30)
    
    monitor = PreMarketMonitor()
    
    # 模擬不同的數據品質情況
    test_scenarios = [
        {
            'name': '全部真實數據',
            'real_ratio': 1.0,
            'should_display': True
        },
        {
            'name': '70%真實數據',
            'real_ratio': 0.7,
            'should_display': True
        },
        {
            'name': '50%真實數據',
            'real_ratio': 0.5,
            'should_display': True
        },
        {
            'name': '30%真實數據',
            'real_ratio': 0.3,
            'should_display': False
        },
        {
            'name': '全部模擬數據',
            'real_ratio': 0.0,
            'should_display': False
        }
    ]
    
    print("數據品質門檻測試 (門檻: 50%):")
    for scenario in test_scenarios:
        real_ratio = scenario['real_ratio']
        should_display = scenario['should_display']
        
        # 模擬判斷邏輯
        would_display = real_ratio >= 0.5
        
        status = "✅ 正確" if would_display == should_display else "❌ 錯誤"
        action = "顯示" if would_display else "跳過"
        
        print(f"  {scenario['name']} ({real_ratio:.0%}): {action} - {status}")

def recommend_usage():
    """使用建議"""
    print(f"\n💡 使用建議")
    print("-" * 20)
    
    print("1. 📊 數據品質優先:")
    print("   • 只有真實數據比例≥50%時才顯示監控")
    print("   • 避免用戶被模擬數據誤導")
    print("   • 確保投資決策基於真實市場資訊")
    print()
    print("2. 🕐 最佳使用時間:")
    print("   • 美股交易時間: 台灣時間21:30-04:00")
    print("   • 亞股交易時間: 台灣時間09:00-15:00")
    print("   • 避開API限制高峰期")
    print()
    print("3. 🔧 故障排除:")
    print("   • 如果看不到監控數據，表示API受限")
    print("   • 等待一段時間後重試")
    print("   • 或在不同時間段使用")
    print()
    print("4. ⚠️ 注意事項:")
    print("   • 模擬數據僅供系統測試使用")
    print("   • 投資決策請以真實數據為準")
    print("   • 系統會自動過濾低品質數據")

if __name__ == "__main__":
    # 設置日誌級別
    logging.basicConfig(
        level=logging.WARNING,  # 只顯示警告和錯誤
        format='%(levelname)s: %(message)s'
    )
    
    try:
        # 執行測試
        test_real_data_only()
        
        # 測試品質門檻
        test_data_quality_threshold()
        
        # 提供使用建議
        recommend_usage()
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
