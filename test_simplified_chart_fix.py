#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化版圖表橫軸日期顯示修復
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class SimplifiedChartTestWindow(QMainWindow):
    """簡化版圖表橫軸日期顯示修復測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎯 簡化版圖表橫軸日期顯示修復測試")
        self.setGeometry(100, 100, 950, 750)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("🎯 簡化版圖表橫軸日期顯示修復測試")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #28a745; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 功能說明
        info_text = QTextEdit()
        info_text.setMaximumHeight(320)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3 style="color: #ffffff;">🎯 簡化版修復策略</h3>
        
        <h4 style="color: #cccccc;">🔧 核心改進</h4>
        <ul>
            <li><b>簡化X軸數據</b> - 使用序號 (0, 1, 2, ...) 而非時間戳</li>
            <li><b>直接設置標籤</b> - 繪製圖表後立即設置日期標籤</li>
            <li><b>移除複雜邏輯</b> - 不再使用 DateAxisItem，避免兼容性問題</li>
            <li><b>簡化標籤格式</b> - 使用 MM/DD 格式減少標籤長度</li>
            <li><b>智能間隔</b> - 根據數據點數量自動選擇標籤間隔</li>
        </ul>
        
        <h4 style="color: #cccccc;">⚡ 技術實現</h4>
        <ul>
            <li><b>X軸數據</b> - list(range(len(daily_data))) 生成序號</li>
            <li><b>日期標籤</b> - date_strings = [d.strftime('%m/%d') for d in daily_data['Date']]</li>
            <li><b>標籤設置</b> - bottom_axis.setTicks([date_labels])</li>
            <li><b>樣式設置</b> - setPen(color='white') + setTextPen(color='white')</li>
            <li><b>強制顯示</b> - show() + setStyle(showValues=True)</li>
        </ul>
        
        <h4 style="color: #cccccc;">🎯 預期效果</h4>
        <ul>
            <li><b>橫軸顯示</b> - 應該看到 MM/DD 格式的日期標籤</li>
            <li><b>標籤間隔</b> - 根據數據量智能調整，避免重疊</li>
            <li><b>清晰可見</b> - 白色字體在深色背景下清晰可見</li>
            <li><b>完整覆蓋</b> - 包含第一個和最後一個日期</li>
            <li><b>無錯誤</b> - 不再依賴可能失敗的 DateAxisItem</li>
        </ul>
        
        <h3 style="color: #ffffff;">🧪 測試重點</h3>
        
        <p style="color: #28a745; font-weight: bold;">
        <b>關鍵測試步驟：</b><br>
        1. 開啟台股爬蟲界面<br>
        2. 切換到「圖表分析」分頁<br>
        3. 選擇「歷史指數資料」<br>
        4. 點擊「生成圖表」<br>
        5. <span style="color: #00ff00;">觀察橫軸是否顯示 MM/DD 格式的日期</span><br>
        6. 檢查控制台輸出的調試信息<br>
        7. 測試不同圖表類型的效果
        </p>
        
        <h4 style="color: #cccccc;">🔍 調試信息</h4>
        <p style="color: #cccccc;">
        簡化版系統會輸出以下調試信息：<br>
        • "🔥 開始設置日期標籤..."<br>
        • "✅ 成功設置 X 個日期標籤"<br>
        • "📅 標籤內容: ['01/15', '01/22', ...]"<br>
        • "✅ 歷史指數圖表生成完成！"
        </p>
        
        <h4 style="color: #cccccc;">💡 優勢</h4>
        <ul>
            <li><b>簡單可靠</b> - 不依賴複雜的 DateAxisItem</li>
            <li><b>兼容性好</b> - 適用於各種 PyQtGraph 版本</li>
            <li><b>性能優秀</b> - 減少了不必要的軸設置操作</li>
            <li><b>易於調試</b> - 邏輯簡單，問題容易定位</li>
        </ul>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🎯 開啟台股爬蟲 (測試簡化版日期顯示修復)")
        test_btn.clicked.connect(self.open_crawler_dialog)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 成功提示
        success_label = QLabel("🎯 簡化版修復已實施！使用序號X軸 + 直接標籤設置 + 智能間隔")
        success_label.setStyleSheet("color: #28a745; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #1a4a1a; border: 1px solid #28a745; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 初始化日誌
        self.log("🎯 簡化版圖表橫軸日期顯示修復測試已啟動")
        self.log("⚡ 使用序號X軸 - 避免時間戳兼容性問題")
        self.log("⚡ 直接標籤設置 - 繪製後立即設置日期標籤")
        self.log("⚡ 移除複雜邏輯 - 不再依賴 DateAxisItem")
        self.log("⚡ 簡化標籤格式 - MM/DD 格式更簡潔")
        self.log("⚡ 智能間隔控制 - 根據數據量調整標籤密度")
    
    def log(self, message):
        """添加日誌"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
    
    def get_timestamp(self):
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def open_crawler_dialog(self):
        """開啟爬蟲對話框"""
        try:
            self.log("🎯 正在開啟台股爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("🎯 請重點測試以下功能：")
            self.log("   • 切換到「圖表分析」分頁")
            self.log("   • 選擇「歷史指數資料」")
            self.log("   • 點擊「生成圖表」按鈕")
            self.log("   • 🔍 觀察橫軸是否顯示 MM/DD 日期")
            self.log("   • 📊 檢查控制台的調試輸出")
            self.log("   • 🎯 測試不同的圖表類型")
            
            # 顯示對話框
            dialog.exec()
            
            self.log("✅ 爬蟲對話框已關閉")
            
        except Exception as e:
            self.log(f"❌ 開啟爬蟲界面失敗: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("簡化版圖表橫軸日期顯示修復測試")
    app.setApplicationVersion("3.0")
    
    print("🎯 簡化版圖表橫軸日期顯示修復測試已啟動")
    print("⚡ 使用序號X軸 - 避免時間戳問題")
    print("⚡ 直接標籤設置 - 立即設置日期標籤")
    print("⚡ 移除複雜邏輯 - 不依賴 DateAxisItem")
    print("⚡ 簡化標籤格式 - MM/DD 更簡潔")
    print("⚡ 智能間隔控制 - 自動調整密度")
    print("🎯 現在橫軸應該能正確顯示日期了！")
    
    # 創建主窗口
    window = SimplifiedChartTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
