#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試策略交集分析中股票名稱顯示修正
驗證括號內顯示的是股票名稱而不是"股票代碼"格式
"""

import re
import sys
import os

def test_taiwan_stocks_dictionary():
    """測試台灣股票對照表是否包含常見股票"""
    print("🔍 測試台灣股票對照表完整性")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取 taiwan_stocks 字典內容
        pattern = r'taiwan_stocks = \{(.*?)\}'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 找不到 taiwan_stocks 字典")
            return False
        
        dict_content = match.group(1)
        
        # 從圖片中看到的股票代碼
        test_codes = [
            "0052", "0057", "00692", "00757", "00770", "00830", "00876", "00877", 
            "00888", "00893", "00895", "00909", "00911", "1315", "1519", "1802", 
            "2421", "3062", "3167", "3189", "3289", "3455", "3587", "3708", 
            "4714", "4991", "5243", "5278", "6122", "6124", "6417", "6426", 
            "6530", "6613", "6664", "6667", "6698", "6933", "8039", "8046", 
            "8358", "8462"
        ]
        
        found_count = 0
        missing_codes = []
        
        for code in test_codes:
            if f'"{code}"' in dict_content:
                found_count += 1
                print(f"✅ 找到股票代碼: {code}")
            else:
                missing_codes.append(code)
                print(f"❌ 缺失股票代碼: {code}")
        
        print(f"\n📊 統計結果:")
        print(f"  總測試股票: {len(test_codes)}")
        print(f"  已包含: {found_count}")
        print(f"  缺失: {len(missing_codes)}")
        
        if missing_codes:
            print(f"\n⚠️  缺失的股票代碼: {', '.join(missing_codes)}")
        
        # 至少要包含80%的測試股票
        success_rate = found_count / len(test_codes)
        if success_rate >= 0.8:
            print(f"✅ 對照表完整性良好 ({success_rate:.1%})")
            return True
        else:
            print(f"❌ 對照表完整性不足 ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_get_stock_names_method():
    """測試 get_stock_names_for_codes 方法的改進"""
    print("\n🔍 測試股票名稱獲取方法")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找方法定義
        method_pattern = r'def get_stock_names_for_codes\(self, stock_codes\):(.*?)(?=def \w+\(self\)|class \w+|$)'
        match = re.search(method_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 找不到 get_stock_names_for_codes 方法")
            return False
        
        method_content = match.group(1)
        
        # 檢查是否有數據庫查詢邏輯
        db_query_patterns = [
            r'cursor\.execute',
            r'stock_info',
            r'SELECT.*name.*FROM',
            r'missing_codes'
        ]
        
        found_improvements = []
        for pattern in db_query_patterns:
            if re.search(pattern, method_content):
                found_improvements.append(pattern)
        
        if found_improvements:
            print("✅ 發現方法改進:")
            for improvement in found_improvements:
                print(f"    {improvement}")
        else:
            print("❌ 沒有發現方法改進")
            return False
        
        # 檢查是否移除了 f"股票{code}" 格式
        if 'f"股票{code}"' in method_content:
            print("❌ 仍然使用 f'股票{code}' 格式")
            return False
        else:
            print("✅ 已移除 f'股票{code}' 格式")
        
        # 檢查是否使用代碼本身作為後備
        if 'stock_names[code] = code' in method_content:
            print("✅ 使用代碼本身作為後備名稱")
            return True
        else:
            print("❌ 沒有適當的後備機制")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_intersection_result_format():
    """測試交集結果顯示格式"""
    print("\n🔍 測試交集結果顯示格式")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找結果格式化代碼
        format_patterns = [
            r'f"{code}\({.*?}\)"',
            r'stock_info = \[.*?for code.*?\]',
            r'get_stock_names_for_codes'
        ]
        
        found_formats = []
        for pattern in format_patterns:
            matches = re.findall(pattern, content)
            if matches:
                found_formats.extend(matches)
        
        if found_formats:
            print("✅ 發現結果格式化邏輯:")
            for fmt in found_formats[:3]:  # 只顯示前3個
                print(f"    {fmt}")
        else:
            print("❌ 沒有發現結果格式化邏輯")
            return False
        
        # 檢查是否在交集分析中使用
        intersection_methods = ['calculate_strategy_intersection', 'analyze_all_strategy_combinations']
        
        for method_name in intersection_methods:
            method_pattern = f'def {method_name}\\(self\\):(.*?)(?=def \\w+\\(self\\)|class \\w+|$)'
            match = re.search(method_pattern, content, re.DOTALL)
            
            if match:
                method_content = match.group(1)
                if 'get_stock_names_for_codes' in method_content:
                    print(f"✅ {method_name} 使用股票名稱獲取方法")
                else:
                    print(f"❌ {method_name} 沒有使用股票名稱獲取方法")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def simulate_stock_name_display():
    """模擬股票名稱顯示效果"""
    print("\n🎯 模擬股票名稱顯示效果")
    print("=" * 40)
    
    # 模擬的股票代碼和對應名稱
    test_stocks = {
        "2330": "台積電",
        "0052": "富邦科技", 
        "00692": "富邦公司治理",
        "00757": "統一FANG+",
        "1315": "中華",
        "3289": "宜特",
        "6667": "信紘科",
        "8039": "台虹"
    }
    
    print("📋 修正前的顯示格式:")
    for code in test_stocks.keys():
        print(f"  {code}(股票{code})")  # 舊格式
    
    print("\n📋 修正後的顯示格式:")
    for code, name in test_stocks.items():
        print(f"  {code}({name})")  # 新格式
    
    print("\n✅ 改進效果:")
    print("  • 括號內顯示實際股票名稱")
    print("  • 提升結果可讀性")
    print("  • 用戶更容易識別股票")
    print("  • 專業度提升")
    
    return True

def test_database_fallback_mechanism():
    """測試數據庫後備查詢機制"""
    print("\n🔍 測試數據庫後備查詢機制")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找數據庫查詢相關代碼
        db_patterns = [
            r'cursor\.execute.*SELECT.*name.*FROM.*stock_info',
            r'missing_codes',
            r'db_connections.*price',
            r'fetchone\(\)'
        ]
        
        found_db_logic = []
        for pattern in db_patterns:
            if re.search(pattern, content):
                found_db_logic.append(pattern)
        
        if len(found_db_logic) >= 3:
            print("✅ 發現完整的數據庫後備查詢機制:")
            for logic in found_db_logic:
                print(f"    {logic}")
        else:
            print(f"❌ 數據庫後備查詢機制不完整 (找到 {len(found_db_logic)}/4)")
            return False
        
        # 檢查錯誤處理
        error_patterns = [
            r'except.*db_error',
            r'logging\.warning',
            r'數據庫查詢.*失敗'
        ]
        
        found_error_handling = False
        for pattern in error_patterns:
            if re.search(pattern, content):
                found_error_handling = True
                print(f"✅ 發現錯誤處理: {pattern}")
                break
        
        if not found_error_handling:
            print("❌ 沒有發現適當的錯誤處理")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 策略交集股票名稱顯示修正測試")
    print("=" * 50)
    
    # 執行所有測試
    tests = [
        ("台灣股票對照表", test_taiwan_stocks_dictionary),
        ("股票名稱獲取方法", test_get_stock_names_method),
        ("交集結果格式", test_intersection_result_format),
        ("數據庫後備機制", test_database_fallback_mechanism),
        ("顯示效果模擬", simulate_stock_name_display)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 統計結果
    print("\n" + "=" * 50)
    print("📊 測試結果摘要:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("\n🎉 所有測試通過！股票名稱顯示問題已修正")
        print("\n💡 修正效果:")
        print("  ✅ 擴充了台灣股票對照表")
        print("  ✅ 添加了數據庫後備查詢機制")
        print("  ✅ 移除了 '股票代碼' 格式顯示")
        print("  ✅ 括號內顯示實際股票名稱")
        print("  ✅ 提升了結果可讀性")
    else:
        print(f"\n⚠️  {total - passed} 個測試失敗，需要進一步檢查")
    
    return passed == total

if __name__ == "__main__":
    main()
