# Finlab GUI 更新說明

## ✅ 已完成的更新

### 1. 可編輯的更新期間
- **問題**：之前的更新期間是固定的，無法修改
- **解決**：現在每個檔案的更新期間都可以編輯
- **功能**：
  - 開始日期輸入框：可以修改開始日期
  - 結束日期輸入框：可以修改結束日期
  - 日期格式：YYYY-MM-DD（如：2025-07-14）
  - 自動驗證：會檢查日期格式是否正確

### 2. 界面改進
```
┌─────────────────────────────────────────────────────────────┐
│  檔案名稱          │ 最後日期   │ 建議更新範圍        │ 更新 │
├─────────────────────────────────────────────────────────────┤
│ pe.pkl            │ 2025-07-13 │[2025-07-14][至][2025-07-20]│[更新]│
│                   │            │    ↑可編輯  ↑可編輯      │     │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 pe.pkl 更新問題分析

### 問題診斷結果
經過測試發現：
- ✅ pe.pkl檔案存在（31筆資料）
- ✅ 最後日期：2025-07-13
- ✅ 建議更新範圍：2025-07-14 至 2025-07-20
- ❌ **無法導入finlab.crawler模組**

### 根本原因
```
✗ 無法導入 crawl_pe 函數
✗ 無法導入finlab.crawler，使用簡化版測試
```

**問題**：系統中沒有安裝finlab套件，所以所有爬蟲功能都使用簡化版（假資料）

### 解決方案

#### 方案1：安裝finlab套件
```bash
pip install finlab
```

#### 方案2：檢查finlab安裝
```bash
python -c "import finlab.crawler; print('finlab已安裝')"
```

#### 方案3：使用本地finlab
如果您有本地的finlab代碼，確保：
1. finlab目錄在Python路徑中
2. crawler.py檔案存在
3. crawl_pe函數可用

## 🎯 新功能使用方法

### 編輯更新期間
1. **修改開始日期**：
   - 點擊開始日期輸入框
   - 輸入新的開始日期（如：2025-07-01）
   - 格式必須是：YYYY-MM-DD

2. **修改結束日期**：
   - 點擊結束日期輸入框
   - 輸入新的結束日期（如：2025-07-20）
   - 格式必須是：YYYY-MM-DD

3. **執行更新**：
   - 點擊該行的「更新」按鈕
   - 系統會使用您設定的日期範圍進行更新

### 使用場景

#### 場景1：更新最近一週資料
```
開始日期：2025-07-14
結束日期：2025-07-20
```

#### 場景2：補充歷史資料
```
開始日期：2025-06-01
結束日期：2025-07-13
```

#### 場景3：重新爬取特定期間
```
開始日期：2025-07-01
結束日期：2025-07-31
```

## 🚨 注意事項

### 日期格式
- ✅ 正確：2025-07-14
- ❌ 錯誤：2025/07/14
- ❌ 錯誤：07-14-2025
- ❌ 錯誤：2025-7-14

### 日期範圍
- 開始日期必須早於或等於結束日期
- 建議不要設定過大的日期範圍（避免請求過多）
- 週末和假日可能沒有資料

### 錯誤處理
如果出現錯誤，檢查：
1. 日期格式是否正確
2. 網路連接是否正常
3. finlab套件是否已安裝
4. API是否可用

## 🔄 更新流程

### 自動流程
1. 系統檢測現有檔案的最後日期
2. 自動設定建議的更新範圍
3. 用戶可以修改日期範圍
4. 點擊更新按鈕執行

### 手動流程
1. 用戶修改開始/結束日期
2. 點擊更新按鈕
3. 系統驗證日期格式
4. 執行爬蟲更新
5. 顯示結果和日誌

## 📊 測試結果

### pe.pkl檔案狀態
- 檔案存在：✅
- 資料筆數：31
- 資料範圍：2025-06-13 至 2025-07-13
- 最後日期：2025-07-13
- 建議更新：2025-07-14 至 2025-07-20

### 爬蟲函數映射
- price.pkl → crawl_price
- bargin_report.pkl → crawl_bargin
- **pe.pkl → crawl_pe** ⚠️（需要finlab套件）
- monthly_report.pkl → crawl_monthly_report

## 🛠️ 下一步

1. **安裝finlab套件**（如果需要真實資料）
2. **測試pe.pkl更新功能**
3. **驗證其他檔案的更新功能**
4. **確認日期編輯功能正常**

現在GUI已經支援可編輯的更新期間，您可以根據需要調整每個檔案的更新範圍！
