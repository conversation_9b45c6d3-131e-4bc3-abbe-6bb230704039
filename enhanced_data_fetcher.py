#!/usr/bin/env python3
"""
增強版台股數據獲取模組
整合多個數據源，提供穩定的股票數據獲取服務
集成智能Yahoo獲取器，優化台股數據獲取
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
from datetime import datetime, timedelta
import json
import re

# 導入智能Yahoo獲取器
try:
    from smart_yahoo_fetcher import SmartYahooFetcher
    SMART_YAHOO_AVAILABLE = True
    logging.info("✅ 智能Yahoo獲取器已載入")
except ImportError:
    SMART_YAHOO_AVAILABLE = False
    logging.warning("⚠️ 智能Yahoo獲取器不可用")

class EnhancedDataFetcher:
    """增強版數據獲取器，整合多個數據源"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 優化後的數據源優先級 - 智能Yahoo優先
        self.data_sources = [
            'smart_yahoo',     # 智能Yahoo獲取器 - 最優先
            'twelvedata',      # Twelve Data
            'twse_api',        # 證交所API
            'twstock',         # 台股專用庫
            'database'         # 本地數據庫
        ]

        # 緩存設置
        self.cache = {}
        self.cache_timeout = 300  # 5分鐘緩存

        # 初始化智能Yahoo獲取器
        if SMART_YAHOO_AVAILABLE:
            self.smart_yahoo = SmartYahooFetcher()
            logging.info("✅ 智能Yahoo獲取器已初始化")
        else:
            self.smart_yahoo = None

        # 初始化 Twelve Data
        try:
            from twelvedata_fetcher import TwelveDataFetcher
            self.twelve_data = TwelveDataFetcher()
            logging.info("✅ Twelve Data API 已初始化")
        except ImportError:
            self.twelve_data = None
            logging.warning("⚠️ Twelve Data 模組不可用")
        
    def get_stock_data(self, stock_id, days=90, source='auto'):
        """
        獲取股票數據，自動嘗試多個數據源
        
        Args:
            stock_id: 股票代碼
            days: 獲取天數
            source: 指定數據源或'auto'自動選擇
        
        Returns:
            pandas.DataFrame: 股票數據
        """
        # 檢查緩存
        cache_key = f"{stock_id}_{days}"
        if cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_timeout:
                logging.info(f"📦 使用緩存數據: {stock_id}")
                return data
        
        # 格式化股票代碼
        formatted_id = self._format_stock_id(stock_id)
        
        if source == 'auto':
            # 自動嘗試多個數據源
            for src in self.data_sources:
                try:
                    logging.info(f"🔄 嘗試數據源 {src} 獲取 {stock_id}")
                    data = self._fetch_from_source(formatted_id, days, src)
                    if not data.empty:
                        logging.info(f"✅ 成功從 {src} 獲取 {stock_id} 數據")
                        # 緩存數據
                        self.cache[cache_key] = (time.time(), data)
                        return data
                except Exception as e:
                    logging.warning(f"⚠️ 數據源 {src} 失敗: {e}")
                    continue
            
            logging.error(f"❌ 所有數據源都無法獲取 {stock_id} 的數據")
            return pd.DataFrame()
        else:
            # 使用指定數據源
            return self._fetch_from_source(formatted_id, days, source)
    
    def _format_stock_id(self, stock_id):
        """格式化股票代碼"""
        # 移除前導零和後綴
        clean_id = str(stock_id).lstrip('0')
        if not clean_id:
            clean_id = '0'

        # 嘗試生成padded格式（僅對數字代碼）
        try:
            padded = f"{int(clean_id):04d}" if clean_id.isdigit() else clean_id
        except:
            padded = clean_id

        return {
            'original': stock_id,
            'clean': clean_id,
            'yfinance_tw': f"{clean_id}.TW",
            'yfinance_two': f"{clean_id}.TWO",
            'padded': padded
        }
    
    def _fetch_from_source(self, formatted_id, days, source):
        """從指定數據源獲取數據"""
        if source == 'smart_yahoo':
            return self._fetch_smart_yahoo(formatted_id, days)
        elif source == 'yfinance':
            return self._fetch_yfinance(formatted_id, days)
        elif source == 'twelvedata':
            return self._fetch_twelvedata(formatted_id, days)
        elif source == 'twse_api':
            return self._fetch_twse_api(formatted_id, days)
        elif source == 'yahoo_tw':
            return self._fetch_yahoo_tw(formatted_id, days)
        elif source == 'investing_com':
            return self._fetch_investing(formatted_id, days)
        elif source == 'twstock':
            return self._fetch_twstock(formatted_id, days)
        elif source == 'database':
            return self._fetch_database(formatted_id, days)
        else:
            raise ValueError(f"未知的數據源: {source}")

    def _fetch_smart_yahoo(self, formatted_id, days):
        """使用智能Yahoo獲取器獲取數據"""
        try:
            if not self.smart_yahoo:
                logging.warning("⚠️ 智能Yahoo獲取器未初始化")
                return pd.DataFrame()

            stock_id = formatted_id['clean']

            # 使用智能Yahoo獲取器
            df = self.smart_yahoo.get_stock_data(stock_id, days)

            if not df.empty:
                logging.info(f"✅ 智能Yahoo成功獲取 {stock_id} 數據 ({len(df)} 天)")
                return df
            else:
                logging.warning(f"⚠️ 智能Yahoo無法獲取 {stock_id} 數據")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"❌ 智能Yahoo獲取失敗 {formatted_id['clean']}: {e}")
            return pd.DataFrame()

    def _fetch_yfinance(self, formatted_id, days):
        """使用yfinance獲取數據 - 優化版本，添加錯誤過濾"""
        try:
            import yfinance as yf

            stock_id = formatted_id['clean']

            # 智能格式選擇 - 根據股票代碼判斷市場
            if stock_id.startswith(('6', '8', '9')):
                # 櫃買市場優先
                suffixes = ['.TWO', '.TW']
            else:
                # 上市市場優先
                suffixes = ['.TW', '.TWO']

            # 錯誤過濾模式
            ignore_errors = [
                "possibly delisted",
                "No data found, symbol may be delisted",
                "HTTP Error 404"
            ]

            for suffix in suffixes:
                try:
                    ticker = stock_id + suffix
                    stock = yf.Ticker(ticker)

                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days + 30)  # 多獲取一些數據

                    data = stock.history(start=start_date, end=end_date)

                    if not data.empty:
                        # 轉換格式
                        df = pd.DataFrame({
                            'date': data.index,
                            'Open': data['Open'],
                            'High': data['High'],
                            'Low': data['Low'],
                            'Close': data['Close'],
                            'Volume': data['Volume']
                        })
                        df = df.reset_index(drop=True)
                        df = df.tail(days)  # 只取需要的天數

                        logging.info(f"✅ yfinance 成功獲取 {ticker} 數據 ({len(df)} 天)")
                        return df

                except Exception as e:
                    error_msg = str(e).lower()

                    # 檢查是否為可忽略的錯誤
                    should_ignore = any(pattern.lower() in error_msg for pattern in ignore_errors)

                    if should_ignore:
                        logging.debug(f"忽略yfinance錯誤 {ticker}: {e}")
                    else:
                        logging.warning(f"yfinance {ticker} 錯誤: {e}")
                    continue

            logging.warning(f"⚠️ yfinance 無法獲取 {stock_id} 數據")
            return pd.DataFrame()

        except ImportError:
            logging.warning("yfinance 模組未安裝")
            return pd.DataFrame()

    def _fetch_twelvedata(self, formatted_id, days):
        """使用 Twelve Data API 獲取數據"""
        try:
            if not self.twelve_data:
                logging.warning("⚠️ Twelve Data 未初始化")
                return pd.DataFrame()

            stock_id = formatted_id['clean']

            # 檢查是否為台股（Twelve Data 免費版不支援台股）
            if self._is_taiwan_stock(stock_id):
                logging.debug(f"跳過台股 {stock_id}，Twelve Data 免費版不支援")
                return pd.DataFrame()

            # 嘗試獲取美股數據
            df = self.twelve_data.get_stock_data(stock_id, '1day', days)

            if not df.empty:
                # 轉換為標準格式
                df = df.rename(columns={
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                })

                # 確保有date欄位
                if 'date' not in df.columns and 'datetime' in df.columns:
                    df['date'] = df['datetime'].dt.date

                logging.info(f"✅ Twelve Data 成功獲取 {stock_id} 數據 ({len(df)} 筆)")
                return df
            else:
                logging.warning(f"⚠️ Twelve Data 無法獲取 {stock_id} 數據")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"Twelve Data 獲取失敗: {e}")
            return pd.DataFrame()

    def _is_taiwan_stock(self, stock_id):
        """判斷是否為台股"""
        try:
            # 台股代碼通常是4位數字
            # 如果包含字母，則不是台股
            if not stock_id.isdigit():
                return False

            stock_num = int(stock_id)
            return 1000 <= stock_num <= 9999
        except:
            return False

    def _fetch_twse_api(self, formatted_id, days):
        """使用證交所API獲取數據"""
        try:
            stock_id = formatted_id['padded']
            
            # 獲取最近的交易數據
            url = f"https://www.twse.com.tw/exchangeReport/STOCK_DAY"
            
            data_frames = []
            current_date = datetime.now()
            
            # 獲取最近幾個月的數據
            for i in range(3):  # 獲取3個月數據
                target_date = current_date - timedelta(days=30*i)
                params = {
                    'response': 'json',
                    'date': target_date.strftime('%Y%m%d'),
                    'stockNo': stock_id
                }
                
                try:
                    response = self.session.get(url, params=params, timeout=10)
                    if response.status_code == 200:
                        json_data = response.json()
                        
                        if 'data' in json_data and json_data['data']:
                            df_month = self._parse_twse_data(json_data['data'])
                            if not df_month.empty:
                                data_frames.append(df_month)
                    
                    time.sleep(0.5)  # 避免請求過快
                    
                except Exception as e:
                    logging.debug(f"TWSE API 月份 {target_date.strftime('%Y%m')} 失敗: {e}")
                    continue
            
            if data_frames:
                df = pd.concat(data_frames, ignore_index=True)
                df = df.sort_values('date').tail(days)
                logging.info(f"✅ TWSE API 成功獲取 {stock_id} 數據 ({len(df)} 天)")
                return df
            
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"TWSE API 獲取失敗: {e}")
            return pd.DataFrame()
    
    def _parse_twse_data(self, data):
        """解析證交所數據格式"""
        try:
            rows = []
            for row in data:
                if len(row) >= 9:
                    date_str = row[0].replace('/', '-')
                    # 轉換民國年為西元年
                    year, month, day = date_str.split('-')
                    year = str(int(year) + 1911)
                    date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    
                    # 清理數值字符串
                    def clean_number(s):
                        return float(s.replace(',', '').replace('--', '0'))
                    
                    rows.append({
                        'date': pd.to_datetime(date_str),
                        'Open': clean_number(row[3]),
                        'High': clean_number(row[4]),
                        'Low': clean_number(row[5]),
                        'Close': clean_number(row[6]),
                        'Volume': clean_number(row[1])
                    })
            
            return pd.DataFrame(rows)
            
        except Exception as e:
            logging.error(f"解析TWSE數據失敗: {e}")
            return pd.DataFrame()
    
    def _fetch_yahoo_tw(self, formatted_id, days):
        """使用Yahoo台股API獲取數據"""
        try:
            stock_id = formatted_id['clean']
            url = f"https://tw.stock.yahoo.com/quote/{stock_id}/history"
            
            # 這裡可以實現Yahoo台股的爬蟲邏輯
            # 由於Yahoo的反爬蟲機制，這裡先返回空數據
            logging.info(f"Yahoo TW API 暫不可用")
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"Yahoo TW 獲取失敗: {e}")
            return pd.DataFrame()
    
    def _fetch_investing(self, formatted_id, days):
        """使用Investing.com獲取數據"""
        try:
            # 這裡可以實現Investing.com的API調用
            logging.info(f"Investing.com API 暫不可用")
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"Investing.com 獲取失敗: {e}")
            return pd.DataFrame()
    
    def _fetch_twstock(self, formatted_id, days):
        """使用twstock獲取數據"""
        try:
            import twstock
            
            stock_id = formatted_id['clean']
            stock = twstock.Stock(stock_id)
            
            # 獲取歷史數據
            data = stock.fetch_from(2024, 1)  # 從2024年開始獲取
            
            if data:
                df = pd.DataFrame({
                    'date': [d.date for d in data],
                    'Open': [d.open for d in data],
                    'High': [d.high for d in data],
                    'Low': [d.low for d in data],
                    'Close': [d.close for d in data],
                    'Volume': [d.capacity for d in data]
                })
                
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date').tail(days)
                
                logging.info(f"✅ twstock 成功獲取 {stock_id} 數據 ({len(df)} 天)")
                return df
            
            return pd.DataFrame()
            
        except ImportError:
            logging.warning("twstock 模組未安裝")
            return pd.DataFrame()
        except Exception as e:
            logging.error(f"twstock 獲取失敗: {e}")
            return pd.DataFrame()

    def _fetch_database(self, formatted_id, days):
        """從本地數據庫獲取數據"""
        try:
            # 這裡實現從本地數據庫獲取數據的邏輯
            # 需要根據實際的數據庫結構實現
            stock_id = formatted_id['clean']
            logging.info(f"💾 嘗試從數據庫獲取 {stock_id}")

            # 暫時返回空DataFrame，需要實際實現
            return pd.DataFrame()

        except Exception as e:
            logging.error(f"數據庫獲取失敗: {e}")
            return pd.DataFrame()
    
    def get_stock_list(self):
        """獲取股票列表"""
        try:
            # 使用證交所API獲取股票列表
            url = "https://www.twse.com.tw/exchangeReport/STOCK_DAY_ALL"
            params = {
                'response': 'json',
                'date': datetime.now().strftime('%Y%m%d')
            }
            
            response = self.session.get(url, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    stocks = []
                    for row in data['data']:
                        if len(row) >= 2:
                            stocks.append({
                                'stock_id': row[0],
                                'name': row[1]
                            })
                    return pd.DataFrame(stocks)
            
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"獲取股票列表失敗: {e}")
            return pd.DataFrame()

# 創建全局實例
enhanced_fetcher = EnhancedDataFetcher()

def get_stock_data_enhanced(stock_id, days=90):
    """便捷函數：獲取股票數據"""
    return enhanced_fetcher.get_stock_data(stock_id, days)

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    # 測試獲取台積電數據
    print("🧪 測試獲取台積電(2330)數據...")
    df = get_stock_data_enhanced('2330', 30)
    
    if not df.empty:
        print(f"✅ 成功獲取 {len(df)} 天數據")
        print(df.head())
        print(f"日期範圍: {df['date'].min()} 到 {df['date'].max()}")
    else:
        print("❌ 獲取數據失敗")
