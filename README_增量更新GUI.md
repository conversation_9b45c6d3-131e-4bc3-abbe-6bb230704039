# Finlab 爬蟲系統 - 增量更新版

## 🚀 新功能特色

### 📊 智能資料狀態檢測
- **自動檢測現有pkl檔案**：系統會自動掃描 `finlab_ml_course/history/tables/` 目錄
- **顯示最後更新日期**：清楚顯示每個資料檔案的最後日期
- **智能更新建議**：自動計算從最後日期到今天的更新範圍

### 🔄 增量更新功能
- **避免重複爬取**：只更新缺失的日期範圍，節省時間和資源
- **個別更新**：可以單獨更新特定的資料檔案
- **批量更新**：一鍵更新所有需要更新的資料
- **實時狀態顯示**：更新過程中顯示進度和結果

### 📋 資料檔案管理
支援的資料檔案類型：
- `price.pkl` - 股價資料
- `bargin_report.pkl` - 融資融券
- `pe.pkl` - 本益比
- `monthly_report.pkl` - 月報
- `balance_sheet.pkl` - 資產負債表
- `income_sheet.pkl` - 損益表
- `cash_flows.pkl` - 現金流量表
- `twse_divide_ratio.pkl` - 上市除權息
- `otc_divide_ratio.pkl` - 上櫃除權息
- `twse_cap_reduction.pkl` - 上市減資
- `otc_cap_reduction.pkl` - 上櫃減資

## 🖥️ 界面說明

### 資料檔案狀態表格
| 欄位 | 說明 |
|------|------|
| 檔案名稱 | 資料類型的中文名稱 |
| 最後日期 | 檔案中資料的最後日期 |
| 建議更新範圍 | 從最後日期+1天到今天 |
| 狀態 | 可更新/已是最新/需要爬取 |
| 操作 | 雙擊該行可單獨更新 |

### 操作方式
1. **單個更新**：雙擊表格中的任一行
2. **全部更新**：點擊「全部更新」按鈕
3. **重新檢查**：點擊「重新檢查」按鈕刷新狀態

## 🛠️ 使用方法

### 1. 啟動GUI
```bash
python finlab_crawler_gui.py
```

### 2. 創建測試資料（可選）
```bash
python test_gui.py
# 選擇選項 1 創建測試資料
```

### 3. 檢查現有檔案
```bash
python test_gui.py
# 選擇選項 2 檢查現有檔案狀態
```

## 📝 更新邏輯

### 一般資料（日頻資料）
- 股價、融資融券、本益比、月報等
- 按日期範圍更新：`crawler_func(start_date, end_date)`

### 財務資料（季頻資料）
- 資產負債表、損益表、現金流量表
- 按季度更新：自動計算季度發布日期

### 除權息和減資資料
- 按日期範圍更新，但資料可能不連續

## ⚡ 優勢

1. **節省時間**：只更新缺失的資料，避免重複爬取
2. **智能管理**：自動檢測檔案狀態，提供更新建議
3. **用戶友好**：直觀的表格界面，操作簡單
4. **錯誤處理**：完善的錯誤處理和日誌記錄
5. **進度顯示**：實時顯示更新進度和結果

## 🔧 技術特點

- **檔案檢測**：使用pickle讀取現有資料檔案
- **日期計算**：智能計算更新範圍
- **多線程**：後台執行更新，不阻塞界面
- **異常處理**：完善的錯誤處理機制
- **日誌系統**：詳細的操作日誌記錄

## 📋 系統要求

- Python 3.7+
- tkinter（GUI界面）
- pandas（資料處理）
- finlab（爬蟲功能，可選）

## 🚨 注意事項

1. 確保 `finlab_ml_course/history/tables/` 目錄存在
2. 首次使用建議先創建測試資料
3. 更新過程中請勿關閉程式
4. 建議定期備份重要資料檔案

## 🔄 更新流程

1. **檢測階段**：掃描現有pkl檔案，讀取最後日期
2. **計算階段**：計算需要更新的日期範圍
3. **顯示階段**：在表格中顯示檔案狀態和建議
4. **更新階段**：執行增量爬取，只獲取缺失資料
5. **完成階段**：更新檔案，刷新狀態顯示

這個增量更新版本大大提升了資料管理的效率，讓您可以輕鬆維護最新的金融資料！
