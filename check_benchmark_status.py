#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 benchmark.pkl 檔案的狀況
"""

import pandas as pd
import os
from datetime import datetime

def check_benchmark_file():
    """檢查 benchmark.pkl 檔案"""
    file_path = 'history/tables/benchmark.pkl'
    
    print('🔍 檢查 benchmark.pkl 檔案狀況')
    print('=' * 60)
    
    if not os.path.exists(file_path):
        print(f'❌ 檔案不存在: {file_path}')
        return False
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f'📁 檔案資訊:')
        print(f'   路徑: {file_path}')
        print(f'   大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)')
        print(f'   修改時間: {datetime.fromtimestamp(file_mtime)}')
        
        # 讀取資料
        print(f'\n📖 正在讀取資料...')
        data = pd.read_pickle(file_path)
        
        print(f'✅ 讀取成功:')
        print(f'   資料筆數: {len(data):,}')
        print(f'   資料類型: {type(data)}')
        print(f'   索引類型: {type(data.index)}')
        print(f'   索引名稱: {data.index.names}')
        
        if hasattr(data, 'columns'):
            print(f'   欄位數量: {len(data.columns)}')
            print(f'   欄位名稱: {list(data.columns)[:5]}...')
        
        # 檢查日期範圍
        if 'date' in data.index.names:
            dates = data.index.get_level_values('date')
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f'\n📅 日期範圍分析:')
            print(f'   起始日期: {start_date}')
            print(f'   結束日期: {end_date}')
            print(f'   時間跨度: {end_date.year - start_date.year + 1} 年')
            print(f'   唯一日期數: {unique_dates}')
            
            # 檢查最近的資料
            recent_dates = sorted(dates.unique())[-5:]
            print(f'   最近5個日期: {[str(d)[:10] for d in recent_dates]}')
            
            # 檢查台股指數
            if '發行量加權股價指數' in data.columns:
                taiex_data = data['發行量加權股價指數']
                print(f'\n📈 台股指數分析:')
                print(f'   最小值: {taiex_data.min():.2f}')
                print(f'   最大值: {taiex_data.max():.2f}')
                
                # 最新的台股指數
                latest_date = dates.max()
                latest_taiex = data[data.index.get_level_values('date') == latest_date]['發行量加權股價指數'].iloc[0]
                print(f'   最新指數 ({latest_date.strftime("%Y-%m-%d")}): {latest_taiex:.2f}')
            
            # 檢查資料完整性
            print(f'\n🔍 資料完整性檢查:')
            
            # 檢查是否有重複的日期
            duplicate_dates = dates.duplicated().sum()
            if duplicate_dates > 0:
                print(f'   ⚠️ 發現重複日期: {duplicate_dates} 個')
            else:
                print(f'   ✅ 沒有重複日期')
            
            # 檢查是否有空值
            null_count = data.isnull().sum().sum()
            if null_count > 0:
                print(f'   ⚠️ 發現空值: {null_count} 個')
            else:
                print(f'   ✅ 沒有空值')
            
            # 檢查資料密度
            total_days = (end_date - start_date).days + 1
            coverage = unique_dates / total_days * 100
            print(f'   資料覆蓋率: {coverage:.1f}% (考慮週末假日是正常的)')
            
            # 檢查更新狀況
            today = datetime.now().date()
            end_date_only = end_date.date() if hasattr(end_date, 'date') else end_date
            days_behind = (today - end_date_only).days
            
            print(f'\n📊 更新狀況:')
            print(f'   今天日期: {today}')
            print(f'   最新資料: {end_date_only}')
            print(f'   落後天數: {days_behind} 天')
            
            if days_behind <= 1:
                print(f'   ✅ 資料很新')
            elif days_behind <= 7:
                print(f'   ⚠️ 資料稍舊')
            else:
                print(f'   ❌ 資料較舊')
            
        else:
            print(f'\n⚠️ 未找到標準的日期索引')
            print(f'   索引內容預覽: {data.index[:5]}')
        
        # 顯示樣本資料
        print(f'\n📊 樣本資料 (前3筆):')
        print(data.head(3))
        
        print(f'\n✅ benchmark.pkl 檔案檢查完成')
        return True
        
    except Exception as e:
        print(f'❌ 讀取失敗: {str(e)}')
        import traceback
        print(f'詳細錯誤: {traceback.format_exc()}')
        return False

def check_backup_files():
    """檢查備份檔案"""
    print(f'\n🔍 檢查備份檔案:')
    backup_dir = 'history/tables'
    
    if os.path.exists(backup_dir):
        backup_files = [f for f in os.listdir(backup_dir) if 'benchmark_backup' in f]
        
        if backup_files:
            print(f'   找到 {len(backup_files)} 個備份檔案:')
            for backup_file in sorted(backup_files):
                backup_path = os.path.join(backup_dir, backup_file)
                backup_size = os.path.getsize(backup_path)
                backup_mtime = os.path.getmtime(backup_path)
                print(f'   • {backup_file}: {backup_size/1024/1024:.1f} MB ({datetime.fromtimestamp(backup_mtime)})')
        else:
            print(f'   沒有找到備份檔案')
    else:
        print(f'   備份目錄不存在')

def main():
    """主函數"""
    success = check_benchmark_file()
    check_backup_files()
    
    if success:
        print(f'\n🎉 檢查完成！benchmark.pkl 檔案狀況良好')
    else:
        print(f'\n⚠️ 檢查發現問題，請查看上述錯誤訊息')

if __name__ == "__main__":
    main()
