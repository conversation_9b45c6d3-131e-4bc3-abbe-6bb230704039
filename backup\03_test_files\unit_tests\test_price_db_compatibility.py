"""
測試統一資料庫與price.db的相容性
"""

import sqlite3
import pandas as pd
import os
from unified_stock_database import UnifiedStockDatabase

def test_price_db_compatibility():
    """測試與price.db的相容性"""
    print("🧪 測試統一資料庫與price.db的相容性")
    print("=" * 50)
    
    # 1. 檢查原始price.db結構
    print("1. 檢查原始price.db結構...")
    original_db_path = "db/price.db"
    
    if os.path.exists(original_db_path):
        conn = sqlite3.connect(original_db_path)
        cursor = conn.cursor()
        
        # 獲取表結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        original_columns = [col[1] for col in cursor.fetchall()]
        print(f"   原始price.db欄位: {original_columns}")
        
        # 獲取樣本資料
        cursor.execute("SELECT * FROM stock_daily_data LIMIT 3")
        original_sample = cursor.fetchall()
        print(f"   原始資料範例: {original_sample[0] if original_sample else '無資料'}")
        
        conn.close()
    else:
        print("   ⚠️ 原始price.db不存在")
        return False
    
    # 2. 建立統一資料庫
    print("\n2. 建立統一資料庫...")
    unified_db_path = "test_unified_price.db"
    
    # 刪除測試資料庫（如果存在）
    if os.path.exists(unified_db_path):
        os.remove(unified_db_path)
    
    unified_db = UnifiedStockDatabase(unified_db_path)
    
    # 檢查統一資料庫結構
    conn = sqlite3.connect(unified_db_path)
    cursor = conn.cursor()
    
    cursor.execute("PRAGMA table_info(stock_daily_data)")
    unified_columns = [col[1] for col in cursor.fetchall()]
    print(f"   統一資料庫欄位: {unified_columns}")
    
    conn.close()
    
    # 3. 測試資料插入
    print("\n3. 測試資料插入...")
    
    # 建立測試資料（使用price.db格式）
    test_data = pd.DataFrame({
        'stock_id': ['2330', '2317', '0050'],
        'stock_name': ['台積電', '鴻海', '元大台灣50'],
        'listing_status': ['上市', '上市', '上市'],
        'industry': ['半導體業', '電腦及週邊設備業', 'ETF'],
        'Volume': [50000000, 30000000, 20000000],
        'Transaction': [25000, 15000, 10000],
        'TradeValue': [2500000000, 1500000000, 1000000000],
        'Open': [500.0, 100.0, 125.0],
        'High': [510.0, 105.0, 127.0],
        'Low': [495.0, 98.0, 123.0],
        'Close': [505.0, 102.0, 126.0],
        'Change': [5.0, 2.0, 1.0],
        'date': ['2025-07-18', '2025-07-18', '2025-07-18']
    })
    
    saved_count = unified_db.save_daily_data(test_data, 'test')
    print(f"   成功插入 {saved_count} 筆測試資料")
    
    # 4. 測試查詢相容性
    print("\n4. 測試查詢相容性...")
    
    # 測試get_stock_data方法（模擬原有price.db的查詢方式）
    df_2330 = unified_db.get_stock_data('2330')
    print(f"   查詢台積電資料: {len(df_2330)} 筆")
    if not df_2330.empty:
        print(f"   欄位: {list(df_2330.columns)}")
        print(f"   範例: {df_2330.iloc[0].to_dict()}")
    
    # 測試日期範圍查詢
    df_range = unified_db.get_stock_data('2330', '2025-07-01', '2025-07-31')
    print(f"   日期範圍查詢: {len(df_range)} 筆")
    
    # 5. 測試直接SQL查詢（模擬現有策略程式的查詢方式）
    print("\n5. 測試直接SQL查詢...")
    
    conn = sqlite3.connect(unified_db_path)
    
    # 模擬現有策略程式可能使用的查詢
    test_queries = [
        "SELECT * FROM stock_daily_data WHERE stock_id = '2330'",
        "SELECT stock_id, Close, Volume FROM stock_daily_data WHERE date = '2025-07-18'",
        "SELECT DISTINCT stock_id FROM stock_daily_data ORDER BY stock_id",
        "SELECT * FROM stock_daily_data WHERE stock_id = '2330' AND date >= '2025-07-01'"
    ]
    
    for i, query in enumerate(test_queries, 1):
        try:
            df = pd.read_sql_query(query, conn)
            print(f"   查詢 {i}: 成功，{len(df)} 筆結果")
        except Exception as e:
            print(f"   查詢 {i}: 失敗 - {e}")
    
    conn.close()
    
    # 6. 測試統計功能
    print("\n6. 測試統計功能...")
    stats = unified_db.get_database_stats()
    print(f"   資料庫統計: {stats}")
    
    # 7. 清理測試檔案
    print("\n7. 清理測試檔案...")
    if os.path.exists(unified_db_path):
        os.remove(unified_db_path)
        print("   測試資料庫已刪除")
    
    print("\n✅ 相容性測試完成！")
    return True

def test_migration_compatibility():
    """測試遷移功能的相容性"""
    print("\n🔄 測試資料遷移相容性")
    print("=" * 50)
    
    from data_migration_tool import DataMigrationTool
    
    # 建立遷移工具
    migration_tool = DataMigrationTool("test_migration.db")
    
    # 檢查來源資料庫
    print("1. 檢查來源資料庫...")
    db_status = migration_tool.check_source_databases()
    
    for db_name, status in db_status.items():
        print(f"   {db_name}: {'✅' if status['exists'] else '❌'} {status['path']}")
        if status['exists']:
            print(f"     表格: {status['tables']}")
            print(f"     記錄數: {status['total_records']:,}")
    
    # 如果price.db存在，測試遷移
    if db_status['price_db']['exists']:
        print("\n2. 測試price.db遷移...")
        result = migration_tool.migrate_price_db()
        print(f"   遷移結果: {'成功' if result['success'] else '失敗'}")
        print(f"   遷移記錄數: {result['migrated_records']}")
        if result['error']:
            print(f"   錯誤: {result['error']}")
    
    # 清理測試檔案
    test_db_path = "test_migration.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        print("\n3. 測試檔案已清理")
    
    print("\n✅ 遷移相容性測試完成！")

def compare_with_original_price_db():
    """與原始price.db進行詳細比較"""
    print("\n📊 與原始price.db詳細比較")
    print("=" * 50)
    
    original_db_path = "db/price.db"
    if not os.path.exists(original_db_path):
        print("❌ 原始price.db不存在，無法比較")
        return
    
    # 讀取原始price.db的結構和資料
    conn_original = sqlite3.connect(original_db_path)
    
    # 獲取表結構
    cursor = conn_original.cursor()
    cursor.execute("PRAGMA table_info(stock_daily_data)")
    original_schema = cursor.fetchall()
    
    print("1. 表結構比較:")
    print("   原始price.db結構:")
    for col in original_schema:
        print(f"     {col[1]} ({col[2]})")
    
    # 獲取資料統計
    cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
    original_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
    original_stocks = cursor.fetchone()[0]
    
    cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data")
    original_date_range = cursor.fetchone()
    
    print(f"\n2. 資料統計比較:")
    print(f"   原始price.db:")
    print(f"     總記錄數: {original_count:,}")
    print(f"     股票數量: {original_stocks:,}")
    print(f"     日期範圍: {original_date_range[0]} 至 {original_date_range[1]}")
    
    # 獲取樣本資料
    cursor.execute("SELECT * FROM stock_daily_data LIMIT 5")
    original_samples = cursor.fetchall()
    
    print(f"\n3. 樣本資料:")
    for i, sample in enumerate(original_samples, 1):
        print(f"   樣本 {i}: {sample}")
    
    conn_original.close()
    
    print("\n✅ 比較完成！")

if __name__ == "__main__":
    print("🚀 統一股票資料庫相容性測試")
    print("=" * 60)
    
    # 執行相容性測試
    success = test_price_db_compatibility()
    
    if success:
        # 執行遷移測試
        test_migration_compatibility()
        
        # 與原始price.db比較
        compare_with_original_price_db()
    
    print("\n🎉 所有測試完成！")
    input("\n按 Enter 結束...")
