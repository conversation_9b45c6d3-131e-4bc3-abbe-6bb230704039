#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試改善後的台灣證交所市場數據爬蟲對話框
"""

import sys
import os

# 避免導入其他模組時的問題
os.environ['PYTHONPATH'] = os.getcwd()

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

def test_dialog():
    """測試對話框"""
    app = QApplication(sys.argv)
    
    try:
        # 只導入我們需要的對話框
        from twse_market_data_dialog import TWSEMarketDataDialog
        
        # 創建對話框
        dialog = TWSEMarketDataDialog()
        
        # 顯示對話框
        dialog.show()
        
        print("✅ 對話框已開啟，請檢查以下改善項目：")
        print("1. 📅 日期範圍選擇功能")
        print("2. 🎨 結果顯示區域的顏色對比")
        print("3. 📊 數據查看分頁")
        print("4. 👁️ 查看數據按鈕")
        print("5. 📤 導出CSV按鈕")
        print("6. 📏 對話框大小調整")
        
        # 運行應用程式
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        print("請確認 twse_market_data_dialog.py 檔案存在")
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")

if __name__ == "__main__":
    test_dialog()
