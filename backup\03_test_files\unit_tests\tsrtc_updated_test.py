#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新版 TSRTC 台灣股票即時爬蟲功能
使用新的證交所API端點
"""

import os
import json
import csv
import time
import ssl
from datetime import date, datetime
import requests
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class UpdatedTsrtcTester:
    """更新版 TSRTC 爬蟲測試器"""
    
    def __init__(self):
        # 新的API端點
        self.base_url = 'https://mis.twse.com.tw'
        self.api_endpoint = f'{self.base_url}/stock/api/getStockInfo.jsp'
        self.index_page = f'{self.base_url}/stock/index.jsp'
        
        self.session = requests.Session()
        # 設置SSL驗證為False來避免證書問題
        self.session.verify = False
        
        # 設置User-Agent
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': self.base_url
        })
        
    def test_connection(self):
        """測試基本連接"""
        print("🔍 測試證交所網站連接...")
        
        try:
            # 測試主頁
            response = self.session.get(self.base_url, timeout=10)
            print(f"✅ 主網站連接成功，狀態碼: {response.status_code}")
            
            # 測試基本市況頁面
            response = self.session.get(f'{self.base_url}/stock/index', timeout=10)
            print(f"✅ 基本市況頁面連接成功，狀態碼: {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"❌ 連接測試失敗: {e}")
            return False
    
    def test_api_endpoint(self):
        """測試API端點可用性"""
        print("🔍 測試API端點...")
        
        try:
            # 先訪問基本市況頁面建立session
            self.session.get(self.index_page, timeout=10)
            
            # 構建測試查詢
            timestamp = int(time.time() * 1000)
            test_url = f'{self.api_endpoint}?_={timestamp}&ex_ch=tse_2330.tw'
            
            print(f"🔗 測試URL: {test_url}")
            
            response = self.session.get(test_url, timeout=10)
            
            if response.status_code == 200:
                try:
                    content = json.loads(response.text)
                    rtcode = content.get('rtcode', 'N/A')
                    rtmessage = content.get('rtmessage', 'N/A')
                    
                    print(f"✅ API端點回應成功")
                    print(f"  - rtcode: {rtcode}")
                    print(f"  - rtmessage: {rtmessage}")
                    
                    if rtcode == '0000':
                        print("✅ API狀態正常")
                        return True
                    else:
                        print(f"⚠️ API回應異常狀態: {rtcode} - {rtmessage}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失敗: {e}")
                    print(f"原始回應: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ API端點回應失敗，狀態碼: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API端點測試失敗: {e}")
            return False
    
    def test_stock_data(self, stock_id='2330'):
        """測試股票數據獲取"""
        print(f"\n🧪 測試股票 {stock_id} 數據獲取...")
        
        try:
            # 建立session
            self.session.get(self.index_page, timeout=10)
            
            # 構建查詢URL
            timestamp = int(time.time() * 1000)
            channel = f'tse_{stock_id}.tw'
            query_url = f'{self.api_endpoint}?_={timestamp}&ex_ch={channel}'
            
            print(f"🔗 查詢URL: {query_url}")
            
            response = self.session.get(query_url, timeout=10)
            
            if response.status_code == 200:
                content = json.loads(response.text)
                
                if content.get('rtcode') == '0000':
                    msg_array = content.get('msgArray', [])
                    
                    if msg_array:
                        stock_data = msg_array[0]
                        self.display_stock_info(stock_data, stock_id)
                        return stock_data
                    else:
                        print("❌ 沒有獲取到股票數據")
                        return None
                else:
                    print(f"❌ API回應錯誤: {content.get('rtmessage', 'Unknown error')}")
                    return None
            else:
                print(f"❌ HTTP請求失敗，狀態碼: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 股票數據測試失敗: {e}")
            return None
    
    def display_stock_info(self, stock_data, stock_id):
        """顯示股票資訊"""
        print(f"\n📈 股票 {stock_id} 即時資訊:")
        
        # 基本資訊
        print(f"  股票代號: {stock_data.get('c', 'N/A')}")
        print(f"  股票名稱: {stock_data.get('n', 'N/A')}")
        print(f"  全名: {stock_data.get('nf', 'N/A')}")
        print(f"  交易所: {stock_data.get('ex', 'N/A')}")
        
        # 價格資訊
        current_price = stock_data.get('z', 'N/A')
        open_price = stock_data.get('o', 'N/A')
        high_price = stock_data.get('h', 'N/A')
        low_price = stock_data.get('l', 'N/A')
        prev_close = stock_data.get('y', 'N/A')
        
        print(f"  最近成交價: {current_price}")
        print(f"  開盤價: {open_price}")
        print(f"  最高價: {high_price}")
        print(f"  最低價: {low_price}")
        print(f"  昨收價: {prev_close}")
        
        # 計算漲跌
        if current_price != 'N/A' and prev_close != 'N/A':
            try:
                change = float(current_price) - float(prev_close)
                change_pct = (change / float(prev_close)) * 100
                print(f"  漲跌: {change:+.2f} ({change_pct:+.2f}%)")
            except:
                pass
        
        # 成交量資訊
        print(f"  當盤成交量: {stock_data.get('tv', 'N/A')}")
        print(f"  累積成交量: {stock_data.get('v', 'N/A')}")
        
        # 五檔資訊
        ask_prices = stock_data.get('a', 'N/A')
        ask_volumes = stock_data.get('f', 'N/A')
        bid_prices = stock_data.get('b', 'N/A')
        bid_volumes = stock_data.get('g', 'N/A')
        
        print(f"  賣出五檔: {ask_prices}")
        print(f"  賣出量: {ask_volumes}")
        print(f"  買入五檔: {bid_prices}")
        print(f"  買入量: {bid_volumes}")
        
        # 時間資訊
        print(f"  資料時間: {stock_data.get('t', 'N/A')}")
        print(f"  日期: {stock_data.get('d', 'N/A')}")
        
        # 漲跌停資訊
        print(f"  漲停價: {stock_data.get('u', 'N/A')}")
        print(f"  跌停價: {stock_data.get('w', 'N/A')}")
    
    def test_multiple_stocks(self, stock_list=['2330', '2317', '2454', '1301', '2382']):
        """測試多支股票查詢"""
        print(f"\n🧪 測試多支股票查詢: {stock_list}")
        
        try:
            # 建立session
            self.session.get(self.index_page, timeout=10)
            
            # 構建查詢URL
            timestamp = int(time.time() * 1000)
            channels = '|'.join(f'tse_{stock}.tw' for stock in stock_list)
            query_url = f'{self.api_endpoint}?_={timestamp}&ex_ch={channels}'
            
            print(f"🔗 查詢URL: {query_url}")
            
            response = self.session.get(query_url, timeout=10)
            
            if response.status_code == 200:
                content = json.loads(response.text)
                
                if content.get('rtcode') == '0000':
                    msg_array = content.get('msgArray', [])
                    
                    print(f"✅ 成功獲取 {len(msg_array)} 支股票數據")
                    
                    for i, stock_data in enumerate(msg_array):
                        stock_id = stock_data.get('c', 'N/A')
                        stock_name = stock_data.get('n', 'N/A')
                        price = stock_data.get('z', 'N/A')
                        volume = stock_data.get('v', 'N/A')
                        time_str = stock_data.get('t', 'N/A')
                        
                        print(f"  {i+1}. {stock_id} {stock_name}: 價格={price}, 成交量={volume}, 時間={time_str}")
                    
                    return msg_array
                else:
                    print(f"❌ API回應錯誤: {content.get('rtmessage', 'Unknown error')}")
                    return None
            else:
                print(f"❌ HTTP請求失敗，狀態碼: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 多股票查詢測試失敗: {e}")
            return None
    
    def test_original_stocks(self):
        """測試原始TSRTC清單中的股票"""
        original_stocks = ['3380', '3019', '2349', '2609', '2324']
        print(f"\n🧪 測試原始TSRTC清單股票: {original_stocks}")
        
        return self.test_multiple_stocks(original_stocks)

def main():
    """主測試函數"""
    print("=" * 60)
    print("🚀 更新版 TSRTC 台灣股票即時爬蟲測試")
    print("=" * 60)
    
    tester = UpdatedTsrtcTester()
    
    # 1. 測試基本連接
    print("\n1️⃣ 測試基本連接")
    connection_ok = tester.test_connection()
    
    if not connection_ok:
        print("❌ 基本連接失敗，無法進行後續測試")
        return
    
    # 2. 測試API端點
    print("\n2️⃣ 測試API端點")
    api_ok = tester.test_api_endpoint()
    
    if not api_ok:
        print("❌ API端點不可用，無法進行後續測試")
        return
    
    # 3. 測試單一股票數據
    print("\n3️⃣ 測試單一股票數據（台積電 2330）")
    single_result = tester.test_stock_data('2330')
    
    # 4. 測試多支熱門股票
    print("\n4️⃣ 測試多支熱門股票")
    multi_result = tester.test_multiple_stocks(['2330', '2317', '2454', '1301', '2382'])
    
    # 5. 測試原始TSRTC清單股票
    print("\n5️⃣ 測試原始TSRTC清單股票")
    original_result = tester.test_original_stocks()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    print(f"✅ 基本連接: {'正常' if connection_ok else '異常'}")
    print(f"✅ API端點: {'正常' if api_ok else '異常'}")
    print(f"✅ 單一股票查詢: {'成功' if single_result else '失敗'}")
    print(f"✅ 多股票查詢: {'成功' if multi_result else '失敗'}")
    print(f"✅ 原始清單查詢: {'成功' if original_result else '失敗'}")
    
    if connection_ok and api_ok and (single_result or multi_result):
        print("\n🎉 更新版TSRTC爬蟲功能正常！")
        print("📝 功能特色:")
        print("  • ✅ 可以獲取即時股價數據")
        print("  • ✅ 支援單一和批量查詢")
        print("  • ✅ 數據包含完整的五檔資訊")
        print("  • ✅ 包含成交量和時間戳")
        print("  • ✅ 適合用於即時監控和數據收集")
        
        print("\n📋 API狀態:")
        print("  • 證交所API端點已更新為HTTPS")
        print("  • 需要先訪問基本市況頁面建立session")
        print("  • API回應格式保持不變")
        print("  • 建議使用適當的User-Agent和Referer")
        
    else:
        print("\n⚠️ TSRTC爬蟲功能存在問題")
        print("📝 可能原因:")
        print("  • 證交所API政策變更")
        print("  • 需要更新API端點URL")
        print("  • 需要適當的HTTP headers")
        print("  • 可能需要處理反爬蟲機制")

if __name__ == "__main__":
    main()
