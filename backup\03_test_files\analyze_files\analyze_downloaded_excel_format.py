#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析下載的Excel文件格式
"""

import os
import pandas as pd
import glob

def analyze_excel_format():
    """分析Excel文件格式"""
    print("🔍 分析下載的Excel文件格式")
    print("=" * 60)
    
    # 尋找最新的Excel文件
    download_dir = "D:/Finlab/history/tables/monthly_revenue"
    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    # 選擇最新的文件
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📄 分析文件: {os.path.basename(latest_file)}")
    print(f"📁 完整路徑: {latest_file}")
    
    try:
        # 檢查文件類型
        with open(latest_file, 'rb') as f:
            header = f.read(100)
            print(f"📄 文件開頭: {header[:50]}")

        # 嘗試不同的讀取方法
        df = None

        # 方法1: 嘗試作為HTML讀取
        try:
            print("🔄 嘗試HTML格式讀取...")
            df = pd.read_html(latest_file, header=None, encoding='utf-8-sig')[0]
            print("✅ HTML格式讀取成功")
        except Exception as e:
            print(f"❌ HTML格式失敗: {str(e)[:50]}...")

        # 方法2: 嘗試作為Excel讀取
        if df is None:
            try:
                print("🔄 嘗試Excel格式讀取...")
                df = pd.read_excel(latest_file, header=None)
                print("✅ Excel格式讀取成功")
            except Exception as e:
                print(f"❌ Excel格式失敗: {str(e)[:50]}...")

        # 方法3: 嘗試作為CSV讀取
        if df is None:
            try:
                print("🔄 嘗試CSV格式讀取...")
                df = pd.read_csv(latest_file, header=None, encoding='utf-8-sig')
                print("✅ CSV格式讀取成功")
            except Exception as e:
                print(f"❌ CSV格式失敗: {str(e)[:50]}...")

        if df is None:
            print("❌ 所有讀取方法都失敗")
            return

        print(f"📊 文件包含 {len(df)} 行, {len(df.columns)} 列")
        
        print("\n📋 前20行內容:")
        print("-" * 80)
        for i in range(min(20, len(df))):
            row = df.iloc[i]
            print(f"行 {i+1:2d}: {[str(cell)[:30] for cell in row[:6]]}")
        
        print("\n🔍 尋找可能的數據起始行:")
        print("-" * 80)
        
        # 檢查每一行的第一列
        for index in range(min(50, len(df))):
            first_cell = str(df.iloc[index, 0]).strip()
            
            # 檢查各種可能的格式
            formats_found = []
            
            # 格式1: 2025/06
            if '/' in first_cell:
                parts = first_cell.split('/')
                if len(parts) == 2:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        if 2020 <= year <= 2030 and 1 <= month <= 12:
                            formats_found.append(f"年/月格式: {first_cell}")
                    except ValueError:
                        pass
            
            # 格式2: 2025-06
            if '-' in first_cell:
                parts = first_cell.split('-')
                if len(parts) == 2:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        if 2020 <= year <= 2030 and 1 <= month <= 12:
                            formats_found.append(f"年-月格式: {first_cell}")
                    except ValueError:
                        pass
            
            # 格式3: 純數字年月 (202506)
            if first_cell.isdigit() and len(first_cell) == 6:
                try:
                    year = int(first_cell[:4])
                    month = int(first_cell[4:6])
                    if 2020 <= year <= 2030 and 1 <= month <= 12:
                        formats_found.append(f"數字年月格式: {first_cell}")
                except ValueError:
                    pass
            
            # 格式4: 包含"年"和"月"的中文格式
            if '年' in first_cell and '月' in first_cell:
                formats_found.append(f"中文年月格式: {first_cell}")
            
            # 格式5: 包含數字的其他格式
            if any(char.isdigit() for char in first_cell) and len(first_cell) > 3:
                formats_found.append(f"包含數字: {first_cell}")
            
            if formats_found:
                print(f"行 {index+1:2d}: {first_cell[:50]} -> {', '.join(formats_found)}")
        
        print("\n📊 列標題分析:")
        print("-" * 80)
        
        # 檢查前幾行是否包含列標題
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            row_str = ' | '.join([str(cell)[:15] for cell in row[:8]])
            print(f"行 {i+1:2d}: {row_str}")
            
            # 檢查是否包含營收相關關鍵字
            row_text = ' '.join([str(cell) for cell in row])
            keywords = ['營收', '月增率', '年增率', '累計營收', '累計年增率', '開盤價', '收盤價']
            found_keywords = [kw for kw in keywords if kw in row_text]
            if found_keywords:
                print(f"      -> 包含關鍵字: {', '.join(found_keywords)}")
        
        print("\n💡 建議的解析策略:")
        print("-" * 80)
        print("1. 檢查實際的日期格式")
        print("2. 尋找包含營收關鍵字的標題行")
        print("3. 確定數據起始行的位置")
        print("4. 驗證數據列的對應關係")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    analyze_excel_format()

if __name__ == "__main__":
    main()
