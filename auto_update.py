# 修復 ipywidgets 依賴問題
import sys
import types

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass

    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

print(f"finlab 路径: {finlab_path}")
print(f"路径存在: {os.path.exists(finlab_path)}")

# 現在可以安全導入 crawler 模組
from crawler import (
    # 爬蟲函數
    crawl_price,
    crawl_bargin,
    crawl_pe,
    crawl_monthly_report,  # 月營收爬蟲 (使用修復後的版本)
    crawl_monthly_revenue,
    crawl_finance_statement_by_date,
    crawl_benchmark,
    crawl_unified_financial_data,  # 統一財務資料爬蟲 (取代舊的分離版本)
    crawl_divide_ratio,  # 統一除權息爬蟲 (上市 + 上櫃)
    crawl_cap_reduction,  # 統一減資爬蟲 (上市 + 上櫃)
    crawl_balance_sheet,  # 資產負債表爬蟲
    # 核心函數
    table_date_range,
    update_table,
    date_range,
    month_range,
    season_range,
)


import datetime
from inspect import signature
import urllib3
import pandas as pd
import time
import random
import pickle

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 導入股票名稱映射功能
try:
    from stock_name_mapping import (
        get_stock_name,
        get_full_stock_id,
        get_cached_stock_info,
        update_stock_names_in_dataframe,
        get_builtin_stock_name
    )
    print("✅ 股票名稱映射模組載入成功")
    STOCK_NAME_MODULE_AVAILABLE = True
except ImportError:
    print("⚠️ 股票名稱映射模組未找到，將使用內建映射表")
    STOCK_NAME_MODULE_AVAILABLE = False

    # 內建股票名稱映射表
    BUILTIN_STOCK_NAMES = {
        '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
        '2412': '中華電', '2881': '富邦金', '2882': '國泰金',
        '2303': '聯電', '2308': '台達電', '2002': '中鋼',
        '1301': '台塑', '1303': '南亞', '2207': '和泰車',
        '2886': '兆豐金', '2891': '中信金', '2892': '第一金',
        '1104': '環泥', '1108': '幸福', '1109': '信大',
        '1203': '味王', '9958': '世紀鋼', '9962': '有益'
    }

    def get_stock_name(stock_id):
        return BUILTIN_STOCK_NAMES.get(str(stock_id).split()[0], f'股票{stock_id}')

    def get_full_stock_id(stock_id):
        stock_name = get_stock_name(stock_id)
        clean_stock_id = str(stock_id).split()[0]
        return f'{clean_stock_id} {stock_name}'

    def get_cached_stock_info():
        return {}

    def update_stock_names_in_dataframe(df, stock_id_column='stock_id'):
        return df

    def get_builtin_stock_name(stock_id):
        return get_stock_name(stock_id)



def is_trading_day(date):
    """檢查是否為交易日 (簡單版本 - 排除週末)"""
    return date.weekday() < 5  # 0-4 是週一到週五

def filter_trading_days(dates):
    """過濾出交易日"""
    return [date for date in dates if is_trading_day(date)]

def save_pe_data_only_db(df, table_name):
    """專門處理PE資料的保存函數 - 只保存DB格式"""
    if table_name != 'pe':
        return False

    # 使用指定的PE資料庫位置
    target_db_file = 'D:/Finlab/history/tables/pe_data.db'

    print(f"🎯 更新PE資料庫:")
    print(f"   DB: {target_db_file}")

    # 確保目標目錄存在
    target_dir = os.path.dirname(target_db_file)
    if not os.path.exists(target_dir):
        os.makedirs(target_dir, exist_ok=True)
        print(f"📁 創建目錄: {target_dir}")

    # 更新DB檔案
    if save_pe_to_database(df, target_db_file):
        print(f"✅ PE資料庫更新成功")
        print(f"   數據量: {len(df):,} 筆")
        return target_db_file
    else:
        print(f"❌ PE資料庫更新失敗")
        return False

def save_pe_to_database(df, db_path='pe_data.db'):
    """將PE數據保存到SQLite數據庫 - 增強版本，支援增量更新"""
    try:
        import sqlite3

        # 重置索引，將MultiIndex轉換為普通列
        df_reset = df.reset_index()

        # 確保日期列為字符串格式
        if 'date' in df_reset.columns:
            df_reset['date'] = df_reset['date'].dt.strftime('%Y-%m-%d')

        # 統一欄位名稱 - 將中文欄位名稱轉換為英文
        column_mapping = {
            '殖利率(%)': 'dividend_yield',
            '本益比': 'pe_ratio',
            '股利年度': 'dividend_year',
            '股價淨值比': 'pb_ratio'
        }

        # 重新命名欄位
        df_reset = df_reset.rename(columns=column_mapping)

        # 確保所有必要欄位都存在
        required_columns = ['stock_id', 'date', 'dividend_yield', 'pe_ratio', 'dividend_year', 'pb_ratio']
        for col in required_columns:
            if col not in df_reset.columns:
                df_reset[col] = None

        # 移除任何可能存在的 index 欄位或其他不需要的欄位
        unwanted_columns = ['index', 'level_0', 'level_1']
        for col in unwanted_columns:
            if col in df_reset.columns:
                df_reset = df_reset.drop(col, axis=1)
                print(f"🗑️ 已移除不需要的欄位: {col}")

        # 只保留必要的欄位，確保沒有額外的欄位
        final_columns = ['stock_id', 'stock_name', 'date', 'dividend_yield', 'pe_ratio', 'dividend_year', 'pb_ratio']

        # 添加 stock_name 欄位（如果不存在）
        if 'stock_name' not in df_reset.columns:
            # 從 stock_id 中提取股票名稱
            if 'stock_id' in df_reset.columns:
                def extract_stock_name(stock_id_str):
                    if not stock_id_str or pd.isna(stock_id_str):
                        return ''
                    stock_str = str(stock_id_str).strip()
                    if ' ' in stock_str:
                        parts = stock_str.split(' ', 1)
                        return parts[1] if len(parts) > 1 else ''
                    return ''

                def extract_stock_id(stock_id_str):
                    if not stock_id_str or pd.isna(stock_id_str):
                        return stock_id_str  # 保持原值
                    stock_str = str(stock_id_str).strip()
                    if ' ' in stock_str:
                        return stock_str.split(' ')[0]
                    return stock_str  # 如果沒有空格，保持原值

                # 先提取股票名稱
                df_reset['stock_name'] = df_reset['stock_id'].apply(extract_stock_name)

                # 再更新 stock_id 為純代碼
                df_reset['stock_id'] = df_reset['stock_id'].apply(extract_stock_id)
            else:
                df_reset['stock_name'] = ''

        # 只保留最終需要的欄位
        df_reset = df_reset[final_columns]

        # 檢查並清理空值
        print(f"📊 清理前 DataFrame 形狀: {df_reset.shape}")

        # 檢查關鍵欄位的空值情況
        null_stock_id = df_reset['stock_id'].isnull().sum()
        null_date = df_reset['date'].isnull().sum()
        empty_stock_id = (df_reset['stock_id'] == '').sum()

        print(f"📊 空值檢查:")
        print(f"   stock_id 為 NULL: {null_stock_id}")
        print(f"   stock_id 為空字串: {empty_stock_id}")
        print(f"   date 為 NULL: {null_date}")

        # 移除 stock_id 或 date 為空的記錄
        before_clean = len(df_reset)
        df_reset = df_reset.dropna(subset=['stock_id', 'date'])
        df_reset = df_reset[df_reset['stock_id'] != '']
        df_reset = df_reset[df_reset['date'] != '']
        after_clean = len(df_reset)

        if before_clean > after_clean:
            removed_count = before_clean - after_clean
            print(f"🗑️ 已移除 {removed_count} 筆無效記錄（stock_id 或 date 為空）")

        print(f"📊 最終 DataFrame 欄位: {list(df_reset.columns)}")
        print(f"📊 最終 DataFrame 形狀: {df_reset.shape}")

        # 如果清理後沒有資料，直接返回
        if df_reset.empty:
            print("⚠️ 清理後沒有有效資料，跳過保存")
            return True

        # 最終驗證：確保關鍵欄位沒有空值
        print(f"📊 最終驗證關鍵欄位...")
        critical_columns = ['stock_id', 'date']

        for col in critical_columns:
            null_count = df_reset[col].isnull().sum()
            empty_count = (df_reset[col] == '').sum() if df_reset[col].dtype == 'object' else 0

            if null_count > 0 or empty_count > 0:
                print(f"❌ 發現 {col} 欄位有 {null_count} 個 NULL 值和 {empty_count} 個空字串")
                print(f"🔍 顯示前5個問題記錄:")

                problem_mask = df_reset[col].isnull() | (df_reset[col] == '')
                problem_records = df_reset[problem_mask].head()
                for idx, row in problem_records.iterrows():
                    print(f"   [{idx}] {dict(row)}")

                print("❌ 由於關鍵欄位有空值，取消保存")
                conn.close()
                return False

        print(f"✅ 關鍵欄位驗證通過")

        # 創建數據庫連接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建表格（如果不存在）- 使用英文欄位名稱
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pe_data (
                stock_id TEXT,
                stock_name TEXT,
                date TEXT,
                dividend_yield REAL,
                pe_ratio REAL,
                dividend_year INTEGER,
                pb_ratio REAL,
                PRIMARY KEY (stock_id, date)
            )
        ''')

        # 處理股票代碼和名稱分離
        if 'stock_id' in df_reset.columns:
            def split_stock_info(stock_str):
                if not stock_str or pd.isna(stock_str):
                    return None, None
                stock_str = str(stock_str).strip()
                if ' ' in stock_str:
                    parts = stock_str.split(' ', 1)
                    return parts[0], parts[1] if len(parts) > 1 else ''
                else:
                    return stock_str, ''

            # 分離股票代碼和名稱
            split_result = df_reset['stock_id'].apply(lambda x: pd.Series(split_stock_info(x)))
            df_reset['stock_code'] = split_result[0]
            df_reset['stock_name'] = split_result[1]

            # 更新stock_id為純代碼
            df_reset['stock_id'] = df_reset['stock_code']
            df_reset = df_reset.drop('stock_code', axis=1)

        # 增量更新：先刪除相同日期的資料，再插入新資料
        if 'date' in df_reset.columns:
            dates_to_update = df_reset['date'].unique()
            for date in dates_to_update:
                cursor.execute('DELETE FROM pe_data WHERE date = ?', (date,))

        # 插入新資料
        df_reset.to_sql('pe_data', conn, if_exists='append', index=False)

        # 創建索引以提高查詢性能
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON pe_data(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON pe_data(stock_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON pe_data(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON pe_data(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_stock ON pe_data(date, stock_id)')
        except:
            pass  # 索引可能已存在

        conn.commit()

        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM pe_data')
        record_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM pe_data')
        unique_stocks = cursor.fetchone()[0]

        cursor.execute('SELECT MIN(date), MAX(date) FROM pe_data WHERE date IS NOT NULL')
        date_range_result = cursor.fetchone()
        min_date, max_date = date_range_result if date_range_result and date_range_result[0] else (None, None)

        conn.close()

        # 計算文件大小
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB

        print(f"💾 已同步更新PE數據庫: {db_path}")
        print(f"   記錄數: {record_count:,} 筆")
        print(f"   股票數: {unique_stocks:,} 檔")
        print(f"   日期範圍: {min_date} ~ {max_date}")
        print(f"   文件大小: {db_size:.1f} MB")
        print(f"   欄位結構: stock_id, stock_name, date, 殖利率(%), 本益比, 股利年度, 股價淨值比")

        return True

    except Exception as e:
        print(f"⚠️ PE數據庫更新失敗: {str(e)[:50]}...")
        import traceback
        traceback.print_exc()
        return False

def save_twse_divide_ratio_to_database(df, db_file):
    """將台股除權息資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('twse_divide_ratio', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 台股除權息資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")
        return True

    except Exception as e:
        print(f"❌ 保存台股除權息資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_divide_ratio_to_database(df, db_file):
    """將統一除權息資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        target_dir = os.path.dirname(db_file)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)

        # 重設索引以便儲存
        df_to_save = df.reset_index()

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('divide_ratio', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 統一除權息資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")

        # 顯示市場分布
        if 'market' in df_to_save.columns:
            market_stats = df_to_save['market'].value_counts()
            print(f"   市場分布:")
            for market, count in market_stats.items():
                market_name = '上市' if market == 'TWSE' else '上櫃'
                print(f"     {market_name} ({market}): {count:,} 筆")

        return True

    except Exception as e:
        print(f"❌ 保存統一除權息資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_cap_reduction_to_database(df, db_file):
    """將統一減資資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        target_dir = os.path.dirname(db_file)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)

        # 重設索引以便儲存
        df_to_save = df.reset_index()

        # 確保日期格式為純日期字串 (YYYY-MM-DD)
        if 'date' in df_to_save.columns:
            df_to_save['date'] = pd.to_datetime(df_to_save['date']).dt.strftime('%Y-%m-%d')

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('cap_reduction', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 統一減資資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")

        # 顯示市場分布
        if 'market' in df_to_save.columns:
            market_stats = df_to_save['market'].value_counts()
            print(f"   市場分布:")
            for market, count in market_stats.items():
                market_name = '上市' if market == 'TWSE' else '上櫃'
                print(f"     {market_name} ({market}): {count:,} 筆")

        return True

    except Exception as e:
        print(f"❌ 保存統一減資資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_financial_data_to_database(df, db_file):
    """將統一財務資料保存到SQLite資料庫，並調整欄位順序"""
    try:
        import sqlite3

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 調整欄位順序：stock_id 第1位，stock_name 第2位，report_date 第3位，date 第4位
        columns = list(df_to_save.columns)

        # 移除不需要的索引欄位
        unwanted_cols = ['level_0', 'index']
        for col in unwanted_cols:
            if col in columns:
                columns.remove(col)
                df_to_save = df_to_save.drop(col, axis=1)

        # 確保關鍵欄位存在
        required_cols = ['stock_id', 'stock_name', 'report_date', 'date']
        missing_cols = [col for col in required_cols if col not in columns]
        if missing_cols:
            print(f"⚠️ 缺少必要欄位: {missing_cols}")
            return False

        # 重新排列欄位順序
        # 1. stock_id (第1位)
        # 2. stock_name (第2位)
        # 3. report_date (第3位)
        # 4. date (第4位)
        # 5. 其他欄位

        # 移除關鍵欄位從原位置
        for col in required_cols:
            if col in columns:
                columns.remove(col)

        # 按正確順序重新插入
        new_columns = ['stock_id', 'stock_name', 'report_date', 'date'] + columns

        # 重新排列 DataFrame
        df_to_save = df_to_save[new_columns]
        print(f"✅ 已調整欄位順序: stock_id(1) → stock_name(2) → report_date(3) → date(4) → 其他欄位")

        # 確保日期格式為純日期字串
        if 'date' in df_to_save.columns:
            df_to_save['date'] = pd.to_datetime(df_to_save['date']).dt.strftime('%Y-%m-%d')
        if 'report_date' in df_to_save.columns:
            df_to_save['report_date'] = pd.to_datetime(df_to_save['report_date']).dt.strftime('%Y-%m-%d')

        # 清理資料類型，確保所有欄位都是 SQLite 支援的類型
        for col in df_to_save.columns:
            if df_to_save[col].dtype == 'object':
                # 將所有 object 類型轉換為字串，處理 tuple 等不支援的類型
                df_to_save[col] = df_to_save[col].astype(str)
                # 將 'nan' 字串替換為空字串
                df_to_save[col] = df_to_save[col].replace('nan', '')

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('financial_data', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 統一財務資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")

        # 檢查檔案大小
        file_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
        print(f"   檔案大小: {file_size:.1f} MB")

        return True

    except Exception as e:
        print(f"❌ 保存統一財務資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_otc_divide_ratio_to_database(df, db_file):
    """將櫃買除權息資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('otc_divide_ratio', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 櫃買除權息資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")
        return True

    except Exception as e:
        print(f"❌ 保存櫃買除權息資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False



def convert_price_pkl_to_db():
    """將 price.pkl 轉換為 newprice.db 格式"""
    print("=" * 80)
    print("🔄 開始轉換 price.pkl 到 newprice.db 格式")
    print("=" * 80)

    try:
        # 定義檔案路徑
        pkl_file = 'finlab_ml_course/history/tables/price.pkl'
        target_db_file = 'D:/Finlab/history/tables/newprice.db'

        # 檢查 pkl 檔案是否存在
        if not os.path.exists(pkl_file):
            print(f"❌ 找不到 pkl 檔案: {pkl_file}")
            return False

        print(f"📂 讀取 pkl 檔案: {pkl_file}")

        # 讀取 pkl 檔案
        df = pd.read_pickle(pkl_file)

        print(f"📊 資料概況:")
        print(f"   資料形狀: {df.shape}")
        print(f"   索引類型: {type(df.index)}")
        print(f"   索引名稱: {df.index.names}")
        print(f"   欄位名稱: {list(df.columns)}")

        # 檢查索引結構
        if hasattr(df.index, 'levels'):
            print(f"   索引層級數: {df.index.nlevels}")
            for i, level in enumerate(df.index.levels):
                print(f"   層級 {i}: {len(level)} 個唯一值")
                if i == 0:  # 通常是股票代碼
                    print(f"     範例: {list(level[:5])}")
                elif i == 1:  # 通常是日期
                    print(f"     日期範圍: {level.min()} ~ {level.max()}")

        # 顯示前幾筆資料
        print(f"\n📋 前5筆資料預覽:")
        print(df.head())

        # 檢查資料類型
        print(f"\n📊 資料類型:")
        print(df.dtypes)

        # 確保目標目錄存在
        target_dir = os.path.dirname(target_db_file)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)
            print(f"📁 創建目錄: {target_dir}")

        # 轉換為 DB 格式
        print(f"\n💾 轉換為 DB 格式: {target_db_file}")

        if save_price_to_database(df, target_db_file):
            print(f"✅ 轉換成功!")

            # 驗證轉換結果
            print(f"\n🔍 驗證轉換結果:")
            import sqlite3
            conn = sqlite3.connect(target_db_file)
            cursor = conn.cursor()

            # 檢查表格結構
            cursor.execute("PRAGMA table_info(price)")
            columns = cursor.fetchall()
            print(f"   DB 表格欄位: {[col[1] for col in columns]}")

            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM price")
            count = cursor.fetchone()[0]
            print(f"   DB 資料筆數: {count:,}")

            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
            unique_stocks = cursor.fetchone()[0]
            print(f"   不重複股票數: {unique_stocks:,}")

            # 檢查日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM price")
            date_range = cursor.fetchone()
            print(f"   日期範圍: {date_range[0]} ~ {date_range[1]}")

            # 檢查前幾筆資料
            cursor.execute("SELECT * FROM price LIMIT 3")
            sample_data = cursor.fetchall()
            print(f"   前3筆資料: {sample_data}")

            conn.close()

            # 檢查檔案大小
            db_size = os.path.getsize(target_db_file) / (1024 * 1024)  # MB
            print(f"   DB 檔案大小: {db_size:.2f} MB")

            return True
        else:
            print(f"❌ 轉換失敗")
            return False

    except Exception as e:
        print(f"❌ 轉換過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_price_to_database(df, db_file):
    """將股價資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 連接到資料庫
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        df_to_save.to_sql('price', conn, if_exists='replace', index=False)

        conn.close()
        print(f"✅ 股價資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")
        return True

    except Exception as e:
        print(f"❌ 保存股價資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def safe_convert_to_float(value, default=0.0):
    """安全轉換為浮點數"""
    if value is None or value == '' or value == '--' or str(value).strip() == '':
        return default
    try:
        return float(str(value).replace(',', ''))
    except (ValueError, TypeError):
        return default

def safe_convert_to_int(value, default=0):
    """安全轉換為整數"""
    if value is None or value == '' or value == '--' or str(value).strip() == '':
        return default
    try:
        return int(float(str(value).replace(',', '')))
    except (ValueError, TypeError):
        return default

def save_price_to_newprice_database(df, db_file):
    """將股價資料增量保存到 newprice.db (標準格式)"""
    conn = None
    try:
        import sqlite3

        if df.empty:
            print("⚠️ 資料為空，跳過保存")
            return True

        # 確保目錄存在
        db_dir = os.path.dirname(db_file)
        if db_dir:  # 只有當目錄路徑不為空時才創建
            os.makedirs(db_dir, exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 檢查資料結構
        print(f"📊 準備保存資料: {len(df_to_save)} 筆")
        print(f"   欄位: {list(df_to_save.columns)}")

        # 連接到資料庫
        conn = sqlite3.connect(db_file, timeout=30.0)  # 增加超時時間
        cursor = conn.cursor()

        # 檢查表格是否存在，如果不存在則創建
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                stock_name TEXT,
                listing_status TEXT,
                industry TEXT,
                Volume INTEGER,
                [Transaction] INTEGER,
                TradeValue INTEGER,
                [Open] REAL,
                High REAL,
                Low REAL,
                [Close] REAL,
                [Change] REAL,
                date TEXT
            )
        """)

        # 處理每一筆資料
        for _, row in df_to_save.iterrows():
            # 提取股票代碼和名稱
            if 'stock_id' in row.index:
                stock_id = str(row['stock_id'])
            else:
                # 如果沒有 stock_id，嘗試從索引獲取
                stock_id = str(row.name[0]) if hasattr(row, 'name') and isinstance(row.name, tuple) else 'Unknown'

            # 獲取日期
            if 'date' in row.index:
                date_str = str(row['date'])[:10]  # 只取年月日部分
            else:
                date_str = str(row.name[1])[:10] if hasattr(row, 'name') and isinstance(row.name, tuple) else 'Unknown'

            # 獲取股票名稱、上市狀態、產業別
            stock_name = str(row.get('stock_name', ''))
            listing_status = str(row.get('listing_status', ''))
            industry = str(row.get('industry', 'ETF'))

            # 檢查該股票該日期的資料是否已存在
            cursor.execute("""
                SELECT COUNT(*) FROM stock_daily_data
                WHERE stock_id = ? AND date = ?
            """, (stock_id, date_str))

            exists = cursor.fetchone()[0] > 0

            if exists:
                # 更新現有資料
                cursor.execute("""
                    UPDATE stock_daily_data SET
                        stock_name = ?, listing_status = ?, industry = ?,
                        Volume = ?, [Transaction] = ?, TradeValue = ?,
                        [Open] = ?, High = ?, Low = ?, [Close] = ?, [Change] = ?
                    WHERE stock_id = ? AND date = ?
                """, (
                    stock_name, listing_status, industry,
                    safe_convert_to_int(row.get('Volume', 0)),
                    safe_convert_to_int(row.get('Transaction', 0)),
                    safe_convert_to_int(row.get('TradeValue', 0)),
                    safe_convert_to_float(row.get('Open', 0)),
                    safe_convert_to_float(row.get('High', 0)),
                    safe_convert_to_float(row.get('Low', 0)),
                    safe_convert_to_float(row.get('Close', 0)),
                    safe_convert_to_float(row.get('Change', 0.0)),  # 使用爬取的漲跌價差
                    stock_id, date_str
                ))
            else:
                # 插入新資料
                cursor.execute("""
                    INSERT INTO stock_daily_data (
                        stock_id, stock_name, listing_status, industry,
                        Volume, [Transaction], TradeValue, [Open], High, Low, [Close], [Change], date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stock_id,
                    stock_name,  # 使用爬取的股票名稱
                    listing_status,  # 使用爬取的上市狀態
                    industry,  # 使用爬取的產業別
                    safe_convert_to_int(row.get('Volume', 0)),
                    safe_convert_to_int(row.get('Transaction', 0)),
                    safe_convert_to_int(row.get('TradeValue', 0)),
                    safe_convert_to_float(row.get('Open', 0)),
                    safe_convert_to_float(row.get('High', 0)),
                    safe_convert_to_float(row.get('Low', 0)),
                    safe_convert_to_float(row.get('Close', 0)),
                    safe_convert_to_float(row.get('Change', 0.0)),  # 使用爬取的漲跌價差
                    date_str
                ))

        # 提交事務
        conn.commit()

        print(f"✅ 股價資料已增量保存到: {db_file}")
        return True

    except Exception as e:
        print(f"❌ 保存股價資料到 newprice.db 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 確保連接被正確關閉
        if conn:
            try:
                conn.close()
            except:
                pass

def convert_price_pkl_to_db():
    """將 price.pkl 轉換為 newprice.db 格式"""
    print("=" * 80)
    print("🔄 開始轉換 price.pkl 到 newprice.db 格式")
    print("=" * 80)

    try:
        # 定義檔案路徑
        pkl_file = 'D:/Finlab/history/tables/price.pkl'
        target_db_file = 'D:/Finlab/history/tables/newprice.db'

        # 檢查 pkl 檔案是否存在
        if not os.path.exists(pkl_file):
            print(f"❌ 找不到 pkl 檔案: {pkl_file}")
            return False

        print(f"📂 讀取 pkl 檔案: {pkl_file}")

        # 檢查檔案大小
        file_size = os.path.getsize(pkl_file) / (1024 * 1024)  # MB
        print(f"📊 PKL 檔案大小: {file_size:.2f} MB")

        # 讀取 pkl 檔案
        print("⏳ 正在讀取大型 pkl 檔案，請稍候...")
        df = pd.read_pickle(pkl_file)

        print(f"📊 資料概況:")
        print(f"   資料形狀: {df.shape}")
        print(f"   索引類型: {type(df.index)}")
        print(f"   索引名稱: {df.index.names}")
        print(f"   欄位名稱: {list(df.columns)}")

        # 檢查索引結構
        if hasattr(df.index, 'levels'):
            print(f"   索引層級數: {df.index.nlevels}")
            for i, level in enumerate(df.index.levels):
                print(f"   層級 {i}: {len(level)} 個唯一值")
                if i == 0:  # 通常是股票代碼
                    print(f"     股票代碼範例: {list(level[:5])}")
                elif i == 1:  # 通常是日期
                    print(f"     日期範圍: {level.min()} ~ {level.max()}")

        # 顯示前幾筆資料
        print(f"\n📋 前5筆資料預覽:")
        print(df.head())

        # 檢查資料類型
        print(f"\n📊 資料類型:")
        print(df.dtypes)

        # 確保目標目錄存在
        target_dir = os.path.dirname(target_db_file)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir, exist_ok=True)
            print(f"📁 創建目錄: {target_dir}")

        # 轉換為 DB 格式
        print(f"\n💾 轉換為 DB 格式: {target_db_file}")
        print("⏳ 正在轉換大型資料，請稍候...")

        if save_price_to_database(df, target_db_file):
            print(f"✅ 轉換成功!")

            # 驗證轉換結果
            print(f"\n🔍 驗證轉換結果:")
            import sqlite3
            conn = sqlite3.connect(target_db_file)
            cursor = conn.cursor()

            # 檢查表格結構
            cursor.execute("PRAGMA table_info(price)")
            columns = cursor.fetchall()
            print(f"   DB 表格欄位: {[col[1] for col in columns]}")

            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM price")
            count = cursor.fetchone()[0]
            print(f"   DB 資料筆數: {count:,}")

            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
            unique_stocks = cursor.fetchone()[0]
            print(f"   不重複股票數: {unique_stocks:,}")

            # 檢查日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM price")
            date_range = cursor.fetchone()
            print(f"   日期範圍: {date_range[0]} ~ {date_range[1]}")

            # 檢查前幾筆資料
            cursor.execute("SELECT stock_id, date, open, high, low, close, volume FROM price LIMIT 5")
            sample_data = cursor.fetchall()
            print(f"   前5筆資料:")
            for row in sample_data:
                print(f"     {row}")

            conn.close()

            # 檢查檔案大小
            db_size = os.path.getsize(target_db_file) / (1024 * 1024)  # MB
            print(f"   DB 檔案大小: {db_size:.2f} MB")

            return True
        else:
            print(f"❌ 轉換失敗")
            return False

    except Exception as e:
        print(f"❌ 轉換過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_price_to_database(df, db_file):
    """將股價資料保存到SQLite資料庫"""
    try:
        import sqlite3

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        print("⏳ 正在重置索引...")
        df_to_save = df.reset_index()

        # 連接到資料庫
        print("⏳ 正在連接資料庫...")
        conn = sqlite3.connect(db_file)

        # 保存資料，如果表格已存在則替換
        print("⏳ 正在保存資料到資料庫...")
        df_to_save.to_sql('price', conn, if_exists='replace', index=False, chunksize=10000)

        conn.close()
        print(f"✅ 股價資料已保存到資料庫: {db_file}")
        print(f"   資料筆數: {len(df):,}")
        return True

    except Exception as e:
        print(f"❌ 保存股價資料到資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False







def save_batch_to_database(df, db_path, is_first_batch=False):
    """批次保存資料到資料庫（正確的欄位結構）"""
    try:
        import sqlite3

        # 重置索引
        df_reset = df.reset_index()

        # 處理 MultiIndex
        if 'stock_id' in df_reset.columns:
            # 將 stock_id 拆分為 stock_id 和 stock_name
            df_reset[['stock_id', 'stock_name']] = df_reset['stock_id'].apply(
                lambda x: pd.Series(split_stock_id(x))
            )
            # 移除原始的 stock_id 欄位
            df_reset = df_reset.drop('stock_id', axis=1)

        # 確保日期格式正確
        if 'date' in df_reset.columns:
            if df_reset['date'].dtype != 'object':
                df_reset['date'] = df_reset['date'].dt.strftime('%Y-%m-%d')

        # 重新排列欄位順序
        cols = ['stock_id', 'stock_name', 'date'] + [col for col in df_reset.columns if col not in ['stock_id', 'stock_name', 'date']]
        df_reset = df_reset[cols]

        # 創建資料庫連接
        conn = sqlite3.connect(db_path)

        # 寫入資料庫
        if_exists_mode = 'replace' if is_first_batch else 'append'
        df_reset.to_sql('monthly_report', conn, if_exists=if_exists_mode, index=False)

        # 創建索引 (只在第一次創建時)
        if is_first_batch:
            cursor = conn.cursor()
            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON monthly_report(stock_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON monthly_report(stock_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON monthly_report(date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON monthly_report(stock_id, date)')
            except:
                pass

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ 批次保存失敗: {e}")
        return False

def apply_stock_name_consistency_fix(db_path):
    """應用股票名稱一致性修正"""
    try:
        import sqlite3

        print("🔧 開始股票名稱一致性修正...")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 統一的股票名稱對照表（使用簡化名稱格式）
        correct_stock_names = {
            '1104': '環泥', '1108': '幸福', '1109': '信大',
            '1203': '味王', '9958': '世紀鋼', '9962': '有益',
            '9945': '潤泰新', '9950': '萬國通', '9951': '皇田'
        }

        total_updated = 0

        # 檢查資料庫結構
        cursor.execute("PRAGMA table_info(monthly_report)")
        columns = [row[1] for row in cursor.fetchall()]

        if 'stock_id' in columns and 'stock_name' in columns:
            # 新的欄位結構：使用 stock_id 和 stock_name
            for stock_id, correct_name in correct_stock_names.items():
                # 查找該股票代碼的不同名稱變體
                cursor.execute('''
                    SELECT DISTINCT stock_name, COUNT(*) as count
                    FROM monthly_report
                    WHERE stock_id = ? AND stock_name != ?
                    GROUP BY stock_name
                ''', (stock_id, correct_name))

                variants = cursor.fetchall()

                for variant_name, count in variants:
                    if variant_name and variant_name != correct_name:
                        print(f"  修正 '{stock_id} {variant_name}' -> '{stock_id} {correct_name}' ({count} 筆記錄)")

                        # 更新記錄
                        cursor.execute('''
                            UPDATE monthly_report
                            SET stock_name = ?
                            WHERE stock_id = ? AND stock_name = ?
                        ''', (correct_name, stock_id, variant_name))

                        updated_rows = cursor.rowcount
                        total_updated += updated_rows
        else:
            # 舊的欄位結構：使用 stock_id
            for stock_id, correct_name in correct_stock_names.items():
                # 查找所有該股票代碼的變體
                cursor.execute('''
                    SELECT DISTINCT stock_id, COUNT(*) as count
                    FROM monthly_report
                    WHERE stock_id LIKE ? AND stock_id != ?
                    GROUP BY stock_id
                ''', (f'{stock_id}%', f'{stock_id} {correct_name}'))

                variants = cursor.fetchall()

                for variant_name, count in variants:
                    if len(variant_name) > 4:  # 排除純數字代碼
                        target_name = f'{stock_id} {correct_name}'
                        print(f"  修正 '{variant_name}' -> '{target_name}' ({count} 筆記錄)")

                        # 更新記錄
                        cursor.execute('''
                            UPDATE monthly_report
                            SET stock_id = ?
                            WHERE stock_id = ?
                        ''', (target_name, variant_name))

                        updated_rows = cursor.rowcount
                        total_updated += updated_rows

        conn.commit()
        conn.close()

        if total_updated > 0:
            print(f"✅ 股票名稱修正完成，共更新 {total_updated} 筆記錄")
        else:
            print("✅ 股票名稱已統一，無需修正")

        return True

    except Exception as e:
        print(f"⚠️ 股票名稱修正失敗: {e}")
        return False

def split_stock_id(stock_id_str):
    """將 stock_id 拆分為股票代碼和股票名稱"""
    if not stock_id_str or pd.isna(stock_id_str):
        return None, None

    stock_str = str(stock_id_str).strip()

    # 如果包含空格，按空格分割
    if ' ' in stock_str:
        parts = stock_str.split(' ', 1)
        stock_id = parts[0]
        stock_name = parts[1] if len(parts) > 1 else ''
    else:
        # 如果只有代碼，名稱為空
        stock_id = stock_str
        stock_name = ''

    return stock_id, stock_name

def save_monthly_report_to_database(df, db_path='monthly_report.db'):
    """將月營收數據保存到SQLite數據庫（正確的欄位結構）"""
    try:
        import sqlite3

        # 重置索引，將MultiIndex轉換為普通列
        df_reset = df.reset_index()

        # 處理 MultiIndex 重置後的 tuple 問題
        if 'index' in df_reset.columns and len(df_reset) > 0 and isinstance(df_reset['index'].iloc[0], tuple):
            # 拆分 tuple 為 stock_id 和 date
            df_reset['stock_id'] = df_reset['index'].apply(lambda x: x[0] if isinstance(x, tuple) else str(x))
            df_reset['date'] = df_reset['index'].apply(lambda x: x[1] if isinstance(x, tuple) and len(x) > 1 else None)

            # 移除原始的 index 欄位
            df_reset = df_reset.drop('index', axis=1)

        # 處理其他索引列名問題
        elif df_reset.columns[0] == 0:  # 如果第一列是數字索引
            # 重新命名列
            new_columns = ['stock_id', 'date'] + list(df_reset.columns[2:])
            df_reset.columns = new_columns

        # 確保有 stock_id 欄位
        if 'stock_id' not in df_reset.columns:
            print("⚠️ 找不到 stock_id 欄位")
            return False

        # 將 stock_id 拆分為 stock_id 和 stock_name
        df_reset[['stock_id_new', 'stock_name']] = df_reset['stock_id'].apply(
            lambda x: pd.Series(split_stock_id(x))
        )

        # 移除原始的混合 stock_id 欄位，使用新的純股票代碼
        df_reset = df_reset.drop('stock_id', axis=1)
        df_reset = df_reset.rename(columns={'stock_id_new': 'stock_id'})

        # 動態更新股票名稱
        if STOCK_NAME_MODULE_AVAILABLE:
            try:
                # 使用動態股票名稱映射更新 stock_name
                stock_info = get_cached_stock_info()
                if stock_info:
                    # 保留原有名稱作為備用
                    original_names = df_reset['stock_name'].copy()

                    # 更新為最新的股票名稱
                    df_reset['stock_name'] = df_reset['stock_id'].apply(
                        lambda x: stock_info.get(str(x), {}).get('stock_name',
                                original_names[df_reset['stock_id']==x].iloc[0] if len(original_names[df_reset['stock_id']==x]) > 0 else f'股票{x}')
                    )
                    print("✅ 已動態更新月營收股票名稱")
            except Exception as e:
                print(f"⚠️ 動態更新月營收股票名稱失敗: {e}")

        # 確保日期列為字符串格式
        if 'date' in df_reset.columns:
            if df_reset['date'].dtype == 'object':
                df_reset['date'] = df_reset['date'].astype(str)
            else:
                df_reset['date'] = df_reset['date'].dt.strftime('%Y-%m-%d')

        # 重新排列欄位順序
        cols = ['stock_id', 'stock_name', 'date'] + [col for col in df_reset.columns if col not in ['stock_id', 'stock_name', 'date']]
        df_reset = df_reset[cols]

        # 創建數據庫連接
        conn = sqlite3.connect(db_path)

        # 寫入數據庫（替換現有數據）
        df_reset.to_sql('monthly_report', conn, if_exists='replace', index=False)

        # 創建索引以提高查詢性能
        cursor = conn.cursor()
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON monthly_report(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON monthly_report(stock_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON monthly_report(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON monthly_report(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_stock ON monthly_report(date, stock_id)')
        except:
            pass  # 索引可能已存在

        conn.commit()

        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM monthly_report')
        record_count = cursor.fetchone()[0]

        conn.close()

        # 計算文件大小
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB

        print(f"💾 已同步更新月營收數據庫: {db_path}")
        print(f"   記錄數: {record_count:,} 筆")
        print(f"   文件大小: {db_size:.1f} MB")
        print(f"   欄位結構: stock_id, stock_name, date, 當月營收, 上月營收...")

        return True

    except Exception as e:
        print(f"⚠️ 月營收數據庫更新失敗: {str(e)[:50]}...")
        return False

def save_bargin_report_to_database(df, db_path='bargin_report.db'):
    """將 bargin_report 數據保存到SQLite數據庫"""
    try:
        import sqlite3

        # 檢查原始索引結構
        index_columns = df.index.names

        # 重置索引，將MultiIndex轉換為普通列
        df_reset = df.reset_index()

        # 檢查索引欄位名稱並重新命名
        if len(index_columns) >= 2:
            # 第一個索引通常是股票相關，第二個是日期相關
            first_col = df_reset.columns[0]  # 第一個重置的索引欄位
            second_col = df_reset.columns[1]  # 第二個重置的索引欄位

            # 重新命名為標準格式
            df_reset = df_reset.rename(columns={
                first_col: 'stock_id_raw',
                second_col: 'date'
            })

        # 處理 stock_id，分離代碼和名稱
        if 'stock_id_raw' in df_reset.columns:
            def split_stock_info(stock_str):
                if not stock_str or pd.isna(stock_str):
                    return None, None

                stock_str = str(stock_str).strip()

                # 如果包含空格，按空格分割（格式：'代碼 名稱'）
                if ' ' in stock_str:
                    parts = stock_str.split(' ', 1)
                    stock_code = parts[0]
                    stock_name = parts[1] if len(parts) > 1 else ''
                else:
                    # 如果只有代碼，名稱為空
                    stock_code = stock_str
                    stock_name = ''

                return stock_code, stock_name

            # 分離股票代碼和名稱
            split_result = df_reset['stock_id_raw'].apply(lambda x: pd.Series(split_stock_info(x)))
            df_reset['stock_id'] = split_result[0]  # 股票代碼
            df_reset['stock_name'] = split_result[1]  # 股票名稱

            # 移除原始欄位
            df_reset = df_reset.drop('stock_id_raw', axis=1)

        # 確保日期格式正確
        if 'date' in df_reset.columns:
            df_reset['date'] = pd.to_datetime(df_reset['date']).dt.strftime('%Y-%m-%d')

        # 重新排列欄位順序（確保欄位存在）
        available_cols = list(df_reset.columns)
        ordered_cols = []

        # 先添加主要欄位（如果存在）
        for col in ['stock_id', 'stock_name', 'date']:
            if col in available_cols:
                ordered_cols.append(col)

        # 再添加其他欄位
        for col in available_cols:
            if col not in ordered_cols:
                ordered_cols.append(col)

        df_reset = df_reset[ordered_cols]

        # 創建數據庫連接
        conn = sqlite3.connect(db_path)

        # 創建表格（如果不存在）
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bargin_report (
                stock_id TEXT,
                stock_name TEXT,
                date TEXT,
                "外陸資買進股數(不含外資自營商)" TEXT,
                "外陸資賣出股數(不含外資自營商)" TEXT,
                "外陸資買賣超股數(不含外資自營商)" TEXT,
                "外資自營商買進股數" TEXT,
                "外資自營商賣出股數" TEXT,
                "外資自營商買賣超股數" TEXT,
                "投信買進股數" TEXT,
                "投信賣出股數" TEXT,
                "投信買賣超股數" TEXT,
                "自營商買進股數(自行買賣)" TEXT,
                "自營商賣出股數(自行買賣)" TEXT,
                "自營商買賣超股數(自行買賣)" TEXT,
                "自營商買進股數(避險)" TEXT,
                "自營商賣出股數(避險)" TEXT,
                "自營商買賣超股數(避險)" TEXT
            )
        ''')

        # 增量更新：先刪除相同日期的資料，再插入新資料
        dates_to_update = df_reset['date'].unique()
        for date in dates_to_update:
            cursor.execute('DELETE FROM bargin_report WHERE date = ?', (date,))

        # 插入新資料
        df_reset.to_sql('bargin_report', conn, if_exists='append', index=False)

        # 創建索引以提高查詢性能
        cursor = conn.cursor()
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON bargin_report(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON bargin_report(stock_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON bargin_report(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON bargin_report(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_stock ON bargin_report(date, stock_id)')
        except:
            pass  # 索引可能已存在

        conn.commit()

        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM bargin_report')
        record_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM bargin_report')
        unique_stocks = cursor.fetchone()[0]

        conn.close()

        # 計算文件大小
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB

        print(f"💾 已同步更新三大法人數據庫: {db_path}")
        print(f"   記錄數: {record_count:,} 筆")
        print(f"   股票數: {unique_stocks:,} 檔")
        print(f"   文件大小: {db_size:.1f} MB")
        print(f"   欄位結構: stock_id, stock_name, date, 外陸資買進股數...")

        return True

    except Exception as e:
        print(f"⚠️ 三大法人數據庫更新失敗: {str(e)[:50]}...")
        return False

def save_bargin_report_to_database_incremental(df, db_path='bargin_report.db'):
    """將 bargin_report 數據增量保存到SQLite數據庫"""
    try:
        import sqlite3

        # 重置索引，將MultiIndex轉換為普通列
        df_reset = df.reset_index()

        # 檢查索引欄位名稱並重新命名
        if len(df_reset.columns) >= 2:
            first_col = df_reset.columns[0]  # 第一個重置的索引欄位
            second_col = df_reset.columns[1]  # 第二個重置的索引欄位

            # 重新命名為標準格式
            df_reset = df_reset.rename(columns={
                first_col: 'stock_id_raw',
                second_col: 'date'
            })

        # 處理 stock_id，分離代碼和名稱
        if 'stock_id_raw' in df_reset.columns:
            def split_stock_info(stock_str):
                if not stock_str or pd.isna(stock_str):
                    return None, None

                stock_str = str(stock_str).strip()

                # 如果包含空格，按空格分割（格式：'代碼 名稱'）
                if ' ' in stock_str:
                    parts = stock_str.split(' ', 1)
                    stock_code = parts[0]
                    stock_name = parts[1] if len(parts) > 1 else ''
                else:
                    # 如果只有代碼，名稱為空
                    stock_code = stock_str
                    stock_name = ''

                return stock_code, stock_name

            # 分離股票代碼和名稱
            split_result = df_reset['stock_id_raw'].apply(lambda x: pd.Series(split_stock_info(x)))
            df_reset['stock_id'] = split_result[0]  # 股票代碼
            df_reset['stock_name'] = split_result[1]  # 股票名稱

            # 移除原始欄位
            df_reset = df_reset.drop('stock_id_raw', axis=1)

        # 確保日期格式正確
        if 'date' in df_reset.columns:
            df_reset['date'] = pd.to_datetime(df_reset['date']).dt.strftime('%Y-%m-%d')

        # 重新排列欄位順序
        cols = ['stock_id', 'stock_name', 'date'] + [col for col in df_reset.columns if col not in ['stock_id', 'stock_name', 'date']]
        df_reset = df_reset[cols]

        # 創建數據庫連接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 創建表格（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bargin_report (
                stock_id TEXT,
                stock_name TEXT,
                date TEXT,
                "外陸資買進股數(不含外資自營商)" TEXT,
                "外陸資賣出股數(不含外資自營商)" TEXT,
                "外陸資買賣超股數(不含外資自營商)" TEXT,
                "外資自營商買進股數" TEXT,
                "外資自營商賣出股數" TEXT,
                "外資自營商買賣超股數" TEXT,
                "投信買進股數" TEXT,
                "投信賣出股數" TEXT,
                "投信買賣超股數" TEXT,
                "自營商買進股數(自行買賣)" TEXT,
                "自營商賣出股數(自行買賣)" TEXT,
                "自營商買賣超股數(自行買賣)" TEXT,
                "自營商買進股數(避險)" TEXT,
                "自營商賣出股數(避險)" TEXT,
                "自營商買賣超股數(避險)" TEXT
            )
        ''')

        # 增量更新：先刪除相同日期的資料，再插入新資料
        dates_to_update = df_reset['date'].unique()
        for date in dates_to_update:
            cursor.execute('DELETE FROM bargin_report WHERE date = ?', (date,))

        # 插入新資料
        df_reset.to_sql('bargin_report', conn, if_exists='append', index=False)

        # 創建索引以提高查詢性能
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON bargin_report(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_name ON bargin_report(stock_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON bargin_report(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON bargin_report(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_stock ON bargin_report(date, stock_id)')
        except:
            pass  # 索引可能已存在

        conn.commit()

        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM bargin_report')
        record_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM bargin_report')
        unique_stocks = cursor.fetchone()[0]

        cursor.execute('SELECT MIN(date), MAX(date) FROM bargin_report WHERE date IS NOT NULL')
        date_range_result = cursor.fetchone()
        min_date, max_date = date_range_result if date_range_result and date_range_result[0] else (None, None)

        conn.close()

        # 計算文件大小
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB

        print(f"💾 已增量更新三大法人數據庫: {db_path}")
        print(f"   新增日期: {list(dates_to_update)}")
        print(f"   總記錄數: {record_count:,} 筆")
        print(f"   股票數: {unique_stocks:,} 檔")
        print(f"   日期範圍: {min_date} ~ {max_date}")
        print(f"   文件大小: {db_size:.1f} MB")

        return True

    except Exception as e:
        print(f"⚠️ 三大法人數據庫增量更新失敗: {str(e)[:50]}...")
        return False

def save_benchmark_to_database(df, db_path='benchmark.db'):
    """將 benchmark 數據保存到SQLite數據庫 - 優化版"""
    try:
        import sqlite3

        if df.empty:
            print("⚠️ benchmark 資料為空")
            return False

        # 檢查MultiIndex結構
        if not hasattr(df.index, 'names') or len(df.index.names) != 2 or df.index.names[0] is None:
            print(f"⚠️ benchmark 索引結構不正確: {df.index.names}")
            print(f"   嘗試修復索引結構...")

            # 嘗試修復索引結構
            if 'stock_id' in df.columns and 'date' in df.columns:
                df = df.set_index(['stock_id', 'date'])
                print(f"   ✅ 索引結構已修復")
            else:
                print(f"   ❌ 無法修復索引結構，缺少必要欄位")
                return False

        # 重置索引，將MultiIndex轉換為普通列
        df_reset = df.reset_index()

        # 檢查必要欄位
        if 'stock_id' not in df_reset.columns or 'date' not in df_reset.columns:
            print(f"⚠️ benchmark 缺少必要欄位: {df_reset.columns.tolist()}")
            return False

        # 處理日期格式 - 保持完整的時間戳格式
        df_reset['date'] = pd.to_datetime(df_reset['date']).dt.strftime('%Y-%m-%d %H:%M:%S')

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 創建數據庫連接 - 優化設定
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()

        # 優化SQLite設定
        cursor.execute('PRAGMA journal_mode=WAL')  # 使用WAL模式提高並發性能
        cursor.execute('PRAGMA synchronous=NORMAL')  # 平衡安全性和性能
        cursor.execute('PRAGMA cache_size=10000')  # 增加緩存
        cursor.execute('PRAGMA temp_store=memory')  # 使用內存存儲臨時數據

        # 動態創建表格結構
        columns_info = []
        for col in df_reset.columns:
            if col in ['stock_id', 'date']:
                columns_info.append(f'"{col}" TEXT')
            else:
                columns_info.append(f'"{col}" REAL')

        create_table_sql = f'''
            CREATE TABLE IF NOT EXISTS benchmark (
                {', '.join(columns_info)}
            )
        '''
        cursor.execute(create_table_sql)

        # 檢查並添加新欄位（動態表格結構更新）
        cursor.execute("PRAGMA table_info(benchmark)")
        existing_columns = {col[1] for col in cursor.fetchall()}
        new_columns = set(df_reset.columns) - existing_columns

        if new_columns:
            print(f"   🔧 發現新欄位，正在添加: {list(new_columns)}")
            for col in new_columns:
                if col not in ['stock_id', 'date']:
                    try:
                        cursor.execute(f'ALTER TABLE benchmark ADD COLUMN "{col}" REAL')
                        print(f"   ✅ 已添加欄位: {col}")
                    except Exception as e:
                        print(f"   ⚠️ 添加欄位失敗 {col}: {e}")

        # 優化：使用事務批次處理
        cursor.execute('BEGIN TRANSACTION')

        try:
            # 增量更新：先刪除相同日期的資料（優化版）
            dates_to_update = df_reset['date'].unique()
            if len(dates_to_update) == 1:
                # 單日期優化
                cursor.execute('DELETE FROM benchmark WHERE date LIKE ?', (dates_to_update[0][:10] + '%',))
            else:
                # 多日期批次刪除
                date_list = "','".join([d[:10] for d in dates_to_update])
                cursor.execute(f"DELETE FROM benchmark WHERE substr(date, 1, 10) IN ('{date_list}')")

            # 批次插入新資料 - 使用executemany提高效率
            columns = list(df_reset.columns)
            placeholders = ','.join(['?' for _ in columns])
            insert_sql = f'INSERT INTO benchmark ({",".join([f'"{col}"' for col in columns])}) VALUES ({placeholders})'

            # 轉換為元組列表以提高插入效率
            data_tuples = [tuple(row) for row in df_reset.values]
            cursor.executemany(insert_sql, data_tuples)

            # 提交事務
            cursor.execute('COMMIT')

            # 簡化統計資訊（避免慢查詢）
            cursor.execute('SELECT COUNT(*) FROM benchmark WHERE substr(date, 1, 10) = ?', (dates_to_update[0][:10],))
            new_records = cursor.fetchone()[0]

            print(f"💾 benchmark數據庫更新成功")
            print(f"   新增記錄: {new_records:,} 筆")
            print(f"   新增日期: {len(dates_to_update)} 天")

            conn.close()
            return True

        except Exception as e:
            # 回滾事務
            cursor.execute('ROLLBACK')
            raise e

    except Exception as e:
        print(f"⚠️ benchmark數據庫更新失敗: {str(e)[:50]}...")
        import traceback
        traceback.print_exc()
        return False

def safe_to_pickle(df, table_name):
    """安全的保存函數，直接更新目標檔案而不創建備份"""

    try:
        # 確保目錄存在
        if not os.path.exists('data'):
            os.makedirs('data')
        if not os.path.exists('history'):
            os.makedirs('history')

        # PE資料特殊處理 - 只更新到指定的DB位置
        if table_name == 'pe':
            # 使用指定的PE資料庫位置
            target_db_file = 'D:/Finlab/history/tables/pe_data.db'

            print(f"🎯 更新PE資料庫:")
            print(f"   DB: {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 更新DB檔案


            if save_pe_to_database(df, target_db_file):
                print(f"✅ PE資料庫更新成功")
                print(f"   數據量: {len(df):,} 筆")
            else:
                print(f"❌ PE資料庫更新失敗")

            filename = target_db_file
        # 月營收資料特殊處理 - 只保存DB檔案
        elif table_name == 'monthly_report':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/monthly_report.db'

            print(f"🎯 更新月營收資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 只保存到資料庫
            save_monthly_report_to_database(df, target_db_file)

            filename = target_db_file
        # 三大法人資料特殊處理 - 只更新DB檔案，不處理PKL
        elif table_name == 'bargin_report':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/bargin_report.db'

            print(f"🎯 更新三大法人資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 直接更新DB檔案（增量更新）
            save_bargin_report_to_database(df, target_db_file)

            filename = target_db_file
        # benchmark資料特殊處理 - 只更新DB檔案，不處理PKL
        elif table_name == 'benchmark':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/benchmark.db'

            print(f"🎯 更新benchmark資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 直接更新DB檔案（增量更新）
            save_benchmark_to_database(df, target_db_file)

            filename = target_db_file
        # 統一除權息資料特殊處理 - 只更新DB檔案
        elif table_name == 'divide_ratio':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/divide_ratio.db'

            print(f"🎯 更新統一除權息資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 直接更新DB檔案
            save_divide_ratio_to_database(df, target_db_file)

            filename = target_db_file
        # 統一減資資料特殊處理 - 只更新DB檔案
        elif table_name == 'cap_reduction':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/cap_reduction.db'

            print(f"🎯 更新統一減資資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 直接更新DB檔案
            save_cap_reduction_to_database(df, target_db_file)

            filename = target_db_file
        # 統一財務資料特殊處理 - 只更新DB檔案
        elif table_name == 'financial_data':
            # 使用標準Finlab位置
            target_db_file = 'D:/Finlab/history/tables/financial_data.db'

            print(f"🎯 更新統一財務資料到標準Finlab位置:")
            print(f"   DB:  {target_db_file}")

            # 確保目標目錄存在
            target_dir = os.path.dirname(target_db_file)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir, exist_ok=True)
                print(f"📁 創建目錄: {target_dir}")

            # 直接更新DB檔案
            save_financial_data_to_database(df, target_db_file)

            filename = target_db_file
        else:
            # 其他資料表使用標準邏輯
            filename = f'data/{table_name}.pkl'
            df.to_pickle(filename)
            print(f"💾 已保存資料: {filename} ({len(df)} 筆)")

        # 更新日期範圍記錄
        date_range_file = 'history/date_range.pickle'

        try:
            if os.path.exists(date_range_file):
                dates_record = pickle.load(open(date_range_file, 'rb'))
            else:
                dates_record = {}

            # 嘗試從資料中提取日期範圍
            if 'date' in df.columns:
                try:
                    # 確保日期欄位是 datetime 類型
                    df['date'] = pd.to_datetime(df['date'])
                    min_date = df['date'].min()
                    max_date = df['date'].max()
                    dates_record[table_name] = (min_date, max_date)
                    print(f"📅 更新日期範圍: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}")
                except Exception as date_error:
                    print(f"⚠️ 日期欄位處理失敗: {date_error}")
                    # 使用當前日期作為備用
                    today = datetime.datetime.now()
                    dates_record[table_name] = (today - datetime.timedelta(days=30), today)
                    print(f"📅 使用預設日期範圍")
            elif hasattr(df.index, 'levels') and len(df.index.levels) > 1:
                # MultiIndex 情況 - PE資料格式
                date_values = df.index.get_level_values('date')
                min_date = date_values.min()
                max_date = date_values.max()
                dates_record[table_name] = (min_date, max_date)
                print(f"📅 更新日期範圍: {min_date.strftime('%Y-%m-%d')} 至 {max_date.strftime('%Y-%m-%d')}")
            else:
                # 使用當前日期作為備用
                today = datetime.datetime.now()
                dates_record[table_name] = (today - datetime.timedelta(days=30), today)
                print(f"📅 使用預設日期範圍")

            pickle.dump(dates_record, open(date_range_file, 'wb'))

        except Exception as e:
            print(f"⚠️ 日期範圍記錄更新失敗: {e}")

        return True

    except Exception as e:
        print(f"❌ 保存失敗: {e}")
        return False

def get_pe_date_range_direct():
    """直接從PE檔案讀取日期範圍，避免table_date_range的兼容性問題"""
    try:
        # 檢查多個可能的PE檔案位置，優先標準Finlab位置
        pe_paths = [
            'D:/Finlab/history/tables/pe.pkl',  # 標準Finlab位置（優先）
            'data/pe.pkl',  # data目錄
            'pe.pkl',  # 當前目錄
            'history/tables/pe.pkl'  # 相對路徑
        ]

        for path in pe_paths:
            if os.path.exists(path):
                try:
                    print(f"🔍 檢查PE檔案: {path}")
                    df = pd.read_pickle(path)

                    # 嘗試從MultiIndex中獲取日期
                    if hasattr(df.index, 'levels') and 'date' in df.index.names:
                        dates = df.index.get_level_values('date')
                        first_date = dates.min()
                        last_date = dates.max()

                        # 確保返回datetime類型
                        if hasattr(first_date, 'to_pydatetime'):
                            first_date = first_date.to_pydatetime()
                        if hasattr(last_date, 'to_pydatetime'):
                            last_date = last_date.to_pydatetime()

                        print(f"✅ 成功讀取PE日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                        return first_date, last_date
                    else:
                        print(f"⚠️ PE檔案索引結構不符合預期: {df.index.names}")
                        print(f"   檔案列名: {list(df.columns)}")
                        print(f"   這可能不是標準的finlab PE檔案格式")
                        print(f"   將備份現有檔案並重新爬取標準格式")

                        # 備份非標準格式的檔案
                        backup_path = path + f".backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        try:
                            import shutil
                            shutil.copy2(path, backup_path)
                            print(f"   已備份原檔案到: {backup_path}")
                        except Exception as backup_error:
                            print(f"   備份失敗: {backup_error}")

                        # 返回None讓系統重新爬取標準格式
                        return None, None

                except Exception as e:
                    print(f"⚠️ 讀取PE檔案失敗 {path}: {str(e)[:50]}...")
                    continue

        print("❌ 未找到有效的PE檔案")
        return None, None

    except Exception as e:
        print(f"❌ 獲取PE日期範圍失敗: {e}")
        return None, None



def auto_update_pe_direct():
    """專門的PE自動更新函數 - 直接更新pe.pkl不創建備份"""
    print("=" * 80)
    print("🚀 PE資料自動更新 - 直接更新模式")
    print("=" * 80)

    try:
        # 檢查PE爬蟲函數是否可用
        try:
            from finlab.crawler import crawl_pe
            print("✅ PE爬蟲函數載入成功")
        except ImportError as e:
            print(f"❌ 無法載入PE爬蟲函數: {e}")
            print("請確保finlab/crawler.py文件存在且包含crawl_pe函數")
            return False

        # 使用現有的auto_update邏輯，但修改為直接更新模式
        print("� 使用增量更新邏輯...")

        # 調用現有的auto_update函數，但設置為PE直接更新模式
        from finlab.crawler import date_range
        auto_update('pe', crawl_pe, date_range)

        print("✅ PE資料直接更新完成!")
        return True

    except Exception as e:
        print(f"❌ PE資料更新失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def auto_update(table_name, crawl_function, time_range=None):
    """自動更新資料表 - 增強版本"""
    try:
        print(f"\n🔄 開始更新 {table_name}...")

        sig = signature(crawl_function)

        if len(sig.parameters) != 0:
            # 需要日期參數的爬蟲函數
            print(f"📅 獲取 {table_name} 表格日期範圍...")

            # PE資料特殊處理 - 優先從DB檔案讀取日期範圍
            if table_name == 'pe':
                # 首先檢查 pe_data.db 檔案
                pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
                if os.path.exists(pe_db_path):
                    try:
                        import sqlite3
                        conn = sqlite3.connect(pe_db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT MIN(date), MAX(date) FROM pe_data WHERE date IS NOT NULL')
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            first_date = pd.to_datetime(date_range_result[0])
                            last_date = pd.to_datetime(date_range_result[1])
                            print(f"   直接讀取DB檔案日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                        else:
                            first_date, last_date = None, None
                        conn.close()
                    except Exception as e:
                        print(f"⚠️ 讀取pe_data.db失敗: {e}")
                        first_date, last_date = None, None
                else:
                    print(f"⚠️ pe_data.db不存在，將創建新檔案")
                    first_date, last_date = None, None
            elif table_name == 'monthly_report':
                # 月營收資料特殊處理 - 直接讀取 monthly_report.db 檔案
                monthly_db_path = 'D:/Finlab/history/tables/monthly_report.db'
                if os.path.exists(monthly_db_path):
                    try:
                        import sqlite3
                        conn = sqlite3.connect(monthly_db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT MIN(date), MAX(date) FROM monthly_report WHERE date IS NOT NULL')
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            first_date = pd.to_datetime(date_range_result[0])
                            last_date = pd.to_datetime(date_range_result[1])
                            print(f"   直接讀取DB檔案日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                        else:
                            first_date, last_date = None, None
                        conn.close()
                    except Exception as e:
                        print(f"⚠️ 讀取monthly_report.db失敗: {e}")
                        first_date, last_date = None, None
                else:
                    print(f"⚠️ monthly_report.db不存在，將創建新檔案")
                    first_date, last_date = None, None
            elif table_name == 'bargin_report':
                # 三大法人資料特殊處理 - 直接讀取DB檔案
                bargin_db_path = 'D:/Finlab/history/tables/bargin_report.db'
                if os.path.exists(bargin_db_path):
                    try:
                        import sqlite3
                        conn = sqlite3.connect(bargin_db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT MIN(date), MAX(date) FROM bargin_report WHERE date IS NOT NULL')
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            first_date = pd.to_datetime(date_range_result[0])
                            last_date = pd.to_datetime(date_range_result[1])
                            print(f"   直接讀取DB檔案日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                        else:
                            first_date, last_date = None, None
                        conn.close()
                    except Exception as e:
                        print(f"⚠️ 讀取bargin_report.db失敗: {e}")
                        first_date, last_date = None, None
                else:
                    print(f"⚠️ bargin_report.db不存在，將創建新檔案")
                    first_date, last_date = None, None
            elif table_name == 'benchmark':
                # benchmark資料特殊處理 - 直接讀取DB檔案
                benchmark_db_path = 'D:/Finlab/history/tables/benchmark.db'
                if os.path.exists(benchmark_db_path):
                    try:
                        import sqlite3
                        conn = sqlite3.connect(benchmark_db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT MIN(date), MAX(date) FROM benchmark WHERE date IS NOT NULL')
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            first_date = pd.to_datetime(date_range_result[0])
                            last_date = pd.to_datetime(date_range_result[1])
                            print(f"   直接讀取DB檔案日期範圍: {first_date.strftime('%Y-%m-%d %H:%M:%S')} 至 {last_date.strftime('%Y-%m-%d %H:%M:%S')}")
                        else:
                            first_date, last_date = None, None
                        conn.close()
                    except Exception as e:
                        print(f"⚠️ 讀取benchmark.db失敗: {e}")
                        first_date, last_date = None, None
                else:
                    print(f"⚠️ benchmark.db不存在，將創建新檔案")
                    first_date, last_date = None, None
            elif table_name == 'price':
                # price資料特殊處理 - 直接讀取 price.db 檔案
                price_db_path = 'D:/Finlab/history/tables/price.db'
                if os.path.exists(price_db_path):
                    try:
                        import sqlite3
                        conn = sqlite3.connect(price_db_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT MIN(date), MAX(date) FROM stock_daily_data WHERE date IS NOT NULL')
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            first_date = pd.to_datetime(date_range_result[0])
                            last_date = pd.to_datetime(date_range_result[1])
                            print(f"   直接讀取DB檔案日期範圍: {first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}")
                        else:
                            first_date, last_date = None, None
                        conn.close()
                    except Exception as e:
                        print(f"⚠️ 讀取price.db失敗: {e}")
                        first_date, last_date = None, None
                else:
                    print(f"⚠️ price.db不存在，將創建新檔案")
                    first_date, last_date = None, None
            else:
                first_date, last_date = table_date_range(table_name)

            print(f"   表格日期範圍: {first_date} 至 {last_date}")

            if time_range:
                # 檢查 time_range 是否為函數（需要調用以獲取日期列表）
                if callable(time_range):
                    print(f"📅 調用日期範圍函數獲取更新範圍...")

                    # 檢查函數是否需要參數
                    import inspect
                    sig = inspect.signature(time_range)

                    if len(sig.parameters) == 0:
                        # 無參數函數，直接調用
                        try:
                            date_list = time_range()

                            if isinstance(date_list, list):
                                if len(date_list) == 0:
                                    print(f"✅ {table_name} 已是最新，無需更新")
                                    return
                                else:
                                    print(f"📅 增量更新範圍: {date_list[0].strftime('%Y-%m-%d')} 至 {date_list[-1].strftime('%Y-%m-%d')}")
                                    trading_dates = filter_trading_days(date_list)

                                    if trading_dates:
                                        # 使用改進的更新函數
                                        improved_update_table(table_name, crawl_function, trading_dates)
                                        print(f"✅ {table_name} 更新完成")
                                    else:
                                        print(f"✅ {table_name} 沒有需要更新的交易日")
                                    return  # 直接返回，不執行後續邏輯
                            else:
                                print(f"⚠️ 日期範圍函數返回格式錯誤，使用預設邏輯")
                        except Exception as e:
                            print(f"⚠️ 調用日期範圍函數失敗: {e}，使用預設邏輯")
                    else:
                        # 需要參數的函數，使用預設的增量更新邏輯
                        print(f"⚠️ 日期範圍函數需要參數，使用預設增量更新邏輯")

                # 檢查 time_range 是否為日期列表（用於直接傳入的日期列表）
                elif isinstance(time_range, list) and len(time_range) > 0:
                    # 直接使用傳入的日期列表
                    print(f"📅 使用預定義日期列表進行更新 ({len(time_range)} 天)")
                    trading_dates = filter_trading_days(time_range)

                    if trading_dates:
                        # 使用改進的更新函數
                        improved_update_table(table_name, crawl_function, trading_dates)
                        print(f"✅ {table_name} 更新完成")
                    else:
                        print(f"✅ {table_name} 沒有需要更新的交易日")
                    return  # 直接返回，不執行後續邏輯

                # 所有資料表都使用增量更新邏輯
                if table_name == 'monthly_report':
                    # 月營收資料：從最後日期的下個月開始更新
                    if last_date:
                        # 對於月營收，從下個月開始，但不能超過當前月份
                        next_month = last_date + datetime.timedelta(days=32)  # 確保跳到下個月
                        next_month = next_month.replace(day=1)  # 設為該月1號

                        # 確保是 datetime 類型
                        if isinstance(next_month, datetime.date) and not isinstance(next_month, datetime.datetime):
                            next_month = datetime.datetime.combine(next_month, datetime.time())

                        # 檢查是否超過當前月份，如果是則設為當前月份
                        current_month = datetime.datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                        if next_month > current_month:
                            start_update = current_month
                            print(f"   💡 調整開始日期到當前月份: {start_update.strftime('%Y-%m-%d')}")
                        else:
                            start_update = next_month
                    else:
                        # 如果沒有現有資料，從2018年開始
                        start_update = datetime.datetime(2018, 1, 1)

                    end_update = datetime.datetime.now()
                    print(f"📅 月營收增量更新範圍: {start_update.strftime('%Y-%m-%d')} 至 {end_update.strftime('%Y-%m-%d')}")

                    if last_date:
                        print(f"   💡 從現有資料最後日期 {last_date.strftime('%Y-%m-%d')} 之後開始更新")
                elif table_name == 'bargin_report':
                    # 三大法人資料：從最後日期的下一個交易日開始更新
                    if last_date:
                        # 對於三大法人，從下一個交易日開始
                        start_update = last_date + datetime.timedelta(days=1)
                    else:
                        # 如果沒有現有資料，從2012年開始（三大法人資料較早）
                        start_update = datetime.datetime(2012, 5, 1)

                    # 更新到今天（而不是只更新1天）
                    end_update = datetime.datetime.now()

                    # 如果開始日期已經是今天或之後，則設為無需更新
                    if start_update.date() > end_update.date():
                        print(f"📅 三大法人資料已是最新，無需更新")
                        print(f"   最後日期: {last_date.strftime('%Y-%m-%d')}")
                        print(f"   今天日期: {end_update.strftime('%Y-%m-%d')}")
                        # 設定為相同日期，讓後續邏輯跳過更新
                        start_update = end_update

                    print(f"📅 三大法人增量更新範圍: {start_update.strftime('%Y-%m-%d')} 至 {end_update.strftime('%Y-%m-%d')}")

                    # 如果沒有現有資料，從2011年開始（三大法人資料較早）
                    if not last_date:
                        start_update = datetime.datetime(2011, 1, 1)
                        end_update = datetime.datetime.now()
                        print(f"📅 {table_name} 完整爬取範圍: {start_update.strftime('%Y-%m-%d')} 至 {end_update.strftime('%Y-%m-%d')}")
                        days_to_update = (end_update.date() - start_update.date()).days + 1
                        print(f"   💡 沒有現有資料，從2011年開始完整爬取")
                        print(f"   � 預計更新 {days_to_update} 天的資料")
                    else:
                        # 有現有資料，只更新新的部分
                        days_to_update = (end_update.date() - start_update.date()).days + 1
                        print(f"   💡 從現有資料最後日期 {last_date.strftime('%Y-%m-%d')} 之後開始更新")
                        print(f"   📊 預計更新 {days_to_update} 天的資料")
                else:
                    # 其他資料表使用增量更新
                    start_update = last_date + datetime.timedelta(days=1) if last_date else datetime.datetime(2018, 1, 1)
                    end_update = datetime.datetime.now()
                    print(f"📅 增量更新範圍: {start_update.strftime('%Y-%m-%d')} 至 {end_update.strftime('%Y-%m-%d')}")

                if start_update <= end_update:
                    dates = time_range(start_update, end_update)
                    if dates:
                        # 過濾出交易日
                        trading_dates = filter_trading_days(dates)

                        if trading_dates:
                            # 使用改進的更新函數
                            improved_update_table(table_name, crawl_function, trading_dates)
                            print(f"✅ {table_name} 更新完成")
                        else:
                            print(f"✅ {table_name} 沒有需要更新的交易日")
                    else:
                        print(f"✅ {table_name} 日期範圍內沒有資料需要更新")
                else:
                    print(f"✅ {table_name} 已是最新，無需更新")
            else:
                print(f"⚠️ {table_name} 無法獲取日期範圍或時間範圍函數")
        else:
            # 不需要日期參數的爬蟲函數
            print(f"🔄 執行完整爬取 {table_name}...")
            df = crawl_function()
            safe_to_pickle(df, table_name)
            print(f"✅ {table_name} 完整爬取完成")

    except Exception as e:
        print(f"❌ 更新 {table_name} 失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")

def improved_update_table(table_name, crawl_function, dates):
    """改進的表格更新函數 - 從現有檔案增量更新"""

    if not dates:
        print("該時間段沒有可以爬取之資料")
        return

    print(f'start crawl {table_name} from {dates[0]} to {dates[-1]}')

    # 讀取現有資料
    existing_df = None
    existing_file = None

    if table_name == 'pe':
        # PE資料特殊處理 - 優先從DB檔案讀取
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        existing_file = None

        if os.path.exists(pe_db_path):
            existing_file = pe_db_path
        else:
            # 如果DB檔案不存在，檢查PKL檔案作為備用
            pe_paths = [
                'D:/Finlab/history/tables/pe.pkl',  # 標準Finlab位置
                'pe.pkl',  # 當前目錄
                'data/pe.pkl',  # data目錄
                'history/tables/pe.pkl'  # 相對路徑
            ]

            for path in pe_paths:
                if os.path.exists(path):
                    existing_file = path
                    break
    elif table_name == 'monthly_report':
        # 月營收資料特殊處理 - 直接從DB檔案獲取資料
        monthly_db_path = 'D:/Finlab/history/tables/monthly_report.db'
        if os.path.exists(monthly_db_path):
            existing_file = monthly_db_path
            print(f"ℹ️ 月營收資料將從DB檔案讀取: {monthly_db_path}")
        else:
            existing_file = None
            print(f"ℹ️ monthly_report.db不存在，將創建新檔案")
    elif table_name == 'bargin_report':
        # 三大法人資料特殊處理 - 不讀取PKL檔案，直接更新DB
        existing_file = None  # 不使用現有PKL檔案
    elif table_name == 'benchmark':
        # benchmark資料特殊處理 - 不讀取PKL檔案，直接更新DB
        existing_file = None  # 不使用現有PKL檔案
    elif table_name == 'price':
        # price資料特殊處理 - 使用 newprice.db
        existing_file = None  # 不使用現有PKL檔案，直接更新DB
    elif table_name == 'financial_data':
        # 財務資料特殊處理 - 直接從DB檔案獲取資料
        financial_db_path = 'D:/Finlab/history/tables/financial_data.db'
        if os.path.exists(financial_db_path):
            existing_file = financial_db_path
            print(f"ℹ️ 財務資料將從DB檔案讀取: {financial_db_path}")
        else:
            existing_file = None
            print(f"ℹ️ financial_data.db不存在，將創建新檔案")
    else:
        existing_file = f'data/{table_name}.pkl'

    if existing_file and os.path.exists(existing_file):
        try:
            # 特殊處理PE的DB檔案
            if table_name == 'pe' and existing_file.endswith('.db'):
                import sqlite3
                conn = sqlite3.connect(existing_file)
                existing_df = pd.read_sql('SELECT * FROM pe_data', conn)
                conn.close()

                # 轉換為適當的格式
                if not existing_df.empty:
                    if 'stock_id' in existing_df.columns and 'date' in existing_df.columns:
                        existing_df['date'] = pd.to_datetime(existing_df['date'])
                        existing_df = existing_df.set_index(['stock_id', 'date'])

                print(f"📂 從DB檔案讀取PE資料: {existing_file} ({len(existing_df)} 筆資料)")
            elif table_name == 'monthly_report' and existing_file.endswith('.db'):
                # 月營收資料從DB檔案讀取
                import sqlite3
                conn = sqlite3.connect(existing_file)
                existing_df = pd.read_sql('SELECT * FROM monthly_report', conn)
                conn.close()

                # 轉換為適當的格式
                if not existing_df.empty:
                    if 'stock_id' in existing_df.columns and 'date' in existing_df.columns:
                        existing_df['date'] = pd.to_datetime(existing_df['date'])
                        existing_df = existing_df.set_index(['stock_id', 'date'])

                print(f"📂 從DB檔案讀取月營收資料: {existing_file} ({len(existing_df)} 筆資料)")
            elif table_name == 'financial_data' and existing_file.endswith('.db'):
                # 財務資料從DB檔案讀取
                import sqlite3
                conn = sqlite3.connect(existing_file)
                existing_df = pd.read_sql('SELECT * FROM financial_data', conn)
                conn.close()

                # 轉換為適當的格式
                if not existing_df.empty:
                    if 'stock_id' in existing_df.columns and 'date' in existing_df.columns:
                        existing_df['date'] = pd.to_datetime(existing_df['date'])
                        existing_df = existing_df.set_index(['stock_id', 'date'])

                print(f"📂 從DB檔案讀取財務資料: {existing_file} ({len(existing_df)} 筆資料)")
            else:
                # 一般PKL檔案處理
                existing_df = pd.read_pickle(existing_file)
                print(f"📂 讀取現有檔案: {existing_file} ({len(existing_df)} 筆資料)")
        except Exception as e:
            print(f"⚠️ 讀取現有檔案失敗: {e}")
            existing_df = None
    else:
        if table_name == 'pe':
            print(f"ℹ️ 未找到現有PE檔案，將創建新檔案")
        elif table_name == 'monthly_report':
            print(f"ℹ️ monthly_report.db不存在，將創建新檔案")
        elif table_name == 'bargin_report':
            print(f"ℹ️ 三大法人資料將直接更新到DB檔案，不使用PKL檔案")
        elif table_name == 'benchmark':
            print(f"ℹ️ benchmark資料將直接更新到DB檔案，不使用PKL檔案")
        elif table_name == 'price':
            print(f"ℹ️ price資料將直接更新到 newprice.db，不使用PKL檔案")
        else:
            print(f"ℹ️ 現有檔案不存在: {existing_file}")

    df_list = []
    success_count = 0

    for i, date in enumerate(dates):
        try:
            progress = f"[{i+1:3d}/{len(dates):3d}] {(i+1)/len(dates)*100:5.1f}%"
            print(f"\r{progress} {date.strftime('%Y-%m-%d')}", end="", flush=True)

            # 調用爬蟲函數
            df = crawl_function(date)

            if df is not None and not df.empty:
                # 對於benchmark和bargin_report，立即保存每天的資料
                if table_name == 'benchmark':
                    target_db_file = 'D:/Finlab/history/tables/benchmark.db'
                    if save_benchmark_to_database(df, target_db_file):
                        success_count += 1
                        print(f"💾 已保存 {dates[i].strftime('%Y-%m-%d')} 的資料")
                    else:
                        print(f"❌ 保存失敗 {dates[i].strftime('%Y-%m-%d')}")
                elif table_name == 'bargin_report':
                    target_db_file = 'D:/Finlab/history/tables/bargin_report.db'
                    if save_bargin_report_to_database_incremental(df, target_db_file):
                        success_count += 1
                        print(f"💾 已保存 {dates[i].strftime('%Y-%m-%d')} 的資料")
                    else:
                        print(f"❌ 保存失敗 {dates[i].strftime('%Y-%m-%d')}")
                elif table_name == 'price':
                    # 直接保存到 price.db
                    price_db_file = 'D:/Finlab/history/tables/price.db'
                    target_db_file = price_db_file
                    print(f"💾 保存到 price.db")

                    if save_price_to_newprice_database(df, target_db_file):
                        success_count += 1
                        print(f"💾 已保存 {dates[i].strftime('%Y-%m-%d')} 的資料")
                    else:
                        print(f"❌ 保存失敗 {dates[i].strftime('%Y-%m-%d')}")
                else:
                    # 其他資料類型收集到列表中，最後批次處理
                    df_list.append(df)
                    success_count += 1

            # 增加延遲避免被封鎖 - 大幅縮短延遲時間
            if i < len(dates) - 1:  # 不是最後一個
                delay = random.uniform(0.5, 1.0)  # 縮短延遲時間到0.5-1秒
                time.sleep(delay)

        except Exception:
            continue

    print(f"\n📊 完成: {success_count}/{len(dates)} 成功")
 
    # 對於benchmark、bargin_report和price，資料已經即時保存，直接完成
    if table_name in ['benchmark', 'bargin_report', 'price']:
        if success_count > 0:
            print(f"✅ {table_name} 即時更新完成 ({success_count} 天成功)")
        else:
            print(f"❌ {table_name} 沒有成功爬取任何資料")
        return

    # 其他資料類型的批次處理邏輯
    if df_list:
        new_df = pd.concat(df_list, ignore_index=True)

        # 與現有資料合併
        if existing_df is not None:
            combined_df = pd.concat([existing_df, new_df])

            # PE資料特殊處理
            if table_name == 'pe':
                if not hasattr(combined_df.index, 'names') or combined_df.index.names != ['stock_id', 'date']:
                    combined_df = combined_df.reset_index()
                    if 'stock_id' in combined_df.columns and 'date' in combined_df.columns:
                        combined_df = combined_df.set_index(['stock_id', 'date'])

                combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
                combined_df = combined_df.sort_index()

                expected_columns = ['殖利率(%)', '本益比', '股利年度', '股價淨值比']
                available_columns = [col for col in expected_columns if col in combined_df.columns]
                if available_columns:
                    combined_df = combined_df[available_columns]
            else:
                if hasattr(combined_df.index, 'names') and len(combined_df.index.names) > 1:
                    combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
                else:
                    combined_df = combined_df.drop_duplicates()
        else:
            combined_df = new_df

        # 保存合併後的資料 (只處理非DB類型的資料)
        if safe_to_pickle(combined_df, table_name):
            print(f"✅ {table_name} 更新完成 ({len(combined_df):,} 筆)")
        else:
            print(f"❌ 保存失敗")
    else:
        print(f"❌ 沒有成功爬取任何資料")

def interactive_benchmark_update():
    """互動式benchmark更新函數"""
    print("🚀 Benchmark 互動式更新系統")
    print("💾 資料庫位置: D:/Finlab/history/tables/benchmark.db")

    # 顯示當前資料庫狀態
    try:
        import sqlite3
        db_path = 'D:/Finlab/history/tables/benchmark.db'
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT MIN(date), MAX(date) FROM benchmark WHERE date IS NOT NULL')
            date_range_result = cursor.fetchone()
            if date_range_result and date_range_result[0]:
                print(f"📊 當前最後日期: {date_range_result[1]}")
            else:
                print("📊 資料庫為空，將從頭開始")
            conn.close()
        else:
            print("📊 資料庫不存在，將創建新檔案")
    except Exception as e:
        print(f"⚠️ 無法讀取資料庫狀態: {e}")

    while True:
        print("\n" + "="*50)
        print("🎯 Benchmark 資料爬取選項")
        print("="*50)
        print("1. 20天存一次  （繼續爬取，每20天顯示選項）")
        print("2. 100天存一次 （繼續爬取，每100天顯示選項）")
        print("3. 更新到最新日期 （繼續到最新日期）")
        print("4. 立即停止")
        print("="*50)

        try:
            choice = input("請輸入選項 (1-4): ").strip()

            if choice == '1':
                batch_update_benchmark(20)
            elif choice == '2':
                batch_update_benchmark(100)
            elif choice == '3':
                auto_update('benchmark', crawl_benchmark, date_range)
                break
            elif choice == '4':
                print("⏹️ 用戶選擇停止")
                break
            else:
                print("❌ 無效選項，請輸入 1-4")

        except KeyboardInterrupt:
            print("\n⏹️ 用戶中斷程式")
            break
        except Exception as e:
            print(f"❌ 程式錯誤: {e}")

    print("\n👋 程式結束")

def batch_update_benchmark(batch_days):
    """批次更新benchmark資料"""
    print(f"\n📅 選項: {batch_days}天存一次")

    # 獲取最後日期
    try:
        import sqlite3
        db_path = 'D:/Finlab/history/tables/benchmark.db'
        last_date = None

        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT MAX(date) FROM benchmark WHERE date IS NOT NULL')
            result = cursor.fetchone()
            if result and result[0]:
                last_date = pd.to_datetime(result[0])
            conn.close()

        if last_date:
            start_date = last_date + datetime.timedelta(days=1)
            print(f"📊 從最後日期繼續: {last_date.strftime('%Y-%m-%d')}")
        else:
            start_date = datetime.datetime(2018, 1, 1)
            print(f"📊 從頭開始爬取")

        current_date = start_date
        end_date = datetime.datetime.now()

        while current_date <= end_date:
            # 計算批次範圍
            batch_end = min(current_date + datetime.timedelta(days=batch_days-1), end_date)

            # 生成日期範圍
            dates = date_range(current_date, batch_end)
            trading_dates = filter_trading_days(dates)

            if trading_dates:
                print(f"\n🚀 開始{batch_days}天批次({current_date.strftime('%Y-%m-%d')} ~ {batch_end.strftime('%Y-%m-%d')})")
                improved_update_table('benchmark', crawl_benchmark, trading_dates)

                # 每批次後詢問是否繼續
                if current_date < end_date:
                    print(f"\n📊 已完成 {current_date.strftime('%Y-%m-%d')} ~ {batch_end.strftime('%Y-%m-%d')}")
                    choice = input("繼續下一個批次？(y/n，直接按Enter繼續): ").strip().lower()
                    if choice == 'n':
                        print("⏹️ 用戶選擇停止")
                        break

            current_date = batch_end + datetime.timedelta(days=1)

    except Exception as e:
        print(f"❌ 批次更新失敗: {e}")
        import traceback
        traceback.print_exc()

def get_newprice_incremental_date_range():
    """獲取基於 price.db 的增量更新日期範圍 (自動更新到最新日期)"""
    import sqlite3
    from datetime import datetime, timedelta

    # 直接使用 price.db
    price_db = 'D:/Finlab/history/tables/price.db'

    target_db = None
    if os.path.exists(price_db):
        target_db = price_db
        print(f"📊 使用 price.db 檢查最後日期")
    else:
        print(f"📊 price.db 不存在，將進行完整更新")

    try:
        if not target_db:
            print(f"❌ 找不到 price.db，將進行完整更新")
            # 如果沒有資料庫，返回最近30天的日期範圍
            from datetime import datetime, timedelta
            today = datetime.now()
            start_date = today - timedelta(days=30)
            dates = []
            current = start_date
            while current <= today:
                if current.weekday() < 5:  # 只包含工作日
                    dates.append(current)
                current += timedelta(days=1)
            return dates

        conn = sqlite3.connect(target_db)
        cursor = conn.cursor()

        # 獲取最後日期
        cursor.execute('SELECT MAX(date) FROM stock_daily_data')
        last_date_str = cursor.fetchone()[0]
        conn.close()

        if not last_date_str:
            print(f"❌ {os.path.basename(target_db)} 中沒有資料")
            # 如果資料庫存在但沒有資料，返回最近30天的日期範圍
            from datetime import datetime, timedelta
            today = datetime.now()
            start_date = today - timedelta(days=30)
            dates = []
            current = start_date
            while current <= today:
                if current.weekday() < 5:  # 只包含工作日
                    dates.append(current)
                current += timedelta(days=1)
            return dates

        # 計算增量更新範圍 (從最後日期的下一天開始，更新到今天)
        last_date = datetime.strptime(last_date_str, '%Y-%m-%d')
        start_date = last_date + timedelta(days=1)
        today = datetime.now()

        # 如果已經是最新的，就不需要更新
        if start_date > today:
            print(f"📊 price.db 已是最新: {last_date_str}")
            print(f"🎯 無需更新")
            return []

        print(f"📊 price.db 最後日期: {last_date_str}")
        print(f"🎯 計劃增量更新範圍: {start_date.strftime('%Y-%m-%d')} 到 {today.strftime('%Y-%m-%d')}")

        # 生成工作日日期範圍 (排除週末)
        dates = []
        current = start_date
        while current <= today:
            # 只包含工作日 (週一到週五)
            if current.weekday() < 5:
                dates.append(current)
            current += timedelta(days=1)

        print(f"📅 實際更新工作日數量: {len(dates)} 天")
        if dates:
            print(f"   從: {dates[0].strftime('%Y-%m-%d')} ({dates[0].strftime('%A')})")
            print(f"   到: {dates[-1].strftime('%Y-%m-%d')} ({dates[-1].strftime('%A')})")

        return dates

    except Exception as e:
        print(f"❌ 獲取增量日期範圍失敗: {e}")
        return None

def main():
    """主函數 - 自動更新所有資料庫"""
    print("=" * 60)
    print("🚀 Finlab 資料庫自動更新系統")
    print("=" * 60)

    try:
        # 開始自動更新
        print("🔄 開始自動更新...")

        # 定義所有要更新的資料表 (只保存DB格式)
        update_tasks = [
                ('price', crawl_price, get_newprice_incremental_date_range),
                ('bargin_report', crawl_bargin, date_range),
                ('pe', crawl_pe, date_range),
                ('benchmark', crawl_benchmark, date_range),
                ('monthly_report', crawl_monthly_report, month_range),
                ('financial_data', crawl_unified_financial_data, season_range),  # 統一財務資料 (取代分離的損益表和資產負債表)
                ('divide_ratio', crawl_divide_ratio, None),  # 統一除權息資料 (上市 + 上櫃)
                ('cap_reduction', crawl_cap_reduction, None),  # 統一減資資料 (上市 + 上櫃)
            ]

        # 檢查命令行參數，允許指定特定資料表
        import sys
        if len(sys.argv) > 1:
            specified_table = sys.argv[1].lower()
            # 過濾出指定的資料表
            filtered_tasks = [task for task in update_tasks if task[0] == specified_table]
            if filtered_tasks:
                update_tasks = filtered_tasks
                print(f"🎯 專門更新 {specified_table} 資料...")
            else:
                print(f"⚠️ 未找到資料表 '{specified_table}'，將更新所有資料表")

        print(f"📋 計劃更新 {len(update_tasks)} 個資料表:")
        for i, (table_name, _, _) in enumerate(update_tasks, 1):
            print(f"   {i}. {table_name}")
        print()

        success_count = 0
        total_count = len(update_tasks)

        for table_name, crawl_func, time_range_func in update_tasks:
            try:
                auto_update(table_name, crawl_func, time_range_func)
                success_count += 1
            except Exception as e:
                print(f"❌ {table_name} 更新過程發生錯誤: {e}")
                import traceback
                traceback.print_exc()

        print("\n" + "=" * 60)
        print("📊 更新結果總結")
        print("=" * 60)
        print(f"成功更新: {success_count}/{total_count} 個資料表")

        if success_count > 0:
            print("✅ 所有資料已保存到資料庫檔案")

        print("🎉 更新完成！")

    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷程式")
    except Exception as e:
        print(f"❌ 程式錯誤: {e}")

if __name__ == "__main__":
    # 檢查是否有命令行參數
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "convert_price":
            # 執行 price.pkl 轉換
            convert_price_pkl_to_db()

        elif sys.argv[1] == "convert_all":
            # 執行所有資料轉換
            print("🚀 開始轉換所有資料...")
            price_success = convert_price_pkl_to_db()

            if price_success:
                print("\n🎉 所有資料轉換完成！")
            else:
                print("\n⚠️ 部分轉換失敗，請檢查錯誤訊息")
        elif sys.argv[1] == "price":
            # 執行價格資料爬蟲
            print("🚀 開始執行價格資料爬蟲...")
            try:
                # 導入爬蟲函數
                sys.path.insert(0, 'finlab')
                from crawler import crawl_price

                # 執行爬蟲 (使用增量更新)
                update_table('price', crawl_price, get_newprice_incremental_date_range())
                print(f"✅ 價格資料爬蟲執行完成")
            except Exception as e:
                print(f"❌ 價格資料爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "bargin_report":
            # 執行三大法人資料爬蟲
            print("🚀 開始執行三大法人資料爬蟲...")
            try:
                # 導入爬蟲函數
                sys.path.insert(0, 'finlab')
                from crawler import crawl_bargin

                # 執行爬蟲 (使用增量更新)
                update_table('bargin_report', crawl_bargin, None)
                print(f"✅ 三大法人資料爬蟲執行完成")
            except Exception as e:
                print(f"❌ 三大法人資料爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "pe":
            # 執行本益比資料爬蟲
            print("🚀 開始執行本益比資料爬蟲...")
            try:
                # 導入爬蟲函數
                sys.path.insert(0, 'finlab')
                from crawler import crawl_pe

                # 執行爬蟲 (使用增量更新)
                update_table('pe', crawl_pe, None)
                print(f"✅ 本益比資料爬蟲執行完成")
            except Exception as e:
                print(f"❌ 本益比資料爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "monthly_report":
            # 執行月營收資料爬蟲
            print("🚀 開始執行月營收資料爬蟲...")
            try:
                # 導入爬蟲函數
                sys.path.insert(0, 'finlab')
                from crawler import crawl_monthly_report

                # 執行爬蟲 (使用增量更新)
                update_table('monthly_report', crawl_monthly_report, None)
                print(f"✅ 月營收資料爬蟲執行完成")
            except Exception as e:
                print(f"❌ 月營收資料爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "financial_data":
            # 執行統一財務資料爬蟲
            print("🚀 開始執行統一財務資料爬蟲...")
            try:
                # 導入爬蟲函數
                sys.path.insert(0, 'finlab')
                from crawler import crawl_unified_financial_data, season_range

                # 生成季度日期範圍
                start_date = datetime.date(2018, 1, 1)
                end_date = datetime.date.today()
                season_dates = season_range(start_date, end_date)

                print(f"📅 季度日期範圍: {len(season_dates)} 個季度")
                print(f"   從 {season_dates[0]} 到 {season_dates[-1]}")

                # 執行爬蟲 (使用季度更新)
                improved_update_table('financial_data', crawl_unified_financial_data, season_dates)
                print(f"✅ 統一財務資料爬蟲執行完成")
            except Exception as e:
                print(f"❌ 統一財務資料爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "divide_ratio":
            # 執行統一除權息爬蟲
            print("🚀 開始執行統一除權息爬蟲...")
            try:
                df = crawl_divide_ratio()
                if df is not None and not df.empty:
                    print(f"✅ 統一除權息爬蟲執行成功: {len(df):,} 筆資料")
                else:
                    print("⚠️ 統一除權息爬蟲未獲取到資料")
            except Exception as e:
                print(f"❌ 統一除權息爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        elif sys.argv[1] == "cap_reduction":
            # 執行統一減資爬蟲
            print("🚀 開始執行統一減資爬蟲...")
            try:
                df = crawl_cap_reduction()
                if df is not None and not df.empty:
                    print(f"✅ 統一減資爬蟲執行成功: {len(df):,} 筆資料")
                else:
                    print("⚠️ 統一減資爬蟲未獲取到資料")
            except Exception as e:
                print(f"❌ 統一減資爬蟲執行失敗: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"❌ 未知的參數: {sys.argv[1]}")
            print("💡 可用參數:")
            print("   price - 執行價格資料爬蟲")
            print("   bargin_report - 執行三大法人資料爬蟲")
            print("   pe - 執行本益比資料爬蟲")
            print("   monthly_report - 執行月營收資料爬蟲")
            print("   financial_data - 執行統一財務資料爬蟲")
            print("   divide_ratio - 執行統一除權息爬蟲")
            print("   cap_reduction - 執行統一減資爬蟲")
            print("   convert_price - 轉換價格資料")
            print("   convert_all - 轉換所有資料")
    else:
        main()
