#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
正確分析台積電月營收Excel文件
重新理解文件結構
"""

import pandas as pd
import os
import glob
import re

def correct_excel_analysis():
    """正確分析台積電月營收Excel文件"""
    print("🔍 重新分析台積電月營收Excel文件")
    print("=" * 50)
    
    # 尋找最新下載的Excel文件
    download_dir = "D:/Finlab/history/tables/monthly_revenue"
    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
    
    if not excel_files:
        print("❌ 未找到下載的Excel文件")
        return False
    
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📊 分析文件: {latest_file}")
    
    try:
        # 讀取Excel文件
        df = pd.read_excel(latest_file, header=None)
        print(f"📋 文件包含 {len(df)} 行, {len(df.columns)} 列")
        
        print("\n🔍 重新理解文件結構...")
        print("既然這是台積電的月營收數據，讓我們尋找月份相關的模式...")
        
        # 尋找可能的月份標識
        month_indicators = []
        
        for index in range(min(100, len(df))):
            row = df.iloc[index]
            
            # 檢查每一列是否包含月份信息
            for col_idx, cell in enumerate(row):
                cell_str = str(cell).strip()
                
                # 檢查各種月份格式
                month_patterns = [
                    r'\d{4}/\d{1,2}',     # 2025/06
                    r'\d{4}-\d{1,2}',     # 2025-06
                    r'\d{4}\.\d{1,2}',    # 2025.06
                    r'[A-Za-z]{3}-\d{2}', # Jun-25
                    r'\d{1,2}/\d{4}',     # 06/2025
                    r'2024|2025',         # 年份
                ]
                
                for pattern in month_patterns:
                    if re.search(pattern, cell_str):
                        month_indicators.append((index, col_idx, cell_str, pattern))
                        break
        
        if month_indicators:
            print(f"✅ 找到 {len(month_indicators)} 個月份相關標識:")
            for row_idx, col_idx, content, pattern in month_indicators[:20]:
                print(f"   行{row_idx}列{col_idx}: '{content}' (格式: {pattern})")
        
        # 尋找數值密集的區域（可能是數據區域）
        print(f"\n🔍 尋找數值密集區域...")
        
        numeric_density = []
        
        for index in range(min(200, len(df))):
            row = df.iloc[index]
            numeric_count = 0
            
            for cell in row:
                cell_str = str(cell).replace(',', '').replace('+', '').replace('-', '').strip()
                if cell_str.replace('.', '').isdigit() and len(cell_str) > 0:
                    numeric_count += 1
            
            if numeric_count >= 5:  # 至少5個數字
                numeric_density.append((index, numeric_count))
        
        if numeric_density:
            print(f"✅ 找到 {len(numeric_density)} 個數值密集行:")
            for row_idx, count in numeric_density[:10]:
                row = df.iloc[row_idx]
                sample_data = [str(x)[:10] for x in row[:8]]
                print(f"   行{row_idx} ({count}個數字): {sample_data}")
        
        # 尋找包含"營收"、"億"等關鍵字的區域
        print(f"\n🔍 尋找營收相關關鍵字...")
        
        revenue_keywords = ['營收', '億', '月增', '年增', '累計', '收入']
        keyword_locations = []
        
        for index in range(min(50, len(df))):
            row = df.iloc[index]
            
            for col_idx, cell in enumerate(row):
                cell_str = str(cell)
                
                for keyword in revenue_keywords:
                    if keyword in cell_str:
                        keyword_locations.append((index, col_idx, cell_str, keyword))
        
        if keyword_locations:
            print(f"✅ 找到 {len(keyword_locations)} 個營收相關關鍵字:")
            for row_idx, col_idx, content, keyword in keyword_locations[:15]:
                print(f"   行{row_idx}列{col_idx}: '{content}' (關鍵字: {keyword})")
        
        # 基於分析結果，嘗試找到正確的數據區域
        print(f"\n🎯 推測數據結構...")
        
        # 如果有月份標識和數值密集區域，找到它們的交集
        if month_indicators and numeric_density:
            # 找到最可能的數據起始行
            month_rows = set(item[0] for item in month_indicators)
            numeric_rows = set(item[0] for item in numeric_density)
            
            # 找到既有月份又有數字的行
            data_candidate_rows = month_rows.intersection(numeric_rows)
            
            if data_candidate_rows:
                suggested_start = min(data_candidate_rows)
                print(f"✅ 建議數據起始行: {suggested_start}")
                
                # 顯示該行的詳細內容
                print(f"\n📊 行{suggested_start}的詳細內容:")
                row = df.iloc[suggested_start]
                for i, cell in enumerate(row[:15]):
                    print(f"   列{i}: '{cell}'")
                
                return suggested_start
            else:
                print("⚠️ 未找到同時包含月份和數字的行")
        
        # 如果上述方法失敗，嘗試其他策略
        print(f"\n🔄 使用備用策略...")
        
        # 尋找第一個包含大量數字的行
        for index in range(min(100, len(df))):
            row = df.iloc[index]
            
            # 檢查是否有足夠的數字列
            numeric_cols = 0
            for cell in row:
                try:
                    cell_str = str(cell).replace(',', '').strip()
                    if cell_str and cell_str != 'nan':
                        float(cell_str)
                        numeric_cols += 1
                except:
                    pass
            
            if numeric_cols >= 8:  # 至少8個數字列
                print(f"✅ 備用方案：建議數據起始行 {index}")
                
                # 顯示該行內容
                print(f"📊 行{index}的內容:")
                for i, cell in enumerate(row[:12]):
                    print(f"   列{i}: '{cell}'")
                
                return index
        
        print("❌ 無法確定數據結構")
        return None
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        return None

def test_parsing_with_found_structure(data_start_row):
    """使用找到的結構測試解析"""
    if data_start_row is None:
        print("❌ 無法測試，未找到數據起始行")
        return
    
    print(f"\n🧪 使用行{data_start_row}測試解析...")
    
    try:
        # 尋找Excel文件
        download_dir = "D:/Finlab/history/tables/monthly_revenue"
        excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
        latest_file = max(excel_files, key=os.path.getctime)
        
        # 讀取文件
        df = pd.read_excel(latest_file, header=None)
        
        # 嘗試解析前5行數據
        print("📊 嘗試解析前5行數據:")
        
        for i in range(5):
            if data_start_row + i < len(df):
                row = df.iloc[data_start_row + i]
                
                print(f"\n行{data_start_row + i}:")
                
                # 嘗試解析第一列作為月份
                month_cell = str(row.iloc[0]).strip()
                print(f"   月份欄位: '{month_cell}'")
                
                # 嘗試解析數值欄位
                for j in range(1, min(12, len(row))):
                    cell_value = row.iloc[j]
                    
                    try:
                        # 清理數值
                        cleaned = str(cell_value).replace(',', '').replace('+', '').strip()
                        if cleaned and cleaned != 'nan':
                            float_value = float(cleaned)
                            print(f"   列{j}: {cell_value} -> {float_value}")
                        else:
                            print(f"   列{j}: {cell_value} -> None")
                    except:
                        print(f"   列{j}: {cell_value} -> 無法解析")
        
    except Exception as e:
        print(f"❌ 測試解析失敗: {e}")

def main():
    """主函數"""
    print("🔄 重新分析台積電月營收Excel文件")
    print("感謝您的澄清！讓我正確理解文件結構")
    
    data_start_row = correct_excel_analysis()
    
    if data_start_row is not None:
        test_parsing_with_found_structure(data_start_row)
        print(f"\n🎉 分析完成！找到數據起始行: {data_start_row}")
        print("現在可以基於這個結構修復Excel解析器")
    else:
        print("\n❌ 仍無法確定文件結構，可能需要手動檢查Excel文件")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
