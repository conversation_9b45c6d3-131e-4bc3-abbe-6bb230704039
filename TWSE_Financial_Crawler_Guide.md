# TWSE 財務報表爬蟲使用指南

## 🎯 概述

基於 TWSEMCPServer 架構，我們成功整合了台灣證交所官方 OpenAPI 的財務報表爬蟲，可以自動獲取**綜合損益表**和**資產負債表**資料。

## ✨ 功能特色

✅ **官方資料來源**: 直接使用台灣證交所 OpenAPI  
✅ **完整整合**: 與現有 auto_update.py 系統無縫整合  
✅ **雙表支援**: 同時支援綜合損益表和資產負債表  
✅ **即時更新**: 獲取最新的財務報表資料  
✅ **多格式儲存**: 支援 SQLite 資料庫格式  
✅ **豐富分析**: 包含 1000+ 家上市公司資料  

## 🚀 快速開始

### 方法1: 單獨執行財務報表爬蟲

```bash
python auto_update.py twse_financial_statements
```

### 方法2: 執行完整自動更新

```bash
python auto_update.py
```

### 方法3: 直接測試

```bash
python twse_openapi_crawler.py
```

## 📊 資料來源

- **基礎 URL**: `https://openapi.twse.com.tw/v1`
- **綜合損益表**: `/opendata/t187ap06_L_ci`
- **資產負債表**: `/opendata/t187ap07_L_ci`
- **資料範圍**: 1000+ 家上市公司
- **更新頻率**: 季度更新

## 💾 資料儲存結構

### 檔案位置
```
history/tables/
├── income_statements.db      # 綜合損益表
├── balance_sheets.db         # 資產負債表
└── financial_statements.db   # 合併資料庫
```

### 資料表結構

#### 綜合損益表 (income_statements)
- **stock_id**: 股票代碼
- **stock_name**: 公司名稱
- **營業收入**: 營業收入
- **營業成本**: 營業成本
- **營業毛利（毛損）**: 營業毛利
- **營業利益（損失）**: 營業利益
- **本期淨利（淨損）**: 本期淨利
- **date**: 資料日期
- **data_type**: 資料類型

#### 資產負債表 (balance_sheets)
- **stock_id**: 股票代碼
- **stock_name**: 公司名稱
- **流動資產**: 流動資產
- **非流動資產**: 非流動資產
- **資產總額**: 資產總額
- **流動負債**: 流動負債
- **非流動負債**: 非流動負債
- **負債總額**: 負債總額
- **權益總額**: 權益總額
- **date**: 資料日期
- **data_type**: 資料類型

## 🔍 查詢範例

### 基本查詢

```sql
-- 查詢台積電綜合損益表
SELECT * FROM income_statements WHERE stock_id = '2330';

-- 查詢台積電資產負債表
SELECT * FROM balance_sheets WHERE stock_id = '2330';
```

### 進階分析查詢

```sql
-- 營收前20名公司
SELECT 
    stock_id, stock_name, 
    CAST(營業收入 AS REAL) as revenue,
    ROUND(CAST(營業毛利（毛損） AS REAL) / CAST(營業收入 AS REAL) * 100, 2) as gross_margin
FROM income_statements 
WHERE 營業收入 IS NOT NULL AND CAST(營業收入 AS REAL) > 0
ORDER BY CAST(營業收入 AS REAL) DESC 
LIMIT 20;

-- 財務健康度分析 (負債比 < 50%)
SELECT 
    stock_id, stock_name,
    CAST(資產總額 AS REAL) as total_assets,
    ROUND((CAST(負債總額 AS REAL) / CAST(資產總額 AS REAL)) * 100, 2) as debt_ratio
FROM balance_sheets 
WHERE CAST(資產總額 AS REAL) > 0
  AND (CAST(負債總額 AS REAL) / CAST(資產總額 AS REAL)) < 0.5
ORDER BY debt_ratio ASC;

-- 高獲利率公司 (淨利率 > 10%)
SELECT 
    i.stock_id, i.stock_name,
    CAST(i.營業收入 AS REAL) as revenue,
    CAST(i.本期淨利（淨損） AS REAL) as net_profit,
    ROUND(CAST(i.本期淨利（淨損） AS REAL) / CAST(i.營業收入 AS REAL) * 100, 2) as net_margin
FROM income_statements i
WHERE i.營業收入 IS NOT NULL 
  AND CAST(i.營業收入 AS REAL) > 0
  AND i.本期淨利（淨損） IS NOT NULL
  AND CAST(i.本期淨利（淨損） AS REAL) > 0
  AND (CAST(i.本期淨利（淨損） AS REAL) / CAST(i.營業收入 AS REAL)) > 0.1
ORDER BY net_margin DESC;
```

## 📈 財務分析範例

### Python 分析腳本

```python
import sqlite3
import pandas as pd

# 連接資料庫
conn = sqlite3.connect('history/tables/financial_statements.db')

# 綜合財務分析
query = """
SELECT 
    i.stock_id, i.stock_name,
    CAST(i.營業收入 AS REAL) as revenue,
    CAST(i.本期淨利（淨損） AS REAL) as net_profit,
    CAST(b.資產總額 AS REAL) as total_assets,
    ROUND(CAST(i.本期淨利（淨損） AS REAL) / CAST(i.營業收入 AS REAL) * 100, 2) as net_margin,
    ROUND(CAST(i.本期淨利（淨損） AS REAL) / CAST(b.資產總額 AS REAL) * 100, 2) as roa
FROM income_statements i
JOIN balance_sheets b ON i.stock_id = b.stock_id
WHERE CAST(i.營業收入 AS REAL) > 0 AND CAST(i.本期淨利（淨損） AS REAL) > 0
ORDER BY roa DESC
LIMIT 20
"""

df = pd.read_sql_query(query, conn)
print(df)
conn.close()
```

### 執行現成的分析腳本

```bash
python financial_analysis_demo.py
```

## 🧪 測試和驗證

### 測試爬蟲功能

```bash
python test_twse_financial_crawler.py
```

### 驗證資料完整性

```bash
python -c "
import sqlite3
conn = sqlite3.connect('history/tables/financial_statements.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM income_statements')
print(f'綜合損益表記錄數: {cursor.fetchone()[0]}')
cursor.execute('SELECT COUNT(*) FROM balance_sheets')
print(f'資產負債表記錄數: {cursor.fetchone()[0]}')
conn.close()
"
```

## 🔧 配置說明

### auto_update.py 配置

```python
update_tasks = [
    ('twse_financial_statements', crawl_twse_financial_statements, date_range),
    # 其他任務...
]
```

### finlab/crawler.py 新增函數

- `crawl_twse_financial_statements(date)`: 主要爬蟲函數
- `save_twse_financial_data(income_df, balance_df)`: 資料儲存函數

## 📊 實際資料範例

### 台積電 (2330) 財務數據

**綜合損益表**:
- 營業收入: 839,253,664 千元
- 營業毛利: 493,395,076 千元
- 本期淨利: 360,732,661 千元
- 毛利率: 58.8%
- 淨利率: 43.0%

**資產負債表**:
- 資產總額: 7,133,287,420 千元
- 負債總額: 2,531,662,963 千元
- 權益總額: 4,601,624,457 千元
- 負債比率: 35.5%
- ROA: 5.1%

## ⚠️ 注意事項

1. **網路連線**: 需要穩定的網路連線訪問 TWSE OpenAPI
2. **資料更新**: 財務報表資料通常季度更新
3. **API 限制**: 避免過於頻繁的請求
4. **資料格式**: 金額單位為千元新台幣
5. **SSL 警告**: 程式會跳過 SSL 驗證，這是正常的

## 🔄 與現有系統整合

### 現金流量表爬蟲並存

```python
update_tasks = [
    ('cash_flows', crawl_cash_flows, date_range),                    # MOPS 現金流量表
    ('twse_financial_statements', crawl_twse_financial_statements, date_range),  # TWSE 財務報表
]
```

### 資料來源比較

| 項目 | MOPS 爬蟲 | TWSE OpenAPI |
|------|-----------|--------------|
| 現金流量表 | ✅ | ❌ |
| 綜合損益表 | ✅ | ✅ |
| 資產負債表 | ✅ | ✅ |
| 穩定性 | 中等 | 高 |
| 更新頻率 | 季度 | 季度 |
| 資料完整性 | 高 | 高 |

## 🎉 成功案例

✅ **1008 家上市公司**: 完整覆蓋台股上市公司  
✅ **35 個損益項目**: 詳細的綜合損益表資料  
✅ **28 個資產負債項目**: 完整的資產負債表資料  
✅ **即時更新**: 與證交所同步更新  
✅ **高穩定性**: 基於官方 API，穩定可靠  

## 📞 支援

如有問題，請檢查：
1. 網路連線是否正常
2. 資料庫檔案是否正確生成
3. 執行測試腳本驗證功能
4. 查看錯誤訊息進行除錯

---

**🎯 現在你擁有了完整的台股財務報表爬蟲系統！**
