#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的股票篩選功能
"""

import sys
import pandas as pd
from datetime import datetime
from stock_signal_scanner import StockSignalScanner

def test_filtering_fix():
    """測試修復後的篩選功能"""
    print("🧪 測試修復後的股票篩選功能")
    print("="*50)
    
    # 創建掃描器
    scanner = StockSignalScanner()
    
    # 模擬不同的口訣數據 - 確保有不同的成功率和獲利率
    test_mantras = [
        {
            'mantra': '【RSI+MA+MACD】超賣反彈配金叉，MACD轉強三重確認買',
            'success_rate': 0.75,
            'avg_profit': 0.08,
            'strategy_code': 'RSI+MA+MACD'
        },
        {
            'mantra': '【RSI+MA+BB】超賣反彈配金叉，布林支撐三重確認買',
            'success_rate': 0.68,
            'avg_profit': 0.06,
            'strategy_code': 'RSI+MA+BB'
        },
        {
            'mantra': '【MACD+BB】MACD金叉配下軌反彈，進場信號強',
            'success_rate': 0.72,
            'avg_profit': 0.05,
            'strategy_code': 'MACD+BB'
        }
    ]
    
    print(f"📋 測試口訣數量: {len(test_mantras)}")
    for i, mantra in enumerate(test_mantras, 1):
        print(f"  {i}. 成功率: {mantra['success_rate']:.1%}, 獲利: {mantra['avg_profit']:.1%}")
        print(f"     {mantra['mantra'][:60]}...")
    
    # 測試掃描所有股票
    print(f"\n🔍 測試掃描所有股票...")
    all_signals = scanner.scan_all_stocks(test_mantras)
    
    print(f"✅ 掃描完成，發現 {len(all_signals)} 個信號")
    
    # 分析結果
    if all_signals:
        print(f"\n📊 信號分析:")
        
        # 按成功率分組
        success_rates = [signal.success_rate for signal in all_signals]
        avg_profits = [signal.avg_profit for signal in all_signals]
        
        print(f"  • 成功率範圍: {min(success_rates):.1%} - {max(success_rates):.1%}")
        print(f"  • 平均獲利範圍: {min(avg_profits):.1%} - {max(avg_profits):.1%}")
        
        # 檢查是否有重複值
        unique_success_rates = set(success_rates)
        unique_avg_profits = set(avg_profits)
        
        print(f"  • 不同成功率數量: {len(unique_success_rates)}")
        print(f"  • 不同獲利率數量: {len(unique_avg_profits)}")
        
        if len(unique_success_rates) <= 3:  # 應該有多種不同的成功率
            print("  ⚠️  警告: 成功率變化太少，可能仍有問題")
        else:
            print("  ✅ 成功率有足夠變化，篩選邏輯正常")
        
        if len(unique_avg_profits) <= 3:  # 應該有多種不同的獲利率
            print("  ⚠️  警告: 獲利率變化太少，可能仍有問題")
        else:
            print("  ✅ 獲利率有足夠變化，篩選邏輯正常")
        
        # 顯示前10個信號的詳細信息
        print(f"\n📋 前10個信號詳情:")
        for i, signal in enumerate(all_signals[:10], 1):
            print(f"  {i:2d}. {signal.stock_id} {signal.stock_name[:10]:<10} | "
                  f"成功率: {signal.success_rate:5.1%} | 獲利: {signal.avg_profit:5.1%} | "
                  f"強度: {signal.signal_strength:5.1%}")
        
        # 統計不同成功率的分佈
        print(f"\n📈 成功率分佈:")
        rate_counts = {}
        for rate in success_rates:
            rate_key = f"{rate:.1%}"
            rate_counts[rate_key] = rate_counts.get(rate_key, 0) + 1
        
        for rate, count in sorted(rate_counts.items()):
            print(f"  {rate}: {count} 個信號")
    
    else:
        print("❌ 沒有找到任何信號")

def main():
    """主函數"""
    try:
        test_filtering_fix()
        print(f"\n🎉 測試完成！")
        return 0
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
