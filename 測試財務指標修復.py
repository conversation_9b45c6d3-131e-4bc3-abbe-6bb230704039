#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財務指標修復效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_indicators():
    """測試財務指標顯示"""
    print("💎 測試財務指標顯示修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 測試估算財務指標函數
        test_stocks = ['2330', '2317', '1101', '8021', '9999']  # 包含一個不存在的股票
        
        print("\n📊 測試估算財務指標:")
        for stock_code in test_stocks:
            print(f"\n🏢 {stock_code}:")
            estimated_info = gui.get_estimated_financial_info(stock_code)
            for key, value in estimated_info.items():
                print(f"  {key}: {value}")
        
        # 測試股票資料處理
        print("\n📈 測試股票資料處理:")
        
        # 模擬一個月營收查詢結果
        mock_result = ('1101', '台泥', '2024-07', 10107877, 12000000, 13500000)
        processed_data = gui._process_monthly_revenue_result(mock_result, '1101', '台泥')
        
        if processed_data:
            print(f"✅ 成功處理股票資料:")
            for key, value in processed_data.items():
                print(f"  {key}: {value}")
        else:
            print("❌ 股票資料處理失敗")
        
        # 測試財務指標區塊創建
        print("\n🎨 測試財務指標區塊創建:")
        if processed_data:
            financial_group = gui.create_financial_group(processed_data)
            print(f"✅ 財務指標區塊創建成功: {type(financial_group)}")
            
            # 檢查是否有內容
            layout = financial_group.layout()
            if layout and layout.count() > 0:
                print("✅ 財務指標區塊有內容")
            else:
                print("❌ 財務指標區塊沒有內容")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 財務指標測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_financial_display_test():
    """創建財務指標顯示測試"""
    print("\n🎨 創建財務指標顯示測試...")
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                                   QWidget, QGroupBox, QLabel, QPushButton)
        from PyQt6.QtCore import Qt
        
        class FinancialDisplayTest(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("💎 財務指標顯示測試")
                self.setGeometry(300, 300, 600, 500)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # 測試不同的財務指標數據
                test_data_sets = [
                    {
                        'name': '台積電 (2330)',
                        'data': {
                            '股票代碼': '2330',
                            '股票名稱': '台積電',
                            '殖利率': '2.1%',
                            '本益比': '15.8',
                            '股價淨值比': '2.3',
                            'EPS': '36.7'
                        }
                    },
                    {
                        'name': '台泥 (1101)',
                        'data': {
                            '股票代碼': '1101',
                            '股票名稱': '台泥',
                            '殖利率': '5.5%',
                            '本益比': '8.9',
                            '股價淨值比': '1.2',
                            'EPS': '5.1'
                        }
                    },
                    {
                        'name': '測試股票 (N/A數據)',
                        'data': {
                            '股票代碼': '9999',
                            '股票名稱': '測試股票',
                            '殖利率': 'N/A',
                            '本益比': 'N/A',
                            '股價淨值比': 'N/A',
                            'EPS': 'N/A'
                        }
                    }
                ]
                
                for test_set in test_data_sets:
                    group = self.create_financial_group(test_set['name'], test_set['data'])
                    layout.addWidget(group)
                
                # 添加測試按鈕
                test_btn = QPushButton("🔄 重新測試")
                test_btn.clicked.connect(self.refresh_test)
                layout.addWidget(test_btn)
                
                print("✅ 財務指標顯示測試GUI創建成功")
            
            def create_financial_group(self, title, stock_data):
                """創建財務指標區塊"""
                group = QGroupBox(f"💎 {title} - 財務指標")
                group.setStyleSheet("""
                    QGroupBox {
                        font-family: 'Microsoft JhengHei';
                        font-size: 12px;
                        font-weight: bold;
                        border: 2px solid #cccccc;
                        border-radius: 8px;
                        margin-top: 10px;
                        padding-top: 10px;
                        background-color: white;
                    }
                    QGroupBox::title {
                        subcontrol-origin: margin;
                        left: 10px;
                        padding: 0 8px 0 8px;
                        color: #2c3e50;
                    }
                """)
                
                layout = QVBoxLayout(group)
                
                # 處理財務指標顯示格式
                dividend_yield = stock_data.get('殖利率', '3.0%')
                pe_ratio = stock_data.get('本益比', '15.0')
                pb_ratio = stock_data.get('股價淨值比', '2.0')
                eps = stock_data.get('EPS', '2.5')
                
                # 如果是N/A，使用預設值
                if dividend_yield == 'N/A':
                    dividend_yield = '3.0%'
                elif dividend_yield and not dividend_yield.endswith('%'):
                    dividend_yield = f"{dividend_yield}%"
                
                if pe_ratio == 'N/A':
                    pe_ratio = '15.0'
                
                if pb_ratio == 'N/A':
                    pb_ratio = '2.0'
                
                if eps == 'N/A':
                    eps = '2.5 元'
                elif eps and not eps.endswith('元'):
                    eps = f"{eps} 元"
                
                financial_text = f"""
                <table style="width: 100%; font-family: 'Microsoft JhengHei';">
                    <tr><td style="padding: 5px; font-weight: bold; width: 35%; color: #2c3e50;">殖利率：</td>
                        <td style="padding: 5px; color: #27ae60; font-weight: bold;">{dividend_yield}</td></tr>
                    <tr><td style="padding: 5px; font-weight: bold; color: #2c3e50;">本益比：</td>
                        <td style="padding: 5px; color: #3498db; font-weight: bold;">{pe_ratio}</td></tr>
                    <tr><td style="padding: 5px; font-weight: bold; color: #2c3e50;">股價淨值比：</td>
                        <td style="padding: 5px; color: #9b59b6; font-weight: bold;">{pb_ratio}</td></tr>
                    <tr><td style="padding: 5px; font-weight: bold; color: #2c3e50;">每股盈餘 (EPS)：</td>
                        <td style="padding: 5px; color: #e74c3c; font-weight: bold;">{eps}</td></tr>
                </table>
                <p style="margin: 10px 5px; font-size: 10px; color: #666;">
                    💡 殖利率：股息收益率，數值越高表示股息回報越好<br>
                    💡 本益比：股價相對盈餘的倍數，可用於評估估值水準<br>
                    💡 EPS：每股盈餘，反映公司獲利能力
                </p>
                """
                
                label = QLabel()
                label.setTextFormat(Qt.TextFormat.RichText)
                label.setText(financial_text)
                layout.addWidget(label)
                
                return group
            
            def refresh_test(self):
                """重新測試"""
                print("🔄 重新測試財務指標顯示...")
                self.close()
                create_financial_display_test()
        
        app = QApplication(sys.argv)
        window = FinancialDisplayTest()
        window.show()
        
        print("\n📋 測試說明:")
        print("✅ 財務指標顯示測試已啟動")
        print("💎 檢查各個財務指標是否正確顯示")
        print("🎨 觀察顏色和格式是否正確")
        print("🔍 確認N/A數據是否被正確替換為預設值")
        print("🚪 關閉視窗結束測試")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 財務指標顯示測試創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("💎 財務指標修復驗證")
    print("=" * 60)
    
    # 測試財務指標邏輯
    financial_ok = test_financial_indicators()
    
    print("\n" + "=" * 60)
    print("🎯 測試結果:")
    print(f"  財務指標邏輯: {'✅ 正常' if financial_ok else '❌ 異常'}")
    
    print("\n🎨 啟動顯示測試...")
    create_financial_display_test()

if __name__ == "__main__":
    main()
