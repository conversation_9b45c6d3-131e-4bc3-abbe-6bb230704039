#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab Tables 數據分析與移植工具
分析外部專案的pkl數據表格並移植到本專案
"""

import os
import pickle
import pandas as pd
import sqlite3
import logging
from datetime import datetime
from pathlib import Path
import shutil

class FinlabTablesAnalyzer:
    """Finlab數據表格分析器"""
    
    def __init__(self, finlab_tables_path=None):
        self.finlab_tables_path = finlab_tables_path or "D:/□Finlab學習/用 Python 理財：打造自己的 AI 股票理專_原始檔案/finlab_ml_course/finlab_ml_course/history/tables"
        # 統一使用與本專案其他數據庫相同的目錄
        self.local_data_path = "D:/Finlab/history/tables"
        self.db_path = "D:/Finlab/history/tables/finlab_financial_data.db"

        # 確保目錄存在
        os.makedirs(self.local_data_path, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建本地數據目錄
        os.makedirs(self.local_data_path, exist_ok=True)
        
        # 定義數據表格映射
        self.table_mappings = {
            'balance_sheet.pkl': {
                'name': '資產負債表',
                'description': '公司資產、負債和股東權益數據',
                'key_fields': ['總資產', '總負債', '股東權益', '流動資產', '流動負債'],
                'db_table': 'balance_sheet'
            },
            'income_sheet.pkl': {
                'name': '綜合損益表',
                'description': '公司營收、成本、費用和獲利數據',
                'key_fields': ['營業收入', '營業成本', '營業利益', '稅前淨利', '本期淨利'],
                'db_table': 'income_statement'
            },
            'income_sheet_cumulate.pkl': {
                'name': '累計損益表',
                'description': '累計至當季的損益數據',
                'key_fields': ['累計營收', '累計營業利益', '累計淨利'],
                'db_table': 'income_statement_cumulative'
            },
            'cash_flows.pkl': {
                'name': '現金流量表',
                'description': '營業、投資、融資活動現金流',
                'key_fields': ['營業現金流', '投資現金流', '融資現金流', '自由現金流'],
                'db_table': 'cash_flows'
            },
            'monthly_report.pkl': {
                'name': '月營收報告',
                'description': '每月營業收入數據',
                'key_fields': ['當月營收', '去年同月營收', '月增率', '年增率'],
                'db_table': 'monthly_revenue'
            },
            'pe.pkl': {
                'name': '本益比數據',
                'description': '股價本益比歷史數據',
                'key_fields': ['本益比', '股價', '每股盈餘'],
                'db_table': 'pe_ratio'
            },
            'twse_divide_ratio.pkl': {
                'name': '上市除權息',
                'description': '上市股票除權息數據',
                'key_fields': ['現金股利', '股票股利', '除息日', '除權日'],
                'db_table': 'twse_dividend'
            },
            'otc_divide_ratio.pkl': {
                'name': '上櫃除權息',
                'description': '上櫃股票除權息數據',
                'key_fields': ['現金股利', '股票股利', '除息日', '除權日'],
                'db_table': 'otc_dividend'
            },
            'bargin_report.pkl': {
                'name': '融資融券',
                'description': '融資融券餘額數據',
                'key_fields': ['融資餘額', '融券餘額', '融資增減', '融券增減'],
                'db_table': 'margin_trading'
            },
            'benchmark.pkl': {
                'name': '基準指數',
                'description': '大盤指數和基準數據',
                'key_fields': ['加權指數', '櫃買指數', '報酬率'],
                'db_table': 'benchmark'
            },
            'twse_cap_reduction.pkl': {
                'name': '上市減資',
                'description': '上市股票減資數據',
                'key_fields': ['減資比例', '減資後股本', '減資日期'],
                'db_table': 'twse_capital_reduction'
            },
            'otc_cap_reduction.pkl': {
                'name': '上櫃減資',
                'description': '上櫃股票減資數據',
                'key_fields': ['減資比例', '減資後股本', '減資日期'],
                'db_table': 'otc_capital_reduction'
            },
            'forign_hold_ratio.pkl': {
                'name': '外資持股比例',
                'description': '外資持股比例數據',
                'key_fields': ['外資持股比例', '外資持股張數', '變化量'],
                'db_table': 'foreign_holding'
            }
        }
    
    def analyze_pkl_file(self, pkl_file_path):
        """分析單個pkl檔案"""
        try:
            self.logger.info(f"📊 分析檔案: {pkl_file_path}")
            
            # 讀取pkl檔案
            with open(pkl_file_path, 'rb') as f:
                data = pickle.load(f)
            
            analysis_result = {
                'file_name': os.path.basename(pkl_file_path),
                'file_size': os.path.getsize(pkl_file_path),
                'data_type': type(data).__name__,
                'shape': None,
                'columns': None,
                'date_range': None,
                'stock_count': None,
                'sample_data': None
            }
            
            if isinstance(data, pd.DataFrame):
                analysis_result['shape'] = data.shape
                analysis_result['columns'] = list(data.columns) if hasattr(data, 'columns') else None
                
                # 分析日期範圍
                if hasattr(data, 'index') and len(data.index) > 0:
                    try:
                        if hasattr(data.index, 'min') and hasattr(data.index, 'max'):
                            analysis_result['date_range'] = {
                                'start': str(data.index.min()),
                                'end': str(data.index.max())
                            }
                    except:
                        pass
                
                # 股票數量
                if hasattr(data, 'columns'):
                    analysis_result['stock_count'] = len(data.columns)
                
                # 樣本數據
                try:
                    analysis_result['sample_data'] = data.head(3).to_dict() if len(data) > 0 else None
                except:
                    analysis_result['sample_data'] = "無法提取樣本數據"
            
            elif isinstance(data, dict):
                analysis_result['keys'] = list(data.keys())[:10]  # 只顯示前10個key
                analysis_result['total_keys'] = len(data.keys())
                
                # 嘗試分析字典內容
                if data:
                    first_key = list(data.keys())[0]
                    first_value = data[first_key]
                    analysis_result['value_type'] = type(first_value).__name__
                    
                    if isinstance(first_value, pd.DataFrame):
                        analysis_result['value_shape'] = first_value.shape
                        analysis_result['value_columns'] = list(first_value.columns)
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"❌ 分析檔案失敗 {pkl_file_path}: {e}")
            return {
                'file_name': os.path.basename(pkl_file_path),
                'error': str(e)
            }
    
    def analyze_all_tables(self):
        """分析所有數據表格"""
        self.logger.info("🔍 開始分析所有Finlab數據表格")
        
        if not os.path.exists(self.finlab_tables_path):
            self.logger.error(f"❌ Finlab tables路徑不存在: {self.finlab_tables_path}")
            return {}
        
        analysis_results = {}
        
        # 分析所有pkl檔案
        for file_name in os.listdir(self.finlab_tables_path):
            if file_name.endswith('.pkl'):
                file_path = os.path.join(self.finlab_tables_path, file_name)
                analysis_results[file_name] = self.analyze_pkl_file(file_path)
        
        return analysis_results
    
    def convert_pkl_to_sqlite(self, pkl_file_path, table_name):
        """將pkl檔案轉換為SQLite數據庫"""
        try:
            self.logger.info(f"🔄 轉換 {pkl_file_path} 到 SQLite")
            
            # 讀取pkl檔案
            with open(pkl_file_path, 'rb') as f:
                data = pickle.load(f)
            
            # 創建數據庫連接
            conn = sqlite3.connect(self.db_path)
            
            if isinstance(data, pd.DataFrame):
                # 直接保存DataFrame
                data.to_sql(table_name, conn, if_exists='replace', index=True)
                self.logger.info(f"✅ DataFrame保存成功: {data.shape}")
                
            elif isinstance(data, dict):
                # 處理字典格式數據
                if data:
                    # 嘗試將字典轉換為DataFrame
                    try:
                        df = pd.DataFrame(data)
                        df.to_sql(table_name, conn, if_exists='replace', index=True)
                        self.logger.info(f"✅ 字典轉DataFrame保存成功: {df.shape}")
                    except:
                        # 如果無法直接轉換，保存為JSON格式
                        import json
                        json_data = pd.DataFrame([{'data': json.dumps(data, default=str)}])
                        json_data.to_sql(f"{table_name}_json", conn, if_exists='replace', index=False)
                        self.logger.info(f"✅ 字典保存為JSON格式")
            
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 轉換失敗 {pkl_file_path}: {e}")
            return False
    
    def migrate_all_tables(self):
        """移植所有數據表格"""
        self.logger.info("🚀 開始移植所有Finlab數據表格")
        
        if not os.path.exists(self.finlab_tables_path):
            self.logger.error(f"❌ Finlab tables路徑不存在: {self.finlab_tables_path}")
            return False
        
        success_count = 0
        total_count = 0
        
        for file_name, table_info in self.table_mappings.items():
            file_path = os.path.join(self.finlab_tables_path, file_name)
            
            if os.path.exists(file_path):
                total_count += 1
                self.logger.info(f"📊 處理: {table_info['name']} ({file_name})")
                
                if self.convert_pkl_to_sqlite(file_path, table_info['db_table']):
                    success_count += 1
                    
                    # 複製原始pkl檔案到本地
                    local_pkl_path = os.path.join(self.local_data_path, file_name)
                    shutil.copy2(file_path, local_pkl_path)
                    self.logger.info(f"📁 複製原始檔案到: {local_pkl_path}")
            else:
                self.logger.warning(f"⚠️ 檔案不存在: {file_path}")
        
        self.logger.info(f"✅ 移植完成: {success_count}/{total_count} 個表格成功")
        return success_count > 0
    
    def create_data_summary(self):
        """創建數據摘要報告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取所有表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            summary = {
                'database_path': self.db_path,
                'total_tables': len(tables),
                'tables': {}
            }
            
            for (table_name,) in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    summary['tables'][table_name] = {
                        'row_count': row_count,
                        'column_count': len(columns),
                        'columns': [col[1] for col in columns]
                    }
                except Exception as e:
                    summary['tables'][table_name] = {'error': str(e)}
            
            conn.close()
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ 創建摘要失敗: {e}")
            return {}
    
    def generate_analysis_report(self):
        """生成完整分析報告"""
        self.logger.info("📋 生成Finlab數據分析報告")
        
        # 分析原始數據
        analysis_results = self.analyze_all_tables()
        
        # 創建報告
        report = f"""# 📊 Finlab數據表格分析報告

## 🎯 分析概述
- **分析時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **數據源路徑**: {self.finlab_tables_path}
- **本地數據路徑**: {self.local_data_path}
- **發現表格數**: {len(analysis_results)}

## 📋 數據表格詳細分析

"""
        
        for file_name, analysis in analysis_results.items():
            table_info = self.table_mappings.get(file_name, {})
            
            report += f"""### 📊 {table_info.get('name', file_name)}

**檔案**: `{file_name}`
**描述**: {table_info.get('description', '未知')}
**檔案大小**: {analysis.get('file_size', 0):,} bytes
**數據類型**: {analysis.get('data_type', '未知')}
"""
            
            if 'shape' in analysis and analysis['shape']:
                report += f"**數據維度**: {analysis['shape'][0]:,} 行 × {analysis['shape'][1]:,} 列\n"
            
            if 'stock_count' in analysis and analysis['stock_count']:
                report += f"**股票數量**: {analysis['stock_count']:,} 支\n"
            
            if 'date_range' in analysis and analysis['date_range']:
                report += f"**日期範圍**: {analysis['date_range']['start']} ~ {analysis['date_range']['end']}\n"
            
            if 'columns' in analysis and analysis['columns']:
                report += f"**主要欄位**: {', '.join(analysis['columns'][:5])}{'...' if len(analysis['columns']) > 5 else ''}\n"
            
            report += "\n"
        
        # 保存報告
        report_path = os.path.join(self.local_data_path, "finlab_analysis_report.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"📄 分析報告已保存: {report_path}")
        return report_path

def main():
    """主函數"""
    print("🔍 Finlab數據表格分析與移植工具")
    print("=" * 60)
    
    # 創建分析器
    analyzer = FinlabTablesAnalyzer()
    
    try:
        # 步驟1: 分析數據表格
        print("\n📊 步驟1: 分析Finlab數據表格")
        analysis_results = analyzer.analyze_all_tables()
        
        if analysis_results:
            print(f"✅ 發現 {len(analysis_results)} 個數據表格")
            
            # 步驟2: 移植數據表格
            print("\n🚀 步驟2: 移植數據表格到本地")
            if analyzer.migrate_all_tables():
                print("✅ 數據移植成功")
                
                # 步驟3: 創建摘要報告
                print("\n📋 步驟3: 創建數據摘要")
                summary = analyzer.create_data_summary()
                if summary:
                    print(f"✅ 數據庫包含 {summary['total_tables']} 個表格")
                
                # 步驟4: 生成分析報告
                print("\n📄 步驟4: 生成分析報告")
                report_path = analyzer.generate_analysis_report()
                print(f"✅ 報告已生成: {report_path}")
                
                print("\n🎉 所有步驟完成！")
                print(f"📁 本地數據路徑: {analyzer.local_data_path}")
                print(f"🗄️ 數據庫路徑: {analyzer.db_path}")
                
            else:
                print("❌ 數據移植失敗")
        else:
            print("❌ 未發現任何數據表格")
            
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        return False
    
    return True

class FinlabDataCrawler:
    """Finlab數據爬取器 - 自動更新財務數據"""

    def __init__(self, db_path="D:/Finlab/history/tables/finlab_financial_data.db"):
        self.db_path = db_path

        # 確保數據庫目錄存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.logger = logging.getLogger(__name__)

        # 數據源URL映射
        self.data_sources = {
            'balance_sheet': {
                'url': 'https://api.finlab.tw/v1/data/balance_sheet',
                'description': '資產負債表'
            },
            'income_statement': {
                'url': 'https://api.finlab.tw/v1/data/income_statement',
                'description': '綜合損益表'
            },
            'cash_flows': {
                'url': 'https://api.finlab.tw/v1/data/cash_flows',
                'description': '現金流量表'
            },
            'monthly_revenue': {
                'url': 'https://api.finlab.tw/v1/data/monthly_revenue',
                'description': '月營收'
            },
            'pe_ratio': {
                'url': 'https://api.finlab.tw/v1/data/pe_ratio',
                'description': '本益比'
            },
            'dividend': {
                'url': 'https://api.finlab.tw/v1/data/dividend',
                'description': '股利發放'
            },
            'margin_trading': {
                'url': 'https://api.finlab.tw/v1/data/margin_trading',
                'description': '融資融券'
            }
        }

    def fetch_data_from_api(self, data_type, start_date=None, end_date=None):
        """從API獲取數據"""
        try:
            import requests

            if data_type not in self.data_sources:
                raise ValueError(f"不支援的數據類型: {data_type}")

            url = self.data_sources[data_type]['url']
            params = {}

            if start_date:
                params['start_date'] = start_date
            if end_date:
                params['end_date'] = end_date

            self.logger.info(f"🌐 從API獲取數據: {data_type}")

            # 這裡需要實際的API金鑰和認證
            headers = {
                'Authorization': 'Bearer YOUR_API_KEY',
                'Content-Type': 'application/json'
            }

            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()

            data = response.json()

            # 轉換為DataFrame
            if isinstance(data, dict) and 'data' in data:
                df = pd.DataFrame(data['data'])
            else:
                df = pd.DataFrame(data)

            self.logger.info(f"✅ 獲取成功: {df.shape}")
            return df

        except Exception as e:
            self.logger.error(f"❌ API獲取失敗 {data_type}: {e}")
            return None

    def update_database(self, data_type, df):
        """更新數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 檢查表格是否存在
            cursor = conn.cursor()
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{data_type}'")
            table_exists = cursor.fetchone() is not None

            if table_exists:
                # 增量更新策略
                self.logger.info(f"🔄 增量更新表格: {data_type}")

                # 獲取最新日期
                cursor.execute(f"SELECT MAX(date) FROM {data_type} WHERE date IS NOT NULL")
                last_date = cursor.fetchone()[0]

                if last_date:
                    # 只插入新數據
                    new_data = df[df.index > last_date] if hasattr(df, 'index') else df
                    if len(new_data) > 0:
                        new_data.to_sql(data_type, conn, if_exists='append', index=True)
                        self.logger.info(f"✅ 新增 {len(new_data)} 筆記錄")
                    else:
                        self.logger.info("ℹ️ 沒有新數據需要更新")
                else:
                    # 如果沒有日期信息，直接替換
                    df.to_sql(data_type, conn, if_exists='replace', index=True)
                    self.logger.info(f"✅ 替換表格數據: {len(df)} 筆記錄")
            else:
                # 創建新表格
                df.to_sql(data_type, conn, if_exists='replace', index=True)
                self.logger.info(f"✅ 創建新表格: {data_type} ({len(df)} 筆記錄)")

            conn.close()
            return True

        except Exception as e:
            self.logger.error(f"❌ 數據庫更新失敗 {data_type}: {e}")
            return False

    def crawl_all_data(self, start_date=None, end_date=None):
        """爬取所有數據"""
        self.logger.info("🚀 開始爬取所有財務數據")

        success_count = 0
        total_count = len(self.data_sources)

        for data_type, info in self.data_sources.items():
            self.logger.info(f"📊 處理: {info['description']} ({data_type})")

            # 從API獲取數據
            df = self.fetch_data_from_api(data_type, start_date, end_date)

            if df is not None and len(df) > 0:
                # 更新數據庫
                if self.update_database(data_type, df):
                    success_count += 1
            else:
                self.logger.warning(f"⚠️ 跳過空數據: {data_type}")

        self.logger.info(f"✅ 爬取完成: {success_count}/{total_count} 個數據源成功")
        return success_count > 0

    def schedule_daily_update(self):
        """排程每日更新"""
        import schedule
        import time

        def daily_job():
            self.logger.info("⏰ 執行每日數據更新")
            self.crawl_all_data()

        # 每天早上8點更新
        schedule.every().day.at("08:00").do(daily_job)

        self.logger.info("⏰ 已設置每日更新排程 (08:00)")

        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分鐘檢查一次

if __name__ == "__main__":
    main()
