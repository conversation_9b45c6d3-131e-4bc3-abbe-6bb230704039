@echo off
chcp 65001 >nul
title 台股智能選股系統 - 啟動器

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo ========================================
echo.
echo 正在啟動程式，請稍候...
echo.

REM 檢查可執行檔是否存在
if exist "dist\台股智能選股系統.exe" (
    echo ✅ 找到主程式檔案
    echo 🔄 正在啟動...
    echo.
    
    REM 切換到 dist 目錄並執行程式
    cd /d "dist"
    start "" "台股智能選股系統.exe"
    
    echo ✅ 程式已啟動！
    echo.
    echo 💡 提示：
    echo    - 首次運行可能需要較長時間初始化
    echo    - 請確保網路連接正常
    echo    - 如有問題請查看日誌檔案
    echo.
    
) else (
    echo ❌ 錯誤：找不到主程式檔案
    echo.
    echo 請確認以下檔案存在：
    echo    dist\台股智能選股系統.exe
    echo.
    echo 如果檔案不存在，請重新執行編譯腳本：
    echo    python compile_to_exe.py
    echo.
)

echo 按任意鍵關閉此視窗...
pause >nul
