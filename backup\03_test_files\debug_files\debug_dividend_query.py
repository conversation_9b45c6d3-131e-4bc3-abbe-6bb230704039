#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
調試除權息查詢問題
"""

import sqlite3
import os

def debug_dividend_query():
    """調試除權息查詢"""
    print("🔍 調試除權息查詢問題...")
    
    try:
        dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
        
        if not os.path.exists(dividend_db_path):
            print(f"❌ 除權息資料庫不存在: {dividend_db_path}")
            return
        
        conn = sqlite3.connect(dividend_db_path)
        cursor = conn.cursor()
        
        # 使用與GUI相同的查詢邏輯
        start_date = "2025-07-15"
        end_date = "2025-07-22"
        
        print(f"📅 查詢期間: {start_date} 到 {end_date}")
        
        # 完全相同的查詢語句
        query = """
            SELECT stock_code, stock_name, year, ex_dividend_date,
                   MAX(cash_dividend) as cash_dividend,
                   MAX(stock_dividend) as stock_dividend,
                   MAX(total_dividend) as total_dividend,
                   MAX(dividend_yield) as dividend_yield,
                   MAX(eps) as eps,
                   MAX(data_source) as data_source
            FROM dividend_data
            WHERE ex_dividend_date IS NOT NULL
            AND ex_dividend_date != ''
            AND ex_dividend_date >= ?
            AND ex_dividend_date <= ?
            AND (data_source IS NULL OR data_source != 'sample_data')
            GROUP BY stock_code, ex_dividend_date
            ORDER BY ex_dividend_date, stock_code
        """
        
        cursor.execute(query, (start_date, end_date))
        results = cursor.fetchall()
        
        print(f"📊 查詢結果: 找到 {len(results)} 筆記錄")
        
        if results:
            print("\n📋 前10筆查詢結果:")
            print("股票代碼 | 股票名稱 | 年份 | 除權息日 | 現金股利 | 資料來源")
            print("-" * 80)
            
            # 轉換為字典格式（與GUI相同）
            records = []
            for row in results[:10]:
                record = {
                    'stock_code': row[0],
                    'stock_name': row[1] or '',
                    'year': row[2],
                    'ex_dividend_date': row[3],
                    'cash_dividend': float(row[4]) if row[4] else 0.0,
                    'stock_dividend': float(row[5]) if row[5] else 0.0,
                    'total_dividend': float(row[6]) if row[6] else 0.0,
                    'dividend_yield': float(row[7]) if row[7] else 0.0,
                    'eps': float(row[8]) if row[8] else 0.0,
                    'data_source': row[9] or ''
                }
                records.append(record)
                
                # 顯示處理後的資料
                print(f"{record['stock_code']:<8} | {record['stock_name']:<10} | {record['year']} | {record['ex_dividend_date']} | {record['cash_dividend']:.2f} | {record['data_source']}")
            
            # 檢查年份分布
            print(f"\n📊 年份分布統計:")
            year_counts = {}
            for row in results:
                year = row[2]
                year_counts[year] = year_counts.get(year, 0) + 1
            
            for year in sorted(year_counts.keys()):
                count = year_counts[year]
                percentage = (count / len(results)) * 100
                print(f"  {year}年: {count} 筆 ({percentage:.1f}%)")
                
        else:
            print("❌ 沒有找到符合條件的資料")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

def check_sample_data():
    """檢查是否有sample_data干擾"""
    print("\n🔍 檢查sample_data...")
    
    try:
        dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
        conn = sqlite3.connect(dividend_db_path)
        cursor = conn.cursor()
        
        # 檢查sample_data
        cursor.execute("""
            SELECT COUNT(*) FROM dividend_data 
            WHERE data_source = 'sample_data'
        """)
        sample_count = cursor.fetchone()[0]
        print(f"📊 sample_data記錄數: {sample_count}")
        
        if sample_count > 0:
            cursor.execute("""
                SELECT stock_code, stock_name, year, ex_dividend_date, cash_dividend
                FROM dividend_data 
                WHERE data_source = 'sample_data'
                AND ex_dividend_date >= '2025-07-15'
                AND ex_dividend_date <= '2025-07-22'
                LIMIT 10
            """)
            sample_results = cursor.fetchall()
            
            if sample_results:
                print("⚠️ 發現sample_data在查詢範圍內:")
                for row in sample_results:
                    print(f"  {row[0]} {row[1]} {row[2]} {row[3]} {row[4]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查sample_data失敗: {e}")

def main():
    """主函數"""
    print("🚀 除權息查詢調試")
    print("=" * 50)
    
    debug_dividend_query()
    check_sample_data()

if __name__ == "__main__":
    main()
