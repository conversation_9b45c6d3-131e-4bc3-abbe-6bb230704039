#!/usr/bin/env python3
"""
測試多策略整合分析系統信息面板改進
驗證策略說明和使用說明的重新組織
"""

import sys
import os
from datetime import datetime

def test_info_panel_structure():
    """測試信息面板結構"""
    print("🧪 測試信息面板結構改進")
    print("=" * 60)
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        # 創建GUI實例
        gui = MultiStrategyGUI()
        
        # 檢查信息面板是否存在
        if hasattr(gui, 'create_info_panel'):
            print("✅ 信息面板創建方法存在")
        else:
            print("❌ 信息面板創建方法不存在")
            return False
        
        # 檢查策略說明方法
        if hasattr(gui, 'get_strategy_info_html'):
            print("✅ 策略說明HTML方法存在")
        else:
            print("❌ 策略說明HTML方法不存在")
            return False
        
        # 檢查使用說明方法
        if hasattr(gui, 'get_usage_info_html'):
            print("✅ 使用說明HTML方法存在")
        else:
            print("❌ 使用說明HTML方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試信息面板結構失敗: {e}")
        return False

def test_strategy_info_content():
    """測試策略說明內容"""
    print("\n📚 測試策略說明內容")
    print("-" * 40)
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        gui = MultiStrategyGUI()
        strategy_html = gui.get_strategy_info_html()
        
        # 檢查必要的策略內容
        required_strategies = [
            "RSI策略",
            "MACD策略", 
            "布林通道策略",
            "移動平均交叉策略"
        ]
        
        required_elements = [
            "定義",
            "參數",
            "買入信號",
            "賣出信號", 
            "適用場景",
            "策略精神"
        ]
        
        print("   檢查策略覆蓋:")
        for strategy in required_strategies:
            if strategy in strategy_html:
                print(f"     ✅ {strategy}")
            else:
                print(f"     ❌ {strategy}")
                return False
        
        print("   檢查內容要素:")
        for element in required_elements:
            if element in strategy_html:
                print(f"     ✅ {element}")
            else:
                print(f"     ❌ {element}")
                return False
        
        # 檢查時間框架說明
        time_frames = ["短期", "中期", "長期"]
        found_timeframes = 0
        for tf in time_frames:
            if tf in strategy_html:
                found_timeframes += 1
        
        if found_timeframes >= 2:
            print(f"     ✅ 時間框架說明 ({found_timeframes}/3)")
        else:
            print(f"     ⚠️ 時間框架說明不足 ({found_timeframes}/3)")
        
        # 檢查HTML格式
        if "<h3>" in strategy_html and "<h4>" in strategy_html and "<ul>" in strategy_html:
            print("     ✅ HTML格式正確")
        else:
            print("     ❌ HTML格式不正確")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試策略說明內容失敗: {e}")
        return False

def test_usage_info_content():
    """測試使用說明內容"""
    print("\n📖 測試使用說明內容")
    print("-" * 40)
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        gui = MultiStrategyGUI()
        usage_html = gui.get_usage_info_html()
        
        # 檢查必要的使用說明內容
        required_sections = [
            "支援策略標記",
            "圖表說明",
            "操作流程",
            "使用技巧",
            "信號解讀",
            "注意事項",
            "功能說明"
        ]
        
        print("   檢查說明章節:")
        for section in required_sections:
            if section in usage_html:
                print(f"     ✅ {section}")
            else:
                print(f"     ❌ {section}")
                return False
        
        # 檢查操作流程步驟
        operation_steps = [
            "選擇股票",
            "開始分析", 
            "查看圖表",
            "策略過濾",
            "一致性分析",
            "導出結果"
        ]
        
        print("   檢查操作步驟:")
        found_steps = 0
        for step in operation_steps:
            if step in usage_html:
                found_steps += 1
                print(f"     ✅ {step}")
            else:
                print(f"     ❌ {step}")
        
        if found_steps >= 4:
            print(f"     ✅ 操作流程完整 ({found_steps}/6)")
        else:
            print(f"     ❌ 操作流程不完整 ({found_steps}/6)")
            return False
        
        # 檢查信號說明
        signal_types = ["買入信號", "賣出信號", "信號強度", "多重確認"]
        found_signals = 0
        for signal in signal_types:
            if signal in usage_html:
                found_signals += 1
        
        if found_signals >= 3:
            print(f"     ✅ 信號解讀說明 ({found_signals}/4)")
        else:
            print(f"     ❌ 信號解讀說明不足 ({found_signals}/4)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試使用說明內容失敗: {e}")
        return False

def test_content_organization():
    """測試內容組織邏輯"""
    print("\n🔄 測試內容組織邏輯")
    print("-" * 40)
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        gui = MultiStrategyGUI()
        strategy_html = gui.get_strategy_info_html()
        usage_html = gui.get_usage_info_html()
        
        # 檢查內容分離
        print("   檢查內容分離:")
        
        # 策略說明應該包含技術內容
        technical_keywords = ["定義", "參數", "週期", "指標", "EMA", "標準差"]
        strategy_technical_count = sum(1 for kw in technical_keywords if kw in strategy_html)
        
        if strategy_technical_count >= 4:
            print(f"     ✅ 策略說明包含技術內容 ({strategy_technical_count}/6)")
        else:
            print(f"     ❌ 策略說明技術內容不足 ({strategy_technical_count}/6)")
            return False
        
        # 使用說明應該包含操作內容
        operation_keywords = ["操作", "點擊", "選擇", "查看", "勾選", "導出"]
        usage_operation_count = sum(1 for kw in operation_keywords if kw in usage_html)
        
        if usage_operation_count >= 4:
            print(f"     ✅ 使用說明包含操作內容 ({usage_operation_count}/6)")
        else:
            print(f"     ❌ 使用說明操作內容不足 ({usage_operation_count}/6)")
            return False
        
        # 檢查重複內容
        print("   檢查內容重複:")
        
        # 圖表標記說明應該在使用說明中
        if "三角形標記" in usage_html and "三角形標記" not in strategy_html:
            print("     ✅ 圖表標記說明正確分配到使用說明")
        else:
            print("     ⚠️ 圖表標記說明分配可能有問題")
        
        # 技術參數應該在策略說明中
        if "週期14天" in strategy_html and "週期14天" not in usage_html:
            print("     ✅ 技術參數正確分配到策略說明")
        else:
            print("     ⚠️ 技術參數分配可能有問題")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試內容組織邏輯失敗: {e}")
        return False

def test_html_formatting():
    """測試HTML格式化"""
    print("\n🎨 測試HTML格式化")
    print("-" * 40)
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        gui = MultiStrategyGUI()
        strategy_html = gui.get_strategy_info_html()
        usage_html = gui.get_usage_info_html()
        
        # 檢查HTML標籤
        html_tags = ["<h3>", "<h4>", "<ul>", "<li>", "<b>", "<span>", "<ol>"]
        
        print("   檢查策略說明HTML:")
        for tag in html_tags:
            if tag in strategy_html:
                print(f"     ✅ {tag}")
            else:
                print(f"     ❌ {tag}")
        
        print("   檢查使用說明HTML:")
        for tag in html_tags:
            if tag in usage_html:
                print(f"     ✅ {tag}")
            else:
                print(f"     ❌ {tag}")
        
        # 檢查顏色標記
        color_styles = ["color: #e74c3c", "color: #f39c12", "color: #27ae60"]
        found_colors = sum(1 for color in color_styles if color in strategy_html)
        
        if found_colors >= 2:
            print(f"     ✅ 顏色標記 ({found_colors}/3)")
        else:
            print(f"     ⚠️ 顏色標記不足 ({found_colors}/3)")
        
        # 檢查表情符號
        emojis = ["🔴", "🔵", "🟡", "🟢", "📊", "📖", "💡", "⚠️"]
        strategy_emoji_count = sum(1 for emoji in emojis if emoji in strategy_html)
        usage_emoji_count = sum(1 for emoji in emojis if emoji in usage_html)
        
        if strategy_emoji_count >= 4:
            print(f"     ✅ 策略說明表情符號 ({strategy_emoji_count}/8)")
        else:
            print(f"     ⚠️ 策略說明表情符號不足 ({strategy_emoji_count}/8)")
        
        if usage_emoji_count >= 4:
            print(f"     ✅ 使用說明表情符號 ({usage_emoji_count}/8)")
        else:
            print(f"     ⚠️ 使用說明表情符號不足 ({usage_emoji_count}/8)")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試HTML格式化失敗: {e}")
        return False

def generate_info_panel_report():
    """生成信息面板改進報告"""
    print("\n📊 生成信息面板改進報告")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"info_panel_improvements_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("多策略整合分析系統 - 信息面板改進報告\n")
            f.write("=" * 60 + "\n")
            f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("🎯 改進目標:\n")
            f.write("• 將原'策略說明'改名為'使用說明'\n")
            f.write("• 新增真正的'策略說明'，包含技術指標詳解\n")
            f.write("• 明確區分技術內容與操作說明\n")
            f.write("• 提供完整的策略參數和適用場景說明\n\n")
            
            f.write("✅ 實現的改進:\n\n")
            
            f.write("1. 信息面板重新組織:\n")
            f.write("   📊 信號統計 - 各策略信號統計表格\n")
            f.write("   🎯 一致性分析 - 多策略一致性分析\n")
            f.write("   📚 策略說明 - 技術指標詳細說明 (新增)\n")
            f.write("   📖 使用說明 - 操作流程和使用技巧 (重新命名)\n\n")
            
            f.write("2. 策略說明內容 (📚 策略說明):\n")
            f.write("   🔴 RSI策略:\n")
            f.write("     • 定義: 相對強弱指標，衡量股價漲跌幅度\n")
            f.write("     • 參數: 週期14天，超買線70，超賣線30\n")
            f.write("     • 適用場景: 短期交易(1-5天)，震盪行情\n")
            f.write("     • 策略精神: 逆勢操作，超賣買入，超買賣出\n\n")
            
            f.write("   🔵 MACD策略:\n")
            f.write("     • 定義: 指數平滑移動平均線組合\n")
            f.write("     • 參數: 快線12、慢線26、信號線9\n")
            f.write("     • 適用場景: 中期趨勢(5-20天)，趨勢行情\n")
            f.write("     • 策略精神: 順勢操作，捕捉趨勢轉折\n\n")
            
            f.write("   🟡 布林通道策略:\n")
            f.write("     • 定義: 中軌(20日MA)±2倍標準差通道\n")
            f.write("     • 參數: 週期20天，標準差倍數2\n")
            f.write("     • 適用場景: 短期交易(2-10天)，震盪行情\n")
            f.write("     • 策略精神: 均值回歸，價格偏離後回歸\n\n")
            
            f.write("   🟢 移動平均交叉策略:\n")
            f.write("     • 定義: 短期與長期移動平均線交叉\n")
            f.write("     • 參數: 短期5日MA，長期20日MA\n")
            f.write("     • 適用場景: 長期投資(20-60天)，趨勢行情\n")
            f.write("     • 策略精神: 趨勢跟隨，順應主要方向\n\n")
            
            f.write("3. 使用說明內容 (📖 使用說明):\n")
            f.write("   🚀 操作流程:\n")
            f.write("     1. 選擇股票 - 從下拉選單選擇\n")
            f.write("     2. 開始分析 - 點擊分析按鈕\n")
            f.write("     3. 查看圖表 - 價格圖+信號統計圖\n")
            f.write("     4. 策略過濾 - 勾選/取消策略顯示\n")
            f.write("     5. 一致性分析 - 調整窗口參數\n")
            f.write("     6. 導出結果 - 保存分析結果\n\n")
            
            f.write("   🎨 圖表說明:\n")
            f.write("     • 🔴 RSI: 三角形標記，紅色系\n")
            f.write("     • 🔵 MACD: 方形標記，藍色系\n")
            f.write("     • 🟡 布林通道: 圓形標記，黃色系\n")
            f.write("     • 🟢 移動平均: 菱形標記，綠色系\n\n")
            
            f.write("   💡 使用技巧:\n")
            f.write("     • 多策略確認提高可信度\n")
            f.write("     • 關注高強度信號標註\n")
            f.write("     • 結合一致性分析找最佳點\n")
            f.write("     • 使用策略過濾功能\n\n")
            
            f.write("4. 內容組織邏輯:\n")
            f.write("   📚 策略說明 = 技術內容\n")
            f.write("     • 指標定義和計算方法\n")
            f.write("     • 參數設定和數值範圍\n")
            f.write("     • 買賣信號判斷條件\n")
            f.write("     • 適用場景和時間框架\n")
            f.write("     • 策略核心理念和精神\n\n")
            
            f.write("   📖 使用說明 = 操作內容\n")
            f.write("     • 軟體操作步驟流程\n")
            f.write("     • 圖表標記和視覺說明\n")
            f.write("     • 功能使用技巧和方法\n")
            f.write("     • 信號解讀和判斷方式\n")
            f.write("     • 注意事項和風險提醒\n\n")
            
            f.write("🎨 視覺改進:\n")
            f.write("• 使用顏色標記區分時間框架\n")
            f.write("• 表情符號增強視覺識別\n")
            f.write("• HTML格式化提升閱讀體驗\n")
            f.write("• 分層標題結構化內容\n")
            f.write("• 列表格式便於快速查找\n\n")
            
            f.write("💡 用戶體驗提升:\n")
            f.write("• 技術學習: 策略說明提供深度技術知識\n")
            f.write("• 操作指導: 使用說明提供實用操作指南\n")
            f.write("• 快速查找: 分頁設計便於快速定位信息\n")
            f.write("• 完整覆蓋: 從理論到實踐的完整指導\n")
            f.write("• 專業呈現: 結構化內容提升專業感\n")
        
        print(f"✅ 信息面板改進報告已生成: {report_filename}")
        return report_filename
        
    except Exception as e:
        print(f"❌ 生成改進報告失敗: {e}")
        return None

def main():
    """主函數"""
    print("🚀 多策略整合分析系統 - 信息面板改進測試")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("信息面板結構測試", test_info_panel_structure),
        ("策略說明內容測試", test_strategy_info_content),
        ("使用說明內容測試", test_usage_info_content),
        ("內容組織邏輯測試", test_content_organization),
        ("HTML格式化測試", test_html_formatting)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 生成報告
    report_file = generate_info_panel_report()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！信息面板改進成功")
        print("\n💡 現在信息面板具備:")
        print("   📚 策略說明 - 完整的技術指標詳解")
        print("   📖 使用說明 - 實用的操作指導")
        print("   🎯 內容分離 - 技術與操作內容明確區分")
        print("   🎨 視覺優化 - 顏色標記和表情符號")
        print("   📊 結構化 - 分層標題和列表格式")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    if report_file:
        print(f"\n📊 詳細報告已保存至: {report_file}")

if __name__ == '__main__':
    main()
