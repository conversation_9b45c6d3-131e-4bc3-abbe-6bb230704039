#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試市場數據爬蟲功能
"""

import sys
from PyQt6.QtWidgets import QApplication

def test_market_crawler():
    """測試市場數據爬蟲"""
    try:
        # 測試導入
        from market_data_crawler_dialog import MarketDataCrawlerDialog
        print("✅ 市場數據爬蟲模組導入成功")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建對話框
        dialog = MarketDataCrawlerDialog()
        print("✅ 對話框創建成功")
        
        # 顯示對話框
        dialog.show()
        print("✅ 對話框顯示成功")
        print("🎉 測試完成！可以手動測試爬蟲功能")
        
        # 運行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_market_crawler()
