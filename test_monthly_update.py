#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 auto_update.py 是否能正確更新 monthly_report.pkl
"""

import sys
import os
import datetime
import pandas as pd
import shutil

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 導入 auto_update 函數
from auto_update import (
    crawl_monthly_report_fixed,
    auto_update,
    table_date_range,
    month_range,
    to_pickle
)

def backup_monthly_report():
    """備份現有的 monthly_report.pkl"""
    original_file = "history/tables/monthly_report.pkl"
    backup_file = f"history/tables/monthly_report_backup_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已備份: {backup_file}")
        return backup_file
    else:
        print(f"⚠️ 原始檔案不存在: {original_file}")
        return None

def check_monthly_report_status():
    """檢查 monthly_report.pkl 的狀態"""
    file_path = "history/tables/monthly_report.pkl"
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return None
    
    try:
        df = pd.read_pickle(file_path)
        print(f"📊 monthly_report.pkl 狀態:")
        print(f"   總筆數: {len(df):,}")
        
        dates = df.index.get_level_values('date')
        print(f"   日期範圍: {dates.min()} 至 {dates.max()}")
        
        # 檢查最新資料
        latest_date = dates.max()
        print(f"   最新資料: {latest_date}")
        
        # 計算需要更新的月數
        today = datetime.datetime.now()
        months_behind = (today.year - latest_date.year) * 12 + (today.month - latest_date.month)
        print(f"   落後月數: {months_behind} 個月")
        
        return df, latest_date, months_behind
        
    except Exception as e:
        print(f"❌ 讀取檔案失敗: {str(e)}")
        return None

def test_single_month_update():
    """測試單月更新"""
    print(f"\n🧪 測試單月月營收更新")
    print("=" * 50)
    
    # 測試 2024年11月
    test_date = datetime.date(2024, 11, 1)
    print(f"📅 測試日期: {test_date}")
    
    try:
        result = crawl_monthly_report_fixed(test_date)
        
        if len(result) > 0:
            print(f"✅ 單月測試成功: {len(result)} 筆資料")
            print(f"   欄位: {list(result.columns)}")
            print(f"   索引類型: {type(result.index)}")
            
            # 檢查資料格式是否符合 monthly_report.pkl
            if isinstance(result.index, pd.MultiIndex):
                index_names = result.index.names
                print(f"   索引名稱: {index_names}")
                
                if index_names == ['stock_id', 'date']:
                    print(f"   ✅ 索引格式正確")
                else:
                    print(f"   ⚠️ 索引格式不符: 期望 ['stock_id', 'date']，實際 {index_names}")
            else:
                print(f"   ❌ 索引不是 MultiIndex")
            
            # 顯示前幾筆資料
            print(f"   前3筆:")
            print(result.head(3))
            
            return result
        else:
            print(f"❌ 單月測試失敗: 無資料")
            return None
            
    except Exception as e:
        print(f"❌ 單月測試異常: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return None

def test_auto_update_monthly():
    """測試 auto_update 函數更新月營收"""
    print(f"\n🧪 測試 auto_update 函數更新月營收")
    print("=" * 50)
    
    try:
        # 備份現有檔案
        backup_file = backup_monthly_report()
        
        # 檢查現有狀態
        status = check_monthly_report_status()
        if status is None:
            print(f"❌ 無法檢查現有狀態")
            return False
        
        original_df, latest_date, months_behind = status
        original_count = len(original_df)
        
        print(f"\n🔄 開始使用 auto_update 更新月營收...")
        
        # 使用 auto_update 函數
        from auto_update import crawl_monthly_report
        auto_update('monthly_report', crawl_monthly_report, month_range)
        
        print(f"\n📊 檢查更新後狀態...")
        
        # 檢查更新後狀態
        updated_status = check_monthly_report_status()
        if updated_status is None:
            print(f"❌ 更新後無法讀取檔案")
            return False
        
        updated_df, updated_latest_date, updated_months_behind = updated_status
        updated_count = len(updated_df)
        
        print(f"\n📈 更新結果比較:")
        print(f"   更新前: {original_count:,} 筆，最新日期 {latest_date}")
        print(f"   更新後: {updated_count:,} 筆，最新日期 {updated_latest_date}")
        print(f"   新增: {updated_count - original_count:,} 筆")
        
        if updated_count > original_count:
            print(f"   ✅ 更新成功！")
            return True
        else:
            print(f"   ⚠️ 沒有新增資料")
            return False
            
    except Exception as e:
        print(f"❌ auto_update 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("🔧 Monthly Report 更新測試工具")
    print("=" * 60)
    print("🎯 目標: 測試 auto_update.py 是否能正確更新 monthly_report.pkl")
    print("💡 使用: 修復版月營收爬蟲 (基於 FinLab 方法)")
    print("=" * 60)
    
    # 檢查現有狀態
    print(f"\n📋 檢查現有 monthly_report.pkl 狀態...")
    status = check_monthly_report_status()
    
    # 測試單月更新
    single_result = test_single_month_update()
    
    # 如果單月測試成功，測試完整更新
    if single_result is not None:
        print(f"\n💾 單月測試成功，準備測試完整更新...")
        
        # 詢問是否繼續
        print(f"\n⚠️ 注意: 完整更新會修改 monthly_report.pkl 檔案")
        print(f"   (已自動備份，可以安全進行)")
        
        # 自動繼續測試
        success = test_auto_update_monthly()
        
        if success:
            print(f"\n🎉 月營收更新測試完全成功！")
            print(f"✅ auto_update.py 可以正確更新 monthly_report.pkl")
            print(f"✅ 修復版爬蟲運作正常")
            print(f"✅ 資料格式符合要求")
        else:
            print(f"\n⚠️ 完整更新測試有問題")
    else:
        print(f"\n❌ 單月測試失敗，無法進行完整更新測試")

if __name__ == "__main__":
    main()
