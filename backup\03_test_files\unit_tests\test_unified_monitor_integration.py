#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統一監控管理器整合功能
"""

import sys
import os
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit, QLineEdit, QLabel, QHBoxLayout
from PyQt6.QtCore import Qt

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class UnifiedMonitorTestWindow(QMainWindow):
    """統一監控管理器測試視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("統一監控管理器整合測試")
        self.setGeometry(100, 100, 900, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🎯 統一監控股票管理系統測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196f3; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 股票輸入區域
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("監控股票:"))
        
        self.stocks_input = QLineEdit()
        self.stocks_input.setPlaceholderText("輸入股票代碼，用逗號分隔 (例: 2330,2317,2454)")
        input_layout.addWidget(self.stocks_input)
        
        self.update_btn = QPushButton("🔄 更新監控清單")
        self.update_btn.clicked.connect(self.update_monitor_stocks)
        input_layout.addWidget(self.update_btn)
        
        layout.addLayout(input_layout)
        
        # 測試按鈕區域
        button_layout = QHBoxLayout()
        
        self.test_manager_btn = QPushButton("🧪 測試統一管理器")
        self.test_manager_btn.clicked.connect(self.test_unified_manager)
        button_layout.addWidget(self.test_manager_btn)
        
        self.test_sync_btn = QPushButton("🔄 測試同步功能")
        self.test_sync_btn.clicked.connect(self.test_sync_functionality)
        button_layout.addWidget(self.test_sync_btn)
        
        self.test_main_app_btn = QPushButton("🚀 測試主程式整合")
        self.test_main_app_btn.clicked.connect(self.test_main_app_integration)
        button_layout.addWidget(self.test_main_app_btn)
        
        layout.addLayout(button_layout)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        # 初始化
        self.init_unified_manager()
        self.log("🚀 統一監控管理器測試系統已啟動")
    
    def init_unified_manager(self):
        """初始化統一監控管理器"""
        try:
            from unified_monitor_manager import get_monitor_manager
            self.monitor_manager = get_monitor_manager()
            
            # 連接信號
            self.monitor_manager.stocks_updated.connect(self.on_stocks_updated)
            
            # 載入當前股票清單
            current_stocks = self.monitor_manager.get_stocks_string()
            self.stocks_input.setText(current_stocks)
            
            self.log("✅ 統一監控管理器初始化成功")
            
        except Exception as e:
            self.log(f"❌ 統一監控管理器初始化失敗: {e}")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def on_stocks_updated(self, stocks):
        """當股票清單更新時的回調"""
        self.log(f"📡 收到股票更新信號: {len(stocks)} 支股票")
        self.log(f"   股票清單: {', '.join(stocks)}")
    
    def update_monitor_stocks(self):
        """更新監控股票清單"""
        try:
            stocks_str = self.stocks_input.text().strip()
            if not stocks_str:
                self.log("⚠️ 請輸入股票代碼")
                return
            
            success = self.monitor_manager.set_stocks_from_string(stocks_str)
            if success:
                self.log(f"✅ 監控股票清單已更新: {stocks_str}")
            else:
                self.log("❌ 更新監控股票清單失敗")
                
        except Exception as e:
            self.log(f"❌ 更新監控股票失敗: {e}")
    
    def test_unified_manager(self):
        """測試統一管理器基本功能"""
        self.log("🧪 開始測試統一管理器...")
        
        try:
            # 測試獲取股票清單
            stocks = self.monitor_manager.get_stocks()
            self.log(f"✅ 獲取股票清單: {len(stocks)} 支")
            
            # 測試統計資訊
            stats = self.monitor_manager.get_stats()
            self.log(f"✅ 統計資訊: {stats}")
            
            # 測試添加股票
            test_stock = "1234"
            if self.monitor_manager.add_stock(test_stock):
                self.log(f"✅ 添加測試股票成功: {test_stock}")
                
                # 測試移除股票
                if self.monitor_manager.remove_stock(test_stock):
                    self.log(f"✅ 移除測試股票成功: {test_stock}")
                else:
                    self.log(f"❌ 移除測試股票失敗: {test_stock}")
            else:
                self.log(f"❌ 添加測試股票失敗: {test_stock}")
            
            self.log("🎉 統一管理器基本功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 統一管理器測試失敗: {e}")
    
    def test_sync_functionality(self):
        """測試同步功能"""
        self.log("🔄 開始測試同步功能...")
        
        try:
            # 測試設定新的股票清單
            test_stocks = ['2330', '2317', '2454', '3008', '2412']
            
            self.log(f"📝 設定測試股票清單: {test_stocks}")
            success = self.monitor_manager.set_stocks(test_stocks)
            
            if success:
                self.log("✅ 股票清單設定成功")
                
                # 驗證設定結果
                current_stocks = self.monitor_manager.get_stocks()
                if current_stocks == test_stocks:
                    self.log("✅ 股票清單驗證成功")
                else:
                    self.log(f"❌ 股票清單驗證失敗: 期望 {test_stocks}, 實際 {current_stocks}")
            else:
                self.log("❌ 股票清單設定失敗")
            
            self.log("🎉 同步功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 同步功能測試失敗: {e}")
    
    def test_main_app_integration(self):
        """測試主程式整合"""
        self.log("🚀 開始測試主程式整合...")
        
        try:
            # 檢查配置檔案
            config_file = "app_config.json"
            if os.path.exists(config_file):
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                monitor_config = config.get('monitor', {})
                self.log(f"✅ 配置檔案存在，監控配置: {monitor_config}")
            else:
                self.log("⚠️ 配置檔案不存在")
            
            # 測試主程式類別導入
            try:
                from O3mh_gui_v21_optimized import StockScreenerGUI
                self.log("✅ 主程式類別導入成功")
                
                # 注意：這裡不實際創建主程式實例，避免衝突
                self.log("ℹ️ 主程式整合測試完成（未實際啟動主程式）")
                
            except ImportError as e:
                self.log(f"❌ 主程式類別導入失敗: {e}")
            
            self.log("🎉 主程式整合測試完成")
            
        except Exception as e:
            self.log(f"❌ 主程式整合測試失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("統一監控管理器測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = UnifiedMonitorTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
