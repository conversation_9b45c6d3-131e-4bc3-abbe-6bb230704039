#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
詳細診斷下載問題
"""

import os
import time
import glob
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def monitor_download_directory():
    """監控下載目錄的變化"""
    download_paths = [
        "D:/Finlab/history/tables/monthly_revenue",
        "C:/Users/<USER>/Downloads",
        "./downloads"
    ]
    
    # 確保目錄存在
    existing_paths = [path for path in download_paths if os.path.exists(path)]
    
    print("📂 監控的下載目錄:")
    initial_files = {}
    for path in existing_paths:
        files = set(glob.glob(os.path.join(path, "*.*")))
        initial_files[path] = files
        print(f"   {path}: {len(files)} 個文件")
    
    return existing_paths, initial_files

def test_download_with_monitoring():
    """測試下載並監控文件變化"""
    driver = None
    try:
        print("🚀 開始詳細下載測試...")
        
        # 監控下載目錄
        download_paths, initial_files = monitor_download_directory()
        
        # 設置Chrome
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        # 設置下載目錄
        prefs = {
            "download.default_directory": "C:/Users/<USER>/Downloads",
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        driver = webdriver.Chrome(options=options)
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        driver.get(url)
        
        # 等待頁面載入
        print("⏳ 等待頁面載入...")
        time.sleep(10)
        
        # 處理廣告
        print("🚫 處理廣告...")
        try:
            # 隱藏Google廣告iframe
            ad_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@id, 'google_ads_iframe')]")
            for iframe in ad_iframes:
                driver.execute_script("arguments[0].style.display = 'none';", iframe)
                print(f"   隱藏廣告iframe: {iframe.get_attribute('id')}")
        except Exception as e:
            print(f"   處理廣告時出錯: {e}")
        
        # 查找XLS按鈕
        print("🔍 查找XLS按鈕...")
        xls_button = None
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        
        for i, input_elem in enumerate(all_inputs):
            try:
                input_value = input_elem.get_attribute("value")
                if input_value and "XLS" in input_value:
                    print(f"   找到XLS按鈕: {input_value}")
                    if input_elem.is_displayed() and input_elem.is_enabled():
                        xls_button = input_elem
                        break
            except:
                continue
        
        if not xls_button:
            print("❌ 未找到XLS按鈕")
            return False
        
        # 點擊按鈕前的狀態
        print("\n📊 點擊前的文件狀態:")
        for path in download_paths:
            current_files = set(glob.glob(os.path.join(path, "*.*")))
            print(f"   {path}: {len(current_files)} 個文件")
        
        # 點擊按鈕
        print("\n🖱️ 點擊XLS按鈕...")
        click_success = False
        
        # 嘗試多種點擊方法
        methods = [
            ("直接執行onclick", lambda: driver.execute_script(xls_button.get_attribute("onclick"))),
            ("JavaScript點擊", lambda: driver.execute_script("arguments[0].click();", xls_button)),
            ("直接點擊", lambda: xls_button.click())
        ]
        
        for method_name, method_func in methods:
            try:
                print(f"   嘗試 {method_name}...")
                method_func()
                print(f"   ✅ {method_name} 成功")
                click_success = True
                break
            except Exception as e:
                print(f"   ❌ {method_name} 失敗: {e}")
        
        if not click_success:
            print("❌ 所有點擊方法都失敗")
            return False
        
        # 監控下載過程
        print("\n⏳ 監控下載過程 (30秒)...")
        start_time = time.time()
        timeout = 30
        
        while time.time() - start_time < timeout:
            for path in download_paths:
                current_files = set(glob.glob(os.path.join(path, "*.*")))
                initial = initial_files[path]
                new_files = current_files - initial
                
                if new_files:
                    for new_file in new_files:
                        filename = os.path.basename(new_file)
                        file_size = os.path.getsize(new_file) if os.path.exists(new_file) else 0
                        
                        print(f"🆕 新文件: {filename} ({file_size} bytes) 在 {path}")
                        
                        # 檢查是否為Excel文件
                        if new_file.lower().endswith(('.xls', '.xlsx')):
                            if 'salemondetail' in filename.lower():
                                print(f"✅ 找到目標Excel文件: {filename}")
                                
                                # 等待文件穩定
                                time.sleep(3)
                                final_size = os.path.getsize(new_file) if os.path.exists(new_file) else 0
                                print(f"📄 最終文件大小: {final_size} bytes")
                                
                                if final_size > 0:
                                    print("🎉 下載成功！")
                                    return True
                                else:
                                    print("⚠️ 文件大小為0")
                        
                        # 檢查下載中的文件
                        elif filename.endswith(('.crdownload', '.tmp', '.part')):
                            print(f"⏳ 下載中: {filename}")
            
            time.sleep(2)
        
        print("⏰ 下載監控超時")
        
        # 最終狀態檢查
        print("\n📊 最終文件狀態:")
        for path in download_paths:
            current_files = set(glob.glob(os.path.join(path, "*.*")))
            new_files = current_files - initial_files[path]
            print(f"   {path}: 新增 {len(new_files)} 個文件")
            for new_file in new_files:
                filename = os.path.basename(new_file)
                file_size = os.path.getsize(new_file) if os.path.exists(new_file) else 0
                print(f"      {filename} ({file_size} bytes)")
        
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        if driver:
            print("\n按Enter鍵關閉瀏覽器...")
            input()
            driver.quit()

if __name__ == "__main__":
    test_download_with_monitoring()
