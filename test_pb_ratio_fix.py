#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股價淨值比修正邏輯
"""

import os
import sqlite3
from datetime import datetime, timedelta

def test_pb_ratio_logic():
    """測試修正後的PB值邏輯"""
    print("🔍 測試股價淨值比修正邏輯")
    print("=" * 60)
    
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    
    if not os.path.exists(pe_db_path):
        print(f"❌ PE資料庫不存在: {pe_db_path}")
        return
    
    try:
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 模擬get_real_financial_info的邏輯
        stock_code = '2429'
        target_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📊 測試股票: {stock_code}")
        print(f"📅 目標日期: {target_date}")
        print("-" * 40)
        
        # 1. 查找最接近的可用日期（3天內）
        cursor.execute("""
            SELECT DISTINCT date
            FROM pe_data
            WHERE date <= ? AND date >= ?
            ORDER BY date DESC
            LIMIT 1
        """, (target_date, (datetime.strptime(target_date, '%Y-%m-%d') - timedelta(days=3)).strftime('%Y-%m-%d')))
        
        available_dates = cursor.fetchall()
        
        if available_dates:
            pe_date = available_dates[0][0]
            
            # 檢查數據是否過舊
            pe_date_obj = datetime.strptime(pe_date, '%Y-%m-%d')
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d')
            days_diff = (target_date_obj - pe_date_obj).days
            
            print(f"✅ 找到PE資料日期: {pe_date}")
            print(f"📊 距離目標日期: {days_diff} 天")
            
            if days_diff > 3:
                print(f"⚠️ PE資料過舊，不使用")
                return
            
            # 2. 獲取PE資料
            cursor.execute("""
                SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                FROM pe_data
                WHERE date = ? AND stock_id = ?
            """, (pe_date, stock_code))
            
            result = cursor.fetchone()
            
            if result:
                result_stock_id, result_stock_name, dividend_yield, pe_ratio, pb_ratio = result
                
                print(f"📋 查詢結果:")
                print(f"   股票代碼: {result_stock_id}")
                print(f"   股票名稱: {result_stock_name}")
                print(f"   殖利率: {dividend_yield}")
                print(f"   本益比: {pe_ratio}")
                print(f"   股價淨值比: {pb_ratio}")
                
                # 3. 驗證股票代碼是否匹配
                if result_stock_id != stock_code:
                    print(f"❌ 股票代碼不匹配: 查詢{stock_code}, 結果{result_stock_id}")
                    return
                
                # 4. 驗證PB值的合理性
                if pb_ratio and pb_ratio != 'N/A' and pb_ratio > 0:
                    if 0.1 <= pb_ratio <= 50:
                        formatted_pb = f"{pb_ratio:.2f}" if isinstance(pb_ratio, (int, float)) else str(pb_ratio)
                        print(f"✅ PB值正常: {formatted_pb}")
                    else:
                        print(f"⚠️ PB值異常: {pb_ratio}，可能為錯誤數據")
                else:
                    print(f"❌ PB值無效: {pb_ratio}")
                
            else:
                print(f"❌ 找不到 {stock_code} 在 {pe_date} 的資料")
        else:
            print(f"❌ 在3天內找不到可用的PE資料")
        
        # 5. 檢查PB值6.72的分布情況
        print("\n" + "=" * 60)
        print("🔍 檢查PB值6.72的分布情況")
        print("-" * 40)
        
        cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
        latest_date = cursor.fetchone()
        
        if latest_date:
            latest_date = latest_date[0]
            
            cursor.execute("""
                SELECT stock_id, stock_name, pb_ratio
                FROM pe_data
                WHERE date = ? AND ABS(pb_ratio - 6.72) < 0.01
                ORDER BY stock_id
            """, (latest_date,))
            
            pb_672_stocks = cursor.fetchall()
            
            print(f"📊 在 {latest_date} 有 {len(pb_672_stocks)} 檔股票的PB值接近6.72:")
            
            for i, (stock_id, stock_name, pb_ratio) in enumerate(pb_672_stocks):
                if i < 10:  # 只顯示前10個
                    print(f"   {stock_id} {stock_name}: {pb_ratio}")
                elif i == 10:
                    print(f"   ... 還有 {len(pb_672_stocks) - 10} 檔股票")
                    break
            
            if len(pb_672_stocks) > 5:
                print(f"⚠️ 發現 {len(pb_672_stocks)} 檔股票有相同的PB值，可能存在數據承襲問題")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def check_2429_specific():
    """專門檢查2429的數據"""
    print("\n" + "=" * 60)
    print("🎯 專門檢查2429銘旺科的數據")
    print("=" * 60)
    
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    try:
        # 檢查PE資料
        if os.path.exists(pe_db_path):
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            print("📊 PE資料庫中的2429數據:")
            cursor.execute("""
                SELECT date, dividend_yield, pe_ratio, pb_ratio
                FROM pe_data
                WHERE stock_id = '2429'
                ORDER BY date DESC
                LIMIT 5
            """)
            
            pe_results = cursor.fetchall()
            
            if pe_results:
                print("日期\t\t殖利率\t本益比\tPB值")
                print("-" * 40)
                for date, dividend_yield, pe_ratio, pb_ratio in pe_results:
                    print(f"{date}\t{dividend_yield}\t{pe_ratio}\t{pb_ratio}")
            else:
                print("❌ PE資料庫中找不到2429的數據")
            
            conn.close()
        
        # 檢查股價資料
        if os.path.exists(price_db_path):
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            print("\n📊 股價資料庫中的2429數據:")
            cursor.execute("""
                SELECT date, close
                FROM stock_daily_data
                WHERE stock_id = '2429'
                ORDER BY date DESC
                LIMIT 5
            """)
            
            price_results = cursor.fetchall()
            
            if price_results:
                print("日期\t\t收盤價")
                print("-" * 20)
                for date, close in price_results:
                    print(f"{date}\t{close}")
            else:
                print("❌ 股價資料庫中找不到2429的數據")
            
            conn.close()
            
    except Exception as e:
        print(f"❌ 檢查2429數據失敗: {e}")

if __name__ == "__main__":
    test_pb_ratio_logic()
    check_2429_specific()
