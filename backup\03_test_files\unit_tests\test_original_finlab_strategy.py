#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試基於原始FinLab結構的高殖利率烏龜策略
"""

import sys
import pandas as pd
from datetime import datetime
sys.path.append('strategies')

def test_original_finlab_strategy():
    """測試原始FinLab結構的策略"""
    
    print("🧪 測試基於原始FinLab結構的高殖利率烏龜策略")
    print("=" * 60)
    
    # 1. 測試策略初始化
    print("1️⃣ 測試策略初始化...")
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        strategy = HighYieldTurtleStrategy()
        
        if strategy.pe_data_cache is not None:
            print(f"   ✅ 策略初始化成功")
            print(f"   📊 數據筆數: {len(strategy.pe_data_cache):,}")
            
            # 檢查數據結構
            if hasattr(strategy.pe_data_cache.index, 'get_level_values'):
                stock_ids = strategy.pe_data_cache.index.get_level_values('stock_id')
                unique_stocks = stock_ids.nunique()
                print(f"   📈 涵蓋股票數: {unique_stocks:,} 支")
                
                # 顯示樣本股票
                sample_stocks = stock_ids.unique()[:5]
                print(f"   📋 樣本股票: {sample_stocks.tolist()}")
        else:
            print(f"   ❌ 策略初始化失敗，數據未載入")
            return False
            
    except Exception as e:
        print(f"   ❌ 策略初始化失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 2. 測試股票代碼提取功能
    print(f"\n2️⃣ 測試股票代碼提取功能...")
    test_codes = [
        "1101 台泥",
        "2330 台積電", 
        "2881 富邦金",
        "0050 元大台灣50"
    ]
    
    for full_code in test_codes:
        extracted = strategy.extract_stock_code(full_code)
        print(f"   {full_code} -> {extracted}")
    
    # 3. 測試股票查找功能
    print(f"\n3️⃣ 測試股票查找功能...")
    test_stock_ids = ['1101', '2330', '2881', '1108']
    
    for stock_id in test_stock_ids:
        full_id = strategy.find_stock_in_pkl(stock_id)
        if full_id:
            print(f"   ✅ {stock_id} -> {full_id}")
        else:
            print(f"   ❌ {stock_id} -> 未找到")
    
    # 4. 測試PKL數據獲取
    print(f"\n4️⃣ 測試PKL數據獲取...")
    for stock_id in test_stock_ids[:3]:  # 只測試前3個
        pkl_data = strategy.get_stock_pkl_data(stock_id)
        if pkl_data:
            print(f"   ✅ {stock_id}:")
            print(f"      完整代號: {pkl_data.get('完整代號', 'N/A')}")
            print(f"      殖利率: {pkl_data.get('殖利率(%)', 'N/A')}%")
            print(f"      本益比: {pkl_data.get('本益比', 'N/A')}")
            print(f"      股價淨值比: {pkl_data.get('股價淨值比', 'N/A')}")
        else:
            print(f"   ❌ {stock_id}: 無法獲取數據")
    
    # 5. 測試完整策略分析
    print(f"\n5️⃣ 測試完整策略分析...")
    
    # 創建測試股價數據
    dates = pd.date_range(end=datetime.now(), periods=50, freq='D')
    prices = [100 * (1.005 ** i) for i in range(50)]  # 溫和上漲
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 50
    }, index=dates)
    
    # 測試多支股票
    test_stocks = ['1101', '1108', '2330', '2881', '2882']
    suitable_stocks = []
    
    for stock_id in test_stocks:
        try:
            result = strategy.analyze_stock(test_df, stock_id=stock_id)
            
            score = result.get('score', 0)
            suitable = result.get('suitable', False)
            details = result.get('details', {})
            data_source = details.get('data_source', '未知')
            
            status = "✅ 符合" if suitable else "❌ 不符合"
            print(f"   {status} {stock_id}: 評分{score}/100 ({data_source})")
            
            if suitable:
                suitable_stocks.append(stock_id)
                
                # 顯示詳細信息
                full_id = details.get('full_stock_id', stock_id)
                dividend = details.get('dividend_yield', 'N/A')
                pe_ratio = details.get('pe_ratio', 'N/A')
                pb_ratio = details.get('pb_ratio', 'N/A')
                print(f"      📊 {full_id}: 殖利率{dividend}%, 本益比{pe_ratio}, 股價淨值比{pb_ratio}")
            
        except Exception as e:
            print(f"   ❌ {stock_id}: 分析失敗 - {e}")
    
    print(f"\n   📊 測試結果: {len(suitable_stocks)}/{len(test_stocks)} 支股票符合策略")
    
    # 6. 測試策略信息
    print(f"\n6️⃣ 測試策略信息...")
    strategy_info = strategy.get_strategy_info()
    print(f"   策略名稱: {strategy_info['name']}")
    print(f"   版本: {strategy_info['version']}")
    print(f"   數據來源: {strategy_info['data_source']}")
    print(f"   數據已載入: {strategy_info['data_loaded']}")
    
    print(f"\n" + "=" * 60)
    print(f"✅ 原始FinLab結構策略測試完成！")
    
    if suitable_stocks:
        print(f"🎉 找到 {len(suitable_stocks)} 支符合策略的股票")
        print(f"📊 策略成功適配原始FinLab PKL數據結構")
    else:
        print(f"⚠️ 當前測試條件下沒有股票符合策略")
    
    return True

if __name__ == "__main__":
    success = test_original_finlab_strategy()
    
    if success:
        print(f"\n🎯 總結:")
        print(f"✅ 高殖利率烏龜策略已成功適配原始FinLab PKL數據結構")
        print(f"✅ 支援MultiIndex格式 (stock_id, date)")
        print(f"✅ 正確處理 '1101 台泥' 格式的股票代碼")
        print(f"✅ 自動轉換object類型數據為數值類型")
        print(f"✅ 統一資料庫路徑: D:\\Finlab\\history\\tables")
        print(f"🚀 策略已準備就緒，可在GUI中正常使用！")
    else:
        print(f"\n❌ 策略測試失敗，請檢查上述錯誤信息")
