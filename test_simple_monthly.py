#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化版月營收爬蟲
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import datetime
import pandas as pd
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 從 auto_update.py 導入簡化版函數
from auto_update import crawl_monthly_report_simple

def test_simple_monthly_crawler():
    """測試簡化版月營收爬蟲"""
    print("🔧 測試簡化版月營收爬蟲")
    print("=" * 60)
    print("💡 基於您提供的程式改寫")
    print("=" * 60)
    
    # 測試較舊的月份 (確定已發布)
    test_date = datetime.datetime(2024, 12, 10)  # 2024年11月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年11月營收)")
    
    try:
        print("🔄 開始測試簡化版爬蟲...")
        result = crawl_monthly_report_simple(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ 簡化版爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 檢查資料格式是否符合 monthly_report.pkl
            print(f"\n🔍 格式檢查:")
            
            # 檢查索引
            if result.index.names == ['stock_id', 'date']:
                print(f"   ✅ 索引格式正確: {result.index.names}")
            else:
                print(f"   ❌ 索引格式錯誤: {result.index.names}")
            
            # 檢查必要欄位
            required_columns = ['當月營收']
            missing_columns = [col for col in required_columns if col not in result.columns]
            
            if not missing_columns:
                print(f"   ✅ 必要欄位完整")
            else:
                print(f"   ⚠️ 缺少欄位: {missing_columns}")
            
            # 檢查股票代碼格式
            stock_ids = result.index.get_level_values('stock_id')
            sample_ids = stock_ids[:3].tolist()
            print(f"   股票代碼樣本: {sample_ids}")
            
            # 檢查一些知名股票
            tsmc_candidates = [sid for sid in stock_ids if '2330' in str(sid)]
            if tsmc_candidates:
                tsmc_id = tsmc_candidates[0]
                tsmc_data = result.loc[tsmc_id]
                print(f"   找到台積電: {tsmc_id}")
                if '當月營收' in result.columns:
                    revenue = tsmc_data['當月營收'].iloc[0] if hasattr(tsmc_data['當月營收'], 'iloc') else tsmc_data['當月營收']
                    print(f"   台積電營收: {revenue:,}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('monthly_simple_test.pkl')
            print(f"\n💾 測試資料已保存: monthly_simple_test.pkl")
            
            return True
        else:
            print(f"\n⚠️ 簡化版爬蟲無資料")
            return False
            
    except Exception as e:
        print(f"\n❌ 簡化版爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def compare_with_existing():
    """與現有 monthly_report.pkl 比較格式"""
    print(f"\n🔍 與現有 monthly_report.pkl 格式比較")
    print("=" * 60)
    
    try:
        existing_file = 'history/tables/monthly_report.pkl'
        test_file = 'monthly_simple_test.pkl'
        
        if os.path.exists(existing_file) and os.path.exists(test_file):
            existing_data = pd.read_pickle(existing_file)
            test_data = pd.read_pickle(test_file)
            
            print(f"📊 格式比較:")
            print(f"   現有資料索引: {existing_data.index.names}")
            print(f"   測試資料索引: {test_data.index.names}")
            
            print(f"   現有資料欄位: {list(existing_data.columns)}")
            print(f"   測試資料欄位: {list(test_data.columns)}")
            
            # 檢查格式相容性
            if existing_data.index.names == test_data.index.names:
                print(f"   ✅ 索引格式相容")
            else:
                print(f"   ❌ 索引格式不相容")
            
            common_columns = set(existing_data.columns).intersection(set(test_data.columns))
            print(f"   共同欄位: {len(common_columns)} 個 - {list(common_columns)}")
            
            if len(common_columns) >= 1:
                print(f"   ✅ 欄位基本相容")
            else:
                print(f"   ❌ 欄位差異太大")
                
        else:
            print(f"❌ 無法比較 (檔案不存在)")
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")

def test_individual_functions():
    """測試個別函數"""
    print(f"\n🔍 測試個別簡化版函數")
    print("=" * 60)
    
    try:
        from auto_update import monthly_report_simple, monthly_report_otc_simple
        
        # 測試參數
        year = 2024
        month = 11
        
        print(f"🧪 測試上市公司 monthly_report_simple({year}, {month})...")
        try:
            sii_result = monthly_report_simple(year, month)
            if sii_result is not None and not sii_result.empty:
                print(f"   ✅ 上市成功: {len(sii_result)} 筆")
                print(f"   欄位: {list(sii_result.columns)}")
            else:
                print(f"   ⚠️ 上市無資料")
        except Exception as e:
            print(f"   ❌ 上市失敗: {str(e)[:50]}...")
        
        print(f"🧪 測試上櫃公司 monthly_report_otc_simple({year}, {month})...")
        try:
            otc_result = monthly_report_otc_simple(year, month)
            if otc_result is not None and not otc_result.empty:
                print(f"   ✅ 上櫃成功: {len(otc_result)} 筆")
                print(f"   欄位: {list(otc_result.columns)}")
            else:
                print(f"   ⚠️ 上櫃無資料")
        except Exception as e:
            print(f"   ❌ 上櫃失敗: {str(e)[:50]}...")
            
    except ImportError as e:
        print(f"❌ 無法導入函數: {str(e)}")
    except Exception as e:
        print(f"❌ 個別函數測試失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 簡化版月營收爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 測試基於您提供程式的簡化版爬蟲")
    print("💡 特色: 更簡潔的實現 + 符合 monthly_report.pkl 格式")
    print("=" * 60)
    
    # 測試1: 個別函數
    test_individual_functions()
    
    # 測試2: 完整爬蟲
    success = test_simple_monthly_crawler()
    
    # 測試3: 格式比較
    if success:
        compare_with_existing()
    
    if success:
        print(f"\n🎉 簡化版月營收爬蟲測試成功！")
        print(f"💡 現在可以在 auto_update.py 中使用:")
        print(f"   ('monthly_report', crawl_monthly_report, month_range),")
        print(f"🔧 系統會自動使用簡化版爬蟲進行直接更新")
    else:
        print(f"\n⚠️ 簡化版仍有問題，可能需要進一步調整")

if __name__ == "__main__":
    import os
    main()
