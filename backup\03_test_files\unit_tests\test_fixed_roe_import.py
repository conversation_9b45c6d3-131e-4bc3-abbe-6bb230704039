#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修正後的ROE數據庫匯入功能
"""

import os
import sys
from datetime import datetime

def test_fixed_import():
    """測試修正後的匯入功能"""
    try:
        print("🔧 測試修正後的ROE數據庫匯入功能")
        print("=" * 60)
        
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        
        # 找到最新的CSV文件
        csv_dir = "D:/Finlab/history/tables"
        csv_files = []
        
        if os.path.exists(csv_dir):
            for file in os.listdir(csv_dir):
                if 'roe_data' in file.lower() and file.endswith('.csv'):
                    file_path = os.path.join(csv_dir, file)
                    csv_files.append((file_path, os.path.getctime(file_path)))
        
        if not csv_files:
            print("❌ 沒有找到ROE CSV文件")
            return False
        
        # 選擇最新的文件
        latest_csv = max(csv_files, key=lambda x: x[1])[0]
        print(f"📁 使用CSV文件: {os.path.basename(latest_csv)}")
        
        # 創建下載器並測試匯入
        downloader = GoodinfoROECSVDownloader()
        
        print(f"\n💾 開始測試數據庫匯入...")
        success = downloader.import_csv_to_database(latest_csv)
        
        if success:
            print("✅ 數據庫匯入測試成功！")
            
            # 驗證匯入結果
            import sqlite3
            db_path = downloader.db_path
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM roe_data")
            count = cursor.fetchone()[0]
            print(f"📊 數據庫中總資料筆數: {count}")
            
            # 顯示最新的資料
            cursor.execute("""
                SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year 
                FROM roe_data 
                WHERE report_year = 2024
                ORDER BY roe_value DESC 
                LIMIT 5
            """)
            rows = cursor.fetchall()
            
            if rows:
                print(f"\n📋 2024年ROE最高前5名:")
                for i, row in enumerate(rows, 1):
                    print(f"  {i}. {row[0]} {row[1]} - ROE: {row[2]}% (變化: {row[3]}) EPS: {row[4]}")
            
            conn.close()
            return True
        else:
            print("❌ 數據庫匯入測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_fixed_import()
