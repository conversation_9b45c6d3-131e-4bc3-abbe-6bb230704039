"""
測試新版自動股價爬蟲程式
驗證功能是否正常，並與原有price.db相容
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

def test_new_price_crawler():
    """測試新版自動股價爬蟲"""
    print("🧪 測試新版自動股價爬蟲程式")
    print("=" * 50)
    
    # 1. 測試模組導入
    print("1. 測試模組導入...")
    
    try:
        from new_price_crawler_auto import NewPriceCrawlerAuto
        print("   ✅ new_price_crawler_auto 導入成功")
    except ImportError as e:
        print(f"   ❌ new_price_crawler_auto 導入失敗: {e}")
        return False
    
    # 2. 測試爬蟲初始化
    print("\n2. 測試爬蟲初始化...")
    
    try:
        # 使用測試資料庫路徑
        test_db_path = "test_price_crawler.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        crawler = NewPriceCrawlerAuto(test_db_path)
        print("   ✅ 爬蟲初始化成功")
        
        # 檢查資料庫是否創建
        if os.path.exists(test_db_path):
            print("   ✅ 測試資料庫創建成功")
        else:
            print("   ❌ 測試資料庫創建失敗")
            return False
            
    except Exception as e:
        print(f"   ❌ 爬蟲初始化失敗: {e}")
        return False
    
    # 3. 測試資料庫結構
    print("\n3. 測試資料庫結構...")
    
    try:
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_daily_data'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("   ✅ stock_daily_data 表存在")
            
            # 檢查表結構
            cursor.execute("PRAGMA table_info(stock_daily_data)")
            columns = [col[1] for col in cursor.fetchall()]
            expected_columns = ['stock_id', 'date', 'Open', 'High', 'Low', 'Close', 'Volume']
            
            if all(col in columns for col in expected_columns):
                print("   ✅ 表結構正確")
                print(f"   📋 欄位: {columns}")
            else:
                print(f"   ❌ 表結構不正確，期望: {expected_columns}，實際: {columns}")
                return False
        else:
            print("   ❌ stock_daily_data 表不存在")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 資料庫結構測試失敗: {e}")
        return False
    
    # 4. 測試資料轉換功能
    print("\n4. 測試資料轉換功能...")
    
    try:
        # 創建測試資料（模擬爬蟲返回的格式）
        test_data = pd.DataFrame({
            'StockID': ['2330', '2317'],
            'Date': ['2025-07-18', '2025-07-18'],
            'Open': [500.0, 100.0],
            'Max': [510.0, 105.0],
            'Min': [495.0, 98.0],
            'Close': [505.0, 102.0],
            'TradeVolume': [50000000, 30000000]
        })
        
        # 測試轉換功能
        converted_data = crawler.convert_to_price_db_format(test_data)
        
        if not converted_data.empty:
            print("   ✅ 資料轉換成功")
            print(f"   📊 轉換後欄位: {list(converted_data.columns)}")
            print(f"   📈 轉換後記錄數: {len(converted_data)}")
            
            # 檢查轉換後的欄位
            expected_columns = ['stock_id', 'date', 'Open', 'High', 'Low', 'Close', 'Volume']
            if all(col in converted_data.columns for col in expected_columns):
                print("   ✅ 轉換後欄位正確")
            else:
                print("   ❌ 轉換後欄位不正確")
                return False
        else:
            print("   ❌ 資料轉換失敗")
            return False
            
    except Exception as e:
        print(f"   ❌ 資料轉換測試失敗: {e}")
        return False
    
    # 5. 測試資料儲存功能
    print("\n5. 測試資料儲存功能...")
    
    try:
        # 儲存測試資料
        saved_count = crawler.save_to_price_db(test_data, 'test')
        
        if saved_count > 0:
            print(f"   ✅ 資料儲存成功: {saved_count} 筆")
            
            # 驗證資料是否正確儲存
            conn = sqlite3.connect(test_db_path)
            df_check = pd.read_sql_query('SELECT * FROM stock_daily_data', conn)
            conn.close()
            
            if not df_check.empty:
                print(f"   ✅ 資料庫驗證成功: {len(df_check)} 筆記錄")
                print(f"   📋 範例記錄: {df_check.iloc[0].to_dict()}")
            else:
                print("   ❌ 資料庫驗證失敗：無資料")
                return False
        else:
            print("   ❌ 資料儲存失敗")
            return False
            
    except Exception as e:
        print(f"   ❌ 資料儲存測試失敗: {e}")
        return False
    
    # 6. 測試統計功能
    print("\n6. 測試統計功能...")
    
    try:
        stats = crawler.get_database_stats()
        
        if stats:
            print("   ✅ 統計功能正常")
            for key, value in stats.items():
                print(f"   📊 {key}: {value}")
        else:
            print("   ❌ 統計功能失敗")
            return False
            
    except Exception as e:
        print(f"   ❌ 統計功能測試失敗: {e}")
        return False
    
    # 7. 清理測試檔案
    print("\n7. 清理測試檔案...")
    
    try:
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("   ✅ 測試檔案清理完成")
    except Exception as e:
        print(f"   ⚠️ 測試檔案清理失敗: {e}")
    
    print("\n✅ 新版自動股價爬蟲測試完成！")
    return True

def test_compatibility_with_original():
    """測試與原有price.db的相容性"""
    print("\n🔄 測試與原有price.db的相容性")
    print("=" * 50)
    
    original_db_path = "db/price.db"
    
    if not os.path.exists(original_db_path):
        print("❌ 原有price.db不存在，跳過相容性測試")
        return True
    
    try:
        from new_price_crawler_auto import NewPriceCrawlerAuto
        
        # 使用原有的price.db路徑
        crawler = NewPriceCrawlerAuto(original_db_path)
        
        print("1. 測試讀取原有資料庫...")
        stats = crawler.get_database_stats()
        
        if stats:
            print("   ✅ 成功讀取原有price.db")
            print(f"   📊 總記錄數: {stats.get('total_records', 0):,}")
            print(f"   📊 股票數量: {stats.get('stock_count', 0):,}")
            print(f"   📊 日期範圍: {stats.get('date_range', {})}")
            print(f"   📊 最新日期: {stats.get('latest_date', 'N/A')}")
        else:
            print("   ❌ 讀取原有price.db失敗")
            return False
        
        print("\n2. 測試資料庫結構相容性...")
        
        conn = sqlite3.connect(original_db_path)
        cursor = conn.cursor()
        
        # 檢查表結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = [col[1] for col in cursor.fetchall()]
        
        expected_columns = ['stock_id', 'date', 'Open', 'High', 'Low', 'Close', 'Volume']
        compatible = all(col in columns for col in expected_columns)
        
        if compatible:
            print("   ✅ 資料庫結構完全相容")
        else:
            print(f"   ⚠️ 資料庫結構可能需要調整")
            print(f"   📋 現有欄位: {columns}")
            print(f"   📋 期望欄位: {expected_columns}")
        
        # 測試查詢功能
        cursor.execute("SELECT * FROM stock_daily_data LIMIT 3")
        sample_data = cursor.fetchall()
        
        if sample_data:
            print("   ✅ 資料查詢功能正常")
            print(f"   📋 範例資料: {sample_data[0]}")
        
        conn.close()
        
        print("\n✅ 相容性測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 相容性測試失敗: {e}")
        return False

def show_usage_examples():
    """顯示使用範例"""
    print("\n📋 使用範例")
    print("=" * 50)
    
    examples = [
        ("每日自動更新（預設）", "python new_price_crawler_auto.py"),
        ("爬取最近3天資料", "python new_price_crawler_auto.py --days 3"),
        ("指定日期範圍", "python new_price_crawler_auto.py --start-date 2025-07-15 --end-date 2025-07-18"),
        ("只顯示統計資訊", "python new_price_crawler_auto.py --stats-only"),
        ("使用自訂資料庫路徑", "python new_price_crawler_auto.py --db-path /path/to/custom.db"),
    ]
    
    for desc, cmd in examples:
        print(f"• {desc}:")
        print(f"  {cmd}")
        print()

if __name__ == "__main__":
    print("🚀 新版自動股價爬蟲測試程式")
    print("=" * 60)
    
    # 執行測試
    success1 = test_new_price_crawler()
    success2 = test_compatibility_with_original()
    
    if success1 and success2:
        print("\n🎉 所有測試通過！")
        
        # 顯示使用範例
        show_usage_examples()
        
        print("📝 部署說明:")
        print("1. 將 new_price_crawler_auto.py 複製到目標位置")
        print("2. 重新命名為 price_crawler_auto(HL).py")
        print("3. 更新排程任務指向新程式")
        print("4. 測試排程執行是否正常")
        
    else:
        print("\n❌ 部分測試失敗，請檢查相關問題")
    
    input("\n按 Enter 結束...")
