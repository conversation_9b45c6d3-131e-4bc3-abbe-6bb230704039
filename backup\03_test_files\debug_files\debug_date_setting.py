#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試日期設定問題
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoAlertPresentException, TimeoutException
import time
import os
import glob

def debug_date_setting():
    """調試日期設定問題"""
    
    # 設定下載目錄
    download_dir = "C:/Users/<USER>/Downloads"
    
    # 設定Chrome選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    
    driver = None
    try:
        print("🚀 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        driver.get(url)
        
        # 等待頁面載入
        time.sleep(5)
        
        # 處理廣告
        print("🚫 處理廣告...")
        try:
            ad_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@id, 'google_ads_iframe')]")
            for iframe in ad_iframes:
                driver.execute_script("arguments[0].remove();", iframe)
                print("   移除廣告iframe")
        except:
            pass
        
        # 檢查日期輸入框
        print("\n📅 檢查日期輸入框...")
        date_inputs = driver.find_elements(By.XPATH, "//input[@type='month']")
        print(f"找到 {len(date_inputs)} 個日期輸入框")
        
        for i, input_elem in enumerate(date_inputs):
            current_value = input_elem.get_attribute("value")
            input_id = input_elem.get_attribute("id")
            input_name = input_elem.get_attribute("name")
            print(f"   日期輸入框 {i+1}: id='{input_id}', name='{input_name}', 當前值='{current_value}'")
        
        if len(date_inputs) >= 2:
            print("\n🔧 測試日期設定方法...")
            
            # 方法1: 直接設定value屬性
            print("方法1: 直接設定value屬性")
            try:
                driver.execute_script("arguments[0].value = '2023-01';", date_inputs[0])
                driver.execute_script("arguments[0].value = '2025-07';", date_inputs[1])
                
                # 檢查設定結果
                new_value1 = date_inputs[0].get_attribute("value")
                new_value2 = date_inputs[1].get_attribute("value")
                print(f"   設定後: 開始='{new_value1}', 結束='{new_value2}'")
                
                if new_value1 == "2023-01" and new_value2 == "2025-07":
                    print("   ✅ 方法1成功")
                else:
                    print("   ❌ 方法1失敗")
            except Exception as e:
                print(f"   ❌ 方法1錯誤: {e}")
            
            # 方法2: 清除後輸入
            print("\n方法2: 清除後輸入")
            try:
                date_inputs[0].clear()
                date_inputs[0].send_keys("2023-01")
                
                date_inputs[1].clear()
                date_inputs[1].send_keys("2025-07")
                
                # 檢查設定結果
                new_value1 = date_inputs[0].get_attribute("value")
                new_value2 = date_inputs[1].get_attribute("value")
                print(f"   設定後: 開始='{new_value1}', 結束='{new_value2}'")
                
                if new_value1 == "2023-01" and new_value2 == "2025-07":
                    print("   ✅ 方法2成功")
                else:
                    print("   ❌ 方法2失敗")
            except Exception as e:
                print(f"   ❌ 方法2錯誤: {e}")
            
            # 方法3: 觸發事件
            print("\n方法3: 觸發change事件")
            try:
                driver.execute_script("""
                    arguments[0].value = '2023-01';
                    arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                """, date_inputs[0])
                
                driver.execute_script("""
                    arguments[0].value = '2025-07';
                    arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                    arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                """, date_inputs[1])
                
                # 檢查設定結果
                new_value1 = date_inputs[0].get_attribute("value")
                new_value2 = date_inputs[1].get_attribute("value")
                print(f"   設定後: 開始='{new_value1}', 結束='{new_value2}'")
                
                if new_value1 == "2023-01" and new_value2 == "2025-07":
                    print("   ✅ 方法3成功")
                    
                    # 嘗試查詢
                    print("\n🔍 嘗試查詢...")
                    
                    # 再次處理廣告
                    try:
                        ad_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@id, 'google_ads_iframe')]")
                        for iframe in ad_iframes:
                            driver.execute_script("arguments[0].remove();", iframe)
                    except:
                        pass
                    
                    # 查詢方法
                    query_methods = [
                        ("JavaScript執行ReloadDetail", lambda: driver.execute_script("ReloadDetail();")),
                        ("JavaScript點擊查詢按鈕", lambda: driver.execute_script(
                            "arguments[0].click();", driver.find_element(By.XPATH, "//input[@value='查詢']")))
                    ]
                    
                    for method_name, method_func in query_methods:
                        try:
                            print(f"   嘗試 {method_name}...")
                            method_func()
                            print(f"   ✅ {method_name} 成功")
                            time.sleep(5)  # 等待查詢結果
                            break
                        except Exception as e:
                            print(f"   ⚠️ {method_name} 失敗: {e}")
                    
                    # 測試XLS下載
                    print("\n📥 測試XLS下載...")
                    
                    # 記錄下載前的文件
                    initial_files = set(glob.glob(os.path.join(download_dir, "*.xls")))
                    print(f"   下載前Excel文件數: {len(initial_files)}")
                    
                    # 查找並點擊XLS按鈕
                    try:
                        xls_button = driver.find_element(By.XPATH, "//input[@value='XLS']")
                        onclick_code = xls_button.get_attribute("onclick")
                        print(f"   XLS按鈕onclick: {onclick_code}")
                        
                        # 執行onclick
                        driver.execute_script(onclick_code)
                        print("   ✅ XLS按鈕點擊成功")
                        
                        # 等待下載
                        print("   ⏳ 等待下載...")
                        for i in range(30):  # 等待30秒
                            current_files = set(glob.glob(os.path.join(download_dir, "*.xls")))
                            new_files = current_files - initial_files
                            
                            if new_files:
                                new_file = list(new_files)[0]
                                print(f"   ✅ 檢測到新文件: {os.path.basename(new_file)}")
                                file_size = os.path.getsize(new_file)
                                print(f"   📊 文件大小: {file_size} bytes")
                                
                                if file_size > 0:
                                    print("   🎉 下載成功!")
                                    return True
                                else:
                                    print("   ⚠️ 文件大小為0")
                            
                            time.sleep(2)
                        
                        print("   ⏰ 下載超時")
                        
                    except Exception as e:
                        print(f"   ❌ XLS下載失敗: {e}")
                    
                else:
                    print("   ❌ 方法3失敗")
                    
            except Exception as e:
                print(f"   ❌ 方法3錯誤: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        if driver:
            input("按Enter鍵關閉瀏覽器...")
            driver.quit()

if __name__ == "__main__":
    print("🧪 調試日期設定問題...")
    print("=" * 50)
    
    success = debug_date_setting()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 測試成功!")
    else:
        print("❌ 測試失敗!")
