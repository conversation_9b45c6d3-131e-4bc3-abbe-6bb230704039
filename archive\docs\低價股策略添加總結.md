
# 💰 低價股策略 - 添加完成總結

## 📋 策略概述

### 🎯 策略特色
- **策略類型**: 低價股專門策略
- **核心理念**: 專門針對低價股的量化選股
- **選股目標**: 尋找具有突破潛力的低價股
- **風險控制**: 排除金融股，設定5%停損

### 💰 低價股定義
- **絕對價格**: 收盤價≤25元
- **相對價格**: 低於整體市場分級的40%
- **排除標的**: 金融股（降低系統性風險）

### 📈 核心篩選條件（5個條件）
1. **近5日創120日新高** - 確保突破有效性
2. **60日價格區間≤30%** - 價格整理充分
3. **低於市場40%分位** - 相對低價
4. **收盤價≤25元** - 絕對低價
5. **5日均量>100張** - 流動性充足

## ✅ 實現功能

### 🔧 技術實現
- ✅ 策略檢查方法 (`check_low_price_strategy`)
- ✅ 120日新高檢查 (`check_120_day_new_high`)
- ✅ 價格區間檢查 (`check_price_range_narrow`)
- ✅ 專用表格設置 (`setup_low_price_strategy_table`)

### 📊 評分系統 (100分制)
- **120日新高** (25分): 近5日內創120日新高
- **價格區間** (20分): 60日價格區間≤30%
- **市場分級** (20分): 低於市場40%分位
- **絕對價格** (15分): 收盤價≤25元
- **成交量** (20分): 5日均量>100張

### 📋 表格顯示
- 股票代碼、股票名稱、收盤價（≤25元標綠）
- 低價股評分（彩色標示）
- 120日新高、價格區間、低價特徵
- 成交量、5日均量（>100張標綠）、策略狀態

### 📖 說明文檔
- ✅ 策略概述
- ✅ 詳細技術說明（5個核心條件）
- ✅ 實戰使用指南

## 🚀 使用指南

### 📋 操作步驟
1. **選擇策略**: 在下拉選單選擇「低價股策略」
2. **執行篩選**: 點擊執行策略
3. **查看評分**: 重點關注80分以上股票
4. **突破確認**: 確認近5日內有創120日新高
5. **風險控制**: 設定5%停損

### 💡 投資建議
- **適合對象**: 偏好低價股、追求高成長的積極型投資者
- **持股期間**: 建議短中期持有（1-3個月）
- **風險控制**: 嚴格執行5%停損
- **資金配置**: 每檔標的持有部位上限20%

## 🎊 策略優勢

### 🌟 核心優勢
1. **低價優勢**: 低價股具有較大的上漲空間和彈性
2. **突破確認**: 結合120日新高確認突破有效性
3. **風險控制**: 嚴格的篩選條件和停損機制
4. **量化選股**: 100分制評分系統客觀評估

### 🎯 適用情境
- **成長投資**: 尋找具有突破潛力的低價股
- **短中期投資**: 適合1-3個月的投資週期
- **積極策略**: 追求高成長的投資策略
- **技術突破**: 重視價格突破的選股策略

## 📊 原始策略邏輯

### 🔍 FinLab原始程式碼邏輯
```python
# 5個核心條件
condition1 = (close == close.rolling(120).max()).sustain(5,1)  # 近5日創120日新高
condition2 = (1 - low.rolling(60).min()/high.rolling(60).max()) < 0.3  # 價格區間≤30%
condition3 = close <= close.quantile_row(0.4)  # 低於市場40%分位
condition4 = close <= 25  # 收盤價≤25元
condition5 = vol.average(5) > 100*1000  # 5日均量>100張

# 交集所有條件
position = condition1 & condition2 & condition3 & condition4 & condition5

# 最後再挑選前5低價的標的
position = close * (position.astype(int))
position = position[position > 0].is_smallest(5)
```

### 📈 交易參數
- **重新平衡**: 每月底產生訊號
- **進場時機**: 隔月第一個交易日
- **交易價格**: 開盤價進出
- **停損設定**: 5%停損
- **部位限制**: 每檔標的持有部位上限20%
- **手續費**: 1.425/1000*0.3 (30%折扣)

---

**💰 低價股策略已成功添加到系統中！**

**現在您可以使用這個專門針對低價股設計的量化選股策略，在低價股中尋找具有突破潛力的投資機會！**
