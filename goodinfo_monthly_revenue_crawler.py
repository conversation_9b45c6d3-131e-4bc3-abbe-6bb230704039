#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoodInfo 月營收數據抓取器
基於現有的 goodinfo_data_system.py 擴展月營收抓取功能
"""

import sqlite3
import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import random
from datetime import datetime, timedelta
import logging
import os
import re

class GoodInfoMonthlyRevenueCrawler:
    """GoodInfo 月營收數據抓取器"""
    
    def __init__(self, db_path="db/monthly_revenue.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.setup_session()
        self.init_database()
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def setup_session(self):
        """設置請求會話"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def init_database(self):
        """初始化數據庫"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建月營收表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,                    -- 當月營收(千元)
                    revenue_mom REAL,               -- 月增率(%)
                    revenue_yoy REAL,               -- 年增率(%)
                    cumulative_revenue REAL,        -- 累計營收(千元)
                    cumulative_yoy REAL,            -- 累計年增率(%)
                    data_source TEXT DEFAULT 'goodinfo',
                    crawl_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 月營收數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
    
    def crawl_monthly_revenue(self, stock_id):
        """抓取單一股票的月營收數據"""
        try:
            self.logger.info(f"🚀 開始抓取 {stock_id} 月營收數據...")
            
            # 構建URL
            url = f"https://goodinfo.tw/tw/StockInfo/StockSalesMon.asp?STOCK_ID={stock_id}"
            
            # 隨機延遲避免被封鎖
            time.sleep(random.uniform(2, 4))
            
            # 發送請求
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code != 200:
                self.logger.error(f"❌ 請求失敗，狀態碼: {response.status_code}")
                return None
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 使用pandas讀取表格
            tables = pd.read_html(response.text)
            
            # 尋找月營收表格
            revenue_data = self.parse_revenue_tables(tables, stock_id)
            
            if revenue_data:
                self.logger.info(f"✅ 成功解析 {stock_id} 月營收數據: {len(revenue_data)} 筆")
                return revenue_data
            else:
                self.logger.warning(f"⚠️ 未找到 {stock_id} 的月營收數據")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 抓取 {stock_id} 月營收失敗: {e}")
            return None
    
    def parse_revenue_tables(self, tables, stock_id):
        """解析月營收表格"""
        try:
            revenue_data = []
            
            for i, table in enumerate(tables):
                # 檢查是否為月營收表格
                if self.is_revenue_table(table):
                    self.logger.info(f"✅ 找到月營收表格 (表格 {i+1})")
                    
                    # 解析表格數據
                    parsed_data = self.extract_revenue_data(table, stock_id)
                    if parsed_data:
                        revenue_data.extend(parsed_data)
            
            return revenue_data if revenue_data else None
            
        except Exception as e:
            self.logger.error(f"❌ 解析月營收表格失敗: {e}")
            return None
    
    def is_revenue_table(self, table):
        """判斷是否為月營收表格"""
        try:
            # 檢查表格是否包含月營收相關欄位
            columns = table.columns.tolist()
            table_str = str(table.head())
            
            # 關鍵字檢查
            revenue_keywords = ['營收', '月增率', '年增率', '累計', '千元']
            keyword_count = sum(1 for keyword in revenue_keywords if keyword in table_str)
            
            # 檢查行數（月營收表格通常有12行以上）
            has_enough_rows = len(table) >= 6
            
            return keyword_count >= 2 and has_enough_rows
            
        except Exception as e:
            self.logger.error(f"❌ 表格檢查失敗: {e}")
            return False
    
    def extract_revenue_data(self, table, stock_id):
        """從表格提取月營收數據"""
        try:
            revenue_records = []
            current_year = datetime.now().year
            
            # 獲取股票名稱（如果可能）
            stock_name = self.get_stock_name(stock_id)
            
            for index, row in table.iterrows():
                try:
                    # 嘗試解析年月
                    year_month = self.parse_year_month(row, index)
                    if not year_month:
                        continue
                    
                    year, month = year_month
                    
                    # 解析營收數據
                    revenue_info = self.parse_revenue_row(row)
                    if not revenue_info:
                        continue
                    
                    # 構建記錄
                    record = {
                        'stock_id': stock_id,
                        'stock_name': stock_name,
                        'year': year,
                        'month': month,
                        'revenue': revenue_info.get('revenue'),
                        'revenue_mom': revenue_info.get('revenue_mom'),
                        'revenue_yoy': revenue_info.get('revenue_yoy'),
                        'cumulative_revenue': revenue_info.get('cumulative_revenue'),
                        'cumulative_yoy': revenue_info.get('cumulative_yoy'),
                        'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    revenue_records.append(record)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue
            
            return revenue_records if revenue_records else None
            
        except Exception as e:
            self.logger.error(f"❌ 提取營收數據失敗: {e}")
            return None
    
    def parse_year_month(self, row, index):
        """解析年月信息"""
        try:
            # 嘗試從第一列獲取年月信息
            first_col = str(row.iloc[0]) if len(row) > 0 else ""
            
            # 匹配年月格式 (例如: "2024/01", "113/01", "01月")
            year_month_patterns = [
                r'(\d{4})/(\d{1,2})',  # 2024/01
                r'(\d{3})/(\d{1,2})',  # 113/01 (民國年)
                r'(\d{1,2})月',         # 01月
            ]
            
            current_year = datetime.now().year
            
            for pattern in year_month_patterns:
                match = re.search(pattern, first_col)
                if match:
                    if '月' in pattern:
                        # 只有月份，使用當前年份
                        month = int(match.group(1))
                        return current_year, month
                    else:
                        year_str, month_str = match.groups()
                        year = int(year_str)
                        month = int(month_str)
                        
                        # 處理民國年
                        if year < 1000:
                            year += 1911
                        
                        return year, month
            
            return None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 解析年月失敗: {e}")
            return None
    
    def parse_revenue_row(self, row):
        """解析營收行數據"""
        try:
            revenue_info = {}
            
            # 嘗試解析各個欄位（根據常見的GoodInfo表格結構）
            for i, value in enumerate(row):
                try:
                    # 清理數值
                    cleaned_value = self.clean_numeric_value(str(value))
                    
                    if cleaned_value is not None:
                        # 根據位置推測欄位類型
                        if i == 1:  # 通常第二列是當月營收
                            revenue_info['revenue'] = cleaned_value
                        elif i == 2:  # 第三列可能是月增率
                            revenue_info['revenue_mom'] = cleaned_value
                        elif i == 3:  # 第四列可能是年增率
                            revenue_info['revenue_yoy'] = cleaned_value
                        elif i == 4:  # 第五列可能是累計營收
                            revenue_info['cumulative_revenue'] = cleaned_value
                        elif i == 5:  # 第六列可能是累計年增率
                            revenue_info['cumulative_yoy'] = cleaned_value
                            
                except Exception as e:
                    continue
            
            return revenue_info if revenue_info else None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 解析營收行失敗: {e}")
            return None
    
    def clean_numeric_value(self, value_str):
        """清理數值字符串"""
        try:
            if pd.isna(value_str) or value_str in ['nan', 'None', '', '-']:
                return None
            
            # 移除常見的非數字字符
            cleaned = str(value_str).replace(',', '').replace('%', '').replace('+', '').strip()
            
            # 嘗試轉換為浮點數
            return float(cleaned)
            
        except (ValueError, TypeError):
            return None
    
    def get_stock_name(self, stock_id):
        """獲取股票名稱"""
        # 簡單的股票名稱對照表
        stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
            '2412': '中華電', '2881': '富邦金', '2882': '國泰金'
        }
        return stock_names.get(stock_id, f'股票{stock_id}')
    
    def save_to_database(self, revenue_data):
        """儲存月營收數據到數據庫"""
        try:
            if not revenue_data:
                return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            for record in revenue_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue 
                        (stock_id, stock_name, year, month, revenue, revenue_mom, 
                         revenue_yoy, cumulative_revenue, cumulative_yoy, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_id'], record['stock_name'], record['year'], 
                        record['month'], record['revenue'], record['revenue_mom'],
                        record['revenue_yoy'], record['cumulative_revenue'], 
                        record['cumulative_yoy'], record['crawl_date']
                    ))
                    saved_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 儲存記錄失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 成功儲存 {saved_count} 筆月營收數據")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 儲存數據庫失敗: {e}")
            return False
    
    def crawl_multiple_stocks(self, stock_list):
        """批量抓取多支股票的月營收數據"""
        try:
            self.logger.info(f"🚀 開始批量抓取 {len(stock_list)} 支股票的月營收數據...")
            
            all_data = []
            success_count = 0
            
            for i, stock_id in enumerate(stock_list, 1):
                self.logger.info(f"📊 處理第 {i}/{len(stock_list)} 支股票: {stock_id}")
                
                # 抓取數據
                revenue_data = self.crawl_monthly_revenue(stock_id)
                
                if revenue_data:
                    # 儲存到數據庫
                    if self.save_to_database(revenue_data):
                        all_data.extend(revenue_data)
                        success_count += 1
                        self.logger.info(f"✅ {stock_id} 處理完成")
                    else:
                        self.logger.warning(f"⚠️ {stock_id} 儲存失敗")
                else:
                    self.logger.warning(f"⚠️ {stock_id} 抓取失敗")
                
                # 隨機延遲避免被封鎖
                if i < len(stock_list):
                    delay = random.uniform(3, 6)
                    self.logger.info(f"⏳ 等待 {delay:.1f} 秒...")
                    time.sleep(delay)
            
            self.logger.info(f"🎉 批量抓取完成: {success_count}/{len(stock_list)} 支股票成功")
            return all_data
            
        except Exception as e:
            self.logger.error(f"❌ 批量抓取失敗: {e}")
            return []

def test_monthly_revenue_crawler():
    """測試月營收抓取器"""
    print("🚀 測試 GoodInfo 月營收抓取器")
    print("=" * 60)
    
    crawler = GoodInfoMonthlyRevenueCrawler()
    
    # 測試單一股票
    test_stock = '2330'
    print(f"\n📊 測試抓取 {test_stock} 月營收數據...")
    
    revenue_data = crawler.crawl_monthly_revenue(test_stock)
    
    if revenue_data:
        print(f"✅ 成功抓取 {len(revenue_data)} 筆月營收數據")
        
        # 顯示前5筆數據
        print("\n📋 前5筆數據:")
        for i, record in enumerate(revenue_data[:5], 1):
            print(f"{i}. {record['year']}/{record['month']:02d} - "
                  f"營收: {record['revenue']:,.0f}千元, "
                  f"年增率: {record['revenue_yoy']:.1f}%")
        
        # 儲存到數據庫
        if crawler.save_to_database(revenue_data):
            print("✅ 數據已儲存到數據庫")
        
        return True
    else:
        print("❌ 抓取失敗")
        return False

if __name__ == "__main__":
    test_monthly_revenue_crawler()
