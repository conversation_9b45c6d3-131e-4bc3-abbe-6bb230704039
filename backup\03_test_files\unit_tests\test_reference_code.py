#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於參考程式碼的測試版本
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import NoAlertPresentException, TimeoutException
import time
import os
import shutil
import glob

def test_reference_approach():
    """測試參考程式碼的方法"""
    
    # 設定下載目錄
    download_dir = "C:/Users/<USER>/Downloads"
    os.makedirs(download_dir, exist_ok=True)
    
    # 設定 Chrome 選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    })
    # 先不使用無頭模式，方便觀察
    # chrome_options.add_argument("--headless")
    
    driver = None
    try:
        print("🚀 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 目標網址
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        driver.get(url)
        
        # 等待頁面載入
        wait = WebDriverWait(driver, 10)
        time.sleep(5)  # 額外等待以確保動態內容載入
        print("⏳ 頁面載入完成")
        
        # 處理可能的彈出視窗
        try:
            alert = driver.switch_to.alert
            alert.dismiss()
            print("✅ 已處理彈出視窗")
        except NoAlertPresentException:
            print("📝 無彈出視窗")
        except TimeoutException:
            print("⚠️ 處理彈出視窗逾時")
        
        # 檢查頁面標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 先檢查現有的日期選擇器
        print("\n🔍 檢查日期選擇器...")
        try:
            # 查找可能的日期輸入元素
            date_elements = driver.find_elements(By.XPATH, "//input[@type='month']")
            print(f"找到 {len(date_elements)} 個月份輸入框")
            
            for i, elem in enumerate(date_elements):
                value = elem.get_attribute("value")
                name = elem.get_attribute("name")
                id_attr = elem.get_attribute("id")
                print(f"   日期元素 {i+1}: value='{value}', name='{name}', id='{id_attr}'")
            
            # 如果找到日期輸入框，嘗試設定日期範圍
            if len(date_elements) >= 2:
                print("📅 設定日期範圍...")
                # 設定開始日期
                start_date_elem = date_elements[0]
                start_date_elem.clear()
                start_date_elem.send_keys("2023-01")
                print("   開始日期設為: 2023-01")
                
                # 設定結束日期
                end_date_elem = date_elements[1]
                end_date_elem.clear()
                end_date_elem.send_keys("2025-07")
                print("   結束日期設為: 2025-07")
                
                # 查找查詢按鈕
                query_buttons = driver.find_elements(By.XPATH, "//input[@value='查詢']")
                if query_buttons:
                    print("🔍 點擊查詢按鈕...")
                    query_buttons[0].click()
                    time.sleep(5)  # 等待資料刷新
                    print("✅ 查詢完成")
                else:
                    print("⚠️ 未找到查詢按鈕")
            
        except Exception as e:
            print(f"⚠️ 日期設定失敗: {e}")
        
        # 記錄下載前的文件狀態
        print("\n📊 下載前的文件狀態:")
        initial_files = set(glob.glob(os.path.join(download_dir, "*.xls")))
        print(f"   Excel文件數量: {len(initial_files)}")
        
        # 點擊 XLS 匯出按鈕
        print("\n🔍 尋找XLS按鈕...")
        try:
            # 多種方式尋找XLS按鈕
            xls_selectors = [
                "//input[@value='XLS']",
                "//*[contains(text(), 'XLS')]",
                "//input[contains(@onclick, 'export2xls')]",
                "//input[@type='button' and @value='XLS']"
            ]
            
            xls_button = None
            for selector in xls_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        xls_button = elements[0]
                        print(f"✅ 找到XLS按鈕: {selector}")
                        break
                except:
                    continue
            
            if xls_button:
                print("🖱️ 點擊XLS按鈕...")
                
                # 嘗試多種點擊方法
                try:
                    # 方法1: 直接執行onclick事件
                    onclick_code = xls_button.get_attribute("onclick")
                    if onclick_code:
                        driver.execute_script(onclick_code)
                        print("✅ onclick事件執行成功")
                    else:
                        # 方法2: JavaScript點擊
                        driver.execute_script("arguments[0].click();", xls_button)
                        print("✅ JavaScript點擊成功")
                        
                except Exception as e:
                    print(f"⚠️ 點擊失敗: {e}")
                    # 方法3: 直接點擊
                    xls_button.click()
                    print("✅ 直接點擊成功")
                
            else:
                print("❌ 未找到XLS按鈕")
                return False
                
        except Exception as e:
            print(f"❌ XLS按鈕處理失敗: {e}")
            return False
        
        # 等待下載完成
        print("\n⏳ 等待下載完成...")
        download_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < download_timeout:
            current_files = set(glob.glob(os.path.join(download_dir, "*.xls")))
            new_files = current_files - initial_files
            
            if new_files:
                new_file = list(new_files)[0]
                print(f"✅ 檢測到新文件: {os.path.basename(new_file)}")
                
                # 等待文件穩定
                time.sleep(2)
                if os.path.exists(new_file) and os.path.getsize(new_file) > 0:
                    # 重新命名文件
                    timestamp = time.strftime('%Y%m%d_%H%M%S')
                    new_filename = f"2330_台積電_monthly_revenue_2023-01_to_2025-07_{timestamp}.xls"
                    target_path = os.path.join(download_dir, new_filename)
                    
                    try:
                        shutil.move(new_file, target_path)
                        print(f"🎉 文件已重新命名: {new_filename}")
                        return target_path
                    except Exception as e:
                        print(f"⚠️ 重新命名失敗: {e}")
                        return new_file
                        
            time.sleep(2)
        
        print("⏰ 下載超時")
        return None
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None
        
    finally:
        if driver:
            input("按Enter鍵關閉瀏覽器...")
            driver.quit()

if __name__ == "__main__":
    print("🧪 測試參考程式碼方法...")
    print("=" * 50)
    
    result = test_reference_approach()
    
    print("\n" + "=" * 50)
    if result:
        print(f"🎉 測試成功: {result}")
    else:
        print("❌ 測試失敗")
