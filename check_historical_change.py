#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 中歷史資料的 Change 欄位狀況
"""

import sqlite3
import pandas as pd

def check_historical_change():
    """檢查 newprice.db 中歷史資料的 Change 欄位狀況"""
    
    print("=" * 80)
    print("🔍 檢查 newprice.db 中歷史資料的 Change 欄位狀況")
    print("=" * 80)
    
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        conn = sqlite3.connect(newprice_db)
        
        # 檢查整體 Change 欄位統計
        print("\n📊 整體 Change 欄位統計:")
        query_overall = '''
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_change,
                COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_change,
                COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_change,
                MIN(date) as earliest_date,
                MAX(date) as latest_date
            FROM stock_daily_data
        '''
        
        stats_overall = pd.read_sql_query(query_overall, conn)
        
        total = stats_overall.iloc[0]['total_records']
        zero = stats_overall.iloc[0]['zero_change']
        positive = stats_overall.iloc[0]['positive_change']
        negative = stats_overall.iloc[0]['negative_change']
        
        print(f"   總記錄數: {total:,}")
        print(f"   Change = 0: {zero:,} ({zero/total*100:.1f}%)")
        print(f"   Change > 0: {positive:,} ({positive/total*100:.1f}%)")
        print(f"   Change < 0: {negative:,} ({negative/total*100:.1f}%)")
        print(f"   日期範圍: {stats_overall.iloc[0]['earliest_date']} ~ {stats_overall.iloc[0]['latest_date']}")
        
        # 按日期範圍檢查 Change 欄位
        print(f"\n📊 按日期範圍檢查 Change 欄位:")
        
        date_ranges = [
            ('2023-09-01', '2023-09-20', '最近修正前'),
            ('2023-09-21', '2023-09-21', '測試日期'),
            ('2023-01-01', '2023-08-31', '2023年前8個月'),
            ('2022-01-01', '2022-12-31', '2022年全年'),
            ('2021-01-01', '2021-12-31', '2021年全年')
        ]
        
        for start_date, end_date, description in date_ranges:
            query_range = f'''
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_count,
                    COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_count,
                    COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_count
                FROM stock_daily_data
                WHERE date >= '{start_date}' AND date <= '{end_date}'
            '''
            
            stats_range = pd.read_sql_query(query_range, conn)
            
            if stats_range.iloc[0]['total'] > 0:
                total_range = stats_range.iloc[0]['total']
                zero_range = stats_range.iloc[0]['zero_count']
                zero_pct = (zero_range / total_range) * 100
                
                print(f"   {description} ({start_date} ~ {end_date}):")
                print(f"      總數: {total_range:,}, Change=0: {zero_range:,} ({zero_pct:.1f}%)")
        
        # 檢查 0050 的歷史 Change 值
        print(f"\n📊 0050 歷史 Change 值範例:")
        query_0050 = '''
            SELECT date, [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 20
        '''
        
        df_0050 = pd.read_sql_query(query_0050, conn)
        
        for _, row in df_0050.iterrows():
            print(f"   {row['date']}: Close={row['Close']}, Change={row['Change']}")
        
        # 分析結果
        print(f"\n🔍 分析結果:")
        
        if zero / total > 0.8:  # 如果超過80%的記錄Change為0
            print(f"   ❌ 大部分歷史資料的 Change 欄位為 0 ({zero/total*100:.1f}%)")
            print(f"   💡 需要處理歷史資料的 Change 欄位")
            
            # 提供處理選項
            print(f"\n📋 處理選項:")
            print(f"   1. 從 price.db 複製 Change 值到 newprice.db")
            print(f"   2. 根據前一日收盤價計算 Change 值")
            print(f"   3. 保持現狀（只有新爬取的資料有正確的 Change 值）")
            
            return False
        else:
            print(f"   ✅ 歷史資料的 Change 欄位正常")
            return True
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_historical_change()
    
    if not success:
        print(f"\n⚠️ 需要處理歷史資料的 Change 欄位")
    else:
        print(f"\n✅ Change 欄位狀況正常")
