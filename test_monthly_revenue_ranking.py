#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收排行榜功能
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, date

def test_monthly_revenue_db():
    """測試月營收資料庫是否存在和資料結構"""
    print("=" * 60)
    print("🔍 測試月營收資料庫")
    print("=" * 60)
    
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 月營收資料庫不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(monthly_report)")
        columns = cursor.fetchall()
        print(f"📋 資料表欄位:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM monthly_report")
        total_count = cursor.fetchone()[0]
        print(f"📊 總資料筆數: {total_count:,}")
        
        # 檢查最新資料日期
        cursor.execute("SELECT MAX(date) FROM monthly_report")
        latest_date = cursor.fetchone()[0]
        print(f"📅 最新資料日期: {latest_date}")
        
        # 檢查2025年7月的資料
        cursor.execute("SELECT COUNT(*) FROM monthly_report WHERE date LIKE '2025-07%'")
        july_count = cursor.fetchone()[0]
        print(f"📊 2025年7月資料筆數: {july_count:,}")
        
        # 檢查資料樣本
        cursor.execute("""
            SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收 
            FROM monthly_report 
            WHERE date LIKE '2025-07%' 
            LIMIT 5
        """)
        samples = cursor.fetchall()
        print(f"📋 資料樣本:")
        for sample in samples:
            print(f"   {sample}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫檢查失敗: {e}")
        return False

def test_calculate_revenue_growth():
    """測試營收成長率計算"""
    print("\n" + "=" * 60)
    print("🧮 測試營收成長率計算")
    print("=" * 60)
    
    # 創建測試資料
    test_data = {
        'stock_id': ['2330', '2317', '2454'],
        'stock_name': ['台積電', '鴻海', '聯發科'],
        '當月營收': [100000, 80000, 60000],
        '上月營收': [95000, 85000, 55000],
        '去年當月營收': [90000, 75000, 50000]
    }
    
    df = pd.DataFrame(test_data)
    print("📊 測試資料:")
    print(df)
    
    # 計算YoY和MoM
    try:
        # 轉換數值欄位
        df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
        df['上月營收'] = pd.to_numeric(df['上月營收'], errors='coerce')
        df['去年當月營收'] = pd.to_numeric(df['去年當月營收'], errors='coerce')
        
        # 計算YoY (Year over Year) 成長率
        df['YoY%'] = ((df['當月營收'] - df['去年當月營收']) / df['去年當月營收'] * 100).round(2)
        
        # 計算MoM (Month over Month) 成長率
        df['MoM%'] = ((df['當月營收'] - df['上月營收']) / df['上月營收'] * 100).round(2)
        
        print("\n📈 計算結果:")
        print(df[['stock_id', 'stock_name', 'YoY%', 'MoM%']])
        
        # 測試排序
        print("\n🏆 YoY排序 (前3名):")
        yoy_sorted = df.sort_values('YoY%', ascending=False)
        for idx, (_, row) in enumerate(yoy_sorted.iterrows(), 1):
            print(f"   {idx}. {row['stock_id']} {row['stock_name']} YoY: {row['YoY%']:.2f}%")
        
        print("\n🏆 MoM排序 (前3名):")
        mom_sorted = df.sort_values('MoM%', ascending=False)
        for idx, (_, row) in enumerate(mom_sorted.iterrows(), 1):
            print(f"   {idx}. {row['stock_id']} {row['stock_name']} MoM: {row['MoM%']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 計算失敗: {e}")
        return False

def test_date_logic():
    """測試日期判斷邏輯"""
    print("\n" + "=" * 60)
    print("📅 測試日期判斷邏輯")
    print("=" * 60)
    
    test_dates = [
        date(2025, 7, 5),   # 7月5日 - 應該使用API
        date(2025, 7, 10),  # 7月10日 - 應該使用API
        date(2025, 7, 15),  # 7月15日 - 應該檢查資料庫
        date(2025, 7, 29),  # 7月29日 - 應該檢查資料庫
    ]
    
    for test_date in test_dates:
        should_use_api = test_date.day <= 10
        print(f"📅 {test_date.strftime('%Y-%m-%d')} (第{test_date.day}日)")
        print(f"   {'✅ 使用API' if should_use_api else '📊 檢查資料庫'}")
    
    return True

def test_ranking_data_format():
    """測試排行榜資料格式"""
    print("\n" + "=" * 60)
    print("📋 測試排行榜資料格式")
    print("=" * 60)
    
    # 模擬排行榜資料
    ranking_data = []
    test_stocks = [
        ('2330', '台積電', 120000, 15.5, 8.2),
        ('2317', '鴻海', 95000, 12.3, -2.1),
        ('2454', '聯發科', 85000, 25.8, 15.6),
    ]
    
    for idx, (stock_id, stock_name, revenue, yoy, mom) in enumerate(test_stocks, 1):
        ranking_data.append({
            '排名': idx,
            '股票代碼': stock_id,
            '股票名稱': stock_name,
            '當月營收': f"{revenue:,.0f}",
            'YoY%': f"{yoy:.2f}%",
            'MoM%': f"{mom:.2f}%",
            '日期': '2025-07-29'
        })
    
    print("🏆 YoY排行榜格式:")
    for data in ranking_data:
        print(f"   {data['排名']}. {data['股票代碼']} {data['股票名稱']} "
              f"營收:{data['當月營收']} YoY:{data['YoY%']}")
    
    print("\n🏆 MoM排行榜格式:")
    for data in ranking_data:
        print(f"   {data['排名']}. {data['股票代碼']} {data['股票名稱']} "
              f"營收:{data['當月營收']} MoM:{data['MoM%']}")
    
    return True

def main():
    """主測試函數"""
    print("🚀 月營收排行榜功能測試")
    print("=" * 60)
    
    tests = [
        ("月營收資料庫檢查", test_monthly_revenue_db),
        ("營收成長率計算", test_calculate_revenue_growth),
        ("日期判斷邏輯", test_date_logic),
        ("排行榜資料格式", test_ranking_data_format),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通過' if result else '失敗'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 異常 - {e}")
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總結: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！月營收排行榜功能準備就緒。")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")

if __name__ == "__main__":
    main()
