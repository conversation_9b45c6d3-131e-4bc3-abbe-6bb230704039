#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試查詢結果問題
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_query_result():
    """調試查詢結果問題"""
    
    print("=" * 60)
    print("🔍 調試查詢結果問題")
    print("=" * 60)
    
    try:
        # 跳過 get 函數測試，直接查詢資料庫
        print("📊 跳過 get 函數測試，直接查詢資料庫...")
        
        # 直接查詢資料庫
        print(f"\n📊 直接查詢資料庫:")
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        
        # 查詢 0050 的最新資料
        query = '''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close], Volume, [Transaction], TradeValue
            FROM stock_daily_data
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            print(f"   ✅ 找到 {len(df)} 筆 0050 資料:")
            for _, row in df.iterrows():
                print(f"   {row['date']}: {row['stock_id']} | {row['stock_name']} | {row['listing_status']} | {row['industry']} | 收盤:{row['Close']}")
        else:
            print(f"   ❌ 未找到 0050 資料")
        
        # 檢查資料庫結構
        print(f"\n🏗️ 檢查資料庫結構:")
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = cursor.fetchall()
        
        print(f"   資料表欄位:")
        for col in columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 檢查是否有 NULL 值
        print(f"\n🔍 檢查 NULL 值:")
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(stock_name) as has_name,
                COUNT(listing_status) as has_status,
                COUNT(industry) as has_industry
            FROM stock_daily_data 
            WHERE stock_id = '0050'
        ''')
        null_stats = cursor.fetchone()
        
        print(f"   0050 統計:")
        print(f"     總記錄數: {null_stats[0]}")
        print(f"     有股票名稱: {null_stats[1]}")
        print(f"     有上市狀態: {null_stats[2]}")
        print(f"     有產業別: {null_stats[3]}")
        
        # 檢查最新日期的資料
        print(f"\n📅 檢查最新日期的資料:")
        cursor.execute('''
            SELECT MAX(date) as latest_date
            FROM stock_daily_data
        ''')
        latest_date = cursor.fetchone()[0]
        print(f"   最新日期: {latest_date}")
        
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, [Close]
            FROM stock_daily_data 
            WHERE date = ? AND stock_id LIKE '00%'
            ORDER BY stock_id
            LIMIT 10
        ''', (latest_date,))
        latest_etfs = cursor.fetchall()
        
        print(f"   最新日期的 ETF 資料:")
        for stock_id, name, status, industry, close in latest_etfs:
            status_display = status if status else 'NULL'
            industry_display = industry if industry else 'NULL'
            print(f"     {stock_id}: {name} | {status_display} | {industry_display} | {close}")
        
        conn.close()
        
        # 測試你的查詢方式
        print(f"\n🧪 模擬你的查詢方式:")
        
        # 假設你是這樣查詢的
        conn = sqlite3.connect(newprice_db)
        
        # 模擬可能的查詢
        test_queries = [
            "SELECT * FROM stock_daily_data WHERE stock_id = '0050' ORDER BY date DESC LIMIT 7",
            "SELECT stock_id, stock_name, listing_status, industry, Volume, Transaction, TradeValue FROM stock_daily_data WHERE stock_id = '0050' ORDER BY date DESC LIMIT 7",
            f"SELECT * FROM stock_daily_data WHERE date = '{latest_date}' AND stock_id LIKE '00%' ORDER BY stock_id LIMIT 7"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   測試查詢 {i}:")
            print(f"   SQL: {query}")
            
            try:
                df = pd.read_sql_query(query, conn)
                if not df.empty:
                    print(f"   結果: {len(df)} 筆資料")
                    for _, row in df.iterrows():
                        status = row.get('listing_status', 'N/A')
                        industry = row.get('industry', 'N/A')
                        print(f"     {row['stock_id']}: {status} | {industry}")
                else:
                    print(f"   結果: 無資料")
            except Exception as e:
                print(f"   錯誤: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_query_result()
