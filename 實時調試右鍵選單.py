#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
實時調試右鍵選單功能
"""

import sys
import os
import logging
from datetime import datetime

# 設置詳細日誌
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'右鍵選單調試_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
    ]
)

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函數"""
    print("=" * 80)
    print("🐛 實時調試右鍵選單功能")
    print("=" * 80)
    print("📋 使用說明:")
    print("1. 程式啟動後，請執行月營收排行榜查詢")
    print("2. 在左側「全部股票」或「選股結果」列表中右鍵點擊股票")
    print("3. 觀察控制台日誌輸出，查看詳細的執行過程")
    print("4. 檢查是否出現「月營收綜合評估」選項")
    print("=" * 80)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        print("\n📊 當前程式狀態:")
        print(f"  - 右側表格欄位數: {gui.result_table.columnCount()}")
        print(f"  - 右側表格行數: {gui.result_table.rowCount()}")
        print(f"  - 左側全部股票列表項目數: {gui.all_stocks_list.count()}")
        print(f"  - 左側選股結果列表項目數: {gui.filtered_stocks_list.count()}")
        
        # 檢查右鍵選單設置
        print(f"\n🖱️ 右鍵選單設置檢查:")
        print(f"  - 全部股票列表右鍵政策: {gui.all_stocks_list.contextMenuPolicy()}")
        print(f"  - 選股結果列表右鍵政策: {gui.filtered_stocks_list.contextMenuPolicy()}")
        print(f"  - 結果表格右鍵政策: {gui.result_table.contextMenuPolicy()}")
        
        # 檢查信號連接
        all_stocks_receivers = gui.all_stocks_list.receivers(gui.all_stocks_list.customContextMenuRequested)
        filtered_stocks_receivers = gui.filtered_stocks_list.receivers(gui.filtered_stocks_list.customContextMenuRequested)
        result_table_receivers = gui.result_table.receivers(gui.result_table.customContextMenuRequested)
        
        print(f"\n📡 信號連接檢查:")
        print(f"  - 全部股票列表信號接收器數量: {all_stocks_receivers}")
        print(f"  - 選股結果列表信號接收器數量: {filtered_stocks_receivers}")
        print(f"  - 結果表格信號接收器數量: {result_table_receivers}")
        
        # 添加調試鉤子
        original_show_context_menu_for_list = gui.show_stock_context_menu_for_list
        original_is_monthly_revenue_ranking = gui.is_monthly_revenue_ranking
        original_find_stock_in_result_table = gui.find_stock_in_result_table
        
        def debug_show_context_menu_for_list(position):
            print(f"\n🔥 [調試] 左側列表右鍵選單被觸發！")
            print(f"🔥 [調試] 觸發位置: {position}")
            print(f"🔥 [調試] 觸發時間: {datetime.now()}")
            return original_show_context_menu_for_list(position)
        
        def debug_is_monthly_revenue_ranking():
            result = original_is_monthly_revenue_ranking()
            print(f"🔥 [調試] 月營收排行榜檢測結果: {result}")
            return result
        
        def debug_find_stock_in_result_table(stock_code):
            print(f"🔥 [調試] 搜尋股票: {stock_code}")
            result = original_find_stock_in_result_table(stock_code)
            print(f"🔥 [調試] 搜尋結果: {result}")
            return result
        
        # 替換為調試版本
        gui.show_stock_context_menu_for_list = debug_show_context_menu_for_list
        gui.is_monthly_revenue_ranking = debug_is_monthly_revenue_ranking
        gui.find_stock_in_result_table = debug_find_stock_in_result_table
        
        print(f"\n🔧 調試鉤子已安裝")
        
        # 顯示GUI
        print(f"\n🖥️ 顯示主程式GUI...")
        print(f"📋 請按照以下步驟進行測試:")
        print(f"1. 選擇日期（建議：2025-07-29）")
        print(f"2. 選擇「月營收排行榜 (YoY排序) + 綜合評分」")
        print(f"3. 點擊「執行排行」")
        print(f"4. 等待查詢完成")
        print(f"5. 切換到左側「全部股票」或「選股結果」標籤")
        print(f"6. 右鍵點擊任一股票")
        print(f"7. 觀察控制台輸出和右鍵選單內容")
        print(f"=" * 80)
        
        gui.show()
        
        # 執行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 程式啟動失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
