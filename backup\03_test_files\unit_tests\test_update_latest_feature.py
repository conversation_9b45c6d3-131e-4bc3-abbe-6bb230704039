#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試更新至最新月份功能
"""

import os
import sqlite3
from datetime import datetime

def test_update_latest_feature():
    """測試更新至最新月份功能"""
    print("🧪 測試更新至最新月份功能")
    print("=" * 50)
    
    try:
        # 檢查全股票數據庫
        download_dir = "D:/Finlab/history/tables/monthly_revenue"
        all_stocks_db = os.path.join(download_dir, "all_stocks_revenue.db")
        
        if os.path.exists(all_stocks_db):
            print("✅ 找到全股票數據庫")
            
            # 檢查現有資料
            conn = sqlite3.connect(all_stocks_db)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM all_stocks_revenue")
            total_stocks = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT data_month, crawl_date, COUNT(*) as stock_count
                FROM all_stocks_revenue 
                GROUP BY data_month, crawl_date
                ORDER BY crawl_date DESC
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            
            if result:
                data_month, crawl_date, stock_count = result
                print(f"📊 現有資料狀態:")
                print(f"   資料月份: {data_month}")
                print(f"   股票數量: {stock_count} 支")
                print(f"   最後更新: {crawl_date}")
                
                # 檢查是否需要更新
                try:
                    last_update = datetime.strptime(crawl_date, '%Y-%m-%d %H:%M:%S')
                    now = datetime.now()
                    days_since_update = (now - last_update).days
                    
                    print(f"📅 距離上次更新: {days_since_update} 天")
                    
                    if days_since_update == 0:
                        print("✅ 資料是最新的")
                    elif days_since_update <= 7:
                        print("⚠️ 建議更新資料")
                    else:
                        print("❌ 資料過舊，強烈建議更新")
                        
                except ValueError:
                    print("⚠️ 無法解析更新時間")
                
                # 顯示成長率統計
                cursor.execute("""
                    SELECT 
                        COUNT(CASE WHEN yoy_change_pct > 20 THEN 1 END) as high_growth,
                        COUNT(CASE WHEN yoy_change_pct BETWEEN 0 AND 20 THEN 1 END) as positive_growth,
                        COUNT(CASE WHEN yoy_change_pct < 0 THEN 1 END) as negative_growth,
                        AVG(yoy_change_pct) as avg_growth
                    FROM all_stocks_revenue 
                    WHERE yoy_change_pct IS NOT NULL
                """)
                
                growth_stats = cursor.fetchone()
                if growth_stats:
                    high, positive, negative, avg_growth = growth_stats
                    print(f"\n📈 成長率分析:")
                    print(f"   高成長(>20%): {high} 支")
                    print(f"   正成長(0-20%): {positive} 支")
                    print(f"   負成長(<0%): {negative} 支")
                    print(f"   平均年增率: {avg_growth:.1f}%" if avg_growth else "   平均年增率: N/A")
                
                # 顯示營收前5名
                cursor.execute("""
                    SELECT stock_id, stock_name, current_month_revenue, yoy_change_pct
                    FROM all_stocks_revenue 
                    WHERE current_month_revenue IS NOT NULL
                    ORDER BY current_month_revenue DESC 
                    LIMIT 5
                """)
                
                top_revenue = cursor.fetchall()
                if top_revenue:
                    print(f"\n💰 營收前5名:")
                    for i, (stock_id, name, revenue, yoy) in enumerate(top_revenue, 1):
                        revenue_str = f"{revenue/1000:.0f}萬"
                        yoy_str = f"{yoy:+.1f}%" if yoy else "N/A"
                        print(f"   {i}. {stock_id} {name:<8} 營收:{revenue_str:<10} 年增率:{yoy_str}")
                
            else:
                print("❌ 數據庫中沒有資料")
            
            conn.close()
            
        else:
            print("❌ 未找到全股票數據庫")
            print("💡 請先使用GUI下載全股票營收資料")
        
        print(f"\n🎯 更新至最新月份功能說明:")
        print(f"1. 點擊「🔄 更新至最新月份」按鈕")
        print(f"2. 系統會自動下載當月最新的全股票營收資料")
        print(f"3. 自動更新數據庫中的資料")
        print(f"4. 顯示更新統計和結果")
        
        print(f"\n🔄 自動更新功能說明:")
        print(f"1. 勾選「啟用每日自動更新」")
        print(f"2. 系統會在每天早上9點自動更新")
        print(f"3. 點擊「檢查更新狀態」查看最新狀態")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def show_gui_features():
    """展示GUI新功能"""
    print(f"\n🎨 GUI新功能展示:")
    print("=" * 30)
    
    print("✨ 新增功能:")
    print("1. 🔄 更新至最新月份按鈕")
    print("   - 快速更新當月最新營收資料")
    print("   - 自動處理和儲存到數據庫")
    print("   - 顯示更新統計資訊")
    
    print("\n2. 🔄 自動更新設定")
    print("   - 啟用每日自動更新選項")
    print("   - 顯示最後更新時間")
    print("   - 自動化資料維護")
    
    print("\n3. 📊 檢查更新狀態按鈕")
    print("   - 檢查資料新舊程度")
    print("   - 顯示詳細統計資訊")
    print("   - 更新建議提示")
    
    print("\n4. 📈 增強的統計顯示")
    print("   - 成長率分析")
    print("   - 營收排名")
    print("   - 市場概況")

def main():
    """主函數"""
    print("🧪 更新至最新月份功能測試")
    print("=" * 50)
    
    # 測試功能
    success = test_update_latest_feature()
    
    if success:
        show_gui_features()
        print(f"\n🎉 測試完成！")
        print(f"✅ 更新至最新月份功能已準備就緒")
        print(f"💡 請使用GUI測試完整功能")
    else:
        print(f"\n❌ 測試失敗")
    
    input(f"\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
