#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復缺少模組的問題
檢查並修復編譯後可執行檔中缺少的模組
"""

import sys
import os

def check_and_fix_imports():
    """檢查並修復導入問題"""
    print("🔍 檢查系統模組...")
    
    # 檢查關鍵模組
    critical_modules = [
        'inspect',
        'importlib',
        'importlib.util',
        'sqlite3',
        'json',
        'csv',
        'datetime',
        'threading',
        'concurrent.futures',
        'logging',
        'traceback',
        'os',
        'sys',
        'time',
        'random',
        'calendar'
    ]
    
    missing_modules = []
    
    for module in critical_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ 發現缺少的模組: {missing_modules}")
        return False
    else:
        print("\n✅ 所有關鍵模組檢查通過")
        return True

def create_module_compatibility_layer():
    """創建模組兼容性層"""
    print("🔧 創建模組兼容性層...")
    
    # 為缺少的模組創建替代實現
    compatibility_code = '''
# 模組兼容性層
import sys

# 確保 inspect 模組可用
try:
    import inspect
except ImportError:
    print("⚠️ inspect 模組不可用，創建替代實現")
    class MockInspect:
        @staticmethod
        def signature(func):
            # 簡單的簽名模擬
            class MockSignature:
                def __init__(self):
                    self.parameters = {}
            return MockSignature()
    
    sys.modules['inspect'] = MockInspect()

# 確保 importlib 模組可用
try:
    import importlib
    import importlib.util
except ImportError:
    print("⚠️ importlib 模組不可用，創建替代實現")
    class MockImportlib:
        @staticmethod
        def import_module(name):
            return __import__(name)
        
        class util:
            @staticmethod
            def spec_from_file_location(name, location):
                return None
            
            @staticmethod
            def module_from_spec(spec):
                return None
    
    sys.modules['importlib'] = MockImportlib()
    sys.modules['importlib.util'] = MockImportlib.util()

print("✅ 模組兼容性層創建完成")
'''
    
    # 將兼容性代碼寫入文件
    with open('module_compatibility.py', 'w', encoding='utf-8') as f:
        f.write(compatibility_code)
    
    print("✅ 兼容性層文件已創建: module_compatibility.py")

def update_main_program():
    """更新主程式以包含兼容性檢查"""
    print("🔧 更新主程式...")
    
    # 在主程式開頭添加兼容性檢查
    compatibility_import = '''
# 模組兼容性檢查和修復
try:
    # 載入兼容性層
    import module_compatibility
except ImportError:
    # 如果兼容性層不存在，直接創建必要的替代
    import sys
    
    # inspect 模組替代
    if 'inspect' not in sys.modules:
        try:
            import inspect
        except ImportError:
            class MockInspect:
                @staticmethod
                def signature(func):
                    class MockSignature:
                        def __init__(self):
                            self.parameters = {}
                    return MockSignature()
            sys.modules['inspect'] = MockInspect()
    
    # importlib 模組替代
    if 'importlib' not in sys.modules:
        try:
            import importlib
        except ImportError:
            class MockImportlib:
                @staticmethod
                def import_module(name):
                    return __import__(name)
            sys.modules['importlib'] = MockImportlib()

'''
    
    # 讀取原始主程式
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 在導入部分之前插入兼容性檢查
        lines = original_content.split('\n')
        
        # 找到第一個實際的導入語句
        insert_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') and not line.strip().startswith('#!/') and not line.strip().startswith('#'):
                insert_index = i
                break
        
        # 插入兼容性代碼
        compatibility_lines = compatibility_import.strip().split('\n')
        lines = lines[:insert_index] + compatibility_lines + [''] + lines[insert_index:]
        
        # 寫回文件
        modified_content = '\n'.join(lines)
        with open('O3mh_gui_v21_optimized_fixed.py', 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("✅ 已創建修復版本: O3mh_gui_v21_optimized_fixed.py")
        return True
        
    except Exception as e:
        print(f"❌ 更新主程式失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 開始修復缺少模組的問題...")
    print("=" * 50)
    
    # 步驟1: 檢查模組
    if not check_and_fix_imports():
        print("⚠️ 發現缺少的模組，繼續修復...")
    
    # 步驟2: 創建兼容性層
    create_module_compatibility_layer()
    
    # 步驟3: 更新主程式
    if update_main_program():
        print("\n🎉 修復完成！")
        print("\n📋 下一步:")
        print("1. 使用修復版本重新編譯:")
        print("   python compile_to_exe.py")
        print("2. 或者直接測試修復版本:")
        print("   python O3mh_gui_v21_optimized_fixed.py")
    else:
        print("\n❌ 修復失敗，請手動檢查問題")

if __name__ == "__main__":
    main()
