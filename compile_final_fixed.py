#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import shutil
import time

def main():
    print("開始編譯最終修復版本...")
    print("目標：完全解決 pyqtgraph 錯誤問題")
    print()
    
    # 清理環境
    if os.path.exists('build'):
        shutil.rmtree('build')
    time.sleep(2)
    
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'final_fixed_compile.spec']
    
    try:
        print("正在編譯...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            exe_path = 'dist/StockAnalyzer_Final_Fixed.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"編譯成功！")
                print(f"檔案: {exe_path}")
                print(f"大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                launcher = """@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終修復版

echo.
echo ========================================
echo    台股智能選股系統 v22.2 - 最終修復版
echo ========================================
echo.

if exist "dist\\StockAnalyzer_Final_Fixed.exe" (
    echo 找到最終修復版
    echo 正在啟動...
    echo.
    echo 最終修復版特點：
    echo    完全解決 pyqtgraph 錯誤
    echo    選股結果正常顯示
    echo    完整的策略交集分析
    echo    穩定的系統運行
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Final_Fixed.exe"
    
    echo 最終修復版已啟動！
    echo.
    
) else (
    echo 錯誤：找不到最終修復版
    echo.
    pause
    exit /b 1
)

echo 修復的問題：
echo    解決 pyqtgraph 模組錯誤
echo    修復選股結果顯示
echo    完善策略交集分析
echo    提升系統穩定性
echo.

timeout /t 3 >nul
"""
                
                with open('啟動最終修復版.bat', 'w', encoding='utf-8') as f:
                    f.write(launcher)
                
                print("創建啟動腳本: 啟動最終修復版.bat")
                print()
                print("最終修復版編譯完成！")
                print("特點：")
                print("  ✅ 完全解決 pyqtgraph 錯誤")
                print("  ✅ 選股結果正常顯示")
                print("  ✅ 完整的策略交集分析")
                print("  ✅ 穩定的系統運行")
                print()
                print("使用方法：")
                print("  雙擊執行: 啟動最終修復版.bat")
                return True
            else:
                print("找不到編譯後的檔案")
                return False
        else:
            print("編譯失敗！")
            if result.stderr:
                print("錯誤:", result.stderr[-1000:])
            return False
            
    except Exception as e:
        print(f"編譯過程錯誤: {e}")
        return False

if __name__ == "__main__":
    main()
