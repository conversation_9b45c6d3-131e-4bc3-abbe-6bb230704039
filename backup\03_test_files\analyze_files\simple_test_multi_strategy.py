#!/usr/bin/env python3
"""
簡單測試多策略分析系統的改進功能
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """測試導入功能"""
    try:
        from multi_strategy_gui import MultiStrategyGUI
        print("✅ MultiStrategyGUI 導入成功")
        return True
    except Exception as e:
        print(f"❌ MultiStrategyGUI 導入失敗: {e}")
        return False

def test_class_methods():
    """測試類別方法"""
    try:
        from multi_strategy_gui import MultiStrategyGUI
        
        # 檢查新增的方法是否存在
        methods_to_check = [
            'on_stock_input_changed',
            'on_stock_combo_changed', 
            'search_and_select_stock',
            'auto_start_analysis',
            'select_previous_stock',
            'select_next_stock',
            'update_navigation_buttons'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MultiStrategyGUI, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                
        return True
    except Exception as e:
        print(f"❌ 方法檢查失敗: {e}")
        return False

def test_gui_creation():
    """測試GUI創建"""
    try:
        from PyQt6.QtWidgets import QApplication
        from multi_strategy_gui import MultiStrategyGUI
        
        app = QApplication([])
        window = MultiStrategyGUI()
        
        # 檢查新增的UI元件
        ui_components = [
            ('stock_input', 'QLineEdit'),
            ('prev_btn', 'QPushButton'),
            ('next_btn', 'QPushButton'),
            ('analyze_btn', 'QPushButton')
        ]
        
        for attr_name, expected_type in ui_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                print(f"✅ UI元件 {attr_name} ({expected_type}) 存在")
            else:
                print(f"❌ UI元件 {attr_name} 不存在")
        
        # 檢查按鈕文字
        if hasattr(window, 'analyze_btn'):
            button_text = window.analyze_btn.text()
            if "開始分析" in button_text:
                print("✅ 分析按鈕名稱正確")
            else:
                print(f"❌ 分析按鈕名稱錯誤: {button_text}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ GUI創建測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始簡單測試多策略分析系統改進功能...")
    print("=" * 50)
    
    tests = [
        ("導入測試", test_import),
        ("方法檢查", test_class_methods),
        ("GUI創建測試", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通過")
        else:
            print(f"❌ {test_name} 失敗")
    
    print("\n" + "=" * 50)
    print(f"🎉 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎊 所有測試通過！多策略分析系統改進功能正常！")
        print("\n📋 改進功能摘要:")
        print("   • ✅ 手動輸入股票代碼/名稱")
        print("   • ✅ 自動開始分析")
        print("   • ✅ 前一檔/後一檔導航按鈕")
        print("   • ✅ 按鈕名稱改為'開始分析'")
        print("   • ✅ 智能股票搜尋匹配")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")

if __name__ == '__main__':
    main()
