#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版自動股價爬蟲程式 - 替代原有的price_crawler_auto(HL).py
使用統一爬蟲系統爬取資料，並轉換更新至price.db
完全相容原有的price.db格式和功能

執行方式：
1. 手動執行：python new_price_crawler_auto.py
2. 排程執行：每日自動執行
3. 參數執行：python new_price_crawler_auto.py --days 7

功能特色：
- 使用先進的爬蟲技術
- 自動錯誤處理和重試
- 完全相容price.db格式
- 詳細的執行日誌
- 支援日期範圍參數
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
import argparse
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

# 設定日誌
def setup_logging():
    """設定日誌系統"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_filename = os.path.join(log_dir, f"price_crawler_auto_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

# 初始化日誌
logger = setup_logging()


class NewPriceCrawlerAuto:
    """新版自動股價爬蟲類別"""
    
    def __init__(self, price_db_path: str = "db/price.db"):
        """
        初始化爬蟲
        
        Args:
            price_db_path: price.db的路徑
        """
        self.price_db_path = price_db_path
        self.ensure_price_db_exists()
        
        logger.info("🚀 新版自動股價爬蟲初始化完成")
        logger.info(f"📁 目標資料庫: {self.price_db_path}")
    
    def ensure_price_db_exists(self):
        """確保price.db資料庫和目錄存在"""
        # 確保目錄存在
        db_dir = os.path.dirname(self.price_db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
        
        # 如果資料庫不存在，創建基本結構
        if not os.path.exists(self.price_db_path):
            logger.info("📋 創建新的price.db資料庫")
            self.create_price_db_structure()
        else:
            logger.info("✅ price.db資料庫已存在")
    
    def create_price_db_structure(self):
        """創建price.db的基本結構（相容原有格式）"""
        conn = sqlite3.connect(self.price_db_path)
        cursor = conn.cursor()
        
        # 創建與原有price.db相容的表結構
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT NOT NULL,
                date TEXT NOT NULL,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Volume REAL,
                PRIMARY KEY (stock_id, date)
            )
        ''')
        
        # 創建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_daily_data(stock_id, date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON stock_daily_data(date)')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ price.db基本結構創建完成")
    
    def crawl_daily_data(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        爬取每日交易資料
        
        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            
        Returns:
            爬取結果統計
        """
        try:
            # 導入統一爬蟲系統
            from taiwan_stock_price_crawler import gen_task_parameter_list, crawler
            
            # 設定預設日期範圍
            if not start_date:
                start_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            logger.info(f"📅 爬取日期範圍: {start_date} 至 {end_date}")
            
            # 生成爬取任務列表
            task_list = gen_task_parameter_list(start_date, end_date)
            total_tasks = len(task_list)
            
            logger.info(f"📋 共 {total_tasks} 個爬取任務")
            
            success_count = 0
            error_count = 0
            total_records = 0
            
            # 執行爬取任務
            for i, task in enumerate(task_list, 1):
                try:
                    logger.info(f"🔄 執行任務 {i}/{total_tasks}: {task['data_source'].upper()} {task['date']}")
                    
                    # 執行爬取
                    df = crawler(task)
                    
                    if not df.empty:
                        # 轉換並儲存到price.db
                        saved_count = self.save_to_price_db(df, task['data_source'])
                        
                        if saved_count > 0:
                            success_count += 1
                            total_records += saved_count
                            logger.info(f"✅ {task['data_source'].upper()} {task['date']}: {saved_count} 筆")
                        else:
                            error_count += 1
                            logger.warning(f"⚠️ {task['data_source'].upper()} {task['date']}: 儲存失敗")
                    else:
                        logger.warning(f"⚠️ {task['data_source'].upper()} {task['date']}: 無資料")
                        
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ {task['data_source'].upper()} {task['date']}: {e}")
            
            # 返回結果統計
            result = {
                'success': success_count > 0,
                'total_tasks': total_tasks,
                'success_count': success_count,
                'error_count': error_count,
                'total_records': total_records,
                'start_date': start_date,
                'end_date': end_date
            }
            
            logger.info(f"🎉 爬取完成！成功: {success_count}, 錯誤: {error_count}, 總記錄: {total_records}")
            
            return result
            
        except ImportError as e:
            logger.error(f"❌ 爬蟲模組導入失敗: {e}")
            return {'success': False, 'error': f'模組導入失敗: {e}'}
        
        except Exception as e:
            logger.error(f"❌ 爬取過程發生錯誤: {e}")
            return {'success': False, 'error': str(e)}
    
    def save_to_price_db(self, df: pd.DataFrame, data_source: str) -> int:
        """
        將資料轉換並儲存到price.db
        
        Args:
            df: 爬取的資料DataFrame
            data_source: 資料來源 (twse/tpex)
            
        Returns:
            儲存的記錄數
        """
        if df.empty:
            return 0
        
        try:
            # 轉換為price.db格式
            df_converted = self.convert_to_price_db_format(df)
            
            if df_converted.empty:
                logger.warning("⚠️ 資料轉換後為空")
                return 0
            
            # 儲存到price.db
            conn = sqlite3.connect(self.price_db_path)
            
            saved_count = 0
            for _, row in df_converted.iterrows():
                try:
                    # 使用INSERT OR REPLACE避免重複資料
                    conn.execute('''
                        INSERT OR REPLACE INTO stock_daily_data 
                        (stock_id, date, Open, High, Low, Close, Volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row['stock_id'],
                        row['date'],
                        row['Open'],
                        row['High'],
                        row['Low'],
                        row['Close'],
                        row['Volume']
                    ))
                    saved_count += 1
                    
                except Exception as e:
                    logger.warning(f"⚠️ 儲存單筆資料失敗: {e}")
            
            conn.commit()
            conn.close()
            
            return saved_count
            
        except Exception as e:
            logger.error(f"❌ 儲存到price.db失敗: {e}")
            return 0
    
    def convert_to_price_db_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        將爬取的資料轉換為price.db格式
        
        Args:
            df: 原始爬取資料
            
        Returns:
            轉換後的DataFrame
        """
        try:
            df_converted = df.copy()
            
            # 標準化欄位名稱對應
            column_mapping = {
                # 統一爬蟲格式 -> price.db格式
                'StockID': 'stock_id',
                'Date': 'date',
                'Max': 'High',
                'Min': 'Low',
                'TradeVolume': 'Volume',
                
                # 其他可能的格式
                'stock_code': 'stock_id',
                'trading_date': 'date',
                'open_price': 'Open',
                'high_price': 'High',
                'low_price': 'Low',
                'close_price': 'Close',
                'volume': 'Volume'
            }
            
            # 重新命名欄位
            for old_name, new_name in column_mapping.items():
                if old_name in df_converted.columns:
                    df_converted = df_converted.rename(columns={old_name: new_name})
            
            # 確保必要欄位存在
            required_columns = ['stock_id', 'date', 'Open', 'High', 'Low', 'Close', 'Volume']
            
            for col in required_columns:
                if col not in df_converted.columns:
                    if col in ['Open', 'High', 'Low', 'Close']:
                        df_converted[col] = 0.0
                    elif col == 'Volume':
                        df_converted[col] = 0
                    else:
                        df_converted[col] = ''
            
            # 只保留必要欄位
            df_converted = df_converted[required_columns]
            
            # 資料清理
            df_converted = df_converted.dropna(subset=['stock_id', 'date'])
            df_converted = df_converted[df_converted['stock_id'] != '']
            df_converted = df_converted[df_converted['date'] != '']
            
            # 確保數值欄位為正確型別
            numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            for col in numeric_columns:
                df_converted[col] = pd.to_numeric(df_converted[col], errors='coerce').fillna(0)
            
            logger.info(f"📊 資料轉換完成: {len(df_converted)} 筆記錄")
            
            return df_converted
            
        except Exception as e:
            logger.error(f"❌ 資料轉換失敗: {e}")
            return pd.DataFrame()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """獲取price.db統計資訊"""
        try:
            conn = sqlite3.connect(self.price_db_path)
            cursor = conn.cursor()
            
            # 總記錄數
            cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
            total_records = cursor.fetchone()[0]
            
            # 股票數量
            cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data')
            stock_count = cursor.fetchone()[0]
            
            # 日期範圍
            cursor.execute('SELECT MIN(date), MAX(date) FROM stock_daily_data')
            date_range = cursor.fetchone()
            
            # 最新資料日期
            cursor.execute('SELECT date, COUNT(*) FROM stock_daily_data GROUP BY date ORDER BY date DESC LIMIT 1')
            latest_data = cursor.fetchone()
            
            conn.close()
            
            stats = {
                'total_records': total_records,
                'stock_count': stock_count,
                'date_range': {
                    'start': date_range[0],
                    'end': date_range[1]
                },
                'latest_date': latest_data[0] if latest_data else None,
                'latest_count': latest_data[1] if latest_data else 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ 獲取統計資訊失敗: {e}")
            return {}
    
    def run_daily_update(self, days: int = 1) -> bool:
        """
        執行每日更新（主要入口點）
        
        Args:
            days: 爬取天數（預設1天，即昨天的資料）
            
        Returns:
            是否成功
        """
        try:
            logger.info("🚀 開始執行每日股價資料更新")
            
            # 計算日期範圍
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 顯示更新前統計
            logger.info("📊 更新前資料庫統計:")
            before_stats = self.get_database_stats()
            for key, value in before_stats.items():
                logger.info(f"   {key}: {value}")
            
            # 執行爬取
            result = self.crawl_daily_data(start_date, end_date)
            
            if result['success']:
                # 顯示更新後統計
                logger.info("📊 更新後資料庫統計:")
                after_stats = self.get_database_stats()
                for key, value in after_stats.items():
                    logger.info(f"   {key}: {value}")
                
                # 計算新增記錄數
                new_records = after_stats.get('total_records', 0) - before_stats.get('total_records', 0)
                logger.info(f"📈 本次新增記錄: {new_records} 筆")
                
                logger.info("✅ 每日股價資料更新完成")
                return True
            else:
                logger.error("❌ 每日股價資料更新失敗")
                return False
                
        except Exception as e:
            logger.error(f"❌ 執行每日更新時發生錯誤: {e}")
            return False


def main():
    """主程式入口"""
    parser = argparse.ArgumentParser(description='新版自動股價爬蟲程式')
    parser.add_argument('--days', type=int, default=1, help='爬取天數（預設1天）')
    parser.add_argument('--db-path', type=str, default='db/price.db', help='price.db路徑')
    parser.add_argument('--start-date', type=str, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--stats-only', action='store_true', help='只顯示統計資訊')
    
    args = parser.parse_args()
    
    try:
        # 創建爬蟲實例
        crawler = NewPriceCrawlerAuto(args.db_path)
        
        if args.stats_only:
            # 只顯示統計資訊
            logger.info("📊 price.db統計資訊:")
            stats = crawler.get_database_stats()
            for key, value in stats.items():
                logger.info(f"   {key}: {value}")
            return
        
        # 執行爬取
        if args.start_date and args.end_date:
            # 使用指定日期範圍
            result = crawler.crawl_daily_data(args.start_date, args.end_date)
        else:
            # 使用每日更新模式
            result = crawler.run_daily_update(args.days)
        
        # 根據結果設定退出碼
        sys.exit(0 if result else 1)
        
    except KeyboardInterrupt:
        logger.info("⏹️ 使用者中斷執行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程式執行失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
