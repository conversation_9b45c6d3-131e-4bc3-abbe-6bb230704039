#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試批量下載功能
"""

from enhanced_monthly_revenue_downloader import EnhancedMonthlyRevenueDownloader
import logging

def main():
    """測試批量下載功能"""
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試批量下載功能")
    print("=" * 50)
    
    # 創建下載器實例
    downloader = EnhancedMonthlyRevenueDownloader()
    
    # 測試獲取股票列表
    print("📋 測試獲取股票列表...")
    stock_list = downloader.get_all_stock_list()
    print(f"✅ 獲取到 {len(stock_list)} 支股票")
    print(f"前10支股票: {stock_list[:10]}")
    
    # 測試批量下載（只下載前3支股票）
    print("\n📊 測試批量下載（前3支股票）...")
    result = downloader.batch_download_all_stocks(
        start_date='2024-01',
        end_date='2025-07',
        max_stocks=3,
        delay_seconds=2
    )
    
    print("\n📈 測試結果:")
    print(f"✅ 成功: {result['success']} 支股票")
    print(f"❌ 失敗: {result['failed']} 支股票")
    print(f"📊 總計: {result['total']} 支股票")
    print(f"📈 總記錄數: {result.get('total_records', 0)} 筆")
    
    if result['failed_stocks']:
        print(f"⚠️ 失敗股票: {result['failed_stocks']}")

if __name__ == "__main__":
    main()
