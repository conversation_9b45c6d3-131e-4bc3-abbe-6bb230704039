#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證月營收更新成功
"""

import pandas as pd
import datetime
import os

def verify_update_success():
    """驗證更新成功"""
    file_path = "history/tables/monthly_report.pkl"
    
    print(f"🎉 驗證月營收更新成功")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return False
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"📁 檔案資訊:")
        print(f"   大小: {file_size:.1f} MB")
        print(f"   修改時間: {file_mtime}")
        
        # 讀取資料
        df = pd.read_pickle(file_path)
        
        print(f"\n📊 資料資訊:")
        print(f"   總筆數: {len(df):,}")
        print(f"   索引: {df.index.names}")
        print(f"   欄位數: {len(df.columns)}")
        print(f"   欄位: {list(df.columns)}")
        
        # 安全地檢查日期範圍
        print(f"\n📅 日期檢查:")
        try:
            dates = df.index.get_level_values('date')
            
            # 轉換為統一格式進行比較
            if len(dates) > 0:
                # 獲取所有唯一日期並排序
                unique_dates = sorted(dates.unique())
                
                print(f"   日期數量: {len(unique_dates)}")
                print(f"   最早日期: {unique_dates[0]}")
                print(f"   最新日期: {unique_dates[-1]}")
                
                # 檢查最近幾個月的資料
                print(f"\n📊 最近5個月的資料:")
                for date in unique_dates[-5:]:
                    count = len(df[df.index.get_level_values('date') == date])
                    print(f"   {date}: {count:,} 筆")
                
                # 檢查是否有今年的資料
                latest_date = unique_dates[-1]
                if hasattr(latest_date, 'year'):
                    latest_year = latest_date.year
                elif hasattr(latest_date, 'date'):
                    latest_year = latest_date.date().year
                else:
                    latest_year = int(str(latest_date)[:4])
                
                current_year = datetime.datetime.now().year
                print(f"\n📈 時效性檢查:")
                print(f"   最新資料年份: {latest_year}")
                print(f"   當前年份: {current_year}")
                
                if latest_year >= current_year - 1:  # 最新資料在去年或今年
                    print(f"   ✅ 資料較新")
                else:
                    print(f"   ⚠️ 資料較舊")
                
        except Exception as e:
            print(f"   ⚠️ 日期檢查失敗: {str(e)}")
        
        # 檢查股票數量
        print(f"\n📈 股票檢查:")
        try:
            stocks = df.index.get_level_values('stock_id')
            unique_stocks = stocks.unique()
            print(f"   股票數量: {len(unique_stocks)}")
            print(f"   前5檔股票: {list(unique_stocks[:5])}")
        except Exception as e:
            print(f"   ⚠️ 股票檢查失敗: {str(e)}")
        
        # 檢查營收資料
        print(f"\n💰 營收資料檢查:")
        try:
            if '當月營收' in df.columns:
                # 轉換為數值進行統計
                revenue_data = pd.to_numeric(df['當月營收'], errors='coerce').dropna()
                
                print(f"   有效營收筆數: {len(revenue_data):,}")
                if len(revenue_data) > 0:
                    print(f"   營收範圍: {revenue_data.min():,.0f} ~ {revenue_data.max():,.0f} 千元")
                    print(f"   平均營收: {revenue_data.mean():,.0f} 千元")
            else:
                print(f"   ⚠️ 找不到 '當月營收' 欄位")
        except Exception as e:
            print(f"   ⚠️ 營收檢查失敗: {str(e)}")
        
        # 檢查資料完整性
        print(f"\n🔍 資料完整性:")
        try:
            # 檢查重複索引
            duplicates = df.index.duplicated().sum()
            print(f"   重複索引: {duplicates}")
            
            # 檢查空值
            null_counts = df.isnull().sum()
            total_nulls = null_counts.sum()
            print(f"   空值總數: {total_nulls:,}")
            
            if total_nulls > 0:
                print(f"   主要空值欄位:")
                for col, count in null_counts.items():
                    if count > 0:
                        percentage = count / len(df) * 100
                        print(f"     {col}: {count:,} ({percentage:.1f}%)")
        except Exception as e:
            print(f"   ⚠️ 完整性檢查失敗: {str(e)}")
        
        # 判斷更新狀態
        print(f"\n🎯 更新狀態判斷:")
        
        # 檢查檔案是否今天有修改
        today = datetime.date.today()
        if file_mtime.date() == today:
            print(f"   ✅ 檔案今天有更新")
            update_success = True
        else:
            days_old = (today - file_mtime.date()).days
            print(f"   ⚠️ 檔案 {days_old} 天前更新")
            update_success = False
        
        # 檢查資料量是否合理
        if len(df) > 300000:  # 超過30萬筆資料
            print(f"   ✅ 資料量充足 ({len(df):,} 筆)")
        else:
            print(f"   ⚠️ 資料量較少 ({len(df):,} 筆)")
        
        # 檢查欄位是否完整
        expected_columns = ['當月營收', '上月營收', '去年當月營收', '上月比較增減(%)', '去年同月增減(%)']
        missing_columns = set(expected_columns) - set(df.columns)
        if not missing_columns:
            print(f"   ✅ 主要欄位完整")
        else:
            print(f"   ⚠️ 缺少欄位: {missing_columns}")
        
        print(f"\n🎉 驗證完成！")
        
        if update_success and len(df) > 300000 and not missing_columns:
            print(f"✅ 月營收更新完全成功！")
            print(f"✅ 爬蟲可以直接更新到 monthly_report.pkl")
            print(f"✅ 資料格式正確且完整")
            return True
        else:
            print(f"⚠️ 更新部分成功，但可能需要進一步檢查")
            return False
        
    except Exception as e:
        print(f"❌ 驗證失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_backup_files():
    """檢查備份檔案"""
    backup_dir = "history/tables"
    
    print(f"\n📁 備份檔案檢查:")
    print("=" * 40)
    
    try:
        files = os.listdir(backup_dir)
        backup_files = [f for f in files if f.startswith('monthly_report_backup')]
        
        if backup_files:
            print(f"   找到 {len(backup_files)} 個備份檔案:")
            for backup_file in sorted(backup_files)[-5:]:  # 顯示最近5個
                file_path = os.path.join(backup_dir, backup_file)
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"     {backup_file}")
                print(f"       大小: {size:.1f} MB, 時間: {mtime}")
        else:
            print(f"   無備份檔案")
    except Exception as e:
        print(f"   ❌ 檢查備份失敗: {str(e)}")

if __name__ == "__main__":
    print("🔍 月營收更新成功驗證工具")
    print("=" * 70)
    print("🎯 目標: 驗證月營收爬蟲已成功更新到 monthly_report.pkl")
    print("=" * 70)
    
    # 檢查備份檔案
    check_backup_files()
    
    # 驗證更新成功
    success = verify_update_success()
    
    if success:
        print(f"\n🎊 恭喜！月營收爬蟲更新流程完全成功！")
        print(f"🎯 您現在可以使用 auto_update.py 直接更新 monthly_report.pkl")
    else:
        print(f"\n💡 更新流程基本成功，建議進一步檢查細節")
