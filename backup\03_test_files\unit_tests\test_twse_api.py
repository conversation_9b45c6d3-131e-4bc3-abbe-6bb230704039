#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試上市股票API
"""

import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_twse_api():
    """測試上市股票API"""
    print("🔍 測試上市股票API...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試不同的日期格式
    test_urls = [
        # 測試原始格式
        "https://www.twse.com.tw/exchangeReport/TWT49U?response=json&strDate=20240701&endDate=20240731",
        # 測試單一日期
        "https://www.twse.com.tw/exchangeReport/TWT49U?response=json&date=20240714",
        # 測試不同日期格式
        "https://www.twse.com.tw/exchangeReport/TWT49U?response=json&strDate=20240714&endDate=20240714",
        # 測試民國年格式
        "https://www.twse.com.tw/exchangeReport/TWT49U?response=json&strDate=1130714&endDate=1130714",
    ]
    
    for i, url in enumerate(test_urls):
        print(f"\n📊 測試URL {i+1}: {url}")
        
        try:
            response = requests.get(url, headers=headers, verify=False, timeout=30)
            print(f"狀態碼: {response.status_code}")
            print(f"內容類型: {response.headers.get('content-type', 'unknown')}")
            print(f"內容長度: {len(response.text)}")
            
            # 顯示前500字元
            print(f"內容預覽:")
            print(response.text[:500])
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_twse_api()
