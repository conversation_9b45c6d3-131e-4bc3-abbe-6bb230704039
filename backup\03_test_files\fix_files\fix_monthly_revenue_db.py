#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復月營收資料庫結構，添加缺少的欄位
"""

import sqlite3
import os
import logging

def fix_monthly_revenue_database():
    """修復月營收資料庫結構"""
    
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    
    print("🔧 修復月營收資料庫結構")
    print("=" * 50)
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查現有表格結構
        cursor.execute("PRAGMA table_info(monthly_revenue)")
        columns_info = cursor.fetchall()
        existing_columns = [col[1] for col in columns_info]
        
        print(f"📋 現有欄位: {existing_columns}")
        
        # 需要添加的新欄位（注意：ALTER TABLE ADD COLUMN 不支援非常數預設值）
        required_columns = {
            'stock_name': 'TEXT',
            'industry': 'TEXT',
            'market': 'TEXT',
            'year': 'INTEGER',
            'month': 'INTEGER',
            'revenue_mom': 'REAL',
            'revenue_yoy': 'REAL',
            'cumulative_revenue': 'REAL',
            'cumulative_revenue_yoy': 'REAL',
            'remark': 'TEXT',
            'updated_at': 'TIMESTAMP'  # 移除 DEFAULT，避免 ALTER TABLE 錯誤
        }
        
        # 添加缺少的欄位
        added_columns = []
        for column_name, column_type in required_columns.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f'ALTER TABLE monthly_revenue ADD COLUMN {column_name} {column_type}')
                    added_columns.append(column_name)
                    print(f"✅ 已添加欄位: {column_name} ({column_type})")
                except Exception as e:
                    print(f"❌ 添加欄位 {column_name} 失敗: {e}")
        
        if not added_columns:
            print("✅ 所有必要欄位都已存在")
        else:
            print(f"✅ 成功添加 {len(added_columns)} 個欄位: {', '.join(added_columns)}")
        
        # 檢查並創建索引
        print(f"\n🔍 檢查索引...")
        
        # 獲取現有索引
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='monthly_revenue'")
        existing_indexes = [idx[0] for idx in cursor.fetchall()]
        print(f"📋 現有索引: {existing_indexes}")
        
        # 需要的索引
        required_indexes = [
            ('idx_monthly_revenue_stock_year_month', 'stock_id, year, month'),
            ('idx_monthly_revenue_year_month', 'year, month'),
            ('idx_monthly_revenue_stock', 'stock_id'),
            ('idx_monthly_revenue_industry', 'industry'),
            ('idx_monthly_revenue_market', 'market')
        ]
        
        created_indexes = []
        for index_name, index_columns in required_indexes:
            if index_name not in existing_indexes:
                try:
                    cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON monthly_revenue({index_columns})')
                    created_indexes.append(index_name)
                    print(f"✅ 已創建索引: {index_name}")
                except Exception as e:
                    print(f"❌ 創建索引 {index_name} 失敗: {e}")
        
        if not created_indexes:
            print("✅ 所有必要索引都已存在")
        else:
            print(f"✅ 成功創建 {len(created_indexes)} 個索引")
        
        # 提交變更
        conn.commit()
        
        # 檢查最終結構
        print(f"\n📊 最終表格結構:")
        cursor.execute("PRAGMA table_info(monthly_revenue)")
        final_columns = cursor.fetchall()
        
        for col in final_columns:
            col_id, name, data_type, not_null, default_val, pk = col
            nullable = "NOT NULL" if not_null else "NULL"
            default = f"DEFAULT {default_val}" if default_val else ""
            primary = "PRIMARY KEY" if pk else ""
            print(f"  {name} {data_type} {nullable} {default} {primary}".strip())
        
        # 檢查數據量
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 資料庫統計:")
        print(f"  總記錄數: {total_count:,}")
        
        if total_count > 0:
            # 檢查年月分布
            cursor.execute("SELECT year, month, COUNT(*) FROM monthly_revenue GROUP BY year, month ORDER BY year, month")
            year_month_stats = cursor.fetchall()
            print(f"  年月分布:")
            for year, month, count in year_month_stats:
                if year and month:
                    print(f"    {year}/{month:02d}: {count:,} 筆")
            
            # 檢查市場分布
            cursor.execute("SELECT market, COUNT(*) FROM monthly_revenue WHERE market IS NOT NULL AND market != '' GROUP BY market")
            market_stats = cursor.fetchall()
            if market_stats:
                print(f"  市場分布:")
                for market, count in market_stats:
                    print(f"    {market}: {count:,} 筆")
        
        conn.close()
        
        print(f"\n🎉 資料庫結構修復完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """測試資料庫操作"""
    
    print(f"\n🧪 測試資料庫操作")
    print("=" * 30)
    
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試插入操作（包含 updated_at）
        test_data = {
            'stock_id': 'TEST',
            'stock_name': '測試股票',
            'industry': '測試產業',
            'market': '測試市場',
            'year': 2024,
            'month': 12,
            'revenue': 1000000,
            'revenue_mom': 5.5,
            'revenue_yoy': 10.2,
            'cumulative_revenue': 12000000,
            'cumulative_revenue_yoy': 8.8,
            'remark': '測試備註'
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO monthly_revenue
            (stock_id, stock_name, industry, market, year, month, revenue,
             revenue_mom, revenue_yoy, cumulative_revenue, cumulative_revenue_yoy,
             remark, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            test_data['stock_id'],
            test_data['stock_name'],
            test_data['industry'],
            test_data['market'],
            test_data['year'],
            test_data['month'],
            test_data['revenue'],
            test_data['revenue_mom'],
            test_data['revenue_yoy'],
            test_data['cumulative_revenue'],
            test_data['cumulative_revenue_yoy'],
            test_data['remark']
        ))
        
        conn.commit()
        
        # 驗證插入結果
        cursor.execute('''
            SELECT stock_id, stock_name, industry, market, revenue, updated_at
            FROM monthly_revenue 
            WHERE stock_id = 'TEST'
        ''')
        
        result = cursor.fetchone()
        if result:
            print(f"✅ 測試插入成功:")
            print(f"  股票代號: {result[0]}")
            print(f"  股票名稱: {result[1]}")
            print(f"  產業別: {result[2]}")
            print(f"  市場別: {result[3]}")
            print(f"  營收: {result[4]:,}")
            print(f"  更新時間: {result[5]}")
            
            # 清理測試數據
            cursor.execute("DELETE FROM monthly_revenue WHERE stock_id = 'TEST'")
            conn.commit()
            print(f"✅ 測試數據已清理")
        else:
            print(f"❌ 測試插入失敗")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = fix_monthly_revenue_database()
    
    if success:
        test_database_operations()
    else:
        print("❌ 資料庫修復失敗，跳過測試")
