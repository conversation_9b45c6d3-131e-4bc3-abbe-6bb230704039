#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試櫃買中心API
"""

import requests
import json
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def test_tpex_direct():
    """直接測試櫃買中心API"""
    print("🔍 直接測試櫃買中心除權息API...")
    
    # 使用測試中成功的URL
    url = "https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php?l=zh-tw&d=113/01/01&ed=113/12/31"
    print(f"📡 URL: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        print(f"📊 HTTP狀態碼: {response.status_code}")
        print(f"📄 回應長度: {len(response.text)} 字元")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 成功解析JSON")
                print(f"📋 JSON鍵值: {list(data.keys())}")
                
                if "aaData" in data:
                    records = data["aaData"]
                    print(f"📊 找到 {len(records)} 筆記錄")
                    
                    if records:
                        print("📋 前5筆記錄:")
                        for i, record in enumerate(records[:5]):
                            print(f"  {i+1}. {record}")
                        
                        # 轉換為我們需要的格式
                        converted_data = []
                        for record in records[:10]:  # 只處理前10筆作為示例
                            try:
                                converted_data.append({
                                    '股票代碼': str(record[1]).strip() if len(record) > 1 else '',
                                    '股票名稱': str(record[2]).strip() if len(record) > 2 else '',
                                    '除權息日': str(record[0]).strip() if len(record) > 0 else '',
                                    '現金股利': str(record[13]).strip() if len(record) > 13 else '0',
                                    '股票股利': str(record[14]).strip() if len(record) > 14 else '0',
                                    '下載時間': '2025/07/12 22:45',
                                    '備註': '櫃買中心真實資料'
                                })
                            except Exception as e:
                                print(f"⚠️ 處理記錄時出錯: {e}")
                                continue
                        
                        print(f"\n📋 轉換後的前5筆資料:")
                        for i, item in enumerate(converted_data[:5]):
                            print(f"  {i+1}. {item}")
                        
                        return converted_data
                    else:
                        print("⚠️ aaData為空")
                else:
                    print("❌ 沒有找到aaData")
                    print(f"📄 完整回應: {data}")
                    
            except Exception as e:
                print(f"❌ JSON解析失敗: {e}")
                print(f"📄 回應內容前500字元: {response.text[:500]}")
        else:
            print(f"❌ HTTP請求失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 請求失敗: {e}")
    
    return None

def save_test_data(data, filename="test_real_dividend.csv"):
    """保存測試資料"""
    if not data:
        print("⚠️ 沒有資料可保存")
        return False
    
    try:
        import csv
        with open(filename, 'w', newline='', encoding='utf-8-sig') as file:
            fieldnames = ['股票代碼', '股票名稱', '除權息日', '現金股利', '股票股利', '下載時間', '備註']
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"✅ 測試資料已保存至: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 保存失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 櫃買中心API直接測試")
    print("=" * 50)
    
    # 測試API
    data = test_tpex_direct()
    
    if data:
        print(f"\n🎉 成功獲取 {len(data)} 筆測試資料")
        
        # 保存資料
        if save_test_data(data):
            print("✅ 資料保存成功")
        else:
            print("❌ 資料保存失敗")
    else:
        print("\n❌ 未獲取到任何資料")
    
    print("\n按 Enter 鍵結束...")
    input()
