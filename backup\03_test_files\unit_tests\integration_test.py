#!/usr/bin/env python3
"""
智能Yahoo數據源整合測試
測試所有三個主要組件的集成效果
"""

import logging
import time
import pandas as pd
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_smart_yahoo_fetcher():
    """測試智能Yahoo獲取器"""
    print("🧪 測試1: 智能Yahoo獲取器")
    print("-" * 50)
    
    try:
        from smart_yahoo_fetcher import SmartYahooFetcher
        
        fetcher = SmartYahooFetcher()
        
        # 測試不同市場的股票
        test_cases = [
            ('2330', '上市', '台積電'),
            ('2317', '上市', '鴻海'),
            ('6218', '櫃買', '青蛙撞奶'),
            ('8277', '櫃買', '商丞'),
        ]
        
        success_count = 0
        for stock_id, market, name in test_cases:
            print(f"\n📊 測試 {stock_id} ({name} - {market})")
            
            # 測試日數據
            daily_data = fetcher.get_stock_data(stock_id, 10)
            if not daily_data.empty:
                print(f"  ✅ 日數據: {len(daily_data)} 天")
                success_count += 1
            else:
                print(f"  ❌ 日數據失敗")
            
            # 測試盤中數據
            intraday_data = fetcher.get_intraday_data(stock_id)
            if not intraday_data.empty:
                print(f"  ✅ 盤中數據: {len(intraday_data)} 筆")
            else:
                print(f"  ❌ 盤中數據失敗")
        
        print(f"\n📊 智能Yahoo測試結果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
        
    except ImportError:
        print("❌ 智能Yahoo獲取器不可用")
        return False

def test_enhanced_data_fetcher():
    """測試增強版數據獲取器"""
    print("\n🧪 測試2: 增強版數據獲取器")
    print("-" * 50)
    
    try:
        from enhanced_data_fetcher import enhanced_fetcher
        
        test_stocks = ['2330', '2317', '2454']
        success_count = 0
        
        for stock_id in test_stocks:
            print(f"\n📊 測試 {stock_id}")
            
            data = enhanced_fetcher.get_stock_data(stock_id, 10)
            if not data.empty:
                print(f"  ✅ 成功獲取 {len(data)} 天數據")
                success_count += 1
            else:
                print(f"  ❌ 獲取失敗")
        
        print(f"\n📊 增強版獲取器測試結果: {success_count}/{len(test_stocks)} 成功")
        return success_count > 0
        
    except ImportError:
        print("❌ 增強版數據獲取器不可用")
        return False

def test_intraday_data_fetcher():
    """測試盤中數據獲取器"""
    print("\n🧪 測試3: 盤中數據獲取器")
    print("-" * 50)
    
    try:
        from intraday_data_fetcher import IntradayDataFetcher
        
        fetcher = IntradayDataFetcher()
        test_stocks = ['2330', '2317']
        success_count = 0
        
        for stock_id in test_stocks:
            print(f"\n📊 測試 {stock_id}")
            
            data = fetcher.get_intraday_data(stock_id)
            if not data.empty:
                print(f"  ✅ 成功獲取 {len(data)} 筆盤中數據")
                success_count += 1
            else:
                print(f"  ❌ 獲取失敗")
        
        print(f"\n📊 盤中數據獲取器測試結果: {success_count}/{len(test_stocks)} 成功")
        return success_count > 0
        
    except ImportError:
        print("❌ 盤中數據獲取器不可用")
        return False

def test_unified_data_manager():
    """測試統一數據管理系統"""
    print("\n🧪 測試4: 統一數據管理系統")
    print("-" * 50)
    
    try:
        from unified_data_manager import unified_manager, get_stock_data_unified
        
        test_stocks = ['2330', '2317']
        success_count = 0
        
        for stock_id in test_stocks:
            print(f"\n📊 測試 {stock_id}")
            
            # 測試日數據
            daily_data = get_stock_data_unified(stock_id, 10, 'daily')
            if not daily_data.empty:
                print(f"  ✅ 日數據: {len(daily_data)} 天")
                success_count += 1
            else:
                print(f"  ❌ 日數據失敗")
            
            # 測試盤中數據
            intraday_data = get_stock_data_unified(stock_id, data_type='intraday')
            if not intraday_data.empty:
                print(f"  ✅ 盤中數據: {len(intraday_data)} 筆")
            else:
                print(f"  ❌ 盤中數據失敗")
        
        # 顯示系統狀態
        print(f"\n📊 系統健康狀態:")
        health = unified_manager.get_health_status()
        print(f"  • 健康數據源: {health['healthy_sources']}")
        print(f"  • 降級數據源: {health['degraded_sources']}")
        print(f"  • 失敗數據源: {health['failed_sources']}")
        
        print(f"\n📊 統一管理系統測試結果: {success_count}/{len(test_stocks)} 成功")
        return success_count > 0
        
    except ImportError:
        print("❌ 統一數據管理系統不可用")
        return False

def test_data_system_config():
    """測試數據系統配置"""
    print("\n🧪 測試5: 數據系統配置")
    print("-" * 50)
    
    try:
        from data_system_config import system_config
        
        # 測試配置功能
        enabled_sources = system_config.get_enabled_sources()
        print(f"✅ 啟用的數據源: {len(enabled_sources)}")
        
        for source in enabled_sources[:3]:  # 只顯示前3個
            print(f"  • {source.name} (優先級: {source.priority})")
        
        # 測試特定配置
        is_smart_yahoo_enabled = system_config.is_source_enabled('smart_yahoo')
        is_legacy_enabled = system_config.is_source_enabled('yfinance_legacy')
        
        print(f"✅ 智能Yahoo: {'啟用' if is_smart_yahoo_enabled else '禁用'}")
        print(f"✅ 傳統Yahoo: {'啟用' if is_legacy_enabled else '禁用'}")
        
        return True
        
    except ImportError:
        print("❌ 數據系統配置不可用")
        return False

def performance_test():
    """性能測試"""
    print("\n🧪 測試6: 性能測試")
    print("-" * 50)
    
    try:
        from smart_yahoo_fetcher import SmartYahooFetcher
        
        fetcher = SmartYahooFetcher()
        test_stocks = ['2330', '2317', '2454', '2412', '3008']
        
        print(f"📊 測試 {len(test_stocks)} 支股票的獲取速度...")
        
        start_time = time.time()
        success_count = 0
        
        for stock_id in test_stocks:
            data = fetcher.get_stock_data(stock_id, 5)
            if not data.empty:
                success_count += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"✅ 成功獲取: {success_count}/{len(test_stocks)}")
        print(f"✅ 總耗時: {total_time:.2f}秒")
        print(f"✅ 平均每支股票: {total_time/len(test_stocks):.2f}秒")
        
        # 測試緩存效果
        print(f"\n📦 測試緩存效果...")
        start_time = time.time()
        
        for stock_id in test_stocks:
            data = fetcher.get_stock_data(stock_id, 5)  # 相同請求
        
        end_time = time.time()
        cache_time = end_time - start_time
        
        print(f"✅ 緩存耗時: {cache_time:.2f}秒")
        print(f"✅ 速度提升: {total_time/cache_time:.1f}倍")
        
        return True
        
    except ImportError:
        print("❌ 性能測試不可用")
        return False

def main():
    """主測試函數"""
    print("🚀 智能Yahoo數據源整合測試")
    print("=" * 80)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 執行所有測試
    tests = [
        ("智能Yahoo獲取器", test_smart_yahoo_fetcher),
        ("增強版數據獲取器", test_enhanced_data_fetcher),
        ("盤中數據獲取器", test_intraday_data_fetcher),
        ("統一數據管理系統", test_unified_data_manager),
        ("數據系統配置", test_data_system_config),
        ("性能測試", performance_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結報告
    print("\n" + "=" * 80)
    print("📊 整合測試總結報告")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"🎯 測試結果: {passed}/{total} 通過")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    print(f"\n📋 詳細結果:")
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  • {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 所有測試通過！智能Yahoo數據源整合成功！")
        print(f"\n💡 系統已優化完成，具備以下特性:")
        print(f"  • 智能格式選擇，減少錯誤")
        print(f"  • 錯誤過濾，清潔日誌")
        print(f"  • 統一管理，自動切換")
        print(f"  • 健康監控，實時狀態")
        print(f"  • 緩存機制，提升性能")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關組件")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
