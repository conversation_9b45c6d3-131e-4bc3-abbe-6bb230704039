#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
from datetime import datetime, timedelta

def test_date_range_query():
    """測試時間範圍查詢"""
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 測試查詢2025年7月1日到7月31日的資料
        start_date = "2025-07-01"
        end_date = "2025-07-31"
        
        query = """
            SELECT stock_code, stock_name, year, ex_dividend_date,
                   cash_dividend, stock_dividend, total_dividend,
                   dividend_yield, eps, data_source
            FROM dividend_data
            WHERE ex_dividend_date IS NOT NULL
            AND ex_dividend_date != ''
            AND ex_dividend_date >= ?
            AND ex_dividend_date <= ?
            ORDER BY ex_dividend_date, stock_code
        """
        
        cursor = conn.cursor()
        cursor.execute(query, (start_date, end_date))
        results = cursor.fetchall()
        
        print(f"📊 查詢期間: {start_date} 至 {end_date}")
        print(f"📈 找到記錄數: {len(results)}")
        
        if results:
            print("\n📋 前10筆記錄:")
            print("股票代碼 | 股票名稱 | 除權息日期 | 現金股利 | 股票股利")
            print("-" * 60)
            for i, row in enumerate(results[:10]):
                print(f"{row[0]} | {row[1]} | {row[3]} | {row[4]} | {row[5]}")
        else:
            print("❌ 未找到符合條件的記錄")
            
        # 檢查資料庫中的日期範圍
        cursor.execute("""
            SELECT MIN(ex_dividend_date), MAX(ex_dividend_date), COUNT(*)
            FROM dividend_data 
            WHERE ex_dividend_date IS NOT NULL AND ex_dividend_date != ''
        """)
        min_date, max_date, total_count = cursor.fetchone()
        print(f"\n📅 資料庫日期範圍: {min_date} 至 {max_date}")
        print(f"📊 總記錄數: {total_count}")
        
        # 檢查7月份的資料
        cursor.execute("""
            SELECT COUNT(*) FROM dividend_data 
            WHERE ex_dividend_date LIKE '2025-07%'
        """)
        july_count = cursor.fetchone()[0]
        print(f"📈 2025年7月記錄數: {july_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")

def test_stock_query():
    """測試股票查詢"""
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試查詢2317鴻海的資料
        stock_code = "2317"
        year = "2025"
        
        query = """
            SELECT stock_code, stock_name, year, ex_dividend_date,
                   cash_dividend, stock_dividend, total_dividend,
                   dividend_yield, eps, data_source
            FROM dividend_data
            WHERE stock_code = ? AND year = ?
            ORDER BY ex_dividend_date
        """
        
        cursor.execute(query, (stock_code, year))
        results = cursor.fetchall()
        
        print(f"\n🔍 股票查詢測試: {stock_code} ({year}年)")
        print(f"📈 找到記錄數: {len(results)}")
        
        if results:
            print("\n📋 查詢結果:")
            for row in results:
                print(f"股票: {row[0]} {row[1]}")
                print(f"除權息日期: {row[3]}")
                print(f"現金股利: {row[4]}")
                print(f"股票股利: {row[5]}")
                print("-" * 40)
        else:
            print("❌ 未找到符合條件的記錄")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 股票查詢失敗: {e}")

if __name__ == "__main__":
    print("🧪 測試除權息資料庫查詢功能")
    print("=" * 50)
    
    test_date_range_query()
    test_stock_query()
