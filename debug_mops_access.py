#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 MOPS 網站訪問問題
"""

import requests
import time
from urllib.parse import urlencode

def test_mops_website():
    """測試 MOPS 網站的各種訪問方式"""
    
    print("=" * 80)
    print("🔍 調試 MOPS 網站訪問")
    print("=" * 80)
    
    # 測試不同的 URL 格式
    test_urls = [
        # 原始 R 語言版本的 URL
        "http://mops.twse.com.tw/server-java/t164sb01?step=1&CO_ID=2330&SYEAR=2024&SSEASON=1&REPORT_ID=C",
        
        # HTTPS 版本
        "https://mops.twse.com.tw/server-java/t164sb01?step=1&CO_ID=2330&SYEAR=2024&SSEASON=1&REPORT_ID=C",
        
        # 不同的參數格式
        "http://mops.twse.com.tw/server-java/t164sb01?step=1&CO_ID=2330&SYEAR=2023&SSEASON=4&REPORT_ID=C",
        
        # MOPS 首頁
        "http://mops.twse.com.tw/",
        "https://mops.twse.com.tw/",
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    for i, url in enumerate(test_urls):
        print(f"\n🔍 測試 {i+1}: {url}")
        
        try:
            response = session.get(url, timeout=10, verify=False)
            print(f"   狀態碼: {response.status_code}")
            print(f"   內容長度: {len(response.content)} bytes")
            
            if response.status_code == 200:
                print(f"   ✅ 訪問成功")
                
                # 檢查內容是否包含財務報表相關資訊
                content = response.text
                if '資產負債表' in content or '綜合損益表' in content or '現金流量表' in content:
                    print(f"   📊 包含財務報表資料")
                elif '查無資料' in content or '無此資料' in content:
                    print(f"   ⚠️ 查無資料")
                elif 'error' in content.lower() or 'exception' in content.lower():
                    print(f"   ❌ 包含錯誤訊息")
                else:
                    print(f"   📄 其他內容")
                    
                # 顯示前200個字符
                print(f"   內容預覽: {content[:200]}...")
                
            elif response.status_code == 404:
                print(f"   ❌ 頁面不存在")
            elif response.status_code == 403:
                print(f"   ❌ 訪問被拒絕")
            else:
                print(f"   ⚠️ 其他錯誤")
                
        except requests.exceptions.SSLError as e:
            print(f"   ❌ SSL 錯誤: {e}")
        except requests.exceptions.Timeout:
            print(f"   ⏰ 請求超時")
        except requests.exceptions.ConnectionError as e:
            print(f"   🔌 連線錯誤: {e}")
        except Exception as e:
            print(f"   ❌ 其他錯誤: {e}")
        
        time.sleep(1)

def test_alternative_mops_urls():
    """測試其他可能的 MOPS URL 格式"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試其他可能的 MOPS URL 格式")
    print("=" * 80)
    
    # 可能的新 URL 格式
    alternative_formats = [
        # 新的 API 格式
        "https://mops.twse.com.tw/mops/web/ajax_t164sb01",
        "https://mops.twse.com.tw/mops/web/t164sb01",
        
        # 不同的 server 路徑
        "https://mops.twse.com.tw/server-java/FileDownLoad",
        "https://mops.twse.com.tw/nas/t21/sii/t21sc03_sii.html",
        
        # 查詢頁面
        "https://mops.twse.com.tw/mops/web/index",
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    })
    
    for url in alternative_formats:
        print(f"\n🔍 測試: {url}")
        
        try:
            response = session.get(url, timeout=10, verify=False)
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 訪問成功")
                content = response.text
                print(f"   內容長度: {len(content)} 字符")
                
                # 檢查是否是財務報表相關頁面
                if any(keyword in content for keyword in ['財務報表', '資產負債', '損益表', '現金流量']):
                    print(f"   📊 可能是財務報表相關頁面")
                
                # 顯示標題
                if '<title>' in content:
                    title_start = content.find('<title>') + 7
                    title_end = content.find('</title>', title_start)
                    if title_end > title_start:
                        title = content[title_start:title_end]
                        print(f"   📄 頁面標題: {title}")
            else:
                print(f"   ❌ 狀態碼: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
        
        time.sleep(1)

def check_r_script_date():
    """檢查 R 腳本的日期和可能的過期問題"""
    
    print(f"\n" + "=" * 80)
    print("📅 檢查 R 腳本的時效性")
    print("=" * 80)
    
    print("🔍 R 腳本分析:")
    print("   - 使用的測試參數: 2317 (鴻海), 2017年第2季")
    print("   - 這是 2017年的資料，可能網站結構已經改變")
    print("   - URL 格式: http://mops.twse.com.tw/server-java/t164sb01")
    print()
    
    print("💡 可能的問題:")
    print("   1. MOPS 網站可能已經升級到 HTTPS")
    print("   2. URL 結構可能已經改變")
    print("   3. 參數格式可能需要調整")
    print("   4. 可能需要先訪問主頁獲取 session")
    print()
    
    print("🎯 建議的解決方案:")
    print("   1. 檢查 MOPS 網站的當前結構")
    print("   2. 嘗試模擬瀏覽器的完整訪問流程")
    print("   3. 使用更新的 URL 格式")
    print("   4. 考慮使用 selenium 進行動態網頁爬取")

def main():
    """主函數"""
    
    print("🔍 MOPS 網站訪問調試")
    
    # 測試基本訪問
    test_mops_website()
    
    # 測試其他 URL 格式
    test_alternative_mops_urls()
    
    # 檢查 R 腳本時效性
    check_r_script_date()
    
    print(f"\n" + "=" * 80)
    print("📊 調試總結")
    print("=" * 80)
    print("🎯 基於測試結果，MOPS 網站可能已經:")
    print("   1. 改變了 URL 結構")
    print("   2. 升級了安全機制")
    print("   3. 需要不同的訪問方式")
    print()
    print("💡 建議:")
    print("   1. 繼續使用現有的 TWSE OpenAPI 系統")
    print("   2. 尋找其他現金流量表資料源")
    print("   3. 或考慮付費的財務資料 API")

if __name__ == "__main__":
    main()
