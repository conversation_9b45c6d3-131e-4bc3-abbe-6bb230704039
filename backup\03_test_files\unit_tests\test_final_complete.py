#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終完整功能測試
"""

from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
import pandas as pd
import os

def test_final_complete():
    """最終完整功能測試"""
    
    print("🎯 最終完整功能測試")
    print("=" * 60)
    
    # 使用正確的資料庫路徑
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    downloader = MopsBulkRevenueDownloader(db_path)
    
    # 測試2024年6月完整下載
    print("📅 下載 2024年6月 完整數據...")
    results = downloader.download_all_markets(2024, 6)
    
    print("\n📊 下載結果:")
    total = 0
    for market, count in results.items():
        print(f"  {market}: {count} 家公司")
        total += count
    
    print(f"\n✅ 總計: {total} 家公司")
    
    # 檢查Excel檔案
    excel_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.xlsx"
    if os.path.exists(excel_file):
        try:
            df = pd.read_excel(excel_file, sheet_name='月營收資料')
            
            print(f"\n📊 Excel檔案檢查:")
            print(f"  記錄數: {len(df):,}")
            print(f"  欄位: {list(df.columns)}")
            
            # 檢查數據完整性
            print(f"\n🔍 數據完整性檢查:")
            
            # 檢查營收數據
            revenue_count = df['營收(千元)'].notna().sum()
            print(f"  有營收數據: {revenue_count} / {len(df)} 筆")
            
            # 檢查月增率數據
            mom_count = df['營收月增率(%)'].notna().sum()
            print(f"  有月增率數據: {mom_count} / {len(df)} 筆")
            
            # 檢查年增率數據
            yoy_count = df['營收年增率(%)'].notna().sum()
            print(f"  有年增率數據: {yoy_count} / {len(df)} 筆")
            
            # 檢查累計營收數據
            cum_count = df['累計營收(千元)'].notna().sum()
            print(f"  有累計營收數據: {cum_count} / {len(df)} 筆")
            
            # 檢查累計年增率數據
            cum_yoy_count = df['累計營收年增率(%)'].notna().sum()
            print(f"  有累計年增率數據: {cum_yoy_count} / {len(df)} 筆")
            
            # 顯示前5筆完整資料
            print(f"\n🔍 前5筆完整資料:")
            for i, row in df.head(5).iterrows():
                print(f"  {i+1}. {row['股票代號']} {row['股票名稱']} [{row['市場別']}]:")
                print(f"     營收: {row['營收(千元)']} | 月增率: {row['營收月增率(%)']}% | 年增率: {row['營收年增率(%)']}%")
                print(f"     累計營收: {row['累計營收(千元)']} | 累計年增率: {row['累計營收年增率(%)']}%")
                print(f"     備註: {row['備註']}")
                print()
            
            # 檢查最高營收的公司
            print(f"🏆 營收最高的前3家公司:")
            top_companies = df.nlargest(3, '營收(千元)')
            for i, row in top_companies.iterrows():
                print(f"  {row['股票代號']} {row['股票名稱']}: {row['營收(千元)']} 千元")
                
        except Exception as e:
            print(f"❌ Excel檔案檢查失敗: {e}")
    else:
        print("❌ Excel檔案不存在")
    
    # 檢查CSV檔案
    csv_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.csv"
    if os.path.exists(csv_file):
        try:
            df_csv = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"\n📄 CSV檔案檢查:")
            print(f"  記錄數: {len(df_csv):,}")
            print(f"  欄位: {list(df_csv.columns)}")
            
            # 檢查前3筆CSV數據
            print(f"\n🔍 前3筆CSV數據:")
            for i, row in df_csv.head(3).iterrows():
                print(f"  {row['股票代號']} {row['股票名稱']}: 營收 {row['營收(千元)']} 千元")
                
        except Exception as e:
            print(f"❌ CSV檔案檢查失敗: {e}")
    else:
        print("❌ CSV檔案不存在")
    
    print(f"\n🎉 測試完成！")

if __name__ == "__main__":
    test_final_complete()
