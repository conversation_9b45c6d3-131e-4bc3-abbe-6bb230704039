"""
測試MOPS網站結構
"""

import requests
import urllib3
from bs4 import BeautifulSoup
import sys

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_mops_structure():
    """測試MOPS網站結構"""
    try:
        print('🔍 檢查MOPS網站結構...')
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        # 測試查詢API
        query_url = 'https://mops.twse.com.tw/mops/web/ajax_t51sb01'
        query_params = {
            'encodeURIComponent': '1',
            'step': '1',
            'firstin': '1',
            'TYPEK': 'sii',
            'code': ''
        }
        
        print(f'📡 請求URL: {query_url}')
        print(f'📋 參數: {query_params}')
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(query_url, params=query_params, verify=False, timeout=30)
        print(f'✅ 回應狀態: {response.status_code}')
        print(f'📏 回應長度: {len(response.text)} 字元')
        
        if response.status_code != 200:
            print(f'❌ HTTP錯誤: {response.status_code}')
            return False
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有form標籤
        forms = soup.find_all('form')
        print(f'🔍 找到 {len(forms)} 個form標籤')
        
        csv_form_found = False
        for i, form in enumerate(forms):
            print(f'  Form {i+1}:')
            form_name = form.get('name', '無')
            form_action = form.get('action', '無')
            form_method = form.get('method', '無')
            
            print(f'    name: {form_name}')
            print(f'    action: {form_action}')
            print(f'    method: {form_method}')
            
            # 查找filename input
            filename_input = form.find('input', {'name': 'filename'})
            if filename_input:
                filename_value = filename_input.get('value', '無值')
                print(f'    ✅ 找到filename: {filename_value}')
                csv_form_found = True
            else:
                print(f'    ❌ 未找到filename input')
            
            # 查找所有input
            inputs = form.find_all('input')
            print(f'    inputs: {len(inputs)} 個')
            for inp in inputs:
                inp_name = inp.get('name', '無名稱')
                inp_value = inp.get('value', '無值')
                inp_type = inp.get('type', '無類型')
                print(f'      - {inp_name} ({inp_type}): {inp_value}')
        
        # 查找包含CSV的文字
        if 'CSV' in response.text:
            print('✅ 回應中包含CSV相關內容')
        else:
            print('❌ 回應中未找到CSV相關內容')
        
        # 查找包含另存的文字
        if '另存' in response.text:
            print('✅ 回應中包含「另存」相關內容')
        else:
            print('❌ 回應中未找到「另存」相關內容')
        
        # 查找特定的form name
        fm_form = soup.find('form', {'name': 'fm'})
        if fm_form:
            print('✅ 找到name="fm"的form')
        else:
            print('❌ 未找到name="fm"的form')
            
            # 嘗試查找其他可能的form
            print('🔍 查找其他可能的form...')
            for form in forms:
                if 'filename' in str(form):
                    print(f'  找到包含filename的form: {form.get("name", "無名稱")}')
        
        # 顯示部分HTML內容
        print('\n📄 HTML內容片段:')
        html_snippet = response.text[:2000] if len(response.text) > 2000 else response.text
        print(html_snippet)
        
        return csv_form_found
        
    except Exception as e:
        print(f'❌ 檢查失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_alternative_approach():
    """測試替代方法"""
    try:
        print('\n🔍 測試替代方法...')
        
        # 嘗試直接訪問基本頁面
        base_url = 'https://mops.twse.com.tw/mops/web/t51sb01'
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get(base_url, headers=headers, verify=False, timeout=30)
        print(f'✅ 基本頁面狀態: {response.status_code}')
        
        if '基本資料查詢彙總表' in response.text:
            print('✅ 基本頁面內容正確')
        else:
            print('❌ 基本頁面內容異常')
        
        return True
        
    except Exception as e:
        print(f'❌ 替代方法失敗: {e}')
        return False

if __name__ == "__main__":
    print("🚀 開始MOPS網站結構測試")
    print("=" * 50)
    
    success1 = test_mops_structure()
    success2 = test_alternative_approach()
    
    print("\n" + "=" * 50)
    if success1:
        print("✅ MOPS結構測試成功")
    else:
        print("❌ MOPS結構測試失敗")
    
    if success2:
        print("✅ 替代方法測試成功")
    else:
        print("❌ 替代方法測試失敗")
    
    sys.exit(0 if (success1 or success2) else 1)
