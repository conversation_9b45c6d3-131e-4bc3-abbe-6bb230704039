#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試最終GUI整合 - 高殖利率烏龜策略
"""

import sys
sys.path.append('strategies')

from high_yield_turtle_strategy import HighYieldTurtleStrategy
import pandas as pd
from datetime import datetime

def test_final_gui_integration():
    """測試最終GUI整合"""
    
    print("🎯 測試最終GUI整合 - 高殖利率烏龜策略")
    print("=" * 60)
    
    # 1. 測試策略載入
    print("1️⃣ 測試策略載入...")
    try:
        strategy = HighYieldTurtleStrategy()
        print(f"   ✅ 策略載入成功")
        print(f"   📊 PE數據筆數: {len(strategy.pe_data_cache):,}")
        print(f"   📈 涵蓋股票數: {strategy.pe_data_cache['股票代號'].nunique():,} 支")
        print(f"   📅 數據時間範圍: {strategy.pe_data_cache['date'].min()} ~ {strategy.pe_data_cache['date'].max()}")
    except Exception as e:
        print(f"   ❌ 策略載入失敗: {e}")
        return False
    
    # 2. 測試GUI中常用的股票
    print("\n2️⃣ 測試GUI中常用的股票...")
    
    # 創建模擬股價數據
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    base_price = 50
    prices = [base_price * (1.01 ** i) for i in range(100)]
    
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices], 
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    
    # 測試熱門股票
    popular_stocks = [
        ('台泥', '1101'),
        ('亞泥', '1102'), 
        ('嘉泥', '1103'),
        ('幸福', '1108'),
        ('台塑', '1301'),
        ('台化', '1326'),
        ('台積電', '2330'),
        ('聯電', '2303'),
        ('富邦金', '2881'),
        ('國泰金', '2882'),
        ('開發金', '2883'),
        ('玉山金', '2884'),
        ('元大金', '2885'),
        ('兆豐金', '2886'),
        ('華南金', '2880'),
        ('永豐金', '2890')
    ]
    
    suitable_stocks = []
    high_score_stocks = []
    
    for name, stock_id in popular_stocks:
        try:
            result = strategy.analyze_stock(test_df, stock_id=stock_id)
            
            score = result.get('score', 0)
            suitable = result.get('suitable', False)
            details = result.get('details', {})
            data_source = details.get('data_source', '未知')
            
            status = "✅ 符合" if suitable else "❌ 不符合"
            print(f"   {status} {name}({stock_id}): 評分{score}/100 ({data_source})")
            
            if suitable:
                suitable_stocks.append((name, stock_id, score))
                
            if score >= 80:
                high_score_stocks.append((name, stock_id, score))
                
                # 顯示高分股票的詳細信息
                if 'pe_info' in details:
                    pe_info = details['pe_info']
                    dividend = pe_info.get('殖利率(%)', 'N/A')
                    pe_ratio = pe_info.get('本益比', 'N/A')
                    pb_ratio = pe_info.get('股價淨值比', 'N/A')
                    print(f"      📊 殖利率: {dividend}%, 本益比: {pe_ratio}, 股價淨值比: {pb_ratio}")
            
        except Exception as e:
            print(f"   ❌ {name}({stock_id}): 測試失敗 - {e}")
    
    print(f"\n   📊 測試統計:")
    print(f"      符合策略: {len(suitable_stocks)}/{len(popular_stocks)} 支")
    print(f"      高分股票 (≥80分): {len(high_score_stocks)} 支")
    
    # 3. 測試策略參數
    print("\n3️⃣ 測試策略參數...")
    try:
        # 測試不同的策略參數
        test_params = {
            'min_dividend_yield': 5.0,
            'max_pe_ratio': 20.0,
            'max_pb_ratio': 2.0
        }
        
        print(f"   📋 當前策略參數:")
        for param, value in test_params.items():
            print(f"      {param}: {value}")
        
        # 統計符合各項條件的股票數量
        pe_data = strategy.pe_data_cache
        
        # 獲取最新數據
        latest_data = pe_data.sort_values('date').groupby('股票代號').tail(1)
        
        high_dividend = latest_data[latest_data['殖利率(%)'] >= test_params['min_dividend_yield']]
        low_pe = latest_data[(latest_data['本益比'] > 0) & (latest_data['本益比'] <= test_params['max_pe_ratio'])]
        low_pb = latest_data[(latest_data['股價淨值比'] > 0) & (latest_data['股價淨值比'] <= test_params['max_pb_ratio'])]
        
        print(f"\n   📊 符合條件的股票統計:")
        print(f"      高殖利率 (≥{test_params['min_dividend_yield']}%): {len(high_dividend)} 支")
        print(f"      低本益比 (≤{test_params['max_pe_ratio']}): {len(low_pe)} 支")
        print(f"      低股價淨值比 (≤{test_params['max_pb_ratio']}): {len(low_pb)} 支")
        
        # 同時符合所有條件的股票
        combined = latest_data[
            (latest_data['殖利率(%)'] >= test_params['min_dividend_yield']) &
            (latest_data['本益比'] > 0) & (latest_data['本益比'] <= test_params['max_pe_ratio']) &
            (latest_data['股價淨值比'] > 0) & (latest_data['股價淨值比'] <= test_params['max_pb_ratio'])
        ]
        
        print(f"      同時符合所有條件: {len(combined)} 支")
        
        if len(combined) > 0:
            print(f"\n   🏆 推薦股票 (前5名):")
            top_combined = combined.nlargest(5, '殖利率(%)')
            for idx, row in top_combined.iterrows():
                stock_id = row['股票代號']
                dividend = row['殖利率(%)']
                pe_ratio = row['本益比']
                pb_ratio = row['股價淨值比']
                print(f"      {stock_id}: 殖利率{dividend:.2f}%, 本益比{pe_ratio:.2f}, 股價淨值比{pb_ratio:.2f}")
        
    except Exception as e:
        print(f"   ❌ 策略參數測試失敗: {e}")
    
    # 4. 測試GUI調用接口
    print("\n4️⃣ 測試GUI調用接口...")
    try:
        if suitable_stocks:
            test_stock = suitable_stocks[0]
            name, stock_id, score = test_stock
            
            # 模擬GUI調用
            result = strategy.analyze_stock(test_df, stock_id=stock_id)
            
            print(f"   ✅ GUI接口測試成功")
            print(f"   📊 測試股票: {name}({stock_id})")
            print(f"   📈 評分: {result['score']}/100")
            print(f"   🎯 建議: {'買入' if result['suitable'] else '觀望'}")
            
            # 檢查返回的詳細信息
            details = result.get('details', {})
            if details:
                print(f"   📋 詳細信息:")
                for key, value in details.items():
                    if key != 'pe_info':  # pe_info 太詳細，不在這裡顯示
                        print(f"      {key}: {value}")
        else:
            print("   ⚠️ 沒有符合條件的股票進行GUI接口測試")
            
    except Exception as e:
        print(f"   ❌ GUI接口測試失敗: {e}")
    
    print("\n" + "=" * 60)
    print("✅ 最終GUI整合測試完成！")
    
    if suitable_stocks:
        print(f"🎉 高殖利率烏龜策略已成功整合到GUI系統中！")
        print(f"📊 在測試的 {len(popular_stocks)} 支熱門股票中，有 {len(suitable_stocks)} 支符合策略條件")
        
        if high_score_stocks:
            print(f"⭐ 其中 {len(high_score_stocks)} 支獲得高分評價 (≥80分)")
    else:
        print("⚠️ 當前測試條件下沒有股票符合策略，可能需要調整策略參數")
    
    return True

if __name__ == "__main__":
    test_final_gui_integration()
