#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正ROE數據匯入問題
清理數據庫並重新匯入正確的數據
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime

def clean_roe_database():
    """清理ROE數據庫"""
    db_path = 'D:/Finlab/history/tables/roe_data.db'
    
    print("🧹 清理ROE數據庫...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 刪除所有資料
        cursor.execute("DELETE FROM roe_data")
        
        # 重置自增ID
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='roe_data'")
        
        conn.commit()
        conn.close()
        
        print("✅ ROE數據庫清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理數據庫失敗: {e}")
        return False

def analyze_csv_structure():
    """分析CSV文件結構"""
    csv_file = 'D:/Finlab/history/tables/roe_data_scraped_20250716_002721.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return None
    
    print(f"📊 分析CSV文件: {os.path.basename(csv_file)}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"📋 CSV欄位 ({len(df.columns)}個):")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. '{col}'")
        
        print(f"\n📈 資料筆數: {len(df)}")
        
        # 顯示前2筆資料的關鍵欄位
        print(f"\n📋 前2筆關鍵資料:")
        for i, row in df.head(2).iterrows():
            print(f"  第{i+1}筆:")
            print(f"    排名: {row.get('排 名', 'N/A')}")
            print(f"    代號: {row.get('stock_code', 'N/A')}")
            print(f"    名稱: {row.get('stock_name', 'N/A')}")
            print(f"    ROE: {row.get('ROE (%)', 'N/A')}")
            print(f"    ROE變化: {row.get('ROE 增減', 'N/A')}")
            print(f"    EPS: {row.get('EPS (元)', 'N/A')}")
            print(f"    年度: {row.get('財報 年度', 'N/A')}")
        
        return df
        
    except Exception as e:
        print(f"❌ 分析CSV失敗: {e}")
        return None

def clean_numeric_value(value):
    """清理數值"""
    if pd.isna(value) or value == '' or value is None:
        return None
    
    # 轉換為字符串並清理
    str_value = str(value).strip()
    
    # 移除常見的非數字字符
    str_value = str_value.replace('+', '').replace('%', '').replace(',', '').replace('--', '')
    
    # 處理空字符串
    if str_value == '' or str_value == '-':
        return None
    
    try:
        return float(str_value)
    except:
        return None

def import_csv_correctly():
    """正確匯入CSV數據"""
    csv_file = 'D:/Finlab/history/tables/roe_data_scraped_20250716_002721.csv'
    db_path = 'D:/Finlab/history/tables/roe_data.db'
    
    print(f"\n📥 正確匯入CSV數據...")
    
    try:
        # 讀取CSV
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # 連接數據庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 確保表格存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roe_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                roe_value REAL,
                roe_change REAL,
                eps_value REAL,
                report_year INTEGER,
                rank_position INTEGER,
                crawl_date TEXT,
                data_source TEXT DEFAULT 'goodinfo_csv',
                UNIQUE(stock_code, report_year)
            )
        """)
        
        success_count = 0
        error_count = 0
        
        print(f"📊 開始處理 {len(df)} 筆資料...")
        
        for index, row in df.iterrows():
            try:
                # 提取和清理數據
                stock_code = str(row.get('stock_code', '')).strip()
                stock_name = str(row.get('stock_name', '')).strip()
                
                # 清理數值欄位
                roe_value = clean_numeric_value(row.get('ROE (%)'))
                roe_change = clean_numeric_value(row.get('ROE 增減'))
                eps_value = clean_numeric_value(row.get('EPS (元)'))
                report_year = clean_numeric_value(row.get('財報 年度'))
                rank_position = clean_numeric_value(row.get('排 名'))
                
                # 轉換年度為整數
                if report_year is not None:
                    report_year = int(report_year)
                
                # 轉換排名為整數
                if rank_position is not None:
                    rank_position = int(rank_position)
                
                # 獲取爬取時間
                crawl_date = row.get('crawl_date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                data_source = row.get('data_source', 'goodinfo_scraped')
                
                # 驗證必要欄位
                if not stock_code or stock_code == 'nan':
                    print(f"⚠️ 第{index+1}筆：股票代號為空，跳過")
                    continue
                
                # 插入數據庫
                cursor.execute("""
                    INSERT OR REPLACE INTO roe_data
                    (stock_code, stock_name, roe_value, roe_change, eps_value,
                     report_year, rank_position, crawl_date, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stock_code,
                    stock_name,
                    roe_value,
                    roe_change,
                    eps_value,
                    report_year,
                    rank_position,
                    crawl_date,
                    data_source
                ))
                
                success_count += 1
                
                # 顯示進度
                if success_count % 50 == 0:
                    print(f"  已處理 {success_count} 筆...")
                
            except Exception as e:
                error_count += 1
                print(f"⚠️ 第{index+1}筆匯入失敗: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 匯入完成:")
        print(f"  • 成功: {success_count} 筆")
        print(f"  • 失敗: {error_count} 筆")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 匯入失敗: {e}")
        return False

def verify_import_result():
    """驗證匯入結果"""
    db_path = 'D:/Finlab/history/tables/roe_data.db'
    
    print(f"\n🔍 驗證匯入結果...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查總數
        cursor.execute("SELECT COUNT(*) FROM roe_data")
        total_count = cursor.fetchone()[0]
        print(f"📈 總資料筆數: {total_count}")
        
        # 檢查有效數據
        cursor.execute("SELECT COUNT(*) FROM roe_data WHERE roe_value IS NOT NULL")
        valid_roe_count = cursor.fetchone()[0]
        print(f"📊 有效ROE數據: {valid_roe_count}")
        
        # 檢查年度分布
        cursor.execute("SELECT report_year, COUNT(*) FROM roe_data GROUP BY report_year ORDER BY report_year")
        year_counts = cursor.fetchall()
        print(f"📅 年度分布:")
        for year, count in year_counts:
            print(f"  • {year}年: {count}筆")
        
        # 顯示前5筆資料
        cursor.execute("""
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year, rank_position
            FROM roe_data 
            WHERE roe_value IS NOT NULL
            ORDER BY rank_position 
            LIMIT 5
        """)
        top_records = cursor.fetchall()
        
        print(f"\n📋 ROE最高前5名:")
        for i, record in enumerate(top_records, 1):
            stock_code, stock_name, roe_value, roe_change, eps_value, report_year, rank_position = record
            print(f"  {i}. {stock_code} {stock_name} - ROE: {roe_value}% (變化: {roe_change}) EPS: {eps_value} 年度: {report_year}")
        
        conn.close()
        
        return valid_roe_count > 0
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔧 ROE數據匯入修正工具")
    print("=" * 50)
    
    # 1. 分析CSV結構
    df = analyze_csv_structure()
    if df is None:
        return
    
    # 2. 清理數據庫
    if not clean_roe_database():
        return
    
    # 3. 重新匯入數據
    if not import_csv_correctly():
        return
    
    # 4. 驗證結果
    if verify_import_result():
        print(f"\n🎉 ROE數據修正完成！")
        print(f"💡 現在可以在ROE資料查看窗口中看到正確的數據了")
    else:
        print(f"\n❌ ROE數據修正失敗")

if __name__ == "__main__":
    main()
