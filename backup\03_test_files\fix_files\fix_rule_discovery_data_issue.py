#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正交易規則發現系統的數據問題
生成測試數據並修正分析器
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_stock_data():
    """創建測試股票數據"""
    print("📊 創建測試股票數據...")
    
    # 台股常見股票代碼
    stock_codes = [
        '2330', '2317', '2454', '2881', '2882', '2883', '2884', '2885',
        '2886', '2887', '2888', '2889', '2890', '2891', '2892', '2893',
        '1101', '1102', '1103', '1216', '1301', '1303', '1326', '1402',
        '2002', '2105', '2207', '2301', '2303', '2308', '2311', '2312',
        '2313', '2314', '2324', '2327', '2328', '2329', '2357', '2382',
        '2395', '2408', '2409', '2412', '2474', '2603', '2609', '2610',
        '2633', '2801', '2823', '2880', '3008', '3045', '3711', '4904',
        '4938', '5871', '5876', '5880', '6505', '6669', '8454', '8996'
    ]
    
    # 生成2年的交易日
    start_date = datetime.now() - timedelta(days=800)
    end_date = datetime.now() - timedelta(days=1)
    
    # 生成交易日（排除週末）
    trading_days = []
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # 週一到週五
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    print(f"📅 生成 {len(trading_days)} 個交易日")
    
    # 連接資料庫
    conn = sqlite3.connect('db/price.db')
    cursor = conn.cursor()
    
    # 清空現有數據
    cursor.execute('DELETE FROM stock_daily_data')
    
    total_records = 0
    
    for stock_code in stock_codes:
        print(f"📈 生成股票 {stock_code} 的數據...")
        
        # 初始價格
        base_price = random.uniform(20, 500)
        current_price = base_price
        
        stock_data = []
        
        for date in trading_days:
            # 生成價格變動（隨機遊走 + 趨勢）
            daily_return = np.random.normal(0.001, 0.025)  # 平均0.1%，標準差2.5%
            current_price *= (1 + daily_return)
            
            # 確保價格不會太低
            if current_price < 5:
                current_price = 5 + random.uniform(0, 10)
            
            # 生成開高低收
            open_price = current_price * (1 + np.random.normal(0, 0.005))
            high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.01)))
            close_price = current_price
            
            # 生成成交量
            volume = int(np.random.lognormal(13, 1))  # 對數正態分布
            
            stock_data.append({
                'stock_id': stock_code,
                'date': date.strftime('%Y-%m-%d'),
                'Open': round(open_price, 2),
                'High': round(high_price, 2),
                'Low': round(low_price, 2),
                'Close': round(close_price, 2),
                'Volume': volume
            })
        
        # 批量插入數據
        cursor.executemany('''
            INSERT INTO stock_daily_data (stock_id, date, Open, High, Low, Close, Volume)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', [(d['stock_id'], d['date'], d['Open'], d['High'], d['Low'], d['Close'], d['Volume']) 
              for d in stock_data])
        
        total_records += len(stock_data)
    
    conn.commit()
    conn.close()
    
    print(f"✅ 成功創建 {total_records} 筆測試數據")
    print(f"📊 涵蓋 {len(stock_codes)} 檔股票")
    print(f"📅 時間範圍: {trading_days[0].strftime('%Y-%m-%d')} 到 {trading_days[-1].strftime('%Y-%m-%d')}")
    
    return True

def fix_analyzer_parameters():
    """修正分析器參數"""
    print("\n🔧 修正分析器參數...")
    
    try:
        # 讀取原始文件
        with open('mass_backtest_analyzer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修正參數
        modifications = [
            ('self.min_data_days = 730', 'self.min_data_days = 200'),  # 降低最少數據天數
            ('self.min_trades = 10', 'self.min_trades = 3'),  # 降低最少交易次數
            ('WHERE date >= date(\'now\', \'-3 years\')', 'WHERE date >= date(\'now\', \'-2 years\')')  # 縮短查詢範圍
        ]
        
        modified = False
        for old, new in modifications:
            if old in content:
                content = content.replace(old, new)
                modified = True
                print(f"✅ 修正: {old} -> {new}")
        
        if modified:
            # 備份原始文件
            with open('mass_backtest_analyzer_backup.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 寫入修正後的文件
            with open('mass_backtest_analyzer.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 分析器參數已修正")
        else:
            print("ℹ️ 分析器參數無需修正")
        
        return True
        
    except Exception as e:
        print(f"❌ 修正分析器參數失敗: {e}")
        return False

def test_fixed_system():
    """測試修正後的系統"""
    print("\n🧪 測試修正後的系統...")
    
    try:
        from mass_backtest_analyzer import MassBacktestAnalyzer
        
        # 創建分析器
        analyzer = MassBacktestAnalyzer()
        
        # 載入股票清單
        stocks = analyzer.load_stock_list()
        print(f"📊 載入 {len(stocks)} 檔股票")
        
        if len(stocks) == 0:
            print("❌ 沒有載入到股票數據")
            return False
        
        # 測試單檔股票分析
        test_stock = stocks[0]
        print(f"📈 測試股票: {test_stock}")
        
        # 載入股票數據
        df = analyzer.load_stock_data(test_stock)
        if df.empty:
            print("❌ 載入股票數據失敗")
            return False
        
        print(f"✅ 載入 {len(df)} 筆數據")
        
        # 計算技術指標
        df = analyzer.calculate_technical_indicators(df)
        
        # 生成信號
        signals = analyzer.generate_strategy_signals(df)
        
        # 統計信號
        total_signals = 0
        for strategy, signal_data in signals.items():
            buy_count = len(signal_data['buy'])
            sell_count = len(signal_data['sell'])
            total_signals += buy_count + sell_count
            print(f"📊 {strategy}: 買入{buy_count}個, 賣出{sell_count}個")
        
        if total_signals > 0:
            print(f"✅ 系統修正成功！總共生成 {total_signals} 個信號")
            return True
        else:
            print("⚠️ 系統可以運行但沒有生成信號")
            return False
        
    except Exception as e:
        print(f"❌ 測試修正後的系統失敗: {e}")
        return False

def create_quick_test_script():
    """創建快速測試腳本"""
    print("\n📝 創建快速測試腳本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試交易規則發現系統
"""

from trading_rule_miner import TradingRuleMiner
from trading_mantra_generator import TradingMantraGenerator

def quick_test():
    """快速測試"""
    print("🚀 快速測試交易規則發現系統")
    print("="*50)
    
    try:
        # 創建規則挖掘器
        rule_miner = TradingRuleMiner()
        
        # 執行小規模分析（50檔股票）
        print("📊 開始分析（50檔股票）...")
        combinations = rule_miner.run_mass_analysis(50)
        
        print(f"✅ 發現 {len(combinations)} 種組合模式")
        
        # 提取規則
        rules = rule_miner.extract_trading_rules(combinations)
        print(f"📋 提取出 {len(rules)} 條交易規則")
        
        if len(rules) > 0:
            # 生成口訣
            mantra_generator = TradingMantraGenerator()
            mantras = mantra_generator.generate_all_mantras(rules)
            
            print("\\n🎭 生成的口訣:")
            for i, mantra in enumerate(mantras['buy_mantras'][:3], 1):
                print(f"  {i}. {mantra.mantra_text}")
            
            print("\\n✅ 系統運行正常！")
        else:
            print("\\n⚠️ 沒有發現有效規則，可能需要調整參數")
        
    except Exception as e:
        print(f"\\n❌ 測試失敗: {e}")

if __name__ == "__main__":
    quick_test()
'''
    
    with open('quick_test_rule_discovery.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 快速測試腳本已創建: quick_test_rule_discovery.py")

def main():
    """主函數"""
    print("🔧 修正交易規則發現系統的數據問題")
    print("="*60)
    
    # 1. 創建測試數據
    if not create_test_stock_data():
        print("❌ 創建測試數據失敗")
        return
    
    # 2. 修正分析器參數
    if not fix_analyzer_parameters():
        print("❌ 修正分析器參數失敗")
        return
    
    # 3. 測試修正後的系統
    if not test_fixed_system():
        print("❌ 系統測試失敗")
        return
    
    # 4. 創建快速測試腳本
    create_quick_test_script()
    
    print("\n" + "="*60)
    print("🎉 修正完成！")
    print("\n💡 現在您可以:")
    print("1. 重新啟動GUI工具測試")
    print("2. 運行快速測試: python quick_test_rule_discovery.py")
    print("3. 使用較小的參數開始分析（如100檔股票）")
    
    print("\n📊 建議的分析參數:")
    print("• 分析股票數量: 100-200檔")
    print("• 最低成功率: 60%")
    print("• 最低平均報酬: 1%")

if __name__ == "__main__":
    main()
