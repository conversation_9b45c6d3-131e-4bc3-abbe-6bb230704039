#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
財經新聞爬蟲基礎框架
基於 hsunAlfred/newsCrawler 改寫，使用SQLite資料庫
"""

from abc import abstractmethod, ABCMeta
from datetime import datetime, timedelta
import traceback
import logging
import sqlite3
import os

class NewsCrawlerBase(metaclass=ABCMeta):
    """新聞爬蟲基礎類別"""
    
    def __init__(self, db_path=None):
        """
        初始化新聞爬蟲
        
        Args:
            db_path: 資料庫路徑，預設為 D:/Finlab/history/tables/news.db
        """
        self.timeNow = datetime.today()
        
        # 設置資料庫路徑
        if db_path is None:
            # 預設與price.db放在同一目錄
            default_dir = "D:/Finlab/history/tables"
            if not os.path.exists(default_dir):
                os.makedirs(default_dir, exist_ok=True)
            self.db_path = os.path.join(default_dir, "news.db")
        else:
            self.db_path = db_path
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化資料庫
        self.init_database()
        
    def init_database(self):
        """初始化SQLite資料庫和表格"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 建立新聞內容表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news_content (
                        no INTEGER PRIMARY KEY AUTOINCREMENT,
                        news_id TEXT UNIQUE NOT NULL,
                        date TEXT,
                        time TEXT,
                        source TEXT,
                        title TEXT,
                        reporter TEXT,
                        link TEXT,
                        content TEXT,
                        stock_code TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 建立新聞標籤表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news_tag (
                        no INTEGER PRIMARY KEY AUTOINCREMENT,
                        source TEXT NOT NULL,
                        news_id TEXT NOT NULL,
                        tag TEXT,
                        FOREIGN KEY (news_id) REFERENCES news_content (news_id)
                    )
                ''')
                
                # 建立索引以提高查詢效能
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_date ON news_content(date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_stock ON news_content(stock_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_source ON news_content(source)')
                
                conn.commit()
                self.logger.info(f"✅ 資料庫初始化完成: {self.db_path}")
                
        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")
            raise

    def run(self, ndays: int = 1, interval: str = "0600", stock_code: str = None):
        """
        執行新聞爬取
        
        Args:
            ndays: 最近n天的資料
            interval: 一天開始的時間 (HHMM格式)
            stock_code: 特定股票代碼，None表示爬取所有新聞
        
        Returns:
            str: 執行結果
        """
        ndaysAgo = self.timeNow - timedelta(days=ndays)
        
        try:
            self.logger.info(f"🚀 開始爬取新聞: {ndays}天前至今")
            if stock_code:
                self.logger.info(f"🎯 目標股票: {stock_code}")
            
            # 提取原始資料
            rawDatas = self.extract(ndaysAgo, interval, stock_code)
            self.logger.info(f"📊 提取到 {len(rawDatas)} 筆原始資料")
            
            if not rawDatas:
                return "⚠️ 未找到新聞資料"
            
            # 轉換資料格式
            cleanDatas, cleanTags = self.transform(rawDatas)
            self.logger.info(f"🔄 轉換完成: {len(list(cleanDatas))} 筆新聞, {len(list(cleanTags))} 筆標籤")
            
            # 重新生成生成器（因為上面已經消耗了）
            cleanDatas, cleanTags = self.transform(rawDatas)
            
            # 載入到資料庫
            status = self.load(cleanDatas, cleanTags)
            self.logger.info(f"✅ 新聞爬取完成: {status}")
            
            return status
            
        except Exception as e:
            error_msg = self.errLog(e)
            self.logger.error(f"❌ 新聞爬取失敗: {error_msg}")
            return error_msg

    @abstractmethod
    def extract(self, nDaysAgo, interval: str, stock_code: str = None) -> list:
        """
        提取新聞資料
        
        Args:
            nDaysAgo: n天前的datetime
            interval: 時間間隔
            stock_code: 股票代碼
            
        Returns:
            list: [(news_id, 日期, 時間, 標題, 連結, 記者, 內文, tag, stock_code), ...]
        """
        pass

    @abstractmethod
    def transform(self, rawDatas):
        """
        轉換資料格式
        
        Args:
            rawDatas: 原始資料列表
            
        Returns:
            tuple: (cleanDatas生成器, cleanTags生成器)
        """
        pass

    def load(self, cleanDatas, cleanTags):
        """
        載入資料到SQLite資料庫
        
        Args:
            cleanDatas: 清理後的新聞資料
            cleanTags: 清理後的標籤資料
            
        Returns:
            str: 執行結果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 插入新聞內容
                news_count = 0
                for data in cleanDatas:
                    try:
                        cursor.execute('''
                            INSERT OR IGNORE INTO news_content 
                            (news_id, date, time, source, title, reporter, link, content, stock_code)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', data)
                        if cursor.rowcount > 0:
                            news_count += 1
                    except Exception as e:
                        self.logger.warning(f"插入新聞失敗: {e}")
                        continue
                
                # 插入標籤
                tag_count = 0
                for tag_data in cleanTags:
                    if tag_data[-1]:  # 確保標籤不為空
                        try:
                            cursor.execute('''
                                INSERT INTO news_tag (source, news_id, tag)
                                VALUES (?, ?, ?)
                            ''', tag_data)
                            tag_count += 1
                        except Exception as e:
                            self.logger.warning(f"插入標籤失敗: {e}")
                            continue
                
                conn.commit()
                
                result = f"✅ 成功載入 {news_count} 筆新聞, {tag_count} 筆標籤"
                self.logger.info(result)
                return result
                
        except Exception as e:
            error_msg = f"❌ 資料載入失敗: {e}"
            self.logger.error(error_msg)
            return error_msg

    def get_news_by_stock(self, stock_code: str, days: int = 7):
        """
        根據股票代碼獲取新聞
        
        Args:
            stock_code: 股票代碼
            days: 最近幾天的新聞
            
        Returns:
            list: 新聞列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 計算日期範圍
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
                
                cursor.execute('''
                    SELECT news_id, date, time, source, title, reporter, link, content
                    FROM news_content
                    WHERE stock_code = ? AND date >= ?
                    ORDER BY date DESC, time DESC
                ''', (stock_code, start_date))
                
                results = cursor.fetchall()
                
                # 轉換為字典格式
                news_list = []
                for row in results:
                    news_list.append({
                        'news_id': row[0],
                        'date': row[1],
                        'time': row[2],
                        'source': row[3],
                        'title': row[4],
                        'reporter': row[5],
                        'link': row[6],
                        'content': row[7]
                    })
                
                return news_list
                
        except Exception as e:
            self.logger.error(f"❌ 獲取股票新聞失敗: {e}")
            return []

    def errLog(self, e):
        """記錄錯誤日誌"""
        error_detail = f'{str(e)}\n{traceback.format_exc()}'
        self.logger.error(error_detail)
        return f'{e}'

class NewsCrawlerException(Exception):
    """新聞爬蟲異常類別"""
    
    def __init__(self, *args) -> None:
        super().__init__(args)
