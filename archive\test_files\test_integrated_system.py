#!/usr/bin/env python3
"""
測試整合 Twelve Data 後的系統
"""

import pandas as pd
import logging
import time

# 設置日誌
logging.basicConfig(level=logging.INFO)

def test_enhanced_data_fetcher():
    """測試增強版數據獲取器"""
    print("🧪 測試增強版數據獲取器...")
    
    try:
        from enhanced_data_fetcher import EnhancedDataFetcher
        
        fetcher = EnhancedDataFetcher()
        
        # 測試台股（應該跳過 Twelve Data）
        print(f"\n📊 測試台股數據獲取:")
        taiwan_stocks = ['2330', '2317', '2454']
        
        for stock in taiwan_stocks:
            print(f"\n🔍 測試 {stock}:")
            df = fetcher.get_stock_data(stock, days=10)
            
            if not df.empty:
                print(f"   ✅ 成功獲取 {len(df)} 筆數據")
                print(f"   最新價格: {df['Close'].iloc[-1]:.2f}")
                print(f"   數據範圍: {df['date'].min()} 到 {df['date'].max()}")
            else:
                print(f"   ❌ 無法獲取數據")
        
        # 測試美股（應該可以使用 Twelve Data）
        print(f"\n🇺🇸 測試美股數據獲取:")
        us_stocks = ['AAPL', 'MSFT', 'GOOGL']
        
        for stock in us_stocks:
            print(f"\n🔍 測試 {stock}:")
            df = fetcher.get_stock_data(stock, days=10)
            
            if not df.empty:
                print(f"   ✅ 成功獲取 {len(df)} 筆數據")
                print(f"   最新價格: ${df['Close'].iloc[-1]:.2f}")
                print(f"   數據範圍: {df['date'].min()} 到 {df['date'].max()}")
            else:
                print(f"   ❌ 無法獲取數據")
            
            time.sleep(2)  # 避免API限制
        
        return True
        
    except Exception as e:
        print(f"❌ 增強版數據獲取器測試失敗: {e}")
        return False

def test_intraday_data_fetcher():
    """測試盤中數據獲取器"""
    print(f"\n🧪 測試盤中數據獲取器...")
    
    try:
        from intraday_data_fetcher import get_intraday_data, get_market_status
        
        # 測試市場狀態
        print(f"\n📊 測試市場狀態:")
        status = get_market_status()
        print(f"   市場狀態: {status}")
        
        # 測試台股盤中數據
        print(f"\n📈 測試台股盤中數據:")
        taiwan_stocks = ['2330', '2317']
        
        for stock in taiwan_stocks:
            print(f"\n🔍 測試 {stock} 盤中數據:")
            df = get_intraday_data(stock, '5m')
            
            if not df.empty:
                print(f"   ✅ 成功獲取 {len(df)} 筆5分鐘數據")
                if 'close' in df.columns:
                    print(f"   最新價格: {df['close'].iloc[-1]:.2f}")
                if 'datetime' in df.columns:
                    print(f"   時間範圍: {df['datetime'].min()} 到 {df['datetime'].max()}")
            else:
                print(f"   ❌ 無法獲取盤中數據")
        
        # 測試美股盤中數據（如果市場開盤）
        print(f"\n🇺🇸 測試美股盤中數據:")
        us_stocks = ['AAPL']
        
        for stock in us_stocks:
            print(f"\n🔍 測試 {stock} 盤中數據:")
            df = get_intraday_data(stock, '5m')
            
            if not df.empty:
                print(f"   ✅ 成功獲取 {len(df)} 筆5分鐘數據")
                if 'close' in df.columns:
                    print(f"   最新價格: ${df['close'].iloc[-1]:.2f}")
                if 'datetime' in df.columns:
                    print(f"   時間範圍: {df['datetime'].min()} 到 {df['datetime'].max()}")
            else:
                print(f"   ❌ 無法獲取盤中數據")
            
            time.sleep(2)  # 避免API限制
        
        return True
        
    except Exception as e:
        print(f"❌ 盤中數據獲取器測試失敗: {e}")
        return False

def test_twelvedata_direct():
    """直接測試 Twelve Data API"""
    print(f"\n🧪 直接測試 Twelve Data API...")
    
    try:
        from twelvedata_fetcher import TwelveDataFetcher
        
        fetcher = TwelveDataFetcher()
        
        # 測試API連接
        if fetcher.test_api_connection():
            print("✅ Twelve Data API 連接正常")
            
            # 測試美股數據
            print(f"\n📈 測試美股數據:")
            us_stocks = ['AAPL', 'MSFT']
            
            for stock in us_stocks:
                print(f"\n🔍 測試 {stock}:")
                
                # 測試日K線
                df_daily = fetcher.get_stock_data(stock, '1day', 5)
                if not df_daily.empty:
                    print(f"   ✅ 日K線: {len(df_daily)} 筆數據")
                    print(f"   最新價格: ${df_daily['close'].iloc[-1]:.2f}")
                
                # 測試5分鐘數據
                df_5min = fetcher.get_intraday_data(stock, '5min')
                if not df_5min.empty:
                    print(f"   ✅ 5分鐘: {len(df_5min)} 筆數據")
                
                time.sleep(8)  # 避免API限制
            
            # 測試API使用情況
            usage = fetcher.get_api_usage()
            if usage:
                print(f"\n📊 API使用情況:")
                print(f"   計劃: {usage.get('plan_category', 'unknown')}")
                print(f"   今日使用: {usage.get('daily_usage', 'unknown')}/{usage.get('plan_daily_limit', 'unknown')}")
            
            return True
        else:
            print("❌ Twelve Data API 連接失敗")
            return False
        
    except Exception as e:
        print(f"❌ Twelve Data 直接測試失敗: {e}")
        return False

def test_main_program():
    """測試主程式是否能正常啟動"""
    print(f"\n🧪 測試主程式啟動...")
    
    try:
        # 這裡只是導入測試，不實際啟動GUI
        import O3mh_gui_v21_optimized
        print("✅ 主程式模組導入成功")
        return True
        
    except Exception as e:
        print(f"❌ 主程式測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 開始測試整合 Twelve Data 後的系統...")
    
    results = []
    
    # 測試各個組件
    results.append(("Twelve Data 直接測試", test_twelvedata_direct()))
    results.append(("增強版數據獲取器", test_enhanced_data_fetcher()))
    results.append(("盤中數據獲取器", test_intraday_data_fetcher()))
    results.append(("主程式啟動", test_main_program()))
    
    # 總結結果
    print(f"\n🎉 測試完成！")
    print(f"\n📋 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎊 所有測試都通過！系統整合成功！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關組件")
    
    print(f"\n💡 說明:")
    print(f"   - Twelve Data 免費版支援美股、外匯、加密貨幣")
    print(f"   - 台股數據仍使用 yfinance 等現有數據源")
    print(f"   - 系統會自動選擇最適合的數據源")
    print(f"   - 盤中監控功能已整合多數據源支援")
