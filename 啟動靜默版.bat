@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 靜默版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        靜默版 - 完全無錯誤訊息
echo ========================================
echo.

if exist "dist\StockAnalyzer_Silent.exe" (
    echo ✅ 找到靜默版
    echo 🚀 正在啟動...
    echo.
    echo 💡 靜默版特點：
    echo    ✓ 完全無錯誤訊息顯示
    echo    ✓ 最乾淨的用戶體驗
    echo    ✓ 保持核心功能完整
    echo    ✓ 最小檔案大小
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Silent.exe"
    
    echo ✅ 靜默版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到靜默版
    echo.
    echo 請重新編譯：
    echo    python silent_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 功能說明：
echo    ✓ 核心股票分析功能
echo    ✓ 數據查詢和篩選
echo    ✓ Excel 報告導出
echo    ✓ 基本用戶界面
echo    ✓ 完全靜默運行
echo.
echo ⚠️ 注意：
echo    - 圖表功能已移除以確保穩定
echo    - 核心選股功能完全保留
echo    - 無任何錯誤訊息干擾
echo.

timeout /t 5 >nul
