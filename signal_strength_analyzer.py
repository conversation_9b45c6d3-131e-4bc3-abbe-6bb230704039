#!/usr/bin/env python3
"""
信號強度分析器
為不同策略的買賣信號添加強度提示文字
"""

import pandas as pd
import numpy as np
from typing import Dict, <PERSON><PERSON>, List

class SignalStrengthAnalyzer:
    """信號強度分析器"""
    
    def __init__(self):
        self.rsi_thresholds = {
            'oversold_strong': 20,
            'oversold_moderate': 30,
            'overbought_moderate': 70,
            'overbought_strong': 80
        }
        
        self.volume_thresholds = {
            'low': 0.5,
            'normal': 1.0,
            'high': 2.0,
            'extreme': 3.0
        }
    
    def analyze_rsi_signal_strength(self, df: pd.DataFrame, index: int) -> Tuple[str, str, str]:
        """
        分析RSI信號強度
        返回: (強度等級, 提示文字, 顏色)
        """
        try:
            if 'RSI' not in df.columns or index >= len(df):
                return "中等", "RSI信號", "#FFA500"
            
            rsi = df['RSI'].iloc[index]
            price = df['Close'].iloc[index]
            
            # 計算成交量比率
            volume_ratio = 1.0
            if 'Volume' in df.columns and index >= 10:
                current_volume = df['Volume'].iloc[index]
                avg_volume = df['Volume'].iloc[index-10:index].mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # 買入信號強度分析
            if rsi <= self.rsi_thresholds['oversold_strong']:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "極強", "超跌爆量", "#FF0000"
                else:
                    return "強", "嚴重超賣", "#FF4500"
            elif rsi <= self.rsi_thresholds['oversold_moderate']:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "強", "超賣放量", "#FF6600"
                else:
                    return "中等", "超賣反彈", "#FFA500"
            
            # 賣出信號強度分析
            elif rsi >= self.rsi_thresholds['overbought_strong']:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "極強", "過熱爆量", "#8B0000"
                else:
                    return "強", "嚴重過熱", "#DC143C"
            elif rsi >= self.rsi_thresholds['overbought_moderate']:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "強", "過熱放量", "#FF1493"
                else:
                    return "中等", "過熱回調", "#FF69B4"
            
            return "弱", "信號微弱", "#D3D3D3"
            
        except Exception as e:
            return "中等", "RSI信號", "#FFA500"
    
    def analyze_macd_signal_strength(self, df: pd.DataFrame, index: int) -> Tuple[str, str, str]:
        """
        分析MACD信號強度
        返回: (強度等級, 提示文字, 顏色)
        """
        try:
            if index >= len(df) or index < 1:
                return "中等", "MACD信號", "#FFA500"
            
            # 檢查是否有MACD數據
            macd_cols = [col for col in df.columns if 'MACD' in col.upper()]
            if not macd_cols:
                return "中等", "MACD信號", "#FFA500"
            
            # 假設有MACD和Signal線
            current_price = df['Close'].iloc[index]
            prev_price = df['Close'].iloc[index-1]
            
            # 計算成交量比率
            volume_ratio = 1.0
            if 'Volume' in df.columns and index >= 10:
                current_volume = df['Volume'].iloc[index]
                avg_volume = df['Volume'].iloc[index-10:index].mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # 價格變化幅度
            price_change = (current_price - prev_price) / prev_price if prev_price > 0 else 0
            
            # 買入信號（金叉）
            if price_change > 0:
                if volume_ratio >= self.volume_thresholds['extreme']:
                    return "極強", "金叉爆量", "#00FF00"
                elif volume_ratio >= self.volume_thresholds['high']:
                    return "強", "金叉放量", "#32CD32"
                elif abs(price_change) > 0.03:
                    return "強", "強勢金叉", "#228B22"
                else:
                    return "中等", "金叉信號", "#90EE90"
            
            # 賣出信號（死叉）
            else:
                if volume_ratio >= self.volume_thresholds['extreme']:
                    return "極強", "死叉爆量", "#8B0000"
                elif volume_ratio >= self.volume_thresholds['high']:
                    return "強", "死叉放量", "#DC143C"
                elif abs(price_change) > 0.03:
                    return "強", "強勢死叉", "#B22222"
                else:
                    return "中等", "死叉信號", "#F08080"
            
        except Exception as e:
            return "中等", "MACD信號", "#FFA500"
    
    def analyze_bollinger_signal_strength(self, df: pd.DataFrame, index: int) -> Tuple[str, str, str]:
        """
        分析布林通道信號強度
        返回: (強度等級, 提示文字, 顏色)
        """
        try:
            if index >= len(df):
                return "中等", "布林信號", "#FFA500"
            
            current_price = df['Close'].iloc[index]
            
            # 計算簡單的布林通道
            if index >= 20:
                sma20 = df['Close'].iloc[index-19:index+1].mean()
                std20 = df['Close'].iloc[index-19:index+1].std()
                upper_band = sma20 + 2 * std20
                lower_band = sma20 - 2 * std20
                
                # 計算價格相對位置
                band_width = upper_band - lower_band
                price_position = (current_price - lower_band) / band_width if band_width > 0 else 0.5
                
                # 計算成交量比率
                volume_ratio = 1.0
                if 'Volume' in df.columns and index >= 10:
                    current_volume = df['Volume'].iloc[index]
                    avg_volume = df['Volume'].iloc[index-10:index].mean()
                    volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
                
                # 下軌買入信號
                if price_position <= 0.1:  # 接近或跌破下軌
                    if volume_ratio >= self.volume_thresholds['extreme']:
                        return "極強", "下軌爆量", "#FF0000"
                    elif volume_ratio >= self.volume_thresholds['high']:
                        return "強", "下軌放量", "#FF4500"
                    else:
                        return "中等", "下軌支撐", "#FFA500"
                
                # 上軌賣出信號
                elif price_position >= 0.9:  # 接近或突破上軌
                    if volume_ratio >= self.volume_thresholds['extreme']:
                        return "極強", "上軌爆量", "#8B0000"
                    elif volume_ratio >= self.volume_thresholds['high']:
                        return "強", "上軌放量", "#DC143C"
                    else:
                        return "中等", "上軌阻力", "#FF69B4"
                
                # 中軌附近
                elif 0.4 <= price_position <= 0.6:
                    return "弱", "中軌整理", "#D3D3D3"
            
            return "中等", "布林信號", "#FFA500"
            
        except Exception as e:
            return "中等", "布林信號", "#FFA500"
    
    def analyze_ma_cross_signal_strength(self, df: pd.DataFrame, index: int) -> Tuple[str, str, str]:
        """
        分析移動平均交叉信號強度
        返回: (強度等級, 提示文字, 顏色)
        """
        try:
            if index >= len(df) or index < 30:
                return "中等", "均線信號", "#FFA500"
            
            # 計算短期和長期移動平均
            short_ma = df['Close'].iloc[index-9:index+1].mean()  # 10日均線
            long_ma = df['Close'].iloc[index-29:index+1].mean()   # 30日均線
            
            # 前一日的移動平均
            prev_short_ma = df['Close'].iloc[index-10:index].mean()
            prev_long_ma = df['Close'].iloc[index-30:index].mean()
            
            current_price = df['Close'].iloc[index]
            
            # 計算成交量比率
            volume_ratio = 1.0
            if 'Volume' in df.columns and index >= 10:
                current_volume = df['Volume'].iloc[index]
                avg_volume = df['Volume'].iloc[index-10:index].mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # 判斷交叉類型
            is_golden_cross = short_ma > long_ma and prev_short_ma <= prev_long_ma
            is_death_cross = short_ma < long_ma and prev_short_ma >= prev_long_ma
            
            # 計算均線斜率
            ma_slope = (short_ma - prev_short_ma) / prev_short_ma if prev_short_ma > 0 else 0
            
            # 金叉信號
            if is_golden_cross:
                if volume_ratio >= self.volume_thresholds['extreme'] and abs(ma_slope) > 0.02:
                    return "極強", "強勢金叉", "#00FF00"
                elif volume_ratio >= self.volume_thresholds['high']:
                    return "強", "金叉放量", "#32CD32"
                elif abs(ma_slope) > 0.015:
                    return "強", "急速金叉", "#228B22"
                else:
                    return "中等", "金叉信號", "#90EE90"
            
            # 死叉信號
            elif is_death_cross:
                if volume_ratio >= self.volume_thresholds['extreme'] and abs(ma_slope) > 0.02:
                    return "極強", "強勢死叉", "#8B0000"
                elif volume_ratio >= self.volume_thresholds['high']:
                    return "強", "死叉放量", "#DC143C"
                elif abs(ma_slope) > 0.015:
                    return "強", "急速死叉", "#B22222"
                else:
                    return "中等", "死叉信號", "#F08080"
            
            # 多頭排列
            elif short_ma > long_ma and current_price > short_ma:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "強", "多頭強勢", "#32CD32"
                else:
                    return "中等", "多頭排列", "#90EE90"
            
            # 空頭排列
            elif short_ma < long_ma and current_price < short_ma:
                if volume_ratio >= self.volume_thresholds['high']:
                    return "強", "空頭強勢", "#DC143C"
                else:
                    return "中等", "空頭排列", "#F08080"
            
            return "弱", "均線糾結", "#D3D3D3"
            
        except Exception as e:
            return "中等", "均線信號", "#FFA500"
    
    def get_signal_strength_text(self, strategy_name: str, df: pd.DataFrame, index: int, signal_type: str) -> Tuple[str, str]:
        """
        獲取信號強度文字和顏色
        返回: (提示文字, 顏色)
        """
        try:
            if "RSI" in strategy_name.upper():
                strength, text, color = self.analyze_rsi_signal_strength(df, index)
            elif "MACD" in strategy_name.upper():
                strength, text, color = self.analyze_macd_signal_strength(df, index)
            elif "布林" in strategy_name or "BOLLINGER" in strategy_name.upper():
                strength, text, color = self.analyze_bollinger_signal_strength(df, index)
            elif "移動平均" in strategy_name or "均線" in strategy_name or "MA" in strategy_name.upper():
                strength, text, color = self.analyze_ma_cross_signal_strength(df, index)
            else:
                # 通用分析
                strength, text, color = self.analyze_general_signal_strength(df, index, signal_type)
            
            # 根據信號類型調整文字
            if signal_type.upper() in ['BUY', 'LONG', '買入']:
                prefix = "🟢 "
            elif signal_type.upper() in ['SELL', 'SHORT', '賣出']:
                prefix = "🔴 "
            else:
                prefix = "🟡 "
            
            return f"{prefix}{text}", color
            
        except Exception as e:
            return "🟡 信號", "#FFA500"
    
    def analyze_general_signal_strength(self, df: pd.DataFrame, index: int, signal_type: str) -> Tuple[str, str, str]:
        """
        通用信號強度分析
        """
        try:
            if index >= len(df) or index < 5:
                return "中等", "信號", "#FFA500"
            
            # 計算成交量比率
            volume_ratio = 1.0
            if 'Volume' in df.columns and index >= 10:
                current_volume = df['Volume'].iloc[index]
                avg_volume = df['Volume'].iloc[index-10:index].mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # 計算價格變化
            current_price = df['Close'].iloc[index]
            prev_price = df['Close'].iloc[index-1] if index > 0 else current_price
            price_change = (current_price - prev_price) / prev_price if prev_price > 0 else 0
            
            # 根據成交量和價格變化判斷強度
            if volume_ratio >= self.volume_thresholds['extreme']:
                if abs(price_change) > 0.05:
                    return "極強", "爆量突破", "#FF0000"
                else:
                    return "強", "爆量信號", "#FF4500"
            elif volume_ratio >= self.volume_thresholds['high']:
                if abs(price_change) > 0.03:
                    return "強", "放量突破", "#FF6600"
                else:
                    return "中等", "放量信號", "#FFA500"
            elif abs(price_change) > 0.05:
                return "中等", "價格突破", "#FFD700"
            else:
                return "弱", "信號微弱", "#D3D3D3"
                
        except Exception as e:
            return "中等", "信號", "#FFA500"

# 創建全局實例
signal_analyzer = SignalStrengthAnalyzer()
