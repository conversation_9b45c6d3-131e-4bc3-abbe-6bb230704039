#!/usr/bin/env python3
"""
測試優化版小資族資優生策略
"""
import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_sample_stock_data(stock_id, days=100):
    """創建樣本股票數據"""
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 生成模擬價格數據
    np.random.seed(hash(stock_id) % 2**32)  # 使用股票代碼作為種子
    
    base_price = np.random.uniform(20, 80)  # 基礎價格
    price_changes = np.random.normal(0, 0.02, days)  # 價格變化
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))  # 價格不能低於1
    
    # 生成其他數據
    volumes = np.random.uniform(100000, 1000000, days)  # 成交量
    highs = [p * np.random.uniform(1.0, 1.05) for p in prices]  # 最高價
    lows = [p * np.random.uniform(0.95, 1.0) for p in prices]   # 最低價
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': prices,
        'High': highs,
        'Low': lows,
        'Close': prices,
        'Volume': volumes
    })
    
    df.set_index('Date', inplace=True)
    return df

def test_single_stock_analysis():
    """測試單一股票分析"""
    print("🔍 測試單一股票分析...")
    print("=" * 50)
    
    try:
        from strategies.small_investor_strategy import SmallInvestorEliteStrategy
        
        # 創建策略實例
        strategy = SmallInvestorEliteStrategy()
        
        # 測試股票
        test_stocks = ['2330', '2317', '2454', '1301', '2881']
        
        for stock_id in test_stocks:
            print(f"\n📊 分析股票: {stock_id}")
            print("-" * 30)
            
            # 創建樣本數據
            df = create_sample_stock_data(stock_id)
            
            # 分析股票
            result = strategy.analyze_stock(df, stock_id=stock_id)
            
            # 顯示結果
            print(f"股票代碼: {stock_id}")
            print(f"是否適合: {'✅ 適合' if result['suitable'] else '❌ 不適合'}")
            print(f"綜合評分: {result['comprehensive_score']}/100")
            print(f"傳統評分: {result['score']}/7")
            print(f"數據品質: {result.get('data_quality', '未知')}")
            print(f"分析原因: {result['reason']}")
            
            # 顯示詳細評分
            details = result.get('details', {})
            if details:
                print("\n📋 詳細評分:")
                for condition, detail in details.items():
                    score = detail.get('score', 0)
                    reason = detail.get('reason', '')
                    print(f"  • {condition}: {score}分 - {reason}")
        
        print("\n✅ 單一股票分析測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 單一股票分析測試失敗: {e}")
        return False

def test_multiple_stocks_ranking():
    """測試多檔股票排名"""
    print("\n🏆 測試多檔股票排名...")
    print("=" * 50)
    
    try:
        from strategies.small_investor_strategy import SmallInvestorEliteStrategy
        
        # 創建策略實例
        strategy = SmallInvestorEliteStrategy()
        
        # 創建多檔股票數據
        test_stocks = ['2330', '2317', '2454', '1301', '2881', '2382', '2412', '6505', '2303', '3008']
        stock_data_dict = {}
        
        print(f"📊 準備 {len(test_stocks)} 檔股票數據...")
        for stock_id in test_stocks:
            stock_data_dict[stock_id] = create_sample_stock_data(stock_id)
        
        # 批量分析並排名
        print("🔍 開始批量分析...")
        ranking_results = strategy.analyze_multiple_stocks(stock_data_dict, top_n=10)
        
        # 生成排名報告
        print("📋 生成排名報告...")
        report = strategy.generate_ranking_report(ranking_results)
        print("\n" + report)
        
        print("\n✅ 多檔股票排名測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 多檔股票排名測試失敗: {e}")
        return False

def test_finmind_integration():
    """測試FinMind數據整合"""
    print("\n💰 測試FinMind數據整合...")
    print("=" * 50)
    
    try:
        from strategies.small_investor_strategy import SmallInvestorEliteStrategy
        
        # 創建策略實例
        strategy = SmallInvestorEliteStrategy()
        
        # 檢查FinMind提供器狀態
        if strategy.finmind_provider:
            print("✅ FinMind數據提供器已初始化")
            
            # 測試財務數據獲取
            test_stock = "2330"
            print(f"📊 測試股票: {test_stock}")
            
            financial_metrics = strategy.calculate_financial_metrics(test_stock)
            if financial_metrics:
                print("✅ 財務數據獲取成功:")
                for key, value in financial_metrics.items():
                    if key != 'is_real_data':
                        print(f"  • {key}: {value}")
            else:
                print("⚠️ 財務數據獲取失敗或無數據")
        else:
            print("⚠️ FinMind數據提供器未初始化，將使用代理指標")
        
        print("✅ FinMind數據整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ FinMind數據整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試優化版小資族資優生策略")
    print("=" * 60)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 測試結果
    test_results = []
    
    # 1. 測試單一股票分析
    test_results.append(("單一股票分析", test_single_stock_analysis()))
    
    # 2. 測試多檔股票排名
    test_results.append(("多檔股票排名", test_multiple_stocks_ranking()))
    
    # 3. 測試FinMind數據整合
    test_results.append(("FinMind數據整合", test_finmind_integration()))
    
    # 顯示測試摘要
    print("\n📊 測試摘要")
    print("=" * 40)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 測試結果: {passed}/{len(test_results)} 項通過")
    
    if passed == len(test_results):
        print("🎉 所有測試通過！優化版策略運作正常")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")

if __name__ == "__main__":
    main()
