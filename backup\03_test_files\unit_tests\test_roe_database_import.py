#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試ROE資料數據庫匯入功能
"""

import os
import pandas as pd
from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

def test_csv_import():
    """測試CSV匯入功能"""
    print("🧪 測試ROE資料數據庫匯入")
    print("=" * 50)
    
    # 找到最新的CSV文件
    csv_dir = "D:/Finlab/history/tables"
    csv_files = []
    
    if os.path.exists(csv_dir):
        for file in os.listdir(csv_dir):
            if 'roe_data' in file.lower() and file.endswith('.csv'):
                file_path = os.path.join(csv_dir, file)
                csv_files.append((file_path, os.path.getctime(file_path)))
    
    if not csv_files:
        print("❌ 沒有找到ROE CSV文件")
        return False
    
    # 選擇最新的文件
    latest_csv = max(csv_files, key=lambda x: x[1])[0]
    print(f"📁 使用CSV文件: {os.path.basename(latest_csv)}")
    
    # 檢查文件內容
    try:
        print("\n📊 檢查CSV文件內容:")
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        print(f"  • 資料筆數: {len(df)}")
        print(f"  • 欄位數量: {len(df.columns)}")
        print(f"  • 欄位名稱: {list(df.columns)}")
        
        if len(df) > 0:
            print(f"\n📋 前3筆原始資料:")
            for i, row in df.head(3).iterrows():
                print(f"  {i+1}. {row.iloc[1] if len(row) > 1 else 'N/A'} {row.iloc[2] if len(row) > 2 else 'N/A'}")
        
    except Exception as e:
        print(f"❌ 讀取CSV失敗: {e}")
        return False
    
    # 測試數據庫匯入
    try:
        print(f"\n💾 測試數據庫匯入:")
        downloader = GoodinfoROECSVDownloader()
        
        # 匯入數據庫
        success = downloader.import_csv_to_database(latest_csv)
        
        if success:
            print("✅ 數據庫匯入成功！")
            
            # 驗證數據庫內容
            import sqlite3
            db_path = downloader.db_path
            
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查表格結構
                cursor.execute("PRAGMA table_info(roe_data)")
                columns = cursor.fetchall()
                print(f"\n📊 數據庫表格結構:")
                for col in columns:
                    print(f"  • {col[1]} ({col[2]})")
                
                # 檢查資料筆數
                cursor.execute("SELECT COUNT(*) FROM roe_data")
                count = cursor.fetchone()[0]
                print(f"\n📈 數據庫資料筆數: {count}")
                
                # 顯示前5筆資料
                cursor.execute("""
                    SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year 
                    FROM roe_data 
                    ORDER BY rank_position 
                    LIMIT 5
                """)
                rows = cursor.fetchall()
                
                if rows:
                    print(f"\n📋 數據庫前5筆資料:")
                    for i, row in enumerate(rows, 1):
                        print(f"  {i}. {row[0]} {row[1]} - ROE: {row[2]}% (變化: {row[3]}) EPS: {row[4]} 年度: {row[5]}")
                
                conn.close()
                
            else:
                print(f"⚠️ 數據庫文件不存在: {db_path}")
            
            return True
        else:
            print("❌ 數據庫匯入失敗")
            return False
            
    except Exception as e:
        print(f"❌ 數據庫匯入測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_data_standardization():
    """測試資料標準化功能"""
    print("\n🔧 測試資料標準化功能")
    print("=" * 50)
    
    try:
        # 創建測試資料
        test_data = {
            '排 名': [1, 2, 3],
            'stock_code': ['1213', '5314', '2718'],
            'stock_name': ['大飲', '世紀*', '全心投控'],
            'ROE (%)': ['104', '84.6', '81.7'],
            'ROE 增減': ['+151', '+64.4', '+46.8'],
            'EPS (元)': ['8.71', '18.25', '35.33'],
            '財報 年度': [2024, 2024, 2024],
            'crawl_date': ['2025-07-15 23:00:34', '2025-07-15 23:00:34', '2025-07-15 23:00:34'],
            'data_source': ['goodinfo_scraped', 'goodinfo_scraped', 'goodinfo_scraped']
        }
        
        df = pd.DataFrame(test_data)
        print(f"📊 測試資料:")
        print(f"  • 原始欄位: {list(df.columns)}")
        print(f"  • 資料筆數: {len(df)}")
        
        # 測試標準化
        downloader = GoodinfoROECSVDownloader()
        df_cleaned = downloader.standardize_data(df)
        
        print(f"\n✅ 標準化結果:")
        print(f"  • 標準化欄位: {list(df_cleaned.columns)}")
        print(f"  • 資料筆數: {len(df_cleaned)}")
        
        if len(df_cleaned) > 0:
            print(f"\n📋 標準化後資料:")
            for i, row in df_cleaned.iterrows():
                print(f"  {i+1}. {row.get('stock_code', 'N/A')} {row.get('stock_name', 'N/A')} - ROE: {row.get('roe_value', 'N/A')}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料標準化測試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("🚀 ROE資料數據庫匯入測試")
    print("=" * 60)
    
    # 測試1: 資料標準化
    test1_result = test_data_standardization()
    
    # 測試2: CSV匯入
    test2_result = test_csv_import()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    print(f"  • 資料標準化測試: {'✅ 通過' if test1_result else '❌ 失敗'}")
    print(f"  • CSV匯入測試: {'✅ 通過' if test2_result else '❌ 失敗'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有測試通過！ROE資料數據庫匯入功能正常")
        print("\n💡 現在您可以:")
        print("  1. 使用ROE下載器下載最新資料")
        print("  2. 自動匯入到數據庫供分析使用")
        print("  3. 在除權息交易系統中整合ROE指標")
    else:
        print("\n⚠️ 部分測試失敗，請檢查錯誤信息")
    
    return test1_result and test2_result

if __name__ == "__main__":
    main()
