
# 模組兼容性層
import sys

# 確保 inspect 模組可用
try:
    import inspect
except ImportError:
    print("⚠️ inspect 模組不可用，創建替代實現")
    class MockInspect:
        @staticmethod
        def signature(func):
            # 簡單的簽名模擬
            class MockSignature:
                def __init__(self):
                    self.parameters = {}
            return MockSignature()
    
    sys.modules['inspect'] = MockInspect()

# 確保 importlib 模組可用
try:
    import importlib
    import importlib.util
except ImportError:
    print("⚠️ importlib 模組不可用，創建替代實現")
    class MockImportlib:
        @staticmethod
        def import_module(name):
            return __import__(name)
        
        class util:
            @staticmethod
            def spec_from_file_location(name, location):
                return None
            
            @staticmethod
            def module_from_spec(spec):
                return None
    
    sys.modules['importlib'] = MockImportlib()
    sys.modules['importlib.util'] = MockImportlib.util()

# 確保 pydoc 模組可用
try:
    import pydoc
except ImportError:
    print("⚠️ pydoc 模組不可用，創建替代實現")
    class MockPydoc:
        @staticmethod
        def help(obj):
            return f"Help for {obj}"

        @staticmethod
        def doc(obj):
            return getattr(obj, '__doc__', 'No documentation available')

    sys.modules['pydoc'] = MockPydoc()

# 確保 doctest 模組可用
try:
    import doctest
except ImportError:
    print("⚠️ doctest 模組不可用，創建替代實現")
    class MockDoctest:
        @staticmethod
        def testmod(*args, **kwargs):
            return None

    sys.modules['doctest'] = MockDoctest()

# 確保 difflib 模組可用
try:
    import difflib
except ImportError:
    print("⚠️ difflib 模組不可用，創建替代實現")
    class MockDifflib:
        @staticmethod
        def unified_diff(*args, **kwargs):
            return []

    sys.modules['difflib'] = MockDifflib()

print("✅ 模組兼容性層創建完成")
