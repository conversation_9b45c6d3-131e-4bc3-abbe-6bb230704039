#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
賺錢機會排名演示
展示基於賺錢機會的智能排名算法
"""

import re

def demo_profit_ranking():
    """展示賺錢機會排名"""
    print("💰 賺錢機會排名算法演示")
    print("=" * 50)
    print("🎯 核心目標: 排名越高 = 買進後賺錢機會越大")
    print()
    
    # 模擬符合條件的股票
    qualified_stocks = [
        {
            "股票代碼": "2330",
            "股票名稱": "台積電", 
            "收盤價": 580.0,
            "條件結果": [
                {"matched": True, "message": "MA240 582.5 > 昨日 580.2 強勢上升"},
                {"matched": True, "message": "MA240未來趨勢 明顯上升"},
                {"matched": True, "message": "MA20 585.3 > 5日前 578.1 持續上升"},
                {"matched": True, "message": "成交量 大幅放大 1.8倍"},
                {"matched": True, "message": "RSI 65.2 強勢區間"},
                {"matched": True, "message": "成交額 158億 > 100億"}
            ]
        },
        {
            "股票代碼": "2454",
            "股票名稱": "聯發科",
            "收盤價": 1200.0,
            "條件結果": [
                {"matched": True, "message": "MA240 1205.3 > 昨日 1202.1 上升"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 1210.5 > 5日前 1195.2 明顯上升"},
                {"matched": True, "message": "成交量 明顯放大 2.1倍"},
                {"matched": True, "message": "RSI 72.9 偏高"},
                {"matched": True, "message": "成交額 180億 > 100億"}
            ]
        },
        {
            "股票代碼": "2317",
            "股票名稱": "鴻海",
            "收盤價": 110.5,
            "條件結果": [
                {"matched": True, "message": "MA240 111.2 > 昨日 110.8 上升"},
                {"matched": True, "message": "MA240未來趨勢 持續上升"},
                {"matched": True, "message": "MA20 112.1 > 5日前 109.5 強勢上升"},
                {"matched": True, "message": "成交量 激增 1.4倍"},
                {"matched": True, "message": "RSI 58.7 適中"},
                {"matched": True, "message": "成交額 125億 > 100億"}
            ]
        },
        {
            "股票代碼": "1301",
            "股票名稱": "台塑",
            "收盤價": 85.3,
            "條件結果": [
                {"matched": True, "message": "MA240 85.8 > 昨日 85.6 上升"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 86.2 > 5日前 84.1 上升"},
                {"matched": True, "message": "成交量 增加 1.2倍"},
                {"matched": True, "message": "RSI 55.3 適中"},
                {"matched": True, "message": "成交額 105億 > 100億"}
            ]
        },
        {
            "股票代碼": "2412",
            "股票名稱": "中華電",
            "收盤價": 125.6,
            "條件結果": [
                {"matched": True, "message": "MA240 126.1 > 昨日 125.9 上升"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 126.8 > 5日前 124.2 上升"},
                {"matched": True, "message": "成交量 放大 1.3倍"},
                {"matched": True, "message": "RSI 52.1 適中"},
                {"matched": True, "message": "成交額 112億 > 100億"}
            ]
        }
    ]
    
    print("📊 原始股票列表 (未排名):")
    print("-" * 40)
    for i, stock in enumerate(qualified_stocks, 1):
        print(f"{i}. {stock['股票代碼']} {stock['股票名稱']} - ${stock['收盤價']}")
    
    # 計算賺錢機會排名
    ranked_stocks = calculate_profit_ranking(qualified_stocks)
    
    print(f"\n🏆 賺錢機會排名結果:")
    print("-" * 60)
    print(f"{'排名':<4} {'股票':<12} {'價格':<8} {'總分':<6} {'賺錢機會':<8} {'主要優勢'}")
    print("-" * 60)
    
    for i, stock in enumerate(ranked_stocks, 1):
        score = stock.get('profit_score', 0)
        opportunity = get_profit_opportunity_level(score)
        advantages = get_main_advantages(stock)
        
        print(f"#{i:<3} {stock['股票代碼']} {stock['股票名稱']:<6} ${stock['收盤價']:<7.0f} {score:<6.0f} {opportunity:<8} {advantages}")
    
    return ranked_stocks

def calculate_profit_ranking(qualified_stocks):
    """計算賺錢機會排名 (模擬主程式邏輯)"""
    for stock in qualified_stocks:
        profit_score = 0
        stock_id = stock["股票代碼"]
        price = stock.get("收盤價", 0)
        
        # 1. 趨勢強度分析 (35分)
        trend_score = 0
        ma240_strength = 0
        ma20_strength = 0
        
        for condition_result in stock["條件結果"]:
            message = condition_result["message"]
            
            # 年線趨勢強度
            if "MA240" in message:
                if "強勢" in message or "大幅" in message:
                    ma240_strength = 15
                elif "明顯" in message or "持續" in message:
                    ma240_strength = 12
                elif "上升" in message:
                    ma240_strength = 8
                else:
                    ma240_strength = 5
            
            # 月線趨勢強度
            if "MA20" in message:
                if "強勢" in message or "大幅" in message:
                    ma20_strength = 12
                elif "明顯" in message or "持續" in message:
                    ma20_strength = 10
                elif "上升" in message:
                    ma20_strength = 7
                else:
                    ma20_strength = 4
        
        trend_score = ma240_strength + ma20_strength
        profit_score += min(trend_score, 35)
        
        # 2. 動量指標強度 (25分)
        momentum_score = 0
        
        for condition_result in stock["條件結果"]:
            message = condition_result["message"]
            
            # RSI動量分析
            if "RSI" in message:
                rsi_match = re.search(r'(\d+\.?\d*)', message)
                if rsi_match:
                    rsi_value = float(rsi_match.group(1))
                    if 55 <= rsi_value <= 70:
                        momentum_score += 15  # 最佳買進區間
                    elif 50 <= rsi_value < 55:
                        momentum_score += 12
                    elif 70 < rsi_value <= 75:
                        momentum_score += 8
                    else:
                        momentum_score += 5
                else:
                    momentum_score += 10
            
            # 成交量動能
            if "成交" in message and "量" in message:
                if "大幅" in message or "激增" in message:
                    momentum_score += 10
                elif "明顯" in message or "放大" in message:
                    momentum_score += 7
                elif "增加" in message:
                    momentum_score += 5
                else:
                    momentum_score += 3
        
        profit_score += min(momentum_score, 25)
        
        # 3. 價格位置優勢 (20分)
        if 30 <= price <= 150:
            price_score = 20
        elif 150 < price <= 300:
            price_score = 15
        elif 15 <= price < 30:
            price_score = 12
        elif 300 < price <= 600:
            price_score = 10
        else:
            price_score = 5
        
        profit_score += price_score
        
        # 4. 歷史表現偏好 (15分)
        if stock_id in ["2330", "2317", "2382", "2454"]:
            historical_score = 15  # 龍頭股
        elif stock_id.startswith("23"):
            historical_score = 12  # 電子股
        elif stock_id.startswith("24"):
            historical_score = 13  # 半導體
        elif stock_id.startswith("28"):
            historical_score = 10  # 化學
        elif stock_id.startswith("13"):
            historical_score = 8   # 塑化
        else:
            historical_score = 5
        
        profit_score += historical_score
        
        # 5. 市場流動性 (5分)
        liquidity_score = 3  # 預設分數
        for condition_result in stock["條件結果"]:
            message = condition_result["message"]
            if "成交額" in message:
                amount_match = re.search(r'(\d+)億', message)
                if amount_match:
                    amount = int(amount_match.group(1))
                    if amount >= 50:
                        liquidity_score = 5
                    elif amount >= 20:
                        liquidity_score = 4
                    elif amount >= 10:
                        liquidity_score = 3
                    else:
                        liquidity_score = 2
        
        profit_score += liquidity_score
        
        stock["profit_score"] = min(profit_score, 100)
        
        # 詳細評分記錄
        stock["score_breakdown"] = {
            "趨勢": trend_score,
            "動量": min(momentum_score, 25),
            "價格": price_score,
            "歷史": historical_score,
            "流動性": liquidity_score
        }
    
    # 按賺錢機會評分排序
    qualified_stocks.sort(key=lambda x: x.get("profit_score", 0), reverse=True)
    return qualified_stocks

def get_profit_opportunity_level(score):
    """獲取賺錢機會等級"""
    if score >= 85:
        return "極高🔥"
    elif score >= 70:
        return "高📈"
    elif score >= 60:
        return "中高⚡"
    elif score >= 50:
        return "中等📊"
    else:
        return "較低⚠️"

def get_main_advantages(stock):
    """獲取主要優勢"""
    advantages = []
    breakdown = stock.get("score_breakdown", {})
    
    if breakdown.get("趨勢", 0) >= 25:
        advantages.append("強勢趨勢")
    if breakdown.get("動量", 0) >= 20:
        advantages.append("動量強勁")
    if breakdown.get("價格", 0) >= 18:
        advantages.append("價格合理")
    if breakdown.get("歷史", 0) >= 12:
        advantages.append("龍頭股")
    if breakdown.get("流動性", 0) >= 4:
        advantages.append("高流動性")
    
    return " | ".join(advantages) if advantages else "基本面良好"

def show_detailed_analysis(ranked_stocks):
    """顯示詳細分析"""
    print(f"\n📊 詳細評分分析:")
    print("-" * 70)
    print(f"{'股票':<8} {'趨勢':<6} {'動量':<6} {'價格':<6} {'歷史':<6} {'流動性':<6} {'總分':<6} {'建議'}")
    print("-" * 70)
    
    for stock in ranked_stocks:
        breakdown = stock.get("score_breakdown", {})
        total = stock.get("profit_score", 0)
        
        if total >= 80:
            suggestion = "強烈買入"
        elif total >= 70:
            suggestion = "建議買入"
        elif total >= 60:
            suggestion = "可考慮"
        else:
            suggestion = "謹慎觀望"
        
        print(f"{stock['股票代碼']:<8} "
              f"{breakdown.get('趨勢', 0):<6.0f} "
              f"{breakdown.get('動量', 0):<6.0f} "
              f"{breakdown.get('價格', 0):<6.0f} "
              f"{breakdown.get('歷史', 0):<6.0f} "
              f"{breakdown.get('流動性', 0):<6.0f} "
              f"{total:<6.0f} "
              f"{suggestion}")

def show_investment_strategy():
    """顯示投資策略建議"""
    print(f"\n💡 投資策略建議:")
    print("-" * 25)
    
    print("🎯 資金配置:")
    print("  • 排名 #1-2: 投入50%資金 (最高賺錢機會)")
    print("  • 排名 #3-5: 投入30%資金 (分散風險)")
    print("  • 現金保留: 保留20%資金 (應急備用)")
    print()
    
    print("📈 進場時機:")
    print("  • 85分以上: 立即進場，不要猶豫")
    print("  • 70-84分: 等待小幅回檔進場")
    print("  • 60-69分: 分批進場，控制風險")
    print("  • 60分以下: 觀望為主，等待更好機會")
    print()
    
    print("🛡️ 風險控制:")
    print("  • 停損點: 買入價 -5%")
    print("  • 停利點: 買入價 +15%")
    print("  • 持股時間: 建議1-3個月")
    print("  • 最大持股: 不超過5支股票")

def main():
    """主函數"""
    print("🚀 賺錢機會排名完整演示")
    print("=" * 40)
    
    # 展示排名功能
    ranked_stocks = demo_profit_ranking()
    
    # 詳細分析
    show_detailed_analysis(ranked_stocks)
    
    # 投資策略
    show_investment_strategy()
    
    # 總結
    print(f"\n🎯 核心邏輯總結:")
    print("=" * 20)
    print("💰 趨勢強度 (35%) - 決定長期賺錢能力")
    print("⚡ 動量指標 (25%) - 決定短期爆發力")
    print("📊 價格位置 (20%) - 決定上漲空間")
    print("🏆 歷史表現 (15%) - 決定穩定性")
    print("💧 市場流動性 (5%) - 決定進出便利性")
    print()
    print("🎉 現在排名完全基於賺錢機會！")
    print("✨ 排名越高 = 買進後賺錢機會越大！")

if __name__ == "__main__":
    main()
