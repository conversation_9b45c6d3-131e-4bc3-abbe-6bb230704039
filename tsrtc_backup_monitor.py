#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TSRTC 備用即時股價監控系統
整合TSRTC作為主系統的備用數據源
"""

import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
import urllib3
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
urllib3.disable_warnings(InsecureRequestWarning)

class TSRTCBackupMonitor:
    """TSRTC備用監控器"""
    
    def __init__(self):
        self.base_url = 'https://mis.twse.com.tw'
        self.api_endpoint = f'{self.base_url}/stock/api/getStockInfo.jsp'
        self.index_page = f'{self.base_url}/stock/index'

        # 創建session
        self.session = requests.Session()
        self.session.verify = False

        # 設置headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': self.base_url
        })

        # 監控狀態
        self.is_monitoring = False
        self.last_update_time = None
        self.error_count = 0
        self.max_errors = 5

        # 添加模擬數據模式（用於非交易時間或API故障）
        self.demo_mode = False
        self.demo_data_cache = {}

        # 數據源優先順序設定（預設Yahoo優先）
        self.data_source_priority = "Yahoo"  # "Yahoo" 或 "TSRTC"

        logging.info("✅ TSRTC備用監控器初始化完成")
        
        logging.info("✅ TSRTC備用監控器初始化完成")
    
    def test_connection(self) -> bool:
        """測試連接狀態"""
        try:
            # 先訪問基本頁面建立session
            response = self.session.get(self.index_page, timeout=10)
            if response.status_code == 200:
                logging.info("✅ TSRTC連接測試成功")
                return True
            else:
                logging.error(f"❌ TSRTC連接測試失敗，狀態碼: {response.status_code}")
                return False
        except Exception as e:
            logging.error(f"❌ TSRTC連接測試異常: {e}")
            return False
    
    def get_single_stock_realtime(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        獲取單一股票即時資料
        
        Args:
            stock_code: 股票代碼
            
        Returns:
            dict: 即時股價資料，格式與主系統相容
        """
        try:
            # 建立session（減少超時時間）
            self.session.get(self.index_page, timeout=8)

            # 構建查詢URL
            timestamp = int(time.time() * 1000)
            channel = f'tse_{stock_code}.tw'
            query_url = f'{self.api_endpoint}?_={timestamp}&ex_ch={channel}'

            response = self.session.get(query_url, timeout=8)  # 減少超時時間
            
            if response.status_code == 200:
                content = json.loads(response.text)
                
                if content.get('rtcode') == '0000':
                    msg_array = content.get('msgArray', [])
                    
                    if msg_array:
                        raw_data = msg_array[0]
                        
                        # 轉換為主系統相容格式
                        formatted_data = self._format_stock_data(raw_data)
                        
                        self.error_count = 0  # 重置錯誤計數
                        self.last_update_time = datetime.now()
                        
                        return formatted_data
                    else:
                        logging.warning(f"⚠️ TSRTC沒有獲取到 {stock_code} 的數據")
                else:
                    logging.error(f"❌ TSRTC API錯誤: {content.get('rtmessage', 'Unknown error')}")
            else:
                logging.error(f"❌ TSRTC HTTP錯誤，狀態碼: {response.status_code}")
                
        except Exception as e:
            self.error_count += 1
            logging.error(f"❌ TSRTC獲取 {stock_code} 數據失敗: {e}")
            
        return None
    
    def get_multiple_stocks_realtime(self, stock_codes: List[str]) -> Dict[str, Any]:
        """
        獲取多支股票即時資料
        
        Args:
            stock_codes: 股票代碼列表
            
        Returns:
            dict: 多支股票的即時資料
        """
        results = {}
        
        try:
            # 根據優先順序決定主要數據源
            if self.data_source_priority == "Yahoo":
                # Yahoo優先：先嘗試Yahoo Finance
                logging.info("🌐 Yahoo優先模式，首先嘗試Yahoo Finance")
                yahoo_results = self._try_yahoo_finance(stock_codes)
                if yahoo_results and len(yahoo_results) >= len(stock_codes) * 0.7:  # 成功率≥70%
                    logging.info(f"✅ Yahoo Finance成功獲取 {len(yahoo_results)}/{len(stock_codes)} 支股票")
                    return yahoo_results
                else:
                    logging.info("⚠️ Yahoo Finance成功率不足，嘗試TSRTC補充")
                    results.update(yahoo_results)  # 保留Yahoo的結果

            # 建立TSRTC session（減少超時時間）
            self.session.get(self.index_page, timeout=8)

            # 優化批量查詢：減少批量大小，增加重試機制
            batch_size = 8  # 減少批量大小以提高成功率
            max_retries = 2  # 添加重試機制

            for i in range(0, len(stock_codes), batch_size):
                batch = stock_codes[i:i + batch_size]

                # 重試機制
                for retry in range(max_retries):
                    try:
                        # 構建查詢URL
                        timestamp = int(time.time() * 1000)
                        channels = '|'.join(f'tse_{code}.tw' for code in batch)
                        query_url = f'{self.api_endpoint}?_={timestamp}&ex_ch={channels}'

                        response = self.session.get(query_url, timeout=6)  # 縮短超時時間

                        if response.status_code == 200:
                            content = json.loads(response.text)

                            if content.get('rtcode') == '0000':
                                msg_array = content.get('msgArray', [])

                                for raw_data in msg_array:
                                    stock_code = raw_data.get('c', '')
                                    if stock_code:
                                        formatted_data = self._format_stock_data(raw_data)
                                        # 驗證數據有效性
                                        if formatted_data.get('current_price', 0) > 0:
                                            results[stock_code] = formatted_data

                                # 成功則跳出重試循環
                                break
                            else:
                                if retry == max_retries - 1:  # 最後一次重試
                                    logging.warning(f"⚠️ TSRTC批量查詢錯誤: {content.get('rtmessage', 'Unknown error')}")
                        else:
                            if retry == max_retries - 1:  # 最後一次重試
                                logging.warning(f"⚠️ TSRTC批量查詢HTTP錯誤，狀態碼: {response.status_code}")

                    except Exception as e:
                        if retry == max_retries - 1:  # 最後一次重試
                            logging.warning(f"⚠️ TSRTC批量查詢異常: {e}")
                        else:
                            time.sleep(0.1)  # 重試前短暫延遲

                # 批量間延遲優化
                if i + batch_size < len(stock_codes):
                    time.sleep(0.1)  # 進一步減少延遲
            
            # 對於沒有獲取到數據的股票，嘗試單獨重試
            failed_stocks = [code for code in stock_codes if code not in results]
            if failed_stocks and len(failed_stocks) <= 3:  # 只對少量失敗股票重試
                logging.info(f"🔄 對 {len(failed_stocks)} 支失敗股票進行單獨重試: {failed_stocks}")

                for stock_code in failed_stocks:
                    try:
                        single_result = self.get_single_stock_realtime(stock_code)
                        if single_result and single_result.get('current_price', 0) > 0:
                            results[stock_code] = single_result
                            logging.info(f"✅ 單獨重試成功: {stock_code}")
                        time.sleep(0.1)  # 單獨查詢間延遲
                    except Exception as e:
                        logging.warning(f"⚠️ 單獨重試失敗 {stock_code}: {e}")

            self.error_count = 0
            self.last_update_time = datetime.now()

        except Exception as e:
            self.error_count += 1
            logging.error(f"❌ TSRTC批量獲取數據失敗: {e}")

        # 根據優先順序和結果決定是否需要嘗試其他數據源
        if not results or len(results) < len(stock_codes) * 0.5:  # 成功率低於50%
            current_time = datetime.now()

            if not self.demo_mode:
                # 檢查是否在交易時間外
                if (current_time.weekday() >= 5 or  # 週末
                    current_time.hour < 9 or current_time.hour >= 14):  # 非交易時間
                    logging.info("⚠️ 非交易時間，啟用模擬數據模式")
                    self.demo_mode = True
                else:
                    # 交易時間內，根據優先順序嘗試備用方案
                    if self.data_source_priority == "Yahoo":
                        # Yahoo優先，如果TSRTC失敗或成功率低，嘗試Yahoo
                        logging.info("🌐 Yahoo優先模式，嘗試Yahoo Finance API")
                        yahoo_results = self._try_yahoo_finance(stock_codes)
                        if yahoo_results:
                            # 合併結果，Yahoo優先
                            combined_results = yahoo_results.copy()
                            for code, data in results.items():
                                if code not in combined_results and data:
                                    combined_results[code] = data
                            logging.info(f"✅ 使用Yahoo Finance數據源，獲取 {len(yahoo_results)} 支")
                            return combined_results
                    else:
                        # TSRTC優先，如果失敗才嘗試Yahoo
                        if not results:
                            logging.warning("⚠️ TSRTC API不可用，嘗試Yahoo Finance備用方案")
                            yahoo_results = self._try_yahoo_finance(stock_codes)
                            if yahoo_results:
                                logging.info("✅ 使用Yahoo Finance備用數據源")
                                return yahoo_results

                    # 所有API都失敗，啟用模擬模式
                    logging.warning("⚠️ 所有API都不可用，啟用模擬數據模式")
                    self.demo_mode = True

            # 如果沒有任何結果，返回模擬數據
            if not results:
                return self._generate_demo_data(stock_codes)

        return results

    def _safe_float(self, value) -> float:
        """
        安全轉換為浮點數，處理 '-' 和空值

        Args:
            value: 要轉換的值

        Returns:
            float: 轉換後的浮點數，失敗時返回0.0
        """
        try:
            if value is None or value == '' or value == '-':
                return 0.0
            return float(str(value).replace(',', ''))
        except (ValueError, TypeError):
            return 0.0

    def _safe_int(self, value) -> int:
        """
        安全轉換為整數，處理 '-' 和空值

        Args:
            value: 要轉換的值

        Returns:
            int: 轉換後的整數，失敗時返回0
        """
        try:
            if value is None or value == '' or value == '-':
                return 0
            return int(str(value).replace(',', ''))
        except (ValueError, TypeError):
            return 0

    def _format_stock_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        將TSRTC原始數據轉換為主系統相容格式

        Args:
            raw_data: TSRTC原始數據

        Returns:
            dict: 格式化後的數據
        """
        try:
            # 基本資訊
            stock_code = raw_data.get('c', '')
            stock_name = raw_data.get('n', '')

            # 價格資訊 - 安全轉換，處理 '-' 字符串
            current_price = self._safe_float(raw_data.get('z', 0))
            prev_close = self._safe_float(raw_data.get('y', 0))
            open_price = self._safe_float(raw_data.get('o', 0))
            high_price = self._safe_float(raw_data.get('h', 0))
            low_price = self._safe_float(raw_data.get('l', 0))
            
            # 計算漲跌
            change_amount = current_price - prev_close if current_price and prev_close else 0
            change_percent = (change_amount / prev_close * 100) if prev_close else 0

            # 成交量 - 安全轉換
            volume = self._safe_int(raw_data.get('v', '0'))
            current_volume = self._safe_int(raw_data.get('tv', '0'))
            
            # 五檔資訊 - 安全處理
            ask_prices = []
            ask_volumes = []
            bid_prices = []
            bid_volumes = []

            # 處理賣出五檔
            if raw_data.get('a'):
                ask_prices = [self._safe_float(p) for p in raw_data.get('a', '').split('_') if p]
            if raw_data.get('f'):
                ask_volumes = [self._safe_int(v) for v in raw_data.get('f', '').split('_') if v]

            # 處理買入五檔
            if raw_data.get('b'):
                bid_prices = [self._safe_float(p) for p in raw_data.get('b', '').split('_') if p]
            if raw_data.get('g'):
                bid_volumes = [self._safe_int(v) for v in raw_data.get('g', '').split('_') if v]
            
            # 時間資訊
            data_time = raw_data.get('t', '')
            data_date = raw_data.get('d', '')
            
            # 格式化為主系統相容格式
            formatted_data = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'current_price': current_price,
                'prev_close': prev_close,
                'open_price': open_price,
                'high_price': high_price,
                'low_price': low_price,
                'change_amount': change_amount,
                'change_percent': change_percent,
                'volume': volume,
                'current_volume': current_volume,
                'ask_prices': ask_prices[:5],  # 只取前5檔
                'ask_volumes': ask_volumes[:5],
                'bid_prices': bid_prices[:5],
                'bid_volumes': bid_volumes[:5],
                'data_time': data_time,
                'data_date': data_date,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'source': 'TSRTC',
                'raw_data': raw_data  # 保留原始數據供調試
            }
            
            return formatted_data
            
        except Exception as e:
            logging.error(f"❌ TSRTC數據格式化失敗: {e}")
            return {}
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        獲取監控器健康狀態
        
        Returns:
            dict: 健康狀態資訊
        """
        status = {
            'is_healthy': self.error_count < self.max_errors,
            'error_count': self.error_count,
            'max_errors': self.max_errors,
            'last_update_time': self.last_update_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_update_time else None,
            'connection_status': 'OK' if self.error_count == 0 else 'WARNING' if self.error_count < self.max_errors else 'ERROR',
            'source_name': 'TSRTC (Taiwan Stock Exchange Real Time Crawler)',
            'api_endpoint': self.api_endpoint
        }
        
        return status

    def _generate_demo_data(self, stock_codes: List[str]) -> Dict[str, Any]:
        """生成模擬數據（用於非交易時間或API故障時）"""
        import random

        results = {}

        # 股票名稱對照
        stock_names = {
            "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "3008": "大立光",
            "2412": "中華電", "6290": "良維", "6763": "綠界科技", "8499": "鼎炫-KY",
            "6667": "信紘科", "1815": "富喬"
        }

        # 基準價格和波動率（更真實的模擬）
        stock_info = {
            "2330": {"price": 580.0, "volatility": 0.02, "volume_base": 30000},
            "2317": {"price": 163.5, "volatility": 0.025, "volume_base": 25000},
            "2454": {"price": 1410.0, "volatility": 0.03, "volume_base": 15000},
            "3008": {"price": 2800.0, "volatility": 0.025, "volume_base": 8000},
            "2412": {"price": 130.0, "volatility": 0.015, "volume_base": 20000},
            "6290": {"price": 85.0, "volatility": 0.04, "volume_base": 5000},
            "6763": {"price": 420.0, "volatility": 0.035, "volume_base": 8000},
            "8499": {"price": 95.0, "volatility": 0.045, "volume_base": 3000},
            "6667": {"price": 75.0, "volatility": 0.04, "volume_base": 4000},
            "1815": {"price": 45.0, "volatility": 0.05, "volume_base": 2000}
        }

        for stock_code in stock_codes:
            # 獲取股票信息
            info = stock_info.get(stock_code, {"price": 100.0, "volatility": 0.03, "volume_base": 5000})
            base_price = info["price"]
            volatility = info["volatility"]
            volume_base = info["volume_base"]

            # 使用正態分佈生成更真實的價格變動
            change_percent = random.gauss(0, volatility * 100)  # 正態分佈
            change_percent = max(-9.5, min(9.5, change_percent))  # 限制在漲跌停範圍內

            change_amount = base_price * (change_percent / 100)
            current_price = base_price + change_amount

            # 生成更真實的成交量（基於基準量的變動）
            volume_multiplier = random.uniform(0.5, 2.0)
            volume = int(volume_base * volume_multiplier)

            # 創建模擬數據
            demo_data = {
                'stock_code': stock_code,
                'stock_name': stock_names.get(stock_code, f'股票{stock_code}'),
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'open_price': round(base_price + random.uniform(-1, 1), 2),
                'high_price': round(current_price + random.uniform(0, 2), 2),
                'low_price': round(current_price - random.uniform(0, 2), 2),
                'change_amount': round(change_amount, 2),
                'change_percent': round(change_percent, 2),
                'volume': volume,
                'current_volume': volume,
                'ask_prices': [],
                'ask_volumes': [],
                'bid_prices': [],
                'bid_volumes': [],
                'data_time': datetime.now().strftime('%H:%M:%S'),
                'data_date': datetime.now().strftime('%Y%m%d'),
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'source': 'TSRTC-DEMO',
                'raw_data': {'demo': True}
            }

            results[stock_code] = demo_data

        logging.info(f"📊 生成了 {len(results)} 支股票的模擬數據")
        return results

    def _try_yahoo_finance(self, stock_codes: List[str]) -> Dict[str, Any]:
        """嘗試使用Yahoo Finance API獲取數據"""
        results = {}

        try:
            import requests
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            for stock_code in stock_codes:
                try:
                    # Yahoo Finance API URL
                    yahoo_symbol = f"{stock_code}.TW"
                    yahoo_url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}"

                    response = session.get(yahoo_url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()

                        if 'chart' in data and data['chart']['result']:
                            chart_data = data['chart']['result'][0]
                            meta = chart_data.get('meta', {})

                            current_price = meta.get('regularMarketPrice', 0)
                            prev_close = meta.get('previousClose', 0)

                            if current_price > 0:
                                change_amount = current_price - prev_close
                                change_percent = (change_amount / prev_close * 100) if prev_close else 0

                                # 股票名稱對照
                                stock_names = {
                                    "2330": "台積電", "2317": "鴻海", "2454": "聯發科",
                                    "3008": "大立光", "2412": "中華電"
                                }

                                yahoo_data = {
                                    'stock_code': stock_code,
                                    'stock_name': stock_names.get(stock_code, f'股票{stock_code}'),
                                    'current_price': round(current_price, 2),
                                    'prev_close': prev_close,
                                    'open_price': meta.get('regularMarketOpen', current_price),
                                    'high_price': meta.get('regularMarketDayHigh', current_price),
                                    'low_price': meta.get('regularMarketDayLow', current_price),
                                    'change_amount': round(change_amount, 2),
                                    'change_percent': round(change_percent, 2),
                                    'volume': meta.get('regularMarketVolume', 0),
                                    'current_volume': meta.get('regularMarketVolume', 0),
                                    'ask_prices': [],
                                    'ask_volumes': [],
                                    'bid_prices': [],
                                    'bid_volumes': [],
                                    'data_time': datetime.now().strftime('%H:%M:%S'),
                                    'data_date': datetime.now().strftime('%Y%m%d'),
                                    'timestamp': datetime.now().strftime('%H:%M:%S'),
                                    'source': 'Yahoo-Finance',
                                    'raw_data': meta
                                }

                                results[stock_code] = yahoo_data

                except Exception as e:
                    logging.debug(f"Yahoo Finance獲取 {stock_code} 失敗: {e}")
                    continue

            if results:
                logging.info(f"✅ Yahoo Finance成功獲取 {len(results)} 支股票數據")

        except Exception as e:
            logging.warning(f"⚠️ Yahoo Finance API失敗: {e}")

        return results

    def set_data_source_priority(self, priority: str):
        """設定數據源優先順序
        Args:
            priority: "Yahoo" 或 "TSRTC"
        """
        if priority in ["Yahoo", "TSRTC"]:
            self.data_source_priority = priority
            logging.info(f"✅ 數據源優先順序已設定為: {priority}")
        else:
            logging.warning(f"⚠️ 無效的數據源優先順序: {priority}")

    def get_data_source_priority(self) -> str:
        """獲取當前數據源優先順序"""
        return self.data_source_priority

    def reset_error_count(self):
        """重置錯誤計數"""
        self.error_count = 0
        logging.info("✅ TSRTC錯誤計數已重置")

class TSRTCIntegration:
    """TSRTC整合類，用於與主系統整合"""
    
    def __init__(self):
        self.tsrtc_monitor = TSRTCBackupMonitor()
        self.is_enabled = True
        
    def get_realtime_quote(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        獲取即時報價（主系統相容介面）
        
        Args:
            stock_code: 股票代碼
            
        Returns:
            dict: 即時報價數據
        """
        if not self.is_enabled:
            return None
            
        return self.tsrtc_monitor.get_single_stock_realtime(stock_code)
    
    def get_multiple_quotes(self, stock_codes: List[str]) -> Dict[str, Any]:
        """
        獲取多支股票報價（主系統相容介面）
        
        Args:
            stock_codes: 股票代碼列表
            
        Returns:
            dict: 多支股票報價數據
        """
        if not self.is_enabled:
            return {}
            
        return self.tsrtc_monitor.get_multiple_stocks_realtime(stock_codes)
    
    def test_availability(self) -> bool:
        """測試可用性"""
        return self.tsrtc_monitor.test_connection()
    
    def get_status(self) -> Dict[str, Any]:
        """獲取狀態"""
        status = self.tsrtc_monitor.get_health_status()
        status['is_enabled'] = self.is_enabled
        return status
    
    def enable(self):
        """啟用TSRTC"""
        self.is_enabled = True
        logging.info("✅ TSRTC備用監控已啟用")
    
    def disable(self):
        """禁用TSRTC"""
        self.is_enabled = False
        logging.info("⏹️ TSRTC備用監控已禁用")

# 創建全局實例
tsrtc_integration = TSRTCIntegration()

# 便捷函數
def get_tsrtc_quote(stock_code: str) -> Optional[Dict[str, Any]]:
    """便捷函數：獲取TSRTC即時報價"""
    return tsrtc_integration.get_realtime_quote(stock_code)

def get_tsrtc_multiple_quotes(stock_codes: List[str]) -> Dict[str, Any]:
    """便捷函數：獲取TSRTC多支股票報價"""
    return tsrtc_integration.get_multiple_quotes(stock_codes)

def test_tsrtc_availability() -> bool:
    """便捷函數：測試TSRTC可用性"""
    return tsrtc_integration.test_availability()

def get_tsrtc_status() -> Dict[str, Any]:
    """便捷函數：獲取TSRTC狀態"""
    return tsrtc_integration.get_status()

if __name__ == "__main__":
    # 測試程式
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🧪 TSRTC備用監控系統測試")
    print("=" * 50)
    
    # 測試連接
    if test_tsrtc_availability():
        print("✅ TSRTC連接正常")
        
        # 測試單一股票
        quote = get_tsrtc_quote('2330')
        if quote:
            print(f"✅ 台積電即時報價: {quote['current_price']}")
        
        # 測試多支股票
        quotes = get_tsrtc_multiple_quotes(['2330', '2317', '2454'])
        if quotes:
            print(f"✅ 獲取到 {len(quotes)} 支股票報價")
        
        # 顯示狀態
        status = get_tsrtc_status()
        print(f"📊 TSRTC狀態: {status['connection_status']}")
        
    else:
        print("❌ TSRTC連接失敗")
