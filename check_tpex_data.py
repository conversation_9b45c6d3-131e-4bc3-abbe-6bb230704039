#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 TPEX 上櫃公司資料
"""

import sqlite3
import pandas as pd
import os

def check_tpex_data():
    """檢查 TPEX 資料"""
    
    print("=" * 80)
    print("📊 檢查 TPEX 上櫃公司資料")
    print("=" * 80)
    
    # 檢查 TPEX 上櫃股票資訊
    print("📈 TPEX 上櫃股票資訊:")
    tpex_stock_db = r'D:\Finlab\history\tables\tpex_stock_info.db'
    
    if os.path.exists(tpex_stock_db):
        conn = sqlite3.connect(tpex_stock_db)
        
        # 檢查表格結構
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(tpex_stock_info)")
        columns = cursor.fetchall()
        print(f"   📋 表格欄位: {[col[1] for col in columns]}")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM tpex_stock_info")
        count = cursor.fetchone()[0]
        print(f"   📊 資料筆數: {count:,}")
        
        # 顯示範例資料
        df = pd.read_sql_query("SELECT * FROM tpex_stock_info LIMIT 10", conn)
        print(f"   📋 範例資料:")
        print(df)
        
        conn.close()
    else:
        print(f"   ❌ 檔案不存在: {tpex_stock_db}")
    
    # 檢查 TPEX 財務比率
    print(f"\n📊 TPEX 上櫃財務比率:")
    tpex_ratios_db = r'D:\Finlab\history\tables\tpex_financial_ratios.db'
    
    if os.path.exists(tpex_ratios_db):
        conn = sqlite3.connect(tpex_ratios_db)
        
        # 檢查表格結構
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(tpex_financial_ratios)")
        columns = cursor.fetchall()
        print(f"   📋 表格欄位: {[col[1] for col in columns]}")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM tpex_financial_ratios")
        count = cursor.fetchone()[0]
        print(f"   📊 資料筆數: {count:,}")
        
        # 顯示範例資料
        df = pd.read_sql_query("SELECT * FROM tpex_financial_ratios LIMIT 10", conn)
        print(f"   📋 範例資料:")
        print(df)
        
        conn.close()
    else:
        print(f"   ❌ 檔案不存在: {tpex_ratios_db}")

def compare_twse_tpex_coverage():
    """比較 TWSE 和 TPEX 的覆蓋範圍"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 比較 TWSE 和 TPEX 覆蓋範圍")
    print("=" * 80)
    
    # TWSE 資料
    twse_db = r'D:\Finlab\history\tables\financial_statements.db'
    if os.path.exists(twse_db):
        conn = sqlite3.connect(twse_db)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM income_statements")
        twse_count = cursor.fetchone()[0]
        print(f"📈 TWSE 上市公司數量: {twse_count:,}")
        
        # 顯示 TWSE 股票代碼範例
        cursor.execute("SELECT DISTINCT stock_id FROM income_statements ORDER BY stock_id LIMIT 10")
        twse_samples = [row[0] for row in cursor.fetchall()]
        print(f"   範例股票代碼: {twse_samples}")
        
        conn.close()
    else:
        print(f"❌ TWSE 資料庫不存在: {twse_db}")
        twse_count = 0
    
    # TPEX 資料
    tpex_db = r'D:\Finlab\history\tables\tpex_financial_ratios.db'
    if os.path.exists(tpex_db):
        conn = sqlite3.connect(tpex_db)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM tpex_financial_ratios")
        tpex_count = cursor.fetchone()[0]
        print(f"📊 TPEX 上櫃公司數量: {tpex_count:,}")
        
        # 顯示 TPEX 股票代碼範例
        cursor.execute("SELECT DISTINCT stock_id FROM tpex_financial_ratios ORDER BY stock_id LIMIT 10")
        tpex_samples = [row[0] for row in cursor.fetchall()]
        print(f"   範例股票代碼: {tpex_samples}")
        
        conn.close()
    else:
        print(f"❌ TPEX 資料庫不存在: {tpex_db}")
        tpex_count = 0
    
    # 總結
    total_count = twse_count + tpex_count
    print(f"\n📊 總覆蓋範圍:")
    print(f"   上市公司 (TWSE): {twse_count:,} 家")
    print(f"   上櫃公司 (TPEX): {tpex_count:,} 家")
    print(f"   總計: {total_count:,} 家")

def check_specific_otc_companies():
    """檢查特定上櫃公司是否存在"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 檢查特定上櫃公司")
    print("=" * 80)
    
    # 知名上櫃公司代碼
    otc_companies = [
        ('3443', '創意'),
        ('4966', '譜瑞'),
        ('5269', '祥碩'),
        ('6488', '環球晶'),
        ('8069', '元太'),
        ('8299', '群聯'),
        ('3034', '聯詠'),
        ('3661', '世芯'),
        ('4919', '新唐'),
        ('6415', '矽力')
    ]
    
    tpex_db = r'D:\Finlab\history\tables\tpex_financial_ratios.db'
    if os.path.exists(tpex_db):
        conn = sqlite3.connect(tpex_db)
        
        print(f"📋 檢查知名上櫃公司:")
        found_count = 0
        
        for code, name in otc_companies:
            cursor = conn.cursor()
            cursor.execute("SELECT stock_id, stock_name FROM tpex_financial_ratios WHERE stock_id = ?", (code,))
            result = cursor.fetchone()
            
            if result:
                print(f"   ✅ {code} {result[1]} (找到)")
                found_count += 1
            else:
                print(f"   ❌ {code} {name} (未找到)")
        
        print(f"\n📊 找到 {found_count}/{len(otc_companies)} 家知名上櫃公司")
        
        conn.close()
    else:
        print(f"❌ TPEX 資料庫不存在")

def main():
    """主函數"""
    
    print("🔍 TPEX 上櫃公司資料檢查")
    
    # 檢查 TPEX 資料
    check_tpex_data()
    
    # 比較覆蓋範圍
    compare_twse_tpex_coverage()
    
    # 檢查特定上櫃公司
    check_specific_otc_companies()
    
    print(f"\n" + "=" * 80)
    print(f"📊 總結")
    print("=" * 80)
    print(f"✅ TPEX 上櫃公司資料檢查完成")
    print(f"💡 現在你有了:")
    print(f"   - TWSE API: 上市公司財務報表")
    print(f"   - TPEX API: 上櫃公司基本資訊和財務比率")
    print(f"   - 合計覆蓋台股上市+上櫃公司")

if __name__ == "__main__":
    main()
