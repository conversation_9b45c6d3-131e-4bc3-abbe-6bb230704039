#!/usr/bin/env python3
"""
測試TSRTC監控視窗修復功能
"""

import sys
import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QHeaderView, QListWidget, QLineEdit, QMessageBox
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QColor

# 設置日誌
logging.basicConfig(level=logging.INFO)

class TSRTCMonitorDialog(QDialog):
    """修復後的TSRTC監控對話框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 模擬TSRTC監控器
        self.tsrtc_monitor = self.create_mock_monitor()
        
        # 監控股票列表（可設定）
        self.monitored_stocks = ['2330', '2317', '2454', '2412', '2881', '1301', '2382', '3008', '2002', '2886']
        
        # 數據記憶（避免清零）
        self.stock_data_cache = {}
        
        self.setup_ui()
        self.setup_timer()

    def create_mock_monitor(self):
        """創建模擬監控器"""
        class MockTSRTCMonitor:
            def test_connection(self):
                return True
            
            def get_multiple_stocks_realtime(self, stock_codes):
                """模擬獲取多支股票數據"""
                import random
                quotes = {}
                
                stock_names = {
                    '2330': '台積電', '2317': '鴻海', '2454': '聯發科', '2412': '中華電',
                    '2881': '富邦金', '1301': '台塑', '2382': '廣達', '3008': '大立光',
                    '2002': '中鋼', '2886': '兆豐金'
                }
                
                for code in stock_codes:
                    # 模擬有時候獲取失敗
                    if random.random() > 0.2:  # 80%成功率
                        price = random.uniform(50, 1000)
                        change = random.uniform(-20, 20)
                        change_pct = (change / price) * 100
                        
                        quotes[code] = {
                            'stock_code': code,
                            'stock_name': stock_names.get(code, f'股票{code}'),
                            'current_price': round(price, 2),
                            'change_amount': round(change, 2),
                            'change_percent': round(change_pct, 2),
                            'volume': f"{random.randint(1000, 999999):,}",
                            'timestamp': datetime.now().strftime('%H:%M:%S'),
                            'source': 'TSRTC'
                        }
                
                return quotes
        
        return MockTSRTCMonitor()

    def setup_ui(self):
        self.setWindowTitle("🔄 TSRTC 備用即時股價監控")
        self.setGeometry(100, 100, 1200, 800)
        
        # 設置視窗標誌 - 添加標準視窗控制按鈕
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint |
            Qt.WindowType.WindowSystemMenuHint |
            Qt.WindowType.WindowTitleHint
        )

        layout = QVBoxLayout()

        # 狀態區域
        status_layout = QHBoxLayout()
        self.status_label = QLabel("🔄 準備就緒")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #339af0;")
        
        # 股票數量顯示
        self.stock_count_label = QLabel(f"📊 監控股票: {len(self.monitored_stocks)} 支")
        self.stock_count_label.setStyleSheet("font-size: 12px; color: #51cf66;")

        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.stock_count_label)
        status_layout.addStretch()
        
        # 測試連接按鈕
        self.test_btn = QPushButton("🔍 測試連接")
        self.test_btn.clicked.connect(self.test_connection)
        self.test_btn.setStyleSheet("QPushButton { padding: 6px 12px; font-size: 11px; }")
        status_layout.addWidget(self.test_btn)
        
        # 股票設定按鈕
        self.settings_btn = QPushButton("⚙️ 股票設定")
        self.settings_btn.clicked.connect(self.open_stock_settings)
        self.settings_btn.setStyleSheet("QPushButton { padding: 6px 12px; font-size: 11px; }")
        status_layout.addWidget(self.settings_btn)
        
        layout.addLayout(status_layout)

        # 股票表格
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            '股票代碼', '股票名稱', '現價', '漲跌', '漲跌幅', '成交量', '時間', '來源'
        ])

        # 設置表格樣式
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.table)

        # 控制按鈕
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ 開始監控")
        self.start_btn.clicked.connect(self.start_monitoring)
        self.start_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #51cf66; color: white; border: none; border-radius: 4px; }")
        
        self.stop_btn = QPushButton("⏸️ 停止監控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #ff6b6b; color: white; border: none; border-radius: 4px; }")
        self.stop_btn.setEnabled(False)
        
        self.refresh_btn = QPushButton("🔄 立即更新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.refresh_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #339af0; color: white; border: none; border-radius: 4px; }")

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def setup_timer(self):
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)

    def open_stock_settings(self):
        """開啟股票設定對話框"""
        class StockSettingsDialog(QDialog):
            def __init__(self, parent, current_stocks):
                super().__init__(parent)
                self.current_stocks = current_stocks.copy()
                self.setWindowTitle("⚙️ TSRTC 監控股票設定")
                self.setModal(True)
                self.setFixedSize(500, 400)
                self.setup_ui()
            
            def setup_ui(self):
                layout = QVBoxLayout()
                
                # 標題
                title = QLabel("📊 設定要監控的股票代碼")
                title.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
                layout.addWidget(title)
                
                # 股票列表
                self.stock_list = QListWidget()
                for stock in self.current_stocks:
                    self.stock_list.addItem(stock)
                layout.addWidget(self.stock_list)
                
                # 添加股票
                add_layout = QHBoxLayout()
                self.stock_input = QLineEdit()
                self.stock_input.setPlaceholderText("輸入股票代碼 (例: 2330)")
                add_btn = QPushButton("➕ 添加")
                add_btn.clicked.connect(self.add_stock)
                
                add_layout.addWidget(self.stock_input)
                add_layout.addWidget(add_btn)
                layout.addLayout(add_layout)
                
                # 控制按鈕
                button_layout = QHBoxLayout()
                remove_btn = QPushButton("➖ 移除選中")
                remove_btn.clicked.connect(self.remove_stock)
                
                ok_btn = QPushButton("✅ 確定")
                ok_btn.clicked.connect(self.accept)
                
                cancel_btn = QPushButton("❌ 取消")
                cancel_btn.clicked.connect(self.reject)
                
                button_layout.addWidget(remove_btn)
                button_layout.addStretch()
                button_layout.addWidget(ok_btn)
                button_layout.addWidget(cancel_btn)
                layout.addLayout(button_layout)
                
                self.setLayout(layout)
            
            def add_stock(self):
                stock_code = self.stock_input.text().strip()
                if stock_code and stock_code.isdigit() and len(stock_code) == 4:
                    if stock_code not in self.current_stocks:
                        self.current_stocks.append(stock_code)
                        self.stock_list.addItem(stock_code)
                        self.stock_input.clear()
                    else:
                        QMessageBox.warning(self, "警告", f"股票代碼 {stock_code} 已存在")
                else:
                    QMessageBox.warning(self, "錯誤", "請輸入有效的4位數股票代碼")
            
            def remove_stock(self):
                current_item = self.stock_list.currentItem()
                if current_item:
                    stock_code = current_item.text()
                    self.current_stocks.remove(stock_code)
                    self.stock_list.takeItem(self.stock_list.row(current_item))
        
        # 顯示設定對話框
        dialog = StockSettingsDialog(self, self.monitored_stocks)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.monitored_stocks = dialog.current_stocks
            self.stock_count_label.setText(f"📊 監控股票: {len(self.monitored_stocks)} 支")
            self.status_label.setText("✅ 股票設定已更新")
            
            # 清空快取，重新載入
            self.stock_data_cache.clear()
            self.table.setRowCount(0)

    def test_connection(self):
        self.status_label.setText("🔍 測試連接中...")
        if self.tsrtc_monitor.test_connection():
            self.status_label.setText("✅ TSRTC連接正常")
        else:
            self.status_label.setText("❌ TSRTC連接失敗")

    def start_monitoring(self):
        self.timer.start(5000)  # 5秒更新一次
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("🚀 監控中...")

    def stop_monitoring(self):
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("⏹️ 監控已停止")

    def refresh_data(self):
        """刷新數據 - 帶記憶功能，避免清零"""
        try:
            # 使用設定的監控股票列表
            quotes = self.tsrtc_monitor.get_multiple_stocks_realtime(self.monitored_stocks)
            
            # 更新快取數據
            for stock_code, data in quotes.items():
                if data and data.get('current_price', 0) > 0:  # 只有成功獲取數據才更新快取
                    self.stock_data_cache[stock_code] = data
            
            # 準備顯示數據（結合快取和新數據）
            display_data = {}
            for stock_code in self.monitored_stocks:
                if stock_code in self.stock_data_cache:
                    display_data[stock_code] = self.stock_data_cache[stock_code]
                else:
                    # 如果沒有快取數據，創建空白項目
                    display_data[stock_code] = {
                        'stock_code': stock_code,
                        'stock_name': f'股票{stock_code}',
                        'current_price': 0.0,
                        'change_amount': 0.0,
                        'change_percent': 0.0,
                        'volume': '0',
                        'timestamp': '等待更新...',
                        'source': 'TSRTC'
                    }
            
            # 更新表格
            self.table.setRowCount(len(display_data))
            
            for row, (stock_code, data) in enumerate(display_data.items()):
                # 股票代碼
                self.table.setItem(row, 0, QTableWidgetItem(data.get('stock_code', stock_code)))
                
                # 股票名稱
                self.table.setItem(row, 1, QTableWidgetItem(data.get('stock_name', f'股票{stock_code}')))
                
                # 現價
                price = data.get('current_price', 0)
                price_text = f"{price:.2f}" if price > 0 else "0.00"
                self.table.setItem(row, 2, QTableWidgetItem(price_text))

                # 漲跌金額
                change = data.get('change_amount', 0)
                change_item = QTableWidgetItem(f"{change:+.2f}" if change != 0 else "0.00")
                if change > 0:
                    change_item.setBackground(QColor(255, 200, 200))  # 紅色背景
                elif change < 0:
                    change_item.setBackground(QColor(200, 255, 200))  # 綠色背景
                self.table.setItem(row, 3, change_item)

                # 漲跌幅
                pct = data.get('change_percent', 0)
                pct_item = QTableWidgetItem(f"{pct:+.2f}%" if pct != 0 else "0.00%")
                if pct > 0:
                    pct_item.setBackground(QColor(255, 200, 200))  # 紅色背景
                elif pct < 0:
                    pct_item.setBackground(QColor(200, 255, 200))  # 綠色背景
                self.table.setItem(row, 4, pct_item)

                # 成交量
                volume = data.get('volume', '0')
                self.table.setItem(row, 5, QTableWidgetItem(str(volume)))
                
                # 時間戳
                timestamp = data.get('timestamp', '等待更新...')
                self.table.setItem(row, 6, QTableWidgetItem(timestamp))
                
                # 來源
                source = data.get('source', 'TSRTC')
                self.table.setItem(row, 7, QTableWidgetItem(source))

            # 更新狀態
            successful_updates = len([d for d in display_data.values() if d.get('current_price', 0) > 0])
            current_time = datetime.now().strftime('%H:%M:%S')
            self.status_label.setText(f"✅ 已更新 {successful_updates}/{len(self.monitored_stocks)} 支股票 - {current_time}")

        except Exception as e:
            self.status_label.setText(f"❌ 更新失敗: {str(e)}")
            logging.error(f"TSRTC數據更新失敗: {e}")

def main():
    """主函數"""
    print("🧪 測試TSRTC監控視窗修復功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示對話框
    dialog = TSRTCMonitorDialog()
    dialog.show()
    
    print("✅ TSRTC監控視窗已開啟")
    print("🔍 測試項目:")
    print("  1. ✅ 視窗右上角有最大化、最小化、關閉按鈕")
    print("  2. ✅ 有股票設定功能（⚙️ 股票設定按鈕）")
    print("  3. ✅ 有記憶功能（更新時不會清零）")
    print("  4. ✅ 改善的界面樣式")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
