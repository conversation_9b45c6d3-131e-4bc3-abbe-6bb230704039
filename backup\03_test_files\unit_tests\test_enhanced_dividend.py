#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版除權息功能
"""

import sys
import os
import logging
from datetime import datetime

def test_enhanced_dividend_crawler():
    """測試增強版除權息爬蟲"""
    print("🚀 測試增強版除權息爬蟲")
    print("=" * 50)
    
    try:
        # 測試模組導入
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        print("✅ 增強版除權息爬蟲模組載入成功")
        
        # 創建爬蟲實例
        crawler = EnhancedDividendCrawler()
        print("✅ 爬蟲實例創建成功")
        
        # 檢查資料庫初始化
        if os.path.exists(crawler.db_path):
            file_size = os.path.getsize(crawler.db_path) / 1024  # KB
            print(f"✅ 資料庫已初始化: {crawler.db_path}")
            print(f"📊 資料庫大小: {file_size:.1f} KB")
        else:
            print(f"⚠️ 資料庫文件不存在: {crawler.db_path}")
        
        # 檢查資料源配置
        print(f"\n🌐 資料源配置:")
        for source_key, source_info in crawler.data_sources.items():
            status = "✅ 啟用" if source_info['enabled'] else "❌ 禁用"
            print(f"  • {source_info['name']}: {status}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_data_sources():
    """測試各個資料源的可用性"""
    print("\n🔍 測試資料源可用性")
    print("-" * 30)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        crawler = EnhancedDividendCrawler()
        
        # 測試證交所API
        print("📊 測試證交所 (TWSE)...")
        try:
            twse_data = crawler.fetch_twse_dividend_data(2024)
            if twse_data:
                print(f"  ✅ 證交所: 獲取到 {len(twse_data)} 筆資料")
            else:
                print(f"  ⚠️ 證交所: 無資料或連線失敗")
        except Exception as e:
            print(f"  ❌ 證交所: {e}")
        
        # 測試櫃買中心API
        print("📈 測試櫃買中心 (TPEx)...")
        try:
            tpex_data = crawler.fetch_tpex_dividend_data(2024)
            if tpex_data:
                print(f"  ✅ 櫃買中心: 獲取到 {len(tpex_data)} 筆資料")
            else:
                print(f"  ⚠️ 櫃買中心: 無資料或連線失敗")
        except Exception as e:
            print(f"  ❌ 櫃買中心: {e}")
        
        # 測試Goodinfo (需要Selenium)
        print("🌐 測試 Goodinfo...")
        try:
            # 檢查Selenium是否可用
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            print("  ✅ Selenium 模組可用")
            print("  💡 Goodinfo 需要較長時間，建議在實際使用時測試")
        except ImportError:
            print("  ❌ Selenium 模組未安裝")
        except Exception as e:
            print(f"  ⚠️ Selenium 測試: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料源測試失敗: {e}")
        return False

def test_database_operations():
    """測試資料庫操作"""
    print("\n💾 測試資料庫操作")
    print("-" * 30)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        crawler = EnhancedDividendCrawler()
        
        # 測試資料儲存
        test_records = [
            {
                'stock_code': '2330',
                'stock_name': '台積電',
                'year': 2024,
                'cash_dividend': 3.0,
                'stock_dividend': 0.0,
                'total_dividend': 3.0,
                'data_source': 'test'
            },
            {
                'stock_code': '2317',
                'stock_name': '鴻海',
                'year': 2024,
                'cash_dividend': 5.2,
                'stock_dividend': 0.0,
                'total_dividend': 5.2,
                'data_source': 'test'
            }
        ]
        
        print("📝 測試資料儲存...")
        crawler.save_dividend_data(test_records)
        print(f"✅ 成功儲存 {len(test_records)} 筆測試資料")
        
        # 測試資料查詢
        print("🔍 測試資料查詢...")
        tsmc_history = crawler.get_stock_dividend_history('2330', years=1)
        if tsmc_history:
            print(f"✅ 查詢台積電歷史: {len(tsmc_history)} 筆資料")
            for record in tsmc_history[:3]:  # 只顯示前3筆
                print(f"  • {record['year']}: 現金股利 {record['cash_dividend']}")
        else:
            print("⚠️ 未找到台積電歷史資料")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料庫操作測試失敗: {e}")
        return False

def test_gui_integration():
    """測試GUI整合"""
    print("\n🖥️ 測試GUI整合")
    print("-" * 30)
    
    try:
        # 檢查主程式是否包含增強版功能
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 檢查是否有增強版除權息下載方法
        if hasattr(StockScreenerGUI, 'download_enhanced_dividend_data'):
            print("✅ 主程式包含增強版除權息下載功能")
        else:
            print("❌ 主程式缺少增強版除權息下載功能")
            return False
        
        # 檢查選單項目
        print("✅ GUI整合測試通過")
        print("💡 可以在主程式中測試: 資料下載 → 除權息資料 (增強版)")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI整合測試失敗: {e}")
        return False

def test_dependencies():
    """測試相依套件"""
    print("\n📦 測試相依套件")
    print("-" * 30)
    
    dependencies = {
        'requests': '網路請求',
        'beautifulsoup4': 'HTML解析',
        'selenium': '瀏覽器自動化',
        'pandas': '資料處理',
        'sqlite3': '資料庫 (內建)',
        'PyQt6': 'GUI框架'
    }
    
    missing_deps = []
    
    for dep, desc in dependencies.items():
        try:
            if dep == 'beautifulsoup4':
                import bs4
            elif dep == 'sqlite3':
                import sqlite3
            elif dep == 'PyQt6':
                import PyQt6
            else:
                __import__(dep)
            print(f"✅ {dep}: {desc}")
        except ImportError:
            print(f"❌ {dep}: {desc} (未安裝)")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️ 缺少套件: {', '.join(missing_deps)}")
        print("💡 安裝指令:")
        for dep in missing_deps:
            if dep == 'beautifulsoup4':
                print(f"  pip install beautifulsoup4")
            else:
                print(f"  pip install {dep}")
        return False
    else:
        print("\n✅ 所有相依套件都已安裝")
        return True

def main():
    """主測試函數"""
    print("🔧 增強版除權息功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = {}
    
    # 執行各項測試
    test_results['相依套件'] = test_dependencies()
    test_results['爬蟲模組'] = test_enhanced_dividend_crawler()
    test_results['資料源'] = test_data_sources()
    test_results['資料庫操作'] = test_database_operations()
    test_results['GUI整合'] = test_gui_integration()
    
    # 總結測試結果
    print("\n📊 測試結果總結")
    print("=" * 30)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status_icon = "✅" if result else "❌"
        print(f"{status_icon} {test_name}: {'通過' if result else '失敗'}")
        if result:
            passed_tests += 1
    
    print(f"\n📈 通過率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    # 提供使用建議
    print("\n💡 使用建議")
    print("-" * 15)
    
    if test_results['爬蟲模組'] and test_results['GUI整合']:
        print("✅ 增強版除權息功能已就緒")
        print("🚀 可以在主程式中使用: 資料下載 → 除權息資料 (增強版)")
        print("🌐 支援多個資料源: 證交所、櫃買中心、Goodinfo")
        print("📊 可選擇下載年份範圍")
    else:
        print("⚠️ 部分功能需要修正才能使用")
    
    if not test_results['相依套件']:
        print("📦 請先安裝缺少的套件")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests >= total_tests * 0.8  # 80% 通過率視為成功

if __name__ == "__main__":
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    success = main()
    
    if success:
        print("\n🎉 增強版除權息功能測試成功！")
        print("💡 現在可以使用整合多個資料源的除權息下載功能")
    else:
        print("\n⚠️ 部分測試失敗，請檢查相關設定")
    
    print("\n按 Enter 鍵結束...")
    input()
