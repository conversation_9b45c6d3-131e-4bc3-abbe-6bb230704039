#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查資料庫檔案位置和內容
"""

import sqlite3
import pandas as pd
import os

def check_db_location():
    """檢查資料庫檔案位置和內容"""
    
    print("=" * 60)
    print("📂 檢查資料庫檔案位置和內容")
    print("=" * 60)
    
    # 可能的資料庫路徑
    possible_paths = [
        'D:/Finlab/history/tables/newprice.db',
        'D:/Finlab/backup/O3mh_strategy2AA/newprice.db',
        './newprice.db',
        'newprice.db'
    ]
    
    for db_path in possible_paths:
        print(f"\n🔍 檢查路徑: {db_path}")
        
        if os.path.exists(db_path):
            print(f"   ✅ 檔案存在")
            
            # 檢查檔案大小
            file_size = os.path.getsize(db_path)
            print(f"   📊 檔案大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            
            # 檢查檔案修改時間
            import datetime
            mod_time = os.path.getmtime(db_path)
            mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
            print(f"   🕒 最後修改時間: {mod_time_str}")
            
            try:
                # 連接資料庫並檢查內容
                conn = sqlite3.connect(db_path)
                
                # 檢查表格
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"   📋 表格: {[table[0] for table in tables]}")
                
                # 檢查 stock_daily_data 表格
                if ('stock_daily_data',) in tables:
                    # 檢查最新日期
                    query_latest = '''
                        SELECT MAX(date) as latest_date, COUNT(*) as total_records
                        FROM stock_daily_data
                    '''
                    
                    result = pd.read_sql_query(query_latest, conn)
                    latest_date = result.iloc[0]['latest_date']
                    total_records = result.iloc[0]['total_records']
                    
                    print(f"   📅 最新日期: {latest_date}")
                    print(f"   📊 總記錄數: {total_records:,}")
                    
                    # 檢查最近5天的資料
                    query_recent = '''
                        SELECT date, COUNT(*) as record_count
                        FROM stock_daily_data
                        GROUP BY date
                        ORDER BY date DESC
                        LIMIT 10
                    '''
                    
                    df_recent = pd.read_sql_query(query_recent, conn)
                    print(f"   📋 最近10天資料:")
                    for _, row in df_recent.iterrows():
                        print(f"      {row['date']}: {row['record_count']} 筆")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 讀取資料庫失敗: {e}")
        else:
            print(f"   ❌ 檔案不存在")
    
    # 檢查當前工作目錄
    print(f"\n📂 當前工作目錄: {os.getcwd()}")
    
    # 列出當前目錄的 .db 檔案
    print(f"\n📋 當前目錄的 .db 檔案:")
    for file in os.listdir('.'):
        if file.endswith('.db'):
            file_size = os.path.getsize(file)
            print(f"   {file} ({file_size:,} bytes)")

if __name__ == "__main__":
    check_db_location()
