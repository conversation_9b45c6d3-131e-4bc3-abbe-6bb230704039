#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
診斷GoodInfo月營收下載問題
檢查網站結構變化和頁面載入問題
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

def diagnose_goodinfo_page():
    """診斷GoodInfo頁面問題"""
    print("🔍 診斷GoodInfo月營收頁面問題")
    print("=" * 50)
    
    try:
        # 設置Chrome選項
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        # 不使用無頭模式，這樣可以看到頁面
        
        print("🔧 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=options)
        
        # 測試URL
        test_url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問URL: {test_url}")
        
        driver.get(test_url)
        
        # 等待頁面載入
        print("⏳ 等待頁面載入...")
        time.sleep(5)
        
        # 檢查頁面標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 檢查頁面是否正常載入
        if "goodinfo" not in title.lower() and "台積電" not in title:
            print("❌ 頁面標題異常，可能載入失敗")
            return False
        
        # 檢查頁面內容
        page_source = driver.page_source
        print(f"📋 頁面內容長度: {len(page_source)} 字符")
        
        # 檢查是否包含營收相關關鍵字
        revenue_keywords = ["營收", "億", "月增", "年增", "累計"]
        found_keywords = []
        
        for keyword in revenue_keywords:
            if keyword in page_source:
                found_keywords.append(keyword)
        
        print(f"🔍 找到營收關鍵字: {found_keywords}")
        
        if not found_keywords:
            print("❌ 頁面不包含營收相關內容")
            
            # 檢查是否有錯誤訊息
            error_indicators = ["錯誤", "error", "404", "無法", "找不到"]
            found_errors = []
            
            for error in error_indicators:
                if error in page_source.lower():
                    found_errors.append(error)
            
            if found_errors:
                print(f"⚠️ 發現錯誤指標: {found_errors}")
        
        # 檢查divDetail元素
        print(f"\n🔍 檢查divDetail元素...")
        try:
            div_detail = driver.find_element(By.ID, "divDetail")
            detail_text = div_detail.text
            print(f"✅ 找到divDetail元素")
            print(f"📊 divDetail內容長度: {len(detail_text)} 字符")
            
            if detail_text:
                # 顯示前500字符
                preview = detail_text[:500]
                print(f"📋 divDetail內容預覽:\n{preview}")
                
                # 檢查是否包含股票代號
                import re
                stock_codes = re.findall(r'\b\d{4}\b', detail_text)
                if stock_codes:
                    unique_codes = list(set(stock_codes))[:10]  # 前10個不重複代號
                    print(f"📈 找到股票代號: {unique_codes}")
                else:
                    print("❌ divDetail中未找到股票代號")
            else:
                print("❌ divDetail元素為空")
                
        except Exception as e:
            print(f"❌ 無法找到divDetail元素: {e}")
        
        # 檢查表格元素
        print(f"\n🔍 檢查表格元素...")
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"📊 找到 {len(tables)} 個表格")
        
        if tables:
            # 檢查第一個表格
            first_table = tables[0]
            rows = first_table.find_elements(By.TAG_NAME, "tr")
            print(f"📋 第一個表格有 {len(rows)} 行")
            
            if len(rows) > 0:
                # 顯示前3行內容
                for i, row in enumerate(rows[:3]):
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if cells:
                        cell_texts = [cell.text[:20] for cell in cells[:5]]  # 前5列，每列最多20字符
                        print(f"   行{i+1}: {cell_texts}")
        
        # 檢查匯出按鈕
        print(f"\n🔍 檢查匯出按鈕...")
        export_buttons = driver.find_elements(By.XPATH, "//input[@value='XLS']")
        print(f"📤 找到 {len(export_buttons)} 個XLS匯出按鈕")
        
        for i, button in enumerate(export_buttons):
            onclick = button.get_attribute("onclick")
            print(f"   按鈕{i+1}: {onclick}")
        
        # 檢查JavaScript錯誤
        print(f"\n🔍 檢查JavaScript錯誤...")
        logs = driver.get_log('browser')
        if logs:
            print(f"⚠️ 發現 {len(logs)} 個瀏覽器日誌:")
            for log in logs[-5:]:  # 顯示最後5個日誌
                print(f"   {log['level']}: {log['message'][:100]}")
        else:
            print("✅ 無JavaScript錯誤")
        
        # 等待用戶檢查
        input("\n按Enter繼續...")
        
        # 嘗試點擊匯出按鈕測試
        if export_buttons:
            print(f"\n🧪 測試匯出按鈕...")
            response = input("是否要測試點擊匯出按鈕？(y/n): ").lower().strip()
            
            if response == 'y':
                try:
                    # 設置下載目錄
                    download_dir = "D:/Finlab/history/tables/monthly_revenue"
                    os.makedirs(download_dir, exist_ok=True)
                    
                    print("🔄 點擊匯出按鈕...")
                    export_buttons[0].click()
                    
                    print("⏳ 等待下載...")
                    time.sleep(5)
                    
                    # 檢查下載文件
                    import glob
                    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
                    
                    if excel_files:
                        latest_file = max(excel_files, key=os.path.getctime)
                        file_size = os.path.getsize(latest_file)
                        print(f"✅ 下載成功: {os.path.basename(latest_file)} ({file_size} bytes)")
                        
                        # 簡單檢查文件內容
                        try:
                            import pandas as pd
                            df = pd.read_excel(latest_file, header=None)
                            print(f"📊 Excel文件: {len(df)} 行, {len(df.columns)} 列")
                            
                            # 顯示前3行
                            print("📋 前3行內容:")
                            for i in range(min(3, len(df))):
                                row_data = [str(x)[:15] for x in df.iloc[i][:5]]
                                print(f"   行{i+1}: {row_data}")
                                
                        except Exception as e:
                            print(f"⚠️ 無法讀取Excel文件: {e}")
                    else:
                        print("❌ 未找到下載文件")
                        
                except Exception as e:
                    print(f"❌ 點擊匯出按鈕失敗: {e}")
        
        input("\n按Enter關閉瀏覽器...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 診斷失敗: {e}")
        return False

def suggest_solutions():
    """建議解決方案"""
    print("\n💡 可能的解決方案:")
    print("=" * 30)
    
    print("1. **網站結構變化**:")
    print("   - GoodInfo可能更新了頁面結構")
    print("   - 需要更新數據解析邏輯")
    print("   - 檢查divDetail元素是否存在")
    
    print("\n2. **載入時間問題**:")
    print("   - 增加頁面載入等待時間")
    print("   - 使用WebDriverWait等待元素")
    print("   - 檢查網路連線狀況")
    
    print("\n3. **反爬蟲機制**:")
    print("   - 網站可能加強了反爬蟲")
    print("   - 需要添加更多延遲")
    print("   - 考慮使用不同的User-Agent")
    
    print("\n4. **JavaScript問題**:")
    print("   - 檢查頁面JavaScript錯誤")
    print("   - 確保所有腳本正常載入")
    print("   - 可能需要執行特定JavaScript")

def main():
    """主函數"""
    print("🧪 GoodInfo月營收下載問題診斷")
    print("=" * 50)
    
    response = input("是否要開啟瀏覽器進行診斷？(y/n): ").lower().strip()
    if response != 'y':
        suggest_solutions()
        return
    
    success = diagnose_goodinfo_page()
    
    if success:
        suggest_solutions()
        print("\n🎉 診斷完成！")
    else:
        print("\n❌ 診斷失敗")

if __name__ == "__main__":
    main()
