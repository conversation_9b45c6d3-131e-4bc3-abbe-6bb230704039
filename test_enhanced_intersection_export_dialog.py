#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版策略交集導出對話框功能
"""

import sys
import os
import tempfile
import json
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit
from PyQt6.QtCore import Qt

# 添加主程序路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestIntersectionExportDialog(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("測試策略交集導出對話框")
        self.setGeometry(100, 100, 600, 400)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加說明文字
        self.info_text = QTextEdit()
        self.info_text.setPlainText(
            "🧪 策略交集導出對話框測試工具\n\n"
            "此工具用於測試新的增強版導出對話框功能：\n"
            "• 詢問用戶是否要開啟存檔位置\n"
            "• 提供多種開啟選項\n"
            "• 處理開啟失敗的情況\n\n"
            "點擊下方按鈕進行測試："
        )
        self.info_text.setMaximumHeight(150)
        layout.addWidget(self.info_text)
        
        # 測試按鈕
        test1_btn = QPushButton("🎯 測試完整導出成功")
        test1_btn.clicked.connect(self.test_complete_export)
        layout.addWidget(test1_btn)
        
        test2_btn = QPushButton("⚠️ 測試部分導出成功")
        test2_btn.clicked.connect(self.test_partial_export)
        layout.addWidget(test2_btn)
        
        test3_btn = QPushButton("❌ 測試開啟失敗情況")
        test3_btn.clicked.connect(self.test_open_failure)
        layout.addWidget(test3_btn)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setPlainText("測試結果將顯示在這裡...")
        layout.addWidget(self.result_text)
        
        # 創建臨時測試文件
        self.setup_test_files()
    
    def setup_test_files(self):
        """創建測試用的臨時文件"""
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp(prefix="intersection_test_")
        
        # 創建測試JSON文件
        test_data = {
            "strategies": ["CANSLIM量價齊升", "藏獒"],
            "intersection_stocks": ["2330", "2317"],
            "intersection_count": 2,
            "individual_counts": {
                "CANSLIM量價齊升": 5,
                "藏獒": 8
            },
            "analysis_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.json_file = os.path.join(self.temp_dir, "策略交集_測試_20250730_235014.json")
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 創建測試報告文件
        self.report_file = os.path.join(self.temp_dir, "策略交集_測試_20250730_235014_詳細報告.txt")
        with open(self.report_file, 'w', encoding='utf-8') as f:
            f.write("🔍 策略交集分析詳細報告\n")
            f.write("=" * 40 + "\n\n")
            f.write("📊 分析策略：CANSLIM量價齊升, 藏獒\n")
            f.write("📈 共同股票：2330, 2317\n")
            f.write("🎯 交集數量：2 支\n\n")
            f.write("詳細分析結果...\n")
        
        self.log(f"✅ 測試文件已創建：\n📁 {self.temp_dir}")
    
    def log(self, message):
        """添加日誌信息"""
        current_text = self.result_text.toPlainText()
        timestamp = datetime.now().strftime('%H:%M:%S')
        new_text = f"{current_text}\n[{timestamp}] {message}"
        self.result_text.setPlainText(new_text)
        
        # 滾動到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.result_text.setTextCursor(cursor)
    
    def test_complete_export(self):
        """測試完整導出成功的情況"""
        self.log("🎯 測試完整導出成功...")
        
        # 模擬增強版導出對話框
        self.show_enhanced_intersection_export_dialog(
            self.json_file, 
            self.report_file, 
            2
        )
    
    def test_partial_export(self):
        """測試部分導出成功的情況"""
        self.log("⚠️ 測試部分導出成功...")
        
        # 模擬簡化版導出對話框
        self.show_simple_intersection_export_dialog(
            self.json_file,
            2,
            "無法寫入詳細報告文件：權限不足"
        )
    
    def test_open_failure(self):
        """測試開啟失敗的情況"""
        self.log("❌ 測試開啟失敗情況...")
        
        # 使用無效路徑測試
        invalid_json = "/invalid/path/test.json"
        invalid_report = "/invalid/path/test_report.txt"
        
        self.show_enhanced_intersection_export_dialog(
            invalid_json,
            invalid_report,
            0
        )
    
    def show_enhanced_intersection_export_dialog(self, json_filename, report_filename, stock_count):
        """顯示增強版策略交集導出對話框（從主程序複製）"""
        from PyQt6.QtWidgets import QMessageBox
        import platform
        import subprocess
        
        # 獲取檔案資訊
        json_size = os.path.getsize(json_filename) if os.path.exists(json_filename) else 0
        report_size = os.path.getsize(report_filename) if os.path.exists(report_filename) else 0
        total_size_mb = (json_size + report_size) / (1024 * 1024)
        
        # 創建自定義對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("導出完成")
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setText("✅ 交集結果已成功導出！")
        
        # 構建詳細信息
        detail_info = f"📊 數據文件：{os.path.basename(json_filename)}\n"
        detail_info += f"📝 詳細報告：{os.path.basename(report_filename)}\n"
        detail_info += f"📁 存放位置：{os.path.dirname(json_filename)}\n"
        detail_info += f"📈 共同股票數量：{stock_count} 支\n"
        detail_info += f"💾 檔案大小：{total_size_mb:.2f} MB\n\n"
        detail_info += f"❓ 是否要立即開啟存檔位置？"
        
        msg_box.setInformativeText(detail_info)
        
        # 添加自定義按鈕
        open_btn = msg_box.addButton("📂 開啟位置", QMessageBox.ButtonRole.YesRole)
        view_btn = msg_box.addButton("📄 查看報告", QMessageBox.ButtonRole.ActionRole)
        later_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        
        # 設置默認按鈕
        msg_box.setDefaultButton(open_btn)
        
        # 顯示對話框並處理用戶選擇
        reply = msg_box.exec()
        clicked_button = msg_box.clickedButton()
        
        try:
            if clicked_button == open_btn:
                # 開啟檔案所在資料夾
                folder_path = os.path.dirname(json_filename)
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])
                
                self.log(f"✅ 已開啟存檔位置：{folder_path}")
                
            elif clicked_button == view_btn:
                # 開啟詳細報告檔案
                if platform.system() == "Windows":
                    os.startfile(report_filename)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", report_filename])
                else:  # Linux
                    subprocess.run(["xdg-open", report_filename])
                
                self.log(f"✅ 已開啟詳細報告：{os.path.basename(report_filename)}")
                
            else:  # later_btn
                # 顯示檔案位置提示
                QMessageBox.information(
                    self,
                    "導出完成",
                    f"📋 交集結果已成功保存\n\n"
                    f"📁 檔案位置：\n{json_filename}\n{report_filename}\n\n"
                    f"💡 您可以稍後手動開啟這些檔案"
                )
                self.log("📋 用戶選擇稍後開啟")
                
        except Exception as e:
            # 如果開啟失敗，顯示錯誤信息和檔案位置
            error_msg = f"❌ 無法開啟檔案或資料夾\n\n錯誤信息：{str(e)}\n\n📁 請手動開啟以下位置：\n{os.path.dirname(json_filename)}\n\n📊 檔案名稱：\n• {os.path.basename(json_filename)}\n• {os.path.basename(report_filename)}"
            QMessageBox.warning(self, "開啟失敗", error_msg)
            self.log(f"❌ 開啟失敗：{str(e)}")
    
    def show_simple_intersection_export_dialog(self, json_filename, stock_count, error_msg):
        """顯示簡化版策略交集導出對話框（從主程序複製）"""
        from PyQt6.QtWidgets import QMessageBox
        import platform
        import subprocess
        
        # 獲取檔案資訊
        json_size = os.path.getsize(json_filename) if os.path.exists(json_filename) else 0
        json_size_mb = json_size / (1024 * 1024)
        
        # 創建自定義對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("部分導出完成")
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setText("⚠️ 數據文件導出成功，但詳細報告導出失敗")
        
        # 構建詳細信息
        detail_info = f"✅ 數據文件：{os.path.basename(json_filename)}\n"
        detail_info += f"📁 存放位置：{os.path.dirname(json_filename)}\n"
        detail_info += f"📈 共同股票數量：{stock_count} 支\n"
        detail_info += f"💾 檔案大小：{json_size_mb:.2f} MB\n\n"
        detail_info += f"❌ 詳細報告導出失敗：{error_msg}\n\n"
        detail_info += f"❓ 是否要開啟數據文件位置？"
        
        msg_box.setInformativeText(detail_info)
        
        # 添加自定義按鈕
        open_btn = msg_box.addButton("📂 開啟位置", QMessageBox.ButtonRole.YesRole)
        later_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        
        # 設置默認按鈕
        msg_box.setDefaultButton(open_btn)
        
        # 顯示對話框並處理用戶選擇
        reply = msg_box.exec()
        clicked_button = msg_box.clickedButton()
        
        try:
            if clicked_button == open_btn:
                # 開啟檔案所在資料夾
                folder_path = os.path.dirname(json_filename)
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])
                
                self.log(f"✅ 已開啟存檔位置：{folder_path}")
                
            else:  # later_btn
                # 顯示檔案位置提示
                QMessageBox.information(
                    self,
                    "導出完成",
                    f"📋 數據文件已成功保存\n\n"
                    f"📁 檔案位置：\n{json_filename}\n\n"
                    f"💡 您可以稍後手動開啟此檔案"
                )
                self.log("📋 用戶選擇稍後開啟")
                
        except Exception as e:
            # 如果開啟失敗，顯示錯誤信息和檔案位置
            error_msg = f"❌ 無法開啟檔案或資料夾\n\n錯誤信息：{str(e)}\n\n📁 請手動開啟以下位置：\n{os.path.dirname(json_filename)}\n\n📊 檔案名稱：{os.path.basename(json_filename)}"
            QMessageBox.warning(self, "開啟失敗", error_msg)
            self.log(f"❌ 開啟失敗：{str(e)}")

def main():
    app = QApplication(sys.argv)
    window = TestIntersectionExportDialog()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
