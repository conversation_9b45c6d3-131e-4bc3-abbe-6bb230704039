#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試界面調試輸出
運行實際的掃描並查看調試信息
"""

import sys
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TestMainWindow(QMainWindow):
    """測試主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("市場掃描界面調試測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建界面組件（模擬主程式的組件）
        self.market_sentiment_label = QLabel("📈 市場情緒：等待掃描...")
        self.us_status_label = QLabel("🇺🇸 美股：等待數據...")
        self.tw_status_label = QLabel("🇹🇼 台指期：等待數據...")
        self.premarket_status_label = QLabel("📊 開盤前監控：準備中...")
        
        # 掃描按鈕
        self.scan_btn = QPushButton("🔍 開始測試掃描")
        self.scan_btn.clicked.connect(self.test_scan)
        
        # 添加到布局
        layout.addWidget(QLabel("=== 市場掃描界面調試測試 ==="))
        layout.addWidget(self.market_sentiment_label)
        layout.addWidget(self.us_status_label)
        layout.addWidget(self.tw_status_label)
        layout.addWidget(self.premarket_status_label)
        layout.addWidget(self.scan_btn)
        
        # 初始化監控器
        self.init_monitor()
        
        # 掃描狀態
        self._is_scanning = False
        
    def init_monitor(self):
        """初始化監控器"""
        try:
            from monitoring.pre_market_monitor import PreMarketMonitor
            self.pre_market_monitor = PreMarketMonitor()
            print("✅ 監控器初始化成功")
        except Exception as e:
            print(f"❌ 監控器初始化失敗: {e}")
            self.pre_market_monitor = None
    
    def test_scan(self):
        """測試掃描"""
        if self._is_scanning:
            print("⚠️ 掃描已在進行中")
            return
            
        if not self.pre_market_monitor:
            print("❌ 監控器未初始化")
            return
        
        print("🚀 開始測試掃描...")
        self._is_scanning = True
        self.scan_btn.setText("🔍 掃描中...")
        self.scan_btn.setEnabled(False)
        
        # 使用定時器模擬異步掃描
        QTimer.singleShot(100, self.run_scan)
    
    def run_scan(self):
        """運行掃描"""
        try:
            print("📊 執行掃描...")
            results = self.pre_market_monitor.run_full_scan()
            
            print(f"📋 掃描完成，結果類型: {type(results)}")
            if results:
                print(f"📋 結果鍵: {list(results.keys())}")
            
            # 調用成功回調
            self._on_scan_success(results)
            
        except Exception as e:
            print(f"❌ 掃描失敗: {e}")
            self._on_scan_error(str(e))
    
    def _on_scan_success(self, results):
        """掃描成功回調 - 複製主程式的邏輯"""
        try:
            print(f"🔍 DEBUG: _on_scan_success 被調用，results類型: {type(results)}")
            print(f"🔍 DEBUG: results是否為空: {not bool(results)}")
            if results:
                print(f"🔍 DEBUG: results鍵: {list(results.keys()) if isinstance(results, dict) else 'Not dict'}")
            
            # 重置掃描狀態
            self._is_scanning = False
            self.scan_btn.setText("🔍 開始測試掃描")
            self.scan_btn.setEnabled(True)

            if results:
                print(f"🔍 DEBUG: 開始更新界面，results有數據")
                
                # 🎯 更新儀表板
                print(f"🔍 DEBUG: 調用 update_market_dashboard")
                self.update_market_dashboard(results)

                # 🎯 更新詳細數據顯示
                print(f"🔍 DEBUG: 調用 update_market_details")
                self.update_market_details(results)

                print(f"🔍 DEBUG: 界面更新完成")
                print("✅ 智能市場掃描完成")

            else:
                self.premarket_status_label.setText("❌ 掃描失敗，請檢查網路連接")
                print("❌ 市場掃描失敗")

        except Exception as e:
            print(f"❌ 掃描成功回調處理失敗: {e}")
    
    def _on_scan_error(self, error_message):
        """掃描失敗回調"""
        self._is_scanning = False
        self.scan_btn.setText("🔍 開始測試掃描")
        self.scan_btn.setEnabled(True)
        self.premarket_status_label.setText(f"❌ 掃描失敗: {error_message}")
        print(f"❌ 掃描失敗: {error_message}")
    
    def update_market_dashboard(self, results):
        """更新市場儀表板 - 複製主程式的邏輯"""
        try:
            print(f"🔍 DEBUG: update_market_dashboard 被調用")
            
            # 檢查組件是否存在
            if not hasattr(self, 'market_sentiment_label'):
                print(f"🔍 DEBUG: market_sentiment_label 不存在，退出")
                return
            
            print(f"🔍 DEBUG: market_sentiment_label 存在，繼續更新")

            # 🛡️ 智能判斷結果格式並獲取市場情緒
            sentiment = "中性"
            if results and isinstance(results, dict):
                # 優化掃描器格式
                if 'market_summary' in results:
                    sentiment = results['market_summary'].get('market_sentiment', '中性')
                # 傳統監控器格式
                elif hasattr(self, 'pre_market_monitor') and self.pre_market_monitor:
                    try:
                        sentiment = self.pre_market_monitor.get_market_sentiment()
                    except:
                        sentiment = "中性"

            print(f"🔍 DEBUG: 市場情緒: {sentiment}")
            self.market_sentiment_label.setText(f"📈 {sentiment}")

            # 🛡️ 更新美股狀態
            us_data = results.get('us_indices', {}) or results.get('market_indices', {})
            if us_data:
                print(f"🔍 DEBUG: 美股數據: {len(us_data)} 項")
                us_changes = []
                # 處理開盤前監控器格式
                if any(isinstance(item, dict) and 'change_pct' in item for item in us_data.values()):
                    us_changes = [data.get('change_pct', 0) for data in us_data.values()
                                if isinstance(data, dict) and 'change_pct' in data]

                if us_changes:
                    avg_change = sum(us_changes) / len(us_changes)
                    us_trend = "📈" if avg_change > 0 else "📉" if avg_change < 0 else "➡️"
                    
                    print(f"🔍 DEBUG: 美股平均變化: {avg_change:+.2f}% {us_trend}")
                    self.us_status_label.setText(f"🇺🇸 美股：{us_trend}{avg_change:+.2f}%")
            else:
                print(f"🔍 DEBUG: 沒有美股數據")

            # 🛡️ 更新台指期狀態
            tw_data = results.get('taiwan_futures', {})
            if tw_data:
                print(f"🔍 DEBUG: 台指期數據: {len(tw_data)} 項")
                tw_index = tw_data.get('台股加權指數', {})
                if tw_index and isinstance(tw_index, dict):
                    price = tw_index.get('price', 0)
                    change_pct = tw_index.get('change_pct', 0)
                    tw_trend = "📈" if change_pct > 0 else "📉" if change_pct < 0 else "➡️"
                    
                    print(f"🔍 DEBUG: 台指期: {price} ({change_pct:+.2f}%) {tw_trend}")
                    self.tw_status_label.setText(f"🇹🇼 台指期：{tw_trend}{price} ({change_pct:+.2f}%)")
            else:
                print(f"🔍 DEBUG: 沒有台指期數據")

            print(f"🔍 DEBUG: update_market_dashboard 完成")

        except Exception as e:
            print(f"❌ update_market_dashboard 失敗: {e}")
    
    def update_market_details(self, results):
        """更新市場詳細數據"""
        try:
            print(f"🔍 DEBUG: update_market_details 被調用")
            
            if not results:
                print(f"🔍 DEBUG: 沒有結果數據")
                return
            
            # 統計數據
            total_items = 0
            categories = []
            
            for category, data in results.items():
                if category not in ['timestamp'] and isinstance(data, dict):
                    item_count = len(data)
                    total_items += item_count
                    categories.append(f"{category}:{item_count}")
            
            summary = f"📊 掃描完成: {total_items} 項數據 ({', '.join(categories)})"
            print(f"🔍 DEBUG: 數據摘要: {summary}")
            self.premarket_status_label.setText(summary)
            
            print(f"🔍 DEBUG: update_market_details 完成")
            
        except Exception as e:
            print(f"❌ update_market_details 失敗: {e}")

def main():
    """主函數"""
    print("🧪 市場掃描界面調試測試")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # 創建測試窗口
    window = TestMainWindow()
    window.show()
    
    print("🖥️ 測試窗口已顯示")
    print("💡 點擊「開始測試掃描」按鈕來測試界面更新")
    print("📋 觀察控制台輸出的調試信息")
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
