#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷 PE 資料中的空值問題
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'finlab'))

def diagnose_pe_null_issue():
    """診斷 PE 資料中的空值問題"""
    print("=" * 80)
    print("🔍 診斷 PE 資料中的空值問題")
    print("=" * 80)
    
    try:
        # 檢查資料庫中的空值情況
        pe_db_path = 'D:/Finlab/history/tables/pe.db'
        
        if not os.path.exists(pe_db_path):
            print("❌ pe.db 不存在")
            return
        
        print("📊 檢查資料庫中的空值情況...")
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 檢查總記錄數
        cursor.execute("SELECT COUNT(*) FROM pe_data")
        total_count = cursor.fetchone()[0]
        print(f"   總記錄數: {total_count:,}")
        
        # 檢查 stock_id 空值
        cursor.execute("SELECT COUNT(*) FROM pe_data WHERE stock_id IS NULL OR stock_id = ''")
        null_stock_id = cursor.fetchone()[0]
        print(f"   stock_id 為空的記錄: {null_stock_id:,}")
        
        # 檢查 date 空值
        cursor.execute("SELECT COUNT(*) FROM pe_data WHERE date IS NULL OR date = ''")
        null_date = cursor.fetchone()[0]
        print(f"   date 為空的記錄: {null_date:,}")
        
        # 檢查 stock_name 空值
        cursor.execute("SELECT COUNT(*) FROM pe_data WHERE stock_name IS NULL OR stock_name = ''")
        null_stock_name = cursor.fetchone()[0]
        print(f"   stock_name 為空的記錄: {null_stock_name:,}")
        
        # 如果有空值記錄，顯示範例
        if null_stock_id > 0:
            print(f"\n📋 stock_id 為空的記錄範例:")
            cursor.execute("SELECT * FROM pe_data WHERE stock_id IS NULL OR stock_id = '' LIMIT 5")
            null_examples = cursor.fetchall()
            for row in null_examples:
                print(f"   {row}")
        
        if null_date > 0:
            print(f"\n📋 date 為空的記錄範例:")
            cursor.execute("SELECT * FROM pe_data WHERE date IS NULL OR date = '' LIMIT 5")
            null_examples = cursor.fetchall()
            for row in null_examples:
                print(f"   {row}")
        
        conn.close()
        
        # 檢查爬蟲返回的資料
        print(f"\n🧪 檢查爬蟲返回的資料...")
        from crawler import crawl_pe
        
        # 測試爬取一天的資料
        test_date = datetime(2024, 12, 30)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        df = crawl_pe(test_date)
        
        if not df.empty:
            print(f"✅ 成功爬取 {len(df)} 筆 PE 資料")
            
            # 重置索引檢查
            df_reset = df.reset_index()
            print(f"\n📊 重置索引後的欄位: {list(df_reset.columns)}")
            
            # 檢查空值
            print(f"\n📊 爬蟲資料空值檢查:")
            for col in df_reset.columns:
                null_count = df_reset[col].isnull().sum()
                empty_count = (df_reset[col] == '').sum() if df_reset[col].dtype == 'object' else 0
                print(f"   {col}: NULL={null_count}, 空字串={empty_count}")
            
            # 檢查 stock_id 的格式
            print(f"\n📊 stock_id 格式檢查:")
            stock_id_samples = df_reset['stock_id'].head(10).tolist()
            for i, stock_id in enumerate(stock_id_samples):
                print(f"   [{i}] '{stock_id}' (type: {type(stock_id)})")
            
            # 檢查是否有包含空格的 stock_id
            has_space = df_reset['stock_id'].str.contains(' ', na=False).sum()
            print(f"   包含空格的 stock_id: {has_space}")
            
            # 模擬處理過程
            print(f"\n🔧 模擬處理過程...")
            
            # 統一欄位名稱
            column_mapping = {
                '殖利率(%)': 'dividend_yield',
                '本益比': 'pe_ratio', 
                '股利年度': 'dividend_year',
                '股價淨值比': 'pb_ratio'
            }
            df_reset = df_reset.rename(columns=column_mapping)
            
            # 處理股票名稱分離
            if 'stock_id' in df_reset.columns:
                def extract_stock_name(stock_id_str):
                    if not stock_id_str or pd.isna(stock_id_str):
                        return ''
                    stock_str = str(stock_id_str).strip()
                    if ' ' in stock_str:
                        parts = stock_str.split(' ', 1)
                        return parts[1] if len(parts) > 1 else ''
                    return ''
                
                # 提取股票名稱
                df_reset['stock_name'] = df_reset['stock_id'].apply(extract_stock_name)
                
                # 更新 stock_id 為純代碼
                df_reset['stock_id'] = df_reset['stock_id'].apply(lambda x: str(x).split(' ')[0] if x and not pd.isna(x) else x)
            
            print(f"📊 處理後的空值檢查:")
            for col in ['stock_id', 'stock_name', 'date']:
                if col in df_reset.columns:
                    null_count = df_reset[col].isnull().sum()
                    empty_count = (df_reset[col] == '').sum() if df_reset[col].dtype == 'object' else 0
                    print(f"   {col}: NULL={null_count}, 空字串={empty_count}")
            
            # 檢查處理後的 stock_id 範例
            print(f"\n📊 處理後的 stock_id 範例:")
            processed_samples = df_reset[['stock_id', 'stock_name']].head(10)
            for _, row in processed_samples.iterrows():
                print(f"   stock_id='{row['stock_id']}', stock_name='{row['stock_name']}'")
        
        else:
            print("⚠️ 爬取資料為空")
        
    except Exception as e:
        print(f"❌ 診斷過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def clean_pe_database():
    """清理 PE 資料庫中的空值記錄"""
    print("\n" + "=" * 80)
    print("🧹 清理 PE 資料庫中的空值記錄")
    print("=" * 80)
    
    pe_db_path = 'D:/Finlab/history/tables/pe.db'
    
    if not os.path.exists(pe_db_path):
        print("❌ pe.db 不存在")
        return
    
    try:
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 檢查清理前的記錄數
        cursor.execute("SELECT COUNT(*) FROM pe_data")
        before_count = cursor.fetchone()[0]
        
        # 檢查空值記錄數
        cursor.execute("SELECT COUNT(*) FROM pe_data WHERE stock_id IS NULL OR stock_id = '' OR date IS NULL OR date = ''")
        null_count = cursor.fetchone()[0]
        
        print(f"📊 清理前統計:")
        print(f"   總記錄數: {before_count:,}")
        print(f"   空值記錄數: {null_count:,}")
        
        if null_count == 0:
            print("✅ 沒有需要清理的空值記錄")
            conn.close()
            return
        
        # 確認清理
        response = input(f"\n確定要刪除 {null_count:,} 筆空值記錄嗎? (y/n): ")
        if response.lower() != 'y':
            print("❌ 取消清理")
            conn.close()
            return
        
        # 刪除空值記錄
        cursor.execute("DELETE FROM pe_data WHERE stock_id IS NULL OR stock_id = '' OR date IS NULL OR date = ''")
        deleted_count = cursor.rowcount
        
        # 提交變更
        conn.commit()
        
        # 檢查清理後的記錄數
        cursor.execute("SELECT COUNT(*) FROM pe_data")
        after_count = cursor.fetchone()[0]
        
        print(f"\n📊 清理後統計:")
        print(f"   刪除記錄數: {deleted_count:,}")
        print(f"   剩餘記錄數: {after_count:,}")
        
        # 優化資料庫
        print(f"🔧 優化資料庫...")
        cursor.execute("VACUUM")
        
        conn.close()
        
        print(f"✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 清理過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_pe_null_issue()
    
    # 詢問是否要清理
    response = input("\n是否要清理資料庫中的空值記錄? (y/n): ")
    if response.lower() == 'y':
        clean_pe_database()
