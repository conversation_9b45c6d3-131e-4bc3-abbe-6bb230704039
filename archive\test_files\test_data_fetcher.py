#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
測試多數據源獲取器
測試 yfinance 和 twstock 的數據獲取功能
"""
import logging
import sys
import os

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_yfinance():
    """測試 yfinance 數據獲取"""
    print("=" * 60)
    print("🔥 測試 yfinance 數據獲取")
    print("=" * 60)
    
    try:
        import yfinance as yf
        print("✅ yfinance 模組已安裝")
        
        # 測試獲取台積電數據
        test_stocks = ['2330.TW', '2317.TW', '2454.TW']
        
        for stock in test_stocks:
            print(f"\n📊 測試股票: {stock}")
            try:
                df = yf.download(stock, period='1mo', interval='1d', progress=False)
                if not df.empty:
                    print(f"✅ 成功獲取 {len(df)} 條記錄")
                    print(f"   最新收盤價: {df['Close'].iloc[-1]:.2f}")
                    print(f"   日期範圍: {df.index[0].date()} 到 {df.index[-1].date()}")
                else:
                    print(f"⚠️ 未獲取到數據")
            except Exception as e:
                print(f"❌ 獲取失敗: {e}")
                
    except ImportError:
        print("❌ yfinance 模組未安裝")
        print("   請執行: pip install yfinance")

def test_twstock():
    """測試 twstock 數據獲取"""
    print("\n" + "=" * 60)
    print("🔥 測試 twstock 數據獲取")
    print("=" * 60)
    
    try:
        import twstock
        print("✅ twstock 模組已安裝")
        
        # 測試獲取股票數據
        test_stocks = ['2330', '2317', '2454']
        
        for stock_id in test_stocks:
            print(f"\n📊 測試股票: {stock_id}")
            try:
                stock = twstock.Stock(stock_id)
                
                # 獲取最近30天數據
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=60)
                
                data = stock.fetch_from(start_date.year, start_date.month)
                
                if data:
                    print(f"✅ 成功獲取 {len(data)} 條記錄")
                    latest = data[-1]
                    print(f"   最新收盤價: {latest.close}")
                    print(f"   日期: {latest.date}")
                    print(f"   成交量: {latest.capacity}")
                else:
                    print(f"⚠️ 未獲取到數據")
                    
            except Exception as e:
                print(f"❌ 獲取失敗: {e}")
        
        # 測試即時數據
        print(f"\n📡 測試即時數據")
        try:
            real_time_data = twstock.realtime.get(['2330', '2317'])
            if real_time_data and real_time_data.get('success'):
                print("✅ 即時數據獲取成功")
                for stock_info in real_time_data.get('msgArray', []):
                    stock_id = stock_info.get('c', '')
                    price = stock_info.get('z', 0)
                    print(f"   {stock_id}: {price}")
            else:
                print("⚠️ 即時數據獲取失敗")
        except Exception as e:
            print(f"❌ 即時數據獲取失敗: {e}")
                
    except ImportError:
        print("❌ twstock 模組未安裝")
        print("   請執行: pip install twstock")

def test_enhanced_data_fetcher():
    """測試增強版數據獲取器"""
    print("\n" + "=" * 60)
    print("🔥 測試增強版數據獲取器")
    print("=" * 60)
    
    try:
        # 導入增強版數據獲取器
        from O3mh_gui_v22_enhanced import EnhancedDataFetcher
        
        fetcher = EnhancedDataFetcher()
        test_stocks = ['2330', '2317', '2454']
        
        for stock_id in test_stocks:
            print(f"\n📊 測試股票: {stock_id}")
            
            # 測試各種數據源
            print("   📈 測試 yfinance:")
            df_yf = fetcher.fetch_stock_data_yfinance(stock_id, 30)
            if not df_yf.empty:
                print(f"      ✅ 成功獲取 {len(df_yf)} 條記錄")
            else:
                print(f"      ⚠️ 未獲取到數據")
            
            print("   📉 測試 twstock:")
            df_tw = fetcher.fetch_stock_data_twstock(stock_id, 30)
            if not df_tw.empty:
                print(f"      ✅ 成功獲取 {len(df_tw)} 條記錄")
            else:
                print(f"      ⚠️ 未獲取到數據")
            
            print("   🔄 測試多數據源備援:")
            df_fallback = fetcher.fetch_stock_data_with_fallback(
                stock_id, 30, None, preferred_source='yfinance'
            )
            if not df_fallback.empty:
                print(f"      ✅ 備援機制成功，獲取 {len(df_fallback)} 條記錄")
                print(f"      最新收盤價: {df_fallback['Close'].iloc[-1]:.2f}")
            else:
                print(f"      ❌ 所有數據源都失敗")
                
    except Exception as e:
        print(f"❌ 測試增強版數據獲取器失敗: {e}")
        import traceback
        traceback.print_exc()

def install_missing_packages():
    """安裝缺失的套件"""
    print("\n" + "=" * 60)
    print("🔧 檢查並安裝缺失的套件")
    print("=" * 60)
    
    import subprocess
    
    packages = [
        ('yfinance', 'yfinance'),
        ('twstock', 'twstock'),
        ('pandas', 'pandas'),
        ('requests', 'requests')
    ]
    
    for package_name, import_name in packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安裝")
        except ImportError:
            print(f"⚠️ {package_name} 未安裝，正在安裝...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
                print(f"✅ {package_name} 安裝成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package_name} 安裝失敗: {e}")

def main():
    """主函數"""
    print("🚀 多數據源獲取器測試工具")
    print("測試 yfinance 和 twstock 的數據獲取功能")
    
    # 檢查並安裝套件
    install_missing_packages()
    
    # 執行各項測試
    test_yfinance()
    test_twstock()
    test_enhanced_data_fetcher()
    
    print("\n" + "=" * 60)
    print("🎯 測試完成！")
    print("如果有錯誤，請檢查網路連接和套件安裝")
    print("=" * 60)

if __name__ == "__main__":
    main() 