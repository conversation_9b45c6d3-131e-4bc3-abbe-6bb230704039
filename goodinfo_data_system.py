#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoodInfo 資料系統架構
整合多種財務資料爬取和除權息策略分析
"""

import sqlite3
import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import random
from datetime import datetime, timedelta
import urllib.parse
import os

class GoodInfoDataSystem:
    def __init__(self, db_path="goodinfo_data.db"):
        """
        初始化 GoodInfo 資料系統
        
        Args:
            db_path: 資料庫路徑
        """
        self.db_path = db_path
        self.session = requests.Session()
        self.setup_session()
        self.init_database()
    
    def setup_session(self):
        """設定爬蟲 session"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
        
        # 初始化 cookies
        try:
            response = self.session.get("https://goodinfo.tw/tw/index.asp", timeout=30)
            basic_cookies = {
                'IS_TOUCH_DEVICE': 'F',
                'SCREEN_SIZE': 'WIDTH=1920&HEIGHT=1080',
                'CLIENT_ID': f'{datetime.now().strftime("%Y%m%d%H%M%S")}_{random.randint(100,999)}',
            }
            for name, value in basic_cookies.items():
                self.session.cookies.set(name, value)
        except Exception as e:
            print(f"⚠️ Session 初始化警告: {e}")
    
    def init_database(self):
        """初始化資料庫結構"""
        print("🗄️ 初始化資料庫...")
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 除權息時間表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dividend_schedule (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    market TEXT,                    -- 市場別 (上市/上櫃)
                    stock_code TEXT,               -- 股票代號
                    stock_name TEXT,               -- 股票名稱
                    shareholder_meeting_date TEXT, -- 股東會日期
                    ex_dividend_date TEXT,         -- 除息交易日
                    ex_right_date TEXT,            -- 除權交易日
                    cash_dividend REAL,            -- 現金股利
                    stock_dividend REAL,           -- 股票股利
                    total_dividend REAL,           -- 股利合計
                    dividend_yield REAL,           -- 現金殖利率
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, ex_dividend_date, ex_right_date)
                )
            ''')
            
            # 個股基本資料
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_basic_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE,
                    stock_name TEXT,
                    market TEXT,                   -- 市場別
                    industry TEXT,                 -- 產業別
                    market_cap REAL,              -- 市值
                    shares_outstanding REAL,       -- 流通股數
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 財務績效資料
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS financial_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT,
                    year_quarter TEXT,             -- 年度季別
                    revenue REAL,                  -- 營業收入
                    gross_profit REAL,             -- 營業毛利
                    operating_profit REAL,         -- 營業利益
                    net_profit REAL,               -- 稅後淨利
                    eps REAL,                      -- 每股盈餘
                    roe REAL,                      -- ROE
                    roa REAL,                      -- ROA
                    gross_margin REAL,             -- 毛利率
                    operating_margin REAL,         -- 營業利益率
                    net_margin REAL,               -- 淨利率
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, year_quarter)
                )
            ''')
            
            # 股價資料
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_price (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT,
                    date TEXT,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume INTEGER,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            ''')
            
            # 除權息策略分析
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dividend_strategy (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT,
                    ex_date TEXT,                  -- 除權息日
                    strategy_type TEXT,            -- 策略類型
                    recommendation TEXT,           -- 建議 (參加/不參加)
                    reason TEXT,                   -- 理由
                    expected_return REAL,          -- 預期報酬率
                    risk_level TEXT,               -- 風險等級
                    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, ex_date, strategy_type)
                )
            ''')
            
            conn.commit()
        
        print("✅ 資料庫初始化完成")
    
    def crawl_data_by_category(self, category, stock_code=None):
        """
        根據類別爬取資料
        
        Args:
            category: 資料類別
            stock_code: 股票代號 (可選)
        """
        category_mapping = {
            # 個股市況
            'basic_info': 'StockInfo/StockDetail.asp',
            'news': 'StockInfo/StockNews.asp',
            'performance': 'StockInfo/StockBzPerformance.asp',
            'financial_status': 'StockInfo/StockFinanceAnalysis.asp',
            'cash_flow': 'StockInfo/StockCashFlow.asp',
            'monthly_revenue': 'StockInfo/StockSalesMon.asp',
            'product_revenue': 'StockInfo/StockSalesProduct.asp',
            
            # 股東資訊
            'shareholder_meeting': 'StockInfo/StockShareholdersMeeting.asp',
            'dividend_policy': 'StockInfo/StockDividendPolicy.asp',
            'ex_dividend_schedule': 'StockInfo/StockDividendSchedule.asp',
            'capital_increase': 'StockInfo/StockCapitalIncrease.asp',
            'employee_bonus': 'StockInfo/StockEmployeeBonus.asp',
            
            # 財務報表
            'financial_report': 'StockInfo/StockFinanceAnalysis.asp',
            'balance_sheet': 'StockInfo/StockBalanceSheet.asp',
            'income_statement': 'StockInfo/StockIncomeStatement.asp',
            'cash_flow_statement': 'StockInfo/StockCashFlowStatement.asp',
            
            # 技術分析
            'k_chart': 'StockInfo/StockChart.asp',
            'technical_analysis': 'StockInfo/StockTechnicalAnalysis.asp',
        }
        
        if category not in category_mapping:
            print(f"❌ 不支援的資料類別: {category}")
            return None
        
        if not stock_code:
            print(f"❌ 需要提供股票代號")
            return None
        
        url = f"https://goodinfo.tw/{category_mapping[category]}?STOCK_ID={stock_code}"
        
        try:
            print(f"🕷️ 爬取 {stock_code} 的 {category} 資料...")
            
            # 隨機延遲
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                # 解析資料
                soup = BeautifulSoup(response.text, 'html.parser')
                tables = pd.read_html(response.text)
                
                print(f"✅ 成功獲取 {len(tables)} 個表格")
                return {
                    'tables': tables,
                    'soup': soup,
                    'raw_html': response.text
                }
            else:
                print(f"❌ 請求失敗，狀態碼: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 爬取失敗: {e}")
            return None
    
    def update_dividend_schedule(self):
        """更新除權息時間表"""
        print("📊 更新除權息時間表...")
        
        url = "https://goodinfo.tw/tw/StockDividendScheduleList.asp"
        params = {
            'MARKET_CAT': '全部',
            'INDUSTRY_CAT': '全部', 
            'YEAR': '即將除權息'
        }
        
        full_url = f"{url}?" + urllib.parse.urlencode(params, encoding='utf-8')
        
        try:
            time.sleep(random.uniform(1, 3))
            response = self.session.get(full_url, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                tables = pd.read_html(response.text)
                
                if tables:
                    # 找到主要資料表格
                    main_table = max(tables, key=lambda x: x.shape[0] * x.shape[1])
                    
                    # 清理和標準化資料
                    cleaned_data = self.clean_dividend_schedule_data(main_table)
                    
                    # 儲存到資料庫
                    self.save_dividend_schedule(cleaned_data)
                    
                    print(f"✅ 成功更新 {len(cleaned_data)} 筆除權息資料")
                    return cleaned_data
                else:
                    print("❌ 未找到除權息資料表格")
                    return None
            else:
                print(f"❌ 請求失敗，狀態碼: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 更新除權息時間表失敗: {e}")
            return None
    
    def clean_dividend_schedule_data(self, df):
        """清理除權息時間表資料"""
        print("🧹 清理除權息資料...")
        
        cleaned_data = []
        
        try:
            # 跳過標題行，處理實際資料
            for i, row in df.iterrows():
                if i < 3:  # 跳過前3行標題
                    continue
                
                try:
                    # 提取基本資訊
                    market = str(row.iloc[0]) if len(row) > 0 else ""
                    stock_code = str(row.iloc[1]) if len(row) > 1 else ""
                    stock_name = str(row.iloc[2]) if len(row) > 2 else ""
                    
                    # 跳過無效資料
                    if not stock_code or stock_code == 'nan' or len(stock_code) < 4:
                        continue
                    
                    # 提取除權息資訊
                    ex_dividend_date = str(row.iloc[4]) if len(row) > 4 else ""
                    ex_right_date = str(row.iloc[9]) if len(row) > 9 else ""
                    
                    # 提取股利資訊
                    cash_dividend = 0.0
                    stock_dividend = 0.0
                    
                    try:
                        if len(row) > 13:
                            cash_dividend = float(str(row.iloc[13]).replace(',', '')) if str(row.iloc[13]) != 'nan' else 0.0
                        if len(row) > 16:
                            stock_dividend = float(str(row.iloc[16]).replace(',', '')) if str(row.iloc[16]) != 'nan' else 0.0
                    except:
                        pass
                    
                    total_dividend = cash_dividend + stock_dividend
                    
                    cleaned_record = {
                        'market': market,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'ex_dividend_date': ex_dividend_date,
                        'ex_right_date': ex_right_date,
                        'cash_dividend': cash_dividend,
                        'stock_dividend': stock_dividend,
                        'total_dividend': total_dividend
                    }
                    
                    cleaned_data.append(cleaned_record)
                    
                except Exception as e:
                    print(f"⚠️ 處理第 {i} 行資料時發生錯誤: {e}")
                    continue
            
            print(f"✅ 清理完成，有效資料 {len(cleaned_data)} 筆")
            return cleaned_data
            
        except Exception as e:
            print(f"❌ 資料清理失敗: {e}")
            return []
    
    def save_dividend_schedule(self, data):
        """儲存除權息時間表到資料庫"""
        if not data:
            return
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for record in data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO dividend_schedule 
                        (market, stock_code, stock_name, ex_dividend_date, ex_right_date, 
                         cash_dividend, stock_dividend, total_dividend)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['market'],
                        record['stock_code'],
                        record['stock_name'],
                        record['ex_dividend_date'],
                        record['ex_right_date'],
                        record['cash_dividend'],
                        record['stock_dividend'],
                        record['total_dividend']
                    ))
                except Exception as e:
                    print(f"⚠️ 儲存資料失敗: {e}")
            
            conn.commit()

    def analyze_dividend_strategy(self, days_ahead=14):
        """
        分析未來指定天數內的除權息策略

        Args:
            days_ahead: 分析未來幾天的除權息 (預設14天)

        Returns:
            list: 策略分析結果
        """
        print(f"📈 分析未來 {days_ahead} 天的除權息策略...")

        # 獲取未來除權息股票
        upcoming_dividends = self.get_upcoming_dividends(days_ahead)

        if not upcoming_dividends:
            print("❌ 未找到即將除權息的股票")
            return []

        strategies = []

        for dividend in upcoming_dividends:
            try:
                strategy = self.evaluate_dividend_participation(dividend)
                if strategy:
                    strategies.append(strategy)
            except Exception as e:
                print(f"⚠️ 分析 {dividend['stock_code']} 策略時發生錯誤: {e}")

        # 儲存策略分析結果
        self.save_dividend_strategies(strategies)

        print(f"✅ 完成 {len(strategies)} 檔股票的策略分析")
        return strategies

    def get_upcoming_dividends(self, days_ahead):
        """獲取即將除權息的股票"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 查詢即將除權息的股票
            cursor.execute('''
                SELECT * FROM dividend_schedule
                WHERE ex_dividend_date LIKE '%即將%'
                   OR ex_right_date LIKE '%即將%'
                   OR (cash_dividend > 0 OR stock_dividend > 0)
                ORDER BY total_dividend DESC
            ''')

            results = cursor.fetchall()
            columns = [description[0] for description in cursor.description]

            dividends = []
            for row in results:
                dividend_dict = dict(zip(columns, row))
                dividends.append(dividend_dict)

            return dividends

    def evaluate_dividend_participation(self, dividend_info):
        """
        評估是否參加除權息

        Args:
            dividend_info: 除權息資訊

        Returns:
            dict: 策略分析結果
        """
        stock_code = dividend_info['stock_code']
        stock_name = dividend_info['stock_name']
        cash_dividend = dividend_info['cash_dividend']
        stock_dividend = dividend_info['stock_dividend']
        total_dividend = dividend_info['total_dividend']

        print(f"🔍 分析 {stock_code} {stock_name} 除權息策略...")

        # 基本策略評估標準
        strategy = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'ex_date': dividend_info.get('ex_dividend_date', ''),
            'cash_dividend': cash_dividend,
            'stock_dividend': stock_dividend,
            'total_dividend': total_dividend,
            'recommendation': 'HOLD',  # 預設建議
            'strategy_type': 'BASIC',
            'reason': [],
            'expected_return': 0.0,
            'risk_level': 'MEDIUM',
            'score': 0
        }

        # 評估標準1: 現金殖利率
        if cash_dividend > 0:
            if cash_dividend >= 3.0:  # 現金股利 >= 3元
                strategy['score'] += 30
                strategy['reason'].append(f"高現金股利 {cash_dividend}元")
            elif cash_dividend >= 1.0:
                strategy['score'] += 15
                strategy['reason'].append(f"中等現金股利 {cash_dividend}元")
            else:
                strategy['score'] += 5
                strategy['reason'].append(f"低現金股利 {cash_dividend}元")

        # 評估標準2: 股票股利
        if stock_dividend > 0:
            if stock_dividend >= 1.0:
                strategy['score'] += 20
                strategy['reason'].append(f"股票股利 {stock_dividend}元")
            else:
                strategy['score'] += 10
                strategy['reason'].append(f"少量股票股利 {stock_dividend}元")

        # 評估標準3: 總股利
        if total_dividend >= 5.0:
            strategy['score'] += 25
            strategy['reason'].append(f"高總股利 {total_dividend}元")
        elif total_dividend >= 2.0:
            strategy['score'] += 15
            strategy['reason'].append(f"中等總股利 {total_dividend}元")

        # 評估標準4: 股票類型判斷
        if stock_code.startswith('00'):  # ETF
            strategy['score'] += 10
            strategy['reason'].append("ETF相對穩定")
            strategy['risk_level'] = 'LOW'
        elif stock_code in ['2330', '2317', '2454', '2382']:  # 大型權值股
            strategy['score'] += 15
            strategy['reason'].append("大型權值股")
            strategy['risk_level'] = 'LOW'

        # 決定最終建議
        if strategy['score'] >= 60:
            strategy['recommendation'] = 'BUY'
            strategy['strategy_type'] = 'AGGRESSIVE'
            strategy['expected_return'] = total_dividend * 0.8  # 預期80%填息機率
        elif strategy['score'] >= 40:
            strategy['recommendation'] = 'HOLD'
            strategy['strategy_type'] = 'MODERATE'
            strategy['expected_return'] = total_dividend * 0.6
        else:
            strategy['recommendation'] = 'AVOID'
            strategy['strategy_type'] = 'CONSERVATIVE'
            strategy['expected_return'] = total_dividend * 0.3

        # 風險評估
        if cash_dividend < 1.0 and stock_dividend > 2.0:
            strategy['risk_level'] = 'HIGH'
            strategy['reason'].append("股票股利比例過高")

        strategy['reason'] = '; '.join(strategy['reason'])

        return strategy

    def save_dividend_strategies(self, strategies):
        """儲存策略分析結果"""
        if not strategies:
            return

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for strategy in strategies:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO dividend_strategy
                        (stock_code, ex_date, strategy_type, recommendation,
                         reason, expected_return, risk_level)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        strategy['stock_code'],
                        strategy['ex_date'],
                        strategy['strategy_type'],
                        strategy['recommendation'],
                        strategy['reason'],
                        strategy['expected_return'],
                        strategy['risk_level']
                    ))
                except Exception as e:
                    print(f"⚠️ 儲存策略失敗: {e}")

            conn.commit()

    def get_dividend_recommendations(self, recommendation_type='ALL'):
        """
        獲取除權息建議

        Args:
            recommendation_type: 建議類型 ('BUY', 'HOLD', 'AVOID', 'ALL')

        Returns:
            list: 建議清單
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if recommendation_type == 'ALL':
                cursor.execute('''
                    SELECT ds.*, s.stock_name, s.cash_dividend, s.total_dividend
                    FROM dividend_strategy ds
                    LEFT JOIN dividend_schedule s ON ds.stock_code = s.stock_code
                    ORDER BY ds.expected_return DESC
                ''')
            else:
                cursor.execute('''
                    SELECT ds.*, s.stock_name, s.cash_dividend, s.total_dividend
                    FROM dividend_strategy ds
                    LEFT JOIN dividend_schedule s ON ds.stock_code = s.stock_code
                    WHERE ds.recommendation = ?
                    ORDER BY ds.expected_return DESC
                ''', (recommendation_type,))

            results = cursor.fetchall()
            columns = [description[0] for description in cursor.description]

            recommendations = []
            for row in results:
                rec_dict = dict(zip(columns, row))
                recommendations.append(rec_dict)

            return recommendations

def test_goodinfo_system():
    """測試 GoodInfo 資料系統"""
    print("🚀 測試 GoodInfo 資料系統...")
    
    system = GoodInfoDataSystem()
    
    # 測試1: 更新除權息時間表
    print("\n" + "="*60)
    print("📊 測試1: 更新除權息時間表")
    dividend_data = system.update_dividend_schedule()
    
    if dividend_data:
        print(f"✅ 成功獲取 {len(dividend_data)} 筆除權息資料")
        
        # 顯示前5筆
        print("\n前5筆資料:")
        for i, record in enumerate(dividend_data[:5]):
            print(f"{i+1}. {record['stock_code']} {record['stock_name']} - 現金股利: {record['cash_dividend']}")
    
    # 測試2: 除權息策略分析
    print("\n" + "="*60)
    print("📊 測試2: 除權息策略分析")

    strategies = system.analyze_dividend_strategy(days_ahead=14)

    if strategies:
        print(f"✅ 完成 {len(strategies)} 檔股票的策略分析")

        # 顯示建議買入的股票
        buy_recommendations = [s for s in strategies if s['recommendation'] == 'BUY']
        if buy_recommendations:
            print(f"\n🎯 建議參加除權息的股票 ({len(buy_recommendations)} 檔):")
            for i, rec in enumerate(buy_recommendations[:5]):
                print(f"{i+1}. {rec['stock_code']} {rec['stock_name']}")
                print(f"   現金股利: {rec['cash_dividend']}元, 預期報酬: {rec['expected_return']:.2f}元")
                print(f"   理由: {rec['reason']}")
                print()

        # 顯示風險較高的股票
        high_risk = [s for s in strategies if s['risk_level'] == 'HIGH']
        if high_risk:
            print(f"⚠️ 高風險股票 ({len(high_risk)} 檔):")
            for rec in high_risk[:3]:
                print(f"- {rec['stock_code']} {rec['stock_name']}: {rec['reason']}")

    # 測試3: 獲取建議清單
    print("\n" + "="*60)
    print("📊 測試3: 獲取除權息建議")

    buy_recs = system.get_dividend_recommendations('BUY')
    if buy_recs:
        print(f"✅ 找到 {len(buy_recs)} 檔建議買入的股票")

        print("\n📈 前5檔建議:")
        for i, rec in enumerate(buy_recs[:5]):
            print(f"{i+1}. {rec['stock_code']} - 預期報酬: {rec['expected_return']:.2f}元")

    # 測試4: 爬取個股基本資料 (簡化版)
    print("\n" + "="*60)
    print("📊 測試4: 爬取個股基本資料")

    test_stocks = ['2330']  # 只測試一檔以節省時間
    for stock_code in test_stocks:
        basic_data = system.crawl_data_by_category('basic_info', stock_code)
        if basic_data:
            print(f"✅ 成功獲取 {stock_code} 基本資料，包含 {len(basic_data['tables'])} 個表格")
        time.sleep(2)

if __name__ == "__main__":
    test_goodinfo_system()
