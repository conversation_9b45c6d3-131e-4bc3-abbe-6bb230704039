# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有必要的數據文件
datas = [
    ('config', 'config'),
    ('strategies', 'strategies'),
    ('charts', 'charts'),
    ('dialogs', 'dialogs'),
    ('core', 'core'),
    ('data', 'data'),
    ('gui', 'gui'),
    ('monitoring', 'monitoring'),
    ('finlab', 'finlab'),
    ('finlab_analysis', 'finlab_analysis'),
    ('finlab_integration', 'finlab_integration'),
    ('*.json', '.'),
    ('*.yaml', '.'),
    ('*.txt', '.'),
]

# 隱藏導入的模組
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'pyqtgraph',
    'pandas',
    'numpy',
    'sqlite3',
    'requests',
    'beautifulsoup4',
    'selenium',
    'webdriver_manager',
    'yfinance',
    'matplotlib',
    'seaborn',
    'openpyxl',
    'xlsxwriter',
    'schedule',
    'tqdm',
    'psutil',
    'cryptography',
    'sqlalchemy',
    'finlab',
    'finlab.data',
    'finlab.backtest',
    'finlab.crawler',
    'stock_filter',
    'enhanced_market_scanner',
    'real_market_data_fetcher',
    'smart_trading_strategies',
    'roe_data_crawler',
    'goodinfo_roe_csv_downloader',
    'market_data_crawler',
    'unified_monitor_manager',
    'auto_update',
    'enhanced_data_fetcher',
    'finmind_data_provider',
    'pe_data_provider',
    'real_data_sources',
    'safe_market_scanner',
    'optimized_market_scanner',
    'intraday_data_fetcher',
    'smart_yahoo_fetcher',
    'twelvedata_fetcher',
    'enhanced_yfinance_fetcher',
    'finmind_quota_manager',
    'stock_name_mapping',
    'strategy_intersection_analyzer',
    'multi_strategy_chart',
    'stock_basic_info_crawler',
    'google_stock_news_gui',
    'google_news_fast',
    'google_real_time_news',
    'improved_roe_downloader',
    'roe_data_downloader_gui',
    'twse_market_data_dialog',
    'market_data_crawler_dialog',
    'unified_monthly_revenue_gui',
    'ashui_backtest_gui',
    'ashui_backtest_system',
    'ai_technical_backtest',
    'multi_strategy_gui',
    'auto_rule_discovery_gui',
    'dividend_trading_gui',
    'dividend_trading_system',
    'u02_crawlers_gui',
    'mops_income_statement_gui',
    'monthly_revenue_downloader_gui',
    'stock_basic_info_gui',
    'finlab_tables_gui',
    'finlab_crawler_gui',
    'daily_trading_crawler_gui',
]

# 排除不需要的模組
excludes = [
    'tkinter',
    'test',
    'unittest',
    'pdb',
    'doctest',
    'difflib',
    'inspect',
    'pydoc',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PySide2',
    'PySide6',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
