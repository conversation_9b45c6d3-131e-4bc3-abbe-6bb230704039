#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def quick_fix():
    """快速修復 stock_info 表問題"""
    
    print("🚀 快速修復 stock_info 表...")
    
    # 基本股票資料
    stocks = {
        "2330": ("台積電", "半導體業"),
        "2317": ("鴻海", "電子業"),
        "2454": ("聯發科", "半導體業"),
        "2412": ("中華電", "電信業"),
        "2881": ("富邦金", "金融業"),
        "2882": ("國泰金", "金融業"),
        "2884": ("玉山金", "金融業"),
        "2885": ("元大金", "金融業"),
        "2308": ("台達電", "電子業"),
        "2382": ("廣達", "電子業"),
        "2303": ("聯電", "半導體業"),
        "2357": ("華碩", "電子業"),
        "3008": ("大立光", "光電業"),
        "2409": ("友達", "光電業"),
        "2327": ("國巨", "電子零組件"),
        "2379": ("瑞昱", "半導體業"),
        "0050": ("元大台灣50", "ETF"),
        "0056": ("元大高股息", "ETF"),
        "0051": ("元大中型100", "ETF"),
        "1101": ("台泥", "水泥業"),
        "1301": ("台塑", "塑膠業"),
        "2002": ("中鋼", "鋼鐵業"),
        "2886": ("兆豐金", "金融業"),
        "2891": ("中信金", "金融業"),
        "2892": ("第一金", "金融業")
    }
    
    try:
        # 連接數據庫
        conn = sqlite3.connect('db/price.db')
        cursor = conn.cursor()
        
        print("📊 檢查現有表格...")
        
        # 檢查是否有其他表格包含股票信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"現有表格: {tables}")
        
        # 嘗試從現有表格提取股票信息
        extracted_stocks = {}
        
        # 檢查 price 表
        if 'price' in tables:
            try:
                cursor.execute("PRAGMA table_info(price)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"price 表欄位: {columns}")
                
                if 'stock_id' in columns and 'stock_name' in columns:
                    cursor.execute("SELECT DISTINCT stock_id, stock_name, industry FROM price LIMIT 50")
                    for row in cursor.fetchall():
                        if row[0] and row[1]:  # 確保不是 NULL
                            extracted_stocks[row[0]] = (row[1], row[2] if row[2] else '科技業')
                    print(f"從 price 表提取 {len(extracted_stocks)} 支股票")
            except Exception as e:
                print(f"price 表處理失敗: {e}")
        
        # 檢查 stock_daily_data 表
        if 'stock_daily_data' in tables and not extracted_stocks:
            try:
                cursor.execute("PRAGMA table_info(stock_daily_data)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"stock_daily_data 表欄位: {columns}")
                
                if 'stock_id' in columns:
                    cursor.execute("SELECT DISTINCT stock_id, industry FROM stock_daily_data LIMIT 50")
                    for row in cursor.fetchall():
                        if row[0]:  # 確保不是 NULL
                            stock_name = stocks.get(row[0], (f"股票{row[0]}", "科技業"))[0]
                            extracted_stocks[row[0]] = (stock_name, row[1] if row[1] else '科技業')
                    print(f"從 stock_daily_data 表提取 {len(extracted_stocks)} 支股票")
            except Exception as e:
                print(f"stock_daily_data 表處理失敗: {e}")
        
        # 如果沒有提取到數據，使用預設股票列表
        if not extracted_stocks:
            extracted_stocks = stocks
            print(f"使用預設股票列表: {len(extracted_stocks)} 支股票")
        
        # 創建 stock_info 表
        print("📋 創建 stock_info 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                stock_id TEXT PRIMARY KEY,
                stock_name TEXT NOT NULL,
                industry TEXT DEFAULT '科技業',
                market TEXT DEFAULT '上市'
            )
        ''')
        
        # 清空並插入數據
        cursor.execute('DELETE FROM stock_info')
        
        insert_count = 0
        for stock_id, (stock_name, industry) in extracted_stocks.items():
            market = '上櫃' if stock_id.startswith('6') or stock_id.startswith('8') else '上市'
            cursor.execute('''
                INSERT INTO stock_info (stock_id, stock_name, industry, market)
                VALUES (?, ?, ?, ?)
            ''', (stock_id, stock_name, industry, market))
            insert_count += 1
        
        conn.commit()
        
        # 驗證結果
        cursor.execute("SELECT COUNT(*) FROM stock_info")
        count = cursor.fetchone()[0]
        
        cursor.execute("SELECT stock_id, stock_name, industry FROM stock_info LIMIT 5")
        samples = cursor.fetchall()
        
        conn.close()
        
        print(f"✅ 成功創建 stock_info 表，插入 {insert_count} 筆資料")
        print("📊 範例資料:")
        for stock_id, stock_name, industry in samples:
            print(f"   {stock_id}: {stock_name} ({industry})")
        
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        return False

if __name__ == "__main__":
    success = quick_fix()
    if success:
        print("\n🎉 修復完成！現在可以重新啟動主程式。")
    else:
        print("\n❌ 修復失敗！")
