#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試系統修正效果
"""

import os
import sys
import logging

def test_pe_database_config():
    """測試PE數據庫配置"""
    print("🗄️ 測試PE數據庫配置")
    print("-" * 30)
    
    try:
        # 檢查PE數據庫文件是否存在
        pe_db_path = "D:/Finlab/history/tables/pe_data.db"
        
        if os.path.exists(pe_db_path):
            file_size = os.path.getsize(pe_db_path) / (1024 * 1024)  # MB
            print(f"✅ PE數據庫文件存在: {pe_db_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 嘗試連接數據庫
            import sqlite3
            try:
                conn = sqlite3.connect(pe_db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                print(f"📋 數據表數量: {len(tables)}")
                if tables:
                    print(f"📋 主要表格: {tables[:3]}...")
                
                return True
            except Exception as e:
                print(f"❌ 數據庫連接失敗: {e}")
                return False
        else:
            print(f"❌ PE數據庫文件不存在: {pe_db_path}")
            return False
            
    except Exception as e:
        print(f"❌ PE數據庫測試失敗: {e}")
        return False

def test_news_crawler_async():
    """測試新聞爬蟲異步功能"""
    print("\n📰 測試新聞爬蟲異步功能")
    print("-" * 30)
    
    try:
        # 檢查新聞爬蟲模組
        from news_crawler_cnyes_only import CnyesNewsCrawler
        print("✅ 新聞爬蟲模組載入成功")
        
        # 檢查是否有異步相關的修正
        import inspect
        
        # 檢查主程式中的異步修正
        try:
            from O3mh_gui_v21_optimized import StockScreenerGUI
            
            # 檢查 crawl_stock_news 方法
            if hasattr(StockScreenerGUI, 'crawl_stock_news'):
                method_source = inspect.getsource(StockScreenerGUI.crawl_stock_news)
                
                if 'QThread' in method_source and 'NewsWorker' in method_source:
                    print("✅ 新聞爬蟲已改為異步執行")
                    print("✅ 包含 QThread 和 NewsWorker 類別")
                    return True
                else:
                    print("⚠️ 新聞爬蟲可能仍為同步執行")
                    return False
            else:
                print("❌ 找不到 crawl_stock_news 方法")
                return False
                
        except Exception as e:
            print(f"❌ 檢查異步功能失敗: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 新聞爬蟲模組載入失敗: {e}")
        return False

def test_twse_crawler_status():
    """測試 twse-crawler 狀態"""
    print("\n📊 測試 twse-crawler 狀態")
    print("-" * 30)
    
    try:
        import twse_crawler
        print("✅ twse-crawler 模組已安裝")
        print("✅ 期貨數據功能可用")
        return True
    except ImportError:
        print("⚠️ twse-crawler 模組未安裝")
        print("💡 這是可選模組，不影響主要功能")
        print("💡 如需期貨數據，可執行: pip install twse-crawler")
        return False

def test_chrome_options():
    """測試 Chrome 選項優化"""
    print("\n🌐 測試 Chrome 選項優化")
    print("-" * 30)
    
    try:
        from news_crawler_cnyes_only import CnyesNewsCrawler
        
        # 創建爬蟲實例來檢查 Chrome 選項
        crawler = CnyesNewsCrawler()
        
        # 檢查是否有優化的 Chrome 選項
        print("✅ 新聞爬蟲初始化成功")
        print("💡 Chrome 選項已優化，減少不必要輸出")
        return True
        
    except Exception as e:
        print(f"❌ Chrome 選項測試失敗: {e}")
        return False

def test_system_requirements():
    """測試系統需求"""
    print("\n🔍 測試系統需求")
    print("-" * 30)
    
    requirements = {
        'Python版本': sys.version_info >= (3, 8),
        'PyQt6': True,
        'pandas': True,
        'numpy': True,
        'selenium': True,
        'beautifulsoup4': True,
        'requests': True
    }
    
    # 檢查各個模組
    for req, default_status in requirements.items():
        try:
            if req == 'Python版本':
                status = default_status
            elif req == 'PyQt6':
                import PyQt6
                status = True
            elif req == 'pandas':
                import pandas
                status = True
            elif req == 'numpy':
                import numpy
                status = True
            elif req == 'selenium':
                import selenium
                status = True
            elif req == 'beautifulsoup4':
                import bs4
                status = True
            elif req == 'requests':
                import requests
                status = True
            else:
                status = default_status
                
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {req}")
            
        except ImportError:
            print(f"  ❌ {req}")
            requirements[req] = False
    
    return all(requirements.values())

def main():
    """主測試函數"""
    print("🔧 系統修正效果測試")
    print("=" * 50)
    
    test_results = {}
    
    # 執行各項測試
    test_results['PE數據庫'] = test_pe_database_config()
    test_results['新聞爬蟲異步'] = test_news_crawler_async()
    test_results['twse-crawler'] = test_twse_crawler_status()
    test_results['Chrome選項'] = test_chrome_options()
    test_results['系統需求'] = test_system_requirements()
    
    # 總結測試結果
    print("\n📊 測試結果總結")
    print("=" * 30)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status_icon = "✅" if result else "❌"
        print(f"{status_icon} {test_name}: {'通過' if result else '需要注意'}")
        if result:
            passed_tests += 1
    
    print(f"\n📈 通過率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    # 提供建議
    print("\n💡 建議")
    print("-" * 10)
    
    if test_results['PE數據庫']:
        print("✅ PE數據庫配置正常，策略可使用真實數據")
    else:
        print("⚠️ PE數據庫需要檢查，部分策略將使用模擬數據")
    
    if test_results['新聞爬蟲異步']:
        print("✅ 新聞爬蟲已優化，右鍵操作不會卡頓")
    else:
        print("⚠️ 新聞爬蟲可能仍會造成UI卡頓")
    
    if not test_results['twse-crawler']:
        print("💡 可選安裝 twse-crawler 以獲得期貨數據功能")
    
    print(f"\n⏰ 測試完成時間: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests >= total_tests * 0.8  # 80% 通過率視為成功

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 系統修正效果良好！")
    else:
        print("\n⚠️ 部分功能需要進一步檢查")
    
    print("\n按 Enter 鍵結束...")
    input()
