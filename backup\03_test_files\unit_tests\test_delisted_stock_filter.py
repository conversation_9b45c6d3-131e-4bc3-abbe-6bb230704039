#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試已下市股票篩選功能
"""

import sys
import logging
from typing import List

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_delisted_stock_filter():
    """測試已下市股票篩選功能"""
    print("🔍 測試已下市股票篩選功能")
    print("=" * 50)
    
    try:
        from stock_filter import StockFilter
        
        # 創建篩選器
        filter = StockFilter()
        
        # 測試股票清單（包含已下市股票）
        test_stocks = [
            # 正常股票
            '2330',    # 台積電 - 有效
            '2317',    # 鴻海 - 有效  
            '00631L',  # 元大台灣50正2 - 有效
            '00878',   # 國泰永續高股息 - 有效
            
            # 已下市或不活躍股票
            '1724',    # 台硝 - 應被排除
            '5820',    # 日盛金 - 應被排除
            '1258',    # 其祥-KY - 應被排除
            '1333',    # 恩得利 - 應被排除
            
            # 權證等衍生性商品
            '030001',  # 權證 - 應被排除
            '020011',  # ETN - 應被排除
        ]
        
        print("📊 測試股票清單:")
        print("-" * 50)
        
        for stock in test_stocks:
            is_valid = filter.is_valid_stock_code(stock)
            status = "✅ 有效" if is_valid else "❌ 無效"
            print(f"  {stock:<8}: {status}")
        
        # 執行批量篩選
        print(f"\n📋 批量篩選測試:")
        print("-" * 50)
        
        valid_stocks = filter.filter_stock_list(test_stocks)
        
        print(f"原始股票數量: {len(test_stocks)}")
        print(f"有效股票數量: {len(valid_stocks)}")
        print(f"排除股票數量: {len(test_stocks) - len(valid_stocks)}")
        print(f"有效股票清單: {valid_stocks}")
        
        # 檢查已下市股票是否被正確排除
        delisted_stocks = ['1724', '5820', '1258', '1333']
        excluded_delisted = [stock for stock in delisted_stocks if stock not in valid_stocks]
        
        print(f"\n🚫 已下市股票排除檢查:")
        print("-" * 50)
        print(f"已下市股票: {delisted_stocks}")
        print(f"成功排除: {excluded_delisted}")
        print(f"排除率: {len(excluded_delisted)}/{len(delisted_stocks)} ({len(excluded_delisted)/len(delisted_stocks)*100:.1f}%)")
        
        # 檢查正常股票是否被保留
        normal_stocks = ['2330', '2317', '00631L', '00878']
        kept_normal = [stock for stock in normal_stocks if stock in valid_stocks]
        
        print(f"\n✅ 正常股票保留檢查:")
        print("-" * 50)
        print(f"正常股票: {normal_stocks}")
        print(f"成功保留: {kept_normal}")
        print(f"保留率: {len(kept_normal)}/{len(normal_stocks)} ({len(kept_normal)/len(normal_stocks)*100:.1f}%)")
        
        # 獲取篩選規則摘要
        summary = filter.get_filter_summary()
        print(f"\n📋 篩選規則摘要:")
        print("-" * 50)
        print(f"描述: {summary['description']}")
        print(f"ETF數量: {summary['etf_count']}")
        print(f"包含規則: {len(summary['include_rules'])}")
        print(f"排除規則: {len(summary['exclude_rules'])}")
        
        # 檢查已下市股票清單
        delisted_list = filter.get_delisted_stocks()
        print(f"\n🚫 已下市股票清單:")
        print("-" * 50)
        print(f"已下市股票數量: {len(delisted_list)}")
        print(f"清單: {delisted_list}")
        
        # 判斷測試結果
        success = (
            len(excluded_delisted) == len(delisted_stocks) and  # 所有已下市股票都被排除
            len(kept_normal) == len(normal_stocks)              # 所有正常股票都被保留
        )
        
        if success:
            print(f"\n🎉 測試通過！已下市股票篩選功能正常工作。")
            return True
        else:
            print(f"\n⚠️ 測試未完全通過，請檢查篩選邏輯。")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_stock_list_display():
    """測試股票清單顯示功能"""
    print("\n🔍 測試股票清單顯示功能")
    print("=" * 50)
    
    try:
        # 模擬股票清單（包含問題股票）
        stock_list = [
            '1713', '1714', '1717', '1718', '1720', '1721', '1722', '1723',
            '1724',  # 沒有名稱的股票
            '1725', '1726', '1727',
            '5609', '5701', '5703', '5704', '5706',
            '5820',  # 沒有名稱的股票
            '5864', '5871', '5876', '5878'
        ]
        
        # 檢查哪些股票應該被過濾掉
        from stock_filter import StockFilter
        filter = StockFilter()
        
        print("📊 股票清單篩選結果:")
        print("-" * 50)
        
        valid_stocks = []
        invalid_stocks = []
        
        for stock in stock_list:
            is_valid = filter.is_valid_stock_code(stock)
            if is_valid:
                valid_stocks.append(stock)
                print(f"  {stock}: ✅ 保留")
            else:
                invalid_stocks.append(stock)
                print(f"  {stock}: ❌ 排除（已下市或不活躍）")
        
        print(f"\n📈 篩選統計:")
        print(f"原始股票: {len(stock_list)} 支")
        print(f"保留股票: {len(valid_stocks)} 支")
        print(f"排除股票: {len(invalid_stocks)} 支")
        print(f"排除率: {len(invalid_stocks)/len(stock_list)*100:.1f}%")
        
        print(f"\n✅ 建議：")
        print("• 1724 (台硝) 和 5820 (日盛金) 已被識別為已下市或不活躍股票")
        print("• 這些股票將不會出現在「全部股票」清單中")
        print("• 用戶將看到更乾淨、更有用的股票清單")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🚀 已下市股票篩選功能測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("已下市股票篩選", test_delisted_stock_filter()))
    test_results.append(("股票清單顯示", test_stock_list_display()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！已下市股票篩選功能已成功整合。")
        print("\n📋 功能說明:")
        print("• 自動識別並排除已下市或不活躍的股票")
        print("• 包含1724 (台硝) 和5820 (日盛金) 等問題股票")
        print("• 確保股票清單只顯示有效的投資標的")
        print("• 提升用戶體驗和系統可用性")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能的實現。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
