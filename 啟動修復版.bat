@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 修復版

echo.
echo ========================================
echo    台股智能選股系統 v22.1 - 修復版
echo ========================================
echo.

if exist "dist\StockAnalyzer_Fixed.exe" (
    echo 找到修復版
    echo 正在啟動...
    echo.
    echo 修復版特點：
    echo    修復選股結果顯示問題
    echo    完整的策略交集分析
    echo    改善的用戶體驗
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Fixed.exe"
    
    echo 修復版已啟動！
    echo.
    
) else (
    echo 錯誤：找不到修復版
    echo.
    pause
    exit /b 1
)

timeout /t 3 >nul
