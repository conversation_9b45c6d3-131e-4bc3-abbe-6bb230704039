#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試公開資訊觀測站選單功能
"""

import webbrowser
import time

def test_mops_website():
    """測試公開資訊觀測站網址"""
    
    mops_url = "https://mopsov.twse.com.tw/mops/web/index"
    
    print("🧪 測試公開資訊觀測站功能")
    print("=" * 50)
    
    print(f"📋 網址: {mops_url}")
    
    try:
        print("🚀 正在開啟公開資訊觀測站...")
        webbrowser.open(mops_url)
        print("✅ 網站已在預設瀏覽器中開啟")
        
        print("\n📝 測試結果:")
        print("✅ 網址格式正確")
        print("✅ webbrowser.open() 執行成功")
        print("✅ 應該會在瀏覽器中看到公開資訊觀測站首頁")
        
        print("\n🔍 預期功能:")
        print("• 查看上市櫃公司財務報告")
        print("• 瀏覽重大訊息公告")
        print("• 搜尋公司基本資料")
        print("• 下載年報、季報等文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = test_mops_website()
    
    if success:
        print("\n🎉 測試完成！公開資訊觀測站選單功能正常")
    else:
        print("\n❌ 測試失敗！請檢查網址或瀏覽器設定")
