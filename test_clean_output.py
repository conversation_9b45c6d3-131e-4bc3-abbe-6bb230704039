#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試清理後的輸出
"""

import sys
import os
import types
from datetime import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_clean_price():
    """測試清理後的 price 函數"""
    print("🧪 測試清理後的輸出...")
    
    try:
        from crawler import price_twe, price_otc
        
        # 測試一個日期
        test_date = datetime(2022, 9, 1)
        print(f"測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("\n--- TWSE 測試 ---")
        result_twe = price_twe(test_date)
        if result_twe is not None and not result_twe.empty:
            print(f"✅ TWSE 成功，{len(result_twe)} 筆資料")
        else:
            print("⚠️ TWSE 無資料")
        
        print("\n--- TPEX 測試 ---")
        result_otc = price_otc(test_date)
        if result_otc is not None and not result_otc.empty:
            print(f"✅ TPEX 成功，{len(result_otc)} 筆資料")
        else:
            print("⚠️ TPEX 無資料")
            
        print("\n✅ 測試完成，輸出應該很簡潔")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        return False

if __name__ == "__main__":
    test_clean_price()
