#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試交易規則發現系統整合
驗證系統是否能正常啟動和運行
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox

def test_imports():
    """測試所有必要模組是否可以正常導入"""
    print("🔍 測試模組導入...")
    
    try:
        # 測試核心分析模組
        from mass_backtest_analyzer import MassBacktestAnalyzer
        print("✅ mass_backtest_analyzer.py - 導入成功")
        
        from trading_rule_miner import TradingRuleMiner
        print("✅ trading_rule_miner.py - 導入成功")
        
        from trading_mantra_generator import TradingMantraGenerator
        print("✅ trading_mantra_generator.py - 導入成功")
        
        from auto_rule_discovery_gui import AutoRuleDiscoveryGUI
        print("✅ auto_rule_discovery_gui.py - 導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_gui_launch():
    """測試GUI是否能正常啟動"""
    print("\n🖥️ 測試GUI啟動...")
    
    try:
        app = QApplication(sys.argv)
        
        from auto_rule_discovery_gui import AutoRuleDiscoveryGUI
        
        # 創建GUI實例
        window = AutoRuleDiscoveryGUI()
        
        # 設置窗口屬性
        window.setWindowTitle("🔍 交易規則發現系統 - 測試模式")
        window.setGeometry(200, 200, 1400, 900)
        
        print("✅ GUI創建成功")
        
        # 顯示窗口
        window.show()
        print("✅ GUI顯示成功")
        
        # 顯示測試完成訊息
        msg = QMessageBox()
        msg.setWindowTitle("測試完成")
        msg.setText("""
🎉 交易規則發現系統測試完成！

✅ 所有模組導入成功
✅ GUI界面啟動成功
✅ 系統已整合到主程式

📍 使用方式：
1. 啟動主程式 O3mh_gui_v21_optimized.py
2. 點擊菜單：📊 回測 → 🔍 交易規則發現系統
3. 設定分析參數並開始分析

🎯 功能特色：
• 分析2000檔股票歷史數據
• 自動發現最有效的交易規則
• 生成易記的買入/賣出口訣
• 支援Excel和JSON導出

點擊OK關閉測試視窗...
        """)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI啟動失敗: {e}")
        return False

def test_main_program_integration():
    """測試主程式整合"""
    print("\n🔗 測試主程式整合...")
    
    try:
        # 檢查主程式文件是否存在
        if not os.path.exists('O3mh_gui_v21_optimized.py'):
            print("❌ 主程式文件不存在")
            return False
        
        # 檢查是否已添加菜單項目
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '交易規則發現系統' in content and 'open_rule_discovery_system' in content:
            print("✅ 菜單項目已添加")
            print("✅ 對應方法已實現")
            return True
        else:
            print("❌ 主程式整合不完整")
            return False
            
    except Exception as e:
        print(f"❌ 主程式整合測試失敗: {e}")
        return False

def show_usage_guide():
    """顯示使用指南"""
    print("\n" + "="*60)
    print("🎯 交易規則發現系統使用指南")
    print("="*60)
    
    print("\n📍 啟動方式：")
    print("1. 執行主程式：python O3mh_gui_v21_optimized.py")
    print("2. 點擊菜單：📊 回測 → 🔍 交易規則發現系統")
    print("3. 或直接執行：python auto_rule_discovery_gui.py")
    
    print("\n⚙️ 使用步驟：")
    print("1. 設定分析參數：")
    print("   • 分析股票數量：100-2000檔")
    print("   • 最低成功率：50-90%")
    print("   • 最低平均報酬：1-10%")
    print("2. 點擊「🚀 開始分析」")
    print("3. 等待分析完成（可能需要幾分鐘）")
    print("4. 查看結果：")
    print("   • 📋 交易規則：查看發現的規則")
    print("   • 🟢 買入口訣：查看買入建議")
    print("   • 🔴 賣出口訣：查看賣出建議")
    print("   • ⚠️ 風險控制：查看風險管理")
    print("5. 點擊「💾 導出結果」保存分析結果")
    
    print("\n🎭 口訣範例：")
    print("🟢 買入：RSI超賣遇MACD金叉，買入時機到（勝率73%）")
    print("🔴 賣出：RSI超買遇MACD死叉，賣出保獲利（勝率75%）")
    print("⚠️ 風險：多策略確認進場，單一信號需謹慎")
    
    print("\n💡 使用建議：")
    print("• 重點關注成功率≥70%的規則")
    print("• 優先使用多策略確認的組合")
    print("• 結合風險控制口訣使用")
    print("• 定期更新規則以適應市場變化")
    
    print("\n📁 輸出文件：")
    print("• trading_mantras_YYYYMMDD_HHMMSS.json - 口訣文件")
    print("• trading_rules_YYYYMMDD_HHMMSS.xlsx - 規則報告")

def main():
    """主函數"""
    print("🚀 交易規則發現系統整合測試")
    print("="*50)
    
    # 測試模組導入
    import_success = test_imports()
    
    if not import_success:
        print("\n❌ 模組導入測試失敗，請檢查文件是否存在")
        return False
    
    # 測試主程式整合
    integration_success = test_main_program_integration()
    
    if not integration_success:
        print("\n❌ 主程式整合測試失敗")
        return False
    
    # 測試GUI啟動
    gui_success = test_gui_launch()
    
    if not gui_success:
        print("\n❌ GUI啟動測試失敗")
        return False
    
    # 顯示使用指南
    show_usage_guide()
    
    print("\n" + "="*50)
    print("🎉 所有測試通過！系統已準備就緒！")
    print("="*50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✨ 您現在可以使用交易規則發現系統了！")
        else:
            print("\n❌ 測試失敗，請檢查系統配置")
    except Exception as e:
        print(f"\n💥 測試過程發生錯誤: {e}")
        print("請檢查所有必要文件是否存在並重試")
