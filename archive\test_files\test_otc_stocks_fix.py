#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試上櫃股票修復效果
"""

import sys
import os
import time
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def test_otc_stock_conversion():
    """測試上櫃股票代碼轉換"""
    print("🔧 測試上櫃股票代碼轉換")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 測試各種股票代碼格式
    test_cases = [
        # (輸入代碼, 預期輸出, 說明)
        ('00743', '0743.TWO', '上櫃股票-標準格式'),
        ('00767', '0767.TWO', '上櫃股票-標準格式'),
        ('008201', '8201.TWO', '上櫃股票-多位數'),
        ('00776', '0776.TWO', '上櫃股票-標準格式'),
        ('00858', '0858.TWO', '上櫃股票-標準格式'),
        ('2330', '2330.TW', '上市股票-台積電'),
        ('2317', '2317.TW', '上市股票-鴻海'),
        ('1234', '1234.TW', '上市股票-4位數'),
        ('9999', '9999.TW', '上市股票-邊界值'),
    ]
    
    print("代碼轉換測試:")
    for input_code, expected, description in test_cases:
        result = strategy.convert_stock_symbol(input_code)
        status = "✅" if result == expected else "❌"
        print(f"{status} {input_code:>6} → {result:>10} (預期: {expected:>10}) - {description}")
    
    print()

def test_otc_data_fetching():
    """測試上櫃股票數據獲取"""
    print("📊 測試上櫃股票數據獲取")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    strategy.request_delay = 0.3  # 增加延遲避免錯誤
    strategy.max_retries = 2
    
    # 測試上櫃股票（這些是真實的上櫃股票代碼）
    otc_stocks = [
        '3443',  # 創意
        '4966',  # 譜瑞-KY
        '6488',  # 環球晶
        '4919',  # 新唐
        '3034',  # 聯詠
    ]
    
    print("上櫃股票數據獲取測試:")
    success_count = 0
    
    for i, stock_id in enumerate(otc_stocks):
        print(f"[{i+1}/{len(otc_stocks)}] 測試 {stock_id}...", end=" ")
        
        start_time = time.time()
        
        try:
            # 測試 twstock
            data_tw = strategy.fetch_daily_data_twstock(stock_id, 10)
            
            # 測試 yfinance
            data_yf = strategy.fetch_daily_data_yfinance(stock_id, 10)
            
            elapsed = time.time() - start_time
            
            if not data_tw.empty:
                print(f"✅ twstock成功 ({len(data_tw)}筆, {elapsed:.2f}s)")
                success_count += 1
            elif not data_yf.empty:
                print(f"✅ yfinance成功 ({len(data_yf)}筆, {elapsed:.2f}s)")
                success_count += 1
            else:
                print(f"❌ 兩種方式都失敗 ({elapsed:.2f}s)")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"⚠️ 錯誤 ({elapsed:.2f}s): {str(e)[:30]}...")
    
    print(f"\n成功率: {success_count}/{len(otc_stocks)} ({success_count/len(otc_stocks)*100:.1f}%)")
    print()

def test_problematic_stocks():
    """測試之前有問題的股票代碼"""
    print("🚨 測試之前有問題的股票代碼")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    strategy.request_delay = 0.2
    strategy.max_retries = 1  # 快速測試
    
    # 之前出現錯誤的股票代碼
    problem_stocks = [
        '00743', '00767', '008201', '00776', '00858',
        '00866', '00877', '00886', '00887', '00888',
        '00906', '00928'
    ]
    
    print("問題股票代碼處理測試:")
    handled_count = 0
    
    for i, stock_id in enumerate(problem_stocks):
        print(f"[{i+1:2d}/{len(problem_stocks)}] 測試 {stock_id}...", end=" ")
        
        start_time = time.time()
        
        try:
            # 檢查是否會被正確處理（不產生錯誤）
            data = strategy.fetch_daily_data(stock_id, 5)
            elapsed = time.time() - start_time
            
            if not data.empty:
                print(f"✅ 成功獲取數據 ({len(data)}筆, {elapsed:.2f}s)")
                handled_count += 1
            else:
                # 檢查是否被正確添加到失敗列表
                if stock_id in strategy.failed_stocks:
                    print(f"✅ 正確處理失敗 ({elapsed:.2f}s)")
                    handled_count += 1
                else:
                    print(f"❌ 未正確處理 ({elapsed:.2f}s)")
                    
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"⚠️ 仍有錯誤 ({elapsed:.2f}s): {str(e)[:30]}...")
    
    print(f"\n正確處理率: {handled_count}/{len(problem_stocks)} ({handled_count/len(problem_stocks)*100:.1f}%)")
    print(f"失敗列表大小: {len(strategy.failed_stocks)}")
    print()

def test_batch_processing():
    """測試批量處理改進"""
    print("⚡ 測試批量處理改進")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    strategy.request_delay = 0.1
    strategy.max_retries = 2
    
    # 混合測試：正常股票 + 問題股票
    mixed_stocks = [
        '2330', '2317',  # 正常上市股票
        '3443', '4966',  # 正常上櫃股票
        '00743', '00767', '008201',  # 問題股票代碼
        '9999', '0000'   # 無效股票代碼
    ]
    
    print("批量處理測試:")
    start_time = time.time()
    
    try:
        # 測試批量獲取
        batch_results = strategy.batch_fetch_data(mixed_stocks, max_workers=3)
        
        elapsed = time.time() - start_time
        
        print(f"✅ 批量處理完成")
        print(f"⏱️  總耗時: {elapsed:.2f} 秒")
        print(f"📊 輸入股票: {len(mixed_stocks)} 支")
        print(f"📊 成功獲取: {len(batch_results)} 支")
        print(f"📊 失敗股票: {len(strategy.failed_stocks)} 支")
        
        if batch_results:
            print(f"成功股票: {list(batch_results.keys())}")
        
        if strategy.failed_stocks:
            print(f"失敗股票: {list(strategy.failed_stocks)}")
            
    except Exception as e:
        print(f"❌ 批量處理失敗: {e}")
    
    print()

def test_ultra_fast_analysis():
    """測試超快速分析"""
    print("🚀 測試超快速分析")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 使用較小的測試集
    test_stocks = [
        '2330', '2317', '2454',  # 上市股票
        '3443', '4966', '6488',  # 上櫃股票
    ]
    
    print("超快速分析測試:")
    start_time = time.time()
    
    try:
        results = strategy.ultra_fast_analysis(test_stocks)
        
        elapsed = time.time() - start_time
        
        print(f"✅ 超快速分析完成")
        print(f"⏱️  總耗時: {elapsed:.2f} 秒")
        print(f"📊 測試股票: {len(test_stocks)} 支")
        print(f"🎯 找到信號: {len(results)} 支")
        
        if results:
            print(f"信號詳情:")
            for result in results:
                print(f"  • {result['stock_id']}: {result['result']} (信心度{result['confidence']}%)")
        
    except Exception as e:
        print(f"❌ 超快速分析失敗: {e}")

def main():
    """主測試函數"""
    print("🛠️ 上櫃股票修復效果測試")
    print("=" * 60)
    
    try:
        test_otc_stock_conversion()
        test_otc_data_fetching()
        test_problematic_stocks()
        test_batch_processing()
        test_ultra_fast_analysis()
        
        print("=" * 60)
        print("🎉 修復效果測試完成")
        print("=" * 60)
        
        print("\n💡 修復要點:")
        print("• 正確處理上櫃股票代碼格式")
        print("• 自動跳過無效股票代碼")
        print("• 智能錯誤處理和重試機制")
        print("• 失敗股票自動管理")
        print("• 批量處理性能優化")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
