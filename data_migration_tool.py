"""
資料遷移工具
將現有的price.db、daily_trading.db、stock_basic_info.db資料遷移到統一資料庫
"""

import sqlite3
import pandas as pd
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from unified_stock_database import UnifiedStockDatabase

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataMigrationTool:
    """資料遷移工具類"""
    
    def __init__(self, unified_db_path: str = None):
        self.unified_db = UnifiedStockDatabase(unified_db_path)
        self.migration_log = []
        
        # 預設資料庫路徑
        self.source_databases = {
            'price_db': 'db/price.db',
            'daily_trading_db': 'daily_trading.db',
            'stock_basic_info_db': 'D:/Finlab/history/tables/stock_basic_info.db'
        }
        
        logger.info("資料遷移工具初始化完成")
    
    def check_source_databases(self) -> Dict[str, Dict[str, Any]]:
        """檢查來源資料庫狀態"""
        status = {}
        
        for db_name, db_path in self.source_databases.items():
            db_info = {
                'path': db_path,
                'exists': os.path.exists(db_path),
                'tables': [],
                'total_records': 0,
                'error': None
            }
            
            if db_info['exists']:
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 獲取表名
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
                    db_info['tables'] = tables
                    
                    # 計算總記錄數
                    total_records = 0
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        total_records += count
                    
                    db_info['total_records'] = total_records
                    conn.close()
                    
                except Exception as e:
                    db_info['error'] = str(e)
                    logger.error(f"檢查 {db_name} 失敗: {e}")
            
            status[db_name] = db_info
        
        return status
    
    def migrate_price_db(self) -> Dict[str, Any]:
        """遷移price.db資料"""
        result = {
            'success': False,
            'migrated_records': 0,
            'error': None,
            'details': []
        }
        
        try:
            db_path = self.source_databases['price_db']
            if not os.path.exists(db_path):
                result['error'] = f"price.db不存在: {db_path}"
                return result
            
            logger.info("開始遷移price.db資料...")
            
            conn = sqlite3.connect(db_path)
            
            # 檢查表結構
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            if 'stock_daily_data' in tables:
                # 遷移stock_daily_data表
                df = pd.read_sql_query('SELECT * FROM stock_daily_data', conn)
                
                if not df.empty:
                    # 標準化欄位名稱
                    df_clean = self._standardize_price_db_columns(df)
                    
                    # 儲存到統一資料庫
                    saved_count = self.unified_db.save_daily_data(df_clean, 'price_db_migration')
                    result['migrated_records'] += saved_count
                    result['details'].append(f"stock_daily_data: {saved_count} 筆")
                    
                    logger.info(f"price.db遷移完成: {saved_count} 筆記錄")
            
            # 檢查是否有其他相關表
            for table in tables:
                if table not in ['stock_daily_data', 'sqlite_sequence']:
                    try:
                        df = pd.read_sql_query(f'SELECT * FROM {table}', conn)
                        if not df.empty:
                            result['details'].append(f"{table}: {len(df)} 筆 (需手動處理)")
                            logger.info(f"發現額外表格 {table}: {len(df)} 筆記錄")
                    except Exception as e:
                        logger.warning(f"無法讀取表格 {table}: {e}")
            
            conn.close()
            result['success'] = True
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"遷移price.db失敗: {e}")
        
        return result
    
    def migrate_daily_trading_db(self) -> Dict[str, Any]:
        """遷移daily_trading.db資料"""
        result = {
            'success': False,
            'migrated_records': 0,
            'error': None,
            'details': []
        }
        
        try:
            db_path = self.source_databases['daily_trading_db']
            if not os.path.exists(db_path):
                result['error'] = f"daily_trading.db不存在: {db_path}"
                return result
            
            logger.info("開始遷移daily_trading.db資料...")
            
            conn = sqlite3.connect(db_path)
            df = pd.read_sql_query('SELECT * FROM daily_trading_data', conn)
            conn.close()
            
            if not df.empty:
                # 標準化欄位名稱
                df_clean = self._standardize_daily_trading_columns(df)
                
                # 儲存到統一資料庫
                saved_count = self.unified_db.save_daily_data(df_clean, 'daily_trading_migration')
                result['migrated_records'] = saved_count
                result['details'].append(f"daily_trading_data: {saved_count} 筆")
                
                logger.info(f"daily_trading.db遷移完成: {saved_count} 筆記錄")
                result['success'] = True
            else:
                result['error'] = "daily_trading.db中沒有資料"
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"遷移daily_trading.db失敗: {e}")
        
        return result
    
    def migrate_stock_basic_info_db(self) -> Dict[str, Any]:
        """遷移stock_basic_info.db資料"""
        result = {
            'success': False,
            'migrated_records': 0,
            'error': None,
            'details': []
        }
        
        try:
            db_path = self.source_databases['stock_basic_info_db']
            if not os.path.exists(db_path):
                result['error'] = f"stock_basic_info.db不存在: {db_path}"
                return result
            
            logger.info("開始遷移stock_basic_info.db資料...")
            
            conn = sqlite3.connect(db_path)
            df = pd.read_sql_query('SELECT * FROM stock_basic_info', conn)
            conn.close()
            
            if not df.empty:
                # 標準化欄位名稱
                df_clean = self._standardize_basic_info_columns(df)
                
                # 儲存到統一資料庫
                saved_count = self.unified_db.save_basic_info(df_clean)
                result['migrated_records'] = saved_count
                result['details'].append(f"stock_basic_info: {saved_count} 筆")
                
                logger.info(f"stock_basic_info.db遷移完成: {saved_count} 筆記錄")
                result['success'] = True
            else:
                result['error'] = "stock_basic_info.db中沒有資料"
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"遷移stock_basic_info.db失敗: {e}")
        
        return result
    
    def _standardize_price_db_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """標準化price.db的欄位名稱 - 保持原有格式不變"""
        # price.db已經是標準格式，不需要重新命名
        # 欄位: stock_id, stock_name, listing_status, industry, Volume, Transaction, TradeValue, Open, High, Low, Close, Change, date

        df_clean = df.copy()

        # 確保必要欄位存在，使用price.db的原始欄位名稱
        required_columns = {
            'stock_id': '',
            'stock_name': '',
            'listing_status': '',
            'industry': '',
            'Volume': 0,
            'Transaction': 0,
            'TradeValue': 0,
            'Open': 0.0,
            'High': 0.0,
            'Low': 0.0,
            'Close': 0.0,
            'Change': 0.0,
            'date': ''
        }

        for col, default_value in required_columns.items():
            if col not in df_clean.columns:
                df_clean[col] = default_value

        return df_clean
    
    def _standardize_daily_trading_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """標準化daily_trading.db的欄位名稱 - 轉換為price.db格式"""
        column_mapping = {
            'StockID': 'stock_id',
            'Date': 'date',
            'DataSource': 'data_source',
            'Open': 'Open',
            'Max': 'High',
            'Min': 'Low',
            'Close': 'Close',
            'Change': 'Change',
            'TradeVolume': 'Volume',
            'TradeValue': 'TradeValue',
            'TransactionCount': 'Transaction'
        }

        df_clean = df.copy()

        # 重新命名欄位為price.db格式
        for old_name, new_name in column_mapping.items():
            if old_name in df_clean.columns:
                df_clean = df_clean.rename(columns={old_name: new_name})

        # 確保price.db必要欄位存在
        required_columns = {
            'stock_id': '',
            'stock_name': '',
            'listing_status': '',
            'industry': '',
            'Volume': 0,
            'Transaction': 0,
            'TradeValue': 0,
            'Open': 0.0,
            'High': 0.0,
            'Low': 0.0,
            'Close': 0.0,
            'Change': 0.0,
            'date': ''
        }

        for col, default_value in required_columns.items():
            if col not in df_clean.columns:
                df_clean[col] = default_value

        return df_clean
    
    def _standardize_basic_info_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """標準化stock_basic_info.db的欄位名稱 - 轉換為與price.db相容的格式"""
        column_mapping = {
            'company_code': 'stock_id',
            'company_name': 'stock_name',
            'company_abbreviation': 'company_abbreviation',
            'industry_category': 'industry',
            'market_type': 'market_type',
            'listing_date': 'listing_date',
            'par_value': 'par_value',
            'paid_capital': 'paid_capital',
            'issued_shares': 'issued_shares',
            'chairman': 'chairman',
            'general_manager': 'general_manager',
            'address': 'address',
            'phone': 'phone',
            'website': 'website'
        }

        df_clean = df.copy()

        # 重新命名欄位
        for old_name, new_name in column_mapping.items():
            if old_name in df_clean.columns:
                df_clean = df_clean.rename(columns={old_name: new_name})

        return df_clean
    
    def migrate_all(self) -> Dict[str, Any]:
        """遷移所有資料庫"""
        logger.info("開始完整資料遷移...")
        
        overall_result = {
            'success': True,
            'total_migrated': 0,
            'results': {},
            'summary': []
        }
        
        # 檢查來源資料庫
        db_status = self.check_source_databases()
        
        # 遷移price.db
        if db_status['price_db']['exists']:
            price_result = self.migrate_price_db()
            overall_result['results']['price_db'] = price_result
            if price_result['success']:
                overall_result['total_migrated'] += price_result['migrated_records']
                overall_result['summary'].append(f"price.db: {price_result['migrated_records']} 筆")
            else:
                overall_result['success'] = False
                overall_result['summary'].append(f"price.db: 失敗 - {price_result['error']}")
        else:
            overall_result['summary'].append("price.db: 檔案不存在")
        
        # 遷移daily_trading.db
        if db_status['daily_trading_db']['exists']:
            daily_result = self.migrate_daily_trading_db()
            overall_result['results']['daily_trading_db'] = daily_result
            if daily_result['success']:
                overall_result['total_migrated'] += daily_result['migrated_records']
                overall_result['summary'].append(f"daily_trading.db: {daily_result['migrated_records']} 筆")
            else:
                overall_result['summary'].append(f"daily_trading.db: 失敗 - {daily_result['error']}")
        else:
            overall_result['summary'].append("daily_trading.db: 檔案不存在")
        
        # 遷移stock_basic_info.db
        if db_status['stock_basic_info_db']['exists']:
            basic_result = self.migrate_stock_basic_info_db()
            overall_result['results']['stock_basic_info_db'] = basic_result
            if basic_result['success']:
                overall_result['total_migrated'] += basic_result['migrated_records']
                overall_result['summary'].append(f"stock_basic_info.db: {basic_result['migrated_records']} 筆")
            else:
                overall_result['summary'].append(f"stock_basic_info.db: 失敗 - {basic_result['error']}")
        else:
            overall_result['summary'].append("stock_basic_info.db: 檔案不存在")
        
        logger.info(f"資料遷移完成，總共遷移 {overall_result['total_migrated']} 筆記錄")
        
        return overall_result
    
    def generate_migration_report(self, results: Dict[str, Any]) -> str:
        """生成遷移報告"""
        report_lines = [
            "# 資料遷移報告",
            f"## 遷移時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"## 統一資料庫路徑: {self.unified_db.db_path}",
            "",
            "## 遷移結果摘要",
            f"- 總遷移記錄數: {results['total_migrated']:,} 筆",
            f"- 整體狀態: {'成功' if results['success'] else '部分失敗'}",
            ""
        ]
        
        # 詳細結果
        report_lines.append("## 詳細結果")
        for summary in results['summary']:
            report_lines.append(f"- {summary}")
        
        report_lines.append("")
        
        # 各資料庫詳情
        for db_name, result in results.get('results', {}).items():
            report_lines.append(f"### {db_name}")
            report_lines.append(f"- 狀態: {'成功' if result['success'] else '失敗'}")
            report_lines.append(f"- 遷移記錄數: {result['migrated_records']:,} 筆")
            
            if result['error']:
                report_lines.append(f"- 錯誤: {result['error']}")
            
            if result['details']:
                report_lines.append("- 詳細資訊:")
                for detail in result['details']:
                    report_lines.append(f"  - {detail}")
            
            report_lines.append("")
        
        # 後續建議
        report_lines.extend([
            "## 後續建議",
            "1. 驗證遷移後的資料完整性",
            "2. 測試現有策略程式的相容性",
            "3. 備份原始資料庫檔案",
            "4. 更新程式配置指向新的統一資料庫",
            ""
        ])
        
        return "\n".join(report_lines)
    
    def save_migration_report(self, results: Dict[str, Any], filename: str = None) -> str:
        """儲存遷移報告"""
        if filename is None:
            filename = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        report_content = self.generate_migration_report(results)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"遷移報告已儲存: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"儲存遷移報告失敗: {e}")
            return ""


def main():
    """主程式"""
    print("🚀 資料遷移工具")
    print("=" * 50)
    
    # 建立遷移工具
    migration_tool = DataMigrationTool()
    
    # 檢查來源資料庫
    print("📊 檢查來源資料庫...")
    db_status = migration_tool.check_source_databases()
    
    for db_name, status in db_status.items():
        print(f"  {db_name}: {'✅' if status['exists'] else '❌'} {status['path']}")
        if status['exists']:
            print(f"    表格: {status['tables']}")
            print(f"    記錄數: {status['total_records']:,}")
        if status['error']:
            print(f"    錯誤: {status['error']}")
    
    print()
    
    # 詢問是否繼續
    response = input("是否開始遷移? (y/N): ").strip().lower()
    if response != 'y':
        print("遷移已取消")
        return
    
    # 執行遷移
    print("🔄 開始資料遷移...")
    results = migration_tool.migrate_all()
    
    # 顯示結果
    print("\n📋 遷移結果:")
    for summary in results['summary']:
        print(f"  {summary}")
    
    print(f"\n總計遷移: {results['total_migrated']:,} 筆記錄")
    print(f"整體狀態: {'✅ 成功' if results['success'] else '⚠️ 部分失敗'}")
    
    # 儲存報告
    report_file = migration_tool.save_migration_report(results)
    if report_file:
        print(f"\n📄 遷移報告已儲存: {report_file}")
    
    print("\n🎉 遷移程序完成！")


if __name__ == "__main__":
    main()
