#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試PKL數據結構，查看可用的字段
"""

import sys
import pandas as pd
import logging

def test_pkl_data_structure():
    """測試PKL數據結構"""
    
    print("🔍 測試PKL數據結構")
    print("=" * 50)
    
    try:
        # 導入策略
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
        
        strategy = HighYieldTurtleStrategyOptimized()
        print("✅ 策略導入成功")
        
        # 測試幾個股票的PKL數據
        test_stocks = ['2330', '2881', '1101', '2882', '1108']
        
        print(f"\n📊 測試股票PKL數據結構:")
        print(f"{'股票代碼':<8} {'可用字段數':<10} {'主要字段'}")
        print(f"{'-'*8} {'-'*10} {'-'*50}")
        
        all_fields = set()
        
        for stock_id in test_stocks:
            pkl_info = strategy.get_stock_pkl_data(stock_id)
            
            if pkl_info:
                fields = list(pkl_info.keys())
                all_fields.update(fields)
                main_fields = [f for f in fields if any(keyword in f for keyword in ['殖利率', '本益比', '營收', '營業', '董監', '成長', '利益'])]
                
                print(f"{stock_id:<8} {len(fields):<10} {', '.join(main_fields[:3])}")
                
                # 顯示第一個股票的詳細信息
                if stock_id == test_stocks[0]:
                    print(f"\n📋 {stock_id} 的詳細PKL數據:")
                    for key, value in pkl_info.items():
                        if any(keyword in key for keyword in ['殖利率', '本益比', '營收', '營業', '董監', '成長', '利益', '持股']):
                            print(f"   {key}: {value}")
            else:
                print(f"{stock_id:<8} {'無數據':<10} {'N/A'}")
        
        print(f"\n🔍 所有可用字段 (共{len(all_fields)}個):")
        relevant_fields = [f for f in sorted(all_fields) if any(keyword in f for keyword in ['殖利率', '本益比', '營收', '營業', '董監', '成長', '利益', '持股', '毛利', '淨利', 'ROE', 'ROA'])]
        
        for i, field in enumerate(relevant_fields):
            if i % 3 == 0:
                print()
            print(f"   {field:<25}", end="")
        
        print(f"\n\n💡 建議使用的字段:")
        suggested_fields = {
            '營收成長': ['營收成長率(%)', '營收成長率', '營收年增率(%)', '營收年增率'],
            '營業利益率': ['營業利益率(%)', '營業利益率', '營業毛利率(%)', '營業毛利率'],
            '董監持股': ['董監持股(%)', '董監持股', '董監事持股(%)', '董監事持股'],
            'ROE': ['ROE(%)', 'ROE', '股東權益報酬率(%)', '股東權益報酬率'],
            'ROA': ['ROA(%)', 'ROA', '資產報酬率(%)', '資產報酬率']
        }
        
        for category, possible_fields in suggested_fields.items():
            found_fields = [f for f in possible_fields if f in all_fields]
            if found_fields:
                print(f"   {category}: {found_fields[0]} ✅")
            else:
                print(f"   {category}: 無可用字段 ❌")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pkl_data_structure()
    
    if success:
        print(f"\n🎯 結論:")
        print(f"✅ PKL數據結構測試完成")
        print(f"💡 可以根據實際可用字段更新GUI顯示邏輯")
        print(f"🔧 建議在GUI中使用實際存在的字段名稱")
    else:
        print(f"\n❌ 測試失敗，需要檢查PKL數據載入")
