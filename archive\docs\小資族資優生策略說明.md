# 小資族資優生策略說明

## 📊 策略概述

**小資族資優生策略** 是 FinLab 招牌課程「[小資族選股策略](https://hahow.in/cr/python-finance)」的長青策略。這個價值型策略，尋找的標的是小而美的股票，價格都在五十塊以下，然而小市值的公司通常風險比較高，所以此策略利用了 **自由現金流** 跟 **負債比率**，來觀察此公司的營運狀況，營運狀況不用到非常好，只求不要太差就可以了。

很多網站都會說**負債比率**是個很重要的指標，但我們多次測試，發現它在很多策略中都無法增加獲利，甚至還會減少…，然而這只是我們的經驗，不代表 **負債比率** 真的不用看，還是請各位自行斟酌。

以上的條件看起來都挺保守的，接下來才是比較積極的指標，以下的指標都是用來觀察一家公司的營收跟淨利！假如一家正常營運的公司獲利比其它公司好，那沒理由不投資呀！所以這個策略用了 **市值營收比** 、 **本益比** 、**營業利益成長率**，來觀察公司是否持續獲利，並且獲利越來越高。

一家獲利良好的公司，獲利持續成長，固然是好事，但不能只是獲利表面上成長，而稅後淨利卻很少，那也不行！所以這邊就用了額外的一個指標，來判斷稅後淨利的大小。

## 🎯 策略原理

此策略基於您提供的 finlab 程式碼實現，完整的原始策略邏輯如下：

```python
from finlab import data
from finlab.backtest import sim
import pandas as pd

# 獲取基礎數據
股本 = data.get('financial_statement:股本')
price = data.get('price:收盤價')
vol = data.get('price:成交股數')
市值 = data.get('etl:market_value')

# 計算自由現金流（4季滾動平均）
df1 = data.get('financial_statement:投資活動之淨現金流入_流出')
df2 = data.get('financial_statement:營業活動之淨現金流入_流出')
自由現金流 = (df1 + df2).rolling(4).mean()

# 計算ROE
稅後淨利 = data.get('fundamental_features:經常稅後淨利')
權益總計 = data.get('financial_statement:股東權益總額')
股東權益報酬率 = 稅後淨利 / 權益總計

# 營業利益成長率
營業利益成長率 = data.get('fundamental_features:營業利益成長率')

# 計算市值營收比
當月營收 = data.get('monthly_revenue:當月營收') * 1000
當季營收 = 當月營收.rolling(4).sum()
市值營收比 = 市值 / 當季營收

# 策略條件
condition1 = (市值 < 1e10)           # 市值小於100億
condition2 = 自由現金流 > 0           # 自由現金流為正
condition3 = 股東權益報酬率 > 0       # ROE為正
condition4 = 營業利益成長率 > 0       # 營業利益成長率為正
condition5 = 市值營收比 < 2           # 市值營收比小於2
condition6 = vol > 200000            # 成交量大於20萬股

# RSV技術指標排名
rsv = (price - price.rolling(60).min()) / (price.rolling(60).max() - price.rolling(60).min())

# 最終選股邏輯：符合所有條件且RSV排名前10
position = ((condition1 & condition2 & condition3 & condition4 & condition5 & condition6) * rsv).is_largest(10)

# 調整時間索引並執行回測
position = position.reindex(當月營收.index_str_to_date().index)
report = sim(position, live_performance_start='2018-04-01')
```

### 策略設計理念

這個策略的設計理念是尋找「小而美」的股票，具體特點：

1. **保守的風險控制**：
   - 使用**自由現金流**確保公司營運健康，營運狀況不用到非常好，只求不要太差
   - 雖然**負債比率**在多次測試中無法增加獲利，甚至還會減少，但仍可作為參考指標

2. **積極的成長指標**：
   - 使用**市值營收比**、**本益比**、**營業利益成長率**
   - 觀察公司是否持續獲利，並且獲利越來越高
   - 假如一家正常營運的公司獲利比其它公司好，那沒理由不投資

3. **稅後淨利品質**：
   - 不只看表面獲利成長，而稅後淨利卻很少
   - 使用額外指標來判斷稅後淨利的大小

### 策略核心思想
這是一個**多因子量化選股策略**，結合了：
- **基本面篩選**：財務健康度（現金流、ROE、成長性）
- **估值篩選**：避免高估值股票（市值營收比）
- **流動性篩選**：確保交易流動性
- **技術面排名**：使用RSV進行相對強度排名
- **組合管理**：選取前10檔最優股票

## 📋 策略條件

### 1. 市值條件 💰
- **條件**: 市值 < 100億元
- **目的**: 篩選中小型股票，避免大型股流動性不足
- **實現**: 使用股價×成交量作為市值代理指標

### 2. 自由現金流條件 💸
- **條件**: 自由現金流 > 0
- **目的**: 確保公司有正向現金流，財務健康
- **計算**: (投資活動現金流 + 營業活動現金流).rolling(4).mean()

### 3. ROE條件 📈
- **條件**: 股東權益報酬率 > 0
- **目的**: 確保公司獲利能力良好
- **計算**: 稅後淨利 / 權益總計

### 4. 營業利益成長率條件 🚀
- **條件**: 營業利益成長率 > 0
- **目的**: 篩選成長性良好的公司
- **意義**: 公司營運效率提升

### 5. 市值營收比條件 💹
- **條件**: 市值營收比 < 2
- **目的**: 避免估值過高的股票
- **計算**: 市值 / 當季營收

### 6. 成交量條件 📊
- **條件**: 成交量 > 20萬股 (200張)
- **目的**: 確保流動性充足
- **意義**: 避免流動性不足的股票

### 7. RSV技術排名 ⭐ (加分項)
- **條件**: RSV > 0.5 (相對強勢)
- **目的**: 技術面確認，選擇相對強勢股票
- **計算**: (收盤價 - 60日最低價) / (60日最高價 - 60日最低價)

## 🔧 使用方法

### 在GUI中使用
1. 開啟台股智能選股系統
2. 在策略選擇下拉選單中找到 **"🚀 進階策略"**
3. 選擇 **"Finlab量化策略"**
4. 點擊 **"開始選股"** 執行策略

### 策略評分機制
- **總分**: 6分 (6個基本條件)
- **通過標準**: 所有6個基本條件都必須通過
- **加分項**: RSV排名 > 0.5 會顯示 "⭐RSV加分"

## 📊 輸出結果說明

### 成功案例
```
✅ 適合
小資族資優生評分: 6/6 (通過: 市值,自由現金流,ROE,營業利益成長率,市值營收比,成交量) | ⭐RSV加分
```

### 失敗案例
```
❌ 不適合
小資族資優生評分不足: 4/6 | 未通過: 成交量: 成交量 150張 <= 200張; 市值營收比: 市值營收比代理 2.5 >= 2
```

## ⚠️ 重要注意事項

### 🚨 數據需求警告
**此策略目前無法正常運作，因為缺少以下關鍵財務數據：**

1. **市值數據** ❌
   - 需要：股本 × 股價
   - 來源：公開資訊觀測站、TEJ等

2. **自由現金流數據** ❌
   - 需要：投資活動現金流 + 營業活動現金流
   - 來源：財務報表、財報狗等

3. **ROE數據** ❌
   - 需要：稅後淨利 / 權益總計
   - 來源：財務報表

4. **營業利益成長率數據** ❌
   - 需要：營業利益年增率
   - 來源：財務報表

5. **月營收數據** ❌
   - 需要：每月營收公告
   - 來源：公開資訊觀測站

6. **全市場數據** ❌
   - 需要：所有股票數據進行RSV排名
   - 來源：完整股票數據庫

### 🔧 實際應用建議
1. **數據整合**: 必須整合真實的財務數據庫
   - TEJ台灣經濟新報
   - 財報狗 API
   - 公開資訊觀測站
   - finlab 數據庫

2. **系統架構**: 建立完整的數據管道
   - 自動數據更新
   - 數據品質檢查
   - 異常處理機制

3. **回測驗證**: 使用歷史數據驗證策略效果
4. **風險控制**: 結合其他風險指標

## 🔄 策略優化建議

### 短期優化
1. **整合真實財務數據**: 連接財務數據API
2. **動態參數調整**: 根據市場環境調整參數
3. **多時間框架**: 增加不同時間框架的分析

### 長期優化
1. **機器學習**: 使用ML模型優化選股邏輯
2. **情緒指標**: 整合市場情緒和資金流向
3. **產業輪動**: 考慮產業輪動效應

## 📈 策略特色

### 優點
- ✅ **系統化選股**: 避免主觀判斷偏誤
- ✅ **多維度篩選**: 結合財務面和技術面
- ✅ **風險控制**: 多重條件降低風險
- ✅ **易於理解**: 邏輯清晰，參數透明

### 適用場景
- 📊 **量化投資**: 適合系統化投資策略
- 🎯 **價值投資**: 注重基本面的投資者
- 📈 **成長投資**: 尋找成長性股票
- 🔄 **定期篩選**: 適合定期執行的選股策略

## 📊 當前狀態

### ✅ 已完成
- 策略邏輯實現
- GUI 整合
- 條件檢查框架
- 數據缺失警告系統
- 測試腳本

### ❌ 待完成 (關鍵)
- 財務數據整合
- 市值計算
- 營收數據獲取
- 全市場排名功能
- 真實數據驗證

## 🚀 開始使用

您可以在系統的 **"🚀 進階策略"** 中找到 **"Finlab量化策略"**，但請注意：

⚠️ **目前此策略會顯示數據缺失警告，無法正常選股**

### 測試方法
```bash
python test_finlab_strategy.py
```

### 實際部署步驟
1. 整合財務數據源
2. 修改策略檢查函數
3. 移除代理指標邏輯
4. 進行回測驗證
5. 正式上線使用

---

*策略框架已完成，但需要真實財務數據才能正常運作。*
