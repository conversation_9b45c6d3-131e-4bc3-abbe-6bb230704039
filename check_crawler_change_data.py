#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查爬蟲原始資料是否包含漲跌價差資訊
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def check_crawler_change_data():
    """檢查爬蟲原始資料是否包含漲跌價差資訊"""
    
    print("=" * 80)
    print("🔍 檢查爬蟲原始資料是否包含漲跌價差資訊")
    print("=" * 80)
    
    try:
        from crawler import price_twe, price_otc, merge, o2tp
        
        # 測試一個已知有資料的日期
        test_date = datetime(2023, 9, 20)
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 檢查上市資料
        print(f"\n🏢 檢查上市資料 (price_twe):")
        dftwe = price_twe(test_date)
        
        if not dftwe.empty:
            print(f"   ✅ 成功獲取 {len(dftwe)} 筆上市資料")
            print(f"   📋 上市資料欄位: {list(dftwe.columns)}")
            
            # 檢查是否有漲跌相關欄位
            change_related_columns = []
            for col in dftwe.columns:
                if '漲跌' in col or 'change' in col.lower() or '差' in col:
                    change_related_columns.append(col)
            
            if change_related_columns:
                print(f"   📊 找到漲跌相關欄位: {change_related_columns}")
                
                # 顯示 0050 的漲跌資料
                df_reset = dftwe.reset_index()
                etf_0050 = df_reset[df_reset.index.to_series().astype(str).str.contains('0050')]
                
                if not etf_0050.empty:
                    row = etf_0050.iloc[0]
                    print(f"   📈 0050 漲跌資料:")
                    for col in change_related_columns:
                        print(f"      {col}: {row.get(col, 'N/A')}")
            else:
                print(f"   ❌ 未找到漲跌相關欄位")
        else:
            print(f"   ❌ 上市資料為空")
        
        # 等待一下
        import time
        time.sleep(5)
        
        # 檢查上櫃資料
        print(f"\n🏪 檢查上櫃資料 (price_otc):")
        dfotc = price_otc(test_date)
        
        if not dfotc.empty:
            print(f"   ✅ 成功獲取 {len(dfotc)} 筆上櫃資料")
            print(f"   📋 上櫃資料欄位: {list(dfotc.columns)}")
            
            # 檢查是否有漲跌相關欄位
            change_related_columns = []
            for col in dfotc.columns:
                if '漲跌' in col or 'change' in col.lower() or '差' in col:
                    change_related_columns.append(col)
            
            if change_related_columns:
                print(f"   📊 找到漲跌相關欄位: {change_related_columns}")
                
                # 顯示第一筆的漲跌資料
                if len(dfotc) > 0:
                    row = dfotc.iloc[0]
                    print(f"   📈 第一筆漲跌資料:")
                    for col in change_related_columns:
                        print(f"      {col}: {row.get(col, 'N/A')}")
            else:
                print(f"   ❌ 未找到漲跌相關欄位")
        else:
            print(f"   ❌ 上櫃資料為空")
        
        # 檢查合併後的資料
        if not dftwe.empty and not dfotc.empty:
            print(f"\n🔗 檢查合併資料 (merge):")
            df_merged = merge(dftwe, dfotc, o2tp)
            
            if not df_merged.empty:
                print(f"   ✅ 成功合併 {len(df_merged)} 筆資料")
                print(f"   📋 合併後欄位: {list(df_merged.columns)}")
                
                # 檢查是否有漲跌相關欄位
                change_related_columns = []
                for col in df_merged.columns:
                    if '漲跌' in col or 'change' in col.lower() or '差' in col:
                        change_related_columns.append(col)
                
                if change_related_columns:
                    print(f"   📊 合併後漲跌相關欄位: {change_related_columns}")
                else:
                    print(f"   ❌ 合併後未找到漲跌相關欄位")
                    print(f"   💡 這解釋了為什麼 Change 欄位為 0")
            else:
                print(f"   ❌ 合併後資料為空")
        
        # 總結
        print(f"\n📋 總結:")
        print(f"   如果原始資料中沒有漲跌價差欄位，我們需要:")
        print(f"   1. 檢查是否可以從其他欄位計算 Change")
        print(f"   2. 或者從前一日收盤價計算漲跌")
        print(f"   3. 或者接受 Change 欄位為 0（如果不需要此資訊）")
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_crawler_change_data()
