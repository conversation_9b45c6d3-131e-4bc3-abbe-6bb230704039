#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的 PE 保存功能
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'finlab'))

def test_pe_save():
    """測試 PE 保存功能"""
    print("=" * 80)
    print("🧪 測試修復後的 PE 保存功能")
    print("=" * 80)
    
    try:
        # 導入必要的函數
        from crawler import crawl_pe
        from auto_update import save_pe_to_database
        
        print("✅ 成功導入函數")
        
        # 使用一個測試日期
        test_date = datetime(2024, 12, 30)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 爬取 PE 資料
        print("🚀 爬取 PE 資料...")
        df = crawl_pe(test_date)
        
        if df.empty:
            print("⚠️ 爬取資料為空")
            return
        
        print(f"✅ 成功爬取 {len(df)} 筆 PE 資料")
        
        # 檢查 DataFrame 結構
        df_reset = df.reset_index()
        print(f"\n📊 DataFrame 資訊:")
        print(f"   形狀: {df_reset.shape}")
        print(f"   欄位: {list(df_reset.columns)}")
        
        # 顯示前幾筆資料
        print(f"\n📋 前3筆資料:")
        print(df_reset.head(3))
        
        # 測試保存到資料庫
        print(f"\n💾 測試保存到資料庫...")
        test_db_path = 'D:/Finlab/history/tables/pe_test.db'
        
        # 如果測試資料庫存在，先刪除
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print(f"🗑️ 已刪除舊的測試資料庫")
        
        # 保存資料
        result = save_pe_to_database(df, test_db_path)
        
        if result:
            print(f"✅ 資料保存成功")
            
            # 驗證保存結果
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            # 檢查表格結構
            cursor.execute("PRAGMA table_info(pe_data)")
            columns = cursor.fetchall()
            
            print(f"\n📊 資料庫表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查資料數量
            cursor.execute("SELECT COUNT(*) FROM pe_data")
            count = cursor.fetchone()[0]
            print(f"\n📊 保存記錄數: {count}")
            
            # 檢查資料內容
            cursor.execute("SELECT * FROM pe_data LIMIT 3")
            sample_data = cursor.fetchall()
            
            print(f"\n📋 資料庫中的前3筆資料:")
            for row in sample_data:
                print(f"   {row}")
            
            # 檢查該日期的資料
            cursor.execute("SELECT COUNT(*) FROM pe_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            date_count = cursor.fetchone()[0]
            print(f"\n📊 該日期記錄數: {date_count}")
            
            conn.close()
            
            # 清理測試資料庫
            os.remove(test_db_path)
            print(f"🗑️ 已清理測試資料庫")
            
        else:
            print(f"❌ 資料保存失敗")
        
        # 測試保存到實際資料庫
        print(f"\n💾 測試保存到實際 PE 資料庫...")
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        
        # 檢查保存前的記錄數
        if os.path.exists(pe_db_path):
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM pe_data")
            before_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM pe_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            before_date_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   保存前總記錄數: {before_count:,}")
            print(f"   保存前該日期記錄數: {before_date_count}")
        
        # 保存到實際資料庫
        result = save_pe_to_database(df, pe_db_path)
        
        if result:
            print(f"✅ 實際資料庫保存成功")
            
            # 檢查保存後的記錄數
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM pe_data")
            after_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM pe_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            after_date_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   保存後總記錄數: {after_count:,}")
            print(f"   保存後該日期記錄數: {after_date_count}")
            
            if after_date_count > 0:
                print(f"✅ 該日期資料保存成功")
            else:
                print(f"⚠️ 該日期資料保存可能有問題")
        else:
            print(f"❌ 實際資料庫保存失敗")
        
        print(f"\n✅ PE 保存功能測試完成")
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pe_save()
