#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用修復後的 benchmark 爬蟲進行測試
參考成功爬蟲的寫法：直接使用 requests.get + headers + 重試機制
"""

# 修復 ipywidgets 依賴問題
import sys
import types

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass

    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

import datetime
import pandas as pd
import time
import random
import urllib3
import requests
from io import StringIO

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def crawl_benchmark_fixed(date):
    """
    修復版本的 benchmark 爬蟲
    參考 price_twe 的成功寫法
    """
    import requests
    import random
    import urllib3
    
    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    date_str = date.strftime('%Y%m%d')
    
    # 使用與成功爬蟲相同的 URL 和參數
    url = "https://www.twse.com.tw/rwd/zh/TAIEX/MI_5MINS_INDEX"
    params = {
        "response": "csv",
        "date": date_str,
        "_": "1544020420045"
    }
    
    # 設置 headers (參考成功爬蟲)
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for attempt in range(3):  # 重試3次
        try:
            # 隨機延遲 (參考成功爬蟲)
            time.sleep(random.uniform(2, 5))
            
            print(f"   嘗試 {attempt+1}/3: 請求 {url}...")
            
            # 直接使用 requests.get (參考成功爬蟲)
            res = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            
            print(f"   回應狀態: {res.status_code}")
            print(f"   回應長度: {len(res.text)} 字元")
            
            if res.status_code == 429:
                print("⚠️ TWSE 回應 429 Too Many Requests，等待60秒後重試")
                time.sleep(60)
                continue
            
            res.raise_for_status()
            
            # 檢查回應內容
            if len(res.text) < 10:
                print(f"   ⚠️ 回應內容太短，可能是假日")
                return pd.DataFrame()
            
            print(f"   前100字元: {res.text[:100]}")
            
            # 利用 pandas 將資料整理成表格
            df = pd.read_csv(StringIO(res.text.replace("=","")), header=1, index_col='時間')
            
            # 資料處理
            df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
            df.columns = df.columns.str.replace(' ', '')
            df.index = pd.to_datetime(date.strftime('%Y %m %d ') + pd.Series(df.index))
            df = df.apply(lambda s: s.astype(str).str.replace(",", "").astype(float))
            df = df.reset_index().rename(columns={'時間':'date'})
            df['stock_id'] = '台股指數'
            
            result = df.set_index(['stock_id', 'date'])
            print(f"   ✅ 成功處理: {len(result)} 筆資料")
            return result
            
        except Exception as e:
            print(f"   ❌ 嘗試 {attempt+1} 失敗: {str(e)[:50]}...")
            if attempt < 2:
                wait_time = (attempt + 1) * 10
                print(f"   ⏳ 等待 {wait_time} 秒後重試...")
                time.sleep(wait_time)
    
    print("   ❌ 所有重試都失敗")
    return pd.DataFrame()

def test_fixed_benchmark():
    """測試修復後的 benchmark 爬蟲"""
    print("🔧 測試修復後的 Benchmark 爬蟲")
    print("=" * 60)
    
    # 選擇測試日期
    test_date = datetime.datetime.now() - datetime.timedelta(days=1)
    while test_date.weekday() >= 5:  # 跳過週末
        test_date -= datetime.timedelta(days=1)
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
    
    print(f"\n🧪 測試修復後的 Benchmark 爬蟲...")
    try:
        result = crawl_benchmark_fixed(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ 修復後的 Benchmark 爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   欄位: {list(result.columns)[:5]}...")
            print(f"   索引: {result.index.names}")
            
            # 檢查台股指數
            if '發行量加權股價指數' in result.columns:
                taiex = result['發行量加權股價指數'].iloc[0]
                print(f"   台股指數: {taiex:.2f}")
            
            # 保存測試結果
            result.to_pickle('benchmark_test_fixed.pkl')
            print(f"   測試資料已保存: benchmark_test_fixed.pkl")
            
            return True
        else:
            print(f"\n⚠️ 爬取成功但無資料 (可能是假日)")
            return False
            
    except Exception as e:
        print(f"\n❌ 修復後的爬蟲仍然失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def compare_original_vs_fixed():
    """比較原版和修復版的差異"""
    print(f"\n🔍 比較原版和修復版爬蟲")
    print("=" * 60)
    
    # 導入原版爬蟲
    try:
        from crawler import crawl_benchmark
        
        test_date = datetime.datetime.now() - datetime.timedelta(days=1)
        while test_date.weekday() >= 5:
            test_date -= datetime.timedelta(days=1)
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 測試原版
        print(f"\n🧪 測試原版 crawl_benchmark...")
        try:
            original_result = crawl_benchmark(test_date)
            if original_result is not None and not original_result.empty:
                print(f"   ✅ 原版成功: {len(original_result)} 筆")
                original_success = True
            else:
                print(f"   ⚠️ 原版無資料")
                original_success = False
        except Exception as e:
            print(f"   ❌ 原版失敗: {str(e)[:50]}...")
            original_success = False
        
        # 測試修復版
        print(f"\n🧪 測試修復版 crawl_benchmark_fixed...")
        try:
            fixed_result = crawl_benchmark_fixed(test_date)
            if fixed_result is not None and not fixed_result.empty:
                print(f"   ✅ 修復版成功: {len(fixed_result)} 筆")
                fixed_success = True
            else:
                print(f"   ⚠️ 修復版無資料")
                fixed_success = False
        except Exception as e:
            print(f"   ❌ 修復版失敗: {str(e)[:50]}...")
            fixed_success = False
        
        # 分析結果
        print(f"\n📊 比較結果:")
        print(f"   原版: {'✅ 成功' if original_success else '❌ 失敗'}")
        print(f"   修復版: {'✅ 成功' if fixed_success else '❌ 失敗'}")
        
        if not original_success and fixed_success:
            print(f"\n🎉 修復成功！")
            print(f"💡 關鍵改進:")
            print(f"   • 使用直接 requests.get 而不是 requests_get")
            print(f"   • 添加 User-Agent headers")
            print(f"   • 添加重試機制和隨機延遲")
            print(f"   • 使用 verify=False")
            return True
        elif original_success and fixed_success:
            print(f"\n✅ 兩個版本都成功")
            return True
        else:
            print(f"\n⚠️ 仍有問題需要解決")
            return False
            
    except ImportError:
        print(f"❌ 無法導入原版爬蟲")
        return False

def main():
    """主函數"""
    print("🔧 Benchmark 爬蟲修復測試")
    print("=" * 60)
    print("🎯 目標: 使用成功爬蟲的寫法修復 benchmark")
    print("💡 改進: requests.get + headers + 重試 + 隨機延遲")
    print("=" * 60)
    
    # 測試修復版
    fixed_success = test_fixed_benchmark()
    
    if fixed_success:
        # 比較原版和修復版
        compare_success = compare_original_vs_fixed()
        
        if compare_success:
            print(f"\n🎉 Benchmark 爬蟲修復成功！")
            print(f"💡 現在可以安全使用 benchmark 更新")
            print(f"🔧 建議在 crawler.py 中應用這個修復")
        else:
            print(f"\n⚠️ 需要進一步調試")
    else:
        print(f"\n❌ 修復版仍有問題")

if __name__ == "__main__":
    main()
