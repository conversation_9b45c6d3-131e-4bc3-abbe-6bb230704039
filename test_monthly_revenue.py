#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新後的月營收爬蟲
"""

import sys
import os
import datetime
import pandas as pd

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 導入更新後的函數
from auto_update import (
    month_revenue_mops_direct_fixed,
    get_monthly_revenue_data,
    process_month_revenue_data_original
)

def test_single_type():
    """測試單一類型的月營收爬蟲"""
    print("🔍 測試單一類型月營收爬蟲")
    print("=" * 50)
    
    # 測試參數: 2024年11月 (民國113年)
    test_date = datetime.date(2024, 11, 1)
    year = 113  # 民國年
    month = 11
    
    print(f"📅 測試日期: {test_date} (民國{year}年{month}月)")
    
    # 測試國內上市
    print(f"\n📊 測試國內上市...")
    domestic_sii = get_monthly_revenue_data('sii', year, month, test_date, foreign=False)
    print(f"   結果: {len(domestic_sii)} 筆資料")
    if len(domestic_sii) > 0:
        print(f"   欄位: {list(domestic_sii.columns)}")
        print(f"   前3筆:")
        print(domestic_sii.head(3))
    
    # 測試國外上市
    print(f"\n📊 測試國外上市...")
    foreign_sii = get_monthly_revenue_data('sii', year, month, test_date, foreign=True)
    print(f"   結果: {len(foreign_sii)} 筆資料")
    if len(foreign_sii) > 0:
        print(f"   欄位: {list(foreign_sii.columns)}")
        print(f"   前3筆:")
        print(foreign_sii.head(3))
    
    # 測試國內上櫃
    print(f"\n📊 測試國內上櫃...")
    domestic_otc = get_monthly_revenue_data('otc', year, month, test_date, foreign=False)
    print(f"   結果: {len(domestic_otc)} 筆資料")
    if len(domestic_otc) > 0:
        print(f"   欄位: {list(domestic_otc.columns)}")
        print(f"   前3筆:")
        print(domestic_otc.head(3))
    
    # 測試國外上櫃
    print(f"\n📊 測試國外上櫃...")
    foreign_otc = get_monthly_revenue_data('otc', year, month, test_date, foreign=True)
    print(f"   結果: {len(foreign_otc)} 筆資料")
    if len(foreign_otc) > 0:
        print(f"   欄位: {list(foreign_otc.columns)}")
        print(f"   前3筆:")
        print(foreign_otc.head(3))

def test_combined_crawler():
    """測試合併版月營收爬蟲"""
    print(f"\n🔍 測試合併版月營收爬蟲")
    print("=" * 50)
    
    # 測試參數: 2024年11月 (民國113年)
    test_date = datetime.date(2024, 11, 1)
    year = 113  # 民國年
    month = 11
    
    print(f"📅 測試日期: {test_date} (民國{year}年{month}月)")
    
    # 測試上市 (國內+國外)
    print(f"\n📊 測試上市 (國內+國外)...")
    sii_combined = month_revenue_mops_direct_fixed('sii', year, month, test_date)
    print(f"   上市總計: {len(sii_combined)} 筆資料")
    if len(sii_combined) > 0:
        print(f"   欄位: {list(sii_combined.columns)}")
        print(f"   索引類型: {type(sii_combined.index)}")
        print(f"   前5筆:")
        print(sii_combined.head(5))
        
        # 保存測試結果
        sii_combined.to_csv('test_sii_combined.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_sii_combined.csv")
    
    # 測試上櫃 (國內+國外)
    print(f"\n📊 測試上櫃 (國內+國外)...")
    otc_combined = month_revenue_mops_direct_fixed('otc', year, month, test_date)
    print(f"   上櫃總計: {len(otc_combined)} 筆資料")
    if len(otc_combined) > 0:
        print(f"   欄位: {list(otc_combined.columns)}")
        print(f"   索引類型: {type(otc_combined.index)}")
        print(f"   前5筆:")
        print(otc_combined.head(5))
        
        # 保存測試結果
        otc_combined.to_csv('test_otc_combined.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_otc_combined.csv")
    
    # 合併上市+上櫃
    if len(sii_combined) > 0 and len(otc_combined) > 0:
        print(f"\n📊 合併上市+上櫃...")
        all_combined = pd.concat([sii_combined, otc_combined], ignore_index=False)
        print(f"   總計: {len(all_combined)} 筆資料")
        print(f"   欄位: {list(all_combined.columns)}")
        
        # 保存最終結果
        all_combined.to_csv('test_all_monthly_revenue.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: test_all_monthly_revenue.csv")
        
        # 統計分析
        print(f"\n📈 統計分析:")
        if '當月營收' in all_combined.columns:
            revenue_stats = all_combined['當月營收'].describe()
            print(f"   當月營收統計:")
            print(f"     總筆數: {revenue_stats['count']}")
            print(f"     平均值: {revenue_stats['mean']:,.0f}")
            print(f"     中位數: {revenue_stats['50%']:,.0f}")
            print(f"     最大值: {revenue_stats['max']:,.0f}")
            print(f"     最小值: {revenue_stats['min']:,.0f}")

def test_url_accessibility():
    """測試 URL 可訪問性"""
    print(f"\n🔍 測試 URL 可訪問性")
    print("=" * 50)
    
    import requests
    import time
    
    # 測試 URL 列表
    test_urls = [
        'https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_11_0.html',  # 國內上市
        'https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_11_1.html',  # 國外上市
        'https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_113_11_0.html',  # 國內上櫃
        'https://mopsov.twse.com.tw/nas/t21/otc/t21sc03_113_11_1.html',  # 國外上櫃
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📄 測試 {i}/4: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=30, verify=False)
            print(f"   狀態碼: {response.status_code}")
            print(f"   內容長度: {len(response.content)} bytes")
            
            if response.status_code == 200:
                response.encoding = 'big5'
                content = response.text
                
                # 檢查內容
                if '查無資料' in content:
                    print(f"   ⚠️ 查無資料")
                elif '公司代號' in content:
                    print(f"   ✅ 包含營收資料")
                    
                    # 嘗試解析表格
                    try:
                        dfs = pd.read_html(content)
                        print(f"   📊 找到 {len(dfs)} 個表格")
                        
                        for j, df in enumerate(dfs):
                            if df.shape[1] > 5:  # 可能的營收表格
                                print(f"     表格 {j+1}: {df.shape}")
                    except Exception as e:
                        print(f"   ⚠️ 表格解析失敗: {str(e)[:50]}...")
                else:
                    print(f"   ⚠️ 內容格式未知")
            else:
                print(f"   ❌ HTTP錯誤")
                
        except Exception as e:
            print(f"   ❌ 請求失敗: {str(e)[:50]}...")
        
        # 延遲避免被封鎖
        if i < len(test_urls):
            time.sleep(3)

if __name__ == "__main__":
    print("🚀 月營收爬蟲測試程式")
    print("=" * 60)
    
    # 測試 URL 可訪問性
    test_url_accessibility()
    
    # 測試單一類型爬蟲
    test_single_type()
    
    # 測試合併版爬蟲
    test_combined_crawler()
    
    print(f"\n🎉 測試完成！")
