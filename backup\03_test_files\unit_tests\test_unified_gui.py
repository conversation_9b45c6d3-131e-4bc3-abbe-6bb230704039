#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試統一月營收下載器GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import os
import time
from datetime import datetime

class TestUnifiedMonthlyRevenueGUI:
    def __init__(self):
        self.window = tk.Tk()
        self.setup_window()
        self.create_widgets()
            
    def setup_window(self):
        """設置視窗"""
        self.window.title("📊 統一月營收資料下載器 (測試版)")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
    
    def create_widgets(self):
        """創建界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 統一月營收資料下載器", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 下載設定區域
        settings_frame = ttk.LabelFrame(main_frame, text="下載設定", padding="10")
        settings_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)

        # 下載模式選擇
        ttk.Label(settings_frame, text="下載模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.download_mode_var = tk.StringVar(value="single")
        mode_frame = ttk.Frame(settings_frame)
        mode_frame.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        single_radio = ttk.Radiobutton(mode_frame, text="單一股票", variable=self.download_mode_var, 
                                      value="single", command=self.toggle_download_mode)
        single_radio.pack(side=tk.LEFT, padx=(0, 20))
        
        all_radio = ttk.Radiobutton(mode_frame, text="全股票下載", variable=self.download_mode_var, 
                                   value="all", command=self.toggle_download_mode)
        all_radio.pack(side=tk.LEFT)

        # 股票代號輸入（單一股票模式）
        self.stock_input_frame = ttk.Frame(settings_frame)
        self.stock_input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Label(self.stock_input_frame, text="股票代號:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.stock_var = tk.StringVar(value="2330")
        self.stock_entry = ttk.Entry(self.stock_input_frame, textvariable=self.stock_var, width=15)
        self.stock_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        ttk.Label(self.stock_input_frame, text="(例如: 2330, 2317, 2454)",
                 font=('Arial', 9), foreground='gray').grid(row=0, column=2, sticky=tk.W)

        # 日期範圍設定
        self.use_date_range_var = tk.BooleanVar(value=True)
        date_range_check = ttk.Checkbutton(settings_frame, text="📅 指定日期範圍",
                                         variable=self.use_date_range_var,
                                         command=self.toggle_date_range)
        date_range_check.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))

        # 日期輸入框
        self.date_frame = ttk.Frame(settings_frame)
        self.date_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(self.date_frame, text="開始月份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.start_date_var = tk.StringVar(value="2024-01")
        self.start_date_entry = ttk.Entry(self.date_frame, textvariable=self.start_date_var, width=10)
        self.start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(self.date_frame, text="結束月份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.end_date_var = tk.StringVar(value="2025-07")
        self.end_date_entry = ttk.Entry(self.date_frame, textvariable=self.end_date_var, width=10)
        self.end_date_entry.grid(row=0, column=3, sticky=tk.W)

        ttk.Label(self.date_frame, text="(格式: YYYY-MM，如: 2024-01)",
                 font=('Arial', 8), foreground='gray').grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(2, 0))

        # 下載按鈕區域
        download_btn_frame = ttk.Frame(settings_frame)
        download_btn_frame.grid(row=4, column=0, columnspan=3, pady=(15, 0))

        # 主要下載按鈕
        self.download_btn = ttk.Button(download_btn_frame, text="🚀 開始下載",
                                     command=self.start_download)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 測試連接按鈕
        self.test_btn = ttk.Button(download_btn_frame, text="🔧 測試連接",
                                 command=self.test_connection)
        self.test_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 初始化界面狀態
        self.toggle_download_mode()
        self.toggle_date_range()
        
        # 進度顯示區域
        progress_frame = ttk.LabelFrame(main_frame, text="下載進度", padding="10")
        progress_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 狀態標籤
        self.status_var = tk.StringVar(value="準備就緒 - 測試版下載器")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日誌顯示區域
        log_frame = ttk.LabelFrame(main_frame, text="執行日誌", padding="10")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 日誌文字框
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 控制按鈕區域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        # 清除日誌按鈕
        clear_btn = ttk.Button(control_frame, text="🗑️ 清除日誌", command=self.clear_log)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 關閉按鈕
        close_btn = ttk.Button(control_frame, text="❌ 關閉", command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
        
        # 初始化日誌
        self.log_message("✅ 統一月營收下載器已啟動 (測試版)")
    
    def toggle_download_mode(self):
        """切換下載模式"""
        mode = self.download_mode_var.get()
        if mode == "single":
            # 顯示股票輸入框
            for child in self.stock_input_frame.winfo_children():
                child.grid()
        else:
            # 隱藏股票輸入框
            for child in self.stock_input_frame.winfo_children():
                child.grid_remove()
    
    def toggle_date_range(self):
        """切換日期範圍設定的可用性"""
        state = tk.NORMAL if self.use_date_range_var.get() else tk.DISABLED
        for child in self.date_frame.winfo_children():
            if isinstance(child, ttk.Entry):
                child.configure(state=state)
    
    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.window.update_idletasks()
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("📝 日誌已清除")
    
    def test_connection(self):
        """測試連接"""
        self.log_message("🔧 測試連接中...")
        self.status_var.set("測試連接中...")
        
        def test_thread():
            try:
                time.sleep(2)  # 模擬測試過程
                self.log_message("✅ 連接測試成功")
                self.status_var.set("連接測試成功")
            except Exception as e:
                self.log_message(f"❌ 連接測試失敗: {str(e)}")
                self.status_var.set("連接測試失敗")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def start_download(self):
        """開始下載"""
        download_mode = self.download_mode_var.get()
        
        # 檢查輸入
        if download_mode == "single":
            stock_code = self.stock_var.get().strip()
            if not stock_code:
                messagebox.showerror("錯誤", "請輸入股票代號")
                return
        
        # 禁用下載按鈕
        self.download_btn.configure(state=tk.DISABLED)
        self.test_btn.configure(state=tk.DISABLED)
        
        if download_mode == "single":
            self.log_message(f"🚀 開始下載股票 {stock_code} 的月營收資料...")
        else:
            self.log_message("🚀 開始下載全股票月營收資料...")
            
        self.status_var.set("下載中...")
        self.progress_var.set(0)
        
        def download_thread():
            try:
                # 模擬下載過程
                for i in range(101):
                    time.sleep(0.05)
                    self.progress_var.set(i)
                    if i % 20 == 0:
                        self.log_message(f"📊 下載進度: {i}%")
                
                self.log_message("✅ 下載完成 (模擬)")
                self.status_var.set("下載完成")
                
                if download_mode == "single":
                    messagebox.showinfo("成功", f"股票 {stock_code} 的月營收資料下載完成！(模擬)")
                else:
                    messagebox.showinfo("成功", "全股票月營收資料下載完成！(模擬)")
                
            except Exception as e:
                self.log_message(f"❌ 下載失敗: {str(e)}")
                self.status_var.set("下載失敗")
                messagebox.showerror("錯誤", f"下載失敗：\n{str(e)}")
            finally:
                # 重新啟用按鈕
                self.download_btn.configure(state=tk.NORMAL)
                self.test_btn.configure(state=tk.NORMAL)
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def run(self):
        """運行GUI"""
        self.window.mainloop()

if __name__ == "__main__":
    app = TestUnifiedMonthlyRevenueGUI()
    app.run()
