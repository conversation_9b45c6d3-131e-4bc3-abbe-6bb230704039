#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的台灣證交所市場數據爬蟲測試
"""

def test_basic_imports():
    """測試基本模組導入"""
    print("🔍 測試基本模組導入...")
    
    try:
        from market_data_crawler import MarketDataCrawler, TWSEMarketDataAPI
        print("✅ market_data_crawler 模組導入成功")
        
        from twse_market_data_dialog import TWSEMarketDataDialog
        print("✅ twse_market_data_dialog 模組導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_api_basic():
    """測試基本 API 功能"""
    print("\n🔍 測試基本 API 功能...")
    
    try:
        from market_data_crawler import TWSEMarketDataAPI
        
        # 測試簡單的 API 調用
        print("   測試 API 客戶端...")
        api = TWSEMarketDataAPI()
        print("   ✅ API 客戶端創建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API 測試失敗: {e}")
        return False

def test_crawler_basic():
    """測試基本爬蟲功能"""
    print("\n🔍 測試基本爬蟲功能...")
    
    try:
        from market_data_crawler import MarketDataCrawler
        
        # 創建爬蟲實例
        crawler = MarketDataCrawler()
        print("   ✅ 爬蟲實例創建成功")
        
        # 檢查資料庫路徑
        print(f"   📁 資料庫路徑: {crawler.db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬蟲測試失敗: {e}")
        return False

def test_gui_files():
    """測試 GUI 檔案存在"""
    print("\n🔍 測試 GUI 檔案...")
    
    import os
    
    files_to_check = [
        "market_data_crawler.py",
        "twse_market_data_dialog.py",
        "O3mh_gui_v21_optimized.py"
    ]
    
    all_exist = True
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            print(f"   ✅ {file_name} 存在")
        else:
            print(f"   ❌ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主測試函數"""
    print("🧪 台灣證交所市場數據爬蟲簡單測試")
    print("=" * 50)
    
    tests = [
        ("基本模組導入", test_basic_imports),
        ("基本 API 功能", test_api_basic),
        ("基本爬蟲功能", test_crawler_basic),
        ("GUI 檔案檢查", test_gui_files),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}測試通過")
            else:
                print(f"❌ {test_name}測試失敗")
        except Exception as e:
            print(f"❌ {test_name}測試異常: {e}")
    
    print(f"\n📊 測試結果:")
    print(f"   通過: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n🎉 基本測試全部通過！")
        print(f"\n💡 使用方法:")
        print(f"   1. 啟動 GUI 程式: python O3mh_gui_v21_optimized.py")
        print(f"   2. 點擊選單：爬蟲 → 📊 台灣證交所市場數據")
        print(f"   3. 選擇要爬取的數據類型並開始爬取")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關模組")

if __name__ == "__main__":
    main()
