#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的月營收下載器
"""

import os
import sys
import time
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fixed_downloader():
    """測試修復後的下載器"""
    print("🚀 測試修復後的月營收下載器")
    print("=" * 60)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器初始化成功")
        
        # 測試台積電
        stock_id = '2330'
        print(f"\n📊 測試下載 {stock_id} 月營收數據...")
        print("💡 注意：已停用無頭模式，瀏覽器視窗會顯示")
        
        # 執行下載
        result = downloader.download_stock_revenue(stock_id)
        
        if result:
            print(f"✅ 下載成功！獲得 {result} 筆數據")
            return True
        else:
            print("❌ 下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_date_range():
    """測試日期範圍功能"""
    print("\n🚀 測試日期範圍功能")
    print("=" * 60)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器初始化成功")
        
        # 測試台積電，指定日期範圍
        stock_id = '2330'
        start_date = '2024-01'
        end_date = '2025-07'
        
        print(f"\n📊 測試下載 {stock_id} 月營收數據")
        print(f"📅 日期範圍: {start_date} 到 {end_date}")
        
        # 執行下載
        result = downloader.download_stock_revenue(stock_id, start_date, end_date)
        
        if result:
            print(f"✅ 日期範圍下載成功！獲得 {result} 筆數據")
            return True
        else:
            print("❌ 日期範圍下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ 日期範圍測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 修復後月營收下載器測試")
    print("=" * 70)
    
    print("🔧 修復內容:")
    print("• 停用無頭模式，確保頁面正確載入")
    print("• 增強按鈕尋找日誌，便於診斷問題")
    print("• 添加詳細的元素檢查資訊")
    print("• 改善XPath選擇器的錯誤處理")
    
    tests = [
        ("基本下載測試", test_fixed_downloader),
        # ("日期範圍測試", test_with_date_range)  # 可選測試
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n開始測試: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 70)
    print("📋 修復測試總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    if passed >= len(results):
        print("\n🎉 修復測試成功！")
        print("下載器已修復，可以正常使用。")
        
        print("\n💡 使用建議:")
        print("1. 如果仍有問題，檢查日誌中的詳細按鈕資訊")
        print("2. 確保Chrome瀏覽器和ChromeDriver版本匹配")
        print("3. 檢查網路連線和GoodInfo網站可訪問性")
        print("4. 必要時可以暫時停用防毒軟體的網頁保護")
        
    else:
        print("\n⚠️ 仍有問題需要進一步診斷")
        print("請檢查日誌輸出中的詳細錯誤資訊")

if __name__ == "__main__":
    main()
