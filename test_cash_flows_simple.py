#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的現金流量表爬蟲測試
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def simple_test():
    """簡化測試"""
    
    print("=" * 60)
    print("🧪 簡化現金流量表爬蟲測試")
    print("=" * 60)
    
    try:
        # 1. 測試導入
        print("📦 測試模組導入...")
        from crawler import crawl_cash_flows
        print("✅ 成功導入 crawl_cash_flows")
        
        # 2. 測試輔助函數
        print("\n🔧 測試輔助函數...")
        from crawler import get_financial_year_season, get_financial_report_date
        
        test_date = datetime.datetime(2022, 5, 15)
        year, season = get_financial_year_season(test_date)
        report_date = get_financial_report_date(year, season)
        
        print(f"   測試日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"   財報期間: {year}年第{season}季")
        print(f"   發布日期: {report_date}")
        
        # 3. 檢查現有資料
        print("\n📖 檢查現有現金流量表資料...")
        
        # 檢查 DB 檔案
        db_path = os.path.join('history', 'tables', 'cash_flows.db')
        if os.path.exists(db_path):
            print(f"   ✅ 找到現金流量表 DB: {db_path}")
            
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM cash_flows")
            count = cursor.fetchone()[0]
            print(f"   📊 現有資料筆數: {count:,}")
            
            # 檢查日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM cash_flows")
            date_range = cursor.fetchone()
            print(f"   📅 日期範圍: {date_range[0]} ~ {date_range[1]}")
            
            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM cash_flows")
            stock_count = cursor.fetchone()[0]
            print(f"   🏢 股票數量: {stock_count}")
            
            conn.close()
        else:
            print(f"   ⚠️ 未找到現金流量表 DB: {db_path}")
        
        # 檢查 pickle 檔案
        pickle_path = os.path.join('history', 'cash_flows.pkl')
        if os.path.exists(pickle_path):
            print(f"   ✅ 找到現金流量表 pickle: {pickle_path}")
            
            import pandas as pd
            df = pd.read_pickle(pickle_path)
            print(f"   📊 pickle 資料形狀: {df.shape}")
        else:
            print(f"   ⚠️ 未找到現金流量表 pickle: {pickle_path}")
        
        # 4. 測試爬蟲函數 (不實際執行網路請求)
        print("\n🚀 測試爬蟲函數結構...")
        
        # 檢查函數是否可調用
        if callable(crawl_cash_flows):
            print("   ✅ crawl_cash_flows 函數可調用")
            
            # 檢查函數參數
            import inspect
            sig = inspect.signature(crawl_cash_flows)
            print(f"   📋 函數參數: {list(sig.parameters.keys())}")
            
        else:
            print("   ❌ crawl_cash_flows 不是可調用函數")
        
        print("\n✅ 簡化測試完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_update_cash_flows():
    """測試 auto_update 中的現金流量表功能"""
    
    print("\n" + "=" * 60)
    print("🔗 測試 auto_update 現金流量表功能")
    print("=" * 60)
    
    try:
        # 檢查 auto_update.py 的配置
        auto_update_path = 'auto_update.py'
        
        if os.path.exists(auto_update_path):
            with open(auto_update_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查導入
            if 'crawl_cash_flows' in content:
                print("✅ auto_update.py 包含 crawl_cash_flows 導入")
            else:
                print("❌ auto_update.py 缺少 crawl_cash_flows 導入")
            
            # 檢查任務配置
            lines = content.split('\n')
            cash_flows_task_found = False
            cash_flows_task_enabled = False
            
            for line in lines:
                if "'cash_flows'" in line and 'crawl_cash_flows' in line:
                    cash_flows_task_found = True
                    if not line.strip().startswith('#'):
                        cash_flows_task_enabled = True
                    break
            
            if cash_flows_task_found:
                if cash_flows_task_enabled:
                    print("✅ cash_flows 任務已啟用")
                else:
                    print("⚠️ cash_flows 任務存在但被註解")
            else:
                print("❌ 未找到 cash_flows 任務配置")
            
            return cash_flows_task_enabled
        else:
            print("❌ 未找到 auto_update.py 檔案")
            return False
            
    except Exception as e:
        print(f"❌ 檢查 auto_update 配置失敗: {e}")
        return False

def show_usage_instructions():
    """顯示使用說明"""
    
    print("\n" + "=" * 60)
    print("📝 現金流量表爬蟲使用說明")
    print("=" * 60)
    
    print("🚀 啟動方式:")
    print("   1. 單獨執行現金流量表爬蟲:")
    print("      python auto_update.py cash_flows")
    print()
    print("   2. 執行完整自動更新 (包含現金流量表):")
    print("      python auto_update.py")
    print()
    
    print("📊 資料儲存位置:")
    print("   - DB 格式: history/tables/cash_flows.db")
    print("   - Pickle 格式: history/cash_flows.pkl")
    print()
    
    print("🔧 功能特色:")
    print("   ✅ 支援增量更新")
    print("   ✅ 自動判斷財報季別")
    print("   ✅ 從 MOPS 官方網站爬取")
    print("   ✅ 包含完整的現金流量項目")
    print("   ✅ 與現有系統完全整合")
    print()
    
    print("📋 資料內容:")
    print("   - 營業活動現金流量")
    print("   - 投資活動現金流量") 
    print("   - 籌資活動現金流量")
    print("   - 期初/期末現金餘額")
    print("   - 383 個詳細現金流量項目")

def main():
    """主函數"""
    
    # 執行簡化測試
    test1_result = simple_test()
    
    # 測試 auto_update 配置
    test2_result = test_auto_update_cash_flows()
    
    # 顯示使用說明
    show_usage_instructions()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試總結")
    print("=" * 60)
    
    if test1_result and test2_result:
        print("🎉 所有測試通過！現金流量表爬蟲已準備就緒")
        print("💡 建議: 執行 'python auto_update.py cash_flows' 開始爬取")
    elif test1_result:
        print("⚠️ 爬蟲功能正常，但需要啟用 auto_update 任務")
        print("💡 建議: 取消註解 auto_update.py 中的 cash_flows 任務")
    else:
        print("❌ 爬蟲功能有問題，請檢查相關配置")
    
    return test1_result and test2_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
