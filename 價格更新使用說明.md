# 📈 價格爬蟲自動更新使用說明

## 🎯 功能概述

已成功修正 `auto_update.py` 和 `finlab\crawler.py`，現在可以自動從 `newprice.db` 的最新日期開始，持續更新到最新日期。

## 📊 當前狀態

- **資料庫位置**: `D:\Finlab\history\tables\newprice.db`
- **最新日期**: 2022-09-21
- **總記錄數**: 2,269,107 筆
- **需要更新**: 742 個工作日 (從 2022-09-22 到 2025-07-25)

## 🚀 執行方式

### 方法一：更新所有資料表（包含價格）
```bash
python auto_update.py
```

### 方法二：只更新價格資料
```bash
python auto_update.py price
```

## 📋 自動化功能

### ✅ 智能日期檢測
- 自動讀取 `newprice.db` 中的最新日期
- 從最新日期的下一個工作日開始更新
- 自動過濾週末和非交易日
- 更新到今天為止的所有工作日

### ✅ 增量更新
- 不會重複爬取已存在的資料
- 每日資料即時保存到資料庫
- 支援中斷後續傳功能

### ✅ 資料完整性
- 自動添加股票名稱、上市狀態、產業分類
- 過濾權證資料（7xxx 開頭）
- 處理日期格式轉換問題

## 📂 修正的檔案

### 1. `auto_update.py`
- **修正函數**: `get_newprice_incremental_date_range()`
- **改進內容**:
  - 從固定5天測試改為自動更新到最新日期
  - 自動過濾工作日
  - 智能檢測是否需要更新

### 2. `finlab\crawler.py`
- **函數**: `crawl_price(date)`
- **功能確認**:
  - 正確處理單日爬取
  - 添加股票資訊（名稱、狀態、產業）
  - 過濾權證資料

## ⚠️ 注意事項

### 🌐 網路需求
- 確保網路連線穩定
- 爬取過程中會有延遲以避免被封鎖
- 建議在非交易時間執行

### ⏰ 執行時間
- 742 個工作日預計需要數小時完成
- 每日約 0.5-1 秒延遲
- 可以中斷後重新執行（會自動續傳）

### 💾 儲存空間
- 每日約 2,000+ 筆記錄
- 742 天約增加 150 萬筆記錄
- 確保有足夠的磁碟空間

## 📈 執行範例

```bash
# 進入專案目錄
cd D:\Finlab\backup\O3mh_strategy2AA

# 執行價格更新
python auto_update.py price
```

### 預期輸出
```
============================================================
🚀 Finlab 資料庫自動更新系統
============================================================
🔄 開始自動更新...
📊 newprice.db 最後日期: 2022-09-21
🎯 計劃增量更新範圍: 2022-09-22 到 2025-07-26
📅 實際更新工作日數量: 742 天
   從: 2022-09-22 (Thursday)
   到: 2025-07-25 (Friday)

🔄 開始更新 price...
[  1/742]   0.1% 2022-09-22 💾 已保存 2022-09-22 的資料
[  2/742]   0.3% 2022-09-23 💾 已保存 2022-09-23 的資料
...
```

## 🔍 驗證方法

### 檢查更新進度
```python
import sqlite3
import pandas as pd

conn = sqlite3.connect(r'D:\Finlab\history\tables\newprice.db')
query = 'SELECT MAX(date) as latest_date, COUNT(*) as total_records FROM stock_daily_data'
result = pd.read_sql_query(query, conn)
print(f"最新日期: {result.iloc[0]['latest_date']}")
print(f"總記錄數: {result.iloc[0]['total_records']:,}")
conn.close()
```

### 檢查 0050 資料
```python
query_0050 = '''
    SELECT date, [Close], Volume 
    FROM stock_daily_data 
    WHERE stock_id = '0050'
    ORDER BY date DESC
    LIMIT 10
'''
df_0050 = pd.read_sql_query(query_0050, conn)
print(df_0050)
```

## ✅ 完成確認

當看到以下訊息時，表示更新完成：
```
✅ price 即時更新完成 (742 天成功)
📊 更新結果總結
成功更新: 1/1 個資料表
✅ 資料已保存
🎉 更新完成！
```

## 🆘 故障排除

### 常見問題
1. **網路連線問題**: 檢查網路連線，重新執行
2. **權限問題**: 確保對資料庫檔案有寫入權限
3. **磁碟空間不足**: 清理磁碟空間
4. **中斷執行**: 重新執行會自動從中斷處繼續

### 聯絡支援
如有問題，請提供：
- 錯誤訊息
- 執行的命令
- 當前資料庫狀態
