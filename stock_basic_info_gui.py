"""
上市櫃基本資料清單GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import os
from datetime import datetime
from stock_basic_info_crawler import (
    StockBasicInfoDatabase, 
    crawler_stock_basic_info, 
    get_market_types,
    crawler_all_markets
)

class StockBasicInfoGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("上市櫃基本資料清單爬蟲")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化變數
        self.is_running = False
        self.db = StockBasicInfoDatabase()  # 使用預設路徑 D:\Finlab\history\tables\stock_basic_info.db
        
        self.setup_ui()
        self.update_stats()
    
    def setup_ui(self):
        """設置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 上市櫃基本資料清單爬蟲", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 市場別選擇框架
        market_frame = ttk.LabelFrame(main_frame, text="市場別選擇", padding="10")
        market_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        market_frame.columnconfigure(1, weight=1)
        
        # 市場別選項
        ttk.Label(market_frame, text="選擇市場別:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.market_var = tk.StringVar()
        market_combo = ttk.Combobox(market_frame, textvariable=self.market_var, 
                                   values=list(get_market_types().values()), 
                                   state="readonly", width=15)
        market_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        market_combo.set("上市")  # 預設選擇
        
        # 全部市場選項
        self.all_markets_var = tk.BooleanVar()
        all_markets_check = ttk.Checkbutton(market_frame, text="爬取全部市場別",
                                          variable=self.all_markets_var,
                                          command=self.toggle_market_selection)
        all_markets_check.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

        # 範例資料模式選項
        self.sample_data_var = tk.BooleanVar()
        sample_data_check = ttk.Checkbutton(market_frame, text="範例資料模式（測試用）",
                                          variable=self.sample_data_var)
        sample_data_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 控制按鈕框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 開始爬取按鈕
        self.start_button = ttk.Button(control_frame, text="🚀 開始爬取", 
                                      command=self.start_crawling)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按鈕
        self.stop_button = ttk.Button(control_frame, text="⏹️ 停止", 
                                     command=self.stop_crawling, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 匯出CSV按鈕
        export_button = ttk.Button(control_frame, text="📤 匯出CSV", 
                                  command=self.export_csv)
        export_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 開啟資料庫資料夾按鈕
        folder_button = ttk.Button(control_frame, text="📁 開啟資料庫資料夾", 
                                  command=self.open_database_folder)
        folder_button.pack(side=tk.LEFT)
        
        # 進度條
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 統計資訊框架
        stats_frame = ttk.LabelFrame(main_frame, text="資料庫統計", padding="10")
        stats_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure(1, weight=1)
        
        self.stats_text = tk.Text(stats_frame, height=4, width=50)
        self.stats_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 更新統計按鈕
        update_stats_button = ttk.Button(stats_frame, text="🔄 更新統計", 
                                        command=self.update_stats)
        update_stats_button.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # 日誌框架
        log_frame = ttk.LabelFrame(main_frame, text="執行日誌", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 日誌文字區域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日誌按鈕
        clear_log_button = ttk.Button(log_frame, text="🗑️ 清除日誌", 
                                     command=self.clear_log)
        clear_log_button.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
    
    def toggle_market_selection(self):
        """切換市場選擇模式"""
        # 這個功能可以用來控制是否啟用市場別下拉選單
        pass
    
    def log_message(self, message):
        """添加日誌訊息（執行緒安全）"""
        def _log():
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            try:
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)
                self.root.update_idletasks()
            except tk.TclError:
                # GUI 已關閉，忽略錯誤
                pass

        # 確保在主執行緒中執行
        try:
            self.root.after(0, _log)
        except tk.TclError:
            # GUI 已關閉，忽略錯誤
            pass
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
    
    def update_stats(self):
        """更新統計資訊（執行緒安全）"""
        try:
            stats = self.db.get_market_stats()
            total_count = self.db.get_data_count()

            stats_info = f"總計: {total_count} 筆資料\n\n"

            market_names = get_market_types()
            for market_code, count in stats.items():
                market_name = market_names.get(market_code, market_code)
                stats_info += f"{market_name}: {count} 筆\n"

            try:
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(1.0, stats_info)
            except tk.TclError:
                # GUI 已關閉，忽略錯誤
                pass

        except Exception as e:
            self.log_message(f"更新統計失敗: {e}")
    
    def get_selected_market_code(self):
        """取得選中的市場代碼"""
        market_types = get_market_types()
        selected_name = self.market_var.get()
        
        for code, name in market_types.items():
            if name == selected_name:
                return code
        return 'sii'  # 預設返回上市
    
    def start_crawling(self):
        """開始爬取"""
        if self.is_running:
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()
        
        # 在新線程中執行爬取
        thread = threading.Thread(target=self.crawling_worker)
        thread.daemon = True
        thread.start()
    
    def stop_crawling(self):
        """停止爬取（執行緒安全）"""
        self.is_running = False
        try:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.progress.stop()
            self.log_message("⏹️ 爬取已停止")
        except tk.TclError:
            # GUI 已關閉，忽略錯誤
            pass
    
    def crawling_worker(self):
        """爬取工作線程"""
        try:
            # 檢查是否使用範例資料模式
            use_sample = self.sample_data_var.get()
            if use_sample:
                self.log_message("🧪 使用範例資料模式進行測試")

            if self.all_markets_var.get():
                # 爬取全部市場
                mode_text = "範例資料" if use_sample else "實際資料"
                self.log_message(f"🚀 開始爬取全部市場{mode_text}...")
                results = crawler_all_markets(use_sample)

                total_saved = 0
                for market_code, df in results.items():
                    if not self.is_running:
                        break

                    if not df.empty:
                        saved_count = self.db.save_data(df)
                        total_saved += saved_count
                        market_name = get_market_types().get(market_code, market_code)
                        self.log_message(f"✅ {market_name}: 儲存 {saved_count} 筆資料")
                    else:
                        market_name = get_market_types().get(market_code, market_code)
                        self.log_message(f"⚠️ {market_name}: 無資料")

                self.log_message(f"🎉 全部市場爬取完成，總共儲存 {total_saved} 筆資料")
            else:
                # 爬取單一市場
                market_code = self.get_selected_market_code()
                market_name = get_market_types().get(market_code, market_code)

                mode_text = "範例資料" if use_sample else "實際資料"
                self.log_message(f"🚀 開始爬取 {market_name} {mode_text}...")

                df = crawler_stock_basic_info(market_code, use_sample)

                if not df.empty and self.is_running:
                    saved_count = self.db.save_data(df)
                    self.log_message(f"✅ 成功儲存 {saved_count} 筆 {market_name} 資料")
                elif df.empty:
                    self.log_message(f"⚠️ {market_name} 無資料")

                self.log_message(f"🎉 {market_name} 爬取完成")
            
            # 更新統計
            self.root.after(0, self.update_stats)
            
        except Exception as e:
            self.log_message(f"❌ 爬取過程發生錯誤: {e}")
        finally:
            # 確保在主線程中更新UI
            self.root.after(0, self.stop_crawling)
    
    def export_csv(self):
        """匯出CSV"""
        try:
            # 選擇儲存路徑
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="儲存CSV檔案"
            )
            
            if file_path:
                # 檢查是否要匯出特定市場
                if not self.all_markets_var.get():
                    market_code = self.get_selected_market_code()
                    success = self.db.export_to_csv(file_path, market_code)
                else:
                    success = self.db.export_to_csv(file_path)
                
                if success:
                    self.log_message(f"✅ 成功匯出資料到: {file_path}")
                    messagebox.showinfo("匯出成功", f"資料已成功匯出到:\n{file_path}")
                else:
                    self.log_message("❌ 匯出失敗")
                    messagebox.showerror("匯出失敗", "匯出過程中發生錯誤")
        
        except Exception as e:
            self.log_message(f"❌ 匯出錯誤: {e}")
            messagebox.showerror("錯誤", f"匯出過程中發生錯誤:\n{e}")
    
    def open_database_folder(self):
        """開啟資料庫資料夾"""
        try:
            db_dir = os.path.dirname(self.db.db_path)
            if os.path.exists(db_dir):
                os.startfile(db_dir)
                self.log_message(f"📁 已開啟資料庫資料夾: {db_dir}")
            else:
                messagebox.showerror("錯誤", "資料庫資料夾不存在")
        except Exception as e:
            self.log_message(f"❌ 開啟資料夾失敗: {e}")
            messagebox.showerror("錯誤", f"無法開啟資料夾:\n{e}")
    
    def run(self):
        """執行GUI"""
        self.log_message("🎯 上市櫃基本資料清單爬蟲已啟動")
        self.log_message("📋 請選擇市場別，然後點擊「開始爬取」")
        self.log_message("💡 提示：可選擇「爬取全部市場別」一次取得所有資料")
        
        self.root.mainloop()

def main():
    """主函數"""
    app = StockBasicInfoGUI()
    app.run()

if __name__ == "__main__":
    main()
