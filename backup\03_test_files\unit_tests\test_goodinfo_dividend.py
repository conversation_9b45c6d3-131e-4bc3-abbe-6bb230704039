#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試GoodInfo除權息資料爬取功能
專門測試是否能正確獲取包含日期的除權息資料
"""

import sys
import os
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_goodinfo_dividend_crawler():
    """測試GoodInfo除權息爬蟲"""
    print("🧪 測試GoodInfo除權息資料爬取")
    print("=" * 50)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        # 創建爬蟲實例
        crawler = EnhancedDividendCrawler()
        
        # 測試2024年資料
        print("📊 開始測試2024年除權息資料爬取...")
        print("⏰ 這可能需要幾分鐘時間，請耐心等待...")
        
        dividend_data = crawler.fetch_goodinfo_dividend_data(2024)
        
        if dividend_data:
            print(f"\n✅ 成功獲取 {len(dividend_data)} 筆除權息資料")
            
            # 分析資料品質
            with_date_count = 0
            without_date_count = 0
            cash_dividend_count = 0
            stock_dividend_count = 0
            
            print("\n📋 資料樣本 (前10筆):")
            print("-" * 80)
            print(f"{'股票代碼':<8} {'股票名稱':<12} {'除權息日期':<12} {'現金股利':<8} {'股票股利':<8}")
            print("-" * 80)
            
            for i, record in enumerate(dividend_data[:10]):
                stock_code = record['stock_code']
                stock_name = record['stock_name'][:10]  # 限制名稱長度
                ex_date = record.get('ex_dividend_date', '無') or '無'
                cash_div = record['cash_dividend']
                stock_div = record['stock_dividend']
                
                print(f"{stock_code:<8} {stock_name:<12} {ex_date:<12} {cash_div:<8.2f} {stock_div:<8.2f}")
                
                # 統計
                if record.get('ex_dividend_date'):
                    with_date_count += 1
                else:
                    without_date_count += 1
                    
                if record['cash_dividend'] > 0:
                    cash_dividend_count += 1
                if record['stock_dividend'] > 0:
                    stock_dividend_count += 1
            
            # 顯示統計結果
            print("\n📊 資料品質分析:")
            print(f"  • 總筆數: {len(dividend_data)}")
            print(f"  • 有除權息日期: {with_date_count} 筆 ({with_date_count/len(dividend_data)*100:.1f}%)")
            print(f"  • 無除權息日期: {without_date_count} 筆 ({without_date_count/len(dividend_data)*100:.1f}%)")
            print(f"  • 有現金股利: {cash_dividend_count} 筆")
            print(f"  • 有股票股利: {stock_dividend_count} 筆")
            
            # 儲存測試資料
            print("\n💾 儲存測試資料到資料庫...")
            crawler.save_dividend_data(dividend_data)
            
            # 匯出CSV檔案
            print("📁 匯出CSV檔案...")
            csv_file = crawler.export_to_csv(2024)
            if csv_file:
                print(f"✅ CSV檔案已匯出: {csv_file}")
            
            return True
            
        else:
            print("❌ 未獲取到任何除權息資料")
            print("💡 可能原因:")
            print("  • 網路連線問題")
            print("  • GoodInfo網站結構變更")
            print("  • Chrome瀏覽器或WebDriver問題")
            return False
            
    except ImportError as e:
        print(f"❌ 模組載入失敗: {e}")
        print("💡 請確認enhanced_dividend_crawler.py檔案存在")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        return False

def test_specific_stocks():
    """測試特定股票的除權息資料"""
    print("\n🎯 測試特定股票除權息查詢")
    print("=" * 40)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        crawler = EnhancedDividendCrawler()
        
        # 測試知名股票
        test_stocks = ['2330', '2317', '2454', '2412']  # 台積電、鴻海、聯發科、中華電
        
        for stock_code in test_stocks:
            print(f"\n📊 查詢 {stock_code} 的歷史除權息資料...")
            history = crawler.get_stock_dividend_history(stock_code, years=3)
            
            if history:
                print(f"✅ 找到 {len(history)} 筆歷史資料:")
                for record in history:
                    ex_date = record.get('ex_dividend_date', '無日期')
                    cash_div = record['cash_dividend']
                    stock_div = record['stock_dividend']
                    print(f"  • {record['year']}: 除權息日期={ex_date}, 現金股利={cash_div}, 股票股利={stock_div}")
            else:
                print(f"⚠️ 未找到 {stock_code} 的歷史資料")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定股票查詢失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🔧 GoodInfo除權息資料爬取測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 檢查Chrome瀏覽器
    print("🌐 檢查Chrome瀏覽器環境...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("✅ Chrome瀏覽器環境正常")
        
    except Exception as e:
        print(f"❌ Chrome瀏覽器環境異常: {e}")
        print("💡 請確認已安裝Chrome瀏覽器和ChromeDriver")
        return False
    
    # 執行測試
    test_results = []
    
    print("\n" + "="*60)
    result1 = test_goodinfo_dividend_crawler()
    test_results.append(("GoodInfo爬蟲測試", result1))
    
    print("\n" + "="*60)
    result2 = test_specific_stocks()
    test_results.append(("特定股票查詢測試", result2))
    
    # 總結
    print("\n" + "="*60)
    print("📊 測試結果總結")
    print("="*30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(test_results)} 項測試通過")
    
    if passed == len(test_results):
        print("🎉 所有測試通過！GoodInfo除權息爬蟲功能正常")
        print("💡 現在可以在主程式中使用除權息下載功能")
    else:
        print("⚠️ 部分測試失敗，請檢查相關設定")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*60)
    if success:
        print("🚀 測試成功！可以開始使用除權息下載功能")
    else:
        print("🔧 請根據錯誤訊息修正問題後重新測試")
    
    input("\n按 Enter 鍵結束...")
