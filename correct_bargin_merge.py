#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正確合併 bargin_report 檔案
"""

import pandas as pd
import os
import shutil
from datetime import datetime
import gc

def correct_merge():
    """正確合併 bargin_report 檔案"""
    print("🔧 正確合併 bargin_report 檔案")
    print("=" * 60)
    
    source_dir = r"D:\□Finlab學習\用 Python 理財：打造自己的 AI 股票理專_原始檔案\202507_新資料庫\history\tables"
    target_file = "history/tables/bargin_report.pkl"
    
    main_file = os.path.join(source_dir, "bargin_report.pkl")
    old_file = os.path.join(source_dir, "bargin_report_old.pkl")
    
    try:
        # 創建備份
        if os.path.exists(target_file):
            backup_file = f"{target_file}_incorrect_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            shutil.copy2(target_file, backup_file)
            print(f"✅ 備份錯誤檔案: {backup_file}")
        
        # 讀取兩個檔案
        print("📖 讀取 bargin_report_old.pkl...")
        old_data = pd.read_pickle(old_file)
        old_dates = old_data.index.get_level_values('date')
        print(f"   舊檔案: {len(old_data):,} 筆 ({old_dates.min()} 至 {old_dates.max()})")
        
        print("📖 讀取 bargin_report.pkl...")
        main_data = pd.read_pickle(main_file)
        main_dates = main_data.index.get_level_values('date')
        print(f"   主檔案: {len(main_data):,} 筆 ({main_dates.min()} 至 {main_dates.max()})")
        
        # 找出主檔案中新增的部分
        old_end_date = old_dates.max()
        print(f"\n🔍 分析新增資料...")
        print(f"   舊檔案結束日期: {old_end_date}")
        
        # 取得主檔案中比舊檔案更新的資料
        new_data = main_data[main_data.index.get_level_values('date') > old_end_date]
        new_dates = new_data.index.get_level_values('date')
        
        if len(new_data) > 0:
            print(f"   ✅ 找到新增資料: {len(new_data):,} 筆")
            print(f"   新增日期範圍: {new_dates.min()} 至 {new_dates.max()}")

            # 合併舊資料和新資料
            print("🔄 合併資料...")
            combined_data = pd.concat([old_data, new_data])
        else:
            print("   ⚠️ 沒有找到新增資料")
            print("   可能主檔案沒有比舊檔案更新的資料")

            # 檢查是否主檔案有更完整的資料
            main_unique_dates = set(main_dates)
            old_unique_dates = set(old_dates)

            if len(main_unique_dates) > len(old_unique_dates):
                print("   🔄 使用主檔案作為基礎...")
                combined_data = main_data.copy()
            else:
                print("   🔄 使用舊檔案作為基礎...")
                combined_data = old_data.copy()
        
        # 去重並排序
        print("🔄 去重並排序...")
        combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
        combined_data = combined_data.sort_index()
        
        final_dates = combined_data.index.get_level_values('date')
        print(f"✅ 合併完成:")
        print(f"   最終筆數: {len(combined_data):,}")
        print(f"   日期範圍: {final_dates.min()} 至 {final_dates.max()}")
        print(f"   唯一日期數: {len(final_dates.unique())}")
        
        # 保存結果
        print("💾 保存合併結果...")
        combined_data.to_pickle(target_file)
        
        # 驗證結果
        final_size = os.path.getsize(target_file)
        print(f"✅ 保存完成:")
        print(f"   檔案大小: {final_size:,} bytes ({final_size/1024/1024:.1f} MB)")
        print(f"   檔案位置: {target_file}")
        
        # 更新日期記錄
        print("🔄 更新日期範圍記錄...")
        date_range_file = "history/date_range.pickle"
        
        if os.path.exists(date_range_file):
            import pickle
            with open(date_range_file, 'rb') as f:
                date_ranges = pickle.load(f)
        else:
            date_ranges = {}
        
        date_ranges['bargin_report'] = (final_dates.min(), final_dates.max())
        
        with open(date_range_file, 'wb') as f:
            pickle.dump(date_ranges, f)
        
        print(f"✅ 日期範圍已更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("🔧 Bargin Report 正確合併工具")
    print("=" * 60)
    print("🎯 目標: 正確合併兩個 bargin_report 檔案")
    print("💡 策略: 舊檔案 + 主檔案的新增部分")
    print("=" * 60)
    
    response = input("是否開始正確合併？(y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    success = correct_merge()
    
    if success:
        print("\n🎉 正確合併完成！")
        print("💡 現在應該有完整的 bargin_report 資料了")
        print("📊 檔案大小應該接近 300MB")
    else:
        print("\n❌ 合併失敗")

if __name__ == "__main__":
    main()
