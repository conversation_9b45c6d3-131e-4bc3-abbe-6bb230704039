#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試公開資訊觀測站批量月營收下載功能
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import re
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_mops_bulk_download():
    """測試公開資訊觀測站批量下載"""
    
    print("🧪 測試公開資訊觀測站批量月營收下載")
    print("=" * 60)
    
    # 測試參數：2024年6月上市公司本國公司
    year = 2024
    month = 6
    market = 'sii'  # sii=上市, otc=上櫃, rotc=興櫃, pub=公開發行
    company_type = 0  # 0=本國公司, 1=外國公司
    
    # 轉換為民國年
    roc_year = year - 1911
    
    # 構建URL（使用新的網址格式）
    url = f"https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{roc_year}_{month}_{company_type}.html"
    
    print(f"📋 測試參數:")
    print(f"  年度: {year} (民國{roc_year}年)")
    print(f"  月份: {month}")
    print(f"  市場: {market} ({'上市' if market == 'sii' else '上櫃' if market == 'otc' else '興櫃' if market == 'rotc' else '公開發行'})")
    print(f"  公司類型: {company_type} ({'本國公司' if company_type == 0 else '外國公司'})")
    print(f"📡 URL: {url}")
    print()
    
    try:
        print("🚀 正在下載數據...")
        
        # 發送請求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        response.encoding = 'big5'  # 公開資訊觀測站使用Big5編碼
        
        if response.status_code == 200:
            print("✅ 網頁下載成功")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尋找表格
            tables = soup.find_all('table')
            print(f"🔍 找到 {len(tables)} 個表格")
            
            # 尋找包含營收數據的表格
            revenue_data = []
            
            for table_idx, table in enumerate(tables):
                rows = table.find_all('tr')
                print(f"  表格 {table_idx + 1}: {len(rows)} 行")
                
                # 檢查是否為營收數據表格
                if len(rows) > 10:  # 營收表格應該有很多行
                    for row_idx, row in enumerate(rows):
                        cells = row.find_all(['td', 'th'])
                        
                        if len(cells) >= 3:  # 至少要有股票代號、名稱、營收
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            
                            # 檢查是否為數據行（第一欄是股票代號）
                            if len(cell_texts) > 0 and re.match(r'^\d{4}$', cell_texts[0]):
                                try:
                                    stock_id = cell_texts[0]
                                    stock_name = cell_texts[1] if len(cell_texts) > 1 else ""
                                    revenue_str = cell_texts[2] if len(cell_texts) > 2 else "0"
                                    
                                    # 清理營收數字（移除逗號等）
                                    revenue_clean = re.sub(r'[,\s]', '', revenue_str)
                                    revenue = int(revenue_clean) if revenue_clean.isdigit() else 0
                                    
                                    if revenue > 0:  # 只記錄有營收的公司
                                        revenue_data.append({
                                            'stock_id': stock_id,
                                            'stock_name': stock_name,
                                            'revenue': revenue,
                                            'year': year,
                                            'month': month
                                        })
                                        
                                        if len(revenue_data) <= 10:  # 只顯示前10筆作為示例
                                            print(f"    📊 {stock_id} {stock_name}: {revenue:,}")
                                            
                                except (ValueError, IndexError) as e:
                                    continue
            
            print(f"\n✅ 解析完成！共找到 {len(revenue_data)} 筆營收數據")
            
            if revenue_data:
                # 顯示統計信息
                total_revenue = sum(item['revenue'] for item in revenue_data)
                avg_revenue = total_revenue / len(revenue_data)
                
                print(f"📈 統計信息:")
                print(f"  總營收: {total_revenue:,} 千元")
                print(f"  平均營收: {avg_revenue:,.0f} 千元")
                print(f"  最高營收: {max(revenue_data, key=lambda x: x['revenue'])['stock_name']} ({max(item['revenue'] for item in revenue_data):,})")
                print(f"  最低營收: {min(revenue_data, key=lambda x: x['revenue'])['stock_name']} ({min(item['revenue'] for item in revenue_data):,})")
                
                # 保存為CSV測試
                df = pd.DataFrame(revenue_data)
                csv_file = f"mops_revenue_{year}_{month:02d}_{market}_test.csv"
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                print(f"💾 測試數據已保存到: {csv_file}")
                
                return True, revenue_data
            else:
                print("❌ 未找到營收數據")
                return False, []
                
        else:
            print(f"❌ 網頁下載失敗，狀態碼: {response.status_code}")
            return False, []
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        return False, []

def test_multiple_markets():
    """測試多個市場的數據下載"""
    
    print("\n🔄 測試多個市場數據下載")
    print("=" * 60)
    
    markets = {
        'sii': '上市',
        'otc': '上櫃',
        'rotc': '興櫃'
    }
    
    year = 2024
    month = 6
    
    all_results = {}
    
    for market_code, market_name in markets.items():
        print(f"\n📊 測試 {market_name} 市場...")
        
        roc_year = year - 1911
        url = f"https://mopsov.twse.com.tw/nas/t21/{market_code}/t21sc03_{roc_year}_{month}_0.html"
        
        try:
            response = requests.get(url, timeout=10, verify=False)
            response.encoding = 'big5'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                tables = soup.find_all('table')
                
                revenue_count = 0
                for table in tables:
                    rows = table.find_all('tr')
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 3:
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            if len(cell_texts) > 0 and re.match(r'^\d{4}$', cell_texts[0]):
                                revenue_count += 1
                
                all_results[market_code] = revenue_count
                print(f"  ✅ {market_name}: {revenue_count} 家公司")
            else:
                print(f"  ❌ {market_name}: 下載失敗 (狀態碼: {response.status_code})")
                all_results[market_code] = 0
                
        except Exception as e:
            print(f"  ❌ {market_name}: 錯誤 - {e}")
            all_results[market_code] = 0
    
    print(f"\n📊 總結:")
    total_companies = sum(all_results.values())
    for market_code, count in all_results.items():
        market_name = markets[market_code]
        print(f"  {market_name}: {count} 家公司")
    print(f"  總計: {total_companies} 家公司")
    
    return all_results

if __name__ == "__main__":
    # 測試單一市場
    success, data = test_mops_bulk_download()
    
    if success:
        print("\n🎉 單一市場測試成功！")
        
        # 測試多個市場
        results = test_multiple_markets()
        
        if sum(results.values()) > 0:
            print("\n🎉 多市場測試成功！")
            print("\n✅ 公開資訊觀測站批量下載功能可用")
            print("💡 建議整合到月營收下載器中")
        else:
            print("\n⚠️ 多市場測試部分失敗")
    else:
        print("\n❌ 測試失敗，請檢查網址或網路連接")
