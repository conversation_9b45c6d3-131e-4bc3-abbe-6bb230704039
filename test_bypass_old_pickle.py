#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試繞過舊 pickle 檔案的方案
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd
import shutil

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_read_current_pickle():
    """測試讀取當前的 price.pkl"""
    print("🧪 測試讀取當前的 price.pkl...")
    
    price_file = "history/tables/price.pkl"
    
    if not os.path.exists(price_file):
        print("❌ price.pkl 不存在")
        return False
    
    try:
        print("1️⃣ 嘗試 pd.read_pickle...")
        data = pd.read_pickle(price_file)
        print(f"✅ pd.read_pickle 成功，{len(data)} 筆資料")
        return True
        
    except Exception as e:
        print(f"❌ pd.read_pickle 失敗: {str(e)}")
        
        try:
            print("2️⃣ 嘗試 pickle.load...")
            import pickle
            with open(price_file, 'rb') as f:
                data = pickle.load(f)
            print(f"✅ pickle.load 成功，{len(data)} 筆資料")
            return True
            
        except Exception as e2:
            print(f"❌ pickle.load 也失敗: {str(e2)}")
            return False

def create_backup_and_new_file():
    """創建備份並建立新檔案"""
    print("\n🧪 創建備份並建立新檔案...")
    
    price_file = "history/tables/price.pkl"
    backup_file = "history/tables/price_backup.pkl"
    
    if not os.path.exists(price_file):
        print("❌ price.pkl 不存在")
        return False
    
    try:
        # 創建備份
        print("1️⃣ 創建備份...")
        shutil.copy2(price_file, backup_file)
        print(f"✅ 備份創建成功: {backup_file}")
        
        # 嘗試讀取並重新保存
        print("2️⃣ 嘗試讀取並重新保存...")
        
        # 先嘗試讀取
        try:
            data = pd.read_pickle(price_file)
            print(f"✅ 讀取成功，{len(data)} 筆資料")
            
            # 重新保存
            new_file = "history/tables/price_new.pkl"
            data.to_pickle(new_file)
            print(f"✅ 重新保存成功: {new_file}")
            
            # 驗證新檔案
            test_data = pd.read_pickle(new_file)
            print(f"✅ 新檔案驗證成功，{len(test_data)} 筆資料")
            
            return True
            
        except Exception as e:
            print(f"❌ 處理失敗: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ 備份失敗: {str(e)}")
        return False

def test_simple_to_pickle():
    """測試簡化的 to_pickle 函數"""
    print("\n🧪 測試簡化的 to_pickle 函數...")
    
    try:
        # 創建測試資料
        test_data = pd.DataFrame({
            'value': [1, 2, 3, 4, 5]
        })
        test_data['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03', '2022-01-04', '2022-01-05'])
        test_data['stock_id'] = ['TEST1', 'TEST2', 'TEST3', 'TEST4', 'TEST5']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 簡化的存檔邏輯
        test_file = "history/tables/test_simple.pkl"
        
        if os.path.exists(test_file):
            print("讀取現有檔案...")
            try:
                old_data = pd.read_pickle(test_file)
                combined = pd.concat([old_data, test_data])
                combined = combined.drop_duplicates()
                combined = combined.sort_index()
                print(f"合併後: {len(combined)} 筆")
            except Exception as e:
                print(f"讀取失敗，創建新檔案: {str(e)}")
                combined = test_data
        else:
            print("創建新檔案...")
            combined = test_data
        
        # 保存
        combined.to_pickle(test_file)
        print("✅ 保存成功")
        
        # 驗證
        verify_data = pd.read_pickle(test_file)
        print(f"✅ 驗證成功，{len(verify_data)} 筆資料")
        
        # 清理
        try:
            os.remove(test_file)
            print("🧹 清理測試檔案")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 簡化測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 繞過舊 pickle 檔案測試")
    print("=" * 60)
    
    # 測試1: 讀取當前檔案
    test1 = test_read_current_pickle()
    
    # 測試2: 簡化的 to_pickle
    test2 = test_simple_to_pickle()
    
    # 測試3: 備份和重建（如果需要）
    test3 = True
    if not test1:
        test3 = create_backup_and_new_file()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    print(f"讀取當前檔案: {'✅ 成功' if test1 else '❌ 失敗'}")
    print(f"簡化 to_pickle: {'✅ 成功' if test2 else '❌ 失敗'}")
    print(f"備份和重建: {'✅ 成功' if test3 else '❌ 失敗'}")
    
    if not test1:
        print("\n⚠️ 當前 price.pkl 檔案有兼容性問題")
        print("💡 建議方案:")
        print("   1. 備份現有檔案")
        print("   2. 重新創建兼容的檔案")
        print("   3. 或者使用新的檔案名稱")
    
    if test2:
        print("\n✅ 簡化的存檔邏輯正常")
        print("🚀 可以考慮使用新的存檔策略")
    
    return test1 and test2

if __name__ == "__main__":
    main()
