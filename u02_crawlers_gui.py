#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab 爬蟲系統 GUI 界面

提供圖形化界面來選擇和執行各種爬蟲功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                            QTextEdit, QProgressBar, QGroupBox, QFrame,
                            QMessageBox, QDateEdit, QSpinBox, QComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class CrawlerWorker(QThread):
    """爬蟲工作線程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(str, bool)
    
    def __init__(self, crawler_type, crawler_func, params=None):
        super().__init__()
        self.crawler_type = crawler_type
        self.crawler_func = crawler_func
        self.params = params or {}
    
    def run(self):
        try:
            self.progress.emit(f"🔄 開始執行 {self.crawler_type} 爬蟲...")
            
            # 根據爬蟲類型調用相應的函數
            if self.crawler_type in ["股價資料", "三大法人", "本益比", "大盤指數"]:
                # 這些爬蟲函數只需要一個日期參數
                test_date = self.params.get('end_date', datetime.now() - timedelta(days=1))
                result = self.crawler_func(test_date)

            elif self.crawler_type == "月營收":
                # 月營收爬蟲也只需要一個日期參數
                test_date = self.params.get('end_date', datetime.now() - timedelta(days=1))
                result = self.crawler_func(test_date)

            elif self.crawler_type == "財務報表":
                # 財務報表爬蟲需要一個日期參數
                test_date = self.params.get('end_date', datetime.now() - timedelta(days=1))
                result = self.crawler_func(test_date)

            else:  # 除權息、減資
                result = self.crawler_func()
            
            if hasattr(result, 'shape'):
                message = f"✅ {self.crawler_type} 爬蟲執行完成\n📊 獲取資料筆數: {result.shape[0]} 筆"
            else:
                message = f"✅ {self.crawler_type} 爬蟲執行完成"
                
            self.finished.emit(message, True)
            
        except Exception as e:
            error_msg = f"❌ {self.crawler_type} 爬蟲執行失敗: {str(e)}"
            self.finished.emit(error_msg, False)

class FinlabCrawlerGUI(QMainWindow):
    """Finlab 爬蟲系統主界面"""
    
    def __init__(self):
        super().__init__()
        self.crawler_funcs = None
        self.current_worker = None
        self.init_ui()
        self.init_crawlers()
    
    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle("🚀 Finlab 新版資料庫爬蟲系統")
        self.setGeometry(100, 100, 1000, 700)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
                color: #000000;
            }
            QWidget {
                background-color: #ffffff;
                color: #000000;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 3px solid #000000;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: #ffffff;
                color: #000000;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
                color: #000000;
                font-weight: bold;
                font-size: 14px;
            }
            QLabel {
                color: #000000;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
            }
            QDateEdit, QSpinBox, QComboBox {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #000000;
                padding: 5px;
                font-size: 11px;
                font-weight: bold;
            }
            QDateEdit::drop-down, QSpinBox::up-button, QSpinBox::down-button, QComboBox::drop-down {
                background-color: #f0f0f0;
                border: 1px solid #000000;
            }
            QPushButton {
                background-color: #4CAF50;
                border: 2px solid #000000;
                color: #000000;
                padding: 8px 16px;
                text-align: center;
                font-size: 12px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
                color: #ffffff;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #666666;
                border: 2px solid #cccccc;
            }
        """)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🚀 Finlab 新版資料庫爬蟲系統")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 創建爬蟲按鈕區域
        self.create_crawler_buttons(main_layout)
        
        # 創建參數設置區域
        self.create_parameter_section(main_layout)
        
        # 創建日誌顯示區域
        self.create_log_section(main_layout)
        
        # 創建進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def create_crawler_buttons(self, main_layout):
        """創建爬蟲功能按鈕"""
        crawler_group = QGroupBox("📊 可用的爬蟲功能")
        crawler_layout = QGridLayout(crawler_group)
        
        # 爬蟲功能列表 - 使用高對比度顏色
        self.crawlers = [
            ("股價資料", "price", "#2E86AB"),
            ("三大法人", "bargin_report", "#A23B72"),
            ("本益比", "pe", "#F18F01"),
            ("月營收", "monthly_report", "#C73E1D"),
            ("大盤指數", "benchmark", "#7209B7"),
            ("財務報表", "financial_statement", "#264653"),
            ("上市除權息", "twse_divide_ratio", "#2A9D8F"),
            ("上櫃除權息", "otc_divide_ratio", "#E76F51"),
            ("上市減資", "twse_cap_reduction", "#6A4C93"),
            ("上櫃減資", "otc_cap_reduction", "#1B4332")
        ]
        
        self.crawler_buttons = {}
        
        # 創建按鈕（2列5行）
        for i, (name, key, color) in enumerate(self.crawlers):
            btn = QPushButton(f"{i+1}. {name}")
            btn.setMinimumHeight(50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 3px solid #000000;
                    color: #ffffff;
                    padding: 12px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    border-radius: 8px;
                }}
                QPushButton:hover {{
                    background-color: #ffffff;
                    color: #000000;
                    border: 3px solid #000000;
                }}
                QPushButton:pressed {{
                    background-color: #000000;
                    color: #ffffff;
                    border: 3px solid #ffffff;
                }}
                QPushButton:disabled {{
                    background-color: #e0e0e0;
                    color: #666666;
                    border: 3px solid #999999;
                }}
            """)
            btn.clicked.connect(lambda checked, n=name, k=key: self.run_crawler(n, k))
            
            row = i // 5
            col = i % 5
            crawler_layout.addWidget(btn, row, col)
            self.crawler_buttons[key] = btn
        
        main_layout.addWidget(crawler_group)
    
    def create_parameter_section(self, main_layout):
        """創建參數設置區域"""
        param_group = QGroupBox("⚙️ 參數設置")
        param_layout = QHBoxLayout(param_group)
        
        # 日期範圍設置
        date_frame = QFrame()
        date_layout = QVBoxLayout(date_frame)
        
        date_layout.addWidget(QLabel("📅 日期範圍:"))
        
        date_range_layout = QHBoxLayout()
        date_range_layout.addWidget(QLabel("開始日期:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        date_range_layout.addWidget(self.start_date)
        
        date_range_layout.addWidget(QLabel("結束日期:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        date_range_layout.addWidget(self.end_date)
        
        date_layout.addLayout(date_range_layout)
        param_layout.addWidget(date_frame)
        
        # 財務報表參數
        finance_frame = QFrame()
        finance_layout = QVBoxLayout(finance_frame)
        
        finance_layout.addWidget(QLabel("📈 財務報表參數:"))
        
        finance_param_layout = QGridLayout()
        finance_param_layout.addWidget(QLabel("起始年度:"), 0, 0)
        self.start_year = QSpinBox()
        self.start_year.setRange(2000, 2030)
        self.start_year.setValue(datetime.now().year - 1)
        finance_param_layout.addWidget(self.start_year, 0, 1)
        
        finance_param_layout.addWidget(QLabel("結束年度:"), 0, 2)
        self.end_year = QSpinBox()
        self.end_year.setRange(2000, 2030)
        self.end_year.setValue(datetime.now().year)
        finance_param_layout.addWidget(self.end_year, 0, 3)
        
        finance_param_layout.addWidget(QLabel("起始季度:"), 1, 0)
        self.start_season = QComboBox()
        self.start_season.addItems(["1", "2", "3", "4"])
        finance_param_layout.addWidget(self.start_season, 1, 1)
        
        finance_param_layout.addWidget(QLabel("結束季度:"), 1, 2)
        self.end_season = QComboBox()
        self.end_season.addItems(["1", "2", "3", "4"])
        self.end_season.setCurrentText("4")
        finance_param_layout.addWidget(self.end_season, 1, 3)
        
        finance_layout.addLayout(finance_param_layout)
        param_layout.addWidget(finance_frame)
        
        main_layout.addWidget(param_group)
    
    def create_log_section(self, main_layout):
        """創建日誌顯示區域"""
        log_group = QGroupBox("📋 執行日誌")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                border: 1px solid #555555;
                border-radius: 4px;
            }
        """)
        
        log_layout.addWidget(self.log_text)
        
        # 清除日誌按鈕
        clear_btn = QPushButton("🗑️ 清除日誌")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                max-width: 100px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        clear_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_btn)
        
        main_layout.addWidget(log_group)
        
        # 初始化日誌
        self.add_log("🚀 Finlab 爬蟲系統已啟動")
    
    def init_crawlers(self):
        """初始化爬蟲功能"""
        try:
            # 檢查依賴
            self.check_dependencies()
            
            # 嘗試載入爬蟲模組
            self.load_crawler_modules()
            
        except Exception as e:
            self.add_log(f"❌ 初始化失敗: {str(e)}")
            QMessageBox.critical(self, "初始化錯誤", f"爬蟲系統初始化失敗:\n{str(e)}")
    
    def check_dependencies(self):
        """檢查依賴套件"""
        required_packages = ['pandas', 'requests', 'lxml']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            msg = f"缺少必要依賴: {', '.join(missing_packages)}\n請安裝: pip install {' '.join(missing_packages)}"
            self.add_log(f"❌ {msg}")
            QMessageBox.warning(self, "依賴檢查", msg)
            return False
        
        self.add_log("✅ 依賴檢查通過")
        return True
    
    def load_crawler_modules(self):
        """載入爬蟲模組"""
        try:
            # 嘗試使用安裝版 finlab
            from finlab.crawler import (
                crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
                crawl_finance_statement_by_date, crawl_twse_divide_ratio,
                crawl_otc_divide_ratio, crawl_twse_cap_reduction,
                crawl_otc_cap_reduction, crawl_benchmark
            )
            
            self.crawler_funcs = {
                'price': crawl_price,
                'bargin_report': crawl_bargin,
                'pe': crawl_pe,
                'monthly_report': crawl_monthly_report,
                'benchmark': crawl_benchmark,
                'financial_statement': crawl_finance_statement_by_date,
                'twse_divide_ratio': crawl_twse_divide_ratio,
                'otc_divide_ratio': crawl_otc_divide_ratio,
                'twse_cap_reduction': crawl_twse_cap_reduction,
                'otc_cap_reduction': crawl_otc_cap_reduction,
            }
            
            self.add_log("✅ 安裝版 Finlab 爬蟲模組載入成功")
            
        except ImportError:
            # 使用簡化版本
            self.crawler_funcs = self.create_simple_crawlers()
            self.add_log("⚠️ 使用簡化版爬蟲功能")
            self.add_log("💡 如需完整功能，請安裝: pip install finlab")
    
    def create_simple_crawlers(self):
        """創建簡化版爬蟲函數"""
        import pandas as pd
        
        def simple_crawler(*args, **kwargs):
            return pd.DataFrame({'message': ['簡化版功能 - 需要完整的 finlab 模組']})
        
        return {key: simple_crawler for key in [
            'price', 'bargin_report', 'pe', 'monthly_report', 'benchmark',
            'financial_statement', 'twse_divide_ratio', 'otc_divide_ratio',
            'twse_cap_reduction', 'otc_cap_reduction'
        ]}
    
    def run_crawler(self, crawler_name, crawler_key):
        """執行爬蟲"""
        if self.current_worker and self.current_worker.isRunning():
            QMessageBox.warning(self, "執行中", "已有爬蟲正在執行中，請等待完成")
            return
        
        if not self.crawler_funcs:
            QMessageBox.critical(self, "錯誤", "爬蟲模組未正確載入")
            return
        
        # 準備參數
        params = {}
        if crawler_key in ['price', 'bargin_report', 'pe', 'benchmark']:
            params['start_date'] = self.start_date.date().toPython()
            params['end_date'] = self.end_date.date().toPython()
        elif crawler_key == 'monthly_report':
            params['start_date'] = self.start_date.date().toPython()
            params['end_date'] = self.end_date.date().toPython()
        elif crawler_key == 'financial_statement':
            params['year1'] = self.start_year.value()
            params['year2'] = self.end_year.value()
            params['season1'] = int(self.start_season.currentText())
            params['season2'] = int(self.end_season.currentText())
        
        # 禁用所有按鈕
        self.set_buttons_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 無限進度條
        
        # 創建工作線程
        crawler_func = self.crawler_funcs[crawler_key]
        self.current_worker = CrawlerWorker(crawler_name, crawler_func, params)
        self.current_worker.progress.connect(self.add_log)
        self.current_worker.finished.connect(self.on_crawler_finished)
        self.current_worker.start()
    
    def on_crawler_finished(self, message, success):
        """爬蟲完成回調"""
        self.add_log(message)
        self.set_buttons_enabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "執行完成", message)
        else:
            QMessageBox.critical(self, "執行失敗", message)
    
    def set_buttons_enabled(self, enabled):
        """設置按鈕啟用狀態"""
        for btn in self.crawler_buttons.values():
            btn.setEnabled(enabled)
    
    def add_log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 自動滾動到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.clear()
        self.add_log("📋 日誌已清除")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("Finlab 爬蟲系統")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = FinlabCrawlerGUI()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
