#!/usr/bin/env python3
"""
增強版市場掃描器
整合多個數據源，解決Yahoo台灣DNS問題
優先使用yfinance，備用多個數據源
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
import random
import re
import threading
import yfinance as yf
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

class EnhancedMarketScanner:
    """增強版市場掃描器 - 多數據源整合"""
    
    def __init__(self):
        """初始化掃描器"""
        self.session = requests.Session()
        
        # 🛡️ 反爬蟲機制
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0'
        }
        
        # 緩存機制
        self.cache = {}
        self.cache_timeout = 180  # 3分鐘緩存
        
        # 線程控制 - 降低並發數避免頻率限制
        self.max_workers = 2  # 減少並發數避免429錯誤
        self.request_delay = 0.8  # 增加延遲避免頻率限制
        
        # 數據源優先級（yfinance優先）
        self.data_sources = [
            'yfinance',
            'yahoo_us',
            'investing_com',
            'yahoo_tw_backup',
            'twse_api'
        ]
        
        # 失敗的數據源記錄
        self.failed_sources = set()
        
        logging.info("✅ 增強版市場掃描器初始化完成")
    
    def get_random_headers(self) -> Dict[str, str]:
        """獲取隨機請求標頭"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)
        return headers
    
    def make_safe_request(self, url: str, timeout: int = 5, **kwargs) -> Optional[requests.Response]:
        """安全請求方法 - 縮短超時時間"""
        try:
            headers = self.get_random_headers()
            response = self.session.get(url, headers=headers, timeout=timeout, **kwargs)
            
            # 隨機延遲
            time.sleep(random.uniform(0.1, 0.3))
            
            return response
        except Exception as e:
            logging.debug(f"請求失敗 {url}: {e}")
            return None
    
    def get_yfinance_data(self, symbol: str, retry_count: int = 3) -> Optional[Dict]:
        """🚀 優先使用yfinance獲取數據 - 最可靠的數據源"""
        for attempt in range(retry_count):
            try:
                # 檢查緩存
                cache_key = f"yf_{symbol}"
                if cache_key in self.cache:
                    cache_time, data = self.cache[cache_key]
                    if time.time() - cache_time < self.cache_timeout:
                        return data

                # 台股代碼格式
                if symbol.startswith('^'):
                    ticker_symbol = symbol  # 指數代碼
                else:
                    # 直接使用.TW後綴，避免多次嘗試
                    ticker_symbol = f"{symbol}.TW"

                # 獲取數據
                ticker = yf.Ticker(ticker_symbol)

                # 添加延遲避免頻率限制
                if attempt > 0:
                    time.sleep(0.5 * attempt)  # 遞增延遲

                # 獲取基本信息和歷史數據
                info = ticker.info
                hist = ticker.history(period='2d')  # 獲取2天數據

                if not hist.empty and info and len(info) > 5:  # 確保有有效信息
                    latest = hist.iloc[-1]
                    prev = hist.iloc[-2] if len(hist) > 1 else latest

                    current_price = latest['Close']
                    prev_close = prev['Close']
                    pct_change = ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0

                    data = {
                        'symbol': symbol,
                        'name': info.get('longName', info.get('shortName', symbol)),
                        'open': latest['Open'],
                        'high': latest['High'],
                        'low': latest['Low'],
                        'close': current_price,
                        'volume': latest['Volume'],
                        'pct_change': round(pct_change, 2),
                        'prev_close': prev_close,
                        'market_cap': info.get('marketCap', 0),
                        'timestamp': datetime.now(),
                        'source': 'yfinance'
                    }

                    # 緩存數據
                    self.cache[cache_key] = (time.time(), data)
                    logging.debug(f"✅ yfinance獲取 {symbol} 成功 (嘗試 {attempt + 1})")
                    return data

                # 如果數據不完整，嘗試下一次
                if attempt < retry_count - 1:
                    logging.debug(f"⚠️ yfinance數據不完整 {symbol}，重試中... (嘗試 {attempt + 1})")
                    continue

                return None

            except Exception as e:
                if attempt < retry_count - 1:
                    logging.debug(f"⚠️ yfinance獲取 {symbol} 失敗，重試中... (嘗試 {attempt + 1}): {e}")
                    time.sleep(0.3 * (attempt + 1))  # 遞增延遲
                else:
                    logging.debug(f"❌ yfinance獲取 {symbol} 最終失敗: {e}")

        return None
    
    def get_yahoo_us_data(self, symbol: str) -> Optional[Dict]:
        """使用Yahoo美國站獲取數據"""
        try:
            # 台股在Yahoo美國站的格式
            if symbol.startswith('^'):
                yahoo_symbol = symbol
            else:
                yahoo_symbol = f"{symbol}.TW"
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}"
            
            response = self.make_safe_request(url, timeout=3)
            if not response or response.status_code != 200:
                return None
            
            data = response.json()
            result = data.get('chart', {}).get('result', [])
            
            if not result:
                return None
            
            chart_data = result[0]
            meta = chart_data.get('meta', {})
            timestamps = chart_data.get('timestamp', [])
            indicators = chart_data.get('indicators', {}).get('quote', [{}])[0]
            
            if not timestamps or not indicators:
                return None
            
            # 獲取最新數據
            current_price = meta.get('regularMarketPrice', 0)
            prev_close = meta.get('previousClose', 0)
            pct_change = ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0
            
            data = {
                'symbol': symbol,
                'name': meta.get('longName', symbol),
                'open': indicators.get('open', [0])[-1] or 0,
                'high': indicators.get('high', [0])[-1] or 0,
                'low': indicators.get('low', [0])[-1] or 0,
                'close': current_price,
                'volume': indicators.get('volume', [0])[-1] or 0,
                'pct_change': round(pct_change, 2),
                'prev_close': prev_close,
                'timestamp': datetime.now(),
                'source': 'yahoo_us'
            }
            
            logging.debug(f"✅ Yahoo美國站獲取 {symbol} 成功")
            return data
            
        except Exception as e:
            logging.debug(f"Yahoo美國站獲取 {symbol} 失敗: {e}")
            return None
    
    def get_stock_data_multi_source(self, symbol: str) -> Optional[Dict]:
        """多數據源獲取股票數據"""
        # 檢查緩存
        cache_key = f"multi_{symbol}"
        if cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_timeout:
                return data
        
        # 按優先級嘗試數據源
        for source in self.data_sources:
            if source in self.failed_sources:
                continue
                
            try:
                if source == 'yfinance':
                    data = self.get_yfinance_data(symbol)
                elif source == 'yahoo_us':
                    data = self.get_yahoo_us_data(symbol)
                else:
                    continue  # 其他數據源暫時跳過
                
                if data:
                    # 緩存數據
                    self.cache[cache_key] = (time.time(), data)
                    return data
                    
            except Exception as e:
                logging.debug(f"數據源 {source} 獲取 {symbol} 失敗: {e}")
                # 如果連續失敗，標記為失敗數據源
                self.failed_sources.add(source)
                continue
        
        logging.warning(f"⚠️ 所有數據源都無法獲取 {symbol} 數據")
        return None
    
    def scan_market_indices(self) -> Dict[str, Any]:
        """掃描市場指數 - 使用多數據源"""
        indices = {
            '^TWII': '台股加權指數',
            '0050': '台灣50ETF',
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科'
        }
        
        results = {}
        
        # 使用線程池並行獲取數據
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任務
            future_to_symbol = {
                executor.submit(self.get_stock_data_multi_source, symbol): (symbol, name)
                for symbol, name in indices.items()
            }
            
            # 收集結果
            for future in as_completed(future_to_symbol, timeout=15):  # 15秒超時
                symbol, name = future_to_symbol[future]
                try:
                    data = future.result(timeout=5)
                    if data:
                        results[symbol] = {
                            'name': name,
                            'data': data
                        }
                        logging.info(f"✅ 獲取 {name} 數據成功 (來源: {data.get('source', 'unknown')})")
                    else:
                        logging.warning(f"⚠️ 獲取 {name} 數據失敗")
                except Exception as e:
                    logging.error(f"❌ 獲取 {name} 數據異常: {e}")
        
        return results
    
    def scan_hot_stocks(self, stock_list: List[str] = None) -> Dict[str, Any]:
        """掃描熱門股票 - 使用多數據源，避免頻率限制"""
        if not stock_list:
            stock_list = ['2330', '2317', '2454', '3008', '2412', '2881', '2882', '6505', '2303', '1301']

        results = {}

        # 🛡️ 使用較小的批次處理避免頻率限制
        batch_size = 3  # 每批處理3支股票

        for i in range(0, len(stock_list), batch_size):
            batch = stock_list[i:i + batch_size]

            # 使用線程池處理當前批次
            with ThreadPoolExecutor(max_workers=min(len(batch), 2)) as executor:
                # 提交當前批次任務
                future_to_stock = {
                    executor.submit(self.get_stock_data_multi_source, stock): stock
                    for stock in batch
                }

                # 收集當前批次結果
                for future in as_completed(future_to_stock, timeout=15):
                    stock = future_to_stock[future]
                    try:
                        data = future.result(timeout=8)
                        if data:
                            results[stock] = data
                            logging.debug(f"✅ 獲取 {stock} 數據成功 (來源: {data.get('source', 'unknown')})")
                        else:
                            logging.warning(f"⚠️ 獲取 {stock} 數據為空")
                    except Exception as e:
                        logging.warning(f"⚠️ 獲取 {stock} 數據異常: {e}")

            # 批次間延遲
            if i + batch_size < len(stock_list):
                time.sleep(self.request_delay)

        return results
    
    def run_full_scan(self) -> Dict[str, Any]:
        """執行完整市場掃描 - 優化版本"""
        try:
            logging.info("🚀 開始執行增強版市場掃描...")
            start_time = time.time()
            
            scan_results = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_indices': {},
                'hot_stocks': {},
                'market_summary': {},
                'scan_duration': 0,
                'data_sources_used': list(set(self.data_sources) - self.failed_sources)
            }
            
            # 並行執行掃描任務
            with ThreadPoolExecutor(max_workers=2) as executor:
                # 提交掃描任務
                indices_future = executor.submit(self.scan_market_indices)
                stocks_future = executor.submit(self.scan_hot_stocks)
                
                # 獲取結果
                try:
                    scan_results['market_indices'] = indices_future.result(timeout=25)
                    logging.info(f"✅ 市場指數掃描完成，獲取 {len(scan_results['market_indices'])} 項數據")
                except Exception as e:
                    logging.error(f"❌ 市場指數掃描失敗: {e}")
                
                try:
                    scan_results['hot_stocks'] = stocks_future.result(timeout=30)
                    logging.info(f"✅ 熱門股票掃描完成，獲取 {len(scan_results['hot_stocks'])} 項數據")
                except Exception as e:
                    logging.error(f"❌ 熱門股票掃描失敗: {e}")
            
            # 生成市場摘要
            scan_results['market_summary'] = self._generate_market_summary(scan_results)
            
            # 計算掃描耗時
            scan_results['scan_duration'] = round(time.time() - start_time, 2)
            
            logging.info(f"✅ 增強版市場掃描完成，耗時 {scan_results['scan_duration']} 秒")
            logging.info(f"📊 使用數據源: {', '.join(scan_results['data_sources_used'])}")
            
            return scan_results
            
        except Exception as e:
            logging.error(f"❌ 市場掃描失敗: {e}")
            return None
    
    def _generate_market_summary(self, scan_results: Dict) -> Dict[str, Any]:
        """生成市場摘要"""
        try:
            summary = {
                'total_items': 0,
                'positive_count': 0,
                'negative_count': 0,
                'avg_change': 0,
                'market_sentiment': '中性',
                'data_quality': 'good'
            }
            
            all_changes = []
            
            # 統計市場指數
            for symbol, data in scan_results.get('market_indices', {}).items():
                if 'data' in data and 'pct_change' in data['data']:
                    change = data['data']['pct_change']
                    all_changes.append(change)
                    if change > 0:
                        summary['positive_count'] += 1
                    elif change < 0:
                        summary['negative_count'] += 1
            
            # 統計熱門股票
            for symbol, data in scan_results.get('hot_stocks', {}).items():
                if 'pct_change' in data:
                    change = data['pct_change']
                    all_changes.append(change)
                    if change > 0:
                        summary['positive_count'] += 1
                    elif change < 0:
                        summary['negative_count'] += 1
            
            summary['total_items'] = len(all_changes)
            
            if all_changes:
                summary['avg_change'] = round(np.mean(all_changes), 2)
                
                # 判斷市場情緒
                if summary['avg_change'] > 1:
                    summary['market_sentiment'] = '樂觀'
                elif summary['avg_change'] < -1:
                    summary['market_sentiment'] = '悲觀'
                else:
                    summary['market_sentiment'] = '中性'
            
            # 評估數據質量
            if summary['total_items'] >= 10:
                summary['data_quality'] = 'excellent'
            elif summary['total_items'] >= 5:
                summary['data_quality'] = 'good'
            else:
                summary['data_quality'] = 'limited'
            
            return summary
            
        except Exception as e:
            logging.error(f"生成市場摘要失敗: {e}")
            return {}
    
    def clear_cache(self):
        """清除緩存"""
        self.cache.clear()
        self.failed_sources.clear()
        logging.info("🗑️ 增強版掃描器緩存已清除")

# 創建全局實例
enhanced_scanner = EnhancedMarketScanner()

def run_enhanced_scan() -> Dict[str, Any]:
    """便捷函數：執行增強掃描"""
    return enhanced_scanner.run_full_scan()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試增強版市場掃描器...")
    
    scanner = EnhancedMarketScanner()
    results = scanner.run_full_scan()
    
    if results:
        print("✅ 掃描成功")
        print(f"⏱️ 耗時: {results['scan_duration']} 秒")
        print(f"📊 市場指數: {len(results['market_indices'])} 項")
        print(f"📈 熱門股票: {len(results['hot_stocks'])} 項")
        print(f"💭 市場情緒: {results['market_summary'].get('market_sentiment', '未知')}")
        print(f"🔧 數據源: {', '.join(results['data_sources_used'])}")
    else:
        print("❌ 掃描失敗")
    
    print("🎉 測試完成！")
