@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終修復版

echo.
echo ========================================
echo    台股智能選股系統 v22.2 - 最終修復版
echo ========================================
echo.

if exist "dist\StockAnalyzer_Final_Fixed.exe" (
    echo 找到最終修復版
    echo 正在啟動...
    echo.
    echo 最終修復版特點：
    echo    完全解決 pyqtgraph 錯誤
    echo    選股結果正常顯示
    echo    完整的策略交集分析
    echo    穩定的系統運行
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Final_Fixed.exe"
    
    echo 最終修復版已啟動！
    echo.
    
) else (
    echo 錯誤：找不到最終修復版
    echo.
    pause
    exit /b 1
)

echo 修復的問題：
echo    解決 pyqtgraph 模組錯誤
echo    修復選股結果顯示
echo    完善策略交集分析
echo    提升系統穩定性
echo.

timeout /t 3 >nul
