@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終修復版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         最終修復版 - 模組問題已解決
echo ========================================
echo.

if exist "dist\台股智能選股系統_最終修復版.exe" (
    echo ✅ 找到最終修復版
    echo 🚀 正在啟動...
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_最終修復版.exe"
    
    echo ✅ 最終修復版已啟動！
    echo.
    echo 💡 修復內容：
    echo    ✓ 解決 inspect 模組問題
    echo    ✓ 解決 pydoc 模組問題
    echo    ✓ 解決 doctest 模組問題
    echo    ✓ 優化啟動流程
    echo.
    
) else (
    echo ❌ 錯誤：找不到最終修復版
    echo.
    echo 請重新編譯：
    echo    python simple_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 如果程式無法正常運行，請檢查：
echo    1. 系統環境是否正常
echo    2. 防毒軟體是否阻擋
echo    3. 執行權限是否足夠
echo.

timeout /t 5 >nul
