#!/usr/bin/env python3
"""
市場監控多線程測試腳本
測試新實現的多線程市場監控功能
"""

import sys
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
import threading

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MockPreMarketMonitor:
    """模擬的開盤前監控器"""
    
    def __init__(self):
        self.name = "模擬監控器"
    
    def run_full_scan(self):
        """模擬掃描過程"""
        # 模擬耗時的掃描過程
        for i in range(5):
            time.sleep(1)  # 模擬網絡請求延遲
            logging.info(f"掃描進度: {(i+1)*20}%")
        
        # 返回模擬結果
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'us_indices': {
                'S&P500': {'change_pct': 0.85, 'price': 4500.0},
                'Nasdaq': {'change_pct': 1.20, 'price': 14000.0}
            },
            'commodities': {
                '黃金期貨': {'change_pct': -0.30, 'price': 2000.0},
                'WTI原油': {'change_pct': 2.10, 'price': 75.0}
            },
            'fx_rates': {
                'USD/TWD': {'rate': 31.50, 'change_pct': 0.15}
            }
        }
    
    def get_market_sentiment(self):
        """模擬市場情緒"""
        return "樂觀"

class MarketScanWorker(QObject):
    """市場掃描工作器 - 測試版本"""
    
    # 定義信號
    scan_started = pyqtSignal()
    scan_progress = pyqtSignal(str)
    scan_completed = pyqtSignal(dict)
    scan_error = pyqtSignal(str)
    
    def __init__(self, pre_market_monitor):
        super().__init__()
        self.pre_market_monitor = pre_market_monitor
        self.is_running = False
    
    def start_scan(self):
        """開始掃描"""
        if self.is_running:
            self.scan_progress.emit("⚠️ 掃描正在進行中...")
            return
            
        self.is_running = True
        self.scan_started.emit()
        
        # 在新線程中執行掃描
        scan_thread = threading.Thread(target=self._run_scan, daemon=True)
        scan_thread.start()
    
    def _run_scan(self):
        """在背景線程中執行掃描"""
        try:
            self.scan_progress.emit("🔍 正在獲取美股數據...")
            time.sleep(1)
            
            self.scan_progress.emit("📊 正在分析商品價格...")
            time.sleep(1)
            
            self.scan_progress.emit("💱 正在獲取匯率數據...")
            time.sleep(1)
            
            # 執行完整掃描
            results = self.pre_market_monitor.run_full_scan()
            
            self.scan_progress.emit("✅ 掃描完成")
            self.scan_completed.emit(results)
            
        except Exception as e:
            self.scan_error.emit(f"掃描失敗: {str(e)}")
        finally:
            self.is_running = False

class TestMainWindow(QMainWindow):
    """測試主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("市場監控多線程測試")
        self.setGeometry(100, 100, 600, 500)
        
        # 初始化組件
        self.init_ui()
        self.init_market_monitor()
    
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🔍 市場監控多線程測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 狀態標籤
        self.status_label = QLabel("準備就緒")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(self.status_label)
        
        # 掃描按鈕
        self.scan_button = QPushButton("🚀 開始市場掃描")
        self.scan_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.scan_button.clicked.connect(self.start_scan)
        layout.addWidget(self.scan_button)
        
        # 結果顯示區域
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("font-family: monospace; background-color: #f8f8f8;")
        self.result_text.setPlainText("等待掃描結果...")
        layout.addWidget(self.result_text)
        
        # 測試計時器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.update_ui_test)
        self.test_timer.start(100)  # 每100ms更新一次，測試UI響應性
        
        self.ui_update_count = 0
    
    def init_market_monitor(self):
        """初始化市場監控器"""
        # 創建模擬監控器
        self.pre_market_monitor = MockPreMarketMonitor()
        
        # 創建掃描工作器
        self.market_scan_worker = MarketScanWorker(self.pre_market_monitor)
        
        # 連接信號
        self.market_scan_worker.scan_started.connect(self.on_scan_started)
        self.market_scan_worker.scan_progress.connect(self.on_scan_progress)
        self.market_scan_worker.scan_completed.connect(self.on_scan_completed)
        self.market_scan_worker.scan_error.connect(self.on_scan_error)
    
    def start_scan(self):
        """開始掃描"""
        self.market_scan_worker.start_scan()
    
    def on_scan_started(self):
        """掃描開始回調"""
        self.status_label.setText("🔍 掃描已開始...")
        self.scan_button.setEnabled(False)
        self.result_text.setPlainText("掃描進行中...\n")
        print("✅ 掃描開始 - UI未阻塞")
    
    def on_scan_progress(self, message):
        """掃描進度回調"""
        self.status_label.setText(message)
        current_text = self.result_text.toPlainText()
        self.result_text.setPlainText(current_text + f"{message}\n")
        print(f"📊 進度更新: {message}")
    
    def on_scan_error(self, error_message):
        """掃描錯誤回調"""
        self.status_label.setText(f"❌ 掃描失敗: {error_message}")
        self.scan_button.setEnabled(True)
        current_text = self.result_text.toPlainText()
        self.result_text.setPlainText(current_text + f"❌ 錯誤: {error_message}\n")
        print(f"❌ 掃描錯誤: {error_message}")
    
    def on_scan_completed(self, results):
        """掃描完成回調"""
        self.status_label.setText("✅ 掃描完成")
        self.scan_button.setEnabled(True)
        
        # 格式化結果
        result_text = "📊 掃描結果:\n"
        result_text += f"時間: {results['timestamp']}\n\n"
        
        # 美股指數
        if 'us_indices' in results:
            result_text += "🇺🇸 美股指數:\n"
            for name, data in results['us_indices'].items():
                result_text += f"  {name}: {data['change_pct']:+.2f}% (${data['price']:.0f})\n"
            result_text += "\n"
        
        # 商品價格
        if 'commodities' in results:
            result_text += "🛢️ 商品價格:\n"
            for name, data in results['commodities'].items():
                result_text += f"  {name}: {data['change_pct']:+.2f}% (${data['price']:.0f})\n"
            result_text += "\n"
        
        # 匯率
        if 'fx_rates' in results:
            result_text += "💱 匯率:\n"
            for name, data in results['fx_rates'].items():
                result_text += f"  {name}: {data['rate']:.4f} ({data['change_pct']:+.2f}%)\n"
        
        self.result_text.setPlainText(result_text)
        print("✅ 掃描完成 - 結果已顯示")
    
    def update_ui_test(self):
        """測試UI響應性"""
        self.ui_update_count += 1
        # 每秒更新一次標題，證明UI沒有被阻塞
        if self.ui_update_count % 10 == 0:  # 每1秒
            current_time = datetime.now().strftime('%H:%M:%S')
            self.setWindowTitle(f"市場監控多線程測試 - {current_time}")

def main():
    """主函數"""
    print("🚀 啟動市場監控多線程測試")
    
    app = QApplication(sys.argv)
    
    # 創建測試窗口
    window = TestMainWindow()
    window.show()
    
    print("✅ 測試窗口已啟動")
    print("💡 點擊按鈕開始測試多線程掃描")
    print("🔍 觀察UI是否保持響應性")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
