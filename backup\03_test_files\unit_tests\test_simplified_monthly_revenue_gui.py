#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化後的月營收下載器GUI
移除批量股票功能，專注於全股票+日期範圍功能
"""

import os
import sys
import time
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gui_structure():
    """測試GUI結構是否正確"""
    print("🖥️ 測試簡化後的GUI結構")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from monthly_revenue_downloader_gui import MonthlyRevenueDownloaderGUI
        
        # 創建隱藏的根視窗
        root = tk.Tk()
        root.withdraw()  # 隱藏主視窗
        
        # 創建GUI實例
        gui = MonthlyRevenueDownloaderGUI(root)
        print("✅ GUI實例創建成功")
        
        # 檢查是否移除了批量股票相關功能
        print("\n🔍 檢查批量股票功能是否已移除...")
        
        # 檢查是否還有batch_stock_frame屬性
        if hasattr(gui, 'batch_stock_frame'):
            print("❌ 批量股票框架仍然存在")
        else:
            print("✅ 批量股票框架已移除")
        
        # 檢查是否還有batch_stocks_var屬性
        if hasattr(gui, 'batch_stocks_var'):
            print("❌ 批量股票變數仍然存在")
        else:
            print("✅ 批量股票變數已移除")
        
        # 檢查是否還有set_preset_stocks方法
        if hasattr(gui, 'set_preset_stocks'):
            print("❌ 預設股票方法仍然存在")
        else:
            print("✅ 預設股票方法已移除")
        
        # 檢查是否還有download_batch_stocks方法
        if hasattr(gui, 'download_batch_stocks'):
            print("❌ 批量下載方法仍然存在")
        else:
            print("✅ 批量下載方法已移除")
        
        # 檢查全股票日期範圍功能
        print("\n📅 檢查全股票日期範圍功能...")
        
        if hasattr(gui, 'all_use_date_range_var'):
            print("✅ 全股票日期範圍選項存在")
            
            if hasattr(gui, 'all_start_date_var') and hasattr(gui, 'all_end_date_var'):
                print("✅ 全股票日期輸入變數存在")
                
                if hasattr(gui, 'toggle_all_date_range'):
                    print("✅ 全股票日期範圍切換功能存在")
                    
                    # 測試全股票日期範圍切換
                    gui.all_use_date_range_var.set(True)
                    gui.toggle_all_date_range()
                    
                    start_state = gui.all_start_date_entry.cget('state')
                    end_state = gui.all_end_date_entry.cget('state')
                    
                    if start_state == 'normal' and end_state == 'normal':
                        print("✅ 全股票日期範圍啟用功能正常")
                    else:
                        print(f"⚠️ 全股票日期範圍啟用狀態: start={start_state}, end={end_state}")

                    # 測試停用
                    gui.all_use_date_range_var.set(False)
                    gui.toggle_all_date_range()

                    start_state = gui.all_start_date_entry.cget('state')
                    end_state = gui.all_end_date_entry.cget('state')

                    if start_state == 'disabled' and end_state == 'disabled':
                        print("✅ 全股票日期範圍停用功能正常")
                    else:
                        print(f"⚠️ 全股票日期範圍停用狀態: start={start_state}, end={end_state}")
                else:
                    print("❌ 全股票日期範圍切換功能不存在")
            else:
                print("❌ 全股票日期輸入變數不存在")
        else:
            print("❌ 全股票日期範圍選項不存在")
        
        # 檢查下載模式選項
        print("\n🎯 檢查下載模式選項...")
        
        # 測試模式切換
        gui.download_mode.set("all_stocks")
        gui.on_mode_change()
        print("✅ 全股票模式切換測試完成")
        
        gui.download_mode.set("single_stock")
        gui.on_mode_change()
        print("✅ 單一股票模式切換測試完成")
        
        # 清理
        gui.window.destroy()
        root.destroy()
        
        print("\n✅ GUI結構測試完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI結構測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_downloader_integration():
    """測試下載器整合"""
    print("\n🔧 測試下載器整合")
    print("=" * 60)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器初始化成功")
        
        # 檢查是否支援日期範圍參數
        import inspect
        sig = inspect.signature(downloader.download_stock_revenue)
        params = list(sig.parameters.keys())
        
        if 'start_date' in params and 'end_date' in params:
            print("✅ 下載器支援日期範圍參數")
        else:
            print(f"❌ 下載器不支援日期範圍參數，當前參數: {params}")
        
        # 檢查數據庫
        if os.path.exists(downloader.db_path):
            print(f"✅ 數據庫文件存在: {downloader.db_path}")
        else:
            print(f"⚠️ 數據庫文件不存在: {downloader.db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下載器整合測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 簡化後月營收下載器測試")
    print("=" * 70)
    
    tests = [
        ("GUI結構測試", test_gui_structure),
        ("下載器整合測試", test_downloader_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n開始測試: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 70)
    print("📋 簡化測試總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🎯 簡化後的功能特色:")
    print("• ✅ 移除批量股票下載：簡化界面，專注核心功能")
    print("• ✅ 移除預設股票按鈕：減少不必要的複雜性")
    print("• ✅ 全股票+日期範圍：大宗需求的完美組合")
    print("• ✅ 單一股票+日期範圍：測試和驗證的理想選擇")
    print("• ✅ 清晰的雙模式設計：全股票 vs 單一股票")
    
    print("\n💡 使用場景:")
    print("1. 🎯 單一股票測試：驗證下載功能是否正常")
    print("2. 📊 全股票建庫：建立完整的月營收資料庫")
    print("3. 📅 指定時間範圍：只下載需要的時間區間數據")
    print("4. 🔄 定期更新：使用全股票模式更新最新資料")
    
    print("\n🚀 啟動方法:")
    print("python launch_monthly_revenue_gui.py")
    
    if passed >= len(results):
        print("\n🎉 簡化測試成功！")
        print("月營收下載器已成功簡化，專注於核心功能。")
    else:
        print("\n⚠️ 部分功能需要進一步調整")

if __name__ == "__main__":
    main()
