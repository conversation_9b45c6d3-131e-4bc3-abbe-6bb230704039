#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修復後的Excel解析器
"""

import os
import glob

def test_fixed_excel_parser():
    """測試修復後的Excel解析器"""
    print("🚀 測試修復後的Excel解析器")
    print("=" * 50)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 尋找最新下載的Excel文件
        download_dir = "D:/Finlab/history/tables/monthly_revenue"
        excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
        
        if not excel_files:
            print("❌ 未找到下載的Excel文件")
            print("請先運行Selenium下載測試")
            return False
        
        # 使用最新的文件
        latest_file = max(excel_files, key=os.path.getctime)
        print(f"📊 測試文件: {latest_file}")
        
        # 測試解析
        stock_id = '2330'
        print(f"\n🔧 解析 {stock_id} Excel文件...")
        
        revenue_data = downloader.parse_excel_file(latest_file, stock_id)
        
        if revenue_data:
            print(f"✅ 解析成功，獲得 {len(revenue_data)} 筆數據")
            
            # 顯示前5筆數據
            print("\n📋 前5筆數據:")
            for i, record in enumerate(revenue_data[:5], 1):
                print(f"{i}. {record['year']}/{record['month']:02d} - "
                      f"營收: {record['revenue']:,}億, "
                      f"年增率: {record['revenue_yoy']:+.1f}%, "
                      f"收盤價: {record['close_price']:,.0f}元")
            
            # 測試儲存到數據庫
            print(f"\n💾 儲存到數據庫...")
            if downloader.save_to_database(revenue_data):
                print("✅ 數據已成功儲存到數據庫")
                
                # 查詢驗證
                print("\n🔍 查詢驗證...")
                import sqlite3
                conn = sqlite3.connect(downloader.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT COUNT(*) FROM monthly_revenue 
                    WHERE stock_id = ? AND data_source = 'goodinfo_excel'
                """, (stock_id,))
                
                excel_count = cursor.fetchone()[0]
                print(f"✅ 數據庫中來自Excel的 {stock_id} 數據: {excel_count} 筆")
                
                # 顯示最新數據
                cursor.execute("""
                    SELECT year, month, revenue, revenue_yoy, close_price 
                    FROM monthly_revenue 
                    WHERE stock_id = ? AND data_source = 'goodinfo_excel'
                    ORDER BY year DESC, month DESC 
                    LIMIT 3
                """, (stock_id,))
                
                recent_data = cursor.fetchall()
                print("\n📋 數據庫中最新3筆Excel數據:")
                for year, month, revenue, yoy, close_price in recent_data:
                    revenue_str = f"{revenue:,.0f}億" if revenue else "N/A"
                    yoy_str = f"{yoy:+.1f}%" if yoy else "N/A"
                    price_str = f"{close_price:,.0f}元" if close_price else "N/A"
                    print(f"   {year}/{month:02d} - 營收: {revenue_str}, 年增率: {yoy_str}, 收盤: {price_str}")
                
                conn.close()
                
                return True
            else:
                print("❌ 儲存到數據庫失敗")
                return False
        else:
            print("❌ 解析失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_month_parsing():
    """測試月份解析功能"""
    print("\n🔍 測試月份解析功能")
    print("=" * 30)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 測試不同格式的月份
        test_months = [
            '2025/06',  # GoodInfo實際格式
            '2025/05',
            '2024/12',
            'Jun-25',   # 原始期望格式
            'May-25',
            '2025.06',  # 點號格式
            'invalid'   # 無效格式
        ]
        
        print("📅 月份解析測試:")
        for month_str in test_months:
            year, month = downloader.parse_month_string(month_str)
            if year and month:
                print(f"   ✅ '{month_str}' -> {year}/{month:02d}")
            else:
                print(f"   ❌ '{month_str}' -> 解析失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 月份解析測試失敗: {e}")
        return False

def test_number_parsing():
    """測試數字解析功能"""
    print("\n🔢 測試數字解析功能")
    print("=" * 30)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 測試不同格式的數字
        test_numbers = [
            '2,637',    # 千分位逗號
            '+93',      # 正號
            '+9.62',    # 正號小數
            '-17.7',    # 負號
            '(12.5)',   # 括號負數
            '1060',     # 普通數字
            '',         # 空值
            'nan',      # 無效值
            'N/A'       # 無效值
        ]
        
        print("🔢 數字解析測試:")
        for number_str in test_numbers:
            result = downloader.safe_float(number_str)
            print(f"   '{number_str}' -> {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 數字解析測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 修復後Excel解析器測試套件")
    print("=" * 60)
    
    # 測試月份解析
    month_test = test_month_parsing()
    
    # 測試數字解析
    number_test = test_number_parsing()
    
    # 測試Excel解析
    excel_test = test_fixed_excel_parser()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    print(f"   月份解析: {'✅ 通過' if month_test else '❌ 失敗'}")
    print(f"   數字解析: {'✅ 通過' if number_test else '❌ 失敗'}")
    print(f"   Excel解析: {'✅ 通過' if excel_test else '❌ 失敗'}")
    
    if excel_test:
        print("\n🎉 Excel解析器修復成功！")
        print("現在可以在月營收下載器GUI中重新嘗試下載")
        print("Selenium下載功能應該能正常工作了")
    else:
        print("\n⚠️ Excel解析器仍有問題，需要進一步調試")
    
    input("\n按Enter鍵退出...")
    return excel_test

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
