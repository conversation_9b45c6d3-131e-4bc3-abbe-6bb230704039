#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版月營收下載器
"""

from enhanced_monthly_revenue_downloader import EnhancedMonthlyRevenueDownloader
import logging
import sys

def main():
    """主測試函數"""
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 測試增強版月營收下載器")
    print("=" * 50)
    
    # 創建下載器實例
    downloader = EnhancedMonthlyRevenueDownloader()
    
    # 測試股票列表
    test_stocks = ['2330', '2317', '2454']  # 台積電、鴻海、聯發科
    
    for stock_id in test_stocks:
        print(f"\n📊 測試下載 {stock_id} 的月營收資料...")
        
        try:
            result = downloader.download_stock_revenue_enhanced(stock_id)
            
            if result and result > 0:
                print(f"✅ {stock_id} 下載成功！共處理 {result} 筆記錄")
            else:
                print(f"❌ {stock_id} 下載失敗")
                
        except Exception as e:
            print(f"❌ {stock_id} 下載過程中發生錯誤: {e}")
            
        print("-" * 30)
    
    print("\n🎉 測試完成！")

if __name__ == "__main__":
    main()
