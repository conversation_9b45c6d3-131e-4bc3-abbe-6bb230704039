#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試右鍵選單"加入監控清單"功能
"""

import sys
import os
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem, QTextEdit, QPushButton, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MonitorContextMenuTestWindow(QMainWindow):
    """監控右鍵選單測試視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("右鍵選單「加入監控清單」功能測試")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🎯 右鍵選單「加入監控清單」功能測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196f3; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 測試按鈕區域
        button_layout = QHBoxLayout()
        
        self.test_basic_btn = QPushButton("🧪 測試基本功能")
        self.test_basic_btn.clicked.connect(self.test_basic_functionality)
        button_layout.addWidget(self.test_basic_btn)
        
        self.test_integration_btn = QPushButton("🔗 測試整合功能")
        self.test_integration_btn.clicked.connect(self.test_integration)
        button_layout.addWidget(self.test_integration_btn)
        
        self.test_main_app_btn = QPushButton("🚀 測試主程式")
        self.test_main_app_btn.clicked.connect(self.test_main_app)
        button_layout.addWidget(self.test_main_app_btn)
        
        layout.addLayout(button_layout)
        
        # 測試表格
        self.create_test_table()
        layout.addWidget(self.test_table)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        self.log("🚀 右鍵選單測試系統已啟動")
        self.log("💡 在上方表格的股票上按右鍵測試「加入監控清單」功能")
    
    def create_test_table(self):
        """創建測試表格"""
        self.test_table = QTableWidget()
        self.test_table.setRowCount(5)
        self.test_table.setColumnCount(3)
        self.test_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "股價"])
        
        # 添加測試資料
        test_stocks = [
            ("2330", "台積電", "580.00"),
            ("2317", "鴻海", "105.50"),
            ("2454", "聯發科", "890.00"),
            ("3008", "大立光", "2100.00"),
            ("2412", "中華電", "125.00")
        ]
        
        for row, (code, name, price) in enumerate(test_stocks):
            self.test_table.setItem(row, 0, QTableWidgetItem(code))
            self.test_table.setItem(row, 1, QTableWidgetItem(name))
            self.test_table.setItem(row, 2, QTableWidgetItem(price))
        
        # 設置右鍵選單
        self.test_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.test_table.customContextMenuRequested.connect(self.show_test_context_menu)
        
        # 調整表格外觀
        self.test_table.resizeColumnsToContents()
        self.test_table.setAlternatingRowColors(True)
    
    def show_test_context_menu(self, position):
        """顯示測試右鍵選單"""
        try:
            # 獲取點擊的項目
            item = self.test_table.itemAt(position)
            if not item:
                self.log("❌ 沒有點擊到有效項目")
                return
            
            # 獲取股票資訊
            row = item.row()
            stock_code_item = self.test_table.item(row, 0)
            stock_name_item = self.test_table.item(row, 1)
            
            if not stock_code_item:
                self.log("❌ 無法獲取股票代碼")
                return
            
            stock_code = stock_code_item.text().strip()
            stock_name = stock_name_item.text().strip() if stock_name_item else "未知"
            
            self.log(f"📊 選中股票: {stock_code} {stock_name}")
            
            # 創建右鍵選單
            from PyQt6.QtWidgets import QMenu
            context_menu = QMenu(self)
            
            # 添加選單項目
            news_action = context_menu.addAction(f"📰 爬取 {stock_code} {stock_name} 新聞")
            news_action.triggered.connect(lambda: self.test_news_action(stock_code, stock_name))
            
            chart_action = context_menu.addAction(f"📈 查看 {stock_code} K線圖")
            chart_action.triggered.connect(lambda: self.test_chart_action(stock_code, stock_name))
            
            info_action = context_menu.addAction(f"ℹ️ 查看 {stock_code} 基本資料")
            info_action.triggered.connect(lambda: self.test_info_action(stock_code, stock_name))
            
            context_menu.addSeparator()
            
            # 🎯 新增：加入監控清單功能
            monitor_action = context_menu.addAction(f"📊 加入監控清單")
            monitor_action.triggered.connect(lambda: self.test_add_to_monitor(stock_code, stock_name))
            
            # 顯示選單
            global_pos = self.test_table.mapToGlobal(position)
            context_menu.exec(global_pos)
            
        except Exception as e:
            self.log(f"❌ 顯示右鍵選單失敗: {e}")
    
    def test_add_to_monitor(self, stock_code, stock_name):
        """測試加入監控清單功能"""
        try:
            self.log(f"🎯 測試加入監控清單: {stock_code} {stock_name}")
            
            # 測試統一監控管理器
            from unified_monitor_manager import get_monitor_manager
            monitor_manager = get_monitor_manager()
            
            # 添加股票到監控清單
            success = monitor_manager.add_stock(stock_code)
            
            if success:
                current_count = monitor_manager.get_stock_count()
                self.log(f"✅ 成功加入監控清單！目前監控 {current_count} 支股票")
                
                # 顯示當前監控清單
                current_stocks = monitor_manager.get_stocks()
                self.log(f"📊 當前監控清單: {', '.join(current_stocks)}")
                
            else:
                self.log(f"❌ 加入監控清單失敗或股票已存在")
                
        except Exception as e:
            self.log(f"❌ 測試加入監控清單失敗: {e}")
    
    def test_news_action(self, stock_code, stock_name):
        """測試新聞功能"""
        self.log(f"📰 測試新聞功能: {stock_code} {stock_name}")
    
    def test_chart_action(self, stock_code, stock_name):
        """測試圖表功能"""
        self.log(f"📈 測試圖表功能: {stock_code} {stock_name}")
    
    def test_info_action(self, stock_code, stock_name):
        """測試資訊功能"""
        self.log(f"ℹ️ 測試資訊功能: {stock_code} {stock_name}")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def test_basic_functionality(self):
        """測試基本功能"""
        self.log("🧪 開始測試基本功能...")
        
        try:
            # 測試統一監控管理器
            from unified_monitor_manager import get_monitor_manager
            monitor_manager = get_monitor_manager()
            
            self.log("✅ 統一監控管理器載入成功")
            
            # 測試基本操作
            test_stock = "1234"
            
            # 添加測試股票
            success = monitor_manager.add_stock(test_stock)
            if success:
                self.log(f"✅ 添加測試股票成功: {test_stock}")
            else:
                self.log(f"❌ 添加測試股票失敗: {test_stock}")
            
            # 檢查股票是否存在
            is_monitoring = monitor_manager.is_monitoring(test_stock)
            self.log(f"📊 股票監控狀態: {test_stock} = {is_monitoring}")
            
            # 移除測試股票
            success = monitor_manager.remove_stock(test_stock)
            if success:
                self.log(f"✅ 移除測試股票成功: {test_stock}")
            else:
                self.log(f"❌ 移除測試股票失敗: {test_stock}")
            
            self.log("🎉 基本功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 基本功能測試失敗: {e}")
    
    def test_integration(self):
        """測試整合功能"""
        self.log("🔗 開始測試整合功能...")
        
        try:
            # 測試配置檔案
            config_file = "app_config.json"
            if os.path.exists(config_file):
                self.log("✅ 配置檔案存在")
                
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                monitor_config = config.get('monitor', {})
                stocks = monitor_config.get('stocks', '')
                self.log(f"📊 配置中的監控股票: {stocks}")
                
            else:
                self.log("⚠️ 配置檔案不存在")
            
            # 測試統一監控管理器統計
            from unified_monitor_manager import get_monitor_manager
            monitor_manager = get_monitor_manager()
            
            stats = monitor_manager.get_stats()
            self.log(f"📈 監控統計: {stats}")
            
            self.log("🎉 整合功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 整合功能測試失敗: {e}")
    
    def test_main_app(self):
        """測試主程式整合"""
        self.log("🚀 開始測試主程式整合...")
        
        try:
            # 檢查主程式是否可以導入
            from O3mh_gui_v21_optimized import StockScreenerGUI
            self.log("✅ 主程式類別導入成功")
            
            # 檢查是否有 add_stock_to_monitor 方法
            if hasattr(StockScreenerGUI, 'add_stock_to_monitor'):
                self.log("✅ add_stock_to_monitor 方法存在")
            else:
                self.log("❌ add_stock_to_monitor 方法不存在")
            
            # 檢查是否有統一監控管理器整合
            if hasattr(StockScreenerGUI, 'monitor_manager'):
                self.log("✅ 統一監控管理器已整合到主程式")
            else:
                self.log("⚠️ 統一監控管理器可能未完全整合")
            
            self.log("🎉 主程式整合測試完成")
            
        except Exception as e:
            self.log(f"❌ 主程式整合測試失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("監控右鍵選單測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = MonitorContextMenuTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
