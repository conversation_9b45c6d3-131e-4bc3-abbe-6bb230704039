{"name": "突破回踩策略", "description": "識別關鍵支撐阻力突破後的回踩買點機會", "parameters": {"lookback_period": 20, "breakout_threshold": 2, "pullback_max_percent": 8, "pullback_min_percent": 3, "volume_surge_ratio": 1.8, "consolidation_days": 5, "rsi_filter": true, "rsi_oversold": 35, "support_test_tolerance": 1}, "signal_logic": {"breakout_detection": {"type": "resistance_break", "period": 20, "min_volume_ratio": 1.5, "min_price_change": 2}, "pullback_entry": {"type": "support_retest", "max_pullback": 8, "min_pullback": 3, "volume_decline": true, "rsi_oversold_bounce": true}, "confirmation": {"type": "renewed_strength", "volume_increase": true, "price_recovery": true, "time_limit": 5}}, "risk_management": {"stop_loss": {"type": "support_break", "buffer_percent": 2}, "position_sizing": {"base_percent": 2, "max_percent": 5, "volatility_adjustment": true}, "take_profit": [{"level": 1, "percent": 8, "partial_exit": 30}, {"level": 2, "percent": 15, "partial_exit": 50}, {"level": 3, "percent": 25, "partial_exit": 100}]}}