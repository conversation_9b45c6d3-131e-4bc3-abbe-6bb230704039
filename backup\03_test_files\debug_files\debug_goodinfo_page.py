#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
調試GoodInfo頁面結構
確認我們訪問的是正確的頁面
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def debug_goodinfo_page():
    """調試GoodInfo頁面"""
    print("🔍 調試GoodInfo頁面結構")
    print("=" * 50)
    
    try:
        # 設置Chrome選項
        options = Options()
        # 不使用無頭模式，這樣我們可以看到實際頁面
        # options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("🔧 啟動Chrome瀏覽器（有頭模式）...")
        driver = webdriver.Chrome(options=options)
        
        # 訪問台積電月營收頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        
        driver.get(url)
        time.sleep(5)  # 等待頁面載入
        
        # 檢查頁面標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 檢查頁面內容
        print("\n🔍 檢查頁面內容...")
        
        # 尋找表格
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"✅ 找到 {len(tables)} 個表格")
        
        # 檢查是否包含月份數據
        page_source = driver.page_source
        
        month_patterns = ['Jun-25', 'May-25', 'Apr-25', 'Mar-25', 'Feb-25', 'Jan-25']
        found_months = []
        
        for pattern in month_patterns:
            if pattern in page_source:
                found_months.append(pattern)
        
        if found_months:
            print(f"✅ 頁面包含月份數據: {found_months}")
        else:
            print("❌ 頁面不包含預期的月份數據")
            
            # 檢查是否包含其他月份格式
            import re
            month_matches = re.findall(r'[A-Za-z]{3}-\d{2}', page_source)
            if month_matches:
                print(f"✅ 找到其他月份格式: {month_matches[:10]}")
        
        # 檢查匯出按鈕
        print("\n🔍 檢查匯出按鈕...")
        
        export_buttons = driver.find_elements(By.XPATH, "//input[@value='XLS']")
        print(f"✅ 找到 {len(export_buttons)} 個XLS按鈕")
        
        for i, button in enumerate(export_buttons):
            onclick = button.get_attribute("onclick")
            print(f"   按鈕{i+1}: {onclick}")
        
        # 檢查divDetail內容
        print("\n🔍 檢查divDetail內容...")
        
        try:
            div_detail = driver.find_element(By.ID, "divDetail")
            detail_text = div_detail.text[:500]  # 前500字符
            print(f"✅ divDetail內容預覽: {detail_text}")
            
            # 檢查divDetail中的表格
            detail_tables = div_detail.find_elements(By.TAG_NAME, "table")
            print(f"✅ divDetail包含 {len(detail_tables)} 個表格")
            
            if detail_tables:
                # 檢查第一個表格的內容
                first_table = detail_tables[0]
                rows = first_table.find_elements(By.TAG_NAME, "tr")
                print(f"✅ 第一個表格有 {len(rows)} 行")
                
                # 顯示前幾行
                for i, row in enumerate(rows[:5]):
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if not cells:
                        cells = row.find_elements(By.TAG_NAME, "th")
                    
                    if cells:
                        cell_texts = [cell.text[:20] for cell in cells[:8]]  # 前8個單元格
                        print(f"   行{i}: {cell_texts}")
                
        except Exception as e:
            print(f"❌ 檢查divDetail失敗: {e}")
        
        # 等待用戶檢查
        input("\n按Enter繼續（請檢查瀏覽器中的頁面內容）...")
        
        # 嘗試點擊匯出按鈕
        if export_buttons:
            print("\n🔄 嘗試點擊匯出按鈕...")
            
            try:
                export_buttons[0].click()
                print("✅ 匯出按鈕點擊成功")
                
                # 等待下載
                time.sleep(5)
                print("⏳ 等待下載完成...")
                
            except Exception as e:
                print(f"❌ 點擊匯出按鈕失敗: {e}")
        
        input("\n按Enter關閉瀏覽器...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 GoodInfo頁面調試工具")
    print("=" * 50)
    print("⚠️ 此工具會開啟瀏覽器視窗，請準備檢查頁面內容")
    
    response = input("\n是否繼續？(y/n): ").lower().strip()
    if response != 'y':
        print("❌ 用戶取消")
        return
    
    success = debug_goodinfo_page()
    
    if success:
        print("\n🎉 調試完成！")
        print("📋 請根據瀏覽器中看到的內容確認：")
        print("1. 頁面是否顯示台積電的歷史月營收數據？")
        print("2. 是否有Jun-25, May-25等月份標籤？")
        print("3. 匯出的Excel是否包含正確的數據？")
    else:
        print("\n❌ 調試失敗")

if __name__ == "__main__":
    main()
