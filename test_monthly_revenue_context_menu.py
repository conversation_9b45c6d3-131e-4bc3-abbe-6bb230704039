#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收排行榜右鍵選單功能
"""

import sys
import os
from datetime import datetime, date

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_context_menu_functions():
    """測試右鍵選單相關函數"""
    print("=" * 60)
    print("🖱️ 測試月營收排行榜右鍵選單功能")
    print("=" * 60)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查新增的函數是否存在
        functions_to_check = [
            'is_monthly_revenue_ranking',
            'show_monthly_revenue_context_menu',
            'get_monthly_revenue_stock_data',
            'show_monthly_revenue_assessment',
            'generate_monthly_revenue_assessment_text',
            'get_growth_color'
        ]
        
        print("\n🔍 檢查新增函數:")
        for func_name in functions_to_check:
            if hasattr(gui, func_name):
                print(f"  ✅ {func_name} 函數存在")
            else:
                print(f"  ❌ {func_name} 函數不存在")
        
        # 測試月營收排行榜檢測函數
        print(f"\n🔍 測試月營收排行榜檢測:")
        
        # 模擬設置月營收排行榜表格
        gui.result_table.setColumnCount(13)
        gui.result_table.setHorizontalHeaderLabels([
            "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
            "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
            "殖利率(%)", "本益比", "股價淨值比", "EPS"
        ])
        
        is_monthly = gui.is_monthly_revenue_ranking()
        print(f"  月營收排行榜檢測結果: {'✅ 是' if is_monthly else '❌ 否'}")
        
        # 測試成長率顏色函數
        print(f"\n🎨 測試成長率顏色函數:")
        test_growth_values = ["+15.50%", "-8.20%", "0.00%", "N/A", "invalid"]
        
        for growth_val in test_growth_values:
            color_style = gui.get_growth_color(growth_val)
            print(f"  {growth_val:<10} -> {color_style}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_data_extraction():
    """測試股票資料提取功能"""
    print("\n" + "=" * 60)
    print("📊 測試股票資料提取功能")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QTableWidgetItem
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        # 設置測試資料
        gui.result_table.setColumnCount(14)
        gui.result_table.setRowCount(3)
        gui.result_table.setHorizontalHeaderLabels([
            "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
            "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
            "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
        ])
        
        # 填入測試資料
        test_data = [
            ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
             "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
            ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
             "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"],
            ["3", "2454", "聯發科", "202507", "85,000,000", "80,000,000", "75,000,000", 
             "+13.33%", "*****%", "2.80", "18.20", "3.20", "46.70", "82.1"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                gui.result_table.setItem(row, col, item)
        
        print("✅ 測試資料設置完成")
        
        # 測試資料提取
        print("\n📋 測試股票資料提取:")
        for row in range(3):
            stock_data = gui.get_monthly_revenue_stock_data(row)
            if stock_data:
                print(f"\n  第 {row+1} 行資料:")
                print(f"    股票: {stock_data['股票代碼']} {stock_data['股票名稱']}")
                print(f"    排名: {stock_data['排名']}")
                print(f"    當月營收: {stock_data['當月營收']}")
                print(f"    YoY%: {stock_data['YoY%']}")
                print(f"    MoM%: {stock_data['MoM%']}")
                print(f"    EPS: {stock_data['EPS']}")
                print(f"    綜合評分: {stock_data.get('綜合評分', 'N/A')}")
            else:
                print(f"  ❌ 第 {row+1} 行資料提取失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票資料提取測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assessment_text_generation():
    """測試評估文本生成功能"""
    print("\n" + "=" * 60)
    print("📝 測試評估文本生成功能")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        # 模擬股票資料
        test_stock_data = {
            '排名': '1',
            '股票代碼': '2330',
            '股票名稱': '台積電',
            '西元年月': '202507',
            '當月營收': '120,000,000',
            '上個月營收': '115,000,000',
            '去年同月營收': '100,000,000',
            'YoY%': '+20.00%',
            'MoM%': '*****%',
            '殖利率': '3.25',
            '本益比': '15.80',
            '股價淨值比': '2.45',
            'EPS': '36.71',
            '綜合評分': '85.5'
        }
        
        print("✅ 測試資料準備完成")
        
        # 生成評估文本
        assessment_html = gui.generate_monthly_revenue_assessment_text(test_stock_data)
        
        print(f"\n📄 評估文本生成結果:")
        print(f"  文本長度: {len(assessment_html)} 字符")
        print(f"  包含HTML標籤: {'✅ 是' if '<div' in assessment_html else '❌ 否'}")
        print(f"  包含股票資訊: {'✅ 是' if '2330' in assessment_html and '台積電' in assessment_html else '❌ 否'}")
        print(f"  包含營收資料: {'✅ 是' if '120,000,000' in assessment_html else '❌ 否'}")
        print(f"  包含成長率: {'✅ 是' if '+20.00%' in assessment_html and '*****%' in assessment_html else '❌ 否'}")
        print(f"  包含財務指標: {'✅ 是' if '15.80' in assessment_html and '36.71' in assessment_html else '❌ 否'}")
        print(f"  包含綜合評分: {'✅ 是' if '85.5' in assessment_html else '❌ 否'}")
        
        # 測試不同成長率的顏色
        print(f"\n🎨 測試成長率顏色:")
        color_tests = [
            ('+20.00%', '正成長'),
            ('-5.50%', '負成長'),
            ('0.00%', '零成長'),
            ('N/A', '無資料')
        ]
        
        for growth_val, description in color_tests:
            color_style = gui.get_growth_color(growth_val)
            print(f"  {growth_val:<10} ({description:<6}): {color_style}")
        
        return True
        
    except Exception as e:
        print(f"❌ 評估文本生成測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始測試月營收排行榜右鍵選單功能...")
    
    # 測試右鍵選單函數
    success1 = test_context_menu_functions()
    
    # 測試股票資料提取
    success2 = test_stock_data_extraction()
    
    # 測試評估文本生成
    success3 = test_assessment_text_generation()
    
    if success1 and success2 and success3:
        print("\n🎉 所有測試通過！月營收排行榜右鍵選單功能正常。")
        print("\n📋 功能清單:")
        print("  ✅ 右鍵選單檢測")
        print("  ✅ 月營收排行榜識別")
        print("  ✅ 股票資料提取")
        print("  ✅ 評估文本生成")
        print("  ✅ 成長率顏色標示")
        print("\n🎯 使用方法:")
        print("  1. 執行月營收排行榜查詢")
        print("  2. 在結果表格中右鍵點擊任一股票")
        print("  3. 選擇「月營收綜合評估」選項")
        print("  4. 查看詳細的評估報告")
    else:
        print("\n❌ 部分測試失敗，請檢查相關功能。")
