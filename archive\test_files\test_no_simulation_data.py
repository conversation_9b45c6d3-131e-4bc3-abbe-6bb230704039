#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試無模擬數據版本 - 確保只顯示真實數據和免費API數據
"""

import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入監控系統
from O3mh_gui_v21_optimized import PreMarketMonitor

def test_no_simulation_data():
    """測試無模擬數據版本"""
    print("🚫 測試無模擬數據版本 - 只顯示真實數據")
    print("=" * 60)
    
    # 初始化監控系統
    monitor = PreMarketMonitor()
    print(f"✅ 監控系統初始化: {monitor.name} v{monitor.version}")
    print(f"📊 免費API啟用: {'是' if monitor.free_apis_enabled else '否'}")
    
    print("\n🔍 測試各類數據獲取...")
    print("-" * 40)
    
    # 測試各類數據
    test_categories = [
        ('美股指數', monitor.get_us_indices),
        ('亞洲指數', monitor.get_asia_indices),
        ('商品價格', monitor.get_commodity_prices),
        ('外匯匯率', monitor.get_fx_rates),
        ('台灣市場', monitor.get_taiwan_futures),
        ('加密貨幣', monitor.get_crypto_prices)
    ]
    
    all_data = {}
    simulation_count = 0
    real_data_count = 0
    free_api_count = 0
    
    for category_name, get_method in test_categories:
        print(f"\n📊 測試 {category_name}:")
        
        try:
            data = get_method()
            all_data[category_name] = data
            
            if data:
                for name, item in data.items():
                    status = item.get('status', '未知')
                    
                    # 檢查數據類型
                    if '模擬數據' in status:
                        simulation_count += 1
                        print(f"  ⚠️ {name}: {status} - 這不應該出現！")
                    elif '真實數據' in status:
                        real_data_count += 1
                        print(f"  ✅ {name}: {status}")
                    elif any(api in status for api in ['CoinGecko', 'ExchangeRate-API', 'Finnhub']):
                        free_api_count += 1
                        print(f"  🆓 {name}: {status}")
                    else:
                        print(f"  ❓ {name}: {status}")
            else:
                print(f"  ⚪ 無數據 (API受限或網路問題)")
                
        except Exception as e:
            print(f"  ❌ 獲取失敗: {e}")
    
    # 統計結果
    print(f"\n📊 數據統計結果:")
    print("-" * 30)
    total_items = simulation_count + real_data_count + free_api_count
    
    print(f"總數據項目: {total_items}")
    print(f"真實數據: {real_data_count} ({real_data_count/total_items*100:.1f}%)" if total_items > 0 else "真實數據: 0")
    print(f"免費API: {free_api_count} ({free_api_count/total_items*100:.1f}%)" if total_items > 0 else "免費API: 0")
    print(f"模擬數據: {simulation_count} ({simulation_count/total_items*100:.1f}%)" if total_items > 0 else "模擬數據: 0")
    
    # 檢查是否有模擬數據
    if simulation_count > 0:
        print(f"\n❌ 發現 {simulation_count} 個模擬數據項目！")
        print("💡 這些項目不應該出現，需要進一步修復")
        return False
    else:
        print(f"\n✅ 沒有發現模擬數據！")
        print("💡 系統正確地只顯示真實數據和免費API數據")
    
    return True

def test_forex_specifically():
    """專門測試外匯數據"""
    print(f"\n💱 專門測試外匯數據")
    print("-" * 30)
    
    monitor = PreMarketMonitor()
    
    # 測試免費API外匯
    print("🆓 測試免費API外匯數據:")
    forex_data = monitor._get_forex_free_api()
    
    if forex_data:
        print("✅ 免費API外匯數據獲取成功:")
        for name, data in forex_data.items():
            print(f"  • {name}: {data['rate']:.4f} [{data['status']}]")
    else:
        print("❌ 免費API外匯數據獲取失敗")
    
    # 測試完整外匯方法
    print("\n📊 測試完整外匯方法:")
    all_forex = monitor.get_fx_rates()
    
    if all_forex:
        print("✅ 外匯數據獲取成功:")
        for name, data in all_forex.items():
            change_indicator = "📈" if data.get('change_pct', 0) > 0 else "📉" if data.get('change_pct', 0) < 0 else "➡️"
            print(f"  {change_indicator} {name}: {data['rate']:.4f} ({data.get('change_pct', 0):+.2f}%) [{data['status']}]")
    else:
        print("⚪ 外匯數據無法獲取 (API受限)")

def test_full_scan_quality():
    """測試完整掃描的數據品質"""
    print(f"\n🔍 測試完整掃描數據品質")
    print("-" * 30)
    
    monitor = PreMarketMonitor()
    
    # 執行完整掃描
    scan_results = monitor.run_full_scan()
    
    if scan_results:
        print("✅ 完整掃描成功")
        print(f"📅 掃描時間: {scan_results['timestamp']}")
        
        # 分析數據品質
        categories = ['us_indices', 'asia_indices', 'commodities', 'fx_rates', 'taiwan_futures', 'crypto']
        
        total_items = 0
        quality_breakdown = {
            '真實數據': 0,
            '免費API': 0,
            '模擬數據': 0,
            '其他': 0
        }
        
        for category in categories:
            data = scan_results.get(category, {})
            if isinstance(data, dict):
                for item_data in data.values():
                    if isinstance(item_data, dict) and 'status' in item_data:
                        total_items += 1
                        status = item_data['status']
                        
                        if '真實數據' in status:
                            quality_breakdown['真實數據'] += 1
                        elif any(api in status for api in ['CoinGecko', 'ExchangeRate-API', 'Finnhub', 'FMP']):
                            quality_breakdown['免費API'] += 1
                        elif '模擬數據' in status:
                            quality_breakdown['模擬數據'] += 1
                        else:
                            quality_breakdown['其他'] += 1
        
        print(f"\n📊 掃描結果品質分析:")
        print(f"總項目數: {total_items}")
        
        for quality_type, count in quality_breakdown.items():
            percentage = (count / total_items * 100) if total_items > 0 else 0
            if quality_type == '模擬數據' and count > 0:
                print(f"❌ {quality_type}: {count} ({percentage:.1f}%) - 不應該出現！")
            elif quality_type == '真實數據':
                print(f"✅ {quality_type}: {count} ({percentage:.1f}%)")
            elif quality_type == '免費API':
                print(f"🆓 {quality_type}: {count} ({percentage:.1f}%)")
            else:
                print(f"❓ {quality_type}: {count} ({percentage:.1f}%)")
        
        # 檢查是否符合顯示標準
        high_quality_items = quality_breakdown['真實數據'] + quality_breakdown['免費API']
        high_quality_ratio = high_quality_items / total_items if total_items > 0 else 0
        
        print(f"\n🎯 品質評估:")
        print(f"高品質數據比例: {high_quality_ratio:.1%}")
        
        if high_quality_ratio >= 0.1:
            print("✅ 符合顯示標準 (≥10%)")
        else:
            print("❌ 不符合顯示標準 (<10%)")
        
        return quality_breakdown['模擬數據'] == 0
    
    else:
        print("❌ 完整掃描失敗或數據品質不足")
        return True  # 沒有掃描結果表示沒有模擬數據

def main():
    """主測試函數"""
    print("🧪 開始測試無模擬數據系統")
    print("=" * 60)
    
    # 設置日誌級別
    logging.basicConfig(
        level=logging.WARNING,  # 只顯示警告和錯誤
        format='%(levelname)s: %(message)s'
    )
    
    try:
        # 測試1: 基本數據獲取
        test1_passed = test_no_simulation_data()
        
        # 測試2: 外匯數據專項測試
        test_forex_specifically()
        
        # 測試3: 完整掃描品質測試
        test3_passed = test_full_scan_quality()
        
        # 總結
        print(f"\n🎯 測試總結")
        print("=" * 30)
        
        if test1_passed and test3_passed:
            print("✅ 所有測試通過！")
            print("💡 系統成功移除了模擬數據")
            print("🎉 現在只會顯示真實數據和免費API數據")
        else:
            print("❌ 部分測試失敗")
            print("💡 系統仍需要進一步調整")
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
