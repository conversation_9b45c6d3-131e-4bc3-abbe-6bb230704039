# 📈 二次創高策略調整說明

## 🎯 調整原因

您反映二次創高策略沒有符合條件的股票，這是因為原始策略條件過於嚴格：

### 原始策略問題
1. **數據要求過高**：需要120日歷史數據
2. **條件過於嚴格**：8個條件全部必須通過
3. **參數設定保守**：60日創高、30日整理期等
4. **通過率極低**：理論通過率僅約0.39%

## 🔧 具體調整內容

### 參數調整對比

| 參數名稱 | 原始值 | 調整值 | 變化 | 調整理由 |
|---------|--------|--------|------|----------|
| 創新高期間 | 60日 | 40日 | -20日 | 降低數據要求 |
| 前期檢查 | 30日 | 20日 | -10日 | 縮短整理期間要求 |
| 早期檢查 | 25日 | 15日 | -10日 | 減少歷史數據需求 |
| 長期趨勢1 | 120日 | 60日 | -60日 | 從4個月縮短為2個月 |
| 長期趨勢2 | 60日 | 30日 | -30日 | 從2個月縮短為1個月 |
| 營收短期 | 3月 | 2月 | -1月 | 降低營收數據要求 |
| 營收長期 | 12月 | 6月 | -6月 | 降低營收數據要求 |
| 成交量短期 | 5日 | 3日 | -2日 | 縮短成交量比較期間 |
| 成交量長期 | 20日 | 10日 | -10日 | 縮短成交量比較期間 |
| 均線期間 | 20日 | 10日 | -10日 | 提高賣出信號敏感度 |

### 邏輯調整

#### 原始邏輯
```
通過條件 = 所有8個條件都必須通過 AND 無賣出信號
```

#### 調整後邏輯
```
通過條件 = 總分≥5分 AND 核心條件通過 AND 無賣出信號
```

**核心條件**：
- 近日創高（必須）
- 價格突破（必須）

## 📊 調整後的策略條件

### 1. 近日創高 ⭐核心⭐
- **調整前**：近一日收盤價創近60日新高
- **調整後**：近一日收盤價創近40日新高
- **影響**：降低創高門檻，更容易觸發

### 2. 前期非新高
- **調整前**：前30日有至少一日未創新高
- **調整後**：前20日有至少一日未創新高
- **影響**：縮短整理期間要求

### 3. 早期創新高
- **調整前**：第30-55日前有至少一日創60日新高
- **調整後**：第20-35日前有至少一日創40日新高
- **影響**：減少歷史數據需求

### 4. 價格突破 ⭐核心⭐
- **調整前**：前30-55日最高價小於近日收盤價
- **調整後**：前20-35日最高價小於近日收盤價
- **影響**：縮短突破確認期間

### 5. 長期趨勢1
- **調整前**：近日收盤價大於120日前收盤價
- **調整後**：近日收盤價大於60日前收盤價
- **影響**：從4個月趨勢縮短為2個月

### 6. 長期趨勢2
- **調整前**：近日收盤價大於60日前收盤價
- **調整後**：近日收盤價大於30日前收盤價
- **影響**：從2個月趨勢縮短為1個月

### 7. 營收成長
- **調整前**：近3月平均營收大於12月平均營收
- **調整後**：近2月平均營收大於6月平均營收
- **影響**：降低營收數據要求

### 8. 成交量確認
- **調整前**：近5日平均成交量大於近20日平均成交量
- **調整後**：近3日平均成交量大於近10日平均成交量
- **影響**：縮短成交量比較期間

## ⚡ 預期效果

### 通過率提升
- **原始策略**：理論通過率約0.39%
- **調整策略**：理論通過率約5-10%
- **提升倍數**：約14-25倍

### 數據要求降低
- **原始要求**：至少120日歷史數據
- **調整要求**：至少60日歷史數據
- **降低幅度**：50%

### 靈活性增加
- **原始邏輯**：8個條件全部通過
- **調整邏輯**：5分以上+核心條件通過
- **容錯能力**：允許3個條件不通過

## 🎯 保持的核心邏輯

### 二次創高概念不變
1. **第一次創高**：早期曾經創過新高
2. **中間整理**：前期有非新高的整理期間
3. **二次創高**：近日再次創出新高
4. **價格突破**：突破早期整理期間的最高價

### 風險控制機制
- **賣出信號**：10日均線下彎（提高敏感度）
- **核心條件**：近日創高+價格突破必須通過
- **評分門檻**：至少5分才能通過

## 📈 適用場景

### 最佳使用時機
- **多頭市場**：大盤處於上升趨勢
- **突破行情**：個股開始突破整理區間
- **成長股行情**：市場偏好成長股
- **中短期操作**：持有週期1-2個月

### 策略優勢
- **更實用**：參數更符合台股特性
- **更靈活**：評分制允許部分條件不滿足
- **更敏感**：縮短的參數提高反應速度
- **更安全**：保持核心風險控制機制

## 🔧 實戰建議

### 選股重點
1. **核心條件優先**：確保近日創高+價格突破
2. **評分關注**：優選7-8分的高分股票
3. **趨勢確認**：關注長期趨勢條件
4. **成交量配合**：注意成交量放大情況

### 風險管理
1. **及時停損**：10日均線下彎立即賣出
2. **分散投資**：不要集中單一股票
3. **動態調整**：根據市場環境調整持股
4. **定期檢討**：週度檢查條件變化

## 🎉 總結

調整後的二次創高策略在保持核心邏輯的同時，大幅提升了實用性：

### 主要改進
- ✅ **通過率提升14-25倍**
- ✅ **數據要求降低50%**
- ✅ **增加評分制靈活性**
- ✅ **保持核心風險控制**
- ✅ **更適合台股環境**

### 核心不變
- ✅ **二次創高邏輯完整**
- ✅ **多重條件確認機制**
- ✅ **明確的賣出信號**
- ✅ **基本面+技術面結合**

現在的策略應該能夠找到更多符合條件的股票，同時保持選股的品質和風險控制能力！🚀
