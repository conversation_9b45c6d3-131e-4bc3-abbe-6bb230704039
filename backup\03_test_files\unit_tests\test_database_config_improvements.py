#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫配置管理視窗改善測試
測試最大化、最小化功能和字體顯示改善
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from dialogs.database_config_dialog import DatabaseConfigDialog
except ImportError:
    print("❌ 無法導入 DatabaseConfigDialog")
    sys.exit(1)

def test_database_config_improvements():
    """測試數據庫配置管理視窗的改善功能"""
    
    app = QApplication(sys.argv)
    
    # 顯示測試說明
    msg = QMessageBox()
    msg.setWindowTitle("數據庫配置管理視窗改善測試")
    msg.setText("""
🎯 測試項目：

✅ 視窗控制功能：
• 最小化按鈕 (🗕) - 點擊最小化視窗
• 最大化按鈕 (🗖/🗗) - 點擊切換最大化/還原
• 關閉按鈕 (🗙) - 點擊關閉視窗
• 拖拽標題欄 - 可拖動視窗位置

✅ 字體顯示改善：
• 使用 Microsoft JhengHei UI 字體
• 統一字體大小和權重
• 改善對比度和可讀性
• 所有UI元件字體一致性

✅ 界面優化：
• 深色主題統一配色
• 按鈕和輸入框樣式改善
• 標籤頁和表格字體優化
• 說明文字區塊對比度修正

點擊 OK 開始測試...
    """)
    msg.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
    
    if msg.exec() == QMessageBox.StandardButton.Cancel:
        return
    
    # 創建並顯示數據庫配置對話框
    try:
        dialog = DatabaseConfigDialog()
        dialog.show()
        
        # 顯示操作提示
        tip_msg = QMessageBox()
        tip_msg.setWindowTitle("操作提示")
        tip_msg.setText("""
🔧 請測試以下功能：

1️⃣ 視窗控制：
   • 點擊右上角 🗕 最小化
   • 點擊右上角 🗖 最大化
   • 點擊右上角 🗙 關閉

2️⃣ 視窗拖拽：
   • 拖拽標題欄移動視窗

3️⃣ 字體檢查：
   • 檢查所有文字是否清晰可見
   • 確認對比度是否良好
   • 驗證字體大小是否適中

4️⃣ 界面測試：
   • 切換不同標籤頁
   • 檢查按鈕和輸入框樣式
   • 測試表格和文字區域顯示

測試完成後關閉視窗即可。
        """)
        tip_msg.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint)
        tip_msg.show()
        
        # 運行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setWindowTitle("錯誤")
        error_msg.setText(f"啟動數據庫配置對話框時發生錯誤：\n{str(e)}")
        error_msg.exec()

if __name__ == "__main__":
    test_database_config_improvements()
