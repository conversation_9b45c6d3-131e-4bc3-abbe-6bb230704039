#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修改後的即時股價監控系統界面
驗證是否成功移除四象限，只保留自選股監控
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def test_monitor_ui():
    """測試監控界面"""
    try:
        # 導入修改後的監控系統
        from real_time_stock_monitor import RealTimeStockMonitor
        
        print("✅ 成功導入修改後的監控系統")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建監控視窗
        monitor = RealTimeStockMonitor()
        
        # 檢查界面元素
        print("🔍 檢查界面元素...")
        
        # 檢查是否移除了四象限相關元素
        removed_elements = []
        if not hasattr(monitor, 'volume_widget'):
            removed_elements.append("成交量排行")
        if not hasattr(monitor, 'gainers_widget'):
            removed_elements.append("漲幅排行")
        if not hasattr(monitor, 'losers_widget'):
            removed_elements.append("跌幅排行")
            
        # 檢查是否保留了自選股監控
        if hasattr(monitor, 'watchlist_widget'):
            print("✅ 自選股監控元件存在")
        else:
            print("❌ 自選股監控元件不存在")
            
        if removed_elements:
            print(f"✅ 成功移除元素: {', '.join(removed_elements)}")
        else:
            print("⚠️ 可能未完全移除四象限元素")
            
        # 檢查視窗標題
        if "自選股" in monitor.windowTitle():
            print("✅ 視窗標題已更新")
        else:
            print("⚠️ 視窗標題可能需要更新")
            
        # 顯示視窗
        monitor.show()
        print("✅ 監控視窗已顯示")
        
        # 設置自動關閉計時器（5秒後關閉）
        def close_app():
            print("🔄 測試完成，關閉應用程式")
            monitor.close()
            app.quit()
            
        timer = QTimer()
        timer.timeout.connect(close_app)
        timer.start(5000)  # 5秒後關閉
        
        # 顯示測試結果對話框
        QMessageBox.information(
            monitor,
            "測試結果",
            f"✅ 即時股價監控系統修改測試\n\n"
            f"🎯 修改內容:\n"
            f"• 移除成交量排行象限\n"
            f"• 移除漲幅排行象限\n"
            f"• 移除跌幅排行象限\n"
            f"• 保留自選股監控為全區域\n"
            f"• 移除自選股監控標題以節省空間\n\n"
            f"📊 界面狀態:\n"
            f"• 自選股監控: {'✅ 存在' if hasattr(monitor, 'watchlist_widget') else '❌ 不存在'}\n"
            f"• 移除的元素: {', '.join(removed_elements) if removed_elements else '無'}\n"
            f"• 垂直空間優化: ✅ 完成\n\n"
            f"⏰ 視窗將在5秒後自動關閉"
        )
        
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return 1
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return 1

if __name__ == "__main__":
    print("🚀 開始測試修改後的即時股價監控系統...")
    print("=" * 50)
    
    result = test_monitor_ui()
    
    print("=" * 50)
    if result == 0:
        print("✅ 測試完成")
    else:
        print("❌ 測試失敗")
    
    sys.exit(result)
