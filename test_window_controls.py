#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試台灣證交所市場數據爬蟲對話框的窗口控制按鈕
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

class TestMainWindow(QMainWindow):
    """測試主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("測試主窗口")
        self.setGeometry(100, 100, 400, 300)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加測試按鈕
        test_btn = QPushButton("🚀 開啟台灣證交所市場數據爬蟲")
        test_btn.clicked.connect(self.open_twse_dialog)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(test_btn)
        
        self.twse_dialog = None
    
    def open_twse_dialog(self):
        """開啟台灣證交所市場數據爬蟲對話框"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            # 如果對話框已存在且可見，就將其提到前面
            if self.twse_dialog and self.twse_dialog.isVisible():
                self.twse_dialog.raise_()
                self.twse_dialog.activateWindow()
                return
            
            # 創建新的對話框
            self.twse_dialog = TWSEMarketDataDialog(self)
            self.twse_dialog.show()
            self.twse_dialog.raise_()
            self.twse_dialog.activateWindow()
            
            print("✅ 對話框已開啟")
            print("🔍 請檢查窗口右上角是否有以下按鈕：")
            print("   - ➖ 最小化按鈕")
            print("   - ⬜ 最大化按鈕")
            print("   - ❌ 關閉按鈕")
            print("🎯 測試功能：")
            print("   1. 點擊最小化按鈕，窗口應該最小化到工作列")
            print("   2. 點擊最大化按鈕，窗口應該全螢幕顯示")
            print("   3. 再次點擊最大化按鈕，窗口應該恢復原始大小")
            print("   4. 點擊關閉按鈕，窗口應該關閉")
            print("   5. 可以拖拽窗口標題列來移動窗口")
            print("   6. 可以拖拽窗口邊緣來調整大小")
            
        except ImportError as e:
            print(f"❌ 導入錯誤: {e}")
            print("請確認 twse_market_data_dialog.py 檔案存在")
        except Exception as e:
            print(f"❌ 其他錯誤: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("台灣證交所市場數據爬蟲測試")
    app.setApplicationVersion("1.0")
    
    # 創建主窗口
    main_window = TestMainWindow()
    main_window.show()
    
    print("🚀 測試程式已啟動")
    print("📋 測試步驟：")
    print("1. 點擊「開啟台灣證交所市場數據爬蟲」按鈕")
    print("2. 檢查新開啟的對話框右上角的控制按鈕")
    print("3. 測試各個按鈕的功能")
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
