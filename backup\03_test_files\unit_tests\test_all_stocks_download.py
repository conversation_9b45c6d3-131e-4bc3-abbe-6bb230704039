#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試全股票下載功能
"""

import time

def test_all_stocks_download():
    """測試全股票下載功能"""
    print("🧪 測試全股票下載功能")
    print("=" * 40)
    
    try:
        # 導入下載器
        from goodinfo_monthly_revenue_downloader import GoodInfoMonthlyRevenueDownloader
        
        downloader = GoodInfoMonthlyRevenueDownloader()
        
        print("🚀 開始測試全股票下載...")
        
        # 使用台積電代號觸發全股票下載
        result = downloader.download_stock_revenue('2330')
        
        if result:
            print("✅ 下載成功！")
            
            # 處理Excel文件
            print("📊 處理Excel文件...")
            
            from all_stocks_revenue_processor import AllStocksRevenueProcessor
            processor = AllStocksRevenueProcessor()
            
            import glob
            import os
            
            excel_files = glob.glob(os.path.join(downloader.download_dir, "*.xls*"))
            
            if excel_files:
                latest_file = max(excel_files, key=os.path.getctime)
                print(f"📋 處理文件: {os.path.basename(latest_file)}")
                
                revenue_data = processor.process_excel_file(latest_file)
                
                if revenue_data:
                    print(f"✅ 成功解析 {len(revenue_data)} 支股票")
                    
                    # 儲存到數據庫
                    if processor.save_to_database(revenue_data):
                        print("✅ 數據已儲存到數據庫")
                        
                        # 顯示前10名
                        top_performers = processor.get_top_performers(10)
                        if top_performers:
                            print(f"\n🏆 營收前10名:")
                            for i, (stock_id, name, revenue, yoy, market) in enumerate(top_performers['top_revenue'], 1):
                                revenue_str = f"{revenue/1000:.0f}萬" if revenue else "N/A"
                                yoy_str = f"{yoy:+.1f}%" if yoy else "N/A"
                                print(f"   {i:2d}. {stock_id} {name:<8} 營收:{revenue_str:<10} 年增率:{yoy_str} ({market})")
                        
                        print(f"\n🎉 測試成功！")
                        return True
                    else:
                        print("❌ 儲存失敗")
                else:
                    print("❌ 解析失敗")
            else:
                print("❌ 未找到Excel文件")
        else:
            print("❌ 下載失敗")
        
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 全股票下載功能測試")
    print("=" * 50)
    
    response = input("是否要測試全股票下載功能？(y/n): ").lower().strip()
    if response != 'y':
        print("❌ 用戶取消")
        return
    
    success = test_all_stocks_download()
    
    if success:
        print("\n🎉 測試完成！全股票下載功能正常")
    else:
        print("\n❌ 測試失敗")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
