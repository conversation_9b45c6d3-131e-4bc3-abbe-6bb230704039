#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 auto_update.py 的新結構
"""

import sys
import os

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_import_structure():
    """測試導入結構"""
    print("🧪 測試 auto_update.py 導入結構...")
    
    try:
        # 測試導入 auto_update 模組
        import auto_update
        print("✅ 成功導入 auto_update 模組")
        
        # 檢查主要函數是否存在
        required_functions = [
            'main',
            'auto_update',
            'save_pe_to_database',
            'safe_commit'
        ]
        
        for func_name in required_functions:
            if hasattr(auto_update, func_name):
                print(f"✅ 函數 {func_name} 存在")
            else:
                print(f"❌ 函數 {func_name} 不存在")
        
        # 檢查爬蟲函數是否可用
        crawler_functions = [
            'crawl_price',
            'crawl_bargin', 
            'crawl_pe',
            'crawl_benchmark',
            'crawl_monthly_report',
            'crawl_twse_divide_ratio',
            'crawl_otc_divide_ratio',
            'crawl_twse_cap_reduction',
            'crawl_otc_cap_reduction'
        ]
        
        print("\n📋 檢查爬蟲函數可用性:")
        for func_name in crawler_functions:
            if hasattr(auto_update, func_name):
                print(f"✅ {func_name} 可用")
            else:
                print(f"❌ {func_name} 不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_update_tasks_structure():
    """測試更新任務結構"""
    print("\n🧪 測試更新任務結構...")
    
    try:
        # 模擬 main 函數中的更新任務定義
        from auto_update import (
            crawl_price, crawl_bargin, crawl_pe, crawl_benchmark, 
            crawl_monthly_report, crawl_twse_divide_ratio, 
            crawl_otc_divide_ratio, crawl_twse_cap_reduction, 
            crawl_otc_cap_reduction, date_range, month_range
        )
        
        update_tasks = [
            ('price', crawl_price, date_range),
            ('bargin_report', crawl_bargin, date_range),
            ('pe', crawl_pe, date_range),
            ('benchmark', crawl_benchmark, date_range),
            ('monthly_report', crawl_monthly_report, month_range),
            ('twse_divide_ratio', crawl_twse_divide_ratio, None),
            ('otc_divide_ratio', crawl_otc_divide_ratio, None),
            ('twse_cap_reduction', crawl_twse_cap_reduction, None),
            ('otc_cap_reduction', crawl_otc_cap_reduction, None),
        ]
        
        print(f"✅ 成功定義 {len(update_tasks)} 個更新任務:")
        for i, (table_name, crawl_func, time_range_func) in enumerate(update_tasks, 1):
            time_func_name = time_range_func.__name__ if time_range_func else "None"
            print(f"   {i}. {table_name} -> {crawl_func.__name__} -> {time_func_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新任務結構測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_line_args():
    """測試命令行參數處理"""
    print("\n🧪 測試命令行參數處理...")
    
    try:
        # 模擬不同的命令行參數
        test_cases = [
            [],  # 無參數
            ['pe'],  # 指定 PE
            ['benchmark'],  # 指定 benchmark
            ['invalid_table'],  # 無效的資料表
        ]
        
        from auto_update import (
            crawl_price, crawl_bargin, crawl_pe, crawl_benchmark, 
            crawl_monthly_report, crawl_twse_divide_ratio, 
            crawl_otc_divide_ratio, crawl_twse_cap_reduction, 
            crawl_otc_cap_reduction, date_range, month_range
        )
        
        update_tasks = [
            ('price', crawl_price, date_range),
            ('bargin_report', crawl_bargin, date_range),
            ('pe', crawl_pe, date_range),
            ('benchmark', crawl_benchmark, date_range),
            ('monthly_report', crawl_monthly_report, month_range),
            ('twse_divide_ratio', crawl_twse_divide_ratio, None),
            ('otc_divide_ratio', crawl_otc_divide_ratio, None),
            ('twse_cap_reduction', crawl_twse_cap_reduction, None),
            ('otc_cap_reduction', crawl_otc_cap_reduction, None),
        ]
        
        for test_argv in test_cases:
            print(f"\n📝 測試參數: {test_argv}")
            
            # 模擬命令行參數處理邏輯
            current_tasks = update_tasks.copy()
            
            if len(test_argv) > 0:
                specified_table = test_argv[0].lower()
                filtered_tasks = [task for task in update_tasks if task[0] == specified_table]
                if filtered_tasks:
                    current_tasks = filtered_tasks
                    print(f"   🎯 專門更新 {specified_table} 資料")
                else:
                    print(f"   ⚠️ 未找到資料表 '{specified_table}'，將更新所有資料表")
            
            print(f"   📋 將更新 {len(current_tasks)} 個資料表")
        
        print("✅ 命令行參數處理測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 命令行參數處理測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 auto_update.py 結構測試")
    print("=" * 50)
    
    tests = [
        test_import_structure,
        test_update_tasks_structure,
        test_command_line_args
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！auto_update.py 結構正確")
    else:
        print("⚠️ 部分測試失敗，請檢查 auto_update.py")

if __name__ == "__main__":
    main()
