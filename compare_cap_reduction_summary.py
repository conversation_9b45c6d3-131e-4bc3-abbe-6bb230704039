#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較 TWSE 和 OTC 減資資料的綜合報告
"""

import sqlite3
import pandas as pd
import os

def compare_cap_reduction_data():
    """比較 TWSE 和 OTC 減資資料"""
    
    twse_db = 'D:/Finlab/history/tables/twse_cap_reduction.db'
    otc_db = 'D:/Finlab/history/tables/otc_cap_reduction.db'
    
    print("=" * 80)
    print("📊 TWSE vs OTC 減資資料比較報告")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(twse_db):
        print(f"❌ TWSE 檔案不存在: {twse_db}")
        return False
    
    if not os.path.exists(otc_db):
        print(f"❌ OTC 檔案不存在: {otc_db}")
        return False
    
    try:
        # 連接 TWSE 資料庫
        twse_conn = sqlite3.connect(twse_db)
        twse_cursor = twse_conn.cursor()
        
        # 連接 OTC 資料庫
        otc_conn = sqlite3.connect(otc_db)
        otc_cursor = otc_conn.cursor()
        
        print("📈 基本統計比較:")
        print("-" * 50)
        
        # TWSE 統計
        twse_cursor.execute("SELECT COUNT(*) FROM twse_cap_reduction")
        twse_total = twse_cursor.fetchone()[0]
        
        twse_cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM twse_cap_reduction")
        twse_stocks = twse_cursor.fetchone()[0]
        
        twse_cursor.execute("SELECT MIN(date), MAX(date) FROM twse_cap_reduction")
        twse_date_range = twse_cursor.fetchone()
        
        # OTC 統計
        otc_cursor.execute("SELECT COUNT(*) FROM otc_cap_reduction")
        otc_total = otc_cursor.fetchone()[0]
        
        otc_cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM otc_cap_reduction")
        otc_stocks = otc_cursor.fetchone()[0]
        
        otc_cursor.execute("SELECT MIN(date), MAX(date) FROM otc_cap_reduction")
        otc_date_range = otc_cursor.fetchone()
        
        print(f"TWSE (上市):")
        print(f"   總減資案例: {twse_total:,} 次")
        print(f"   涉及股票數: {twse_stocks:,} 檔")
        print(f"   日期範圍: {twse_date_range[0][:10]} ~ {twse_date_range[1][:10]}")
        
        print(f"\nOTC (上櫃):")
        print(f"   總減資案例: {otc_total:,} 次")
        print(f"   涉及股票數: {otc_stocks:,} 檔")
        print(f"   日期範圍: {otc_date_range[0][:10]} ~ {otc_date_range[1][:10]}")
        
        print(f"\n合計:")
        print(f"   總減資案例: {twse_total + otc_total:,} 次")
        print(f"   涉及股票數: {twse_stocks + otc_stocks:,} 檔")
        
        # 減資原因比較
        print(f"\n📊 減資原因比較:")
        print("-" * 50)
        
        # TWSE 減資原因
        twse_cursor.execute("""
            SELECT 減資原因, COUNT(*) as count 
            FROM twse_cap_reduction 
            GROUP BY 減資原因 
            ORDER BY count DESC
        """)
        twse_reasons = twse_cursor.fetchall()
        
        # OTC 減資原因
        otc_cursor.execute("""
            SELECT 減資源因, COUNT(*) as count 
            FROM otc_cap_reduction 
            GROUP BY 減資源因 
            ORDER BY count DESC
        """)
        otc_reasons = otc_cursor.fetchall()
        
        print("TWSE (上市) 減資原因:")
        for reason, count in twse_reasons:
            percentage = (count / twse_total) * 100
            print(f"   {reason}: {count} 次 ({percentage:.1f}%)")
        
        print("\nOTC (上櫃) 減資原因:")
        for reason, count in otc_reasons:
            percentage = (count / otc_total) * 100
            print(f"   {reason}: {count} 次 ({percentage:.1f}%)")
        
        # 年度趨勢比較
        print(f"\n📈 年度減資趨勢比較:")
        print("-" * 50)
        
        # TWSE 年度統計
        twse_cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM twse_cap_reduction 
            GROUP BY year 
            ORDER BY year DESC
        """)
        twse_years = dict(twse_cursor.fetchall())
        
        # OTC 年度統計
        otc_cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM otc_cap_reduction 
            GROUP BY year 
            ORDER BY year DESC
        """)
        otc_years = dict(otc_cursor.fetchall())
        
        # 合併年度資料
        all_years = sorted(set(list(twse_years.keys()) + list(otc_years.keys())), reverse=True)
        
        print(f"{'年度':<6} {'TWSE':<8} {'OTC':<8} {'合計':<8}")
        print("-" * 32)
        for year in all_years:
            twse_count = twse_years.get(year, 0)
            otc_count = otc_years.get(year, 0)
            total_count = twse_count + otc_count
            print(f"{year:<6} {twse_count:<8} {otc_count:<8} {total_count:<8}")
        
        # 減資比例統計比較
        print(f"\n📊 減資比例統計比較:")
        print("-" * 50)
        
        # TWSE 減資比例統計
        twse_cursor.execute("""
            SELECT 
                MIN(twse_cap_divide_ratio) as min_ratio,
                MAX(twse_cap_divide_ratio) as max_ratio,
                AVG(twse_cap_divide_ratio) as avg_ratio
            FROM twse_cap_reduction 
            WHERE twse_cap_divide_ratio IS NOT NULL
        """)
        twse_ratio_stats = twse_cursor.fetchone()
        
        # OTC 減資比例統計
        otc_cursor.execute("""
            SELECT 
                MIN(otc_cap_divide_ratio) as min_ratio,
                MAX(otc_cap_divide_ratio) as max_ratio,
                AVG(otc_cap_divide_ratio) as avg_ratio
            FROM otc_cap_reduction 
            WHERE otc_cap_divide_ratio IS NOT NULL
        """)
        otc_ratio_stats = otc_cursor.fetchone()
        
        print("TWSE (上市) 減資比例:")
        if twse_ratio_stats and twse_ratio_stats[0] is not None:
            print(f"   最小比例: {twse_ratio_stats[0]:.4f}")
            print(f"   最大比例: {twse_ratio_stats[1]:.4f}")
            print(f"   平均比例: {twse_ratio_stats[2]:.4f}")
        
        print("\nOTC (上櫃) 減資比例:")
        if otc_ratio_stats and otc_ratio_stats[0] is not None:
            print(f"   最小比例: {otc_ratio_stats[0]:.4f}")
            print(f"   最大比例: {otc_ratio_stats[1]:.4f}")
            print(f"   平均比例: {otc_ratio_stats[2]:.4f}")
        
        # 檔案大小比較
        print(f"\n💾 檔案大小比較:")
        print("-" * 50)
        
        twse_size = os.path.getsize(twse_db) / 1024  # KB
        otc_size = os.path.getsize(otc_db) / 1024   # KB
        
        print(f"TWSE 資料庫: {twse_size:.1f} KB")
        print(f"OTC 資料庫:  {otc_size:.1f} KB")
        print(f"總計:       {twse_size + otc_size:.1f} KB")
        
        twse_conn.close()
        otc_conn.close()
        
        print(f"\n✅ 比較報告完成！")
        return True
        
    except Exception as e:
        print(f"❌ 比較過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    compare_cap_reduction_data()
