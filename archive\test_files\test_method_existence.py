#!/usr/bin/env python3
"""
測試方法是否存在
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

def test_method_existence():
    """測試方法是否存在"""
    print("🧪 測試方法是否存在")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查方法是否存在
        method_name = 'save_strategy_result_to_cache'
        
        print(f"\n🔍 檢查方法: {method_name}")
        
        if hasattr(window, method_name):
            method = getattr(window, method_name)
            print(f"  ✅ 方法存在")
            print(f"  📍 方法類型: {type(method)}")
            print(f"  📍 是否可調用: {callable(method)}")
            
            # 檢查方法的文檔字符串
            if hasattr(method, '__doc__') and method.__doc__:
                print(f"  📝 文檔字符串: {method.__doc__.strip()}")
            
            # 嘗試獲取方法的代碼位置
            if hasattr(method, '__code__'):
                code = method.__code__
                print(f"  📍 文件名: {code.co_filename}")
                print(f"  📍 行號: {code.co_firstlineno}")
            
        else:
            print(f"  ❌ 方法不存在")
            
            # 列出所有可用的方法
            print(f"\n📋 可用的方法（包含'save'或'cache'）:")
            all_methods = [attr for attr in dir(window) if callable(getattr(window, attr))]
            relevant_methods = [method for method in all_methods if 'save' in method.lower() or 'cache' in method.lower()]
            
            for method in relevant_methods:
                print(f"  📌 {method}")
        
        # 檢查類的MRO（方法解析順序）
        print(f"\n🔍 類的方法解析順序:")
        for i, cls in enumerate(StockScreenerGUI.__mro__):
            print(f"  {i+1}. {cls}")
        
        # 檢查是否有語法錯誤
        print(f"\n🔍 檢查語法錯誤:")
        try:
            import ast
            with open("O3mh_gui_v21_optimized.py", "r", encoding="utf-8") as f:
                source_code = f.read()
            
            # 嘗試解析AST
            tree = ast.parse(source_code)
            print(f"  ✅ 語法檢查通過")
            
        except SyntaxError as e:
            print(f"  ❌ 語法錯誤: {e}")
            print(f"  📍 行號: {e.lineno}")
            print(f"  📍 位置: {e.offset}")
        except Exception as e:
            print(f"  ⚠️ 語法檢查失敗: {e}")
        
        return hasattr(window, method_name)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_file_integrity():
    """檢查文件完整性"""
    print(f"\n🔍 檢查文件完整性:")
    
    try:
        with open("O3mh_gui_v21_optimized.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        print(f"  📊 總行數: {len(lines)}")
        
        # 查找save_strategy_result_to_cache方法定義
        method_line = None
        for i, line in enumerate(lines):
            if "def save_strategy_result_to_cache" in line:
                method_line = i + 1
                break
        
        if method_line:
            print(f"  ✅ 找到方法定義在第 {method_line} 行")
            print(f"  📝 方法定義: {lines[method_line-1].strip()}")
            
            # 檢查縮進
            indent = len(lines[method_line-1]) - len(lines[method_line-1].lstrip())
            print(f"  📏 縮進: {indent} 個空格")
            
            # 檢查前後幾行
            print(f"\n  📋 前後上下文:")
            start = max(0, method_line - 3)
            end = min(len(lines), method_line + 3)
            
            for i in range(start, end):
                marker = ">>>" if i == method_line - 1 else "   "
                print(f"  {marker} {i+1:4d}: {lines[i].rstrip()}")
        else:
            print(f"  ❌ 未找到方法定義")
        
        # 查找方法調用
        call_lines = []
        for i, line in enumerate(lines):
            if "save_strategy_result_to_cache" in line and "def " not in line:
                call_lines.append(i + 1)
        
        if call_lines:
            print(f"\n  📞 找到方法調用在第 {call_lines} 行")
            for line_num in call_lines:
                print(f"  📝 調用: {lines[line_num-1].strip()}")
        else:
            print(f"  ⚠️ 未找到方法調用")
            
    except Exception as e:
        print(f"  ❌ 檢查文件失敗: {e}")

def main():
    """主函數"""
    print("🚀 啟動方法存在性測試")
    print("=" * 50)
    
    # 檢查文件完整性
    check_file_integrity()
    
    # 測試方法存在性
    success = test_method_existence()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 方法存在性測試通過")
        print("問題可能是:")
        print("  1. Python解釋器緩存問題")
        print("  2. 模組重載問題")
        print("  3. 運行時動態修改問題")
        
        print(f"\n💡 建議解決方案:")
        print("  1. 重新啟動Python解釋器")
        print("  2. 清除Python緩存文件")
        print("  3. 重新載入模組")
    else:
        print("❌ 方法存在性測試失敗")
        print("需要檢查方法定義是否正確")

if __name__ == "__main__":
    main()
