#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 otc_divide_ratio.db 檔案內容
"""

import sqlite3
import pandas as pd
import os

def check_otc_divide_ratio_db():
    """檢查櫃買除權息資料庫"""
    db_file = 'D:/Finlab/history/tables/otc_divide_ratio.db'
    
    if not os.path.exists(db_file):
        print(f"❌ 資料庫檔案不存在: {db_file}")
        return
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        
        # 檢查表格
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 資料庫中的表格: {[table[0] for table in tables]}")
        
        # 檢查 otc_divide_ratio 表格
        if ('otc_divide_ratio',) in tables:
            # 獲取表格結構
            cursor.execute("PRAGMA table_info(otc_divide_ratio);")
            columns = cursor.fetchall()
            print(f"\n📊 otc_divide_ratio 表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 獲取資料統計
            cursor.execute("SELECT COUNT(*) FROM otc_divide_ratio;")
            total_records = cursor.fetchone()[0]
            print(f"\n📈 總記錄數: {total_records:,}")
            
            # 獲取股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM otc_divide_ratio;")
            unique_stocks = cursor.fetchone()[0]
            print(f"📈 股票數量: {unique_stocks:,}")
            
            # 獲取日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM otc_divide_ratio WHERE date IS NOT NULL;")
            date_range = cursor.fetchone()
            if date_range and date_range[0]:
                print(f"📅 日期範圍: {date_range[0]} ~ {date_range[1]}")
            
            # 顯示前幾筆資料
            print(f"\n📝 前5筆資料:")
            df_sample = pd.read_sql("SELECT * FROM otc_divide_ratio LIMIT 5", conn)
            print(df_sample.to_string(index=False))
            
            # 顯示最新的資料
            print(f"\n📝 最新5筆資料:")
            df_latest = pd.read_sql("SELECT * FROM otc_divide_ratio ORDER BY date DESC LIMIT 5", conn)
            print(df_latest.to_string(index=False))
            
        else:
            print(f"❌ 找不到 otc_divide_ratio 表格")
        
        conn.close()
        
        # 檢查檔案大小
        file_size = os.path.getsize(db_file) / 1024  # KB
        print(f"\n💾 檔案大小: {file_size:.1f} KB")
        
    except Exception as e:
        print(f"❌ 檢查資料庫失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_otc_divide_ratio_db()
