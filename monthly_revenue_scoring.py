#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月營收綜合評分系統
實現多種月營收YoY和MoM的綜合評價方法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple

class MonthlyRevenueScorer:
    """月營收綜合評分器"""
    
    def __init__(self):
        self.scoring_methods = {
            'weighted_average': self.weighted_average_score,
            'quadrant_analysis': self.quadrant_analysis_score,
            'momentum_score': self.momentum_score,
            'stability_score': self.stability_score,
            'comprehensive_score': self.comprehensive_score
        }
    
    def weighted_average_score(self, yoy: float, mom: float, 
                             yoy_weight: float = 0.7, mom_weight: float = 0.3) -> Dict:
        """
        加權平均評分法
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
            yoy_weight: YoY權重 (預設0.7)
            mom_weight: MoM權重 (預設0.3)
        
        Returns:
            Dict: 包含評分和評級的字典
        """
        # 正規化權重
        total_weight = yoy_weight + mom_weight
        yoy_weight = yoy_weight / total_weight
        mom_weight = mom_weight / total_weight
        
        # 計算加權平均
        weighted_score = yoy * yoy_weight + mom * mom_weight
        
        # 評級分類
        if weighted_score >= 20:
            grade = "A+ (優異)"
        elif weighted_score >= 10:
            grade = "A (良好)"
        elif weighted_score >= 5:
            grade = "B+ (尚可)"
        elif weighted_score >= 0:
            grade = "B (持平)"
        elif weighted_score >= -10:
            grade = "C (偏弱)"
        else:
            grade = "D (疲弱)"
        
        return {
            'method': '加權平均法',
            'score': round(weighted_score, 2),
            'grade': grade,
            'yoy_weight': yoy_weight,
            'mom_weight': mom_weight
        }
    
    def quadrant_analysis_score(self, yoy: float, mom: float) -> Dict:
        """
        四象限分析評分法
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
        
        Returns:
            Dict: 包含象限分析結果的字典
        """
        # 判斷象限
        if yoy > 0 and mom > 0:
            quadrant = "第一象限"
            description = "雙重成長"
            score = 90 + min(yoy + mom, 10)  # 90-100分
            grade = "A+"
            color = "🟢"
        elif yoy > 0 and mom <= 0:
            quadrant = "第二象限"
            description = "長期成長，短期調整"
            score = 70 + min(yoy, 20) + max(mom, -10)  # 約60-90分
            grade = "A" if score >= 80 else "B+"
            color = "🟡"
        elif yoy <= 0 and mom > 0:
            quadrant = "第三象限"
            description = "短期復甦，長期待觀察"
            score = 50 + min(mom, 20) + max(yoy, -10)  # 約40-70分
            grade = "B" if score >= 60 else "C"
            color = "🟡"
        else:  # yoy <= 0 and mom <= 0
            quadrant = "第四象限"
            description = "雙重衰退"
            score = 30 + max(yoy + mom, -30)  # 0-30分
            grade = "D"
            color = "🔴"
        
        return {
            'method': '四象限分析法',
            'quadrant': quadrant,
            'description': description,
            'score': max(0, min(100, round(score, 1))),
            'grade': grade,
            'color': color
        }
    
    def momentum_score(self, yoy: float, mom: float) -> Dict:
        """
        動能評分法 - 重視成長加速度
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
        
        Returns:
            Dict: 包含動能評分結果的字典
        """
        # 動能計算：如果MoM > YoY/12，表示成長加速
        monthly_yoy = yoy / 12  # 年增率轉月增率概念
        acceleration = mom - monthly_yoy
        
        # 基礎分數
        base_score = (yoy * 0.6 + mom * 0.4)
        
        # 加速度獎勵/懲罰
        if acceleration > 2:
            momentum_bonus = 10
            momentum_desc = "強勁加速"
        elif acceleration > 0:
            momentum_bonus = 5
            momentum_desc = "溫和加速"
        elif acceleration > -2:
            momentum_bonus = 0
            momentum_desc = "穩定成長"
        else:
            momentum_bonus = -5
            momentum_desc = "成長減速"
        
        final_score = base_score + momentum_bonus
        
        # 評級
        if final_score >= 25:
            grade = "A+ (動能強勁)"
        elif final_score >= 15:
            grade = "A (動能良好)"
        elif final_score >= 5:
            grade = "B (動能普通)"
        elif final_score >= -5:
            grade = "C (動能偏弱)"
        else:
            grade = "D (動能疲弱)"
        
        return {
            'method': '動能評分法',
            'score': round(final_score, 2),
            'grade': grade,
            'acceleration': round(acceleration, 2),
            'momentum_desc': momentum_desc,
            'base_score': round(base_score, 2),
            'momentum_bonus': momentum_bonus
        }
    
    def stability_score(self, yoy: float, mom: float) -> Dict:
        """
        穩定性評分法 - 重視成長的穩定性
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
        
        Returns:
            Dict: 包含穩定性評分結果的字典
        """
        # 計算YoY和MoM的差異程度
        monthly_yoy = yoy / 12
        volatility = abs(mom - monthly_yoy)
        
        # 基礎分數（偏重YoY）
        base_score = yoy * 0.8 + mom * 0.2
        
        # 穩定性調整
        if volatility <= 2:
            stability_bonus = 5
            stability_desc = "非常穩定"
        elif volatility <= 5:
            stability_bonus = 2
            stability_desc = "相對穩定"
        elif volatility <= 10:
            stability_bonus = 0
            stability_desc = "波動適中"
        else:
            stability_bonus = -3
            stability_desc = "波動較大"
        
        final_score = base_score + stability_bonus
        
        # 評級
        if final_score >= 20:
            grade = "A+ (穩健成長)"
        elif final_score >= 10:
            grade = "A (穩定成長)"
        elif final_score >= 0:
            grade = "B (成長平穩)"
        elif final_score >= -10:
            grade = "C (略有波動)"
        else:
            grade = "D (波動較大)"
        
        return {
            'method': '穩定性評分法',
            'score': round(final_score, 2),
            'grade': grade,
            'volatility': round(volatility, 2),
            'stability_desc': stability_desc,
            'base_score': round(base_score, 2),
            'stability_bonus': stability_bonus
        }
    
    def comprehensive_score(self, yoy: float, mom: float) -> Dict:
        """
        綜合評分法 - 結合多種評分方法
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
        
        Returns:
            Dict: 包含綜合評分結果的字典
        """
        # 獲取各種評分
        weighted = self.weighted_average_score(yoy, mom)
        quadrant = self.quadrant_analysis_score(yoy, mom)
        momentum = self.momentum_score(yoy, mom)
        stability = self.stability_score(yoy, mom)
        
        # 綜合計算（各方法權重）
        weights = {
            'weighted': 0.3,
            'quadrant': 0.3,
            'momentum': 0.2,
            'stability': 0.2
        }
        
        # 正規化各評分到0-100範圍
        normalized_scores = {
            'weighted': max(0, min(100, weighted['score'] + 50)),
            'quadrant': quadrant['score'],
            'momentum': max(0, min(100, momentum['score'] + 50)),
            'stability': max(0, min(100, stability['score'] + 50))
        }
        
        # 計算綜合分數
        comprehensive = sum(normalized_scores[method] * weight 
                          for method, weight in weights.items())
        
        # 最終評級
        if comprehensive >= 85:
            final_grade = "A+ (卓越)"
        elif comprehensive >= 75:
            final_grade = "A (優秀)"
        elif comprehensive >= 65:
            final_grade = "B+ (良好)"
        elif comprehensive >= 55:
            final_grade = "B (普通)"
        elif comprehensive >= 45:
            final_grade = "C (偏弱)"
        else:
            final_grade = "D (疲弱)"
        
        return {
            'method': '綜合評分法',
            'score': round(comprehensive, 1),
            'grade': final_grade,
            'sub_scores': normalized_scores,
            'weights': weights,
            'details': {
                'weighted': weighted,
                'quadrant': quadrant,
                'momentum': momentum,
                'stability': stability
            }
        }
    
    def evaluate_company(self, yoy: float, mom: float, 
                        methods: List[str] = None) -> Dict:
        """
        評價單一公司的月營收表現
        
        Args:
            yoy: 年增率 (%)
            mom: 月增率 (%)
            methods: 要使用的評分方法列表
        
        Returns:
            Dict: 包含所有評分結果的字典
        """
        if methods is None:
            methods = list(self.scoring_methods.keys())
        
        results = {}
        for method in methods:
            if method in self.scoring_methods:
                results[method] = self.scoring_methods[method](yoy, mom)
        
        return results

def demo_scoring_system():
    """示範評分系統的使用"""
    print("=" * 80)
    print("📊 月營收綜合評分系統示範")
    print("=" * 80)
    
    scorer = MonthlyRevenueScorer()
    
    # 測試案例
    test_cases = [
        ("台積電", 15.5, 8.2),   # 雙重成長
        ("鴻海", 12.3, -2.1),    # 長期成長，短期調整
        ("聯發科", -5.2, 15.6),  # 短期復甦
        ("某公司", -8.5, -3.2),  # 雙重衰退
        ("穩定股", 8.0, 0.7),    # 穩定成長
    ]
    
    for company, yoy, mom in test_cases:
        print(f"\n🏢 {company} (YoY: {yoy:+.1f}%, MoM: {mom:+.1f}%)")
        print("-" * 60)
        
        # 獲取所有評分
        results = scorer.evaluate_company(yoy, mom)
        
        # 顯示主要評分結果
        for method_name, result in results.items():
            if method_name == 'comprehensive_score':
                print(f"🎯 {result['method']}: {result['score']:.1f}分 - {result['grade']}")
            elif method_name == 'quadrant_analysis':
                print(f"{result['color']} {result['method']}: {result['description']} ({result['score']:.1f}分)")
            else:
                print(f"📈 {result['method']}: {result['score']:.1f}分 - {result['grade']}")

if __name__ == "__main__":
    demo_scoring_system()
