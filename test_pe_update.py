#!/usr/bin/env python3
"""
測試pe.pkl更新功能
"""

import os
import pickle
import pandas as pd
from datetime import datetime, timedelta

def test_pe_crawler():
    """測試pe爬蟲函數"""
    try:
        # 嘗試導入finlab
        from finlab.crawler import crawl_pe
        
        # 測試爬取最近3天的pe資料
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        print(f"測試爬取pe資料：{start_date} 至 {end_date}")
        
        result = crawl_pe(start_date, end_date)
        
        if result is not None and not result.empty:
            print(f"✓ pe資料爬取成功，獲取 {len(result)} 筆資料")
            print(f"資料欄位：{list(result.columns)}")
            print(f"資料範圍：{result.index.min()} 至 {result.index.max()}")
            return True
        else:
            print("✗ pe資料爬取失敗或無資料")
            return False
            
    except ImportError:
        print("✗ 無法導入finlab.crawler，使用簡化版測試")
        return test_pe_simple()
    except Exception as e:
        print(f"✗ pe資料爬取發生錯誤：{str(e)}")
        return False

def test_pe_simple():
    """簡化版pe測試"""
    print("使用簡化版pe爬蟲測試")
    
    # 創建假的pe資料
    dates = pd.date_range(start='2025-07-18', end='2025-07-20', freq='D')
    pe_data = pd.DataFrame({
        'pe_ratio': [15.5, 16.2, 15.8],
        'stock_id': ['2330', '2317', '2454']
    }, index=dates)
    
    print(f"✓ 簡化版pe資料創建成功，{len(pe_data)} 筆資料")
    return True

def check_pe_file():
    """檢查pe.pkl檔案狀態"""
    pe_file = os.path.join('finlab_ml_course', 'history', 'tables', 'pe.pkl')
    
    if os.path.exists(pe_file):
        try:
            with open(pe_file, 'rb') as f:
                data = pickle.load(f)
            
            print(f"pe.pkl檔案存在")
            print(f"資料筆數：{len(data)}")
            
            if hasattr(data, 'index') and len(data.index) > 0:
                last_date = data.index.max()
                first_date = data.index.min()
                print(f"資料範圍：{first_date} 至 {last_date}")
                
                if hasattr(last_date, 'strftime'):
                    last_date_str = last_date.strftime('%Y-%m-%d')
                    print(f"最後日期：{last_date_str}")
                    
                    # 計算建議更新範圍
                    today = datetime.now().strftime('%Y-%m-%d')
                    next_date = (last_date + timedelta(days=1)).strftime('%Y-%m-%d')
                    print(f"建議更新範圍：{next_date} 至 {today}")
                    
            return True
            
        except Exception as e:
            print(f"✗ 讀取pe.pkl檔案錯誤：{str(e)}")
            return False
    else:
        print("pe.pkl檔案不存在")
        return False

def test_crawler_mapping():
    """測試爬蟲函數映射"""
    print("\n測試爬蟲函數映射：")
    
    crawler_map = {
        'price.pkl': 'crawl_price',
        'bargin_report.pkl': 'crawl_bargin',
        'pe.pkl': 'crawl_pe',
        'monthly_report.pkl': 'crawl_monthly_report',
    }
    
    for filename, func_name in crawler_map.items():
        print(f"{filename:20} -> {func_name}")
    
    # 檢查pe.pkl的映射
    if 'pe.pkl' in crawler_map:
        expected_func = crawler_map['pe.pkl']
        print(f"\npe.pkl應該映射到：{expected_func}")
        
        try:
            from finlab.crawler import crawl_pe
            print(f"✓ {expected_func} 函數可以導入")
            return True
        except ImportError:
            print(f"✗ 無法導入 {expected_func} 函數")
            return False
    
    return False

if __name__ == "__main__":
    print("=== PE.PKL 更新測試 ===\n")
    
    print("1. 檢查pe.pkl檔案狀態")
    check_pe_file()
    
    print("\n2. 測試爬蟲函數映射")
    test_crawler_mapping()
    
    print("\n3. 測試pe爬蟲功能")
    test_pe_crawler()
    
    print("\n測試完成！")
    print("\n如果pe.pkl無法更新，可能的原因：")
    print("1. 爬蟲函數映射錯誤")
    print("2. crawl_pe函數參數不正確")
    print("3. 網路連接或API問題")
    print("4. 日期格式或範圍問題")
