#!/usr/bin/env python3
"""
智能分析演示腳本
展示系統的智能分析功能
"""

import pandas as pd
import numpy as np
from smart_trading_strategies import StrategyManager, TradingSignal

def create_sample_data():
    """創建示例股票數據"""
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    
    # 模擬股價數據
    base_price = 100
    prices = []
    volume = []
    
    for i in range(100):
        # 模擬價格波動
        change = np.random.normal(0, 0.02)  # 2%波動
        if i > 50:  # 後半段模擬上漲趨勢
            change += 0.005
        
        base_price *= (1 + change)
        prices.append(base_price)
        volume.append(np.random.randint(1000000, 5000000))
    
    # 創建OHLC數據
    df = pd.DataFrame({
        'date': dates,
        'Open': [p * 0.995 for p in prices],
        'High': [p * 1.01 for p in prices],
        'Low': [p * 0.99 for p in prices],
        'Close': prices,
        'Volume': volume
    })
    
    return df

def demo_smart_analysis():
    """演示智能分析功能"""
    print("🚀 台股智能分析系統演示")
    print("=" * 50)
    
    # 創建策略管理器
    strategy_manager = StrategyManager()
    
    # 創建示例數據
    print("📊 正在生成示例股票數據...")
    df = create_sample_data()
    
    print(f"✅ 數據生成完成：{len(df)} 天的交易數據")
    print(f"📈 價格區間：{df['Close'].min():.2f} - {df['Close'].max():.2f}")
    print(f"💰 最新價格：{df['Close'].iloc[-1]:.2f}")
    print()
    
    # 執行智能分析
    print("🎯 正在執行智能策略分析...")
    best_strategy, best_signal = strategy_manager.get_best_strategy_recommendation(df)
    
    print(f"✅ 分析完成！")
    print()
    
    # 顯示分析結果
    print("📋 智能分析結果：")
    print("-" * 30)
    print(f"🎯 最佳策略：{best_strategy}")
    print(f"📈 交易信號：{best_signal.signal_type}")
    print(f"💪 信號強度：{best_signal.strength:.1f}/100")
    print(f"⏰ 進場時機：{best_signal.entry_timing}")
    print(f"💭 分析原因：{best_signal.reasoning}")
    print(f"🎁 目標價位：{best_signal.price_target:.2f}")
    print(f"🛡️ 建議停損：{best_signal.stop_loss:.2f}")
    print(f"💎 獲利目標：{best_signal.take_profit:.2f}")
    print(f"⚡ 風險等級：{best_signal.risk_level}")
    print()
    
    # 測試所有策略
    print("🔍 詳細策略分析：")
    print("-" * 30)
    
    for strategy_name, strategy in strategy_manager.strategies.items():
        signal = strategy.analyze(df)
        print(f"📊 {strategy_name}:")
        print(f"   信號：{signal.signal_type} (強度:{signal.strength:.1f})")
        print(f"   原因：{signal.reasoning}")
        print()
    
    # 顯示策略指南
    print("📚 策略使用指南：")
    print("-" * 30)
    guide = strategy_manager.get_strategy_guide()
    print(guide)
    
    print("🎉 演示完成！")
    print("💡 您的實際系統具備完全相同的智能分析功能！")

if __name__ == "__main__":
    demo_smart_analysis() 