#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理測試資料
"""

import sqlite3

def cleanup_test_data():
    """清理錯誤的測試資料"""
    
    print("=" * 60)
    print("🗑️ 清理錯誤的測試資料")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    print("🗑️ 刪除錯誤的測試資料...")
    
    # 刪除 2022-08-31 之後的資料
    cursor.execute('DELETE FROM stock_daily_data WHERE date > "2022-08-30"')
    deleted_count = cursor.rowcount
    
    conn.commit()
    conn.close()
    
    print(f"✅ 已刪除 {deleted_count} 筆錯誤資料")
    
    # 驗證刪除結果
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    cursor.execute('SELECT MAX(date) FROM stock_daily_data')
    last_date = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
    total_count = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"📊 清理後狀態:")
    print(f"  最後日期: {last_date}")
    print(f"  總資料筆數: {total_count:,}")
    
    return True

if __name__ == "__main__":
    cleanup_test_data()
