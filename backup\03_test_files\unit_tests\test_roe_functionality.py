#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試年度ROE資料抓取功能
"""

import sys
import os
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_roe_crawler():
    """測試ROE爬蟲核心功能"""
    print("\n" + "="*50)
    print("🧪 測試ROE爬蟲核心功能")
    print("="*50)
    
    try:
        from roe_data_crawler import ROEDataCrawler
        
        # 創建爬蟲實例
        crawler = ROEDataCrawler()
        print("✅ ROE爬蟲實例創建成功")
        
        # 測試抓取2023年資料
        year = 2023
        roe_data = crawler.fetch_roe_data(year)
        
        if roe_data:
            print(f"✅ 成功抓取 {len(roe_data)} 筆 {year} 年度ROE資料")
            
            # 顯示前5筆資料
            print("\n📊 前5筆ROE資料:")
            for i, record in enumerate(roe_data[:5], 1):
                print(f"  {i}. {record['stock_code']} {record['stock_name']} - ROE: {record['roe_value']}%")
            
            # 測試儲存到資料庫
            success = crawler.save_to_database(roe_data, overwrite=True)
            if success:
                print("✅ 資料庫儲存成功")
            else:
                print("❌ 資料庫儲存失敗")
            
            # 測試CSV匯出
            csv_file = crawler.export_to_csv(year)
            if csv_file and os.path.exists(csv_file):
                print(f"✅ CSV匯出成功: {os.path.basename(csv_file)}")
            else:
                print("❌ CSV匯出失敗")
                
            return True
        else:
            print("❌ 未能抓取到ROE資料")
            return False
            
    except Exception as e:
        print(f"❌ ROE爬蟲測試失敗: {e}")
        return False

def test_roe_downloader_import():
    """測試ROE下載器模組導入"""
    print("\n" + "="*50)
    print("🧪 測試ROE下載器模組導入")
    print("="*50)
    
    try:
        from roe_data_downloader import ROEDataDownloader
        print("✅ ROE下載器模組導入成功")
        
        # 測試創建實例（不顯示GUI）
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        downloader = ROEDataDownloader()
        print("✅ ROE下載器實例創建成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ ROE下載器測試失敗: {e}")
        return False

def test_main_program_integration():
    """測試主程式整合"""
    print("\n" + "="*50)
    print("🧪 測試主程式整合")
    print("="*50)
    
    try:
        # 檢查主程式檔案是否存在
        main_file = "O3mh_gui_v21_optimized.py"
        if not os.path.exists(main_file):
            print(f"❌ 主程式檔案不存在: {main_file}")
            return False
        
        # 檢查是否包含ROE相關代碼
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查選單項目
        if '年度ROE資料' in content:
            print("✅ 選單項目已添加")
        else:
            print("❌ 選單項目未找到")
            return False
        
        # 檢查處理方法
        if 'open_roe_data_downloader' in content:
            print("✅ 處理方法已添加")
        else:
            print("❌ 處理方法未找到")
            return False
        
        print("✅ 主程式整合檢查通過")
        return True
        
    except Exception as e:
        print(f"❌ 主程式整合測試失敗: {e}")
        return False

def test_database_and_files():
    """測試資料庫和檔案生成"""
    print("\n" + "="*50)
    print("🧪 測試資料庫和檔案生成")
    print("="*50)
    
    try:
        # 檢查資料庫目錄
        db_dir = "D:/Finlab/history/tables"
        if not os.path.exists(db_dir):
            print(f"⚠️ 資料庫目錄不存在，創建中: {db_dir}")
            os.makedirs(db_dir, exist_ok=True)
        
        # 檢查ROE資料庫檔案
        db_file = os.path.join(db_dir, "roe_data.db")
        if os.path.exists(db_file):
            print(f"✅ ROE資料庫檔案存在: {os.path.basename(db_file)}")
            print(f"   檔案大小: {os.path.getsize(db_file)} bytes")
        else:
            print("⚠️ ROE資料庫檔案不存在")
        
        # 檢查CSV檔案
        csv_files = [f for f in os.listdir(db_dir) if f.startswith('roe_data_') and f.endswith('.csv')]
        if csv_files:
            print(f"✅ 找到 {len(csv_files)} 個ROE CSV檔案:")
            for csv_file in sorted(csv_files)[-3:]:  # 顯示最新的3個檔案
                file_path = os.path.join(db_dir, csv_file)
                file_size = os.path.getsize(file_path)
                print(f"   📄 {csv_file} ({file_size} bytes)")
        else:
            print("⚠️ 未找到ROE CSV檔案")
        
        return True
        
    except Exception as e:
        print(f"❌ 資料庫和檔案測試失敗: {e}")
        return False

def run_comprehensive_test():
    """執行完整測試"""
    print("🚀 開始年度ROE資料抓取功能完整測試")
    print("測試時間:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("ROE爬蟲核心", test_roe_crawler()))
    test_results.append(("ROE下載器導入", test_roe_downloader_import()))
    test_results.append(("主程式整合", test_main_program_integration()))
    test_results.append(("資料庫和檔案", test_database_and_files()))
    
    # 顯示測試結果摘要
    print("\n" + "="*50)
    print("📋 測試結果摘要")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 測試統計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！年度ROE資料抓取功能運作正常")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("\n💡 使用說明:")
        print("1. 開啟主程式: python O3mh_gui_v21_optimized.py")
        print("2. 點擊選單「🕷️ 爬蟲」→「📊 年度ROE資料」")
        print("3. 選擇年份並點擊「🚀 開始下載」")
        print("4. 等待下載完成")
    else:
        print("\n🔧 請檢查以下項目:")
        print("1. 確認所有相關檔案存在")
        print("2. 檢查網路連接")
        print("3. 確認資料庫目錄權限")
    
    sys.exit(0 if success else 1)
