#!/usr/bin/env python3
"""
PreMarketMonitor 提取測試腳本
測試 PreMarketMonitor 提取是否成功
"""
import sys
import os

def test_premarket_module():
    """測試 PreMarketMonitor 模組"""
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        print("✅ PreMarketMonitor 模組導入成功")

        # 測試實例化
        monitor = PreMarketMonitor()
        print(f"✅ PreMarketMonitor 實例化成功: {monitor.name}")

        # 測試基本屬性
        if hasattr(monitor, 'start_monitoring'):
            print("✅ start_monitoring 方法存在")

        if hasattr(monitor, 'get_market_summary'):
            print("✅ get_market_summary 方法存在")

        return True
    except Exception as e:
        print(f"❌ PreMarketMonitor 模組測試失敗: {e}")
        return False

def test_main_program():
    """測試主程式是否正常"""
    try:
        import O3mh_gui_v21_optimized
        print("✅ 主程式模組導入成功")

        # 測試 PreMarketMonitor 類別是否可用
        from O3mh_gui_v21_optimized import PreMarketMonitor
        monitor = PreMarketMonitor()
        print(f"✅ 主程式中的 PreMarketMonitor 正常: {monitor.name}")

        return True
    except Exception as e:
        print(f"❌ 主程式測試失敗: {e}")
        return False

def check_file_size_reduction():
    """檢查文件大小減少"""
    try:
        import os

        current_size = os.path.getsize('O3mh_gui_v21_optimized.py')
        backup_size = os.path.getsize('O3mh_gui_v21_optimized_BEFORE_PREMARKET_EXTRACTION.py')

        reduction = backup_size - current_size
        percentage = (reduction / backup_size) * 100

        print(f"📊 文件大小變化:")
        print(f"   原始: {backup_size:,} 字節")
        print(f"   現在: {current_size:,} 字節")
        print(f"   減少: {reduction:,} 字節 ({percentage:.1f}%)")

        return reduction > 0
    except Exception as e:
        print(f"❌ 文件大小檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始 PreMarketMonitor 提取測試...")
    print("=" * 50)

    tests = [
        ("PreMarketMonitor 模組測試", test_premarket_module),
        ("主程式兼容性測試", test_main_program),
        ("文件大小減少檢查", check_file_size_reduction)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 執行 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")

    if passed == total:
        print("🎉 PreMarketMonitor 提取成功！")
        print("✅ PreMarketMonitor 已成功模組化")
        print("✅ 主程式功能保持完整")
        print("✅ 文件大小已減少")
        return True
    else:
        print("❌ 部分測試失敗，需要檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)