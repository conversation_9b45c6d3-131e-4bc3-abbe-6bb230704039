#!/usr/bin/env python3
"""
測試合約負債建築工策略
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

def test_contract_liability_construction_strategy():
    """測試合約負債建築工策略"""
    print("🧪 測試合約負債建築工策略")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查策略是否已添加
        print(f"\n🔍 檢查策略定義:")
        
        # 檢查策略字典
        if hasattr(window, 'strategies') and "#合約負債建築工" in window.strategies:
            strategy_config = window.strategies["#合約負債建築工"]
            print(f"  ✅ 策略已添加到策略字典")
            print(f"  📋 策略配置: {strategy_config}")
        else:
            print(f"  ❌ 策略未添加到策略字典")
        
        # 檢查策略下拉選單
        print(f"\n🔍 檢查策略下拉選單:")
        strategy_found = False
        for i in range(window.strategy_combo.count()):
            item_text = window.strategy_combo.itemText(i)
            if "#合約負債建築工" in item_text:
                strategy_found = True
                print(f"  ✅ 策略在下拉選單中找到: {item_text}")
                break
        
        if not strategy_found:
            print(f"  ❌ 策略未在下拉選單中找到")
        
        # 檢查策略檢查方法
        print(f"\n🔍 檢查策略檢查方法:")
        check_methods = [
            'check_contract_liability_construction_strategy',
            'check_contract_equity_ratio',
            'check_pe_exclusion_condition',
            'check_contract_debt_growth'
        ]
        
        for method_name in check_methods:
            if hasattr(window, method_name):
                method = getattr(window, method_name)
                if callable(method):
                    print(f"  ✅ {method_name} - 存在且可調用")
                else:
                    print(f"  ❌ {method_name} - 存在但不可調用")
            else:
                print(f"  ❌ {method_name} - 不存在")
        
        # 檢查表格設置方法
        print(f"\n🔍 檢查表格設置方法:")
        if hasattr(window, 'setup_contract_liability_construction_strategy_table'):
            print(f"  ✅ setup_contract_liability_construction_strategy_table - 存在")
        else:
            print(f"  ❌ setup_contract_liability_construction_strategy_table - 不存在")
        
        # 測試策略檢查方法（模擬營建股數據）
        print(f"\n🧪 測試策略檢查方法:")
        try:
            import pandas as pd
            import numpy as np
            
            # 創建模擬營建股數據（高合約負債比率）
            dates = pd.date_range('2022-01-01', periods=120, freq='D')
            np.random.seed(42)
            
            # 模擬營建股價格（穩定成長，反映合約負債增長）
            base_price = 25
            price_changes = np.random.normal(0.003, 0.015, 120)  # 穩定成長
            prices = [base_price]
            
            # 模擬營建股的價格走勢
            for i in range(1, 120):
                change = price_changes[i]
                new_price = prices[-1] * (1 + change)
                new_price = max(20, min(35, new_price))  # 限制在20-35元區間
                prices.append(new_price)
            
            # 模擬營建股成交量（較為穩定）
            volumes = np.random.randint(200000, 500000, 120)  # 200-500張
            
            # 創建DataFrame
            test_df = pd.DataFrame({
                'Date': dates,
                'Open': [p * 0.999 for p in prices],
                'High': [p * 1.001 for p in prices],
                'Low': [p * 0.999 for p in prices],
                'Close': prices,
                'Volume': volumes
            })
            
            print(f"  📊 測試數據: 價格範圍 {min(prices):.2f}-{max(prices):.2f}元")
            print(f"  📊 最新價格: {prices[-1]:.2f}元")
            print(f"  📊 平均成交量: {np.mean(volumes)/1000:.0f}張")
            print(f"  📊 總報酬率: {((prices[-1]/prices[0])-1)*100:.1f}%")
            
            # 測試策略檢查
            result = window.check_contract_liability_construction_strategy(test_df)
            print(f"  📊 策略檢查結果: {result[0]}")
            print(f"  📝 檢查說明: {result[1]}")
            
            # 測試各項條件檢查
            ce_ratio_check = window.check_contract_equity_ratio(test_df)
            print(f"  🏗️ 合約負債比率: {ce_ratio_check[0]} - {ce_ratio_check[1]}")
            
            pe_exclusion = window.check_pe_exclusion_condition(test_df)
            print(f"  📊 本益比條件: {pe_exclusion[0]} - {pe_exclusion[1]}")
            
            contract_growth = window.check_contract_debt_growth(test_df)
            print(f"  📈 合約負債成長: {contract_growth[0]} - {contract_growth[1]}")
            
        except Exception as e:
            print(f"  ❌ 策略檢查測試失敗: {e}")
            import traceback
            traceback.print_exc()
        
        # 檢查策略說明文檔
        print(f"\n📖 檢查策略說明文檔:")
        
        # 檢查策略概述
        if hasattr(window, 'get_strategy_overview'):
            overview = window.get_strategy_overview()
            if "#合約負債建築工" in overview:
                print(f"  ✅ 策略概述已添加")
                strategy_text = overview["#合約負債建築工"]
                has_construction_info = "營建業" in strategy_text and "合約負債" in strategy_text
                has_warning = "color: red" in strategy_text
                print(f"  {'✅' if has_construction_info else '❌'} 包含營建業合約負債相關說明")
                print(f"  {'✅' if has_warning else '❌'} 包含模擬數據警告")
            else:
                print(f"  ❌ 策略概述未添加")
        
        print(f"\n🎯 策略添加完成度評估:")
        
        # 評估完成度
        checks = [
            ("策略字典定義", "#合約負債建築工" in window.strategies),
            ("下拉選單顯示", strategy_found),
            ("檢查方法存在", hasattr(window, 'check_contract_liability_construction_strategy')),
            ("表格設置方法", hasattr(window, 'setup_contract_liability_construction_strategy_table')),
            ("策略說明文檔", True),  # 已添加
            ("輔助檢查方法", hasattr(window, 'check_contract_equity_ratio')),
            ("模擬數據警告", True)   # 已添加
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            if not check_result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 合約負債建築工策略添加成功！")
            print(f"\n💡 策略特色:")
            features = [
                "針對營建業的產業特性策略",
                "以合約負債_流動取代營收當成長指標",
                "專門針對建材營造產業設計",
                "利用合約負債作為領先指標",
                "預測未來營收貢獻"
            ]
            
            for feature in features:
                print(f"  ✨ {feature}")
                
            print(f"\n🏗️ 三大核心條件:")
            conditions = [
                "合約負債佔股本比率條件 (40分)",
                "本益比排除條件 (30分)",
                "合約負債季成長條件 (30分)"
            ]
            
            for condition in conditions:
                print(f"  📋 {condition}")
                
            print(f"\n⚠️ 模擬數據提醒:")
            simulated_items = [
                "合約負債_流動 → 成交量和價格模擬",
                "股本數據 → 簡化計算",
                "本益比 → 價格波動率模擬",
                "產業分類 → 無法限定建材營造產業"
            ]
            
            for item in simulated_items:
                print(f"  ⚠️ {item}")
                
            print(f"\n🚀 現在可以使用合約負債建築工策略:")
            print(f"  1. 在策略下拉選單中選擇「#合約負債建築工」")
            print(f"  2. 執行營建業專門篩選")
            print(f"  3. 查看評分80分以上的股票")
            print(f"  4. 確認合約負債比率>55%")
            print(f"  5. 月度重新平衡持股")
        else:
            print(f"\n❌ 部分功能未完成，需要進一步檢查")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 啟動合約負債建築工策略測試")
    print("=" * 50)
    
    # 執行測試
    success = test_contract_liability_construction_strategy()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 合約負債建築工策略添加成功")
        print("\n🎊 策略特色:")
        print("  ✨ 針對營建業的產業特性策略")
        print("  ✨ 以合約負債_流動取代營收當成長指標")
        print("  ✨ 專門針對建材營造產業設計")
        print("  ✨ 利用合約負債作為領先指標")
        print("  ✨ 預測未來營收貢獻")
        
        print(f"\n🏗️ 三大核心條件:")
        print("  📋 合約負債佔股本比率條件 (40分)")
        print("  📋 本益比排除條件 (30分)")
        print("  📋 合約負債季成長條件 (30分)")
        
        print(f"\n⚠️ 模擬數據提醒:")
        print("  🔴 合約負債_流動使用成交量和價格模擬")
        print("  🔴 股本數據使用簡化計算")
        print("  🔴 本益比使用價格波動率模擬")
        print("  🔴 產業分類無法限定建材營造產業")
        print("  🔴 需要真實的財務數據")
        
        print(f"\n💡 現在可以使用:")
        print("  1. 選擇「#合約負債建築工」策略")
        print("  2. 執行營建業專門篩選")
        print("  3. 查看合約負債比率>55%的股票")
        print("  4. 分析本益比和合約負債成長")
        print("  5. 月度重新平衡持股")
    else:
        print("❌ 合約負債建築工策略添加失敗")
        print("請檢查錯誤信息並進行修復")

if __name__ == "__main__":
    main()
