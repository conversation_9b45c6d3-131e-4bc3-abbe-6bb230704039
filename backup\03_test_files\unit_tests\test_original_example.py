#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試您提供的原始範例程式碼
確認是否能正確獲取台積電即時股價
"""

import requests
from bs4 import BeautifulSoup

def test_original_example():
    """測試您提供的原始範例程式碼"""
    print("🧪 測試您提供的原始範例程式碼")
    print("=" * 50)
    
    try:
        # 您提供的原始程式碼
        url = 'https://tw.stock.yahoo.com/quote/2330'    # 台積電 Yahoo 股市網址
        web = requests.get(url)                          # 取得網頁內容
        soup = BeautifulSoup(web.text, "html.parser")    # 轉換內容
        
        print(f"📡 請求URL: {url}")
        print(f"✅ 請求成功，狀態碼: {web.status_code}")
        print(f"📄 頁面內容長度: {len(web.text)}")
        
        # 檢查頁面是否包含台積電相關資訊
        if '台積電' in web.text or '2330' in web.text:
            print("✅ 頁面包含台積電相關資訊")
        else:
            print("❌ 頁面不包含台積電相關資訊")
        
        title = soup.find('h1')             # 找到 h1 的內容
        print(f"📊 H1標題: {title.get_text() if title else '未找到'}")
        
        # 嘗試原始的CSS選擇器
        try:
            a = soup.select('.Fz(32px)')[0]     # 找到第一個 class 為 Fz(32px) 的內容
            print(f"💰 價格元素 (.Fz(32px)): {a.get_text()}")
        except Exception as e:
            print(f"❌ 無法找到價格元素 (.Fz(32px)): {e}")
            
            # 嘗試轉義版本
            try:
                a = soup.select('.Fz\\(32px\\)')[0]
                print(f"💰 價格元素 (.Fz\\(32px\\)): {a.get_text()}")
            except Exception as e2:
                print(f"❌ 轉義版本也失敗: {e2}")
                a = None
        
        try:
            b = soup.select('.Fz(20px)')[0]     # 找到第一個 class 為 Fz(20px) 的內容
            print(f"📈 漲跌元素 (.Fz(20px)): {b.get_text()}")
        except Exception as e:
            print(f"❌ 無法找到漲跌元素 (.Fz(20px)): {e}")
            
            # 嘗試轉義版本
            try:
                b = soup.select('.Fz\\(20px\\)')[0]
                print(f"📈 漲跌元素 (.Fz\\(20px\\)): {b.get_text()}")
            except Exception as e2:
                print(f"❌ 轉義版本也失敗: {e2}")
                b = None
        
        s = ''                              # 漲或跌的狀態
        try:
            # 如果 main-0-QuoteHeader-Proxy id 的 div 裡有 C($c-trend-down) 的 class
            # 表示狀態為下跌
            if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C($c-trend-down)')[0]:
                s = '-'
                print("📉 趨勢: 下跌")
        except:
            try:
                # 如果 main-0-QuoteHeader-Proxy id 的 div 裡有 C($c-trend-up) 的 class
                # 表示狀態為上漲
                if soup.select('#main-0-QuoteHeader-Proxy')[0].select('.C($c-trend-up)')[0]:
                    s = '+'
                    print("📈 趨勢: 上漲")
            except:
                # 如果都沒有包含，表示平盤
                s = '-'
                print("➡️ 趨勢: 平盤")
        
        # 嘗試輸出結果
        if title and a and b:
            result = f'{title.get_text()} : {a.get_text()} ( {s}{b.get_text()} )'
            print(f"\n🎉 成功結果: {result}")
            return True
        else:
            print(f"\n❌ 無法獲取完整資訊")
            print(f"  標題: {'✅' if title else '❌'}")
            print(f"  價格: {'✅' if a else '❌'}")
            print(f"  漲跌: {'✅' if b else '❌'}")
            return False
            
    except Exception as e:
        print(f"❌ 程式執行失敗: {e}")
        return False

def debug_page_content():
    """調試頁面內容"""
    print("\n🔍 調試頁面內容")
    print("=" * 50)
    
    try:
        url = 'https://tw.stock.yahoo.com/quote/2330'
        web = requests.get(url)
        soup = BeautifulSoup(web.text, "html.parser")
        
        # 檢查所有可能的價格相關元素
        print("🔍 搜尋所有包含數字的元素:")
        
        # 尋找所有包含數字的span元素
        spans = soup.find_all('span')
        price_candidates = []
        
        for span in spans:
            text = span.get_text().strip()
            if text and any(c.isdigit() for c in text):
                # 檢查是否可能是股價（包含小數點，範圍合理）
                try:
                    # 移除逗號後檢查
                    clean_text = text.replace(',', '').replace('$', '')
                    if '.' in clean_text:
                        value = float(clean_text)
                        if 100 <= value <= 2000:  # 台積電股價合理範圍
                            price_candidates.append((text, span.get('class', [])))
                except:
                    pass
        
        if price_candidates:
            print("💰 可能的股價候選:")
            for text, classes in price_candidates[:10]:  # 只顯示前10個
                print(f"  {text} (class: {classes})")
        else:
            print("❌ 未找到可能的股價元素")
        
        # 檢查所有class包含Fz的元素
        print(f"\n🎨 檢查所有包含'Fz'的class:")
        all_elements = soup.find_all(True)  # 找到所有元素
        fz_classes = set()
        
        for elem in all_elements:
            classes = elem.get('class', [])
            for cls in classes:
                if 'Fz' in cls:
                    fz_classes.add(cls)
        
        if fz_classes:
            print("找到的Fz相關class:")
            for cls in sorted(fz_classes):
                print(f"  .{cls}")
        else:
            print("❌ 未找到Fz相關class")
        
        # 檢查是否有JavaScript渲染的跡象
        print(f"\n🔧 檢查JavaScript:")
        scripts = soup.find_all('script')
        js_content = ""
        for script in scripts:
            if script.string:
                js_content += script.string
        
        if '2330' in js_content:
            print("✅ JavaScript中包含股票代碼")
        else:
            print("❌ JavaScript中不包含股票代碼")
        
        if 'price' in js_content.lower() or 'quote' in js_content.lower():
            print("✅ JavaScript中包含價格相關關鍵字")
        else:
            print("❌ JavaScript中不包含價格相關關鍵字")
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        return False

def test_with_selenium():
    """測試使用Selenium是否能獲取動態內容"""
    print("\n🚀 測試Selenium動態內容獲取")
    print("=" * 50)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # 設置Chrome選項
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 無頭模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            url = 'https://tw.stock.yahoo.com/quote/2330'
            print(f"📡 使用Selenium訪問: {url}")
            
            driver.get(url)
            
            # 等待頁面載入
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 獲取頁面源碼
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, "html.parser")
            
            print(f"✅ Selenium頁面載入成功，內容長度: {len(page_source)}")
            
            # 檢查是否包含股票資訊
            if '台積電' in page_source or '2330' in page_source:
                print("✅ Selenium頁面包含台積電資訊")
                
                # 重新嘗試原始選擇器
                try:
                    price_elem = soup.select('.Fz\\(32px\\)')[0]
                    change_elem = soup.select('.Fz\\(20px\\)')[0]
                    
                    print(f"💰 Selenium獲取價格: {price_elem.get_text()}")
                    print(f"📈 Selenium獲取漲跌: {change_elem.get_text()}")
                    
                    return True
                    
                except Exception as e:
                    print(f"❌ Selenium仍無法解析元素: {e}")
                    return False
            else:
                print("❌ Selenium頁面仍不包含台積電資訊")
                return False
                
        finally:
            driver.quit()
            
    except ImportError:
        print("⚠️ 未安裝Selenium，跳過此測試")
        return None
    except Exception as e:
        print(f"❌ Selenium測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 Yahoo股價爬取驗證測試")
    print("基於您提供的範例程式碼")
    print("=" * 60)
    
    # 測試1: 原始範例程式碼
    result1 = test_original_example()
    
    # 測試2: 調試頁面內容
    result2 = debug_page_content()
    
    # 測試3: Selenium測試
    result3 = test_with_selenium()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 30)
    
    print(f"原始範例程式碼: {'✅ 成功' if result1 else '❌ 失敗'}")
    print(f"頁面內容調試: {'✅ 完成' if result2 else '❌ 失敗'}")
    
    if result3 is None:
        print(f"Selenium測試: ⚠️ 跳過（未安裝）")
    else:
        print(f"Selenium測試: {'✅ 成功' if result3 else '❌ 失敗'}")
    
    if result1:
        print("\n🎉 您的範例程式碼可以正常工作！")
    elif result3:
        print("\n💡 需要使用Selenium來獲取動態內容")
    else:
        print("\n⚠️ Yahoo頁面結構可能已變更，需要進一步分析")

if __name__ == "__main__":
    main()
