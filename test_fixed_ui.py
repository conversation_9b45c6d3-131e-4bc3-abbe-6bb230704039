#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的界面
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    """測試修復後界面的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 界面修復測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_text = QTextEdit()
        title_text.setMaximumHeight(200)
        title_text.setHtml("""
        <h2>🔧 界面修復測試</h2>
        <p><b>修復內容：</b></p>
        <ul>
            <li>✅ <b>錯誤修復</b> - 修復 'name pd is not defined' 錯誤</li>
            <li>✅ <b>界面重設計</b> - 將分頁移到上方，增加圖表區域高度</li>
            <li>✅ <b>布局優化</b> - 重新組織界面布局，提升用戶體驗</li>
            <li>✅ <b>代碼清理</b> - 移除重複代碼，優化程式結構</li>
        </ul>
        <p><b>新界面特色：</b></p>
        <ul>
            <li>🚀 <b>數據爬取分頁</b> - 集中所有爬取控制功能</li>
            <li>📊 <b>數據查看分頁</b> - 專門的數據瀏覽和導出功能</li>
            <li>📈 <b>圖表分析分頁</b> - 大尺寸圖表顯示區域</li>
        </ul>
        """)
        layout.addWidget(title_text)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟台灣證交所爬蟲 (測試修復)")
        test_btn.clicked.connect(self.test_crawler)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🔧 界面修復測試程式已啟動")
        self.log("✅ 主要修復內容：")
        self.log("1. 修復 pandas 導入錯誤")
        self.log("2. 重新設計界面布局，分頁移到上方")
        self.log("3. 增加圖表顯示區域高度")
        self.log("4. 清理重複代碼")
        self.log("5. 優化用戶體驗")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_crawler(self):
        """測試爬蟲界面"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟台灣證交所市場數據爬蟲...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("🎯 測試重點：")
            self.log("1. 檢查是否有錯誤訊息")
            self.log("2. 確認分頁是否在上方")
            self.log("3. 驗證圖表區域是否足夠大")
            self.log("4. 測試各項功能是否正常")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("🔧 界面修復測試程式已啟動")
    print("✅ 主要修復：")
    print("1. 修復 pandas 導入錯誤")
    print("2. 重新設計界面布局")
    print("3. 增加圖表區域高度")
    print("4. 優化用戶體驗")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
