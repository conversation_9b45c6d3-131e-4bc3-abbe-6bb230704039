#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
統一監控股票管理系統
確保所有監控功能使用同一個股票清單
"""

import json
import logging
import os
from datetime import datetime
from typing import List, Optional
from PyQt6.QtCore import QObject, pyqtSignal

class UnifiedMonitorManager(QObject):
    """統一監控股票管理器"""
    
    # 信號：當監控股票清單更新時發出
    stocks_updated = pyqtSignal(list)  # 發送新的股票清單
    
    def __init__(self, config_file="app_config.json"):
        super().__init__()
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self._stocks = []
        self.load_stocks()
    
    def load_stocks(self) -> List[str]:
        """從配置檔案載入監控股票清單"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                stocks_str = config.get('monitor', {}).get('stocks', '')
                if stocks_str:
                    self._stocks = [s.strip() for s in stocks_str.split(',') if s.strip()]
                else:
                    # 預設監控股票
                    self._stocks = ['2330', '2317', '2454', '2412', '2881']
                
                self.logger.info(f"✅ 載入監控股票: {len(self._stocks)} 支")
                return self._stocks
            else:
                # 使用預設股票清單
                self._stocks = ['2330', '2317', '2454', '2412', '2881']
                self.save_stocks()
                return self._stocks
                
        except Exception as e:
            self.logger.error(f"❌ 載入監控股票失敗: {e}")
            self._stocks = ['2330', '2317', '2454', '2412', '2881']
            return self._stocks
    
    def save_stocks(self) -> bool:
        """保存監控股票清單到配置檔案"""
        try:
            # 讀取現有配置
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 更新監控配置
            if 'monitor' not in config:
                config['monitor'] = {}
            
            config['monitor']['stocks'] = ','.join(self._stocks)
            config['monitor']['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 監控股票已保存: {len(self._stocks)} 支")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存監控股票失敗: {e}")
            return False
    
    def get_stocks(self) -> List[str]:
        """獲取當前監控股票清單"""
        return self._stocks.copy()
    
    def set_stocks(self, stocks: List[str]) -> bool:
        """設定監控股票清單"""
        try:
            # 清理和驗證股票代碼
            cleaned_stocks = []
            for stock in stocks:
                stock = str(stock).strip()
                if stock and stock.isdigit() and len(stock) == 4:
                    cleaned_stocks.append(stock)
            
            # 去重並保持順序
            unique_stocks = []
            for stock in cleaned_stocks:
                if stock not in unique_stocks:
                    unique_stocks.append(stock)
            
            self._stocks = unique_stocks
            
            # 保存到配置檔案
            if self.save_stocks():
                # 發出更新信號
                self.stocks_updated.emit(self._stocks.copy())
                self.logger.info(f"✅ 監控股票已更新: {self._stocks}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 設定監控股票失敗: {e}")
            return False
    
    def add_stock(self, stock_code: str) -> bool:
        """添加單一股票到監控清單"""
        try:
            stock_code = str(stock_code).strip()
            if not stock_code or not stock_code.isdigit() or len(stock_code) != 4:
                self.logger.warning(f"⚠️ 無效的股票代碼: {stock_code}")
                return False
            
            if stock_code not in self._stocks:
                self._stocks.append(stock_code)
                if self.save_stocks():
                    self.stocks_updated.emit(self._stocks.copy())
                    self.logger.info(f"✅ 添加股票到監控: {stock_code}")
                    return True
            else:
                self.logger.info(f"ℹ️ 股票已在監控清單中: {stock_code}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 添加股票失敗: {e}")
            return False
    
    def remove_stock(self, stock_code: str) -> bool:
        """從監控清單移除股票"""
        try:
            stock_code = str(stock_code).strip()
            if stock_code in self._stocks:
                self._stocks.remove(stock_code)
                if self.save_stocks():
                    self.stocks_updated.emit(self._stocks.copy())
                    self.logger.info(f"✅ 從監控移除股票: {stock_code}")
                    return True
            else:
                self.logger.info(f"ℹ️ 股票不在監控清單中: {stock_code}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 移除股票失敗: {e}")
            return False
    
    def add_stocks(self, stock_codes: List[str]) -> bool:
        """批量添加股票到監控清單"""
        try:
            current_stocks = self._stocks.copy()
            added_count = 0
            
            for stock_code in stock_codes:
                stock_code = str(stock_code).strip()
                if stock_code and stock_code.isdigit() and len(stock_code) == 4:
                    if stock_code not in current_stocks:
                        current_stocks.append(stock_code)
                        added_count += 1
            
            if added_count > 0:
                self._stocks = current_stocks
                if self.save_stocks():
                    self.stocks_updated.emit(self._stocks.copy())
                    self.logger.info(f"✅ 批量添加 {added_count} 支股票到監控")
                    return True
            else:
                self.logger.info("ℹ️ 沒有新股票需要添加")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 批量添加股票失敗: {e}")
            return False
    
    def clear_stocks(self) -> bool:
        """清空監控股票清單"""
        try:
            self._stocks = []
            if self.save_stocks():
                self.stocks_updated.emit(self._stocks.copy())
                self.logger.info("✅ 監控股票清單已清空")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 清空監控股票失敗: {e}")
            return False
    
    def get_stocks_string(self) -> str:
        """獲取股票清單的字串格式（逗號分隔）"""
        return ','.join(self._stocks)
    
    def set_stocks_from_string(self, stocks_str: str) -> bool:
        """從字串設定股票清單（逗號分隔）"""
        try:
            if not stocks_str:
                return self.clear_stocks()
            
            stock_list = [s.strip() for s in stocks_str.split(',') if s.strip()]
            return self.set_stocks(stock_list)
            
        except Exception as e:
            self.logger.error(f"❌ 從字串設定股票失敗: {e}")
            return False
    
    def get_stock_count(self) -> int:
        """獲取監控股票數量"""
        return len(self._stocks)
    
    def is_monitoring(self, stock_code: str) -> bool:
        """檢查是否正在監控指定股票"""
        return str(stock_code).strip() in self._stocks
    
    def get_stats(self) -> dict:
        """獲取監控統計資訊"""
        return {
            'total_stocks': len(self._stocks),
            'stocks': self._stocks.copy(),
            'stocks_string': self.get_stocks_string(),
            'last_updated': self.get_last_updated()
        }
    
    def get_last_updated(self) -> Optional[str]:
        """獲取最後更新時間"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get('monitor', {}).get('last_updated')
        except:
            pass
        return None

# 全域單例實例
_monitor_manager = None

def get_monitor_manager() -> UnifiedMonitorManager:
    """獲取統一監控管理器的單例實例"""
    global _monitor_manager
    if _monitor_manager is None:
        _monitor_manager = UnifiedMonitorManager()
    return _monitor_manager

def get_monitor_stocks() -> List[str]:
    """快速獲取監控股票清單"""
    return get_monitor_manager().get_stocks()

def set_monitor_stocks(stocks: List[str]) -> bool:
    """快速設定監控股票清單"""
    return get_monitor_manager().set_stocks(stocks)

def add_monitor_stock(stock_code: str) -> bool:
    """快速添加監控股票"""
    return get_monitor_manager().add_stock(stock_code)

def remove_monitor_stock(stock_code: str) -> bool:
    """快速移除監控股票"""
    return get_monitor_manager().remove_stock(stock_code)
