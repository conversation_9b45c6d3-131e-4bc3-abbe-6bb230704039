#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股票名稱查詢功能
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_stock_name_lookup():
    """測試股票名稱查詢功能"""
    
    # 模擬GoogleStockNewsDialog的股票名稱映射
    stock_name_mapping = {
        # 權值股
        '台積電': '2330', 'TSMC': '2330', '台積': '2330',
        '鴻海': '2317', '鴻海精密': '2317', 
        '聯發科': '2454', 'MediaTek': '2454', '聯發': '2454',
        '台達電': '2308', '廣達': '2382', '聯電': '2303',
        '大立光': '3008', '國巨': '2327', '研華': '2395',
        '中華電': '2412', '中華電信': '2412',
        '台塑': '1301', '南亞': '1303', '台泥': '1101',
        '中鋼': '2002', '統一': '1216',

        # 金融股
        '富邦金': '2881', '國泰金': '2882', '兆豐金': '2886',
        '中信金': '2891', '第一金': '2892', '合庫金': '5880',
        '玉山金': '2884', '永豐金': '2890', '華南金': '2880',

        # 科技股
        '華碩': '2357', '友達': '2409', '南亞科': '2408',
        '和碩': '4938', '緯創': '3231', '仁寶': '2324',
        '英業達': '2356', '宏碁': '2353', '微星': '2377',

        # 傳產股
        '和泰車': '2207', '裕隆': '2201', '中華': '2204',
        '台塑化': '6505', '長榮': '2603', '陽明': '2609',
        '萬海': '2615', '台船': '2208',

        # ETF
        '台灣50': '0050', '元大台灣50': '0050',
        '中型100': '0051', '元大中型100': '0051',
        '富邦科技': '0052', '電子': '0053',
        '台商50': '0054', '高股息': '0056',
        '元大高股息': '0056', '永豐ESG': '00930',
        '富邦元宇宙': '00903',
    }
    
    def lookup_stock_code_by_name(input_text):
        """根據輸入文字查詢股票代碼"""
        input_text = input_text.strip()
        
        # 如果輸入的已經是數字代碼，直接返回
        if input_text.isdigit() or (input_text.startswith('00') and input_text[2:].isdigit()):
            return input_text
        
        # 直接匹配
        if input_text in stock_name_mapping:
            return stock_name_mapping[input_text]
        
        # 模糊匹配（包含關係）
        for name, code in stock_name_mapping.items():
            if input_text in name or name in input_text:
                return code
        
        # 如果都找不到，返回原輸入
        return input_text
    
    # 測試案例
    test_cases = [
        # 股票代碼
        ("2330", "2330"),
        ("0050", "0050"),
        ("00930", "00930"),
        
        # 完整股票名稱
        ("台積電", "2330"),
        ("鴻海", "2317"),
        ("聯發科", "2454"),
        ("元大台灣50", "0050"),
        ("永豐ESG", "00930"),
        
        # 簡稱
        ("台積", "2330"),
        ("聯發", "2454"),
        ("中華電", "2412"),
        ("高股息", "0056"),
        
        # 英文名稱
        ("TSMC", "2330"),
        ("MediaTek", "2454"),
        
        # 不存在的名稱
        ("不存在的股票", "不存在的股票"),
        ("9999", "9999"),
    ]
    
    print("🧪 測試股票名稱查詢功能")
    print("=" * 50)
    
    all_passed = True
    for input_text, expected in test_cases:
        result = lookup_stock_code_by_name(input_text)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_text}' → '{result}' (期望: '{expected}')")
        
        if result != expected:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有測試通過！")
    else:
        print("❌ 部分測試失敗")
    
    return all_passed

if __name__ == "__main__":
    test_stock_name_lookup()
