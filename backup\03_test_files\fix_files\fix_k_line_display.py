#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K線圖顯示修復工具
修復K線圖中某些日期沒有顯示的問題
"""

import sys
import os

def fix_candlestick_item():
    """修復CandlestickItem類別的潛在問題"""
    print("🔧 修復CandlestickItem類別")
    print("=" * 50)
    
    candlestick_path = "charts/candlestick.py"
    
    if not os.path.exists(candlestick_path):
        print(f"❌ 找不到檔案: {candlestick_path}")
        return False
    
    try:
        with open(candlestick_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否需要修復
        fixes_needed = []
        
        # 1. 檢查數據驗證
        if "pd.isna" not in content:
            fixes_needed.append("添加NaN值檢查")
        
        # 2. 檢查錯誤處理
        if "try:" not in content or content.count("try:") < 2:
            fixes_needed.append("加強錯誤處理")
        
        # 3. 檢查數據邊界檢查
        if "len(self.data)" not in content:
            fixes_needed.append("添加數據邊界檢查")
        
        if fixes_needed:
            print("⚠️ 發現需要修復的問題:")
            for fix in fixes_needed:
                print(f"  • {fix}")
            
            # 創建修復版本
            create_fixed_candlestick()
            return True
        else:
            print("✅ CandlestickItem類別看起來正常")
            return True
            
    except Exception as e:
        print(f"❌ 檢查CandlestickItem時出錯: {e}")
        return False

def create_fixed_candlestick():
    """創建修復版的CandlestickItem"""
    print("🔧 創建修復版CandlestickItem")
    
    fixed_content = '''#!/usr/bin/env python3
"""
K線圖組件模組 - 修復版
修復了數據驗證和錯誤處理問題
"""
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging

# PyQt6 兼容性修復
try:
    from PyQt6.QtCore import QRectF, QPointF
    from PyQt6.QtGui import QPainter, QPicture, QPen, QBrush
    from PyQt6.QtCore import Qt
    PYQT_VERSION = 6
except ImportError:
    try:
        from PyQt5.QtCore import QRectF, QPointF, Qt
        from PyQt5.QtGui import QPainter, QPicture, QPen, QBrush
        PYQT_VERSION = 5
    except ImportError:
        raise ImportError("需要安裝 PyQt5 或 PyQt6")


class CandlestickItem(pg.GraphicsObject):
    """
    根據提供的數據繪製K線圖 - 修復版
    資料格式：DataFrame with columns [date, Open, High, Low, Close, Volume]
    """
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = QPicture()
        self.valid_data_count = 0
        self.skipped_data_count = 0
        self.generate_picture()

    def validate_ohlc_data(self, open_price, high_price, low_price, close_price, index):
        """驗證OHLC數據的有效性"""
        try:
            # 檢查NaN值
            if pd.isna(open_price) or pd.isna(high_price) or pd.isna(low_price) or pd.isna(close_price):
                logging.warning(f"K線數據包含NaN值 (索引: {index})")
                return False, "包含NaN值"
            
            # 檢查負數
            if open_price <= 0 or high_price <= 0 or low_price <= 0 or close_price <= 0:
                logging.warning(f"K線數據包含非正數 (索引: {index})")
                return False, "包含非正數"
            
            # 檢查邏輯關係
            if high_price < low_price:
                logging.warning(f"最高價({high_price}) < 最低價({low_price}) (索引: {index})")
                return False, "最高價小於最低價"
            
            if high_price < max(open_price, close_price):
                logging.warning(f"最高價({high_price}) < max(開盤價, 收盤價) (索引: {index})")
                return False, "最高價小於開盤或收盤價"
            
            if low_price > min(open_price, close_price):
                logging.warning(f"最低價({low_price}) > min(開盤價, 收盤價) (索引: {index})")
                return False, "最低價大於開盤或收盤價"
            
            return True, "數據有效"
            
        except Exception as e:
            logging.error(f"驗證OHLC數據時出錯 (索引: {index}): {e}")
            return False, f"驗證錯誤: {str(e)}"

    def generate_picture(self):
        """生成K線圖的繪圖指令 - 修復版"""
        if self.data is None or len(self.data) == 0:
            logging.warning("K線數據為空，無法繪製")
            return
        
        painter = QPainter(self.picture)
        
        try:
            # 設置畫筆 - 兼容 PyQt5/PyQt6
            try:
                painter.setPen(pg.mkPen('k'))  # 黑色邊框
            except:
                # 如果 pyqtgraph 的 mkPen 有問題，使用 Qt 原生方法
                if PYQT_VERSION == 6:
                    painter.setPen(QPen(Qt.GlobalColor.black))
                else:
                    painter.setPen(QPen(Qt.black))

            w = 0.6  # K線寬度
            self.valid_data_count = 0
            self.skipped_data_count = 0

            for i in range(len(self.data)):
                try:
                    # 獲取OHLC數據
                    open_price = float(self.data['Open'].iloc[i])
                    high_price = float(self.data['High'].iloc[i])
                    low_price = float(self.data['Low'].iloc[i])
                    close_price = float(self.data['Close'].iloc[i])
                    
                    # 驗證數據有效性
                    is_valid, reason = self.validate_ohlc_data(open_price, high_price, low_price, close_price, i)
                    
                    if not is_valid:
                        self.skipped_data_count += 1
                        logging.debug(f"跳過無效K線數據 (索引: {i}): {reason}")
                        continue
                    
                    # 台股的顏色習慣：紅色表示上漲，綠色表示下跌
                    if close_price > open_price:
                        # 上漲 - 紅色
                        try:
                            painter.setBrush(pg.mkBrush('r'))
                        except:
                            if PYQT_VERSION == 6:
                                painter.setBrush(QBrush(Qt.GlobalColor.red))
                            else:
                                painter.setBrush(QBrush(Qt.red))
                        
                        # 繪製實體（開盤價到收盤價）
                        rect = QRectF(i - w/2, open_price, w, close_price - open_price)
                        painter.drawRect(rect)
                        
                    else:
                        # 下跌 - 綠色
                        try:
                            painter.setBrush(pg.mkBrush('g'))
                        except:
                            if PYQT_VERSION == 6:
                                painter.setBrush(QBrush(Qt.GlobalColor.green))
                            else:
                                painter.setBrush(QBrush(Qt.green))
                        
                        # 繪製實體（收盤價到開盤價）
                        rect = QRectF(i - w/2, close_price, w, open_price - close_price)
                        painter.drawRect(rect)

                    # 繪製上下影線
                    painter.drawLine(QPointF(i, low_price), QPointF(i, high_price))
                    
                    self.valid_data_count += 1
                    
                except Exception as e:
                    self.skipped_data_count += 1
                    logging.error(f"繪製K線時出錯 (索引: {i}): {e}")
                    continue

            logging.info(f"K線繪製完成: 成功 {self.valid_data_count} 根，跳過 {self.skipped_data_count} 根")
            
        except Exception as e:
            logging.error(f"K線圖繪製過程出錯: {e}")
        finally:
            painter.end()

    def paint(self, painter, option, widget):
        """繪製K線圖"""
        try:
            painter.drawPicture(0, 0, self.picture)
        except Exception as e:
            logging.error(f"K線圖繪製到畫布時出錯: {e}")

    def boundingRect(self):
        """返回邊界矩形"""
        try:
            return QRectF(self.picture.boundingRect())
        except Exception as e:
            logging.error(f"獲取K線圖邊界時出錯: {e}")
            return QRectF(0, 0, 100, 100)  # 返回默認邊界
    
    def get_statistics(self):
        """獲取繪製統計信息"""
        return {
            'total_data': len(self.data) if self.data is not None else 0,
            'valid_candlesticks': self.valid_data_count,
            'skipped_candlesticks': self.skipped_data_count,
            'success_rate': (self.valid_data_count / len(self.data) * 100) if len(self.data) > 0 else 0
        }
'''
    
    # 備份原檔案
    backup_path = "charts/candlestick_backup.py"
    if os.path.exists("charts/candlestick.py"):
        try:
            with open("charts/candlestick.py", 'r', encoding='utf-8') as f:
                original_content = f.read()
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"✅ 原檔案已備份到: {backup_path}")
        except Exception as e:
            print(f"⚠️ 備份原檔案失敗: {e}")
    
    # 寫入修復版本
    try:
        os.makedirs("charts", exist_ok=True)
        with open("charts/candlestick.py", 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        print("✅ 修復版CandlestickItem已創建")
        return True
    except Exception as e:
        print(f"❌ 創建修復版本失敗: {e}")
        return False

def create_k_line_test():
    """創建K線圖測試工具"""
    print("🧪 創建K線圖測試工具")
    
    test_content = '''#!/usr/bin/env python3
"""
K線圖測試工具
測試修復後的K線圖顯示效果
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
import pyqtgraph as pg

# 導入修復版的CandlestickItem
try:
    from charts.candlestick import CandlestickItem
    print("✅ 成功導入修復版CandlestickItem")
except ImportError as e:
    print(f"❌ 導入CandlestickItem失敗: {e}")
    sys.exit(1)

def create_test_data(days=60):
    """創建測試數據"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # 生成交易日
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
    
    data_list = []
    base_price = 1000
    
    for i, date in enumerate(date_range):
        # 模擬價格波動
        price_change = np.random.normal(0, 20)
        base_price += price_change
        
        open_price = base_price + np.random.normal(0, 5)
        high_price = max(open_price, base_price) + abs(np.random.normal(0, 10))
        low_price = min(open_price, base_price) - abs(np.random.normal(0, 10))
        close_price = base_price + np.random.normal(0, 5)
        volume = np.random.randint(10000, 100000) * 1000
        
        # 模擬一些數據問題
        if i % 20 == 0:  # 每20天有一個潛在問題
            if np.random.random() < 0.3:  # 30%機率有問題
                if np.random.random() < 0.5:
                    # 模擬NaN值
                    open_price = np.nan
                else:
                    # 模擬邏輯錯誤
                    high_price = low_price - 10
        
        data_list.append({
            'date': date,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    return pd.DataFrame(data_list)

class KLineTestWindow(QMainWindow):
    """K線圖測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("K線圖修復測試")
        self.setGeometry(100, 100, 1200, 800)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建圖表視圖
        self.chart_view = pg.PlotWidget()
        self.chart_view.setBackground('k')
        self.chart_view.setTitle("K線圖修復測試", color='w', size='16pt')
        layout.addWidget(self.chart_view)
        
        # 載入測試數據
        self.load_test_data()
    
    def load_test_data(self):
        """載入測試數據"""
        print("📊 載入測試數據...")
        
        # 創建測試數據
        df = create_test_data(60)
        print(f"✅ 創建了 {len(df)} 天的測試數據")
        
        # 創建K線圖
        candlestick = CandlestickItem(df)
        self.chart_view.addItem(candlestick)
        
        # 獲取統計信息
        stats = candlestick.get_statistics()
        print(f"📊 K線圖統計:")
        print(f"   總數據量: {stats['total_data']}")
        print(f"   成功繪製: {stats['valid_candlesticks']}")
        print(f"   跳過數量: {stats['skipped_candlesticks']}")
        print(f"   成功率: {stats['success_rate']:.1f}%")
        
        # 更新標題
        title = f"K線圖修復測試 - 成功率: {stats['success_rate']:.1f}% ({stats['valid_candlesticks']}/{stats['total_data']})"
        self.chart_view.setTitle(title, color='w', size='14pt')

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 創建測試窗口
    window = KLineTestWindow()
    window.show()
    
    print("🎯 K線圖測試窗口已開啟")
    print("請檢查K線圖是否正常顯示，特別注意是否有缺失的日期")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("test_k_line_fix.py", 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ K線圖測試工具已創建: test_k_line_fix.py")
        return True
    except Exception as e:
        print(f"❌ 創建測試工具失敗: {e}")
        return False

def main():
    """主修復流程"""
    print("🔧 K線圖顯示修復工具")
    print("=" * 60)
    
    print("\n步驟1: 檢查和修復CandlestickItem")
    success1 = fix_candlestick_item()
    
    print("\n步驟2: 創建測試工具")
    success2 = create_k_line_test()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ K線圖修復完成！")
        print("\n🎯 下一步操作:")
        print("1. 運行測試: python test_k_line_fix.py")
        print("2. 重新啟動主程式測試K線圖")
        print("3. 檢查是否還有缺失的日期")
        
        print("\n🔧 修復內容:")
        print("• 添加了完整的數據驗證邏輯")
        print("• 加強了錯誤處理機制")
        print("• 提供了詳細的統計信息")
        print("• 改善了NaN值和異常數據的處理")
        
    else:
        print("❌ 修復過程中出現問題")
        print("請檢查錯誤信息並手動修復")

if __name__ == "__main__":
    main()
