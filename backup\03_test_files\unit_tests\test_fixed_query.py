#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修正後的除權息查詢
"""

import sqlite3
import os

def test_fixed_query():
    """測試修正後的查詢邏輯"""
    print("🔍 測試修正後的除權息查詢...")
    
    try:
        dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
        
        if not os.path.exists(dividend_db_path):
            print(f"❌ 除權息資料庫不存在: {dividend_db_path}")
            return
        
        conn = sqlite3.connect(dividend_db_path)
        cursor = conn.cursor()
        
        start_date = "2025-07-15"
        end_date = "2025-07-22"
        
        print(f"📅 查詢期間: {start_date} 到 {end_date}")
        
        # 使用修正後的查詢邏輯
        query = """
            SELECT stock_code, stock_name, year, ex_dividend_date,
                   cash_dividend, stock_dividend, total_dividend,
                   dividend_yield, eps, data_source
            FROM (
                SELECT stock_code, stock_name, year, ex_dividend_date,
                       cash_dividend, stock_dividend, total_dividend,
                       dividend_yield, eps, data_source,
                       ROW_NUMBER() OVER (
                           PARTITION BY stock_code, ex_dividend_date 
                           ORDER BY 
                               CASE WHEN data_source = 'goodinfo' THEN 1
                                    WHEN data_source = 'csv_import' THEN 2
                                    ELSE 3 END,
                               ABS(year - CAST(substr(ex_dividend_date, 1, 4) AS INTEGER)),
                               year DESC
                       ) as rn
                FROM dividend_data
                WHERE ex_dividend_date IS NOT NULL
                AND ex_dividend_date != ''
                AND ex_dividend_date >= ?
                AND ex_dividend_date <= ?
                AND (data_source IS NULL OR data_source != 'sample_data')
                AND year >= 2020  -- 排除明顯過舊的資料
            ) ranked
            WHERE rn = 1
            ORDER BY ex_dividend_date, stock_code
        """
        
        cursor.execute(query, (start_date, end_date))
        results = cursor.fetchall()
        
        print(f"📊 修正後查詢結果: 找到 {len(results)} 筆記錄")
        
        if results:
            print("\n📋 前15筆查詢結果:")
            print("股票代碼 | 股票名稱 | 年份 | 除權息日 | 現金股利 | 資料來源")
            print("-" * 80)
            
            for row in results[:15]:
                stock_code = row[0]
                stock_name = row[1] or 'N/A'
                year = row[2]
                ex_date = row[3]
                cash_dividend = row[4] or 0
                data_source = row[9] or 'N/A'
                
                print(f"{stock_code:<8} | {stock_name:<10} | {year} | {ex_date} | {cash_dividend:.2f} | {data_source}")
            
            # 檢查年份分布
            print(f"\n📊 修正後年份分布統計:")
            year_counts = {}
            for row in results:
                year = row[2]
                year_counts[year] = year_counts.get(year, 0) + 1
            
            for year in sorted(year_counts.keys()):
                count = year_counts[year]
                percentage = (count / len(results)) * 100
                print(f"  {year}年: {count} 筆 ({percentage:.1f}%)")
                
            # 檢查年份與除權息日期的一致性
            print(f"\n📊 年份與除權息日期一致性檢查:")
            consistent_count = 0
            for row in results:
                year = row[2]
                ex_date = row[3]
                ex_year = int(ex_date.split('-')[0])
                if abs(year - ex_year) <= 1:  # 允許1年的差異
                    consistent_count += 1
            
            consistency_rate = (consistent_count / len(results)) * 100
            print(f"  一致或接近的記錄: {consistent_count}/{len(results)} ({consistency_rate:.1f}%)")
                
        else:
            print("❌ 沒有找到符合條件的資料")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🚀 修正後除權息查詢測試")
    print("=" * 50)
    
    test_fixed_query()

if __name__ == "__main__":
    main()
