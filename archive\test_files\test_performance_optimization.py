#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試日內策略性能優化效果
"""

import sys
import os
import time
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def performance_test():
    """性能測試對比"""
    print("🚀 日內策略性能優化測試")
    print("=" * 60)
    
    # 測試股票列表
    test_stocks = [
        '2330', '2317', '2454', '1301', '2303', '2382', '2412', '2881', '2886', '3008',
        '2002', '1216', '1101', '2207', '2308', '2327', '2357', '2395', '2408', '2409',
        '2474', '2603', '2609', '2615', '2633', '2801', '2880', '2882', '2883', '2884',
        '2885', '2887', '2890', '2891', '2892', '2912', '3034', '3037', '3045', '3711'
    ]
    
    print(f"📊 測試 {len(test_stocks)} 支股票")
    
    # 測試1: 快速模式
    print("\n🔥 測試1: 快速模式 (fast_mode=True)")
    print("-" * 40)
    
    strategy_fast = IntradayOpeningRangeStrategy()
    strategy_fast.fast_mode = True
    strategy_fast.enable_cache = True
    
    start_time = time.time()
    fast_results = []
    
    for i, stock_id in enumerate(test_stocks):
        print(f"[{i+1:2d}/{len(test_stocks)}] 快速分析 {stock_id}...", end=" ")
        
        try:
            result = strategy_fast.fast_breakout_analysis(stock_id)
            if result and result.get('result') in ['buy_signal', 'wait_signal']:
                confidence = result.get('signal_details', {}).get('confidence', 0)
                fast_results.append({
                    'stock_id': stock_id,
                    'result': result.get('result'),
                    'confidence': confidence,
                    'reason': result.get('reason', '')
                })
                print(f"✅ {result.get('result')} ({confidence}%)")
            else:
                print(f"❌ 無信號")
        except Exception as e:
            print(f"⚠️ 錯誤: {str(e)[:20]}...")
    
    fast_time = time.time() - start_time
    
    # 測試2: 完整模式（模擬）
    print(f"\n🐌 測試2: 完整模式模擬 (包含盤中數據生成)")
    print("-" * 40)
    
    strategy_full = IntradayOpeningRangeStrategy()
    strategy_full.fast_mode = False
    strategy_full.enable_cache = True
    
    start_time = time.time()
    full_results = []
    
    # 只測試前10支股票來模擬完整模式的耗時
    test_subset = test_stocks[:10]
    
    for i, stock_id in enumerate(test_subset):
        print(f"[{i+1:2d}/{len(test_subset)}] 完整分析 {stock_id}...", end=" ")
        
        try:
            result = strategy_full.simulate_breakout_trading(stock_id, datetime.datetime.now())
            if result and result.get('result') in ['buy_signal', 'wait_signal']:
                confidence = result.get('signal_details', {}).get('confidence', 0)
                full_results.append({
                    'stock_id': stock_id,
                    'result': result.get('result'),
                    'confidence': confidence,
                    'reason': result.get('reason', '')
                })
                print(f"✅ {result.get('result')} ({confidence}%)")
            else:
                print(f"❌ 無信號")
        except Exception as e:
            print(f"⚠️ 錯誤: {str(e)[:20]}...")
    
    full_time = time.time() - start_time
    
    # 性能分析
    print("\n" + "=" * 60)
    print("📈 性能分析結果")
    print("=" * 60)
    
    # 時間對比
    print(f"⏱️  時間對比:")
    print(f"• 快速模式: {fast_time:.2f} 秒 ({len(test_stocks)} 支股票)")
    print(f"• 完整模式: {full_time:.2f} 秒 ({len(test_subset)} 支股票)")
    
    # 推算完整模式處理所有股票的時間
    estimated_full_time = full_time * len(test_stocks) / len(test_subset)
    print(f"• 完整模式估算: {estimated_full_time:.2f} 秒 ({len(test_stocks)} 支股票)")
    
    # 性能提升
    if estimated_full_time > 0:
        speedup = estimated_full_time / fast_time
        print(f"🚀 性能提升: {speedup:.1f}x 倍")
    
    # 平均處理時間
    avg_fast = fast_time / len(test_stocks)
    avg_full = full_time / len(test_subset)
    print(f"\n📊 平均處理時間:")
    print(f"• 快速模式: {avg_fast:.3f} 秒/股票")
    print(f"• 完整模式: {avg_full:.3f} 秒/股票")
    
    # 結果對比
    print(f"\n📋 結果對比:")
    print(f"• 快速模式信號數: {len(fast_results)}")
    print(f"• 完整模式信號數: {len(full_results)} (僅測試{len(test_subset)}支)")
    
    if fast_results:
        print(f"\n🎯 快速模式信號詳情:")
        fast_results.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        for i, result in enumerate(fast_results[:5]):
            print(f"{i+1}. {result['stock_id']}: {result['result']} ({result['confidence']}%)")
            print(f"   {result['reason']}")
    
    if full_results:
        print(f"\n🎯 完整模式信號詳情:")
        full_results.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        for i, result in enumerate(full_results[:3]):
            print(f"{i+1}. {result['stock_id']}: {result['result']} ({result['confidence']}%)")
            print(f"   {result['reason']}")
    
    # 緩存效果測試
    print(f"\n🗄️  緩存效果測試:")
    print("-" * 30)
    
    # 第二次運行快速模式（應該更快）
    start_time = time.time()
    cached_count = 0
    
    for stock_id in test_stocks[:10]:  # 測試前10支
        result = strategy_fast.fast_breakout_analysis(stock_id)
        if result:
            cached_count += 1
    
    cached_time = time.time() - start_time
    
    print(f"• 首次運行: {fast_time/len(test_stocks)*10:.3f} 秒 (10支股票)")
    print(f"• 緩存運行: {cached_time:.3f} 秒 (10支股票)")
    
    if fast_time > 0:
        cache_speedup = (fast_time/len(test_stocks)*10) / cached_time if cached_time > 0 else 1
        print(f"• 緩存加速: {cache_speedup:.1f}x 倍")
    
    # 總結建議
    print(f"\n💡 優化建議:")
    if speedup > 5:
        print(f"✅ 性能優化效果顯著，建議使用快速模式")
    elif speedup > 2:
        print(f"⚠️ 性能有所提升，可根據需求選擇模式")
    else:
        print(f"❌ 性能提升有限，需要進一步優化")
    
    print(f"\n🎯 使用建議:")
    print(f"• 日常篩選: 使用快速模式 (fast_mode=True)")
    print(f"• 詳細分析: 使用完整模式 (fast_mode=False)")
    print(f"• 大量測試: 啟用緩存 (enable_cache=True)")
    print(f"• 實時監控: 結合兩種模式使用")

def memory_usage_test():
    """記憶體使用測試"""
    try:
        import psutil
        import os
        
        print(f"\n🧠 記憶體使用測試:")
        print("-" * 30)
        
        process = psutil.Process(os.getpid())
        
        # 測試前記憶體
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        print(f"• 測試前記憶體: {mem_before:.1f} MB")
        
        # 運行快速模式
        strategy = IntradayOpeningRangeStrategy()
        strategy.fast_mode = True
        strategy.enable_cache = True
        
        test_stocks = ['2330', '2317', '2454', '1301', '2303']
        for stock_id in test_stocks:
            strategy.fast_breakout_analysis(stock_id)
        
        # 測試後記憶體
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        print(f"• 測試後記憶體: {mem_after:.1f} MB")
        print(f"• 記憶體增加: {mem_after - mem_before:.1f} MB")
        
        # 清理緩存
        strategy.data_cache.clear()
        strategy.cache_timestamps.clear()
        
        mem_cleaned = process.memory_info().rss / 1024 / 1024  # MB
        print(f"• 清理後記憶體: {mem_cleaned:.1f} MB")
        
    except ImportError:
        print(f"⚠️ 需要安裝 psutil 來測試記憶體使用: pip install psutil")

if __name__ == "__main__":
    performance_test()
    memory_usage_test()
