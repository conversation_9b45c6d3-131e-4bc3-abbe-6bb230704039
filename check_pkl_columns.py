#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 PKL 檔案的確切欄位名稱
"""

import pandas as pd

def check_pkl_columns():
    """檢查 PKL 檔案的確切欄位名稱"""
    
    pkl_file = 'D:/Finlab/history/tables/twse_divide_ratio.pkl'
    
    try:
        df = pd.read_pickle(pkl_file)
        
        print("📋 PKL 檔案欄位名稱 (包含索引):")
        print(f"索引名稱: {df.index.names}")
        
        print(f"\n📋 PKL 檔案欄位名稱 (共 {len(df.columns)} 個):")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. '{col}' (長度: {len(col)})")
            # 檢查是否包含特殊字符
            if ' ' in col:
                print(f"       ↳ 包含空格")
            if '(' in col or ')' in col:
                print(f"       ↳ 包含括號")
        
        # 特別檢查問題欄位
        problem_columns = [col for col in df.columns if '最近一次申報' in col]
        print(f"\n🔍 包含 '最近一次申報' 的欄位:")
        for col in problem_columns:
            print(f"   '{col}'")
            print(f"   字節表示: {repr(col)}")
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == "__main__":
    check_pkl_columns()
