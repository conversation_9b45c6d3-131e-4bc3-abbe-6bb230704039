#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試腳本
"""

def test_imports():
    """測試模組導入"""
    print("測試模組導入...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        print("✅ SafeMarketScanner 導入成功")
        
        scanner = SafeMarketScanner()
        print(f"✅ 掃描器初始化成功: {scanner.name} v{scanner.version}")
        
        # 測試新方法
        print("測試新增方法...")
        
        china_data = scanner.get_china_indices()
        print(f"✅ 中國指數: {len(china_data)} 項")
        
        fx_data = scanner.get_fx_rates()
        print(f"✅ 外匯匯率: {len(fx_data)} 項")
        if '美元指數' in fx_data:
            print("✅ 美元指數已包含")
        
        tw_pos = scanner.get_taiwan_futures_positions()
        print(f"✅ 台股期貨多空: {len(tw_pos)} 項")
        
        foreign_pos = scanner.get_foreign_futures_positions()
        print(f"✅ 外資期貨多空: {len(foreign_pos)} 項")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_dialog():
    """測試對話框"""
    print("\n測試對話框...")
    
    try:
        from market_data_crawler_dialog import MarketDataCrawlerDialog, MarketCrawlerWorker
        print("✅ 對話框模組導入成功")
        return True
    except Exception as e:
        print(f"❌ 對話框測試失敗: {e}")
        return False

def test_real_fetcher():
    """測試真實數據獲取器"""
    print("\n測試真實數據獲取器...")
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        print("✅ 真實數據獲取器導入成功")
        return True
    except Exception as e:
        print(f"❌ 真實數據獲取器測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🧪 簡單測試開始")
    print("=" * 40)
    
    results = []
    
    # 測試導入
    results.append(("模組導入", test_imports()))
    results.append(("對話框", test_dialog()))
    results.append(("真實數據獲取器", test_real_fetcher()))
    
    print("\n" + "=" * 40)
    print("📋 測試結果:")
    
    passed = 0
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"   {status} {name}")
        if result:
            passed += 1
    
    print(f"\n📊 {passed}/{len(results)} 測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！")
    else:
        print("⚠️ 部分測試失敗")
