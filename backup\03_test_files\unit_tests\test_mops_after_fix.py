#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的MOPS下載器
"""

def test_mops_downloader():
    """測試MOPS下載器"""
    
    print("🧪 測試修復後的MOPS下載器")
    print("=" * 50)
    
    try:
        from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
        
        # 初始化下載器
        db_path = "D:/Finlab/history/tables/monthly_revenue.db"
        downloader = MopsBulkRevenueDownloader(db_path)
        
        print("✅ 下載器初始化成功")
        
        # 測試下載少量數據（避免過度請求）
        print(f"\n🔄 測試下載2024年8月上市前10家公司數據...")
        
        # 下載上市數據
        sii_data = downloader.download_market_revenue('sii', 2024, 8)
        
        if len(sii_data) > 0:
            print(f"📊 成功獲取 {len(sii_data)} 筆上市數據")
            
            # 只保存前5筆測試
            test_data = sii_data[:5]
            print(f"\n🔍 測試數據:")
            for i, data in enumerate(test_data):
                print(f"  {i+1}. {data['stock_id']} {data['stock_name']}")
                print(f"     營收: {data['revenue']:,} | 年增率: {data.get('revenue_yoy', 'N/A')}%")
            
            # 測試保存到資料庫
            print(f"\n💾 測試保存到資料庫...")
            saved_count = downloader.save_to_database(test_data)
            
            if saved_count > 0:
                print(f"✅ 成功保存 {saved_count} 筆數據")
                
                # 驗證保存結果
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT stock_id, stock_name, revenue, revenue_yoy, market, updated_at
                    FROM monthly_revenue 
                    WHERE year = 2024 AND month = 8
                    ORDER BY stock_id
                    LIMIT 5
                ''')
                
                results = cursor.fetchall()
                print(f"\n🔍 資料庫驗證結果:")
                for row in results:
                    stock_id, stock_name, revenue, revenue_yoy, market, updated_at = row
                    print(f"  {stock_id} {stock_name} [{market}]")
                    print(f"    營收: {revenue:,} | 年增率: {revenue_yoy}% | 更新: {updated_at}")
                
                # 檢查總數據量
                cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
                total_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT year || '-' || month) FROM monthly_revenue")
                month_count = cursor.fetchone()[0]
                
                print(f"\n📊 資料庫總覽:")
                print(f"  總記錄數: {total_count:,}")
                print(f"  涵蓋月份: {month_count} 個月")
                
                conn.close()
                
                return True
            else:
                print("❌ 保存失敗")
                return False
        else:
            print("⚠️ 沒有獲取到數據")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """測試GUI整合"""
    
    print(f"\n🧪 測試GUI整合")
    print("=" * 30)
    
    try:
        from unified_monthly_revenue_gui import UnifiedMonthlyRevenueGUI
        
        print("✅ GUI模組導入成功")
        
        # 檢查資料庫狀態
        import sqlite3
        import os
        
        db_path = "D:/Finlab/history/tables/monthly_revenue.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查 updated_at 欄位
            cursor.execute("PRAGMA table_info(monthly_revenue)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'updated_at' in columns:
                print("✅ updated_at 欄位存在")
                
                # 檢查數據完整性
                cursor.execute("SELECT COUNT(*) FROM monthly_revenue WHERE updated_at IS NOT NULL")
                valid_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
                total_count = cursor.fetchone()[0]
                
                print(f"✅ updated_at 完整性: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%)")
            else:
                print("❌ updated_at 欄位不存在")
                return False
            
            conn.close()
        
        print(f"\n💡 GUI功能狀態:")
        print(f"  • 🆕 新建資料庫: ✅ 可用")
        print(f"  • 📥 匯入CSV: ✅ 可用")
        print(f"  • 📊 查看數據庫: ✅ 可用")
        print(f"  • 🔄 傳統下載: ✅ 可用")
        print(f"  • 📅 MOPS批量下載: ✅ 已修復")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    
    print("🎯 測試修復後的月營收功能")
    print("=" * 60)
    
    # 測試1: MOPS下載器
    success1 = test_mops_downloader()
    
    # 測試2: GUI整合
    success2 = test_gui_integration()
    
    # 總結
    print(f"\n" + "="*60)
    print(f"🎉 測試總結:")
    print(f"  MOPS下載器: {'✅ 通過' if success1 else '❌ 失敗'}")
    print(f"  GUI整合: {'✅ 通過' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print(f"\n🎊 所有功能測試通過！")
        print(f"💡 updated_at 欄位問題已完全解決")
        print(f"🚀 現在可以正常使用月營收功能：")
        print(f"  1. 開啟主程式 O3mh_gui_v21_optimized.py")
        print(f"  2. 點擊選單：爬蟲 → 📈 月營收資料下載器")
        print(f"  3. 使用批量下載功能不會再出現錯誤")
    else:
        print(f"\n⚠️ 部分功能仍有問題，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
