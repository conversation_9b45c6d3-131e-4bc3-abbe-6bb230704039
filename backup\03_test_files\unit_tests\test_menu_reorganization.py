#!/usr/bin/env python3
"""
測試台股智能選股系統選單重組
驗證新增的爬蟲、資料庫、除權息選單
"""

import sys
import os
from datetime import datetime

def test_menu_structure():
    """測試選單結構"""
    print("🧪 測試選單結構重組")
    print("=" * 60)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查新增的三個左側選單
        new_menus = [
            ("🕷️ 爬蟲", "crawler_menu"),
            ("🗄️ 資料庫", "database_menu"), 
            ("💰 除權息", "dividend_menu")
        ]
        
        print("   檢查新增的左側選單:")
        for menu_name, menu_var in new_menus:
            if menu_name in content and menu_var in content:
                print(f"     ✅ {menu_name}")
            else:
                print(f"     ❌ {menu_name}")
                return False
        
        # 檢查保留的現有選單
        existing_menus = [
            "📊 回測",
            "🔧 工具", 
            "📥 資料下載",
            "🌐 GoodInfo",
            "❓ 幫助"
        ]
        
        print("   檢查保留的現有選單:")
        for menu_name in existing_menus:
            if menu_name in content:
                print(f"     ✅ {menu_name}")
            else:
                print(f"     ❌ {menu_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試選單結構失敗: {e}")
        return False

def test_crawler_menu():
    """測試爬蟲選單內容"""
    print("\n🕷️ 測試爬蟲選單")
    print("-" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查爬蟲選單項目
        crawler_items = [
            "📰 財經新聞爬蟲",
            "🌐 GoodInfo 爬蟲",
            "📊 技術分析爬蟲"
        ]
        
        print("   檢查爬蟲選單項目:")
        for item in crawler_items:
            if item in content:
                print(f"     ✅ {item}")
            else:
                print(f"     ❌ {item}")
                return False
        
        # 檢查對應的方法連接
        crawler_methods = [
            "open_news_crawler",
            "open_goodinfo_crawler", 
            "open_goodinfo_technical_crawler"
        ]
        
        print("   檢查爬蟲方法連接:")
        for method in crawler_methods:
            if method in content:
                print(f"     ✅ {method}")
            else:
                print(f"     ❌ {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試爬蟲選單失敗: {e}")
        return False

def test_database_menu():
    """測試資料庫選單內容"""
    print("\n🗄️ 測試資料庫選單")
    print("-" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查資料庫選單項目
        database_items = [
            "💾 數據管理",
            "🌐 GoodInfo 資料庫",
            "📥 資料下載"
        ]
        
        print("   檢查資料庫選單項目:")
        for item in database_items:
            if item in content:
                print(f"     ✅ {item}")
            else:
                print(f"     ❌ {item}")
                return False
        
        # 檢查對應的方法連接
        database_methods = [
            "open_data_manager",
            "open_goodinfo_database_manager",
            "open_data_downloader"
        ]
        
        print("   檢查資料庫方法連接:")
        for method in database_methods:
            if method in content:
                print(f"     ✅ {method}")
            else:
                print(f"     ❌ {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試資料庫選單失敗: {e}")
        return False

def test_dividend_menu():
    """測試除權息選單內容"""
    print("\n💰 測試除權息選單")
    print("-" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查除權息選單項目
        dividend_items = [
            "📥 除權息資料下載",
            "📊 除權息策略分析",
            "📋 除權息報告"
        ]
        
        print("   檢查除權息選單項目:")
        for item in dividend_items:
            if item in content:
                print(f"     ✅ {item}")
            else:
                print(f"     ❌ {item}")
                return False
        
        # 檢查對應的方法連接
        dividend_methods = [
            "download_enhanced_dividend_data",
            "open_goodinfo_dividend_strategy",
            "open_dividend_report_generator"
        ]
        
        print("   檢查除權息方法連接:")
        for method in dividend_methods:
            if method in content:
                print(f"     ✅ {method}")
            else:
                print(f"     ❌ {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試除權息選單失敗: {e}")
        return False

def test_menu_deduplication():
    """測試選單去重"""
    print("\n🔄 測試選單去重")
    print("-" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否移除了重複項目
        print("   檢查重複項目移除:")
        
        # 檢查工具選單是否移除了數據管理
        tools_section_start = content.find("# 工具菜單")
        tools_section_end = content.find("# 資料下載菜單", tools_section_start)
        if tools_section_start != -1 and tools_section_end != -1:
            tools_section = content[tools_section_start:tools_section_end]
            if "💾 數據管理" not in tools_section:
                print("     ✅ 工具選單已移除重複的數據管理")
            else:
                print("     ❌ 工具選單仍有重複的數據管理")
                return False
        
        # 檢查資料下載選單是否移除了除權息相關項目
        download_section_start = content.find("# 資料下載菜單")
        download_section_end = content.find("# GoodInfo 專區菜單", download_section_start)
        if download_section_start != -1 and download_section_end != -1:
            download_section = content[download_section_start:download_section_end]
            if "💰 除權息資料" not in download_section:
                print("     ✅ 資料下載選單已移除重複的除權息項目")
            else:
                print("     ❌ 資料下載選單仍有重複的除權息項目")
                return False
        
        # 檢查GoodInfo選單是否簡化
        goodinfo_section_start = content.find("# GoodInfo 專區菜單")
        goodinfo_section_end = content.find("# 幫助菜單", goodinfo_section_start)
        if goodinfo_section_start != -1 and goodinfo_section_end != -1:
            goodinfo_section = content[goodinfo_section_start:goodinfo_section_end]
            # 檢查是否移除了爬蟲相關項目（已移到爬蟲選單）
            if "🕷️ GoodInfo 爬蟲" not in goodinfo_section:
                print("     ✅ GoodInfo選單已移除重複的爬蟲項目")
            else:
                print("     ❌ GoodInfo選單仍有重複的爬蟲項目")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試選單去重失敗: {e}")
        return False

def test_menu_order():
    """測試選單順序"""
    print("\n📋 測試選單順序")
    print("-" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查選單出現順序
        menu_order = [
            "🕷️ 爬蟲",
            "🗄️ 資料庫", 
            "💰 除權息",
            "📊 回測",
            "🔧 工具",
            "📥 資料下載",
            "🌐 GoodInfo",
            "❓ 幫助"
        ]
        
        print("   檢查選單順序:")
        last_position = 0
        for menu_name in menu_order:
            position = content.find(f"addMenu('{menu_name}')")
            if position != -1:
                if position > last_position:
                    print(f"     ✅ {menu_name} (位置: {position})")
                    last_position = position
                else:
                    print(f"     ❌ {menu_name} 順序錯誤")
                    return False
            else:
                print(f"     ❌ {menu_name} 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試選單順序失敗: {e}")
        return False

def generate_menu_reorganization_report():
    """生成選單重組報告"""
    print("\n📊 生成選單重組報告")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"menu_reorganization_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("台股智能選股系統 - 選單重組報告\n")
            f.write("=" * 60 + "\n")
            f.write(f"重組時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("🎯 重組目標:\n")
            f.write("• 在左側新增「爬蟲」、「資料庫」、「除權息」三個專門選單\n")
            f.write("• 保留現有選單名稱和功能\n")
            f.write("• 移除重複項目，優化選單結構\n")
            f.write("• 提升用戶體驗和功能分類清晰度\n\n")
            
            f.write("✅ 實現的改進:\n\n")
            
            f.write("1. 新增左側專門選單:\n\n")
            
            f.write("   🕷️ 爬蟲選單:\n")
            f.write("     • 📰 財經新聞爬蟲 - 爬取財經新聞資料\n")
            f.write("     • 🌐 GoodInfo 爬蟲 - 爬取 GoodInfo 股票資料\n")
            f.write("     • 📊 技術分析爬蟲 - 爬取技術分析資料\n\n")
            
            f.write("   🗄️ 資料庫選單:\n")
            f.write("     • 💾 數據管理 - 管理股價數據和數據庫\n")
            f.write("     • 🌐 GoodInfo 資料庫 - 管理 GoodInfo 資料庫\n")
            f.write("     • 📥 資料下載 - 下載股票資料到資料庫\n\n")
            
            f.write("   💰 除權息選單:\n")
            f.write("     • 📥 除權息資料下載 - 下載台股除權息資料\n")
            f.write("     • 📊 除權息策略分析 - GoodInfo 除權息策略分析\n")
            f.write("     • 📋 除權息報告 - 生成除權息投資報告\n\n")
            
            f.write("2. 保留現有選單 (重新整理):\n\n")
            
            f.write("   📊 回測選單:\n")
            f.write("     • 🎯 阿水一式回測分析\n")
            f.write("     • 🤖 AI技術指標回測\n")
            f.write("     • 🎨 多策略整合分析\n")
            f.write("     • 📖 策略解讀指南\n")
            f.write("     • 🧪 信號強度測試\n\n")
            
            f.write("   🔧 工具選單:\n")
            f.write("     • 🎯 策略參數優化\n")
            f.write("     • 🗄️ 數據庫設定\n")
            f.write("     • 📈 策略績效分析\n\n")
            
            f.write("   📥 資料下載選單:\n")
            f.write("     • 📊 股價資料\n")
            f.write("     • 📋 財務報表\n")
            f.write("     • 🎯 除權息交易系統\n\n")
            
            f.write("   🌐 GoodInfo選單:\n")
            f.write("     • 📊 GoodInfo 策略分析\n")
            f.write("     • 📋 策略報告生成\n\n")
            
            f.write("   ❓ 幫助選單:\n")
            f.write("     • ℹ️ 關於系統\n")
            f.write("     • 📖 使用說明\n")
            f.write("     • 🤖 AI技術指標回測說明\n\n")
            
            f.write("3. 選單去重優化:\n")
            f.write("   • 移除工具選單中重複的「數據管理」\n")
            f.write("   • 移除資料下載選單中重複的「除權息資料」\n")
            f.write("   • 移除資料下載選單中重複的「財經新聞爬蟲」\n")
            f.write("   • 簡化GoodInfo選單，移除重複的爬蟲項目\n")
            f.write("   • 移除GoodInfo選單中重複的「資料庫管理」\n\n")
            
            f.write("4. 選單順序優化:\n")
            f.write("   左側新增選單 → 現有功能選單 → 幫助選單\n")
            f.write("   🕷️ 爬蟲 → 🗄️ 資料庫 → 💰 除權息 → 📊 回測 → 🔧 工具 → 📥 資料下載 → 🌐 GoodInfo → ❓ 幫助\n\n")
            
            f.write("💡 用戶體驗提升:\n")
            f.write("• 功能分類更清晰：爬蟲、資料庫、除權息各有專門選單\n")
            f.write("• 減少重複項目：避免用戶困惑\n")
            f.write("• 邏輯更合理：相關功能集中管理\n")
            f.write("• 操作更便利：快速找到所需功能\n\n")
            
            f.write("🔧 技術實現:\n")
            f.write("• 重新組織 init_menu_bar() 方法\n")
            f.write("• 新增對應的方法連接\n")
            f.write("• 保持向後兼容性\n")
            f.write("• 添加適當的狀態提示\n")
        
        print(f"✅ 選單重組報告已生成: {report_filename}")
        return report_filename
        
    except Exception as e:
        print(f"❌ 生成選單重組報告失敗: {e}")
        return None

def main():
    """主函數"""
    print("🚀 台股智能選股系統 - 選單重組測試")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("選單結構測試", test_menu_structure),
        ("爬蟲選單測試", test_crawler_menu),
        ("資料庫選單測試", test_database_menu),
        ("除權息選單測試", test_dividend_menu),
        ("選單去重測試", test_menu_deduplication),
        ("選單順序測試", test_menu_order)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 生成報告
    report_file = generate_menu_reorganization_report()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！選單重組成功")
        print("\n💡 新的選單結構:")
        print("   🕷️ 爬蟲 - 集中所有爬蟲功能")
        print("   🗄️ 資料庫 - 集中所有資料庫管理")
        print("   💰 除權息 - 集中所有除權息功能")
        print("   📊 回測 - 保留所有回測功能")
        print("   🔧 工具 - 保留工具和優化功能")
        print("   📥 資料下載 - 保留基本下載功能")
        print("   🌐 GoodInfo - 簡化為策略分析")
        print("   ❓ 幫助 - 保留幫助和說明")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    if report_file:
        print(f"\n📊 詳細報告已保存至: {report_file}")

if __name__ == '__main__':
    main()
