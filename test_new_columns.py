#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新增的月營收排行榜欄位
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, date

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_monthly_revenue_columns():
    """測試月營收排行榜的新欄位"""
    print("=" * 60)
    print("🧪 測試月營收排行榜新欄位")
    print("=" * 60)
    
    try:
        # 連接資料庫
        db_path = 'D:/Finlab/history/tables/monthly_report.db'
        if not os.path.exists(db_path):
            print(f"❌ 資料庫檔案不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path)
        
        # 查詢2025年7月的資料樣本
        query = """
        SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收
        FROM monthly_report 
        WHERE date LIKE '2025-07%' 
        AND 當月營收 IS NOT NULL 
        AND 上月營收 IS NOT NULL 
        AND 去年當月營收 IS NOT NULL
        LIMIT 10
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 沒有找到有效的月營收資料")
            return False
            
        print(f"✅ 找到 {len(df)} 筆測試資料")
        
        # 轉換數值欄位
        df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
        df['上月營收'] = pd.to_numeric(df['上月營收'], errors='coerce')
        df['去年當月營收'] = pd.to_numeric(df['去年當月營收'], errors='coerce')
        
        # 計算YoY和MoM
        df['YoY%'] = ((df['當月營收'] - df['去年當月營收']) / df['去年當月營收'] * 100).round(2)
        df['MoM%'] = ((df['當月營收'] - df['上月營收']) / df['上月營收'] * 100).round(2)
        
        # 模擬新的排行榜格式
        print("\n📊 新的月營收排行榜格式測試:")
        print("-" * 120)
        print(f"{'排名':<4} {'代碼':<6} {'名稱':<10} {'西元年月':<8} {'當月營收':<12} {'上月營收':<12} {'去年同月':<12} {'YoY%':<8} {'MoM%':<8}")
        print("-" * 120)
        
        # 按YoY排序取前5名
        sorted_df = df.sort_values('YoY%', ascending=False).head(5)
        
        for idx, (_, row) in enumerate(sorted_df.iterrows(), 1):
            # 計算西元年月 (202507)
            date_obj = datetime.strptime(row['date'], '%Y-%m-%d').date()
            year_month = date_obj.strftime('%Y%m')
            
            print(f"{idx:<4} {row['stock_id']:<6} {row['stock_name'][:8]:<10} {year_month:<8} "
                  f"{row['當月營收']:>10,.0f} {row['上月營收']:>10,.0f} {row['去年當月營收']:>10,.0f} "
                  f"{row['YoY%']:>6.1f}% {row['MoM%']:>6.1f}%")
        
        print("\n✅ 新欄位測試成功！")
        print("\n📋 新增欄位說明:")
        print("1. 西元年月: 格式為 YYYYMM (例如: 202507)")
        print("2. 上個月營收: 顯示上月營收數據，便於驗證MoM計算")
        print("3. 去年同月營收: 顯示去年同月營收數據，便於驗證YoY計算")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_data_format():
    """測試資料格式"""
    print("\n" + "=" * 60)
    print("📋 測試資料格式")
    print("=" * 60)
    
    # 模擬排行榜資料項目
    test_date = date(2025, 7, 29)
    year_month = test_date.strftime('%Y%m')  # 202507
    
    sample_data = {
        '排名': 1,
        '股票代碼': '2330',
        '股票名稱': '台積電',
        '西元年月': year_month,
        '當月營收': '120,000,000',
        '上個月營收': '115,000,000', 
        '去年同月營收': '100,000,000',
        'YoY%': '20.00%',
        'MoM%': '4.35%',
        '綜合評分': '85.5'
    }
    
    print("🏆 排行榜資料項目範例:")
    for key, value in sample_data.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ 西元年月格式: {year_month} (來自日期: {test_date})")
    print("✅ 營收數據格式: 千分位逗號分隔")
    print("✅ 百分比格式: 保留兩位小數")
    
    return True

if __name__ == "__main__":
    print("🚀 開始測試月營收排行榜新欄位...")
    
    # 測試新欄位
    success1 = test_monthly_revenue_columns()
    
    # 測試資料格式
    success2 = test_data_format()
    
    if success1 and success2:
        print("\n🎉 所有測試通過！月營收排行榜新欄位功能正常。")
    else:
        print("\n❌ 部分測試失敗，請檢查相關功能。")
