#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試ROE下載器的年份選擇功能
基於用戶提供的GoodInfo截圖
"""

import sys
import os
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

def show_available_years():
    """顯示GoodInfo網站上可用的年份選項"""
    print("\n📋 檢查GoodInfo網站可用年份...")
    print("-" * 40)
    
    downloader = GoodinfoROECSVDownloader()
    
    try:
        # 啟動瀏覽器
        driver = downloader.setup_driver()
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw2/StockList.asp?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
        driver.get(url)
        
        # 等待頁面載入
        import time
        time.sleep(3)
        
        # 查找年份選擇器
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import Select
        
        year_selectors = [
            "select:has(option[value='2024'])",
            "select:has(option[value='2023'])",
            "select:has(option[value='2022'])",
            "select",
        ]
        
        for selector in year_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.tag_name == 'select':
                        select = Select(element)
                        options = [option.text.strip() for option in select.options]
                        
                        # 檢查是否包含年份選項
                        year_options = [opt for opt in options if any(str(y) in opt for y in range(2005, 2030))]
                        
                        if year_options:
                            print(f"✅ 找到年份選擇器，可用選項:")
                            for i, option in enumerate(options[:15], 1):  # 只顯示前15個
                                print(f"   {i:2d}. {option}")
                            if len(options) > 15:
                                print(f"   ... 還有 {len(options)-15} 個選項")
                            
                            # 關閉瀏覽器
                            driver.quit()
                            return
                            
            except Exception as e:
                continue
        
        print("⚠️ 未找到年份選擇器")
        
        # 關閉瀏覽器
        driver.quit()
        
    except Exception as e:
        print(f"❌ 檢查年份選項時發生錯誤: {e}")
        try:
            driver.quit()
        except:
            pass

def test_year_selection():
    """測試不同年份的選擇"""
    print("\n🧪 測試ROE下載器年份選擇功能")
    print("=" * 50)
    
    # 測試年份列表（基於用戶截圖）
    test_years = [
        2024,  # 當前年份
        2023,  # 去年
        2020,  # 較早年份
        2025,  # 未來年份（應該使用預設）
        2010,  # 很早的年份
    ]
    
    downloader = GoodinfoROECSVDownloader()
    
    for year in test_years:
        print(f"\n📅 測試年份: {year}")
        print("-" * 30)
        
        try:
            # 只測試年份選擇，不實際下載
            success = test_year_selection_only(downloader, year)
            
            if success:
                print(f"✅ 年份 {year} 選擇成功")
            else:
                print(f"⚠️ 年份 {year} 選擇失敗，將使用預設年份")
                
        except Exception as e:
            print(f"❌ 年份 {year} 測試出錯: {e}")
    
    print(f"\n🏁 年份選擇測試完成")

def test_year_selection_only(downloader, year):
    """只測試年份選擇，不下載數據"""
    try:
        # 啟動瀏覽器
        driver = downloader.setup_driver()
        
        # 訪問頁面
        url = "https://goodinfo.tw/tw2/StockList.asp?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
        driver.get(url)
        
        # 等待頁面載入
        import time
        time.sleep(3)
        
        # 嘗試選擇年份
        success = downloader.select_year(driver, year)
        
        # 關閉瀏覽器
        driver.quit()
        
        return success
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def main():
    """主函數"""
    print("🔍 GoodInfo ROE年份選擇測試工具")
    print("=" * 50)
    
    # 顯示可用年份
    show_available_years()
    
    # 測試年份選擇
    test_year_selection()
    
    print(f"\n💡 測試建議:")
    print(f"   • 根據用戶截圖，GoodInfo支援2024-2005年的ROE數據")
    print(f"   • 建議在GUI中提供這些年份選項")
    print(f"   • 對於不支援的年份，系統會自動使用最新資料")

if __name__ == "__main__":
    main()
