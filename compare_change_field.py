#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較 price.db 和 newprice.db 中 Change 欄位的差異
"""

import sqlite3
import pandas as pd

def compare_change_field():
    """比較兩個資料庫中 Change 欄位的差異"""
    
    print("=" * 80)
    print("🔍 比較 price.db 和 newprice.db 中 Change 欄位的差異")
    print("=" * 80)
    
    # 資料庫路徑
    price_db = r'D:\Finlab\history\tables\price.db'
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        # 檢查 price.db 中的 Change 欄位
        print("\n📊 price.db 中的 Change 欄位:")
        conn_price = sqlite3.connect(price_db)
        
        # 檢查 0050 的 Change 值
        query_price = '''
            SELECT date, [Open], [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        df_price = pd.read_sql_query(query_price, conn_price)
        
        print("   0050 最近10天的 Change 值:")
        for _, row in df_price.iterrows():
            print(f"   {row['date']}: Open={row['Open']}, Close={row['Close']}, Change={row['Change']}")
        
        # 統計 Change 欄位的分布
        query_stats_price = '''
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_change,
                COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_change,
                COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_change,
                AVG([Change]) as avg_change,
                MIN([Change]) as min_change,
                MAX([Change]) as max_change
            FROM stock_daily_data
            WHERE date >= '2023-09-01' AND date <= '2023-09-20'
        '''
        stats_price = pd.read_sql_query(query_stats_price, conn_price)
        
        print(f"\n   統計 (2023-09-01 到 2023-09-20):")
        print(f"   總記錄數: {stats_price.iloc[0]['total_records']:,}")
        print(f"   Change = 0: {stats_price.iloc[0]['zero_change']:,}")
        print(f"   Change > 0: {stats_price.iloc[0]['positive_change']:,}")
        print(f"   Change < 0: {stats_price.iloc[0]['negative_change']:,}")
        print(f"   平均 Change: {stats_price.iloc[0]['avg_change']:.4f}")
        print(f"   最小 Change: {stats_price.iloc[0]['min_change']:.4f}")
        print(f"   最大 Change: {stats_price.iloc[0]['max_change']:.4f}")
        
        conn_price.close()
        
        # 檢查 newprice.db 中的 Change 欄位
        print("\n📊 newprice.db 中的 Change 欄位:")
        conn_newprice = sqlite3.connect(newprice_db)
        
        # 檢查 0050 的 Change 值
        query_newprice = '''
            SELECT date, [Open], [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        df_newprice = pd.read_sql_query(query_newprice, conn_newprice)
        
        print("   0050 最近10天的 Change 值:")
        for _, row in df_newprice.iterrows():
            print(f"   {row['date']}: Open={row['Open']}, Close={row['Close']}, Change={row['Change']}")
        
        # 統計 Change 欄位的分布
        query_stats_newprice = '''
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_change,
                COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_change,
                COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_change,
                AVG([Change]) as avg_change,
                MIN([Change]) as min_change,
                MAX([Change]) as max_change
            FROM stock_daily_data
            WHERE date >= '2023-09-01' AND date <= '2023-09-20'
        '''
        stats_newprice = pd.read_sql_query(query_stats_newprice, conn_newprice)
        
        print(f"\n   統計 (2023-09-01 到 2023-09-20):")
        print(f"   總記錄數: {stats_newprice.iloc[0]['total_records']:,}")
        print(f"   Change = 0: {stats_newprice.iloc[0]['zero_change']:,}")
        print(f"   Change > 0: {stats_newprice.iloc[0]['positive_change']:,}")
        print(f"   Change < 0: {stats_newprice.iloc[0]['negative_change']:,}")
        print(f"   平均 Change: {stats_newprice.iloc[0]['avg_change']:.4f}")
        print(f"   最小 Change: {stats_newprice.iloc[0]['min_change']:.4f}")
        print(f"   最大 Change: {stats_newprice.iloc[0]['max_change']:.4f}")
        
        conn_newprice.close()
        
        # 分析差異
        print(f"\n🔍 差異分析:")
        
        price_zero_pct = (stats_price.iloc[0]['zero_change'] / stats_price.iloc[0]['total_records']) * 100
        newprice_zero_pct = (stats_newprice.iloc[0]['zero_change'] / stats_newprice.iloc[0]['total_records']) * 100
        
        print(f"   price.db 中 Change=0 的比例: {price_zero_pct:.2f}%")
        print(f"   newprice.db 中 Change=0 的比例: {newprice_zero_pct:.2f}%")
        
        if newprice_zero_pct > 90:
            print(f"   ❌ newprice.db 中大部分 Change 值為 0，需要修正")
            print(f"   💡 建議：修正爬蟲或存儲邏輯來計算正確的 Change 值")
        else:
            print(f"   ✅ Change 值分布正常")
        
        # 檢查原始爬蟲資料是否包含漲跌資訊
        print(f"\n🔍 檢查原始爬蟲資料:")
        print(f"   需要檢查爬蟲是否能獲取漲跌價差資訊")
        
    except Exception as e:
        print(f"❌ 比較失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    compare_change_field()
