#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試表格改進效果
"""

import sys
import pandas as pd
from datetime import datetime
import logging

def test_table_improvements():
    """測試表格改進效果"""
    
    print("🎨 測試表格改進效果")
    print("=" * 60)
    
    # 1. 測試數據收集邏輯
    print("1️⃣ 測試數據收集邏輯...")
    
    try:
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
        
        strategy = HighYieldTurtleStrategyOptimized()
        print("   ✅ 策略導入成功")
        
        # 創建測試數據
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        prices = [100 * (1.005 ** i) for i in range(100)]
        test_df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': [100000] * 100
        }, index=dates)
        
        # 測試幾個股票
        test_stocks = [
            ('2330', '台積電'),
            ('2881', '富邦金'),
            ('2882', '國泰金'),
            ('1101', '台泥'),
            ('1108', '幸福')
        ]
        
        print(f"   📊 測試股票數據收集:")
        print(f"   {'股票代碼':<8} {'股票名稱':<8} {'評分':<6} {'殖利率':<8} {'本益比':<8} {'股價淨值比':<10}")
        print(f"   {'-'*8} {'-'*8} {'-'*6} {'-'*8} {'-'*8} {'-'*10}")
        
        results = []
        
        for stock_id, stock_name in test_stocks:
            try:
                result = strategy.analyze_stock(test_df, stock_id=stock_id)
                details = result.get('details', {})
                
                score = result['score']
                dividend_yield = details.get('dividend_yield', 0)
                pe_ratio = details.get('pe_ratio', 0)
                pb_ratio = details.get('pb_ratio', 0)
                volume = details.get('volume', 0)
                
                # 模擬GUI的數據處理
                gui_result = {
                    "股票代碼": stock_id,
                    "股票名稱": stock_name,
                    "收盤價": test_df.iloc[-1]['Close'],
                    "烏龜評分": score,
                    "殖利率": f"{dividend_yield:.2f}%",
                    "本益比": pe_ratio,
                    "股價淨值比": pb_ratio,
                    "成交量": volume,
                    "策略狀態": "🐢 符合" if result['suitable'] else "不符合"
                }
                
                results.append(gui_result)
                
                print(f"   {stock_id:<8} {stock_name:<8} {score:<6} {dividend_yield:.2f}%{'':<3} {pe_ratio:<8.2f} {pb_ratio:<10.2f}")
                
            except Exception as e:
                print(f"   {stock_id:<8} {stock_name:<8} 錯誤: {e}")
        
        print(f"   ✅ 成功收集 {len(results)} 支股票的數據")
        
    except Exception as e:
        print(f"   ❌ 數據收集測試失敗: {e}")
        return False
    
    # 2. 測試配色邏輯
    print(f"\n2️⃣ 測試配色邏輯...")
    
    def get_color_description(value, thresholds, labels):
        """獲取顏色描述"""
        if value >= thresholds[0]:
            return labels[0]  # 紅色
        elif value >= thresholds[1]:
            return labels[1]  # 橙色
        else:
            return labels[2]  # 綠色
    
    print(f"   📊 配色規則測試:")
    print(f"   {'指標':<12} {'數值':<10} {'配色':<15} {'說明'}")
    print(f"   {'-'*12} {'-'*10} {'-'*15} {'-'*20}")
    
    # 測試各種數值的配色
    test_cases = [
        ("烏龜評分", [90, 70, 50], [80, 60], ["高分(紅)", "中分(橙)", "低分(綠)"]),
        ("殖利率(%)", [8.5, 4.2, 1.8], [6, 3], ["高殖利率(紅)", "中殖利率(橙)", "低殖利率(綠)"]),
        ("本益比", [12, 20, 35], [15, 25], ["低本益比(紅)", "中本益比(橙)", "高本益比(綠)"]),
        ("股價淨值比", [1.2, 2.5, 4.0], [1.5, 3], ["低淨值比(紅)", "中淨值比(橙)", "高淨值比(綠)"]),
        ("成交量(千張)", [1500, 200, 50], [1000, 100], ["大量(紅)", "中量(橙)", "小量(綠)"])
    ]
    
    for metric, values, thresholds, labels in test_cases:
        for value in values:
            color_desc = get_color_description(value, thresholds, labels)
            print(f"   {metric:<12} {value:<10} {color_desc:<15}")
    
    print(f"   ✅ 配色邏輯測試完成")
    
    # 3. 測試表格結構
    print(f"\n3️⃣ 測試表格結構...")
    
    expected_columns = [
        "股票代碼", "股票名稱", "收盤價", "烏龜評分", "殖利率",
        "本益比", "股價淨值比", "成交量", "策略狀態"
    ]
    
    print(f"   📋 新表格結構 (共{len(expected_columns)}列):")
    for i, col in enumerate(expected_columns):
        print(f"      {i+1}. {col}")
    
    print(f"   ✅ 移除了N/A字段：營收成長、營業利益率、董監持股")
    print(f"   ✅ 保留了有實際數據的字段：本益比、股價淨值比")
    
    # 4. 模擬表格顯示效果
    print(f"\n4️⃣ 模擬表格顯示效果...")
    
    print(f"   📊 表格數據預覽:")
    header = f"   {'股票代碼':<8} {'股票名稱':<8} {'收盤價':<8} {'評分':<6} {'殖利率':<8} {'本益比':<8} {'淨值比':<8} {'成交量':<10} {'狀態':<8}"
    print(header)
    print(f"   {'-'*8} {'-'*8} {'-'*8} {'-'*6} {'-'*8} {'-'*8} {'-'*8} {'-'*10} {'-'*8}")
    
    for result in results:
        stock_id = result["股票代碼"]
        stock_name = result["股票名稱"]
        close_price = result["收盤價"]
        score = result["烏龜評分"]
        dividend = result["殖利率"]
        pe_ratio = result["本益比"]
        pb_ratio = result["股價淨值比"]
        volume = result["成交量"]
        status = result["策略狀態"]
        
        # 添加顏色標記
        score_color = "🔴" if score >= 80 else "🟠" if score >= 60 else "🟢"
        dividend_value = float(dividend.replace("%", ""))
        dividend_color = "🔴" if dividend_value >= 6 else "🟠" if dividend_value >= 3 else "🟢"
        pe_color = "🔴" if pe_ratio <= 15 else "🟠" if pe_ratio <= 25 else "🟢"
        pb_color = "🔴" if pb_ratio <= 1.5 else "🟠" if pb_ratio <= 3 else "🟢"
        volume_k = volume/1000
        volume_color = "🔴" if volume_k >= 1000 else "🟠" if volume_k >= 100 else "🟢"
        status_color = "🔴" if "符合" in status else "🟢"
        
        print(f"   {stock_id:<8} {stock_name:<8} {close_price:<8.2f} {score_color}{score:<5} {dividend_color}{dividend:<7} {pe_color}{pe_ratio:<7.2f} {pb_color}{pb_ratio:<7.2f} {volume_color}{volume_k:<9.0f}張 {status_color}{status:<7}")
    
    print(f"\n   🎨 顏色說明:")
    print(f"      🔴 紅色: 標準以上/優秀")
    print(f"      🟠 橙色: 中等水準")
    print(f"      🟢 綠色: 低於標準/需改善")
    
    print(f"\n" + "=" * 60)
    print(f"✅ 表格改進測試完成！")
    
    return True

if __name__ == "__main__":
    success = test_table_improvements()
    
    if success:
        print(f"\n🎉 改進效果總結:")
        print(f"✅ 移除了N/A字段，只顯示有實際數據的列")
        print(f"✅ 改用字體顏色而不是背景色，視覺效果更清晰")
        print(f"✅ 紅色表示標準以上，綠色表示低於標準")
        print(f"✅ 表格結構更簡潔，信息更有價值")
        print(f"")
        print(f"🚀 現在請重新啟動GUI程序查看改進效果:")
        print(f"1. 關閉當前的GUI程序")
        print(f"2. 重新運行 python O3mh_gui_v21_optimized.py")
        print(f"3. 選擇「高殖利率烏龜策略」並執行篩選")
        print(f"4. 右側表格現在會顯示:")
        print(f"   - 更簡潔的列結構（移除N/A字段）")
        print(f"   - 清晰的字體顏色標示")
        print(f"   - 有意義的實際數據")
    else:
        print(f"\n❌ 測試失敗，需要進一步檢查")
