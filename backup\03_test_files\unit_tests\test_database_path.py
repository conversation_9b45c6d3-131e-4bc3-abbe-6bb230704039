"""
測試每日交易資料庫路徑設定
確認資料庫存放在正確的位置：D:\Finlab\history\tables\
"""

import sys
import os

def test_database_path():
    """測試資料庫路徑設定"""
    print("🔍 測試每日交易資料庫路徑設定")
    print("=" * 50)
    
    try:
        from taiwan_stock_price_crawler import DailyTradingDatabase
        
        # 初始化資料庫
        db = DailyTradingDatabase()
        
        print(f"📍 資料庫完整路徑: {db.db_path}")
        
        # 檢查路徑是否正確
        expected_path = r"D:\Finlab\history\tables\daily_trading.db"
        if db.db_path == expected_path:
            print("✅ 資料庫路徑設定正確")
        else:
            print(f"❌ 路徑不符，期望: {expected_path}")
            return False
        
        # 檢查目錄是否存在
        db_dir = os.path.dirname(db.db_path)
        if os.path.exists(db_dir):
            print(f"✅ 資料庫目錄存在: {db_dir}")
        else:
            print(f"❌ 資料庫目錄不存在: {db_dir}")
            return False
        
        # 檢查與price.db的關係
        price_db_path = os.path.join(db_dir, "price.db")
        if os.path.exists(price_db_path):
            print("✅ 確認與price.db在同一目錄")
            
            # 顯示目錄中的資料庫檔案
            db_files = [f for f in os.listdir(db_dir) if f.endswith('.db')]
            print(f"\n📁 目錄中的資料庫檔案:")
            for db_file in db_files:
                file_path = os.path.join(db_dir, db_file)
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"  📄 {db_file} ({file_size:,} bytes)")
        else:
            print("⚠️ price.db不在此目錄，但路徑設定正確")
        
        # 測試資料庫功能
        count = db.get_data_count()
        print(f"\n📊 資料庫統計: {count} 筆資料")
        
        print("\n✅ 資料庫路徑測試完全通過！")
        print("🎯 daily_trading.db 已正確設定在與 price.db 相同的目錄")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_database_path():
    """測試GUI模組的資料庫路徑"""
    print("\n🔍 測試GUI模組資料庫路徑")
    print("=" * 50)
    
    try:
        import tkinter as tk
        from daily_trading_crawler_gui import DailyTradingCrawlerGUI
        
        # 創建隱藏的根視窗
        root = tk.Tk()
        root.withdraw()
        
        # 初始化GUI
        app = DailyTradingCrawlerGUI()
        
        print(f"📍 GUI使用的資料庫路徑: {app.db.db_path}")
        
        # 檢查路徑是否正確
        expected_path = r"D:\Finlab\history\tables\daily_trading.db"
        if app.db.db_path == expected_path:
            print("✅ GUI資料庫路徑設定正確")
        else:
            print(f"❌ GUI路徑不符，期望: {expected_path}")
            root.destroy()
            return False
        
        # 測試統計功能
        app.update_stats()
        print("✅ GUI統計功能正常")
        
        root.destroy()
        print("✅ GUI資料庫路徑測試通過")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 開始資料庫路徑驗證測試")
    print("🎯 目標：確認資料庫存放在 D:\\Finlab\\history\\tables\\")
    print("=" * 60)
    
    tests = [
        ("核心模組資料庫路徑", test_database_path),
        ("GUI模組資料庫路徑", test_gui_database_path),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 測試未通過")
        except Exception as e:
            print(f"❌ {test_name} 測試發生錯誤: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有路徑測試通過！")
        print("✅ daily_trading.db 已正確設定在與 price.db 相同的目錄")
        print("📁 資料庫位置: D:\\Finlab\\history\\tables\\daily_trading.db")
        print("\n📋 使用說明:")
        print("1. 啟動主程式後，點擊 🕷️ 爬蟲 → 📊 每日交易資料")
        print("2. 爬取的資料將自動儲存到指定目錄")
        print("3. 可與現有的 price.db 一起進行統一管理")
    else:
        print("⚠️ 部分測試未通過，請檢查路徑設定")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
