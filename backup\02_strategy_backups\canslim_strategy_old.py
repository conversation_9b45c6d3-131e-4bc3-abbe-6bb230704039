#!/usr/bin/env python3
"""
CANSLIM策略模組 - 威廉·歐尼爾經典選股法
完整實現量價齊升核心邏輯
"""
import logging
import pandas as pd
import numpy as np
from .base_strategy import BaseStrategy


class CANSLIMStrategy(BaseStrategy):
    """CANSLIM量價齊升策略 - 威廉·歐尼爾經典選股法"""

    def __init__(self):
        super().__init__(
            name="CANSLIM量價齊升策略",
            description="威廉·歐尼爾經典選股法：高成交量、高成交金額、股價上漲的成長股",
            strategy_type="growth_momentum"
        )

        # 策略參數
        self.min_volume_ratio = 1.5  # 最小成交量倍數
        self.min_price_change = 0.02  # 最小漲幅2%
        self.min_amount = 10000000  # 最小成交金額1000萬
        self.lookback_days = 20  # 回看天數
        self.rps_period = 250  # RPS相對強度期間

        # 評分權重
        self.weights = {
            'current_earnings': 25,  # C - 當季盈餘
            'annual_earnings': 20,   # A - 年度盈餘
            'new_products': 15,      # N - 新產品/服務
            'supply_demand': 20,     # S - 供需關係(量價)
            'leader_laggard': 10,    # L - 領導股vs落後股
            'institutional': 5,      # I - 機構認同度
            'market_direction': 5    # M - 市場方向
        }

    def check_current_earnings(self, financial_data=None):
        """檢查當前盈利 (C - Current Earnings)"""
        try:
            if financial_data is None or 'EPS成長率' not in str(financial_data):
                return False, "❌ 缺少EPS成長率數據"

            # 模擬檢查邏輯
            # if 'EPS成長率' in financial_data:
            #     eps_growth = financial_data['EPS成長率'].iloc[-1]
            #     if eps_growth >= self.min_eps_growth:
            #         return True, f"EPS成長率 {eps_growth:.1%} >= {self.min_eps_growth:.1%}"
            #     else:
            #         return False, f"EPS成長率 {eps_growth:.1%} < {self.min_eps_growth:.1%}"

            return False, "需要整合財務數據以檢查EPS成長率"

        except Exception as e:
            return False, f"當前盈利檢查失敗: {str(e)}"

    def check_annual_earnings(self, financial_data=None):
        """檢查年度盈利 (A - Annual Earnings)"""
        try:
            if financial_data is None or 'ROE' not in str(financial_data):
                return False, "❌ 缺少ROE數據"

            # 模擬檢查邏輯
            # if 'ROE' in financial_data:
            #     roe = financial_data['ROE'].iloc[-1]
            #     if roe >= self.min_roe:
            #         return True, f"ROE {roe:.1%} >= {self.min_roe:.1%}"
            #     else:
            #         return False, f"ROE {roe:.1%} < {self.min_roe:.1%}"

            return False, "需要整合財務數據以檢查ROE"

        except Exception as e:
            return False, f"年度盈利檢查失敗: {str(e)}"

    def check_new_products(self, market_data=None):
        """檢查新產品/服務 (N - New Products)"""
        try:
            # 這個條件需要基本面研究，無法從價格數據判斷
            return False, "❌ 新產品/服務條件需要基本面研究，無法從技術數據判斷"

        except Exception as e:
            return False, f"新產品檢查失敗: {str(e)}"

    def check_supply_demand(self, df):
        """檢查供需關係 (S - Supply and Demand)"""
        try:
            # 使用成交量作為供需指標
            volume_pass, volume_reason = self.check_volume_condition(df,
                                                                   min_volume=100000,
                                                                   volume_ratio=self.min_volume_ratio)

            if volume_pass:
                return True, f"供需良好: {volume_reason}"
            else:
                return False, f"供需不佳: {volume_reason}"

        except Exception as e:
            return False, f"供需檢查失敗: {str(e)}"

    def check_leader_laggard(self, df):
        """檢查領導股/落後股 (L - Leader or Laggard)"""
        try:
            # 使用價格相對強度作為指標
            if len(df) < 60:
                return False, "數據不足以判斷領導地位"

            # 計算60日價格表現
            price_60_ago = df['Close'].iloc[-60] if len(df) >= 60 else df['Close'].iloc[0]
            current_price = df['Close'].iloc[-1]

            performance = (current_price - price_60_ago) / price_60_ago

            if performance > 0.1:  # 60日漲幅超過10%
                return True, f"領導股特徵: 60日漲幅 {performance:.1%} > 10%"
            else:
                return False, f"非領導股: 60日漲幅 {performance:.1%} <= 10%"

        except Exception as e:
            return False, f"領導股檢查失敗: {str(e)}"

    def check_institutional_sponsorship(self, market_data=None):
        """檢查機構認同 (I - Institutional Sponsorship)"""
        try:
            # 這個條件需要機構持股數據，無法從價格數據判斷
            return False, "❌ 機構認同條件需要機構持股數據，無法從技術數據判斷"

        except Exception as e:
            return False, f"機構認同檢查失敗: {str(e)}"

    def check_market_direction(self, df):
        """檢查市場方向 (M - Market Direction)"""
        try:
            # 使用移動平均線判斷趨勢
            trend_pass, trend_reason = self.check_price_trend(df, ma_period=20)

            if trend_pass:
                return True, f"市場方向良好: {trend_reason}"
            else:
                return False, f"市場方向不佳: {trend_reason}"

        except Exception as e:
            return False, f"市場方向檢查失敗: {str(e)}"

    def analyze_stock(self, df, financial_data=None, market_data=None, **kwargs):
        """分析單一股票是否符合CANSLIM策略"""
        try:
            if df.empty or len(df) < 60:
                return {
                    'suitable': False,
                    'reason': '數據不足，需要至少60日數據',
                    'details': {},
                    'score': 0,
                    'strategy_name': self.name
                }

            results = {}
            total_score = 0
            max_score = 7  # CANSLIM七個條件
            missing_data_warnings = []

            # C - Current Earnings (當前盈利)
            c_pass, c_reason = self.check_current_earnings(financial_data)
            results['當前盈利(C)'] = {'pass': c_pass, 'reason': c_reason}
            if c_pass:
                total_score += 1
            elif "缺少EPS成長率數據" in c_reason:
                missing_data_warnings.append("EPS數據")

            # A - Annual Earnings (年度盈利)
            a_pass, a_reason = self.check_annual_earnings(financial_data)
            results['年度盈利(A)'] = {'pass': a_pass, 'reason': a_reason}
            if a_pass:
                total_score += 1
            elif "缺少ROE數據" in a_reason:
                missing_data_warnings.append("ROE數據")

            # N - New Products (新產品)
            n_pass, n_reason = self.check_new_products(market_data)
            results['新產品(N)'] = {'pass': n_pass, 'reason': n_reason}
            if n_pass:
                total_score += 1
            elif "需要基本面研究" in n_reason:
                missing_data_warnings.append("新產品資訊")

            # S - Supply and Demand (供需)
            s_pass, s_reason = self.check_supply_demand(df)
            results['供需關係(S)'] = {'pass': s_pass, 'reason': s_reason}
            if s_pass:
                total_score += 1

            # L - Leader or Laggard (領導股)
            l_pass, l_reason = self.check_leader_laggard(df)
            results['領導股(L)'] = {'pass': l_pass, 'reason': l_reason}
            if l_pass:
                total_score += 1

            # I - Institutional Sponsorship (機構認同)
            i_pass, i_reason = self.check_institutional_sponsorship(market_data)
            results['機構認同(I)'] = {'pass': i_pass, 'reason': i_reason}
            if i_pass:
                total_score += 1
            elif "需要機構持股數據" in i_reason:
                missing_data_warnings.append("機構持股數據")

            # M - Market Direction (市場方向)
            m_pass, m_reason = self.check_market_direction(df)
            results['市場方向(M)'] = {'pass': m_pass, 'reason': m_reason}
            if m_pass:
                total_score += 1

            # 判斷是否符合條件（由於缺少關鍵財務數據，降低門檻）
            suitable = total_score >= 3  # 至少通過3個條件

            # 生成總結
            passed_items = [k for k, v in results.items() if v['pass']]

            # 構建警告信息
            warning_msg = ""
            if missing_data_warnings:
                warning_msg = f" | ⚠️ 缺少關鍵數據: {', '.join(missing_data_warnings)}"

            reason = f"CANSLIM評分: {total_score}/{max_score}{warning_msg}"

            if passed_items:
                reason += f" | 通過項目: {', '.join(passed_items)}"

            if not suitable and missing_data_warnings:
                reason += " | 🚨 警告：由於缺少財務和基本面數據，此策略無法正確執行"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'strategy_name': self.name,
                'missing_data': missing_data_warnings
            }

        except Exception as e:
            logging.error(f"CANSLIM策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name,
                'missing_data': ['所有財務數據']
            }