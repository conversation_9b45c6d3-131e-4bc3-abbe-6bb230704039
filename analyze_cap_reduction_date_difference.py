#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 TWSE 和 OTC 減資資料日期差異的原因
"""

import sqlite3
import pandas as pd
import os
import datetime

def analyze_cap_reduction_date_difference():
    """分析減資資料日期差異的原因"""
    
    print("=" * 80)
    print("🔍 分析 TWSE vs OTC 減資資料日期差異")
    print("=" * 80)
    
    twse_db = 'D:/Finlab/history/tables/twse_cap_reduction.db'
    otc_db = 'D:/Finlab/history/tables/otc_cap_reduction.db'
    
    print("📊 1. 詳細年度分布比較")
    print("-" * 50)
    
    # 分析 TWSE 資料
    if os.path.exists(twse_db):
        try:
            conn = sqlite3.connect(twse_db)
            cursor = conn.cursor()
            
            print("🏛️ TWSE (上市) 減資年度分布:")
            cursor.execute("""
                SELECT strftime('%Y', date) as year, COUNT(*) as count 
                FROM twse_cap_reduction 
                GROUP BY year 
                ORDER BY year DESC
            """)
            twse_years = cursor.fetchall()
            
            for year, count in twse_years:
                print(f"   {year}: {count} 次")
            
            # 檢查最近的減資案例
            print(f"\n📋 TWSE 最近 10 筆減資案例:")
            cursor.execute("""
                SELECT stock_id, date, 減資原因, 恢復買賣日期
                FROM twse_cap_reduction 
                ORDER BY date DESC 
                LIMIT 10
            """)
            recent_twse = cursor.fetchall()
            
            for stock_id, date, reason, resume_date in recent_twse:
                print(f"   {date[:10]} - {stock_id} ({reason}) - 恢復: {resume_date}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ TWSE 分析失敗: {e}")
    
    # 分析 OTC 資料
    if os.path.exists(otc_db):
        try:
            conn = sqlite3.connect(otc_db)
            cursor = conn.cursor()
            
            print(f"\n🏪 OTC (上櫃) 減資年度分布:")
            cursor.execute("""
                SELECT strftime('%Y', date) as year, COUNT(*) as count 
                FROM otc_cap_reduction 
                GROUP BY year 
                ORDER BY year DESC
            """)
            otc_years = cursor.fetchall()
            
            for year, count in otc_years:
                print(f"   {year}: {count} 次")
            
            # 檢查最近的減資案例
            print(f"\n📋 OTC 最近 10 筆減資案例:")
            cursor.execute("""
                SELECT stock_id, date, 減資源因, 恢復買賣日期
                FROM otc_cap_reduction 
                ORDER BY date DESC 
                LIMIT 10
            """)
            recent_otc = cursor.fetchall()
            
            for stock_id, date, reason, resume_date in recent_otc:
                print(f"   {date[:10]} - {stock_id} ({reason}) - 恢復: {resume_date}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ OTC 分析失敗: {e}")
    
    print(f"\n📊 2. 資料來源分析")
    print("-" * 50)
    
    print("🔍 檢查原始 PKL 檔案:")
    
    # 檢查原始 PKL 檔案
    twse_pkl = 'history/tables/twse_cap_reduction.pkl'
    otc_pkl = 'history/tables/otc_cap_reduction.pkl'
    
    if os.path.exists(twse_pkl):
        try:
            twse_df = pd.read_pickle(twse_pkl)
            print(f"📂 TWSE PKL 檔案:")
            print(f"   檔案: {twse_pkl}")
            print(f"   記錄數: {len(twse_df)} 筆")
            
            if hasattr(twse_df.index, 'get_level_values'):
                dates = twse_df.index.get_level_values('date')
                print(f"   日期範圍: {dates.min().strftime('%Y-%m-%d')} ~ {dates.max().strftime('%Y-%m-%d')}")
                
                # 年度分布
                years = dates.year.value_counts().sort_index(ascending=False)
                print(f"   年度分布:")
                for year, count in years.head(10).items():
                    print(f"     {year}: {count} 次")
            
        except Exception as e:
            print(f"❌ TWSE PKL 讀取失敗: {e}")
    else:
        print(f"❌ TWSE PKL 檔案不存在: {twse_pkl}")
    
    if os.path.exists(otc_pkl):
        try:
            otc_df = pd.read_pickle(otc_pkl)
            print(f"\n📂 OTC PKL 檔案:")
            print(f"   檔案: {otc_pkl}")
            print(f"   記錄數: {len(otc_df)} 筆")
            
            if hasattr(otc_df.index, 'get_level_values'):
                dates = otc_df.index.get_level_values('date')
                print(f"   日期範圍: {dates.min().strftime('%Y-%m-%d')} ~ {dates.max().strftime('%Y-%m-%d')}")
                
                # 年度分布
                years = dates.year.value_counts().sort_index(ascending=False)
                print(f"   年度分布:")
                for year, count in years.head(10).items():
                    print(f"     {year}: {count} 次")
            
        except Exception as e:
            print(f"❌ OTC PKL 讀取失敗: {e}")
    else:
        print(f"❌ OTC PKL 檔案不存在: {otc_pkl}")
    
    print(f"\n📊 3. API 狀態分析")
    print("-" * 50)
    
    # 測試 TWSE API
    print("🔗 TWSE API 測試:")
    try:
        import requests
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 測試最近30天
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=30)
        
        start_datestr = start_date.strftime('%Y%m%d')
        end_datestr = end_date.strftime('%Y%m%d')
        
        twse_url = f"https://www.twse.com.tw/rwd/zh/reducation/TWTAUU?response=csv&startDate={start_datestr}&endDate={end_datestr}&_=1551597854043"
        
        response = requests.get(twse_url, timeout=10, verify=False)
        print(f"   狀態碼: {response.status_code}")
        print(f"   回應長度: {len(response.text)} 字元")
        
        if response.status_code == 200:
            # 檢查是否有資料
            if 'csv' in response.headers.get('Content-Type', '').lower() or response.text.count('\n') > 1:
                lines = response.text.strip().split('\n')
                print(f"   CSV 行數: {len(lines)}")
                if len(lines) > 2:  # 標題行 + 至少一行資料
                    print(f"   ✅ 有新資料")
                else:
                    print(f"   ℹ️ 無新資料 (正常)")
            else:
                print(f"   ⚠️ 非 CSV 格式回應")
        
    except Exception as e:
        print(f"   ❌ TWSE API 測試失敗: {e}")
    
    # 測試 OTC API
    print(f"\n🔗 OTC API 測試:")
    try:
        # 測試最近30天 (民國年格式)
        start_year = start_date.year - 1911
        start_month = start_date.month
        start_day = start_date.day
        start_datestr_roc = f"{start_year:02d}/{start_month:02d}/{start_day:02d}"
        
        end_year = end_date.year - 1911
        end_month = end_date.month
        end_day = end_date.day
        end_datestr_roc = f"{end_year:02d}/{end_month:02d}/{end_day:02d}"
        
        otc_url = f"https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php?l=zh-tw&d={start_datestr_roc}&ed={end_datestr_roc}"
        
        response = requests.get(otc_url, timeout=10, verify=False)
        print(f"   狀態碼: {response.status_code}")
        print(f"   回應長度: {len(response.text)} 字元")
        print(f"   Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        if response.status_code == 200:
            if response.text.startswith('<!DOCTYPE'):
                print(f"   ❌ 返回 HTML 頁面 (可能是 404)")
            else:
                print(f"   ✅ 可能有效的回應")
        
    except Exception as e:
        print(f"   ❌ OTC API 測試失敗: {e}")
    
    print(f"\n📊 4. 結論分析")
    print("-" * 50)
    
    print("🎯 可能的原因:")
    print("1. 📈 市場活動差異:")
    print("   - 上市公司 (TWSE) 減資案例較多且持續")
    print("   - 上櫃公司 (OTC) 減資案例較少，近年可能沒有新案例")
    
    print("\n2. 🔗 API 狀態差異:")
    print("   - TWSE API 正常運作，能獲取最新資料")
    print("   - OTC API 可能已停用或端點改變")
    
    print("\n3. 📊 資料更新頻率:")
    print("   - TWSE 資料持續更新到 2025 年")
    print("   - OTC 資料可能在 2022 年後停止更新")
    
    print("\n4. 🏛️ 監管政策:")
    print("   - 上市和上櫃公司的減資規定可能不同")
    print("   - 資料公開的方式和頻率可能有差異")
    
    print(f"\n✅ 分析完成！")

if __name__ == "__main__":
    analyze_cap_reduction_date_difference()
