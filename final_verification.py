#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終驗證：確認所有修改都正確工作
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'finlab'))

def final_verification():
    """最終驗證所有修改"""
    print("=" * 80)
    print("🔍 最終驗證：Price 爬蟲修改完成檢查")
    print("=" * 80)
    
    verification_results = []
    
    # 1. 檢查資料庫檔案
    print("1️⃣ 檢查資料庫檔案...")
    price_db_path = 'D:/Finlab/history/tables/price.db'
    newprice_db_path = 'D:/Finlab/history/tables/newprice.db'
    
    if os.path.exists(price_db_path):
        print("   ✅ price.db 存在")
        verification_results.append("✅ price.db 檔案存在")
    else:
        print("   ❌ price.db 不存在")
        verification_results.append("❌ price.db 檔案不存在")
    
    if not os.path.exists(newprice_db_path):
        print("   ✅ newprice.db 不存在（已正確改名）")
        verification_results.append("✅ newprice.db 已正確改名")
    else:
        print("   ⚠️ newprice.db 仍存在")
        verification_results.append("⚠️ newprice.db 仍存在")
    
    # 2. 檢查資料庫內容
    print("\n2️⃣ 檢查資料庫內容...")
    if os.path.exists(price_db_path):
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 檢查總記錄數
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_records = cursor.fetchone()[0]
            print(f"   📊 總記錄數: {total_records:,}")
            
            # 檢查股票數
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
            unique_stocks = cursor.fetchone()[0]
            print(f"   📊 股票數: {unique_stocks:,}")
            
            # 檢查日期範圍
            cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data")
            date_range = cursor.fetchone()
            print(f"   📊 日期範圍: {date_range[0]} ~ {date_range[1]}")
            
            # 檢查市場別分布
            cursor.execute("""
                SELECT listing_status, COUNT(DISTINCT stock_id) as stock_count
                FROM stock_daily_data 
                GROUP BY listing_status 
                ORDER BY stock_count DESC
            """)
            market_stats = cursor.fetchall()
            
            print(f"   📊 市場別分布:")
            for status, count in market_stats:
                status_name = status if status else 'ETF/未分類'
                print(f"      {status_name}: {count:,} 檔")
            
            # 關鍵檢查：興櫃股票
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '興櫃'")
            rotc_count = cursor.fetchone()[0]
            
            if rotc_count == 0:
                print("   ✅ 無興櫃股票記錄")
                verification_results.append("✅ 資料庫中無興櫃股票")
            else:
                print(f"   ❌ 仍有 {rotc_count:,} 筆興櫃股票記錄")
                verification_results.append(f"❌ 資料庫中仍有 {rotc_count:,} 筆興櫃股票")
            
            # 關鍵檢查：權證
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
            warrant_count = cursor.fetchone()[0]
            
            if warrant_count == 0:
                print("   ✅ 無權證記錄")
                verification_results.append("✅ 資料庫中無權證")
            else:
                print(f"   ❌ 仍有 {warrant_count:,} 筆權證記錄")
                verification_results.append(f"❌ 資料庫中仍有 {warrant_count:,} 筆權證")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ 資料庫檢查失敗: {e}")
            verification_results.append("❌ 資料庫檢查失敗")
    
    # 3. 檢查爬蟲過濾功能
    print("\n3️⃣ 檢查爬蟲過濾功能...")
    try:
        from crawler import crawl_price
        
        # 使用一個已知的交易日測試
        test_date = datetime(2024, 12, 30)
        print(f"   測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        df = crawl_price(test_date)
        
        if not df.empty:
            df_reset = df.reset_index()
            
            if 'listing_status' in df_reset.columns:
                # 檢查是否有興櫃股票
                rotc_in_crawl = len(df_reset[df_reset['listing_status'] == '興櫃'])
                
                if rotc_in_crawl == 0:
                    print("   ✅ 爬蟲成功過濾興櫃股票")
                    verification_results.append("✅ 爬蟲過濾興櫃股票功能正常")
                else:
                    print(f"   ❌ 爬蟲仍返回 {rotc_in_crawl} 檔興櫃股票")
                    verification_results.append(f"❌ 爬蟲仍返回興櫃股票")
                
                # 檢查市場別分布
                listing_stats = df_reset['listing_status'].value_counts()
                print(f"   📊 爬取資料市場別:")
                for status, count in listing_stats.items():
                    status_name = status if status else 'ETF/未分類'
                    print(f"      {status_name}: {count} 檔")
            else:
                print("   ⚠️ 爬取資料中沒有 listing_status 欄位")
                verification_results.append("⚠️ 爬取資料缺少 listing_status 欄位")
        else:
            print("   ⚠️ 爬蟲返回空資料（可能是非交易日）")
            verification_results.append("⚠️ 爬蟲測試返回空資料")
            
    except Exception as e:
        print(f"   ❌ 爬蟲測試失敗: {e}")
        verification_results.append("❌ 爬蟲測試失敗")
    
    # 4. 檢查存檔路徑功能
    print("\n4️⃣ 檢查存檔路徑功能...")
    try:
        from auto_update import get_newprice_incremental_date_range
        
        date_range = get_newprice_incremental_date_range()
        
        if date_range is not None:
            if isinstance(date_range, list) and len(date_range) == 0:
                print("   ✅ 正確檢測到資料庫已是最新")
                verification_results.append("✅ 增量更新功能正常")
            else:
                print(f"   ✅ 正確獲取增量更新範圍: {len(date_range)} 天")
                verification_results.append("✅ 增量更新功能正常")
        else:
            print("   ❌ 增量更新功能異常")
            verification_results.append("❌ 增量更新功能異常")
            
    except Exception as e:
        print(f"   ❌ 存檔路徑測試失敗: {e}")
        verification_results.append("❌ 存檔路徑測試失敗")
    
    # 5. 總結
    print("\n" + "=" * 80)
    print("📋 驗證結果總結")
    print("=" * 80)
    
    success_count = 0
    total_count = len(verification_results)
    
    for i, result in enumerate(verification_results, 1):
        print(f"{i:2d}. {result}")
        if result.startswith("✅"):
            success_count += 1
    
    print(f"\n📊 驗證統計:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 所有驗證項目都通過！Price 爬蟲修改完成。")
        print(f"✅ 興櫃股票已成功移除")
        print(f"✅ 存檔路徑已改為 price.db")
        print(f"✅ 所有功能正常運作")
    else:
        print(f"\n⚠️ 有 {total_count - success_count} 個項目需要注意")
    
    return success_count == total_count

if __name__ == "__main__":
    final_verification()
