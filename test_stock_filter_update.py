#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股票篩選器更新功能
"""

import pandas as pd
import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_stock_filter_module():
    """測試股票篩選器模組"""
    print("🧪 測試股票篩選器模組")
    print("=" * 50)
    
    try:
        from stock_filter import StockFilter
        
        # 創建篩選器
        stock_filter = StockFilter()
        
        # 測試股票清單（包含需要被過濾的股票）
        test_stocks = [
            '2330',    # 台積電 - 應該保留
            '2317',    # 鴻海 - 應該保留
            '2881C',   # 富邦金權證 - 應該被過濾
            '2891C',   # 中信金權證 - 應該被過濾
            '2454',    # 聯發科 - 應該保留
            '00878',   # 國泰永續高股息ETF - 應該保留
            '00631L',  # 元大台灣50正2 - 應該保留
            '2330A',   # 台積電權證 - 應該被過濾
            '030001',  # 權證 - 應該被過濾
            '020011',  # ETN - 應該被過濾
        ]
        
        print("📋 測試股票清單:")
        for stock in test_stocks:
            is_valid = stock_filter.is_valid_stock_code(stock)
            status = "✅ 保留" if is_valid else "❌ 過濾"
            print(f"  {stock}: {status}")
        
        # 批量篩選測試
        print(f"\n📊 批量篩選測試:")
        filtered_stocks = stock_filter.filter_stock_list(test_stocks)
        print(f"原始股票: {test_stocks}")
        print(f"篩選後: {filtered_stocks}")
        
        # 檢查特定的權證是否被正確過濾
        print(f"\n🎯 重點檢查:")
        critical_tests = ['2881C', '2891C', '2330A']
        for stock in critical_tests:
            is_valid = stock_filter.is_valid_stock_code(stock)
            if not is_valid:
                print(f"✅ {stock} 已被正確過濾")
            else:
                print(f"❌ {stock} 未被過濾（錯誤）")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票篩選器測試失敗: {e}")
        return False

def test_main_program_filter():
    """測試主程序的篩選邏輯"""
    print(f"\n🧪 測試主程序篩選邏輯")
    print("=" * 50)
    
    # 模擬主程序的篩選邏輯
    test_data = pd.DataFrame({
        'stock_id': [
            '2330',    # 台積電 - 應該保留
            '2317',    # 鴻海 - 應該保留
            '2881C',   # 富邦金權證 - 應該被過濾
            '2891C',   # 中信金權證 - 應該被過濾
            '2454',    # 聯發科 - 應該保留
            '00878',   # 國泰永續高股息ETF - 應該保留
            '00631L',  # 元大台灣50正2 - 應該保留
            '2330A',   # 台積電權證 - 應該被過濾
            '12345',   # 5碼股票 - 應該保留
            '030001',  # 權證 - 應該被過濾
        ],
        'stock_name': [
            '台積電', '鴻海', '富邦金C', '中信金C', '聯發科',
            '國泰永續高股息', '元大台灣50正2', '台積電A', '測試股票', '權證'
        ]
    })
    
    print(f"📊 原始資料: {len(test_data)} 筆")
    print("原始股票清單:")
    for _, row in test_data.iterrows():
        print(f"  {row['stock_id']} - {row['stock_name']}")
    
    # 應用主程序的篩選邏輯
    original_count = len(test_data)
    
    # 1. 保留4碼純數字股票
    condition1 = test_data['stock_id'].str.match(r'^[0-9]{4}$')
    # 2. 保留5碼純數字股票
    condition2 = test_data['stock_id'].str.match(r'^[0-9]{5}$')
    # 3. 保留00開頭的ETF（5碼或6碼，可含字母後綴）
    condition3 = test_data['stock_id'].str.match(r'^00[0-9]{3,4}[A-Z]*$')
    # 4. 排除4碼數字+1個英文字母的權證（如2881C、2891C），但保留ETF
    exclude_condition = test_data['stock_id'].str.match(r'^[1-9][0-9]{3}[A-Z]$')  # 只排除非00開頭的4碼+字母
    
    # 應用篩選條件
    filtered_df = test_data[(condition1 | condition2 | condition3) & ~exclude_condition]
    
    print(f"\n📊 篩選後資料: {len(filtered_df)} 筆")
    print("篩選後股票清單:")
    for _, row in filtered_df.iterrows():
        print(f"  {row['stock_id']} - {row['stock_name']}")
    
    # 檢查被過濾的股票
    filtered_out = test_data[~test_data['stock_id'].isin(filtered_df['stock_id'])]
    print(f"\n🗑️ 被過濾的股票: {len(filtered_out)} 筆")
    for _, row in filtered_out.iterrows():
        print(f"  {row['stock_id']} - {row['stock_name']}")
    
    # 驗證關鍵股票是否被正確處理
    print(f"\n🎯 關鍵驗證:")
    
    # 檢查2881C和2891C是否被過濾
    if '2881C' not in filtered_df['stock_id'].values:
        print("✅ 2881C 已被正確過濾")
    else:
        print("❌ 2881C 未被過濾（錯誤）")
    
    if '2891C' not in filtered_df['stock_id'].values:
        print("✅ 2891C 已被正確過濾")
    else:
        print("❌ 2891C 未被過濾（錯誤）")
    
    # 檢查正常股票是否被保留
    if '2330' in filtered_df['stock_id'].values:
        print("✅ 2330 (台積電) 已被正確保留")
    else:
        print("❌ 2330 (台積電) 被錯誤過濾")
    
    if '00878' in filtered_df['stock_id'].values:
        print("✅ 00878 (ETF) 已被正確保留")
    else:
        print("❌ 00878 (ETF) 被錯誤過濾")
    
    return len(filtered_out) > 0  # 如果有股票被過濾，說明篩選功能正常

def main():
    """主函數"""
    print("🚀 股票篩選器更新功能測試")
    print("=" * 60)
    
    # 測試1: 股票篩選器模組
    test1_result = test_stock_filter_module()
    
    # 測試2: 主程序篩選邏輯
    test2_result = test_main_program_filter()
    
    print(f"\n📋 測試總結:")
    print(f"✅ 股票篩選器模組測試: {'通過' if test1_result else '失敗'}")
    print(f"✅ 主程序篩選邏輯測試: {'通過' if test2_result else '失敗'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有測試通過！")
        print(f"💡 修復效果:")
        print(f"   • 2881C、2891C 等四碼+英文字母的權證已被正確過濾")
        print(f"   • 正常股票（如2330、2317）仍被正確保留")
        print(f"   • ETF（如00878、00631L）仍被正確保留")
        print(f"   • 股票清單中不再顯示「未分類/未分類」的權證")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查配置")

if __name__ == "__main__":
    main()
