#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試新檔案策略
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def quick_test():
    """快速測試"""
    print("🧪 快速測試新檔案策略...")
    
    try:
        from crawler import to_pickle
        
        # 創建小量測試資料
        test_data = pd.DataFrame({
            'close': [100, 101],
            'volume': [1000, 1100]
        })
        test_data['date'] = pd.to_datetime(['2022-12-01', '2022-12-02'])
        test_data['stock_id'] = ['TEST1', 'TEST2']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 測試存檔
        print("執行 to_pickle...")
        to_pickle(test_data, 'price')
        print("✅ 存檔完成")
        
        # 檢查結果
        price_v2_file = "history/tables/price_v2.pkl"
        if os.path.exists(price_v2_file):
            data = pd.read_pickle(price_v2_file)
            print(f"✅ price_v2.pkl 創建成功，{len(data)} 筆資料")
            
            # 檢查最新日期
            latest_date = data.index.get_level_values('date').max()
            print(f"   最新日期: {latest_date}")
            
            return True
        else:
            print("❌ price_v2.pkl 未創建")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("=" * 50)
    print("🚀 快速修復測試")
    print("=" * 50)
    
    success = quick_test()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 修復成功！")
        print("\n✨ 現在的策略:")
        print("   • price 表格使用 price_v2.pkl")
        print("   • 避免舊檔案的兼容性問題")
        print("   • 分批存檔正常工作")
        
        print("\n🚀 建議:")
        print("   • 重新啟動 auto_update.py")
        print("   • 不會再有 numpy 錯誤")
        print("   • 每10筆正常存檔")
    else:
        print("❌ 還需要進一步檢查")
    
    return success

if __name__ == "__main__":
    main()
