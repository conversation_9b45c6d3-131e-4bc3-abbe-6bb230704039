#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 Change 值的計算方式和含義
"""

import sqlite3
import pandas as pd

def analyze_change_calculation():
    """分析 Change 值的計算方式和含義"""
    
    print("=" * 80)
    print("🔍 分析 Change 值的計算方式和含義")
    print("=" * 80)
    
    price_db = r'D:\Finlab\history\tables\price.db'
    
    try:
        conn = sqlite3.connect(price_db)
        
        # 檢查 0050 的連續幾天資料來分析 Change 的計算方式
        print("\n📊 分析 0050 的 Change 計算方式:")
        
        query_0050 = '''
            SELECT date, [Open], [High], [Low], [Close], [Change]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 20
        '''
        
        df_0050 = pd.read_sql_query(query_0050, conn)
        
        print("   最近20天的價格和 Change 值:")
        print("   日期        開盤    最高    最低    收盤    Change")
        print("   " + "-" * 55)
        
        for _, row in df_0050.iterrows():
            print(f"   {row['date']} {row['Open']:6.2f} {row['High']:6.2f} {row['Low']:6.2f} {row['Close']:6.2f} {row['Change']:7.2f}")
        
        # 計算前一日收盤價來驗證 Change 的計算方式
        print(f"\n🔍 驗證 Change 計算方式:")
        
        # 按日期排序（從舊到新）
        df_sorted = df_0050.sort_values('date')
        
        print("   日期        收盤    前日收盤  實際Change  資料庫Change  差異")
        print("   " + "-" * 70)
        
        for i in range(1, min(10, len(df_sorted))):
            current = df_sorted.iloc[i]
            previous = df_sorted.iloc[i-1]
            
            current_close = current['Close']
            previous_close = previous['Close']
            actual_change = current_close - previous_close
            db_change = current['Change']
            difference = abs(actual_change - db_change)
            
            print(f"   {current['date']} {current_close:6.2f} {previous_close:8.2f} {actual_change:10.2f} {db_change:12.2f} {difference:6.3f}")
        
        # 檢查不同股票的 Change 值範圍
        print(f"\n📊 不同股票的 Change 值統計:")
        
        query_stats = '''
            SELECT 
                stock_id,
                AVG([Close]) as avg_close,
                AVG([Change]) as avg_change,
                MIN([Change]) as min_change,
                MAX([Change]) as max_change,
                COUNT(*) as records
            FROM stock_daily_data 
            WHERE date >= '2025-07-01' AND date <= '2025-07-25'
            GROUP BY stock_id
            HAVING records >= 10
            ORDER BY avg_close DESC
            LIMIT 15
        '''
        
        stats = pd.read_sql_query(query_stats, conn)
        
        print("   股票代碼  平均收盤價  平均Change  最小Change  最大Change  記錄數")
        print("   " + "-" * 65)
        
        for _, row in stats.iterrows():
            print(f"   {row['stock_id']:>6} {row['avg_close']:10.2f} {row['avg_change']:10.2f} {row['min_change']:10.2f} {row['max_change']:10.2f} {row['records']:6.0f}")
        
        # 檢查 Change 值與收盤價的關係
        print(f"\n🔍 分析 Change 值的性質:")
        
        # 檢查是否為絕對值（價差）還是百分比
        query_analysis = '''
            SELECT 
                [Close],
                [Change],
                CASE 
                    WHEN [Close] > 0 THEN ([Change] / [Close]) * 100 
                    ELSE 0 
                END as change_percentage
            FROM stock_daily_data 
            WHERE stock_id = '0050' AND date >= '2025-07-01'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        analysis = pd.read_sql_query(query_analysis, conn)
        
        print("   收盤價    Change   Change/收盤價*100")
        print("   " + "-" * 35)
        
        for _, row in analysis.iterrows():
            print(f"   {row['Close']:6.2f} {row['Change']:8.2f} {row['change_percentage']:13.2f}%")
        
        # 檢查高價股和低價股的 Change 值
        print(f"\n📊 高價股 vs 低價股的 Change 值:")
        
        query_price_comparison = '''
            SELECT 
                CASE 
                    WHEN AVG([Close]) >= 100 THEN '高價股(>=100)'
                    WHEN AVG([Close]) >= 50 THEN '中價股(50-100)'
                    ELSE '低價股(<50)'
                END as price_category,
                COUNT(*) as stock_count,
                AVG([Close]) as avg_close,
                AVG(ABS([Change])) as avg_abs_change,
                AVG(ABS([Change]) / [Close] * 100) as avg_change_pct
            FROM stock_daily_data 
            WHERE date >= '2025-07-01' AND [Close] > 0
            GROUP BY 
                CASE 
                    WHEN AVG([Close]) >= 100 THEN '高價股(>=100)'
                    WHEN AVG([Close]) >= 50 THEN '中價股(50-100)'
                    ELSE '低價股(<50)'
                END
            ORDER BY avg_close DESC
        '''
        
        price_comparison = pd.read_sql_query(query_price_comparison, conn)
        
        print("   價格分類      股票數  平均收盤價  平均|Change|  平均變動%")
        print("   " + "-" * 55)
        
        for _, row in price_comparison.iterrows():
            print(f"   {row['price_category']:12} {row['stock_count']:6.0f} {row['avg_close']:10.2f} {row['avg_abs_change']:11.2f} {row['avg_change_pct']:9.2f}%")
        
        conn.close()
        
        # 總結分析
        print(f"\n📋 Change 值分析總結:")
        print(f"   1. Change 值的單位：絕對價差（新台幣）")
        print(f"   2. 計算方式：今日收盤價 - 昨日收盤價")
        print(f"   3. 正值表示上漲，負值表示下跌")
        print(f"   4. 不是百分比，而是實際的價格差異")
        print(f"   5. 高價股的 Change 絕對值通常較大")
        print(f"   6. 低價股的 Change 絕對值通常較小")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_change_calculation()
