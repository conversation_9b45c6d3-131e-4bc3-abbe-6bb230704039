#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併 price.pkl 和 price_v2.pkl 檔案
以 price_v2.pkl 的資料為準，覆蓋相同日期的資料
"""

import pandas as pd
import os
import shutil
from datetime import datetime
import gc

def merge_price_files():
    """合併 price.pkl 和 price_v2.pkl 檔案"""
    
    print("=" * 60)
    print("🔄 開始合併 price.pkl 和 price_v2.pkl")
    print("=" * 60)
    
    # 檔案路徑
    price_old_file = "history/tables/price.pkl"
    price_new_file = "history/tables/price_v2.pkl"
    backup_file = "history/tables/price_backup_before_merge.pkl"
    
    # 檢查檔案是否存在
    if not os.path.exists(price_old_file):
        print(f"❌ {price_old_file} 不存在")
        return False
    
    if not os.path.exists(price_new_file):
        print(f"❌ {price_new_file} 不存在")
        return False
    
    try:
        # 1. 創建備份
        print("1️⃣ 創建備份...")
        shutil.copy2(price_old_file, backup_file)
        print(f"✅ 備份創建成功: {backup_file}")
        
        # 2. 讀取舊檔案
        print("2️⃣ 讀取舊檔案 price.pkl...")
        try:
            old_data = pd.read_pickle(price_old_file)
            print(f"✅ 舊檔案讀取成功: {len(old_data):,} 筆資料")
            
            old_date_range = old_data.index.get_level_values('date')
            print(f"   日期範圍: {old_date_range.min()} 至 {old_date_range.max()}")
            
        except Exception as e:
            print(f"❌ 舊檔案讀取失敗: {str(e)}")
            print("🔄 嘗試其他方法...")
            
            # 如果讀取失敗，只使用新檔案
            print("⚠️ 由於舊檔案讀取失敗，將只使用新檔案資料")
            new_data = pd.read_pickle(price_new_file)
            new_data.to_pickle(price_old_file)
            print("✅ 已將新檔案資料複製到 price.pkl")
            return True
        
        # 3. 讀取新檔案
        print("3️⃣ 讀取新檔案 price_v2.pkl...")
        new_data = pd.read_pickle(price_new_file)
        print(f"✅ 新檔案讀取成功: {len(new_data):,} 筆資料")
        
        new_date_range = new_data.index.get_level_values('date')
        print(f"   日期範圍: {new_date_range.min()} 至 {new_date_range.max()}")
        
        # 4. 找出重疊的日期
        print("4️⃣ 分析日期重疊...")
        old_dates = set(old_data.index.get_level_values('date'))
        new_dates = set(new_data.index.get_level_values('date'))
        overlap_dates = old_dates.intersection(new_dates)
        
        print(f"   舊檔案日期數: {len(old_dates)}")
        print(f"   新檔案日期數: {len(new_dates)}")
        print(f"   重疊日期數: {len(overlap_dates)}")
        
        if overlap_dates:
            print(f"   重疊日期範圍: {min(overlap_dates)} 至 {max(overlap_dates)}")
        
        # 5. 合併資料
        print("5️⃣ 合併資料...")
        
        # 移除舊資料中與新資料重疊的日期
        if overlap_dates:
            print(f"   移除舊資料中的 {len(overlap_dates)} 個重疊日期...")
            old_data_filtered = old_data[~old_data.index.get_level_values('date').isin(overlap_dates)]
            print(f"   過濾後舊資料: {len(old_data_filtered):,} 筆")
        else:
            old_data_filtered = old_data
            print("   沒有重疊日期，保留所有舊資料")
        
        # 合併資料
        print("   合併新舊資料...")
        merged_data = pd.concat([old_data_filtered, new_data])
        print(f"✅ 合併完成: {len(merged_data):,} 筆資料")
        
        # 清理記憶體
        del old_data, new_data, old_data_filtered
        gc.collect()
        
        # 6. 去除重複並排序
        print("6️⃣ 去除重複並排序...")
        print("   去除重複...")
        merged_data = merged_data[~merged_data.index.duplicated(keep='last')]
        print(f"   去重後: {len(merged_data):,} 筆資料")
        
        print("   排序索引...")
        merged_data = merged_data.sort_index()
        
        final_date_range = merged_data.index.get_level_values('date')
        print(f"✅ 最終資料日期範圍: {final_date_range.min()} 至 {final_date_range.max()}")
        
        # 7. 保存合併後的資料
        print("7️⃣ 保存合併後的資料...")
        merged_data.to_pickle(price_old_file)
        print(f"✅ 合併資料已保存到: {price_old_file}")
        
        # 8. 驗證合併結果
        print("8️⃣ 驗證合併結果...")
        verify_data = pd.read_pickle(price_old_file)
        verify_date_range = verify_data.index.get_level_values('date')
        
        print(f"✅ 驗證成功:")
        print(f"   總筆數: {len(verify_data):,}")
        print(f"   日期範圍: {verify_date_range.min()} 至 {verify_date_range.max()}")
        print(f"   檔案大小: {os.path.getsize(price_old_file):,} bytes")
        
        # 9. 清理
        del merged_data, verify_data
        gc.collect()
        
        print("\n" + "=" * 60)
        print("🎉 合併完成！")
        print("=" * 60)
        print("📊 結果摘要:")
        print(f"   • 合併檔案: {price_old_file}")
        print(f"   • 備份檔案: {backup_file}")
        print(f"   • 新檔案資料優先覆蓋重疊日期")
        print(f"   • 建議保留 {price_new_file} 作為參考")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併過程發生錯誤: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        
        # 如果出錯，恢復備份
        if os.path.exists(backup_file):
            print("🔄 恢復備份檔案...")
            shutil.copy2(backup_file, price_old_file)
            print("✅ 備份已恢復")
        
        return False

def check_files_before_merge():
    """合併前檢查檔案狀態"""
    print("🔍 檢查檔案狀態...")
    
    files_to_check = [
        "history/tables/price.pkl",
        "history/tables/price_v2.pkl"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            mtime = os.path.getmtime(file_path)
            print(f"   {file_path}:")
            print(f"     大小: {size:,} bytes ({size/1024/1024:.1f} MB)")
            print(f"     修改時間: {datetime.fromtimestamp(mtime)}")
            
            try:
                data = pd.read_pickle(file_path)
                date_range = data.index.get_level_values('date')
                print(f"     資料: {len(data):,} 筆")
                print(f"     日期: {date_range.min()} 至 {date_range.max()}")
            except Exception as e:
                print(f"     ❌ 讀取失敗: {str(e)[:50]}...")
        else:
            print(f"   {file_path}: ❌ 不存在")
        print()

def main():
    """主函數"""
    print("🔄 Price 檔案合併工具")
    print("=" * 60)
    
    # 檢查檔案狀態
    check_files_before_merge()
    
    # 確認操作
    print("⚠️ 注意事項:")
    print("   • 此操作會修改 price.pkl")
    print("   • 會自動創建備份檔案")
    print("   • 相同日期以 price_v2.pkl 為準")
    print("   • 建議在非運行時間執行")
    
    response = input("\n是否繼續合併？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    # 執行合併
    success = merge_price_files()
    
    if success:
        print("\n💡 後續建議:")
        print("   • 檢查合併後的 price.pkl 檔案")
        print("   • 可以保留 price_v2.pkl 作為參考")
        print("   • 備份檔案可在確認無誤後刪除")
        print("   • 重新啟動 auto_update.py 時會使用合併後的檔案")
    else:
        print("\n❌ 合併失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
