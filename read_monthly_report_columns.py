#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讀取 monthly_report.pkl 檔案並確認欄位名稱
"""

import pandas as pd
import datetime
import os

def read_monthly_report_columns():
    """讀取 monthly_report.pkl 並檢查欄位"""
    file_path = "history/tables/monthly_report.pkl"
    
    print(f"📖 讀取 monthly_report.pkl 欄位資訊")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"📁 檔案資訊:")
        print(f"   路徑: {file_path}")
        print(f"   大小: {file_size:.1f} MB")
        print(f"   修改時間: {file_mtime}")
        
        # 讀取資料
        print(f"\n📖 讀取資料...")
        df = pd.read_pickle(file_path)
        
        print(f"✅ 讀取成功:")
        print(f"   總筆數: {len(df):,}")
        print(f"   資料類型: {type(df)}")
        
        # 索引資訊
        print(f"\n🔍 索引資訊:")
        print(f"   索引類型: {type(df.index)}")
        print(f"   索引名稱: {df.index.names}")
        
        if hasattr(df.index, 'levels'):
            print(f"   索引層級數: {df.index.nlevels}")
            for i, level_name in enumerate(df.index.names):
                level_values = df.index.get_level_values(i)
                unique_count = len(level_values.unique())
                print(f"   層級 {i} ({level_name}): {unique_count} 個唯一值")
        
        # 欄位資訊
        print(f"\n📊 欄位資訊:")
        print(f"   欄位數量: {len(df.columns)}")
        print(f"   欄位名稱:")
        
        for i, col in enumerate(df.columns, 1):
            # 檢查欄位的資料類型和非空值數量
            non_null_count = df[col].count()
            data_type = df[col].dtype
            
            print(f"   {i:2d}. '{col}'")
            print(f"       - 資料類型: {data_type}")
            print(f"       - 非空值: {non_null_count:,} / {len(df):,} ({non_null_count/len(df)*100:.1f}%)")
            
            # 如果是數值類型，顯示統計資訊
            if pd.api.types.is_numeric_dtype(df[col]):
                try:
                    stats = df[col].describe()
                    print(f"       - 範圍: {stats['min']:,.2f} ~ {stats['max']:,.2f}")
                    print(f"       - 平均: {stats['mean']:,.2f}")
                except:
                    pass
            
            # 如果是字串類型，顯示樣本
            elif pd.api.types.is_object_dtype(df[col]):
                try:
                    unique_values = df[col].dropna().unique()
                    if len(unique_values) <= 10:
                        print(f"       - 唯一值: {list(unique_values)}")
                    else:
                        print(f"       - 唯一值數量: {len(unique_values)}")
                        print(f"       - 樣本: {list(unique_values[:5])}...")
                except:
                    pass
            
            print()  # 空行分隔
        
        # 顯示前幾筆資料
        print(f"📋 前5筆資料預覽:")
        print("=" * 80)
        
        # 選擇要顯示的欄位 (避免輸出太寬)
        display_columns = df.columns[:5] if len(df.columns) > 5 else df.columns
        sample_data = df[display_columns].head()
        
        print(sample_data)
        
        if len(df.columns) > 5:
            print(f"\n... 還有 {len(df.columns) - 5} 個欄位未顯示")
        
        # 檢查最新資料
        print(f"\n📅 最新資料檢查:")
        try:
            if 'date' in df.index.names:
                dates = df.index.get_level_values('date')
                latest_date = max(dates)
                latest_data = df[df.index.get_level_values('date') == latest_date]
                
                print(f"   最新日期: {latest_date}")
                print(f"   該日期筆數: {len(latest_data):,}")
                
                if len(latest_data) > 0:
                    print(f"   最新資料樣本:")
                    print(latest_data[display_columns].head(3))
        except Exception as e:
            print(f"   ⚠️ 最新資料檢查失敗: {str(e)}")
        
        print(f"\n🎉 欄位檢查完成！")
        return df.columns.tolist()
        
    except Exception as e:
        print(f"❌ 讀取失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return None

def compare_with_expected_columns():
    """比較實際欄位與預期欄位"""
    print(f"\n🔍 欄位比較分析")
    print("=" * 60)
    
    # 預期的月營收欄位 (基於我們的爬蟲)
    expected_columns = [
        '當月營收',
        '上月營收', 
        '去年當月營收',
        '上月比較增減(%)',
        '去年同月增減(%)',
        '當月累計營收',
        '去年累計營收',
        '前期比較增減(%)',
        '備註'
    ]
    
    print(f"📋 預期欄位 (基於爬蟲設計):")
    for i, col in enumerate(expected_columns, 1):
        print(f"   {i}. '{col}'")
    
    # 讀取實際欄位
    actual_columns = read_monthly_report_columns()
    
    if actual_columns:
        print(f"\n🔍 欄位比較:")
        
        # 找出相同的欄位
        common_columns = set(expected_columns) & set(actual_columns)
        print(f"✅ 相同欄位 ({len(common_columns)}):")
        for col in sorted(common_columns):
            print(f"   - '{col}'")
        
        # 找出缺少的欄位
        missing_columns = set(expected_columns) - set(actual_columns)
        if missing_columns:
            print(f"\n⚠️ 缺少欄位 ({len(missing_columns)}):")
            for col in sorted(missing_columns):
                print(f"   - '{col}'")
        
        # 找出額外的欄位
        extra_columns = set(actual_columns) - set(expected_columns)
        if extra_columns:
            print(f"\n➕ 額外欄位 ({len(extra_columns)}):")
            for col in sorted(extra_columns):
                print(f"   - '{col}'")
        
        # 總結
        print(f"\n📊 總結:")
        print(f"   預期欄位: {len(expected_columns)}")
        print(f"   實際欄位: {len(actual_columns)}")
        print(f"   相同欄位: {len(common_columns)}")
        print(f"   匹配率: {len(common_columns)/len(expected_columns)*100:.1f}%")

if __name__ == "__main__":
    print("📖 Monthly Report 欄位檢查工具")
    print("=" * 70)
    print("🎯 目標: 確認 monthly_report.pkl 的欄位名稱")
    print("=" * 70)
    
    # 比較欄位
    compare_with_expected_columns()
