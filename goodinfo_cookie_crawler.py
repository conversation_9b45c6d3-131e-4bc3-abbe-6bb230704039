#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 Cookie 方法爬取 GoodInfo 除權息資料
參考：https://colab.research.google.com/github/ywchiu/largitdata/blob/master/code/Course_244.ipynb
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
from datetime import datetime
import urllib.parse

class GoodInfoCookieCrawler:
    def __init__(self):
        # 設定基本的 headers 和 cookies
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
        }
        
        # 建立 session
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 初始化 cookies
        self.init_cookies()
    
    def init_cookies(self):
        """初始化 cookies - 先訪問首頁獲取基本 cookies"""
        print("🍪 初始化 cookies...")
        
        try:
            # 訪問首頁
            response = self.session.get("https://goodinfo.tw/tw/index.asp", timeout=30)
            print(f"   首頁回應狀態: {response.status_code}")
            
            # 設定一些基本的 cookies
            basic_cookies = {
                'IS_TOUCH_DEVICE': 'F',
                'SCREEN_SIZE': 'WIDTH=1920&HEIGHT=1080',
                'CLIENT_ID': f'{datetime.now().strftime("%Y%m%d%H%M%S")}_{random.randint(100,999)}_{random.randint(100,255)}.{random.randint(100,255)}.{random.randint(100,255)}.{random.randint(100,255)}',
            }
            
            for name, value in basic_cookies.items():
                self.session.cookies.set(name, value)
            
            print("   ✅ Cookies 初始化完成")
            
        except Exception as e:
            print(f"   ❌ Cookies 初始化失敗: {e}")
    
    def get_dividend_schedule_list(self, year="即將除權息"):
        """
        獲取除權息時間表
        
        Args:
            year: 年份，可以是具體年份如"2024"或"即將除權息"
        
        Returns:
            DataFrame: 除權息資料
        """
        print(f"🕷️ 爬取 GoodInfo 除權息資料 ({year})...")
        
        # 建構URL
        base_url = "https://goodinfo.tw/tw/StockDividendScheduleList.asp"
        params = {
            'MARKET_CAT': '全部',
            'INDUSTRY_CAT': '全部', 
            'YEAR': year
        }
        
        url = f"{base_url}?" + urllib.parse.urlencode(params, encoding='utf-8')
        print(f"📊 目標URL: {url}")
        
        try:
            # 隨機延遲
            time.sleep(random.uniform(1, 3))
            
            # 發送請求
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            
            print(f"📡 回應狀態碼: {response.status_code}")
            print(f"📄 回應內容長度: {len(response.text)}")
            
            # 儲存原始回應
            with open(f"goodinfo_dividend_{year}_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print(f"💾 已儲存原始回應到 goodinfo_dividend_{year}_response.html")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 方法1: 尋找特定的資料容器
            data_container = soup.select_one('#txtFinDetailData')
            if data_container:
                print("✅ 找到 #txtFinDetailData 容器")
                try:
                    dfs = pd.read_html(data_container.prettify())
                    if dfs:
                        print(f"✅ 成功解析 {len(dfs)} 個表格")
                        return dfs[0] if dfs else pd.DataFrame()
                except Exception as e:
                    print(f"❌ 解析 #txtFinDetailData 失敗: {e}")
            
            # 方法2: 尋找所有表格
            print("🔍 嘗試解析所有表格...")
            try:
                all_tables = pd.read_html(response.text)
                print(f"✅ 找到 {len(all_tables)} 個表格")
                
                # 分析每個表格
                for i, table in enumerate(all_tables):
                    print(f"\n📋 表格 {i+1}:")
                    print(f"   大小: {table.shape}")
                    print(f"   欄位: {list(table.columns)[:5]}...")
                    
                    # 檢查是否包含除權息相關欄位
                    columns_str = ' '.join(str(col) for col in table.columns)
                    if any(keyword in columns_str for keyword in ['除權', '除息', '股利', '股票', '代號']):
                        print(f"   ✅ 這可能是除權息資料表格")
                        return table
                
                # 如果沒有找到明確的除權息表格，返回最大的表格
                if all_tables:
                    largest_table = max(all_tables, key=lambda x: x.shape[0] * x.shape[1])
                    print(f"✅ 返回最大的表格 (大小: {largest_table.shape})")
                    return largest_table
                
            except Exception as e:
                print(f"❌ 解析表格失敗: {e}")
            
            # 方法3: 手動解析HTML表格
            print("🔍 嘗試手動解析HTML表格...")
            tables = soup.find_all('table')
            print(f"找到 {len(tables)} 個 <table> 標籤")
            
            for i, table in enumerate(tables):
                rows = table.find_all('tr')
                if len(rows) > 5:  # 假設資料表格至少有5行
                    print(f"\n📋 分析表格 {i+1} (共 {len(rows)} 行):")
                    
                    # 提取前幾行看看內容
                    sample_data = []
                    for j, row in enumerate(rows[:10]):
                        cells = row.find_all(['td', 'th'])
                        if cells:
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            sample_data.append(cell_texts)
                            print(f"   第{j+1}行: {cell_texts[:5]}...")
                    
                    # 檢查是否包含除權息相關內容
                    all_text = ' '.join(' '.join(row) for row in sample_data)
                    if any(keyword in all_text for keyword in ['除權', '除息', '股利', '股票代號']):
                        print(f"   ✅ 這可能是除權息資料表格，嘗試轉換為DataFrame")
                        
                        # 提取所有資料
                        all_data = []
                        for row in rows:
                            cells = row.find_all(['td', 'th'])
                            if cells:
                                cell_texts = [cell.get_text(strip=True) for cell in cells]
                                all_data.append(cell_texts)
                        
                        if all_data:
                            # 假設第一行是標題
                            if len(all_data) > 1:
                                df = pd.DataFrame(all_data[1:], columns=all_data[0])
                            else:
                                df = pd.DataFrame(all_data)
                            
                            print(f"   ✅ 成功建立DataFrame (大小: {df.shape})")
                            return df
            
            print("❌ 未找到合適的除權息資料")
            return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 爬取失敗: {e}")
            return pd.DataFrame()
    
    def get_stock_dividend_detail(self, stock_id):
        """
        獲取個股除權息詳細資料
        
        Args:
            stock_id: 股票代號
            
        Returns:
            DataFrame: 個股除權息資料
        """
        print(f"🔍 爬取股票 {stock_id} 的除權息資料...")
        
        url = f"https://goodinfo.tw/StockInfo/StockDividendPolicy.asp?STOCK_ID={stock_id}"
        
        try:
            # 隨機延遲
            time.sleep(random.uniform(1, 3))
            
            # 發送請求
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            
            print(f"   回應狀態: {response.status_code}")
            
            # 儲存回應
            with open(f"goodinfo_stock_{stock_id}_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尋找除權息資料表格
            try:
                tables = pd.read_html(response.text)
                if tables:
                    print(f"   ✅ 找到 {len(tables)} 個表格")
                    
                    # 返回最大的表格（通常是主要資料表格）
                    largest_table = max(tables, key=lambda x: x.shape[0] * x.shape[1])
                    print(f"   ✅ 返回最大表格 (大小: {largest_table.shape})")
                    return largest_table
                
            except Exception as e:
                print(f"   ❌ 解析表格失敗: {e}")
            
            return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 爬取股票 {stock_id} 失敗: {e}")
            return pd.DataFrame()

def test_goodinfo_cookie_crawler():
    """測試 GoodInfo Cookie 爬蟲"""
    print("🚀 測試 GoodInfo Cookie 爬蟲...")
    
    crawler = GoodInfoCookieCrawler()
    
    # 測試1: 爬取除權息時間表
    print("\n" + "="*60)
    print("📊 測試1: 爬取即將除權息清單")
    df = crawler.get_dividend_schedule_list("即將除權息")
    
    if not df.empty:
        print(f"✅ 成功獲取除權息資料，共 {len(df)} 筆")
        print("\n前5筆資料:")
        print(df.head().to_string())
        
        # 儲存結果
        filename = f"goodinfo_dividend_schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n💾 資料已儲存到: {filename}")
    else:
        print("❌ 未獲取到除權息資料")
    
    # 測試2: 爬取個股除權息資料
    print("\n" + "="*60)
    print("📊 測試2: 爬取個股除權息資料")
    test_stocks = [2330, 2317]  # 台積電、鴻海
    
    for stock_id in test_stocks:
        print(f"\n🔍 測試股票: {stock_id}")
        stock_df = crawler.get_stock_dividend_detail(stock_id)
        
        if not stock_df.empty:
            print(f"✅ 成功獲取股票 {stock_id} 資料，共 {len(stock_df)} 筆")
            print(f"前3筆資料:")
            print(stock_df.head(3).to_string())
            
            # 儲存結果
            filename = f"goodinfo_stock_{stock_id}_dividend_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            stock_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"💾 已儲存到: {filename}")
        else:
            print(f"❌ 未獲取到股票 {stock_id} 的資料")
        
        time.sleep(2)  # 避免請求太頻繁

if __name__ == "__main__":
    test_goodinfo_cookie_crawler()
