#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoodInfo 除權息資料爬蟲
參考：https://thisisyuwang.blogspot.com/2020/02/python-goodinfopbr.html
"""

import requests
import bs4 as bs
import pandas as pd
import numpy as np
import time
import random
from datetime import datetime
import urllib.parse

class GoodInfoDividendCrawler:
    def __init__(self):
        # 建立虛擬的Header User agent清單，防止IP被鎖
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1623.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
        ]

        # 建立session以保持連線
        self.session = requests.Session()

        # 設定更完整的headers
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def get_random_header(self):
        """獲取隨機User-Agent"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = self.user_agents[np.random.randint(0, len(self.user_agents))]
        return headers

    def make_request(self, url, method='GET', **kwargs):
        """統一的請求方法，包含反反爬蟲機制"""
        headers = self.get_random_header()

        # 隨機延遲
        time.sleep(random.uniform(1, 3))

        try:
            if method.upper() == 'POST':
                response = self.session.post(url, headers=headers, timeout=30, **kwargs)
            else:
                response = self.session.get(url, headers=headers, timeout=30, **kwargs)

            # 設定正確的編碼
            if 'charset' in response.headers.get('content-type', ''):
                response.encoding = response.apparent_encoding
            else:
                response.encoding = 'utf-8'

            return response

        except Exception as e:
            print(f"❌ 請求失敗: {e}")
            return None
    
    def crawl_dividend_schedule_list(self, year="即將除權息"):
        """
        爬取除權息時間表
        
        Args:
            year: 年份，可以是具體年份如"2024"或"即將除權息"
        
        Returns:
            DataFrame: 除權息資料
        """
        print(f"🕷️ 開始爬取 GoodInfo 除權息資料 ({year})...")
        
        # 建構URL
        base_url = "https://goodinfo.tw/tw/StockDividendScheduleList.asp"
        params = {
            'MARKET_CAT': '全部',
            'INDUSTRY_CAT': '全部', 
            'YEAR': year
        }
        
        # URL編碼
        url = f"{base_url}?" + urllib.parse.urlencode(params, encoding='utf-8')
        print(f"📊 目標URL: {url}")
        
        try:
            # 先訪問首頁建立session
            print("🌐 先訪問 GoodInfo 首頁建立 session...")
            home_response = self.make_request("https://goodinfo.tw/tw/index.asp")

            if home_response:
                print(f"   首頁回應狀態: {home_response.status_code}")
                time.sleep(2)

            # 發送主要請求
            response = self.make_request(url, method='POST')

            if not response:
                return pd.DataFrame()

            print(f"📡 回應狀態碼: {response.status_code}")
            print(f"📄 回應內容長度: {len(response.text)}")

            # 檢查是否需要等待JavaScript
            if "初始化中" in response.text or "請稍候" in response.text:
                print("⏳ 網站需要JavaScript初始化，嘗試等待...")
                time.sleep(5)

                # 重新請求
                response = self.make_request(url, method='POST')
                if not response:
                    return pd.DataFrame()
            
            # 解析HTML
            soup = bs.BeautifulSoup(response.content, "html.parser")
            
            # 尋找表格
            tables = soup.find_all("table")
            print(f"🔍 找到 {len(tables)} 個表格")
            
            if not tables:
                print("❌ 未找到表格資料")
                return pd.DataFrame()
            
            # 嘗試解析主要資料表格
            dividend_data = []
            
            for i, table in enumerate(tables):
                print(f"\n📋 分析第 {i+1} 個表格...")
                rows = table.find_all("tr")
                print(f"   找到 {len(rows)} 行")
                
                if len(rows) > 5:  # 假設主要資料表格會有較多行
                    print(f"   這可能是主要資料表格")
                    
                    # 分析表格結構
                    for j, row in enumerate(rows[:10]):  # 只看前10行
                        cells = row.find_all(["td", "th"])
                        if cells:
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            print(f"   第{j+1}行: {cell_texts[:5]}...")  # 只顯示前5個欄位
            
            # 儲存原始HTML以供分析
            with open("goodinfo_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("💾 已儲存原始HTML到 goodinfo_response.html")

            # 顯示前500字元以供調試
            print(f"📄 回應內容預覽:")
            print(response.text[:500])
            print("..." if len(response.text) > 500 else "")
            
            return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 爬取失敗: {e}")
            return pd.DataFrame()
    
    def crawl_stock_dividend_detail(self, stock_id):
        """
        爬取個股除權息詳細資料
        
        Args:
            stock_id: 股票代號
            
        Returns:
            dict: 除權息資料
        """
        print(f"🔍 爬取股票 {stock_id} 的除權息資料...")
        
        try:
            # 建構URL
            url = f"https://goodinfo.tw/StockInfo/StockDetail.asp?STOCK_ID={stock_id}"

            # 發送請求
            response = self.make_request(url, method='POST')

            if not response:
                return {}
            
            # 解析HTML
            soup = bs.BeautifulSoup(response.content, "html.parser")
            
            # 找出所有<td>標籤
            data = soup.find_all("td")
            
            print(f"   找到 {len(data)} 個 <td> 標籤")
            
            # 尋找除權息相關資料
            dividend_info = {}
            
            # 這裡需要根據實際HTML結構來定位除權息資料
            # 先儲存前200個td的內容以供分析
            td_contents = []
            for i, td in enumerate(data[:200]):
                text = td.get_text(strip=True)
                td_contents.append(f"td[{i}]: {text}")
            
            # 儲存分析結果
            with open(f"stock_{stock_id}_analysis.txt", "w", encoding="utf-8") as f:
                f.write("\n".join(td_contents))
            
            print(f"   已儲存分析結果到 stock_{stock_id}_analysis.txt")
            
            return dividend_info
            
        except Exception as e:
            print(f"❌ 爬取股票 {stock_id} 失敗: {e}")
            return {}

def test_goodinfo_crawler():
    """測試GoodInfo爬蟲"""
    print("🚀 測試 GoodInfo 除權息爬蟲...")
    
    crawler = GoodInfoDividendCrawler()
    
    # 測試1: 爬取除權息時間表
    print("\n" + "="*60)
    print("📊 測試1: 爬取即將除權息清單")
    df = crawler.crawl_dividend_schedule_list("即將除權息")
    
    # 測試2: 爬取個股資料
    print("\n" + "="*60)
    print("📊 測試2: 爬取個股除權息資料")
    test_stocks = [2330, 2317, 2454]  # 台積電、鴻海、聯發科
    
    for stock_id in test_stocks:
        info = crawler.crawl_stock_dividend_detail(stock_id)
        print(f"股票 {stock_id}: {info}")
        time.sleep(2)  # 避免請求太頻繁

if __name__ == "__main__":
    test_goodinfo_crawler()
