#!/usr/bin/env python3
"""
膽小貓策略測試腳本
測試低股價、低波動、營收成長的穩健投資法
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加主程式路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_data(stock_id, days=300, low_volatility=False):
    """創建測試用的股票數據"""
    np.random.seed(hash(stock_id) % 2**32)  # 根據股票代碼設定種子
    
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 基礎價格走勢 - 膽小貓偏好低價股
    if low_volatility:
        base_price = 15 + np.random.uniform(0, 15)  # 15-30元
        price_trend = np.random.uniform(0.0005, 0.002)  # 較小的日均漲幅
        volatility = 0.01  # 低波動
    else:
        base_price = 25 + np.random.uniform(0, 25)  # 25-50元
        price_trend = np.random.uniform(-0.001, 0.003)  # 正常波動
        volatility = 0.025  # 正常波動
    
    prices = []
    volumes = []
    
    for i in range(days):
        if i == 0:
            price = base_price
        else:
            # 價格隨機遊走 + 趨勢
            if low_volatility:
                # 低波動股票的價格變化更平穩
                change = np.random.normal(price_trend, volatility * 0.5)
            else:
                change = np.random.normal(price_trend, volatility)
            
            price = prices[-1] * (1 + change)
            price = max(price, 1.0)  # 最低1元
        
        prices.append(price)
        
        # 成交量：膽小貓需要基本流動性
        base_volume = 150000 + np.random.uniform(0, 300000)  # 基礎量較大
        if i > 0 and prices[i] > prices[i-1]:  # 上漲時適度放量
            volume_multiplier = np.random.uniform(1.1, 1.8)
        else:
            volume_multiplier = np.random.uniform(0.9, 1.3)
        
        volume = int(base_volume * volume_multiplier)
        volumes.append(volume)
    
    # 創建OHLC數據
    opens = []
    highs = []
    lows = []
    
    for i, close_price in enumerate(prices):
        if low_volatility:
            # 低波動股票的日內波動較小
            daily_range = close_price * np.random.uniform(0.01, 0.03)
        else:
            daily_range = close_price * np.random.uniform(0.02, 0.05)
        
        open_price = close_price * np.random.uniform(0.98, 1.02)
        high_price = max(open_price, close_price) + daily_range * np.random.uniform(0, 0.5)
        low_price = min(open_price, close_price) - daily_range * np.random.uniform(0, 0.5)
        
        opens.append(open_price)
        highs.append(high_price)
        lows.append(max(low_price, 1.0))  # 最低1元
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': opens,
        'High': highs,
        'Low': lows,
        'Close': prices,
        'Volume': volumes
    })
    
    return df

def test_timid_cat_strategy():
    """測試膽小貓策略"""
    try:
        # 導入膽小貓策略類
        from timid_cat_standalone import TimidCatStrategy
        
        # 初始化策略
        timid_cat = TimidCatStrategy()
        
        # 測試股票列表 - 包含一些低價股代碼
        test_stocks = ['1101', '1102', '1216', '1301', '1303', '2105', '2201', '2204', '2206', '2207']
        
        print("🐱 膽小貓策略測試")
        print("=" * 60)
        print("策略特色：低股價、低波動、營收成長的穩健投資法")
        print("像貓咪一樣膽小謹慎，專注安全的投資機會")
        print("=" * 60)
        
        results = []
        
        for i, stock_id in enumerate(test_stocks):
            print(f"\n📊 測試股票: {stock_id}")
            
            # 創建測試數據 - 部分股票設為低波動
            low_vol = (i % 3 == 0)  # 每3支股票中有1支是低波動
            df = create_sample_data(stock_id, low_volatility=low_vol)
            
            # 執行膽小貓分析
            result = timid_cat.analyze_stock(df)
            
            if result['suitable']:
                print(f"✅ 符合膽小貓條件")
                print(f"   總分: {result['score']}/8")
                print(f"   原因: {result['reason']}")
                
                # 顯示詳細評分
                details = result['details']
                for key, value in details.items():
                    status = "✓" if value['pass'] else "✗"
                    print(f"   {key}: {status} - {value['reason']}")
                
                results.append({
                    'stock_id': stock_id,
                    'score': result['score'],
                    'suitable': True,
                    'low_volatility': low_vol
                })
            else:
                print(f"❌ 不符合膽小貓條件")
                print(f"   總分: {result['score']}/8")
                print(f"   原因: {result['reason']}")
                
                # 顯示主要未通過項目
                details = result['details']
                failed_items = [k for k, v in details.items() if not v['pass']]
                if failed_items:
                    print(f"   主要問題: {', '.join(failed_items[:3])}")
                
                results.append({
                    'stock_id': stock_id,
                    'score': result['score'],
                    'suitable': False,
                    'low_volatility': low_vol
                })
        
        # 統計結果
        suitable_stocks = [r for r in results if r['suitable']]
        avg_score = np.mean([r['score'] for r in results])
        low_vol_suitable = [r for r in suitable_stocks if r['low_volatility']]
        
        print("\n" + "=" * 60)
        print("📈 測試結果統計")
        print(f"測試股票數: {len(test_stocks)}")
        print(f"符合條件: {len(suitable_stocks)} 支")
        print(f"通過率: {len(suitable_stocks)/len(test_stocks)*100:.1f}%")
        print(f"平均評分: {avg_score:.1f}/8")
        print(f"低波動股票通過數: {len(low_vol_suitable)}")
        
        if suitable_stocks:
            print(f"\n🎯 符合條件的股票:")
            for stock in sorted(suitable_stocks, key=lambda x: x['score'], reverse=True):
                vol_type = "低波動" if stock['low_volatility'] else "正常波動"
                print(f"   {stock['stock_id']}: {stock['score']}/8分 ({vol_type})")
        
        print("\n💡 膽小貓策略說明:")
        print("🐱 策略特質：像貓咪一樣體型小、敏感、膽小")
        print("• 創新高: 近5日至少1日創100日新高")
        print("• 價格區間: 60日高低價差 < 30%")
        print("• 低股價: ≤ 30元且低於市場40%分位數")
        print("• 成交量: 5日均量 > 100張")
        print("• 低波動: K線波動率 ≤ 8% ⭐核心條件⭐")
        print("• 營收動能: 短期 > 長期")
        print("• 超低價: ≤ 20元（加分項）")
        print("• 通過標準: 至少4個基本條件 + 低波動必須通過")
        
        print("\n🎯 投資心法:")
        print("• 有不對勁就膽小走人")
        print("• 有順風的標的就長抱")
        print("• 保持小賠大賺的節奏")
        print("• 像貓咪一樣謹慎但敏銳")
        
        return results
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        print("請確保 timid_cat_standalone.py 文件存在且包含 TimidCatStrategy 類")
        return []
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_timid_cat_strategy()
