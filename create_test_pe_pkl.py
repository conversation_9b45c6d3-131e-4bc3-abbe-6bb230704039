#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建測試用的 pe.pkl 檔案
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_pe_data():
    """創建測試用的PE資料"""
    
    # 創建日期範圍（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    # 生成交易日（排除週末）
    dates = []
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # 週一到週五
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    # 模擬股票代碼
    stock_codes = ['1101', '2330', '2317', '2454', '3008', '1216', '1301', '2881', '2882', '2883']
    
    # 創建多層索引資料
    data = []
    for date in dates:
        for stock in stock_codes:
            # 模擬PE資料
            pe_ratio = np.random.uniform(5, 30)  # PE比在5-30之間
            pb_ratio = np.random.uniform(0.5, 3)  # PB比在0.5-3之間
            dividend_yield = np.random.uniform(0, 8)  # 殖利率在0-8%之間
            
            data.append({
                'date': date,
                'stock_id': stock,
                'PE_ratio': round(pe_ratio, 2),
                'PB_ratio': round(pb_ratio, 2),
                'dividend_yield': round(dividend_yield, 2),
                'market_cap': np.random.uniform(100, 10000)  # 市值（億）
            })
    
    # 創建DataFrame
    df = pd.DataFrame(data)
    
    # 設置多層索引
    df = df.set_index(['date', 'stock_id'])
    
    print(f"✅ 創建測試PE資料")
    print(f"   日期範圍: {dates[0].strftime('%Y-%m-%d')} 至 {dates[-1].strftime('%Y-%m-%d')}")
    print(f"   股票數量: {len(stock_codes)}")
    print(f"   總資料筆數: {len(df)}")
    print(f"   資料欄位: {list(df.columns)}")
    
    return df

def main():
    """主函數"""
    print("=" * 50)
    print("🏗️ 創建測試用 pe.pkl 檔案")
    print("=" * 50)
    
    # 創建測試資料
    df = create_test_pe_data()
    
    # 保存為pickle檔案
    df.to_pickle('pe.pkl')
    print(f"\n💾 測試資料已保存為 pe.pkl")
    
    # 驗證檔案
    try:
        test_df = pd.read_pickle('pe.pkl')
        print(f"✅ 檔案驗證成功")
        print(f"   讀取資料筆數: {len(test_df)}")
        
        # 獲取最後日期
        if hasattr(test_df.index, 'levels'):
            dates = test_df.index.get_level_values(0)
            last_date = dates.max().strftime('%Y-%m-%d')
            first_date = dates.min().strftime('%Y-%m-%d')
        else:
            last_date = test_df.index.max().strftime('%Y-%m-%d')
            first_date = test_df.index.min().strftime('%Y-%m-%d')
        
        print(f"   最後日期: {last_date}")
        print(f"   第一日期: {first_date}")
        
    except Exception as e:
        print(f"❌ 檔案驗證失敗: {e}")
    
    print("\n" + "=" * 50)
    print("✨ 現在可以測試GUI中的pe.pkl更新功能了！")
    print("=" * 50)

if __name__ == "__main__":
    main()
