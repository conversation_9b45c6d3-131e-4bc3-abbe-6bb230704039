# 月營收排行榜功能說明

## 🎯 功能概述

在台股智能選股系統中新增了「月營收排行榜」功能，支援 YoY（年增率）和 MoM（月增率）兩種排序方式。

## 📋 功能特色

### 1. 雙重排序模式
- **月營收排行榜(YoY)**：按年增率排序，顯示與去年同月相比的成長情況
- **月營收排行榜(MoM)**：按月增率排序，顯示與上月相比的成長情況

### 2. 智能資料來源
- **1-10日**：從 GitHub TWSEMCPServer API 獲取最新資料（因月營收陸續公佈）
- **11日後**：從本地資料庫 `D:/Finlab/history/tables/monthly_report.db` 讀取完整資料

### 3. 完整顯示介面
- **右側表格**：顯示排名、股票代碼、股票名稱、當月營收、YoY%、MoM%
- **左側列表**：顯示簡化的排行榜資訊，支援點擊查看K線圖

## 🚀 使用方法

### 步驟 1：選擇計算日期
在「計算日期」欄位選擇要查詢的日期

### 步驟 2：選擇排行榜類型
在「當日排行榜」下拉選單中選擇：
- 月營收排行榜(YoY)
- 月營收排行榜(MoM)

### 步驟 3：執行查詢
點擊「執行排行」按鈕

### 步驟 4：查看結果
- 右側表格顯示詳細排行榜
- 左側列表顯示簡化資訊
- 點擊左側股票可查看K線圖

## 📊 資料格式說明

### 表格欄位
| 欄位 | 說明 | 範例 |
|------|------|------|
| 排名 | 排行榜名次 | 1, 2, 3... |
| 股票代碼 | 4位數股票代碼 | 2330, 2317 |
| 股票名稱 | 公司名稱 | 台積電, 鴻海 |
| 當月營收(千元) | 當月營業收入 | 120,000,000 |
| YoY% | 年增率 | +15.50% |
| MoM% | 月增率 | +8.20% |

### 顏色標示
- **紅色**：正成長（YoY% > 0 或 MoM% > 0）
- **綠色**：負成長（YoY% < 0 或 MoM% < 0）
- **灰色**：零成長（YoY% = 0 或 MoM% = 0）

## 🔧 技術實現

### 核心函數
- `get_monthly_revenue_ranking()`: 主要排行榜獲取函數
- `should_fetch_from_api()`: 判斷資料來源
- `fetch_monthly_revenue_from_db()`: 從資料庫讀取
- `fetch_latest_monthly_revenue()`: 從API獲取
- `calculate_revenue_growth()`: 計算成長率

### 資料庫結構
```sql
CREATE TABLE monthly_report (
    stock_id TEXT,
    stock_name TEXT,
    date TEXT,
    當月營收 TEXT,
    上月營收 TEXT,
    去年當月營收 TEXT,
    ...
);
```

### API 整合
使用 GitHub TWSEMCPServer 的 `get_company_monthly_revenue(code)` 指令獲取最新月營收資料。

## 📅 使用時機建議

### 最佳查詢時間
- **每月11日後**：資料最完整，建議使用資料庫資料
- **每月1-10日**：使用API獲取最新公佈資料

### 投資應用
- **YoY排行榜**：適合長期投資分析，觀察公司年度成長趨勢
- **MoM排行榜**：適合短期投資分析，觀察公司月度營運變化

## ⚠️ 注意事項

1. **資料時效性**：月營收資料通常在每月10日前陸續公佈完畢
2. **API限制**：GitHub API可能有請求頻率限制，建議適度使用
3. **資料完整性**：1-10日期間的API資料可能不完整
4. **網路連接**：API功能需要穩定的網路連接

## 🎉 功能優勢

1. **即時性**：結合API和資料庫，確保資料最新
2. **完整性**：支援所有上市上櫃公司
3. **易用性**：簡單的下拉選單操作
4. **視覺化**：清楚的排行榜顯示和顏色標示
5. **整合性**：與現有K線圖功能完美整合

## 🔮 未來擴展

可考慮增加的功能：
- 營收成長率區間篩選
- 行業別月營收排行榜
- 營收趨勢圖表顯示
- 營收預測功能
- 匯出Excel報告

---

**開發完成日期**：2025-07-29  
**測試狀態**：✅ 全部通過  
**相容性**：與現有系統完全相容
