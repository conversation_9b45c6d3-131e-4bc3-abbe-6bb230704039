#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證 newprice.db 與 price.db 結構一致性
"""

import sqlite3
import os

def verify_structure():
    """驗證結構一致性"""
    
    print('=' * 80)
    print('🔍 驗證 newprice.db 與 price.db 結構一致性')
    print('=' * 80)

    # 檢查兩個檔案
    price_db = 'D:/Finlab/history/tables/price.db'
    newprice_db = 'D:/Finlab/history/tables/newprice.db'

    for db_name, db_file in [('標準 price.db', price_db), ('新 newprice.db', newprice_db)]:
        if os.path.exists(db_file):
            print(f'\n📊 {db_name}:')
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 檢查檔案大小
            size = os.path.getsize(db_file) / (1024 * 1024)
            print(f'   檔案大小: {size:.2f} MB')
            
            # 檢查表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f'   表格: {[table[0] for table in tables]}')
            
            # 檢查表格結構
            if tables:
                table_name = tables[0][0]
                cursor.execute(f'PRAGMA table_info({table_name})')
                columns = cursor.fetchall()
                print(f'   欄位結構:')
                for col in columns:
                    print(f'     {col[1]} ({col[2]})')
                
                # 檢查資料筆數
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]
                print(f'   資料筆數: {count:,}')
                
                # 檢查股票數
                cursor.execute(f'SELECT COUNT(DISTINCT stock_id) FROM {table_name}')
                stocks = cursor.fetchone()[0]
                print(f'   不重複股票數: {stocks:,}')
                
                # 檢查日期範圍
                cursor.execute(f'SELECT MIN(date), MAX(date) FROM {table_name}')
                date_range = cursor.fetchone()
                print(f'   日期範圍: {date_range[0]} ~ {date_range[1]}')
                
                # 檢查範例資料
                cursor.execute(f'SELECT * FROM {table_name} LIMIT 2')
                samples = cursor.fetchall()
                print(f'   範例資料:')
                for i, row in enumerate(samples, 1):
                    print(f'     第{i}筆: {row}')
            
            conn.close()
        else:
            print(f'\n❌ {db_name}: 檔案不存在')

    print(f'\n✅ 結構比較完成！')

if __name__ == "__main__":
    verify_structure()
