#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略下拉選單顯示測試腳本
測試模擬數據策略是否正確以紅色顯示並移至下方
"""
import sys
import os

def test_strategy_combo_display():
    """測試策略下拉選單顯示"""
    print("🚀 測試策略下拉選單顯示...")

    try:
        # 設置環境變量以避免實際顯示 GUI
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'

        # 導入必要的模組
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtGui import QBrush, QColor

        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 導入主程式
        import O3mh_gui_v21_optimized

        # 創建策略下拉選單
        combo = O3mh_gui_v21_optimized.SimpleStrategyComboBox()

        # 模擬策略數據
        strategies = {
            "勝率73.45%": {},
            "破底反彈高量": {},
            "阿水一式": {},
            "阿水二式": {},
            "藏獒": {},
            "CANSLIM量價齊升": {},
            "膽小貓": {},
            "監獄兔": {},  # 模擬數據策略
            "超級績效台股版": {},  # 模擬數據策略
            "台股總體經濟ETF": {},  # 模擬數據策略
            "小蝦米跟大鯨魚": {},  # 模擬數據策略
        }

        # 模擬數據策略列表
        simulated_data_strategies = [
            "監獄兔", "超級績效台股版", "台股總體經濟ETF", "小蝦米跟大鯨魚",
            "研發魔人", "高殖利率烏龜", "台灣十五小市值", "本益成長比"
        ]

        # 策略分類 (模擬數據策略在下方)
        strategy_categories = {
            "📈 經典策略": ["勝率73.45%", "破底反彈高量"],
            "🌟 阿水策略系列": ["阿水一式", "阿水二式"],
            "🚀 進階策略": ["藏獒", "CANSLIM量價齊升", "膽小貓"],
            "⚠️ 需要數據優化 (模擬數據)": simulated_data_strategies
        }

        # 設置策略
        combo.set_strategies_compact(strategies, strategy_categories)

        print("✅ 策略下拉選單創建成功")

        # 檢查項目順序和顏色
        print("\n📋 策略選單項目檢查:")
        for i in range(combo.count()):
            item_text = combo.itemText(i)
            item = combo.model().item(i)

            if item_text.startswith("▼"):
                # 分類標題
                print(f"  📂 {item_text}")
                if "模擬數據" in item_text:
                    print(f"     ✅ 模擬數據分類已移至下方")
            elif item_text.strip():
                # 策略項目
                is_enabled = item.isEnabled()
                foreground = item.foreground()

                if any(sim_strategy in item_text for sim_strategy in simulated_data_strategies):
                    print(f"     🔴 {item_text.strip()} (模擬數據策略)")
                    if foreground.color() == QColor(220, 20, 60):
                        print(f"        ✅ 正確顯示為紅色")
                    else:
                        print(f"        ❌ 顏色設置可能有問題")
                else:
                    print(f"     ⚫ {item_text.strip()} (真實數據策略)")

        # 檢查模擬數據策略是否在下方
        simulated_section_found = False
        simulated_section_index = -1

        for i in range(combo.count()):
            item_text = combo.itemText(i)
            if "模擬數據" in item_text:
                simulated_section_found = True
                simulated_section_index = i
                break

        if simulated_section_found:
            total_items = combo.count()
            position_percentage = (simulated_section_index / total_items) * 100
            print(f"\n📊 模擬數據策略位置分析:")
            print(f"   模擬數據分類位置: {simulated_section_index}/{total_items} ({position_percentage:.1f}%)")

            if position_percentage > 60:  # 在下半部分
                print(f"   ✅ 模擬數據策略已成功移至下方")
            else:
                print(f"   ⚠️ 模擬數據策略位置可能需要調整")

        # 清理
        combo.close()

        return True

    except Exception as e:
        print(f"❌ 策略下拉選單測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 策略下拉選單顯示測試")
    print("=" * 60)

    success = test_strategy_combo_display()

    print("\n" + "=" * 60)
    if success:
        print("🎉 策略下拉選單顯示測試通過！")
        print("✅ 模擬數據策略以紅色顯示")
        print("✅ 模擬數據策略已移至下方")
        print("✅ 策略分類正確組織")
        print("✅ 提示信息正確設置")
    else:
        print("❌ 策略下拉選單顯示測試失敗")
        print("需要進一步檢查問題")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)