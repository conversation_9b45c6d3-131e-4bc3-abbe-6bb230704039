#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 auto_update.py 整合效果
"""

import sys
import os
from datetime import datetime

def test_finlab_functions():
    """測試 finlab 核心函數"""
    print("🔍 測試 finlab 核心函數...")
    
    try:
        # 添加AI_finlab目錄到Python路徑
        ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
        if ai_finlab_path not in sys.path:
            sys.path.insert(0, ai_finlab_path)

        # 導入核心函數
        from crawler import (
            crawl_pe, table_date_range, update_table, 
            to_pickle, date_range, commit
        )
        
        print("✅ 核心函數導入成功")
        
        # 測試 table_date_range
        print("\n📅 測試 table_date_range...")
        try:
            first_date, last_date = table_date_range('pe')
            print(f"   PE表格日期範圍: {first_date} 至 {last_date}")
        except Exception as e:
            print(f"   ⚠️ table_date_range 錯誤: {e}")
        
        # 測試 date_range
        print("\n📈 測試 date_range...")
        try:
            if last_date:
                dates = date_range(last_date, datetime.now())
                if dates:
                    print(f"   需要更新的日期數量: {len(dates)}")
                    print(f"   日期範圍: {dates[0]} 至 {dates[-1]}")
                else:
                    print("   ✅ 無需更新日期")
            else:
                print("   ⚠️ 無法獲取最後日期")
        except Exception as e:
            print(f"   ❌ date_range 錯誤: {e}")
        
        # 測試 crawl_pe 函數簽名
        print("\n🔍 測試 crawl_pe 函數簽名...")
        try:
            from inspect import signature
            sig = signature(crawl_pe)
            print(f"   crawl_pe 參數數量: {len(sig.parameters)}")
            print(f"   參數: {list(sig.parameters.keys())}")
        except Exception as e:
            print(f"   ❌ 函數簽名檢查錯誤: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試錯誤: {e}")
        return False

def test_auto_update_logic():
    """測試 auto_update 邏輯"""
    print("\n🧪 測試 auto_update 邏輯...")
    
    try:
        ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
        if ai_finlab_path not in sys.path:
            sys.path.insert(0, ai_finlab_path)

        from crawler import (
            crawl_pe, table_date_range, update_table, 
            to_pickle, date_range, commit
        )
        from inspect import signature
        
        # 模擬 auto_update 的邏輯
        table_name = 'pe'
        crawl_function = crawl_pe
        time_range = date_range
        
        print(f"   表格名稱: {table_name}")
        print(f"   爬蟲函數: {crawl_function.__name__}")
        
        sig = signature(crawl_function)
        
        if len(sig.parameters) != 0:
            print("   ✅ 爬蟲函數需要日期參數")
            
            first_date, last_date = table_date_range(table_name)
            print(f"   表格日期範圍: {first_date} 至 {last_date}")
            
            if last_date:
                dates = time_range(last_date, datetime.now())
                if dates:
                    print(f"   📈 需要更新 {len(dates)} 個日期")
                    print(f"   更新範圍: {dates[0]} 至 {dates[-1]}")
                    
                    # 這裡不實際執行 update_table，只是測試邏輯
                    print("   ✅ 可以執行 update_table")
                else:
                    print("   ✅ 資料已是最新")
            else:
                print("   ⚠️ 無法獲取最後日期")
        else:
            print("   ✅ 爬蟲函數不需要日期參數")
            print("   可以直接執行 crawl_function()")
        
        return True
        
    except Exception as e:
        print(f"❌ auto_update 邏輯測試錯誤: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 AUTO_UPDATE.PY 整合測試")
    print("=" * 60)
    
    # 測試1: finlab 核心函數
    success1 = test_finlab_functions()
    
    # 測試2: auto_update 邏輯
    success2 = test_auto_update_logic()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    print(f"1. Finlab 核心函數: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"2. Auto_update 邏輯: {'✅ 成功' if success2 else '❌ 失敗'}")
    
    overall_success = success1 and success2
    print(f"\n🎯 整體測試結果: {'✅ 全部成功' if overall_success else '❌ 部分失敗'}")
    
    if overall_success:
        print("\n✨ GUI 中的 pe.pkl 更新功能應該可以正常工作！")
        print("   現在使用的是 auto_update.py 的標準方式")
    else:
        print("\n⚠️ 發現問題，請檢查上述失敗的測試項目")

if __name__ == "__main__":
    main()
