# Finlab 爬蟲系統 GUI 使用說明

## 🎯 概述

本GUI系統提供了友好的圖形化界面來操作 Finlab 爬蟲功能，支援10種不同的資料爬取功能。

## 📁 檔案說明

### 主要檔案
- **`u02_crawlers_gui.py`** - PyQt6版本的GUI界面（功能最完整）
- **`u02_crawlers_gui_simple.py`** - tkinter版本的GUI界面（相容性最佳）
- **`start_crawler_gui.py`** - 自動啟動器（推薦使用）

### 原始檔案
- **`u02_crawlers.py`** - 命令列版本
- **`u02_crawlers.ipynb`** - 原始Jupyter notebook

## 🚀 快速開始

### 方法1: 使用自動啟動器（推薦）
```bash
python start_crawler_gui.py
```
啟動器會自動檢測可用的GUI框架並選擇最佳版本。

### 方法2: 直接啟動特定版本
```bash
# PyQt6版本（功能最完整）
python u02_crawlers_gui.py

# tkinter版本（相容性最佳）
python u02_crawlers_gui_simple.py
```

## 🖥️ 界面介紹

### 主要區域

#### 1. 📊 爬蟲功能按鈕區
- **10個彩色按鈕**，每個對應一種爬蟲功能
- **按鈕顏色編碼**：不同顏色代表不同類型的資料
- **點擊執行**：直接點擊按鈕即可執行對應的爬蟲

#### 2. ⚙️ 參數設置區
- **日期範圍設置**：
  - PyQt6版本：提供日期選擇器
  - tkinter版本：提供天數選擇下拉選單
- **財務報表參數**：
  - 年度選擇
  - 季度選擇

#### 3. 📋 執行日誌區
- **即時顯示**執行狀態和結果
- **深色主題**的日誌顯示區域
- **自動滾動**到最新日誌
- **清除日誌**按鈕

#### 4. 📊 進度指示
- **進度條**：顯示爬蟲執行狀態
- **按鈕禁用**：執行期間防止重複點擊

## 🎛️ 功能詳解

### 爬蟲功能列表

| 編號 | 功能名稱 | 說明 | 參數需求 |
|------|----------|------|----------|
| 1 | 股價資料 | 爬取股票價格資訊 | 日期範圍 |
| 2 | 三大法人 | 爬取三大法人買賣資料 | 日期範圍 |
| 3 | 本益比 | 爬取本益比資料 | 日期範圍 |
| 4 | 月營收 | 爬取月營收資料 | 日期範圍 |
| 5 | 大盤指數 | 爬取大盤指數資料 | 日期範圍 |
| 6 | 財務報表 | 爬取財務報表資料 | 年度+季度 |
| 7 | 上市除權息 | 爬取上市除權息資料 | 無需參數 |
| 8 | 上櫃除權息 | 爬取上櫃除權息資料 | 無需參數 |
| 9 | 上市減資 | 爬取上市減資資料 | 無需參數 |
| 10 | 上櫃減資 | 爬取上櫃減資資料 | 無需參數 |

### 參數設置說明

#### 日期範圍參數
- **PyQt6版本**：
  - 開始日期：可選擇具體日期
  - 結束日期：可選擇具體日期
  - 預設：最近30天

- **tkinter版本**：
  - 天數選擇：7, 30, 60, 90, 180, 365天
  - 預設：30天

#### 財務報表參數
- **年度**：2020-2030年可選
- **季度**：1-4季可選
- **預設**：當前年度第4季

## 🔧 系統需求

### 必要依賴
```bash
pip install pandas requests
```

### GUI框架（任選其一）
```bash
# PyQt6版本（推薦）
pip install PyQt6

# tkinter版本（通常已內建）
# 如未安裝請查閱系統文檔
```

### 完整功能（可選）
```bash
pip install finlab
```

## 💡 使用技巧

### 1. 選擇合適的版本
- **PyQt6版本**：功能最完整，界面最美觀
- **tkinter版本**：相容性最佳，適合舊系統

### 2. 參數設置建議
- **股價類資料**：建議30-90天範圍
- **財務報表**：建議按季度查詢
- **除權息資料**：無需設置參數，自動全量更新

### 3. 執行順序建議
1. 先執行基礎資料（股價、大盤指數）
2. 再執行分析資料（三大法人、本益比）
3. 最後執行特殊資料（除權息、減資）

## 🚨 注意事項

### 執行限制
- **同時執行**：一次只能執行一個爬蟲
- **網路需求**：需要穩定的網路連線
- **執行時間**：根據資料量不同，執行時間1-10分鐘不等

### 錯誤處理
- **依賴缺失**：系統會自動檢查並提示安裝
- **網路錯誤**：會在日誌中顯示詳細錯誤資訊
- **資料錯誤**：會彈出錯誤對話框並記錄日誌

### 資料來源
- **官方網站**：台灣證券交易所、櫃買中心等
- **使用限制**：請遵守資料來源網站的使用條款
- **資料準確性**：建議與官方資料交叉驗證

## 🔍 故障排除

### 常見問題

#### 1. GUI無法啟動
```
❌ 沒有可用的GUI框架
```
**解決方案**：
```bash
pip install PyQt6
# 或確認 tkinter 已安裝
```

#### 2. 爬蟲執行失敗
```
❌ 爬蟲執行失敗: No module named 'finlab'
```
**解決方案**：
```bash
pip install finlab
# 或使用簡化版功能
```

#### 3. 網路連線問題
**解決方案**：
- 檢查網路連線
- 確認防火牆設置
- 稍後重試

#### 4. 資料格式錯誤
**解決方案**：
- 檢查參數設置
- 確認日期範圍合理
- 查看詳細錯誤日誌

## 📊 版本比較

| 特性 | PyQt6版本 | tkinter版本 | 命令列版本 |
|------|-----------|-------------|------------|
| 界面美觀度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ |
| 功能完整度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 相容性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 易用性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 資源占用 | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🆘 技術支援

### 檢查清單
1. ✅ Python 版本 >= 3.7
2. ✅ 必要依賴已安裝
3. ✅ 網路連線正常
4. ✅ GUI框架可用

### 日誌分析
- **綠色訊息**：正常執行
- **黃色警告**：需要注意但不影響執行
- **紅色錯誤**：執行失敗，需要處理

---

**開發日期**: 2025-07-20  
**版本**: GUI v1.0  
**支援**: Python 3.7+
