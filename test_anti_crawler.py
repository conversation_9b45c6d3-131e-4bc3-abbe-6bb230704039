#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試反爬蟲機制
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import time
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 從 auto_update.py 導入反爬蟲函數
from auto_update import (
    get_random_user_agent, 
    get_enhanced_headers, 
    smart_delay, 
    enhanced_request,
    session_request
)

def test_user_agent():
    """測試隨機 User-Agent"""
    print("🔧 測試隨機 User-Agent")
    print("=" * 50)
    
    agents = set()
    for i in range(5):
        agent = get_random_user_agent()
        agents.add(agent)
        print(f"   {i+1}. {agent[:50]}...")
    
    print(f"✅ 生成了 {len(agents)} 個不同的 User-Agent")
    return len(agents) > 1

def test_enhanced_headers():
    """測試增強 headers"""
    print(f"\n🔧 測試增強 headers")
    print("=" * 50)
    
    headers = get_enhanced_headers()
    
    print(f"📋 Headers 內容:")
    for key, value in headers.items():
        print(f"   {key}: {value[:50]}...")
    
    required_headers = ['User-Agent', 'Accept', 'Accept-Language']
    missing = [h for h in required_headers if h not in headers]
    
    if not missing:
        print(f"✅ 所有必要 headers 都存在")
        return True
    else:
        print(f"❌ 缺少 headers: {missing}")
        return False

def test_smart_delay():
    """測試智能延遲"""
    print(f"\n🔧 測試智能延遲")
    print("=" * 50)
    
    print("測試基本延遲...")
    start_time = time.time()
    last_time = smart_delay(base_delay=2, variance=1)
    elapsed = time.time() - start_time
    print(f"   基本延遲: {elapsed:.1f} 秒")
    
    print("測試連續請求延遲...")
    start_time = time.time()
    smart_delay(base_delay=2, variance=1, last_request_time=last_time)
    elapsed = time.time() - start_time
    print(f"   連續延遲: {elapsed:.1f} 秒")
    
    return True

def test_enhanced_request():
    """測試增強請求函數"""
    print(f"\n🔧 測試增強請求函數")
    print("=" * 50)
    
    # 測試一個簡單的網站
    test_url = "https://httpbin.org/user-agent"
    
    try:
        print(f"🌐 測試 URL: {test_url}")
        response = enhanced_request(test_url)
        
        if response.status_code == 200:
            print(f"✅ 請求成功: {response.status_code}")
            
            # 檢查回應內容
            try:
                import json
                data = response.json()
                user_agent = data.get('user-agent', '')
                print(f"   回應 User-Agent: {user_agent[:50]}...")
                return True
            except:
                print(f"   回應內容: {response.text[:100]}...")
                return True
        else:
            print(f"⚠️ 請求狀態: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 請求失敗: {str(e)}")
        return False

def test_session_request():
    """測試會話請求"""
    print(f"\n🔧 測試會話請求")
    print("=" * 50)
    
    test_url = "https://httpbin.org/headers"
    
    try:
        print(f"🌐 測試會話請求: {test_url}")
        response = session_request(test_url)
        
        if response.status_code == 200:
            print(f"✅ 會話請求成功: {response.status_code}")
            return True
        else:
            print(f"⚠️ 會話請求狀態: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 會話請求失敗: {str(e)}")
        return False

def test_mops_connectivity():
    """測試 MOPS 網站連接性"""
    print(f"\n🔧 測試 MOPS 網站連接性")
    print("=" * 50)
    
    # 測試 MOPS 主頁
    mops_url = "https://mops.twse.com.tw/"
    
    try:
        print(f"🌐 測試 MOPS 主頁: {mops_url}")
        response = enhanced_request(mops_url)
        
        if response.status_code == 200:
            print(f"✅ MOPS 主頁可訪問: {response.status_code}")
            print(f"   回應長度: {len(response.text)} 字元")
            
            # 檢查是否被封鎖
            if '您的網頁IP已經被證交所封鎖' in response.text:
                print(f"   ⚠️ IP 仍被封鎖")
                return False
            else:
                print(f"   ✅ IP 未被封鎖")
                return True
        else:
            print(f"⚠️ MOPS 主頁狀態: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MOPS 連接失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 反爬蟲機制測試工具")
    print("=" * 60)
    print("🎯 目標: 驗證反爬蟲機制是否正常工作")
    print("=" * 60)
    
    tests = [
        ("隨機 User-Agent", test_user_agent),
        ("增強 Headers", test_enhanced_headers),
        ("智能延遲", test_smart_delay),
        ("增強請求", test_enhanced_request),
        ("會話請求", test_session_request),
        ("MOPS 連接性", test_mops_connectivity),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {str(e)}")
            results.append((test_name, False))
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(results)} 個測試通過")
    
    if passed >= len(results) * 0.8:  # 80% 通過率
        print(f"🎉 反爬蟲機制基本正常！")
        print(f"💡 現在可以安全運行 auto_update.py")
    else:
        print(f"⚠️ 反爬蟲機制可能需要調整")

if __name__ == "__main__":
    main()
