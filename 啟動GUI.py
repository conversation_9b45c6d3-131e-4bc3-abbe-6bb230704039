#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab 爬蟲系統 GUI 啟動器
"""

import sys
import subprocess

def main():
    """啟動GUI"""
    print("🚀 啟動 Finlab 爬蟲系統 GUI...")
    
    try:
        # 檢查Python
        print("✅ Python 已就緒")
        
        # 啟動GUI
        subprocess.run([sys.executable, "finlab_crawler_gui.py"])
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        input("按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
