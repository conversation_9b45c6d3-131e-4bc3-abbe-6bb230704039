#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新 MOPS 網站月營收爬蟲
基於 https://mopsov.twse.com.tw/mops/web/t05st10_ifrs
"""

import requests
import pandas as pd
import datetime
import time
import random
import urllib3
from io import StringIO

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_enhanced_headers():
    """獲取增強的 HTTP headers"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    ]
    
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://mopsov.twse.com.tw/mops/web/t05st10_ifrs',
        'Cache-Control': 'max-age=0',
    }

def smart_delay(base_delay=2, variance=1):
    """智能延遲"""
    delay = base_delay + random.uniform(0, variance)
    time.sleep(delay)
    return time.time()

def new_month_revenue(name, date):
    """
    使用新 MOPS 網站爬取月營收
    
    Args:
        name: 'sii' (上市) 或 'otc' (上櫃)
        date: datetime 物件
    
    Returns:
        pandas.DataFrame: 月營收資料
    """
    year = date.year - 1911  # 轉換為民國年
    month = (date.month + 10) % 12 + 1  # 計算營收月份
    if month == 12:
        year -= 1
    
    print(f'🔄 開始爬取新MOPS {name} 月營收 (民國{year}年{month}月)')
    
    # 新 MOPS 月營收 URL (正確的新網站地址 - 每月營業收入彙總表)
    url = "https://mopsov.twse.com.tw/mops/web/t21sc04_ifrs"
    
    # 智能延遲
    smart_delay(base_delay=3, variance=2)
    
    try:
        headers = get_enhanced_headers()
        
        # 創建 session
        session = requests.Session()
        session.headers.update(headers)
        
        # 第一步: 獲取查詢頁面
        print(f"   📄 獲取查詢頁面...")
        response = session.get(url, verify=False, timeout=30)
        
        if response.status_code != 200:
            print(f"   ❌ 無法訪問查詢頁面: {response.status_code}")
            return pd.DataFrame()
        
        print(f"   ✅ 查詢頁面載入成功")
        
        # 第二步: 提交查詢表單
        print(f"   🔍 提交查詢表單...")
        
        # 構建表單資料 (基於新網站結構)
        form_data = {
            'encodeURIComponent': '1',
            'step': '1',
            'firstin': '1',
            'off': '1',
            'isQuery': 'Y',
            'TYPEK': 'sii' if name == 'sii' else 'otc',  # 指定上市或上櫃
            'year': str(year),
            'month': str(month),
            'co_id': '',  # 公司代號 (空白表示全部)
            'keyword4': '',
            'code1': '',
            'TYPEK2': '',
            'checkbtn': '',
            'queryName': 'co_id',
            'inpuType': 'co_id',
            'isnew': 'false'
        }
        
        # 提交表單
        response = session.post(url, data=form_data, verify=False, timeout=60)
        
        if response.status_code != 200:
            print(f"   ❌ 查詢失敗: {response.status_code}")
            return pd.DataFrame()
        
        print(f"   ✅ 查詢成功: {len(response.content)} bytes")
        
        # 設置正確的編碼
        response.encoding = 'big5'
        content = response.text
        
        # 檢查是否有錯誤訊息
        if '查無資料' in content or '無此資料' in content:
            print(f"   ⚠️ 查無資料")
            return pd.DataFrame()
        
        if '您的網頁IP已經被證交所封鎖' in content:
            print(f"   ❌ IP被封鎖")
            return pd.DataFrame()
        
        # 保存回應內容用於調試
        debug_filename = f'new_mops_monthly_{name}_{year}_{month}.html'
        with open(debug_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   💾 回應內容已保存: {debug_filename}")
        
        # 第三步: 解析 HTML 表格
        print(f"   📊 解析月營收表格...")
        
        try:
            # 使用 pandas 解析 HTML 表格
            dfs = pd.read_html(StringIO(content), encoding='big-5')
            print(f"   ✅ 找到 {len(dfs)} 個表格")
            
            if len(dfs) == 0:
                print(f"   ⚠️ 沒有找到表格")
                return pd.DataFrame()
            
            # 處理表格資料
            processed_data = process_new_monthly_data(dfs, name, date)
            
            if len(processed_data) > 0:
                print(f"   ✅ 成功處理 {len(processed_data)} 筆月營收資料")
                return processed_data
            else:
                print(f"   ⚠️ 處理後無有效資料")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"   ❌ 解析表格失敗: {str(e)[:50]}...")
            return pd.DataFrame()
        
    except Exception as e:
        print(f"   ❌ 爬取失敗: {str(e)[:50]}...")
        return pd.DataFrame()

def process_new_monthly_data(dfs, name, date):
    """
    處理新 MOPS 網站的月營收表格
    
    Args:
        dfs: pandas 解析的表格列表
        name: 'sii' 或 'otc'
        date: datetime 物件
    
    Returns:
        pandas.DataFrame: 處理後的月營收資料
    """
    print(f"   🔧 處理月營收表格...")
    
    all_data = []
    
    for i, df in enumerate(dfs):
        print(f"     處理表格 {i+1}: {df.shape}")
        
        try:
            # 跳過太小的表格
            if df.shape[0] < 3 or df.shape[1] < 5:
                continue
            
            # 檢查是否為月營收資料表格
            if not is_monthly_revenue_table(df):
                continue
            
            # 處理表格資料
            processed_rows = process_monthly_table(df, date)
            
            if processed_rows:
                all_data.extend(processed_rows)
                print(f"       ✅ 提取 {len(processed_rows)} 筆資料")
            
        except Exception as e:
            print(f"       ⚠️ 處理表格 {i+1} 失敗: {str(e)[:30]}...")
            continue
    
    if all_data:
        # 轉換為 DataFrame
        result_df = pd.DataFrame(all_data)
        
        # 設置索引
        if 'stock_id' in result_df.columns and 'date' in result_df.columns:
            result_df = result_df.set_index(['stock_id', 'date'])
        
        print(f"   ✅ 總共處理 {len(result_df)} 筆月營收資料")
        return result_df
    else:
        print(f"   ⚠️ 沒有提取到有效資料")
        return pd.DataFrame()

def is_monthly_revenue_table(df):
    """判斷是否為月營收資料表格"""
    # 檢查表格內容是否包含月營收相關關鍵字
    content_str = str(df.values).lower()
    
    monthly_keywords = [
        '當月營收', '營業收入', '營收', 'revenue',
        '上月營收', '去年當月營收',
        '公司代號', '股票代號', '公司名稱',
        '增減', '比較', '%'
    ]
    
    found_keywords = sum(1 for keyword in monthly_keywords if keyword in content_str)
    
    # 如果找到足夠的月營收關鍵字，認為是月營收表格
    return found_keywords >= 3

def process_monthly_table(df, date):
    """處理單個月營收表格"""
    processed_rows = []
    
    try:
        # 處理多層欄位名稱
        if hasattr(df.columns, 'levels') and len(df.columns.levels) > 1:
            new_columns = []
            for col in df.columns:
                if hasattr(col, '__len__') and len(col) > 1:
                    # 合併多層欄位名稱
                    col_name = '_'.join([str(c) for c in col if str(c) != 'nan'])
                    new_columns.append(col_name)
                else:
                    new_columns.append(str(col))
            df.columns = new_columns
        
        # 尋找包含股票代號的行
        for idx, row in df.iterrows():
            try:
                # 檢查第一欄是否為股票代號
                first_col = str(row.iloc[0]).strip()
                
                # 跳過標題行和無效行
                if not first_col or first_col == 'nan' or '公司代號' in first_col or first_col == '合計':
                    continue
                
                # 股票代號格式檢查
                if not (first_col.isdigit() and len(first_col) == 4):
                    continue
                
                # 獲取公司名稱
                company_name = str(row.iloc[1]).strip() if len(row) > 1 else ''
                
                # 獲取當月營收 (通常在第3欄)
                current_revenue = str(row.iloc[2]).strip() if len(row) > 2 else '0'
                
                # 清理數值
                current_revenue = current_revenue.replace(',', '').replace('--', '0')
                current_revenue = pd.to_numeric(current_revenue, errors='coerce')
                
                if pd.isna(current_revenue):
                    continue
                
                # 建立基本資料
                stock_data = {
                    'stock_id': f"{first_col} {company_name}",
                    'date': date,
                    '當月營收': current_revenue,
                }
                
                # 添加其他欄位 (如果存在)
                if len(row) > 3:
                    last_month_revenue = str(row.iloc[3]).strip().replace(',', '').replace('--', '0')
                    stock_data['上月營收'] = pd.to_numeric(last_month_revenue, errors='coerce')
                
                if len(row) > 4:
                    last_year_revenue = str(row.iloc[4]).strip().replace(',', '').replace('--', '0')
                    stock_data['去年當月營收'] = pd.to_numeric(last_year_revenue, errors='coerce')
                
                if len(row) > 5:
                    mom_change = str(row.iloc[5]).strip().replace(',', '').replace('%', '').replace('--', '0')
                    stock_data['較上月增減(%)'] = pd.to_numeric(mom_change, errors='coerce')
                
                if len(row) > 6:
                    yoy_change = str(row.iloc[6]).strip().replace(',', '').replace('%', '').replace('--', '0')
                    stock_data['較去年同月增減(%)'] = pd.to_numeric(yoy_change, errors='coerce')
                
                processed_rows.append(stock_data)
                
            except Exception as e:
                continue  # 跳過有問題的行
        
    except Exception as e:
        print(f"         處理表格時出錯: {str(e)[:30]}...")
    
    return processed_rows

def new_crawl_monthly_report(date):
    """
    新的月營收爬蟲主函數
    
    Args:
        date: datetime 物件
    
    Returns:
        pandas.DataFrame: 合併的月營收資料
    """
    print(f"🔄 開始爬取新MOPS月營收 - {date.strftime('%Y-%m-%d')}")
    
    # 爬取上市公司
    df_sii = new_month_revenue('sii', date)
    time.sleep(5)  # 延遲避免被封鎖
    
    # 爬取上櫃公司
    df_otc = new_month_revenue('otc', date)
    
    # 合併資料
    if len(df_sii) > 0 and len(df_otc) > 0:
        print(f"✅ 合併上市({len(df_sii)})和上櫃({len(df_otc)})資料")
        combined_df = pd.concat([df_sii, df_otc], ignore_index=False)
        return combined_df
    elif len(df_sii) > 0:
        print(f"⚠️ 只有上市資料: {len(df_sii)} 筆")
        return df_sii
    elif len(df_otc) > 0:
        print(f"⚠️ 只有上櫃資料: {len(df_otc)} 筆")
        return df_otc
    else:
        print(f"❌ 沒有獲取到任何資料")
        return pd.DataFrame()

def test_new_monthly_crawler():
    """測試新月營收爬蟲"""
    print("🔧 測試新 MOPS 月營收爬蟲")
    print("=" * 60)
    
    # 測試日期 (爬取較舊的月份，確定已發布)
    test_date = datetime.datetime(2024, 12, 10)  # 爬取2024年11月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
    
    try:
        result = new_crawl_monthly_report(test_date)
        
        if not result.empty:
            print(f"\n✅ 測試成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('new_monthly_test.pkl')
            print(f"\n💾 測試資料已保存: new_monthly_test.pkl")
            
        else:
            print(f"\n❌ 測試失敗: 無資料")
            
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")

if __name__ == "__main__":
    test_new_monthly_crawler()
