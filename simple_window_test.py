#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試窗口控制按鈕
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QGroupBox, QCheckBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class TestDialog(QDialog):
    """測試對話框 - 模擬台灣證交所市場數據爬蟲界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📊 台灣證交所市場數據爬蟲 - 測試版")
        self.setMinimumSize(800, 700)
        self.resize(900, 750)
        
        # 設定窗口標誌：包含最小化、最大化、關閉按鈕
        self.setWindowFlags(
            Qt.WindowType.Window | 
            Qt.WindowType.WindowMinimizeButtonHint | 
            Qt.WindowType.WindowMaximizeButtonHint | 
            Qt.WindowType.WindowCloseButtonHint |
            Qt.WindowType.WindowTitleHint |
            Qt.WindowType.WindowSystemMenuHint
        )
        
        # 設定窗口屬性
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)
        
        self.setup_ui()
    
    def setup_ui(self):
        """設置用戶界面"""
        layout = QVBoxLayout(self)
        
        # 標題
        title_label = QLabel("📊 台灣證交所市場數據爬蟲")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2E86AB; margin: 10px;")
        layout.addWidget(title_label)
        
        # 說明文字
        desc_label = QLabel(
            "✅ 窗口控制按鈕測試版本\n\n"
            "請檢查窗口右上角是否有以下按鈕：\n"
            "• ➖ 最小化按鈕\n"
            "• ⬜ 最大化按鈕\n"
            "• ❌ 關閉按鈕\n\n"
            "測試功能：\n"
            "1. 點擊最小化按鈕 - 窗口應該最小化到工作列\n"
            "2. 點擊最大化按鈕 - 窗口應該全螢幕顯示\n"
            "3. 再次點擊最大化按鈕 - 窗口應該恢復原始大小\n"
            "4. 點擊關閉按鈕 - 窗口應該關閉\n"
            "5. 拖拽窗口標題列 - 可以移動窗口\n"
            "6. 拖拽窗口邊緣 - 可以調整大小"
        )
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                color: #333;
                line-height: 1.5;
            }
        """)
        layout.addWidget(desc_label)
        
        # 數據類型選擇群組（模擬）
        data_group = QGroupBox("📋 選擇數據類型")
        data_layout = QVBoxLayout(data_group)
        
        # 市場指數資訊
        market_index_cb = QCheckBox("📈 市場指數資訊")
        market_index_cb.setChecked(True)
        data_layout.addWidget(market_index_cb)
        
        # 融資融券統計
        margin_trading_cb = QCheckBox("💰 融資融券統計")
        margin_trading_cb.setChecked(True)
        data_layout.addWidget(margin_trading_cb)
        
        # 發行量加權股價指數歷史資料
        historical_index_cb = QCheckBox("📊 發行量加權股價指數歷史資料")
        historical_index_cb.setChecked(True)
        data_layout.addWidget(historical_index_cb)
        
        layout.addWidget(data_group)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 測試按鈕
        test_btn = QPushButton("🧪 測試窗口控制")
        test_btn.clicked.connect(self.test_window_controls)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(test_btn)
        
        # 關閉按鈕
        close_btn = QPushButton("❌ 關閉")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def test_window_controls(self):
        """測試窗口控制功能"""
        from PyQt6.QtWidgets import QMessageBox
        
        QMessageBox.information(
            self,
            "窗口控制測試",
            "✅ 窗口控制按鈕測試\n\n"
            "請依序測試以下功能：\n\n"
            "1️⃣ 最小化按鈕 (➖)\n"
            "   - 點擊後窗口應該最小化到工作列\n"
            "   - 從工作列點擊可以恢復窗口\n\n"
            "2️⃣ 最大化按鈕 (⬜)\n"
            "   - 點擊後窗口應該全螢幕顯示\n"
            "   - 再次點擊應該恢復原始大小\n\n"
            "3️⃣ 關閉按鈕 (❌)\n"
            "   - 點擊後窗口應該關閉\n\n"
            "4️⃣ 窗口拖拽\n"
            "   - 拖拽標題列可以移動窗口\n"
            "   - 拖拽邊緣可以調整大小"
        )
    
    def closeEvent(self, event):
        """處理窗口關閉事件"""
        from PyQt6.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self,
            "確認關閉",
            "確定要關閉窗口嗎？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("窗口控制測試")
    app.setApplicationVersion("1.0")
    
    # 創建對話框
    dialog = TestDialog()
    dialog.show()
    
    print("🚀 窗口控制測試程式已啟動")
    print("📋 請檢查窗口右上角的控制按鈕：")
    print("   ➖ 最小化按鈕")
    print("   ⬜ 最大化按鈕") 
    print("   ❌ 關閉按鈕")
    print("🎯 點擊「測試窗口控制」按鈕查看詳細測試說明")
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
