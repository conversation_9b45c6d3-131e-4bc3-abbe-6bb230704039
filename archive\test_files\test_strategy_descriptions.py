#!/usr/bin/env python3
"""
測試5分鐘策略說明功能
"""

import sys
import os

def test_strategy_descriptions():
    """測試策略說明功能"""
    print("🧪 測試5分鐘策略說明功能...")
    
    try:
        # 添加當前目錄到Python路徑
        sys.path.insert(0, os.getcwd())
        
        # 導入主程式模組
        import O3mh_gui_v21_optimized as main_module
        
        print("✅ 主程式模組載入成功")
        
        # 創建策略說明對話框實例來測試
        from O3mh_gui_v21_optimized import StrategyInfoDialog
        
        # 測試的5分鐘策略列表
        five_min_strategies = [
            "5分鐘突破策略",
            "5分鐘均值回歸", 
            "5分鐘動量策略",
            "5分鐘量價策略",
            "5分鐘剝頭皮",
            "5分鐘趨勢跟隨"
        ]
        
        print(f"\n📊 測試策略說明內容:")
        
        for strategy_name in five_min_strategies:
            try:
                # 創建策略說明對話框（不顯示GUI）
                dialog = StrategyInfoDialog(strategy_name)
                
                # 獲取策略說明內容
                usage_guide = dialog.get_usage_guide()
                
                # 檢查說明內容
                if strategy_name in usage_guide:
                    print(f"   ✅ {strategy_name}: 說明內容已添加")
                    
                    # 檢查關鍵內容
                    key_sections = ["策略核心理念", "最佳使用時機", "操作要點", "風險控制", "實戰技巧", "注意事項"]
                    missing_sections = []
                    
                    for section in key_sections:
                        if section not in usage_guide:
                            missing_sections.append(section)
                    
                    if missing_sections:
                        print(f"      ⚠️ 缺少章節: {', '.join(missing_sections)}")
                    else:
                        print(f"      ✅ 包含所有必要章節")
                        
                    # 檢查內容長度
                    content_length = len(usage_guide)
                    if content_length > 1000:
                        print(f"      ✅ 內容豐富 ({content_length} 字符)")
                    else:
                        print(f"      ⚠️ 內容較少 ({content_length} 字符)")
                        
                else:
                    print(f"   ❌ {strategy_name}: 使用預設說明")
                    
            except Exception as e:
                print(f"   ❌ {strategy_name}: 測試失敗 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_content_quality():
    """測試策略說明內容品質"""
    print(f"\n🧪 測試策略說明內容品質...")
    
    try:
        from O3mh_gui_v21_optimized import StrategyInfoDialog
        
        # 測試一個策略的詳細內容
        strategy_name = "5分鐘突破策略"
        dialog = StrategyInfoDialog(strategy_name)
        usage_guide = dialog.get_usage_guide()
        
        print(f"📋 {strategy_name} 內容檢查:")
        
        # 檢查必要的關鍵詞
        required_keywords = [
            "突破", "成交量", "止損", "止盈", "風險控制",
            "操作要點", "實戰技巧", "注意事項"
        ]
        
        found_keywords = []
        missing_keywords = []
        
        for keyword in required_keywords:
            if keyword in usage_guide:
                found_keywords.append(keyword)
            else:
                missing_keywords.append(keyword)
        
        print(f"   ✅ 包含關鍵詞: {', '.join(found_keywords)}")
        if missing_keywords:
            print(f"   ⚠️ 缺少關鍵詞: {', '.join(missing_keywords)}")
        
        # 檢查HTML格式
        html_tags = ["<h2>", "<h3>", "<ul>", "<li>", "<strong>"]
        html_found = [tag for tag in html_tags if tag in usage_guide]
        
        if len(html_found) >= 4:
            print(f"   ✅ HTML格式正確")
        else:
            print(f"   ⚠️ HTML格式可能有問題")
        
        # 顯示部分內容
        print(f"\n📝 內容預覽:")
        lines = usage_guide.split('\n')[:10]
        for line in lines:
            if line.strip():
                print(f"   {line.strip()[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 內容品質測試失敗: {e}")
        return False

def test_all_strategies_coverage():
    """測試所有策略的覆蓋情況"""
    print(f"\n🧪 測試所有策略覆蓋情況...")
    
    try:
        from O3mh_gui_v21_optimized import StrategyInfoDialog
        
        # 所有策略列表（包括原有策略）
        all_strategies = [
            "勝率73.45%",
            "破底反彈高量", 
            "阿水一式",
            "阿水二式",
            "藏獒",
            "CANSLIM量價齊升",
            "膽小貓",
            "二次創高股票",
            "三頻率RSI策略",
            "5分鐘突破策略",
            "5分鐘均值回歸",
            "5分鐘動量策略", 
            "5分鐘量價策略",
            "5分鐘剝頭皮",
            "5分鐘趨勢跟隨"
        ]
        
        print(f"📊 策略說明覆蓋情況:")
        
        custom_descriptions = 0
        default_descriptions = 0
        
        for strategy in all_strategies:
            try:
                dialog = StrategyInfoDialog(strategy)
                usage_guide = dialog.get_usage_guide()
                
                if strategy in usage_guide and "這是一個自定義策略" not in usage_guide:
                    print(f"   ✅ {strategy}: 自定義說明")
                    custom_descriptions += 1
                else:
                    print(f"   📝 {strategy}: 預設說明")
                    default_descriptions += 1
                    
            except Exception as e:
                print(f"   ❌ {strategy}: 錯誤 - {e}")
        
        print(f"\n📈 統計結果:")
        print(f"   自定義說明: {custom_descriptions} 個")
        print(f"   預設說明: {default_descriptions} 個")
        print(f"   總策略數: {len(all_strategies)} 個")
        print(f"   覆蓋率: {custom_descriptions/len(all_strategies)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 覆蓋情況測試失敗: {e}")
        return False

def show_strategy_usage_examples():
    """顯示策略使用範例"""
    print(f"\n💡 5分鐘策略使用範例:")
    
    examples = {
        "5分鐘突破策略": {
            "適用場景": "開盤突破、重要技術位突破",
            "操作方式": "等待突破確認 + 成交量放大 → 快速進場",
            "風險控制": "突破失敗立即止損2-3%",
            "獲利目標": "突破後4-6%或下一阻力位"
        },
        "5分鐘均值回歸": {
            "適用場景": "震盪市場、超買超賣反彈",
            "操作方式": "偏離MA20達3% + RSI極值 → 反向操作",
            "風險控制": "偏離度擴大至5%止損",
            "獲利目標": "回歸至MA20附近1-2%"
        },
        "5分鐘動量策略": {
            "適用場景": "趨勢啟動、MACD金叉死叉",
            "操作方式": "MACD信號 + 動量確認 + 量能配合",
            "風險控制": "動量衰減時及時減倉",
            "獲利目標": "跟隨動量發展，移動止盈"
        }
    }
    
    for strategy, details in examples.items():
        print(f"\n🎯 {strategy}:")
        for key, value in details.items():
            print(f"   {key}: {value}")

if __name__ == "__main__":
    print("🚀 開始測試5分鐘策略說明功能...")
    
    results = []
    
    # 執行各項測試
    results.append(("策略說明功能", test_strategy_descriptions()))
    results.append(("內容品質檢查", test_strategy_content_quality()))
    results.append(("策略覆蓋情況", test_all_strategies_coverage()))
    
    # 顯示使用範例
    show_strategy_usage_examples()
    
    # 總結結果
    print(f"\n🎉 測試完成！")
    print(f"\n📋 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎊 所有測試都通過！5分鐘策略說明功能完美運作！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n💡 如何使用策略說明功能:")
    print(f"   1. 啟動主程式")
    print(f"   2. 在策略選單中選擇任一5分鐘策略")
    print(f"   3. 點擊「📖 策略說明」按鈕")
    print(f"   4. 查看詳細的策略使用指南")
    print(f"   5. 根據說明內容進行實際操作")
    
    print(f"\n🎯 策略說明包含內容:")
    print(f"   📊 策略核心理念")
    print(f"   🎯 最佳使用時機") 
    print(f"   🔧 詳細操作要點")
    print(f"   🛡️ 風險控制方法")
    print(f"   💡 實戰操作技巧")
    print(f"   ⚠️ 重要注意事項")
    
    print(f"\n🚀 現在您可以充分理解和善用每種5分鐘策略了！")
