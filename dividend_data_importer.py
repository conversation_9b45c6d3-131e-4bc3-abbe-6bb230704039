#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息資料匯入工具
支援從CSV檔案匯入除權息資料到除權息資料庫
"""

import os
import sqlite3
import pandas as pd
import logging
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QLabel, QFileDialog, QTextEdit, 
                            QProgressBar, QGroupBox, QCheckBox, QSpinBox, QComboBox,
                            QMessageBox, QTableWidget, QTableWidgetItem)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DividendImportWorker(QThread):
    """除權息資料匯入工作執行緒"""
    
    progress_updated = pyqtSignal(int, str)
    finished_signal = pyqtSignal(bool, str, dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, csv_file, db_path, import_options):
        super().__init__()
        self.csv_file = csv_file
        self.db_path = db_path
        self.import_options = import_options
    
    def run(self):
        """執行匯入作業"""
        try:
            self.progress_updated.emit(10, "正在讀取CSV檔案...")
            
            # 讀取CSV檔案
            df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
            total_rows = len(df)
            
            self.progress_updated.emit(20, f"已讀取 {total_rows} 筆資料")
            
            # 檢查必要欄位
            required_columns = ['stock_code']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.error_signal.emit(f"CSV檔案缺少必要欄位: {', '.join(missing_columns)}")
                return
            
            self.progress_updated.emit(30, "正在處理資料...")
            
            # 處理資料
            records = []
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # 處理股票代碼
                    stock_code = str(row['stock_code']).strip()
                    if len(stock_code) < 4:
                        stock_code = stock_code.zfill(4)
                    
                    # 處理股票名稱
                    stock_name = str(row.get('stock_name', '')).strip()
                    if not stock_name or stock_name == 'nan':
                        stock_name = f"股票{stock_code}"
                    
                    # 處理年份
                    year = self.import_options.get('year', datetime.now().year)
                    if 'year' in row and pd.notna(row['year']):
                        year = int(row['year'])
                    
                    # 處理除權息日期
                    ex_dividend_date = None
                    if 'ex_dividend_date' in row and pd.notna(row['ex_dividend_date']):
                        ex_dividend_date = str(row['ex_dividend_date']).strip()
                        if ex_dividend_date and ex_dividend_date != 'nan':
                            # 嘗試標準化日期格式
                            try:
                                if '/' in ex_dividend_date:
                                    # 處理 2025/06/01 格式
                                    parts = ex_dividend_date.split('/')
                                    if len(parts) == 3:
                                        if len(parts[0]) == 2:  # '25/06/01
                                            ex_dividend_date = f"20{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                                        else:  # 2025/06/01
                                            ex_dividend_date = f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                                elif '-' not in ex_dividend_date and len(ex_dividend_date) == 8:
                                    # 處理 20250601 格式
                                    ex_dividend_date = f"{ex_dividend_date[:4]}-{ex_dividend_date[4:6]}-{ex_dividend_date[6:8]}"
                            except:
                                pass  # 保持原格式
                    
                    # 處理股利資料
                    cash_dividend = 0.0
                    if 'cash_dividend' in row and pd.notna(row['cash_dividend']):
                        try:
                            cash_dividend = float(row['cash_dividend'])
                        except:
                            cash_dividend = 0.0
                    
                    stock_dividend = 0.0
                    if 'stock_dividend' in row and pd.notna(row['stock_dividend']):
                        try:
                            stock_dividend = float(row['stock_dividend'])
                        except:
                            stock_dividend = 0.0
                    
                    total_dividend = cash_dividend + stock_dividend
                    
                    # 處理其他欄位
                    eps = None
                    if 'eps' in row and pd.notna(row['eps']):
                        try:
                            eps = float(row['eps'])
                        except:
                            eps = None
                    
                    dividend_yield = None
                    if 'dividend_yield' in row and pd.notna(row['dividend_yield']):
                        try:
                            dividend_yield = float(row['dividend_yield'])
                        except:
                            dividend_yield = None
                    
                    # 資料來源
                    data_source = str(row.get('data_source', 'csv_import')).strip()
                    if not data_source or data_source == 'nan':
                        data_source = 'csv_import'
                    
                    record = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'year': year,
                        'ex_dividend_date': ex_dividend_date,
                        'cash_dividend': cash_dividend,
                        'stock_dividend': stock_dividend,
                        'total_dividend': total_dividend,
                        'eps': eps,
                        'dividend_yield': dividend_yield,
                        'data_source': data_source
                    }
                    
                    records.append(record)
                    
                except Exception as e:
                    logger.warning(f"跳過第{index+1}行資料: {e}")
                    skipped_count += 1
                    continue
                
                # 更新進度
                if index % 100 == 0:
                    progress = 30 + int((index / total_rows) * 50)
                    self.progress_updated.emit(progress, f"已處理 {index+1}/{total_rows} 筆資料")
            
            self.progress_updated.emit(80, f"正在匯入 {len(records)} 筆資料到資料庫...")
            
            # 匯入資料庫
            success_count, updated_count = self.import_to_database(records)
            
            self.progress_updated.emit(100, "匯入完成")
            
            # 統計結果
            result_stats = {
                'total_rows': total_rows,
                'processed_rows': len(records),
                'skipped_rows': skipped_count,
                'success_count': success_count,
                'updated_count': updated_count
            }
            
            result_message = f"匯入完成！\n"
            result_message += f"總資料筆數: {total_rows}\n"
            result_message += f"成功處理: {len(records)} 筆\n"
            result_message += f"跳過無效: {skipped_count} 筆\n"
            result_message += f"新增資料: {success_count} 筆\n"
            result_message += f"更新資料: {updated_count} 筆"
            
            self.finished_signal.emit(True, result_message, result_stats)
            
        except Exception as e:
            self.error_signal.emit(f"匯入失敗: {str(e)}")
    
    def import_to_database(self, records):
        """匯入資料到資料庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            success_count = 0
            updated_count = 0
            
            for record in records:
                try:
                    # 檢查是否已存在
                    cursor.execute('''
                        SELECT id FROM dividend_data 
                        WHERE stock_code = ? AND year = ? AND data_source = ?
                    ''', (record['stock_code'], record['year'], record['data_source']))
                    
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新現有資料
                        cursor.execute('''
                            UPDATE dividend_data 
                            SET stock_name = ?, ex_dividend_date = ?, cash_dividend = ?, 
                                stock_dividend = ?, total_dividend = ?, eps = ?, 
                                dividend_yield = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE stock_code = ? AND year = ? AND data_source = ?
                        ''', (
                            record['stock_name'], record['ex_dividend_date'], 
                            record['cash_dividend'], record['stock_dividend'], 
                            record['total_dividend'], record['eps'], 
                            record['dividend_yield'], record['stock_code'], 
                            record['year'], record['data_source']
                        ))
                        updated_count += 1
                    else:
                        # 插入新資料
                        cursor.execute('''
                            INSERT INTO dividend_data 
                            (stock_code, stock_name, year, ex_dividend_date, cash_dividend, 
                             stock_dividend, total_dividend, eps, dividend_yield, 
                             data_source, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ''', (
                            record['stock_code'], record['stock_name'], record['year'],
                            record['ex_dividend_date'], record['cash_dividend'], 
                            record['stock_dividend'], record['total_dividend'], 
                            record['eps'], record['dividend_yield'], record['data_source']
                        ))
                        success_count += 1
                        
                except Exception as e:
                    logger.warning(f"儲存資料失敗 {record['stock_code']}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return success_count, updated_count
            
        except Exception as e:
            logger.error(f"資料庫操作失敗: {e}")
            raise

class DividendDataImporter(QMainWindow):
    """除權息資料匯入工具主視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("除權息資料匯入工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2a2a2a;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #ffffff;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
            QTextEdit {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
            }
            QLabel {
                color: #ffffff;
            }
            QComboBox, QSpinBox {
                background-color: #333333;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        
        self.init_ui()
        
        # 預設資料庫路徑
        self.db_path = "D:/Finlab/history/tables/dividend_data.db"
        self.csv_file = None
        self.import_worker = None
    
    def init_ui(self):
        """初始化用戶界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("📊 除權息資料匯入工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c; margin: 15px;")
        layout.addWidget(title_label)
        
        # 檔案選擇區域
        file_group = QGroupBox("📁 選擇CSV檔案")
        file_layout = QVBoxLayout(file_group)
        
        file_button_layout = QHBoxLayout()
        self.select_file_btn = QPushButton("🔍 選擇CSV檔案")
        self.select_file_btn.clicked.connect(self.select_csv_file)
        file_button_layout.addWidget(self.select_file_btn)
        
        self.file_label = QLabel("尚未選擇檔案")
        self.file_label.setStyleSheet("color: #888888; font-style: italic;")
        file_button_layout.addWidget(self.file_label)
        file_button_layout.addStretch()
        
        file_layout.addLayout(file_button_layout)
        layout.addWidget(file_group)
        
        # 匯入選項
        options_group = QGroupBox("⚙️ 匯入選項")
        options_layout = QVBoxLayout(options_group)
        
        # 年份設定
        year_layout = QHBoxLayout()
        year_layout.addWidget(QLabel("預設年份:"))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2010, 2030)
        self.year_spin.setValue(datetime.now().year)
        year_layout.addWidget(self.year_spin)
        year_layout.addStretch()
        options_layout.addLayout(year_layout)
        
        # 覆蓋選項
        self.overwrite_check = QCheckBox("覆蓋現有資料")
        self.overwrite_check.setChecked(True)
        options_layout.addWidget(self.overwrite_check)
        
        layout.addWidget(options_group)
        
        # 進度區域
        progress_group = QGroupBox("📊 匯入進度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("準備就緒")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 操作按鈕
        button_layout = QHBoxLayout()
        
        self.import_btn = QPushButton("🚀 開始匯入")
        self.import_btn.clicked.connect(self.start_import)
        self.import_btn.setEnabled(False)
        button_layout.addWidget(self.import_btn)
        
        self.preview_btn = QPushButton("👁️ 預覽資料")
        self.preview_btn.clicked.connect(self.preview_data)
        self.preview_btn.setEnabled(False)
        button_layout.addWidget(self.preview_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 日誌區域
        log_group = QGroupBox("📝 匯入日誌")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """記錄日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def select_csv_file(self):
        """選擇CSV檔案"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, 
            "選擇除權息CSV檔案", 
            "", 
            "CSV檔案 (*.csv);;所有檔案 (*)"
        )
        
        if file_path:
            self.csv_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setStyleSheet("color: #4caf50; font-weight: bold;")
            
            self.import_btn.setEnabled(True)
            self.preview_btn.setEnabled(True)
            
            self.log(f"✅ 已選擇檔案: {os.path.basename(file_path)}")
    
    def preview_data(self):
        """預覽CSV資料"""
        if not self.csv_file:
            return
        
        try:
            self.log("👁️ 正在預覽CSV資料...")
            
            # 讀取前10行資料
            df = pd.read_csv(self.csv_file, encoding='utf-8-sig', nrows=10)
            
            preview_text = f"📊 檔案預覽 (前10行):\n"
            preview_text += f"總欄位數: {len(df.columns)}\n"
            preview_text += f"欄位名稱: {', '.join(df.columns)}\n\n"
            
            # 顯示資料樣本
            for index, row in df.iterrows():
                preview_text += f"第{index+1}行: "
                preview_text += f"代碼:{row.get('stock_code', 'N/A')} "
                preview_text += f"名稱:{row.get('stock_name', 'N/A')} "
                preview_text += f"日期:{row.get('ex_dividend_date', 'N/A')} "
                preview_text += f"股利:{row.get('cash_dividend', 'N/A')}\n"
            
            QMessageBox.information(self, "資料預覽", preview_text)
            self.log("✅ 資料預覽完成")
            
        except Exception as e:
            error_msg = f"❌ 預覽失敗: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "預覽錯誤", error_msg)
    
    def start_import(self):
        """開始匯入"""
        if not self.csv_file:
            QMessageBox.warning(self, "警告", "請先選擇CSV檔案")
            return
        
        if not os.path.exists(self.db_path):
            QMessageBox.critical(self, "錯誤", f"除權息資料庫不存在: {self.db_path}")
            return
        
        # 準備匯入選項
        import_options = {
            'year': self.year_spin.value(),
            'overwrite': self.overwrite_check.isChecked()
        }
        
        # 禁用按鈕
        self.import_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        self.select_file_btn.setEnabled(False)
        
        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.log("🚀 開始匯入除權息資料...")
        
        # 創建工作執行緒
        self.import_worker = DividendImportWorker(self.csv_file, self.db_path, import_options)
        self.import_worker.progress_updated.connect(self.on_progress_updated)
        self.import_worker.finished_signal.connect(self.on_import_finished)
        self.import_worker.error_signal.connect(self.on_import_error)
        
        self.import_worker.start()
    
    def on_progress_updated(self, value, message):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log(message)
    
    def on_import_finished(self, success, message, stats):
        """匯入完成"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("匯入完成")
        
        # 重新啟用按鈕
        self.import_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        self.select_file_btn.setEnabled(True)
        
        if success:
            self.log("🎉 匯入成功完成！")
            QMessageBox.information(self, "匯入成功", message)
        else:
            self.log("❌ 匯入失敗")
            QMessageBox.critical(self, "匯入失敗", message)
    
    def on_import_error(self, error_message):
        """匯入錯誤"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("匯入失敗")
        
        # 重新啟用按鈕
        self.import_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        self.select_file_btn.setEnabled(True)
        
        self.log(f"❌ 匯入錯誤: {error_message}")
        QMessageBox.critical(self, "匯入錯誤", error_message)

def main():
    """主函數"""
    app = QApplication([])
    
    # 設置應用程式資訊
    app.setApplicationName("除權息資料匯入工具")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = DividendDataImporter()
    window.show()
    
    # 運行應用程式
    app.exec()

if __name__ == "__main__":
    main()
