#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試證交所和櫃買中心官方API的除權息資料獲取
"""

import requests
import json
import urllib3
from datetime import datetime

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_twse_api():
    """測試證交所API"""
    print("🔍 測試證交所除權息API...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試不同的API端點
    apis_to_test = [
        {
            'name': '除權息公告',
            'url': 'https://www.twse.com.tw/exchangeReport/TWT49U',
            'params': {
                'response': 'json',
                'date': '20240101',
                'selectType': 'ALL'
            }
        },
        {
            'name': '除權息計算結果表',
            'url': 'https://www.twse.com.tw/exchangeReport/TWTAUU',
            'params': {
                'response': 'json',
                'date': '20240701'
            }
        }
    ]
    
    for api in apis_to_test:
        try:
            print(f"\n📡 測試 {api['name']}...")
            print(f"URL: {api['url']}")
            print(f"參數: {api['params']}")
            
            response = requests.get(api['url'], params=api['params'], headers=headers, timeout=30, verify=False)
            print(f"狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"回應格式: JSON")
                    print(f"主要鍵值: {list(data.keys())}")
                    
                    if 'data' in data and data['data']:
                        print(f"資料筆數: {len(data['data'])}")
                        print("前3筆資料範例:")
                        for i, record in enumerate(data['data'][:3]):
                            print(f"  第{i+1}筆: {record}")
                    elif 'aaData' in data and data['aaData']:
                        print(f"資料筆數: {len(data['aaData'])}")
                        print("前3筆資料範例:")
                        for i, record in enumerate(data['aaData'][:3]):
                            print(f"  第{i+1}筆: {record}")
                    else:
                        print("⚠️ 無資料或資料格式不符預期")
                        print(f"完整回應: {data}")
                        
                except json.JSONDecodeError:
                    print(f"回應格式: 非JSON")
                    print(f"回應內容前500字元: {response.text[:500]}")
            else:
                print(f"❌ 請求失敗: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ API測試失敗: {e}")

def test_tpex_api():
    """測試櫃買中心API"""
    print("\n\n🔍 測試櫃買中心除權息API...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試不同的API端點
    apis_to_test = [
        {
            'name': '除權息資料',
            'url': 'https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php',
            'params': {
                'l': 'zh-tw',
                'd': '113/07/01',  # 民國年格式
                'stkno': '',
                'o': 'json'
            }
        },
        {
            'name': '除權息計算結果',
            'url': 'https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php',
            'params': {
                'l': 'zh-tw',
                'd': '113/07/01',
                'o': 'json'
            }
        }
    ]
    
    for api in apis_to_test:
        try:
            print(f"\n📡 測試 {api['name']}...")
            print(f"URL: {api['url']}")
            print(f"參數: {api['params']}")
            
            response = requests.get(api['url'], params=api['params'], headers=headers, timeout=30, verify=False)
            print(f"狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"回應格式: JSON")
                    print(f"主要鍵值: {list(data.keys())}")
                    
                    if 'aaData' in data and data['aaData']:
                        print(f"資料筆數: {len(data['aaData'])}")
                        print("前3筆資料範例:")
                        for i, record in enumerate(data['aaData'][:3]):
                            print(f"  第{i+1}筆: {record}")
                    else:
                        print("⚠️ 無資料或資料格式不符預期")
                        print(f"完整回應: {data}")
                        
                except json.JSONDecodeError:
                    print(f"回應格式: 非JSON")
                    print(f"回應內容前500字元: {response.text[:500]}")
            else:
                print(f"❌ 請求失敗: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ API測試失敗: {e}")

if __name__ == "__main__":
    print("🚀 開始測試官方除權息API...")
    test_twse_api()
    test_tpex_api()
    print("\n✅ API測試完成")
