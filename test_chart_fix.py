#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試圖表修復功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class ChartFixTestWindow(QMainWindow):
    """圖表修復測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📈 圖表修復功能測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("📈 圖表修復功能測試")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4ff; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 功能說明
        info_text = QTextEdit()
        info_text.setMaximumHeight(300)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3 style="color: #ffffff;">🔧 圖表修復內容</h3>
        
        <h4 style="color: #cccccc;">📊 歷史指數圖表修復</h4>
        <ul>
            <li><b>數據處理</b> - 正確處理日期格式和收盤指數數據</li>
            <li><b>日期分組</b> - 按日期分組，處理同一天多筆記錄的情況</li>
            <li><b>時間軸</b> - 正確設置X軸為日期軸，Y軸為收盤指數</li>
            <li><b>數據範圍</b> - 自動調整Y軸範圍，留適當邊距</li>
        </ul>
        
        <h4 style="color: #cccccc;">📈 市場指數圖表修復</h4>
        <ul>
            <li><b>分類軸</b> - 使用股票代號作為X軸，避免時間戳重疊問題</li>
            <li><b>數據分散</b> - 將數據點分散在X軸上，而不是擠在一條線上</li>
            <li><b>標籤顯示</b> - 當股票數量不多時顯示股票代號標籤</li>
            <li><b>範圍調整</b> - 自動調整X和Y軸範圍以最佳顯示</li>
        </ul>
        
        <h4 style="color: #cccccc;">💰 融資融券圖表修復</h4>
        <ul>
            <li><b>股票軸</b> - 使用股票代號作為X軸分類</li>
            <li><b>餘額顯示</b> - Y軸顯示融資餘額，單位為千元</li>
            <li><b>數據排序</b> - 按股票代號排序，便於查看</li>
            <li><b>視覺優化</b> - 調整柱寬和顏色，提升視覺效果</li>
        </ul>
        
        <h3 style="color: #ffffff;">🎯 修復前後對比</h3>
        
        <table style="color: #ffffff; border-collapse: collapse; width: 100%;">
        <tr style="background-color: #333333;">
            <th style="border: 1px solid #555; padding: 8px;">問題</th>
            <th style="border: 1px solid #555; padding: 8px;">修復前</th>
            <th style="border: 1px solid #555; padding: 8px;">修復後</th>
        </tr>
        <tr>
            <td style="border: 1px solid #555; padding: 8px;">X軸顯示</td>
            <td style="border: 1px solid #555; padding: 8px;">所有點擠在一條垂直線</td>
            <td style="border: 1px solid #555; padding: 8px;">正確分散在X軸上</td>
        </tr>
        <tr style="background-color: #2a2a2a;">
            <td style="border: 1px solid #555; padding: 8px;">時間軸</td>
            <td style="border: 1px solid #555; padding: 8px;">時間戳相同導致重疊</td>
            <td style="border: 1px solid #555; padding: 8px;">使用分類軸或正確的日期軸</td>
        </tr>
        <tr>
            <td style="border: 1px solid #555; padding: 8px;">數據範圍</td>
            <td style="border: 1px solid #555; padding: 8px;">軸範圍不當</td>
            <td style="border: 1px solid #555; padding: 8px;">自動調整最佳範圍</td>
        </tr>
        </table>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟台股爬蟲 (測試修復後的圖表)")
        test_btn.clicked.connect(self.open_crawler_dialog)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 成功提示
        success_label = QLabel("🎉 圖表顯示問題已修復，現在可以正確顯示分散的數據點！")
        success_label.setStyleSheet("color: #00ff00; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #2d4a2d; border: 1px solid #4a7c4a; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 初始化日誌
        self.log("📈 圖表修復功能測試程式已啟動")
        self.log("✅ 歷史指數圖表修復完成")
        self.log("✅ 市場指數圖表修復完成")
        self.log("✅ 融資融券圖表修復完成")
        self.log("✅ X軸分散顯示問題已解決")
    
    def log(self, message):
        """添加日誌"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
    
    def get_timestamp(self):
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def open_crawler_dialog(self):
        """開啟爬蟲對話框"""
        try:
            self.log("🚀 正在開啟台股爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("📈 請測試以下圖表功能：")
            self.log("   • 切換到「圖表分析」分頁")
            self.log("   • 選擇不同的數據類型和圖表類型")
            self.log("   • 觀察數據點是否正確分散在X軸上")
            self.log("   • 檢查軸標籤和範圍是否合理")
            
            # 顯示對話框
            dialog.exec()
            
            self.log("✅ 爬蟲對話框已關閉")
            
        except Exception as e:
            self.log(f"❌ 開啟爬蟲界面失敗: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("圖表修復功能測試")
    app.setApplicationVersion("1.0")
    
    print("📈 圖表修復功能測試程式已啟動")
    print("✅ 圖表顯示問題已修復")
    print("✅ X軸分散顯示已實現")
    
    # 創建主窗口
    window = ChartFixTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
