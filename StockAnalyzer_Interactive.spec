# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('app_config.json', '.'), ('finmind_config.json', '.')]
binaries = []
hiddenimports = ['PyQt6', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'pyqtgraph', 'pyqtgraph.graphicsItems', 'pyqtgraph.graphicsItems.PlotItem', 'pyqtgraph.graphicsItems.ViewBox', 'pyqtgraph.graphicsItems.AxisItem', 'pyqtgraph.graphicsItems.LegendItem', 'pyqtgraph.graphicsItems.TextItem', 'pyqtgraph.graphicsItems.InfiniteLine', 'pyqtgraph.graphicsItems.ScatterPlotItem', 'pyqtgraph.graphicsItems.BarGraphItem', 'pyqtgraph.SignalProxy', 'pandas', 'numpy', 'sqlite3', 'requests', 'yfinance', 'finmind', 'selenium', 'bs4', 'lxml', 'openpyxl', 'xlsxwriter']
tmp_ret = collect_all('pyqtgraph')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('PyQt6')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5', 'PySide2', 'PySide6', 'tkinter'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='StockAnalyzer_Interactive',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
