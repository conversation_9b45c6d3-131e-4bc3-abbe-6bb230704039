#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終界面測試
測試修改後的界面更新是否正常工作
"""

import sys
import time
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_callback_with_debug():
    """測試帶調試信息的回調"""
    print("Testing callback with debug info...")
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        # 模擬主程式的界面組件
        class MockMainWindow:
            def __init__(self):
                self.market_sentiment_label = MockLabel("Market Sentiment")
                self.us_status_label = MockLabel("US Status")
                self.tw_futures_label = MockLabel("TW Futures")
                self.premarket_status_label = MockLabel("Premarket Status")
                self.market_details_text = MockTextEdit("Market Details")
                self.pre_market_monitor = monitor
            
            def show_status(self, message, is_error=False):
                print(f"STATUS: {message} {'(ERROR)' if is_error else ''}")
            
            def update_market_dashboard(self, results):
                print("MOCK: update_market_dashboard called")
                if hasattr(self, 'market_sentiment_label'):
                    try:
                        sentiment = self.pre_market_monitor.get_market_sentiment()
                        self.market_sentiment_label.setText(f"Market Sentiment: {sentiment}")
                        print(f"  Updated sentiment: {sentiment}")
                    except Exception as e:
                        print(f"  Sentiment update failed: {e}")
                
                # 更新美股狀態
                us_data = results.get('us_indices', {})
                if us_data and hasattr(self, 'us_status_label'):
                    us_changes = [data.get('change_pct', 0) for data in us_data.values() 
                                if isinstance(data, dict) and 'change_pct' in data]
                    if us_changes:
                        avg_change = sum(us_changes) / len(us_changes)
                        trend = "UP" if avg_change > 0 else "DOWN" if avg_change < 0 else "FLAT"
                        self.us_status_label.setText(f"US: {trend} {avg_change:+.2f}%")
                        print(f"  Updated US status: {trend} {avg_change:+.2f}%")
            
            def update_market_details(self, results):
                print("MOCK: update_market_details called")
                if hasattr(self, 'market_details_text'):
                    total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                    details = f"Market Details: {total_items} items from {results.get('timestamp', 'unknown')}"
                    self.market_details_text.setText(details)
                    print(f"  Updated details: {details}")
            
            def _on_scan_success(self, results):
                """模擬主程式的成功回調"""
                try:
                    print("MOCK: _on_scan_success called")
                    print(f"  Results type: {type(results)}")
                    if results:
                        print(f"  Results keys: {list(results.keys()) if isinstance(results, dict) else 'Not dict'}")
                        
                        print("  Calling update_market_dashboard...")
                        self.update_market_dashboard(results)
                        
                        print("  Calling update_market_details...")
                        self.update_market_details(results)
                        
                        # 更新狀態標籤
                        if hasattr(self, 'premarket_status_label'):
                            total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                            timestamp = results.get('timestamp', 'unknown')
                            self.premarket_status_label.setText(f"Scan Complete ({timestamp}) - {total_items} items")
                            print(f"  Updated premarket status: {total_items} items")
                        
                        self.show_status("Scan completed successfully")
                        print("MOCK: _on_scan_success completed")
                    else:
                        print("  No results")
                        self.show_status("Scan failed: No results", is_error=True)
                        
                except Exception as e:
                    print(f"MOCK: _on_scan_success failed: {e}")
        
        class MockLabel:
            def __init__(self, name):
                self.name = name
                self._text = f"Initial {name}"
            
            def setText(self, text):
                self._text = text
                print(f"    LABEL[{self.name}]: '{text}'")
            
            def text(self):
                return self._text
        
        class MockTextEdit:
            def __init__(self, name):
                self.name = name
                self._text = f"Initial {name}"
            
            def setText(self, text):
                self._text = text
                print(f"    TEXT[{self.name}]: '{text}'")
            
            def toPlainText(self):
                return self._text
        
        # 創建模擬主窗口
        mock_window = MockMainWindow()
        
        # 創建 ScanWorker
        scan_worker = ScanWorker(
            monitor,
            callback_success=mock_window._on_scan_success,
            callback_error=lambda msg: print(f"ERROR CALLBACK: {msg}")
        )
        
        print("Starting scan...")
        scan_worker.start_scan()
        
        # 等待完成
        print("Waiting for completion...")
        time.sleep(10)
        
        print("Test completed!")
        
        # 顯示最終狀態
        print("\nFinal component states:")
        print(f"  Market Sentiment: {mock_window.market_sentiment_label.text()}")
        print(f"  US Status: {mock_window.us_status_label.text()}")
        print(f"  Premarket Status: {mock_window.premarket_status_label.text()}")
        print(f"  Market Details: {mock_window.market_details_text.toPlainText()}")
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    """主函數"""
    print("Final UI Test")
    print("=" * 50)
    
    success = test_callback_with_debug()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("If you see component updates above, the UI should work in the main program.")
    else:
        print("\n❌ Test failed!")
    
    print("\nNext steps:")
    print("1. Run the main program")
    print("2. Click the scan button")
    print("3. Check if you see the DEBUG messages in the console")
    print("4. Check if the UI components update")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Test failed: {e}")
