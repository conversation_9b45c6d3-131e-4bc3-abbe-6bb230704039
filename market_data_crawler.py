#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市場數據爬蟲模組
整合台灣證交所 OpenAPI 的市場數據功能
包含：市場指數資訊、融資融券統計、發行量加權股價指數歷史資料
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TWSEMarketDataAPI:
    """台灣證交所市場數據 API 客戶端"""
    
    BASE_URL = "https://openapi.twse.com.tw/v1"
    USER_AGENT = "finlab-market-crawler/1.0"
    
    @classmethod
    def get_data(cls, endpoint: str, timeout: float = 30.0) -> List[Dict[str, Any]]:
        """
        從 TWSE API 獲取數據
        
        Args:
            endpoint: API 端點路徑
            timeout: 請求超時時間
            
        Returns:
            包含 API 響應數據的字典列表
        """
        url = f"{cls.BASE_URL}{endpoint}"
        logger.info(f"正在獲取 TWSE 數據: {url}")
        
        try:
            response = requests.get(
                url,
                headers={
                    "User-Agent": cls.USER_AGENT,
                    "Accept": "application/json"
                },
                verify=False,  # 跳過 SSL 憑證驗證
                timeout=timeout
            )
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            data = response.json()
            return data if isinstance(data, list) else [data] if data else []
            
        except Exception as e:
            logger.error(f"從 {url} 獲取數據失敗: {e}")
            raise
    
    @classmethod
    def get_latest_market_data(cls, endpoint: str, count: int = 1, timeout: float = 30.0) -> List[Dict[str, Any]]:
        """
        獲取最新的市場數據
        
        Args:
            endpoint: API 端點路徑
            count: 返回最新記錄數量
            timeout: 請求超時時間
            
        Returns:
            最新市場數據記錄列表
        """
        try:
            data = cls.get_data(endpoint, timeout)
            return data[-count:] if data else []
        except Exception as e:
            logger.error(f"獲取最新市場數據失敗: {e}")
            return []

class MarketDataCrawler:
    """市場數據爬蟲"""
    
    def __init__(self, db_path: str = "D:/Finlab/history/tables"):
        """
        初始化市場數據爬蟲
        
        Args:
            db_path: 資料庫存儲路徑
        """
        self.db_path = db_path
        self.api_client = TWSEMarketDataAPI()
        
        # 確保目錄存在
        os.makedirs(db_path, exist_ok=True)
    
    def get_market_index_info(self) -> pd.DataFrame:
        """
        獲取市場指數資訊

        Returns:
            包含市場指數資訊的 DataFrame
        """
        logger.info("🔍 獲取市場指數資訊...")

        try:
            # 使用 TWSE OpenAPI 獲取市場指數資訊
            # 獲取更多數據而不是只有1筆
            data = self.api_client.get_data("/exchangeReport/MI_INDEX")

            if not data:
                logger.warning("⚠️ 未獲取到市場指數資訊")
                return pd.DataFrame()

            # 轉換為 DataFrame
            df = pd.DataFrame(data)

            # 添加爬取時間
            df['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            logger.info(f"✅ 成功獲取市場指數資訊: {len(df)} 筆記錄")
            return df

        except Exception as e:
            logger.error(f"❌ 獲取市場指數資訊失敗: {e}")
            return pd.DataFrame()
    
    def get_margin_trading_info(self) -> pd.DataFrame:
        """
        獲取融資融券統計
        
        Returns:
            包含融資融券統計的 DataFrame
        """
        logger.info("🔍 獲取融資融券統計...")
        
        try:
            # 使用 TWSE OpenAPI 獲取融資融券統計
            data = self.api_client.get_data("/exchangeReport/MI_MARGN")
            
            if not data:
                logger.warning("⚠️ 未獲取到融資融券統計")
                return pd.DataFrame()
            
            # 只取前50筆避免資料過多
            data = data[:50] if len(data) > 50 else data
            
            # 轉換為 DataFrame
            df = pd.DataFrame(data)
            
            # 添加爬取時間
            df['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            logger.info(f"✅ 成功獲取融資融券統計: {len(df)} 筆記錄")
            return df
            
        except Exception as e:
            logger.error(f"❌ 獲取融資融券統計失敗: {e}")
            return pd.DataFrame()
    
    def get_market_historical_index(self, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        獲取發行量加權股價指數歷史資料

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)

        Returns:
            包含歷史指數資料的 DataFrame
        """
        logger.info(f"🔍 獲取發行量加權股價指數歷史資料 ({start_date} 至 {end_date})...")

        try:
            all_data = []

            if start_date and end_date:
                # 如果指定了日期範圍，逐日獲取數據
                current_date = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

                while current_date <= end_date_obj:
                    date_str = current_date.strftime('%Y%m%d')

                    try:
                        # 使用證交所的歷史數據API
                        endpoint = f"/exchangeReport/STOCK_DAY_ALL?date={date_str}"
                        daily_data = self.api_client.get_data(endpoint)

                        if daily_data:
                            # 處理每日數據
                            for item in daily_data:
                                if isinstance(item, dict):
                                    item['Date'] = current_date.strftime('%Y%m%d')
                                    item['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    all_data.append(item)

                        logger.info(f"📅 {current_date.strftime('%Y-%m-%d')}: 獲取 {len(daily_data) if daily_data else 0} 筆數據")

                        # 避免請求過於頻繁
                        time.sleep(1)

                    except Exception as e:
                        logger.warning(f"⚠️ {current_date.strftime('%Y-%m-%d')} 數據獲取失敗: {e}")

                    current_date += timedelta(days=1)
            else:
                # 如果沒有指定日期範圍，獲取最近的數據
                data = self.api_client.get_data("/indicesReport/MI_5MINS_HIST")
                if data:
                    all_data = data

            if not all_data:
                logger.warning("⚠️ 未獲取到歷史指數資料")
                return pd.DataFrame()

            # 轉換為 DataFrame
            df = pd.DataFrame(all_data)

            # 如果沒有 crawl_time 欄位，添加它
            if 'crawl_time' not in df.columns:
                df['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            logger.info(f"✅ 成功獲取歷史指數資料: {len(df)} 筆記錄")
            return df

        except Exception as e:
            logger.error(f"❌ 獲取歷史指數資料失敗: {e}")
            return pd.DataFrame()

    def get_market_historical_index_with_progress(self, start_date: str = None, end_date: str = None, progress_callback=None) -> pd.DataFrame:
        """
        獲取發行量加權股價指數歷史資料（帶進度回調）

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            progress_callback: 進度回調函數 (current_day, total_days)

        Returns:
            包含歷史指數資料的 DataFrame
        """
        logger.info(f"🔍 獲取發行量加權股價指數歷史資料 ({start_date} 至 {end_date})...")

        try:
            all_data = []

            if start_date and end_date:
                # 如果指定了日期範圍，逐日獲取數據
                current_date = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

                # 計算總天數
                total_days = (end_date_obj - current_date).days + 1
                current_day = 0

                while current_date <= end_date_obj:
                    current_day += 1
                    date_str = current_date.strftime('%Y%m%d')

                    # 調用進度回調
                    if progress_callback:
                        progress_callback(current_day, total_days)

                    try:
                        # 使用證交所的歷史數據API
                        endpoint = f"/exchangeReport/STOCK_DAY_ALL?date={date_str}"
                        daily_data = self.api_client.get_data(endpoint)

                        if daily_data:
                            # 處理每日數據
                            for item in daily_data:
                                if isinstance(item, dict):
                                    item['Date'] = current_date.strftime('%Y%m%d')
                                    item['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    all_data.append(item)

                        logger.info(f"📅 {current_date.strftime('%Y-%m-%d')}: 獲取 {len(daily_data) if daily_data else 0} 筆數據")

                        # 避免請求過於頻繁
                        time.sleep(1)

                    except Exception as e:
                        logger.warning(f"⚠️ {current_date.strftime('%Y-%m-%d')} 數據獲取失敗: {e}")

                    current_date += timedelta(days=1)
            else:
                # 如果沒有指定日期範圍，獲取最近的數據
                if progress_callback:
                    progress_callback(1, 1)
                data = self.api_client.get_data("/indicesReport/MI_5MINS_HIST")
                if data:
                    all_data = data

            if not all_data:
                logger.warning("⚠️ 未獲取到歷史指數資料")
                return pd.DataFrame()

            # 轉換為 DataFrame
            df = pd.DataFrame(all_data)

            # 如果沒有 crawl_time 欄位，添加它
            if 'crawl_time' not in df.columns:
                df['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            logger.info(f"✅ 成功獲取歷史指數資料: {len(df)} 筆記錄")
            return df

        except Exception as e:
            logger.error(f"❌ 獲取歷史指數資料失敗: {e}")
            return pd.DataFrame()
    
    def save_to_database(self, df: pd.DataFrame, table_name: str, db_name: str) -> bool:
        """
        將數據保存到資料庫
        
        Args:
            df: 要保存的 DataFrame
            table_name: 表格名稱
            db_name: 資料庫名稱
            
        Returns:
            保存是否成功
        """
        if df.empty:
            logger.warning(f"⚠️ DataFrame 為空，跳過保存到 {table_name}")
            return False
        
        try:
            db_file = os.path.join(self.db_path, f"{db_name}.db")
            
            with sqlite3.connect(db_file) as conn:
                # 使用 replace 模式避免重複數據
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                
            logger.info(f"✅ 成功保存 {len(df)} 筆記錄到 {db_file} 的 {table_name} 表格")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存數據到資料庫失敗: {e}")
            return False
    
    def crawl_market_index_info(self) -> bool:
        """
        爬取並保存市場指數資訊
        
        Returns:
            爬取是否成功
        """
        logger.info("🚀 開始爬取市場指數資訊")
        
        try:
            # 獲取數據
            df = self.get_market_index_info()
            
            if df.empty:
                return False
            
            # 保存到資料庫
            success = self.save_to_database(df, 'market_index_info', 'market_index')
            
            if success:
                logger.info("🎉 市場指數資訊爬取完成")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 市場指數資訊爬取失敗: {e}")
            return False
    
    def crawl_margin_trading_info(self) -> bool:
        """
        爬取並保存融資融券統計
        
        Returns:
            爬取是否成功
        """
        logger.info("🚀 開始爬取融資融券統計")
        
        try:
            # 獲取數據
            df = self.get_margin_trading_info()
            
            if df.empty:
                return False
            
            # 保存到資料庫
            success = self.save_to_database(df, 'margin_trading_info', 'margin_trading')
            
            if success:
                logger.info("🎉 融資融券統計爬取完成")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 融資融券統計爬取失敗: {e}")
            return False
    
    def crawl_market_historical_index(self, start_date: str = None, end_date: str = None) -> bool:
        """
        爬取並保存發行量加權股價指數歷史資料

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)

        Returns:
            爬取是否成功
        """
        logger.info(f"🚀 開始爬取發行量加權股價指數歷史資料 ({start_date} 至 {end_date})")

        try:
            # 獲取數據
            df = self.get_market_historical_index(start_date, end_date)

            if df.empty:
                return False

            # 保存到資料庫
            success = self.save_to_database(df, 'market_historical_index', 'market_historical')

            if success:
                logger.info("🎉 發行量加權股價指數歷史資料爬取完成")

            return success

        except Exception as e:
            logger.error(f"❌ 發行量加權股價指數歷史資料爬取失敗: {e}")
            return False

    def crawl_market_historical_index_with_progress(self, start_date: str = None, end_date: str = None, progress_callback=None) -> bool:
        """
        爬取並保存發行量加權股價指數歷史資料（帶進度回調）

        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            progress_callback: 進度回調函數 (current_day, total_days)

        Returns:
            爬取是否成功
        """
        logger.info(f"🚀 開始爬取發行量加權股價指數歷史資料 ({start_date} 至 {end_date})")

        try:
            # 獲取數據（帶進度回調）
            df = self.get_market_historical_index_with_progress(start_date, end_date, progress_callback)

            if df.empty:
                return False

            # 保存到資料庫
            success = self.save_to_database(df, 'market_historical_index', 'market_historical')

            if success:
                logger.info("🎉 發行量加權股價指數歷史資料爬取完成")

            return success

        except Exception as e:
            logger.error(f"❌ 發行量加權股價指數歷史資料爬取失敗: {e}")
            return False
    
    def crawl_all_market_data(self) -> Dict[str, bool]:
        """
        爬取所有市場數據
        
        Returns:
            各項爬取結果的字典
        """
        logger.info("🚀 開始爬取所有市場數據")
        
        results = {}
        
        # 爬取市場指數資訊
        results['market_index_info'] = self.crawl_market_index_info()
        time.sleep(2)  # 避免請求過於頻繁
        
        # 爬取融資融券統計
        results['margin_trading_info'] = self.crawl_margin_trading_info()
        time.sleep(2)
        
        # 爬取歷史指數資料
        results['market_historical_index'] = self.crawl_market_historical_index()
        
        # 統計結果
        success_count = sum(results.values())
        total_count = len(results)
        
        logger.info(f"📊 市場數據爬取完成: {success_count}/{total_count} 成功")
        
        return results

def main():
    """主函數 - 測試市場數據爬蟲"""
    
    print("🔍 台灣證交所市場數據爬蟲測試")
    print("=" * 60)
    
    # 創建爬蟲實例
    crawler = MarketDataCrawler()
    
    # 爬取所有市場數據
    results = crawler.crawl_all_market_data()
    
    # 顯示結果
    print(f"\n📊 爬取結果:")
    for data_type, success in results.items():
        status = "✅ 成功" if success else "❌ 失敗"
        print(f"   {data_type}: {status}")
    
    print(f"\n💡 數據已保存到: {crawler.db_path}")

if __name__ == "__main__":
    main()
