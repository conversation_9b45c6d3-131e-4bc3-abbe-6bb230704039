#!/usr/bin/env python3
"""
測試 Twelve Data API 支援的台股格式
"""

import requests
import time
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

API_KEY = "42c14f2a63c1420798627dce49fd682a"
BASE_URL = "https://api.twelvedata.com"

def test_symbol_format(symbol_format):
    """測試特定的股票代碼格式"""
    url = f"{BASE_URL}/time_series"
    params = {
        'symbol': symbol_format,
        'interval': '1day',
        'outputsize': 5,
        'apikey': API_KEY,
        'format': 'JSON'
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'status' in data and data['status'] == 'error':
                return False, data.get('message', '未知錯誤')
            elif 'values' in data and data['values']:
                return True, f"成功獲取 {len(data['values'])} 筆數據"
            else:
                return False, "無數據返回"
        else:
            return False, f"HTTP錯誤: {response.status_code}"
            
    except Exception as e:
        return False, f"請求失敗: {e}"

def test_taiwan_stock_formats():
    """測試台股的各種格式"""
    print("🧪 測試 Twelve Data API 台股格式支援...")
    
    # 測試台積電 (2330) 的各種格式
    test_formats = [
        "2330.TW",
        "2330.TWO", 
        "2330.TPE",
        "TSE:2330",
        "TPE:2330",
        "TWSE:2330",
        "2330",
        "02330.TW",
        "TW:2330",
        "TAIWAN:2330"
    ]
    
    print(f"\n📊 測試台積電 (2330) 的不同格式:")
    
    successful_formats = []
    
    for symbol_format in test_formats:
        print(f"\n🔍 測試格式: {symbol_format}")
        
        success, message = test_symbol_format(symbol_format)
        
        if success:
            print(f"   ✅ 成功: {message}")
            successful_formats.append(symbol_format)
        else:
            print(f"   ❌ 失敗: {message}")
        
        # 避免API速率限制
        time.sleep(8)
    
    print(f"\n🎉 測試完成！")
    print(f"✅ 成功的格式: {successful_formats}")
    
    return successful_formats

def test_other_taiwan_stocks():
    """測試其他台股"""
    print(f"\n📈 測試其他台股...")
    
    # 如果找到成功的格式，測試其他股票
    test_stocks = ["2317", "2454", "3008", "2412"]
    
    # 使用最可能的格式
    formats_to_try = ["2330.TW", "2330.TWO", "TSE:2330"]
    
    for stock in test_stocks:
        print(f"\n🔍 測試股票: {stock}")
        
        for fmt_template in formats_to_try:
            symbol_format = fmt_template.replace("2330", stock)
            
            success, message = test_symbol_format(symbol_format)
            
            if success:
                print(f"   ✅ {symbol_format}: {message}")
                break
            else:
                print(f"   ❌ {symbol_format}: {message}")
            
            time.sleep(2)

def test_api_info():
    """測試API基本信息"""
    print(f"\n📊 測試API基本信息...")
    
    # 測試API使用情況
    url = f"{BASE_URL}/api_usage"
    params = {
        'apikey': API_KEY,
        'format': 'JSON'
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API使用情況: {data}")
        else:
            print(f"❌ 無法獲取API使用情況: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API使用情況查詢失敗: {e}")

if __name__ == "__main__":
    print("🚀 開始測試 Twelve Data API...")
    
    # 測試API基本信息
    test_api_info()
    
    # 測試台股格式
    successful_formats = test_taiwan_stock_formats()
    
    # 如果有成功的格式，測試其他股票
    if successful_formats:
        test_other_taiwan_stocks()
    else:
        print("❌ 沒有找到有效的台股格式")
    
    print(f"\n🎉 測試完成！")
