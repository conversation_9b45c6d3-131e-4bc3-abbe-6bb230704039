#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 price 更新功能
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'finlab'))

def test_price_update():
    """測試 price 更新功能"""
    print("=" * 80)
    print("🧪 測試 price 更新功能")
    print("=" * 80)
    
    try:
        # 導入必要的函數
        from auto_update import get_newprice_incremental_date_range, update_table
        from crawler import crawl_price
        
        print("✅ 成功導入函數")
        
        # 測試日期範圍獲取
        print("\n📅 測試日期範圍獲取...")
        date_range = get_newprice_incremental_date_range()
        
        if date_range is None:
            print("❌ 日期範圍獲取失敗")
            return
        elif isinstance(date_range, list) and len(date_range) == 0:
            print("✅ 資料庫已是最新，無需更新")
            return
        else:
            print(f"✅ 獲取到日期範圍: {len(date_range)} 天")
            if date_range:
                print(f"   從: {date_range[0].strftime('%Y-%m-%d')}")
                print(f"   到: {date_range[-1].strftime('%Y-%m-%d')}")
        
        # 檢查 price.db 狀態
        print(f"\n📊 檢查 price.db 狀態...")
        price_db_path = 'D:/Finlab/history/tables/price.db'
        
        if os.path.exists(price_db_path):
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT MAX(date) FROM stock_daily_data")
            last_date = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
            unique_stocks = cursor.fetchone()[0]
            
            # 檢查是否有興櫃股票
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '興櫃'")
            rotc_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   總記錄數: {total_records:,}")
            print(f"   最後日期: {last_date}")
            print(f"   股票數: {unique_stocks:,}")
            print(f"   興櫃股票記錄: {rotc_count:,}")
            
            if rotc_count == 0:
                print(f"   ✅ 確認：資料庫中無興櫃股票")
            else:
                print(f"   ⚠️ 警告：資料庫中仍有興櫃股票")
        else:
            print("❌ price.db 不存在")
            return
        
        # 如果有需要更新的日期，測試更新一天
        if date_range and len(date_range) > 0:
            test_date = date_range[0]  # 取第一個需要更新的日期
            print(f"\n🚀 測試更新單日資料: {test_date.strftime('%Y-%m-%d')}")
            
            # 記錄更新前的狀態
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            before_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_before = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"   更新前該日期記錄: {before_count:,}")
            print(f"   更新前總記錄: {total_before:,}")
            
            # 執行爬蟲
            df = crawl_price(test_date)
            
            if not df.empty:
                print(f"   ✅ 爬蟲成功，獲取 {len(df)} 筆資料")
                
                # 檢查爬取資料
                df_reset = df.reset_index()
                if 'listing_status' in df_reset.columns:
                    rotc_in_crawl = len(df_reset[df_reset['listing_status'] == '興櫃'])
                    if rotc_in_crawl == 0:
                        print(f"   ✅ 爬取資料中無興櫃股票")
                    else:
                        print(f"   ⚠️ 爬取資料中有 {rotc_in_crawl} 檔興櫃股票")
                
                # 保存資料
                from auto_update import save_price_to_newprice_database
                if save_price_to_newprice_database(df, price_db_path):
                    print(f"   ✅ 資料保存成功")
                    
                    # 檢查保存後的狀態
                    conn = sqlite3.connect(price_db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
                    after_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
                    total_after = cursor.fetchone()[0]
                    
                    # 檢查該日期是否有興櫃股票被保存
                    cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ? AND listing_status = '興櫃'", (test_date.strftime('%Y-%m-%d'),))
                    saved_rotc = cursor.fetchone()[0]
                    
                    conn.close()
                    
                    print(f"   更新後該日期記錄: {after_count:,}")
                    print(f"   更新後總記錄: {total_after:,}")
                    print(f"   該日期興櫃記錄: {saved_rotc:,}")
                    
                    if saved_rotc == 0:
                        print(f"   ✅ 驗證通過：沒有興櫃股票被保存")
                    else:
                        print(f"   ⚠️ 驗證失敗：有興櫃股票被保存")
                else:
                    print(f"   ❌ 資料保存失敗")
            else:
                print(f"   ⚠️ 爬蟲返回空資料")
        
        print(f"\n✅ 測試完成")
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_price_update()
