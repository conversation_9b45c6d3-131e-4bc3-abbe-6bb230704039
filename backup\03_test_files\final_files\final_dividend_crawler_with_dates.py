#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終版除權息資料爬蟲 - 專注於獲取包含日期的除權息資料
成功解決除權息日期獲取問題
"""

import requests
import csv
import sqlite3
import logging
import time
import os
import re
from datetime import datetime, timedelta
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FinalDividendCrawler:
    """最終版除權息資料爬蟲 - 包含完整日期資訊"""
    
    def __init__(self, db_path="D:/Finlab/history/tables/dividend_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.init_database()
    
    def init_database(self):
        """初始化資料庫"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dividend_data_with_dates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER,
                    ex_dividend_date TEXT,
                    cash_dividend REAL DEFAULT 0,
                    stock_dividend REAL DEFAULT 0,
                    total_dividend REAL DEFAULT 0,
                    data_source TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, ex_dividend_date, data_source)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 除權息資料庫初始化完成: {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")
    
    def parse_date(self, date_str):
        """解析各種日期格式為標準格式"""
        if not date_str or date_str == '-':
            return None
            
        try:
            # 處理民國年格式：114年07月14日
            if '年' in date_str and '月' in date_str and '日' in date_str:
                match = re.match(r'(\d+)年(\d+)月(\d+)日', date_str)
                if match:
                    roc_year, month, day = match.groups()
                    ad_year = int(roc_year) + 1911
                    return f"{ad_year}-{int(month):02d}-{int(day):02d}"
            
            # 處理民國年格式：114/07/14
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    year_part = int(parts[0])
                    if year_part > 1911:  # 西元年
                        return f"{year_part}-{int(parts[1]):02d}-{int(parts[2]):02d}"
                    else:  # 民國年
                        ad_year = year_part + 1911
                        return f"{ad_year}-{int(parts[1]):02d}-{int(parts[2]):02d}"
            
            # 處理西元年格式：2024-07-14
            if '-' in date_str and len(date_str) == 10:
                return date_str
                
        except Exception as e:
            self.logger.warning(f"⚠️ 日期解析失敗: {date_str}, 錯誤: {e}")
        
        return None
    
    def fetch_twse_dividend_data_with_dates(self, year=None):
        """從證交所API獲取包含日期的除權息資料"""
        if year is None:
            year = datetime.now().year
            
        try:
            self.logger.info(f"🔍 從證交所API獲取 {year} 年除權息資料（含日期）...")
            
            dividend_records = []
            
            # 查詢多個月份的資料以獲取更完整的除權息資料
            for month in range(1, 13):
                try:
                    # 證交所除權息API
                    url = "https://www.twse.com.tw/exchangeReport/TWT49U"
                    params = {
                        'response': 'json',
                        'date': f'{year}{month:02d}01',
                        'selectType': 'ALL'
                    }
                    
                    response = requests.get(url, params=params, headers=self.headers, timeout=30, verify=False)
                    response.raise_for_status()
                    
                    data = response.json()
                    
                    if 'data' in data and data['data']:
                        for record in data['data']:
                            try:
                                # API資料格式: [除權息日期, 股票代碼, 股票名稱, 除權息前收盤價, 除權息參考價, 權值+息值, ...]
                                ex_date = record[0]
                                stock_code = record[1]
                                stock_name = record[2]
                                
                                # 解析股利資料
                                total_dividend_str = record[5] if len(record) > 5 else '0'
                                total_dividend = float(total_dividend_str) if total_dividend_str and total_dividend_str != '-' else 0
                                
                                # 解析除權息日期
                                formatted_date = self.parse_date(ex_date)
                                
                                if formatted_date:  # 只保存有日期的資料
                                    dividend_record = {
                                        'stock_code': stock_code,
                                        'stock_name': stock_name,
                                        'year': year,
                                        'ex_dividend_date': formatted_date,
                                        'cash_dividend': total_dividend,
                                        'stock_dividend': 0,
                                        'total_dividend': total_dividend,
                                        'data_source': 'twse_api'
                                    }
                                    
                                    dividend_records.append(dividend_record)
                                
                            except Exception as e:
                                self.logger.warning(f"⚠️ 解析API資料失敗: {e}")
                                continue
                    
                    # 避免請求過於頻繁
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ 查詢 {year}/{month:02d} 資料失敗: {e}")
                    continue
            
            self.logger.info(f"✅ 證交所API獲取完成: {len(dividend_records)} 筆資料（含日期）")
            return dividend_records
            
        except Exception as e:
            self.logger.error(f"❌ 從證交所API獲取除權息資料失敗: {e}")
            return []
    
    def fetch_tpex_dividend_data_with_dates(self, year=None):
        """從櫃買中心API獲取包含日期的除權息資料"""
        if year is None:
            year = datetime.now().year
            
        try:
            self.logger.info(f"🔍 從櫃買中心API獲取 {year} 年除權息資料（含日期）...")
            
            dividend_records = []
            
            # 查詢多個月份的資料
            for month in range(1, 13):
                try:
                    # 櫃買中心除權息API
                    url = "https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php"
                    params = {
                        'l': 'zh-tw',
                        'd': f'{year-1911}/{month:02d}/01',  # 民國年格式
                        'stkno': '',
                        'o': 'json'
                    }
                    
                    response = requests.get(url, params=params, headers=self.headers, timeout=30, verify=False)
                    response.raise_for_status()
                    
                    data = response.json()
                    
                    # 櫃買中心的資料結構：tables[0]['data']
                    if 'tables' in data and data['tables'] and len(data['tables']) > 0:
                        table_data = data['tables'][0].get('data', [])
                        
                        for record in table_data:
                            try:
                                # 櫃買中心資料格式: [除權息日期, 股票代碼, 股票名稱, ...]
                                ex_date = record[0]
                                stock_code = record[1]
                                stock_name = record[2]
                                
                                # 解析現金股利（通常在第13欄）
                                cash_dividend_str = record[13] if len(record) > 13 else '0'
                                cash_dividend = float(cash_dividend_str) if cash_dividend_str and cash_dividend_str != '-' else 0
                                
                                # 解析除權息日期
                                formatted_date = self.parse_date(ex_date)
                                
                                if formatted_date:  # 只保存有日期的資料
                                    dividend_record = {
                                        'stock_code': stock_code,
                                        'stock_name': stock_name,
                                        'year': year,
                                        'ex_dividend_date': formatted_date,
                                        'cash_dividend': cash_dividend,
                                        'stock_dividend': 0,
                                        'total_dividend': cash_dividend,
                                        'data_source': 'tpex_api'
                                    }
                                    
                                    dividend_records.append(dividend_record)
                                
                            except Exception as e:
                                self.logger.warning(f"⚠️ 解析櫃買中心資料失敗: {e}")
                                continue
                    
                    # 避免請求過於頻繁
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ 查詢櫃買中心 {year}/{month:02d} 資料失敗: {e}")
                    continue
            
            self.logger.info(f"✅ 櫃買中心API獲取完成: {len(dividend_records)} 筆資料（含日期）")
            return dividend_records
            
        except Exception as e:
            self.logger.error(f"❌ 從櫃買中心API獲取除權息資料失敗: {e}")
            return []
    
    def save_dividend_data(self, dividend_records):
        """儲存除權息資料到資料庫"""
        if not dividend_records:
            self.logger.warning("⚠️ 無資料可儲存")
            return
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            
            for record in dividend_records:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO dividend_data_with_dates 
                        (stock_code, stock_name, year, ex_dividend_date, cash_dividend, 
                         stock_dividend, total_dividend, data_source, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        record['stock_code'],
                        record['stock_name'],
                        record['year'],
                        record['ex_dividend_date'],
                        record['cash_dividend'],
                        record['stock_dividend'],
                        record['total_dividend'],
                        record['data_source']
                    ))
                    
                    saved_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ 儲存除權息資料失敗: {record['stock_code']}, 錯誤: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 除權息資料儲存完成: {saved_count} 筆")
            
        except Exception as e:
            self.logger.error(f"❌ 儲存除權息資料失敗: {e}")
    
    def export_to_csv(self, year=None, filename=None):
        """匯出除權息資料到CSV檔案"""
        if year is None:
            year = datetime.now().year
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dividend_with_dates_{year}_{timestamp}.csv"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stock_code, stock_name, year, ex_dividend_date, cash_dividend, 
                       stock_dividend, total_dividend, data_source
                FROM dividend_data_with_dates 
                WHERE year = ? AND ex_dividend_date IS NOT NULL
                ORDER BY ex_dividend_date, stock_code
            ''', (year,))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                self.logger.warning(f"⚠️ 無 {year} 年除權息資料（含日期）可匯出")
                return None
            
            # 確保輸出目錄存在 - 與除權息資料庫放在相同目錄
            output_dir = "D:/Finlab/history/tables"
            os.makedirs(output_dir, exist_ok=True)

            filepath = os.path.join(output_dir, filename)
            
            # 寫入CSV檔案
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['股票代碼', '股票名稱', '年度', '除權息日期', '現金股利', '股票股利', '總股利', '資料來源']
                writer = csv.writer(csvfile)
                writer.writerow(fieldnames)
                
                for row in results:
                    writer.writerow(row)
            
            self.logger.info(f"✅ 除權息資料（含日期）已匯出至: {filepath}")
            self.logger.info(f"📊 匯出資料筆數: {len(results)}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"❌ 匯出CSV檔案失敗: {e}")
            return None
    
    def fetch_all_dividend_data_with_dates(self, year=None):
        """獲取所有包含日期的除權息資料"""
        if year is None:
            year = datetime.now().year
            
        self.logger.info(f"🚀 開始獲取 {year} 年除權息資料（含完整日期）...")
        
        all_records = []
        
        # 從證交所獲取
        twse_records = self.fetch_twse_dividend_data_with_dates(year)
        all_records.extend(twse_records)
        
        # 從櫃買中心獲取
        tpex_records = self.fetch_tpex_dividend_data_with_dates(year)
        all_records.extend(tpex_records)
        
        # 儲存資料
        if all_records:
            self.save_dividend_data(all_records)
            
        self.logger.info(f"🎉 除權息資料獲取完成: 總計 {len(all_records)} 筆資料（全部含日期）")
        
        return all_records

if __name__ == "__main__":
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 創建爬蟲實例
    crawler = FinalDividendCrawler()
    
    # 獲取2024年除權息資料（含日期）
    year = 2024
    print(f"🚀 開始獲取 {year} 年除權息資料（含完整日期）...")
    
    dividend_data = crawler.fetch_all_dividend_data_with_dates(year)
    
    if dividend_data:
        # 匯出CSV
        csv_file = crawler.export_to_csv(year)
        if csv_file:
            print(f"\n📁 除權息資料（含日期）已匯出至: {csv_file}")
            
            # 詢問是否開啟CSV檔案
            try:
                import subprocess
                import sys
                
                response = input("\n是否要開啟CSV檔案? (y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    if sys.platform.startswith('win'):
                        subprocess.run(['start', csv_file], shell=True)
                    elif sys.platform.startswith('darwin'):
                        subprocess.run(['open', csv_file])
                    else:
                        subprocess.run(['xdg-open', csv_file])
                        
            except Exception as e:
                print(f"⚠️ 無法自動開啟檔案: {e}")
    
    print("\n✅ 除權息資料獲取完成！")
