#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試改善後的真實數據獲取
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_improved_real_data():
    """測試改善後的真實數據獲取"""
    print("🚀 測試改善後的真實數據獲取")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試美元指數
        print("\n💵 測試美元指數...")
        dollar_start = time.time()
        dollar_data = fetcher.get_dollar_index_real()
        dollar_time = time.time() - dollar_start
        
        if dollar_data:
            for name, data in dollar_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status} ({dollar_time:.1f}秒)")
        else:
            print(f"❌ 美元指數獲取失敗 ({dollar_time:.1f}秒)")
        
        # 測試外匯匯率
        print("\n💱 測試外匯匯率...")
        fx_start = time.time()
        fx_data = fetcher.get_enhanced_fx_rates()
        fx_time = time.time() - fx_start
        
        if fx_data:
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status}")
            print(f"   外匯獲取耗時: {fx_time:.1f}秒")
        else:
            print(f"❌ 外匯匯率獲取失敗 ({fx_time:.1f}秒)")
        
        # 測試商品價格
        print("\n🛢️ 測試商品價格...")
        commodity_start = time.time()
        commodity_data = fetcher.get_commodities_real()
        commodity_time = time.time() - commodity_start
        
        if commodity_data:
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: ${price} ({change_pct:+.2f}%) - {status}")
            print(f"   商品獲取耗時: {commodity_time:.1f}秒")
        else:
            print(f"❌ 商品價格獲取失敗 ({commodity_time:.1f}秒)")
        
        # 測試美股指數
        print("\n📈 測試美股指數...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"✅ {name}: {price} ({change_pct:+.2f}%) - {status}")
            print(f"   美股獲取耗時: {us_time:.1f}秒")
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner_integration():
    """測試安全掃描器整合"""
    print("\n🔍 測試安全掃描器整合...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 測試外匯數據
        print("💱 測試外匯數據...")
        fx_data = scanner.get_fx_rates()
        if fx_data:
            real_count = 0
            for name, data in fx_data.items():
                status = data.get('status', '未知')
                if '真實數據' in status:
                    real_count += 1
                    rate = data.get('rate', 0)
                    change_pct = data.get('change_pct', 0)
                    print(f"✅ {name}: {rate} ({change_pct:+.2f}%) - {status}")
                else:
                    print(f"⚠️ {name}: {status}")
            
            print(f"   真實數據: {real_count}/{len(fx_data)}")
        else:
            print("❌ 外匯數據為空")
        
        # 測試商品數據
        print("\n🛢️ 測試商品數據...")
        commodity_data = scanner.get_commodities()
        if commodity_data:
            real_count = 0
            for name, data in commodity_data.items():
                status = data.get('status', '未知')
                if '真實數據' in status:
                    real_count += 1
                    price = data.get('price', 0)
                    change_pct = data.get('change_pct', 0)
                    print(f"✅ {name}: ${price} ({change_pct:+.2f}%) - {status}")
                else:
                    print(f"⚠️ {name}: {status}")
            
            print(f"   真實數據: {real_count}/{len(commodity_data)}")
        else:
            print("❌ 商品數據為空")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 改善後的真實數據獲取測試")
    print("=" * 60)
    
    tests = [
        ("改善後的真實數據獲取器", test_improved_real_data),
        ("安全掃描器整合", test_safe_scanner_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 改善總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🚀 改善內容:")
    print("• 美元指數：使用多個代碼 (DX-Y.NYB, DXY, ^DXY)")
    print("• 外匯匯率：使用多個代碼備援")
    print("• 反爬蟲優化：減少延遲時間 (0.3-0.8秒)")
    print("• 請求間隔：從1.5秒減少到0.5秒")
    print("• 即時數據優先：使用ticker.info獲取最新價格")
    print("• 歷史數據備援：info失敗時使用history")
    
    print("\n💡 數據狀態說明:")
    print("• 真實數據：成功從yfinance獲取的實際市場數據")
    print("• ❌ 無法獲取數據：所有數據源都失敗")
    print("• 不再顯示模擬數據：確保數據真實性")
    
    if passed > 0:
        print("\n🎉 數據獲取改善成功！")
        print("現在應該能獲取到更多真實的市場數據。")
    else:
        print("\n⚠️ 需要進一步檢查網路連接或yfinance版本")

if __name__ == "__main__":
    main()
