#!/usr/bin/env python3
"""
5分鐘策略演示程序
展示如何使用5分鐘數據進行實時策略分析
"""

import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 設置日誌
logging.basicConfig(level=logging.INFO)

class FiveMinuteStrategyDemo:
    """5分鐘策略演示類"""
    
    def __init__(self):
        """初始化演示系統"""
        self.current_positions = {}  # 當前持倉
        self.trade_history = []      # 交易歷史
        self.signals_history = []    # 信號歷史
        
        # 載入策略系統
        try:
            from five_minute_strategies import FiveMinuteStrategies
            self.strategy_engine = FiveMinuteStrategies()
            logging.info("✅ 5分鐘策略引擎載入成功")
        except ImportError:
            logging.error("❌ 無法載入5分鐘策略引擎")
            self.strategy_engine = None
        
        # 載入數據源
        try:
            from twse_realtime_crawler import TWSERealtimeCrawler
            self.data_source = TWSERealtimeCrawler()
            logging.info("✅ TWSE數據源載入成功")
        except ImportError:
            logging.error("❌ 無法載入TWSE數據源")
            self.data_source = None
    
    def get_5min_data(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """
        獲取5分鐘數據
        
        Args:
            symbol: 股票代碼
            periods: 數據期數
        
        Returns:
            5分鐘K線數據
        """
        if self.data_source:
            # 從TWSE數據源獲取
            df = self.data_source.get_5min_kline_data(symbol)
            if not df.empty:
                return df.tail(periods)
        
        # 如果沒有實際數據，生成模擬數據用於演示
        logging.warning("⚠️ 使用模擬數據進行演示")
        return self.generate_sample_data(symbol, periods)
    
    def generate_sample_data(self, symbol: str, periods: int) -> pd.DataFrame:
        """
        生成模擬5分鐘數據用於演示
        
        Args:
            symbol: 股票代碼
            periods: 數據期數
        
        Returns:
            模擬的5分鐘K線數據
        """
        # 基礎價格（根據股票代碼設定）
        base_prices = {
            '2330': 1070,  # 台積電
            '2317': 160,   # 鴻海
            '2454': 1280,  # 聯發科
            '3008': 2180,  # 大立光
            '2412': 130    # 中華電
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # 生成時間序列
        end_time = datetime.now().replace(second=0, microsecond=0)
        start_time = end_time - timedelta(minutes=5 * periods)
        time_index = pd.date_range(start=start_time, end=end_time, freq='5T')[:-1]
        
        # 生成價格數據（隨機遊走 + 趨勢）
        np.random.seed(42)  # 固定種子以便重現
        
        # 添加一些趨勢和波動
        trend = np.sin(np.arange(periods) * 0.1) * 0.02  # 正弦趨勢
        noise = np.random.normal(0, 0.01, periods)       # 隨機噪音
        returns = trend + noise
        
        # 計算價格
        prices = [base_price]
        for i in range(periods - 1):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(new_price)
        
        # 生成OHLCV數據
        data = []
        for i, (timestamp, close_price) in enumerate(zip(time_index, prices)):
            # 生成開高低價
            volatility = abs(returns[i]) * 2
            high = close_price * (1 + volatility * np.random.uniform(0.2, 0.8))
            low = close_price * (1 - volatility * np.random.uniform(0.2, 0.8))
            
            if i == 0:
                open_price = close_price
            else:
                open_price = prices[i-1]
            
            # 確保價格邏輯正確
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            # 生成成交量
            base_volume = 1000
            volume_multiplier = 1 + abs(returns[i]) * 10  # 波動大時成交量大
            volume = int(base_volume * volume_multiplier * np.random.uniform(0.5, 2.0))
            
            data.append({
                'datetime': timestamp,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def run_strategy_analysis(self, symbol: str, strategies: list = None) -> dict:
        """
        運行策略分析
        
        Args:
            symbol: 股票代碼
            strategies: 要使用的策略列表
        
        Returns:
            分析結果
        """
        if not self.strategy_engine:
            return {'error': '策略引擎未載入'}
        
        # 獲取5分鐘數據
        df = self.get_5min_data(symbol, periods=100)
        
        if df.empty:
            return {'error': '無法獲取數據'}
        
        # 執行策略分析
        results = self.strategy_engine.analyze_stock(symbol, df, strategies)
        
        # 記錄信號歷史
        all_signals = self.strategy_engine.get_all_signals(results)
        self.signals_history.extend(all_signals)
        
        return results
    
    def display_analysis_results(self, results: dict):
        """
        顯示分析結果
        
        Args:
            results: 策略分析結果
        """
        print(f"\n{'='*60}")
        print(f"📊 {results['symbol']} 5分鐘策略分析結果")
        print(f"{'='*60}")
        print(f"分析時間: {results['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"數據點數: {results['data_points']} 個5分鐘K線")
        
        total_signals = 0
        
        for strategy_name, strategy_result in results['strategies'].items():
            signals = strategy_result.get('signals', [])
            total_signals += len(signals)
            
            print(f"\n🎯 {strategy_name}")
            print(f"   描述: {strategy_result.get('description', 'N/A')}")
            
            if signals:
                print(f"   信號數量: {len(signals)}")
                for i, signal in enumerate(signals, 1):
                    print(f"   信號{i}: {signal['type']} @ {signal['price']:.2f}")
                    print(f"          原因: {signal['reason']}")
                    print(f"          止損: {signal['stop_loss']:.2f}")
                    print(f"          止盈: {signal['take_profit']:.2f}")
                    print(f"          信心度: {signal['confidence']:.1%}")
            else:
                print(f"   信號數量: 0")
        
        print(f"\n📈 總計: {total_signals} 個交易信號")
        
        # 顯示高信心度信號
        all_signals = self.strategy_engine.get_all_signals(results)
        high_confidence_signals = self.strategy_engine.filter_signals(all_signals, min_confidence=0.7)
        
        if high_confidence_signals:
            print(f"\n🔥 高信心度信號 (≥70%):")
            for signal in high_confidence_signals:
                print(f"   {signal['strategy']}: {signal['type']} @ {signal['price']:.2f} "
                      f"({signal['confidence']:.1%})")
    
    def plot_strategy_chart(self, symbol: str, results: dict):
        """
        繪製策略圖表
        
        Args:
            symbol: 股票代碼
            results: 策略分析結果
        """
        # 獲取數據
        df = self.get_5min_data(symbol, periods=50)  # 最近50個5分鐘
        
        if df.empty:
            print("❌ 無法繪製圖表：數據不足")
            return
        
        # 計算技術指標
        df = self.strategy_engine.calculate_indicators(df)
        
        # 創建圖表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), 
                                           gridspec_kw={'height_ratios': [3, 1, 1]})
        
        # 主圖：K線和均線
        ax1.plot(df['datetime'], df['close'], label='收盤價', linewidth=2, color='black')
        ax1.plot(df['datetime'], df['MA5'], label='MA5', alpha=0.7, color='blue')
        ax1.plot(df['datetime'], df['MA20'], label='MA20', alpha=0.7, color='red')
        
        # 布林帶
        if 'BB_upper' in df.columns:
            ax1.fill_between(df['datetime'], df['BB_upper'], df['BB_lower'], 
                           alpha=0.1, color='gray', label='布林帶')
        
        # 標記交易信號
        all_signals = self.strategy_engine.get_all_signals(results)
        for signal in all_signals:
            if signal['confidence'] >= 0.7:  # 只顯示高信心度信號
                color = 'green' if signal['type'] == 'BUY' else 'red'
                marker = '^' if signal['type'] == 'BUY' else 'v'
                ax1.scatter(df['datetime'].iloc[-1], signal['price'], 
                          color=color, marker=marker, s=100, zorder=5)
                ax1.annotate(f"{signal['strategy']}\n{signal['type']}", 
                           xy=(df['datetime'].iloc[-1], signal['price']),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                           fontsize=8, color='white')
        
        ax1.set_title(f'{symbol} 5分鐘K線圖與交易信號', fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 成交量圖
        colors = ['red' if close >= open else 'green' 
                 for close, open in zip(df['close'], df['open'])]
        ax2.bar(df['datetime'], df['volume'], color=colors, alpha=0.7)
        ax2.plot(df['datetime'], df['Volume_MA'], color='orange', label='成交量均線')
        ax2.set_title('成交量', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # RSI圖
        if 'RSI' in df.columns:
            ax3.plot(df['datetime'], df['RSI'], label='RSI', color='purple')
            ax3.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='超買線')
            ax3.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='超賣線')
            ax3.fill_between(df['datetime'], 70, 100, alpha=0.1, color='red')
            ax3.fill_between(df['datetime'], 0, 30, alpha=0.1, color='green')
        
        ax3.set_title('RSI指標', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 100)
        
        # 格式化x軸
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=30))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        plt.show()
    
    def run_realtime_monitoring(self, symbols: list, duration_minutes: int = 30):
        """
        運行即時監控
        
        Args:
            symbols: 股票代碼列表
            duration_minutes: 監控時長（分鐘）
        """
        print(f"🚀 開始即時監控 {symbols}，監控時長: {duration_minutes} 分鐘")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        while datetime.now() < end_time:
            print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 執行策略掃描...")
            
            for symbol in symbols:
                try:
                    # 分析股票
                    results = self.run_strategy_analysis(symbol)
                    
                    if 'error' in results:
                        print(f"❌ {symbol}: {results['error']}")
                        continue
                    
                    # 獲取高信心度信號
                    all_signals = self.strategy_engine.get_all_signals(results)
                    high_confidence_signals = self.strategy_engine.filter_signals(
                        all_signals, min_confidence=0.7
                    )
                    
                    if high_confidence_signals:
                        print(f"🔥 {symbol} 發現高信心度信號:")
                        for signal in high_confidence_signals:
                            print(f"   {signal['strategy']}: {signal['type']} @ {signal['price']:.2f} "
                                  f"({signal['confidence']:.1%}) - {signal['reason']}")
                    else:
                        print(f"📊 {symbol}: 無高信心度信號")
                
                except Exception as e:
                    print(f"❌ {symbol} 分析失敗: {e}")
            
            # 等待下次掃描
            time.sleep(60)  # 每分鐘掃描一次
        
        print(f"\n⏹️ 監控結束")
    
    def demo_all_strategies(self, symbol: str = '2330'):
        """
        演示所有策略
        
        Args:
            symbol: 股票代碼
        """
        print(f"🎯 演示所有5分鐘策略 - {symbol}")
        
        # 運行分析
        results = self.run_strategy_analysis(symbol)
        
        if 'error' in results:
            print(f"❌ 演示失敗: {results['error']}")
            return
        
        # 顯示結果
        self.display_analysis_results(results)
        
        # 繪製圖表
        try:
            self.plot_strategy_chart(symbol, results)
        except Exception as e:
            print(f"⚠️ 圖表繪製失敗: {e}")
        
        return results

def main():
    """主程序"""
    print("🚀 5分鐘策略演示程序")
    
    # 創建演示實例
    demo = FiveMinuteStrategyDemo()
    
    # 演示選項
    while True:
        print(f"\n{'='*50}")
        print("請選擇演示選項:")
        print("1. 單股策略分析")
        print("2. 多股即時監控")
        print("3. 策略圖表展示")
        print("4. 完整演示")
        print("0. 退出")
        print("="*50)
        
        choice = input("請輸入選項 (0-4): ").strip()
        
        if choice == '0':
            print("👋 再見！")
            break
        
        elif choice == '1':
            symbol = input("請輸入股票代碼 (預設2330): ").strip() or '2330'
            results = demo.run_strategy_analysis(symbol)
            if 'error' not in results:
                demo.display_analysis_results(results)
        
        elif choice == '2':
            symbols_input = input("請輸入股票代碼，用逗號分隔 (預設2330,2317,2454): ").strip()
            symbols = [s.strip() for s in symbols_input.split(',')] if symbols_input else ['2330', '2317', '2454']
            duration = int(input("請輸入監控時長(分鐘，預設5): ").strip() or '5')
            demo.run_realtime_monitoring(symbols, duration)
        
        elif choice == '3':
            symbol = input("請輸入股票代碼 (預設2330): ").strip() or '2330'
            results = demo.run_strategy_analysis(symbol)
            if 'error' not in results:
                demo.plot_strategy_chart(symbol, results)
        
        elif choice == '4':
            symbol = input("請輸入股票代碼 (預設2330): ").strip() or '2330'
            demo.demo_all_strategies(symbol)
        
        else:
            print("❌ 無效選項，請重新選擇")

if __name__ == "__main__":
    main()
