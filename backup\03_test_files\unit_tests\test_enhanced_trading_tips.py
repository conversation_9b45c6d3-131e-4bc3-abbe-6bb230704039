#!/usr/bin/env python3
"""
測試增強版看盤重點功能
整合基本觀察重點與實戰選股技巧
"""

import sys
import logging
from datetime import datetime, time
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QTextEdit, QStatusBar, QDialog, QMessageBox
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QFont

# 設置日誌
logging.basicConfig(level=logging.INFO)

class EnhancedTradingTipsWindow(QMainWindow):
    """增強版看盤重點測試視窗"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("📊 增強版看盤重點 - 整合實戰技巧")
        self.setGeometry(100, 100, 1200, 800)
        
        self.init_ui()
        self.init_trading_ticker()
        
    def init_ui(self):
        """初始化界面"""
        # 中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📊 台股看盤重點 - 五大時段操作指南（增強版）")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 當前時間顯示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #28a745; margin: 10px;")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.time_label)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        
        show_tips_btn = QPushButton("📖 查看完整看盤重點")
        show_tips_btn.clicked.connect(self.show_trading_tips)
        show_tips_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; }")
        
        self.enable_btn = QPushButton("🎯 啟用跑馬燈")
        self.enable_btn.clicked.connect(self.enable_trading_ticker)
        self.enable_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; }")
        
        self.disable_btn = QPushButton("⏹️ 停用跑馬燈")
        self.disable_btn.clicked.connect(self.disable_trading_ticker)
        self.disable_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #dc3545; color: white; border: none; border-radius: 4px; }")
        self.disable_btn.setEnabled(False)
        
        test_btn = QPushButton("🧪 測試所有時段")
        test_btn.clicked.connect(self.test_all_periods)
        test_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #6f42c1; color: white; border: none; border-radius: 4px; }")
        
        button_layout.addWidget(show_tips_btn)
        button_layout.addWidget(self.enable_btn)
        button_layout.addWidget(self.disable_btn)
        button_layout.addWidget(test_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 簡要說明
        summary = QTextEdit()
        summary.setReadOnly(True)
        summary.setMaximumHeight(200)
        summary.setHtml("""
        <h3 style="color: #2E86AB;">📋 功能說明</h3>
        <p><strong>看盤重點功能</strong>整合了台股五大時段的基本觀察重點與實戰選股技巧：</p>
        <ul>
            <li><strong>🌅 盤前集合競價</strong>：預告強勢標的、消息驅動股、族群龍頭</li>
            <li><strong>🌄 早盤時段</strong>：跳空續航股、主力標的、技術面強勢股</li>
            <li><strong>☀️ 中盤時段</strong>：族群輪動股、支撐反彈股、ETF套利標的</li>
            <li><strong>🌇 午盤時段</strong>：反彈先鋒、防禦性股、空頭破壞股</li>
            <li><strong>🌆 收盤前試撮</strong>：大宗交易熱股、ETF尾盤換股、暫緩撮合股</li>
        </ul>
        <p><strong>跑馬燈功能</strong>會根據當前時間自動顯示相應時段的精簡操作重點。</p>
        """)
        layout.addWidget(summary)
        
        # 狀態欄
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("📊 增強版看盤重點系統已就緒")
        
        # 時間更新定時器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # 每秒更新時間
        
    def show_trading_tips(self):
        """顯示完整看盤重點"""
        dialog = QDialog(self)
        dialog.setWindowTitle("📊 看盤重點 - 台股盤中五大時段操作指南")
        dialog.setModal(True)
        dialog.resize(1000, 800)
        
        # 設置視窗標誌
        dialog.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )
        
        layout = QVBoxLayout(dialog)
        
        # 內容文字
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2 style="color: #2E86AB;">📊 台股看盤重點 - 五大時段操作指南</h2>
        
        <h3 style="color: #339af0;">📋 概述</h3>
        <p>每日台股盤中可分為五大時段，每個時段都有不同的市場特色與操作重點。</p>
        
        <h3 style="color: #dc3545;">🌅 1. 盤前集合競價（08:30–09:00）</h3>
        <h4 style="color: #dc3545; margin-left: 20px;">基本觀察重點：</h4>
        <ul>
            <li><strong>國際盤勢連動</strong>：關注美股期貨、港股收盤走勢及主要經濟數據</li>
            <li><strong>籌碼變動</strong>：觀察融資融券餘額增減，判斷偏多或偏空</li>
            <li><strong>模擬撮合</strong>：試撮僅供參考，主力常在此階段試盤</li>
        </ul>
        <h4 style="color: #dc3545; margin-left: 20px;">實戰選股技巧：</h4>
        <ul>
            <li><strong>🎯 預告強勢標的</strong>：留意集合競價成交金額＞1,000萬元的個股</li>
            <li><strong>📰 消息驅動個股</strong>：關注前日收盤後公告重大利多/利空的股票</li>
            <li><strong>👑 族群龍頭</strong>：觀察產業龍頭搶籌或拋壓情況</li>
        </ul>
        
        <h3 style="color: #28a745;">🌄 2. 早盤時段（09:00–10:30）</h3>
        <h4 style="color: #28a745; margin-left: 20px;">實戰選股技巧：</h4>
        <ul>
            <li><strong>🚀 跳空續航股</strong>：開盤跳空高開但放量維持上攻的股票</li>
            <li><strong>💪 主力標的</strong>：9:00–10:30成交量突然放大且價量同步上揚</li>
            <li><strong>📈 技術面強勢股</strong>：KD鈍化高檔、RSI持續向上</li>
        </ul>
        
        <h3 style="color: #fd7e14;">☀️ 3. 中盤時段（10:30–12:00）</h3>
        <h4 style="color: #fd7e14; margin-left: 20px;">實戰選股技巧：</h4>
        <ul>
            <li><strong>🔄 族群輪動股</strong>：當日族群漲幅前茅產業的次強補漲股</li>
            <li><strong>📊 支撐反彈股</strong>：回測均線且量能不急跌，逢低承接</li>
            <li><strong>💰 ETF套利標的</strong>：ETF折價<-1%有套利機會</li>
        </ul>
        
        <h3 style="color: #6f42c1;">🌇 4. 午盤時段（12:00–13:25）</h3>
        <h4 style="color: #6f42c1; margin-left: 20px;">實戰選股技巧：</h4>
        <ul>
            <li><strong>⚡ 反彈先鋒</strong>：量縮後放量反轉且出現長下影線</li>
            <li><strong>🛡️ 防禦性股</strong>：高股息ETF及公用事業類股避險</li>
            <li><strong>📉 空頭破壞股</strong>：權值股跌破10日均線且量能放大</li>
        </ul>
        
        <h3 style="color: #e83e8c;">🌆 5. 收盤前試撮（13:25–13:30）</h3>
        <h4 style="color: #e83e8c; margin-left: 20px;">實戰選股技巧：</h4>
        <ul>
            <li><strong>💎 大宗交易熱股</strong>：13:25–13:30出現大宗交易的個股</li>
            <li><strong>🔄 ETF尾盤換股</strong>：高股息ETF及主題ETF的尾盤操作</li>
            <li><strong>⚠️ 暫緩撮合個股</strong>：次日開盤需特別留意跳動幅度</li>
        </ul>
        
        <hr>
        <p style="color: #666; font-style: italic;">💡 <strong>實戰提醒</strong>：結合技術指標、籌碼面與國際消息面，全方位觀察台股脈動，提升進出場勝率。</p>
        """)
        
        layout.addWidget(content)
        
        # 關閉按鈕
        close_btn = QPushButton("❌ 關閉")
        close_btn.clicked.connect(dialog.close)
        close_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #6c757d; color: white; border: none; border-radius: 4px; }")
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
        
        dialog.exec()
        
    def init_trading_ticker(self):
        """初始化看盤重點跑馬燈"""
        self.trading_ticker_enabled = False
        self.trading_ticker_timer = QTimer()
        self.trading_ticker_timer.timeout.connect(self.check_trading_time)
        
        # 看盤重點訊息 - 整合實戰技巧
        self.trading_messages = {
            'pre_market': "🌅 盤前集合競價 (8:30-9:00)：🎯留意成交金額>1000萬個股、📰消息驅動股異動、👑族群龍頭搶籌情況",
            'early_session': "🌄 早盤時段 (9:00-10:30)：🚀跳空續航股、💪主力標的量價齊揚、📈技術面強勢股KD鈍化高檔",
            'mid_session': "☀️ 中盤時段 (10:30-12:00)：🔄族群輪動補漲股、📊支撐反彈股逢低承接、💰ETF套利折價機會",
            'afternoon': "🌇 午盤時段 (12:00-13:25)：⚡反彈先鋒長下影線、🛡️防禦性高股息ETF、📉權值股破線避開",
            'closing': "🌆 收盤前試撮 (13:25-13:30)：💎大宗交易熱股、🔄ETF尾盤換股、⚠️暫緩撮合股次日留意",
            'after_hours': "📊 盤後時段：檢視當日表現、準備明日策略、關注國際市場動向、零股交易13:40-14:30"
        }
    
    def update_time_display(self):
        """更新時間顯示"""
        now = datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 判斷當前時段
        current_time = now.time()
        period = self.get_current_period(current_time)
        
        self.time_label.setText(f"⏰ 當前時間：{time_str} | 📊 當前時段：{period}")
    
    def get_current_period(self, current_time):
        """獲取當前時段名稱"""
        if time(8, 30) <= current_time < time(9, 0):
            return "盤前集合競價"
        elif time(9, 0) <= current_time < time(10, 30):
            return "早盤時段"
        elif time(10, 30) <= current_time < time(12, 0):
            return "中盤時段"
        elif time(12, 0) <= current_time < time(13, 30):
            return "午盤時段"
        elif time(13, 25) <= current_time < time(13, 30):
            return "收盤前試撮"
        else:
            return "盤後時段"
    
    def enable_trading_ticker(self):
        """啟用看盤重點跑馬燈"""
        if self.trading_ticker_enabled:
            QMessageBox.information(self, "跑馬燈", "📊 看盤重點跑馬燈已經啟用！")
            return
        
        self.trading_ticker_enabled = True
        self.trading_ticker_timer.start(3000)  # 每3秒檢查一次（測試用）
        self.enable_btn.setEnabled(False)
        self.disable_btn.setEnabled(True)
        
        QMessageBox.information(
            self, 
            "跑馬燈啟用", 
            "🎯 增強版看盤重點跑馬燈已啟用！\n\n"
            "系統將根據當前時間自動顯示相應時段的實戰操作重點。\n"
            "包含基本觀察重點與實戰選股技巧。"
        )
        
        # 立即檢查一次
        self.check_trading_time()
    
    def disable_trading_ticker(self):
        """停用看盤重點跑馬燈"""
        self.trading_ticker_enabled = False
        self.trading_ticker_timer.stop()
        self.enable_btn.setEnabled(True)
        self.disable_btn.setEnabled(False)
        
        self.status_bar.showMessage("📊 看盤重點跑馬燈已停用")
        QMessageBox.information(self, "跑馬燈", "⏹️ 看盤重點跑馬燈已停用")
    
    def check_trading_time(self):
        """檢查交易時間並顯示相應提醒"""
        if not self.trading_ticker_enabled:
            return
            
        now = datetime.now()
        current_time = now.time()
        
        # 判斷當前時段
        message = ""
        if time(8, 30) <= current_time < time(9, 0):
            message = self.trading_messages['pre_market']
        elif time(9, 0) <= current_time < time(10, 30):
            message = self.trading_messages['early_session']
        elif time(10, 30) <= current_time < time(12, 0):
            message = self.trading_messages['mid_session']
        elif time(12, 0) <= current_time < time(13, 30):
            message = self.trading_messages['afternoon']
        elif time(13, 25) <= current_time < time(13, 30):
            message = self.trading_messages['closing']
        else:
            message = self.trading_messages['after_hours']
        
        # 在狀態欄顯示跑馬燈
        if message:
            self.status_bar.showMessage(message)
    
    def test_all_periods(self):
        """測試所有時段的訊息"""
        messages = [
            ("🌅 盤前集合競價", self.trading_messages['pre_market']),
            ("🌄 早盤時段", self.trading_messages['early_session']),
            ("☀️ 中盤時段", self.trading_messages['mid_session']),
            ("🌇 午盤時段", self.trading_messages['afternoon']),
            ("🌆 收盤前試撮", self.trading_messages['closing']),
            ("📊 盤後時段", self.trading_messages['after_hours'])
        ]
        
        # 創建測試對話框
        dialog = QMessageBox(self)
        dialog.setWindowTitle("🧪 增強版跑馬燈訊息測試")
        dialog.setIcon(QMessageBox.Icon.Information)
        
        content = "📊 增強版看盤重點跑馬燈訊息（整合實戰技巧）：\n\n"
        for period, message in messages:
            content += f"{period}：\n{message}\n\n"
        
        dialog.setText(content)
        dialog.exec()

def main():
    """主函數"""
    print("📊 測試增強版看盤重點功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示主視窗
    window = EnhancedTradingTipsWindow()
    window.show()
    
    print("✅ 增強版看盤重點測試視窗已開啟")
    print("🔍 新增功能:")
    print("  1. ✅ 整合基本觀察重點與實戰選股技巧")
    print("  2. ✅ 五大時段詳細操作指南")
    print("  3. ✅ 跑馬燈顯示精簡實戰重點")
    print("  4. ✅ 完整看盤重點對話框")
    print("  5. ✅ 時段自動判斷與提醒")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
