#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試對比度修復
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class ContrastTestWindow(QMainWindow):
    """測試對比度修復的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎨 對比度修復測試")
        self.setGeometry(100, 100, 900, 700)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🎨 對比度修復測試")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2E86AB; margin: 15px; padding: 15px;")
        layout.addWidget(title_label)
        
        # 修復說明
        fix_info = QTextEdit()
        fix_info.setMaximumHeight(300)
        fix_info.setHtml("""
        <h3>🔧 對比度問題修復</h3>
        <p><b>修復前的問題：</b></p>
        <ul>
            <li>❌ <b>白底白字</b> - 文字在白色背景上顯示為白色或淺色，無法看清</li>
            <li>❌ <b>對比度不足</b> - 文字顏色與背景顏色對比度太低</li>
            <li>❌ <b>可讀性差</b> - 用戶難以閱讀界面內容</li>
        </ul>
        
        <p><b>修復後的改善：</b></p>
        <ul>
            <li>✅ <b>深色文字</b> - 所有文字改為深色 (#212529)</li>
            <li>✅ <b>高對比度</b> - 確保文字與背景有足夠的對比度</li>
            <li>✅ <b>清晰可讀</b> - 所有文字都清晰可見</li>
            <li>✅ <b>一致性</b> - 統一的顏色方案</li>
        </ul>
        
        <p><b>具體修復項目：</b></p>
        <ul>
            <li>🏷️ <b>標籤文字</b> - 改為深色 #212529</li>
            <li>☑️ <b>複選框</b> - 文字改為深色，增加字重</li>
            <li>📅 <b>日期控件</b> - 文字和邊框樣式優化</li>
            <li>📋 <b>下拉選單</b> - 文字顏色和邊框樣式改善</li>
            <li>📊 <b>數據表格</b> - 表格內容和標題對比度提升</li>
        </ul>
        
        <p><b>顏色方案：</b></p>
        <ul>
            <li><span style="color: #212529; font-weight: bold;">#212529</span> - 主要文字顏色（深灰黑）</li>
            <li><span style="color: #495057; font-weight: bold;">#495057</span> - 次要文字顏色（中灰）</li>
            <li><span style="color: #007bff; font-weight: bold;">#007bff</span> - 強調色（藍色）</li>
            <li><span style="background-color: #f8f9fa; padding: 2px 6px; border: 1px solid #dee2e6;">#f8f9fa</span> - 背景色（淺灰）</li>
        </ul>
        """)
        layout.addWidget(fix_info)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟修復後的爬蟲界面")
        test_btn.clicked.connect(self.test_fixed_interface)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(test_btn)
        
        # 對比測試區域
        contrast_label = QLabel("📋 對比度測試區域")
        contrast_label.setStyleSheet("color: #212529; font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(contrast_label)
        
        # 好的對比度示例
        good_example = QLabel("✅ 良好對比度：深色文字在淺色背景上")
        good_example.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                color: #212529;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 14px;
                margin: 5px;
            }
        """)
        layout.addWidget(good_example)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🎨 對比度修復測試程式已啟動")
        self.log("✅ 所有文字顏色已修復為深色 #212529")
        self.log("✅ 背景保持淺色，確保高對比度")
        self.log("✅ 界面可讀性大幅提升")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_fixed_interface(self):
        """測試修復後的界面"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟修復後的台灣證交所爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 界面已成功創建")
            self.log("🎯 請檢查以下項目：")
            self.log("1. 所有文字是否清晰可見")
            self.log("2. 沒有白底白字的問題")
            self.log("3. 對比度是否足夠")
            self.log("4. 界面是否美觀易讀")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = ContrastTestWindow()
    window.show()
    
    print("🎨 對比度修復測試程式已啟動")
    print("✅ 主要修復：")
    print("1. 所有文字改為深色 #212529")
    print("2. 提高文字與背景的對比度")
    print("3. 統一顏色方案")
    print("4. 改善界面可讀性")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
