#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試Yahoo股價頁面結構
分析實際的HTML內容和CSS選擇器
"""

import requests
from bs4 import BeautifulSoup
import re

def debug_yahoo_page(stock_code='2330'):
    """調試Yahoo股價頁面"""
    print(f"🔍 調試Yahoo股價頁面: {stock_code}")
    print("=" * 60)
    
    try:
        url = f'https://tw.stock.yahoo.com/quote/{stock_code}'
        print(f"📡 請求URL: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(url, headers=headers)
        print(f"✅ 請求成功，狀態碼: {response.status_code}")
        print(f"📄 頁面內容長度: {len(response.text)}")
        
        soup = BeautifulSoup(response.text, "html.parser")
        
        # 檢查標題
        print(f"\n📊 頁面標題:")
        title = soup.find('title')
        if title:
            print(f"  Title標籤: {title.get_text().strip()}")
        
        h1_tags = soup.find_all('h1')
        for i, h1 in enumerate(h1_tags):
            print(f"  H1標籤{i+1}: {h1.get_text().strip()}")
        
        # 檢查是否包含股票資訊
        print(f"\n🔍 搜尋股票代碼 '{stock_code}':")
        if stock_code in response.text:
            print(f"  ✅ 頁面包含股票代碼 {stock_code}")
        else:
            print(f"  ❌ 頁面不包含股票代碼 {stock_code}")
        
        # 檢查價格相關的CSS類別
        print(f"\n💰 檢查價格相關元素:")
        
        # 檢查各種可能的價格選擇器
        price_selectors = [
            '.Fz\\(32px\\)',
            '.Fz(32px)',
            '[class*="32px"]',
            '[class*="price"]',
            '[class*="Price"]',
            'span[data-symbol]',
            '.D\\(ib\\).Fz\\(32px\\)',
            '.Fw\\(b\\).Fz\\(32px\\)'
        ]
        
        for selector in price_selectors:
            try:
                elements = soup.select(selector)
                if elements:
                    print(f"  ✅ 找到 {selector}: {len(elements)} 個元素")
                    for i, elem in enumerate(elements[:3]):  # 只顯示前3個
                        text = elem.get_text().strip()
                        if text:
                            print(f"    元素{i+1}: '{text}'")
                else:
                    print(f"  ❌ 未找到 {selector}")
            except Exception as e:
                print(f"  ⚠️ {selector} 選擇器錯誤: {e}")
        
        # 檢查漲跌相關元素
        print(f"\n📈 檢查漲跌相關元素:")
        
        change_selectors = [
            '.Fz\\(20px\\)',
            '.Fz(20px)',
            '[class*="20px"]',
            '[class*="change"]',
            '[class*="Change"]'
        ]
        
        for selector in change_selectors:
            try:
                elements = soup.select(selector)
                if elements:
                    print(f"  ✅ 找到 {selector}: {len(elements)} 個元素")
                    for i, elem in enumerate(elements[:3]):
                        text = elem.get_text().strip()
                        if text:
                            print(f"    元素{i+1}: '{text}'")
                else:
                    print(f"  ❌ 未找到 {selector}")
            except Exception as e:
                print(f"  ⚠️ {selector} 選擇器錯誤: {e}")
        
        # 檢查主要容器
        print(f"\n🎯 檢查主要容器:")
        
        main_selectors = [
            '#main-0-QuoteHeader-Proxy',
            '[id*="QuoteHeader"]',
            '[class*="QuoteHeader"]',
            '[data-symbol]'
        ]
        
        for selector in main_selectors:
            try:
                elements = soup.select(selector)
                if elements:
                    print(f"  ✅ 找到 {selector}: {len(elements)} 個元素")
                    if elements:
                        elem = elements[0]
                        # 檢查內部的趨勢元素
                        down_elements = elem.select('[class*="trend-down"]') or elem.select('[class*="Down"]')
                        up_elements = elem.select('[class*="trend-up"]') or elem.select('[class*="Up"]')
                        print(f"    下跌元素: {len(down_elements)} 個")
                        print(f"    上漲元素: {len(up_elements)} 個")
                else:
                    print(f"  ❌ 未找到 {selector}")
            except Exception as e:
                print(f"  ⚠️ {selector} 選擇器錯誤: {e}")
        
        # 搜尋數字模式
        print(f"\n🔢 搜尋數字模式:")
        
        # 尋找可能的股價數字
        price_patterns = [
            r'\b\d{1,4}(?:,\d{3})*(?:\.\d{1,2})?\b',  # 一般價格格式
            r'\b\d{2,4}\.\d{1,2}\b',  # 簡單小數格式
            r'\b[1-9]\d{2,3}\b'  # 三到四位數整數
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                # 過濾可能的股價範圍
                potential_prices = [m for m in matches if 10 <= float(m.replace(',', '')) <= 10000]
                if potential_prices:
                    print(f"  可能的股價: {potential_prices[:10]}")  # 只顯示前10個
        
        # 檢查JavaScript內容
        print(f"\n🔧 檢查JavaScript內容:")
        script_tags = soup.find_all('script')
        print(f"  找到 {len(script_tags)} 個script標籤")
        
        for i, script in enumerate(script_tags):
            if script.string and stock_code in script.string:
                print(f"  Script{i+1} 包含股票代碼")
                # 尋找JSON數據
                if '"price"' in script.string or '"currentPrice"' in script.string:
                    print(f"    可能包含價格數據")
        
        # 檢查meta標籤
        print(f"\n📋 檢查meta標籤:")
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            if meta.get('property') or meta.get('name'):
                content = meta.get('content', '')
                if stock_code in content or any(char.isdigit() for char in content):
                    prop = meta.get('property') or meta.get('name')
                    print(f"  {prop}: {content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 Yahoo股價頁面結構調試工具")
    print("=" * 60)
    
    # 測試台積電
    success = debug_yahoo_page('2330')
    
    if success:
        print(f"\n💡 調試完成！")
        print(f"📝 建議:")
        print(f"  1. 檢查頁面是否需要JavaScript渲染")
        print(f"  2. 確認CSS選擇器是否正確")
        print(f"  3. 考慮使用Selenium進行動態內容抓取")
        print(f"  4. 檢查是否有API接口可用")
    else:
        print(f"\n❌ 調試失敗，請檢查網路連線")

if __name__ == "__main__":
    main()
