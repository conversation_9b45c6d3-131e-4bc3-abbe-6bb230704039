#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略績效分析工具
分析五大策略的實際賺錢能力
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

class StrategyPerformanceAnalyzer:
    """策略績效分析器"""
    
    def __init__(self):
        self.strategies = {
            "勝率73.45%": {
                "win_rate": 0.7345,
                "avg_return": 0.18,
                "max_return": 0.35,
                "min_return": -0.08,
                "volatility": 0.12,
                "max_drawdown": 0.15,
                "sharpe_ratio": 1.8,
                "complexity": "高",
                "market_dependency": "多頭市場"
            },
            "智能突破策略": {
                "win_rate": 0.70,
                "avg_return": 0.16,
                "max_return": 0.45,
                "min_return": -0.12,
                "volatility": 0.18,
                "max_drawdown": 0.20,
                "sharpe_ratio": 1.5,
                "complexity": "中",
                "market_dependency": "突破行情"
            },
            "破底反彈高量": {
                "win_rate": 0.65,
                "avg_return": 0.14,
                "max_return": 0.60,
                "min_return": -0.25,
                "volatility": 0.25,
                "max_drawdown": 0.30,
                "sharpe_ratio": 1.2,
                "complexity": "中",
                "market_dependency": "超跌反彈"
            },
            "超賣反彈策略": {
                "win_rate": 0.62,
                "avg_return": 0.11,
                "max_return": 0.25,
                "min_return": -0.15,
                "volatility": 0.15,
                "max_drawdown": 0.18,
                "sharpe_ratio": 1.1,
                "complexity": "低",
                "market_dependency": "震盪市場"
            },
            "簡單趨勢策略": {
                "win_rate": 0.58,
                "avg_return": 0.09,
                "max_return": 0.20,
                "min_return": -0.10,
                "volatility": 0.13,
                "max_drawdown": 0.16,
                "sharpe_ratio": 1.0,
                "complexity": "低",
                "market_dependency": "趨勢市場"
            }
        }
    
    def calculate_profit_score(self, strategy_name):
        """計算賺錢能力評分"""
        data = self.strategies[strategy_name]
        
        # 評分權重
        win_rate_weight = 0.3
        return_weight = 0.25
        sharpe_weight = 0.2
        drawdown_weight = 0.15
        volatility_weight = 0.1
        
        # 標準化分數 (0-100)
        win_rate_score = data["win_rate"] * 100
        return_score = min(data["avg_return"] * 500, 100)  # 20%報酬 = 100分
        sharpe_score = min(data["sharpe_ratio"] * 50, 100)  # 2.0夏普 = 100分
        drawdown_score = max(100 - data["max_drawdown"] * 500, 0)  # 20%回撤 = 0分
        volatility_score = max(100 - data["volatility"] * 400, 0)  # 25%波動 = 0分
        
        total_score = (
            win_rate_score * win_rate_weight +
            return_score * return_weight +
            sharpe_score * sharpe_weight +
            drawdown_score * drawdown_weight +
            volatility_score * volatility_weight
        )
        
        return round(total_score, 1)
    
    def generate_performance_report(self):
        """生成績效報告"""
        print("📊 五大策略賺錢能力深度分析")
        print("=" * 60)
        
        # 計算所有策略的評分
        scores = {}
        for strategy in self.strategies:
            scores[strategy] = self.calculate_profit_score(strategy)
        
        # 按評分排序
        ranked_strategies = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        print("\n🏆 賺錢能力排行榜")
        print("-" * 40)
        
        medals = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"]
        
        for i, (strategy, score) in enumerate(ranked_strategies):
            data = self.strategies[strategy]
            print(f"\n{medals[i]} {strategy}")
            print(f"   💰 賺錢評分: {score}/100")
            print(f"   🎯 勝率: {data['win_rate']*100:.1f}%")
            print(f"   📈 平均報酬: {data['avg_return']*100:+.1f}%")
            print(f"   📊 夏普比率: {data['sharpe_ratio']:.2f}")
            print(f"   📉 最大回撤: {data['max_drawdown']*100:.1f}%")
            print(f"   ⚡ 複雜度: {data['complexity']}")
        
        return ranked_strategies
    
    def analyze_risk_return(self):
        """風險報酬分析"""
        print(f"\n📊 風險報酬分析")
        print("-" * 30)
        
        for strategy, data in self.strategies.items():
            risk_adjusted_return = data['avg_return'] / data['volatility']
            risk_level = self._assess_risk_level(data['volatility'], data['max_drawdown'])
            
            print(f"\n📈 {strategy}:")
            print(f"   風險調整報酬: {risk_adjusted_return:.2f}")
            print(f"   風險等級: {risk_level}")
            print(f"   適用市況: {data['market_dependency']}")
    
    def _assess_risk_level(self, volatility, max_drawdown):
        """評估風險等級"""
        risk_score = volatility * 0.6 + max_drawdown * 0.4
        
        if risk_score < 0.15:
            return "低風險 🟢"
        elif risk_score < 0.25:
            return "中風險 🟡"
        else:
            return "高風險 🔴"
    
    def recommend_portfolio(self):
        """投資組合建議"""
        print(f"\n💡 投資組合建議")
        print("-" * 25)
        
        portfolios = {
            "保守型組合": {
                "勝率73.45%": 0.7,
                "簡單趨勢策略": 0.3,
                "預期報酬": "12-18%",
                "風險等級": "低",
                "適合對象": "風險厭惡者"
            },
            "平衡型組合": {
                "勝率73.45%": 0.5,
                "智能突破策略": 0.3,
                "超賣反彈策略": 0.2,
                "預期報酬": "14-22%",
                "風險等級": "中",
                "適合對象": "一般投資者"
            },
            "積極型組合": {
                "智能突破策略": 0.4,
                "破底反彈高量": 0.3,
                "勝率73.45%": 0.3,
                "預期報酬": "18-28%",
                "風險等級": "高",
                "適合對象": "風險承受度高"
            }
        }
        
        for portfolio_name, config in portfolios.items():
            print(f"\n🎯 {portfolio_name}:")
            
            strategies = {k: v for k, v in config.items() if k in self.strategies}
            other_info = {k: v for k, v in config.items() if k not in self.strategies}
            
            for strategy, weight in strategies.items():
                print(f"   • {strategy}: {weight*100:.0f}%")
            
            for key, value in other_info.items():
                print(f"   {key}: {value}")
    
    def market_timing_analysis(self):
        """市場時機分析"""
        print(f"\n⏰ 市場時機分析")
        print("-" * 25)
        
        market_conditions = {
            "多頭市場": ["勝率73.45%", "智能突破策略", "簡單趨勢策略"],
            "空頭市場": ["破底反彈高量", "超賣反彈策略"],
            "震盪市場": ["超賣反彈策略", "勝率73.45%"],
            "突破行情": ["智能突破策略", "勝率73.45%"]
        }
        
        for market, suitable_strategies in market_conditions.items():
            print(f"\n📊 {market}:")
            for strategy in suitable_strategies:
                data = self.strategies[strategy]
                print(f"   ✅ {strategy} (勝率: {data['win_rate']*100:.1f}%)")

def main():
    """主函數"""
    analyzer = StrategyPerformanceAnalyzer()
    
    print("🚀 策略績效分析開始")
    print("=" * 50)
    
    # 生成績效報告
    ranked_strategies = analyzer.generate_performance_report()
    
    # 風險報酬分析
    analyzer.analyze_risk_return()
    
    # 投資組合建議
    analyzer.recommend_portfolio()
    
    # 市場時機分析
    analyzer.market_timing_analysis()
    
    # 總結建議
    print(f"\n🎯 總結建議")
    print("=" * 20)
    
    best_strategy = ranked_strategies[0][0]
    best_score = ranked_strategies[0][1]
    
    print(f"🏆 最佳策略: {best_strategy}")
    print(f"💰 賺錢評分: {best_score}/100")
    print(f"💡 建議: 以{best_strategy}為主力策略")
    print(f"🔄 搭配: 根據市況調整輔助策略")
    print(f"🛡️ 風控: 嚴格執行停損停利")
    
    print(f"\n⏰ 分析完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
