#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查現金流量表更新狀況
"""

import sqlite3
import os
from datetime import datetime

def check_cash_flows_update():
    """檢查現金流量表更新狀況"""
    
    print("=" * 80)
    print("📊 檢查現金流量表更新狀況")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    if os.path.exists(cash_flows_db):
        conn = sqlite3.connect(cash_flows_db)
        cursor = conn.cursor()
        
        # 檢查最新資料日期
        cursor.execute("SELECT MAX(date) FROM cash_flows")
        latest_date = cursor.fetchone()[0]
        
        # 檢查總資料筆數
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        total_count = cursor.fetchone()[0]
        
        # 檢查最新一季的資料筆數
        cursor.execute("SELECT COUNT(*) FROM cash_flows WHERE date = ?", (latest_date,))
        latest_count = cursor.fetchone()[0]
        
        print(f"📊 現金流量表更新狀況:")
        print(f"   📅 最新資料日期: {latest_date}")
        print(f"   📊 總資料筆數: {total_count:,}")
        print(f"   📈 最新一季筆數: {latest_count:,}")
        
        # 檢查是否有今年的資料
        current_year = datetime.now().year
        cursor.execute("SELECT COUNT(*) FROM cash_flows WHERE date LIKE ?", (f"{current_year}%",))
        current_year_count = cursor.fetchone()[0]
        print(f"   📅 {current_year}年資料筆數: {current_year_count:,}")
        
        # 檢查台積電最新資料
        cursor.execute("""
            SELECT date, 營業活動之淨現金流入（流出）, 投資活動之淨現金流入（流出）, 籌資活動之淨現金流入（流出）
            FROM cash_flows 
            WHERE stock_id = '2330' 
            ORDER BY date DESC 
            LIMIT 3
        """)
        
        tsmc_data = cursor.fetchall()
        if tsmc_data:
            print(f"\n📈 台積電最新現金流量:")
            for row in tsmc_data:
                date, operating, investing, financing = row
                print(f"   {date}: 營業 {operating:,.0f}, 投資 {investing:,.0f}, 籌資 {financing:,.0f}")
        
        # 檢查各年度資料分布
        cursor.execute("""
            SELECT SUBSTR(date, 1, 4) as year, COUNT(*) as count
            FROM cash_flows 
            GROUP BY SUBSTR(date, 1, 4)
            ORDER BY year DESC
            LIMIT 5
        """)
        
        year_data = cursor.fetchall()
        print(f"\n📅 各年度資料分布:")
        for year, count in year_data:
            print(f"   {year}年: {count:,} 筆")
        
        conn.close()
        
        # 判斷是否需要更新
        if latest_date and latest_date.startswith(str(current_year)):
            print(f"\n✅ 現金流量表資料已是最新 ({latest_date})")
        else:
            print(f"\n⚠️ 現金流量表資料需要更新 (最新: {latest_date})")
            
        return True
        
    else:
        print(f"❌ 現金流量表檔案不存在: {cash_flows_db}")
        return False

def manual_update_cash_flows():
    """手動執行現金流量表更新"""
    
    print(f"\n" + "=" * 80)
    print(f"🔄 手動執行現金流量表更新")
    print("=" * 80)
    
    try:
        import sys
        import os
        
        # 添加 finlab 路徑
        current_dir = os.path.dirname(os.path.abspath(__file__))
        finlab_path = os.path.join(current_dir, 'finlab')
        if finlab_path not in sys.path:
            sys.path.insert(0, finlab_path)
        
        from crawler import crawl_cash_flows
        from datetime import datetime
        
        print("📦 成功導入 crawl_cash_flows 函數")
        
        # 使用當前日期
        current_date = datetime.now()
        print(f"📅 更新日期: {current_date.strftime('%Y-%m-%d')}")
        
        print("🔄 開始執行現金流量表更新...")
        result = crawl_cash_flows(current_date)
        
        if result is not None:
            print(f"✅ 現金流量表更新成功！")
            if hasattr(result, '__len__'):
                print(f"📊 更新資料筆數: {len(result):,}")
            
            # 重新檢查更新後的狀況
            print(f"\n📊 更新後狀況:")
            check_cash_flows_update()
            
            return True
        else:
            print(f"⚠️ 現金流量表更新返回空結果")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 執行現金流量表更新失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🔍 現金流量表更新檢查")
    
    # 檢查當前狀況
    current_status = check_cash_flows_update()
    
    if current_status:
        # 詢問是否要手動更新
        print(f"\n💡 是否要執行手動更新？")
        choice = input("輸入 'y' 執行更新，其他鍵跳過: ").strip().lower()
        
        if choice == 'y':
            update_result = manual_update_cash_flows()
            
            if update_result:
                print(f"\n🎉 現金流量表更新完成！")
            else:
                print(f"\n⚠️ 現金流量表更新失敗")
        else:
            print(f"\n⏭️ 跳過手動更新")
    
    print(f"\n" + "=" * 80)
    print(f"📊 總結")
    print("=" * 80)
    print(f"✅ 現金流量表爬蟲仍可正常使用")
    print(f"📊 建議定期執行: python auto_update.py cash_flows")
    print(f"💰 現金流量分析是投資分析的重要組成部分")

if __name__ == "__main__":
    main()
