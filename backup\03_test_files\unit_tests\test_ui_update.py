#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試界面更新過程
檢查為什麼界面組件沒有顯示結果
"""

import sys
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_ui_update_directly():
    """直接測試界面更新"""
    print("Testing UI update directly...")
    
    try:
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主窗口
        window = QMainWindow()
        window.setWindowTitle("UI Update Test")
        window.setGeometry(100, 100, 600, 400)
        
        # 創建中央widget
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建界面組件（模擬主程式）
        market_sentiment_label = QLabel("Market Sentiment: Waiting...")
        us_status_label = QLabel("US Market: Waiting...")
        tw_status_label = QLabel("TW Market: Waiting...")
        premarket_status_label = QLabel("Premarket: Waiting...")
        
        layout.addWidget(QLabel("=== UI Update Test ==="))
        layout.addWidget(market_sentiment_label)
        layout.addWidget(us_status_label)
        layout.addWidget(tw_status_label)
        layout.addWidget(premarket_status_label)
        
        # 測試按鈕
        test_btn = QPushButton("Test Update")
        layout.addWidget(test_btn)
        
        def test_update():
            print("Updating UI components...")
            
            # 獲取真實數據
            try:
                from monitoring.pre_market_monitor import PreMarketMonitor
                monitor = PreMarketMonitor()
                results = monitor.run_full_scan()
                
                if results:
                    print("Got scan results, updating UI...")
                    
                    # 更新市場情緒
                    try:
                        sentiment = monitor.get_market_sentiment()
                        market_sentiment_label.setText(f"Market Sentiment: {sentiment}")
                        print(f"Updated sentiment: {sentiment}")
                    except Exception as e:
                        print(f"Failed to update sentiment: {e}")
                    
                    # 更新美股狀態
                    us_data = results.get('us_indices', {})
                    if us_data:
                        us_changes = [data.get('change_pct', 0) for data in us_data.values() 
                                    if isinstance(data, dict) and 'change_pct' in data]
                        if us_changes:
                            avg_change = sum(us_changes) / len(us_changes)
                            trend = "UP" if avg_change > 0 else "DOWN" if avg_change < 0 else "FLAT"
                            us_status_label.setText(f"US Market: {trend} {avg_change:+.2f}%")
                            print(f"Updated US status: {trend} {avg_change:+.2f}%")
                    
                    # 更新台指期狀態
                    tw_data = results.get('taiwan_futures', {})
                    if tw_data:
                        tw_index = tw_data.get('台股加權指數', {})
                        if tw_index and isinstance(tw_index, dict):
                            price = tw_index.get('price', 0)
                            change_pct = tw_index.get('change_pct', 0)
                            trend = "UP" if change_pct > 0 else "DOWN" if change_pct < 0 else "FLAT"
                            tw_status_label.setText(f"TW Market: {trend} {price} ({change_pct:+.2f}%)")
                            print(f"Updated TW status: {trend} {price} ({change_pct:+.2f}%)")
                    
                    # 更新總體狀態
                    total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                    premarket_status_label.setText(f"Scan Complete: {total_items} items")
                    print(f"Updated premarket status: {total_items} items")
                    
                    print("UI update completed!")
                else:
                    print("No scan results")
                    premarket_status_label.setText("Scan failed: No results")
                    
            except Exception as e:
                print(f"Update failed: {e}")
                premarket_status_label.setText(f"Update failed: {e}")
        
        test_btn.clicked.connect(test_update)
        
        # 顯示窗口
        window.show()
        
        print("Window shown. Click 'Test Update' to test UI updates.")
        print("Check if the labels update correctly.")
        
        # 自動測試
        QTimer.singleShot(1000, test_update)  # 1秒後自動測試
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"UI test failed: {e}")
        return 1

def test_main_program_components():
    """測試主程式的組件是否存在"""
    print("\nTesting main program components...")
    
    try:
        # 嘗試導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        print("Creating main program instance...")
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 創建主程式實例
        main_window = StockScreenerGUI()
        
        # 檢查關鍵組件是否存在
        components_to_check = [
            'market_sentiment_label',
            'us_status_label', 
            'tw_futures_label',
            'premarket_status_label',
            'scan_btn'
        ]
        
        print("Checking components:")
        for component_name in components_to_check:
            if hasattr(main_window, component_name):
                component = getattr(main_window, component_name)
                print(f"  ✓ {component_name}: {type(component).__name__}")
                
                # 嘗試更新組件
                try:
                    if hasattr(component, 'setText'):
                        original_text = component.text()
                        component.setText("TEST UPDATE")
                        new_text = component.text()
                        component.setText(original_text)  # 恢復原文
                        print(f"    - Text update test: {'PASS' if new_text == 'TEST UPDATE' else 'FAIL'}")
                except Exception as e:
                    print(f"    - Text update test: FAIL ({e})")
            else:
                print(f"  ✗ {component_name}: NOT FOUND")
        
        # 測試掃描功能
        print("\nTesting scan function...")
        if hasattr(main_window, '_on_scan_success'):
            print("  ✓ _on_scan_success method exists")
            
            # 獲取測試數據
            try:
                from monitoring.pre_market_monitor import PreMarketMonitor
                monitor = PreMarketMonitor()
                results = monitor.run_full_scan()
                
                if results:
                    print("  ✓ Got scan results")
                    
                    # 測試回調
                    try:
                        print("  Testing _on_scan_success callback...")
                        main_window._on_scan_success(results)
                        print("  ✓ _on_scan_success executed without error")
                    except Exception as e:
                        print(f"  ✗ _on_scan_success failed: {e}")
                else:
                    print("  ✗ No scan results")
            except Exception as e:
                print(f"  ✗ Scan failed: {e}")
        else:
            print("  ✗ _on_scan_success method not found")
        
        return True
        
    except Exception as e:
        print(f"Main program test failed: {e}")
        return False

def main():
    """主函數"""
    print("UI Update Test")
    print("=" * 50)
    
    # 1. 測試主程式組件
    main_test_success = test_main_program_components()
    
    if main_test_success:
        print("\nMain program components test: PASS")
    else:
        print("\nMain program components test: FAIL")
    
    # 2. 直接測試界面更新
    print("\nStarting direct UI update test...")
    ui_result = test_ui_update_directly()
    
    print(f"\nTest completed with result: {ui_result}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Test failed: {e}")
