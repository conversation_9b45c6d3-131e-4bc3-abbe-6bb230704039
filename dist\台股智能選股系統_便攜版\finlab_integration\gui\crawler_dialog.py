#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab GUI整合模組
為主程式添加finlab功能界面
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QDateEdit, QProgressBar, QTextEdit, QGroupBox,
                            QGridLayout, QSpinBox, QCheckBox, QComboBox, QMessageBox)
from PyQt6.QtCore import QThread, pyqtSignal, QDate, QTimer
from PyQt6.QtGui import QFont
import datetime
import logging

class FinlabCrawlerDialog(QDialog):
    """Finlab爬蟲對話框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.crawler_thread = None
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("📊 Finlab專業數據爬取")
        self.setGeometry(200, 200, 600, 500)
        
        layout = QVBoxLayout()
        
        # 標題
        title = QLabel("🕷️ Finlab專業股市數據爬取系統")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 日期選擇區域
        date_group = QGroupBox("📅 爬取日期設定")
        date_layout = QGridLayout()
        
        date_layout.addWidget(QLabel("開始日期:"), 0, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        date_layout.addWidget(self.start_date, 0, 1)
        
        date_layout.addWidget(QLabel("結束日期:"), 0, 2)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        date_layout.addWidget(self.end_date, 0, 3)
        
        date_group.setLayout(date_layout)
        layout.addWidget(date_group)
        
        # 爬取選項
        options_group = QGroupBox("⚙️ 爬取選項")
        options_layout = QGridLayout()
        
        self.crawl_price = QCheckBox("股價數據")
        self.crawl_price.setChecked(True)
        options_layout.addWidget(self.crawl_price, 0, 0)
        
        self.crawl_volume = QCheckBox("成交量數據")
        self.crawl_volume.setChecked(True)
        options_layout.addWidget(self.crawl_volume, 0, 1)
        
        self.update_only = QCheckBox("僅更新缺失數據")
        self.update_only.setChecked(True)
        options_layout.addWidget(self.update_only, 1, 0)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # 進度顯示
        progress_group = QGroupBox("📊 爬取進度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        progress_layout.addWidget(self.status_text)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 開始爬取")
        self.start_button.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ 停止爬取")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.close_button = QPushButton("❌ 關閉")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def start_crawling(self):
        """開始爬取"""
        try:
            start_date = self.start_date.date().toPython()
            end_date = self.end_date.date().toPython()
            
            if start_date > end_date:
                QMessageBox.warning(self, "錯誤", "開始日期不能晚於結束日期")
                return
            
            # 創建爬取線程
            self.crawler_thread = CrawlerThread(
                start_date=start_date,
                end_date=end_date,
                db_connections=getattr(self.parent, 'db_connections', {}),
                update_only=self.update_only.isChecked()
            )
            
            # 連接信號
            self.crawler_thread.progress_updated.connect(self.update_progress)
            self.crawler_thread.status_updated.connect(self.update_status)
            self.crawler_thread.finished.connect(self.crawling_finished)
            
            # 開始爬取
            self.crawler_thread.start()
            
            # 更新UI狀態
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setValue(0)
            self.status_text.clear()
            self.update_status("開始爬取數據...")
            
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"啟動爬取失敗: {str(e)}")
    
    def stop_crawling(self):
        """停止爬取"""
        if self.crawler_thread and self.crawler_thread.isRunning():
            self.crawler_thread.terminate()
            self.crawler_thread.wait()
            self.crawling_finished()
            self.update_status("爬取已停止")
    
    def crawling_finished(self):
        """爬取完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100)
    
    def update_progress(self, value):
        """更新進度條"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新狀態信息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")

class CrawlerThread(QThread):
    """爬取線程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    
    def __init__(self, start_date, end_date, db_connections, update_only=True):
        super().__init__()
        self.start_date = start_date
        self.end_date = end_date
        self.db_connections = db_connections
        self.update_only = update_only
    
    def run(self):
        """執行爬取"""
        try:
            from finlab_integration.core.crawler_adapter import FinlabCrawlerAdapter
            
            crawler = FinlabCrawlerAdapter(self.db_connections)
            
            if self.update_only:
                self.status_updated.emit("更新到最新數據...")
                success_count = crawler.update_to_latest()
                self.status_updated.emit(f"更新完成，處理了 {success_count} 個交易日")
            else:
                self.status_updated.emit(f"爬取 {self.start_date} 到 {self.end_date} 的數據...")
                success_count = crawler.crawl_date_range(self.start_date, self.end_date)
                self.status_updated.emit(f"爬取完成，處理了 {success_count} 個交易日")
            
            self.progress_updated.emit(100)
            
        except Exception as e:
            self.status_updated.emit(f"爬取失敗: {str(e)}")
            logging.error(f"爬取線程錯誤: {e}")
