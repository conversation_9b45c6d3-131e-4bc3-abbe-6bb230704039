#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 process_github_monthly_data 函數移動後是否正常工作
"""

import sys
import os
import datetime
import pandas as pd

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加 finlab 路徑
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_function_import():
    """測試函數導入"""
    print("🧪 測試 process_github_monthly_data 函數移動")
    print("=" * 60)
    
    try:
        # 測試從 crawler.py 導入
        print("📦 測試從 crawler.py 導入...")
        from crawler import process_github_monthly_data
        print("✅ 成功從 crawler.py 導入 process_github_monthly_data")
        
        # 測試從 auto_update.py 導入
        print("\n📦 測試從 auto_update.py 導入...")
        from auto_update import process_github_monthly_data as auto_process_github_monthly_data
        print("✅ 成功從 auto_update.py 導入 process_github_monthly_data")
        
        # 檢查函數是否相同
        if process_github_monthly_data == auto_process_github_monthly_data:
            print("✅ 兩個導入的函數是同一個")
        else:
            print("⚠️ 兩個導入的函數不同")
        
        # 測試函數基本功能
        print("\n🔧 測試函數基本功能...")
        
        # 創建測試資料
        test_data = pd.DataFrame({
            0: ['1101', '1102', '1103'],  # 股票代碼
            1: ['台泥', '亞泥', '嘉泥'],  # 公司名稱
            2: ['1000000', '2000000', '3000000'],  # 當月營收
            3: ['900000', '1800000', '2700000'],   # 上月營收
            4: ['950000', '1900000', '2800000'],   # 去年當月營收
            5: ['11.1', '11.1', '11.1'],           # 上月比較增減(%)
            6: ['5.3', '5.3', '7.1'],              # 去年同月增減(%)
            7: ['10000000', '20000000', '30000000'], # 當月累計營收
            8: ['9500000', '19000000', '28000000'],  # 去年累計營收
            9: ['5.3', '5.3', '7.1'],              # 前期比較增減(%)
            10: ['', '', '']                       # 備註
        })
        
        test_date = datetime.date(2024, 11, 1)
        test_dfs = [test_data]
        
        # 調用函數
        result = process_github_monthly_data(test_dfs, test_date, 'sii')
        
        if len(result) > 0:
            print(f"✅ 函數執行成功: {len(result)} 筆資料")
            print(f"   欄位: {list(result.columns)}")
            print(f"   前3筆:")
            print(result.head(3))
        else:
            print("⚠️ 函數執行成功但無資料")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_auto_update_structure():
    """測試 auto_update.py 的結構化"""
    print(f"\n🏗️ 測試 auto_update.py 結構化")
    print("=" * 60)
    
    try:
        # 檢查 auto_update.py 的大小
        auto_update_path = "auto_update.py"
        if os.path.exists(auto_update_path):
            file_size = os.path.getsize(auto_update_path)
            with open(auto_update_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📁 auto_update.py 狀態:")
            print(f"   檔案大小: {file_size / 1024:.1f} KB")
            print(f"   總行數: {len(lines)}")
            
            # 檢查是否還有 process_github_monthly_data 定義
            has_function_def = any('def process_github_monthly_data' in line for line in lines)
            if has_function_def:
                print("⚠️ auto_update.py 中仍有 process_github_monthly_data 定義")
            else:
                print("✅ auto_update.py 中已移除 process_github_monthly_data 定義")
            
            # 檢查導入
            has_import = any('process_github_monthly_data' in line and 'import' in line for line in lines)
            if has_import:
                print("✅ auto_update.py 中有正確的導入")
            else:
                print("⚠️ auto_update.py 中沒有導入")
        
        # 檢查 crawler.py 的狀態
        crawler_path = "finlab/crawler.py"
        if os.path.exists(crawler_path):
            with open(crawler_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n📁 crawler.py 狀態:")
            print(f"   總行數: {len(lines)}")
            
            # 檢查是否有 process_github_monthly_data 定義
            has_function_def = any('def process_github_monthly_data' in line for line in lines)
            if has_function_def:
                print("✅ crawler.py 中有 process_github_monthly_data 定義")
            else:
                print("❌ crawler.py 中沒有 process_github_monthly_data 定義")
        
        print(f"\n🎉 結構化測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 結構化測試失敗: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 函數移動測試工具")
    print("=" * 70)
    print("🎯 目標: 測試 process_github_monthly_data 函數移動到 crawler.py")
    print("=" * 70)
    
    # 測試函數導入
    import_success = test_function_import()
    
    # 測試結構化
    structure_success = test_auto_update_structure()
    
    # 總結
    print(f"\n📊 測試總結:")
    print(f"   函數導入: {'✅ 成功' if import_success else '❌ 失敗'}")
    print(f"   結構化: {'✅ 成功' if structure_success else '❌ 失敗'}")
    
    if import_success and structure_success:
        print(f"\n🎉 函數移動完全成功！")
        print(f"✅ process_github_monthly_data 已成功移動到 crawler.py")
        print(f"✅ auto_update.py 結構更加清晰")
        print(f"✅ 導入關係正確")
    else:
        print(f"\n⚠️ 需要進一步調整")
