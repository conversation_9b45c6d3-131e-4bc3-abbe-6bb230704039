#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較兩個 benchmark.pkl 檔案是否可以合併
"""

import pandas as pd
import os
from datetime import datetime

def compare_benchmark_files():
    """比較兩個 benchmark.pkl 檔案"""
    print("🔍 比較兩個 benchmark.pkl 檔案")
    print("=" * 60)
    
    file1 = r"D:\Finlab\backup\O3mh_strategy2AA\history\tables\benchmark.pkl"
    file2 = r"D:\Finlab\backup\O3mh_strategy6AA\history\tables\benchmark.pkl"
    
    files_info = []
    
    for i, file_path in enumerate([file1, file2], 1):
        print(f"\n📊 檔案 {i}: ...{file_path[-20:]}")
        
        if not os.path.exists(file_path):
            print(f"   ❌ 檔案不存在")
            continue
        
        try:
            # 基本資訊
            file_size = os.path.getsize(file_path)
            file_mtime = os.path.getmtime(file_path)
            
            print(f"   大小: {file_size/1024/1024:.1f} MB")
            print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
            
            # 讀取資料（只讀取前1000筆來快速分析）
            print("   正在讀取資料樣本...")
            data = pd.read_pickle(file_path)
            
            # 取樣本進行分析
            sample_size = min(1000, len(data))
            sample_data = data.iloc[:sample_size]
            
            print(f"   總筆數: {len(data):,}")
            print(f"   樣本筆數: {sample_size}")
            print(f"   欄位數: {len(data.columns)}")
            print(f"   欄位名稱: {list(data.columns)[:3]}...")
            
            # 檢查索引結構
            print(f"   索引類型: {type(data.index).__name__}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
            
            # 檢查台股指數
            if '發行量加權股價指數' in data.columns:
                taiex = data['發行量加權股價指數']
                
                # 使用樣本計算範圍（避免處理整個大檔案）
                sample_taiex = sample_data['發行量加權股價指數']
                
                print(f"   台股指數樣本範圍: {sample_taiex.min():.0f} - {sample_taiex.max():.0f}")
                
                # 嘗試獲取全部範圍（如果不會太慢）
                try:
                    full_min = taiex.min()
                    full_max = taiex.max()
                    print(f"   台股指數全範圍: {full_min:.0f} - {full_max:.0f}")
                    
                    files_info.append({
                        'path': file_path,
                        'size_mb': file_size/1024/1024,
                        'records': len(data),
                        'taiex_min': full_min,
                        'taiex_max': full_max,
                        'columns': list(data.columns),
                        'index_type': type(data.index).__name__,
                        'index_names': getattr(data.index, 'names', None)
                    })
                    
                except:
                    print(f"   ⚠️ 無法快速計算全範圍")
                    files_info.append({
                        'path': file_path,
                        'size_mb': file_size/1024/1024,
                        'records': len(data),
                        'taiex_min': sample_taiex.min(),
                        'taiex_max': sample_taiex.max(),
                        'columns': list(data.columns),
                        'index_type': type(data.index).__name__,
                        'index_names': getattr(data.index, 'names', None)
                    })
            
        except Exception as e:
            print(f"   ❌ 分析失敗: {str(e)}")
            import traceback
            print(f"   詳細錯誤: {traceback.format_exc()}")
    
    # 比較分析
    if len(files_info) >= 2:
        print("\n" + "=" * 60)
        print("📊 合併可行性分析")
        print("=" * 60)
        
        file1_info, file2_info = files_info[0], files_info[1]
        
        print(f"📋 檔案對比:")
        print(f"   檔案1: {file1_info['size_mb']:.1f}MB, {file1_info['records']:,}筆")
        print(f"   檔案2: {file2_info['size_mb']:.1f}MB, {file2_info['records']:,}筆")
        
        print(f"\n📈 台股指數範圍:")
        print(f"   檔案1: {file1_info['taiex_min']:.0f} - {file1_info['taiex_max']:.0f}")
        print(f"   檔案2: {file2_info['taiex_min']:.0f} - {file2_info['taiex_max']:.0f}")
        
        # 檢查結構相容性
        print(f"\n🔧 結構相容性:")
        
        # 欄位比較
        cols1 = set(file1_info['columns'])
        cols2 = set(file2_info['columns'])
        
        if cols1 == cols2:
            print(f"   ✅ 欄位完全相同")
        else:
            common_cols = cols1.intersection(cols2)
            only1 = cols1 - cols2
            only2 = cols2 - cols1
            
            print(f"   ⚠️ 欄位有差異:")
            print(f"     共同欄位: {len(common_cols)}")
            print(f"     檔案1獨有: {list(only1)}")
            print(f"     檔案2獨有: {list(only2)}")
        
        # 索引比較
        if file1_info['index_type'] == file2_info['index_type']:
            print(f"   ✅ 索引類型相同: {file1_info['index_type']}")
        else:
            print(f"   ⚠️ 索引類型不同: {file1_info['index_type']} vs {file2_info['index_type']}")
        
        # 時間範圍分析
        print(f"\n📅 時間範圍分析:")
        
        # 根據台股指數推斷時間
        def estimate_period(taiex_min, taiex_max):
            if taiex_min < 4000:
                start_est = "2008年金融危機前"
            elif taiex_min < 8000:
                start_est = "2008-2015年"
            elif taiex_min < 12000:
                start_est = "2016-2019年"
            else:
                start_est = "2020年後"
            
            if taiex_max > 20000:
                end_est = "2024-2025年"
            elif taiex_max > 15000:
                end_est = "2021-2023年"
            elif taiex_max > 12000:
                end_est = "2018-2020年"
            else:
                end_est = "2018年前"
            
            return f"{start_est} 至 {end_est}"
        
        period1 = estimate_period(file1_info['taiex_min'], file1_info['taiex_max'])
        period2 = estimate_period(file2_info['taiex_min'], file2_info['taiex_max'])
        
        print(f"   檔案1推斷期間: {period1}")
        print(f"   檔案2推斷期間: {period2}")
        
        # 合併建議
        print(f"\n💡 合併建議:")
        
        # 檢查是否有重疊
        overlap = not (file1_info['taiex_max'] < file2_info['taiex_min'] or 
                      file2_info['taiex_max'] < file1_info['taiex_min'])
        
        if cols1 == cols2 and file1_info['index_type'] == file2_info['index_type']:
            if overlap:
                print(f"   ✅ 可以合併（需要處理重疊部分）")
                print(f"   📊 預期合併後大小: {file1_info['size_mb'] + file2_info['size_mb']:.1f}MB（去重前）")
            else:
                print(f"   ✅ 可以完美合併（無重疊）")
                print(f"   📊 預期合併後大小: {file1_info['size_mb'] + file2_info['size_mb']:.1f}MB")
        else:
            print(f"   ⚠️ 結構不同，合併需要額外處理")
        
        return True
    else:
        print("\n❌ 無法比較，檔案讀取失敗")
        return False

if __name__ == "__main__":
    compare_benchmark_files()
