#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的月營收數據測試
基於您提供的實際 GoodInfo Excel 數據格式
"""

import sqlite3
import os
from datetime import datetime

class SimpleRevenueProcessor:
    """簡化的營收數據處理器"""
    
    def __init__(self, db_path="db/monthly_revenue.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化數據庫"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,                    -- 單月營收(億)
                    revenue_mom REAL,               -- 月增率(%)
                    revenue_yoy REAL,               -- 年增率(%)
                    cumulative_revenue REAL,        -- 累計營收(億)
                    cumulative_yoy REAL,            -- 累計年增率(%)
                    
                    -- 股價資訊
                    open_price REAL,                -- 開盤價
                    close_price REAL,               -- 收盤價
                    high_price REAL,                -- 最高價
                    low_price REAL,                 -- 最低價
                    price_change REAL,              -- 漲跌(元)
                    price_change_pct REAL,          -- 漲跌(%)
                    
                    data_source TEXT DEFAULT 'goodinfo_manual',
                    crawl_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 數據庫初始化完成")
            
        except Exception as e:
            print(f"❌ 數據庫初始化失敗: {e}")
    
    def parse_month_string(self, month_str):
        """解析月份字符串 (Jun-25 格式)"""
        try:
            if '-' in month_str:
                month_part, year_part = month_str.split('-')
                
                month_mapping = {
                    'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                    'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                }
                
                month = month_mapping.get(month_part)
                year = int(year_part) + 2000 if int(year_part) < 50 else int(year_part) + 1900
                
                return year, month
            
            return None, None
            
        except Exception as e:
            print(f"⚠️ 解析月份失敗: {month_str}, {e}")
            return None, None
    
    def process_sample_data(self):
        """處理基於您提供的實際數據的示例"""
        # 基於您提供的實際 GoodInfo Excel 數據
        sample_data = [
            # 月別, 開盤, 收盤, 最高, 最低, 漲跌(元), 漲跌(%), 營收(億), 月增(%), 年增(%), 累計營收(億), 累計年增(%)
            ('Jun-25', 958, 1060, 1080, 946, 93, 9.62, 2637, -17.7, 26.9, 17730, 40),
            ('May-25', 938, 967, 1000, 911, 59, 6.5, 3205, -8.31, 39.6, 15093, 42.6),
            ('Apr-25', 929, 908, 952, 780, -2, -0.22, 3496, 22.2, 48.1, 11888, 43.5),
            ('Mar-25', 1000, 910, 1030, 910, -130, -12.5, 2860, 9.97, 46.5, 8393, 41.6),
            ('Feb-25', 1065, 1040, 1125, 1040, -95, -8.37, 2600, -11.3, 43.1, 5533, 39.2),
            ('Jan-25', 1070, 1135, 1160, 1055, 60, 5.58, 2933, 5.43, 35.9, 2933, 35.9),
            ('Dec-24', 1020, 1075, 1095, 1015, 79, 7.93, 2782, 0.76, 57.8, 28943, 33.9),
            ('Nov-24', 996, 996, 1090, 992, -34, -3.3, 2761, -12.2, 34, 26161, 31.8),
            ('Oct-24', 967, 1030, 1100, 967, 73, 7.63, 3142, 24.8, 29.2, 23401, 31.5),
            ('Sep-24', 950, 957, 1025, 888, 13, 1.38, 2519, 0.4, 39.6, 20258, 31.9)
        ]
        
        revenue_records = []
        
        for data_row in sample_data:
            try:
                month_str = data_row[0]
                year, month = self.parse_month_string(month_str)
                
                if not year or not month:
                    continue
                
                record = {
                    'stock_id': '2330',
                    'stock_name': '台積電',
                    'year': year,
                    'month': month,
                    'open_price': data_row[1],
                    'close_price': data_row[2],
                    'high_price': data_row[3],
                    'low_price': data_row[4],
                    'price_change': data_row[5],
                    'price_change_pct': data_row[6],
                    'revenue': data_row[7],
                    'revenue_mom': data_row[8],
                    'revenue_yoy': data_row[9],
                    'cumulative_revenue': data_row[10],
                    'cumulative_yoy': data_row[11],
                    'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                revenue_records.append(record)
                
            except Exception as e:
                print(f"⚠️ 處理數據行失敗: {e}")
                continue
        
        print(f"✅ 成功處理 {len(revenue_records)} 筆數據")
        return revenue_records
    
    def save_to_database(self, revenue_data):
        """儲存數據到數據庫"""
        try:
            if not revenue_data:
                return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            for record in revenue_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue 
                        (stock_id, stock_name, year, month, revenue, revenue_mom, 
                         revenue_yoy, cumulative_revenue, cumulative_yoy,
                         open_price, close_price, high_price, low_price, 
                         price_change, price_change_pct, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_id'], record['stock_name'], record['year'], 
                        record['month'], record['revenue'], record['revenue_mom'],
                        record['revenue_yoy'], record['cumulative_revenue'], 
                        record['cumulative_yoy'], record['open_price'],
                        record['close_price'], record['high_price'], record['low_price'],
                        record['price_change'], record['price_change_pct'], record['crawl_date']
                    ))
                    saved_count += 1
                except Exception as e:
                    print(f"⚠️ 儲存記錄失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            print(f"✅ 成功儲存 {saved_count} 筆數據到數據庫")
            return True
            
        except Exception as e:
            print(f"❌ 儲存數據庫失敗: {e}")
            return False
    
    def query_revenue_data(self, stock_id=None, year=None, limit=10):
        """查詢月營收數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT * FROM monthly_revenue WHERE 1=1"
            params = []
            
            if stock_id:
                query += " AND stock_id = ?"
                params.append(stock_id)
            
            if year:
                query += " AND year = ?"
                params.append(year)
            
            query += " ORDER BY year DESC, month DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # 獲取列名
            columns = [description[0] for description in cursor.description]
            
            conn.close()
            
            # 轉換為字典列表
            data = []
            for row in results:
                data.append(dict(zip(columns, row)))
            
            return data
            
        except Exception as e:
            print(f"❌ 查詢數據失敗: {e}")
            return []
    
    def calculate_revenue_growth(self, stock_id='2330'):
        """計算營收成長相關指標"""
        try:
            data = self.query_revenue_data(stock_id=stock_id, limit=12)
            
            if not data:
                return None
            
            # 計算最近12個月平均年增率
            yoy_rates = [d['revenue_yoy'] for d in data if d['revenue_yoy'] is not None]
            avg_yoy = sum(yoy_rates) / len(yoy_rates) if yoy_rates else 0
            
            # 計算最近3個月平均月增率
            recent_mom = [d['revenue_mom'] for d in data[:3] if d['revenue_mom'] is not None]
            avg_mom = sum(recent_mom) / len(recent_mom) if recent_mom else 0
            
            # 最新月份營收
            latest_revenue = data[0]['revenue'] if data else 0
            
            return {
                'stock_id': stock_id,
                'latest_revenue': latest_revenue,
                'avg_yoy_12m': avg_yoy,
                'avg_mom_3m': avg_mom,
                'data_points': len(data)
            }
            
        except Exception as e:
            print(f"❌ 計算營收成長失敗: {e}")
            return None

def test_simple_processor():
    """測試簡化處理器"""
    print("🚀 測試簡化月營收數據處理器")
    print("=" * 60)
    
    processor = SimpleRevenueProcessor()
    
    # 處理示例數據
    print("\n📊 處理示例數據...")
    revenue_data = processor.process_sample_data()
    
    if revenue_data:
        print(f"✅ 成功處理 {len(revenue_data)} 筆數據")
        
        # 顯示前5筆數據
        print("\n📋 前5筆數據:")
        for i, record in enumerate(revenue_data[:5], 1):
            print(f"{i}. {record['year']}/{record['month']:02d} - "
                  f"營收: {record['revenue']:,}億, "
                  f"年增率: {record['revenue_yoy']:+.1f}%, "
                  f"收盤價: {record['close_price']:,.0f}元")
        
        # 儲存到數據庫
        if processor.save_to_database(revenue_data):
            print("\n✅ 數據已儲存到數據庫")
            
            # 查詢驗證
            print("\n🔍 查詢驗證...")
            query_results = processor.query_revenue_data(stock_id='2330', limit=5)
            
            if query_results:
                print(f"✅ 查詢到 {len(query_results)} 筆數據")
                for result in query_results:
                    print(f"   {result['year']}/{result['month']:02d} - "
                          f"營收: {result['revenue']:,}億, "
                          f"年增率: {result['revenue_yoy']:+.1f}%")
            
            # 計算營收成長指標
            print("\n📈 計算營收成長指標...")
            growth_metrics = processor.calculate_revenue_growth('2330')
            
            if growth_metrics:
                print(f"✅ 台積電營收成長分析:")
                print(f"   最新營收: {growth_metrics['latest_revenue']:,}億")
                print(f"   12個月平均年增率: {growth_metrics['avg_yoy_12m']:+.1f}%")
                print(f"   3個月平均月增率: {growth_metrics['avg_mom_3m']:+.1f}%")
                print(f"   數據點數: {growth_metrics['data_points']} 個月")
            
            print("\n🎉 測試完成！月營收數據處理功能正常運作")
            print("\n📋 下一步建議:")
            print("1. 整合到現有策略系統")
            print("2. 開發自動化 Excel 下載功能")
            print("3. 擴展到更多股票")
            
            return True
        else:
            print("❌ 儲存失敗")
            return False
    else:
        print("❌ 處理失敗")
        return False

if __name__ == "__main__":
    test_simple_processor()
