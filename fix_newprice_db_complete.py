#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整修復 newprice.db 和 price.db 的股票資訊
確保未來的爬蟲能正確生成包含完整股票資訊的資料
"""

import os
import sqlite3
import pandas as pd
from stock_name_mapping import get_cached_stock_info

def fix_database_stock_info(db_path, db_name):
    """修復指定資料庫中缺少的股票資訊"""
    
    print(f"\n🔧 修復 {db_name} 中缺少的股票資訊")
    print("=" * 60)
    
    if not os.path.exists(db_path):
        print(f"❌ 找不到資料庫檔案: {db_path}")
        return False
    
    if os.path.getsize(db_path) == 0:
        print(f"⚠️ 資料庫檔案為空: {db_path}")
        return True
    
    print(f"✅ 找到資料庫: {db_path}")
    print(f"📊 檔案大小: {os.path.getsize(db_path) / (1024*1024):.1f} MB")
    
    try:
        # 獲取最新的股票資訊
        print("📡 獲取最新股票資訊...")
        stock_info = get_cached_stock_info()
        
        if not stock_info:
            print("❌ 無法獲取股票資訊")
            return False
        
        print(f"✅ 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 連接資料庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_daily_data'")
        if not cursor.fetchone():
            print("❌ 找不到 stock_daily_data 表格")
            conn.close()
            return False
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = [col[1] for col in cursor.fetchall()]
        
        # 確保必要的欄位存在
        required_columns = ['stock_name', 'listing_status', 'industry']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"⚠️ 缺少欄位，正在添加: {missing_columns}")
            for col in missing_columns:
                cursor.execute(f"ALTER TABLE stock_daily_data ADD COLUMN {col} TEXT")
        
        # 獲取所有唯一的股票代碼
        cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data")
        all_stocks = [row[0] for row in cursor.fetchall()]
        print(f"📊 資料庫中的股票數量: {len(all_stocks)}")
        
        # 統計需要修復的股票
        cursor.execute("""
        SELECT DISTINCT stock_id 
        FROM stock_daily_data 
        WHERE stock_name IS NULL OR stock_name = ''
        OR listing_status IS NULL OR listing_status = ''
        OR industry IS NULL OR industry = ''
        """)
        stocks_to_fix = [row[0] for row in cursor.fetchall()]
        print(f"⚠️ 需要修復的股票: {len(stocks_to_fix)} 檔")
        
        if not stocks_to_fix:
            print("✅ 所有股票資訊都已完整，無需修復")
            conn.close()
            return True
        
        # 開始修復
        print(f"🔧 開始修復股票資訊...")
        fixed_count = 0
        not_found_count = 0
        
        for i, stock_id in enumerate(stocks_to_fix, 1):
            if i % 50 == 0:
                print(f"   進度: {i}/{len(stocks_to_fix)} ({i/len(stocks_to_fix)*100:.1f}%)")
            
            # 清理股票代碼（移除可能的名稱部分）
            clean_stock_id = str(stock_id).split()[0] if stock_id else ''
            
            # 從股票資訊映射中獲取資料
            stock_data = stock_info.get(clean_stock_id, {})
            
            if stock_data:
                stock_name = stock_data.get('stock_name', '')
                listing_status = stock_data.get('listing_status', '')
                industry = stock_data.get('industry', 'ETF')
                
                # 更新資料庫
                cursor.execute("""
                UPDATE stock_daily_data 
                SET stock_name = ?, listing_status = ?, industry = ?
                WHERE stock_id = ?
                AND (stock_name IS NULL OR stock_name = ''
                OR listing_status IS NULL OR listing_status = ''
                OR industry IS NULL OR industry = '')
                """, (stock_name, listing_status, industry, stock_id))
                
                if cursor.rowcount > 0:
                    fixed_count += 1
            else:
                # 對於找不到的股票，設置智能預設值
                not_found_count += 1
                
                # 判斷股票類型並設置更準確的預設值
                stock_id_str = str(clean_stock_id)
                
                if len(stock_id_str) == 6 and stock_id_str.startswith('00'):
                    # ETF (如 006201, 006203 等)
                    default_name = f'ETF{clean_stock_id}'
                    default_listing = '上市'
                    default_industry = 'ETF'
                elif stock_id_str.endswith(('K', 'L', 'R', 'P', 'Q', 'X', 'Y', 'Z')):
                    # 權證或衍生性商品
                    default_name = f'權證{clean_stock_id}'
                    default_listing = '權證'
                    default_industry = '權證'
                elif len(stock_id_str) == 4 and stock_id_str.startswith('0'):
                    # ETF (如 0050, 0056 等)
                    default_name = f'ETF{clean_stock_id}'
                    default_listing = '上市'
                    default_industry = 'ETF'
                elif len(stock_id_str) >= 5 and stock_id_str.isdigit():
                    # 可能是興櫃或其他特殊股票
                    default_name = f'股票{clean_stock_id}'
                    default_listing = '興櫃'
                    default_industry = '其他'
                else:
                    # 一般股票
                    default_name = f'股票{clean_stock_id}'
                    default_listing = '未分類'
                    default_industry = '未分類'
                
                cursor.execute("""
                UPDATE stock_daily_data 
                SET stock_name = ?, listing_status = ?, industry = ?
                WHERE stock_id = ?
                AND (stock_name IS NULL OR stock_name = ''
                OR listing_status IS NULL OR listing_status = ''
                OR industry IS NULL OR industry = '')
                """, (default_name, default_listing, default_industry, stock_id))
        
        # 提交變更
        conn.commit()
        
        print(f"\n✅ {db_name} 修復完成!")
        print(f"   成功修復: {fixed_count} 檔股票")
        print(f"   使用預設值: {not_found_count} 檔股票")
        print(f"   總計處理: {fixed_count + not_found_count} 檔股票")
        
        # 驗證修復結果
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) 
        FROM stock_daily_data 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        AND listing_status IS NOT NULL AND listing_status != ''
        AND industry IS NOT NULL AND industry != ''
        """)
        complete_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        total_stocks = cursor.fetchone()[0]
        
        complete_percentage = (complete_stocks / total_stocks) * 100 if total_stocks > 0 else 0
        
        print(f"📊 {db_name} 最終完整性: {complete_stocks}/{total_stocks} ({complete_percentage:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修復 {db_name} 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawl_price_with_stock_info():
    """測試 crawl_price 函數是否正確返回股票資訊"""
    
    print("\n🧪 測試 crawl_price 函數的股票資訊")
    print("=" * 60)
    
    try:
        import sys
        sys.path.insert(0, 'finlab')
        from crawler import crawl_price
        import datetime
        
        # 測試爬取最近的交易日
        test_date = datetime.date(2025, 7, 25)  # 使用已知有資料的日期
        print(f"🔍 測試日期: {test_date}")
        
        df = crawl_price(test_date)
        
        if df is None or df.empty:
            print("⚠️ 爬取結果為空，可能是週末或假日")
            return True
        
        print(f"✅ 成功爬取 {len(df)} 筆資料")
        
        # 檢查是否包含股票資訊欄位
        required_columns = ['stock_name', 'listing_status', 'industry']
        df_reset = df.reset_index()
        
        print(f"📊 資料欄位: {list(df_reset.columns)}")
        
        missing_columns = [col for col in required_columns if col not in df_reset.columns]
        if missing_columns:
            print(f"❌ 缺少必要欄位: {missing_columns}")
            return False
        
        # 檢查股票資訊完整性
        for col in required_columns:
            non_empty = df_reset[col].notna() & (df_reset[col] != '')
            complete_rate = non_empty.sum() / len(df_reset) * 100
            print(f"   {col}: {complete_rate:.1f}% 完整")
        
        # 顯示範例資料
        print(f"\n📋 範例資料:")
        sample_data = df_reset[['stock_id'] + required_columns].head(5)
        for _, row in sample_data.iterrows():
            print(f"   {row['stock_id']}: {row['stock_name']} | {row['listing_status']} | {row['industry']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🔧 newprice.db 和 price.db 完整修復工具")
    print("=" * 80)
    
    # 確認是否要執行修復
    response = input("\n是否要開始修復資料庫中缺少的股票資訊？(y/N): ")
    if response.lower() != 'y':
        print("❌ 取消修復")
        return
    
    success_count = 0
    
    # 修復 price.db
    price_db_path = "D:/Finlab/history/tables/price.db"
    if fix_database_stock_info(price_db_path, "price.db"):
        success_count += 1
    
    # 修復 newprice.db
    newprice_db_path = "D:/Finlab/history/tables/newprice.db"
    if fix_database_stock_info(newprice_db_path, "newprice.db"):
        success_count += 1
    
    # 測試 crawl_price 函數
    if test_crawl_price_with_stock_info():
        print("\n✅ crawl_price 函數測試通過")
    else:
        print("\n⚠️ crawl_price 函數測試失敗")
    
    print(f"\n📊 修復結果總結:")
    print(f"   成功修復: {success_count}/2 個資料庫")
    
    if success_count == 2:
        print(f"\n✅ 所有資料庫修復完成！")
        print(f"\n💡 建議:")
        print(f"   1. 重新啟動 GUI 程式以載入更新的股票資訊")
        print(f"   2. 測試策略交集分析是否顯示完整的股票名稱")
        print(f"   3. 未來的 price 爬蟲會自動包含完整的股票資訊")
        print(f"   4. 定期執行此修復工具以保持資料完整性")
    else:
        print(f"\n⚠️ 部分資料庫修復失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
