@echo off
chcp 65001 >nul
title 台股智能選股系統 - 便攜版啟動器

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo          便攜版 - 獨立運行
echo ========================================
echo.

REM 檢查主程式是否存在
if exist "台股智能選股系統.exe" (
    echo ✅ 系統檢查通過
    echo 🔄 正在啟動程式...
    echo.
    
    REM 創建必要的目錄
    if not exist "history" mkdir history
    if not exist "history\tables" mkdir history\tables
    if not exist "logs" mkdir logs
    if not exist "reports" mkdir reports
    
    echo 📁 目錄結構已準備完成
    echo 🚀 啟動主程式...
    echo.
    
    REM 啟動主程式
    start "" "台股智能選股系統.exe"
    
    echo ✅ 程式已成功啟動！
    echo.
    echo 💡 使用提示：
    echo    ✓ 首次運行會自動初始化資料庫
    echo    ✓ 請確保網路連接正常以獲取最新資料
    echo    ✓ 所有資料將保存在當前目錄下
    echo    ✓ 可隨時移動整個資料夾到其他位置
    echo.
    echo 📋 重要目錄：
    echo    • history\tables\ - 資料庫檔案
    echo    • logs\ - 程式日誌
    echo    • reports\ - 分析報告
    echo    • config\ - 配置檔案
    echo.
    
) else (
    echo ❌ 錯誤：找不到主程式檔案
    echo.
    echo 請確認檔案 "台股智能選股系統.exe" 存在於當前目錄
    echo.
    echo 如果檔案遺失，請重新下載完整的便攜版套件
    echo.
)

echo 🔚 按任意鍵關閉此視窗...
pause >nul
