#!/usr/bin/env python3
"""
測試TWSE即時資料擷取與5分鐘彙總系統
"""

import pandas as pd
import logging
import time
import os
from datetime import datetime, timedelta

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_twse_realtime_api():
    """測試TWSE即時API連接"""
    print("🧪 測試TWSE即時API連接...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        
        crawler = TWSERealtimeCrawler()
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454', '3008', '2412']
        
        print(f"\n📊 測試即時資料獲取:")
        realtime_data = crawler.get_stock_realtime_data(test_stocks)
        
        if realtime_data:
            print(f"✅ 成功獲取 {len(realtime_data)} 支股票的即時資料")
            
            for symbol, data in realtime_data.items():
                print(f"\n📈 {symbol}:")
                print(f"   成交價: {data['price']:.2f}")
                print(f"   成交量: {data['volume']:,}")
                print(f"   最高價: {data['high']:.2f}")
                print(f"   最低價: {data['low']:.2f}")
                print(f"   開盤價: {data['open']:.2f}")
                print(f"   最佳買價: {data['bid_price']:.2f}")
                print(f"   最佳賣價: {data['ask_price']:.2f}")
                print(f"   更新時間: {data['timestamp']}")
        else:
            print("❌ 無法獲取即時資料")
        
        return len(realtime_data) > 0
        
    except Exception as e:
        print(f"❌ TWSE即時API測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_storage():
    """測試CSV存儲功能"""
    print(f"\n🧪 測試CSV存儲功能...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        
        crawler = TWSERealtimeCrawler()
        
        # 獲取測試資料
        test_stocks = ['2330', '2317']
        realtime_data = crawler.get_stock_realtime_data(test_stocks)
        
        if realtime_data:
            # 保存到CSV
            crawler.save_realtime_to_csv(realtime_data)
            
            # 檢查文件是否創建
            today = datetime.now().strftime('%Y%m%d')
            csv_file = os.path.join(crawler.data_dir, f"realtime_{today}.csv")
            
            if os.path.exists(csv_file):
                print(f"✅ CSV文件創建成功: {csv_file}")
                
                # 讀取並顯示內容
                df = pd.read_csv(csv_file)
                print(f"   文件包含 {len(df)} 筆記錄")
                print(f"   欄位: {list(df.columns)}")
                
                if not df.empty:
                    print(f"   最新記錄:")
                    latest = df.iloc[-1]
                    print(f"     股票: {latest['symbol']}")
                    print(f"     價格: {latest['price']}")
                    print(f"     時間: {latest['timestamp']}")
                
                return True
            else:
                print(f"❌ CSV文件未創建")
                return False
        else:
            print("❌ 無即時資料可保存")
            return False
        
    except Exception as e:
        print(f"❌ CSV存儲測試失敗: {e}")
        return False

def test_database_operations():
    """測試資料庫操作"""
    print(f"\n🧪 測試資料庫操作...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        import sqlite3
        
        crawler = TWSERealtimeCrawler()
        
        # 測試資料庫連接
        conn = sqlite3.connect(crawler.db_path)
        cursor = conn.cursor()
        
        # 檢查表是否存在
        tables = ['realtime_ticks', 'kline_5min']
        
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 表 {table} 存在")
                
                # 檢查記錄數
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   記錄數: {count}")
            else:
                print(f"❌ 表 {table} 不存在")
        
        # 測試插入即時資料
        test_stocks = ['2330']
        realtime_data = crawler.get_stock_realtime_data(test_stocks)
        
        if realtime_data:
            crawler.save_realtime_to_db(realtime_data)
            
            # 檢查是否插入成功
            cursor.execute("SELECT COUNT(*) FROM realtime_ticks")
            new_count = cursor.fetchone()[0]
            print(f"✅ 即時資料插入成功，總記錄數: {new_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫操作測試失敗: {e}")
        return False

def test_5min_aggregation():
    """測試5分鐘彙總功能"""
    print(f"\n🧪 測試5分鐘彙總功能...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        
        crawler = TWSERealtimeCrawler()
        
        # 模擬一些歷史資料進行彙總測試
        test_symbol = '2330'
        
        # 設定測試時間範圍
        end_time = datetime.now().replace(second=0, microsecond=0)
        start_time = end_time - timedelta(minutes=5)
        
        print(f"📊 測試彙總時間範圍: {start_time} 到 {end_time}")
        
        # 嘗試彙總
        kline_data = crawler.aggregate_to_5min_kline(test_symbol, start_time, end_time)
        
        if kline_data:
            print(f"✅ 5分鐘K線彙總成功:")
            print(f"   股票: {kline_data['symbol']}")
            print(f"   時間: {kline_data['datetime']}")
            print(f"   開盤: {kline_data['open']:.2f}")
            print(f"   最高: {kline_data['high']:.2f}")
            print(f"   最低: {kline_data['low']:.2f}")
            print(f"   收盤: {kline_data['close']:.2f}")
            print(f"   成交量: {kline_data['volume']:,}")
            print(f"   Tick數: {kline_data['tick_count']}")
            
            # 保存到資料庫
            crawler.save_5min_kline_to_db(kline_data)
            
            return True
        else:
            print("⚠️ 無足夠資料進行彙總（這是正常的，因為可能沒有歷史tick資料）")
            return True  # 這不算失敗
        
    except Exception as e:
        print(f"❌ 5分鐘彙總測試失敗: {e}")
        return False

def test_realtime_monitoring():
    """測試即時監控功能"""
    print(f"\n🧪 測試即時監控功能（運行30秒）...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        
        crawler = TWSERealtimeCrawler()
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454']
        
        print(f"🚀 開始監控 {test_stocks}...")
        
        # 開始即時擷取
        crawler.start_realtime_crawling(test_stocks, interval=5)
        
        # 運行30秒
        print("⏰ 監控運行中，請等待30秒...")
        time.sleep(30)
        
        # 停止監控
        crawler.stop_realtime_crawling()
        
        # 檢查結果
        import sqlite3
        conn = sqlite3.connect(crawler.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM realtime_ticks")
        tick_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM kline_5min")
        kline_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ 監控測試完成:")
        print(f"   收集到 {tick_count} 筆即時資料")
        print(f"   生成了 {kline_count} 筆5分鐘K線")
        
        return True
        
    except Exception as e:
        print(f"❌ 即時監控測試失敗: {e}")
        return False

def test_data_retrieval():
    """測試資料檢索功能"""
    print(f"\n🧪 測試資料檢索功能...")
    
    try:
        from twse_realtime_crawler import TWSERealtimeCrawler
        
        crawler = TWSERealtimeCrawler()
        
        # 測試獲取5分鐘K線數據
        test_symbol = '2330'
        
        df = crawler.get_5min_kline_data(test_symbol)
        
        if not df.empty:
            print(f"✅ 成功檢索 {test_symbol} 的5分鐘K線數據:")
            print(f"   總共 {len(df)} 筆記錄")
            print(f"   時間範圍: {df['datetime'].min()} 到 {df['datetime'].max()}")
            print(f"   欄位: {list(df.columns)}")
            
            if len(df) > 0:
                print(f"   最新記錄:")
                latest = df.iloc[-1]
                print(f"     時間: {latest['datetime']}")
                print(f"     OHLC: {latest['open']:.2f}/{latest['high']:.2f}/{latest['low']:.2f}/{latest['close']:.2f}")
                print(f"     成交量: {latest['volume']:,}")
            
            return True
        else:
            print("⚠️ 暫無5分鐘K線數據（這是正常的，因為可能剛開始收集）")
            return True
        
    except Exception as e:
        print(f"❌ 資料檢索測試失敗: {e}")
        return False

def cleanup_test_files():
    """清理測試文件"""
    try:
        # 清理資料庫
        if os.path.exists("twse_realtime.db"):
            os.remove("twse_realtime.db")
            print("🗑️ 清理測試資料庫")
        
        # 清理資料目錄
        if os.path.exists("twse_data"):
            import shutil
            shutil.rmtree("twse_data")
            print("🗑️ 清理測試資料目錄")
    except:
        pass

if __name__ == "__main__":
    print("🚀 開始測試TWSE即時資料擷取與5分鐘彙總系統...")
    
    results = []
    
    # 執行各項測試
    results.append(("TWSE即時API連接", test_twse_realtime_api()))
    results.append(("CSV存儲功能", test_csv_storage()))
    results.append(("資料庫操作", test_database_operations()))
    results.append(("5分鐘彙總功能", test_5min_aggregation()))
    results.append(("即時監控功能", test_realtime_monitoring()))
    results.append(("資料檢索功能", test_data_retrieval()))
    
    # 總結結果
    print(f"\n🎉 測試完成！")
    print(f"\n📋 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎊 所有測試都通過！TWSE即時資料擷取系統運行正常！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n💡 TWSE即時資料擷取系統特色:")
    print(f"   ✅ 基於TWSE官方API")
    print(f"   ✅ 每5秒擷取即時資料")
    print(f"   ✅ 自動彙總為5分鐘K線")
    print(f"   ✅ 同時保存CSV和資料庫")
    print(f"   ✅ 多線程並行處理")
    print(f"   ✅ 完整的錯誤處理")
    
    print(f"\n🎯 實際應用建議:")
    print(f"   1. 在交易時間運行以獲得最佳效果")
    print(f"   2. 可調整擷取間隔（建議5-10秒）")
    print(f"   3. 定期備份資料庫和CSV文件")
    print(f"   4. 監控系統資源使用情況")
    print(f"   5. 可擴展支援更多股票代碼")
    
    # 詢問是否清理測試文件
    try:
        response = input(f"\n🗑️ 是否清理測試文件？(y/N): ").strip().lower()
        if response == 'y':
            cleanup_test_files()
    except:
        pass
