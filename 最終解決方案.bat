@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終解決方案

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         最終解決方案 - 多重啟動方式
echo ========================================
echo.

echo 🔍 正在檢查可用的啟動方式...
echo.

REM 方案1: 終極 Python 啟動器
if exist "ultimate_launcher.py" (
    echo ✅ 方案1: 終極 Python 啟動器 (推薦)
    echo 🔄 正在啟動...
    python ultimate_launcher.py
    
    if errorlevel 1 (
        echo ❌ 方案1 失敗，嘗試方案2...
        goto method2
    ) else (
        echo ✅ 方案1 成功啟動！
        goto success
    )
)

:method2
REM 方案2: 直接執行最終修復版
if exist "dist\台股智能選股系統_最終修復版.exe" (
    echo ✅ 方案2: 最終修復版可執行檔
    echo 🔄 正在啟動...
    cd /d "dist"
    start "" "台股智能選股系統_最終修復版.exe"
    echo ✅ 方案2 已啟動！
    goto success
)

REM 方案3: 執行修復版
if exist "dist\台股智能選股系統_修復版.exe" (
    echo ✅ 方案3: 修復版可執行檔
    echo 🔄 正在啟動...
    cd /d "dist"
    start "" "台股智能選股系統_修復版.exe"
    echo ✅ 方案3 已啟動！
    goto success
)

REM 方案4: 執行標準版
if exist "dist\台股智能選股系統.exe" (
    echo ✅ 方案4: 標準版可執行檔
    echo 🔄 正在啟動...
    cd /d "dist"
    start "" "台股智能選股系統.exe"
    echo ✅ 方案4 已啟動！
    goto success
)

REM 所有方案都失敗
echo ❌ 所有啟動方案都失敗！
echo.
echo 💡 故障排除建議：
echo    1. 檢查 Python 環境: python --version
echo    2. 檢查程式文件是否存在
echo    3. 嘗試重新編譯: python simple_compile.py
echo    4. 檢查系統權限
echo    5. 檢查防毒軟體設定
echo.
pause
exit /b 1

:success
echo.
echo 🎉 啟動成功！
echo.
echo 📋 程式功能：
echo    ✓ 多策略智能選股
echo    ✓ 實時K線圖表分析
echo    ✓ 開盤前市場監控
echo    ✓ 技術指標分析
echo    ✓ Excel報告導出
echo.
echo 💡 使用提示：
echo    - 如果程式沒有顯示，請檢查工作列
echo    - 首次啟動可能需要較長時間
echo    - 如有問題，請參考使用說明
echo.

timeout /t 10 >nul
echo 啟動器將在 10 秒後關閉...
