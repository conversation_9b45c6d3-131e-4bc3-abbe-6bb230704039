#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復 price.db 中缺少的股票資訊
使用動態股票名稱映射模組來更新缺少的股票名稱、上市狀態和產業資訊
"""

import os
import sqlite3
import pandas as pd
from stock_name_mapping import get_cached_stock_info

def fix_price_db_stock_info():
    """修復 price.db 中缺少的股票資訊"""
    
    print("=" * 80)
    print("🔧 修復 price.db 中缺少的股票資訊")
    print("=" * 80)
    
    # 檢查資料庫檔案
    db_path = "D:/Finlab/history/tables/price.db"
    if not os.path.exists(db_path):
        print(f"❌ 找不到資料庫檔案: {db_path}")
        return False
    
    print(f"✅ 找到資料庫: {db_path}")
    print(f"📊 檔案大小: {os.path.getsize(db_path) / (1024*1024):.1f} MB")
    
    try:
        # 獲取最新的股票資訊
        print("\n📡 獲取最新股票資訊...")
        stock_info = get_cached_stock_info()
        
        if not stock_info:
            print("❌ 無法獲取股票資訊")
            return False
        
        print(f"✅ 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 連接資料庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"📊 表格欄位: {columns}")
        
        # 獲取所有唯一的股票代碼
        cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data")
        all_stocks = [row[0] for row in cursor.fetchall()]
        print(f"📊 資料庫中的股票數量: {len(all_stocks)}")
        
        # 統計需要修復的股票
        cursor.execute("""
        SELECT DISTINCT stock_id 
        FROM stock_daily_data 
        WHERE stock_name IS NULL OR stock_name = ''
        OR listing_status IS NULL OR listing_status = ''
        OR industry IS NULL OR industry = ''
        """)
        stocks_to_fix = [row[0] for row in cursor.fetchall()]
        print(f"⚠️ 需要修復的股票: {len(stocks_to_fix)} 檔")
        
        if not stocks_to_fix:
            print("✅ 所有股票資訊都已完整，無需修復")
            conn.close()
            return True
        
        # 顯示前10個需要修復的股票
        print(f"🔍 需要修復的股票範例: {stocks_to_fix[:10]}")
        
        # 開始修復
        print(f"\n🔧 開始修復股票資訊...")
        fixed_count = 0
        not_found_count = 0
        
        for i, stock_id in enumerate(stocks_to_fix, 1):
            if i % 50 == 0:
                print(f"   進度: {i}/{len(stocks_to_fix)} ({i/len(stocks_to_fix)*100:.1f}%)")
            
            # 從股票資訊映射中獲取資料
            stock_data = stock_info.get(str(stock_id), {})
            
            if stock_data:
                stock_name = stock_data.get('stock_name', '')
                listing_status = stock_data.get('listing_status', '')
                industry = stock_data.get('industry', 'ETF')
                
                # 更新資料庫
                cursor.execute("""
                UPDATE stock_daily_data 
                SET stock_name = ?, listing_status = ?, industry = ?
                WHERE stock_id = ?
                AND (stock_name IS NULL OR stock_name = ''
                OR listing_status IS NULL OR listing_status = ''
                OR industry IS NULL OR industry = '')
                """, (stock_name, listing_status, industry, stock_id))
                
                if cursor.rowcount > 0:
                    fixed_count += 1
            else:
                # 對於找不到的股票，設置預設值
                not_found_count += 1
                
                # 判斷股票類型並設置更準確的預設值
                stock_id_str = str(stock_id)

                if len(stock_id_str) == 6 and stock_id_str.startswith('00'):
                    # ETF
                    default_name = f'ETF{stock_id}'
                    default_listing = '上市'
                    default_industry = 'ETF'
                elif stock_id_str.endswith(('K', 'L', 'R', 'P', 'Q', 'X', 'Y', 'Z')):
                    # 權證或衍生性商品
                    default_name = f'權證{stock_id}'
                    default_listing = '權證'
                    default_industry = '權證'
                elif len(stock_id_str) == 4 and stock_id_str.startswith('0'):
                    # ETF (如 0050, 0056 等)
                    default_name = f'ETF{stock_id}'
                    default_listing = '上市'
                    default_industry = 'ETF'
                elif len(stock_id_str) >= 5 and stock_id_str.isdigit():
                    # 可能是興櫃或其他特殊股票
                    default_name = f'股票{stock_id}'
                    default_listing = '興櫃'
                    default_industry = '其他'
                else:
                    # 一般股票
                    default_name = f'股票{stock_id}'
                    default_listing = '未分類'
                    default_industry = '未分類'
                
                cursor.execute("""
                UPDATE stock_daily_data 
                SET stock_name = ?, listing_status = ?, industry = ?
                WHERE stock_id = ?
                AND (stock_name IS NULL OR stock_name = ''
                OR listing_status IS NULL OR listing_status = ''
                OR industry IS NULL OR industry = '')
                """, (default_name, default_listing, default_industry, stock_id))
        
        # 提交變更
        conn.commit()
        
        print(f"\n✅ 修復完成!")
        print(f"   成功修復: {fixed_count} 檔股票")
        print(f"   使用預設值: {not_found_count} 檔股票")
        print(f"   總計處理: {fixed_count + not_found_count} 檔股票")
        
        # 驗證修復結果
        print(f"\n🔍 驗證修復結果...")
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) 
        FROM stock_daily_data 
        WHERE stock_name IS NULL OR stock_name = ''
        """)
        remaining_missing_names = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) 
        FROM stock_daily_data 
        WHERE listing_status IS NULL OR listing_status = ''
        """)
        remaining_missing_listing = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) 
        FROM stock_daily_data 
        WHERE industry IS NULL OR industry = ''
        """)
        remaining_missing_industry = cursor.fetchone()[0]
        
        print(f"📊 修復後統計:")
        print(f"   仍缺少股票名稱: {remaining_missing_names} 檔")
        print(f"   仍缺少上市狀態: {remaining_missing_listing} 檔")
        print(f"   仍缺少產業資訊: {remaining_missing_industry} 檔")
        
        # 檢查完整資訊的股票比例
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) 
        FROM stock_daily_data 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        AND listing_status IS NOT NULL AND listing_status != ''
        AND industry IS NOT NULL AND industry != ''
        """)
        complete_stocks = cursor.fetchone()[0]
        
        complete_percentage = (complete_stocks / total_stocks) * 100 if total_stocks > 0 else 0
        
        print(f"\n📊 最終完整性統計:")
        print(f"   完整資訊的股票: {complete_stocks} / {total_stocks} ({complete_percentage:.1f}%)")
        print(f"   改善幅度: +{fixed_count + not_found_count} 檔股票")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🔧 price.db 股票資訊修復工具")
    
    # 確認是否要執行修復
    response = input("\n是否要開始修復 price.db 中缺少的股票資訊？(y/N): ")
    if response.lower() != 'y':
        print("❌ 取消修復")
        return
    
    # 執行修復
    success = fix_price_db_stock_info()
    
    if success:
        print(f"\n✅ 修復完成！")
        print(f"\n💡 建議:")
        print(f"   1. 重新啟動 GUI 程式以載入更新的股票資訊")
        print(f"   2. 測試策略交集分析是否顯示完整的股票名稱")
        print(f"   3. 定期執行此修復工具以保持資料完整性")
    else:
        print(f"\n❌ 修復失敗")

if __name__ == "__main__":
    main()
