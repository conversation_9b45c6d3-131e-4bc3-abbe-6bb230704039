#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
廣告遮擋處理測試腳本
專門測試增強版月營收下載器的廣告處理能力
"""

import sys
import time
from datetime import datetime

def test_ad_blocking():
    """測試廣告遮擋處理功能"""
    try:
        print("=" * 60)
        print("🚫 測試廣告遮擋處理功能")
        print("=" * 60)
        
        # 設置日誌過濾器
        try:
            from selenium_log_filter import setup_clean_selenium_environment
            setup_clean_selenium_environment()
            print("✅ 日誌過濾器已啟用")
        except ImportError:
            print("⚠️ 日誌過濾器不可用")
        
        # 導入增強版下載器
        from enhanced_monthly_revenue_downloader import EnhancedMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = EnhancedMonthlyRevenueDownloader()
        print("✅ 增強版下載器初始化成功")
        
        # 測試瀏覽器啟動
        print("\n🔧 測試瀏覽器啟動...")
        driver = downloader.setup_enhanced_driver()
        
        if not driver:
            print("❌ 瀏覽器啟動失敗")
            return False
        
        print("✅ 瀏覽器啟動成功")
        
        try:
            # 訪問測試頁面
            test_url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
            print(f"\n📱 訪問測試頁面: {test_url}")
            driver.get(test_url)
            
            # 等待頁面載入
            print("⏳ 等待頁面載入...")
            time.sleep(5)
            
            # 測試廣告處理功能
            print("\n🚫 測試廣告處理功能...")
            downloader.handle_popups_and_alerts(driver)
            
            # 檢查是否還有廣告容器
            print("\n🔍 檢查廣告容器狀態...")
            ad_containers = [
                "ats-interstitial-container",
                "ats-overlay", 
                "ats-popup"
            ]
            
            remaining_ads = []
            for container_id in ad_containers:
                try:
                    element = driver.find_element_by_id(container_id)
                    if element.is_displayed():
                        remaining_ads.append(container_id)
                        print(f"⚠️ 廣告容器仍然存在: {container_id}")
                    else:
                        print(f"✅ 廣告容器已處理: {container_id}")
                except:
                    print(f"✅ 廣告容器不存在: {container_id}")
            
            # 測試查詢按鈕點擊
            print("\n🔄 測試查詢按鈕點擊...")
            query_success = downloader._click_query_button_enhanced(driver)
            if query_success:
                print("✅ 查詢按鈕點擊成功")
            else:
                print("❌ 查詢按鈕點擊失敗")
            
            # 等待一下
            time.sleep(3)
            
            # 測試XLS按鈕點擊
            print("\n📥 測試XLS按鈕點擊...")
            xls_success = downloader.find_and_click_xls_button_enhanced(driver)
            if xls_success:
                print("✅ XLS按鈕點擊成功")
            else:
                print("❌ XLS按鈕點擊失敗")
            
            # 總結測試結果
            print("\n" + "=" * 60)
            print("📊 廣告處理測試結果")
            print("=" * 60)
            
            if len(remaining_ads) == 0:
                print("✅ 所有廣告容器都已成功處理")
            else:
                print(f"⚠️ 仍有 {len(remaining_ads)} 個廣告容器未處理: {remaining_ads}")
            
            if query_success and xls_success:
                print("✅ 按鈕點擊功能正常")
                return True
            else:
                print("❌ 按鈕點擊功能異常")
                return False
                
        finally:
            # 關閉瀏覽器
            driver.quit()
            print("🔧 瀏覽器已關閉")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_real_download():
    """測試實際下載功能"""
    try:
        print("\n" + "=" * 60)
        print("📥 測試實際下載功能")
        print("=" * 60)
        
        from enhanced_monthly_revenue_downloader import EnhancedMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = EnhancedMonthlyRevenueDownloader()
        
        # 測試下載（使用較小的日期範圍）
        print("🚀 開始測試下載 2330 月營收資料...")
        print("📅 日期範圍: 2024-01 到 2024-03 (測試用)")
        
        result = downloader.download_stock_revenue_enhanced(
            stock_id="2330",
            start_date="2024-01", 
            end_date="2024-03"
        )
        
        if result:
            print(f"✅ 下載測試成功，獲得 {result} 筆資料")
            return True
        else:
            print("❌ 下載測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 下載測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 廣告遮擋處理測試套件")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 執行廣告處理測試
    print("\n🚫 執行廣告處理測試...")
    ad_blocking_result = test_ad_blocking()
    results.append(("廣告處理功能", ad_blocking_result))
    
    # 詢問是否執行實際下載測試
    print("\n" + "=" * 60)
    user_input = input("是否執行實際下載測試？(y/N): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        print("🚀 執行實際下載測試...")
        download_result = test_real_download()
        results.append(("實際下載功能", download_result))
    else:
        print("⏭️ 跳過實際下載測試")
    
    # 顯示測試結果
    print("\n" + "=" * 60)
    print("📊 測試結果總覽")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 測試統計: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！廣告處理功能正常")
        print("\n💡 修復重點:")
        print("• 增強版廣告容器處理")
        print("• 多重按鈕點擊策略")
        print("• JavaScript清理功能")
        print("• 滾動到元素並點擊")
    else:
        print("⚠️ 部分測試失敗，可能需要進一步調整")
        print("\n🔧 建議檢查:")
        print("• 網站結構是否變更")
        print("• 廣告容器ID是否更新")
        print("• 按鈕定位方法是否有效")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    print(f"\n📋 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("✅ 廣告遮擋處理功能已修復，可以正常使用")
    else:
        print("❌ 仍有問題需要解決")
    
    sys.exit(0 if success else 1)
