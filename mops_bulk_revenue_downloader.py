#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公開資訊觀測站批量月營收下載器
整合到統一月營收下載器中
"""

import requests
import sqlite3
import pandas as pd
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
import time
import logging
import urllib3
import os
import os

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class MopsBulkRevenueDownloader:
    """公開資訊觀測站批量月營收下載器"""
    
    def __init__(self, db_path="D:/Finlab/history/tables/monthly_revenue.db"):
        self.db_path = db_path
        self.setup_logging()
        self.init_database()
        
        # 市場代碼對應
        self.markets = {
            'sii': '上市',
            'otc': '上櫃', 
            'rotc': '興櫃',
            'pub': '公開發行'
        }
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('mops_bulk_downloader.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('MopsBulkDownloader')

    def parse_percentage(self, percentage_str):
        """解析百分比字串"""
        if not percentage_str or percentage_str.strip() == '-':
            return None

        try:
            # 移除百分號和空格
            clean_str = percentage_str.replace('%', '').replace(',', '').strip()
            return float(clean_str)
        except:
            return None

    def init_database(self):
        """初始化數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_revenue'")
            table_exists = cursor.fetchone()

            if not table_exists:
                # 創建完整的月營收表格
                cursor.execute('''
                    CREATE TABLE monthly_revenue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_id TEXT NOT NULL,
                        stock_name TEXT,
                        industry TEXT,
                        year INTEGER NOT NULL,
                        month INTEGER NOT NULL,
                        revenue REAL,
                        revenue_mom REAL,
                        revenue_yoy REAL,
                        cumulative_revenue REAL,
                        cumulative_revenue_yoy REAL,
                        remark TEXT,
                        market TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_id, year, month)
                    )
                ''')
            else:
                # 檢查並添加缺少的欄位
                cursor.execute("PRAGMA table_info(monthly_revenue)")
                columns = [column[1] for column in cursor.fetchall()]

                # 需要添加的新欄位（注意：ALTER TABLE ADD COLUMN 不支援非常數預設值）
                new_columns = {
                    'industry': 'TEXT',
                    'revenue_mom': 'REAL',
                    'revenue_yoy': 'REAL',
                    'cumulative_revenue': 'REAL',
                    'cumulative_revenue_yoy': 'REAL',
                    'remark': 'TEXT',
                    'market': 'TEXT',
                    'updated_at': 'TIMESTAMP'  # 移除 DEFAULT，在插入時手動設定
                }

                for column_name, column_type in new_columns.items():
                    if column_name not in columns:
                        cursor.execute(f'ALTER TABLE monthly_revenue ADD COLUMN {column_name} {column_type}')
                        self.logger.info(f"✅ 已添加 {column_name} 欄位到現有表格")
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON monthly_revenue(stock_id, year, month)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON monthly_revenue(year, month)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
            
    def download_market_revenue(self, market, year, month, company_type=0):
        """下載指定市場的月營收數據"""
        
        # 轉換為民國年
        roc_year = year - 1911
        
        # 構建URL
        url = f"https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{roc_year}_{month}_{company_type}.html"
        
        self.logger.info(f"📡 下載 {self.markets.get(market, market)} {year}年{month}月 數據...")
        self.logger.info(f"🔗 URL: {url}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, verify=False, timeout=30)
            response.encoding = 'big5'
            
            if response.status_code == 200:
                self.logger.info("✅ 網頁下載成功")
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                tables = soup.find_all('table')
                
                revenue_data = []
                
                for table in tables:
                    rows = table.find_all('tr')
                    
                    if len(rows) > 10:  # 營收表格應該有很多行
                        for row in rows:
                            cells = row.find_all(['td', 'th'])
                            
                            if len(cells) >= 3:
                                cell_texts = [cell.get_text(strip=True) for cell in cells]
                                
                                # 檢查是否為數據行（第一欄是股票代號）
                                if len(cell_texts) > 0 and re.match(r'^\d{4}$', cell_texts[0]):
                                    try:
                                        # 根據實際HTML結構解析完整的資料欄位
                                        # [0]股票代號 [1]股票名稱 [2]上月營收 [3]當月營收 [4]去年同月營收
                                        # [5]月增率 [6]年增率 [7]累計營收 [8]累計去年營收 [9]累計年增率 [10]備註

                                        stock_id = cell_texts[0] if len(cell_texts) > 0 else ""
                                        stock_name = cell_texts[1] if len(cell_texts) > 1 else ""

                                        # 營業收入（當月）- 第3欄
                                        revenue_str = cell_texts[3] if len(cell_texts) > 3 else "0"
                                        revenue_clean = re.sub(r'[,\s]', '', revenue_str)
                                        revenue = int(revenue_clean) if revenue_clean.isdigit() else 0

                                        # 營業收入較上月增減(%) - 第5欄
                                        revenue_mom_str = cell_texts[5] if len(cell_texts) > 5 else ""
                                        revenue_mom = self.parse_percentage(revenue_mom_str)

                                        # 營業收入較去年同月增減(%) - 第6欄
                                        revenue_yoy_str = cell_texts[6] if len(cell_texts) > 6 else ""
                                        revenue_yoy = self.parse_percentage(revenue_yoy_str)

                                        # 累計營業收入 - 第7欄
                                        cumulative_revenue_str = cell_texts[7] if len(cell_texts) > 7 else "0"
                                        cumulative_revenue_clean = re.sub(r'[,\s]', '', cumulative_revenue_str)
                                        cumulative_revenue = int(cumulative_revenue_clean) if cumulative_revenue_clean.isdigit() else 0

                                        # 累計營業收入較去年同期增減(%) - 第9欄
                                        cumulative_revenue_yoy_str = cell_texts[9] if len(cell_texts) > 9 else ""
                                        cumulative_revenue_yoy = self.parse_percentage(cumulative_revenue_yoy_str)

                                        # 備註 - 第10欄
                                        remark = cell_texts[10] if len(cell_texts) > 10 else ""

                                        if revenue > 0:
                                            revenue_data.append({
                                                'stock_id': stock_id,
                                                'stock_name': stock_name,
                                                'industry': '',  # 此格式沒有產業別資訊
                                                'year': year,
                                                'month': month,
                                                'revenue': revenue,
                                                'revenue_mom': revenue_mom,
                                                'revenue_yoy': revenue_yoy,
                                                'cumulative_revenue': cumulative_revenue,
                                                'cumulative_revenue_yoy': cumulative_revenue_yoy,
                                                'remark': remark,
                                                'market': self.markets.get(market, market)
                                            })
                                            
                                    except (ValueError, IndexError):
                                        continue
                
                self.logger.info(f"✅ 解析完成！找到 {len(revenue_data)} 筆營收數據")
                return revenue_data
                
            else:
                self.logger.error(f"❌ 網頁下載失敗，狀態碼: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ 下載過程中發生錯誤: {e}")
            return []
            
    def save_to_database(self, revenue_data):
        """保存數據到數據庫（兼容現有結構）"""
        if not revenue_data:
            return 0

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 檢查表格結構，確定是否有 updated_at 欄位
            cursor.execute("PRAGMA table_info(monthly_revenue)")
            columns = [column[1] for column in cursor.fetchall()]
            has_updated_at = 'updated_at' in columns

            saved_count = 0
            updated_count = 0

            for data in revenue_data:
                # 檢查是否已存在
                cursor.execute('''
                    SELECT id FROM monthly_revenue
                    WHERE stock_id = ? AND year = ? AND month = ?
                ''', (data['stock_id'], data['year'], data['month']))

                existing = cursor.fetchone()

                # 根據是否有 updated_at 欄位使用不同的 SQL
                if has_updated_at:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue
                        (stock_id, stock_name, industry, year, month, revenue,
                         revenue_mom, revenue_yoy, cumulative_revenue, cumulative_revenue_yoy,
                         remark, market, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        data['stock_id'],
                        data['stock_name'],
                        data.get('industry', ''),
                        data['year'],
                        data['month'],
                        data['revenue'],
                        data.get('revenue_mom'),
                        data.get('revenue_yoy'),
                        data.get('cumulative_revenue'),
                        data.get('cumulative_revenue_yoy'),
                        data.get('remark', ''),
                        data.get('market', '')
                    ))
                else:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue
                        (stock_id, stock_name, industry, year, month, revenue,
                         revenue_mom, revenue_yoy, cumulative_revenue, cumulative_revenue_yoy,
                         remark, market)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data['stock_id'],
                        data['stock_name'],
                        data.get('industry', ''),
                        data['year'],
                        data['month'],
                        data['revenue'],
                        data.get('revenue_mom'),
                        data.get('revenue_yoy'),
                        data.get('cumulative_revenue'),
                        data.get('cumulative_revenue_yoy'),
                        data.get('remark', ''),
                        data.get('market', '')
                    ))

                if existing:
                    updated_count += 1
                else:
                    saved_count += 1

            conn.commit()
            conn.close()

            self.logger.info(f"💾 數據保存完成：新增 {saved_count} 筆，更新 {updated_count} 筆")
            return saved_count + updated_count

        except Exception as e:
            self.logger.error(f"❌ 數據保存失敗: {e}")
            return 0

    def export_to_files(self, revenue_data, year, month):
        """將月營收數據匯出為Excel和CSV檔案"""
        if not revenue_data:
            return

        try:
            # 確保輸出目錄存在
            output_dir = "D:/Finlab/history/tables"
            os.makedirs(output_dir, exist_ok=True)

            # 準備DataFrame
            df = pd.DataFrame(revenue_data)

            # 重新排列欄位順序，讓資料更易讀
            column_order = ['stock_id', 'stock_name', 'industry', 'market', 'revenue',
                           'revenue_mom', 'revenue_yoy', 'cumulative_revenue',
                           'cumulative_revenue_yoy', 'remark', 'year', 'month']
            df = df.reindex(columns=column_order)

            # 排序：先按市場，再按股票代號
            market_order = {'上市': 1, '上櫃': 2, '興櫃': 3, '公開發行': 4}
            df['market_sort'] = df['market'].map(market_order).fillna(5)
            df = df.sort_values(['market_sort', 'stock_id']).drop('market_sort', axis=1)

            # 格式化數字欄位（加上千分位逗號）
            df['revenue_formatted'] = df['revenue'].apply(lambda x: f"{x:,}" if pd.notnull(x) else "")
            df['cumulative_revenue_formatted'] = df['cumulative_revenue'].apply(lambda x: f"{x:,}" if pd.notnull(x) else "")

            # 檔案名稱
            file_prefix = f"monthly_revenue_{year}_{month:02d}"

            # 匯出Excel檔案
            excel_file = os.path.join(output_dir, f"{file_prefix}.xlsx")

            # 創建Excel writer，設定格式
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # 主要資料表（包含完整欄位）
                export_df = df[['stock_id', 'stock_name', 'industry', 'market', 'revenue_formatted',
                               'revenue_mom', 'revenue_yoy', 'cumulative_revenue_formatted',
                               'cumulative_revenue_yoy', 'remark']].copy()
                export_df.columns = ['股票代號', '股票名稱', '產業別', '市場別', '營收(千元)',
                                   '營收月增率(%)', '營收年增率(%)', '累計營收(千元)',
                                   '累計營收年增率(%)', '備註']
                export_df.to_excel(writer, sheet_name='月營收資料', index=False)

                # 統計資料表
                stats_data = []
                for market in df['market'].unique():
                    market_df = df[df['market'] == market]
                    stats_data.append({
                        '市場別': market,
                        '公司數量': len(market_df),
                        '總營收(千元)': f"{market_df['revenue'].sum():,}",
                        '平均營收(千元)': f"{market_df['revenue'].mean():,.0f}",
                        '最高營收(千元)': f"{market_df['revenue'].max():,}",
                        '最低營收(千元)': f"{market_df['revenue'].min():,}"
                    })

                # 總計
                stats_data.append({
                    '市場別': '總計',
                    '公司數量': len(df),
                    '總營收(千元)': f"{df['revenue'].sum():,}",
                    '平均營收(千元)': f"{df['revenue'].mean():,.0f}",
                    '最高營收(千元)': f"{df['revenue'].max():,}",
                    '最低營收(千元)': f"{df['revenue'].min():,}"
                })

                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='統計資料', index=False)

                # 格式化Excel
                workbook = writer.book

                # 設定主要資料表格式
                worksheet = writer.sheets['月營收資料']
                worksheet.column_dimensions['A'].width = 12  # 股票代號
                worksheet.column_dimensions['B'].width = 20  # 股票名稱
                worksheet.column_dimensions['C'].width = 15  # 產業別
                worksheet.column_dimensions['D'].width = 10  # 市場別
                worksheet.column_dimensions['E'].width = 15  # 營收
                worksheet.column_dimensions['F'].width = 12  # 營收月增率
                worksheet.column_dimensions['G'].width = 12  # 營收年增率
                worksheet.column_dimensions['H'].width = 15  # 累計營收
                worksheet.column_dimensions['I'].width = 15  # 累計營收年增率
                worksheet.column_dimensions['J'].width = 20  # 備註

                # 設定統計資料表格式
                stats_worksheet = writer.sheets['統計資料']
                stats_worksheet.column_dimensions['A'].width = 12
                stats_worksheet.column_dimensions['B'].width = 12
                for col in ['C', 'D', 'E', 'F']:
                    stats_worksheet.column_dimensions[col].width = 18

            # 匯出CSV檔案（包含完整欄位）
            csv_file = os.path.join(output_dir, f"{file_prefix}.csv")
            export_df = df[['stock_id', 'stock_name', 'industry', 'market', 'revenue',
                           'revenue_mom', 'revenue_yoy', 'cumulative_revenue',
                           'cumulative_revenue_yoy', 'remark']].copy()
            export_df.columns = ['股票代號', '股票名稱', '產業別', '市場別', '營收(千元)',
                               '營收月增率(%)', '營收年增率(%)', '累計營收(千元)',
                               '累計營收年增率(%)', '備註']
            export_df.to_csv(csv_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"📊 Excel檔案已匯出: {excel_file}")
            self.logger.info(f"📊 CSV檔案已匯出: {csv_file}")

            # 顯示統計資訊
            total_companies = len(df)
            total_revenue = df['revenue'].sum()
            self.logger.info(f"📈 {year}年{month}月統計: {total_companies} 家公司，總營收 {total_revenue:,} 千元")

            for market in df['market'].unique():
                market_count = len(df[df['market'] == market])
                self.logger.info(f"  {market}: {market_count} 家公司")

        except Exception as e:
            self.logger.error(f"❌ 檔案匯出失敗: {e}")
            
    def download_all_markets(self, year, month):
        """下載所有市場的月營收數據"""
        
        self.logger.info(f"🚀 開始下載 {year}年{month}月 全市場月營收數據...")
        
        total_count = 0
        results = {}
        
        for market_code, market_name in self.markets.items():
            # 只下載上市和上櫃，跳過興櫃和公開發行公司
            if market_code in ['rotc', 'pub']:
                continue
                
            self.logger.info(f"📊 正在下載 {market_name} 市場...")
            
            # 下載數據
            revenue_data = self.download_market_revenue(market_code, year, month)
            
            if revenue_data:
                # 保存到數據庫
                saved_count = self.save_to_database(revenue_data)
                total_count += saved_count
                results[market_name] = len(revenue_data)

                self.logger.info(f"✅ {market_name} 完成：{len(revenue_data)} 家公司")
            else:
                results[market_name] = 0
                self.logger.warning(f"⚠️ {market_name} 無數據")
            
            # 避免請求過於頻繁
            time.sleep(2)
        
        self.logger.info(f"🎉 全市場下載完成！總計處理 {total_count} 筆數據")

        # 顯示統計
        for market_name, count in results.items():
            self.logger.info(f"  {market_name}: {count} 家公司")

        # 匯出Excel和CSV檔案
        if total_count > 0:
            self.logger.info(f"📊 正在匯出 {year}年{month}月 Excel和CSV檔案...")

            # 從資料庫讀取數據用於匯出（避免重複下載）
            try:
                conn = sqlite3.connect(self.db_path)
                query = """
                    SELECT stock_id, stock_name, industry, year, month, revenue,
                           revenue_mom, revenue_yoy, cumulative_revenue,
                           cumulative_revenue_yoy, remark,
                           COALESCE(market, '未知') as market
                    FROM monthly_revenue
                    WHERE year = ? AND month = ?
                    ORDER BY market, stock_id
                """
                df = pd.read_sql_query(query, conn, params=(year, month))
                conn.close()

                if not df.empty:
                    # 轉換為字典格式
                    all_revenue_data = df.to_dict('records')
                    self.export_to_files(all_revenue_data, year, month)
                else:
                    self.logger.warning("⚠️ 資料庫中沒有找到數據用於匯出")

            except Exception as e:
                self.logger.error(f"❌ 從資料庫讀取數據失敗: {e}")

        return results
        
    def download_date_range(self, start_year, start_month, end_year, end_month):
        """下載指定日期範圍的月營收數據"""
        
        self.logger.info(f"📅 開始批量下載：{start_year}/{start_month:02d} ~ {end_year}/{end_month:02d}")
        
        current_year = start_year
        current_month = start_month
        
        total_results = {}
        
        while (current_year < end_year) or (current_year == end_year and current_month <= end_month):
            
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"📅 下載 {current_year}年{current_month}月 數據")
            self.logger.info(f"{'='*60}")
            
            # 下載當月所有市場數據
            month_results = self.download_all_markets(current_year, current_month)
            total_results[f"{current_year}-{current_month:02d}"] = month_results
            
            # 移動到下個月
            current_month += 1
            if current_month > 12:
                current_month = 1
                current_year += 1
                
            # 避免請求過於頻繁
            time.sleep(3)
        
        self.logger.info(f"\n🎉 批量下載完成！")
        return total_results

if __name__ == "__main__":
    # 測試批量下載器
    downloader = MopsBulkRevenueDownloader("test_mops_bulk_revenue.db")
    
    # 測試下載2024年6月數據
    results = downloader.download_all_markets(2024, 6)
    
    print("\n📊 下載結果:")
    for market, count in results.items():
        print(f"  {market}: {count} 家公司")
