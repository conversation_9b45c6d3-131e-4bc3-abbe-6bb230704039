#!/usr/bin/env python3
"""
第二階段重構測試
測試新創建的模組是否正常工作
"""
import sys
import os

def test_core_module():
    """測試核心模組"""
    try:
        from core import setup_logging, performance_monitor, safe_execute
        print("✅ 核心模組導入成功")

        # 測試日誌設置
        setup_logging()
        print("✅ 日誌系統設置成功")

        # 測試裝飾器
        @performance_monitor
        def test_func():
            return "test"

        result = test_func()
        if result == "test":
            print("✅ 性能監控裝飾器正常")

        return True
    except Exception as e:
        print(f"❌ 核心模組測試失敗: {e}")
        return False

def test_config_module():
    """測試配置模組"""
    try:
        from config import ConfigManager
        from config.config_manager import config_manager

        print("✅ 配置模組導入成功")

        # 測試配置管理器
        config = ConfigManager()

        # 測試設置和獲取
        config.set("test.value", "hello")
        value = config.get("test.value")

        if value == "hello":
            print("✅ 配置管理器功能正常")

        # 測試全局實例
        global_value = config_manager.get("ui.theme", "default")
        print(f"✅ 全局配置管理器正常，主題: {global_value}")

        return True
    except Exception as e:
        print(f"❌ 配置模組測試失敗: {e}")
        return False

def test_data_module():
    """測試數據模組"""
    try:
        from data import DataFetcher, data_fetcher

        print("✅ 數據模組導入成功")

        # 測試數據獲取器實例化
        fetcher = DataFetcher()
        print("✅ 數據獲取器實例化成功")

        # 測試全局實例
        if hasattr(data_fetcher, 'fetch_stock_data'):
            print("✅ 全局數據獲取器正常")

        return True
    except Exception as e:
        print(f"❌ 數據模組測試失敗: {e}")
        return False

def test_original_program():
    """測試原程式是否仍然正常"""
    try:
        import O3mh_gui_v21_optimized
        print("✅ 原程式模組導入成功")

        # 測試策略類別
        from O3mh_gui_v21_optimized import TripleRSIStrategy
        strategy = TripleRSIStrategy()

        if hasattr(strategy, 'analyze_stock'):
            print("✅ 原程式策略功能正常")

        return True
    except Exception as e:
        print(f"❌ 原程式測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始第二階段重構測試...")
    print("=" * 60)

    tests = [
        ("核心模組測試", test_core_module),
        ("配置模組測試", test_config_module),
        ("數據模組測試", test_data_module),
        ("原程式兼容性測試", test_original_program)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 執行 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")

    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed}/{total} 通過")

    if passed == total:
        print("🎉 第二階段重構測試全部通過！")
        print("✅ 新模組創建成功")
        print("✅ 原程式功能保持完整")
        return True
    else:
        print("❌ 部分測試失敗，需要檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)