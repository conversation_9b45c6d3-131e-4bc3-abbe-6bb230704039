#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將 otc_divide_ratio.pkl 轉換為 DB 格式
並保留 2018-01-01 以後的資料
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime

def convert_and_filter_otc_divide_ratio():
    """轉換 PKL 檔案為 DB 格式並過濾資料"""
    
    pkl_file = 'D:/Finlab/history/tables/otc_divide_ratio.pkl'
    db_file = 'D:/Finlab/history/tables/otc_divide_ratio.db'
    backup_db_file = 'D:/Finlab/history/tables/otc_divide_ratio_backup.db'
    
    print("🔄 開始轉換 otc_divide_ratio.pkl 到 DB 格式")
    print("🎯 同時保留 2018-01-01 以後的資料")
    print("=" * 60)
    
    # 檢查 PKL 檔案是否存在
    if not os.path.exists(pkl_file):
        print(f"❌ PKL 檔案不存在: {pkl_file}")
        return False
    
    try:
        # 讀取 PKL 檔案
        print(f"📂 讀取 PKL 檔案: {pkl_file}")
        df = pd.read_pickle(pkl_file)
        print(f"✅ 成功讀取 PKL 檔案")
        print(f"   資料筆數: {len(df):,}")
        print(f"   檔案大小: {os.path.getsize(pkl_file) / 1024 / 1024:.1f} MB")
        
        # 檢查資料結構
        print(f"\n📊 資料結構分析:")
        print(f"   索引類型: {type(df.index)}")
        print(f"   索引名稱: {df.index.names}")
        print(f"   欄位數量: {len(df.columns)}")
        print(f"   欄位名稱: {list(df.columns)}")
        
        # 檢查日期範圍
        if hasattr(df.index, 'get_level_values') and 'date' in df.index.names:
            dates = df.index.get_level_values('date')
            min_date = dates.min()
            max_date = dates.max()
            print(f"   原始日期範圍: {min_date.strftime('%Y-%m-%d')} ~ {max_date.strftime('%Y-%m-%d')}")
            
            # 檢查股票數量
            if 'stock_id' in df.index.names:
                stocks = df.index.get_level_values('stock_id').unique()
                print(f"   股票數量: {len(stocks):,}")
        
        # 顯示前幾筆資料
        print(f"\n📝 前3筆資料:")
        print(df.head(3).to_string())
        
        # 備份現有的 DB 檔案（如果存在）
        if os.path.exists(db_file):
            print(f"\n💾 備份現有 DB 檔案到: {backup_db_file}")
            import shutil
            shutil.copy2(db_file, backup_db_file)
        
        # 轉換為 DB 格式
        print(f"\n🔄 轉換為 DB 格式...")
        
        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()
        
        # 清理欄位名稱，確保與爬蟲函數一致
        print(f"🔧 清理欄位名稱...")
        
        # 檢查並顯示原始欄位名稱
        original_columns = list(df_to_save.columns)
        print(f"   原始欄位數: {len(original_columns)}")
        
        # 精確的欄位映射（移除空格）
        column_mapping = {}
        for col in original_columns:
            if '最近一次申報資料 季別/日期' in col:
                column_mapping[col] = '最近一次申報資料季別/日期'
            elif '最近一次申報每股 (單位)淨值' in col:
                column_mapping[col] = '最近一次申報每股(單位)淨值'
            elif '最近一次申報每股 (單位)盈餘' in col:
                column_mapping[col] = '最近一次申報每股(單位)盈餘'
        
        print(f"   發現需要重命名的欄位: {len(column_mapping)} 個")
        for old_name, new_name in column_mapping.items():
            print(f"      '{old_name}' -> '{new_name}'")
        
        # 重命名欄位
        if column_mapping:
            df_to_save = df_to_save.rename(columns=column_mapping)
            print(f"✅ 欄位名稱已標準化")
        else:
            print(f"ℹ️ 無需重命名欄位")
        
        final_columns = list(df_to_save.columns)
        print(f"   最終欄位數: {len(final_columns)}")
        
        # 檢查是否還有重複欄位
        duplicate_columns = [col for col in final_columns if final_columns.count(col) > 1]
        if duplicate_columns:
            print(f"⚠️ 發現重複欄位: {set(duplicate_columns)}")
        else:
            print(f"✅ 無重複欄位")
        
        # 過濾資料：保留 2018-01-01 以後的資料
        print(f"\n🔍 過濾資料：保留 2018-01-01 以後的資料...")
        
        # 確保日期欄位是 datetime 格式
        if 'date' in df_to_save.columns:
            df_to_save['date'] = pd.to_datetime(df_to_save['date'])
            
            # 過濾資料
            cutoff_date = pd.Timestamp('2018-01-01')
            df_filtered = df_to_save[df_to_save['date'] >= cutoff_date].copy()
            
            print(f"   過濾前: {len(df_to_save):,} 筆")
            print(f"   過濾後: {len(df_filtered):,} 筆")
            print(f"   移除筆數: {len(df_to_save) - len(df_filtered):,} 筆")
            
            if len(df_filtered) > 0:
                filtered_min_date = df_filtered['date'].min()
                filtered_max_date = df_filtered['date'].max()
                print(f"   過濾後日期範圍: {filtered_min_date.strftime('%Y-%m-%d')} ~ {filtered_max_date.strftime('%Y-%m-%d')}")
                
                # 檢查股票數量
                if 'stock_id' in df_filtered.columns:
                    filtered_stocks = df_filtered['stock_id'].nunique()
                    print(f"   過濾後股票數: {filtered_stocks:,} 檔")
                
                # 顯示按年份的資料分布
                print(f"   按年份分布:")
                year_counts = df_filtered.groupby(df_filtered['date'].dt.year).size()
                for year, count in year_counts.items():
                    print(f"      {year}: {count:,} 筆")
            else:
                print(f"⚠️ 過濾後沒有資料")
                return False
        else:
            print(f"⚠️ 找不到 'date' 欄位，無法過濾")
            df_filtered = df_to_save
        
        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)
        
        # 連接到資料庫
        conn = sqlite3.connect(db_file)
        
        # 保存資料
        df_filtered.to_sql('otc_divide_ratio', conn, if_exists='replace', index=False)
        
        # 創建索引以提高查詢性能
        cursor = conn.cursor()
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_id ON otc_divide_ratio(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON otc_divide_ratio(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_date ON otc_divide_ratio(stock_id, date)')
            print("✅ 已創建資料庫索引")
        except Exception as e:
            print(f"⚠️ 創建索引時出現警告: {e}")
        
        conn.commit()
        
        # 獲取統計信息
        cursor.execute('SELECT COUNT(*) FROM otc_divide_ratio')
        record_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM otc_divide_ratio')
        unique_stocks = cursor.fetchone()[0]
        
        cursor.execute('SELECT MIN(date), MAX(date) FROM otc_divide_ratio WHERE date IS NOT NULL')
        date_range_result = cursor.fetchone()
        min_date, max_date = date_range_result if date_range_result and date_range_result[0] else (None, None)
        
        conn.close()
        
        # 計算文件大小
        db_size = os.path.getsize(db_file) / 1024 / 1024  # MB
        pkl_size = os.path.getsize(pkl_file) / 1024 / 1024  # MB
        
        print(f"\n✅ 轉換完成！")
        print(f"📊 DB 檔案統計:")
        print(f"   檔案位置: {db_file}")
        print(f"   記錄數: {record_count:,} 筆")
        print(f"   股票數: {unique_stocks:,} 檔")
        print(f"   日期範圍: {min_date} ~ {max_date}")
        print(f"   DB 檔案大小: {db_size:.1f} MB")
        print(f"   PKL 檔案大小: {pkl_size:.1f} MB")
        print(f"   壓縮比: {pkl_size/db_size:.1f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ 轉換失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_conversion():
    """驗證轉換結果"""
    
    db_file = 'D:/Finlab/history/tables/otc_divide_ratio.db'
    
    print(f"\n🔍 驗證轉換結果...")
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查是否有2018年之前的資料
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM otc_divide_ratio WHERE date < '2018-01-01'")
        before_2018_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM otc_divide_ratio WHERE date >= '2018-01-01'")
        after_2018_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM otc_divide_ratio")
        min_date, max_date = cursor.fetchone()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(otc_divide_ratio);")
        columns_info = cursor.fetchall()
        
        conn.close()
        
        print(f"📊 驗證結果:")
        print(f"   2018年之前的資料: {before_2018_count:,} 筆")
        print(f"   2018年之後的資料: {after_2018_count:,} 筆")
        print(f"   最早日期: {min_date}")
        print(f"   最晚日期: {max_date}")
        print(f"   欄位數量: {len(columns_info)}")
        
        if before_2018_count == 0 and after_2018_count > 0:
            print(f"✅ 驗證通過：成功轉換並過濾資料")
            return True
        else:
            print(f"❌ 驗證失敗")
            return False
            
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 otc_divide_ratio PKL 轉 DB 工具")
    print("保留 2018-01-01 以後的資料")
    print("=" * 60)
    
    # 執行轉換
    if convert_and_filter_otc_divide_ratio():
        # 驗證轉換結果
        if verify_conversion():
            print("\n🎉 轉換和驗證都成功完成！")
        else:
            print("\n⚠️ 轉換完成但驗證失敗，請檢查資料")
    else:
        print("\n❌ 轉換失敗")

if __name__ == "__main__":
    main()
