#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統一資料庫系統
"""

import sqlite3
import pandas as pd
from unified_stock_database import UnifiedStockDatabase

def test_database_structure():
    """測試資料庫結構"""
    print("=== 測試資料庫結構 ===")
    
    db_path = 'D:/Finlab/history/tables/unified_stock_data_new.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 檢查stock_daily_data表結構
    cursor.execute('PRAGMA table_info(stock_daily_data)')
    columns = cursor.fetchall()
    print('stock_daily_data表結構:')
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    print()
    
    # 檢查stock_info表結構
    cursor.execute('PRAGMA table_info(stock_info)')
    columns = cursor.fetchall()
    print('stock_info表結構:')
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    conn.close()

def test_data_content():
    """測試資料內容"""
    print("\n=== 測試資料內容 ===")
    
    db_path = 'D:/Finlab/history/tables/unified_stock_data_new.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 統計資料
    cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
    daily_count = cursor.fetchone()[0]
    print(f'交易資料筆數: {daily_count:,}')
    
    cursor.execute('SELECT COUNT(*) FROM stock_info')
    info_count = cursor.fetchone()[0]
    print(f'基本資料筆數: {info_count:,}')
    
    # 檢查資料完整性
    cursor.execute('''
        SELECT COUNT(DISTINCT s.stock_id) as total_stocks,
               COUNT(DISTINCT i.stock_id) as stocks_with_info
        FROM stock_daily_data s
        LEFT JOIN stock_info i ON s.stock_id = i.stock_id
    ''')
    result = cursor.fetchone()
    print(f'有交易資料的股票數: {result[0]:,}')
    print(f'有基本資料的股票數: {result[1]:,}')
    
    conn.close()

def test_query_performance():
    """測試查詢效能"""
    print("\n=== 測試查詢效能 ===")
    
    db = UnifiedStockDatabase('D:/Finlab/history/tables/unified_stock_data_new.db')
    
    # 測試獲取股票清單
    import time
    start_time = time.time()
    stock_list = db.get_stock_list()
    end_time = time.time()
    
    print(f'獲取股票清單: {len(stock_list)} 支股票，耗時 {end_time - start_time:.2f} 秒')
    
    # 顯示前10支股票
    print('\n前10支股票:')
    for i, stock in enumerate(stock_list[:10]):
        print(f"  {stock['stock_id']} {stock['stock_name']} ({stock['listing_status']}/{stock['industry']})")

def test_gui_compatibility():
    """測試GUI相容性"""
    print("\n=== 測試GUI相容性 ===")
    
    db_path = 'D:/Finlab/history/tables/unified_stock_data_new.db'
    conn = sqlite3.connect(db_path)
    
    # 模擬GUI的查詢
    query = '''
        SELECT DISTINCT s.stock_id, 
               COALESCE(i.stock_name, '') as stock_name,
               COALESCE(i.listing_status, '') as listing_status,
               COALESCE(i.industry, '') as industry
        FROM stock_daily_data s
        LEFT JOIN stock_info i ON s.stock_id = i.stock_id
        WHERE s.stock_id GLOB '[0-9][0-9][0-9][0-9]*'
          AND LENGTH(s.stock_id) BETWEEN 4 AND 6
        ORDER BY
            CASE
                WHEN LENGTH(s.stock_id) = 4 THEN 1
                WHEN LENGTH(s.stock_id) = 5 THEN 2
                WHEN LENGTH(s.stock_id) = 6 THEN 3
            END,
            CAST(s.stock_id AS INTEGER)
        LIMIT 20
    '''
    
    df = pd.read_sql_query(query, conn)
    print(f'GUI查詢結果: {len(df)} 支股票')
    
    print('\nGUI顯示格式範例:')
    for _, row in df.iterrows():
        stock_name = row['stock_name'] if row['stock_name'] else '未知'
        listing_status = row['listing_status'] if row['listing_status'] else ''
        industry = row['industry'] if row['industry'] else ''
        
        # 組合顯示文字（與GUI相同的邏輯）
        if listing_status and industry:
            item_text = f"{row['stock_id']} {stock_name} ({listing_status}/{industry})"
        elif listing_status:
            item_text = f"{row['stock_id']} {stock_name} ({listing_status})"
        elif stock_name != '未知':
            item_text = f"{row['stock_id']} {stock_name}"
        else:
            item_text = f"{row['stock_id']}"
            
        print(f"  {item_text}")
    
    conn.close()

def main():
    """主程式"""
    print("🚀 統一股票資料庫系統測試")
    print("=" * 50)
    
    try:
        test_database_structure()
        test_data_content()
        test_query_performance()
        test_gui_compatibility()
        
        print("\n✅ 所有測試完成！")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
