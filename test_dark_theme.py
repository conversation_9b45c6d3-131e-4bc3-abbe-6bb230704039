#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試黑底主題設計
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class DarkThemeTestWindow(QMainWindow):
    """測試黑底主題的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🌙 黑底主題設計測試")
        self.setGeometry(100, 100, 1100, 900)
        
        # 設置黑底主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🌙 黑底主題設計")
        title_font = QFont()
        title_font.setPointSize(22)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 20px; padding: 20px;")
        layout.addWidget(title_label)
        
        # 主題說明
        theme_info = QTextEdit()
        theme_info.setMaximumHeight(450)
        theme_info.setStyleSheet("""
            QTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
            }
        """)
        theme_info.setHtml("""
        <h2 style="color: #00d4ff;">🌙 黑底主題設計優勢</h2>
        
        <h3 style="color: #ffffff;">✨ 視覺優勢</h3>
        <ul>
            <li><b>護眼設計</b> - 減少藍光刺激，長時間使用更舒適</li>
            <li><b>現代感</b> - 符合當前流行的暗色主題趨勢</li>
            <li><b>專業外觀</b> - 給人專業、高端的視覺印象</li>
            <li><b>節能效果</b> - OLED螢幕下可節省電力</li>
        </ul>
        
        <h3 style="color: #ffffff;">🎨 設計特色</h3>
        <ul>
            <li><b>主背景</b> - #2b2b2b (深灰黑)</li>
            <li><b>次背景</b> - #3c3c3c (中灰)</li>
            <li><b>控件背景</b> - #404040 (淺灰)</li>
            <li><b>主要文字</b> - #ffffff (純白)</li>
            <li><b>次要文字</b> - #cccccc (淺灰)</li>
            <li><b>強調色</b> - #00d4ff (青藍色)</li>
        </ul>
        
        <h3 style="color: #ffffff;">🔧 技術實現</h3>
        <ul>
            <li><b>統一配色</b> - 所有元件使用一致的顏色方案</li>
            <li><b>高對比度</b> - 確保文字清晰可讀</li>
            <li><b>漸層效果</b> - 使用不同深度的灰色創造層次感</li>
            <li><b>強調色運用</b> - 青藍色用於重要元素和互動狀態</li>
        </ul>
        
        <h3 style="color: #ffffff;">🎯 用戶體驗</h3>
        <ul>
            <li><b>無對比度問題</b> - 白字在黑底上清晰可見</li>
            <li><b>視覺舒適</b> - 減少眼部疲勞</li>
            <li><b>專注力提升</b> - 暗色背景有助於專注內容</li>
            <li><b>美觀大方</b> - 現代化的視覺設計</li>
        </ul>
        
        <h3 style="color: #ffffff;">📊 顏色對比度</h3>
        <table border="1" style="border-collapse: collapse; width: 100%; color: #ffffff;">
            <tr style="background-color: #505050;">
                <th style="padding: 8px;">前景色</th>
                <th style="padding: 8px;">背景色</th>
                <th style="padding: 8px;">對比度</th>
                <th style="padding: 8px;">評級</th>
            </tr>
            <tr>
                <td style="padding: 8px; color: #ffffff;">#ffffff</td>
                <td style="padding: 8px; background-color: #2b2b2b;">#2b2b2b</td>
                <td style="padding: 8px;">15.3:1</td>
                <td style="padding: 8px; color: #00ff00;">AAA</td>
            </tr>
            <tr>
                <td style="padding: 8px; color: #cccccc;">#cccccc</td>
                <td style="padding: 8px; background-color: #2b2b2b;">#2b2b2b</td>
                <td style="padding: 8px;">9.5:1</td>
                <td style="padding: 8px; color: #00ff00;">AAA</td>
            </tr>
            <tr>
                <td style="padding: 8px; color: #00d4ff;">#00d4ff</td>
                <td style="padding: 8px; background-color: #2b2b2b;">#2b2b2b</td>
                <td style="padding: 8px;">8.2:1</td>
                <td style="padding: 8px; color: #00ff00;">AAA</td>
            </tr>
        </table>
        """)
        layout.addWidget(theme_info)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟黑底主題爬蟲界面")
        test_btn.clicked.connect(self.test_dark_theme)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #00d4ff;
                color: #000000;
                border: none;
                padding: 18px 40px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 18px;
            }
            QPushButton:hover {
                background-color: #00b8e6;
            }
        """)
        layout.addWidget(test_btn)
        
        # 主題對比展示
        contrast_label = QLabel("🎨 主題對比展示")
        contrast_label.setStyleSheet("color: #ffffff; font-size: 16px; font-weight: bold; margin: 15px 5px 5px 5px;")
        layout.addWidget(contrast_label)
        
        # 顏色示例
        color_demo = QLabel("✅ 白色文字在黑色背景上 - 完美對比度，無需調整")
        color_demo.setStyleSheet("""
            QLabel {
                background-color: #3c3c3c;
                color: #ffffff;
                padding: 15px;
                border: 1px solid #555555;
                border-radius: 6px;
                font-size: 14px;
                margin: 5px;
            }
        """)
        layout.addWidget(color_demo)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #00d4ff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🌙 黑底主題測試程式已啟動")
        self.log("✅ 採用現代化暗色設計")
        self.log("✅ 完美的對比度，無需調整")
        self.log("✅ 護眼且專業的視覺效果")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_dark_theme(self):
        """測試黑底主題"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟黑底主題爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 黑底主題界面已成功創建")
            self.log("🎯 請欣賞現代化的暗色設計")
            self.log("🌙 護眼且專業的視覺效果")
            self.log("✨ 完美的對比度，清晰易讀")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = DarkThemeTestWindow()
    window.show()
    
    print("🌙 黑底主題測試程式已啟動")
    print("✅ 現代化暗色設計")
    print("✅ 護眼且專業")
    print("✅ 完美對比度")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
