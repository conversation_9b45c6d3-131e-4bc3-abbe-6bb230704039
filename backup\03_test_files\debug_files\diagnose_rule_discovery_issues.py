#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷交易規則發現系統問題
檢查資料庫、數據和分析流程
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database_connection():
    """檢查資料庫連接"""
    print("🔍 檢查資料庫連接...")
    
    # 可能的資料庫路徑
    possible_paths = [
        "db/price.db",
        "history/tables/price.db", 
        "D:/Finlab/history/tables/price.db",
        "price.db"
    ]
    
    for db_path in possible_paths:
        if os.path.exists(db_path):
            print(f"✅ 找到資料庫: {db_path}")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"📋 資料庫表格: {[table[0] for table in tables]}")
                
                conn.close()
                return db_path
            except Exception as e:
                print(f"❌ 資料庫連接失敗: {e}")
        else:
            print(f"❌ 資料庫不存在: {db_path}")
    
    return None

def check_stock_data_table(db_path):
    """檢查股票數據表格"""
    print(f"\n📊 檢查股票數據表格...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查可能的表格名稱
        possible_tables = [
            'stock_daily_data',
            'stock_data', 
            'daily_data',
            'price_data',
            'stock_prices'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [table[0] for table in cursor.fetchall()]
        print(f"📋 現有表格: {existing_tables}")
        
        # 找到正確的表格
        correct_table = None
        for table in possible_tables:
            if table in existing_tables:
                correct_table = table
                break
        
        if not correct_table:
            print("❌ 找不到股票數據表格")
            # 檢查第一個表格的結構
            if existing_tables:
                first_table = existing_tables[0]
                print(f"🔍 檢查表格 {first_table} 的結構:")
                cursor.execute(f"PRAGMA table_info({first_table})")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"   {col[1]} ({col[2]})")
            conn.close()
            return None, None
        
        print(f"✅ 使用表格: {correct_table}")
        
        # 檢查表格結構
        cursor.execute(f"PRAGMA table_info({correct_table})")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"📋 表格欄位: {column_names}")
        
        # 檢查數據量
        cursor.execute(f"SELECT COUNT(*) FROM {correct_table}")
        total_rows = cursor.fetchone()[0]
        print(f"📊 總數據筆數: {total_rows}")
        
        # 檢查股票數量
        if 'stock_id' in column_names:
            cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {correct_table}")
            stock_count = cursor.fetchone()[0]
            print(f"📈 股票數量: {stock_count}")
            
            # 檢查最近的數據
            cursor.execute(f"""
                SELECT stock_id, COUNT(*) as data_count, MAX(date) as latest_date
                FROM {correct_table} 
                GROUP BY stock_id 
                ORDER BY data_count DESC 
                LIMIT 10
            """)
            top_stocks = cursor.fetchall()
            print(f"📊 數據最多的前10檔股票:")
            for stock in top_stocks:
                print(f"   {stock[0]}: {stock[1]}筆數據, 最新日期: {stock[2]}")
        
        conn.close()
        return correct_table, column_names
        
    except Exception as e:
        print(f"❌ 檢查股票數據表格失敗: {e}")
        return None, None

def test_stock_data_loading(db_path, table_name):
    """測試股票數據載入"""
    print(f"\n🧪 測試股票數據載入...")
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 獲取一檔有較多數據的股票
        cursor = conn.cursor()
        cursor.execute(f"""
            SELECT stock_id, COUNT(*) as data_count
            FROM {table_name}
            WHERE date >= date('now', '-3 years')
            GROUP BY stock_id
            HAVING data_count >= 500
            ORDER BY data_count DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ 找不到有足夠數據的股票")
            conn.close()
            return False
        
        test_stock = result[0]
        data_count = result[1]
        print(f"📊 測試股票: {test_stock} (數據量: {data_count})")
        
        # 載入數據
        query = f"""
            SELECT date, open, high, low, close, volume
            FROM {table_name}
            WHERE stock_id = ?
            AND date >= date('now', '-1000 days')
            ORDER BY date
        """
        
        df = pd.read_sql_query(query, conn, params=(test_stock,))
        conn.close()
        
        if df.empty:
            print("❌ 載入的數據為空")
            return False
        
        print(f"✅ 成功載入 {len(df)} 筆數據")
        print(f"📅 日期範圍: {df['date'].min()} 到 {df['date'].max()}")
        print(f"💰 價格範圍: {df['close'].min():.2f} 到 {df['close'].max():.2f}")
        
        # 檢查數據完整性
        missing_data = df.isnull().sum()
        if missing_data.sum() > 0:
            print(f"⚠️ 缺失數據: {missing_data.to_dict()}")
        else:
            print("✅ 數據完整，無缺失值")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試股票數據載入失敗: {e}")
        return False

def test_technical_indicators(db_path, table_name):
    """測試技術指標計算"""
    print(f"\n📈 測試技術指標計算...")
    
    try:
        from mass_backtest_analyzer import MassBacktestAnalyzer
        
        # 創建分析器，使用正確的資料庫路徑
        analyzer = MassBacktestAnalyzer(db_path=db_path)
        
        # 修正表格名稱
        analyzer.table_name = table_name
        
        # 載入測試數據
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f"""
            SELECT stock_id FROM {table_name}
            WHERE date >= date('now', '-2 years')
            GROUP BY stock_id
            HAVING COUNT(*) >= 500
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ 找不到測試股票")
            conn.close()
            return False
        
        test_stock = result[0]
        conn.close()
        
        print(f"📊 測試股票: {test_stock}")
        
        # 載入股票數據
        df = analyzer.load_stock_data(test_stock)
        if df.empty:
            print("❌ 載入股票數據失敗")
            return False
        
        print(f"✅ 載入 {len(df)} 筆數據")
        
        # 計算技術指標
        df = analyzer.calculate_technical_indicators(df)
        
        # 檢查指標
        indicators = ['rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'ma5', 'ma20']
        for indicator in indicators:
            if indicator in df.columns:
                valid_count = df[indicator].notna().sum()
                print(f"✅ {indicator}: {valid_count} 個有效值")
            else:
                print(f"❌ {indicator}: 指標缺失")
        
        # 生成信號
        signals = analyzer.generate_strategy_signals(df)
        
        print(f"📊 信號統計:")
        for strategy, signal_data in signals.items():
            buy_count = len(signal_data['buy'])
            sell_count = len(signal_data['sell'])
            print(f"   {strategy}: 買入{buy_count}個, 賣出{sell_count}個")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試技術指標計算失敗: {e}")
        return False

def create_fixed_analyzer():
    """創建修正版的分析器"""
    print(f"\n🔧 創建修正版分析器...")
    
    # 檢查並修正 mass_backtest_analyzer.py
    try:
        with open('mass_backtest_analyzer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否需要修正表格名稱
        if 'stock_daily_data' in content:
            print("⚠️ 發現硬編碼的表格名稱，需要修正")
            
            # 建議的修正方案
            print("💡 建議修正方案:")
            print("1. 在 __init__ 方法中添加 table_name 參數")
            print("2. 動態檢測正確的表格名稱")
            print("3. 降低最少交易次數門檻")
            
            return True
        
    except Exception as e:
        print(f"❌ 檢查分析器失敗: {e}")
        return False

def main():
    """主診斷函數"""
    print("🔍 交易規則發現系統問題診斷")
    print("="*50)
    
    # 1. 檢查資料庫連接
    db_path = check_database_connection()
    if not db_path:
        print("\n❌ 無法找到有效的資料庫文件")
        print("💡 請確認資料庫路徑是否正確")
        return
    
    # 2. 檢查股票數據表格
    table_name, columns = check_stock_data_table(db_path)
    if not table_name:
        print("\n❌ 無法找到有效的股票數據表格")
        return
    
    # 3. 測試數據載入
    if not test_stock_data_loading(db_path, table_name):
        print("\n❌ 股票數據載入測試失敗")
        return
    
    # 4. 測試技術指標計算
    if not test_technical_indicators(db_path, table_name):
        print("\n❌ 技術指標計算測試失敗")
    
    # 5. 創建修正版分析器
    create_fixed_analyzer()
    
    print("\n" + "="*50)
    print("🎯 診斷完成！")
    print("\n💡 可能的問題和解決方案:")
    print("1. 資料庫表格名稱不匹配 - 需要修正硬編碼的表格名")
    print("2. 數據量不足 - 降低最少數據天數要求")
    print("3. 信號門檻太高 - 降低最少交易次數")
    print("4. 資料庫路徑錯誤 - 確認正確的資料庫路徑")

if __name__ == "__main__":
    main()
