#!/usr/bin/env python3
"""
測試TSRTC數據格式化修復
"""

import sys
import os
import json
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_safe_conversion():
    """測試安全轉換函數"""
    print("🧪 測試安全轉換函數...")
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor

        monitor = TSRTCBackupMonitor()
        
        # 測試 _safe_float
        test_cases_float = [
            ('42.85', 42.85),
            ('-', 0.0),
            ('', 0.0),
            (None, 0.0),
            ('0', 0.0),
            ('123.45', 123.45),
            ('1,234.56', 1234.56),
            ('invalid', 0.0)
        ]
        
        print("📊 測試 _safe_float:")
        for input_val, expected in test_cases_float:
            result = monitor._safe_float(input_val)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {input_val!r} -> {result} (期望: {expected})")
        
        # 測試 _safe_int
        test_cases_int = [
            ('1600', 1600),
            ('-', 0),
            ('', 0),
            (None, 0),
            ('0', 0),
            ('12345', 12345),
            ('1,234', 1234),
            ('invalid', 0)
        ]
        
        print("\n📊 測試 _safe_int:")
        for input_val, expected in test_cases_int:
            result = monitor._safe_int(input_val)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {input_val!r} -> {result} (期望: {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全轉換函數測試失敗: {e}")
        return False

def test_data_formatting():
    """測試數據格式化"""
    print("\n🧪 測試數據格式化...")
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor

        monitor = TSRTCBackupMonitor()
        
        # 模擬包含 '-' 的原始數據
        test_raw_data = {
            'c': '2330',
            'n': '台積電',
            'z': '580.00',  # 現價
            'y': '575.00',  # 昨收
            'o': '-',       # 開盤價 (問題數據)
            'h': '582.00',  # 最高
            'l': '-',       # 最低 (問題數據)
            'v': '12345',   # 成交量
            'tv': '-',      # 當盤成交量 (問題數據)
            'a': '580.50_581.00_-_582.00_',  # 賣出五檔 (包含問題數據)
            'f': '100_200_-_300_',           # 賣出量 (包含問題數據)
            'b': '579.50_579.00_578.50_-_',  # 買入五檔 (包含問題數據)
            'g': '150_250_-_400_',           # 買入量 (包含問題數據)
            't': '13:30:00',
            'd': '20250714'
        }
        
        print("📊 測試原始數據:")
        for key, value in test_raw_data.items():
            print(f"   {key}: {value!r}")
        
        # 格式化數據
        formatted_data = monitor._format_stock_data(test_raw_data)
        
        if formatted_data:
            print("\n✅ 格式化成功！")
            print("📊 格式化結果:")
            
            key_mappings = {
                'stock_code': '股票代碼',
                'stock_name': '股票名稱',
                'current_price': '現價',
                'prev_close': '昨收',
                'open_price': '開盤',
                'high_price': '最高',
                'low_price': '最低',
                'change_amount': '漲跌',
                'change_percent': '漲跌幅%',
                'volume': '成交量',
                'current_volume': '當盤量',
                'ask_prices': '賣出價',
                'ask_volumes': '賣出量',
                'bid_prices': '買入價',
                'bid_volumes': '買入量'
            }
            
            for key, chinese_name in key_mappings.items():
                if key in formatted_data:
                    value = formatted_data[key]
                    print(f"   {chinese_name}: {value}")
            
            # 檢查關鍵數據
            checks = [
                ('現價 > 0', formatted_data.get('current_price', 0) > 0),
                ('昨收 > 0', formatted_data.get('prev_close', 0) > 0),
                ('開盤價為0 (因為是-)', formatted_data.get('open_price', -1) == 0),
                ('最低價為0 (因為是-)', formatted_data.get('low_price', -1) == 0),
                ('當盤量為0 (因為是-)', formatted_data.get('current_volume', -1) == 0),
                ('有賣出價格', len(formatted_data.get('ask_prices', [])) > 0),
                ('有買入價格', len(formatted_data.get('bid_prices', [])) > 0)
            ]
            
            print("\n🔍 數據檢查:")
            all_passed = True
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
                if not result:
                    all_passed = False
            
            return all_passed
        else:
            print("❌ 格式化失敗")
            return False
        
    except Exception as e:
        print(f"❌ 數據格式化測試失敗: {e}")
        return False

def test_real_tsrtc_data():
    """測試真實TSRTC數據"""
    print("\n🧪 測試真實TSRTC數據獲取...")
    
    try:
        from tsrtc_backup_monitor import TSRTCIntegration

        monitor = TSRTCIntegration()
        
        # 測試獲取台積電數據
        print("📊 嘗試獲取台積電(2330)即時數據...")
        data = monitor.get_realtime_quote('2330')
        
        if data:
            print("✅ 成功獲取數據！")
            print("📊 數據內容:")
            
            key_mappings = {
                'stock_code': '股票代碼',
                'stock_name': '股票名稱',
                'current_price': '現價',
                'prev_close': '昨收',
                'change_amount': '漲跌',
                'change_percent': '漲跌幅%',
                'volume': '成交量',
                'source': '數據來源'
            }
            
            for key, chinese_name in key_mappings.items():
                if key in data:
                    value = data[key]
                    if key == 'change_percent':
                        print(f"   {chinese_name}: {value:.2f}%")
                    elif key in ['current_price', 'prev_close', 'change_amount']:
                        print(f"   {chinese_name}: {value:.2f}")
                    else:
                        print(f"   {chinese_name}: {value}")
            
            return True
        else:
            print("⚠️ 無法獲取數據（可能是非交易時間或網路問題）")
            return True  # 非交易時間是正常的
        
    except Exception as e:
        print(f"❌ 真實數據測試失敗: {e}")
        return False

def test_batch_data():
    """測試批量數據獲取"""
    print("\n🧪 測試批量數據獲取...")
    
    try:
        from tsrtc_backup_monitor import TSRTCIntegration

        monitor = TSRTCIntegration()
        
        # 測試批量獲取
        test_stocks = ['2330', '2317', '2454']
        print(f"📊 嘗試批量獲取數據: {test_stocks}")
        
        results = monitor.get_multiple_quotes(test_stocks)
        
        if results:
            print(f"✅ 成功獲取 {len(results)} 支股票數據")
            
            for stock_code, data in results.items():
                if data:
                    name = data.get('stock_name', 'N/A')
                    price = data.get('current_price', 0)
                    change_pct = data.get('change_percent', 0)
                    print(f"   📈 {stock_code} {name}: {price:.2f} ({change_pct:+.2f}%)")
                else:
                    print(f"   ❌ {stock_code}: 無數據")
            
            return True
        else:
            print("⚠️ 無法獲取批量數據")
            return True  # 非交易時間是正常的
        
    except Exception as e:
        print(f"❌ 批量數據測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 TSRTC數據格式化修復測試")
    print("=" * 60)
    
    tests = [
        ("安全轉換函數", test_safe_conversion),
        ("數據格式化", test_data_formatting),
        ("真實TSRTC數據", test_real_tsrtc_data),
        ("批量數據獲取", test_batch_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 測試結果總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！TSRTC數據格式化問題已修復")
        print("\n💡 修復內容:")
        print("   • 添加了 _safe_float() 函數處理 '-' 字符串")
        print("   • 添加了 _safe_int() 函數處理數值轉換")
        print("   • 修復了價格、成交量、五檔數據的安全轉換")
        print("   • 增強了錯誤處理和容錯能力")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
