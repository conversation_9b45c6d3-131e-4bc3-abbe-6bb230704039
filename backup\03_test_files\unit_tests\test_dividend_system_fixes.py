#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息交易系統修復效果
驗證股票名稱顯示、顏色對比改善、真實股利資料等功能
"""

import sys
import os
import sqlite3
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt

class DividendSystemFixTest(QMainWindow):
    """除權息交易系統修復測試"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("除權息交易系統修復測試")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🔧 除權息交易系統修復測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 測試按鈕區域
        button_layout = QHBoxLayout()
        
        self.test_db_btn = QPushButton("📊 測試資料庫連接")
        self.test_db_btn.clicked.connect(self.test_database_connection)
        button_layout.addWidget(self.test_db_btn)
        
        self.test_stock_names_btn = QPushButton("📝 測試股票名稱")
        self.test_stock_names_btn.clicked.connect(self.test_stock_names)
        button_layout.addWidget(self.test_stock_names_btn)
        
        self.test_colors_btn = QPushButton("🎨 測試顏色對比")
        self.test_colors_btn.clicked.connect(self.test_color_contrast)
        button_layout.addWidget(self.test_colors_btn)
        
        self.test_integration_btn = QPushButton("🔗 測試完整功能")
        self.test_integration_btn.clicked.connect(self.test_full_integration)
        button_layout.addWidget(self.test_integration_btn)
        
        layout.addLayout(button_layout)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        self.log("🚀 除權息交易系統修復測試已啟動")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def test_database_connection(self):
        """測試除權息資料庫連接"""
        self.log("📊 開始測試除權息資料庫連接...")
        
        try:
            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            
            if not os.path.exists(dividend_db_path):
                self.log(f"❌ 除權息資料庫不存在: {dividend_db_path}")
                return False
            
            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()
            
            # 檢查資料庫結構
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dividend_data'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                self.log("❌ dividend_data 表格不存在")
                conn.close()
                return False
            
            self.log("✅ dividend_data 表格存在")
            
            # 檢查資料數量
            cursor.execute("SELECT COUNT(*) FROM dividend_data")
            total_count = cursor.fetchone()[0]
            self.log(f"📊 總記錄數: {total_count}")
            
            # 檢查2025年6月份資料
            cursor.execute("""
                SELECT COUNT(*) FROM dividend_data 
                WHERE year = 2025 AND ex_dividend_date LIKE '2025-06%'
            """)
            june_count = cursor.fetchone()[0]
            self.log(f"📅 2025年6月份記錄數: {june_count}")
            
            # 檢查股票名稱完整性
            cursor.execute("""
                SELECT COUNT(*) FROM dividend_data 
                WHERE stock_name IS NOT NULL AND stock_name != ''
            """)
            name_count = cursor.fetchone()[0]
            self.log(f"📝 有股票名稱的記錄數: {name_count}")
            
            # 顯示部分資料
            cursor.execute("""
                SELECT stock_code, stock_name, ex_dividend_date, cash_dividend 
                FROM dividend_data 
                WHERE year = 2025 AND ex_dividend_date LIKE '2025-06%'
                LIMIT 5
            """)
            
            sample_data = cursor.fetchall()
            if sample_data:
                self.log("📋 6月份除權息資料樣本:")
                for row in sample_data:
                    stock_code, stock_name, ex_date, dividend = row
                    self.log(f"  {stock_code} {stock_name or '(無名稱)'} {ex_date} 股利:{dividend or 0}")
            else:
                self.log("⚠️ 無6月份除權息資料")
            
            conn.close()
            self.log("✅ 除權息資料庫連接測試完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 除權息資料庫連接測試失敗: {e}")
            return False
    
    def test_stock_names(self):
        """測試股票名稱獲取功能"""
        self.log("📝 開始測試股票名稱獲取...")
        
        try:
            from dividend_trading_gui import DividendTradingGUI
            
            # 創建GUI實例（不顯示）
            app = QApplication.instance() or QApplication(sys.argv)
            gui = DividendTradingGUI()
            
            # 測試股票名稱獲取
            test_stocks = ['2330', '2317', '3443', '8454', '2454']
            
            for stock_code in test_stocks:
                stock_name = gui.get_stock_name_from_dividend_db(stock_code)
                if stock_name:
                    self.log(f"✅ {stock_code}: {stock_name}")
                else:
                    self.log(f"❌ {stock_code}: 無法獲取名稱")
            
            # 測試真實除權息資料獲取
            dividend_data = gui.get_real_dividend_data()
            
            if dividend_data:
                self.log(f"✅ 獲取到 {len(dividend_data)} 筆真實除權息資料")
                for stock_code, data in list(dividend_data.items())[:3]:
                    self.log(f"  {stock_code}: {data.get('stock_name', '無名稱')} 股利:{data.get('cash_dividend', 0)}")
            else:
                self.log("❌ 無法獲取真實除權息資料")
            
            gui.close()
            self.log("✅ 股票名稱獲取測試完成")
            
        except Exception as e:
            self.log(f"❌ 股票名稱獲取測試失敗: {e}")
    
    def test_color_contrast(self):
        """測試顏色對比改善"""
        self.log("🎨 開始測試顏色對比改善...")
        
        try:
            from PyQt6.QtGui import QColor
            
            # 測試新的顏色配置
            colors = {
                "正值背景": QColor(46, 125, 50),    # 深綠色
                "負值背景": QColor(183, 28, 28),    # 深紅色
                "中性背景": QColor(97, 97, 97),     # 深灰色
                "文字顏色": QColor(255, 255, 255),  # 白色
                "優秀評分": QColor(76, 175, 80),    # 綠色
                "良好評分": QColor(139, 195, 74),   # 淺綠色
                "普通評分": QColor(255, 193, 7),    # 黃色
                "不佳評分": QColor(244, 67, 54)     # 紅色
            }
            
            self.log("🎨 新的顏色配置:")
            for name, color in colors.items():
                rgb = f"RGB({color.red()}, {color.green()}, {color.blue()})"
                self.log(f"  {name}: {rgb}")
            
            # 計算對比度（簡化版本）
            def calculate_contrast_ratio(color1, color2):
                # 簡化的對比度計算
                l1 = (color1.red() * 0.299 + color1.green() * 0.587 + color1.blue() * 0.114) / 255
                l2 = (color2.red() * 0.299 + color2.green() * 0.587 + color2.blue() * 0.114) / 255
                
                if l1 > l2:
                    return (l1 + 0.05) / (l2 + 0.05)
                else:
                    return (l2 + 0.05) / (l1 + 0.05)
            
            # 測試對比度
            white = QColor(255, 255, 255)
            test_backgrounds = [
                ("深綠色背景", QColor(46, 125, 50)),
                ("深紅色背景", QColor(183, 28, 28)),
                ("深灰色背景", QColor(97, 97, 97))
            ]
            
            self.log("\n📊 對比度測試結果:")
            for name, bg_color in test_backgrounds:
                contrast = calculate_contrast_ratio(white, bg_color)
                status = "✅ 良好" if contrast >= 4.5 else "⚠️ 需改善"
                self.log(f"  {name} vs 白色文字: {contrast:.2f} {status}")
            
            self.log("✅ 顏色對比測試完成")
            
        except Exception as e:
            self.log(f"❌ 顏色對比測試失敗: {e}")
    
    def test_full_integration(self):
        """測試完整功能整合"""
        self.log("🔗 開始測試完整功能整合...")
        
        try:
            # 測試除權息交易系統
            from dividend_trading_system import DividendTradingSystem
            
            system = DividendTradingSystem()
            self.log("✅ 除權息交易系統創建成功")
            
            # 測試資料獲取
            dividend_data = system.get_dividend_data_from_db()
            
            if not dividend_data.empty:
                self.log(f"✅ 從資料庫獲取 {len(dividend_data)} 筆除權息資料")
                
                # 顯示資料樣本
                for i, (_, row) in enumerate(dividend_data.head(3).iterrows()):
                    stock_code = row['stock_code']
                    stock_name = row.get('stock_name', '無名稱')
                    ex_date = row['ex_date']
                    dividend = row['cash_dividend']
                    self.log(f"  {stock_code} {stock_name} {ex_date} 股利:{dividend}")
            else:
                self.log("⚠️ 資料庫無資料，將使用示例資料")
            
            # 測試市場情緒評估
            result = system.evaluate_market_sentiment()
            
            if result and 'sentiment_score' in result:
                self.log(f"✅ 市場情緒評估成功")
                self.log(f"  情緒分數: {result['sentiment_score']:.1f}/100")
                self.log(f"  市場狀態: {result.get('sentiment', 'unknown')}")
                self.log(f"  建議策略: {result.get('strategy', 'unknown')}")
                
                # 檢查個股表現資料
                performance_data = result.get('performance_data', {})
                if performance_data:
                    self.log(f"  個股表現資料: {len(performance_data)} 檔股票")
                    for stock_code, perf in list(performance_data.items())[:2]:
                        gain = perf.get('same_day_gain', 0)
                        filled = perf.get('filled_dividend', False)
                        self.log(f"    {stock_code}: 當日{gain:+.1f}元, 填息:{filled}")
            else:
                self.log("❌ 市場情緒評估失敗")
            
            self.log("✅ 完整功能整合測試完成")
            
        except Exception as e:
            self.log(f"❌ 完整功能整合測試失敗: {e}")
            import traceback
            self.log(traceback.format_exc())

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("除權息系統修復測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = DividendSystemFixTest()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
