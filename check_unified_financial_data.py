#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查統一財務資料
"""

import sqlite3
import pandas as pd
import os

def check_unified_financial_data():
    """檢查統一財務資料"""
    
    print("=" * 80)
    print("📊 檢查統一財務資料")
    print("=" * 80)
    
    # 檢查統一財務資料檔案
    unified_db = r'D:\Finlab\history\tables\financial_data.db'
    
    if os.path.exists(unified_db):
        conn = sqlite3.connect(unified_db)
        
        # 檢查表格結構
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        
        print(f"📋 統一財務資料表格欄位 (總共 {len(columns)} 個):")
        
        # 分類顯示欄位
        basic_columns = []
        income_columns = []
        balance_columns = []
        
        for col in columns:
            col_name = col[1]
            if col_name in ['stock_id', 'stock_name', 'year', 'quarter', 'date', 'report_date', 'data_type']:
                basic_columns.append(col_name)
            elif col_name.startswith('income_'):
                income_columns.append(col_name)
            elif col_name.startswith('balance_'):
                balance_columns.append(col_name)
        
        print(f"   基本資訊 ({len(basic_columns)} 個): {basic_columns}")
        print(f"   損益項目 ({len(income_columns)} 個): {income_columns[:5]}...")
        print(f"   資產負債 ({len(balance_columns)} 個): {balance_columns[:5]}...")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        count = cursor.fetchone()[0]
        print(f"\n📊 資料筆數: {count:,}")
        
        # 檢查台積電資料
        print(f"\n📈 台積電 (2330) 財務資料:")
        query = """
        SELECT stock_id, stock_name, year, quarter, date, report_date
        FROM financial_data 
        WHERE stock_id = '2330'
        """
        
        df = pd.read_sql_query(query, conn)
        if len(df) > 0:
            row = df.iloc[0]
            print(f"   股票代碼: {row['stock_id']}")
            print(f"   公司名稱: {row['stock_name']}")
            print(f"   年度: {row['year']} (西元年)")
            print(f"   季別: {row['quarter']}")
            print(f"   統一日期: {row['date']}")
            print(f"   報告日期: {row['report_date']}")
        else:
            print(f"   ❌ 未找到台積電資料")
        
        # 檢查財務數據
        print(f"\n💰 台積電主要財務數據:")
        query2 = """
        SELECT income_營業收入, income_本期淨利（淨損）, 
               balance_資產總額, balance_負債總額
        FROM financial_data 
        WHERE stock_id = '2330'
        """
        
        df2 = pd.read_sql_query(query2, conn)
        if len(df2) > 0:
            row = df2.iloc[0]
            print(f"   營業收入: {float(row['income_營業收入']):,.0f} 千元")
            print(f"   本期淨利: {float(row['income_本期淨利（淨損）']):,.0f} 千元")
            print(f"   資產總額: {float(row['balance_資產總額']):,.0f} 千元")
            print(f"   負債總額: {float(row['balance_負債總額']):,.0f} 千元")
        
        # 檢查日期格式統一性
        print(f"\n📅 日期格式檢查:")
        query3 = """
        SELECT DISTINCT year, quarter, date, report_date
        FROM financial_data 
        ORDER BY year, quarter
        LIMIT 5
        """
        
        df3 = pd.read_sql_query(query3, conn)
        print(f"   年度格式: {df3['year'].dtype} (應為西元年)")
        print(f"   統一日期格式: {df3['date'].iloc[0]} (YYYY-MM-DD)")
        print(f"   報告日期格式: {df3['report_date'].iloc[0]} (YYYY-MM-DD)")
        
        conn.close()
        return True
    else:
        print(f"❌ 統一財務資料檔案不存在: {unified_db}")
        return False

def compare_old_vs_new():
    """比較舊檔案與新檔案"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 比較舊檔案與新統一檔案")
    print("=" * 80)
    
    # 檢查舊檔案
    old_files = [
        ('income_statements.db', 'income_statements'),
        ('balance_sheets.db', 'balance_sheets'),
        ('financial_statements.db', 'income_statements')
    ]
    
    target_dir = r'D:\Finlab\history\tables'
    
    print(f"📋 舊檔案狀況:")
    for filename, table_name in old_files:
        file_path = os.path.join(target_dir, filename)
        if os.path.exists(file_path):
            try:
                conn = sqlite3.connect(file_path)
                cursor = conn.cursor()
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {filename}: {count:,} 筆資料")
                conn.close()
            except Exception as e:
                print(f"   ❌ {filename}: 讀取失敗 ({e})")
        else:
            print(f"   ⚠️ {filename}: 檔案不存在")
    
    # 檢查新檔案
    new_file = os.path.join(target_dir, 'financial_data.db')
    if os.path.exists(new_file):
        conn = sqlite3.connect(new_file)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        count = cursor.fetchone()[0]
        print(f"\n📊 新統一檔案:")
        print(f"   ✅ financial_data.db: {count:,} 筆資料")
        conn.close()
    
    print(f"\n💡 建議:")
    print(f"   1. 新的 financial_data.db 已包含完整的財務資料")
    print(f"   2. 日期格式已統一為西元年")
    print(f"   3. 損益表和資產負債表已合併")
    print(f"   4. 可以考慮移除舊的分離檔案")

def show_usage_examples():
    """顯示使用範例"""
    
    print(f"\n" + "=" * 80)
    print(f"📝 統一財務資料使用範例")
    print("=" * 80)
    
    print(f"🔍 SQL 查詢範例:")
    
    examples = [
        ("查詢台積電財務資料", """
SELECT stock_id, stock_name, year, quarter, 
       income_營業收入, income_本期淨利（淨損）,
       balance_資產總額, balance_負債總額
FROM financial_data 
WHERE stock_id = '2330';
"""),
        ("營收前10名公司", """
SELECT stock_id, stock_name, income_營業收入
FROM financial_data 
ORDER BY CAST(income_營業收入 AS INTEGER) DESC 
LIMIT 10;
"""),
        ("ROA 計算", """
SELECT stock_id, stock_name,
       ROUND(CAST(income_本期淨利（淨損） AS REAL) / 
             CAST(balance_資產總額 AS REAL) * 100, 2) as ROA
FROM financial_data 
WHERE income_本期淨利（淨損） IS NOT NULL 
  AND balance_資產總額 IS NOT NULL
ORDER BY ROA DESC 
LIMIT 10;
"""),
        ("按年度查詢", """
SELECT year, COUNT(*) as company_count,
       AVG(CAST(income_營業收入 AS REAL)) as avg_revenue
FROM financial_data 
GROUP BY year 
ORDER BY year;
""")
    ]
    
    for title, query in examples:
        print(f"\n📊 {title}:")
        print(f"```sql{query}```")

def main():
    """主函數"""
    
    print("🔍 統一財務資料檢查")
    
    # 檢查統一財務資料
    check_result = check_unified_financial_data()
    
    # 比較舊檔案與新檔案
    compare_old_vs_new()
    
    # 顯示使用範例
    show_usage_examples()
    
    print(f"\n" + "=" * 80)
    print(f"📊 總結")
    print("=" * 80)
    
    if check_result:
        print(f"🎉 統一財務資料檢查完成！")
        print(f"\n✅ 改進成果:")
        print(f"   - 日期格式統一為西元年")
        print(f"   - 損益表和資產負債表合併")
        print(f"   - 單一檔案便於管理")
        print(f"   - 欄位命名標準化")
        
        print(f"\n🚀 立即可用:")
        print(f"   資料庫: D:\\Finlab\\history\\tables\\financial_data.db")
        print(f"   表格: financial_data")
        print(f"   包含: 1,008 家上市公司完整財務資料")
    else:
        print(f"❌ 統一財務資料檢查失敗")

if __name__ == "__main__":
    main()
