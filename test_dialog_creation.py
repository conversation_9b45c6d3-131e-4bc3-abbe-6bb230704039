#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 DateOverlapDialog 創建
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dialog_creation():
    """測試對話框創建"""
    try:
        from twse_market_data_dialog import DateOverlapDialog
        
        print("✅ DateOverlapDialog 導入成功")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主窗口作為父窗口
        main_window = QMainWindow()
        
        # 測試參數
        requested_start = "2025-07-01"
        requested_end = "2025-07-31"
        existing_min = "2025-06-28"
        existing_max = "2025-07-28"
        overlapping_dates = ["20250701", "20250702", "20250703"]
        total_existing = 31
        
        print("🔧 正在創建 DateOverlapDialog...")
        
        # 創建對話框
        dialog = DateOverlapDialog(
            main_window,
            requested_start,
            requested_end,
            existing_min,
            existing_max,
            overlapping_dates,
            total_existing
        )
        
        print("✅ DateOverlapDialog 創建成功")
        print("✅ QRadioButton 組件正常工作")
        print("✅ 所有必要的 PyQt6 組件都已正確導入")
        
        # 測試獲取用戶選擇
        choice = dialog.get_user_choice()
        print(f"✅ 默認選擇: {choice}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🔧 DateOverlapDialog 創建測試")
    print("=" * 50)
    
    success = test_dialog_creation()
    
    print("=" * 50)
    if success:
        print("🎉 測試通過")
        print("✅ QRadioButton 導入問題已修復")
        print("✅ DateOverlapDialog 可以正常創建")
        print("✅ 開始爬取按鈕現在可以正常工作")
    else:
        print("❌ 測試失敗")
        print("❌ 仍有問題需要修復")

if __name__ == "__main__":
    main()
