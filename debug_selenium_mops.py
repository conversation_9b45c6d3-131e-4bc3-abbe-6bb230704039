#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 Selenium MOPS 頁面結構
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time
from bs4 import BeautifulSoup

def debug_mops_page_structure():
    """調試 MOPS 頁面結構"""
    
    print("=" * 80)
    print("🔍 調試 MOPS 頁面結構 (使用 Selenium)")
    print("=" * 80)
    
    driver = None
    
    try:
        # 設定 Chrome 選項 (非無頭模式，可以看到瀏覽器)
        chrome_options = Options()
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # 自動下載並設置 ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 訪問 MOPS 現金流量表頁面
        url = 'https://mops.twse.com.tw/mops/web/t164sb05'
        print(f"📡 訪問頁面: {url}")
        
        driver.get(url)
        time.sleep(5)  # 等待頁面完全加載
        
        # 檢查頁面基本資訊
        print(f"📄 頁面標題: {driver.title}")
        print(f"📄 當前 URL: {driver.current_url}")
        
        # 獲取頁面源碼
        page_source = driver.page_source
        print(f"📄 頁面源碼長度: {len(page_source)} 字符")
        
        # 檢查是否有重定向
        if driver.current_url != url:
            print(f"⚠️ 頁面被重定向到: {driver.current_url}")
        
        # 使用 BeautifulSoup 解析頁面結構
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 檢查所有表單
        forms = soup.find_all('form')
        print(f"\n📝 找到 {len(forms)} 個表單:")
        
        for i, form in enumerate(forms):
            action = form.get('action', '無action')
            method = form.get('method', '無method')
            print(f"   表單 {i+1}: action={action}, method={method}")
            
            # 檢查表單內的輸入元素
            inputs = form.find_all(['input', 'select', 'textarea'])
            print(f"      輸入元素: {len(inputs)} 個")
            
            for j, input_elem in enumerate(inputs[:5]):  # 只顯示前5個
                name = input_elem.get('name', '無name')
                input_type = input_elem.get('type', input_elem.name)
                print(f"         {j+1}. {input_type}: name='{name}'")
        
        # 檢查所有輸入元素 (不限於表單內)
        all_inputs = soup.find_all(['input', 'select', 'textarea'])
        print(f"\n📋 頁面中所有輸入元素: {len(all_inputs)} 個")
        
        # 尋找可能的公司代碼輸入框
        co_id_candidates = []
        for input_elem in all_inputs:
            name = input_elem.get('name', '')
            id_attr = input_elem.get('id', '')
            
            if any(keyword in name.lower() for keyword in ['co_id', 'company', 'stock']):
                co_id_candidates.append(f"name='{name}', id='{id_attr}'")
            elif any(keyword in id_attr.lower() for keyword in ['co_id', 'company', 'stock']):
                co_id_candidates.append(f"name='{name}', id='{id_attr}'")
        
        if co_id_candidates:
            print(f"📊 可能的公司代碼輸入框:")
            for candidate in co_id_candidates:
                print(f"   - {candidate}")
        else:
            print(f"⚠️ 未找到明顯的公司代碼輸入框")
        
        # 檢查是否包含現金流量相關內容
        cash_flow_keywords = ['現金流量', '營業活動', '投資活動', '籌資活動']
        found_keywords = [kw for kw in cash_flow_keywords if kw in page_source]
        
        if found_keywords:
            print(f"\n💰 頁面包含現金流量關鍵字: {found_keywords}")
        else:
            print(f"\n⚠️ 頁面未包含現金流量關鍵字")
        
        # 檢查是否有 JavaScript 錯誤或特殊內容
        if 'location.href' in page_source:
            print(f"🔄 頁面包含 JavaScript 重定向")
        
        if 'error' in page_source.lower():
            print(f"❌ 頁面可能包含錯誤訊息")
        
        # 保存頁面源碼到檔案以供檢查
        with open('mops_page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print(f"\n💾 頁面源碼已保存到: mops_page_source.html")
        
        # 嘗試尋找所有可能的選擇器
        print(f"\n🔍 嘗試不同的選擇器:")
        
        selectors_to_try = [
            ('name', 'co_id'),
            ('id', 'co_id'),
            ('name', 'company_id'),
            ('name', 'stock_id'),
            ('css', 'input[name*="co"]'),
            ('css', 'input[type="text"]'),
            ('xpath', '//input[@type="text"]'),
        ]
        
        for selector_type, selector in selectors_to_try:
            try:
                if selector_type == 'name':
                    elements = driver.find_elements(By.NAME, selector)
                elif selector_type == 'id':
                    elements = driver.find_elements(By.ID, selector)
                elif selector_type == 'css':
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                elif selector_type == 'xpath':
                    elements = driver.find_elements(By.XPATH, selector)
                
                print(f"   {selector_type}='{selector}': 找到 {len(elements)} 個元素")
                
                if elements:
                    for i, elem in enumerate(elements[:3]):  # 只顯示前3個
                        tag_name = elem.tag_name
                        name_attr = elem.get_attribute('name')
                        id_attr = elem.get_attribute('id')
                        print(f"      {i+1}. <{tag_name}> name='{name_attr}' id='{id_attr}'")
                
            except Exception as e:
                print(f"   {selector_type}='{selector}': 錯誤 - {e}")
        
        # 等待用戶檢查 (如果是非無頭模式)
        print(f"\n⏸️ 瀏覽器將保持開啟 10 秒，請檢查頁面...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ 調試過程發生錯誤: {e}")
        
    finally:
        if driver:
            driver.quit()
            print(f"🔒 瀏覽器已關閉")

def main():
    """主函數"""
    
    print("🔍 MOPS 頁面結構調試")
    
    debug_mops_page_structure()
    
    print(f"\n" + "=" * 80)
    print("📊 調試總結")
    print("=" * 80)
    print("🎯 基於調試結果:")
    print("   1. 檢查 mops_page_source.html 檔案")
    print("   2. 確認頁面是否正確加載")
    print("   3. 找出正確的表單元素選擇器")
    print("   4. 檢查是否需要額外的等待時間")

if __name__ == "__main__":
    main()
