#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證當日排行榜系統修改
"""

import re

def verify_changes():
    """驗證修改是否正確"""
    print("🔍 驗證當日排行榜系統修改")
    print("=" * 50)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查1: 執行排行按鈕樣式
        print("\n1. 檢查執行排行按鈕樣式...")
        if 'background: qlineargradient' in content and '#4A90E2' in content:
            print("   ✅ 執行排行按鈕已改為有質感的藍色漸層")
        else:
            print("   ❌ 執行排行按鈕樣式未正確修改")
        
        # 檢查2: 策略說明按鈕
        print("\n2. 檢查策略說明按鈕...")
        if 'QPushButton("策略說明")' in content:
            print("   ✅ 策略說明按鈕已移除📖符號")
        else:
            print("   ❌ 策略說明按鈕未正確修改")
        
        # 檢查3: 排行榜查詢限制
        print("\n3. 檢查排行榜查詢限制...")
        limit_100_count = content.count('LIMIT 100')
        if limit_100_count >= 3:
            print(f"   ✅ 找到 {limit_100_count} 個 LIMIT 100，排行榜已改為100筆")
        else:
            print(f"   ❌ 只找到 {limit_100_count} 個 LIMIT 100，應該要有3個")
        
        # 檢查4: 移除舊方法
        print("\n4. 檢查是否移除舊的策略管理方法...")
        removed_methods = ['def add_strategy', 'def edit_strategy', 'def delete_strategy']
        found_old_methods = []
        
        for method in removed_methods:
            if method in content:
                found_old_methods.append(method)
        
        if not found_old_methods:
            print("   ✅ 舊的策略管理方法已成功移除")
        else:
            print(f"   ❌ 仍存在舊方法: {found_old_methods}")
        
        # 檢查5: 新增的排行榜方法
        print("\n5. 檢查新增的排行榜方法...")
        new_methods = [
            'def execute_ranking',
            'def get_ranking_data', 
            'def display_ranking_results',
            'def switch_to_result_tab'
        ]
        
        found_new_methods = []
        for method in new_methods:
            if method in content:
                found_new_methods.append(method)
        
        if len(found_new_methods) == len(new_methods):
            print(f"   ✅ 所有新方法都已添加: {found_new_methods}")
        else:
            missing = set(new_methods) - set(found_new_methods)
            print(f"   ❌ 缺少方法: {missing}")
        
        # 檢查6: 排行榜下拉選單
        print("\n6. 檢查排行榜下拉選單...")
        if 'self.ranking_combo = QComboBox()' in content:
            print("   ✅ 排行榜下拉選單已添加")
            
            # 檢查選項
            ranking_options = ['漲停排行榜', '跌停排行榜', '成交量排行榜']
            all_options_found = all(option in content for option in ranking_options)
            
            if all_options_found:
                print("   ✅ 所有排行榜選項都已添加")
            else:
                print("   ❌ 排行榜選項不完整")
        else:
            print("   ❌ 排行榜下拉選單未找到")
        
        # 檢查7: 查詢條件優化
        print("\n7. 檢查查詢條件優化...")
        if 'AND close > prev_close' in content and 'AND close < prev_close' in content:
            print("   ✅ 查詢條件已優化（漲跌條件更精確）")
        else:
            print("   ❌ 查詢條件未優化")
        
        print("\n" + "=" * 50)
        print("✅ 驗證完成！")
        print("\n📋 修改摘要:")
        print("• 執行排行按鈕: 改為有質感的藍色漸層")
        print("• 策略說明按鈕: 移除📖符號節省寬度")
        print("• 排行榜資料: 從50筆改為100筆")
        print("• 查詢條件: 優化漲跌判斷邏輯")
        print("• 移除功能: 新增、編輯、刪除策略方法")
        print("• 新增功能: 完整的排行榜系統")
        
        return True
        
    except FileNotFoundError:
        print("❌ 找不到 O3mh_gui_v21_optimized.py 檔案")
        return False
    except Exception as e:
        print(f"❌ 驗證過程發生錯誤: {e}")
        return False

if __name__ == "__main__":
    verify_changes()
