{"name": "智能多空策略", "description": "基於多重指標的智能買賣信號策略，包含進場和出場邏輯", "parameters": {"ma_short": 10, "ma_medium": 20, "ma_long": 60, "rsi_period": 14, "rsi_oversold": 30, "rsi_overbought": 70, "bb_period": 20, "bb_std": 2, "volume_ratio_threshold": 1.5, "stop_loss_percent": 8, "take_profit_percent": 15, "trailing_stop_percent": 5}, "buy_signals": [{"type": "ma_golden_cross", "short_ma": 10, "long_ma": 20, "weight": 0.3}, {"type": "rsi_oversold_recovery", "rsi_threshold": 30, "recovery_level": 40, "weight": 0.25}, {"type": "bollinger_bounce", "bb_position": "lower", "weight": 0.2}, {"type": "volume_surge", "volume_ratio": 1.5, "weight": 0.25}], "sell_signals": [{"type": "ma_death_cross", "short_ma": 10, "long_ma": 20, "weight": 0.3}, {"type": "rsi_overbought", "rsi_threshold": 70, "weight": 0.25}, {"type": "bollinger_top", "bb_position": "upper", "weight": 0.2}, {"type": "stop_loss", "stop_percent": 8, "weight": 0.25}]}