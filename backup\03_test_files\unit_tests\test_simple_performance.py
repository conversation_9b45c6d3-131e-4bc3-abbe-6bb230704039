#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版新聞爬蟲效能測試（不依賴psutil）
"""

import time
import gc
from datetime import datetime

def simple_performance_test(func, name, *args, **kwargs):
    """簡單的效能測試"""
    print(f"\n🔍 測試 {name}")
    print("-" * 40)
    
    # 強制垃圾回收
    gc.collect()
    
    # 記錄開始時間
    start_time = time.time()
    
    try:
        print(f"⏰ 開始時間: {datetime.now().strftime('%H:%M:%S')}")
        result = func(*args, **kwargs)
        success = True
        print(f"✅ 執行成功")
    except Exception as e:
        result = f"錯誤: {str(e)}"
        success = False
        print(f"❌ 執行失敗: {e}")
    
    # 記錄結束時間
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"⏱️  執行時間: {duration:.2f} 秒")
    print(f"📄 結果: {result}")
    
    # 再次垃圾回收
    gc.collect()
    
    return {
        'name': name,
        'success': success,
        'duration': duration,
        'result': result
    }

def test_quick_crawler():
    """測試極速爬蟲"""
    from news_crawler_lightweight import QuickNewsCrawler
    crawler = QuickNewsCrawler()
    results = crawler.quick_search("2330", days=1)
    return f"找到 {len(results)} 筆台積電新聞"

def test_lightweight_crawler():
    """測試輕量級爬蟲"""
    from news_crawler_lightweight import LightweightNewsCrawler
    crawler = LightweightNewsCrawler()
    return crawler.run_lightweight(ndays=1)

def test_optimized_crawler():
    """測試優化版爬蟲"""
    from news_crawler_optimized import OptimizedNewsCrawler
    crawler = OptimizedNewsCrawler()
    return crawler.run(ndays=1)

def main():
    """主測試函數"""
    print("🚀 新聞爬蟲效能測試")
    print("=" * 50)
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 測試1: 極速爬蟲
    try:
        result = simple_performance_test(test_quick_crawler, "極速爬蟲")
        results.append(result)
    except Exception as e:
        print(f"❌ 極速爬蟲測試失敗: {e}")
        results.append({'name': '極速爬蟲', 'success': False, 'duration': 0, 'result': str(e)})
    
    time.sleep(1)  # 讓系統穩定
    
    # 測試2: 輕量級爬蟲
    try:
        result = simple_performance_test(test_lightweight_crawler, "輕量級爬蟲")
        results.append(result)
    except Exception as e:
        print(f"❌ 輕量級爬蟲測試失敗: {e}")
        results.append({'name': '輕量級爬蟲', 'success': False, 'duration': 0, 'result': str(e)})
    
    time.sleep(1)
    
    # 測試3: 優化版爬蟲
    try:
        result = simple_performance_test(test_optimized_crawler, "優化版爬蟲")
        results.append(result)
    except Exception as e:
        print(f"❌ 優化版爬蟲測試失敗: {e}")
        results.append({'name': '優化版爬蟲', 'success': False, 'duration': 0, 'result': str(e)})
    
    # 顯示比較結果
    print("\n" + "=" * 50)
    print("📊 效能比較結果")
    print("=" * 50)
    
    print(f"{'爬蟲類型':<15} {'狀態':<6} {'時間(秒)':<10} {'結果'}")
    print("-" * 60)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        duration_str = f"{result['duration']:.2f}" if result['success'] else "N/A"
        result_str = str(result['result'])[:30] + "..." if len(str(result['result'])) > 30 else str(result['result'])
        print(f"{result['name']:<15} {status:<6} {duration_str:<10} {result_str}")
    
    # 找出最佳選項
    successful_results = [r for r in results if r['success']]
    
    if successful_results:
        fastest = min(successful_results, key=lambda x: x['duration'])
        
        print(f"\n🏆 效能分析:")
        print(f"  ⚡ 最快速度: {fastest['name']} ({fastest['duration']:.2f}秒)")
        
        # 速度分級
        for result in successful_results:
            if result['duration'] < 1:
                grade = "🚀 極速"
            elif result['duration'] < 5:
                grade = "⚡ 快速"
            elif result['duration'] < 15:
                grade = "🐢 中等"
            else:
                grade = "🐌 較慢"
            
            print(f"  {grade}: {result['name']} ({result['duration']:.2f}秒)")
        
        print(f"\n💡 建議:")
        if fastest['duration'] < 1:
            print(f"  ✅ 推薦使用 {fastest['name']} - 速度極快，系統負擔最小")
        elif fastest['duration'] < 5:
            print(f"  ✅ 推薦使用 {fastest['name']} - 速度快，適合即時查詢")
        else:
            print(f"  ⚠️ 所有爬蟲都較慢，建議檢查網路連線")
        
        print(f"  📱 GUI使用建議: 優先使用最快的爬蟲以改善用戶體驗")
        
    else:
        print(f"\n❌ 所有測試都失敗了")
        print(f"💡 可能原因:")
        print(f"  • 網路連線問題")
        print(f"  • 相關模組未正確安裝")
        print(f"  • API服務暫時不可用")

def test_gui_integration():
    """測試GUI整合效果"""
    print(f"\n🖥️  GUI整合測試")
    print("-" * 40)
    
    try:
        # 模擬GUI中的新聞爬取流程
        print("📱 模擬GUI新聞爬取流程...")
        
        start_time = time.time()
        
        # 步驟1: 快速檢查
        from news_crawler_lightweight import QuickNewsCrawler
        quick_crawler = QuickNewsCrawler()
        quick_results = quick_crawler.quick_search("2330", days=1)
        
        step1_time = time.time() - start_time
        
        if quick_results:
            print(f"✅ 極速搜尋成功: {len(quick_results)} 筆新聞 ({step1_time:.2f}秒)")
            print(f"💡 GUI建議: 使用極速模式，用戶等待時間最短")
        else:
            print(f"⚠️ 極速搜尋無結果，嘗試輕量級模式...")
            
            # 步驟2: 輕量級爬取
            from news_crawler_lightweight import LightweightNewsCrawler
            lite_crawler = LightweightNewsCrawler()
            lite_result = lite_crawler.run_lightweight(ndays=1, stock_code="2330")
            
            step2_time = time.time() - start_time
            print(f"✅ 輕量級爬取完成: {lite_result} ({step2_time:.2f}秒)")
            print(f"💡 GUI建議: 輕量級模式適合作為備用方案")
        
        total_time = time.time() - start_time
        
        if total_time < 2:
            print(f"🎉 GUI體驗: 優秀 (總時間 {total_time:.2f}秒)")
        elif total_time < 5:
            print(f"😊 GUI體驗: 良好 (總時間 {total_time:.2f}秒)")
        else:
            print(f"😐 GUI體驗: 一般 (總時間 {total_time:.2f}秒)")
        
    except Exception as e:
        print(f"❌ GUI整合測試失敗: {e}")

if __name__ == "__main__":
    main()
    
    # 額外測試GUI整合
    test_gui_integration()
    
    print(f"\n" + "=" * 50)
    print("🎯 總結建議:")
    print("1. 優先使用極速爬蟲進行即時查詢")
    print("2. 輕量級爬蟲作為備用方案")
    print("3. 避免在GUI中使用重量級爬蟲")
    print("4. 考慮添加快取機制減少重複請求")
    
    print("\n按 Enter 鍵結束...")
    input()
