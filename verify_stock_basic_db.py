#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證股票基本資料資料庫檔案
確認 .db 檔案正確生成和結構
"""

import os
import sqlite3
import pandas as pd
from stock_basic_info_crawler import StockBasicInfoDatabase

def verify_database_file():
    """驗證資料庫檔案"""
    
    print("🔍 驗證股票基本資料資料庫檔案")
    print("=" * 60)
    
    # 1. 檢查資料庫檔案
    db = StockBasicInfoDatabase()
    db_path = db.db_path
    
    print(f"📁 資料庫路徑: {db_path}")
    
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path) / 1024  # KB
        print(f"✅ 檔案存在")
        print(f"📊 檔案大小: {file_size:.1f} KB")
        
        # 檢查檔案副檔名
        if db_path.endswith('.db'):
            print(f"✅ 檔案格式: SQLite .db 檔案")
        else:
            print(f"⚠️ 檔案格式: {os.path.splitext(db_path)[1]}")
    else:
        print(f"❌ 檔案不存在")
        return False
    
    # 2. 檢查資料庫結構
    print(f"\n📊 檢查資料庫結構:")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 表格數量: {len(tables)}")
        for table in tables:
            print(f"   - {table[0]}")
        
        # 檢查 stock_basic_info 表格結構
        if ('stock_basic_info',) in tables:
            print(f"\n📋 stock_basic_info 表格結構:")
            cursor.execute("PRAGMA table_info(stock_basic_info)")
            columns = cursor.fetchall()
            
            print(f"📊 欄位數量: {len(columns)}")
            for col in columns:
                col_id, col_name, col_type, not_null, default, pk = col
                print(f"   {col_id+1:2d}. {col_name:<25} {col_type:<10} {'NOT NULL' if not_null else ''}")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM stock_basic_info")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 總資料筆數: {total_count}")
        
        if total_count > 0:
            # 檢查各市場別資料
            cursor.execute("""
            SELECT market_type, COUNT(*) 
            FROM stock_basic_info 
            GROUP BY market_type 
            ORDER BY market_type
            """)
            market_stats = cursor.fetchall()
            
            print(f"📊 各市場別統計:")
            for market, count in market_stats:
                print(f"   {market}: {count} 筆")
            
            # 顯示範例資料
            print(f"\n📋 範例資料 (前3筆):")
            cursor.execute("""
            SELECT company_code, company_name, market_type, industry_category 
            FROM stock_basic_info 
            LIMIT 3
            """)
            samples = cursor.fetchall()
            
            for i, (code, name, market, industry) in enumerate(samples, 1):
                print(f"   {i}. {code} {name} ({market}) - {industry}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 資料庫檢查失敗: {e}")
        return False
    
    # 3. 檢查資料庫功能
    print(f"\n🔧 檢查資料庫功能:")
    
    try:
        # 測試統計功能
        stats = db.get_market_stats()
        print(f"✅ 統計功能正常: {stats}")
        
        # 測試計數功能
        count = db.get_data_count()
        print(f"✅ 計數功能正常: {count} 筆")
        
    except Exception as e:
        print(f"❌ 功能測試失敗: {e}")
        return False
    
    print(f"\n🎉 資料庫檔案驗證完成！")
    return True

def check_database_location():
    """檢查資料庫位置是否與其他資料庫一致"""
    
    print(f"\n📁 檢查資料庫位置一致性:")
    
    # 預期的資料庫目錄
    expected_dir = r"D:\Finlab\history\tables"
    
    # 檢查目錄是否存在
    if os.path.exists(expected_dir):
        print(f"✅ 資料庫目錄存在: {expected_dir}")
        
        # 列出目錄中的 .db 檔案
        db_files = [f for f in os.listdir(expected_dir) if f.endswith('.db')]
        
        print(f"📊 目錄中的 .db 檔案:")
        for db_file in sorted(db_files):
            file_path = os.path.join(expected_dir, db_file)
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"   - {db_file:<25} ({file_size:>8.1f} KB)")
        
        # 檢查是否包含 stock_basic_info.db
        if 'stock_basic_info.db' in db_files:
            print(f"✅ stock_basic_info.db 位於正確位置")
        else:
            print(f"❌ stock_basic_info.db 不在預期位置")
            
    else:
        print(f"❌ 資料庫目錄不存在: {expected_dir}")

def main():
    """主函數"""
    
    print("🔍 股票基本資料資料庫檔案驗證工具")
    print("=" * 80)
    
    # 驗證資料庫檔案
    if verify_database_file():
        print(f"\n✅ 資料庫檔案驗證通過")
    else:
        print(f"\n❌ 資料庫檔案驗證失敗")
    
    # 檢查位置一致性
    check_database_location()
    
    print(f"\n💡 說明:")
    print(f"   - 資料庫檔案: stock_basic_info.db")
    print(f"   - 檔案格式: SQLite 資料庫")
    print(f"   - 存放位置: D:/Finlab/history/tables/")
    print(f"   - 與 price.db 在同一目錄")
    print(f"   - 可使用 SQLite 工具開啟查看")

if __name__ == "__main__":
    main()
