#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試現金流量表爬蟲功能
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_cash_flows_crawler():
    """測試現金流量表爬蟲功能"""
    
    print("=" * 80)
    print("🧪 測試現金流量表爬蟲功能")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        print("📦 導入爬蟲模組...")
        from crawler import crawl_cash_flows
        print("✅ 成功導入 crawl_cash_flows 函數")
        
        # 測試日期 (使用最近的財報發布日期)
        test_date = datetime.datetime(2022, 5, 15)  # Q1 財報發布日
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 執行爬蟲測試
        print(f"\n🚀 開始測試現金流量表爬蟲...")
        result = crawl_cash_flows(test_date)
        
        # 檢查結果
        if result is not None and len(result) > 0:
            print(f"✅ 爬蟲測試成功！")
            print(f"📊 獲取資料筆數: {len(result)}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 顯示資料結構
            print(f"\n📋 資料結構:")
            print(f"   索引: {result.index.names}")
            print(f"   形狀: {result.shape}")
            
            # 顯示前幾筆資料
            print(f"\n📊 前5筆資料範例:")
            print(result.head())
            
            # 顯示主要現金流量項目
            main_cash_flow_items = [
                '本期稅前淨利（淨損）',
                '營業活動之淨現金流入（流出）',
                '投資活動之淨現金流入（流出）',
                '籌資活動之淨現金流入（流出）',
                '期末現金及約當現金餘額'
            ]
            
            print(f"\n💰 主要現金流量項目檢查:")
            for item in main_cash_flow_items:
                if item in result.columns:
                    non_null_count = result[item].notna().sum()
                    percentage = (non_null_count / len(result)) * 100
                    print(f"   ✅ {item:<25} 有數值: {non_null_count:>6} ({percentage:>5.1f}%)")
                else:
                    print(f"   ❌ {item:<25} 欄位不存在")
            
            return True
            
        else:
            print(f"⚠️ 爬蟲測試返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        print(f"請確保 finlab/crawler.py 存在且包含 crawl_cash_flows 函數")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cash_flows_helper_functions():
    """測試現金流量表相關的輔助函數"""
    
    print(f"\n🔧 測試輔助函數...")
    
    try:
        from crawler import (
            get_financial_year_season,
            get_financial_report_date,
            load_existing_cash_flows,
            should_update_cash_flows
        )
        
        # 測試日期解析
        test_dates = [
            datetime.datetime(2022, 3, 15),   # Q4 (前一年)
            datetime.datetime(2022, 5, 15),   # Q1
            datetime.datetime(2022, 8, 14),   # Q2
            datetime.datetime(2022, 11, 14),  # Q3
        ]
        
        print(f"📅 測試日期解析:")
        for date in test_dates:
            year, season = get_financial_year_season(date)
            report_date = get_financial_report_date(year, season)
            print(f"   {date.strftime('%Y-%m-%d')} -> {year}年第{season}季 (發布日: {report_date})")
        
        # 測試載入現有資料
        print(f"\n📖 測試載入現有資料:")
        existing_data = load_existing_cash_flows()
        if existing_data is not None:
            print(f"   ✅ 成功載入現有資料: {len(existing_data)} 筆")
        else:
            print(f"   ⚠️ 未找到現有資料")
        
        # 測試更新檢查
        print(f"\n🔍 測試更新檢查:")
        need_update = should_update_cash_flows(existing_data, 2022, 1)
        print(f"   2022年第1季是否需要更新: {need_update}")
        
        print(f"✅ 輔助函數測試完成")
        return True
        
    except ImportError as e:
        print(f"❌ 導入輔助函數失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 輔助函數測試失敗: {e}")
        return False

def test_auto_update_integration():
    """測試與 auto_update.py 的整合"""
    
    print(f"\n🔗 測試 auto_update.py 整合...")
    
    try:
        # 檢查 auto_update.py 是否包含現金流量表任務
        auto_update_path = os.path.join(current_dir, 'auto_update.py')
        
        if os.path.exists(auto_update_path):
            with open(auto_update_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'crawl_cash_flows' in content:
                print(f"   ✅ auto_update.py 已包含 crawl_cash_flows 導入")
            else:
                print(f"   ❌ auto_update.py 未包含 crawl_cash_flows 導入")
            
            if "'cash_flows'" in content:
                print(f"   ✅ auto_update.py 已包含 cash_flows 任務")
            else:
                print(f"   ⚠️ auto_update.py 的 cash_flows 任務被註解")
            
            return True
        else:
            print(f"   ❌ 未找到 auto_update.py 檔案")
            return False
            
    except Exception as e:
        print(f"❌ 整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    
    print("🧪 現金流量表爬蟲功能測試")
    print("=" * 80)
    
    test_results = []
    
    # 測試1: 輔助函數
    print("📋 測試1: 輔助函數")
    result1 = test_cash_flows_helper_functions()
    test_results.append(("輔助函數", result1))
    
    # 測試2: 爬蟲功能 (簡化版本，避免實際網路請求)
    print(f"\n📋 測試2: 爬蟲功能檢查")
    try:
        from crawler import crawl_cash_flows
        print(f"   ✅ crawl_cash_flows 函數存在")
        test_results.append(("爬蟲函數", True))
    except ImportError:
        print(f"   ❌ crawl_cash_flows 函數不存在")
        test_results.append(("爬蟲函數", False))
    
    # 測試3: auto_update 整合
    print(f"\n📋 測試3: auto_update 整合")
    result3 = test_auto_update_integration()
    test_results.append(("auto_update整合", result3))
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 測試結果總結:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print(f"🎉 所有測試通過！現金流量表爬蟲功能已準備就緒")
        print(f"\n📝 使用方法:")
        print(f"   1. 啟用 auto_update.py 中的 cash_flows 任務")
        print(f"   2. 執行: python auto_update.py cash_flows")
        print(f"   3. 或在 update_tasks 中取消註解 cash_flows 行")
    else:
        print(f"⚠️ 部分測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
