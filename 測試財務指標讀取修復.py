#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財務指標讀取修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_info_reading():
    """測試財務指標讀取修復"""
    print("🧪 測試財務指標讀取修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox, QTextEdit
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試財務指標讀取修復")
        dialog.setFixedSize(1000, 900)
        
        layout = QVBoxLayout(dialog)
        
        # 測試結果顯示
        results_group = QGroupBox("📊 財務指標讀取測試結果")
        results_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        results_layout = QVBoxLayout(results_group)
        
        # 測試股票列表
        test_stocks = ['2301', '2330', '2317', '1101', '8021', '6669']
        
        results_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; text-align: center;">📊 財務指標讀取測試</h3>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #e8f5e8; border-radius: 6px; border-left: 4px solid #4caf50;">
                <h4 style="color: #2e7d32; margin: 0 0 8px 0;">✅ 修復內容：</h4>
                <ul style="margin: 0; padding-left: 20px; color: #2e7d32; font-size: 11px;">
                    <li><strong>優先讀取pe_data.db：</strong>專門的財務指標資料庫</li>
                    <li><strong>備用計算方案：</strong>從financial_statements.db計算指標</li>
                    <li><strong>智能欄位匹配：</strong>自動識別不同資料庫的欄位名稱</li>
                    <li><strong>真實數據優先：</strong>不再使用hardcode估算值</li>
                    <li><strong>透明顯示：</strong>無數據時顯示N/A</li>
                </ul>
            </div>
        </div>
        """
        
        results_label = QLabel()
        results_label.setTextFormat(Qt.TextFormat.RichText)
        results_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        results_label.setWordWrap(True)
        results_label.setText(results_text)
        results_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        results_layout.addWidget(results_label)
        
        # 實際測試結果
        test_results_text = QTextEdit()
        test_results_text.setMaximumHeight(300)
        test_results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #333333;
            }
        """)
        
        # 執行實際測試
        test_output = "🔍 財務指標讀取測試結果:\n\n"
        
        for stock_code in test_stocks:
            test_output += f"📊 {stock_code}:\n"
            try:
                financial_info = gui.get_real_financial_info(stock_code)
                for key, value in financial_info.items():
                    test_output += f"  {key}: {value}\n"
                test_output += f"  ✅ 成功讀取\n\n"
            except Exception as e:
                test_output += f"  ❌ 讀取失敗: {e}\n\n"
        
        test_results_text.setPlainText(test_output)
        results_layout.addWidget(test_results_text)
        layout.addWidget(results_group)
        
        # 技術說明
        technical_group = QGroupBox("🔧 技術實現說明")
        technical_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        technical_layout = QVBoxLayout(technical_group)

        technical_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h4 style="color: #2c3e50; margin: 0 0 10px 0;">讀取策略：</h4>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 4px; border-left: 4px solid #2196f3;">
                <p style="margin: 0; font-size: 11px; color: #0d47a1;">
                    <strong>1. 優先級資料庫：</strong><br>
                    • pe_data.db (專門財務指標)<br>
                    • tpex_financial_ratios.db (櫃買中心財務比率)<br>
                    • 自動偵測表格結構和欄位名稱
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #fff3e0; border-radius: 4px; border-left: 4px solid #ff9800;">
                <p style="margin: 0; font-size: 11px; color: #e65100;">
                    <strong>2. 備用計算方案：</strong><br>
                    • 從financial_statements.db獲取財報數據<br>
                    • 計算EPS、ROE、ROA等基本指標<br>
                    • 使用合理的股數估算（需改進）
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #f3e5f5; border-radius: 4px; border-left: 4px solid #9c27b0;">
                <p style="margin: 0; font-size: 11px; color: #4a148c;">
                    <strong>3. 智能欄位匹配：</strong><br>
                    • 自動識別pe、本益比、PE等欄位<br>
                    • 自動識別pb、股價淨值比、PB等欄位<br>
                    • 自動識別dividend_yield、殖利率等欄位<br>
                    • 自動識別eps、每股盈餘、EPS等欄位
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #4caf50;">
                <p style="margin: 0; font-size: 11px; color: #2e7d32;">
                    <strong>4. 資料品質保證：</strong><br>
                    • 檢查數據有效性（非空、非N/A）<br>
                    • 記錄數據來源和獲取狀態<br>
                    • 無數據時誠實顯示N/A<br>
                    • 避免誤導性的假數據
                </p>
            </div>
        </div>
        """

        technical_label = QLabel()
        technical_label.setTextFormat(Qt.TextFormat.RichText)
        technical_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        technical_label.setWordWrap(True)
        technical_label.setText(technical_text)
        technical_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        technical_layout.addWidget(technical_label)
        layout.addWidget(technical_group)
        
        print("✅ 對話框創建成功")
        print("📋 修復內容：")
        print("  1. 優先從pe_data.db讀取財務指標")
        print("  2. 智能欄位匹配和數據提取")
        print("  3. 備用計算方案確保數據可用性")
        print("  4. 真實數據優先，避免估算值")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 財務指標讀取修復測試")
    print("=" * 50)
    
    result = test_financial_info_reading()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 財務指標讀取功能已優化")
        print("📊 現在可以從pe_data.db等真實資料庫讀取指標")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
