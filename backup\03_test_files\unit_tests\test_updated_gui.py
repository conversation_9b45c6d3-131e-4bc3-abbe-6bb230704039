#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新後的GUI界面
"""

import sys
from PyQt6.QtWidgets import QApplication
from auto_rule_discovery_gui import AutoRuleDiscoveryGUI

def test_gui():
    """測試GUI功能"""
    print("🧪 測試更新後的GUI界面")
    print("="*50)
    
    try:
        app = QApplication(sys.argv)
        
        # 創建GUI實例
        window = AutoRuleDiscoveryGUI()
        
        print("✅ GUI創建成功")
        print("\n🆕 新增功能:")
        print("• 左側面板添加垂直捲軸")
        print("• 策略選擇複選框（RSI、MACD、布林通道、移動平均）")
        print("• 三重確認選項")
        print("• 最少信號數量設定")
        print("• 分析時間範圍選擇")
        print("• 快速設定按鈕（保守、平衡、積極）")
        
        print("\n📱 界面改進:")
        print("• 左側面板支援捲軸，可容納更多控制項")
        print("• 垂直空間利用更充分")
        print("• 參數設定更豐富和靈活")
        
        # 設置窗口屬性
        window.setWindowTitle("🔍 交易規則發現系統 - 更新版")
        window.setGeometry(200, 200, 1400, 900)
        
        # 顯示窗口
        window.show()
        print("✅ GUI顯示成功")
        
        print("\n🎯 使用說明:")
        print("1. 左側面板現在有捲軸，可以滾動查看所有選項")
        print("2. 可以選擇要使用的策略組合")
        print("3. 可以啟用三重確認提高成功率")
        print("4. 可以使用快速設定按鈕快速配置參數")
        print("5. 所有原有功能保持不變")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = test_gui()
    if success:
        print("\n🎉 GUI更新測試成功！")
        print("現在可以啟動完整的GUI進行使用")
    else:
        print("\n❌ GUI更新測試失敗")
