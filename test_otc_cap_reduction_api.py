#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 OTC 減資 API 是否正常工作
"""

import requests
import json
import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_otc_cap_reduction_api():
    """測試櫃買中心減資 API"""
    
    print("=" * 80)
    print("🔍 測試櫃買中心減資 API")
    print("=" * 80)
    
    # 準備測試日期範圍 (最近30天)
    end_date = datetime.datetime.now()
    start_date = end_date - datetime.timedelta(days=30)
    
    # 轉換為民國年格式
    start_year = start_date.year - 1911
    start_month = start_date.month
    start_day = start_date.day
    start_datestr = f"{start_year:02d}/{start_month:02d}/{start_day:02d}"
    
    end_year = end_date.year - 1911
    end_month = end_date.month
    end_day = end_date.day
    end_datestr = f"{end_year:02d}/{end_month:02d}/{end_day:02d}"
    
    print(f"📅 測試日期範圍: {start_datestr} ~ {end_datestr}")
    print(f"   (西元: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')})")
    
    # 測試原始 URL
    original_url = f"https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php?l=zh-tw&d={start_datestr}&ed={end_datestr}"
    print(f"\n🔗 測試原始 URL:")
    print(f"   {original_url}")
    
    try:
        response = requests.get(original_url, timeout=10, verify=False)
        print(f"📊 回應狀態: {response.status_code}")
        print(f"📊 回應長度: {len(response.text)} 字元")
        print(f"📊 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        
        if response.status_code == 200:
            # 檢查回應內容
            content = response.text[:500]
            print(f"\n📋 回應內容前500字元:")
            print(content)
            
            # 嘗試解析 JSON
            try:
                json_data = json.loads(response.text)
                print(f"\n✅ JSON 解析成功")
                print(f"📊 JSON 鍵: {list(json_data.keys())}")
                
                # 檢查資料結構
                if 'tables' in json_data:
                    print(f"📊 tables 結構: {type(json_data['tables'])}")
                    if json_data['tables']:
                        print(f"📊 第一個表格鍵: {list(json_data['tables'][0].keys())}")
                        if 'data' in json_data['tables'][0]:
                            data_count = len(json_data['tables'][0]['data'])
                            print(f"📊 資料筆數: {data_count}")
                
                if 'aaData' in json_data:
                    print(f"📊 aaData 資料筆數: {len(json_data['aaData'])}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失敗: {e}")
                
        else:
            print(f"❌ API 請求失敗，狀態碼: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 網路請求失敗: {e}")
    
    # 測試替代 URL 格式
    print(f"\n🔗 測試替代 URL 格式:")
    
    # 格式1: 不同的日期格式
    alt_start = start_date.strftime('%Y%m%d')
    alt_end = end_date.strftime('%Y%m%d')
    alt_url1 = f"https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php?l=zh-tw&startDate={alt_start}&endDate={alt_end}"
    print(f"   格式1: {alt_url1}")
    
    try:
        response = requests.get(alt_url1, timeout=10, verify=False)
        print(f"   狀態: {response.status_code}")
        if response.status_code == 200:
            try:
                json_data = json.loads(response.text)
                print(f"   ✅ JSON 解析成功，鍵: {list(json_data.keys())}")
            except:
                print(f"   ❌ JSON 解析失敗")
    except:
        print(f"   ❌ 請求失敗")
    
    # 格式2: 簡化參數
    alt_url2 = f"https://www.tpex.org.tw/web/stock/exright/revivt/revivt_result.php"
    params = {
        'l': 'zh-tw',
        'd': start_datestr,
        'ed': end_datestr
    }
    print(f"   格式2: {alt_url2} with params {params}")
    
    try:
        response = requests.get(alt_url2, params=params, timeout=10, verify=False)
        print(f"   狀態: {response.status_code}")
        if response.status_code == 200:
            try:
                json_data = json.loads(response.text)
                print(f"   ✅ JSON 解析成功，鍵: {list(json_data.keys())}")
            except:
                print(f"   ❌ JSON 解析失敗")
    except:
        print(f"   ❌ 請求失敗")
    
    # 測試新的可能 URL 路徑
    print(f"\n🔗 測試新的可能 URL 路徑:")

    # 可能的新路徑
    new_paths = [
        "https://www.tpex.org.tw/web/stock/exright/revivt/revivt.php",
        "https://www.tpex.org.tw/web/stock/exright/revivt.php",
        "https://www.tpex.org.tw/web/stock/exright/revivt_result.php",
        "https://www.tpex.org.tw/openapi/v1/tpex_mainboard_exright_revivt",
        "https://www.tpex.org.tw/api/stock/exright/revivt",
    ]

    for i, url in enumerate(new_paths, 1):
        print(f"   路徑{i}: {url}")
        try:
            # 嘗試不同的參數格式
            test_params = [
                {'l': 'zh-tw', 'd': start_datestr, 'ed': end_datestr},
                {'startDate': start_datestr, 'endDate': end_datestr},
                {'start': start_datestr, 'end': end_datestr},
                {}  # 無參數
            ]

            for j, params in enumerate(test_params):
                try:
                    response = requests.get(url, params=params, timeout=10, verify=False)
                    if response.status_code == 200:
                        content_type = response.headers.get('Content-Type', '')
                        if 'json' in content_type.lower():
                            try:
                                json_data = json.loads(response.text)
                                print(f"     ✅ 參數組{j+1}: JSON 成功，鍵: {list(json_data.keys())}")
                                break
                            except:
                                print(f"     ⚠️ 參數組{j+1}: 200 但 JSON 解析失敗")
                        elif response.text and not response.text.startswith('<!DOCTYPE'):
                            print(f"     ⚠️ 參數組{j+1}: 200 但非 HTML，長度: {len(response.text)}")
                    else:
                        print(f"     ❌ 參數組{j+1}: 狀態 {response.status_code}")
                except:
                    continue
            else:
                print(f"     ❌ 所有參數組都失敗")
        except:
            print(f"     ❌ 連接失敗")

    print(f"\n✅ API 測試完成")

if __name__ == "__main__":
    test_otc_cap_reduction_api()
