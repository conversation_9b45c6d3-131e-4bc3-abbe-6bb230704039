#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab 爬蟲系統 GUI - 最終可用版本

簡潔、實用、功能完整的GUI界面
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime, timedelta
import threading
import traceback
import os
import pandas as pd
import pickle

class FinlabCrawlerGUI:
    """Finlab 爬蟲系統GUI - 增量更新版本"""

    def __init__(self, root):
        self.root = root
        self.crawler_funcs = None
        self.data_info = {}  # 存儲各資料表的資訊
        self.setup_ui()
        self.init_crawlers()
        self.check_existing_data()
    
    def setup_ui(self):
        """設置界面"""
        self.root.title("Finlab 爬蟲系統 - 增量更新版")
        self.root.geometry("1400x800")

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        # 設定主框架的行權重，讓日誌區域可以擴展
        main_frame.rowconfigure(3, weight=1)  # 日誌區域所在的行

        # 標題
        title = ttk.Label(main_frame, text="Finlab 爬蟲系統 - 增量更新版", font=('Arial', 16, 'bold'))
        title.grid(row=0, column=0, pady=(0, 20))

        # 資料狀態顯示區域
        self.create_data_status_area(main_frame)

        # 日誌區域
        self.create_log_area(main_frame)

        # 進度條
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        self.progress.grid_remove()

    def check_existing_data(self):
        """檢查現有資料檔案"""
        self.data_info = {}

        # 定義資料檔案對應關係
        data_files = {
            'price.pkl': '股價資料',
            'bargin_report.pkl': '融資融券',
            'pe.pkl': '本益比',
            'monthly_report.pkl': '月報',
            'balance_sheet.pkl': '資產負債表',
            'income_sheet.pkl': '損益表',
            'income_sheet_cumulate.pkl': '損益表(累計)',
            'cash_flows.pkl': '現金流量表',
            'benchmark.pkl': '基準指數',
            'forign_hold_ratio.pkl': '外資持股比例',
            'twse_divide_ratio.pkl': '上市除權息',
            'otc_divide_ratio.pkl': '上櫃除權息',
            'twse_cap_reduction.pkl': '上市減資',
            'otc_cap_reduction.pkl': '上櫃減資'
        }

        for filename, display_name in data_files.items():
            filepath = os.path.join(r'D:\Finlab\history\tables', filename)

            if os.path.exists(filepath):
                try:
                    # 讀取pkl檔案獲取日期範圍
                    data = None

                    # 嘗試多種方式讀取pickle檔案，優先使用pandas避免numpy警告
                    try:
                        # 首先嘗試使用pandas讀取，這樣可以避免numpy警告
                        data = pd.read_pickle(filepath)
                    except Exception as e1:
                        # 如果pandas失敗，嘗試標準pickle但忽略警告
                        try:
                            import warnings
                            with warnings.catch_warnings():
                                warnings.simplefilter("ignore")  # 忽略所有警告
                                with open(filepath, 'rb') as f:
                                    data = pickle.load(f)
                        except (AttributeError, ModuleNotFoundError, ImportError) as e2:
                            # 如果還是失敗，嘗試使用不同的編碼
                            try:
                                with warnings.catch_warnings():
                                    warnings.simplefilter("ignore")
                                    with open(filepath, 'rb') as f:
                                        data = pickle.load(f, encoding='latin1')
                            except Exception as e3:
                                # 最後嘗試：忽略錯誤
                                try:
                                    with warnings.catch_warnings():
                                        warnings.simplefilter("ignore")
                                        with open(filepath, 'rb') as f:
                                            data = pickle.load(f, errors='ignore')
                                except:
                                    raise e1  # 拋出原始錯誤

                    if data is not None and hasattr(data, 'index') and len(data.index) > 0:
                        # 獲取日期範圍
                        if hasattr(data.index, 'levels') and len(data.index.levels) > 1:
                            # MultiIndex的情況，通常是 (stock_id, date)
                            # 檢查哪個層級是日期
                            date_level = None
                            for i, level_values in enumerate(data.index.levels):
                                if hasattr(level_values, 'dtype') and 'datetime' in str(level_values.dtype):
                                    date_level = i
                                    break

                            if date_level is not None:
                                dates = data.index.get_level_values(date_level)
                            else:
                                # 假設第二個層級是日期
                                dates = data.index.get_level_values(1)
                        else:
                            # 單層索引，檢查是否為日期類型
                            if hasattr(data.index, 'dtype') and 'datetime' in str(data.index.dtype):
                                dates = data.index
                            else:
                                # 嘗試解析為日期
                                try:
                                    dates = pd.to_datetime(data.index)
                                except:
                                    dates = data.index

                        if len(dates) > 0:
                            first_date = dates.min()
                            last_date = dates.max()

                            # 處理日期格式
                            try:
                                if hasattr(first_date, 'strftime') and hasattr(last_date, 'strftime'):
                                    first_date_str = first_date.strftime('%Y-%m-%d')
                                    last_date_str = last_date.strftime('%Y-%m-%d')
                                else:
                                    # 嘗試轉換為日期
                                    first_date = pd.to_datetime(first_date)
                                    last_date = pd.to_datetime(last_date)
                                    first_date_str = first_date.strftime('%Y-%m-%d')
                                    last_date_str = last_date.strftime('%Y-%m-%d')

                                if first_date_str == last_date_str:
                                    date_range_str = first_date_str
                                else:
                                    date_range_str = f"{first_date_str} 至 {last_date_str}"
                            except:
                                date_range_str = f"{str(first_date)} 至 {str(last_date)}"
                                last_date_str = str(last_date)
                        else:
                            date_range_str = "無資料"
                            last_date_str = "無資料"
                    else:
                        date_range_str = "無資料"
                        last_date_str = "無資料"

                    self.data_info[filename] = {
                        'display_name': display_name,
                        'date_range': date_range_str,
                        'last_date': last_date_str,
                        'exists': True,
                        'filepath': filepath
                    }
                except Exception as e:
                    self.data_info[filename] = {
                        'display_name': display_name,
                        'date_range': f"讀取錯誤: {str(e)}",
                        'last_date': f"讀取錯誤: {str(e)}",
                        'exists': True,
                        'filepath': filepath
                    }
            else:
                self.data_info[filename] = {
                    'display_name': display_name,
                    'date_range': "檔案不存在",
                    'last_date': "檔案不存在",
                    'exists': False,
                    'filepath': filepath
                }

        # 更新UI顯示
        self.update_data_status_display()

    def open_file_directory(self, filepath):
        """開啟檔案所在目錄"""
        import subprocess
        import platform

        try:
            directory = os.path.dirname(filepath)

            if platform.system() == "Windows":
                # Windows系統
                subprocess.run(['explorer', directory])
            elif platform.system() == "Darwin":
                # macOS系統
                subprocess.run(['open', directory])
            else:
                # Linux系統
                subprocess.run(['xdg-open', directory])

            self.log_message(f"已開啟目錄：{directory}")
        except Exception as e:
            self.log_message(f"✗ 無法開啟目錄：{str(e)}")

    def update_data_status_display(self):
        """更新資料狀態顯示"""
        # 清空現有行（保留標題行）
        for widget in self.table_frame.winfo_children():
            if int(widget.grid_info()['row']) > 0:
                widget.destroy()

        today = datetime.now().strftime('%Y-%m-%d')
        row = 1

        # 存儲日期輸入框的引用
        self.date_entries = {}

        for filename, info in self.data_info.items():
            # 計算建議更新範圍
            if info['exists'] and info['last_date'] != "檔案不存在" and "錯誤" not in info['last_date']:
                try:
                    last_date = datetime.strptime(info['last_date'], '%Y-%m-%d')
                    start_date = (last_date + timedelta(days=1)).strftime('%Y-%m-%d')
                except:
                    start_date = "2023-01-01"
            else:
                start_date = "2023-01-01"

            # 創建表格行
            # 欄位1：檔案名稱 - 顯示pkl檔案全名
            file_label = ttk.Label(self.table_frame, text=filename,
                                 relief='solid', borderwidth=1, padding=5)
            file_label.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=1, pady=1)

            # 欄位2：檔案說明 - 顯示中文說明
            display_name = info.get('display_name', filename)
            desc_label = ttk.Label(self.table_frame, text=display_name,
                                 relief='solid', borderwidth=1, padding=5)
            desc_label.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=1, pady=1)

            # 欄位3：資料範圍 - 顯示現有資料的日期範圍
            date_range = info.get('date_range', info['last_date'])
            date_label = ttk.Label(self.table_frame, text=date_range,
                                 relief='solid', borderwidth=1, padding=5)
            date_label.grid(row=row, column=2, sticky=(tk.W, tk.E), padx=1, pady=1)

            # 欄位4：更新範圍 - 可編輯的日期輸入框
            range_frame = ttk.Frame(self.table_frame, relief='solid', borderwidth=1)
            range_frame.grid(row=row, column=3, sticky=(tk.W, tk.E), padx=1, pady=1)

            # 開始日期輸入框
            start_var = tk.StringVar(value=start_date)
            start_entry = ttk.Entry(range_frame, textvariable=start_var, width=12, font=('Arial', 9))
            start_entry.pack(side='left', padx=3)

            ttk.Label(range_frame, text="至", font=('Arial', 9)).pack(side='left', padx=4)

            # 結束日期輸入框
            end_var = tk.StringVar(value=today)
            end_entry = ttk.Entry(range_frame, textvariable=end_var, width=12, font=('Arial', 9))
            end_entry.pack(side='left', padx=3)

            # 存儲日期變數
            self.date_entries[filename] = {
                'start': start_var,
                'end': end_var
            }

            # 欄位5：操作按鈕 - 更新按鈕和開啟目錄按鈕
            button_frame = ttk.Frame(self.table_frame, relief='solid', borderwidth=1)
            button_frame.grid(row=row, column=4, sticky=(tk.W, tk.E), padx=1, pady=1)

            # 更新按鈕
            update_btn = ttk.Button(button_frame, text="更新",
                                  command=lambda f=filename: self.update_single_data_with_custom_range(f))
            update_btn.pack(side='left', padx=2)

            # 開啟目錄按鈕
            open_dir_btn = ttk.Button(button_frame, text="開啟目錄",
                                    command=lambda p=info['filepath']: self.open_file_directory(p))
            open_dir_btn.pack(side='left', padx=2)

            row += 1

    def create_data_status_area(self, parent):
        """創建資料狀態顯示區域"""
        # 資料狀態框架
        status_frame = ttk.LabelFrame(parent, text="資料檔案狀態", padding="10")
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 創建表格框架
        table_frame = ttk.Frame(status_frame)
        table_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 創建表格標題
        headers = ['檔案名稱', '檔案說明', '資料範圍', '更新範圍', '操作']
        for i, header in enumerate(headers):
            label = ttk.Label(table_frame, text=header, font=('Arial', 10, 'bold'),
                            relief='solid', borderwidth=1, background='lightgray')
            label.grid(row=0, column=i, sticky=(tk.W, tk.E), padx=1, pady=1)

        # 設置列權重 - 按照您的要求調整寬度比例
        table_frame.columnconfigure(0, weight=2, minsize=100)  # 檔案名稱 - pkl檔案全名
        table_frame.columnconfigure(1, weight=2, minsize=100)  # 檔案說明 - 中文說明
        table_frame.columnconfigure(2, weight=1, minsize=120)  # 資料範圍 - 固定較小寬度
        table_frame.columnconfigure(3, weight=4, minsize=300)  # 更新範圍 - 更寬以顯示輸入框
        table_frame.columnconfigure(4, weight=2, minsize=150)  # 操作按鈕

        self.table_frame = table_frame
        status_frame.columnconfigure(0, weight=1)

        # 全部更新按鈕
        button_frame = ttk.Frame(status_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0))

        ttk.Button(button_frame, text="全部更新", command=self.update_all_data).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="重新檢查", command=self.check_existing_data).pack(side='left')

    def update_single_data_with_custom_range(self, filename):
        """使用自定義日期範圍更新單個資料檔案"""
        if filename not in self.data_info or filename not in self.date_entries:
            return

        info = self.data_info[filename]

        # 獲取用戶輸入的日期範圍
        start_date = self.date_entries[filename]['start'].get()
        end_date = self.date_entries[filename]['end'].get()

        # 驗證日期格式
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            self.log_message(f"✗ 日期格式錯誤，請使用 YYYY-MM-DD 格式")
            return

        # 執行更新
        self.log_message(f"開始更新 {filename} ({start_date} 至 {end_date})")

        def update_thread():
            try:
                self.progress.grid()
                self.progress.start()

                # 根據檔案類型選擇對應的爬蟲函數
                crawler_map = {
                    'price.pkl': 'crawl_price',
                    'bargin_report.pkl': 'crawl_bargin',
                    'pe.pkl': 'crawl_pe',
                    'monthly_report.pkl': 'crawl_monthly_report',
                    'balance_sheet.pkl': 'crawl_balance_sheet',
                    'income_sheet.pkl': 'crawl_income_sheet',
                    'cash_flows.pkl': 'crawl_cash_flows',
                    'twse_divide_ratio.pkl': 'crawl_twse_divide_ratio',
                    'otc_divide_ratio.pkl': 'crawl_otc_divide_ratio',
                    'twse_cap_reduction.pkl': 'crawl_twse_cap_reduction',
                    'otc_cap_reduction.pkl': 'crawl_otc_cap_reduction'
                }

                if filename in crawler_map:
                    func_name = crawler_map[filename]
                    if func_name in self.crawler_funcs:
                        crawler_func = self.crawler_funcs[func_name]

                        # 調用爬蟲函數
                        if func_name in ['crawl_balance_sheet', 'crawl_income_sheet', 'crawl_cash_flows']:
                            # 財務報表需要特殊處理
                            result = self.crawl_financial_data(crawler_func, start_date, end_date)
                        else:
                            # 一般資料爬取
                            result = self.crawl_general_data(crawler_func, start_date, end_date)

                        if result is not None and not result.empty:
                            self.log_message(f"✓ {filename} 更新完成，獲取 {len(result)} 筆資料")
                        else:
                            self.log_message(f"⚠️ {filename} 更新完成，但無新資料")

                        # 重新檢查資料狀態
                        self.root.after(0, self.check_existing_data)
                    else:
                        self.log_message(f"✗ 找不到對應的爬蟲函數: {func_name}")
                else:
                    self.log_message(f"✗ 未知的檔案類型: {filename}")

            except Exception as e:
                self.log_message(f"✗ 更新 {filename} 時發生錯誤: {str(e)}")
            finally:
                self.root.after(0, lambda: (self.progress.stop(), self.progress.grid_remove()))

        threading.Thread(target=update_thread, daemon=True).start()

    def update_single_data(self, filename):
        """更新單個資料檔案 - 使用 auto_update.py 的方式"""
        if filename not in self.data_info:
            return

        info = self.data_info[filename]

        # 執行更新
        self.log_message(f"🔄 開始更新 {info['display_name']}...")

        def update_thread():
            try:
                self.progress.grid()
                self.progress.start()

                # 根據檔案類型選擇對應的表格名稱和爬蟲函數
                table_map = {
                    'price.pkl': ('price', 'crawl_price', 'date_range'),
                    'bargin_report.pkl': ('bargin_report', 'crawl_bargin', 'date_range'),
                    'pe.pkl': ('pe', 'crawl_pe', 'date_range'),
                    'monthly_report.pkl': ('monthly_report', 'crawl_monthly_report', 'month_range'),
                    'twse_divide_ratio.pkl': ('twse_divide_ratio', 'crawl_twse_divide_ratio', None),
                    'otc_divide_ratio.pkl': ('otc_divide_ratio', 'crawl_otc_divide_ratio', None),
                    'twse_cap_reduction.pkl': ('twse_cap_reduction', 'crawl_twse_cap_reduction', None),
                    'otc_cap_reduction.pkl': ('otc_cap_reduction', 'crawl_otc_cap_reduction', None)
                }

                if filename not in table_map:
                    self.log_message(f"❌ 不支援的檔案類型: {filename}")
                    return

                table_name, func_name, time_range_func = table_map[filename]

                # 檢查必要的函數是否存在
                required_funcs = [func_name, 'table_date_range', 'update_table', 'to_pickle', 'commit']
                if time_range_func:
                    required_funcs.append(time_range_func)

                missing_funcs = [f for f in required_funcs if f not in self.crawler_funcs]
                if missing_funcs:
                    self.log_message(f"❌ 缺少必要函數: {missing_funcs}")
                    return

                # 使用 auto_update 的邏輯
                self.auto_update_table(table_name, func_name, time_range_func)

            except Exception as e:
                self.log_message(f"❌ 更新 {info['display_name']} 時發生錯誤: {str(e)}")
                import traceback
                self.log_message(f"詳細錯誤: {traceback.format_exc()}")
            finally:
                self.root.after(0, lambda: (self.progress.stop(), self.progress.grid_remove()))

        threading.Thread(target=update_thread, daemon=True).start()

    def auto_update_table(self, table_name, crawl_func_name, time_range_func_name):
        """實現 auto_update.py 的邏輯"""
        try:
            crawl_function = self.crawler_funcs[crawl_func_name]

            # 檢查爬蟲函數的參數
            from inspect import signature
            sig = signature(crawl_function)

            if len(sig.parameters) != 0:
                # 需要日期參數的爬蟲函數
                self.log_message(f"📅 獲取 {table_name} 表格的日期範圍...")

                first_date, last_date = self.crawler_funcs['table_date_range'](table_name)
                self.log_message(f"   表格日期範圍: {first_date} 至 {last_date}")

                if time_range_func_name and time_range_func_name in self.crawler_funcs:
                    time_range_func = self.crawler_funcs[time_range_func_name]
                    dates = time_range_func(last_date, datetime.now())

                    if dates:
                        self.log_message(f"📈 需要更新的日期數量: {len(dates)}")
                        self.log_message(f"   更新範圍: {dates[0]} 至 {dates[-1]}")

                        # 執行更新
                        self.crawler_funcs['update_table'](table_name, crawl_function, dates)
                        self.log_message(f"✅ {table_name} 表格更新完成")
                    else:
                        self.log_message(f"✅ {table_name} 已是最新，無需更新")
                else:
                    self.log_message(f"❌ 找不到時間範圍函數: {time_range_func_name}")
            else:
                # 不需要日期參數的爬蟲函數
                self.log_message(f"🔄 執行完整爬取 {table_name}...")
                df = crawl_function()
                self.crawler_funcs['to_pickle'](df, table_name)
                self.log_message(f"✅ {table_name} 完整爬取完成")

            # 提交更新
            self.crawler_funcs['commit']()
            self.log_message(f"💾 {table_name} 資料已提交保存")

            # 重新檢查資料狀態
            self.root.after(0, self.check_existing_data)

        except Exception as e:
            self.log_message(f"❌ auto_update_table 錯誤: {str(e)}")
            import traceback
            self.log_message(f"詳細錯誤: {traceback.format_exc()}")

    def update_all_data(self):
        """更新所有資料"""
        self.log_message("開始更新所有資料...")

        def update_all_thread():
            for filename in self.data_info.keys():
                if filename in self.data_info:
                    self.update_single_data(filename)
                    # 等待一段時間避免過於頻繁的請求
                    import time
                    time.sleep(2)

        threading.Thread(target=update_all_thread, daemon=True).start()

    def crawl_general_data(self, crawler_func, start_date, end_date):
        """爬取一般資料（按日期範圍）"""
        try:
            # 檢查爬蟲函數是否需要 datetime 對象
            func_name = getattr(crawler_func, '__name__', 'unknown')

            if func_name in ['crawl_pe', 'crawl_price', 'crawl_bargin', 'crawl_monthly_report']:
                # 這些函數需要逐日爬取，並且期望 datetime 對象
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')

                all_data = []
                current_date = start_dt

                while current_date <= end_dt:
                    try:
                        self.log_message(f"爬取 {current_date.strftime('%Y-%m-%d')} 的資料...")
                        daily_data = crawler_func(current_date)

                        if daily_data is not None and not daily_data.empty:
                            all_data.append(daily_data)
                            self.log_message(f"✓ {current_date.strftime('%Y-%m-%d')} 完成，獲取 {len(daily_data)} 筆資料")
                        else:
                            self.log_message(f"⚠️ {current_date.strftime('%Y-%m-%d')} 無資料")

                        current_date += timedelta(days=1)

                        # 避免請求過於頻繁
                        import time
                        time.sleep(2)

                    except Exception as e:
                        self.log_message(f"✗ {current_date.strftime('%Y-%m-%d')} 失敗: {str(e)}")
                        current_date += timedelta(days=1)
                        continue

                if all_data:
                    result = pd.concat(all_data, ignore_index=True)
                    return result
                else:
                    return pd.DataFrame()
            else:
                # 其他函數直接傳入字符串日期
                result = crawler_func(start_date, end_date)
                return result

        except Exception as e:
            self.log_message(f"爬取資料時發生錯誤: {str(e)}")
            return None

    def crawl_financial_data(self, crawler_func, start_date, end_date):
        """爬取財務資料（按季度）"""
        try:
            # 財務資料通常按季度發布，需要特殊處理
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # 生成季度日期
            season_dates = self.generate_season_range(start_dt, end_dt)

            all_data = []
            for date in season_dates:
                try:
                    data = crawler_func(date.strftime('%Y-%m-%d'))
                    if data is not None and not data.empty:
                        all_data.append(data)
                        self.log_message(f"✓ 獲取 {date.strftime('%Y-%m-%d')} 財務資料")
                except Exception as e:
                    self.log_message(f"✗ 獲取 {date.strftime('%Y-%m-%d')} 財務資料失敗: {str(e)}")

            if all_data:
                import pandas as pd
                result = pd.concat(all_data, ignore_index=True)
                return result
            else:
                return None

        except Exception as e:
            self.log_message(f"爬取財務資料時發生錯誤: {str(e)}")
            return None

    def create_log_area(self, parent):
        """創建日誌區域 - 參考 daily_trading_crawler_gui.py"""
        log_frame = ttk.LabelFrame(parent, text="執行日誌", padding="10")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 建立文字框和滾動條 - 使用 pack 布局
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.log_text = tk.Text(text_frame, height=15, wrap='word', font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 清除按鈕
        clear_btn = ttk.Button(log_frame, text="🗑️ 清除日誌", command=self.clear_log)
        clear_btn.grid(row=1, column=0, pady=(5, 0), sticky=tk.W)

        # 初始日誌
        self.log_message("Finlab 爬蟲系統 - 增量更新版已啟動")
    
    def init_crawlers(self):
        """初始化爬蟲 - 使用 auto_update.py 的方式"""
        try:
            # 添加AI_finlab目錄到Python路徑
            ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
            if ai_finlab_path not in sys.path:
                sys.path.insert(0, ai_finlab_path)

            # 導入 finlab crawler 的核心函數，跳過 ipywidgets 依賴
            try:
                from crawler import (
                    # 爬蟲函數
                    crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
                    crawl_finance_statement_by_date, crawl_twse_divide_ratio,
                    crawl_otc_divide_ratio, crawl_twse_cap_reduction,
                    crawl_otc_cap_reduction,
                    # 核心更新函數
                    table_date_range, update_table, to_pickle, date_range,
                    month_range, season_range, commit
                )
            except ImportError as widget_error:
                # 如果是 ipywidgets 錯誤，嘗試只導入必要的函數
                if 'ipywidgets' in str(widget_error):
                    # 創建一個臨時的 widgets 模組來避免 ipywidgets 依賴
                    import types
                    widgets_mock = types.ModuleType('widgets')
                    widgets_mock.Output = lambda **kwargs: None
                    sys.modules['ipywidgets'] = widgets_mock
                    sys.modules['widgets'] = widgets_mock

                    from crawler import (
                        crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
                        crawl_finance_statement_by_date, crawl_twse_divide_ratio,
                        crawl_otc_divide_ratio, crawl_twse_cap_reduction,
                        crawl_otc_cap_reduction,
                        table_date_range, update_table, to_pickle, date_range,
                        month_range, season_range, commit
                    )
                else:
                    raise widget_error

            self.crawler_funcs = {
                # 爬蟲函數
                'crawl_price': crawl_price,
                'crawl_bargin': crawl_bargin,
                'crawl_pe': crawl_pe,
                'crawl_monthly_report': crawl_monthly_report,
                'crawl_balance_sheet': lambda start, end: crawl_finance_statement_by_date(start, end, 'balance_sheet'),
                'crawl_income_sheet': lambda start, end: crawl_finance_statement_by_date(start, end, 'income_sheet'),
                'crawl_cash_flows': lambda start, end: crawl_finance_statement_by_date(start, end, 'cash_flows'),
                'crawl_twse_divide_ratio': crawl_twse_divide_ratio,
                'crawl_otc_divide_ratio': crawl_otc_divide_ratio,
                'crawl_twse_cap_reduction': crawl_twse_cap_reduction,
                'crawl_otc_cap_reduction': crawl_otc_cap_reduction,
                # 核心更新函數
                'table_date_range': table_date_range,
                'update_table': update_table,
                'to_pickle': to_pickle,
                'date_range': date_range,
                'month_range': month_range,
                'season_range': season_range,
                'commit': commit
            }

            self.add_log("✅ Finlab 爬蟲模組載入成功")

        except ImportError as e:
            # 使用簡化版
            self.crawler_funcs = self.create_simple_crawlers()
            self.add_log(f"⚠️ 無法載入 finlab 模組: {e}")
            self.add_log("⚠️ 使用簡化版功能")

        except Exception as e:
            self.add_log(f"❌ 初始化失敗: {e}")
    
    def create_simple_crawlers(self):
        """創建簡化版爬蟲"""
        import pandas as pd
        from datetime import datetime, timedelta
        import numpy as np

        def create_realistic_data(start_date, end_date, data_type):
            """創建更真實的測試資料"""
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d')
                end = datetime.strptime(end_date, '%Y-%m-%d')
            except:
                # 如果日期解析失敗，使用預設範圍
                start = datetime.now() - timedelta(days=7)
                end = datetime.now()

            # 生成日期範圍（只包含工作日）
            dates = []
            current = start
            while current <= end:
                if current.weekday() < 5:  # 週一到週五
                    dates.append(current)
                current += timedelta(days=1)

            if not dates:
                dates = [datetime.now()]

            # 生成股票代碼
            stock_ids = ['2330 台積電', '2317 鴻海', '2454 聯發科', '2881 富邦金', '2412 中華電']

            data_list = []
            for date in dates:
                for stock_id in stock_ids:
                    if data_type == 'price':
                        # 股價資料
                        base_price = np.random.uniform(100, 500)
                        data_list.append({
                            'stock_id': stock_id,
                            'date': date,
                            '開盤價': round(base_price * np.random.uniform(0.98, 1.02), 2),
                            '最高價': round(base_price * np.random.uniform(1.00, 1.05), 2),
                            '最低價': round(base_price * np.random.uniform(0.95, 1.00), 2),
                            '收盤價': round(base_price, 2),
                            '成交股數': int(np.random.uniform(1000000, 10000000)),
                            '成交金額': int(base_price * np.random.uniform(1000000, 10000000))
                        })
                    elif data_type == 'pe':
                        # 本益比資料
                        data_list.append({
                            'stock_id': stock_id,
                            'date': date,
                            '本益比': round(np.random.uniform(10, 30), 2),
                            '殖利率(%)': round(np.random.uniform(2, 8), 2),
                            '股價淨值比': round(np.random.uniform(1, 5), 2)
                        })
                    elif data_type == 'bargin':
                        # 融資融券資料
                        data_list.append({
                            'stock_id': stock_id,
                            'date': date,
                            '外陸資買進股數(不含外資自營商)': int(np.random.uniform(100000, 1000000)),
                            '外陸資賣出股數(不含外資自營商)': int(np.random.uniform(100000, 1000000)),
                            '投信買進股數': int(np.random.uniform(10000, 100000)),
                            '投信賣出股數': int(np.random.uniform(10000, 100000))
                        })
                    elif data_type == 'monthly':
                        # 月報資料（只在每月10日更新）
                        if date.day == 10:
                            data_list.append({
                                'stock_id': stock_id,
                                'date': date,
                                '當月營收': int(np.random.uniform(1000000, 10000000)),
                                '上月營收': int(np.random.uniform(1000000, 10000000)),
                                '去年當月營收': int(np.random.uniform(1000000, 10000000)),
                                '上月比較增減(%)': round(np.random.uniform(-20, 20), 2)
                            })
                    else:
                        # 其他資料類型
                        data_list.append({
                            'stock_id': stock_id,
                            'date': date,
                            'value': round(np.random.uniform(1, 100), 2),
                            'message': f'簡化版{data_type}資料'
                        })

            if not data_list:
                # 如果沒有資料，至少返回一筆
                data_list.append({
                    'stock_id': '2330 台積電',
                    'date': datetime.now(),
                    'message': f'簡化版{data_type}功能 - 請安裝 finlab 獲得完整功能'
                })

            df = pd.DataFrame(data_list)
            df = df.set_index(['stock_id', 'date'])
            return df

        def simple_price_crawler(start_date, end_date):
            return create_realistic_data(start_date, end_date, 'price')

        def simple_pe_crawler(start_date, end_date):
            return create_realistic_data(start_date, end_date, 'pe')

        def simple_bargin_crawler(start_date, end_date):
            return create_realistic_data(start_date, end_date, 'bargin')

        def simple_monthly_crawler(start_date, end_date):
            return create_realistic_data(start_date, end_date, 'monthly')

        def simple_other_crawler(start_date, end_date):
            return create_realistic_data(start_date, end_date, 'other')

        return {
            'crawl_price': simple_price_crawler,
            'crawl_pe': simple_pe_crawler,
            'crawl_bargin': simple_bargin_crawler,
            'crawl_monthly_report': simple_monthly_crawler,
            'crawl_benchmark': simple_other_crawler,
            'crawl_balance_sheet': simple_other_crawler,
            'crawl_income_sheet': simple_other_crawler,
            'crawl_cash_flows': simple_other_crawler,
            'crawl_twse_divide_ratio': simple_other_crawler,
            'crawl_otc_divide_ratio': simple_other_crawler,
            'crawl_twse_cap_reduction': simple_other_crawler,
            'crawl_otc_cap_reduction': simple_other_crawler
        }
    
    def run_crawler(self, crawler_key, crawler_name):
        """執行爬蟲"""
        if not self.crawler_funcs:
            messagebox.showerror("錯誤", "爬蟲模組未載入")
            return
        
        def crawler_thread():
            try:
                self.set_buttons_enabled(False)
                self.progress.grid()
                self.progress.start()
                
                self.add_log(f"🔄 開始執行 {crawler_name}...")
                
                # 獲取爬蟲函數
                crawler_func = self.crawler_funcs[crawler_key]
                
                # 解析日期範圍
                try:
                    start_date = datetime.strptime(self.start_date.get(), '%Y-%m-%d').date()
                    end_date = datetime.strptime(self.end_date.get(), '%Y-%m-%d').date()
                except ValueError:
                    raise Exception("日期格式錯誤，請使用 YYYY-MM-DD 格式")

                # 根據爬蟲類型調用
                if crawler_key in ['price', 'bargin_report', 'pe', 'benchmark']:
                    # 需要日期範圍的爬蟲 - 使用 date_range
                    result = self.crawl_with_date_range(crawler_func, start_date, end_date, 'daily')

                elif crawler_key == 'monthly_report':
                    # 月營收 - 使用 month_range
                    result = self.crawl_with_date_range(crawler_func, start_date, end_date, 'monthly')

                elif crawler_key == 'financial_statement':
                    # 財務報表 - 使用 season_range
                    result = self.crawl_with_date_range(crawler_func, start_date, end_date, 'seasonal')

                else:
                    # 不需要參數的爬蟲（除權息、減資）
                    result = crawler_func()
                
                # 顯示結果
                if hasattr(result, 'shape'):
                    if result.shape[0] > 0:
                        message = f"✅ {crawler_name} 執行完成，獲取 {result.shape[0]} 筆資料"
                    else:
                        message = f"⚠️ {crawler_name} 執行完成，但沒有獲取到資料"
                else:
                    message = f"✅ {crawler_name} 執行完成"
                
                self.add_log(message)
                messagebox.showinfo("執行完成", message)
                
            except Exception as e:
                error_msg = f"❌ {crawler_name} 執行失敗: {str(e)}"
                self.add_log(error_msg)
                messagebox.showerror("執行失敗", error_msg)
                
            finally:
                self.progress.stop()
                self.progress.grid_remove()
                self.set_buttons_enabled(True)
        
        # 啟動線程
        thread = threading.Thread(target=crawler_thread, daemon=True)
        thread.start()

    def crawl_with_date_range(self, crawler_func, start_date, end_date, range_type):
        """使用日期範圍批量爬取資料"""
        import pandas as pd
        from dateutil.rrule import rrule, DAILY, MONTHLY

        # 生成日期列表
        if range_type == 'daily':
            dates = [dt.date() for dt in rrule(DAILY, dtstart=start_date, until=end_date)]
        elif range_type == 'monthly':
            dates = [dt.date() for dt in rrule(MONTHLY, dtstart=start_date, until=end_date)]
        elif range_type == 'seasonal':
            # 季度範圍邏輯
            dates = self.generate_season_range(start_date, end_date)
        else:
            dates = [start_date]

        if not dates:
            self.add_log("⚠️ 沒有可爬取的日期")
            return pd.DataFrame()

        self.add_log(f"📅 準備爬取 {len(dates)} 個日期的資料...")

        all_data = []
        success_count = 0

        for i, date in enumerate(dates):
            try:
                self.add_log(f"🔄 爬取 {date} 的資料... ({i+1}/{len(dates)})")

                # 調用爬蟲函數
                data = crawler_func(date)

                if data is not None and not data.empty:
                    all_data.append(data)
                    success_count += 1
                    self.add_log(f"✅ {date} 完成，獲取 {len(data)} 筆資料")
                else:
                    self.add_log(f"⚠️ {date} 無資料")

                # 避免請求過於頻繁
                if i < len(dates) - 1:
                    import time
                    time.sleep(2)

            except Exception as e:
                self.add_log(f"❌ {date} 失敗: {str(e)}")

        # 合併所有資料
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            self.add_log(f"📊 總共成功爬取 {success_count}/{len(dates)} 個日期，合計 {len(result)} 筆資料")
            return result
        else:
            self.add_log("❌ 沒有成功爬取任何資料")
            return pd.DataFrame()

    def generate_season_range(self, start_date, end_date):
        """生成季度日期範圍"""
        import datetime

        ret = []
        for year in range(start_date.year-1, end_date.year+1):
            ret += [
                datetime.date(year, 5, 15),    # Q1
                datetime.date(year, 8, 14),    # Q2
                datetime.date(year, 11, 14),   # Q3
                datetime.date(year+1, 3, 31)  # Q4
            ]

        # 過濾在範圍內的日期
        ret = [r for r in ret if start_date < r < end_date]
        return ret
    
    def set_buttons_enabled(self, enabled):
        """設置按鈕狀態"""
        state = 'normal' if enabled else 'disabled'
        for btn in self.buttons.values():
            btn.configure(state=state)
    
    def log_message(self, message, level='INFO'):
        """記錄日誌訊息 - 參考 daily_trading_crawler_gui.py"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert('end', log_entry)
        self.log_text.see('end')

        # 限制日誌行數
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete('1.0', '100.0')

        # 立即更新界面
        self.root.update_idletasks()

        # 同時輸出到終端
        print(f"[{timestamp}] {message}")

    def add_log(self, message):
        """添加日誌 - 別名方法"""
        self.log_message(message)

    def clear_log(self):
        """清除日誌"""
        self.log_text.delete('1.0', 'end')
        self.log_message("日誌已清除")

def main():
    """主函數"""
    root = tk.Tk()
    app = FinlabCrawlerGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("程式已中斷")
    except Exception as e:
        print(f"程式執行錯誤: {e}")

if __name__ == "__main__":
    main()
