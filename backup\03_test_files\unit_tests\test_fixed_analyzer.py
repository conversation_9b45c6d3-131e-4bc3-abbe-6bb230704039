#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的分析器
"""

from mass_backtest_analyzer import MassBacktestAnalyzer
import logging

logging.basicConfig(level=logging.INFO)

def test_analyzer():
    """測試分析器"""
    print("🧪 測試修正後的分析器")
    print("="*40)
    
    try:
        # 創建分析器
        analyzer = MassBacktestAnalyzer()
        print(f"✅ 分析器初始化成功，資料庫路徑: {analyzer.db_path}")
        
        # 載入股票清單
        stocks = analyzer.load_stock_list()
        print(f"📊 載入 {len(stocks)} 檔股票")
        
        if len(stocks) == 0:
            print("❌ 沒有載入到股票")
            return False
        
        # 顯示前10檔股票
        print(f"📈 前10檔股票: {stocks[:10]}")
        
        # 測試載入單檔股票數據
        test_stock = stocks[0]
        print(f"\n🔍 測試股票: {test_stock}")
        
        df = analyzer.load_stock_data(test_stock, days=500)
        
        if df.empty:
            print("❌ 載入股票數據失敗")
            return False
        
        print(f"✅ 載入 {len(df)} 筆數據")
        print(f"📅 日期範圍: {df.index.min()} 到 {df.index.max()}")
        print(f"💰 價格範圍: {df['close'].min():.2f} 到 {df['close'].max():.2f}")
        
        # 計算技術指標
        print(f"\n📈 計算技術指標...")
        df = analyzer.calculate_technical_indicators(df)
        
        # 檢查指標
        indicators = ['rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'ma5', 'ma20']
        for indicator in indicators:
            if indicator in df.columns:
                valid_count = df[indicator].notna().sum()
                print(f"   ✅ {indicator}: {valid_count} 個有效值")
            else:
                print(f"   ❌ {indicator}: 指標缺失")
        
        # 生成策略信號
        print(f"\n🎯 生成策略信號...")
        signals = analyzer.generate_strategy_signals(df)
        
        total_signals = 0
        for strategy, signal_data in signals.items():
            buy_count = len(signal_data['buy'])
            sell_count = len(signal_data['sell'])
            total_signals += buy_count + sell_count
            print(f"   📊 {strategy}: 買入{buy_count}個, 賣出{sell_count}個")
        
        if total_signals > 0:
            print(f"\n✅ 測試成功！總共生成 {total_signals} 個信號")
            return True
        else:
            print(f"\n⚠️ 沒有生成信號，可能需要調整參數")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rule_miner():
    """測試規則挖掘器"""
    print(f"\n🔍 測試規則挖掘器")
    print("="*40)
    
    try:
        from trading_rule_miner import TradingRuleMiner
        
        # 創建規則挖掘器
        rule_miner = TradingRuleMiner()
        print("✅ 規則挖掘器初始化成功")
        
        # 執行小規模分析（10檔股票）
        print("📊 開始小規模分析（10檔股票）...")
        combinations = rule_miner.run_mass_analysis(10)
        
        print(f"✅ 發現 {len(combinations)} 種組合模式")
        
        if len(combinations) > 0:
            # 提取規則
            rules = rule_miner.extract_trading_rules(combinations)
            print(f"📋 提取出 {len(rules)} 條交易規則")
            
            if len(rules) > 0:
                print("\n📜 規則範例:")
                for i, rule in enumerate(rules[:3], 1):
                    print(f"   {i}. {rule.rule_name} (成功率: {rule.success_rate:.1%})")
                
                return True
            else:
                print("⚠️ 沒有提取到有效規則")
                return False
        else:
            print("⚠️ 沒有發現組合模式")
            return False
        
    except Exception as e:
        print(f"❌ 規則挖掘器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 測試修正後的交易規則發現系統")
    print("="*50)
    
    # 測試分析器
    analyzer_ok = test_analyzer()
    
    if analyzer_ok:
        # 測試規則挖掘器
        miner_ok = test_rule_miner()
        
        if miner_ok:
            print("\n" + "="*50)
            print("🎉 所有測試通過！系統已修正完成！")
            print("\n💡 現在您可以:")
            print("1. 重新啟動GUI工具")
            print("2. 使用以下建議參數:")
            print("   • 分析股票數量: 100檔")
            print("   • 最低成功率: 60%")
            print("   • 最低平均報酬: 1%")
            print("\n🚀 啟動GUI: python auto_rule_discovery_gui.py")
        else:
            print("\n❌ 規則挖掘器測試失敗")
    else:
        print("\n❌ 分析器測試失敗")

if __name__ == "__main__":
    main()
