#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試查詢歷史除權息資料
"""

import requests
import json
import urllib3
from datetime import datetime, timedelta

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_historical_dividend():
    """測試查詢歷史除權息資料"""
    print("🔍 測試查詢歷史除權息資料...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試不同的日期參數
    test_dates = [
        ('20240714', '2024年7月14日'),
        ('20230714', '2023年7月14日'),
        ('20220714', '2022年7月14日'),
        ('20210714', '2021年7月14日'),
    ]
    
    print("📊 測試證交所API - 不同日期查詢:")
    
    for date_param, desc in test_dates:
        print(f"\n🗓️ 測試 {desc} (參數: {date_param}):")
        
        url = "https://www.twse.com.tw/exchangeReport/TWT49U"
        params = {
            'response': 'json',
            'date': date_param,
            'selectType': 'ALL'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            data = response.json()
            
            print(f"  API標題: {data.get('title', 'N/A')}")
            print(f"  資料筆數: {len(data.get('data', []))}")
            print(f"  開始日期: {data.get('strDate', 'N/A')}")
            print(f"  結束日期: {data.get('endDate', 'N/A')}")
            
            if 'data' in data and data['data']:
                sample = data['data'][0]
                print(f"  樣本除權息日期: {sample[0]}")
            
        except Exception as e:
            print(f"  ❌ 查詢失敗: {e}")
    
    # 測試查詢特定股票的歷史除權息
    print(f"\n\n📊 測試查詢特定股票歷史除權息 (台積電 2330):")
    
    # 嘗試不同的API端點
    test_urls = [
        "https://www.twse.com.tw/exchangeReport/TWT49U",
        "https://www.twse.com.tw/exchangeReport/STOCK_DAY",
        "https://www.twse.com.tw/fund/TWT38U",
    ]
    
    for url in test_urls:
        print(f"\n🔗 測試API: {url}")
        
        params = {
            'response': 'json',
            'date': '20240701',
            'stockNo': '2330'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            data = response.json()
            
            print(f"  回應狀態: {response.status_code}")
            print(f"  回應鍵值: {list(data.keys())}")
            
            if 'title' in data:
                print(f"  標題: {data['title']}")
            
        except Exception as e:
            print(f"  ❌ 測試失敗: {e}")
    
    # 測試月份範圍查詢
    print(f"\n\n📊 測試月份範圍查詢 (2024年整年):")
    
    months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
    
    for month in months[:3]:  # 只測試前3個月
        date_param = f"2024{month}01"
        print(f"\n🗓️ 測試 2024年{month}月:")
        
        url = "https://www.twse.com.tw/exchangeReport/TWT49U"
        params = {
            'response': 'json',
            'date': date_param,
            'selectType': 'ALL'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
            data = response.json()
            
            print(f"  標題: {data.get('title', 'N/A')}")
            print(f"  資料筆數: {len(data.get('data', []))}")
            
            if 'data' in data and data['data']:
                # 檢查是否有該月份的資料
                sample = data['data'][0]
                ex_date = sample[0]
                print(f"  除權息日期: {ex_date}")
                
                # 檢查年份
                if '年' in ex_date:
                    import re
                    match = re.match(r'(\d+)年', ex_date)
                    if match:
                        roc_year = int(match.group(1))
                        ad_year = roc_year + 1911
                        print(f"  轉換年份: {ad_year}")
            
        except Exception as e:
            print(f"  ❌ 查詢失敗: {e}")

if __name__ == "__main__":
    test_historical_dividend()
