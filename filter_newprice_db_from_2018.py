#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保留 newprice.db 中 2018-01-01 以後的資料，移除之前的資料
"""

import sqlite3
import os
import shutil
from datetime import datetime

def filter_newprice_db_from_2018():
    """保留 2018-01-01 以後的資料"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    backup_file = 'D:/Finlab/history/tables/newprice_backup.db'
    
    print("=" * 80)
    print("🔄 保留 newprice.db 中 2018-01-01 以後的資料")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查原始檔案大小
    original_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
    print(f"📁 原始檔案: {db_file}")
    print(f"📊 原始檔案大小: {original_size:.2f} MB")
    
    try:
        # 創建備份
        print(f"\n💾 創建備份檔案...")
        shutil.copy2(db_file, backup_file)
        backup_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
        print(f"✅ 備份完成: {backup_file} ({backup_size:.2f} MB)")
        
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查原始資料統計
        print(f"\n📊 原始資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        original_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {original_count:,}")
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        date_range = cursor.fetchone()
        print(f"   日期範圍: {date_range[0][:10]} ~ {date_range[1][:10]}")
        
        # 檢查 2018 年前的資料
        cursor.execute("SELECT COUNT(*) FROM price WHERE date < '2018-01-01'")
        before_2018_count = cursor.fetchone()[0]
        print(f"   2018年前資料: {before_2018_count:,} 筆")
        
        # 檢查 2018 年後的資料
        cursor.execute("SELECT COUNT(*) FROM price WHERE date >= '2018-01-01'")
        after_2018_count = cursor.fetchone()[0]
        print(f"   2018年後資料: {after_2018_count:,} 筆")
        
        # 檢查年度分布
        print(f"\n📊 年度分布 (2018年前):")
        cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM price 
            WHERE date < '2018-01-01'
            GROUP BY year 
            ORDER BY year
        """)
        before_years = cursor.fetchall()
        
        total_before = 0
        for year, count in before_years:
            print(f"   {year}: {count:,} 筆")
            total_before += count
        
        print(f"   總計: {total_before:,} 筆")
        
        # 確認刪除
        print(f"\n⚠️ 即將刪除 {before_2018_count:,} 筆 2018年前的資料")
        print(f"   保留 {after_2018_count:,} 筆 2018年後的資料")
        print(f"   預計節省空間: {(before_2018_count / original_count) * 100:.1f}%")
        
        # 執行刪除
        print(f"\n🗑️ 開始刪除 2018年前的資料...")
        cursor.execute("DELETE FROM price WHERE date < '2018-01-01'")
        deleted_count = cursor.rowcount
        print(f"✅ 已刪除 {deleted_count:,} 筆資料")

        # 提交事務
        conn.commit()
        print(f"✅ 事務已提交")

        # 關閉連接後重新開啟進行 VACUUM
        conn.close()

        # 優化資料庫
        print(f"\n🔧 優化資料庫...")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 檢查刪除後的統計
        print(f"\n📊 刪除後資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {final_count:,}")
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        final_date_range = cursor.fetchone()
        print(f"   日期範圍: {final_date_range[0][:10]} ~ {final_date_range[1][:10]}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        final_stocks = cursor.fetchone()[0]
        print(f"   不重複股票數: {final_stocks:,}")
        
        # 檢查年度分布
        print(f"\n📊 保留資料年度分布:")
        cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM price 
            GROUP BY year 
            ORDER BY year
        """)
        final_years = cursor.fetchall()
        
        for year, count in final_years:
            print(f"   {year}: {count:,} 筆")
        
        # 檢查最早和最晚的資料
        print(f"\n📊 資料範圍驗證:")
        cursor.execute("SELECT stock_id, date FROM price ORDER BY date LIMIT 5")
        earliest = cursor.fetchall()
        print(f"   最早 5 筆資料:")
        for stock_id, date in earliest:
            print(f"     {date[:10]} - {stock_id}")
        
        cursor.execute("SELECT stock_id, date FROM price ORDER BY date DESC LIMIT 5")
        latest = cursor.fetchall()
        print(f"   最晚 5 筆資料:")
        for stock_id, date in latest:
            print(f"     {date[:10]} - {stock_id}")
        
        conn.close()
        
        # 檢查最終檔案大小
        final_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
        space_saved = original_size - final_size
        space_saved_percent = (space_saved / original_size) * 100
        
        print(f"\n💾 檔案大小變化:")
        print(f"   原始大小: {original_size:.2f} MB")
        print(f"   最終大小: {final_size:.2f} MB")
        print(f"   節省空間: {space_saved:.2f} MB ({space_saved_percent:.1f}%)")
        
        print(f"\n✅ 資料過濾完成！")
        print(f"📁 主檔案: {db_file}")
        print(f"📁 備份檔案: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 過濾過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出錯，嘗試從備份恢復
        if os.path.exists(backup_file):
            print(f"\n🔄 嘗試從備份恢復...")
            try:
                shutil.copy2(backup_file, db_file)
                print(f"✅ 已從備份恢復")
            except Exception as restore_error:
                print(f"❌ 恢復失敗: {restore_error}")
        
        return False

def verify_filtered_data():
    """驗證過濾後的資料"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    
    print("\n" + "=" * 80)
    print("🔍 驗證過濾後的資料")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查是否還有 2018年前的資料
        cursor.execute("SELECT COUNT(*) FROM price WHERE date < '2018-01-01'")
        before_count = cursor.fetchone()[0]
        
        if before_count == 0:
            print("✅ 確認：沒有 2018年前的資料")
        else:
            print(f"❌ 警告：仍有 {before_count} 筆 2018年前的資料")
        
        # 檢查最早日期
        cursor.execute("SELECT MIN(date) FROM price")
        min_date = cursor.fetchone()[0]
        print(f"📅 最早日期: {min_date[:10]}")
        
        if min_date >= '2018-01-01':
            print("✅ 確認：最早日期符合要求")
        else:
            print(f"❌ 警告：最早日期早於 2018-01-01")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == "__main__":
    success = filter_newprice_db_from_2018()
    if success:
        verify_filtered_data()
    else:
        print("❌ 過濾失敗，請檢查錯誤訊息")
