#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試改進後的備用監控系統更新速度和成功率
"""

import sys
import time
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_tsrtc_improvements():
    """測試TSRTC改進效果"""
    print("🚀 測試改進後的TSRTC備用監控系統")
    print("=" * 60)
    
    try:
        # 導入TSRTC監控器
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        
        # 創建監控器實例
        monitor = TSRTCBackupMonitor()
        
        # 測試股票列表（包含截圖中的股票）
        test_stocks = ["2330", "2317", "2454", "3008", "2412", "6290", "6763", "8499", "6667", "1815"]
        
        print(f"📊 測試股票: {', '.join(test_stocks)}")
        print(f"📈 股票數量: {len(test_stocks)} 支")
        print()
        
        # 進行多輪測試
        test_rounds = 3
        all_results = []
        
        for round_num in range(test_rounds):
            print(f"🔄 第 {round_num + 1} 輪測試:")
            
            start_time = time.time()
            
            # 獲取數據
            results = monitor.get_multiple_stocks_realtime(test_stocks)
            
            end_time = time.time()
            update_time = end_time - start_time
            
            # 分析結果
            successful_count = len([r for r in results.values() if r and r.get('current_price', 0) > 0])
            success_rate = (successful_count / len(test_stocks)) * 100
            
            print(f"   ⏱️ 更新時間: {update_time:.2f}秒")
            print(f"   ✅ 成功獲取: {successful_count}/{len(test_stocks)} 支 ({success_rate:.1f}%)")
            
            # 顯示詳細結果
            for stock_code in test_stocks:
                if stock_code in results and results[stock_code]:
                    data = results[stock_code]
                    price = data.get('current_price', 0)
                    name = data.get('stock_name', stock_code)
                    if price > 0:
                        print(f"      📈 {stock_code} ({name}): {price}")
                    else:
                        print(f"      ⚠️ {stock_code}: 價格為0")
                else:
                    print(f"      ❌ {stock_code}: 無數據")
            
            all_results.append({
                'round': round_num + 1,
                'time': update_time,
                'success_count': successful_count,
                'success_rate': success_rate,
                'results': results
            })
            
            print()
            
            # 輪次間延遲
            if round_num < test_rounds - 1:
                time.sleep(3)
        
        # 統計分析
        print("📊 測試統計分析:")
        print("-" * 40)
        
        avg_time = sum(r['time'] for r in all_results) / len(all_results)
        avg_success_rate = sum(r['success_rate'] for r in all_results) / len(all_results)
        min_time = min(r['time'] for r in all_results)
        max_time = max(r['time'] for r in all_results)
        
        print(f"平均更新時間: {avg_time:.2f}秒")
        print(f"最快更新時間: {min_time:.2f}秒")
        print(f"最慢更新時間: {max_time:.2f}秒")
        print(f"平均成功率: {avg_success_rate:.1f}%")
        
        # 評估改進效果
        print(f"\n🎯 改進效果評估:")
        print("-" * 40)
        
        if avg_time <= 3.0:
            print("✅ 更新速度: 優秀 (≤3秒)")
        elif avg_time <= 5.0:
            print("⚠️ 更新速度: 良好 (3-5秒)")
        else:
            print("❌ 更新速度: 需要改進 (>5秒)")
        
        if avg_success_rate >= 80:
            print("✅ 成功率: 優秀 (≥80%)")
        elif avg_success_rate >= 60:
            print("⚠️ 成功率: 良好 (60-80%)")
        else:
            print("❌ 成功率: 需要改進 (<60%)")
        
        # 分析問題股票
        problem_stocks = []
        for stock_code in test_stocks:
            success_count = sum(1 for r in all_results if stock_code in r['results'] and r['results'][stock_code] and r['results'][stock_code].get('current_price', 0) > 0)
            if success_count < len(all_results) * 0.5:  # 成功率低於50%
                problem_stocks.append(stock_code)
        
        if problem_stocks:
            print(f"\n⚠️ 問題股票分析:")
            print(f"經常獲取失敗的股票: {', '.join(problem_stocks)}")
            print("可能原因:")
            for stock in problem_stocks:
                if stock in ["6290", "6763", "8499"]:
                    print(f"  • {stock}: 可能是新股或小型股，交易量較小")
                elif stock in ["3008"]:
                    print(f"  • {stock}: 高價股，可能數據更新較慢")
                else:
                    print(f"  • {stock}: 需要檢查股票狀態")
        
        return avg_success_rate >= 70 and avg_time <= 5.0
        
    except ImportError as e:
        print(f"❌ 無法導入TSRTC監控器: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_gui_integration():
    """測試GUI整合效果"""
    print(f"\n🖥️ GUI整合測試")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ PyQt6環境正常")
        print("✅ 可以整合到備用監控GUI")
        print("✅ 支援定時更新機制")
        
        # 模擬定時器測試
        def timer_test():
            print("🔄 模擬定時更新...")
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(timer_test)
        timer.start(1000)  # 1秒後執行
        
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 改進後的備用監控系統測試")
    print("=" * 80)
    print(f"📅 測試時間: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 測試TSRTC改進
    tsrtc_success = test_tsrtc_improvements()
    
    # 2. 測試GUI整合
    gui_success = test_gui_integration()
    
    # 總結
    print(f"\n🎊 測試總結")
    print("=" * 80)
    
    if tsrtc_success and gui_success:
        print("✅ 備用監控系統改進成功！")
        print()
        print("🚀 主要改進:")
        print("  1. 批量大小優化 (15 → 8)，提高成功率")
        print("  2. 超時時間縮短 (10s → 6s)，加快響應")
        print("  3. 添加重試機制，處理失敗股票")
        print("  4. 智能分批處理，避免大批量失敗")
        print("  5. 數據驗證增強，確保質量")
        print("  6. 狀態顯示優化，更清晰的反饋")
        print()
        print("📈 預期效果:")
        print("  • 更新速度提升 30-50%")
        print("  • 數據獲取成功率提升到 80%+")
        print("  • 減少卡頓和超時問題")
        print("  • 更穩定的監控體驗")
        print()
        print("🎯 現在備用監控系統應該能夠:")
        print("  • 更快速地更新股價數據")
        print("  • 更可靠地獲取所有股票信息")
        print("  • 提供更好的用戶體驗")
    else:
        print("❌ 部分測試失敗，需要進一步調整")
        if not tsrtc_success:
            print("  • TSRTC數據獲取需要優化")
        if not gui_success:
            print("  • GUI整合需要檢查")
    
    print("=" * 80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
    
    print(f"\n👋 測試完成！")
