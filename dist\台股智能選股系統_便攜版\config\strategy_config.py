#!/usr/bin/env python3
"""
策略配置模組
從原始程式中提取的策略相關類別
"""
import logging
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QDialogButtonBox, QFormLayout, QTextEdit, QListWidget,
    QListWidgetItem, QMessageBox, QTabWidget, QWidget, QGridLayout
)
from PyQt5.QtCore import Qt
class StrategyParams:
    def __init__(self, name="Default", description="",
                 ma240_days=240, trend_days=30,
                 ma60_days=60, ma20_days=20,
                 volume_short=3, volume_long=18, cross_days=3,
                 rsi_long=13, rsi_long_threshold=50,
                 rsi_short=6, rsi_short_threshold=70,
                 # 破底高反彈策略參數
                 break_bottom_min_percent=2, rebound_max_percent=15,
                 rebound_min_percent=5, volume_trend_days=5,
                 kline_pattern_check=True):
        self.name = name
        self.description = description
        self.ma240_days = ma240_days
        self.trend_days = trend_days
        self.ma60_days = ma60_days
        self.ma20_days = ma20_days
        self.volume_short = volume_short
        self.volume_long = volume_long
        self.cross_days = cross_days
        self.rsi_long = rsi_long
        self.rsi_long_threshold = rsi_long_threshold
        self.rsi_short = rsi_short
        self.rsi_short_threshold = rsi_short_threshold
        # 破底高反彈策略參數
        self.break_bottom_min_percent = break_bottom_min_percent  # 破底最小百分比
        self.rebound_max_percent = rebound_max_percent  # 反彈上限百分比
        self.rebound_min_percent = rebound_min_percent  # 反彈最小百分比
        self.volume_trend_days = volume_trend_days  # 成交量趨勢分析天數
        self.kline_pattern_check = kline_pattern_check  # 是否啟用K線形態檢查
        
        # 🚀 新增：開盤區間突破策略參數 🚀
        self.intraday_enabled = False  # 是否啟用日內策略
        self.intraday_lookback_days = 5
        self.intraday_open_range_minutes = 30
        self.intraday_ma_period = 5
        self.intraday_vol_multiplier = 1.5
        self.intraday_profit_target = 0.02  # 2%
        self.intraday_stop_loss = 0.01      # 1%

    @staticmethod
    def default():
        return StrategyParams()

    def save(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.__dict__, f, ensure_ascii=False, indent=4)

    @classmethod
    def load(cls, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if "parameters" in data:
                params = data["parameters"]
            else:
                params = data
            return cls(
                name=data.get("name", "Default"),
                description=data.get("description", ""),
                ma240_days=params.get("ma240_days", 240),
                trend_days=params.get("trend_days", 30),
                ma60_days=params.get("ma60_days", 60),
                ma20_days=params.get("ma20_days", 20),
                volume_short=params.get("volume_short", 3),
                volume_long=params.get("volume_long", 18),
                cross_days=params.get("cross_days", 3),
                rsi_long=params.get("rsi_long", 13),
                rsi_long_threshold=params.get("rsi_long_threshold", 50),
                rsi_short=params.get("rsi_short", 6),
                rsi_short_threshold=params.get("rsi_short_threshold", 70),
                # 破底高反彈策略參數
                break_bottom_min_percent=params.get("break_bottom_min_percent", 2),
                rebound_max_percent=params.get("rebound_max_percent", 15),
                rebound_min_percent=params.get("rebound_min_percent", 5),
                volume_trend_days=params.get("volume_trend_days", 5),
                kline_pattern_check=params.get("kline_pattern_check", True)
            )
        except Exception as e:
            logging.error(f"載入策略參數時出錯: {e}")
            logging.error(traceback.format_exc())
            return cls.default()

class Strategy:
    def __init__(self, params):
        self.params = params

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        df['Signal'] = 0
        for i in range(1, len(df)):
            if df['MA5'].iloc[i] > df['MA20'].iloc[i] and df['MA5'].iloc[i-1] <= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = 1
            elif df['MA5'].iloc[i] < df['MA20'].iloc[i] and df['MA5'].iloc[i-1] >= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = -1
        return df

# ===================== 策略設定對話框 =====================
class StrategyDialog(QDialog):
    def __init__(self, params: StrategyParams = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("策略設置")
        self.setModal(True)
        layout = QVBoxLayout(self)
        
        # 策略名稱與描述
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("策略名稱:"))
        self.name_edit = QLineEdit()
        if params:
            self.name_edit.setText(params.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("策略描述:"))
        self.desc_edit = QLineEdit()
        if params:
            self.desc_edit.setText(params.description)
        desc_layout.addWidget(self.desc_edit)
        layout.addLayout(desc_layout)
        
        # 參數設置群組（示意，可根據需求增加設定項目）
        params_group = QGroupBox("參數設置")
        params_layout = QVBoxLayout(params_group)
        params_layout.addWidget(QLabel("240日線天數:"))
        self.ma240_days_input = QSpinBox()
        self.ma240_days_input.setValue(params.ma240_days if params else 240)
        params_layout.addWidget(self.ma240_days_input)
        params_layout.addWidget(QLabel("趨勢天數:"))
        self.trend_days_input = QSpinBox()
        self.trend_days_input.setValue(params.trend_days if params else 30)
        params_layout.addWidget(self.trend_days_input)
        params_layout.addWidget(QLabel("60日線天數:"))
        self.ma60_days_input = QSpinBox()
        self.ma60_days_input.setValue(params.ma60_days if params else 60)
        params_layout.addWidget(self.ma60_days_input)
        params_layout.addWidget(QLabel("20日線天數:"))
        self.ma20_days_input = QSpinBox()
        self.ma20_days_input.setValue(params.ma20_days if params else 20)
        params_layout.addWidget(self.ma20_days_input)
        params_layout.addWidget(QLabel("短期成交量天數:"))
        self.volume_short_input = QSpinBox()
        self.volume_short_input.setValue(params.volume_short if params else 3)
        params_layout.addWidget(self.volume_short_input)
        params_layout.addWidget(QLabel("長期成交量天數:"))
        self.volume_long_input = QSpinBox()
        self.volume_long_input.setValue(params.volume_long if params else 18)
        params_layout.addWidget(self.volume_long_input)
        params_layout.addWidget(QLabel("交叉天數:"))
        self.cross_days_input = QSpinBox()
        self.cross_days_input.setValue(params.cross_days if params else 3)
        params_layout.addWidget(self.cross_days_input)
        params_layout.addWidget(QLabel("長期RSI天數:"))
        self.rsi_long_input = QSpinBox()
        self.rsi_long_input.setValue(params.rsi_long if params else 13)
        params_layout.addWidget(self.rsi_long_input)
        params_layout.addWidget(QLabel("長期RSI閾值:"))
        self.rsi_long_threshold_input = QSpinBox()
        self.rsi_long_threshold_input.setValue(params.rsi_long_threshold if params else 50)
        params_layout.addWidget(self.rsi_long_threshold_input)
        params_layout.addWidget(QLabel("短期RSI天數:"))
        self.rsi_short_input = QSpinBox()
        self.rsi_short_input.setValue(params.rsi_short if params else 6)
        params_layout.addWidget(self.rsi_short_input)
        params_layout.addWidget(QLabel("短期RSI閾值:"))
        self.rsi_short_threshold_input = QSpinBox()
        self.rsi_short_threshold_input.setValue(params.rsi_short_threshold if params else 70)
        params_layout.addWidget(self.rsi_short_threshold_input)
        
        # 新增破底高反彈策略參數
        params_layout.addWidget(QLabel("破底最小百分比:"))
        self.break_bottom_min_percent_input = QDoubleSpinBox()
        self.break_bottom_min_percent_input.setRange(0.5, 10)
        self.break_bottom_min_percent_input.setSingleStep(0.5)
        self.break_bottom_min_percent_input.setValue(params.break_bottom_min_percent if params else 2)
        self.break_bottom_min_percent_input.setSuffix("%")
        params_layout.addWidget(self.break_bottom_min_percent_input)
        
        params_layout.addWidget(QLabel("反彈上限百分比:"))
        self.rebound_max_percent_input = QDoubleSpinBox()
        self.rebound_max_percent_input.setRange(5, 30)
        self.rebound_max_percent_input.setSingleStep(1)
        self.rebound_max_percent_input.setValue(params.rebound_max_percent if params else 15)
        self.rebound_max_percent_input.setSuffix("%")
        params_layout.addWidget(self.rebound_max_percent_input)
        
        params_layout.addWidget(QLabel("反彈最小百分比:"))
        self.rebound_min_percent_input = QDoubleSpinBox()
        self.rebound_min_percent_input.setRange(1, 10)
        self.rebound_min_percent_input.setSingleStep(0.5)
        self.rebound_min_percent_input.setValue(params.rebound_min_percent if params else 5)
        self.rebound_min_percent_input.setSuffix("%")
        params_layout.addWidget(self.rebound_min_percent_input)
        
        params_layout.addWidget(QLabel("成交量趨勢分析天數:"))
        self.volume_trend_days_input = QSpinBox()
        self.volume_trend_days_input.setRange(3, 10)
        self.volume_trend_days_input.setValue(params.volume_trend_days if params else 5)
        params_layout.addWidget(self.volume_trend_days_input)
        
        params_layout.addWidget(QLabel("啟用K線形態檢查:"))
        self.kline_pattern_check_input = QCheckBox()
        self.kline_pattern_check_input.setChecked(params.kline_pattern_check if params else True)
        params_layout.addWidget(self.kline_pattern_check_input)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存")
        save_button.clicked.connect(self.accept)
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def get_strategy_params(self) -> StrategyParams:
        return StrategyParams(
            name=self.name_edit.text(),
            description=self.desc_edit.text(),
            ma240_days=self.ma240_days_input.value(),
            trend_days=self.trend_days_input.value(),
            ma60_days=self.ma60_days_input.value(),
            ma20_days=self.ma20_days_input.value(),
            volume_short=self.volume_short_input.value(),
            volume_long=self.volume_long_input.value(),
            cross_days=self.cross_days_input.value(),
            rsi_long=self.rsi_long_input.value(),
            rsi_long_threshold=self.rsi_long_threshold_input.value(),
            rsi_short=self.rsi_short_input.value(),
            rsi_short_threshold=self.rsi_short_threshold_input.value(),
            # 破底高反彈策略參數
            break_bottom_min_percent=self.break_bottom_min_percent_input.value(),
            rebound_max_percent=self.rebound_max_percent_input.value(),
            rebound_min_percent=self.rebound_min_percent_input.value(),
            volume_trend_days=self.volume_trend_days_input.value(),
            kline_pattern_check=self.kline_pattern_check_input.isChecked()
        )

    def add_condition(self):
        """添加新條件"""
        dialog = ConditionDialog(self)
        if dialog.exec():
            condition = dialog.get_condition()
            self.conditions.append(condition)
            self.update_conditions_list()
    
    def edit_condition(self):
        """編輯選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        dialog = ConditionDialog(self, self.conditions[current_row])
        if dialog.exec():
            self.conditions[current_row] = dialog.get_condition()
            self.update_conditions_list()
    
    def remove_condition(self):
        """刪除選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        self.conditions.pop(current_row)
        self.update_conditions_list()
    
    def get_conditions(self):
        """獲取所有條件"""
        return self.conditions

# ===================== 條件編輯對話框 =====================
class ConditionDialog(QDialog):
    """條件編輯對話框"""
    def __init__(self, parent=None, condition=None):
        super().__init__(parent)
        self.setWindowTitle("編輯條件")
        self.resize(500, 400)
        
        self.condition = condition or {}
        layout = QVBoxLayout(self)
        
        # 條件類型選擇
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("條件類型:"))
        self.condition_type = QComboBox()
        self.condition_type.addItem("均線位置", "ma_position")
        self.condition_type.addItem("均線趨勢", "ma_trend")
        self.condition_type.addItem("成交量增加", "volume_increase")
        self.condition_type.addItem("MACD信號", "macd")
        self.condition_type.addItem("RSI指標", "rsi")
        # 新增：破底後高反彈條件
        self.condition_type.addItem("破底反彈", "break_bottom_rebound")
        
        current_type = self.condition.get("type", "") if self.condition else ""
        index = self.condition_type.findData(current_type)
        if index >= 0:
            self.condition_type.setCurrentIndex(index)
        
        self.condition_type.currentIndexChanged.connect(self.update_condition_form)
        type_layout.addWidget(self.condition_type)
        layout.addLayout(type_layout)
        
        # 條件參數區域
        self.condition_form = QWidget()
        self.condition_form_layout = QFormLayout(self.condition_form)
        layout.addWidget(self.condition_form)
        
        # 按鈕
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.update_condition_form()
    
    def update_condition_form(self):
        while self.condition_form_layout.count():
            item = self.condition_form_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        condition_type = self.condition_type.currentData()
        
        if condition_type == "ma_position":
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            self.position_combo = QComboBox()
            self.position_combo.addItem("上方", "above")
            self.position_combo.addItem("下方", "below")
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.position_combo.findData(self.condition.get("position", "above"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            self.price_combo = QComboBox()
            self.price_combo.addItem("收盤價", "close")
            self.price_combo.addItem("開盤價", "open")
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.price_combo.findData(self.condition.get("price", "close"))
                if index >= 0:
                    self.price_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("價格:", self.price_combo)
        
        elif condition_type == "ma_trend":
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            self.trend_combo = QComboBox()
            self.trend_combo.addItem("向上", "up")
            self.trend_combo.addItem("向下", "down")
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.trend_combo.findData(self.condition.get("trend", "up"))
                if index >= 0:
                    self.trend_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("趨勢:", self.trend_combo)
            
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "ma_trend" else 5)
            self.condition_form_layout.addRow("天數:", self.days_spin)
        
        elif condition_type == "volume_increase":
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "volume_increase" else 5)
            self.condition_form_layout.addRow("比較天數:", self.days_spin)
            
            self.percent_spin = QDoubleSpinBox()
            self.percent_spin.setRange(0, 500)
            self.percent_spin.setSuffix("%")
            self.percent_spin.setValue(self.condition.get("percent", 20) if self.condition and self.condition.get("type") == "volume_increase" else 20)
            self.condition_form_layout.addRow("增加比例:", self.percent_spin)
        
        elif condition_type == "macd":
            self.signal_combo = QComboBox()
            self.signal_combo.addItem("金叉", "golden_cross")
            self.signal_combo.addItem("死叉", "death_cross")
            self.signal_combo.addItem("MACD在零線上", "above_zero")
            if self.condition and self.condition.get("type") == "macd":
                index = self.signal_combo.findData(self.condition.get("signal", "golden_cross"))
                if index >= 0:
                    self.signal_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("信號:", self.signal_combo)
        
        elif condition_type == "rsi":
            self.period_combo = QComboBox()
            self.period_combo.addItem("RSI(6)", 6)
            self.period_combo.addItem("RSI(14)", 14)
            self.period_combo.addItem("RSI(24)", 24)
            if self.condition and self.condition.get("type") == "rsi":
                index = self.period_combo.findData(self.condition.get("period", 14))
                if index >= 0:
                    self.period_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("週期:", self.period_combo)
            
            self.position_combo = QComboBox()
            self.position_combo.addItem("超賣區", "oversold")
            self.position_combo.addItem("超買區", "overbought")
            self.position_combo.addItem("中間區域", "middle")
            if self.condition and self.condition.get("type") == "rsi":
                index = self.position_combo.findData(self.condition.get("position", "oversold"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            self.min_spin = QSpinBox()
            self.min_spin.setRange(0, 100)
            self.min_spin.setValue(self.condition.get("min", 30) if self.condition and self.condition.get("type") == "rsi" else 30)
            self.condition_form_layout.addRow("超賣值 (<):", self.min_spin)
            
            self.max_spin = QSpinBox()
            self.max_spin.setRange(0, 100)
            self.max_spin.setValue(self.condition.get("max", 70) if self.condition and self.condition.get("type") == "rsi" else 70)
            self.condition_form_layout.addRow("超買值 (>):", self.max_spin)
        
        elif condition_type == "break_bottom_rebound":
            # 新增破底反彈條件選項
            self.lookback_spin = QSpinBox()
            self.lookback_spin.setRange(3, 30)
            default_lookback = self.condition.get("lookback", 10) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 10
            self.lookback_spin.setValue(default_lookback)
            self.condition_form_layout.addRow("回溯天數:", self.lookback_spin)
            
            self.rebound_spin = QDoubleSpinBox()
            self.rebound_spin.setRange(0, 100)
            self.rebound_spin.setSuffix("%")
            default_rebound = self.condition.get("rebound_threshold", 5) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 5
            self.rebound_spin.setValue(default_rebound)
            self.condition_form_layout.addRow("反彈幅度:", self.rebound_spin)
            
            self.rebound_max_spin = QDoubleSpinBox()
            self.rebound_max_spin.setRange(5, 30)
            self.rebound_max_spin.setSuffix("%")
            default_rebound_max = self.condition.get("rebound_max", 15) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 15
            self.rebound_max_spin.setValue(default_rebound_max)
            self.condition_form_layout.addRow("反彈上限:", self.rebound_max_spin)
            
            self.volume_mult_spin = QDoubleSpinBox()
            self.volume_mult_spin.setDecimals(1)
            self.volume_mult_spin.setRange(1, 10)
            default_volume_mult = self.condition.get("volume_multiplier", 2) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 2
            self.volume_mult_spin.setValue(default_volume_mult)
            self.condition_form_layout.addRow("成交量倍數:", self.volume_mult_spin)
            
            self.break_bottom_pct_spin = QDoubleSpinBox()
            self.break_bottom_pct_spin.setRange(0.5, 10)
            self.break_bottom_pct_spin.setSuffix("%")
            default_break_pct = self.condition.get("break_bottom_pct", 2) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 2
            self.break_bottom_pct_spin.setValue(default_break_pct)
            self.condition_form_layout.addRow("破底百分比:", self.break_bottom_pct_spin)
            
            self.check_recent_low = QCheckBox("僅檢查反彈初期")
            self.check_recent_low.setChecked(self.condition.get("check_recent_low", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_recent_low)
            
            self.check_volume_trend = QCheckBox("檢查成交量趨勢")
            self.check_volume_trend.setChecked(self.condition.get("check_volume_trend", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_volume_trend)
            
            self.check_kline_pattern = QCheckBox("檢查K線形態")
            self.check_kline_pattern.setChecked(self.condition.get("check_kline_pattern", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_kline_pattern)
    
    def get_condition(self):
        condition_type = self.condition_type.currentData()
        condition = {"type": condition_type}
        
        if condition_type == "ma_position":
            condition["ma"] = self.ma_combo.currentText()
            condition["position"] = self.position_combo.currentData()
            condition["price"] = self.price_combo.currentData()
        elif condition_type == "ma_trend":
            condition["ma"] = self.ma_combo.currentText()
            condition["trend"] = self.trend_combo.currentData()
            condition["days"] = self.days_spin.value()
        elif condition_type == "volume_increase":
            condition["days"] = self.days_spin.value()
            condition["percent"] = self.percent_spin.value()
        elif condition_type == "macd":
            condition["signal"] = self.signal_combo.currentData()
        elif condition_type == "rsi":
            condition["period"] = self.period_combo.currentData()
            condition["position"] = self.position_combo.currentData()
            condition["min"] = self.min_spin.value()
            condition["max"] = self.max_spin.value()
        elif condition_type == "break_bottom_rebound":
            condition["lookback"] = self.lookback_spin.value()
            condition["rebound_threshold"] = self.rebound_spin.value()
            condition["rebound_max"] = self.rebound_max_spin.value()
            condition["volume_multiplier"] = self.volume_mult_spin.value()
            condition["break_bottom_pct"] = self.break_bottom_pct_spin.value()
            condition["check_recent_low"] = self.check_recent_low.isChecked()
            condition["check_volume_trend"] = self.check_volume_trend.isChecked()
            condition["check_kline_pattern"] = self.check_kline_pattern.isChecked()
        
        return condition

