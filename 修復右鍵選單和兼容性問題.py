#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復右鍵選單和兼容性問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_numpy_compatibility():
    """修復numpy兼容性問題"""
    print("🔧 修復numpy兼容性問題...")
    
    try:
        import warnings
        warnings.filterwarnings('ignore', category=FutureWarning)
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        
        # 嘗試修復numpy._core問題
        try:
            import numpy._core.numeric
            print("✅ numpy._core.numeric 可用")
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
                print("✅ numpy._core.numeric 兼容性修復成功")
            except ImportError:
                print("⚠️ numpy._core.numeric 無法修復，但不影響主要功能")
        
        import pandas as pd
        print("✅ pandas 載入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ numpy兼容性修復失敗: {e}")
        return False

def test_right_click_menu():
    """測試右鍵選單功能"""
    print("\n🖱️ 測試右鍵選單功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QTableWidget, QTableWidgetItem, QMenu
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 創建測試表格
        table = QTableWidget(3, 2)
        table.setHorizontalHeaderLabels(["股票代碼", "股票名稱"])
        
        # 添加測試資料
        test_data = [("2330", "台積電"), ("2317", "鴻海"), ("1101", "台泥")]
        for row, (code, name) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(code))
            table.setItem(row, 1, QTableWidgetItem(name))
        
        # 設置右鍵選單
        table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        
        def show_menu(position):
            menu = QMenu()
            menu.addAction("測試選項")
            menu.exec(table.mapToGlobal(position))
            print("✅ 右鍵選單顯示成功")
        
        table.customContextMenuRequested.connect(show_menu)
        
        print("✅ 右鍵選單設置成功")
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 右鍵選單測試失敗: {e}")
        return False

def test_main_gui_import():
    """測試主GUI導入"""
    print("\n📊 測試主GUI導入...")
    
    try:
        # 修復兼容性問題後再導入
        fix_numpy_compatibility()
        
        from O3mh_gui_v21_optimized import FinlabGUI
        print("✅ 主GUI導入成功")
        
        # 測試創建實例
        from PyQt6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        gui = FinlabGUI()
        print("✅ 主GUI實例創建成功")
        
        # 檢查右鍵選單設置
        table = gui.result_table
        policy = table.contextMenuPolicy()
        expected = Qt.ContextMenuPolicy.CustomContextMenu
        
        if policy == expected:
            print("✅ 右鍵選單策略設置正確")
        else:
            print(f"❌ 右鍵選單策略錯誤: {policy} != {expected}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 主GUI測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pkl_file_reading():
    """測試PKL檔案讀取"""
    print("\n📁 測試PKL檔案讀取...")
    
    try:
        import pandas as pd
        import warnings
        warnings.filterwarnings('ignore')
        
        pkl_files = [
            'pe.pkl',
            'history/pe.pkl',
            'history/tables/monthly_report_new.pkl',
            'history/tables/monthly_report.pkl'
        ]
        
        for pkl_path in pkl_files:
            if os.path.exists(pkl_path):
                try:
                    df = pd.read_pickle(pkl_path)
                    print(f"✅ {pkl_path}: 讀取成功，形狀 {df.shape}")
                except Exception as e:
                    print(f"❌ {pkl_path}: 讀取失敗 - {e}")
            else:
                print(f"⚠️ {pkl_path}: 檔案不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ PKL檔案測試失敗: {e}")
        return False

def create_simple_test_gui():
    """創建簡單的測試GUI"""
    print("\n🎯 創建簡單測試GUI...")
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, 
                                   QTableWidgetItem, QMenu, QMessageBox, QVBoxLayout, QWidget)
        from PyQt6.QtCore import Qt
        
        class SimpleTestGUI(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("右鍵選單測試")
                self.setGeometry(100, 100, 600, 400)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # 創建表格
                self.table = QTableWidget(5, 3)
                self.table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "價格"])
                
                # 添加測試資料
                test_data = [
                    ("2330", "台積電", "580"),
                    ("2317", "鴻海", "105"),
                    ("2454", "聯發科", "750"),
                    ("1101", "台泥", "45"),
                    ("8021", "尖點", "25")
                ]
                
                for row, (code, name, price) in enumerate(test_data):
                    self.table.setItem(row, 0, QTableWidgetItem(code))
                    self.table.setItem(row, 1, QTableWidgetItem(name))
                    self.table.setItem(row, 2, QTableWidgetItem(price))
                
                # 設置右鍵選單
                self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.table.customContextMenuRequested.connect(self.show_context_menu)
                
                layout.addWidget(self.table)
                
                print("✅ 簡單測試GUI創建成功")
            
            def show_context_menu(self, position):
                """顯示右鍵選單"""
                try:
                    print(f"🖱️ 右鍵選單觸發: {position}")
                    
                    item = self.table.itemAt(position)
                    if not item:
                        return
                    
                    row = item.row()
                    stock_code = self.table.item(row, 0).text()
                    stock_name = self.table.item(row, 1).text()
                    
                    print(f"📊 選中股票: {stock_code} {stock_name}")
                    
                    # 創建選單
                    menu = QMenu(self)
                    
                    # 添加選項
                    assessment_action = menu.addAction(f"📊 {stock_code} 月營收評估")
                    assessment_action.triggered.connect(
                        lambda: self.show_assessment(stock_code, stock_name)
                    )
                    
                    news_action = menu.addAction(f"📰 {stock_code} 股票新聞")
                    news_action.triggered.connect(
                        lambda: self.show_news(stock_code, stock_name)
                    )
                    
                    # 顯示選單
                    menu.exec(self.table.mapToGlobal(position))
                    print("✅ 右鍵選單顯示成功")
                    
                except Exception as e:
                    print(f"❌ 右鍵選單錯誤: {e}")
            
            def show_assessment(self, stock_code, stock_name):
                """顯示評估"""
                print(f"📊 顯示 {stock_code} {stock_name} 評估")
                QMessageBox.information(
                    self, "月營收評估", 
                    f"這裡會顯示 {stock_code} {stock_name} 的月營收綜合評估\n\n"
                    f"包括：\n"
                    f"• YoY年增率\n"
                    f"• MoM月增率\n"
                    f"• 綜合排名\n"
                    f"• 財務指標"
                )
            
            def show_news(self, stock_code, stock_name):
                """顯示新聞"""
                print(f"📰 顯示 {stock_code} {stock_name} 新聞")
                QMessageBox.information(
                    self, "股票新聞", 
                    f"這裡會顯示 {stock_code} {stock_name} 的相關新聞"
                )
        
        app = QApplication(sys.argv)
        window = SimpleTestGUI()
        window.show()
        
        print("\n📋 測試說明:")
        print("1. 在表格中的任意股票行上右鍵點擊")
        print("2. 應該會出現包含兩個選項的右鍵選單")
        print("3. 點擊選項測試功能")
        print("4. 關閉視窗結束測試")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 簡單測試GUI創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 右鍵選單和兼容性問題修復工具")
    print("=" * 60)
    
    # 1. 修復numpy兼容性
    numpy_ok = fix_numpy_compatibility()
    
    # 2. 測試右鍵選單基本功能
    menu_ok = test_right_click_menu()
    
    # 3. 測試PKL檔案讀取
    pkl_ok = test_pkl_file_reading()
    
    # 4. 測試主GUI導入
    gui_ok = test_main_gui_import()
    
    print("\n" + "=" * 60)
    print("🎯 測試結果總結:")
    print(f"  numpy兼容性: {'✅ 正常' if numpy_ok else '❌ 異常'}")
    print(f"  右鍵選單功能: {'✅ 正常' if menu_ok else '❌ 異常'}")
    print(f"  PKL檔案讀取: {'✅ 正常' if pkl_ok else '❌ 異常'}")
    print(f"  主GUI導入: {'✅ 正常' if gui_ok else '❌ 異常'}")
    
    if all([numpy_ok, menu_ok, pkl_ok, gui_ok]):
        print("\n🎉 所有測試通過！")
        print("\n是否要啟動簡單測試GUI來驗證右鍵選單？(y/n): ", end="")
        try:
            choice = input().strip().lower()
            if choice in ['y', 'yes', '是']:
                create_simple_test_gui()
        except KeyboardInterrupt:
            print("\n👋 測試結束")
    else:
        print("\n⚠️ 部分測試失敗，請檢查相關問題")
        
        print("\n🔧 建議修復步驟:")
        if not numpy_ok:
            print("  1. 更新numpy版本: pip install --upgrade numpy")
        if not menu_ok:
            print("  2. 檢查PyQt6安裝: pip install --upgrade PyQt6")
        if not pkl_ok:
            print("  3. 檢查pandas版本: pip install --upgrade pandas")
        if not gui_ok:
            print("  4. 檢查主程式依賴項目")

if __name__ == "__main__":
    main()
