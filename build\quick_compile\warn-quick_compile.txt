
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), _pytest._py.path (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), psutil (optional), netrc (delayed, conditional), getpass (delayed), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), _pytest._py.path (delayed), http.server (delayed, optional)
missing module named urllib.splittype - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.ContentTooShortError - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.pathname2url - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.url2pathname - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.urlretrieve - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), IPython.utils.timing (optional), fsspec.asyn (conditional, optional), joblib.externals.loky.backend.fork_exec (delayed, optional), torch._inductor.codecache (delayed, conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level), tty (top-level), click._termui_impl (conditional), tqdm.utils (delayed, optional), accelerate.commands.menu.keymap (delayed, conditional)
missing module named pickle5 - imported by numpy.compat.py3k (optional), srsly.cloudpickle.compat (conditional, optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), torch._jit_internal (optional)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), dill._objects (optional), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.stats._stats_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.io._netcdf (top-level), pygame.surfarray (top-level), nltk.parse.transitionparser (optional), statsmodels.formula.formulatools (delayed), scipy.signal._spline_filters (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level), statsmodels.tsa.adfvalues (top-level), statsmodels.stats._lilliefors_critical_values (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), dill._dill (delayed), scipy.optimize._minpack_py (top-level), scipy.io._netcdf (top-level), torch._dynamo.variables.misc (optional), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), _pytest.python_api (conditional), IPython.core.magics.namespace (delayed, conditional, optional), dill._dill (delayed), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pandas.compat.numpy.function (top-level), scipy.io._mmio (top-level), spacy.tokens._serialize (top-level), pygame.surfarray (top-level), pytesseract.pytesseract (optional), scipy._lib.array_api_compat.numpy._typing (top-level), pyqtgraph.debug (top-level), seaborn._core.typing (top-level), seaborn._marks.base (top-level), seaborn._stats.density (top-level), pyqtgraph.flowchart.Flowchart (top-level)
excluded module named pydoc - imported by module_compatibility (optional), numpy.lib.utils (delayed), _sitebuiltins (delayed), IPython.core.tbtools (top-level), jedi.api.keywords (top-level), scipy._lib._docscrape (top-level), sympy.printing.pretty.pretty (delayed), pyarrow.vendored.docscrape (top-level), rich.markdown (conditional), joblib.memory (top-level), nltk.util (top-level), pyqtgraph.parametertree.interactive (top-level), seaborn._docstrings (top-level), seaborn.external.docscrape (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level), dill._dill (delayed), dill._objects (optional)
excluded module named doctest - imported by module_compatibility (optional), numpy.testing._private.utils (delayed), pytz (delayed), Cython.Compiler.Visitor (conditional), _pytest.debugging (delayed), _pytest.doctest (delayed, conditional), tornado.util (delayed), tornado.iostream (delayed), tornado.httputil (delayed), pickletools (delayed), mpmath.calculus.quadrature (conditional), mpmath.calculus.inverselaplace (conditional), mpmath.calculus.optimization (conditional), mpmath.calculus.odes (conditional), mpmath.matrices.matrices (conditional), mpmath.identification (conditional), mpmath.ctx_mp (conditional), mpmath (delayed), sympy.testing.runtests (top-level), rsa.common (conditional), rsa.transform (conditional), rsa.prime (conditional), rsa.parallel (conditional), rsa.key (conditional), rsa.pkcs1 (conditional), rsa (conditional), nltk.parse.malt (conditional), lxml.doctestcompare (top-level)
excluded module named difflib - imported by module_compatibility (optional), numpy.testing._private.utils (delayed), traitlets.config.configurable (delayed, conditional), _pytest.assertion.util (delayed), parso.python.diff (top-level), jedi.api.refactoring (top-level), click.parser (delayed, conditional), black.output (delayed), black.ranges (top-level), yapf.yapflib.yapf_api (top-level), torch.serialization (top-level), torch.jit._trace (delayed, conditional), torch._dynamo.repro.after_dynamo (delayed), wasabi.util (top-level), sklearn.utils._testing (top-level), transformers.agents.python_interpreter (top-level), torch.onnx._internal.fx._pass (top-level), torch.testing._internal.optests.generate_tests (top-level), pip._internal.commands (delayed), lxml.html.diff (top-level), torch._numpy.testing.utils (delayed), torch.onnx.verification (top-level)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
excluded module named unittest - imported by numpy.testing (top-level), numpy.testing._private.utils (top-level), Cython.TestUtils (top-level), _pytest.debugging (top-level), tornado.util (conditional), tornado.httputil (conditional), torch.utils._config_module (top-level), torch._inductor.utils (top-level), torch._dynamo.trace_rules (top-level), torch._dynamo.variables.tensor (top-level), torch._dynamo.testing (top-level), torch.testing._internal.common_utils (top-level), pdfminer.ccitt (delayed, conditional), paddle.jit.sot.opcode_translator.skip_files (top-level), sklearn.utils._testing (top-level), torch.testing._internal.common_cuda (top-level), torch.testing._internal.common_device_type (top-level), torch._dynamo.variables.higher_order_ops (delayed, conditional, optional), torch._inductor.compile_fx (top-level), torch._inductor.compile_fx_ext (delayed, conditional), torch._dynamo.backends.distributed (top-level), torch.testing._internal.optests.generate_tests (top-level), _pytest.unittest (delayed, conditional), spacy.tests.serialize.test_resource_warning (top-level), spacy.tests.test_language (top-level), torch._inductor.aoti_eager (top-level), torch._numpy.testing.utils (top-level), torch.fx.passes.tests.test_pass_manager (top-level), torch.testing._internal.opinfo.core (top-level), torch.testing._internal.opinfo.definitions._masked (top-level), torch.testing._internal.common_methods_invocations (top-level), torch.testing._internal.opinfo.definitions.linalg (top-level), torch.testing._internal.opinfo.definitions.special (top-level), torch.testing._internal.opinfo.definitions.fft (top-level), torch.testing._internal.opinfo.definitions.signal (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.common_fsdp (top-level), torch.testing._internal.common_modules (top-level), torch.testing._internal.common_nn (top-level), torch.testing._internal.common_optimizers (top-level), torch.testing._internal.common_quantization (top-level), torch.testing._internal.distributed.distributed_test (top-level), torch.testing._internal.distributed.rpc.rpc_test (top-level), torch.testing._internal.distributed.rpc_utils (top-level), torch.testing._internal.hop_db (top-level), torch.testing._internal.inductor_utils (top-level), torch.testing._internal.torchbind_impls (delayed), torch.testing._internal.triton_utils (top-level)
missing module named numpy.typing._UIntLike_co - imported by numpy.typing (conditional), statsmodels.tools.typing (conditional)
missing module named numpy.typing._FloatLike_co - imported by numpy.typing (conditional), statsmodels.tools.typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy.stats._mstats_extras (top-level), scipy.optimize._lbfgsb_py (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.signal._spline_filters (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), pygame.surfarray (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), dill._objects (optional), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named numpy.uint - imported by numpy (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), trio._file_io (conditional), trio._path (conditional), prompt_toolkit.eventloop.inputhook (conditional), torch.utils._backport_slots (conditional), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), httpx._transports.wsgi (conditional), cloudpathlib.cloudpath (conditional), jaraco.collections (conditional), pip._vendor.pkg_resources (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional), pip._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional), pip._vendor.platformdirs.android (delayed, conditional, optional)
missing module named 'unittest.mock' - imported by setuptools._distutils._msvccompiler (top-level), torch._guards (top-level), torch._export (top-level), torch._dispatch.python (top-level), torch._dynamo.symbolic_convert (top-level), torch._inductor.ir (top-level), torch._functorch.aot_autograd (top-level), torch._dynamo.eval_frame (top-level), torch._inductor.ops_handler (top-level), torch._inductor.dependencies (top-level), torch._inductor.codegen.cpp_utils (top-level), torch._inductor.debug (top-level), torch._dynamo.testing (top-level), torch._dynamo.variables.functions (top-level), torch.testing._internal.common_utils (top-level), datasets.utils.file_utils (top-level), torch.utils.data.datapipes.dataframe.dataframes (delayed), datasets.builder (top-level), paddle.utils.cpp_extension.cpp_extension (conditional), torch._dynamo.backends.common (top-level), torch._inductor.lowering (top-level), torch._inductor.select_algorithm (top-level), torch._inductor.codegen.cuda.cuda_template (top-level), torch._inductor.codegen.cpp_gemm_template (top-level), torch._inductor.codegen.cpp_template (top-level), torch._inductor.codegen.cuda.cutlass_utils (delayed), torch._inductor.codegen.rocm.rocm_template (top-level), torch._inductor.codegen.cpp_grouped_gemm_template (top-level), torch._functorch._aot_autograd.traced_function_transforms (top-level), torch._dynamo.test_minifier_common (top-level), torch._inductor.codegen.cpp_bmm_template (top-level), torch._inductor.codegen.cpp_flex_attention_template (top-level), torch._inductor.mock_cache (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.logging_utils (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pytz.tzinfo (optional), pyreadline3.keysyms.common (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional), IPython.utils._process_cli (top-level)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level), IPython.utils._process_cli (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional), pip._vendor.packaging._manylinux (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional), thinc.tests.regression.issue519.test_issue519 (optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named StringIO - imported by Cython.Compiler.Annotate (optional), six (conditional), simplejson.compat (conditional, optional), pydub.audio_segment (optional), srsly.ruamel_yaml.compat (conditional), nltk.corpus.reader.timit (delayed, optional), pip._vendor.urllib3.packages.six (conditional), pip._vendor.distlib.compat (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named defusedxml - imported by PIL.Image (optional), openpyxl.xml (delayed, optional)
missing module named shiboken2 - imported by pyqtgraph.Qt (conditional), matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), pyqtgraph.Qt (conditional, optional), pyqtgraph.debug (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named shiboken6 - imported by pyqtgraph.Qt (conditional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (top-level), spacy_pkuseg (top-level), spacy_pkuseg.trainer (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), paddle.dataset.flowers (top-level), jieba (delayed), transformers.data.processors.squad (top-level), multitasking (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), datasets.parallel.parallel (top-level), jieba (delayed, conditional), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), transformers.data.processors.squad (top-level), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), uvicorn.supervisors.multiprocess (top-level)
missing module named multiprocessing.Lock - imported by multiprocessing (top-level), gradio.flagging (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional), datasets.parallel.parallel (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level), wasabi.printer (top-level), paddle.distributed.parallel (top-level), paddle.distributed.fleet.base.role_maker (top-level), paddle.incubate.distributed.fleet.role_maker (top-level), paddle.distributed.launch.utils.kv_server (top-level), paddle.distributed.parallel_with_gloo (top-level), multitasking (top-level), spacy_pkuseg (top-level), spacy_pkuseg.trainer (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (delayed, conditional), black (delayed, conditional)
missing module named multiprocessing.Manager - imported by multiprocessing (top-level), black.concurrency (top-level), datasets.utils.py_utils (top-level), paddle.distributed.parallel (top-level), paddle.distributed.fleet.base.role_maker (top-level), paddle.incubate.distributed.fleet.role_maker (top-level), paddle.distributed.parallel_with_gloo (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional), multiprocess.resource_tracker (conditional), multiprocess.shared_memory (conditional)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib.parallel (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed), multiprocess.util (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level)
missing module named 'tkinter.messagebox' - imported by nltk.app.chartparser_app (top-level), nltk.app.chunkparser_app (delayed, optional), nltk.app.concordance_app (delayed, optional), nltk.app.rdparser_app (delayed, optional), nltk.app.srparser_app (delayed, optional), nltk.downloader (delayed, optional)
missing module named 'tkinter.filedialog' - imported by nltk.draw.util (top-level), nltk.app.chartparser_app (top-level), nltk.app.chunkparser_app (top-level)
missing module named 'tkinter.font' - imported by nltk.sem.drt (optional), nltk.app.chartparser_app (top-level), nltk.app.chunkparser_app (top-level), nltk.app.collocations_app (top-level), nltk.app.concordance_app (top-level), nltk.app.rdparser_app (top-level), nltk.app.srparser_app (top-level)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named nltk.corpus.CorpusReader - imported by nltk.corpus (delayed, conditional), nltk.corpus.reader.wordnet (delayed, conditional)
missing module named nltk.corpus.reader.CorpusReader - imported by nltk.corpus.reader (top-level), nltk.corpus.reader.wordnet (top-level), nltk.corpus.reader.lin (top-level), nltk.corpus.reader.sentiwordnet (top-level), nltk.corpus.reader.crubadan (top-level), nltk.corpus.reader.bcp47 (top-level)
missing module named pygame._common - imported by pygame.rect (top-level), pygame.rwobject (top-level), pygame.color (top-level), pygame.surface (top-level), pygame.display (top-level), pygame.draw (top-level), pygame.image (top-level), pygame.key (top-level), pygame.mask (top-level), pygame.transform (top-level), pygame.pixelcopy (top-level), pygame.pixelarray (top-level), pygame.font (top-level), pygame.mixer_music (top-level), pygame.mixer (top-level)
missing module named OpenGL - imported by pygame (delayed)
missing module named 'pygame.overlay' - imported by pygame (optional)
missing module named 'pygame.cdrom' - imported by pygame (conditional, optional)
missing module named ossaudiodev - imported by nltk.corpus.reader.timit (delayed, optional)
excluded module named tkinter - imported by ipykernel.eventloops (delayed), nltk.draw (optional), nltk.draw.cfg (top-level), nltk.draw.tree (top-level), nltk.draw.util (top-level), nltk.draw.table (top-level), nltk.sem.drt (delayed, optional), nltk.app (optional), nltk.app.chartparser_app (top-level), nltk.app.chunkparser_app (top-level), nltk.app.collocations_app (top-level), nltk.app.concordance_app (top-level), nltk.app.nemo_app (top-level), nltk.app.rdparser_app (top-level), nltk.app.srparser_app (top-level), nltk.downloader (optional), nltk (optional), roe_data_downloader_gui (top-level), unified_monthly_revenue_gui (top-level), mops_income_statement_gui (top-level), daily_trading_crawler_gui (top-level), stock_basic_info_gui (top-level), unified_stock_crawler (top-level)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), torch._functorch.partitioners (delayed), torch.fx.passes.graph_drawer (optional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional), multiprocess (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.io._harwell_boeing.hb (top-level), sklearn.cluster._spectral (top-level), sklearn.ensemble._gb (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level), scipy.io._harwell_boeing.hb (top-level)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named cupyx - imported by thinc.compat (optional), scipy._lib._array_api (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), opt_einsum.backends.jax (delayed, conditional), datasets.formatting.jax_formatter (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.generation.flax_utils (top-level), scipy._lib._array_api (delayed, conditional)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), opt_einsum.backends.cupy (delayed), srsly.msgpack._msgpack_numpy (optional), thinc.compat (optional), thinc.shims.pytorch (delayed, conditional), spacy.compat (optional), sklearn.utils._testing (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional), pyqtgraph.util.cupy_helper (delayed, conditional, optional), spacy.tests.test_misc (delayed, optional), thinc.tests.test_util (optional)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._spline_filters (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.optimize._optimize (top-level), scipy.linalg._decomp (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.io._mmio (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named scipy.sparse.linalg.matrix_power - imported by scipy.sparse.linalg (delayed), scipy.sparse._matrix (delayed)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.lil_matrix - imported by scipy.sparse (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.sparse.dia_matrix - imported by scipy.sparse (top-level), sklearn.cluster._bicluster (top-level)
missing module named scipy.sparse.sparray - imported by scipy.sparse (delayed), networkx.utils.backends (delayed), sklearn.utils.fixes (optional)
missing module named scipy.sparse.coo_array - imported by scipy.sparse (top-level), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), sklearn.utils._param_validation (top-level), sklearn.metrics.pairwise (top-level), sklearn.neighbors._base (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.ensemble._gb (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), sklearn.metrics._classification (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), sklearn.utils.fixes (optional)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), scipy._lib._array_api (delayed), pandas.core.dtypes.common (delayed, conditional, optional), sklearn.tree._classes (top-level), sklearn.ensemble._forest (top-level), sklearn.ensemble._gb (top-level), sklearn.ensemble._iforest (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.musicgen_melody.feature_extraction_musicgen_melody (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional)
missing module named 'torchvision.io' - imported by datasets.features.video (delayed, conditional), datasets.formatting.np_formatter (delayed, conditional), datasets.formatting.torch_formatter (delayed, conditional), datasets.formatting.tf_formatter (delayed, conditional), datasets.formatting.jax_formatter (delayed, conditional), torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython.Parser - imported by Cython.Compiler.Main (delayed, conditional, optional)
missing module named imp - imported by Cython.Build.Inline (conditional), cffi.verifier (conditional), cffi._imp_emulation (optional), pyximport._pyximport2 (top-level), simplejson.compat (conditional)
missing module named cStringIO - imported by Cython.StringIOTree (optional), cffi.ffiplatform (optional), simplejson.compat (conditional, optional), xlrd.timemachine (conditional), srsly.ruamel_yaml.compat (conditional)
missing module named pygments.lexers.CppLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.CythonLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level), transformers.agents.agents (conditional)
missing module named _winreg - imported by platform (delayed, optional), tzlocal.win32 (optional), selenium.webdriver.firefox.firefox_binary (delayed, optional), pygments.formatters.img (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed, optional), Cython.Compiler.Annotate (delayed, optional), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (conditional), transformers.agents.agents (conditional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named htmlentitydefs - imported by pip._vendor.distlib.compat (conditional), lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named urlparse - imported by pip._vendor.distlib.compat (conditional), lxml.ElementInclude (optional), lxml.html.html5parser (optional), spacy_pkuseg.download (conditional, optional)
missing module named urllib2 - imported by pip._vendor.distlib.compat (conditional), lxml.ElementInclude (optional), lxml.html.html5parser (optional), spacy_pkuseg.download (conditional, optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), sortedcontainers.sorteddict (optional), parso.python.tree (optional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), srsly.ruamel_yaml.compat (conditional), srsly.ruamel_yaml.comments (conditional), html5lib._utils (optional), html5lib._trie._base (optional), peewee (optional), patsy.constraint (optional), pip._vendor.urllib3._collections (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named collections.MutableMapping - imported by collections (optional), h2.settings (optional), srsly.ruamel_yaml.compat (conditional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional), pip._vendor.urllib3._collections (optional), pip._vendor.distlib.compat (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named pythran - imported by Cython.Build.Dependencies (optional), Cython.Compiler.Pythran (optional)
missing module named collections.Iterable - imported by collections (optional), Cython.Build.Dependencies (optional)
missing module named pyximport.test - imported by pyximport (conditional), pyximport.pyxbuild (conditional)
missing module named sqlite3.OperationalError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named sqlite3.DatabaseError - imported by sqlite3 (optional), IPython.core.history (optional)
missing module named 'IPython.config' - imported by IPython.core.history (conditional)
missing module named nbformat - imported by IPython.core.magics.basic (delayed), IPython.core.interactiveshell (delayed, conditional), thinc.tests.test_examples (delayed)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
excluded module named pdb - imported by _pytest.hookspec (conditional), _pytest.debugging (delayed, conditional), IPython.core.debugger (top-level), prompt_toolkit.application.application (delayed), IPython.core.magics.execution (top-level), IPython.terminal.debugger (conditional), ipykernel.kernelapp (delayed), sympy.testing.runtests (top-level), torch.distributed (top-level), paddle.jit.dy2static.convert_call_func (top-level)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed), sklearn.utils._testing (delayed, optional)
missing module named exceptiongroup - imported by _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), trio._util (conditional), trio._core._run (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional), trio._channel (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional), _pytest.unittest (conditional)
missing module named 'curio.meta' - imported by sniffio._impl (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional), sqlalchemy.util.langhelpers (conditional)
missing module named zope - imported by _pytest.unittest (delayed, conditional)
missing module named 'twisted.trial' - imported by _pytest.unittest (delayed, conditional)
missing module named 'argcomplete.completers' - imported by _pytest._argcomplete (conditional, optional)
missing module named pexpect - imported by IPython.utils._process_posix (delayed, conditional), _pytest.pytester (conditional), _pytest.legacypath (conditional), jupyter_client.ssh.tunnel (optional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), socks (optional), peewee (conditional, optional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), pip._vendor.distlib.compat (optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional), patsy.compat_ordereddict (optional), pip._vendor.distlib.compat (optional)
missing module named cPickle - imported by IPython.external.pickleshare (optional), pycparser.ply.yacc (delayed, optional), spacy.compat (optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by trio._core._run (delayed), torch.testing._internal.common_utils (optional), spacy.tests.conftest (top-level), spacy.tests.parser.test_nn_beam (top-level), spacy.tests.pipeline.test_edit_tree_lemmatizer (top-level), spacy.tests.tokenizer.test_explain (top-level), thinc.tests.backends.test_ops (top-level), thinc.tests.conftest (top-level), thinc.tests.layers.test_linear (top-level), thinc.tests.layers.test_uniqued (top-level), thinc.tests.shims.test_pytorch_grad_scaler (top-level), thinc.tests.test_util (top-level), torch.testing._internal.hypothesis_utils (top-level)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional), srsly.ruamel_yaml.comments (conditional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional), hyperframe.flags (optional), srsly.ruamel_yaml.comments (conditional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional), srsly.ruamel_yaml.compat (conditional)
missing module named OpenSSL - imported by trio._dtls (delayed, conditional), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named pgen2 - imported by blib2to3.pgen2.conv (top-level)
missing module named uvloop - imported by black.concurrency (delayed, optional), aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named tokenize_rt - imported by black.handle_ipynb_magics (delayed)
missing module named jupyter_ai - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named jupyter_ai_magics - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named 'astroid.node_classes' - imported by asttokens.astroid_compat (optional)
missing module named 'astroid.nodes' - imported by asttokens.astroid_compat (optional)
missing module named astroid - imported by asttokens.astroid_compat (optional), asttokens.util (optional)
missing module named pathlib2 - imported by IPython.external.pickleshare (optional), paddle.hapi.hub (delayed, optional)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named 'cython.cimports' - imported by zmq.backend.cython._zmq (top-level)
missing module named gevent - imported by apscheduler.executors.gevent (optional), apscheduler.schedulers.gevent (optional), zmq.green.core (top-level), zmq.green.poll (top-level)
missing module named 'gevent.core' - imported by zmq.green.core (delayed, optional)
missing module named 'gevent.hub' - imported by zmq.green.core (top-level)
missing module named 'gevent.event' - imported by apscheduler.schedulers.gevent (optional), zmq.green.core (top-level)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.proxy - imported by zmq.backend (top-level), zmq.sugar (top-level)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional), fsspec.implementations.sftp (top-level)
missing module named cloudpickle - imported by ipykernel.pickleutil (delayed)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (optional)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
excluded module named PySide6 - imported by ipykernel.eventloops (delayed, conditional, optional)
excluded module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
excluded module named PyQt5 - imported by ipykernel.eventloops (delayed, conditional, optional), PyQt5.QtCore (top-level), PyQt5.QtGui (top-level), PyQt5.QtWidgets (top-level)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by ipykernel.eventloops (delayed), IPython.lib.guisupport (delayed)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named __builtin__ - imported by Cython.Shadow (optional), Cython.Utils (optional), Cython.Compiler.Errors (optional), Cython.Compiler.Main (optional), Cython.Compiler.Scanning (delayed, optional), Cython.Compiler.Symtab (optional), Cython.Compiler.Code (optional), Cython.Compiler.ExprNodes (optional), Cython.Compiler.Optimize (optional), Cython.Distutils.build_ext (optional), Cython.Build.Inline (delayed, optional), faiss.swigfaiss_avx2 (optional), faiss.swigfaiss (optional), sentencepiece (optional), pip._vendor.distlib.compat (conditional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'triton.runtime' - imported by torch.utils._triton (delayed), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.runtime.runtime_utils (delayed, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.select_algorithm (delayed, optional), torch._inductor.fx_passes.reinplace (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional), torch._inductor.ir (delayed), torch._inductor.codecache (delayed, conditional), torch._library.triton (delayed), torch._inductor.utils (delayed)
missing module named 'triton.compiler' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.async_compile (delayed, optional), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional), torch._inductor.utils (delayed)
missing module named 'triton.backends' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.utils (delayed)
missing module named 'triton.language' - imported by torch.utils._triton (delayed, conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed), torch._inductor.codegen.triton_split_scan (delayed), transformers.integrations.finegrained_fp8 (conditional), torch.sparse._triton_ops (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'triton.tools' - imported by torch.utils._triton (delayed, conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional)
missing module named triton - imported by torch.utils._triton (delayed, optional), torch._inductor.runtime.hints (conditional), torch._dynamo.logging (conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed, conditional), transformers.integrations.finegrained_fp8 (conditional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm (optional), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.ir (optional), torch._functorch._aot_autograd.autograd_cache (delayed, conditional), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch.testing._internal.inductor_utils (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.cuda._memory_viz (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'scipy.optimize._highspy._core.simplex_constants' - imported by scipy.optimize._linprog_highs (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level), scipy.signal._spline_filters (top-level)
missing module named scipy.optimize.root_scalar - imported by scipy.optimize (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._stats_py (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.optimize.brentq - imported by scipy.optimize (delayed), scipy.integrate._ivp.ivp (delayed), scipy.stats._binomtest (top-level), scipy.stats._odds_ratio (top-level), statsmodels.genmod.generalized_linear_model (delayed, conditional)
missing module named scipy.optimize.OptimizeResult - imported by scipy.optimize (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.ivp (top-level), scipy._lib.cobyqa.main (top-level), scipy._lib.cobyqa.problem (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._lsq.bvls (top-level), scipy.optimize._spectral (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level), scipy.optimize._qap (top-level), scipy.optimize._direct_py (top-level)
missing module named scipy.optimize.minimize_scalar - imported by scipy.optimize (top-level), scipy.interpolate._bsplines (top-level), scipy.stats._multicomp (top-level)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional), filelock._unix (conditional, optional), pty (delayed, optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named optree - imported by torch.utils._cxx_pytree (top-level), torch._dynamo.polyfills.pytree (conditional)
missing module named 'hypothesis.strategies' - imported by pydantic.v1._hypothesis_plugin (top-level), spacy.tests.parser.test_nn_beam (top-level), thinc.tests.strategies (top-level), spacy.tests.pipeline.test_edit_tree_lemmatizer (top-level), spacy.tests.tokenizer.test_explain (top-level), thinc.tests.backends.test_ops (top-level), thinc.tests.layers.test_uniqued (top-level), thinc.tests.shims.test_pytorch_grad_scaler (top-level), torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by thinc.tests.strategies (top-level), torch.testing._internal.hypothesis_utils (top-level)
missing module named pytest_subtests - imported by torch.testing._internal.opinfo.core (delayed, conditional, optional)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._spline_filters (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.complex64 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.signal._spline_filters (top-level)
missing module named numpy.arctan - imported by numpy (top-level), scipy.signal._spline_filters (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), scipy.signal._waveforms (top-level), statsmodels.sandbox.distributions.multivariate (top-level), statsmodels.datasets.anes96.data (top-level)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.device_mesh (conditional), torch.distributed.distributed_c10d (top-level), torch._inductor.codegen.wrapper (delayed, optional), torch.distributed.tensor._collective_utils (top-level), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed._symmetric_memory (top-level), torch.distributed.constants (top-level), torch.distributed._tools.fake_collectives (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.common_distributed (top-level), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named torchvision - imported by datasets.features.video (delayed, conditional), transformers.image_utils (conditional), torch.testing._internal.common_quantization (optional), torch.testing._internal.distributed.distributed_test (optional)
missing module named objgraph - imported by torch.testing._internal.common_utils (delayed, conditional, optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named xmlrunner - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level), transformers.quantizers.base (conditional)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named bitsandbytes - imported by accelerate.utils.imports (delayed), accelerate.utils.bnb (delayed), transformers.utils.import_utils (delayed), transformers.models.rwkv.modeling_rwkv (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named intel_extension_for_pytorch - imported by accelerate.utils.imports (delayed, conditional), accelerate.accelerator (delayed, conditional), transformers.utils.import_utils (delayed, conditional), transformers.trainer (delayed)
missing module named 'habana_frameworks.torch' - imported by accelerate.utils.imports (delayed, conditional), transformers.utils.import_utils (delayed, conditional)
missing module named torch_musa - imported by accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional), transformers.utils.import_utils (delayed)
missing module named torch_mlu - imported by accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional), transformers.utils.import_utils (delayed)
missing module named torch_npu - imported by accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional), accelerate.accelerator (conditional), transformers.utils.import_utils (delayed), transformers.modeling_flash_attention_utils (conditional), transformers.integrations.npu_flash_attention (conditional)
missing module named torch_xla - imported by accelerate.utils.imports (conditional, optional), transformers.pytorch_utils (delayed, conditional), transformers.utils.import_utils (delayed), transformers.trainer (conditional), huggingface_hub.serialization._torch (delayed, conditional), torch._tensor (delayed, conditional)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named 'mlx.core' - imported by transformers.utils.generic (delayed)
missing module named tensorflow - imported by opt_einsum.backends.tensorflow (delayed, conditional), onnxruntime.transformers.machine_info (delayed, optional), thinc.compat (delayed), datasets.utils.py_utils (delayed, conditional), datasets.features.features (delayed, conditional), datasets.formatting.tf_formatter (delayed, conditional), datasets.utils.tf_utils (delayed, conditional), datasets.arrow_dataset (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.utils.generic (delayed, conditional), transformers.models.encoder_decoder.modeling_tf_encoder_decoder (top-level), transformers.modeling_tf_outputs (top-level), transformers.modeling_tf_utils (top-level), transformers.activations_tf (top-level), transformers.tf_utils (top-level), safetensors.tensorflow (top-level), transformers.trainer_utils (delayed, conditional), transformers.modelcard (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.models.roformer.modeling_roformer (delayed, optional), transformers.models.roformer.modeling_tf_roformer (top-level), transformers.onnx.convert (delayed), transformers.models.albert.modeling_albert (delayed, optional), transformers.models.albert.modeling_tf_albert (top-level), transformers.models.bart.modeling_tf_bart (top-level), transformers.models.bert.modeling_bert (delayed, optional), transformers.models.bert.modeling_tf_bert (top-level), transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.big_bird.modeling_big_bird (delayed, optional), transformers.models.blenderbot_small.modeling_tf_blenderbot_small (top-level), transformers.models.blenderbot.modeling_tf_blenderbot (top-level), transformers.models.blip.modeling_tf_blip (top-level), transformers.models.blip.modeling_tf_blip_text (top-level), transformers.models.camembert.modeling_tf_camembert (top-level), transformers.models.canine.modeling_canine (delayed, optional), transformers.models.clip.modeling_tf_clip (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.convbert.modeling_convbert (delayed, optional), transformers.models.convbert.modeling_tf_convbert (top-level), transformers.models.convnext.modeling_tf_convnext (top-level), transformers.models.convnextv2.modeling_tf_convnextv2 (top-level), transformers.models.ctrl.modeling_tf_ctrl (top-level), transformers.models.cvt.modeling_tf_cvt (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.modeling_tf_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.distilbert.modeling_tf_distilbert (top-level), transformers.models.dpr.modeling_tf_dpr (top-level), transformers.models.electra.modeling_electra (delayed, optional), transformers.models.electra.modeling_tf_electra (top-level), transformers.models.esm.modeling_tf_esm (top-level), transformers.models.flaubert.modeling_tf_flaubert (top-level), transformers.models.funnel.modeling_funnel (delayed, optional), transformers.models.funnel.modeling_tf_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (delayed, optional), transformers.models.gpt2.modeling_tf_gpt2 (top-level), transformers.models.gpt2.tokenization_gpt2_tf (top-level), transformers.models.gpt_neo.modeling_gpt_neo (delayed, optional), transformers.models.gptj.modeling_tf_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.groupvit.modeling_tf_groupvit (top-level), transformers.models.hubert.modeling_tf_hubert (top-level), transformers.models.idefics.modeling_tf_idefics (top-level), transformers.models.idefics.perceiver_tf (top-level), transformers.models.idefics.vision_tf (top-level), transformers.models.idefics.processing_idefics (conditional), transformers.models.imagegpt.modeling_imagegpt (delayed, optional), transformers.models.layoutlm.modeling_tf_layoutlm (top-level), transformers.models.layoutlmv3.modeling_tf_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_tf_xlm_roberta (top-level), transformers.models.led.modeling_tf_led (top-level), transformers.models.longformer.modeling_tf_longformer (top-level), transformers.models.lxmert.modeling_lxmert (delayed, optional), transformers.models.lxmert.modeling_tf_lxmert (top-level), transformers.models.marian.modeling_tf_marian (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.mbart.modeling_tf_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (delayed, optional), transformers.models.mistral.modeling_tf_mistral (top-level), transformers.models.mobilebert.modeling_mobilebert (delayed, optional), transformers.models.mobilebert.modeling_tf_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (delayed, optional), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (delayed, optional), transformers.models.mobilevit.modeling_tf_mobilevit (top-level), transformers.models.mpnet.modeling_tf_mpnet (top-level), transformers.models.t5.modeling_t5 (delayed, optional), transformers.models.t5.modeling_tf_t5 (top-level), transformers.models.mt5.modeling_mt5 (delayed, optional), transformers.models.openai.modeling_tf_openai (top-level), transformers.models.opt.modeling_tf_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_tf_pegasus (top-level), transformers.models.rag.modeling_tf_rag (top-level), transformers.models.regnet.modeling_tf_regnet (top-level), transformers.models.rembert.modeling_rembert (delayed, optional), transformers.models.rembert.modeling_tf_rembert (top-level), transformers.models.resnet.modeling_tf_resnet (top-level), transformers.models.roberta.modeling_tf_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_tf_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (delayed, optional), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_tf_sam (top-level), transformers.models.sam.processing_sam (conditional), transformers.models.segformer.modeling_tf_segformer (top-level), transformers.models.speech_to_text.modeling_tf_speech_to_text (top-level), transformers.models.swiftformer.modeling_tf_swiftformer (top-level), transformers.models.tapas.modeling_tapas (delayed, optional), transformers.models.tapas.modeling_tf_tapas (top-level), transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.wav2vec2.modeling_tf_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_tf_whisper (top-level), transformers.generation.tf_logits_process (top-level), transformers.models.xglm.modeling_tf_xglm (top-level), transformers.models.xlm.modeling_tf_xlm (top-level), transformers.models.xlnet.modeling_tf_xlnet (top-level), transformers.models.xlnet.modeling_xlnet (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.generation.tf_utils (top-level), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.glue (conditional), transformers.data.processors.squad (conditional), transformers.pipelines.base (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text2text_generation (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.token_classification (conditional), transformers.pipelines (conditional), huggingface_hub.keras_mixin (conditional, optional), huggingface_hub.serialization._tensorflow (delayed, conditional), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.efficientformer.modeling_tf_efficientformer (top-level), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.deprecated.nezha.modeling_nezha (delayed, optional), transformers.models.deprecated.qdqbert.modeling_qdqbert (delayed, optional), transformers.models.deprecated.realm.modeling_realm (delayed, optional), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl (top-level), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities (top-level), transformers.training_args_tf (conditional), transformers.keras_callbacks (top-level), transformers.optimization_tf (top-level), thinc.tests.conftest (delayed, optional), thinc.tests.layers.test_tensorflow_wrapper (delayed)
missing module named 'jax.core' - imported by transformers.utils.generic (delayed, conditional)
missing module named UserDict - imported by pytz.lazy (optional), simplejson.ordered_dict (top-level)
missing module named zstandard - imported by urllib3.response (optional), urllib3.util.request (optional), fsspec.compression (optional), httpx._decoders (optional), datasets.utils.extract (delayed), smart_open.compression (delayed)
missing module named brotli - imported by urllib3.response (optional), urllib3.util.request (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.response (optional), urllib3.util.request (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level), pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named compression - imported by urllib3.response (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1._hypothesis_plugin (optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level), thinc.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level), thinc.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level), thinc.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level), thinc.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level), thinc.mypy (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level), thinc.tests.mypy.test_mypy (delayed)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional), seaborn.widgets (optional), finlab.crawler (top-level), pip._vendor.rich.live (delayed, conditional, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named pydantic.ConstrainedStr - imported by pydantic (optional), spacy.schemas (optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic.deprecated.copy_internals (delayed, conditional), huggingface_hub._webhooks_payload (conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), fastapi.openapi.utils (top-level), gradio.data_classes (top-level), gradio.server_messages (top-level), confection (optional), spacy.schemas (optional), spacy.pipeline._edit_tree_internals.schemas (optional), weasel.schemas (optional), openai.resources.beta.realtime.realtime (top-level), thinc.tests.test_config (optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional), pip._vendor.requests.cookies (optional), pip._vendor.distlib.util (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named 'python_socks._types' - imported by urllib3.contrib._socks_override (top-level), websocket._http (optional)
missing module named 'python_socks._protocols' - imported by urllib3.contrib._socks_override (top-level)
missing module named 'python_socks._helpers' - imported by urllib3.contrib._socks_override (top-level)
missing module named 'python_socks._errors' - imported by urllib3.contrib._socks_override (top-level), websocket._http (optional)
missing module named 'python_socks._connectors' - imported by urllib3.contrib._socks_override (top-level)
missing module named 'python_socks.sync' - imported by urllib3.contrib.socks (optional), websockets.sync.client (optional), websocket._http (optional)
missing module named python_socks - imported by urllib3.contrib.socks (optional), urllib3.contrib._socks_override (top-level), websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional), spacy.util (delayed, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), pyarrow (optional), tqdm.version (optional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional), seaborn.external.docscrape (delayed, conditional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._parallel_backends (delayed, optional), joblib._dask (optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named js - imported by fsspec.implementations.http_sync (delayed, optional), urllib3.contrib.emscripten.fetch (top-level)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level), uvicorn.workers (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by joblib._dask (optional), fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by peewee (optional), sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named 'pymysql.constants' - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named psycopg2 - imported by peewee (optional), sqlalchemy (top-level), sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named MySQLdb - imported by peewee (optional), sqlalchemy (top-level)
missing module named pysqlite2 - imported by peewee (optional), sqlalchemy (top-level)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), pip._vendor.urllib3.contrib.pyopenssl (optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level), pyqtgraph.util.numba_helper (delayed, conditional, optional), pyqtgraph.functions_numba (top-level)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named hf_xet - imported by huggingface_hub.file_download (delayed, optional), huggingface_hub._commit_api (delayed)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named 'torch_xla.core' - imported by accelerate.optimizer (conditional), accelerate.state (conditional), accelerate.utils.operations (conditional), accelerate.utils.other (conditional), accelerate.utils.random (conditional), accelerate.checkpointing (conditional), accelerate.accelerator (conditional), transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional), torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named tf_keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional), huggingface_hub.keras_mixin (conditional, optional)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named 'twine.settings' - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named 'twine.exceptions' - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named twine - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named 'werkzeug.routing' - imported by websockets.asyncio.router (top-level), websockets.sync.router (top-level)
missing module named 'werkzeug.exceptions' - imported by websockets.sync.router (top-level)
missing module named werkzeug - imported by websockets.asyncio.router (top-level)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named diffusers - imported by gradio.pipelines_utils (delayed, optional), gradio.pipelines (conditional), gradio.interface (conditional)
missing module named transformers_js_py - imported by gradio.pipelines_utils (delayed, optional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named pyaudioop - imported by pydub.utils (optional)
missing module named 'mcp.server' - imported by gradio.mcp (top-level)
missing module named mcp - imported by gradio.mcp (top-level)
missing module named spaces - imported by gradio.blocks (optional)
missing module named altair - imported by gradio.components.plot (delayed)
missing module named 'bokeh.embed' - imported by gradio.components.plot (delayed, conditional)
missing module named bokeh - imported by gradio.components.plot (delayed, optional)
missing module named polars - imported by gradio.components.dataframe (delayed, conditional), datasets.formatting.polars_formatter (delayed, conditional), datasets.arrow_dataset (delayed, conditional), datasets.iterable_dataset (delayed, conditional), talib (optional)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named 'gunicorn.arbiter' - imported by uvicorn.workers (top-level)
missing module named watchfiles - imported by uvicorn.supervisors.watchfilesreload (top-level)
missing module named httptools - imported by uvicorn.protocols.http.httptools_impl (top-level), uvicorn.protocols.http.auto (optional)
missing module named 'pyodide.http' - imported by gradio.analytics (optional)
missing module named 'authlib.integrations' - imported by gradio.oauth (delayed, optional)
missing module named authlib - imported by gradio.oauth (delayed, optional)
missing module named 'itsdangerous.exc' - imported by starlette.middleware.sessions (top-level)
missing module named itsdangerous - imported by starlette.middleware.sessions (top-level)
missing module named pyodide - imported by gradio.processing_utils (conditional)
missing module named 'tensorboard.summary' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard (top-level)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional), paddle.distributed.fleet.base.distributed_strategy (delayed, conditional), proto.marshal.compat (optional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named ml_dtypes - imported by onnx.reference.custom_element_types (optional)
missing module named re2 - imported by onnx.reference.ops.op_regex_full_match (delayed, optional)
missing module named 'onnx.onnx_cpp2py_export.defs' - imported by onnx.defs (top-level), onnx.reference.ops._op_list (top-level)
missing module named 'onnx.onnx_cpp2py_export.checker' - imported by onnx.external_data_helper (top-level), onnx.checker (top-level), onnx.model_container (top-level)
missing module named 'onnx.onnx_cpp2py_export.version_converter' - imported by onnx.version_converter (top-level)
missing module named 'onnx.onnx_cpp2py_export.shape_inference' - imported by onnx.shape_inference (top-level)
missing module named 'onnx.onnx_cpp2py_export.printer' - imported by onnx.printer (top-level)
missing module named 'onnx.onnx_cpp2py_export.parser' - imported by onnx.parser (top-level)
missing module named 'tensorboard.plugins' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.compat' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard._onnx_graph (top-level), torch.utils.tensorboard._pytorch_graph (top-level), torch.utils.tensorboard._proto_graph (top-level), torch.utils.tensorboard.summary (top-level)
missing module named tensorboard - imported by torch.utils.tensorboard (top-level)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), accelerate.tracking (delayed, optional), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named '__pypy__.builders' - imported by msgpack.fallback (conditional), pip._vendor.msgpack.fallback (conditional)
missing module named __pypy__ - imported by msgpack.fallback (conditional), pip._vendor.msgpack.fallback (conditional)
missing module named 'jax.numpy' - imported by datasets.features.features (delayed, conditional), datasets.formatting.jax_formatter (delayed), datasets.arrow_dataset (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.utils.generic (delayed, conditional), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.generation.flax_utils (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named transformers.models.pop2piano.Pop2PianoTokenizer - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoProcessor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoFeatureExtractor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named 'tensorflow.keras' - imported by transformers.optimization_tf (optional), thinc.tests.layers.test_mnist (delayed)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional)
missing module named 'lz4.frame' - imported by datasets.utils.extract (delayed), joblib.compressor (optional)
missing module named py7zr - imported by datasets.utils.extract (delayed)
missing module named rarfile - imported by datasets.utils.extract (delayed)
missing module named av - imported by datasets.features.video (delayed), transformers.image_utils (delayed), transformers.pipelines.video_classification (conditional)
missing module named librosa - imported by datasets.features.audio (delayed, optional), transformers.audio_utils (conditional)
missing module named soundfile - imported by datasets.features.audio (delayed, optional), transformers.agents.agent_types (conditional)
missing module named 'tensorflow.python' - imported by datasets.utils.py_utils (delayed, conditional), torch.contrib._tensorboard_vis (optional)
missing module named 'nbconvert.preprocessors' - imported by thinc.tests.test_examples (delayed)
missing module named nbconvert - imported by thinc.tests.test_examples (delayed)
missing module named ml_datasets - imported by spacy.cli.profile (delayed, conditional, optional), thinc.tests.layers.test_basic_tagger (delayed), thinc.tests.layers.test_mnist (delayed), thinc.tests.model.test_model (delayed)
missing module named mock - imported by spacy.tests.doc.test_underscore (top-level), spacy.tests.matcher.test_dependency_matcher (top-level), spacy.tests.matcher.test_matcher_api (top-level), spacy.tests.matcher.test_phrase_matcher (top-level), spacy.tests.pipeline.test_analysis (top-level), thinc.tests.layers.test_linear (top-level), thinc.tests.layers.test_with_debug (top-level)
missing module named mxnet - imported by thinc.compat (delayed), thinc.tests.layers.test_mxnet_wrapper (delayed)
missing module named pathy - imported by thinc.tests.conftest (delayed)
missing module named thinc_bigendian_ops - imported by thinc.backends (delayed, optional), thinc.tests.backends.test_ops (delayed, optional)
missing module named thinc_apple_ops - imported by thinc.backends.mps_ops (conditional, optional), thinc.backends (delayed, optional), thinc.tests.backends.test_ops (delayed, optional)
missing module named 'mypy.subtypes' - imported by thinc.mypy (top-level)
missing module named 'mypy.errors' - imported by thinc.mypy (top-level)
missing module named 'mypy.checker' - imported by thinc.mypy (top-level)
missing module named 'cupy.cuda' - imported by thinc.model (delayed), spacy.compat (optional), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named os_signpost - imported by thinc.compat (optional)
missing module named 'tensorflow.experimental' - imported by thinc.compat (delayed), transformers.models.sam.image_processing_sam (conditional)
missing module named 'cupy.cublas' - imported by thinc.compat (optional)
missing module named pep517 - imported by catalogue._importlib_metadata (delayed)
missing module named collections.Sized - imported by collections (conditional), srsly.ruamel_yaml.comments (conditional)
missing module named copy_reg - imported by srsly.ruamel_yaml.representer (conditional), spacy.compat (optional)
missing module named collections.Hashable - imported by collections (conditional), srsly.ruamel_yaml.compat (conditional)
missing module named _ruamel_yaml - imported by srsly.ruamel_yaml.main (optional)
missing module named configobj - imported by srsly.ruamel_yaml.util (delayed)
missing module named 'paddle.fluid.initializer' - imported by jieba.lac_small.creator (top-level), jieba.lac_small.nets (top-level)
missing module named 'paddle.base.libpaddle.pir' - imported by paddle.pir (top-level), paddle.pir.core (top-level), paddle.autograd.ir_backward (top-level), paddle.static.nn.control_flow (top-level), paddle.decomposition.decomp (top-level)
missing module named 'paddle.fluid.transpiler' - imported by paddle.distributed.fleet.meta_optimizers.sharding_optimizer (delayed, conditional)
missing module named sqlite3.NotSupportedError - imported by sqlite3 (top-level), paddle.nn.clip (top-level)
missing module named mpi4py - imported by paddle.incubate.distributed.fleet.role_maker (delayed)
missing module named org - imported by setuptools.sandbox (conditional)
missing module named paddle.distributed.fleet.proto.fleet_executor_desc_pb2 - imported by paddle.distributed.fleet.proto (delayed), paddle.base.executor (delayed)
missing module named proto.trainer_desc_pb2 - imported by proto (delayed), paddle.base.trainer_desc (delayed)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named paddleaudio - imported by paddle.audio.backends.init_backend (delayed, conditional, optional)
missing module named etcd3 - imported by paddle.distributed.fleet.elastic (delayed), paddle.distributed.launch.controllers.master (delayed), paddle.distributed.launch.utils.etcd_client (top-level), apscheduler.jobstores.etcd (optional)
missing module named paddle.fluid - imported by paddle (delayed), paddle.distributed.fleet.meta_optimizers.sharding_optimizer (delayed), jieba.lac_small.predict (top-level), jieba.lac_small.utils (top-level), jieba.lac_small.creator (top-level), jieba.lac_small.nets (top-level), jieba.lac_small.reader_small (top-level)
missing module named pyvi - imported by spacy.lang.vi (delayed, conditional, optional)
missing module named pymorphy3 - imported by spacy.lang.ru.lemmatizer (delayed, conditional, optional), spacy.lang.uk.lemmatizer (delayed, conditional, optional)
missing module named pymorphy2 - imported by spacy.lang.ru.lemmatizer (delayed, conditional, optional), spacy.lang.uk.lemmatizer (delayed, conditional, optional)
missing module named 'pythainlp.tokenize' - imported by spacy.lang.th (delayed, optional)
missing module named natto - imported by spacy.lang.ko (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional), spacy.lang.ja (delayed, optional)
missing module named gensim - imported by spacy.lang.el.get_pos_from_wiktionary (delayed)
missing module named 'google.cloud.storage' - imported by cloudpathlib.gs.gsclient (optional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named 'botocore.session' - imported by cloudpathlib.s3.s3client (optional)
missing module named 'botocore.exceptions' - imported by cloudpathlib.s3.s3client (optional), smart_open.s3 (optional)
missing module named 'botocore.config' - imported by cloudpathlib.s3.s3client (optional)
missing module named 'boto3.s3' - imported by cloudpathlib.s3.s3client (optional)
missing module named boto3 - imported by cloudpathlib.s3.s3client (optional), smart_open.s3 (optional), accelerate.commands.config.sagemaker (conditional)
missing module named 'pathlib._local' - imported by cloudpathlib.cloudpath (conditional)
missing module named azure.core.pipeline.transport.AioHttpTransportResponse - imported by azure.core.pipeline.transport (conditional), azure.core.utils._pipeline_transport_rest_shared (conditional)
missing module named cchardet - imported by azure.core.pipeline.transport._aiohttp (delayed, conditional, optional), bs4.dammit (optional)
missing module named 'opentelemetry.trace' - imported by azure.core.tracing.opentelemetry (top-level), azure.core.pipeline.policies._distributed_tracing (conditional)
missing module named 'opentelemetry.context' - imported by azure.core.tracing.opentelemetry (optional)
missing module named 'opentelemetry.propagate' - imported by azure.core.tracing.opentelemetry (top-level)
missing module named opentelemetry - imported by azure.core.tracing.opentelemetry (top-level)
missing module named 'azure.core.tracing.ext.opentelemetry_span' - imported by azure.core.settings (delayed, optional)
missing module named 'azure.core.tracing.ext.opencensus_span' - imported by azure.core.settings (delayed, optional)
missing module named 'azure.storage' - imported by cloudpathlib.azure.azblobclient (optional)
missing module named mypy_boto3_s3 - imported by smart_open.s3 (conditional)
missing module named 'botocore.client' - imported by smart_open.s3 (optional)
missing module named spacy_transformers - imported by spacy.cli.init_config (delayed, optional)
missing module named 'cupy.random' - imported by spacy.util (optional)
missing module named joblibspark - imported by datasets.parallel.parallel (delayed, conditional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'numpy.lib.array_utils' - imported by joblib._memmapping_reducer (delayed, optional), scipy._lib.array_api_compat.common._linalg (conditional)
missing module named multiprocess.set_start_method - imported by multiprocess (top-level), multiprocess.spawn (top-level)
missing module named multiprocess.get_start_method - imported by multiprocess (top-level), multiprocess.spawn (top-level)
missing module named _multiprocess.sem_unlink - imported by _multiprocess (optional), multiprocess.synchronize (optional)
missing module named _multiprocess.SemLock - imported by _multiprocess (optional), multiprocess.synchronize (optional)
missing module named multiprocess.BufferTooShort - imported by multiprocess (top-level), multiprocess.connection (top-level)
missing module named multiprocess.AuthenticationError - imported by multiprocess (top-level), multiprocess.connection (top-level)
missing module named multiprocess.TimeoutError - imported by multiprocess (top-level), multiprocess.pool (top-level)
missing module named multiprocess.get_context - imported by multiprocess (top-level), multiprocess.pool (top-level), multiprocess.managers (top-level), multiprocess.sharedctypes (top-level), datasets.utils.tf_utils (top-level)
excluded module named test - imported by multiprocess.util (delayed)
missing module named multiprocess.Pool - imported by multiprocess (top-level), datasets.arrow_dataset (top-level), datasets.builder (top-level)
missing module named cbor - imported by datasets.packaged_modules.webdataset.webdataset (delayed)
missing module named 'pyspark.sql' - imported by datasets.packaged_modules.spark.spark (conditional)
missing module named pyspark - imported by datasets.arrow_dataset (conditional), datasets.io.spark (top-level), datasets.packaged_modules.spark.spark (delayed, conditional)
missing module named 'jaxlib.xla_client' - imported by datasets.formatting.jax_formatter (delayed)
missing module named jaxlib - imported by datasets.formatting.jax_formatter (conditional)
missing module named 'elasticsearch.helpers' - imported by datasets.search (delayed)
missing module named faiss.swigfaiss_sve - imported by faiss.loader (conditional, optional)
missing module named faiss.swigfaiss_avx512 - imported by faiss.loader (conditional, optional)
missing module named faiss.swigfaiss_avx512_spr - imported by faiss.loader (conditional, optional)
missing module named 'numpy.distutils' - imported by faiss.loader (delayed, conditional)
missing module named elasticsearch - imported by datasets.search (delayed, conditional, optional)
missing module named datasets.Features - imported by datasets (top-level), datasets.io.csv (top-level), datasets.io.abc (top-level), datasets.io.json (top-level), datasets.io.parquet (top-level), datasets.io.text (top-level), datasets.io.generator (top-level), datasets.io.spark (top-level), datasets.io.sql (top-level)
missing module named keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional)
missing module named h5py - imported by thinc.compat (optional), transformers.modeling_tf_utils (top-level), pyqtgraph.metaarray.MetaArray (optional), pyqtgraph.exporters.HDF5Exporter (delayed)
missing module named 'deepspeed.utils' - imported by accelerate.utils.dataclasses (delayed, conditional), transformers.integrations.deepspeed (delayed)
missing module named 'transformer_engine.common' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'intel_transformer_engine.recipe' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'transformer_engine.pytorch' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named intel_transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named oneccl_bindings_for_pytorch - imported by accelerate.state (delayed, conditional)
missing module named smdistributed - imported by accelerate.state (delayed, conditional)
missing module named torch_sdaa - imported by accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional)
missing module named 'torch_xla.runtime' - imported by accelerate.utils.imports (conditional, optional), accelerate.optimizer (conditional), accelerate.state (conditional), transformers.trainer (conditional)
missing module named 'intel_extension_for_pytorch.xpu' - imported by accelerate.utils.memory (delayed, conditional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional), accelerate.data_loader (conditional), accelerate.accelerator (conditional), accelerate.launchers (delayed, conditional), transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named torchdata - imported by accelerate.data_loader (delayed, conditional)
missing module named pretrain_t5 - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_gpt - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_bert - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named 'megatron.training' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.legacy' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.inference' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.core' - imported by accelerate.utils.megatron_lm (conditional)
missing module named lomo_optim - imported by accelerate.optimizer (delayed, conditional), accelerate.accelerator (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named torch.nn.MSELoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named torch.nn.CrossEntropyLoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.modeling_utils (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.kosmos2.modeling_kosmos2 (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.mamba2.modeling_mamba2 (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2_5_vl.modeling_qwen2_5_vl (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.smolvlm.modeling_smolvlm (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named torch.nn.BCEWithLogitsLoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named peft - imported by accelerate.utils.modeling (delayed, conditional), accelerate.utils.fsdp_utils (delayed, conditional), transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named torch.distributed.ReduceOp - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), accelerate.utils.operations (conditional)
missing module named 'torch_xla.amp' - imported by accelerate.utils.modeling (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named habana_frameworks - imported by accelerate.utils.imports (delayed)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), accelerate.utils.environment (delayed, conditional), torch.cuda.memory (delayed, conditional, optional)
missing module named megatron - imported by accelerate.utils.dataclasses (delayed)
missing module named 'transformers.deepspeed' - imported by accelerate.utils.dataclasses (delayed, conditional)
missing module named torchao - imported by accelerate.utils.dataclasses (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.float8' - imported by accelerate.utils.ao (delayed, conditional), accelerate.accelerator (delayed, conditional)
missing module named 'deepspeed.checkpoint' - imported by accelerate.accelerator (delayed, conditional)
missing module named msamp - imported by accelerate.accelerator (delayed, conditional)
missing module named swanlab - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed)
missing module named 'dvclive.plots' - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional)
missing module named dvclive - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed)
missing module named clearml - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional)
missing module named mlflow - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed)
missing module named aim - imported by accelerate.tracking (delayed)
missing module named comet_ml - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named wandb - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named deepspeed - imported by accelerate.utils.deepspeed (delayed), accelerate.state (delayed, conditional), accelerate.utils.operations (delayed, conditional), accelerate.utils.other (delayed, conditional), accelerate.accelerator (delayed), transformers.integrations.deepspeed (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.modeling_utils (conditional)
missing module named 'deepspeed.ops' - imported by accelerate.utils.deepspeed (delayed, conditional)
missing module named 'bitsandbytes.optim' - imported by accelerate.utils.deepspeed (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'smdistributed.modelparallel' - imported by transformers.trainer_pt_utils (conditional), transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named 'peft.utils' - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named apollo_torch - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named 'torchdistx.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional)
missing module named apex - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.trainer (conditional)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named einops - imported by transformers.integrations.npu_flash_attention (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), torch._dynamo.decorators (delayed, conditional)
missing module named 'flash_attn.layers' - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.modernbert.modeling_modernbert (conditional)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named 'torchao.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.dtypes' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed)
missing module named 'quark.torch' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_quark (delayed)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named 'flute.utils' - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'torchao.quantization' - imported by transformers.modeling_utils (conditional), transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named torch.nn.Identity - imported by torch.nn (top-level), transformers.modeling_utils (top-level)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named 'torchvision.transforms' - imported by transformers.image_utils (conditional), transformers.image_processing_utils_fast (conditional), transformers.models.convnext.image_processing_convnext_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.depth_pro.image_processing_depth_pro_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.gemma3.image_processing_gemma3_fast (conditional), transformers.models.got_ocr2.image_processing_got_ocr2_fast (conditional), transformers.models.llama4.image_processing_llama4_fast (conditional), transformers.models.llava.image_processing_llava_fast (conditional), transformers.models.llava_next.image_processing_llava_next_fast (conditional), transformers.models.llava_onevision.image_processing_llava_onevision_fast (conditional), transformers.models.phi4_multimodal.image_processing_phi4_multimodal_fast (top-level), transformers.models.pixtral.image_processing_pixtral_fast (conditional), transformers.models.qwen2_vl.image_processing_qwen2_vl_fast (conditional), transformers.models.rt_detr.image_processing_rt_detr_fast (conditional), transformers.models.siglip2.image_processing_siglip2_fast (conditional)
missing module named transformers.models.llava.LlavaImageProcessor - imported by transformers.models.llava (conditional, optional), transformers (conditional, optional)
missing module named yt_dlp - imported by transformers.image_utils (delayed, conditional)
missing module named decord - imported by transformers.image_utils (delayed)
missing module named blobfile - imported by tiktoken.load (delayed, conditional, optional)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named _sentencepiece - imported by sentencepiece (conditional)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named transformers.models.mt5.MT5TokenizerFast - imported by transformers.models.mt5 (conditional, optional), transformers (conditional, optional)
missing module named quark - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named 'compressed_tensors.config' - imported by transformers.utils.quantization_config (delayed)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named mlx - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named pycocotools - imported by transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named py3nvml - imported by onnxruntime.transformers.machine_info (top-level)
missing module named cpuinfo - imported by onnxruntime.transformers.machine_info (top-level)
missing module named 'onnxruntime.training' - imported by onnxruntime.capi.onnxruntime_validation (delayed, optional)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.generation.flax_utils (top-level)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional)
missing module named 'jax.experimental' - imported by transformers.generation.flax_logits_process (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'peft.tuners' - imported by transformers.models.data2vec.modeling_data2vec_audio (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named torch.nn.LogSoftmax - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named torch.nn.KLDivLoss - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named timm - imported by transformers.models.timm_backbone.modeling_timm_backbone (conditional), transformers.models.conditional_detr.modeling_conditional_detr (conditional), transformers.models.deformable_detr.modeling_deformable_detr (conditional), transformers.models.detr.modeling_detr (conditional), transformers.models.grounding_dino.modeling_grounding_dino (conditional), transformers.models.table_transformer.modeling_table_transformer (conditional), transformers.models.timm_wrapper.modeling_timm_wrapper (conditional)
missing module named 'timm.data' - imported by transformers.models.timm_wrapper.configuration_timm_wrapper (conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named 'tensorflow.compiler' - imported by transformers.models.t5.modeling_tf_t5 (top-level), transformers.generation.tf_utils (top-level)
missing module named 'apex.normalization' - imported by transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named torch.nn.L1Loss - imported by torch.nn (top-level), transformers.models.speecht5.modeling_speecht5 (top-level)
missing module named 'torchaudio.compliance' - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named num2words - imported by transformers.models.smolvlm.processing_smolvlm (conditional)
missing module named torch.nn.LayerNorm - imported by torch.nn (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level)
missing module named 'torchvision.ops' - imported by transformers.models.omdet_turbo.processing_omdet_turbo (conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.deprecated.deta.image_processing_deta (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), torch._inductor.utils (delayed, optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named 'rapidfuzz.distance._initialize_cpp_sse2' - imported by rapidfuzz.distance._initialize (conditional)
missing module named 'rapidfuzz.distance._initialize_cpp_avx2' - imported by rapidfuzz.distance._initialize (conditional)
missing module named 'rapidfuzz.distance.metrics_cpp_sse2' - imported by rapidfuzz.distance.OSA (conditional), rapidfuzz.distance.DamerauLevenshtein (conditional), rapidfuzz.distance.Hamming (conditional), rapidfuzz.distance.Indel (conditional), rapidfuzz.distance.Jaro (conditional), rapidfuzz.distance.JaroWinkler (conditional), rapidfuzz.distance.LCSseq (conditional), rapidfuzz.distance.Levenshtein (conditional), rapidfuzz.distance.Postfix (conditional), rapidfuzz.distance.Prefix (conditional)
missing module named 'rapidfuzz.utils_cpp_sse2' - imported by rapidfuzz.utils (conditional)
missing module named 'rapidfuzz.utils_cpp_avx2' - imported by rapidfuzz.utils (conditional)
missing module named 'rapidfuzz.process_cpp_sse2' - imported by rapidfuzz.process (conditional)
missing module named 'rapidfuzz.process_cpp_avx2' - imported by rapidfuzz.process (conditional)
missing module named 'rapidfuzz.fuzz_cpp_sse2' - imported by rapidfuzz.fuzz (conditional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named transformers.models.mistral3.processing_mistral3 - imported by transformers.models.mistral3 (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named torch.nn.SmoothL1Loss - imported by torch.nn (top-level), transformers.models.lxmert.modeling_lxmert (top-level)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named natten - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'tensorflow.compat' - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named xformers - imported by transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named 'natten.functional' - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named spqr_quant - imported by transformers.integrations.spqr (delayed, conditional)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.env' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named kernels - imported by transformers.integrations.hub_kernels (optional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.tune' - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named torch.nn.BCELoss - imported by torch.nn (top-level), transformers.generation.watermarking (top-level)
missing module named array_api_compat - imported by sklearn.utils._array_api (delayed, conditional, optional), sklearn.utils._testing (delayed, optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named markdownify - imported by transformers.agents.search (delayed, optional)
missing module named duckduckgo_search - imported by transformers.agents.search (delayed, optional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.distributed._symmetric_memory (top-level), torch.autograd (top-level), torch.testing._internal.common_distributed (top-level)
missing module named 'numba.cuda' - imported by torch.testing._internal.common_cuda (conditional, optional)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch.fx.passes.utils.common (top-level), torch.distributed.nn.api.remote_module (top-level), torch._dynamo.mutation_guard (top-level), torch.fx.experimental.proxy_tensor (top-level)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx.utils (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnx.defs.OpSchema' - imported by torch.onnx._internal.fx.type_utils (conditional)
missing module named onnxscript - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib._tensor_typing (top-level), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._verification (conditional), torch.onnx._internal.exporter._reporting (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter._ir_passes (delayed, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (delayed), torch.onnx._internal.exporter._building (top-level)
missing module named 'onnxscript.onnx_opset' - imported by torch.onnx._internal.exporter._torchlib.ops.core (top-level)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._lazy_import (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named astunparse - imported by torch.jit.frontend (optional)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.distributed.Work - imported by torch.distributed (conditional), torch.distributed.pipelining.schedules (conditional)
missing module named torch.distributed.group - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), torch.distributed.algorithms.model_averaging.utils (top-level)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (optional), torch.distributed.elastic.rendezvous.etcd_store (optional), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (optional), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pulp - imported by torch.distributed._tools.sac_ilp (optional)
missing module named pwlf - imported by torch.distributed._tools.sac_estimator (delayed, optional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._higher_order_ops.cond (top-level), torch._subclasses.fake_tensor (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'torch._inductor.fb' - imported by torch._inductor.remote_cache (delayed, conditional, optional), torch._dynamo.pgo (delayed, optional), torch._inductor.cpp_builder (conditional), torch._inductor.compile_fx (conditional), torch._inductor.graph (conditional), torch._functorch._aot_autograd.autograd_cache (delayed, optional), torch._inductor.runtime.autotune_cache (delayed, optional), torch._inductor.codecache (conditional), torch._inductor.utils (delayed, optional), torch._dynamo.utils (delayed, conditional, optional)
missing module named 'triton.testing' - imported by torch._inductor.runtime.benchmarking (delayed, optional), torch._inductor.utils (delayed), torch._utils_internal (delayed, conditional)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional), torch._inductor.codegen.rocm.ck_conv_template (optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named redis - imported by torch._inductor.remote_cache (optional), pip._vendor.cachecontrol.caches.redis_cache (conditional), apscheduler.jobstores.redis (optional)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.types (top-level), torch._dynamo.convert_frame (top-level), torch._functorch._aot_autograd.input_output_analysis (top-level), torch._inductor.fx_passes.reinplace (top-level), torch._dynamo.decorators (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._inductor.compile_worker.subproc_pool (delayed, conditional), torch._inductor.codegen.rocm.compile_command (delayed, conditional), torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'ck4inductor.grouped_conv_fwd' - imported by torch._inductor.codegen.rocm.ck_conv_template (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named cutlass_generator - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named cutlass - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named 'triton._utils' - imported by torch._higher_order_ops.triton_kernel_wrap (delayed, conditional)
missing module named 'triton._C' - imported by torch._higher_order_ops.triton_kernel_wrap (conditional)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._inductor.config (conditional), torch._functorch.config (conditional), torch._dynamo.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named torch._dynamo.variables.symbolic_convert - imported by torch._dynamo.variables.base (conditional)
missing module named 'optree._C' - imported by torch._dynamo.polyfills.pytree (conditional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, conditional, optional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional), seaborn.external.appdirs (delayed, conditional, optional)
missing module named com - imported by torch._appdirs (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch_xla.utils' - imported by torch._tensor (delayed, conditional)
missing module named torch.tensor - imported by torch (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch._softmax_backward_data - imported by torch (delayed), transformers.pytorch_utils (delayed)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), transformers.models.nemotron.modeling_nemotron (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch._library.infer_schema (top-level), torch.cuda (top-level), torch._inductor.graph (top-level), torch.distributed.nn.api.remote_module (top-level), transformers.models.blip.modeling_blip_text (top-level), torch.xpu (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named numpy.complex128 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'numpy.linalg._linalg' - imported by scipy._lib.array_api_compat.numpy.linalg (delayed, optional)
missing module named scipy.integrate.trapz - imported by scipy.integrate (delayed, optional), statsmodels.genmod.generalized_estimating_equations (delayed, optional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level), seaborn.external.kde (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named svgling - imported by nltk.tree.tree (delayed)
missing module named norm - imported by nltk.translate.gale_church (optional)
missing module named nltk.corpus.WordNetCorpusReader - imported by nltk.corpus (top-level), nltk.translate.meteor_score (top-level)
missing module named pycrfsuite - imported by nltk.tag.crf (optional)
missing module named 'bllipparser.RerankingParser' - imported by nltk.parse.bllip (optional)
missing module named bllipparser - imported by nltk.parse.bllip (optional)
missing module named nltk.induce_pcfg - imported by nltk (delayed), nltk.grammar (delayed)
missing module named nltk.Prover9 - imported by nltk (delayed), nltk.sem.glue (delayed)
missing module named nltk.word_tokenize - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.FreqDist - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.nonterminals - imported by nltk (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Tree - imported by nltk (delayed), nltk.tree.tree (delayed), nltk.chunk.regexp (delayed)
missing module named nltk.ProbabilisticTree - imported by nltk (delayed), nltk.tree.tree (delayed)
missing module named nltk.Production - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Nonterminal - imported by nltk (delayed), nltk.draw.cfg (delayed)
missing module named nltk.CFG - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.parse.recursivedescent (delayed), nltk.parse.shiftreduce (delayed), nltk.grammar (delayed)
missing module named numpypy - imported by nltk (optional)
missing module named pyimod02_importers - imported by C:\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named pip._vendor.msgpack._cmsgpack - imported by pip._vendor.msgpack (conditional, optional)
excluded module named __main__ - imported by pip._vendor.pkg_resources (delayed, optional)
missing module named urllib3_secure_extra - imported by pip._vendor.urllib3 (optional)
missing module named 'pip._vendor.urllib3.packages.six.moves' - imported by pip._vendor.urllib3.exceptions (top-level), pip._vendor.urllib3.connection (top-level), pip._vendor.urllib3.util.response (top-level), pip._vendor.urllib3.connectionpool (top-level), pip._vendor.urllib3.request (top-level), pip._vendor.urllib3.util.queue (top-level), pip._vendor.urllib3.poolmanager (top-level)
missing module named Queue - imported by pip._vendor.urllib3.util.queue (conditional), pip._vendor.distlib.compat (conditional)
missing module named 'OpenSSL.crypto' - imported by pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named 'pip._vendor.rich.markdown' - imported by pip._vendor.rich.__main__ (top-level)
missing module named keyring - imported by pip._internal.network.auth (delayed)
missing module named _abcoll - imported by patsy.compat_ordereddict (optional), pip._vendor.distlib.compat (optional)
missing module named HTMLParser - imported by pip._vendor.distlib.compat (conditional)
missing module named xmlrpclib - imported by pip._vendor.distlib.compat (conditional)
missing module named httplib - imported by pip._vendor.distlib.compat (conditional)
missing module named ConfigParser - imported by pip._vendor.distlib.compat (conditional)
missing module named java - imported by platform (delayed), pip._vendor.distlib.scripts (delayed, conditional)
missing module named pip.__file__ - imported by pip (top-level), pip._internal.build_env (top-level)
missing module named crawler - imported by auto_update (top-level)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named enhanced_network_manager - imported by dialogs.monitor_window (delayed, conditional, optional), intraday_data_fetcher (delayed, optional)
missing module named enhanced_monthly_revenue_downloader - imported by unified_monthly_revenue_gui (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named enhanced_dividend_crawler - imported by D:\Finlab\backup\O3mh_strategy2AA\O3mh_gui_v21_optimized_fixed.py (delayed, optional)
missing module named integrated_strategy_help_dialog - imported by D:\Finlab\backup\O3mh_strategy2AA\O3mh_gui_v21_optimized_fixed.py (delayed, optional)
missing module named trading_mantra_generator - imported by auto_rule_discovery_gui (top-level)
missing module named trading_rule_miner - imported by auto_rule_discovery_gui (top-level)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named pandas._testing.makeDataFrame - imported by pandas._testing (optional), statsmodels.compat.pandas (optional)
missing module named 'pandas.util.testing' - imported by statsmodels.compat.pandas (optional)
missing module named patsy.DesignMatrix - imported by patsy (delayed), statsmodels.tools.data (delayed)
missing module named patsy.DesignInfo - imported by patsy (delayed), statsmodels.base.model (delayed), statsmodels.genmod.generalized_linear_model (delayed), statsmodels.base._constraints (delayed)
missing module named patsy.EvalEnvironment - imported by patsy (delayed, conditional), statsmodels.base.model (delayed, conditional)
missing module named patsy.dmatrix - imported by patsy (delayed, conditional), statsmodels.regression._prediction (delayed, conditional), statsmodels.graphics.regressionplots (top-level), statsmodels.base.model (delayed, conditional), statsmodels.base._prediction_inference (delayed, conditional)
missing module named patsy.NAAction - imported by patsy (top-level), statsmodels.formula.formulatools (top-level)
missing module named patsy.dmatrices - imported by patsy (delayed, conditional), statsmodels.base.data (delayed, conditional), statsmodels.formula.formulatools (top-level)
missing module named 'numpy.testing.utils' - imported by patsy.constraint (delayed, optional)
missing module named cvxopt - imported by statsmodels.stats._knockoff (delayed, optional), statsmodels.regression.linear_model (delayed, optional)
missing module named statsmodels.sandbox.stats.ex_multicomp - imported by statsmodels.sandbox.stats.multicomp (conditional)
missing module named twse_crawler - imported by real_data_sources (optional)
excluded module named PyQt5.QtGui - imported by charts.candlestick (optional), dialogs.strategy_info_dialog (top-level), dialogs.monitor_window (top-level), dialogs.stock_manager_dialog (top-level)
excluded module named PyQt5.QtCore - imported by charts.candlestick (optional), config.strategy_config (top-level), dialogs.strategy_info_dialog (top-level), dialogs.monitor_window (top-level), dialogs.stock_manager_dialog (top-level)
excluded module named PyQt5.QtWidgets - imported by config.strategy_config (top-level), D:\Finlab\backup\O3mh_strategy2AA\O3mh_gui_v21_optimized_fixed.py (optional), dialogs.strategy_info_dialog (top-level), dialogs.monitor_window (top-level), dialogs.stock_manager_dialog (top-level)
missing module named codes - imported by twstock.stock (optional)
missing module named analytics - imported by twstock.stock (optional)
missing module named twisted - imported by apscheduler.schedulers.twisted (optional)
missing module named 'gevent.lock' - imported by apscheduler.schedulers.gevent (optional)
missing module named 'kazoo.client' - imported by apscheduler.jobstores.zookeeper (optional)
missing module named kazoo - imported by apscheduler.jobstores.zookeeper (top-level)
missing module named rethinkdb - imported by apscheduler.jobstores.rethinkdb (optional)
missing module named 'pymongo.errors' - imported by apscheduler.jobstores.mongodb (optional)
missing module named pymongo - imported by apscheduler.jobstores.mongodb (optional)
missing module named bson - imported by apscheduler.jobstores.mongodb (optional)
missing module named 'backports.zoneinfo' - imported by apscheduler.util (conditional)
missing module named requests_cache - imported by yfinance.data (delayed, optional)
missing module named frozendict._frozendict - imported by frozendict (optional)
missing module named pymysql - imported by peewee (optional)
missing module named 'psycopg2.extras' - imported by peewee (optional)
missing module named psycopg2cffi - imported by peewee (optional)
missing module named pysqlite3 - imported by peewee (optional)
missing module named 'numpy._core.numeric' - imported by D:\Finlab\backup\O3mh_strategy2AA\O3mh_gui_v21_optimized_fixed.py (delayed, conditional, optional)
missing module named metaarray - imported by pyqtgraph.widgets.DataTreeWidget (optional), pyqtgraph.flowchart.library.common (optional)
missing module named 'OpenGL.GL' - imported by pyqtgraph.graphicsItems.PlotCurveItem (delayed), pyqtgraph.widgets.RawImageWidget (optional)
missing module named colorcet - imported by pyqtgraph.colormap (delayed, conditional, optional), pyqtgraph.widgets.ColorMapMenu (delayed)
missing module named 'h5py.highlevel' - imported by pyqtgraph.metaarray.MetaArray (conditional, optional)
missing module named bottleneck - imported by pyqtgraph.imageview.ImageView (optional)
missing module named pyqtgraph.PlotItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level), pyqtgraph.exporters.HDF5Exporter (top-level), pyqtgraph.exporters.Matplotlib (top-level)
missing module named pyqtgraph.ErrorBarItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level)
missing module named pyside2uic - imported by pyqtgraph.Qt (delayed, conditional, optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
