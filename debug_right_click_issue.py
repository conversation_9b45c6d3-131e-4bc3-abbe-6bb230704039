#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試右鍵選單問題
"""

import sys
import os
import logging
from datetime import datetime, date

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_right_click_issue():
    """調試右鍵選單問題"""
    print("=" * 60)
    print("🐛 調試右鍵選單問題")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QTableWidgetItem
        from PyQt6.QtCore import Qt, QPoint
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 模擬執行月營收排行榜查詢的完整過程
        print("\n📊 模擬月營收排行榜查詢過程...")
        
        # 設置查詢日期
        selected_date = date(2025, 7, 29)
        
        # 模擬獲取月營收排行榜資料
        mock_ranking_data = [
            {
                '排名': 1,
                '股票代碼': '2330',
                '股票名稱': '台積電',
                '西元年月': '202507',
                '當月營收': '120,000,000',
                '上個月營收': '115,000,000',
                '去年同月營收': '100,000,000',
                'YoY%': '+20.00%',
                'MoM%': '*****%',
                '殖利率': '3.25',
                '本益比': '15.80',
                '股價淨值比': '2.45',
                'EPS': '36.71',
                '綜合評分': '85.5'
            },
            {
                '排名': 2,
                '股票代碼': '2317',
                '股票名稱': '鴻海',
                '西元年月': '202507',
                '當月營收': '95,000,000',
                '上個月營收': '92,000,000',
                '去年同月營收': '88,000,000',
                'YoY%': '*****%',
                'MoM%': '*****%',
                '殖利率': '4.50',
                '本益比': '12.50',
                '股價淨值比': '1.85',
                'EPS': '9.60',
                '綜合評分': '78.2'
            }
        ]
        
        # 模擬 display_ranking_results 函數的行為
        ranking_type = "月營收排行榜 (YoY排序) + 綜合評分"
        
        print(f"📋 設置表格結構 - 排行榜類型: {ranking_type}")
        
        # 設置表格結構（模擬 update_right_side_table 函數）
        gui.result_table.setSortingEnabled(False)
        gui.result_table.setRowCount(0)
        
        if "月營收排行榜" in ranking_type:
            if "綜合評分" in ranking_type:
                gui.result_table.setColumnCount(14)
                gui.result_table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                    "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                    "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
                ])
                print("✅ 設置14欄表格（含綜合評分）")
            else:
                gui.result_table.setColumnCount(13)
                gui.result_table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                    "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                    "殖利率(%)", "本益比", "股價淨值比", "EPS"
                ])
                print("✅ 設置13欄表格")
        
        # 填入測試資料
        gui.result_table.setRowCount(len(mock_ranking_data))
        for row, data in enumerate(mock_ranking_data):
            gui.result_table.setItem(row, 0, QTableWidgetItem(str(data['排名'])))
            gui.result_table.setItem(row, 1, QTableWidgetItem(data['股票代碼']))
            gui.result_table.setItem(row, 2, QTableWidgetItem(data['股票名稱']))
            gui.result_table.setItem(row, 3, QTableWidgetItem(data['西元年月']))
            gui.result_table.setItem(row, 4, QTableWidgetItem(data['當月營收']))
            gui.result_table.setItem(row, 5, QTableWidgetItem(data['上個月營收']))
            gui.result_table.setItem(row, 6, QTableWidgetItem(data['去年同月營收']))
            gui.result_table.setItem(row, 7, QTableWidgetItem(data['YoY%']))
            gui.result_table.setItem(row, 8, QTableWidgetItem(data['MoM%']))
            gui.result_table.setItem(row, 9, QTableWidgetItem(data['殖利率']))
            gui.result_table.setItem(row, 10, QTableWidgetItem(data['本益比']))
            gui.result_table.setItem(row, 11, QTableWidgetItem(data['股價淨值比']))
            gui.result_table.setItem(row, 12, QTableWidgetItem(data['EPS']))
            if gui.result_table.columnCount() > 13:
                gui.result_table.setItem(row, 13, QTableWidgetItem(data['綜合評分']))
        
        print("✅ 測試資料填入完成")
        
        # 檢查表格狀態
        print(f"\n🔍 檢查表格狀態:")
        print(f"  欄位數量: {gui.result_table.columnCount()}")
        print(f"  行數: {gui.result_table.rowCount()}")
        
        # 檢查所有欄位標題
        print(f"  欄位標題:")
        for i in range(gui.result_table.columnCount()):
            header_item = gui.result_table.horizontalHeaderItem(i)
            header_text = header_item.text() if header_item else "None"
            print(f"    第{i}欄: '{header_text}'")
        
        # 測試月營收排行榜檢測
        print(f"\n🔍 測試月營收排行榜檢測:")
        is_monthly = gui.is_monthly_revenue_ranking()
        print(f"  檢測結果: {'✅ 是月營收排行榜' if is_monthly else '❌ 不是月營收排行榜'}")
        
        # 詳細檢查檢測邏輯
        column_count = gui.result_table.columnCount()
        print(f"  欄位數量檢查: {column_count} >= 13 = {column_count >= 13}")
        
        header_item = gui.result_table.horizontalHeaderItem(0)
        if header_item:
            header_text = header_item.text()
            print(f"  第一欄標題檢查: '{header_text}' == '排名' = {header_text == '排名'}")
        else:
            print(f"  第一欄標題檢查: None")
        
        # 測試股票資料提取
        print(f"\n📊 測試股票資料提取:")
        if gui.result_table.rowCount() > 0:
            stock_data = gui.get_monthly_revenue_stock_data(0)
            if stock_data:
                print(f"  ✅ 成功提取第一行資料:")
                print(f"    股票: {stock_data['股票代碼']} {stock_data['股票名稱']}")
                print(f"    排名: {stock_data['排名']}")
                print(f"    YoY%: {stock_data['YoY%']}")
                print(f"    EPS: {stock_data['EPS']}")
            else:
                print(f"  ❌ 無法提取股票資料")
        
        # 手動測試右鍵選單
        print(f"\n🖱️ 手動測試右鍵選單:")
        try:
            # 模擬右鍵點擊第一行
            position = QPoint(100, 50)  # 表格內的位置
            
            print("  正在調用 show_stock_context_menu...")
            gui.show_stock_context_menu(position)
            print("  ✅ 右鍵選單函數調用完成")
            
        except Exception as e:
            print(f"  ❌ 右鍵選單函數調用失敗: {e}")
            import traceback
            traceback.print_exc()
        
        # 顯示GUI進行實際測試
        print(f"\n🖥️ 顯示GUI進行實際測試...")
        print("  請在表格中右鍵點擊股票，檢查選單內容")
        
        gui.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test():
    """創建簡單的右鍵選單測試"""
    print("\n" + "=" * 60)
    print("🧪 創建簡單的右鍵選單測試")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QMenu
        from PyQt6.QtCore import Qt, QPoint
        
        class SimpleTestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("右鍵選單測試")
                self.setGeometry(100, 100, 800, 400)
                
                # 創建中央widget
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                
                # 創建表格
                self.table = QTableWidget()
                self.table.setColumnCount(14)
                self.table.setRowCount(2)
                
                # 設置標題
                self.table.setHorizontalHeaderLabels([
                    "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
                    "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
                    "殖利率(%)", "本益比", "股價淨值比", "EPS", "綜合評分"
                ])
                
                # 填入測試資料
                test_data = [
                    ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
                     "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71", "85.5"],
                    ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
                     "*****%", "*****%", "4.50", "12.50", "1.85", "9.60", "78.2"]
                ]
                
                for row, row_data in enumerate(test_data):
                    for col, cell_data in enumerate(row_data):
                        item = QTableWidgetItem(str(cell_data))
                        self.table.setItem(row, col, item)
                
                # 設置右鍵選單
                self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.table.customContextMenuRequested.connect(self.show_context_menu)
                
                layout.addWidget(self.table)
                
                print("✅ 簡單測試視窗創建完成")
            
            def show_context_menu(self, position):
                """顯示右鍵選單"""
                print(f"🖱️ 右鍵點擊位置: {position}")
                
                # 獲取點擊的項目
                item = self.table.itemAt(position)
                if not item:
                    print("❌ 沒有點擊到有效項目")
                    return
                
                row = item.row()
                stock_code = self.table.item(row, 1).text()
                stock_name = self.table.item(row, 2).text()
                
                print(f"📈 股票: {stock_code} {stock_name}")
                
                # 創建右鍵選單
                context_menu = QMenu(self)
                
                # 添加月營收綜合評估選項
                assessment_action = context_menu.addAction(f"📊 {stock_code} {stock_name} 月營收綜合評估")
                assessment_action.triggered.connect(lambda: self.show_assessment(stock_code, stock_name))
                
                # 添加其他選項
                context_menu.addSeparator()
                news_action = context_menu.addAction(f"📰 爬取 {stock_code} {stock_name} 新聞")
                chart_action = context_menu.addAction(f"📈 查看 {stock_code} K線圖")
                
                # 顯示選單
                context_menu.exec(self.table.mapToGlobal(position))
                print("✅ 右鍵選單顯示完成")
            
            def show_assessment(self, stock_code, stock_name):
                """顯示評估"""
                print(f"🎯 顯示 {stock_code} {stock_name} 的月營收綜合評估")
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建測試視窗
        test_window = SimpleTestWindow()
        test_window.show()
        
        print("🖥️ 簡單測試視窗已顯示，請測試右鍵選單功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 簡單測試創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始調試右鍵選單問題...")
    
    # 主要調試
    success1 = debug_right_click_issue()
    
    # 簡單測試
    success2 = create_simple_test()
    
    if success1 and success2:
        print("\n🎉 調試完成！")
        print("\n📋 檢查要點:")
        print("  1. 表格是否正確設置為月營收排行榜格式")
        print("  2. is_monthly_revenue_ranking() 函數是否返回 True")
        print("  3. 右鍵選單是否包含月營收綜合評估選項")
        print("  4. 簡單測試視窗的右鍵選單是否正常工作")
    else:
        print("\n❌ 調試過程中發現問題，請檢查錯誤訊息。")
