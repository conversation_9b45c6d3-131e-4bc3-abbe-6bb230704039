#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 price.db 中的資料，驗證興櫃股票是否已被移除
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime

def check_price_db():
    """檢查 price.db 中的資料"""
    print("=" * 80)
    print("🔍 檢查 price.db 中的資料")
    print("=" * 80)
    
    # 資料庫路徑
    price_db_path = 'D:/Finlab/history/tables/price.db'
    newprice_db_path = 'D:/Finlab/history/tables/newprice.db'
    
    # 檢查檔案是否存在
    print("📂 檢查資料庫檔案:")
    print(f"   price.db: {'✅ 存在' if os.path.exists(price_db_path) else '❌ 不存在'}")
    print(f"   newprice.db: {'✅ 存在' if os.path.exists(newprice_db_path) else '❌ 不存在'}")
    
    # 選擇要檢查的資料庫
    target_db = None
    if os.path.exists(price_db_path):
        target_db = price_db_path
        db_name = "price.db"
    elif os.path.exists(newprice_db_path):
        target_db = newprice_db_path
        db_name = "newprice.db"
    else:
        print("❌ 找不到任何價格資料庫")
        return
    
    print(f"\n🎯 檢查資料庫: {db_name}")
    print()
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(target_db)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 資料庫表格: {[table[0] for table in tables]}")
        
        # 檢查主要表格
        table_name = 'stock_daily_data' if 'stock_daily_data' in [t[0] for t in tables] else 'price'
        
        # 檢查表格結構
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        print(f"\n📋 表格 '{table_name}' 欄位結構:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查資料總數
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 總資料筆數: {total_count:,}")
        
        # 檢查股票數量
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name}")
        unique_stocks = cursor.fetchone()[0]
        print(f"📊 不重複股票數: {unique_stocks:,}")
        
        # 檢查日期範圍
        cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table_name}")
        date_range = cursor.fetchone()
        print(f"📊 日期範圍: {date_range[0]} ~ {date_range[1]}")
        
        # 檢查是否有 listing_status 欄位
        column_names = [col[1] for col in columns]
        if 'listing_status' in column_names:
            print(f"\n📈 市場別分布:")
            cursor.execute(f"""
                SELECT listing_status, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
                FROM {table_name} 
                GROUP BY listing_status 
                ORDER BY stock_count DESC
            """)
            market_stats = cursor.fetchall()
            
            for status, stock_count, record_count in market_stats:
                status_name = status if status else 'ETF/未分類'
                print(f"   {status_name}: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
            
            # 檢查是否有興櫃股票
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE listing_status = '興櫃'")
            rotc_count = cursor.fetchone()[0]
            
            if rotc_count == 0:
                print(f"\n✅ 確認：資料庫中沒有興櫃股票")
            else:
                print(f"\n⚠️ 警告：資料庫中仍有 {rotc_count:,} 筆興櫃股票記錄")
                
                # 顯示興櫃股票範例
                cursor.execute(f"""
                    SELECT DISTINCT stock_id, stock_name 
                    FROM {table_name} 
                    WHERE listing_status = '興櫃' 
                    LIMIT 10
                """)
                rotc_examples = cursor.fetchall()
                print(f"   興櫃股票範例:")
                for stock_id, stock_name in rotc_examples:
                    print(f"     {stock_id} {stock_name}")
        else:
            print(f"\n⚠️ 資料庫中沒有 listing_status 欄位，無法檢查市場別")
        
        # 檢查特定股票類型
        print(f"\n🔍 檢查特定股票類型:")
        
        # ETF (00開頭)
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE stock_id LIKE '00%'")
        etf_count = cursor.fetchone()[0]
        print(f"   ETF (00開頭): {etf_count:,} 檔")
        
        # 4位數股票
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE LENGTH(stock_id) = 4 AND stock_id NOT LIKE '00%'")
        four_digit_count = cursor.fetchone()[0]
        print(f"   4位數股票: {four_digit_count:,} 檔")
        
        # 權證 (7開頭)
        cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name} WHERE stock_id LIKE '7%'")
        warrant_count = cursor.fetchone()[0]
        print(f"   權證 (7開頭): {warrant_count:,} 檔")
        
        if warrant_count > 0:
            print(f"   ⚠️ 警告：資料庫中仍有權證資料")
        else:
            print(f"   ✅ 確認：資料庫中沒有權證資料")
        
        # 顯示最新資料範例
        print(f"\n📋 最新資料範例 (前10筆):")
        if 'listing_status' in column_names:
            cursor.execute(f"""
                SELECT stock_id, stock_name, listing_status, date, Close 
                FROM {table_name} 
                ORDER BY date DESC, stock_id 
                LIMIT 10
            """)
        else:
            cursor.execute(f"""
                SELECT stock_id, date, Close 
                FROM {table_name} 
                ORDER BY date DESC, stock_id 
                LIMIT 10
            """)
        
        latest_data = cursor.fetchall()
        for row in latest_data:
            print(f"   {row}")
        
        conn.close()
        
        print(f"\n✅ 資料庫檢查完成")
        
    except Exception as e:
        print(f"❌ 檢查資料庫時發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def rename_newprice_to_price():
    """將 newprice.db 重新命名為 price.db"""
    print("=" * 80)
    print("🔄 重新命名 newprice.db 為 price.db")
    print("=" * 80)
    
    newprice_db_path = 'D:/Finlab/history/tables/newprice.db'
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    try:
        if os.path.exists(newprice_db_path):
            if os.path.exists(price_db_path):
                print(f"⚠️ price.db 已存在，將備份為 price_backup.db")
                backup_path = 'D:/Finlab/history/tables/price_backup.db'
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                os.rename(price_db_path, backup_path)
                print(f"✅ 已備份原 price.db 為 price_backup.db")
            
            os.rename(newprice_db_path, price_db_path)
            print(f"✅ 已將 newprice.db 重新命名為 price.db")
            
            # 檢查檔案大小
            file_size = os.path.getsize(price_db_path) / (1024 * 1024)  # MB
            print(f"📊 price.db 檔案大小: {file_size:.2f} MB")
            
        else:
            print(f"❌ newprice.db 不存在，無法重新命名")
            
    except Exception as e:
        print(f"❌ 重新命名失敗: {e}")

if __name__ == "__main__":
    # 檢查資料庫
    check_price_db()
    
    print("\n" + "=" * 80)
    
    # 詢問是否要重新命名
    newprice_db_path = 'D:/Finlab/history/tables/newprice.db'
    if os.path.exists(newprice_db_path):
        print("🔍 發現 newprice.db 檔案")
        response = input("是否要將 newprice.db 重新命名為 price.db? (y/n): ")
        if response.lower() == 'y':
            rename_newprice_to_price()
            print("\n重新命名完成後的檢查:")
            check_price_db()
