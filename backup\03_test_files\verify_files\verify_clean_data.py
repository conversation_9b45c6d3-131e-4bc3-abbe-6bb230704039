#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證清理後的數據
"""

import pandas as pd
import os

def verify_clean_data():
    """驗證清理後的數據"""
    
    print("✅ 驗證清理後的月營收數據")
    print("=" * 50)
    
    excel_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.xlsx"
    
    if os.path.exists(excel_file):
        try:
            # 讀取主要數據
            df = pd.read_excel(excel_file, sheet_name='月營收資料')
            
            print(f"📊 Excel檔案檢查:")
            print(f"  檔案: {excel_file}")
            print(f"  記錄數: {len(df):,}")
            
            # 市場分布
            market_dist = dict(df['市場別'].value_counts())
            print(f"  市場分布: {market_dist}")
            
            # 檢查是否只有上市上櫃
            markets = set(df['市場別'])
            only_listed = markets <= {'上市', '上櫃'}
            print(f"  只包含上市上櫃: {'✅' if only_listed else '❌'}")
            
            if not only_listed:
                other_markets = markets - {'上市', '上櫃'}
                print(f"  其他市場: {other_markets}")
            
            # 顯示前10筆資料
            print(f"\n🔍 前10筆資料:")
            for i, row in df.head(10).iterrows():
                print(f"  {row['股票代號']} {row['股票名稱']} ({row['市場別']}): {row['營收(千元)']}")
            
            # 讀取統計資料
            try:
                stats_df = pd.read_excel(excel_file, sheet_name='統計資料')
                print(f"\n📈 統計資料:")
                for i, row in stats_df.iterrows():
                    print(f"  {row['市場別']}: {row['公司數量']} 家，總營收 {row['總營收(千元)']}")
            except:
                print("  統計資料表讀取失敗")
            
            # 檢查營收數據合理性
            print(f"\n💰 營收數據檢查:")
            print(f"  最高營收: {df['營收(千元)'].str.replace(',', '').astype(float).max():,.0f} 千元")
            print(f"  最低營收: {df['營收(千元)'].str.replace(',', '').astype(float).min():,.0f} 千元")
            print(f"  平均營收: {df['營收(千元)'].str.replace(',', '').astype(float).mean():,.0f} 千元")
            
        except Exception as e:
            print(f"❌ 檔案讀取失敗: {e}")
    else:
        print(f"❌ 檔案不存在: {excel_file}")
    
    # 檢查CSV檔案
    csv_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.csv"
    if os.path.exists(csv_file):
        try:
            csv_df = pd.read_csv(csv_file)
            print(f"\n📄 CSV檔案檢查:")
            print(f"  記錄數: {len(csv_df):,}")
            print(f"  市場分布: {dict(csv_df['市場別'].value_counts())}")
            
            # 檢查Excel和CSV是否一致
            excel_count = len(df) if 'df' in locals() else 0
            csv_count = len(csv_df)
            print(f"  與Excel一致: {'✅' if excel_count == csv_count else '❌'}")
            
        except Exception as e:
            print(f"❌ CSV檔案讀取失敗: {e}")
    else:
        print(f"❌ CSV檔案不存在: {csv_file}")

if __name__ == "__main__":
    verify_clean_data()
