# 🐱 膽小貓策略 - 完整說明文檔

## 📊 策略概述

**膽小貓策略**是一個專注於**低股價、低波動、營收成長**的穩健投資法。策略體質跟貓咪一樣，體型小、敏感、膽小，喜歡安全的窩，不愛在外頭亂跑。擁有不錯的年化報酬/MDD，就像乖乖的貓貓。

## 🎯 策略核心特色

### 歷史驗證表現
- **回測期間**：2008年回測至今幾乎年年獲利！
- **風險控制**：擁有不錯的年化報酬/MDD比例
- **技術改良**：參考《飆股的長相》裡的K線波動率，改善「低價股策略」的不穩定性

### 主軸元素
- **低股價**：專注30元以下的小型股
- **低波動**：K線波動率≤8%的穩定股票
- **營收成長**：短期營收動能大於長期動能

## 🔧 核心技術創新

### K線波動率濾網（核心技術）
**目的**：利用波動率找到穩定性較高的創高突破股，增加買點的安全性，降低追到假突破的機率。

**計算方式**：
```python
# 紅K波動率
bullish_volatility = abs(close.shift() - open_) + abs(open_ - low) + abs(low - high) + abs(high - close)

# 黑K波動率  
bearish_volatility = abs(close.shift() - open_) + abs(open_ - high) + abs(high-low) + abs(low - close)

# 標準化
volatility = candle_volatility.average(timeperiod) / close.average(timeperiod) * 100
```

### 風險控制特色
由於低波動的特性，能讓勝敗手的波動特性分離，可以停損設的小，大幅降低虧錢的交易的下檔風險，雖然勝率比較低，但果斷停損是為了防止未來持續失血。

## 📋 完整篩選條件

### 1. 創新高確認
- **條件**：收盤價近5日至少有1日創收盤價近100日新高
- **程式碼**：`(close == close.rolling(100).max()).sustain(5,1)`
- **目的**：確保股票處於突破上升趨勢

### 2. 價格區間控制（低波動核心）
- **條件**：近60日股價高低區間在30%內
- **程式碼**：`(1 - low.rolling(60).min()/high.rolling(60).max()) < 0.3`
- **目的**：篩選低波動、穩定的股票

### 3. 低股價雙重篩選
- **條件1**：收盤價低於整體市場分級的40%
- **條件2**：收盤價低於30元
- **程式碼**：`close <= close.quantile_row(0.4) & close <= 30`
- **目的**：專注小型股，提高成長空間

### 4. 流動性確認
- **條件**：5日均量大於100張
- **程式碼**：`vol.average(5) > 100*1000`
- **目的**：確保基本流動性

### 5. K線波動率濾網 ⭐核心⭐
- **條件**：K線波動率 ≤ 8%
- **目的**：改善低價股策略不穩定性的關鍵技術
- **重要性**：必要條件，核心技術指標

### 6. 營收動能確認
- **條件**：營收短期動能大於長期動能
- **程式碼**：`rev.average(2) > rev.average(12)`
- **目的**：確保基本面成長動能

### 7. 最終篩選
- **條件**：最後再挑選前8低價的標地
- **程式碼**：`close * (position.astype(int)) → position[position > 0].is_smallest(8)`
- **目的**：在符合條件的股票中，優選最低價的標的

## 📊 完整操作參數

### 交易執行設定
```python
report = sim(position, 
    resample="M",                    # 每月底產生訊號
    name="膽小貓", 
    upload=True, 
    stop_loss=0.03,                  # 3%停損
    trade_at_price='open',           # 開盤價進出
    position_limit=1/5,              # 每檔標的持有部位上限20%
    fee_ratio=1.425/1000*0.3,        # 手續費折扣
    mae_mfe_window=40                # MAE/MFE視窗40天
)
```

### 操作流程
1. **訊號產生**：每月底產生訊號
2. **進場時機**：隔月第一個交易日進場
3. **交易價格**：開盤價進出
4. **持有限制**：每檔標的持有部位上限為20%
5. **停損設定**：3%停損
6. **風險監控**：MAE/MFE視窗40天

## 🐱 膽小貓投資哲學

### 三大核心理念
1. **有不對勁就膽小走人**
   - 3%固定停損，果斷停損防止未來持續失血
   - K線波動率突然放大時立即停損
   - 營收動能轉弱時停損

2. **有順風的標的就長抱**
   - 月度調整，讓獲利奔跑
   - 持續符合條件的股票繼續持有
   - 低波動特性支持長期持有

3. **保持小賠大賺的節奏**
   - 雖然勝率比較低，但控制下檔風險
   - 低波動讓勝敗手波動特性分離
   - 小停損配合長期持有獲利股票

### 貓咪特質體現
- **體型小**：專注低股價股票（≤30元）
- **敏感**：對波動敏感，K線波動率≤8%
- **膽小**：喜歡安全的窩，不愛在外頭亂跑
- **謹慎**：嚴格的風險控制和篩選條件

## 🎯 實戰操作要點

### 成功關鍵
1. **嚴格執行**：嚴格按照7大條件篩選，不可妥協
2. **紀律停損**：3%停損必須嚴格執行
3. **月度調整**：按月重新評估和調整持股
4. **波動率核心**：K線波動率≤8%是核心條件，不可忽視
5. **貓咪心態**：保持膽小謹慎但敏銳的投資心態

### 風險控制
- **固定停損**：3%停損，防止持續失血
- **波動監控**：持續監控K線波動率變化
- **營收追蹤**：關注月營收公布，確認營收動能
- **流動性管理**：確保基本流動性，避免冷門股

### 績效監控
- **月度檢討**：每月底重新篩選，動態調整持股
- **MAE/MFE分析**：40天視窗分析最大利潤/虧損
- **回測驗證**：定期回測驗證策略有效性

## ⚠️ 注意事項

### 策略特性
- **勝率特性**：勝率比較低，但透過小停損控制風險
- **耐心等待**：需要耐心等待符合條件的股票出現
- **技術依賴**：高度依賴K線波動率技術指標

### 風險提醒
- **市場風險**：雖然適應性強，但仍需注意系統性風險
- **流動性風險**：低價股可能面臨流動性不足問題
- **小型股風險**：小型股波動可能較大

## 🎉 總結

膽小貓策略是一個經過歷史驗證的穩健投資方法，通過創新的K線波動率技術，成功改善了傳統低價股策略的不穩定性。策略體現了貓咪的特質：膽小謹慎但敏銳，在安全的環境中穩健成長。

**核心價值**：
- 歷史驗證有效（2008年至今幾乎年年獲利）
- 技術創新（K線波動率濾網）
- 風險控制優秀（小停損策略）
- 適合保守投資者的穩健投資法

像貓咪一樣，有不對勁就膽小走人，有順風的標的就長抱，保持小賠大賺的節奏！🐱
