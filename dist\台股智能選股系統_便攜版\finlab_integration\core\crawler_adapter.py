#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab爬蟲適配器
整合finlab的專業爬蟲功能到現有系統
"""

import datetime
import requests
import pandas as pd
import time
import logging
from io import StringIO
import numpy as np
from typing import Optional, Dict, List
import sqlite3

class FinlabCrawlerAdapter:
    """Finlab爬蟲功能適配器"""
    
    def __init__(self, db_connections: Dict = None):
        self.db_connections = db_connections or {}
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def requests_get(self, url: str, **kwargs) -> requests.Response:
        """安全的HTTP請求"""
        try:
            response = self.session.get(url, timeout=30, **kwargs)
            response.raise_for_status()
            return response
        except Exception as e:
            self.logger.error(f"HTTP請求失敗: {url}, 錯誤: {e}")
            raise
    
    def crawl_twse_price(self, date: datetime.date) -> pd.DataFrame:
        """爬取台股價格數據"""
        try:
            date_str = date.strftime('%Y%m%d')
            url = f"https://www.twse.com.tw/rwd/zh/afterTrading/MI_INDEX?date={date_str}&type=ALLBUT0999&response=csv"
            
            self.logger.info(f"爬取台股價格數據: {date_str}")
            response = self.requests_get(url)
            
            # 解析CSV數據
            lines = response.text.split('\n')
            # 找到數據開始的行
            data_start = 0
            for i, line in enumerate(lines):
                if '證券代號' in line:
                    data_start = i
                    break
            
            if data_start == 0:
                self.logger.warning(f"未找到數據: {date_str}")
                return pd.DataFrame()
            
            # 讀取數據
            csv_data = '\n'.join(lines[data_start:])
            df = pd.read_csv(StringIO(csv_data))
            
            # 數據清理和格式化
            df = df.dropna(how='all', axis=1).dropna(thresh=3, axis=0)
            
            if not df.empty:
                # 重命名欄位
                column_mapping = {
                    '證券代號': 'stock_id',
                    '證券名稱': 'stock_name', 
                    '開盤價': 'Open',
                    '最高價': 'High',
                    '最低價': 'Low',
                    '收盤價': 'Close',
                    '成交股數': 'Volume'
                }
                
                df = df.rename(columns=column_mapping)
                df['date'] = date
                
                # 數據類型轉換
                numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col].astype(str).str.replace(',', ''), errors='coerce')
                
                self.logger.info(f"成功爬取 {len(df)} 筆台股數據")
                return df
            
        except Exception as e:
            self.logger.error(f"爬取台股價格失敗: {e}")
            return pd.DataFrame()
    
    def crawl_otc_price(self, date: datetime.date) -> pd.DataFrame:
        """爬取櫃買價格數據"""
        try:
            date_str = date.strftime('%Y/%m/%d')
            url = f"https://www.tpex.org.tw/web/stock/aftertrading/otc_quotes_no1430/stk_wn1430_result.php?l=zh-tw&d={date_str}&se=AL"
            
            self.logger.info(f"爬取櫃買價格數據: {date_str}")
            response = self.requests_get(url)
            
            data = response.json()
            if 'aaData' not in data:
                return pd.DataFrame()
            
            # 轉換為DataFrame
            df = pd.DataFrame(data['aaData'])
            
            if not df.empty:
                # 設置欄位名稱
                df.columns = ['stock_id', 'stock_name', 'Close', 'change', 'Open', 
                             'High', 'Low', 'Volume', 'value', 'last_buy', 'last_sell']
                
                df['date'] = date
                
                # 數據類型轉換
                numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col].astype(str).str.replace(',', ''), errors='coerce')
                
                self.logger.info(f"成功爬取 {len(df)} 筆櫃買數據")
                return df
            
        except Exception as e:
            self.logger.error(f"爬取櫃買價格失敗: {e}")
            return pd.DataFrame()
    
    def crawl_stock_price(self, date: datetime.date) -> pd.DataFrame:
        """爬取完整股價數據（台股+櫃買）"""
        try:
            # 爬取台股數據
            twse_df = self.crawl_twse_price(date)
            time.sleep(3)  # 避免請求過於頻繁
            
            # 爬取櫃買數據
            otc_df = self.crawl_otc_price(date)
            
            # 合併數據
            if not twse_df.empty and not otc_df.empty:
                # 確保欄位一致
                common_columns = ['stock_id', 'stock_name', 'Open', 'High', 'Low', 'Close', 'Volume', 'date']
                twse_df = twse_df[common_columns]
                otc_df = otc_df[common_columns]
                
                combined_df = pd.concat([twse_df, otc_df], ignore_index=True)
                self.logger.info(f"合併完成，總計 {len(combined_df)} 筆數據")
                return combined_df
            elif not twse_df.empty:
                return twse_df
            elif not otc_df.empty:
                return otc_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"爬取股價數據失敗: {e}")
            return pd.DataFrame()
    
    def save_to_database(self, df: pd.DataFrame, table_name: str = 'stock_prices'):
        """保存數據到數據庫"""
        if df.empty:
            self.logger.warning("數據為空，跳過保存")
            return False
        
        try:
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                df.to_sql(table_name, conn, if_exists='append', index=False)
                self.logger.info(f"成功保存 {len(df)} 筆數據到數據庫")
                return True
            else:
                self.logger.warning("未找到數據庫連接")
                return False
                
        except Exception as e:
            self.logger.error(f"保存數據失敗: {e}")
            return False
    
    def crawl_date_range(self, start_date: datetime.date, end_date: datetime.date):
        """爬取日期範圍內的數據"""
        current_date = start_date
        success_count = 0
        total_days = (end_date - start_date).days + 1
        
        self.logger.info(f"開始爬取 {start_date} 到 {end_date} 的數據")
        
        while current_date <= end_date:
            # 跳過週末
            if current_date.weekday() < 5:  # 0-4 是週一到週五
                try:
                    df = self.crawl_stock_price(current_date)
                    if not df.empty:
                        if self.save_to_database(df):
                            success_count += 1
                    
                    # 避免請求過於頻繁
                    time.sleep(5)
                    
                except Exception as e:
                    self.logger.error(f"處理日期 {current_date} 失敗: {e}")
            
            current_date += datetime.timedelta(days=1)
        
        self.logger.info(f"爬取完成，成功處理 {success_count} 個交易日")
        return success_count

    def get_latest_data_date(self) -> Optional[datetime.date]:
        """獲取數據庫中最新的數據日期"""
        try:
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                cursor = conn.cursor()
                cursor.execute("SELECT MAX(date) FROM stock_prices")
                result = cursor.fetchone()
                if result and result[0]:
                    return datetime.datetime.strptime(result[0], '%Y-%m-%d').date()
            return None
        except Exception as e:
            self.logger.error(f"獲取最新數據日期失敗: {e}")
            return None
    
    def update_to_latest(self):
        """更新數據到最新日期"""
        try:
            latest_date = self.get_latest_data_date()
            today = datetime.date.today()
            
            if latest_date is None:
                # 如果沒有數據，從30天前開始
                start_date = today - datetime.timedelta(days=30)
            else:
                # 從最新日期的下一天開始
                start_date = latest_date + datetime.timedelta(days=1)
            
            if start_date <= today:
                self.logger.info(f"更新數據從 {start_date} 到 {today}")
                return self.crawl_date_range(start_date, today)
            else:
                self.logger.info("數據已是最新")
                return 0
                
        except Exception as e:
            self.logger.error(f"更新數據失敗: {e}")
            return 0
