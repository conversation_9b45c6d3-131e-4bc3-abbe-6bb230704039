#!/usr/bin/env python3
"""
測試監控股票表格的公司名稱欄位功能
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_monitor_table_structure():
    """測試監控表格結構"""
    print("🧪 測試監控股票表格的公司名稱欄位功能")
    print("=" * 60)
    
    try:
        # 導入GUI模組
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建GUI實例
        gui = StockScreenerGUI()
        
        # 檢查監控表格的列數和標題
        if hasattr(gui, 'intraday_data_table'):
            table = gui.intraday_data_table
            column_count = table.columnCount()
            
            print(f"📊 監控表格列數: {column_count}")
            
            # 獲取列標題
            headers = []
            for i in range(column_count):
                header_item = table.horizontalHeaderItem(i)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append(f"列{i}")
            
            print("📋 列標題:")
            for i, header in enumerate(headers):
                print(f"  {i}: {header}")
            
            # 檢查是否包含公司名稱欄位
            if "公司名稱" in headers:
                company_name_index = headers.index("公司名稱")
                print(f"✅ 公司名稱欄位已添加，位於第 {company_name_index} 列")
                
                # 檢查欄位順序是否正確
                expected_order = [
                    "股票代碼", "最新價", "公司名稱", "漲跌", "漲跌%", 
                    "成交量", "更新時間", "狀態", "操作"
                ]
                
                if headers == expected_order:
                    print("✅ 列標題順序正確")
                else:
                    print("❌ 列標題順序不正確")
                    print(f"期望順序: {expected_order}")
                    print(f"實際順序: {headers}")
                
            else:
                print("❌ 公司名稱欄位未找到")
                return False
            
            # 測試get_stock_name方法
            if hasattr(gui, 'get_stock_name'):
                print("\n🔍 測試get_stock_name方法:")
                test_stock_id = "2330"
                stock_name = gui.get_stock_name(test_stock_id)
                print(f"  股票代碼 {test_stock_id} 的名稱: {stock_name}")
                
                if stock_name and stock_name != f"股票{test_stock_id}":
                    print("✅ get_stock_name方法工作正常")
                else:
                    print("⚠️ get_stock_name方法返回默認值（可能是數據庫未連接）")
            else:
                print("❌ get_stock_name方法未找到")
                return False
            
            print("\n✅ 監控表格公司名稱欄位功能測試完成")
            return True
            
        else:
            print("❌ 監控表格未找到")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        logging.error(f"測試錯誤: {e}")
        return False

def test_column_width_settings():
    """測試列寬設置"""
    print("\n🔧 測試列寬設置")
    print("-" * 40)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        gui = StockScreenerGUI()
        
        if hasattr(gui, 'intraday_data_table'):
            table = gui.intraday_data_table
            
            # 檢查列寬設置
            expected_widths = {
                0: 80,   # 股票代碼
                1: 80,   # 最新價
                2: 120,  # 公司名稱
                3: 60,   # 漲跌
                4: 70,   # 漲跌%
                5: 100,  # 成交量
                6: 80,   # 更新時間
                7: 60,   # 狀態
                8: 80    # 操作
            }
            
            print("📏 列寬設置:")
            all_correct = True
            for col, expected_width in expected_widths.items():
                actual_width = table.columnWidth(col)
                status = "✅" if actual_width == expected_width else "❌"
                print(f"  列{col}: {actual_width}px (期望: {expected_width}px) {status}")
                if actual_width != expected_width:
                    all_correct = False
            
            if all_correct:
                print("✅ 所有列寬設置正確")
            else:
                print("❌ 部分列寬設置不正確")
            
            return all_correct
            
    except Exception as e:
        print(f"❌ 測試列寬設置失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 開始測試監控股票表格的公司名稱欄位功能")
    print("=" * 80)
    
    # 測試表格結構
    structure_ok = test_monitor_table_structure()
    
    # 測試列寬設置
    width_ok = test_column_width_settings()
    
    print("\n" + "=" * 80)
    print("📊 測試結果總結:")
    print(f"  表格結構: {'✅ 通過' if structure_ok else '❌ 失敗'}")
    print(f"  列寬設置: {'✅ 通過' if width_ok else '❌ 失敗'}")
    
    if structure_ok and width_ok:
        print("\n🎉 所有測試通過！監控股票表格的公司名稱欄位功能已成功添加。")
        print("\n📋 功能說明:")
        print("  • 監控股票表格現在包含9列")
        print("  • 公司名稱欄位位於第3列（最新價之後）")
        print("  • 公司名稱會從數據庫中自動獲取")
        print("  • 如果數據庫中沒有找到，會顯示默認格式")
    else:
        print("\n❌ 部分測試失敗，請檢查修改是否正確。")
    
    print("\n" + "=" * 80)
