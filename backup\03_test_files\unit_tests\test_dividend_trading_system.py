#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息交易系統測試腳本
演示蘇松泙除權息操作策略的程式化實現
"""

import pandas as pd
from datetime import datetime, timedelta
from dividend_trading_system import DividendTradingSystem

def test_market_sentiment_evaluation():
    """測試市場情緒評估功能"""
    print("=" * 60)
    print("🔍 測試市場情緒評估功能")
    print("=" * 60)
    
    system = DividendTradingSystem()
    
    # 步驟1: 評估市場情緒
    print("\n📊 執行市場情緒評估...")
    result = system.evaluate_market_sentiment()
    
    print(f"✅ 評估完成!")
    print(f"   情緒分數: {result['sentiment_score']:.1f}/100")
    print(f"   市場狀態: {result['sentiment']}")
    print(f"   建議策略: {result['strategy']}")
    print(f"   資金投入比例: {result['position_ratio']:.1%}")
    
    # 顯示個股表現
    print(f"\n📈 率先除權息個股表現:")
    for stock_code, performance in result.get('performance_data', {}).items():
        print(f"   {stock_code}: 當日{performance['same_day_gain']:+.1f}元, "
              f"填息: {'是' if performance['filled_dividend'] else '否'}")
    
    return result

def test_trading_candidates():
    """測試交易候選股票功能"""
    print("\n" + "=" * 60)
    print("🎯 測試交易候選股票功能")
    print("=" * 60)
    
    system = DividendTradingSystem()
    
    # 獲取未來7天的候選股票
    print("\n📋 獲取未來7天除權息候選股票...")
    candidates = system.get_trading_candidates(days_ahead=7)
    
    print(f"✅ 找到 {len(candidates)} 個交易候選:")
    
    for candidate in candidates:
        print(f"\n📊 {candidate['stock_code']} {candidate['stock_name']}")
        print(f"   除權息日: {candidate['ex_date']}")
        print(f"   現金股利: {candidate['cash_dividend']:.1f}元")
        print(f"   距離天數: {candidate['days_to_ex']}天")
        print(f"   績優股: {'是' if candidate['is_premium_stock'] else '否'}")
        
        recommendation = candidate['recommended_action']
        print(f"   建議策略: {recommendation.get('strategy', '標準策略')}")
        print(f"   進場時機: {recommendation['entry_timing']}")
        print(f"   出場策略: {recommendation['exit_strategy']}")
        print(f"   風險等級: {recommendation['risk_level']}")
        print(f"   建議倉位: {recommendation['position_size']:.1%}")
    
    return candidates

def test_trading_execution():
    """測試交易執行功能"""
    print("\n" + "=" * 60)
    print("⚡ 測試交易執行功能")
    print("=" * 60)
    
    system = DividendTradingSystem()
    
    # 步驟2: 測試買進執行
    print("\n💰 測試買進執行...")
    test_stock = "2330"  # 台積電
    
    buy_result = system.execute_buy_signal(test_stock)
    
    if buy_result['success']:
        print(f"✅ {test_stock} 買進成功:")
        print(f"   成交價格: {buy_result['price']}元")
        print(f"   買進數量: {buy_result['quantity']}股")
        print(f"   總成本: {buy_result['total_cost']:,.0f}元")
        print(f"   成交時間: {buy_result['trade_time']}")
    else:
        print(f"❌ {test_stock} 買進失敗: {buy_result['reason']}")
    
    # 步驟3: 測試除權息當日分析
    print(f"\n📊 測試除權息當日分析...")
    analysis_result = system.analyze_ex_dividend_performance(test_stock)
    
    print(f"✅ {test_stock} 分析完成:")
    print(f"   建議動作: {analysis_result['action']}")
    print(f"   決策理由: {analysis_result['reason']}")
    print(f"   緊急程度: {analysis_result.get('urgency', 'N/A')}")
    
    # 如果建議賣出，執行賣出
    if analysis_result['action'] in ['SELL', 'PARTIAL_SELL']:
        print(f"\n💸 執行賣出...")
        sell_result = system.execute_sell_signal(test_stock, analysis_result)
        
        if sell_result['success']:
            print(f"✅ {test_stock} 賣出成功:")
            print(f"   成交價格: {sell_result['price']}元")
            print(f"   賣出數量: {sell_result['quantity']}股")
            print(f"   總金額: {sell_result['total_amount']:,.0f}元")
            print(f"   損益: {sell_result['profit_loss']:+,.0f}元")
        else:
            print(f"❌ {test_stock} 賣出失敗: {sell_result['reason']}")
    
    return buy_result, analysis_result

def test_premium_stock_strategy():
    """測試績優股特殊策略"""
    print("\n" + "=" * 60)
    print("🏆 測試績優股特殊策略")
    print("=" * 60)
    
    system = DividendTradingSystem()
    
    # 測試台積電策略
    print("\n📊 測試台積電先跌後漲策略...")
    tsmc_analysis = system.analyze_ex_dividend_performance("2330")
    print(f"台積電策略: {tsmc_analysis['action']} - {tsmc_analysis['reason']}")
    
    # 測試鴻海策略
    print("\n📊 測試鴻海預設停利策略...")
    hon_hai_analysis = system.analyze_ex_dividend_performance("2317")
    print(f"鴻海策略: {hon_hai_analysis['action']} - {hon_hai_analysis['reason']}")
    
    return tsmc_analysis, hon_hai_analysis

def test_trading_report():
    """測試交易報告功能"""
    print("\n" + "=" * 60)
    print("📈 測試交易報告功能")
    print("=" * 60)
    
    system = DividendTradingSystem()
    
    # 生成交易報告
    print("\n📊 生成交易報告...")
    report = system.generate_trading_report()
    
    if 'error' not in report:
        summary = report['summary']
        print(f"✅ 交易報告生成成功:")
        print(f"   總交易次數: {summary['total_trades']}")
        print(f"   總損益: {summary['total_profit_loss']:+,.2f}元")
        print(f"   勝率: {summary['win_rate']:.1f}%")
        
        if summary['current_sentiment'] is not None:
            print(f"   當前市場情緒: {summary['current_sentiment']:.1f}/100")
        
        # 顯示最近交易
        recent_trades = report.get('recent_trades', [])
        if recent_trades:
            print(f"\n📋 最近交易記錄:")
            for trade in recent_trades[:3]:  # 只顯示前3筆
                print(f"   {trade['trade_date']} {trade['stock_code']} "
                      f"{trade['action']} {trade['quantity']}股 @ {trade['price']}元")
    else:
        print(f"❌ 交易報告生成失敗: {report['error']}")
    
    return report

def demonstrate_complete_workflow():
    """演示完整的除權息交易流程"""
    print("\n" + "=" * 80)
    print("🎯 完整除權息交易流程演示")
    print("基於蘇松泙《平民股神不蝕本投資戰法》")
    print("=" * 80)
    
    system = DividendTradingSystem()
    
    # 完整流程演示
    print("\n🔄 執行完整交易流程...")
    
    # 步驟1: 市場情緒評估
    print("\n【步驟1】市場情緒評估")
    print("-" * 40)
    sentiment_result = system.evaluate_market_sentiment()
    print(f"市場情緒: {sentiment_result['sentiment']} ({sentiment_result['sentiment_score']:.1f}/100)")
    print(f"投資策略: {sentiment_result['strategy']}")
    
    # 步驟2: 選擇交易候選
    print("\n【步驟2】選擇交易候選")
    print("-" * 40)
    candidates = system.get_trading_candidates(days_ahead=7)
    print(f"找到 {len(candidates)} 個候選股票")
    
    # 選擇最佳候選（這裡選擇第一個）
    if candidates:
        best_candidate = candidates[0]
        stock_code = best_candidate['stock_code']
        print(f"選擇交易標的: {stock_code} {best_candidate['stock_name']}")
        print(f"除權息日: {best_candidate['ex_date']}")
        print(f"現金股利: {best_candidate['cash_dividend']:.1f}元")
        
        # 步驟3: 執行買進（模擬1:25分定格買進）
        print("\n【步驟3】執行買進（1:25分定格）")
        print("-" * 40)
        buy_result = system.execute_buy_signal(stock_code)
        
        if buy_result['success']:
            print(f"✅ 買進成功: {buy_result['quantity']}股 @ {buy_result['price']}元")
            
            # 步驟4: 除權息當日分析
            print("\n【步驟4】除權息當日分析")
            print("-" * 40)
            analysis = system.analyze_ex_dividend_performance(stock_code)
            print(f"分析結果: {analysis['action']} - {analysis['reason']}")
            
            # 步驟5: 執行賣出決策
            if analysis['action'] in ['SELL', 'PARTIAL_SELL']:
                print("\n【步驟5】執行賣出")
                print("-" * 40)
                sell_result = system.execute_sell_signal(stock_code, analysis)
                
                if sell_result['success']:
                    print(f"✅ 賣出成功: {sell_result['quantity']}股 @ {sell_result['price']}元")
                    print(f"💰 本次交易損益: {sell_result['profit_loss']:+,.0f}元")
            else:
                print("\n【步驟5】持續持有")
                print("-" * 40)
                print("根據策略建議，繼續持有股票")
        else:
            print(f"❌ 買進失敗: {buy_result['reason']}")
    
    # 步驟6: 生成交易報告
    print("\n【步驟6】交易報告")
    print("-" * 40)
    report = system.generate_trading_report()
    if 'error' not in report:
        summary = report['summary']
        print(f"總交易次數: {summary['total_trades']}")
        print(f"總損益: {summary['total_profit_loss']:+,.2f}元")
        print(f"勝率: {summary['win_rate']:.1f}%")
    
    print("\n🎉 完整流程演示結束!")

def run_all_tests():
    """執行所有測試"""
    print("🧪 除權息交易系統全面測試")
    print("=" * 80)
    
    try:
        # 1. 市場情緒評估測試
        sentiment_result = test_market_sentiment_evaluation()
        
        # 2. 交易候選測試
        candidates = test_trading_candidates()
        
        # 3. 交易執行測試
        buy_result, analysis_result = test_trading_execution()
        
        # 4. 績優股策略測試
        tsmc_analysis, hon_hai_analysis = test_premium_stock_strategy()
        
        # 5. 交易報告測試
        report = test_trading_report()
        
        # 6. 完整流程演示
        demonstrate_complete_workflow()
        
        print("\n" + "=" * 80)
        print("✅ 所有測試完成!")
        print("🎯 除權息交易系統已準備就緒")
        print("💡 可以使用 python dividend_trading_gui.py 啟動GUI界面")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
