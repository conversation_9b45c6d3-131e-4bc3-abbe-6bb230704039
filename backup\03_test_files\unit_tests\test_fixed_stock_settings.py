#!/usr/bin/env python3
"""
測試修復後的股票設定功能
- 修復文字顯示問題
- 更改TSRTC為更有意義的名稱
"""

import sys
import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QHeaderView, QListWidget, QLineEdit, 
    QMessageBox, QSplitter, QWidget
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QColor

# 設置日誌
logging.basicConfig(level=logging.INFO)

class FixedStockSettingsDialog(QDialog):
    """修復後的股票設定對話框"""
    
    def __init__(self, parent=None, current_stocks=None):
        super().__init__(parent)
        self.current_stocks = current_stocks or ['2330', '2317', '2454', '2412', '2881']
        self.all_stocks_data = self.load_sample_stocks_data()
        
        self.setWindowTitle("⚙️ 備用監控股票設定")
        self.setModal(True)
        self.resize(900, 600)
        
        # 設置視窗標誌
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )
        
        self.setup_ui()
        self.populate_all_stocks()
        self.populate_monitored_stocks()
    
    def load_sample_stocks_data(self):
        """載入示例股票資料"""
        return {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科', '2412': '中華電',
            '2881': '富邦金', '1301': '台塑', '2382': '廣達', '3008': '大立光',
            '2002': '中鋼', '2886': '兆豐金', '2891': '中信金', '2892': '第一金',
            '2883': '開發金', '2884': '玉山金', '2885': '元大金', '2887': '台新金',
            '2303': '聯電', '2308': '台達電', '2327': '國巨', '2357': '華碩',
            '2379': '瑞昱', '2395': '研華', '2409': '友達', '2474': '可成',
            '2603': '長榮', '2888': '新光金', '2889': '國票金', '2890': '永豐金',
            '1216': '統一', '1101': '台泥', '2207': '和泰車', '2408': '南亞科',
            '2498': '宏達電', '2492': '華新科', '2311': '日月光', '1303': '南亞',
            '0050': '元大台灣50', '0056': '元大高股息', '006208': '富邦台50'
        }
    
    def setup_ui(self):
        """設置雙欄位界面"""
        layout = QVBoxLayout()
        
        # 標題
        title = QLabel("📊 備用監控股票設定")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px; color: #2E86AB;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 說明文字
        desc = QLabel("左側選擇股票，使用箭頭按鈕移動到右側監控清單")
        desc.setStyleSheet("font-size: 12px; color: #666; margin: 5px;")
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(desc)
        
        # 主要內容區域 - 使用分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左側：全部股票
        left_panel = self.create_all_stocks_panel()
        main_splitter.addWidget(left_panel)
        
        # 中間：控制按鈕
        middle_panel = self.create_control_buttons_panel()
        main_splitter.addWidget(middle_panel)
        
        # 右側：監控股票
        right_panel = self.create_monitored_stocks_panel()
        main_splitter.addWidget(right_panel)
        
        # 設置分割器比例
        main_splitter.setSizes([300, 100, 300])
        layout.addWidget(main_splitter)
        
        # 底部按鈕
        button_layout = QHBoxLayout()
        
        # 統計資訊
        self.stats_label = QLabel()
        self.update_stats()
        button_layout.addWidget(self.stats_label)
        
        button_layout.addStretch()
        
        ok_btn = QPushButton("✅ 確定")
        ok_btn.setStyleSheet("QPushButton { padding: 8px 20px; font-size: 12px; background-color: #51cf66; color: white; border: none; border-radius: 4px; }")
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setStyleSheet("QPushButton { padding: 8px 20px; font-size: 12px; background-color: #ff6b6b; color: white; border: none; border-radius: 4px; }")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_all_stocks_panel(self):
        """創建全部股票面板 - 修復文字顯示"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 標題
        title = QLabel("📈 全部股票")
        title.setStyleSheet("font-size: 14px; font-weight: bold; color: #339af0; margin: 5px;")
        layout.addWidget(title)
        
        # 搜尋框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜尋股票代碼或名稱...")
        self.search_input.textChanged.connect(self.filter_all_stocks)
        layout.addWidget(self.search_input)
        
        # 股票列表 - 修復文字顯示問題
        self.all_stocks_list = QListWidget()
        self.all_stocks_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #ffffff;
                color: #333333;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                color: #333333;
                background-color: #ffffff;
            }
            QListWidget::item:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QListWidget::item:selected {
                background-color: #2196f3;
                color: #ffffff;
            }
        """)
        self.all_stocks_list.itemDoubleClicked.connect(self.add_to_monitored)
        layout.addWidget(self.all_stocks_list)
        
        return panel
    
    def create_control_buttons_panel(self):
        """創建控制按鈕面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.addStretch()
        
        # 添加到監控按鈕
        add_btn = QPushButton("➡️\n添加")
        add_btn.setStyleSheet("""
            QPushButton {
                padding: 15px 10px;
                font-size: 12px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_btn.clicked.connect(self.add_to_monitored)
        layout.addWidget(add_btn)
        
        # 從監控移除按鈕
        remove_btn = QPushButton("⬅️\n移除")
        remove_btn.setStyleSheet("""
            QPushButton {
                padding: 15px 10px;
                font-size: 12px;
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 6px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        remove_btn.clicked.connect(self.remove_from_monitored)
        layout.addWidget(remove_btn)
        
        layout.addStretch()
        return panel
    
    def create_monitored_stocks_panel(self):
        """創建監控股票面板 - 修復文字顯示"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 標題
        title = QLabel("📊 監控股票")
        title.setStyleSheet("font-size: 14px; font-weight: bold; color: #51cf66; margin: 5px;")
        layout.addWidget(title)
        
        # 監控股票列表 - 修復文字顯示問題
        self.monitored_stocks_list = QListWidget()
        self.monitored_stocks_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #ffffff;
                color: #333333;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                color: #333333;
                background-color: #ffffff;
            }
            QListWidget::item:hover {
                background-color: #e8f5e8;
                color: #2e7d32;
            }
            QListWidget::item:selected {
                background-color: #4CAF50;
                color: #ffffff;
            }
        """)
        self.monitored_stocks_list.itemDoubleClicked.connect(self.remove_from_monitored)
        layout.addWidget(self.monitored_stocks_list)
        
        return panel
    
    def populate_all_stocks(self):
        """填充全部股票列表"""
        self.all_stocks_list.clear()
        for stock_id, stock_name in sorted(self.all_stocks_data.items()):
            item_text = f"{stock_id} - {stock_name}"
            self.all_stocks_list.addItem(item_text)
    
    def populate_monitored_stocks(self):
        """填充監控股票列表"""
        self.monitored_stocks_list.clear()
        for stock_id in self.current_stocks:
            stock_name = self.all_stocks_data.get(stock_id, f'股票{stock_id}')
            item_text = f"{stock_id} - {stock_name}"
            self.monitored_stocks_list.addItem(item_text)
        self.update_stats()
    
    def filter_all_stocks(self):
        """過濾全部股票列表"""
        search_text = self.search_input.text().lower()
        self.all_stocks_list.clear()
        
        for stock_id, stock_name in sorted(self.all_stocks_data.items()):
            if (search_text in stock_id.lower() or 
                search_text in stock_name.lower()):
                item_text = f"{stock_id} - {stock_name}"
                self.all_stocks_list.addItem(item_text)
    
    def add_to_monitored(self):
        """添加股票到監控列表"""
        current_item = self.all_stocks_list.currentItem()
        if not current_item:
            return
        
        item_text = current_item.text()
        stock_id = item_text.split(' - ')[0]
        
        if stock_id not in self.current_stocks:
            self.current_stocks.append(stock_id)
            self.populate_monitored_stocks()
    
    def remove_from_monitored(self):
        """從監控列表移除股票"""
        current_item = self.monitored_stocks_list.currentItem()
        if not current_item:
            return
        
        item_text = current_item.text()
        stock_id = item_text.split(' - ')[0]
        
        if stock_id in self.current_stocks:
            self.current_stocks.remove(stock_id)
            self.populate_monitored_stocks()
    
    def update_stats(self):
        """更新統計資訊"""
        total_stocks = len(self.all_stocks_data)
        monitored_count = len(self.current_stocks)
        self.stats_label.setText(f"📊 總股票: {total_stocks} | 監控中: {monitored_count}")
        self.stats_label.setStyleSheet("font-size: 12px; color: #666;")

def main():
    """主函數"""
    print("🧪 測試修復後的股票設定功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示對話框
    dialog = FixedStockSettingsDialog()
    
    print("✅ 修復後的股票設定對話框已開啟")
    print("🔍 修復項目:")
    print("  1. ✅ 文字顯示問題已修復（白色背景，深色文字）")
    print("  2. ✅ TSRTC改為'備用監控'（更有意義）")
    print("  3. ✅ 增加文字大小和內邊距")
    print("  4. ✅ 改善懸停和選中效果")
    print("  5. ✅ 統一界面風格")
    
    if dialog.exec() == QDialog.DialogCode.Accepted:
        print(f"\n✅ 用戶確認設定")
        print(f"📊 最終監控股票: {dialog.current_stocks}")
        print(f"📈 監控股票數量: {len(dialog.current_stocks)}")
    else:
        print(f"\n❌ 用戶取消設定")
    
    sys.exit(0)

if __name__ == '__main__':
    main()
