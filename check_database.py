#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
檢查數據庫結構
"""

import sqlite3
import os
from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

def check_database_structure():
    """檢查數據庫結構"""
    downloader = GoodinfoROECSVDownloader()
    
    print(f"數據庫路徑: {downloader.db_path}")
    
    if not os.path.exists(downloader.db_path):
        print("❌ 數據庫檔案不存在")
        return
    
    try:
        conn = sqlite3.connect(downloader.db_path)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roe_data'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ roe_data 表格不存在")
            conn.close()
            return
        
        print("✅ roe_data 表格存在")
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(roe_data)")
        columns = cursor.fetchall()
        
        print("\n📋 表格結構:")
        for col in columns:
            cid, name, type_, notnull, default, pk = col
            null_str = "NOT NULL" if notnull else "NULL"
            pk_str = "PRIMARY KEY" if pk else ""
            default_str = f"DEFAULT {default}" if default else ""
            print(f"  {name}: {type_} {null_str} {default_str} {pk_str}")
        
        # 檢查約束
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='roe_data'")
        create_sql = cursor.fetchone()[0]
        print(f"\n📝 建表語句:")
        print(create_sql)
        
        # 檢查資料數量
        cursor.execute("SELECT COUNT(*) FROM roe_data")
        count = cursor.fetchone()[0]
        print(f"\n📊 資料數量: {count} 筆")
        
        if count > 0:
            # 顯示前幾筆資料
            cursor.execute("SELECT * FROM roe_data LIMIT 3")
            rows = cursor.fetchall()
            print("\n📝 前3筆資料:")
            for row in rows:
                print(f"  {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查數據庫失敗: {e}")

def recreate_database():
    """重新創建數據庫"""
    downloader = GoodinfoROECSVDownloader()
    
    print(f"🔄 重新創建數據庫: {downloader.db_path}")
    
    try:
        # 刪除舊數據庫
        if os.path.exists(downloader.db_path):
            os.remove(downloader.db_path)
            print("🗑️ 已刪除舊數據庫")
        
        # 創建新數據庫
        conn = sqlite3.connect(downloader.db_path)
        downloader.create_roe_table(conn)
        conn.close()
        
        print("✅ 新數據庫創建成功")
        
        # 檢查新結構
        check_database_structure()
        
    except Exception as e:
        print(f"❌ 重新創建數據庫失敗: {e}")

def main():
    print("🔍 數據庫檢查工具")
    print("=" * 40)
    
    print("1. 檢查當前數據庫結構")
    check_database_structure()
    
    print("\n" + "=" * 40)
    choice = input("是否要重新創建數據庫? (y/N): ").strip().lower()
    
    if choice == 'y':
        print("\n2. 重新創建數據庫")
        recreate_database()
    else:
        print("保持現有數據庫")

if __name__ == "__main__":
    main()
