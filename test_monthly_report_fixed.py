#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復版的 monthly_report 爬蟲
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import datetime
import pandas as pd
import time
import random
import urllib3
import requests
from io import StringIO

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 從 auto_update.py 導入修復版函數
from auto_update import crawl_monthly_report_fixed

def test_fixed_monthly_report():
    """測試修復版的 monthly_report 爬蟲"""
    print("🔧 測試修復版 Monthly Report 爬蟲")
    print("=" * 60)
    
    # 測試較舊的月份 (確定已發布)
    test_date = datetime.datetime(2025, 1, 10)  # 2024年12月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年12月營收)")
    
    try:
        print("🔄 開始測試修復版爬蟲...")
        result = crawl_monthly_report_fixed(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ 修復版爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)[:5]}...")
            
            # 檢查一些知名股票
            stock_ids = result.index.get_level_values('stock_id')
            print(f"   股票數量: {len(stock_ids.unique())}")
            
            # 尋找台積電
            tsmc_candidates = [sid for sid in stock_ids if '2330' in str(sid) or '台積電' in str(sid)]
            if tsmc_candidates:
                tsmc_id = tsmc_candidates[0]
                tsmc_data = result.loc[tsmc_id]
                print(f"   找到台積電 ({tsmc_id})")
                if '當月營收' in result.columns:
                    revenue = tsmc_data['當月營收'].iloc[0] if hasattr(tsmc_data['當月營收'], 'iloc') else tsmc_data['當月營收']
                    print(f"   台積電營收: {revenue}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('monthly_report_test_fixed.pkl')
            print(f"\n💾 測試資料已保存: monthly_report_test_fixed.pkl")
            
            return True
        else:
            print(f"\n⚠️ 修復版爬蟲無資料")
            return False
            
    except Exception as e:
        print(f"\n❌ 修復版爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def compare_with_existing():
    """與現有資料比較"""
    print(f"\n🔍 與現有 monthly_report.pkl 比較")
    print("=" * 60)
    
    try:
        existing_file = 'history/tables/monthly_report.pkl'
        if os.path.exists(existing_file):
            existing_data = pd.read_pickle(existing_file)
            print(f"📊 現有資料:")
            print(f"   筆數: {len(existing_data):,}")
            
            dates = existing_data.index.get_level_values('date')
            print(f"   日期範圍: {dates.min()} 至 {dates.max()}")
            
            # 檢查最新資料
            latest_date = dates.max()
            print(f"   最新資料: {latest_date}")
            
            # 計算需要更新的月數
            today = datetime.datetime.now()
            months_behind = (today.year - latest_date.year) * 12 + (today.month - latest_date.month)
            print(f"   落後月數: {months_behind} 個月")
            
            if months_behind > 1:
                print(f"   ✅ 需要更新，修復版爬蟲很有用")
            else:
                print(f"   ⚠️ 資料較新，可能不需要立即更新")
        else:
            print(f"❌ 現有檔案不存在")
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 Monthly Report 修復版測試工具")
    print("=" * 60)
    print("🎯 目標: 測試修復版月營收爬蟲")
    print("💡 改進: requests.get + headers + 重試 + 更長延遲")
    print("=" * 60)
    
    # 測試修復版
    success = test_fixed_monthly_report()
    
    # 與現有資料比較
    compare_with_existing()
    
    if success:
        print(f"\n🎉 Monthly Report 修復版測試成功！")
        print(f"💡 現在可以在 auto_update.py 中使用:")
        print(f"   ('monthly_report', crawl_monthly_report, month_range),")
        print(f"🔧 系統會自動使用修復版爬蟲")
    else:
        print(f"\n⚠️ 修復版仍有問題，可能需要進一步調整")

if __name__ == "__main__":
    import os
    main()
