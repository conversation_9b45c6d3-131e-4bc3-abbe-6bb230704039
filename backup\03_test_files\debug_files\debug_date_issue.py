#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試除權息日期問題
"""

import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_date_issue():
    """調試除權息日期問題"""
    print("🔍 調試除權息日期問題...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 測試證交所API - 查詢2024年7月
    print("\n📊 測試證交所API - 2024年7月資料:")
    url = "https://www.twse.com.tw/exchangeReport/TWT49U"
    params = {
        'response': 'json',
        'date': '20240701',  # 2024年7月
        'selectType': 'ALL'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        data = response.json()
        
        print(f"API標題: {data.get('title', 'N/A')}")
        print(f"資料筆數: {len(data.get('data', []))}")
        
        if 'data' in data and data['data']:
            print("\n前5筆原始資料:")
            for i, record in enumerate(data['data'][:5]):
                print(f"第{i+1}筆: {record}")
                
                # 分析日期欄位
                ex_date = record[0]
                stock_code = record[1]
                stock_name = record[2]
                
                print(f"  -> 股票: {stock_code} {stock_name}")
                print(f"  -> 原始除權息日期: '{ex_date}'")
                
                # 檢查日期格式
                if '年' in ex_date:
                    import re
                    match = re.match(r'(\d+)年(\d+)月(\d+)日', ex_date)
                    if match:
                        roc_year, month, day = match.groups()
                        ad_year = int(roc_year) + 1911
                        print(f"  -> 民國年: {roc_year}, 轉換後西元年: {ad_year}")
                        print(f"  -> 完整日期: {ad_year}-{int(month):02d}-{int(day):02d}")
                
                print()
        
        # 檢查API的日期範圍
        print(f"API回應的日期範圍:")
        print(f"  開始日期: {data.get('strDate', 'N/A')}")
        print(f"  結束日期: {data.get('endDate', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 證交所API測試失敗: {e}")
    
    # 測試櫃買中心API
    print("\n\n📊 測試櫃買中心API - 2024年7月資料:")
    url = "https://www.tpex.org.tw/web/stock/exright/dailyquo/exDailyQ_result.php"
    params = {
        'l': 'zh-tw',
        'd': '113/07/01',  # 民國113年 = 西元2024年
        'stkno': '',
        'o': 'json'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        data = response.json()
        
        print(f"API日期範圍: {data.get('date', 'N/A')}")
        
        if 'tables' in data and data['tables'] and len(data['tables']) > 0:
            table_data = data['tables'][0].get('data', [])
            print(f"資料筆數: {len(table_data)}")
            
            if table_data:
                print("\n前3筆原始資料:")
                for i, record in enumerate(table_data[:3]):
                    print(f"第{i+1}筆: {record}")
                    
                    ex_date = record[0]
                    stock_code = record[1]
                    stock_name = record[2]
                    
                    print(f"  -> 股票: {stock_code} {stock_name}")
                    print(f"  -> 原始除權息日期: '{ex_date}'")
                    
                    # 檢查日期格式
                    if '/' in ex_date:
                        parts = ex_date.split('/')
                        if len(parts) == 3:
                            roc_year = int(parts[0])
                            ad_year = roc_year + 1911
                            print(f"  -> 民國年: {roc_year}, 轉換後西元年: {ad_year}")
                            print(f"  -> 完整日期: {ad_year}-{int(parts[1]):02d}-{int(parts[2]):02d}")
                    
                    print()
        
    except Exception as e:
        print(f"❌ 櫃買中心API測試失敗: {e}")
    
    # 測試查詢不同年份
    print("\n\n📊 測試查詢2023年資料:")
    params_2023 = {
        'response': 'json',
        'date': '20230701',  # 2023年7月
        'selectType': 'ALL'
    }
    
    try:
        response = requests.get("https://www.twse.com.tw/exchangeReport/TWT49U", 
                              params=params_2023, headers=headers, timeout=30, verify=False)
        data = response.json()
        
        print(f"2023年API標題: {data.get('title', 'N/A')}")
        print(f"2023年資料筆數: {len(data.get('data', []))}")
        
        if 'data' in data and data['data']:
            sample_record = data['data'][0]
            print(f"2023年樣本資料: {sample_record}")
            print(f"2023年除權息日期: {sample_record[0]}")
        
    except Exception as e:
        print(f"❌ 2023年資料測試失敗: {e}")

if __name__ == "__main__":
    debug_date_issue()
