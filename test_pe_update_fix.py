#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 pe.pkl 更新功能
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

def test_crawler_import():
    """測試爬蟲模組導入"""
    print("🔍 測試爬蟲模組導入...")
    
    try:
        # 添加AI_finlab目錄到Python路徑
        ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
        if ai_finlab_path not in sys.path:
            sys.path.insert(0, ai_finlab_path)
        
        from crawler import crawl_pe
        print("✅ crawl_pe 函數導入成功")
        return crawl_pe
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return None
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
        return None

def test_pe_data_read():
    """測試讀取現有的 pe.pkl 檔案"""
    print("\n🔍 測試讀取現有 pe.pkl 檔案...")
    
    pe_file = 'pe.pkl'
    if os.path.exists(pe_file):
        try:
            # 使用 pandas 讀取避免 numpy 警告
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                data = pd.read_pickle(pe_file)
            
            print(f"✅ pe.pkl 讀取成功")
            print(f"   資料筆數: {len(data)}")
            
            if hasattr(data, 'index') and len(data.index) > 0:
                if hasattr(data.index, 'levels') and len(data.index.levels) > 1:
                    # MultiIndex
                    dates = data.index.get_level_values(0)
                    last_date = dates.max().strftime('%Y-%m-%d')
                    first_date = dates.min().strftime('%Y-%m-%d')
                else:
                    # 單層索引
                    last_date = data.index.max().strftime('%Y-%m-%d')
                    first_date = data.index.min().strftime('%Y-%m-%d')
                
                print(f"   日期範圍: {first_date} 至 {last_date}")
                return last_date
            else:
                print("   ⚠️ 無法獲取日期資訊")
                return None
                
        except Exception as e:
            print(f"❌ 讀取失敗: {e}")
            return None
    else:
        print("❌ pe.pkl 檔案不存在")
        return None

def test_pe_crawl():
    """測試 pe 資料爬取"""
    print("\n🔍 測試 pe 資料爬取...")
    
    crawl_pe = test_crawler_import()
    if not crawl_pe:
        return False
    
    try:
        # 測試爬取今天的資料
        today = datetime.now().strftime('%Y-%m-%d')
        print(f"   嘗試爬取 {today} 的 PE 資料...")
        
        result = crawl_pe(today)
        
        if result is not None and not result.empty:
            print(f"✅ PE 資料爬取成功")
            print(f"   獲取資料筆數: {len(result)}")
            print(f"   資料欄位: {list(result.columns)}")
            return True
        else:
            print("⚠️ 爬取成功但無資料（可能是非交易日）")
            return True
            
    except Exception as e:
        print(f"❌ 爬取失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_gui_crawler_funcs():
    """測試GUI中的爬蟲函數字典"""
    print("\n🔍 測試GUI中的爬蟲函數字典...")
    
    try:
        # 模擬GUI的初始化過程
        ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
        if ai_finlab_path not in sys.path:
            sys.path.insert(0, ai_finlab_path)

        from crawler import (
            crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
            crawl_finance_statement_by_date, crawl_twse_divide_ratio,
            crawl_otc_divide_ratio, crawl_twse_cap_reduction,
            crawl_otc_cap_reduction
        )

        crawler_funcs = {
            'crawl_price': crawl_price,
            'crawl_bargin': crawl_bargin,
            'crawl_pe': crawl_pe,
            'crawl_monthly_report': crawl_monthly_report,
            'crawl_balance_sheet': lambda start, end: crawl_finance_statement_by_date(start, end, 'balance_sheet'),
            'crawl_income_sheet': lambda start, end: crawl_finance_statement_by_date(start, end, 'income_sheet'),
            'crawl_cash_flows': lambda start, end: crawl_finance_statement_by_date(start, end, 'cash_flows'),
            'crawl_twse_divide_ratio': crawl_twse_divide_ratio,
            'crawl_otc_divide_ratio': crawl_otc_divide_ratio,
            'crawl_twse_cap_reduction': crawl_twse_cap_reduction,
            'crawl_otc_cap_reduction': crawl_otc_cap_reduction,
        }
        
        print("✅ 爬蟲函數字典創建成功")
        
        # 測試字典中的函數檢查
        test_funcs = ['crawl_pe', 'crawl_price', 'crawl_bargin']
        for func_name in test_funcs:
            if func_name in crawler_funcs:
                print(f"   ✅ {func_name} 存在於字典中")
            else:
                print(f"   ❌ {func_name} 不存在於字典中")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 PE.PKL 更新功能測試")
    print("=" * 60)
    
    # 測試1: 爬蟲模組導入
    success1 = test_crawler_import() is not None
    
    # 測試2: 讀取現有資料
    last_date = test_pe_data_read()
    success2 = last_date is not None
    
    # 測試3: 爬取功能
    success3 = test_pe_crawl()
    
    # 測試4: GUI函數字典
    success4 = test_gui_crawler_funcs()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    print(f"1. 爬蟲模組導入: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"2. 讀取現有資料: {'✅ 成功' if success2 else '❌ 失敗'}")
    print(f"3. 爬取功能測試: {'✅ 成功' if success3 else '❌ 失敗'}")
    print(f"4. GUI函數字典: {'✅ 成功' if success4 else '❌ 失敗'}")
    
    if last_date:
        print(f"\n📅 建議更新範圍:")
        next_date = (datetime.strptime(last_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        today = datetime.now().strftime('%Y-%m-%d')
        print(f"   從 {next_date} 至 {today}")
    
    overall_success = all([success1, success2, success3, success4])
    print(f"\n🎯 整體測試結果: {'✅ 全部成功' if overall_success else '❌ 部分失敗'}")
    
    if overall_success:
        print("\n✨ pe.pkl 更新功能應該可以正常工作！")
        print("   請在GUI中測試pe.pkl的更新功能。")
    else:
        print("\n⚠️ 發現問題，請檢查上述失敗的測試項目。")

if __name__ == "__main__":
    main()
