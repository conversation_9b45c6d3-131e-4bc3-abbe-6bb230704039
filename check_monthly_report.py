#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 monthly_report.pkl 檔案狀況
"""

import pandas as pd
import os
from datetime import datetime

def check_monthly_report():
    """檢查 monthly_report.pkl 檔案"""
    file_path = 'history/tables/monthly_report.pkl'
    
    print('🔍 檢查 monthly_report.pkl 現況')
    print('=' * 50)
    
    if not os.path.exists(file_path):
        print('❌ 檔案不存在')
        return False
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f'📁 檔案資訊:')
        print(f'   路徑: {file_path}')
        print(f'   大小: {file_size/1024/1024:.1f} MB')
        print(f'   修改時間: {datetime.fromtimestamp(file_mtime)}')
        
        # 讀取資料
        print(f'\n📖 讀取資料...')
        data = pd.read_pickle(file_path)
        
        print(f'✅ 讀取成功:')
        print(f'   資料筆數: {len(data):,}')
        print(f'   資料類型: {type(data)}')
        print(f'   索引: {data.index.names}')
        print(f'   欄位數: {len(data.columns)}')
        print(f'   欄位名稱: {list(data.columns)[:5]}...')
        
        # 檢查日期範圍
        if 'date' in data.index.names:
            dates = data.index.get_level_values('date')
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f'\n📅 日期範圍:')
            print(f'   起始: {start_date}')
            print(f'   結束: {end_date}')
            print(f'   唯一日期數: {unique_dates}')
            
            # 檢查最近的日期
            recent_dates = sorted(dates.unique())[-3:]
            print(f'   最近3個日期: {[str(d)[:10] for d in recent_dates]}')
            
            # 檢查更新狀況
            today = datetime.now().date()
            end_date_only = end_date.date() if hasattr(end_date, 'date') else end_date
            
            # 月營收通常是月底發布，所以檢查月份差異
            from dateutil.relativedelta import relativedelta
            months_behind = (today.year - end_date_only.year) * 12 + (today.month - end_date_only.month)
            
            print(f'\n📊 更新狀況:')
            print(f'   今天: {today}')
            print(f'   最新資料: {end_date_only}')
            print(f'   落後月數: {months_behind} 個月')
            
            if months_behind <= 1:
                print(f'   ✅ 資料很新')
            elif months_behind <= 3:
                print(f'   ⚠️ 資料稍舊')
            else:
                print(f'   ❌ 資料較舊，需要更新')
        
        # 顯示樣本資料
        print(f'\n📊 樣本資料:')
        print(data.head(3))
        
        return True
        
    except Exception as e:
        print(f'❌ 讀取失敗: {str(e)}')
        import traceback
        print(f'詳細錯誤: {traceback.format_exc()}')
        return False

if __name__ == "__main__":
    check_monthly_report()
