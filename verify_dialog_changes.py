#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證導出對話框修改是否正確
"""

import os
import re

def check_dialog_modifications():
    """檢查主程序中的對話框修改"""
    print("🔍 檢查策略交集導出對話框修改...")
    
    main_file = "O3mh_gui_v21_optimized.py"
    
    if not os.path.exists(main_file):
        print(f"❌ 找不到主程序文件：{main_file}")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查修改點1：是否調用了增強版對話框
    check1 = "show_enhanced_intersection_export_dialog" in content
    print(f"✅ 增強版對話框調用：{'已添加' if check1 else '❌ 未找到'}")
    
    # 檢查修改點2：是否有增強版對話框方法
    check2 = "def show_enhanced_intersection_export_dialog" in content
    print(f"✅ 增強版對話框方法：{'已添加' if check2 else '❌ 未找到'}")
    
    # 檢查修改點3：是否有簡化版對話框方法
    check3 = "def show_simple_intersection_export_dialog" in content
    print(f"✅ 簡化版對話框方法：{'已添加' if check3 else '❌ 未找到'}")
    
    # 檢查修改點4：是否調用了簡化版對話框
    check4 = "show_simple_intersection_export_dialog" in content
    print(f"✅ 簡化版對話框調用：{'已添加' if check4 else '❌ 未找到'}")
    
    # 檢查按鈕文字
    check5 = "📂 開啟位置" in content
    print(f"✅ 開啟位置按鈕：{'已添加' if check5 else '❌ 未找到'}")
    
    check6 = "📄 查看報告" in content
    print(f"✅ 查看報告按鈕：{'已添加' if check6 else '❌ 未找到'}")
    
    check7 = "📋 稍後開啟" in content
    print(f"✅ 稍後開啟按鈕：{'已添加' if check7 else '❌ 未找到'}")
    
    # 檢查開啟檔案的邏輯
    check8 = "os.startfile" in content
    print(f"✅ Windows開啟檔案：{'已添加' if check8 else '❌ 未找到'}")
    
    check9 = "subprocess.run" in content
    print(f"✅ 跨平台開啟檔案：{'已添加' if check9 else '❌ 未找到'}")
    
    # 統計修改情況
    total_checks = 9
    passed_checks = sum([check1, check2, check3, check4, check5, check6, check7, check8, check9])
    
    print(f"\n📊 修改檢查結果：{passed_checks}/{total_checks} 項通過")
    
    if passed_checks == total_checks:
        print("🎉 所有修改都已正確應用！")
        return True
    else:
        print("⚠️ 部分修改可能有問題，請檢查代碼")
        return False

def check_specific_lines():
    """檢查特定行的修改"""
    print("\n🔍 檢查特定行的修改...")
    
    main_file = "O3mh_gui_v21_optimized.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 檢查第9483行附近（原本的QMessageBox.information調用）
    found_enhanced_call = False
    for i, line in enumerate(lines[9480:9490], 9481):
        if "show_enhanced_intersection_export_dialog" in line:
            print(f"✅ 第{i}行：找到增強版對話框調用")
            found_enhanced_call = True
            break
    
    if not found_enhanced_call:
        print("❌ 未找到增強版對話框調用")
    
    # 檢查第9487行附近（原本的部分導出情況）
    found_simple_call = False
    for i, line in enumerate(lines[9485:9495], 9486):
        if "show_simple_intersection_export_dialog" in line:
            print(f"✅ 第{i}行：找到簡化版對話框調用")
            found_simple_call = True
            break
    
    if not found_simple_call:
        print("❌ 未找到簡化版對話框調用")
    
    # 檢查方法定義
    method_lines = []
    for i, line in enumerate(lines, 1):
        if "def show_enhanced_intersection_export_dialog" in line:
            method_lines.append(f"增強版對話框方法定義：第{i}行")
        elif "def show_simple_intersection_export_dialog" in line:
            method_lines.append(f"簡化版對話框方法定義：第{i}行")
    
    for method_line in method_lines:
        print(f"✅ {method_line}")
    
    return found_enhanced_call and found_simple_call and len(method_lines) == 2

def main():
    """主函數"""
    print("🧪 策略交集導出對話框修改驗證工具")
    print("=" * 50)
    
    # 檢查修改
    check1 = check_dialog_modifications()
    check2 = check_specific_lines()
    
    print("\n" + "=" * 50)
    if check1 and check2:
        print("🎉 所有修改驗證通過！")
        print("💡 新的導出對話框功能已正確實現：")
        print("   • 詢問用戶是否要開啟存檔位置")
        print("   • 提供多種開啟選項（位置、報告、稍後）")
        print("   • 處理開啟失敗的情況")
        print("   • 支援跨平台檔案開啟")
    else:
        print("⚠️ 部分修改可能有問題，請檢查代碼")
    
    print("\n🚀 可以測試新功能了！")

if __name__ == "__main__":
    main()
