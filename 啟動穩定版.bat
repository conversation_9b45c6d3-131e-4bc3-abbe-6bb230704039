@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         穩定版 - 獨立可執行檔
echo ========================================
echo.

if exist "dist\台股智能選股系統_穩定版.exe" (
    echo ✅ 找到穩定版可執行檔
    echo 🚀 正在啟動...
    echo.
    echo 💡 穩定版特點：
    echo    ✓ 完全獨立，無需 Python 環境
    echo    ✓ 包含所有必要依賴
    echo    ✓ 解決所有模組問題
    echo    ✓ 優化啟動速度
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_穩定版.exe"
    
    echo ✅ 穩定版已啟動！
    echo.
    echo 📋 如果程式無法正常運行：
    echo    1. 檢查防毒軟體設定
    echo    2. 確認執行權限
    echo    3. 檢查系統兼容性
    echo.
    
) else (
    echo ❌ 錯誤：找不到穩定版可執行檔
    echo.
    echo 請重新編譯：
    echo    python stable_compile.py
    echo.
    pause
    exit /b 1
)

timeout /t 5 >nul
