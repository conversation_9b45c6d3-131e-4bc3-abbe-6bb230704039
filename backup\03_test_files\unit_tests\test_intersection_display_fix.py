#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試策略交集顯示修復
驗證股票名稱和詳細交集結果的顯示
"""

import sys
import pandas as pd
from strategy_intersection_analyzer import StrategyIntersectionAnalyzer

class MockIntersectionDisplay:
    """模擬策略交集顯示修復"""
    
    def __init__(self):
        self.intersection_analyzer = StrategyIntersectionAnalyzer()
        self.setup_mock_data()
    
    def setup_mock_data(self):
        """設置模擬數據"""
        # 模擬策略結果
        strategy_results = {
            "藏寶": pd.DataFrame({
                "股票代碼": ["6290", "6196", "6667", "4414", "2414"],
                "股票名稱": ["良維", "帆宣", "信紘科", "如興", "精技"],
                "評分": [85, 78, 82, 75, 80]
            }),
            "二次創高股票": pd.DataFrame({
                "股票代碼": ["8499", "0885", "2801", "2884", "6667"],
                "股票名稱": ["鼎炫-KY", "富邦越南", "彰銀", "玉山金", "信紘科"],
                "評分": [90, 85, 88, 82, 79]
            }),
            "CANSLIM量價齊升": pd.DataFrame({
                "股票代碼": ["8499", "0885", "2801", "2884", "6667"],
                "股票名稱": ["鼎炫-KY", "富邦越南", "彰銀", "玉山金", "信紘科"],
                "評分": [92, 87, 85, 90, 83]
            })
        }
        
        # 添加策略結果到分析器
        for strategy, df in strategy_results.items():
            self.intersection_analyzer.add_strategy_result(strategy, df, "股票代碼")
    
    def get_stock_names_for_codes(self, stock_codes):
        """根據股票代碼獲取股票名稱（修復後的版本）"""
        # 台灣股票完整名稱對照表（包含測試用的股票）
        taiwan_stocks = {
            # 權值股 & 科技股
            "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2308": "台達電", "2382": "廣達",
            "2303": "聯電", "2357": "華碩", "2409": "友達", "3008": "大立光", "3711": "日月光投控",
            "2379": "瑞昱", "3034": "聯詠", "3481": "群創", "4938": "和碩", "2327": "國巨",
            
            # 金融股
            "2881": "富邦金", "2882": "國泰金", "2883": "開發金", "2884": "玉山金", "2885": "元大金",
            "2886": "兆豐金", "2887": "台新金", "2888": "新光金", "2889": "國票金", "2890": "永豐金",
            "2891": "中信金", "2892": "第一金", "2880": "華南金", "5880": "合庫金", "2823": "中壽",
            "2801": "彰銀",
            
            # 傳統產業
            "1101": "台泥", "1102": "亞泥", "1103": "嘉泥", "1216": "統一", "1301": "台塑",
            "1303": "南亞", "1326": "台化", "1402": "遠東新", "2002": "中鋼", "2105": "正新",
            
            # 測試用股票（從圖片中看到的）
            "6290": "良維", "6196": "帆宣", "6667": "信紘科", "4414": "如興", "2414": "精技",
            "8499": "鼎炫-KY", "0885": "富邦越南",
            
            # ETF
            "0050": "元大台灣50", "0051": "元大中型100", "0052": "富邦科技", "0053": "元大電子",
            "0054": "元大台商50", "0055": "元大MSCI金融", "0056": "元大高股息", "0057": "富邦摩台",
        }
        
        stock_names = {}
        
        try:
            # 使用台股對照表獲取股票名稱
            for code in stock_codes:
                if code in taiwan_stocks:
                    stock_names[code] = taiwan_stocks[code]
                else:
                    stock_names[code] = f"股票{code}"
                    
        except Exception as e:
            print(f"❌ 獲取股票名稱失敗: {e}")
            # 出錯時使用代碼本身
            for code in stock_codes:
                stock_names[code] = f"股票{code}"
        
        return stock_names
    
    def test_intersection_display(self):
        """測試交集顯示修復"""
        print("🔍 測試策略交集顯示修復")
        print("="*50)
        
        # 分析所有組合
        all_results = self.intersection_analyzer.analyze_all_combinations()
        
        # 獲取最佳組合
        top_combinations = self.intersection_analyzer.get_top_intersection_combinations()
        
        # 生成報告（修復後的版本）
        report_lines = ["🔍 所有策略組合分析結果", "=" * 50, ""]
        report_lines.append("📊 按交集數量排序:")
        
        if top_combinations:
            for i, (combo_name, count) in enumerate(top_combinations, 1):
                report_lines.append(f"  {i:2d}. {combo_name}: {count} 支共同股票")

                # 顯示所有組合的詳細股票（包含名稱）
                if combo_name in all_results:
                    stocks = all_results[combo_name]['intersection_stocks']
                    if stocks:
                        # 獲取股票名稱
                        stock_names = self.get_stock_names_for_codes(list(stocks))
                        stock_info = [f"{code}({stock_names[code]})" for code in sorted(stocks)]
                        report_lines.append(f"      股票: {', '.join(stock_info)}")
                    else:
                        report_lines.append(f"      股票: 無共同股票")
                    report_lines.append("")
        else:
            report_lines.append("  ⚠️ 沒有找到有交集的策略組合")
        
        # 顯示結果
        print("\n".join(report_lines))
        
        return len(top_combinations) > 0

def test_stock_name_mapping():
    """測試股票名稱映射"""
    print("\n🏷️  測試股票名稱映射")
    print("="*30)
    
    mock = MockIntersectionDisplay()
    
    # 測試股票代碼
    test_codes = ["6290", "6196", "6667", "4414", "2414", "8499", "0885", "2801", "2884", "9999"]
    
    stock_names = mock.get_stock_names_for_codes(test_codes)
    
    print("股票代碼 → 股票名稱:")
    for code in test_codes:
        name = stock_names.get(code, "未知")
        status = "✅" if not name.startswith("股票") else "⚠️"
        print(f"  {status} {code} → {name}")
    
    # 檢查是否有正確的名稱映射
    correct_mappings = 0
    for code, name in stock_names.items():
        if not name.startswith("股票"):
            correct_mappings += 1
    
    print(f"\n📊 映射結果: {correct_mappings}/{len(test_codes)} 個股票有正確名稱")
    
    return correct_mappings >= len(test_codes) - 1  # 允許1個未知股票

def main():
    """主函數"""
    try:
        mock = MockIntersectionDisplay()
        
        # 測試交集顯示
        display_ok = mock.test_intersection_display()
        
        # 測試股票名稱映射
        mapping_ok = test_stock_name_mapping()
        
        if display_ok and mapping_ok:
            print(f"\n🎉 策略交集顯示修復驗證通過！")
            print(f"   ✅ 交集結果正確顯示")
            print(f"   ✅ 股票名稱正確映射")
        else:
            print(f"\n❌ 策略交集顯示修復驗證失敗！")
            if not display_ok:
                print(f"   ❌ 交集結果顯示有問題")
            if not mapping_ok:
                print(f"   ❌ 股票名稱映射有問題")
        
        return 0 if (display_ok and mapping_ok) else 1
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
