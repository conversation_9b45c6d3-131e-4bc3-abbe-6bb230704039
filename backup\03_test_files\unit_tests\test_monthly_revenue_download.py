#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試月營收下載器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader

def test_single_stock_download():
    """測試單一股票下載"""
    print("🚀 開始測試月營收下載器...")
    
    # 創建下載器實例
    downloader = GoodinfoMonthlyRevenueDownloader()
    
    # 測試股票代碼
    test_stock = "2330"
    
    print(f"📊 測試下載 {test_stock} 的月營收資料...")
    
    try:
        # 下載單一股票資料
        result = downloader.download_stock_revenue(test_stock)
        
        if result:
            print(f"✅ 下載成功！共處理 {result} 筆資料")
            return True
        else:
            print("❌ 下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ 下載過程中發生錯誤: {e}")
        return False

def test_date_range_download():
    """測試指定日期範圍下載"""
    print("\n🗓️ 測試指定日期範圍下載...")
    
    # 創建下載器實例
    downloader = GoodinfoMonthlyRevenueDownloader()
    
    # 測試股票代碼和日期範圍
    test_stock = "2317"
    start_date = "2023-01"
    end_date = "2024-12"
    
    print(f"📊 測試下載 {test_stock} 的月營收資料 ({start_date} 到 {end_date})...")
    
    try:
        # 下載指定日期範圍的資料
        result = downloader.download_stock_revenue(test_stock, start_date, end_date)
        
        if result:
            print(f"✅ 日期範圍下載成功！共處理 {result} 筆資料")
            return True
        else:
            print("❌ 日期範圍下載失敗")
            return False
            
    except Exception as e:
        print(f"❌ 日期範圍下載過程中發生錯誤: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("📈 月營收下載器測試程式")
    print("=" * 60)
    
    # 測試1: 單一股票下載
    success1 = test_single_stock_download()
    
    # 測試2: 日期範圍下載
    success2 = test_date_range_download()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    print(f"• 單一股票下載: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"• 日期範圍下載: {'✅ 成功' if success2 else '❌ 失敗'}")
    
    if success1 or success2:
        print("\n🎉 至少有一個測試成功！月營收下載器基本功能正常")
    else:
        print("\n💥 所有測試都失敗了，需要進一步檢查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
