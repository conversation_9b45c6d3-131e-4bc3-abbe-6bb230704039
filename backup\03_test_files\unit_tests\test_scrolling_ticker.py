#!/usr/bin/env python3
"""
測試真正的跑馬燈滾動效果
"""

import sys
import logging
from datetime import datetime, time
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QTextEdit, QStatusBar, QProgressBar
)
from PyQt6.QtCore import QTimer, Qt

# 設置日誌
logging.basicConfig(level=logging.INFO)

class ScrollingTickerTestWindow(QMainWindow):
    """測試跑馬燈滾動效果的主視窗"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("📊 跑馬燈滾動效果測試")
        self.setGeometry(100, 100, 1200, 600)
        
        self.init_ui()
        self.init_trading_ticker()
        
    def init_ui(self):
        """初始化界面"""
        # 中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📊 跑馬燈滾動效果測試")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 當前時間顯示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #28a745; margin: 10px;")
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.time_label)
        
        # 說明文字
        desc = QTextEdit()
        desc.setReadOnly(True)
        desc.setMaximumHeight(250)
        desc.setHtml("""
        <h3 style="color: #2E86AB;">📊 真正的跑馬燈滾動效果</h3>
        <p><strong>功能說明</strong>：跑馬燈文字會在狀態欄中持續從右到左滾動移動。</p>
        
        <h4 style="color: #28a745;">✅ 修正後的功能特性：</h4>
        <ul>
            <li><strong>🔄 持續滾動</strong>：文字在狀態欄中持續循環滾動</li>
            <li><strong>⏱️ 滾動速度</strong>：每1600毫秒移動一個字符位置（緩慢舒適的移動感覺）</li>
            <li><strong>🔁 循環顯示</strong>：文字滾動到末尾後從頭開始</li>
            <li><strong>📊 時段更新</strong>：每30秒檢查時段變化並更新內容</li>
            <li><strong>🎯 手動控制</strong>：可隨時啟用/停用滾動效果</li>
        </ul>
        
        <h4 style="color: #dc3545;">🔧 技術實現：</h4>
        <ul>
            <li><strong>雙定時器設計</strong>：時段檢查定時器 + 滾動顯示定時器</li>
            <li><strong>字符位置控制</strong>：精確控制顯示文字的起始位置</li>
            <li><strong>循環邏輯</strong>：文字末尾自動銜接開頭形成循環</li>
            <li><strong>顯示區域限制</strong>：固定寬度的顯示窗口</li>
        </ul>
        
        <p style="color: #666;"><strong>觀察重點</strong>：請注意視窗底部狀態欄中文字的持續滾動效果。</p>
        """)
        layout.addWidget(desc)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        
        toggle_btn = QPushButton("🎯 切換跑馬燈")
        toggle_btn.clicked.connect(self.toggle_trading_ticker)
        toggle_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; }")
        
        speed_up_btn = QPushButton("⚡ 加速滾動")
        speed_up_btn.clicked.connect(self.speed_up_ticker)
        speed_up_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #fd7e14; color: white; border: none; border-radius: 4px; }")
        
        slow_down_btn = QPushButton("🐌 減速滾動")
        slow_down_btn.clicked.connect(self.slow_down_ticker)
        slow_down_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #6f42c1; color: white; border: none; border-radius: 4px; }")
        
        test_msg_btn = QPushButton("🧪 測試訊息")
        test_msg_btn.clicked.connect(self.test_different_messages)
        test_msg_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-size: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; }")
        
        button_layout.addWidget(toggle_btn)
        button_layout.addWidget(speed_up_btn)
        button_layout.addWidget(slow_down_btn)
        button_layout.addWidget(test_msg_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 初始化狀態欄
        self.init_status_bar()
        
        # 時間更新定時器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # 每秒更新時間
        
    def init_status_bar(self):
        """初始化狀態欄"""
        self.status_bar = self.statusBar()
        
        # 跑馬燈顯示區域
        self.ticker_label = QLabel("")
        self.ticker_label.setStyleSheet("color: #2E86AB; font-weight: bold; padding: 2px 5px; font-family: 'Courier New', monospace;")
        self.ticker_label.setMinimumWidth(500)
        self.status_bar.addWidget(self.ticker_label)
        
        # 跑馬燈控制按鈕
        self.ticker_btn = QPushButton("📊 跑馬燈: 開")
        self.ticker_btn.clicked.connect(self.toggle_trading_ticker)
        self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #28a745; color: white; border: none; border-radius: 3px; }")
        self.ticker_btn.setToolTip("點擊切換看盤重點跑馬燈")
        self.status_bar.addPermanentWidget(self.ticker_btn)
        
        # 速度顯示
        self.speed_label = QLabel("1600ms")
        self.speed_label.setStyleSheet("color: #666; font-size: 10px; padding: 2px;")
        self.status_bar.addPermanentWidget(self.speed_label)
        
    def init_trading_ticker(self):
        """初始化看盤重點跑馬燈"""
        self.trading_ticker_enabled = True  # 預設啟用
        self.scroll_speed = 1600  # 滾動速度（毫秒）- 緩慢舒適速度
        
        # 跑馬燈滾動相關變數
        self.ticker_text = ""
        self.ticker_position = 0
        self.ticker_display_width = 60  # 顯示區域字符寬度
        
        # 時段檢查定時器（每30秒檢查一次時段變化）
        self.trading_ticker_timer = QTimer()
        self.trading_ticker_timer.timeout.connect(self.check_trading_time)
        
        # 跑馬燈滾動定時器
        self.ticker_scroll_timer = QTimer()
        self.ticker_scroll_timer.timeout.connect(self.update_ticker_display)
        
        # 看盤重點訊息 - 整合實戰技巧
        self.trading_messages = {
            'pre_market': "🌅 盤前集合競價 (8:30-9:00)：🎯留意成交金額>1000萬個股、📰消息驅動股異動、👑族群龍頭搶籌情況",
            'early_session': "🌄 早盤時段 (9:00-10:30)：🚀跳空續航股、💪主力標的量價齊揚、📈技術面強勢股KD鈍化高檔",
            'mid_session': "☀️ 中盤時段 (10:30-12:00)：🔄族群輪動補漲股、📊支撐反彈股逢低承接、💰ETF套利折價機會",
            'afternoon': "🌇 午盤時段 (12:00-13:25)：⚡反彈先鋒長下影線、🛡️防禦性高股息ETF、📉權值股破線避開",
            'closing': "🌆 收盤前試撮 (13:25-13:30)：💎大宗交易熱股、🔄ETF尾盤換股、⚠️暫緩撮合股次日留意",
            'after_hours': "📊 盤後時段：檢視當日表現、準備明日策略、關注國際市場動向、零股交易13:40-14:30"
        }
        
        # 自動啟動跑馬燈
        self.trading_ticker_timer.start(30000)  # 每30秒檢查一次時段
        self.ticker_scroll_timer.start(self.scroll_speed)  # 開始滾動
        
        # 立即檢查一次當前時段
        self.check_trading_time()
    
    def update_time_display(self):
        """更新時間顯示"""
        now = datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 判斷當前時段
        current_time = now.time()
        period = self.get_current_period(current_time)
        
        status = "開" if getattr(self, 'trading_ticker_enabled', False) else "關"
        speed_sec = self.scroll_speed / 1000
        self.time_label.setText(f"⏰ 當前時間：{time_str} | 📊 當前時段：{period} | 🎯 跑馬燈：{status} | ⚡ 速度：{speed_sec}秒/字符")
    
    def get_current_period(self, current_time):
        """獲取當前時段名稱"""
        if time(8, 30) <= current_time < time(9, 0):
            return "盤前集合競價"
        elif time(9, 0) <= current_time < time(10, 30):
            return "早盤時段"
        elif time(10, 30) <= current_time < time(12, 0):
            return "中盤時段"
        elif time(12, 0) <= current_time < time(13, 30):
            return "午盤時段"
        elif time(13, 25) <= current_time < time(13, 30):
            return "收盤前試撮"
        else:
            return "盤後時段"
    
    def check_trading_time(self):
        """檢查交易時間並設置跑馬燈文字"""
        if not getattr(self, 'trading_ticker_enabled', False):
            self.ticker_text = ""
            return
            
        now = datetime.now()
        current_time = now.time()
        
        # 判斷當前時段並設置跑馬燈文字
        if time(8, 30) <= current_time < time(9, 0):
            self.ticker_text = self.trading_messages['pre_market']
        elif time(9, 0) <= current_time < time(10, 30):
            self.ticker_text = self.trading_messages['early_session']
        elif time(10, 30) <= current_time < time(12, 0):
            self.ticker_text = self.trading_messages['mid_session']
        elif time(12, 0) <= current_time < time(13, 30):
            self.ticker_text = self.trading_messages['afternoon']
        elif time(13, 25) <= current_time < time(13, 30):
            self.ticker_text = self.trading_messages['closing']
        else:
            self.ticker_text = self.trading_messages['after_hours']
        
        # 重置滾動位置
        self.ticker_position = 0
        logging.info(f"時段更新: {self.get_current_period(current_time)}")
    
    def update_ticker_display(self):
        """更新跑馬燈顯示（滾動效果）"""
        if not getattr(self, 'trading_ticker_enabled', False):
            return
            
        if not self.ticker_text:
            self.ticker_label.setText("")
            return
        
        # 添加空格讓文字循環滾動
        full_text = self.ticker_text + "    " * 15  # 添加更多空格分隔
        
        # 計算顯示的文字片段
        if self.ticker_position >= len(full_text):
            self.ticker_position = 0
        
        # 取得要顯示的文字片段
        display_text = full_text[self.ticker_position:self.ticker_position + self.ticker_display_width]
        
        # 如果不夠長，從頭補充
        if len(display_text) < self.ticker_display_width:
            display_text += full_text[:self.ticker_display_width - len(display_text)]
        
        # 更新顯示
        self.ticker_label.setText(display_text)
        
        # 移動位置
        self.ticker_position += 1
    
    def toggle_trading_ticker(self):
        """切換跑馬燈開關"""
        if getattr(self, 'trading_ticker_enabled', False):
            # 停用跑馬燈
            self.trading_ticker_enabled = False
            self.trading_ticker_timer.stop()
            self.ticker_scroll_timer.stop()
            self.ticker_btn.setText("📊 跑馬燈: 關")
            self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #dc3545; color: white; border: none; border-radius: 3px; }")
            self.ticker_label.setText("📊 看盤重點跑馬燈已停用")
        else:
            # 啟用跑馬燈
            self.trading_ticker_enabled = True
            self.trading_ticker_timer.start(30000)  # 每30秒檢查一次時段
            self.ticker_scroll_timer.start(self.scroll_speed)  # 開始滾動
            self.ticker_btn.setText("📊 跑馬燈: 開")
            self.ticker_btn.setStyleSheet("QPushButton { padding: 4px 8px; font-size: 10px; background-color: #28a745; color: white; border: none; border-radius: 3px; }")
            # 立即檢查一次時段並開始滾動
            self.check_trading_time()
    
    def speed_up_ticker(self):
        """加速跑馬燈滾動"""
        if self.scroll_speed > 50:
            self.scroll_speed -= 50
            self.ticker_scroll_timer.stop()
            self.ticker_scroll_timer.start(self.scroll_speed)
            self.speed_label.setText(f"{self.scroll_speed}ms")
            logging.info(f"跑馬燈加速至: {self.scroll_speed}ms")
    
    def slow_down_ticker(self):
        """減速跑馬燈滾動"""
        if self.scroll_speed < 1000:
            self.scroll_speed += 50
            self.ticker_scroll_timer.stop()
            self.ticker_scroll_timer.start(self.scroll_speed)
            self.speed_label.setText(f"{self.scroll_speed}ms")
            logging.info(f"跑馬燈減速至: {self.scroll_speed}ms")
    
    def test_different_messages(self):
        """測試不同長度的訊息"""
        test_messages = [
            "📊 短訊息測試",
            "🌅 這是一個中等長度的測試訊息，用來驗證跑馬燈的滾動效果",
            "🌄 這是一個非常長的測試訊息，包含了大量的文字內容，用來測試跑馬燈在處理長文字時的滾動效果和循環顯示功能，確保文字能夠完整地從右到左滾動並且在末尾時能夠順利地回到開頭繼續滾動"
        ]
        
        import random
        self.ticker_text = random.choice(test_messages)
        self.ticker_position = 0
        logging.info(f"測試訊息: {self.ticker_text[:50]}...")

def main():
    """主函數"""
    print("📊 測試跑馬燈滾動效果")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建並顯示主視窗
    window = ScrollingTickerTestWindow()
    window.show()
    
    print("✅ 跑馬燈滾動效果測試視窗已開啟")
    print("🔍 測試重點:")
    print("  1. ✅ 文字持續滾動效果")
    print("  2. ✅ 循環顯示功能")
    print("  3. ✅ 滾動速度控制")
    print("  4. ✅ 時段自動更新")
    print("  5. ✅ 開關控制功能")
    print("\n💡 觀察要點:")
    print("  📊 狀態欄中文字的持續滾動")
    print("  🔄 文字滾動到末尾後的循環")
    print("  ⚡ 滾動速度的變化效果")
    print("  🎯 開關控制的即時反應")
    print("\n🎯 測試功能:")
    print("  1. 🎯 切換跑馬燈 - 開關滾動效果")
    print("  2. ⚡ 加速滾動 - 提高滾動速度")
    print("  3. 🐌 減速滾動 - 降低滾動速度")
    print("  4. 🧪 測試訊息 - 隨機切換不同長度訊息")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
