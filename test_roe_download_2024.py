#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試ROE資料下載器 - 2024年資料
修復2025年資料不存在的問題
"""

import sys
import os
import logging
from datetime import datetime

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('roe_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_roe_download_2024():
    """測試下載2024年ROE資料"""
    try:
        logger.info("🚀 開始測試ROE資料下載器...")
        
        # 測試年份
        test_year = 2024
        logger.info(f"📅 測試年份: {test_year}")
        
        # 方法1: 測試roe_data_crawler
        logger.info("=" * 50)
        logger.info("🔍 測試方法1: roe_data_crawler")
        try:
            from roe_data_crawler import ROEDataCrawler
            
            crawler = ROEDataCrawler()
            records = crawler.fetch_roe_data(test_year)
            
            if records:
                logger.info(f"✅ 方法1成功: 獲取 {len(records)} 筆資料")
                
                # 儲存到資料庫
                success = crawler.save_to_database(records, overwrite=True)
                if success:
                    logger.info("✅ 資料庫儲存成功")
                    
                    # 匯出CSV
                    csv_file = crawler.export_to_csv(test_year)
                    if csv_file:
                        logger.info(f"✅ CSV匯出成功: {csv_file}")
                        return True
                    else:
                        logger.warning("⚠️ CSV匯出失敗")
                else:
                    logger.warning("⚠️ 資料庫儲存失敗")
            else:
                logger.warning("⚠️ 方法1: 未獲取到資料")
                
        except Exception as e:
            logger.error(f"❌ 方法1失敗: {e}")
        
        # 方法2: 測試improved_roe_downloader
        logger.info("=" * 50)
        logger.info("🔍 測試方法2: improved_roe_downloader")
        try:
            from improved_roe_downloader import ImprovedROEDownloader
            
            downloader = ImprovedROEDownloader()
            csv_file = downloader.download_roe_data(test_year)
            
            if csv_file:
                logger.info(f"✅ 方法2成功: {csv_file}")
                return True
            else:
                logger.warning("⚠️ 方法2: 未獲取到資料")
                
        except Exception as e:
            logger.error(f"❌ 方法2失敗: {e}")
        
        # 方法3: 測試goodinfo_roe_csv_downloader
        logger.info("=" * 50)
        logger.info("🔍 測試方法3: goodinfo_roe_csv_downloader")
        try:
            from goodinfo_roe_csv_downloader import GoodInfoROEDownloader
            
            downloader = GoodInfoROEDownloader()
            csv_file = downloader.download_roe_data(test_year)
            
            if csv_file:
                logger.info(f"✅ 方法3成功: {csv_file}")
                return True
            else:
                logger.warning("⚠️ 方法3: 未獲取到資料")
                
        except Exception as e:
            logger.error(f"❌ 方法3失敗: {e}")
        
        logger.error("❌ 所有方法都失敗了")
        return False
        
    except Exception as e:
        logger.error(f"❌ 測試過程發生錯誤: {e}")
        return False

def check_database_content():
    """檢查資料庫內容"""
    try:
        import sqlite3
        import pandas as pd
        
        db_path = "D:/Finlab/history/tables/roe_data.db"
        if not os.path.exists(db_path):
            logger.warning(f"⚠️ 資料庫檔案不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        
        # 查詢資料庫統計
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM roe_data")
        total_count = cursor.fetchone()[0]
        logger.info(f"📊 資料庫總筆數: {total_count}")
        
        # 查詢各年份資料數量
        cursor.execute("SELECT year, COUNT(*) FROM roe_data GROUP BY year ORDER BY year DESC")
        year_stats = cursor.fetchall()
        
        logger.info("📅 各年份資料統計:")
        for year, count in year_stats:
            logger.info(f"   {year}年: {count} 筆")
        
        # 查詢最新資料
        cursor.execute("""
            SELECT stock_code, stock_name, year, roe_value, rank_position
            FROM roe_data 
            WHERE year = (SELECT MAX(year) FROM roe_data)
            ORDER BY rank_position 
            LIMIT 10
        """)
        
        latest_data = cursor.fetchall()
        if latest_data:
            logger.info("🏆 最新年度前10名ROE:")
            for i, (code, name, year, roe, rank) in enumerate(latest_data, 1):
                logger.info(f"   {i:2d}. {code} {name} - ROE: {roe}% (排名: {rank})")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ 檢查資料庫失敗: {e}")

if __name__ == "__main__":
    logger.info("🎯 ROE資料下載器測試開始")
    logger.info(f"📅 當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 執行測試
    success = test_roe_download_2024()
    
    if success:
        logger.info("🎉 測試成功！")
        # 檢查資料庫內容
        check_database_content()
    else:
        logger.error("💥 測試失敗！")
        logger.info("💡 建議:")
        logger.info("   1. 檢查網路連線")
        logger.info("   2. 確認GoodInfo網站是否正常")
        logger.info("   3. 嘗試手動訪問 https://goodinfo.tw")
        logger.info("   4. 檢查是否需要更新Selenium WebDriver")
    
    logger.info("🏁 測試結束")
