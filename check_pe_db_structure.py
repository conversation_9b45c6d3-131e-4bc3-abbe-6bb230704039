#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 PE 資料庫結構並診斷問題
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime

def check_pe_db_structure():
    """檢查 PE 資料庫結構"""
    print("=" * 80)
    print("🔍 檢查 PE 資料庫結構")
    print("=" * 80)
    
    pe_db_path = 'D:/Finlab/history/tables/pe.db'
    
    if not os.path.exists(pe_db_path):
        print("❌ pe.db 不存在")
        return
    
    try:
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(pe_data)")
        columns = cursor.fetchall()
        
        print("📊 pe_data 表格結構:")
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
        
        # 檢查是否有 index 欄位
        column_names = [col[1] for col in columns]
        if 'index' in column_names:
            print("⚠️ 發現 'index' 欄位")
        else:
            print("✅ 沒有 'index' 欄位")
        
        # 檢查資料範例
        cursor.execute("SELECT * FROM pe_data LIMIT 5")
        sample_data = cursor.fetchall()
        
        print("\n📋 資料範例:")
        for row in sample_data:
            print(f"   {row}")
        
        # 檢查總記錄數
        cursor.execute("SELECT COUNT(*) FROM pe_data")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 總記錄數: {total_count:,}")
        
        # 檢查最新日期
        cursor.execute("SELECT MAX(date) FROM pe_data")
        last_date = cursor.fetchone()[0]
        print(f"📊 最新日期: {last_date}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查資料庫時發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def test_pe_data_structure():
    """測試 PE 資料結構"""
    print("\n" + "=" * 80)
    print("🧪 測試 PE 資料結構")
    print("=" * 80)
    
    try:
        import sys
        import os
        
        # 添加路徑
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, os.path.join(current_dir, 'finlab'))
        
        from crawler import crawl_pe
        
        # 測試爬取一天的資料
        test_date = datetime(2024, 12, 30)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        df = crawl_pe(test_date)
        
        if not df.empty:
            print(f"✅ 成功爬取 {len(df)} 筆 PE 資料")
            
            # 檢查 DataFrame 結構
            print(f"\n📊 DataFrame 資訊:")
            print(f"   形狀: {df.shape}")
            print(f"   索引: {df.index.names}")
            print(f"   欄位: {list(df.columns)}")
            
            # 重置索引檢查
            df_reset = df.reset_index()
            print(f"\n📊 重置索引後:")
            print(f"   形狀: {df_reset.shape}")
            print(f"   欄位: {list(df_reset.columns)}")
            
            # 檢查是否有 index 欄位
            if 'index' in df_reset.columns:
                print("⚠️ 發現 'index' 欄位")
                print(f"   index 欄位內容: {df_reset['index'].head()}")
            else:
                print("✅ 沒有 'index' 欄位")
            
            # 顯示前幾筆資料
            print(f"\n📋 前5筆資料:")
            print(df_reset.head())
            
        else:
            print("⚠️ 爬取資料為空")
            
    except Exception as e:
        print(f"❌ 測試 PE 資料結構時發生錯誤: {e}")
        import traceback
        traceback.print_exc()

def fix_pe_database():
    """修復 PE 資料庫結構"""
    print("\n" + "=" * 80)
    print("🔧 修復 PE 資料庫結構")
    print("=" * 80)
    
    pe_db_path = 'D:/Finlab/history/tables/pe.db'
    
    if not os.path.exists(pe_db_path):
        print("❌ pe.db 不存在")
        return
    
    try:
        conn = sqlite3.connect(pe_db_path)
        cursor = conn.cursor()
        
        # 檢查是否有 index 欄位
        cursor.execute("PRAGMA table_info(pe_data)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'index' in column_names:
            print("⚠️ 發現 'index' 欄位，需要修復")
            
            # 備份原始表格
            backup_table = f"pe_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute(f"CREATE TABLE {backup_table} AS SELECT * FROM pe_data")
            print(f"📦 已備份原始表格為: {backup_table}")
            
            # 創建新的正確結構表格
            cursor.execute("DROP TABLE pe_data")
            cursor.execute('''
                CREATE TABLE pe_data (
                    stock_id TEXT,
                    stock_name TEXT,
                    date TEXT,
                    "殖利率(%)" REAL,
                    "本益比" REAL,
                    "股利年度" TEXT,
                    "股價淨值比" REAL,
                    PRIMARY KEY (stock_id, date)
                )
            ''')
            
            # 從備份表格複製資料（排除 index 欄位）
            expected_columns = ['stock_id', 'stock_name', 'date', '殖利率(%)', '本益比', '股利年度', '股價淨值比']
            existing_columns = [col for col in expected_columns if col in column_names]
            
            if existing_columns:
                columns_str = ', '.join([f'"{col}"' for col in existing_columns])
                cursor.execute(f"INSERT INTO pe_data ({columns_str}) SELECT {columns_str} FROM {backup_table}")
                
                # 檢查複製結果
                cursor.execute("SELECT COUNT(*) FROM pe_data")
                new_count = cursor.fetchone()[0]
                print(f"✅ 已複製 {new_count:,} 筆資料到新表格")
            
            conn.commit()
            print("✅ PE 資料庫結構修復完成")
            
        else:
            print("✅ PE 資料庫結構正常，無需修復")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 修復 PE 資料庫時發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_pe_db_structure()
    test_pe_data_structure()
    
    response = input("\n是否要修復 PE 資料庫結構? (y/n): ")
    if response.lower() == 'y':
        fix_pe_database()
