@echo off
chcp 65001 > nul
title 台股智能選股系統 v21.0

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 台股智能選股系統 🚀                    ║
echo ║                                                              ║
echo ║  版本: v21.0 優化增強版 (獨立執行檔)                        ║
echo ║  編譯日期: 2025-07-30                                        ║
echo ║  檔案大小: 101MB                                             ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📍 系統資訊:
echo    • 免安裝獨立執行檔
echo    • 支援 Windows 10/11 (64位元)
echo    • 包含完整功能，無功能縮減
echo    • 自動創建資料庫和配置檔案
echo.
echo 🔄 正在啟動程式...
echo.

REM 檢查執行檔是否存在
if not exist "台股智能選股系統.exe" (
    echo ❌ 錯誤: 找不到執行檔 "台股智能選股系統.exe"
    echo.
    echo 💡 請確認:
    echo    1. 檔案是否在正確位置
    echo    2. 檔案是否被防毒軟體隔離
    echo    3. 是否有足夠的檔案權限
    echo.
    pause
    exit /b 1
)

REM 啟動程式
start "" "台股智能選股系統.exe"

REM 檢查啟動結果
if errorlevel 1 (
    echo.
    echo ❌ 程式啟動失敗
    echo.
    echo 💡 可能的解決方案:
    echo    1. 以系統管理員身分執行此腳本
    echo    2. 將程式加入防毒軟體白名單
    echo    3. 檢查 Windows 版本是否為 10/11
    echo    4. 安裝最新的 Visual C++ 可轉散發套件
    echo.
    echo 📖 詳細說明請參考 "說明文檔" 資料夾中的使用說明
    echo.
    pause
) else (
    echo ✅ 程式啟動成功！
    echo.
    echo 💡 使用提示:
    echo    • 首次啟動會自動創建資料庫檔案
    echo    • 需要網路連線以獲取最新股票資料
    echo    • 詳細使用說明請參考說明文檔
    echo.
    timeout /t 3 /nobreak > nul
)
