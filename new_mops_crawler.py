#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新 MOPS 網站財報爬蟲
基於 https://mopsov.twse.com.tw/mops/web/t164sb04
"""

import requests
import pandas as pd
import datetime
import time
import random
import urllib3
from io import StringIO

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_enhanced_headers():
    """獲取增強的 HTTP headers"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]
    
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://mopsov.twse.com.tw/mops/web/index',
        'Cache-Control': 'max-age=0',
    }

def smart_delay(base_delay=2, variance=1):
    """智能延遲"""
    delay = base_delay + random.uniform(0, variance)
    time.sleep(delay)
    return time.time()

def crawl_new_mops_income_statement(year, season, stock_id=None):
    """
    使用新 MOPS 網站爬取綜合損益表
    
    Args:
        year: 民國年 (例如: 112 代表 2023年)
        season: 季別 (1, 2, 3, 4)
        stock_id: 股票代號 (如果指定，只爬取該股票；否則爬取所有股票)
    
    Returns:
        pandas.DataFrame: 財報資料
    """
    print(f"🔄 爬取新MOPS綜合損益表: 民國{year}年Q{season}")
    
    # 新 MOPS 綜合損益表 URL
    url = "https://mopsov.twse.com.tw/mops/web/t164sb04"
    
    # 智能延遲
    smart_delay(base_delay=3, variance=2)
    
    try:
        headers = get_enhanced_headers()
        
        # 第一步: 獲取查詢頁面 (獲取 session)
        session = requests.Session()
        session.headers.update(headers)
        
        print(f"   📄 獲取查詢頁面...")
        response = session.get(url, verify=False, timeout=30)
        
        if response.status_code != 200:
            print(f"   ❌ 無法訪問查詢頁面: {response.status_code}")
            return pd.DataFrame()
        
        print(f"   ✅ 查詢頁面載入成功")
        
        # 第二步: 提交查詢表單
        print(f"   🔍 提交查詢表單...")
        
        # 構建表單資料
        form_data = {
            'step': '1',
            'firstin': 'ture',  # 注意: 原網站就是這樣拼寫的
            'off': '1',
            'co_id': stock_id if stock_id else '',  # 如果不指定股票代號，會返回所有股票
            'year': str(year),
            'season': str(season),
        }
        
        # 提交表單
        response = session.post(url, data=form_data, verify=False, timeout=60)
        
        if response.status_code != 200:
            print(f"   ❌ 查詢失敗: {response.status_code}")
            return pd.DataFrame()
        
        print(f"   ✅ 查詢成功: {len(response.content)} bytes")
        
        # 第三步: 解析回應內容
        content = response.text
        
        # 檢查是否有錯誤訊息
        if '查無資料' in content or '無此資料' in content:
            print(f"   ⚠️ 查無資料")
            return pd.DataFrame()
        
        if '您的網頁IP已經被證交所封鎖' in content:
            print(f"   ❌ IP被封鎖")
            return pd.DataFrame()
        
        # 保存回應內容用於調試
        debug_filename = f'new_mops_response_{year}_{season}.html'
        with open(debug_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   💾 回應內容已保存: {debug_filename}")
        
        # 第四步: 解析 HTML 表格
        print(f"   📊 解析財報表格...")
        
        try:
            # 使用 pandas 解析 HTML 表格
            dfs = pd.read_html(StringIO(content))
            print(f"   ✅ 找到 {len(dfs)} 個表格")
            
            if len(dfs) == 0:
                print(f"   ⚠️ 沒有找到表格")
                return pd.DataFrame()
            
            # 處理表格資料
            processed_data = process_new_mops_tables(dfs, year, season)
            
            if len(processed_data) > 0:
                print(f"   ✅ 成功處理 {len(processed_data)} 筆財報資料")
                return processed_data
            else:
                print(f"   ⚠️ 處理後無有效資料")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"   ❌ 解析表格失敗: {str(e)[:50]}...")
            return pd.DataFrame()
        
    except Exception as e:
        print(f"   ❌ 爬取失敗: {str(e)[:50]}...")
        return pd.DataFrame()

def process_new_mops_tables(dfs, year, season):
    """
    處理新 MOPS 網站的財報表格
    
    Args:
        dfs: pandas 解析的表格列表
        year: 民國年
        season: 季別
    
    Returns:
        pandas.DataFrame: 處理後的財報資料
    """
    print(f"   🔧 處理財報表格...")
    
    all_data = []
    
    # 計算日期 (財報發布日期)
    report_date = get_financial_report_date(year, season)
    
    for i, df in enumerate(dfs):
        print(f"     處理表格 {i+1}: {df.shape}")
        
        try:
            # 跳過太小的表格
            if df.shape[0] < 3 or df.shape[1] < 3:
                continue
            
            # 檢查是否為財報資料表格
            if not is_financial_table(df):
                continue
            
            # 處理表格資料
            processed_rows = process_financial_table(df, report_date)
            
            if processed_rows:
                all_data.extend(processed_rows)
                print(f"       ✅ 提取 {len(processed_rows)} 筆資料")
            
        except Exception as e:
            print(f"       ⚠️ 處理表格 {i+1} 失敗: {str(e)[:30]}...")
            continue
    
    if all_data:
        # 轉換為 DataFrame
        result_df = pd.DataFrame(all_data)
        
        # 設置索引
        if 'stock_id' in result_df.columns and 'date' in result_df.columns:
            result_df = result_df.set_index(['stock_id', 'date'])
        
        print(f"   ✅ 總共處理 {len(result_df)} 筆財報資料")
        return result_df
    else:
        print(f"   ⚠️ 沒有提取到有效資料")
        return pd.DataFrame()

def is_financial_table(df):
    """判斷是否為財報資料表格"""
    # 檢查表格內容是否包含財報相關關鍵字
    content_str = str(df.values).lower()
    
    financial_keywords = [
        '營業收入', '營收', '收入', 'revenue',
        '營業成本', '成本', 'cost',
        '營業費用', '費用', 'expense',
        '營業利益', '利益', 'income',
        '淨利', '損益', 'profit', 'loss',
        '股票代號', '公司代號', '公司名稱'
    ]
    
    found_keywords = sum(1 for keyword in financial_keywords if keyword in content_str)
    
    # 如果找到足夠的財報關鍵字，認為是財報表格
    return found_keywords >= 2

def process_financial_table(df, report_date):
    """處理單個財報表格"""
    processed_rows = []
    
    try:
        # 獲取欄位名稱
        columns = list(df.columns)
        
        # 處理多層欄位名稱
        if hasattr(df.columns, 'levels') and len(df.columns.levels) > 1:
            new_columns = []
            for col in df.columns:
                if hasattr(col, '__len__') and len(col) > 1:
                    # 合併多層欄位名稱
                    col_name = '_'.join([str(c) for c in col if str(c) != 'nan'])
                    new_columns.append(col_name)
                else:
                    new_columns.append(str(col))
            df.columns = new_columns
        
        # 尋找包含股票代號的行
        for idx, row in df.iterrows():
            try:
                # 檢查第一欄是否為股票代號
                first_col = str(row.iloc[0]).strip()
                
                # 股票代號格式檢查
                if not (first_col.isdigit() and len(first_col) == 4):
                    continue
                
                # 獲取公司名稱
                company_name = str(row.iloc[1]).strip() if len(row) > 1 else ''
                
                # 建立基本資料
                stock_data = {
                    'stock_id': f"{first_col} {company_name}",
                    'date': report_date,
                }
                
                # 提取財報數值
                for col_idx, col_name in enumerate(df.columns[2:], 2):  # 從第3欄開始
                    if col_idx < len(row):
                        value = str(row.iloc[col_idx]).strip()
                        
                        # 清理數值
                        if value and value != 'nan' and value != '--':
                            try:
                                # 移除千分位逗號
                                clean_value = value.replace(',', '').replace('(', '-').replace(')', '')
                                numeric_value = pd.to_numeric(clean_value, errors='coerce')
                                
                                if not pd.isna(numeric_value):
                                    stock_data[str(col_name)] = numeric_value
                            except:
                                # 如果無法轉換為數值，保留原始文字
                                stock_data[str(col_name)] = value
                
                # 只有當有足夠的財報資料時才加入
                if len(stock_data) > 3:  # 除了 stock_id, date 之外還有其他資料
                    processed_rows.append(stock_data)
                
            except Exception as e:
                continue  # 跳過有問題的行
        
    except Exception as e:
        print(f"         處理表格時出錯: {str(e)[:30]}...")
    
    return processed_rows

def get_financial_report_date(year, season):
    """
    根據年度和季別計算財報發布日期
    
    Args:
        year: 民國年
        season: 季別 (1, 2, 3, 4)
    
    Returns:
        datetime.date: 財報發布日期
    """
    # 轉換為西元年
    western_year = year + 1911
    
    # 財報發布日期規則
    if season == 1:  # Q1 財報
        return datetime.date(western_year, 5, 15)
    elif season == 2:  # Q2 財報
        return datetime.date(western_year, 8, 14)
    elif season == 3:  # Q3 財報
        return datetime.date(western_year, 11, 14)
    elif season == 4:  # Q4 財報
        return datetime.date(western_year + 1, 3, 31)
    else:
        raise ValueError(f"無效的季別: {season}")

def test_new_mops_crawler():
    """測試新 MOPS 爬蟲"""
    print("🔧 測試新 MOPS 財報爬蟲")
    print("=" * 60)
    
    # 測試參數
    test_cases = [
        (112, 4),  # 2023年Q4
        (113, 1),  # 2024年Q1
    ]
    
    for year, season in test_cases:
        print(f"\n📅 測試: 民國{year}年Q{season}")
        
        try:
            result = crawl_new_mops_income_statement(year, season)
            
            if not result.empty:
                print(f"✅ 成功: {len(result)} 筆資料")
                print(f"   索引: {result.index.names}")
                print(f"   欄位數: {len(result.columns)}")
                
                # 顯示樣本資料
                print(f"   樣本資料:")
                print(result.head(2))
                
                # 保存測試結果
                filename = f'new_mops_test_{year}_{season}.pkl'
                result.to_pickle(filename)
                print(f"   💾 測試資料已保存: {filename}")
                
            else:
                print(f"❌ 無資料")
                
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")

if __name__ == "__main__":
    test_new_mops_crawler()
