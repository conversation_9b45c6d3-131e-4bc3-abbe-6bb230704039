#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Google新聞365天搜尋設定
"""

def test_google_news_settings():
    """測試Google新聞爬蟲的365天設定"""
    print("🔍 測試Google新聞365天搜尋設定")
    print("=" * 60)
    
    try:
        # 測試GUI設定
        print("\n📋 測試GUI預設設定:")
        print("-" * 40)
        
        from google_stock_news_gui import GoogleStockNewsDialog
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # 創建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 創建對話框實例
        dialog = GoogleStockNewsDialog(None, "2330")
        
        # 檢查預設值
        default_days = dialog.days_spinbox.value()
        max_days = dialog.days_spinbox.maximum()
        min_days = dialog.days_spinbox.minimum()
        
        print(f"✅ 預設搜尋天數: {default_days} 天")
        print(f"✅ 最大搜尋天數: {max_days} 天")
        print(f"✅ 最小搜尋天數: {min_days} 天")
        
        # 驗證設定
        if default_days == 365:
            print("🎉 GUI預設值設定正確：365天")
        else:
            print(f"❌ GUI預設值錯誤：期望365天，實際{default_days}天")
        
        if max_days == 365:
            print("✅ 最大值設定正確：365天")
        else:
            print(f"❌ 最大值錯誤：期望365天，實際{max_days}天")
        
        # 測試主程式設定
        print("\n📋 測試主程式右鍵選單設定:")
        print("-" * 40)
        
        try:
            from O3mh_gui_v21_optimized import StockScreenerGUI
            
            # 創建主程式實例
            gui = StockScreenerGUI()
            
            # 檢查crawl_google_stock_news方法
            import inspect
            source = inspect.getsource(gui.crawl_google_stock_news)
            
            if "setValue(365)" in source:
                print("✅ 主程式右鍵選單設定正確：365天")
            elif "setValue(30)" in source:
                print("❌ 主程式右鍵選單仍為30天，需要更新")
            elif "setValue(7)" in source:
                print("❌ 主程式右鍵選單仍為7天，需要更新")
            else:
                print("⚠️ 無法確定主程式右鍵選單設定")
            
            print("✅ 主程式載入成功")
            
        except Exception as e:
            print(f"❌ 主程式測試失敗: {e}")
        
        print("\n" + "=" * 60)
        print("✅ Google新聞365天設定測試完成！")
        
        print("\n📊 365天搜尋的優勢:")
        print("🎯 **節省時間**:")
        print("• 一次性獲取完整年度資料")
        print("• 避免重複搜尋不同時間範圍")
        print("• 適合深度分析和研究")
        
        print("\n🎯 **資料完整性**:")
        print("• 涵蓋所有重要事件和公告")
        print("• 包含季報、年報等定期資訊")
        print("• 適合冷門股票新聞搜尋")
        
        print("\n🎯 **使用建議**:")
        print("• 即時追蹤：手動調整為7-14天")
        print("• 一般分析：手動調整為30天")
        print("• 深度研究：使用預設365天")
        print("• 冷門股票：使用預設365天")
        
        print("\n🔧 **效能考量**:")
        print("• 搜尋時間：5-10分鐘（視股票熱門程度）")
        print("• 新聞數量：50-500篇（視股票類型）")
        print("• 系統負擔：中等，可接受範圍")
        
        print("\n🖱️ **使用方式**:")
        print("1. **右鍵選單**：自動使用365天設定")
        print("2. **選單功能**：預設365天，可手動調整")
        print("3. **快速模式**：自動啟用，優化爬取速度")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確認以下檔案存在：")
        print("• google_stock_news_gui.py")
        print("• O3mh_gui_v21_optimized.py")
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def show_comparison_table():
    """顯示不同天數設定的比較表"""
    print("\n📊 搜尋天數設定比較表")
    print("=" * 80)
    
    comparison_data = [
        ("搜尋天數", "適用場景", "新聞數量", "爬取時間", "推薦指數"),
        ("-" * 8, "-" * 20, "-" * 8, "-" * 8, "-" * 8),
        ("7-14天", "即時事件追蹤", "10-30篇", "1-2分鐘", "★★★☆☆"),
        ("30天", "一般投資分析", "30-80篇", "3-5分鐘", "★★★★☆"),
        ("60-90天", "深度研究分析", "50-150篇", "5-8分鐘", "★★★★★"),
        ("365天", "完整年度資料", "100-500篇", "8-15分鐘", "★★★★★"),
    ]
    
    for row in comparison_data:
        print(f"{row[0]:<10} {row[1]:<20} {row[2]:<10} {row[3]:<10} {row[4]:<10}")
    
    print("\n🎯 **365天設定的特殊優勢**:")
    print("• 🔄 **一次到位**：無需重複搜尋不同時間範圍")
    print("• 📈 **趨勢分析**：可觀察長期新聞趨勢變化")
    print("• 🎯 **冷門股票**：確保獲得足夠的新聞資料")
    print("• 💾 **資料保存**：建立完整的新聞資料庫")
    print("• ⏰ **時間效率**：一次搜尋滿足所有分析需求")

if __name__ == "__main__":
    success = test_google_news_settings()
    
    if success:
        show_comparison_table()
        print("\n🎉 測試完成！Google新聞搜尋已設定為365天預設值。")
    else:
        print("\n❌ 測試失敗，請檢查相關檔案和設定。")
