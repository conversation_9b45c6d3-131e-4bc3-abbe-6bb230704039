#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新 MOPS 網站的財報 API
"""

import requests
import urllib3
import datetime

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_new_financial_api():
    """測試新的財報 API"""
    print("🔍 測試新 MOPS 財報 API")
    print("=" * 60)
    
    # 新的 MOPS 網站基礎 URL
    base_url = "https://mopsov.twse.com.tw/mops/web"
    
    # 測試不同的財報 API 端點
    api_endpoints = [
        "t164sb04",  # 綜合損益表 (income statement)
        "t164sb03",  # 資產負債表
        "t164sb05",  # 現金流量表
        "t163sb01",  # 財務報告公告
        "t211sb01_q1",  # 財務資訊重點專區
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    for endpoint in api_endpoints:
        print(f"\n🌐 測試端點: {endpoint}")
        url = f"{base_url}/{endpoint}"
        
        try:
            response = requests.get(url, headers=headers, verify=False, timeout=30)
            
            print(f"   URL: {url}")
            print(f"   狀態: {response.status_code}")
            print(f"   長度: {len(response.content)} bytes")
            print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
            
            if response.status_code == 200:
                content = response.text
                
                # 檢查是否包含表單或查詢介面
                if 'form' in content.lower():
                    print(f"   💡 包含表單 - 可能是查詢介面")
                
                # 檢查是否包含財報相關關鍵字
                keywords = ['公司代號', '年度', '季別', '財務報表', '損益表', '資產負債表']
                found_keywords = [kw for kw in keywords if kw in content]
                
                if found_keywords:
                    print(f"   🎯 找到關鍵字: {found_keywords}")
                
                # 保存頁面內容用於分析
                filename = f"mops_{endpoint}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   💾 頁面已保存: {filename}")
                
            else:
                print(f"   ❌ 無法訪問")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {str(e)[:50]}...")

def test_income_statement_query():
    """測試綜合損益表查詢"""
    print(f"\n🔍 測試綜合損益表查詢")
    print("=" * 60)
    
    # 綜合損益表的 URL
    url = "https://mopsov.twse.com.tw/mops/web/t164sb04"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Referer': 'https://mopsov.twse.com.tw/mops/web/index',
    }
    
    try:
        # 先獲取查詢頁面
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 成功訪問綜合損益表頁面")
            print(f"   頁面長度: {len(response.content)} bytes")
            
            content = response.text
            
            # 分析表單結構
            if '<form' in content:
                print(f"   💡 找到表單，分析參數...")
                
                # 尋找可能的參數
                import re
                
                # 尋找 input 欄位
                inputs = re.findall(r'<input[^>]*name=["\']([^"\']*)["\'][^>]*>', content)
                selects = re.findall(r'<select[^>]*name=["\']([^"\']*)["\'][^>]*>', content)
                
                print(f"   📋 Input 欄位: {inputs}")
                print(f"   📋 Select 欄位: {selects}")
                
                # 嘗試提交查詢 (以台積電為例)
                if inputs or selects:
                    print(f"\n🧪 嘗試查詢台積電 (2330) 的財報...")
                    
                    # 構建查詢參數
                    query_data = {
                        'co_id': '2330',  # 台積電
                        'year': '112',    # 2023年 (民國112年)
                        'season': '4',    # Q4
                    }
                    
                    # 添加其他可能需要的參數
                    for input_name in inputs:
                        if input_name not in query_data:
                            query_data[input_name] = ''
                    
                    try:
                        query_response = requests.post(url, data=query_data, headers=headers, verify=False, timeout=30)
                        
                        if query_response.status_code == 200:
                            print(f"   ✅ 查詢成功: {len(query_response.content)} bytes")
                            
                            # 檢查是否包含財報資料
                            query_content = query_response.text
                            
                            if '台積電' in query_content or '2330' in query_content:
                                print(f"   🎯 找到台積電資料!")
                                
                                # 保存查詢結果
                                with open('tsmc_income_statement.html', 'w', encoding='utf-8') as f:
                                    f.write(query_content)
                                print(f"   💾 台積電財報已保存: tsmc_income_statement.html")
                                
                            else:
                                print(f"   ⚠️ 查詢結果中未找到預期資料")
                                
                        else:
                            print(f"   ❌ 查詢失敗: {query_response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ 查詢錯誤: {str(e)[:50]}...")
            
        else:
            print(f"❌ 無法訪問綜合損益表頁面: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 新 MOPS 財報 API 測試工具")
    print("=" * 60)
    print("🎯 目標: 測試新 MOPS 網站的財報查詢功能")
    print("=" * 60)
    
    # 測試1: 基本 API 端點
    test_new_financial_api()
    
    # 測試2: 綜合損益表查詢
    test_income_statement_query()
    
    print(f"\n💡 下一步:")
    print(f"   1. 分析保存的 HTML 檔案，了解查詢參數")
    print(f"   2. 建立新的財報爬蟲函數")
    print(f"   3. 整合到 auto_update.py 中")

if __name__ == "__main__":
    main()
