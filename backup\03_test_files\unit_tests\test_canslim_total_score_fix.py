#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 CANSLIM total_score 錯誤修復
"""
import sys
import os
import pandas as pd
import numpy as np

def create_test_data():
    """創建測試數據"""
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 創建基本價格數據
    base_price = 100
    price_changes = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))  # 確保價格不為負
    
    volumes = np.random.randint(100000, 1000000, 100)
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p * 0.99 for p in prices],
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': volumes
    })
    
    return df

def test_canslim_strategy_result_format():
    """測試 CANSLIM 策略結果格式"""
    print("🚀 測試 CANSLIM 策略結果格式...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入策略模組
        from strategies.canslim_strategy import CANSLIMStrategy
        
        print("✅ CANSLIM 策略模組導入成功")
        
        # 創建策略實例
        canslim = CANSLIMStrategy()
        
        # 創建測試數據
        test_df = create_test_data()
        
        print("✅ 測試數據創建成功")
        
        # 執行策略分析
        result = canslim.analyze_stock(test_df)
        
        print("✅ 策略分析執行成功")
        
        # 檢查結果格式
        print(f"\n📊 策略分析結果:")
        print(f"  結果類型: {type(result)}")
        print(f"  結果鍵值: {list(result.keys())}")
        
        # 檢查關鍵欄位
        required_fields = ['suitable', 'reason', 'details', 'strategy_name']
        score_fields = ['score', 'total_score']
        
        missing_fields = []
        for field in required_fields:
            if field not in result:
                missing_fields.append(field)
            else:
                print(f"  ✅ {field}: {result[field]}")
        
        # 檢查評分欄位
        score_field_found = None
        for field in score_fields:
            if field in result:
                score_field_found = field
                print(f"  ✅ 評分欄位 {field}: {result[field]}")
                break
        
        if not score_field_found:
            print(f"  ❌ 缺少評分欄位: {score_fields}")
            return False
        
        if missing_fields:
            print(f"  ❌ 缺少必要欄位: {missing_fields}")
            return False
        
        print(f"  ✅ 所有必要欄位都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_program_compatibility():
    """測試主程式兼容性"""
    print("\n🚀 測試主程式兼容性...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入必要的模組
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 導入主程式
        import O3mh_gui_v21_optimized
        
        # 創建主視窗
        main_window = O3mh_gui_v21_optimized.StockScreenerGUI()
        
        print("✅ 主視窗創建成功")
        
        # 創建測試數據
        test_df = create_test_data()
        
        # 測試 CANSLIM 策略檢查方法
        success, message = main_window.check_canslim_strategy(test_df)
        
        print(f"✅ CANSLIM 策略檢查執行成功")
        print(f"  結果: {success}")
        print(f"  訊息: {message}")
        
        # 檢查是否還有 total_score 錯誤
        if "'total_score'" in message:
            print(f"❌ 仍然存在 total_score 錯誤")
            return False
        
        print(f"✅ 沒有 total_score 錯誤")
        
        # 清理
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 主程式兼容性測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_score_field_compatibility():
    """測試評分欄位兼容性"""
    print("\n🚀 測試評分欄位兼容性...")
    
    try:
        # 測試兼容性代碼
        test_results = [
            {'score': 75, 'suitable': True},  # 只有 score
            {'total_score': 80, 'suitable': True},  # 只有 total_score
            {'score': 60, 'total_score': 65, 'suitable': True},  # 兩者都有
            {'suitable': False}  # 都沒有
        ]
        
        for i, result in enumerate(test_results):
            # 模擬主程式中的兼容性代碼
            score = result.get('total_score', result.get('score', 0))
            print(f"  測試 {i+1}: 結果 {result} -> 評分: {score}")
        
        print("✅ 評分欄位兼容性測試通過")
        
        return True
        
    except Exception as e:
        print(f"❌ 評分欄位兼容性測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 CANSLIM total_score 錯誤修復測試")
    print("=" * 60)
    
    success1 = test_canslim_strategy_result_format()
    success2 = test_main_program_compatibility()
    success3 = test_score_field_compatibility()
    
    overall_success = success1 and success2 and success3
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 CANSLIM total_score 錯誤修復測試通過！")
        print("✅ CANSLIM 策略結果格式正確")
        print("✅ 主程式兼容性正常")
        print("✅ 評分欄位兼容性完善")
        print("✅ 不再出現 total_score 錯誤")
    else:
        print("❌ CANSLIM total_score 錯誤修復測試失敗")
        print("需要進一步檢查問題")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
