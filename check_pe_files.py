#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 pe.pkl 和 pe_old.pkl 的內容和時間範圍
"""

import pandas as pd
import os
from datetime import datetime

def check_pe_file(file_path, file_name):
    """檢查單個 pe 檔案"""
    print(f"\n📊 檢查 {file_name}...")
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return None
    
    try:
        # 檢查檔案資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f"   檔案大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
        print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
        
        # 讀取資料
        print("   正在讀取資料...")
        data = pd.read_pickle(file_path)
        
        print(f"   資料筆數: {len(data):,}")
        print(f"   資料結構: {type(data)}")
        
        if hasattr(data, 'index'):
            print(f"   索引類型: {type(data.index)}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
        
        if hasattr(data, 'columns'):
            print(f"   欄位數量: {len(data.columns)}")
            print(f"   欄位名稱: {list(data.columns)[:5]}...")
        
        # 檢查日期範圍
        if hasattr(data, 'index') and hasattr(data.index, 'names') and 'date' in data.index.names:
            dates = data.index.get_level_values('date')
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f"   ✅ 日期範圍: {start_date} 至 {end_date}")
            print(f"   唯一日期數: {unique_dates}")
            
            return {
                'file_path': file_path,
                'file_name': file_name,
                'data': data,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(data),
                'unique_dates': unique_dates
            }
        else:
            print("   ⚠️ 未找到日期索引")
            return None
            
    except Exception as e:
        print(f"   ❌ 讀取失敗: {str(e)}")
        import traceback
        print(f"   詳細錯誤: {traceback.format_exc()}")
        return None

def analyze_pe_files():
    """分析兩個 pe 檔案"""
    print("🔍 PE 檔案分析工具")
    print("=" * 60)
    
    # 檢查兩個檔案
    pe_file = "history/tables/pe.pkl"
    pe_old_file = "history/tables/pe_old.pkl"
    
    pe_info = check_pe_file(pe_file, "pe.pkl")
    pe_old_info = check_pe_file(pe_old_file, "pe_old.pkl")
    
    # 分析結果
    print("\n" + "=" * 60)
    print("📊 分析結果")
    print("=" * 60)
    
    if pe_info and pe_old_info:
        print("✅ 兩個檔案都成功讀取")
        
        print(f"\n📋 檔案比較:")
        print(f"   pe.pkl:     {pe_info['start_date']} 至 {pe_info['end_date']} ({pe_info['record_count']:,} 筆)")
        print(f"   pe_old.pkl: {pe_old_info['start_date']} 至 {pe_old_info['end_date']} ({pe_old_info['record_count']:,} 筆)")
        
        # 檢查時間連續性
        gap_days = (pe_info['start_date'] - pe_old_info['end_date']).days
        print(f"\n🔍 時間間隔分析:")
        print(f"   pe_old.pkl 結束: {pe_old_info['end_date']}")
        print(f"   pe.pkl 開始:     {pe_info['start_date']}")
        print(f"   間隔天數: {gap_days} 天")
        
        if gap_days <= 5:  # 考慮週末，5天內算連續
            print("✅ 時間範圍相鄰，建議合併")
            return True, pe_info, pe_old_info
        elif gap_days < 0:
            print("⚠️ 時間範圍重疊")
            overlap_days = abs(gap_days)
            print(f"   重疊天數: {overlap_days} 天")
            return True, pe_info, pe_old_info
        else:
            print(f"❌ 時間範圍不連續，間隔 {gap_days} 天")
            return False, pe_info, pe_old_info
    
    elif pe_info:
        print("⚠️ 只有 pe.pkl 可讀取")
        return False, pe_info, None
    
    elif pe_old_info:
        print("⚠️ 只有 pe_old.pkl 可讀取")
        return False, None, pe_old_info
    
    else:
        print("❌ 兩個檔案都無法讀取")
        return False, None, None

def merge_pe_files(pe_info, pe_old_info):
    """合併兩個 pe 檔案"""
    print("\n🔄 開始合併 pe 檔案...")
    
    try:
        # 創建備份
        import shutil
        backup_file = f"history/tables/pe_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        shutil.copy2(pe_info['file_path'], backup_file)
        print(f"✅ 備份創建: {backup_file}")
        
        # 合併資料
        print("🔄 合併資料...")
        old_data = pe_old_info['data']
        new_data = pe_info['data']
        
        # 檢查重疊
        old_dates = set(old_data.index.get_level_values('date'))
        new_dates = set(new_data.index.get_level_values('date'))
        overlap_dates = old_dates.intersection(new_dates)
        
        if overlap_dates:
            print(f"⚠️ 發現重疊日期: {len(overlap_dates)} 個")
            print(f"   重疊範圍: {min(overlap_dates)} 至 {max(overlap_dates)}")
            print("   以新檔案資料為準")
            
            # 移除舊資料中的重疊日期
            old_data_filtered = old_data[~old_data.index.get_level_values('date').isin(overlap_dates)]
        else:
            print("✅ 沒有重疊日期")
            old_data_filtered = old_data
        
        # 合併
        merged_data = pd.concat([old_data_filtered, new_data])
        print(f"✅ 合併完成: {len(merged_data):,} 筆資料")
        
        # 去重並排序
        print("🔄 去重並排序...")
        merged_data = merged_data[~merged_data.index.duplicated(keep='last')]
        merged_data = merged_data.sort_index()
        
        final_dates = merged_data.index.get_level_values('date')
        print(f"✅ 最終資料: {len(merged_data):,} 筆")
        print(f"   日期範圍: {final_dates.min()} 至 {final_dates.max()}")
        
        # 保存
        print("💾 保存合併後的資料...")
        merged_data.to_pickle(pe_info['file_path'])
        print(f"✅ 已保存到: {pe_info['file_path']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    should_merge, pe_info, pe_old_info = analyze_pe_files()
    
    if should_merge and pe_info and pe_old_info:
        print("\n💡 建議合併這兩個檔案")
        response = input("是否進行合併？(y/N): ").strip().lower()
        
        if response == 'y':
            success = merge_pe_files(pe_info, pe_old_info)
            
            if success:
                print("\n🎉 合併成功！")
                print("💡 後續建議:")
                print("   • 檢查合併後的 pe.pkl")
                print("   • 確認無誤後可刪除 pe_old.pkl")
                print("   • 備份檔案已自動創建")
            else:
                print("\n❌ 合併失敗")
        else:
            print("\n❌ 操作已取消")
    else:
        print("\n💡 不建議合併或檔案無法讀取")

if __name__ == "__main__":
    main()
