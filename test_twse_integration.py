#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試台灣證交所市場數據爬蟲整合
"""

import sys
import os

def test_imports():
    """測試模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        from market_data_crawler import MarketDataCrawler, TWSEMarketDataAPI
        print("✅ market_data_crawler 模組導入成功")
        
        from twse_market_data_dialog import TWSEMarketDataDialog, MarketDataWorker
        print("✅ twse_market_data_dialog 模組導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_api_connection():
    """測試 API 連接"""
    print("\n🔍 測試 API 連接...")
    
    try:
        from market_data_crawler import TWSEMarketDataAPI
        
        # 測試獲取市場指數資訊
        data = TWSEMarketDataAPI.get_latest_market_data("/exchangeReport/MI_INDEX", count=1)
        
        if data:
            print(f"✅ API 連接成功，獲取到 {len(data)} 筆市場指數資料")
            return True
        else:
            print("⚠️ API 連接成功但無數據")
            return False
            
    except Exception as e:
        print(f"❌ API 連接失敗: {e}")
        return False

def test_crawler_functionality():
    """測試爬蟲功能"""
    print("\n🔍 測試爬蟲功能...")
    
    try:
        from market_data_crawler import MarketDataCrawler
        
        crawler = MarketDataCrawler()
        
        # 測試市場指數資訊
        print("   測試市場指數資訊...")
        df_index = crawler.get_market_index_info()
        if not df_index.empty:
            print(f"   ✅ 市場指數資訊: {len(df_index)} 筆")
        else:
            print("   ⚠️ 市場指數資訊: 無數據")
        
        # 測試融資融券統計
        print("   測試融資融券統計...")
        df_margin = crawler.get_margin_trading_info()
        if not df_margin.empty:
            print(f"   ✅ 融資融券統計: {len(df_margin)} 筆")
        else:
            print("   ⚠️ 融資融券統計: 無數據")
        
        # 測試歷史指數資料
        print("   測試歷史指數資料...")
        df_historical = crawler.get_market_historical_index()
        if not df_historical.empty:
            print(f"   ✅ 歷史指數資料: {len(df_historical)} 筆")
        else:
            print("   ⚠️ 歷史指數資料: 無數據")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬蟲功能測試失敗: {e}")
        return False

def test_database_save():
    """測試資料庫保存"""
    print("\n🔍 測試資料庫保存...")
    
    try:
        from market_data_crawler import MarketDataCrawler
        import pandas as pd
        
        crawler = MarketDataCrawler()
        
        # 創建測試數據
        test_data = pd.DataFrame({
            'test_column': ['test_value'],
            'crawl_time': ['2025-07-27 13:00:00']
        })
        
        # 測試保存
        success = crawler.save_to_database(test_data, 'test_table', 'test_db')
        
        if success:
            print("✅ 資料庫保存測試成功")
            
            # 清理測試檔案
            test_db_path = os.path.join(crawler.db_path, 'test_db.db')
            if os.path.exists(test_db_path):
                os.remove(test_db_path)
                print("✅ 測試檔案已清理")
            
            return True
        else:
            print("❌ 資料庫保存測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 資料庫保存測試失敗: {e}")
        return False

def test_gui_integration():
    """測試 GUI 整合"""
    print("\n🔍 測試 GUI 整合...")
    
    try:
        # 檢查 GUI 程式中是否有對應的方法
        gui_file = "O3mh_gui_v21_optimized.py"
        
        if not os.path.exists(gui_file):
            print(f"❌ GUI 檔案不存在: {gui_file}")
            return False
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否有對應的方法
        if 'open_twse_market_data_crawler' in content:
            print("✅ GUI 整合方法存在")
        else:
            print("❌ GUI 整合方法不存在")
            return False
        
        # 檢查是否有選單項目
        if '台灣證交所市場數據' in content:
            print("✅ GUI 選單項目存在")
        else:
            print("❌ GUI 選單項目不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ GUI 整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 台灣證交所市場數據爬蟲整合測試")
    print("=" * 60)
    
    tests = [
        ("模組導入", test_imports),
        ("API 連接", test_api_connection),
        ("爬蟲功能", test_crawler_functionality),
        ("資料庫保存", test_database_save),
        ("GUI 整合", test_gui_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}測試:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}測試通過")
            else:
                print(f"❌ {test_name}測試失敗")
        except Exception as e:
            print(f"❌ {test_name}測試異常: {e}")
    
    print(f"\n📊 測試結果總結:")
    print(f"   通過: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n🎉 所有測試通過！台灣證交所市場數據爬蟲整合成功！")
        print(f"\n💡 使用方法:")
        print(f"   1. 啟動 GUI 程式")
        print(f"   2. 點擊選單：爬蟲 → 📊 台灣證交所市場數據")
        print(f"   3. 選擇要爬取的數據類型")
        print(f"   4. 點擊「🚀 開始爬取」")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關模組和配置")

if __name__ == "__main__":
    main()
