#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收爬蟲直接更新到 monthly_report.pkl 的完整流程
"""

import sys
import os
import datetime
import pandas as pd
import shutil

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加 finlab 路徑
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def backup_monthly_report():
    """備份現有的 monthly_report.pkl"""
    original_file = "history/tables/monthly_report.pkl"
    backup_file = f"history/tables/monthly_report_backup_update_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已備份: {backup_file}")
        return backup_file
    else:
        print(f"⚠️ 原始檔案不存在: {original_file}")
        return None

def check_current_monthly_report():
    """檢查當前 monthly_report.pkl 的狀態"""
    file_path = "history/tables/monthly_report.pkl"
    
    print(f"📊 檢查當前 monthly_report.pkl 狀態")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return None
    
    try:
        df = pd.read_pickle(file_path)
        
        # 基本資訊
        print(f"📁 檔案資訊:")
        file_size = os.path.getsize(file_path) / (1024 * 1024)
        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        print(f"   大小: {file_size:.1f} MB")
        print(f"   修改時間: {file_mtime}")
        
        print(f"\n📊 資料資訊:")
        print(f"   總筆數: {len(df):,}")
        print(f"   索引: {df.index.names}")
        print(f"   欄位: {list(df.columns)}")
        
        # 日期範圍
        dates = df.index.get_level_values('date')
        date_list = sorted(dates.unique())
        print(f"\n📅 日期範圍:")
        print(f"   最早: {date_list[0]}")
        print(f"   最新: {date_list[-1]}")
        print(f"   總月數: {len(date_list)}")
        
        # 計算需要更新的月數
        latest_date = date_list[-1]
        if isinstance(latest_date, pd.Timestamp):
            latest_date = latest_date.date()
        
        today = datetime.date.today()
        months_behind = (today.year - latest_date.year) * 12 + (today.month - latest_date.month)
        print(f"   落後月數: {months_behind}")
        
        return {
            'df': df,
            'latest_date': latest_date,
            'months_behind': months_behind,
            'original_count': len(df)
        }
        
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return None

def test_single_month_crawl():
    """測試單月爬蟲"""
    print(f"\n🧪 測試單月月營收爬蟲")
    print("=" * 50)
    
    try:
        from auto_update import crawl_monthly_report_fixed
        
        # 測試最新月份 (2024年11月)
        test_date = datetime.date(2024, 11, 1)
        print(f"📅 測試日期: {test_date}")
        
        result = crawl_monthly_report_fixed(test_date)
        
        if len(result) > 0:
            print(f"✅ 爬蟲測試成功: {len(result)} 筆資料")
            print(f"   欄位: {list(result.columns)}")
            print(f"   索引: {result.index.names}")
            
            # 檢查資料格式
            if isinstance(result.index, pd.MultiIndex) and result.index.names == ['stock_id', 'date']:
                print(f"   ✅ 索引格式正確")
            else:
                print(f"   ❌ 索引格式錯誤")
            
            # 檢查欄位格式
            expected_columns = ['當月營收', '上月營收', '去年當月營收', '上月比較增減(%)', '去年同月增減(%)', '當月累計營收', '去年累計營收', '前期比較增減(%)']
            missing_columns = set(expected_columns) - set(result.columns)
            if not missing_columns:
                print(f"   ✅ 欄位格式正確")
            else:
                print(f"   ⚠️ 缺少欄位: {missing_columns}")
            
            return result
        else:
            print(f"❌ 爬蟲測試失敗: 無資料")
            return None
            
    except Exception as e:
        print(f"❌ 爬蟲測試異常: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return None

def test_auto_update_integration():
    """測試 auto_update 整合"""
    print(f"\n🔧 測試 auto_update 整合")
    print("=" * 50)
    
    try:
        # 備份檔案
        backup_file = backup_monthly_report()
        
        # 檢查當前狀態
        current_status = check_current_monthly_report()
        if not current_status:
            print(f"❌ 無法檢查當前狀態")
            return False
        
        original_count = current_status['original_count']
        months_behind = current_status['months_behind']
        
        print(f"\n🔄 開始使用 auto_update 更新月營收...")
        print(f"   原始筆數: {original_count:,}")
        print(f"   落後月數: {months_behind}")
        
        # 使用 auto_update 函數
        from auto_update import auto_update, month_range
        from crawler import crawl_monthly_report
        
        # 執行更新
        auto_update('monthly_report', crawl_monthly_report, month_range)
        
        print(f"\n📊 檢查更新後狀態...")
        
        # 檢查更新後狀態
        updated_status = check_current_monthly_report()
        if not updated_status:
            print(f"❌ 更新後無法讀取檔案")
            return False
        
        updated_count = updated_status['original_count']
        updated_latest_date = updated_status['latest_date']
        updated_months_behind = updated_status['months_behind']
        
        print(f"\n📈 更新結果:")
        print(f"   更新前筆數: {original_count:,}")
        print(f"   更新後筆數: {updated_count:,}")
        print(f"   新增筆數: {updated_count - original_count:,}")
        print(f"   更新前最新日期: {current_status['latest_date']}")
        print(f"   更新後最新日期: {updated_latest_date}")
        print(f"   更新後落後月數: {updated_months_behind}")
        
        if updated_count > original_count:
            print(f"   ✅ 更新成功！新增了 {updated_count - original_count:,} 筆資料")
            return True
        elif updated_months_behind < months_behind:
            print(f"   ✅ 更新成功！減少了 {months_behind - updated_months_behind} 個月的落後")
            return True
        else:
            print(f"   ⚠️ 沒有新增資料或減少落後")
            return False
            
    except Exception as e:
        print(f"❌ auto_update 整合測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def verify_data_integrity():
    """驗證資料完整性"""
    print(f"\n🔍 驗證資料完整性")
    print("=" * 50)
    
    try:
        df = pd.read_pickle("history/tables/monthly_report.pkl")
        
        print(f"📊 完整性檢查:")
        print(f"   總筆數: {len(df):,}")
        
        # 檢查重複資料
        duplicates = df.index.duplicated().sum()
        print(f"   重複索引: {duplicates}")
        
        # 檢查空值
        null_counts = df.isnull().sum()
        total_nulls = null_counts.sum()
        print(f"   空值總數: {total_nulls}")
        
        if total_nulls > 0:
            print(f"   各欄位空值:")
            for col, count in null_counts.items():
                if count > 0:
                    print(f"     {col}: {count}")
        
        # 檢查資料類型
        print(f"\n📋 資料類型:")
        for col in df.columns:
            dtype = df[col].dtype
            print(f"   {col}: {dtype}")
        
        # 檢查最新資料
        dates = df.index.get_level_values('date')
        latest_date = max(dates)
        latest_count = len(df[df.index.get_level_values('date') == latest_date])
        
        print(f"\n📅 最新資料:")
        print(f"   最新日期: {latest_date}")
        print(f"   該日期筆數: {latest_count:,}")
        
        if duplicates == 0 and total_nulls == 0:
            print(f"\n✅ 資料完整性驗證通過")
            return True
        else:
            print(f"\n⚠️ 資料完整性有問題")
            return False
            
    except Exception as e:
        print(f"❌ 完整性驗證失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 月營收更新流程測試工具")
    print("=" * 70)
    print("🎯 目標: 確保月營收爬蟲可直接更新到 monthly_report.pkl")
    print("=" * 70)
    
    # 1. 檢查當前狀態
    current_status = check_current_monthly_report()
    if not current_status:
        print(f"\n❌ 無法檢查當前檔案狀態")
        return
    
    # 2. 測試單月爬蟲
    single_result = test_single_month_crawl()
    if single_result is None or (isinstance(single_result, pd.DataFrame) and single_result.empty):
        print(f"\n❌ 單月爬蟲測試失敗")
        return
    
    # 3. 測試完整更新流程
    print(f"\n⚠️ 準備測試完整更新流程")
    print(f"   這將修改 monthly_report.pkl 檔案")
    print(f"   (已自動備份，可以安全進行)")
    
    update_success = test_auto_update_integration()
    
    # 4. 驗證資料完整性
    if update_success:
        integrity_ok = verify_data_integrity()
        
        if integrity_ok:
            print(f"\n🎉 月營收更新流程測試完全成功！")
            print(f"✅ 爬蟲可以直接更新到 monthly_report.pkl")
            print(f"✅ 資料格式正確")
            print(f"✅ 資料完整性良好")
            print(f"✅ 更新流程正常")
        else:
            print(f"\n⚠️ 更新成功但資料完整性有問題")
    else:
        print(f"\n❌ 更新流程測試失敗")

if __name__ == "__main__":
    main()
