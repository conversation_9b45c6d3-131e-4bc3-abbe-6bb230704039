#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GoodInfo 月營收資料下載器
使用Selenium模擬點擊網站的"匯出XLS"功能
基於ROE下載器的實現方式
"""

import os
import time
import pandas as pd
import sqlite3
from datetime import datetime
import logging
import glob
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class GoodinfoMonthlyRevenueDownloader:
    def __init__(self):
        self.base_url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp"
        self.download_dir = "D:/Finlab/history/tables/monthly_revenue"
        self.db_path = os.path.join(self.download_dir, "monthly_revenue.db")
        
        # 確保下載目錄存在
        os.makedirs(self.download_dir, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化數據庫
        self.init_database()
        
    def init_database(self):
        """初始化數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,                    -- 單月營收(億)
                    revenue_mom REAL,               -- 月增率(%)
                    revenue_yoy REAL,               -- 年增率(%)
                    cumulative_revenue REAL,        -- 累計營收(億)
                    cumulative_yoy REAL,            -- 累計年增率(%)
                    
                    -- 股價資訊
                    open_price REAL,                -- 開盤價
                    close_price REAL,               -- 收盤價
                    high_price REAL,                -- 最高價
                    low_price REAL,                 -- 最低價
                    price_change REAL,              -- 漲跌(元)
                    price_change_pct REAL,          -- 漲跌(%)
                    
                    data_source TEXT DEFAULT 'goodinfo_excel',
                    crawl_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_year_month ON monthly_revenue(stock_id, year, month)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crawl_date ON monthly_revenue(crawl_date)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 月營收數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
    
    def setup_driver(self):
        """設置Chrome瀏覽器驅動"""
        try:
            options = Options()
            
            # 設置下載目錄
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            options.add_experimental_option("prefs", prefs)
            
            # 基本選項
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 啟用無頭模式 - 不顯示瀏覽器視窗
            options.add_argument('--headless')

            # 優化選項
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript-harmony-shipping')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-web-security')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--no-first-run')
            options.add_argument('--safebrowsing-disable-auto-update')
            options.add_argument('--enable-automation')
            options.add_argument('--password-store=basic')
            options.add_argument('--use-mock-keychain')

            # 減少警告
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-component-extensions-with-background-pages')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-logging')
            options.add_argument('--disable-notifications')
            options.add_argument('--log-level=3')
            options.add_argument('--silent')

            # 禁用Google服務
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)
            
            driver = webdriver.Chrome(options=options)
            self.logger.info("🔧 Chrome瀏覽器已啟動 (無頭模式)")
            return driver

        except Exception as e:
            self.logger.error(f"❌ Chrome瀏覽器啟動失敗: {e}")
            return None

    def setup_simple_driver(self):
        """設置簡化版瀏覽器驅動（增強版，基於參考程式碼）"""
        try:
            options = Options()

            # 使用用戶的默認下載目錄，避免權限問題
            username = os.getenv('USERNAME')
            default_download_dir = f"C:/Users/<USER>/Downloads"

            # 設置下載目錄（基於參考程式碼的配置）
            prefs = {
                "download.default_directory": default_download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,  # 保持安全瀏覽，但不阻擋下載
                # 禁用各種通知和彈窗
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.default_content_setting_values.automatic_downloads": 1
            }
            options.add_experimental_option("prefs", prefs)

            # 基本設定（基於參考程式碼）
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 可選：無頭模式（參考程式碼建議）
            options.add_argument('--headless=new')  # 使用新版無頭模式（不開啟瀏覽器視窗）

            # GPU 和 WebGL 相關優化（消除錯誤訊息）
            options.add_argument('--disable-webgl')
            options.add_argument('--disable-webgl2')
            options.add_argument('--disable-3d-apis')
            options.add_argument('--disable-accelerated-2d-canvas')
            options.add_argument('--disable-gpu-sandbox')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--disable-angle-features=d3d11')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-dev-tools')
            options.add_argument('--disable-crash-reporter')

            # 隱藏自動測試軟體控制提示和其他干擾
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--disable-extensions')

            # 禁用各種通知和彈窗
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')

            # 減少錯誤訊息
            options.add_argument('--log-level=3')
            options.add_argument('--silent')
            options.add_argument('--disable-logging')

            # 設置ChromeDriver路徑（如果需要）
            driver_path = self.find_chromedriver_path()
            if driver_path:
                self.logger.info(f"🔧 使用ChromeDriver: {driver_path}")
                driver = webdriver.Chrome(executable_path=driver_path, options=options)
            else:
                driver = webdriver.Chrome(options=options)

            # 執行JavaScript來隱藏自動化檢測
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.logger.info(f"🔧 Chrome瀏覽器已啟動 (增強模式，無頭模式)")

            # 記錄實際的下載目錄
            self.actual_download_dir = default_download_dir
            self.logger.info(f"📁 實際下載目錄: {self.actual_download_dir}")

            return driver

        except Exception as e:
            self.logger.error(f"❌ Chrome瀏覽器啟動失敗: {e}")
            return None

    def find_chromedriver_path(self):
        """尋找ChromeDriver路徑"""
        try:
            # 常見的ChromeDriver路徑
            possible_paths = [
                'chromedriver.exe',
                'chromedriver',
                '/usr/local/bin/chromedriver',
                '/usr/bin/chromedriver',
                'C:/chromedriver/chromedriver.exe',
                'D:/chromedriver/chromedriver.exe'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    return path

            return None
        except:
            return None

    def handle_ads_and_popups(self, driver):
        """處理廣告和彈窗（改進版本）"""
        try:
            self.logger.info("🚫 處理廣告和彈窗...")

            # 處理alert彈窗
            try:
                from selenium.common.exceptions import NoAlertPresentException, TimeoutException
                alert = driver.switch_to.alert
                alert.dismiss()
                self.logger.info("✅ 已處理alert彈窗")
            except NoAlertPresentException:
                self.logger.info("📝 無alert彈窗")
            except TimeoutException:
                self.logger.info("⚠️ 處理alert彈窗逾時")
            except:
                pass

            # 隱藏Google廣告iframe（更積極的方法）
            ad_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@id, 'google_ads_iframe')]")
            for iframe in ad_iframes:
                try:
                    driver.execute_script("""
                        arguments[0].style.display = 'none';
                        arguments[0].style.visibility = 'hidden';
                        arguments[0].style.width = '0px';
                        arguments[0].style.height = '0px';
                        arguments[0].remove();
                    """, iframe)
                    self.logger.info("🚫 移除Google廣告iframe")
                except:
                    pass

            # 隱藏其他廣告容器
            ad_selectors = [
                "#ats-interstitial-container",
                ".ad-container",
                ".advertisement",
                "[id*='google_ads']",
                "[class*='google-ads']",
                "div[style*='position: fixed']",  # 可能的彈窗
                "div[style*='z-index']"  # 高層級元素
            ]

            for selector in ad_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            driver.execute_script("""
                                arguments[0].style.display = 'none';
                                arguments[0].style.visibility = 'hidden';
                                arguments[0].remove();
                            """, element)
                            self.logger.info(f"🚫 移除廣告元素: {selector}")
                except:
                    pass

            # 等待一下讓頁面穩定
            time.sleep(3)

        except Exception as e:
            self.logger.warning(f"⚠️ 處理廣告時出錯: {e}")

    def find_export_button_simple(self, driver):
        """簡化版按鈕尋找方法（基於測試成功的版本）"""
        try:
            # 列出所有input元素並尋找XLS按鈕
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            self.logger.info(f"📋 找到 {len(all_inputs)} 個input元素")

            for i, input_elem in enumerate(all_inputs):
                try:
                    input_type = input_elem.get_attribute("type")
                    input_value = input_elem.get_attribute("value")
                    input_onclick = input_elem.get_attribute("onclick")

                    # 詳細日誌輸出
                    self.logger.info(f"   Input {i+1}: type='{input_type}', value='{input_value}'")
                    if input_onclick:
                        self.logger.info(f"            onclick='{input_onclick[:100]}...'")

                    # 尋找XLS相關按鈕
                    if input_value and ("XLS" in input_value or "匯出" in input_value):
                        if input_elem.is_displayed() and input_elem.is_enabled():
                            self.logger.info(f"✅ 找到匯出按鈕: {input_value}")
                            return input_elem
                        else:
                            self.logger.warning(f"⚠️ 找到按鈕但不可用: {input_value} (displayed={input_elem.is_displayed()}, enabled={input_elem.is_enabled()})")

                except Exception as e:
                    self.logger.warning(f"   Input {i+1}: 讀取失敗 - {e}")
                    continue

            # 如果上面的方法失敗，嘗試XPath選擇器
            self.logger.info("🔍 嘗試XPath選擇器...")
            selectors = [
                '//input[@value="匯出 XLS"]',
                '//input[@value="XLS"]',
                '//input[contains(@value, "XLS")]',
                '//input[contains(@value, "匯出")]',
                '//button[contains(text(), "XLS")]'
            ]

            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    self.logger.info(f"   選擇器 {selector}: 找到 {len(elements)} 個元素")
                    if elements:
                        button = elements[0]
                        if button.is_displayed() and button.is_enabled():
                            self.logger.info(f"✅ 通過XPath找到按鈕: {selector}")
                            return button
                        else:
                            self.logger.warning(f"⚠️ XPath按鈕不可用: {selector} (displayed={button.is_displayed()}, enabled={button.is_enabled()})")
                except Exception as e:
                    self.logger.warning(f"   選擇器失敗: {selector} - {e}")
                    continue

            return None

        except Exception as e:
            self.logger.error(f"❌ 尋找匯出按鈕時發生錯誤: {e}")
            return None

    def set_date_range(self, driver, start_date, end_date):
        """設定日期範圍篩選（增強版，基於參考程式碼）"""
        try:
            self.logger.info(f"📅 設定日期範圍: {start_date} 到 {end_date}")

            # 處理可能的彈出視窗
            try:
                alert = driver.switch_to.alert
                alert.dismiss()  # 關閉彈出視窗
                self.logger.info("已處理彈出視窗")
            except:
                self.logger.info("無彈出視窗")

            # 方法1: 尋找下拉選單 (ID為 SEL_S_YM 和 SEL_E_YM)
            try:
                from selenium.webdriver.support.ui import Select

                # 起始年月下拉選單
                start_select_element = driver.find_element(By.ID, "SEL_S_YM")
                start_select = Select(start_select_element)
                start_select.select_by_value(start_date.replace('-', '/'))  # 轉換格式 2024-01 -> 2024/01
                self.logger.info(f"✅ 設定起始年月: {start_date}")

                # 結束年月下拉選單
                end_select_element = driver.find_element(By.ID, "SEL_E_YM")
                end_select = Select(end_select_element)
                end_select.select_by_value(end_date.replace('-', '/'))  # 轉換格式 2024-12 -> 2024/12
                self.logger.info(f"✅ 設定結束年月: {end_date}")

                # 點擊查詢按鈕
                query_button = driver.find_element(By.ID, "btnQuery")
                query_button.click()
                self.logger.info("✅ 點擊查詢按鈕")
                time.sleep(5)  # 等待資料刷新

                return True

            except Exception as e:
                self.logger.warning(f"⚠️ 下拉選單方法失敗: {e}")

            # 方法2: 尋找月份類型輸入框
            date_inputs = driver.find_elements(By.XPATH, "//input[@type='month']")

            if len(date_inputs) >= 2:
                # 設定開始日期
                start_input = date_inputs[0]
                start_input.clear()
                start_input.send_keys(start_date)

                # 設定結束日期
                end_input = date_inputs[1]
                end_input.clear()
                end_input.send_keys(end_date)

                # 處理廣告後再點擊查詢按鈕
                self.handle_ads_and_popups(driver)

                # 點擊查詢按鈕（使用多種方法）
                query_success = False
                query_methods = [
                    ("JavaScript執行", lambda: driver.execute_script("ReloadDetail();")),
                    ("直接點擊", lambda: driver.find_element(By.XPATH, "//input[@value='查詢']").click()),
                    ("JavaScript點擊", lambda: driver.execute_script("arguments[0].click();",
                                                                   driver.find_element(By.XPATH, "//input[@value='查詢']")))
                ]

                for method_name, method_func in query_methods:
                    try:
                        self.logger.info(f"嘗試 {method_name}...")
                        method_func()
                        query_success = True
                        self.logger.info(f"✅ {method_name} 成功")
                        break
                    except Exception as e:
                        self.logger.warning(f"⚠️ {method_name} 失敗: {e}")

                if query_success:
                    time.sleep(5)  # 等待查詢結果
                    self.logger.info("✅ 日期範圍設定完成")
                    return True
                else:
                    self.logger.warning("⚠️ 所有查詢方法都失敗")

            else:
                self.logger.warning("⚠️ 未找到日期輸入框")

            return False

        except Exception as e:
            self.logger.warning(f"⚠️ 設定日期範圍失敗: {e}")
            # 繼續執行，不中斷下載流程
            return False
    
    def download_stock_revenue(self, stock_id, start_date=None, end_date=None):
        """下載單一股票的月營收資料

        Args:
            stock_id: 股票代號
            start_date: 開始日期 (格式: YYYY-MM，如: 2022-01)
            end_date: 結束日期 (格式: YYYY-MM，如: 2025-07)
        """
        # 方法1: 嘗試Selenium下載Excel
        result = self.download_with_selenium(stock_id, start_date, end_date)
        if result:
            return result

        # 方法2: 備用網頁抓取方法
        self.logger.info(f"🔄 Selenium下載失敗，嘗試網頁抓取方法...")
        return self.download_with_scraper(stock_id)

    def download_with_selenium(self, stock_id, start_date=None, end_date=None):
        """使用Selenium下載Excel文件（基於測試成功的簡化版本）"""
        driver = None
        try:
            self.logger.info(f"🚀 使用Selenium下載 {stock_id} 月營收資料...")

            # 設置瀏覽器（簡化版本）
            driver = self.setup_simple_driver()
            if not driver:
                return None

            # 構建URL
            url = f"{self.base_url}?STOCK_ID={stock_id}"
            self.logger.info(f"📱 訪問頁面: {url}")

            driver.get(url)

            # 等待頁面載入（改進版本）
            self.logger.info("⏳ 等待頁面初始化...")

            # 等待頁面不再顯示"初始化中"
            max_wait_time = 30
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                try:
                    page_source = driver.page_source
                    if "初始化中" not in page_source and "請稍候" not in page_source:
                        self.logger.info("✅ 頁面初始化完成")
                        break
                    time.sleep(2)
                except:
                    time.sleep(2)
            else:
                self.logger.warning("⚠️ 頁面初始化超時，繼續嘗試...")

            # 額外等待確保頁面完全載入
            time.sleep(5)

            # 處理廣告和彈窗
            self.handle_ads_and_popups(driver)

            # 檢查頁面標題並提取股票名稱
            title = driver.title
            self.logger.info(f"📄 頁面標題: {title}")

            # 從標題中提取股票名稱 (格式: "2330 台積電 - 單月營收 - Goodinfo!台灣股市資訊網")
            stock_name = "未知"
            try:
                if " - " in title:
                    title_parts = title.split(" - ")[0].strip()  # 取第一部分
                    if " " in title_parts:
                        parts = title_parts.split(" ", 1)  # 分割成代號和名稱
                        if len(parts) >= 2 and parts[0] == stock_id:
                            stock_name = parts[1].strip()
                            self.logger.info(f"📊 股票名稱: {stock_name}")
            except Exception as e:
                self.logger.warning(f"⚠️ 提取股票名稱失敗: {e}")

            # 檢查頁面內容
            page_source = driver.page_source
            if "營收" in page_source or "月營收" in page_source:
                self.logger.info("✅ 頁面包含營收相關內容")
            else:
                self.logger.warning("⚠️ 頁面可能不包含營收內容")

            # 如果有日期範圍，設定日期篩選
            if start_date and end_date:
                self.set_date_range(driver, start_date, end_date)

            # 尋找匯出按鈕（使用測試成功的方法）
            export_button = self.find_export_button_simple(driver)
            if not export_button:
                self.logger.warning("⚠️ 未找到匯出按鈕")
                return None

            # 點擊匯出按鈕（增強版本，處理攔截問題）
            self.logger.info("📥 點擊匯出按鈕...")

            # 先嘗試關閉可能的彈窗或廣告
            try:
                # 查找並關閉可能的廣告容器
                ad_containers = driver.find_elements(By.ID, "ats-interstitial-container")
                for container in ad_containers:
                    if container.is_displayed():
                        driver.execute_script("arguments[0].style.display = 'none';", container)
                        self.logger.info("🚫 隱藏廣告容器")
            except:
                pass

            # 滾動到按鈕位置
            try:
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", export_button)
                time.sleep(1)
            except:
                pass

            # 嘗試多種點擊方法
            click_success = False

            # 方法1: 直接執行onclick事件
            try:
                onclick_code = export_button.get_attribute("onclick")
                if onclick_code:
                    self.logger.info("🔄 方法1: 直接執行onclick事件...")
                    driver.execute_script(onclick_code)
                    self.logger.info("✅ onclick事件執行成功")
                    click_success = True
            except Exception as e:
                self.logger.warning(f"⚠️ onclick事件執行失敗: {e}")

            # 方法2: JavaScript點擊
            if not click_success:
                try:
                    self.logger.info("🔄 方法2: JavaScript點擊...")
                    driver.execute_script("arguments[0].click();", export_button)
                    self.logger.info("✅ JavaScript點擊成功")
                    click_success = True
                except Exception as e:
                    self.logger.warning(f"⚠️ JavaScript點擊失敗: {e}")

            # 方法3: 直接點擊
            if not click_success:
                try:
                    self.logger.info("🔄 方法3: 直接點擊...")
                    export_button.click()
                    self.logger.info("✅ 直接點擊成功")
                    click_success = True
                except Exception as e:
                    self.logger.warning(f"⚠️ 直接點擊失敗: {e}")

            if not click_success:
                self.logger.error("❌ 所有點擊方法都失敗")
                return None

            # 等待一下讓頁面響應
            time.sleep(3)

            # 檢查是否有彈窗或警告
            try:
                # 檢查是否有alert對話框
                alert = driver.switch_to.alert
                alert_text = alert.text
                self.logger.info(f"⚠️ 發現彈窗: {alert_text}")
                alert.accept()  # 點擊確定
                self.logger.info("✅ 已處理彈窗")
                time.sleep(2)
            except:
                self.logger.info("📝 沒有發現alert彈窗")

            # 檢查是否有其他類型的彈窗或對話框
            try:
                # 檢查是否有模態對話框
                modal_elements = driver.find_elements(By.CSS_SELECTOR, ".modal, .dialog, .popup")
                if modal_elements:
                    self.logger.info(f"🔍 發現 {len(modal_elements)} 個模態對話框")
                    for modal in modal_elements:
                        if modal.is_displayed():
                            self.logger.info("📋 處理模態對話框...")
                            # 嘗試找到確定或關閉按鈕
                            buttons = modal.find_elements(By.TAG_NAME, "button")
                            for button in buttons:
                                if button.text in ["確定", "OK", "下載", "匯出"]:
                                    button.click()
                                    self.logger.info(f"✅ 點擊了按鈕: {button.text}")
                                    time.sleep(2)
                                    break
            except Exception as e:
                self.logger.info(f"📝 檢查模態對話框時出錯: {e}")

            # 等待下載完成（傳遞股票名稱和日期區間，增加超時時間）
            downloaded_file = self.wait_for_download(stock_id, stock_name=stock_name,
                                                   start_date=start_date, end_date=end_date, timeout=60)

            if downloaded_file:
                self.logger.info(f"✅ Excel 下載成功: {downloaded_file}")

                # 解析並儲存數據
                revenue_data = self.parse_excel_file(downloaded_file, stock_id)
                if revenue_data:
                    if self.save_to_database(revenue_data):
                        self.logger.info(f"✅ {stock_id} 月營收數據已儲存到數據庫")

                        # 清理下載的文件
                        try:
                            os.remove(downloaded_file)
                        except:
                            pass

                        return len(revenue_data)

            return None

        except Exception as e:
            self.logger.error(f"❌ Selenium下載失敗: {e}")
            return None

        finally:
            if driver:
                driver.quit()

    def download_with_scraper(self, stock_id):
        """使用網頁抓取方式下載月營收資料（備用方案）"""
        try:
            import requests
            from bs4 import BeautifulSoup

            self.logger.info(f"🌐 使用網頁抓取方式下載 {stock_id} 月營收資料...")

            # 設置請求會話
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })

            # 構建URL
            url = f"{self.base_url}?STOCK_ID={stock_id}"
            self.logger.info(f"📱 訪問頁面: {url}")

            # 隨機延遲
            time.sleep(2)

            # 發送請求
            response = session.get(url, timeout=30)
            response.encoding = 'utf-8'

            if response.status_code != 200:
                self.logger.error(f"❌ 請求失敗，狀態碼: {response.status_code}")
                return None

            # 檢查是否包含營收數據
            if '營收' not in response.text and '月營收' not in response.text:
                self.logger.error("❌ 頁面不包含營收數據")
                return None

            # 使用pandas讀取表格
            try:
                tables = pd.read_html(response.text)
                self.logger.info(f"✅ 找到 {len(tables)} 個表格")
            except Exception as e:
                self.logger.error(f"❌ 無法解析表格: {e}")
                return None

            # 尋找月營收表格
            revenue_data = self.parse_revenue_tables_scraper(tables, stock_id)

            if revenue_data:
                self.logger.info(f"✅ 成功解析 {len(revenue_data)} 筆月營收數據")

                # 儲存到數據庫
                if self.save_to_database(revenue_data):
                    return len(revenue_data)

            return None

        except Exception as e:
            self.logger.error(f"❌ 網頁抓取失敗: {e}")
            return None
    
    def find_export_button(self, driver):
        """尋找匯出按鈕"""
        try:
            # 基於您提供的HTML代碼，精確定位XLS匯出按鈕
            selectors = [
                # 精確匹配：onclick包含export2xls的按鈕
                "//input[@type='button'][@value='XLS'][contains(@onclick, 'export2xls')]",
                # 備用：任何值為XLS的input按鈕
                "//input[@type='button'][@value='XLS']",
                # 備用：包含XLS的input
                "//input[contains(@value, 'XLS')]",
                # 更寬泛的匹配
                "//input[@onclick and contains(@onclick, 'export2xls')]"
            ]

            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        self.logger.info(f"✅ 找到匯出按鈕: {selector}")
                        # 驗證按鈕是否可見和可點擊
                        button = elements[0]
                        if button.is_displayed() and button.is_enabled():
                            return button
                        else:
                            self.logger.warning(f"⚠️ 按鈕找到但不可點擊: {selector}")
                except Exception as e:
                    self.logger.debug(f"選擇器失敗: {selector}, 錯誤: {e}")
                    continue

            # 如果精確匹配失敗，嘗試通過JavaScript執行
            try:
                self.logger.info("🔄 嘗試通過JavaScript直接執行匯出...")
                # 直接執行export2xls函數
                driver.execute_script("""
                    var divDetail = document.getElementById('divDetail');
                    if (divDetail && typeof export2xls === 'function') {
                        export2xls(divDetail.innerHTML, 'SaleMonDetail.xls');
                        return true;
                    }
                    return false;
                """)
                self.logger.info("✅ JavaScript匯出執行成功")
                return "javascript_executed"  # 特殊標記
            except Exception as e:
                self.logger.warning(f"⚠️ JavaScript匯出失敗: {e}")

            # 最後嘗試：查找所有input按鈕並檢查onclick屬性
            try:
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                for input_elem in all_inputs:
                    try:
                        value = input_elem.get_attribute("value")
                        onclick = input_elem.get_attribute("onclick")

                        if (value and "XLS" in value.upper()) or (onclick and "export2xls" in onclick):
                            self.logger.info(f"✅ 通過屬性檢查找到匯出按鈕: value={value}, onclick={onclick}")
                            return input_elem
                    except:
                        continue
            except:
                pass

            self.logger.error("❌ 所有方法都無法找到匯出按鈕")
            return None

        except Exception as e:
            self.logger.error(f"❌ 尋找匯出按鈕失敗: {e}")
            return None
    
    def wait_for_download(self, stock_id, stock_name="未知", start_date=None, end_date=None, timeout=90):
        """等待下載完成（改進版，支持多目錄監控、股票名稱和日期區間）"""
        try:
            start_time = time.time()

            # 監控多個可能的下載位置
            download_paths = [
                getattr(self, 'actual_download_dir', self.download_dir),  # 實際設置的下載目錄
                self.download_dir,  # 原始設置的下載目錄
                f"C:/Users/<USER>'USERNAME')}/Downloads",  # 用戶默認下載目錄
            ]

            # 去重並確保目錄存在
            download_paths = list(set([path for path in download_paths if os.path.exists(path)]))

            initial_files_dict = {}
            for path in download_paths:
                initial_files_dict[path] = set(glob.glob(os.path.join(path, "*.*")))
                self.logger.info(f"📂 監控下載目錄: {path} (初始文件: {len(initial_files_dict[path])})")

            while time.time() - start_time < timeout:
                for download_path in download_paths:
                    current_files = set(glob.glob(os.path.join(download_path, "*.*")))
                    initial_files = initial_files_dict[download_path]
                    new_files = current_files - initial_files

                    # 如果沒有新文件，檢查現有的最近文件
                    if not new_files:
                        excel_files = [f for f in current_files if f.lower().endswith(('.xls', '.xlsx'))]
                        self.logger.info(f"🔍 在 {download_path} 檢查 {len(excel_files)} 個Excel文件")

                        for excel_file in excel_files:
                            filename = os.path.basename(excel_file)
                            filename_lower = filename.lower()

                            if (filename_lower.startswith('salemondetail') or
                                'salemondetail' in filename_lower):
                                # 檢查文件是否在下載開始後創建/修改
                                file_ctime = os.path.getctime(excel_file)
                                file_mtime = os.path.getmtime(excel_file)
                                recent_time = max(file_ctime, file_mtime)

                                time_diff = time.time() - recent_time
                                self.logger.info(f"🔍 檢查文件: {filename}, 時間差: {time_diff:.1f}秒")

                                # 如果文件很新（5分鐘內），認為是剛下載的
                                if time_diff < 300:  # 5分鐘
                                    self.logger.info(f"🔍 檢測到最近的Excel文件: {filename}")
                                    new_files = {excel_file}
                                    break

                    # 檢查是否有新文件
                    if new_files:
                        for new_file in new_files:
                            filename = os.path.basename(new_file)
                            self.logger.info(f"🆕 在 {download_path} 發現新文件: {filename}")

                            # 詳細檢查文件名匹配邏輯
                            filename_lower = filename.lower()
                            is_excel = new_file.lower().endswith(('.xls', '.xlsx'))
                            has_salemondetail = 'salemondetail' in filename_lower
                            has_monthly = 'monthly' in filename_lower
                            has_revenue = 'revenue' in filename_lower
                            starts_with_salemondetail = filename_lower.startswith('salemondetail')
                            has_salemondetail_bracket = 'salemondetail (' in filename_lower

                            self.logger.info(f"   文件名檢查: Excel={is_excel}, SaleMonDetail={has_salemondetail}, "
                                           f"Monthly={has_monthly}, Revenue={has_revenue}, "
                                           f"StartsWith={starts_with_salemondetail}, Bracket={has_salemondetail_bracket}")

                            match_criteria = (is_excel and (has_salemondetail or has_monthly or has_revenue or
                                            starts_with_salemondetail or has_salemondetail_bracket))
                            self.logger.info(f"   匹配結果: {match_criteria}")

                            # 檢查是否為Excel文件或GoodInfo的特定文件名
                            filename = os.path.basename(new_file).lower()
                            if (new_file.lower().endswith(('.xls', '.xlsx')) and
                                ('salemondetail' in filename or 'monthly' in filename or 'revenue' in filename or
                                 filename.startswith('salemondetail') or
                                 # 處理帶括號的重複下載文件，如 SaleMonDetail (1).xls
                                 'salemondetail (' in filename)):
                                # 等待文件穩定
                                time.sleep(2)
                                if os.path.exists(new_file) and os.path.getsize(new_file) > 0:
                                    # 複製文件到目標目錄（包含股票名稱和日期區間）
                                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                    # 清理股票名稱中的特殊字符
                                    clean_stock_name = "".join(c for c in stock_name if c.isalnum() or c in "._-")

                                    # 構建包含日期區間的檔名
                                    date_range = ""
                                    if start_date and end_date:
                                        date_range = f"_{start_date}_to_{end_date}"
                                    elif start_date:
                                        date_range = f"_from_{start_date}"
                                    elif end_date:
                                        date_range = f"_to_{end_date}"

                                    new_filename = f"{stock_id}_{clean_stock_name}_monthly_revenue{date_range}_{timestamp}.xls"
                                    target_path = os.path.join(self.download_dir, new_filename)

                                    try:
                                        import shutil
                                        shutil.copy2(new_file, target_path)
                                        self.logger.info(f"✅ 下載完成並複製: {new_filename}")
                                        return target_path
                                    except Exception as e:
                                        self.logger.warning(f"⚠️ 複製文件失敗: {e}")
                                        self.logger.info(f"✅ 下載完成: {os.path.basename(new_file)}")
                                        return new_file

                            # 檢查是否為臨時下載文件
                            elif new_file.lower().endswith(('.crdownload', '.tmp', '.part')):
                                self.logger.info(f"⏳ 檢測到下載中的文件: {os.path.basename(new_file)}")

                    # 檢查現有Excel文件是否有更新
                    excel_files = [f for f in current_files if f.lower().endswith(('.xls', '.xlsx'))]
                    # 進一步篩選出營收相關的文件
                    revenue_files = []
                    for f in excel_files:
                        filename_lower = os.path.basename(f).lower()
                        if (any(keyword in filename_lower for keyword in ['salemondetail', 'monthly', 'revenue']) or
                            filename_lower.startswith('salemondetail') or
                            'salemondetail (' in filename_lower):  # 處理重複下載文件
                            revenue_files.append(f)
                    for excel_file in revenue_files:
                        # 檢查文件創建時間或修改時間
                        file_ctime = os.path.getctime(excel_file)
                        file_mtime = os.path.getmtime(excel_file)
                        recent_time = max(file_ctime, file_mtime)

                        if recent_time > start_time and os.path.getsize(excel_file) > 0:
                            # 複製文件到目標目錄（包含股票名稱和日期區間）
                            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                            # 清理股票名稱中的特殊字符
                            clean_stock_name = "".join(c for c in stock_name if c.isalnum() or c in "._-")

                            # 構建包含日期區間的檔名
                            date_range = ""
                            if start_date and end_date:
                                date_range = f"_{start_date}_to_{end_date}"
                            elif start_date:
                                date_range = f"_from_{start_date}"
                            elif end_date:
                                date_range = f"_to_{end_date}"

                            new_filename = f"{stock_id}_{clean_stock_name}_monthly_revenue{date_range}_{timestamp}.xls"
                            target_path = os.path.join(self.download_dir, new_filename)

                            try:
                                import shutil
                                shutil.copy2(excel_file, target_path)
                                self.logger.info(f"✅ 檢測到更新文件並複製: {new_filename}")
                                return target_path
                            except Exception as e:
                                self.logger.warning(f"⚠️ 複製文件失敗: {e}")
                                self.logger.info(f"✅ 檢測到更新文件: {os.path.basename(excel_file)}")
                                return excel_file

                time.sleep(2)

            self.logger.warning(f"⚠️ 下載超時 ({timeout}秒)")
            return None

        except Exception as e:
            self.logger.error(f"❌ 等待下載失敗: {e}")
            return None

    def parse_excel_file(self, excel_file, stock_id):
        """解析 Excel 文件 (修復版，支援GoodInfo實際格式)"""
        try:
            self.logger.info(f"📊 解析 Excel 文件: {excel_file}")

            # 使用正確的Excel讀取方法
            df = pd.read_excel(excel_file, header=None)
            self.logger.info(f"📋 Excel 文件包含 {len(df)} 行, {len(df.columns)} 列")

            # 檢查文件格式
            if len(df) > 0:
                first_row = [str(cell) for cell in df.iloc[0]]
                self.logger.info(f"📄 第一行內容: {first_row}")

                # 檢查是否為全股票格式
                if '公司代號' in first_row and '公司名稱' in first_row:
                    self.logger.info("📊 檢測到全股票營收格式")
                    return self.parse_all_stocks_format(df, stock_id)
                else:
                    self.logger.info("📊 檢測到單股票歷史格式")
                    return self.parse_single_stock_format(df, stock_id)

            self.logger.error("❌ Excel文件為空")
            return None

            # 從數據起始行開始解析
            for index in range(data_start_row, len(df)):
                try:
                    row = df.iloc[index]

                    # 解析月別（第一列）
                    month_str = str(row.iloc[0]).strip()
                    if pd.isna(month_str) or month_str == 'nan' or month_str == '':
                        continue

                    year, month = self.parse_month_string(month_str)
                    if not year or not month:
                        self.logger.debug(f"⚠️ 跳過無效月份: {month_str}")
                        continue

                    # 解析數據（基於實際Excel結構）
                    record = {
                        'stock_id': stock_id,
                        'stock_name': stock_name,
                        'year': year,
                        'month': month,

                        # 股價資訊 (列1-6)
                        'open_price': self.safe_float(row.iloc[1]),      # 開盤
                        'close_price': self.safe_float(row.iloc[2]),     # 收盤
                        'high_price': self.safe_float(row.iloc[3]),      # 最高
                        'low_price': self.safe_float(row.iloc[4]),       # 最低
                        'price_change': self.safe_float(row.iloc[5]),    # 漲跌(元)
                        'price_change_pct': self.safe_float(row.iloc[6]), # 漲跌(%)

                        # 營收資訊 (列7-11，基於實際結構調整)
                        'revenue': self.safe_float(row.iloc[7]),         # 單月營收(億)
                        'revenue_mom': self.safe_float(row.iloc[8]),     # 月增(%)
                        'revenue_yoy': self.safe_float(row.iloc[9]),     # 年增(%)
                        'cumulative_revenue': self.safe_float(row.iloc[10]), # 累計營收(億)
                        'cumulative_yoy': self.safe_float(row.iloc[11]) if len(row) > 11 else None, # 累計年增(%)

                        'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    revenue_records.append(record)
                    self.logger.debug(f"✅ 解析成功: {year}/{month:02d} - 營收: {record['revenue']}")

                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue

            self.logger.info(f"✅ 成功解析 {len(revenue_records)} 筆月營收數據")
            return revenue_records if revenue_records else None

        except Exception as e:
            self.logger.error(f"❌ 解析 Excel 文件失敗: {e}")
            return None

    def parse_all_stocks_format(self, df, target_stock_id):
        """解析全股票格式的Excel文件"""
        try:
            self.logger.info(f"📊 解析全股票格式，尋找股票 {target_stock_id}")

            # 假設第一行是標題
            if len(df) < 2:
                self.logger.error("❌ 文件內容不足")
                return None

            # 獲取當前日期作為營收月份
            from datetime import datetime
            current_date = datetime.now()
            year = current_date.year
            month = current_date.month

            # 如果是月初，可能是上個月的資料
            if current_date.day <= 10:
                if month == 1:
                    year -= 1
                    month = 12
                else:
                    month -= 1

            self.logger.info(f"📅 推測營收月份: {year}/{month:02d}")

            # 尋找目標股票
            target_found = False
            for index in range(1, len(df)):  # 跳過標題行
                try:
                    row = df.iloc[index]
                    stock_id = str(row.iloc[0]).strip()

                    if stock_id == target_stock_id:
                        target_found = True
                        stock_name = str(row.iloc[1]).strip()

                        # 解析營收數據
                        current_revenue = self.safe_float_convert(row.iloc[2])  # 當月營收
                        last_month_revenue = self.safe_float_convert(row.iloc[3])  # 上月營收
                        last_year_revenue = self.safe_float_convert(row.iloc[4])  # 去年當月營收
                        mom_change = self.safe_float_convert(row.iloc[5])  # 月增率

                        # 計算年增率
                        yoy_change = None
                        if current_revenue and last_year_revenue and last_year_revenue != 0:
                            yoy_change = ((current_revenue - last_year_revenue) / last_year_revenue) * 100

                        revenue_record = {
                            'stock_id': target_stock_id,
                            'stock_name': stock_name,
                            'year': year,
                            'month': month,
                            'revenue': current_revenue,
                            'revenue_mom': mom_change,
                            'revenue_yoy': yoy_change,
                            'cumulative_revenue': current_revenue,  # 單月資料，累計就是當月
                            'cumulative_yoy': yoy_change,
                            'data_source': 'GoodInfo_AllStocks'
                        }

                        self.logger.info(f"✅ 找到 {target_stock_id} {stock_name} 營收資料")
                        self.logger.info(f"📊 {year}/{month:02d} 營收: {current_revenue:,} (月增: {mom_change}%, 年增: {yoy_change:.2f}%)")

                        return [revenue_record]

                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue

            if not target_found:
                self.logger.warning(f"⚠️ 未找到股票 {target_stock_id} 的資料")
                return None

        except Exception as e:
            self.logger.error(f"❌ 解析全股票格式失敗: {e}")
            return None

    def parse_single_stock_format(self, df, stock_id):
        """解析單股票歷史格式的Excel文件（原有邏輯）"""
        try:
            self.logger.info(f"📊 解析單股票歷史格式")

            # 獲取股票名稱
            stock_name = self.get_stock_name(stock_id)
            revenue_records = []

            # 尋找數據起始行（跳過標題行）
            data_start_row = self.find_data_start_row(df)
            self.logger.info(f"📍 數據起始行: {data_start_row}")

            if data_start_row is None:
                self.logger.error("❌ 未找到數據起始行")
                return None

            # 從數據起始行開始解析（原有邏輯）
            # ... 這裡保留原有的單股票歷史解析邏輯

            return revenue_records

        except Exception as e:
            self.logger.error(f"❌ 解析單股票格式失敗: {e}")
            return None

    def safe_float_convert(self, value):
        """安全地轉換為浮點數"""
        try:
            if pd.isna(value) or value == '' or value == 'nan':
                return None

            # 移除千分位逗號和其他非數字字符
            str_value = str(value).replace(',', '').replace(' ', '')

            # 處理負號
            if str_value.startswith('-'):
                return -float(str_value[1:])

            return float(str_value)
        except:
            return None

    def find_data_start_row(self, df):
        """尋找數據起始行"""
        try:
            # 尋找包含年份格式的行 (如 2025/06)
            for index, row in df.iterrows():
                first_cell = str(row.iloc[0]).strip()

                # 檢查是否為年份/月份格式
                if '/' in first_cell:
                    parts = first_cell.split('/')
                    if len(parts) == 2:
                        try:
                            year = int(parts[0])
                            month = int(parts[1])
                            if 2020 <= year <= 2030 and 1 <= month <= 12:
                                return index
                        except ValueError:
                            continue

            return None

        except Exception as e:
            self.logger.error(f"❌ 尋找數據起始行失敗: {e}")
            return None

    def parse_month_string(self, month_str):
        """解析月份字符串 (支援多種格式)"""
        try:
            month_str = str(month_str).strip()

            # 格式1: 2025/06 (GoodInfo實際格式)
            if '/' in month_str:
                parts = month_str.split('/')
                if len(parts) == 2:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        if 1 <= month <= 12:
                            return year, month
                    except ValueError:
                        pass

            # 格式2: Jun-25 (原始期望格式)
            if '-' in month_str:
                month_part, year_part = month_str.split('-')

                month_mapping = {
                    'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                    'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                }

                month = month_mapping.get(month_part)
                if month:
                    year = int(year_part)
                    # 處理兩位數年份
                    if year < 50:
                        year += 2000
                    elif year < 100:
                        year += 1900

                    return year, month

            # 格式3: 2025.06
            if '.' in month_str:
                parts = month_str.split('.')
                if len(parts) == 2:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        if 1 <= month <= 12:
                            return year, month
                    except ValueError:
                        pass

            return None, None

        except Exception as e:
            self.logger.warning(f"⚠️ 解析月份失敗: {month_str}, {e}")
            return None, None

    def safe_float(self, value):
        """安全轉換為浮點數 (支援GoodInfo格式)"""
        try:
            if pd.isna(value) or value == '' or str(value).strip() == '':
                return None

            # 轉換為字符串並清理
            cleaned = str(value).strip()

            # 移除常見的格式字符
            cleaned = cleaned.replace(',', '')  # 移除千分位逗號
            cleaned = cleaned.replace('+', '')  # 移除正號
            cleaned = cleaned.replace('%', '')  # 移除百分號
            cleaned = cleaned.replace('(', '-').replace(')', '')  # 處理負數括號格式

            # 處理空字符串或無效值
            if not cleaned or cleaned in ['-', 'nan', 'None', 'N/A']:
                return None

            return float(cleaned)

        except (ValueError, TypeError):
            return None

    def get_stock_name(self, stock_id):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
            '2412': '中華電', '2881': '富邦金', '2882': '國泰金',
            '2303': '聯電', '2308': '台達電', '2002': '中鋼',
            '1301': '台塑', '1303': '南亞', '2207': '和泰車',
            '2886': '兆豐金', '2891': '中信金', '2892': '第一金'
        }
        return stock_names.get(stock_id, f'股票{stock_id}')

    def save_to_database(self, revenue_data):
        """儲存數據到數據庫"""
        try:
            if not revenue_data:
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for record in revenue_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue
                        (stock_id, stock_name, year, month, revenue, revenue_mom,
                         revenue_yoy, cumulative_revenue, cumulative_yoy,
                         open_price, close_price, high_price, low_price,
                         price_change, price_change_pct, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_id'], record['stock_name'], record['year'],
                        record['month'], record['revenue'], record['revenue_mom'],
                        record['revenue_yoy'], record['cumulative_revenue'],
                        record['cumulative_yoy'], record['open_price'],
                        record['close_price'], record['high_price'], record['low_price'],
                        record['price_change'], record['price_change_pct'], record['crawl_date']
                    ))
                    saved_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 儲存記錄失敗: {e}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 成功儲存 {saved_count} 筆數據到數據庫")
            return True

        except Exception as e:
            self.logger.error(f"❌ 儲存數據庫失敗: {e}")
            return False

    def download_multiple_stocks(self, stock_list, progress_callback=None):
        """批量下載多支股票的月營收資料"""
        try:
            self.logger.info(f"🚀 開始批量下載 {len(stock_list)} 支股票的月營收資料...")

            results = []
            success_count = 0

            for i, stock_id in enumerate(stock_list, 1):
                try:
                    if progress_callback:
                        progress_callback(f"處理第 {i}/{len(stock_list)} 支股票: {stock_id}")

                    self.logger.info(f"📊 處理第 {i}/{len(stock_list)} 支股票: {stock_id}")

                    # 下載數據
                    record_count = self.download_stock_revenue(stock_id)

                    if record_count:
                        results.append({
                            'stock_id': stock_id,
                            'status': 'success',
                            'record_count': record_count
                        })
                        success_count += 1
                        self.logger.info(f"✅ {stock_id} 處理完成，獲得 {record_count} 筆數據")
                    else:
                        results.append({
                            'stock_id': stock_id,
                            'status': 'failed',
                            'record_count': 0
                        })
                        self.logger.warning(f"⚠️ {stock_id} 處理失敗")

                    # 隨機延遲避免被封鎖
                    if i < len(stock_list):
                        delay = 3  # 固定3秒延遲
                        time.sleep(delay)

                except Exception as e:
                    self.logger.error(f"❌ 處理 {stock_id} 時發生錯誤: {e}")
                    results.append({
                        'stock_id': stock_id,
                        'status': 'error',
                        'record_count': 0,
                        'error': str(e)
                    })
                    continue

            self.logger.info(f"🎉 批量下載完成: {success_count}/{len(stock_list)} 支股票成功")

            if progress_callback:
                progress_callback(f"批量下載完成: {success_count}/{len(stock_list)} 支股票成功")

            return results

        except Exception as e:
            self.logger.error(f"❌ 批量下載失敗: {e}")
            return []

    def parse_revenue_tables_scraper(self, tables, stock_id):
        """解析網頁抓取的月營收表格"""
        try:
            revenue_data = []

            for i, table in enumerate(tables):
                # 檢查是否為月營收表格
                if self.is_revenue_table_scraper(table):
                    self.logger.info(f"✅ 找到月營收表格 (表格 {i+1})")

                    # 解析表格數據
                    parsed_data = self.extract_revenue_data_scraper(table, stock_id)
                    if parsed_data:
                        revenue_data.extend(parsed_data)

            return revenue_data if revenue_data else None

        except Exception as e:
            self.logger.error(f"❌ 解析月營收表格失敗: {e}")
            return None

    def is_revenue_table_scraper(self, table):
        """判斷是否為月營收表格"""
        try:
            # 轉換為字符串檢查
            table_str = str(table)

            # 檢查關鍵字
            revenue_keywords = ['營收', '月增率', '年增率', '累計', '億']
            keyword_count = sum(1 for keyword in revenue_keywords if keyword in table_str)

            # 檢查行數（月營收表格通常有12行以上）
            has_enough_rows = len(table) >= 6

            # 檢查是否有數字格式的數據
            has_numeric_data = False
            for _, row in table.head(3).iterrows():
                for cell in row:
                    cell_str = str(cell)
                    if any(char.isdigit() for char in cell_str):
                        has_numeric_data = True
                        break
                if has_numeric_data:
                    break

            return keyword_count >= 2 and has_enough_rows and has_numeric_data

        except Exception as e:
            self.logger.error(f"❌ 表格檢查失敗: {e}")
            return False

    def extract_revenue_data_scraper(self, table, stock_id):
        """從網頁抓取的表格提取月營收數據"""
        try:
            revenue_records = []

            # 獲取股票名稱
            stock_name = self.get_stock_name(stock_id)

            for index, row in table.iterrows():
                try:
                    # 跳過標題行
                    if index < 2:
                        continue

                    # 嘗試解析年月
                    year_month = self.parse_year_month_from_row_scraper(row, index)
                    if not year_month:
                        continue

                    year, month = year_month

                    # 解析營收數據
                    revenue_info = self.parse_revenue_row_scraper(row)
                    if not revenue_info:
                        continue

                    # 構建記錄
                    record = {
                        'stock_id': stock_id,
                        'stock_name': stock_name,
                        'year': year,
                        'month': month,
                        'revenue': revenue_info.get('revenue'),
                        'revenue_mom': revenue_info.get('revenue_mom'),
                        'revenue_yoy': revenue_info.get('revenue_yoy'),
                        'cumulative_revenue': revenue_info.get('cumulative_revenue'),
                        'cumulative_yoy': revenue_info.get('cumulative_yoy'),
                        'open_price': revenue_info.get('open_price'),
                        'close_price': revenue_info.get('close_price'),
                        'high_price': revenue_info.get('high_price'),
                        'low_price': revenue_info.get('low_price'),
                        'price_change': revenue_info.get('price_change'),
                        'price_change_pct': revenue_info.get('price_change_pct'),
                        'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    revenue_records.append(record)

                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue

            return revenue_records if revenue_records else None

        except Exception as e:
            self.logger.error(f"❌ 提取營收數據失敗: {e}")
            return None

    def parse_year_month_from_row_scraper(self, row, index):
        """從行中解析年月信息（網頁抓取版）"""
        try:
            # 嘗試從第一列獲取年月信息
            first_col = str(row.iloc[0]) if len(row) > 0 else ""

            # 嘗試不同的年月格式
            import re

            # 格式1: 2025.06
            match = re.search(r'(\d{4})\.(\d{1,2})', first_col)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                return year, month

            # 格式2: Jun-25
            month_mapping = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
            }

            match = re.search(r'([A-Za-z]{3})-(\d{2})', first_col)
            if match:
                month_str = match.group(1)
                year_str = match.group(2)

                month = month_mapping.get(month_str)
                if month:
                    year = int(year_str) + 2000 if int(year_str) < 50 else int(year_str) + 1900
                    return year, month

            # 格式3: 2024/01
            match = re.search(r'(\d{4})/(\d{1,2})', first_col)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                return year, month

            return None

        except Exception as e:
            self.logger.warning(f"⚠️ 解析年月失敗: {e}")
            return None

    def parse_revenue_row_scraper(self, row):
        """解析營收行數據（網頁抓取版）"""
        try:
            revenue_info = {}

            # 根據列數動態解析
            for i, value in enumerate(row):
                try:
                    cleaned_value = self.clean_numeric_value_scraper(str(value))

                    if cleaned_value is not None:
                        # 根據位置推測欄位類型
                        if i == 1:  # 開盤價
                            revenue_info['open_price'] = cleaned_value
                        elif i == 2:  # 收盤價
                            revenue_info['close_price'] = cleaned_value
                        elif i == 3:  # 最高價
                            revenue_info['high_price'] = cleaned_value
                        elif i == 4:  # 最低價
                            revenue_info['low_price'] = cleaned_value
                        elif i == 5:  # 漲跌(元)
                            revenue_info['price_change'] = cleaned_value
                        elif i == 6:  # 漲跌(%)
                            revenue_info['price_change_pct'] = cleaned_value
                        elif i == 7:  # 單月營收
                            revenue_info['revenue'] = cleaned_value
                        elif i == 8:  # 月增率
                            revenue_info['revenue_mom'] = cleaned_value
                        elif i == 9:  # 年增率
                            revenue_info['revenue_yoy'] = cleaned_value
                        elif i == 10:  # 累計營收
                            revenue_info['cumulative_revenue'] = cleaned_value
                        elif i == 11:  # 累計年增率
                            revenue_info['cumulative_yoy'] = cleaned_value

                except Exception as e:
                    continue

            return revenue_info if revenue_info else None

        except Exception as e:
            self.logger.warning(f"⚠️ 解析營收行失敗: {e}")
            return None

    def clean_numeric_value_scraper(self, value_str):
        """清理數值字符串（網頁抓取版）"""
        try:
            if pd.isna(value_str) or value_str in ['nan', 'None', '', '-', 'N/A']:
                return None

            # 移除常見的非數字字符
            cleaned = str(value_str).replace(',', '').replace('%', '').replace('+', '').strip()

            # 處理負號
            if cleaned.startswith('(') and cleaned.endswith(')'):
                cleaned = '-' + cleaned[1:-1]

            # 嘗試轉換為浮點數
            return float(cleaned)

        except (ValueError, TypeError):
            return None

def main():
    """主函數"""
    downloader = GoodinfoMonthlyRevenueDownloader()

    # 測試股票代碼列表
    test_stocks = ['2330', '2317', '2454', '6505', '3008']

    print("🚀 開始下載GoodInfo月營收資料...")
    print(f"📁 下載目錄: {downloader.download_dir}")
    print(f"🎯 測試股票: {', '.join(test_stocks)}")

    try:
        downloader.download_multiple_stocks(test_stocks)
        print("✅ 下載完成！")
    except Exception as e:
        print(f"❌ 下載過程中發生錯誤: {e}")
    finally:
        if hasattr(downloader, 'driver') and downloader.driver:
            downloader.driver.quit()

if __name__ == "__main__":
    main()
