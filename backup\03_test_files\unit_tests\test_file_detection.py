#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試文件檢測邏輯
"""

import os
import glob
from datetime import datetime

def test_file_detection():
    """測試文件檢測邏輯"""
    
    # 檢查Downloads目錄中的Excel文件
    username = os.getenv('USERNAME')
    download_dir = f"C:/Users/<USER>/Downloads"
    
    print(f"📂 檢查下載目錄: {download_dir}")
    
    # 獲取所有Excel文件
    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
    
    print(f"📋 找到 {len(excel_files)} 個Excel文件:")
    
    for excel_file in excel_files:
        filename = os.path.basename(excel_file).lower()
        file_time = os.path.getctime(excel_file)
        file_size = os.path.getsize(excel_file)
        
        # 檢查是否為營收相關文件
        is_revenue_file = (
            'salemondetail' in filename or 
            'monthly' in filename or 
            'revenue' in filename or
            filename.startswith('salemondetail')
        )
        
        print(f"  📄 {os.path.basename(excel_file)}")
        print(f"      大小: {file_size} bytes")
        print(f"      時間: {datetime.fromtimestamp(file_time)}")
        print(f"      營收文件: {'✅' if is_revenue_file else '❌'}")
        print()
    
    # 篩選營收文件
    revenue_files = [f for f in excel_files if any(keyword in os.path.basename(f).lower() 
                   for keyword in ['salemondetail', 'monthly', 'revenue']) or
                   os.path.basename(f).lower().startswith('salemondetail')]
    
    print(f"🎯 營收相關文件 ({len(revenue_files)} 個):")
    for revenue_file in revenue_files:
        print(f"  ✅ {os.path.basename(revenue_file)}")
    
    # 檢查最近的營收文件
    if revenue_files:
        recent_files = sorted(revenue_files, key=os.path.getctime, reverse=True)
        most_recent = recent_files[0]
        
        print(f"\n🕒 最新的營收文件:")
        print(f"  📄 {os.path.basename(most_recent)}")
        print(f"  📅 {datetime.fromtimestamp(os.path.getctime(most_recent))}")
        print(f"  📊 {os.path.getsize(most_recent)} bytes")
        
        return most_recent
    else:
        print("\n❌ 沒有找到營收相關文件")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 文件檢測邏輯測試")
    print("=" * 60)
    
    result = test_file_detection()
    
    if result:
        print(f"\n✅ 測試成功！找到文件: {os.path.basename(result)}")
    else:
        print("\n❌ 測試失敗！沒有找到合適的文件")
