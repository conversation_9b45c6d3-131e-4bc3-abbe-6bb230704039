#!/usr/bin/env python3
"""
簡化的TSRTC '-' 字符串修復測試
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dash_conversion():
    """測試 '-' 字符串轉換修復"""
    print("🧪 測試TSRTC '-' 字符串轉換修復")
    print("=" * 50)
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        
        monitor = TSRTCBackupMonitor()
        
        # 測試安全轉換函數
        print("📊 測試 _safe_float 函數:")
        test_values = ['-', '', None, '42.85', '0', 'invalid']
        
        for value in test_values:
            try:
                result = monitor._safe_float(value)
                print(f"   ✅ {value!r} -> {result}")
            except Exception as e:
                print(f"   ❌ {value!r} -> 錯誤: {e}")
        
        print("\n📊 測試 _safe_int 函數:")
        for value in test_values:
            try:
                result = monitor._safe_int(value)
                print(f"   ✅ {value!r} -> {result}")
            except Exception as e:
                print(f"   ❌ {value!r} -> 錯誤: {e}")
        
        # 測試數據格式化
        print("\n📊 測試數據格式化 (包含 '-' 值):")
        
        problem_data = {
            'c': '2330',
            'n': '台積電',
            'z': '580.00',  # 現價
            'y': '575.00',  # 昨收
            'o': '-',       # 開盤價 (問題值)
            'h': '582.00',  # 最高
            'l': '-',       # 最低 (問題值)
            'v': '12345',   # 成交量
            'tv': '-',      # 當盤成交量 (問題值)
            't': '13:30:00',
            'd': '20250714'
        }
        
        try:
            formatted = monitor._format_stock_data(problem_data)
            
            if formatted:
                print("   ✅ 數據格式化成功！")
                print(f"   📈 現價: {formatted.get('current_price', 'N/A')}")
                print(f"   📈 昨收: {formatted.get('prev_close', 'N/A')}")
                print(f"   📈 開盤: {formatted.get('open_price', 'N/A')} (原始: '-')")
                print(f"   📈 最低: {formatted.get('low_price', 'N/A')} (原始: '-')")
                print(f"   📈 當盤量: {formatted.get('current_volume', 'N/A')} (原始: '-')")
                
                # 檢查是否沒有錯誤
                if (formatted.get('open_price') == 0 and 
                    formatted.get('low_price') == 0 and 
                    formatted.get('current_volume') == 0):
                    print("   ✅ '-' 值正確轉換為 0")
                    return True
                else:
                    print("   ❌ '-' 值轉換不正確")
                    return False
            else:
                print("   ❌ 數據格式化失敗")
                return False
                
        except Exception as e:
            print(f"   ❌ 數據格式化異常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_error_logging():
    """測試錯誤日誌是否還會出現"""
    print("\n🧪 測試錯誤日誌修復")
    print("=" * 50)
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        import logging
        import io
        
        # 創建日誌捕獲器
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.ERROR)
        
        # 添加到根日誌記錄器
        root_logger = logging.getLogger()
        root_logger.addHandler(handler)
        root_logger.setLevel(logging.ERROR)
        
        monitor = TSRTCBackupMonitor()
        
        # 測試多個包含 '-' 的數據
        test_cases = [
            {'z': '-', 'y': '100', 'o': '-', 'h': '-', 'l': '-', 'v': '-', 'tv': '-'},
            {'z': '50', 'y': '-', 'o': '49', 'h': '-', 'l': '-', 'v': '1000', 'tv': '-'},
            {'z': '-', 'y': '-', 'o': '-', 'h': '-', 'l': '-', 'v': '-', 'tv': '-'},
        ]
        
        error_count = 0
        for i, test_data in enumerate(test_cases):
            print(f"   測試案例 {i+1}: {test_data}")
            
            # 清空日誌捕獲器
            log_capture.seek(0)
            log_capture.truncate(0)
            
            # 執行格式化
            result = monitor._format_stock_data(test_data)
            
            # 檢查是否有錯誤日誌
            log_contents = log_capture.getvalue()
            if "could not convert string to float: '-'" in log_contents:
                print(f"   ❌ 仍有 '-' 轉換錯誤")
                error_count += 1
            else:
                print(f"   ✅ 無 '-' 轉換錯誤")
        
        # 移除處理器
        root_logger.removeHandler(handler)
        
        if error_count == 0:
            print(f"\n✅ 所有測試案例都沒有 '-' 轉換錯誤！")
            return True
        else:
            print(f"\n❌ 有 {error_count} 個測試案例仍有錯誤")
            return False
        
    except Exception as e:
        print(f"❌ 錯誤日誌測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 TSRTC '-' 字符串修復驗證")
    print("=" * 60)
    
    # 執行測試
    test1_result = test_dash_conversion()
    test2_result = test_error_logging()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    print(f"   轉換功能測試: {'✅ 通過' if test1_result else '❌ 失敗'}")
    print(f"   錯誤日誌測試: {'✅ 通過' if test2_result else '❌ 失敗'}")
    
    if test1_result and test2_result:
        print("\n🎉 TSRTC '-' 字符串問題已完全修復！")
        print("\n💡 修復內容:")
        print("   • 添加了 _safe_float() 函數處理 '-' 字符串")
        print("   • 添加了 _safe_int() 函數處理數值轉換")
        print("   • 修復了價格、成交量、五檔數據的安全轉換")
        print("   • 消除了 'could not convert string to float: \"-\"' 錯誤")
        print("\n✨ 系統現在可以正常處理TSRTC返回的 '-' 值，不會再產生錯誤日誌")
        return True
    else:
        print("\n⚠️ 修復不完整，請檢查相關代碼")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
