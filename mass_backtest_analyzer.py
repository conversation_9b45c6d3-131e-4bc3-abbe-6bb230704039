#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大量回測分析引擎
透過分析2000檔股票超過2年的數據，找出最有效的術語組合規則
"""

import pandas as pd
import numpy as np
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import os

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SignalCombination:
    """信號組合數據類"""
    name: str
    conditions: List[str]
    buy_signals: List[Dict]
    sell_signals: List[Dict]
    win_rate: float
    avg_return: float
    max_drawdown: float
    total_trades: int
    confidence_score: float

@dataclass
class TradingRule:
    """交易規則數據類"""
    rule_id: str
    rule_name: str
    buy_conditions: List[str]
    sell_conditions: List[str]
    success_rate: float
    avg_profit: float
    risk_score: float
    sample_size: int
    market_conditions: List[str]

class MassBacktestAnalyzer:
    """大量回測分析器"""
    
    def __init__(self, db_path: str = "db/price.db"):
        self.db_path = db_path
        self.strategies = ['RSI', 'MACD', '布林通道', '移動平均交叉']
        self.min_data_days = 200  # 2年最少數據
        self.min_trades = 3  # 最少交易次數
        
        # 術語定義
        self.signal_terms = {
            'RSI': {
                'buy': ['RSI超賣反彈', 'RSI黃金交叉', 'RSI背離買點', 'RSI突破50'],
                'sell': ['RSI超買回落', 'RSI死亡交叉', 'RSI背離賣點', 'RSI跌破50']
            },
            'MACD': {
                'buy': ['MACD金叉', 'MACD零軸突破', 'MACD背離買點', 'MACD柱狀轉正'],
                'sell': ['MACD死叉', 'MACD零軸跌破', 'MACD背離賣點', 'MACD柱狀轉負']
            },
            '布林通道': {
                'buy': ['布林下軌反彈', '布林中軌突破', '布林收縮後擴張', '布林%B回升'],
                'sell': ['布林上軌回落', '布林中軌跌破', '布林收縮警示', '布林%B下降']
            },
            '移動平均交叉': {
                'buy': ['MA金叉', 'MA多頭排列', 'MA支撐確認', 'MA趨勢轉強'],
                'sell': ['MA死叉', 'MA空頭排列', 'MA壓力確認', 'MA趨勢轉弱']
            }
        }
        
        # 組合規則模板
        self.combination_templates = [
            # 雙策略組合
            {'type': 'dual', 'pattern': '{strategy1}_{term1} + {strategy2}_{term2}'},
            # 三策略組合
            {'type': 'triple', 'pattern': '{strategy1}_{term1} + {strategy2}_{term2} + {strategy3}_{term3}'},
            # 條件組合
            {'type': 'conditional', 'pattern': '當{condition}時，{strategy1}_{term1}配合{strategy2}_{term2}'}
        ]
    
    def load_stock_list(self) -> List[str]:
        """載入股票清單"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取有足夠數據的股票
            cursor.execute("""
                SELECT stock_id, COUNT(*) as data_count
                FROM stock_daily_data 
                WHERE date >= date('now', '-2 years')
                GROUP BY stock_id
                HAVING data_count >= ?
                ORDER BY data_count DESC
                LIMIT 2000
            """, (self.min_data_days,))
            
            stocks = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            logger.info(f"載入 {len(stocks)} 檔股票進行分析")
            return stocks
            
        except Exception as e:
            logger.error(f"載入股票清單失敗: {e}")
            return []
    
    def load_stock_data(self, stock_id: str, days: int = 1000) -> pd.DataFrame:
        """載入單檔股票數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT date, Open as open, High as high, Low as low, Close as close, Volume as volume
                FROM stock_daily_data
                WHERE stock_id = ?
                AND date >= date('now', '-{} days')
                ORDER BY date
            """.format(days)
            
            df = pd.read_sql_query(query, conn, params=(stock_id,))
            conn.close()
            
            if df.empty:
                return df
                
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"載入股票 {stock_id} 數據失敗: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        if df.empty:
            return df
            
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # 布林通道
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['bb_std'] = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['ma20'] + (df['bb_std'] * 2)
            df['bb_lower'] = df['ma20'] - (df['bb_std'] * 2)
            df['bb_width'] = df['bb_upper'] - df['bb_lower']
            df['bb_percent'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # 移動平均
            df['ma5'] = df['close'].rolling(window=5).mean()
            df['ma10'] = df['close'].rolling(window=10).mean()
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['ma60'] = df['close'].rolling(window=60).mean()
            
            return df
            
        except Exception as e:
            logger.error(f"計算技術指標失敗: {e}")
            return df
    
    def generate_strategy_signals(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """生成各策略信號"""
        signals = {}
        
        try:
            # RSI信號
            rsi_buy = []
            rsi_sell = []
            
            for i in range(1, len(df)):
                rsi_curr = df['rsi'].iloc[i]
                rsi_prev = df['rsi'].iloc[i-1]
                
                # RSI買入信號
                if rsi_prev <= 30 and rsi_curr > 30:  # RSI超賣反彈
                    rsi_buy.append({'index': i, 'type': 'RSI超賣反彈', 'strength': 0.8})
                elif rsi_prev <= 50 and rsi_curr > 50:  # RSI突破50
                    rsi_buy.append({'index': i, 'type': 'RSI突破50', 'strength': 0.6})
                
                # RSI賣出信號
                if rsi_prev >= 70 and rsi_curr < 70:  # RSI超買回落
                    rsi_sell.append({'index': i, 'type': 'RSI超買回落', 'strength': 0.8})
                elif rsi_prev >= 50 and rsi_curr < 50:  # RSI跌破50
                    rsi_sell.append({'index': i, 'type': 'RSI跌破50', 'strength': 0.6})
            
            signals['RSI'] = {'buy': rsi_buy, 'sell': rsi_sell}
            
            # MACD信號
            macd_buy = []
            macd_sell = []
            
            for i in range(1, len(df)):
                macd_curr = df['macd'].iloc[i]
                macd_prev = df['macd'].iloc[i-1]
                signal_curr = df['macd_signal'].iloc[i]
                signal_prev = df['macd_signal'].iloc[i-1]
                
                # MACD買入信號
                if macd_prev <= signal_prev and macd_curr > signal_curr:  # MACD金叉
                    strength = 0.9 if macd_curr > 0 else 0.7
                    macd_buy.append({'index': i, 'type': 'MACD金叉', 'strength': strength})
                elif macd_prev <= 0 and macd_curr > 0:  # MACD零軸突破
                    macd_buy.append({'index': i, 'type': 'MACD零軸突破', 'strength': 0.8})
                
                # MACD賣出信號
                if macd_prev >= signal_prev and macd_curr < signal_curr:  # MACD死叉
                    strength = 0.9 if macd_curr < 0 else 0.7
                    macd_sell.append({'index': i, 'type': 'MACD死叉', 'strength': strength})
                elif macd_prev >= 0 and macd_curr < 0:  # MACD零軸跌破
                    macd_sell.append({'index': i, 'type': 'MACD零軸跌破', 'strength': 0.8})
            
            signals['MACD'] = {'buy': macd_buy, 'sell': macd_sell}
            
            # 布林通道信號
            bb_buy = []
            bb_sell = []
            
            for i in range(1, len(df)):
                close_curr = df['close'].iloc[i]
                close_prev = df['close'].iloc[i-1]
                bb_lower_curr = df['bb_lower'].iloc[i]
                bb_lower_prev = df['bb_lower'].iloc[i-1]
                bb_upper_curr = df['bb_upper'].iloc[i]
                bb_upper_prev = df['bb_upper'].iloc[i-1]
                ma20_curr = df['ma20'].iloc[i]
                ma20_prev = df['ma20'].iloc[i-1]
                
                # 布林通道買入信號
                if close_prev <= bb_lower_prev and close_curr > bb_lower_curr:  # 布林下軌反彈
                    bb_buy.append({'index': i, 'type': '布林下軌反彈', 'strength': 0.8})
                elif close_prev <= ma20_prev and close_curr > ma20_curr:  # 布林中軌突破
                    bb_buy.append({'index': i, 'type': '布林中軌突破', 'strength': 0.6})
                
                # 布林通道賣出信號
                if close_prev >= bb_upper_prev and close_curr < bb_upper_curr:  # 布林上軌回落
                    bb_sell.append({'index': i, 'type': '布林上軌回落', 'strength': 0.8})
                elif close_prev >= ma20_prev and close_curr < ma20_curr:  # 布林中軌跌破
                    bb_sell.append({'index': i, 'type': '布林中軌跌破', 'strength': 0.6})
            
            signals['布林通道'] = {'buy': bb_buy, 'sell': bb_sell}
            
            # 移動平均交叉信號
            ma_buy = []
            ma_sell = []
            
            for i in range(1, len(df)):
                ma5_curr = df['ma5'].iloc[i]
                ma5_prev = df['ma5'].iloc[i-1]
                ma20_curr = df['ma20'].iloc[i]
                ma20_prev = df['ma20'].iloc[i-1]
                
                # 移動平均買入信號
                if ma5_prev <= ma20_prev and ma5_curr > ma20_curr:  # MA金叉
                    ma_buy.append({'index': i, 'type': 'MA金叉', 'strength': 0.7})
                
                # 移動平均賣出信號
                if ma5_prev >= ma20_prev and ma5_curr < ma20_curr:  # MA死叉
                    ma_sell.append({'index': i, 'type': 'MA死叉', 'strength': 0.7})
            
            signals['移動平均交叉'] = {'buy': ma_buy, 'sell': ma_sell}
            
            return signals

        except Exception as e:
            logger.error(f"生成策略信號失敗: {e}")
            return {}

    def find_signal_combinations(self, signals: Dict[str, Dict], df: pd.DataFrame) -> List[SignalCombination]:
        """尋找有效的信號組合"""
        combinations = []

        try:
            # 雙策略組合分析
            for strategy1 in self.strategies:
                for strategy2 in self.strategies:
                    if strategy1 >= strategy2:  # 避免重複組合
                        continue

                    # 分析買入組合
                    for term1 in self.signal_terms[strategy1]['buy']:
                        for term2 in self.signal_terms[strategy2]['buy']:
                            combo = self.analyze_dual_combination(
                                signals, df, strategy1, strategy2, term1, term2, 'buy'
                            )
                            if combo and combo.total_trades >= self.min_trades:
                                combinations.append(combo)

                    # 分析賣出組合
                    for term1 in self.signal_terms[strategy1]['sell']:
                        for term2 in self.signal_terms[strategy2]['sell']:
                            combo = self.analyze_dual_combination(
                                signals, df, strategy1, strategy2, term1, term2, 'sell'
                            )
                            if combo and combo.total_trades >= self.min_trades:
                                combinations.append(combo)

            return combinations

        except Exception as e:
            logger.error(f"尋找信號組合失敗: {e}")
            return []

    def analyze_dual_combination(self, signals: Dict, df: pd.DataFrame,
                               strategy1: str, strategy2: str, term1: str, term2: str,
                               signal_type: str) -> SignalCombination:
        """分析雙策略組合"""
        try:
            # 獲取對應策略的信號
            signals1 = signals.get(strategy1, {}).get(signal_type, [])
            signals2 = signals.get(strategy2, {}).get(signal_type, [])

            # 尋找時間窗口內的組合信號
            combined_signals = []
            window = 3  # 3天窗口

            for sig1 in signals1:
                if sig1['type'] == term1:
                    for sig2 in signals2:
                        if sig2['type'] == term2:
                            if abs(sig1['index'] - sig2['index']) <= window:
                                combined_signals.append({
                                    'index': min(sig1['index'], sig2['index']),
                                    'strength': (sig1['strength'] + sig2['strength']) / 2,
                                    'components': [sig1, sig2]
                                })

            if not combined_signals:
                return None

            # 計算組合績效
            returns = []
            for signal in combined_signals:
                idx = signal['index']
                if idx < len(df) - 20:  # 確保有足夠的後續數據
                    entry_price = df['close'].iloc[idx]
                    exit_price = df['close'].iloc[idx + 10]  # 10天後賣出

                    if signal_type == 'buy':
                        ret = (exit_price - entry_price) / entry_price
                    else:
                        ret = (entry_price - exit_price) / entry_price

                    returns.append(ret)

            if not returns:
                return None

            # 計算統計指標
            win_rate = sum(1 for r in returns if r > 0) / len(returns)
            avg_return = np.mean(returns)
            max_drawdown = min(returns) if returns else 0

            # 計算信心分數
            confidence_score = self.calculate_confidence_score(
                win_rate, avg_return, len(returns), max_drawdown
            )

            combo_name = f"{strategy1}_{term1} + {strategy2}_{term2}"

            return SignalCombination(
                name=combo_name,
                conditions=[f"{strategy1}: {term1}", f"{strategy2}: {term2}"],
                buy_signals=combined_signals if signal_type == 'buy' else [],
                sell_signals=combined_signals if signal_type == 'sell' else [],
                win_rate=win_rate,
                avg_return=avg_return,
                max_drawdown=max_drawdown,
                total_trades=len(returns),
                confidence_score=confidence_score
            )

        except Exception as e:
            logger.error(f"分析雙策略組合失敗: {e}")
            return None

    def calculate_confidence_score(self, win_rate: float, avg_return: float,
                                 sample_size: int, max_drawdown: float) -> float:
        """計算信心分數"""
        try:
            # 基礎分數 (勝率權重40%)
            win_score = win_rate * 40

            # 報酬分數 (平均報酬權重30%)
            return_score = min(avg_return * 100, 10) * 3  # 最高30分

            # 樣本大小分數 (樣本數權重20%)
            sample_score = min(sample_size / 50, 1) * 20  # 50次交易得滿分

            # 風險分數 (最大回撤權重10%)
            risk_score = max(0, 10 + max_drawdown * 100)  # 回撤越小分數越高

            total_score = win_score + return_score + sample_score + risk_score
            return min(total_score, 100)

        except Exception as e:
            logger.error(f"計算信心分數失敗: {e}")
            return 0

    def analyze_single_stock(self, stock_id: str) -> List[SignalCombination]:
        """分析單檔股票"""
        try:
            # 載入數據
            df = self.load_stock_data(stock_id)
            if df.empty or len(df) < self.min_data_days:
                return []

            # 計算技術指標
            df = self.calculate_technical_indicators(df)

            # 生成策略信號
            signals = self.generate_strategy_signals(df)

            # 尋找信號組合
            combinations = self.find_signal_combinations(signals, df)

            logger.info(f"股票 {stock_id}: 發現 {len(combinations)} 個有效組合")
            return combinations

        except Exception as e:
            logger.error(f"分析股票 {stock_id} 失敗: {e}")
            return []
