#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試阿水二式策略表格修復
驗證所有符合條件的股票都能正確顯示技術指標數據
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_test_stock_data(stock_id, trend_type="bearish"):
    """創建測試股票數據"""
    dates = pd.date_range(start='2024-01-01', end='2024-07-12', freq='D')
    dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
    
    n_days = len(dates)
    base_price = 100.0
    
    if trend_type == "bearish":
        # 空頭趨勢
        trend = np.linspace(0, -25, n_days)
        noise = np.random.normal(0, 1.5, n_days)
    else:
        # 多頭趨勢
        trend = np.linspace(0, 20, n_days)
        noise = np.random.normal(0, 1.5, n_days)
    
    prices = base_price + trend + noise
    prices = np.maximum(prices, 20)
    
    volumes = np.random.normal(500000, 100000, n_days)
    volumes = np.maximum(volumes, 50000)
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': prices * (1 + np.random.normal(0, 0.01, n_days)),
        'High': prices * (1 + np.abs(np.random.normal(0, 0.02, n_days))),
        'Low': prices * (1 - np.abs(np.random.normal(0, 0.02, n_days))),
        'Close': prices,
        'Volume': volumes.astype(int)
    })
    
    return df

def test_ashui_indicators_calculation():
    """測試阿水二式指標計算"""
    print("📊 測試阿水二式指標計算")
    print("=" * 50)
    
    # 測試多個股票
    test_stocks = [
        ("1342", "bearish"),  # 空頭股票
        ("9917", "bearish"),  # 空頭股票
        ("8916", "bullish"),  # 多頭股票
        ("8440", "bearish"),  # 空頭股票
        ("8279", "bullish"),  # 多頭股票
    ]
    
    results = []
    
    for stock_id, trend_type in test_stocks:
        print(f"\n📈 測試股票 {stock_id} ({trend_type})")
        
        # 創建測試數據
        df = create_test_stock_data(stock_id, trend_type)
        
        try:
            # 計算移動平均線
            df['MA20'] = df['Close'].rolling(window=20).mean()
            df['MA60'] = df['Close'].rolling(window=60).mean() if len(df) >= 60 else df['Close'].rolling(window=len(df)).mean()
            df['MA120'] = df['Close'].rolling(window=120).mean() if len(df) >= 120 else df['Close'].rolling(window=len(df)).mean()
            
            latest = df.iloc[-1]
            current_price = latest['Close']
            ma20 = latest['MA20']
            ma60 = latest['MA60']
            ma120 = latest['MA120']
            
            # 計算距離20MA的百分比
            distance_to_ma = abs((current_price - ma20) / ma20 * 100) if ma20 > 0 else 0
            
            # 判斷20MA趨勢
            if len(df) >= 5:
                ma20_slope = (df['MA20'].iloc[-1] - df['MA20'].iloc[-5]) / df['MA20'].iloc[-5] * 100
                if ma20_slope < -1:
                    ma_trend = "強勢下彎"
                elif ma20_slope < 0:
                    ma_trend = "下彎"
                else:
                    ma_trend = "平緩"
            else:
                ma_trend = "數據不足"
            
            # 判斷空頭排列程度
            if current_price < ma20 < ma60 < ma120:
                bear_alignment = "完整空排"
            elif current_price < ma20 < ma60:
                bear_alignment = "短期空排"
            else:
                bear_alignment = "部分空排"
            
            # 判斷高檔位置
            high_60 = df['High'].rolling(60).max().iloc[-1] if len(df) >= 60 else current_price
            high_120 = df['High'].rolling(120).max().iloc[-1] if len(df) >= 120 else current_price
            
            if current_price >= high_120 * 0.9:
                high_position = "120日高檔"
            elif current_price >= high_60 * 0.9:
                high_position = "60日高檔"
            else:
                high_position = "非高檔"
            
            # 計算空方評分
            ashui_short_score = 50  # 基礎分
            if ma20_slope < 0:
                ashui_short_score += 20
            if current_price < ma20:
                ashui_short_score += 15
            if bear_alignment in ["完整空排", "短期空排"]:
                ashui_short_score += 15
            if distance_to_ma > 2:
                ashui_short_score += 10
            if high_position in ["120日高檔", "60日高檔"]:
                ashui_short_score += 10
            
            # 設置策略狀態
            if trend_type == "bearish" and ashui_short_score >= 70:
                strategy_status = "✅ 空方信號"
            else:
                strategy_status = "❌ 不符合"
            
            result = {
                "股票代碼": stock_id,
                "當前價格": f"{current_price:.2f}",
                "20MA趨勢": ma_trend,
                "空頭排列": bear_alignment,
                "距20MA": f"{distance_to_ma:.1f}%",
                "高檔位置": high_position,
                "空方評分": ashui_short_score,
                "策略狀態": strategy_status
            }
            
            results.append(result)
            
            print(f"  💰 當前價格: {current_price:.2f}")
            print(f"  📉 20MA趨勢: {ma_trend} ({ma20_slope:.2f}%)")
            print(f"  🐻 空頭排列: {bear_alignment}")
            print(f"  📏 距20MA: {distance_to_ma:.1f}%")
            print(f"  🏔️ 高檔位置: {high_position}")
            print(f"  🎯 空方評分: {ashui_short_score}/100")
            print(f"  📊 策略狀態: {strategy_status}")
            
        except Exception as e:
            print(f"  ❌ 計算失敗: {e}")
            result = {
                "股票代碼": stock_id,
                "當前價格": "計算失敗",
                "20MA趨勢": "計算失敗",
                "空頭排列": "計算失敗",
                "距20MA": "計算失敗",
                "高檔位置": "計算失敗",
                "空方評分": 0,
                "策略狀態": "❌ 計算失敗"
            }
            results.append(result)
    
    # 顯示結果表格
    print("\n📋 阿水二式策略測試結果表格")
    print("=" * 100)
    print(f"{'股票代碼':<8} {'當前價格':<10} {'20MA趨勢':<12} {'空頭排列':<12} {'距20MA':<10} {'高檔位置':<12} {'空方評分':<8} {'策略狀態':<15}")
    print("-" * 100)
    
    for result in results:
        print(f"{result['股票代碼']:<8} {result['當前價格']:<10} {result['20MA趨勢']:<12} {result['空頭排列']:<12} {result['距20MA']:<10} {result['高檔位置']:<12} {result['空方評分']:<8} {result['策略狀態']:<15}")
    
    print("\n✅ 阿水二式指標計算測試完成！")
    return results

def test_table_data_structure():
    """測試表格數據結構"""
    print("\n🔧 測試表格數據結構")
    print("=" * 50)
    
    # 模擬matching_stocks
    matching_stocks = ["1342", "9917", "8916", "8440", "8279"]
    
    # 模擬results數據
    mock_results = []
    for i, stock_id in enumerate(matching_stocks):
        trend_type = "bearish" if i % 2 == 0 else "bullish"
        df = create_test_stock_data(stock_id, trend_type)
        
        result = {
            "股票代碼": stock_id,
            "股票名稱": f"測試股票{i+1}",
            "收盤價": df['Close'].iloc[-1],
            "歷史數據": df,
            "Volume": df['Volume'].iloc[-1]
        }
        mock_results.append(result)
    
    # 測試過濾邏輯
    qualified_results = []
    for result in mock_results:
        stock_id = result["股票代碼"]
        if stock_id in matching_stocks:
            qualified_results.append(result)
    
    print(f"📊 總股票數: {len(mock_results)}")
    print(f"📊 符合條件股票數: {len(matching_stocks)}")
    print(f"📊 過濾後股票數: {len(qualified_results)}")
    
    # 驗證每個股票都有必要的數據
    for i, result in enumerate(qualified_results):
        stock_id = result["股票代碼"]
        has_historical_data = "歷史數據" in result and result["歷史數據"] is not None
        has_price_data = "收盤價" in result
        has_volume_data = "Volume" in result
        
        print(f"  股票 {stock_id}: 歷史數據={has_historical_data}, 價格數據={has_price_data}, 成交量數據={has_volume_data}")
    
    print("✅ 表格數據結構測試完成！")
    return qualified_results

if __name__ == "__main__":
    print("🧪 阿水二式策略表格修復測試")
    print("=" * 60)
    
    # 測試指標計算
    calculation_results = test_ashui_indicators_calculation()
    
    # 測試表格數據結構
    table_results = test_table_data_structure()
    
    print(f"\n🎉 測試完成！")
    print(f"📊 指標計算測試: {len(calculation_results)} 個股票")
    print(f"📊 表格數據測試: {len(table_results)} 個股票")
    
    print("\n📋 修復重點:")
    print("✅ 直接從歷史數據計算技術指標")
    print("✅ 不依賴條件結果的訊息解析")
    print("✅ 為所有符合條件的股票計算數據")
    print("✅ 提供計算失敗時的預設值")
    print("✅ 確保表格顯示所有matching_stocks中的股票")
    
    print("\n" + "=" * 60)
