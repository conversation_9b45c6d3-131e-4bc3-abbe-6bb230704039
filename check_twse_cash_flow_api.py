#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 TWSE OpenAPI 是否有現金流量表資料
"""

import requests
import json
import time

def check_twse_api_endpoints():
    """檢查 TWSE API 端點"""
    
    print("=" * 80)
    print("🔍 檢查 TWSE OpenAPI 現金流量表端點")
    print("=" * 80)
    
    base_url = "https://openapi.twse.com.tw/v1"
    
    # 可能的現金流量表端點
    potential_endpoints = [
        "/opendata/t187ap08_L_ci",  # 可能的現金流量表 (一般業)
        "/opendata/t187ap08_O_ci",  # 可能的現金流量表 (其他業)
        "/opendata/t187ap08_F_ci",  # 可能的現金流量表 (金融業)
        "/opendata/t187ap09_L_ci",  # 其他可能的財務報表
        "/opendata/t187ap10_L_ci",  # 其他可能的財務報表
        "/opendata/t187ap11_L_ci",  # 其他可能的財務報表
        "/opendata/t187ap12_L_ci",  # 其他可能的財務報表
    ]
    
    # 已知的端點
    known_endpoints = [
        ("/opendata/t187ap06_L_ci", "綜合損益表 (一般業)"),
        ("/opendata/t187ap07_L_ci", "資產負債表 (一般業)"),
    ]
    
    print("📊 已知端點:")
    for endpoint, description in known_endpoints:
        test_endpoint(base_url, endpoint, description)
    
    print(f"\n🔍 測試可能的現金流量表端點:")
    for endpoint in potential_endpoints:
        test_endpoint(base_url, endpoint, "未知")
        time.sleep(1)  # 避免請求過於頻繁

def test_endpoint(base_url, endpoint, description):
    """測試單個端點"""
    
    url = f"{base_url}{endpoint}"
    
    try:
        resp = requests.get(
            url,
            headers={
                "User-Agent": "finlab-crawler/1.0",
                "Accept": "application/json"
            },
            verify=False,
            timeout=10
        )
        
        if resp.status_code == 200:
            try:
                data = resp.json()
                if data and len(data) > 0:
                    print(f"   ✅ {endpoint} - {description}")
                    print(f"      📊 資料筆數: {len(data)}")
                    
                    # 檢查第一筆資料的欄位
                    if isinstance(data[0], dict):
                        columns = list(data[0].keys())
                        print(f"      📋 欄位數: {len(columns)}")
                        
                        # 檢查是否包含現金流量相關欄位
                        cash_flow_columns = [col for col in columns if '現金' in col or '流量' in col]
                        if cash_flow_columns:
                            print(f"      💰 現金流量欄位: {len(cash_flow_columns)} 個")
                            for col in cash_flow_columns[:3]:
                                print(f"         - {col}")
                            if len(cash_flow_columns) > 3:
                                print(f"         ... 還有 {len(cash_flow_columns)-3} 個")
                        else:
                            print(f"      ⚠️ 無現金流量相關欄位")
                        
                        # 顯示前幾個欄位
                        print(f"      📋 前5個欄位: {columns[:5]}")
                else:
                    print(f"   ⚠️ {endpoint} - 無資料")
            except json.JSONDecodeError:
                print(f"   ❌ {endpoint} - JSON 解析失敗")
        elif resp.status_code == 404:
            print(f"   ❌ {endpoint} - 端點不存在")
        else:
            print(f"   ❌ {endpoint} - HTTP {resp.status_code}")
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ {endpoint} - 請求超時")
    except requests.exceptions.ConnectionError:
        print(f"   🔌 {endpoint} - 連線錯誤")
    except Exception as e:
        print(f"   ❌ {endpoint} - 錯誤: {e}")

def check_existing_cash_flows():
    """檢查現有的現金流量表資料"""
    
    print(f"\n" + "=" * 80)
    print(f"📊 檢查現有現金流量表資料")
    print("=" * 80)
    
    import sqlite3
    import os
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    if os.path.exists(cash_flows_db):
        try:
            conn = sqlite3.connect(cash_flows_db)
            cursor = conn.cursor()
            
            # 檢查最新資料日期
            cursor.execute("SELECT MAX(date) FROM cash_flows")
            latest_date = cursor.fetchone()[0]
            
            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM cash_flows")
            total_count = cursor.fetchone()[0]
            
            # 檢查最新一季的資料
            cursor.execute("SELECT COUNT(*) FROM cash_flows WHERE date = ?", (latest_date,))
            latest_count = cursor.fetchone()[0]
            
            print(f"📊 現有現金流量表狀況:")
            print(f"   📅 最新資料日期: {latest_date}")
            print(f"   📊 總資料筆數: {total_count:,}")
            print(f"   📈 最新一季筆數: {latest_count:,}")
            
            # 檢查台積電資料
            cursor.execute("""
                SELECT date, 營業活動之淨現金流入（流出）, 投資活動之淨現金流入（流出）, 籌資活動之淨現金流入（流出）
                FROM cash_flows 
                WHERE stock_id = '2330' 
                ORDER BY date DESC 
                LIMIT 3
            """)
            
            tsmc_data = cursor.fetchall()
            if tsmc_data:
                print(f"\n📈 台積電最近3季現金流量:")
                for row in tsmc_data:
                    date, operating, investing, financing = row
                    print(f"   {date}: 營業 {operating:,}, 投資 {investing:,}, 籌資 {financing:,}")
            
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 檢查現金流量表失敗: {e}")
            return False
    else:
        print(f"❌ 現金流量表檔案不存在: {cash_flows_db}")
        return False

def test_mops_crawler():
    """測試 MOPS 爬蟲是否還能工作"""
    
    print(f"\n" + "=" * 80)
    print(f"🧪 測試 MOPS 現金流量表爬蟲")
    print("=" * 80)
    
    try:
        import sys
        import os
        
        # 添加 finlab 路徑
        current_dir = os.path.dirname(os.path.abspath(__file__))
        finlab_path = os.path.join(current_dir, 'finlab')
        if finlab_path not in sys.path:
            sys.path.insert(0, finlab_path)
        
        from crawler import crawl_cash_flows
        import datetime
        
        print("📦 成功導入 crawl_cash_flows 函數")
        
        # 測試日期
        test_date = datetime.datetime(2022, 5, 15)  # Q1 財報發布日
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("⚠️ 注意: 這將進行實際的 MOPS 網站爬取測試")
        choice = input("是否執行 MOPS 爬蟲測試？(y/n，預設 n): ").strip().lower()
        
        if choice == 'y':
            print("🔄 開始測試 MOPS 現金流量表爬蟲...")
            result = crawl_cash_flows(test_date)
            
            if result is not None and len(result) > 0:
                print(f"✅ MOPS 爬蟲測試成功！")
                print(f"📊 獲取資料筆數: {len(result):,}")
                return True
            else:
                print(f"⚠️ MOPS 爬蟲測試返回空資料")
                return False
        else:
            print("⏭️ 跳過 MOPS 爬蟲測試")
            return None
            
    except ImportError as e:
        print(f"❌ 導入 crawl_cash_flows 失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試 MOPS 爬蟲失敗: {e}")
        return False

def provide_recommendations():
    """提供建議"""
    
    print(f"\n" + "=" * 80)
    print(f"💡 現金流量表爬蟲建議")
    print("=" * 80)
    
    print("🎯 基於檢查結果的建議:")
    print()
    
    print("📊 **如果 TWSE OpenAPI 有現金流量表端點**:")
    print("   ✅ 建議使用 TWSE OpenAPI")
    print("   ✅ 更穩定可靠")
    print("   ✅ 與統一財務資料使用相同來源")
    print("   ✅ 可以整合到統一財務資料中")
    print()
    
    print("⚠️ **如果 TWSE OpenAPI 沒有現金流量表端點**:")
    print("   🔄 繼續使用 MOPS 爬蟲")
    print("   🛠️ 需要定期檢查和維護")
    print("   📊 保持現有的 cash_flows.db")
    print()
    
    print("❌ **如果 MOPS 爬蟲也無法使用**:")
    print("   🔍 尋找其他資料來源")
    print("   💰 考慮付費的財務資料 API")
    print("   📈 或暫時使用現有的歷史資料")

def main():
    """主函數"""
    
    print("🔍 現金流量表爬蟲狀態檢查")
    
    # 檢查 TWSE API 端點
    check_twse_api_endpoints()
    
    # 檢查現有資料
    existing_data = check_existing_cash_flows()
    
    # 測試 MOPS 爬蟲
    mops_result = test_mops_crawler()
    
    # 提供建議
    provide_recommendations()
    
    print(f"\n" + "=" * 80)
    print(f"📊 檢查總結")
    print("=" * 80)
    
    print(f"📊 現有資料: {'✅ 存在' if existing_data else '❌ 不存在'}")
    
    if mops_result is True:
        print(f"🔄 MOPS 爬蟲: ✅ 可用")
    elif mops_result is False:
        print(f"🔄 MOPS 爬蟲: ❌ 不可用")
    else:
        print(f"🔄 MOPS 爬蟲: ⏭️ 未測試")

if __name__ == "__main__":
    main()
