#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ROE數據庫匯入錯誤診斷工具
診斷ROE下載器GUI中數據庫匯入失敗的具體原因
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime
import traceback

def find_latest_roe_csv():
    """找到最新的ROE CSV文件"""
    csv_dir = "D:/Finlab/history/tables"
    csv_files = []
    
    if os.path.exists(csv_dir):
        for file in os.listdir(csv_dir):
            if 'roe_data' in file.lower() and file.endswith('.csv'):
                file_path = os.path.join(csv_dir, file)
                csv_files.append((file_path, os.path.getctime(file_path)))
    
    if not csv_files:
        print("❌ 沒有找到ROE CSV文件")
        return None
    
    # 選擇最新的文件
    latest_csv = max(csv_files, key=lambda x: x[1])[0]
    print(f"📁 找到最新CSV文件: {os.path.basename(latest_csv)}")
    return latest_csv

def analyze_csv_structure(csv_file):
    """分析CSV文件結構"""
    try:
        print(f"\n📊 分析CSV文件結構: {os.path.basename(csv_file)}")
        print("=" * 60)
        
        # 讀取CSV
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"📋 基本信息:")
        print(f"  • 資料筆數: {len(df)}")
        print(f"  • 欄位數量: {len(df.columns)}")
        print(f"  • 檔案大小: {os.path.getsize(csv_file)} bytes")
        
        print(f"\n📝 欄位列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. '{col}' ({df[col].dtype})")
        
        print(f"\n📋 前3筆原始資料:")
        for i, row in df.head(3).iterrows():
            print(f"  第{i+1}筆: {dict(row)}")
        
        return df
        
    except Exception as e:
        print(f"❌ 分析CSV失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")
        return None

def test_database_connection():
    """測試數據庫連接"""
    try:
        print(f"\n💾 測試數據庫連接")
        print("=" * 60)
        
        db_path = "D:/Finlab/history/tables/roe_data.db"
        print(f"📁 數據庫路徑: {db_path}")
        
        # 檢查目錄是否存在
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            print(f"⚠️ 數據庫目錄不存在，創建: {db_dir}")
            os.makedirs(db_dir, exist_ok=True)
        
        # 連接數據庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roe_data'")
        table_exists = cursor.fetchone() is not None
        
        print(f"📊 表格存在: {'是' if table_exists else '否'}")
        
        if table_exists:
            # 檢查表格結構
            cursor.execute("PRAGMA table_info(roe_data)")
            columns = cursor.fetchall()
            print(f"📝 表格欄位:")
            for col in columns:
                print(f"  • {col[1]} ({col[2]}) {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
            
            # 檢查現有資料數量
            cursor.execute("SELECT COUNT(*) FROM roe_data")
            count = cursor.fetchone()[0]
            print(f"📊 現有資料筆數: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 數據庫連接失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_data_standardization(csv_file):
    """測試資料標準化"""
    try:
        print(f"\n🔧 測試資料標準化")
        print("=" * 60)
        
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        
        # 創建下載器實例
        downloader = GoodinfoROECSVDownloader()
        
        # 讀取原始CSV
        df_original = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"📊 原始資料: {len(df_original)} 筆，{len(df_original.columns)} 欄位")
        
        # 執行標準化
        df_cleaned = downloader.standardize_data(df_original)
        print(f"📊 標準化後: {len(df_cleaned)} 筆，{len(df_cleaned.columns)} 欄位")
        
        print(f"\n📝 標準化後欄位:")
        for i, col in enumerate(df_cleaned.columns, 1):
            print(f"  {i:2d}. '{col}' ({df_cleaned[col].dtype})")
        
        print(f"\n📋 標準化後前3筆資料:")
        for i, row in df_cleaned.head(3).iterrows():
            print(f"  第{i+1}筆:")
            for col in df_cleaned.columns:
                print(f"    {col}: {row[col]}")
        
        return df_cleaned
        
    except Exception as e:
        print(f"❌ 資料標準化失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")
        return None

def test_database_import(df_cleaned):
    """測試數據庫匯入"""
    try:
        print(f"\n💾 測試數據庫匯入")
        print("=" * 60)
        
        db_path = "D:/Finlab/history/tables/roe_data.db"
        conn = sqlite3.connect(db_path)
        
        # 創建表格
        print("🏗️ 創建/檢查表格結構...")
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roe_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                roe_value REAL,
                roe_change REAL,
                eps_value REAL,
                report_year INTEGER,
                rank_position INTEGER,
                crawl_date TEXT,
                data_source TEXT DEFAULT 'goodinfo_csv',
                UNIQUE(stock_code, report_year)
            )
        """)
        
        # 嘗試匯入資料
        print("📥 嘗試匯入資料...")
        df_cleaned.to_sql('roe_data', conn, if_exists='append', index=False)
        
        conn.commit()
        
        # 驗證匯入結果
        cursor.execute("SELECT COUNT(*) FROM roe_data")
        count = cursor.fetchone()[0]
        print(f"✅ 匯入成功！數據庫中共有 {count} 筆資料")
        
        # 顯示最新匯入的資料
        cursor.execute("""
            SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year 
            FROM roe_data 
            ORDER BY id DESC 
            LIMIT 5
        """)
        rows = cursor.fetchall()
        
        if rows:
            print(f"\n📋 最新匯入的5筆資料:")
            for i, row in enumerate(rows, 1):
                print(f"  {i}. {row[0]} {row[1]} - ROE: {row[2]}% (變化: {row[3]}) EPS: {row[4]} 年度: {row[5]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 數據庫匯入失敗: {e}")
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def main():
    """主診斷流程"""
    print("🔍 ROE數據庫匯入錯誤診斷工具")
    print("=" * 80)
    
    # 1. 找到最新的CSV文件
    csv_file = find_latest_roe_csv()
    if not csv_file:
        return False
    
    # 2. 分析CSV文件結構
    df_original = analyze_csv_structure(csv_file)
    if df_original is None:
        return False
    
    # 3. 測試數據庫連接
    if not test_database_connection():
        return False
    
    # 4. 測試資料標準化
    df_cleaned = test_data_standardization(csv_file)
    if df_cleaned is None:
        return False
    
    # 5. 測試數據庫匯入
    if not test_database_import(df_cleaned):
        return False
    
    print(f"\n🎉 診斷完成！所有測試都通過了。")
    print("💡 如果GUI仍然失敗，可能是GUI代碼中的問題。")
    
    return True

if __name__ == "__main__":
    main()
