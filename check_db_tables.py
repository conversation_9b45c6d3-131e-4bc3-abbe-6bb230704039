#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查台灣證交所市場數據資料庫表格結構
"""

import sqlite3
import os

def check_database_tables():
    """檢查資料庫表格"""
    
    db_files = {
        'market_index': 'market_index.db',
        'margin_trading': 'margin_trading.db', 
        'historical_index': 'market_historical.db'
    }
    
    for data_type, db_file in db_files.items():
        db_path = f"D:/Finlab/history/tables/{db_file}"
        
        print(f"\n🔍 檢查 {data_type} ({db_file})")
        print("=" * 60)
        
        if not os.path.exists(db_path):
            print(f"❌ 檔案不存在: {db_path}")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查所有表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"📋 表格列表:")
            for table in tables:
                table_name = table[0]
                print(f"   - {table_name}")
                
                # 檢查表格結構
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                print(f"     欄位: {[col[1] for col in columns]}")
                
                # 檢查資料筆數
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"     筆數: {count}")
                
                # 顯示前3筆資料
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    rows = cursor.fetchall()
                    print(f"     範例資料:")
                    for i, row in enumerate(rows, 1):
                        print(f"       {i}. {row[:3]}...")  # 只顯示前3個欄位
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 檢查失敗: {e}")

if __name__ == "__main__":
    check_database_tables()
