#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試數據庫自動檢測功能
"""

import os
import sqlite3
import json
from ashui_backtest_system import AshiuBacktestSystem

def test_database_detection():
    """測試數據庫自動檢測功能"""
    print("🔍 測試數據庫自動檢測功能")
    print("=" * 50)
    
    # 1. 檢查主程序配置文件
    config_file = "app_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                db_path = config.get('database', {}).get('price_db_path')
                print(f"📋 主程序配置文件: {config_file}")
                print(f"📍 配置的數據庫路徑: {db_path}")
                
                if db_path and os.path.exists(db_path):
                    print(f"✅ 配置的數據庫文件存在")
                    test_database_structure(db_path)
                else:
                    print(f"❌ 配置的數據庫文件不存在")
        except Exception as e:
            print(f"❌ 讀取配置文件失敗: {e}")
    else:
        print(f"⚠️ 主程序配置文件不存在: {config_file}")
    
    print()
    
    # 2. 檢查常見數據庫路徑
    print("🔍 檢查常見數據庫路徑:")
    common_paths = [
        "D:/Finlab/history/tables/price.db",
        "./data/price_data.db",
        "./db/price.db",
        "data/price_data.db"
    ]
    
    found_databases = []
    for path in common_paths:
        if os.path.exists(path):
            print(f"✅ 找到數據庫: {path}")
            found_databases.append(path)
            test_database_structure(path)
        else:
            print(f"❌ 不存在: {path}")
    
    print()
    
    # 3. 測試回測系統的自動檢測
    print("🚀 測試回測系統自動檢測:")
    try:
        backtest_system = AshiuBacktestSystem()
        print(f"📍 回測系統使用的數據庫: {backtest_system.db_path}")
        
        # 測試獲取股票列表
        stock_list = backtest_system.get_stock_list()
        print(f"📊 可用股票數量: {len(stock_list)}")
        
        if stock_list:
            print(f"📋 前10支股票: {stock_list[:10]}")
            
            # 測試獲取股票數據
            test_stock = stock_list[0]
            df = backtest_system.get_stock_data(test_stock, "2023-01-01", "2023-12-31")
            print(f"📈 {test_stock} 的數據筆數: {len(df)}")
            
            if not df.empty:
                print(f"📅 數據日期範圍: {df['date'].min()} 到 {df['date'].max()}")
                print(f"📊 數據欄位: {list(df.columns)}")
        else:
            print("⚠️ 沒有找到股票數據")
            
    except Exception as e:
        print(f"❌ 回測系統測試失敗: {e}")
    
    print()
    
    # 4. 建議
    print("💡 建議:")
    if found_databases:
        print(f"✅ 找到 {len(found_databases)} 個數據庫文件")
        print("✅ 回測系統應該能正常工作")
    else:
        print("❌ 沒有找到任何數據庫文件")
        print("💡 建議:")
        print("   1. 檢查主程序是否正確配置數據庫路徑")
        print("   2. 執行 test_ashui_backtest.py 生成示例數據")
        print("   3. 確認數據庫文件路徑和權限")

def test_database_structure(db_path):
    """測試數據庫結構"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 獲取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   📋 包含表格: {tables}")
        
        # 檢查股價相關表格
        stock_tables = [t for t in tables if 'stock' in t.lower() or 'price' in t.lower()]
        if stock_tables:
            table_name = stock_tables[0]
            print(f"   📊 主要股價表: {table_name}")
            
            # 檢查表結構
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"   📝 欄位: {columns}")
            
            # 檢查數據量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📈 數據筆數: {count:,}")
            
            if count > 0:
                # 檢查日期範圍
                date_cols = [col for col in columns if 'date' in col.lower()]
                if date_cols:
                    date_col = date_cols[0]
                    cursor.execute(f"SELECT MIN({date_col}), MAX({date_col}) FROM {table_name}")
                    min_date, max_date = cursor.fetchone()
                    print(f"   📅 日期範圍: {min_date} 到 {max_date}")
                
                # 檢查股票數量
                stock_cols = [col for col in columns if 'stock' in col.lower() or 'code' in col.lower()]
                if stock_cols:
                    stock_col = stock_cols[0]
                    cursor.execute(f"SELECT COUNT(DISTINCT {stock_col}) FROM {table_name}")
                    stock_count = cursor.fetchone()[0]
                    print(f"   📊 股票數量: {stock_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 檢查數據庫結構失敗: {e}")

if __name__ == '__main__':
    test_database_detection()
