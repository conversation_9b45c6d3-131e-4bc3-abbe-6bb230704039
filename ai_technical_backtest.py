#!/usr/bin/env python3
"""
AI技術指標回測系統
基於技術指標的智能回測分析，無需OpenAI API Key
參考Colab程式碼架構，實現本地化回測功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import yfinance as yf
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import os
import io
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import QBuffer, QIODevice

# 嘗試導入talib，如果失敗則使用手動計算
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logging.warning("⚠️ talib未安裝，將使用手動計算技術指標")

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class BacktestResult:
    """回測結果數據類"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    profit_factor: float
    start_date: str
    end_date: str
    strategy_name: str

class TechnicalIndicators:
    """技術指標計算類"""
    
    @staticmethod
    def calculate_sma(data: pd.Series, period: int) -> pd.Series:
        """簡單移動平均線"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def calculate_ema(data: pd.Series, period: int) -> pd.Series:
        """指數移動平均線"""
        return data.ewm(span=period).mean()
    
    @staticmethod
    def calculate_rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相對強弱指標"""
        if TALIB_AVAILABLE:
            try:
                return pd.Series(talib.RSI(data.values, timeperiod=period), index=data.index)
            except:
                pass

        # 手動計算RSI
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指標"""
        if TALIB_AVAILABLE:
            try:
                macd, signal_line, histogram = talib.MACD(data.values, fastperiod=fast, slowperiod=slow, signalperiod=signal)
                return (pd.Series(macd, index=data.index),
                       pd.Series(signal_line, index=data.index),
                       pd.Series(histogram, index=data.index))
            except:
                pass

        # 手動計算MACD
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    @staticmethod
    def calculate_bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林通道"""
        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    @staticmethod
    def calculate_stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """隨機指標"""
        if TALIB_AVAILABLE:
            try:
                k, d = talib.STOCH(high.values, low.values, close.values,
                                  fastk_period=k_period, slowk_period=d_period, slowd_period=d_period)
                return pd.Series(k, index=close.index), pd.Series(d, index=close.index)
            except:
                pass

        # 手動計算
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

class TradingStrategy:
    """交易策略基類"""
    
    def __init__(self, name: str):
        self.name = name
        self.signals = pd.DataFrame()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信號 - 子類需要實現"""
        raise NotImplementedError
    
    def calculate_returns(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.Series:
        """計算策略收益"""
        # 計算每日收益率
        returns = data['close'].pct_change()
        
        # 策略收益 = 信號 * 收益率
        strategy_returns = signals['position'].shift(1) * returns
        
        return strategy_returns.fillna(0)

class RSIStrategy(TradingStrategy):
    """RSI策略"""
    
    def __init__(self, rsi_period: int = 14, oversold: float = 30, overbought: float = 70):
        super().__init__("RSI策略")
        self.rsi_period = rsi_period
        self.oversold = oversold
        self.overbought = overbought
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成RSI交易信號"""
        signals = pd.DataFrame(index=data.index)
        
        # 計算RSI
        rsi = TechnicalIndicators.calculate_rsi(data['close'], self.rsi_period)
        
        # 生成信號
        signals['rsi'] = rsi
        signals['signal'] = 0
        signals['position'] = 0
        
        # 買入信號：RSI < 超賣線
        buy_mask = rsi < self.oversold
        signals.loc[buy_mask, 'signal'] = 1

        # 賣出信號：RSI > 超買線
        sell_mask = rsi > self.overbought
        signals.loc[sell_mask, 'signal'] = -1

        # 添加信號強度分析
        signals['signal_strength'] = 0.0
        signals['signal_text'] = ''

        # 買入信號強度
        for idx in signals[buy_mask].index:
            try:
                rsi_val = rsi.loc[idx]
                if rsi_val <= 20:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🟢 嚴重超賣'
                elif rsi_val <= 25:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🟢 強力超賣'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🟢 超賣反彈'
            except:
                signals.loc[idx, 'signal_text'] = '🟢 RSI買入'

        # 賣出信號強度
        for idx in signals[sell_mask].index:
            try:
                rsi_val = rsi.loc[idx]
                if rsi_val >= 80:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🔴 嚴重過熱'
                elif rsi_val >= 75:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🔴 強力過熱'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🔴 過熱回調'
            except:
                signals.loc[idx, 'signal_text'] = '🔴 RSI賣出'
        
        # 計算持倉
        signals['position'] = signals['signal'].ffill().fillna(0)
        
        return signals

class MACDStrategy(TradingStrategy):
    """MACD策略"""
    
    def __init__(self, fast: int = 12, slow: int = 26, signal: int = 9):
        super().__init__("MACD策略")
        self.fast = fast
        self.slow = slow
        self.signal_period = signal
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成MACD交易信號"""
        signals = pd.DataFrame(index=data.index)
        
        # 計算MACD
        macd, signal_line, histogram = TechnicalIndicators.calculate_macd(
            data['close'], self.fast, self.slow, self.signal_period
        )
        
        signals['macd'] = macd
        signals['signal_line'] = signal_line
        signals['histogram'] = histogram
        signals['signal'] = 0
        signals['position'] = 0
        
        # 買入信號：MACD線上穿信號線
        golden_cross = (macd > signal_line) & (macd.shift(1) <= signal_line.shift(1))
        signals.loc[golden_cross, 'signal'] = 1

        # 賣出信號：MACD線下穿信號線
        death_cross = (macd < signal_line) & (macd.shift(1) >= signal_line.shift(1))
        signals.loc[death_cross, 'signal'] = -1

        # 添加信號強度分析
        signals['signal_strength'] = 0.0
        signals['signal_text'] = ''

        # 金叉信號強度
        for idx in signals[golden_cross].index:
            try:
                macd_val = macd.loc[idx]
                signal_val = signal_line.loc[idx]

                # 判斷是否在零軸上方
                above_zero = macd_val > 0 and signal_val > 0

                # 計算交叉強度
                cross_strength = abs(macd_val - signal_val)

                if above_zero and cross_strength > 0.5:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🟢 強勢金叉'
                elif above_zero:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🟢 零軸金叉'
                elif cross_strength > 0.3:
                    signals.loc[idx, 'signal_strength'] = 0.6
                    signals.loc[idx, 'signal_text'] = '🟢 急速金叉'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🟢 金叉信號'
            except:
                signals.loc[idx, 'signal_text'] = '🟢 MACD買入'

        # 死叉信號強度
        for idx in signals[death_cross].index:
            try:
                macd_val = macd.loc[idx]
                signal_val = signal_line.loc[idx]

                # 判斷是否在零軸下方
                below_zero = macd_val < 0 and signal_val < 0

                # 計算交叉強度
                cross_strength = abs(macd_val - signal_val)

                if below_zero and cross_strength > 0.5:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🔴 強勢死叉'
                elif below_zero:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🔴 零軸死叉'
                elif cross_strength > 0.3:
                    signals.loc[idx, 'signal_strength'] = 0.6
                    signals.loc[idx, 'signal_text'] = '🔴 急速死叉'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🔴 死叉信號'
            except:
                signals.loc[idx, 'signal_text'] = '🔴 MACD賣出'
        
        # 計算持倉
        signals['position'] = signals['signal'].ffill().fillna(0)

        return signals

class BollingerBandsStrategy(TradingStrategy):
    """布林通道策略"""
    
    def __init__(self, period: int = 20, std_dev: float = 2):
        super().__init__("布林通道策略")
        self.period = period
        self.std_dev = std_dev
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成布林通道交易信號"""
        signals = pd.DataFrame(index=data.index)
        
        # 計算布林通道
        upper_band, middle_band, lower_band = TechnicalIndicators.calculate_bollinger_bands(
            data['close'], self.period, self.std_dev
        )
        
        signals['upper_band'] = upper_band
        signals['middle_band'] = middle_band
        signals['lower_band'] = lower_band
        signals['signal'] = 0
        signals['position'] = 0
        
        # 買入信號：價格觸及下軌
        lower_touch = data['close'] <= lower_band
        signals.loc[lower_touch, 'signal'] = 1

        # 賣出信號：價格觸及上軌
        upper_touch = data['close'] >= upper_band
        signals.loc[upper_touch, 'signal'] = -1

        # 添加信號強度分析
        signals['signal_strength'] = 0.0
        signals['signal_text'] = ''

        # 下軌買入信號強度
        for idx in signals[lower_touch].index:
            try:
                price = data['close'].loc[idx]
                lower_val = lower_band.loc[idx]
                middle_val = middle_band.loc[idx]

                # 計算價格相對於下軌的位置
                penetration = (lower_val - price) / lower_val if lower_val > 0 else 0

                if penetration > 0.02:  # 跌破下軌超過2%
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🟢 跌破下軌'
                elif penetration > 0.01:  # 跌破下軌1-2%
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🟢 觸及下軌'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🟢 下軌支撐'
            except:
                signals.loc[idx, 'signal_text'] = '🟢 布林買入'

        # 上軌賣出信號強度
        for idx in signals[upper_touch].index:
            try:
                price = data['close'].loc[idx]
                upper_val = upper_band.loc[idx]

                # 計算價格相對於上軌的位置
                penetration = (price - upper_val) / upper_val if upper_val > 0 else 0

                if penetration > 0.02:  # 突破上軌超過2%
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🔴 突破上軌'
                elif penetration > 0.01:  # 突破上軌1-2%
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🔴 觸及上軌'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🔴 上軌阻力'
            except:
                signals.loc[idx, 'signal_text'] = '🔴 布林賣出'
        
        # 計算持倉
        signals['position'] = signals['signal'].ffill().fillna(0)

        return signals

class MovingAverageCrossStrategy(TradingStrategy):
    """移動平均交叉策略"""
    
    def __init__(self, short_period: int = 10, long_period: int = 30):
        super().__init__("移動平均交叉策略")
        self.short_period = short_period
        self.long_period = long_period
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成移動平均交叉信號"""
        signals = pd.DataFrame(index=data.index)
        
        # 計算移動平均線
        short_ma = TechnicalIndicators.calculate_sma(data['close'], self.short_period)
        long_ma = TechnicalIndicators.calculate_sma(data['close'], self.long_period)
        
        signals['short_ma'] = short_ma
        signals['long_ma'] = long_ma
        signals['signal'] = 0
        signals['position'] = 0
        
        # 買入信號：短期均線上穿長期均線
        golden_cross = (short_ma > long_ma) & (short_ma.shift(1) <= long_ma.shift(1))
        signals.loc[golden_cross, 'signal'] = 1

        # 賣出信號：短期均線下穿長期均線
        death_cross = (short_ma < long_ma) & (short_ma.shift(1) >= long_ma.shift(1))
        signals.loc[death_cross, 'signal'] = -1

        # 添加信號強度分析
        signals['signal_strength'] = 0.0
        signals['signal_text'] = ''

        # 金叉信號強度
        for idx in signals[golden_cross].index:
            try:
                short_val = short_ma.loc[idx]
                long_val = long_ma.loc[idx]
                price = data['close'].loc[idx]

                # 計算均線間距
                ma_gap = (short_val - long_val) / long_val if long_val > 0 else 0

                # 計算價格相對於均線的位置
                price_above_short = price > short_val

                # 計算均線斜率
                if idx > 0:
                    short_slope = (short_val - short_ma.shift(1).loc[idx]) / short_ma.shift(1).loc[idx] if short_ma.shift(1).loc[idx] > 0 else 0
                else:
                    short_slope = 0

                if price_above_short and ma_gap > 0.02 and short_slope > 0.01:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🟢 強勢金叉'
                elif price_above_short and ma_gap > 0.01:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🟢 金叉放量'
                elif short_slope > 0.015:
                    signals.loc[idx, 'signal_strength'] = 0.6
                    signals.loc[idx, 'signal_text'] = '🟢 急速金叉'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🟢 金叉信號'
            except:
                signals.loc[idx, 'signal_text'] = '🟢 均線買入'

        # 死叉信號強度
        for idx in signals[death_cross].index:
            try:
                short_val = short_ma.loc[idx]
                long_val = long_ma.loc[idx]
                price = data['close'].loc[idx]

                # 計算均線間距
                ma_gap = (long_val - short_val) / long_val if long_val > 0 else 0

                # 計算價格相對於均線的位置
                price_below_short = price < short_val

                # 計算均線斜率
                if idx > 0:
                    short_slope = (short_val - short_ma.shift(1).loc[idx]) / short_ma.shift(1).loc[idx] if short_ma.shift(1).loc[idx] > 0 else 0
                else:
                    short_slope = 0

                if price_below_short and ma_gap > 0.02 and short_slope < -0.01:
                    signals.loc[idx, 'signal_strength'] = 0.9
                    signals.loc[idx, 'signal_text'] = '🔴 強勢死叉'
                elif price_below_short and ma_gap > 0.01:
                    signals.loc[idx, 'signal_strength'] = 0.7
                    signals.loc[idx, 'signal_text'] = '🔴 死叉放量'
                elif short_slope < -0.015:
                    signals.loc[idx, 'signal_strength'] = 0.6
                    signals.loc[idx, 'signal_text'] = '🔴 急速死叉'
                else:
                    signals.loc[idx, 'signal_strength'] = 0.5
                    signals.loc[idx, 'signal_text'] = '🔴 死叉信號'
            except:
                signals.loc[idx, 'signal_text'] = '🔴 均線賣出'
        
        # 計算持倉
        signals['position'] = signals['signal'].ffill().fillna(0)

        return signals

class BacktestEngine:
    """回測引擎"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.commission = 0.001425  # 台股手續費
        self.tax = 0.003  # 台股證交稅
    
    def run_backtest(self, data: pd.DataFrame, strategy: TradingStrategy) -> BacktestResult:
        """執行回測"""
        try:
            # 生成交易信號
            signals = strategy.generate_signals(data)
            
            # 計算策略收益
            strategy_returns = strategy.calculate_returns(data, signals)
            
            # 計算累積收益
            cumulative_returns = (1 + strategy_returns).cumprod()
            
            # 計算績效指標
            total_return = cumulative_returns.iloc[-1] - 1
            annual_return = self._calculate_annual_return(cumulative_returns)
            max_drawdown = self._calculate_max_drawdown(cumulative_returns)
            sharpe_ratio = self._calculate_sharpe_ratio(strategy_returns)
            
            # 計算交易統計
            trades = self._calculate_trade_stats(signals, data)
            
            return BacktestResult(
                total_return=total_return,
                annual_return=annual_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=trades['win_rate'],
                total_trades=trades['total_trades'],
                profit_trades=trades['profit_trades'],
                loss_trades=trades['loss_trades'],
                avg_profit=trades['avg_profit'],
                avg_loss=trades['avg_loss'],
                profit_factor=trades['profit_factor'],
                start_date=data.index[0].strftime('%Y-%m-%d'),
                end_date=data.index[-1].strftime('%Y-%m-%d'),
                strategy_name=strategy.name
            )
            
        except Exception as e:
            logging.error(f"回測執行失敗: {e}")
            return None
    
    def _calculate_annual_return(self, cumulative_returns: pd.Series) -> float:
        """計算年化收益率"""
        days = (cumulative_returns.index[-1] - cumulative_returns.index[0]).days
        years = days / 365.25
        if years > 0:
            return (cumulative_returns.iloc[-1] ** (1/years)) - 1
        return 0
    
    def _calculate_max_drawdown(self, cumulative_returns: pd.Series) -> float:
        """計算最大回撤"""
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        return drawdown.min()
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """計算夏普比率"""
        excess_returns = returns - risk_free_rate/252  # 日化無風險利率
        if excess_returns.std() != 0:
            return excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        return 0
    
    def _calculate_trade_stats(self, signals: pd.DataFrame, data: pd.DataFrame) -> Dict:
        """計算交易統計"""
        # 找出交易點
        position_changes = signals['position'].diff()
        trades = position_changes[position_changes != 0]
        
        if len(trades) < 2:
            return {
                'total_trades': 0, 'profit_trades': 0, 'loss_trades': 0,
                'win_rate': 0, 'avg_profit': 0, 'avg_loss': 0, 'profit_factor': 0
            }
        
        # 計算每筆交易收益
        trade_returns = []
        entry_price = None
        
        for date, position_change in trades.items():
            if position_change > 0:  # 買入
                entry_price = data.loc[date, 'close']
            elif position_change < 0 and entry_price is not None:  # 賣出
                exit_price = data.loc[date, 'close']
                trade_return = (exit_price - entry_price) / entry_price
                trade_returns.append(trade_return)
                entry_price = None
        
        if not trade_returns:
            return {
                'total_trades': 0, 'profit_trades': 0, 'loss_trades': 0,
                'win_rate': 0, 'avg_profit': 0, 'avg_loss': 0, 'profit_factor': 0
            }
        
        trade_returns = np.array(trade_returns)
        profit_trades = trade_returns[trade_returns > 0]
        loss_trades = trade_returns[trade_returns < 0]
        
        return {
            'total_trades': len(trade_returns),
            'profit_trades': len(profit_trades),
            'loss_trades': len(loss_trades),
            'win_rate': len(profit_trades) / len(trade_returns) if len(trade_returns) > 0 else 0,
            'avg_profit': profit_trades.mean() if len(profit_trades) > 0 else 0,
            'avg_loss': loss_trades.mean() if len(loss_trades) > 0 else 0,
            'profit_factor': abs(profit_trades.sum() / loss_trades.sum()) if len(loss_trades) > 0 and loss_trades.sum() != 0 else 0
        }

def get_stock_data(symbol: str, start_date: str, end_date: str, db_path: str = None) -> pd.DataFrame:
    """獲取股票數據 - 使用專案資料庫"""
    try:
        import sqlite3
        import os

        # 清理股票代碼（移除.TW等後綴）
        clean_symbol = symbol.replace('.TW', '').replace('.TWO', '')

        logging.info(f"從資料庫獲取 {clean_symbol} 的數據 ({start_date} ~ {end_date})")

        # 嘗試多個可能的資料庫路徑
        possible_db_paths = []

        # 如果有傳入資料庫路徑，優先使用
        if db_path and os.path.exists(db_path):
            possible_db_paths.append(db_path)
            logging.info(f"使用傳入的資料庫路徑: {db_path}")

        # 添加其他可能的路徑（按優先順序）
        possible_db_paths.extend([
            "D:/Finlab/history/tables/price.db",  # 主程式使用的路徑
            "db/price.db",
            "../history/tables/price.db",
            "price.db",
            "stock_data.db"
        ])

        for db_path in possible_db_paths:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)

                    # 檢查表結構
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = [table[0] for table in cursor.fetchall()]

                    # 嘗試不同的表名
                    possible_tables = ['stock_daily_data', 'stock_price', 'daily_data']
                    table_name = None

                    for table in possible_tables:
                        if table in tables:
                            table_name = table
                            break

                    if not table_name:
                        logging.warning(f"在 {db_path} 中未找到股票數據表")
                        conn.close()
                        continue

                    # 檢查表的欄位結構
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]

                    # 確定欄位名稱映射
                    column_mapping = {}

                    # 股票代碼欄位
                    for col in ['stock_id', 'symbol', 'stock_code']:
                        if col in columns:
                            column_mapping['symbol'] = col
                            break

                    # 日期欄位
                    for col in ['date', 'trading_date', 'datetime']:
                        if col in columns:
                            column_mapping['date'] = col
                            break

                    # OHLCV欄位
                    ohlcv_mapping = {
                        'open': ['open', 'Open', 'opening_price', 'open_price'],
                        'high': ['high', 'High', 'highest_price', 'high_price'],
                        'low': ['low', 'Low', 'lowest_price', 'low_price'],
                        'close': ['close', 'Close', 'closing_price', 'close_price'],
                        'volume': ['volume', 'Volume', 'trading_volume']
                    }

                    for target, candidates in ohlcv_mapping.items():
                        for col in candidates:
                            if col in columns:
                                column_mapping[target] = col
                                break

                    # 檢查必要欄位是否都找到
                    required_fields = ['symbol', 'date', 'open', 'high', 'low', 'close']
                    if not all(field in column_mapping for field in required_fields):
                        logging.warning(f"表 {table_name} 缺少必要欄位")
                        conn.close()
                        continue

                    # 構建查詢
                    select_fields = [
                        f"{column_mapping['date']} as date",
                        f"{column_mapping['open']} as open",
                        f"{column_mapping['high']} as high",
                        f"{column_mapping['low']} as low",
                        f"{column_mapping['close']} as close"
                    ]

                    if 'volume' in column_mapping:
                        select_fields.append(f"{column_mapping['volume']} as volume")

                    query = f"""
                        SELECT {', '.join(select_fields)}
                        FROM {table_name}
                        WHERE {column_mapping['symbol']} = ?
                        AND {column_mapping['date']} >= ?
                        AND {column_mapping['date']} <= ?
                        ORDER BY {column_mapping['date']}
                    """

                    df = pd.read_sql_query(query, conn, params=[clean_symbol, start_date, end_date])
                    conn.close()

                    if not df.empty:
                        # 處理日期格式
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)

                        # 確保數值類型
                        numeric_cols = ['open', 'high', 'low', 'close']
                        if 'volume' in df.columns:
                            numeric_cols.append('volume')

                        for col in numeric_cols:
                            df[col] = pd.to_numeric(df[col], errors='coerce')

                        # 移除包含NaN的行
                        df = df.dropna()

                        if not df.empty:
                            logging.info(f"✅ 從資料庫成功獲取 {clean_symbol} 的數據，共 {len(df)} 筆")
                            return df

                except Exception as e:
                    logging.warning(f"從 {db_path} 獲取數據失敗: {e}")
                    if 'conn' in locals():
                        conn.close()
                    continue

        # 如果資料庫獲取失敗，生成示例數據
        logging.warning(f"無法從資料庫獲取 {clean_symbol} 的數據，生成示例數據")
        return generate_sample_stock_data(clean_symbol, start_date, end_date)

    except Exception as e:
        logging.error(f"獲取股票數據完全失敗: {e}")
        # 最後備用方案：生成示例數據
        return generate_sample_stock_data(symbol, start_date, end_date)

def generate_sample_stock_data(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    """生成示例股票數據，用於演示和測試"""
    try:
        import numpy as np
        from datetime import datetime, timedelta

        # 解析日期
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        # 生成日期範圍（只包含工作日）
        dates = pd.bdate_range(start=start, end=end)

        if len(dates) == 0:
            # 如果沒有工作日，至少生成一些數據
            dates = pd.date_range(start=start, end=end, freq='D')
            dates = dates[:min(100, len(dates))]  # 限制最多100天

        # 根據股票代碼設定基礎價格
        base_prices = {
            '2330': 500,  # 台積電
            '2317': 100,  # 鴻海
            '2454': 800,  # 聯發科
            '2881': 25,   # 富邦金
            '2882': 50,   # 國泰金
        }

        base_price = base_prices.get(symbol, 100)

        # 生成價格數據（隨機遊走）
        np.random.seed(hash(symbol) % 1000)  # 使用股票代碼作為種子，確保一致性

        returns = np.random.normal(0.001, 0.02, len(dates))  # 日收益率
        prices = [base_price]

        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, base_price * 0.5))  # 防止價格過低

        # 生成OHLCV數據
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成開高低價
            volatility = 0.02
            high = close * (1 + np.random.uniform(0, volatility))
            low = close * (1 - np.random.uniform(0, volatility))
            open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))

            # 確保價格邏輯正確
            high = max(high, open_price, close)
            low = min(low, open_price, close)

            # 生成成交量
            volume = np.random.randint(1000000, 10000000)

            data.append({
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume
            })

        df = pd.DataFrame(data, index=dates)
        logging.info(f"✅ 為 {symbol} 生成示例數據，共 {len(df)} 筆")
        return df

    except Exception as e:
        logging.error(f"生成示例數據失敗: {e}")
        # 返回最基本的數據
        return pd.DataFrame({
            'open': [100],
            'high': [105],
            'low': [95],
            'close': [102],
            'volume': [1000000]
        }, index=[datetime.now()])

# 預設策略列表
AVAILABLE_STRATEGIES = {
    'RSI策略': RSIStrategy,
    'MACD策略': MACDStrategy,
    '布林通道策略': BollingerBandsStrategy,
    '移動平均交叉策略': MovingAverageCrossStrategy
}

class BacktestResultsWidget:
    """回測結果顯示組件"""

    def __init__(self):
        self.figure = Figure(figsize=(12, 8.5))  # 調整為更緊湊的高度
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setMinimumHeight(650)  # 減少最小高度
        self.canvas.setMaximumHeight(700)  # 設置最大高度限制
        self.current_symbol_info = None  # 儲存當前股票信息

    def plot_results(self, data: pd.DataFrame, signals: pd.DataFrame, result: BacktestResult, symbol_info: dict = None):
        """繪製回測結果圖表"""
        self.figure.clear()
        self.current_symbol_info = symbol_info  # 儲存股票信息供後續使用

        # 創建子圖 - 進一步壓縮主圖，最大化表格顯示空間
        gs = self.figure.add_gridspec(3, 2, height_ratios=[1.8, 1.0, 1.8], hspace=0.3, wspace=0.3)

        # 主圖：價格和交易信號
        ax1 = self.figure.add_subplot(gs[0, :])
        ax1.plot(data.index, data['close'], label='收盤價', linewidth=2, color='#1f77b4', alpha=0.8)

        # 標記買賣點 - 增強視覺效果
        buy_signals = signals[signals['signal'] == 1]
        sell_signals = signals[signals['signal'] == -1]

        if not buy_signals.empty:
            ax1.scatter(buy_signals.index, data.loc[buy_signals.index, 'close'],
                       color='#2e7d32', marker='^', s=150, label='買入',
                       zorder=5, edgecolors='white', linewidth=2)

            # 添加買入信號強度文字
            for idx in buy_signals.index:
                try:
                    signal_text = signals.loc[idx, 'signal_text'] if 'signal_text' in signals.columns else '🟢 買入'
                    price = data.loc[idx, 'close']
                    ax1.annotate(signal_text,
                               xy=(idx, price),
                               xytext=(5, 15),
                               textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7),
                               fontsize=8, ha='left',
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
                except:
                    pass

        if not sell_signals.empty:
            ax1.scatter(sell_signals.index, data.loc[sell_signals.index, 'close'],
                       color='#d32f2f', marker='v', s=150, label='賣出',
                       zorder=5, edgecolors='white', linewidth=2)

            # 添加賣出信號強度文字
            for idx in sell_signals.index:
                try:
                    signal_text = signals.loc[idx, 'signal_text'] if 'signal_text' in signals.columns else '🔴 賣出'
                    price = data.loc[idx, 'close']
                    ax1.annotate(signal_text,
                               xy=(idx, price),
                               xytext=(5, -15),
                               textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7),
                               fontsize=8, ha='left',
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
                except:
                    pass

        # 構建完整標題
        if symbol_info and 'code' in symbol_info and 'name' in symbol_info:
            title = f"{symbol_info['code']} - {symbol_info['name']} - {result.strategy_name} - 交易信號圖"
        else:
            title = f'{result.strategy_name} - 交易信號圖'
        ax1.set_title(title, fontsize=14, fontweight='bold')
        ax1.set_ylabel('價格 (元)', labelpad=15)  # 增加標籤與軸的距離
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 累積收益圖
        ax2 = self.figure.add_subplot(gs[1, :])
        strategy_returns = self._calculate_strategy_returns(data, signals)
        cumulative_returns = (1 + strategy_returns).cumprod()
        benchmark_returns = (1 + data['close'].pct_change()).cumprod()

        ax2.plot(cumulative_returns.index, cumulative_returns, label='策略收益',
                linewidth=2.5, color='#ff7f0e', alpha=0.9)
        ax2.plot(benchmark_returns.index, benchmark_returns, label='買入持有',
                linewidth=2.5, color='#2ca02c', alpha=0.8)
        ax2.set_title('累積收益比較', fontsize=12, fontweight='bold')
        ax2.set_ylabel('累積收益', labelpad=15)  # 增加標籤與軸的距離
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)

        # 添加基準線
        ax2.axhline(y=1.0, color='gray', linestyle='--', alpha=0.5, linewidth=1)

        # 績效指標表格
        ax3 = self.figure.add_subplot(gs[2, 0])
        ax3.axis('off')

        metrics_data = [
            ['總收益率', f'{result.total_return:.2%}'],
            ['年化收益率', f'{result.annual_return:.2%}'],
            ['最大回撤', f'{result.max_drawdown:.2%}'],
            ['夏普比率', f'{result.sharpe_ratio:.2f}'],
            ['勝率', f'{result.win_rate:.2%}']
        ]

        table = ax3.table(cellText=metrics_data,
                         colLabels=['指標', '數值'],
                         cellLoc='center',
                         loc='upper center',
                         colWidths=[0.5, 0.5])
        table.auto_set_font_size(False)
        table.set_fontsize(7)  # 稍微增加字體確保可讀性
        table.scale(1, 0.9)    # 適度壓縮表格高度
        ax3.set_title('績效指標', fontsize=9, pad=2)  # 適中的標題字體

        # 交易統計
        ax4 = self.figure.add_subplot(gs[2, 1])
        ax4.axis('off')

        trade_data = [
            ['總交易次數', f'{result.total_trades}'],
            ['獲利交易', f'{result.profit_trades}'],
            ['虧損交易', f'{result.loss_trades}'],
            ['平均獲利', f'{result.avg_profit:.2%}'],
            ['平均虧損', f'{result.avg_loss:.2%}'],
            ['獲利因子', f'{result.profit_factor:.2f}']
        ]

        table2 = ax4.table(cellText=trade_data,
                          colLabels=['交易統計', '數值'],
                          cellLoc='center',
                          loc='upper center',
                          colWidths=[0.5, 0.5])
        table2.auto_set_font_size(False)
        table2.set_fontsize(7)  # 稍微增加字體確保可讀性
        table2.scale(1, 0.9)    # 適度壓縮表格高度
        ax4.set_title('交易統計', fontsize=9, pad=2)  # 適中的標題字體

        # 調整整體佈局，更緊湊的顯示
        self.figure.tight_layout(pad=1.0)
        # 手動調整子圖間距，上下留白保持一致，確保視覺平衡
        self.figure.subplots_adjust(bottom=0.04, top=0.96, left=0.12, right=0.95, hspace=0.25, wspace=0.25)
        self.canvas.draw()

    def _calculate_strategy_returns(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.Series:
        """計算策略收益率"""
        returns = data['close'].pct_change()
        strategy_returns = signals['position'].shift(1) * returns
        return strategy_returns.fillna(0)

    def copy_chart_to_clipboard(self):
        """複製圖表到剪貼簿"""
        try:
            # 將圖表保存到內存中的緩衝區
            buffer = io.BytesIO()
            self.figure.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)

            # 創建QPixmap並複製到剪貼簿
            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())

            clipboard = QApplication.clipboard()
            clipboard.setPixmap(pixmap)

            buffer.close()
            return True
        except Exception as e:
            logging.error(f"複製圖表失敗: {e}")
            return False

    def save_chart_to_file(self, strategy_name: str = ""):
        """保存圖表到文件"""
        try:
            # 生成檔名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if self.current_symbol_info and 'code' in self.current_symbol_info and 'name' in self.current_symbol_info:
                code = self.current_symbol_info['code']
                name = self.current_symbol_info['name']
                filename = f"{code}-{name}-{strategy_name}-{timestamp}.png"
            else:
                filename = f"回測結果-{strategy_name}-{timestamp}.png"

            # 確保檔名中沒有非法字符
            filename = "".join(c for c in filename if c.isalnum() or c in ('-', '_', '.', '中', '文'))

            # 創建保存目錄
            save_dir = "backtest_charts"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            filepath = os.path.join(save_dir, filename)

            # 檢查文件是否已存在，如果存在則添加編號
            counter = 1
            original_filepath = filepath
            while os.path.exists(filepath):
                name_part, ext = os.path.splitext(original_filepath)
                filepath = f"{name_part}_{counter:02d}{ext}"
                counter += 1

            # 保存圖表
            self.figure.savefig(filepath, format='png', dpi=300, bbox_inches='tight')

            return filepath
        except Exception as e:
            logging.error(f"保存圖表失敗: {e}")
            return None

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)

    print("🧪 測試AI技術指標回測系統...")

    # 獲取台積電數據
    data = get_stock_data('2330', '2023-01-01', '2024-01-01')

    if not data.empty:
        print(f"✅ 成功獲取數據: {len(data)} 筆")

        # 測試RSI策略
        strategy = RSIStrategy()
        engine = BacktestEngine()
        result = engine.run_backtest(data, strategy)

        if result:
            print(f"📊 {result.strategy_name} 回測結果:")
            print(f"   總收益率: {result.total_return:.2%}")
            print(f"   年化收益率: {result.annual_return:.2%}")
            print(f"   最大回撤: {result.max_drawdown:.2%}")
            print(f"   夏普比率: {result.sharpe_ratio:.2f}")
            print(f"   勝率: {result.win_rate:.2%}")
            print(f"   總交易次數: {result.total_trades}")
        else:
            print("❌ 回測失敗")
    else:
        print("❌ 無法獲取數據")

    print("🎉 測試完成！")
