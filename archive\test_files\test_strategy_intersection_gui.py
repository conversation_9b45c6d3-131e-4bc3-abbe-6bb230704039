#!/usr/bin/env python3
"""
測試策略交集GUI功能
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_intersection_gui():
    """測試策略交集GUI功能"""
    print("🧪 測試策略交集GUI功能")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        print("✅ 主程式導入成功")
        
        # 檢查策略交集分析器是否可用
        try:
            from strategy_intersection_analyzer import StrategyIntersectionAnalyzer
            print("✅ 策略交集分析器可用")
        except ImportError as e:
            print(f"❌ 策略交集分析器不可用: {e}")
            return False
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        # 檢查是否有策略交集相關的屬性
        if hasattr(window, 'intersection_analyzer'):
            print("✅ 策略交集分析器已初始化")
        else:
            print("❌ 策略交集分析器未初始化")
        
        if hasattr(window, 'strategy_results_cache'):
            print("✅ 策略結果緩存已初始化")
        else:
            print("❌ 策略結果緩存未初始化")
        
        # 檢查是否有策略交集標籤頁
        tab_widget = window.findChildren(window.tab_widget.__class__)[0]
        intersection_tab_found = False
        
        for i in range(tab_widget.count()):
            tab_text = tab_widget.tabText(i)
            if "策略交集" in tab_text:
                intersection_tab_found = True
                print(f"✅ 找到策略交集標籤頁: {tab_text}")
                break
        
        if not intersection_tab_found:
            print("❌ 未找到策略交集標籤頁")
        
        # 檢查策略交集相關的UI元件
        if hasattr(window, 'intersection_strategy_vars'):
            print(f"✅ 策略選擇複選框已創建，共 {len(window.intersection_strategy_vars)} 個")
        else:
            print("❌ 策略選擇複選框未創建")
        
        if hasattr(window, 'intersection_result_text'):
            print("✅ 交集結果顯示區域已創建")
        else:
            print("❌ 交集結果顯示區域未創建")
        
        # 檢查按鈕
        buttons_to_check = [
            'calculate_intersection_btn',
            'analyze_all_btn', 
            'export_intersection_btn'
        ]
        
        for button_name in buttons_to_check:
            if hasattr(window, button_name):
                print(f"✅ 按鈕 {button_name} 已創建")
            else:
                print(f"❌ 按鈕 {button_name} 未創建")
        
        # 模擬策略結果數據
        print(f"\n🧪 模擬策略結果數據:")
        test_strategy_results(window)
        
        print(f"\n📊 GUI功能測試總結:")
        print("=" * 30)
        
        if (hasattr(window, 'intersection_analyzer') and 
            hasattr(window, 'strategy_results_cache') and
            intersection_tab_found and
            hasattr(window, 'intersection_strategy_vars')):
            print("✅ 策略交集GUI功能完整")
            print("🎯 您現在可以:")
            print("  1. 執行多個策略")
            print("  2. 切換到「🔗 策略交集」標籤頁")
            print("  3. 選擇要分析的策略")
            print("  4. 點擊「🎯 計算交集」")
            print("  5. 查看共同選中的股票")
            return True
        else:
            print("❌ 策略交集GUI功能不完整")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_results(window):
    """測試策略結果緩存功能"""
    try:
        import pandas as pd
        
        # 模擬CANSLIM策略結果
        canslim_data = pd.DataFrame({
            '股票代碼': ['2330', '2317', '2454', '2327', '2603'],
            '股票名稱': ['台積電', '鴻海', '聯發科', '國巨', '長榮'],
            '收盤價': [500, 100, 800, 300, 150],
            '成交量': [50000, 30000, 20000, 15000, 40000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 模擬藏獒策略結果
        mastiff_data = pd.DataFrame({
            '股票代碼': ['2330', '2454', '2327', '3008', '1590'],
            '股票名稱': ['台積電', '聯發科', '國巨', '大立光', '亞德客'],
            '收盤價': [500, 800, 300, 2000, 250],
            '成交量': [50000, 20000, 15000, 10000, 12000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 模擬二次創高策略結果
        second_high_data = pd.DataFrame({
            '股票代碼': ['2330', '2317', '3008', '2412', '2881'],
            '股票名稱': ['台積電', '鴻海', '大立光', '中華電', '富邦金'],
            '收盤價': [500, 100, 2000, 120, 60],
            '成交量': [50000, 30000, 10000, 25000, 35000],
            '符合條件': [True, True, True, True, True]
        })
        
        # 保存到緩存
        window.strategy_results_cache['CANSLIM量價齊升'] = canslim_data
        window.strategy_results_cache['藏獒'] = mastiff_data
        window.strategy_results_cache['二次創高股票'] = second_high_data
        
        print(f"  ✅ 模擬 CANSLIM量價齊升: {len(canslim_data)} 支股票")
        print(f"  ✅ 模擬 藏獒: {len(mastiff_data)} 支股票")
        print(f"  ✅ 模擬 二次創高股票: {len(second_high_data)} 支股票")
        
        # 計算預期交集
        canslim_stocks = set(canslim_data['股票代碼'])
        mastiff_stocks = set(mastiff_data['股票代碼'])
        second_high_stocks = set(second_high_data['股票代碼'])
        
        # 三個策略的交集
        three_way_intersection = canslim_stocks & mastiff_stocks & second_high_stocks
        print(f"  🎯 三策略交集預期結果: {list(three_way_intersection)}")
        
        # 兩兩交集
        canslim_mastiff = canslim_stocks & mastiff_stocks
        canslim_second = canslim_stocks & second_high_stocks
        mastiff_second = mastiff_stocks & second_high_stocks
        
        print(f"  🔗 CANSLIM + 藏獒: {list(canslim_mastiff)}")
        print(f"  🔗 CANSLIM + 二次創高: {list(canslim_second)}")
        print(f"  🔗 藏獒 + 二次創高: {list(mastiff_second)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模擬策略結果失敗: {e}")
        return False

def create_usage_guide():
    """創建使用指南"""
    guide = """
# 🔗 策略交集分析使用指南

## 🚀 快速開始

### 步驟1: 執行策略
1. 在主界面選擇策略（如：CANSLIM量價齊升）
2. 點擊「執行策略」
3. 等待策略完成
4. 重複執行其他策略（如：藏獒、二次創高股票）

### 步驟2: 分析交集
1. 切換到「🔗 策略交集」標籤頁
2. 勾選要分析的策略（建議選擇2-3個）
3. 點擊「🎯 計算交集」
4. 查看共同選中的股票

### 步驟3: 查看結果
- 📈 交集股票：同時被多個策略選中的股票
- 📊 各策略統計：每個策略的股票數量和交集比例
- 🔗 兩兩交集：任意兩個策略的交集情況
- 🎯 獨有股票：每個策略獨有的股票

## 💡 使用技巧

### 🎯 推薦策略組合
1. **積極型**: CANSLIM + 藏獒 + 二次創高
2. **穩健型**: 勝率73.45% + 膽小貓
3. **動能型**: 藏獒 + 二次創高

### 📊 結果解讀
- **交集股票多**: 表示多個策略都看好，可信度高
- **交集股票少**: 可能需要調整策略組合
- **無交集**: 策略差異大，建議分別考慮

### 🔍 進階分析
1. 點擊「🔍 分析所有組合」查看所有可能的策略組合
2. 使用「📁 導出結果」保存分析結果
3. 定期重新分析以適應市場變化

## ⚠️ 注意事項
- 必須先執行策略才能進行交集分析
- 建議至少選擇2個策略進行比較
- 交集結果會隨市場條件變化

## 🎉 預期效果
通過策略交集分析，您可以：
- 找到最有潛力的股票（多策略認同）
- 提高選股的可信度
- 優化投資組合配置
- 降低單一策略的風險
"""
    
    with open("策略交集分析使用指南.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print(f"\n📖 使用指南已保存到: 策略交集分析使用指南.md")

def main():
    """主函數"""
    print("🚀 策略交集GUI功能測試")
    print("=" * 50)
    
    # 測試GUI功能
    success = test_intersection_gui()
    
    # 創建使用指南
    create_usage_guide()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 策略交集GUI功能測試通過")
        print("\n🎊 恭喜！您現在可以使用策略交集分析功能了！")
        
        print(f"\n💡 使用步驟:")
        steps = [
            "啟動主程式",
            "執行2-3個不同的策略",
            "切換到「🔗 策略交集」標籤頁",
            "選擇要分析的策略",
            "點擊「🎯 計算交集」",
            "查看共同選中的股票"
        ]
        
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step}")
            
        print(f"\n🎯 特別推薦:")
        print(f"  嘗試分析 CANSLIM + 藏獒 + 二次創高 的交集")
        print(f"  這個組合通常能找到最有潛力的成長股！")
        
    else:
        print("❌ 策略交集GUI功能測試失敗")
        print("請檢查代碼整合是否正確")

if __name__ == "__main__":
    main()
