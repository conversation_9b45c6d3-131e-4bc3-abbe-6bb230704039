# 🎨 GUI界面重新設計

## 📋 您的需求整理

根據您的要求，重新設計了更簡潔的GUI界面：

### 🎯 **新的欄位設計**：

| 欄位 | 內容 | 說明 |
|------|------|------|
| **欄位1** | 檔案名稱 | 只顯示檔案名稱（如：股價資料），不顯示完整路徑 |
| **欄位2** | 資料範圍 | 顯示現有資料的日期範圍（如：2007-04-23 至 2022-08-30） |
| **欄位3** | 更新範圍 | 可編輯的日期輸入框（開始日期 至 結束日期） |
| **欄位4** | 操作 | 包含"更新"按鈕和"開啟目錄"按鈕 |

## ✅ **已完成的修正**：

### 1. **表格標題簡化**：
```
舊版：['目錄及檔案名稱', '資料範圍', '建議更新範圍', '更新', '開啟目錄']
新版：['檔案名稱', '資料範圍', '更新範圍', '操作']
```

### 2. **檔案名稱顯示**：
```python
# 舊版：顯示完整路徑
full_path = info['filepath']  # D:\Finlab\history\tables\price.pkl

# 新版：只顯示友善名稱
display_name = info.get('display_name', filename)  # 股價資料
```

### 3. **按鈕整合**：
```python
# 新版：將兩個按鈕放在同一個框架中
button_frame = ttk.Frame(self.table_frame, relief='solid', borderwidth=1)
update_btn.pack(side='left', padx=2)      # 更新按鈕
open_dir_btn.pack(side='left', padx=2)    # 開啟目錄按鈕
```

### 4. **列權重優化**：
```python
table_frame.columnconfigure(0, weight=2)  # 檔案名稱
table_frame.columnconfigure(1, weight=2)  # 資料範圍
table_frame.columnconfigure(2, weight=3)  # 更新範圍
table_frame.columnconfigure(3, weight=2)  # 操作按鈕
```

## 🎮 **新GUI界面預覽**：

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│  檔案名稱        │ 資料範圍                    │ 更新範圍              │ 操作           │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 股價資料         │ 2007-04-23 至 2022-08-30   │ [2022-08-31] 至       │ [更新] [開啟目錄] │
│                  │                             │ [2025-07-20]          │                │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 本益比           │ 2023-07-21 至 2025-07-20   │ [2025-07-21] 至       │ [更新] [開啟目錄] │
│                  │                             │ [2025-07-20]          │                │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ 三大法人買賣超   │ 2012-05-02 至 2022-08-30   │ [2022-08-31] 至       │ [更新] [開啟目錄] │
│                  │                             │ [2025-07-20]          │                │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

## 🎯 **設計優勢**：

1. **✅ 更簡潔**：4個欄位取代原來的5個欄位
2. **✅ 更清楚**：檔案名稱使用友善名稱而非完整路徑
3. **✅ 更整齊**：操作按鈕整合在同一欄位
4. **✅ 更直觀**：欄位名稱更符合使用者理解

## 🚀 **使用方式**：

1. **查看資料**：在"資料範圍"欄位查看現有資料的日期範圍
2. **設定更新**：在"更新範圍"欄位輸入要更新的日期範圍
3. **執行更新**：點擊"更新"按鈕開始爬取資料
4. **開啟目錄**：點擊"開啟目錄"按鈕查看檔案位置

**現在GUI界面更加簡潔清楚！** 🎉
