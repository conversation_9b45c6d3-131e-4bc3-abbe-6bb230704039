#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 pe.pkl 檔案的存儲期間
"""

import pandas as pd
import os
from datetime import datetime

def check_pe_period():
    """檢查 pe.pkl 的存儲期間"""
    file_path = r"D:\Finlab\backup\O3mh_strategy2AA\history\tables\pe.pkl"
    
    print("🔍 檢查 PE 檔案存儲期間")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f"📁 檔案資訊:")
        print(f"   路徑: {file_path}")
        print(f"   大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
        print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
        
        # 讀取資料
        print(f"\n📖 正在讀取資料...")
        data = pd.read_pickle(file_path)
        
        print(f"✅ 讀取成功:")
        print(f"   資料筆數: {len(data):,}")
        print(f"   資料類型: {type(data)}")
        
        # 檢查索引
        if hasattr(data, 'index'):
            print(f"   索引類型: {type(data.index)}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
        
        # 檢查欄位
        if hasattr(data, 'columns'):
            print(f"   欄位數量: {len(data.columns)}")
            print(f"   欄位名稱: {list(data.columns)[:3]}...")
        
        # 檢查日期範圍
        if hasattr(data, 'index') and hasattr(data.index, 'names') and 'date' in data.index.names:
            print(f"\n📅 日期分析:")
            dates = data.index.get_level_values('date')
            
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f"   ✅ 存儲期間: {start_date} 至 {end_date}")
            print(f"   時間跨度: {end_date.year - start_date.year + 1} 年")
            print(f"   唯一日期數: {unique_dates}")
            
            # 計算資料密度
            total_days = (end_date - start_date).days + 1
            coverage = unique_dates / total_days * 100
            print(f"   資料密度: {coverage:.1f}%")
            
            # 顯示前幾筆和後幾筆資料的日期
            print(f"\n📊 日期分布:")
            unique_dates_sorted = sorted(dates.unique())
            print(f"   最早幾個日期: {unique_dates_sorted[:3]}")
            print(f"   最晚幾個日期: {unique_dates_sorted[-3:]}")
            
        else:
            print(f"\n⚠️ 未找到標準的日期索引")
            print(f"   索引內容預覽: {data.index[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 讀取失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    check_pe_period()
