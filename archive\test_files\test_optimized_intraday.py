#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化後的日內策略
"""

import sys
import os
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def generate_test_stock_data(stock_id, scenario="bullish"):
    """生成測試用股票數據"""
    np.random.seed(42)  # 固定隨機種子以便重現
    
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    
    if scenario == "bullish":
        # 上漲趨勢數據
        base_price = 50.0
        trend = np.linspace(0, 10, 30)  # 30天上漲10元
        noise = np.random.normal(0, 1, 30)
        closes = base_price + trend + noise
        
        # 確保價格合理
        closes = np.maximum(closes, 30.0)
        
        # 生成其他價格
        opens = closes * (0.98 + np.random.random(30) * 0.04)  # 開盤價在收盤價±2%
        highs = closes * (1.01 + np.random.random(30) * 0.03)  # 最高價比收盤價高1-4%
        lows = closes * (0.97 - np.random.random(30) * 0.03)   # 最低價比收盤價低0-3%
        
        # 成交量：上漲時放大
        volumes = 50000 + np.random.randint(0, 100000, 30)
        
    elif scenario == "consolidation":
        # 整理數據
        base_price = 45.0
        noise = np.random.normal(0, 0.5, 30)
        closes = base_price + noise
        
        opens = closes * (0.99 + np.random.random(30) * 0.02)
        highs = closes * (1.005 + np.random.random(30) * 0.015)
        lows = closes * (0.985 - np.random.random(30) * 0.015)
        
        volumes = 30000 + np.random.randint(0, 50000, 30)
        
    else:  # bearish
        # 下跌趨勢數據
        base_price = 40.0
        trend = np.linspace(0, -8, 30)  # 30天下跌8元
        noise = np.random.normal(0, 0.8, 30)
        closes = base_price + trend + noise
        
        closes = np.maximum(closes, 20.0)  # 最低20元
        
        opens = closes * (1.01 - np.random.random(30) * 0.02)
        highs = closes * (1.02 - np.random.random(30) * 0.01)
        lows = closes * (0.96 - np.random.random(30) * 0.04)
        
        volumes = 40000 + np.random.randint(0, 60000, 30)
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': opens,
        'High': highs,
        'Low': lows,
        'Close': closes,
        'Volume': volumes.astype(int)
    })
    
    return df

def test_strategy_scenarios():
    """測試不同市場情境下的策略表現"""
    print("🧪 測試優化後的日內策略")
    print("=" * 50)
    
    strategy = IntradayOpeningRangeStrategy()
    
    test_cases = [
        {"stock_id": "2330", "scenario": "bullish", "name": "上漲趨勢"},
        {"stock_id": "2317", "scenario": "consolidation", "name": "整理格局"},
        {"stock_id": "2454", "scenario": "bearish", "name": "下跌趨勢"},
        {"stock_id": "1301", "scenario": "bullish", "name": "強勢上漲"},
        {"stock_id": "2303", "scenario": "consolidation", "name": "震盪整理"},
    ]
    
    results_summary = {
        'total': 0,
        'pre_market_passed': 0,
        'buy_signals': 0,
        'wait_signals': 0,
        'no_signals': 0
    }
    
    for case in test_cases:
        print(f"\n📊 測試 {case['stock_id']} - {case['name']}")
        print("-" * 30)
        
        # 生成測試數據
        test_data = generate_test_stock_data(case['stock_id'], case['scenario'])
        
        # 計算指標
        test_data = strategy.calculate_indicators(test_data)
        
        # 檢查盤前條件
        pre_market_ok, reason = strategy.check_pre_market_conditions(test_data)
        
        # 顯示關鍵數據
        latest = test_data.iloc[-1]
        prev_close = test_data.iloc[-2]['Close']
        ma5 = latest['MA5']
        volume = latest['Volume']
        avg_vol = latest['AvgVol']
        
        ma5_ratio = prev_close / ma5 if ma5 > 0 else 0
        vol_ratio = volume / avg_vol if avg_vol > 0 else 0
        
        print(f"  💰 收盤價: {prev_close:.2f}")
        print(f"  📊 MA5: {ma5:.2f} (比率: {ma5_ratio:.3f})")
        print(f"  📈 成交量: {volume:,.0f} (比率: {vol_ratio:.2f})")
        print(f"  🎯 盤前篩選: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
        print(f"  📝 詳細評分: {reason}")
        
        results_summary['total'] += 1
        
        if pre_market_ok:
            results_summary['pre_market_passed'] += 1
            
            # 模擬盤中交易
            result = strategy.simulate_breakout_trading(case['stock_id'], datetime.datetime.now())
            
            if result:
                result_type = result.get('result', 'unknown')
                signal_reason = result.get('reason', '未知')
                
                print(f"  🚀 交易信號: {result_type}")
                print(f"  📋 信號原因: {signal_reason}")
                
                if result_type == 'buy_signal':
                    results_summary['buy_signals'] += 1
                    signal_details = result.get('signal_details', {})
                    confidence = signal_details.get('confidence', 0)
                    print(f"  ⭐ 信心度: {confidence}%")
                elif result_type == 'wait_signal':
                    results_summary['wait_signals'] += 1
                else:
                    results_summary['no_signals'] += 1
            else:
                print("  ❌ 無法生成交易信號")
                results_summary['no_signals'] += 1
        else:
            print("  ⏭️ 跳過交易信號分析")
    
    # 顯示總結
    print("\n" + "=" * 50)
    print("📈 策略測試總結")
    print("=" * 50)
    
    total = results_summary['total']
    pre_passed = results_summary['pre_market_passed']
    buy_signals = results_summary['buy_signals']
    wait_signals = results_summary['wait_signals']
    no_signals = results_summary['no_signals']
    
    print(f"總測試股票: {total}")
    print(f"通過盤前篩選: {pre_passed} ({pre_passed/total*100:.1f}%)")
    print(f"產生買入信號: {buy_signals} ({buy_signals/total*100:.1f}%)")
    print(f"產生等待信號: {wait_signals} ({wait_signals/total*100:.1f}%)")
    print(f"無明確信號: {no_signals} ({no_signals/total*100:.1f}%)")
    
    # 評估優化效果
    print(f"\n🎯 優化效果評估:")
    if buy_signals > 0:
        print(f"✅ 成功產生 {buy_signals} 個買入信號")
    else:
        print("❌ 仍未產生買入信號，需要進一步優化")
    
    if pre_passed / total > 0.6:
        print(f"✅ 盤前篩選通過率 {pre_passed/total*100:.1f}% (目標>60%)")
    else:
        print(f"⚠️ 盤前篩選通過率 {pre_passed/total*100:.1f}% 偏低")

if __name__ == "__main__":
    test_strategy_scenarios()
