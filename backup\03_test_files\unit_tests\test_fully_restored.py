#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完全恢復的版本
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_fully_restored():
    """測試完全恢復的版本"""
    print("🔄 測試完全恢復的版本（回到最初能工作的狀態）")
    print("=" * 60)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 檢查設定
        print(f"📊 請求間隔: {fetcher.min_request_interval}秒")
        print("📊 使用簡單的yfinance.history()方法")
        print("📊 移除複雜的重試和錯誤處理")
        
        # 測試美股指數
        print("\n📈 測試美股指數（完全恢復版本）...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({us_time:.1f}秒):")
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
        
        # 測試中國股市
        print("\n🇨🇳 測試中國股市指數（完全恢復版本）...")
        china_start = time.time()
        china_data = fetcher.get_china_indices_real()
        china_time = time.time() - china_start
        
        if china_data:
            print(f"✅ 中國股市指數獲取成功 ({china_time:.1f}秒):")
            for name, data in china_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 中國股市指數獲取失敗 ({china_time:.1f}秒)")
        
        # 測試外匯匯率
        print("\n💱 測試外匯匯率（完全恢復版本）...")
        fx_start = time.time()
        fx_data = fetcher.get_enhanced_fx_rates()
        fx_time = time.time() - fx_start
        
        if fx_data:
            print(f"✅ 外匯匯率獲取成功 ({fx_time:.1f}秒):")
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 外匯匯率獲取失敗 ({fx_time:.1f}秒)")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        # 統計成功率
        categories = [
            ('美股指數', us_data),
            ('中國股市', china_data),
            ('外匯匯率', fx_data)
        ]
        
        success_count = sum(1 for _, data in categories if data)
        total_count = len(categories)
        success_rate = (success_count / total_count) * 100
        
        print(f"📊 成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_rate >= 66.7  # 至少2/3成功
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner_restored():
    """測試安全掃描器恢復版本"""
    print("\n🔍 測試安全掃描器恢復版本...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 快速測試美股
        print("📊 快速測試美股指數...")
        start_time = time.time()
        
        us_data = scanner.get_us_indices()
        
        scan_time = time.time() - start_time
        
        if us_data:
            success_count = 0
            for name, data in us_data.items():
                status = data.get('status', '')
                if '真實數據' in status:
                    success_count += 1
                    price = data.get('price', 0)
                    change_pct = data.get('change_pct', 0)
                    print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
            
            print(f"\n📊 成功獲取: {success_count}/{len(us_data)} 項")
            print(f"⏱️ 掃描耗時: {scan_time:.1f}秒")
            
            return success_count > 0
        else:
            print("❌ 美股指數獲取失敗")
            return False
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔄 完全恢復版本測試")
    print("=" * 70)
    
    tests = [
        ("完全恢復版本", test_fully_restored),
        ("安全掃描器恢復", test_safe_scanner_restored)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n開始測試: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 70)
    print("📋 完全恢復版本總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🔄 完全恢復的設定:")
    print("• 請求間隔：2.5秒")
    print("• 隨機延遲：2.0-4.0秒")
    print("• 使用簡單的 yf.Ticker(symbol).history(period='2d')")
    print("• 移除複雜的 _safe_yfinance_request 方法")
    print("• 移除過度的重試和錯誤處理")
    print("• 恢復到最初能工作的狀態")
    
    print("\n💡 保留的功能:")
    print("• ✅ 基本的反爬蟲措施")
    print("• ✅ 顏色顯示功能")
    print("• ✅ 線程處理（避免UI凍結）")
    print("• ✅ 簡單穩定的數據獲取")
    
    print("\n❌ 移除的複雜功能:")
    print("• 複雜的重試機制")
    print("• 過度的頻率限制檢測")
    print("• 多重備援代碼")
    print("• 網頁爬取備援（暫時移除）")
    
    if passed > 0:
        print("\n🎉 完全恢復成功！")
        print("現在回到最初能正常工作的簡單版本。")
    else:
        print("\n⚠️ 需要進一步檢查")

if __name__ == "__main__":
    main()
