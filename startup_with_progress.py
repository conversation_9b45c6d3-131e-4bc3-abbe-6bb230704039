#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台股智能選股系統 - 帶進度條的啟動器
解決啟動時間過長和缺少反饋的問題
"""

import sys
import os
import time
import threading
from PyQt6.QtWidgets import (QApplication, QSplashScreen, QProgressBar, 
                            QVBoxLayout, QLabel, QWidget, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor

class StartupProgress(QObject):
    """啟動進度信號"""
    progress_updated = pyqtSignal(int, str)
    startup_completed = pyqtSignal()
    startup_failed = pyqtSignal(str)

class CustomSplashScreen(QSplashScreen):
    """自定義啟動畫面"""
    
    def __init__(self):
        # 創建一個簡單的啟動畫面
        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor(45, 45, 45))
        
        # 在pixmap上繪製內容
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        
        # 標題
        title_font = QFont("Microsoft YaHei", 24, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.drawText(50, 100, "🚀 台股智能選股系統")
        
        # 版本信息
        version_font = QFont("Microsoft YaHei", 12)
        painter.setFont(version_font)
        painter.drawText(50, 140, "版本 v22.0 - 優化增強版")
        
        # 載入提示
        painter.drawText(50, 180, "正在初始化系統組件...")
        painter.drawText(50, 210, "請稍候，首次啟動可能需要較長時間")
        
        painter.end()
        
        super().__init__(pixmap)
        
        # 設置進度條
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(50, 320, 500, 25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 5px;
                text-align: center;
                background-color: #333;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        # 狀態標籤
        self.status_label = QLabel("正在啟動...", self)
        self.status_label.setGeometry(50, 280, 500, 30)
        self.status_label.setStyleSheet("color: white; font-size: 12px;")
        
    def update_progress(self, value, message):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.repaint()
        QApplication.processEvents()

class StartupManager:
    """啟動管理器"""
    
    def __init__(self):
        self.progress = StartupProgress()
        self.splash = None
        
    def show_splash(self):
        """顯示啟動畫面"""
        self.splash = CustomSplashScreen()
        self.splash.show()
        
        # 連接進度信號
        self.progress.progress_updated.connect(self.splash.update_progress)
        self.progress.startup_completed.connect(self.on_startup_completed)
        self.progress.startup_failed.connect(self.on_startup_failed)
        
        return self.splash
    
    def start_loading(self):
        """開始載入過程"""
        # 在新線程中執行載入
        loading_thread = threading.Thread(target=self._loading_process, daemon=True)
        loading_thread.start()
    
    def _loading_process(self):
        """載入過程"""
        try:
            steps = [
                (10, "檢查系統環境..."),
                (20, "載入核心模組..."),
                (30, "初始化數據庫連接..."),
                (40, "載入股票篩選器..."),
                (50, "初始化市場掃描器..."),
                (60, "載入策略模組..."),
                (70, "初始化圖表組件..."),
                (80, "載入GUI界面..."),
                (90, "完成系統初始化..."),
                (95, "準備啟動主程式..."),
            ]
            
            for progress, message in steps:
                self.progress.progress_updated.emit(progress, message)
                time.sleep(0.5)  # 模擬載入時間
            
            # 實際載入主程式
            self.progress.progress_updated.emit(98, "載入主程式...")
            
            # 這裡是關鍵：實際導入主程式
            try:
                from O3mh_gui_v21_optimized import StockScreenerGUI, main
                self.main_function = main
                self.progress.progress_updated.emit(100, "啟動完成！")
                time.sleep(0.5)
                self.progress.startup_completed.emit()
            except Exception as e:
                self.progress.startup_failed.emit(f"載入主程式失敗: {str(e)}")
                
        except Exception as e:
            self.progress.startup_failed.emit(f"啟動過程發生錯誤: {str(e)}")
    
    def on_startup_completed(self):
        """啟動完成"""
        if self.splash:
            self.splash.close()
        
        # 啟動主程式
        try:
            self.main_function()
        except Exception as e:
            QMessageBox.critical(None, "啟動錯誤", f"主程式啟動失敗:\n{str(e)}")
    
    def on_startup_failed(self, error_message):
        """啟動失敗"""
        if self.splash:
            self.splash.close()
        
        QMessageBox.critical(None, "啟動失敗", f"系統啟動失敗:\n{error_message}")
        sys.exit(1)

def main():
    """主函數"""
    # 創建應用程式
    app = QApplication(sys.argv)
    app.setApplicationName("台股智能選股系統")
    app.setApplicationVersion("22.0")
    
    # 設置應用程式樣式
    app.setStyleSheet("""
        QWidget {
            font-family: "Microsoft YaHei";
        }
    """)
    
    # 創建啟動管理器
    startup_manager = StartupManager()
    
    # 顯示啟動畫面
    splash = startup_manager.show_splash()
    
    # 開始載入過程
    startup_manager.start_loading()
    
    # 運行應用程式
    try:
        sys.exit(app.exec())
    except SystemExit:
        pass
    except Exception as e:
        QMessageBox.critical(None, "系統錯誤", f"應用程式運行時發生錯誤:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
