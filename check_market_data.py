#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查市場數據爬蟲的結果
"""

import sqlite3
import pandas as pd
import os

def check_database(db_path, db_name, table_name):
    """檢查資料庫內容"""
    
    db_file = os.path.join(db_path, f"{db_name}.db")
    
    if not os.path.exists(db_file):
        print(f"❌ 資料庫不存在: {db_file}")
        return
    
    print(f"\n📊 檢查 {db_name}.db - {table_name}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查表格是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📋 資料庫中的表格: {[table[0] for table in tables]}")
        
        # 讀取數據
        df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        
        print(f"📊 資料筆數: {len(df)}")
        print(f"📋 欄位: {list(df.columns)}")
        
        if not df.empty:
            print(f"\n📋 前3筆資料:")
            for i, row in df.head(3).iterrows():
                print(f"   記錄 {i+1}:")
                for col, val in row.items():
                    print(f"     {col}: {val}")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def main():
    """主函數"""
    
    print("🔍 檢查市場數據爬蟲結果")
    
    db_path = "D:/Finlab/history/tables"
    
    # 檢查各個資料庫
    databases = [
        ("market_index", "market_index_info"),
        ("margin_trading", "margin_trading_info"),
        ("market_historical", "market_historical_index")
    ]
    
    for db_name, table_name in databases:
        check_database(db_path, db_name, table_name)

if __name__ == "__main__":
    main()
