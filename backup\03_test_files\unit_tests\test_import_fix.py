#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試導入修復
"""

import sys
import pandas as pd
from datetime import datetime

def test_import_fix():
    """測試導入修復"""
    
    print("🔧 測試高殖利率烏龜策略導入修復")
    print("=" * 50)
    
    # 1. 測試直接導入
    print("1️⃣ 測試直接導入策略類...")
    
    try:
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
        print("   ✅ 成功導入 HighYieldTurtleStrategyOptimized")
        
        # 創建實例
        strategy = HighYieldTurtleStrategyOptimized()
        print("   ✅ 成功創建策略實例")
        
    except ImportError as e:
        print(f"   ❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 創建實例失敗: {e}")
        return False
    
    # 2. 測試舊的錯誤導入（應該失敗）
    print("\n2️⃣ 測試舊的錯誤導入（預期失敗）...")
    
    try:
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        print("   ⚠️ 意外成功：舊類名仍然存在")
    except ImportError as e:
        print(f"   ✅ 預期的導入失敗: {e}")
    
    # 3. 模擬GUI中的導入邏輯
    print("\n3️⃣ 模擬GUI中的導入邏輯...")
    
    class MockGUI:
        def __init__(self):
            self._turtle_strategy_instance = None
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬修復後的導入邏輯"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據"

                # 使用單例模式避免重複載入PKL數據
                if not hasattr(self, '_turtle_strategy_instance') or self._turtle_strategy_instance is None:
                    from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
                    self._turtle_strategy_instance = HighYieldTurtleStrategyOptimized()
                    print("   ✅ 成功創建策略實例")

                turtle_strategy = self._turtle_strategy_instance

                # 執行高殖利率烏龜分析
                result = turtle_strategy.analyze_stock(df, stock_id=stock_id)

                # 提取詳細信息用於表格顯示
                details = result.get('details', {})
                
                # 構建詳細的返回信息
                detailed_info = {
                    'suitable': result['suitable'],
                    'reason': result['reason'],
                    'score': result['score'],
                    'dividend_yield': details.get('dividend_yield', 0),
                    'pe_ratio': details.get('pe_ratio', 0),
                    'pb_ratio': details.get('pb_ratio', 0),
                    'close_price': details.get('close_price', 0),
                    'volume': details.get('volume', 0),
                    'data_source': details.get('data_source', 'FinLab PKL')
                }

                # 轉換為舊格式的返回值，但附加詳細信息
                if result['suitable']:
                    return True, result['reason'], detailed_info
                else:
                    return False, result['reason'], detailed_info
                    
            except ImportError as e:
                print(f"   ❌ 導入失敗: {e}")
                return False, f"導入錯誤: {str(e)}", {}
            except Exception as e:
                print(f"   ❌ 策略檢查失敗: {e}")
                return False, f"策略檢查錯誤: {str(e)}", {}
    
    # 4. 測試完整流程
    print("\n4️⃣ 測試完整的策略分析流程...")
    
    # 創建測試數據
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    
    mock_gui = MockGUI()
    
    # 測試幾支股票
    test_stocks = ['2881', '2882', '1108']
    
    for stock_id in test_stocks:
        try:
            print(f"   📊 測試股票: {stock_id}")
            result = mock_gui.check_high_yield_turtle_strategy(test_df, stock_id=stock_id)
            
            if len(result) == 3:
                matched, message, detailed_info = result
                score = detailed_info.get('score', 0)
                dividend = detailed_info.get('dividend_yield', 0)
                status = "✅ 符合" if matched else "❌ 不符合"
                print(f"      {status} - 評分: {score}, 殖利率: {dividend:.2f}%")
            else:
                matched, message = result
                status = "✅ 符合" if matched else "❌ 不符合"
                print(f"      {status} - {message}")
                
        except Exception as e:
            print(f"      ❌ 測試失敗: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"✅ 導入修復測試完成！")
    
    return True

if __name__ == "__main__":
    success = test_import_fix()
    
    if success:
        print(f"\n🎉 修復測試總結:")
        print(f"✅ 策略類導入正常")
        print(f"✅ 實例創建成功")
        print(f"✅ 策略分析功能正常")
        print(f"✅ GUI中的導入錯誤已修復")
        print(f"🚀 現在可以正常使用高殖利率烏龜策略了！")
    else:
        print(f"\n❌ 修復測試失敗，需要進一步檢查")
