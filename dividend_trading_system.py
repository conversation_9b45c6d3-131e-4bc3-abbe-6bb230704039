#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息交易系統 - 基於蘇松泙除權息操作策略
實現程式化的除權息交易決策和風險控制
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sqlite3
import requests
from typing import Dict, List, Tuple, Optional
import json
import os

class DividendTradingSystem:
    """除權息交易系統主類"""
    
    def __init__(self, db_path: str = "dividend_trading.db"):
        self.db_path = db_path
        self.logger = self._setup_logger()
        self.init_database()
        
        # 績優股清單（特殊策略）
        self.premium_stocks = {
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科',
            '2412': '中華電',
            '1301': '台塑'
        }
        
        # 市場行情評估狀態
        self.market_sentiment = None
        self.position_size_ratio = 1.0  # 資金投入比例
        
    def _setup_logger(self):
        """設置日誌系統"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dividend_trading.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def init_database(self):
        """初始化數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 除權息資料表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dividend_schedule (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    ex_date DATE NOT NULL,
                    cash_dividend REAL,
                    stock_dividend REAL,
                    last_close_price REAL,
                    ex_open_price REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, ex_date)
                )
            ''')
            
            # 交易記錄表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    action TEXT NOT NULL,  -- BUY, SELL
                    price REAL NOT NULL,
                    quantity INTEGER NOT NULL,
                    trade_date DATE NOT NULL,
                    strategy TEXT,
                    profit_loss REAL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 市場行情評估表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_sentiment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    evaluation_date DATE NOT NULL,
                    sentiment_score REAL,  -- 0-100分
                    leading_stocks_performance TEXT,  -- JSON格式
                    position_size_ratio REAL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")

    def get_dividend_data_from_db(self) -> pd.DataFrame:
        """從除權息資料庫獲取資料"""
        try:
            # 除權息資料庫路徑
            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"

            if not os.path.exists(dividend_db_path):
                self.logger.warning(f"⚠️ 除權息資料庫不存在: {dividend_db_path}")
                return pd.DataFrame()

            conn = sqlite3.connect(dividend_db_path)

            # 獲取6月份的率先除權息股票資料
            # 支援多種日期格式：2025-06-01 和 '25/06/01
            query = """
                SELECT stock_code, stock_name, ex_dividend_date, cash_dividend, total_dividend
                FROM dividend_data
                WHERE ex_dividend_date IS NOT NULL
                AND (ex_dividend_date LIKE '2025-06%' OR ex_dividend_date LIKE '%/06/%' OR ex_dividend_date LIKE '%-06-%')
                ORDER BY ex_dividend_date
            """

            df = pd.read_sql_query(query, conn)
            conn.close()

            if not df.empty:
                # 重命名欄位以符合系統需求
                df = df.rename(columns={'ex_dividend_date': 'ex_date'})
                df['ex_date'] = pd.to_datetime(df['ex_date'])

                # 如果沒有現金股利，使用總股利
                df['cash_dividend'] = df['cash_dividend'].fillna(df['total_dividend'])
                df['cash_dividend'] = df['cash_dividend'].fillna(0)

                self.logger.info(f"✅ 從除權息資料庫獲取 {len(df)} 筆6月份資料")
            else:
                self.logger.warning("⚠️ 除權息資料庫中無6月份資料")

            return df

        except Exception as e:
            self.logger.error(f"❌ 從除權息資料庫獲取資料失敗: {e}")
            return pd.DataFrame()

    def fetch_dividend_schedule(self) -> pd.DataFrame:
        """從除權息資料庫獲取除權息時間表"""
        try:
            self.logger.info("📊 正在從除權息資料庫獲取時間表...")

            # 嘗試從除權息資料庫獲取資料
            dividend_data = self.get_dividend_data_from_db()

            if not dividend_data.empty:
                # 保存到本地數據庫
                self._save_dividend_schedule(dividend_data)
                self.logger.info(f"✅ 從資料庫獲取除權息時間表完成，共 {len(dividend_data)} 筆記錄")
                return dividend_data

            # 如果資料庫沒有資料，使用示例資料
            self.logger.warning("⚠️ 除權息資料庫無資料，使用示例資料")
            sample_data = [
                {'stock_code': '3443', 'stock_name': '創意', 'ex_date': '2025-06-01', 'cash_dividend': 5.0},
                {'stock_code': '8454', 'stock_name': '富邦媒', 'ex_date': '2025-06-04', 'cash_dividend': 8.0},
                {'stock_code': '2330', 'stock_name': '台積電', 'ex_date': '2025-06-25', 'cash_dividend': 8.0},
                {'stock_code': '2317', 'stock_name': '鴻海', 'ex_date': '2025-07-25', 'cash_dividend': 2.0},
                {'stock_code': '2454', 'stock_name': '聯發科', 'ex_date': '2025-07-15', 'cash_dividend': 3.0},
            ]

            df = pd.DataFrame(sample_data)
            df['ex_date'] = pd.to_datetime(df['ex_date'])

            # 保存到數據庫
            self._save_dividend_schedule(df)

            self.logger.info(f"✅ 使用示例除權息時間表，共 {len(df)} 筆記錄")
            return df

        except Exception as e:
            self.logger.error(f"❌ 獲取除權息時間表失敗: {e}")
            return pd.DataFrame()
    
    def _save_dividend_schedule(self, df: pd.DataFrame):
        """保存除權息時間表到數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            for _, row in df.iterrows():
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO dividend_schedule 
                    (stock_code, stock_name, ex_date, cash_dividend, stock_dividend)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    row['stock_code'],
                    row['stock_name'],
                    row['ex_date'].strftime('%Y-%m-%d'),
                    row['cash_dividend'],
                    row.get('stock_dividend', 0.0)
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 保存除權息資料失敗: {e}")
    
    def evaluate_market_sentiment(self) -> Dict:
        """步驟1: 評估除權息行情好壞"""
        try:
            self.logger.info("🔍 開始評估除權息市場行情...")
            
            # 獲取率先除權息的股票（6月份）
            df = self.fetch_dividend_schedule()
            early_stocks = df[df['ex_date'].dt.month == 6].sort_values('ex_date')
            
            if early_stocks.empty:
                self.logger.warning("⚠️ 未找到6月份除權息股票")
                return {'sentiment_score': 50, 'status': 'neutral'}
            
            sentiment_scores = []
            performance_data = {}
            
            for _, stock in early_stocks.iterrows():
                stock_code = stock['stock_code']
                stock_name = stock['stock_name']
                
                # 獲取股票價格資料（這裡需要整合股價API）
                performance = self._analyze_stock_performance(stock_code, stock['ex_date'])
                performance_data[stock_code] = performance
                
                # 計算表現分數
                if performance['filled_dividend']:
                    if performance['extra_gain'] > 0:
                        score = 90  # 填息且額外上漲
                    else:
                        score = 70  # 僅填息
                elif performance['same_day_gain'] > 0:
                    score = 60  # 當日上漲但未填息
                else:
                    score = 30  # 貼息
                
                sentiment_scores.append(score)
                
                self.logger.info(f"📈 {stock_name}({stock_code}) 表現分數: {score}")
            
            # 計算整體市場情緒
            avg_score = np.mean(sentiment_scores) if sentiment_scores else 50
            
            # 特別關注權值股表現
            if '2330' in performance_data:
                tsmc_performance = performance_data['2330']
                if tsmc_performance['filled_dividend'] and tsmc_performance['extra_gain'] > 0:
                    avg_score += 10  # 台積電表現好，加分
                elif not tsmc_performance['filled_dividend']:
                    avg_score -= 15  # 台積電表現差，扣分
            
            # 決定投資策略
            if avg_score >= 70:
                sentiment = 'bullish'
                position_ratio = 1.0  # 積極投入
                strategy = "行情良好，積極操作"
            elif avg_score >= 50:
                sentiment = 'neutral'
                position_ratio = 0.6  # 謹慎投入
                strategy = "行情普通，謹慎操作"
            else:
                sentiment = 'bearish'
                position_ratio = 0.3  # 保守投入
                strategy = "行情不佳，保守操作"
            
            self.market_sentiment = sentiment
            self.position_size_ratio = position_ratio
            
            # 保存評估結果
            self._save_market_sentiment(avg_score, performance_data, position_ratio, strategy)
            
            result = {
                'sentiment_score': avg_score,
                'sentiment': sentiment,
                'position_ratio': position_ratio,
                'strategy': strategy,
                'performance_data': performance_data
            }
            
            self.logger.info(f"📊 市場情緒評估完成: {sentiment} (分數: {avg_score:.1f})")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 市場情緒評估失敗: {e}")
            return {'sentiment_score': 50, 'status': 'error'}
    
    def _analyze_stock_performance(self, stock_code: str, ex_date: datetime) -> Dict:
        """分析個股除權息表現"""
        try:
            # 這裡應該整合實際的股價API
            # 模擬數據
            if stock_code == '3443':  # 創意
                return {
                    'pre_close': 255,
                    'ex_open': 250,
                    'ex_close': 264,
                    'same_day_gain': 14,
                    'filled_dividend': True,
                    'extra_gain': 9,  # 填息後額外漲幅
                    'fill_days': 0  # 當日填息
                }
            elif stock_code == '8454':  # 富邦媒
                return {
                    'pre_close': 240,
                    'ex_open': 232,
                    'ex_close': 232,
                    'same_day_gain': 0,
                    'filled_dividend': False,
                    'extra_gain': 0,
                    'fill_days': None
                }
            elif stock_code == '2330':  # 台積電
                return {
                    'pre_close': 227.5,
                    'ex_open': 219.5,
                    'ex_close': 218,
                    'same_day_gain': -1.5,
                    'filled_dividend': True,
                    'extra_gain': 10,  # 後續填息並上漲
                    'fill_days': 20  # 20個交易日後填息
                }
            else:
                # 預設表現
                return {
                    'pre_close': 100,
                    'ex_open': 95,
                    'ex_close': 96,
                    'same_day_gain': 1,
                    'filled_dividend': False,
                    'extra_gain': 0,
                    'fill_days': None
                }
                
        except Exception as e:
            self.logger.error(f"❌ 分析股票 {stock_code} 表現失敗: {e}")
            return {}
    
    def _save_market_sentiment(self, score: float, performance_data: Dict, 
                              position_ratio: float, strategy: str):
        """保存市場情緒評估結果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO market_sentiment 
                (evaluation_date, sentiment_score, leading_stocks_performance, 
                 position_size_ratio, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                datetime.now().strftime('%Y-%m-%d'),
                score,
                json.dumps(performance_data, ensure_ascii=False),
                position_ratio,
                strategy
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ 保存市場情緒評估失敗: {e}")
    
    def get_trading_candidates(self, days_ahead: int = 7) -> List[Dict]:
        """獲取未來N天內的除權息交易候選股票"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            start_date = datetime.now().strftime('%Y-%m-%d')
            end_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
            
            query = '''
                SELECT * FROM dividend_schedule 
                WHERE ex_date BETWEEN ? AND ?
                ORDER BY ex_date
            '''
            
            df = pd.read_sql_query(query, conn, params=[start_date, end_date])
            conn.close()
            
            candidates = []
            for _, row in df.iterrows():
                candidate = {
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name'],
                    'ex_date': row['ex_date'],
                    'cash_dividend': row['cash_dividend'],
                    'days_to_ex': (pd.to_datetime(row['ex_date']) - datetime.now()).days,
                    'is_premium_stock': row['stock_code'] in self.premium_stocks,
                    'recommended_action': self._get_trading_recommendation(row)
                }
                candidates.append(candidate)
            
            self.logger.info(f"📋 找到 {len(candidates)} 個交易候選股票")
            return candidates
            
        except Exception as e:
            self.logger.error(f"❌ 獲取交易候選失敗: {e}")
            return []
    
    def _get_trading_recommendation(self, stock_data: Dict) -> Dict:
        """根據策略給出交易建議"""
        stock_code = stock_data['stock_code']
        is_premium = stock_code in self.premium_stocks
        
        # 基本建議
        recommendation = {
            'entry_timing': '除權息前1-7天',
            'entry_method': '下午1:25分定格買進',
            'exit_strategy': '除權息當日根據表現決定',
            'position_size': self.position_size_ratio,
            'risk_level': 'medium'
        }
        
        # 績優股特殊策略
        if is_premium:
            if stock_code == '2330':  # 台積電
                recommendation.update({
                    'strategy': '先跌後漲策略',
                    'entry_timing': '除權息前7天分批買進',
                    'exit_strategy': '除權息前一日賣出一半，填息後再回補',
                    'risk_level': 'low',
                    'notes': '台積電通常會填息，可採取長期持有策略'
                })
            elif stock_code == '2317':  # 鴻海
                recommendation.update({
                    'strategy': '預設停利策略',
                    'exit_strategy': '設定除權息開盤價+1.5元預賣',
                    'risk_level': 'medium',
                    'notes': '鴻海填息時間可能較長，建議設定停利點'
                })
        
        # 根據市場情緒調整
        if self.market_sentiment == 'bearish':
            recommendation['position_size'] *= 0.5
            recommendation['risk_level'] = 'high'
            recommendation['notes'] = recommendation.get('notes', '') + ' (市場行情不佳，降低投入)'
        elif self.market_sentiment == 'bullish':
            recommendation['position_size'] = min(recommendation['position_size'] * 1.2, 1.0)
            recommendation['notes'] = recommendation.get('notes', '') + ' (市場行情良好，可積極操作)'
        
        return recommendation

    def execute_buy_signal(self, stock_code: str, target_date: str = None) -> Dict:
        """步驟2: 執行買進信號（下午1:25分定格買進）"""
        try:
            current_time = datetime.now()

            # 檢查是否為交易時間
            if current_time.hour == 13 and current_time.minute == 25:
                self.logger.info(f"⏰ 正在執行 {stock_code} 的1:25分定格買進...")

                # 這裡應該整合實際的交易API
                # 模擬買進邏輯
                result = self._simulate_buy_order(stock_code)

                if result['success']:
                    # 記錄交易
                    self._record_trade(
                        stock_code=stock_code,
                        action='BUY',
                        price=result['price'],
                        quantity=result['quantity'],
                        strategy='1:25分定格買進'
                    )

                    self.logger.info(f"✅ {stock_code} 買進成功: {result['quantity']}股 @ {result['price']}元")
                else:
                    self.logger.warning(f"⚠️ {stock_code} 買進失敗: {result['reason']}")

                return result
            else:
                return {
                    'success': False,
                    'reason': f'非交易時間 (當前: {current_time.strftime("%H:%M")})',
                    'next_opportunity': '下午1:25分'
                }

        except Exception as e:
            self.logger.error(f"❌ 執行買進信號失敗: {e}")
            return {'success': False, 'reason': str(e)}

    def _simulate_buy_order(self, stock_code: str) -> Dict:
        """模擬買進訂單"""
        # 實際應用中這裡會連接券商API
        import random

        # 模擬成交價格（收盤價）
        base_price = 100  # 實際應該從市場獲取
        price = round(base_price * (1 + random.uniform(-0.02, 0.02)), 2)

        # 根據資金比例計算買進數量
        available_capital = 1000000  # 假設100萬可用資金
        position_value = available_capital * self.position_size_ratio * 0.2  # 單一股票最多20%
        quantity = int(position_value / price / 1000) * 1000  # 整張買進

        return {
            'success': True,
            'price': price,
            'quantity': quantity,
            'total_cost': price * quantity,
            'trade_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def analyze_ex_dividend_performance(self, stock_code: str) -> Dict:
        """步驟3: 分析除權息當日表現並決定賣出策略"""
        try:
            self.logger.info(f"📊 分析 {stock_code} 除權息當日表現...")

            # 獲取除權息當日股價資料
            performance = self._get_ex_dividend_performance(stock_code)

            if not performance:
                return {'action': 'HOLD', 'reason': '無法獲取股價資料'}

            ex_open_price = performance['ex_open_price']
            current_price = performance['current_price']
            price_change = current_price - ex_open_price
            change_percent = (price_change / ex_open_price) * 100

            # 決策邏輯
            decision = self._make_sell_decision(stock_code, performance)

            self.logger.info(f"📈 {stock_code} 當日表現: {price_change:+.2f} ({change_percent:+.1f}%)")
            self.logger.info(f"🎯 交易決策: {decision['action']} - {decision['reason']}")

            return decision

        except Exception as e:
            self.logger.error(f"❌ 分析除權息表現失敗: {e}")
            return {'action': 'HOLD', 'reason': f'分析失敗: {e}'}

    def _get_ex_dividend_performance(self, stock_code: str) -> Dict:
        """獲取除權息當日股價表現"""
        # 實際應用中應該從股價API獲取
        # 這裡使用模擬資料
        import random

        ex_open_price = 90.0  # 除權息開盤參考價
        current_price = ex_open_price * (1 + random.uniform(-0.05, 0.08))

        return {
            'ex_open_price': ex_open_price,
            'current_price': round(current_price, 2),
            'high_price': round(current_price * 1.02, 2),
            'low_price': round(current_price * 0.98, 2),
            'volume': random.randint(1000, 10000)
        }

    def _make_sell_decision(self, stock_code: str, performance: Dict) -> Dict:
        """根據除權息當日表現做出賣出決策"""
        ex_open_price = performance['ex_open_price']
        current_price = performance['current_price']
        high_price = performance['high_price']

        price_change = current_price - ex_open_price
        change_percent = (price_change / ex_open_price) * 100

        is_premium_stock = stock_code in self.premium_stocks

        # 一般股票策略
        if not is_premium_stock:
            if price_change < 0:  # 平盤以下
                return {
                    'action': 'SELL',
                    'reason': '股價低於平盤，停損賣出',
                    'urgency': 'high',
                    'target_price': current_price
                }
            elif change_percent >= 3:  # 長紅（漲幅3%以上）
                return {
                    'action': 'SELL',
                    'reason': '長紅停利，當日賣出',
                    'urgency': 'medium',
                    'target_price': current_price
                }
            elif change_percent > 0:  # 見紅
                return {
                    'action': 'PARTIAL_SELL',
                    'reason': '見紅，可考慮部分停利或隔日觀察',
                    'urgency': 'low',
                    'target_price': high_price * 0.95  # 略低於最高價
                }
            else:
                return {
                    'action': 'HOLD',
                    'reason': '持平，繼續觀察',
                    'urgency': 'low'
                }

        # 績優股特殊策略
        else:
            if stock_code == '2330':  # 台積電
                if price_change < -2:  # 大幅貼息
                    return {
                        'action': 'HOLD',
                        'reason': '台積電先跌後漲策略，暫時持有等待填息',
                        'urgency': 'low',
                        'notes': '預期20個交易日內填息'
                    }
                elif change_percent >= 2:
                    return {
                        'action': 'PARTIAL_SELL',
                        'reason': '台積電表現良好，部分停利',
                        'urgency': 'medium'
                    }

            elif stock_code == '2317':  # 鴻海
                # 預設停利策略
                target_price = ex_open_price + 1.5
                if current_price >= target_price:
                    return {
                        'action': 'SELL',
                        'reason': f'達到預設停利點 {target_price}元',
                        'urgency': 'medium',
                        'target_price': target_price
                    }
                elif price_change < -1:
                    return {
                        'action': 'HOLD',
                        'reason': '等待填息，設定預賣單',
                        'urgency': 'low',
                        'target_price': target_price
                    }

        return {
            'action': 'HOLD',
            'reason': '績優股策略，繼續持有',
            'urgency': 'low'
        }

    def execute_sell_signal(self, stock_code: str, decision: Dict) -> Dict:
        """執行賣出信號"""
        try:
            if decision['action'] in ['SELL', 'PARTIAL_SELL']:
                # 模擬賣出
                result = self._simulate_sell_order(stock_code, decision)

                if result['success']:
                    # 記錄交易
                    self._record_trade(
                        stock_code=stock_code,
                        action='SELL',
                        price=result['price'],
                        quantity=result['quantity'],
                        strategy=decision['reason'],
                        profit_loss=result.get('profit_loss', 0)
                    )

                    self.logger.info(f"✅ {stock_code} 賣出成功: {result['quantity']}股 @ {result['price']}元")
                    if result.get('profit_loss'):
                        self.logger.info(f"💰 損益: {result['profit_loss']:+.2f}元")

                return result
            else:
                return {'success': False, 'reason': '無需賣出'}

        except Exception as e:
            self.logger.error(f"❌ 執行賣出信號失敗: {e}")
            return {'success': False, 'reason': str(e)}

    def _simulate_sell_order(self, stock_code: str, decision: Dict) -> Dict:
        """模擬賣出訂單"""
        # 實際應用中連接券商API
        import random

        # 獲取持倉
        position = self._get_position(stock_code)
        if not position:
            return {'success': False, 'reason': '無持倉'}

        # 決定賣出數量
        if decision['action'] == 'PARTIAL_SELL':
            sell_quantity = position['quantity'] // 2  # 賣出一半
        else:
            sell_quantity = position['quantity']  # 全部賣出

        # 模擬成交價格
        target_price = decision.get('target_price', position['avg_cost'] * 1.01)
        actual_price = target_price * (1 + random.uniform(-0.005, 0.005))

        # 計算損益
        profit_loss = (actual_price - position['avg_cost']) * sell_quantity

        return {
            'success': True,
            'price': round(actual_price, 2),
            'quantity': sell_quantity,
            'total_amount': actual_price * sell_quantity,
            'profit_loss': round(profit_loss, 2),
            'trade_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _get_position(self, stock_code: str) -> Dict:
        """獲取持倉資訊"""
        # 實際應用中從交易記錄計算
        # 這裡返回模擬持倉
        return {
            'stock_code': stock_code,
            'quantity': 2000,  # 2張
            'avg_cost': 95.0,
            'market_value': 95.0 * 2000
        }

    def _record_trade(self, stock_code: str, action: str, price: float,
                     quantity: int, strategy: str = '', profit_loss: float = 0):
        """記錄交易到數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trading_records
                (stock_code, action, price, quantity, trade_date, strategy, profit_loss)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                stock_code, action, price, quantity,
                datetime.now().strftime('%Y-%m-%d'),
                strategy, profit_loss
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"❌ 記錄交易失敗: {e}")

    def generate_trading_report(self) -> Dict:
        """生成交易報告"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 獲取交易統計
            trades_df = pd.read_sql_query('''
                SELECT * FROM trading_records
                ORDER BY created_at DESC
            ''', conn)

            # 獲取市場情緒
            sentiment_df = pd.read_sql_query('''
                SELECT * FROM market_sentiment
                ORDER BY created_at DESC LIMIT 1
            ''', conn)

            conn.close()

            # 計算統計數據
            total_trades = len(trades_df)
            total_profit_loss = trades_df['profit_loss'].sum() if not trades_df.empty else 0
            win_rate = len(trades_df[trades_df['profit_loss'] > 0]) / total_trades * 100 if total_trades > 0 else 0

            current_sentiment = sentiment_df.iloc[0] if not sentiment_df.empty else None

            report = {
                'summary': {
                    'total_trades': total_trades,
                    'total_profit_loss': round(total_profit_loss, 2),
                    'win_rate': round(win_rate, 1),
                    'current_sentiment': current_sentiment['sentiment_score'] if current_sentiment is not None else None
                },
                'recent_trades': trades_df.head(10).to_dict('records') if not trades_df.empty else [],
                'market_sentiment': current_sentiment.to_dict() if current_sentiment is not None else None
            }

            return report

        except Exception as e:
            self.logger.error(f"❌ 生成交易報告失敗: {e}")
            return {'error': str(e)}
