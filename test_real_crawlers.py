#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試真實的 finlab 爬蟲功能

這個腳本會嘗試安裝並測試真實的 finlab 爬蟲功能
"""

import sys
import os
import subprocess
import logging
from datetime import datetime, timedelta
import traceback

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def install_finlab():
    """嘗試安裝 finlab"""
    print("🔧 嘗試安裝 finlab...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "finlab"])
        print("✅ finlab 安裝成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ finlab 安裝失敗: {e}")
        return False

def test_finlab_import():
    """測試 finlab 模組導入"""
    print("🔍 測試 finlab 模組導入...")
    try:
        import finlab
        print(f"✅ finlab 版本: {finlab.__version__ if hasattr(finlab, '__version__') else '未知'}")
        
        from finlab.crawler import (
            crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
            crawl_finance_statement_by_date, crawl_twse_divide_ratio,
            crawl_otc_divide_ratio, crawl_twse_cap_reduction,
            crawl_otc_cap_reduction, crawl_benchmark
        )
        print("✅ 爬蟲函數導入成功")
        return True
        
    except ImportError as e:
        print(f"❌ finlab 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 未預期錯誤: {e}")
        return False

def test_single_crawler(name, func, *args):
    """測試單個爬蟲功能"""
    print(f"\n🧪 測試 {name}...")
    try:
        result = func(*args)
        
        if result is None:
            print(f"⚠️  {name} 返回 None")
            return False
        elif hasattr(result, 'shape'):
            print(f"✅ {name} 成功 - 資料形狀: {result.shape}")
            return True
        elif hasattr(result, '__len__'):
            print(f"✅ {name} 成功 - 資料長度: {len(result)}")
            return True
        else:
            print(f"✅ {name} 成功 - 返回類型: {type(result)}")
            return True
            
    except Exception as e:
        print(f"❌ {name} 失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🚀 Finlab 真實爬蟲功能測試")
    print("=" * 60)
    
    # 檢查是否已安裝 finlab
    if not test_finlab_import():
        print("\n💡 嘗試安裝 finlab...")
        if not install_finlab():
            print("❌ 無法安裝 finlab，測試終止")
            return False
        
        # 重新測試導入
        if not test_finlab_import():
            print("❌ 安裝後仍無法導入 finlab，測試終止")
            return False
    
    # 導入爬蟲函數
    try:
        from finlab.crawler import (
            crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
            crawl_finance_statement_by_date, crawl_twse_divide_ratio,
            crawl_otc_divide_ratio, crawl_twse_cap_reduction,
            crawl_otc_cap_reduction, crawl_benchmark
        )
    except ImportError as e:
        print(f"❌ 無法導入爬蟲函數: {e}")
        return False
    
    # 準備測試參數
    test_date = datetime.now() - timedelta(days=7)  # 一週前的日期
    
    # 測試結果
    results = {}
    
    print(f"\n📅 使用測試日期: {test_date.strftime('%Y-%m-%d')}")
    print("=" * 60)
    
    # 測試需要日期參數的爬蟲（快速測試）
    quick_tests = [
        ("股價資料", crawl_price, test_date),
        ("三大法人", crawl_bargin, test_date),
        ("本益比", crawl_pe, test_date),
        ("月營收", crawl_monthly_report, test_date),
        ("大盤指數", crawl_benchmark, test_date),
        ("財務報表", crawl_finance_statement_by_date, test_date),
    ]
    
    for name, func, *args in quick_tests:
        success = test_single_crawler(name, func, *args)
        results[name] = success
        
        # 短暫停頓避免過於頻繁請求
        import time
        time.sleep(2)
    
    # 測試不需要參數的爬蟲（這些可能比較慢）
    print(f"\n📊 測試除權息和減資資料...")
    slow_tests = [
        ("上市除權息", crawl_twse_divide_ratio),
        ("上櫃除權息", crawl_otc_divide_ratio),
        ("上市減資", crawl_twse_cap_reduction),
        ("上櫃減資", crawl_otc_cap_reduction),
    ]
    
    for name, func in slow_tests:
        success = test_single_crawler(name, func)
        results[name] = success
        
        # 較長停頓
        import time
        time.sleep(5)
    
    # 顯示結果摘要
    print("\n" + "=" * 60)
    print("📊 測試結果摘要")
    print("=" * 60)
    
    total = len(results)
    passed = sum(1 for success in results.values() if success)
    failed = total - passed
    
    print(f"📈 總測試數: {total}")
    print(f"✅ 通過: {passed}")
    print(f"❌ 失敗: {failed}")
    print(f"📊 成功率: {passed/total*100:.1f}%")
    
    print(f"\n📋 詳細結果:")
    for name, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {name}")
    
    if failed > 0:
        print(f"\n⚠️  注意事項:")
        print("  • 部分失敗可能是因為測試日期沒有資料")
        print("  • 網路連線問題也可能導致失敗")
        print("  • 資料來源網站可能暫時無法存取")
        print("  • 這不一定表示GUI功能有問題")
    
    print(f"\n💡 GUI功能評估:")
    if passed >= 7:  # 70%以上成功
        print("  ✅ GUI中的10個爬蟲功能應該都能正常執行")
    elif passed >= 5:  # 50%以上成功
        print("  ⚠️  GUI中的爬蟲功能大部分能正常執行")
    else:
        print("  ❌ GUI中的爬蟲功能可能有問題，需要進一步檢查")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        traceback.print_exc()
