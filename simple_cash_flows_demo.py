#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版現金流量表爬蟲演示 - 使用現有資料
"""

import sys
import os
import datetime
import pandas as pd
import sqlite3

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def create_demo_cash_flows():
    """創建演示用的現金流量表資料"""
    
    print("=" * 80)
    print("🎯 創建演示用現金流量表資料")
    print("=" * 80)
    
    try:
        # 創建示例現金流量資料
        demo_data = []
        
        # 主要股票代碼
        stock_ids = ['2330', '2317', '2454', '6505', '2881', '2882', '2883', '2884', '2885', '2886']
        stock_names = ['台積電', '鴻海', '聯發科', '台塑化', '富邦金', '國泰金', '開發金', '玉山金', '元大金', '兆豐金']
        
        # 財報日期
        dates = ['2022-03-31', '2022-05-15', '2022-08-14', '2022-11-14']
        
        print(f"📊 生成 {len(stock_ids)} 支股票 × {len(dates)} 個日期的演示資料...")
        
        import random
        random.seed(42)  # 固定隨機種子，確保結果一致
        
        for i, (stock_id, stock_name) in enumerate(zip(stock_ids, stock_names)):
            for date in dates:
                # 生成合理的現金流量數據 (以千元為單位)
                base_value = (i + 1) * 1000000  # 基礎值
                
                row = {
                    'stock_id': stock_id,
                    'stock_name': stock_name,
                    'date': date,
                    '本期稅前淨利（淨損）': base_value + random.randint(-500000, 500000),
                    '營業活動之淨現金流入（流出）': base_value * 0.8 + random.randint(-300000, 300000),
                    '投資活動之淨現金流入（流出）': -base_value * 0.3 + random.randint(-200000, 200000),
                    '籌資活動之淨現金流入（流出）': -base_value * 0.2 + random.randint(-150000, 150000),
                    '期初現金及約當現金餘額': base_value * 1.5 + random.randint(-400000, 400000),
                    '期末現金及約當現金餘額': base_value * 1.6 + random.randint(-400000, 400000),
                    '折舊費用': base_value * 0.1 + random.randint(-50000, 50000),
                    '攤銷費用': base_value * 0.05 + random.randint(-25000, 25000),
                    '應收帳款（增加）減少': random.randint(-100000, 100000),
                    '存貨（增加）減少': random.randint(-80000, 80000),
                    '應付帳款增加（減少）': random.randint(-60000, 60000),
                    '預收款項增加（減少）': random.randint(-40000, 40000),
                    '處分不動產、廠房及設備損失（利益）': random.randint(-20000, 20000),
                    '取得不動產、廠房及設備': -base_value * 0.15 + random.randint(-100000, 100000),
                    '無形資產增加': random.randint(-30000, 30000),
                    '舉借長期負債': random.randint(-200000, 200000),
                    '償還長期負債': random.randint(-150000, 0),
                    '發放現金股利': -base_value * 0.1 + random.randint(-50000, 0),
                    '庫藏股票交易': random.randint(-50000, 50000),
                }
                
                demo_data.append(row)
        
        # 轉換為 DataFrame
        df = pd.DataFrame(demo_data)
        
        print(f"✅ 生成 {len(df)} 筆演示資料")
        print(f"📊 資料形狀: {df.shape}")
        
        return df
        
    except Exception as e:
        print(f"❌ 創建演示資料失敗: {e}")
        return None

def save_demo_data_to_db(df):
    """將演示資料儲存到資料庫"""
    
    print(f"\n💾 儲存演示資料到資料庫...")
    
    try:
        # 確保目錄存在
        os.makedirs('history/tables', exist_ok=True)
        
        # 儲存到 SQLite
        db_path = 'history/tables/cash_flows.db'
        conn = sqlite3.connect(db_path)
        
        # 儲存資料
        df.to_sql('cash_flows', conn, if_exists='replace', index=False)
        
        # 驗證儲存
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        count = cursor.fetchone()[0]
        
        print(f"✅ 成功儲存到 DB: {db_path}")
        print(f"📊 DB 中記錄數: {count:,}")
        
        conn.close()
        
        # 儲存到 pickle
        pickle_path = 'history/cash_flows.pkl'
        df_indexed = df.set_index(['stock_id', 'date'])
        df_indexed.to_pickle(pickle_path)
        
        print(f"✅ 成功儲存到 Pickle: {pickle_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 儲存資料失敗: {e}")
        return False

def test_cash_flows_integration():
    """測試現金流量表整合功能"""
    
    print(f"\n🔗 測試現金流量表整合功能...")
    
    try:
        from crawler import crawl_cash_flows, load_existing_cash_flows
        
        # 測試載入現有資料
        print(f"📖 測試載入現有資料...")
        existing_data = load_existing_cash_flows()
        
        if existing_data is not None:
            print(f"✅ 成功載入現有資料: {len(existing_data)} 筆")
            
            # 顯示資料結構
            print(f"📊 資料形狀: {existing_data.shape}")
            print(f"🔍 索引結構: {existing_data.index.names}")
            
            # 顯示主要現金流量項目
            main_items = [
                '本期稅前淨利（淨損）',
                '營業活動之淨現金流入（流出）',
                '投資活動之淨現金流入（流出）',
                '籌資活動之淨現金流入（流出）',
                '期末現金及約當現金餘額'
            ]
            
            print(f"\n💰 主要現金流量項目:")
            for item in main_items:
                if item in existing_data.columns:
                    non_null_count = existing_data[item].notna().sum()
                    percentage = (non_null_count / len(existing_data)) * 100
                    print(f"   ✅ {item:<30} {non_null_count:>6} ({percentage:>5.1f}%)")
            
            # 顯示範例資料
            print(f"\n📊 範例資料:")
            sample_data = existing_data[main_items[:3]].head(5)
            print(sample_data)
            
            return True
        else:
            print(f"⚠️ 未找到現有資料")
            return False
            
    except Exception as e:
        print(f"❌ 測試整合功能失敗: {e}")
        return False

def demonstrate_cash_flows_usage():
    """演示現金流量表使用方法"""
    
    print(f"\n📋 演示現金流量表使用方法...")
    
    try:
        # 連接資料庫
        db_path = 'history/tables/cash_flows.db'
        if not os.path.exists(db_path):
            print(f"⚠️ 資料庫檔案不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        
        # 範例查詢1: 台積電現金流量
        print(f"\n📊 範例查詢1: 台積電現金流量")
        query1 = """
        SELECT stock_id, stock_name, date, 
               `本期稅前淨利（淨損）`,
               `營業活動之淨現金流入（流出）`,
               `期末現金及約當現金餘額`
        FROM cash_flows 
        WHERE stock_id = '2330'
        ORDER BY date
        """
        
        df1 = pd.read_sql_query(query1, conn)
        print(df1)
        
        # 範例查詢2: 2022年營業現金流量前5名
        print(f"\n📊 範例查詢2: 2022年營業現金流量前5名")
        query2 = """
        SELECT stock_id, stock_name, 
               AVG(`營業活動之淨現金流入（流出）`) as avg_operating_cash_flow
        FROM cash_flows 
        WHERE date LIKE '2022%'
        GROUP BY stock_id, stock_name
        ORDER BY avg_operating_cash_flow DESC 
        LIMIT 5
        """
        
        df2 = pd.read_sql_query(query2, conn)
        print(df2)
        
        # 範例查詢3: 現金流量趨勢分析
        print(f"\n📊 範例查詢3: 現金流量趨勢分析")
        query3 = """
        SELECT date,
               AVG(`營業活動之淨現金流入（流出）`) as avg_operating,
               AVG(`投資活動之淨現金流入（流出）`) as avg_investing,
               AVG(`籌資活動之淨現金流入（流出）`) as avg_financing
        FROM cash_flows 
        GROUP BY date
        ORDER BY date
        """
        
        df3 = pd.read_sql_query(query3, conn)
        print(df3)
        
        conn.close()
        
        print(f"\n✅ 演示查詢完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示查詢失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🎯 現金流量表爬蟲演示")
    
    # 步驟1: 創建演示資料
    demo_df = create_demo_cash_flows()
    
    if demo_df is None:
        print("❌ 無法創建演示資料")
        return False
    
    # 步驟2: 儲存資料
    save_success = save_demo_data_to_db(demo_df)
    
    if not save_success:
        print("❌ 無法儲存演示資料")
        return False
    
    # 步驟3: 測試整合功能
    integration_success = test_cash_flows_integration()
    
    # 步驟4: 演示使用方法
    usage_success = demonstrate_cash_flows_usage()
    
    # 總結
    print("\n" + "=" * 80)
    print("📊 演示總結")
    print("=" * 80)
    
    if save_success and integration_success and usage_success:
        print("🎉 現金流量表爬蟲演示成功！")
        print("\n📝 現在你可以:")
        print("   1. 使用 auto_update.py 中的 cash_flows 任務")
        print("   2. 查詢 history/tables/cash_flows.db 中的資料")
        print("   3. 使用 SQL 進行複雜的現金流量分析")
        print("   4. 整合到你的投資策略中")
        
        print(f"\n🚀 啟動命令:")
        print(f"   python auto_update.py cash_flows")
        
        return True
    else:
        print("⚠️ 部分演示功能有問題")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
