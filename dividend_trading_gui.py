#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息交易系統GUI - 基於蘇松泙策略的程式化交易界面
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QTableWidget, QTableWidgetItem,
                            QLabel, QTextEdit, QTabWidget, QGroupBox, QProgressBar,
                            QMessageBox, QComboBox, QSpinBox, QDateEdit, QCheckBox, QDialog)
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QColor, QPalette
import logging

from dividend_trading_system import DividendTradingSystem

# 嘗試導入PE數據提供器
try:
    from pe_data_provider import PEDataProvider
    PE_DATA_AVAILABLE = True
except ImportError:
    PE_DATA_AVAILABLE = False
    logging.warning("⚠️ PE數據提供器不可用，將只使用除權息資料庫中的EPS資料")

class DividendTradingGUI(QMainWindow):
    """除權息交易系統主界面"""
    
    def __init__(self):
        super().__init__()
        self.trading_system = DividendTradingSystem()

        # 初始化PE數據提供器
        self.pe_data_provider = None
        if PE_DATA_AVAILABLE:
            try:
                # 嘗試從配置中獲取PE數據庫路徑
                pe_db_path = "D:/Finlab/history/tables/pe_data.db"
                if os.path.exists(pe_db_path):
                    self.pe_data_provider = PEDataProvider(pe_db_path)
                    logging.info("✅ PE數據提供器初始化成功")
                else:
                    logging.warning("⚠️ PE數據庫文件不存在，將只使用除權息資料庫中的EPS資料")
            except Exception as e:
                logging.error(f"❌ PE數據提供器初始化失敗: {e}")

        self.init_ui()
        self.setup_timer()

        # 初始化完成後自動執行近一週的除權息查詢
        QTimer.singleShot(500, self.auto_query_next_week)

    def auto_query_next_week(self):
        """自動查詢未來一週的除權息資料"""
        try:
            # 設置為未來一週的日期範圍
            self.set_date_range(7)
            # 自動執行查詢
            self.query_by_date_range()
            self.log_message("✅ 已自動載入未來一週的除權息資料")
        except Exception as e:
            self.log_message(f"⚠️ 自動查詢失敗: {e}")

    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle("🎯 除權息交易系統 - 蘇松泙策略程式化")
        self.setGeometry(100, 100, 1400, 900)
        
        # 設置主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QTextEdit {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
            QLabel {
                color: #333333;
                font-weight: normal;
            }
            QComboBox {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 4px 8px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666666;
            }
            QDateEdit {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 4px 8px;
                min-height: 20px;
            }
            /* 標籤頁樣式改善 */
            QTabWidget::pane {
                border: 2px solid #cccccc;
                background-color: #ffffff;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                color: #333333;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: #ffffff;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #64B5F6;
                color: #ffffff;
            }
        """)
        
        # 創建中央widget和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 創建標籤頁
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 創建各個標籤頁（先創建日誌標籤頁以初始化auto_log_checkbox）
        self.create_trading_log_tab()
        self.create_market_analysis_tab()
        self.create_dividend_query_tab()  # 新增查詢功能標籤頁
        self.create_trading_candidates_tab()
        self.create_trading_execution_tab()
        self.create_performance_monitor_tab()

        # 🎯 啟動後直接切換到除權息查詢頁籤
        self.tab_widget.setCurrentIndex(2)  # 除權息查詢是第3個頁籤（索引為2）
        
        # 狀態欄
        self.statusBar().showMessage("🚀 除權息交易系統已啟動")
        
    def create_market_analysis_tab(self):
        """創建市場分析標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 市場情緒評估區域
        sentiment_group = QGroupBox("📊 市場情緒評估 (步驟1)")
        sentiment_layout = QVBoxLayout(sentiment_group)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        self.evaluate_btn = QPushButton("🔍 評估除權息行情")
        self.evaluate_btn.clicked.connect(self.evaluate_market_sentiment)
        button_layout.addWidget(self.evaluate_btn)

        # 評分標準說明按鈕
        scoring_info_btn = QPushButton("📊 評分標準說明")
        scoring_info_btn.clicked.connect(self.show_scoring_criteria)
        button_layout.addWidget(scoring_info_btn)

        button_layout.addStretch()
        
        sentiment_layout.addLayout(button_layout)
        
        # 結果顯示
        self.sentiment_result = QTextEdit()
        self.sentiment_result.setMaximumHeight(200)
        self.sentiment_result.setReadOnly(True)
        sentiment_layout.addWidget(self.sentiment_result)
        
        # 率先除權息股票表現
        self.leading_stocks_table = QTableWidget()
        self.leading_stocks_table.setColumnCount(6)
        self.leading_stocks_table.setHorizontalHeaderLabels([
            "股票代碼", "股票名稱", "除權息日", "股利", "當日表現", "評分"
        ])
        sentiment_layout.addWidget(QLabel("🎯 率先除權息股票表現:"))
        sentiment_layout.addWidget(self.leading_stocks_table)
        
        layout.addWidget(sentiment_group)
        self.tab_widget.addTab(tab, "📊 市場分析")

    def create_dividend_query_tab(self):
        """創建除權息查詢標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 查詢控制區域
        query_group = QGroupBox("🔍 除權息資料查詢")
        query_layout = QVBoxLayout(query_group)

        # 查詢方式選擇
        query_mode_layout = QHBoxLayout()
        query_mode_layout.addWidget(QLabel("查詢方式:"))

        self.query_mode_combo = QComboBox()
        self.query_mode_combo.addItem("📅 按時間範圍查詢", "date_range")
        self.query_mode_combo.addItem("📊 按股票查詢", "stock")
        self.query_mode_combo.currentTextChanged.connect(self.on_query_mode_changed)
        query_mode_layout.addWidget(self.query_mode_combo)
        query_mode_layout.addStretch()
        query_layout.addLayout(query_mode_layout)

        # 股票查詢模式
        self.stock_query_widget = QWidget()
        stock_query_layout = QHBoxLayout(self.stock_query_widget)
        stock_query_layout.setContentsMargins(0, 0, 0, 0)

        stock_query_layout.addWidget(QLabel("選擇股票:"))

        # 股票下拉選單
        self.query_stock_combo = QComboBox()
        self.query_stock_combo.setMinimumWidth(200)
        self.query_stock_combo.setEditable(True)  # 允許手動輸入
        self.query_stock_combo.setPlaceholderText("輸入股票代碼或選擇...")
        stock_query_layout.addWidget(self.query_stock_combo)

        # 年份選擇
        stock_query_layout.addWidget(QLabel("查詢年份:"))
        self.query_year_combo = QComboBox()
        current_year = datetime.now().year
        for year in range(current_year - 15, current_year + 2):  # 過去15年到未來1年
            self.query_year_combo.addItem(str(year))
        self.query_year_combo.setCurrentText(str(current_year))
        stock_query_layout.addWidget(self.query_year_combo)

        # 查詢按鈕
        self.query_btn = QPushButton("🔍 查詢除權息資料")
        self.query_btn.clicked.connect(self.query_dividend_data)
        stock_query_layout.addWidget(self.query_btn)

        # 圖表分析按鈕
        self.chart_analysis_btn = QPushButton("📊 歷史圖表分析")
        self.chart_analysis_btn.clicked.connect(self.show_dividend_charts)
        stock_query_layout.addWidget(self.chart_analysis_btn)

        stock_query_layout.addStretch()
        query_layout.addWidget(self.stock_query_widget)

        # 時間範圍查詢模式
        self.date_range_query_widget = QWidget()
        date_range_layout = QVBoxLayout(self.date_range_query_widget)
        date_range_layout.setContentsMargins(0, 0, 0, 0)

        # 日期選擇行
        date_select_layout = QHBoxLayout()

        # 開始日期
        date_select_layout.addWidget(QLabel("開始日期:"))
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate())  # 預設今天開始
        self.start_date_edit.setCalendarPopup(True)
        date_select_layout.addWidget(self.start_date_edit)

        # 結束日期
        date_select_layout.addWidget(QLabel("結束日期:"))
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addDays(7))  # 預設未來一週
        self.end_date_edit.setCalendarPopup(True)
        date_select_layout.addWidget(self.end_date_edit)

        # 時間範圍查詢按鈕
        self.date_range_query_btn = QPushButton("🔍 查詢時間範圍")
        self.date_range_query_btn.clicked.connect(self.query_by_date_range)
        date_select_layout.addWidget(self.date_range_query_btn)

        date_select_layout.addStretch()
        date_range_layout.addLayout(date_select_layout)

        # 快速選擇按鈕行
        quick_select_layout = QHBoxLayout()
        quick_select_layout.addWidget(QLabel("快速選擇:"))

        self.next_week_btn = QPushButton("近一週")
        self.next_week_btn.clicked.connect(lambda: self.set_date_range(7))
        quick_select_layout.addWidget(self.next_week_btn)

        self.next_two_weeks_btn = QPushButton("近兩週")
        self.next_two_weeks_btn.clicked.connect(lambda: self.set_date_range(14))
        quick_select_layout.addWidget(self.next_two_weeks_btn)

        self.next_month_btn = QPushButton("近一個月")
        self.next_month_btn.clicked.connect(lambda: self.set_date_range(30))
        quick_select_layout.addWidget(self.next_month_btn)

        quick_select_layout.addStretch()
        date_range_layout.addLayout(quick_select_layout)

        query_layout.addWidget(self.date_range_query_widget)

        # 預設顯示時間範圍查詢，隱藏股票查詢
        self.date_range_query_widget.setVisible(True)
        self.stock_query_widget.setVisible(False)

        # 查詢結果顯示區域
        self.query_result_text = QTextEdit()
        self.query_result_text.setMaximumHeight(150)
        self.query_result_text.setReadOnly(True)
        query_layout.addWidget(QLabel("📋 查詢結果:"))
        query_layout.addWidget(self.query_result_text)

        # 詳細資料表格
        self.dividend_detail_table = QTableWidget()
        self.dividend_detail_table.setColumnCount(11)
        self.dividend_detail_table.setHorizontalHeaderLabels([
            "股票代碼", "股票名稱", "股利年度", "除權息日", "現金股利", "股票股利", "總股利", "殖利率", "每股盈餘", "最近股價", "資料來源"
        ])
        query_layout.addWidget(QLabel("📊 歷史除權息詳細資料:"))

        # 添加說明標籤
        explanation_label = QLabel("💡 股利年度：股利來源年度 | 除權息日：實際執行日期")
        explanation_label.setStyleSheet("color: #888888; font-size: 11px; margin: 2px;")
        query_layout.addWidget(explanation_label)

        query_layout.addWidget(self.dividend_detail_table)

        # 添加功能按鈕
        button_layout = QHBoxLayout()
        self.open_dividend_folder_btn = QPushButton("📁 開啟除權息目錄")
        self.open_dividend_folder_btn.clicked.connect(self.open_dividend_folder)
        button_layout.addWidget(self.open_dividend_folder_btn)

        # 添加5年歷史分析按鈕
        self.five_year_analysis_btn = QPushButton("📊 5年歷史分析")
        self.five_year_analysis_btn.clicked.connect(self.show_five_year_analysis)
        self.five_year_analysis_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.five_year_analysis_btn)

        button_layout.addStretch()
        query_layout.addLayout(button_layout)

        layout.addWidget(query_group)
        self.tab_widget.addTab(tab, "🔍 除權息查詢")

        # 載入股票清單
        self.load_stock_list_for_query()

    def create_trading_candidates_tab(self):
        """創建交易候選標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 候選股票篩選
        filter_group = QGroupBox("🎯 交易候選篩選")
        filter_layout = QHBoxLayout(filter_group)
        
        filter_layout.addWidget(QLabel("未來天數:"))
        self.days_spin = QSpinBox()
        self.days_spin.setRange(1, 30)
        self.days_spin.setValue(7)
        filter_layout.addWidget(self.days_spin)
        
        self.refresh_candidates_btn = QPushButton("🔄 刷新候選股票")
        self.refresh_candidates_btn.clicked.connect(self.refresh_trading_candidates)
        filter_layout.addWidget(self.refresh_candidates_btn)
        
        filter_layout.addStretch()
        layout.addWidget(filter_group)
        
        # 候選股票表格
        self.candidates_table = QTableWidget()
        self.candidates_table.setColumnCount(8)
        self.candidates_table.setHorizontalHeaderLabels([
            "股票代碼", "股票名稱", "除權息日", "現金股利", "距離天數", 
            "績優股", "建議策略", "風險等級"
        ])
        layout.addWidget(QLabel("📋 除權息交易候選股票:"))
        layout.addWidget(self.candidates_table)
        
        self.tab_widget.addTab(tab, "🎯 交易候選")
        
    def create_trading_execution_tab(self):
        """創建交易執行標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 買進執行區域
        buy_group = QGroupBox("💰 買進執行 (步驟2)")
        buy_layout = QVBoxLayout(buy_group)
        
        # 1:25分定格買進
        timing_layout = QHBoxLayout()
        timing_layout.addWidget(QLabel("⏰ 當前時間:"))
        self.current_time_label = QLabel()
        timing_layout.addWidget(self.current_time_label)
        timing_layout.addWidget(QLabel("| 下次1:25分定格:"))
        self.next_timing_label = QLabel()
        timing_layout.addWidget(self.next_timing_label)
        timing_layout.addStretch()
        buy_layout.addLayout(timing_layout)
        
        # 手動買進控制
        manual_buy_layout = QHBoxLayout()
        manual_buy_layout.addWidget(QLabel("股票代碼:"))
        self.buy_stock_combo = QComboBox()
        self.buy_stock_combo.setEditable(True)
        manual_buy_layout.addWidget(self.buy_stock_combo)
        
        self.manual_buy_btn = QPushButton("🛒 手動買進")
        self.manual_buy_btn.clicked.connect(self.manual_buy_stock)
        manual_buy_layout.addWidget(self.manual_buy_btn)
        manual_buy_layout.addStretch()
        buy_layout.addLayout(manual_buy_layout)
        
        layout.addWidget(buy_group)
        
        # 賣出執行區域
        sell_group = QGroupBox("💸 賣出執行 (步驟3)")
        sell_layout = QVBoxLayout(sell_group)
        
        # 除權息當日分析
        analysis_layout = QHBoxLayout()
        self.analyze_performance_btn = QPushButton("📊 分析除權息表現")
        self.analyze_performance_btn.clicked.connect(self.analyze_ex_dividend_performance)
        analysis_layout.addWidget(self.analyze_performance_btn)
        analysis_layout.addStretch()
        sell_layout.addLayout(analysis_layout)
        
        # 賣出決策顯示
        self.sell_decision_text = QTextEdit()
        self.sell_decision_text.setMaximumHeight(150)
        self.sell_decision_text.setReadOnly(True)
        sell_layout.addWidget(QLabel("🎯 賣出決策:"))
        sell_layout.addWidget(self.sell_decision_text)
        
        # 執行賣出
        execute_sell_layout = QHBoxLayout()
        self.execute_sell_btn = QPushButton("💸 執行賣出")
        self.execute_sell_btn.clicked.connect(self.execute_sell_order)
        self.execute_sell_btn.setEnabled(False)
        execute_sell_layout.addWidget(self.execute_sell_btn)
        execute_sell_layout.addStretch()
        sell_layout.addLayout(execute_sell_layout)
        
        layout.addWidget(sell_group)
        
        # 持倉監控
        position_group = QGroupBox("📊 當前持倉")
        position_layout = QVBoxLayout(position_group)
        
        self.position_table = QTableWidget()
        self.position_table.setColumnCount(6)
        self.position_table.setHorizontalHeaderLabels([
            "股票代碼", "股票名稱", "持有數量", "平均成本", "當前價格", "損益"
        ])
        position_layout.addWidget(self.position_table)
        
        layout.addWidget(position_group)
        
        self.tab_widget.addTab(tab, "⚡ 交易執行")
        
    def create_performance_monitor_tab(self):
        """創建績效監控標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 績效統計
        stats_group = QGroupBox("📈 交易績效統計")
        stats_layout = QVBoxLayout(stats_group)
        
        # 統計數據顯示
        stats_info_layout = QHBoxLayout()
        
        self.total_trades_label = QLabel("總交易次數: 0")
        stats_info_layout.addWidget(self.total_trades_label)
        
        self.total_profit_label = QLabel("總損益: $0")
        stats_info_layout.addWidget(self.total_profit_label)
        
        self.win_rate_label = QLabel("勝率: 0%")
        stats_info_layout.addWidget(self.win_rate_label)
        
        stats_info_layout.addStretch()
        stats_layout.addLayout(stats_info_layout)
        
        # 刷新按鈕
        refresh_stats_btn = QPushButton("🔄 刷新統計")
        refresh_stats_btn.clicked.connect(self.refresh_performance_stats)
        stats_layout.addWidget(refresh_stats_btn)
        
        layout.addWidget(stats_group)
        
        # 月度績效表格
        monthly_group = QGroupBox("📅 月度績效")
        monthly_layout = QVBoxLayout(monthly_group)
        
        self.monthly_performance_table = QTableWidget()
        self.monthly_performance_table.setColumnCount(5)
        self.monthly_performance_table.setHorizontalHeaderLabels([
            "月份", "交易次數", "總損益", "勝率", "最大回撤"
        ])
        monthly_layout.addWidget(self.monthly_performance_table)
        
        layout.addWidget(monthly_group)
        
        self.tab_widget.addTab(tab, "📈 績效監控")
        
    def create_trading_log_tab(self):
        """創建交易日誌標籤頁"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 日誌控制
        log_control_layout = QHBoxLayout()
        
        self.auto_log_checkbox = QCheckBox("自動記錄")
        self.auto_log_checkbox.setChecked(True)
        log_control_layout.addWidget(self.auto_log_checkbox)
        
        clear_log_btn = QPushButton("🗑️ 清空日誌")
        clear_log_btn.clicked.connect(self.clear_trading_log)
        log_control_layout.addWidget(clear_log_btn)
        
        log_control_layout.addStretch()
        layout.addLayout(log_control_layout)
        
        # 交易日誌顯示
        self.trading_log = QTextEdit()
        self.trading_log.setReadOnly(True)
        self.trading_log.setFont(QFont("Consolas", 9))
        layout.addWidget(self.trading_log)
        
        self.tab_widget.addTab(tab, "📝 交易日誌")
        
    def setup_timer(self):
        """設置定時器"""
        # 每秒更新時間顯示
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_display)
        self.timer.start(1000)
        
        # 每分鐘檢查是否到達1:25分
        self.trading_timer = QTimer()
        self.trading_timer.timeout.connect(self.check_trading_time)
        self.trading_timer.start(60000)  # 每分鐘檢查一次
        
    def update_time_display(self):
        """更新時間顯示"""
        current_time = datetime.now()
        self.current_time_label.setText(current_time.strftime("%H:%M:%S"))
        
        # 計算下次1:25分的時間
        if current_time.hour < 13 or (current_time.hour == 13 and current_time.minute < 25):
            next_timing = current_time.replace(hour=13, minute=25, second=0, microsecond=0)
        else:
            next_timing = (current_time + timedelta(days=1)).replace(hour=13, minute=25, second=0, microsecond=0)
        
        self.next_timing_label.setText(next_timing.strftime("%m/%d %H:%M"))
        
    def check_trading_time(self):
        """檢查是否到達交易時間"""
        current_time = datetime.now()
        if current_time.hour == 13 and current_time.minute == 25:
            self.log_message("⏰ 到達1:25分定格買進時間！")
            # 這裡可以自動執行買進邏輯
            
    def evaluate_market_sentiment(self):
        """評估市場情緒"""
        try:
            self.log_message("🔍 開始評估除權息市場行情...")
            self.evaluate_btn.setEnabled(False)
            
            # 執行市場情緒評估
            result = self.trading_system.evaluate_market_sentiment()
            
            # 顯示結果
            sentiment_text = f"""
📊 市場情緒評估結果:
• 情緒分數: {result['sentiment_score']:.1f}/100
• 市場狀態: {result['sentiment']}
• 建議策略: {result['strategy']}
• 資金投入比例: {result['position_ratio']:.1%}
            """
            self.sentiment_result.setText(sentiment_text)
            
            # 更新率先除權息股票表格
            self.update_leading_stocks_table(result.get('performance_data', {}))
            
            self.log_message(f"✅ 市場情緒評估完成: {result['sentiment']} (分數: {result['sentiment_score']:.1f})")
            
        except Exception as e:
            self.log_message(f"❌ 市場情緒評估失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"市場情緒評估失敗:\n{e}")
        finally:
            self.evaluate_btn.setEnabled(True)
            
    def update_leading_stocks_table(self, performance_data):
        """更新率先除權息股票表格 - 使用真實除權息資料"""
        try:
            # 獲取真實的除權息資料
            dividend_data = self.get_real_dividend_data()

            self.leading_stocks_table.setRowCount(len(performance_data))

            for row, (stock_code, data) in enumerate(performance_data.items()):
                # 股票代碼
                self.leading_stocks_table.setItem(row, 0, QTableWidgetItem(stock_code))

                # 股票名稱 - 從除權息資料庫獲取
                stock_name = self.get_stock_name_from_dividend_db(stock_code)
                if not stock_name:
                    stock_name = data.get('name', f'股票{stock_code}')
                self.leading_stocks_table.setItem(row, 1, QTableWidgetItem(stock_name))

                # 除權息日期和股利 - 從除權息資料庫獲取
                dividend_info = dividend_data.get(stock_code, {})
                ex_date = dividend_info.get('ex_date', '2025-06-01')
                cash_dividend = dividend_info.get('cash_dividend', 0.0)

                self.leading_stocks_table.setItem(row, 2, QTableWidgetItem(str(ex_date)))
                self.leading_stocks_table.setItem(row, 3, QTableWidgetItem(f"{cash_dividend:.1f}"))

                # 當日表現 - 改善顏色對比
                same_day_gain = data.get('same_day_gain', 0)
                performance_text = f"{same_day_gain:+.1f}"
                performance_item = QTableWidgetItem(performance_text)

                # 使用超高對比度顏色 (符合WCAG AAA標準)
                if same_day_gain > 0:
                    performance_item.setBackground(QColor(27, 94, 32))     # 更深綠色背景
                    performance_item.setForeground(QColor(255, 255, 255))  # 白色文字
                elif same_day_gain < 0:
                    performance_item.setBackground(QColor(136, 14, 79))    # 更深紅色背景
                    performance_item.setForeground(QColor(255, 255, 255))  # 白色文字
                else:
                    performance_item.setBackground(QColor(66, 66, 66))     # 更深灰色背景
                    performance_item.setForeground(QColor(255, 255, 255))  # 白色文字

                self.leading_stocks_table.setItem(row, 4, performance_item)

                # 評分 - 改善顏色對比
                if data.get('filled_dividend') and data.get('extra_gain', 0) > 0:
                    score = "優秀"
                    score_color = QColor(76, 175, 80)   # 綠色
                elif data.get('filled_dividend'):
                    score = "良好"
                    score_color = QColor(139, 195, 74)  # 淺綠色
                elif same_day_gain > 0:
                    score = "普通"
                    score_color = QColor(255, 193, 7)   # 黃色
                else:
                    score = "不佳"
                    score_color = QColor(244, 67, 54)   # 紅色

                score_item = QTableWidgetItem(score)
                score_item.setForeground(score_color)
                self.leading_stocks_table.setItem(row, 5, score_item)

        except Exception as e:
            self.log_message(f"❌ 更新率先除權息股票表格失敗: {e}")
            # 使用原始方法作為備用
            self.update_leading_stocks_table_fallback(performance_data)

    def get_real_dividend_data(self):
        """從除權息資料庫獲取真實資料"""
        try:
            import sqlite3
            import os

            # 除權息資料庫路徑
            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"

            if not os.path.exists(dividend_db_path):
                return {}

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            # 獲取6月份的除權息資料 - 支援多種日期格式，排除sample_data並真正去重
            # 使用GROUP BY來確保同一股票同一日期只有一筆記錄
            cursor.execute("""
                SELECT stock_code, stock_name, ex_dividend_date,
                       MAX(cash_dividend) as cash_dividend,
                       MAX(total_dividend) as total_dividend
                FROM dividend_data
                WHERE ex_dividend_date IS NOT NULL
                AND (ex_dividend_date LIKE '2025-06%' OR ex_dividend_date LIKE '%/06/%' OR ex_dividend_date LIKE '%-06-%')
                AND (data_source IS NULL OR data_source != 'sample_data')
                GROUP BY stock_code, ex_dividend_date
                ORDER BY ex_dividend_date
            """)

            results = cursor.fetchall()
            conn.close()

            dividend_data = {}
            for row in results:
                stock_code, stock_name, ex_date, cash_dividend, total_dividend = row
                dividend_data[stock_code] = {
                    'stock_name': stock_name,
                    'ex_date': ex_date,
                    'cash_dividend': cash_dividend or total_dividend or 0.0
                }

            return dividend_data

        except Exception as e:
            self.log_message(f"❌ 獲取真實除權息資料失敗: {e}")
            return {}

    def get_stock_name_from_dividend_db(self, stock_code):
        """從除權息資料庫獲取股票名稱"""
        try:
            import sqlite3
            import os

            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"

            if not os.path.exists(dividend_db_path):
                return None

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT stock_name
                FROM dividend_data
                WHERE stock_code = ?
                AND stock_name IS NOT NULL
                AND (data_source IS NULL OR data_source != 'sample_data')
                LIMIT 1
            """, (stock_code,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result else None

        except Exception as e:
            return None

    def update_leading_stocks_table_fallback(self, performance_data):
        """備用的表格更新方法"""
        self.leading_stocks_table.setRowCount(len(performance_data))

        for row, (stock_code, data) in enumerate(performance_data.items()):
            self.leading_stocks_table.setItem(row, 0, QTableWidgetItem(stock_code))
            self.leading_stocks_table.setItem(row, 1, QTableWidgetItem(data.get('name', f'股票{stock_code}')))
            self.leading_stocks_table.setItem(row, 2, QTableWidgetItem('2025-06-01'))
            self.leading_stocks_table.setItem(row, 3, QTableWidgetItem('5.0'))

            # 當日表現 - 高對比度顏色
            same_day_gain = data.get('same_day_gain', 0)
            performance_text = f"{same_day_gain:+.1f}"
            performance_item = QTableWidgetItem(performance_text)

            if same_day_gain > 0:
                performance_item.setBackground(QColor(27, 94, 32))     # 更深綠色
                performance_item.setForeground(QColor(255, 255, 255))
            elif same_day_gain < 0:
                performance_item.setBackground(QColor(136, 14, 79))    # 更深紅色
                performance_item.setForeground(QColor(255, 255, 255))
            else:
                performance_item.setBackground(QColor(66, 66, 66))     # 更深灰色
                performance_item.setForeground(QColor(255, 255, 255))

            self.leading_stocks_table.setItem(row, 4, performance_item)

            # 評分
            if data.get('filled_dividend') and data.get('extra_gain', 0) > 0:
                score = "優秀"
                score_color = QColor(76, 175, 80)
            elif data.get('filled_dividend'):
                score = "良好"
                score_color = QColor(139, 195, 74)
            elif same_day_gain > 0:
                score = "普通"
                score_color = QColor(255, 193, 7)
            else:
                score = "不佳"
                score_color = QColor(244, 67, 54)

            score_item = QTableWidgetItem(score)
            score_item.setForeground(score_color)
            self.leading_stocks_table.setItem(row, 5, score_item)

    def refresh_trading_candidates(self):
        """刷新交易候選股票"""
        try:
            days_ahead = self.days_spin.value()
            candidates = self.trading_system.get_trading_candidates(days_ahead)
            
            self.candidates_table.setRowCount(len(candidates))
            self.buy_stock_combo.clear()
            
            for row, candidate in enumerate(candidates):
                self.candidates_table.setItem(row, 0, QTableWidgetItem(candidate['stock_code']))
                self.candidates_table.setItem(row, 1, QTableWidgetItem(candidate['stock_name']))
                self.candidates_table.setItem(row, 2, QTableWidgetItem(candidate['ex_date']))
                self.candidates_table.setItem(row, 3, QTableWidgetItem(f"{candidate['cash_dividend']:.1f}"))
                self.candidates_table.setItem(row, 4, QTableWidgetItem(str(candidate['days_to_ex'])))
                
                # 績優股標記 - 改善對比度
                is_premium = "是" if candidate['is_premium_stock'] else "否"
                premium_item = QTableWidgetItem(is_premium)
                if candidate['is_premium_stock']:
                    # 使用深藍色背景配白色文字，確保高對比度
                    premium_item.setBackground(QColor(25, 118, 210))  # 深藍色背景
                    premium_item.setForeground(QColor(255, 255, 255))  # 白色文字
                    premium_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
                else:
                    # 普通股票使用淺灰色背景配深色文字
                    premium_item.setBackground(QColor(245, 245, 245))  # 淺灰色背景
                    premium_item.setForeground(QColor(66, 66, 66))     # 深灰色文字
                self.candidates_table.setItem(row, 5, premium_item)
                
                # 建議策略
                strategy = candidate['recommended_action'].get('strategy', '標準策略')
                self.candidates_table.setItem(row, 6, QTableWidgetItem(strategy))
                
                # 風險等級 - 改善對比度
                risk_level = candidate['recommended_action'].get('risk_level', 'medium')
                risk_item = QTableWidgetItem(risk_level)
                if risk_level == 'low':
                    # 低風險：深綠色背景配白色文字
                    risk_item.setBackground(QColor(46, 125, 50))   # 深綠色
                    risk_item.setForeground(QColor(255, 255, 255)) # 白色文字
                elif risk_level == 'high':
                    # 高風險：深紅色背景配白色文字
                    risk_item.setBackground(QColor(198, 40, 40))   # 深紅色
                    risk_item.setForeground(QColor(255, 255, 255)) # 白色文字
                else:
                    # 中等風險：深橙色背景配白色文字
                    risk_item.setBackground(QColor(230, 126, 34))  # 深橙色
                    risk_item.setForeground(QColor(255, 255, 255)) # 白色文字
                risk_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
                self.candidates_table.setItem(row, 7, risk_item)
                
                # 添加到買進下拉選單
                self.buy_stock_combo.addItem(f"{candidate['stock_code']} {candidate['stock_name']}")
            
            self.log_message(f"🔄 已刷新 {len(candidates)} 個交易候選股票")
            
        except Exception as e:
            self.log_message(f"❌ 刷新候選股票失敗: {e}")
            
    def log_message(self, message):
        """記錄日誌消息"""
        if self.auto_log_checkbox.isChecked():
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}"
            self.trading_log.append(log_entry)
            
            # 自動滾動到底部
            scrollbar = self.trading_log.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
    
    def manual_buy_stock(self):
        """手動買進股票"""
        try:
            stock_text = self.buy_stock_combo.currentText()
            if not stock_text:
                QMessageBox.warning(self, "警告", "請選擇股票代碼")
                return
                
            stock_code = stock_text.split()[0]
            
            # 執行買進
            result = self.trading_system.execute_buy_signal(stock_code)
            
            if result['success']:
                self.log_message(f"✅ {stock_code} 買進成功: {result['quantity']}股 @ {result['price']}元")
                QMessageBox.information(self, "成功", f"買進成功!\n{stock_code}: {result['quantity']}股 @ {result['price']}元")
            else:
                self.log_message(f"❌ {stock_code} 買進失敗: {result['reason']}")
                QMessageBox.warning(self, "失敗", f"買進失敗:\n{result['reason']}")
                
        except Exception as e:
            self.log_message(f"❌ 手動買進失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"手動買進失敗:\n{e}")
    
    def analyze_ex_dividend_performance(self):
        """分析除權息表現"""
        # 這裡應該讓用戶選擇要分析的股票
        # 簡化起見，使用第一個候選股票
        if self.buy_stock_combo.count() > 0:
            stock_text = self.buy_stock_combo.currentText()
            stock_code = stock_text.split()[0]
            
            decision = self.trading_system.analyze_ex_dividend_performance(stock_code)
            
            decision_text = f"""
🎯 {stock_code} 除權息當日分析:
• 建議動作: {decision['action']}
• 理由: {decision['reason']}
• 緊急程度: {decision.get('urgency', 'N/A')}
• 目標價格: {decision.get('target_price', 'N/A')}
            """
            self.sell_decision_text.setText(decision_text)
            
            # 如果建議賣出，啟用賣出按鈕
            if decision['action'] in ['SELL', 'PARTIAL_SELL']:
                self.execute_sell_btn.setEnabled(True)
                self.current_sell_decision = decision
                self.current_sell_stock = stock_code
            else:
                self.execute_sell_btn.setEnabled(False)
                
            self.log_message(f"📊 {stock_code} 分析完成: {decision['action']} - {decision['reason']}")
    
    def execute_sell_order(self):
        """執行賣出訂單"""
        try:
            if hasattr(self, 'current_sell_decision') and hasattr(self, 'current_sell_stock'):
                result = self.trading_system.execute_sell_signal(
                    self.current_sell_stock, 
                    self.current_sell_decision
                )
                
                if result['success']:
                    profit_text = f" (損益: {result['profit_loss']:+.2f}元)" if result.get('profit_loss') else ""
                    self.log_message(f"✅ {self.current_sell_stock} 賣出成功: {result['quantity']}股 @ {result['price']}元{profit_text}")
                    QMessageBox.information(self, "成功", f"賣出成功!\n{self.current_sell_stock}: {result['quantity']}股 @ {result['price']}元{profit_text}")
                    
                    # 清空決策
                    self.sell_decision_text.clear()
                    self.execute_sell_btn.setEnabled(False)
                else:
                    self.log_message(f"❌ {self.current_sell_stock} 賣出失敗: {result['reason']}")
                    QMessageBox.warning(self, "失敗", f"賣出失敗:\n{result['reason']}")
                    
        except Exception as e:
            self.log_message(f"❌ 執行賣出失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"執行賣出失敗:\n{e}")
    
    def refresh_performance_stats(self):
        """刷新績效統計"""
        try:
            report = self.trading_system.generate_trading_report()
            
            if 'error' not in report:
                summary = report['summary']
                self.total_trades_label.setText(f"總交易次數: {summary['total_trades']}")
                self.total_profit_label.setText(f"總損益: ${summary['total_profit_loss']:,.2f}")
                self.win_rate_label.setText(f"勝率: {summary['win_rate']:.1f}%")
                
                self.log_message("📈 績效統計已更新")
            else:
                self.log_message(f"❌ 績效統計更新失敗: {report['error']}")
                
        except Exception as e:
            self.log_message(f"❌ 刷新績效統計失敗: {e}")
    
    def clear_trading_log(self):
        """清空交易日誌"""
        self.trading_log.clear()
        self.log_message("🗑️ 交易日誌已清空")

    def load_stock_list_for_query(self):
        """載入股票清單供查詢使用"""
        try:
            import sqlite3
            import os

            # 優先從PE資料庫載入股票清單 (更準確的個股數據)
            pe_db_path = "D:/Finlab/history/tables/pe_data.db"
            if os.path.exists(pe_db_path):
                self.log_message("📊 從PE資料庫載入股票清單...")
                conn = sqlite3.connect(pe_db_path)
                cursor = conn.cursor()

                cursor.execute("SELECT DISTINCT 股票代號, 名稱 FROM pe_data ORDER BY 股票代號")
                stocks = cursor.fetchall()
                conn.close()

                self.query_stock_combo.clear()
                self.query_stock_combo.addItem("請選擇股票...", "")

                for stock_code, stock_name in stocks:
                    display_text = f"{stock_code} {stock_name}" if stock_name else stock_code
                    self.query_stock_combo.addItem(display_text, stock_code)

                self.log_message(f"✅ 從PE資料庫載入 {len(stocks)} 支股票供查詢")
            else:
                # 如果PE資料庫不存在，從除權息資料庫載入
                self.log_message("⚠️ 找不到PE資料庫，嘗試從除權息資料庫載入股票清單")
                self.load_stocks_from_dividend_db()

        except Exception as e:
            self.log_message(f"❌ 載入股票清單失敗: {e}")
            self.load_stocks_from_dividend_db()

    def load_stocks_from_dividend_db(self):
        """從除權息資料庫載入股票清單"""
        try:
            import sqlite3
            import os

            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            if os.path.exists(dividend_db_path):
                conn = sqlite3.connect(dividend_db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT stock_code, stock_name
                    FROM dividend_data
                    WHERE (data_source IS NULL OR data_source != 'sample_data')
                    ORDER BY stock_code
                """)
                stocks = cursor.fetchall()
                conn.close()

                self.query_stock_combo.clear()
                self.query_stock_combo.addItem("請選擇股票...", "")

                for stock_code, stock_name in stocks:
                    display_text = f"{stock_code} {stock_name}" if stock_name else stock_code
                    self.query_stock_combo.addItem(display_text, stock_code)

                self.log_message(f"✅ 從除權息資料庫載入 {len(stocks)} 支股票")
            else:
                self.log_message("⚠️ 找不到股票資料庫，請先下載除權息資料")

        except Exception as e:
            self.log_message(f"❌ 從除權息資料庫載入股票清單失敗: {e}")

    def query_dividend_data(self):
        """查詢除權息資料"""
        try:
            # 獲取選擇的股票和年份
            selected_stock = self.query_stock_combo.currentData()
            if not selected_stock:
                # 嘗試從輸入文字中提取股票代碼
                input_text = self.query_stock_combo.currentText().strip()
                if input_text and input_text != "請選擇股票...":
                    selected_stock = input_text.split()[0]  # 取第一個詞作為股票代碼
                else:
                    QMessageBox.warning(self, "警告", "請選擇或輸入股票代碼")
                    return

            selected_year = self.query_year_combo.currentText()

            self.log_message(f"🔍 查詢 {selected_stock} 在 {selected_year} 年的除權息資料...")

            # 從資料庫查詢
            dividend_records = self.get_dividend_history(selected_stock, selected_year)

            if dividend_records:
                # 顯示查詢結果摘要
                total_records = len(dividend_records)
                total_cash_dividend = sum(record.get('cash_dividend', 0) for record in dividend_records)

                result_text = f"""
📊 查詢結果摘要:
• 股票代碼: {selected_stock}
• 查詢年份: {selected_year}
• 除權息次數: {total_records} 次
• 現金股利總額: {total_cash_dividend:.2f} 元
• 資料來源: 除權息資料庫
                """
                self.query_result_text.setText(result_text.strip())

                # 更新詳細資料表格
                self.update_dividend_detail_table(dividend_records)

                self.log_message(f"✅ 查詢完成，找到 {total_records} 筆除權息記錄")
            else:
                self.query_result_text.setText(f"❌ 未找到 {selected_stock} 在 {selected_year} 年的除權息資料")
                self.dividend_detail_table.setRowCount(0)
                self.log_message(f"⚠️ 未找到 {selected_stock} 的除權息資料")

        except Exception as e:
            self.log_message(f"❌ 查詢除權息資料失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"查詢失敗:\n{e}")

    def get_dividend_history(self, stock_code, year):
        """從資料庫獲取除權息歷史資料"""
        try:
            import sqlite3
            import os

            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            if not os.path.exists(dividend_db_path):
                return []

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            # 查詢指定股票和年份的除權息資料，排除sample_data並真正去重
            # 使用GROUP BY來確保同一股票同一日期只有一筆記錄
            query = """
                SELECT stock_code, stock_name, year, ex_dividend_date,
                       MAX(cash_dividend) as cash_dividend,
                       MAX(stock_dividend) as stock_dividend,
                       MAX(total_dividend) as total_dividend,
                       MAX(dividend_yield) as dividend_yield,
                       MAX(eps) as eps,
                       MAX(data_source) as data_source
                FROM dividend_data
                WHERE stock_code = ? AND year = ?
                AND (data_source IS NULL OR data_source != 'sample_data')
                GROUP BY stock_code, ex_dividend_date
                ORDER BY ex_dividend_date
            """

            cursor.execute(query, (stock_code, year))
            results = cursor.fetchall()
            conn.close()

            # 轉換為字典格式
            records = []
            for row in results:
                record = {
                    'stock_code': row[0],
                    'stock_name': row[1] or '',
                    'year': row[2],
                    'ex_dividend_date': row[3],
                    'cash_dividend': float(row[4]) if row[4] else 0.0,
                    'stock_dividend': float(row[5]) if row[5] else 0.0,
                    'total_dividend': float(row[6]) if row[6] else 0.0,
                    'dividend_yield': float(row[7]) if row[7] else 0.0,
                    'eps': float(row[8]) if row[8] else 0.0,
                    'data_source': row[9] or ''
                }
                records.append(record)

            return records

        except Exception as e:
            self.log_message(f"❌ 獲取除權息歷史資料失敗: {e}")
            return []

    def update_dividend_detail_table(self, records):
        """更新除權息詳細資料表格"""
        try:
            self.dividend_detail_table.setRowCount(len(records))

            for row, record in enumerate(records):
                # 股票代碼
                stock_code = record['stock_code'] or 'N/A'
                self.dividend_detail_table.setItem(row, 0, QTableWidgetItem(str(stock_code)))

                # 股票名稱
                stock_name = record['stock_name'] or 'N/A'
                self.dividend_detail_table.setItem(row, 1, QTableWidgetItem(str(stock_name)))

                # 年份
                self.dividend_detail_table.setItem(row, 2, QTableWidgetItem(str(record['year'])))

                # 除權息日
                ex_date = record['ex_dividend_date'] or 'N/A'
                self.dividend_detail_table.setItem(row, 3, QTableWidgetItem(str(ex_date)))

                # 現金股利
                cash_dividend = record['cash_dividend']
                self.dividend_detail_table.setItem(row, 4, QTableWidgetItem(f"{cash_dividend:.2f}"))

                # 股票股利
                stock_dividend = record['stock_dividend']
                self.dividend_detail_table.setItem(row, 5, QTableWidgetItem(f"{stock_dividend:.2f}"))

                # 總股利
                total_dividend = record['total_dividend']
                self.dividend_detail_table.setItem(row, 6, QTableWidgetItem(f"{total_dividend:.2f}"))

                # 殖利率 - 如果資料庫沒有，則計算
                dividend_yield = record['dividend_yield']
                if dividend_yield == 0.0 and cash_dividend > 0:
                    # 嘗試計算殖利率
                    latest_price = self.get_latest_stock_price(stock_code)
                    if latest_price and latest_price > 0:
                        dividend_yield = (cash_dividend / latest_price) * 100

                yield_item = QTableWidgetItem(f"{dividend_yield:.2f}%" if dividend_yield > 0 else "N/A")
                if dividend_yield > 5.0:
                    yield_item.setForeground(QColor(76, 175, 80))  # 綠色 - 高殖利率
                elif dividend_yield > 3.0:
                    yield_item.setForeground(QColor(255, 193, 7))  # 黃色 - 中等殖利率
                elif dividend_yield > 0:
                    yield_item.setForeground(QColor(244, 67, 54))  # 紅色 - 低殖利率
                self.dividend_detail_table.setItem(row, 7, yield_item)

                # 每股盈餘 - 優先使用除權息資料庫，其次嘗試PE數據庫
                eps = record['eps']

                # 如果除權息資料庫沒有EPS資料，嘗試從PE數據庫獲取
                if eps <= 0 and self.pe_data_provider:
                    pe_eps = self.get_eps_from_pe_database(record['stock_code'], record['year'])
                    if pe_eps and pe_eps > 0:
                        eps = pe_eps
                        logging.info(f"📊 使用PE數據庫EPS: {record['stock_code']} = {eps:.2f}")

                eps_item = QTableWidgetItem(f"{eps:.2f}" if eps > 0 else "N/A")
                if eps > 5.0:
                    eps_item.setForeground(QColor(76, 175, 80))  # 綠色 - 高EPS
                elif eps > 2.0:
                    eps_item.setForeground(QColor(255, 193, 7))  # 黃色 - 中等EPS
                elif eps > 0:
                    eps_item.setForeground(QColor(244, 67, 54))  # 紅色 - 低EPS

                # 如果是從PE數據庫獲取的，添加標記
                if eps > 0 and record['eps'] <= 0 and self.pe_data_provider:
                    eps_item.setToolTip("📊 資料來源: PE數據庫計算")

                self.dividend_detail_table.setItem(row, 8, eps_item)

                # 最近股價
                try:
                    latest_price = self.get_latest_stock_price(stock_code)
                    if latest_price and latest_price > 0:
                        price_item = QTableWidgetItem(f"{latest_price:.2f}")
                        price_item.setForeground(QColor(33, 150, 243))  # 藍色 - 股價
                    else:
                        price_item = QTableWidgetItem("N/A")
                        price_item.setForeground(QColor(128, 128, 128))  # 灰色 - 無資料
                except Exception as e:
                    price_item = QTableWidgetItem("N/A")
                    price_item.setForeground(QColor(128, 128, 128))  # 灰色 - 錯誤

                self.dividend_detail_table.setItem(row, 9, price_item)

                # 資料來源
                data_source = record['data_source'] or ''
                self.dividend_detail_table.setItem(row, 10, QTableWidgetItem(data_source))

            # 調整欄位寬度
            self.dividend_detail_table.resizeColumnsToContents()

        except Exception as e:
            self.log_message(f"❌ 更新詳細資料表格失敗: {e}")

    def get_latest_stock_price(self, stock_code):
        """獲取最近一個交易日的股價"""
        try:
            # 首先嘗試從股價資料庫獲取
            price = self.get_price_from_database(stock_code)
            if price and price > 0:
                return price

            # 如果資料庫沒有資料，嘗試即時獲取（但不要在批量處理時使用）
            # return self.get_real_time_price(stock_code)
            return None

        except Exception as e:
            return None

    def get_price_from_database(self, stock_code):
        """從股價資料庫獲取最近股價"""
        try:
            import sqlite3
            import os

            price_db_path = "D:/Finlab/history/tables/price.db"
            if not os.path.exists(price_db_path):
                return None

            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()

            # 查詢最近的股價資料 - 使用正確的欄位名稱
            cursor.execute("""
                SELECT Close FROM stock_daily_data
                WHERE stock_id = ?
                ORDER BY date DESC
                LIMIT 1
            """, (stock_code,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return float(result[0])
            return None

        except Exception as e:
            return None

    def get_real_time_price(self, stock_code):
        """獲取即時股價"""
        try:
            from core_web_crawler import YahooFinanceCrawler

            crawler = YahooFinanceCrawler()
            result = crawler.get_single_stock_price(stock_code)

            if result and 'current_price' in result:
                return float(result['current_price'])
            return None

        except Exception as e:
            return None

    def open_dividend_folder(self):
        """開啟除權息目錄"""
        try:
            import os
            import platform
            import subprocess

            # 除權息資料庫目錄 - 使用正確的路徑格式
            dividend_folder = r"D:\Finlab\history\tables"

            # 檢查目錄是否存在
            if not os.path.exists(dividend_folder):
                # 嘗試替代路徑
                alt_folder = os.path.join(os.getcwd(), "history", "tables")
                if os.path.exists(alt_folder):
                    dividend_folder = alt_folder
                else:
                    QMessageBox.warning(self, "警告", f"除權息目錄不存在:\n{dividend_folder}\n\n請確認資料庫目錄路徑是否正確。")
                    return

            system = platform.system()

            # 轉換為正確的路徑格式
            dividend_folder = os.path.normpath(dividend_folder)

            if system == "Windows":
                # Windows 優先使用 os.startfile，最穩定的方法
                try:
                    os.startfile(dividend_folder)
                except Exception:
                    # 如果 startfile 失敗，使用 start 命令
                    subprocess.run(['cmd', '/c', 'start', '', dividend_folder], shell=True)
            elif system == "Darwin":  # macOS
                subprocess.run(['open', dividend_folder])
            else:  # Linux
                subprocess.run(['xdg-open', dividend_folder])

            self.log_message(f"✅ 已開啟除權息目錄: {dividend_folder}")

        except Exception as e:
            error_msg = f"開啟除權息目錄失敗: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            QMessageBox.critical(self, "錯誤", f"無法開啟除權息目錄:\n\n{error_msg}\n\n請手動開啟檔案管理器並導航到:\nD:\\Finlab\\history\\tables")

    def show_dividend_charts(self):
        """顯示除權息歷史圖表分析"""
        try:
            # 獲取選擇的股票
            selected_stock = self.query_stock_combo.currentData()
            if not selected_stock:
                input_text = self.query_stock_combo.currentText().strip()
                if input_text and input_text != "請選擇股票...":
                    selected_stock = input_text.split()[0]
                else:
                    QMessageBox.warning(self, "警告", "請選擇或輸入股票代碼")
                    return

            self.log_message(f"📊 生成 {selected_stock} 的除權息歷史圖表...")

            # 創建圖表分析窗口
            self.create_dividend_chart_window(selected_stock)

        except Exception as e:
            self.log_message(f"❌ 顯示圖表分析失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"圖表分析失敗:\n{e}")

    def create_dividend_chart_window(self, stock_code):
        """創建除權息圖表分析窗口"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTabWidget
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.figure import Figure
            import numpy as np

            # 創建對話框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"📊 {stock_code} 除權息歷史圖表分析")
            dialog.setGeometry(200, 200, 1200, 800)
            dialog.setModal(True)

            layout = QVBoxLayout(dialog)

            # 創建標籤頁
            tab_widget = QTabWidget()
            layout.addWidget(tab_widget)

            # 獲取多年歷史資料
            historical_data = self.get_multi_year_dividend_data(stock_code)

            if not historical_data:
                QMessageBox.warning(self, "警告", f"未找到 {stock_code} 的歷史除權息資料")
                return

            # 創建配息歷史圖表標籤頁
            dividend_tab = QWidget()
            dividend_layout = QVBoxLayout(dividend_tab)

            # 配息歷史圖表
            fig1 = Figure(figsize=(12, 6))
            canvas1 = FigureCanvas(fig1)
            dividend_layout.addWidget(canvas1)

            self.plot_dividend_history(fig1, historical_data, stock_code)

            tab_widget.addTab(dividend_tab, "📈 配息歷史趨勢")

            # 創建股價趨勢圖表標籤頁
            price_tab = QWidget()
            price_layout = QVBoxLayout(price_tab)

            # 股價趨勢圖表
            fig2 = Figure(figsize=(12, 6))
            canvas2 = FigureCanvas(fig2)
            price_layout.addWidget(canvas2)

            self.plot_price_trend_around_ex_date(fig2, historical_data, stock_code)

            tab_widget.addTab(price_tab, "📊 除權息前後股價趨勢")

            # 按鈕區域
            button_layout = QHBoxLayout()

            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            # 顯示對話框
            dialog.exec()

        except Exception as e:
            self.log_message(f"❌ 創建圖表分析窗口失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"創建圖表窗口失敗:\n{e}")

    def get_multi_year_dividend_data(self, stock_code):
        """獲取多年除權息資料"""
        try:
            import sqlite3
            import os

            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            if not os.path.exists(dividend_db_path):
                return []

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            # 獲取過去10年的除權息資料，排除sample_data並真正去重
            # 使用GROUP BY來確保同一年度同一日期只有一筆記錄
            query = """
                SELECT year, ex_dividend_date,
                       MAX(cash_dividend) as cash_dividend,
                       MAX(stock_dividend) as stock_dividend,
                       MAX(total_dividend) as total_dividend,
                       MAX(dividend_yield) as yield_rate,
                       MAX(eps) as fill_days
                FROM dividend_data
                WHERE stock_code = ?
                AND (data_source IS NULL OR data_source != 'sample_data')
                GROUP BY year, ex_dividend_date
                ORDER BY year DESC, ex_dividend_date DESC
                LIMIT 50
            """

            cursor.execute(query, (stock_code,))
            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            self.log_message(f"❌ 獲取多年除權息資料失敗: {e}")
            return []

    def plot_dividend_history(self, figure, data, stock_code):
        """繪製配息歷史趨勢圖"""
        try:
            if not data:
                return

            # 清除圖表
            figure.clear()

            # 準備資料
            years = [row[0] for row in data]
            cash_dividends = [row[2] or 0 for row in data]
            stock_dividends = [row[3] or 0 for row in data]
            total_dividends = [row[4] or 0 for row in data]
            yield_rates = [row[5] or 0 for row in data]

            # 反轉資料順序（從舊到新）
            years.reverse()
            cash_dividends.reverse()
            stock_dividends.reverse()
            total_dividends.reverse()
            yield_rates.reverse()

            # 創建子圖
            ax1 = figure.add_subplot(2, 1, 1)
            ax2 = figure.add_subplot(2, 1, 2)

            # 配息金額趨勢圖
            x_pos = range(len(years))
            width = 0.35

            ax1.bar([x - width/2 for x in x_pos], cash_dividends, width,
                   label='現金股利', color='#4CAF50', alpha=0.8)
            ax1.bar([x + width/2 for x in x_pos], stock_dividends, width,
                   label='股票股利', color='#2196F3', alpha=0.8)

            ax1.plot(x_pos, total_dividends, 'ro-', label='總股利', linewidth=2, markersize=6)

            ax1.set_xlabel('年份')
            ax1.set_ylabel('股利金額 (元)')
            ax1.set_title(f'{stock_code} 歷年配息趨勢')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(years, rotation=45)
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 殖利率趨勢圖
            ax2.plot(x_pos, yield_rates, 'go-', linewidth=2, markersize=6, color='#FF9800')
            ax2.fill_between(x_pos, yield_rates, alpha=0.3, color='#FF9800')

            ax2.set_xlabel('年份')
            ax2.set_ylabel('殖利率 (%)')
            ax2.set_title(f'{stock_code} 歷年殖利率趨勢')
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels(years, rotation=45)
            ax2.grid(True, alpha=0.3)

            # 添加平均線
            if yield_rates:
                avg_yield = sum(yield_rates) / len(yield_rates)
                ax2.axhline(y=avg_yield, color='red', linestyle='--',
                           label=f'平均殖利率: {avg_yield:.2f}%')
                ax2.legend()

            figure.tight_layout()

        except Exception as e:
            self.log_message(f"❌ 繪製配息歷史圖表失敗: {e}")

    def plot_price_trend_around_ex_date(self, figure, data, stock_code):
        """繪製除權息前後股價趨勢圖"""
        try:
            if not data:
                return

            # 清除圖表
            figure.clear()

            # 模擬除權息前後股價資料（實際應用中應從股價資料庫獲取）
            import numpy as np
            import random

            ax = figure.add_subplot(1, 1, 1)

            # 為每個除權息事件創建模擬股價趨勢
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

            for i, row in enumerate(data[:5]):  # 只顯示最近5次除權息
                year = row[0]
                cash_dividend = row[2] or 0

                # 模擬除權息前後兩週的股價資料
                days = list(range(-14, 15))  # 前後兩週
                base_price = 50 + random.uniform(-20, 20)  # 基準價格

                # 生成模擬股價
                prices = []
                for day in days:
                    if day < 0:  # 除權息前
                        price = base_price + random.uniform(-2, 3)
                    elif day == 0:  # 除權息日
                        price = base_price - cash_dividend + random.uniform(-1, 1)
                    else:  # 除權息後
                        # 模擬填息過程
                        fill_progress = min(day / 10, 1.0)  # 10天內填息
                        price = (base_price - cash_dividend) + (cash_dividend * fill_progress) + random.uniform(-1, 1)

                    prices.append(max(price, 10))  # 確保價格不會太低

                # 繪製股價線
                color = colors[i % len(colors)]
                ax.plot(days, prices, 'o-', label=f'{year}年 (股利:{cash_dividend:.1f}元)',
                       color=color, linewidth=2, markersize=4, alpha=0.8)

                # 標記除權息日
                ax.axvline(x=0, color=color, linestyle='--', alpha=0.5)

            # 設置圖表
            ax.set_xlabel('除權息日前後天數')
            ax.set_ylabel('股價 (元)')
            ax.set_title(f'{stock_code} 除權息前後股價趨勢 (模擬資料)')
            ax.axvline(x=0, color='red', linestyle='-', linewidth=2, alpha=0.7, label='除權息日')
            ax.grid(True, alpha=0.3)
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

            # 添加說明文字
            ax.text(0.02, 0.98, '註: 此為模擬資料，實際應用需連接股價資料庫',
                   transform=ax.transAxes, fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            figure.tight_layout()

        except Exception as e:
            self.log_message(f"❌ 繪製股價趨勢圖表失敗: {e}")

    def get_scoring_criteria_info(self):
        """獲取評分標準說明"""
        return """
📊 除權息交易系統評分標準說明:

🎯 當日表現評分:
• +3.0以上: 優秀 (90-100分) - 填息且額外上漲
• +1.0~+3.0: 良好 (70-89分) - 成功填息
• 0~+1.0: 普通 (50-69分) - 部分填息
• -1.0~0: 不佳 (30-49分) - 小幅貼息
• -1.0以下: 差 (0-29分) - 大幅貼息

💰 評分計算邏輯:
1. 基礎分數 = 50分
2. 填息加分 = (填息幅度 / 股利金額) × 30分
3. 額外漲幅加分 = 額外漲幅 × 10分
4. 最終分數 = 基礎分數 + 填息加分 + 額外漲幅加分

🏆 績優股特殊評分:
• 台積電(2330): 重視長期填息能力
• 鴻海(2317): 考慮預設停利點達成率
• 其他權值股: 根據歷史表現調整權重

📈 市場情緒影響:
• 牛市: 評分標準放寬10%
• 熊市: 評分標準收緊10%
• 震盪市: 使用標準評分
        """

    def show_scoring_criteria(self):
        """顯示評分標準說明"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 除權息交易系統評分標準")
            dialog.setGeometry(300, 300, 600, 500)
            dialog.setModal(True)

            layout = QVBoxLayout(dialog)

            # 評分標準文字
            criteria_text = QTextEdit()
            criteria_text.setReadOnly(True)
            criteria_text.setText(self.get_scoring_criteria_info())
            layout.addWidget(criteria_text)

            # 關閉按鈕
            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.log_message(f"❌ 顯示評分標準失敗: {e}")

    def on_query_mode_changed(self):
        """查詢模式改變時的處理"""
        current_mode = self.query_mode_combo.currentData()

        if current_mode == "stock":
            self.stock_query_widget.setVisible(True)
            self.date_range_query_widget.setVisible(False)
        elif current_mode == "date_range":
            self.stock_query_widget.setVisible(False)
            self.date_range_query_widget.setVisible(True)

    def set_date_range(self, days):
        """設置日期範圍 - 從今天開始算未來的日期範圍"""
        start_date = QDate.currentDate()
        end_date = start_date.addDays(days)

        self.start_date_edit.setDate(start_date)
        self.end_date_edit.setDate(end_date)

    def query_by_date_range(self):
        """按時間範圍查詢除權息資料"""
        try:
            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

            self.log_message(f"🔍 查詢 {start_date} 至 {end_date} 期間的除權息資料...")

            # 從資料庫查詢
            dividend_records = self.get_dividend_by_date_range(start_date, end_date)

            if dividend_records:
                # 顯示查詢結果摘要
                total_records = len(dividend_records)
                unique_stocks = len(set(record['stock_code'] for record in dividend_records))
                total_cash_dividend = sum(record.get('cash_dividend', 0) for record in dividend_records)

                result_text = f"""
📊 時間範圍查詢結果:
• 查詢期間: {start_date} 至 {end_date}
• 除權息股票數: {unique_stocks} 檔
• 除權息次數: {total_records} 次
• 現金股利總額: {total_cash_dividend:.2f} 元
• 資料來源: 除權息資料庫

💡 說明: "股利年度"是指股利來源年度，"除權息日"是實際執行日期
例如：2024年度盈餘在2025年7月除權息
                """
                self.query_result_text.setText(result_text.strip())

                # 更新詳細資料表格
                self.update_dividend_detail_table(dividend_records)

                self.log_message(f"✅ 查詢完成，找到 {unique_stocks} 檔股票的 {total_records} 筆除權息記錄")
            else:
                self.query_result_text.setText(f"❌ 在 {start_date} 至 {end_date} 期間未找到除權息資料")
                self.dividend_detail_table.setRowCount(0)
                self.log_message(f"⚠️ 指定期間內無除權息資料")

        except Exception as e:
            self.log_message(f"❌ 時間範圍查詢失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"查詢失敗:\n{e}")

    def get_dividend_by_date_range(self, start_date, end_date):
        """從資料庫獲取指定時間範圍的除權息資料"""
        try:
            import sqlite3
            import os

            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            if not os.path.exists(dividend_db_path):
                return []

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            # 查詢指定時間範圍的除權息資料
            # 修正：優先選擇年份與除權息日期年份接近的資料，排除明顯錯誤的歷史資料
            query = """
                SELECT stock_code, stock_name, year, ex_dividend_date,
                       cash_dividend, stock_dividend, total_dividend,
                       dividend_yield, eps, data_source
                FROM (
                    SELECT stock_code, stock_name, year, ex_dividend_date,
                           cash_dividend, stock_dividend, total_dividend,
                           dividend_yield, eps, data_source,
                           ROW_NUMBER() OVER (
                               PARTITION BY stock_code, ex_dividend_date
                               ORDER BY
                                   CASE WHEN data_source = 'goodinfo' THEN 1
                                        WHEN data_source = 'csv_import' THEN 2
                                        ELSE 3 END,
                                   ABS(year - CAST(substr(ex_dividend_date, 1, 4) AS INTEGER)),
                                   year DESC
                           ) as rn
                    FROM dividend_data
                    WHERE ex_dividend_date IS NOT NULL
                    AND ex_dividend_date != ''
                    AND ex_dividend_date >= ?
                    AND ex_dividend_date <= ?
                    AND (data_source IS NULL OR data_source != 'sample_data')
                    AND year >= 2020  -- 排除明顯過舊的資料
                ) ranked
                WHERE rn = 1
                ORDER BY ex_dividend_date, stock_code
            """

            self.log_message(f"🔍 執行優化查詢: {start_date} 到 {end_date}")

            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            conn.close()

            # 轉換為字典格式
            records = []
            for row in results:
                record = {
                    'stock_code': row[0],
                    'stock_name': row[1] or '',
                    'year': row[2],
                    'ex_dividend_date': row[3],
                    'cash_dividend': float(row[4]) if row[4] else 0.0,
                    'stock_dividend': float(row[5]) if row[5] else 0.0,
                    'total_dividend': float(row[6]) if row[6] else 0.0,
                    'dividend_yield': float(row[7]) if row[7] else 0.0,
                    'eps': float(row[8]) if row[8] else 0.0,
                    'data_source': row[9] or ''
                }
                records.append(record)

            return records

        except Exception as e:
            self.log_message(f"❌ 查詢資料庫失敗: {e}")
            return []

    def get_eps_from_pe_database(self, stock_code, year=None):
        """從PE數據庫獲取每股盈餘資料"""
        if not self.pe_data_provider:
            return None

        try:
            # 如果沒有指定年份，使用當前年份
            if not year:
                year = datetime.now().year

            # 構造日期字符串（使用年底日期）
            date_str = f"{year}-12-31"

            # 從PE數據庫獲取資料
            pe_data = self.pe_data_provider.get_pe_data(stock_code, date_str)

            if pe_data:
                # 檢查是否有EPS相關資料
                # PE數據庫可能包含本益比，可以通過股價/本益比計算EPS
                pe_ratio = pe_data.get('pe_ratio')
                if pe_ratio and pe_ratio > 0:
                    # 嘗試獲取股價來計算EPS - 使用PE數據庫中的股價資訊
                    try:
                        # 直接從PE數據中獲取股價相關資訊
                        if 'stock_price' in pe_data and pe_data['stock_price']:
                            stock_price = float(pe_data['stock_price'])
                            calculated_eps = stock_price / pe_ratio
                            logging.info(f"📊 從PE數據庫計算EPS: {stock_code} = {calculated_eps:.2f}")
                            return calculated_eps

                        # 如果PE數據中沒有股價，嘗試從備份數據庫獲取
                        backup_db_path = "D:/Finlab/backup/O3mh_strategy(20250713_全策略模組化_除權息_新聞)/goodinfo_data.db"
                        if os.path.exists(backup_db_path):
                            conn = sqlite3.connect(backup_db_path)
                            cursor = conn.cursor()

                            # 查詢股價數據
                            cursor.execute("""
                                SELECT close_price FROM stock_price
                                WHERE stock_code = ? AND date LIKE ?
                                ORDER BY date DESC LIMIT 1
                            """, (stock_code, f"{year}%"))

                            result = cursor.fetchone()
                            conn.close()

                            if result:
                                stock_price = result[0]
                                calculated_eps = stock_price / pe_ratio
                                logging.info(f"📊 從備份數據庫計算EPS: {stock_code} = {calculated_eps:.2f}")
                                return calculated_eps

                    except Exception as calc_error:
                        logging.warning(f"⚠️ 計算EPS失敗: {calc_error}")

                # 如果無法計算EPS，返回預設值或從PE數據中的EPS欄位獲取
                if 'eps' in pe_data and pe_data['eps']:
                    try:
                        eps_value = float(pe_data['eps'])
                        logging.info(f"📊 從PE數據庫直接獲取EPS: {stock_code} = {eps_value:.2f}")
                        return eps_value
                    except (ValueError, TypeError):
                        pass

            return None

        except Exception as e:
            logging.error(f"❌ 從PE數據庫獲取EPS失敗 {stock_code}: {e}")
            return None

    def show_five_year_analysis(self):
        """顯示5年歷史分析窗口"""
        try:
            # 獲取當前選中的股票 - 使用正確的combo box
            selected_stock = self.query_stock_combo.currentData()
            current_stock = self.query_stock_combo.currentText().strip()

            # 調試信息
            self.log_message(f"🔍 調試 - currentData(): {selected_stock}")
            self.log_message(f"🔍 調試 - currentText(): '{current_stock}'")
            self.log_message(f"🔍 調試 - combo count: {self.query_stock_combo.count()}")

            stock_code = None

            if selected_stock and selected_stock != "":
                # 從data獲取股票代碼
                stock_code = selected_stock
                self.log_message(f"✅ 從currentData獲取股票代碼: {stock_code}")
            elif current_stock and current_stock != "請選擇股票..." and current_stock != "":
                # 嘗試從輸入文字中提取股票代碼
                stock_code = self.extract_stock_code(current_stock)
                self.log_message(f"✅ 從currentText提取股票代碼: {stock_code}")
            else:
                self.log_message(f"❌ 沒有選擇股票或輸入為空")
                self.show_styled_message("警告", "請先選擇要分析的股票", "warning")
                return

            if not stock_code:
                self.log_message(f"❌ 無法解析股票代碼: '{current_stock}'")
                self.show_styled_message("錯誤", f"無法解析股票代碼: {current_stock}", "error")
                return

            self.log_message(f"📊 開始分析 {stock_code} 的5年歷史數據...")

            # 創建分析窗口
            self.create_five_year_analysis_window(stock_code)

        except Exception as e:
            self.log_message(f"❌ 開啟5年歷史分析失敗: {e}")
            self.show_styled_message("錯誤", f"開啟5年歷史分析失敗:\n{str(e)}", "error")

    def extract_stock_code(self, stock_text):
        """提取股票代碼 - 支援多種格式"""
        try:
            if not stock_text or stock_text.strip() == "":
                return None

            stock_text = stock_text.strip()

            # 格式1: "1264 道瑩" - 空格分隔
            if ' ' in stock_text:
                parts = stock_text.split(' ')
                if parts[0].isdigit():
                    return parts[0]

            # 格式2: "1264(道瑩)" - 括號分隔
            if '(' in stock_text:
                return stock_text.split('(')[0].strip()

            # 格式3: "1264-道瑩" - 破折號分隔
            if '-' in stock_text:
                return stock_text.split('-')[0].strip()

            # 格式4: 純數字股票代碼
            if stock_text.isdigit():
                return stock_text

            # 格式5: 提取開頭的數字部分
            import re
            match = re.match(r'^(\d+)', stock_text)
            if match:
                return match.group(1)

            return None

        except Exception as e:
            self.log_message(f"❌ 提取股票代碼失敗: {e}")
            return None

    def show_styled_message(self, title, message, msg_type="info"):
        """顯示樣式化的訊息框"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)

            # 設置樣式
            if msg_type == "warning":
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: #fff3cd;
                        color: #856404;
                        font-size: 14px;
                    }
                    QMessageBox QLabel {
                        color: #856404;
                        font-size: 14px;
                    }
                    QMessageBox QPushButton {
                        background-color: #ffc107;
                        color: #212529;
                        border: 1px solid #ffc107;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QMessageBox QPushButton:hover {
                        background-color: #e0a800;
                        border-color: #d39e00;
                    }
                """)
            elif msg_type == "error":
                msg_box.setIcon(QMessageBox.Icon.Critical)
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: #f8d7da;
                        color: #721c24;
                        font-size: 14px;
                    }
                    QMessageBox QLabel {
                        color: #721c24;
                        font-size: 14px;
                    }
                    QMessageBox QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: 1px solid #dc3545;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QMessageBox QPushButton:hover {
                        background-color: #c82333;
                        border-color: #bd2130;
                    }
                """)
            elif msg_type == "success":
                msg_box.setIcon(QMessageBox.Icon.Information)
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: #d4edda;
                        color: #155724;
                        font-size: 14px;
                    }
                    QMessageBox QLabel {
                        color: #155724;
                        font-size: 14px;
                    }
                    QMessageBox QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: 1px solid #28a745;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QMessageBox QPushButton:hover {
                        background-color: #218838;
                        border-color: #1e7e34;
                    }
                """)
            else:  # info
                msg_box.setIcon(QMessageBox.Icon.Information)
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: #d1ecf1;
                        color: #0c5460;
                        font-size: 14px;
                    }
                    QMessageBox QLabel {
                        color: #0c5460;
                        font-size: 14px;
                    }
                    QMessageBox QPushButton {
                        background-color: #17a2b8;
                        color: white;
                        border: 1px solid #17a2b8;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                        min-width: 80px;
                    }
                    QMessageBox QPushButton:hover {
                        background-color: #138496;
                        border-color: #117a8b;
                    }
                """)

            msg_box.exec()

        except Exception as e:
            # 如果樣式化訊息框失敗，使用標準訊息框
            if msg_type == "warning":
                QMessageBox.warning(self, title, message)
            elif msg_type == "error":
                QMessageBox.critical(self, title, message)
            else:
                QMessageBox.information(self, title, message)

    def create_five_year_analysis_window(self, stock_code):
        """創建5年歷史分析窗口"""
        try:
            # 創建對話框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"📊 {stock_code} - 5年除權息歷史分析")
            dialog.setGeometry(100, 100, 1400, 900)
            dialog.setModal(True)

            # 主布局
            main_layout = QVBoxLayout(dialog)

            # 標題區域
            title_layout = QHBoxLayout()
            title_label = QLabel(f"📊 {stock_code} 除權息5年歷史分析")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2196F3;
                    padding: 10px;
                }
            """)
            title_layout.addWidget(title_label)
            title_layout.addStretch()

            # 添加刷新按鈕
            refresh_btn = QPushButton("🔄 刷新數據")
            refresh_btn.clicked.connect(lambda: self.refresh_five_year_data(stock_code, tab_widget))
            title_layout.addWidget(refresh_btn)

            main_layout.addLayout(title_layout)

            # 創建標籤頁
            tab_widget = QTabWidget()
            main_layout.addWidget(tab_widget)

            # 載入並顯示數據
            self.load_five_year_analysis_data(stock_code, tab_widget)

            # 按鈕區域
            button_layout = QHBoxLayout()

            # 導出報告按鈕
            export_btn = QPushButton("📄 導出分析報告")
            export_btn.clicked.connect(lambda: self.export_five_year_report(stock_code))
            button_layout.addWidget(export_btn)

            button_layout.addStretch()

            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)
            button_layout.addWidget(close_btn)

            main_layout.addLayout(button_layout)

            # 顯示對話框
            dialog.exec()

        except Exception as e:
            self.log_message(f"❌ 創建5年歷史分析窗口失敗: {e}")
            self.show_styled_message("錯誤", f"創建分析窗口失敗: {str(e)}", "error")

    def load_five_year_analysis_data(self, stock_code, tab_widget):
        """載入5年歷史分析數據"""
        try:
            # 獲取5年歷史數據
            historical_data = self.get_five_year_dividend_data(stock_code)

            if not historical_data:
                # 創建無數據提示頁面
                no_data_tab = QWidget()
                layout = QVBoxLayout(no_data_tab)

                no_data_label = QLabel(f"⚠️ 沒有找到 {stock_code} 的歷史除權息數據")
                no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_data_label.setStyleSheet("font-size: 16px; color: #FF9800; padding: 50px;")
                layout.addWidget(no_data_label)

                tab_widget.addTab(no_data_tab, "📊 數據概覽")
                return

            # 1. 數據概覽頁面
            self.create_data_overview_tab(tab_widget, stock_code, historical_data)

            # 2. 除權息趨勢圖表頁面
            self.create_dividend_trend_tab(tab_widget, stock_code, historical_data)

            # 3. 股價變化分析頁面
            self.create_price_analysis_tab(tab_widget, stock_code, historical_data)

            # 4. 投資決策建議頁面
            self.create_investment_advice_tab(tab_widget, stock_code, historical_data)

            self.log_message(f"✅ 成功載入 {stock_code} 的5年歷史分析數據")

        except Exception as e:
            self.log_message(f"❌ 載入5年歷史分析數據失敗: {e}")

            # 創建錯誤提示頁面
            error_tab = QWidget()
            layout = QVBoxLayout(error_tab)

            error_label = QLabel(f"❌ 載入數據失敗: {str(e)}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("font-size: 14px; color: #F44336; padding: 50px;")
            layout.addWidget(error_label)

            tab_widget.addTab(error_tab, "❌ 錯誤")

    def get_five_year_dividend_data(self, stock_code):
        """獲取指定股票過去5年的除權息數據"""
        try:
            import sqlite3
            from datetime import datetime, timedelta

            # 計算5年前的日期
            current_year = datetime.now().year
            five_years_ago = current_year - 5

            # 連接除權息資料庫
            dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
            if not os.path.exists(dividend_db_path):
                self.log_message(f"⚠️ 除權息資料庫不存在: {dividend_db_path}")
                return []

            conn = sqlite3.connect(dividend_db_path)
            cursor = conn.cursor()

            # 查詢過去5年的除權息數據
            query = """
                SELECT year, stock_name, ex_dividend_date,
                       cash_dividend, stock_dividend, total_dividend,
                       dividend_yield, eps, data_source
                FROM dividend_data
                WHERE stock_code = ?
                AND year >= ?
                AND ex_dividend_date IS NOT NULL
                AND ex_dividend_date != ''
                ORDER BY year DESC, ex_dividend_date DESC
            """

            cursor.execute(query, (stock_code, five_years_ago))
            results = cursor.fetchall()
            conn.close()

            if results:
                self.log_message(f"📊 找到 {stock_code} 過去5年的 {len(results)} 筆除權息記錄")
            else:
                self.log_message(f"⚠️ 沒有找到 {stock_code} 過去5年的除權息記錄")

            return results

        except Exception as e:
            self.log_message(f"❌ 獲取5年除權息數據失敗: {e}")
            return []

    def get_stock_price_around_ex_date(self, stock_code, ex_date, days_before=14, days_after=14):
        """獲取除權息日前後指定天數的股價數據"""
        try:
            import sqlite3
            from datetime import datetime, timedelta

            # 解析除權息日期
            if not ex_date:
                return []

            try:
                ex_datetime = datetime.strptime(ex_date, '%Y-%m-%d')
            except ValueError:
                try:
                    ex_datetime = datetime.strptime(ex_date, '%Y/%m/%d')
                except ValueError:
                    self.log_message(f"⚠️ 無法解析除權息日期: {ex_date}")
                    return []

            # 計算查詢日期範圍
            start_date = ex_datetime - timedelta(days=days_before)
            end_date = ex_datetime + timedelta(days=days_after)

            # 連接股價資料庫
            price_db_path = "D:/Finlab/history/tables/price.db"
            if not os.path.exists(price_db_path):
                self.log_message(f"⚠️ 股價資料庫不存在: {price_db_path}")
                return self.generate_simulated_price_data(ex_datetime, days_before, days_after)

            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()

            # 嘗試不同的表名和欄位名
            table_queries = [
                ("stock_daily_data", "stock_id", "date", "Open", "High", "Low", "Close", "Volume"),
                ("stock_data", "stock_id", "date", "open", "high", "low", "close", "volume"),
                ("daily_data", "code", "trading_date", "open_price", "high_price", "low_price", "close_price", "volume")
            ]

            price_data = []
            for table_name, stock_col, date_col, open_col, high_col, low_col, close_col, vol_col in table_queries:
                try:
                    query = f"""
                        SELECT {date_col}, {open_col}, {high_col}, {low_col}, {close_col}, {vol_col}
                        FROM {table_name}
                        WHERE {stock_col} = ?
                        AND {date_col} >= ?
                        AND {date_col} <= ?
                        ORDER BY {date_col}
                    """

                    cursor.execute(query, (stock_code, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
                    results = cursor.fetchall()

                    if results:
                        price_data = results
                        self.log_message(f"📈 從 {table_name} 獲取到 {len(results)} 筆股價數據")
                        break

                except sqlite3.OperationalError:
                    continue

            conn.close()

            if not price_data:
                self.log_message(f"⚠️ 沒有找到 {stock_code} 在 {ex_date} 前後的股價數據，使用模擬數據")
                return self.generate_simulated_price_data(ex_datetime, days_before, days_after)

            return price_data

        except Exception as e:
            self.log_message(f"❌ 獲取股價數據失敗: {e}")
            return self.generate_simulated_price_data(ex_datetime if 'ex_datetime' in locals() else datetime.now(), days_before, days_after)

    def generate_simulated_price_data(self, ex_datetime, days_before=14, days_after=14, base_price=50):
        """生成模擬的股價數據"""
        try:
            import random
            from datetime import timedelta

            price_data = []
            current_price = base_price + random.uniform(-10, 10)

            # 生成除權息前後的模擬股價
            for i in range(-days_before, days_after + 1):
                date = ex_datetime + timedelta(days=i)

                # 模擬價格波動
                if i < 0:  # 除權息前
                    price_change = random.uniform(-0.5, 1.0)
                elif i == 0:  # 除權息日，假設下跌2-5元
                    price_change = -random.uniform(2, 5)
                else:  # 除權息後，模擬填息過程
                    fill_rate = min(i / 10, 1.0)  # 10天內逐漸填息
                    price_change = random.uniform(-0.3, 0.8) + (fill_rate * 0.5)

                current_price += price_change
                current_price = max(current_price, 10)  # 確保價格不會太低

                # 生成OHLCV數據
                open_price = current_price + random.uniform(-0.5, 0.5)
                high_price = max(open_price, current_price) + random.uniform(0, 1)
                low_price = min(open_price, current_price) - random.uniform(0, 1)
                close_price = current_price
                volume = random.randint(1000, 10000) * 1000

                price_data.append((
                    date.strftime('%Y-%m-%d'),
                    round(open_price, 2),
                    round(high_price, 2),
                    round(low_price, 2),
                    round(close_price, 2),
                    volume
                ))

            return price_data

        except Exception as e:
            self.log_message(f"❌ 生成模擬股價數據失敗: {e}")
            return []

    def create_data_overview_tab(self, tab_widget, stock_code, historical_data):
        """創建數據概覽頁面"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 標題
            title_label = QLabel(f"📊 {stock_code} 除權息數據概覽")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; padding: 10px;")
            layout.addWidget(title_label)

            # 統計摘要
            summary_group = QGroupBox("📈 5年統計摘要")
            summary_layout = QVBoxLayout(summary_group)

            # 計算統計數據
            years = list(set([row[0] for row in historical_data]))
            years.sort(reverse=True)

            total_cash_dividend = sum([row[3] or 0 for row in historical_data])
            total_stock_dividend = sum([row[4] or 0 for row in historical_data])
            avg_dividend_yield = sum([row[6] or 0 for row in historical_data if row[6]]) / len([row[6] for row in historical_data if row[6]]) if any(row[6] for row in historical_data) else 0

            # 統計信息顯示
            stats_text = f"""
📅 分析期間: {min(years)} - {max(years)} ({len(years)}年)
📊 除權息次數: {len(historical_data)}次
💰 累計現金股利: {total_cash_dividend:.2f}元
📈 累計股票股利: {total_stock_dividend:.2f}元
📊 平均殖利率: {avg_dividend_yield:.2f}%
            """

            stats_label = QLabel(stats_text)
            stats_label.setStyleSheet("font-size: 14px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;")
            summary_layout.addWidget(stats_label)

            layout.addWidget(summary_group)

            # 詳細數據表格
            table_group = QGroupBox("📋 詳細除權息記錄")
            table_layout = QVBoxLayout(table_group)

            # 創建表格
            table = QTableWidget()
            table.setColumnCount(8)
            table.setHorizontalHeaderLabels([
                "年份", "除權息日", "現金股利", "股票股利", "總股利", "殖利率", "每股盈餘", "資料來源"
            ])

            # 填充數據
            table.setRowCount(len(historical_data))
            for row, data in enumerate(historical_data):
                table.setItem(row, 0, QTableWidgetItem(str(data[0])))
                table.setItem(row, 1, QTableWidgetItem(data[2] or ""))
                table.setItem(row, 2, QTableWidgetItem(f"{data[3]:.2f}" if data[3] else "0.00"))
                table.setItem(row, 3, QTableWidgetItem(f"{data[4]:.2f}" if data[4] else "0.00"))
                table.setItem(row, 4, QTableWidgetItem(f"{data[5]:.2f}" if data[5] else "0.00"))
                table.setItem(row, 5, QTableWidgetItem(f"{data[6]:.2f}%" if data[6] else "N/A"))
                table.setItem(row, 6, QTableWidgetItem(f"{data[7]:.2f}" if data[7] else "N/A"))
                table.setItem(row, 7, QTableWidgetItem(data[8] or ""))

            # 設置表格樣式
            table.resizeColumnsToContents()
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

            table_layout.addWidget(table)
            layout.addWidget(table_group)

            tab_widget.addTab(tab, "📊 數據概覽")

        except Exception as e:
            self.log_message(f"❌ 創建數據概覽頁面失敗: {e}")

    def create_dividend_trend_tab(self, tab_widget, stock_code, historical_data):
        """創建除權息趨勢圖表頁面"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            import matplotlib.pyplot as plt

            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 標題
            title_label = QLabel(f"📈 {stock_code} 除權息趨勢分析")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; padding: 10px;")
            layout.addWidget(title_label)

            # 創建圖表
            fig = Figure(figsize=(14, 10))
            canvas = FigureCanvas(fig)
            layout.addWidget(canvas)

            # 準備數據
            years = [row[0] for row in historical_data]
            cash_dividends = [row[3] or 0 for row in historical_data]
            stock_dividends = [row[4] or 0 for row in historical_data]
            total_dividends = [row[5] or 0 for row in historical_data]
            dividend_yields = [row[6] or 0 for row in historical_data]

            # 按年份排序
            sorted_data = sorted(zip(years, cash_dividends, stock_dividends, total_dividends, dividend_yields))
            years, cash_dividends, stock_dividends, total_dividends, dividend_yields = zip(*sorted_data)

            # 創建子圖
            ax1 = fig.add_subplot(3, 1, 1)
            ax2 = fig.add_subplot(3, 1, 2)
            ax3 = fig.add_subplot(3, 1, 3)

            # 1. 股利金額趨勢
            x_pos = range(len(years))
            width = 0.35

            ax1.bar([x - width/2 for x in x_pos], cash_dividends, width,
                   label='現金股利', color='#4CAF50', alpha=0.8)
            ax1.bar([x + width/2 for x in x_pos], stock_dividends, width,
                   label='股票股利', color='#2196F3', alpha=0.8)
            ax1.plot(x_pos, total_dividends, 'ro-', label='總股利', linewidth=2, markersize=6)

            ax1.set_title('歷年股利發放趨勢', fontsize=14, fontweight='bold')
            ax1.set_ylabel('股利金額 (元)')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(years)
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 殖利率趨勢
            ax2.plot(x_pos, dividend_yields, 'go-', linewidth=2, markersize=6, color='#FF9800')
            ax2.fill_between(x_pos, dividend_yields, alpha=0.3, color='#FF9800')

            ax2.set_title('歷年殖利率趨勢', fontsize=14, fontweight='bold')
            ax2.set_ylabel('殖利率 (%)')
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels(years)
            ax2.grid(True, alpha=0.3)

            # 添加平均線
            if dividend_yields:
                avg_yield = sum(dividend_yields) / len(dividend_yields)
                ax2.axhline(y=avg_yield, color='red', linestyle='--',
                           label=f'平均殖利率: {avg_yield:.2f}%')
                ax2.legend()

            # 3. 股利成長率分析
            if len(total_dividends) > 1:
                growth_rates = []
                growth_years = []
                for i in range(1, len(total_dividends)):
                    if total_dividends[i-1] > 0:
                        growth_rate = ((total_dividends[i] - total_dividends[i-1]) / total_dividends[i-1]) * 100
                        growth_rates.append(growth_rate)
                        growth_years.append(years[i])

                if growth_rates:
                    colors = ['green' if rate >= 0 else 'red' for rate in growth_rates]
                    ax3.bar(range(len(growth_rates)), growth_rates, color=colors, alpha=0.7)
                    ax3.set_title('股利成長率分析', fontsize=14, fontweight='bold')
                    ax3.set_ylabel('成長率 (%)')
                    ax3.set_xticks(range(len(growth_rates)))
                    ax3.set_xticklabels(growth_years)
                    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
                    ax3.grid(True, alpha=0.3)

            fig.tight_layout()
            canvas.draw()

            tab_widget.addTab(tab, "📈 趨勢圖表")

        except Exception as e:
            self.log_message(f"❌ 創建趨勢圖表頁面失敗: {e}")
            # 創建錯誤提示
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"❌ 圖表載入失敗: {str(e)}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(error_label)
            tab_widget.addTab(tab, "📈 趨勢圖表")

    def create_price_analysis_tab(self, tab_widget, stock_code, historical_data):
        """創建股價變化分析頁面"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from datetime import datetime

            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 標題
            title_label = QLabel(f"📊 {stock_code} 除權息前後股價分析")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; padding: 10px;")
            layout.addWidget(title_label)

            # 創建圖表
            fig = Figure(figsize=(14, 12))
            canvas = FigureCanvas(fig)
            layout.addWidget(canvas)

            # 分析最近3年的除權息事件
            recent_data = [row for row in historical_data if row[2]][:3]  # 取最近3次有除權息日的記錄

            if not recent_data:
                # 沒有數據時顯示提示
                ax = fig.add_subplot(1, 1, 1)
                ax.text(0.5, 0.5, '沒有足夠的除權息數據進行股價分析',
                       ha='center', va='center', fontsize=16, transform=ax.transAxes)
                ax.set_title('股價分析')
                fig.tight_layout()
                canvas.draw()
                tab_widget.addTab(tab, "📊 股價分析")
                return

            # 為每個除權息事件創建子圖
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

            for i, data_row in enumerate(recent_data):
                year, stock_name, ex_date, cash_dividend, stock_dividend, total_dividend = data_row[:6]

                # 獲取除權息前後股價數據
                price_data = self.get_stock_price_around_ex_date(stock_code, ex_date)

                if not price_data:
                    continue

                # 創建子圖
                ax = fig.add_subplot(len(recent_data), 1, i + 1)

                # 準備股價數據
                dates = []
                closes = []
                volumes = []

                for price_row in price_data:
                    try:
                        date_str = price_row[0]
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        dates.append(date_obj)
                        closes.append(float(price_row[4]))  # 收盤價
                        volumes.append(int(price_row[5]))   # 成交量
                    except (ValueError, IndexError):
                        continue

                if not dates:
                    continue

                # 找到除權息日的索引
                ex_datetime = datetime.strptime(ex_date, '%Y-%m-%d')
                ex_index = None
                for idx, date in enumerate(dates):
                    if date.date() == ex_datetime.date():
                        ex_index = idx
                        break

                # 繪製股價線
                color = colors[i % len(colors)]
                ax.plot(dates, closes, 'o-', color=color, linewidth=2, markersize=4, alpha=0.8)

                # 標記除權息日
                if ex_index is not None:
                    ax.axvline(x=dates[ex_index], color='red', linestyle='--', linewidth=2, alpha=0.7)
                    ax.annotate(f'除權息日\n股利:{total_dividend:.2f}元',
                              xy=(dates[ex_index], closes[ex_index]),
                              xytext=(10, 10), textcoords='offset points',
                              bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                              arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

                # 計算填息情況
                if ex_index is not None and ex_index > 0:
                    pre_ex_price = closes[ex_index - 1]  # 除權息前一日價格
                    ex_day_price = closes[ex_index]      # 除權息日價格

                    # 計算理論除權息價格
                    theoretical_price = pre_ex_price - (total_dividend or 0)

                    # 添加理論價格線
                    ax.axhline(y=theoretical_price, color='orange', linestyle=':', alpha=0.7,
                              label=f'理論除權息價格: {theoretical_price:.2f}元')

                    # 分析填息情況
                    post_ex_prices = closes[ex_index + 1:] if ex_index + 1 < len(closes) else []
                    fill_days = None

                    for day, price in enumerate(post_ex_prices, 1):
                        if price >= pre_ex_price:
                            fill_days = day
                            break

                    fill_status = f"填息天數: {fill_days}天" if fill_days else "尚未完全填息"

                    # 添加填息信息
                    ax.text(0.02, 0.98, f'{year}年除權息分析\n{fill_status}',
                           transform=ax.transAxes, fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

                # 設置圖表
                ax.set_ylabel('股價 (元)')
                ax.set_title(f'{year}年除權息前後股價變化')
                ax.grid(True, alpha=0.3)
                ax.legend()

                # 格式化x軸日期
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

            fig.tight_layout()
            canvas.draw()

            tab_widget.addTab(tab, "📊 股價分析")

        except Exception as e:
            self.log_message(f"❌ 創建股價分析頁面失敗: {e}")
            # 創建錯誤提示
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"❌ 股價分析載入失敗: {str(e)}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(error_label)
            tab_widget.addTab(tab, "📊 股價分析")

    def create_investment_advice_tab(self, tab_widget, stock_code, historical_data):
        """創建投資決策建議頁面"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 標題
            title_label = QLabel(f"💡 {stock_code} 投資決策建議")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; padding: 10px;")
            layout.addWidget(title_label)

            # 分析結果
            analysis_group = QGroupBox("📊 綜合分析結果")
            analysis_layout = QVBoxLayout(analysis_group)

            # 計算關鍵指標
            years = list(set([row[0] for row in historical_data]))
            cash_dividends = [row[3] or 0 for row in historical_data if row[3]]
            dividend_yields = [row[6] or 0 for row in historical_data if row[6]]

            # 穩定性分析
            dividend_stability = "高" if len(set(cash_dividends)) <= 3 else "中" if len(set(cash_dividends)) <= 5 else "低"
            avg_yield = sum(dividend_yields) / len(dividend_yields) if dividend_yields else 0

            # 成長性分析
            if len(cash_dividends) >= 2:
                recent_dividends = sorted([(row[0], row[3] or 0) for row in historical_data if row[3]], reverse=True)[:3]
                growth_trend = "上升" if len(recent_dividends) >= 2 and recent_dividends[0][1] > recent_dividends[-1][1] else "穩定"
            else:
                growth_trend = "數據不足"

            # 投資建議評分
            score = 0
            if avg_yield >= 4:
                score += 30
            elif avg_yield >= 2:
                score += 20
            else:
                score += 10

            if dividend_stability == "高":
                score += 25
            elif dividend_stability == "中":
                score += 15
            else:
                score += 5

            if growth_trend == "上升":
                score += 25
            elif growth_trend == "穩定":
                score += 15
            else:
                score += 5

            if len(historical_data) >= 5:
                score += 20
            elif len(historical_data) >= 3:
                score += 10

            # 建議等級
            if score >= 80:
                recommendation = "🟢 強烈建議參與"
                risk_level = "低風險"
            elif score >= 60:
                recommendation = "🟡 建議參與"
                risk_level = "中等風險"
            elif score >= 40:
                recommendation = "🟠 謹慎參與"
                risk_level = "中高風險"
            else:
                recommendation = "🔴 不建議參與"
                risk_level = "高風險"

            # 顯示分析結果
            analysis_text = f"""
📊 綜合評分: {score}/100分

🎯 投資建議: {recommendation}
⚠️  風險等級: {risk_level}

📈 關鍵指標分析:
• 平均殖利率: {avg_yield:.2f}%
• 股利穩定性: {dividend_stability}
• 成長趨勢: {growth_trend}
• 歷史記錄: {len(historical_data)}次除權息

💡 具體建議:
"""

            if score >= 80:
                analysis_text += """
✅ 該股票除權息表現優異，建議積極參與
✅ 殖利率穩定且具有成長潛力
✅ 適合長期持有並參與每年除權息
"""
            elif score >= 60:
                analysis_text += """
✅ 該股票除權息表現良好，可以參與
⚠️  建議關注市場環境和公司基本面
✅ 適合穩健型投資者參與
"""
            elif score >= 40:
                analysis_text += """
⚠️  該股票除權息表現一般，需謹慎評估
⚠️  建議深入研究公司財務狀況
⚠️  適合有經驗的投資者參與
"""
            else:
                analysis_text += """
❌ 該股票除權息表現不佳，不建議參與
❌ 股利不穩定或殖利率過低
❌ 建議尋找其他投資標的
"""

            analysis_label = QLabel(analysis_text)
            analysis_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            analysis_layout.addWidget(analysis_label)

            layout.addWidget(analysis_group)

            # 風險提醒
            risk_group = QGroupBox("⚠️ 風險提醒")
            risk_layout = QVBoxLayout(risk_group)

            risk_text = """
📋 投資風險提醒:

1. 📊 歷史表現不代表未來結果
2. 💰 除權息投資需考慮稅務成本
3. 📈 股價波動可能影響實際收益
4. 🏢 公司基本面變化會影響配息政策
5. 🌍 總體經濟環境會影響股市表現

💡 建議:
• 分散投資，不要集中單一標的
• 定期檢視投資組合
• 關注公司財報和產業動態
• 設定停損停利點
"""

            risk_label = QLabel(risk_text)
            risk_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    padding: 10px;
                    background-color: #fff3cd;
                    border-radius: 5px;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                }
            """)
            risk_layout.addWidget(risk_label)

            layout.addWidget(risk_group)

            tab_widget.addTab(tab, "💡 投資建議")

        except Exception as e:
            self.log_message(f"❌ 創建投資建議頁面失敗: {e}")
            # 創建錯誤提示
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"❌ 投資建議載入失敗: {str(e)}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(error_label)
            tab_widget.addTab(tab, "💡 投資建議")

    def refresh_five_year_data(self, stock_code, tab_widget):
        """刷新5年歷史數據"""
        try:
            # 清除現有頁籤
            tab_widget.clear()

            # 重新載入數據
            self.load_five_year_analysis_data(stock_code, tab_widget)

            self.log_message(f"✅ 已刷新 {stock_code} 的5年歷史數據")

        except Exception as e:
            self.log_message(f"❌ 刷新5年歷史數據失敗: {e}")

    def export_five_year_report(self, stock_code):
        """導出5年分析報告"""
        try:
            from datetime import datetime
            import os

            # 獲取數據
            historical_data = self.get_five_year_dividend_data(stock_code)

            if not historical_data:
                self.show_styled_message("警告", "沒有數據可以導出", "warning")
                return

            # 創建報告內容
            report_content = self.generate_report_content(stock_code, historical_data)

            # 保存報告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{stock_code}_5年除權息分析報告_{timestamp}.txt"

            # 確保目錄存在
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            filepath = os.path.join(reports_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)

            self.log_message(f"✅ 分析報告已導出: {filepath}")
            self.show_styled_message("成功", f"分析報告已導出至:\n{filepath}", "success")

            # 詢問是否開啟報告
            reply = self.show_question_dialog("開啟報告", "是否要開啟導出的報告？")
            if reply:
                os.startfile(filepath)

        except Exception as e:
            self.log_message(f"❌ 導出分析報告失敗: {e}")
            self.show_styled_message("錯誤", f"導出報告失敗: {str(e)}", "error")

    def show_question_dialog(self, title, message):
        """顯示樣式化的問題對話框"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Icon.Question)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)

            # 設置樣式
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #e2e3e5;
                    color: #383d41;
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #383d41;
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: 1px solid #6c757d;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                    margin: 2px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #5a6268;
                    border-color: #545b62;
                }
                QMessageBox QPushButton:default {
                    background-color: #007bff;
                    border-color: #007bff;
                }
                QMessageBox QPushButton:default:hover {
                    background-color: #0069d9;
                    border-color: #0062cc;
                }
            """)

            result = msg_box.exec()
            return result == QMessageBox.StandardButton.Yes

        except Exception as e:
            # 如果樣式化對話框失敗，使用標準對話框
            reply = QMessageBox.question(self, title, message)
            return reply == QMessageBox.StandardButton.Yes

    def generate_report_content(self, stock_code, historical_data):
        """生成報告內容"""
        try:
            from datetime import datetime

            # 計算統計數據
            years = list(set([row[0] for row in historical_data]))
            years.sort(reverse=True)

            total_cash_dividend = sum([row[3] or 0 for row in historical_data])
            total_stock_dividend = sum([row[4] or 0 for row in historical_data])
            dividend_yields = [row[6] or 0 for row in historical_data if row[6]]
            avg_dividend_yield = sum(dividend_yields) / len(dividend_yields) if dividend_yields else 0

            # 生成報告
            report = f"""
{'='*60}
{stock_code} 除權息5年歷史分析報告
{'='*60}

📅 報告生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 分析期間: {min(years)} - {max(years)} ({len(years)}年)

{'='*60}
📈 統計摘要
{'='*60}

📊 除權息次數: {len(historical_data)}次
💰 累計現金股利: {total_cash_dividend:.2f}元
📈 累計股票股利: {total_stock_dividend:.2f}元
📊 平均殖利率: {avg_dividend_yield:.2f}%

{'='*60}
📋 詳細記錄
{'='*60}

"""

            # 添加詳細記錄
            report += f"{'年份':<8} {'除權息日':<12} {'現金股利':<10} {'股票股利':<10} {'總股利':<10} {'殖利率':<10} {'每股盈餘':<10}\n"
            report += "-" * 80 + "\n"

            for data in historical_data:
                year, stock_name, ex_date, cash_div, stock_div, total_div, yield_rate, eps = data[:8]
                report += f"{year:<8} {ex_date or 'N/A':<12} {cash_div or 0:<10.2f} {stock_div or 0:<10.2f} {total_div or 0:<10.2f} {yield_rate or 0:<10.2f} {eps or 0:<10.2f}\n"

            # 添加投資建議
            report += f"\n{'='*60}\n💡 投資建議\n{'='*60}\n"

            # 簡化的建議邏輯
            if avg_dividend_yield >= 4:
                report += "🟢 殖利率表現優異，建議積極參與除權息\n"
            elif avg_dividend_yield >= 2:
                report += "🟡 殖利率表現良好，可以考慮參與除權息\n"
            else:
                report += "🔴 殖利率偏低，需謹慎評估是否參與除權息\n"

            report += f"\n⚠️ 風險提醒: 歷史表現不代表未來結果，投資前請詳細評估風險\n"
            report += f"📊 本報告僅供參考，不構成投資建議\n"

            return report

        except Exception as e:
            return f"生成報告內容失敗: {str(e)}"

def main():
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("除權息交易系統")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("台股智能選股系統")
    
    # 創建主視窗
    window = DividendTradingGUI()
    window.show()
    
    # 啟動應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
