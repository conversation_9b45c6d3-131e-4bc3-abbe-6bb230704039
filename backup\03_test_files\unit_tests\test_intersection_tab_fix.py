#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試策略交集分析頁籤跳轉修正
驗證"計算交集"和"分析所有組合"執行完後不會自動跳轉到"選股結果"頁籤
"""

import re

def test_calculate_intersection_no_tab_switch():
    """測試計算交集功能不會自動跳轉頁籤"""
    print("🔍 測試計算交集功能的頁籤跳轉行為")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 calculate_strategy_intersection 方法
        method_pattern = r'def calculate_strategy_intersection\(self\):(.*?)(?=def \w+\(self\)|class \w+|$)'
        match = re.search(method_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 找不到 calculate_strategy_intersection 方法")
            return False
        
        method_content = match.group(1)
        
        # 檢查是否包含頁籤跳轉代碼
        tab_switch_patterns = [
            r'self\.stock_tabs\.setCurrentIndex\(',
            r'self\.tab_widget\.setCurrentIndex\(',
            r'跳轉到.*選股結果',
            r'setCurrentIndex\(1\)',  # 左側活頁跳轉
            r'setCurrentIndex\(2\)'   # 右側活頁跳轉
        ]
        
        found_tab_switches = []
        for pattern in tab_switch_patterns:
            matches = re.findall(pattern, method_content)
            if matches:
                found_tab_switches.extend(matches)
        
        if found_tab_switches:
            print("❌ 發現頁籤跳轉代碼:")
            for switch in found_tab_switches:
                print(f"    {switch}")
            return False
        else:
            print("✅ 沒有發現頁籤跳轉代碼")
            
        # 檢查是否有修正註釋
        if "策略交集分析完成後不跳轉頁籤" in method_content:
            print("✅ 發現修正註釋，確認已修正")
            return True
        else:
            print("⚠️  沒有發現修正註釋")
            return True  # 只要沒有跳轉代碼就算通過
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_analyze_combinations_no_tab_switch():
    """測試分析所有組合功能不會自動跳轉頁籤"""
    print("\n🔍 測試分析所有組合功能的頁籤跳轉行為")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 analyze_all_strategy_combinations 方法
        method_pattern = r'def analyze_all_strategy_combinations\(self\):(.*?)(?=def \w+\(self\)|class \w+|$)'
        match = re.search(method_pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 找不到 analyze_all_strategy_combinations 方法")
            return False
        
        method_content = match.group(1)
        
        # 檢查是否包含頁籤跳轉代碼
        tab_switch_patterns = [
            r'self\.stock_tabs\.setCurrentIndex\(',
            r'self\.tab_widget\.setCurrentIndex\(',
            r'跳轉到.*選股結果',
            r'setCurrentIndex\(1\)',  # 左側活頁跳轉
            r'setCurrentIndex\(2\)'   # 右側活頁跳轉
        ]
        
        found_tab_switches = []
        for pattern in tab_switch_patterns:
            matches = re.findall(pattern, method_content)
            if matches:
                found_tab_switches.extend(matches)
        
        if found_tab_switches:
            print("❌ 發現頁籤跳轉代碼:")
            for switch in found_tab_switches:
                print(f"    {switch}")
            return False
        else:
            print("✅ 沒有發現頁籤跳轉代碼")
            return True
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_intersection_result_display():
    """測試交集結果顯示機制"""
    print("\n🔍 測試交集結果顯示機制")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否有結果顯示的文本框
        if 'intersection_result_text' in content:
            print("✅ 發現交集結果顯示文本框")
        else:
            print("❌ 沒有發現交集結果顯示文本框")
            return False
        
        # 檢查結果顯示邏輯
        display_patterns = [
            r'self\.intersection_result_text\.setPlainText',
            r'self\.intersection_result_text\.setText',
            r'intersection_result_text.*insert'
        ]
        
        found_display_logic = False
        for pattern in display_patterns:
            if re.search(pattern, content):
                found_display_logic = True
                print(f"✅ 發現結果顯示邏輯: {pattern}")
                break
        
        if not found_display_logic:
            print("❌ 沒有發現結果顯示邏輯")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_intersection_tab_structure():
    """測試策略交集頁籤結構"""
    print("\n🔍 測試策略交集頁籤結構")
    print("=" * 40)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查頁籤創建
        if '策略交集' in content and 'intersection_frame' in content:
            print("✅ 發現策略交集頁籤創建代碼")
        else:
            print("❌ 沒有發現策略交集頁籤創建代碼")
            return False
        
        # 檢查按鈕創建
        button_patterns = [
            r'計算交集',
            r'分析所有組合',
            r'導出結果'
        ]
        
        found_buttons = []
        for pattern in button_patterns:
            if pattern in content:
                found_buttons.append(pattern)
                print(f"✅ 發現按鈕: {pattern}")
        
        if len(found_buttons) >= 2:  # 至少要有計算交集和分析組合按鈕
            print(f"✅ 發現 {len(found_buttons)} 個按鈕，結構完整")
            return True
        else:
            print(f"❌ 只發現 {len(found_buttons)} 個按鈕，結構不完整")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def simulate_user_workflow():
    """模擬用戶使用流程"""
    print("\n🎯 模擬用戶使用流程")
    print("=" * 40)
    
    workflow_steps = [
        "1. 用戶切換到「🔗 策略交集」頁籤",
        "2. 用戶選擇要分析的策略（勾選複選框）",
        "3. 用戶點擊「🎯 計算交集」按鈕",
        "4. 系統在同一頁面顯示交集分析結果",
        "5. 用戶查看結果（不需要切換頁籤）",
        "",
        "或者：",
        "3. 用戶點擊「🔍 分析所有組合」按鈕",
        "4. 系統在同一頁面顯示所有組合分析結果",
        "5. 用戶查看結果（不需要切換頁籤）"
    ]
    
    print("📋 預期的用戶使用流程:")
    for step in workflow_steps:
        if step:
            print(f"  {step}")
        else:
            print("")
    
    print("\n✅ 修正後的行為:")
    print("  • 執行完「計算交集」後，結果直接顯示在策略交集頁籤中")
    print("  • 執行完「分析所有組合」後，結果直接顯示在策略交集頁籤中")
    print("  • 用戶不需要手動切換到「選股結果」頁籤")
    print("  • 提升用戶體驗，減少操作步驟")
    
    return True

def main():
    """主測試函數"""
    print("🚀 策略交集頁籤跳轉修正測試")
    print("=" * 50)
    
    # 執行所有測試
    tests = [
        ("計算交集功能", test_calculate_intersection_no_tab_switch),
        ("分析組合功能", test_analyze_combinations_no_tab_switch),
        ("結果顯示機制", test_intersection_result_display),
        ("頁籤結構", test_intersection_tab_structure),
        ("用戶流程", simulate_user_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 統計結果
    print("\n" + "=" * 50)
    print("📊 測試結果摘要:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("\n🎉 所有測試通過！策略交集頁籤跳轉問題已修正")
        print("\n💡 修正效果:")
        print("  ✅ 「計算交集」執行完後不會跳轉頁籤")
        print("  ✅ 「分析所有組合」執行完後不會跳轉頁籤")
        print("  ✅ 結果直接在策略交集頁籤中顯示")
        print("  ✅ 用戶體驗得到改善")
    else:
        print(f"\n⚠️  {total - passed} 個測試失敗，需要進一步檢查")
    
    return passed == total

if __name__ == "__main__":
    main()
