#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
即時股價監控系統 - 自選股專用版
基於Yahoo Finance免費爬取技術的全屏自選股監控視窗
移除了成交量排行、漲幅排行、跌幅排行三個象限，專注於自選股監控
"""

import sys
import os
import json
import logging
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QGridLayout, QTableWidget, QTableWidgetItem,
                            QLabel, QPushButton, QComboBox, QLineEdit, QTextEdit,
                            QSplitter, QFrame, QHeaderView, QMessageBox, QProgressBar,
                            QDialog, QTabWidget, QSpinBox, QButtonGroup, QRadioButton,
                            QCheckBox)
from PyQt6.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QColor, QPalette

# 導入核心爬蟲技術
from core_web_crawler import YahooFinanceCrawler

class StockDataWorker(QThread):
    """股票資料獲取工作執行緒 - 增強版"""
    data_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(str)

    def __init__(self, crawler, task_type, params=None):
        super().__init__()
        self.crawler = crawler
        self.task_type = task_type
        self.params = params or {}
        self.max_retries = 3
        self.retry_delay = 1  # 秒

    def run(self):
        """執行資料獲取任務"""
        try:
            self.progress_updated.emit(f"開始獲取 {self.task_type} 資料...")

            if self.task_type == "single_stock":
                self._handle_single_stock()

            elif self.task_type == "multiple_stocks":
                self._handle_multiple_stocks()

            elif self.task_type == "ranking":
                self._handle_ranking_data()

        except Exception as e:
            self.error_occurred.emit(f"資料獲取失敗: {str(e)}")

    def _handle_single_stock(self):
        """處理單一股票資料獲取"""
        stock_code = self.params.get('stock_code')

        for attempt in range(self.max_retries):
            try:
                self.progress_updated.emit(f"獲取 {stock_code} 資料 (嘗試 {attempt + 1}/{self.max_retries})")

                result = self.crawler.get_single_stock_price(stock_code)
                if result:
                    self.data_ready.emit({'type': 'single_stock', 'data': result})
                    return
                else:
                    if attempt < self.max_retries - 1:
                        self.msleep(self.retry_delay * 1000)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    self.progress_updated.emit(f"重試獲取 {stock_code} (錯誤: {str(e)})")
                    self.msleep(self.retry_delay * 1000)
                else:
                    raise e

        self.error_occurred.emit(f"無法獲取股票 {stock_code} 資料 (已重試 {self.max_retries} 次)")

    def _handle_multiple_stocks(self):
        """處理多支股票資料獲取"""
        stock_codes = self.params.get('stock_codes', [])

        if not stock_codes:
            self.error_occurred.emit("沒有指定股票代碼")
            return

        self.progress_updated.emit(f"獲取 {len(stock_codes)} 支股票資料...")

        for attempt in range(self.max_retries):
            try:
                results = self.crawler.get_multiple_stocks_price(stock_codes)
                if results:
                    self.data_ready.emit({'type': 'multiple_stocks', 'data': results})
                    return
                else:
                    if attempt < self.max_retries - 1:
                        self.msleep(self.retry_delay * 1000)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    self.progress_updated.emit(f"重試獲取多支股票資料 (錯誤: {str(e)})")
                    self.msleep(self.retry_delay * 1000)
                else:
                    raise e

        self.error_occurred.emit(f"無法獲取多支股票資料 (已重試 {self.max_retries} 次)")

    def _handle_ranking_data(self):
        """處理排行榜資料獲取 - 已停用，因為移除了四象限設計"""
        self.progress_updated.emit("排行榜資料獲取已停用")
        self.error_occurred.emit("排行榜功能已移除，請使用自選股監控")

    def _generate_mock_ranking_data(self, ranking_type, limit):
        """生成模擬排行榜數據 - 已停用，因為移除了四象限設計"""
        # 此方法已停用，因為不再需要排行榜數據
        logging.info(f"模擬排行榜數據生成已停用: {ranking_type}")
        return []

class StockTableWidget(QTableWidget):
    """自定義股票表格元件"""
    
    def __init__(self, headers, parent=None):
        super().__init__(parent)
        self.headers = headers
        self.setup_table()
        
    def setup_table(self):
        """設置表格"""
        self.setColumnCount(len(self.headers))
        self.setHorizontalHeaderLabels(self.headers)
        
        # 設置表格樣式
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # 設置欄位寬度
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        # 設置字體
        font = QFont()
        font.setPointSize(9)
        self.setFont(font)
        
    def update_data(self, data_list):
        """更新表格資料"""
        self.setRowCount(len(data_list))
        
        for row, data in enumerate(data_list):
            for col, header in enumerate(self.headers):
                value = self.get_cell_value(data, header)
                item = QTableWidgetItem(str(value))
                
                # 根據漲跌設置顏色 - 適合黑色背景，只有漲停跌停用全背景色
                if header in ['漲跌', '漲跌幅']:
                    if isinstance(data, dict):
                        # 根據漲跌金額或百分比判斷趨勢
                        change_amount = data.get('change_amount', 0)
                        change_percent = data.get('change_percent', 0)

                        # 判斷是否為漲停或跌停（漲跌幅接近10%或7%）
                        is_limit_up = change_percent >= 9.5  # 接近漲停
                        is_limit_down = change_percent <= -9.5  # 接近跌停

                        if is_limit_up:
                            # 漲停：全紅背景
                            item.setBackground(QColor(220, 53, 69))    # 深紅色背景
                            item.setForeground(QColor(255, 255, 255))  # 白色文字
                        elif is_limit_down:
                            # 跌停：全綠背景
                            item.setBackground(QColor(40, 167, 69))    # 深綠色背景
                            item.setForeground(QColor(255, 255, 255))  # 白色文字
                        elif change_amount > 0 or change_percent > 0:
                            # 一般上漲：只有紅色文字
                            item.setForeground(QColor(255, 80, 80))    # 亮紅色文字
                        elif change_amount < 0 or change_percent < 0:
                            # 一般下跌：只有綠色文字
                            item.setForeground(QColor(80, 255, 80))    # 亮綠色文字
                        else:
                            # 平盤：灰色文字
                            item.setForeground(QColor(200, 200, 200))  # 灰色文字

                        # 也可以根據 trend 欄位判斷（如果有的話）
                        trend = data.get('trend', 'flat')
                        if trend == 'up' and not is_limit_up:
                            item.setForeground(QColor(255, 80, 80))  # 亮紅色
                        elif trend == 'down' and not is_limit_down:
                            item.setForeground(QColor(80, 255, 80))  # 亮綠色
                
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.setItem(row, col, item)
    
    def get_cell_value(self, data, header):
        """根據表頭獲取對應的資料值"""
        if not isinstance(data, dict):
            return ""

        header_mapping = {
            '排名': data.get('rank', ''),
            '股票代碼': data.get('stock_code', ''),
            '股票名稱': data.get('stock_name', ''),
            '現價': f"{data.get('current_price', 0):.2f}",
            '漲跌': f"{data.get('change_amount', 0):+.2f}",
            '漲跌幅': f"{data.get('change_percent', 0):+.2f}%",
            '成交量': data.get('volume', data.get('trading_volume', '')),  # 支援兩種成交量欄位名稱
            '更新時間': data.get('timestamp', data.get('update_time', ''))  # 支援兩種時間欄位名稱
        }

        return header_mapping.get(header, "")

class RealTimeStockMonitor(QMainWindow):
    """即時股價監控主視窗"""
    
    def __init__(self):
        super().__init__()
        self.crawler = YahooFinanceCrawler()

        # 🎯 整合統一監控管理器
        try:
            from unified_monitor_manager import get_monitor_manager
            self.monitor_manager = get_monitor_manager()
            self.watchlist = self.monitor_manager.get_stocks()

            # 連接監控股票更新信號
            self.monitor_manager.stocks_updated.connect(self.on_watchlist_updated)
            logging.info(f"✅ 即時監控已整合統一管理器，載入 {len(self.watchlist)} 支股票")

        except Exception as e:
            logging.warning(f"⚠️ 統一監控管理器整合失敗，使用預設清單: {e}")
            self.watchlist = ['2330', '2317', '2454', '2412', '2881']  # 預設自選股
            self.monitor_manager = None

        self.workers = {}
        self.setup_ui()
        self.setup_timers()
        self.load_settings()

        # 🚀 啟動時自動開始監控
        QTimer.singleShot(1000, self.start_monitoring)  # 延遲1秒後自動開始監控
        
    def setup_ui(self):
        """設置使用者介面"""
        self.setWindowTitle("即時股價監控系統 - 自選股監控")
        self.setGeometry(100, 100, 1400, 900)

        # 設置整體黑色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
            }
        """)

        # 主要容器
        central_widget = QWidget()
        central_widget.setStyleSheet("background-color: #1e1e1e;")
        self.setCentralWidget(central_widget)

        # 主要佈局 - 優化間距以節省垂直空間
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)  # 減少邊距
        main_layout.setSpacing(3)  # 減少元件間距

        # 控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 只保留自選股監控，移除四象限設計
        # 創建自選股監控區域，佔據整個監控區域
        self.watchlist_widget = self.create_main_watchlist_widget()
        main_layout.addWidget(self.watchlist_widget)

        # 狀態列
        self.status_label = QLabel("系統就緒")
        self.status_label.setMaximumHeight(25)  # 限制狀態列高度
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #2d2d2d;
                color: #ffffff;
                padding: 4px 8px;
                border-top: 1px solid #404040;
            }
        """)
        main_layout.addWidget(self.status_label)
        
    def create_control_panel(self):
        """創建控制面板 - 黑色主題"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.Box)
        panel.setMaximumHeight(60)  # 減少高度從80到60
        panel.setStyleSheet("""
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
            }
            QLineEdit {
                background-color: #3d3d3d;
                border: 1px solid #555555;
                color: #ffffff;
                padding: 4px;
                border-radius: 3px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                border: none;
                color: #ffffff;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QComboBox {
                background-color: #3d3d3d;
                border: 1px solid #555555;
                color: #ffffff;
                padding: 4px;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #555555;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #ffffff;
            }
            QComboBox QAbstractItemView {
                background-color: #3d3d3d;
                color: #ffffff;
                selection-background-color: #0078d4;
            }
            QProgressBar {
                background-color: #3d3d3d;
                border: 1px solid #555555;
                border-radius: 3px;
                text-align: center;
                color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 2px;
            }
        """)

        layout = QHBoxLayout(panel)
        layout.setContentsMargins(8, 5, 8, 5)  # 減少邊距
        layout.setSpacing(8)  # 減少元件間距
        
        # 自選股管理
        layout.addWidget(QLabel("自選股:"))
        self.watchlist_input = QLineEdit()
        self.watchlist_input.setPlaceholderText("輸入股票代碼，用逗號分隔 (例: 2330,2317,2454)")
        self.watchlist_input.setText(','.join(self.watchlist))
        layout.addWidget(self.watchlist_input)
        
        # 更新按鈕
        self.update_btn = QPushButton("🔄 立即更新")
        self.update_btn.clicked.connect(self.manual_update)
        layout.addWidget(self.update_btn)
        
        # 自動更新設置
        layout.addWidget(QLabel("更新間隔:"))
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["30秒", "1分鐘", "2分鐘", "5分鐘"])
        self.interval_combo.setCurrentText("1分鐘")
        self.interval_combo.currentTextChanged.connect(self.update_timer_interval)
        layout.addWidget(self.interval_combo)
        
        # 開始/停止按鈕
        self.start_stop_btn = QPushButton("▶️ 開始監控")
        self.start_stop_btn.clicked.connect(self.toggle_monitoring)
        layout.addWidget(self.start_stop_btn)

        # 設定按鈕
        self.settings_btn = QPushButton("⚙️ 設定")
        self.settings_btn.clicked.connect(self.open_settings)
        layout.addWidget(self.settings_btn)

        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        layout.addWidget(self.progress_bar)

        return panel
        
    def create_main_watchlist_widget(self):
        """創建主要的自選股監控區域 - 黑色主題無標題版本"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.Box)
        widget.setStyleSheet("""
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #404040;
            }
        """)

        layout = QVBoxLayout(widget)
        # 移除標題以節省垂直空間，並優化邊距
        layout.setContentsMargins(5, 5, 5, 5)  # 減少邊距
        layout.setSpacing(0)  # 移除元件間距

        # 表格 - 使用更豐富的欄位
        headers = ['股票代碼', '股票名稱', '現價', '漲跌', '漲跌幅', '成交量', '更新時間']
        table = StockTableWidget(headers)

        # 設置表格樣式 - 黑色主題配色
        table.setStyleSheet("""
            QTableWidget {
                font-size: 12px;
                gridline-color: #404040;
                background-color: #1e1e1e;
                alternate-background-color: #2a2a2a;
                color: #ffffff;
                selection-background-color: #0078d4;
                selection-color: #ffffff;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #404040;
                color: #ffffff;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #2d2d2d;
                color: #ffffff;
                font-weight: bold;
                padding: 8px;
                border: 1px solid #404040;
                font-size: 12px;
                height: 30px;
            }
            QHeaderView::section:hover {
                background-color: #404040;
            }
        """)

        layout.addWidget(table)

        # 儲存表格引用
        setattr(widget, 'table', table)
        setattr(widget, 'title', "自選股監控")  # 保留引用但不顯示

        return widget

    def create_quadrant_widget(self, title, headers):
        """創建象限元件 - 保留此方法以維持兼容性，但不再使用"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.Box)

        layout = QVBoxLayout(widget)

        # 標題
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(title_label)

        # 表格
        table = StockTableWidget(headers)
        layout.addWidget(table)

        # 儲存表格引用
        setattr(widget, 'table', table)
        setattr(widget, 'title', title)

        return widget
        
    def setup_timers(self):
        """設置定時器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.auto_update)
        self.monitoring = False
        
    def update_timer_interval(self):
        """更新定時器間隔"""
        if self.monitoring:
            interval_text = self.interval_combo.currentText()
            interval_map = {"30秒": 30000, "1分鐘": 60000, "2分鐘": 120000, "5分鐘": 300000}
            interval = interval_map.get(interval_text, 60000)
            self.update_timer.setInterval(interval)
            
    def toggle_monitoring(self):
        """切換監控狀態"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()
            
    def start_monitoring(self):
        """開始監控"""
        self.monitoring = True
        self.start_stop_btn.setText("⏸️ 停止監控")
        self.update_timer_interval()
        self.update_timer.start()
        self.status_label.setText("🟢 監控中...")
        self.manual_update()  # 立即更新一次
        
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        self.start_stop_btn.setText("▶️ 開始監控")
        self.update_timer.stop()
        self.status_label.setText("🔴 監控已停止")
        
    def manual_update(self):
        """手動更新"""
        self.update_watchlist()
        self.fetch_all_data()
        
    def auto_update(self):
        """自動更新"""
        self.fetch_all_data()
        
    def update_watchlist(self):
        """更新自選股清單"""
        watchlist_text = self.watchlist_input.text().strip()
        if watchlist_text:
            self.watchlist = [code.strip() for code in watchlist_text.split(',') if code.strip()]
            
    def fetch_all_data(self):
        """獲取所有資料 - 只獲取自選股資料"""
        self.status_label.setText("📡 正在更新自選股資料...")

        # 只獲取自選股資料，移除排行榜資料獲取
        self.fetch_watchlist_data()
        
    def fetch_ranking_data(self, ranking_type, widget):
        """獲取排行榜資料 - 已停用，保留方法以維持兼容性"""
        # 此方法已停用，因為移除了四象限設計
        # 保留此方法以避免其他代碼調用時出錯
        logging.info(f"排行榜資料獲取已停用: {ranking_type}")
        return

    def fetch_watchlist_data(self):
        """獲取自選股資料 - 增強版"""
        if not self.watchlist:
            self.status_label.setText("⚠️ 沒有設定自選股")
            return

        worker = StockDataWorker(self.crawler, "multiple_stocks", {
            'stock_codes': self.watchlist
        })
        worker.data_ready.connect(self.update_watchlist_widget)
        worker.error_occurred.connect(self.handle_error)
        worker.progress_updated.connect(self.handle_progress)

        self.workers['watchlist'] = worker
        worker.start()

    def handle_progress(self, message):
        """處理進度更新"""
        self.status_label.setText(f"📡 {message}")

        # 顯示進度條
        if not self.progress_bar.isVisible():
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不確定進度

    def handle_error(self, error_message):
        """處理錯誤 - 增強版"""
        self.status_label.setText(f"❌ {error_message}")

        # 隱藏進度條
        if self.progress_bar.isVisible():
            self.progress_bar.setVisible(False)

        # 記錄錯誤
        import logging
        logging.error(f"即時監控錯誤: {error_message}")

        # 如果是網路錯誤，可以考慮自動重試
        if "網路" in error_message or "連線" in error_message:
            self.status_label.setText(f"🔄 網路錯誤，將在下次更新時重試")

    def handle_data_ready(self):
        """處理數據就緒"""
        # 隱藏進度條
        if self.progress_bar.isVisible():
            self.progress_bar.setVisible(False)

        # 更新狀態
        current_time = datetime.now().strftime('%H:%M:%S')
        self.status_label.setText(f"✅ 更新完成 - {current_time}")
        
    def update_ranking_widget(self, data, widget):
        """更新排行榜元件 - 已停用，保留方法以維持兼容性"""
        # 此方法已停用，因為移除了四象限設計
        logging.info("排行榜元件更新已停用")
        return

    def update_watchlist_widget(self, data):
        """更新自選股元件"""
        if data['type'] == 'multiple_stocks':
            self.watchlist_widget.table.update_data(data['data'])
            self.handle_data_ready()

    def open_settings(self):
        """開啟設定對話框"""
        dialog = SettingsDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 應用新設定
            self.apply_settings(dialog.get_settings())

    def apply_settings(self, settings):
        """應用設定"""
        try:
            # 更新自選股
            if 'watchlist' in settings:
                self.watchlist = settings['watchlist']
                self.watchlist_input.setText(','.join(self.watchlist))

            # 更新更新間隔
            if 'update_interval' in settings:
                self.interval_combo.setCurrentText(settings['update_interval'])
                self.update_timer_interval()

            # 保存設定到文件
            self.save_settings(settings)

            self.status_label.setText("✅ 設定已更新")

        except Exception as e:
            self.status_label.setText(f"❌ 設定更新失敗: {str(e)}")

    def save_settings(self, settings):
        """保存設定到文件"""
        try:
            import json
            import os

            settings_file = 'realtime_monitor_settings.json'

            # 添加時間戳
            settings['last_updated'] = datetime.now().isoformat()

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logging.error(f"保存設定失敗: {e}")

    def load_settings(self):
        """載入設定"""
        try:
            import json
            import os

            settings_file = 'realtime_monitor_settings.json'

            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 應用載入的設定
                if 'watchlist' in settings and settings['watchlist']:
                    self.watchlist = settings['watchlist']
                    self.watchlist_input.setText(','.join(self.watchlist))

                if 'update_interval' in settings:
                    self.interval_combo.setCurrentText(settings['update_interval'])

                return settings

        except Exception as e:
            logging.error(f"載入設定失敗: {e}")

        return {}

    def closeEvent(self, event):
        """關閉事件處理"""
        try:
            # 停止監控
            if hasattr(self, 'monitoring') and self.monitoring:
                self.stop_monitoring()

            # 停止所有工作線程
            for worker in self.workers.values():
                if worker.isRunning():
                    worker.terminate()
                    worker.wait(1000)  # 等待1秒

            # 保存當前設定
            current_settings = {
                'watchlist': self.watchlist,
                'update_interval': self.interval_combo.currentText() if hasattr(self, 'interval_combo') else '1分鐘'
            }
            self.save_settings(current_settings)

            logging.info("即時股價監控系統已關閉")

        except Exception as e:
            logging.error(f"關閉監控系統時發生錯誤: {e}")

        event.accept()


class SettingsDialog(QDialog):
    """設定對話框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📊 即時監控設定")
        self.setModal(True)
        self.setFixedSize(600, 500)

        # 從父窗口獲取當前設定
        self.parent_monitor = parent
        self.init_ui()
        self.load_current_settings()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 標題
        title_label = QLabel("⚙️ 即時股價監控系統設定")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # 分頁標籤
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 自選股設定頁面
        watchlist_tab = self.create_watchlist_tab()
        tab_widget.addTab(watchlist_tab, "📈 自選股管理")

        # 更新設定頁面
        update_tab = self.create_update_tab()
        tab_widget.addTab(update_tab, "🔄 更新設定")

        # 顯示設定頁面
        display_tab = self.create_display_tab()
        tab_widget.addTab(display_tab, "🖥️ 顯示設定")

        # 按鈕
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("✅ 確定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def create_watchlist_tab(self):
        """創建自選股設定頁面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 說明
        info_label = QLabel(
            "📋 自選股管理\n\n"
            "在此設定您要監控的股票代碼。支援台股代碼（如：2330、2317）\n"
            "和ETF代碼（如：0050、0056）。\n\n"
            "✨ 新版本專注於自選股監控，提供更清晰的全屏監控體驗。"
        )
        info_label.setStyleSheet("background-color: #f0f8ff; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)

        # 當前自選股
        layout.addWidget(QLabel("📊 當前自選股:"))
        self.current_watchlist = QTextEdit()
        self.current_watchlist.setMaximumHeight(100)
        self.current_watchlist.setPlaceholderText("目前沒有設定自選股")
        layout.addWidget(self.current_watchlist)

        # 新增股票
        layout.addWidget(QLabel("➕ 新增股票:"))
        add_layout = QHBoxLayout()

        self.add_stock_input = QLineEdit()
        self.add_stock_input.setPlaceholderText("輸入股票代碼（例：2330）")
        add_layout.addWidget(self.add_stock_input)

        self.add_btn = QPushButton("新增")
        self.add_btn.clicked.connect(self.add_stock)
        add_layout.addWidget(self.add_btn)

        layout.addLayout(add_layout)

        # 常用股票快速新增
        layout.addWidget(QLabel("🔥 常用股票快速新增:"))
        popular_layout = QGridLayout()

        popular_stocks = [
            ("2330", "台積電"), ("2317", "鴻海"), ("2454", "聯發科"),
            ("2412", "中華電"), ("2882", "國泰金"), ("2308", "台達電"),
            ("0050", "元大台灣50"), ("0056", "元大高股息"), ("006208", "富邦台50")
        ]

        for i, (code, name) in enumerate(popular_stocks):
            btn = QPushButton(f"{code}\n{name}")
            btn.clicked.connect(lambda checked, c=code: self.add_popular_stock(c))
            btn.setMaximumHeight(50)
            popular_layout.addWidget(btn, i // 3, i % 3)

        layout.addLayout(popular_layout)

        # 清除按鈕
        self.clear_btn = QPushButton("🗑️ 清除所有自選股")
        self.clear_btn.clicked.connect(self.clear_watchlist)
        self.clear_btn.setStyleSheet("background-color: #ffebee;")
        layout.addWidget(self.clear_btn)

        return widget

    def create_update_tab(self):
        """創建更新設定頁面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 說明
        info_label = QLabel(
            "🔄 更新設定\n\n"
            "設定資料更新頻率和相關參數。頻率越高，資料越即時，但也會增加系統負載。"
        )
        info_label.setStyleSheet("background-color: #f0fff0; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)

        # 更新間隔
        layout.addWidget(QLabel("⏰ 自動更新間隔:"))
        self.update_interval_combo = QComboBox()
        self.update_interval_combo.addItems(["30秒", "1分鐘", "2分鐘", "5分鐘", "10分鐘"])
        layout.addWidget(self.update_interval_combo)

        # 重試設定
        layout.addWidget(QLabel("🔄 錯誤重試次數:"))
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(1, 10)
        self.retry_spin.setValue(3)
        layout.addWidget(self.retry_spin)

        # 超時設定
        layout.addWidget(QLabel("⏱️ 請求超時時間 (秒):"))
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 60)
        self.timeout_spin.setValue(15)
        layout.addWidget(self.timeout_spin)

        # 數據源選擇
        layout.addWidget(QLabel("🌐 數據源設定:"))
        self.data_source_group = QButtonGroup()

        self.yahoo_radio = QRadioButton("Yahoo Finance (推薦)")
        self.yahoo_radio.setChecked(True)
        self.data_source_group.addButton(self.yahoo_radio)
        layout.addWidget(self.yahoo_radio)

        self.selenium_radio = QRadioButton("強制使用 Selenium (較慢但更穩定)")
        self.data_source_group.addButton(self.selenium_radio)
        layout.addWidget(self.selenium_radio)

        layout.addStretch()
        return widget

    def create_display_tab(self):
        """創建顯示設定頁面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 說明
        info_label = QLabel(
            "🖥️ 顯示設定\n\n"
            "自訂自選股監控界面的顯示方式和外觀。\n"
            "新版本專注於自選股監控，提供更清晰的監控體驗。"
        )
        info_label.setStyleSheet("background-color: #fff8f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)

        # 顯示行數
        layout.addWidget(QLabel("📊 自選股顯示行數:"))
        self.display_rows_spin = QSpinBox()
        self.display_rows_spin.setRange(5, 100)  # 增加上限，因為只有自選股
        self.display_rows_spin.setValue(50)  # 預設顯示更多行
        layout.addWidget(self.display_rows_spin)

        # 字體大小
        layout.addWidget(QLabel("🔤 字體大小:"))
        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["小", "中", "大"])
        self.font_size_combo.setCurrentText("中")
        layout.addWidget(self.font_size_combo)

        # 顏色主題
        layout.addWidget(QLabel("🎨 顏色主題:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["預設", "深色", "護眼綠"])
        layout.addWidget(self.theme_combo)

        # 聲音提醒
        self.sound_checkbox = QCheckBox("🔔 啟用聲音提醒")
        layout.addWidget(self.sound_checkbox)

        # 桌面通知
        self.notification_checkbox = QCheckBox("📢 啟用桌面通知")
        layout.addWidget(self.notification_checkbox)

        layout.addStretch()
        return widget

    def load_current_settings(self):
        """載入當前設定"""
        if self.parent_monitor:
            # 載入自選股
            watchlist_text = '\n'.join(self.parent_monitor.watchlist)
            self.current_watchlist.setPlainText(watchlist_text)

            # 載入更新間隔
            current_interval = self.parent_monitor.interval_combo.currentText()
            self.update_interval_combo.setCurrentText(current_interval)

    def add_stock(self):
        """新增股票到自選股"""
        stock_code = self.add_stock_input.text().strip().upper()
        if stock_code:
            current_text = self.current_watchlist.toPlainText()
            current_stocks = [s.strip() for s in current_text.split('\n') if s.strip()]

            if stock_code not in current_stocks:
                current_stocks.append(stock_code)
                self.current_watchlist.setPlainText('\n'.join(current_stocks))
                self.add_stock_input.clear()
            else:
                QMessageBox.information(self, "提示", f"股票 {stock_code} 已在自選股中")

    def add_popular_stock(self, stock_code):
        """新增常用股票"""
        current_text = self.current_watchlist.toPlainText()
        current_stocks = [s.strip() for s in current_text.split('\n') if s.strip()]

        if stock_code not in current_stocks:
            current_stocks.append(stock_code)
            self.current_watchlist.setPlainText('\n'.join(current_stocks))
        else:
            QMessageBox.information(self, "提示", f"股票 {stock_code} 已在自選股中")

    def clear_watchlist(self):
        """清除自選股"""
        reply = QMessageBox.question(
            self,
            "確認",
            "確定要清除所有自選股嗎？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.current_watchlist.clear()

    def get_settings(self):
        """獲取設定"""
        watchlist_text = self.current_watchlist.toPlainText()
        watchlist = [s.strip() for s in watchlist_text.split('\n') if s.strip()]

        settings = {
            'watchlist': watchlist,
            'update_interval': self.update_interval_combo.currentText(),
            'retry_count': self.retry_spin.value(),
            'timeout': self.timeout_spin.value(),
            'force_selenium': self.selenium_radio.isChecked(),
            'display_rows': self.display_rows_spin.value(),
            'font_size': self.font_size_combo.currentText(),
            'theme': self.theme_combo.currentText(),
            'sound_enabled': self.sound_checkbox.isChecked(),
            'notification_enabled': self.notification_checkbox.isChecked()
        }

        return settings

    def on_watchlist_updated(self, new_stocks):
        """當統一監控管理器的股票清單更新時的回調"""
        try:
            logging.info(f"📡 即時監控收到股票更新信號: {len(new_stocks)} 支股票")

            # 更新自選股清單
            self.watchlist = new_stocks.copy()

            # 重新啟動監控
            self.stop_all_monitoring()
            self.start_monitoring()

            # 更新狀態顯示
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"✅ 自選股已更新: {len(self.watchlist)} 支")

            logging.info(f"✅ 即時監控自選股已同步更新: {', '.join(self.watchlist)}")

        except Exception as e:
            logging.error(f"❌ 即時監控股票更新失敗: {e}")

    def stop_all_monitoring(self):
        """停止所有監控執行緒"""
        try:
            for worker_name, worker in self.workers.items():
                if worker and worker.isRunning():
                    worker.terminate()
                    worker.wait()
            self.workers.clear()
            logging.info("🛑 所有監控執行緒已停止")
        except Exception as e:
            logging.error(f"❌ 停止監控執行緒失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式樣式
    app.setStyle('Fusion')
    
    # 創建主視窗
    window = RealTimeStockMonitor()
    window.show()
    window.showMaximized()  # 視窗最大化
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
