#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專用啟動器 - 台股智能選股系統.exe
"""

import sys
import os
import subprocess
import time

# 模組修復
try:
    import inspect
except ImportError:
    class MockInspect:
        @staticmethod
        def signature(func):
            class MockSignature:
                def __init__(self):
                    self.parameters = {}
            return MockSignature()
    sys.modules['inspect'] = MockInspect()

try:
    import pydoc
except ImportError:
    class MockPydoc:
        @staticmethod
        def help(obj):
            return f"Help for {obj}"
    sys.modules['pydoc'] = MockPydoc()

def main():
    print("🚀 啟動 台股智能選股系統.exe")
    print("🔧 模組修復已應用")
    
    exe_path = "dist/台股智能選股系統.exe"
    if os.path.exists(exe_path):
        try:
            subprocess.Popen([exe_path])
            print("✅ 啟動成功！")
        except Exception as e:
            print(f"❌ 啟動失敗: {e}")
    else:
        print(f"❌ 找不到: {exe_path}")

if __name__ == "__main__":
    main()
