#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新接下來5天資料（從 2022-09-15 開始）
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def save_price_to_newprice_database(df, db_file):
    """將股價資料增量保存到 newprice.db (標準格式)"""
    conn = None
    try:
        if df.empty:
            print("⚠️ 資料為空，跳過保存")
            return True

        # 確保目錄存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 重置索引以便保存到資料庫
        df_to_save = df.reset_index()

        # 檢查資料結構
        print(f"📊 準備保存資料: {len(df_to_save)} 筆")
        print(f"   欄位: {list(df_to_save.columns)}")

        # 連接到資料庫
        conn = sqlite3.connect(db_file, timeout=30.0)
        cursor = conn.cursor()

        # 檢查表格是否存在，如果不存在則創建
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                stock_name TEXT,
                listing_status TEXT,
                industry TEXT,
                Volume INTEGER,
                [Transaction] INTEGER,
                TradeValue INTEGER,
                [Open] REAL,
                High REAL,
                Low REAL,
                [Close] REAL,
                [Change] REAL,
                date TEXT
            )
        """)

        # 建立索引以提高查詢效率
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_stock_date 
            ON stock_daily_data (stock_id, date)
        """)

        # 欄位映射 - 將中文欄位名映射到英文
        column_mapping = {
            '成交股數': 'Volume',
            '成交筆數': 'Transaction', 
            '成交金額': 'TradeValue',
            '開盤價': 'Open',
            '最高價': 'High',
            '最低價': 'Low',
            '收盤價': 'Close',
            '漲跌價差': 'Change'
        }

        # 重新命名欄位
        df_renamed = df_to_save.copy()
        for old_name, new_name in column_mapping.items():
            if old_name in df_renamed.columns:
                df_renamed = df_renamed.rename(columns={old_name: new_name})

        # 確保必要欄位存在
        required_columns = ['stock_id', 'date']
        missing_columns = [col for col in required_columns if col not in df_renamed.columns]
        if missing_columns:
            print(f"❌ 缺少必要欄位: {missing_columns}")
            return False

        # 🔧 修正日期格式問題 - 將 Timestamp 轉換為字串
        if 'date' in df_renamed.columns:
            df_renamed['date'] = df_renamed['date'].astype(str)
            # 如果日期格式是 YYYY-MM-DD HH:MM:SS，只取日期部分
            df_renamed['date'] = df_renamed['date'].str.split(' ').str[0]
            print(f"   🔧 已修正日期格式，範例: {df_renamed['date'].iloc[0] if not df_renamed.empty else 'N/A'}")

        # 批量插入資料
        saved_count = 0
        for _, row in df_renamed.iterrows():
            try:
                # 檢查是否已存在相同的記錄
                cursor.execute("""
                    SELECT COUNT(*) FROM stock_daily_data 
                    WHERE stock_id = ? AND date = ?
                """, (row['stock_id'], row['date']))
                
                exists = cursor.fetchone()[0] > 0
                
                if exists:
                    # 更新現有記錄
                    cursor.execute("""
                        UPDATE stock_daily_data SET
                            stock_name = ?,
                            listing_status = ?,
                            industry = ?,
                            Volume = ?,
                            [Transaction] = ?,
                            TradeValue = ?,
                            [Open] = ?,
                            High = ?,
                            Low = ?,
                            [Close] = ?,
                            [Change] = ?
                        WHERE stock_id = ? AND date = ?
                    """, (
                        row.get('stock_name', ''),
                        row.get('listing_status', ''),
                        row.get('industry', ''),
                        row.get('Volume', None),
                        row.get('Transaction', None),
                        row.get('TradeValue', None),
                        row.get('Open', None),
                        row.get('High', None),
                        row.get('Low', None),
                        row.get('Close', None),
                        row.get('Change', None),
                        row['stock_id'],
                        row['date']
                    ))
                else:
                    # 插入新記錄
                    cursor.execute("""
                        INSERT INTO stock_daily_data 
                        (stock_id, stock_name, listing_status, industry, Volume, [Transaction], 
                         TradeValue, [Open], High, Low, [Close], [Change], date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row['stock_id'],
                        row.get('stock_name', ''),
                        row.get('listing_status', ''),
                        row.get('industry', ''),
                        row.get('Volume', None),
                        row.get('Transaction', None),
                        row.get('TradeValue', None),
                        row.get('Open', None),
                        row.get('High', None),
                        row.get('Low', None),
                        row.get('Close', None),
                        row.get('Change', None),
                        row['date']
                    ))
                
                saved_count += 1
                
                if saved_count % 500 == 0:
                    print(f"   已處理 {saved_count} 筆資料...")
                    
            except Exception as e:
                print(f"⚠️ 保存單筆資料失敗: {e}")

        conn.commit()
        print(f"✅ 成功保存 {saved_count} 筆資料到 newprice.db")
        return True

    except Exception as e:
        print(f"❌ 保存股價資料到 newprice.db 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            try:
                conn.close()
            except:
                pass

def update_next_5_days():
    """更新接下來5天資料"""
    
    print("=" * 60)
    print("📈 更新接下來5天資料（從 2022-09-15 開始）")
    print("=" * 60)
    
    try:
        from crawler import crawl_price
        
        # 從 2022-09-15 開始更新5個工作日
        start_date = datetime(2022, 9, 15)
        dates_to_update = []
        
        current_date = start_date
        for i in range(10):  # 檢查10天，找出5個工作日
            if current_date.weekday() < 5:  # 週一到週五
                dates_to_update.append(current_date)
                if len(dates_to_update) >= 5:
                    break
            current_date += timedelta(days=1)
        
        print(f"📅 準備更新的日期:")
        for i, date in enumerate(dates_to_update, 1):
            print(f"   {i}. {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
        
        db_file = 'D:/Finlab/history/tables/newprice.db'
        total_saved = 0
        
        # 逐日更新
        for i, date in enumerate(dates_to_update, 1):
            print(f"\n🔄 更新第 {i} 天: {date.strftime('%Y-%m-%d')}")
            
            try:
                df = crawl_price(date)
                
                if df is not None and not df.empty:
                    print(f"   ✅ 成功爬取 {len(df)} 筆資料")
                    
                    # 保存到資料庫
                    success = save_price_to_newprice_database(df, db_file)
                    
                    if success:
                        total_saved += len(df)
                        print(f"   💾 成功保存到資料庫")
                    else:
                        print(f"   ❌ 保存到資料庫失敗")
                        
                else:
                    print(f"   ❌ 爬取失敗或無資料")
                    
            except Exception as e:
                print(f"   ❌ 處理失敗: {e}")
        
        print(f"\n📊 更新完成統計:")
        print(f"   總共保存: {total_saved} 筆資料")
        
        # 驗證更新結果
        print(f"\n🔍 驗證更新結果:")
        conn = sqlite3.connect(db_file)
        
        # 檢查最新日期
        query_latest = '''
            SELECT MAX(date) as latest_date, COUNT(*) as total_records
            FROM stock_daily_data
        '''
        
        result = pd.read_sql_query(query_latest, conn)
        latest_date = result.iloc[0]['latest_date']
        total_records = result.iloc[0]['total_records']
        
        print(f"   📅 資料庫最新日期: {latest_date}")
        print(f"   📊 總記錄數: {total_records}")
        
        # 檢查新增的日期
        target_dates = [date.strftime('%Y-%m-%d') for date in dates_to_update]
        
        for target_date in target_dates:
            query_check = '''
                SELECT COUNT(*) as count
                FROM stock_daily_data
                WHERE date = ?
            '''
            result = pd.read_sql_query(query_check, conn, params=[target_date])
            count = result.iloc[0]['count']
            status = "✅ 存在" if count > 0 else "❌ 不存在"
            print(f"   {target_date}: {status} ({count} 筆)")
        
        # 檢查 0050 最新資料
        query_0050 = '''
            SELECT date, [Close], Volume, stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 10
        '''
        
        df_0050 = pd.read_sql_query(query_0050, conn)
        
        if not df_0050.empty:
            print(f"\n📈 0050 最新10筆資料:")
            for _, row in df_0050.iterrows():
                close_val = row['Close'] if pd.notna(row['Close']) else 'NULL'
                volume_val = row['Volume'] if pd.notna(row['Volume']) else 'NULL'
                print(f"   {row['date']}: 收盤={close_val}, 成交量={volume_val}")
        
        conn.close()
        
        print(f"\n✅ 接下來5天資料更新完成！")
        print(f"\n📋 資料庫檔案位置: {db_file}")
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_next_5_days()
