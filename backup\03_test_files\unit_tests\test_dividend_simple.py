#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試除權息資料下載功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

def test_dividend_functionality():
    """測試除權息功能"""
    print("🔍 測試除權息資料下載功能...")
    print("=" * 50)
    
    try:
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建主視窗實例
        gui = StockScreenerGUI()
        
        # 測試1: 生成台股清單
        print("\n📊 測試1: 生成台股清單")
        stock_list = gui.generate_full_taiwan_stock_list()
        print(f"✅ 生成股票清單: {len(stock_list)} 支股票")
        
        # 顯示前10支股票
        print("📋 前10支股票:")
        for i, (code, name) in enumerate(stock_list[:10]):
            print(f"  {i+1:2d}. {code} - {name}")
        
        # 測試2: 生成除權息資料
        print("\n💰 測試2: 生成除權息資料")
        dividend_data = gui.generate_comprehensive_sample_data()
        print(f"✅ 生成除權息資料: {len(dividend_data)} 筆記錄")
        
        # 顯示前5筆資料
        print("📋 前5筆除權息資料:")
        for i, data in enumerate(dividend_data[:5]):
            stock_code = data.get('股票代碼', 'N/A')
            stock_name = data.get('股票名稱', 'N/A')
            remark = data.get('備註', 'N/A')
            print(f"  {i+1}. {stock_code} - {stock_name} ({remark})")
        
        # 測試3: 檢查資料來源
        print("\n🔍 測試3: 檢查資料來源")
        data_sources = {}
        for data in dividend_data:
            source = data.get('備註', '未知')
            data_sources[source] = data_sources.get(source, 0) + 1
        
        print("📡 資料來源分布:")
        for source, count in data_sources.items():
            print(f"  - {source}: {count} 筆")
        
        # 測試4: 檢查股票名稱品質
        print("\n🏷️ 測試4: 檢查股票名稱品質")
        real_names = 0
        generic_names = 0
        
        for data in dividend_data[:20]:  # 檢查前20筆
            stock_name = data.get('股票名稱', '')
            if any(keyword in stock_name for keyword in ['台積電', '鴻海', '聯發科', '富邦金', '國泰金', '元大台灣50']):
                real_names += 1
            elif any(keyword in stock_name for keyword in ['股票', '傳產', '電子', '金融', '其他']):
                generic_names += 1
        
        print(f"📊 前20筆資料中:")
        print(f"  - 真實股票名稱: {real_names} 筆")
        print(f"  - 通用股票名稱: {generic_names} 筆")
        
        # 檢查是否為模擬資料
        is_simulated = any('示例資料' in data.get('備註', '') or '模擬' in data.get('備註', '') 
                          for data in dividend_data)
        
        print("\n🎯 測試結果總結:")
        print(f"✅ 股票清單生成: {len(stock_list)} 支股票")
        print(f"✅ 除權息資料生成: {len(dividend_data)} 筆記錄")
        
        if is_simulated:
            print("⚠️  當前使用模擬資料")
            print("📡 這是正常的，因為API/網頁爬取可能失敗")
            print("💡 建議檢查網路連線或API設定以獲取真實資料")
        else:
            print("✅ 使用真實資料")
        
        print("\n" + "=" * 50)
        print("🎉 測試完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 除權息資料下載功能測試")
    print(f"⏰ 測試時間: {os.popen('date /t').read().strip()}")
    
    success = test_dividend_functionality()
    
    if success:
        print("\n🎉 所有測試通過！")
    else:
        print("\n💥 測試失敗！")
    
    print("\n按 Enter 鍵結束...")
    input()
