#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試投資建議區域和文字選取功能修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dialog_creation():
    """測試對話框創建和文字選取功能"""
    print("🧪 測試投資建議和文字選取修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試投資建議和文字選取")
        dialog.setFixedSize(600, 500)
        
        layout = QVBoxLayout(dialog)
        
        # 測試投資建議區塊
        advice_group = QGroupBox("📝 投資建議")
        advice_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        advice_layout = QVBoxLayout(advice_group)

        advice_text = """
        <div style="font-family: 'Microsoft JhengHei'; color: #333333; background-color: #ffffff;">
            <p style="margin: 8px 0; font-size: 12px; color: #2c3e50;">
                🔍 <strong style="color: #e74c3c;">建議關注重點：</strong><br>
                <span style="color: #34495e;">• 營收成長趨勢是否持續</span><br>
                <span style="color: #34495e;">• 財務指標是否合理</span><br>
                <span style="color: #34495e;">• 行業景氣度和競爭態勢</span><br>
                <span style="color: #34495e;">• 公司基本面和未來展望</span>
            </p>
            <p style="margin: 10px 0; font-size: 11px; color: #7f8c8d; background-color: #ecf0f1; padding: 8px; border-radius: 4px;">
                ⚠️ <strong style="color: #e67e22;">風險提醒：</strong>
                <span style="color: #2c3e50;">本評估僅供參考，投資前請詳細研究公司基本面，並考慮個人風險承受能力。</span>
            </p>
        </div>
        """

        advice_label = QLabel()
        advice_label.setTextFormat(Qt.TextFormat.RichText)
        advice_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        advice_label.setWordWrap(True)
        advice_label.setText(advice_text)
        advice_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        advice_layout.addWidget(advice_label)
        layout.addWidget(advice_group)
        
        # 測試財務指標區塊
        financial_group = QGroupBox("💎 財務指標")
        financial_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        financial_layout = QVBoxLayout(financial_group)

        financial_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 8px;">
            <table style="width: 100%; font-family: 'Microsoft JhengHei'; color: #333333;">
                <tr><td style="padding: 6px; font-weight: bold; width: 50%; color: #2c3e50;">殖利率：</td>
                    <td style="padding: 6px; color: #27ae60; font-weight: bold;">2.69%</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">本益比：</td>
                    <td style="padding: 6px; color: #3498db; font-weight: bold;">1.46</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">股價淨值比：</td>
                    <td style="padding: 6px; color: #9b59b6; font-weight: bold;">1.04</td></tr>
                <tr><td style="padding: 6px; font-weight: bold; color: #2c3e50;">每股盈餘 (EPS)：</td>
                    <td style="padding: 6px; color: #e74c3c; font-weight: bold;">8.90 元</td></tr>
            </table>
            <div style="margin: 12px 0; padding: 8px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #3498db;">
                <p style="margin: 2px 0; font-size: 10px; color: #555;">
                    💡 <strong style="color: #2c3e50;">殖利率：</strong><span style="color: #666;">股息收益率，數值越高表示股息回報越好</span><br>
                    💡 <strong style="color: #2c3e50;">本益比：</strong><span style="color: #666;">股價相對盈餘的倍數，可用於評估估值水準</span><br>
                    💡 <strong style="color: #2c3e50;">EPS：</strong><span style="color: #666;">每股盈餘，反映公司獲利能力</span>
                </p>
            </div>
        </div>
        """

        financial_label = QLabel()
        financial_label.setTextFormat(Qt.TextFormat.RichText)
        financial_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        financial_label.setWordWrap(True)
        financial_label.setText(financial_text)
        financial_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px;
                color: #333333;
            }
        """)
        financial_layout.addWidget(financial_label)
        layout.addWidget(financial_group)
        
        # 添加說明文字
        instruction_label = QLabel()
        instruction_label.setText("""
        <div style="padding: 10px; background-color: #e8f4fd; border-radius: 4px; border: 1px solid #bee5eb;">
            <p style="margin: 0; font-size: 11px; color: #0c5460;">
                <strong>📋 測試說明：</strong><br>
                1. 投資建議區域現在應該有內容顯示<br>
                2. 所有文字都可以用滑鼠選取和複製<br>
                3. 文字顏色對比度已優化，避免白底白字問題<br>
                4. 請嘗試選取上方的文字內容
            </p>
        </div>
        """)
        instruction_label.setTextFormat(Qt.TextFormat.RichText)
        instruction_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)
        
        print("✅ 對話框創建成功")
        print("📋 測試項目：")
        print("  1. 投資建議區域是否有內容")
        print("  2. 文字是否可以選取和複製")
        print("  3. 文字顏色是否清晰可見")
        print("  4. 區塊樣式是否正確顯示")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 投資建議和文字選取修復測試")
    print("=" * 50)
    
    result = test_dialog_creation()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 如果看到對話框正常顯示，表示修復成功")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
