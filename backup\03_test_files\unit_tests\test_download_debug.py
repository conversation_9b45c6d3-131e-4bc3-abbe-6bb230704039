#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下載問題調試
"""

import os
import sys
import logging
import traceback

def test_download_debug():
    """調試下載問題"""
    try:
        print("🧪 開始下載調試...")
        
        # 導入下載器
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器實例創建成功")
        
        # 測試股票
        test_stock = "2330"
        print(f"📊 測試股票: {test_stock}")
        
        # 執行下載
        print("🚀 開始下載...")
        result = downloader.download_stock_revenue(test_stock)
        
        print(f"📄 下載結果: {result}")
        print(f"📄 結果類型: {type(result)}")
        
        if result:
            print("✅ 下載返回了結果")
            
            # 檢查是否為文件路徑
            if isinstance(result, str) and os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"📊 文件大小: {file_size} bytes")
                print(f"📁 文件路徑: {result}")
                
                if file_size > 0:
                    print("🎉 文件有內容!")
                    return True
                else:
                    print("❌ 文件大小為0")
                    return False
            else:
                print("⚠️ 結果不是有效的文件路徑")
                return False
        else:
            print("❌ 下載失敗，返回None或False")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        print("詳細錯誤信息:")
        traceback.print_exc()
        return False

def check_download_directories():
    """檢查下載目錄"""
    print("📁 檢查下載目錄...")
    
    directories = [
        "D:/Finlab/history/tables/monthly_revenue",
        "C:/Users/<USER>/Downloads",
        "./downloads",
        "./data"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            files = os.listdir(directory)
            excel_files = [f for f in files if f.endswith(('.xls', '.xlsx'))]
            print(f"✅ {directory}: {len(files)} 個文件 ({len(excel_files)} 個Excel)")
            
            # 顯示最新的Excel文件
            if excel_files:
                excel_files.sort(key=lambda x: os.path.getmtime(os.path.join(directory, x)), reverse=True)
                latest = excel_files[0]
                mtime = os.path.getmtime(os.path.join(directory, latest))
                import datetime
                mtime_str = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"   最新Excel: {latest} ({mtime_str})")
        else:
            print(f"❌ {directory}: 不存在")

def main():
    """主函數"""
    print("🚀 月營收下載調試")
    print("=" * 50)
    
    # 檢查下載目錄
    check_download_directories()
    
    print("\n" + "=" * 50)
    
    # 執行下載測試
    success = test_download_debug()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 下載測試成功!")
    else:
        print("❌ 下載測試失敗!")
    
    # 再次檢查下載目錄
    print("\n📁 測試後的目錄狀態:")
    check_download_directories()

if __name__ == "__main__":
    main()
