#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新後的PE數據與高殖利率烏龜策略
"""

import sys
sys.path.append('strategies')

from high_yield_turtle_strategy import HighYieldTurtleStrategy
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_updated_pe_strategy():
    """測試更新後的PE數據與高殖利率烏龜策略"""
    
    print("🧪 測試更新後的PE數據與高殖利率烏龜策略")
    print("=" * 60)
    
    # 1. 測試策略初始化
    print("1️⃣ 測試策略初始化...")
    try:
        strategy = HighYieldTurtleStrategy()
        print(f"   ✅ 策略初始化成功")
        print(f"   📊 PE數據筆數: {len(strategy.pe_data_cache):,}")
        print(f"   📈 涵蓋股票數: {strategy.pe_data_cache['股票代號'].nunique():,} 支")
    except Exception as e:
        print(f"   ❌ 策略初始化失敗: {e}")
        return False
    
    # 2. 創建測試股價數據
    print("\n2️⃣ 創建測試股價數據...")
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [50 * (1.01 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    print(f"   ✅ 測試數據創建完成，{len(test_df)} 天數據")
    
    # 3. 測試常見股票
    print("\n3️⃣ 測試常見股票分析...")
    test_stocks = ['1101', '1108', '2330', '2454', '2881', '2882', '2886', '2890']
    
    suitable_stocks = []
    
    for stock_id in test_stocks:
        try:
            result = strategy.analyze_stock(test_df, stock_id=stock_id)
            
            score = result.get('score', 0)
            suitable = result.get('suitable', False)
            details = result.get('details', {})
            data_source = details.get('data_source', '未知')
            
            status = "✅ 符合" if suitable else "❌ 不符合"
            print(f"   {status} {stock_id}: 評分{score}/100 ({data_source})")
            
            if suitable:
                suitable_stocks.append(stock_id)
                
                # 顯示詳細信息
                if 'pe_info' in details:
                    pe_info = details['pe_info']
                    dividend_yield = pe_info.get('殖利率(%)', 'N/A')
                    pe_ratio = pe_info.get('本益比', 'N/A')
                    pb_ratio = pe_info.get('股價淨值比', 'N/A')
                    print(f"      📊 殖利率: {dividend_yield}%, 本益比: {pe_ratio}, 股價淨值比: {pb_ratio}")
            
        except Exception as e:
            print(f"   ❌ {stock_id}: 測試失敗 - {e}")
    
    print(f"\n   📊 測試結果: {len(suitable_stocks)}/{len(test_stocks)} 支股票符合策略")
    
    # 4. 尋找高殖利率股票
    print("\n4️⃣ 尋找高殖利率股票...")
    try:
        pe_data = strategy.pe_data_cache
        
        # 轉換數據類型並過濾
        pe_data_clean = pe_data.copy()
        pe_data_clean['殖利率(%)'] = pd.to_numeric(pe_data_clean['殖利率(%)'], errors='coerce')
        pe_data_clean['本益比'] = pd.to_numeric(pe_data_clean['本益比'], errors='coerce')
        
        # 找出高殖利率股票
        high_dividend = pe_data_clean[
            (pe_data_clean['殖利率(%)'] >= 6.0) & 
            (pe_data_clean['殖利率(%)'] <= 20.0) &  # 排除異常值
            (pe_data_clean['本益比'] > 0) &
            (pe_data_clean['本益比'] <= 20)  # 合理的本益比範圍
        ]
        
        print(f"   🎯 找到高殖利率股票: {len(high_dividend):,} 筆記錄")
        
        if len(high_dividend) > 0:
            # 按股票分組，取最新數據
            latest_high_dividend = high_dividend.sort_values('date').groupby('股票代號').tail(1)
            
            print(f"   📈 獨特高殖利率股票: {len(latest_high_dividend)} 支")
            
            # 顯示前10名
            top_dividend = latest_high_dividend.nlargest(10, '殖利率(%)')
            print("\n   🏆 前10名高殖利率股票:")
            for idx, row in top_dividend.iterrows():
                stock_id = row['股票代號']
                dividend = row['殖利率(%)']
                pe_ratio = row['本益比']
                pb_ratio = row['股價淨值比']
                print(f"      {stock_id}: 殖利率{dividend:.2f}%, 本益比{pe_ratio:.2f}, 股價淨值比{pb_ratio:.2f}")
        
    except Exception as e:
        print(f"   ❌ 高殖利率股票分析失敗: {e}")
    
    # 5. 測試策略在GUI中的整合
    print("\n5️⃣ 測試策略整合...")
    try:
        # 模擬GUI調用
        if suitable_stocks:
            test_stock = suitable_stocks[0]
            result = strategy.analyze_stock(test_df, stock_id=test_stock)
            
            print(f"   ✅ GUI整合測試成功")
            print(f"   📊 測試股票 {test_stock}: 評分 {result['score']}/100")
            print(f"   🎯 策略建議: {'買入' if result['suitable'] else '觀望'}")
        else:
            print("   ⚠️ 沒有符合條件的股票進行GUI整合測試")
            
    except Exception as e:
        print(f"   ❌ GUI整合測試失敗: {e}")
    
    print("\n" + "=" * 60)
    print("✅ PE數據與策略測試完成！")
    
    return True

def verify_pe_data_quality():
    """驗證PE數據品質"""
    
    print("\n🔍 驗證PE數據品質")
    print("=" * 40)
    
    try:
        pe_data = pd.read_pickle('D:/Finlab/pe.pkl')
        
        print(f"📊 總數據筆數: {len(pe_data):,}")
        print(f"📈 涵蓋股票數: {pe_data['股票代號'].nunique():,} 支")
        print(f"📅 時間範圍: {pe_data['date'].min()} ~ {pe_data['date'].max()}")
        
        # 檢查數據完整性
        print("\n📋 數據完整性檢查:")
        for col in ['股票代號', '殖利率(%)', '本益比', '股價淨值比']:
            if col in pe_data.columns:
                null_count = pe_data[col].isnull().sum()
                null_pct = (null_count / len(pe_data)) * 100
                print(f"   {col}: {null_count:,} 個空值 ({null_pct:.2f}%)")
        
        # 檢查數據範圍
        print("\n📊 數據範圍檢查:")
        numeric_cols = ['殖利率(%)', '本益比', '股價淨值比']
        for col in numeric_cols:
            if col in pe_data.columns:
                try:
                    col_data = pd.to_numeric(pe_data[col], errors='coerce')
                    valid_data = col_data.dropna()
                    if len(valid_data) > 0:
                        print(f"   {col}: {valid_data.min():.2f} ~ {valid_data.max():.2f} (平均: {valid_data.mean():.2f})")
                except:
                    print(f"   {col}: 數據類型轉換失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ PE數據品質驗證失敗: {e}")
        return False

if __name__ == "__main__":
    # 驗證PE數據品質
    verify_pe_data_quality()
    
    # 測試策略
    test_updated_pe_strategy()
    
    print("\n🎉 所有測試完成！高殖利率烏龜策略現在可以正常使用了。")
