# Finlab量化策略 - 數據整合指南

## 📋 概述

本指南說明如何為 Finlab 量化策略整合真實的財務數據，使策略能夠正常運作。

## 🎯 必需數據清單

### 1. 市值數據
```python
# 需要的數據格式
market_value = {
    'stock_id': '2330',  # 股票代碼
    'market_value': 15000000000000,  # 市值 (元)
    'shares_outstanding': 25930380000,  # 流通股數
    'date': '2024-01-01'
}

# 計算公式
市值 = 股本 × 股價
```

### 2. 自由現金流數據
```python
# 需要的數據格式
cash_flow = {
    'stock_id': '2330',
    'operating_cash_flow': 500000000,  # 營業活動現金流
    'investing_cash_flow': -200000000,  # 投資活動現金流
    'free_cash_flow': 300000000,  # 自由現金流
    'quarter': '2024Q1'
}

# 計算公式
自由現金流 = 營業活動現金流 + 投資活動現金流
```

### 3. ROE數據
```python
# 需要的數據格式
roe_data = {
    'stock_id': '2330',
    'net_income': 200000000,  # 稅後淨利
    'shareholders_equity': 2000000000,  # 股東權益總額
    'roe': 10.0,  # ROE百分比
    'quarter': '2024Q1'
}

# 計算公式
ROE = 稅後淨利 / 權益總計 × 100%
```

### 4. 營業利益成長率
```python
# 需要的數據格式
growth_data = {
    'stock_id': '2330',
    'operating_income_current': 150000000,  # 本期營業利益
    'operating_income_previous': 120000000,  # 去年同期營業利益
    'growth_rate': 25.0,  # 成長率百分比
    'quarter': '2024Q1'
}

# 計算公式
營業利益成長率 = (本期營業利益 - 去年同期營業利益) / 去年同期營業利益 × 100%
```

### 5. 月營收數據
```python
# 需要的數據格式
revenue_data = {
    'stock_id': '2330',
    'monthly_revenue': 50000000,  # 當月營收 (千元)
    'quarterly_revenue': 150000000,  # 當季營收
    'month': '2024-01'
}

# 計算公式
當季營收 = 當月營收.rolling(3).sum()  # 滾動3個月
```

## 🔌 數據源整合

### 1. TEJ 台灣經濟新報
```python
import tejapi

# 設定API金鑰
tejapi.ApiConfig.api_key = "YOUR_API_KEY"

def get_tej_financial_data(stock_id, start_date, end_date):
    """獲取TEJ財務數據"""
    # 市值數據
    market_data = tejapi.get('TWN/APRCD', 
                           coid=stock_id,
                           mdate={'gte': start_date, 'lte': end_date},
                           opts={'columns': ['coid', 'mdate', 'mktcap']})
    
    # 財務數據
    financial_data = tejapi.get('TWN/AFINQ', 
                              coid=stock_id,
                              mdate={'gte': start_date, 'lte': end_date})
    
    return market_data, financial_data
```

### 2. 財報狗 API
```python
import requests

def get_statementdog_data(stock_id):
    """獲取財報狗數據"""
    api_token = "YOUR_API_TOKEN"
    
    # ROE數據
    roe_url = f"https://api.statementdog.com/v1/fundamentals/{stock_id}/roe"
    headers = {"Authorization": f"Token {api_token}"}
    
    response = requests.get(roe_url, headers=headers)
    return response.json()
```

### 3. 公開資訊觀測站
```python
import requests
from bs4 import BeautifulSoup

def get_mops_revenue(stock_id, year, month):
    """獲取公開資訊觀測站月營收"""
    url = "https://mops.twse.com.tw/nas/t21/sii/t21sc03.html"
    
    data = {
        'encodeURIComponent': '1',
        'step': '1',
        'firstin': '1',
        'off': '1',
        'co_id': stock_id,
        'year': year,
        'month': month
    }
    
    response = requests.post(url, data=data)
    # 解析HTML獲取營收數據
    return parse_revenue_data(response.text)
```

### 4. finlab 數據庫
```python
from finlab import data

def get_finlab_data():
    """獲取finlab數據"""
    # 市值
    market_value = data.get('etl:market_value')
    
    # 自由現金流
    investing_cf = data.get('financial_statement:投資活動之淨現金流入_流出')
    operating_cf = data.get('financial_statement:營業活動之淨現金流入_流出')
    free_cash_flow = (investing_cf + operating_cf).rolling(4).mean()
    
    # ROE
    net_income = data.get('fundamental_features:經常稅後淨利')
    equity = data.get('financial_statement:股東權益總額')
    roe = net_income / equity
    
    # 營業利益成長率
    operating_growth = data.get('fundamental_features:營業利益成長率')
    
    # 月營收
    monthly_revenue = data.get('monthly_revenue:當月營收') * 1000
    
    return {
        'market_value': market_value,
        'free_cash_flow': free_cash_flow,
        'roe': roe,
        'operating_growth': operating_growth,
        'monthly_revenue': monthly_revenue
    }
```

## 🔧 策略修改指南

### 1. 修改市值檢查函數
```python
def check_market_value_condition(self, df, market_data):
    """檢查市值條件：市值 < 100億"""
    try:
        if market_data is None or 'market_value' not in market_data:
            return False, "❌ 缺少市值數據"
        
        current_market_value = market_data['market_value'].iloc[-1]
        
        if current_market_value < self.max_market_value:
            return True, f"市值 {current_market_value/1e8:.1f}億 < 100億"
        else:
            return False, f"市值 {current_market_value/1e8:.1f}億 >= 100億"
            
    except Exception as e:
        return False, f"市值條件檢查失敗: {str(e)}"
```

### 2. 修改自由現金流檢查函數
```python
def check_free_cash_flow_condition(self, financial_data):
    """檢查自由現金流條件：自由現金流 > 0"""
    try:
        if financial_data is None or 'free_cash_flow' not in financial_data:
            return False, "❌ 缺少自由現金流數據"
        
        latest_fcf = financial_data['free_cash_flow'].iloc[-1]
        
        if latest_fcf > self.min_free_cash_flow:
            return True, f"自由現金流 {latest_fcf/1e8:.2f}億 > 0"
        else:
            return False, f"自由現金流 {latest_fcf/1e8:.2f}億 <= 0"
            
    except Exception as e:
        return False, f"自由現金流條件檢查失敗: {str(e)}"
```

## 📊 完整整合範例

```python
class FinlabQuantStrategyWithRealData(FinlabQuantStrategy):
    """整合真實數據的Finlab量化策略"""
    
    def __init__(self, data_source='finlab'):
        super().__init__()
        self.data_source = data_source
        self.data_loader = self._init_data_loader()
    
    def _init_data_loader(self):
        """初始化數據載入器"""
        if self.data_source == 'finlab':
            return FinlabDataLoader()
        elif self.data_source == 'tej':
            return TEJDataLoader()
        elif self.data_source == 'statementdog':
            return StatementDogDataLoader()
        else:
            raise ValueError(f"不支援的數據源: {self.data_source}")
    
    def analyze_stock_with_real_data(self, stock_id, end_date=None):
        """使用真實數據分析股票"""
        # 載入所有必需數據
        price_data = self.data_loader.get_price_data(stock_id, end_date)
        market_data = self.data_loader.get_market_data(stock_id, end_date)
        financial_data = self.data_loader.get_financial_data(stock_id, end_date)
        revenue_data = self.data_loader.get_revenue_data(stock_id, end_date)
        
        # 執行分析
        return self.analyze_stock(price_data, market_data, financial_data, revenue_data)
```

## 🚀 部署步驟

1. **選擇數據源**: 根據預算和需求選擇合適的數據源
2. **申請API**: 申請相應的API金鑰或訂閱服務
3. **修改策略**: 按照上述指南修改策略檢查函數
4. **測試驗證**: 使用真實數據進行測試
5. **回測驗證**: 進行歷史回測確認策略效果
6. **正式部署**: 整合到選股系統中

## 💡 建議

- **數據品質**: 確保數據的準確性和及時性
- **異常處理**: 建立完善的異常處理機制
- **數據快取**: 實現數據快取以提高效能
- **監控告警**: 建立數據異常監控和告警機制

---

*完成數據整合後，Finlab量化策略將能夠正常運作並進行有效的股票篩選。*
