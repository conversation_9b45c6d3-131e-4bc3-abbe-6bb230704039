#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正資料庫中的 NULL 股票資訊
"""

import sys
import os
import sqlite3
import pandas as pd

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def fix_null_stock_info():
    """修正資料庫中的 NULL 股票資訊"""
    
    print("=" * 60)
    print("🔧 修正資料庫中的 NULL 股票資訊")
    print("=" * 60)
    
    try:
        from crawler import get_cached_stock_info
        
        # 獲取最新的股票資訊
        print("📡 獲取最新股票資訊...")
        stock_info = get_cached_stock_info()
        print(f"✅ 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 連接資料庫
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 檢查有多少記錄需要修正
        print(f"\n📊 檢查需要修正的記錄...")
        cursor.execute('''
            SELECT COUNT(*) as total_null
            FROM stock_daily_data 
            WHERE listing_status IS NULL OR industry IS NULL
        ''')
        total_null = cursor.fetchone()[0]
        print(f"   需要修正的記錄數: {total_null}")
        
        if total_null == 0:
            print("✅ 所有記錄都已有完整的股票資訊")
            conn.close()
            return
        
        # 獲取所有需要修正的股票代碼
        cursor.execute('''
            SELECT DISTINCT stock_id
            FROM stock_daily_data 
            WHERE listing_status IS NULL OR industry IS NULL
        ''')
        null_stocks = [row[0] for row in cursor.fetchall()]
        print(f"   需要修正的股票數: {len(null_stocks)}")
        
        # 批次修正
        print(f"\n🔧 開始批次修正...")
        fixed_count = 0
        
        for stock_id in null_stocks:
            if stock_id in stock_info:
                info = stock_info[stock_id]
                
                # 更新該股票的所有記錄
                cursor.execute('''
                    UPDATE stock_daily_data 
                    SET listing_status = ?, industry = ?
                    WHERE stock_id = ? AND (listing_status IS NULL OR industry IS NULL)
                ''', (info['listing_status'], info['industry'], stock_id))
                
                updated_rows = cursor.rowcount
                if updated_rows > 0:
                    fixed_count += updated_rows
                    print(f"   ✅ {stock_id} ({info['stock_name']}): 更新 {updated_rows} 筆記錄")
            else:
                print(f"   ⚠️ {stock_id}: 在股票資訊中未找到")
        
        # 提交變更
        conn.commit()
        print(f"\n✅ 修正完成！總共更新了 {fixed_count} 筆記錄")
        
        # 驗證修正結果
        print(f"\n🔍 驗證修正結果...")
        cursor.execute('''
            SELECT COUNT(*) as remaining_null
            FROM stock_daily_data 
            WHERE listing_status IS NULL OR industry IS NULL
        ''')
        remaining_null = cursor.fetchone()[0]
        print(f"   剩餘 NULL 記錄數: {remaining_null}")
        
        # 檢查 0050 的修正結果
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 5
        ''')
        etf_data = cursor.fetchall()
        
        print(f"\n📋 0050 修正後的資料:")
        for stock_id, name, status, industry, date, close in etf_data:
            print(f"   {date}: {stock_id} | {name} | {status} | {industry} | {close}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 修正失敗: {e}")
        import traceback
        traceback.print_exc()

def check_specific_query():
    """檢查特定查詢的結果"""
    
    print("\n" + "=" * 60)
    print("🔍 檢查特定查詢結果")
    print("=" * 60)
    
    try:
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        
        # 模擬你可能使用的查詢
        queries = [
            # 查詢最近7天的 0050 資料
            '''
            SELECT stock_id, stock_name, listing_status, industry, Volume, [Transaction], TradeValue
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 7
            ''',
            
            # 查詢最新日期的所有 ETF
            '''
            SELECT stock_id, stock_name, listing_status, industry, Volume, [Transaction], TradeValue
            FROM stock_daily_data 
            WHERE date = (SELECT MAX(date) FROM stock_daily_data) 
            AND stock_id LIKE '00%'
            ORDER BY stock_id
            LIMIT 7
            '''
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n📊 查詢 {i}:")
            df = pd.read_sql_query(query, conn)
            
            if not df.empty:
                print(f"   結果: {len(df)} 筆資料")
                for _, row in df.iterrows():
                    status = row['listing_status'] if pd.notna(row['listing_status']) else 'NULL'
                    industry = row['industry'] if pd.notna(row['industry']) else 'NULL'
                    print(f"   {row['stock_id']}: {row['stock_name']} | {status} | {industry} | Vol:{row['Volume']} | Trans:{row['Transaction']} | Value:{row['TradeValue']}")
            else:
                print(f"   結果: 無資料")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_null_stock_info()
    check_specific_query()
