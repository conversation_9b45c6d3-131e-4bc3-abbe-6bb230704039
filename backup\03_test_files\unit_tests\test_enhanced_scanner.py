#!/usr/bin/env python3
"""
測試增強版市場掃描器
驗證多數據源整合和yfinance增強功能
"""

import sys
import logging
import time
import threading
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_enhanced_scanner():
    """測試增強版市場掃描器"""
    print("=" * 60)
    print("🚀 測試增強版市場掃描器")
    print("=" * 60)
    
    try:
        from enhanced_market_scanner import EnhancedMarketScanner
        
        scanner = EnhancedMarketScanner()
        
        print("📊 開始效能測試...")
        start_time = time.time()
        
        # 執行掃描
        results = scanner.run_full_scan()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if results:
            print("✅ 掃描成功完成")
            print(f"⏱️ 總耗時: {duration:.2f} 秒")
            print(f"📊 市場指數: {len(results.get('market_indices', {}))} 項")
            print(f"📈 熱門股票: {len(results.get('hot_stocks', {}))} 項")
            print(f"🔧 使用數據源: {', '.join(results.get('data_sources_used', []))}")
            
            # 顯示市場摘要
            summary = results.get('market_summary', {})
            if summary:
                print(f"💭 市場情緒: {summary.get('market_sentiment', '未知')}")
                print(f"📊 總項目: {summary.get('total_items', 0)}")
                print(f"📈 上漲: {summary.get('positive_count', 0)}")
                print(f"📉 下跌: {summary.get('negative_count', 0)}")
                print(f"📊 平均漲跌: {summary.get('avg_change', 0):+.2f}%")
                print(f"🎯 數據質量: {summary.get('data_quality', '未知')}")
            
            # 顯示部分數據
            print("\n📈 市場指數數據:")
            for symbol, data in list(results.get('market_indices', {}).items())[:3]:
                if 'data' in data:
                    price_data = data['data']
                    name = data.get('name', symbol)
                    price = price_data.get('close', 0)
                    change = price_data.get('pct_change', 0)
                    source = price_data.get('source', 'unknown')
                    trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    print(f"   {trend} {name}: ${price:.2f} ({change:+.2f}%) [{source}]")
            
            print("\n📊 熱門股票數據:")
            for symbol, data in list(results.get('hot_stocks', {}).items())[:5]:
                price = data.get('close', 0)
                change = data.get('pct_change', 0)
                volume = data.get('volume', 0)
                source = data.get('source', 'unknown')
                trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                print(f"   {trend} {symbol}: ${price:.2f} ({change:+.2f}%) 量:{volume:,.0f} [{source}]")
            
            return True
        else:
            print("❌ 掃描失敗")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_enhanced_yfinance():
    """測試增強版yfinance獲取器"""
    print("\n" + "=" * 60)
    print("📊 測試增強版yfinance獲取器")
    print("=" * 60)
    
    try:
        from enhanced_yfinance_fetcher import EnhancedYFinanceFetcher
        
        fetcher = EnhancedYFinanceFetcher()
        
        # 測試連接
        print("🧪 測試yfinance連接...")
        if fetcher.test_connection():
            print("✅ yfinance連接正常")
            
            # 測試股票數據獲取
            print("\n📊 測試股票數據獲取:")
            test_stocks = ['2330', '2317', '0050', '2454']
            
            start_time = time.time()
            for stock in test_stocks:
                data = fetcher.get_stock_data(stock)
                if data:
                    print(f"  ✅ {stock}: {data['name']} - ${data['close']:.2f} ({data['pct_change']:+.2f}%) [{data['source']}]")
                else:
                    print(f"  ❌ {stock}: 獲取失敗")
            
            duration = time.time() - start_time
            print(f"⏱️ 獲取耗時: {duration:.2f} 秒")
            
            # 測試批量獲取
            print("\n📈 測試批量數據獲取:")
            batch_stocks = ['2330', '2317', '2454']
            start_time = time.time()
            batch_results = fetcher.get_batch_data(batch_stocks)
            duration = time.time() - start_time
            
            print(f"✅ 批量獲取完成: {len(batch_results)}/{len(batch_stocks)} 成功")
            print(f"⏱️ 批量耗時: {duration:.2f} 秒")
            
            # 測試盤中數據
            print("\n📈 測試盤中數據獲取:")
            intraday_data = fetcher.get_intraday_data('2330', '5m')
            if not intraday_data.empty:
                print(f"  ✅ 台積電盤中數據: {len(intraday_data)} 筆")
                print(f"  📊 時間範圍: {intraday_data['datetime'].min()} ~ {intraday_data['datetime'].max()}")
            else:
                print("  ❌ 盤中數據獲取失敗")
            
            # 顯示緩存統計
            cache_stats = fetcher.get_cache_stats()
            print(f"\n💾 緩存統計: {cache_stats['cache_size']} 項")
            
            return True
        else:
            print("❌ yfinance連接失敗")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_concurrent_performance():
    """測試並發性能"""
    print("\n" + "=" * 60)
    print("🔄 測試並發性能")
    print("=" * 60)
    
    try:
        from enhanced_market_scanner import EnhancedMarketScanner
        from enhanced_yfinance_fetcher import EnhancedYFinanceFetcher
        
        results = []
        threads = []
        
        def run_scanner_test(test_id):
            """執行掃描器測試"""
            try:
                scanner = EnhancedMarketScanner()
                start_time = time.time()
                result = scanner.run_full_scan()
                end_time = time.time()
                
                results.append({
                    'test_id': test_id,
                    'type': 'scanner',
                    'success': result is not None,
                    'duration': end_time - start_time,
                    'data_count': len(result.get('hot_stocks', {})) if result else 0
                })
                
                print(f"   掃描器 {test_id}: {'✅ 成功' if result else '❌ 失敗'} ({end_time - start_time:.2f}秒)")
                
            except Exception as e:
                print(f"   掃描器 {test_id}: ❌ 異常 - {e}")
                results.append({
                    'test_id': test_id,
                    'type': 'scanner',
                    'success': False,
                    'duration': 0,
                    'error': str(e)
                })
        
        def run_yfinance_test(test_id):
            """執行yfinance測試"""
            try:
                fetcher = EnhancedYFinanceFetcher()
                start_time = time.time()
                data = fetcher.get_batch_data(['2330', '2317', '2454'])
                end_time = time.time()
                
                results.append({
                    'test_id': test_id,
                    'type': 'yfinance',
                    'success': len(data) > 0,
                    'duration': end_time - start_time,
                    'data_count': len(data)
                })
                
                print(f"   yfinance {test_id}: {'✅ 成功' if data else '❌ 失敗'} ({end_time - start_time:.2f}秒)")
                
            except Exception as e:
                print(f"   yfinance {test_id}: ❌ 異常 - {e}")
                results.append({
                    'test_id': test_id,
                    'type': 'yfinance',
                    'success': False,
                    'duration': 0,
                    'error': str(e)
                })
        
        # 啟動並發測試
        print("🚀 啟動2個掃描器 + 2個yfinance測試...")
        
        # 掃描器測試
        for i in range(2):
            thread = threading.Thread(target=run_scanner_test, args=(f"S{i+1}",))
            threads.append(thread)
            thread.start()
        
        # yfinance測試
        for i in range(2):
            thread = threading.Thread(target=run_yfinance_test, args=(f"Y{i+1}",))
            threads.append(thread)
            thread.start()
        
        # 等待所有線程完成
        for thread in threads:
            thread.join()
        
        # 統計結果
        scanner_results = [r for r in results if r['type'] == 'scanner']
        yfinance_results = [r for r in results if r['type'] == 'yfinance']
        
        scanner_success = sum(1 for r in scanner_results if r['success'])
        yfinance_success = sum(1 for r in yfinance_results if r['success'])
        
        scanner_avg_time = sum(r['duration'] for r in scanner_results if r['success']) / max(scanner_success, 1)
        yfinance_avg_time = sum(r['duration'] for r in yfinance_results if r['success']) / max(yfinance_success, 1)
        
        print(f"\n📊 並發測試結果:")
        print(f"   掃描器成功率: {scanner_success}/{len(scanner_results)} ({scanner_success/len(scanner_results)*100:.1f}%)")
        print(f"   yfinance成功率: {yfinance_success}/{len(yfinance_results)} ({yfinance_success/len(yfinance_results)*100:.1f}%)")
        print(f"   掃描器平均耗時: {scanner_avg_time:.2f} 秒")
        print(f"   yfinance平均耗時: {yfinance_avg_time:.2f} 秒")
        
        return scanner_success > 0 and yfinance_success > 0
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 增強版市場掃描器和yfinance測試程式")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("增強版掃描器", test_enhanced_scanner()))
    test_results.append(("增強版yfinance", test_enhanced_yfinance()))
    test_results.append(("並發性能測試", test_concurrent_performance()))
    
    # 顯示測試總結
    print("\n" + "=" * 60)
    print("📋 測試總結")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有測試通過！增強版系統運作正常")
        print("💡 建議：可以在主程式中啟用增強版掃描器和yfinance")
        return True
    elif passed >= total * 0.75:
        print("⚠️ 大部分測試通過，建議檢查失敗項目")
        return True
    else:
        print("❌ 多項測試失敗，需要檢查和修復")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 測試程式異常: {e}")
        sys.exit(1)
