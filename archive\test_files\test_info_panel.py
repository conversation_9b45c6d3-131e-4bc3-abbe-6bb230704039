#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試資訊面板功能
"""

import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
    QTabWidget, QGroupBox, QPushButton, QLabel, QCheckBox, QSpinBox,
    QSizePolicy
)
from PyQt6.QtCore import QTimer

class TestInfoPanel(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("資訊面板測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 創建標籤頁
        tab_widget = QTabWidget()
        
        # 全部股票頁面
        all_stocks_page = QWidget()
        all_stocks_layout = QVBoxLayout(all_stocks_page)
        all_stocks_layout.addWidget(QLabel("全部股票列表"))
        tab_widget.addTab(all_stocks_page, "全部股票")
        
        # 選股結果頁面
        results_page = QWidget()
        results_layout = QVBoxLayout(results_page)
        results_layout.addWidget(QLabel("選股結果"))
        tab_widget.addTab(results_page, "選股結果")
        
        # 資訊面板頁面
        info_panel_page = QWidget()
        info_panel_layout = QVBoxLayout(info_panel_page)
        self.create_info_panel_content(info_panel_layout)
        tab_widget.addTab(info_panel_page, "資訊面板")
        
        main_layout.addWidget(tab_widget)
    
    def create_info_panel_content(self, layout):
        """創建資訊面板內容"""
        # 面板控制區域
        panel_control_group = QGroupBox("🎛️ 面板控制")
        panel_control_layout = QVBoxLayout(panel_control_group)
        
        # 三個主要功能按鈕
        buttons_layout = QHBoxLayout()
        
        # 日內策略按鈕 (調整高度)
        intraday_btn = QPushButton("📝 日內策略")
        intraday_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b, stop:1 #ee5a52);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
                min-height: 28px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff5252, stop:1 #d32f2f);
            }
        """)
        intraday_btn.setMinimumHeight(28)
        intraday_btn.setMinimumWidth(80)
        intraday_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        intraday_btn.clicked.connect(lambda: self.show_panel_status("📝 日內策略面板已顯示"))

        # 智能分析按鈕 (調整高度)
        analysis_btn = QPushButton("🎯 智能分析")
        analysis_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4fc3f7, stop:1 #29b6f6);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
                min-height: 28px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #29b6f6, stop:1 #0288d1);
            }
        """)
        analysis_btn.setMinimumHeight(28)
        analysis_btn.setMinimumWidth(80)
        analysis_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        analysis_btn.clicked.connect(lambda: self.show_panel_status("🎯 智能分析面板已顯示"))

        # 開盤監控按鈕 (調整高度)
        premarket_btn = QPushButton("🏛️ 開盤監控")
        premarket_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff9800, stop:1 #f57c00);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
                min-height: 28px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f57c00, stop:1 #e65100);
            }
        """)
        premarket_btn.setMinimumHeight(28)
        premarket_btn.setMinimumWidth(80)
        premarket_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        premarket_btn.clicked.connect(lambda: self.show_panel_status("🏛️ 開盤監控面板已顯示"))
        
        buttons_layout.addWidget(intraday_btn)
        buttons_layout.addWidget(analysis_btn)
        buttons_layout.addWidget(premarket_btn)
        panel_control_layout.addLayout(buttons_layout)
        
        # 面板狀態提示
        self.panel_status_label = QLabel("🎛️ 開盤監控面板已顯示")
        self.panel_status_label.setStyleSheet("""
            QLabel {
                color: #4caf50;
                font-size: 11px;
                padding: 5px;
                background-color: rgba(76, 175, 80, 0.1);
                border-radius: 4px;
                border: 1px solid rgba(76, 175, 80, 0.3);
            }
        """)
        panel_control_layout.addWidget(self.panel_status_label)
        
        layout.addWidget(panel_control_group)
        
        # 開盤前市場監控區域
        premarket_group = QGroupBox("🏛️ 開盤前市場監控")
        premarket_layout = QVBoxLayout(premarket_group)
        
        # 市場概況顯示區域 (限制高度)
        market_overview_label = QLabel("📊 正在獲取市場數據...\n\n🇺🇸 美股指數:\n  📈 道瓊: 35,123 (+0.45%)\n  📈 那斯達克: 14,567 (+0.32%)\n  📈 標普500: 4,234 (+0.28%)\n\n🇹🇼 台指期: 22,502 (+0.33%)\n  📊 多方: 42,220口 | 空方: 37,918口\n  📋 未平倉: 180,550口")
        market_overview_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border-radius: 6px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                line-height: 1.3;
                max-height: 120px;
            }
        """)
        market_overview_label.setWordWrap(True)
        market_overview_label.setMaximumHeight(120)
        premarket_layout.addWidget(market_overview_label)
        
        # 三個功能按鈕
        premarket_buttons_layout = QHBoxLayout()
        
        # 智能掃描按鈕 (調整高度)
        scan_btn = QPushButton("🔍 智能掃描\n市場情報")
        scan_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 10px;
                font-weight: bold;
                min-height: 32px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #388e3c, stop:1 #2e7d32);
            }
        """)
        scan_btn.setMinimumHeight(32)
        scan_btn.setMinimumWidth(80)
        scan_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        scan_btn.clicked.connect(lambda: print("智能掃描功能"))

        # 市場分析按鈕 (調整高度)
        market_analysis_btn = QPushButton("🧠 市場分析\n全球動態")
        market_analysis_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 10px;
                font-weight: bold;
                min-height: 32px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #1565c0);
            }
        """)
        market_analysis_btn.setMinimumHeight(32)
        market_analysis_btn.setMinimumWidth(80)
        market_analysis_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        market_analysis_btn.clicked.connect(lambda: print("市場分析功能"))

        # 投資建議按鈕 (調整高度)
        investment_advice_btn = QPushButton("💡 投資建議\n台指期")
        investment_advice_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff9800, stop:1 #f57c00);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 10px;
                font-weight: bold;
                min-height: 32px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f57c00, stop:1 #e65100);
            }
        """)
        investment_advice_btn.setMinimumHeight(32)
        investment_advice_btn.setMinimumWidth(80)
        investment_advice_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        investment_advice_btn.clicked.connect(lambda: print("投資建議功能"))
        
        premarket_buttons_layout.addWidget(scan_btn)
        premarket_buttons_layout.addWidget(market_analysis_btn)
        premarket_buttons_layout.addWidget(investment_advice_btn)
        premarket_layout.addLayout(premarket_buttons_layout)
        
        layout.addWidget(premarket_group)
        
        # 自動更新控制
        auto_update_layout = QHBoxLayout()
        auto_update_checkbox = QCheckBox("🔄 自動更新")
        auto_update_checkbox.setChecked(True)
        
        update_interval_label = QLabel("間隔:")
        update_interval_spinbox = QSpinBox()
        update_interval_spinbox.setRange(1, 60)
        update_interval_spinbox.setValue(5)
        update_interval_spinbox.setSuffix(" 分鐘")
        
        premarket_timer_label = QLabel("🔔 開盤前定時掃描 (8:20)")
        premarket_timer_label.setStyleSheet("color: #ff5722; font-weight: bold;")
        
        auto_update_layout.addWidget(auto_update_checkbox)
        auto_update_layout.addWidget(update_interval_label)
        auto_update_layout.addWidget(update_interval_spinbox)
        auto_update_layout.addStretch()
        auto_update_layout.addWidget(premarket_timer_label)
        
        layout.addLayout(auto_update_layout)
        layout.addStretch()
    
    def show_panel_status(self, message):
        """顯示面板狀態"""
        self.panel_status_label.setText(message)
        print(f"狀態更新: {message}")

def main():
    app = QApplication(sys.argv)
    window = TestInfoPanel()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
