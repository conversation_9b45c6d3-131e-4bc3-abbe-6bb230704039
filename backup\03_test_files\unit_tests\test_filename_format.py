#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試檔名格式生成邏輯
"""

from datetime import datetime

def generate_filename(stock_id, stock_name, start_date=None, end_date=None):
    """生成包含股票名稱和日期區間的檔名"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 清理股票名稱中的特殊字符
    clean_stock_name = "".join(c for c in stock_name if c.isalnum() or c in "._-")
    
    # 構建包含日期區間的檔名
    date_range = ""
    if start_date and end_date:
        date_range = f"_{start_date}_to_{end_date}"
    elif start_date:
        date_range = f"_from_{start_date}"
    elif end_date:
        date_range = f"_to_{end_date}"
    
    filename = f"{stock_id}_{clean_stock_name}_monthly_revenue{date_range}_{timestamp}.xls"
    return filename

def test_filename_formats():
    """測試不同的檔名格式"""
    print("🧪 測試檔名格式生成...")
    
    test_cases = [
        # (stock_id, stock_name, start_date, end_date, description)
        ("2330", "台積電", "2023-01", "2025-07", "完整日期區間"),
        ("2317", "鴻海", "2024-01", None, "只有開始日期"),
        ("2454", "聯發科", None, "2025-07", "只有結束日期"),
        ("6505", "台塑化", None, None, "無日期區間"),
        ("2412", "中華電", "2022-12", "2025-12", "跨年度區間"),
    ]
    
    for stock_id, stock_name, start_date, end_date, description in test_cases:
        print(f"\n📊 測試案例: {description}")
        print(f"   股票代號: {stock_id}")
        print(f"   股票名稱: {stock_name}")
        print(f"   開始日期: {start_date or '未指定'}")
        print(f"   結束日期: {end_date or '未指定'}")
        
        filename = generate_filename(stock_id, stock_name, start_date, end_date)
        print(f"📄 生成檔名: {filename}")
        
        # 驗證檔名內容
        checks = []
        if stock_id in filename:
            checks.append("✅ 包含股票代號")
        else:
            checks.append("❌ 缺少股票代號")
            
        if stock_name in filename:
            checks.append("✅ 包含股票名稱")
        else:
            checks.append("❌ 缺少股票名稱")
            
        if start_date and end_date:
            if f"{start_date}_to_{end_date}" in filename:
                checks.append("✅ 包含完整日期區間")
            else:
                checks.append("❌ 日期區間格式錯誤")
        elif start_date:
            if f"from_{start_date}" in filename:
                checks.append("✅ 包含開始日期")
            else:
                checks.append("❌ 開始日期格式錯誤")
        elif end_date:
            if f"to_{end_date}" in filename:
                checks.append("✅ 包含結束日期")
            else:
                checks.append("❌ 結束日期格式錯誤")
        else:
            if "_to_" not in filename and "_from_" not in filename:
                checks.append("✅ 無日期區間（符合預期）")
            else:
                checks.append("❌ 不應包含日期區間")
        
        if filename.endswith(".xls"):
            checks.append("✅ 正確的檔案副檔名")
        else:
            checks.append("❌ 檔案副檔名錯誤")
            
        for check in checks:
            print(f"   {check}")

if __name__ == "__main__":
    test_filename_formats()
