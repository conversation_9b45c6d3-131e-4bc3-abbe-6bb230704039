#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試右鍵選單功能
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函數"""
    print("=" * 60)
    print("🚀 快速測試右鍵選單功能")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QListWidgetItem
        from PyQt6.QtCore import QPoint
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 添加測試股票到左側列表
        test_stocks = [
            "2330 台積電",
            "2317 鴻海",
            "2454 聯發科"
        ]
        
        gui.all_stocks_list.clear()
        for stock_text in test_stocks:
            item = QListWidgetItem(stock_text)
            gui.all_stocks_list.addItem(item)
        
        gui.filtered_stocks_list.clear()
        for stock_text in test_stocks:
            item = QListWidgetItem(stock_text)
            gui.filtered_stocks_list.addItem(item)
        
        print("✅ 測試股票添加完成")
        
        # 模擬右鍵點擊
        print("\n🖱️ 模擬右鍵點擊測試...")
        
        # 測試全部股票列表
        print("  測試全部股票列表...")
        position = QPoint(50, 20)
        try:
            gui.show_stock_context_menu_for_list(position)
            print("  ✅ 全部股票列表右鍵選單調用成功")
        except Exception as e:
            print(f"  ❌ 全部股票列表右鍵選單調用失敗: {e}")
        
        # 顯示GUI
        print(f"\n🖥️ 顯示GUI進行手動測試...")
        print("📋 測試步驟:")
        print("1. 在左側「全部股票」列表中右鍵點擊任一股票")
        print("2. 檢查是否出現「📊 月營收綜合評估」選項")
        print("3. 如果出現，表示強制顯示模式生效")
        print("4. 如果沒出現，請檢查控制台日誌")
        
        gui.show()
        
        # 執行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
