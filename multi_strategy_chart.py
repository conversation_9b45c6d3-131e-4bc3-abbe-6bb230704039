#!/usr/bin/env python3
"""
多策略整合圖表顯示
將RSI、MACD、布林通道、移動平均交叉四種策略的信號整合在同一圖表上
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from datetime import datetime, timedelta
import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MultiStrategySignalAnalyzer:
    """多策略信號分析器"""
    
    def __init__(self):
        self.strategies = {}
        self.signal_colors = {
            'RSI': {'buy': '#FF6B6B', 'sell': '#4ECDC4'},
            'MACD': {'buy': '#45B7D1', 'sell': '#96CEB4'},
            '布林通道': {'buy': '#FECA57', 'sell': '#FF9FF3'},
            '移動平均交叉': {'buy': '#54A0FF', 'sell': '#5F27CD'}
        }

        self.signal_markers = {
            'RSI': {'buy': '^', 'sell': 'v'},
            'MACD': {'buy': 's', 'sell': 's'},
            '布林通道': {'buy': 'o', 'sell': 'o'},
            '移動平均交叉': {'buy': 'D', 'sell': 'D'}
        }

        self.signal_sizes = {
            'RSI': 120,
            'MACD': 100,
            '布林通道': 80,
            '移動平均交叉': 90
        }
    
    def calculate_rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """計算MACD"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """計算布林通道"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    def calculate_moving_averages(self, prices, short=10, long=30):
        """計算移動平均線"""
        short_ma = prices.rolling(window=short).mean()
        long_ma = prices.rolling(window=long).mean()
        return short_ma, long_ma
    
    def generate_rsi_signals(self, data, oversold=30, overbought=70):
        """生成RSI信號"""
        rsi = self.calculate_rsi(data['close'])
        
        buy_signals = []
        sell_signals = []
        
        for i in range(1, len(data)):
            if rsi.iloc[i-1] <= oversold and rsi.iloc[i] > oversold:
                strength = self.get_rsi_strength(rsi.iloc[i], 'buy')
                buy_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_rsi_text(rsi.iloc[i], 'buy')
                })
            elif rsi.iloc[i-1] <= overbought and rsi.iloc[i] > overbought:
                strength = self.get_rsi_strength(rsi.iloc[i], 'sell')
                sell_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_rsi_text(rsi.iloc[i], 'sell')
                })
        
        return buy_signals, sell_signals
    
    def generate_macd_signals(self, data):
        """生成MACD信號"""
        macd, signal_line, histogram = self.calculate_macd(data['close'])
        
        buy_signals = []
        sell_signals = []
        
        for i in range(1, len(data)):
            # 金叉
            if macd.iloc[i-1] <= signal_line.iloc[i-1] and macd.iloc[i] > signal_line.iloc[i]:
                strength = self.get_macd_strength(macd.iloc[i], signal_line.iloc[i], 'buy')
                buy_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_macd_text(macd.iloc[i], signal_line.iloc[i], 'buy')
                })
            # 死叉
            elif macd.iloc[i-1] >= signal_line.iloc[i-1] and macd.iloc[i] < signal_line.iloc[i]:
                strength = self.get_macd_strength(macd.iloc[i], signal_line.iloc[i], 'sell')
                sell_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_macd_text(macd.iloc[i], signal_line.iloc[i], 'sell')
                })
        
        return buy_signals, sell_signals
    
    def generate_bollinger_signals(self, data):
        """生成布林通道信號"""
        upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data['close'])
        
        buy_signals = []
        sell_signals = []
        
        for i in range(20, len(data)):
            price = data['close'].iloc[i]
            
            # 觸及下軌
            if price <= lower_band.iloc[i]:
                strength = self.get_bollinger_strength(price, lower_band.iloc[i], middle_band.iloc[i], 'buy')
                buy_signals.append({
                    'index': i,
                    'price': price,
                    'strength': strength,
                    'text': self.get_bollinger_text(price, lower_band.iloc[i], 'buy')
                })
            # 觸及上軌
            elif price >= upper_band.iloc[i]:
                strength = self.get_bollinger_strength(price, upper_band.iloc[i], middle_band.iloc[i], 'sell')
                sell_signals.append({
                    'index': i,
                    'price': price,
                    'strength': strength,
                    'text': self.get_bollinger_text(price, upper_band.iloc[i], 'sell')
                })
        
        return buy_signals, sell_signals
    
    def generate_ma_cross_signals(self, data):
        """生成移動平均交叉信號"""
        short_ma, long_ma = self.calculate_moving_averages(data['close'])
        
        buy_signals = []
        sell_signals = []
        
        for i in range(30, len(data)):
            # 金叉
            if short_ma.iloc[i-1] <= long_ma.iloc[i-1] and short_ma.iloc[i] > long_ma.iloc[i]:
                strength = self.get_ma_strength(short_ma.iloc[i], long_ma.iloc[i], data['close'].iloc[i], 'buy')
                buy_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_ma_text(short_ma.iloc[i], long_ma.iloc[i], 'buy')
                })
            # 死叉
            elif short_ma.iloc[i-1] >= long_ma.iloc[i-1] and short_ma.iloc[i] < long_ma.iloc[i]:
                strength = self.get_ma_strength(short_ma.iloc[i], long_ma.iloc[i], data['close'].iloc[i], 'sell')
                sell_signals.append({
                    'index': i,
                    'price': data['close'].iloc[i],
                    'strength': strength,
                    'text': self.get_ma_text(short_ma.iloc[i], long_ma.iloc[i], 'sell')
                })
        
        return buy_signals, sell_signals
    
    def get_rsi_strength(self, rsi_value, signal_type):
        """獲取RSI信號強度"""
        if signal_type == 'buy':
            if rsi_value <= 20:
                return 0.9
            elif rsi_value <= 25:
                return 0.7
            else:
                return 0.5
        else:  # sell
            if rsi_value >= 80:
                return 0.9
            elif rsi_value >= 75:
                return 0.7
            else:
                return 0.5
    
    def get_rsi_text(self, rsi_value, signal_type):
        """獲取RSI信號文字"""
        if signal_type == 'buy':
            if rsi_value <= 20:
                return '嚴重超賣'
            elif rsi_value <= 25:
                return '強力超賣'
            else:
                return '超賣反彈'
        else:  # sell
            if rsi_value >= 80:
                return '嚴重過熱'
            elif rsi_value >= 75:
                return '強力過熱'
            else:
                return '過熱回調'
    
    def get_macd_strength(self, macd_val, signal_val, signal_type):
        """獲取MACD信號強度"""
        cross_strength = abs(macd_val - signal_val)
        above_zero = macd_val > 0 and signal_val > 0
        
        if signal_type == 'buy':
            if above_zero and cross_strength > 0.5:
                return 0.9
            elif above_zero:
                return 0.7
            elif cross_strength > 0.3:
                return 0.6
            else:
                return 0.5
        else:  # sell
            below_zero = macd_val < 0 and signal_val < 0
            if below_zero and cross_strength > 0.5:
                return 0.9
            elif below_zero:
                return 0.7
            elif cross_strength > 0.3:
                return 0.6
            else:
                return 0.5
    
    def get_macd_text(self, macd_val, signal_val, signal_type):
        """獲取MACD信號文字"""
        above_zero = macd_val > 0 and signal_val > 0
        cross_strength = abs(macd_val - signal_val)
        
        if signal_type == 'buy':
            if above_zero and cross_strength > 0.5:
                return '強勢金叉'
            elif above_zero:
                return '零軸金叉'
            elif cross_strength > 0.3:
                return '急速金叉'
            else:
                return '金叉信號'
        else:  # sell
            below_zero = macd_val < 0 and signal_val < 0
            if below_zero and cross_strength > 0.5:
                return '強勢死叉'
            elif below_zero:
                return '零軸死叉'
            elif cross_strength > 0.3:
                return '急速死叉'
            else:
                return '死叉信號'
    
    def get_bollinger_strength(self, price, band_val, middle_val, signal_type):
        """獲取布林通道信號強度"""
        penetration = abs(price - band_val) / band_val if band_val > 0 else 0
        
        if penetration > 0.02:
            return 0.9
        elif penetration > 0.01:
            return 0.7
        else:
            return 0.5
    
    def get_bollinger_text(self, price, band_val, signal_type):
        """獲取布林通道信號文字"""
        penetration = abs(price - band_val) / band_val if band_val > 0 else 0
        
        if signal_type == 'buy':
            if penetration > 0.02:
                return '跌破下軌'
            elif penetration > 0.01:
                return '觸及下軌'
            else:
                return '下軌支撐'
        else:  # sell
            if penetration > 0.02:
                return '突破上軌'
            elif penetration > 0.01:
                return '觸及上軌'
            else:
                return '上軌阻力'
    
    def get_ma_strength(self, short_ma, long_ma, price, signal_type):
        """獲取移動平均交叉信號強度"""
        ma_gap = abs(short_ma - long_ma) / long_ma if long_ma > 0 else 0
        
        if signal_type == 'buy':
            price_above_short = price > short_ma
            if price_above_short and ma_gap > 0.02:
                return 0.9
            elif price_above_short and ma_gap > 0.01:
                return 0.7
            elif ma_gap > 0.015:
                return 0.6
            else:
                return 0.5
        else:  # sell
            price_below_short = price < short_ma
            if price_below_short and ma_gap > 0.02:
                return 0.9
            elif price_below_short and ma_gap > 0.01:
                return 0.7
            elif ma_gap > 0.015:
                return 0.6
            else:
                return 0.5
    
    def get_ma_text(self, short_ma, long_ma, signal_type):
        """獲取移動平均交叉信號文字"""
        ma_gap = abs(short_ma - long_ma) / long_ma if long_ma > 0 else 0
        
        if signal_type == 'buy':
            if ma_gap > 0.02:
                return '強勢金叉'
            elif ma_gap > 0.01:
                return '金叉放量'
            elif ma_gap > 0.015:
                return '急速金叉'
            else:
                return '金叉信號'
        else:  # sell
            if ma_gap > 0.02:
                return '強勢死叉'
            elif ma_gap > 0.01:
                return '死叉放量'
            elif ma_gap > 0.015:
                return '急速死叉'
            else:
                return '死叉信號'
    
    def analyze_all_strategies(self, data):
        """分析所有策略的信號"""
        all_signals = {}
        
        # 生成各策略信號（移除"策略"兩字）
        strategies = {
            'RSI': self.generate_rsi_signals,
            'MACD': self.generate_macd_signals,
            '布林通道': self.generate_bollinger_signals,
            '移動平均交叉': self.generate_ma_cross_signals
        }
        
        for strategy_name, generate_func in strategies.items():
            try:
                buy_signals, sell_signals = generate_func(data)
                all_signals[strategy_name] = {
                    'buy': buy_signals,
                    'sell': sell_signals
                }
            except Exception as e:
                print(f"生成{strategy_name}信號失敗: {e}")
                all_signals[strategy_name] = {'buy': [], 'sell': []}
        
        return all_signals

    def plot_multi_strategy_chart(self, data, all_signals, title="多策略整合分析"):
        """繪製多策略整合圖表"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12),
                                      gridspec_kw={'height_ratios': [3, 1]})

        # 主圖：價格曲線和信號
        ax1.plot(data.index, data['close'], 'k-', linewidth=1.5, label='收盤價', alpha=0.8)

        # 添加各策略信號
        legend_elements = []

        for strategy_name, signals in all_signals.items():
            color_config = self.signal_colors[strategy_name]
            marker_config = self.signal_markers[strategy_name]
            size = self.signal_sizes[strategy_name]

            # 買入信號
            if signals['buy']:
                buy_indices = [s['index'] for s in signals['buy']]
                buy_prices = [s['price'] for s in signals['buy']]
                buy_strengths = [s['strength'] for s in signals['buy']]

                # 根據強度調整透明度
                alphas = [0.5 + 0.5 * strength for strength in buy_strengths]

                scatter = ax1.scatter([data.index[i] for i in buy_indices], buy_prices,
                                    c=color_config['buy'], marker=marker_config['buy'],
                                    s=size, alpha=0.8, edgecolors='white', linewidth=1,
                                    label=f'{strategy_name}-買入', zorder=5)

                # 添加信號強度文字 - 智能位置避開曲線
                for i, signal in enumerate(signals['buy']):
                    if signal['strength'] >= 0.7:  # 只顯示強度較高的信號文字
                        # 計算智能文字位置
                        signal_idx = signal['index']
                        signal_price = signal['price']

                        # 檢查前後價格趨勢決定文字位置
                        if signal_idx > 5 and signal_idx < len(data) - 5:
                            prev_prices = data['close'].iloc[signal_idx-5:signal_idx].mean()
                            next_prices = data['close'].iloc[signal_idx:signal_idx+5].mean()

                            # 如果是上升趨勢，文字放在下方；下降趨勢放在上方
                            if next_prices > prev_prices:
                                text_offset = (10, -35)  # 下方
                                va_align = 'top'
                            else:
                                text_offset = (10, 35)   # 上方
                                va_align = 'bottom'
                        else:
                            text_offset = (10, 35)
                            va_align = 'bottom'

                        ax1.annotate(f"{strategy_name[:2]}\n{signal['text']}",
                                   xy=(data.index[signal_idx], signal_price),
                                   xytext=text_offset, textcoords='offset points',
                                   bbox=dict(boxstyle='round,pad=0.3',
                                           facecolor=color_config['buy'], alpha=0.8),
                                   fontsize=7, ha='left', va=va_align,
                                   arrowprops=dict(arrowstyle='->',
                                                 connectionstyle='arc3,rad=0.1',
                                                 alpha=0.6))

            # 賣出信號
            if signals['sell']:
                sell_indices = [s['index'] for s in signals['sell']]
                sell_prices = [s['price'] for s in signals['sell']]
                sell_strengths = [s['strength'] for s in signals['sell']]

                scatter = ax1.scatter([data.index[i] for i in sell_indices], sell_prices,
                                    c=color_config['sell'], marker=marker_config['sell'],
                                    s=size, alpha=0.8, edgecolors='white', linewidth=1,
                                    label=f'{strategy_name}-賣出', zorder=5)

                # 添加信號強度文字 - 智能位置避開曲線
                for i, signal in enumerate(signals['sell']):
                    if signal['strength'] >= 0.7:  # 只顯示強度較高的信號文字
                        # 計算智能文字位置
                        signal_idx = signal['index']
                        signal_price = signal['price']

                        # 檢查前後價格趨勢決定文字位置
                        if signal_idx > 5 and signal_idx < len(data) - 5:
                            prev_prices = data['close'].iloc[signal_idx-5:signal_idx].mean()
                            next_prices = data['close'].iloc[signal_idx:signal_idx+5].mean()

                            # 如果是下降趨勢，文字放在上方；上升趨勢放在下方
                            if next_prices < prev_prices:
                                text_offset = (10, 35)   # 上方
                                va_align = 'bottom'
                            else:
                                text_offset = (10, -35)  # 下方
                                va_align = 'top'
                        else:
                            text_offset = (10, -35)
                            va_align = 'top'

                        ax1.annotate(f"{strategy_name[:2]}\n{signal['text']}",
                                   xy=(data.index[signal_idx], signal_price),
                                   xytext=text_offset, textcoords='offset points',
                                   bbox=dict(boxstyle='round,pad=0.3',
                                           facecolor=color_config['sell'], alpha=0.8),
                                   fontsize=7, ha='left', va=va_align,
                                   arrowprops=dict(arrowstyle='->',
                                                 connectionstyle='arc3,rad=-0.1',
                                                 alpha=0.6))

        ax1.set_title(f'{title} - 四策略信號整合圖', fontsize=14, fontweight='bold')
        ax1.set_ylabel('價格', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

        # 下圖：信號統計
        self.plot_signal_statistics(ax2, all_signals, data)

        plt.tight_layout()
        return fig

    def plot_signal_statistics(self, ax, all_signals, data):
        """繪製信號統計圖"""
        strategies = list(all_signals.keys())
        buy_counts = [len(signals['buy']) for signals in all_signals.values()]
        sell_counts = [len(signals['sell']) for signals in all_signals.values()]

        x = np.arange(len(strategies))
        width = 0.35

        bars1 = ax.bar(x - width/2, buy_counts, width, label='買入信號',
                      color='lightgreen', alpha=0.7)
        bars2 = ax.bar(x + width/2, sell_counts, width, label='賣出信號',
                      color='lightcoral', alpha=0.7)

        # 添加數值標籤
        for bar in bars1:
            height = bar.get_height()
            ax.annotate(f'{int(height)}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3), textcoords="offset points",
                       ha='center', va='bottom', fontsize=10)

        for bar in bars2:
            height = bar.get_height()
            ax.annotate(f'{int(height)}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3), textcoords="offset points",
                       ha='center', va='bottom', fontsize=10)

        ax.set_xlabel('策略', fontsize=12)
        ax.set_ylabel('信號數量', fontsize=12)
        ax.set_title('各策略信號統計', fontsize=12, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels([s.replace('策略', '') for s in strategies], rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def analyze_signal_consensus(self, all_signals, data, window=5):
        """分析信號一致性"""
        consensus_points = []

        # 獲取所有信號點
        all_buy_points = {}
        all_sell_points = {}

        for strategy_name, signals in all_signals.items():
            all_buy_points[strategy_name] = [s['index'] for s in signals['buy']]
            all_sell_points[strategy_name] = [s['index'] for s in signals['sell']]

        # 尋找一致性信號
        for i in range(len(data)):
            buy_consensus = 0
            sell_consensus = 0

            for strategy_name in all_signals.keys():
                # 檢查窗口內是否有信號
                for buy_idx in all_buy_points[strategy_name]:
                    if abs(buy_idx - i) <= window:
                        buy_consensus += 1
                        break

                for sell_idx in all_sell_points[strategy_name]:
                    if abs(sell_idx - i) <= window:
                        sell_consensus += 1
                        break

            # 記錄高一致性點
            if buy_consensus >= 3:  # 至少3個策略同意買入
                consensus_points.append({
                    'index': i,
                    'type': 'buy',
                    'consensus': buy_consensus,
                    'price': data['close'].iloc[i]
                })
            elif sell_consensus >= 3:  # 至少3個策略同意賣出
                consensus_points.append({
                    'index': i,
                    'type': 'sell',
                    'consensus': sell_consensus,
                    'price': data['close'].iloc[i]
                })

        return consensus_points

    def create_signal_summary_table(self, all_signals):
        """創建信號摘要表"""
        summary_data = []

        for strategy_name, signals in all_signals.items():
            buy_count = len(signals['buy'])
            sell_count = len(signals['sell'])

            # 計算平均強度
            avg_buy_strength = np.mean([s['strength'] for s in signals['buy']]) if signals['buy'] else 0
            avg_sell_strength = np.mean([s['strength'] for s in signals['sell']]) if signals['sell'] else 0

            # 統計強度分布
            strong_buy = len([s for s in signals['buy'] if s['strength'] >= 0.7])
            strong_sell = len([s for s in signals['sell'] if s['strength'] >= 0.7])

            summary_data.append({
                '策略': strategy_name,
                '買入信號': buy_count,
                '賣出信號': sell_count,
                '強買入': strong_buy,
                '強賣出': strong_sell,
                '買入平均強度': f"{avg_buy_strength:.2f}",
                '賣出平均強度': f"{avg_sell_strength:.2f}"
            })

        return pd.DataFrame(summary_data)
