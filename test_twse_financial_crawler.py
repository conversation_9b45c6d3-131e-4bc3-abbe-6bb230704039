#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 TWSE 財務報表爬蟲功能
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_twse_financial_crawler():
    """測試 TWSE 財務報表爬蟲"""
    
    print("=" * 80)
    print("🧪 測試 TWSE 財務報表爬蟲功能")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        print("📦 導入爬蟲模組...")
        from crawler import crawl_twse_financial_statements
        print("✅ 成功導入 crawl_twse_financial_statements 函數")
        
        # 測試日期
        test_date = datetime.datetime.now()
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 執行爬蟲測試
        print(f"\n🚀 開始測試 TWSE 財務報表爬蟲...")
        result = crawl_twse_financial_statements(test_date)
        
        # 檢查結果
        if result and isinstance(result, dict):
            income_df = result.get('income_statements')
            balance_df = result.get('balance_sheets')
            
            print(f"✅ 爬蟲測試成功！")
            
            # 檢查綜合損益表
            if income_df is not None and len(income_df) > 0:
                print(f"📊 綜合損益表: {len(income_df)} 筆資料")
                print(f"📋 欄位數: {len(income_df.columns)}")
                print(f"📋 主要欄位: {list(income_df.columns[:10])}")
                
                # 顯示範例資料
                if 'stock_id' in income_df.columns and 'stock_name' in income_df.columns:
                    sample = income_df[['stock_id', 'stock_name', '營業收入', '營業毛利（毛損）']].head(3)
                    print(f"\n📊 綜合損益表範例:")
                    print(sample)
            else:
                print(f"⚠️ 綜合損益表無資料")
            
            # 檢查資產負債表
            if balance_df is not None and len(balance_df) > 0:
                print(f"\n📊 資產負債表: {len(balance_df)} 筆資料")
                print(f"📋 欄位數: {len(balance_df.columns)}")
                print(f"📋 主要欄位: {list(balance_df.columns[:10])}")
                
                # 顯示範例資料
                if 'stock_id' in balance_df.columns and 'stock_name' in balance_df.columns:
                    sample = balance_df[['stock_id', 'stock_name', '資產總額', '負債總額']].head(3)
                    print(f"\n📊 資產負債表範例:")
                    print(sample)
            else:
                print(f"⚠️ 資產負債表無資料")
            
            return True
            
        else:
            print(f"⚠️ 爬蟲測試返回異常結果")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        print(f"請確保 finlab/crawler.py 存在且包含 crawl_twse_financial_statements 函數")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_content():
    """測試資料庫內容"""
    
    print(f"\n📊 檢查資料庫內容...")
    
    try:
        import sqlite3
        import pandas as pd
        
        # 檢查綜合損益表資料庫
        income_db_path = os.path.join('history', 'tables', 'income_statements.db')
        if os.path.exists(income_db_path):
            print(f"✅ 找到綜合損益表資料庫: {income_db_path}")
            
            conn = sqlite3.connect(income_db_path)
            cursor = conn.cursor()
            
            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM income_statements")
            count = cursor.fetchone()[0]
            print(f"   📊 綜合損益表記錄數: {count:,}")
            
            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM income_statements")
            stock_count = cursor.fetchone()[0]
            print(f"   🏢 股票數量: {stock_count}")
            
            # 顯示範例資料
            cursor.execute("SELECT stock_id, stock_name, 營業收入, 營業毛利（毛損） FROM income_statements LIMIT 5")
            rows = cursor.fetchall()
            print(f"   📋 範例資料:")
            for row in rows:
                print(f"      {row[0]} {row[1]}: 營收={row[2]}, 毛利={row[3]}")
            
            conn.close()
        else:
            print(f"⚠️ 未找到綜合損益表資料庫: {income_db_path}")
        
        # 檢查資產負債表資料庫
        balance_db_path = os.path.join('history', 'tables', 'balance_sheets.db')
        if os.path.exists(balance_db_path):
            print(f"\n✅ 找到資產負債表資料庫: {balance_db_path}")
            
            conn = sqlite3.connect(balance_db_path)
            cursor = conn.cursor()
            
            # 檢查資料筆數
            cursor.execute("SELECT COUNT(*) FROM balance_sheets")
            count = cursor.fetchone()[0]
            print(f"   📊 資產負債表記錄數: {count:,}")
            
            # 檢查股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM balance_sheets")
            stock_count = cursor.fetchone()[0]
            print(f"   🏢 股票數量: {stock_count}")
            
            # 顯示範例資料
            cursor.execute("SELECT stock_id, stock_name, 資產總額, 負債總額 FROM balance_sheets LIMIT 5")
            rows = cursor.fetchall()
            print(f"   📋 範例資料:")
            for row in rows:
                print(f"      {row[0]} {row[1]}: 資產={row[2]}, 負債={row[3]}")
            
            conn.close()
        else:
            print(f"⚠️ 未找到資產負債表資料庫: {balance_db_path}")
        
        # 檢查合併資料庫
        combined_db_path = os.path.join('history', 'tables', 'financial_statements.db')
        if os.path.exists(combined_db_path):
            print(f"\n✅ 找到合併財務資料庫: {combined_db_path}")
            
            conn = sqlite3.connect(combined_db_path)
            cursor = conn.cursor()
            
            # 檢查表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"   📋 包含表格: {[table[0] for table in tables]}")
            
            conn.close()
        else:
            print(f"⚠️ 未找到合併財務資料庫: {combined_db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查資料庫內容失敗: {e}")
        return False

def demonstrate_usage():
    """演示使用方法"""
    
    print(f"\n📝 TWSE 財務報表爬蟲使用方法")
    print("=" * 60)
    
    print("🚀 啟動方式:")
    print("   1. 單獨執行 TWSE 財務報表爬蟲:")
    print("      python auto_update.py twse_financial_statements")
    print()
    print("   2. 執行完整自動更新 (包含 TWSE 財務報表):")
    print("      python auto_update.py")
    print()
    
    print("📊 資料來源:")
    print("   - 台灣證交所官方 OpenAPI")
    print("   - 基礎 URL: https://openapi.twse.com.tw/v1")
    print("   - 綜合損益表: /opendata/t187ap06_L_ci")
    print("   - 資產負債表: /opendata/t187ap07_L_ci")
    print()
    
    print("💾 資料儲存:")
    print("   - 綜合損益表: history/tables/income_statements.db")
    print("   - 資產負債表: history/tables/balance_sheets.db")
    print("   - 合併資料: history/tables/financial_statements.db")
    print()
    
    print("🔍 查詢範例:")
    print("   -- 查詢台積電綜合損益表")
    print("   SELECT * FROM income_statements WHERE stock_id = '2330';")
    print()
    print("   -- 查詢台積電資產負債表")
    print("   SELECT * FROM balance_sheets WHERE stock_id = '2330';")
    print()
    print("   -- 查詢營收前10名")
    print("   SELECT stock_id, stock_name, 營業收入")
    print("   FROM income_statements")
    print("   ORDER BY CAST(營業收入 AS INTEGER) DESC LIMIT 10;")

def main():
    """主函數"""
    
    print("🧪 TWSE 財務報表爬蟲測試")
    
    # 測試1: 爬蟲功能
    test1_result = test_twse_financial_crawler()
    
    # 測試2: 資料庫內容
    test2_result = test_database_content()
    
    # 演示使用方法
    demonstrate_usage()
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 測試結果總結")
    print(f"=" * 80)
    
    if test1_result and test2_result:
        print(f"🎉 所有測試通過！TWSE 財務報表爬蟲已準備就緒")
        print(f"\n💡 建議下一步:")
        print(f"   1. 執行: python auto_update.py twse_financial_statements")
        print(f"   2. 使用 SQL 查詢分析財務資料")
        print(f"   3. 整合到投資策略分析中")
    elif test1_result:
        print(f"⚠️ 爬蟲功能正常，但資料庫檢查有問題")
        print(f"💡 建議: 重新執行爬蟲確保資料正確儲存")
    else:
        print(f"❌ 爬蟲功能有問題，請檢查相關配置")
    
    return test1_result and test2_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
