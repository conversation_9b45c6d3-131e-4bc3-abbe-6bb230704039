#!/usr/bin/env python3
"""
開盤前監控模組
從原始程式中提取的PreMarketMonitor類別
"""
import os
import sys
import json
import logging
import time
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

import pandas as pd
import numpy as np

# 檢查可選依賴
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    logging.warning("⚠️ requests模組未安裝，部分API功能將被停用")

try:
    from apscheduler.schedulers.background import BackgroundScheduler
    SCHEDULER_AVAILABLE = True
except ImportError:
    SCHEDULER_AVAILABLE = False
    logging.warning("⚠️ apscheduler模組未安裝，定時功能將被停用")

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    logging.warning("⚠️ yfinance模組未安裝，部分數據源將被停用")

class PreMarketMonitor:
    """每日開盤前監控與資料擷取 SOP - 整合免費API"""

    def __init__(self):
        self.name = "開盤前監控系統"
        self.version = "3.0"
        self.last_update = None
        self.data_cache = {}
        self.scheduler = None

        # API 請求緩存和限制 - 增加冷卻時間避免頻率限制
        self.api_cache = {}
        self.last_api_call = {}
        self.api_cooldown = 5  # 增加到5秒避免頻率限制
        self.rate_limit_detected = False  # 追蹤是否遇到頻率限制

        # 監控配置
        self.config = {
            'us_indices': {'^GSPC': 'S&P500', '^DJI': 'DowJones', '^IXIC': 'Nasdaq'},
            'asia_indices': {'^N225': '日經225', '^HSI': '恆生指數', '^KS11': '韓國綜合'},
            'commodities': {'CL=F': 'WTI原油', 'GC=F': '黃金期貨', 'SI=F': '白銀期貨'},
            'currencies': {'USDTWD=X': 'USD/TWD', 'EURUSD=X': 'EUR/USD', 'USDJPY=X': 'USD/JPY'},
            'crypto': {'BTC-USD': '比特幣', 'ETH-USD': '以太幣'}
        }

        # 🛡️ 反爬蟲機制 - 參考GoodInfo和Yahoo技術
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]

        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

        self.last_request_time = 0
        self.min_request_interval = 2.0  # 最小請求間隔2秒

        # 免費API配置 - 增強版session
        self.free_apis_enabled = True
        try:
            import requests
            self.session = requests.Session()
            # 初始化隨機headers
            self._update_session_headers()
            # 初始化cookies
            self._init_session_cookies()
        except ImportError:
            self.session = None
            self.free_apis_enabled = False

    def _update_session_headers(self):
        """🛡️ 更新session headers - 隨機User-Agent"""
        import random
        if self.session:
            headers = self.base_headers.copy()
            headers['User-Agent'] = random.choice(self.user_agents)
            self.session.headers.update(headers)

    def _init_session_cookies(self):
        """🛡️ 初始化session cookies - 模擬真實瀏覽器"""
        import random
        from datetime import datetime
        if self.session:
            try:
                basic_cookies = {
                    'IS_TOUCH_DEVICE': 'F',
                    'SCREEN_SIZE': 'WIDTH=1920&HEIGHT=1080',
                    'CLIENT_ID': f'{datetime.now().strftime("%Y%m%d%H%M%S")}_{random.randint(100,999)}',
                }
                for name, value in basic_cookies.items():
                    self.session.cookies.set(name, value)
            except Exception as e:
                logging.debug(f"⚠️ Cookies 初始化警告: {e}")

    def _make_safe_request(self, url, **kwargs):
        """🛡️ 安全請求方法 - 包含完整反爬蟲機制"""
        import time
        import random

        # 檢查請求頻率
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            logging.debug(f"🛡️ 請求頻率控制，等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)

        # 隨機延遲（1-3秒）
        random_delay = random.uniform(1, 3)
        logging.debug(f"🛡️ 隨機延遲 {random_delay:.2f} 秒")
        time.sleep(random_delay)

        # 更新隨機headers
        self._update_session_headers()

        try:
            response = self.session.get(url, timeout=15, **kwargs)

            # 更新最後請求時間
            self.last_request_time = time.time()

            # 檢查是否遇到頻率限制
            if response.status_code == 429:
                self.rate_limit_detected = True
                logging.warning(f"⚠️ 遇到頻率限制 (429)，增加延遲時間")
                time.sleep(10)  # 額外等待10秒
                return None
            elif "Rate limited" in response.text or "Too Many Requests" in response.text:
                self.rate_limit_detected = True
                logging.warning(f"⚠️ 檢測到頻率限制訊息，增加延遲時間")
                time.sleep(10)
                return None

            # 設定正確的編碼
            if 'charset' in response.headers.get('content-type', ''):
                response.encoding = response.apparent_encoding
            else:
                response.encoding = 'utf-8'

            return response

        except Exception as e:
            logging.debug(f"❌ 安全請求失敗: {e}")
            return None

    def _safe_api_call(self, symbol, cache_duration=300, retry_count=3):
        """🛡️ 安全的API調用，包含緩存、限制機制和反爬蟲技術"""
        import time
        from datetime import datetime, timedelta

        current_time = time.time()

        # 檢查緩存
        if symbol in self.api_cache:
            cache_time, cache_data = self.api_cache[symbol]
            if current_time - cache_time < cache_duration:
                logging.debug(f"📋 使用緩存數據: {symbol}")
                return cache_data

        # 如果檢測到頻率限制，增加冷卻時間
        if self.rate_limit_detected:
            extended_cooldown = self.api_cooldown * 3  # 延長到15秒
            logging.info(f"🛡️ 頻率限制模式，延長冷卻時間到 {extended_cooldown} 秒")
        else:
            extended_cooldown = self.api_cooldown

        # 檢查API調用間隔
        if symbol in self.last_api_call:
            time_since_last = current_time - self.last_api_call[symbol]
            if time_since_last < extended_cooldown:
                sleep_time = extended_cooldown - time_since_last
                logging.debug(f"⏳ API限制，等待 {sleep_time:.1f} 秒...")
                time.sleep(sleep_time)

        # 重試機制
        for attempt in range(retry_count):
            try:
                # 執行API調用
                if YFINANCE_AVAILABLE:
                    # 添加延遲避免頻率限制
                    if attempt > 0:
                        delay = 2 ** attempt  # 指數退避
                        logging.debug(f"🛡️ 重試 {attempt + 1}，等待 {delay} 秒...")
                        time.sleep(delay)

                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="2d")

                    if not hist.empty:
                        # 更新緩存和調用時間
                        self.api_cache[symbol] = (current_time, hist)
                        self.last_api_call[symbol] = current_time
                        # 重置頻率限制標記
                        if self.rate_limit_detected:
                            self.rate_limit_detected = False
                            logging.info(f"✅ 頻率限制已解除")
                        return hist
                    else:
                        if attempt < retry_count - 1:
                            logging.debug(f"⚠️ {symbol} 返回空數據，重試中...")
                            continue
                        else:
                            logging.warning(f"⚠️ {symbol} 返回空數據")
                            return None
                else:
                    logging.warning("⚠️ yfinance 不可用")
                    return None

            except Exception as e:
                error_msg = str(e)
                if "429" in error_msg or "Rate limited" in error_msg or "Too Many Requests" in error_msg:
                    self.rate_limit_detected = True
                    logging.warning(f"⚠️ 檢測到頻率限制: {symbol}")
                    if attempt < retry_count - 1:
                        wait_time = 10 * (attempt + 1)  # 遞增等待時間
                        logging.info(f"🛡️ 等待 {wait_time} 秒後重試...")
                        time.sleep(wait_time)
                        continue

                if attempt < retry_count - 1:
                    logging.debug(f"⚠️ API調用失敗 {symbol} (嘗試 {attempt + 1}): {e}")
                    time.sleep(1)  # 短暫延遲後重試
                else:
                    logging.warning(f"⚠️ API調用最終失敗 {symbol}: {e}")

        return None
    
    def get_us_indices(self):
        """獲取美股三大指數 - 真實數據版本"""
        try:
            results = {}

            # 優先使用 yfinance 獲取真實數據
            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取美股指數真實數據")

                symbols = {'^GSPC': 'S&P500', '^DJI': 'DowJones', '^IXIC': 'Nasdaq'}

                for symbol, name in symbols.items():
                    try:
                        hist = self._safe_api_call(symbol)

                        if hist is not None and not hist.empty and len(hist) >= 2:
                            current_price = hist['Close'].iloc[-1]
                            prev_price = hist['Close'].iloc[-2]
                            change = current_price - prev_price
                            change_pct = (change / prev_price) * 100

                            results[name] = {
                                'price': round(current_price, 2),
                                'change': round(change, 2),
                                'change_pct': round(change_pct, 2),
                                'status': '真實數據'
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: {current_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            # 數據不足，跳過此項目
                            logging.warning(f"跳過 {name} - 數據不足")
                            continue

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                return results

            else:
                # 沒有 yfinance，使用智能模擬數據
                logging.info("📊 yfinance 不可用，使用智能模擬數據")
                return self._get_intelligent_us_simulation()

        except Exception as e:
            logging.error(f"獲取美股指數失敗: {e}")
            return self._get_intelligent_us_simulation()

    def _get_fallback_us_data(self, name):
        """獲取美股指數的備用數據"""
        import random
        base_prices = {'S&P500': 5800.0, 'DowJones': 43000.0, 'Nasdaq': 18500.0}
        base_price = base_prices.get(name, 1000.0)

        # 生成合理的市場波動 (-0.8% 到 +0.8%)
        change_pct = random.uniform(-0.8, 0.8)
        change = base_price * (change_pct / 100)
        current_price = base_price + change

        return {
            'price': round(current_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'status': '模擬數據'
        }

    def _get_intelligent_us_simulation(self):
        """智能美股模擬數據"""
        import random
        from datetime import datetime

        # 根據時間調整市場情緒
        hour = datetime.now().hour

        # 美股交易時間調整波動性
        if 21 <= hour <= 23 or 0 <= hour <= 4:  # 美股交易時間 (台灣時間)
            volatility = 1.2  # 交易時間波動較大
        else:
            volatility = 0.6  # 非交易時間波動較小

        base_prices = {'S&P500': 5800.0, 'DowJones': 43000.0, 'Nasdaq': 18500.0}
        results = {}

        for name, base_price in base_prices.items():
            change_pct = random.uniform(-volatility, volatility)
            change = base_price * (change_pct / 100)
            current_price = base_price + change

            results[name] = {
                'price': round(current_price, 2),
                'change': round(change, 2),
                'change_pct': round(change_pct, 2),
                'status': '模擬數據'  # 明確標記為模擬數據
            }

        return results
    
    def get_asia_indices(self):
        """獲取亞洲主要指數 - 真實數據版本"""
        try:
            results = {}

            # 優先使用 yfinance 獲取真實數據
            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取亞洲指數真實數據")

                symbols = {'^N225': '日經225', '^HSI': '恆生指數', '^KS11': '韓國綜合'}

                for symbol, name in symbols.items():
                    try:
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(period="2d")

                        if not hist.empty and len(hist) >= 2:
                            current_price = hist['Close'].iloc[-1]
                            prev_price = hist['Close'].iloc[-2]
                            change = current_price - prev_price
                            change_pct = (change / prev_price) * 100

                            results[name] = {
                                'price': round(current_price, 2),
                                'change': round(change, 2),
                                'change_pct': round(change_pct, 2),
                                'status': '真實數據'
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: {current_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            logging.warning(f"跳過 {name} - 數據不足")
                            continue

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                return results

            else:
                # 沒有 yfinance，使用智能模擬數據
                logging.info("📊 yfinance 不可用，使用智能模擬數據")
                return self._get_intelligent_asia_simulation()

        except Exception as e:
            logging.error(f"獲取亞洲指數失敗: {e}")
            return self._get_intelligent_asia_simulation()

    def _get_fallback_asia_data(self, name):
        """獲取亞洲指數的備用數據"""
        import random
        base_prices = {'日經225': 39000.0, '恆生指數': 16500.0, '韓國綜合': 2500.0}
        base_price = base_prices.get(name, 1000.0)

        # 亞洲市場波動通常較大
        change_pct = random.uniform(-1.2, 1.2)
        change = base_price * (change_pct / 100)
        current_price = base_price + change

        return {
            'price': round(current_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'status': '模擬數據'
        }

    def _get_intelligent_asia_simulation(self):
        """智能亞洲指數模擬數據"""
        import random
        from datetime import datetime

        # 根據時間調整市場情緒
        hour = datetime.now().hour

        # 亞洲交易時間調整
        if 9 <= hour <= 15:  # 亞洲交易時間
            volatility = 1.0
        else:
            volatility = 0.7

        base_prices = {'日經225': 39000.0, '恆生指數': 16500.0, '韓國綜合': 2500.0}
        results = {}

        for name, base_price in base_prices.items():
            change_pct = random.uniform(-volatility, volatility)
            change = base_price * (change_pct / 100)
            current_price = base_price + change

            results[name] = {
                'price': round(current_price, 2),
                'change': round(change, 2),
                'change_pct': round(change_pct, 2),
                'status': '模擬數據'  # 明確標記為模擬數據
            }

        return results
    
    def get_commodity_prices(self):
        """獲取大宗商品價格 - 真實數據版本"""
        try:
            results = {}

            # 優先使用 yfinance 獲取真實數據
            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取商品價格真實數據")

                symbols = {'CL=F': 'WTI原油', 'GC=F': '黃金期貨', 'SI=F': '白銀期貨'}

                for symbol, name in symbols.items():
                    try:
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(period="2d")

                        if not hist.empty and len(hist) >= 2:
                            current_price = hist['Close'].iloc[-1]
                            prev_price = hist['Close'].iloc[-2]
                            change = current_price - prev_price
                            change_pct = (change / prev_price) * 100

                            results[name] = {
                                'price': round(current_price, 2),
                                'change': round(change, 2),
                                'change_pct': round(change_pct, 2),
                                'status': '真實數據'
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: ${current_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            logging.warning(f"跳過 {name} - 數據不足")
                            continue

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                return results

            else:
                # 沒有 yfinance，使用智能模擬數據
                logging.info("📊 yfinance 不可用，使用智能模擬數據")
                return self._get_intelligent_commodity_simulation()

        except Exception as e:
            logging.error(f"獲取商品價格失敗: {e}")
            return self._get_intelligent_commodity_simulation()

    def _get_fallback_commodity_data(self, name):
        """獲取商品的備用數據"""
        import random
        base_prices = {'WTI原油': 75.0, '黃金期貨': 2050.0, '白銀期貨': 24.0}
        base_price = base_prices.get(name, 100.0)

        # 商品波動通常較大
        change_pct = random.uniform(-1.5, 1.5)
        change = base_price * (change_pct / 100)
        current_price = base_price + change

        return {
            'price': round(current_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'status': '模擬數據'
        }

    def _get_intelligent_commodity_simulation(self):
        """智能商品價格模擬數據"""
        import random

        # 商品市場24小時交易，波動性較高
        base_prices = {'WTI原油': 75.0, '黃金期貨': 2050.0, '白銀期貨': 24.0}
        results = {}

        for name, base_price in base_prices.items():
            # 商品波動較大
            change_pct = random.uniform(-1.8, 1.8)
            change = base_price * (change_pct / 100)
            current_price = base_price + change

            results[name] = {
                'price': round(current_price, 2),
                'change': round(change, 2),
                'change_pct': round(change_pct, 2),
                'status': '模擬數據'
            }

        return results
    
    def get_fx_rates(self):
        """獲取匯率資訊 - 多重數據源驗證版"""
        # 1. 優先嘗試免費API (更穩定)
        if self.free_apis_enabled and self.session:
            fx_data = self._get_forex_free_api()
            if fx_data and self._validate_fx_rates(fx_data):
                logging.info("✅ 使用免費API獲取並驗證匯率數據")
                return fx_data
            elif fx_data:
                logging.warning("⚠️ 免費API匯率數據異常，嘗試備用數據源")

        # 2. 嘗試 yfinance 作為備用
        try:
            results = {}

            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取匯率真實數據")

                symbols = {'USDTWD=X': 'USD/TWD', 'EURUSD=X': 'EUR/USD', 'USDJPY=X': 'USD/JPY'}

                for symbol, name in symbols.items():
                    try:
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(period="2d")

                        if not hist.empty and len(hist) >= 2:
                            current_rate = hist['Close'].iloc[-1]
                            prev_rate = hist['Close'].iloc[-2]
                            change = current_rate - prev_rate
                            change_pct = (change / prev_rate) * 100

                            results[name] = {
                                'rate': round(current_rate, 4),
                                'change': round(change, 4),
                                'change_pct': round(change_pct, 2),
                                'status': 'yfinance實時',
                                'source': 'yfinance',
                                'timestamp': datetime.now().strftime('%H:%M:%S')
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: {current_rate:.4f} ({change_pct:+.2f}%)")

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                # 驗證yfinance數據
                if results and self._validate_fx_rates(results):
                    logging.info(f"✅ yfinance 獲取並驗證 {len(results)} 個匯率")
                    return results
                elif results:
                    logging.warning("⚠️ yfinance匯率數據異常，跳過顯示")

            else:
                logging.warning("📊 yfinance 不可用，且無免費API數據，跳過外匯顯示")

        except Exception as e:
            logging.error(f"獲取匯率失敗: {e}")

        return {}

    def _validate_fx_rates(self, fx_data):
        """驗證匯率數據的合理性"""
        try:
            # 定義合理的匯率範圍
            reasonable_ranges = {
                'USD/TWD': (25.0, 35.0),  # 美元對台幣合理範圍
                'EUR/USD': (0.8, 1.3),    # 歐元對美元合理範圍
                'USD/JPY': (100.0, 180.0) # 美元對日圓合理範圍
            }

            for name, data in fx_data.items():
                if name in reasonable_ranges:
                    rate = data.get('rate', 0)
                    min_rate, max_rate = reasonable_ranges[name]

                    if not (min_rate <= rate <= max_rate):
                        logging.warning(f"⚠️ {name} 匯率 {rate} 超出合理範圍 [{min_rate}, {max_rate}]")
                        return False
                    else:
                        logging.info(f"✅ {name} 匯率 {rate} 在合理範圍內")

            return True

        except Exception as e:
            logging.error(f"匯率驗證失敗: {e}")
            return False

    def _get_forex_free_api(self):
        """使用多個免費API獲取外匯數據 - 優化版"""
        try:
            # 方法1: 使用 exchangerate-api.com 免費API (無需API key)
            url = "https://api.exchangerate-api.com/v4/latest/USD"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                results = {}

                if 'rates' in data:
                    rates = data['rates']
                    logging.info(f"📊 ExchangeRate-API 獲取匯率數據: USD基準")

                    # USD/TWD - 台幣匯率
                    if 'TWD' in rates:
                        usd_twd_rate = rates['TWD']
                        results['USD/TWD'] = {
                            'rate': round(usd_twd_rate, 4),
                            'change': 0,  # 免費API通常不提供變化數據
                            'change_pct': 0,
                            'status': f'ExchangeRate-API實時 ({usd_twd_rate:.4f})',
                            'source': 'exchangerate_api',
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        }
                        logging.info(f"💱 USD/TWD: {usd_twd_rate:.4f}")

                    # EUR/USD (需要計算)
                    if 'EUR' in rates:
                        eur_usd_rate = 1 / rates['EUR']
                        results['EUR/USD'] = {
                            'rate': round(eur_usd_rate, 4),
                            'change': 0,
                            'change_pct': 0,
                            'status': f'ExchangeRate-API實時 ({eur_usd_rate:.4f})',
                            'source': 'exchangerate_api',
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        }
                        logging.info(f"💱 EUR/USD: {eur_usd_rate:.4f}")

                    # USD/JPY
                    if 'JPY' in rates:
                        usd_jpy_rate = rates['JPY']
                        results['USD/JPY'] = {
                            'rate': round(usd_jpy_rate, 4),
                            'change': 0,
                            'change_pct': 0,
                            'status': f'ExchangeRate-API實時 ({usd_jpy_rate:.4f})',
                            'source': 'exchangerate_api',
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        }
                        logging.info(f"💱 USD/JPY: {usd_jpy_rate:.4f}")

                if results:
                    logging.info(f"✅ ExchangeRate-API 成功獲取 {len(results)} 個匯率")
                    return results

        except Exception as e:
            logging.warning(f"ExchangeRate-API 失敗: {e}")

        # 方法2: 嘗試備用API - Fixer.io 免費版
        try:
            backup_url = "http://data.fixer.io/api/latest?access_key=free&base=USD&symbols=TWD,EUR,JPY"
            response = self.session.get(backup_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'rates' in data:
                    rates = data['rates']
                    results = {}

                    if 'TWD' in rates:
                        results['USD/TWD'] = {
                            'rate': round(rates['TWD'], 4),
                            'change': 0,
                            'change_pct': 0,
                            'status': 'Fixer.io實時',
                            'source': 'fixer_io'
                        }

                    if results:
                        logging.info(f"✅ Fixer.io 備用API 獲取 {len(results)} 個匯率")
                        return results

        except Exception as e:
            logging.warning(f"Fixer.io 備用API 失敗: {e}")

        return {}

    def _get_fallback_fx_data(self, name):
        """獲取匯率的備用數據"""
        import random
        base_rates = {'USD/TWD': 31.5, 'EUR/USD': 1.08, 'USD/JPY': 150.0}
        base_rate = base_rates.get(name, 1.0)

        # 匯率波動通常較小
        change_pct = random.uniform(-0.3, 0.3)
        change = base_rate * (change_pct / 100)
        current_rate = base_rate + change

        return {
            'rate': round(current_rate, 4),
            'change': round(change, 4),
            'change_pct': round(change_pct, 2),
            'status': '模擬數據'
        }

    def _get_intelligent_fx_simulation(self):
        """智能匯率模擬數據"""
        import random

        base_rates = {'USD/TWD': 31.5, 'EUR/USD': 1.08, 'USD/JPY': 150.0}
        results = {}

        for name, base_rate in base_rates.items():
            # 匯率波動較小
            change_pct = random.uniform(-0.4, 0.4)
            change = base_rate * (change_pct / 100)
            current_rate = base_rate + change

            results[name] = {
                'rate': round(current_rate, 4),
                'change': round(change, 4),
                'change_pct': round(change_pct, 2),
                'status': '模擬數據'
            }

        return results
    
    def get_taiwan_futures(self):
        """獲取台指期資訊 - 真實數據版本"""
        try:
            results = {}

            # 優先使用 yfinance 獲取台灣相關數據
            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取台灣市場真實數據")

                # 使用多個台灣指標
                symbols = {
                    '0050.TW': '台灣50ETF',
                    '^TWII': '台股加權指數',
                    '2330.TW': '台積電'
                }

                for symbol, name in symbols.items():
                    try:
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(period="2d")

                        if not hist.empty and len(hist) >= 1:
                            current_price = hist['Close'].iloc[-1]
                            prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
                            change = current_price - prev_price
                            change_pct = (change / prev_price * 100) if prev_price != 0 else 0
                            volume = hist['Volume'].iloc[-1] if 'Volume' in hist.columns else 0

                            results[name] = {
                                'price': round(current_price, 2),
                                'change': round(change, 2),
                                'change_pct': round(change_pct, 2),
                                'volume': int(volume) if volume > 0 else 0,
                                'status': '真實數據'
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: {current_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            logging.warning(f"跳過 {name} - 數據不足")
                            continue

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                return results

            else:
                # 沒有 yfinance，使用智能模擬數據
                logging.info("📊 yfinance 不可用，使用智能模擬數據")
                return self._get_intelligent_taiwan_simulation()

        except Exception as e:
            logging.error(f"獲取台灣市場數據失敗: {e}")
            return self._get_intelligent_taiwan_simulation()

    def _get_fallback_taiwan_data(self, name):
        """獲取台灣市場的備用數據"""
        import random
        base_prices = {
            '台灣50ETF': 140.0,
            '台股加權指數': 17500.0,
            '台積電': 580.0
        }
        base_price = base_prices.get(name, 100.0)

        # 台股波動
        change_pct = random.uniform(-1.0, 1.0)
        change = base_price * (change_pct / 100)
        current_price = base_price + change

        return {
            'price': round(current_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'volume': random.randint(10000, 100000),
            'status': '模擬數據'
        }

    def _get_intelligent_taiwan_simulation(self):
        """智能台灣市場模擬數據"""
        import random
        from datetime import datetime

        # 根據時間調整市場情緒
        hour = datetime.now().hour

        # 台股交易時間調整
        if 9 <= hour <= 13:  # 台股交易時間
            volatility = 0.8
        else:
            volatility = 0.5

        base_prices = {
            '台灣50ETF': 140.0,
            '台股加權指數': 17500.0,
            '台積電': 580.0
        }
        results = {}

        for name, base_price in base_prices.items():
            change_pct = random.uniform(-volatility, volatility)
            change = base_price * (change_pct / 100)
            current_price = base_price + change

            results[name] = {
                'price': round(current_price, 2),
                'change': round(change, 2),
                'change_pct': round(change_pct, 2),
                'volume': random.randint(10000, 100000),
                'status': '模擬數據'
            }

        return results
    
    def get_crypto_prices(self):
        """獲取加密貨幣價格 - 優先使用免費API"""
        # 1. 嘗試免費API (CoinGecko)
        if self.free_apis_enabled and self.session:
            crypto_data = self._get_crypto_coingecko()
            if crypto_data:
                return crypto_data

        # 2. 備用 yfinance
        try:
            results = {}

            # 優先使用 yfinance 獲取真實數據
            if YFINANCE_AVAILABLE:
                logging.info("📊 使用 yfinance 獲取加密貨幣真實數據")

                symbols = {'BTC-USD': '比特幣', 'ETH-USD': '以太幣'}

                for symbol, name in symbols.items():
                    try:
                        ticker = yf.Ticker(symbol)
                        hist = ticker.history(period="2d")

                        if not hist.empty and len(hist) >= 2:
                            current_price = hist['Close'].iloc[-1]
                            prev_price = hist['Close'].iloc[-2]
                            change = current_price - prev_price
                            change_pct = (change / prev_price) * 100

                            results[name] = {
                                'price': round(current_price, 2),
                                'change': round(change, 2),
                                'change_pct': round(change_pct, 2),
                                'status': '真實數據'
                            }
                            logging.info(f"✅ 獲取 {name} 真實數據: ${current_price:.2f} ({change_pct:+.2f}%)")
                        else:
                            logging.warning(f"跳過 {name} - 數據不足")
                            continue

                    except Exception as e:
                        logging.warning(f"獲取 {name} 數據失敗: {e}")
                        continue

                return results

            else:
                # 沒有 yfinance，使用智能模擬數據
                logging.info("📊 yfinance 不可用，使用智能模擬數據")
                return self._get_intelligent_crypto_simulation()

        except Exception as e:
            logging.error(f"獲取加密貨幣價格失敗: {e}")
            return self._get_intelligent_crypto_simulation()

    def _get_crypto_coingecko(self):
        """使用 CoinGecko 免費API獲取加密貨幣數據"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': 'bitcoin,ethereum',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true'
            }

            response = self.session.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                results = {}

                if 'bitcoin' in data:
                    btc_data = data['bitcoin']
                    results['比特幣'] = {
                        'price': btc_data['usd'],
                        'change_pct': btc_data.get('usd_24h_change', 0),
                        'status': 'CoinGecko實時',
                        'source': 'coingecko'
                    }

                if 'ethereum' in data:
                    eth_data = data['ethereum']
                    results['以太幣'] = {
                        'price': eth_data['usd'],
                        'change_pct': eth_data.get('usd_24h_change', 0),
                        'status': 'CoinGecko實時',
                        'source': 'coingecko'
                    }

                if results:
                    logging.info(f"✅ CoinGecko 獲取 {len(results)} 個加密貨幣")
                    return results

        except Exception as e:
            logging.warning(f"CoinGecko API 失敗: {e}")

        return {}

    def _get_fallback_crypto_data(self, name):
        """獲取加密貨幣的備用數據"""
        import random
        base_prices = {'比特幣': 95000.0, '以太幣': 3500.0}
        base_price = base_prices.get(name, 1000.0)

        # 加密貨幣波動很大
        change_pct = random.uniform(-3.0, 3.0)
        change = base_price * (change_pct / 100)
        current_price = base_price + change

        return {
            'price': round(current_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 2),
            'status': '模擬數據'
        }

    def _get_intelligent_crypto_simulation(self):
        """智能加密貨幣模擬數據"""
        import random

        # 加密貨幣24小時交易，波動性極高
        base_prices = {'比特幣': 95000.0, '以太幣': 3500.0}
        results = {}

        for name, base_price in base_prices.items():
            # 加密貨幣波動極大
            change_pct = random.uniform(-4.0, 4.0)
            change = base_price * (change_pct / 100)
            current_price = base_price + change

            results[name] = {
                'price': round(current_price, 2),
                'change': round(change, 2),
                'change_pct': round(change_pct, 2),
                'status': '模擬數據'
            }

        return results
    
    def get_economic_calendar(self):
        """獲取重要經濟事件（模擬數據）"""
        try:
            # 這裡可以接入經濟日曆API，目前提供示例數據
            events = [
                {'time': '09:30', 'event': '台股開盤', 'importance': '高'},
                {'time': '14:30', 'event': '美國CPI數據發布', 'importance': '高'},
                {'time': '20:00', 'event': 'Fed官員講話', 'importance': '中'},
            ]
            return events
        except Exception as e:
            logging.error(f"獲取經濟日曆失敗: {e}")
            return []
    
    def run_full_scan(self):
        """執行完整的開盤前掃描 - 只有真實數據才顯示"""
        try:
            logging.info("🔍 開始執行開盤前市場掃描...")

            # 先測試是否能獲取真實數據或免費API數據
            test_data = self.get_us_indices()
            has_real_data = any(data.get('status', '') == '真實數據' for data in test_data.values())

            # 如果沒有真實數據，檢查是否有免費API數據
            if not has_real_data:
                crypto_test = self.get_crypto_prices()
                has_free_api_data = any('CoinGecko' in data.get('status', '') for data in crypto_test.values())

                if not has_free_api_data:
                    logging.warning("⚠️ 無法獲取真實市場數據或免費API數據，跳過開盤前監控")
                    return None
                else:
                    logging.info("✅ 檢測到免費API數據，繼續執行監控")

            scan_results = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'us_indices': self.get_us_indices(),
                'asia_indices': self.get_asia_indices(),
                'commodities': self.get_commodity_prices(),
                'fx_rates': self.get_fx_rates(),
                'taiwan_futures': self.get_taiwan_futures(),
                'crypto': self.get_crypto_prices(),
                'economic_events': self.get_economic_calendar()
            }

            # 再次檢查數據品質 (包含免費API數據)
            total_items = 0
            real_data_items = 0
            free_api_items = 0

            for category, data in scan_results.items():
                if category == 'timestamp' or category == 'economic_events':
                    continue
                if isinstance(data, dict):
                    for item_data in data.values():
                        if isinstance(item_data, dict) and 'status' in item_data:
                            total_items += 1
                            status = item_data['status']
                            if '真實數據' in status:
                                real_data_items += 1
                            elif any(api in status for api in ['CoinGecko', 'Finnhub', 'FMP', 'Alpha Vantage']):
                                free_api_items += 1

            quality_data_items = real_data_items + free_api_items
            quality_data_ratio = quality_data_items / total_items if total_items > 0 else 0

            # 如果高品質數據比例低於10%，不顯示結果 (降低門檻以支援免費API)
            if quality_data_ratio < 0.1:
                logging.warning(f"⚠️ 高品質數據比例過低 ({quality_data_ratio:.1%})，跳過開盤前監控")
                return None

            self.data_cache = scan_results
            self.last_update = datetime.now()

            logging.info(f"✅ 開盤前市場掃描完成 (高品質數據比例: {quality_data_ratio:.1%}, 真實數據: {real_data_items}, 免費API: {free_api_items})")
            return scan_results

        except Exception as e:
            logging.error(f"開盤前掃描失敗: {e}")
            return None
    
    def export_to_excel(self, filename=None):
        """導出報告到Excel"""
        try:
            if not self.data_cache:
                self.run_full_scan()
            
            if not filename:
                filename = f"pre_market_report_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx"
            
            # 創建一個確保至少有一個工作表的機制
            has_data = False
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 美股指數
                us_data = self.data_cache.get('us_indices', {})
                if us_data:
                    us_df = pd.DataFrame(us_data).T
                    us_df.to_excel(writer, sheet_name='美股指數')
                    has_data = True
                
                # 亞洲指數
                asia_data = self.data_cache.get('asia_indices', {})
                if asia_data:
                    asia_df = pd.DataFrame(asia_data).T
                    asia_df.to_excel(writer, sheet_name='亞洲指數')
                    has_data = True
                
                # 大宗商品
                commodity_data = self.data_cache.get('commodities', {})
                if commodity_data:
                    commodity_df = pd.DataFrame(commodity_data).T
                    commodity_df.to_excel(writer, sheet_name='大宗商品')
                    has_data = True
                
                # 匯率
                fx_data = self.data_cache.get('fx_rates', {})
                if fx_data:
                    fx_df = pd.DataFrame(fx_data).T
                    fx_df.to_excel(writer, sheet_name='匯率')
                    has_data = True
                
                # 台指期
                tw_data = self.data_cache.get('taiwan_futures', {})
                if tw_data:
                    tw_df = pd.DataFrame(tw_data).T
                    tw_df.to_excel(writer, sheet_name='台指期')
                    has_data = True
                
                # 加密貨幣
                crypto_data = self.data_cache.get('crypto', {})
                if crypto_data:
                    crypto_df = pd.DataFrame(crypto_data).T
                    crypto_df.to_excel(writer, sheet_name='加密貨幣')
                    has_data = True
                
                # 如果沒有任何數據，創建一個摘要工作表
                if not has_data:
                    summary_data = {
                        '項目': ['掃描時間', '數據狀態', '備註'],
                        '內容': [
                            self.data_cache.get('timestamp', '未知'),
                            '數據獲取受限',
                            '由於API速率限制，部分數據可能不可用'
                        ]
                    }
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='掃描摘要', index=False)
                    has_data = True
                
                # 總是創建一個包含基本信息的工作表
                info_data = {
                    '系統信息': ['掃描時間', '系統版本', '數據來源', '市場情緒'],
                    '詳情': [
                        self.data_cache.get('timestamp', '未知'),
                        self.version,
                        'yfinance API',
                        self.get_market_sentiment()
                    ]
                }
                info_df = pd.DataFrame(info_data)
                info_df.to_excel(writer, sheet_name='系統資訊', index=False)
            
            logging.info(f"📊 報告已導出至: {filename}")
            return filename
            
        except Exception as e:
            logging.error(f"導出Excel失敗: {e}")
            return None
    
    def get_market_sentiment(self):
        """分析市場情緒"""
        try:
            if not self.data_cache:
                return "數據不足"
            
            positive_signals = 0
            negative_signals = 0
            neutral_signals = 0
            total_signals = 0
            unavailable_count = 0
            
            # 分析美股
            for name, data in self.data_cache.get('us_indices', {}).items():
                total_signals += 1
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '')
                
                if status == '數據不可用':
                    unavailable_count += 1
                elif change_pct > 0:
                    positive_signals += 1
                elif change_pct < 0:
                    negative_signals += 1
                else:  # change_pct == 0
                    neutral_signals += 1
            
            # 分析商品
            for name, data in self.data_cache.get('commodities', {}).items():
                total_signals += 1
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '')
                
                if status == '數據不可用':
                    unavailable_count += 1
                elif change_pct > 0:
                    positive_signals += 1
                elif change_pct < 0:
                    negative_signals += 1
                else:  # change_pct == 0
                    neutral_signals += 1
            
            # 分析匯率 (USD/TWD 升值對台股偏負面)
            fx_data = self.data_cache.get('fx_rates', {})
            usd_twd = fx_data.get('USD/TWD', {})
            if usd_twd:
                total_signals += 1
                change_pct = usd_twd.get('change_pct', 0)
                status = usd_twd.get('status', '')
                
                if status == '數據不可用':
                    unavailable_count += 1
                elif change_pct > 0:  # USD升值對台股偏負面
                    negative_signals += 1
                elif change_pct < 0:  # USD貶值對台股偏正面
                    positive_signals += 1
                else:
                    neutral_signals += 1
            
            if total_signals == 0:
                return "數據不足"
            
            # 計算有效信號比例
            effective_signals = total_signals - unavailable_count
            if effective_signals == 0:
                return "數據不可用 ⚠️"
            
            # 如果大部分是中性信號，直接返回中性
            if neutral_signals / effective_signals >= 0.6:
                detail = f"中性 🟡 (平盤{neutral_signals}/{effective_signals})"
                return detail
            
            # 計算正面比例（排除中性信號）
            directional_signals = positive_signals + negative_signals
            if directional_signals == 0:
                return "中性 🟡 (無明顯趨勢)"
            
            positive_ratio = positive_signals / directional_signals
            
            # 構建詳細說明
            if positive_ratio >= 0.7:
                sentiment = "樂觀 🟢"
            elif positive_ratio >= 0.3:
                sentiment = "中性 🟡"
            else:
                sentiment = "悲觀 🔴"
            
            # 添加詳細統計
            detail = f"{sentiment} ({positive_signals}↑{negative_signals}↓{neutral_signals}→)"
            
            # 如果數據有限，添加提醒
            if unavailable_count > 0:
                detail += f" (部分數據受限)"
            
            return detail
                
        except Exception as e:
            logging.error(f"分析市場情緒失敗: {e}")
            return "分析失敗"
    
    def start_scheduler(self, hour=8, minute=20):
        """啟動定時掃描（交易日早上8:20）"""
        try:
            if PRE_MARKET_AVAILABLE:
                self.scheduler = BackgroundScheduler()
                self.scheduler.add_job(
                    func=self.run_full_scan,
                    trigger='cron',
                    hour=hour,
                    minute=minute,
                    day_of_week='mon-fri',  # 週一到週五
                    id='pre_market_scan'
                )
                self.scheduler.start()
                logging.info(f"⏰ 定時掃描已啟動：每個交易日 {hour:02d}:{minute:02d}")
                return True
            else:
                logging.warning("⚠️ 定時模組不可用，無法啟動排程")
                return False
        except Exception as e:
            logging.error(f"啟動定時掃描失敗: {e}")
            return False
    
    def stop_scheduler(self):
        """停止定時掃描"""
        try:
            if self.scheduler:
                self.scheduler.shutdown()
                self.scheduler = None
                logging.info("⏹️ 定時掃描已停止")
                return True
            return False
        except Exception as e:
            logging.error(f"停止定時掃描失敗: {e}")
            return False

