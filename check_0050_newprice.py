#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 中的 0050 資料
"""

import sqlite3
import pandas as pd

def check_0050_newprice():
    """檢查 newprice.db 中的 0050 資料"""
    
    print("=" * 60)
    print("📈 檢查 newprice.db 中的 0050 資料")
    print("=" * 60)
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查 0050 的最新資料
        query_0050 = '''
            SELECT date, [Close], Volume, [Open], High, Low, [Transaction], TradeValue, stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 15
        '''
        
        df_0050 = pd.read_sql_query(query_0050, conn)
        
        if not df_0050.empty:
            print(f"📈 0050 最新15筆資料:")
            
            for _, row in df_0050.iterrows():
                close_val = row['Close'] if pd.notna(row['Close']) else 'NULL'
                volume_val = row['Volume'] if pd.notna(row['Volume']) else 'NULL'
                open_val = row['Open'] if pd.notna(row['Open']) else 'NULL'
                
                print(f"   {row['date']}: 開盤={open_val}, 收盤={close_val}, 成交量={volume_val}")
            
            latest_date = df_0050.iloc[0]['date']
            print(f"\n📅 0050 最新日期: {latest_date}")
            
        else:
            print(f"❌ 未找到 0050 資料")
        
        # 檢查 0050 總記錄數
        query_count = '''
            SELECT COUNT(*) as total_count
            FROM stock_daily_data 
            WHERE stock_id = '0050'
        '''
        
        result = pd.read_sql_query(query_count, conn)
        total_count = result.iloc[0]['total_count']
        print(f"\n📊 0050 總記錄數: {total_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 讀取資料庫失敗: {e}")

if __name__ == "__main__":
    check_0050_newprice()
