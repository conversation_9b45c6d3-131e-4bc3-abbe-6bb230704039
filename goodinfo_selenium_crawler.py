#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 Selenium 爬取 GoodInfo 除權息資料
"""

import pandas as pd
import time
import random
from datetime import datetime
import urllib.parse

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("❌ Selenium 未安裝，請執行: pip install selenium")

class GoodInfoSeleniumCrawler:
    def __init__(self):
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium 未安裝")
        
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """設定 Chrome WebDriver"""
        print("🚀 設定 Chrome WebDriver...")
        
        chrome_options = Options()
        
        # 基本設定
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        
        # 設定User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # 設定語言
        chrome_options.add_argument('--lang=zh-TW')
        
        # 啟用無頭模式（背景執行，不顯示瀏覽器視窗）
        chrome_options.add_argument('--headless')
        
        try:
            # 嘗試使用系統的 Chrome
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome WebDriver 設定成功")
        except Exception as e:
            print(f"❌ Chrome WebDriver 設定失敗: {e}")
            print("請確認已安裝 Chrome 瀏覽器和 ChromeDriver")
            raise
    
    def wait_for_page_load(self, timeout=30):
        """等待頁面載入完成"""
        try:
            # 等待頁面不再顯示"初始化中"
            WebDriverWait(self.driver, timeout).until_not(
                EC.text_to_be_present_in_element((By.TAG_NAME, "body"), "初始化中")
            )
            
            # 額外等待確保JavaScript執行完成
            time.sleep(3)
            return True
            
        except TimeoutException:
            print("⏰ 頁面載入超時")
            return False
    
    def crawl_dividend_schedule_list(self, year="即將除權息"):
        """
        爬取除權息時間表
        
        Args:
            year: 年份，可以是具體年份如"2024"或"即將除權息"
        
        Returns:
            DataFrame: 除權息資料
        """
        print(f"🕷️ 開始爬取 GoodInfo 除權息資料 ({year})...")
        
        # 建構URL
        base_url = "https://goodinfo.tw/tw/StockDividendScheduleList.asp"
        params = {
            'MARKET_CAT': '全部',
            'INDUSTRY_CAT': '全部', 
            'YEAR': year
        }
        
        # URL編碼
        url = f"{base_url}?" + urllib.parse.urlencode(params, encoding='utf-8')
        print(f"📊 目標URL: {url}")
        
        try:
            # 訪問頁面
            self.driver.get(url)
            
            # 等待頁面載入
            if not self.wait_for_page_load():
                print("❌ 頁面載入失敗")
                return pd.DataFrame()
            
            print("✅ 頁面載入完成")
            
            # 儲存頁面源碼
            with open("goodinfo_selenium_response.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print("💾 已儲存頁面源碼到 goodinfo_selenium_response.html")
            
            # 尋找表格
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            print(f"🔍 找到 {len(tables)} 個表格")
            
            if not tables:
                print("❌ 未找到表格資料")
                return pd.DataFrame()
            
            # 分析表格內容
            dividend_data = []
            
            for i, table in enumerate(tables):
                print(f"\n📋 分析第 {i+1} 個表格...")
                
                try:
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    print(f"   找到 {len(rows)} 行")
                    
                    if len(rows) > 5:  # 主要資料表格應該有較多行
                        print(f"   這可能是主要資料表格")
                        
                        # 分析前幾行的結構
                        for j, row in enumerate(rows[:5]):
                            cells = row.find_elements(By.TAG_NAME, "td") + row.find_elements(By.TAG_NAME, "th")
                            if cells:
                                cell_texts = [cell.text.strip() for cell in cells]
                                print(f"   第{j+1}行: {cell_texts[:5]}...")
                        
                        # 如果找到標題行，開始提取資料
                        header_found = False
                        for j, row in enumerate(rows):
                            cells = row.find_elements(By.TAG_NAME, "td") + row.find_elements(By.TAG_NAME, "th")
                            if cells:
                                cell_texts = [cell.text.strip() for cell in cells]
                                
                                # 檢查是否為標題行
                                if any(keyword in ''.join(cell_texts) for keyword in ['股票', '除權', '除息', '代號']):
                                    print(f"   找到可能的標題行: {cell_texts}")
                                    header_found = True
                                    
                                    # 提取後續資料行
                                    for k in range(j+1, min(j+11, len(rows))):  # 提取接下來10行作為樣本
                                        data_row = rows[k]
                                        data_cells = data_row.find_elements(By.TAG_NAME, "td")
                                        if data_cells:
                                            data_texts = [cell.text.strip() for cell in data_cells]
                                            if len(data_texts) >= 3:  # 至少要有3個欄位
                                                dividend_data.append(data_texts)
                                                print(f"   資料行{k-j}: {data_texts[:5]}...")
                                    break
                        
                        if header_found:
                            break
                            
                except Exception as e:
                    print(f"   分析表格 {i+1} 時發生錯誤: {e}")
            
            # 建立DataFrame
            if dividend_data:
                print(f"\n✅ 成功提取 {len(dividend_data)} 筆除權息資料")
                
                # 假設基本欄位結構
                columns = ['股票代號', '股票名稱', '除權息日期', '現金股利', '股票股利', '其他資訊']
                
                # 調整欄位數量
                if dividend_data:
                    max_cols = max(len(row) for row in dividend_data)
                    if max_cols > len(columns):
                        columns.extend([f'欄位{i}' for i in range(len(columns), max_cols)])
                
                df = pd.DataFrame(dividend_data, columns=columns[:max_cols] if dividend_data else columns)
                return df
            else:
                print("❌ 未提取到除權息資料")
                return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 爬取失敗: {e}")
            return pd.DataFrame()
    
    def close(self):
        """關閉瀏覽器"""
        if self.driver:
            self.driver.quit()
            print("🔒 瀏覽器已關閉")

def test_selenium_crawler():
    """測試 Selenium 爬蟲"""
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium 未安裝，無法執行測試")
        return
    
    print("🚀 測試 GoodInfo Selenium 爬蟲...")
    
    crawler = None
    try:
        crawler = GoodInfoSeleniumCrawler()
        
        # 測試爬取除權息清單
        print("\n" + "="*60)
        print("📊 測試: 爬取即將除權息清單")
        df = crawler.crawl_dividend_schedule_list("即將除權息")
        
        if not df.empty:
            print(f"✅ 成功獲取 {len(df)} 筆資料")
            print("\n前5筆資料:")
            print(df.head().to_string())
            
            # 儲存結果
            filename = f"goodinfo_dividend_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n💾 資料已儲存到: {filename}")
        else:
            print("❌ 未獲取到資料")
    
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    
    finally:
        if crawler:
            crawler.close()

if __name__ == "__main__":
    test_selenium_crawler()
