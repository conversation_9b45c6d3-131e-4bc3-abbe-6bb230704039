# Finlab 增量更新GUI - 功能演示

## 🎯 主要改進

### 原版GUI vs 新版GUI

#### 原版問題：
- ❌ 每次都要重新爬取所有資料
- ❌ 無法知道現有資料的狀態
- ❌ 浪費時間和網路資源
- ❌ 無法針對性更新

#### 新版優勢：
- ✅ 智能檢測現有資料狀態
- ✅ 只更新缺失的日期範圍
- ✅ 清楚顯示每個檔案的最後日期
- ✅ 可單獨或批量更新
- ✅ 節省時間和資源

## 📊 界面佈局

```
┌─────────────────────────────────────────────────────────────┐
│                Finlab 爬蟲系統 - 增量更新版                    │
├─────────────────────────────────────────────────────────────┤
│  資料檔案狀態                                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │檔案名稱    │最後日期   │建議更新範圍      │狀態    │操作│ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │股價資料    │2025-07-13│2025-07-14至今天  │可更新  │更新│ │
│  │融資融券    │2025-07-13│2025-07-14至今天  │可更新  │更新│ │
│  │本益比      │2025-07-13│2025-07-14至今天  │可更新  │更新│ │
│  │月報        │2025-07-13│2025-07-14至今天  │可更新  │更新│ │
│  │資產負債表  │檔案不存在│2023-01-01至今天  │需爬取  │更新│ │
│  └─────────────────────────────────────────────────────────┘ │
│  [全部更新] [重新檢查]                                        │
├─────────────────────────────────────────────────────────────┤
│  執行日誌                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │[16:22:03] Finlab 爬蟲系統 - 增量更新版已啟動              │ │
│  │[16:22:05] 檢測到 price.pkl，最後日期：2025-07-13         │ │
│  │[16:22:05] 建議更新範圍：2025-07-14 至 2025-07-20         │ │
│  │[16:22:10] 開始更新股價資料 (2025-07-14 至 2025-07-20)    │ │
│  │[16:22:15] ✓ 股價資料更新完成，獲取 7 筆資料               │ │
│  └─────────────────────────────────────────────────────────┘ │
│  [清除日誌]                                                  │
│  ████████████████████████████████████████████████████████   │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 操作流程

### 1. 啟動檢測
```
啟動GUI → 自動掃描pkl檔案 → 讀取最後日期 → 計算更新範圍 → 顯示狀態
```

### 2. 單個更新
```
雙擊表格行 → 確認更新範圍 → 執行爬蟲 → 保存資料 → 刷新狀態
```

### 3. 批量更新
```
點擊全部更新 → 逐個處理檔案 → 顯示進度 → 完成更新 → 刷新狀態
```

## 📋 狀態說明

| 狀態 | 含義 | 操作建議 |
|------|------|----------|
| 可更新 | 檔案存在但資料不是最新 | 雙擊更新缺失日期 |
| 已是最新 | 檔案資料已是最新 | 無需操作 |
| 需要爬取 | 檔案不存在 | 雙擊進行完整爬取 |
| 讀取錯誤 | 檔案損壞或格式錯誤 | 檢查檔案或重新爬取 |

## 🎯 使用場景

### 場景1：日常維護
- **情況**：每天更新最新資料
- **操作**：啟動GUI → 點擊「全部更新」
- **結果**：只更新昨天到今天的資料

### 場景2：特定資料更新
- **情況**：只需要更新股價資料
- **操作**：雙擊「股價資料」行
- **結果**：只更新股價的缺失日期

### 場景3：新環境部署
- **情況**：全新環境，無任何資料
- **操作**：啟動GUI → 所有狀態顯示「需要爬取」→ 全部更新
- **結果**：完整爬取所有資料

### 場景4：資料檢查
- **情況**：想知道資料更新狀態
- **操作**：啟動GUI → 查看狀態表格
- **結果**：一目了然各檔案狀態

## 💡 智能特性

### 1. 自動日期計算
```python
# 檢測最後日期
last_date = "2025-07-13"
# 自動計算更新範圍
start_date = "2025-07-14"  # 最後日期+1天
end_date = "2025-07-20"    # 今天
```

### 2. 檔案類型識別
```python
# 不同檔案類型使用不同更新策略
if filename in ['balance_sheet.pkl', 'income_sheet.pkl']:
    # 財務資料按季度更新
    update_by_season()
else:
    # 一般資料按日期範圍更新
    update_by_date_range()
```

### 3. 錯誤處理
```python
try:
    # 嘗試讀取檔案
    data = pickle.load(file)
    last_date = data.index.max()
except Exception as e:
    # 顯示錯誤狀態
    status = f"讀取錯誤: {str(e)}"
```

## 🚀 效能提升

### 時間節省
- **原版**：每次爬取30天資料 = 30次API請求
- **新版**：只爬取7天缺失資料 = 7次API請求
- **節省**：76%的時間和網路請求

### 資源節省
- **避免重複資料**：不會重複保存已有資料
- **減少網路負載**：只請求必要的資料
- **提升用戶體驗**：更快的更新速度

## 🔧 技術實現

### 檔案檢測
```python
def check_existing_data(self):
    for filename in data_files:
        if os.path.exists(filepath):
            data = pickle.load(filepath)
            last_date = data.index.max()
            # 計算更新範圍
```

### 增量更新
```python
def update_single_data(self, filename):
    # 計算更新日期範圍
    start_date = last_date + 1
    end_date = today
    # 只爬取缺失範圍
    result = crawler_func(start_date, end_date)
```

這個增量更新GUI大大提升了資料管理效率，讓您的Finlab資料始終保持最新狀態！
