#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 Selenium 的 MOPS 現金流量表爬蟲
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time
import pandas as pd
import logging
from typing import Optional
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class SeleniumMOPSCashFlowScraper:
    """使用 Selenium 的 MOPS 現金流量表爬蟲"""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.driver = None
        self._setup_driver()
    
    def _setup_driver(self):
        """設置 Chrome WebDriver"""
        try:
            # 設定 Chrome 選項
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument("--headless")  # 無頭模式
            
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 設置 User-Agent
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            # 自動下載並設置 ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 啟動瀏覽器
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 設置隱式等待
            self.driver.implicitly_wait(10)
            
            logger.info("✅ Chrome WebDriver 設置成功")
            
        except Exception as e:
            logger.error(f"❌ WebDriver 設置失敗: {e}")
            raise
    
    def get_cash_flow_statement(self, stock_code: str, year: int, season: int) -> Optional[pd.DataFrame]:
        """
        使用 Selenium 獲取現金流量表
        
        Args:
            stock_code: 股票代碼 (如 '2330')
            year: 年度 (西元年，如 2024)
            season: 季別 (1-4)
        
        Returns:
            現金流量表 DataFrame 或 None
        """
        logger.info(f"🔍 使用 Selenium 獲取 {stock_code} {year}年第{season}季現金流量表...")
        
        try:
            # 轉換西元年為民國年
            roc_year = str(year - 1911)
            
            # 訪問 MOPS 現金流量表頁面
            url = 'https://mops.twse.com.tw/mops/web/t164sb05'
            logger.info(f"📡 訪問頁面: {url}")
            
            self.driver.get(url)
            
            # 等待頁面加載
            time.sleep(3)
            
            # 檢查頁面是否正確加載
            page_title = self.driver.title
            logger.info(f"📄 頁面標題: {page_title}")
            
            # 尋找並填寫表單元素
            logger.info(f"📝 填寫查詢表單...")
            
            # 輸入公司代碼
            try:
                co_id_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.NAME, 'co_id'))
                )
                co_id_input.clear()
                co_id_input.send_keys(stock_code)
                logger.info(f"   ✅ 輸入公司代碼: {stock_code}")
            except Exception as e:
                logger.error(f"   ❌ 無法找到公司代碼輸入框: {e}")
                return None
            
            # 選擇年度
            try:
                year_select = Select(WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.NAME, 'year'))
                ))
                year_select.select_by_value(roc_year)
                logger.info(f"   ✅ 選擇年度: {roc_year} (民國年)")
            except Exception as e:
                logger.error(f"   ❌ 無法選擇年度: {e}")
                return None
            
            # 選擇季度
            try:
                season_select = Select(WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.NAME, 'season'))
                ))
                season_select.select_by_value(str(season))
                logger.info(f"   ✅ 選擇季度: {season}")
            except Exception as e:
                logger.error(f"   ❌ 無法選擇季度: {e}")
                return None
            
            # 提交表單
            try:
                submit_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[type="submit"]'))
                )
                submit_button.click()
                logger.info(f"   ✅ 提交表單")
            except Exception as e:
                logger.error(f"   ❌ 無法提交表單: {e}")
                return None
            
            # 等待結果頁面加載
            time.sleep(5)
            
            # 檢查是否有錯誤訊息
            page_source = self.driver.page_source
            
            if '查無資料' in page_source or '無此資料' in page_source:
                logger.warning(f"⚠️ {stock_code}: 查無資料")
                return None
            
            # 解析現金流量表
            return self._parse_cash_flow_table(page_source, stock_code)
            
        except Exception as e:
            logger.error(f"❌ 獲取現金流量表失敗: {e}")
            return None
    
    def _parse_cash_flow_table(self, html_content: str, stock_code: str) -> Optional[pd.DataFrame]:
        """解析現金流量表"""
        try:
            logger.info(f"📊 解析現金流量表...")
            
            # 使用 pandas 讀取 HTML 表格
            dfs = pd.read_html(html_content, header=0)
            logger.info(f"   📊 找到 {len(dfs)} 個表格")
            
            # 尋找包含現金流量資料的表格
            for i, df in enumerate(dfs):
                # 檢查表格內容
                table_text = df.to_string()
                
                # 檢查是否包含現金流量相關關鍵字
                cash_flow_keywords = ['現金流量', '營業活動', '投資活動', '籌資活動', '現金及約當現金']
                
                if any(keyword in table_text for keyword in cash_flow_keywords):
                    logger.info(f"   ✅ 表格 {i+1} 包含現金流量資料: {df.shape}")
                    
                    # 清理資料
                    df['stock_id'] = stock_code
                    df = df.dropna(how='all').reset_index(drop=True)
                    
                    # 顯示表格內容
                    logger.info(f"   📋 欄位: {list(df.columns)}")
                    
                    return df
            
            logger.warning(f"⚠️ {stock_code}: 未找到現金流量表")
            return None
            
        except Exception as e:
            logger.error(f"❌ 解析現金流量表失敗: {e}")
            return None
    
    def close(self):
        """關閉瀏覽器"""
        if self.driver:
            self.driver.quit()
            logger.info("🔒 瀏覽器已關閉")

def test_selenium_scraper():
    """測試 Selenium 爬蟲"""
    
    print("=" * 80)
    print("🧪 測試 Selenium MOPS 現金流量表爬蟲")
    print("=" * 80)
    
    scraper = None
    
    try:
        # 創建爬蟲 (先用非無頭模式測試，可以看到瀏覽器操作)
        scraper = SeleniumMOPSCashFlowScraper(headless=False)
        
        # 測試案例
        test_cases = [
            ("2330", 2023, 4),  # 台積電 2023Q4
            ("2317", 2023, 4),  # 鴻海 2023Q4
        ]
        
        successful_tests = 0
        
        for stock_code, year, season in test_cases:
            print(f"\n📊 測試 {stock_code} {year}年第{season}季...")
            
            df = scraper.get_cash_flow_statement(stock_code, year, season)
            
            if df is not None and not df.empty:
                print(f"✅ 成功獲取現金流量表: {df.shape}")
                print(f"📋 欄位: {list(df.columns)}")
                
                # 顯示前幾行資料
                print(f"📊 前5行資料:")
                print(df.head())
                
                successful_tests += 1
            else:
                print(f"❌ 無法獲取現金流量表")
            
            # 等待一下避免請求過於頻繁
            time.sleep(3)
        
        # 測試結果總結
        print(f"\n📊 Selenium 測試結果:")
        print(f"   總測試案例: {len(test_cases)}")
        print(f"   成功案例: {successful_tests}")
        print(f"   成功率: {(successful_tests/len(test_cases)*100):.1f}%")
        
        return successful_tests > 0
        
    except Exception as e:
        print(f"❌ Selenium 測試失敗: {e}")
        return False
        
    finally:
        if scraper:
            scraper.close()

def compare_selenium_with_existing_system():
    """比較 Selenium 方法與現有系統"""
    
    print(f"\n" + "=" * 80)
    print("🔍 Selenium 方法 vs 現有系統比較")
    print("=" * 80)
    
    print("🤖 **Selenium 方法**:")
    print("   優勢:")
    print("   ✅ 可以處理 JavaScript 動態內容")
    print("   ✅ 模擬真實瀏覽器行為")
    print("   ✅ 可以處理複雜的表單提交")
    print("   ✅ 適應網站結構變化")
    print()
    print("   缺點:")
    print("   ⚠️ 執行速度較慢")
    print("   ⚠️ 資源消耗較大")
    print("   ⚠️ 需要安裝瀏覽器驅動")
    print("   ⚠️ 維護成本較高")
    print()
    
    print("🏛️ **現有 TWSE OpenAPI 系統**:")
    print("   優勢:")
    print("   ✅ 官方支援，穩定可靠")
    print("   ✅ 執行速度快")
    print("   ✅ 資源消耗少")
    print("   ✅ 1,008家上市公司 + 835家上櫃公司")
    print("   ✅ 統一的日期格式和資料結構")
    print()
    print("   缺點:")
    print("   ⚠️ 目前缺少現金流量表")
    print()
    
    print("💡 **建議整合方案**:")
    print("   1. 主要使用 TWSE OpenAPI (損益表+資產負債表)")
    print("   2. 如果 Selenium 測試成功，補充現金流量表")
    print("   3. 建立混合架構，發揮各自優勢")

def main():
    """主函數"""
    
    print("🤖 Selenium MOPS 現金流量表爬蟲測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試 Selenium 爬蟲
    selenium_success = test_selenium_scraper()
    
    # 比較分析
    compare_selenium_with_existing_system()
    
    print(f"\n" + "=" * 80)
    print("🎉 Selenium 測試完成")
    print("=" * 80)
    
    if selenium_success:
        print("✅ Selenium 方法測試成功！")
        print("💡 建議:")
        print("   1. 可以整合 Selenium 現金流量表爬蟲")
        print("   2. 與現有 TWSE OpenAPI 系統結合")
        print("   3. 建立完整的三大財務報表系統")
    else:
        print("❌ Selenium 方法測試失敗")
        print("💡 建議:")
        print("   1. 繼續使用現有的 TWSE OpenAPI 系統")
        print("   2. 使用現有的 38,299筆現金流量歷史資料")
        print("   3. 等待更穩定的現金流量表資料源")

if __name__ == "__main__":
    main()
