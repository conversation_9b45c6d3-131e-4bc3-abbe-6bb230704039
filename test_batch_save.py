#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試分批存檔功能
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_batch_save():
    """測試分批存檔功能"""
    print("🧪 測試分批存檔功能...")
    
    try:
        from crawler import update_table, price_twe
        
        # 測試少量日期 (15 個日期，應該會分批存檔)
        start_date = datetime(2022, 9, 1)
        test_dates = []
        
        for i in range(15):  # 15 個日期
            test_date = start_date + timedelta(days=i)
            # 跳過週末
            if test_date.weekday() < 5:  # 0-4 是週一到週五
                test_dates.append(test_date)
        
        print(f"測試日期數量: {len(test_dates)}")
        print(f"預期分批: 第 10 筆會存檔一次，剩餘的會在最後存檔")
        print(f"日期範圍: {test_dates[0].strftime('%Y-%m-%d')} 到 {test_dates[-1].strftime('%Y-%m-%d')}")
        
        print("\n開始測試...")
        update_table('price', price_twe, test_dates[:12])  # 只測試前 12 個日期
        
        print("\n✅ 分批存檔測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_batch_save()
