#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較 PKL 和 DB 檔案的欄位名稱
"""

import pandas as pd
import sqlite3
import os

def compare_columns():
    """比較 PKL 和 DB 檔案的欄位結構"""
    
    pkl_file = 'D:/Finlab/history/tables/twse_divide_ratio.pkl'
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    
    print("🔍 比較 PKL 和 DB 檔案的欄位結構")
    print("=" * 60)
    
    # 檢查 PKL 檔案
    if os.path.exists(pkl_file):
        try:
            print(f"📂 讀取 PKL 檔案: {pkl_file}")
            df_pkl = pd.read_pickle(pkl_file)
            
            print(f"\n📊 PKL 檔案結構:")
            print(f"   資料筆數: {len(df_pkl):,}")
            print(f"   索引類型: {type(df_pkl.index)}")
            print(f"   索引名稱: {df_pkl.index.names}")
            print(f"   欄位數量: {len(df_pkl.columns)}")
            print(f"\n📋 PKL 檔案欄位名稱:")
            for i, col in enumerate(df_pkl.columns, 1):
                print(f"   {i:2d}. {col}")
            
            # 顯示前幾筆資料的結構
            print(f"\n📝 PKL 檔案前3筆資料:")
            print(df_pkl.head(3))
            
        except Exception as e:
            print(f"❌ 讀取 PKL 檔案失敗: {e}")
    else:
        print(f"❌ PKL 檔案不存在: {pkl_file}")
    
    print("\n" + "=" * 60)
    
    # 檢查 DB 檔案
    if os.path.exists(db_file):
        try:
            print(f"📂 讀取 DB 檔案: {db_file}")
            conn = sqlite3.connect(db_file)
            
            # 獲取表格結構
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(twse_divide_ratio);")
            columns_info = cursor.fetchall()
            
            print(f"\n📊 DB 檔案結構:")
            cursor.execute("SELECT COUNT(*) FROM twse_divide_ratio;")
            record_count = cursor.fetchone()[0]
            print(f"   資料筆數: {record_count:,}")
            print(f"   欄位數量: {len(columns_info)}")
            
            print(f"\n📋 DB 檔案欄位名稱:")
            for i, (cid, name, dtype, notnull, default, pk) in enumerate(columns_info, 1):
                print(f"   {i:2d}. {name} ({dtype})")
            
            # 讀取前幾筆資料
            df_db = pd.read_sql("SELECT * FROM twse_divide_ratio LIMIT 3", conn)
            print(f"\n📝 DB 檔案前3筆資料:")
            print(df_db)
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取 DB 檔案失敗: {e}")
    else:
        print(f"❌ DB 檔案不存在: {db_file}")
    
    print("\n" + "=" * 60)
    
    # 比較欄位名稱
    if os.path.exists(pkl_file) and os.path.exists(db_file):
        try:
            # 重新讀取進行比較
            df_pkl = pd.read_pickle(pkl_file)
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(twse_divide_ratio);")
            db_columns_info = cursor.fetchall()
            conn.close()
            
            # PKL 欄位（包含索引）
            pkl_columns = list(df_pkl.index.names) + list(df_pkl.columns)
            pkl_columns = [col for col in pkl_columns if col is not None]
            
            # DB 欄位
            db_columns = [col[1] for col in db_columns_info]
            
            print("🔍 欄位比較結果:")
            print(f"   PKL 欄位數: {len(pkl_columns)}")
            print(f"   DB 欄位數:  {len(db_columns)}")
            
            # 找出差異
            pkl_only = set(pkl_columns) - set(db_columns)
            db_only = set(db_columns) - set(pkl_columns)
            common = set(pkl_columns) & set(db_columns)
            
            print(f"\n✅ 共同欄位 ({len(common)} 個):")
            for col in sorted(common):
                print(f"   ✓ {col}")
            
            if pkl_only:
                print(f"\n⚠️ 只在 PKL 中的欄位 ({len(pkl_only)} 個):")
                for col in sorted(pkl_only):
                    print(f"   - {col}")
            
            if db_only:
                print(f"\n⚠️ 只在 DB 中的欄位 ({len(db_only)} 個):")
                for col in sorted(db_only):
                    print(f"   + {col}")
            
            if not pkl_only and not db_only:
                print("\n🎉 所有欄位完全一致！")
            else:
                print(f"\n⚠️ 發現欄位差異，需要檢查轉換邏輯")
                
        except Exception as e:
            print(f"❌ 比較過程失敗: {e}")

def check_data_sample():
    """檢查資料樣本的內容"""
    
    pkl_file = 'D:/Finlab/history/tables/twse_divide_ratio.pkl'
    db_file = 'D:/Finlab/history/tables/twse_divide_ratio.db'
    
    print("\n" + "=" * 60)
    print("🔍 檢查資料樣本內容")
    print("=" * 60)
    
    try:
        # PKL 資料樣本
        df_pkl = pd.read_pickle(pkl_file)
        print("📝 PKL 檔案樣本 (第1筆資料):")
        sample_pkl = df_pkl.iloc[0]
        for col, value in sample_pkl.items():
            print(f"   {col}: {value}")
        
        # DB 資料樣本
        conn = sqlite3.connect(db_file)
        df_db = pd.read_sql("SELECT * FROM twse_divide_ratio LIMIT 1", conn)
        conn.close()
        
        print("\n📝 DB 檔案樣本 (第1筆資料):")
        if not df_db.empty:
            for col, value in df_db.iloc[0].items():
                print(f"   {col}: {value}")
        
    except Exception as e:
        print(f"❌ 檢查資料樣本失敗: {e}")

def main():
    """主函數"""
    compare_columns()
    check_data_sample()

if __name__ == "__main__":
    main()
