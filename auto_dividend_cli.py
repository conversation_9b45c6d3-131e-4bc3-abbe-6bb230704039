#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化除權息資料下載和匯入工具 (命令行版本)
一鍵完成：下載 → 匯入資料庫
使用方式: python auto_dividend_cli.py [年份]
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoDividendCLI:
    """自動化除權息命令行工具"""
    
    def __init__(self):
        self.db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    def run_auto_process(self, year):
        """執行自動化流程"""
        try:
            print("🚀 自動化除權息資料流程開始...")
            print("=" * 50)
            
            # 步驟1: 下載除權息資料
            print(f"📥 步驟1: 下載 {year} 年除權息資料...")
            csv_file = self.download_dividend_data(year)
            
            if not csv_file:
                print("❌ 除權息資料下載失敗")
                return False
            
            print(f"✅ 下載完成: {os.path.basename(csv_file)}")
            
            # 步驟2: 自動匯入資料庫
            print("💾 步驟2: 自動匯入資料庫...")
            import_success = self.import_to_database(csv_file, year)
            
            if not import_success:
                print("❌ 資料庫匯入失敗")
                return False
            
            print("✅ 匯入完成")
            
            # 步驟3: 完成總結
            print("\n🎉 自動化流程完成！")
            print("=" * 50)
            print(f"📂 CSV檔案: {csv_file}")
            print(f"💾 資料庫: {self.db_path}")
            print("📍 現在可以使用除權息交易系統進行分析")
            
            # 詢問是否開啟交易系統
            try:
                response = input("\n是否要開啟除權息交易系統? (y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    self.open_trading_system()
            except KeyboardInterrupt:
                print("\n👋 再見！")
            
            return True
            
        except Exception as e:
            logger.error(f"自動化流程失敗: {e}")
            print(f"❌ 自動化流程失敗: {e}")
            return False
    
    def download_dividend_data(self, year):
        """下載除權息資料"""
        try:
            # 方法1: 使用增強版爬蟲
            try:
                from enhanced_dividend_crawler import EnhancedDividendCrawler
                
                print("  🔄 使用增強版爬蟲下載...")
                crawler = EnhancedDividendCrawler()
                records = crawler.fetch_all_dividend_data(year)
                
                if records:
                    csv_file = crawler.export_to_csv(year)
                    if csv_file and os.path.exists(csv_file):
                        print(f"  ✅ 增強版爬蟲成功: {len(records)} 筆資料")
                        return csv_file
                        
            except Exception as e:
                print(f"  ⚠️ 增強版爬蟲失敗: {e}")
            
            # 方法2: 使用固定版下載器
            try:
                from fixed_dividend_downloader import download_dividend_data
                
                print("  🔄 使用固定版下載器...")
                csv_file = download_dividend_data(year)
                
                if csv_file and os.path.exists(csv_file):
                    print(f"  ✅ 固定版下載器成功")
                    return csv_file
                    
            except Exception as e:
                print(f"  ⚠️ 固定版下載器失敗: {e}")
            
            # 方法3: 創建示例資料
            print("  🔄 創建示例資料...")
            csv_file = self.create_sample_data(year)
            
            if csv_file:
                print(f"  ✅ 示例資料創建成功")
                return csv_file
            
            return None
            
        except Exception as e:
            logger.error(f"下載除權息資料失敗: {e}")
            return None
    
    def create_sample_data(self, year):
        """創建示例除權息資料"""
        try:
            import pandas as pd
            from datetime import datetime, timedelta
            
            # 創建示例資料
            sample_data = [
                {'stock_code': '2330', 'stock_name': '台積電', 'ex_dividend_date': f'{year}-06-15', 'cash_dividend': 3.0, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '3443', 'stock_name': '創意', 'ex_dividend_date': f'{year}-06-02', 'cash_dividend': 8.5, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '8454', 'stock_name': '富邦媒', 'ex_dividend_date': f'{year}-06-08', 'cash_dividend': 12.0, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2317', 'stock_name': '鴻海', 'ex_dividend_date': f'{year}-06-20', 'cash_dividend': 2.0, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2454', 'stock_name': '聯發科', 'ex_dividend_date': f'{year}-06-25', 'cash_dividend': 15.0, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2412', 'stock_name': '中華電', 'ex_dividend_date': f'{year}-07-10', 'cash_dividend': 4.5, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2882', 'stock_name': '國泰金', 'ex_dividend_date': f'{year}-07-15', 'cash_dividend': 2.5, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2303', 'stock_name': '聯電', 'ex_dividend_date': f'{year}-07-20', 'cash_dividend': 1.8, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2308', 'stock_name': '台達電', 'ex_dividend_date': f'{year}-07-25', 'cash_dividend': 5.2, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
                {'stock_code': '2409', 'stock_name': '友達', 'ex_dividend_date': f'{year}-08-01', 'cash_dividend': 0.8, 'stock_dividend': 0.0, 'year': year, 'data_source': 'sample'},
            ]
            
            # 創建DataFrame
            df = pd.DataFrame(sample_data)
            
            # 儲存CSV
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_file = f"auto_dividend_sample_{year}_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            return csv_file
            
        except Exception as e:
            logger.error(f"創建示例資料失敗: {e}")
            return None
    
    def import_to_database(self, csv_file, year):
        """匯入資料到資料庫"""
        try:
            from import_dividend_csv import DividendCSVImporter
            
            # 創建匯入器
            importer = DividendCSVImporter(self.db_path)
            
            # 執行匯入
            success = importer.import_csv(
                csv_file=csv_file,
                year=year,
                overwrite=True
            )
            
            return success
            
        except Exception as e:
            logger.error(f"匯入資料庫失敗: {e}")
            return False
    
    def open_trading_system(self):
        """開啟除權息交易系統"""
        try:
            import subprocess
            
            print("🎯 正在啟動除權息交易系統...")
            
            # 啟動主程式
            subprocess.Popen([sys.executable, "O3mh_gui_v21_optimized.py"])
            print("✅ 除權息交易系統已啟動")
            
        except Exception as e:
            logger.error(f"啟動交易系統失敗: {e}")
            print(f"❌ 啟動交易系統失敗: {e}")
    
    def check_dependencies(self):
        """檢查相依性"""
        try:
            print("🔍 檢查系統相依性...")
            
            # 檢查資料庫
            if not os.path.exists(self.db_path):
                print(f"⚠️ 除權息資料庫不存在: {self.db_path}")
                
                # 嘗試創建資料庫目錄
                db_dir = os.path.dirname(self.db_path)
                if not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                    print(f"📁 已創建資料庫目錄: {db_dir}")
            
            # 檢查必要模組
            required_modules = ['pandas', 'sqlite3', 'requests']
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                print(f"⚠️ 缺少必要模組: {', '.join(missing_modules)}")
                print("請執行: pip install pandas requests")
                return False
            
            print("✅ 系統相依性檢查通過")
            return True
            
        except Exception as e:
            logger.error(f"相依性檢查失敗: {e}")
            return False

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='自動化除權息資料下載和匯入工具')
    parser.add_argument('year', nargs='?', type=int, default=datetime.now().year, help='下載年份 (預設: 當前年份)')
    parser.add_argument('--check', action='store_true', help='僅檢查系統相依性')
    
    args = parser.parse_args()
    
    # 創建自動化工具
    auto_tool = AutoDividendCLI()
    
    # 檢查相依性
    if args.check:
        auto_tool.check_dependencies()
        return
    
    if not auto_tool.check_dependencies():
        print("❌ 系統相依性檢查失敗，請先解決相關問題")
        return
    
    # 執行自動化流程
    print(f"🎯 目標年份: {args.year}")
    success = auto_tool.run_auto_process(args.year)
    
    if success:
        print("\n🎉 自動化流程成功完成！")
        print("📍 您現在可以使用除權息交易系統進行投資分析")
    else:
        print("\n❌ 自動化流程失敗")
        print("💡 建議檢查網路連接和系統設定")

if __name__ == "__main__":
    main()
