#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 0050 股票資訊映射問題
"""

import sys
import os
import sqlite3

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_0050_mapping():
    """調試 0050 映射問題"""
    
    print("=" * 60)
    print("🔍 調試 0050 股票資訊映射問題")
    print("=" * 60)
    
    try:
        from crawler import get_cached_stock_info
        
        # 獲取股票資訊
        print("📡 獲取股票資訊...")
        stock_info = get_cached_stock_info()
        
        # 檢查 0050 相關的股票代碼
        print(f"\n🔍 檢查 0050 相關股票代碼:")
        
        etf_codes = ['0050', '0051', '0052', '0053', '0056']
        
        for code in etf_codes:
            print(f"\n   測試代碼: '{code}'")
            
            # 直接查找
            if code in stock_info:
                info = stock_info[code]
                print(f"   ✅ 找到: {info}")
            else:
                print(f"   ❌ 未找到")
                
                # 檢查相似的鍵
                similar_keys = [k for k in stock_info.keys() if code in k]
                if similar_keys:
                    print(f"   相似鍵: {similar_keys}")
                else:
                    print(f"   無相似鍵")
        
        # 檢查所有 ETF 相關的鍵
        print(f"\n📋 所有 ETF 相關鍵 (00 開頭):")
        etf_keys = [k for k in stock_info.keys() if k.startswith('00')]
        etf_keys.sort()
        
        print(f"   總數: {len(etf_keys)}")
        for i, key in enumerate(etf_keys[:20]):  # 只顯示前20個
            info = stock_info[key]
            print(f"   {key}: {info['stock_name']} | {info['listing_status']} | {info['industry']}")
        
        if len(etf_keys) > 20:
            print(f"   ... 還有 {len(etf_keys) - 20} 個")
        
        # 檢查資料庫中的實際資料
        print(f"\n📊 檢查資料庫中 0050 的實際資料:")
        newprice_db = 'D:/Finlab/history/tables/newprice.db'
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, date, [Close]
            FROM stock_daily_data 
            WHERE stock_id = '0050'
            ORDER BY date DESC
            LIMIT 5
        ''')
        db_data = cursor.fetchall()
        
        if db_data:
            print(f"   資料庫中的 0050 記錄:")
            for stock_id, name, status, industry, date, close in db_data:
                print(f"   {date}: {stock_id} | {name} | {status} | {industry} | 收盤:{close}")
        else:
            print(f"   ❌ 資料庫中沒有 0050 記錄")
        
        # 檢查其他 ETF 的狀況
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id LIKE '00%' AND date = (SELECT MAX(date) FROM stock_daily_data)
            ORDER BY stock_id
            LIMIT 10
        ''')
        other_etfs = cursor.fetchall()
        
        print(f"\n📋 其他 ETF 在資料庫中的狀況:")
        for stock_id, name, status, industry in other_etfs:
            status_display = status if status else 'NULL'
            industry_display = industry if industry else 'NULL'
            print(f"   {stock_id}: {name} | {status_display} | {industry_display}")
        
        conn.close()
        
        # 測試映射函數
        print(f"\n🧪 測試映射函數:")
        test_mapping = lambda x: stock_info.get(str(x), {})
        
        for code in ['0050', '0051', '0052']:
            result = test_mapping(code)
            print(f"   {code} -> {result}")
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_0050_mapping()
