#!/usr/bin/env python3
"""
測試智能代理整合功能
驗證代理管理器在股票數據獲取中的效果
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_proxy_manager():
    """測試代理管理器基本功能"""
    print("🧪 測試智能代理管理器")
    print("=" * 80)
    
    try:
        from smart_proxy_manager import SmartRequestManager, get_proxy_stats
        
        print("✅ 智能代理管理器載入成功")
        
        # 創建請求管理器
        manager = SmartRequestManager()
        
        # 測試基本請求
        print(f"\n🔍 測試基本HTTP請求:")
        test_url = "https://httpbin.org/ip"
        
        response = manager.make_request(test_url)
        if response:
            print(f"  ✅ 請求成功: {response.status_code}")
            try:
                data = response.json()
                print(f"  📍 IP地址: {data.get('origin', 'N/A')}")
            except:
                print(f"  📄 響應長度: {len(response.text)} 字符")
        else:
            print(f"  ❌ 請求失敗")
        
        # 獲取統計信息
        stats = get_proxy_stats()
        print(f"\n📊 代理統計:")
        print(f"  總代理數: {stats['total_proxies']}")
        print(f"  可用代理: {stats['active_proxies']}")
        print(f"  失敗代理: {stats['banned_proxies']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 智能代理管理器載入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_stock_data_with_proxy():
    """測試使用代理獲取股票數據"""
    print(f"\n📈 測試股票數據獲取（使用代理）")
    print("-" * 40)
    
    try:
        from smart_proxy_manager import make_smart_request, get_smart_json
        
        # 測試證交所API
        print(f"\n🏢 測試證交所API:")
        test_stocks = ['2330', '2317', '5360']
        
        for stock_code in test_stocks:
            print(f"\n📊 測試股票 {stock_code}:")
            
            # 證交所API
            twse_url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"
            params = {
                'ex_ch': f'tse_{stock_code}.tw',
                'json': '1',
                'delay': '0'
            }
            
            start_time = time.time()
            response = make_smart_request(twse_url, params=params)
            end_time = time.time()
            
            if response and response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('stat') == 'OK' and 'msgArray' in data and data['msgArray']:
                        stock_info = data['msgArray'][0]
                        price = stock_info.get('z', '-')
                        volume = stock_info.get('v', '-')
                        
                        print(f"  ✅ TWSE API 成功")
                        print(f"    價格: {price}")
                        print(f"    成交量: {volume}")
                        print(f"    耗時: {end_time - start_time:.2f}秒")
                    else:
                        print(f"  ⚠️ TWSE API 無數據")
                except Exception as e:
                    print(f"  ❌ TWSE API 解析失敗: {e}")
            else:
                print(f"  ❌ TWSE API 請求失敗")
        
        return True
        
    except ImportError:
        print(f"⚠️ 智能代理管理器不可用，跳過測試")
        return False
    except Exception as e:
        print(f"❌ 股票數據測試失敗: {e}")
        return False

def test_proxy_benefits():
    """測試代理的優勢"""
    print(f"\n🎯 代理優勢測試")
    print("-" * 40)
    
    benefits = [
        {
            "優勢": "🌐 IP輪換",
            "描述": "使用不同IP地址發送請求，避免單一IP被限制",
            "效果": "降低被封鎖的風險"
        },
        {
            "優勢": "🔄 自動重試",
            "描述": "代理失敗時自動切換到下一個可用代理",
            "效果": "提高請求成功率"
        },
        {
            "優勢": "📊 智能選擇",
            "描述": "根據成功率和響應時間選擇最佳代理",
            "效果": "優化性能和穩定性"
        },
        {
            "優勢": "⚡ 頻率分散",
            "描述": "將請求分散到多個代理，降低單一來源頻率",
            "效果": "避免頻率限制"
        },
        {
            "優勢": "🛡️ 錯誤容忍",
            "描述": "代理失敗時自動標記並冷卻，避免重複使用",
            "效果": "提高系統穩定性"
        }
    ]
    
    for benefit in benefits:
        print(f"\n{benefit['優勢']}")
        print(f"  描述: {benefit['描述']}")
        print(f"  效果: {benefit['效果']}")

def test_proxy_configuration():
    """測試代理配置"""
    print(f"\n⚙️ 代理配置測試")
    print("-" * 40)
    
    config_types = [
        {
            "類型": "免費代理",
            "優點": ["成本低", "容易獲得", "適合測試"],
            "缺點": ["穩定性差", "速度慢", "可能不安全"],
            "適用": "開發測試環境"
        },
        {
            "類型": "付費代理",
            "優點": ["穩定性高", "速度快", "安全可靠", "技術支援"],
            "缺點": ["需要費用", "需要配置"],
            "適用": "生產環境"
        },
        {
            "類型": "住宅代理",
            "優點": ["真實IP", "不易被檢測", "地理位置多樣"],
            "缺點": ["價格較高", "速度可能較慢"],
            "適用": "需要高匿名性的場景"
        },
        {
            "類型": "數據中心代理",
            "優點": ["速度快", "價格相對便宜", "穩定性好"],
            "缺點": ["容易被識別", "可能被封鎖"],
            "適用": "大量數據抓取"
        }
    ]
    
    for config in config_types:
        print(f"\n📋 {config['類型']}")
        print(f"  優點: {', '.join(config['優點'])}")
        print(f"  缺點: {', '.join(config['缺點'])}")
        print(f"  適用: {config['適用']}")

def test_integration_scenarios():
    """測試整合場景"""
    print(f"\n🔗 整合場景測試")
    print("-" * 40)
    
    scenarios = [
        {
            "場景": "🕘 開盤時段高頻請求",
            "問題": "大量用戶同時請求導致API限制",
            "解決": "使用代理池分散請求，降低單一IP頻率",
            "效果": "避免429錯誤，提高成功率"
        },
        {
            "場景": "🌍 地理位置限制",
            "問題": "某些API限制特定地區訪問",
            "解決": "使用不同地區的代理服務器",
            "效果": "突破地理限制，獲取數據"
        },
        {
            "場景": "🚫 IP封鎖問題",
            "問題": "頻繁請求導致IP被暫時封鎖",
            "解決": "自動切換到其他可用代理",
            "效果": "保持服務連續性"
        },
        {
            "場景": "📊 大量股票監控",
            "問題": "監控多支股票時請求量大",
            "解決": "智能分配請求到不同代理",
            "效果": "提高並發處理能力"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['場景']}")
        print(f"  問題: {scenario['問題']}")
        print(f"  解決: {scenario['解決']}")
        print(f"  效果: {scenario['效果']}")

def test_proxy_recommendations():
    """測試代理推薦"""
    print(f"\n💡 代理服務推薦")
    print("-" * 40)
    
    recommendations = [
        {
            "服務": "亮數據 (Bright Data)",
            "類型": "住宅代理",
            "特點": ["全球IP池", "高匿名性", "企業級穩定性"],
            "價格": "較高",
            "適用": "專業數據抓取"
        },
        {
            "服務": "ProxyMesh",
            "類型": "數據中心代理",
            "特點": ["美國IP", "HTTP/HTTPS支援", "簡單易用"],
            "價格": "中等",
            "適用": "一般網路請求"
        },
        {
            "服務": "Smartproxy",
            "類型": "住宅+數據中心",
            "特點": ["多種代理類型", "全球覆蓋", "API友好"],
            "價格": "中等",
            "適用": "多樣化需求"
        },
        {
            "服務": "免費代理列表",
            "類型": "公共代理",
            "特點": ["免費使用", "數量多", "經常更新"],
            "價格": "免費",
            "適用": "測試和學習"
        }
    ]
    
    for rec in recommendations:
        print(f"\n🌐 {rec['服務']}")
        print(f"  類型: {rec['類型']}")
        print(f"  特點: {', '.join(rec['特點'])}")
        print(f"  價格: {rec['價格']}")
        print(f"  適用: {rec['適用']}")

def main():
    """主函數"""
    print("🚀 智能代理整合測試")
    print("=" * 80)
    
    try:
        # 測試代理管理器
        proxy_success = test_proxy_manager()
        
        # 測試股票數據獲取
        if proxy_success:
            test_stock_data_with_proxy()
        
        # 測試代理優勢
        test_proxy_benefits()
        
        # 測試代理配置
        test_proxy_configuration()
        
        # 測試整合場景
        test_integration_scenarios()
        
        # 測試代理推薦
        test_proxy_recommendations()
        
        print(f"\n" + "=" * 80)
        print(f"🎯 測試總結")
        print(f"=" * 80)
        
        if proxy_success:
            print(f"✅ 智能代理管理器整合成功")
            
            summary = [
                "🌐 智能代理池管理",
                "🔄 自動故障轉移",
                "📊 代理性能監控",
                "⚡ 請求頻率分散",
                "🛡️ 錯誤容忍機制",
                "🎯 最佳代理選擇"
            ]
            
            print(f"\n💡 主要功能:")
            for feature in summary:
                print(f"  {feature}")
            
            print(f"\n🚀 現在您可以:")
            next_steps = [
                "配置付費代理服務提高穩定性",
                "使用代理池避免IP限制",
                "享受更穩定的數據獲取體驗",
                "監控代理性能和成功率",
                "在高頻請求時保持服務穩定"
            ]
            
            for i, step in enumerate(next_steps, 1):
                print(f"  {i}. {step}")
        else:
            print(f"⚠️ 智能代理管理器載入失敗，使用直接連接")
        
        print(f"\n💡 配置建議:")
        suggestions = [
            "創建 proxy_config.json 文件配置付費代理",
            "根據使用量選擇合適的代理服務",
            "定期監控代理性能和成功率",
            "在生產環境中使用穩定的付費代理",
            "測試環境可以使用免費代理"
        ]
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
