#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修復後的月營收下載器
"""

import sys
import os

def test_basic_imports():
    """測試基本導入"""
    print("🔍 測試基本導入...")
    
    try:
        import pandas as pd
        print("✅ pandas 可用")
    except ImportError:
        print("❌ pandas 未安裝")
        return False
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        print("❌ requests 未安裝")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup 可用")
    except ImportError:
        print("❌ BeautifulSoup 未安裝，需要: pip install beautifulsoup4")
        return False
    
    return True

def test_downloader_import():
    """測試下載器導入"""
    print("\n📦 測試下載器導入...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        print("✅ 月營收下載器導入成功")
        
        # 創建實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        print("✅ 下載器實例創建成功")
        
        return downloader
        
    except Exception as e:
        print(f"❌ 下載器導入失敗: {e}")
        return None

def test_scraper_method(downloader):
    """測試網頁抓取方法"""
    print("\n🌐 測試網頁抓取方法...")
    
    try:
        # 測試台積電
        stock_id = '2330'
        print(f"正在測試 {stock_id}...")
        
        result = downloader.download_with_scraper(stock_id)
        
        if result:
            print(f"✅ 網頁抓取成功，獲得 {result} 筆數據")
            return True
        else:
            print("❌ 網頁抓取失敗")
            return False
            
    except Exception as e:
        print(f"❌ 網頁抓取測試失敗: {e}")
        return False

def test_database_query(downloader):
    """測試數據庫查詢"""
    print("\n🗄️ 測試數據庫查詢...")
    
    try:
        import sqlite3
        
        conn = sqlite3.connect(downloader.db_path)
        cursor = conn.cursor()
        
        # 查詢總記錄數
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
        total_count = cursor.fetchone()[0]
        print(f"✅ 數據庫總記錄數: {total_count}")
        
        if total_count > 0:
            # 查詢最新記錄
            cursor.execute("""
                SELECT stock_id, stock_name, year, month, revenue, revenue_yoy 
                FROM monthly_revenue 
                ORDER BY year DESC, month DESC 
                LIMIT 5
            """)
            
            recent_records = cursor.fetchall()
            print("✅ 最新5筆記錄:")
            
            for record in recent_records:
                stock_id, stock_name, year, month, revenue, yoy = record
                revenue_str = f"{revenue:,.0f}億" if revenue else "N/A"
                yoy_str = f"{yoy:+.1f}%" if yoy else "N/A"
                print(f"   {stock_id} {stock_name} {year}/{month:02d} - 營收: {revenue_str}, 年增率: {yoy_str}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 數據庫查詢失敗: {e}")
        return False

def test_gui_compatibility():
    """測試GUI兼容性"""
    print("\n🖥️ 測試GUI兼容性...")
    
    try:
        from monthly_revenue_downloader_gui import MonthlyRevenueDownloaderGUI
        print("✅ GUI模組導入成功")
        
        # 測試是否能創建GUI實例（不實際顯示）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隱藏視窗
        
        gui = MonthlyRevenueDownloaderGUI(root)
        print("✅ GUI實例創建成功")
        
        # 清理
        gui.window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 修復後月營收下載器測試")
    print("=" * 50)
    
    # 測試基本導入
    if not test_basic_imports():
        print("\n❌ 基本依賴缺失，請安裝必要的套件")
        return False
    
    # 測試下載器導入
    downloader = test_downloader_import()
    if not downloader:
        print("\n❌ 下載器導入失敗")
        return False
    
    # 測試網頁抓取方法
    scraper_success = test_scraper_method(downloader)
    
    # 測試數據庫查詢
    db_success = test_database_query(downloader)
    
    # 測試GUI兼容性
    gui_success = test_gui_compatibility()
    
    # 總結
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    
    tests = [
        ("基本導入", True),
        ("下載器導入", True),
        ("網頁抓取", scraper_success),
        ("數據庫查詢", db_success),
        ("GUI兼容性", gui_success)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(tests)} 項測試通過")
    
    if passed >= 3:  # 至少3項通過就算成功
        print("\n🎉 修復後的下載器基本功能正常！")
        print("\n📋 使用建議:")
        print("1. 現在可以在GUI中重新嘗試下載")
        print("2. 如果Selenium仍然失敗，系統會自動使用網頁抓取方法")
        print("3. 網頁抓取方法更穩定，但可能獲得的數據格式略有不同")
        
        if scraper_success:
            print("\n✅ 網頁抓取方法已驗證可用，可以作為主要下載方式")
        else:
            print("\n⚠️ 網頁抓取方法仍有問題，可能需要進一步調試")
        
        return True
    else:
        print(f"\n⚠️ 仍有 {len(tests) - passed} 項測試失敗")
        print("\n🔧 建議檢查:")
        print("- 網絡連接是否正常")
        print("- GoodInfo網站是否可以正常訪問")
        print("- 是否有防火牆阻擋")
        
        return False

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 測試完成！可以重新嘗試使用月營收下載器")
    else:
        print("❌ 測試未完全通過，可能需要進一步排查問題")
    
    input("\n按Enter鍵退出...")
    sys.exit(0 if success else 1)
