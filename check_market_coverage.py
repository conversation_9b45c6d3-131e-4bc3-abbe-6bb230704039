#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 TWSE 財務報表資料的市場覆蓋範圍
"""

import sqlite3
import pandas as pd
import os

def analyze_market_coverage():
    """分析市場覆蓋範圍"""
    
    print("=" * 80)
    print("📊 TWSE 財務報表資料市場覆蓋範圍分析")
    print("=" * 80)
    
    # 連接資料庫
    db_path = os.path.join('history', 'tables', 'financial_statements.db')
    if not os.path.exists(db_path):
        print(f"❌ 資料庫不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    
    try:
        # 1. 檢查總體資料
        query1 = "SELECT COUNT(*) as total_count FROM income_statements"
        total_count = pd.read_sql_query(query1, conn).iloc[0]['total_count']
        print(f"📈 總公司數量: {total_count:,}")
        
        # 2. 檢查股票代碼分布
        print(f"\n📊 股票代碼首位數字分布:")
        query2 = """
        SELECT 
            SUBSTR(stock_id, 1, 1) as first_digit,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM income_statements), 1) as percentage
        FROM income_statements 
        GROUP BY SUBSTR(stock_id, 1, 1)
        ORDER BY first_digit
        """
        
        df2 = pd.read_sql_query(query2, conn)
        for _, row in df2.iterrows():
            print(f"   {row['first_digit']}xxx: {row['count']:>4} 家 ({row['percentage']:>5.1f}%)")
        
        # 3. 分析市場類型
        print(f"\n🏢 市場類型分析:")
        
        # 上市公司通常是 1000-9999
        # 上櫃公司通常是 4位數，但有些特殊規則
        query3 = """
        SELECT 
            stock_id, stock_name,
            CASE 
                WHEN stock_id BETWEEN '1000' AND '9999' THEN '疑似上市'
                ELSE '其他'
            END as market_guess
        FROM income_statements 
        ORDER BY stock_id
        """
        
        df3 = pd.read_sql_query(query3, conn)
        market_distribution = df3['market_guess'].value_counts()
        
        for market_type, count in market_distribution.items():
            percentage = (count / len(df3)) * 100
            print(f"   {market_type}: {count:>4} 家 ({percentage:>5.1f}%)")
        
        # 4. 顯示代表性公司
        print(f"\n🔍 代表性公司範例:")
        
        # 顯示不同代碼段的公司
        code_ranges = [
            ('1000-1999', '1000', '1999'),
            ('2000-2999', '2000', '2999'),
            ('3000-3999', '3000', '3999'),
            ('4000-4999', '4000', '4999'),
            ('5000-5999', '5000', '5999'),
            ('6000-6999', '6000', '6999'),
        ]
        
        for range_name, start, end in code_ranges:
            query4 = f"""
            SELECT stock_id, stock_name
            FROM income_statements 
            WHERE stock_id BETWEEN '{start}' AND '{end}'
            ORDER BY stock_id
            LIMIT 5
            """
            
            df4 = pd.read_sql_query(query4, conn)
            if len(df4) > 0:
                print(f"\n   {range_name} 範例:")
                for _, row in df4.iterrows():
                    print(f"      {row['stock_id']} {row['stock_name']}")
        
        # 5. 檢查是否有上櫃公司特徵
        print(f"\n🔍 檢查上櫃公司特徵:")
        
        # 一些知名的上櫃公司代碼
        otc_samples = ['3443', '4966', '5269', '6488', '8069', '8299']
        
        for code in otc_samples:
            query5 = f"SELECT stock_id, stock_name FROM income_statements WHERE stock_id = '{code}'"
            result = pd.read_sql_query(query5, conn)
            
            if len(result) > 0:
                print(f"   ✅ 找到 {code} {result.iloc[0]['stock_name']} (上櫃公司)")
            else:
                print(f"   ❌ 未找到 {code} (上櫃公司)")
        
        # 6. API 端點分析
        print(f"\n🔗 API 端點分析:")
        print(f"   使用的 API: https://openapi.twse.com.tw/v1/opendata/t187ap06_L_ci")
        print(f"   API 說明: 綜合損益表 (一般業)")
        print(f"   '_L_ci' 可能表示: Listed Companies, Comprehensive Income")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

def check_twse_api_endpoints():
    """檢查 TWSE API 端點"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 檢查 TWSE API 端點")
    print("=" * 80)
    
    try:
        import requests
        
        base_url = "https://openapi.twse.com.tw/v1"
        
        # 測試不同的端點
        endpoints = [
            ("/opendata/t187ap06_L_ci", "綜合損益表 (一般業)"),
            ("/opendata/t187ap07_L_ci", "資產負債表 (一般業)"),
        ]
        
        for endpoint, description in endpoints:
            print(f"\n📡 測試端點: {endpoint}")
            print(f"   說明: {description}")
            
            try:
                url = f"{base_url}{endpoint}"
                resp = requests.get(url, timeout=10, verify=False)
                
                if resp.status_code == 200:
                    data = resp.json()
                    print(f"   ✅ 成功獲取資料: {len(data)} 筆")
                    
                    # 檢查第一筆資料的欄位
                    if data and len(data) > 0:
                        first_record = data[0]
                        print(f"   📋 欄位範例: {list(first_record.keys())[:5]}...")
                        
                        # 檢查是否有公司代號
                        if '公司代號' in first_record:
                            print(f"   🏢 第一家公司: {first_record['公司代號']} {first_record.get('公司名稱', 'N/A')}")
                else:
                    print(f"   ❌ HTTP 錯誤: {resp.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 請求失敗: {e}")
    
    except ImportError:
        print(f"❌ 無法導入 requests 模組")

def research_twse_otc_coverage():
    """研究 TWSE API 是否包含上櫃公司"""
    
    print(f"\n" + "=" * 80)
    print(f"📚 TWSE API 上市上櫃覆蓋研究")
    print("=" * 80)
    
    print(f"🔍 關於台灣證交所 OpenAPI:")
    print(f"   1. TWSE (Taiwan Stock Exchange) 主要負責上市公司")
    print(f"   2. TPEX (Taipei Exchange) 主要負責上櫃公司")
    print(f"   3. openapi.twse.com.tw 可能主要提供上市公司資料")
    print(f"   4. 上櫃公司資料可能需要從 TPEX API 獲取")
    
    print(f"\n💡 建議:")
    print(f"   1. 當前 TWSE API 主要包含上市公司")
    print(f"   2. 如需上櫃公司資料，可能需要額外的 TPEX API")
    print(f"   3. 或者使用原本的 MOPS 爬蟲作為補充")
    
    print(f"\n🎯 結論:")
    print(f"   目前的 twse_financial_statements 主要涵蓋上市公司")
    print(f"   如需完整的上市+上櫃覆蓋，建議:")
    print(f"   - 保留現有的 TWSE API 爬蟲 (上市公司)")
    print(f"   - 添加 TPEX API 爬蟲 (上櫃公司)")
    print(f"   - 或使用 MOPS 爬蟲作為全覆蓋方案")

def main():
    """主函數"""
    
    print("🔍 TWSE 財務報表市場覆蓋範圍檢查")
    
    # 分析現有資料
    analyze_market_coverage()
    
    # 檢查 API 端點
    check_twse_api_endpoints()
    
    # 研究覆蓋範圍
    research_twse_otc_coverage()
    
    print(f"\n" + "=" * 80)
    print(f"📊 總結")
    print("=" * 80)
    print(f"✅ 已完成市場覆蓋範圍分析")
    print(f"💡 請查看上述分析結果以了解資料範圍")

if __name__ == "__main__":
    main()
