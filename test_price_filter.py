#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 price 爬蟲的興櫃股票過濾功能
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

print(f"finlab 路径: {finlab_path}")
print(f"路径存在: {os.path.exists(finlab_path)}")

# 導入爬蟲函數
try:
    from crawler import crawl_price, get_cached_stock_info
    print("✅ 成功導入 crawl_price 函數")
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

def test_price_filter():
    """測試 price 爬蟲的興櫃股票過濾功能"""
    print("=" * 80)
    print("🧪 測試 price 爬蟲的興櫃股票過濾功能")
    print("=" * 80)
    
    # 使用最近的交易日進行測試
    test_date = datetime(2024, 12, 30)  # 使用一個已知的交易日
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
    print()
    
    try:
        # 先獲取股票資訊緩存
        print("📊 獲取股票資訊...")
        stock_info = get_cached_stock_info()
        
        if stock_info:
            print(f"✅ 獲取到 {len(stock_info)} 檔股票資訊")
            
            # 統計各市場別股票數量
            market_stats = {}
            for stock_id, info in stock_info.items():
                listing_status = info.get('listing_status', '未分類')
                market_stats[listing_status] = market_stats.get(listing_status, 0) + 1
            
            print("📈 股票市場分布:")
            for market, count in market_stats.items():
                print(f"   {market}: {count} 檔")
        else:
            print("⚠️ 未獲取到股票資訊")
        
        print()
        
        # 執行 price 爬蟲
        print("🚀 執行 price 爬蟲...")
        df = crawl_price(test_date)
        
        if not df.empty:
            print(f"✅ 成功獲取 {len(df)} 筆股價資料")
            
            # 重置索引以便分析
            df_reset = df.reset_index()
            
            # 檢查是否有 listing_status 欄位
            if 'listing_status' in df_reset.columns:
                print("\n📊 過濾後的股票市場分布:")
                listing_stats = df_reset['listing_status'].value_counts()
                for status, count in listing_stats.items():
                    print(f"   {status if status else 'ETF/未分類'}: {count} 檔")
                
                # 檢查是否還有興櫃股票
                rotc_count = len(df_reset[df_reset['listing_status'] == '興櫃'])
                if rotc_count == 0:
                    print("✅ 成功過濾所有興櫃股票")
                else:
                    print(f"⚠️ 仍有 {rotc_count} 檔興櫃股票未被過濾")
            else:
                print("⚠️ 資料中沒有 listing_status 欄位")
            
            # 顯示一些範例資料
            print("\n📋 前5筆資料範例:")
            print(df_reset[['stock_id', 'stock_name', 'listing_status', 'Close']].head())
            
            # 檢查特定股票類型
            print("\n🔍 檢查特定股票類型:")
            
            # 檢查是否有 ETF
            etf_stocks = df_reset[df_reset['stock_id'].str.startswith('00')]
            if not etf_stocks.empty:
                print(f"   ETF (00開頭): {len(etf_stocks)} 檔")
                print(f"   範例: {etf_stocks['stock_id'].head(3).tolist()}")
            
            # 檢查上市股票
            twse_stocks = df_reset[df_reset['listing_status'] == '上市']
            if not twse_stocks.empty:
                print(f"   上市股票: {len(twse_stocks)} 檔")
                print(f"   範例: {twse_stocks['stock_id'].head(3).tolist()}")
            
            # 檢查上櫃股票
            otc_stocks = df_reset[df_reset['listing_status'] == '上櫃']
            if not otc_stocks.empty:
                print(f"   上櫃股票: {len(otc_stocks)} 檔")
                print(f"   範例: {otc_stocks['stock_id'].head(3).tolist()}")
            
        else:
            print("❌ 未獲取到股價資料")
            
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_price_filter()
