#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收排行榜與PE資料的整合
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, date

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pe_integration():
    """測試PE資料整合"""
    print("=" * 60)
    print("🧪 測試月營收排行榜與PE資料整合")
    print("=" * 60)
    
    try:
        # 1. 測試月營收資料
        monthly_db_path = 'D:/Finlab/history/tables/monthly_report.db'
        if not os.path.exists(monthly_db_path):
            print(f"❌ 月營收資料庫不存在: {monthly_db_path}")
            return False
            
        # 2. 測試PE資料
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        if not os.path.exists(pe_db_path):
            print(f"❌ PE資料庫不存在: {pe_db_path}")
            return False
        
        print("✅ 兩個資料庫都存在")
        
        # 3. 獲取月營收資料樣本
        conn_monthly = sqlite3.connect(monthly_db_path)
        monthly_query = """
        SELECT stock_id, stock_name, 當月營收, 上月營收, 去年當月營收
        FROM monthly_report 
        WHERE date LIKE '2025-07%' 
        AND 當月營收 IS NOT NULL 
        LIMIT 10
        """
        monthly_df = pd.read_sql_query(monthly_query, conn_monthly)
        conn_monthly.close()
        
        if monthly_df.empty:
            print("❌ 沒有找到月營收資料")
            return False
            
        print(f"✅ 找到 {len(monthly_df)} 筆月營收資料")
        
        # 4. 獲取PE資料樣本
        conn_pe = sqlite3.connect(pe_db_path)
        
        # 找到最新的PE資料日期
        cursor = conn_pe.cursor()
        cursor.execute("SELECT MAX(date) FROM pe_data WHERE date LIKE '2025-07%'")
        latest_pe_date = cursor.fetchone()[0]
        
        if not latest_pe_date:
            print("❌ 沒有找到2025年7月的PE資料")
            conn_pe.close()
            return False
            
        print(f"✅ 最新PE資料日期: {latest_pe_date}")
        
        pe_query = """
        SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
        FROM pe_data 
        WHERE date = ?
        LIMIT 20
        """
        pe_df = pd.read_sql_query(pe_query, conn_pe, params=[latest_pe_date])
        conn_pe.close()
        
        print(f"✅ 找到 {len(pe_df)} 筆PE資料")
        
        # 5. 測試資料合併
        print("\n🔄 測試資料合併...")
        
        # 轉換月營收數值
        monthly_df['當月營收'] = pd.to_numeric(monthly_df['當月營收'], errors='coerce')
        monthly_df['上月營收'] = pd.to_numeric(monthly_df['上月營收'], errors='coerce')
        monthly_df['去年當月營收'] = pd.to_numeric(monthly_df['去年當月營收'], errors='coerce')
        
        # 計算YoY和MoM
        monthly_df['YoY%'] = ((monthly_df['當月營收'] - monthly_df['去年當月營收']) / monthly_df['去年當月營收'] * 100).round(2)
        monthly_df['MoM%'] = ((monthly_df['當月營收'] - monthly_df['上月營收']) / monthly_df['上月營收'] * 100).round(2)
        
        # 合併PE資料
        merged_df = monthly_df.merge(
            pe_df[['stock_id', 'dividend_yield', 'pe_ratio', 'pb_ratio']], 
            on='stock_id', 
            how='left'
        )
        
        print(f"✅ 合併完成: {len(merged_df)} 筆資料")
        
        # 6. 顯示整合後的資料格式
        print("\n📊 整合後的月營收排行榜格式:")
        print("-" * 150)
        print(f"{'排名':<4} {'代碼':<6} {'名稱':<10} {'西元年月':<8} {'當月營收':<12} {'上月營收':<12} {'去年同月':<12} {'YoY%':<8} {'MoM%':<8} {'殖利率':<8} {'本益比':<8} {'股淨比':<8}")
        print("-" * 150)
        
        # 按YoY排序取前5名
        sorted_df = merged_df.sort_values('YoY%', ascending=False).head(5)
        
        for idx, (_, row) in enumerate(sorted_df.iterrows(), 1):
            year_month = "202507"  # 2025年7月
            
            # 格式化PE數據
            dividend_yield = f"{row['dividend_yield']:.2f}" if pd.notna(row['dividend_yield']) else "N/A"
            pe_ratio = f"{row['pe_ratio']:.2f}" if pd.notna(row['pe_ratio']) else "N/A"
            pb_ratio = f"{row['pb_ratio']:.2f}" if pd.notna(row['pb_ratio']) else "N/A"
            
            print(f"{idx:<4} {row['stock_id']:<6} {row['stock_name'][:8]:<10} {year_month:<8} "
                  f"{row['當月營收']:>10,.0f} {row['上月營收']:>10,.0f} {row['去年當月營收']:>10,.0f} "
                  f"{row['YoY%']:>6.1f}% {row['MoM%']:>6.1f}% {dividend_yield:>6} {pe_ratio:>6} {pb_ratio:>6}")
        
        print("\n✅ PE資料整合測試成功！")
        
        # 7. 統計資料覆蓋率
        print(f"\n📈 資料覆蓋率統計:")
        total_stocks = len(merged_df)
        pe_coverage = len(merged_df[merged_df['pe_ratio'].notna()])
        dividend_coverage = len(merged_df[merged_df['dividend_yield'].notna()])
        pb_coverage = len(merged_df[merged_df['pb_ratio'].notna()])
        
        print(f"   總股票數: {total_stocks}")
        print(f"   本益比覆蓋率: {pe_coverage}/{total_stocks} ({pe_coverage/total_stocks*100:.1f}%)")
        print(f"   殖利率覆蓋率: {dividend_coverage}/{total_stocks} ({dividend_coverage/total_stocks*100:.1f}%)")
        print(f"   股價淨值比覆蓋率: {pb_coverage}/{total_stocks} ({pb_coverage/total_stocks*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_table_structure():
    """測試新的表格結構"""
    print("\n" + "=" * 60)
    print("📋 測試新的表格結構")
    print("=" * 60)
    
    # 模擬新的排行榜資料項目
    sample_data = {
        '排名': 1,
        '股票代碼': '2330',
        '股票名稱': '台積電',
        '西元年月': '202507',
        '當月營收': '120,000,000',
        '上個月營收': '115,000,000', 
        '去年同月營收': '100,000,000',
        'YoY%': '20.00%',
        'MoM%': '4.35%',
        '殖利率': '3.25',
        '本益比': '15.80',
        '股價淨值比': '2.45',
        'EPS': 'N/A',
        '綜合評分': '85.5'
    }
    
    print("🏆 新的排行榜資料項目範例:")
    for key, value in sample_data.items():
        print(f"   {key}: {value}")
    
    print(f"\n📊 新的表格結構:")
    print("一般排行榜 (13欄):")
    headers_normal = [
        "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)", 
        "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
        "殖利率(%)", "本益比", "股價淨值比", "EPS"
    ]
    for i, header in enumerate(headers_normal, 1):
        print(f"   {i:2d}. {header}")
    
    print("\n綜合評分排行榜 (14欄):")
    headers_score = headers_normal + ["綜合評分"]
    for i, header in enumerate(headers_score, 1):
        print(f"   {i:2d}. {header}")
    
    print("\n✅ 新增的PE相關欄位:")
    print("   - 殖利率(%): 顯示股票的殖利率")
    print("   - 本益比: 顯示股票的本益比")
    print("   - 股價淨值比: 顯示股票的PB比")
    print("   - EPS: 每股盈餘 (目前暫時顯示N/A)")
    
    return True

if __name__ == "__main__":
    print("🚀 開始測試月營收排行榜PE資料整合...")
    
    # 測試PE資料整合
    success1 = test_pe_integration()
    
    # 測試新表格結構
    success2 = test_new_table_structure()
    
    if success1 and success2:
        print("\n🎉 所有測試通過！月營收排行榜PE資料整合功能正常。")
    else:
        print("\n❌ 部分測試失敗，請檢查相關功能。")
