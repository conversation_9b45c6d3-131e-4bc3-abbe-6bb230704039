#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試日期重疊檢查功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class DateOverlapTestWindow(QMainWindow):
    """日期重疊檢查功能測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📅 日期重疊檢查功能測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("📅 日期重疊檢查功能測試")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4ff; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 功能說明
        info_text = QTextEdit()
        info_text.setMaximumHeight(300)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3 style="color: #ffffff;">🎯 新增功能說明</h3>
        
        <h4 style="color: #cccccc;">📊 智能日期重疊檢查</h4>
        <ul>
            <li><b>自動檢測</b> - 爬取前自動檢查數據庫中的現有日期範圍</li>
            <li><b>重疊提醒</b> - 發現重疊日期時彈出詳細的處理選項對話框</li>
            <li><b>智能建議</b> - 提供「跳過重疊」和「覆蓋重疊」兩種處理方式</li>
            <li><b>節省時間</b> - 避免重複下載已存在的數據</li>
        </ul>
        
        <h4 style="color: #cccccc;">🔍 現有數據檢查</h4>
        <ul>
            <li><b>數據範圍查看</b> - 點擊「檢查現有數據」按鈕查看各數據庫的日期範圍</li>
            <li><b>詳細統計</b> - 顯示總天數、記錄數、最近數據等詳細信息</li>
            <li><b>使用建議</b> - 提供數據下載和管理的最佳實踐建議</li>
        </ul>
        
        <h4 style="color: #cccccc;">⚡ 優化效果</h4>
        <ul>
            <li><b>避免重複下載</b> - 智能跳過已存在的日期，大幅節省下載時間</li>
            <li><b>用戶友好</b> - 清晰的提示和選項，讓用戶明確知道將要執行的操作</li>
            <li><b>數據完整性</b> - 確保不會意外覆蓋重要數據</li>
            <li><b>靈活選擇</b> - 用戶可根據需要選擇跳過或覆蓋重疊數據</li>
        </ul>
        
        <h3 style="color: #ffffff;">🧪 測試步驟</h3>
        
        <p style="color: #00ff00; font-weight: bold;">
        1. 點擊「檢查現有數據」查看數據庫狀態<br>
        2. 設置一個與現有數據重疊的日期範圍<br>
        3. 開始爬取，觀察重疊檢查對話框<br>
        4. 測試「跳過重疊」和「覆蓋重疊」兩種選項
        </p>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟台股爬蟲 (測試日期重疊檢查)")
        test_btn.clicked.connect(self.open_crawler_dialog)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 成功提示
        success_label = QLabel("🎉 日期重疊檢查功能已實現，避免重複下載數據！")
        success_label.setStyleSheet("color: #00ff00; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #2d4a2d; border: 1px solid #4a7c4a; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 初始化日誌
        self.log("📅 日期重疊檢查功能測試程式已啟動")
        self.log("✅ 智能日期重疊檢測已實現")
        self.log("✅ 現有數據範圍檢查已添加")
        self.log("✅ 用戶友好的處理選項已實現")
        self.log("✅ 避免重複下載機制已完成")
    
    def log(self, message):
        """添加日誌"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
    
    def get_timestamp(self):
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def open_crawler_dialog(self):
        """開啟爬蟲對話框"""
        try:
            self.log("🚀 正在開啟台股爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("📅 請測試以下功能：")
            self.log("   • 點擊「檢查現有數據」查看數據庫狀態")
            self.log("   • 設置重疊的日期範圍並開始爬取")
            self.log("   • 觀察日期重疊檢查對話框")
            self.log("   • 測試「跳過」和「覆蓋」選項")
            
            # 顯示對話框
            dialog.exec()
            
            self.log("✅ 爬蟲對話框已關閉")
            
        except Exception as e:
            self.log(f"❌ 開啟爬蟲界面失敗: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("日期重疊檢查功能測試")
    app.setApplicationVersion("1.0")
    
    print("📅 日期重疊檢查功能測試程式已啟動")
    print("✅ 智能日期重疊檢測已實現")
    print("✅ 避免重複下載機制已完成")
    
    # 創建主窗口
    window = DateOverlapTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
