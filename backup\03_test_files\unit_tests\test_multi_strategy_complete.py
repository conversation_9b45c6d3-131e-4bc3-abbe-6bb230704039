#!/usr/bin/env python3
"""
多策略整合分析系統完整測試
測試修復後的信號顯示、圖表完善和導出功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_test_data():
    """生成測試數據"""
    print("📊 生成測試數據...")
    
    # 生成6個月的測試數據
    dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
    dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 設置隨機種子確保可重現
    np.random.seed(42)
    
    # 初始價格
    base_price = 100
    prices = []
    
    for i in range(len(dates)):
        if i == 0:
            price = base_price
        else:
            # 添加趨勢和隨機波動
            trend = 0.001 * np.sin(i * 0.05)  # 長期趨勢
            cycle = 0.02 * np.sin(i * 0.3)    # 短期週期
            noise = np.random.normal(0, 0.015) # 隨機噪音
            price = prices[-1] * (1 + trend + cycle + noise)
        
        prices.append(max(price, 10))  # 確保價格不會太低
    
    # 生成OHLCV數據
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        volatility = 0.02
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
        
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        volume = np.random.randint(100000, 1000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    print(f"✅ 測試數據生成完成，共 {len(df)} 個交易日")
    return df

def test_signal_generation():
    """測試信號生成"""
    print("\n🎯 測試信號生成...")
    
    try:
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        
        # 生成測試數據
        test_data = generate_test_data()
        
        # 創建分析器
        analyzer = MultiStrategySignalAnalyzer()
        
        # 分析所有策略
        all_signals = analyzer.analyze_all_strategies(test_data)
        
        print("📋 信號生成結果:")
        for strategy_name, signals in all_signals.items():
            buy_count = len(signals['buy'])
            sell_count = len(signals['sell'])
            
            # 計算平均強度
            avg_buy_strength = np.mean([s['strength'] for s in signals['buy']]) if signals['buy'] else 0
            avg_sell_strength = np.mean([s['strength'] for s in signals['sell']]) if signals['sell'] else 0
            
            print(f"  🔸 {strategy_name}:")
            print(f"     買入信號: {buy_count} 個 (平均強度: {avg_buy_strength:.2f})")
            print(f"     賣出信號: {sell_count} 個 (平均強度: {avg_sell_strength:.2f})")
            
            # 顯示前3個買入信號的詳細信息
            if signals['buy']:
                print(f"     前3個買入信號:")
                for i, signal in enumerate(signals['buy'][:3]):
                    date = test_data.index[signal['index']].strftime('%Y-%m-%d')
                    print(f"       {i+1}. {date} - 價格: {signal['price']:.2f}, 強度: {signal['strength']:.2f}")
        
        return True, all_signals, test_data
        
    except Exception as e:
        print(f"❌ 信號生成測試失敗: {e}")
        return False, None, None

def test_chart_generation():
    """測試圖表生成"""
    print("\n📈 測試圖表生成...")
    
    try:
        # 使用之前的測試結果
        success, all_signals, test_data = test_signal_generation()
        if not success:
            return False
        
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        analyzer = MultiStrategySignalAnalyzer()
        
        # 生成圖表
        fig = analyzer.plot_multi_strategy_chart(
            test_data, 
            all_signals,
            "多策略整合測試圖表"
        )
        
        print("✅ 圖表生成成功")
        
        # 保存圖表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"多策略測試圖表_{timestamp}.png"
        fig.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 圖表已保存為: {filename}")
        
        plt.close(fig)  # 關閉圖表避免內存洩漏
        
        return True
        
    except Exception as e:
        print(f"❌ 圖表生成測試失敗: {e}")
        return False

def test_consensus_analysis():
    """測試一致性分析"""
    print("\n🎯 測試一致性分析...")
    
    try:
        # 使用之前的測試結果
        success, all_signals, test_data = test_signal_generation()
        if not success:
            return False
        
        from multi_strategy_chart import MultiStrategySignalAnalyzer
        analyzer = MultiStrategySignalAnalyzer()
        
        # 分析信號一致性
        consensus_points = analyzer.analyze_signal_consensus(all_signals, test_data, window=5)
        
        print(f"✅ 一致性分析完成，發現 {len(consensus_points)} 個高一致性點")
        
        # 顯示前5個一致性點
        if consensus_points:
            print("\n📋 高一致性信號點 (前5個):")
            for i, point in enumerate(consensus_points[:5]):
                date = test_data.index[point['index']].strftime('%Y-%m-%d')
                signal_type = "買入" if point['type'] == 'buy' else "賣出"
                emoji = "🟢" if point['type'] == 'buy' else "🔴"
                
                print(f"   {i+1}. {emoji} {date} - {signal_type}信號")
                print(f"      一致性: {point['consensus']}/4, 價格: {point['price']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 一致性分析測試失敗: {e}")
        return False

def test_gui_functionality():
    """測試GUI功能"""
    print("\n🖥️ 測試GUI功能...")
    
    try:
        from multi_strategy_gui import MultiStrategyGUI
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # 創建應用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 創建GUI窗口
        gui = MultiStrategyGUI()
        
        print("✅ GUI窗口創建成功")
        print("✅ 控制面板初始化完成")
        print("✅ 圖表組件初始化完成")
        print("✅ 信息面板初始化完成")
        
        # 測試股票列表載入
        if gui.stock_combo.count() > 1:
            print(f"✅ 股票列表載入成功，共 {gui.stock_combo.count()-1} 支股票")
        
        # 不顯示窗口，只測試功能
        # gui.show()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI功能測試失敗: {e}")
        return False

def test_export_functionality():
    """測試導出功能"""
    print("\n💾 測試導出功能...")
    
    try:
        # 使用之前的測試結果
        success, all_signals, test_data = test_signal_generation()
        if not success:
            return False
        
        from multi_strategy_gui import MultiStrategyGUI
        from PyQt6.QtWidgets import QApplication
        import tempfile
        import os
        
        # 創建應用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 創建GUI窗口
        gui = MultiStrategyGUI()
        
        # 設置測試數據
        gui.all_signals = all_signals
        gui.current_data = test_data
        gui.stock_combo.addItem("測試股票 (2330)", "2330")
        gui.stock_combo.setCurrentIndex(1)
        
        # 更新圖表
        gui.update_chart()
        
        # 測試Excel導出功能
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            gui._export_to_excel(tmp_file.name, True, True)
            
            if os.path.exists(tmp_file.name) and os.path.getsize(tmp_file.name) > 0:
                print("✅ Excel導出功能測試成功")
                os.unlink(tmp_file.name)  # 清理臨時文件
            else:
                print("❌ Excel導出功能測試失敗")
                return False
        
        # 測試圖表導出功能
        import tempfile
        import time

        temp_dir = tempfile.gettempdir()
        chart_filename = os.path.join(temp_dir, f"test_chart_{int(time.time())}.png")

        try:
            gui.figure.savefig(chart_filename, dpi=300, bbox_inches='tight')

            if os.path.exists(chart_filename) and os.path.getsize(chart_filename) > 0:
                print("✅ 圖表導出功能測試成功")
                try:
                    os.unlink(chart_filename)  # 清理臨時文件
                except:
                    pass  # 如果無法刪除，忽略錯誤
            else:
                print("❌ 圖表導出功能測試失敗")
                return False
        except Exception as e:
            print(f"❌ 圖表導出功能測試失敗: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 導出功能測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 多策略整合分析系統完整測試")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("信號生成", test_signal_generation),
        ("圖表生成", test_chart_generation),
        ("一致性分析", test_consensus_analysis),
        ("GUI功能", test_gui_functionality),
        ("導出功能", test_export_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            if test_name == "信號生成":
                result, _, _ = test_func()
            else:
                result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 測試結果總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！多策略整合分析系統功能完整且正常運作")
        print("\n💡 系統特色:")
        print("   • 🎯 四策略信號整合顯示")
        print("   • 📊 智能信號強度分析")
        print("   • 🔍 多策略一致性分析")
        print("   • 📈 專業級圖表視覺化")
        print("   • 💾 完整的導出功能")
        print("   • 🖥️ 直觀的GUI界面")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
