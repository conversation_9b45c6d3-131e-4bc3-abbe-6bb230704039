#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統一路徑配置
"""

import sys
import os
sys.path.append('strategies')

def test_unified_path():
    """測試統一路徑配置"""
    
    print("🧪 測試統一路徑配置")
    print("=" * 40)
    
    # 1. 檢查統一路徑
    unified_path = "D:/Finlab/history/tables"
    print(f"📂 統一資料庫路徑: {unified_path}")
    
    if os.path.exists(unified_path):
        print("✅ 統一路徑存在")
        
        # 檢查重要檔案
        important_files = ['pe.pkl', 'benchmark.pkl', 'price.pkl']
        for filename in important_files:
            file_path = os.path.join(unified_path, filename)
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"   ✅ {filename}: {size_mb:.1f} MB")
            else:
                print(f"   ❌ {filename}: 不存在")
    else:
        print("❌ 統一路徑不存在")
        return False
    
    # 2. 測試策略載入
    print(f"\n📊 測試策略載入...")
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        strategy = HighYieldTurtleStrategy()
        
        print(f"✅ 策略載入成功")
        print(f"📂 PE數據路徑: {strategy.pe_pkl_path}")
        
        if strategy.pe_data_cache is not None:
            print(f"📊 PE數據筆數: {len(strategy.pe_data_cache):,}")
            print(f"📈 涵蓋股票數: {strategy.pe_data_cache['股票代號'].nunique():,} 支")
        else:
            print("❌ PE數據未載入")
            return False
            
    except Exception as e:
        print(f"❌ 策略載入失敗: {e}")
        return False
    
    print(f"\n✅ 統一路徑配置測試成功！")
    return True

if __name__ == "__main__":
    success = test_unified_path()
    
    if success:
        print(f"\n🎉 統一資料庫路徑配置完成！")
        print(f"📂 所有資料庫檔案統一存放在: D:\\Finlab\\history\\tables")
        print(f"✅ 專案目錄名稱變更時不需要重複複製資料庫檔案")
        print(f"🎯 高殖利率烏龜策略可正常使用統一路徑的數據")
    else:
        print(f"\n❌ 統一路徑配置有問題，請檢查上述錯誤信息")
