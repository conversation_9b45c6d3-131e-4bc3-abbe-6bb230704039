#!/usr/bin/env python3
"""
測試自動執行策略功能
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def test_auto_execute_functionality():
    """測試自動執行策略功能"""
    print("🧪 測試自動執行策略功能")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        window.show()
        
        # 設置測試環境
        setup_test_environment(window)
        
        # 顯示測試指南
        show_test_guide(window)
        
        # 自動切換到策略交集標籤頁
        switch_to_intersection_tab(window)
        
        print("✅ 測試環境已準備完成")
        print("🎯 請按照指南進行測試")
        
        # 運行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def setup_test_environment(window):
    """設置測試環境"""
    try:
        import pandas as pd
        
        print("📊 設置測試環境...")
        
        # 只添加一個策略的結果，其他策略保持未執行狀態
        canslim_data = pd.DataFrame({
            '股票代碼': ['2330', '2317', '2454'],
            '股票名稱': ['台積電', '鴻海', '聯發科'],
            '收盤價': [500, 100, 800],
            '成交量': [50000, 30000, 20000],
            '符合條件': [True, True, True]
        })
        
        # 只保存一個策略結果，模擬其他策略未執行的情況
        window.strategy_results_cache['CANSLIM量價齊升'] = canslim_data
        
        print("✅ 測試環境設置完成")
        print(f"  📈 已執行策略: CANSLIM量價齊升 ({len(canslim_data)} 支股票)")
        print(f"  ⏳ 未執行策略: 藏獒, 二次創高股票, 膽小貓")
        
        return True
        
    except Exception as e:
        print(f"❌ 設置測試環境失敗: {e}")
        return False

def show_test_guide(window):
    """顯示測試指南"""
    guide_text = """
🧪 自動執行策略功能測試

📊 測試環境說明：
• 已執行：CANSLIM量價齊升 (3支股票)
• 未執行：藏獒、二次創高股票、膽小貓

🔍 測試步驟：
1. 切換到「🔗 策略交集」標籤頁
2. 勾選多個策略（包括未執行的）
3. 點擊「🎯 計算交集」
4. 觀察程式是否詢問自動執行
5. 選擇「是」測試自動執行功能

💡 預期行為：
• 程式會檢測到未執行的策略
• 彈出確認對話框詢問是否自動執行
• 選擇「是」後會自動執行缺失的策略
• 執行完成後自動進行交集分析

⚠️ 注意事項：
• 自動執行需要數據庫連接正常
• 執行過程中會顯示進度對話框
• 可以隨時取消執行過程

點擊「確定」開始測試！
"""
    
    QMessageBox.information(window, "🧪 自動執行策略測試", guide_text)

def switch_to_intersection_tab(window):
    """自動切換到策略交集標籤頁"""
    try:
        def switch_tab():
            tab_widget = window.tab_widget
            for i in range(tab_widget.count()):
                if "策略交集" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    print(f"✅ 已切換到策略交集標籤頁")
                    
                    # 預選一些策略（包括未執行的）
                    if hasattr(window, 'intersection_strategy_vars'):
                        strategies_to_select = ['CANSLIM量價齊升', '藏獒', '二次創高股票']
                        for strategy in strategies_to_select:
                            if strategy in window.intersection_strategy_vars:
                                window.intersection_strategy_vars[strategy].setChecked(True)
                        print("✅ 已預選測試策略組合（包含未執行的策略）")
                    break
        
        # 延遲500毫秒執行
        QTimer.singleShot(500, switch_tab)
        
    except Exception as e:
        print(f"⚠️ 切換標籤頁失敗: {e}")

def create_test_scenarios():
    """創建測試場景說明"""
    scenarios = """
# 🧪 自動執行策略功能測試場景

## 測試場景1: 部分策略未執行
**設置**: 只執行CANSLIM，其他策略未執行
**操作**: 選擇CANSLIM + 藏獒 + 二次創高進行交集分析
**預期**: 程式詢問是否自動執行藏獒和二次創高

## 測試場景2: 全部策略未執行
**設置**: 清空所有策略結果緩存
**操作**: 選擇任意策略組合進行交集分析
**預期**: 程式詢問是否自動執行所有選中的策略

## 測試場景3: 用戶取消自動執行
**設置**: 部分策略未執行
**操作**: 選擇包含未執行策略的組合，點擊「否」
**預期**: 程式停止交集分析，提示用戶手動執行

## 測試場景4: 自動執行過程中取消
**設置**: 多個策略未執行
**操作**: 選擇自動執行，然後在進度對話框中點擊取消
**預期**: 程式停止執行，已執行的策略保留結果

## 測試場景5: 自動執行失敗處理
**設置**: 數據庫連接異常或策略配置錯誤
**操作**: 嘗試自動執行策略
**預期**: 程式顯示錯誤信息，提供友好的失敗處理

## 驗證要點

### 用戶體驗
- [ ] 對話框信息清晰易懂
- [ ] 進度顯示準確
- [ ] 可以隨時取消操作
- [ ] 錯誤信息友好

### 功能正確性
- [ ] 正確檢測未執行的策略
- [ ] 自動執行策略成功
- [ ] 執行結果正確保存到緩存
- [ ] 執行完成後自動進行交集分析

### 異常處理
- [ ] 數據庫連接失敗處理
- [ ] 策略執行失敗處理
- [ ] 用戶取消操作處理
- [ ] 部分成功部分失敗的處理

## 測試數據

### 模擬策略結果
```python
# CANSLIM量價齊升 (已執行)
['2330', '2317', '2454'] # 台積電、鴻海、聯發科

# 藏獒 (未執行，需要自動執行)
['2330', '2454', '2327', '3008', '1590']

# 二次創高股票 (未執行，需要自動執行)
['2330', '2317', '3008', '2412', '2881']
```

### 預期交集結果
- CANSLIM + 藏獒: ['2330', '2454']
- CANSLIM + 二次創高: ['2330', '2317']
- 三策略交集: ['2330']
"""
    
    with open("自動執行策略測試場景.md", "w", encoding="utf-8") as f:
        f.write(scenarios)
    
    print("📖 測試場景說明已保存到: 自動執行策略測試場景.md")

def main():
    """主函數"""
    print("🚀 啟動自動執行策略功能測試")
    print("=" * 50)
    
    # 創建測試場景說明
    create_test_scenarios()
    
    # 啟動測試
    test_auto_execute_functionality()

if __name__ == "__main__":
    main()
