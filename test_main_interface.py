#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試主界面
"""

import sys
from PyQt6.QtWidgets import QApplication

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    try:
        from twse_market_data_dialog import TWSEMarketDataDialog
        
        print("🚀 正在開啟台灣證交所爬蟲界面...")
        print("📋 請檢查複選框文字是否顯示：")
        print("1. 📈 市場指數資訊 (即時更新)")
        print("2. 💰 融資融券統計 (每日更新)")
        print("3. 📊 發行量加權股價指數歷史資料 (歷史數據)")
        
        # 創建對話框
        dialog = TWSEMarketDataDialog()
        dialog.show()
        
        print("✅ 界面已成功創建並顯示")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
