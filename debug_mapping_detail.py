#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
詳細調試股票代碼映射
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_mapping_detail():
    """詳細調試映射過程"""
    
    print("=" * 60)
    print("🔍 詳細調試股票代碼映射")
    print("=" * 60)
    
    try:
        from crawler import fetch_stock_info_for_price
        
        # 獲取股票資訊
        print("📡 獲取股票資訊...")
        stock_info = fetch_stock_info_for_price()
        
        print(f"📊 獲取到 {len(stock_info)} 檔股票資訊")
        
        # 檢查特定股票代碼
        test_codes = ['1101', '2330', '0050']
        
        print(f"\n🔍 檢查特定股票代碼:")
        for code in test_codes:
            print(f"\n   測試代碼: '{code}' (類型: {type(code)})")
            
            # 直接查找
            if code in stock_info:
                info = stock_info[code]
                print(f"   ✅ 直接找到: {info}")
            else:
                print(f"   ❌ 直接未找到")
            
            # 檢查所有鍵
            matching_keys = [k for k in stock_info.keys() if k == code]
            print(f"   匹配的鍵: {matching_keys}")
            
            # 檢查相似的鍵
            similar_keys = [k for k in stock_info.keys() if code in k or k in code]
            print(f"   相似的鍵: {similar_keys[:5]}")  # 只顯示前5個
        
        # 檢查 stock_info 的鍵格式
        print(f"\n📋 stock_info 鍵格式範例:")
        keys_sample = list(stock_info.keys())[:10]
        for key in keys_sample:
            print(f"   '{key}' (類型: {type(key)}, 長度: {len(key)})")
        
        # 測試映射函數
        print(f"\n🧪 測試映射函數:")
        test_mapping = lambda x: stock_info.get(str(x), {}).get('stock_name', 'NOT_FOUND')
        
        for code in test_codes:
            result = test_mapping(code)
            print(f"   {code} -> '{result}'")
        
        # 檢查是否有編碼問題
        print(f"\n🔍 檢查編碼問題:")
        for code in test_codes:
            str_code = str(code)
            print(f"   原始: '{code}' -> 字符串: '{str_code}' -> 編碼: {str_code.encode('utf-8')}")
            
            # 檢查 stock_info 中的鍵編碼
            for key in list(stock_info.keys())[:3]:
                if key.startswith(code[0]):
                    print(f"   stock_info 鍵: '{key}' -> 編碼: {key.encode('utf-8')}")
                    break
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_mapping_detail()
