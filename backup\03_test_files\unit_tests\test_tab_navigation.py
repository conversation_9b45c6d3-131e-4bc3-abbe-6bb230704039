#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試活頁跳轉功能
驗證策略執行完成後的活頁切換
"""
import sys
import os
import logging
from datetime import datetime

def test_tab_structure():
    """測試活頁結構"""
    print("🚀 測試活頁結構...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入必要模組
        from PyQt6.QtWidgets import QApplication, QTabWidget
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 導入主程式
        import O3mh_gui_v21_optimized
        
        print("✅ 主程式導入成功")
        
        # 創建主視窗實例（不顯示）
        main_window = O3mh_gui_v21_optimized.StockScreenerGUI()
        
        print("✅ 主視窗創建成功")
        
        # 檢查活頁結構
        tab_widgets = main_window.findChildren(QTabWidget)
        
        print(f"📊 找到 {len(tab_widgets)} 個活頁組件")
        
        for i, tab_widget in enumerate(tab_widgets):
            print(f"\n📋 活頁組件 {i+1}:")
            tab_count = tab_widget.count()
            print(f"  活頁數量: {tab_count}")
            
            for j in range(tab_count):
                tab_text = tab_widget.tabText(j)
                print(f"  索引 {j}: {tab_text}")
        
        # 檢查主要活頁組件
        if hasattr(main_window, 'tab_widget'):
            print(f"\n🎯 右側主活頁組件:")
            right_tab_count = main_window.tab_widget.count()
            print(f"  活頁數量: {right_tab_count}")
            
            for j in range(right_tab_count):
                tab_text = main_window.tab_widget.tabText(j)
                print(f"  索引 {j}: {tab_text}")
                if "選股結果" in tab_text:
                    print(f"    ✅ 找到選股結果活頁，索引: {j}")
        
        return True
        
    except Exception as e:
        print(f"❌ 活頁結構測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tab_navigation_logic():
    """測試活頁跳轉邏輯"""
    print("\n🚀 測試活頁跳轉邏輯...")
    
    try:
        # 檢查修復後的代碼
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查策略完成後的跳轉代碼
        navigation_patterns = [
            "策略完成後跳轉到選股結果活頁",
            "left_tab_widget.setCurrentIndex(1)",
            "self.tab_widget.setCurrentIndex(2)",
            "交集分析完成後跳轉到選股結果活頁"
        ]
        
        found_patterns = []
        for pattern in navigation_patterns:
            if pattern in content:
                found_patterns.append(pattern)
                print(f"  ✅ 找到: {pattern}")
            else:
                print(f"  ❌ 缺失: {pattern}")
        
        if len(found_patterns) >= 3:
            print("✅ 活頁跳轉邏輯檢查通過")
            return True
        else:
            print("❌ 活頁跳轉邏輯檢查失敗")
            return False
        
    except Exception as e:
        print(f"❌ 活頁跳轉邏輯測試失敗: {e}")
        return False

def test_strategy_completion_flow():
    """測試策略完成流程"""
    print("\n🚀 測試策略完成流程...")
    
    try:
        # 檢查 run_strategy 方法中的跳轉邏輯
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找 run_strategy 方法
        run_strategy_lines = []
        in_run_strategy = False
        
        for i, line in enumerate(lines):
            if 'def run_strategy(self):' in line:
                in_run_strategy = True
                run_strategy_lines.append((i+1, line.strip()))
            elif in_run_strategy and line.strip().startswith('def ') and 'run_strategy' not in line:
                break
            elif in_run_strategy:
                run_strategy_lines.append((i+1, line.strip()))
        
        # 檢查跳轉邏輯
        has_left_tab_jump = False
        has_right_tab_jump = False
        
        for line_num, line in run_strategy_lines:
            if 'left_tab_widget.setCurrentIndex(1)' in line:
                has_left_tab_jump = True
                print(f"  ✅ 第 {line_num} 行: 左側活頁跳轉邏輯")
            elif 'self.tab_widget.setCurrentIndex(2)' in line:
                has_right_tab_jump = True
                print(f"  ✅ 第 {line_num} 行: 右側活頁跳轉邏輯")
        
        if has_left_tab_jump and has_right_tab_jump:
            print("✅ 策略完成流程檢查通過")
            return True
        else:
            print("❌ 策略完成流程檢查失敗")
            print(f"  左側活頁跳轉: {'✅' if has_left_tab_jump else '❌'}")
            print(f"  右側活頁跳轉: {'✅' if has_right_tab_jump else '❌'}")
            return False
        
    except Exception as e:
        print(f"❌ 策略完成流程測試失敗: {e}")
        return False

def test_intersection_analysis_flow():
    """測試策略交集分析流程"""
    print("\n🚀 測試策略交集分析流程...")
    
    try:
        # 檢查交集分析完成後的跳轉邏輯
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找交集分析相關的跳轉邏輯
        intersection_patterns = [
            "交集分析完成後跳轉到選股結果活頁",
            "intersection_count > 0"
        ]
        
        found_intersection_logic = False
        for pattern in intersection_patterns:
            if pattern in content:
                found_intersection_logic = True
                print(f"  ✅ 找到交集分析邏輯: {pattern}")
        
        if found_intersection_logic:
            print("✅ 策略交集分析流程檢查通過")
            return True
        else:
            print("❌ 策略交集分析流程檢查失敗")
            return False
        
    except Exception as e:
        print(f"❌ 策略交集分析流程測試失敗: {e}")
        return False

def generate_tab_navigation_summary():
    """生成活頁跳轉功能總結"""
    print("\n📋 活頁跳轉功能總結")
    print("=" * 50)
    
    print("🎯 修復內容:")
    print("1. 策略執行完成後自動跳轉")
    print("   - 左側活頁 → 選股結果 (索引1)")
    print("   - 右側活頁 → 選股結果 (索引2)")
    
    print("\n2. 策略交集分析完成後自動跳轉")
    print("   - 左側活頁 → 選股結果 (索引1)")
    print("   - 右側活頁 → 選股結果 (索引2)")
    
    print("\n📊 活頁結構:")
    print("左側活頁 (stock_tabs):")
    print("  索引 0: 全部股票")
    print("  索引 1: 選股結果 ✅")
    print("  索引 2: 資訊面板")
    
    print("\n右側活頁 (tab_widget):")
    print("  索引 0: K線圖")
    print("  索引 1: 盤中監控")
    print("  索引 2: 選股結果 ✅")
    print("  索引 3: 🔗 策略交集")
    
    print("\n🔧 修復位置:")
    print("1. run_strategy() 方法 (第一個)")
    print("2. run_strategy() 方法 (第二個)")
    print("3. calculate_strategy_intersection() 方法")
    
    print("\n✅ 預期效果:")
    print("- 執行任何策略後，自動切換到選股結果頁面")
    print("- 執行策略交集分析後，自動切換到選股結果頁面")
    print("- 用戶無需手動切換活頁即可查看結果")

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 活頁跳轉功能測試")
    print("=" * 60)
    
    success1 = test_tab_structure()
    success2 = test_tab_navigation_logic()
    success3 = test_strategy_completion_flow()
    success4 = test_intersection_analysis_flow()
    
    overall_success = success1 and success2 and success3 and success4
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 活頁跳轉功能測試通過！")
        print("✅ 活頁結構正確")
        print("✅ 跳轉邏輯完整")
        print("✅ 策略完成流程正確")
        print("✅ 交集分析流程正確")
        print("✅ 策略執行完成後會自動跳轉到選股結果頁面")
    else:
        print("❌ 活頁跳轉功能測試失敗")
        print("需要進一步檢查問題")
    
    generate_tab_navigation_summary()
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
