#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試英文欄位名稱和優化格式的除權息資料
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def clean_database_for_english_test():
    """清理資料庫以測試英文欄位格式"""
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if os.path.exists(db_file):
        try:
            os.remove(db_file)
            print(f"🗑️ 已清理現有資料庫以測試英文欄位: {db_file}")
            return True
        except Exception as e:
            print(f"❌ 清理資料庫失敗: {e}")
            return False
    else:
        print(f"ℹ️ 資料庫檔案不存在，無需清理")
        return True

def test_english_columns_crawler():
    """測試英文欄位名稱的除權息爬蟲"""
    
    print("=" * 80)
    print("🧪 測試英文欄位名稱的除權息爬蟲")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_divide_ratio
        print("✅ 成功導入 crawl_divide_ratio 函數")
        
        # 執行爬蟲
        print("🔄 開始執行統一除權息爬蟲...")
        result = crawl_divide_ratio()
        
        if result is not None and not result.empty:
            print(f"✅ 爬蟲執行成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 檢查英文欄位名稱
            print(f"\n📋 欄位名稱檢查:")
            
            expected_columns = [
                'stock_name', 'close_price_before_ex', 'ex_dividend_reference_price',
                'opening_reference_price', 'divide_ratio', 'market'
            ]
            
            for col in expected_columns:
                if col in result.columns:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ 缺少 {col}")
            
            # 檢查不應該存在的中文欄位
            chinese_columns = ['股票代號', '股票名稱', '資料日期', '除權息前收盤價', '開盤競價基準']
            found_chinese = [col for col in chinese_columns if col in result.columns]
            
            if found_chinese:
                print(f"   ⚠️ 仍包含中文欄位: {found_chinese}")
            else:
                print(f"   ✅ 已移除所有中文欄位")
            
            # 檢查索引結構
            index_names = result.index.names
            print(f"\n📊 索引結構: {index_names}")
            
            # 檢查 stock_name 位置
            columns_list = list(result.columns)
            if 'stock_name' in columns_list:
                stock_name_position = columns_list.index('stock_name')
                print(f"📍 stock_name 位置: 第 {stock_name_position + 1} 欄")
                
                if stock_name_position == 0:  # stock_id 是索引，所以 stock_name 應該是第一欄
                    print(f"   ✅ stock_name 位置正確 (緊鄰 stock_id)")
                else:
                    print(f"   ⚠️ stock_name 位置可能需要調整")
            
            # 顯示完整欄位列表
            print(f"\n📋 完整欄位列表:")
            for i, col in enumerate(result.columns, 1):
                print(f"   {i:2d}. {col}")
            
            # 檢查日期格式
            sample_dates = result.index.get_level_values('date').unique()[:3]
            print(f"\n📅 日期格式檢查:")
            for date in sample_dates:
                print(f"   {date} (類型: {type(date)})")
            
            # 檢查是否只包含日期 (不含時分秒)
            if len(sample_dates) > 0:
                sample_date = sample_dates[0]
                if hasattr(sample_date, 'hour'):
                    print(f"   ⚠️ 日期包含時分秒資訊")
                else:
                    print(f"   ✅ 日期格式正確 (只包含年月日)")
            
            # 顯示範例資料
            print(f"\n📊 前3筆資料範例:")
            sample_df = result.head(3).reset_index()
            
            # 選擇關鍵欄位顯示
            key_columns = ['stock_id', 'stock_name', 'date', 'market', 'close_price_before_ex', 'opening_reference_price', 'divide_ratio']
            available_columns = [col for col in key_columns if col in sample_df.columns]
            
            print(sample_df[available_columns].to_string(index=False))
            
            return True
        else:
            print(f"⚠️ 爬蟲執行返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_english_columns():
    """檢查資料庫中的英文欄位"""
    
    print(f"\n" + "=" * 80)
    print("📊 檢查資料庫中的英文欄位")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            
            # 檢查表格結構
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(divide_ratio)")
            columns = cursor.fetchall()
            
            print(f"📋 資料庫表格結構 (英文欄位):")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查關鍵英文欄位
            column_names = [col[1] for col in columns]
            
            expected_english_columns = [
                'stock_id', 'stock_name', 'date', 'market',
                'close_price_before_ex', 'opening_reference_price', 'divide_ratio'
            ]
            
            print(f"\n📋 關鍵英文欄位檢查:")
            for col in expected_english_columns:
                if col in column_names:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ 缺少 {col}")
            
            # 檢查是否還有中文欄位
            chinese_patterns = ['股票', '除權', '開盤', '資料']
            chinese_columns = [col for col in column_names if any(pattern in col for pattern in chinese_patterns)]
            
            if chinese_columns:
                print(f"\n⚠️ 仍包含中文欄位:")
                for col in chinese_columns:
                    print(f"   - {col}")
            else:
                print(f"\n✅ 已完全移除中文欄位")
            
            # 檢查資料範例
            cursor.execute("SELECT stock_id, stock_name, date, market, close_price_before_ex, opening_reference_price FROM divide_ratio LIMIT 5")
            sample_data = cursor.fetchall()
            
            print(f"\n📊 前5筆資料範例:")
            print(f"{'stock_id':<8} {'stock_name':<12} {'date':<12} {'market':<6} {'close_price':<12} {'open_ref':<10}")
            print("-" * 70)
            for row in sample_data:
                stock_id = str(row[0])[:8]
                stock_name = str(row[1])[:12] if row[1] else 'N/A'
                date = str(row[2])[:10]
                market = str(row[3])
                close_price = f"{row[4]:.2f}" if row[4] else 'N/A'
                open_ref = f"{row[5]:.2f}" if row[5] else 'N/A'
                print(f"{stock_id:<8} {stock_name:<12} {date:<12} {market:<6} {close_price:<12} {open_ref:<10}")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 檢查資料庫失敗: {e}")
            return False
    else:
        print(f"❌ 資料庫檔案不存在: {db_file}")
        return False

def test_sql_queries_english():
    """測試英文欄位的 SQL 查詢"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試英文欄位的 SQL 查詢")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    if not os.path.exists(db_file):
        print(f"❌ 資料庫檔案不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 測試查詢1: 使用英文欄位查詢台積電
        print(f"🔍 測試查詢1: 使用英文欄位查詢台積電")
        query1 = """
        SELECT stock_id, stock_name, date, close_price_before_ex, opening_reference_price, divide_ratio 
        FROM divide_ratio 
        WHERE stock_id = '2330' 
        ORDER BY date DESC 
        LIMIT 3
        """
        
        cursor.execute(query1)
        results1 = cursor.fetchall()
        
        if results1:
            print(f"   ✅ 查詢成功，找到 {len(results1)} 筆資料")
            for row in results1:
                print(f"      {row[0]} {row[1]} {row[2]} 收盤:{row[3]} 基準:{row[4]} 比率:{row[5]:.4f}")
        else:
            print(f"   ⚠️ 未找到台積電資料")
        
        # 測試查詢2: 計算平均除權息比率
        print(f"\n🔍 測試查詢2: 計算各市場平均除權息比率")
        query2 = """
        SELECT market, 
               COUNT(*) as count,
               AVG(divide_ratio) as avg_ratio,
               MIN(divide_ratio) as min_ratio,
               MAX(divide_ratio) as max_ratio
        FROM divide_ratio 
        WHERE divide_ratio IS NOT NULL
        GROUP BY market
        """
        
        cursor.execute(query2)
        results2 = cursor.fetchall()
        
        if results2:
            print(f"   ✅ 查詢成功")
            print(f"   {'市場':<6} {'筆數':<8} {'平均比率':<10} {'最小比率':<10} {'最大比率':<10}")
            print(f"   {'-'*50}")
            for row in results2:
                market_name = '上市' if row[0] == 'TWSE' else '上櫃'
                print(f"   {market_name:<6} {row[1]:<8} {row[2]:.4f}    {row[3]:.4f}    {row[4]:.4f}")
        else:
            print(f"   ⚠️ 未找到統計資料")
        
        # 測試查詢3: 查詢特定日期範圍
        print(f"\n🔍 測試查詢3: 查詢2024年除權息資料")
        query3 = """
        SELECT COUNT(*) as count_2024
        FROM divide_ratio 
        WHERE date >= '2024-01-01' AND date <= '2024-12-31'
        """
        
        cursor.execute(query3)
        result3 = cursor.fetchone()
        
        if result3:
            print(f"   ✅ 2024年除權息資料: {result3[0]:,} 筆")
        else:
            print(f"   ⚠️ 未找到2024年資料")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL 查詢測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_english_usage_examples():
    """顯示英文欄位的使用範例"""
    
    print(f"\n" + "=" * 80)
    print("📝 英文欄位使用範例")
    print("=" * 80)
    
    print("🔍 **SQL 查詢範例 (英文欄位)**:")
    print("```sql")
    print("-- 查詢台積電除權息資料")
    print("SELECT stock_id, stock_name, date, close_price_before_ex, opening_reference_price, divide_ratio")
    print("FROM divide_ratio")
    print("WHERE stock_id = '2330'")
    print("ORDER BY date DESC;")
    print()
    print("-- 查詢上市公司除權息資料")
    print("SELECT stock_id, stock_name, date, divide_ratio")
    print("FROM divide_ratio")
    print("WHERE market = 'TWSE'")
    print("ORDER BY divide_ratio DESC;")
    print()
    print("-- 計算除權息比率統計")
    print("SELECT market,")
    print("       COUNT(*) as total_records,")
    print("       AVG(divide_ratio) as avg_ratio,")
    print("       STDDEV(divide_ratio) as std_ratio")
    print("FROM divide_ratio")
    print("GROUP BY market;")
    print("```")
    print()
    
    print("🐍 **Python 使用範例**:")
    print("```python")
    print("import sqlite3")
    print("import pandas as pd")
    print()
    print("# 連接資料庫")
    print("conn = sqlite3.connect('D:/Finlab/history/tables/divide_ratio.db')")
    print()
    print("# 查詢台積電資料")
    print("df = pd.read_sql('''")
    print("    SELECT stock_id, stock_name, date, close_price_before_ex, divide_ratio")
    print("    FROM divide_ratio")
    print("    WHERE stock_id = '2330'")
    print("    ORDER BY date DESC")
    print("''', conn)")
    print()
    print("# 計算統計資訊")
    print("stats = df['divide_ratio'].describe()")
    print("print(stats)")
    print()
    print("conn.close()")
    print("```")

def main():
    """主函數"""
    
    print("🧪 英文欄位名稱和格式優化測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步驟1: 清理資料庫
    clean_success = clean_database_for_english_test()
    
    # 步驟2: 測試英文欄位爬蟲
    crawler_success = test_english_columns_crawler()
    
    # 步驟3: 檢查資料庫英文欄位
    database_success = check_database_english_columns()
    
    # 步驟4: 測試英文欄位 SQL 查詢
    query_success = test_sql_queries_english()
    
    # 顯示使用範例
    show_english_usage_examples()
    
    print(f"\n" + "=" * 80)
    print("📊 英文欄位測試結果總結")
    print("=" * 80)
    
    print(f"🗑️ 資料庫清理: {'✅ 成功' if clean_success else '❌ 失敗'}")
    print(f"🧪 英文欄位爬蟲: {'✅ 成功' if crawler_success else '❌ 失敗'}")
    print(f"📊 資料庫英文欄位: {'✅ 成功' if database_success else '❌ 失敗'}")
    print(f"🔍 英文欄位查詢: {'✅ 成功' if query_success else '❌ 失敗'}")
    
    if all([clean_success, crawler_success, database_success, query_success]):
        print(f"\n🎉 英文欄位格式優化測試全部通過！")
        print(f"💡 優化特點:")
        print(f"   ✅ 所有欄位名稱改為英文")
        print(f"   ✅ 移除重複的股票代號欄位")
        print(f"   ✅ stock_name 位於 stock_id 右側")
        print(f"   ✅ 移除資料日期欄位")
        print(f"   ✅ date 只包含年月日 (無時分秒)")
        print(f"   ✅ 統一的除權息比率欄位 (divide_ratio)")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
