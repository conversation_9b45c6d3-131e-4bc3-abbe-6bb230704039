#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析為什麼某些股票沒有月營收資料的真實原因
"""

import pandas as pd
import sqlite3
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_data_sources():
    """檢查所有可能的資料來源"""
    print("=" * 80)
    print("🔍 檢查月營收資料來源")
    print("=" * 80)
    
    # 檢查資料庫檔案
    db_paths = [
        'D:/Finlab/history/tables/monthly_report.db',
        'history/tables/monthly_report.db'
    ]
    
    # 檢查PKL檔案
    pkl_paths = [
        'D:/Finlab/history/tables/monthly_report_new.pkl',
        'D:/Finlab/history/tables/monthly_report.pkl',
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl'
    ]
    
    print("📁 資料庫檔案檢查:")
    for db_path in db_paths:
        exists = os.path.exists(db_path)
        print(f"  {db_path}: {'✅ 存在' if exists else '❌ 不存在'}")
        
        if exists:
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM monthly_report")
                count = cursor.fetchone()[0]
                print(f"    記錄數: {count:,}")
                
                # 檢查最新日期
                cursor.execute("SELECT MAX(date) FROM monthly_report")
                max_date = cursor.fetchone()[0]
                print(f"    最新日期: {max_date}")
                
                conn.close()
            except Exception as e:
                print(f"    ❌ 讀取失敗: {e}")
    
    print("\n📁 PKL檔案檢查:")
    for pkl_path in pkl_paths:
        exists = os.path.exists(pkl_path)
        print(f"  {pkl_path}: {'✅ 存在' if exists else '❌ 不存在'}")
        
        if exists:
            try:
                df = pd.read_pickle(pkl_path)
                print(f"    資料形狀: {df.shape}")
                
                # 重置索引以便分析
                if isinstance(df.index, pd.MultiIndex):
                    df_reset = df.reset_index()
                else:
                    df_reset = df.copy()
                
                if 'date' in df_reset.columns:
                    max_date = df_reset['date'].max()
                    print(f"    最新日期: {max_date}")
                
                if 'stock_id' in df_reset.columns:
                    unique_stocks = df_reset['stock_id'].nunique()
                    print(f"    股票數量: {unique_stocks}")
                
            except Exception as e:
                print(f"    ❌ 讀取失敗: {e}")

def analyze_specific_stocks():
    """分析特定股票的資料情況"""
    print("\n" + "=" * 80)
    print("🔍 分析特定股票的資料情況")
    print("=" * 80)
    
    # 測試股票
    test_stocks = ['2330', '2317', '8021', '2429', '1234', '9999']
    
    # 嘗試從PKL檔案讀取
    pkl_paths = [
        'history/tables/monthly_report_new.pkl',
        'history/tables/monthly_report.pkl'
    ]
    
    for pkl_path in pkl_paths:
        if os.path.exists(pkl_path):
            print(f"\n📁 分析檔案: {pkl_path}")
            try:
                df = pd.read_pickle(pkl_path)
                
                # 重置索引
                if isinstance(df.index, pd.MultiIndex):
                    df_reset = df.reset_index()
                else:
                    df_reset = df.copy()
                
                print(f"📊 檔案資訊:")
                print(f"  總記錄數: {len(df_reset):,}")
                print(f"  欄位: {list(df_reset.columns)}")
                
                if 'stock_id' in df_reset.columns:
                    # 分析股票ID格式
                    sample_stock_ids = df_reset['stock_id'].head(10).tolist()
                    print(f"  股票ID樣本: {sample_stock_ids}")
                    
                    # 檢查每個測試股票
                    for stock_code in test_stocks:
                        print(f"\n  🔍 檢查股票 {stock_code}:")
                        
                        # 精確匹配
                        exact_match = df_reset[df_reset['stock_id'] == stock_code]
                        print(f"    精確匹配: {len(exact_match)} 筆")
                        
                        # 包含匹配
                        contains_match = df_reset[df_reset['stock_id'].astype(str).str.contains(stock_code, na=False)]
                        print(f"    包含匹配: {len(contains_match)} 筆")
                        
                        if len(contains_match) > 0:
                            # 顯示匹配的記錄
                            latest_record = contains_match.iloc[0]
                            print(f"    股票ID: {latest_record.get('stock_id', 'N/A')}")
                            print(f"    日期: {latest_record.get('date', 'N/A')}")
                            print(f"    當月營收: {latest_record.get('當月營收', 'N/A')}")
                            print(f"    上月營收: {latest_record.get('上月營收', 'N/A')}")
                            print(f"    去年同月營收: {latest_record.get('去年當月營收', 'N/A')}")
                        else:
                            print(f"    ❌ 未找到任何記錄")
                            
                            # 分析可能的原因
                            print(f"    🔍 可能的原因分析:")
                            
                            # 檢查是否有類似的股票代碼
                            similar_stocks = df_reset[df_reset['stock_id'].astype(str).str.contains(stock_code[:3], na=False)]
                            if len(similar_stocks) > 0:
                                similar_ids = similar_stocks['stock_id'].unique()[:5]
                                print(f"      類似股票代碼: {list(similar_ids)}")
                            else:
                                print(f"      沒有找到類似的股票代碼")
                            
                            # 檢查股票代碼格式
                            if stock_code.isdigit():
                                if len(stock_code) == 4:
                                    print(f"      股票代碼格式正確 (4位數字)")
                                else:
                                    print(f"      股票代碼長度異常: {len(stock_code)}")
                            else:
                                print(f"      股票代碼包含非數字字符")
                            
                            # 檢查是否為有效的台股代碼
                            if stock_code in ['1234', '9999']:
                                print(f"      這可能不是真實的股票代碼")
                            elif stock_code in ['8021', '2429']:
                                print(f"      這是真實的股票代碼，但可能:")
                                print(f"        - 不在月營收公告範圍內")
                                print(f"        - 已下市或暫停交易")
                                print(f"        - 資料收集時間範圍外")
                                print(f"        - 屬於特殊類別（如興櫃、ETF等）")
                
                # 只分析第一個找到的檔案
                break
                
            except Exception as e:
                print(f"❌ 分析失敗: {e}")
                continue

def check_stock_categories():
    """檢查股票分類和市場"""
    print("\n" + "=" * 80)
    print("🔍 檢查股票分類和市場歸屬")
    print("=" * 80)
    
    # 台股市場分類
    market_info = {
        '2330': {'name': '台積電', 'market': '上市', 'category': '半導體'},
        '2317': {'name': '鴻海', 'market': '上市', 'category': '電子'},
        '8021': {'name': '尖點', 'market': '上櫃', 'category': '軟體'},
        '2429': {'name': '銘旺科', 'market': '上市', 'category': '電子'},
        '1234': {'name': '不存在', 'market': '無', 'category': '測試'},
        '9999': {'name': '不存在', 'market': '無', 'category': '測試'}
    }
    
    print("📊 股票市場歸屬分析:")
    for stock_code, info in market_info.items():
        print(f"  {stock_code} {info['name']}:")
        print(f"    市場: {info['market']}")
        print(f"    類別: {info['category']}")
        
        if info['market'] == '上櫃':
            print(f"    ⚠️ 上櫃股票的月營收資料可能收集較少")
        elif info['market'] == '無':
            print(f"    ❌ 非真實股票代碼")
        else:
            print(f"    ✅ 應該有月營收資料")

def main():
    """主函數"""
    print("🚀 月營收資料缺失原因分析")
    
    # 檢查資料來源
    check_data_sources()
    
    # 分析特定股票
    analyze_specific_stocks()
    
    # 檢查股票分類
    check_stock_categories()
    
    print("\n" + "=" * 80)
    print("📋 總結分析")
    print("=" * 80)
    print("🔍 可能的資料缺失原因:")
    print("1. 📁 資料檔案不存在或路徑錯誤")
    print("2. 🏢 股票不在月營收公告範圍內（如ETF、REITs等）")
    print("3. 📅 資料收集時間範圍限制")
    print("4. 🏛️ 上櫃股票資料收集不完整")
    print("5. 📊 股票已下市或暫停交易")
    print("6. 🔢 股票代碼格式或匹配問題")
    print("7. 💾 資料庫結構或索引問題")
    print("\n✅ 建議解決方案:")
    print("1. 確認資料檔案存在且可讀取")
    print("2. 檢查股票是否為真實的上市/上櫃公司")
    print("3. 確認股票代碼格式正確")
    print("4. 檢查資料收集的時間範圍")
    print("5. 對於沒有資料的股票，正確顯示 N/A")
    print("=" * 80)

if __name__ == "__main__":
    main()
