#!/usr/bin/env python3
"""
多策略整合GUI
將四種技術指標策略的信號整合在同一圖表上顯示
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QComboBox,
                            QTableWidget, QTableWidgetItem, QTabWidget,
                            QTextEdit, QGroupBox, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QSplitter, QLineEdit,
                            QAbstractItemView)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MultiStrategyAnalysisThread(QThread):
    """多策略分析線程"""
    finished = pyqtSignal(dict, pd.DataFrame)
    progress = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, data):
        super().__init__()
        self.data = data
    
    def run(self):
        try:
            from multi_strategy_chart import MultiStrategySignalAnalyzer
            
            self.progress.emit("初始化分析器...")
            analyzer = MultiStrategySignalAnalyzer()
            
            self.progress.emit("分析RSI策略...")
            all_signals = analyzer.analyze_all_strategies(self.data)
            
            self.progress.emit("分析完成！")
            self.finished.emit(all_signals, self.data)
            
        except Exception as e:
            self.error.emit(f"分析失敗: {str(e)}")

class MultiStrategyGUI(QMainWindow):
    """多策略整合GUI主界面"""

    def __init__(self, db_path=None):
        super().__init__()
        self.all_signals = None
        self.current_data = None
        self.analyzer = None
        self.db_path = db_path
        self.stock_list = {}  # 股票代碼與名稱對應
        self.init_ui()
        self.load_stock_list()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle('📊 多策略整合分析系統')
        self.setGeometry(100, 100, 1400, 900)
        
        # 創建中央組件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 創建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左側：圖表區域
        chart_widget = self.create_chart_widget()
        splitter.addWidget(chart_widget)
        
        # 右側：信息面板
        info_panel = self.create_info_panel()
        splitter.addWidget(info_panel)
        
        # 設置分割比例
        splitter.setSizes([1000, 400])
        
        # 狀態欄
        self.statusBar().showMessage('準備就緒 - 選擇股票後自動開始分析，或點擊"開始分析"按鈕')
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_control_panel(self):
        """創建控制面板"""
        group = QGroupBox("🎛️ 控制面板")
        layout = QHBoxLayout(group)
        
        # 股票選擇標籤
        stock_label = QLabel('📈 選擇股票:')
        stock_label.setStyleSheet("font-weight: bold; color: #ffffff;")  # 改為白色以提高對比度
        layout.addWidget(stock_label)

        # 手動輸入框 (寬度減半)
        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText('輸入股票代碼或名稱 (例: 2330 或 台積電)')
        self.stock_input.setMinimumWidth(100)  # 從200減少到100
        self.stock_input.setMaximumWidth(100)  # 限制最大寬度
        self.stock_input.returnPressed.connect(self.on_stock_input_changed)
        self.stock_input.textChanged.connect(self.on_stock_input_text_changed)
        self.stock_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: #ffffff;
                color: #2c3e50;
                font-size: 13px;
                font-weight: bold;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.stock_input)

        # 股票選擇下拉選單 (再加寬30%以更完整顯示股票名稱)
        self.stock_combo = QComboBox()
        self.stock_combo.setMinimumWidth(176)  # 從135再加寬30%到176
        self.stock_combo.setMaximumWidth(176)  # 限制最大寬度
        self.stock_combo.setMaxVisibleItems(15)
        self.stock_combo.currentTextChanged.connect(self.on_stock_combo_changed)
        self.stock_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                color: #2c3e50;
                font-size: 13px;
                font-weight: bold;
            }
            QComboBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox:hover {
                border-color: #2980b9;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #2c3e50;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #3498db;
                background-color: #ffffff;
                color: #2c3e50;
                selection-background-color: #3498db;
                selection-color: #ffffff;
                font-size: 13px;
                font-weight: bold;
                outline: none;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-bottom: 1px solid #ecf0f1;
                color: #2c3e50;
                background-color: #ffffff;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3498db;
                color: #ffffff;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.stock_combo)

        # 移除重新整理按鈕 (根據用戶要求)

        # 導航按鈕 (簡化為圖示)
        self.prev_btn = QPushButton('←')
        self.prev_btn.setToolTip('前一檔股票')
        self.prev_btn.clicked.connect(self.select_previous_stock)
        self.prev_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 30px;
                max-width: 30px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.prev_btn)

        self.next_btn = QPushButton('→')
        self.next_btn.setToolTip('後一檔股票')
        self.next_btn.clicked.connect(self.select_next_stock)
        self.next_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 30px;
                max-width: 30px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.next_btn)

        # 分析按鈕 (移除圖示，減少寬度)
        self.analyze_btn = QPushButton('分析')
        self.analyze_btn.setToolTip('開始多策略分析')
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 10px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 40px;
                max-width: 50px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.analyze_btn)
        
        # 策略選擇（移除"策略"兩字）
        layout.addWidget(QLabel('顯示策略:'))

        self.strategy_checkboxes = {}
        strategies = ['RSI', 'MACD', '布林通道', '移動平均交叉']

        for strategy in strategies:
            checkbox = QCheckBox(strategy)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_chart)
            self.strategy_checkboxes[strategy] = checkbox
            layout.addWidget(checkbox)

        # 分析長度選擇
        layout.addWidget(QLabel('分析長度:'))

        # 創建分析長度按鈕組
        self.analysis_length_buttons = {}
        length_options = [
            ('半年', 180),
            ('1年', 365),
            ('2年', 730)
        ]

        for label, days in length_options:
            btn = QPushButton(label)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, d=days: self.set_analysis_length(d))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2c3e50;
                    color: #ffffff;
                    border: 1px solid #34495e;
                    border-radius: 3px;
                    padding: 4px 6px;
                    font-size: 11px;
                    font-weight: bold;
                    min-width: 18px;
                    max-width: 22px;
                }
                QPushButton:checked {
                    background-color: #3498db;
                    color: white;
                    border-color: #2980b9;
                }
                QPushButton:hover {
                    background-color: #34495e;
                    color: #ffffff;
                }
                QPushButton:checked:hover {
                    background-color: #2980b9;
                    color: white;
                }
            """)
            self.analysis_length_buttons[days] = btn
            layout.addWidget(btn)

        # 設置默認選中1年
        self.analysis_length_buttons[365].setChecked(True)
        self.current_analysis_days = 365

        # 移除一致性窗口設置以節省空間
        # 使用固定值5作為一致性窗口
        self.consensus_window_value = 5
        
        layout.addStretch()
        
        # 導出按鈕 (移除圖示，減少寬度)
        export_btn = QPushButton('導出')
        export_btn.setToolTip('導出分析結果到Excel檔案')
        export_btn.clicked.connect(self.export_results)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 10px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 40px;
                max-width: 50px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(export_btn)
        
        return group

    def set_analysis_length(self, days):
        """設置分析長度"""
        # 取消其他按鈕的選中狀態
        for btn_days, btn in self.analysis_length_buttons.items():
            btn.setChecked(btn_days == days)

        self.current_analysis_days = days

        # 如果已經有選中的股票，重新分析
        if hasattr(self, 'current_stock_code') and self.current_stock_code:
            self.start_analysis()

    def create_chart_widget(self):
        """創建圖表組件"""
        group = QGroupBox("📈 多策略整合圖表")
        layout = QVBoxLayout(group)
        
        # 創建matplotlib圖表
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        return group
    
    def create_info_panel(self):
        """創建信息面板"""
        group = QGroupBox("📋 分析信息")
        layout = QVBoxLayout(group)
        
        # 創建標籤頁
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 信號統計標籤頁
        self.signal_table = QTableWidget()
        tab_widget.addTab(self.signal_table, "📊 信號統計")
        
        # 一致性分析標籤頁
        self.consensus_text = QTextEdit()
        self.consensus_text.setReadOnly(True)
        self.consensus_text.setStyleSheet("""
            QTextEdit {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        tab_widget.addTab(self.consensus_text, "🎯 一致性分析")

        # 術語解釋標籤頁
        terminology_info = QTextEdit()
        terminology_info.setReadOnly(True)
        terminology_info.setStyleSheet("""
            QTextEdit {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        terminology_info.setHtml(self.get_terminology_info_html())
        tab_widget.addTab(terminology_info, "🎯 術語解釋")

        # 策略說明標籤頁
        strategy_info = QTextEdit()
        strategy_info.setReadOnly(True)
        strategy_info.setStyleSheet("""
            QTextEdit {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        strategy_info.setHtml(self.get_strategy_info_html())
        tab_widget.addTab(strategy_info, "📚 策略說明")

        # 使用說明標籤頁
        usage_info = QTextEdit()
        usage_info.setReadOnly(True)
        usage_info.setStyleSheet("""
            QTextEdit {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        usage_info.setHtml(self.get_usage_info_html())
        tab_widget.addTab(usage_info, "📖 使用說明")
        
        return group

    def get_terminology_info_html(self):
        """獲取術語解釋HTML"""
        return """
        <div style="background-color: #2a2a2a; color: #ffffff; padding: 15px; border-radius: 8px;">
        <h3 style="color: #00d4ff; margin-bottom: 20px;">🎯 技術分析術語詳解</h3>

        <h4 style="color: #3498db; margin-bottom: 15px;">🔵 MACD相關術語</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #555555;">
            <tr style="background-color: #3498db; color: white;">
                <th style="padding: 10px; border: 1px solid #555555;">術語</th>
                <th style="padding: 10px; border: 1px solid #555555;">定義</th>
                <th style="padding: 10px; border: 1px solid #555555;">意義</th>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>MA零軸金叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">MACD線從零軸下方向上穿越零軸，同時向上穿越信號線</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;"><b>強烈買入信號</b> - 趨勢由弱轉強</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>零軸上金叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">MACD線在零軸上方向上穿越信號線</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;">買入信號 - 上升趨勢延續</td>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>零軸下金叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">MACD線在零軸下方向上穿越信號線</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #f39c12;">弱買入信號 - 可能反彈</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>死叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">MACD線向下穿越信號線</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #e74c3c;">賣出信號 - 趨勢轉弱</td>
            </tr>
        </table>

        <h4 style="color: #f39c12; margin-bottom: 15px;">🟡 布林通道相關術語</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #555555;">
            <tr style="background-color: #f39c12; color: white;">
                <th style="padding: 10px; border: 1px solid #555555;">術語</th>
                <th style="padding: 10px; border: 1px solid #555555;">定義</th>
                <th style="padding: 10px; border: 1px solid #555555;">意義</th>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>布林觸及下軌</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">股價跌至布林通道下軌附近或跌破下軌</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;"><b>超賣信號</b> - 可能反彈</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>布林觸及上軌</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">股價漲至布林通道上軌附近或突破上軌</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #e74c3c;"><b>超買信號</b> - 可能回調</td>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>通道收縮</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">上下軌距離縮小，波動率降低</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #f39c12;">盤整狀態 - 醞釀突破</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>通道擴張</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">上下軌距離擴大，波動率增加</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #9b59b6;">趨勢加速 - 注意風險</td>
            </tr>
        </table>

        <h4 style="color: #27ae60; margin-bottom: 15px;">🟢 移動平均相關術語</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #555555;">
            <tr style="background-color: #27ae60; color: white;">
                <th style="padding: 10px; border: 1px solid #555555;">術語</th>
                <th style="padding: 10px; border: 1px solid #555555;">定義</th>
                <th style="padding: 10px; border: 1px solid #555555;">意義</th>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>黃金交叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">短期MA向上穿越長期MA</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;"><b>買入信號</b> - 上升趨勢開始</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>死亡交叉</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">短期MA向下穿越長期MA</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #e74c3c;"><b>賣出信號</b> - 下降趨勢開始</td>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>多頭排列</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">短期MA > 中期MA > 長期MA，且均線向上</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;">強勢上升趨勢</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>空頭排列</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">短期MA < 中期MA < 長期MA，且均線向下</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #e74c3c;">強勢下降趨勢</td>
            </tr>
        </table>

        <h4 style="color: #e74c3c; margin-bottom: 15px;">🔴 RSI相關術語</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #555555;">
            <tr style="background-color: #e74c3c; color: white;">
                <th style="padding: 10px; border: 1px solid #555555;">術語</th>
                <th style="padding: 10px; border: 1px solid #555555;">定義</th>
                <th style="padding: 10px; border: 1px solid #555555;">意義</th>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>超買區</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">RSI > 70，表示買盤過熱</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #e74c3c;">可能回調 - 考慮賣出</td>
            </tr>
            <tr style="background-color: #404040; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>超賣區</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">RSI < 30，表示賣盤過度</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #27ae60;">可能反彈 - 考慮買入</td>
            </tr>
            <tr style="background-color: #333333; color: #ffffff;">
                <td style="padding: 10px; border: 1px solid #555555;"><b>RSI背離</b></td>
                <td style="padding: 10px; border: 1px solid #555555;">價格創新高但RSI未創新高(或相反)</td>
                <td style="padding: 10px; border: 1px solid #555555; color: #f39c12;">趨勢可能反轉</td>
            </tr>
        </table>

        <h4 style="color: #00d4ff; margin-bottom: 15px;">💡 實戰應用提示</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b style="color: #27ae60;">🎯 最強信號</b>：MA零軸金叉 + 布林觸及下軌反彈 = 絕佳買點</li>
            <li><b style="color: #e74c3c;">⚠️ 風險警示</b>：單一指標可能失效，建議多重確認</li>
            <li><b style="color: #f39c12;">📊 時間框架</b>：短線看RSI+布林，中線看MACD，長線看MA交叉</li>
            <li><b style="color: #9b59b6;">🔄 市場環境</b>：震盪市重視超買超賣，趨勢市重視交叉信號</li>
        </ul>
        </div>
        """

    def get_strategy_info_html(self):
        """獲取策略說明HTML"""
        return """
        <div style="background-color: #2a2a2a; color: #ffffff; padding: 15px; border-radius: 8px;">
        <h3 style="color: #00d4ff; margin-bottom: 20px;">📚 技術指標策略詳解</h3>

        <h4 style="color: #e74c3c; margin-bottom: 15px;">🔴 RSI策略 (相對強弱指標)</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>定義</b>：衡量股價漲跌幅度的動量震盪指標，範圍0-100</li>
            <li><b>參數</b>：預設週期14天，超買線70，超賣線30</li>
            <li><b>買入信號</b>：RSI從30以下向上突破30時</li>
            <li><b>賣出信號</b>：RSI從70以上向下跌破70時</li>
            <li><b>適用場景</b>：<span style="color: #e74c3c; font-weight: bold;">短期交易(1-5天)</span>，震盪行情效果佳</li>
            <li><b>策略精神</b>：逆勢操作，在超賣時買入，超買時賣出</li>
        </ul>

        <h4 style="color: #3498db; margin-bottom: 15px;">🔵 MACD策略 (指數平滑移動平均線)</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>定義</b>：由快線(12日EMA)、慢線(26日EMA)及信號線(9日EMA)組成</li>
            <li><b>參數</b>：快線12、慢線26、信號線9</li>
            <li><b>🎯 MA零軸金叉</b>：<span style="color: #3498db; font-weight: bold;">MACD線從零軸下方向上穿越零軸，同時MACD線向上穿越信號線</span></li>
            <li><b>買入信號</b>：MACD線向上穿越信號線(金叉)，特別是零軸金叉</li>
            <li><b>賣出信號</b>：MACD線向下穿越信號線(死叉)</li>
            <li><b>適用場景</b>：<span style="color: #f39c12; font-weight: bold;">中期趨勢(5-20天)</span>，趨勢行情效果佳</li>
            <li><b>策略精神</b>：順勢操作，捕捉趨勢轉折點</li>
        </ul>

        <h4 style="color: #f39c12; margin-bottom: 15px;">🟡 布林通道策略 (Bollinger Bands)</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>定義</b>：由中軌(20日MA)及上下軌(±2倍標準差)組成的通道</li>
            <li><b>參數</b>：週期20天，標準差倍數2</li>
            <li><b>🎯 布林觸及下軌</b>：<span style="color: #f39c12; font-weight: bold;">股價跌至布林通道下軌附近或跌破下軌，通常表示超賣狀態</span></li>
            <li><b>買入信號</b>：股價觸及下軌後反彈(超賣反彈)</li>
            <li><b>賣出信號</b>：股價觸及上軌後回落(超買回調)</li>
            <li><b>適用場景</b>：<span style="color: #e74c3c; font-weight: bold;">短期交易(2-10天)</span>，震盪行情效果佳</li>
            <li><b>策略精神</b>：均值回歸，價格偏離平均值後會回歸</li>
        </ul>

        <h4 style="color: #27ae60; margin-bottom: 15px;">🟢 移動平均交叉策略 (MA Cross)</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>定義</b>：短期移動平均線與長期移動平均線的交叉</li>
            <li><b>參數</b>：短期10日MA，長期30日MA</li>
            <li><b>🎯 黃金交叉</b>：<span style="color: #27ae60; font-weight: bold;">短期MA向上穿越長期MA，表示上升趨勢開始</span></li>
            <li><b>🎯 死亡交叉</b>：<span style="color: #e74c3c; font-weight: bold;">短期MA向下穿越長期MA，表示下降趨勢開始</span></li>
            <li><b>買入信號</b>：短期MA向上穿越長期MA(黃金交叉)</li>
            <li><b>賣出信號</b>：短期MA向下穿越長期MA(死亡交叉)</li>
            <li><b>適用場景</b>：<span style="color: #27ae60; font-weight: bold;">長期投資(20-60天)</span>，明確趨勢行情</li>
            <li><b>策略精神</b>：趨勢跟隨，順應市場主要方向</li>
        </ul>

        <h4 style="color: #9b59b6; margin-bottom: 15px;">⚡ 策略組合運用</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>多重確認</b>：2個以上策略同向信號，可信度較高</li>
            <li><b>時間框架</b>：短期策略適合當沖，長期策略適合波段</li>
            <li><b>市場環境</b>：震盪市用RSI+布林通道，趨勢市用MACD+MA</li>
            <li><b>風險控制</b>：設定停損點，避免單一策略依賴</li>
        </ul>
        </div>
        """

    def get_usage_info_html(self):
        """獲取使用說明HTML"""
        return """
        <div style="background-color: #2a2a2a; color: #ffffff; padding: 15px; border-radius: 8px;">
        <h3 style="color: #00d4ff; margin-bottom: 20px;">📖 多策略整合分析使用說明</h3>

        <h4 style="color: #f39c12; margin-bottom: 15px;">📊 支援策略標記：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b style="color: #e74c3c;">🔴 RSI策略</b>：三角形標記，紅色系</li>
            <li><b style="color: #3498db;">🔵 MACD策略</b>：三角形標記，藍色系</li>
            <li><b style="color: #f39c12;">🟡 布林通道策略</b>：三角形標記，黃色系</li>
            <li><b style="color: #27ae60;">🟢 移動平均交叉策略</b>：三角形標記，綠色系</li>
        </ul>

        <h4 style="color: #9b59b6; margin-bottom: 15px;">🎨 圖表說明：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>統一形狀</b>：買入為上三角形(▲)，賣出為下三角形(▼)</li>
            <li><b>不同顏色</b>：區分不同策略</li>
            <li><b>標記大小</b>：反映信號重要性</li>
            <li><b>文字標註</b>：顯示強度較高的信號</li>
        </ul>

        <h4 style="color: #27ae60; margin-bottom: 15px;">🚀 操作流程：</h4>
        <ol style="color: #ffffff; line-height: 1.8;">
            <li><b>選擇股票</b>：手動輸入股票代碼/名稱或從下拉選單選擇</li>
            <li><b>自動分析</b>：選擇股票後自動開始分析，或點擊"分析"按鈕</li>
            <li><b>快速導航</b>：使用"←"/"→"按鈕快速切換股票</li>
            <li><b>查看圖表</b>：上圖顯示價格+信號，下圖顯示信號統計</li>
            <li><b>策略過濾</b>：勾選/取消策略複選框來過濾顯示</li>
            <li><b>一致性分析</b>：調整一致性窗口查看多策略確認點</li>
            <li><b>導出結果</b>：點擊"導出"按鈕將分析結果(含內嵌圖表)導出為Excel檔案</li>
        </ol>

        <h4 style="color: #e74c3c; margin-bottom: 15px;">💡 使用技巧：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>多策略確認</b>：多個策略同時發出信號時可信度更高</li>
            <li><b>強度判斷</b>：關注有文字標註的高強度信號</li>
            <li><b>一致性分析</b>：查看一致性分析找出最佳進出點</li>
            <li><b>策略過濾</b>：可單獨查看某個策略的信號</li>
            <li><b>時間框架</b>：結合不同時間框架的策略信號</li>
        </ul>

        <h4 style="color: #3498db; margin-bottom: 15px;">📈 信號解讀：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>買入信號</b>：向上三角形(^)，建議考慮買入</li>
            <li><b>賣出信號</b>：向下三角形(v)，建議考慮賣出</li>
            <li><b>信號強度</b>：標記大小反映信號強度</li>
            <li><b>多重確認</b>：同一時點多個策略信號更可靠</li>
        </ul>

        <h4 style="color: #e74c3c; margin-bottom: 15px;">⚠️ 注意事項：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li>多策略整合可以提高準確性，但不保證100%正確</li>
            <li>建議結合基本面分析和風險管理</li>
            <li>不同市場環境下各策略的有效性可能不同</li>
            <li>請設定適當的停損點控制風險</li>
            <li>技術分析僅供參考，投資決策需謹慎</li>
        </ul>

        <h4 style="color: #27ae60; margin-bottom: 15px;">🔧 功能說明：</h4>
        <ul style="color: #ffffff; line-height: 1.8;">
            <li><b>信號統計表</b>：顯示各策略的詳細信號統計</li>
            <li><b>一致性分析</b>：分析多策略的一致性程度</li>
            <li><b>策略說明</b>：查看各技術指標的詳細說明</li>
            <li><b>圖表互動</b>：可縮放、平移查看詳細信號</li>
        </ul>
        </div>
        """
    
    def generate_test_data(self):
        """生成測試數據"""
        try:
            # 生成6個月的測試數據
            dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日
            
            # 生成價格數據
            np.random.seed(42)
            base_price = 100
            prices = []
            
            for i in range(len(dates)):
                if i == 0:
                    price = base_price
                else:
                    # 添加趨勢和隨機波動
                    trend = 0.001 * np.sin(i * 0.1)
                    noise = np.random.normal(0, 0.02)
                    price = prices[-1] * (1 + trend + noise)
                
                prices.append(price)
            
            # 生成OHLCV數據
            data = []
            for i, (date, close) in enumerate(zip(dates, prices)):
                volatility = 0.02
                high = close * (1 + np.random.uniform(0, volatility))
                low = close * (1 - np.random.uniform(0, volatility))
                open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))
                
                high = max(high, open_price, close)
                low = min(low, open_price, close)
                volume = np.random.randint(100000, 1000000)
                
                data.append({
                    'date': date,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })
            
            self.current_data = pd.DataFrame(data)
            self.current_data.set_index('date', inplace=True)
            
            self.analyze_btn.setEnabled(True)
            self.statusBar().showMessage(f'測試數據已生成 - {len(self.current_data)} 個交易日')
            
        except Exception as e:
            QMessageBox.critical(self, '錯誤', f'生成測試數據失敗: {str(e)}')

    def load_stock_list(self):
        """載入股票清單 - 與主程式的"全部股票"一致"""
        try:
            self.stock_combo.clear()
            self.stock_combo.addItem("請選擇股票...")

            # 嘗試從資料庫載入完整股票清單（與主程式一致）
            if self.db_path and os.path.exists(self.db_path):
                import sqlite3
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # 檢查表結構
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]

                stock_data = []

                # 使用與主程式完全相同的查詢邏輯
                for table_name in ['stock_daily_data', 'price', 'stock_price', 'daily_data']:
                    if table_name in tables:
                        try:
                            # 使用與主程式完全相同的查詢條件
                            query = f"""
                                SELECT DISTINCT stock_id, stock_name
                                FROM {table_name}
                                WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                                  AND LENGTH(stock_id) BETWEEN 4 AND 6
                                  AND stock_id IS NOT NULL
                                  AND stock_id != ''
                                ORDER BY
                                    CASE
                                        WHEN LENGTH(stock_id) = 4 THEN 1
                                        WHEN LENGTH(stock_id) = 5 THEN 2
                                        WHEN LENGTH(stock_id) = 6 THEN 3
                                    END,
                                    CAST(stock_id AS INTEGER)
                            """

                            cursor.execute(query)
                            results = cursor.fetchall()

                            print(f"📊 從表 {table_name} 查詢到 {len(results)} 支股票")

                            # 組合股票清單（與主程式格式完全一致）
                            for stock_id, stock_name in results:
                                if stock_id:
                                    # 格式：股票代碼 股票名稱（與主程式一致）
                                    if stock_name and stock_name != stock_id:
                                        display_text = f"{stock_id} {stock_name}"
                                    else:
                                        display_text = f"{stock_id}"
                                    stock_data.append((stock_id, display_text))

                            if stock_data:  # 如果成功獲取到數據就跳出循環
                                break

                        except Exception as e:
                            print(f"查詢表 {table_name} 失敗: {e}")
                            continue

                conn.close()

                if stock_data:
                    # 載入所有股票（完整清單，與主程式"全部股票"完全一致）
                    for code, display_text in stock_data:
                        self.stock_combo.addItem(display_text, code)
                    self.statusBar().showMessage(f'✅ 已載入 {len(stock_data)} 支股票（與主程式"全部股票"完全一致）')
                    print(f"📊 多策略分析系統：成功載入 {len(stock_data)} 支股票")
                else:
                    print("⚠️ 無法從資料庫載入股票，使用預設清單")
                    self.load_default_stocks()
            else:
                self.load_default_stocks()

            # 更新導航按鈕狀態
            self.update_navigation_buttons()

        except Exception as e:
            print(f"載入股票清單失敗: {e}")
            self.load_default_stocks()

    def load_default_stocks(self):
        """載入預設股票清單 - 與主程式熱門股票一致"""
        default_stocks = [
            # 權值股
            ('2330', '2330 - 台積電'),
            ('2317', '2317 - 鴻海'),
            ('2454', '2454 - 聯發科'),
            ('2412', '2412 - 中華電'),
            ('2303', '2303 - 聯電'),
            ('2308', '2308 - 台達電'),
            ('2382', '2382 - 廣達'),
            ('3008', '3008 - 大立光'),
            ('2327', '2327 - 國巨'),
            ('2395', '2395 - 研華'),

            # 金融股
            ('2881', '2881 - 富邦金'),
            ('2882', '2882 - 國泰金'),
            ('2886', '2886 - 兆豐金'),
            ('2891', '2891 - 中信金'),
            ('2892', '2892 - 第一金'),
            ('5880', '5880 - 合庫金'),

            # 傳統產業
            ('2002', '2002 - 中鋼'),
            ('1301', '1301 - 台塑'),
            ('1303', '1303 - 南亞'),
            ('1216', '1216 - 統一'),
            ('1101', '1101 - 台泥'),
            ('2207', '2207 - 和泰車'),

            # 生技醫療
            ('4938', '4938 - 和碩'),
            ('6505', '6505 - 台塑化'),
            ('2357', '2357 - 華碩'),
            ('2409', '2409 - 友達'),
            ('2408', '2408 - 南亞科'),

            # 航運股
            ('2603', '2603 - 長榮'),
            ('2609', '2609 - 陽明'),
            ('2615', '2615 - 萬海'),
        ]

        for code, display_text in default_stocks:
            self.stock_combo.addItem(display_text, code)

        self.statusBar().showMessage(f'已載入預設股票清單（{len(default_stocks)} 支熱門股票）')

        # 更新導航按鈕狀態
        self.update_navigation_buttons()

    def on_stock_input_changed(self):
        """處理股票輸入框回車事件"""
        input_text = self.stock_input.text().strip()
        if input_text:
            self.search_and_select_stock(input_text)

    def on_stock_input_text_changed(self, text):
        """處理股票輸入框文字變化"""
        # 可以在這裡添加即時搜尋功能
        pass

    def on_stock_combo_changed(self, text):
        """處理下拉選單變化"""
        if self.stock_combo.currentIndex() > 0:
            # 同步更新輸入框
            self.stock_input.setText(text)
            # 自動開始分析
            self.auto_start_analysis()

    def search_and_select_stock(self, search_text):
        """搜尋並選擇股票"""
        try:
            search_text = search_text.strip().upper()  # 轉為大寫進行比較
            best_match_index = -1
            exact_match_index = -1

            # 在下拉選單中搜尋匹配的股票
            for i in range(1, self.stock_combo.count()):  # 跳過第一個"請選擇股票..."
                item_text = self.stock_combo.itemText(i)
                item_data = self.stock_combo.itemData(i)

                if not item_data:
                    continue

                # 精確匹配股票代碼
                if search_text == item_data.upper():
                    exact_match_index = i
                    break

                # 檢查是否包含搜尋文字（股票代碼或名稱）
                if (search_text in item_text.upper() or
                    search_text in item_data.upper()):
                    if best_match_index == -1:
                        best_match_index = i

            # 優先使用精確匹配
            target_index = exact_match_index if exact_match_index != -1 else best_match_index

            if target_index != -1:
                self.stock_combo.setCurrentIndex(target_index)
                self.stock_input.setText(self.stock_combo.itemText(target_index))
                return True

            # 如果沒找到，顯示警告
            QMessageBox.warning(self, '警告', f'找不到股票: {search_text}')
            return False

        except Exception as e:
            print(f"搜尋股票失敗: {e}")
            return False

    def auto_start_analysis(self):
        """自動開始分析"""
        if self.stock_combo.currentIndex() > 0:
            # 延遲一點時間再開始分析，避免頻繁觸發
            QTimer.singleShot(500, self.start_analysis)

    def select_previous_stock(self):
        """選擇前一檔股票"""
        current_index = self.stock_combo.currentIndex()
        if current_index > 1:  # 跳過第一個"請選擇股票..."
            self.stock_combo.setCurrentIndex(current_index - 1)
            # 會自動觸發 on_stock_combo_changed 進行分析

    def select_next_stock(self):
        """選擇後一檔股票"""
        current_index = self.stock_combo.currentIndex()
        if current_index < self.stock_combo.count() - 1:
            self.stock_combo.setCurrentIndex(current_index + 1)
            # 會自動觸發 on_stock_combo_changed 進行分析

    def update_navigation_buttons(self):
        """更新導航按鈕狀態"""
        current_index = self.stock_combo.currentIndex()
        total_count = self.stock_combo.count()

        # 前一檔按鈕：當前不是第一個有效股票時啟用
        self.prev_btn.setEnabled(current_index > 1)

        # 後一檔按鈕：當前不是最後一個股票時啟用
        self.next_btn.setEnabled(current_index < total_count - 1 and current_index > 0)

    def get_stock_data_from_db(self, symbol, start_date, end_date):
        """從資料庫獲取股票數據"""
        try:
            if not self.db_path or not os.path.exists(self.db_path):
                return None

            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 檢查表結構
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [table[0] for table in cursor.fetchall()]

            # 嘗試不同的表名
            for table_name in ['stock_daily_data', 'stock_price', 'daily_data']:
                if table_name in tables:
                    try:
                        # 檢查欄位
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = [col[1] for col in cursor.fetchall()]

                        # 建立欄位映射
                        column_mapping = {}
                        for target, candidates in [
                            ('open', ['open', 'Open', 'opening_price']),
                            ('high', ['high', 'High', 'highest_price']),
                            ('low', ['low', 'Low', 'lowest_price']),
                            ('close', ['close', 'Close', 'closing_price']),
                            ('volume', ['volume', 'Volume', 'trading_volume'])
                        ]:
                            for candidate in candidates:
                                if candidate in columns:
                                    column_mapping[target] = candidate
                                    break

                        if len(column_mapping) >= 4:  # 至少要有OHLC
                            # 構建查詢
                            select_columns = []
                            for target in ['open', 'high', 'low', 'close', 'volume']:
                                if target in column_mapping:
                                    select_columns.append(f"{column_mapping[target]} as {target}")

                            query = f"""
                                SELECT date, {', '.join(select_columns)}
                                FROM {table_name}
                                WHERE stock_id = ? AND date >= ? AND date <= ?
                                ORDER BY date
                            """

                            cursor.execute(query, (symbol, start_date, end_date))
                            rows = cursor.fetchall()

                            if rows:
                                columns = ['date'] + [col.split(' as ')[1] for col in select_columns]
                                df = pd.DataFrame(rows, columns=columns)
                                df['date'] = pd.to_datetime(df['date'])
                                df.set_index('date', inplace=True)

                                conn.close()
                                return df
                    except Exception as e:
                        continue

            conn.close()
            return None

        except Exception as e:
            print(f"獲取股票數據失敗: {e}")
            return None

    def generate_sample_data_for_stock(self, symbol):
        """為特定股票生成示例數據"""
        try:
            # 生成6個月的測試數據
            dates = pd.date_range(start='2024-01-01', end='2024-06-30', freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日

            # 根據股票代碼生成不同的隨機種子
            np.random.seed(hash(symbol) % 1000)

            # 初始價格
            base_price = 100
            prices = []

            for i in range(len(dates)):
                if i == 0:
                    price = base_price
                else:
                    # 添加趨勢和隨機波動
                    trend = 0.001 * np.sin(i * 0.1)
                    noise = np.random.normal(0, 0.02)
                    price = prices[-1] * (1 + trend + noise)

                prices.append(price)

            # 生成OHLCV數據
            data = []
            for i, (date, close) in enumerate(zip(dates, prices)):
                volatility = 0.02
                high = close * (1 + np.random.uniform(0, volatility))
                low = close * (1 - np.random.uniform(0, volatility))
                open_price = close * (1 + np.random.uniform(-volatility/2, volatility/2))

                high = max(high, open_price, close)
                low = min(low, open_price, close)
                volume = np.random.randint(100000, 1000000)

                data.append({
                    'date': date,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })

            df = pd.DataFrame(data)
            df.set_index('date', inplace=True)
            return df

        except Exception as e:
            print(f"生成示例數據失敗: {e}")
            return pd.DataFrame()
    
    def start_analysis(self):
        """開始多策略分析"""
        # 更新導航按鈕狀態
        self.update_navigation_buttons()

        # 檢查是否選擇了股票或輸入了股票
        selected_stock = None

        # 優先檢查下拉選單
        if self.stock_combo.currentIndex() > 0:
            selected_stock = self.stock_combo.currentData()

        # 如果下拉選單沒選擇，檢查輸入框
        if not selected_stock:
            input_text = self.stock_input.text().strip()
            if input_text:
                # 嘗試從輸入框搜尋股票
                if self.search_and_select_stock(input_text):
                    selected_stock = self.stock_combo.currentData()

        # 最終檢查
        if not selected_stock:
            QMessageBox.warning(self, '警告', '請先選擇股票或輸入股票代碼/名稱')
            return

        # 保存當前股票代碼，用於分析長度切換時重新分析
        self.current_stock_code = selected_stock

        # 設置日期範圍（根據選擇的分析長度）
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=self.current_analysis_days)).strftime('%Y-%m-%d')

        # 嘗試從資料庫獲取數據
        self.statusBar().showMessage(f'正在載入 {selected_stock} 的數據...')
        self.current_data = self.get_stock_data_from_db(selected_stock, start_date, end_date)

        if self.current_data is None or self.current_data.empty:
            # 如果無法從資料庫獲取，生成示例數據
            self.statusBar().showMessage(f'無法從資料庫獲取 {selected_stock} 數據，使用示例數據...')
            self.current_data = self.generate_sample_data_for_stock(selected_stock)

        if self.current_data is None or self.current_data.empty:
            QMessageBox.warning(self, '警告', f'無法獲取 {selected_stock} 的數據')
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不確定進度
        
        # 創建分析線程
        self.analysis_thread = MultiStrategyAnalysisThread(self.current_data)
        self.analysis_thread.finished.connect(self.on_analysis_finished)
        self.analysis_thread.progress.connect(self.on_analysis_progress)
        self.analysis_thread.error.connect(self.on_analysis_error)
        self.analysis_thread.start()
    
    def on_analysis_progress(self, message):
        """分析進度更新"""
        self.statusBar().showMessage(message)
    
    def on_analysis_finished(self, all_signals, data):
        """分析完成"""
        self.all_signals = all_signals
        self.progress_bar.setVisible(False)
        
        # 更新圖表
        self.update_chart()
        
        # 更新信號統計表
        self.update_signal_table()
        
        # 更新一致性分析
        self.update_consensus_analysis()
        
        self.statusBar().showMessage('多策略分析完成！')
    
    def on_analysis_error(self, error_message):
        """分析錯誤"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '分析錯誤', error_message)
        self.statusBar().showMessage('分析失敗')
    
    def update_chart(self):
        """更新圖表"""
        if self.all_signals is None or self.current_data is None:
            return

        try:
            # 清除現有圖表
            self.figure.clear()

            # 過濾選中的策略
            filtered_signals = {}
            for strategy, checkbox in self.strategy_checkboxes.items():
                if checkbox.isChecked():
                    filtered_signals[strategy] = self.all_signals[strategy]

            if not filtered_signals:
                # 如果沒有選中策略，顯示空圖表
                ax = self.figure.add_subplot(1, 1, 1)
                ax.text(0.5, 0.5, '請選擇要顯示的策略', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('多策略整合分析')

                self.figure.tight_layout()
                self.canvas.draw()
                return

            # 獲取選擇的股票代碼和名稱
            selected_stock_code = self.stock_combo.currentData() or "測試數據"
            selected_stock_text = self.stock_combo.currentText() or "測試數據"

            # 從下拉選單文字中提取股票名稱 (格式: "代碼 名稱")
            if " " in selected_stock_text and selected_stock_code != "測試數據":
                stock_name = selected_stock_text.split(" ", 1)[1] if len(selected_stock_text.split(" ", 1)) > 1 else ""
                title_text = f"{selected_stock_code} {stock_name}"
            else:
                title_text = selected_stock_code

            # 創建單一價格圖表（占滿整個圖表區域）
            ax1 = self.figure.add_subplot(1, 1, 1)

            # 繪製價格線
            dates = self.current_data.index
            prices = self.current_data['close']
            ax1.plot(dates, prices, 'b-', linewidth=1.5, label='收盤價', alpha=0.8)

            # 繪製各策略信號（更新策略名稱）
            colors = {
                'RSI': {'buy': '#FF6B6B', 'sell': '#FF4757'},
                'MACD': {'buy': '#4ECDC4', 'sell': '#45B7B8'},
                '布林通道': {'buy': '#FFA726', 'sell': '#FF9800'},
                '移動平均交叉': {'buy': '#66BB6A', 'sell': '#4CAF50'}
            }
            # 統一使用三角形標記：買入為上三角形，賣出為下三角形
            markers = {
                'RSI': {'buy': '^', 'sell': 'v'},
                'MACD': {'buy': '^', 'sell': 'v'},
                '布林通道': {'buy': '^', 'sell': 'v'},
                '移動平均交叉': {'buy': '^', 'sell': 'v'}
            }

            for strategy_name, signals in filtered_signals.items():
                if 'buy' in signals and 'sell' in signals:
                    # 買入信號
                    buy_signals = signals['buy']
                    if buy_signals:
                        buy_dates = [dates[s['index']] for s in buy_signals if s['index'] < len(dates)]
                        buy_prices = [s['price'] for s in buy_signals if s['index'] < len(dates)]
                        buy_strengths = [s['strength'] for s in buy_signals if s['index'] < len(dates)]

                        if buy_dates and buy_prices:
                            # 根據強度調整大小 (增加50%大小)
                            sizes = [45 + 60 * strength for strength in buy_strengths]
                            ax1.scatter(buy_dates, buy_prices,
                                      color=colors.get(strategy_name, {}).get('buy', 'blue'),
                                      marker=markers.get(strategy_name, {}).get('buy', '^'),
                                      s=sizes, alpha=0.8, label=f'{strategy_name} 買入',
                                      edgecolors='white', linewidth=1)

                            # 添加高強度信號的文字標註
                            for i, signal in enumerate(buy_signals):
                                if signal['index'] < len(dates) and signal['strength'] >= 0.7:
                                    ax1.annotate(f"{strategy_name[:2]}\n{signal.get('text', 'BUY')}",
                                               xy=(dates[signal['index']], signal['price']),
                                               xytext=(5, 15), textcoords='offset points',
                                               bbox=dict(boxstyle='round,pad=0.3',
                                                       facecolor=colors.get(strategy_name, {}).get('buy', 'blue'),
                                                       alpha=0.7),
                                               fontsize=8, ha='left',
                                               arrowprops=dict(arrowstyle='->',
                                                             connectionstyle='arc3,rad=0.1'))

                    # 賣出信號
                    sell_signals = signals['sell']
                    if sell_signals:
                        sell_dates = [dates[s['index']] for s in sell_signals if s['index'] < len(dates)]
                        sell_prices = [s['price'] for s in sell_signals if s['index'] < len(dates)]
                        sell_strengths = [s['strength'] for s in sell_signals if s['index'] < len(dates)]

                        if sell_dates and sell_prices:
                            # 根據強度調整大小 (增加50%大小)
                            sizes = [45 + 60 * strength for strength in sell_strengths]
                            ax1.scatter(sell_dates, sell_prices,
                                      color=colors.get(strategy_name, {}).get('sell', 'red'),
                                      marker=markers.get(strategy_name, {}).get('sell', 'v'),
                                      s=sizes, alpha=0.8, label=f'{strategy_name} 賣出',
                                      edgecolors='white', linewidth=1)

                            # 添加高強度信號的文字標註
                            for i, signal in enumerate(sell_signals):
                                if signal['index'] < len(dates) and signal['strength'] >= 0.7:
                                    ax1.annotate(f"{strategy_name[:2]}\n{signal.get('text', 'SELL')}",
                                               xy=(dates[signal['index']], signal['price']),
                                               xytext=(5, -20), textcoords='offset points',
                                               bbox=dict(boxstyle='round,pad=0.3',
                                                       facecolor=colors.get(strategy_name, {}).get('sell', 'red'),
                                                       alpha=0.7),
                                               fontsize=8, ha='left',
                                               arrowprops=dict(arrowstyle='->',
                                                             connectionstyle='arc3,rad=-0.1'))

            ax1.set_title(f'多策略整合分析 - {title_text}')
            ax1.set_ylabel('價格')
            ax1.grid(True, alpha=0.3)
            ax1.legend(bbox_to_anchor=(1.02, 1), loc='upper left')

            # 調整文字標註位置，避免遮蓋符號
            self.adjust_annotation_positions(ax1, filtered_signals, dates)

            self.figure.tight_layout()
            self.canvas.draw()

            # 更新右側信號統計表格
            self.update_signal_table(filtered_signals)

        except Exception as e:
            print(f"更新圖表失敗: {e}")
            # 顯示錯誤信息
            self.figure.clear()
            ax = self.figure.add_subplot(1, 1, 1)
            ax.text(0.5, 0.5, f'圖表更新失敗:\n{str(e)}', ha='center', va='center', transform=ax.transAxes)
            self.figure.tight_layout()
            self.canvas.draw()
    
    def update_signal_table(self, filtered_signals=None):
        """更新信號統計表"""
        if filtered_signals is None:
            if self.all_signals is None:
                return
            # 過濾選中的策略
            filtered_signals = {}
            for strategy, checkbox in self.strategy_checkboxes.items():
                if checkbox.isChecked():
                    filtered_signals[strategy] = self.all_signals[strategy]

        try:
            # 統計各策略信號數量
            strategy_stats = {}
            for strategy_name, signals in filtered_signals.items():
                if 'buy' in signals and 'sell' in signals:
                    buy_count = len(signals['buy'])
                    sell_count = len(signals['sell'])
                    strategy_stats[strategy_name] = {'買入': buy_count, '賣出': sell_count}

            # 設置表格
            self.signal_table.setRowCount(len(strategy_stats))
            self.signal_table.setColumnCount(3)
            self.signal_table.setHorizontalHeaderLabels(['策略', '買入信號', '賣出信號'])

            # 填充數據
            for row, (strategy_name, stats) in enumerate(strategy_stats.items()):
                # 策略名稱
                strategy_item = QTableWidgetItem(strategy_name)
                strategy_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.signal_table.setItem(row, 0, strategy_item)

                # 買入信號數量（綠色背景）
                buy_item = QTableWidgetItem(str(stats['買入']))
                buy_item.setBackground(QColor(144, 238, 144))  # 淺綠色
                buy_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.signal_table.setItem(row, 1, buy_item)

                # 賣出信號數量（淺紅色背景）
                sell_item = QTableWidgetItem(str(stats['賣出']))
                sell_item.setBackground(QColor(255, 182, 193))  # 淺紅色
                sell_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.signal_table.setItem(row, 2, sell_item)

            # 調整表格外觀
            self.signal_table.resizeColumnsToContents()
            self.signal_table.horizontalHeader().setStretchLastSection(True)

        except Exception as e:
            print(f"更新信號表失敗: {e}")
    
    def update_consensus_analysis(self):
        """更新一致性分析"""
        if self.all_signals is None or self.current_data is None:
            return
        
        try:
            from multi_strategy_chart import MultiStrategySignalAnalyzer
            analyzer = MultiStrategySignalAnalyzer()
            
            window = self.consensus_window_value
            consensus_points = analyzer.analyze_signal_consensus(
                self.all_signals, self.current_data, window
            )
            
            # 生成一致性分析報告
            report = f"<h3>🎯 信號一致性分析報告</h3>"
            report += f"<p><b>分析窗口：</b>{window} 個交易日</p>"
            report += f"<p><b>高一致性點數量：</b>{len(consensus_points)}</p><br>"
            
            if consensus_points:
                report += "<h4>📊 高一致性信號點：</h4><ul>"
                
                for point in consensus_points[:10]:  # 只顯示前10個
                    date = self.current_data.index[point['index']].strftime('%Y-%m-%d')
                    signal_type = "買入" if point['type'] == 'buy' else "賣出"
                    emoji = "🟢" if point['type'] == 'buy' else "🔴"
                    
                    report += f"<li>{emoji} <b>{date}</b> - {signal_type}信號 "
                    report += f"(一致性: {point['consensus']}/4, 價格: {point['price']:.2f})</li>"
                
                report += "</ul>"
                
                if len(consensus_points) > 10:
                    report += f"<p><i>... 還有 {len(consensus_points) - 10} 個一致性點</i></p>"
            else:
                report += "<p>⚠️ 未發現高一致性信號點，建議調整分析窗口或檢查策略參數。</p>"
            
            report += "<br><h4>💡 使用建議：</h4>"
            report += "<ul>"
            report += "<li>一致性越高的信號點，可信度通常越高</li>"
            report += "<li>建議重點關注一致性≥3的信號點</li>"
            report += "<li>結合信號強度和市場環境進行最終決策</li>"
            report += "</ul>"
            
            self.consensus_text.setHtml(report)
            
        except Exception as e:
            print(f"更新一致性分析失敗: {e}")
    
    def export_results(self):
        """導出分析結果"""
        if self.all_signals is None:
            QMessageBox.warning(self, '警告', '沒有分析結果可導出')
            return

        try:
            from PyQt6.QtWidgets import QFileDialog
            import os

            # 獲取選擇的股票代碼
            selected_stock = self.stock_combo.currentData() or "測試數據"

            # 選擇保存位置
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            default_filename = f"多策略分析_{selected_stock}_{timestamp}"

            # 簡化導出流程，直接導出包含圖表的Excel文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                '保存多策略分析結果',
                default_filename + '.xlsx',
                'Excel files (*.xlsx);;All files (*.*)'
            )

            if not file_path:
                return

            # 直接導出包含圖表的完整Excel文件
            self._export_to_excel(file_path, True, True)  # 包含信號數據和統計摘要

            QMessageBox.information(self, '導出成功',
                                  f'多策略分析結果已成功導出至:\n{file_path}\n\n'
                                  f'包含內容:\n• 各策略信號數據\n• 統計摘要\n• 內嵌圖表')
            return  # 提前返回，跳過舊的導出邏輯

            # 舊的導出邏輯已被上面的簡化流程替代

        except Exception as e:
            QMessageBox.critical(self, '導出失敗', f'導出過程中發生錯誤:\n{str(e)}')

    def _export_to_excel(self, filename, include_signals, include_summary):
        """導出到Excel文件，包含內嵌圖表"""
        try:
            import pandas as pd
            import io
            from openpyxl.drawing.image import Image as OpenpyxlImage

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 導出信號數據
                if include_signals:
                    for strategy_name, signals in self.all_signals.items():
                        # 合併買入和賣出信號
                        all_signals_data = []

                        # 買入信號
                        for signal in signals['buy']:
                            all_signals_data.append({
                                '日期': self.current_data.index[signal['index']].strftime('%Y-%m-%d'),
                                '價格': signal['price'],
                                '強度': signal['strength'],
                                '描述': signal.get('text', ''),
                                '信號類型': '買入'
                            })

                        # 賣出信號
                        for signal in signals['sell']:
                            all_signals_data.append({
                                '日期': self.current_data.index[signal['index']].strftime('%Y-%m-%d'),
                                '價格': signal['price'],
                                '強度': signal['strength'],
                                '描述': signal.get('text', ''),
                                '信號類型': '賣出'
                            })

                        if all_signals_data:
                            df = pd.DataFrame(all_signals_data)
                            df = df.sort_values('日期')
                            df.to_excel(writer, sheet_name=strategy_name, index=False)

                # 導出統計摘要
                if include_summary:
                    # 策略統計
                    stats_data = []
                    for strategy_name, signals in self.all_signals.items():
                        buy_count = len(signals['buy'])
                        sell_count = len(signals['sell'])
                        strong_buy = len([s for s in signals['buy'] if s['strength'] >= 0.7])
                        strong_sell = len([s for s in signals['sell'] if s['strength'] >= 0.7])

                        stats_data.append({
                            '策略名稱': strategy_name,
                            '買入信號數': buy_count,
                            '賣出信號數': sell_count,
                            '強買入信號數': strong_buy,
                            '強賣出信號數': strong_sell,
                            '總信號數': buy_count + sell_count
                        })

                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='策略統計', index=False)

                    # 一致性分析
                    try:
                        from multi_strategy_chart import MultiStrategySignalAnalyzer
                        analyzer = MultiStrategySignalAnalyzer()
                        consensus_points = analyzer.analyze_signal_consensus(
                            self.all_signals, self.current_data, window=self.consensus_window_value
                        )

                        if consensus_points:
                            consensus_data = []
                            for point in consensus_points:
                                consensus_data.append({
                                    '日期': self.current_data.index[point['index']].strftime('%Y-%m-%d'),
                                    '信號類型': '買入' if point['type'] == 'buy' else '賣出',
                                    '一致性': point['consensus'],
                                    '價格': point['price']
                                })
                            consensus_df = pd.DataFrame(consensus_data)
                            consensus_df.to_excel(writer, sheet_name='一致性分析', index=False)
                    except:
                        pass  # 如果一致性分析失敗，跳過

                # 添加圖表工作表並內嵌圖形
                try:
                    # 創建圖表工作表
                    workbook = writer.book
                    chart_sheet = workbook.create_sheet('圖表')

                    # 將matplotlib圖表保存為圖片
                    img_buffer = io.BytesIO()
                    self.figure.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
                    img_buffer.seek(0)

                    # 將圖片插入Excel
                    img = OpenpyxlImage(img_buffer)
                    # 調整圖片大小以適應Excel
                    img.width = 800
                    img.height = 600
                    chart_sheet.add_image(img, 'A1')

                    # 添加圖表說明
                    chart_sheet['A25'] = '多策略整合分析圖表'
                    chart_sheet['A26'] = f'生成時間: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'

                except Exception as chart_error:
                    print(f"添加圖表到Excel失敗: {chart_error}")
                    # 即使圖表添加失敗，也不影響其他數據的導出

        except Exception as e:
            raise Exception(f"導出Excel文件失敗: {str(e)}")

    def adjust_annotation_positions(self, ax, filtered_signals, dates):
        """調整文字標註位置，避免遮蓋符號"""
        try:
            # 收集所有信號點的位置
            signal_positions = []

            for strategy_name, signals in filtered_signals.items():
                # 處理買入信號
                if 'buy' in signals:
                    for signal in signals['buy']:
                        if signal['index'] < len(dates):
                            signal_positions.append({
                                'x': dates[signal['index']],
                                'y': signal['price'],
                                'type': 'buy',
                                'strategy': strategy_name
                            })

                # 處理賣出信號
                if 'sell' in signals:
                    for signal in signals['sell']:
                        if signal['index'] < len(dates):
                            signal_positions.append({
                                'x': dates[signal['index']],
                                'y': signal['price'],
                                'type': 'sell',
                                'strategy': strategy_name
                            })

            # 根據信號密度調整標註位置
            if signal_positions:
                # 按時間排序
                signal_positions.sort(key=lambda x: x['x'])

                # 為密集區域的信號調整標註位置
                for i, pos in enumerate(signal_positions):
                    # 檢查附近是否有其他信號
                    nearby_signals = [p for p in signal_positions
                                    if abs((p['x'] - pos['x']).days) <= 3 and p != pos]

                    if len(nearby_signals) > 0:
                        # 如果有附近信號，添加簡化標註
                        strategy_short = pos['strategy'][:3]  # 取策略名稱前3個字

                        # 根據信號類型調整位置
                        if pos['type'] == 'buy':
                            offset_y = pos['y'] * (1 + 0.015 * (i % 4 + 1))  # 向上偏移
                            color = 'green'
                        else:
                            offset_y = pos['y'] * (1 - 0.015 * (i % 4 + 1))  # 向下偏移
                            color = 'red'

                        # 添加調整後的標註
                        ax.annotate(strategy_short,
                                  xy=(pos['x'], pos['y']),
                                  xytext=(pos['x'], offset_y),
                                  fontsize=7, ha='center', color=color,
                                  bbox=dict(boxstyle="round,pad=0.1",
                                          facecolor='white', alpha=0.8,
                                          edgecolor=color),
                                  arrowprops=dict(arrowstyle='->',
                                                connectionstyle='arc3,rad=0.1',
                                                alpha=0.6, color=color))

        except Exception as e:
            print(f"標註位置調整錯誤: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 創建主窗口
    window = MultiStrategyGUI()
    window.show()
    
    print("🚀 多策略整合分析系統已啟動")
    print("💡 功能特色:")
    print("   • 🎯 四策略信號整合：RSI、MACD、布林通道、移動平均交叉")
    print("   • 📊 智能信號強度分析")
    print("   • 🔍 信號一致性分析")
    print("   • 📈 多策略同圖顯示")
    print("   • 📋 詳細統計報告")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
