#!/usr/bin/env python3
"""
測試優化市場掃描器
驗證智能掃描功能的效能改善
"""

import sys
import logging
import time
import threading
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_scanner_performance():
    """測試掃描器效能"""
    print("=" * 60)
    print("🚀 測試優化市場掃描器效能")
    print("=" * 60)
    
    try:
        from optimized_market_scanner import OptimizedMarketScanner
        
        scanner = OptimizedMarketScanner()
        
        print("📊 開始效能測試...")
        start_time = time.time()
        
        # 執行掃描
        results = scanner.run_full_scan()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if results:
            print("✅ 掃描成功完成")
            print(f"⏱️ 總耗時: {duration:.2f} 秒")
            print(f"📊 市場指數: {len(results.get('market_indices', {}))} 項")
            print(f"📈 熱門股票: {len(results.get('hot_stocks', {}))} 項")
            
            # 顯示市場摘要
            summary = results.get('market_summary', {})
            if summary:
                print(f"💭 市場情緒: {summary.get('market_sentiment', '未知')}")
                print(f"📊 總項目: {summary.get('total_items', 0)}")
                print(f"📈 上漲: {summary.get('positive_count', 0)}")
                print(f"📉 下跌: {summary.get('negative_count', 0)}")
                print(f"📊 平均漲跌: {summary.get('avg_change', 0):+.2f}%")
            
            # 顯示部分數據
            print("\n📈 市場指數數據:")
            for symbol, data in list(results.get('market_indices', {}).items())[:3]:
                if 'data' in data:
                    price_data = data['data']
                    name = data.get('name', symbol)
                    price = price_data.get('close', 0)
                    change = price_data.get('pct_change', 0)
                    trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    print(f"   {trend} {name}: ${price:.2f} ({change:+.2f}%)")
            
            print("\n📊 熱門股票數據:")
            for symbol, data in list(results.get('hot_stocks', {}).items())[:5]:
                price = data.get('close', 0)
                change = data.get('pct_change', 0)
                volume = data.get('volume', 0)
                trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                print(f"   {trend} {symbol}: ${price:.2f} ({change:+.2f}%) 量:{volume:,.0f}")
            
            return True
        else:
            print("❌ 掃描失敗")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_concurrent_scanning():
    """測試並發掃描"""
    print("\n" + "=" * 60)
    print("🔄 測試並發掃描能力")
    print("=" * 60)
    
    try:
        from optimized_market_scanner import OptimizedMarketScanner
        
        results = []
        threads = []
        
        def run_scan(scanner_id):
            """執行單次掃描"""
            try:
                scanner = OptimizedMarketScanner()
                start_time = time.time()
                result = scanner.run_full_scan()
                end_time = time.time()
                
                results.append({
                    'scanner_id': scanner_id,
                    'success': result is not None,
                    'duration': end_time - start_time,
                    'data_count': len(result.get('hot_stocks', {})) if result else 0
                })
                
                print(f"   掃描器 {scanner_id}: {'✅ 成功' if result else '❌ 失敗'} ({end_time - start_time:.2f}秒)")
                
            except Exception as e:
                print(f"   掃描器 {scanner_id}: ❌ 異常 - {e}")
                results.append({
                    'scanner_id': scanner_id,
                    'success': False,
                    'duration': 0,
                    'error': str(e)
                })
        
        # 啟動3個並發掃描
        print("🚀 啟動3個並發掃描...")
        for i in range(3):
            thread = threading.Thread(target=run_scan, args=(i+1,))
            threads.append(thread)
            thread.start()
        
        # 等待所有線程完成
        for thread in threads:
            thread.join()
        
        # 統計結果
        successful = sum(1 for r in results if r['success'])
        total = len(results)
        avg_duration = sum(r['duration'] for r in results if r['success']) / successful if successful > 0 else 0
        
        print(f"\n📊 並發測試結果:")
        print(f"   成功率: {successful}/{total} ({successful/total*100:.1f}%)")
        print(f"   平均耗時: {avg_duration:.2f} 秒")
        
        return successful > 0
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_memory_usage():
    """測試記憶體使用情況"""
    print("\n" + "=" * 60)
    print("💾 測試記憶體使用情況")
    print("=" * 60)
    
    try:
        import psutil
        import os
        from optimized_market_scanner import OptimizedMarketScanner
        
        process = psutil.Process(os.getpid())
        
        # 測試前記憶體
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        print(f"📊 測試前記憶體: {memory_before:.2f} MB")
        
        # 執行多次掃描
        scanner = OptimizedMarketScanner()
        
        for i in range(3):
            print(f"   執行第 {i+1} 次掃描...")
            result = scanner.run_full_scan()
            
            memory_current = process.memory_info().rss / 1024 / 1024
            print(f"   第 {i+1} 次後記憶體: {memory_current:.2f} MB")
        
        # 清除緩存
        scanner.clear_cache()
        
        # 測試後記憶體
        memory_after = process.memory_info().rss / 1024 / 1024
        memory_increase = memory_after - memory_before
        
        print(f"\n💾 記憶體使用分析:")
        print(f"   測試前: {memory_before:.2f} MB")
        print(f"   測試後: {memory_after:.2f} MB")
        print(f"   增加量: {memory_increase:.2f} MB")
        
        if memory_increase < 50:  # 小於50MB增長
            print("   ✅ 記憶體使用良好")
            return True
        else:
            print("   ⚠️ 記憶體使用偏高")
            return False
            
    except ImportError:
        print("⚠️ psutil 未安裝，跳過記憶體測試")
        return True
    except Exception as e:
        print(f"❌ 記憶體測試失敗: {e}")
        return False

def test_error_handling():
    """測試錯誤處理"""
    print("\n" + "=" * 60)
    print("🛡️ 測試錯誤處理機制")
    print("=" * 60)
    
    try:
        from optimized_market_scanner import OptimizedMarketScanner
        
        scanner = OptimizedMarketScanner()
        
        # 測試無效股票代碼
        print("🧪 測試無效股票代碼...")
        invalid_data = scanner.get_yahoo_market_data("INVALID_SYMBOL")
        
        if invalid_data is None:
            print("   ✅ 正確處理無效股票代碼")
        else:
            print("   ⚠️ 無效股票代碼處理異常")
        
        # 測試網路錯誤模擬
        print("🧪 測試錯誤恢復機制...")
        
        # 暫時修改URL來模擬錯誤
        original_url = "https://tw.quote.finance.yahoo.net"
        
        # 執行掃描（應該能處理部分失敗）
        results = scanner.run_full_scan()
        
        if results:
            print("   ✅ 錯誤恢復機制正常")
            return True
        else:
            print("   ⚠️ 錯誤恢復需要改進")
            return False
            
    except Exception as e:
        print(f"❌ 錯誤處理測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 優化市場掃描器測試程式")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("效能測試", test_scanner_performance()))
    test_results.append(("並發測試", test_concurrent_scanning()))
    test_results.append(("記憶體測試", test_memory_usage()))
    test_results.append(("錯誤處理測試", test_error_handling()))
    
    # 顯示測試總結
    print("\n" + "=" * 60)
    print("📋 測試總結")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有測試通過！優化掃描器運作正常")
        print("💡 建議：可以在主程式中啟用優化掃描器")
        return True
    elif passed >= total * 0.75:
        print("⚠️ 大部分測試通過，建議檢查失敗項目")
        return True
    else:
        print("❌ 多項測試失敗，需要檢查和修復")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 測試程式異常: {e}")
        sys.exit(1)
