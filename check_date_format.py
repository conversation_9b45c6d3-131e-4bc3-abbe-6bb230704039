#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查資料庫中的日期格式
"""

import sqlite3
import pandas as pd

def check_date_format():
    """檢查資料庫中的日期格式"""
    
    db_file = r'D:\Finlab\history\tables\divide_ratio.db'
    
    try:
        conn = sqlite3.connect(db_file)
        
        # 檢查日期格式
        cursor = conn.cursor()
        cursor.execute("SELECT date FROM divide_ratio LIMIT 5")
        dates = cursor.fetchall()
        
        print("📅 資料庫中的日期格式:")
        for date in dates:
            print(f"   {date[0]} (類型: {type(date[0])})")
        
        # 使用 pandas 讀取檢查
        df = pd.read_sql("SELECT stock_id, date FROM divide_ratio LIMIT 5", conn)
        print(f"\n📊 Pandas 讀取的日期格式:")
        for i, row in df.iterrows():
            print(f"   {row['date']} (類型: {type(row['date'])})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

if __name__ == "__main__":
    check_date_format()
