#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試年份選擇功能
"""

import sys
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QCheckBox, QScrollArea, QWidget)
from PyQt6.QtCore import Qt

class YearSelectionTestDialog(QDialog):
    """年份選擇測試對話框"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("年份選擇測試")
        self.setFixedSize(400, 350)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 說明文字
        info_label = QLabel("""
📊 除權息資料年份選擇測試

🌐 資料來源:
• Goodinfo 台灣股市資訊網
• 包含即將除權息的完整清單
• 提供準確的除權息日期和股利資訊

請選擇要下載的年份 (2010-2025):
        """)
        layout.addWidget(info_label)
        
        # 年份選擇 - 擴展到2010年
        current_year = datetime.now().year
        self.year_checkboxes = {}
        
        # 創建滾動區域來容納更多年份選項
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 從當前年份到2010年的年份選項
        for year in range(current_year, 2009, -1):
            checkbox = QCheckBox(f"{year} 年")
            if year == current_year:
                checkbox.setChecked(True)
            self.year_checkboxes[year] = checkbox
            scroll_layout.addWidget(checkbox)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setMaximumHeight(200)  # 限制高度以顯示滾動條
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 按鈕
        button_layout = QHBoxLayout()
        test_btn = QPushButton("🧪 測試選擇")
        select_all_btn = QPushButton("✅ 全選")
        clear_all_btn = QPushButton("❌ 清除")
        close_btn = QPushButton("🚪 關閉")
        
        button_layout.addWidget(test_btn)
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(clear_all_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
        
        # 結果顯示
        self.result_label = QLabel("尚未測試")
        self.result_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(self.result_label)
        
        # 連接信號
        test_btn.clicked.connect(self.test_selection)
        select_all_btn.clicked.connect(self.select_all)
        clear_all_btn.clicked.connect(self.clear_all)
        close_btn.clicked.connect(self.close)
    
    def test_selection(self):
        """測試選擇結果"""
        selected_years = [year for year, checkbox in self.year_checkboxes.items() if checkbox.isChecked()]
        
        if not selected_years:
            self.result_label.setText("❌ 未選擇任何年份")
            self.result_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
        else:
            selected_years.sort(reverse=True)  # 降序排列
            result_text = f"✅ 已選擇 {len(selected_years)} 個年份:\n"
            result_text += f"📅 年份清單: {', '.join(map(str, selected_years))}\n"
            result_text += f"📊 年份範圍: {min(selected_years)} - {max(selected_years)}"
            
            self.result_label.setText(result_text)
            self.result_label.setStyleSheet("color: green; font-weight: bold; padding: 10px;")
            
            print(f"🎯 測試結果:")
            print(f"   選擇年份數量: {len(selected_years)}")
            print(f"   年份清單: {selected_years}")
            print(f"   最早年份: {min(selected_years)}")
            print(f"   最晚年份: {max(selected_years)}")
    
    def select_all(self):
        """全選所有年份"""
        for checkbox in self.year_checkboxes.values():
            checkbox.setChecked(True)
        self.result_label.setText("✅ 已全選所有年份")
        self.result_label.setStyleSheet("color: blue; font-weight: bold; padding: 10px;")
    
    def clear_all(self):
        """清除所有選擇"""
        for checkbox in self.year_checkboxes.values():
            checkbox.setChecked(False)
        self.result_label.setText("❌ 已清除所有選擇")
        self.result_label.setStyleSheet("color: orange; font-weight: bold; padding: 10px;")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    print("🚀 啟動年份選擇測試...")
    print(f"📅 當前年份: {datetime.now().year}")
    print(f"📊 支援年份範圍: 2010 - {datetime.now().year}")
    
    dialog = YearSelectionTestDialog()
    dialog.show()
    
    result = app.exec()
    
    print("🎉 測試完成!")
    return result

if __name__ == "__main__":
    main()
