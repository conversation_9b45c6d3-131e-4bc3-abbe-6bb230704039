#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進的ROE資料下載器
使用多種真實資料源獲取ROE資料，不使用模擬資料
"""

import os
import sys
import sqlite3
import pandas as pd
import requests
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

class ImprovedROEDownloader:
    def __init__(self):
        self.db_path = "D:/Finlab/history/tables/roe_data.db"
        self.download_dir = "D:/Finlab/history/tables"
        
        # 確保目錄存在
        os.makedirs(self.download_dir, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化資料庫
        self.init_database()

    def init_database(self):
        """初始化ROE資料庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS roe_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    roe_value REAL,
                    rank_position INTEGER,
                    market_cap REAL,
                    industry TEXT,
                    data_source TEXT DEFAULT 'real_data',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, year)
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ ROE資料庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")

    def download_roe_data(self, year=None):
        """下載真實ROE資料"""
        if year is None:
            year = datetime.now().year - 1
        
        self.logger.info(f"🚀 開始下載 {year} 年度真實ROE資料...")
        
        # 方法1: 使用改進的Selenium方法
        roe_data = self.fetch_with_selenium(year)
        
        if not roe_data:
            # 方法2: 嘗試直接HTTP請求
            roe_data = self.fetch_with_requests(year)
        
        if not roe_data:
            # 方法3: 嘗試其他網站
            roe_data = self.fetch_from_other_sources(year)
        
        if roe_data:
            self.logger.info(f"✅ 成功獲取 {len(roe_data)} 筆真實ROE資料")
            
            # 儲存到資料庫
            success = self.save_to_database(roe_data, year)
            if success:
                # 匯出CSV
                csv_file = self.export_to_csv(year)
                return csv_file
        else:
            self.logger.error(f"❌ 無法獲取 {year} 年度真實ROE資料")
            self.logger.error("❌ 請檢查網路連線或稍後再試")
        
        return None

    def fetch_with_selenium(self, year):
        """使用Selenium獲取ROE資料"""
        driver = None
        try:
            self.logger.info(f"🤖 使用Selenium獲取 {year} 年度ROE資料...")
            
            # 設置Chrome選項
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            
            # 設置User-Agent
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            driver = webdriver.Chrome(options=options)
            
            # 訪問GoodInfo ROE排行頁面
            url = "https://goodinfo.tw/tw2/StockList.asp?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
            self.logger.info(f"📱 訪問頁面: {url}")
            driver.get(url)
            
            # 等待頁面載入
            wait = WebDriverWait(driver, 30)
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            
            # 等待額外時間確保資料載入完成
            time.sleep(8)
            
            # 尋找ROE資料表格
            tables = driver.find_elements(By.TAG_NAME, "table")
            self.logger.info(f"🔍 找到 {len(tables)} 個表格")
            
            roe_data = []
            
            for table_idx, table in enumerate(tables):
                try:
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    if len(rows) < 10:  # 跳過太小的表格
                        continue
                        
                    self.logger.info(f"📋 檢查表格 {table_idx + 1}: {len(rows)} 行")
                    
                    # 檢查是否為ROE資料表格
                    header_row = rows[0] if rows else None
                    if header_row:
                        header_text = header_row.text.lower()
                        if any(keyword in header_text for keyword in ['roe', '股東權益報酬率', '代號', '名稱']):
                            self.logger.info(f"✅ 找到ROE資料表格")
                            roe_data = self.parse_table_data(table, year)
                            if roe_data:
                                break
                except Exception as e:
                    self.logger.warning(f"⚠️ 解析表格 {table_idx + 1} 失敗: {e}")
                    continue
            
            return roe_data
            
        except Exception as e:
            self.logger.error(f"❌ Selenium獲取失敗: {e}")
            return None
        finally:
            if driver:
                driver.quit()

    def parse_table_data(self, table, year):
        """解析表格資料"""
        roe_records = []
        
        try:
            rows = table.find_elements(By.TAG_NAME, "tr")
            
            # 解析表頭
            header_row = rows[0] if rows else None
            if not header_row:
                return []
            
            headers = []
            for cell in header_row.find_elements(By.TAG_NAME, "th"):
                headers.append(cell.text.strip())
            
            if not headers:
                for cell in header_row.find_elements(By.TAG_NAME, "td"):
                    headers.append(cell.text.strip())
            
            self.logger.info(f"📋 表頭: {headers}")
            
            # 尋找關鍵欄位
            code_idx = self.find_column_index(headers, ['代號', '股票代號', '代碼'])
            name_idx = self.find_column_index(headers, ['名稱', '股票名稱', '公司名稱'])
            roe_idx = self.find_column_index(headers, ['roe', 'ROE', '股東權益報酬率', 'ROE(%)'])
            
            if code_idx == -1 or roe_idx == -1:
                self.logger.warning(f"⚠️ 找不到必要欄位: 代號={code_idx}, ROE={roe_idx}")
                return []
            
            # 解析資料行
            for row_idx, row in enumerate(rows[1:], 1):
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) <= max(code_idx, roe_idx):
                        continue
                    
                    stock_code = cells[code_idx].text.strip()
                    stock_name = cells[name_idx].text.strip() if name_idx != -1 else ""
                    roe_text = cells[roe_idx].text.strip()
                    
                    # 驗證股票代碼
                    if not self.is_valid_stock_code(stock_code):
                        continue
                    
                    # 解析ROE值
                    roe_value = self.parse_roe_value(roe_text)
                    if roe_value is None:
                        continue
                    
                    record = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'year': year,
                        'roe_value': roe_value,
                        'rank_position': row_idx,
                        'market_cap': None,
                        'industry': '',
                        'data_source': 'goodinfo_real'
                    }
                    
                    roe_records.append(record)
                    
                    if len(roe_records) >= 500:  # 限制記錄數
                        break
                        
                except Exception as e:
                    continue
            
            self.logger.info(f"✅ 成功解析 {len(roe_records)} 筆ROE資料")
            return roe_records
            
        except Exception as e:
            self.logger.error(f"❌ 解析表格失敗: {e}")
            return []

    def find_column_index(self, headers, possible_names):
        """尋找欄位索引"""
        for i, header in enumerate(headers):
            for name in possible_names:
                if name.lower() in header.lower():
                    return i
        return -1

    def is_valid_stock_code(self, code):
        """驗證股票代碼"""
        if not code or len(code) < 4:
            return False
        
        # 檢查是否為純數字
        if not code.isdigit():
            return False
        
        # 檢查長度（4-6位）
        if len(code) < 4 or len(code) > 6:
            return False
        
        return True

    def parse_roe_value(self, roe_text):
        """解析ROE數值"""
        try:
            if not roe_text or roe_text in ['-', 'N/A', '']:
                return None
            
            # 移除百分號和逗號
            roe_text = roe_text.replace('%', '').replace(',', '').strip()
            
            # 轉換為浮點數
            return float(roe_text)
        except:
            return None

    def fetch_with_requests(self, year):
        """使用HTTP請求獲取ROE資料"""
        try:
            self.logger.info(f"🌐 使用HTTP請求獲取 {year} 年度ROE資料...")
            
            # 這裡可以實現HTTP請求邏輯
            # 由於GoodInfo有反爬蟲機制，這個方法可能需要更複雜的實現
            self.logger.warning("⚠️ HTTP請求方法開發中...")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ HTTP請求失敗: {e}")
            return None

    def fetch_from_other_sources(self, year):
        """從其他資料源獲取ROE資料"""
        try:
            self.logger.info(f"🔍 嘗試其他資料源獲取 {year} 年度ROE資料...")
            
            # 這裡可以實現其他資料源的邏輯
            self.logger.warning("⚠️ 其他資料源開發中...")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 其他資料源失敗: {e}")
            return None

    def save_to_database(self, roe_data, year):
        """儲存ROE資料到資料庫"""
        if not roe_data:
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 先刪除該年度的舊資料
            cursor.execute('DELETE FROM roe_data WHERE year = ?', (year,))
            
            # 插入新資料
            for record in roe_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO roe_data 
                    (stock_code, stock_name, year, roe_value, rank_position, 
                     market_cap, industry, data_source, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    record['stock_code'],
                    record['stock_name'],
                    record['year'],
                    record['roe_value'],
                    record['rank_position'],
                    record['market_cap'],
                    record['industry'],
                    record['data_source']
                ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 成功儲存 {len(roe_data)} 筆ROE資料到資料庫")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 儲存資料庫失敗: {e}")
            return False

    def export_to_csv(self, year):
        """匯出ROE資料到CSV"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"roe_data_{year}_{timestamp}.csv"
            filepath = os.path.join(self.download_dir, filename)
            
            conn = sqlite3.connect(self.db_path)
            query = '''
                SELECT stock_code, stock_name, year, roe_value, rank_position,
                       market_cap, industry, data_source
                FROM roe_data
                WHERE year = ?
                ORDER BY rank_position, roe_value DESC
            '''
            df = pd.read_sql_query(query, conn, params=(year,))
            conn.close()
            
            if not df.empty:
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                self.logger.info(f"✅ CSV匯出成功: {filepath}")
                return filepath
            else:
                self.logger.warning("⚠️ 無資料可匯出")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ CSV匯出失敗: {e}")
            return None

def main():
    """測試改進的ROE下載器"""
    print("🚀 測試改進的ROE下載器")
    print("=" * 50)
    
    downloader = ImprovedROEDownloader()
    
    # 下載2024年ROE資料
    year = 2024
    csv_file = downloader.download_roe_data(year)
    
    if csv_file:
        print(f"✅ 下載成功: {csv_file}")
    else:
        print("❌ 下載失敗")

if __name__ == "__main__":
    main()
