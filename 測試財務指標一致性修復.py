#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財務指標一致性修復
確保get_real_financial_info與排行榜計算方法一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_info_consistency():
    """測試財務指標一致性修復"""
    print("🧪 測試財務指標一致性修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox, QTextEdit
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試財務指標一致性修復")
        dialog.setFixedSize(1200, 1100)
        
        layout = QVBoxLayout(dialog)
        
        # 修復說明
        intro_group = QGroupBox("📊 財務指標一致性修復")
        intro_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        intro_layout = QVBoxLayout(intro_group)
        
        intro_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; text-align: center;">📊 與排行榜計算方法一致性修復</h3>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #e8f5e8; border-radius: 6px; border-left: 4px solid #4caf50;">
                <h4 style="color: #2e7d32; margin: 0 0 8px 0;">✅ 修復內容：</h4>
                <ul style="margin: 0; padding-left: 20px; color: #2e7d32; font-size: 11px;">
                    <li><strong>統一資料來源：</strong>使用與排行榜相同的pe_data.db和price.db</li>
                    <li><strong>統一查詢邏輯：</strong>向前查找7天內最接近的資料日期</li>
                    <li><strong>統一EPS計算：</strong>使用相同的_calculate_eps函數</li>
                    <li><strong>統一格式化：</strong>保持與排行榜相同的數值格式</li>
                    <li><strong>統一錯誤處理：</strong>相同的N/A處理邏輯</li>
                </ul>
            </div>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
                <h4 style="color: #0d47a1; margin: 0 0 8px 0;">🔄 一致性保證：</h4>
                <div style="text-align: center; font-family: 'Consolas', monospace; font-size: 12px; color: #1565c0; background-color: #e1f5fe; padding: 8px; border-radius: 4px;">
                    <strong>排行榜數據 = 個股財務指標數據</strong>
                </div>
                <p style="margin: 8px 0 0 0; font-size: 10px; color: #0277bd; text-align: center;">
                    確保用戶在排行榜和個股查看時看到相同的財務指標
                </p>
            </div>
        </div>
        """
        
        intro_label = QLabel()
        intro_label.setTextFormat(Qt.TextFormat.RichText)
        intro_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        intro_label.setWordWrap(True)
        intro_label.setText(intro_text)
        intro_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        intro_layout.addWidget(intro_label)
        layout.addWidget(intro_group)
        
        # 實際測試結果
        test_group = QGroupBox("🧪 一致性測試結果")
        test_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        test_layout = QVBoxLayout(test_group)
        
        test_results_text = QTextEdit()
        test_results_text.setMaximumHeight(400)
        test_results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #333333;
            }
        """)
        
        # 執行實際測試
        test_output = "🔍 財務指標一致性測試結果:\n\n"
        
        # 測試股票列表（包含排行榜中的股票）
        test_stocks = ['2301', '2330', '2317', '1101', '8021', '6669']
        
        for stock_code in test_stocks:
            test_output += f"📊 {stock_code}:\n"
            try:
                # 測試修復後的財務資訊獲取
                financial_info = gui.get_real_financial_info(stock_code)
                
                test_output += f"  資料來源: pe_data.db + price.db (與排行榜一致)\n"
                for key, value in financial_info.items():
                    test_output += f"  {key}: {value}\n"
                
                # 驗證EPS計算邏輯
                if '股價' in financial_info and '本益比' in financial_info and financial_info['股價'] != 'N/A' and financial_info['本益比'] != 'N/A':
                    try:
                        price = float(financial_info['股價'])
                        pe = float(financial_info['本益比'])
                        # 使用與排行榜相同的計算函數
                        calculated_eps = gui._calculate_eps(price, pe)
                        test_output += f"  驗證EPS計算: _calculate_eps({price}, {pe}) = {calculated_eps}\n"
                        
                        if calculated_eps == financial_info['EPS']:
                            test_output += f"  ✅ EPS計算一致性: 通過\n"
                        else:
                            test_output += f"  ❌ EPS計算一致性: 失敗\n"
                    except:
                        test_output += f"  ⚠️ EPS計算驗證: 無法驗證\n"
                
                test_output += f"  ✅ 測試完成\n\n"
                
            except Exception as e:
                test_output += f"  ❌ 測試失敗: {e}\n\n"
        
        test_results_text.setPlainText(test_output)
        test_layout.addWidget(test_results_text)
        layout.addWidget(test_group)
        
        # 技術對比說明
        comparison_group = QGroupBox("🔧 修復前後對比")
        comparison_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        comparison_layout = QVBoxLayout(comparison_group)

        comparison_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h4 style="color: #2c3e50; margin: 0 0 10px 0;">修復對比：</h4>
            
            <table style="width: 100%; border-collapse: collapse; font-size: 10px;">
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">項目</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">修復前</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">修復後</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>資料來源</strong></td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545;">多個不同路徑，不一致</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">統一使用pe_data.db + price.db</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>查詢邏輯</strong></td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545;">簡單的最新資料查詢</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">向前查找7天內最接近日期</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>EPS計算</strong></td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545;">簡單除法，錯誤處理不完整</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">使用_calculate_eps函數，完整錯誤處理</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>格式化</strong></td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545;">不一致的格式</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">與排行榜相同的格式</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>一致性</strong></td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #dc3545;">排行榜 ≠ 個股查看</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">排行榜 = 個股查看</td>
                </tr>
            </table>
            
            <div style="margin: 15px 0; padding: 10px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #4caf50;">
                <p style="margin: 0; font-size: 11px; color: #2e7d32;">
                    <strong>🎯 修復效果：</strong><br>
                    現在用戶在月營收排行榜中看到的財務指標，與點擊個股查看的財務指標完全一致，
                    確保了數據的統一性和用戶體驗的一致性。
                </p>
            </div>
        </div>
        """

        comparison_label = QLabel()
        comparison_label.setTextFormat(Qt.TextFormat.RichText)
        comparison_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        comparison_label.setWordWrap(True)
        comparison_label.setText(comparison_text)
        comparison_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        comparison_layout.addWidget(comparison_label)
        layout.addWidget(comparison_group)
        
        print("✅ 對話框創建成功")
        print("📋 修復內容：")
        print("  1. 統一使用pe_data.db和price.db作為資料來源")
        print("  2. 採用與排行榜相同的查詢邏輯")
        print("  3. 使用相同的_calculate_eps函數")
        print("  4. 保持相同的數值格式化方式")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 財務指標一致性修復測試")
    print("=" * 50)
    
    result = test_financial_info_consistency()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 財務指標計算已與排行榜保持一致")
        print("📊 確保用戶看到統一的財務數據")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
