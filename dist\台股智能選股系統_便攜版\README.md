# 📊 Finlab財務數據管理系統

一個完整的財務數據分析、移植和管理系統，專為處理Finlab課程中的財務數據表格而設計。

## 🎯 功能特色

### 📋 數據分析與移植
- **自動分析** Finlab pkl數據表格
- **智能移植** 將pkl檔案轉換為SQLite數據庫
- **完整報告** 生成詳細的數據分析報告
- **增量更新** 支援數據的增量更新機制

### 🖥️ 直觀GUI界面
- **用戶友好** 的圖形化操作界面
- **實時預覽** 數據表格內容
- **統計分析** 內建數據統計功能
- **圖表展示** 支援多種圖表類型

### 🕷️ 自動數據爬取
- **API整合** 支援從Finlab API獲取最新數據
- **排程更新** 自動化的數據更新機制
- **錯誤處理** 完善的錯誤處理和重試機制

### 🧪 完整測試套件
- **單元測試** 覆蓋所有核心功能
- **整合測試** 端到端的系統測試
- **模擬測試** 使用Mock進行API測試

## 📦 支援的數據表格

| 表格名稱 | 描述 | 更新頻率 |
|---------|------|---------|
| 📊 資產負債表 | 公司資產、負債和股東權益 | 季度 |
| 💰 綜合損益表 | 營收、成本、費用和獲利 | 季度 |
| 💸 現金流量表 | 營業、投資、融資現金流 | 季度 |
| 📈 月營收報告 | 每月營業收入數據 | 月度 |
| 📊 本益比數據 | 股價本益比歷史數據 | 每日 |
| 💎 股利發放 | 現金股利和股票股利 | 年度 |
| 🏦 融資融券 | 融資融券餘額數據 | 每日 |
| 📊 基準指數 | 大盤指數和基準數據 | 每日 |
| 🌍 外資持股 | 外資持股比例數據 | 每日 |

## 🚀 快速開始

### 1. 環境準備

```bash
# 安裝Python依賴
pip install pandas sqlite3 tkinter matplotlib seaborn requests pyyaml schedule

# 或使用requirements.txt
pip install -r requirements.txt
```

### 2. 配置設置

編輯 `config.yaml` 文件，設置您的Finlab數據路徑：

```yaml
data_sources:
  finlab_tables_path: "您的Finlab數據表格路徑"
```

### 3. 啟動系統

```bash
# 啟動GUI界面
python run_finlab_system.py gui

# 執行數據分析
python run_finlab_system.py analyze

# 移植數據到本地
python run_finlab_system.py migrate

# 檢查系統狀態
python run_finlab_system.py status
```

## 📖 使用指南

### GUI操作流程

1. **設置數據路徑**
   - 在控制面板中設置Finlab數據表格路徑
   - 點擊"瀏覽"按鈕選擇目錄

2. **分析數據表格**
   - 點擊"🔍 分析數據表格"按鈕
   - 系統會自動分析所有pkl檔案

3. **移植數據**
   - 點擊"🚀 移植數據"按鈕
   - 將pkl檔案轉換為SQLite數據庫

4. **查看數據**
   - 從下拉選單選擇數據表格
   - 點擊"📈 查看數據"預覽內容
   - 點擊"📊 數據統計"查看統計信息

### 命令行操作

```bash
# 顯示幫助信息
python run_finlab_system.py --help

# 詳細輸出模式
python run_finlab_system.py analyze --verbose

# 指定自定義路徑
python run_finlab_system.py migrate --finlab-path "自定義路徑"

# 執行測試
python run_finlab_system.py test
```

## 🏗️ 系統架構

```
finlab_system/
├── 📊 核心模組
│   ├── finlab_tables_analysis_and_migration.py  # 數據分析與移植
│   ├── finlab_tables_gui.py                     # GUI界面
│   └── run_finlab_system.py                     # 系統啟動器
├── 🧪 測試模組
│   └── test_finlab_system.py                    # 測試套件
├── ⚙️ 配置文件
│   ├── config.yaml                              # 系統配置
│   └── requirements.txt                         # Python依賴
├── 📁 數據目錄
│   └── data/finlab_tables/                      # 本地數據存儲
├── 📝 日誌目錄
│   └── logs/                                    # 系統日誌
└── 📄 文檔
    └── README.md                                # 使用說明
```

## 🔧 進階配置

### API設置

如需使用API爬取功能，請在 `.env` 文件中設置API金鑰：

```env
FINLAB_API_KEY=your_api_key_here
```

### 自動更新排程

系統支援自動化數據更新：

```python
from finlab_tables_analysis_and_migration import FinlabDataCrawler

crawler = FinlabDataCrawler()
crawler.schedule_daily_update()  # 每日08:00自動更新
```

### 數據庫備份

```python
# 手動備份數據庫
import shutil
shutil.copy2("data/finlab_tables/finlab_financial_data.db", "backup.db")
```

## 🧪 測試

```bash
# 執行所有測試
python test_finlab_system.py

# 執行特定測試類
python -m unittest test_finlab_system.TestFinlabTablesAnalyzer

# 執行整合測試
python run_finlab_system.py test
```

## 📊 數據格式

### 輸入格式
- **pkl檔案**: Pandas DataFrame格式
- **索引**: 日期時間索引
- **列**: 股票代碼

### 輸出格式
- **SQLite數據庫**: 結構化關聯式數據庫
- **CSV檔案**: 可選的CSV匯出
- **報告**: Markdown/HTML格式

## 🔍 故障排除

### 常見問題

1. **GUI無法啟動**
   ```bash
   # 檢查tkinter是否安裝
   python -c "import tkinter; print('tkinter OK')"
   ```

2. **數據庫連接失敗**
   ```bash
   # 檢查數據庫檔案權限
   ls -la data/finlab_tables/
   ```

3. **pkl檔案讀取失敗**
   ```bash
   # 檢查檔案格式和編碼
   python -c "import pickle; pickle.load(open('file.pkl', 'rb'))"
   ```

### 日誌檢查

系統日誌位於 `logs/` 目錄：

```bash
# 查看最新日誌
tail -f logs/finlab_system_$(date +%Y%m%d).log
```

## 🤝 貢獻指南

歡迎提交Issue和Pull Request！

1. Fork本專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟Pull Request

## 📄 授權條款

本專案採用MIT授權條款 - 詳見 [LICENSE](LICENSE) 文件

## 🙏 致謝

- 感謝Finlab提供優質的財務數據和課程內容
- 感謝Python社群提供強大的數據處理工具

---

**📞 聯絡方式**

如有問題或建議，請通過以下方式聯絡：
- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [提交Issue](https://github.com/your-repo/issues)

**⭐ 如果這個專案對您有幫助，請給我們一個星星！**
