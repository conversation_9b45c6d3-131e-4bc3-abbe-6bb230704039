
# 🔧 自動執行策略故障排除指南

## 常見問題及解決方案

### 1. "所有策略執行都失敗了" 錯誤

#### 可能原因：
- 數據庫連接問題
- 股票數據未載入
- 策略執行超時
- 網路連接問題

#### 解決方案：
1. **檢查數據庫連接**
   ```
   確保 price 數據庫正常連接
   檢查數據庫配置是否正確
   ```

2. **檢查股票數據**
   ```
   確保股票數據已正確載入
   檢查數據更新是否正常
   ```

3. **增加等待時間**
   ```
   策略執行可能需要更長時間
   網路較慢時需要更多耐心
   ```

### 2. 策略執行超時

#### 修復措施：
- 等待時間從30秒增加到60秒
- 改進執行成功判斷邏輯
- 增加前置條件檢查

#### 新的執行邏輯：
```python
def execute_strategy_synchronously(self, strategy_name):
    # 1. 檢查前置條件
    # 2. 執行策略
    # 3. 智能等待完成
    # 4. 驗證執行結果
```

### 3. 前置條件檢查

#### 檢查項目：
- 數據庫連接狀態
- 股票數據可用性
- 系統資源狀況

#### 使用方法：
```python
if window.check_strategy_prerequisites():
    # 可以執行策略
else:
    # 需要先解決前置條件問題
```

### 4. 調試建議

#### 查看日誌：
```
🔄 開始自動執行策略: 策略名稱
🚀 執行策略: 策略名稱
✅ 策略執行成功: 策略名稱，獲得 X 支股票
```

#### 常見日誌信息：
- `❌ 數據庫未連接` - 需要檢查數據庫配置
- `❌ 沒有可用的股票數據` - 需要載入股票數據
- `⚠️ 策略執行超時但有結果` - 執行成功但較慢
- `❌ 策略執行超時且無結果` - 執行失敗

### 5. 最佳實踐

#### 使用建議：
1. 確保網路連接穩定
2. 在非高峰時段執行
3. 一次不要執行太多策略
4. 定期檢查數據庫狀態

#### 故障排除步驟：
1. 檢查數據庫連接
2. 驗證股票數據
3. 測試單個策略執行
4. 檢查系統資源
5. 查看詳細日誌

### 6. 聯繫支援

如果問題持續存在：
1. 收集詳細的錯誤日誌
2. 記錄重現步驟
3. 檢查系統環境
4. 提供具體的錯誤信息
