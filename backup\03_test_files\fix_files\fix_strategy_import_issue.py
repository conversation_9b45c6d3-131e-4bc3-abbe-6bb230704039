#!/usr/bin/env python3
"""
修復策略匯入問題
解決策略結果顯示為"、、、、、、"符號的問題
"""

import logging
import re
from typing import List, Dict, Any

class StrategyImportFixer:
    """策略匯入問題修復器"""
    
    def __init__(self):
        self.name = "策略匯入修復器"
        
    def diagnose_table_content(self, result_table):
        """診斷表格內容問題"""
        print("🔍 診斷策略結果表格內容...")
        print("=" * 60)
        
        if not result_table or result_table.rowCount() == 0:
            print("❌ 表格為空或不存在")
            return False
        
        print(f"📊 表格基本信息:")
        print(f"  • 行數: {result_table.rowCount()}")
        print(f"  • 列數: {result_table.columnCount()}")
        
        # 檢查表頭
        headers = []
        for col in range(result_table.columnCount()):
            header = result_table.horizontalHeaderItem(col)
            header_text = header.text() if header else f"列{col}"
            headers.append(header_text)
        
        print(f"  • 表頭: {headers}")
        
        # 檢查前幾行內容
        print(f"\n📋 前5行內容檢查:")
        problem_found = False
        
        for row in range(min(5, result_table.rowCount())):
            row_data = []
            for col in range(result_table.columnCount()):
                item = result_table.item(row, col)
                if item:
                    text = item.text()
                    row_data.append(text)
                    
                    # 檢查是否有問題字符
                    if self._has_problem_characters(text):
                        problem_found = True
                        print(f"  ⚠️ 第{row}行第{col}列發現問題字符: '{text}'")
                else:
                    row_data.append("None")
            
            print(f"  第{row}行: {row_data}")
        
        return not problem_found
    
    def _has_problem_characters(self, text: str) -> bool:
        """檢查是否包含問題字符"""
        problem_patterns = [
            r'、{3,}',  # 連續3個以上的頓號
            r'^、+$',   # 只有頓號的字符串
            r'[^\w\s\u4e00-\u9fff\.\-]',  # 非正常字符（排除中文、數字、字母、點、減號）
        ]
        
        for pattern in problem_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def fix_table_content(self, result_table):
        """修復表格內容"""
        print("\n🔧 開始修復表格內容...")
        
        if not result_table or result_table.rowCount() == 0:
            print("❌ 無法修復：表格為空")
            return False
        
        fixed_count = 0
        
        for row in range(result_table.rowCount()):
            for col in range(result_table.columnCount()):
                item = result_table.item(row, col)
                if item:
                    original_text = item.text()
                    
                    if self._has_problem_characters(original_text):
                        # 嘗試修復
                        fixed_text = self._fix_text(original_text, row, col)
                        if fixed_text != original_text:
                            item.setText(fixed_text)
                            fixed_count += 1
                            print(f"  ✅ 修復第{row}行第{col}列: '{original_text}' -> '{fixed_text}'")
        
        print(f"\n📊 修復完成，共修復 {fixed_count} 個問題項目")
        return fixed_count > 0
    
    def _fix_text(self, text: str, row: int, col: int) -> str:
        """修復問題文本"""
        # 如果是股票代碼列（通常是第0列）
        if col == 0:
            # 嘗試提取4位數字股票代碼
            stock_code_match = re.search(r'\b(\d{4})\b', text)
            if stock_code_match:
                return stock_code_match.group(1)
            else:
                return f"未知{row:04d}"  # 生成一個臨時代碼
        
        # 如果是股票名稱列（通常是第1列）
        elif col == 1:
            # 移除問題字符，保留中文和基本符號
            cleaned = re.sub(r'[^\w\s\u4e00-\u9fff\(\)\-]', '', text)
            if cleaned.strip():
                return cleaned.strip()
            else:
                return f"股票{row:03d}"  # 生成一個臨時名稱
        
        # 其他列
        else:
            # 移除連續的頓號
            cleaned = re.sub(r'、{2,}', '、', text)
            cleaned = re.sub(r'^、+|、+$', '', cleaned)  # 移除開頭結尾的頓號
            
            if not cleaned.strip():
                return "N/A"
            
            return cleaned.strip()
    
    def extract_valid_stocks(self, result_table) -> List[Dict[str, str]]:
        """從表格中提取有效的股票信息"""
        print("\n📤 提取有效股票信息...")
        
        valid_stocks = []
        
        if not result_table or result_table.rowCount() == 0:
            print("❌ 表格為空，無法提取股票")
            return valid_stocks
        
        for row in range(result_table.rowCount()):
            try:
                # 獲取股票代碼（第0列）
                code_item = result_table.item(row, 0)
                code = code_item.text() if code_item else ""
                
                # 獲取股票名稱（第1列）
                name_item = result_table.item(row, 1)
                name = name_item.text() if name_item else ""
                
                # 驗證股票代碼格式
                if self._is_valid_stock_code(code):
                    stock_info = {
                        'code': code,
                        'name': name if name and not self._has_problem_characters(name) else f"股票{code}",
                        'row': row
                    }
                    valid_stocks.append(stock_info)
                    print(f"  ✅ 第{row}行: {code} - {stock_info['name']}")
                else:
                    print(f"  ❌ 第{row}行: 無效股票代碼 '{code}'")
                    
            except Exception as e:
                print(f"  ❌ 第{row}行: 處理錯誤 - {e}")
        
        print(f"\n📊 提取完成，找到 {len(valid_stocks)} 支有效股票")
        return valid_stocks
    
    def _is_valid_stock_code(self, code: str) -> bool:
        """驗證股票代碼是否有效"""
        if not code:
            return False
        
        # 台股代碼通常是4位數字
        return bool(re.match(r'^\d{4}$', code.strip()))
    
    def create_fixed_import_function(self):
        """創建修復版的匯入函數"""
        return '''
def import_stocks_from_strategy_fixed(self):
    """修復版：從策略結果匯入股票到監控列表"""
    try:
        # 檢查是否有策略結果
        if not hasattr(self, 'result_table') or self.result_table.rowCount() == 0:
            QMessageBox.warning(self, "匯入失敗",
                              "沒有可匯入的策略結果！\\n\\n請先執行策略選股，然後再匯入結果。")
            return

        # 使用修復器診斷和修復表格
        from fix_strategy_import_issue import StrategyImportFixer
        fixer = StrategyImportFixer()
        
        # 診斷表格內容
        is_healthy = fixer.diagnose_table_content(self.result_table)
        
        if not is_healthy:
            # 嘗試修復表格內容
            fixed = fixer.fix_table_content(self.result_table)
            if not fixed:
                QMessageBox.warning(self, "匯入失敗", "策略結果表格內容有問題，無法修復。")
                return
        
        # 提取有效股票
        valid_stocks = fixer.extract_valid_stocks(self.result_table)
        
        if not valid_stocks:
            QMessageBox.warning(self, "匯入失敗", "沒有找到有效的股票代碼。")
            return

        # 獲取當前監控列表
        current_stocks = self.monitor_stocks_input.text().strip()
        current_list = [s.strip() for s in current_stocks.split(',') if s.strip()] if current_stocks else []

        # 提取股票代碼
        new_stocks = [stock['code'] for stock in valid_stocks]

        # 合併股票列表（去重）
        all_stocks = list(dict.fromkeys(current_list + new_stocks))

        # 更新監控列表
        self.monitor_stocks_input.setText(','.join(all_stocks))

        # 顯示匯入結果
        imported_count = len(new_stocks)
        total_count = len(all_stocks)
        current_strategy = self.strategy_combo.currentText()

        stock_details = "\\n".join([f"{s['code']} - {s['name']}" for s in valid_stocks])

        QMessageBox.information(self, "匯入成功",
                              f"✅ 成功匯入 {imported_count} 支股票到監控列表！\\n\\n"
                              f"策略: {current_strategy}\\n"
                              f"匯入股票:\\n{stock_details}\\n\\n"
                              f"監控總數: {total_count} 支股票")

        # 自動更新監控列表顯示
        if hasattr(self, 'update_monitor_stocks'):
            self.update_monitor_stocks()

        logging.info(f"從策略 {current_strategy} 匯入 {imported_count} 支股票到監控列表")

    except Exception as e:
        QMessageBox.critical(self, "匯入錯誤", f"匯入股票時發生錯誤：\\n{str(e)}")
        logging.error(f"匯入策略股票失敗: {e}")
'''

def test_fixer():
    """測試修復器功能"""
    print("🧪 測試策略匯入修復器")
    print("=" * 60)
    
    fixer = StrategyImportFixer()
    
    # 測試問題字符檢測
    test_texts = [
        "2330",  # 正常股票代碼
        "台積電",  # 正常股票名稱
        "、、、、、、、、",  # 問題字符
        "2330、、、",  # 混合問題
        "",  # 空字符串
        "2330 台積電",  # 正常組合
    ]
    
    print("🔍 測試問題字符檢測:")
    for text in test_texts:
        has_problem = fixer._has_problem_characters(text)
        status = "❌ 有問題" if has_problem else "✅ 正常"
        print(f"  '{text}' -> {status}")
    
    # 測試文本修復
    print("\n🔧 測試文本修復:")
    problem_texts = [
        ("、、、、2330、、、", 0, 0),  # 股票代碼列
        ("、、、台積電、、、", 0, 1),  # 股票名稱列
        ("、、、、、、", 0, 2),  # 其他列
    ]
    
    for text, row, col in problem_texts:
        fixed = fixer._fix_text(text, row, col)
        print(f"  第{row}行第{col}列: '{text}' -> '{fixed}'")
    
    # 測試股票代碼驗證
    print("\n✅ 測試股票代碼驗證:")
    test_codes = ["2330", "1234", "abc", "12345", "", "、、、"]
    
    for code in test_codes:
        is_valid = fixer._is_valid_stock_code(code)
        status = "✅ 有效" if is_valid else "❌ 無效"
        print(f"  '{code}' -> {status}")
    
    print("\n🎉 修復器測試完成！")

if __name__ == "__main__":
    test_fixer()
