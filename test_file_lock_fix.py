#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試檔案鎖定修復
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd
import time

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_file_lock_handling():
    """測試檔案鎖定處理"""
    print("🧪 測試檔案鎖定處理...")
    
    try:
        from crawler import to_pickle
        
        # 創建測試資料
        test_data = pd.DataFrame({
            'value': [1, 2, 3]
        })
        test_data['date'] = pd.to_datetime(['2022-01-01', '2022-01-02', '2022-01-03'])
        test_data['stock_id'] = ['TEST1', 'TEST2', 'TEST3']
        test_data = test_data.set_index(['stock_id', 'date'])
        
        print(f"測試資料: {len(test_data)} 筆")
        
        # 測試存檔到一個新的檔案名稱，避免與正在運行的程式衝突
        test_table_name = 'test_file_lock'
        
        print("\n1️⃣ 測試正常存檔...")
        to_pickle(test_data, test_table_name)
        print("✅ 正常存檔成功")
        
        # 檢查檔案是否存在
        test_file = f"history/tables/{test_table_name}.pkl"
        if os.path.exists(test_file):
            print(f"✅ 檔案已創建: {test_file}")
            
            # 讀取驗證
            saved_data = pd.read_pickle(test_file)
            print(f"✅ 檔案可讀取，共 {len(saved_data)} 筆資料")
        else:
            print("❌ 檔案未創建")
            return False
        
        print("\n2️⃣ 測試追加存檔...")
        # 創建更多測試資料
        more_data = pd.DataFrame({
            'value': [4, 5]
        })
        more_data['date'] = pd.to_datetime(['2022-01-04', '2022-01-05'])
        more_data['stock_id'] = ['TEST4', 'TEST5']
        more_data = more_data.set_index(['stock_id', 'date'])
        
        to_pickle(more_data, test_table_name)
        print("✅ 追加存檔成功")
        
        # 驗證合併結果
        final_data = pd.read_pickle(test_file)
        print(f"✅ 最終資料: {len(final_data)} 筆")
        
        # 清理測試檔案
        try:
            os.remove(test_file)
            print("🧹 清理測試檔案")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_current_price_file():
    """檢查當前 price.pkl 檔案狀態"""
    print("\n🔍 檢查當前 price.pkl 檔案狀態...")
    
    price_file = "history/tables/price.pkl"
    
    if os.path.exists(price_file):
        size = os.path.getsize(price_file)
        mtime = os.path.getmtime(price_file)
        print(f"   檔案大小: {size:,} bytes")
        print(f"   修改時間: {datetime.fromtimestamp(mtime)}")
        
        try:
            # 嘗試讀取檔案
            data = pd.read_pickle(price_file)
            latest_date = data.index.get_level_values('date').max()
            earliest_date = data.index.get_level_values('date').min()
            print(f"   資料筆數: {len(data):,}")
            print(f"   日期範圍: {earliest_date} 至 {latest_date}")
            
            return True
        except Exception as e:
            print(f"   ❌ 檔案讀取失敗: {str(e)}")
            return False
    else:
        print("   檔案不存在")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 檔案鎖定修復測試")
    print("=" * 60)
    
    # 檢查當前檔案狀態
    file_ok = check_current_price_file()
    
    # 測試檔案鎖定處理
    test_ok = test_file_lock_handling()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    print(f"當前 price.pkl: {'✅ 正常' if file_ok else '❌ 有問題'}")
    print(f"檔案鎖定處理: {'✅ 正常' if test_ok else '❌ 有問題'}")
    
    if test_ok:
        print("\n🎉 檔案鎖定修復成功！")
        print("✨ 現在分批存檔應該更穩定:")
        print("   • 檔案鎖定時會重試")
        print("   • 存檔失敗不會重置計數")
        print("   • 避免每1筆就存檔的問題")
    else:
        print("\n⚠️ 還需要進一步檢查")
    
    print("\n💡 建議:")
    print("   • 如果 auto_update.py 正在運行，可能會有檔案鎖定")
    print("   • 可以等待當前批次完成，或重新啟動程式")
    print("   • 新的錯誤處理會讓程式更穩定")
    
    return test_ok

if __name__ == "__main__":
    main()
