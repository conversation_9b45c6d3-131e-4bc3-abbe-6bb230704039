#!/usr/bin/env python3
"""
測試所有技術指標策略的圖表解讀功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class StrategyTestWindow(QMainWindow):
    """策略測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle('🧪 技術指標策略解讀功能測試')
        self.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel('🧪 技術指標策略解讀功能測試')
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 測試按鈕
        strategies = [
            ('RSI策略', '📊 相對強弱指標'),
            ('MACD策略', '📈 指數平滑異同移動平均線'),
            ('布林通道策略', '🌊 布林格帶'),
            ('移動平均交叉策略', '📏 雙均線系統')
        ]
        
        for strategy_name, description in strategies:
            btn = QPushButton(f'{description}\n測試 {strategy_name} 解讀功能')
            btn.clicked.connect(lambda checked, name=strategy_name: self.test_strategy(name))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 15px;
                    margin: 5px;
                    border-radius: 8px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            layout.addWidget(btn)
        
        # 綜合測試按鈕
        comprehensive_btn = QPushButton('🚀 啟動完整解讀系統')
        comprehensive_btn.clicked.connect(self.launch_comprehensive_system)
        comprehensive_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                margin: 10px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(comprehensive_btn)
        
        # AI回測系統按鈕
        ai_btn = QPushButton('🤖 啟動AI技術指標回測系統')
        ai_btn.clicked.connect(self.launch_ai_system)
        ai_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px;
                margin: 5px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(ai_btn)
        
        # 創建回測GUI實例用於測試
        try:
            from ashui_backtest_gui import AshiuBacktestGUI
            self.backtest_gui = AshiuBacktestGUI()
            print("✅ 回測GUI模組載入成功")
        except ImportError as e:
            print(f"❌ 回測GUI模組載入失敗: {e}")
            self.backtest_gui = None
    
    def test_strategy(self, strategy_name):
        """測試特定策略的解讀功能"""
        if not self.backtest_gui:
            print(f"❌ 無法測試 {strategy_name}：回測GUI未載入")
            return
        
        try:
            print(f"\n🧪 測試 {strategy_name} 解讀功能...")
            
            # 測試基本概念
            basic_html = self.backtest_gui.get_strategy_basic_concepts_html(strategy_name)
            print(f"✅ {strategy_name} 基本概念載入成功 ({len(basic_html)} 字符)")
            
            # 測試信號解讀
            signal_html = self.backtest_gui.get_strategy_signal_interpretation_html(strategy_name)
            print(f"✅ {strategy_name} 信號解讀載入成功 ({len(signal_html)} 字符)")
            
            # 測試實戰應用
            practical_html = self.backtest_gui.get_strategy_practical_application_html(strategy_name)
            print(f"✅ {strategy_name} 實戰應用載入成功 ({len(practical_html)} 字符)")
            
            # 顯示解讀指南
            self.backtest_gui.strategy_combo = type('MockCombo', (), {
                'currentText': lambda: strategy_name
            })()
            self.backtest_gui.show_chart_interpretation_guide()
            
            print(f"🎉 {strategy_name} 解讀功能測試完成！")
            
        except Exception as e:
            print(f"❌ {strategy_name} 測試失敗: {e}")
    
    def launch_comprehensive_system(self):
        """啟動完整的解讀系統"""
        try:
            from technical_strategy_interpretation_gui import TechnicalStrategyInterpretationGUI
            self.interpretation_gui = TechnicalStrategyInterpretationGUI()
            self.interpretation_gui.show()
            print("🚀 完整解讀系統啟動成功")
        except Exception as e:
            print(f"❌ 完整解讀系統啟動失敗: {e}")
    
    def launch_ai_system(self):
        """啟動AI技術指標回測系統"""
        try:
            import ai_technical_backtest
            # 這裡可以啟動AI回測系統
            print("🤖 AI技術指標回測系統準備啟動...")
            print("💡 請手動運行: python ai_technical_backtest.py")
        except ImportError:
            print("❌ AI技術指標回測模組未找到")

def run_comprehensive_test():
    """運行綜合測試"""
    print("🧪 開始綜合測試所有策略解讀功能...")
    
    strategies = ['RSI策略', 'MACD策略', '布林通道策略', '移動平均交叉策略']
    
    try:
        from ashui_backtest_gui import AshiuBacktestGUI
        gui = AshiuBacktestGUI()
        
        for strategy in strategies:
            print(f"\n📊 測試 {strategy}...")
            
            # 測試各個方法
            basic = gui.get_strategy_basic_concepts_html(strategy)
            signal = gui.get_strategy_signal_interpretation_html(strategy)
            practical = gui.get_strategy_practical_application_html(strategy)
            
            print(f"   ✅ 基本概念: {len(basic)} 字符")
            print(f"   ✅ 信號解讀: {len(signal)} 字符") 
            print(f"   ✅ 實戰應用: {len(practical)} 字符")
        
        print("\n🎉 所有策略解讀功能測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 綜合測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 技術指標策略解讀功能測試程式")
    print("=" * 50)
    
    # 先運行綜合測試
    if run_comprehensive_test():
        print("\n🚀 啟動GUI測試界面...")
        
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # 創建測試窗口
        test_window = StrategyTestWindow()
        test_window.show()
        
        print("\n📋 測試說明:")
        print("1. 點擊各策略按鈕測試解讀功能")
        print("2. 點擊 '啟動完整解讀系統' 查看整合界面")
        print("3. 點擊 'AI技術指標回測系統' 啟動回測功能")
        
        print("\n✨ 新增功能特色:")
        print("• 🎯 四大策略完整解讀：RSI、MACD、布林通道、移動平均交叉")
        print("• 📚 三層次內容：基本概念、信號解讀、實戰應用")
        print("• 💡 針對性指導：每種策略的特定操作建議")
        print("• 🔄 策略切換：動態切換不同策略的解讀內容")
        
        sys.exit(app.exec())
    else:
        print("❌ 綜合測試失敗，請檢查相關模組")

if __name__ == '__main__':
    main()
