#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試單一股票下載功能（包含股票名稱）
"""

import sys
import os
from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader

def test_single_stock():
    """測試單一股票下載"""
    print("🧪 測試單一股票下載功能...")
    
    # 初始化下載器
    downloader = GoodinfoMonthlyRevenueDownloader()
    
    # 測試股票代號
    test_stock = "2330"  # 台積電
    
    print(f"📊 測試股票: {test_stock}")
    
    try:
        # 測試不同的日期區間
        test_cases = [
            # (start_date, end_date, description)
            ("2023-01", "2025-07", "指定開始和結束日期"),
            ("2024-01", None, "只指定開始日期"),
            (None, "2025-07", "只指定結束日期"),
            (None, None, "不指定日期區間")
        ]

        for start_date, end_date, description in test_cases:
            print(f"\n📅 測試案例: {description}")
            print(f"   開始日期: {start_date or '未指定'}")
            print(f"   結束日期: {end_date or '未指定'}")

            # 下載股票資料
            result = downloader.download_stock_revenue(test_stock, start_date, end_date)

            if result:
                print(f"✅ 下載成功: {result}")

                # 檢查文件名格式
                filename = os.path.basename(result)
                print(f"📄 檔案名稱: {filename}")

                # 檢查是否包含股票名稱
                if "台積電" in filename or "TSMC" in filename:
                    print("✅ 文件名包含股票名稱")
                else:
                    print("⚠️ 文件名未包含股票名稱")

                # 檢查是否包含日期區間
                if start_date and end_date and f"{start_date}_to_{end_date}" in filename:
                    print("✅ 文件名包含完整日期區間")
                elif start_date and f"from_{start_date}" in filename:
                    print("✅ 文件名包含開始日期")
                elif end_date and f"to_{end_date}" in filename:
                    print("✅ 文件名包含結束日期")
                elif not start_date and not end_date:
                    print("✅ 無日期區間（符合預期）")
                else:
                    print("⚠️ 日期區間格式可能有問題")

            else:
                print("❌ 下載失敗")

            # 只測試第一個案例，避免過多請求
            break
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_stock()
