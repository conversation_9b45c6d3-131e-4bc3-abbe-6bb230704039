#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試表格字體修復
"""

import sys
from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, 
                            QTableWidget, QTableWidgetItem, QPushButton)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor

class TestTableDialog(QDialog):
    """測試表格對話框"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 測試表格字體修復")
        self.setGeometry(100, 100, 800, 400)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 創建測試表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(['日期', '來源', '標題 (測試雙擊)'])
        table.setRowCount(3)
        
        # 禁用編輯功能，防止雙擊時字形變形
        table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 設定統一字體
        table_font = QFont("Microsoft JhengHei", 12)
        table.setFont(table_font)
        
        # 設置樣式
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                gridline-color: #e0e0e0;
                color: #1a1a1a;
                border: 2px solid #1976d2;
                border-radius: 8px;
                font-family: "Microsoft JhengHei";
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #e0e0e0;
                border-right: 1px solid #e0e0e0;
                font-family: "Microsoft JhengHei";
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #1976d2;
                color: white;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #e3f2fd;
                color: #1a1a1a;
            }
        """)
        
        # 添加測試資料
        test_data = [
            ("2025-07-19", "奇摩股市", "台積電法說會釋出利多訊息，股價創新高"),
            ("2025-07-18", "CMoney", "2330台積電 - 台積電法說報告短評光芒？"),
            ("2025-07-17", "經濟日報", "台積電第二季財報超預期，展望樂觀")
        ]
        
        for row, (date, source, title) in enumerate(test_data):
            # 日期項目
            date_item = QTableWidgetItem(date)
            date_item.setFont(table_font)
            date_item.setFlags(date_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            table.setItem(row, 0, date_item)
            
            # 來源項目
            source_item = QTableWidgetItem(source)
            source_item.setFont(table_font)
            source_item.setFlags(source_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            table.setItem(row, 1, source_item)
            
            # 標題項目
            title_item = QTableWidgetItem(title)
            title_item.setFont(table_font)
            title_item.setFlags(title_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            title_item.setForeground(QColor(25, 118, 210))  # 藍色連結樣式
            table.setItem(row, 2, title_item)
        
        layout.addWidget(table)
        
        # 說明文字
        info_btn = QPushButton("✅ 測試說明：請嘗試雙擊標題，字體應該不會變形")
        info_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                padding: 10px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
        """)
        layout.addWidget(info_btn)
        
        # 關閉按鈕
        close_btn = QPushButton("❌ 關閉")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                padding: 10px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
        """)
        layout.addWidget(close_btn)

def main():
    app = QApplication(sys.argv)
    
    dialog = TestTableDialog()
    dialog.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
