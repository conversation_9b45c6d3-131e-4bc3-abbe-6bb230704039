#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試現有文件檢測
"""

import os
import glob
from datetime import datetime

def test_existing_file_detection():
    """測試現有文件檢測邏輯"""
    download_path = "C:/Users/<USER>/Downloads"
    
    print(f"📁 檢查目錄: {download_path}")
    
    # 獲取所有文件
    all_files = glob.glob(os.path.join(download_path, "*.*"))
    excel_files = [f for f in all_files if f.lower().endswith(('.xls', '.xlsx'))]
    
    print(f"📊 總文件數: {len(all_files)}")
    print(f"📊 Excel文件數: {len(excel_files)}")
    
    # 測試文件名檢測邏輯
    target_file = None
    for excel_file in excel_files:
        filename = os.path.basename(excel_file)
        filename_lower = filename.lower()
        
        print(f"\n📄 檢查文件: {filename}")
        
        # 檢測邏輯
        has_salemondetail = 'salemondetail' in filename_lower
        has_monthly = 'monthly' in filename_lower
        has_revenue = 'revenue' in filename_lower
        starts_with_salemondetail = filename_lower.startswith('salemondetail')
        has_salemondetail_bracket = 'salemondetail (' in filename_lower
        
        print(f"   SaleMonDetail: {has_salemondetail}")
        print(f"   Monthly: {has_monthly}")
        print(f"   Revenue: {has_revenue}")
        print(f"   StartsWith: {starts_with_salemondetail}")
        print(f"   Bracket: {has_salemondetail_bracket}")
        
        match_criteria = (has_salemondetail or has_monthly or has_revenue or 
                         starts_with_salemondetail or has_salemondetail_bracket)
        
        print(f"   匹配結果: {match_criteria}")
        
        if match_criteria:
            # 檢查文件時間
            file_ctime = os.path.getctime(excel_file)
            file_mtime = os.path.getmtime(excel_file)
            recent_time = max(file_ctime, file_mtime)
            
            ctime_str = datetime.fromtimestamp(file_ctime).strftime('%Y-%m-%d %H:%M:%S')
            mtime_str = datetime.fromtimestamp(file_mtime).strftime('%Y-%m-%d %H:%M:%S')
            recent_str = datetime.fromtimestamp(recent_time).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"   創建時間: {ctime_str}")
            print(f"   修改時間: {mtime_str}")
            print(f"   最近時間: {recent_str}")
            
            file_size = os.path.getsize(excel_file)
            print(f"   文件大小: {file_size} bytes")
            
            if filename.lower().startswith('salemondetail'):
                target_file = excel_file
                print(f"   ✅ 這是目標文件!")
    
    if target_file:
        print(f"\n🎯 找到目標文件: {os.path.basename(target_file)}")
        
        # 嘗試複製文件
        try:
            import shutil
            target_dir = "D:/Finlab/history/tables/monthly_revenue"
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            new_filename = f"2330_台積電_monthly_revenue_{timestamp}.xls"
            new_path = os.path.join(target_dir, new_filename)
            
            shutil.copy2(target_file, new_path)
            print(f"✅ 文件複製成功: {new_filename}")
            
            return new_path
            
        except Exception as e:
            print(f"❌ 文件複製失敗: {e}")
            return target_file
    else:
        print("❌ 未找到目標文件")
        return None

if __name__ == "__main__":
    result = test_existing_file_detection()
    if result:
        print(f"\n🎉 測試成功: {result}")
    else:
        print("\n❌ 測試失敗")
