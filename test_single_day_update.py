#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試單天的 price 更新（包含股票資訊）
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_single_day_update():
    """測試單天更新"""
    
    print("=" * 60)
    print("🧪 測試單天 price 更新（包含股票資訊）")
    print("=" * 60)
    
    try:
        # 導入必要模組
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        print("✅ 模組導入成功")
        
        # 測試爬取一天的資料
        test_date = datetime(2022, 8, 31)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("🔄 開始爬取資料...")
        df = crawl_price(test_date)
        
        if df is not None and not df.empty:
            print(f"✅ 爬取成功！資料筆數: {len(df)}")
            
            # 檢查資料結構
            df_reset = df.reset_index()
            print(f"📊 資料結構:")
            print(f"   欄位: {list(df_reset.columns)}")
            
            # 檢查是否有股票資訊
            if 'stock_name' in df_reset.columns:
                print(f"✅ 包含股票名稱")
                non_empty_names = df_reset[df_reset['stock_name'] != ''].shape[0]
                print(f"   有名稱的股票: {non_empty_names}/{len(df_reset)}")
            
            if 'listing_status' in df_reset.columns:
                print(f"✅ 包含上市狀態")
                status_counts = df_reset['listing_status'].value_counts()
                print(f"   狀態分布: {dict(status_counts)}")
            
            if 'industry' in df_reset.columns:
                print(f"✅ 包含產業別")
                industry_counts = df_reset['industry'].value_counts().head(5)
                print(f"   前5大產業: {dict(industry_counts)}")
            
            # 顯示範例資料
            print(f"\n📋 範例資料:")
            sample_data = df_reset[['stock_id', 'stock_name', 'listing_status', 'industry', '收盤價']].head(10)
            for _, row in sample_data.iterrows():
                print(f"   {row['stock_id']} {row['stock_name']} | {row['listing_status']} | {row['industry']} | 收盤:{row['收盤價']}")
            
            # 測試保存到資料庫
            print(f"\n💾 測試保存到資料庫...")
            db_file = 'D:/Finlab/history/tables/newprice.db'
            success = save_price_to_newprice_database(df, db_file)
            
            if success:
                print(f"✅ 保存成功")
                
                # 驗證保存結果
                import sqlite3
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT stock_id, stock_name, listing_status, industry, [Close] 
                    FROM stock_daily_data 
                    WHERE date = "2022-08-31" 
                    ORDER BY stock_id 
                    LIMIT 10
                ''')
                saved_data = cursor.fetchall()
                
                print(f"📊 保存驗證:")
                for stock_id, stock_name, listing_status, industry, close in saved_data:
                    print(f"   {stock_id} {stock_name} | {listing_status} | {industry} | 收盤:{close}")
                
                conn.close()
            else:
                print(f"❌ 保存失敗")
                
        else:
            print("❌ 爬取失敗或無資料")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_day_update()
