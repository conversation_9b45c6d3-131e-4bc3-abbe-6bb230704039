#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
統一的財務資料爬蟲 - 將綜合損益表和資產負債表合併到單一檔案
解決日期格式問題，統一使用西元年
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TWSEOpenAPIClient:
    """台灣證交所 OpenAPI 客戶端"""
    
    BASE_URL = "https://openapi.twse.com.tw/v1"
    USER_AGENT = "finlab-crawler/1.0"
    
    @classmethod
    def get_data(cls, endpoint: str, timeout: float = 30.0) -> List[Dict[str, Any]]:
        """從 TWSE API 端點獲取資料"""
        url = f"{cls.BASE_URL}{endpoint}"
        logger.info(f"🔍 獲取 TWSE 資料: {url}")
        
        try:
            resp = requests.get(
                url,
                headers={
                    "User-Agent": cls.USER_AGENT,
                    "Accept": "application/json"
                },
                verify=False,
                timeout=timeout
            )
            resp.raise_for_status()
            resp.encoding = 'utf-8'
            data = resp.json()
            
            return data if isinstance(data, list) else [data] if data else []
            
        except Exception as e:
            logger.error(f"❌ 獲取資料失敗 {url}: {e}")
            raise

def convert_roc_date_to_standard(roc_date_str):
    """
    轉換民國年日期為標準日期格式
    
    Args:
        roc_date_str: 民國年日期字串，如 '1140726'
    
    Returns:
        str: 標準日期格式，如 '2025-07-26'
    """
    try:
        if not roc_date_str or len(str(roc_date_str)) != 7:
            return None
        
        roc_date_str = str(roc_date_str)
        roc_year = int(roc_date_str[:3])  # 前3位是民國年
        month = roc_date_str[3:5]         # 中間2位是月
        day = roc_date_str[5:7]           # 後2位是日
        
        # 轉換為西元年
        western_year = roc_year + 1911
        
        return f"{western_year}-{month}-{day}"
        
    except Exception as e:
        print(f"⚠️ 日期轉換失敗: {roc_date_str} -> {e}")
        return None

def standardize_financial_data(df, data_type):
    """標準化財務資料格式"""
    
    # 標準化欄位名稱
    if '公司代號' in df.columns:
        df = df.rename(columns={'公司代號': 'stock_id'})
    if '公司名稱' in df.columns:
        df = df.rename(columns={'公司名稱': 'stock_name'})
    
    # 轉換民國年為西元年
    if '年度' in df.columns:
        df['year'] = df['年度'].astype(int) + 1911
        df = df.drop(columns=['年度'])  # 移除原始民國年欄位
    
    if '季別' in df.columns:
        df['quarter'] = df['季別'].astype(int)
        df = df.drop(columns=['季別'])  # 移除原始季別欄位
    
    # 轉換出表日期為標準日期格式
    if '出表日期' in df.columns:
        df['report_date'] = df['出表日期'].apply(convert_roc_date_to_standard)
        df = df.drop(columns=['出表日期'])  # 移除原始出表日期欄位
    
    # 添加統一的日期欄位 (類似 price.db 的格式)
    if 'year' in df.columns and 'quarter' in df.columns:
        # 根據年度和季別生成統一的日期
        df['date'] = df.apply(lambda row: f"{int(row['year'])}-{int(row['quarter']*3):02d}-01", axis=1)
    else:
        df['date'] = datetime.now().strftime('%Y-%m-%d')
    
    # 添加資料類型
    df['data_type'] = data_type
    
    # 確保 stock_id 為字串
    if 'stock_id' in df.columns:
        df['stock_id'] = df['stock_id'].astype(str)
    
    return df

def crawl_unified_financial_data():
    """爬取統一的財務資料"""
    
    print("=" * 80)
    print("📊 爬取統一財務資料 (綜合損益表 + 資產負債表)")
    print("=" * 80)
    
    client = TWSEOpenAPIClient()
    all_financial_data = []
    
    # 1. 爬取綜合損益表
    print("🔍 獲取綜合損益表資料...")
    try:
        income_data = client.get_data("/opendata/t187ap06_L_ci")
        if income_data:
            income_df = pd.DataFrame(income_data)
            income_df = standardize_financial_data(income_df, 'income_statement')
            
            # 添加表格類型前綴到財務項目欄位
            financial_columns = [col for col in income_df.columns 
                               if col not in ['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date', 'data_type']]
            
            rename_dict = {col: f"income_{col}" for col in financial_columns}
            income_df = income_df.rename(columns=rename_dict)
            
            all_financial_data.append(income_df)
            print(f"✅ 綜合損益表: {len(income_df)} 筆資料")
        else:
            print("⚠️ 未獲取到綜合損益表資料")
    except Exception as e:
        print(f"❌ 爬取綜合損益表失敗: {e}")
    
    # 等待避免 API 限制
    time.sleep(2)
    
    # 2. 爬取資產負債表
    print("🔍 獲取資產負債表資料...")
    try:
        balance_data = client.get_data("/opendata/t187ap07_L_ci")
        if balance_data:
            balance_df = pd.DataFrame(balance_data)
            balance_df = standardize_financial_data(balance_df, 'balance_sheet')
            
            # 添加表格類型前綴到財務項目欄位
            financial_columns = [col for col in balance_df.columns 
                               if col not in ['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date', 'data_type']]
            
            rename_dict = {col: f"balance_{col}" for col in financial_columns}
            balance_df = balance_df.rename(columns=rename_dict)
            
            all_financial_data.append(balance_df)
            print(f"✅ 資產負債表: {len(balance_df)} 筆資料")
        else:
            print("⚠️ 未獲取到資產負債表資料")
    except Exception as e:
        print(f"❌ 爬取資產負債表失敗: {e}")
    
    # 3. 合併資料
    if len(all_financial_data) > 0:
        print("🔄 合併財務資料...")
        
        # 使用 stock_id 和 date 作為合併鍵
        merged_df = all_financial_data[0]
        
        for i in range(1, len(all_financial_data)):
            merged_df = pd.merge(
                merged_df, 
                all_financial_data[i], 
                on=['stock_id', 'stock_name', 'year', 'quarter', 'report_date', 'date'], 
                how='outer',
                suffixes=('', '_dup')
            )
            
            # 移除重複的 data_type 欄位
            if 'data_type_dup' in merged_df.columns:
                merged_df = merged_df.drop(columns=['data_type_dup'])
        
        # 設置統一的 data_type
        merged_df['data_type'] = 'financial_statements'
        
        print(f"✅ 合併完成: {len(merged_df)} 筆資料，{len(merged_df.columns)} 個欄位")
        return merged_df
    else:
        print("❌ 無法獲取任何財務資料")
        return pd.DataFrame()

def save_unified_financial_data(df):
    """儲存統一的財務資料到單一檔案"""
    
    print(f"\n💾 儲存統一財務資料...")
    
    try:
        # 確保目錄存在
        target_dir = r'D:\Finlab\history\tables'
        os.makedirs(target_dir, exist_ok=True)
        
        if not df.empty:
            # 儲存到統一的財務資料檔案
            unified_db_path = os.path.join(target_dir, 'financial_data.db')
            conn = sqlite3.connect(unified_db_path)
            df.to_sql('financial_data', conn, if_exists='replace', index=False)
            conn.close()
            
            print(f"✅ 統一財務資料已儲存: {unified_db_path}")
            print(f"📊 資料筆數: {len(df):,}")
            print(f"📋 總欄位數: {len(df.columns)}")
            
            # 顯示主要欄位
            key_columns = ['stock_id', 'stock_name', 'year', 'quarter', 'date', 'report_date']
            income_columns = [col for col in df.columns if col.startswith('income_')][:5]
            balance_columns = [col for col in df.columns if col.startswith('balance_')][:5]
            
            print(f"📋 主要欄位:")
            print(f"   基本資訊: {key_columns}")
            print(f"   損益項目: {income_columns}...")
            print(f"   資產負債: {balance_columns}...")
            
            return True
        else:
            print("⚠️ 無資料可儲存")
            return False
        
    except Exception as e:
        print(f"❌ 儲存統一財務資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🚀 統一財務資料爬蟲")
    print("解決日期格式問題，合併綜合損益表和資產負債表")
    
    # 爬取統一財務資料
    financial_df = crawl_unified_financial_data()
    
    # 儲存資料
    save_success = save_unified_financial_data(financial_df)
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 統一財務資料爬取總結")
    print(f"=" * 80)
    
    if save_success:
        print(f"🎉 統一財務資料爬取成功！")
        print(f"\n💡 改進項目:")
        print(f"   ✅ 統一日期格式 (西元年)")
        print(f"   ✅ 合併損益表和資產負債表")
        print(f"   ✅ 單一檔案儲存")
        print(f"   ✅ 標準化欄位命名")
        
        print(f"\n📝 使用方法:")
        print(f"   資料庫: D:\\Finlab\\history\\tables\\financial_data.db")
        print(f"   表格: financial_data")
        print(f"   查詢: SELECT * FROM financial_data WHERE stock_id = '2330'")
        
        return True
    else:
        print(f"❌ 統一財務資料爬取失敗")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
