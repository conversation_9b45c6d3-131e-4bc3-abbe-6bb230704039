#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正ROE數據庫結構問題
重建數據庫表格以確保所有欄位都存在
"""

import os
import sqlite3
import pandas as pd
from datetime import datetime

def fix_roe_database():
    """修正ROE數據庫結構"""
    print("🔧 修正ROE數據庫結構")
    print("=" * 50)
    
    db_path = "D:/Finlab/history/tables/roe_data.db"
    
    try:
        # 確保目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 連接數據庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查現有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roe_data'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("📊 檢查現有表格結構...")
            cursor.execute("PRAGMA table_info(roe_data)")
            existing_columns = [column[1] for column in cursor.fetchall()]
            print(f"  現有欄位: {existing_columns}")
            
            # 備份現有資料
            cursor.execute("SELECT COUNT(*) FROM roe_data")
            existing_count = cursor.fetchone()[0]
            print(f"  現有資料筆數: {existing_count}")
            
            if existing_count > 0:
                print("💾 備份現有資料...")
                backup_df = pd.read_sql_query("SELECT * FROM roe_data", conn)
                backup_file = f"roe_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                backup_path = os.path.join(os.path.dirname(db_path), backup_file)
                backup_df.to_csv(backup_path, index=False, encoding='utf-8-sig')
                print(f"✅ 備份完成: {backup_file}")
            
            # 刪除舊表格
            print("🗑️ 刪除舊表格...")
            cursor.execute("DROP TABLE roe_data")
        
        # 創建新表格
        print("🏗️ 創建新表格...")
        cursor.execute("""
            CREATE TABLE roe_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                roe_value REAL,
                roe_change REAL,
                eps_value REAL,
                report_year INTEGER,
                rank_position INTEGER,
                crawl_date TEXT,
                data_source TEXT DEFAULT 'goodinfo_csv',
                UNIQUE(stock_code, report_year)
            )
        """)
        
        # 驗證新表格結構
        cursor.execute("PRAGMA table_info(roe_data)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"✅ 新表格欄位: {new_columns}")
        
        conn.commit()
        conn.close()
        
        print("🎉 數據庫結構修正完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修正數據庫失敗: {e}")
        return False

def test_import_latest_csv():
    """測試匯入最新的CSV資料"""
    print("\n📥 測試匯入最新CSV資料")
    print("=" * 50)
    
    try:
        from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader
        
        # 找到最新的CSV文件
        csv_dir = "D:/Finlab/history/tables"
        csv_files = []
        
        if os.path.exists(csv_dir):
            for file in os.listdir(csv_dir):
                if 'roe_data' in file.lower() and file.endswith('.csv') and 'backup' not in file:
                    file_path = os.path.join(csv_dir, file)
                    csv_files.append((file_path, os.path.getctime(file_path)))
        
        if not csv_files:
            print("❌ 沒有找到ROE CSV文件")
            return False
        
        # 選擇最新的文件
        latest_csv = max(csv_files, key=lambda x: x[1])[0]
        print(f"📁 使用CSV文件: {os.path.basename(latest_csv)}")
        
        # 創建下載器並匯入
        downloader = GoodinfoROECSVDownloader()
        success = downloader.import_csv_to_database(latest_csv)
        
        if success:
            print("✅ CSV匯入成功！")
            
            # 驗證匯入結果
            db_path = downloader.db_path
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM roe_data")
            count = cursor.fetchone()[0]
            print(f"📊 匯入資料筆數: {count}")
            
            # 顯示前5筆資料
            cursor.execute("""
                SELECT stock_code, stock_name, roe_value, roe_change, eps_value, report_year 
                FROM roe_data 
                ORDER BY roe_value DESC 
                LIMIT 5
            """)
            rows = cursor.fetchall()
            
            if rows:
                print(f"\n📋 ROE最高前5名:")
                for i, row in enumerate(rows, 1):
                    print(f"  {i}. {row[0]} {row[1]} - ROE: {row[2]}% (變化: {row[3]}) EPS: {row[4]} 年度: {row[5]}")
            
            conn.close()
            return True
        else:
            print("❌ CSV匯入失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試匯入失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")
        return False

def main():
    """主函數"""
    print("🚀 ROE數據庫修正工具")
    print("=" * 60)
    
    # 步驟1: 修正數據庫結構
    fix_success = fix_roe_database()
    
    if not fix_success:
        print("❌ 數據庫修正失敗，停止執行")
        return False
    
    # 步驟2: 測試匯入最新資料
    import_success = test_import_latest_csv()
    
    print("\n" + "=" * 60)
    print("📊 修正結果總結:")
    print(f"  • 數據庫結構修正: {'✅ 成功' if fix_success else '❌ 失敗'}")
    print(f"  • CSV資料匯入: {'✅ 成功' if import_success else '❌ 失敗'}")
    
    if fix_success and import_success:
        print("\n🎉 ROE數據庫修正完成！")
        print("\n💡 現在您可以:")
        print("  1. 正常使用ROE下載器")
        print("  2. 成功匯入資料到數據庫")
        print("  3. 在投資分析中使用ROE指標")
        print("  4. 重新測試ROE下載功能")
        
        print("\n🔄 建議操作:")
        print("  • 重新啟動ROE下載器")
        print("  • 測試下載和匯入功能")
        print("  • 驗證資料完整性")
    else:
        print("\n⚠️ 修正過程中遇到問題，請檢查錯誤信息")
    
    return fix_success and import_success

if __name__ == "__main__":
    main()
