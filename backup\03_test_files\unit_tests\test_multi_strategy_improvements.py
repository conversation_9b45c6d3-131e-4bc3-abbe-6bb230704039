#!/usr/bin/env python3
"""
測試多策略整合分析系統的改進功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from multi_strategy_gui import MultiStrategyGUI

def test_new_features():
    """測試新功能"""
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = MultiStrategyGUI()
    window.show()
    
    def run_tests():
        """運行測試"""
        print("\n🧪 開始測試新功能...")
        
        # 測試1: 手動輸入股票代碼
        print("📝 測試1: 手動輸入股票代碼")
        window.stock_input.setText("2330")
        window.on_stock_input_changed()
        print("✅ 手動輸入2330測試完成")
        
        # 測試2: 測試導航按鈕狀態
        print("📝 測試2: 導航按鈕狀態")
        window.update_navigation_buttons()
        print(f"✅ 前一檔按鈕啟用: {window.prev_btn.isEnabled()}")
        print(f"✅ 後一檔按鈕啟用: {window.next_btn.isEnabled()}")
        
        # 測試3: 測試股票搜尋功能
        print("📝 測試3: 股票搜尋功能")
        result = window.search_and_select_stock("台積電")
        print(f"✅ 搜尋'台積電'結果: {result}")
        
        # 測試4: 測試下拉選單變化
        print("📝 測試4: 下拉選單變化")
        if window.stock_combo.count() > 1:
            window.stock_combo.setCurrentIndex(1)
            print("✅ 下拉選單選擇測試完成")
        
        print("\n🎉 所有測試完成！")
        print("📋 新功能摘要:")
        print("   • ✅ 手動輸入股票代碼/名稱")
        print("   • ✅ 自動開始分析")
        print("   • ✅ 前一檔/後一檔導航按鈕")
        print("   • ✅ 按鈕名稱改為'開始分析'")
        print("   • ✅ 智能股票搜尋匹配")
        
    # 延遲執行測試，等待界面完全載入
    QTimer.singleShot(2000, run_tests)
    
    sys.exit(app.exec())

if __name__ == '__main__':
    test_new_features()
