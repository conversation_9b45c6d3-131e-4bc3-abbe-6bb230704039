# 📊 自建台股資料擷取系統完整說明

## 🎯 系統概述

我已經為您創建了一個完整的**自建台股資料擷取系統**，基於台灣證券交易所（TWSE）官方API，實現了從即時資料擷取到5分鐘K線彙總的完整解決方案。

## 🚀 核心功能

### 1️⃣ **TWSE即時資料擷取器** (`twse_realtime_crawler.py`)

#### 📊 **資料來源**
- **台灣證券交易所官方API**: `https://mis.twse.com.tw/stock/api/getStockInfo.jsp`
- **即時更新頻率**: 每5秒擷取一次（可調整）
- **資料內容**: 成交價、成交量、最佳5檔買賣價格、開高低價等

#### 🔄 **擷取流程**
1. **發送HTTP請求**: 使用 `requests.get()` 向TWSE API發送請求
2. **解析JSON資料**: 自動解析回傳的JSON格式資料
3. **資料清理**: 處理空值、格式化數字、轉換時間戳
4. **雙重存儲**: 同時保存到CSV文件和SQLite資料庫

#### 📈 **5分鐘K線彙總**
- **自動彙總**: 每5分鐘自動將tick資料彙總為OHLCV格式
- **OHLC計算**: 開盤價（第一筆）、最高價（最大值）、最低價（最小值）、收盤價（最後一筆）
- **成交量統計**: 計算5分鐘內的總成交量
- **Tick計數**: 記錄彙總期間的資料筆數

### 2️⃣ **多資料源整合系統** (`integrated_data_system.py`)

#### 🔗 **整合的資料源**
1. **TWSE即時擷取器**: 官方即時資料
2. **自建資料擷取器**: 多網站爬蟲
3. **Twelve Data API**: 國際金融資料
4. **增強版資料獲取器**: yfinance等多源整合
5. **盤中資料獲取器**: 專門的盤中資料

#### 🎯 **智能選擇機制**
- **自動容錯**: 一個資料源失敗自動切換到下一個
- **優先級排序**: 根據資料品質和穩定性排序
- **即時切換**: 動態選擇最佳可用資料源

## 📁 資料存儲格式

### 📄 **CSV文件格式**
```csv
symbol,timestamp,price,volume,bid_price,ask_price,bid_volume,ask_volume,high,low,open,change,change_percent
2330,2025-07-08 09:00:05,1070.00,1000,1069.00,1071.00,500,300,1075.00,1065.00,1070.00,5.00,0.47
```

### 🗄️ **資料庫結構**

#### **即時資料表** (`realtime_ticks`)
```sql
CREATE TABLE realtime_ticks (
    id INTEGER PRIMARY KEY,
    symbol TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    price REAL,
    volume INTEGER,
    bid_price REAL,
    ask_price REAL,
    bid_volume INTEGER,
    ask_volume INTEGER,
    high REAL,
    low REAL,
    open REAL
);
```

#### **5分鐘K線表** (`kline_5min`)
```sql
CREATE TABLE kline_5min (
    id INTEGER PRIMARY KEY,
    symbol TEXT NOT NULL,
    datetime TIMESTAMP NOT NULL,
    open REAL,
    high REAL,
    low REAL,
    close REAL,
    volume INTEGER,
    tick_count INTEGER,
    UNIQUE(symbol, datetime)
);
```

## 🖥️ 使用方法

### 📊 **基本使用**

#### 1. **啟動即時監控**
```python
from twse_realtime_crawler import TWSERealtimeCrawler

# 創建擷取器
crawler = TWSERealtimeCrawler()

# 監控股票列表
stocks = ['2330', '2317', '2454', '3008', '2412']

# 開始即時擷取（每5秒一次）
crawler.start_realtime_crawling(stocks, interval=5)

# 運行一段時間後停止
time.sleep(300)  # 運行5分鐘
crawler.stop_realtime_crawling()
```

#### 2. **獲取歷史5分鐘數據**
```python
# 獲取台積電的5分鐘K線數據
df = crawler.get_5min_kline_data('2330')

print(f"獲取到 {len(df)} 筆5分鐘K線數據")
print(df.head())
```

#### 3. **獲取即時報價**
```python
# 獲取多支股票的即時報價
realtime_data = crawler.get_stock_realtime_data(['2330', '2317'])

for symbol, data in realtime_data.items():
    print(f"{symbol}: {data['price']:.2f} ({data['change']:+.2f})")
```

### 🔗 **整合系統使用**

#### 1. **自動選擇最佳資料源**
```python
from integrated_data_system import integrated_data_system

# 自動選擇最佳資料源獲取日K線
df_daily = integrated_data_system.get_daily_data('2330', days=30, source='auto')

# 自動選擇最佳資料源獲取5分鐘數據
df_5min = integrated_data_system.get_intraday_data('2330', interval='5m', source='auto')

# 自動選擇最佳資料源獲取即時報價
quote = integrated_data_system.get_realtime_quote('2330', source='auto')
```

#### 2. **指定特定資料源**
```python
# 使用TWSE官方資料
df_twse = integrated_data_system.get_intraday_data('2330', source='twse')

# 使用自建爬蟲
df_custom = integrated_data_system.get_daily_data('2330', source='custom')

# 使用Twelve Data API
df_twelve = integrated_data_system.get_daily_data('AAPL', source='twelve_data')
```

#### 3. **啟動整合監控**
```python
# 啟動多資料源監控
stocks = ['2330', '2317', '2454']

def data_callback(quote_data):
    print(f"收到即時資料: {quote_data}")

integrated_data_system.start_realtime_monitoring(
    stocks, 
    callback=data_callback,
    sources=['twse', 'custom']
)
```

## ⚙️ 配置選項

### 🔧 **擷取參數**
```python
# 自定義擷取間隔
crawler.start_realtime_crawling(stocks, interval=10)  # 每10秒擷取一次

# 自定義資料存儲路徑
crawler = TWSERealtimeCrawler(
    data_dir="my_stock_data",
    db_path="my_database.db"
)
```

### 📊 **彙總設置**
- **彙總頻率**: 預設每5分鐘，可修改為其他時間間隔
- **資料保留**: 自動保留所有歷史資料
- **索引優化**: 自動創建時間和股票代碼索引

## 🎯 實際應用場景

### 📈 **日內交易**
```python
# 監控關鍵股票的5分鐘走勢
stocks = ['2330', '2317', '2454']
crawler.start_realtime_crawling(stocks)

# 定期檢查5分鐘K線
while True:
    for stock in stocks:
        df = crawler.get_5min_kline_data(stock)
        if not df.empty:
            latest = df.iloc[-1]
            print(f"{stock}: {latest['close']:.2f}")
    
    time.sleep(60)  # 每分鐘檢查一次
```

### 🔍 **策略回測**
```python
# 獲取歷史5分鐘數據進行回測
start_date = datetime(2025, 7, 1)
end_date = datetime(2025, 7, 8)

df = crawler.get_5min_kline_data('2330', start_date, end_date)

# 計算技術指標
df['MA5'] = df['close'].rolling(5).mean()
df['MA20'] = df['close'].rolling(20).mean()

# 生成交易信號
df['signal'] = np.where(df['MA5'] > df['MA20'], 1, -1)
```

### 📊 **即時監控面板**
```python
def create_monitoring_dashboard():
    stocks = ['2330', '2317', '2454', '3008', '2412']
    
    while True:
        print("\n" + "="*50)
        print(f"即時監控面板 - {datetime.now().strftime('%H:%M:%S')}")
        print("="*50)
        
        realtime_data = crawler.get_stock_realtime_data(stocks)
        
        for symbol, data in realtime_data.items():
            change_color = "🔴" if data['change'] < 0 else "🟢"
            print(f"{change_color} {symbol}: {data['price']:.2f} "
                  f"({data['change']:+.2f}, {data['change_percent']:+.2f}%)")
        
        time.sleep(5)
```

## 🛠️ 系統維護

### 📋 **日常維護**
1. **資料庫備份**: 定期備份SQLite資料庫文件
2. **CSV歸檔**: 定期歸檔舊的CSV文件
3. **日誌監控**: 檢查系統日誌確保正常運行
4. **磁碟空間**: 監控資料存儲空間使用情況

### 🔧 **性能優化**
```python
# 資料庫優化
import sqlite3

conn = sqlite3.connect('twse_realtime.db')
conn.execute('VACUUM')  # 壓縮資料庫
conn.execute('ANALYZE')  # 更新統計信息
conn.close()
```

### 📊 **監控指標**
- **擷取成功率**: 監控API請求成功率
- **資料延遲**: 檢查資料更新延遲
- **存儲效率**: 監控資料庫大小和查詢性能
- **系統資源**: CPU和記憶體使用情況

## 🎉 系統優勢

### ✅ **技術優勢**
1. **官方資料源**: 基於TWSE官方API，資料準確可靠
2. **即時性**: 5秒更新頻率，接近即時
3. **完整性**: 包含價格、成交量、買賣檔等完整資訊
4. **穩定性**: 多重錯誤處理和自動重試機制
5. **擴展性**: 模組化設計，易於擴展和維護

### 📊 **資料優勢**
1. **高頻率**: 5分鐘K線滿足大部分分析需求
2. **多格式**: 同時提供CSV和資料庫格式
3. **歷史資料**: 自動累積歷史資料
4. **標準化**: 統一的資料格式便於分析

### 🔧 **使用優勢**
1. **簡單易用**: 提供便捷函數和清晰API
2. **自動化**: 無需手動干預的自動擷取
3. **整合性**: 與現有系統完美整合
4. **靈活性**: 支援多種使用場景和配置

## 🎯 下一步發展

### 🚀 **功能擴展**
1. **WebSocket連接**: 實現真正的即時推送
2. **更多交易所**: 支援櫃買中心、興櫃等
3. **技術指標**: 內建常用技術指標計算
4. **警報系統**: 價格突破等事件警報

### 📈 **性能提升**
1. **分散式部署**: 支援多機器分散式擷取
2. **快取優化**: 智能快取減少API請求
3. **壓縮存儲**: 資料壓縮節省存儲空間
4. **並行處理**: 多線程並行擷取提升效率

**🎊 現在您擁有了一個完整、穩定、高效的自建台股資料擷取系統！**
