#!/usr/bin/env python3
import os
import sys
import json
import logging
import traceback
import sqlite3
from datetime import datetime, timedelta
import time

import numpy as np
import pandas as pd
import pyqtgraph as pg
from PyQt6.QtCore import Qt, QDate, QPointF, QRectF, QTimer, QThread, pyqtSignal, QThreadPool, QRunnable
from PyQt6.QtGui import QColor, QBrush, QPainter, QPicture, QPen, QFont
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QTabWidget, QGroupBox, QScrollArea, QMessageBox, QDialog, QLineEdit, QFileDialog,
    QListWidget, QComboBox, QCalendarWidget, QTableWidget, QTableWidgetItem,
    QStatusBar, QGridLayout, QSpinBox, QListWidgetItem, QProgressBar, QSplitter, QCheckBox,
    QDialogButtonBox, QFormLayout, QGraphicsRectItem, QDateEdit, QProgressDialog,
    QDoubleSpinBox
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='app.log',
    filemode='a'
)

# ===================== CandlestickItem =====================
class CandlestickItem(pg.GraphicsObject):
    """
    根據提供的數據繪製K線圖
    資料格式：每一列 [timestamp, open, close, low, high]
    """
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = QPicture()
        self.generate_picture()
        
    def generate_picture(self):
        """生成K線圖的繪圖指令"""
        painter = QPainter(self.picture)
        painter.setPen(pg.mkPen('k'))  # 黑色邊框
        w = 0.6  # K線寬度
        
        for i in range(len(self.data)):
            # 獲取OHLC數據
            open_price = float(self.data['Open'].iloc[i])
            high_price = float(self.data['High'].iloc[i])
            low_price = float(self.data['Low'].iloc[i])
            close_price = float(self.data['Close'].iloc[i])
            
            # 台股的顏色習慣：紅色表示上漲，綠色表示下跌
            if close_price > open_price:
                # 上漲，畫紅色K線
                painter.setBrush(pg.mkBrush('r'))
                painter.drawRect(QRectF(i-w/2, open_price, w, close_price-open_price))
            else:
                # 下跌，畫綠色K線
                painter.setBrush(pg.mkBrush('g'))
                painter.drawRect(QRectF(i-w/2, close_price, w, open_price-close_price))
            
            # 畫上下影線
            painter.drawLine(QPointF(i, low_price), QPointF(i, high_price))
        
        painter.end()
        
    def paint(self, painter, option, widget):
        painter.drawPicture(0, 0, self.picture)
        
    def boundingRect(self):
        return QRectF(self.picture.boundingRect())

# ===================== 策略參數與策略類別 =====================
class StrategyParams:
    def __init__(self, name="Default", description="",
                 ma240_days=240, trend_days=30,
                 ma60_days=60, ma20_days=20,
                 volume_short=3, volume_long=18, cross_days=3,
                 rsi_long=13, rsi_long_threshold=50,
                 rsi_short=6, rsi_short_threshold=70):
        self.name = name
        self.description = description
        self.ma240_days = ma240_days
        self.trend_days = trend_days
        self.ma60_days = ma60_days
        self.ma20_days = ma20_days
        self.volume_short = volume_short
        self.volume_long = volume_long
        self.cross_days = cross_days
        self.rsi_long = rsi_long
        self.rsi_long_threshold = rsi_long_threshold
        self.rsi_short = rsi_short
        self.rsi_short_threshold = rsi_short_threshold

    @staticmethod
    def default():
        return StrategyParams()

    def save(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.__dict__, f, ensure_ascii=False, indent=4)

    @classmethod
    def load(cls, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if "parameters" in data:
                params = data["parameters"]
            else:
                params = data
            return cls(
                name=data.get("name", "Default"),
                description=data.get("description", ""),
                ma240_days=params.get("ma240_days", 240),
                trend_days=params.get("trend_days", 30),
                ma60_days=params.get("ma60_days", 60),
                ma20_days=params.get("ma20_days", 20),
                volume_short=params.get("volume_short", 3),
                volume_long=params.get("volume_long", 18),
                cross_days=params.get("cross_days", 3),
                rsi_long=params.get("rsi_long", 13),
                rsi_long_threshold=params.get("rsi_long_threshold", 50),
                rsi_short=params.get("rsi_short", 6),
                rsi_short_threshold=params.get("rsi_short_threshold", 70)
            )
        except Exception as e:
            logging.error(f"載入策略參數時出錯: {e}")
            logging.error(traceback.format_exc())
            return cls.default()

class Strategy:
    def __init__(self, params):
        self.params = params

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        df['Signal'] = 0
        for i in range(1, len(df)):
            if df['MA5'].iloc[i] > df['MA20'].iloc[i] and df['MA5'].iloc[i-1] <= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = 1
            elif df['MA5'].iloc[i] < df['MA20'].iloc[i] and df['MA5'].iloc[i-1] >= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = -1
        return df

# ===================== 策略設定對話框 =====================
class StrategyDialog(QDialog):
    def __init__(self, params: StrategyParams = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("策略設置")
        self.setModal(True)
        layout = QVBoxLayout(self)
        
        # 策略名稱與描述
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("策略名稱:"))
        self.name_edit = QLineEdit()
        if params:
            self.name_edit.setText(params.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("策略描述:"))
        self.desc_edit = QLineEdit()
        if params:
            self.desc_edit.setText(params.description)
        desc_layout.addWidget(self.desc_edit)
        layout.addLayout(desc_layout)
        
        # 參數設置群組（這裡僅示意，可根據需求增加設定項目）
        params_group = QGroupBox("參數設置")
        params_layout = QVBoxLayout(params_group)
        params_layout.addWidget(QLabel("240日線天數:"))
        self.ma240_days_input = QSpinBox()
        self.ma240_days_input.setValue(params.ma240_days if params else 240)
        params_layout.addWidget(self.ma240_days_input)
        params_layout.addWidget(QLabel("趨勢天數:"))
        self.trend_days_input = QSpinBox()
        self.trend_days_input.setValue(params.trend_days if params else 30)
        params_layout.addWidget(self.trend_days_input)
        params_layout.addWidget(QLabel("60日線天數:"))
        self.ma60_days_input = QSpinBox()
        self.ma60_days_input.setValue(params.ma60_days if params else 60)
        params_layout.addWidget(self.ma60_days_input)
        params_layout.addWidget(QLabel("20日線天數:"))
        self.ma20_days_input = QSpinBox()
        self.ma20_days_input.setValue(params.ma20_days if params else 20)
        params_layout.addWidget(self.ma20_days_input)
        params_layout.addWidget(QLabel("短期成交量天數:"))
        self.volume_short_input = QSpinBox()
        self.volume_short_input.setValue(params.volume_short if params else 3)
        params_layout.addWidget(self.volume_short_input)
        params_layout.addWidget(QLabel("長期成交量天數:"))
        self.volume_long_input = QSpinBox()
        self.volume_long_input.setValue(params.volume_long if params else 18)
        params_layout.addWidget(self.volume_long_input)
        params_layout.addWidget(QLabel("交叉天數:"))
        self.cross_days_input = QSpinBox()
        self.cross_days_input.setValue(params.cross_days if params else 3)
        params_layout.addWidget(self.cross_days_input)
        params_layout.addWidget(QLabel("長期RSI天數:"))
        self.rsi_long_input = QSpinBox()
        self.rsi_long_input.setValue(params.rsi_long if params else 13)
        params_layout.addWidget(self.rsi_long_input)
        params_layout.addWidget(QLabel("長期RSI閾值:"))
        self.rsi_long_threshold_input = QSpinBox()
        self.rsi_long_threshold_input.setValue(params.rsi_long_threshold if params else 50)
        params_layout.addWidget(self.rsi_long_threshold_input)
        params_layout.addWidget(QLabel("短期RSI天數:"))
        self.rsi_short_input = QSpinBox()
        self.rsi_short_input.setValue(params.rsi_short if params else 6)
        params_layout.addWidget(self.rsi_short_input)
        params_layout.addWidget(QLabel("短期RSI閾值:"))
        self.rsi_short_threshold_input = QSpinBox()
        self.rsi_short_threshold_input.setValue(params.rsi_short_threshold if params else 70)
        params_layout.addWidget(self.rsi_short_threshold_input)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存")
        save_button.clicked.connect(self.accept)
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def get_strategy_params(self) -> StrategyParams:
        return StrategyParams(
            name=self.name_edit.text(),
            description=self.desc_edit.text(),
            ma240_days=self.ma240_days_input.value(),
            trend_days=self.trend_days_input.value(),
            ma60_days=self.ma60_days_input.value(),
            ma20_days=self.ma20_days_input.value(),
            volume_short=self.volume_short_input.value(),
            volume_long=self.volume_long_input.value(),
            cross_days=self.cross_days_input.value(),
            rsi_long=self.rsi_long_input.value(),
            rsi_long_threshold=self.rsi_long_threshold_input.value(),
            rsi_short=self.rsi_short_input.value(),
            rsi_short_threshold=self.rsi_short_threshold_input.value()
        )

    def add_condition(self):
        """添加新條件"""
        dialog = ConditionDialog(self)
        if dialog.exec():
            condition = dialog.get_condition()
            self.conditions.append(condition)
            self.update_conditions_list()
    
    def edit_condition(self):
        """編輯選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        dialog = ConditionDialog(self, self.conditions[current_row])
        if dialog.exec():
            self.conditions[current_row] = dialog.get_condition()
            self.update_conditions_list()
    
    def remove_condition(self):
        """刪除選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        self.conditions.pop(current_row)
        self.update_conditions_list()
    
    def get_conditions(self):
        """獲取所有條件"""
        return self.conditions


class ConditionDialog(QDialog):
    """條件編輯對話框"""
    def __init__(self, parent=None, condition=None):
        super().__init__(parent)
        self.setWindowTitle("編輯條件")
        self.resize(500, 400)
        
        self.condition = condition or {}
        layout = QVBoxLayout(self)
        
        # 條件類型選擇
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("條件類型:"))
        self.condition_type = QComboBox()
        self.condition_type.addItem("均線位置", "ma_position")
        self.condition_type.addItem("均線趨勢", "ma_trend")
        self.condition_type.addItem("成交量增加", "volume_increase")
        self.condition_type.addItem("MACD信號", "macd")
        self.condition_type.addItem("RSI指標", "rsi")
        
        # 設置當前選中的條件類型
        current_type = condition.get("type", "") if condition else ""
        index = self.condition_type.findData(current_type)
        if index >= 0:
            self.condition_type.setCurrentIndex(index)
        
        self.condition_type.currentIndexChanged.connect(self.update_condition_form)
        type_layout.addWidget(self.condition_type)
        layout.addLayout(type_layout)
        
        # 條件參數區域
        self.condition_form = QWidget()
        self.condition_form_layout = QFormLayout(self.condition_form)
        layout.addWidget(self.condition_form)
        
        # 按鈕
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # 初始化表單
        self.update_condition_form()
    
    def update_condition_form(self):
        """根據選擇的條件類型更新表單"""
        # 清空現有表單內容
        while self.condition_form_layout.count():
            item = self.condition_form_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        condition_type = self.condition_type.currentData()
        
        # 均線位置條件
        if condition_type == "ma_position":
            # 均線選擇
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            # 位置選擇
            self.position_combo = QComboBox()
            self.position_combo.addItem("上方", "above")
            self.position_combo.addItem("下方", "below")
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.position_combo.findData(self.condition.get("position", "above"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            # 價格類型
            self.price_combo = QComboBox()
            self.price_combo.addItem("收盤價", "close")
            self.price_combo.addItem("開盤價", "open")
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.price_combo.findData(self.condition.get("price", "close"))
                if index >= 0:
                    self.price_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("價格:", self.price_combo)
        
        # 均線趨勢條件
        elif condition_type == "ma_trend":
            # 均線選擇
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            # 趨勢選擇
            self.trend_combo = QComboBox()
            self.trend_combo.addItem("向上", "up")
            self.trend_combo.addItem("向下", "down")
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.trend_combo.findData(self.condition.get("trend", "up"))
                if index >= 0:
                    self.trend_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("趨勢:", self.trend_combo)
            
            # 天數
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "ma_trend" else 5)
            self.condition_form_layout.addRow("天數:", self.days_spin)
        
        # 成交量增加條件
        elif condition_type == "volume_increase":
            # 天數
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "volume_increase" else 5)
            self.condition_form_layout.addRow("比較天數:", self.days_spin)
            
            # 百分比
            self.percent_spin = QDoubleSpinBox()
            self.percent_spin.setRange(0, 500)
            self.percent_spin.setSuffix("%")
            self.percent_spin.setValue(self.condition.get("percent", 20) if self.condition and self.condition.get("type") == "volume_increase" else 20)
            self.condition_form_layout.addRow("增加比例:", self.percent_spin)
        
        # MACD信號
        elif condition_type == "macd":
            # 信號類型
            self.signal_combo = QComboBox()
            self.signal_combo.addItem("金叉", "golden_cross")
            self.signal_combo.addItem("死叉", "death_cross")
            self.signal_combo.addItem("MACD在零線上", "above_zero")
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "macd":
                index = self.signal_combo.findData(self.condition.get("signal", "golden_cross"))
                if index >= 0:
                    self.signal_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("信號:", self.signal_combo)
        
        # RSI指標
        elif condition_type == "rsi":
            # 週期
            self.period_combo = QComboBox()
            self.period_combo.addItem("RSI(6)", 6)
            self.period_combo.addItem("RSI(14)", 14)
            self.period_combo.addItem("RSI(24)", 24)
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "rsi":
                index = self.period_combo.findData(self.condition.get("period", 14))
                if index >= 0:
                    self.period_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("週期:", self.period_combo)
            
            # 位置類型
            self.position_combo = QComboBox()
            self.position_combo.addItem("超賣區", "oversold")
            self.position_combo.addItem("超買區", "overbought")
            self.position_combo.addItem("中間區域", "middle")
            
            # 設置當前值
            if self.condition and self.condition.get("type") == "rsi":
                index = self.position_combo.findData(self.condition.get("position", "oversold"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            # 超賣值
            self.min_spin = QSpinBox()
            self.min_spin.setRange(0, 100)
            self.min_spin.setValue(self.condition.get("min", 30) if self.condition and self.condition.get("type") == "rsi" else 30)
            self.condition_form_layout.addRow("超賣值 (<):", self.min_spin)
            
            # 超買值
            self.max_spin = QSpinBox()
            self.max_spin.setRange(0, 100)
            self.max_spin.setValue(self.condition.get("max", 70) if self.condition and self.condition.get("type") == "rsi" else 70)
            self.condition_form_layout.addRow("超買值 (>):", self.max_spin)
    
    def get_condition(self):
        """獲取條件數據"""
        condition_type = self.condition_type.currentData()
        condition = {"type": condition_type}
        
        # 均線位置條件
        if condition_type == "ma_position":
            condition["ma"] = self.ma_combo.currentText()
            condition["position"] = self.position_combo.currentData()
            condition["price"] = self.price_combo.currentData()
        
        # 均線趨勢條件
        elif condition_type == "ma_trend":
            condition["ma"] = self.ma_combo.currentText()
            condition["trend"] = self.trend_combo.currentData()
            condition["days"] = self.days_spin.value()
        
        # 成交量增加條件
        elif condition_type == "volume_increase":
            condition["days"] = self.days_spin.value()
            condition["percent"] = self.percent_spin.value()
        
        # MACD信號
        elif condition_type == "macd":
            condition["signal"] = self.signal_combo.currentData()
        
        # RSI指標
        elif condition_type == "rsi":
            condition["period"] = self.period_combo.currentData()
            condition["position"] = self.position_combo.currentData()
            condition["min"] = self.min_spin.value()
            condition["max"] = self.max_spin.value()
        
        return condition

# ===================== 主 GUI 類別 =====================
class MultiDBWorker(QThread):
    initialized = pyqtSignal(dict)  # 發送連接字典 {'price': conn1, 'pe': conn2}
    error = pyqtSignal(str)

    def __init__(self, db_config):
        super().__init__()
        self.db_config = db_config  # {'price': path1, 'pe': path2}

    def run(self):
        connections = {}
        try:
            # 連接價格數據庫
            if 'price' in self.db_config:
                conn = sqlite3.connect(
                    self.db_config['price'],
                    timeout=15,
                    check_same_thread=False
                )
                conn.execute("PRAGMA journal_mode = WAL")
                connections['price'] = conn

            # 連接PE數據庫
            if 'pe' in self.db_config:
                pe_conn = sqlite3.connect(
                    self.db_config['pe'],
                    timeout=15,
                    check_same_thread=False
                )
                pe_conn.execute("PRAGMA journal_mode = WAL")
                connections['pe'] = pe_conn

            self.initialized.emit(connections)
        except Exception as e:
            self.error.emit(f"數據庫連接失敗: {str(e)}")
            logging.error(traceback.format_exc())

class ScreenerWorker(QThread):
    progress_updated = pyqtSignal(int, int, str)  # (current, total, stock_id)
    result_ready = pyqtSignal(pd.DataFrame)

    def __init__(self, stock_ids, strategy, db_connector):
        super().__init__()
        self.stock_ids = stock_ids
        self.strategy = strategy
        self.db_connector = db_connector
        self._is_running = True

    def run(self):
        results = []
        try:
            total = len(self.stock_ids)
            for idx, stock_id in enumerate(self.stock_ids, 1):
                if not self._is_running:
                    break
                
                self.progress_updated.emit(idx, total, stock_id)
                
                try:
                    data = self.db_connector.get_combined_stock_data(stock_id)
                    if not data.empty:
                        # 執行策略分析...
                        if self.apply_strategy(data):  # 替換為實際策略條件
                            results.append({
                                '股票代碼': stock_id,
                                '收盤價': data['Close'].iloc[-1],
                                '條件符合': '是'
                            })
                except Exception as e:
                    logging.error(f"處理 {stock_id} 時出錯: {str(e)}")
                
            self.result_ready.emit(pd.DataFrame(results))
        
        except Exception as e:
            logging.error(f"選股過程出錯: {str(e)}")
            self.result_ready.emit(pd.DataFrame())

    def apply_strategy(self, data):
        """示例策略條件"""
        # 這裡替換為實際策略邏輯
        return len(data) > 100  # 示例條件

    def stop(self):
        self._is_running = False

class WorkerRunnable(QRunnable):
    """通用異步任務執行器"""
    def __init__(self, fn, callback, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.callback = callback
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            result = self.fn(*self.args, **self.kwargs)
            QTimer.singleShot(0, lambda: self.callback(result))
        except Exception as e:
            logging.error(f"執行任務出錯: {str(e)}")
            QTimer.singleShot(0, lambda: self.callback(None))

class StockScreenerGUI(QMainWindow):
    CONFIG_FILE = "app_config.json"
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("台股智能選股系統")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化數據
        self.config = self.load_config()
        self.db_connections = {}
        self.db_tables = {'price': 'stock_daily_data', 'pe': None}
        self.thread_pool = QThreadPool()
        self.period_map = {
            "2W": 10,       # 2週約10個交易日
            "1M": 22,       # 1個月約22個交易日
            "2M": 44,       # 2個月約44個交易日
            "6M": 132,      # 6個月約132個交易日
            "1Y": 264,      # 1年約264個交易日
            "2Y": 528,      # 2年約528個交易日
        }
        
        # 儲存對QApplication實例的引用，用於事件處理
        self.app = QApplication.instance()
        
        # 初始化UI
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout()  # 改為水平布局
        
        # 初始化狀態列
        self.init_status_bar()
        
        # 初始化主UI元件
        self.init_ui_components()
        
        # 連接數據庫
        self.init_database()
        
        # 明確設置策略名稱 
        self.strategy_name = "勝率73.45%"  # 設置默認策略名稱為勝率73.45%
        
        # 載入選股策略
        self.load_strategies()
        
        logging.info("GUI 初始化完成")
    
    def init_status_bar(self):
        """初始化狀態列和相關元件"""
        self.status_bar = self.statusBar()
        
        # 數據庫狀態標籤
        self.db_status_label = QLabel("數據庫未連接")
        self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.db_status_label)
        
        # 重新連接按鈕
        self.reconnect_btn = QPushButton("重新連接數據庫")
        self.reconnect_btn.clicked.connect(self.reconnect_database)
        self.status_bar.addPermanentWidget(self.reconnect_btn)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def show_status(self, message, timeout=5000, is_error=False):
        """顯示狀態欄訊息"""
        self.status_bar.showMessage(message, timeout)
        if is_error:
            self.status_bar.setStyleSheet("color: red;")
        else:
            self.status_bar.setStyleSheet("")
        # 確保UI立即更新
        QApplication.processEvents()
    
    def update_db_status(self, connected=True):
        """更新數據庫連接狀態"""
        if connected and 'price' in self.db_connections:
            text = "價格DB: ✓"
            if 'pe' in self.db_connections:
                text += " | PE DB: ✓"
            self.db_status_label.setText(text)
            self.db_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.db_status_label.setText("數據庫未連接")
            self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
    
    def init_ui_components(self):
        """初始化UI元件"""
        # 創建主要分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(main_splitter)
        
        # 創建左側面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        
        # =========== 左側上方：選股策略 ===========
        strategy_group = QGroupBox("選股策略")
        strategy_layout = QVBoxLayout(strategy_group)
        
        # 策略控制區
        strategy_control_layout = QGridLayout()
        
        # 日期選擇
        strategy_control_layout.addWidget(QLabel("計算日期:"), 0, 0)
        self.strategy_date = QDateEdit()
        self.strategy_date.setDate(QDate.currentDate())
        self.strategy_date.setCalendarPopup(True)
        strategy_control_layout.addWidget(self.strategy_date, 0, 1)
        
        # 最近交易日按鈕
        latest_date_btn = QPushButton("最近交易日")
        latest_date_btn.clicked.connect(self.set_latest_trading_date)
        strategy_control_layout.addWidget(latest_date_btn, 0, 2)
        
        # 策略選擇
        strategy_control_layout.addWidget(QLabel("策略:"), 1, 0)
        self.strategy_combo = QComboBox()
        strategy_control_layout.addWidget(self.strategy_combo, 1, 1, 1, 2)
        
        # 策略按鈕
        strategy_btn_layout = QHBoxLayout()
        add_btn = QPushButton("新增")
        add_btn.clicked.connect(self.add_strategy)
        edit_btn = QPushButton("編輯")
        edit_btn.clicked.connect(self.edit_strategy)
        delete_btn = QPushButton("刪除")
        delete_btn.clicked.connect(self.delete_strategy)
        run_btn = QPushButton("執行選股")
        run_btn.clicked.connect(self.run_strategy)
        
        strategy_btn_layout.addWidget(add_btn)
        strategy_btn_layout.addWidget(edit_btn)
        strategy_btn_layout.addWidget(delete_btn)
        strategy_btn_layout.addWidget(run_btn)
        
        strategy_layout.addLayout(strategy_control_layout)
        strategy_layout.addLayout(strategy_btn_layout)
        
        # 添加策略組到左側面板
        left_layout.addWidget(strategy_group)
        
        # =========== 左側中下方：股票列表 ===========
        
        # 使用標籤頁分離全部股票和選股結果
        stock_tabs = QTabWidget()
        
        # 全部股票頁面
        all_stocks_page = QWidget()
        all_stocks_layout = QVBoxLayout(all_stocks_page)
        
        # 股票搜索框
        search_layout = QHBoxLayout()  # 修正：創建搜索布局
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜尋股票...")
        self.search_input.textChanged.connect(self.filter_stocks)
        search_layout.addWidget(self.search_input)
        all_stocks_layout.addLayout(search_layout)
        
        # 股票列表
        self.all_stocks_list = QListWidget()
        self.all_stocks_list.setMinimumWidth(180)
        self.all_stocks_list.setFont(QFont("Microsoft JhengHei", 10))
        self.all_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        all_stocks_layout.addWidget(self.all_stocks_list)
        
        # 添加全部股票頁面到標籤頁
        stock_tabs.addTab(all_stocks_page, "全部股票")
        
        # 選股結果頁面
        filtered_stocks_page = QWidget()
        filtered_stocks_layout = QVBoxLayout(filtered_stocks_page)
        
        # 選股結果列表
        self.filtered_stocks_list = QListWidget()
        self.filtered_stocks_list.setMinimumWidth(180)
        self.filtered_stocks_list.setFont(QFont("Microsoft JhengHei", 10))
        self.filtered_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        filtered_stocks_layout.addWidget(self.filtered_stocks_list)
        
        # 添加選股結果頁面到標籤頁
        stock_tabs.addTab(filtered_stocks_page, "選股結果")
        
        # 添加股票標籤頁到左側面板
        left_layout.addWidget(stock_tabs, 1)  # 1表示拉伸因子
        
        main_splitter.addWidget(left_panel)
        
        # 創建右側面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        
        # 主要內容區域 - 標籤頁
        self.tab_widget = QTabWidget()
        
        # K線圖頁面
        k_chart_page = QWidget()
        k_chart_layout = QVBoxLayout(k_chart_page)
        
        # 添加控制區域
        controls_layout = QHBoxLayout()
        controls_layout.addWidget(QLabel("週期:"))
        self.period_combo = QComboBox()
        for period in ["2W", "1M", "2M", "6M", "1Y", "2Y"]:
            self.period_combo.addItem(period)
        # 設置預設值為 2M
        self.period_combo.setCurrentText("2M")
        self.period_combo.currentIndexChanged.connect(self.on_period_changed)
        controls_layout.addWidget(self.period_combo)
        
        # 添加更多控制選項
        controls_layout.addStretch()
        k_chart_layout.addLayout(controls_layout)
        
        # 添加K線圖視圖
        self.chart_view = pg.PlotWidget()
        self.chart_view.setBackground('k')  # 黑色背景
        self.chart_view.showGrid(x=True, y=True, alpha=0.2)
        k_chart_layout.addWidget(self.chart_view)
        
        # 添加成交量視圖
        self.volume_view = pg.PlotWidget()
        self.volume_view.setBackground('k')  # 黑色背景
        self.volume_view.showGrid(x=True, y=True, alpha=0.2)
        k_chart_layout.addWidget(self.volume_view)
        self.volume_view.setMaximumHeight(150)  # 限制成交量視圖的高度
        
        # 連結x軸
        self.chart_view.getViewBox().setXLink(self.volume_view.getViewBox())
        
        self.tab_widget.addTab(k_chart_page, "K線圖")
        
        # 選股結果頁面
        result_page = QWidget()
        result_layout = QVBoxLayout(result_page)
        
        # 結果摘要
        self.result_summary_label = QLabel("尚未執行選股策略")
        result_layout.addWidget(self.result_summary_label)
        
        # 添加筛选按钮
        filter_buttons_layout = QHBoxLayout()
        self.show_matching_btn = QPushButton("僅顯示符合條件股票")
        self.show_matching_btn.clicked.connect(self.filter_matching_results)
        self.restore_all_btn = QPushButton("顯示全部股票")
        self.restore_all_btn.clicked.connect(self.restore_all_results)
        filter_buttons_layout.addWidget(self.show_matching_btn)
        filter_buttons_layout.addWidget(self.restore_all_btn)
        result_layout.addLayout(filter_buttons_layout)
        
        # 結果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(8)  # 股票代碼、名稱、收盤價、5個條件
        self.result_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價", "條件1", "條件2", "條件3", "條件4", "條件5"])
        self.result_table.doubleClicked.connect(self.on_result_double_clicked)
        result_layout.addWidget(self.result_table)
        
        self.tab_widget.addTab(result_page, "選股結果")
        
        # 添加到右側面板
        right_layout.addWidget(self.tab_widget)
        
        # 添加右側面板到分割器
        main_splitter.addWidget(right_panel)
        
        # 設置分割比例
        main_splitter.setSizes([300, 900])  # 左側小，右側大
        
        # 設置主布局
        self.central_widget.setLayout(self.main_layout)
    
    def init_database(self):
        """初始化數據庫連接"""
        try:
            price_db_path = self.resolve_db_path('price')
            pe_db_path = self.resolve_db_path('pe')
            
            self.show_status("正在連接數據庫...")
            
            # 直接在主線程連接數據庫
            self.db_connections = {}
            # 存儲表名
            self.db_tables = {'price': 'stock_daily_data', 'pe': None}
            
            # 輸出路徑信息以便調試
            logging.info(f"嘗試連接price數據庫: {price_db_path}")
            logging.info(f"嘗試連接pe數據庫: {pe_db_path}")
            
            # 連接price.db
            if os.path.exists(price_db_path):
                self.db_connections['price'] = sqlite3.connect(price_db_path)
                logging.info(f"成功連接price數據庫: {price_db_path}")
                
                # 驗證表結構
                cursor = self.db_connections['price'].cursor()
                cursor.execute("PRAGMA table_info(stock_daily_data)")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"檢測到的欄位: {columns}")
                
                # 檢查必要欄位
                required_cols = ['stock_id', 'Open', 'High', 'Low', 'Close', 'Volume', 'date']
                missing = [col for col in required_cols if col not in columns]
                if missing:
                    logging.error(f"數據表缺少必要欄位: {missing}")
                    self.show_status(f"數據表缺少必要欄位: {missing}", is_error=True)
                else:
                    logging.info("數據表結構驗證通過")
                    self.load_stock_list()  # 載入股票列表
                    self.update_db_status(True)
                    self.show_status("數據庫連接成功")
            else:
                logging.error(f"價格數據庫文件不存在: {price_db_path}")
                self.show_status(f"價格數據庫文件不存在: {price_db_path}", is_error=True)
            
        except Exception as e:
            self.show_status(f"數據庫連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def detect_db_tables(self, db_type):
        """檢測數據庫表結構"""
        try:
            conn = self.db_connections[db_type]
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            if tables:
                # 使用第一個表作為主表
                self.db_tables[db_type] = tables[0][0]
                
                # 檢查表結構
                cursor.execute(f"PRAGMA table_info({self.db_tables[db_type]})")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"{db_type}數據庫表 {self.db_tables[db_type]} 的欄位: {columns}")
                
            return len(tables) > 0
        except Exception as e:
            logging.error(f"檢測{db_type}數據庫表失敗: {str(e)}")
            return False
    
    def load_stock_list(self):
        """載入股票清單"""
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法載入股票清單", is_error=True)
                return
            
            conn = self.db_connections['price']
            table = self.db_tables['price']
            
            # SQLite不支持原生REGEXP，改用GLOB
            query = f"""
                SELECT DISTINCT stock_id, stock_name 
                FROM {table}
                WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                  AND LENGTH(stock_id) BETWEEN 4 AND 6
                ORDER BY 
                    CASE 
                        WHEN LENGTH(stock_id) = 4 THEN 1
                        WHEN LENGTH(stock_id) = 5 THEN 2
                        WHEN LENGTH(stock_id) = 6 THEN 3
                    END,
                    CAST(stock_id AS INTEGER)
            """
            
            logging.info(f"執行股票列表查詢: {query}")
            df = pd.read_sql_query(query, conn)
            logging.info(f"查詢到 {len(df)} 支股票")
            
            if df.empty:
                self.show_status("未找到股票資料", is_error=True)
                return
            
            # 清空並添加股票列表
            self.all_stocks_list.clear()
            for _, row in df.iterrows():
                # 將股票代號和名稱一起顯示
                item_text = f"{row['stock_id']} {row['stock_name']}"
                self.all_stocks_list.addItem(item_text)
            
            self.show_status(f"載入了 {len(df)} 支股票")
            
        except Exception as e:
            self.show_status(f"載入股票列表失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def get_selected_stocks(self):
        """獲取選中的股票，若無選中則返回全部"""
        selected = []
        for index in range(self.all_stocks_list.count()):
            item = self.all_stocks_list.item(index)
            if item.isSelected():
                selected.append(item.text())
        
        # 若未選中則返回全部
        if not selected:
            for index in range(self.all_stocks_list.count()):
                item = self.all_stocks_list.item(index)
                selected.append(item.text())
                
        return selected
    
    def run_screener(self):
        """執行選股"""
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法執行選股", is_error=True)
                return
                
            # 獲取選股策略
            strategy = self.strategy_combo.currentData()
            
            # 獲取股票列表
            stock_ids = self.get_selected_stocks()
            if not stock_ids:
                self.show_status("無可用股票", is_error=True)
                return
                
            self.show_status(f"正在分析 {len(stock_ids)} 支股票...")
            
            # 設置進度條
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(stock_ids))
            self.progress_bar.setValue(0)
            
            # 清空結果表格
            self.result_table.setRowCount(0)
            
            # 獲取當前計算日期並確保是datetime對象
            current_date = self.strategy_date.date().toString("yyyy-MM-dd")
            current_date = datetime.strptime(current_date, "%Y-%m-%d")
            logging.info(f"當前計算日期: {current_date}")
            
            # 設定更嚴格的日期差異閾值，確保能排除已下市或已沒交易的股票
            delisting_threshold = 15  # 將閾值從30天降低到15天，更嚴格地排除已沒交易的股票
            
            # 特別處理金可股票（8406）
            special_delisted_stocks = ['8406']  # 金可股票代碼
            logging.info(f"特別處理已下市股票列表: {special_delisted_stocks}")
            
            # 記錄當前日期以便日誌
            logging.info(f"執行選股日期: {current_date.strftime('%Y-%m-%d')}，將排除超過{delisting_threshold}天未交易的股票")
            
            
            # 依序分析每支股票
            results = []
            excluded_count = 0
            for idx, stock_id in enumerate(stock_ids):
                # 更新進度
                self.progress_bar.setValue(idx + 1)
                self.show_status(f"分析中: {stock_id} ({idx+1}/{len(stock_ids)})")
                QApplication.processEvents()  # 確保UI更新
                
                # 獲取股票數據
                try:
                    data = self.fetch_stock_data(stock_id)
                    
                    # 檢查是否為已下市股票
                    if not data.empty:
                        # 特別處理金可股票（8406）及其他已知下市股票
                        if stock_id in special_delisted_stocks:
                            logging.warning(f"股票 {stock_id} 已被標記為下市股票，直接排除")
                            excluded_count += 1
                            continue
                            
                        # 檢查最後交易日與當前計算日期的差距
                        try:
                            last_trading_date = data['date'].max()
                            if not isinstance(last_trading_date, datetime):
                                last_trading_date = pd.to_datetime(last_trading_date)
                            date_diff = (current_date - last_trading_date).days
                            logging.info(f"股票 {stock_id} 日期比較 - 最後交易日: {last_trading_date}，計算日期: {current_date}，相差: {date_diff}天")
                            
                            if date_diff > delisting_threshold:
                                logging.warning(f"股票 {stock_id} 已沒有交易，最後交易日: {last_trading_date}，相差: {date_diff}天")
                                excluded_count += 1
                                continue
                            
                            if last_trading_date < current_date and date_diff <= delisting_threshold:
                                logging.info(f"股票 {stock_id} 最後交易日: {last_trading_date} 早於計算日期: {current_date}，但相差僅 {date_diff} 天，仍納入分析")
                        except Exception as e:
                            logging.error(f"處理股票 {stock_id} 的日期時出錯: {str(e)}")
                            excluded_count += 1
                            continue
                        
                        # 應用策略
                        if self.apply_strategy(data):  # 替換為實際策略條件
                            results.append({
                                '股票代碼': stock_id,
                                '收盤價': data['Close'].iloc[-1],
                                '條件符合': '是'
                            })
                except Exception as e:
                    logging.error(f"處理 {stock_id} 時出錯: {str(e)}")
            
            # 顯示結果
            self.show_results(results)
            
            # 隱藏進度條
            self.progress_bar.setVisible(False)
            self.show_status(f"選股完成，找到 {len(results)} 支符合條件的股票，排除 {excluded_count} 支已沒交易的股票")
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.show_status(f"選股過程出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def fetch_stock_data(self, stock_id, days=90):
        """獲取股票數據"""
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接", is_error=True)
                return pd.DataFrame()
                
            # 使用SQL查詢獲取數據
            query = f"""
            SELECT date, Open, High, Low, Close, Volume
            FROM {self.db_tables['price']}
            WHERE stock_id = ?
            ORDER BY date DESC
            LIMIT ?
            """
            
            cursor = self.db_connections['price'].cursor()
            cursor.execute(query, (stock_id, days))
            data = cursor.fetchall()
            cursor.close()
            
            if not data:
                return pd.DataFrame()
                
            # 將數據轉換為DataFrame
            df = pd.DataFrame(data, columns=['date', 'Open', 'High', 'Low', 'Close', 'Volume'])
            
            # 轉換數據類型
            df['date'] = pd.to_datetime(df['date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col])
                
            # 反轉順序，使其按日期升序
            df = df.sort_values('date').reset_index(drop=True)
            
            # 計算技術指標
            df = self.calculate_indicators(df)
            
            return df
        except Exception as e:
            self.show_status(f"獲取股票數據時出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
            return pd.DataFrame()
    
    def apply_strategy(self, data, strategy=None):
        """應用選股策略
        
        Args:
            data: 股票數據DataFrame
            strategy: 策略名稱，如果為None則使用簡單的均線策略
        
        Returns:
            bool: 是否符合策略條件
        """
        if data.empty or len(data) < 20:
            return False
            
        # 如果沒有指定策略，使用簡單的均線策略
        if strategy is None:
            # 計算均線
            if 'MA5' not in data.columns:
                data['MA5'] = data['Close'].rolling(5).mean()
            if 'MA20' not in data.columns:
                data['MA20'] = data['Close'].rolling(20).mean()
                
            # 檢查數據是否足夠
            if len(data) < 2:
                return False
                
            # 檢查收盤價是否高於20日均線
            current = data.iloc[-1]
            return current['Close'] > current['MA20']
            
        elif strategy == 'ma_cross':
            # 計算5日均線和20日均線
            data['MA5'] = data['Close'].rolling(5).mean()
            data['MA20'] = data['Close'].rolling(20).mean()
            
            # 當前5日均線是否上穿20日均線
            if len(data) < 2:
                return False
                
            current = data.iloc[-1]
            previous = data.iloc[-2]
            
            cross_up = previous['MA5'] <= previous['MA20'] and current['MA5'] > current['MA20']
            return cross_up
            
        elif strategy == 'volume_surge':
            # 判斷成交量是否放大
            if len(data) < 5:
                return False
                
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].iloc[-6:-1].mean()
            
            return current_volume > avg_volume * 2  # 成交量是否放大2倍
            
        return False
    
    def show_results(self, results):
        """顯示選股結果"""
        # 清空表格
        self.result_table.setRowCount(0)
        
        if not results:
            return
            
        # 添加結果到表格
        self.result_table.setRowCount(len(results))
        for row, stock in enumerate(results):
            self.result_table.setItem(row, 0, QTableWidgetItem(stock['股票代碼']))
            # 添加股票名稱
            self.result_table.setItem(row, 1, QTableWidgetItem(stock.get('股票名稱', 'N/A')))
            self.result_table.setItem(row, 2, QTableWidgetItem(str(stock['收盤價'])))
            self.result_table.setItem(row, 3, QTableWidgetItem("符合"))
    
    def resolve_db_path(self, db_type):
        """解析數據庫路徑"""
        # 使用指定的固定路徑
        if db_type == 'price':
            # 使用正確的路徑
            return os.path.join('D:/Finlab/history/tables/price.db')
        elif db_type == 'pe':
            return os.path.join('D:/Finlab/history/tables/pe_data.db')
        else:
            # 其他資料庫使用本地路徑
            db_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db')
            os.makedirs(db_folder, exist_ok=True)
            return os.path.join(db_folder, f'{db_type}.db')
    
    def load_config(self):
        """載入配置文件"""
        if os.path.exists(self.CONFIG_FILE):
            try:
                with open(self.CONFIG_FILE, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_config(self):
        """保存配置文件"""
        with open(self.CONFIG_FILE, 'w') as f:
            json.dump(self.config, f, indent=4)
    
    def reconnect_database(self):
        """重新連接數據庫"""
        try:
            # 關閉現有連接
            self.close_db_connections()
            
            # 重新初始化
            self.init_database()
            
        except Exception as e:
            self.show_status(f"重新連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def closeEvent(self, event):
        """關閉視窗時釋放資源"""
        # 關閉數據庫連接
        for db in self.db_connections.values():
            try:
                db.close()
            except:
                pass
        
        # 確保在退出時調用關閉數據庫連接方法
        try:
            self.close_db_connections()
            logging.info("正常關閉應用程序")
        except Exception as e:
            logging.error(f"關閉時發生錯誤: {str(e)}")
            logging.error(traceback.format_exc())
        
        super().closeEvent(event)

    def on_stock_double_clicked(self, item):
        """雙擊股票時顯示K線圖"""
        # 從列表項提取股票代號
        stock_id = item.text().split()[0]
        
        # 切換到K線圖頁籤
        self.tab_widget.setCurrentIndex(0)  # 切換到K線圖頁簽
        
        # 繪製K線圖
        self.plot_stock(stock_id)

    def on_period_changed(self):
        """處理週期變更"""
        # 獲取當前選中的股票
        current_item = self.all_stocks_list.currentItem()
        if current_item:
            stock_id = current_item.text().split()[0]  # 分割獲取股票代碼
            self.plot_stock(stock_id)
    
    def plot_candlestick(self, df, stock_id):
        """繪製K線蠟燭圖"""
        try:
            # 清空圖表容器
            while self.chart_container_layout.count():
                item = self.chart_container_layout.takeAt(0)
                widget = item.widget()
                if widget:
                    widget.deleteLater()
            
            if df.empty:
                self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
                return
            
            # 獲取股票名稱
            stock_name = df['stock_name'].iloc[0] if 'stock_name' in df.columns else ''
            
            # 設置背景為深色
            pg.setConfigOption('background', 'k')  # 黑色背景
            pg.setConfigOption('foreground', 'w')  # 白色前景
            
            # 技術指標已在fetch_stock_data中計算，這裡不需要重複計算
            
            # 創建圖表佈局
            chart_layout_widget = pg.GraphicsLayoutWidget()
            self.chart_container_layout.addWidget(chart_layout_widget)
            
            # 創建K線圖區域 (上方佔比較大)
            p1 = chart_layout_widget.addPlot(row=0, col=0)
            p1.showGrid(x=True, y=True, alpha=0.3)
            p1.setLabel('left', '價格')
            
            # 隱藏K線圖底部的X軸標籤
            p1.getAxis('bottom').setStyle(showValues=False)
            
            # 在原圖下方添加成交量圖 (下方較小)
            p2 = chart_layout_widget.addPlot(row=1, col=0)
            p2.showGrid(x=True, y=True, alpha=0.3)
            p2.setLabel('left', '成交量(張)')
            
            # 修正Y軸格式化函數，處理多個參數
            def format_volume_with_commas(values):
                ret = []
                for value in values:
                    if value >= 1000:
                        ret.append(f"{int(value):,}")
                    else:
                        ret.append(f"{int(value):,}")
                return ret
            
            # 設置格式化函數
            p2.getAxis('left').tickStrings = format_volume_with_commas
            
            p2.setXLink(p1)  # 連結X軸
            
            # 設置比例 (K線圖佔75%，成交量圖佔25%)
            chart_layout_widget.ci.layout.setRowStretchFactor(0, 3)
            chart_layout_widget.ci.layout.setRowStretchFactor(1, 1)
            
            # 準備X軸日期標籤
            dates = [d.strftime('%Y-%m-%d') for d in df['date']]
            x_axis_2 = p2.getAxis('bottom')
            
            # 設置標題
            title = f"{stock_id} {stock_name} K線圖 (最後交易日: {dates[-1]})"
            p1.setTitle(title, color='w')
            
            # 設置X軸為日期
            if len(dates) > 10:
                # 選擇適當的日期間隔
                step = max(1, len(dates) // 10)
                ticks = [(i, dates[i]) for i in range(0, len(dates), step)]
                x_axis_2.setTicks([ticks, []])
            else:
                # 如果日期較少，全部顯示
                ticks = [(i, date) for i, date in enumerate(dates)]
                x_axis_2.setTicks([ticks, []])
            
            # 設置均線顏色和名稱，確保圖例清晰可見
            line_colors = {
                'MA5': ('w', '5日均線'),
                'MA10': ('y', '10日均線'),
                'MA20': ('m', '月均線(20日)'),
                'MA60': ('c', '季均線(60日)'),
                'MA120': ((255, 165, 0), '半年線(120日)'),
                'MA240': ((255, 105, 180), '年線(240日)'),
                'BB_upper': ((50, 205, 50), '布林上軌'),
                'BB_lower': ((255, 99, 71), '布林下軌')
            }
            
            # 使用內建圖例並增強其可見性
            # 確保圖例大小足夠顯示所有線條
            legend = p1.addLegend(offset=(15, 10), labelTextSize='9pt', brush=pg.mkBrush(0, 0, 0, 200))
            
            # 定義繪製順序
            plot_order = ['MA5', 'MA10', 'MA20', 'MA60', 'MA120', 'MA240', 'BB_upper', 'BB_lower']
            
            # 先繪製所有K線
            for i in range(len(df)):
                # 獲取當前K線數據
                open_p = df['Open'].iloc[i]
                close_p = df['Close'].iloc[i]
                low_p = df['Low'].iloc[i]
                high_p = df['High'].iloc[i]
                
                # 根據收盤價與開盤價的關係決定顏色（紅漲綠跌）
                if close_p > open_p:
                    # 上漲，紅色
                    color = (255, 0, 0)
                else:
                    # 下跌或平盤，綠色
                    color = (0, 255, 0)
                
                # 繪製K線實體
                bar = pg.BarGraphItem(
                    x=[i], height=[abs(close_p - open_p)], 
                    width=0.8, brush=color, pen=color
                )
                
                # 設置實體位置
                y_base = min(close_p, open_p)
                bar.setPos(0, y_base)
                p1.addItem(bar)
                
                # 繪製上下影線
                p1.plot(
                    [i, i], [low_p, high_p],
                    pen=pg.mkPen(color, width=1)
                )
            
            # 繪製各均線
            for ma_name in plot_order:
                if ma_name in line_colors:
                    color, label = line_colors[ma_name]
                    if ma_name in df.columns and not df[ma_name].isna().all():
                        if ma_name == 'MA240':  # 年線加粗
                            p1.plot(np.arange(len(df)), df[ma_name].values, 
                                    pen=pg.mkPen(color, width=3), 
                                    name=label)
                        elif ma_name == 'MA120':  # 半年線加粗且虛線
                            p1.plot(np.arange(len(df)), df[ma_name].values, 
                                    pen=pg.mkPen(color, width=2.5, style=Qt.PenStyle.DashLine), 
                                    name=label)
                        elif 'BB' in ma_name:  # 布林通道使用虛線
                            p1.plot(np.arange(len(df)), df[ma_name].values, 
                                    pen=pg.mkPen(color, width=1, style=Qt.PenStyle.DashLine), 
                                    name=label)
                        else:  # 均線使用實線
                            p1.plot(np.arange(len(df)), df[ma_name].values, 
                                    pen=pg.mkPen(color, width=1), 
                                    name=label)
            
            # 繪製成交量柱狀圖
            volume_data = df['Volume'].values
            if volume_data.max() > 0:
                volume = volume_data
            else:
                volume = np.zeros_like(volume_data)
                p2.setLabel('left', '成交量(無數據)')
            
            for i in range(len(df)):
                if df['Close'].iloc[i] > df['Open'].iloc[i]:
                    color = (255, 0, 0, 180)
                else:
                    color = (0, 255, 0, 180)
                
                vol_bar = pg.BarGraphItem(
                    x=[i], height=[volume[i]], width=0.8, 
                    brush=color, pen=color
                )
                p2.addItem(vol_bar)
            
            if 'BB_upper' in df.columns and not df['BB_upper'].isna().all():
                y_max = max(df['High'].max(), df['BB_upper'].dropna().max()) * 1.01
            else:
                y_max = df['High'].max() * 1.01
                
            if 'BB_lower' in df.columns and not df['BB_lower'].isna().all():
                y_min = min(df['Low'].min(), df['BB_lower'].dropna().min()) * 0.99
            else:
                y_min = df['Low'].min() * 0.99
                
            p1.setYRange(y_min, y_max)
            if volume.max() > 0:
                p2.setYRange(0, volume.max() * 1.1)
            else:
                p2.setYRange(0, 1)
            
            p1.setXRange(-0.5, len(df) - 0.5)
            p2.setXRange(-0.5, len(df) - 0.5)
            
            logging.info(f"繪製K線圖: {stock_id}, 數據點數: {len(df)}, 價格範圍: {y_min:.2f}-{y_max:.2f}")
            logging.info(f"成交量範圍: 0-{volume.max():.2f}, 原始最大成交量: {df['Volume'].max()}")
            
            self.show_status(f"{stock_id} K線圖已更新")
            
            # 添加滑鼠懸停功能 - 十字線和數據標籤
            vLine = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('w', width=0.8))
            hLine = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('w', width=0.8))
            p1.addItem(vLine, ignoreBounds=True)
            p1.addItem(hLine, ignoreBounds=True)

            label = pg.TextItem(html='', border=pg.mkPen('w', width=2), fill=(0, 0, 0, 230), anchor=(0, 0))
            p1.addItem(label)
            label.hide()
            label.setZValue(2000)
            scatter = pg.ScatterPlotItem(size=15, pen=pg.mkPen('w', width=2), brush=pg.mkBrush(255, 0, 0, 250))
            p1.addItem(scatter)
            scatter.setZValue(1500)
            
            def mouseMoved(evt):
                try:
                    pos = evt[0]
                    if self.chart_view.sceneBoundingRect().contains(pos):
                        mousePoint = self.chart_view.getViewBox().mapSceneToView(pos)
                        x = mousePoint.x()
                        x = min(max(0, x), len(df)-1)
                        index = int(x)
                        vLine.setPos(x)
                        hLine.setPos(mousePoint.y())
                        vLine.setVisible(True)
                        hLine.setVisible(True)
                        close = df['Close'].iloc[index]
                        scatter.setData([index], [close])
                        change_pct = 0.0
                        change_color = "white"
                        if index > 0:
                            prev_close = df['Close'].iloc[index-1]
                            change_pct = (close - prev_close) / prev_close * 100
                            change_color = "red" if change_pct >= 0 else "green"
                        date_str = df['date'].iloc[index].strftime('%Y-%m-%d')
                        html = f"""
                        <div style='background-color:rgba(0,0,0,200);padding:5px;'>
                        <span style='color:white;font-weight:bold;'>{date_str}</span><br>
                        <span style='color:white;'>開盤: {df['Open'].iloc[index]:.2f}</span><br>
                        <span style='color:white;'>最高: {df['High'].iloc[index]:.2f}</span><br>
                        <span style='color:white;'>最低: {df['Low'].iloc[index]:.2f}</span><br>
                        <span style='color:white;'>收盤: {df['Close'].iloc[index]:.2f}</span><br>
                        <span style='color:{change_color};'>漲跌幅: {change_pct:+.2f}%</span><br>
                        <span style='color:white;'>成交量: {int(df['Volume'].iloc[index]/1000):,}張</span><br>
                        <span style='color:white;'>成交額: {close * df['Volume'].iloc[index]/10000:.2f}萬</span><br>
                        """
                        
                        for ma in [5, 20, 60, 120, 240]:
                            ma_col = f'MA{ma}'
                            if ma_col in df.columns and x < len(df) and not pd.isna(df[ma_col].iloc[index]):
                                html += f"<span style='color:white;'>{ma_col}: {df[ma_col].iloc[index]:.2f}</span><br>"
                        
                        for rsi_period in [6, 13]:
                            rsi_col = f'RSI{rsi_period}'
                            if rsi_col in df.columns and index < len(df) and not pd.isna(df[rsi_col].iloc[index]):
                                rsi_value = df[rsi_col].iloc[index]
                                color = "red" if rsi_value > 70 else "green" if rsi_value < 30 else "white"
                                html += f"<span style='color:{color};'>{rsi_col}: {rsi_value:.2f}</span><br>"
                        
                        html += "</div>"
                        label.setHtml(html)
                        label.setAnchor((0, 0))
                        x_pos = index - 15 if index > len(df)/2 else index + 5
                        label.setPos(x_pos, mousePoint.y())
                        label.show()
                    else:
                        vLine.setVisible(False)
                        hLine.setVisible(False)
                        label.hide()
                        scatter.setData([], [])
                except Exception as e:
                    logging.error(f"滑鼠懸停事件錯誤: {str(e)}")
                    logging.error(traceback.format_exc())
            
            proxy = pg.SignalProxy(p1.scene().sigMouseMoved, rateLimit=30, slot=mouseMoved)
            p1.proxy = proxy
            p1.update()
            if hasattr(self, 'app') and self.app is not None:
                self.app.processEvents()
            
            self.show_status(f"{stock_id} K線圖已更新")
            
            ma_colors = {
                5: ('w', 2),
                20: ('y', 2),
                60: ('c', 2),
                120: ('m', 2),
                240: ('g', 2)
            }
            
            for ma_days, (color, width) in ma_colors.items():
                ma_col = f'MA{ma_days}'
                if ma_col in df.columns:
                    ma_series = df[ma_col].dropna()
                    if not ma_series.empty:
                        x = np.array(range(len(df) - len(ma_series), len(df)))
                        y = ma_series.values
                        p1.plot(x, y, pen=pg.mkPen(color, width=width), name=f'{ma_days}日均線')
            
            min_price = df['Low'].min() * 0.985
            max_price = df['High'].max() * 1.015
            
            p1.getViewBox().setYRange(min_price, max_price)
            
        except Exception as e:
            self.show_status(f"繪製K線圖時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def update_current_chart(self):
        """更新當前選中股票的圖表"""
        current_item = self.all_stocks_list.currentItem()
        if current_item:
            stock_id = current_item.text().split()[0]
            self.plot_stock(stock_id)

    def set_latest_trading_date(self):
        """設置為最近的交易日"""
        try:
            if 'price' in self.db_connections:
                cursor = self.db_connections['price'].cursor()
                cursor.execute(f"SELECT MAX(date) FROM {self.db_tables['price']}")
                latest_date = cursor.fetchone()[0]
                if latest_date:
                    latest_date = QDate.fromString(latest_date, "yyyy-MM-dd")
                    self.strategy_date.setDate(latest_date)
                    self.show_status(f"已設置為最近交易日: {latest_date.toString('yyyy-MM-dd')}")
                else:
                    self.show_status("無法獲取最近交易日", is_error=True)
            else:
                self.show_status("數據庫未連接，無法獲取最近交易日", is_error=True)
        except Exception as e:
            self.show_status(f"設置最近交易日時出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def add_strategy(self):
        """新增選股策略"""
        dialog = StrategyDialog(self)
        dialog.exec()  # 使用exec()而不是exec
        if dialog.result() == QDialog.DialogCode.Accepted:
            strategy_name = dialog.name_edit.text()
            conditions = dialog.get_conditions()
            
            if not hasattr(self, 'strategies'):
                self.strategies = {}
            
            self.strategies[strategy_name] = conditions
            current_index = self.strategy_combo.findText(strategy_name)
            if current_index == -1:
                self.strategy_combo.addItem(strategy_name)
            self.strategy_combo.setCurrentText(strategy_name)
            self.save_strategies()
            self.show_status(f"已添加策略: {strategy_name}")

    def edit_strategy(self):
        """編輯選股策略"""
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        
        dialog = StrategyDialog(self, 
                              strategy_name=current_strategy, 
                              conditions=self.strategies[current_strategy])
        if dialog.exec():
            new_name = dialog.name_edit.text()
            conditions = dialog.get_conditions()
            
            if new_name != current_strategy:
                del self.strategies[current_strategy]
                self.strategy_combo.removeItem(self.strategy_combo.currentIndex())
                self.strategies[new_name] = conditions
                self.strategy_combo.addItem(new_name)
                self.strategy_combo.setCurrentText(new_name)
            else:
                self.strategies[current_strategy] = conditions
            
            self.save_strategies()
            self.show_status(f"已更新策略: {new_name}")

    def delete_strategy(self):
        """刪除選股策略"""
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        
        reply = QMessageBox.question(self, "確認刪除", 
                                   f"確定要刪除策略 '{current_strategy}' 嗎？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            del self.strategies[current_strategy]
            self.strategy_combo.removeItem(self.strategy_combo.currentIndex())
            self.save_strategies()
            self.show_status(f"已刪除策略: {current_strategy}")

    def save_strategies(self):
        """保存選股策略到配置文件"""
        try:
            strategies_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies.json')
            with open(strategies_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, ensure_ascii=False, indent=4)
        except Exception as e:
            self.show_status(f"保存策略失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def load_strategies(self):
        """從配置文件載入選股策略"""
        try:
            strategies_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies.json')
            
            logging.info("重新創建默認策略: 勝率73.45%")
            
            self.strategies = {
                "勝率73.45%": [
                    {"type": "ma_trend", "ma": "MA240", "trend": "up", "days": 1},
                    {"type": "ma_position", "ma": "MA10", "position": "above", "price": "close"},
                    {"type": "ma_trend", "ma": "MA20", "trend": "up", "days": 5},
                    {"type": "volume_ma_cross", "short_period": 3, "long_period": 18, "days": 3},
                    {"type": "rsi_combined", "rsi_long": 13, "rsi_long_threshold": 50, 
                     "rsi_short": 6, "rsi_short_threshold": 70},
                    {"type": "volume_value", "min_value": 10000000}
                ]
            }
            
            if os.path.exists(strategies_file):
                os.remove(strategies_file)
                logging.info("已刪除舊策略文件")
                
            with open(strategies_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, indent=4, ensure_ascii=False)
            logging.info("已保存新策略文件")
                
            self.strategy_combo.clear()
            for strategy_name in self.strategies.keys():
                self.strategy_combo.addItem(strategy_name)
                
            if "勝率73.45%" in self.strategies:
                self.strategy_combo.setCurrentText("勝率73.45%")
                
        except Exception as e:
            self.show_status(f"載入策略失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def run_strategy(self):
        """執行選股策略"""
        current_strategy = self.strategy_combo.currentText()
        logging.info(f"開始執行策略: {current_strategy}")
        
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        
        # 獲取當前選擇的日期
        selected_date = self.strategy_date.date().toString("yyyy-MM-dd")
        
        progress = QProgressDialog("執行選股策略...", "取消", 0, 100, self)
        progress.setWindowTitle("處理中")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.show()
        
        try:
            cursor = self.db_connections['price'].cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            logging.info(f"數據庫表格: {tables}")
            
            query = f"SELECT DISTINCT stock_id FROM {self.db_tables['price']} WHERE date <= ?"
            logging.info(f"執行查詢: {query} 參數: {selected_date}")
            cursor.execute(query, (selected_date,))
            all_stocks = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            logging.info(f"找到 {len(all_stocks)} 支股票")
            
            # 初始化結果列表
            results = []
            matching_stocks = []

            # 檢查股票數量
            total_stocks = len(all_stocks)
            if total_stocks == 0:
                self.show_status(f"在 {selected_date} 之前沒有可用的股票數據", is_error=True)
                progress.close()
                return

            # 建立股票名稱映射表
            stock_id_to_name = {}
            for j in range(self.all_stocks_list.count()):
                item_text = self.all_stocks_list.item(j).text()
                parts = item_text.split()
                if len(parts) >= 2:
                    stock_id_to_name[parts[0]] = ' '.join(parts[1:])

            start_time = time.time()

            for idx, stock_id in enumerate(all_stocks, 1):
                # 從映射表獲取股票名稱
                stock_name = stock_id_to_name.get(stock_id, "未知股票")
                
                # 更新進度訊息
                self.update_progress_message(idx, total_stocks, stock_id, stock_name)
                
                # 設置進度對話框文字
                elapsed = time.time() - start_time
                avg_time = elapsed / idx if idx > 0 else 0
                remaining = avg_time * (total_stocks - idx)
                progress.setLabelText(
                    f"正在分析 {stock_id} {stock_name}\n"
                    f"進度: {idx}/{total_stocks} ({idx/total_stocks:.1%})\n"
                    f"已用時間: {elapsed:.1f}秒 | 剩餘時間: {remaining:.1f}秒"
                )
                progress.setValue(int(idx / total_stocks * 100))
                
                if progress.wasCanceled():
                    break
                
                df = self.fetch_strategy_data(stock_id, selected_date)
                if df.empty:
                    if idx < 10:
                        logging.info(f"股票 {stock_id} 沒有數據")
                    continue
                
                # 新增：檢查是否為已下市或無交易股票
                delisting_threshold = 15
                special_delisted_stocks = ['8406']
                if stock_id in special_delisted_stocks:
                    logging.warning(f"股票 {stock_id} 被標記為下市股票，跳過")
                    continue
                try:
                    last_trading_date = df['date'].iloc[-1]
                    if not isinstance(last_trading_date, datetime):
                        last_trading_date = pd.to_datetime(last_trading_date)
                    selected_date_dt = datetime.strptime(selected_date, "%Y-%m-%d")
                    date_diff = (selected_date_dt - last_trading_date).days
                    logging.info(f"股票 {stock_id} 最後交易日: {last_trading_date}, 選擇日期: {selected_date_dt}, 相差天數: {date_diff}")
                    if date_diff > delisting_threshold:
                        logging.warning(f"股票 {stock_id} 已無交易，最後交易日: {last_trading_date}，相差 {date_diff} 天，跳過")
                        continue
                except Exception as e:
                    logging.error(f"處理股票 {stock_id} 日期時出錯: {str(e)}")
                    continue
                
                conditions = self.strategies[current_strategy]
                condition_results = []
                all_matched = True
                
                for condition in conditions:
                    matched, message = self.check_condition(df, condition)
                    condition_results.append({"matched": matched, "message": message})
                    if not matched:
                        all_matched = False
                
                close_price = df.iloc[-1]['Close'] if 'Close' in df.columns else None
                result = {
                    "股票代碼": stock_id,
                    "股票名稱": stock_name,
                    "收盤價": close_price,
                    "條件結果": condition_results
                }
                results.append(result)
                
                if all_matched:
                    matching_stocks.append(stock_id)
            
            self.update_strategy_results(results, matching_stocks, current_strategy, selected_date)
            
            # 最终状态显示
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_stock = total_time / total_stocks if total_stocks > 0 else 0
            self.show_status(
                f"選股完成！共處理 {total_stocks} 支股票\n"
                f"總用時: {total_time:.1f}秒 | 平均每支: {avg_time_per_stock:.2f}秒"
            )
            
            progress.setValue(100)
            self.show_status(f"選股完成: {len(matching_stocks)}/{len(results)} 符合條件")
            
            self.tab_widget.setCurrentIndex(1)
            
        except Exception as e:
            self.show_status(f"執行策略時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
        finally:
            progress.close()

    def get_all_stocks(self):
        """獲取所有股票列表"""
        stocks = []
        try:
            query = "SELECT DISTINCT stock_id, stock_name FROM price ORDER BY stock_id"
            with self.db_connections['price'].cursor() as cursor:
                cursor.execute(query)
                for row in cursor.fetchall():
                    stocks.append((row[0], row[1]))
        except Exception as e:
            self.show_status(f"獲取股票列表失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
        
        return stocks

    def fetch_strategy_data(self, stock_id, date):
        """獲取用於策略計算的股票數據"""
        try:
            query = f"""
            SELECT date, Open, High, Low, Close, Volume
            FROM {self.db_tables['price']}
            WHERE stock_id = ? AND date <= ?
            ORDER BY date DESC
            LIMIT 365
            """
            
            cursor = self.db_connections['price'].cursor()
            cursor.execute(query, (stock_id, date))
            data = cursor.fetchall()
            cursor.close()
            
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data, columns=['date', 'Open', 'High', 'Low', 'Close', 'Volume'])
            
            df['date'] = pd.to_datetime(df['date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df = df.sort_values('date', ascending=True).reset_index(drop=True)
            
            df = self.calculate_indicators(df)
            
            return df
        
        except Exception as e:
            logging.error(f"獲取股票數據時出錯: {str(e)}")
            logging.error(traceback.format_exc())
            return pd.DataFrame()

    def check_condition(self, df, condition):
        """檢查股票是否符合指定條件"""
        try:
            condition_type = condition.get("type", "")
            logging.debug(f"檢查條件: {condition_type}")
            
            if df.empty:
                return False, "無數據"
            
            if condition_type == "ma_position":
                ma = condition.get("ma", "MA5")
                position = condition.get("position", "above")
                price_type = condition.get("price", "close")
                
                if ma not in df.columns:
                    return False, f"{ma}不存在"
                
                price = df.iloc[-1]['Close'] if price_type.lower() == 'close' else df.iloc[-1][price_type]
                ma_value = df.iloc[-1][ma]
                
                if position == "above":
                    matched = price > ma_value
                    message = f"收盤價 {price:.2f} {'>' if matched else '<='} {ma} {ma_value:.2f}"
                    return matched, message
                else:
                    matched = price < ma_value
                    message = f"收盤價 {price:.2f} {'<' if matched else '>='} {ma} {ma_value:.2f}"
                    return matched, message
            
            elif condition_type == "ma_trend":
                ma = condition.get("ma", "MA5")
                trend = condition.get("trend", "up")
                days = condition.get("days", 1)
                
                if ma not in df.columns:
                    return False, f"{ma}不存在"
                
                if len(df) < days + 1:
                    return False, f"數據不足{days+1}天"
                
                current_ma = df.iloc[-1][ma]
                prev_ma = df.iloc[-1-days][ma]
                
                if ma == "MA240" and trend == "up":
                    if len(df) < 2:
                        return False, "數據不足2天，無法判斷趨勢"
                    
                    current_ma240 = df.iloc[-1][ma]
                    prev_ma240 = df.iloc[-2][ma]
                    
                    matched = current_ma240 > prev_ma240
                    message = f"{ma} {current_ma240:.2f} {'>' if matched else '<='} 昨日 {prev_ma240:.2f}"
                    return matched, message
                else:
                    if trend == "up":
                        matched = current_ma > prev_ma
                        message = f"{ma} {current_ma:.2f} {'>' if matched else '<='} {days}日前 {prev_ma:.2f}"
                        return matched, message
                    else:
                        matched = current_ma < prev_ma
                        message = f"{ma} {current_ma:.2f} {'<' if matched else '>='} {days}日前 {prev_ma:.2f}"
                        return matched, message
            
            elif condition_type == "volume_increase":
                days = condition.get("days", 5)
                percent = condition.get("percent", 20)
                
                if len(df) < days:
                    return False, f"數據不足{days}天"
                
                current_vol = df.iloc[-1]['Volume']
                avg_vol = df.iloc[-days:-1]['Volume'].mean()
                
                increase = (current_vol / avg_vol - 1) * 100 if avg_vol > 0 else 0
                matched = increase >= percent
                
                message = f"成交量增加 {increase:.2f}% {'≥' if matched else '<'} {percent}%"
                return matched, message
            
            elif condition_type == "macd":
                signal = condition.get("signal", "golden_cross")
                
                if 'MACD' not in df.columns or 'MACDSignal' not in df.columns:
                    return False, "MACD指標不存在"
                
                if len(df) < 2:
                    return False, "數據不足"
                
                current_macd = df.iloc[-1]['MACD']
                current_signal = df.iloc[-1]['MACDSignal']
                prev_macd = df.iloc[-2]['MACD']
                prev_signal = df.iloc[-2]['MACDSignal']
                
                if signal == "golden_cross":
                    matched = prev_macd < prev_signal and current_macd > current_signal
                    message = f"MACD金叉" if matched else "無MACD金叉"
                    return matched, message
                elif signal == "death_cross":
                    matched = prev_macd > prev_signal and current_macd < current_signal
                    message = f"MACD死叉" if matched else "無MACD死叉"
                    return matched, message
                elif signal == "above_zero":
                    matched = current_macd > 0
                    message = f"MACD {current_macd:.4f} {'>' if matched else '<='} 0"
                    return matched, message
            
            elif condition_type == "rsi":
                period = condition.get("period", 14)
                min_val = condition.get("min", 30)
                max_val = condition.get("max", 70)
                position = condition.get("position", "oversold")
                
                rsi_col = f'RSI{period}'
                if rsi_col not in df.columns:
                    return False, f"{rsi_col}指標不存在"
                
                current_rsi = df.iloc[-1][rsi_col]
                
                if position == "oversold":
                    matched = current_rsi <= min_val
                    message = f"{rsi_col} {current_rsi:.2f} {'≤' if matched else '>'} {min_val}"
                    return matched, message
                elif position == "overbought":
                    matched = current_rsi >= max_val
                    message = f"{rsi_col} {current_rsi:.2f} {'≥' if matched else '<'} {max_val}"
                    return matched, message
                elif position == "middle":
                    matched = min_val <= current_rsi <= max_val
                    message = f"{min_val} {'<=' if matched else '>'} {rsi_col} {current_rsi:.2f} {'<=' if matched else '>'} {max_val}"
                    return matched, message
            
            elif condition_type == "rsi_combined":
                rsi_long = condition.get("rsi_long", 13)
                rsi_long_threshold = condition.get("rsi_long_threshold", 50)
                rsi_short = condition.get("rsi_short", 6)
                rsi_short_threshold = condition.get("rsi_short_threshold", 70)
                
                rsi_long_col = f'RSI{rsi_long}'
                rsi_short_col = f'RSI{rsi_short}'
                
                if rsi_long_col not in df.columns:
                    return False, f"{rsi_long_col}指標不存在"
                if rsi_short_col not in df.columns:
                    return False, f"{rsi_short_col}指標不存在"
                
                current_rsi_long = df.iloc[-1][rsi_long_col]
                current_rsi_short = df.iloc[-1][rsi_short_col]
                
                long_matched = current_rsi_long > rsi_long_threshold
                short_matched = current_rsi_short > rsi_short_threshold
                all_matched = long_matched and short_matched
                
                message = f"{rsi_long_col} {current_rsi_long:.2f} {'>' if long_matched else '<='} {rsi_long_threshold} 且 "
                message += f"{rsi_short_col} {current_rsi_short:.2f} {'>' if short_matched else '<='} {rsi_short_threshold}"
                
                return all_matched, message
            
            elif condition_type == "volume_value":
                min_value = condition.get("min_value", 10000000)
                
                if 'Close' not in df.columns or 'Volume' not in df.columns:
                    return False, "缺少股價或成交量數據"
                
                current_close = df.iloc[-1]['Close']
                current_volume = df.iloc[-1]['Volume']
                volume_value = current_close * current_volume
                
                matched = volume_value >= min_value
                formatted_value = f"{volume_value/10000:.2f}萬"
                formatted_min = f"{min_value/10000:.2f}萬"
                
                message = f"成交額 {formatted_value} {'≥' if matched else '<'} {formatted_min}"
                return matched, message
            
            elif condition_type == "volume_ma_cross":
                short_period = condition.get("short_period", 3)
                long_period = condition.get("long_period", 18)
                days_to_check = condition.get("days", 3)
                
                short_ma_col = f'Volume_MA{short_period}'
                long_ma_col = f'Volume_MA{long_period}'
                
                if short_ma_col not in df.columns:
                    if 'Volume' in df.columns:
                        df[short_ma_col] = df['Volume'].rolling(window=short_period).mean()
                        logging.info(f"動態計算了 {short_ma_col}")
                    else:
                        return False, f"{short_ma_col}指標不存在"
                    
                if long_ma_col not in df.columns:
                    if 'Volume' in df.columns:
                        df[long_ma_col] = df['Volume'].rolling(window=long_period).mean()
                        logging.info(f"動態計算了 {long_ma_col}")
                    else:
                        return False, f"{long_ma_col}指標不存在"
                
                if len(df) < days_to_check + 1:
                    return False, f"數據不足{days_to_check}天"
                
                cross_found = False
                cross_day = None
                
                for i in range(1, min(days_to_check+1, len(df))):
                    idx = -i
                    prev_idx = idx - 1
                    if prev_idx < -len(df):
                        continue
                    
                    if (df.iloc[prev_idx][short_ma_col] <= df.iloc[prev_idx][long_ma_col] and 
                        df.iloc[idx][short_ma_col] > df.iloc[idx][long_ma_col]):
                        cross_found = True
                        cross_day = len(df) + idx
                        break
                
                message = f"量能{short_period}日均線與{long_period}日均線 {'已' if cross_found else '未'} 在{days_to_check}日內黃金交叉"
                if cross_found and cross_day is not None:
                    days_ago = len(df) - cross_day
                    message += f" ({days_ago}日前)"
                
                return cross_found, message
            
            elif condition_type == "ma_trend_or":
                ma1 = condition.get("ma1", "MA60")
                ma2 = condition.get("ma2", "MA20")
                trend = condition.get("trend", "up")
                days = condition.get("days", 1)
                
                if ma1 not in df.columns:
                    return False, f"{ma1}不存在"
                if ma2 not in df.columns:
                    return False, f"{ma2}不存在"
                
                if len(df) < days + 1:
                    return False, f"數據不足{days+1}天"
                
                current_ma1 = df.iloc[-1][ma1]
                prev_ma1 = df.iloc[-1-days][ma1]
                ma1_up = current_ma1 > prev_ma1
                
                current_ma2 = df.iloc[-1][ma2]
                prev_ma2 = df.iloc[-1-days][ma2]
                ma2_up = current_ma2 > prev_ma2
                
                matched = ma1_up or ma2_up
                
                ma1_status = f"{ma1} {current_ma1:.2f} {'>' if ma1_up else '<='} {days}日前 {prev_ma1:.2f}"
                ma2_status = f"{ma2} {current_ma2:.2f} {'>' if ma2_up else '<='} {days}日前 {prev_ma2:.2f}"
                
                message = f"{ma1_status} 或 {ma2_status}"
                
                return matched, message
            
            if condition_type not in ['ma_position', 'ma_trend', 'ma_trend_or', 'volume_increase', 'macd', 'rsi', 'rsi_combined', 'volume_value', 'volume_ma_cross']:
                logging.error(f"不支援的條件類型: {condition_type}")
                return False, f"未支援的條件類型: {condition_type}"
            
        except Exception as e:
            logging.error(f"檢查條件時發生錯誤: {str(e)}")
            logging.error(f"條件內容: {condition}")
            logging.error(traceback.format_exc())
            return False, f"錯誤: {str(e)}"

    def update_strategy_results(self, results, matching_stocks, strategy_name, date):
        """更新策略執行結果"""
        if hasattr(self, 'original_rows_visibility'):
            delattr(self, 'original_rows_visibility')
        
        total = len(results)
        matched = len(matching_stocks)
        
        if total > 0:
            match_percentage = matched / total * 100
        else:
            match_percentage = 0
            
        self.result_summary_label.setText(
            f"策略: {strategy_name}\n"
            f"計算日期: {date}\n"
            f"符合條件的股票: {matched}/{total} ({match_percentage:.2f}%)"
        )
        
        self.filtered_stocks_list.clear()
        
        for result in results:
            if all(cr["matched"] for cr in result["條件結果"]):
                stock_id = result["股票代碼"]
                stock_name = result["股票名稱"]
                close_price = result["收盤價"]
                
                item_text = f"{stock_id} {stock_name} {close_price:.2f}" if close_price else f"{stock_id} {stock_name}"
                item = QListWidgetItem(item_text)
                item.setForeground(QColor(0, 255, 128))
                self.filtered_stocks_list.addItem(item)
        
        for i in range(self.findChildren(QTabWidget)[0].count()):
            if self.findChildren(QTabWidget)[0].tabText(i) == "選股結果":
                self.findChildren(QTabWidget)[0].setCurrentIndex(i)
                break
        
        self.result_table.setRowCount(0)
        self.result_table.setColumnCount(9)
        self.result_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價", "條件1", "條件2", "條件3", "條件4", "條件5", "條件6"])
        
        self.result_table.setStyleSheet("""
            QTableWidget {
                background-color: black;
                color: white;
                gridline-color: #333333;
            }
            QTableWidget::item {
                background-color: black;
                border-bottom: 1px solid #333333;
            }
            QHeaderView::section {
                background-color: #222222;
                color: white;
                padding: 4px;
                border: 1px solid #444444;
            }
        """)
        
        for result in results:
            row = self.result_table.rowCount()
            self.result_table.insertRow(row)
            
            stock_id_item = QTableWidgetItem(result["股票代碼"])
            stock_id_item.setForeground(QColor(255, 255, 255))
            self.result_table.setItem(row, 0, stock_id_item)
            
            stock_name_item = QTableWidgetItem(result["股票名稱"])
            stock_name_item.setForeground(QColor(255, 255, 255))
            self.result_table.setItem(row, 1, stock_name_item)
            
            if result["收盤價"] is not None:
                price_item = QTableWidgetItem(f"{result['收盤價']:.2f}")
                price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                price_item.setForeground(QColor(255, 255, 255))
                self.result_table.setItem(row, 2, price_item)
            else:
                price_item = QTableWidgetItem("N/A")
                price_item.setForeground(QColor(255, 255, 255))
                self.result_table.setItem(row, 2, price_item)
            
            for j, condition_result in enumerate(result["條件結果"]):
                if j < 6:
                    item = QTableWidgetItem(condition_result["message"])
                    if condition_result["matched"]:
                        item.setForeground(QColor(0, 255, 0))
                    else:
                        item.setForeground(QColor(255, 0, 0))
                    self.result_table.setItem(row, j + 3, item)
            
            all_matched = all(cr["matched"] for cr in result["條件結果"])
            if all_matched:
                for col in range(self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item:
                        item.setForeground(QColor(0, 255, 128))
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
        
        self.result_table.resizeColumnsToContents()

    def on_result_double_clicked(self, index):
        """點擊結果表格行時的處理"""
        row = index.row()
        stock_id_item = self.result_table.item(row, 0)
        if stock_id_item:
            stock_id = stock_id_item.text()
            self.tab_widget.setCurrentIndex(0)
            self.plot_stock(stock_id)

    def plot_stock(self, stock_id):
        """繪製指定股票的K線圖"""
        try:
            if not stock_id:
                return
                
            self.show_status(f"載入 {stock_id} 的數據...")
            
            period_text = self.period_combo.currentText()
            days = self.period_map.get(period_text, 44)
            
            extra_days = 240
            df = self.fetch_stock_data(stock_id, days + extra_days)
            
            if df.empty:
                self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
                return
                
            display_df = df.tail(days) if len(df) > days else df
            
            self.chart_view.clear()
            self.volume_view.clear()
            
            stock_name = ""
            for j in range(self.all_stocks_list.count()):
                item_text = self.all_stocks_list.item(j).text()
                if item_text.startswith(stock_id):
                    stock_name = item_text[len(stock_id):].strip()
                    break

            if len(display_df) > 0:
                dates = [dt.strftime('%Y-%m-%d') for dt in display_df['date']]
                xdict = dict(enumerate(dates))
                
                title = f"{stock_id} {stock_name} K線圖 (最後交易日: {dates[-1]})"
                self.chart_view.setTitle(title, color='w', size='16pt')
                
                candlestick = CandlestickItem(display_df)
                self.chart_view.addItem(candlestick)
                
                ma_legend = []
                for ma_days, color, name in [
                    (5, 'w', '週線MA5'), 
                    (20, 'y', '月線MA20'),
                    (60, 'c', '季線MA60'),
                    (120, 'm', '半年線MA120'),
                    (240, 'g', '年線MA240')
                ]:
                    if f'MA{ma_days}' in display_df.columns:
                        ma_series = display_df[f'MA{ma_days}'].dropna()
                        if not ma_series.empty:
                            x = np.array(range(len(display_df) - len(ma_series), len(display_df)))
                            y = ma_series.values
                            ma_plot = self.chart_view.plot(x, y, pen=pg.mkPen(color, width=1), name=name)
                            ma_legend.append((ma_plot, name))
                
                if 'BB_upper' in display_df.columns and 'BB_middle' in display_df.columns and 'BB_lower' in display_df.columns:
                    upper_series = display_df['BB_upper'].dropna()
                    if not upper_series.empty:
                        x = np.array(range(len(display_df) - len(upper_series), len(display_df)))
                        y = upper_series.values
                        upper_plot = self.chart_view.plot(x, y, pen=pg.mkPen('r', width=1, style=Qt.PenStyle.DashLine), name='布林上軌')
                        ma_legend.append((upper_plot, '布林上軌'))
                    
                    middle_series = display_df['BB_middle'].dropna()
                    if not middle_series.empty:
                        x = np.array(range(len(display_df) - len(middle_series), len(display_df)))
                        y = middle_series.values
                        ma_legend.append((ma_legend[1][0], '布林中軌(MA20)'))
                    
                    lower_series = display_df['BB_lower'].dropna()
                    if not lower_series.empty:
                        x = np.array(range(len(display_df) - len(lower_series), len(display_df)))
                        y = lower_series.values
                        lower_plot = self.chart_view.plot(x, y, pen=pg.mkPen('b', width=1, style=Qt.PenStyle.DashLine), name='布林下軌')
                        ma_legend.append((lower_plot, '布林下軌'))
                
                if ma_legend:
                    legend = pg.LegendItem(offset=(50, 10))
                    legend.setParentItem(self.chart_view.graphicsItem())
                    for item, name in ma_legend:
                        legend.addItem(item, name)
                
                self.volume_view.clear()
                volume_data = display_df['Volume'].values / 1000
                
                for i in range(len(display_df)):
                    if df['Close'].iloc[i] > df['Open'].iloc[i]:
                        color = (255, 0, 0, 180)
                    else:
                        color = (0, 255, 0, 180)
                    
                    vol_bar = pg.BarGraphItem(
                        x=[i], height=[volume_data[i]], width=0.8, 
                        brush=color, pen=color
                    )
                    self.volume_view.addItem(vol_bar)
                
                volume_axis = self.volume_view.getAxis('left')
                volume_axis.setLabel('成交量', units='張')
                volume_axis.enableAutoSIPrefix(False)
                
                def volume_axis_formatter(values, scale, spacing):
                    return [(value, f"{int(value):,}") for value in values]
                
                volume_axis.tickFormatter = volume_axis_formatter
                
                self.chart_view.getAxis('bottom').setStyle(showValues=False)
                if len(display_df) > 100:
                    tick_step = len(display_df) // 10
                else:
                    tick_step = max(1, len(display_df) // 5)
                
                ticks = [(i, dates[i]) for i in range(0, len(display_df), tick_step)]
                if len(display_df) - 1 not in [t[0] for t in ticks]:
                    ticks.append((len(display_df) - 1, dates[-1]))
                self.volume_view.getAxis('bottom').setTicks([ticks])
                
                vLine = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('w', width=0.8))
                hLine = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('w', width=0.8))
                self.chart_view.addItem(vLine, ignoreBounds=True)
                self.chart_view.addItem(hLine, ignoreBounds=True)
                
                scatter = pg.ScatterPlotItem(size=10, pen=pg.mkPen('w', width=2), brush=pg.mkBrush(255, 255, 0, 200))
                self.chart_view.addItem(scatter)
                
                label = pg.TextItem(html='', border=pg.mkPen('w', width=1), fill=(0, 0, 0, 200))
                self.chart_view.addItem(label)
                label.hide()
                
                self._last_x = None
                self._last_html = None
                
                def mouseMoved(evt):
                    try:
                        pos = evt[0]
                        if self.chart_view.sceneBoundingRect().contains(pos):
                            mousePoint = self.chart_view.getViewBox().mapSceneToView(pos)
                            
                            x = mousePoint.x()
                            x = min(max(0, x), len(display_df)-1)
                            x_int = int(x)
                            
                            vLine.setPos(x)
                            hLine.setPos(mousePoint.y())
                            vLine.setVisible(True)
                            hLine.setVisible(True)
                            
                            close = display_df['Close'].iloc[x_int]
                            scatter.setData([x_int], [close])
                            
                            if self._last_x != x_int:
                                self._last_x = x_int
                                
                                change_pct = 0.0
                                change_color = "white"
                                if x_int > 0:
                                    prev_close = display_df['Close'].iloc[x_int-1]
                                    change_pct = (close - prev_close) / prev_close * 100
                                    change_color = "red" if change_pct >= 0 else "green"
                                
                                current_volume = display_df['Volume'].iloc[x_int]
                                volume_value = close * current_volume
                                
                                date_str = display_df['date'].iloc[x_int].strftime('%Y-%m-%d')
                                html = f"""
                                <div style='background-color:rgba(0,0,0,200);padding:5px;'>
                                <span style='color:white;font-weight:bold;'>{date_str}</span><br>
                                <span style='color:white;'>開盤: {display_df['Open'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>最高: {display_df['High'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>最低: {display_df['Low'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>收盤: {close:.2f}</span><br>
                                <span style='color:{change_color};'>漲跌幅: {change_pct:+.2f}%</span><br>
                                <span style='color:white;'>成交量: {int(current_volume/1000):,}張</span><br>
                                <span style='color:white;'>成交額: {volume_value/10000:.2f}萬</span><br>
                                """
                                
                                for ma in [5, 20, 60, 120, 240]:
                                    ma_col = f'MA{ma}'
                                    if ma_col in display_df.columns and not pd.isna(display_df[ma_col].iloc[x_int]):
                                        html += f"<span style='color:white;'>{ma_col}: {display_df[ma_col].iloc[x_int]:.2f}</span><br>"
                                
                                for rsi_period in [6, 13]:
                                    rsi_col = f'RSI{rsi_period}'
                                    if rsi_col in display_df.columns and not pd.isna(display_df[rsi_col].iloc[x_int]):
                                        rsi_value = display_df[rsi_col].iloc[x_int]
                                        color = "red" if rsi_value > 70 else "green" if rsi_value < 30 else "white"
                                        html += f"<span style='color:{color};'>{rsi_col}: {rsi_value:.2f}</span><br>"
                                
                                html += "</div>"
                                self._last_html = html
                                label.setHtml(html)
                            
                            label.setAnchor((0, 0))
                            
                            x_pos = x_int - 15 if x_int > len(display_df)/2 else x_int + 5
                            
                            label.setPos(x_pos, mousePoint.y())
                            label.show()
                        else:
                            vLine.setVisible(False)
                            hLine.setVisible(False)
                            scatter.setData([], [])
                            label.hide()
                    except Exception as e:
                        logging.error(f"滑鼠懸停事件錯誤: {str(e)}")
                        logging.error(traceback.format_exc())
                
                self.mouse_proxy = pg.SignalProxy(self.chart_view.scene().sigMouseMoved, rateLimit=60, slot=mouseMoved)
                
                self.setWindowTitle(f"台股智能選股系統 - {stock_id}")
                self.show_status(f"已載入 {stock_id} 的K線圖")
                
                if not display_df.empty:
                    price_min = display_df['Low'].min()
                    price_max = display_df['High'].max()
                    price_range = price_max - price_min
                    price_padding = price_range * 0.15
                    y_min = max(0, price_min - price_padding)
                    y_max = price_max + price_padding
                    self.chart_view.setYRange(y_min, y_max)
                
                if not display_df.empty:
                    vol_max = display_df['Volume'].max() / 1000
                    vol_padding_top = vol_max * 0.2
                    self.volume_view.setYRange(0, vol_max + vol_padding_top)
            else:
                self.show_status(f"無法繪製 {stock_id} 的K線圖：數據不足", is_error=True)
        
        except Exception as e:
            self.show_status(f"繪製K線圖時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def filter_stocks(self):
        """根據搜索框的內容過濾股票列表"""
        search_text = self.search_input.text().lower()
        
        if not search_text:
            for i in range(self.all_stocks_list.count()):
                self.all_stocks_list.item(i).setHidden(False)
            return
        
        for i in range(self.all_stocks_list.count()):
            item = self.all_stocks_list.item(i)
            item_text = item.text().lower()
            item.setHidden(search_text not in item_text)

    def close_db_connections(self):
        """關閉所有數據庫連接"""
        try:
            if hasattr(self, 'db_connections'):
                for db_name, conn in self.db_connections.items():
                    logging.info(f"關閉 {db_name} 數據庫連接")
                    conn.close()
                logging.info("所有數據庫連接已關閉")
        except Exception as e:
            logging.error(f"關閉數據庫連接時出錯: {str(e)}")
            logging.error(traceback.format_exc())

    def calculate_indicators(self, df):
        """計算各種技術指標"""
        if df.empty:
            return df
        
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        for period in [5, 10, 20, 60, 120, 240]:
            ma_col = f'MA{period}'
            df[ma_col] = df['Close'].rolling(window=period).mean()
        
        exp12 = df['Close'].ewm(span=12, adjust=False).mean()
        exp26 = df['Close'].ewm(span=26, adjust=False).mean()
        df['MACD'] = exp12 - exp26
        df['MACDSignal'] = df['MACD'].ewm(span=9, adjust=False).mean()
        df['MACDHist'] = df['MACD'] - df['MACDSignal']
        
        for period in [6, 13, 14, 24]:
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            
            rs = avg_gain / avg_loss
            df[f'RSI{period}'] = 100 - (100 / (1 + rs))
        
        df['BB_middle'] = df['MA20']
        std = df['Close'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (std * 2)
        df['BB_lower'] = df['BB_middle'] - (std * 2)
        
        if 'Volume' in df.columns:
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            df['Volume_MA3'] = df['Volume'].rolling(window=3).mean()
            df['Volume_MA18'] = df['Volume'].rolling(window=18).mean()
        
        return df

    def filter_matching_results(self):
        """只显示所有条件都符合的股票"""
        try:
            if not hasattr(self, 'original_rows_visibility'):
                self.original_rows_visibility = []
                for row in range(self.result_table.rowCount()):
                    self.original_rows_visibility.append(not self.result_table.isRowHidden(row))
            
            for row in range(self.result_table.rowCount()):
                all_matched = True
                for col in range(3, self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item and item.foreground().color().green() < 200:
                        all_matched = False
                        break
                
                self.result_table.setRowHidden(row, not all_matched)
            
            self.show_status("已筛选显示符合所有条件的股票")
        except Exception as e:
            self.show_status(f"筛选结果时出错: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def restore_all_results(self):
        """恢复显示所有股票的结果"""
        try:
            if hasattr(self, 'original_rows_visibility'):
                for row, visible in enumerate(self.original_rows_visibility):
                    if row < self.result_table.rowCount():
                        self.result_table.setRowHidden(row, not visible)
            else:
                for row in range(self.result_table.rowCount()):
                    self.result_table.setRowHidden(row, False)
            
            self.show_status("已恢复显示所有股票结果")
        except Exception as e:
            self.show_status(f"恢复结果显示时出错: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

    def update_progress_message(self, current, total, stock_id, stock_name):
        """更新进度信息"""
        percent = current / total * 100
        msg = f"正在處理 {current}/{total} ({percent:.1f}%) - {stock_id} {stock_name}"
        self.show_status(msg)
        QApplication.processEvents()  # 强制更新UI

def main():
    db_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db')
    os.makedirs(db_folder, exist_ok=True)
      
    price_db_path = os.path.join(db_folder, 'price.db')
    if not os.path.exists(price_db_path):
        print(f"警告: 價格數據庫文件不存在: {price_db_path}")
        print("請確保您已經導入了股價數據")
        
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                date TEXT,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Volume REAL,
                PRIMARY KEY (stock_id, date)
            )
            ''')
            conn.commit()
            conn.close()
            print("已創建空的價格數據庫，但您需要導入數據才能進行選股")
        except Exception as e:
            print(f"創建數據庫文件失敗: {str(e)}")
            print(f"請檢查路徑 {db_folder} 是否存在並且有寫入權限")
    
    app = QApplication(sys.argv)
    win = StockScreenerGUI()
    win.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
