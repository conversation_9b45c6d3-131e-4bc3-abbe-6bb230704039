# u02_crawlers.py 使用說明

## 📋 概述

`u02_crawlers.py` 是從 Jupyter notebook (`u02_crawlers.ipynb`) 轉換而來的 Python 腳本，提供了 Finlab 新版資料庫的爬蟲功能。

## ✨ 新功能特色

- ✅ 殖利率資料爬取
- ✅ 三大法人資料爬取  
- ✅ 上櫃所有股票資料
- ✅ 更快速的存取方式
- ✅ 命令列互動介面
- ✅ 自動依賴檢查
- ✅ 簡化版備用功能

## 🚀 快速開始

### 1. 執行腳本
```bash
python u02_crawlers.py
```

### 2. 選擇爬蟲功能
程式會顯示選單，您可以選擇以下功能：

1. **股價資料** (price) - 爬取股票價格資訊
2. **三大法人** (bargin_report) - 爬取三大法人買賣資料
3. **本益比** (pe) - 爬取本益比資料
4. **月營收** (monthly_report) - 爬取月營收資料
5. **大盤指數** (benchmark) - 爬取大盤指數資料
6. **財務報表** (financial_statement) - 爬取財務報表資料
7. **上市除權息** (twse_divide_ratio) - 爬取上市除權息資料
8. **上櫃除權息** (otc_divide_ratio) - 爬取上櫃除權息資料
9. **上市減資** (twse_cap_reduction) - 爬取上市減資資料
10. **上櫃減資** (otc_cap_reduction) - 爬取上櫃減資資料

### 3. 退出程式
輸入 `q` 退出程式

## 📦 依賴套件

### 必要套件
- `pandas` - 資料處理
- `requests` - HTTP 請求
- `lxml` - XML/HTML 解析

### 可選套件
- `finlab` - 完整的 Finlab 功能（推薦）

## 🔧 安裝依賴

### 安裝基本依賴
```bash
pip install pandas requests lxml
```

### 安裝完整 Finlab 套件（推薦）
```bash
pip install finlab
```

## 💡 使用模式

### 模式 1: 完整功能模式
如果已安裝 `finlab` 套件，程式會使用完整的爬蟲功能，可以實際爬取資料。

### 模式 2: 簡化功能模式
如果未安裝 `finlab` 套件，程式會使用簡化版功能，提供模擬的爬蟲操作（用於測試和演示）。

## 📊 資料範圍

- **股價、三大法人、本益比、大盤指數**: 預設爬取最近30天資料
- **月營收**: 預設爬取最近3個月資料
- **財務報表**: 預設爬取最近2季資料
- **除權息、減資**: 執行全量更新

## 🔍 執行範例

```bash
$ python u02_crawlers.py
🔍 測試爬蟲模組...
⚠️ 未安裝 finlab 套件，將使用簡化版功能
💡 如需完整功能，請安裝: pip install finlab

🚀 啟動爬蟲系統...
2025-07-20 15:46:39,163 - INFO - 🔧 在一般 Python 環境中運行，提供命令列介面

============================================================
🚀 Finlab 新版資料庫爬蟲系統
============================================================

可用的爬蟲功能：
1. 股價資料 (price)
2. 三大法人 (bargin_report)
3. 本益比 (pe)
4. 月營收 (monthly_report)
5. 大盤指數 (benchmark)
6. 財務報表 (financial_statement)
7. 上市除權息 (twse_divide_ratio)
8. 上櫃除權息 (otc_divide_ratio)
9. 上市減資 (twse_cap_reduction)
10. 上櫃減資 (otc_cap_reduction)

----------------------------------------
請選擇要執行的爬蟲 (1-10, 或 'q' 退出): 1

🔄 開始執行 股價資料 爬蟲...
📅 使用預設日期範圍（最近30天）
📊 模擬爬取股價資料 (2025-06-20 ~ 2025-07-20)
✅ 股價資料 爬蟲執行完成
📊 獲取資料筆數: 1 筆
```

## 🛠️ 技術特色

### 自動依賴檢查
程式會自動檢查必要的依賴套件，並提供安裝建議。

### 智能模式切換
- 優先使用完整的 `finlab` 套件
- 自動降級到簡化版功能
- 保持程式的可用性

### 友好的使用者介面
- 清晰的選單系統
- 詳細的執行狀態顯示
- 錯誤處理和提示

## 📝 注意事項

1. **網路連線**: 爬蟲功能需要穩定的網路連線
2. **資料來源**: 資料來自台灣證券交易所等官方網站
3. **使用限制**: 請遵守資料來源網站的使用條款
4. **資料準確性**: 建議與官方資料進行交叉驗證

## 🔄 從 Jupyter Notebook 轉換

此腳本是從 `u02_crawlers.ipynb` 轉換而來，主要改進：

- ✅ 支援命令列執行
- ✅ 自動依賴管理
- ✅ 錯誤處理增強
- ✅ 使用者體驗優化
- ✅ 跨平台相容性

## 🆘 故障排除

### 問題 1: 模組導入失敗
```
❌ 缺少必要依賴: pandas, requests, lxml
```
**解決方案**: 安裝缺少的套件
```bash
pip install pandas requests lxml
```

### 問題 2: 網路連線問題
**解決方案**: 檢查網路連線，或稍後重試

### 問題 3: 資料爬取失敗
**解決方案**: 
1. 確認目標網站是否正常
2. 檢查是否有反爬蟲限制
3. 嘗試稍後重新執行

## 📞 支援

如有問題或建議，請檢查：
1. 依賴套件是否正確安裝
2. 網路連線是否正常
3. Python 版本是否相容（建議 Python 3.7+）

---

**轉換完成日期**: 2025-07-20  
**原始檔案**: u02_crawlers.ipynb  
**轉換版本**: u02_crawlers.py v1.0
