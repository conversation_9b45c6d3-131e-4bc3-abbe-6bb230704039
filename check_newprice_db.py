#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 檔案的內容和結構
"""

import sqlite3
import pandas as pd
import os

def check_newprice_db():
    """檢查 newprice.db 檔案"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    
    print("=" * 80)
    print("🔍 檢查 newprice.db 檔案")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查檔案大小
    file_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
    print(f"📁 檔案位置: {db_file}")
    print(f"📊 檔案大小: {file_size:.2f} MB")
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查表格列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 資料庫表格: {[table[0] for table in tables]}")
        
        # 檢查 price 表格結構
        cursor.execute("PRAGMA table_info(price)")
        columns = cursor.fetchall()
        print(f"\n📊 表格結構 (price):")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM price")
        total_count = cursor.fetchone()[0]
        print(f"\n📈 總資料筆數: {total_count:,}")
        
        # 檢查股票數量
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        unique_stocks = cursor.fetchone()[0]
        print(f"📈 不重複股票數: {unique_stocks:,}")
        
        # 檢查日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        date_range = cursor.fetchone()
        print(f"📅 日期範圍: {date_range[0][:10]} ~ {date_range[1][:10]}")
        
        # 顯示前10筆資料
        print(f"\n📋 前10筆資料:")
        cursor.execute("SELECT stock_id, stock_name, date, 收盤價, 開盤價, 最高價, 最低價, 成交股數 FROM price LIMIT 10")
        rows = cursor.fetchall()

        # 建立 DataFrame 以便更好地顯示
        df_sample = pd.DataFrame(rows, columns=['股票代碼', '股票名稱', '日期', '收盤價', '開盤價', '最高價', '最低價', '成交股數'])
        print(df_sample.to_string(index=False))
        
        # 檢查各股票的資料筆數
        print(f"\n📊 各股票資料筆數統計 (前10名):")
        cursor.execute("""
            SELECT stock_id, COUNT(*) as data_count 
            FROM price 
            GROUP BY stock_id 
            ORDER BY data_count DESC 
            LIMIT 10
        """)
        stock_stats = cursor.fetchall()
        
        for stock_id, count in stock_stats:
            print(f"   {stock_id}: {count:,} 筆")
        
        # 檢查年度分布
        print(f"\n📊 年度資料分布:")
        cursor.execute("""
            SELECT strftime('%Y', date) as year, COUNT(*) as count 
            FROM price 
            GROUP BY year 
            ORDER BY year DESC
        """)
        year_stats = cursor.fetchall()
        
        for year, count in year_stats:
            print(f"   {year}: {count:,} 筆")
        
        # 檢查最近的資料
        print(f"\n📊 最近的股價資料 (前5筆):")
        cursor.execute("""
            SELECT stock_id, stock_name, date, 收盤價, 開盤價, 最高價, 最低價, 成交股數
            FROM price
            ORDER BY date DESC, stock_id
            LIMIT 5
        """)
        recent_data = cursor.fetchall()

        for stock_id, stock_name, date, close, open_price, high, low, volume in recent_data:
            print(f"   {date[:10]} - {stock_id} {stock_name}: 收盤 {close}, 開盤 {open_price}, 高 {high}, 低 {low}, 量 {volume}")
        
        # 檢查資料完整性
        print(f"\n📊 資料完整性檢查:")
        
        # 檢查空值
        cursor.execute("SELECT COUNT(*) FROM price WHERE 收盤價 IS NULL OR 收盤價 = ''")
        null_close = cursor.fetchone()[0]
        print(f"   收盤價空值: {null_close:,} 筆")
        
        cursor.execute("SELECT COUNT(*) FROM price WHERE 成交股數 IS NULL OR 成交股數 = ''")
        null_volume = cursor.fetchone()[0]
        print(f"   成交股數空值: {null_volume:,} 筆")
        
        # 檢查資料類型範例
        print(f"\n📊 資料類型範例:")
        cursor.execute("SELECT 收盤價, 開盤價, 最高價, 最低價, 成交股數 FROM price WHERE 收盤價 IS NOT NULL LIMIT 1")
        sample = cursor.fetchone()
        if sample:
            print(f"   收盤價: {sample[0]} (type: {type(sample[0])})")
            print(f"   開盤價: {sample[1]} (type: {type(sample[1])})")
            print(f"   最高價: {sample[2]} (type: {type(sample[2])})")
            print(f"   最低價: {sample[3]} (type: {type(sample[3])})")
            print(f"   成交股數: {sample[4]} (type: {type(sample[4])})")
        
        # 檢查索引效能
        print(f"\n📊 查詢效能測試:")
        import time
        
        # 測試按股票代碼查詢
        start_time = time.time()
        cursor.execute("SELECT COUNT(*) FROM price WHERE stock_id = '2330 台積電'")
        result = cursor.fetchone()[0]
        query_time = time.time() - start_time
        print(f"   按股票代碼查詢 (2330 台積電): {result:,} 筆, 耗時 {query_time:.3f} 秒")
        
        # 測試按日期查詢
        start_time = time.time()
        cursor.execute("SELECT COUNT(*) FROM price WHERE date >= '2022-01-01'")
        result = cursor.fetchone()[0]
        query_time = time.time() - start_time
        print(f"   按日期查詢 (2022年後): {result:,} 筆, 耗時 {query_time:.3f} 秒")
        
        conn.close()
        
        print(f"\n✅ 檢查完成！newprice.db 檔案結構正常")
        return True
        
    except Exception as e:
        print(f"❌ 檢查過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_newprice_db()
