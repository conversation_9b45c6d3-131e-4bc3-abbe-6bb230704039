#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於 R 語言版本轉換的 MOPS 財務報表爬蟲 (Python 版本)
參考: https://github.com/SuYenTing/Quantitative_investment_material_in_R
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
from lxml import html
import time
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class MOPSFinancialScraper:
    """MOPS 財務報表爬蟲 (基於 R 語言版本)"""
    
    def __init__(self):
        self.base_url = "http://mops.twse.com.tw/server-java/t164sb01"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def get_financial_statements(self, stock_code: str, year: int, season: int) -> Dict[str, pd.DataFrame]:
        """
        獲取指定股票的三大財務報表
        
        Args:
            stock_code: 股票代碼 (如 '2330')
            year: 年度 (如 2024)
            season: 季別 (1-4)
        
        Returns:
            包含三大財務報表的字典
        """
        logger.info(f"🔍 獲取 {stock_code} {year}年第{season}季財務報表...")
        
        try:
            # 構建 URL (參考 R 語言版本)
            url = f"{self.base_url}?step=1&CO_ID={stock_code}&SYEAR={year}&SSEASON={season}&REPORT_ID=C"
            logger.info(f"📡 請求 URL: {url}")
            
            # 發送請求
            response = self.session.get(url, timeout=30)
            
            # 設置編碼 (參考 R 語言版本的 Big5 處理)
            response.encoding = 'big5'
            
            if response.status_code != 200:
                logger.error(f"❌ HTTP 錯誤: {response.status_code}")
                return {}
            
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            tree = html.fromstring(response.content.decode('big5'))
            
            # 獲取三大財務報表
            results = {}
            
            # 1. 資產負債表
            balance_sheet = self._extract_balance_sheet(tree)
            if balance_sheet is not None:
                results['資產負債表'] = balance_sheet
                logger.info(f"✅ 資產負債表: {balance_sheet.shape}")
            
            # 2. 綜合損益表
            income_statement = self._extract_income_statement(tree)
            if income_statement is not None:
                results['綜合損益表'] = income_statement
                logger.info(f"✅ 綜合損益表: {income_statement.shape}")
            
            # 3. 現金流量表
            cash_flow = self._extract_cash_flow(tree)
            if cash_flow is not None:
                results['現金流量表'] = cash_flow
                logger.info(f"✅ 現金流量表: {cash_flow.shape}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 獲取財務報表失敗: {e}")
            return {}
    
    def _extract_balance_sheet(self, tree) -> Optional[pd.DataFrame]:
        """提取資產負債表 (參考 R 語言版本)"""
        try:
            # XPath: //table[@class='result_table hasBorder']//tr[@class='tblHead'][1]/th
            headers = tree.xpath("//table[@class='result_table hasBorder']//tr[@class='tblHead'][1]/th/text()")
            
            # XPath: //table[@class='result_table hasBorder']//tr/td
            data = tree.xpath("//table[@class='result_table hasBorder']//tr/td/text()")
            
            if not headers or not data:
                logger.warning("⚠️ 資產負債表: 無法找到資料")
                return None
            
            # 重新整理資料為矩陣 (參考 R 語言的 matrix(ncol=4, byrow=T))
            num_cols = len(headers)
            if len(data) % num_cols != 0:
                logger.warning(f"⚠️ 資產負債表: 資料長度不匹配 ({len(data)} % {num_cols} != 0)")
                return None
            
            # 轉換為 DataFrame
            rows = [data[i:i+num_cols] for i in range(0, len(data), num_cols)]
            df = pd.DataFrame(rows, columns=headers)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 提取資產負債表失敗: {e}")
            return None
    
    def _extract_income_statement(self, tree) -> Optional[pd.DataFrame]:
        """提取綜合損益表 (參考 R 語言版本)"""
        try:
            # XPath: //table[@class='main_table hasBorder'][1]//tr[@class='tblHead'][1]/th
            headers = tree.xpath("//table[@class='main_table hasBorder'][1]//tr[@class='tblHead'][1]/th/text()")
            
            # XPath: //table[@class='main_table hasBorder'][1]//tr/td
            data = tree.xpath("//table[@class='main_table hasBorder'][1]//tr/td/text()")
            
            if not headers or not data:
                logger.warning("⚠️ 綜合損益表: 無法找到資料")
                return None
            
            # 重新整理資料為矩陣 (參考 R 語言的 matrix(ncol=5, byrow=T))
            num_cols = len(headers)
            if len(data) % num_cols != 0:
                logger.warning(f"⚠️ 綜合損益表: 資料長度不匹配 ({len(data)} % {num_cols} != 0)")
                return None
            
            # 轉換為 DataFrame
            rows = [data[i:i+num_cols] for i in range(0, len(data), num_cols)]
            df = pd.DataFrame(rows, columns=headers)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 提取綜合損益表失敗: {e}")
            return None
    
    def _extract_cash_flow(self, tree) -> Optional[pd.DataFrame]:
        """提取現金流量表 (參考 R 語言版本)"""
        try:
            # XPath: //table[@class='main_table hasBorder'][2]//tr[@class='tblHead'][1]/th
            headers = tree.xpath("//table[@class='main_table hasBorder'][2]//tr[@class='tblHead'][1]/th/text()")
            
            # XPath: //table[@class='main_table hasBorder'][2]//tr/td
            data = tree.xpath("//table[@class='main_table hasBorder'][2]//tr/td/text()")
            
            if not headers or not data:
                logger.warning("⚠️ 現金流量表: 無法找到資料")
                return None
            
            # 重新整理資料為矩陣 (參考 R 語言的 matrix(ncol=3, byrow=T))
            num_cols = len(headers)
            if len(data) % num_cols != 0:
                logger.warning(f"⚠️ 現金流量表: 資料長度不匹配 ({len(data)} % {num_cols} != 0)")
                return None
            
            # 轉換為 DataFrame
            rows = [data[i:i+num_cols] for i in range(0, len(data), num_cols)]
            df = pd.DataFrame(rows, columns=headers)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 提取現金流量表失敗: {e}")
            return None

def test_mops_scraper():
    """測試 MOPS 爬蟲"""
    
    print("=" * 80)
    print("🧪 測試 MOPS 財務報表爬蟲 (Python 版本)")
    print("=" * 80)
    
    scraper = MOPSFinancialScraper()
    
    # 測試參數 (參考 R 語言版本)
    test_cases = [
        ("2330", 2024, 1),  # 台積電 2024Q1
        ("2317", 2024, 1),  # 鴻海 2024Q1
        ("2454", 2023, 4),  # 聯發科 2023Q4
    ]
    
    for stock_code, year, season in test_cases:
        print(f"\n📊 測試 {stock_code} {year}年第{season}季...")
        
        results = scraper.get_financial_statements(stock_code, year, season)
        
        if results:
            print(f"✅ 成功獲取 {len(results)} 個財務報表:")
            
            for statement_type, df in results.items():
                print(f"   📋 {statement_type}: {df.shape[0]} 行 x {df.shape[1]} 列")
                
                # 顯示前幾行資料
                if not df.empty:
                    print(f"   📈 欄位: {list(df.columns)}")
                    print(f"   📊 前3行資料:")
                    for i, (_, row) in enumerate(df.head(3).iterrows()):
                        print(f"      {i+1}. {dict(row)}")
        else:
            print(f"❌ 無法獲取財務報表")
        
        # 避免請求過於頻繁
        time.sleep(2)

def compare_with_current_system():
    """與現有系統比較"""
    
    print(f"\n" + "=" * 80)
    print("🔍 與現有系統比較")
    print("=" * 80)
    
    print("📊 現有系統 vs MOPS Python 爬蟲:")
    print()
    
    print("✅ **現有系統優勢**:")
    print("   - TWSE OpenAPI: 官方支援，穩定可靠")
    print("   - 1,008家上市公司覆蓋")
    print("   - 統一的日期格式和資料結構")
    print()
    
    print("🎯 **MOPS Python 爬蟲優勢**:")
    print("   - 完整的三大財務報表")
    print("   - 基於成功的 R 語言版本")
    print("   - 使用 HTTP 避免 SSL 問題")
    print("   - 精確的 XPath 選擇器")
    print()
    
    print("💡 **建議整合方案**:")
    print("   1. 主要使用 TWSE OpenAPI (損益表+資產負債表)")
    print("   2. 使用 MOPS Python 爬蟲補充現金流量表")
    print("   3. 建立混合架構，發揮各自優勢")

def main():
    """主函數"""
    
    print("🐍 MOPS 財務報表爬蟲 (Python 版本)")
    print("基於 R 語言版本轉換")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試爬蟲
    test_mops_scraper()
    
    # 與現有系統比較
    compare_with_current_system()
    
    print(f"\n" + "=" * 80)
    print("🎉 MOPS Python 爬蟲測試完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
