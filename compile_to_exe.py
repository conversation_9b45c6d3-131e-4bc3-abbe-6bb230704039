#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台股智能選股系統編譯腳本
將 O3mh_gui_v21_optimized.py 編譯為可執行檔
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """檢查必要的依賴項"""
    print("🔍 檢查依賴項...")

    # 定義包名和實際導入名的映射
    package_imports = {
        'PyQt6': 'PyQt6.QtCore',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'pyqtgraph': 'pyqtgraph',
        'requests': 'requests',
        'beautifulsoup4': 'bs4',
        'selenium': 'selenium',
        'pyinstaller': 'PyInstaller'
    }

    missing_packages = []

    for package, import_name in package_imports.items():
        try:
            __import__(import_name)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")

    if missing_packages:
        print(f"\n⚠️ 缺少以下依賴項: {', '.join(missing_packages)}")
        print("請執行以下命令安裝:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依賴項檢查完成")
    return True

def create_spec_file():
    """創建 PyInstaller 規格文件"""
    print("📝 創建 PyInstaller 規格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有必要的數據文件
datas = [
    ('config', 'config'),
    ('strategies', 'strategies'),
    ('charts', 'charts'),
    ('dialogs', 'dialogs'),
    ('core', 'core'),
    ('data', 'data'),
    ('gui', 'gui'),
    ('monitoring', 'monitoring'),
    ('finlab', 'finlab'),
    ('finlab_analysis', 'finlab_analysis'),
    ('finlab_integration', 'finlab_integration'),
    ('*.json', '.'),
    ('*.yaml', '.'),
    ('*.txt', '.'),
]

# 隱藏導入的模組
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'pyqtgraph',
    'pandas',
    'numpy',
    'sqlite3',
    'requests',
    'beautifulsoup4',
    'selenium',
    'webdriver_manager',
    'yfinance',
    'matplotlib',
    'seaborn',
    'openpyxl',
    'xlsxwriter',
    'schedule',
    'tqdm',
    'psutil',
    'cryptography',
    'sqlalchemy',
    'finlab',
    'finlab.data',
    'finlab.backtest',
    'finlab.crawler',
    'stock_filter',
    'enhanced_market_scanner',
    'real_market_data_fetcher',
    'smart_trading_strategies',
    'roe_data_crawler',
    'goodinfo_roe_csv_downloader',
    'market_data_crawler',
    'unified_monitor_manager',
    'auto_update',
    'enhanced_data_fetcher',
    'finmind_data_provider',
    'pe_data_provider',
    'real_data_sources',
    'safe_market_scanner',
    'optimized_market_scanner',
    'intraday_data_fetcher',
    'smart_yahoo_fetcher',
    'twelvedata_fetcher',
    'enhanced_yfinance_fetcher',
    'finmind_quota_manager',
    'stock_name_mapping',
    'strategy_intersection_analyzer',
    'multi_strategy_chart',
    'stock_basic_info_crawler',
    'google_stock_news_gui',
    'google_news_fast',
    'google_real_time_news',
    'improved_roe_downloader',
    'roe_data_downloader_gui',
    'twse_market_data_dialog',
    'market_data_crawler_dialog',
    'unified_monthly_revenue_gui',
    'ashui_backtest_gui',
    'ashui_backtest_system',
    'ai_technical_backtest',
    'multi_strategy_gui',
    'auto_rule_discovery_gui',
    'dividend_trading_gui',
    'dividend_trading_system',
    'u02_crawlers_gui',
    'mops_income_statement_gui',
    'monthly_revenue_downloader_gui',
    'stock_basic_info_gui',
    'finlab_tables_gui',
    'finlab_crawler_gui',
    'daily_trading_crawler_gui',
]

# 排除不需要的模組
excludes = [
    'tkinter',
    'test',
    'unittest',
    'pdb',
    'doctest',
    'difflib',
    'inspect',
    'pydoc',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PySide2',
    'PySide6',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('台股智能選股系統.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 規格文件創建完成")

def compile_executable():
    """編譯可執行檔"""
    print("🔨 開始編譯可執行檔...")
    
    # 清理舊的編譯文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理舊的 build 目錄")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理舊的 dist 目錄")
    
    # 執行 PyInstaller
    try:
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            '台股智能選股系統.spec'
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            print(f"📁 可執行檔位置: {os.path.abspath('dist/台股智能選股系統.exe')}")
            return True
        else:
            print("❌ 編譯失敗！")
            print("錯誤輸出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_portable_package():
    """創建便攜版套件"""
    print("📦 創建便攜版套件...")
    
    if not os.path.exists('dist/台股智能選股系統.exe'):
        print("❌ 找不到編譯後的可執行檔")
        return False
    
    # 創建便攜版目錄
    portable_dir = 'dist/台股智能選股系統_便攜版'
    os.makedirs(portable_dir, exist_ok=True)
    
    # 複製可執行檔
    shutil.copy2('dist/台股智能選股系統.exe', portable_dir)
    
    # 複製必要的配置文件和資料夾
    copy_items = [
        'config',
        'strategies', 
        'charts',
        'dialogs',
        'core',
        'data',
        'gui',
        'monitoring',
        'finlab',
        'finlab_analysis',
        'finlab_integration',
        'app_config.json',
        'database_config.json',
        'config.yaml',
        'strategies.json',
        'requirements_complete.txt',
        'README.md',
        'GUI_使用說明.md'
    ]
    
    for item in copy_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(portable_dir, item), dirs_exist_ok=True)
            else:
                shutil.copy2(item, portable_dir)
            print(f"✅ 複製: {item}")
    
    # 創建使用說明
    readme_content = """# 台股智能選股系統 - 便攜版

## 🚀 快速開始

1. 直接執行 `台股智能選股系統.exe`
2. 首次運行會自動創建必要的資料庫和配置文件
3. 建議將整個資料夾放在固定位置使用

## 📁 目錄結構

- `台股智能選股系統.exe` - 主程式
- `config/` - 配置文件
- `strategies/` - 策略定義
- `data/` - 數據文件
- `history/` - 歷史數據（運行後自動創建）
- `logs/` - 日誌文件（運行後自動創建）

## ⚠️ 注意事項

1. 首次運行可能需要較長時間初始化
2. 確保網路連接正常以獲取最新數據
3. 建議定期備份 `history/` 目錄中的數據

## 🔧 故障排除

如果遇到問題，請檢查：
1. Windows Defender 是否阻擋程式運行
2. 防毒軟體是否誤報
3. 是否有足夠的磁碟空間

## 📞 技術支援

如有問題請參考完整的使用說明文檔。
"""
    
    with open(os.path.join(portable_dir, '使用說明.txt'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 便攜版套件創建完成: {os.path.abspath(portable_dir)}")
    return True

def main():
    """主函數"""
    print("🚀 台股智能選股系統編譯工具")
    print("=" * 50)
    
    # 檢查當前目錄
    if not os.path.exists('O3mh_gui_v21_optimized.py'):
        print("❌ 找不到主程式文件 O3mh_gui_v21_optimized.py")
        print("請確保在正確的目錄中運行此腳本")
        return False
    
    # 步驟1: 檢查依賴項
    if not check_dependencies():
        return False
    
    # 步驟2: 創建規格文件
    create_spec_file()
    
    # 步驟3: 編譯可執行檔
    if not compile_executable():
        return False
    
    # 步驟4: 創建便攜版套件
    if not create_portable_package():
        return False
    
    print("\n🎉 編譯完成！")
    print("📁 輸出文件:")
    print(f"   - 可執行檔: dist/台股智能選股系統.exe")
    print(f"   - 便攜版: dist/台股智能選股系統_便攜版/")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
