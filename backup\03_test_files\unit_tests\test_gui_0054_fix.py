#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試主GUI中0054篩選修正
"""

import sys
import logging
import pandas as pd
from unittest.mock import Mock, patch

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_filter_stock_codes_method():
    """測試主GUI的filter_stock_codes方法"""
    print("🔍 測試主GUI的filter_stock_codes方法")
    print("=" * 50)
    
    try:
        # 導入主GUI類別
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建GUI實例（不顯示界面）
        import sys
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        gui = StockScreenerGUI()
        
        # 測試股票代碼清單
        test_codes = [
            '0050',    # 元大台灣50 - 應保留
            '0051',    # 元大中型100 - 應保留
            '0052',    # 富邦科技 - 應保留
            '0053',    # 元大電子 - 應保留
            '0054',    # 元大台商50 - 應排除（已下市）
            '0055',    # 元大MSCI金融 - 應保留
            '0056',    # 元大高股息 - 應保留
            '2330',    # 台積電 - 應保留
            '2317',    # 鴻海 - 應保留
            '1724',    # 台硝 - 應排除（已下市）
            '5820',    # 日盛金 - 應排除（已下市）
            '030001',  # 權證 - 應排除
            '020011'   # ETN - 應排除
        ]
        
        print(f"📊 測試股票代碼清單:")
        print(f"原始代碼數量: {len(test_codes)}")
        print(f"測試代碼: {test_codes}")
        
        # 檢查股票篩選器是否正確初始化
        print(f"\n🔧 股票篩選器狀態:")
        print(f"stock_filter 存在: {hasattr(gui, 'stock_filter')}")
        print(f"stock_filter 非空: {gui.stock_filter is not None}")
        print(f"filter_stock_codes 方法存在: {hasattr(gui, 'filter_stock_codes')}")
        
        if not hasattr(gui, 'filter_stock_codes'):
            print("❌ filter_stock_codes 方法不存在！")
            return False
        
        # 執行篩選
        filtered_codes = gui.filter_stock_codes(test_codes)
        
        print(f"\n📋 篩選結果:")
        print(f"篩選後數量: {len(filtered_codes)}")
        print(f"排除數量: {len(test_codes) - len(filtered_codes)}")
        print(f"篩選後代碼: {filtered_codes}")
        
        # 檢查關鍵排除項目
        excluded_codes = [code for code in test_codes if code not in filtered_codes]
        print(f"被排除代碼: {excluded_codes}")
        
        # 驗證0054是否被正確排除
        is_0054_excluded = '0054' not in filtered_codes
        print(f"\n🎯 關鍵檢查:")
        print(f"0054是否被排除: {'✅ 是' if is_0054_excluded else '❌ 否'}")
        
        # 檢查其他應該被排除的股票
        should_exclude = ['0054', '1724', '5820', '030001', '020011']
        actually_excluded = [code for code in should_exclude if code not in filtered_codes]
        
        print(f"應排除: {should_exclude}")
        print(f"實際排除: {actually_excluded}")
        print(f"排除率: {len(actually_excluded)}/{len(should_exclude)} ({len(actually_excluded)/len(should_exclude)*100:.1f}%)")
        
        # 檢查應該保留的股票
        should_keep = ['0050', '0051', '0052', '0053', '0055', '0056', '2330', '2317']
        actually_kept = [code for code in should_keep if code in filtered_codes]
        
        print(f"應保留: {should_keep}")
        print(f"實際保留: {actually_kept}")
        print(f"保留率: {len(actually_kept)}/{len(should_keep)} ({len(actually_kept)/len(should_keep)*100:.1f}%)")
        
        success = (
            is_0054_excluded and 
            len(actually_excluded) == len(should_exclude) and
            len(actually_kept) == len(should_keep)
        )
        
        if success:
            print(f"\n🎉 測試通過！filter_stock_codes方法正常工作。")
            return True
        else:
            print(f"\n⚠️ 測試未完全通過，請檢查篩選邏輯。")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_load_stock_list_simulation():
    """模擬load_stock_list方法的執行"""
    print("\n🔍 模擬load_stock_list方法執行")
    print("=" * 50)
    
    try:
        # 導入主GUI類別
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建GUI實例
        import sys
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        gui = StockScreenerGUI()
        
        # 模擬資料庫查詢結果
        mock_df = pd.DataFrame({
            'stock_id': ['0050', '0051', '0052', '0053', '0054', '0055', '0056', '2330', '2317', '1724', '5820'],
            'stock_name': ['元大台灣50', '元大中型100', '富邦科技', '元大電子', '元大台商50', '元大MSCI金融', '元大高股息', '台積電', '鴻海', '台硝', '日盛金'],
            'listing_status': ['上市', '上市', '上市', '上市', '已下市', '上市', '上市', '上市', '上市', '已下市', '已下市'],
            'industry': ['ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF', 'ETF', '半導體業', '電子業', '化學工業', '金融保險業']
        })
        
        print(f"📊 模擬資料庫查詢:")
        print(f"原始股票數量: {len(mock_df)}")
        
        # 模擬load_stock_list中的篩選邏輯
        original_count = len(mock_df)
        
        if hasattr(gui, 'stock_filter') and gui.stock_filter:
            print("✅ 股票篩選器已初始化")
            
            # 篩選股票代碼
            valid_codes = gui.filter_stock_codes(mock_df['stock_id'].tolist())
            filtered_df = mock_df[mock_df['stock_id'].isin(valid_codes)]
            filtered_count = len(filtered_df)
            
            print(f"篩選後數量: {filtered_count}")
            print(f"排除數量: {original_count - filtered_count}")
            
            # 模擬all_stocks_list的內容
            print(f"\n📋 模擬「全部股票」清單:")
            print("-" * 50)
            
            for _, row in filtered_df.iterrows():
                stock_name = row['stock_name'] if row['stock_name'] else '未知'
                listing_status = row['listing_status'] if row['listing_status'] else ''
                industry = row['industry'] if row['industry'] else ''

                if listing_status and industry:
                    item_text = f"{row['stock_id']} {stock_name} ({listing_status}/{industry})"
                elif listing_status:
                    item_text = f"{row['stock_id']} {stock_name} ({listing_status})"
                elif stock_name != '未知':
                    item_text = f"{row['stock_id']} {stock_name}"
                else:
                    item_text = f"{row['stock_id']}"
                
                print(item_text)
            
            # 檢查0054是否被排除
            has_0054 = '0054' in valid_codes
            print(f"\n🔍 關鍵檢查:")
            print(f"0054是否在篩選後清單中: {'❌ 是（錯誤）' if has_0054 else '✅ 否（正確）'}")
            
            return not has_0054
        else:
            print("❌ 股票篩選器未初始化")
            return False
        
    except Exception as e:
        print(f"❌ 模擬測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主測試函數"""
    print("🚀 主GUI 0054篩選修正測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("filter_stock_codes方法", test_filter_stock_codes_method()))
    test_results.append(("load_stock_list模擬", test_load_stock_list_simulation()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<25}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！主GUI 0054篩選修正成功。")
        print("\n📋 修正說明:")
        print("• 已添加 filter_stock_codes 方法到主GUI")
        print("• load_stock_list 方法現在會正確應用股票篩選")
        print("• 0054等已下市ETF將不再出現在「全部股票」清單中")
        print("• 重新啟動主GUI後，篩選功能將生效")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能的實現。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
