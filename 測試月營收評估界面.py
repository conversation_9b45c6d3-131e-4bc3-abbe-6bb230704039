#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收評估界面的修復效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI

def test_monthly_revenue_assessment():
    """測試月營收評估界面"""
    print("🚀 測試月營收評估界面修復效果")
    
    app = QApplication(sys.argv)
    
    # 創建主界面
    gui = FinlabGUI()
    
    # 創建測試資料
    test_data_with_rank = {
        '排名': '5',
        '股票代碼': '1101',
        '股票名稱': '台泥',
        '西元年月': '202507',
        '當月營收': '10,107,877',  # 測試大數字（千元單位）
        '上個月營收': '12,500,000',
        '去年同月營收': '13,650,000',
        'YoY%': '-26.00%',
        'MoM%': '-19.90%',
        '殖利率': '4.5',
        '本益比': '12.8',
        '股價淨值比': '1.2',
        'EPS': '2.85',
        '綜合評分': '72.5'
    }
    
    test_data_no_rank = {
        '排名': 'N/A',
        '股票代碼': '2330',
        '股票名稱': '台積電',
        '西元年月': '202507',
        '當月營收': '120,000,000',  # 測試超大數字（千元單位）
        '上個月營收': '115,000,000',
        '去年同月營收': '100,000,000',
        'YoY%': '+20.00%',
        'MoM%': '+4.35%',
        '殖利率': 'N/A',
        '本益比': 'N/A',
        '股價淨值比': 'N/A',
        'EPS': 'N/A',
        '綜合評分': 'N/A'
    }
    
    test_data_small_numbers = {
        '排名': 'N/A',
        '股票代碼': '8021',
        '股票名稱': '尖點',
        '西元年月': '202507',
        '當月營收': '25,000',  # 測試小數字（元單位）
        '上個月營收': '23,000',
        '去年同月營收': '20,000',
        'YoY%': '+25.00%',
        'MoM%': '+8.70%',
        '殖利率': 'N/A',
        '本益比': 'N/A',
        '股價淨值比': 'N/A',
        'EPS': 'N/A',
        '綜合評分': 'N/A'
    }
    
    print("📊 測試案例:")
    print("1. 有排名的股票（台泥）- 負成長")
    print("2. 無排名的股票（台積電）- 正成長，大營收")
    print("3. 無排名的股票（尖點）- 正成長，小營收")
    
    # 測試各種情況
    print("\n🔍 測試1: 有排名的股票（台泥）")
    gui.show_monthly_revenue_assessment(test_data_with_rank)
    
    print("\n🔍 測試2: 無排名的股票（台積電）")
    gui.show_monthly_revenue_assessment(test_data_no_rank)
    
    print("\n🔍 測試3: 小營收股票（尖點）")
    gui.show_monthly_revenue_assessment(test_data_small_numbers)
    
    print("\n✅ 測試完成！")
    print("請檢查以下修復效果：")
    print("1. 排名顯示：有排名顯示'第X名'，無排名顯示'未排名'")
    print("2. 左右兩欄佈局：左欄基本資訊+營收+成長率，右欄財務指標+建議")
    print("3. 營收單位：大數字顯示'千元'，小數字顯示'元'")
    print("4. 成長率顏色：正成長紅色，負成長綠色")
    print("5. 無留白：內容緊湊，充分利用空間")
    
    # 不啟動事件循環，只是測試創建
    app.quit()

def test_revenue_unit_determination():
    """測試營收單位判斷邏輯"""
    print("\n🧪 測試營收單位判斷邏輯")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    test_cases = [
        {'當月營收': '120,000,000', 'expected': '千元', 'description': '1.2億（千元單位）'},
        {'當月營收': '10,107,877', 'expected': '千元', 'description': '1千萬（千元單位）'},
        {'當月營收': '25,000', 'expected': '元', 'description': '2.5萬（元單位）'},
        {'當月營收': '500,000', 'expected': '千元', 'description': '50萬（千元單位）'},
        {'當月營收': '50,000', 'expected': '元', 'description': '5萬（元單位）'},
        {'當月營收': 'N/A', 'expected': '千元', 'description': 'N/A（預設千元）'},
    ]
    
    print("測試案例:")
    for i, case in enumerate(test_cases, 1):
        result = gui.determine_revenue_unit(case)
        status = "✅" if result == case['expected'] else "❌"
        print(f"{i}. {case['description']}: {result} {status}")
        if result != case['expected']:
            print(f"   預期: {case['expected']}, 實際: {result}")
    
    app.quit()

def test_growth_color_logic():
    """測試成長率顏色邏輯"""
    print("\n🎨 測試成長率顏色邏輯")
    
    app = QApplication(sys.argv)
    gui = FinlabGUI()
    
    test_cases = [
        {'+20.00%': 'color: #e74c3c;', 'description': '正成長（紅色）'},
        {'-26.00%': 'color: #27ae60;', 'description': '負成長（綠色）'},
        {'0.00%': 'color: #666666;', 'description': '零成長（灰色）'},
        {'N/A': 'color: #666666;', 'description': 'N/A（灰色）'},
    ]
    
    print("測試案例:")
    for i, case in enumerate(test_cases, 1):
        for rate, expected in case.items():
            if rate != 'description':
                result = gui.get_growth_color_style(rate)
                status = "✅" if result == expected else "❌"
                print(f"{i}. {rate} ({case['description']}): {status}")
                if result != expected:
                    print(f"   預期: {expected}")
                    print(f"   實際: {result}")
                break

    app.quit()

def main():
    """主函數"""
    print("🚀 月營收評估界面修復測試")
    print("=" * 60)
    
    # 測試界面
    test_monthly_revenue_assessment()
    
    # 測試營收單位判斷
    test_revenue_unit_determination()
    
    # 測試成長率顏色
    test_growth_color_logic()
    
    print("\n" + "=" * 60)
    print("🎉 所有測試完成！")
    print("\n修復內容總結:")
    print("✅ 1. 排名顯示修復：N/A顯示為'未排名'")
    print("✅ 2. 左右兩欄佈局：仿照全球市場數據爬蟲")
    print("✅ 3. 營收單位智能判斷：大數字千元，小數字元")
    print("✅ 4. 成長率顏色標示：正紅負綠")
    print("✅ 5. 消除留白：緊湊佈局，充分利用空間")
    print("✅ 6. 界面美化：GroupBox分組，清晰易讀")
    
    print("\n請啟動主程式測試實際效果：")
    print("python O3mh_gui_v21_optimized.py")
    print("然後右鍵點擊股票選擇'月營收綜合評估'")

if __name__ == "__main__":
    main()
