#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化版GoodInfo月營收下載器
基於您提供的程式碼
"""

import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# from webdriver_manager.chrome import ChromeDriverManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_download(stock_id="2330", headless=False):
    """
    測試簡化版下載
    
    Args:
        stock_id: 股票代號
        headless: 是否使用無頭模式
    """
    driver = None
    
    try:
        logger.info(f"🚀 開始測試下載 {stock_id} 月營收資料...")
        
        # 設定下載目錄
        download_dir = os.path.join(os.getcwd(), "test_downloads")
        os.makedirs(download_dir, exist_ok=True)
        logger.info(f"📁 下載目錄: {download_dir}")
        
        # 設定 Chrome 選項
        options = Options()
        
        # 設置下載目錄
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        # 基本設定
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 設定User-Agent
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 無頭模式設定
        if headless:
            options.add_argument('--headless')
            logger.info("🔧 啟用無頭模式")
        else:
            logger.info("🔧 啟用視窗模式（可觀察過程）")
        
        # 減少日誌輸出
        options.add_argument('--log-level=3')
        options.add_argument('--silent')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 初始化 WebDriver（使用系統ChromeDriver）
        try:
            # 嘗試使用系統PATH中的ChromeDriver
            driver = webdriver.Chrome(options=options)
        except Exception as e:
            logger.error(f"❌ 無法啟動Chrome: {e}")
            logger.info("💡 請確保已安裝ChromeDriver並加入系統PATH")
            return False
        logger.info("✅ Chrome瀏覽器已啟動")
        
        # 打開目標網頁
        url = f"https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID={stock_id}"
        logger.info(f"📱 訪問頁面: {url}")
        driver.get(url)
        
        # 等待頁面完全加載
        logger.info("⏳ 等待頁面載入...")
        wait = WebDriverWait(driver, 30)
        
        # 等待表格出現
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("✅ 表格已載入")
        except Exception as e:
            logger.warning(f"⚠️ 等待表格超時: {e}")
        
        # 額外等待確保頁面完全載入
        time.sleep(5)
        logger.info("✅ 頁面載入完成")
        
        # 檢查頁面內容
        page_title = driver.title
        logger.info(f"📄 頁面標題: {page_title}")
        
        # 檢查是否包含營收相關內容
        page_source = driver.page_source
        if "營收" in page_source or "月營收" in page_source:
            logger.info("✅ 頁面包含營收相關內容")
        else:
            logger.warning("⚠️ 頁面可能不包含營收內容")
        
        # 尋找匯出按鈕的多種策略
        export_button = None
        button_selectors = [
            # 基於您提供的程式碼
            '//input[@value="匯出 XLS"]',
            # 其他可能的選擇器
            '//input[@value="XLS"]',
            '//input[contains(@value, "XLS")]',
            '//input[contains(@value, "匯出")]',
            '//input[@type="button"][contains(@onclick, "export2xls")]',
            '//button[contains(text(), "XLS")]',
            '//button[contains(text(), "匯出")]'
        ]
        
        logger.info("🔍 尋找匯出按鈕...")
        for i, selector in enumerate(button_selectors, 1):
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    button = elements[0]
                    if button.is_displayed() and button.is_enabled():
                        export_button = button
                        logger.info(f"✅ 找到匯出按鈕 (策略{i}): {selector}")
                        logger.info(f"   按鈕文字: {button.get_attribute('value') or button.text}")
                        break
                    else:
                        logger.debug(f"   策略{i}: 按鈕存在但不可用")
                else:
                    logger.debug(f"   策略{i}: 未找到元素")
            except Exception as e:
                logger.debug(f"   策略{i} 失敗: {e}")
        
        if not export_button:
            logger.error("❌ 未找到可用的匯出按鈕")
            
            # 列出所有input元素以供調試
            logger.info("🔍 列出頁面中所有input元素:")
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            for i, input_elem in enumerate(all_inputs[:10]):  # 只顯示前10個
                try:
                    input_type = input_elem.get_attribute("type")
                    input_value = input_elem.get_attribute("value")
                    input_onclick = input_elem.get_attribute("onclick")
                    logger.info(f"   Input {i+1}: type='{input_type}', value='{input_value}', onclick='{input_onclick}'")
                except:
                    pass
            
            return False
        
        # 點擊匯出按鈕
        logger.info("📥 點擊匯出按鈕...")
        try:
            # 使用JavaScript點擊，更可靠
            driver.execute_script("arguments[0].click();", export_button)
            logger.info("✅ 按鈕點擊成功")
        except Exception as e:
            logger.warning(f"⚠️ JavaScript點擊失敗，嘗試直接點擊: {e}")
            try:
                export_button.click()
                logger.info("✅ 直接點擊成功")
            except Exception as e2:
                logger.error(f"❌ 所有點擊方式都失敗: {e2}")
                return False
        
        # 等待下載完成
        logger.info("⏳ 等待下載完成...")
        download_timeout = 30
        start_time = time.time()
        downloaded_file = None
        
        while time.time() - start_time < download_timeout:
            # 檢查下載目錄中的檔案
            for filename in os.listdir(download_dir):
                if filename.endswith('.xls') or filename.endswith('.xlsx'):
                    file_path = os.path.join(download_dir, filename)
                    # 檢查檔案是否完整（不是.crdownload）
                    if not filename.endswith('.crdownload') and os.path.getsize(file_path) > 0:
                        downloaded_file = file_path
                        logger.info(f"✅ 檔案下載完成: {filename}")
                        break
            
            if downloaded_file:
                break
                
            time.sleep(1)
        
        if downloaded_file:
            file_size = os.path.getsize(downloaded_file)
            logger.info(f"📊 下載成功！檔案大小: {file_size} bytes")
            
            # 簡單檢查檔案內容
            try:
                import pandas as pd
                df = pd.read_excel(downloaded_file)
                logger.info(f"📈 Excel檔案包含 {len(df)} 行數據")
                if len(df) > 0:
                    logger.info(f"📋 欄位: {list(df.columns)}")
                return True
            except Exception as e:
                logger.warning(f"⚠️ 檔案讀取測試失敗: {e}")
                return True  # 下載成功，但讀取失敗
        else:
            logger.error(f"❌ 下載超時 ({download_timeout}秒)")
            return False
            
    except Exception as e:
        logger.error(f"❌ 測試過程發生錯誤: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()
            logger.info("🔧 瀏覽器已關閉")

def main():
    """主函數"""
    print("🚀 簡化版GoodInfo月營收下載測試")
    print("=" * 60)
    
    # 詢問是否使用無頭模式
    headless_choice = input("是否使用無頭模式？(y/n，預設n): ").lower().strip()
    headless = headless_choice == 'y'
    
    # 詢問股票代號
    stock_input = input("請輸入股票代號（預設2330）: ").strip()
    stock_id = stock_input if stock_input else "2330"
    
    print(f"\n📊 測試設定:")
    print(f"   股票代號: {stock_id}")
    print(f"   無頭模式: {'是' if headless else '否'}")
    print(f"   下載目錄: {os.path.join(os.getcwd(), 'test_downloads')}")
    
    # 執行測試
    success = test_simple_download(stock_id, headless)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 測試成功！月營收資料下載完成")
    else:
        print("❌ 測試失敗！請檢查錯誤信息")
    
    print("\n💡 如果測試失敗，建議:")
    print("1. 檢查網路連線")
    print("2. 確認股票代號正確")
    print("3. 嘗試不使用無頭模式以觀察過程")
    print("4. 檢查GoodInfo網站是否有變更")

if __name__ == "__main__":
    main()
