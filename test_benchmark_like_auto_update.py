#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用與 auto_update.py 相同的模式測試 benchmark 更新
"""

# 修復 ipywidgets 依賴問題
import sys
import types

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass

    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

print(f"🔍 finlab 路徑: {finlab_path}")
print(f"🔍 路徑存在: {os.path.exists(finlab_path)}")

# 現在可以安全導入 crawler 模組
from crawler import (
    crawl_benchmark,
    table_date_range,
    to_pickle,
    date_range,
)

import datetime
import pandas as pd
import time
import random
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def is_trading_day(date):
    """檢查是否為交易日 (簡單版本 - 排除週末)"""
    return date.weekday() < 5  # 0-4 是週一到週五

def filter_trading_days(dates):
    """過濾出交易日"""
    return [date for date in dates if is_trading_day(date)]

def test_benchmark_update_small_batch():
    """測試 benchmark 更新 - 小批量（10筆）"""
    table_name = 'benchmark'
    crawl_function = crawl_benchmark
    
    print(f"\n🔄 測試更新 {table_name} (小批量)...")
    
    try:
        # 獲取當前日期範圍
        print(f"📅 獲取 {table_name} 表格日期範圍...")
        first_date, last_date = table_date_range(table_name)
        print(f"   表格日期範圍: {first_date} 至 {last_date}")
        
        if last_date:
            # 計算需要更新的日期（限制為最近10個交易日）
            dates = date_range(last_date, datetime.datetime.now())
            
            if dates:
                # 過濾出交易日
                trading_dates = filter_trading_days(dates)
                
                # 限制為最多10個日期進行測試
                test_dates = trading_dates[-10:] if len(trading_dates) > 10 else trading_dates
                
                if test_dates:
                    print(f"📈 測試更新 {len(test_dates)} 個交易日")
                    print(f"   測試範圍: {test_dates[0]} 至 {test_dates[-1]}")
                    
                    # 確認是否繼續
                    response = input(f"\n是否開始測試更新 {len(test_dates)} 個日期？(y/N): ").strip().lower()
                    if response != 'y':
                        print("❌ 測試已取消")
                        return False
                    
                    # 使用與 auto_update.py 相同的更新邏輯
                    success = test_update_table(table_name, crawl_function, test_dates)
                    
                    if success:
                        print(f"✅ {table_name} 測試更新完成")
                        return True
                    else:
                        print(f"❌ {table_name} 測試更新失敗")
                        return False
                else:
                    print(f"✅ {table_name} 沒有需要更新的交易日")
                    return True
            else:
                print(f"✅ {table_name} 已是最新，無需更新")
                return True
        else:
            print(f"⚠️ {table_name} 無法獲取日期範圍")
            return False
            
    except Exception as e:
        print(f"❌ 測試更新 {table_name} 失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_update_table(table_name, crawl_function, dates):
    """測試表格更新函數 - 完全模仿 auto_update.py 的邏輯"""
    
    if not dates:
        print("該時間段沒有可以爬取之資料")
        return False

    print(f'start crawl {table_name} from {dates[0]} to {dates[-1]}')

    df_list = []
    success_count = 0
    
    # 創建備份
    backup_file = f"history/tables/{table_name}_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    original_file = f"history/tables/{table_name}.pkl"
    
    if os.path.exists(original_file):
        import shutil
        shutil.copy2(original_file, backup_file)
        print(f"✅ 備份創建: {backup_file}")

    for i, date in enumerate(dates):
        try:
            print(f"crawl{table_name}{date.strftime('%Y-%m-%d')}: {i+1:3d}/{len(dates):3d} [{(i+1)/len(dates)*100:5.1f}%]", end="")

            # 調用爬蟲函數
            df = crawl_function(date)

            if df is not None and not df.empty:
                df_list.append(df)
                success_count += 1
                print("  ✅")
                
                # 每5筆存檔一次（降低風險）
                if len(df_list) % 5 == 0:
                    print(f"  💾 中間存檔 ({len(df_list)} 筆)...")
                    temp_df = pd.concat(df_list, ignore_index=True)
                    to_pickle(temp_df, table_name)
                    
            else:
                print("  ❌ (可能是假日或無資料)")

            # 增加延遲避免被封鎖 - 與 auto_update.py 相同
            if i < len(dates) - 1:  # 不是最後一個
                delay = random.uniform(5, 10)  # 5-10秒隨機延遲
                print(f"  ⏳ 等待 {delay:.1f} 秒...")
                time.sleep(delay)

        except Exception as e:
            print(f"  ❌ 錯誤: {str(e)[:50]}...")
            continue

    # 最終合併和保存資料
    if df_list:
        print(f"\n💾 最終保存...")
        combined_df = pd.concat(df_list, ignore_index=True)
        to_pickle(combined_df, table_name)
        print(f"✅ 成功爬取 {success_count}/{len(dates)} 個日期，共 {len(combined_df)} 筆資料")
        return True
    else:
        print(f"\n❌ 沒有成功爬取任何資料")
        return False

def main():
    """主測試函數"""
    print("🔧 Benchmark 更新測試工具 (模仿 auto_update.py)")
    print("=" * 60)
    print("🎯 目標: 使用與 auto_update.py 相同的邏輯測試 benchmark")
    print("💡 特色: 5-10秒隨機延遲 + 每5筆存檔 + 完整錯誤處理")
    print("=" * 60)
    
    success = test_benchmark_update_small_batch()
    
    if success:
        print("\n🎉 測試成功！")
        print("💡 現在可以在 auto_update.py 中放心使用 benchmark 更新")
        print("✨ 系統會自動處理錯誤和延遲")
    else:
        print("\n⚠️ 測試失敗")
        print("💡 建議檢查網路連接或 API 狀況")

if __name__ == "__main__":
    main()
