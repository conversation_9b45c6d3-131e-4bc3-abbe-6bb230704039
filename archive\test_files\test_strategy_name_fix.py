#!/usr/bin/env python3
"""
測試策略名稱修復功能
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

def test_strategy_name_matching():
    """測試策略名稱匹配功能"""
    print("🧪 測試策略名稱匹配功能")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查策略下拉選單內容
        print(f"\n📋 策略下拉選單內容:")
        for i in range(window.strategy_combo.count()):
            item_text = window.strategy_combo.itemText(i)
            print(f"  {i:2d}: {item_text}")
        
        # 測試策略名稱匹配
        test_strategies = [
            "勝率73.45%", "破底反彈高量", "阿水一式", "阿水二式", 
            "藏獒", "CANSLIM量價齊升", "膽小貓", "二次創高股票", "監獄兔"
        ]
        
        print(f"\n🔍 測試策略名稱匹配:")
        for strategy_name in test_strategies:
            found_index = -1
            found_text = ""
            
            for i in range(window.strategy_combo.count()):
                item_text = window.strategy_combo.itemText(i)
                if item_text == strategy_name or strategy_name in item_text:
                    found_index = i
                    found_text = item_text
                    break
            
            if found_index != -1:
                print(f"  ✅ {strategy_name} -> 找到: {found_text}")
            else:
                print(f"  ❌ {strategy_name} -> 未找到")
        
        # 測試策略字典內容
        print(f"\n📊 策略字典內容:")
        if hasattr(window, 'strategies'):
            for strategy_name in window.strategies.keys():
                print(f"  📈 {strategy_name}")
        else:
            print("  ⚠️ 策略字典未初始化")
        
        # 檢查交集分析器
        print(f"\n🔗 交集分析器狀態:")
        if hasattr(window, 'intersection_analyzer') and window.intersection_analyzer:
            print("  ✅ 交集分析器已初始化")
        else:
            print("  ❌ 交集分析器未初始化")
        
        # 檢查策略結果緩存
        print(f"\n💾 策略結果緩存:")
        if hasattr(window, 'strategy_results_cache'):
            if window.strategy_results_cache:
                for strategy_name, result_df in window.strategy_results_cache.items():
                    print(f"  📊 {strategy_name}: {len(result_df)} 支股票")
            else:
                print("  📝 緩存為空（正常，尚未執行策略）")
        else:
            print("  ❌ 策略結果緩存未初始化")
        
        print(f"\n🎯 修復驗證結果:")
        
        # 檢查關鍵功能
        checks = [
            ("策略下拉選單", window.strategy_combo.count() > 0),
            ("策略字典", hasattr(window, 'strategies') and len(window.strategies) > 0),
            ("交集分析器", hasattr(window, 'intersection_analyzer')),
            ("策略緩存", hasattr(window, 'strategy_results_cache')),
            ("交集界面", hasattr(window, 'intersection_strategy_vars'))
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            if not check_result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 所有檢查通過！策略名稱匹配功能已修復")
            print(f"\n💡 現在可以測試自動執行功能:")
            print(f"  1. 啟動主程式")
            print(f"  2. 切換到「🔗 策略交集」標籤頁")
            print(f"  3. 選擇多個策略")
            print(f"  4. 點擊「🎯 計算交集」")
            print(f"  5. 觀察是否正確詢問自動執行")
        else:
            print(f"\n❌ 部分檢查失敗，需要進一步修復")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_strategy_mapping_info():
    """創建策略名稱映射信息"""
    mapping_info = """
# 🔍 策略名稱映射修復說明

## 問題原因
策略下拉選單使用了分類前綴，導致策略名稱不匹配：
- 實際策略名稱: "藏獒"
- 下拉選單顯示: "🚀 進階策略 > 藏獒"

## 修復方案
修改 `switch_and_execute_strategy` 方法，支援模糊匹配：

```python
# 原來的精確匹配
if item_text == strategy_name:

# 修復後的模糊匹配  
if item_text == strategy_name or strategy_name in item_text:
```

## 策略名稱對應表

| 策略名稱 | 分類 | 下拉選單顯示 |
|---------|------|-------------|
| 勝率73.45% | 📈 經典策略 | 📈 經典策略 > 勝率73.45% |
| 破底反彈高量 | 📈 經典策略 | 📈 經典策略 > 破底反彈高量 |
| 阿水一式 | 🌟 阿水策略系列 | 🌟 阿水策略系列 > 阿水一式 |
| 阿水二式 | 🌟 阿水策略系列 | 🌟 阿水策略系列 > 阿水二式 |
| 藏獒 | 🚀 進階策略 | 🚀 進階策略 > 藏獒 |
| CANSLIM量價齊升 | 🚀 進階策略 | 🚀 進階策略 > CANSLIM量價齊升 |
| 膽小貓 | 🚀 進階策略 | 🚀 進階策略 > 膽小貓 |
| 二次創高股票 | 🚀 進階策略 | 🚀 進階策略 > 二次創高股票 |
| 監獄兔 | 🚀 進階策略 | 🚀 進階策略 > 監獄兔 |

## 測試驗證
運行 `test_strategy_name_fix.py` 驗證修復效果：
- 檢查策略名稱匹配
- 驗證自動執行功能
- 確認交集分析正常

## 使用建議
1. 確保策略名稱在交集分析界面中與實際策略一致
2. 使用模糊匹配避免前綴問題
3. 添加詳細的日誌輸出便於調試
"""
    
    with open("策略名稱映射修復說明.md", "w", encoding="utf-8") as f:
        f.write(mapping_info)
    
    print("📖 策略名稱映射修復說明已保存到: 策略名稱映射修復說明.md")

def main():
    """主函數"""
    print("🚀 啟動策略名稱修復測試")
    print("=" * 50)
    
    # 創建修復說明
    create_strategy_mapping_info()
    
    # 執行測試
    success = test_strategy_name_matching()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 策略名稱匹配修復成功")
        print("\n🎊 現在可以正常使用自動執行功能了！")
        
        print(f"\n💡 下一步測試:")
        steps = [
            "啟動主程式",
            "切換到「🔗 策略交集」標籤頁", 
            "選擇多個策略（包括未執行的）",
            "點擊「🎯 計算交集」",
            "確認程式正確詢問自動執行",
            "測試自動執行功能"
        ]
        
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step}")
            
    else:
        print("❌ 策略名稱匹配修復失敗")
        print("請檢查錯誤信息並進行進一步修復")

if __name__ == "__main__":
    main()
