#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試改進的全球市場數據爬蟲界面
"""

import sys
from PyQt6.QtWidgets import QApplication

def test_improved_dialog():
    """測試改進的對話框"""
    print("🖥️ 測試改進的全球市場數據爬蟲界面")
    print("=" * 50)
    
    try:
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 導入改進的對話框
        from market_data_crawler_dialog import MarketDataCrawlerDialog
        
        # 創建對話框
        dialog = MarketDataCrawlerDialog()
        
        print("✅ 對話框創建成功")
        print("\n🆕 界面改進:")
        print("• 移除詳細說明文字，節省空間")
        print("• 視窗尺寸增大到 1200x800")
        print("• 啟用最大化、最小化、關閉按鈕")
        print("• 使用兩欄布局減少滾動")
        print("• 左欄：市場指數 & 期貨")
        print("• 右欄：商品 & 外匯 & 加密貨幣")
        
        print("\n🎯 界面特點:")
        print("• 黑色背景主題保持一致")
        print("• 進度條高度優化")
        print("• 分類標題清晰標示")
        print("• 複製功能合併兩欄內容")
        
        # 檢查關鍵組件
        components = [
            ('left_result_text', '左欄結果顯示'),
            ('right_result_text', '右欄結果顯示'),
            ('progress_bar', '進度條'),
            ('start_button', '開始按鈕'),
            ('copy_button', '複製按鈕'),
            ('export_button', '導出按鈕')
        ]
        
        print("\n🔍 組件檢查:")
        for attr_name, description in components:
            if hasattr(dialog, attr_name):
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
        
        # 顯示對話框（可選）
        print("\n💡 提示：對話框已準備就緒")
        print("可以在主程式中通過「🕷️ 爬蟲」→「🌍 全球市場數據爬蟲」啟動")
        
        # 不實際顯示對話框，避免阻塞
        # dialog.show()
        # app.exec()
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_window_flags():
    """測試視窗標誌"""
    print("\n🪟 測試視窗標誌...")
    
    try:
        from PyQt6.QtCore import Qt
        
        # 檢查標誌常數
        flags = [
            'Window',
            'WindowMinimizeButtonHint',
            'WindowMaximizeButtonHint', 
            'WindowCloseButtonHint'
        ]
        
        for flag in flags:
            if hasattr(Qt.WindowType, flag):
                print(f"✅ Qt.WindowType.{flag}")
            else:
                print(f"❌ Qt.WindowType.{flag}")
        
        return True
        
    except Exception as e:
        print(f"❌ 視窗標誌測試失敗: {e}")
        return False

def test_layout_structure():
    """測試布局結構"""
    print("\n📐 測試布局結構...")
    
    try:
        # 檢查文件內容
        with open('market_data_crawler_dialog.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        layout_elements = [
            ('left_result_text', '左欄文本框'),
            ('right_result_text', '右欄文本框'),
            ('QHBoxLayout', '水平布局'),
            ('left_column', '左欄布局'),
            ('right_column', '右欄布局'),
            ('setGeometry(100, 100, 1200, 800)', '視窗尺寸設置')
        ]
        
        for element, description in layout_elements:
            if element in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 布局結構測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🚀 全球市場數據爬蟲界面改進測試")
    print("=" * 60)
    
    tests = [
        ("改進的對話框", test_improved_dialog),
        ("視窗標誌", test_window_flags),
        ("布局結構", test_layout_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 界面改進總結:")
    
    passed = sum(1 for _, result in results if result)
    
    print(f"\n✅ 完成的改進:")
    print("• 移除上端詳細說明文字")
    print("• 視窗尺寸增大到 1200x800")
    print("• 啟用最大化、最小化、關閉按鈕")
    print("• 實現兩欄布局設計")
    print("• 左欄：市場指數 & 期貨數據")
    print("• 右欄：商品 & 外匯 & 加密貨幣")
    print("• 優化進度條和標題顯示")
    print("• 改進複製功能合併兩欄內容")
    
    print(f"\n🎯 使用體驗:")
    print("• 減少垂直滾動需求")
    print("• 提高數據可視性")
    print("• 保持黑色主題一致性")
    print("• 支援視窗大小調整")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    if passed == len(results):
        print("🎉 界面改進完成！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")

if __name__ == "__main__":
    main()
