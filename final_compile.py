#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終編譯腳本 - 創建完全穩定的可執行檔
排除有問題的模組，確保編譯成功
"""

import os
import sys
import subprocess
import shutil
import time

def create_final_spec():
    """創建最終的 spec 文件"""
    print("📝 創建最終編譯配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 數據文件
datas = []

# 隱藏導入 - 只包含確定可用的模組
hiddenimports = [
    # 系統核心模組
    'inspect',
    'pydoc', 
    'doctest',
    'difflib',
    'importlib',
    'importlib.util',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'calendar',
    'time',
    'threading',
    'concurrent.futures',
    'logging',
    'traceback',
    'os',
    'sys',
    'random',
    'warnings',
    
    # PyQt6 核心
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # 數據處理核心
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core.numeric',
    
    # 圖表核心
    'pyqtgraph',
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    
    # 網路核心
    'requests',
    'urllib3',
    'bs4',
    'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    'xlsxwriter',
    
    # 其他必要模組
    'setuptools',
    'pkg_resources',
]

# 排除有問題的模組
excludes = [
    'tkinter',
    'test',
    'tests',
    'unittest',
    'pdb',
    'PyQt5',
    'PySide2',
    'PySide6',
    'IPython',
    'jupyter',
    'notebook',
    'twstock',  # 排除有 CSV 文件問題的 twstock
    'twstock.codes',
    'twstock.stock',
    'yfinance',  # 可能有網路依賴問題
    'finlab',    # 可能有依賴問題
    'finmind',   # 可能有依賴問題
    'talib',     # 可能有 C 依賴問題
    'mplfinance', # 可能有依賴問題
    'matplotlib', # 可能有依賴問題
    'seaborn',   # 可能有依賴問題
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StockAnalyzer_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('final_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最終編譯配置已創建")

def clean_and_compile():
    """清理並編譯"""
    print("🧹 清理編譯環境...")
    
    # 清理目錄
    dirs_to_clean = ['build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 清理 {dir_name}")
            except Exception as e:
                print(f"⚠️ 無法清理 {dir_name}: {e}")
    
    # 等待一下確保文件釋放
    time.sleep(2)
    
    print("🔨 開始最終編譯...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'final_compile.spec'
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/StockAnalyzer_Final.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_final_launcher()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            if result.stderr:
                print("錯誤輸出:")
                print(result.stderr[-1500:])  # 顯示最後1500個字符
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_final_launcher():
    """創建最終啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         最終穩定版 - 核心功能版
echo ========================================
echo.

if exist "dist\\StockAnalyzer_Final.exe" (
    echo ✅ 找到最終穩定版
    echo 🚀 正在啟動...
    echo.
    echo 💡 最終穩定版特點：
    echo    ✓ 排除有問題的模組，確保穩定
    echo    ✓ 包含核心股票分析功能
    echo    ✓ 完全獨立，無外部依賴
    echo    ✓ 優化啟動速度
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Final.exe"
    
    echo ✅ 最終穩定版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到最終穩定版
    echo.
    echo 請重新編譯：
    echo    python final_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 使用提示：
echo    - 如果程式沒有顯示，請檢查工作列
echo    - 部分進階功能可能需要網路連接
echo    - 如有問題，請檢查防毒軟體設定
echo.

timeout /t 5 >nul
'''
    
    with open('啟動最終穩定版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建最終啟動腳本: 啟動最終穩定版.bat")

def main():
    """主函數"""
    print("🚀 台股智能選股系統 - 最終編譯器")
    print("=" * 60)
    print("策略：排除有問題的模組，確保核心功能穩定")
    print()
    
    # 步驟1: 創建配置
    create_final_spec()
    print()
    
    # 步驟2: 清理並編譯
    if clean_and_compile():
        print("\n🎉 最終穩定版編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/StockAnalyzer_Final.exe")
        print("   - 啟動最終穩定版.bat")
        print("\n🚀 使用方法:")
        print("   雙擊執行: 啟動最終穩定版.bat")
        print("\n✨ 最終穩定版特點:")
        print("   ✓ 排除有問題的模組，確保穩定運行")
        print("   ✓ 包含核心股票分析功能")
        print("   ✓ 完全獨立，無外部依賴")
        print("   ✓ 優化的檔案大小和啟動速度")
        print("\n⚠️ 注意:")
        print("   - 部分進階功能可能受限（如 twstock 相關功能）")
        print("   - 核心選股和分析功能完全保留")
        return True
    else:
        print("\n❌ 編譯失敗！")
        print("請檢查錯誤信息並重試")
        return False

if __name__ == "__main__":
    main()
