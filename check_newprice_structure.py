#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 newprice.db 的結構，特別是 stock_id 與 stock_name 的處理方式
"""

import sqlite3
import pandas as pd

def check_newprice_structure():
    """檢查 newprice.db 的結構"""
    
    print("=" * 80)
    print("🔍 檢查 D:\\Finlab\\history\\tables\\newprice.db 的結構")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 資料庫中的表格:")
        for i, (table_name,) in enumerate(tables, 1):
            print(f"   {i}. {table_name}")
        
        # 檢查主要表格的結構
        for table_name, in tables:
            print(f"\n📋 表格 '{table_name}' 的結構:")
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for i, (cid, name, type_, notnull, default, pk) in enumerate(columns, 1):
                pk_mark = " (PRIMARY KEY)" if pk else ""
                print(f"   {i:2d}. {name:<20} {type_:<10}{pk_mark}")
            
            # 檢查記錄數
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📊 記錄數: {count:,}")
            
            # 如果有 stock_id 和 stock_name，檢查範例資料
            cursor.execute(f"PRAGMA table_info({table_name})")
            column_names = [col[1] for col in cursor.fetchall()]
            
            if 'stock_id' in column_names:
                print(f"\n   📊 stock_id 範例:")
                if 'stock_name' in column_names:
                    cursor.execute(f"SELECT stock_id, stock_name FROM {table_name} LIMIT 10")
                    sample_data = cursor.fetchall()
                    for stock_id, stock_name in sample_data:
                        print(f"      {stock_id} -> {stock_name}")
                else:
                    cursor.execute(f"SELECT DISTINCT stock_id FROM {table_name} LIMIT 10")
                    sample_data = cursor.fetchall()
                    for stock_id, in sample_data:
                        print(f"      {stock_id}")
                
                # 檢查 stock_id 的唯一值數量
                cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {table_name}")
                unique_stocks = cursor.fetchone()[0]
                print(f"   📊 唯一股票數: {unique_stocks}")
            
            # 如果有日期欄位，檢查日期範圍
            date_columns = [col for col in column_names if 'date' in col.lower()]
            if date_columns:
                date_col = date_columns[0]
                cursor.execute(f"SELECT MIN({date_col}), MAX({date_col}) FROM {table_name}")
                date_range = cursor.fetchone()
                print(f"   📅 日期範圍 ({date_col}): {date_range[0]} ~ {date_range[1]}")
        
        conn.close()
        
        print(f"\n✅ 檢查完成")
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_newprice_structure()
