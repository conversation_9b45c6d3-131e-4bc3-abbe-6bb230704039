#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試 pe 爬蟲功能
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def test_pe_crawl():
    """測試 pe 爬蟲"""
    try:
        # 添加AI_finlab目錄到Python路徑
        ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
        if ai_finlab_path not in sys.path:
            sys.path.insert(0, ai_finlab_path)
        
        from crawler import crawl_pe
        
        # 測試爬取前幾天的資料（避免週末無交易）
        test_date = datetime(2025, 7, 18)  # 週五
        
        print(f"🔍 測試爬取 {test_date.strftime('%Y-%m-%d')} 的 PE 資料...")
        
        result = crawl_pe(test_date)
        
        if result is not None and not result.empty:
            print(f"✅ PE 資料爬取成功！")
            print(f"   獲取資料筆數: {len(result)}")
            print(f"   資料欄位: {list(result.columns)}")
            
            # 保存測試資料
            result.to_pickle('pe_test.pkl')
            print(f"   測試資料已保存為 pe_test.pkl")
            
            return True
        else:
            print("⚠️ 爬取成功但無資料")
            return False
            
    except Exception as e:
        print(f"❌ 爬取失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 PE 爬蟲簡單測試")
    print("=" * 50)
    
    success = test_pe_crawl()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 測試成功！pe 爬蟲功能正常")
    else:
        print("❌ 測試失敗！")
    print("=" * 50)
