#!/usr/bin/env python3
import os
import sys
import json
import logging
import traceback
import sqlite3
from datetime import datetime, timedelta
import time
import re

import numpy as np
import pandas as pd
import pyqtgraph as pg
from pandas import ExcelWriter
from PyQt6.QtCore import Qt, QDate, QPointF, QRectF, QTimer, QThread, pyqtSignal, QThreadPool, QRunnable
from PyQt6.QtGui import QColor, QBrush, QPainter, QPicture, QPen, QFont
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QTabWidget, QGroupBox, QScrollArea, QMessageBox, QDialog, QLineEdit, QFileDialog,
    QListWidget, QComboBox, QCalendarWidget, QTableWidget, QTableWidgetItem,
    QStatusBar, QGridLayout, QSpinBox, QListWidgetItem, QProgressBar, QSplitter, QCheckBox,
    QDialogButtonBox, QFormLayout, QGraphicsRectItem, QDateEdit, QProgressDialog,
    QDoubleSpinBox
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='app.log',
    filemode='a'
)

# ===================== CandlestickItem =====================
class CandlestickItem(pg.GraphicsObject):
    """
    根據提供的數據繪製K線圖
    資料格式：每一列 [timestamp, open, close, low, high]
    """
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = QPicture()
        self.generate_picture()
        
    def generate_picture(self):
        """生成K線圖的繪圖指令"""
        painter = QPainter(self.picture)
        painter.setPen(pg.mkPen('k'))  # 黑色邊框
        w = 0.6  # K線寬度
        
        for i in range(len(self.data)):
            # 獲取OHLC數據
            open_price = float(self.data['Open'].iloc[i])
            high_price = float(self.data['High'].iloc[i])
            low_price = float(self.data['Low'].iloc[i])
            close_price = float(self.data['Close'].iloc[i])
            
            # 台股的顏色習慣：紅色表示上漲，綠色表示下跌
            if close_price > open_price:
                painter.setBrush(pg.mkBrush('r'))
                painter.drawRect(QRectF(i - w/2, open_price, w, close_price - open_price))
            else:
                painter.setBrush(pg.mkBrush('g'))
                painter.drawRect(QRectF(i - w/2, close_price, w, open_price - close_price))
            
            painter.drawLine(QPointF(i, low_price), QPointF(i, high_price))
        
        painter.end()
        
    def paint(self, painter, option, widget):
        painter.drawPicture(0, 0, self.picture)
        
    def boundingRect(self):
        return QRectF(self.picture.boundingRect())

# ===================== 策略參數與策略類別 =====================
class StrategyParams:
    def __init__(self, name="Default", description="",
                 ma240_days=240, trend_days=30,
                 ma60_days=60, ma20_days=20,
                 volume_short=3, volume_long=18, cross_days=3,
                 rsi_long=13, rsi_long_threshold=50,
                 rsi_short=6, rsi_short_threshold=70,
                 # 破底高反彈策略參數
                 break_bottom_min_percent=2, rebound_max_percent=15,
                 rebound_min_percent=5, volume_trend_days=5,
                 kline_pattern_check=True):
        self.name = name
        self.description = description
        self.ma240_days = ma240_days
        self.trend_days = trend_days
        self.ma60_days = ma60_days
        self.ma20_days = ma20_days
        self.volume_short = volume_short
        self.volume_long = volume_long
        self.cross_days = cross_days
        self.rsi_long = rsi_long
        self.rsi_long_threshold = rsi_long_threshold
        self.rsi_short = rsi_short
        self.rsi_short_threshold = rsi_short_threshold
        # 破底高反彈策略參數
        self.break_bottom_min_percent = break_bottom_min_percent  # 破底最小百分比
        self.rebound_max_percent = rebound_max_percent  # 反彈上限百分比
        self.rebound_min_percent = rebound_min_percent  # 反彈最小百分比
        self.volume_trend_days = volume_trend_days  # 成交量趨勢分析天數
        self.kline_pattern_check = kline_pattern_check  # 是否啟用K線形態檢查

    @staticmethod
    def default():
        return StrategyParams()

    def save(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.__dict__, f, ensure_ascii=False, indent=4)

    @classmethod
    def load(cls, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if "parameters" in data:
                params = data["parameters"]
            else:
                params = data
            return cls(
                name=data.get("name", "Default"),
                description=data.get("description", ""),
                ma240_days=params.get("ma240_days", 240),
                trend_days=params.get("trend_days", 30),
                ma60_days=params.get("ma60_days", 60),
                ma20_days=params.get("ma20_days", 20),
                volume_short=params.get("volume_short", 3),
                volume_long=params.get("volume_long", 18),
                cross_days=params.get("cross_days", 3),
                rsi_long=params.get("rsi_long", 13),
                rsi_long_threshold=params.get("rsi_long_threshold", 50),
                rsi_short=params.get("rsi_short", 6),
                rsi_short_threshold=params.get("rsi_short_threshold", 70),
                # 破底高反彈策略參數
                break_bottom_min_percent=params.get("break_bottom_min_percent", 2),
                rebound_max_percent=params.get("rebound_max_percent", 15),
                rebound_min_percent=params.get("rebound_min_percent", 5),
                volume_trend_days=params.get("volume_trend_days", 5),
                kline_pattern_check=params.get("kline_pattern_check", True)
            )
        except Exception as e:
            logging.error(f"載入策略參數時出錯: {e}")
            logging.error(traceback.format_exc())
            return cls.default()

class Strategy:
    def __init__(self, params):
        self.params = params

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        df['Signal'] = 0
        for i in range(1, len(df)):
            if df['MA5'].iloc[i] > df['MA20'].iloc[i] and df['MA5'].iloc[i-1] <= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = 1
            elif df['MA5'].iloc[i] < df['MA20'].iloc[i] and df['MA5'].iloc[i-1] >= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = -1
        return df

# ===================== 策略設定對話框 =====================
class StrategyDialog(QDialog):
    def __init__(self, params: StrategyParams = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("策略設置")
        self.setModal(True)
        layout = QVBoxLayout(self)
        
        # 策略名稱與描述
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("策略名稱:"))
        self.name_edit = QLineEdit()
        if params:
            self.name_edit.setText(params.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("策略描述:"))
        self.desc_edit = QLineEdit()
        if params:
            self.desc_edit.setText(params.description)
        desc_layout.addWidget(self.desc_edit)
        layout.addLayout(desc_layout)
        
        # 參數設置群組（示意，可根據需求增加設定項目）
        params_group = QGroupBox("參數設置")
        params_layout = QVBoxLayout(params_group)
        params_layout.addWidget(QLabel("240日線天數:"))
        self.ma240_days_input = QSpinBox()
        self.ma240_days_input.setValue(params.ma240_days if params else 240)
        params_layout.addWidget(self.ma240_days_input)
        params_layout.addWidget(QLabel("趨勢天數:"))
        self.trend_days_input = QSpinBox()
        self.trend_days_input.setValue(params.trend_days if params else 30)
        params_layout.addWidget(self.trend_days_input)
        params_layout.addWidget(QLabel("60日線天數:"))
        self.ma60_days_input = QSpinBox()
        self.ma60_days_input.setValue(params.ma60_days if params else 60)
        params_layout.addWidget(self.ma60_days_input)
        params_layout.addWidget(QLabel("20日線天數:"))
        self.ma20_days_input = QSpinBox()
        self.ma20_days_input.setValue(params.ma20_days if params else 20)
        params_layout.addWidget(self.ma20_days_input)
        params_layout.addWidget(QLabel("短期成交量天數:"))
        self.volume_short_input = QSpinBox()
        self.volume_short_input.setValue(params.volume_short if params else 3)
        params_layout.addWidget(self.volume_short_input)
        params_layout.addWidget(QLabel("長期成交量天數:"))
        self.volume_long_input = QSpinBox()
        self.volume_long_input.setValue(params.volume_long if params else 18)
        params_layout.addWidget(self.volume_long_input)
        params_layout.addWidget(QLabel("交叉天數:"))
        self.cross_days_input = QSpinBox()
        self.cross_days_input.setValue(params.cross_days if params else 3)
        params_layout.addWidget(self.cross_days_input)
        params_layout.addWidget(QLabel("長期RSI天數:"))
        self.rsi_long_input = QSpinBox()
        self.rsi_long_input.setValue(params.rsi_long if params else 13)
        params_layout.addWidget(self.rsi_long_input)
        params_layout.addWidget(QLabel("長期RSI閾值:"))
        self.rsi_long_threshold_input = QSpinBox()
        self.rsi_long_threshold_input.setValue(params.rsi_long_threshold if params else 50)
        params_layout.addWidget(self.rsi_long_threshold_input)
        params_layout.addWidget(QLabel("短期RSI天數:"))
        self.rsi_short_input = QSpinBox()
        self.rsi_short_input.setValue(params.rsi_short if params else 6)
        params_layout.addWidget(self.rsi_short_input)
        params_layout.addWidget(QLabel("短期RSI閾值:"))
        self.rsi_short_threshold_input = QSpinBox()
        self.rsi_short_threshold_input.setValue(params.rsi_short_threshold if params else 70)
        params_layout.addWidget(self.rsi_short_threshold_input)
        
        # 新增破底高反彈策略參數
        params_layout.addWidget(QLabel("破底最小百分比:"))
        self.break_bottom_min_percent_input = QDoubleSpinBox()
        self.break_bottom_min_percent_input.setRange(0.5, 10)
        self.break_bottom_min_percent_input.setSingleStep(0.5)
        self.break_bottom_min_percent_input.setValue(params.break_bottom_min_percent if params else 2)
        self.break_bottom_min_percent_input.setSuffix("%")
        params_layout.addWidget(self.break_bottom_min_percent_input)
        
        params_layout.addWidget(QLabel("反彈上限百分比:"))
        self.rebound_max_percent_input = QDoubleSpinBox()
        self.rebound_max_percent_input.setRange(5, 30)
        self.rebound_max_percent_input.setSingleStep(1)
        self.rebound_max_percent_input.setValue(params.rebound_max_percent if params else 15)
        self.rebound_max_percent_input.setSuffix("%")
        params_layout.addWidget(self.rebound_max_percent_input)
        
        params_layout.addWidget(QLabel("反彈最小百分比:"))
        self.rebound_min_percent_input = QDoubleSpinBox()
        self.rebound_min_percent_input.setRange(1, 10)
        self.rebound_min_percent_input.setSingleStep(0.5)
        self.rebound_min_percent_input.setValue(params.rebound_min_percent if params else 5)
        self.rebound_min_percent_input.setSuffix("%")
        params_layout.addWidget(self.rebound_min_percent_input)
        
        params_layout.addWidget(QLabel("成交量趨勢分析天數:"))
        self.volume_trend_days_input = QSpinBox()
        self.volume_trend_days_input.setRange(3, 10)
        self.volume_trend_days_input.setValue(params.volume_trend_days if params else 5)
        params_layout.addWidget(self.volume_trend_days_input)
        
        params_layout.addWidget(QLabel("啟用K線形態檢查:"))
        self.kline_pattern_check_input = QCheckBox()
        self.kline_pattern_check_input.setChecked(params.kline_pattern_check if params else True)
        params_layout.addWidget(self.kline_pattern_check_input)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存")
        save_button.clicked.connect(self.accept)
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def get_strategy_params(self) -> StrategyParams:
        return StrategyParams(
            name=self.name_edit.text(),
            description=self.desc_edit.text(),
            ma240_days=self.ma240_days_input.value(),
            trend_days=self.trend_days_input.value(),
            ma60_days=self.ma60_days_input.value(),
            ma20_days=self.ma20_days_input.value(),
            volume_short=self.volume_short_input.value(),
            volume_long=self.volume_long_input.value(),
            cross_days=self.cross_days_input.value(),
            rsi_long=self.rsi_long_input.value(),
            rsi_long_threshold=self.rsi_long_threshold_input.value(),
            rsi_short=self.rsi_short_input.value(),
            rsi_short_threshold=self.rsi_short_threshold_input.value(),
            # 破底高反彈策略參數
            break_bottom_min_percent=self.break_bottom_min_percent_input.value(),
            rebound_max_percent=self.rebound_max_percent_input.value(),
            rebound_min_percent=self.rebound_min_percent_input.value(),
            volume_trend_days=self.volume_trend_days_input.value(),
            kline_pattern_check=self.kline_pattern_check_input.isChecked()
        )

    def add_condition(self):
        """添加新條件"""
        dialog = ConditionDialog(self)
        if dialog.exec():
            condition = dialog.get_condition()
            self.conditions.append(condition)
            self.update_conditions_list()
    
    def edit_condition(self):
        """編輯選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        dialog = ConditionDialog(self, self.conditions[current_row])
        if dialog.exec():
            self.conditions[current_row] = dialog.get_condition()
            self.update_conditions_list()
    
    def remove_condition(self):
        """刪除選中的條件"""
        current_row = self.conditions_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "請先選擇一個條件")
            return
        
        self.conditions.pop(current_row)
        self.update_conditions_list()
    
    def get_conditions(self):
        """獲取所有條件"""
        return self.conditions

# ===================== 條件編輯對話框 =====================
class ConditionDialog(QDialog):
    """條件編輯對話框"""
    def __init__(self, parent=None, condition=None):
        super().__init__(parent)
        self.setWindowTitle("編輯條件")
        self.resize(500, 400)
        
        self.condition = condition or {}
        layout = QVBoxLayout(self)
        
        # 條件類型選擇
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("條件類型:"))
        self.condition_type = QComboBox()
        self.condition_type.addItem("均線位置", "ma_position")
        self.condition_type.addItem("均線趨勢", "ma_trend")
        self.condition_type.addItem("成交量增加", "volume_increase")
        self.condition_type.addItem("MACD信號", "macd")
        self.condition_type.addItem("RSI指標", "rsi")
        # 新增：破底後高反彈條件
        self.condition_type.addItem("破底反彈", "break_bottom_rebound")
        
        current_type = self.condition.get("type", "") if self.condition else ""
        index = self.condition_type.findData(current_type)
        if index >= 0:
            self.condition_type.setCurrentIndex(index)
        
        self.condition_type.currentIndexChanged.connect(self.update_condition_form)
        type_layout.addWidget(self.condition_type)
        layout.addLayout(type_layout)
        
        # 條件參數區域
        self.condition_form = QWidget()
        self.condition_form_layout = QFormLayout(self.condition_form)
        layout.addWidget(self.condition_form)
        
        # 按鈕
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.update_condition_form()
    
    def update_condition_form(self):
        while self.condition_form_layout.count():
            item = self.condition_form_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        condition_type = self.condition_type.currentData()
        
        if condition_type == "ma_position":
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            self.position_combo = QComboBox()
            self.position_combo.addItem("上方", "above")
            self.position_combo.addItem("下方", "below")
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.position_combo.findData(self.condition.get("position", "above"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            self.price_combo = QComboBox()
            self.price_combo.addItem("收盤價", "close")
            self.price_combo.addItem("開盤價", "open")
            if self.condition and self.condition.get("type") == "ma_position":
                index = self.price_combo.findData(self.condition.get("price", "close"))
                if index >= 0:
                    self.price_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("價格:", self.price_combo)
        
        elif condition_type == "ma_trend":
            self.ma_combo = QComboBox()
            for ma in ["MA5", "MA10", "MA20", "MA60", "MA120", "MA240"]:
                self.ma_combo.addItem(ma)
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.ma_combo.findText(self.condition.get("ma", "MA5"))
                if index >= 0:
                    self.ma_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("均線:", self.ma_combo)
            
            self.trend_combo = QComboBox()
            self.trend_combo.addItem("向上", "up")
            self.trend_combo.addItem("向下", "down")
            if self.condition and self.condition.get("type") == "ma_trend":
                index = self.trend_combo.findData(self.condition.get("trend", "up"))
                if index >= 0:
                    self.trend_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("趨勢:", self.trend_combo)
            
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "ma_trend" else 5)
            self.condition_form_layout.addRow("天數:", self.days_spin)
        
        elif condition_type == "volume_increase":
            self.days_spin = QSpinBox()
            self.days_spin.setRange(1, 60)
            self.days_spin.setValue(self.condition.get("days", 5) if self.condition and self.condition.get("type") == "volume_increase" else 5)
            self.condition_form_layout.addRow("比較天數:", self.days_spin)
            
            self.percent_spin = QDoubleSpinBox()
            self.percent_spin.setRange(0, 500)
            self.percent_spin.setSuffix("%")
            self.percent_spin.setValue(self.condition.get("percent", 20) if self.condition and self.condition.get("type") == "volume_increase" else 20)
            self.condition_form_layout.addRow("增加比例:", self.percent_spin)
        
        elif condition_type == "macd":
            self.signal_combo = QComboBox()
            self.signal_combo.addItem("金叉", "golden_cross")
            self.signal_combo.addItem("死叉", "death_cross")
            self.signal_combo.addItem("MACD在零線上", "above_zero")
            if self.condition and self.condition.get("type") == "macd":
                index = self.signal_combo.findData(self.condition.get("signal", "golden_cross"))
                if index >= 0:
                    self.signal_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("信號:", self.signal_combo)
        
        elif condition_type == "rsi":
            self.period_combo = QComboBox()
            self.period_combo.addItem("RSI(6)", 6)
            self.period_combo.addItem("RSI(14)", 14)
            self.period_combo.addItem("RSI(24)", 24)
            if self.condition and self.condition.get("type") == "rsi":
                index = self.period_combo.findData(self.condition.get("period", 14))
                if index >= 0:
                    self.period_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("週期:", self.period_combo)
            
            self.position_combo = QComboBox()
            self.position_combo.addItem("超賣區", "oversold")
            self.position_combo.addItem("超買區", "overbought")
            self.position_combo.addItem("中間區域", "middle")
            if self.condition and self.condition.get("type") == "rsi":
                index = self.position_combo.findData(self.condition.get("position", "oversold"))
                if index >= 0:
                    self.position_combo.setCurrentIndex(index)
            self.condition_form_layout.addRow("位置:", self.position_combo)
            
            self.min_spin = QSpinBox()
            self.min_spin.setRange(0, 100)
            self.min_spin.setValue(self.condition.get("min", 30) if self.condition and self.condition.get("type") == "rsi" else 30)
            self.condition_form_layout.addRow("超賣值 (<):", self.min_spin)
            
            self.max_spin = QSpinBox()
            self.max_spin.setRange(0, 100)
            self.max_spin.setValue(self.condition.get("max", 70) if self.condition and self.condition.get("type") == "rsi" else 70)
            self.condition_form_layout.addRow("超買值 (>):", self.max_spin)
        
        elif condition_type == "break_bottom_rebound":
            # 新增破底反彈條件選項
            self.lookback_spin = QSpinBox()
            self.lookback_spin.setRange(3, 30)
            default_lookback = self.condition.get("lookback", 10) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 10
            self.lookback_spin.setValue(default_lookback)
            self.condition_form_layout.addRow("回溯天數:", self.lookback_spin)
            
            self.rebound_spin = QDoubleSpinBox()
            self.rebound_spin.setRange(0, 100)
            self.rebound_spin.setSuffix("%")
            default_rebound = self.condition.get("rebound_threshold", 5) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 5
            self.rebound_spin.setValue(default_rebound)
            self.condition_form_layout.addRow("反彈幅度:", self.rebound_spin)
            
            self.rebound_max_spin = QDoubleSpinBox()
            self.rebound_max_spin.setRange(5, 30)
            self.rebound_max_spin.setSuffix("%")
            default_rebound_max = self.condition.get("rebound_max", 15) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 15
            self.rebound_max_spin.setValue(default_rebound_max)
            self.condition_form_layout.addRow("反彈上限:", self.rebound_max_spin)
            
            self.volume_mult_spin = QDoubleSpinBox()
            self.volume_mult_spin.setDecimals(1)
            self.volume_mult_spin.setRange(1, 10)
            default_volume_mult = self.condition.get("volume_multiplier", 2) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 2
            self.volume_mult_spin.setValue(default_volume_mult)
            self.condition_form_layout.addRow("成交量倍數:", self.volume_mult_spin)
            
            self.break_bottom_pct_spin = QDoubleSpinBox()
            self.break_bottom_pct_spin.setRange(0.5, 10)
            self.break_bottom_pct_spin.setSuffix("%")
            default_break_pct = self.condition.get("break_bottom_pct", 2) if self.condition and self.condition.get("type") == "break_bottom_rebound" else 2
            self.break_bottom_pct_spin.setValue(default_break_pct)
            self.condition_form_layout.addRow("破底百分比:", self.break_bottom_pct_spin)
            
            self.check_recent_low = QCheckBox("僅檢查反彈初期")
            self.check_recent_low.setChecked(self.condition.get("check_recent_low", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_recent_low)
            
            self.check_volume_trend = QCheckBox("檢查成交量趨勢")
            self.check_volume_trend.setChecked(self.condition.get("check_volume_trend", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_volume_trend)
            
            self.check_kline_pattern = QCheckBox("檢查K線形態")
            self.check_kline_pattern.setChecked(self.condition.get("check_kline_pattern", True) if self.condition and self.condition.get("type") == "break_bottom_rebound" else True)
            self.condition_form_layout.addRow("", self.check_kline_pattern)
    
    def get_condition(self):
        condition_type = self.condition_type.currentData()
        condition = {"type": condition_type}
        
        if condition_type == "ma_position":
            condition["ma"] = self.ma_combo.currentText()
            condition["position"] = self.position_combo.currentData()
            condition["price"] = self.price_combo.currentData()
        elif condition_type == "ma_trend":
            condition["ma"] = self.ma_combo.currentText()
            condition["trend"] = self.trend_combo.currentData()
            condition["days"] = self.days_spin.value()
        elif condition_type == "volume_increase":
            condition["days"] = self.days_spin.value()
            condition["percent"] = self.percent_spin.value()
        elif condition_type == "macd":
            condition["signal"] = self.signal_combo.currentData()
        elif condition_type == "rsi":
            condition["period"] = self.period_combo.currentData()
            condition["position"] = self.position_combo.currentData()
            condition["min"] = self.min_spin.value()
            condition["max"] = self.max_spin.value()
        elif condition_type == "break_bottom_rebound":
            condition["lookback"] = self.lookback_spin.value()
            condition["rebound_threshold"] = self.rebound_spin.value()
            condition["rebound_max"] = self.rebound_max_spin.value()
            condition["volume_multiplier"] = self.volume_mult_spin.value()
            condition["break_bottom_pct"] = self.break_bottom_pct_spin.value()
            condition["check_recent_low"] = self.check_recent_low.isChecked()
            condition["check_volume_trend"] = self.check_volume_trend.isChecked()
            condition["check_kline_pattern"] = self.check_kline_pattern.isChecked()
        
        return condition

# ===================== 主 GUI 類別 =====================
class StockScreenerGUI(QMainWindow):
    CONFIG_FILE = "app_config.json"
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("台股智能選股系統")
        self.setGeometry(100, 100, 1200, 800)
        
        self.config = self.load_config()
        self.db_connections = {}
        self.db_tables = {'price': 'stock_daily_data', 'pe': None}
        self.thread_pool = QThreadPool()
        self.period_map = {
            "2W": 10,
            "1M": 22,
            "2M": 44,
            "6M": 132,
            "1Y": 264,
            "2Y": 528,
        }
        
        self.app = QApplication.instance()
        
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout()
        
        self.init_status_bar()
        self.init_ui_components()
        self.init_database()
        
        self.strategy_name = "勝率73.45%"
        self.load_strategies()
        
        logging.info("GUI 初始化完成")
    
    def init_status_bar(self):
        self.status_bar = self.statusBar()
        self.db_status_label = QLabel("數據庫未連接")
        self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.db_status_label)
        self.reconnect_btn = QPushButton("重新連接數據庫")
        self.reconnect_btn.clicked.connect(self.reconnect_database)
        self.status_bar.addPermanentWidget(self.reconnect_btn)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def show_status(self, message, timeout=5000, is_error=False):
        self.status_bar.showMessage(message, timeout)
        if is_error:
            self.status_bar.setStyleSheet("color: red;")
        else:
            self.status_bar.setStyleSheet("")
        QApplication.processEvents()
    
    def update_db_status(self, connected=True):
        if connected and 'price' in self.db_connections:
            text = "價格DB: ✓"
            if 'pe' in self.db_connections:
                text += " | PE DB: ✓"
            self.db_status_label.setText(text)
            self.db_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.db_status_label.setText("數據庫未連接")
            self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
    
    def init_ui_components(self):
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(main_splitter)
        
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        
        strategy_group = QGroupBox("選股策略")
        strategy_layout = QVBoxLayout(strategy_group)
        
        strategy_control_layout = QGridLayout()
        strategy_control_layout.addWidget(QLabel("計算日期:"), 0, 0)
        self.strategy_date = QDateEdit()
        self.strategy_date.setDate(QDate.currentDate())
        self.strategy_date.setCalendarPopup(True)
        strategy_control_layout.addWidget(self.strategy_date, 0, 1)
        latest_date_btn = QPushButton("最近交易日")
        latest_date_btn.clicked.connect(self.set_latest_trading_date)
        strategy_control_layout.addWidget(latest_date_btn, 0, 2)
        strategy_control_layout.addWidget(QLabel("策略:"), 1, 0)
        self.strategy_combo = QComboBox()
        strategy_control_layout.addWidget(self.strategy_combo, 1, 1, 1, 2)
        strategy_btn_layout = QHBoxLayout()
        add_btn = QPushButton("新增")
        add_btn.clicked.connect(self.add_strategy)
        edit_btn = QPushButton("編輯")
        edit_btn.clicked.connect(self.edit_strategy)
        delete_btn = QPushButton("刪除")
        delete_btn.clicked.connect(self.delete_strategy)
        run_btn = QPushButton("執行選股")
        run_btn.clicked.connect(self.run_strategy)
        strategy_btn_layout.addWidget(add_btn)
        strategy_btn_layout.addWidget(edit_btn)
        strategy_btn_layout.addWidget(delete_btn)
        strategy_btn_layout.addWidget(run_btn)
        strategy_layout.addLayout(strategy_control_layout)
        strategy_layout.addLayout(strategy_btn_layout)
        left_layout.addWidget(strategy_group)
        
        stock_tabs = QTabWidget()
        all_stocks_page = QWidget()
        all_stocks_layout = QVBoxLayout(all_stocks_page)
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜尋股票...")
        self.search_input.textChanged.connect(self.filter_stocks)
        search_layout.addWidget(self.search_input)
        all_stocks_layout.addLayout(search_layout)
        self.all_stocks_list = QListWidget()
        self.all_stocks_list.setMinimumWidth(180)
        self.all_stocks_list.setFont(QFont("Microsoft JhengHei", 10))
        self.all_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        all_stocks_layout.addWidget(self.all_stocks_list)
        stock_tabs.addTab(all_stocks_page, "全部股票")
        filtered_stocks_page = QWidget()
        filtered_stocks_layout = QVBoxLayout(filtered_stocks_page)
        self.filtered_stocks_list = QListWidget()
        self.filtered_stocks_list.setMinimumWidth(180)
        self.filtered_stocks_list.setFont(QFont("Microsoft JhengHei", 10))
        self.filtered_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        filtered_stocks_layout.addWidget(self.filtered_stocks_list)
        stock_tabs.addTab(filtered_stocks_page, "選股結果")
        left_layout.addWidget(stock_tabs, 1)
        main_splitter.addWidget(left_panel)
        
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        self.tab_widget = QTabWidget()
        
        k_chart_page = QWidget()
        k_chart_layout = QVBoxLayout(k_chart_page)
        controls_layout = QHBoxLayout()
        controls_layout.addWidget(QLabel("週期:"))
        self.period_combo = QComboBox()
        for period in ["2W", "1M", "2M", "6M", "1Y", "2Y"]:
            self.period_combo.addItem(period)
        self.period_combo.setCurrentText("2M")
        self.period_combo.currentIndexChanged.connect(self.on_period_changed)
        controls_layout.addWidget(self.period_combo)
        controls_layout.addStretch()
        k_chart_layout.addLayout(controls_layout)
        self.chart_view = pg.PlotWidget()
        self.chart_view.setBackground('k')
        self.chart_view.showGrid(x=True, y=True, alpha=0.2)
        k_chart_layout.addWidget(self.chart_view)
        self.volume_view = pg.PlotWidget()
        self.volume_view.setBackground('k')
        self.volume_view.showGrid(x=True, y=True, alpha=0.2)
        k_chart_layout.addWidget(self.volume_view)
        self.volume_view.setMaximumHeight(150)
        self.chart_view.getViewBox().setXLink(self.volume_view.getViewBox())
        self.tab_widget.addTab(k_chart_page, "K線圖")
        
        result_page = QWidget()
        result_layout = QVBoxLayout(result_page)
        self.result_summary_label = QLabel("尚未執行選股策略")
        result_layout.addWidget(self.result_summary_label)
        filter_buttons_layout = QHBoxLayout()
        self.show_matching_btn = QPushButton("僅顯示符合條件股票")
        self.show_matching_btn.clicked.connect(self.filter_matching_results)
        self.restore_all_btn = QPushButton("顯示全部股票")
        self.restore_all_btn.clicked.connect(self.restore_all_results)
        self.export_excel_btn = QPushButton("導出Excel")
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        filter_buttons_layout.addWidget(self.show_matching_btn)
        filter_buttons_layout.addWidget(self.restore_all_btn)
        filter_buttons_layout.addWidget(self.export_excel_btn)
        result_layout.addLayout(filter_buttons_layout)
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(9)
        self.result_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價", "評分", "條件1", "條件2", "條件3", "條件4", "條件5"])
        self.result_table.doubleClicked.connect(self.on_result_double_clicked)
        
        # 添加排序功能
        self.result_table.setSortingEnabled(True)
        self.result_table.horizontalHeader().setSectionsClickable(True)
        
        # 添加排序控制按鈕
        sort_layout = QHBoxLayout()
        sort_layout.addWidget(QLabel("排序方式："))
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["評分高到低", "股價高到低", "股價低到高", "代碼順序"])
        self.sort_combo.currentIndexChanged.connect(self.sort_results)
        sort_layout.addWidget(self.sort_combo)
        sort_layout.addStretch()
        result_layout.addLayout(sort_layout)
        
        result_layout.addWidget(self.result_table)
        self.tab_widget.addTab(result_page, "選股結果")
        right_layout.addWidget(self.tab_widget)
        main_splitter.addWidget(right_panel)
        main_splitter.setSizes([300, 900])
        self.central_widget.setLayout(self.main_layout)
    
    def init_database(self):
        try:
            price_db_path = self.resolve_db_path('price')
            pe_db_path = self.resolve_db_path('pe')
            self.show_status("正在連接數據庫...")
            self.db_connections = {}
            self.db_tables = {'price': 'stock_daily_data', 'pe': None}
            logging.info(f"嘗試連接price數據庫: {price_db_path}")
            logging.info(f"嘗試連接pe數據庫: {pe_db_path}")
            if os.path.exists(price_db_path):
                self.db_connections['price'] = sqlite3.connect(price_db_path)
                logging.info(f"成功連接price數據庫: {price_db_path}")
                cursor = self.db_connections['price'].cursor()
                cursor.execute("PRAGMA table_info(stock_daily_data)")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"檢測到的欄位: {columns}")
                required_cols = ['stock_id', 'Open', 'High', 'Low', 'Close', 'Volume', 'date']
                missing = [col for col in required_cols if col not in columns]
                if missing:
                    logging.error(f"數據表缺少必要欄位: {missing}")
                    self.show_status(f"數據表缺少必要欄位: {missing}", is_error=True)
                else:
                    logging.info("數據表結構驗證通過")
                    self.load_stock_list()
                    self.update_db_status(True)
                    self.show_status("數據庫連接成功")
            else:
                logging.error(f"價格數據庫文件不存在: {price_db_path}")
                self.show_status(f"價格數據庫文件不存在: {price_db_path}", is_error=True)
        except Exception as e:
            self.show_status(f"數據庫連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def detect_db_tables(self, db_type):
        try:
            conn = self.db_connections[db_type]
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            if tables:
                self.db_tables[db_type] = tables[0][0]
                cursor.execute(f"PRAGMA table_info({self.db_tables[db_type]})")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"{db_type}數據庫表 {self.db_tables[db_type]} 的欄位: {columns}")
            return len(tables) > 0
        except Exception as e:
            logging.error(f"檢測{db_type}數據庫表失敗: {str(e)}")
            return False
    
    def load_stock_list(self):
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法載入股票清單", is_error=True)
                return
            conn = self.db_connections['price']
            table = self.db_tables['price']
            query = f"""
                SELECT DISTINCT stock_id, stock_name 
                FROM {table}
                WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                  AND LENGTH(stock_id) BETWEEN 4 AND 6
                ORDER BY 
                    CASE 
                        WHEN LENGTH(stock_id) = 4 THEN 1
                        WHEN LENGTH(stock_id) = 5 THEN 2
                        WHEN LENGTH(stock_id) = 6 THEN 3
                    END,
                    CAST(stock_id AS INTEGER)
            """
            logging.info(f"執行股票列表查詢: {query}")
            df = pd.read_sql_query(query, conn)
            logging.info(f"查詢到 {len(df)} 支股票")
            if df.empty:
                self.show_status("未找到股票資料", is_error=True)
                return
            self.all_stocks_list.clear()
            for _, row in df.iterrows():
                item_text = f"{row['stock_id']} {row['stock_name']}"
                self.all_stocks_list.addItem(item_text)
            self.show_status(f"載入了 {len(df)} 支股票")
        except Exception as e:
            self.show_status(f"載入股票列表失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def get_selected_stocks(self):
        selected = []
        for index in range(self.all_stocks_list.count()):
            item = self.all_stocks_list.item(index)
            if item.isSelected():
                selected.append(item.text())
        if not selected:
            for index in range(self.all_stocks_list.count()):
                item = self.all_stocks_list.item(index)
                selected.append(item.text())
        return selected
    
    def run_screener(self):
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法執行選股", is_error=True)
                return
            strategy = self.strategy_combo.currentData()
            stock_ids = self.get_selected_stocks()
            if not stock_ids:
                self.show_status("無可用股票", is_error=True)
                return
                
            # 初始化評分數據存儲
            self.stock_score_data = {}
                
            self.show_status(f"正在分析 {len(stock_ids)} 支股票...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(stock_ids))
            self.progress_bar.setValue(0)
            self.result_table.setRowCount(0)
            current_date = self.strategy_date.date().toString("yyyy-MM-dd")
            current_date = datetime.strptime(current_date, "%Y-%m-%d")
            logging.info(f"當前計算日期: {current_date}")
            delisting_threshold = 15
            special_delisted_stocks = ['8406']
            logging.info(f"特別處理已下市股票列表: {special_delisted_stocks}")
            logging.info(f"執行選股日期: {current_date.strftime('%Y-%m-%d')}，將排除超過{delisting_threshold}天未交易的股票")
            results = []
            excluded_count = 0
            for idx, stock_id in enumerate(stock_ids):
                self.progress_bar.setValue(idx + 1)
                self.show_status(f"分析中: {stock_id} ({idx+1}/{len(stock_ids)})")
                QApplication.processEvents()
                try:
                    data = self.fetch_stock_data(stock_id)
                    # 添加股票代碼到數據中，方便後續處理
                    if not data.empty:
                        data['stock_id'] = stock_id
                        
                    if not data.empty:
                        if stock_id in special_delisted_stocks:
                            logging.warning(f"股票 {stock_id} 已被標記為下市股票，直接排除")
                            excluded_count += 1
                            continue
                        try:
                            last_trading_date = data['date'].max()
                            if not isinstance(last_trading_date, datetime):
                                last_trading_date = pd.to_datetime(last_trading_date)
                            date_diff = (current_date - last_trading_date).days
                            logging.info(f"股票 {stock_id} 日期比較 - 最後交易日: {last_trading_date}，計算日期: {current_date}，相差: {date_diff}天")
                            if date_diff > delisting_threshold:
                                logging.warning(f"股票 {stock_id} 已沒有交易，最後交易日: {last_trading_date}，相差: {date_diff}天")
                                excluded_count += 1
                                continue
                            if last_trading_date < current_date and date_diff <= delisting_threshold:
                                logging.info(f"股票 {stock_id} 最後交易日: {last_trading_date} 早於計算日期: {current_date}，但相差僅 {date_diff} 天，仍納入分析")
                        except Exception as e:
                            logging.error(f"處理股票 {stock_id} 的日期時出錯: {str(e)}")
                            excluded_count += 1
                            continue
                            
                        # 應用策略並收集評分數據
                        if self.apply_strategy(data):
                            # 獲取股票名稱
                            stock_name = 'N/A'
                            for j in range(self.all_stocks_list.count()):
                                item_text = self.all_stocks_list.item(j).text()
                                if item_text.startswith(stock_id):
                                    stock_name = item_text[len(stock_id):].strip()
                                    break
                                    
                            # 構建結果數據，包含評分所需信息
                            result_data = {
                                '股票代碼': stock_id,
                                '股票名稱': stock_name,
                                '收盤價': data['Close'].iloc[-1],
                                '條件符合': '是'
                            }
                            
                            # 添加評分所需數據
                            if stock_id in self.stock_score_data:
                                score_data = self.stock_score_data[stock_id]
                                result_data.update({
                                    '最低價': score_data.get('最低價', 0),
                                    '前期低點': score_data.get('前期低點', 0),
                                    '破底程度': score_data.get('破底程度', 0),
                                    '反彈幅度': score_data.get('反彈幅度', 0),
                                    '成交量比例': score_data.get('成交量比例', 0),
                                    '額外條件數': score_data.get('額外條件數', 0),
                                    '股票數據': score_data.get('股票數據', None)
                                })
                                
                            results.append(result_data)
                except Exception as e:
                    logging.error(f"處理 {stock_id} 時出錯: {str(e)}")
            self.show_results(results)
            self.progress_bar.setVisible(False)
            self.show_status(f"選股完成，找到 {len(results)} 支符合條件的股票，排除 {excluded_count} 支已沒交易的股票")
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.show_status(f"選股過程出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def fetch_stock_data(self, stock_id, days=90):
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接", is_error=True)
                return pd.DataFrame()
            query = f"""
            SELECT date, Open, High, Low, Close, Volume
            FROM {self.db_tables['price']}
            WHERE stock_id = ?
            ORDER BY date DESC
            LIMIT ?
            """
            cursor = self.db_connections['price'].cursor()
            cursor.execute(query, (stock_id, days))
            data = cursor.fetchall()
            cursor.close()
            if not data:
                return pd.DataFrame()
            df = pd.DataFrame(data, columns=['date', 'Open', 'High', 'Low', 'Close', 'Volume'])
            df['date'] = pd.to_datetime(df['date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col])
            df = df.sort_values('date').reset_index(drop=True)
            df = self.calculate_indicators(df)
            return df
        except Exception as e:
            self.show_status(f"獲取股票數據時出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
            return pd.DataFrame()
    
    def apply_strategy(self, data, strategy=None):
        if data.empty or len(data) < 20:
            return False
            
        # 破底高反彈策略
        if strategy is None or strategy == 'break_bottom_rebound':
            # 計算基本指標
            if 'MA5' not in data.columns:
                data['MA5'] = data['Close'].rolling(5).mean()
            if 'MA10' not in data.columns:
                data['MA10'] = data['Close'].rolling(10).mean()
            if 'MA20' not in data.columns:
                data['MA20'] = data['Close'].rolling(20).mean()
            if 'RSI6' not in data.columns:
                delta = data['Close'].diff()
                gain = delta.where(delta > 0, 0).rolling(window=6).mean()
                loss = -delta.where(delta < 0, 0).rolling(window=6).mean()
                rs = gain / loss
                data['RSI6'] = 100 - (100 / (1 + rs))
            if 'RSI14' not in data.columns:
                delta = data['Close'].diff()
                gain = delta.where(delta > 0, 0).rolling(window=14).mean()
                loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
                rs = gain / loss
                data['RSI14'] = 100 - (100 / (1 + rs))
                
            if len(data) < 30:  # 需要足夠的數據來分析
                return False
                
            # 獲取策略參數
            lookback = 10
            rebound_threshold = 5
            volume_multiplier = 2
            break_bottom_min_percent = 2
            rebound_max_percent = 15
            volume_trend_days = 5
            
            # 1. 確認是否真正破底
            recent_data = data.iloc[-lookback:]
            previous_data = data.iloc[-lookback-20:-lookback] if len(data) >= lookback+20 else data.iloc[0:-lookback]
            
            # 找到近期最低價
            low_price = recent_data['Close'].min()
            low_price_idx = recent_data['Close'].idxmin()
            
            # 檢查是否為近期新低
            previous_low = previous_data['Close'].min()
            is_new_low = low_price < previous_low * (1 - break_bottom_min_percent/100)
            
            # 檢查低點是否是近期形成的
            days_since_low = len(recent_data) - recent_data.index.get_loc(low_price_idx) - 1
            is_recent_low = days_since_low <= 5
            
            # 2. 確認反彈強度與上限
            current_close = data.iloc[-1]['Close']
            rebound_pct = (current_close - low_price) / low_price * 100 if low_price > 0 else 0
            
            # 檢查反彈是否在合理範圍內
            price_condition = rebound_pct >= rebound_threshold and rebound_pct <= rebound_max_percent
            
            # 3. 確認成交量放大與趨勢
            volume_trend_data = data.iloc[-volume_trend_days:]['Volume']
            volume_trend_increasing = False
            if len(volume_trend_data) >= 3:
                x = np.arange(len(volume_trend_data))
                y = volume_trend_data.values
                slope = np.polyfit(x, y, 1)[0]
                volume_trend_increasing = slope > 0
            
            avg_volume = recent_data.iloc[:-1]['Volume'].mean()
            today_volume = data.iloc[-1]['Volume']
            volume_ratio = today_volume / avg_volume if avg_volume > 0 else 1
            volume_condition = volume_ratio >= volume_multiplier
            
            # 4. 確認反轉信號 - 使用RSI指標
            rsi_condition = False
            if 'RSI6' in data.columns and 'RSI14' in data.columns:
                current_rsi6 = data.iloc[-1]['RSI6']
                current_rsi14 = data.iloc[-1]['RSI14']
                prev_rsi6 = data.iloc[-2]['RSI6'] if len(data) > 2 else 50
                
                # RSI從超賣區回升
                if current_rsi6 > prev_rsi6 and current_rsi6 > 30 and prev_rsi6 < 30:
                    rsi_condition = True
                # 或RSI形成底背離
                elif low_price_idx < len(data) - 3:
                    rsi_at_low = data.iloc[low_price_idx]['RSI14']
                    if current_rsi14 > rsi_at_low + 5:
                        rsi_condition = True
            
            # 5. 確認K線形態
            k_line_condition = False
            recent_candles = data.iloc[-3:]
            
            # 檢查長下影線
            for i, candle in recent_candles.iterrows():
                if candle['Low'] < candle['Open'] and candle['Low'] < candle['Close']:
                    lower_shadow = min(candle['Open'], candle['Close']) - candle['Low']
                    body = abs(candle['Open'] - candle['Close'])
                    if lower_shadow > body * 1.5:
                        k_line_condition = True
                        break
            
            # 檢查其他看漲形態
            if not k_line_condition and len(data) >= 3:
                last_candle = data.iloc[-1]
                if last_candle['Close'] > last_candle['Open']:
                    body = last_candle['Close'] - last_candle['Open']
                    upper_shadow = last_candle['High'] - last_candle['Close']
                    lower_shadow = last_candle['Open'] - last_candle['Low']
                    if lower_shadow > 2 * body and upper_shadow < 0.5 * body:
                        k_line_condition = True
            
            # 6. 檢查是否已經開始形成上升趨勢
            trend_condition = False
            if len(data) >= 5 and 'MA5' in data.columns and 'MA10' in data.columns:
                ma5_current = data.iloc[-1]['MA5']
                ma5_prev = data.iloc[-2]['MA5']
                ma10_current = data.iloc[-1]['MA10']
                
                if ma5_current > ma5_prev and current_close > ma5_current:
                    trend_condition = True
                elif current_close > ma10_current and ma5_current > ma5_prev:
                    trend_condition = True
            
            # 綜合評估
            basic_condition = price_condition and volume_condition
            
            # 輔助條件
            extra_conditions = [is_new_low, is_recent_low, rsi_condition, k_line_condition, trend_condition, volume_trend_increasing]
            extra_condition_count = sum(1 for c in extra_conditions if c)
            
            # 收集評分所需數據
            if basic_condition and extra_condition_count >= 2:
                # 將評分所需的數據添加到全局變量中
                stock_id = data.iloc[-1].get('stock_id', '')
                if hasattr(self, 'stock_score_data') and isinstance(self.stock_score_data, dict):
                    self.stock_score_data[stock_id] = {
                        '股票數據': data,
                        '最低價': low_price,
                        '前期低點': previous_low,
                        '破底程度': (previous_low - low_price) / previous_low * 100 if is_new_low else 0,
                        '反彈幅度': rebound_pct,
                        '成交量比例': volume_ratio,
                        '額外條件數': extra_condition_count
                    }
                return True
            return False
            
        elif strategy == 'ma_cross':
            data['MA5'] = data['Close'].rolling(5).mean()
            data['MA20'] = data['Close'].rolling(20).mean()
            if len(data) < 2:
                return False
            current = data.iloc[-1]
            previous = data.iloc[-2]
            cross_up = previous['MA5'] <= previous['MA20'] and current['MA5'] > current['MA20']
            return cross_up
        elif strategy == 'volume_surge':
            if len(data) < 5:
                return False
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].iloc[-6:-1].mean()
            return current_volume > avg_volume * 2
        return False
    
    def calculate_score(self, stock_data, low_price, previous_low, rebound_pct, volume_ratio, extra_condition_count):
        """計算破底高反彈策略的綜合評分 (0-100分)"""
        score = 0
        
        # 1. 破底程度與時機 (0-25分)
        if low_price < previous_low:
            break_pct = (previous_low - low_price) / previous_low * 100
            # 破底幅度給分
            bottom_score = min(15, break_pct * 3)  # 每破底1%給3分，最高15分
            
            # 檢查是否為近期破底
            if stock_data is not None and len(stock_data) >= 5:
                recent_lows = stock_data['Low'].tail(5)
                if low_price == recent_lows.min():
                    bottom_score += 10  # 如果是近期最低點，額外加10分
            
            score += bottom_score
        
        # 2. 反彈階段評分 (0-35分)
        if rebound_pct > 0:
            # 反彈初期給最高分，反彈越多分數遞減
            if rebound_pct <= 3:  # 反彈<=3%，最佳進場時機
                rebound_score = 35
            elif rebound_pct <= 5:  # 反彈3-5%，次佳進場時機
                rebound_score = 30
            elif rebound_pct <= 8:  # 反彈5-8%，可接受進場時機
                rebound_score = 20
            elif rebound_pct <= 12:  # 反彈8-12%，較晚進場
                rebound_score = 10
            else:  # 反彈>12%，不建議進場
                rebound_score = 0
            score += rebound_score
        
        # 3. 成交量增幅與趨勢 (0-20分)
        volume_score = 0
        if volume_ratio >= 1.5:
            # 基礎分數
            volume_score = min(10, (volume_ratio - 1.5) * 10)  # 從1.5倍開始計分
            
            # 檢查成交量趨勢
            if stock_data is not None and len(stock_data) >= 5:
                recent_volumes = stock_data['Volume'].tail(5)
                avg_volume = recent_volumes.mean()
                if recent_volumes.iloc[-1] > avg_volume * 1.2:  # 當日成交量大於5日平均的1.2倍
                    volume_score += 5
                if recent_volumes.iloc[-1] > recent_volumes.iloc[-2]:  # 成交量較前一日增加
                    volume_score += 5
        
        score += min(20, volume_score)  # 成交量最高20分
        
        # 4. 技術指標確認度 (0-20分)
        # 調整為最高20分，因為反彈階段更重要
        indicator_score = min(20, extra_condition_count * 7)  # 每個指標7分
        score += indicator_score
        
        return round(score)  # 返回0-100的整數分數
    
    def sort_results(self):
        """根據選擇的方式對結果進行排序"""
        sort_method = self.sort_combo.currentText()
        rows = self.result_table.rowCount()
        if rows <= 1:
            return
            
        # 獲取所有行的數據
        table_data = []
        for row in range(rows):
            row_data = {}
            # 獲取股票代碼
            code_item = self.result_table.item(row, 0)
            row_data['code'] = code_item.text() if code_item else ""
            
            # 獲取股價
            price_item = self.result_table.item(row, 2)
            try:
                row_data['price'] = float(price_item.text()) if price_item else 0
            except ValueError:
                row_data['price'] = 0
                
            # 獲取評分
            score_item = self.result_table.item(row, 3)
            try:
                row_data['score'] = int(score_item.text()) if score_item else 0
            except ValueError:
                row_data['score'] = 0
                
            # 保存行的所有單元格
            row_data['items'] = [self.result_table.item(row, col) for col in range(self.result_table.columnCount())]
            table_data.append(row_data)
        
        # 根據選擇的方式排序
        if sort_method == "評分高到低":
            table_data.sort(key=lambda x: x['score'], reverse=True)
        elif sort_method == "股價高到低":
            table_data.sort(key=lambda x: x['price'], reverse=True)
        elif sort_method == "股價低到高":
            table_data.sort(key=lambda x: x['price'])
        elif sort_method == "代碼順序":
            table_data.sort(key=lambda x: x['code'])
        
        # 重新填充表格
        for row, data in enumerate(table_data):
            for col, item in enumerate(data['items']):
                if item:
                    self.result_table.setItem(row, col, QTableWidgetItem(item.text()))
                    # 保持單元格的背景顏色
                    if col == 3:  # 評分列
                        try:
                            score = int(item.text())
                            self.apply_score_color(row, col, score)
                        except ValueError:
                            pass
    
    def apply_score_color(self, row, col, score):
        """根據評分設置不同的背景顏色"""
        item = self.result_table.item(row, col)
        if not item:
            return
            
        if score >= 80:  # 優秀
            item.setBackground(QBrush(QColor(0, 180, 0)))  # 綠色
            item.setForeground(QBrush(QColor(255, 255, 255)))  # 白色文字
        elif score >= 60:  # 良好
            item.setBackground(QBrush(QColor(144, 238, 144)))  # 淺綠色
        elif score >= 40:  # 一般
            item.setBackground(QBrush(QColor(255, 255, 0)))  # 黃色
        else:  # 較差
            item.setBackground(QBrush(QColor(255, 165, 0)))  # 橙色
    
    def show_results(self, results):
        self.result_table.setRowCount(0)
        if not results:
            return
            
        self.result_table.setSortingEnabled(False)  # 暫時禁用排序功能以提高性能
        self.result_table.setRowCount(len(results))
        
        # 初始化original_rows_visibility，確保所有行都是可見的
        self.original_rows_visibility = []
        
        for row, stock in enumerate(results):
            # 基本信息
            self.result_table.setItem(row, 0, QTableWidgetItem(stock['股票代碼']))
            self.result_table.setItem(row, 1, QTableWidgetItem(stock.get('股票名稱', 'N/A')))
            self.result_table.setItem(row, 2, QTableWidgetItem(str(stock['收盤價'])))
            
            # 計算評分 (如果有必要的數據)
            score = 0
            if '破底程度' in stock and '反彈幅度' in stock and '成交量比例' in stock and '額外條件數' in stock:
                score = self.calculate_score(
                    stock_data=stock.get('股票數據', None),
                    low_price=stock.get('最低價', 0),
                    previous_low=stock.get('前期低點', 0),
                    rebound_pct=stock.get('反彈幅度', 0),
                    volume_ratio=stock.get('成交量比例', 0),
                    extra_condition_count=stock.get('額外條件數', 0)
                )
            
            # 顯示評分
            score_item = QTableWidgetItem(str(score))
            self.result_table.setItem(row, 3, score_item)
            self.apply_score_color(row, 3, score)
            
            # 顯示條件符合情況
            self.result_table.setItem(row, 4, QTableWidgetItem("符合"))
            
            # 將每一行標記為可見
            self.original_rows_visibility.append(True)
            self.result_table.setRowHidden(row, False)
        
        # 根據評分排序
        self.result_table.setSortingEnabled(True)
        if self.sort_combo.currentText() == "評分高到低":
            self.result_table.sortItems(3, Qt.SortOrder.DescendingOrder)
    
    def resolve_db_path(self, db_type):
        if db_type == 'price':
            return os.path.join('D:/Finlab/history/tables/price.db')
        elif db_type == 'pe':
            return os.path.join('D:/Finlab/history/tables/pe_data.db')
        else:
            db_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db')
            os.makedirs(db_folder, exist_ok=True)
            return os.path.join(db_folder, f'{db_type}.db')
    
    def load_config(self):
        if os.path.exists(self.CONFIG_FILE):
            try:
                with open(self.CONFIG_FILE, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_config(self):
        with open(self.CONFIG_FILE, 'w') as f:
            json.dump(self.config, f, indent=4)
    
    def reconnect_database(self):
        try:
            self.close_db_connections()
            self.init_database()
        except Exception as e:
            self.show_status(f"重新連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def closeEvent(self, event):
        for db in self.db_connections.values():
            try:
                db.close()
            except:
                pass
        try:
            self.close_db_connections()
            logging.info("正常關閉應用程序")
        except Exception as e:
            logging.error(f"關閉時發生錯誤: {str(e)}")
            logging.error(traceback.format_exc())
        super().closeEvent(event)
    
    def on_stock_double_clicked(self, item):
        stock_id = item.text().split()[0]
        self.tab_widget.setCurrentIndex(0)
        self.plot_stock(stock_id)
    
    def on_period_changed(self):
        current_item = self.all_stocks_list.currentItem()
        if current_item:
            stock_id = current_item.text().split()[0]
            self.plot_stock(stock_id)
    
    def plot_stock(self, stock_id):
        try:
            if not stock_id:
                return
            self.show_status(f"載入 {stock_id} 的數據...")
            period_text = self.period_combo.currentText()
            days = self.period_map.get(period_text, 44)
            extra_days = 240
            df = self.fetch_stock_data(stock_id, days + extra_days)
            if df.empty:
                self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
                return
            display_df = df.tail(days) if len(df) > days else df
            self.chart_view.clear()
            self.volume_view.clear()
            stock_name = ""
            for j in range(self.all_stocks_list.count()):
                item_text = self.all_stocks_list.item(j).text()
                if item_text.startswith(stock_id):
                    stock_name = item_text[len(stock_id):].strip()
                    break
            if len(display_df) > 0:
                dates = [dt.strftime('%Y-%m-%d') for dt in display_df['date']]
                title = f"{stock_id} {stock_name} K線圖 (最後交易日: {dates[-1]})"
                self.chart_view.setTitle(title, color='w', size='16pt')
                candlestick = CandlestickItem(display_df)
                self.chart_view.addItem(candlestick)
                ma_legend = []
                for ma_days, color, name in [
                    (5, 'w', '週線MA5'), 
                    (20, 'y', '月線MA20'),
                    (60, 'c', '季線MA60'),
                    (120, 'm', '半年線MA120'),
                    (240, 'g', '年線MA240')
                ]:
                    if f'MA{ma_days}' in display_df.columns:
                        ma_series = display_df[f'MA{ma_days}'].dropna()
                        if not ma_series.empty:
                            x = np.array(range(len(display_df) - len(ma_series), len(display_df)))
                            y = ma_series.values
                            ma_plot = self.chart_view.plot(x, y, pen=pg.mkPen(color, width=1), name=name)
                            ma_legend.append((ma_plot, name))
                if 'BB_upper' in display_df.columns and 'BB_middle' in display_df.columns and 'BB_lower' in display_df.columns:
                    upper_series = display_df['BB_upper'].dropna()
                    if not upper_series.empty:
                        x = np.array(range(len(display_df) - len(upper_series), len(display_df)))
                        y = upper_series.values
                        upper_plot = self.chart_view.plot(x, y, pen=pg.mkPen('r', width=1, style=Qt.PenStyle.DashLine), name='布林上軌')
                        ma_legend.append((upper_plot, '布林上軌'))
                    middle_series = display_df['BB_middle'].dropna()
                    if not middle_series.empty:
                        ma_legend.append((ma_legend[1][0], '布林中軌(MA20)'))
                    lower_series = display_df['BB_lower'].dropna()
                    if not lower_series.empty:
                        x = np.array(range(len(display_df) - len(lower_series), len(display_df)))
                        y = lower_series.values
                        lower_plot = self.chart_view.plot(x, y, pen=pg.mkPen('b', width=1, style=Qt.PenStyle.DashLine), name='布林下軌')
                        ma_legend.append((lower_plot, '布林下軌'))
                if ma_legend:
                    legend = pg.LegendItem(offset=(50, 10))
                    legend.setParentItem(self.chart_view.graphicsItem())
                    for item, name in ma_legend:
                        legend.addItem(item, name)
                self.volume_view.clear()
                volume_data = display_df['Volume'].values / 1000
                for i in range(len(display_df)):
                    if df['Close'].iloc[i] > df['Open'].iloc[i]:
                        color = (255, 0, 0, 180)
                    else:
                        color = (0, 255, 0, 180)
                    vol_bar = pg.BarGraphItem(
                        x=[i], height=[volume_data[i]], width=0.8, 
                        brush=color, pen=color
                    )
                    self.volume_view.addItem(vol_bar)
                volume_axis = self.volume_view.getAxis('left')
                volume_axis.setLabel('成交量', units='張')
                volume_axis.enableAutoSIPrefix(False)
                def volume_axis_formatter(values, scale, spacing):
                    return [(value, f"{int(value):,}") for value in values]
                volume_axis.tickFormatter = volume_axis_formatter
                self.chart_view.getAxis('bottom').setStyle(showValues=False)
                tick_step = len(display_df) // 10 if len(display_df) > 100 else max(1, len(display_df) // 5)
                ticks = [(i, dates[i]) for i in range(0, len(display_df), tick_step)]
                if len(display_df) - 1 not in [t[0] for t in ticks]:
                    ticks.append((len(display_df) - 1, dates[-1]))
                self.volume_view.getAxis('bottom').setTicks([ticks])
                vLine = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('w', width=0.8))
                hLine = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('w', width=0.8))
                self.chart_view.addItem(vLine, ignoreBounds=True)
                self.chart_view.addItem(hLine, ignoreBounds=True)
                scatter = pg.ScatterPlotItem(size=10, pen=pg.mkPen('w', width=2), brush=pg.mkBrush(255, 255, 0, 200))
                self.chart_view.addItem(scatter)
                scatter.setZValue(1500)
                label = pg.TextItem(html='', border=pg.mkPen('w', width=1), fill=(0, 0, 0, 200))
                self.chart_view.addItem(label)
                label.hide()
                self._last_x = None
                self._last_html = None
                def mouseMoved(evt):
                    try:
                        pos = evt[0]
                        if self.chart_view.sceneBoundingRect().contains(pos):
                            mousePoint = self.chart_view.getViewBox().mapSceneToView(pos)
                            x = mousePoint.x()
                            x = max(0, min(round(x), len(display_df)-1))
                            x_int = int(x)
                            vLine.setPos(x)
                            hLine.setPos(mousePoint.y())
                            vLine.setVisible(True)
                            hLine.setVisible(True)
                            close = display_df['Close'].iloc[x_int]
                            scatter.setData([x_int], [close])
                            if self._last_x != x_int:
                                self._last_x = x_int
                                change_pct = 0.0
                                change_color = "white"
                                if x_int > 0:
                                    prev_close = display_df['Close'].iloc[x_int-1]
                                    change_pct = (close - prev_close) / prev_close * 100
                                    change_color = "red" if change_pct >= 0 else "green"
                                current_volume = display_df['Volume'].iloc[x_int]
                                volume_value = close * current_volume
                                date_str = display_df['date'].iloc[x_int].strftime('%Y-%m-%d')
                                html = f"""
                                <div style='background-color:rgba(0,0,0,200);padding:5px;'>
                                <span style='color:white;font-weight:bold;'>{date_str}</span><br>
                                <span style='color:white;'>開盤: {display_df['Open'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>最高: {display_df['High'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>最低: {display_df['Low'].iloc[x_int]:.2f}</span><br>
                                <span style='color:white;'>收盤: {close:.2f}</span><br>
                                <span style='color:{change_color};'>漲跌幅: {change_pct:+.2f}%</span><br>
                                <span style='color:white;'>成交量: {int(current_volume/1000):,}張</span><br>
                                <span style='color:white;'>成交額: {volume_value/10000:.2f}萬</span><br>
                                """
                                for ma in [5, 20, 60, 120, 240]:
                                    ma_col = f'MA{ma}'
                                    if ma_col in display_df.columns and not pd.isna(display_df[ma_col].iloc[x_int]):
                                        html += f"<span style='color:white;'>{ma_col}: {display_df[ma_col].iloc[x_int]:.2f}</span><br>"
                                for rsi_period in [6, 13]:
                                    rsi_col = f'RSI{rsi_period}'
                                    if rsi_col in display_df.columns and not pd.isna(display_df[rsi_col].iloc[x_int]):
                                        rsi_value = display_df[rsi_col].iloc[x_int]
                                        color = "red" if rsi_value > 70 else "green" if rsi_value < 30 else "white"
                                        html += f"<span style='color:{color};'>{rsi_col}: {rsi_value:.2f}</span><br>"
                                html += "</div>"
                                self._last_html = html
                                label.setHtml(html)
                            label.setAnchor((0, 0))
                            x_pos = x_int - 15 if x_int > len(display_df)/2 else x_int + 5
                            label.setPos(x_pos, mousePoint.y())
                            label.show()
                        else:
                            vLine.setVisible(False)
                            hLine.setVisible(False)
                            scatter.setData([], [])
                            label.hide()
                    except Exception as e:
                        logging.error(f"滑鼠事件錯誤: {str(e)}")
                        logging.error(traceback.format_exc())
                self.mouse_proxy = pg.SignalProxy(self.chart_view.scene().sigMouseMoved, rateLimit=60, slot=mouseMoved)
                self.setWindowTitle(f"台股智能選股系統 - {stock_id}")
                self.show_status(f"已載入 {stock_id} 的K線圖")
                if not display_df.empty:
                    price_min = display_df['Low'].min()
                    price_max = display_df['High'].max()
                    price_range = price_max - price_min
                    price_padding = price_range * 0.15
                    y_min = max(0, price_min - price_padding)
                    y_max = price_max + price_padding
                    self.chart_view.setYRange(y_min, y_max)
                if not display_df.empty:
                    vol_max = display_df['Volume'].max() / 1000
                    vol_padding_top = vol_max * 0.2
                    self.volume_view.setYRange(0, vol_max + vol_padding_top)
                buy_signals = display_df[(display_df['K'] < 20) & (display_df['K'].notna())]
                buy_points = []
                for idx in buy_signals.index:
                    if 0 <= idx < len(display_df):
                        y_pos = display_df['Close'].iloc[idx]
                        buy_points.append({'pos': (idx, y_pos), 'data': 1})
                sell_signals = display_df[(display_df['K'] > 80) & (display_df['K'].notna())]
                sell_points = []
                for idx in sell_signals.index:
                    if 0 <= idx < len(display_df):
                        y_pos = display_df['Close'].iloc[idx]
                        sell_points.append({'pos': (idx, y_pos), 'data': 1})
                buy_scatter = pg.ScatterPlotItem(
                    size=14,
                    pen=pg.mkPen('w'),
                    brush=pg.mkBrush(0, 255, 0, 180),
                    symbol='o',
                    pxMode=True
                )
                if buy_points:
                    buy_scatter.addPoints(buy_points)
                    buy_scatter.setZValue(1000)
                    self.chart_view.addItem(buy_scatter)
                sell_scatter = pg.ScatterPlotItem(
                    size=14,
                    pen=pg.mkPen('r', width=2),
                    brush=pg.mkBrush(255, 0, 0, 0),
                    symbol='x',
                    pxMode=True
                )
                if sell_points:
                    sell_scatter.addPoints(sell_points)
                    sell_scatter.setZValue(1000)
                    self.chart_view.addItem(sell_scatter)
                if buy_points or sell_points:
                    legend = pg.LegendItem(offset=(50, 50))
                    if buy_points:
                        legend.addItem(buy_scatter, 'K<20 買入信號')
                    if sell_points:
                        legend.addItem(sell_scatter, 'K>80 賣出信號')
                    legend.setParentItem(self.chart_view.graphicsItem())
            else:
                self.show_status(f"無法繪製 {stock_id} 的K線圖：數據不足", is_error=True)
        except Exception as e:
            self.show_status(f"繪製K線圖時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def filter_stocks(self):
        search_text = self.search_input.text().lower()
        if not search_text:
            for i in range(self.all_stocks_list.count()):
                self.all_stocks_list.item(i).setHidden(False)
            return
        for i in range(self.all_stocks_list.count()):
            item = self.all_stocks_list.item(i)
            item_text = item.text().lower()
            item.setHidden(search_text not in item_text)
    
    def close_db_connections(self):
        try:
            if hasattr(self, 'db_connections'):
                for db_name, conn in self.db_connections.items():
                    logging.info(f"關閉 {db_name} 數據庫連接")
                    conn.close()
                logging.info("所有數據庫連接已關閉")
        except Exception as e:
            logging.error(f"關閉數據庫連接時出錯: {str(e)}")
            logging.error(traceback.format_exc())
    
    def calculate_indicators(self, df):
        if df.empty:
            return df
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        for period in [5, 10, 20, 60, 120, 240]:
            ma_col = f'MA{period}'
            df[ma_col] = df['Close'].rolling(window=period).mean()
        exp12 = df['Close'].ewm(span=12, adjust=False).mean()
        exp26 = df['Close'].ewm(span=26, adjust=False).mean()
        df['MACD'] = exp12 - exp26
        df['MACDSignal'] = df['MACD'].ewm(span=9, adjust=False).mean()
        df['MACDHist'] = df['MACD'] - df['MACDSignal']
        for period in [6, 13, 14, 24]:
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            rs = avg_gain / avg_loss
            df[f'RSI{period}'] = 100 - (100 / (1 + rs))
        df['BB_middle'] = df['MA20']
        std = df['Close'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (std * 2)
        df['BB_lower'] = df['BB_middle'] - (std * 2)
        if 'Volume' in df.columns:
            df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')
            df['Volume_MA3'] = df['Volume'].rolling(window=3).mean()
            df['Volume_MA18'] = df['Volume'].rolling(window=18).mean()
        if len(df) >= 240:
            df['MA240'] = df['Close'].rolling(window=240).mean()
        else:
            df['MA240'] = np.nan
        low_min = df['Low'].rolling(window=9).min()
        high_max = df['High'].rolling(window=9).max()
        rsv = (df['Close'] - low_min) / (high_max - low_min) * 100
        df['K'] = rsv.ewm(com=2).mean()
        df['D'] = df['K'].ewm(com=2).mean()
        df['J'] = 3 * df['K'] - 2 * df['D']
        return df
    
    def filter_matching_results(self):
        try:
            if not hasattr(self, 'original_rows_visibility'):
                self.original_rows_visibility = []
                for row in range(self.result_table.rowCount()):
                    self.original_rows_visibility.append(not self.result_table.isRowHidden(row))
            for row in range(self.result_table.rowCount()):
                all_matched = True
                for col in range(3, self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item and item.foreground().color().green() < 200:
                        all_matched = False
                        break
                self.result_table.setRowHidden(row, not all_matched)
            self.show_status("已筛选显示符合所有条件的股票")
        except Exception as e:
            self.show_status(f"筛选结果时出错: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def restore_all_results(self):
        try:
            # 無論是否有original_rows_visibility，都顯示所有股票
            row_count = self.result_table.rowCount()
            if row_count == 0:
                self.show_status("沒有股票結果可以顯示")
                return
                
            # 顯示所有行並記錄日誌
            hidden_count = 0
            for row in range(row_count):
                if self.result_table.isRowHidden(row):
                    hidden_count += 1
                    # 確保所有行都顯示
                    self.result_table.setRowHidden(row, False)
            
            # 重置original_rows_visibility
            self.original_rows_visibility = []
            for row in range(row_count):
                self.original_rows_visibility.append(True)
                
            self.show_status(f"已恢复显示所有股票结果，共顯示 {row_count} 支股票（原先隱藏 {hidden_count} 支）")
            logging.info(f"恢復顯示所有股票：總數 {row_count}，原先隱藏 {hidden_count} 支")
        except Exception as e:
            self.show_status(f"恢复结果显示时出错: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def update_progress_message(self, current, total, stock_id, stock_name):
        percent = current / total * 100
        msg = f"正在處理 {current}/{total} ({percent:.1f}%) - {stock_id} {stock_name}"
        self.show_status(msg)
        QApplication.processEvents()
    
    def run_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        logging.info(f"開始執行策略: {current_strategy}")
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        selected_date = self.strategy_date.date().toString("yyyy-MM-dd")
        progress = QProgressDialog("執行選股策略...", "取消", 0, 100, self)
        progress.setWindowTitle("處理中")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.show()
        try:
            cursor = self.db_connections['price'].cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            logging.info(f"數據庫表格: {tables}")
            query = f"SELECT DISTINCT stock_id FROM {self.db_tables['price']} WHERE date <= ?"
            logging.info(f"執行查詢: {query} 參數: {selected_date}")
            cursor.execute(query, (selected_date,))
            all_stocks = [row[0] for row in cursor.fetchall()]
            cursor.close()
            logging.info(f"找到 {len(all_stocks)} 支股票")
            results = []
            matching_stocks = []
            total_stocks = len(all_stocks)
            if total_stocks == 0:
                self.show_status(f"在 {selected_date} 之前沒有可用的股票數據", is_error=True)
                progress.close()
                return
            stock_id_to_name = {}
            for j in range(self.all_stocks_list.count()):
                item_text = self.all_stocks_list.item(j).text()
                parts = item_text.split()
                if len(parts) >= 2:
                    stock_id_to_name[parts[0]] = ' '.join(parts[1:])
            start_time = time.time()
            for idx, stock_id in enumerate(all_stocks, 1):
                stock_name = stock_id_to_name.get(stock_id, "未知股票")
                self.update_progress_message(idx, total_stocks, stock_id, stock_name)
                elapsed = time.time() - start_time
                avg_time = elapsed / idx if idx > 0 else 0
                remaining = avg_time * (total_stocks - idx)
                progress.setLabelText(
                    f"正在分析 {stock_id} {stock_name}\n"
                    f"進度: {idx}/{total_stocks} ({idx/total_stocks:.1%})\n"
                    f"已用時間: {elapsed:.1f}秒 | 剩餘時間: {remaining:.1f}秒"
                )
                progress.setValue(int(idx / total_stocks * 100))
                if progress.wasCanceled():
                    break
                df = self.fetch_strategy_data(stock_id, selected_date)
                if df.empty:
                    if idx < 10:
                        logging.info(f"股票 {stock_id} 沒有數據")
                    continue
                delisting_threshold = 15
                special_delisted_stocks = ['8406']
                if stock_id in special_delisted_stocks:
                    logging.warning(f"股票 {stock_id} 被標記為下市股票，跳過")
                    continue
                try:
                    last_trading_date = df['date'].iloc[-1]
                    if not isinstance(last_trading_date, datetime):
                        last_trading_date = pd.to_datetime(last_trading_date)
                    selected_date_dt = datetime.strptime(selected_date, "%Y-%m-%d")
                    date_diff = (selected_date_dt - last_trading_date).days
                    logging.info(f"股票 {stock_id} 最後交易日: {last_trading_date}, 選擇日期: {selected_date_dt}, 相差天數: {date_diff}")
                    if date_diff > delisting_threshold:
                        logging.warning(f"股票 {stock_id} 已無交易，最後交易日: {last_trading_date}，相差 {date_diff} 天，跳過")
                        continue
                except Exception as e:
                    logging.error(f"處理股票 {stock_id} 日期時出錯: {str(e)}")
                    continue
                conditions = self.strategies[current_strategy]
                condition_results = []
                all_matched = True
                for condition in conditions:
                    matched, message = self.check_condition(df, condition)
                    condition_results.append({"matched": matched, "message": message})
                    if not matched:
                        all_matched = False
                close_price = df.iloc[-1]['Close'] if 'Close' in df.columns else None
                result = {
                    "股票代碼": stock_id,
                    "股票名稱": stock_name,
                    "收盤價": close_price,
                    "條件結果": condition_results,
                    "raw_data": df  # 添加原始數據以便後續分析
                }
                results.append(result)
                if all_matched:
                    matching_stocks.append(stock_id)
                    logging.info(f"股票 {stock_id} {stock_name} 符合所有條件，加入到matching_stocks中")
            
            logging.info(f"符合條件的股票數量: {len(matching_stocks)}")
            logging.info(f"符合條件的股票列表: {matching_stocks}")
            
            # 檢查匹配股票數量與條件結果是否一致
            matched_count_check = 0
            for result in results:
                if "條件結果" in result and result["條件結果"]:
                    if all(cr["matched"] for cr in result["條件結果"]):
                        matched_count_check += 1
            
            if matched_count_check != len(matching_stocks):
                logging.warning(f"警告：匹配股票數量不一致！通過條件檢查: {matched_count_check}, matching_stocks列表: {len(matching_stocks)}")
                
                # 確保所有符合條件的股票都在matching_stocks中
                for result in results:
                    if "條件結果" in result and result["條件結果"]:
                        if all(cr["matched"] for cr in result["條件結果"]):
                            stock_id = result["股票代碼"]
                            if stock_id not in matching_stocks:
                                matching_stocks.append(stock_id)
                                logging.info(f"補充加入符合條件的股票: {stock_id}")
            
            self.update_strategy_results(results, matching_stocks, current_strategy, selected_date)
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_stock = total_time / total_stocks if total_stocks > 0 else 0
            self.show_status(
                f"選股完成！共處理 {total_stocks} 支股票\n"
                f"總用時: {total_time:.1f}秒 | 平均每支: {avg_time_per_stock:.2f}秒"
            )
            progress.setValue(100)
            self.show_status(f"選股完成: {len(matching_stocks)}/{len(results)} 符合條件")
            self.tab_widget.setCurrentIndex(1)
        except Exception as e:
            self.show_status(f"執行策略時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
        finally:
            progress.close()
    
    def get_all_stocks(self):
        stocks = []
        try:
            query = "SELECT DISTINCT stock_id, stock_name FROM price ORDER BY stock_id"
            with self.db_connections['price'].cursor() as cursor:
                cursor.execute(query)
                for row in cursor.fetchall():
                    stocks.append((row[0], row[1]))
        except Exception as e:
            self.show_status(f"獲取股票列表失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
        return stocks
    
    def fetch_strategy_data(self, stock_id, date):
        try:
            query = f"""
            SELECT date, Open, High, Low, Close, Volume
            FROM {self.db_tables['price']}
            WHERE stock_id = ? AND date <= ?
            ORDER BY date DESC
            LIMIT 365
            """
            cursor = self.db_connections['price'].cursor()
            cursor.execute(query, (stock_id, date))
            data = cursor.fetchall()
            cursor.close()
            if not data:
                return pd.DataFrame()
            df = pd.DataFrame(data, columns=['date', 'Open', 'High', 'Low', 'Close', 'Volume'])
            df['date'] = pd.to_datetime(df['date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df = df.sort_values('date', ascending=True).reset_index(drop=True)
            df = self.calculate_indicators(df)
            return df
        except Exception as e:
            logging.error(f"獲取股票數據時出錯: {str(e)}")
            logging.error(traceback.format_exc())
            return pd.DataFrame()
    
    def check_condition(self, df, condition):
        try:
            condition_type = condition.get("type", "")
            logging.debug(f"檢查條件: {condition_type}")
            if df.empty:
                return False, "無數據"
            if condition_type == "ma_position":
                ma = condition.get("ma", "MA5")
                position = condition.get("position", "above")
                price_type = condition.get("price", "close")
                if ma not in df.columns:
                    return False, f"{ma}不存在"
                price = df.iloc[-1]['Close'] if price_type.lower() == 'close' else df.iloc[-1][price_type]
                ma_value = df.iloc[-1][ma]
                if position == "above":
                    matched = price > ma_value
                    message = f"收盤價 {price:.2f} {'>' if matched else '<='} {ma} {ma_value:.2f}"
                    return matched, message
                else:
                    matched = price < ma_value
                    message = f"收盤價 {price:.2f} {'<' if matched else '>='} {ma} {ma_value:.2f}"
                    return matched, message
            elif condition_type == "ma_trend":
                ma = condition.get("ma", "MA5")
                trend = condition.get("trend", "up")
                days = condition.get("days", 1)
                if ma not in df.columns:
                    return False, f"{ma}不存在"
                if len(df) < days + 1:
                    return False, f"數據不足{days+1}天"
                current_ma = df.iloc[-1][ma]
                prev_ma = df.iloc[-1-days][ma]
                if ma == "MA240" and trend == "up":
                    if len(df) < 2:
                        return False, "數據不足2天，無法判斷趨勢"
                    current_ma240 = df.iloc[-1][ma]
                    prev_ma240 = df.iloc[-2][ma]
                    matched = current_ma240 > prev_ma240
                    message = f"{ma} {current_ma240:.2f} {'>' if matched else '<='} 昨日 {prev_ma240:.2f}"
                    return matched, message
                else:
                    if trend == "up":
                        matched = current_ma > prev_ma
                        message = f"{ma} {current_ma:.2f} {'>' if matched else '<='} {days}日前 {prev_ma:.2f}"
                        return matched, message
                    else:
                        matched = current_ma < prev_ma
                        message = f"{ma} {current_ma:.2f} {'<' if matched else '>='} {days}日前 {prev_ma:.2f}"
                        return matched, message
            elif condition_type == "volume_increase":
                days = condition.get("days", 5)
                percent = condition.get("percent", 20)
                if len(df) < days:
                    return False, f"數據不足{days}天"
                current_vol = df.iloc[-1]['Volume']
                avg_vol = df.iloc[-days:-1]['Volume'].mean()
                increase = (current_vol / avg_vol - 1) * 100 if avg_vol > 0 else 0
                matched = increase >= percent
                message = f"成交量增加 {increase:.2f}% {'≥' if matched else '<'} {percent}%"
                return matched, message
            elif condition_type == "macd":
                signal = condition.get("signal", "golden_cross")
                if 'MACD' not in df.columns or 'MACDSignal' not in df.columns:
                    return False, "MACD指標不存在"
                if len(df) < 2:
                    return False, "數據不足"
                current_macd = df.iloc[-1]['MACD']
                current_signal = df.iloc[-1]['MACDSignal']
                prev_macd = df.iloc[-2]['MACD']
                prev_signal = df.iloc[-2]['MACDSignal']
                if signal == "golden_cross":
                    matched = prev_macd < prev_signal and current_macd > current_signal
                    message = "MACD金叉" if matched else "無MACD金叉"
                    return matched, message
                elif signal == "death_cross":
                    matched = prev_macd > prev_signal and current_macd < current_signal
                    message = "MACD死叉" if matched else "無MACD死叉"
                    return matched, message
                elif signal == "above_zero":
                    matched = current_macd > 0
                    message = f"MACD {current_macd:.4f} {'>' if matched else '<='} 0"
                    return matched, message
            elif condition_type == "rsi":
                period = condition.get("period", 14)
                min_val = condition.get("min", 30)
                max_val = condition.get("max", 70)
                position = condition.get("position", "oversold")
                rsi_col = f'RSI{period}'
                if rsi_col not in df.columns:
                    return False, f"{rsi_col}指標不存在"
                current_rsi = df.iloc[-1][rsi_col]
                if position == "oversold":
                    matched = current_rsi <= min_val
                    message = f"{rsi_col} {current_rsi:.2f} {'≤' if matched else '>'} {min_val}"
                    return matched, message
                elif position == "overbought":
                    matched = current_rsi >= max_val
                    message = f"{rsi_col} {current_rsi:.2f} {'≥' if matched else '<'} {max_val}"
                    return matched, message
                elif position == "middle":
                    matched = min_val <= current_rsi <= max_val
                    message = f"{min_val} {'<=' if matched else '>'} {rsi_col} {current_rsi:.2f} {'<=' if matched else '>'} {max_val}"
                    return matched, message
            elif condition_type == "rsi_combined":
                rsi_long = condition.get("rsi_long", 13)
                rsi_long_threshold = condition.get("rsi_long_threshold", 50)
                rsi_short = condition.get("rsi_short", 6)
                rsi_short_threshold = condition.get("rsi_short_threshold", 70)
                rsi_long_col = f'RSI{rsi_long}'
                rsi_short_col = f'RSI{rsi_short}'
                if rsi_long_col not in df.columns:
                    return False, f"{rsi_long_col}指標不存在"
                if rsi_short_col not in df.columns:
                    return False, f"{rsi_short_col}指標不存在"
                current_rsi_long = df.iloc[-1][rsi_long_col]
                current_rsi_short = df.iloc[-1][rsi_short_col]
                long_matched = current_rsi_long > rsi_long_threshold
                short_matched = current_rsi_short > rsi_short_threshold
                all_matched = long_matched and short_matched
                message = (f"{rsi_long_col} {current_rsi_long:.2f} {'>' if long_matched else '<='} {rsi_long_threshold} 且 " +
                           f"{rsi_short_col} {current_rsi_short:.2f} {'>' if short_matched else '<='} {rsi_short_threshold}")
                return all_matched, message
            elif condition_type == "volume_value":
                min_value = condition.get("min_value", 10000000)
                if 'Close' not in df.columns or 'Volume' not in df.columns:
                    return False, "缺少股價或成交量數據"
                current_close = df.iloc[-1]['Close']
                current_volume = df.iloc[-1]['Volume']
                volume_value = current_close * current_volume
                matched = volume_value >= min_value
                formatted_value = f"{volume_value/10000:.2f}萬"
                formatted_min = f"{min_value/10000:.2f}萬"
                message = f"成交額 {formatted_value} {'≥' if matched else '<'} {formatted_min}"
                return matched, message
            elif condition_type == "volume_ma_cross":
                short_period = condition.get("short_period", 3)
                long_period = condition.get("long_period", 18)
                days_to_check = condition.get("days", 3)
                short_ma_col = f'Volume_MA{short_period}'
                long_ma_col = f'Volume_MA{long_period}'
                if short_ma_col not in df.columns:
                    if 'Volume' in df.columns:
                        df[short_ma_col] = df['Volume'].rolling(window=short_period).mean()
                        logging.info(f"動態計算了 {short_ma_col}")
                    else:
                        return False, f"{short_ma_col}指標不存在"
                if long_ma_col not in df.columns:
                    if 'Volume' in df.columns:
                        df[long_ma_col] = df['Volume'].rolling(window=long_period).mean()
                        logging.info(f"動態計算了 {long_ma_col}")
                    else:
                        return False, f"{long_ma_col}指標不存在"
                if len(df) < days_to_check + 1:
                    return False, f"數據不足{days_to_check}天"
                cross_found = False
                cross_day = None
                for i in range(1, min(days_to_check+1, len(df))):
                    idx = -i
                    prev_idx = idx - 1
                    if prev_idx < -len(df):
                        continue
                    if (df.iloc[prev_idx][short_ma_col] <= df.iloc[prev_idx][long_ma_col] and 
                        df.iloc[idx][short_ma_col] > df.iloc[idx][long_ma_col]):
                        cross_found = True
                        cross_day = len(df) + idx
                        break
                message = f"量能{short_period}日均線與{long_period}日均線 {'已' if cross_found else '未'} 在{days_to_check}日內黃金交叉"
                if cross_found and cross_day is not None:
                    days_ago = len(df) - cross_day
                    message += f" ({days_ago}日前)"
                return cross_found, message
            elif condition_type == "ma_trend_or":
                ma1 = condition.get("ma1", "MA60")
                ma2 = condition.get("ma2", "MA20")
                trend = condition.get("trend", "up")
                days = condition.get("days", 1)
                if ma1 not in df.columns:
                    return False, f"{ma1}不存在"
                if ma2 not in df.columns:
                    return False, f"{ma2}不存在"
                if len(df) < days + 1:
                    return False, f"數據不足{days+1}天"
                current_ma1 = df.iloc[-1][ma1]
                prev_ma1 = df.iloc[-1-days][ma1]
                ma1_up = current_ma1 > prev_ma1
                current_ma2 = df.iloc[-1][ma2]
                prev_ma2 = df.iloc[-1-days][ma2]
                ma2_up = current_ma2 > prev_ma2
                matched = ma1_up or ma2_up
                ma1_status = f"{ma1} {current_ma1:.2f} {'>' if ma1_up else '<='} {days}日前 {prev_ma1:.2f}"
                ma2_status = f"{ma2} {current_ma2:.2f} {'>' if ma2_up else '<='} {days}日前 {prev_ma2:.2f}"
                message = f"{ma1_status} 或 {ma2_status}"
                return matched, message
            elif condition_type == "ma_future_trend":
                ma_period = 240
                lookahead_days = 20
                try:
                    df['date'] = pd.to_datetime(df['date']).dt.tz_localize(None)
                    df = df.sort_values('date').reset_index(drop=True)
                    if len(df) < ma_period + lookahead_days:
                        return False, f"數據不足{ma_period + lookahead_days}個交易日"
                    current_end_idx = len(df) - 1
                    current_start_idx = current_end_idx - ma_period + 1
                    if current_start_idx < 0:
                        return False, "數據不足計算當前MA240"
                    old_data_start_idx = current_start_idx
                    old_data_end_idx = old_data_start_idx + lookahead_days - 1
                    if old_data_end_idx >= len(df):
                        return False, "歷史數據範圍超出可用數據"
                    old_data = df.iloc[old_data_start_idx:old_data_end_idx+1]
                    logging.debug(f"舊數據區間索引:{old_data_start_idx}-{old_data_end_idx} 實際天數:{len(old_data)}")
                    if len(old_data) < lookahead_days:
                        return False, f"歷史數據不連續({len(old_data)}/{lookahead_days}天)"
                    old_min_price = old_data['Close'].min()
                    safety_margin = old_min_price * 0.99
                    future_prices = df['Close'].iloc[-lookahead_days:].values
                    all_above = all(price >= safety_margin for price in future_prices)
                    current_price = df['Close'].iloc[-1]
                    margin_percent = ((current_price - old_min_price) / old_min_price) * 100
                    status_icon = "✅" if all_above else "❌"
                    status_text = "符合" if all_above else "不符合"
                    message = (f"{status_icon} {status_text} | 年線扣抵區間:{old_data['date'].iloc[0].strftime('%Y-%m-%d')}~{old_data['date'].iloc[-1].strftime('%Y-%m-%d')}\n"
                               f"安全基準價:{old_min_price:.2f} | 當前價:{current_price:.2f}(差距:{margin_percent:+.1f}%)\n"
                               f"未來{lookahead_days}日預測:{'✅ 安全' if all_above else '❌ 危險'}")
                    return all_above, message
                except Exception as e:
                    logging.error(f"計算未來趨勢時發生異常: {str(e)}")
                    logging.error(traceback.format_exc())
                    return False, "計算發生異常"
            elif condition_type == "break_bottom_rebound":
                # 獲取策略參數
                lookback = condition.get("lookback", 10)
                rebound_threshold = condition.get("rebound_threshold", 5)
                volume_multiplier = condition.get("volume_multiplier", 2)
                
                # 獲取全局策略參數
                break_bottom_min_percent = self.strategy_params.break_bottom_min_percent if hasattr(self, 'strategy_params') else 2
                rebound_max_percent = self.strategy_params.rebound_max_percent if hasattr(self, 'strategy_params') else 15
                rebound_min_percent = self.strategy_params.rebound_min_percent if hasattr(self, 'strategy_params') else 5
                volume_trend_days = self.strategy_params.volume_trend_days if hasattr(self, 'strategy_params') else 5
                kline_pattern_check = self.strategy_params.kline_pattern_check if hasattr(self, 'strategy_params') else True
                
                # 使用條件參數或全局參數（以條件參數優先）
                rebound_threshold = rebound_threshold if rebound_threshold > 0 else rebound_min_percent
                
                if len(df) < lookback + 5:  # 需要更多數據來確認趨勢
                    return False, f"數據不足{lookback + 5}天"
                
                # 1. 確認是否真正破底 - 加強判斷
                recent_data = df.iloc[-lookback:]
                previous_data = df.iloc[-lookback-20:-lookback] if len(df) >= lookback+20 else df.iloc[0:-lookback]
                
                # 找到近期最低價
                low_price = recent_data['Close'].min()
                low_price_idx = recent_data['Close'].idxmin()
                low_price_date = recent_data.loc[low_price_idx, 'date']
                
                # 檢查是否為近期新低 - 使用可調整的百分比
                previous_low = previous_data['Close'].min()
                is_new_low = low_price < previous_low * (1 - break_bottom_min_percent/100)  # 使用參數化的破底百分比
                
                # 檢查低點是否是近期形成的（反彈初期判斷）
                days_since_low = len(recent_data) - recent_data.index.get_loc(low_price_idx) - 1
                is_recent_low = days_since_low <= 5  # 低點是在最近5個交易日內形成的
                
                # 2. 確認反彈強度與上限
                current_close = df.iloc[-1]['Close']
                rebound_pct = (current_close - low_price) / low_price * 100 if low_price > 0 else 0
                
                # 檢查反彈是否在合理範圍內（不要追高）
                price_condition = rebound_pct >= rebound_threshold and rebound_pct <= rebound_max_percent
                
                # 3. 確認成交量放大與趨勢
                if len(recent_data) < 2:
                    return False, "數據不足無法計算成交量"
                
                # 計算成交量趨勢
                volume_trend_data = df.iloc[-volume_trend_days:]['Volume']
                volume_trend_increasing = False
                if len(volume_trend_data) >= 3:
                    # 使用簡單線性回歸判斷成交量趨勢
                    x = np.arange(len(volume_trend_data))
                    y = volume_trend_data.values
                    slope = np.polyfit(x, y, 1)[0]
                    volume_trend_increasing = slope > 0
                
                avg_volume = recent_data.iloc[:-1]['Volume'].mean()
                today_volume = df.iloc[-1]['Volume']
                volume_condition = today_volume >= avg_volume * volume_multiplier
                
                # 4. 確認反轉信號 - 使用RSI指標
                rsi_condition = False
                rsi_message = ""
                if 'RSI6' in df.columns and 'RSI14' in df.columns:
                    current_rsi6 = df.iloc[-1]['RSI6']
                    current_rsi14 = df.iloc[-1]['RSI14']
                    prev_rsi6 = df.iloc[-2]['RSI6'] if len(df) > 2 else 50
                    
                    # RSI從超賣區回升
                    if current_rsi6 > prev_rsi6 and current_rsi6 > 30 and prev_rsi6 < 30:
                        rsi_condition = True
                        rsi_message = f"RSI6從超賣區回升({prev_rsi6:.1f}→{current_rsi6:.1f})"
                    # 或RSI形成底背離
                    elif low_price_idx < len(df) - 3:  # 確保低點不是最近的價格
                        rsi_at_low = df.iloc[low_price_idx]['RSI14']
                        if current_rsi14 > rsi_at_low + 5:  # RSI形成上升趨勢
                            rsi_condition = True
                            rsi_message = f"RSI14底背離({rsi_at_low:.1f}→{current_rsi14:.1f})"
                
                # 5. 確認K線形態 - 檢查是否有長下影線或其他看漲形態
                k_line_condition = False
                k_line_message = ""
                
                if kline_pattern_check:  # 只有在啟用K線形態檢查時才執行
                    recent_candles = df.iloc[-3:]
                    
                    # 檢查長下影線
                    for i, candle in recent_candles.iterrows():
                        if candle['Low'] < candle['Open'] and candle['Low'] < candle['Close']:
                            lower_shadow = min(candle['Open'], candle['Close']) - candle['Low']
                            body = abs(candle['Open'] - candle['Close'])
                            if lower_shadow > body * 1.5:  # 下影線長度是實體的1.5倍以上
                                k_line_condition = True
                                k_line_message = "出現長下影線K線，表示買盤支撐"
                                break
                    
                    # 檢查其他看漲形態（如：錘子線、晨星等）
                    if not k_line_condition and len(df) >= 3:
                        # 檢查錘子線形態
                        last_candle = df.iloc[-1]
                        if last_candle['Close'] > last_candle['Open']:
                            body = last_candle['Close'] - last_candle['Open']
                            upper_shadow = last_candle['High'] - last_candle['Close']
                            lower_shadow = last_candle['Open'] - last_candle['Low']
                            if lower_shadow > 2 * body and upper_shadow < 0.5 * body:
                                k_line_condition = True
                                k_line_message = "出現錘子線形態，表示可能反轉"
                
                # 6. 檢查是否已經開始形成上升趨勢
                trend_condition = False
                trend_message = ""
                if len(df) >= 5 and 'MA5' in df.columns and 'MA10' in df.columns:
                    ma5_current = df.iloc[-1]['MA5']
                    ma5_prev = df.iloc[-2]['MA5']
                    ma10_current = df.iloc[-1]['MA10']
                    
                    if ma5_current > ma5_prev and current_close > ma5_current:
                        trend_condition = True
                        trend_message = "短期均線開始上揚，價格站上MA5"
                    elif current_close > ma10_current and ma5_current > ma5_prev:
                        trend_condition = True
                        trend_message = "價格站上MA10，短期均線上揚"
                
                # 綜合評估
                # 必要條件：價格反彈在合理範圍內 + 成交量放大
                basic_condition = price_condition and volume_condition
                
                # 輔助條件：至少滿足兩項額外條件
                extra_conditions = [is_new_low, is_recent_low, rsi_condition, k_line_condition, trend_condition, volume_trend_increasing]
                extra_condition_count = sum(1 for c in extra_conditions if c)
                
                if basic_condition and extra_condition_count >= 2:  # 必要條件加至少兩項輔助條件
                    message_parts = [f"破底反彈 {rebound_pct:.2f}% (≥{rebound_threshold}%, ≤{rebound_max_percent}%)", 
                                    f"成交量 {today_volume:.0f} ≥ 平均 {avg_volume:.0f}×{volume_multiplier}"]
                    
                    if is_new_low:
                        message_parts.append(f"創近期新低({low_price:.2f} < {previous_low:.2f})")
                    if is_recent_low:
                        message_parts.append(f"低點形成時間近({days_since_low}天)，反彈初期")
                    if volume_trend_increasing:
                        message_parts.append(f"近{volume_trend_days}日成交量呈上升趨勢")
                    if rsi_condition and rsi_message:
                        message_parts.append(rsi_message)
                    if k_line_condition and k_line_message:
                        message_parts.append(k_line_message)
                    if trend_condition and trend_message:
                        message_parts.append(trend_message)
                        
                    message = "，".join(message_parts)
                    return True, message
                else:
                    msg_parts = []
                    if not price_condition:
                        if rebound_pct < rebound_threshold:
                            msg_parts.append(f"反彈 {rebound_pct:.2f}% 小於 {rebound_threshold}%")
                        elif rebound_pct > rebound_max_percent:
                            msg_parts.append(f"反彈 {rebound_pct:.2f}% 大於上限 {rebound_max_percent}%")
                    if not volume_condition:
                        msg_parts.append(f"成交量 {today_volume:.0f} 小於平均 {avg_volume:.0f}×{volume_multiplier}")
                    if not is_new_low:
                        msg_parts.append("非創新低反彈")
                    if not is_recent_low:
                        msg_parts.append(f"低點形成已{days_since_low}天，非反彈初期")
                    if extra_condition_count < 2:
                        msg_parts.append(f"輔助指標確認不足({extra_condition_count}/2)")
                    return False, "，".join(msg_parts)
            if condition_type not in ['ma_position', 'ma_trend', 'ma_trend_or', 'volume_increase', 'macd', 'rsi', 'rsi_combined', 'volume_value', 'volume_ma_cross']:
                logging.error(f"不支援的條件類型: {condition_type}")
                return False, f"未支援的條件類型: {condition_type}"
        except Exception as e:
            logging.error(f"檢查條件時發生錯誤: {str(e)}")
            logging.error(f"條件內容: {condition}")
            logging.error(traceback.format_exc())
            return False, f"錯誤: {str(e)}"
    
    def update_strategy_results(self, results, matching_stocks, strategy_name, date):
        if hasattr(self, 'original_rows_visibility'):
            delattr(self, 'original_rows_visibility')
        total = len(results)
        matched = len(matching_stocks)
        match_percentage = (matched / total * 100) if total > 0 else 0
        
        logging.info(f"更新策略結果：策略名稱={strategy_name}, 日期={date}")
        logging.info(f"總股票數={total}, 符合條件股票數={matched}, 符合比例={match_percentage:.2f}%")
        if matched > 0:
            logging.info(f"符合條件的股票IDs: {matching_stocks}")
        
        self.result_summary_label.setText(
            f"策略: {strategy_name}\n"
            f"計算日期: {date}\n"
            f"符合條件的股票: {matched}/{total} ({match_percentage:.2f}%)"
        )
        
        # 清除並更新符合條件的股票列表
        self.filtered_stocks_list.clear()
        matched_in_list = 0
        
        # 先對結果進行評分排序
        for result in results:
            if "條件結果" in result:
                score = 0
                for cr in result["條件結果"]:
                    if cr["matched"]:
                        score += 25  # 每個符合條件加25分
                result["評分"] = score
            else:
                result["評分"] = 0
        
        # 按評分降序排序結果
        sorted_results = sorted(results, key=lambda x: x.get("評分", 0), reverse=True)
        
        # 更新列表和表格
        for result in sorted_results:
            # 檢查該股票是否有條件結果且全部符合
            has_all_conditions_matched = False
            if "條件結果" in result and result["條件結果"]:
                has_all_conditions_matched = all(cr["matched"] for cr in result["條件結果"])
            
            # 或者檢查該股票是否在matching_stocks中
            stock_id = result["股票代碼"]
            is_in_matching_stocks = stock_id in matching_stocks
            
            # 如果任一條件為真，加入到列表並使用亮綠色標示
            if has_all_conditions_matched or is_in_matching_stocks:
                matched_in_list += 1
                stock_name = result["股票名稱"]
                close_price = result["收盤價"]
                item_text = f"{stock_id} {stock_name} {close_price:.2f}" if close_price else f"{stock_id} {stock_name}"
                item = QListWidgetItem(item_text)
                item.setForeground(QColor(0, 255, 128))  # 使用亮綠色
                self.filtered_stocks_list.addItem(item)
                logging.info(f"添加到列表: {stock_id} - 條件符合狀態:{has_all_conditions_matched}, 在matching_stocks中:{is_in_matching_stocks}")
        
        logging.info(f"符合條件的股票數量 (在列表中): {matched_in_list}")
        
        # 切換到"選股結果"標籤
        for i in range(self.findChildren(QTabWidget)[0].count()):
            if self.findChildren(QTabWidget)[0].tabText(i) == "選股結果":
                self.findChildren(QTabWidget)[0].setCurrentIndex(i)
                break
                
        # 根據策略名稱設置不同的表格結構
        if strategy_name == "破底反彈高量":
            self.setup_break_bottom_rebound_table(results, matching_stocks)
        else:
            # 其他策略使用原有表格結構
            self.setup_default_strategy_table(results, matching_stocks)
            
        # 確保表格中的符合條件股票顯示為亮綠色
        for row in range(self.result_table.rowCount()):
            stock_id = self.result_table.item(row, 0).text()
            if stock_id in matching_stocks:
                for col in range(self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item:
                        item.setForeground(QColor(0, 255, 128))
    
    def parse_and_fill_condition_data(self, row, condition_message, stock_data):
        """解析條件訊息並填充表格數據"""
        # 初始化變數
        rebound_pct = 0
        break_bottom_pct = 5.0  # 預設值
        volume_ratio = 0
        
        # 從條件訊息中提取數據
        if condition_message:
            # 提取反彈幅度
            rebound_match = re.search(r'破底反彈\s*([\d.]+)%', condition_message)
            if rebound_match:
                rebound_pct = float(rebound_match.group(1))
            
            # 提取成交量比例
            volume_match = re.search(r'成交量.*?(\d+)\s*≥\s*平均.*?×(\d+(?:\.\d+)?)', condition_message)
            if volume_match:
                volume_ratio = float(volume_match.group(2))
        
        # 計算評分
        score = self.calculate_score(
            stock_data=stock_data.get('股票數據', None),
            low_price=stock_data.get('最低價', 0),
            previous_low=stock_data.get('前期低點', 0),
            rebound_pct=rebound_pct,
            volume_ratio=volume_ratio,
            extra_condition_count=len([x for x in [True] if x])  # 簡化的條件計數
        )
        
        # 填充表格
        score_item = QTableWidgetItem(f"{int(score)}")
        score_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.result_table.setItem(row, 3, score_item)
        self.apply_score_color(row, 3, int(score))
        
        # 顯示反彈幅度
        rebound_item = QTableWidgetItem(f"{rebound_pct:.2f}%" if rebound_pct > 0 else "--")
        rebound_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 4, rebound_item)
        
        # 顯示破底程度
        break_bottom_item = QTableWidgetItem(f"{break_bottom_pct:.2f}%" if break_bottom_pct > 0 else "--")
        break_bottom_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 5, break_bottom_item)
        
        # 顯示成交量比例
        volume_item = QTableWidgetItem(f"{volume_ratio:.1f}倍" if volume_ratio > 0 else "--")
        volume_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 6, volume_item)
        
        # 顯示RSI狀態
        rsi_status = ""
        if 'RSI6' in stock_data:
            rsi_status = f"RSI6: {stock_data['RSI6']:.2f}"
        rsi_item = QTableWidgetItem(rsi_status if rsi_status else "--")
        rsi_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 7, rsi_item)
        
        # 顯示K線形態
        kline_pattern = ""
        if 'K線' in stock_data:
            kline_pattern = stock_data['K線']
        kline_item = QTableWidgetItem(kline_pattern if kline_pattern else "--")
        kline_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 8, kline_item)
        
        # 顯示趨勢確認
        trend_confirm = ""
        if '趨勢' in stock_data:
            trend_confirm = stock_data['趨勢']
        trend_item = QTableWidgetItem(trend_confirm if trend_confirm else "--")
        trend_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.result_table.setItem(row, 9, trend_item)
    
    def setup_default_strategy_table(self, results, matching_stocks):
        """設置默認策略的表格結構"""
        self.result_table.setRowCount(0)
        self.result_table.setColumnCount(9)
        self.result_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價", "年線趨勢(當日)", "年線未來趨勢", "月線趨勢", "量能黃金交叉", "RSI條件", "成交額條件"])
        self.result_table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                color: #e0e0e0;
                gridline-color: #404040;
            }
            QTableWidget::item {
                padding: 3px;
            }
            QTableWidget::item:!selected {
                border-bottom: 1px solid #333333;
            }
        """)
        # 顯示所有股票，無論是否符合條件
        logging.info(f"顯示所有股票結果，總數: {len(results)}")
        for result in results:
            row = self.result_table.rowCount()
            self.result_table.insertRow(row)
            stock_id_item = QTableWidgetItem(result["股票代碼"])
            stock_id_item.setForeground(QColor(255, 255, 255))
            self.result_table.setItem(row, 0, stock_id_item)
            stock_name_item = QTableWidgetItem(result["股票名稱"])
            stock_name_item.setForeground(QColor(255, 255, 255))
            self.result_table.setItem(row, 1, stock_name_item)
            if result["收盤價"] is not None:
                price_item = QTableWidgetItem(f"{result['收盤價']:.2f}")
                price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                price_item.setForeground(QColor(255, 255, 255))
                self.result_table.setItem(row, 2, price_item)
            else:
                price_item = QTableWidgetItem("N/A")
                price_item.setForeground(QColor(255, 255, 255))
                self.result_table.setItem(row, 2, price_item)
            for j, condition_result in enumerate(result["條件結果"]):
                if j < 6:
                    item = QTableWidgetItem(condition_result["message"])
                    if condition_result["matched"]:
                        item.setForeground(QColor(0, 255, 0))
                        item.setToolTip("此條件已符合")
                    else:
                        item.setForeground(QColor(255, 50, 50))
                        item.setToolTip("此條件未符合")
                    self.result_table.setItem(row, j + 3, item)
            all_matched = all(cr["matched"] for cr in result["條件結果"])
            if all_matched:
                for col in range(self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item:
                        item.setForeground(QColor(0, 255, 128))
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
        self.result_table.resizeColumnsToContents()
        
    def setup_break_bottom_rebound_table(self, results, matching_stocks):
        """設置破底反彈高量策略的表格結構"""
        # 設置表格列數
        self.result_table.setRowCount(0)
        self.result_table.setSortingEnabled(False)
        
        # 設置表頭
        headers = ["股票代碼", "股票名稱", "收盤價", "評分", "反彈幅度", "破底程度", "成交量比例", "RSI狀態", "K線形態", "趨勢確認"]
        self.result_table.setColumnCount(len(headers))
        self.result_table.setHorizontalHeaderLabels(headers)
        
        # 用於存儲排序後的結果
        sorted_results = []
        matching_color = QColor(0, 255, 0)  # 綠色文字
        
        for result in results:
            stock_id = result["股票代碼"]
            rebound_pct = 0
            break_bottom_pct = 0
            volume_ratio = 0
            rsi_status = ""
            kline_pattern = ""
            trend_confirm = ""
            low_price = 0
            previous_low = 0
            
            # 從條件結果中提取數據
            if "條件結果" in result and result["條件結果"]:
                for cr in result["條件結果"]:
                    if not cr["message"]:
                        continue
                    message = cr["message"]
                    
                    # 提取反彈幅度
                    if "破底反彈" in message:
                        try:
                            rebound_match = re.search(r'破底反彈\s*([\d.]+)%', message)
                            if rebound_match:
                                rebound_pct = float(rebound_match.group(1))
                        except:
                            pass
                    
                    # 提取成交量比例
                    if "成交量" in message and "平均" in message:
                        try:
                            # 修改正則表達式以正確提取成交量和平均值
                            volume_match = re.search(r'成交量\s*(\d+)\s*≥\s*平均\s*(\d+)', message)
                            if volume_match:
                                current_volume = float(volume_match.group(1))
                                avg_volume = float(volume_match.group(2))
                                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
                        except Exception as e:
                            logging.error(f"計算成交量比例時出錯: {str(e)}")
                            pass
                    
                    # 提取破底程度
                    if "創近期新低" in message or "低於前期" in message:
                        try:
                            values_match = re.search(r'\(([\d.]+)\s*<\s*([\d.]+)\)', message)
                            if values_match:
                                low_price = float(values_match.group(1))
                                previous_low = float(values_match.group(2))
                                break_bottom_pct = (previous_low - low_price) / previous_low * 100
                        except:
                            pass
                    
                    # 提取RSI狀態
                    if "RSI" in message:
                        rsi_status = message.strip()
                    
                    # 提取K線形態
                    if "K線" in message or "形態" in message:
                        kline_pattern = message.strip()
                    
                    # 提取趨勢確認
                    if "均線" in message or "MA" in message:
                        trend_confirm = message.strip()
            
            # 如果沒有從條件消息中獲取到破底程度，嘗試從原始數據計算
            if break_bottom_pct == 0 and result.get("raw_data") is not None:
                try:
                    df = result["raw_data"]
                    if not df.empty and len(df) > 30:
                        recent_low = df['Low'][-30:].min()
                        previous_low = df['Low'][:-30].min() if len(df) > 30 else df['Low'].min()
                        low_price = recent_low
                        if previous_low > recent_low and previous_low > 0:
                            break_bottom_pct = (previous_low - recent_low) / previous_low * 100
                except:
                    pass
            
            # 計算額外條件數
            extra_condition_count = sum(1 for x in [rsi_status, kline_pattern, trend_confirm] if x)
            
            # 計算評分
            # 使用原始的calculate_score方法
            score = 0
            if stock_id in matching_stocks:
                score = self.calculate_score(
                    stock_data=result.get("raw_data"), 
                    low_price=low_price, 
                    previous_low=previous_low, 
                    rebound_pct=rebound_pct, 
                    volume_ratio=volume_ratio, 
                    extra_condition_count=extra_condition_count
                )
            
            # 儲存結果
            result_data = {
                "股票代碼": stock_id,
                "股票名稱": result["股票名稱"],
                "收盤價": result["收盤價"],
                "評分": score,
                "反彈幅度": rebound_pct,
                "破底程度": break_bottom_pct,
                "成交量比例": volume_ratio,
                "RSI狀態": rsi_status,
                "K線形態": kline_pattern,
                "趨勢確認": trend_confirm,
                "is_matching": stock_id in matching_stocks
            }
            sorted_results.append(result_data)
        
        # 按評分排序
        sorted_results.sort(key=lambda x: x["評分"], reverse=True)
        
        for row, result in enumerate(sorted_results):
            self.result_table.insertRow(row)
            
            # 設置各欄位數據
            items = [
                (result["股票代碼"], None),
                (result["股票名稱"], None),
                (f"{result['收盤價']:.2f}", Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter),
                (f"{int(result['評分'])}", Qt.AlignmentFlag.AlignCenter),
                (f"{result['反彈幅度']:.2f}%" if result['反彈幅度'] > 0 else "--", Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter),
                (f"{result['破底程度']:.2f}%" if result['破底程度'] > 0 else "--", Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter),
                (f"{result['成交量比例']:.2f}" if result['成交量比例'] > 0 else "--", Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter),
                (result["RSI狀態"] if result["RSI狀態"] else "--", None),
                (result["K線形態"] if result["K線形態"] else "--", None),
                (result["趨勢確認"] if result["趨勢確認"] else "--", None)
            ]
            
            for col, (text, alignment) in enumerate(items):
                item = QTableWidgetItem(text)
                if alignment:
                    item.setTextAlignment(alignment)
                
                # 如果是符合條件的股票，只設置綠色文字
                if result["is_matching"]:
                    item.setForeground(matching_color)
                
                self.result_table.setItem(row, col, item)
        
        # 調整列寬並啟用排序
        self.result_table.resizeColumnsToContents()
        self.result_table.setSortingEnabled(True)
        
        return len(sorted_results)
    
    def on_result_double_clicked(self, index):
        row = index.row()
        stock_id_item = self.result_table.item(row, 0)
        if stock_id_item:
            stock_id = stock_id_item.text()
            self.tab_widget.setCurrentIndex(0)
            self.plot_stock(stock_id)
            
    def export_to_excel(self):
        """
        將選股結果表格導出為Excel文件
        """
        try:
            # 檢查表格是否有數據
            if self.result_table.rowCount() == 0:
                self.show_status("沒有可導出的數據", is_error=True)
                return
                
            # 獲取當前日期
            current_date = datetime.now().strftime("%Y%m%d")
            
            # 獲取當前策略名稱
            strategy_name = self.strategy_combo.currentText()
            
            # 判斷是否為篩選後的股票
            is_filtered = len([item for item in self.result_table.selectedItems()]) > 0
            stock_type = "filtered" if is_filtered else "all"
            
            # 設定預設檔名
            default_filename = f"stock_screen_{current_date}_{strategy_name}_{stock_type}.xlsx"
            
            # 打開文件保存對話框，預設目錄為 D:/
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件", "D:/" + default_filename, "Excel Files (*.xlsx);;All Files (*)"
            )
            
            if not file_path:
                return  # 用戶取消了保存
                
            # 如果文件名沒有.xlsx後綴，添加它
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'
                
            # 從表格中獲取數據
            data = []
            headers = []
            
            # 獲取表頭
            for col in range(self.result_table.columnCount()):
                headers.append(self.result_table.horizontalHeaderItem(col).text())
                
            # 獲取表格數據
            for row in range(self.result_table.rowCount()):
                row_data = {}
                for col in range(self.result_table.columnCount()):
                    item = self.result_table.item(row, col)
                    if item:
                        row_data[headers[col]] = item.text()
                    else:
                        row_data[headers[col]] = ""
                data.append(row_data)
                
            # 創建DataFrame
            df = pd.DataFrame(data)
            
            # 使用ExcelWriter導出數據
            with ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='選股結果', index=False)
                
                # 自動調整列寬
                for column in df:
                    column_width = max(df[column].astype(str).map(len).max(), len(column))
                    col_idx = df.columns.get_loc(column)
                    writer.sheets['選股結果'].column_dimensions[chr(65 + col_idx)].width = column_width + 2
                    
            self.show_status(f"已成功導出Excel文件到: {file_path}")
            
        except Exception as e:
            self.show_status(f"導出Excel失敗: {str(e)}", is_error=True)
            logging.error(f"導出Excel失敗: {str(e)}")
            logging.error(traceback.format_exc())
    
    def set_latest_trading_date(self):
        try:
            if 'price' in self.db_connections:
                cursor = self.db_connections['price'].cursor()
                cursor.execute(f"SELECT MAX(date) FROM {self.db_tables['price']}")
                latest_date = cursor.fetchone()[0]
                if latest_date:
                    latest_date = QDate.fromString(latest_date, "yyyy-MM-dd")
                    self.strategy_date.setDate(latest_date)
                    self.show_status(f"已設置為最近交易日: {latest_date.toString('yyyy-MM-dd')}")
                else:
                    self.show_status("無法獲取最近交易日", is_error=True)
            else:
                self.show_status("數據庫未連接，無法獲取最近交易日", is_error=True)
        except Exception as e:
            self.show_status(f"設置最近交易日時出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def add_strategy(self):
        dialog = StrategyDialog(self)
        dialog.exec()
        if dialog.result() == QDialog.DialogCode.Accepted:
            strategy_name = dialog.name_edit.text()
            conditions = dialog.get_conditions()
            if not hasattr(self, 'strategies'):
                self.strategies = {}
            self.strategies[strategy_name] = conditions
            current_index = self.strategy_combo.findText(strategy_name)
            if current_index == -1:
                self.strategy_combo.addItem(strategy_name)
            self.strategy_combo.setCurrentText(strategy_name)
            self.save_strategies()
            self.show_status(f"已添加策略: {strategy_name}")
    
    def edit_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        dialog = StrategyDialog(self, strategy_name=current_strategy, conditions=self.strategies[current_strategy])
        if dialog.exec():
            new_name = dialog.name_edit.text()
            conditions = dialog.get_conditions()
            if new_name != current_strategy:
                del self.strategies[current_strategy]
                self.strategy_combo.removeItem(self.strategy_combo.currentIndex())
                self.strategies[new_name] = conditions
                self.strategy_combo.addItem(new_name)
                self.strategy_combo.setCurrentText(new_name)
            else:
                self.strategies[current_strategy] = conditions
            self.save_strategies()
            self.show_status(f"已更新策略: {new_name}")
    
    def delete_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        reply = QMessageBox.question(self, "確認刪除", f"確定要刪除策略 '{current_strategy}' 嗎？",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            del self.strategies[current_strategy]
            self.strategy_combo.removeItem(self.strategy_combo.currentIndex())
            self.save_strategies()
            self.show_status(f"已刪除策略: {current_strategy}")
    
    def save_strategies(self):
        try:
            strategies_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies.json')
            with open(strategies_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, ensure_ascii=False, indent=4)
        except Exception as e:
            self.show_status(f"保存策略失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def load_strategies(self):
        try:
            strategies_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies.json')
            logging.info("重新創建默認策略")
            self.strategies = {
                "勝率73.45%": [
                    {"type": "ma_trend", "ma": "MA240", "trend": "up", "days": 1},
                    {"type": "ma_future_trend", "ma": "MA240", "days": 20},
                    {"type": "ma_trend", "ma": "MA20", "trend": "up", "days": 5},
                    {"type": "volume_ma_cross", "short_period": 3, "long_period": 18, "days": 3},
                    {"type": "rsi_combined", "rsi_long": 13, "rsi_long_threshold": 50, "rsi_short": 6, "rsi_short_threshold": 70},
                    {"type": "volume_value", "min_value": 10000000}
                ],
                "破底反彈高量": [
                    {"type": "break_bottom_rebound", "lookback": 10, "rebound_threshold": 5, "volume_multiplier": 2}
                ]
            }
            if os.path.exists(strategies_file):
                os.remove(strategies_file)
                logging.info("已刪除舊策略文件")
            with open(strategies_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, indent=4, ensure_ascii=False)
            logging.info("已保存新策略文件")
            self.strategy_combo.clear()
            for strategy_name in self.strategies.keys():
                self.strategy_combo.addItem(strategy_name)
            if "勝率73.45%" in self.strategies:
                self.strategy_combo.setCurrentText("勝率73.45%")
        except Exception as e:
            self.show_status(f"載入策略失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def run_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        logging.info(f"開始執行策略: {current_strategy}")
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        selected_date = self.strategy_date.date().toString("yyyy-MM-dd")
        progress = QProgressDialog("執行選股策略...", "取消", 0, 100, self)
        progress.setWindowTitle("處理中")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.show()
        try:
            cursor = self.db_connections['price'].cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            logging.info(f"數據庫表格: {tables}")
            query = f"SELECT DISTINCT stock_id FROM {self.db_tables['price']} WHERE date <= ?"
            logging.info(f"執行查詢: {query} 參數: {selected_date}")
            cursor.execute(query, (selected_date,))
            all_stocks = [row[0] for row in cursor.fetchall()]
            cursor.close()
            logging.info(f"找到 {len(all_stocks)} 支股票")
            results = []
            matching_stocks = []
            total_stocks = len(all_stocks)
            if total_stocks == 0:
                self.show_status(f"在 {selected_date} 之前沒有可用的股票數據", is_error=True)
                progress.close()
                return
            stock_id_to_name = {}
            for j in range(self.all_stocks_list.count()):
                item_text = self.all_stocks_list.item(j).text()
                parts = item_text.split()
                if len(parts) >= 2:
                    stock_id_to_name[parts[0]] = ' '.join(parts[1:])
            start_time = time.time()
            for idx, stock_id in enumerate(all_stocks, 1):
                stock_name = stock_id_to_name.get(stock_id, "未知股票")
                self.update_progress_message(idx, total_stocks, stock_id, stock_name)
                elapsed = time.time() - start_time
                avg_time = elapsed / idx if idx > 0 else 0
                remaining = avg_time * (total_stocks - idx)
                progress.setLabelText(
                    f"正在分析 {stock_id} {stock_name}\n"
                    f"進度: {idx}/{total_stocks} ({idx/total_stocks:.1%})\n"
                    f"已用時間: {elapsed:.1f}秒 | 剩餘時間: {remaining:.1f}秒"
                )
                progress.setValue(int(idx / total_stocks * 100))
                if progress.wasCanceled():
                    break
                df = self.fetch_strategy_data(stock_id, selected_date)
                if df.empty:
                    if idx < 10:
                        logging.info(f"股票 {stock_id} 沒有數據")
                    continue
                delisting_threshold = 15
                special_delisted_stocks = ['8406']
                if stock_id in special_delisted_stocks:
                    logging.warning(f"股票 {stock_id} 被標記為下市股票，跳過")
                    continue
                try:
                    last_trading_date = df['date'].iloc[-1]
                    if not isinstance(last_trading_date, datetime):
                        last_trading_date = pd.to_datetime(last_trading_date)
                    selected_date_dt = datetime.strptime(selected_date, "%Y-%m-%d")
                    date_diff = (selected_date_dt - last_trading_date).days
                    logging.info(f"股票 {stock_id} 最後交易日: {last_trading_date}, 選擇日期: {selected_date_dt}, 相差天數: {date_diff}")
                    if date_diff > delisting_threshold:
                        logging.warning(f"股票 {stock_id} 已無交易，最後交易日: {last_trading_date}，相差 {date_diff} 天，跳過")
                        continue
                except Exception as e:
                    logging.error(f"處理股票 {stock_id} 日期時出錯: {str(e)}")
                    continue
                conditions = self.strategies[current_strategy]
                condition_results = []
                all_matched = True
                for condition in conditions:
                    matched, message = self.check_condition(df, condition)
                    condition_results.append({"matched": matched, "message": message})
                    if not matched:
                        all_matched = False
                close_price = df.iloc[-1]['Close'] if 'Close' in df.columns else None
                result = {
                    "股票代碼": stock_id,
                    "股票名稱": stock_name,
                    "收盤價": close_price,
                    "條件結果": condition_results,
                    "raw_data": df  # 添加原始數據以便後續分析
                }
                results.append(result)
                if all_matched:
                    matching_stocks.append(stock_id)
                    logging.info(f"股票 {stock_id} {stock_name} 符合所有條件，加入到matching_stocks中")
            
            logging.info(f"符合條件的股票數量: {len(matching_stocks)}")
            logging.info(f"符合條件的股票列表: {matching_stocks}")
            
            # 檢查匹配股票數量與條件結果是否一致
            matched_count_check = 0
            for result in results:
                if "條件結果" in result and result["條件結果"]:
                    if all(cr["matched"] for cr in result["條件結果"]):
                        matched_count_check += 1
            
            if matched_count_check != len(matching_stocks):
                logging.warning(f"警告：匹配股票數量不一致！通過條件檢查: {matched_count_check}, matching_stocks列表: {len(matching_stocks)}")
                
                # 確保所有符合條件的股票都在matching_stocks中
                for result in results:
                    if "條件結果" in result and result["條件結果"]:
                        if all(cr["matched"] for cr in result["條件結果"]):
                            stock_id = result["股票代碼"]
                            if stock_id not in matching_stocks:
                                matching_stocks.append(stock_id)
                                logging.info(f"補充加入符合條件的股票: {stock_id}")
            
            self.update_strategy_results(results, matching_stocks, current_strategy, selected_date)
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_stock = total_time / total_stocks if total_stocks > 0 else 0
            self.show_status(
                f"選股完成！共處理 {total_stocks} 支股票\n"
                f"總用時: {total_time:.1f}秒 | 平均每支: {avg_time_per_stock:.2f}秒"
            )
            progress.setValue(100)
            self.show_status(f"選股完成: {len(matching_stocks)}/{len(results)} 符合條件")
            self.tab_widget.setCurrentIndex(1)
        except Exception as e:
            self.show_status(f"執行策略時發生錯誤: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
        finally:
            progress.close()
    
    def close_db_connections(self):
        try:
            if hasattr(self, 'db_connections'):
                for db_name, conn in self.db_connections.items():
                    logging.info(f"關閉 {db_name} 數據庫連接")
                    conn.close()
                logging.info("所有數據庫連接已關閉")
        except Exception as e:
            logging.error(f"關閉數據庫連接時出錯: {str(e)}")
            logging.error(traceback.format_exc())
    
    def reconnect_database(self):
        try:
            self.close_db_connections()
            self.init_database()
        except Exception as e:
            self.show_status(f"重新連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def closeEvent(self, event):
        for db in self.db_connections.values():
            try:
                db.close()
            except:
                pass
        try:
            self.close_db_connections()
            logging.info("正常關閉應用程序")
        except Exception as e:
            logging.error(f"關閉時發生錯誤: {str(e)}")
            logging.error(traceback.format_exc())
        super().closeEvent(event)

def main():
    db_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db')
    os.makedirs(db_folder, exist_ok=True)
      
    price_db_path = os.path.join(db_folder, 'price.db')
    if not os.path.exists(price_db_path):
        print(f"警告: 價格數據庫文件不存在: {price_db_path}")
        print("請確保您已經導入了股價數據")
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                date TEXT,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Volume REAL,
                PRIMARY KEY (stock_id, date)
            )
            ''')
            conn.commit()
            conn.close()
            print("已創建空的價格數據庫，但您需要導入數據才能進行選股")
        except Exception as e:
            print(f"創建數據庫文件失敗: {str(e)}")
            print(f"請檢查路徑 {db_folder} 是否存在並且有寫入權限")
    app = QApplication(sys.argv)
    win = StockScreenerGUI()
    win.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
