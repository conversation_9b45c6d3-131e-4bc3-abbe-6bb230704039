#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試即時股價監控系統整合
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_realtime_monitor_import():
    """測試即時監控模組導入"""
    print("🧪 測試即時監控模組導入")
    print("=" * 50)
    
    try:
        from real_time_stock_monitor import RealTimeStockMonitor
        print("✅ real_time_stock_monitor 模組導入成功")
        
        from core_web_crawler import YahooFinanceCrawler
        print("✅ core_web_crawler 模組導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_realtime_monitor_creation():
    """測試即時監控窗口創建"""
    print("\n🏗️ 測試即時監控窗口創建")
    print("=" * 50)
    
    try:
        app = QApplication(sys.argv)
        
        from real_time_stock_monitor import RealTimeStockMonitor
        
        # 創建監控窗口
        monitor = RealTimeStockMonitor()
        print("✅ 即時監控窗口創建成功")
        
        # 檢查基本屬性
        print(f"📊 窗口標題: {monitor.windowTitle()}")
        print(f"🔧 爬蟲實例: {'✅' if hasattr(monitor, 'crawler') else '❌'}")
        print(f"📋 自選股列表: {'✅' if hasattr(monitor, 'watchlist') else '❌'}")
        
        # 不顯示窗口，只測試創建
        monitor.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口創建失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_main_gui_integration():
    """測試主程式整合"""
    print("\n🔗 測試主程式整合")
    print("=" * 50)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        print("✅ 主程式模組導入成功")
        
        # 檢查是否有即時監控方法
        if hasattr(StockScreenerGUI, 'open_realtime_stock_monitor'):
            print("✅ 即時監控方法已整合到主程式")
        else:
            print("❌ 即時監控方法未找到")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 主程式導入失敗: {e}")
        return False

def test_crawler_functionality():
    """測試爬蟲功能"""
    print("\n🕷️ 測試爬蟲功能")
    print("=" * 50)
    
    try:
        from core_web_crawler import YahooFinanceCrawler
        
        crawler = YahooFinanceCrawler()
        print("✅ 爬蟲實例創建成功")
        
        # 測試單一股票獲取
        print("📊 測試台積電股價獲取...")
        result = crawler.get_single_stock_price("2330")
        
        if result:
            print("✅ 股價獲取成功")
            print(f"  股票代碼: {result['stock_code']}")
            print(f"  股票名稱: {result['stock_name']}")
            print(f"  現在價格: {result['current_price']}")
            print(f"  數據來源: {result['source']}")
        else:
            print("❌ 股價獲取失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 爬蟲測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 即時股價監控系統整合測試")
    print("=" * 70)
    
    # 測試1: 模組導入
    import_test = test_realtime_monitor_import()
    
    # 測試2: 窗口創建
    creation_test = test_realtime_monitor_creation() if import_test else False
    
    # 測試3: 主程式整合
    integration_test = test_main_gui_integration()
    
    # 測試4: 爬蟲功能
    crawler_test = test_crawler_functionality() if import_test else False
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 整合測試結果總結")
    print("=" * 30)
    
    print(f"模組導入測試: {'✅ 通過' if import_test else '❌ 失敗'}")
    print(f"窗口創建測試: {'✅ 通過' if creation_test else '❌ 失敗'}")
    print(f"主程式整合測試: {'✅ 通過' if integration_test else '❌ 失敗'}")
    print(f"爬蟲功能測試: {'✅ 通過' if crawler_test else '❌ 失敗'}")
    
    all_passed = all([import_test, creation_test, integration_test, crawler_test])
    
    if all_passed:
        print("\n🎉 所有測試通過！即時股價監控系統整合成功！")
        print("🚀 可以從主程式的「工具」菜單啟動即時監控功能")
    else:
        print("\n⚠️ 部分測試失敗，需要進一步調試")
        
        if not import_test:
            print("💡 建議檢查模組文件是否存在")
        if not creation_test:
            print("💡 建議檢查PyQt6和依賴套件安裝")
        if not integration_test:
            print("💡 建議檢查主程式方法整合")
        if not crawler_test:
            print("💡 建議檢查網路連線和Selenium配置")

if __name__ == "__main__":
    main()
