#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息資料庫整合功能
"""

import sys
import os
import logging
import sqlite3
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit, QMessageBox
from PyQt6.QtCore import Qt

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DividendDatabaseTestWindow(QMainWindow):
    """除權息資料庫測試視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("除權息資料庫整合測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 測試按鈕
        self.test_db_btn = QPushButton("🗄️ 測試除權息資料庫")
        self.test_db_btn.clicked.connect(self.test_dividend_database)
        layout.addWidget(self.test_db_btn)
        
        self.test_config_btn = QPushButton("⚙️ 測試資料庫配置對話框")
        self.test_config_btn.clicked.connect(self.test_database_config_dialog)
        layout.addWidget(self.test_config_btn)
        
        self.test_integration_btn = QPushButton("🔗 測試完整整合")
        self.test_integration_btn.clicked.connect(self.test_full_integration)
        layout.addWidget(self.test_integration_btn)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        self.log("🚀 除權息資料庫整合測試系統已啟動")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def test_dividend_database(self):
        """測試除權息資料庫"""
        self.log("📊 開始測試除權息資料庫...")
        
        try:
            # 測試資料庫連接
            db_path = "D:/Finlab/history/tables/dividend_data.db"
            
            if not os.path.exists(db_path):
                self.log(f"❌ 資料庫檔案不存在: {db_path}")
                return
            
            # 連接資料庫
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            self.log(f"📋 資料庫表格: {tables}")
            
            if 'dividend_data' in tables:
                # 獲取統計資訊
                cursor.execute("SELECT COUNT(*) FROM dividend_data")
                total_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM dividend_data")
                stock_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE ex_dividend_date IS NOT NULL")
                with_dates = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT year) FROM dividend_data")
                year_count = cursor.fetchone()[0]
                
                self.log(f"✅ 除權息資料統計:")
                self.log(f"   • 總記錄數: {total_count:,}")
                self.log(f"   • 股票數量: {stock_count:,}")
                self.log(f"   • 有日期記錄: {with_dates:,} ({with_dates/total_count*100:.1f}%)")
                self.log(f"   • 年份數量: {year_count}")
                
                # 顯示範例資料
                cursor.execute("SELECT stock_code, stock_name, year, ex_dividend_date, cash_dividend, total_dividend FROM dividend_data WHERE ex_dividend_date IS NOT NULL LIMIT 5")
                samples = cursor.fetchall()
                
                self.log(f"📋 資料範例:")
                for sample in samples:
                    self.log(f"   • {sample[0]} {sample[1]}: {sample[3]} 股利{sample[5]:.2f}元")
                
            else:
                self.log("❌ dividend_data 表格不存在")
            
            conn.close()
            self.log("✅ 除權息資料庫測試完成")
            
        except Exception as e:
            self.log(f"❌ 除權息資料庫測試失敗: {e}")
    
    def test_database_config_dialog(self):
        """測試資料庫配置對話框"""
        self.log("⚙️ 開始測試資料庫配置對話框...")
        
        try:
            from dialogs.database_config_dialog import DatabaseConfigDialog
            
            # 創建配置對話框
            dialog = DatabaseConfigDialog(self)
            
            self.log("✅ 資料庫配置對話框創建成功")
            
            # 顯示對話框
            result = dialog.exec()
            
            if result:
                self.log("✅ 資料庫配置對話框測試完成")
            else:
                self.log("ℹ️ 使用者取消了配置對話框")
                
        except Exception as e:
            self.log(f"❌ 資料庫配置對話框測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")
    
    def test_full_integration(self):
        """測試完整整合"""
        self.log("🔗 開始測試完整整合...")
        
        try:
            # 1. 測試配置管理器
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            config = config_manager.get_config()
            
            self.log("✅ 配置管理器載入成功")
            
            # 檢查除權息資料庫配置
            db_config = config.get('database', {})
            dividend_path = db_config.get('dividend_db_path', '')
            dividend_enabled = db_config.get('dividend_enabled', False)
            
            self.log(f"📊 除權息資料庫配置:")
            self.log(f"   • 路徑: {dividend_path}")
            self.log(f"   • 啟用: {dividend_enabled}")
            
            # 2. 測試除權息爬蟲
            from enhanced_dividend_crawler import EnhancedDividendCrawler
            crawler = EnhancedDividendCrawler()
            
            self.log("✅ 除權息爬蟲初始化成功")
            
            # 3. 測試資料獲取
            test_data = crawler.get_dividend_data_by_year(2025)
            self.log(f"✅ 獲取2025年資料: {len(test_data)} 筆")
            
            # 4. 測試配置對話框整合
            from dialogs.database_config_dialog import DatabaseConfigDialog
            dialog = DatabaseConfigDialog(self, config)
            
            self.log("✅ 配置對話框整合成功")
            
            self.log("🎉 完整整合測試成功！")
            
            # 顯示成功訊息
            QMessageBox.information(
                self, 
                "測試成功", 
                "🎉 除權息資料庫整合測試全部通過！\n\n"
                "✅ 資料庫連接正常\n"
                "✅ 配置管理正常\n"
                "✅ 爬蟲功能正常\n"
                "✅ 對話框整合正常"
            )
            
        except Exception as e:
            self.log(f"❌ 完整整合測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")
            
            QMessageBox.critical(
                self, 
                "測試失敗", 
                f"❌ 整合測試失敗:\n{str(e)}"
            )

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("除權息資料庫整合測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = DividendDatabaseTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
