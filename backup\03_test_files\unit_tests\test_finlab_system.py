#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab系統測試腳本
測試數據分析、移植和GUI功能
"""

import os
import sys
import unittest
import tempfile
import shutil
import pandas as pd
import sqlite3
from unittest.mock import patch, MagicMock
import tkinter as tk

# 添加當前目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from finlab_tables_analysis_and_migration import FinlabTablesAnalyzer, FinlabDataCrawler

class TestFinlabTablesAnalyzer(unittest.TestCase):
    """測試Finlab數據表格分析器"""
    
    def setUp(self):
        """設置測試環境"""
        self.test_dir = tempfile.mkdtemp()
        self.analyzer = FinlabTablesAnalyzer(self.test_dir)
        
        # 創建測試數據
        self.create_test_data()
    
    def tearDown(self):
        """清理測試環境"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
        if os.path.exists(self.analyzer.db_path):
            os.remove(self.analyzer.db_path)
        if os.path.exists(self.analyzer.local_data_path):
            shutil.rmtree(self.analyzer.local_data_path, ignore_errors=True)
    
    def create_test_data(self):
        """創建測試用的pkl檔案"""
        # 創建測試DataFrame
        test_data = {
            'balance_sheet.pkl': pd.DataFrame({
                '2330': [100, 200, 300],
                '2317': [150, 250, 350],
                '2454': [120, 220, 320]
            }, index=pd.date_range('2023-01-01', periods=3, freq='Q')),
            
            'income_sheet.pkl': pd.DataFrame({
                '2330': [50, 60, 70],
                '2317': [45, 55, 65],
                '2454': [40, 50, 60]
            }, index=pd.date_range('2023-01-01', periods=3, freq='Q')),
            
            'pe.pkl': pd.DataFrame({
                '2330': [15.5, 16.2, 14.8],
                '2317': [12.3, 13.1, 11.9],
                '2454': [18.7, 19.2, 17.5]
            }, index=pd.date_range('2023-01-01', periods=3, freq='M'))
        }
        
        # 保存測試數據
        for filename, df in test_data.items():
            filepath = os.path.join(self.test_dir, filename)
            df.to_pickle(filepath)
    
    def test_analyze_pkl_file(self):
        """測試pkl檔案分析"""
        pkl_file = os.path.join(self.test_dir, 'balance_sheet.pkl')
        result = self.analyzer.analyze_pkl_file(pkl_file)
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['file_name'], 'balance_sheet.pkl')
        self.assertEqual(result['data_type'], 'DataFrame')
        self.assertEqual(result['shape'], (3, 3))
        self.assertEqual(result['stock_count'], 3)
    
    def test_analyze_all_tables(self):
        """測試分析所有表格"""
        results = self.analyzer.analyze_all_tables()
        
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 3)  # 3個測試檔案
        
        for filename in ['balance_sheet.pkl', 'income_sheet.pkl', 'pe.pkl']:
            self.assertIn(filename, results)
            self.assertIn('shape', results[filename])
    
    def test_convert_pkl_to_sqlite(self):
        """測試pkl轉SQLite"""
        pkl_file = os.path.join(self.test_dir, 'balance_sheet.pkl')
        success = self.analyzer.convert_pkl_to_sqlite(pkl_file, 'test_table')
        
        self.assertTrue(success)
        
        # 驗證數據庫
        conn = sqlite3.connect(self.analyzer.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_table'")
        table_exists = cursor.fetchone() is not None
        conn.close()
        
        self.assertTrue(table_exists)
    
    def test_migrate_all_tables(self):
        """測試移植所有表格"""
        success = self.analyzer.migrate_all_tables()
        self.assertTrue(success)
        
        # 檢查數據庫是否創建
        self.assertTrue(os.path.exists(self.analyzer.db_path))
        
        # 檢查表格是否創建
        conn = sqlite3.connect(self.analyzer.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        self.assertGreater(len(tables), 0)
    
    def test_create_data_summary(self):
        """測試創建數據摘要"""
        # 先移植數據
        self.analyzer.migrate_all_tables()
        
        summary = self.analyzer.create_data_summary()
        
        self.assertIsInstance(summary, dict)
        self.assertIn('database_path', summary)
        self.assertIn('total_tables', summary)
        self.assertIn('tables', summary)
        self.assertGreater(summary['total_tables'], 0)
    
    def test_generate_analysis_report(self):
        """測試生成分析報告"""
        report_path = self.analyzer.generate_analysis_report()
        
        self.assertIsInstance(report_path, str)
        self.assertTrue(os.path.exists(report_path))
        
        # 檢查報告內容
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('Finlab數據表格分析報告', content)
        self.assertIn('分析概述', content)

class TestFinlabDataCrawler(unittest.TestCase):
    """測試Finlab數據爬取器"""
    
    def setUp(self):
        """設置測試環境"""
        self.test_db = tempfile.mktemp(suffix='.db')
        self.crawler = FinlabDataCrawler(self.test_db)
    
    def tearDown(self):
        """清理測試環境"""
        if os.path.exists(self.test_db):
            os.remove(self.test_db)
    
    @patch('requests.get')
    def test_fetch_data_from_api(self, mock_get):
        """測試API數據獲取"""
        # 模擬API響應
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'data': [
                {'date': '2023-01-01', '2330': 100, '2317': 150},
                {'date': '2023-01-02', '2330': 105, '2317': 155}
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        df = self.crawler.fetch_data_from_api('balance_sheet')
        
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 2)
        self.assertIn('date', df.columns)
    
    def test_update_database(self):
        """測試數據庫更新"""
        # 創建測試數據
        test_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=3),
            '2330': [100, 105, 110],
            '2317': [150, 155, 160]
        })
        
        success = self.crawler.update_database('test_table', test_df)
        self.assertTrue(success)
        
        # 驗證數據
        conn = sqlite3.connect(self.test_db)
        result_df = pd.read_sql_query("SELECT * FROM test_table", conn)
        conn.close()
        
        self.assertEqual(len(result_df), 3)

class TestFinlabGUI(unittest.TestCase):
    """測試Finlab GUI"""
    
    def setUp(self):
        """設置測試環境"""
        self.root = tk.Tk()
        self.root.withdraw()  # 隱藏窗口
    
    def tearDown(self):
        """清理測試環境"""
        self.root.destroy()
    
    def test_gui_import(self):
        """測試GUI模組導入"""
        try:
            from finlab_tables_gui import FinlabTablesGUI
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"GUI模組導入失敗: {e}")
    
    def test_gui_initialization(self):
        """測試GUI初始化"""
        try:
            from finlab_tables_gui import FinlabTablesGUI
            app = FinlabTablesGUI(self.root)
            self.assertIsNotNone(app)
            self.assertEqual(app.root, self.root)
        except Exception as e:
            self.fail(f"GUI初始化失敗: {e}")

def run_integration_test():
    """執行整合測試"""
    print("🧪 執行Finlab系統整合測試")
    print("=" * 60)
    
    try:
        # 創建測試環境
        test_dir = tempfile.mkdtemp()
        print(f"📁 測試目錄: {test_dir}")
        
        # 創建測試數據
        test_data = pd.DataFrame({
            '2330': [100, 200, 300],
            '2317': [150, 250, 350]
        }, index=pd.date_range('2023-01-01', periods=3, freq='Q'))
        
        test_file = os.path.join(test_dir, 'balance_sheet.pkl')
        test_data.to_pickle(test_file)
        print(f"✅ 創建測試數據: {test_file}")
        
        # 測試分析器
        analyzer = FinlabTablesAnalyzer(test_dir)
        
        # 分析數據
        results = analyzer.analyze_all_tables()
        print(f"✅ 分析完成: {len(results)} 個檔案")
        
        # 移植數據
        success = analyzer.migrate_all_tables()
        print(f"✅ 移植{'成功' if success else '失敗'}")
        
        # 生成報告
        report_path = analyzer.generate_analysis_report()
        print(f"✅ 報告生成: {report_path}")
        
        # 清理
        shutil.rmtree(test_dir, ignore_errors=True)
        if os.path.exists(analyzer.db_path):
            os.remove(analyzer.db_path)
        if os.path.exists(analyzer.local_data_path):
            shutil.rmtree(analyzer.local_data_path, ignore_errors=True)
        
        print("🎉 整合測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 整合測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 Finlab系統測試套件")
    print("=" * 60)
    
    # 執行單元測試
    print("\n📋 執行單元測試...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 執行整合測試
    print("\n🔗 執行整合測試...")
    run_integration_test()

if __name__ == "__main__":
    main()
