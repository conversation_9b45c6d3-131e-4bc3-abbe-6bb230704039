#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正 newprice.db 中的 date 欄位格式，只保留年月日
"""

import sqlite3
import os
import shutil
from datetime import datetime

def fix_date_format_in_newprice_db():
    """修正 date 欄位格式，只保留年月日"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    backup_file = 'D:/Finlab/history/tables/newprice_date_backup.db'
    
    print("=" * 80)
    print("🔄 修正 newprice.db 中的 date 欄位格式")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查原始檔案大小
    original_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
    print(f"📁 原始檔案: {db_file}")
    print(f"📊 原始檔案大小: {original_size:.2f} MB")
    
    try:
        # 創建備份
        print(f"\n💾 創建備份檔案...")
        shutil.copy2(db_file, backup_file)
        backup_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
        print(f"✅ 備份完成: {backup_file} ({backup_size:.2f} MB)")
        
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查原始資料統計
        print(f"\n📊 原始資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        total_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {total_count:,}")
        
        # 檢查 date 格式範例
        print(f"\n📋 原始 date 格式範例:")
        cursor.execute("SELECT date FROM price LIMIT 5")
        sample_dates = cursor.fetchall()
        for date, in sample_dates:
            print(f"   {date}")
        
        # 檢查日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        date_range = cursor.fetchone()
        print(f"   日期範圍: {date_range[0]} ~ {date_range[1]}")
        
        # 修正 date 欄位格式
        print(f"\n🔧 修正 date 欄位格式...")
        print("⏳ 將 TIMESTAMP 格式轉換為 DATE 格式...")
        
        # 使用 SQL 直接更新 date 欄位，只保留年月日部分
        cursor.execute("""
            UPDATE price 
            SET date = DATE(date)
        """)
        
        updated_rows = cursor.rowcount
        print(f"✅ 已更新 {updated_rows:,} 筆資料")
        
        # 提交事務
        conn.commit()
        print(f"✅ 事務已提交")
        
        # 驗證修正結果
        print(f"\n🔍 驗證修正結果:")
        cursor.execute("SELECT date FROM price LIMIT 5")
        new_sample_dates = cursor.fetchall()
        print(f"   修正後 date 格式範例:")
        for date, in new_sample_dates:
            print(f"     {date}")
        
        # 檢查修正後的日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM price")
        new_date_range = cursor.fetchone()
        print(f"   修正後日期範圍: {new_date_range[0]} ~ {new_date_range[1]}")
        
        # 檢查資料完整性
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   最終資料筆數: {final_count:,}")
        
        if final_count == total_count:
            print("✅ 資料完整性驗證通過")
        else:
            print("❌ 資料完整性驗證失敗")
        
        # 檢查是否還有時分秒資訊
        cursor.execute("SELECT COUNT(*) FROM price WHERE date LIKE '% %'")
        time_info_count = cursor.fetchone()[0]
        print(f"   包含時分秒的記錄: {time_info_count} 筆")
        
        # 關閉連接後重新開啟進行 VACUUM
        conn.close()
        
        # 優化資料庫以減少檔案大小
        print(f"\n🔧 優化資料庫以減少檔案大小...")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 最終驗證
        print(f"\n📊 最終驗證:")
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   最終資料筆數: {final_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        final_unique = cursor.fetchone()[0]
        print(f"   最終不重複股票數: {final_unique:,}")
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(price)")
        columns = cursor.fetchall()
        print(f"   最終表格結構:")
        for col in columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 檢查最終範例
        cursor.execute("SELECT stock_id, stock_name, date, 收盤價 FROM price LIMIT 5")
        final_sample = cursor.fetchall()
        print(f"   最終資料範例:")
        for stock_id, stock_name, date, close_price in final_sample:
            print(f"     {stock_id} | {stock_name} | {date} | {close_price}")
        
        conn.close()
        
        # 檢查最終檔案大小
        final_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
        space_saved = original_size - final_size
        space_saved_percent = (space_saved / original_size) * 100
        
        print(f"\n💾 檔案大小變化:")
        print(f"   原始大小: {original_size:.2f} MB")
        print(f"   最終大小: {final_size:.2f} MB")
        if space_saved > 0:
            print(f"   節省空間: {space_saved:.2f} MB ({space_saved_percent:.1f}%)")
        else:
            print(f"   大小變化: {-space_saved:.2f} MB")
        
        print(f"\n✅ date 格式修正完成！")
        print(f"📁 主檔案: {db_file}")
        print(f"📁 備份檔案: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修正過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出錯，嘗試從備份恢復
        if os.path.exists(backup_file):
            print(f"\n🔄 嘗試從備份恢復...")
            try:
                shutil.copy2(backup_file, db_file)
                print(f"✅ 已從備份恢復")
            except Exception as restore_error:
                print(f"❌ 恢復失敗: {restore_error}")
        
        return False

def verify_date_format():
    """驗證 date 格式修正結果"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    
    print("\n" + "=" * 80)
    print("🔍 驗證 date 格式修正結果")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查是否還有時分秒資訊
        cursor.execute("SELECT COUNT(*) FROM price WHERE date LIKE '% %'")
        time_info_count = cursor.fetchone()[0]
        
        if time_info_count == 0:
            print("✅ 確認：所有日期都已移除時分秒資訊")
        else:
            print(f"❌ 警告：仍有 {time_info_count} 筆記錄包含時分秒")
        
        # 檢查日期格式
        cursor.execute("SELECT date FROM price LIMIT 10")
        sample_dates = cursor.fetchall()
        print(f"📅 日期格式範例:")
        for date, in sample_dates:
            print(f"   {date}")
        
        # 檢查日期長度
        cursor.execute("SELECT DISTINCT LENGTH(date) as date_length, COUNT(*) as count FROM price GROUP BY LENGTH(date)")
        date_lengths = cursor.fetchall()
        print(f"\n📊 日期長度分布:")
        for length, count in date_lengths:
            print(f"   長度 {length}: {count:,} 筆")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

if __name__ == "__main__":
    success = fix_date_format_in_newprice_db()
    if success:
        verify_date_format()
    else:
        print("❌ 修正失敗，請檢查錯誤訊息")
