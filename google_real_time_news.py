#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google新聞即時爬蟲
程式碼撰寫: 蘇彥庭
日期: 20210111

2023/04/08程式修改
1. 處理Google RSS連結: 原本為新聞連結 現在被改為Google頁面連結 連結該Google頁面後才會被轉向實際新聞連結
2. 修改經濟日報新聞內容抓取方式

2024/08/14程式修改
1. 處理日期轉換問題
2. 處理Google RSS連結問題
採用此Github專案: https://github.com/SSujitX/google-news-url-decoder/tree/main
提供的Decoder取得正確新聞網址(但實測部分網址可能還是會解析錯誤)
3. 修改鉅亨網新聞內容爬蟲程式碼

2025/07/18 整合修改
- 整合到主GUI系統
- 添加資料庫儲存功能
- 優化錯誤處理
- 添加進度回報功能
"""

import requests
import pandas as pd
import sqlite3
import os
import logging
from datetime import datetime, timedelta
import time
import re
from bs4 import BeautifulSoup
import base64

# google-news-url-decoder
# 移除複雜的 batch execute 函數，因為 Google API 格式經常變化


# 簡化的 Google News URL 解碼器
def decode_google_news_url(source_url):
    """簡化的 Google News URL 解碼，減少錯誤訊息"""
    # 直接返回原始 URL，避免複雜的解碼過程
    return source_url


# 整理Google新聞資料用
def arrangeGoogleNews(elem):
    return ([elem.find('title').getText(),
             elem.find('link').getText(),
             elem.find('pubDate').getText(),
             BeautifulSoup(elem.find('description').getText(), 'html.parser').find('a').getText(),
             elem.find('source').getText()])


class GoogleStockNewsCrawler:
    """Google股票新聞爬蟲 - 基於蘇彥庭的原始程式碼"""

    def __init__(self, db_path=None, progress_callback=None):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path or "D:/Finlab/history/tables/news.db"
        self.progress_callback = progress_callback

        # 初始化資料庫
        self.init_database()

        self.logger.info("✅ Google股票新聞爬蟲初始化完成")
    
    def init_database(self):
        """初始化新聞資料庫"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 創建新聞表格 (與原始程式碼相容的欄位結構)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news_content (
                    news_id TEXT PRIMARY KEY,
                    date TEXT,
                    time TEXT,
                    source TEXT,
                    title TEXT,
                    reporter TEXT,
                    link TEXT,
                    content TEXT,
                    stock_code TEXT,
                    search_keyword TEXT,
                    news_summary TEXT,
                    crawl_time TEXT,
                    pub_date TEXT,
                    news_url TEXT
                )
            ''')

            # 檢查並添加缺失的欄位
            cursor.execute("PRAGMA table_info(news_content)")
            columns = [column[1] for column in cursor.fetchall()]

            required_columns = {
                'search_keyword': 'TEXT',
                'pub_date': 'TEXT',
                'news_url': 'TEXT',
                'news_summary': 'TEXT',
                'crawl_time': 'TEXT'
            }

            for column_name, column_type in required_columns.items():
                if column_name not in columns:
                    try:
                        cursor.execute(f'ALTER TABLE news_content ADD COLUMN {column_name} {column_type}')
                        self.logger.info(f"✅ 添加欄位: {column_name}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 添加欄位 {column_name} 失敗: {e}")

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 資料庫初始化完成: {self.db_path}")

        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")

    def _report_progress(self, message):
        """回報進度"""
        if self.progress_callback:
            self.progress_callback(message)
        self.logger.info(message)
    
    def search_stock_news(self, stock_code: str, days: int = 7):
        """搜尋特定股票的新聞 - 基於原始程式碼邏輯"""
        try:
            self._report_progress(f"🔍 開始搜尋 {stock_code} 的Google新聞...")

            # 獲取股票名稱並構建搜尋關鍵字
            stock_name = self._get_stock_name(stock_code)
            search_keyword = f"{stock_code}{stock_name}" if stock_name else stock_code

            self._report_progress(f"🔍 搜尋關鍵字: {search_keyword}")

            # 建立搜尋網址 (使用原始程式碼的URL格式)
            url = f'https://news.google.com/news/rss/search/section/q/{search_keyword}/?hl=zh-tw&gl=TW&ned=zh-tw_tw'

            # 取得RSS資料
            response = requests.get(url)
            soup = BeautifulSoup(response.text, 'xml')
            items = soup.find_all('item')

            if not items:
                self._report_progress("❌ 沒有找到相關新聞")
                return []

            self._report_progress(f"📰 找到 {len(items)} 筆RSS新聞項目")

            # 整理Google新聞資料
            rows = [arrangeGoogleNews(elem) for elem in items]

            # 組成pandas DataFrame
            df = pd.DataFrame(data=rows, columns=['title', 'link', 'pub_date', 'description', 'source'])

            # 新增時間戳記欄位
            df.insert(0, 'search_time', time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), True)
            # 新增搜尋字串
            df.insert(1, 'search_key', search_keyword, True)

            # 篩選最近的新聞
            near_start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            df['pub_date'] = pd.to_datetime(df['pub_date'])
            df = df[df['pub_date'] >= near_start_date]

            if df.empty:
                self._report_progress(f"❌ 沒有找到最近 {days} 天的新聞")
                return []

            # 按發布時間排序
            df = df.sort_values(['pub_date']).reset_index(drop=True)

            self._report_progress(f"✅ 篩選出 {len(df)} 筆最近 {days} 天的新聞")

            # 爬取新聞內容
            news_list = self._crawl_news_content(df, stock_code)

            return news_list

        except Exception as e:
            self.logger.error(f"❌ 搜尋 {stock_code} 新聞失敗: {e}")
            return []

    def _crawl_news_content(self, df, stock_code):
        """爬取新聞內容 - 基於原始程式碼的beautifulSoupNews函數"""
        news_list = []

        for i, row in df.iterrows():
            try:
                self._report_progress(f"📰 正在下載新聞內容 {i+1}/{len(df)}: {row['title'][:30]}...")

                # 使用原始程式碼的新聞內容爬取邏輯
                try:
                    news_url, content = self._beautiful_soup_news(row['link'])
                    # 檢查內容是否有效
                    if not content or content == 'unknow domain':
                        content = row['description']  # 使用描述作為備用內容
                except Exception as content_error:
                    self.logger.warning(f"⚠️ 內容爬取失敗，使用基本資訊: {str(content_error)[:100]}...")
                    # 如果內容爬取失敗，仍然保存基本新聞資訊
                    news_url = row['link']
                    content = row['description']  # 使用描述作為內容

                # 構建新聞項目
                news_item = {
                    'search_time': row['search_time'],
                    'search_keyword': row['search_key'],
                    'title': row['title'],
                    'link': row['link'],
                    'pub_date': row['pub_date'].strftime('%Y-%m-%d'),
                    'description': row['description'],
                    'source': row['source'],
                    'news_url': news_url,
                    'content': content if content and content != 'unknow domain' else row['description'],
                    'stock_code': stock_code
                }

                news_list.append(news_item)

                # 避免過於頻繁的請求 (原始程式碼使用3秒，但可以縮短)
                time.sleep(1)  # 縮短等待時間

            except Exception as e:
                self.logger.warning(f"⚠️ 處理新聞項目失敗: {e}")
                # 即使處理失敗，也嘗試保存基本資訊
                try:
                    news_item = {
                        'search_time': row['search_time'],
                        'search_keyword': row['search_key'],
                        'title': row['title'],
                        'link': row['link'],
                        'pub_date': row['pub_date'].strftime('%Y-%m-%d'),
                        'description': row['description'],
                        'source': row['source'],
                        'news_url': row['link'],
                        'content': row['description'],
                        'stock_code': stock_code
                    }
                    news_list.append(news_item)
                except:
                    continue

        self._report_progress(f"✅ 成功處理 {len(news_list)} 筆新聞")
        return news_list

    def _beautiful_soup_news(self, url):
        """擷取各家新聞網站新聞函數 - 基於原始程式碼"""

        # 設定headers (與原始程式碼相同)
        headers = {'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                                 'Chrome/87.0.4280.141 Safari/537.36'}

        # 直接使用原始 URL，跳過 Google 解碼（因為 Google API 格式經常變化）
        news_url = url

        # 如果是 Google News URL，嘗試簡單的解碼，失敗就使用原始 URL
        if 'news.google.com' in url and '/articles/' in url:
            try:
                # 嘗試簡單的 base64 解碼
                import urllib.parse
                parsed_url = urllib.parse.urlparse(url)
                if parsed_url.path.startswith('/articles/'):
                    article_id = parsed_url.path.split('/')[-1]
                    try:
                        decoded_bytes = base64.urlsafe_b64decode(article_id + "==")
                        decoded_str = decoded_bytes.decode("latin1", errors='ignore')
                        # 尋找 http 開頭的 URL
                        import re
                        urls = re.findall(r'https?://[^\s\x00-\x1f\x7f-\x9f]+', decoded_str)
                        if urls:
                            news_url = urls[0]
                    except:
                        pass  # 解碼失敗，使用原始 URL
            except:
                pass  # 任何錯誤都使用原始 URL

        # 如果是 Google News URL，直接跳過內容爬取，使用描述
        if 'news.google.com' in news_url:
            return news_url, "Google News URL - 使用新聞描述"

        # 取得該篇新聞連結內容
        try:
            response = requests.get(news_url, headers=headers, timeout=15)
            response.raise_for_status()  # 檢查 HTTP 狀態碼
            soup = BeautifulSoup(response.text, 'html.parser')
        except Exception as e:
            raise Exception(f"無法取得新聞內容 ({news_url}): {e}")

        # 判斷url網域做對應文章擷取
        try:
            domain = re.findall('https://[^/]*', news_url)[0].replace('https://', '')
        except:
            print(f'網址解析錯誤: {news_url}')
            content = 'unknow domain'
            return news_url, content

        if domain == 'udn.com':
            # 聯合新聞網
            item = soup.find_all('section', class_='article-content__editor')[0].find_all('p')
            content = [elem.getText() for elem in item]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ')

        elif domain == 'ec.ltn.com.tw':
            # 自由財經
            item = soup.find_all('div', class_='text')[0].find_all('p', class_='')
            content = [elem.getText() for elem in item]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ').replace(u'\xa0', ' '). \
                replace('一手掌握經濟脈動', '').replace('點我訂閱自由財經Youtube頻道', '')

        elif domain in ['tw.stock.yahoo.com', 'tw.news.yahoo.com']:
            # Yahoo奇摩股市
            item = soup.find_all('div', class_='caas-body')[0].find_all('p')
            content = [elem.getText() for elem in item]
            del_text = soup.find_all('div', class_='caas-body')[0].find_all('a')
            del_text = [elem.getText() for elem in del_text]
            content = [elem for elem in content if elem not in del_text]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ').replace(u'\xa0', ' ')

        elif domain == 'money.udn.com':
            # 經濟日報
            item = soup.find_all('section', id='article_body')[0].find_all('p')
            content = [elem.getText() for elem in item]
            content = [elem for elem in content]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ')

        elif domain == 'www.chinatimes.com':
            # 中時新聞網
            item = soup.find_all('div', class_='article-body')[0].find_all('p')
            content = [elem.getText() for elem in item]
            content = [elem for elem in content]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ')

        elif domain == 'ctee.com.tw':
            # 工商時報
            item = soup.find_all('div', class_='entry-content clearfix single-post-content')[0].find_all('p')
            content = [elem.getText() for elem in item]
            content = [elem for elem in content]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ')

        elif domain == 'news.cnyes.com':
            # 鉅亨網
            sections = soup.find_all('section', style='margin-top:30px')
            content = list()
            for section in sections:
                p_tag = section.find('p')
                if p_tag:
                    content.append(p_tag.getText())
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ').replace(u'\xa0', ' ')

        elif domain == 'finance.ettoday.net':
            # ETtoday
            item = soup.find_all('div', itemprop='articleBody')[0].find_all('p')
            content = [elem.getText() for elem in item]
            content = [elem for elem in content]
            content = ''.join(content)
            content = content.replace('\r', ' ').replace('\n', ' ').replace(u'\xa0', ' ')

        elif domain == 'fnc.ebc.net.tw':
            # EBC東森財經新聞
            content = str(soup.find_all('script')[-2]).split('ReactDOM.render(React.createElement(')[1]
            content = content.split(',')[1].replace('{"content":"', '').replace('"})', '')
            content = re.sub(u'\\\\u003[a-z]+', '', content)
            content = content.replace('/p', ' ').replace('\\n', '')

        else:
            # 未知domain
            content = 'unknow domain'

        return news_url, content

    def _get_stock_name(self, stock_code):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科',
            '2308': '台達電',
            '2412': '中華電',
            '2881': '富邦金',
            '2882': '國泰金',
            '00930': '永豐ESG',
            '00903': '富邦元宇宙',
            '0050': '元大台灣50',
            '0056': '元大高股息'
        }
        return stock_names.get(stock_code, '')

    def save_to_database(self, news_list, stock_code):
        """儲存新聞到資料庫"""
        if not news_list:
            return 0

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            for news in news_list:
                # 生成唯一ID
                news_id = f"google_{news['pub_date'].replace('-', '')}_{hash(news['title']) % 10000}"

                # 解析日期和時間
                pub_date_obj = datetime.strptime(news['pub_date'], '%Y-%m-%d')
                date_str = pub_date_obj.strftime('%Y%m%d')
                time_str = pub_date_obj.strftime('%H%M%S')

                cursor.execute('''
                    INSERT OR IGNORE INTO news_content
                    (news_id, date, time, source, title, reporter, link, content,
                     stock_code, search_keyword, news_summary, crawl_time, pub_date, news_url)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    news_id,
                    date_str,
                    time_str,
                    news['source'],
                    news['title'],
                    '',  # reporter (可以從content中提取)
                    news['link'],
                    news['content'],
                    stock_code,
                    news['search_keyword'],
                    news['description'],  # 使用description作為摘要
                    news['search_time'],
                    news['pub_date'],
                    news['news_url']
                ))

                if cursor.rowcount > 0:
                    saved_count += 1

            conn.commit()
            conn.close()

            self._report_progress(f"✅ 儲存了 {saved_count} 筆新聞到資料庫")
            return saved_count

        except Exception as e:
            self.logger.error(f"❌ 儲存新聞失敗: {e}")
            return 0
    
    def _search_google_news(self, keyword: str, site: str, days: int):
        """使用Google RSS搜尋特定網站的新聞"""
        try:
            # 構建Google News RSS URL
            # 格式: https://news.google.com/rss/search?q=關鍵字+site:網站&hl=zh-TW&gl=TW&ceid=TW:zh-Hant
            encoded_keyword = quote(f"{keyword} site:{site}")
            rss_url = f"https://news.google.com/rss/search?q={encoded_keyword}&hl=zh-TW&gl=TW&ceid=TW:zh-Hant"
            
            self.logger.debug(f"🌐 RSS URL: {rss_url}")
            
            # 設置請求標頭
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/rss+xml, application/xml, text/xml',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            }
            
            # 發送請求
            response = requests.get(rss_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # 解析RSS
            feed = feedparser.parse(response.content)
            
            if not feed.entries:
                return []
            
            news_list = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for entry in feed.entries:
                try:
                    # 解析新聞資訊
                    news_item = self._parse_news_entry(entry, keyword, site)
                    
                    if news_item:
                        # 檢查日期範圍
                        news_date = datetime.strptime(news_item['date'], '%Y%m%d')
                        if news_date >= cutoff_date:
                            news_list.append(news_item)
                
                except Exception as e:
                    self.logger.debug(f"解析新聞項目失敗: {e}")
                    continue
            
            return news_list
            
        except Exception as e:
            self.logger.warning(f"⚠️ Google RSS搜尋失敗 ({site}): {e}")
            return []
    
    def _parse_news_entry(self, entry, keyword, site):
        """解析RSS新聞項目"""
        try:
            # 標題
            title = entry.get('title', '').strip()
            if not title:
                return None
            
            # 連結
            link = entry.get('link', '')
            
            # 發布時間
            published = entry.get('published_parsed')
            if published:
                news_date = datetime(*published[:6])
                date_str = news_date.strftime('%Y%m%d')
                time_str = news_date.strftime('%H%M%S')
            else:
                now = datetime.now()
                date_str = now.strftime('%Y%m%d')
                time_str = now.strftime('%H%M%S')
            
            # 摘要
            summary = entry.get('summary', '').strip()
            if summary:
                # 清理HTML標籤
                summary = BeautifulSoup(summary, 'html.parser').get_text()
            
            # 來源
            source = self._extract_source_from_site(site)
            
            # 記者（從標題或摘要中提取）
            reporter = self._extract_reporter(title, summary)
            
            return {
                'date': date_str,
                'time': time_str,
                'source': source,
                'title': title,
                'reporter': reporter,
                'link': link,
                'summary': summary,
                'search_keyword': keyword,
                'crawl_time': datetime.now().strftime('%Y%m%d %H%M%S')
            }
            
        except Exception as e:
            self.logger.debug(f"解析新聞項目失敗: {e}")
            return None
    
    def _extract_source_from_site(self, site):
        """從網站域名提取來源名稱"""
        source_mapping = {
            'udn.com': '聯合新聞網',
            'ltn.com.tw': '自由財經',
            'tw.stock.yahoo.com': 'Yahoo奇摩股市',
            'money.udn.com': '經濟日報',
            'chinatimes.com': '中時新聞網',
            'ctee.com.tw': '工商時報',
            'cnyes.com': '鉅亨網',
            'ettoday.net': 'ETtoday',
            'ebc.net.tw': 'EBC東森財經新聞'
        }
        return source_mapping.get(site, site)
    
    def _extract_reporter(self, title, summary):
        """從標題或摘要中提取記者名稱"""
        text = f"{title} {summary}"
        
        # 常見的記者名稱模式
        patterns = [
            r'記者[　\s]*([^\s　]{2,4})[　\s]*/',
            r'/記者[　\s]*([^\s　]{2,4})',
            r'([^\s　]{2,4})[　\s]*記者',
            r'文/([^\s　]{2,4})',
            r'撰文[：:][　\s]*([^\s　]{2,4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return ''
    
    def _get_stock_name(self, stock_code):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電',
            '2317': '鴻海',
            '2454': '聯發科',
            '2308': '台達電',
            '2412': '中華電',
            '2881': '富邦金',
            '2882': '國泰金',
            '00930': '永豐ESG',
            '00903': '富邦元宇宙',
            '0050': '元大台灣50',
            '0056': '元大高股息'
        }
        return stock_names.get(stock_code, '')
    
    def _remove_duplicates(self, news_list):
        """去除重複新聞"""
        seen_titles = set()
        unique_news = []
        
        for news in news_list:
            title = news.get('title', '')
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_news.append(news)
        
        return unique_news
    


if __name__ == '__main__':
    # 測試Google股票新聞爬蟲
    logging.basicConfig(level=logging.INFO)

    def progress_callback(message):
        print(f"[進度] {message}")

    crawler = GoogleStockNewsCrawler(progress_callback=progress_callback)

    # 測試搜尋 (使用原始程式碼的測試股票)
    test_stocks = ['2330台積電', '2317鴻海', '2412中華電']

    for search_key in test_stocks:
        # 提取股票代碼
        stock_code = search_key[:4]
        print(f"\n🔍 測試搜尋 {search_key}...")

        news_list = crawler.search_stock_news(stock_code, days=10)

        if news_list:
            print(f"✅ 找到 {len(news_list)} 筆新聞")
            for i, news in enumerate(news_list[:3], 1):
                print(f"  {i}. [{news['source']}] {news['title'][:50]}...")
                print(f"     內容長度: {len(news['content'])} 字元")

            # 儲存到資料庫
            saved = crawler.save_to_database(news_list, stock_code)
            print(f"💾 儲存了 {saved} 筆新聞")

            # 輸出CSV檢查 (與原始程式碼相同)
            df = pd.DataFrame(news_list)
            df.to_csv(f'checkData_{stock_code}.csv', index=False, encoding='utf-8-sig')
            print(f"📄 已輸出 checkData_{stock_code}.csv")
        else:
            print("❌ 沒有找到相關新聞")
