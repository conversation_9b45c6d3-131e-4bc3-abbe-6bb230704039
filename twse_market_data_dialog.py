#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣證交所市場數據爬蟲對話框
提供友好的GUI界面來爬取市場數據
"""

import sys
import logging
import pandas as pd
import sqlite3
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton,
    QLabel, QTextEdit, QProgressBar, QGroupBox, QMessageBox,
    QApplication, QDateEdit, QTableWidget, QTableWidgetItem,
    QTabWidget, QWidget, QHeaderView, QSplitter, QComboBox, QFileDialog,
    QRadioButton
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QColor
from market_data_crawler import MarketDataCrawler

class MarketDataWorker(QThread):
    """市場數據爬蟲工作線程"""

    progress_updated = pyqtSignal(int, str)  # 進度, 訊息
    finished = pyqtSignal(dict)  # 結果
    error_occurred = pyqtSignal(str)  # 錯誤

    def __init__(self, selected_types, start_date=None, end_date=None):
        super().__init__()
        self.selected_types = selected_types
        self.start_date = start_date
        self.end_date = end_date
        self.crawler = MarketDataCrawler()
    
    def run(self):
        """執行爬蟲任務"""
        try:
            results = {}

            # 計算任務權重（歷史數據任務更重）
            task_weights = {}
            total_weight = 0

            for data_type in self.selected_types:
                if data_type == "historical_index":
                    # 歷史數據任務權重更大（根據日期範圍）
                    if self.start_date and self.end_date:
                        from datetime import datetime
                        start = datetime.strptime(self.start_date, '%Y-%m-%d')
                        end = datetime.strptime(self.end_date, '%Y-%m-%d')
                        days = (end - start).days + 1
                        weight = max(50, min(days * 2, 70))  # 50-70的權重
                    else:
                        weight = 20
                    task_weights[data_type] = weight
                else:
                    # 其他任務權重較小
                    task_weights[data_type] = 15
                total_weight += task_weights[data_type]

            current_progress = 0

            for data_type in self.selected_types:
                task_start_progress = current_progress
                task_weight = task_weights[data_type]
                task_end_progress = current_progress + int((task_weight / total_weight) * 90)

                if data_type == "market_index":
                    self.progress_updated.emit(task_start_progress, "正在爬取市場指數資訊...")
                    results['market_index'] = self.crawler.crawl_market_index_info()
                    self.progress_updated.emit(task_end_progress, "市場指數資訊爬取完成")

                elif data_type == "margin_trading":
                    self.progress_updated.emit(task_start_progress, "正在爬取融資融券統計...")
                    results['margin_trading'] = self.crawler.crawl_margin_trading_info()
                    self.progress_updated.emit(task_end_progress, "融資融券統計爬取完成")

                elif data_type == "historical_index":
                    self.progress_updated.emit(task_start_progress, f"正在爬取歷史指數資料 ({self.start_date} 至 {self.end_date})...")

                    # 為歷史數據提供進度回調
                    def progress_callback(current_day, total_days):
                        progress = task_start_progress + int((current_day / total_days) * (task_end_progress - task_start_progress))
                        self.progress_updated.emit(progress, f"正在爬取歷史指數資料 ({current_day}/{total_days} 天)...")

                    results['historical_index'] = self.crawler.crawl_market_historical_index_with_progress(
                        self.start_date, self.end_date, progress_callback
                    )
                    self.progress_updated.emit(task_end_progress, "歷史指數資料爬取完成")

                current_progress = task_end_progress

            self.progress_updated.emit(100, "爬取完成！")
            self.finished.emit(results)

        except Exception as e:
            self.error_occurred.emit(str(e))

class DateOverlapDialog(QDialog):
    """日期重疊處理對話框"""

    def __init__(self, parent, requested_start, requested_end, existing_min, existing_max, overlapping_dates, total_existing):
        super().__init__(parent)
        self.setWindowTitle("📅 檢測到日期重疊")
        self.setModal(True)
        self.setFixedSize(600, 500)

        # 設置樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QRadioButton {
                color: #ffffff;
                font-size: 14px;
                padding: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #888888;
                border-radius: 8px;
                background-color: transparent;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #00d4ff;
                border-radius: 8px;
                background-color: #00d4ff;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 標題
        title_label = QLabel("⚠️ 檢測到日期重疊")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #ff9500; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 信息顯示
        info_text = QTextEdit()
        info_text.setMaximumHeight(200)
        info_text.setReadOnly(True)

        info_content = f"""📊 數據庫現有範圍: {existing_min} 至 {existing_max} ({total_existing} 天)
📅 請求爬取範圍: {requested_start} 至 {requested_end}
⚠️ 重疊日期數量: {len(overlapping_dates)} 天

🔍 重疊的日期:
{self.format_overlapping_dates(overlapping_dates)}

💡 建議:
• 選擇「跳過重疊日期」可節省時間，只下載新數據
• 選擇「覆蓋重疊日期」可確保數據是最新的"""

        info_text.setPlainText(info_content)
        layout.addWidget(info_text)

        # 選項
        options_label = QLabel("請選擇處理方式:")
        options_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-top: 10px;")
        layout.addWidget(options_label)

        # 單選按鈕
        self.skip_radio = QRadioButton("🚀 跳過重疊日期 (推薦) - 只下載新數據，節省時間")
        self.skip_radio.setChecked(True)  # 默認選擇
        layout.addWidget(self.skip_radio)

        self.overwrite_radio = QRadioButton("🔄 覆蓋重疊日期 - 重新下載所有數據，確保最新")
        layout.addWidget(self.overwrite_radio)

        # 按鈕
        button_layout = QHBoxLayout()

        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("background-color: #dc3545;")
        button_layout.addWidget(cancel_btn)

        button_layout.addStretch()

        confirm_btn = QPushButton("✅ 確認")
        confirm_btn.clicked.connect(self.accept)
        confirm_btn.setStyleSheet("background-color: #28a745;")
        button_layout.addWidget(confirm_btn)

        layout.addLayout(button_layout)

    def format_overlapping_dates(self, overlapping_dates):
        """格式化重疊日期顯示"""
        if len(overlapping_dates) <= 10:
            # 如果日期不多，全部顯示
            formatted_dates = []
            for date_str in overlapping_dates:
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    formatted_dates.append(date_obj.strftime('%Y-%m-%d'))
                except:
                    formatted_dates.append(date_str)
            return "   " + ", ".join(formatted_dates)
        else:
            # 如果日期很多，只顯示前幾個和後幾個
            formatted_dates = []
            for i, date_str in enumerate(overlapping_dates):
                if i < 5 or i >= len(overlapping_dates) - 5:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(date_str, '%Y%m%d')
                        formatted_dates.append(date_obj.strftime('%Y-%m-%d'))
                    except:
                        formatted_dates.append(date_str)
                elif i == 5:
                    formatted_dates.append("...")
            return "   " + ", ".join(formatted_dates)

    def get_user_choice(self):
        """獲取用戶選擇"""
        if self.skip_radio.isChecked():
            return "skip"
        elif self.overwrite_radio.isChecked():
            return "overwrite"
        return "skip"  # 默認

class TWSEMarketDataDialog(QDialog):
    """台灣證交所市場數據爬蟲對話框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📊 台灣證交所市場數據爬蟲")
        self.setMinimumSize(800, 700)
        self.resize(900, 750)
        # 設定窗口標誌：包含最小化、最大化、關閉按鈕
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint |
            Qt.WindowType.WindowTitleHint |
            Qt.WindowType.WindowSystemMenuHint
        )

        # 設定窗口屬性
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)  # 關閉時不刪除，只隱藏

        self.worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """設置用戶界面"""
        # 設置窗口大小和樣式
        self.setGeometry(100, 100, 1600, 1000)
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
                border-radius: 8px;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                color: #ffffff;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #3c3c3c;
                border-bottom-color: #3c3c3c;
                color: #00d4ff;
            }
            QTabBar::tab:hover {
                background-color: #505050;
                color: #00d4ff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #3c3c3c;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                background-color: #3c3c3c;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
            }
            QCheckBox {
                color: #ffffff !important;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                spacing: 10px;
                min-height: 30px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #888888;
                border-radius: 4px;
                background-color: #2b2b2b;
                margin-right: 8px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
            QComboBox {
                color: #ffffff;
                background-color: #404040;
                border: 1px solid #666666;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                max-height: 28px;
                min-width: 120px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #505050;
                border-radius: 4px;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #ffffff;
            }
            QDateEdit {
                color: #ffffff;
                background-color: #404040;
                border: 1px solid #666666;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                max-height: 28px;
            }
            QDateEdit::drop-down {
                border: none;
                background-color: #505050;
                border-radius: 4px;
                width: 20px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # 標題
        title_label = QLabel("📊 台灣證交所市場數據爬蟲")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2E86AB; margin: 10px; padding: 10px;")
        layout.addWidget(title_label)

        # 創建分頁控件 - 移到最上方
        self.tab_widget = QTabWidget()

        # 設置分頁
        self.setup_crawler_tab()
        self.setup_data_view_tab()
        self.setup_chart_tab()

        layout.addWidget(self.tab_widget)

    def setup_crawler_tab(self):
        """設置爬蟲控制分頁"""
        crawler_tab = QWidget()
        layout = QVBoxLayout(crawler_tab)
        layout.setSpacing(8)
        layout.setContentsMargins(15, 10, 15, 10)



        # 數據類型選擇群組
        data_group = QGroupBox("選擇數據類型")
        data_layout = QVBoxLayout(data_group)
        data_layout.setSpacing(5)  # 複選框間距
        data_layout.setContentsMargins(10, 8, 10, 8)  # 調整群組內邊距

        # 市場指數資訊
        self.market_index_cb = QCheckBox("市場指數資訊 (即時更新)")
        self.market_index_cb.setChecked(True)

        # 設置字體
        checkbox_font = QFont()
        checkbox_font.setPointSize(11)
        checkbox_font.setBold(False)
        self.market_index_cb.setFont(checkbox_font)
        self.market_index_cb.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                padding: 6px 10px;
                color: #ffffff !important;
                font-weight: normal;
                background-color: transparent;
                spacing: 8px;
                min-height: 24px;
                margin-bottom: 3px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #888888;
                border-radius: 3px;
                background-color: #2b2b2b;
                margin-right: 10px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        data_layout.addWidget(self.market_index_cb)

        # 融資融券統計
        self.margin_trading_cb = QCheckBox("融資融券統計 (每日更新)")
        self.margin_trading_cb.setChecked(True)
        self.margin_trading_cb.setFont(checkbox_font)
        self.margin_trading_cb.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                padding: 6px 10px;
                color: #ffffff !important;
                font-weight: normal;
                background-color: transparent;
                spacing: 8px;
                min-height: 24px;
                margin-bottom: 3px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #888888;
                border-radius: 3px;
                background-color: #2b2b2b;
                margin-right: 10px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        data_layout.addWidget(self.margin_trading_cb)

        # 發行量加權股價指數歷史資料
        self.historical_index_cb = QCheckBox("發行量加權股價指數歷史資料 (歷史數據)")
        self.historical_index_cb.setChecked(True)
        self.historical_index_cb.setFont(checkbox_font)
        self.historical_index_cb.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                padding: 6px 10px;
                color: #ffffff !important;
                font-weight: normal;
                background-color: transparent;
                spacing: 8px;
                min-height: 24px;
                margin-bottom: 3px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #888888;
                border-radius: 3px;
                background-color: #2b2b2b;
                margin-right: 10px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #00d4ff;
                background-color: #00d4ff;
            }
        """)
        data_layout.addWidget(self.historical_index_cb)



        layout.addWidget(data_group)

        # 日期範圍設定
        date_layout = QHBoxLayout()
        date_layout.setSpacing(10)

        # 開始日期
        start_label = QLabel("開始日期:")
        start_label.setStyleSheet("QLabel { color: #ffffff; font-size: 12px; font-weight: normal; }")
        date_layout.addWidget(start_label)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))  # 預設30天前
        self.start_date.setCalendarPopup(True)
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        self.start_date.setMaximumHeight(28)
        date_layout.addWidget(self.start_date)

        # 結束日期
        end_label = QLabel("結束日期:")
        end_label.setStyleSheet("QLabel { color: #ffffff; font-size: 12px; font-weight: normal; }")
        date_layout.addWidget(end_label)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())  # 預設今天
        self.end_date.setCalendarPopup(True)
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        self.end_date.setMaximumHeight(28)
        date_layout.addWidget(self.end_date)

        date_layout.addStretch()  # 添加彈性空間
        layout.addLayout(date_layout)

        # 控制按鈕區域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(6)  # 設置按鈕間距

        # 全選按鈕
        self.select_all_btn = QPushButton("全選")
        self.select_all_btn.clicked.connect(self.select_all_data_types)
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: normal;
                font-size: 11px;
                min-width: 50px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.select_all_btn)

        # 取消全選按鈕
        self.deselect_all_btn = QPushButton("取消全選")
        self.deselect_all_btn.clicked.connect(self.deselect_all_data_types)
        self.deselect_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: normal;
                font-size: 11px;
                min-width: 60px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        button_layout.addWidget(self.deselect_all_btn)

        # 檢查現有數據按鈕
        self.check_data_btn = QPushButton("檢查現有數據")
        self.check_data_btn.clicked.connect(self.check_existing_data)
        self.check_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: normal;
                font-size: 11px;
                min-width: 80px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        button_layout.addWidget(self.check_data_btn)

        button_layout.addStretch()

        # 開始爬取按鈕
        self.start_btn = QPushButton("開始爬取")
        self.start_btn.clicked.connect(self.start_crawling)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #00d4ff;
                color: #000000;
                border: none;
                padding: 4px 12px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #00b8e6;
            }
            QPushButton:disabled {
                background-color: #555555;
            }
        """)
        button_layout.addWidget(self.start_btn)

        layout.addLayout(button_layout)

        # 進度條和狀態
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 狀態標籤
        self.status_label = QLabel("準備就緒")
        self.status_label.setStyleSheet("color: #cccccc; margin: 5px; font-size: 14px; font-weight: bold;")
        layout.addWidget(self.status_label)

        # 爬取日誌區域
        log_group = QGroupBox("📋 爬取日誌")
        log_layout = QVBoxLayout(log_group)

        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        log_layout.addWidget(self.result_text)

        layout.addWidget(log_group)

        # 添加彈性空間
        layout.addStretch()

        self.tab_widget.addTab(crawler_tab, "🚀 數據爬取")

    def setup_data_view_tab(self):
        """設置數據查看分頁"""
        data_tab = QWidget()
        layout = QVBoxLayout(data_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 數據查看控制區域
        control_layout = QHBoxLayout()

        # 數據類型選擇
        data_type_label = QLabel("數據類型:")
        data_type_label.setStyleSheet("QLabel { color: #ffffff; font-size: 14px; font-weight: bold; }")
        control_layout.addWidget(data_type_label)
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems([
            "市場指數資訊",
            "融資融券統計",
            "歷史指數資料"
        ])
        control_layout.addWidget(self.data_type_combo)

        # 查看數據按鈕
        view_data_btn = QPushButton("👁️ 查看數據")
        view_data_btn.clicked.connect(self.view_data)
        view_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        control_layout.addWidget(view_data_btn)

        # 導出CSV按鈕
        export_btn = QPushButton("💾 導出CSV")
        export_btn.clicked.connect(self.export_to_csv)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        control_layout.addWidget(export_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 數據表格
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #555555;
                background-color: #3c3c3c;
                alternate-background-color: #404040;
                selection-background-color: #00d4ff;
                selection-color: #000000;
                font-size: 12px;
                color: #ffffff;
            }
            QTableWidget::item {
                padding: 8px;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            QTableWidget::item:selected {
                background-color: #00d4ff;
                color: #000000;
            }
            QHeaderView::section {
                background-color: #505050;
                padding: 8px;
                border: 1px solid #666666;
                font-weight: bold;
                color: #ffffff;
            }
        """)
        layout.addWidget(self.data_table)

        self.tab_widget.addTab(data_tab, "📊 數據查看")

    def setup_chart_tab(self):
        """設置圖表分析分頁 - 全新matplotlib版本"""
        chart_tab = QWidget()
        layout = QVBoxLayout(chart_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 圖表控制區域
        control_layout = QHBoxLayout()

        # 圖表類型選擇
        chart_type_label = QLabel("圖表類型:")
        chart_type_label.setStyleSheet("QLabel { color: #ffffff; font-size: 14px; font-weight: bold; }")
        control_layout.addWidget(chart_type_label)
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["📈 線圖", "📊 柱狀圖", "📉 面積圖"])
        control_layout.addWidget(self.chart_type_combo)

        # 數據類型選擇
        chart_data_label = QLabel("　數據類型:")
        chart_data_label.setStyleSheet("QLabel { color: #ffffff; font-size: 14px; font-weight: bold; }")
        control_layout.addWidget(chart_data_label)
        self.chart_data_combo = QComboBox()
        self.chart_data_combo.addItems(["市場指數資訊", "融資融券統計", "歷史指數資料"])
        control_layout.addWidget(self.chart_data_combo)

        # 生成圖表按鈕
        self.generate_chart_btn = QPushButton("📈 生成圖表")
        self.generate_chart_btn.clicked.connect(self.generate_new_chart)
        self.generate_chart_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b35;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e55a2b;
            }
        """)
        control_layout.addWidget(self.generate_chart_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 圖表顯示區域 - 使用matplotlib
        try:
            import matplotlib
            matplotlib.use('Qt5Agg')
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.figure import Figure

            # 創建matplotlib圖表
            self.chart_figure = Figure(figsize=(12, 8), facecolor='#2b2b2b')
            self.chart_canvas = FigureCanvas(self.chart_figure)
            self.chart_canvas.setStyleSheet("background-color: #2b2b2b;")

            # 添加到布局
            layout.addWidget(self.chart_canvas)

            # 初始化空圖表
            ax = self.chart_figure.add_subplot(111)
            ax.set_facecolor('#2b2b2b')
            ax.text(0.5, 0.5, '請選擇數據類型並點擊「生成圖表」',
                   ha='center', va='center', transform=ax.transAxes,
                   color='white', fontsize=16)
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_color('white')
            self.chart_figure.tight_layout()
            self.chart_canvas.draw()

            print("✅ 新的matplotlib圖表分頁創建成功！")

        except ImportError as e:
            # 如果沒有 matplotlib，顯示提示
            chart_placeholder = QLabel("📊 圖表功能需要安裝 matplotlib 套件\n\n請執行: pip install matplotlib")
            chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            chart_placeholder.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 2px dashed #dee2e6;
                    border-radius: 8px;
                    padding: 40px;
                    color: #6c757d;
                    font-size: 16px;
                    min-height: 400px;
                }
            """)
            layout.addWidget(chart_placeholder)
            self.chart_canvas = None
            print(f"❌ matplotlib導入失敗: {e}")

        self.tab_widget.addTab(chart_tab, "📈 圖表分析")

    def generate_new_chart(self):
        """生成新的matplotlib圖表"""
        if not hasattr(self, 'chart_canvas') or self.chart_canvas is None:
            QMessageBox.warning(self, "圖表功能不可用", "圖表功能需要安裝 matplotlib 套件\n\n請執行: pip install matplotlib")
            return

        try:
            import sqlite3
            import pandas as pd
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from datetime import datetime

            # 獲取選擇的數據類型
            data_type_name = self.chart_data_combo.currentText()
            chart_type = self.chart_type_combo.currentText()

            print(f"📊 開始生成 {data_type_name} 的 {chart_type}")

            # 映射數據類型到資料庫
            data_type_mapping = {
                "市場指數資訊": ("market_index.db", "market_index_info"),
                "融資融券統計": ("margin_trading.db", "margin_trading_info"),
                "歷史指數資料": ("market_historical.db", "market_historical_index")
            }

            if data_type_name not in data_type_mapping:
                QMessageBox.warning(self, "錯誤", "請選擇有效的數據類型")
                return

            db_file, table_name = data_type_mapping[data_type_name]
            db_path = f"D:/Finlab/history/tables/{db_file}"

            # 讀取數據
            conn = sqlite3.connect(db_path)
            df = pd.read_sql_query(f"SELECT * FROM {table_name} ORDER BY crawl_time", conn)
            conn.close()

            if df.empty:
                QMessageBox.information(self, "無數據", f"沒有 {data_type_name} 的數據可以繪製圖表")
                return

            # 清除舊圖表
            self.chart_figure.clear()

            # 根據數據類型生成不同的圖表
            if data_type_name == "歷史指數資料":
                self.generate_matplotlib_historical_chart(df, chart_type)
            elif data_type_name == "市場指數資訊":
                self.generate_matplotlib_index_chart(df, chart_type)
            elif data_type_name == "融資融券統計":
                self.generate_matplotlib_margin_chart(df, chart_type)

            # 更新圖表顯示
            self.chart_canvas.draw()

            # 切換到圖表分頁
            self.tab_widget.setCurrentIndex(2)

            print("✅ 新的matplotlib圖表生成完成！")

        except Exception as e:
            print(f"❌ 圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "圖表錯誤", f"圖表生成失敗：\n{str(e)}")

    def generate_matplotlib_historical_chart(self, df, chart_type):
        """生成歷史指數matplotlib圖表"""
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates

            print(f"📊 生成歷史指數圖表...")

            # 先檢查數據結構
            print(f"📊 數據形狀: {df.shape}")
            print(f"📊 欄位: {list(df.columns)}")
            print(f"📊 前5筆數據:")
            print(df.head())

            # 檢查數據類型
            print(f"⚠️ 警告：當前數據不是真正的指數資料！")
            print(f"📊 這是個股資料，包含 {len(df)} 支股票/ETF")
            print(f"📊 如果要顯示指數走勢，需要：")
            print(f"   1. 篩選特定指數 (如加權指數)")
            print(f"   2. 或計算市場平均")
            print(f"   3. 或顯示代表性ETF (如0050)")

            # 嘗試篩選代表性指數
            if 'Code' in df.columns:
                # 篩選台灣50 ETF (0050) 作為代表
                index_data = df[df['Code'] == '0050'].copy()
                if len(index_data) == 0:
                    # 如果沒有0050，取第一支ETF
                    index_data = df.head(100).copy()
                    print(f"📊 使用前100筆數據作為示例")
                else:
                    print(f"📊 使用台灣50 ETF (0050) 作為指數代表")
            else:
                index_data = df.copy()

            # 處理數據
            if 'Date' in index_data.columns:
                df_clean = index_data.copy()
                df_clean['Date'] = pd.to_datetime(df_clean['Date'], format='%Y%m%d', errors='coerce')
                df_clean = df_clean.dropna(subset=['Date'])

                # 找收盤價欄位
                price_col = None
                for col in ['ClosingPrice', 'ClosingIndex', '收盤指數']:
                    if col in df_clean.columns:
                        price_col = col
                        break

                if price_col is None:
                    print("❌ 找不到價格欄位")
                    print(f"📊 可用欄位: {list(df_clean.columns)}")
                    return

                print(f"📊 使用價格欄位: {price_col}")

                # 轉換價格數據並檢查
                df_clean[price_col] = pd.to_numeric(df_clean[price_col], errors='coerce')
                df_clean = df_clean.dropna(subset=[price_col])
                df_clean = df_clean.sort_values('Date')

                print(f"📊 準備繪製 {len(df_clean)} 個數據點")
                if len(df_clean) > 0:
                    print(f"📊 價格範圍: {df_clean[price_col].min():.2f} - {df_clean[price_col].max():.2f}")
                    print(f"📊 日期範圍: {df_clean['Date'].min()} 至 {df_clean['Date'].max()}")

                    # 顯示前幾筆數據
                    print(f"📊 前5筆數據:")
                    print(df_clean[['Date', price_col]].head())

                # 創建子圖
                ax = self.chart_figure.add_subplot(111)
                ax.set_facecolor('#2b2b2b')

                # 繪製圖表
                if "線圖" in chart_type:
                    ax.plot(df_clean['Date'], df_clean[price_col], color='#00ff88', linewidth=2, label='收盤指數')
                elif "柱狀圖" in chart_type:
                    ax.bar(df_clean['Date'], df_clean[price_col], color='#00ff88', alpha=0.7, label='收盤指數')
                elif "面積圖" in chart_type:
                    ax.fill_between(df_clean['Date'], df_clean[price_col], color='#00ff88', alpha=0.3, label='收盤指數')
                    ax.plot(df_clean['Date'], df_clean[price_col], color='#00ff88', linewidth=2)

                # 設置標題和標籤
                ax.set_title(f'歷史指數走勢 ({chart_type})', color='white', fontsize=16, pad=20)
                ax.set_xlabel('日期', color='white', fontsize=12)
                ax.set_ylabel('指數', color='white', fontsize=12)

                # 設置日期格式 - 這是關鍵！
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(df_clean)//8)))

                # 設置顏色和樣式
                ax.tick_params(colors='white', labelsize=10)
                for spine in ax.spines.values():
                    spine.set_color('white')

                # 旋轉日期標籤
                for label in ax.xaxis.get_majorticklabels():
                    label.set_rotation(45)

                # 添加網格
                ax.grid(True, alpha=0.3, color='white')

                # 添加圖例
                ax.legend(loc='upper left', facecolor='#2b2b2b', edgecolor='white', labelcolor='white')

                self.chart_figure.tight_layout()

                print("✅ 歷史指數matplotlib圖表生成完成！日期軸應該正常顯示了！")

            else:
                print("❌ 找不到日期欄位")

        except Exception as e:
            print(f"❌ 歷史圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()

    def generate_matplotlib_index_chart(self, df, chart_type):
        """生成市場指數matplotlib圖表"""
        try:
            print(f"📊 生成市場指數圖表...")

            # 處理數據
            if '指數' in df.columns:
                df_clean = df.copy()
                df_clean['指數'] = pd.to_numeric(df_clean['指數'], errors='coerce')
                df_clean = df_clean.dropna(subset=['指數'])

                # 創建子圖
                ax = self.chart_figure.add_subplot(111)
                ax.set_facecolor('#2b2b2b')

                # 使用序號作為X軸
                x_data = list(range(len(df_clean)))
                y_data = df_clean['指數'].tolist()

                # 繪製圖表
                if "線圖" in chart_type:
                    ax.plot(x_data, y_data, color='#ff6b35', linewidth=2, marker='o', markersize=4, label='市場指數')
                elif "柱狀圖" in chart_type:
                    ax.bar(x_data, y_data, color='#ff6b35', alpha=0.7, label='市場指數')
                elif "面積圖" in chart_type:
                    ax.fill_between(x_data, y_data, color='#ff6b35', alpha=0.3, label='市場指數')
                    ax.plot(x_data, y_data, color='#ff6b35', linewidth=2)

                # 設置標題和標籤
                ax.set_title(f'市場指數資訊 ({chart_type})', color='white', fontsize=16, pad=20)
                ax.set_xlabel('數據序號', color='white', fontsize=12)
                ax.set_ylabel('指數', color='white', fontsize=12)

                # 設置顏色和樣式
                ax.tick_params(colors='white', labelsize=10)
                for spine in ax.spines.values():
                    spine.set_color('white')

                # 添加網格
                ax.grid(True, alpha=0.3, color='white')

                # 添加圖例
                ax.legend(loc='upper left', facecolor='#2b2b2b', edgecolor='white', labelcolor='white')

                self.chart_figure.tight_layout()

                print("✅ 市場指數matplotlib圖表生成完成！")

        except Exception as e:
            print(f"❌ 市場指數圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()

    def generate_matplotlib_margin_chart(self, df, chart_type):
        """生成融資融券matplotlib圖表"""
        try:
            print(f"📊 生成融資融券圖表...")

            # 處理數據
            if '融資今日餘額' in df.columns:
                df_clean = df.copy()
                df_clean['融資今日餘額'] = pd.to_numeric(df_clean['融資今日餘額'], errors='coerce')
                df_clean = df_clean.dropna(subset=['融資今日餘額'])

                # 取前20筆數據避免過於擁擠
                df_clean = df_clean.head(20)

                # 創建子圖
                ax = self.chart_figure.add_subplot(111)
                ax.set_facecolor('#2b2b2b')

                # 使用序號作為X軸
                x_data = list(range(len(df_clean)))
                y_data = df_clean['融資今日餘額'].tolist()

                # 繪製圖表
                if "線圖" in chart_type:
                    ax.plot(x_data, y_data, color='#17a2b8', linewidth=2, marker='s', markersize=4, label='融資餘額')
                elif "柱狀圖" in chart_type:
                    ax.bar(x_data, y_data, color='#17a2b8', alpha=0.7, label='融資餘額')
                elif "面積圖" in chart_type:
                    ax.fill_between(x_data, y_data, color='#17a2b8', alpha=0.3, label='融資餘額')
                    ax.plot(x_data, y_data, color='#17a2b8', linewidth=2)

                # 設置標題和標籤
                ax.set_title(f'融資融券統計 ({chart_type})', color='white', fontsize=16, pad=20)
                ax.set_xlabel('股票序號', color='white', fontsize=12)
                ax.set_ylabel('融資餘額', color='white', fontsize=12)

                # 設置顏色和樣式
                ax.tick_params(colors='white', labelsize=10)
                for spine in ax.spines.values():
                    spine.set_color('white')

                # 添加網格
                ax.grid(True, alpha=0.3, color='white')

                # 添加圖例
                ax.legend(loc='upper left', facecolor='#2b2b2b', edgecolor='white', labelcolor='white')

                self.chart_figure.tight_layout()

                print("✅ 融資融券matplotlib圖表生成完成！")

        except Exception as e:
            print(f"❌ 融資融券圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()

    def select_all_data_types(self):
        """全選所有數據類型"""
        self.market_index_cb.setChecked(True)
        self.margin_trading_cb.setChecked(True)
        self.historical_index_cb.setChecked(True)

    def deselect_all_data_types(self):
        """取消全選所有數據類型"""
        self.market_index_cb.setChecked(False)
        self.margin_trading_cb.setChecked(False)
        self.historical_index_cb.setChecked(False)

    def check_existing_data(self):
        """檢查並顯示現有數據範圍"""
        try:
            import sqlite3
            from datetime import datetime

            # 創建檢查結果對話框
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 現有數據範圍檢查")
            dialog.setModal(True)
            dialog.setFixedSize(700, 500)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                }
                QTextEdit {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    padding: 10px;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 12px;
                }
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
            """)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # 標題
            title_label = QLabel("📊 數據庫現有數據範圍")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00d4ff; margin-bottom: 10px;")
            layout.addWidget(title_label)

            # 結果顯示
            result_text = QTextEdit()
            result_text.setReadOnly(True)

            # 檢查各種數據類型
            data_types = {
                'market_index': ('市場指數資訊', 'market_index.db', 'market_index_info'),
                'margin_trading': ('融資融券統計', 'margin_trading.db', 'margin_trading_info'),
                'historical_index': ('歷史指數資料', 'market_historical.db', 'market_historical_index')
            }

            result_content = "📋 數據庫狀態檢查結果:\n\n"

            for data_type, (display_name, db_file, table_name) in data_types.items():
                result_content += f"{'='*50}\n"
                result_content += f"📊 {display_name}\n"
                result_content += f"{'='*50}\n"

                db_path = f"D:/Finlab/history/tables/{db_file}"

                try:
                    conn = sqlite3.connect(db_path)

                    if data_type == 'historical_index':
                        # 歷史數據使用 Date 欄位
                        query = f"""
                        SELECT
                            MIN(Date) as min_date,
                            MAX(Date) as max_date,
                            COUNT(DISTINCT Date) as date_count,
                            COUNT(*) as total_records
                        FROM {table_name}
                        """
                        cursor = conn.execute(query)
                        result = cursor.fetchone()

                        if result and result[0]:
                            min_date, max_date, date_count, total_records = result

                            # 轉換日期格式顯示
                            try:
                                min_date_formatted = datetime.strptime(min_date, '%Y%m%d').strftime('%Y-%m-%d')
                                max_date_formatted = datetime.strptime(max_date, '%Y%m%d').strftime('%Y-%m-%d')
                            except:
                                min_date_formatted = min_date
                                max_date_formatted = max_date

                            result_content += f"✅ 數據庫存在\n"
                            result_content += f"📅 日期範圍: {min_date_formatted} 至 {max_date_formatted}\n"
                            result_content += f"📊 總天數: {date_count} 天\n"
                            result_content += f"📈 總記錄數: {total_records:,} 筆\n"

                            # 檢查最近的數據
                            recent_query = f"SELECT Date FROM {table_name} ORDER BY Date DESC LIMIT 5"
                            recent_dates = [row[0] for row in conn.execute(recent_query).fetchall()]
                            if recent_dates:
                                formatted_recent = []
                                for date_str in recent_dates:
                                    try:
                                        formatted_recent.append(datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d'))
                                    except:
                                        formatted_recent.append(date_str)
                                result_content += f"🕒 最近5天: {', '.join(formatted_recent)}\n"
                        else:
                            result_content += f"⚠️ 數據庫存在但無數據\n"
                    else:
                        # 其他數據使用 crawl_time 欄位
                        query = f"""
                        SELECT
                            MIN(DATE(crawl_time)) as min_date,
                            MAX(DATE(crawl_time)) as max_date,
                            COUNT(DISTINCT DATE(crawl_time)) as date_count,
                            COUNT(*) as total_records
                        FROM {table_name}
                        """
                        cursor = conn.execute(query)
                        result = cursor.fetchone()

                        if result and result[0]:
                            min_date, max_date, date_count, total_records = result
                            result_content += f"✅ 數據庫存在\n"
                            result_content += f"📅 日期範圍: {min_date} 至 {max_date}\n"
                            result_content += f"📊 總天數: {date_count} 天\n"
                            result_content += f"📈 總記錄數: {total_records:,} 筆\n"

                            # 檢查最近的數據
                            recent_query = f"SELECT DATE(crawl_time) FROM {table_name} GROUP BY DATE(crawl_time) ORDER BY DATE(crawl_time) DESC LIMIT 5"
                            recent_dates = [row[0] for row in conn.execute(recent_query).fetchall()]
                            if recent_dates:
                                result_content += f"🕒 最近5天: {', '.join(recent_dates)}\n"
                        else:
                            result_content += f"⚠️ 數據庫存在但無數據\n"

                    conn.close()

                except Exception as e:
                    result_content += f"❌ 無法訪問數據庫: {str(e)}\n"

                result_content += f"\n"

            # 添加建議
            result_content += f"{'='*50}\n"
            result_content += f"💡 使用建議\n"
            result_content += f"{'='*50}\n"
            result_content += f"• 如果要爬取的日期範圍與現有數據重疊，系統會自動詢問處理方式\n"
            result_content += f"• 建議選擇「跳過重疊日期」以節省時間\n"
            result_content += f"• 如果需要更新數據，可選擇「覆蓋重疊日期」\n"
            result_content += f"• 歷史數據建議按需下載，避免重複下載大量數據\n"

            result_text.setPlainText(result_content)
            layout.addWidget(result_text)

            # 關閉按鈕
            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "檢查失敗", f"檢查現有數據時發生錯誤：\n{str(e)}")

    def get_selected_types(self):
        """獲取選中的數據類型"""
        selected = []
        
        if self.market_index_cb.isChecked():
            selected.append("market_index")
        
        if self.margin_trading_cb.isChecked():
            selected.append("margin_trading")
        
        if self.historical_index_cb.isChecked():
            selected.append("historical_index")
        
        return selected

    def check_existing_data_range(self, data_type, start_date, end_date):
        """檢查現有數據庫中的日期範圍"""
        try:
            import sqlite3
            from datetime import datetime, timedelta

            db_files = {
                'market_index': 'market_index.db',
                'margin_trading': 'margin_trading.db',
                'historical_index': 'market_historical.db'
            }

            table_names = {
                'market_index': 'market_index_info',
                'margin_trading': 'margin_trading_info',
                'historical_index': 'market_historical_index'
            }

            if data_type not in db_files:
                return None, None, []

            db_path = f"D:/Finlab/history/tables/{db_files[data_type]}"
            table_name = table_names[data_type]

            try:
                conn = sqlite3.connect(db_path)

                if data_type == 'historical_index':
                    # 歷史數據使用 Date 欄位
                    query = "SELECT MIN(Date) as min_date, MAX(Date) as max_date, COUNT(DISTINCT Date) as date_count FROM " + table_name
                    cursor = conn.execute(query)
                    result = cursor.fetchone()

                    if result and result[0]:
                        min_date_str = result[0]
                        max_date_str = result[1]
                        date_count = result[2]

                        # 獲取所有現有日期
                        date_query = "SELECT DISTINCT Date FROM " + table_name + " ORDER BY Date"
                        existing_dates = [row[0] for row in conn.execute(date_query).fetchall()]

                        conn.close()
                        return min_date_str, max_date_str, existing_dates
                else:
                    # 其他數據使用 crawl_time 欄位
                    query = "SELECT MIN(DATE(crawl_time)) as min_date, MAX(DATE(crawl_time)) as max_date, COUNT(DISTINCT DATE(crawl_time)) as date_count FROM " + table_name
                    cursor = conn.execute(query)
                    result = cursor.fetchone()

                    if result and result[0]:
                        min_date_str = result[0]
                        max_date_str = result[1]
                        date_count = result[2]

                        # 獲取所有現有日期
                        date_query = "SELECT DISTINCT DATE(crawl_time) FROM " + table_name + " ORDER BY DATE(crawl_time)"
                        existing_dates = [row[0] for row in conn.execute(date_query).fetchall()]

                        conn.close()
                        return min_date_str, max_date_str, existing_dates

                conn.close()
                return None, None, []

            except Exception as e:
                print(f"檢查 {data_type} 數據範圍失敗: {e}")
                return None, None, []

        except Exception as e:
            print(f"檢查數據範圍失敗: {e}")
            return None, None, []

    def find_date_overlaps(self, requested_start, requested_end, existing_dates):
        """找出重疊的日期"""
        try:
            from datetime import datetime, timedelta

            # 轉換請求的日期範圍
            start_date = datetime.strptime(requested_start, '%Y-%m-%d')
            end_date = datetime.strptime(requested_end, '%Y-%m-%d')

            # 生成請求的日期列表
            requested_dates = []
            current_date = start_date
            while current_date <= end_date:
                requested_dates.append(current_date.strftime('%Y%m%d'))
                current_date += timedelta(days=1)

            # 找出重疊的日期
            overlapping_dates = []
            for date_str in requested_dates:
                if date_str in existing_dates:
                    overlapping_dates.append(date_str)

            return overlapping_dates, requested_dates

        except Exception as e:
            print(f"查找日期重疊失敗: {e}")
            return [], []

    def start_crawling(self):
        """開始爬取數據"""
        selected_types = self.get_selected_types()

        if not selected_types:
            QMessageBox.warning(self, "警告", "請至少選擇一種數據類型！")
            return

        # 獲取日期範圍
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")

        # 檢查歷史數據的日期重疊
        if "historical_index" in selected_types:
            min_date, max_date, existing_dates = self.check_existing_data_range("historical_index", start_date, end_date)

            if existing_dates:
                overlapping_dates, requested_dates = self.find_date_overlaps(start_date, end_date, existing_dates)

                if overlapping_dates:
                    # 顯示重疊檢查對話框
                    overlap_dialog = DateOverlapDialog(
                        self,
                        start_date,
                        end_date,
                        min_date,
                        max_date,
                        overlapping_dates,
                        len(existing_dates)
                    )

                    result = overlap_dialog.exec()

                    if result == QDialog.DialogCode.Rejected:
                        # 用戶取消
                        return

                    # 獲取用戶選擇
                    action = overlap_dialog.get_user_choice()

                    if action == "skip":
                        # 跳過重疊日期，只爬取新日期
                        new_dates = [date for date in requested_dates if date not in existing_dates]
                        if not new_dates:
                            QMessageBox.information(self, "無需爬取", "所有請求的日期都已存在於數據庫中。")
                            return

                        # 更新日期範圍為只包含新日期
                        from datetime import datetime
                        new_dates_dt = [datetime.strptime(d, '%Y%m%d') for d in new_dates]
                        start_date = min(new_dates_dt).strftime('%Y-%m-%d')
                        end_date = max(new_dates_dt).strftime('%Y-%m-%d')

                        self.result_text.append(f"📅 跳過重疊日期，將爬取 {len(new_dates)} 個新日期")
                        self.result_text.append(f"📅 調整後日期範圍: {start_date} 至 {end_date}")

                    elif action == "overwrite":
                        # 覆蓋重疊日期
                        self.result_text.append(f"📅 將覆蓋 {len(overlapping_dates)} 個重疊日期")

                    # 如果選擇跳過且沒有新日期，則不爬取歷史數據
                    if action == "skip" and not new_dates:
                        selected_types = [t for t in selected_types if t != "historical_index"]
                        if not selected_types:
                            QMessageBox.information(self, "無需爬取", "沒有需要爬取的數據。")
                            return

        # 禁用按鈕
        self.start_btn.setEnabled(False)
        self.select_all_btn.setEnabled(False)
        self.deselect_all_btn.setEnabled(False)

        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空結果
        self.result_text.clear()
        self.data_table.setRowCount(0)
        self.data_table.setColumnCount(0)

        # 創建並啟動工作線程
        self.worker = MarketDataWorker(selected_types, start_date, end_date)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.finished.connect(self.crawling_finished)
        self.worker.error_occurred.connect(self.crawling_error)
        self.worker.start()
    
    def update_progress(self, value, message):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        QApplication.processEvents()
    
    def crawling_finished(self, results):
        """爬取完成"""
        # 恢復按鈕
        self.start_btn.setEnabled(True)
        self.select_all_btn.setEnabled(True)
        self.deselect_all_btn.setEnabled(True)
        
        # 隱藏進度條
        self.progress_bar.setVisible(False)
        
        # 顯示結果
        result_text = "📊 爬取結果：\n\n"
        
        success_count = 0
        total_count = len(results)
        
        for data_type, success in results.items():
            status = "✅ 成功" if success else "❌ 失敗"
            
            if data_type == "market_index":
                result_text += f"📈 市場指數資訊: {status}\n"
            elif data_type == "margin_trading":
                result_text += f"💰 融資融券統計: {status}\n"
            elif data_type == "historical_index":
                result_text += f"📊 歷史指數資料: {status}\n"
            
            if success:
                success_count += 1
        
        result_text += f"\n📊 總計: {success_count}/{total_count} 成功"
        result_text += f"\n💾 數據保存位置: D:/Finlab/history/tables/"
        
        self.result_text.setText(result_text)
        self.status_label.setText(f"完成！成功 {success_count}/{total_count}")
        
        # 顯示完成對話框
        if success_count == total_count:
            QMessageBox.information(
                self, 
                "爬取完成", 
                f"🎉 所有數據爬取成功！\n\n"
                f"成功: {success_count}/{total_count}\n"
                f"數據已保存到: D:/Finlab/history/tables/"
            )
        else:
            QMessageBox.warning(
                self, 
                "部分失敗", 
                f"⚠️ 部分數據爬取失敗\n\n"
                f"成功: {success_count}/{total_count}\n"
                f"請檢查網路連線或稍後再試"
            )
    
    def crawling_error(self, error_message):
        """爬取錯誤"""
        # 恢復按鈕
        self.start_btn.setEnabled(True)
        self.select_all_btn.setEnabled(True)
        self.deselect_all_btn.setEnabled(True)
        
        # 隱藏進度條
        self.progress_bar.setVisible(False)
        
        # 顯示錯誤
        self.status_label.setText("爬取失敗")
        self.result_text.setText(f"❌ 爬取失敗：\n{error_message}")
        
        QMessageBox.critical(
            self, 
            "爬取失敗", 
            f"❌ 爬取過程中發生錯誤：\n\n{error_message}"
        )

    def view_data(self):
        """查看已爬取的數據"""
        try:
            from market_data_crawler import MarketDataCrawler
            import sqlite3
            import pandas as pd
            import os

            crawler = MarketDataCrawler()

            # 檢查哪些數據庫檔案存在
            db_files = {
                'market_index': 'market_index.db',
                'margin_trading': 'margin_trading.db',
                'historical_index': 'market_historical.db'
            }

            available_data = {}

            for data_type, db_file in db_files.items():
                db_path = f"D:/Finlab/history/tables/{db_file}"
                try:
                    conn = sqlite3.connect(db_path)

                    # 使用正確的表格名稱
                    if data_type == 'market_index':
                        df = pd.read_sql_query("SELECT * FROM market_index_info ORDER BY crawl_time DESC LIMIT 100", conn)
                    elif data_type == 'margin_trading':
                        df = pd.read_sql_query("SELECT * FROM margin_trading_info ORDER BY crawl_time DESC LIMIT 100", conn)
                    elif data_type == 'historical_index':
                        df = pd.read_sql_query("SELECT * FROM market_historical_index ORDER BY crawl_time DESC LIMIT 100", conn)

                    if not df.empty:
                        available_data[data_type] = df
                        print(f"✅ 成功讀取 {data_type}: {len(df)} 筆資料")
                    else:
                        print(f"⚠️ {data_type} 表格為空")

                    conn.close()

                except Exception as e:
                    print(f"❌ 讀取 {data_type} 失敗: {e}")
                    continue

            if not available_data:
                # 提供更詳細的診斷信息
                diagnostic_info = "目前沒有可查看的數據。\n\n診斷信息：\n"

                for data_type, db_file in db_files.items():
                    db_path = f"D:/Finlab/history/tables/{db_file}"
                    if os.path.exists(db_path):
                        diagnostic_info += f"✅ {db_file} 檔案存在\n"
                        try:
                            conn = sqlite3.connect(db_path)
                            cursor = conn.cursor()
                            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                            tables = [t[0] for t in cursor.fetchall()]
                            diagnostic_info += f"   表格: {', '.join(tables)}\n"
                            conn.close()
                        except Exception as e:
                            diagnostic_info += f"   錯誤: {e}\n"
                    else:
                        diagnostic_info += f"❌ {db_file} 檔案不存在\n"

                QMessageBox.information(self, "無數據", diagnostic_info)
                return

            # 顯示第一個可用的數據集
            first_data_type = list(available_data.keys())[0]
            df = available_data[first_data_type]

            self.display_dataframe(df, first_data_type)

            # 切換到數據查看分頁
            self.tab_widget.setCurrentIndex(1)

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"查看數據失敗：\n{str(e)}")

    def generate_chart(self):
        """生成圖表"""
        if not self.chart_widget:
            QMessageBox.warning(self, "圖表功能不可用", "圖表功能需要安裝 pyqtgraph 套件\n\n請執行: pip install pyqtgraph")
            return

        try:
            import sqlite3
            import pandas as pd
            import pyqtgraph as pg
            from datetime import datetime

            # 獲取選擇的數據類型
            data_type_name = self.chart_data_combo.currentText()
            chart_type = self.chart_type_combo.currentText()

            # 映射數據類型到資料庫
            data_type_mapping = {
                "市場指數資訊": ("market_index.db", "market_index_info"),
                "融資融券統計": ("margin_trading.db", "margin_trading_info"),
                "歷史指數資料": ("market_historical.db", "market_historical_index")
            }

            if data_type_name not in data_type_mapping:
                QMessageBox.warning(self, "錯誤", "請選擇有效的數據類型")
                return

            db_file, table_name = data_type_mapping[data_type_name]
            db_path = f"D:/Finlab/history/tables/{db_file}"

            # 讀取數據
            conn = sqlite3.connect(db_path)
            df = pd.read_sql_query(f"SELECT * FROM {table_name} ORDER BY crawl_time", conn)
            conn.close()

            if df.empty:
                QMessageBox.information(self, "無數據", f"沒有 {data_type_name} 的數據可以繪製圖表")
                return

            # 清除舊圖表
            self.chart_widget.clear()

            # 重置圖表標題和標籤
            self.chart_widget.setTitle("")
            self.chart_widget.setLabel('left', "")
            self.chart_widget.setLabel('bottom', "")

            # 根據數據類型生成不同的圖表
            if data_type_name == "歷史指數資料":
                self.generate_historical_chart(df, chart_type)
            elif data_type_name == "市場指數資訊":
                self.generate_index_chart(df, chart_type)
            elif data_type_name == "融資融券統計":
                self.generate_margin_chart(df, chart_type)

            # 切換到圖表分頁
            self.tab_widget.setCurrentIndex(2)

        except Exception as e:
            QMessageBox.critical(self, "圖表生成失敗", f"生成圖表時發生錯誤：\n{str(e)}")

    def generate_historical_chart(self, df, chart_type):
        """生成歷史指數圖表 - 使用matplotlib"""
        try:
            import matplotlib
            matplotlib.use('Qt5Agg')  # 設置backend
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.figure import Figure
            import pandas as pd
            from PyQt6.QtWidgets import QVBoxLayout

            print(f"📊 使用matplotlib生成歷史圖表...")

            # 處理數據
            if 'Date' in df.columns:
                df_clean = df.copy()
                df_clean['Date'] = pd.to_datetime(df_clean['Date'], format='%Y%m%d', errors='coerce')
                df_clean = df_clean.dropna(subset=['Date'])

                # 找收盤價欄位
                price_col = None
                for col in ['ClosingPrice', 'ClosingIndex', '收盤指數']:
                    if col in df_clean.columns:
                        price_col = col
                        break

                if price_col is None:
                    print("❌ 找不到價格欄位")
                    return

                df_clean[price_col] = pd.to_numeric(df_clean[price_col], errors='coerce')
                df_clean = df_clean.dropna(subset=[price_col])
                df_clean = df_clean.sort_values('Date')

                print(f"📊 準備繪製 {len(df_clean)} 個數據點")

                # 創建matplotlib圖表
                fig = Figure(figsize=(12, 6), facecolor='#2b2b2b')
                ax = fig.add_subplot(111)
                ax.set_facecolor('#2b2b2b')

                # 繪製圖表
                if "線圖" in chart_type:
                    ax.plot(df_clean['Date'], df_clean[price_col], color='yellow', linewidth=2)
                elif "柱狀圖" in chart_type:
                    ax.bar(df_clean['Date'], df_clean[price_col], color='yellow', alpha=0.7)
                elif "面積圖" in chart_type:
                    ax.fill_between(df_clean['Date'], df_clean[price_col], color='yellow', alpha=0.3)
                    ax.plot(df_clean['Date'], df_clean[price_col], color='yellow', linewidth=2)

                # 設置標題和標籤
                ax.set_title(f'歷史指數走勢 ({chart_type})', color='white', fontsize=14)
                ax.set_xlabel('日期', color='white')
                ax.set_ylabel('指數', color='white')

                # 設置日期格式
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(df_clean)//8)))

                # 設置顏色
                ax.tick_params(colors='white')
                ax.spines['bottom'].set_color('white')
                ax.spines['top'].set_color('white')
                ax.spines['right'].set_color('white')
                ax.spines['left'].set_color('white')

                # 旋轉日期標籤
                for label in ax.xaxis.get_majorticklabels():
                    label.set_rotation(45)

                fig.tight_layout()

                # 替換PyQtGraph圖表
                # 獲取圖表容器
                chart_container = self.chart_widget.parent()
                if chart_container and hasattr(chart_container, 'layout') and chart_container.layout():
                    chart_layout = chart_container.layout()

                    # 移除舊的圖表widget
                    chart_layout.removeWidget(self.chart_widget)
                    self.chart_widget.setParent(None)

                    # 創建新的matplotlib canvas
                    canvas = FigureCanvas(fig)
                    canvas.setStyleSheet("background-color: #2b2b2b;")

                    # 添加到布局
                    chart_layout.addWidget(canvas)
                    self.chart_widget = canvas  # 更新引用
                else:
                    print("❌ 無法獲取圖表容器布局")

                print("✅ matplotlib圖表生成完成！日期軸應該正常顯示了！")

            else:
                QMessageBox.warning(self, "數據錯誤", "找不到日期欄位")

        except Exception as e:
            print(f"歷史圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "圖表錯誤", f"歷史指數圖表生成失敗：\n{str(e)}")

    def confirm_labels_set(self, expected_labels):
        """確認標籤是否正確設置"""
        try:
            bottom_axis = self.chart_widget.getAxis('bottom')
            if bottom_axis is not None:
                # 檢查軸是否有標籤
                try:
                    ticks = bottom_axis.tickValues(0, 100, 10)  # 獲取當前刻度
                    if ticks and len(ticks) > 0:
                        print(f"🔍 軸標籤確認成功！當前有 {len(ticks)} 組刻度")
                    else:
                        print("⚠️ 軸標籤確認失敗，嘗試重新設置...")
                        # 重新設置
                        bottom_axis.setTicks([expected_labels])
                        bottom_axis.setStyle(showValues=True)
                        bottom_axis.show()
                        self.chart_widget.update()
                        print("🔄 重新設置完成")
                except:
                    print("⚠️ 無法檢查刻度，直接重新設置...")
                    bottom_axis.setTicks([expected_labels])
                    bottom_axis.setStyle(showValues=True)
                    bottom_axis.show()
                    self.chart_widget.update()
            else:
                print("❌ 確認時無法獲取底部軸")
        except Exception as e:
            print(f"❌ 確認標籤設置失敗: {e}")

    def set_date_labels_immediately(self, x_data, daily_data):
        """立即設置日期標籤的專用方法"""
        try:
            import pyqtgraph as pg

            print("🔥 立即設置日期標籤...")

            # 創建日期標籤
            date_labels = []
            total_points = len(x_data)

            # 智能選擇標籤間隔
            if total_points <= 5:
                step = 1
            elif total_points <= 20:
                step = max(1, total_points // 5)
            elif total_points <= 50:
                step = max(1, total_points // 8)
            else:
                step = max(1, total_points // 10)

            # 生成日期標籤
            for i in range(0, total_points, step):
                if i < len(daily_data):
                    date_str = daily_data.iloc[i]['Date'].strftime('%m/%d')  # 使用更短的格式
                    date_labels.append((x_data[i], date_str))

            # 確保包含最後一個點
            if total_points > 1 and (total_points - 1) % step != 0:
                last_idx = total_points - 1
                if last_idx < len(daily_data):
                    date_str = daily_data.iloc[last_idx]['Date'].strftime('%m/%d')
                    date_labels.append((x_data[last_idx], date_str))

            # 獲取底部軸並設置標籤
            bottom_axis = self.chart_widget.getAxis('bottom')
            if bottom_axis is not None:
                # 清除現有標籤
                bottom_axis.setTicks([])

                # 設置新標籤
                bottom_axis.setTicks([date_labels])

                # 強制顯示設置
                bottom_axis.setStyle(showValues=True)
                bottom_axis.show()
                bottom_axis.setPen(color='white', width=2)
                bottom_axis.setTextPen(color='white')
                bottom_axis.setHeight(50)

                print(f"🔥 立即設置完成！共 {len(date_labels)} 個日期標籤")
                print(f"📅 標籤內容: {[label[1] for label in date_labels]}")

                # 強制更新圖表
                self.chart_widget.update()
                self.chart_widget.repaint()

                return True
            else:
                print("❌ 無法獲取底部軸")
                return False

        except Exception as e:
            print(f"❌ 立即設置日期標籤失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_index_chart(self, df, chart_type):
        """生成市場指數圖表"""
        try:
            import pyqtgraph as pg
            from datetime import datetime
            import pandas as pd

            print(f"📊 市場指數圖表 - 原始數據形狀: {df.shape}")
            print(f"📊 市場指數圖表 - 欄位: {df.columns.tolist()}")

            # 創建一個乾淨的數據副本
            clean_df = df.copy()

            # 尋找收盤指數欄位
            closing_col = None
            for col in ['收盤指數', 'ClosingIndex', 'Close', 'close', '指數']:
                if col in clean_df.columns:
                    closing_col = col
                    break

            if closing_col is None:
                QMessageBox.warning(self, "數據錯誤", f"找不到收盤指數欄位。可用欄位: {clean_df.columns.tolist()}")
                return

            # 轉換收盤指數為數字
            clean_df[closing_col] = pd.to_numeric(clean_df[closing_col], errors='coerce')

            # 移除無效數據
            clean_df = clean_df.dropna(subset=[closing_col])

            if len(clean_df) == 0:
                QMessageBox.warning(self, "數據錯誤", "無有效的收盤指數數據")
                return

            # 尋找合適的分類欄位來分散X軸
            category_col = None
            for col in ['證券代號', 'Code', 'Symbol', 'symbol', '代號']:
                if col in clean_df.columns:
                    category_col = col
                    break

            if category_col:
                # 按證券代號排序，創建分散的X軸
                clean_df = clean_df.sort_values(category_col)
                x_data = list(range(len(clean_df)))
                x_labels = clean_df[category_col].astype(str).tolist()
                use_category_axis = True
                print(f"📊 使用分類軸: {category_col}, {len(x_labels)} 個類別")
            else:
                # 如果沒有分類欄位，使用序號
                x_data = list(range(len(clean_df)))
                x_labels = [f"項目{i+1}" for i in range(len(clean_df))]
                use_category_axis = False
                print(f"📊 使用序號軸: {len(x_data)} 個項目")

            y_data = clean_df[closing_col].tolist()

            print(f"📊 X軸數據點: {len(x_data)}")
            print(f"📊 Y軸數據範圍: {min(y_data):.2f} - {max(y_data):.2f}")

            # 不使用時間軸，使用普通數字軸
            self.chart_widget.setLabel('bottom', '證券代號' if category_col else '項目序號')

            # 繪製圖表
            if "線圖" in chart_type:
                self.chart_widget.plot(x_data, y_data, pen=pg.mkPen('g', width=2), symbol='o', symbolBrush='g', symbolSize=4)
            elif "柱狀圖" in chart_type:
                bargraph = pg.BarGraphItem(x=x_data, height=y_data, width=0.6, brush='g')
                self.chart_widget.addItem(bargraph)
            elif "面積圖" in chart_type:
                self.chart_widget.plot(x_data, y_data, pen=pg.mkPen('g', width=2), fillLevel=min(y_data), brush=(0, 255, 0, 100))

            self.chart_widget.setLabel('left', '收盤指數')
            self.chart_widget.setTitle(f'市場指數資訊 ({chart_type}) - {len(clean_df)} 檔股票')

            # 設置Y軸範圍
            if y_data:
                y_min, y_max = min(y_data), max(y_data)
                y_range = y_max - y_min
                if y_range > 0:
                    self.chart_widget.setYRange(y_min - y_range * 0.1, y_max + y_range * 0.1)

            # 設置X軸範圍
            if x_data:
                self.chart_widget.setXRange(-0.5, len(x_data) - 0.5)

            # 改進X軸標籤顯示
            try:
                bottom_axis = self.chart_widget.getAxis('bottom')
                if bottom_axis is not None and use_category_axis:
                    # 根據數據量決定顯示策略
                    total_items = len(x_labels)

                    if total_items <= 20:
                        # 數據少時顯示所有標籤
                        ticks = [(i, label[:10]) for i, label in enumerate(x_labels)]
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示所有 {total_items} 個標籤")
                    elif total_items <= 100:
                        # 中等數據量時顯示部分標籤
                        step = max(1, total_items // 10)
                        ticks = [(i, x_labels[i][:10]) for i in range(0, total_items, step)]
                        # 確保包含最後一個
                        if (total_items - 1) % step != 0:
                            ticks.append((total_items - 1, x_labels[-1][:10]))
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示 {len(ticks)} 個標籤 (每 {step} 個顯示一個)")
                    else:
                        # 大量數據時顯示更少標籤
                        step = max(1, total_items // 20)
                        ticks = [(i, x_labels[i][:8]) for i in range(0, total_items, step)]
                        # 確保包含最後一個
                        if (total_items - 1) % step != 0:
                            ticks.append((total_items - 1, x_labels[-1][:8]))
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示 {len(ticks)} 個標籤 (每 {step} 個顯示一個)")

            except Exception as e:
                print(f"❌ 設置X軸標籤失敗: {e}")

            # 安全地更新軸顯示
            try:
                bottom_axis = self.chart_widget.getAxis('bottom')
                if bottom_axis is not None:
                    bottom_axis.setStyle(showValues=True)
                    bottom_axis.show()
                    # 設置軸的顏色和字體
                    bottom_axis.setPen(color='white')
                    bottom_axis.setTextPen(color='white')
                    print("✅ 軸顯示設置完成")
            except Exception as e:
                print(f"❌ 軸顯示更新失敗: {e}")

        except Exception as e:
            print(f"市場指數圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "圖表錯誤", f"市場指數圖表生成失敗：\n{str(e)}")

    def generate_margin_chart(self, df, chart_type):
        """生成融資融券圖表"""
        try:
            import pyqtgraph as pg
            from datetime import datetime
            import pandas as pd

            print(f"📊 融資融券圖表 - 原始數據形狀: {df.shape}")
            print(f"📊 融資融券圖表 - 欄位: {df.columns.tolist()}")

            # 創建一個乾淨的數據副本
            clean_df = df.copy()

            # 尋找融資餘額欄位
            margin_col = None
            for col in ['融資今日餘額', '融資餘額', 'MarginBalance', 'margin_balance']:
                if col in clean_df.columns:
                    margin_col = col
                    break

            if margin_col is None:
                QMessageBox.warning(self, "數據錯誤", f"找不到融資餘額欄位。可用欄位: {clean_df.columns.tolist()}")
                return

            # 轉換融資餘額為數字
            clean_df[margin_col] = pd.to_numeric(clean_df[margin_col], errors='coerce')

            # 移除無效數據
            clean_df = clean_df.dropna(subset=[margin_col])

            if len(clean_df) == 0:
                QMessageBox.warning(self, "數據錯誤", "無有效的融資餘額數據")
                return

            # 尋找股票代號欄位
            stock_col = None
            for col in ['證券代號', 'Code', 'Symbol', 'symbol', '代號', 'stock_code']:
                if col in clean_df.columns:
                    stock_col = col
                    break

            if stock_col:
                # 按股票代號排序
                clean_df = clean_df.sort_values(stock_col)
                x_data = list(range(len(clean_df)))
                x_labels = clean_df[stock_col].astype(str).tolist()
                use_stock_axis = True
                print(f"📊 使用股票代號軸: {stock_col}, {len(x_labels)} 檔股票")
            else:
                # 如果沒有股票代號，使用序號
                x_data = list(range(len(clean_df)))
                x_labels = [f"股票{i+1}" for i in range(len(clean_df))]
                use_stock_axis = False
                print(f"📊 使用序號軸: {len(x_data)} 檔股票")

            y_data = clean_df[margin_col].tolist()

            print(f"📊 X軸數據點: {len(x_data)}")
            print(f"📊 Y軸數據範圍: {min(y_data):,.0f} - {max(y_data):,.0f}")

            # 設置軸標籤
            self.chart_widget.setLabel('bottom', '股票代號' if stock_col else '股票序號')

            # 繪製圖表
            if "線圖" in chart_type:
                self.chart_widget.plot(x_data, y_data, pen=pg.mkPen('r', width=2), symbol='o', symbolBrush='r', symbolSize=4)
            elif "柱狀圖" in chart_type:
                bargraph = pg.BarGraphItem(x=x_data, height=y_data, width=0.6, brush='r')
                self.chart_widget.addItem(bargraph)
            elif "面積圖" in chart_type:
                self.chart_widget.plot(x_data, y_data, pen=pg.mkPen('r', width=2), fillLevel=0, brush=(255, 0, 0, 100))

            self.chart_widget.setLabel('left', '融資餘額 (千元)')
            self.chart_widget.setTitle(f'融資融券統計 ({chart_type}) - {len(clean_df)} 檔股票')

            # 設置Y軸範圍
            if y_data:
                y_min, y_max = min(y_data), max(y_data)
                y_range = y_max - y_min
                if y_range > 0:
                    self.chart_widget.setYRange(y_min - y_range * 0.1, y_max + y_range * 0.1)

            # 設置X軸範圍
            if x_data:
                self.chart_widget.setXRange(-0.5, len(x_data) - 0.5)

            # 改進X軸標籤顯示 - 融資融券圖表
            try:
                bottom_axis = self.chart_widget.getAxis('bottom')
                if bottom_axis is not None and use_stock_axis:
                    # 根據股票數量決定顯示策略
                    total_stocks = len(x_labels)

                    if total_stocks <= 15:
                        # 股票少時顯示所有代號
                        ticks = [(i, label[:8]) for i, label in enumerate(x_labels)]
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示所有 {total_stocks} 個股票代號")
                    elif total_stocks <= 50:
                        # 中等數量時顯示部分代號
                        step = max(1, total_stocks // 10)
                        ticks = [(i, x_labels[i][:8]) for i in range(0, total_stocks, step)]
                        # 確保包含最後一個
                        if (total_stocks - 1) % step != 0:
                            ticks.append((total_stocks - 1, x_labels[-1][:8]))
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示 {len(ticks)} 個股票代號 (每 {step} 個顯示一個)")
                    else:
                        # 大量股票時顯示更少代號
                        step = max(1, total_stocks // 15)
                        ticks = [(i, x_labels[i][:6]) for i in range(0, total_stocks, step)]
                        # 確保包含最後一個
                        if (total_stocks - 1) % step != 0:
                            ticks.append((total_stocks - 1, x_labels[-1][:6]))
                        bottom_axis.setTicks([ticks])
                        print(f"✅ 顯示 {len(ticks)} 個股票代號 (每 {step} 個顯示一個)")

            except Exception as e:
                print(f"❌ 設置X軸標籤失敗: {e}")

            # 安全地更新軸顯示
            try:
                bottom_axis = self.chart_widget.getAxis('bottom')
                if bottom_axis is not None:
                    bottom_axis.setStyle(showValues=True)
                    bottom_axis.show()
                    # 設置軸的顏色和字體
                    bottom_axis.setPen(color='white')
                    bottom_axis.setTextPen(color='white')
                    print("✅ 融資融券圖表軸顯示設置完成")
            except Exception as e:
                print(f"❌ 軸顯示更新失敗: {e}")

        except Exception as e:
            print(f"融資融券圖表生成錯誤: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "圖表錯誤", f"融資融券圖表生成失敗：\n{str(e)}")

    def display_dataframe(self, df, data_type):
        """在表格中顯示DataFrame"""
        if df.empty:
            return

        # 設置表格大小
        self.data_table.setRowCount(len(df))
        self.data_table.setColumnCount(len(df.columns))

        # 設置表頭
        self.data_table.setHorizontalHeaderLabels(df.columns.tolist())

        # 填充數據
        for i in range(len(df)):
            for j in range(len(df.columns)):
                item = QTableWidgetItem(str(df.iloc[i, j]))
                self.data_table.setItem(i, j, item)

        # 調整列寬
        self.data_table.horizontalHeader().setStretchLastSection(True)
        self.data_table.resizeColumnsToContents()

        # 更新狀態
        data_names = {
            'market_index': '市場指數資訊',
            'margin_trading': '融資融券統計',
            'historical_index': '歷史指數資料'
        }

        self.result_text.append(f"📊 正在顯示 {data_names.get(data_type, data_type)} 數據 ({len(df)} 筆)")

    def export_to_csv(self):
        """導出數據到CSV"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            import sqlite3
            import pandas as pd

            # 選擇保存位置
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "導出CSV檔案",
                f"twse_market_data_{QDate.currentDate().toString('yyyyMMdd')}.csv",
                "CSV files (*.csv);;All files (*.*)"
            )

            if not file_path:
                return

            # 收集所有數據
            all_data = []
            data_info = []

            db_files = {
                'market_index': 'market_index.db',
                'margin_trading': 'margin_trading.db',
                'historical_index': 'market_historical.db'
            }

            for data_type, db_file in db_files.items():
                db_path = f"D:/Finlab/history/tables/{db_file}"
                try:
                    conn = sqlite3.connect(db_path)

                    # 使用正確的表格名稱
                    if data_type == 'market_index':
                        df = pd.read_sql_query("SELECT * FROM market_index_info", conn)
                        if not df.empty:
                            df['數據類型'] = '市場指數資訊'
                            all_data.append(df)
                            data_info.append(f"市場指數資訊: {len(df)} 筆")
                    elif data_type == 'margin_trading':
                        df = pd.read_sql_query("SELECT * FROM margin_trading_info", conn)
                        if not df.empty:
                            df['數據類型'] = '融資融券統計'
                            all_data.append(df)
                            data_info.append(f"融資融券統計: {len(df)} 筆")
                    elif data_type == 'historical_index':
                        df = pd.read_sql_query("SELECT * FROM market_historical_index", conn)
                        if not df.empty:
                            df['數據類型'] = '歷史指數資料'
                            all_data.append(df)
                            data_info.append(f"歷史指數資料: {len(df)} 筆")

                    conn.close()

                except Exception as e:
                    continue

            if not all_data:
                QMessageBox.warning(self, "無數據", "沒有可導出的數據。")
                return

            # 分別導出或合併導出
            if len(all_data) == 1:
                # 只有一種數據類型，直接導出
                combined_df = all_data[0]
                combined_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                total_records = len(combined_df)
            else:
                # 多種數據類型，創建多個工作表的CSV或分別導出
                # 由於CSV不支持多工作表，我們創建一個包含所有數據的統一格式

                # 先檢查是否可以安全合併（欄位相似度）
                can_merge = True
                base_columns = set(all_data[0].columns)

                for df in all_data[1:]:
                    current_columns = set(df.columns)
                    # 如果欄位差異太大，不適合合併
                    common_cols = base_columns.intersection(current_columns)
                    if len(common_cols) < 3:  # 至少要有3個共同欄位
                        can_merge = False
                        break

                if can_merge:
                    # 可以合併：使用外連接保留所有欄位
                    combined_df = pd.concat(all_data, ignore_index=True, sort=False)
                    combined_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                    total_records = len(combined_df)
                else:
                    # 不適合合併：分別導出到不同檔案
                    base_name = file_path.rsplit('.', 1)[0]
                    extension = file_path.rsplit('.', 1)[1] if '.' in file_path else 'csv'

                    total_records = 0
                    exported_files = []

                    for i, df in enumerate(all_data):
                        data_type_name = df['數據類型'].iloc[0] if '數據類型' in df.columns else f"數據{i+1}"
                        separate_file = f"{base_name}_{data_type_name}.{extension}"
                        df.to_csv(separate_file, index=False, encoding='utf-8-sig')
                        total_records += len(df)
                        exported_files.append(separate_file)

                    # 更新檔案路徑信息用於後續顯示
                    file_path = f"{len(exported_files)} 個檔案 (主檔名: {base_name})"

            # 詢問是否開啟檔案
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("導出成功")
            msg_box.setIcon(QMessageBox.Icon.Question)
            msg_box.setText("✅ 數據導出成功！")

            # 構建詳細信息
            detail_info = f"📁 檔案位置：{file_path}\n"
            detail_info += f"📊 記錄數量：{total_records} 筆\n"
            detail_info += f"📄 檔案格式：CSV (UTF-8 with BOM)\n"
            if data_info:
                detail_info += f"📋 數據明細：\n" + "\n".join(f"   • {info}" for info in data_info) + "\n"
            detail_info += f"\n❓ 是否要立即開啟導出的檔案？"

            msg_box.setInformativeText(detail_info)

            # 自定義按鈕文字
            yes_btn = msg_box.addButton("🚀 開啟檔案", QMessageBox.ButtonRole.YesRole)
            no_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
            msg_box.setDefaultButton(yes_btn)

            reply = msg_box.exec()

            # 判斷用戶選擇
            if msg_box.clickedButton() == yes_btn:
                try:
                    import os
                    import subprocess
                    import platform

                    # 根據作業系統選擇開啟方式
                    system = platform.system()
                    if system == "Windows":
                        os.startfile(file_path)
                    elif system == "Darwin":  # macOS
                        subprocess.run(["open", file_path], check=True)
                    else:  # Linux
                        subprocess.run(["xdg-open", file_path], check=True)

                    # 成功開啟的提示
                    self.result_text.append(f"🚀 數據已導出並開啟: {file_path}")

                    # 顯示簡短的成功提示
                    QMessageBox.information(
                        self,
                        "開啟成功",
                        f"📂 檔案已在預設應用程式中開啟\n\n"
                        f"💡 提示：如果檔案沒有自動開啟，請檢查預設的CSV檔案關聯程式"
                    )

                except Exception as e:
                    # 開啟失敗的詳細錯誤處理
                    error_msg = (
                        f"❌ 無法自動開啟檔案\n\n"
                        f"錯誤原因：{str(e)}\n\n"
                        f"📁 檔案位置：\n{file_path}\n\n"
                        f"💡 解決方案：\n"
                        f"1. 手動瀏覽到上述位置開啟檔案\n"
                        f"2. 檢查是否安裝了CSV檔案的預設開啟程式\n"
                        f"3. 嘗試用Excel、記事本或其他程式開啟"
                    )

                    QMessageBox.warning(self, "開啟失敗", error_msg)
                    self.result_text.append(f"📤 數據已導出到: {file_path}")
            else:
                self.result_text.append(f"📤 數據已導出到: {file_path}")

                # 顯示檔案位置提示
                QMessageBox.information(
                    self,
                    "導出完成",
                    f"📋 數據已成功保存\n\n"
                    f"📁 檔案位置：\n{file_path}\n\n"
                    f"💡 您可以稍後手動開啟此檔案"
                )

        except Exception as e:
            QMessageBox.critical(self, "導出失敗", f"導出數據失敗：\n{str(e)}")

    def closeEvent(self, event):
        """處理窗口關閉事件"""
        # 如果有正在運行的工作線程，先停止它
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "確認關閉",
                "爬蟲正在運行中，確定要關閉嗎？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 停止工作線程
                if hasattr(self, 'worker') and self.worker:
                    self.worker.terminate()
                    self.worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """測試對話框"""
    app = QApplication(sys.argv)

    dialog = TWSEMarketDataDialog()
    dialog.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
