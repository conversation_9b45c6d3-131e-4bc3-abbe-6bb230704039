#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化掃描測試
完全避免UI更新，只測試核心掃描功能
"""

import sys
import time
import logging
import os

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_core_scan_only():
    """只測試核心掃描功能，不涉及UI"""
    print("最小化掃描測試 - 無UI版本")
    print("=" * 50)
    
    try:
        # 直接測試監控器，不使用ScanWorker
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        print("✅ 創建監控器...")
        monitor = PreMarketMonitor()
        
        print("🚀 執行掃描...")
        start_time = time.time()
        
        # 直接調用掃描方法
        results = monitor.run_full_scan()
        
        scan_time = time.time() - start_time
        
        if results:
            print(f"✅ 掃描成功，耗時 {scan_time:.2f}秒")
            
            # 分析結果
            if isinstance(results, dict):
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"📊 獲取 {total_items} 項數據")
                
                # 顯示各類別數據
                for category, data in results.items():
                    if isinstance(data, dict) and data:
                        print(f"   • {category}: {len(data)} 項")
                
                return True, total_items
            else:
                print(f"⚠️ 結果格式異常: {type(results)}")
                return False, 0
        else:
            print(f"❌ 掃描失敗，耗時 {scan_time:.2f}秒")
            return False, 0
            
    except Exception as e:
        print(f"❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_scanworker_without_ui():
    """測試ScanWorker但不使用UI回調"""
    print("\nScanWorker測試 - 無UI回調版本")
    print("=" * 50)
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        monitor = PreMarketMonitor()
        
        # 結果收集
        results_data = {'success': None, 'error': None}
        
        def simple_success_callback(results):
            print(f"🎉 ScanWorker 成功回調")
            results_data['success'] = results
            if isinstance(results, dict):
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"   獲取 {total_items} 項數據")
        
        def simple_error_callback(error_msg):
            print(f"❌ ScanWorker 錯誤回調: {error_msg}")
            results_data['error'] = error_msg
        
        # 不使用進度回調，避免UI問題
        print("📊 創建 ScanWorker（無進度回調）...")
        scan_worker = ScanWorker(
            monitor,
            callback_success=simple_success_callback,
            callback_error=simple_error_callback,
            progress_callback=None  # 不使用進度回調
        )
        
        print("🚀 啟動 ScanWorker...")
        scan_worker.start_scan()
        
        # 等待完成
        print("⏳ 等待完成...")
        timeout = 20
        start_time = time.time()
        
        while (results_data['success'] is None and 
               results_data['error'] is None and 
               time.time() - start_time < timeout):
            time.sleep(0.5)
        
        elapsed = time.time() - start_time
        
        if results_data['success']:
            print(f"✅ ScanWorker 測試成功，耗時 {elapsed:.2f}秒")
            return True
        elif results_data['error']:
            print(f"❌ ScanWorker 測試失敗: {results_data['error']}")
            return False
        else:
            print(f"⏰ ScanWorker 測試超時（{timeout}秒）")
            return False
            
    except Exception as e:
        print(f"❌ ScanWorker 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("QPainter 問題修復測試")
    print("=" * 60)
    print("修復策略:")
    print("  1. 禁用進度條UI更新")
    print("  2. 移除所有UI線程操作")
    print("  3. 只保留核心掃描功能")
    print("  4. 避免QPainter相關操作")
    print()
    
    # 1. 測試核心掃描功能
    core_success, total_items = test_core_scan_only()
    
    if core_success:
        # 2. 測試ScanWorker（無UI）
        scanworker_success = test_scanworker_without_ui()
        
        print(f"\n🎯 測試結果總結:")
        print(f"   核心掃描: {'✅ 成功' if core_success else '❌ 失敗'}")
        print(f"   ScanWorker: {'✅ 成功' if scanworker_success else '❌ 失敗'}")
        print(f"   數據項目: {total_items}")
        
        if core_success and scanworker_success:
            print("\n🎉 核心功能正常！")
            print("   問題可能在於UI更新部分")
            print("   建議:")
            print("   • 暫時禁用進度條")
            print("   • 避免在掃描過程中更新UI")
            print("   • 只在掃描完成後更新結果")
        else:
            print("\n⚠️ 核心功能有問題")
    else:
        print("\n❌ 核心掃描功能失敗，跳過ScanWorker測試")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 測試結束")
