"""
測試範例資料功能（不使用GUI）
"""

def test_sample_data():
    """測試範例資料功能"""
    try:
        print('🧪 測試範例資料模式...')
        
        # 只導入爬蟲核心功能，避免tkinter
        import sys
        import os
        sys.path.append('.')
        
        from stock_basic_info_crawler import (
            crawler_stock_basic_info, 
            StockBasicInfoDatabase, 
            get_market_types,
            create_sample_data
        )
        
        print('✅ 模組導入成功')
        
        # 測試市場類型
        markets = get_market_types()
        print(f'📊 支援的市場類型: {markets}')
        
        # 測試各市場的範例資料
        for market_code, market_name in markets.items():
            print(f'\n🔍 測試 {market_name}({market_code}) 範例資料...')
            
            # 直接調用範例資料函數
            df = create_sample_data(market_code)
            
            if not df.empty:
                print(f'✅ 成功生成 {len(df)} 筆 {market_name} 範例資料')
                
                # 檢查必要欄位
                required_fields = ['company_code', 'company_name', 'market_type']
                for field in required_fields:
                    if field in df.columns:
                        print(f'  ✅ 包含欄位: {field}')
                    else:
                        print(f'  ❌ 缺少欄位: {field}')
                
                # 顯示前2筆資料
                print(f'  📄 範例資料:')
                for i, row in df.head(2).iterrows():
                    print(f'    {i+1}. {row["company_code"]} - {row["company_name"]}')
            else:
                print(f'❌ 未能生成 {market_name} 範例資料')
                return False
        
        # 測試資料庫功能
        print(f'\n🗄️ 測試資料庫功能...')
        db = StockBasicInfoDatabase('test_sample_final.db')
        print('✅ 資料庫初始化成功')
        
        # 儲存上市公司範例資料
        sii_df = create_sample_data('sii')
        saved_count = db.save_data(sii_df)
        print(f'✅ 成功儲存 {saved_count} 筆上市公司範例資料')
        
        # 測試統計功能
        total_count = db.get_data_count()
        stats = db.get_market_stats()
        print(f'📊 資料庫總筆數: {total_count}')
        print(f'📊 各市場統計: {stats}')
        
        # 測試匯出功能
        export_path = 'test_sample_export.csv'
        success = db.export_to_csv(export_path)
        if success:
            print(f'✅ 成功匯出資料到: {export_path}')
        else:
            print('❌ 匯出失敗')
        
        return True
        
    except Exception as e:
        print(f'❌ 測試失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_crawler_with_fallback():
    """測試爬蟲的備用機制"""
    try:
        print('\n🔄 測試爬蟲備用機制...')
        
        from stock_basic_info_crawler import crawler_stock_basic_info
        
        # 測試正常模式（會因為網路問題回退到範例資料）
        print('🌐 嘗試正常爬取（預期會回退到範例資料）...')
        df = crawler_stock_basic_info('sii', use_sample_data=False)
        
        if not df.empty:
            print(f'✅ 取得 {len(df)} 筆資料（可能是範例資料）')
            
            # 檢查是否為範例資料
            if '範例公司' in str(df['company_name'].iloc[0]):
                print('✅ 確認使用了範例資料作為備用')
            else:
                print('✅ 成功取得實際資料')
        else:
            print('❌ 未能取得任何資料')
            return False
        
        # 測試範例資料模式
        print('\n🧪 測試範例資料模式...')
        df_sample = crawler_stock_basic_info('sii', use_sample_data=True)
        
        if not df_sample.empty:
            print(f'✅ 範例資料模式成功，取得 {len(df_sample)} 筆資料')
        else:
            print('❌ 範例資料模式失敗')
            return False
        
        return True
        
    except Exception as e:
        print(f'❌ 備用機制測試失敗: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始範例資料功能測試")
    print("=" * 60)
    
    success1 = test_sample_data()
    success2 = test_crawler_with_fallback()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有測試通過！")
        print("✅ 範例資料功能正常運作")
        print("✅ 爬蟲備用機制正常運作")
        print("\n💡 說明:")
        print("- 由於網路連線問題，爬蟲會自動使用範例資料")
        print("- 您可以在GUI中勾選「範例資料模式」進行測試")
        print("- 範例資料包含完整的欄位結構，適合功能測試")
    else:
        print("❌ 部分測試失敗")
    
    print("\n📋 下一步:")
    print("1. 啟動主程式測試GUI功能")
    print("2. 解決網路連線問題後測試實際爬取")
    print("3. 使用範例資料模式驗證完整流程")
