#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試修復效果
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_stock_code_conversion():
    """測試股票代碼轉換修復"""
    print("🔧 測試股票代碼轉換修復")
    print("=" * 40)
    
    try:
        from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy
        
        strategy = IntradayOpeningRangeStrategy()
        
        # 測試問題股票代碼
        test_codes = [
            ('00743', '上櫃股票'),
            ('00767', '上櫃股票'),
            ('008201', '上櫃股票-長代碼'),
            ('2330', '上市股票'),
            ('2317', '上市股票'),
        ]
        
        print("代碼轉換測試:")
        for code, desc in test_codes:
            try:
                result = strategy.convert_stock_symbol(code)
                print(f"✅ {code:>6} → {result:>12} ({desc})")
            except Exception as e:
                print(f"❌ {code:>6} → 錯誤: {str(e)[:30]}...")
        
        print()
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    
    return True

def test_failed_stock_handling():
    """測試失敗股票處理"""
    print("🛡️ 測試失敗股票處理")
    print("=" * 40)
    
    try:
        from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy
        
        strategy = IntradayOpeningRangeStrategy()
        
        # 測試無效股票代碼
        invalid_codes = ['00743', '00767', '9999', '0000']
        
        print("失敗股票處理測試:")
        for code in invalid_codes:
            try:
                # 嘗試獲取數據
                data = strategy.fetch_daily_data_twstock(code, 5)
                
                if data.empty:
                    if code in strategy.failed_stocks:
                        print(f"✅ {code}: 正確添加到失敗列表")
                    else:
                        print(f"⚠️ {code}: 數據為空但未添加到失敗列表")
                else:
                    print(f"✅ {code}: 成功獲取數據 ({len(data)} 筆)")
                    
            except Exception as e:
                if code in strategy.failed_stocks:
                    print(f"✅ {code}: 錯誤已處理，添加到失敗列表")
                else:
                    print(f"❌ {code}: 錯誤未正確處理")
        
        print(f"失敗列表大小: {len(strategy.failed_stocks)}")
        print()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
    
    return True

def test_progress_dialog_import():
    """測試進度條相關導入"""
    print("📊 測試進度條相關導入")
    print("=" * 40)
    
    try:
        from PyQt6.QtWidgets import QProgressDialog, QApplication
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 進度條組件導入成功")
        
        # 測試是否可以創建進度條（不顯示）
        app = QApplication.instance()
        if app is None:
            print("⚠️ 需要 QApplication 實例才能測試進度條")
        else:
            print("✅ QApplication 實例存在")
        
        print()
        return True
        
    except ImportError as e:
        print(f"❌ PyQt6 導入失敗: {e}")
        return False

def test_basic_functionality():
    """測試基本功能"""
    print("⚡ 測試基本功能")
    print("=" * 40)
    
    try:
        from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy
        
        strategy = IntradayOpeningRangeStrategy()
        
        # 測試基本屬性
        print(f"✅ 數據源: {strategy.data_source}")
        print(f"✅ 請求延遲: {strategy.request_delay} 秒")
        print(f"✅ 最大重試: {strategy.max_retries} 次")
        print(f"✅ 緩存啟用: {strategy.enable_cache}")
        print(f"✅ 失敗列表: {len(strategy.failed_stocks)} 支")
        
        # 測試方法存在
        methods_to_check = [
            'convert_stock_symbol',
            'fetch_daily_data',
            'batch_fetch_data',
            'ultra_fast_analysis',
            'ultra_fast_analysis_with_progress',
            '_wait_for_rate_limit'
        ]
        
        print("\n方法檢查:")
        for method_name in methods_to_check:
            if hasattr(strategy, method_name):
                print(f"✅ {method_name}")
            else:
                print(f"❌ {method_name} 缺失")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ 基本功能測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 快速修復效果驗證")
    print("=" * 50)
    
    tests = [
        ("股票代碼轉換", test_stock_code_conversion),
        ("失敗股票處理", test_failed_stock_handling),
        ("進度條導入", test_progress_dialog_import),
        ("基本功能", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過\n")
            else:
                print(f"❌ {test_name} 失敗\n")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}\n")
    
    print("=" * 50)
    print(f"🎯 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！修復成功")
        print("\n💡 主要修復:")
        print("• ✅ 上櫃股票代碼正確處理")
        print("• ✅ 失敗股票自動管理")
        print("• ✅ 進度條支持")
        print("• ✅ 錯誤處理機制")
    else:
        print("⚠️ 部分測試失敗，需要進一步檢查")
    
    print("\n🚀 現在可以測試GUI中的日內策略功能！")

if __name__ == "__main__":
    main()
