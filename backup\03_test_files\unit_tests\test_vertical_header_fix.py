#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Google股市新聞垂直標題欄修正
"""

import sys
from PyQt6.QtWidgets import QApplication, QMessageBox

def test_vertical_header():
    """測試垂直標題欄修正"""
    print("🧪 測試Google股市新聞垂直標題欄修正")
    print("=" * 50)
    
    try:
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 測試導入Google新聞GUI
        print("\n📦 測試模組導入:")
        try:
            from google_stock_news_gui import GoogleStockNewsDialog
            print("✅ GoogleStockNewsDialog 導入成功")
        except ImportError as e:
            print(f"❌ GoogleStockNewsDialog 導入失敗: {e}")
            return 1
        
        # 測試創建對話框
        print("\n🖥️ 測試GUI創建:")
        try:
            dialog = GoogleStockNewsDialog()
            print("✅ Google新聞對話框創建成功")
            
        except Exception as e:
            print(f"❌ GUI創建失敗: {e}")
            return 1
        
        print("\n📊 垂直標題欄修正內容:")
        print("1. ✅ 移除額外的編號欄位")
        print("2. ✅ 啟用表格的垂直標題欄（行號欄）")
        print("3. ✅ 設定垂直標題欄寬度為50px")
        print("4. ✅ 添加垂直標題欄樣式")
        print("5. ✅ 使用setVerticalHeaderItem設定行號")
        
        print("\n🎯 表格結構:")
        print("┌────┬────────────┬──────────┬─────────────────────────────────────┐")
        print("│ 1  │ 2025-07-18 │ CMoney   │ 1101 台泥：強力監管維三元能源火災... │")
        print("│ 2  │ 2025-07-17 │ CMoney   │ 1101 台泥：台泥自導自演混淆火災？... │")
        print("│ 3  │ 2025-07-17 │ 東森電視 │ 個股：台泥(1101)三元廠火災，國際... │")
        print("└────┴────────────┴──────────┴─────────────────────────────────────┘")
        print("  ↑")
        print("垂直標題欄")
        print("(綠框區域)")
        
        print("\n🔧 技術細節:")
        print("• 欄位數量：3欄（日期、來源、標題）")
        print("• 垂直標題欄：顯示並設定寬度50px")
        print("• 行號設定：使用setVerticalHeaderItem")
        print("• 樣式設定：淺灰背景，深灰文字，置中對齊")
        
        print("\n🎨 樣式設定:")
        print("• 背景色：#f0f0f0（淺灰色）")
        print("• 文字色：#333333（深灰色）")
        print("• 邊框：1px solid #d0d0d0")
        print("• 字體：粗體，12px，置中對齊")
        
        # 詢問是否要開啟GUI測試
        reply = QMessageBox.question(
            None,
            "垂直標題欄測試",
            "是否要開啟Google股市新聞GUI來測試垂直標題欄？\n\n請檢查：\n• 左側綠框區域是否顯示行號\n• 行號是否從1開始遞增\n• 行號樣式是否清晰可見",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            print("\n🖥️ 開啟GUI測試...")
            print("請檢查以下項目：")
            print("1. 左側是否有行號欄位（綠框區域）")
            print("2. 行號是否從1開始，最新新聞為1")
            print("3. 行號文字是否清晰可見")
            print("4. 表格是否只有3欄：日期、來源、標題")
            dialog.show()
            return app.exec()
        else:
            print("\n✅ 測試完成，未開啟GUI")
            return 0
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_vertical_header())
