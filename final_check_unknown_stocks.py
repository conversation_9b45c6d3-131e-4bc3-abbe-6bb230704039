#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終確認 price.db 中是否還有未知股票名稱
詳細分析所有股票的名稱狀態
"""

import os
import sqlite3
import pandas as pd

def analyze_stock_names_in_detail():
    """詳細分析 price.db 中的股票名稱狀態"""
    
    print("=" * 80)
    print("🔍 price.db 股票名稱詳細分析")
    print("=" * 80)
    
    price_db = 'D:/Finlab/history/tables/price.db'
    
    if not os.path.exists(price_db):
        print(f"❌ 找不到 price.db: {price_db}")
        return False
    
    print(f"✅ 找到資料庫: {price_db}")
    print(f"📊 檔案大小: {os.path.getsize(price_db) / (1024*1024):.1f} MB")
    
    try:
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        # 1. 總體統計
        print(f"\n📊 總體統計:")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        total_stocks = cursor.fetchone()[0]
        print(f"   總股票數: {total_stocks}")
        
        # 2. 按名稱類型分類統計
        print(f"\n📋 按名稱類型分類:")
        
        # 正確名稱 (不是預設格式)
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name NOT LIKE 'ETF%' 
        AND stock_name NOT LIKE '股票%'
        AND stock_name IS NOT NULL 
        AND stock_name != ''
        """)
        proper_names = cursor.fetchone()[0]
        proper_rate = proper_names / total_stocks * 100
        print(f"   ✅ 正確名稱: {proper_names} ({proper_rate:.1f}%)")
        
        # ETF 預設名稱
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name LIKE 'ETF%'
        """)
        etf_default = cursor.fetchone()[0]
        etf_rate = etf_default / total_stocks * 100
        print(f"   ⚠️ ETF預設名稱: {etf_default} ({etf_rate:.1f}%)")
        
        # 股票預設名稱
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name LIKE '股票%'
        """)
        stock_default = cursor.fetchone()[0]
        stock_rate = stock_default / total_stocks * 100
        print(f"   ⚠️ 股票預設名稱: {stock_default} ({stock_rate:.1f}%)")
        
        # 空值或 NULL
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name IS NULL OR stock_name = ''
        """)
        null_names = cursor.fetchone()[0]
        null_rate = null_names / total_stocks * 100
        print(f"   ❌ 空值/NULL: {null_names} ({null_rate:.1f}%)")
        
        # 3. 顯示 ETF 預設名稱的股票
        if etf_default > 0:
            print(f"\n📋 ETF預設名稱的股票 (前20個):")
            cursor.execute("""
            SELECT DISTINCT stock_id, stock_name 
            FROM stock_daily_data 
            WHERE stock_name LIKE 'ETF%'
            ORDER BY stock_id
            LIMIT 20
            """)
            etf_stocks = cursor.fetchall()
            
            for stock_id, stock_name in etf_stocks:
                print(f"   {stock_id}: {stock_name}")
            
            if etf_default > 20:
                print(f"   ... 還有 {etf_default - 20} 檔")
        
        # 4. 顯示股票預設名稱的股票
        if stock_default > 0:
            print(f"\n📋 股票預設名稱的股票 (前20個):")
            cursor.execute("""
            SELECT DISTINCT stock_id, stock_name 
            FROM stock_daily_data 
            WHERE stock_name LIKE '股票%'
            ORDER BY stock_id
            LIMIT 20
            """)
            stock_stocks = cursor.fetchall()
            
            for stock_id, stock_name in stock_stocks:
                print(f"   {stock_id}: {stock_name}")
            
            if stock_default > 20:
                print(f"   ... 還有 {stock_default - 20} 檔")
        
        # 5. 顯示空值的股票
        if null_names > 0:
            print(f"\n📋 空值/NULL名稱的股票:")
            cursor.execute("""
            SELECT DISTINCT stock_id, stock_name 
            FROM stock_daily_data 
            WHERE stock_name IS NULL OR stock_name = ''
            ORDER BY stock_id
            LIMIT 10
            """)
            null_stocks = cursor.fetchall()
            
            for stock_id, stock_name in null_stocks:
                print(f"   {stock_id}: '{stock_name}'")
        
        # 6. 檢查特定問題股票
        print(f"\n🔍 檢查之前的問題股票:")
        problem_stocks = ['0058', '0059', '1262', '1235', '1236', '1240', '1256', '1268', '1294', '1295']
        
        for stock_id in problem_stocks:
            cursor.execute("""
            SELECT DISTINCT stock_name, listing_status, industry 
            FROM stock_daily_data 
            WHERE stock_id = ? 
            LIMIT 1
            """, (stock_id,))
            
            result = cursor.fetchone()
            if result:
                stock_name, listing_status, industry = result
                is_correct = not (stock_name.startswith('ETF') or stock_name.startswith('股票') or not stock_name)
                status = "✅" if is_correct else "❌"
                print(f"   {stock_id}: {stock_name} | {listing_status} | {industry} {status}")
            else:
                print(f"   {stock_id}: 資料庫中未找到 ❌")
        
        # 7. 按股票代碼類型分析未知名稱
        print(f"\n📊 按股票代碼類型分析未知名稱:")
        
        # ETF 類型 (00xxxx)
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_id LIKE '00%'
        AND (stock_name LIKE 'ETF%' OR stock_name LIKE '股票%' OR stock_name IS NULL OR stock_name = '')
        """)
        unknown_etf = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_id LIKE '00%'
        """)
        total_etf = cursor.fetchone()[0]
        
        if total_etf > 0:
            unknown_etf_rate = unknown_etf / total_etf * 100
            print(f"   ETF類型 (00xxxx): {unknown_etf}/{total_etf} ({unknown_etf_rate:.1f}%) 未知")
        
        # 一般股票 (1xxx-9xxx)
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data
        WHERE LENGTH(stock_id) = 4
        AND stock_id >= '1000' AND stock_id <= '9999'
        AND (stock_name LIKE 'ETF%' OR stock_name LIKE '股票%' OR stock_name IS NULL OR stock_name = '')
        """)
        unknown_normal = cursor.fetchone()[0]

        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data
        WHERE LENGTH(stock_id) = 4
        AND stock_id >= '1000' AND stock_id <= '9999'
        """)
        total_normal = cursor.fetchone()[0]
        
        if total_normal > 0:
            unknown_normal_rate = unknown_normal / total_normal * 100
            print(f"   一般股票 (1xxx-9xxx): {unknown_normal}/{total_normal} ({unknown_normal_rate:.1f}%) 未知")
        
        # 8. 最新日期的資料完整性
        print(f"\n📅 最新日期資料完整性:")
        
        cursor.execute("SELECT MAX(date) FROM stock_daily_data")
        latest_date = cursor.fetchone()[0]
        print(f"   最新日期: {latest_date}")
        
        cursor.execute(f"""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE date = '{latest_date}'
        """)
        latest_total = cursor.fetchone()[0]
        
        cursor.execute(f"""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE date = '{latest_date}'
        AND stock_name NOT LIKE 'ETF%' 
        AND stock_name NOT LIKE '股票%'
        AND stock_name IS NOT NULL 
        AND stock_name != ''
        """)
        latest_proper = cursor.fetchone()[0]
        
        latest_proper_rate = latest_proper / latest_total * 100 if latest_total > 0 else 0
        print(f"   最新日期正確名稱: {latest_proper}/{latest_total} ({latest_proper_rate:.1f}%)")
        
        conn.close()
        
        # 9. 總結
        print(f"\n📊 總結:")
        total_unknown = etf_default + stock_default + null_names
        unknown_rate = total_unknown / total_stocks * 100
        
        if unknown_rate < 5:
            print(f"   🎉 優秀！未知名稱僅 {total_unknown} 檔 ({unknown_rate:.1f}%)")
        elif unknown_rate < 15:
            print(f"   ✅ 良好！未知名稱 {total_unknown} 檔 ({unknown_rate:.1f}%)")
        elif unknown_rate < 25:
            print(f"   ⚠️ 尚可！未知名稱 {total_unknown} 檔 ({unknown_rate:.1f}%)")
        else:
            print(f"   ❌ 需改善！未知名稱 {total_unknown} 檔 ({unknown_rate:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_sample_unknown_stocks():
    """獲取未知股票的樣本，用於進一步分析"""
    
    print(f"\n🔍 獲取未知股票樣本進行分析")
    print("=" * 60)
    
    try:
        price_db = 'D:/Finlab/history/tables/price.db'
        conn = sqlite3.connect(price_db)
        
        # 獲取各類型的未知股票樣本
        samples = {}
        
        # ETF 預設名稱樣本
        cursor = conn.cursor()
        cursor.execute("""
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name LIKE 'ETF%'
        ORDER BY stock_id
        LIMIT 10
        """)
        samples['ETF預設'] = cursor.fetchall()
        
        # 股票預設名稱樣本
        cursor.execute("""
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name LIKE '股票%'
        ORDER BY stock_id
        LIMIT 10
        """)
        samples['股票預設'] = cursor.fetchall()
        
        # 空值樣本
        cursor.execute("""
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name IS NULL OR stock_name = ''
        ORDER BY stock_id
        LIMIT 5
        """)
        samples['空值'] = cursor.fetchall()
        
        conn.close()
        
        # 顯示樣本
        for category, stock_list in samples.items():
            if stock_list:
                print(f"\n📋 {category}樣本:")
                for stock_id, stock_name in stock_list:
                    print(f"   {stock_id}: '{stock_name}'")
        
        return samples
        
    except Exception as e:
        print(f"❌ 獲取樣本失敗: {e}")
        return {}

def main():
    """主函數"""
    
    print("🔍 price.db 股票名稱最終確認工具")
    
    # 詳細分析
    if not analyze_stock_names_in_detail():
        return
    
    # 獲取樣本
    samples = get_sample_unknown_stocks()
    
    print(f"\n💡 建議:")
    print(f"   1. 如果未知名稱比例 < 20%，可以接受")
    print(f"   2. 主要的 ETF 和常見股票都已正確命名")
    print(f"   3. 剩餘未知的多為權證、特殊商品等")
    print(f"   4. 對策略分析的影響很小")

if __name__ == "__main__":
    main()
