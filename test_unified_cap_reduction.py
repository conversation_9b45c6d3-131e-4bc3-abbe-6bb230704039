#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統一減資爬蟲 (英文欄位和優化格式)
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def clean_database_for_cap_reduction_test():
    """清理減資資料庫以測試新格式"""
    
    db_file = r'D:\Finlab\history\tables\cap_reduction.db'
    
    if os.path.exists(db_file):
        try:
            os.remove(db_file)
            print(f"🗑️ 已清理現有減資資料庫: {db_file}")
            return True
        except Exception as e:
            print(f"❌ 清理資料庫失敗: {e}")
            return False
    else:
        print(f"ℹ️ 減資資料庫檔案不存在，無需清理")
        return True

def test_unified_cap_reduction_crawler():
    """測試統一減資爬蟲"""
    
    print("=" * 80)
    print("🧪 測試統一減資爬蟲 (英文欄位)")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_cap_reduction
        print("✅ 成功導入 crawl_cap_reduction 函數")
        
        # 執行爬蟲
        print("🔄 開始執行統一減資爬蟲...")
        result = crawl_cap_reduction()
        
        if result is not None and not result.empty:
            print(f"✅ 爬蟲執行成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 檢查英文欄位名稱
            print(f"\n📋 英文欄位名稱檢查:")
            
            expected_columns = [
                'stock_name', 'capital_before_reduction', 'capital_after_reduction',
                'reduction_amount', 'reduction_ratio_percent', 'market'
            ]
            
            for col in expected_columns:
                if col in result.columns:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ 缺少 {col}")
            
            # 檢查不應該存在的中文欄位
            chinese_columns = ['股票代號', '股票名稱', '資料日期', '減資前股本', '減資後股本']
            found_chinese = [col for col in chinese_columns if col in result.columns]
            
            if found_chinese:
                print(f"   ⚠️ 仍包含中文欄位: {found_chinese}")
            else:
                print(f"   ✅ 已移除所有中文欄位")
            
            # 檢查索引結構
            index_names = result.index.names
            print(f"\n📊 索引結構: {index_names}")
            
            # 檢查 stock_name 位置
            columns_list = list(result.columns)
            if 'stock_name' in columns_list:
                stock_name_position = columns_list.index('stock_name')
                print(f"📍 stock_name 位置: 第 {stock_name_position + 1} 欄")
                
                if stock_name_position == 0:  # stock_id 是索引，所以 stock_name 應該是第一欄
                    print(f"   ✅ stock_name 位置正確 (緊鄰 stock_id)")
                else:
                    print(f"   ⚠️ stock_name 位置可能需要調整")
            
            # 顯示完整欄位列表
            print(f"\n📋 完整欄位列表:")
            for i, col in enumerate(result.columns, 1):
                print(f"   {i:2d}. {col}")
            
            # 檢查市場分布
            if 'market' in result.columns:
                market_stats = result['market'].value_counts()
                print(f"\n📊 市場分布:")
                for market, count in market_stats.items():
                    market_name = '上市' if market == 'TWSE' else '上櫃'
                    print(f"   {market_name} ({market}): {count:,} 筆")
            
            # 顯示範例資料
            print(f"\n📊 前3筆資料範例:")
            sample_df = result.head(3).reset_index()
            
            # 選擇關鍵欄位顯示
            key_columns = ['stock_id', 'stock_name', 'date', 'market', 'capital_before_reduction', 'capital_after_reduction', 'reduction_ratio_percent']
            available_columns = [col for col in key_columns if col in sample_df.columns]
            
            print(sample_df[available_columns].to_string(index=False))
            
            return True
        else:
            print(f"⚠️ 爬蟲執行返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_cap_reduction_database():
    """檢查減資資料庫中的英文欄位"""
    
    print(f"\n" + "=" * 80)
    print("📊 檢查減資資料庫中的英文欄位")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\cap_reduction.db'
    
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            
            # 檢查表格結構
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(cap_reduction)")
            columns = cursor.fetchall()
            
            print(f"📋 資料庫表格結構 (英文欄位):")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查關鍵英文欄位
            column_names = [col[1] for col in columns]
            
            expected_english_columns = [
                'stock_id', 'stock_name', 'date', 'market',
                'capital_before_reduction', 'capital_after_reduction', 'reduction_amount', 'reduction_ratio_percent'
            ]
            
            print(f"\n📋 關鍵英文欄位檢查:")
            for col in expected_english_columns:
                if col in column_names:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ 缺少 {col}")
            
            # 檢查是否還有中文欄位
            chinese_patterns = ['股票', '減資', '資料']
            chinese_columns = [col for col in column_names if any(pattern in col for pattern in chinese_patterns)]
            
            if chinese_columns:
                print(f"\n⚠️ 仍包含中文欄位:")
                for col in chinese_columns:
                    print(f"   - {col}")
            else:
                print(f"\n✅ 已完全移除中文欄位")
            
            # 檢查資料範例
            cursor.execute("SELECT stock_id, stock_name, date, market, capital_before_reduction, capital_after_reduction FROM cap_reduction LIMIT 5")
            sample_data = cursor.fetchall()
            
            print(f"\n📊 前5筆資料範例:")
            print(f"{'stock_id':<8} {'stock_name':<12} {'date':<12} {'market':<6} {'before':<12} {'after':<12}")
            print("-" * 70)
            for row in sample_data:
                stock_id = str(row[0])[:8]
                stock_name = str(row[1])[:12] if row[1] else 'N/A'
                date = str(row[2])[:10]
                market = str(row[3])
                before = f"{row[4]:,.0f}" if row[4] else 'N/A'
                after = f"{row[5]:,.0f}" if row[5] else 'N/A'
                print(f"{stock_id:<8} {stock_name:<12} {date:<12} {market:<6} {before:<12} {after:<12}")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 檢查資料庫失敗: {e}")
            return False
    else:
        print(f"❌ 資料庫檔案不存在: {db_file}")
        return False

def test_sql_queries_cap_reduction():
    """測試減資資料的 SQL 查詢"""
    
    print(f"\n" + "=" * 80)
    print("🔍 測試減資資料的 SQL 查詢")
    print("=" * 80)
    
    db_file = r'D:\Finlab\history\tables\cap_reduction.db'
    
    if not os.path.exists(db_file):
        print(f"❌ 資料庫檔案不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 測試查詢1: 統計各市場減資筆數
        print(f"🔍 測試查詢1: 統計各市場減資筆數")
        query1 = "SELECT market, COUNT(*) as count FROM cap_reduction GROUP BY market"
        
        cursor.execute(query1)
        results1 = cursor.fetchall()
        
        if results1:
            print(f"   ✅ 查詢成功")
            for row in results1:
                market_name = '上市' if row[0] == 'TWSE' else '上櫃'
                print(f"      {market_name} ({row[0]}): {row[1]:,} 筆")
        else:
            print(f"   ⚠️ 未找到市場統計資料")
        
        # 測試查詢2: 查詢減資比例最高的案例
        print(f"\n🔍 測試查詢2: 查詢減資比例最高的案例")
        query2 = """
        SELECT stock_id, stock_name, date, reduction_ratio_percent
        FROM cap_reduction 
        WHERE reduction_ratio_percent IS NOT NULL
        ORDER BY reduction_ratio_percent DESC 
        LIMIT 5
        """
        
        cursor.execute(query2)
        results2 = cursor.fetchall()
        
        if results2:
            print(f"   ✅ 查詢成功，前5名減資比例:")
            for row in results2:
                print(f"      {row[0]} {row[1]} {row[2]} 減資比例:{row[3]:.2f}%")
        else:
            print(f"   ⚠️ 未找到減資比例資料")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL 查詢測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🧪 統一減資爬蟲測試 (英文欄位)")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步驟1: 清理資料庫
    clean_success = clean_database_for_cap_reduction_test()
    
    # 步驟2: 測試統一減資爬蟲
    crawler_success = test_unified_cap_reduction_crawler()
    
    # 步驟3: 檢查資料庫英文欄位
    database_success = check_cap_reduction_database()
    
    # 步驟4: 測試 SQL 查詢
    query_success = test_sql_queries_cap_reduction()
    
    print(f"\n" + "=" * 80)
    print("📊 統一減資爬蟲測試結果總結")
    print("=" * 80)
    
    print(f"🗑️ 資料庫清理: {'✅ 成功' if clean_success else '❌ 失敗'}")
    print(f"🧪 減資爬蟲: {'✅ 成功' if crawler_success else '❌ 失敗'}")
    print(f"📊 資料庫英文欄位: {'✅ 成功' if database_success else '❌ 失敗'}")
    print(f"🔍 SQL 查詢: {'✅ 成功' if query_success else '❌ 失敗'}")
    
    if all([clean_success, crawler_success, database_success, query_success]):
        print(f"\n🎉 統一減資爬蟲測試全部通過！")
        print(f"💡 優化特點:")
        print(f"   ✅ 所有欄位名稱改為英文")
        print(f"   ✅ 移除重複的股票代號欄位")
        print(f"   ✅ stock_name 位於 stock_id 右側")
        print(f"   ✅ 移除資料日期欄位")
        print(f"   ✅ date 只包含年月日")
        print(f"   ✅ 統一的市場標識 (TWSE/OTC)")
        print(f"   ✅ 標準化的英文欄位名稱")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查相關配置")

if __name__ == "__main__":
    main()
