#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息資料清理和去重功能
"""

import sqlite3
import os
from datetime import datetime

def test_database_cleanup():
    """測試資料庫清理功能"""
    print("🔍 測試除權息資料庫清理...")
    
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    if not os.path.exists(db_path):
        print("❌ 資料庫不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查總記錄數
        cursor.execute("SELECT COUNT(*) FROM dividend_data")
        total_count = cursor.fetchone()[0]
        print(f"📊 總記錄數: {total_count}")
        
        # 檢查sample_data記錄數
        cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE data_source = 'sample_data'")
        sample_count = cursor.fetchone()[0]
        print(f"🧪 sample_data記錄數: {sample_count}")
        
        # 檢查真實資料記錄數
        cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE data_source IS NULL OR data_source != 'sample_data'")
        real_count = cursor.fetchone()[0]
        print(f"✅ 真實資料記錄數: {real_count}")
        
        # 檢查重複記錄
        cursor.execute("""
            SELECT stock_code, stock_name, ex_dividend_date, COUNT(*) as count
            FROM dividend_data 
            WHERE (data_source IS NULL OR data_source != 'sample_data')
            AND ex_dividend_date IS NOT NULL
            GROUP BY stock_code, ex_dividend_date
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 10
        """)
        
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"\n⚠️ 發現 {len(duplicates)} 組重複記錄:")
            for dup in duplicates:
                print(f"  {dup[0]} {dup[1]} {dup[2]} - 重複 {dup[3]} 次")
        else:
            print("\n✅ 未發現重複記錄")
        
        # 測試時間範圍查詢（排除sample_data）
        print("\n🔍 測試時間範圍查詢（2025-07-15 至 2025-07-31）...")
        cursor.execute("""
            SELECT DISTINCT stock_code, stock_name, ex_dividend_date, cash_dividend, data_source
            FROM dividend_data
            WHERE ex_dividend_date IS NOT NULL
            AND ex_dividend_date != ''
            AND ex_dividend_date >= '2025-07-15'
            AND ex_dividend_date <= '2025-07-31'
            AND (data_source IS NULL OR data_source != 'sample_data')
            ORDER BY ex_dividend_date, stock_code
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        print(f"📈 查詢結果數量: {len(results)}")
        
        if results:
            print("📋 查詢結果範例:")
            for i, row in enumerate(results[:5]):
                stock_code, stock_name, ex_date, cash_div, source = row
                print(f"  {i+1}. {stock_code} {stock_name} - {ex_date} - 股利: {cash_div} - 來源: {source or 'N/A'}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_stock_query():
    """測試股票查詢功能"""
    print("\n🔍 測試股票查詢功能...")
    
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試查詢2330台積電的資料
        stock_code = "2330"
        year = "2025"
        
        cursor.execute("""
            SELECT DISTINCT stock_code, stock_name, year, ex_dividend_date,
                   cash_dividend, stock_dividend, total_dividend,
                   dividend_yield, eps, data_source
            FROM dividend_data
            WHERE stock_code = ? AND year = ?
            AND (data_source IS NULL OR data_source != 'sample_data')
            ORDER BY ex_dividend_date
        """, (stock_code, year))
        
        results = cursor.fetchall()
        print(f"📊 {stock_code} ({year}年) 查詢結果: {len(results)} 筆")
        
        if results:
            for row in results:
                print(f"  股票: {row[0]} {row[1]}")
                print(f"  除權息日: {row[3]}")
                print(f"  現金股利: {row[4]}")
                print(f"  資料來源: {row[9] or 'N/A'}")
                print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 股票查詢測試失敗: {e}")

def test_stock_list_loading():
    """測試股票清單載入"""
    print("\n🔍 測試股票清單載入...")
    
    db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT stock_code, stock_name 
            FROM dividend_data 
            WHERE (data_source IS NULL OR data_source != 'sample_data')
            AND stock_name IS NOT NULL
            ORDER BY stock_code
            LIMIT 20
        """)
        
        stocks = cursor.fetchall()
        print(f"📋 可用股票數量: {len(stocks)}")
        
        if stocks:
            print("股票清單範例:")
            for i, (code, name) in enumerate(stocks[:10]):
                print(f"  {i+1}. {code} {name}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 股票清單測試失敗: {e}")

def main():
    """主測試函數"""
    print("🚀 開始測試除權息資料清理功能...")
    print("=" * 60)
    
    test_database_cleanup()
    test_stock_query()
    test_stock_list_loading()
    
    print("\n🎉 測試完成!")
    print("\n修正摘要:")
    print("1. ✅ 所有查詢都排除 sample_data")
    print("2. ✅ 使用 DISTINCT 去除重複記錄")
    print("3. ✅ 時間範圍查詢設為預設且計算未來日期")
    print("4. ✅ 查詢結果包含股票代碼和股票名稱")

if __name__ == "__main__":
    main()
