#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試三大法人買賣狀況簡化顯示效果
只顯示買賣超資訊，移除買入和賣出詳細數據
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI

def test_simplified_institutional_display():
    """測試簡化的三大法人顯示"""
    print("🧪 測試三大法人買賣狀況簡化顯示")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    print("✅ 主視窗創建成功")
    print("\n📋 測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 在左側或右側股票列表中右鍵點擊股票")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查三大法人買賣狀況區塊")
    print("\n🎯 預期效果:")
    print("• 只顯示買賣超資訊")
    print("• 不顯示買入、賣出詳細數據")
    print("• 區塊高度更緊湊 (120px)")
    print("• 資訊更清晰易讀")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    test_simplified_institutional_display()
