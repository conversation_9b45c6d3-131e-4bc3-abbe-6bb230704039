#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用模擬數據測試日內策略篩選邏輯
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入策略
from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def generate_mock_stock_data(stock_id, base_price=50, trend=0, volatility=0.02, volume_base=1000000):
    """生成模擬股票數據"""
    dates = pd.date_range(end=datetime.now(), periods=20, freq='D')
    
    # 生成價格數據
    prices = []
    current_price = base_price
    
    for i in range(len(dates)):
        # 添加趨勢和隨機波動
        daily_change = trend/100 + np.random.normal(0, volatility)
        current_price = current_price * (1 + daily_change)
        prices.append(current_price)
    
    # 生成OHLC數據
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = volume_base * (1 + np.random.normal(0, 0.3))
        
        data.append({
            'Date': date,
            'Open': max(open_price, 0.1),
            'High': max(high, open_price, close),
            'Low': min(low, open_price, close),
            'Close': max(close, 0.1),
            'Volume': max(int(volume), 1000)
        })
    
    return pd.DataFrame(data)

def test_simulation_intraday():
    """使用模擬數據測試日內策略"""
    print("🧪 使用模擬數據測試日內策略篩選邏輯")
    print("=" * 60)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 創建不同類型的模擬股票
    test_scenarios = [
        {
            'stock_id': '2330',
            'name': '強勢高價股',
            'base_price': 580,
            'trend': 2.0,  # 上漲趨勢
            'volatility': 0.015,
            'volume_base': 50000000
        },
        {
            'stock_id': '2317', 
            'name': '穩定中價股',
            'base_price': 110,
            'trend': 0.5,  # 微漲
            'volatility': 0.02,
            'volume_base': 30000000
        },
        {
            'stock_id': '2454',
            'name': '波動高價股',
            'base_price': 1200,
            'trend': -1.0,  # 下跌趨勢
            'volatility': 0.03,
            'volume_base': 10000000
        },
        {
            'stock_id': '1301',
            'name': '平穩中價股',
            'base_price': 85,
            'trend': 0.0,  # 橫盤
            'volatility': 0.015,
            'volume_base': 20000000
        },
        {
            'stock_id': '2303',
            'name': '活躍低價股',
            'base_price': 45,
            'trend': 3.0,  # 強勢上漲
            'volatility': 0.025,
            'volume_base': 80000000
        },
        {
            'stock_id': '2002',
            'name': '弱勢低價股',
            'base_price': 25,
            'trend': -2.0,  # 下跌
            'volatility': 0.02,
            'volume_base': 15000000
        },
        {
            'stock_id': '3008',
            'name': '超高價股',
            'base_price': 2500,
            'trend': 1.0,  # 溫和上漲
            'volatility': 0.02,
            'volume_base': 5000000
        },
        {
            'stock_id': '9999',
            'name': '極低價股',
            'base_price': 8,
            'trend': 0.5,
            'volatility': 0.04,
            'volume_base': 5000000
        }
    ]
    
    qualified_stocks = []
    failed_stocks = []
    
    for scenario in test_scenarios:
        print(f"\n📊 測試 {scenario['stock_id']} - {scenario['name']}")
        print("-" * 40)
        
        try:
            # 生成模擬數據
            mock_data = generate_mock_stock_data(
                scenario['stock_id'],
                scenario['base_price'],
                scenario['trend'],
                scenario['volatility'],
                scenario['volume_base']
            )
            
            # 計算指標
            mock_data = strategy.calculate_indicators(mock_data)
            
            # 檢查盤前條件
            pre_market_ok, reason = strategy.check_pre_market_conditions(mock_data)
            
            # 顯示關鍵數據
            latest = mock_data.iloc[-1]
            prev_close = mock_data.iloc[-2]['Close']
            ma5 = latest['MA5']
            volume = latest['Volume']
            avg_vol = latest['AvgVol']
            
            print(f"  💰 收盤價: {prev_close:.2f}")
            print(f"  📊 MA5: {ma5:.2f}")
            print(f"  📈 MA5比率: {prev_close/ma5:.3f}")
            print(f"  📊 成交量: {volume:,.0f}")
            print(f"  📈 成交量比率: {volume/avg_vol:.2f}")
            print(f"  🎯 篩選結果: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
            print(f"  📝 詳細評分: {reason}")
            
            if pre_market_ok:
                qualified_stocks.append({
                    'stock_id': scenario['stock_id'],
                    'name': scenario['name'],
                    'close_price': prev_close,
                    'ma5_ratio': prev_close/ma5,
                    'vol_ratio': volume/avg_vol,
                    'reason': reason
                })
            else:
                failed_stocks.append({
                    'stock_id': scenario['stock_id'],
                    'name': scenario['name'],
                    'close_price': prev_close,
                    'reason': reason
                })
                
        except Exception as e:
            print(f"  ❌ 處理失敗: {e}")
            failed_stocks.append({
                'stock_id': scenario['stock_id'],
                'name': scenario['name'],
                'close_price': 0,
                'reason': f'處理失敗: {str(e)}'
            })
    
    # 統計結果
    print("\n" + "=" * 60)
    print("📊 模擬測試結果統計")
    print("=" * 60)
    
    total_tested = len(test_scenarios)
    qualified_count = len(qualified_stocks)
    failed_count = len(failed_stocks)
    
    print(f"總測試情境數: {total_tested}")
    print(f"通過篩選: {qualified_count} ({qualified_count/total_tested*100:.1f}%)")
    print(f"未通過篩選: {failed_count} ({failed_count/total_tested*100:.1f}%)")
    
    if qualified_stocks:
        print(f"\n✅ 通過篩選的股票:")
        for stock in qualified_stocks:
            print(f"  • {stock['stock_id']} ({stock['name']}): {stock['close_price']:.2f}")
            print(f"    MA5比率: {stock['ma5_ratio']:.3f}, 成交量比率: {stock['vol_ratio']:.2f}")
            print(f"    評分詳情: {stock['reason']}")
    
    if failed_stocks:
        print(f"\n❌ 未通過篩選的股票:")
        for stock in failed_stocks:
            print(f"  • {stock['stock_id']} ({stock['name']}): {stock['reason']}")
    
    # 評估篩選效果
    print(f"\n🎯 篩選效果評估:")
    
    if qualified_count == 0:
        print("  ⚠️ 沒有股票通過篩選，篩選條件可能過於嚴格")
        print("  💡 建議: 進一步降低評分門檻或放寬個別條件")
    elif qualified_count <= 2:
        print("  ⚠️ 通過股票數量較少，篩選較為嚴格")
        print("  💡 建議: 適度放寬條件以增加候選股票")
    elif qualified_count <= 4:
        print("  ✅ 篩選效果良好，通過數量適中")
        print("  💡 建議: 當前設定合理，可用於實際交易")
    else:
        print("  ⚠️ 通過股票數量較多，篩選可能過於寬鬆")
        print("  💡 建議: 適度提高標準以提升選股品質")
    
    return qualified_stocks, failed_stocks

def analyze_threshold_sensitivity():
    """分析評分門檻的敏感性"""
    print(f"\n🔬 評分門檻敏感性分析")
    print("-" * 40)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 生成一組測試數據
    test_data = generate_mock_stock_data('TEST', 100, 1.0, 0.02, 20000000)
    test_data = strategy.calculate_indicators(test_data)
    
    # 測試不同的評分門檻
    thresholds = [1, 2, 3, 4, 5, 6]
    
    print("評分門檻對通過率的影響:")
    for threshold in thresholds:
        # 模擬修改門檻
        original_check = strategy.check_pre_market_conditions
        
        def modified_check(daily_df):
            passed, reason = original_check(daily_df)
            # 從reason中提取分數
            if '[評分:' in reason:
                score_part = reason.split('[評分:')[1].split('/')[0].strip()
                try:
                    score = int(score_part)
                    new_passed = score >= threshold
                    return new_passed, reason.replace(f'[評分: {score}/12]', f'[評分: {score}/12, 門檻: {threshold}]')
                except:
                    return passed, reason
            return passed, reason
        
        strategy.check_pre_market_conditions = modified_check
        passed, reason = strategy.check_pre_market_conditions(test_data)
        
        print(f"  門檻 {threshold}: {'✅ 通過' if passed else '❌ 未通過'}")
        
        # 恢復原始方法
        strategy.check_pre_market_conditions = original_check

if __name__ == "__main__":
    try:
        # 執行模擬測試
        qualified, failed = test_simulation_intraday()
        
        # 分析門檻敏感性
        analyze_threshold_sensitivity()
        
        print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("💡 提示: 此測試使用模擬數據，實際效果可能因市場條件而異")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
