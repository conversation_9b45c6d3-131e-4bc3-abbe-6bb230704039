#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GoodInfo Excel 月營收數據下載器
基於實際的 Excel 匯出功能開發
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import random
from datetime import datetime
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import tempfile
import glob

class GoodInfoExcelDownloader:
    """GoodInfo Excel 月營收數據下載器"""
    
    def __init__(self, db_path="db/monthly_revenue.db", download_dir=None):
        self.db_path = db_path
        self.download_dir = download_dir or tempfile.mkdtemp()
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化數據庫
        self.init_database()
        
    def init_database(self):
        """初始化數據庫"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,                    -- 單月營收(億)
                    revenue_mom REAL,               -- 月增率(%)
                    revenue_yoy REAL,               -- 年增率(%)
                    cumulative_revenue REAL,        -- 累計營收(億)
                    cumulative_yoy REAL,            -- 累計年增率(%)
                    
                    -- 股價資訊
                    open_price REAL,                -- 開盤價
                    close_price REAL,               -- 收盤價
                    high_price REAL,                -- 最高價
                    low_price REAL,                 -- 最低價
                    price_change REAL,              -- 漲跌(元)
                    price_change_pct REAL,          -- 漲跌(%)
                    
                    data_source TEXT DEFAULT 'goodinfo_excel',
                    crawl_date TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_year_month ON monthly_revenue(stock_id, year, month)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 數據庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
    
    def setup_chrome_driver(self):
        """設置 Chrome 瀏覽器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 無頭模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 設置下載目錄
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            driver = webdriver.Chrome(options=chrome_options)
            return driver
            
        except Exception as e:
            self.logger.error(f"❌ Chrome 瀏覽器設置失敗: {e}")
            return None
    
    def download_revenue_excel(self, stock_id):
        """下載單一股票的月營收 Excel 文件"""
        driver = None
        try:
            self.logger.info(f"🚀 開始下載 {stock_id} 月營收 Excel...")
            
            # 設置瀏覽器
            driver = self.setup_chrome_driver()
            if not driver:
                return None
            
            # 構建URL
            url = f"https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID={stock_id}"
            self.logger.info(f"📱 訪問頁面: {url}")
            
            driver.get(url)
            
            # 等待頁面載入
            wait = WebDriverWait(driver, 30)
            
            # 等待表格載入
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            time.sleep(3)  # 額外等待確保頁面完全載入
            
            # 尋找匯出按鈕
            export_button = self.find_export_button(driver)
            if not export_button:
                self.logger.warning("⚠️ 未找到匯出按鈕")
                return None
            
            # 點擊匯出按鈕
            self.logger.info("📥 點擊匯出按鈕...")
            driver.execute_script("arguments[0].click();", export_button)
            
            # 等待下載完成
            downloaded_file = self.wait_for_download(stock_id)
            
            if downloaded_file:
                self.logger.info(f"✅ Excel 下載成功: {downloaded_file}")
                return downloaded_file
            else:
                self.logger.warning("⚠️ Excel 下載失敗")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 下載 {stock_id} Excel 失敗: {e}")
            return None
        
        finally:
            if driver:
                driver.quit()
    
    def find_export_button(self, driver):
        """尋找匯出按鈕"""
        try:
            # 可能的匯出按鈕選擇器
            selectors = [
                "//input[@value='XLS']",
                "//button[contains(text(), 'XLS')]",
                "//a[contains(text(), 'XLS')]",
                "//input[@type='button'][contains(@value, 'XLS')]",
                "//input[@type='submit'][contains(@value, 'XLS')]"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        self.logger.info(f"✅ 找到匯出按鈕: {selector}")
                        return elements[0]
                except:
                    continue
            
            # 如果找不到，嘗試通過文字內容尋找
            try:
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                for input_elem in all_inputs:
                    if input_elem.get_attribute("value") and "XLS" in input_elem.get_attribute("value").upper():
                        self.logger.info("✅ 通過文字內容找到匯出按鈕")
                        return input_elem
            except:
                pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 尋找匯出按鈕失敗: {e}")
            return None
    
    def wait_for_download(self, stock_id, timeout=30):
        """等待下載完成"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # 尋找下載的文件
                excel_files = glob.glob(os.path.join(self.download_dir, "*.xls*"))
                
                if excel_files:
                    # 找到最新的文件
                    latest_file = max(excel_files, key=os.path.getctime)
                    
                    # 檢查文件是否下載完成（不是臨時文件）
                    if not latest_file.endswith('.crdownload') and os.path.getsize(latest_file) > 0:
                        # 重命名文件
                        new_filename = f"{stock_id}_monthly_revenue_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xls"
                        new_path = os.path.join(self.download_dir, new_filename)
                        
                        try:
                            os.rename(latest_file, new_path)
                            return new_path
                        except:
                            return latest_file
                
                time.sleep(1)
            
            self.logger.warning(f"⚠️ 下載超時 ({timeout}秒)")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 等待下載失敗: {e}")
            return None
    
    def parse_excel_file(self, excel_file, stock_id):
        """解析 Excel 文件"""
        try:
            self.logger.info(f"📊 解析 Excel 文件: {excel_file}")
            
            # 讀取 Excel 文件
            df = pd.read_excel(excel_file, header=2)  # 跳過前兩行標題
            
            # 獲取股票名稱
            stock_name = self.get_stock_name(stock_id)
            
            revenue_records = []
            
            for index, row in df.iterrows():
                try:
                    # 解析月別
                    month_str = str(row.iloc[0]).strip()
                    if pd.isna(month_str) or month_str == 'nan':
                        continue
                    
                    year, month = self.parse_month_string(month_str)
                    if not year or not month:
                        continue
                    
                    # 解析數據
                    record = {
                        'stock_id': stock_id,
                        'stock_name': stock_name,
                        'year': year,
                        'month': month,
                        
                        # 股價資訊
                        'open_price': self.safe_float(row.iloc[1]),      # 開盤
                        'close_price': self.safe_float(row.iloc[2]),     # 收盤
                        'high_price': self.safe_float(row.iloc[3]),      # 最高
                        'low_price': self.safe_float(row.iloc[4]),       # 最低
                        'price_change': self.safe_float(row.iloc[5]),    # 漲跌(元)
                        'price_change_pct': self.safe_float(row.iloc[6]), # 漲跌(%)
                        
                        # 營收資訊 (營業收入欄位)
                        'revenue': self.safe_float(row.iloc[7]),         # 單月營收(億)
                        'revenue_mom': self.safe_float(row.iloc[8]),     # 月增(%)
                        'revenue_yoy': self.safe_float(row.iloc[9]),     # 年增(%)
                        'cumulative_revenue': self.safe_float(row.iloc[10]), # 累計營收(億)
                        'cumulative_yoy': self.safe_float(row.iloc[11]), # 累計年增(%)
                        
                        'crawl_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    revenue_records.append(record)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ 解析第 {index} 行失敗: {e}")
                    continue
            
            self.logger.info(f"✅ 成功解析 {len(revenue_records)} 筆月營收數據")
            return revenue_records
            
        except Exception as e:
            self.logger.error(f"❌ 解析 Excel 文件失敗: {e}")
            return None
    
    def parse_month_string(self, month_str):
        """解析月份字符串"""
        try:
            # 處理 "Jun-25" 格式
            if '-' in month_str:
                month_part, year_part = month_str.split('-')
                
                # 月份對照表
                month_mapping = {
                    'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                    'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
                }
                
                month = month_mapping.get(month_part)
                year = int(year_part) + 2000 if int(year_part) < 50 else int(year_part) + 1900
                
                return year, month
            
            return None, None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 解析月份失敗: {month_str}, {e}")
            return None, None
    
    def safe_float(self, value):
        """安全轉換為浮點數"""
        try:
            if pd.isna(value) or value == '' or str(value).strip() == '':
                return None
            
            # 移除逗號和其他字符
            cleaned = str(value).replace(',', '').strip()
            return float(cleaned)
            
        except (ValueError, TypeError):
            return None
    
    def get_stock_name(self, stock_id):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科',
            '2412': '中華電', '2881': '富邦金', '2882': '國泰金'
        }
        return stock_names.get(stock_id, f'股票{stock_id}')
    
    def save_to_database(self, revenue_data):
        """儲存數據到數據庫"""
        try:
            if not revenue_data:
                return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            saved_count = 0
            for record in revenue_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue 
                        (stock_id, stock_name, year, month, revenue, revenue_mom, 
                         revenue_yoy, cumulative_revenue, cumulative_yoy,
                         open_price, close_price, high_price, low_price, 
                         price_change, price_change_pct, crawl_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_id'], record['stock_name'], record['year'], 
                        record['month'], record['revenue'], record['revenue_mom'],
                        record['revenue_yoy'], record['cumulative_revenue'], 
                        record['cumulative_yoy'], record['open_price'],
                        record['close_price'], record['high_price'], record['low_price'],
                        record['price_change'], record['price_change_pct'], record['crawl_date']
                    ))
                    saved_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 儲存記錄失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 成功儲存 {saved_count} 筆數據到數據庫")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 儲存數據庫失敗: {e}")
            return False
    
    def download_and_process(self, stock_id):
        """下載並處理單一股票數據"""
        try:
            # 下載 Excel 文件
            excel_file = self.download_revenue_excel(stock_id)
            if not excel_file:
                return None
            
            # 解析 Excel 文件
            revenue_data = self.parse_excel_file(excel_file, stock_id)
            if not revenue_data:
                return None
            
            # 儲存到數據庫
            if self.save_to_database(revenue_data):
                # 清理下載的文件
                try:
                    os.remove(excel_file)
                except:
                    pass
                
                return revenue_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 處理 {stock_id} 失敗: {e}")
            return None

def test_excel_downloader():
    """測試 Excel 下載器"""
    print("🚀 測試 GoodInfo Excel 下載器")
    print("=" * 60)
    
    downloader = GoodInfoExcelDownloader()
    
    # 測試台積電
    test_stock = '2330'
    print(f"\n📊 測試下載 {test_stock} 月營收數據...")
    
    revenue_data = downloader.download_and_process(test_stock)
    
    if revenue_data:
        print(f"✅ 成功處理 {len(revenue_data)} 筆月營收數據")
        
        # 顯示前5筆數據
        print("\n📋 前5筆數據:")
        for i, record in enumerate(revenue_data[:5], 1):
            print(f"{i}. {record['year']}/{record['month']:02d} - "
                  f"營收: {record['revenue']:.0f}億, "
                  f"年增率: {record['revenue_yoy']:.1f}%")
        
        return True
    else:
        print("❌ 處理失敗")
        return False

if __name__ == "__main__":
    test_excel_downloader()
