#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
融資融券查詢工具
獨立的融資融券資訊查詢介面
"""

import sys
import os
import requests
import sqlite3
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QDateEdit, QTableWidget, QTableWidgetItem, 
                            QGroupBox, QTextEdit, QMessageBox, QProgressBar,
                            QComboBox, QSplitter)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt6.QtGui import QFont

class MarginTradingFetcher(QThread):
    """融資融券資料獲取線程"""
    data_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, stock_code, target_date):
        super().__init__()
        self.stock_code = stock_code
        self.target_date = target_date
    
    def run(self):
        """執行資料獲取"""
        try:
            self.progress_updated.emit(20)
            
            # 轉換日期格式為民國年
            target_dt = datetime.strptime(self.target_date, '%Y-%m-%d')
            roc_year = target_dt.year - 1911
            date_str = f"{roc_year}{target_dt.month:02d}{target_dt.day:02d}"
            
            self.progress_updated.emit(40)
            
            # TWSE 融資融券 API
            url = "https://www.twse.com.tw/exchangeReport/MI_MARGN"
            params = {
                'date': date_str,
                'selectType': 'ALL',
                'response': 'json'
            }
            
            self.progress_updated.emit(60)
            
            response = requests.get(url, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                
                self.progress_updated.emit(80)
                
                if 'data' in data and data['data']:
                    # 查找指定股票的資料
                    for row in data['data']:
                        if len(row) >= 10 and row[0] == self.stock_code:
                            margin_info = {
                                '股票代碼': row[0],
                                '股票名稱': row[1],
                                '融資買進': row[2] if row[2] != '--' else '0',
                                '融資賣出': row[3] if row[3] != '--' else '0',
                                '融資現金償還': row[4] if row[4] != '--' else '0',
                                '融資前日餘額': row[5] if row[5] != '--' else '0',
                                '融資今日餘額': row[6] if row[6] != '--' else '0',
                                '融券買進': row[7] if row[7] != '--' else '0',
                                '融券賣出': row[8] if row[8] != '--' else '0',
                                '融券現券償還': row[9] if row[9] != '--' else '0',
                                '融券前日餘額': row[10] if len(row) > 10 and row[10] != '--' else '0',
                                '融券今日餘額': row[11] if len(row) > 11 and row[11] != '--' else '0',
                                '資料日期': self.target_date,
                                '查詢成功': True
                            }
                            self.progress_updated.emit(100)
                            self.data_ready.emit(margin_info)
                            return
                    
                    # 如果沒找到指定股票
                    self.error_occurred.emit(f"在 {self.target_date} 找不到股票 {self.stock_code} 的融資融券資料")
                else:
                    self.error_occurred.emit(f"{self.target_date} 無融資融券資料（可能是非交易日）")
            else:
                self.error_occurred.emit(f"API請求失敗，狀態碼: {response.status_code}")
                
        except Exception as e:
            self.error_occurred.emit(f"獲取資料失敗: {str(e)}")

class MarginTradingQueryTool(QMainWindow):
    """融資融券查詢工具主視窗"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.fetcher = None
        
    def init_ui(self):
        """初始化使用者介面"""
        self.setWindowTitle("💰 融資融券查詢工具 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        
        # 主要widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 主要布局
        main_layout = QVBoxLayout(main_widget)
        
        # 查詢區塊
        query_group = self.create_query_group()
        main_layout.addWidget(query_group)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 結果顯示區塊
        result_group = self.create_result_group()
        splitter.addWidget(result_group)
        
        # 歷史趨勢區塊
        trend_group = self.create_trend_group()
        splitter.addWidget(trend_group)
        
        # 設定分割比例
        splitter.setSizes([600, 400])
        
    def create_query_group(self):
        """創建查詢區塊"""
        group = QGroupBox("🔍 查詢條件")
        layout = QHBoxLayout()
        
        # 股票代碼
        layout.addWidget(QLabel("股票代碼:"))
        self.stock_code_edit = QLineEdit()
        self.stock_code_edit.setPlaceholderText("例如: 2330")
        self.stock_code_edit.setMaxLength(10)
        layout.addWidget(self.stock_code_edit)
        
        # 查詢日期
        layout.addWidget(QLabel("查詢日期:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addWidget(self.date_edit)
        
        # 查詢按鈕
        self.query_button = QPushButton("🚀 查詢")
        self.query_button.clicked.connect(self.query_margin_data)
        layout.addWidget(self.query_button)
        
        # 清除按鈕
        clear_button = QPushButton("🗑️ 清除")
        clear_button.clicked.connect(self.clear_results)
        layout.addWidget(clear_button)
        
        layout.addStretch()
        group.setLayout(layout)
        return group
    
    def create_result_group(self):
        """創建結果顯示區塊"""
        group = QGroupBox("📊 融資融券資訊")
        layout = QVBoxLayout()
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMinimumHeight(300)
        layout.addWidget(self.result_text)
        
        group.setLayout(layout)
        return group
    
    def create_trend_group(self):
        """創建趨勢分析區塊"""
        group = QGroupBox("📈 趨勢分析")
        layout = QVBoxLayout()
        
        # 趨勢表格
        self.trend_table = QTableWidget()
        self.trend_table.setColumnCount(4)
        self.trend_table.setHorizontalHeaderLabels(['日期', '融資餘額', '融券餘額', '變化'])
        layout.addWidget(self.trend_table)
        
        # 趨勢查詢按鈕
        trend_layout = QHBoxLayout()
        trend_layout.addWidget(QLabel("趨勢天數:"))
        
        self.trend_days = QComboBox()
        self.trend_days.addItems(['7天', '14天', '30天'])
        trend_layout.addWidget(self.trend_days)
        
        trend_button = QPushButton("📈 查詢趨勢")
        trend_button.clicked.connect(self.query_trend_data)
        trend_layout.addWidget(trend_button)
        
        trend_layout.addStretch()
        layout.addLayout(trend_layout)
        
        group.setLayout(layout)
        return group
    
    def query_margin_data(self):
        """查詢融資融券資料"""
        stock_code = self.stock_code_edit.text().strip()
        if not stock_code:
            QMessageBox.warning(self, "警告", "請輸入股票代碼")
            return
        
        target_date = self.date_edit.date().toString("yyyy-MM-dd")
        
        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.query_button.setEnabled(False)
        
        # 啟動獲取線程
        self.fetcher = MarginTradingFetcher(stock_code, target_date)
        self.fetcher.data_ready.connect(self.display_margin_data)
        self.fetcher.error_occurred.connect(self.handle_error)
        self.fetcher.progress_updated.connect(self.progress_bar.setValue)
        self.fetcher.finished.connect(self.query_finished)
        self.fetcher.start()
    
    def display_margin_data(self, margin_info):
        """顯示融資融券資料"""
        def format_number(value):
            """格式化數字"""
            try:
                num = int(str(value).replace(',', ''))
                return f"{num:,}"
            except:
                return value
        
        def calculate_change(today, yesterday):
            """計算變化"""
            try:
                today_num = int(str(today).replace(',', ''))
                yesterday_num = int(str(yesterday).replace(',', ''))
                change = today_num - yesterday_num
                if change > 0:
                    return f"<span style='color: #e74c3c;'>+{change:,}</span>"
                elif change < 0:
                    return f"<span style='color: #27ae60;'>{change:,}</span>"
                else:
                    return "<span style='color: #666666;'>持平</span>"
            except:
                return "N/A"
        
        # 計算變化
        margin_change = calculate_change(
            margin_info.get('融資今日餘額', '0'),
            margin_info.get('融資前日餘額', '0')
        )
        short_change = calculate_change(
            margin_info.get('融券今日餘額', '0'),
            margin_info.get('融券前日餘額', '0')
        )
        
        html_content = f"""
        <div style="font-family: 'Microsoft JhengHei'; padding: 10px;">
            <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                💰 {margin_info.get('股票代碼', 'N/A')} {margin_info.get('股票名稱', 'N/A')} 融資融券資訊
            </h2>
            
            <p style="color: #7f8c8d; margin: 10px 0;">
                📅 <strong>資料日期:</strong> {margin_info.get('資料日期', 'N/A')}
            </p>
            
            <div style="background-color: #fdf2f2; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #e74c3c;">
                <h3 style="color: #e74c3c; margin-top: 0;">📈 融資狀況</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>買進:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融資買進', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>賣出:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融資賣出', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>現金償還:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融資現金償還', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>前日餘額:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融資前日餘額', 'N/A'))} 股</td>
                    </tr>
                    <tr style="background-color: #fff;">
                        <td style="padding: 8px; font-weight: bold;"><strong>今日餘額:</strong></td>
                        <td style="padding: 8px; font-weight: bold;">{format_number(margin_info.get('融資今日餘額', 'N/A'))} 股 ({margin_change})</td>
                    </tr>
                </table>
            </div>
            
            <div style="background-color: #f2f8f2; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #27ae60;">
                <h3 style="color: #27ae60; margin-top: 0;">📉 融券狀況</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>買進:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融券買進', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>賣出:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融券賣出', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>現券償還:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融券現券償還', 'N/A'))} 股</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>前日餘額:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">{format_number(margin_info.get('融券前日餘額', 'N/A'))} 股</td>
                    </tr>
                    <tr style="background-color: #fff;">
                        <td style="padding: 8px; font-weight: bold;"><strong>今日餘額:</strong></td>
                        <td style="padding: 8px; font-weight: bold;">{format_number(margin_info.get('融券今日餘額', 'N/A'))} 股 ({short_change})</td>
                    </tr>
                </table>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px; color: #6c757d;">
                💡 <strong>說明:</strong> 融資餘額增加表示看多情緒升溫，融券餘額增加表示看空情緒升溫。
                數據來源：台灣證券交易所
            </div>
        </div>
        """
        
        self.result_text.setHtml(html_content)
    
    def handle_error(self, error_message):
        """處理錯誤"""
        self.result_text.setPlainText(f"❌ 查詢失敗: {error_message}")
        QMessageBox.warning(self, "查詢失敗", error_message)
    
    def query_finished(self):
        """查詢完成"""
        self.progress_bar.setVisible(False)
        self.query_button.setEnabled(True)
    
    def query_trend_data(self):
        """查詢趨勢資料"""
        stock_code = self.stock_code_edit.text().strip()
        if not stock_code:
            QMessageBox.warning(self, "警告", "請先輸入股票代碼")
            return
        
        # 這裡可以實現趨勢資料查詢邏輯
        QMessageBox.information(self, "功能開發中", "趨勢分析功能正在開發中，敬請期待！")
    
    def clear_results(self):
        """清除結果"""
        self.result_text.clear()
        self.trend_table.setRowCount(0)

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設定應用程式資訊
    app.setApplicationName("融資融券查詢工具")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = MarginTradingQueryTool()
    window.show()
    
    # 執行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
