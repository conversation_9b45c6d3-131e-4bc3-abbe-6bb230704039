#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試股票篩選功能
"""

import sqlite3
import pandas as pd
import sys
import os

def check_database():
    """檢查數據庫"""
    print("🔍 檢查數據庫...")
    
    db_path = "db/price.db"
    if not os.path.exists(db_path):
        print(f"❌ 數據庫文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ 找到 {len(tables)} 個表格: {[t[0] for t in tables]}")
        
        # 檢查股票數據
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stock_count = cursor.fetchone()[0]
        print(f"✅ 數據庫中有 {stock_count} 檔股票")
        
        # 檢查最新數據
        cursor.execute("SELECT MAX(date) FROM stock_daily_data")
        latest_date = cursor.fetchone()[0]
        print(f"✅ 最新數據日期: {latest_date}")
        
        # 檢查一檔股票的數據
        cursor.execute("SELECT stock_id, date, close, volume FROM stock_daily_data ORDER BY date DESC LIMIT 5")
        sample_data = cursor.fetchall()
        print(f"✅ 樣本數據:")
        for row in sample_data:
            print(f"   {row[0]} {row[1]} 收盤:{row[2]} 成交量:{row[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 數據庫檢查失敗: {e}")
        return False

def test_technical_indicators():
    """測試技術指標計算"""
    print("\n🔧 測試技術指標計算...")
    
    try:
        from stock_signal_scanner import StockSignalScanner
        
        scanner = StockSignalScanner()
        
        # 獲取一檔股票數據
        df = scanner.get_latest_stock_data("2330", days=50)
        if df.empty:
            print("❌ 無法獲取股票數據")
            return False
        
        print(f"✅ 獲取到 {len(df)} 天的數據")
        
        # 計算技術指標
        df = scanner.calculate_technical_indicators(df)
        if df.empty:
            print("❌ 技術指標計算失敗")
            return False
        
        print(f"✅ 技術指標計算成功")
        
        # 檢查最新指標值
        latest = df.iloc[-1]
        print(f"   RSI: {latest.get('rsi', 'N/A'):.2f}")
        print(f"   MACD: {latest.get('macd', 'N/A'):.4f}")
        print(f"   MA5: {latest.get('ma5', 'N/A'):.2f}")
        print(f"   MA20: {latest.get('ma20', 'N/A'):.2f}")
        print(f"   BB上軌: {latest.get('bb_upper', 'N/A'):.2f}")
        print(f"   BB下軌: {latest.get('bb_lower', 'N/A'):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 技術指標測試失敗: {e}")
        return False

def test_signal_detection():
    """測試信號檢測"""
    print("\n📡 測試信號檢測...")
    
    try:
        from stock_signal_scanner import StockSignalScanner
        
        scanner = StockSignalScanner()
        
        # 測試幾檔股票
        test_stocks = ["2330", "1101", "2454"]
        
        for stock_id in test_stocks:
            print(f"\n   測試股票 {stock_id}:")
            
            df = scanner.get_latest_stock_data(stock_id, days=50)
            if df.empty:
                print(f"     ❌ 無法獲取數據")
                continue
            
            df = scanner.calculate_technical_indicators(df)
            if df.empty:
                print(f"     ❌ 技術指標計算失敗")
                continue
            
            # 測試不同信號
            signals = []
            
            # RSI+MACD信號
            has_signal, details = scanner.check_rsi_ma_macd_signal(df)
            if has_signal:
                signals.append("RSI+MACD")
            
            # MACD+BB信號
            has_signal, details = scanner.check_macd_bb_signal(df)
            if has_signal:
                signals.append("MACD+BB")
            
            if signals:
                print(f"     ✅ 找到信號: {', '.join(signals)}")
            else:
                print(f"     ⚪ 沒有信號")
        
        return True
        
    except Exception as e:
        print(f"❌ 信號檢測測試失敗: {e}")
        return False

def test_mantra_scanning():
    """測試口訣掃描"""
    print("\n🎭 測試口訣掃描...")
    
    try:
        from stock_signal_scanner import StockSignalScanner
        
        scanner = StockSignalScanner()
        
        # 測試簡單口訣
        test_mantra = {
            'mantra_text': '【MACD+BB】MACD金叉配布林下軌反彈，雙重確認買',
            'category': 'buy',
            'success_rate': 0.69,
            'avg_profit': 0.062
        }
        
        print(f"   測試口訣: {test_mantra['mantra_text']}")
        
        signals = scanner.scan_stocks_by_mantra(test_mantra)
        print(f"   找到 {len(signals)} 個符合條件的股票")
        
        if signals:
            print("   符合條件的股票:")
            for i, signal in enumerate(signals[:5]):
                print(f"     {i+1}. {signal.stock_id} {signal.stock_name} - 價格:{signal.current_price:.2f} 強度:{signal.signal_strength:.1%}")
        
        return len(signals) > 0
        
    except Exception as e:
        print(f"❌ 口訣掃描測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 股票篩選功能調試")
    print("="*50)
    
    # 檢查數據庫
    if not check_database():
        print("\n❌ 數據庫檢查失敗，無法繼續測試")
        return
    
    # 測試技術指標
    if not test_technical_indicators():
        print("\n❌ 技術指標測試失敗")
        return
    
    # 測試信號檢測
    if not test_signal_detection():
        print("\n❌ 信號檢測測試失敗")
        return
    
    # 測試口訣掃描
    success = test_mantra_scanning()
    
    print(f"\n" + "="*50)
    print("🎯 調試結論:")
    
    if success:
        print("✅ 股票篩選功能基本正常")
        print("💡 如果GUI中沒有顯示結果，可能的原因:")
        print("   1. 勾選框狀態沒有正確獲取")
        print("   2. 口訣格式不匹配")
        print("   3. 界面更新沒有觸發")
        print("   4. 需要手動點擊篩選按鈕")
    else:
        print("❌ 股票篩選功能有問題")
        print("💡 建議:")
        print("   1. 檢查技術指標計算邏輯")
        print("   2. 放寬信號檢測條件")
        print("   3. 增加調試日誌")

if __name__ == "__main__":
    main()
