#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復版現金流量表爬蟲測試
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_fixed_crawler():
    """測試修復後的現金流量表爬蟲"""
    
    print("=" * 80)
    print("🔧 測試修復後的現金流量表爬蟲")
    print("=" * 80)
    
    try:
        # 1. 測試導入
        print("📦 測試模組導入...")
        from crawler import crawl_cash_flows, get_all_stock_ids
        print("✅ 成功導入爬蟲函數")
        
        # 2. 測試股票代碼獲取
        print("\n📊 測試股票代碼獲取...")
        stock_ids = get_all_stock_ids()
        print(f"✅ 獲取到 {len(stock_ids)} 個股票代碼")
        print(f"📋 股票代碼範例: {stock_ids[:10]}")
        
        # 3. 測試日期解析
        print("\n📅 測試日期解析...")
        from crawler import get_financial_year_season, get_financial_report_date
        
        test_date = datetime.datetime(2022, 5, 15)
        year, season = get_financial_year_season(test_date)
        report_date = get_financial_report_date(year, season)
        
        print(f"   測試日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"   財報期間: {year}年第{season}季")
        print(f"   發布日期: {report_date}")
        
        # 4. 測試現有資料載入
        print("\n📖 測試現有資料載入...")
        from crawler import load_existing_cash_flows
        
        existing_data = load_existing_cash_flows()
        if existing_data is not None:
            print(f"✅ 成功載入現有資料: {len(existing_data)} 筆")
        else:
            print("ℹ️ 未找到現有資料 (首次執行正常)")
        
        # 5. 檢查 tqdm 修復
        print("\n🔧 檢查 tqdm 修復...")
        try:
            from crawler import tqdm
            test_list = list(range(5))
            progress = tqdm(test_list, desc="測試進度條")
            for i in progress:
                pass
            print("✅ tqdm 進度條正常工作")
        except Exception as e:
            print(f"⚠️ tqdm 仍有問題: {e}")
        
        print("\n✅ 所有基礎功能測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_crawl():
    """測試簡化的爬蟲執行"""
    
    print("\n" + "=" * 80)
    print("🚀 測試簡化爬蟲執行")
    print("=" * 80)
    
    try:
        from crawler import crawl_cash_flows
        
        # 使用較舊的日期，避免網路請求問題
        test_date = datetime.datetime(2021, 5, 15)
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        print("⚠️ 注意: 這是實際網路爬取測試，可能需要較長時間...")
        
        # 詢問用戶是否要執行實際爬取
        choice = input("是否執行實際爬取測試？(y/n，預設 n): ").strip().lower()
        
        if choice == 'y':
            print("🔄 開始實際爬取測試...")
            result = crawl_cash_flows(test_date)
            
            if result is not None and len(result) > 0:
                print(f"✅ 爬取成功！獲取 {len(result)} 筆資料")
                print(f"📊 資料欄位數: {len(result.columns)}")
                
                # 顯示部分資料
                print("\n📋 資料範例:")
                print(result.head(3))
                
                return True
            else:
                print("⚠️ 爬取返回空資料")
                return False
        else:
            print("⏭️ 跳過實際爬取測試")
            return True
            
    except Exception as e:
        print(f"❌ 爬取測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """顯示修復總結"""
    
    print("\n" + "=" * 80)
    print("🔧 修復總結")
    print("=" * 80)
    
    print("✅ 已修復的問題:")
    print("   1. tqdm/ipywidgets 依賴問題")
    print("      - 使用標準 tqdm 替代 tqdm_notebook")
    print("      - 添加 ipywidgets mock 替代")
    print("      - 提供 tqdm 的 fallback 實現")
    print()
    print("   2. get_stock_info 函數未定義問題")
    print("      - 從 newprice.db 獲取股票代碼")
    print("      - 從 cash_flows.db 獲取股票代碼")
    print("      - 提供備用股票代碼列表")
    print()
    
    print("🚀 現在可以使用的命令:")
    print("   python auto_update.py cash_flows")
    print()
    
    print("📊 預期行為:")
    print("   - 自動判斷財報季別")
    print("   - 獲取股票代碼列表")
    print("   - 下載財務報表 HTML")
    print("   - 解析現金流量表資料")
    print("   - 儲存到 DB 和 pickle 格式")

def main():
    """主函數"""
    
    print("🔧 現金流量表爬蟲修復版測試")
    
    # 測試1: 基礎功能
    test1_result = test_fixed_crawler()
    
    # 測試2: 簡化爬蟲 (可選)
    test2_result = test_simple_crawl()
    
    # 顯示修復總結
    show_fix_summary()
    
    # 總結
    print("\n" + "=" * 80)
    print("📊 測試結果")
    print("=" * 80)
    
    if test1_result:
        print("🎉 基礎功能修復成功！")
        if test2_result:
            print("🎉 爬蟲功能也正常工作！")
        else:
            print("⚠️ 爬蟲功能可能需要進一步調試")
        
        print("\n💡 建議下一步:")
        print("   1. 執行: python auto_update.py cash_flows")
        print("   2. 觀察爬取過程和錯誤訊息")
        print("   3. 根據需要進一步調整參數")
        
        return True
    else:
        print("❌ 基礎功能仍有問題，需要進一步修復")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
