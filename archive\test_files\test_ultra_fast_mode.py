#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試超快速模式性能
"""

import sys
import os
import time
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def test_ultra_fast_mode():
    """測試超快速模式"""
    print("⚡ 超快速模式性能測試")
    print("=" * 60)
    
    # 測試股票列表
    test_stocks = [
        '2330', '2317', '2454', '1301', '2303', '2382', '2412', '2881', '2886', '3008',
        '2002', '1216', '1101', '2207', '2308', '2327', '2357', '2395', '2408', '2409',
        '2474', '2603', '2609', '2615', '2633', '2801', '2880', '2882', '2883', '2884',
        '2885', '2887', '2890', '2891', '2892', '2912', '3034', '3037', '3045', '3711',
        '4904', '4938', '5871', '5876', '5880', '6505', '6669', '8454', '8996', '9910'
    ]
    
    print(f"📊 測試 {len(test_stocks)} 支股票")
    
    # 測試1: 超快速批量模式
    print("\n⚡ 測試1: 超快速批量模式")
    print("-" * 40)
    
    strategy = IntradayOpeningRangeStrategy()
    strategy.ultra_fast_mode = True
    strategy.enable_cache = True
    
    start_time = time.time()
    
    try:
        results = strategy.ultra_fast_analysis(test_stocks)
        
        ultra_time = time.time() - start_time
        
        print(f"✅ 超快速分析完成")
        print(f"⏱️  總耗時: {ultra_time:.2f} 秒")
        print(f"📊 平均耗時: {ultra_time/len(test_stocks):.3f} 秒/股票")
        print(f"🎯 找到信號: {len(results)} 支股票")
        
        # 顯示結果
        if results:
            print(f"\n🎯 信號詳情:")
            results.sort(key=lambda x: x.get('confidence', 0), reverse=True)
            
            buy_signals = [r for r in results if r['result'] == 'buy_signal']
            wait_signals = [r for r in results if r['result'] == 'wait_signal']
            
            print(f"• 買入信號: {len(buy_signals)} 支")
            print(f"• 等待信號: {len(wait_signals)} 支")
            
            print(f"\n🏆 前5名買入信號:")
            for i, result in enumerate(buy_signals[:5]):
                confidence = result.get('confidence', 0)
                momentum = result.get('price_momentum', 0)
                volume_ratio = result.get('volume_ratio', 0)
                print(f"{i+1}. {result['stock_id']}: 信心度{confidence}% "
                      f"(動能{momentum:+.1f}%, 量比{volume_ratio:.1f})")
        
    except Exception as e:
        print(f"❌ 超快速分析失敗: {e}")
        return
    
    # 測試2: 對比傳統逐一分析
    print(f"\n🐌 測試2: 傳統逐一分析 (前20支)")
    print("-" * 40)
    
    strategy_normal = IntradayOpeningRangeStrategy()
    strategy_normal.fast_mode = True
    strategy_normal.enable_cache = False  # 關閉緩存以公平比較
    
    test_subset = test_stocks[:20]  # 只測試前20支
    
    start_time = time.time()
    normal_results = []
    
    for i, stock_id in enumerate(test_subset):
        print(f"[{i+1:2d}/{len(test_subset)}] 分析 {stock_id}...", end=" ")
        
        try:
            result = strategy_normal.fast_breakout_analysis(stock_id)
            if result and result.get('result') in ['buy_signal', 'wait_signal']:
                confidence = result.get('signal_details', {}).get('confidence', 0)
                normal_results.append({
                    'stock_id': stock_id,
                    'result': result.get('result'),
                    'confidence': confidence
                })
                print(f"✅ {result.get('result')} ({confidence}%)")
            else:
                print(f"❌ 無信號")
        except Exception as e:
            print(f"⚠️ 錯誤: {str(e)[:20]}...")
    
    normal_time = time.time() - start_time
    
    # 性能對比
    print(f"\n" + "=" * 60)
    print("📈 性能對比分析")
    print("=" * 60)
    
    # 推算傳統模式處理所有股票的時間
    estimated_normal_time = normal_time * len(test_stocks) / len(test_subset)
    
    print(f"⏱️  時間對比:")
    print(f"• 超快速模式: {ultra_time:.2f} 秒 ({len(test_stocks)} 支)")
    print(f"• 傳統模式: {normal_time:.2f} 秒 ({len(test_subset)} 支)")
    print(f"• 傳統模式估算: {estimated_normal_time:.2f} 秒 ({len(test_stocks)} 支)")
    
    # 性能提升
    if estimated_normal_time > 0:
        speedup = estimated_normal_time / ultra_time
        print(f"🚀 性能提升: {speedup:.1f}x 倍")
    
    # 平均處理時間
    avg_ultra = ultra_time / len(test_stocks)
    avg_normal = normal_time / len(test_subset)
    
    print(f"\n📊 平均處理時間:")
    print(f"• 超快速模式: {avg_ultra:.3f} 秒/股票")
    print(f"• 傳統模式: {avg_normal:.3f} 秒/股票")
    
    # 結果對比
    print(f"\n📋 結果對比:")
    print(f"• 超快速模式信號: {len(results)} 支 ({len(test_stocks)} 支測試)")
    print(f"• 傳統模式信號: {len(normal_results)} 支 ({len(test_subset)} 支測試)")
    
    # 推算傳統模式的信號數
    estimated_normal_signals = len(normal_results) * len(test_stocks) / len(test_subset)
    print(f"• 傳統模式估算信號: {estimated_normal_signals:.0f} 支")
    
    # 信號質量對比
    if results and normal_results:
        ultra_avg_confidence = sum(r.get('confidence', 0) for r in results) / len(results)
        normal_avg_confidence = sum(r.get('confidence', 0) for r in normal_results) / len(normal_results)
        
        print(f"\n📊 信號質量:")
        print(f"• 超快速模式平均信心度: {ultra_avg_confidence:.1f}%")
        print(f"• 傳統模式平均信心度: {normal_avg_confidence:.1f}%")
    
    # 總結
    print(f"\n💡 總結:")
    if speedup > 10:
        print(f"✅ 超快速模式性能卓越，建議用於大規模篩選")
    elif speedup > 5:
        print(f"✅ 超快速模式性能優秀，適合日常使用")
    elif speedup > 2:
        print(f"⚠️ 超快速模式有所改善，可根據需求選擇")
    else:
        print(f"❌ 超快速模式提升有限，需要進一步優化")
    
    print(f"\n🎯 建議用法:")
    print(f"• 全市場掃描: 使用 ultra_fast_analysis()")
    print(f"• 重點股票分析: 使用 fast_breakout_analysis()")
    print(f"• 詳細策略驗證: 使用 simulate_breakout_trading()")

def test_threading_performance():
    """測試多線程性能"""
    print(f"\n🧵 多線程性能測試")
    print("-" * 40)
    
    test_stocks = ['2330', '2317', '2454', '1301', '2303', '2382', '2412', '2881', '2886', '3008']
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 測試不同線程數
    thread_counts = [1, 2, 4, 8]
    
    for thread_count in thread_counts:
        print(f"測試 {thread_count} 線程...", end=" ")
        
        start_time = time.time()
        
        try:
            batch_data = strategy.batch_fetch_data(test_stocks, max_workers=thread_count)
            elapsed = time.time() - start_time
            
            print(f"耗時 {elapsed:.2f} 秒，獲取 {len(batch_data)} 支股票數據")
            
        except Exception as e:
            print(f"失敗: {e}")
    
    print(f"\n💡 多線程建議:")
    print(f"• CPU密集型任務: 線程數 = CPU核心數")
    print(f"• IO密集型任務: 線程數 = CPU核心數 × 2-4")
    print(f"• 網絡請求: 適度增加線程數，避免過載")

if __name__ == "__main__":
    test_ultra_fast_mode()
    test_threading_performance()
