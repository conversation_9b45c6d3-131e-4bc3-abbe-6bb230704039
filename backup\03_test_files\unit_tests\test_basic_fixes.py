#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基本修正測試
驗證核心修正是否生效
"""

def test_debug_logging_added():
    """測試是否添加了調試日誌"""
    print("🔍 測試調試日誌")
    print("=" * 30)
    
    try:
        with open('dividend_trading_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查調試日誌
        debug_logs = [
            '調試 - currentData()',
            '調試 - currentText()',
            '調試 - combo count',
            '從currentData獲取股票代碼',
            '從currentText提取股票代碼'
        ]
        
        found = 0
        for log in debug_logs:
            if log in content:
                found += 1
                print(f"✅ 找到: {log}")
            else:
                print(f"❌ 缺少: {log}")
        
        print(f"\n結果: {found}/{len(debug_logs)} 調試日誌已添加")
        return found >= 3
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def test_database_path_changes():
    """測試數據庫路徑修改"""
    print("\n🔍 測試數據庫路徑修改")
    print("=" * 30)
    
    try:
        with open('dividend_trading_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否使用PE數據庫
        if 'pe_data.db' in content:
            print("✅ 找到PE數據庫路徑")
        else:
            print("❌ 沒有PE數據庫路徑")
            return False
        
        # 檢查是否使用備份數據庫
        if 'goodinfo_data.db' in content:
            print("✅ 找到備份數據庫路徑")
        else:
            print("❌ 沒有備份數據庫路徑")
            return False
        
        # 檢查是否減少了錯誤的stock_data查詢
        stock_data_queries = content.count('FROM stock_data')
        print(f"📊 stock_data查詢次數: {stock_data_queries}")
        
        if stock_data_queries <= 1:  # 可能還有一些合理的查詢
            print("✅ stock_data查詢已減少")
        else:
            print("❌ stock_data查詢仍然過多")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def test_eps_calculation_improvements():
    """測試EPS計算改進"""
    print("\n🔍 測試EPS計算改進")
    print("=" * 30)
    
    try:
        with open('dividend_trading_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否使用PE數據中的股價
        if 'stock_price' in content and 'pe_data' in content:
            print("✅ 找到PE數據股價使用")
        else:
            print("❌ 沒有PE數據股價使用")
            return False
        
        # 檢查是否有EPS直接獲取
        if "'eps'" in content and 'pe_data' in content:
            print("✅ 找到EPS直接獲取")
        else:
            print("❌ 沒有EPS直接獲取")
            return False
        
        # 檢查是否有備份查詢邏輯
        if 'backup' in content and 'stock_price' in content:
            print("✅ 找到備份查詢邏輯")
        else:
            print("❌ 沒有備份查詢邏輯")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def test_stock_loading_improvements():
    """測試股票載入改進"""
    print("\n🔍 測試股票載入改進")
    print("=" * 30)
    
    try:
        with open('dividend_trading_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否優先使用PE數據庫
        if '股票代號' in content and '名稱' in content and 'pe_data' in content:
            print("✅ 找到PE數據庫股票載入")
        else:
            print("❌ 沒有PE數據庫股票載入")
            return False
        
        # 檢查是否有備用機制
        if 'load_stocks_from_dividend_db' in content:
            print("✅ 找到備用載入機制")
        else:
            print("❌ 沒有備用載入機制")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def simulate_fixed_workflow():
    """模擬修正後的工作流程"""
    print("\n🎯 模擬修正後的工作流程")
    print("=" * 30)
    
    print("📋 股票選擇流程:")
    print("1. 用戶選擇股票 '1264 道瑩'")
    print("2. 系統記錄調試信息:")
    print("   - currentData(): '1264'")
    print("   - currentText(): '1264 道瑩'")
    print("   - combo count: 100+")
    print("3. 從currentData獲取股票代碼: 1264")
    print("4. 開始分析 1264 的5年歷史數據")
    
    print("\n📋 數據庫查詢流程:")
    print("1. 優先查詢PE數據庫獲取EPS")
    print("2. 如果PE數據中有股價，直接計算EPS")
    print("3. 如果沒有，查詢備份數據庫")
    print("4. 避免查詢不存在的stock_data表格")
    
    print("\n📋 股票清單載入流程:")
    print("1. 優先從pe_data.db載入股票清單")
    print("2. 備用從dividend_data.db載入")
    print("3. 最後使用預設股票清單")
    
    print("\n✅ 預期效果:")
    print("  • 不再出現 'no such table: stock_data' 錯誤")
    print("  • 股票選擇功能正常運作")
    print("  • 提供詳細的調試信息")
    print("  • 5年歷史分析功能可以正常使用")
    
    return True

def main():
    """主測試函數"""
    print("🚀 基本修正測試")
    print("=" * 40)
    
    tests = [
        ("調試日誌添加", test_debug_logging_added),
        ("數據庫路徑修改", test_database_path_changes),
        ("EPS計算改進", test_eps_calculation_improvements),
        ("股票載入改進", test_stock_loading_improvements),
        ("修正後工作流程", simulate_fixed_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("📊 測試結果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 結果: {passed}/{len(results)} 測試通過")
    
    if passed >= 4:
        print("\n🎉 基本修正完成！")
        print("\n💡 主要修正:")
        print("  ✅ 添加股票選擇調試信息")
        print("  ✅ 修正數據庫查詢路徑")
        print("  ✅ 改進EPS計算邏輯")
        print("  ✅ 優化股票清單載入")
        
        print("\n🚀 建議測試:")
        print("  1. 重新啟動除權息交易系統")
        print("  2. 選擇任一股票 (如: 1264 道瑩)")
        print("  3. 點擊 '📊 5年歷史分析' 按鈕")
        print("  4. 查看日誌中的調試信息")
        print("  5. 確認不再出現數據庫錯誤")
    else:
        print(f"\n⚠️ {len(results) - passed} 個測試失敗")
        print("建議檢查代碼修正是否完整")
    
    return passed >= 4

if __name__ == "__main__":
    main()
