#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試策略交集分析導出功能修復
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_intersection_export_fix():
    """測試交集分析導出功能修復"""
    print("🧪 測試策略交集分析導出功能修復")
    print("=" * 50)
    
    try:
        # 導入策略交集分析器
        from strategy_intersection_analyzer import StrategyIntersectionAnalyzer
        print("✅ 策略交集分析器導入成功")
        
        # 創建測試數據
        analyzer = StrategyIntersectionAnalyzer()
        
        # 模擬策略結果
        test_strategies = {
            "CANSLIM量價齊升": pd.DataFrame({
                '股票代碼': ['2330', '2454', '3008', '2317', '2382'],
                '股票名稱': ['台積電', '聯發科', '大立光', '鴻海', '廣達'],
                '收盤價': [500, 800, 2000, 100, 80]
            }),
            "藏獒": pd.DataFrame({
                '股票代碼': ['2330', '2317', '2412', '2881', '1301'],
                '股票名稱': ['台積電', '鴻海', '中華電', '富邦金', '台塑'],
                '收盤價': [500, 100, 120, 60, 90]
            }),
            "二次創高股票": pd.DataFrame({
                '股票代碼': ['2330', '2454', '2317', '6505', '3711'],
                '股票名稱': ['台積電', '聯發科', '鴻海', '台塑化', '日月光'],
                '收盤價': [500, 800, 100, 70, 110]
            })
        }
        
        # 添加策略結果到分析器
        for strategy_name, df in test_strategies.items():
            analyzer.add_strategy_result(strategy_name, df, '股票代碼')
            print(f"✅ 已添加策略: {strategy_name} ({len(df)} 支股票)")
        
        print(f"\n🎯 測試交集計算:")
        
        # 測試1: 計算兩個策略的交集
        print(f"\n1️⃣ 測試兩策略交集:")
        try:
            result1 = analyzer.calculate_intersection(["CANSLIM量價齊升", "藏獒"])
            intersection_stocks1 = result1.get('intersection_stocks', set())
            print(f"   CANSLIM量價齊升 + 藏獒: {len(intersection_stocks1)} 支共同股票")
            print(f"   共同股票: {', '.join(sorted(intersection_stocks1))}")
            
            # 測試導出
            filename1 = analyzer.export_intersection_results(result1)
            if filename1:
                print(f"   ✅ 導出成功: {filename1}")
            else:
                print(f"   ❌ 導出失敗")
                
        except Exception as e:
            print(f"   ❌ 計算失敗: {e}")
        
        # 測試2: 計算三個策略的交集
        print(f"\n2️⃣ 測試三策略交集:")
        try:
            result2 = analyzer.calculate_intersection(["CANSLIM量價齊升", "藏獒", "二次創高股票"])
            intersection_stocks2 = result2.get('intersection_stocks', set())
            print(f"   三策略交集: {len(intersection_stocks2)} 支共同股票")
            print(f"   共同股票: {', '.join(sorted(intersection_stocks2))}")
            
            # 測試導出
            filename2 = analyzer.export_intersection_results(result2)
            if filename2:
                print(f"   ✅ 導出成功: {filename2}")
            else:
                print(f"   ❌ 導出失敗")
                
        except Exception as e:
            print(f"   ❌ 計算失敗: {e}")
        
        # 測試3: 分析所有組合
        print(f"\n3️⃣ 測試所有組合分析:")
        try:
            all_results = analyzer.analyze_all_combinations()
            top_combinations = analyzer.get_top_intersection_combinations()
            
            print(f"   找到 {len(top_combinations)} 個有效組合:")
            for i, (combo_name, count) in enumerate(top_combinations, 1):
                print(f"     {i}. {combo_name}: {count} 支共同股票")
                
                # 測試導出最佳組合
                if i == 1 and combo_name in all_results:
                    best_result = all_results[combo_name]
                    filename3 = analyzer.export_intersection_results(best_result)
                    if filename3:
                        print(f"       ✅ 最佳組合導出成功: {filename3}")
                    else:
                        print(f"       ❌ 最佳組合導出失敗")
                        
        except Exception as e:
            print(f"   ❌ 組合分析失敗: {e}")
        
        # 測試4: 空結果導出
        print(f"\n4️⃣ 測試空結果導出:")
        try:
            empty_result = {
                'strategies': ['測試策略A', '測試策略B'],
                'intersection_stocks': set(),
                'intersection_count': 0,
                'individual_counts': {'測試策略A': 0, '測試策略B': 0},
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            filename4 = analyzer.export_intersection_results(empty_result)
            if filename4:
                print(f"   ✅ 空結果導出成功: {filename4}")
            else:
                print(f"   ❌ 空結果導出失敗")
                
        except Exception as e:
            print(f"   ❌ 空結果導出失敗: {e}")
        
        print(f"\n✅ 所有測試完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_gui_integration():
    """測試GUI整合"""
    print(f"\n🖥️ 測試GUI整合")
    print("=" * 30)
    
    try:
        # 檢查主程式是否存在
        if not os.path.exists('O3mh_gui_v21_optimized.py'):
            print("❌ 主程式文件不存在")
            return False
            
        print("✅ 主程式文件存在")
        
        # 檢查關鍵方法是否存在
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        methods_to_check = [
            'export_intersection_results',
            'calculate_strategy_intersection', 
            'analyze_all_strategy_combinations',
            'current_intersection_result'
        ]
        
        print(f"\n🔍 檢查關鍵方法:")
        for method in methods_to_check:
            if method in content:
                print(f"  ✅ {method} - 存在")
            else:
                print(f"  ❌ {method} - 不存在")
                
        # 檢查修復內容
        if 'current_intersection_result = all_results[best_combo_name]' in content:
            print(f"  ✅ 組合分析結果保存修復 - 已應用")
        else:
            print(f"  ❌ 組合分析結果保存修復 - 未應用")
            
        if '沒有可導出的交集結果' in content and '請先執行以下操作之一' in content:
            print(f"  ✅ 導出錯誤信息優化 - 已應用")
        else:
            print(f"  ❌ 導出錯誤信息優化 - 未應用")
            
        return True
        
    except Exception as e:
        print(f"❌ GUI整合測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 策略交集分析導出功能修復測試")
    print("=" * 60)
    
    # 執行測試
    test1_result = test_intersection_export_fix()
    test2_result = test_gui_integration()
    
    print(f"\n📊 測試結果總結:")
    print(f"  交集分析功能測試: {'✅ 通過' if test1_result else '❌ 失敗'}")
    print(f"  GUI整合測試: {'✅ 通過' if test2_result else '❌ 失敗'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有測試通過！修復成功！")
        print(f"\n💡 使用說明:")
        print(f"  1. 執行多個策略獲得結果")
        print(f"  2. 切換到「🔗 策略交集」標籤頁")
        print(f"  3. 選擇要分析的策略")
        print(f"  4. 點擊「🎯 計算交集」或「🔍 分析所有組合」")
        print(f"  5. 點擊「📁 導出結果」即可成功導出")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查修復內容")
