#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab Tables GUI - 財務數據管理界面
提供直觀的GUI來管理和查看Finlab財務數據
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import sqlite3
import os
import threading
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
try:
    import seaborn as sns
except ImportError:
    sns = None
from finlab_tables_analysis_and_migration import FinlabTablesAnalyzer

class FinlabTablesGUI:
    """Finlab財務數據管理GUI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("📊 Finlab財務數據管理系統")
        self.root.geometry("1200x800")
        
        # 設置樣式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 初始化變量
        self.analyzer = None
        self.db_path = "D:/Finlab/history/tables/finlab_financial_data.db"
        self.current_data = None
        
        # 創建界面
        self.create_widgets()
        
        # 檢查數據庫是否存在
        self.check_database()
    
    def create_widgets(self):
        """創建GUI組件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 Finlab財務數據管理系統", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左側控制面板
        control_frame = ttk.LabelFrame(main_frame, text="🎛️ 控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 數據源設置
        ttk.Label(control_frame, text="📁 Finlab數據路徑:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.path_var = tk.StringVar(value="D:/□Finlab學習/用 Python 理財：打造自己的 AI 股票理專_原始檔案/finlab_ml_course/finlab_ml_course/history/tables")
        path_entry = ttk.Entry(control_frame, textvariable=self.path_var, width=40)
        path_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="瀏覽", command=self.browse_path).grid(row=1, column=1, padx=(5, 0), pady=5)
        
        # 操作按鈕
        ttk.Button(control_frame, text="🔍 分析數據表格", 
                  command=self.analyze_tables).grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="🚀 移植數據", 
                  command=self.migrate_data).grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="📋 生成報告", 
                  command=self.generate_report).grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 分隔線
        ttk.Separator(control_frame, orient='horizontal').grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 數據查看
        ttk.Label(control_frame, text="📊 數據表格:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.table_var = tk.StringVar()
        self.table_combo = ttk.Combobox(control_frame, textvariable=self.table_var, state="readonly")
        self.table_combo.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.table_combo.bind('<<ComboboxSelected>>', self.on_table_selected)
        
        ttk.Button(control_frame, text="📈 查看數據", 
                  command=self.view_table_data).grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="📊 數據統計", 
                  command=self.show_statistics).grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 狀態顯示
        self.status_var = tk.StringVar(value="🔄 準備就緒")
        status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=10, column=0, columnspan=2, pady=10)
        
        # 右側主要內容區域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 創建筆記本（標籤頁）
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 數據預覽標籤頁
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="📊 數據預覽")
        
        # 創建數據表格
        self.create_data_table()
        
        # 圖表標籤頁
        self.chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chart_frame, text="📈 圖表分析")
        
        # 日誌標籤頁
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 操作日誌")
        
        # 創建日誌文本框
        self.log_text = tk.Text(self.log_frame, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(self.log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        self.log_frame.columnconfigure(0, weight=1)
        self.log_frame.rowconfigure(0, weight=1)
    
    def create_data_table(self):
        """創建數據表格"""
        # 創建Treeview
        columns = ['Column1', 'Column2', 'Column3', 'Column4', 'Column5']
        self.tree = ttk.Treeview(self.data_frame, columns=columns, show='tree headings')
        
        # 設置列標題
        self.tree.heading('#0', text='Index')
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
        
        # 添加滾動條
        v_scrollbar = ttk.Scrollbar(self.data_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.data_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.data_frame.columnconfigure(0, weight=1)
        self.data_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日誌消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def browse_path(self):
        """瀏覽選擇路徑"""
        path = filedialog.askdirectory(title="選擇Finlab數據表格目錄")
        if path:
            self.path_var.set(path)
            self.log_message(f"📁 設置數據路徑: {path}")
    
    def check_database(self):
        """檢查數據庫狀態"""
        if os.path.exists(self.db_path):
            self.status_var.set("✅ 數據庫已存在")
            self.load_table_list()
        else:
            self.status_var.set("⚠️ 數據庫不存在，請先移植數據")
    
    def load_table_list(self):
        """載入數據表格列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.table_combo['values'] = tables
            if tables:
                self.table_combo.current(0)
            
            self.log_message(f"📋 載入 {len(tables)} 個數據表格")
            
        except Exception as e:
            self.log_message(f"❌ 載入表格列表失敗: {e}")
    
    def analyze_tables(self):
        """分析數據表格"""
        def analyze_worker():
            try:
                self.status_var.set("🔍 正在分析數據表格...")
                self.log_message("🔍 開始分析Finlab數據表格")
                
                # 創建分析器
                self.analyzer = FinlabTablesAnalyzer(self.path_var.get())
                
                # 執行分析
                results = self.analyzer.analyze_all_tables()
                
                if results:
                    self.log_message(f"✅ 分析完成，發現 {len(results)} 個數據表格")
                    self.status_var.set(f"✅ 分析完成 ({len(results)} 個表格)")
                    
                    # 顯示分析結果
                    for file_name, analysis in results.items():
                        if 'error' not in analysis:
                            shape_info = f" ({analysis.get('shape', ['?', '?'])[0]}×{analysis.get('shape', ['?', '?'])[1]})" if analysis.get('shape') else ""
                            self.log_message(f"📊 {file_name}{shape_info}")
                else:
                    self.log_message("❌ 未發現任何數據表格")
                    self.status_var.set("❌ 分析失敗")
                    
            except Exception as e:
                self.log_message(f"❌ 分析失敗: {e}")
                self.status_var.set("❌ 分析失敗")
        
        # 在後台線程中執行
        threading.Thread(target=analyze_worker, daemon=True).start()
    
    def migrate_data(self):
        """移植數據"""
        def migrate_worker():
            try:
                self.status_var.set("🚀 正在移植數據...")
                self.log_message("🚀 開始移植Finlab數據")
                
                if not self.analyzer:
                    self.analyzer = FinlabTablesAnalyzer(self.path_var.get())
                
                # 執行移植
                success = self.analyzer.migrate_all_tables()
                
                if success:
                    self.log_message("✅ 數據移植成功")
                    self.status_var.set("✅ 移植完成")
                    
                    # 重新載入表格列表
                    self.load_table_list()
                else:
                    self.log_message("❌ 數據移植失敗")
                    self.status_var.set("❌ 移植失敗")
                    
            except Exception as e:
                self.log_message(f"❌ 移植失敗: {e}")
                self.status_var.set("❌ 移植失敗")
        
        # 在後台線程中執行
        threading.Thread(target=migrate_worker, daemon=True).start()
    
    def generate_report(self):
        """生成報告"""
        def report_worker():
            try:
                self.status_var.set("📋 正在生成報告...")
                self.log_message("📋 開始生成分析報告")
                
                if not self.analyzer:
                    self.analyzer = FinlabTablesAnalyzer(self.path_var.get())
                
                # 生成報告
                report_path = self.analyzer.generate_analysis_report()
                
                if report_path and os.path.exists(report_path):
                    self.log_message(f"✅ 報告已生成: {report_path}")
                    self.status_var.set("✅ 報告生成完成")
                    
                    # 詢問是否打開報告
                    if messagebox.askyesno("報告生成完成", f"報告已生成:\n{report_path}\n\n是否要打開報告？"):
                        os.startfile(report_path)
                else:
                    self.log_message("❌ 報告生成失敗")
                    self.status_var.set("❌ 報告生成失敗")
                    
            except Exception as e:
                self.log_message(f"❌ 報告生成失敗: {e}")
                self.status_var.set("❌ 報告生成失敗")
        
        # 在後台線程中執行
        threading.Thread(target=report_worker, daemon=True).start()
    
    def on_table_selected(self, event):
        """表格選擇事件"""
        selected_table = self.table_var.get()
        if selected_table:
            self.log_message(f"📊 選擇表格: {selected_table}")
    
    def view_table_data(self):
        """查看表格數據"""
        selected_table = self.table_var.get()
        if not selected_table:
            messagebox.showwarning("警告", "請先選擇一個數據表格")
            return
        
        try:
            self.status_var.set("📊 正在載入數據...")
            self.log_message(f"📊 載入表格數據: {selected_table}")
            
            # 從數據庫讀取數據
            conn = sqlite3.connect(self.db_path)
            query = f"SELECT * FROM {selected_table} LIMIT 1000"  # 限制顯示1000行
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # 清空現有數據
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 更新列標題
            columns = list(df.columns)
            self.tree['columns'] = columns
            
            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=100)
            
            # 插入數據
            for index, row in df.iterrows():
                values = [str(val) for val in row.values]
                self.tree.insert('', 'end', text=str(index), values=values)
            
            self.current_data = df
            self.log_message(f"✅ 載入完成: {len(df)} 行 × {len(df.columns)} 列")
            self.status_var.set(f"✅ 數據已載入 ({len(df)} 行)")
            
        except Exception as e:
            self.log_message(f"❌ 載入數據失敗: {e}")
            self.status_var.set("❌ 載入失敗")
            messagebox.showerror("錯誤", f"載入數據失敗:\n{e}")
    
    def show_statistics(self):
        """顯示數據統計"""
        if self.current_data is None:
            messagebox.showwarning("警告", "請先載入數據")
            return
        
        try:
            # 創建統計窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("📊 數據統計")
            stats_window.geometry("800x600")
            
            # 創建文本框顯示統計信息
            stats_text = tk.Text(stats_window, wrap=tk.WORD)
            stats_scrollbar = ttk.Scrollbar(stats_window, orient="vertical", command=stats_text.yview)
            stats_text.configure(yscrollcommand=stats_scrollbar.set)
            
            stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 生成統計信息
            stats_info = f"""📊 數據統計報告
{'='*50}

📋 基本信息:
- 數據形狀: {self.current_data.shape[0]:,} 行 × {self.current_data.shape[1]:,} 列
- 記憶體使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB

📊 數值列統計:
{self.current_data.describe()}

📈 數據類型:
{self.current_data.dtypes}

🔍 缺失值統計:
{self.current_data.isnull().sum()}
"""
            
            stats_text.insert(tk.END, stats_info)
            
            self.log_message("📊 顯示數據統計")
            
        except Exception as e:
            self.log_message(f"❌ 統計失敗: {e}")
            messagebox.showerror("錯誤", f"統計失敗:\n{e}")

def main():
    """主函數"""
    root = tk.Tk()
    app = FinlabTablesGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
