#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試EPS計算功能
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, date

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_eps_calculation():
    """測試EPS計算功能"""
    print("=" * 60)
    print("🧮 測試EPS計算功能")
    print("=" * 60)
    
    try:
        # 1. 獲取PE資料
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        pe_conn = sqlite3.connect(pe_db_path)
        
        pe_query = """
        SELECT stock_id, stock_name, pe_ratio
        FROM pe_data 
        WHERE date = '2025-07-25'
        AND pe_ratio IS NOT NULL 
        AND pe_ratio > 0
        LIMIT 10
        """
        pe_df = pd.read_sql_query(pe_query, pe_conn)
        pe_conn.close()
        
        print(f"✅ 獲取到 {len(pe_df)} 筆PE資料")
        
        # 2. 獲取股價資料
        price_db_path = 'D:/Finlab/history/tables/price.db'
        price_conn = sqlite3.connect(price_db_path)
        
        price_query = """
        SELECT stock_id, stock_name, Close
        FROM stock_daily_data 
        WHERE date = '2025-07-25'
        AND Close IS NOT NULL 
        AND Close > 0
        """
        price_df = pd.read_sql_query(price_query, price_conn)
        price_conn.close()
        
        print(f"✅ 獲取到 {len(price_df)} 筆股價資料")
        
        # 3. 合併資料
        merged_df = pe_df.merge(price_df[['stock_id', 'Close']], on='stock_id', how='inner')
        print(f"✅ 合併後有 {len(merged_df)} 筆可計算EPS的資料")
        
        # 4. 計算EPS
        merged_df['EPS'] = merged_df['Close'] / merged_df['pe_ratio']
        
        # 5. 顯示結果
        print("\n📊 EPS計算結果:")
        print("-" * 80)
        print(f"{'股票代碼':<8} {'股票名稱':<12} {'股價':<8} {'本益比':<8} {'計算EPS':<10}")
        print("-" * 80)
        
        for _, row in merged_df.head(10).iterrows():
            print(f"{row['stock_id']:<8} {row['stock_name'][:10]:<12} {row['Close']:>6.2f} {row['pe_ratio']:>6.2f} {row['EPS']:>8.2f}")
        
        # 6. 統計分析
        print(f"\n📈 EPS統計:")
        print(f"   平均EPS: {merged_df['EPS'].mean():.2f}")
        print(f"   最高EPS: {merged_df['EPS'].max():.2f} ({merged_df.loc[merged_df['EPS'].idxmax(), 'stock_name']})")
        print(f"   最低EPS: {merged_df['EPS'].min():.2f} ({merged_df.loc[merged_df['EPS'].idxmin(), 'stock_name']})")
        
        # 7. 驗證計算
        print(f"\n🔍 計算驗證 (以第一筆資料為例):")
        first_row = merged_df.iloc[0]
        manual_eps = first_row['Close'] / first_row['pe_ratio']
        print(f"   股票: {first_row['stock_name']} ({first_row['stock_id']})")
        print(f"   股價: {first_row['Close']:.2f}")
        print(f"   本益比: {first_row['pe_ratio']:.2f}")
        print(f"   計算EPS: {first_row['Close']:.2f} ÷ {first_row['pe_ratio']:.2f} = {manual_eps:.2f}")
        print(f"   程式計算: {first_row['EPS']:.2f}")
        print(f"   ✅ 計算{'正確' if abs(manual_eps - first_row['EPS']) < 0.01 else '錯誤'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_eps_formatting():
    """測試EPS格式化"""
    print("\n" + "=" * 60)
    print("📋 測試EPS格式化")
    print("=" * 60)
    
    def calculate_eps(close_price, pe_ratio):
        """模擬EPS計算函數"""
        try:
            if pd.isna(close_price) or pd.isna(pe_ratio) or close_price is None or pe_ratio is None:
                return 'N/A'
            
            close_price = float(close_price)
            pe_ratio = float(pe_ratio)
            
            if pe_ratio <= 0:
                return 'N/A'
            
            eps = close_price / pe_ratio
            return f"{eps:.2f}"
            
        except (ValueError, TypeError, ZeroDivisionError):
            return 'N/A'
    
    # 測試各種情況
    test_cases = [
        (580.0, 15.2, "正常計算"),
        (120.0, 12.8, "正常計算"),
        (850.0, 18.5, "正常計算"),
        (100.0, 0, "本益比為0"),
        (100.0, -5.0, "本益比為負"),
        (None, 15.0, "股價為None"),
        (100.0, None, "本益比為None"),
        (pd.NA, 15.0, "股價為NA"),
        (100.0, pd.NA, "本益比為NA"),
    ]
    
    print("股價     | 本益比  | 計算EPS | 說明")
    print("-" * 45)
    
    for close_price, pe_ratio, description in test_cases:
        eps = calculate_eps(close_price, pe_ratio)
        price_str = f"{close_price}" if close_price is not None else "None"
        pe_str = f"{pe_ratio}" if pe_ratio is not None else "None"
        print(f"{price_str:<8} | {pe_str:<7} | {eps:<7} | {description}")
    
    print("\n✅ EPS格式化測試完成")
    return True

def test_integration_with_monthly_revenue():
    """測試與月營收資料的整合"""
    print("\n" + "=" * 60)
    print("🔗 測試與月營收資料整合")
    print("=" * 60)
    
    try:
        # 模擬月營收排行榜的完整資料結構
        sample_data = {
            '排名': [1, 2, 3],
            '股票代碼': ['2330', '2317', '2454'],
            '股票名稱': ['台積電', '鴻海', '聯發科'],
            '西元年月': ['202507', '202507', '202507'],
            '當月營收': ['120,000,000', '95,000,000', '85,000,000'],
            '上個月營收': ['115,000,000', '92,000,000', '80,000,000'],
            '去年同月營收': ['100,000,000', '88,000,000', '75,000,000'],
            'YoY%': ['20.00%', '7.95%', '13.33%'],
            'MoM%': ['4.35%', '3.26%', '6.25%'],
            '殖利率': ['3.25', '4.50', '2.80'],
            '本益比': ['15.80', '12.50', '18.20'],
            '股價淨值比': ['2.45', '1.85', '3.20'],
            'EPS': ['36.71', '9.60', '46.70'],  # 假設已計算的EPS
            '綜合評分': ['85.5', '78.2', '82.1']
        }
        
        print("🏆 完整的月營收排行榜資料結構 (包含EPS):")
        for key, values in sample_data.items():
            print(f"   {key}: {values}")
        
        print(f"\n📊 新的表格欄位總數: {len(sample_data)} 欄")
        print("✅ EPS欄位已成功整合到月營收排行榜中")
        
        # 驗證EPS計算邏輯
        print(f"\n🔍 EPS計算驗證:")
        stock_prices = [580.0, 120.0, 850.0]  # 假設股價
        pe_ratios = [15.80, 12.50, 18.20]
        
        for i, (stock_id, stock_name) in enumerate(zip(sample_data['股票代碼'], sample_data['股票名稱'])):
            calculated_eps = stock_prices[i] / pe_ratios[i]
            print(f"   {stock_id} {stock_name}: {stock_prices[i]:.1f} ÷ {pe_ratios[i]:.2f} = {calculated_eps:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 整合測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 開始測試EPS計算功能...")
    
    # 測試EPS計算
    success1 = test_eps_calculation()
    
    # 測試EPS格式化
    success2 = test_eps_formatting()
    
    # 測試與月營收整合
    success3 = test_integration_with_monthly_revenue()
    
    if success1 and success2 and success3:
        print("\n🎉 所有EPS測試通過！EPS計算功能正常。")
    else:
        print("\n❌ 部分測試失敗，請檢查相關功能。")
