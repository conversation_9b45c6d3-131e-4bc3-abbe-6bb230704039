#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試閃退問題
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """測試所有必要的模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        from trading_rule_miner import TradingRuleMiner
        print("✅ TradingRuleMiner 導入成功")
    except Exception as e:
        print(f"❌ TradingRuleMiner 導入失敗: {e}")
        return False
    
    try:
        from trading_mantra_generator import TradingMantraGenerator
        print("✅ TradingMantraGenerator 導入成功")
    except Exception as e:
        print(f"❌ TradingMantraGenerator 導入失敗: {e}")
        return False
    
    try:
        from strategy_combination_generator import StrategyCombinationGenerator
        print("✅ StrategyCombinationGenerator 導入成功")
    except Exception as e:
        print(f"❌ StrategyCombinationGenerator 導入失敗: {e}")
        return False
    
    try:
        from stock_signal_scanner import StockSignalScanner
        print("✅ StockSignalScanner 導入成功")
    except Exception as e:
        print(f"❌ StockSignalScanner 導入失敗: {e}")
        return False
    
    return True

def test_rule_miner():
    """測試交易規則挖掘器"""
    print("\n🔧 測試交易規則挖掘器...")
    
    try:
        from trading_rule_miner import TradingRuleMiner
        
        rule_miner = TradingRuleMiner()
        rule_miner.min_success_rate = 0.65
        rule_miner.min_avg_return = 0.02
        
        print("✅ TradingRuleMiner 初始化成功")
        
        # 測試小量分析
        print("🔍 執行小量分析測試 (10檔股票)...")
        combinations = rule_miner.run_mass_analysis(10)
        
        if combinations:
            print(f"✅ 分析成功，找到 {len(combinations)} 個組合")
            
            # 測試規則提取
            print("🔍 測試規則提取...")
            rules = rule_miner.extract_trading_rules(combinations)
            print(f"✅ 規則提取成功，找到 {len(rules)} 條規則")
            
            return True
        else:
            print("❌ 分析結果為空")
            return False
            
    except Exception as e:
        print(f"❌ TradingRuleMiner 測試失敗: {e}")
        traceback.print_exc()
        return False

def test_mantra_generator():
    """測試口訣生成器"""
    print("\n🎭 測試口訣生成器...")
    
    try:
        from trading_mantra_generator import TradingMantraGenerator
        from trading_rule_miner import TradingRuleMiner
        
        # 先生成一些規則
        rule_miner = TradingRuleMiner()
        rule_miner.min_success_rate = 0.65
        rule_miner.min_avg_return = 0.02
        
        combinations = rule_miner.run_mass_analysis(5)  # 更小的測試
        rules = rule_miner.extract_trading_rules(combinations)
        
        if not rules:
            print("❌ 沒有規則可供測試")
            return False
        
        # 測試口訣生成
        mantra_generator = TradingMantraGenerator()
        mantras = mantra_generator.generate_all_mantras(rules)
        
        print(f"✅ 口訣生成成功，生成 {len(mantras)} 條口訣")
        return True
        
    except Exception as e:
        print(f"❌ TradingMantraGenerator 測試失敗: {e}")
        traceback.print_exc()
        return False

def test_strategy_combinations():
    """測試策略組合生成器"""
    print("\n🎯 測試策略組合生成器...")
    
    try:
        from strategy_combination_generator import StrategyCombinationGenerator
        
        generator = StrategyCombinationGenerator()
        mantras = generator.generate_all_combinations()
        
        print(f"✅ 策略組合生成成功")
        print(f"   買入口訣: {len(mantras['buy'])} 條")
        print(f"   賣出口訣: {len(mantras['sell'])} 條")
        
        return True
        
    except Exception as e:
        print(f"❌ StrategyCombinationGenerator 測試失敗: {e}")
        traceback.print_exc()
        return False

def test_stock_scanner():
    """測試股票掃描器"""
    print("\n📡 測試股票掃描器...")
    
    try:
        from stock_signal_scanner import StockSignalScanner
        
        scanner = StockSignalScanner()
        print("✅ StockSignalScanner 初始化成功")
        
        # 測試獲取股票數據
        df = scanner.get_latest_stock_data("2330", days=30)
        if not df.empty:
            print(f"✅ 股票數據獲取成功，獲得 {len(df)} 天數據")
        else:
            print("❌ 股票數據獲取失敗")
            return False
        
        # 測試技術指標計算
        df = scanner.calculate_technical_indicators(df)
        if not df.empty:
            print("✅ 技術指標計算成功")
        else:
            print("❌ 技術指標計算失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ StockSignalScanner 測試失敗: {e}")
        traceback.print_exc()
        return False

def test_database():
    """測試數據庫連接"""
    print("\n💾 測試數據庫連接...")
    
    try:
        import sqlite3
        import os
        
        db_path = "db/price.db"
        if not os.path.exists(db_path):
            print(f"❌ 數據庫文件不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stock_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MAX(date) FROM stock_daily_data")
        latest_date = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ 數據庫連接成功")
        print(f"   股票數量: {stock_count}")
        print(f"   最新日期: {latest_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 閃退問題調試")
    print("="*50)
    
    # 測試各個組件
    tests = [
        ("模組導入", test_imports),
        ("數據庫連接", test_database),
        ("股票掃描器", test_stock_scanner),
        ("策略組合生成器", test_strategy_combinations),
        ("交易規則挖掘器", test_rule_miner),
        ("口訣生成器", test_mantra_generator),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # 總結
    print(f"\n" + "="*50)
    print("🎯 測試結果總結:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過，問題可能在GUI線程處理")
        print("💡 建議:")
        print("   1. 檢查PyQt6線程安全問題")
        print("   2. 檢查信號槽連接")
        print("   3. 檢查UI更新邏輯")
    else:
        print("⚠️ 發現問題組件，需要修復後再測試GUI")

if __name__ == "__main__":
    main()
