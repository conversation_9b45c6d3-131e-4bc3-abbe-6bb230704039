#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

# 檢查PKL檔案
pkl_path = 'history/tables/monthly_report.pkl'

print(f"檢查檔案: {pkl_path}")
print(f"檔案存在: {os.path.exists(pkl_path)}")

if os.path.exists(pkl_path):
    try:
        df = pd.read_pickle(pkl_path)
        print(f"成功讀取PKL檔案")
        print(f"資料形狀: {df.shape}")
        print(f"索引類型: {type(df.index)}")
        
        if isinstance(df.index, pd.MultiIndex):
            print(f"多層索引: {df.index.names}")
            df_reset = df.reset_index()
        else:
            print(f"單層索引: {df.index.name}")
            df_reset = df.copy()
        
        print(f"欄位: {list(df_reset.columns)}")
        print(f"前3筆資料:")
        print(df_reset.head(3))
        
        # 測試查詢2330
        if 'stock_id' in df_reset.columns:
            stock_mask = df_reset['stock_id'].astype(str).str.contains('2330', na=False)
            stock_df = df_reset[stock_mask]
            print(f"\n2330的記錄數: {len(stock_df)}")
            if not stock_df.empty:
                print("2330的最新記錄:")
                if 'date' in stock_df.columns:
                    stock_df = stock_df.sort_values('date', ascending=False)
                print(stock_df.iloc[0])
        
    except Exception as e:
        print(f"讀取失敗: {e}")
else:
    print("檔案不存在")
