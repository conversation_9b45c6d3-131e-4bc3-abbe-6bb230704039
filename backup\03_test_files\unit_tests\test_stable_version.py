#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試穩定版本（可能會卡頓但能獲取數據）
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_stable_version():
    """測試穩定版本"""
    print("🔄 測試穩定版本（可能會卡頓但能獲取數據）")
    print("=" * 60)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 檢查設定
        print(f"📊 請求間隔: {fetcher.min_request_interval}秒")
        print(f"📊 最大重試次數: 3次")
        print("⚠️ 注意：此版本會較慢但更穩定")
        
        # 測試美股指數
        print("\n📈 測試美股指數（穩定版本）...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({us_time:.1f}秒):")
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
        
        # 測試中國股市
        print("\n🇨🇳 測試中國股市指數（穩定版本）...")
        china_start = time.time()
        china_data = fetcher.get_china_indices_real()
        china_time = time.time() - china_start
        
        if china_data:
            print(f"✅ 中國股市指數獲取成功 ({china_time:.1f}秒):")
            for name, data in china_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 中國股市指數獲取失敗 ({china_time:.1f}秒)")
        
        # 測試商品價格
        print("\n🛢️ 測試商品價格（穩定版本）...")
        commodity_start = time.time()
        commodity_data = fetcher.get_commodities_real()
        commodity_time = time.time() - commodity_start
        
        if commodity_data:
            print(f"✅ 商品價格獲取成功 ({commodity_time:.1f}秒):")
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: ${price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 商品價格獲取失敗 ({commodity_time:.1f}秒)")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        # 統計成功率
        categories = [
            ('美股指數', us_data),
            ('中國股市', china_data),
            ('商品價格', commodity_data)
        ]
        
        success_count = sum(1 for _, data in categories if data)
        total_count = len(categories)
        success_rate = (success_count / total_count) * 100
        
        print(f"📊 成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_rate >= 66.7  # 至少2/3成功
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner_stable():
    """測試安全掃描器穩定版本"""
    print("\n🔍 測試安全掃描器穩定版本...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 測試關鍵數據類別
        print("🌍 執行關鍵數據掃描...")
        start_time = time.time()
        
        # 測試美股和中國股市
        results = {
            'us_indices': scanner.get_us_indices(),
            'china_indices': scanner.get_china_indices(),
            'commodities': scanner.get_commodities()
        }
        
        scan_time = time.time() - start_time
        
        # 統計結果
        total_categories = len(results)
        successful_categories = 0
        total_items = 0
        successful_items = 0
        
        for category, data in results.items():
            if data:
                successful_categories += 1
                category_count = len(data)
                total_items += category_count
                
                # 計算成功獲取的項目
                for item_name, item_data in data.items():
                    status = item_data.get('status', '')
                    if '真實數據' in status:
                        successful_items += 1
                
                print(f"  ✅ {category}: {category_count} 項成功")
            else:
                print(f"  ❌ {category}: 失敗")
        
        success_rate = (successful_items / total_items * 100) if total_items > 0 else 0
        
        print(f"\n📊 掃描結果:")
        print(f"  • 成功類別: {successful_categories}/{total_categories}")
        print(f"  • 成功項目: {successful_items}/{total_items}")
        print(f"  • 成功率: {success_rate:.1f}%")
        print(f"  • 掃描耗時: {scan_time:.1f}秒")
        
        return success_rate >= 70  # 成功率超過70%
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔄 穩定版本測試（可能會卡頓但能獲取數據）")
    print("=" * 70)
    
    tests = [
        ("穩定版本數據獲取", test_stable_version),
        ("安全掃描器穩定版本", test_safe_scanner_stable)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n開始測試: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 70)
    print("📋 穩定版本測試總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🔄 穩定版本設定:")
    print("• 請求間隔：2.5秒（較慢但穩定）")
    print("• 隨機延遲：2.0-4.0秒（避免頻率限制）")
    print("• 重試次數：3次（增加成功機會）")
    print("• 頻率限制處理：8-14秒等待時間")
    print("• 一般錯誤重試：3-5秒等待時間")
    
    print("\n💡 保留的功能:")
    print("• ✅ 網頁爬取備援：三層數據源保障")
    print("• ✅ 顏色顯示：漲紅跌綠視覺效果")
    print("• ✅ 線程處理：避免UI完全凍結")
    print("• ✅ 多代碼備援：提高成功率")
    
    print("\n⚠️ 已知問題:")
    print("• 會有卡頓感（但能獲取到數據）")
    print("• 處理時間較長（但更穩定）")
    print("• 可能需要等待較久（但成功率高）")
    
    if passed > 0:
        print("\n🎉 穩定版本恢復成功！")
        print("雖然會有點卡頓，但應該能成功獲取美股和中國股市數據。")
    else:
        print("\n⚠️ 需要進一步調整設定")

if __name__ == "__main__":
    main()
