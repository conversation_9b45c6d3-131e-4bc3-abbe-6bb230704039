#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試圖表橫軸日期顯示修復
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class ChartAxisTestWindow(QMainWindow):
    """圖表橫軸日期顯示修復測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📈 圖表橫軸日期顯示修復測試")
        self.setGeometry(100, 100, 900, 700)
        
        # 設置深色主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 標題
        title_label = QLabel("📈 圖表橫軸日期顯示修復測試")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4ff; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 功能說明
        info_text = QTextEdit()
        info_text.setMaximumHeight(300)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3 style="color: #ffffff;">📈 修復內容</h3>
        
        <h4 style="color: #cccccc;">❌ 問題描述</h4>
        <ul>
            <li><b>橫軸無日期</b> - 圖表分析的橫軸只顯示數字序號，沒有日期信息</li>
            <li><b>時間軸失效</b> - DateAxisItem 設置可能失敗，導致無法顯示日期</li>
            <li><b>標籤缺失</b> - X軸標籤顯示不完整或不清晰</li>
        </ul>
        
        <h4 style="color: #cccccc;">✅ 修復方案</h4>
        <ul>
            <li><b>歷史指數圖表</b> - 改進日期軸設置，添加備用自定義標籤機制</li>
            <li><b>市場指數圖表</b> - 智能顯示證券代號，根據數據量調整標籤密度</li>
            <li><b>融資融券圖表</b> - 優化股票代號顯示，避免標籤重疊</li>
            <li><b>軸顏色設置</b> - 確保軸標籤在深色主題下清晰可見</li>
        </ul>
        
        <h4 style="color: #cccccc;">🎯 修復效果</h4>
        <ul>
            <li><b>歷史指數</b> - 橫軸顯示完整日期 (YYYY-MM-DD 格式)</li>
            <li><b>市場指數</b> - 橫軸顯示證券代號或項目序號</li>
            <li><b>融資融券</b> - 橫軸顯示股票代號或股票序號</li>
            <li><b>智能標籤</b> - 根據數據量自動調整標籤顯示密度</li>
            <li><b>備用機制</b> - 當 DateAxisItem 失敗時自動使用自定義標籤</li>
        </ul>
        
        <h3 style="color: #ffffff;">🧪 測試步驟</h3>
        
        <p style="color: #00ff00; font-weight: bold;">
        1. 點擊下方按鈕開啟台股爬蟲界面<br>
        2. 切換到「圖表分析」分頁<br>
        3. 選擇不同的數據類型 (歷史指數資料/市場指數資訊/融資融券統計)<br>
        4. 選擇不同的圖表類型 (線圖/柱狀圖/面積圖)<br>
        5. 點擊「生成圖表」按鈕<br>
        6. 觀察橫軸是否正確顯示日期或標籤信息
        </p>
        """)
        layout.addWidget(info_text)
        
        # 測試按鈕
        test_btn = QPushButton("📈 開啟台股爬蟲 (測試圖表橫軸日期顯示)")
        test_btn.clicked.connect(self.open_crawler_dialog)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 成功提示
        success_label = QLabel("🎉 圖表橫軸日期顯示問題已修復，現在可以正確顯示時間軸信息！")
        success_label.setStyleSheet("color: #00ff00; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #2d4a2d; border: 1px solid #4a7c4a; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 初始化日誌
        self.log("📈 圖表橫軸日期顯示修復測試程式已啟動")
        self.log("✅ 歷史指數圖表 - 改進日期軸設置和備用標籤機制")
        self.log("✅ 市場指數圖表 - 智能顯示證券代號標籤")
        self.log("✅ 融資融券圖表 - 優化股票代號顯示")
        self.log("✅ 軸顏色設置 - 確保深色主題下清晰可見")
    
    def log(self, message):
        """添加日誌"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
    
    def get_timestamp(self):
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def open_crawler_dialog(self):
        """開啟爬蟲對話框"""
        try:
            self.log("📈 正在開啟台股爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 爬蟲界面已成功創建")
            self.log("📈 請測試以下功能：")
            self.log("   • 切換到「圖表分析」分頁")
            self.log("   • 選擇「歷史指數資料」測試日期軸")
            self.log("   • 選擇「市場指數資訊」測試證券代號軸")
            self.log("   • 選擇「融資融券統計」測試股票代號軸")
            self.log("   • 嘗試不同的圖表類型")
            self.log("   • 觀察橫軸標籤是否正確顯示")
            
            # 顯示對話框
            dialog.exec()
            
            self.log("✅ 爬蟲對話框已關閉")
            
        except Exception as e:
            self.log(f"❌ 開啟爬蟲界面失敗: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("圖表橫軸日期顯示修復測試")
    app.setApplicationVersion("1.0")
    
    print("📈 圖表橫軸日期顯示修復測試程式已啟動")
    print("✅ 歷史指數圖表 - 日期軸設置已改進")
    print("✅ 市場指數圖表 - 證券代號標籤已優化")
    print("✅ 融資融券圖表 - 股票代號顯示已改進")
    print("✅ 橫軸標籤現在可以正確顯示")
    
    # 創建主窗口
    window = ChartAxisTestWindow()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
