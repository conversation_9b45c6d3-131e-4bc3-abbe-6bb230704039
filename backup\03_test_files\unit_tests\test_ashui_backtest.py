#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿水一式回測系統測試腳本
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import os

# 導入回測系統
from ashui_backtest_system import AshiuBacktestSystem
from ashui_regression_optimizer import AshiuRegressionOptimizer

def create_sample_data():
    """創建示例數據用於測試"""
    print("🔧 創建示例數據...")
    
    # 確保數據目錄存在
    os.makedirs('data', exist_ok=True)
    
    # 創建數據庫
    conn = sqlite3.connect('data/price_data.db')
    
    # 創建表格
    conn.execute('''
    CREATE TABLE IF NOT EXISTS stock_prices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        stock_id TEXT NOT NULL,
        date TEXT NOT NULL,
        open REAL NOT NULL,
        high REAL NOT NULL,
        low REAL NOT NULL,
        close REAL NOT NULL,
        volume INTEGER NOT NULL,
        UNIQUE(stock_id, date)
    )
    ''')
    
    # 生成示例股票數據
    stock_ids = ['1101', '2330', '2317', '2454', '3008', '6505', '2412', '2881', '1303', '2002']
    start_date = datetime(2020, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    for stock_id in stock_ids:
        print(f"  生成 {stock_id} 的數據...")
        
        # 生成價格走勢
        days = (end_date - start_date).days
        base_price = np.random.uniform(20, 200)  # 基礎價格
        
        # 生成隨機走勢
        returns = np.random.normal(0.001, 0.03, days)  # 日報酬率
        prices = [base_price]
        
        for i in range(1, days):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(max(new_price, 1.0))  # 確保價格不為負
        
        # 生成OHLCV數據
        current_date = start_date
        for i, close_price in enumerate(prices):
            if current_date.weekday() >= 5:  # 跳過週末
                current_date += timedelta(days=1)
                continue
            
            # 生成OHLC
            volatility = np.random.uniform(0.01, 0.05)
            high = close_price * (1 + volatility)
            low = close_price * (1 - volatility)
            open_price = np.random.uniform(low, high)
            
            # 生成成交量
            base_volume = np.random.randint(100000, 1000000)
            volume_multiplier = np.random.uniform(0.5, 3.0)
            volume = int(base_volume * volume_multiplier)
            
            # 插入數據
            try:
                conn.execute('''
                INSERT OR REPLACE INTO stock_prices 
                (stock_id, date, open, high, low, close, volume)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (stock_id, current_date.strftime('%Y-%m-%d'), 
                     open_price, high, low, close_price, volume))
            except:
                pass
            
            current_date += timedelta(days=1)
    
    conn.commit()
    conn.close()
    print("✅ 示例數據創建完成")

def run_simple_backtest():
    """執行簡單回測測試"""
    print("\n🚀 開始執行阿水一式回測...")
    
    # 初始化回測系統
    backtest_system = AshiuBacktestSystem('data/price_data.db')
    
    # 設置回測參數
    backtest_system.start_date = "2022-01-01"
    backtest_system.end_date = "2023-12-31"
    backtest_system.holding_period = 10
    backtest_system.max_positions = 5
    backtest_system.initial_capital = 1000000
    
    print(f"📅 回測期間: {backtest_system.start_date} 到 {backtest_system.end_date}")
    print(f"💰 初始資金: {backtest_system.initial_capital:,} 元")
    print(f"📊 最大持股數: {backtest_system.max_positions}")
    print(f"⏰ 持有天數: {backtest_system.holding_period}")
    
    # 執行回測
    results = backtest_system.run_backtest()
    
    # 顯示結果
    print("\n📈 回測結果:")
    print("=" * 50)
    
    performance = results['performance']
    print(f"總交易次數: {performance.get('total_trades', 0)}")
    print(f"獲利交易: {performance.get('winning_trades', 0)}")
    print(f"虧損交易: {performance.get('losing_trades', 0)}")
    print(f"勝率: {performance.get('win_rate', 0)*100:.2f}%")
    print(f"平均報酬: {performance.get('avg_return', 0)*100:.2f}%")
    print(f"累積報酬: {performance.get('cumulative_return', 0)*100:.2f}%")
    print(f"夏普比率: {performance.get('sharpe_ratio', 0):.4f}")
    print(f"最大回撤: {performance.get('max_drawdown', 0)*100:.2f}%")
    
    return backtest_system, results

def run_factor_analysis(backtest_system):
    """執行因子分析"""
    print("\n🔍 執行因子分析...")
    
    factor_analysis = backtest_system.analyze_factors()
    
    print("\n📊 因子與報酬相關性:")
    print("-" * 30)
    if 'correlations' in factor_analysis:
        for factor, corr in factor_analysis['correlations'].items():
            print(f"{factor:15}: {corr:8.4f}")
    
    print("\n📈 評分組別表現:")
    print("-" * 40)
    if 'score_groups' in factor_analysis:
        for group, stats in factor_analysis['score_groups'].items():
            print(f"\n{group}:")
            print(f"  交易次數: {stats['count']}")
            print(f"  平均報酬: {stats['avg_return']*100:.2f}%")
            print(f"  勝率: {stats['win_rate']*100:.2f}%")
    
    return factor_analysis

def run_regression_optimization(backtest_system):
    """執行迴歸優化"""
    print("\n🤖 執行迴歸優化...")
    
    # 準備數據
    trades = [r for r in backtest_system.results if 'trade_return' in r]
    
    if len(trades) < 10:
        print("⚠️ 交易記錄不足，無法進行迴歸分析")
        return None
    
    # 初始化優化器
    optimizer = AshiuRegressionOptimizer()
    
    # 準備數據
    X, y = optimizer.prepare_data(backtest_system.results)
    print(f"📊 特徵數量: {X.shape[1]}")
    print(f"📊 樣本數量: {X.shape[0]}")
    
    # 訓練模型
    model_results = optimizer.train_models(X, y)
    
    # 優化權重
    optimized_weights = optimizer.optimize_scoring_weights(X, y)
    
    print("\n🎯 優化後的評分權重:")
    print("-" * 30)
    total_weight = sum(optimized_weights.values())
    for factor, weight in optimized_weights.items():
        percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
        print(f"{factor:15}: {weight:6.1f}分 ({percentage:5.1f}%)")
    
    # 生成優化代碼
    optimized_code = optimizer.generate_optimized_scoring_function()
    
    # 保存優化代碼
    with open('optimized_ashui_score.py', 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('# -*- coding: utf-8 -*-\n')
        f.write('"""\n')
        f.write('阿水一式優化後的評分函數\n')
        f.write('基於回測數據的迴歸分析結果生成\n')
        f.write('"""\n\n')
        f.write('from typing import Dict\n\n')
        f.write(optimized_code)
    
    print(f"\n💾 優化後的評分函數已保存到: optimized_ashui_score.py")
    
    return optimizer, model_results, optimized_weights

def compare_scoring_methods(backtest_system, optimized_weights):
    """比較原始評分和優化評分的效果"""
    print("\n⚖️ 比較原始評分 vs 優化評分...")
    
    trades = [r for r in backtest_system.results if 'trade_return' in r]
    
    if not trades:
        print("⚠️ 沒有交易記錄可供比較")
        return
    
    # 計算原始評分和優化評分
    original_scores = []
    optimized_scores = []
    returns = []
    
    for trade in trades:
        indicators = trade['indicators']
        returns.append(trade['trade_return'])
        
        # 原始評分
        original_score = backtest_system.calculate_ashui_score(indicators)
        original_scores.append(original_score)
        
        # 優化評分（簡化版本）
        opt_score = 0
        turnover = indicators.get('turnover', 0)
        bb_width = indicators.get('bb_width', 0)
        compression_days = indicators.get('compression_days', 0)
        volume_ratio = indicators.get('volume_ratio', 0)
        breakout_pct = indicators.get('breakout_pct', 0)
        
        # 使用優化權重計算
        if turnover >= 10000:
            opt_score += optimized_weights.get('turnover', 25)
        elif turnover >= 1000:
            opt_score += optimized_weights.get('turnover', 25) * 0.5
        
        if 0.08 <= bb_width <= 0.15:
            opt_score += optimized_weights.get('bb_width', 20)
        elif bb_width > 0:
            opt_score += optimized_weights.get('bb_width', 20) * 0.5
        
        if 5 <= compression_days <= 15:
            opt_score += optimized_weights.get('compression_days', 15)
        elif compression_days > 0:
            opt_score += optimized_weights.get('compression_days', 15) * 0.5
        
        if volume_ratio >= 3.0:
            opt_score += optimized_weights.get('volume_ratio', 20)
        elif volume_ratio >= 2.0:
            opt_score += optimized_weights.get('volume_ratio', 20) * 0.7
        
        if breakout_pct >= 5.0:
            opt_score += optimized_weights.get('breakout_pct', 15)
        elif breakout_pct > 0:
            opt_score += optimized_weights.get('breakout_pct', 15) * 0.5
        
        optimized_scores.append(min(opt_score, 100))
    
    # 計算相關性
    original_corr = np.corrcoef(original_scores, returns)[0, 1]
    optimized_corr = np.corrcoef(optimized_scores, returns)[0, 1]
    
    print(f"原始評分與報酬相關性: {original_corr:.4f}")
    print(f"優化評分與報酬相關性: {optimized_corr:.4f}")
    print(f"改善幅度: {((optimized_corr - original_corr) / abs(original_corr) * 100):+.2f}%")

def main():
    """主函數"""
    print("🎯 阿水一式策略回測系統測試")
    print("=" * 50)
    
    # 檢查是否存在數據庫
    if not os.path.exists('data/price_data.db'):
        create_sample_data()
    
    # 執行回測
    backtest_system, results = run_simple_backtest()
    
    # 因子分析
    factor_analysis = run_factor_analysis(backtest_system)
    
    # 迴歸優化
    optimization_results = run_regression_optimization(backtest_system)
    
    if optimization_results:
        optimizer, model_results, optimized_weights = optimization_results
        
        # 比較評分方法
        compare_scoring_methods(backtest_system, optimized_weights)
        
        print(f"\n🎉 回測分析完成！")
        print(f"📊 總共分析了 {len([r for r in backtest_system.results if 'trade_return' in r])} 筆交易")
        print(f"📈 最佳模型 R²: {optimizer.best_model['cv_mean']:.4f}")
        print(f"💾 優化結果已保存到 optimized_ashui_score.py")
        
        # 啟動GUI（可選）
        try:
            from ashui_backtest_gui import main as gui_main
            print(f"\n🖥️ 啟動GUI界面...")
            gui_main()
        except ImportError:
            print(f"\n⚠️ GUI依賴未安裝，跳過GUI啟動")
    
    print(f"\n✅ 測試完成！")

if __name__ == '__main__':
    main()
