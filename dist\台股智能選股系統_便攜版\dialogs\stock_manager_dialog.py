#!/usr/bin/env python3
"""
股票管理對話框模組
從原始程式中提取的StockManagerDialog類別
"""
import logging
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QLineEdit, QMessageBox,
    QDialogButtonBox, QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class StockManagerDialog(QDialog):
    """股票管理對話框 - 用於添加/移除監控股票"""

    def __init__(self, parent=None, stock_list=None):
        super().__init__(parent)
        self.stock_list = stock_list or []
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("⚙️ 監控股票管理")
        self.setGeometry(200, 200, 600, 500)

        # 設置樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QListWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555555;
                selection-background-color: #404040;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                border-radius: 3px;
            }
            QLabel {
                color: #ffffff;
            }
        """)

        layout = QVBoxLayout(self)

        # 標題
        title_label = QLabel("📊 管理監控股票列表")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #51cf66; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 添加股票區域
        add_group = QGroupBox("➕ 添加股票")
        add_layout = QHBoxLayout(add_group)

        self.add_stock_input = QLineEdit()
        self.add_stock_input.setPlaceholderText("輸入股票代碼 (例: 2330)")
        add_layout.addWidget(self.add_stock_input)

        self.add_btn = QPushButton("添加")
        self.add_btn.clicked.connect(self.add_stock)
        self.add_btn.setStyleSheet("background-color: #28a745;")
        add_layout.addWidget(self.add_btn)

        layout.addWidget(add_group)

        # 股票列表區域
        list_group = QGroupBox(f"📋 當前監控股票 ({len(self.stock_list)} 支)")
        list_layout = QVBoxLayout(list_group)

        self.stock_list_widget = QListWidget()
        self.populate_stock_list()
        list_layout.addWidget(self.stock_list_widget)

        # 列表操作按鈕
        list_btn_layout = QHBoxLayout()

        self.remove_btn = QPushButton("🗑️ 移除選中")
        self.remove_btn.clicked.connect(self.remove_selected_stock)
        self.remove_btn.setStyleSheet("background-color: #dc3545;")
        list_btn_layout.addWidget(self.remove_btn)

        self.clear_all_btn = QPushButton("🗑️ 清空全部")
        self.clear_all_btn.clicked.connect(self.clear_all_stocks)
        self.clear_all_btn.setStyleSheet("background-color: #6c757d;")
        list_btn_layout.addWidget(self.clear_all_btn)

        list_btn_layout.addStretch()
        list_layout.addLayout(list_btn_layout)

        layout.addWidget(list_group)

        # 對話框按鈕
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.setStyleSheet("""
            QDialogButtonBox QPushButton {
                min-width: 80px;
                padding: 8px 16px;
            }
        """)
        layout.addWidget(button_box)

        # 連接回車鍵到添加功能
        self.add_stock_input.returnPressed.connect(self.add_stock)

    def populate_stock_list(self):
        """填充股票列表"""
        self.stock_list_widget.clear()
        for stock in self.stock_list:
            self.stock_list_widget.addItem(stock)

    def add_stock(self):
        """添加股票"""
        try:
            stock_code = self.add_stock_input.text().strip().upper()

            if not stock_code:
                QMessageBox.warning(self, "輸入錯誤", "請輸入股票代碼！")
                return

            # 簡單驗證股票代碼格式
            if not stock_code.isdigit() or len(stock_code) != 4:
                QMessageBox.warning(self, "格式錯誤", "股票代碼應為4位數字！")
                return

            # 檢查是否已存在
            if stock_code in self.stock_list:
                QMessageBox.information(self, "重複股票", f"股票 {stock_code} 已在監控列表中！")
                return

            # 添加到列表
            self.stock_list.append(stock_code)
            self.stock_list_widget.addItem(stock_code)

            # 清空輸入框
            self.add_stock_input.clear()

            # 更新標題
            self.update_group_title()

        except Exception as e:
            QMessageBox.critical(self, "添加錯誤", f"添加股票時發生錯誤：\n{str(e)}")

    def remove_selected_stock(self):
        """移除選中的股票"""
        try:
            current_item = self.stock_list_widget.currentItem()

            if not current_item:
                QMessageBox.warning(self, "選擇錯誤", "請先選擇要移除的股票！")
                return

            stock_code = current_item.text()

            reply = QMessageBox.question(self, "確認移除",
                                       f"確定要移除股票 {stock_code} 嗎？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                # 從列表中移除
                self.stock_list.remove(stock_code)

                # 從UI中移除
                row = self.stock_list_widget.row(current_item)
                self.stock_list_widget.takeItem(row)

                # 更新標題
                self.update_group_title()

        except Exception as e:
            QMessageBox.critical(self, "移除錯誤", f"移除股票時發生錯誤：\n{str(e)}")

    def clear_all_stocks(self):
        """清空所有股票"""
        try:
            if not self.stock_list:
                QMessageBox.information(self, "列表為空", "監控列表已經是空的！")
                return

            reply = QMessageBox.question(self, "確認清空",
                                       f"確定要清空所有 {len(self.stock_list)} 支股票嗎？\n\n此操作無法復原。",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                self.stock_list.clear()
                self.stock_list_widget.clear()
                self.update_group_title()
                QMessageBox.information(self, "清空完成", "✅ 所有股票已清空！")

        except Exception as e:
            QMessageBox.critical(self, "清空錯誤", f"清空股票時發生錯誤：\n{str(e)}")

    def update_group_title(self):
        """更新群組標題"""
        try:
            # 找到群組並更新標題
            for child in self.findChildren(QGroupBox):
                if "當前監控股票" in child.title():
                    child.setTitle(f"📋 當前監控股票 ({len(self.stock_list)} 支)")
                    break
        except Exception as e:
            pass  # 忽略標題更新錯誤

    def get_stock_list(self):
        """獲取股票列表"""
        return self.stock_list.copy()

if __name__ == "__main__":
    main()
