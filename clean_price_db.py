#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理 price.db 中的興櫃股票和權證資料
"""

import sqlite3
import os
from datetime import datetime

def clean_price_db():
    """清理 price.db 中的興櫃股票和權證資料"""
    print("=" * 80)
    print("🧹 清理 price.db 中的興櫃股票和權證資料")
    print("=" * 80)
    
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    if not os.path.exists(price_db_path):
        print("❌ price.db 不存在")
        return False
    
    try:
        # 備份資料庫
        backup_path = f'D:/Finlab/history/tables/price_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        print(f"📦 備份資料庫到: {backup_path}")
        
        import shutil
        shutil.copy2(price_db_path, backup_path)
        print("✅ 備份完成")
        
        # 連接資料庫
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查清理前的統計
        print(f"\n📊 清理前統計:")
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        total_before = cursor.fetchone()[0]
        print(f"   總記錄數: {total_before:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stocks_before = cursor.fetchone()[0]
        print(f"   股票數: {stocks_before:,}")
        
        # 統計要刪除的資料
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '興櫃'")
        rotc_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data WHERE listing_status = '興櫃'")
        rotc_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        warrant_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        warrant_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '權證'")
        warrant_status_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data WHERE listing_status = '權證'")
        warrant_status_stocks = cursor.fetchone()[0]
        
        print(f"\n🗑️ 將要刪除的資料:")
        print(f"   興櫃股票: {rotc_stocks:,} 檔, {rotc_records:,} 筆記錄")
        print(f"   權證 (7開頭): {warrant_stocks:,} 檔, {warrant_records:,} 筆記錄")
        print(f"   權證 (狀態): {warrant_status_stocks:,} 檔, {warrant_status_records:,} 筆記錄")
        
        total_to_delete = rotc_records + warrant_records + warrant_status_records
        print(f"   總計將刪除: {total_to_delete:,} 筆記錄")
        
        if total_to_delete == 0:
            print("✅ 沒有需要清理的資料")
            conn.close()
            return True
        
        # 確認清理
        response = input(f"\n確定要清理這些資料嗎? (y/n): ")
        if response.lower() != 'y':
            print("❌ 取消清理")
            conn.close()
            return False
        
        print(f"\n🧹 開始清理...")
        
        # 刪除興櫃股票
        if rotc_records > 0:
            print(f"🗑️ 刪除興櫃股票...")
            cursor.execute("DELETE FROM stock_daily_data WHERE listing_status = '興櫃'")
            deleted_rotc = cursor.rowcount
            print(f"   已刪除 {deleted_rotc:,} 筆興櫃股票記錄")
        
        # 刪除權證 (7開頭)
        if warrant_records > 0:
            print(f"🗑️ 刪除權證 (7開頭)...")
            cursor.execute("DELETE FROM stock_daily_data WHERE stock_id LIKE '7%'")
            deleted_warrant = cursor.rowcount
            print(f"   已刪除 {deleted_warrant:,} 筆權證記錄")
        
        # 刪除權證狀態的記錄
        if warrant_status_records > 0:
            print(f"🗑️ 刪除權證狀態記錄...")
            cursor.execute("DELETE FROM stock_daily_data WHERE listing_status = '權證'")
            deleted_warrant_status = cursor.rowcount
            print(f"   已刪除 {deleted_warrant_status:,} 筆權證狀態記錄")
        
        # 提交變更
        conn.commit()
        
        # 優化資料庫
        print(f"🔧 優化資料庫...")
        cursor.execute("VACUUM")
        
        # 檢查清理後的統計
        print(f"\n📊 清理後統計:")
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        total_after = cursor.fetchone()[0]
        print(f"   總記錄數: {total_after:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stocks_after = cursor.fetchone()[0]
        print(f"   股票數: {stocks_after:,}")
        
        # 檢查市場別分布
        print(f"\n📈 清理後市場別分布:")
        cursor.execute("""
            SELECT listing_status, COUNT(DISTINCT stock_id) as stock_count, COUNT(*) as record_count
            FROM stock_daily_data 
            GROUP BY listing_status 
            ORDER BY stock_count DESC
        """)
        market_stats = cursor.fetchall()
        
        for status, stock_count, record_count in market_stats:
            status_name = status if status else 'ETF/未分類'
            print(f"   {status_name}: {stock_count:,} 檔股票, {record_count:,} 筆記錄")
        
        # 驗證清理結果
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '興櫃'")
        remaining_rotc = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        remaining_warrant = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '權證'")
        remaining_warrant_status = cursor.fetchone()[0]
        
        print(f"\n✅ 清理驗證:")
        print(f"   剩餘興櫃記錄: {remaining_rotc:,}")
        print(f"   剩餘權證記錄: {remaining_warrant:,}")
        print(f"   剩餘權證狀態記錄: {remaining_warrant_status:,}")
        
        if remaining_rotc == 0 and remaining_warrant == 0 and remaining_warrant_status == 0:
            print(f"🎉 清理成功！已移除所有興櫃股票和權證資料")
        else:
            print(f"⚠️ 清理不完全，仍有部分資料殘留")
        
        # 檢查檔案大小變化
        file_size_after = os.path.getsize(price_db_path) / (1024 * 1024)  # MB
        backup_size = os.path.getsize(backup_path) / (1024 * 1024)  # MB
        size_reduction = backup_size - file_size_after
        
        print(f"\n📊 檔案大小變化:")
        print(f"   清理前: {backup_size:.2f} MB")
        print(f"   清理後: {file_size_after:.2f} MB")
        print(f"   減少: {size_reduction:.2f} MB ({size_reduction/backup_size*100:.1f}%)")
        
        conn.close()
        
        print(f"\n✅ 清理完成")
        print(f"📦 備份檔案: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_clean_db():
    """驗證清理後的資料庫"""
    print("=" * 80)
    print("🔍 驗證清理後的 price.db")
    print("=" * 80)
    
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    if not os.path.exists(price_db_path):
        print("❌ price.db 不存在")
        return
    
    try:
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查是否還有興櫃和權證資料
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '興櫃'")
        rotc_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id LIKE '7%'")
        warrant_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE listing_status = '權證'")
        warrant_status_count = cursor.fetchone()[0]
        
        print(f"🔍 驗證結果:")
        print(f"   興櫃股票記錄: {rotc_count:,}")
        print(f"   權證記錄 (7開頭): {warrant_count:,}")
        print(f"   權證狀態記錄: {warrant_status_count:,}")
        
        if rotc_count == 0 and warrant_count == 0 and warrant_status_count == 0:
            print(f"✅ 驗證通過：資料庫中已無興櫃股票和權證資料")
        else:
            print(f"⚠️ 驗證失敗：資料庫中仍有不應存在的資料")
        
        # 檢查保留的資料類型
        cursor.execute("""
            SELECT listing_status, COUNT(DISTINCT stock_id) as stock_count
            FROM stock_daily_data 
            GROUP BY listing_status 
            ORDER BY stock_count DESC
        """)
        market_stats = cursor.fetchall()
        
        print(f"\n📊 保留的股票類型:")
        for status, stock_count in market_stats:
            status_name = status if status else 'ETF/未分類'
            print(f"   {status_name}: {stock_count:,} 檔")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 驗證過程發生錯誤: {e}")

if __name__ == "__main__":
    # 清理資料庫
    if clean_price_db():
        print("\n" + "=" * 80)
        # 驗證清理結果
        verify_clean_db()
