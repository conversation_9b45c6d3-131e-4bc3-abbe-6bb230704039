#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息策略報告生成器
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib 未安裝，將跳過圖表生成")

from goodinfo_data_system import GoodInfoDataSystem

class DividendStrategyReport:
    def __init__(self, db_path="goodinfo_data.db"):
        self.db_path = db_path
        self.system = GoodInfoDataSystem(db_path)
    
    def generate_weekly_report(self):
        """生成週報"""
        print("📊 生成除權息週報...")
        
        # 更新資料
        self.system.update_dividend_schedule()
        strategies = self.system.analyze_dividend_strategy(days_ahead=14)
        
        # 生成報告
        report = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_stocks': len(strategies),
            'buy_recommendations': [],
            'hold_recommendations': [],
            'avoid_recommendations': [],
            'high_yield_stocks': [],
            'risk_analysis': {}
        }
        
        # 分類建議
        for strategy in strategies:
            if strategy['recommendation'] == 'BUY':
                report['buy_recommendations'].append(strategy)
            elif strategy['recommendation'] == 'HOLD':
                report['hold_recommendations'].append(strategy)
            else:
                report['avoid_recommendations'].append(strategy)
            
            # 高殖利率股票 (現金股利 >= 2元)
            if strategy['cash_dividend'] >= 2.0:
                report['high_yield_stocks'].append(strategy)
        
        # 風險分析
        risk_levels = {}
        for strategy in strategies:
            risk = strategy['risk_level']
            if risk not in risk_levels:
                risk_levels[risk] = 0
            risk_levels[risk] += 1
        
        report['risk_analysis'] = risk_levels
        
        # 生成文字報告
        self.generate_text_report(report)
        
        # 生成圖表
        self.generate_charts(strategies)
        
        return report
    
    def generate_text_report(self, report):
        """生成文字報告"""
        report_text = f"""
# 除權息策略週報
**報告日期**: {report['date']}

## 📊 總覽
- 分析股票總數: {report['total_stocks']} 檔
- 建議買入: {len(report['buy_recommendations'])} 檔
- 建議持有: {len(report['hold_recommendations'])} 檔  
- 建議避開: {len(report['avoid_recommendations'])} 檔
- 高殖利率股票: {len(report['high_yield_stocks'])} 檔

## 🎯 建議買入清單

"""
        
        # 建議買入的股票
        if report['buy_recommendations']:
            buy_sorted = sorted(report['buy_recommendations'], 
                              key=lambda x: x['expected_return'], reverse=True)
            
            for i, stock in enumerate(buy_sorted[:10]):  # 前10檔
                report_text += f"""
### {i+1}. {stock['stock_code']} {stock['stock_name']}
- **現金股利**: {stock['cash_dividend']}元
- **股票股利**: {stock['stock_dividend']}元  
- **總股利**: {stock['total_dividend']}元
- **預期報酬**: {stock['expected_return']:.2f}元
- **風險等級**: {stock['risk_level']}
- **分析理由**: {stock['reason']}

"""
        else:
            report_text += "目前無建議買入的股票。\n\n"
        
        # 高殖利率股票
        if report['high_yield_stocks']:
            report_text += "## 💰 高殖利率股票\n\n"
            high_yield_sorted = sorted(report['high_yield_stocks'], 
                                     key=lambda x: x['cash_dividend'], reverse=True)
            
            for stock in high_yield_sorted[:5]:
                report_text += f"- {stock['stock_code']} {stock['stock_name']}: {stock['cash_dividend']}元\n"
        
        # 風險分析
        report_text += f"""
## ⚠️ 風險分析
- 低風險: {report['risk_analysis'].get('LOW', 0)} 檔
- 中風險: {report['risk_analysis'].get('MEDIUM', 0)} 檔  
- 高風險: {report['risk_analysis'].get('HIGH', 0)} 檔

## 📈 投資策略建議

### 積極型投資者
建議關注建議買入清單中預期報酬較高的股票，特別是大型權值股和ETF。

### 穩健型投資者  
建議選擇現金股利較高、風險等級為低或中等的股票。

### 保守型投資者
建議避開股票股利比例過高的股票，專注於現金殖利率穩定的標的。

## ⚠️ 風險提醒
1. 除權息後股價會相應調整，需注意填息能力
2. 股票股利會稀釋每股價值，需評估公司成長性
3. 市場環境變化可能影響填息表現
4. 建議分散投資，避免集中單一標的

---
*本報告僅供參考，投資前請詳細評估個人風險承受能力*
"""
        
        # 儲存報告
        filename = f"dividend_strategy_report_{datetime.now().strftime('%Y%m%d')}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print(f"📄 文字報告已儲存: {filename}")
        
        return report_text
    
    def generate_charts(self, strategies):
        """生成圖表"""
        if not strategies or not MATPLOTLIB_AVAILABLE:
            if not MATPLOTLIB_AVAILABLE:
                print("⚠️ matplotlib 未安裝，跳過圖表生成")
            return

        # 設定中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 創建圖表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('除權息策略分析圖表', fontsize=16, fontweight='bold')
        
        # 圖表1: 建議分布
        recommendations = [s['recommendation'] for s in strategies]
        rec_counts = pd.Series(recommendations).value_counts()
        
        axes[0, 0].pie(rec_counts.values, labels=rec_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('投資建議分布')
        
        # 圖表2: 風險等級分布
        risk_levels = [s['risk_level'] for s in strategies]
        risk_counts = pd.Series(risk_levels).value_counts()
        
        axes[0, 1].bar(risk_counts.index, risk_counts.values)
        axes[0, 1].set_title('風險等級分布')
        axes[0, 1].set_ylabel('股票數量')
        
        # 圖表3: 現金股利分布
        cash_dividends = [s['cash_dividend'] for s in strategies if s['cash_dividend'] > 0]
        if cash_dividends:
            axes[1, 0].hist(cash_dividends, bins=20, alpha=0.7)
            axes[1, 0].set_title('現金股利分布')
            axes[1, 0].set_xlabel('現金股利 (元)')
            axes[1, 0].set_ylabel('股票數量')
        
        # 圖表4: 預期報酬 vs 現金股利
        cash_div = [s['cash_dividend'] for s in strategies]
        expected_ret = [s['expected_return'] for s in strategies]
        colors = ['red' if s['recommendation'] == 'BUY' else 
                 'orange' if s['recommendation'] == 'HOLD' else 'gray' 
                 for s in strategies]
        
        scatter = axes[1, 1].scatter(cash_div, expected_ret, c=colors, alpha=0.6)
        axes[1, 1].set_title('預期報酬 vs 現金股利')
        axes[1, 1].set_xlabel('現金股利 (元)')
        axes[1, 1].set_ylabel('預期報酬 (元)')
        
        # 添加圖例
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='red', label='建議買入'),
                          Patch(facecolor='orange', label='建議持有'),
                          Patch(facecolor='gray', label='建議避開')]
        axes[1, 1].legend(handles=legend_elements)
        
        plt.tight_layout()
        
        # 儲存圖表
        chart_filename = f"dividend_strategy_charts_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 圖表已儲存: {chart_filename}")
    
    def get_next_week_schedule(self):
        """獲取下週除權息時程"""
        print("📅 查詢下週除權息時程...")
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 查詢即將除權息的股票
            cursor.execute('''
                SELECT stock_code, stock_name, ex_dividend_date, ex_right_date,
                       cash_dividend, stock_dividend, total_dividend
                FROM dividend_schedule 
                WHERE (ex_dividend_date LIKE '%即將%' OR ex_right_date LIKE '%即將%')
                   AND (cash_dividend > 0 OR stock_dividend > 0)
                ORDER BY total_dividend DESC
                LIMIT 20
            ''')
            
            results = cursor.fetchall()
            
            if results:
                print(f"✅ 找到 {len(results)} 檔即將除權息的股票")
                
                schedule_text = "\n# 下週除權息時程\n\n"
                schedule_text += "| 股票代號 | 股票名稱 | 除息日 | 除權日 | 現金股利 | 股票股利 | 總股利 |\n"
                schedule_text += "|---------|---------|--------|--------|----------|----------|--------|\n"
                
                for row in results:
                    stock_code, stock_name, ex_div_date, ex_right_date, cash_div, stock_div, total_div = row
                    schedule_text += f"| {stock_code} | {stock_name} | {ex_div_date} | {ex_right_date} | {cash_div} | {stock_div} | {total_div} |\n"
                
                # 儲存時程表
                schedule_filename = f"next_week_dividend_schedule_{datetime.now().strftime('%Y%m%d')}.md"
                with open(schedule_filename, 'w', encoding='utf-8') as f:
                    f.write(schedule_text)
                
                print(f"📅 下週時程表已儲存: {schedule_filename}")
                return results
            else:
                print("❌ 未找到下週除權息資料")
                return []

def main():
    """主程式"""
    print("🚀 啟動除權息策略報告系統...")
    
    reporter = DividendStrategyReport()
    
    # 生成週報
    print("\n" + "="*60)
    report = reporter.generate_weekly_report()
    
    # 生成下週時程
    print("\n" + "="*60)
    schedule = reporter.get_next_week_schedule()
    
    print(f"\n✅ 報告生成完成!")
    print(f"📊 分析了 {report['total_stocks']} 檔股票")
    print(f"🎯 建議買入 {len(report['buy_recommendations'])} 檔")
    print(f"📅 下週有 {len(schedule)} 檔股票除權息")

if __name__ == "__main__":
    main()
