#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較現金流量表和統一財務資料的差異
"""

import sqlite3
import pandas as pd
import os

def compare_data_sources():
    """比較兩個資料來源"""
    
    print("=" * 80)
    print("📊 現金流量表 vs 統一財務資料比較分析")
    print("=" * 80)
    
    # 檢查現金流量表
    print("💰 現金流量表 (cash_flows.db):")
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    if os.path.exists(cash_flows_db):
        conn = sqlite3.connect(cash_flows_db)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(cash_flows)")
        cash_columns = [col[1] for col in cursor.fetchall()]
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        cash_count = cursor.fetchone()[0]
        
        print(f"   📊 資料筆數: {cash_count:,}")
        print(f"   📋 欄位數量: {len(cash_columns)}")
        
        # 找出現金流量相關欄位
        cash_flow_items = [col for col in cash_columns if '現金' in col or '流量' in col]
        operating_items = [col for col in cash_columns if '營業活動' in col]
        investing_items = [col for col in cash_columns if '投資活動' in col]
        financing_items = [col for col in cash_columns if '籌資活動' in col]
        
        print(f"   💰 現金流量項目: {len(cash_flow_items)} 個")
        print(f"   🏭 營業活動項目: {len(operating_items)} 個")
        print(f"   📈 投資活動項目: {len(investing_items)} 個")
        print(f"   💳 籌資活動項目: {len(financing_items)} 個")
        
        # 顯示主要現金流量項目
        main_cash_items = [
            '營業活動之淨現金流入（流出）',
            '投資活動之淨現金流入（流出）',
            '籌資活動之淨現金流入（流出）',
            '期末現金及約當現金餘額'
        ]
        
        print(f"   📋 主要現金流量項目:")
        for item in main_cash_items:
            if item in cash_columns:
                print(f"      ✅ {item}")
            else:
                print(f"      ❌ {item}")
        
        conn.close()
    else:
        print("   ❌ 現金流量表檔案不存在")
    
    print()
    
    # 檢查統一財務資料
    print("📈 統一財務資料 (financial_data.db):")
    financial_db = r'D:\Finlab\history\tables\financial_data.db'
    
    if os.path.exists(financial_db):
        conn = sqlite3.connect(financial_db)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(financial_data)")
        financial_columns = [col[1] for col in cursor.fetchall()]
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        financial_count = cursor.fetchone()[0]
        
        print(f"   📊 資料筆數: {financial_count:,}")
        print(f"   📋 欄位數量: {len(financial_columns)}")
        
        # 分類檢查欄位
        income_items = [col for col in financial_columns if col.startswith('income_')]
        balance_items = [col for col in financial_columns if col.startswith('balance_')]
        cash_flow_items = [col for col in financial_columns if '現金' in col or '流量' in col]
        
        print(f"   📊 損益表項目: {len(income_items)} 個")
        print(f"   🏦 資產負債表項目: {len(balance_items)} 個")
        print(f"   💰 現金流量項目: {len(cash_flow_items)} 個")
        
        # 檢查是否包含現金流量表的主要項目
        main_cash_items = [
            'income_營業活動之淨現金流入（流出）',
            'income_投資活動之淨現金流入（流出）',
            'income_籌資活動之淨現金流入（流出）',
            'balance_現金及約當現金'
        ]
        
        print(f"   📋 現金流量相關項目檢查:")
        for item in main_cash_items:
            if item in financial_columns:
                print(f"      ✅ {item}")
            else:
                print(f"      ❌ {item}")
        
        # 顯示實際的現金流量項目
        if cash_flow_items:
            print(f"   📋 實際現金流量項目:")
            for item in cash_flow_items:
                print(f"      - {item}")
        
        conn.close()
    else:
        print("   ❌ 統一財務資料檔案不存在")

def analyze_data_coverage():
    """分析資料覆蓋範圍"""
    
    print(f"\n" + "=" * 80)
    print(f"🔍 資料覆蓋範圍分析")
    print("=" * 80)
    
    # 檢查現金流量表的資料來源
    print("💰 現金流量表資料來源:")
    print("   📡 來源: MOPS 公開資訊觀測站")
    print("   📊 內容: 專門的現金流量表")
    print("   📋 項目: 383 個詳細現金流量項目")
    print("   🎯 特色: 完整的現金流量分析")
    
    print(f"\n📈 統一財務資料來源:")
    print("   📡 來源: TWSE OpenAPI")
    print("   📊 內容: 綜合損益表 + 資產負債表")
    print("   📋 項目: 28 損益 + 21 資產負債")
    print("   🎯 特色: 官方 API，穩定可靠")

def provide_recommendation():
    """提供建議"""
    
    print(f"\n" + "=" * 80)
    print(f"💡 建議分析")
    print("=" * 80)
    
    print("🤔 現金流量表爬蟲是否還需要？")
    print()
    
    print("✅ **保留現金流量表爬蟲的理由**:")
    print("   1. 📊 **專業性**: 383 個詳細現金流量項目")
    print("   2. 🔍 **深度分析**: 專門用於現金流量分析")
    print("   3. 💰 **完整性**: 包含營業/投資/籌資活動的詳細項目")
    print("   4. 📈 **補充性**: 與統一財務資料形成互補")
    print()
    
    print("❌ **移除現金流量表爬蟲的理由**:")
    print("   1. 🔄 **重複性**: 部分資料與統一財務資料重疊")
    print("   2. 🛠️ **維護成本**: 需要維護兩套爬蟲系統")
    print("   3. ⚠️ **穩定性**: MOPS 網站爬取較不穩定")
    print("   4. 📁 **檔案管理**: 增加檔案管理複雜度")
    print()
    
    print("🎯 **最終建議**:")
    print("   根據你的使用需求決定:")
    print()
    print("   📊 **如果你需要詳細的現金流量分析**:")
    print("      → 保留 cash_flows 爬蟲")
    print("      → 用於專業的現金流量分析")
    print("      → 與統一財務資料互補使用")
    print()
    print("   🎯 **如果你主要做基本財務分析**:")
    print("      → 可以移除 cash_flows 爬蟲")
    print("      → 統一財務資料已足夠")
    print("      → 簡化系統維護")
    print()
    
    print("💭 **我的建議**: ")
    print("   考慮到 TWSE OpenAPI 的穩定性和統一財務資料的完整性，")
    print("   如果你不需要進行深度的現金流量分析，可以考慮:")
    print("   1. 暫時保留 cash_flows 爬蟲但停用")
    print("   2. 觀察統一財務資料是否滿足需求")
    print("   3. 如果滿足需求，再移除 cash_flows 爬蟲")

def show_usage_comparison():
    """顯示使用方式比較"""
    
    print(f"\n" + "=" * 80)
    print(f"📝 使用方式比較")
    print("=" * 80)
    
    print("💰 現金流量表查詢:")
    print("```sql")
    print("-- 查詢台積電現金流量")
    print("SELECT stock_id, stock_name, ")
    print("       營業活動之淨現金流入（流出）,")
    print("       投資活動之淨現金流入（流出）,")
    print("       籌資活動之淨現金流入（流出）")
    print("FROM cash_flows WHERE stock_id = '2330';")
    print("```")
    print()
    
    print("📈 統一財務資料查詢:")
    print("```sql")
    print("-- 查詢台積電財務資料 (包含部分現金流量)")
    print("SELECT stock_id, stock_name,")
    print("       income_營業收入, income_本期淨利（淨損）,")
    print("       balance_資產總額, balance_現金及約當現金")
    print("FROM financial_data WHERE stock_id = '2330';")
    print("```")

def main():
    """主函數"""
    
    print("🔍 現金流量表爬蟲需求分析")
    
    # 比較資料來源
    compare_data_sources()
    
    # 分析資料覆蓋範圍
    analyze_data_coverage()
    
    # 提供建議
    provide_recommendation()
    
    # 顯示使用方式比較
    show_usage_comparison()
    
    print(f"\n" + "=" * 80)
    print(f"📊 總結")
    print("=" * 80)
    print("🤔 現金流量表爬蟲是否還需要？")
    print("💡 答案取決於你的分析需求深度")
    print("🎯 建議先保留，觀察統一財務資料是否足夠")

if __name__ == "__main__":
    main()
