#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試CANSLIM策略與PE數據庫的整合
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

def create_test_data():
    """創建測試股價數據"""
    dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
    dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
    
    # 生成上漲趨勢的股價數據
    np.random.seed(42)
    base_price = 100
    prices = []
    volumes = []
    
    for i, date in enumerate(dates):
        # 生成有上漲趨勢的價格
        trend = 0.001  # 每日平均上漲0.1%
        volatility = np.random.normal(0, 0.02)  # 2%波動率
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + volatility)
        
        prices.append(max(price, 1.0))  # 確保價格不為負
        
        # 生成成交量（高成交量）
        base_volume = 1000000
        volume_multiplier = np.random.uniform(0.8, 2.5)
        volumes.append(int(base_volume * volume_multiplier))
    
    # 創建OHLC數據
    df_data = []
    for i, (date, close, volume) in enumerate(zip(dates, prices, volumes)):
        high = close * np.random.uniform(1.0, 1.03)
        low = close * np.random.uniform(0.97, 1.0)
        open_price = np.random.uniform(low, high)
        
        df_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': volume
        })
    
    return pd.DataFrame(df_data)

def create_test_pe_data():
    """創建測試PE數據"""
    return {
        'stock_id': '2330',
        'date': '20241210',
        'pe_ratio': 18.5,  # 合理的本益比
        'dividend_yield': 2.8,  # 不錯的殖利率
        'pb_ratio': 1.2,  # 合理的股價淨值比
        'market_cap': 15000,  # 大型股市值
        'is_simulated': False  # 真實數據
    }

def create_simulated_pe_data():
    """創建模擬PE數據"""
    return {
        'stock_id': '2330',
        'date': '20241210',
        'pe_ratio': 20.0,
        'dividend_yield': 2.5,
        'pb_ratio': 1.5,
        'market_cap': 12000,
        'is_simulated': True  # 模擬數據
    }

def test_canslim_with_real_pe_data():
    """測試CANSLIM策略使用真實PE數據"""
    print("🧪 測試CANSLIM策略 - 真實PE數據")
    print("=" * 50)
    
    try:
        from strategies.canslim_strategy import CANSLIMStrategy
        
        # 創建策略實例
        canslim = CANSLIMStrategy()
        
        # 創建測試數據
        df = create_test_data()
        pe_data = create_test_pe_data()
        
        print(f"📊 測試數據:")
        print(f"  股價數據: {len(df)} 天")
        print(f"  最新價格: {df['Close'].iloc[-1]:.2f}")
        print(f"  PE數據: 本益比 {pe_data['pe_ratio']}, 殖利率 {pe_data['dividend_yield']}%")
        print(f"  數據來源: {'真實數據' if not pe_data['is_simulated'] else '模擬數據'}")
        
        # 執行分析
        result = canslim.analyze_stock(df, pe_data=pe_data, stock_id='2330')
        
        print(f"\n📈 分析結果:")
        print(f"  是否符合: {result['suitable']}")
        print(f"  總分: {result['score']}/100")
        print(f"  真實數據比例: {result.get('real_data_ratio', 0)*100:.0f}%")
        print(f"  數據來源: {result.get('data_sources', [])}")
        print(f"  原因: {result['reason']}")
        
        # 詳細分析各項目
        print(f"\n📋 詳細分析:")
        details = result.get('details', {})
        for key, value in details.items():
            status = "✅" if value['pass'] else "❌"
            print(f"  {status} {key}: {value['reason']} ({value['score']}分)")
        
        return result
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return None

def test_canslim_with_simulated_pe_data():
    """測試CANSLIM策略使用模擬PE數據"""
    print("\n🧪 測試CANSLIM策略 - 模擬PE數據")
    print("=" * 50)
    
    try:
        from strategies.canslim_strategy import CANSLIMStrategy
        
        # 創建策略實例
        canslim = CANSLIMStrategy()
        
        # 創建測試數據
        df = create_test_data()
        pe_data = create_simulated_pe_data()
        
        print(f"📊 測試數據:")
        print(f"  股價數據: {len(df)} 天")
        print(f"  最新價格: {df['Close'].iloc[-1]:.2f}")
        print(f"  PE數據: 本益比 {pe_data['pe_ratio']}, 殖利率 {pe_data['dividend_yield']}%")
        print(f"  數據來源: {'真實數據' if not pe_data['is_simulated'] else '模擬數據'}")
        
        # 執行分析
        result = canslim.analyze_stock(df, pe_data=pe_data, stock_id='2330')
        
        print(f"\n📈 分析結果:")
        print(f"  是否符合: {result['suitable']}")
        print(f"  總分: {result['score']}/100")
        print(f"  真實數據比例: {result.get('real_data_ratio', 0)*100:.0f}%")
        print(f"  數據來源: {result.get('data_sources', [])}")
        print(f"  原因: {result['reason']}")
        
        return result
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return None

def test_canslim_without_pe_data():
    """測試CANSLIM策略不使用PE數據"""
    print("\n🧪 測試CANSLIM策略 - 無PE數據")
    print("=" * 50)
    
    try:
        from strategies.canslim_strategy import CANSLIMStrategy
        
        # 創建策略實例
        canslim = CANSLIMStrategy()
        
        # 創建測試數據
        df = create_test_data()
        
        print(f"📊 測試數據:")
        print(f"  股價數據: {len(df)} 天")
        print(f"  最新價格: {df['Close'].iloc[-1]:.2f}")
        print(f"  PE數據: 無")
        
        # 執行分析（不傳遞PE數據）
        result = canslim.analyze_stock(df)
        
        print(f"\n📈 分析結果:")
        print(f"  是否符合: {result['suitable']}")
        print(f"  總分: {result['score']}/100")
        print(f"  真實數據比例: {result.get('real_data_ratio', 0)*100:.0f}%")
        print(f"  數據來源: {result.get('data_sources', [])}")
        print(f"  原因: {result['reason']}")
        
        return result
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return None

def compare_results(real_result, simulated_result, no_pe_result):
    """比較不同數據來源的結果"""
    print("\n📊 結果比較分析")
    print("=" * 50)
    
    if not all([real_result, simulated_result, no_pe_result]):
        print("❌ 部分測試失敗，無法進行比較")
        return
    
    print(f"📈 評分比較:")
    print(f"  真實PE數據: {real_result['score']}/100")
    print(f"  模擬PE數據: {simulated_result['score']}/100")
    print(f"  無PE數據:   {no_pe_result['score']}/100")
    
    print(f"\n📊 真實數據比例:")
    print(f"  真實PE數據: {real_result.get('real_data_ratio', 0)*100:.0f}%")
    print(f"  模擬PE數據: {simulated_result.get('real_data_ratio', 0)*100:.0f}%")
    print(f"  無PE數據:   {no_pe_result.get('real_data_ratio', 0)*100:.0f}%")
    
    print(f"\n🎯 結論:")
    if real_result.get('real_data_ratio', 0) > 0:
        print("✅ CANSLIM策略已成功整合PE數據庫")
        print("✅ 使用真實PE數據時，策略達到部分真實數據分析")
        print("✅ 數據來源標記功能正常")
    else:
        print("⚠️ PE數據整合可能存在問題")

def main():
    """主函數"""
    print("🎯 CANSLIM策略PE數據整合測試")
    print("=" * 60)
    
    # 測試三種情況
    real_result = test_canslim_with_real_pe_data()
    simulated_result = test_canslim_with_simulated_pe_data()
    no_pe_result = test_canslim_without_pe_data()
    
    # 比較結果
    compare_results(real_result, simulated_result, no_pe_result)
    
    print(f"\n🎉 測試完成！")
    print(f"⏰ 完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
