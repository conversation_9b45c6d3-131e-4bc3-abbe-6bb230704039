#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復 date_range.pickle 中的日期範圍記錄
"""

import pandas as pd
import pickle
import os

def fix_date_range():
    """修復日期範圍記錄"""
    
    print("🔧 修復 date_range.pickle")
    print("=" * 50)
    
    date_range_file = "history/date_range.pickle"
    price_file = "history/tables/price.pkl"
    
    # 1. 讀取實際的 price.pkl 資料
    print("1️⃣ 讀取實際 price.pkl 資料...")
    try:
        data = pd.read_pickle(price_file)
        dates = data.index.get_level_values('date')
        actual_start = dates.min()
        actual_end = dates.max()
        
        print(f"✅ 實際日期範圍: {actual_start} 至 {actual_end}")
        print(f"   總筆數: {len(data):,}")
        
    except Exception as e:
        print(f"❌ 讀取 price.pkl 失敗: {str(e)}")
        return False
    
    # 2. 讀取當前的 date_range.pickle
    print("\n2️⃣ 檢查當前 date_range.pickle...")
    try:
        if os.path.exists(date_range_file):
            with open(date_range_file, 'rb') as f:
                date_ranges = pickle.load(f)
            
            print("✅ 當前記錄的日期範圍:")
            for table, (start, end) in date_ranges.items():
                print(f"   {table}: {start} 至 {end}")
            
            if 'price' in date_ranges:
                current_start, current_end = date_ranges['price']
                print(f"\n❌ price 記錄錯誤:")
                print(f"   記錄: {current_start} 至 {current_end}")
                print(f"   實際: {actual_start} 至 {actual_end}")
        else:
            print("⚠️ date_range.pickle 不存在，將創建新檔案")
            date_ranges = {}
            
    except Exception as e:
        print(f"❌ 讀取 date_range.pickle 失敗: {str(e)}")
        date_ranges = {}
    
    # 3. 更新 price 的日期範圍
    print("\n3️⃣ 更新 price 日期範圍...")
    date_ranges['price'] = (actual_start, actual_end)
    
    # 4. 保存更新後的記錄
    print("4️⃣ 保存更新後的記錄...")
    try:
        with open(date_range_file, 'wb') as f:
            pickle.dump(date_ranges, f)
        
        print("✅ date_range.pickle 已更新")
        
        # 5. 驗證更新結果
        print("\n5️⃣ 驗證更新結果...")
        with open(date_range_file, 'rb') as f:
            updated_ranges = pickle.load(f)
        
        print("✅ 更新後的記錄:")
        for table, (start, end) in updated_ranges.items():
            print(f"   {table}: {start} 至 {end}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失敗: {str(e)}")
        return False

def main():
    """主函數"""
    success = fix_date_range()
    
    if success:
        print("\n🎉 修復成功！")
        print("💡 現在 auto_update.py 會顯示正確的日期範圍")
        print("✨ 可以重新運行 auto_update.py 測試")
    else:
        print("\n❌ 修復失敗")

if __name__ == "__main__":
    main()
