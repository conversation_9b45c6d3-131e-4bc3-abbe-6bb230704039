#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ROE資料下載器GUI
基於實際GoodInfo表格界面設計的完整下載系統
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
from datetime import datetime
import sqlite3
import pandas as pd
from goodinfo_roe_csv_downloader import GoodinfoROECSVDownloader

class ROEDataDownloaderGUI:
    def __init__(self, parent=None):
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()
        
        self.setup_window()
        self.create_widgets()
        self.downloader = GoodinfoROECSVDownloader()
        
    def setup_window(self):
        """設置視窗"""
        self.window.title("📊 年度ROE資料下載器")
        self.window.geometry("900x750")  # 增大窗口尺寸
        self.window.resizable(True, True)
        
        # 設置圖標
        try:
            self.window.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """創建界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題 - 壓縮版本
        title_label = ttk.Label(main_frame, text="📊 年度ROE資料下載器",
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # 說明文字 - 簡化版本
        desc_text = "從GoodInfo網站下載年度ROE最高排行資料，使用Selenium模擬點擊\"匯出CSV\"功能 | 資料來源: https://goodinfo.tw"
        desc_label = ttk.Label(main_frame, text=desc_text,
                              foreground="#666666", font=("Arial", 8))
        desc_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # 年份選擇 - 支援年度區間 (壓縮padding)
        year_frame = ttk.LabelFrame(main_frame, text="📅 資料年份", padding="5")
        year_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        year_frame.columnconfigure(1, weight=1)

        # 年度區間選擇
        ttk.Label(year_frame, text="下載模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.year_mode_var = tk.StringVar(value="單一年度")
        year_mode_combo = ttk.Combobox(year_frame, textvariable=self.year_mode_var, width=12, state="readonly")
        year_mode_combo['values'] = ["單一年度", "年度區間"]
        year_mode_combo.grid(row=0, column=1, sticky=tk.W)
        year_mode_combo.bind('<<ComboboxSelected>>', self.on_year_mode_change)

        # 單一年度選擇
        self.single_year_frame = ttk.Frame(year_frame)
        self.single_year_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(self.single_year_frame, text="目標年份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.year_var = tk.StringVar(value=str(datetime.now().year))
        self.year_combo = ttk.Combobox(self.single_year_frame, textvariable=self.year_var, width=10)
        self.year_combo['values'] = [str(year) for year in range(2020, datetime.now().year + 1)]
        self.year_combo.grid(row=0, column=1, sticky=tk.W)

        # 年度區間選擇
        self.range_year_frame = ttk.Frame(year_frame)
        self.range_year_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(self.range_year_frame, text="起始年份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.start_year_var = tk.StringVar(value="2020")
        start_year_combo = ttk.Combobox(self.range_year_frame, textvariable=self.start_year_var, width=10)
        start_year_combo['values'] = [str(year) for year in range(2020, datetime.now().year + 1)]
        start_year_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(self.range_year_frame, text="結束年份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))

        self.end_year_var = tk.StringVar(value=str(datetime.now().year))
        end_year_combo = ttk.Combobox(self.range_year_frame, textvariable=self.end_year_var, width=10)
        end_year_combo['values'] = [str(year) for year in range(2020, datetime.now().year + 1)]
        end_year_combo.grid(row=0, column=3, sticky=tk.W)

        # 初始隱藏年度區間選擇
        self.range_year_frame.grid_remove()
        
        # 下載選項 (壓縮padding)
        options_frame = ttk.LabelFrame(main_frame, text="⚙️ 下載選項", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        options_frame.columnconfigure(1, weight=1)

        # 資料範圍
        ttk.Label(options_frame, text="資料範圍:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.range_var = tk.StringVar(value="全部資料")  # 預設改為全部資料
        range_combo = ttk.Combobox(options_frame, textvariable=self.range_var, width=15, state="readonly")
        range_combo['values'] = ["前100名", "前300名", "前500名", "全部資料"]
        range_combo.grid(row=0, column=1, sticky=tk.W)
        
        # 儲存選項
        ttk.Label(options_frame, text="儲存格式:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        save_frame = ttk.Frame(options_frame)
        save_frame.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))

        self.save_db_var = tk.BooleanVar(value=True)  # 預設儲存到資料庫
        self.save_csv_var = tk.BooleanVar(value=True)  # 同時匯出CSV以便查看

        ttk.Checkbutton(save_frame, text="儲存到數據庫(.db檔)", variable=self.save_db_var).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(save_frame, text="匯出CSV檔案", variable=self.save_csv_var).pack(side=tk.LEFT)
        
        # 控制按鈕 (壓縮間距)
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)

        self.download_btn = ttk.Button(button_frame, text="🚀 開始下載",
                                      command=self.start_download, width=12)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.view_data_btn = ttk.Button(button_frame, text="📊 查看資料",
                                       command=self.view_data, width=12)
        self.view_data_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.open_folder_btn = ttk.Button(button_frame, text="📁 開啟資料夾",
                                         command=self.open_data_folder, width=12)
        self.open_folder_btn.pack(side=tk.LEFT)
        
        # 進度條 (壓縮padding)
        progress_frame = ttk.LabelFrame(main_frame, text="📈 下載進度", padding="5")
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.StringVar(value="準備就緒")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(3, 0))
        
        # 日誌區域 (增加高度，減少padding)
        log_frame = ttk.LabelFrame(main_frame, text="📝 下載日誌", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # 創建文字區域和滾動條
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        # 增加日誌文字區域的高度
        self.log_text = tk.Text(text_frame, height=15, wrap=tk.WORD,
                               font=("Consolas", 9), background="#f8f9fa")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        log_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 狀態列 (壓縮間距)
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        status_frame.columnconfigure(1, weight=1)

        self.status_var = tk.StringVar(value="就緒")
        ttk.Label(status_frame, text="狀態:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="#28a745").grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
        # 初始化日誌
        self.log_message("📊 ROE資料下載器已啟動 (改進版)")
        self.log_message("💡 請選擇年份和下載選項，然後點擊 '開始下載'")
        self.log_message("🆕 支援年度區間下載，可一次下載多年資料")
        self.log_message("✅ 預設儲存到資料庫(.db檔)，只使用真實資料源")
        self.log_message("🚫 已移除模擬資料，確保資料真實性")

    def on_year_mode_change(self, event=None):
        """年度模式切換處理"""
        mode = self.year_mode_var.get()
        if mode == "單一年度":
            self.single_year_frame.grid()
            self.range_year_frame.grid_remove()
            self.log_message("📅 切換到單一年度模式")
        else:  # 年度區間
            self.single_year_frame.grid_remove()
            self.range_year_frame.grid()
            self.log_message("📅 切換到年度區間模式")

    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.window.update_idletasks()
    
    def start_download(self):
        """開始下載"""
        if hasattr(self, 'download_thread') and self.download_thread.is_alive():
            messagebox.showwarning("警告", "下載正在進行中，請稍候...")
            return
        
        # 驗證輸入
        try:
            mode = self.year_mode_var.get()
            if mode == "單一年度":
                year = int(self.year_var.get())
                if year < 2020 or year > datetime.now().year:
                    raise ValueError("年份範圍錯誤")
                years_to_download = [year]
            else:  # 年度區間
                start_year = int(self.start_year_var.get())
                end_year = int(self.end_year_var.get())
                if start_year > end_year:
                    raise ValueError("起始年份不能大於結束年份")
                if start_year < 2020 or end_year > datetime.now().year:
                    raise ValueError("年份範圍錯誤")
                years_to_download = list(range(start_year, end_year + 1))
        except ValueError as e:
            messagebox.showerror("錯誤", f"請選擇有效的年份: {str(e)}")
            return
        
        if not self.save_db_var.get() and not self.save_csv_var.get():
            messagebox.showerror("錯誤", "請至少選擇一種儲存格式")
            return
        
        # 開始下載線程
        self.download_thread = threading.Thread(target=self.download_worker, daemon=True)
        self.download_thread.start()
    
    def download_worker(self):
        """下載工作線程"""
        try:
            # 更新UI狀態
            self.window.after(0, lambda: self.set_downloading_state(True))
            
            # 獲取下載參數
            mode = self.year_mode_var.get()
            data_range = self.range_var.get()

            if mode == "單一年度":
                years_to_download = [int(self.year_var.get())]
            else:  # 年度區間
                start_year = int(self.start_year_var.get())
                end_year = int(self.end_year_var.get())
                years_to_download = list(range(start_year, end_year + 1))

            self.window.after(0, lambda: self.log_message(f"🚀 開始下載ROE資料"))
            self.window.after(0, lambda: self.log_message(f"📅 下載年份: {years_to_download}"))
            self.window.after(0, lambda: self.log_message(f"📊 資料範圍: {data_range}"))
            self.window.after(0, lambda: self.log_message(f"🔧 使用Selenium模擬點擊匯出CSV..."))

            # 下載多年度資料
            all_csv_files = []
            total_years = len(years_to_download)

            for i, year in enumerate(years_to_download, 1):
                self.window.after(0, lambda y=year, idx=i, total=total_years:
                    self.log_message(f"📥 下載第 {idx}/{total} 年: {y} 年度資料..."))

                # 使用改進的下載器獲取真實資料
                try:
                    from improved_roe_downloader import ImprovedROEDownloader
                    improved_downloader = ImprovedROEDownloader()
                    csv_file = improved_downloader.download_roe_data(year=year)
                except ImportError:
                    # 回退到原有下載器
                    csv_file = self.downloader.download_roe_csv(year=year)

                if csv_file:
                    all_csv_files.append(csv_file)
                    self.window.after(0, lambda y=year:
                        self.log_message(f"✅ {y} 年度資料下載成功"))
                else:
                    self.window.after(0, lambda y=year:
                        self.log_message(f"❌ {y} 年度資料下載失敗"))

            if not all_csv_files:
                self.window.after(0, lambda: self.log_message("❌ 所有年度資料下載失敗"))
                self.window.after(0, lambda: messagebox.showerror("錯誤", "所有年度資料下載失敗，請檢查網路連線"))
                return

            self.window.after(0, lambda: self.log_message(f"✅ 成功下載 {len(all_csv_files)} 個年度的資料"))

            # 處理所有下載的CSV文件
            total_data_count = 0

            # 處理每個CSV文件
            processed_files = []
            for csv_file in all_csv_files:
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file, encoding='utf-8-sig')
                    data_count = len(df)
                    total_data_count += data_count

                    # 根據範圍篩選資料
                    if data_range == "前100名":
                        df = df.head(100)
                    elif data_range == "前300名":
                        df = df.head(300)
                    elif data_range == "前500名":
                        df = df.head(500)

                    # 如果需要篩選，重新儲存CSV
                    if len(df) < data_count:
                        filtered_csv = csv_file.replace('.csv', f'_filtered_{data_range}.csv')
                        df.to_csv(filtered_csv, index=False, encoding='utf-8-sig')
                        processed_files.append(filtered_csv)
                        self.window.after(0, lambda f=os.path.basename(csv_file), count=len(df):
                            self.log_message(f"📊 {f} 已篩選為 {data_range}: {count} 筆資料"))
                    else:
                        processed_files.append(csv_file)

                except Exception as e:
                    self.window.after(0, lambda f=os.path.basename(csv_file), err=e:
                        self.log_message(f"⚠️ {f} 資料處理警告: {err}"))
                    processed_files.append(csv_file)

            self.window.after(0, lambda: self.log_message(f"✅ 處理完成，共 {total_data_count} 筆ROE資料"))
            
            # 處理儲存選項
            saved_files = []

            # CSV已經下載，標記為完成
            if self.save_csv_var.get():
                saved_files.append("CSV檔案")
                self.window.after(0, lambda: self.log_message(f"✅ CSV檔案已儲存: {len(processed_files)} 個文件"))

            if self.save_db_var.get():
                self.window.after(0, lambda: self.log_message("💾 匯入數據庫..."))
                db_success_count = 0
                for csv_file in processed_files:
                    if self.downloader.import_csv_to_database(csv_file):
                        db_success_count += 1
                        self.window.after(0, lambda f=os.path.basename(csv_file):
                            self.log_message(f"✅ {f} 數據庫匯入成功"))
                    else:
                        self.window.after(0, lambda f=os.path.basename(csv_file):
                            self.log_message(f"❌ {f} 數據庫匯入失敗"))

                if db_success_count > 0:
                    saved_files.append("數據庫")
                    self.window.after(0, lambda: self.log_message(f"✅ 成功匯入 {db_success_count}/{len(processed_files)} 個文件到數據庫"))
                else:
                    self.window.after(0, lambda: self.log_message("❌ 所有文件數據庫匯入失敗"))

            # 完成
            year_info = f"{years_to_download[0]}" if len(years_to_download) == 1 else f"{years_to_download[0]}-{years_to_download[-1]}"
            success_msg = f"🎉 下載完成！\n\n📊 資料筆數: {total_data_count}\n💾 儲存格式: {', '.join(saved_files)}\n📅 資料年份: {year_info}\n📁 檔案數量: {len(processed_files)} 個"
            self.window.after(0, lambda: self.log_message("🎉 下載完成！"))
            self.window.after(0, lambda: messagebox.showinfo("下載完成", success_msg))
            
        except Exception as e:
            error_msg = f"下載過程中發生錯誤: {str(e)}"
            self.window.after(0, lambda: self.log_message(f"❌ {error_msg}"))
            self.window.after(0, lambda: messagebox.showerror("錯誤", error_msg))
        
        finally:
            # 恢復UI狀態
            self.window.after(0, lambda: self.set_downloading_state(False))
    
    def set_downloading_state(self, downloading):
        """設置下載狀態"""
        if downloading:
            self.download_btn.configure(state='disabled', text="⏳ 下載中...")
            self.progress_bar.start()
            self.progress_var.set("正在下載資料...")
            self.status_var.set("下載中")
        else:
            self.download_btn.configure(state='normal', text="🚀 開始下載")
            self.progress_bar.stop()
            self.progress_var.set("下載完成")
            self.status_var.set("就緒")
    
    def view_data(self):
        """查看資料"""
        try:
            db_path = "D:/Finlab/history/tables/roe_data.db"
            if not os.path.exists(db_path):
                messagebox.showwarning("警告", "尚未下載任何ROE資料")
                return
            
            # 查詢最新資料 - 只顯示最新年份的資料
            conn = sqlite3.connect(db_path)

            # 先找出最新年份
            latest_year_query = "SELECT MAX(year) FROM roe_data WHERE year IS NOT NULL"
            latest_year = pd.read_sql_query(latest_year_query, conn).iloc[0, 0]

            query = """
                SELECT stock_code, stock_name, year, roe_value, roe_change, eps_value,
                       report_year, rank_position, crawl_date
                FROM roe_data
                WHERE stock_code IS NOT NULL
                  AND stock_code != ''
                  AND stock_code != 'nan'
                  AND roe_value IS NOT NULL
                  AND year = ?
                ORDER BY CASE WHEN rank_position IS NULL THEN 999999 ELSE rank_position END,
                         roe_value DESC
            """
            df = pd.read_sql_query(query, conn, params=(latest_year,))
            conn.close()
            
            if df.empty:
                messagebox.showinfo("資訊", "數據庫中沒有ROE資料")
                return
            
            # 創建資料查看視窗
            self.show_data_window(df)
            
        except Exception as e:
            messagebox.showerror("錯誤", f"查看資料失敗: {str(e)}")
    
    def show_data_window(self, df):
        """顯示資料查看視窗"""
        data_window = tk.Toplevel(self.window)
        data_window.title("📊 ROE資料查看")
        data_window.geometry("1200x700")  # 增大窗口尺寸
        
        # 創建主框架
        main_frame = ttk.Frame(data_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 添加資料統計標籤
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        info_label = ttk.Label(info_frame, text=f"📊 共顯示 {len(df)} 筆ROE資料",
                              font=('Arial', 10, 'bold'))
        info_label.pack(side=tk.LEFT)

        # 創建Treeview框架
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ['排名', '代號', '名稱', 'ROE(%)', 'ROE變化', 'EPS(元)', '年度', '更新時間']
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=25)

        # 設置欄位標題和寬度 - 優化顯示效果
        column_widths = {
            '排名': 60,
            '代號': 80,
            '名稱': 120,
            'ROE(%)': 80,
            'ROE變化': 80,
            'EPS(元)': 80,
            '年度': 60,
            '更新時間': 150
        }

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[col], minwidth=column_widths[col])
        
        # 插入資料 - 修正NULL值處理
        for _, row in df.iterrows():
            # 安全地處理可能的NULL值
            rank_pos = row.get('rank_position')
            stock_code = row.get('stock_code', '')
            stock_name = row.get('stock_name', '')
            roe_value = row.get('roe_value')
            roe_change = row.get('roe_change')
            eps_value = row.get('eps_value')
            report_year = row.get('report_year')
            crawl_date = row.get('crawl_date', '')

            values = [
                str(int(rank_pos)) if rank_pos is not None and pd.notna(rank_pos) else '',
                str(stock_code) if stock_code and str(stock_code) != 'nan' else '',
                str(stock_name) if stock_name and str(stock_name) != 'nan' else '',
                f"{float(roe_value):.1f}" if roe_value is not None and pd.notna(roe_value) else '',
                f"{float(roe_change):+.1f}" if roe_change is not None and pd.notna(roe_change) else '',
                f"{float(eps_value):.2f}" if eps_value is not None and pd.notna(eps_value) else '',
                str(int(report_year)) if report_year is not None and pd.notna(report_year) else '',
                str(crawl_date)[:16] if crawl_date and str(crawl_date) != 'nan' else ''
            ]
            tree.insert('', tk.END, values=values)
        
        # 添加滾動條
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def open_data_folder(self):
        """開啟資料夾"""
        try:
            folder_path = "D:/Finlab/history/tables"
            if os.path.exists(folder_path):
                os.startfile(folder_path)
            else:
                messagebox.showwarning("警告", f"資料夾不存在: {folder_path}")
        except Exception as e:
            messagebox.showerror("錯誤", f"無法開啟資料夾: {str(e)}")
    
    def run(self):
        """運行GUI"""
        self.window.mainloop()

def main():
    """主函數"""
    app = ROEDataDownloaderGUI()
    app.run()

if __name__ == "__main__":
    main()
