#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試排行榜數據庫查詢功能
"""

import sqlite3
import os
import traceback

def test_database_connection():
    """測試數據庫連接和表格結構"""
    print("🔍 測試排行榜數據庫查詢功能")
    print("=" * 50)
    
    # 檢查數據庫文件
    db_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db')
    price_db_path = os.path.join(db_folder, 'price.db')
    
    print(f"📁 數據庫路徑: {price_db_path}")
    
    if not os.path.exists(price_db_path):
        print("❌ price.db 數據庫文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"📋 數據庫中的表格: {tables}")
        
        # 確認正確的表格名稱
        table_name = 'stock_daily_data'
        if table_name not in tables:
            print(f"❌ 找不到表格 {table_name}")
            if tables:
                table_name = tables[0]
                print(f"🔄 使用第一個表格: {table_name}")
            else:
                print("❌ 數據庫中沒有任何表格")
                return False
        
        # 檢查表格結構
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        print(f"\n📊 表格 {table_name} 的結構:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 檢查數據量
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_rows = cursor.fetchone()[0]
        print(f"\n📈 總數據筆數: {total_rows:,}")
        
        if total_rows == 0:
            print("⚠️ 數據庫中沒有數據")
            return False
        
        # 檢查最新交易日
        cursor.execute(f"SELECT MAX(date) FROM {table_name}")
        latest_date = cursor.fetchone()[0]
        print(f"📅 最新交易日: {latest_date}")
        
        # 檢查當日數據量
        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE date = ?", (latest_date,))
        latest_day_count = cursor.fetchone()[0]
        print(f"📊 最新交易日數據筆數: {latest_day_count:,}")
        
        # 測試排行榜查詢
        print(f"\n🧪 測試排行榜查詢...")
        
        # 測試漲幅排行榜
        print("\n1. 測試漲幅排行榜:")
        query = f"""
            WITH price_with_prev AS (
                SELECT 
                    stock_id,
                    "Close" as close,
                    Volume as volume,
                    date,
                    LAG("Close") OVER (PARTITION BY stock_id ORDER BY date) as prev_close
                FROM {table_name}
                WHERE date <= ?
            )
            SELECT 
                stock_id, 
                close, 
                ROUND(((close - prev_close) / prev_close * 100), 2) as change_percent,
                volume
            FROM price_with_prev 
            WHERE date = ? 
            AND prev_close > 0
            AND close > prev_close
            ORDER BY change_percent DESC
            LIMIT 10
        """
        
        try:
            results = cursor.execute(query, (latest_date, latest_date)).fetchall()
            if results:
                print("   ✅ 漲幅排行榜查詢成功")
                print("   前10名:")
                for i, row in enumerate(results, 1):
                    print(f"   {i:2d}. {row[0]} - 收盤:{row[1]:.2f} 漲幅:{row[2]:+.2f}% 成交量:{row[3]:,.0f}")
            else:
                print("   ⚠️ 漲幅排行榜查詢無結果")
        except Exception as e:
            print(f"   ❌ 漲幅排行榜查詢失敗: {e}")
        
        # 測試跌幅排行榜
        print("\n2. 測試跌幅排行榜:")
        query = f"""
            WITH price_with_prev AS (
                SELECT 
                    stock_id,
                    "Close" as close,
                    Volume as volume,
                    date,
                    LAG("Close") OVER (PARTITION BY stock_id ORDER BY date) as prev_close
                FROM {table_name}
                WHERE date <= ?
            )
            SELECT 
                stock_id, 
                close, 
                ROUND(((close - prev_close) / prev_close * 100), 2) as change_percent,
                volume
            FROM price_with_prev 
            WHERE date = ? 
            AND prev_close > 0
            AND close < prev_close
            ORDER BY change_percent ASC
            LIMIT 10
        """
        
        try:
            results = cursor.execute(query, (latest_date, latest_date)).fetchall()
            if results:
                print("   ✅ 跌幅排行榜查詢成功")
                print("   前10名:")
                for i, row in enumerate(results, 1):
                    print(f"   {i:2d}. {row[0]} - 收盤:{row[1]:.2f} 跌幅:{row[2]:+.2f}% 成交量:{row[3]:,.0f}")
            else:
                print("   ⚠️ 跌幅排行榜查詢無結果")
        except Exception as e:
            print(f"   ❌ 跌幅排行榜查詢失敗: {e}")
        
        # 測試成交量排行榜
        print("\n3. 測試成交量排行榜:")
        query = f"""
            WITH price_with_prev AS (
                SELECT 
                    stock_id,
                    "Close" as close,
                    Volume as volume,
                    date,
                    LAG("Close") OVER (PARTITION BY stock_id ORDER BY date) as prev_close
                FROM {table_name}
                WHERE date <= ?
            )
            SELECT 
                stock_id, 
                close, 
                ROUND(((close - prev_close) / prev_close * 100), 2) as change_percent,
                volume
            FROM price_with_prev 
            WHERE date = ? 
            AND volume > 0
            ORDER BY volume DESC
            LIMIT 10
        """
        
        try:
            results = cursor.execute(query, (latest_date, latest_date)).fetchall()
            if results:
                print("   ✅ 成交量排行榜查詢成功")
                print("   前10名:")
                for i, row in enumerate(results, 1):
                    volume_text = f"{row[3]/1000000:.1f}M" if row[3] >= 1000000 else f"{row[3]/1000:.0f}K"
                    change_text = f"{row[2]:+.2f}%" if row[2] is not None else "N/A"
                    print(f"   {i:2d}. {row[0]} - 收盤:{row[1]:.2f} 漲跌:{change_text} 成交量:{volume_text}")
            else:
                print("   ⚠️ 成交量排行榜查詢無結果")
        except Exception as e:
            print(f"   ❌ 成交量排行榜查詢失敗: {e}")
        
        conn.close()
        
        print(f"\n✅ 數據庫測試完成")
        print(f"📋 修正內容:")
        print(f"• 使用正確的表格名稱: {table_name}")
        print(f"• 使用 LAG() 函數計算前一日收盤價")
        print(f"• 正確處理欄位名稱（Close 需要雙引號）")
        print(f"• 查詢結果限制為100筆")
        
        return True
        
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_database_connection()
