#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 crawl_price 函數中的映射問題
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def debug_crawl_price():
    """調試 crawl_price 函數"""
    
    print("=" * 60)
    print("🔍 調試 crawl_price 函數中的映射")
    print("=" * 60)
    
    try:
        from crawler import price_twe, price_otc, merge, o2tp, fetch_stock_info_for_price
        import time
        import pandas as pd
        
        # 手動執行 crawl_price 的步驟
        test_date = datetime(2022, 8, 31)
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 步驟1: 爬取上市資料
        print(f"\n🔄 步驟1: 爬取上市資料...")
        dftwe = price_twe(test_date)
        print(f"   上市資料: {len(dftwe)} 筆")
        
        # 步驟2: 爬取上櫃資料
        print(f"\n🔄 步驟2: 爬取上櫃資料...")
        time.sleep(5)
        dfotc = price_otc(test_date)
        print(f"   上櫃資料: {len(dfotc)} 筆")
        
        # 步驟3: 合併資料
        print(f"\n🔄 步驟3: 合併資料...")
        if len(dftwe) != 0 and len(dfotc) != 0:
            df = merge(dftwe, dfotc, o2tp)
            print(f"   合併後資料: {len(df)} 筆")
            
            # 步驟4: 獲取股票資訊
            print(f"\n🔄 步驟4: 獲取股票資訊...")
            stock_info = fetch_stock_info_for_price()
            print(f"   股票資訊: {len(stock_info)} 檔")
            
            # 步驟5: 重置索引並檢查
            print(f"\n🔄 步驟5: 重置索引並檢查...")
            df_reset = df.reset_index()
            print(f"   重置後欄位: {list(df_reset.columns)}")
            print(f"   重置後資料筆數: {len(df_reset)}")
            
            # 檢查前5筆的 stock_id
            print(f"\n📋 前5筆 stock_id 詳細資訊:")
            for i in range(min(5, len(df_reset))):
                row = df_reset.iloc[i]
                stock_id = row['stock_id']
                print(f"   第{i+1}筆:")
                print(f"     stock_id: '{stock_id}' (類型: {type(stock_id)})")
                print(f"     str(stock_id): '{str(stock_id)}'")
                
                # 測試映射
                str_stock_id = str(stock_id)
                if str_stock_id in stock_info:
                    info = stock_info[str_stock_id]
                    print(f"     ✅ 映射成功: {info['stock_name']} | {info['listing_status']} | {info['industry']}")
                else:
                    print(f"     ❌ 映射失敗")
                    
                    # 檢查相似的鍵
                    similar_keys = [k for k in stock_info.keys() if str_stock_id in k or k in str_stock_id]
                    print(f"     相似鍵: {similar_keys[:3]}")
            
            # 步驟6: 測試映射函數
            print(f"\n🔄 步驟6: 測試映射函數...")
            
            # 手動執行映射
            df_reset['test_stock_name'] = df_reset['stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('stock_name', 'NOT_FOUND')
            )
            df_reset['test_listing_status'] = df_reset['stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('listing_status', 'NOT_FOUND')
            )
            df_reset['test_industry'] = df_reset['stock_id'].map(
                lambda x: stock_info.get(str(x), {}).get('industry', 'NOT_FOUND')
            )
            
            # 檢查映射結果
            print(f"📊 映射結果統計:")
            name_counts = df_reset['test_stock_name'].value_counts()
            print(f"   stock_name: {dict(name_counts.head())}")
            
            status_counts = df_reset['test_listing_status'].value_counts()
            print(f"   listing_status: {dict(status_counts.head())}")
            
            industry_counts = df_reset['test_industry'].value_counts()
            print(f"   industry: {dict(industry_counts.head())}")
            
            # 顯示成功映射的範例
            successful_mapping = df_reset[df_reset['test_stock_name'] != 'NOT_FOUND'].head(5)
            if not successful_mapping.empty:
                print(f"\n📋 成功映射的範例:")
                for _, row in successful_mapping.iterrows():
                    print(f"   {row['stock_id']}: {row['test_stock_name']} | {row['test_listing_status']} | {row['test_industry']}")
            else:
                print(f"\n❌ 沒有成功映射的記錄")
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_crawl_price()
