#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試資料庫查詢功能
"""

import sqlite3
import os
import pandas as pd

def test_database_connection():
    """測試資料庫連接和資料"""
    print("=" * 60)
    print("🔍 測試月營收資料庫連接和資料")
    print("=" * 60)
    
    # 檢查資料庫檔案
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return False
    
    print(f"✅ 資料庫檔案存在: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        print("\n📋 檢查表格結構:")
        cursor.execute("PRAGMA table_info(monthly_report)")
        columns = cursor.fetchall()
        
        if not columns:
            print("❌ 表格 monthly_report 不存在")
            conn.close()
            return False
        
        print("✅ 表格欄位:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # 檢查資料總數
        cursor.execute("SELECT COUNT(*) FROM monthly_report")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 總記錄數: {total_count:,}")
        
        if total_count == 0:
            print("❌ 資料庫中沒有資料")
            conn.close()
            return False
        
        # 檢查最新資料
        print("\n📅 檢查最新資料:")
        cursor.execute("SELECT DISTINCT date FROM monthly_report ORDER BY date DESC LIMIT 5")
        dates = cursor.fetchall()
        
        print("✅ 最新的5個月份:")
        for date in dates:
            print(f"  - {date[0]}")
        
        # 測試特定股票查詢
        print("\n🔍 測試特定股票查詢:")
        test_stocks = ['2330', '2317', '8021', '2429']
        
        for stock_code in test_stocks:
            query = """
                SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收
                FROM monthly_report
                WHERE stock_id = ?
                ORDER BY date DESC
                LIMIT 1
            """
            
            cursor.execute(query, (stock_code,))
            result = cursor.fetchone()
            
            if result:
                stock_id, stock_name, date, current, last_month, last_year = result
                print(f"  ✅ {stock_code}: {stock_name}")
                print(f"    日期: {date}")
                print(f"    當月營收: {current}")
                print(f"    上月營收: {last_month}")
                print(f"    去年同月: {last_year}")
                
                # 計算成長率
                try:
                    if current and last_year and float(last_year) != 0:
                        yoy_rate = ((float(current) - float(last_year)) / float(last_year) * 100)
                        print(f"    YoY%: {yoy_rate:+.2f}%")
                    
                    if current and last_month and float(last_month) != 0:
                        mom_rate = ((float(current) - float(last_month)) / float(last_month) * 100)
                        print(f"    MoM%: {mom_rate:+.2f}%")
                except (ValueError, TypeError) as e:
                    print(f"    計算成長率失敗: {e}")
                
            else:
                print(f"  ❌ {stock_code}: 未找到資料")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫操作失敗: {e}")
        return False

def test_stock_data_query_function():
    """測試股票資料查詢函數"""
    print("\n" + "=" * 60)
    print("🧪 測試股票資料查詢函數")
    print("=" * 60)
    
    def get_stock_data_from_database(stock_code, stock_name):
        """從資料庫查詢特定股票的月營收資料"""
        try:
            print(f"🔍 查詢股票 {stock_code} {stock_name}")
            
            # 嘗試從資料庫讀取
            monthly_db_path = 'D:/Finlab/history/tables/monthly_report.db'
            if os.path.exists(monthly_db_path):
                conn = sqlite3.connect(monthly_db_path)
                
                # 查詢最新的月營收資料
                query = """
                    SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收
                    FROM monthly_report
                    WHERE stock_id = ?
                    ORDER BY date DESC
                    LIMIT 1
                """
                
                cursor = conn.cursor()
                cursor.execute(query, (stock_code,))
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    # 解析查詢結果
                    stock_id, db_stock_name, date, current_revenue, last_month_revenue, last_year_revenue = result
                    
                    # 計算成長率
                    yoy_rate = "N/A"
                    mom_rate = "N/A"
                    
                    try:
                        if current_revenue and last_year_revenue and float(last_year_revenue) != 0:
                            yoy_rate = f"{((float(current_revenue) - float(last_year_revenue)) / float(last_year_revenue) * 100):+.2f}%"
                    except (ValueError, TypeError):
                        pass
                    
                    try:
                        if current_revenue and last_month_revenue and float(last_month_revenue) != 0:
                            mom_rate = f"{((float(current_revenue) - float(last_month_revenue)) / float(last_month_revenue) * 100):+.2f}%"
                    except (ValueError, TypeError):
                        pass
                    
                    # 構建股票資料
                    stock_data = {
                        '排名': 'N/A',  # 資料庫查詢無排名資訊
                        '股票代碼': stock_code,
                        '股票名稱': db_stock_name or stock_name,
                        '西元年月': date[:7].replace('-', '') if date else 'N/A',  # 轉換為 YYYYMM 格式
                        '當月營收': f"{float(current_revenue):,.0f}" if current_revenue else "N/A",
                        '上個月營收': f"{float(last_month_revenue):,.0f}" if last_month_revenue else "N/A",
                        '去年同月營收': f"{float(last_year_revenue):,.0f}" if last_year_revenue else "N/A",
                        'YoY%': yoy_rate,
                        'MoM%': mom_rate,
                        '殖利率': 'N/A',  # 資料庫查詢無此資訊
                        '本益比': 'N/A',   # 資料庫查詢無此資訊
                        '股價淨值比': 'N/A', # 資料庫查詢無此資訊
                        'EPS': 'N/A',      # 資料庫查詢無此資訊
                        '綜合評分': 'N/A'   # 資料庫查詢無此資訊
                    }
                    
                    print(f"✅ 成功獲取資料")
                    return stock_data
                else:
                    print(f"❌ 未找到資料")
            else:
                print(f"❌ 資料庫檔案不存在")
            
            return None
            
        except Exception as e:
            print(f"❌ 查詢失敗: {e}")
            return None
    
    # 測試多個股票
    test_stocks = [
        ('2330', '台積電'),
        ('2317', '鴻海'),
        ('8021', '尖點'),
        ('2429', '銘旺科'),
        ('1234', '不存在的股票')
    ]
    
    for stock_code, stock_name in test_stocks:
        print(f"\n🔍 測試 {stock_code} {stock_name}:")
        stock_data = get_stock_data_from_database(stock_code, stock_name)
        
        if stock_data:
            print(f"  股票代碼: {stock_data['股票代碼']}")
            print(f"  股票名稱: {stock_data['股票名稱']}")
            print(f"  西元年月: {stock_data['西元年月']}")
            print(f"  當月營收: {stock_data['當月營收']} 千元")
            print(f"  上個月營收: {stock_data['上個月營收']} 千元")
            print(f"  去年同月營收: {stock_data['去年同月營收']} 千元")
            print(f"  YoY%: {stock_data['YoY%']}")
            print(f"  MoM%: {stock_data['MoM%']}")
            
            # 檢查是否有N/A值（除了預期的欄位）
            na_fields = []
            for key, value in stock_data.items():
                if key not in ['排名', '殖利率', '本益比', '股價淨值比', 'EPS', '綜合評分'] and str(value) == 'N/A':
                    na_fields.append(key)
            
            if na_fields:
                print(f"  ⚠️ 有N/A值的欄位: {na_fields}")
            else:
                print(f"  ✅ 營收資料完整，無N/A值")
        else:
            print(f"  ❌ 查詢失敗")

def main():
    """主函數"""
    print("🚀 月營收資料庫查詢測試")
    
    # 測試資料庫連接
    if test_database_connection():
        print("\n✅ 資料庫連接測試通過")
        
        # 測試查詢函數
        test_stock_data_query_function()
        
        print("\n" + "=" * 60)
        print("🎉 測試完成！")
        print("✅ 資料庫查詢功能正常")
        print("✅ 可以獲取真實的月營收資料")
        print("✅ 成長率計算正確")
        print("=" * 60)
        
    else:
        print("\n❌ 資料庫連接測試失敗")
        print("請檢查資料庫檔案是否存在和資料是否完整")

if __name__ == "__main__":
    main()
