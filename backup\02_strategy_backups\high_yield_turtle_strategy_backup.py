#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高殖利率烏龜策略 - PKL真實數據版本
探索具有高殖利率且保持成長動能的冷門中小型股票
現已整合Finlab PKL真實數據，大幅提升策略準確度
"""

import logging
import pandas as pd
import numpy as np
import os
from typing import Dict, Any, Optional
from strategies.base_strategy import BaseStrategy

class HighYieldTurtleStrategy(BaseStrategy):
    """高殖利率烏龜策略 - PKL真實數據版本"""

    def __init__(self):
        super().__init__(
            name="高殖利率烏龜策略",
            description="探索具有高殖利率且保持成長動能的冷門中小型股票，現已整合Finlab PKL真實數據",
            strategy_type="value_growth_hybrid"
        )

        # 策略參數
        self.min_dividend_yield = 4.0      # 最低殖利率要求 4% (調整為更實際的標準)
        self.min_operating_margin = 3.0    # 最低營業利益率 3%
        self.min_insider_holding = 10.0    # 最低董監持股 10%
        self.min_volume = 50000             # 最低成交量 50張
        self.max_volume = 10000000          # 最高成交量 10000張
        self.min_score = 70                 # 最低通過分數 (調整為更實際的標準)

        self.logger = logging.getLogger(__name__)

        # PKL數據路徑 - 使用統一的資料庫目錄
        self.pe_pkl_path = 'D:/Finlab/history/tables/pe.pkl'
        self.pe_data_cache = None
        self.load_pkl_data()

    def load_pkl_data(self):
        """載入PKL數據"""
        try:
            self.logger.info(f"🔄 嘗試載入PKL數據: {self.pe_pkl_path}")
            if os.path.exists(self.pe_pkl_path):
                self.pe_data_cache = pd.read_pickle(self.pe_pkl_path)
                self.logger.info(f"✅ 載入PKL數據成功: {self.pe_data_cache.shape[0]} 筆記錄")

                # 顯示樣本數據
                sample_stocks = self.pe_data_cache['股票代號'].head(5).tolist()
                self.logger.info(f"📊 PKL樣本股票: {sample_stocks}")
            else:
                self.logger.warning(f"⚠️ PKL檔案不存在: {self.pe_pkl_path}")
                self.pe_data_cache = None
        except Exception as e:
            self.logger.error(f"❌ 載入PKL數據失敗: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self.pe_data_cache = None

    def get_stock_pkl_data(self, stock_id):
        """從PKL數據中獲取股票資料"""
        self.logger.debug(f"🔍 查找PKL數據: {stock_id}")

        if self.pe_data_cache is None:
            self.logger.warning(f"⚠️ PKL數據快取為空: {stock_id}")
            return None

        try:
            # 查找股票數據
            stock_data = self.pe_data_cache[self.pe_data_cache['股票代號'] == stock_id]

            if stock_data.empty:
                self.logger.debug(f"⚠️ 未找到PKL數據: {stock_id}")
                return None

            # 取最新數據
            latest = stock_data.iloc[-1]

            pkl_info = {
                'stock_id': stock_id,
                'stock_name': latest.get('名稱', ''),
                'pe_ratio': latest.get('本益比', 0),
                'dividend_yield': latest.get('殖利率(%)', 0),
                'pb_ratio': latest.get('股價淨值比', 0),
                'dividend_per_share': latest.get('每股股利', 0),
                'market': latest.get('市場', ''),
                'dividend_year': latest.get('股利年度', ''),
                'date': latest.get('date', ''),
                'data_count': len(stock_data)
            }

            return pkl_info

        except Exception as e:
            self.logger.error(f"❌ 獲取PKL數據失敗 {stock_id}: {e}")
            return None
    
    def analyze_stock(self, df: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        分析單一股票是否符合高殖利率烏龜策略 - PKL真實數據版本

        Args:
            df: 股票價格數據
            **kwargs: 包含 finmind_data 和 stock_id

        Returns:
            分析結果字典
        """
        try:
            if df.empty or len(df) < 60:
                return {
                    'suitable': False,
                    'reason': '數據不足，需要至少60日數據',
                    'details': {},
                    'score': 0,
                    'strategy_name': self.name
                }

            # 獲取股票代碼
            stock_id = kwargs.get('stock_id', 'Unknown')

            # 處理None或空字符串的情況
            if stock_id is None or stock_id == '':
                stock_id = 'Unknown'
                self.logger.warning(f"⚠️ 股票代碼為空，設置為: {stock_id}")

            self.logger.info(f"🔍 分析股票: {stock_id}")

            # 如果股票代碼無效，拒絕分析
            if stock_id == 'Unknown' or stock_id is None:
                self.logger.error(f"❌ {stock_id}: 股票代碼無效，拒絕使用模擬數據進行投資分析")
                return {
                    'suitable': False,
                    'reason': f'❌ 股票代碼無效({stock_id})，為避免投資風險，拒絕使用模擬數據分析',
                    'details': {'error': 'invalid_stock_id', 'data_source': '無數據'},
                    'score': 0,
                    'strategy_name': self.name
                }

            # 優先使用PKL數據
            pkl_data = self.get_stock_pkl_data(stock_id)

            if pkl_data:
                self.logger.info(f"✅ 使用PKL真實數據分析: {stock_id}")
                # 使用PKL真實數據分析
                return self.analyze_with_pkl_data(df, pkl_data, stock_id)
            else:
                self.logger.error(f"❌ {stock_id}: 無PKL真實數據，拒絕使用模擬數據進行投資分析")
                # 拒絕使用模擬數據，返回明確的錯誤信息
                return {
                    'suitable': False,
                    'reason': f'❌ {stock_id}: 無真實財務數據，為避免投資風險，拒絕分析',
                    'details': {'error': 'no_real_data', 'data_source': '無真實數據'},
                    'score': 0,
                    'strategy_name': self.name
                }

        except Exception as e:
            self.logger.error(f"❌ 高殖利率烏龜策略分析失敗 {stock_id}: {e}")
            return {
                'suitable': False,
                'reason': f'策略分析錯誤: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }

    def analyze_with_pkl_data(self, df: pd.DataFrame, pkl_data: dict, stock_id: str) -> Dict[str, Any]:
        """使用PKL真實數據進行分析"""
        try:
            # 基本信息
            latest = df.iloc[-1]
            close_price = latest['Close']
            volume = latest['Volume']

            results = {}
            total_score = 0
            reasons = []

            # 從PKL數據獲取關鍵指標
            dividend_yield = pkl_data.get('dividend_yield', 0)
            pe_ratio = pkl_data.get('pe_ratio', 0)
            pb_ratio = pkl_data.get('pb_ratio', 0)
            stock_name = pkl_data.get('stock_name', stock_id)
            market = pkl_data.get('market', '')

            # === 1. 殖利率評分 (40分) ===
            dividend_score = 0
            if dividend_yield >= 6:
                dividend_score = 40
                reasons.append(f"高殖利率: {dividend_yield:.1f}%")
            elif dividend_yield >= 4:
                dividend_score = 30
                reasons.append(f"中等殖利率: {dividend_yield:.1f}%")
            elif dividend_yield >= 2:
                dividend_score = 20
                reasons.append(f"基本殖利率: {dividend_yield:.1f}%")
            elif dividend_yield > 0:
                dividend_score = 10
                reasons.append(f"低殖利率: {dividend_yield:.1f}%")
            else:
                reasons.append("無殖利率數據")

            total_score += dividend_score
            results['dividend_yield'] = dividend_yield
            results['dividend_score'] = dividend_score

            # === 2. 估值評分 (35分) ===
            valuation_score = 0

            # PE比評分 (25分)
            if pe_ratio > 0:
                if 8 <= pe_ratio <= 15:  # 合理PE區間
                    valuation_score += 25
                    reasons.append(f"PE合理: {pe_ratio:.1f}倍")
                elif pe_ratio <= 8:  # 低估
                    valuation_score += 30
                    reasons.append(f"PE偏低: {pe_ratio:.1f}倍")
                elif pe_ratio <= 20:  # 略高但可接受
                    valuation_score += 15
                    reasons.append(f"PE略高: {pe_ratio:.1f}倍")
                elif pe_ratio <= 30:  # 偏高
                    valuation_score += 5
                    reasons.append(f"PE偏高: {pe_ratio:.1f}倍")
                else:
                    reasons.append(f"PE過高: {pe_ratio:.1f}倍")
            else:
                reasons.append("無PE數據")

            # PB比評分 (10分)
            if pb_ratio > 0:
                if pb_ratio <= 1.0:  # 非常低PB
                    valuation_score += 10
                    reasons.append(f"PB極低: {pb_ratio:.1f}倍")
                elif pb_ratio <= 1.5:  # 低PB
                    valuation_score += 8
                    reasons.append(f"PB偏低: {pb_ratio:.1f}倍")
                elif pb_ratio <= 2.5:  # 合理PB
                    valuation_score += 5
                    reasons.append(f"PB合理: {pb_ratio:.1f}倍")
                else:
                    reasons.append(f"PB偏高: {pb_ratio:.1f}倍")

            total_score += valuation_score
            results['pe_ratio'] = pe_ratio
            results['pb_ratio'] = pb_ratio
            results['valuation_score'] = valuation_score

            # === 3. 穩定性和品質評分 (25分) ===
            stability_score = 0

            # 上市股票加分 (5分)
            if market == '上市':
                stability_score += 5
                reasons.append("上市股票")
            elif market == '上櫃':
                stability_score += 3
                reasons.append("上櫃股票")

            # 技術面評分 (20分)
            # 計算價格趨勢
            if len(df) >= 20:
                ma20 = df['Close'].rolling(20).mean().iloc[-1]
                price_trend = (close_price / ma20 - 1) * 100

                if price_trend > 5:
                    stability_score += 15
                    reasons.append(f"價格強勢: +{price_trend:.1f}%")
                elif price_trend > 0:
                    stability_score += 10
                    reasons.append(f"價格上升: +{price_trend:.1f}%")
                elif price_trend > -5:
                    stability_score += 5
                    reasons.append(f"價格平穩: {price_trend:.1f}%")
                else:
                    reasons.append(f"價格疲弱: {price_trend:.1f}%")

                results['price_trend'] = price_trend

            # 成交量評分 (5分)
            if 50000 <= volume <= 10000000:  # 50張到10000張
                stability_score += 5
                reasons.append(f"成交量適中: {volume/1000:.0f}張")
            elif volume > 0:
                reasons.append(f"成交量: {volume/1000:.0f}張")

            total_score += stability_score
            results['stability_score'] = stability_score
            results['volume'] = volume

            # === 最終判斷 ===
            results['total_score'] = total_score
            results['stock_name'] = stock_name
            results['close_price'] = close_price
            results['data_source'] = 'PKL真實數據'

            # 策略判斷
            if total_score >= self.min_score:
                reason = f"🐢 PKL版高殖利率烏龜策略符合！{stock_name}({stock_id}) 評分: {total_score}/100 - " + ", ".join(reasons) + " (PKL真實數據)"
                return {
                    'suitable': True,
                    'reason': reason,
                    'details': results,
                    'score': total_score,
                    'strategy_name': self.name
                }
            elif total_score >= 50:
                reason = f"PKL版高殖利率烏龜策略部分符合，{stock_name}({stock_id}) 評分: {total_score}/100 - " + ", ".join(reasons) + " (PKL真實數據)"
                return {
                    'suitable': False,
                    'reason': reason,
                    'details': results,
                    'score': total_score,
                    'strategy_name': self.name
                }
            else:
                reason = f"PKL版高殖利率烏龜策略不符合，{stock_name}({stock_id}) 評分: {total_score}/100 - " + ", ".join(reasons) + " (PKL真實數據)"
                return {
                    'suitable': False,
                    'reason': reason,
                    'details': results,
                    'score': total_score,
                    'strategy_name': self.name
                }

        except Exception as e:
            self.logger.error(f"❌ PKL數據分析失敗 {stock_id}: {e}")
            return {
                'suitable': False,
                'reason': f'PKL數據分析錯誤: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }

    def analyze_with_finmind_data(self, df: pd.DataFrame, finmind_data: dict, stock_id: str) -> Dict[str, Any]:
        """使用FinMind真實數據進行分析 - 已停用模擬數據"""
        try:
            # 只接受真實的FinMind數據
            if not finmind_data:
                self.logger.error(f"❌ {stock_id}: 無FinMind真實數據，拒絕使用模擬數據")
                return {
                    'suitable': False,
                    'reason': f'❌ {stock_id}: 無FinMind真實數據，為避免投資風險，拒絕分析',
                    'details': {'error': 'no_finmind_data', 'data_source': '無真實數據'},
                    'score': 0,
                    'strategy_name': self.name
                }

            # 基本信息
            latest = df.iloc[-1]
            close_price = latest['Close']
            volume = latest['Volume']

            results = {}
            total_score = 0
            reasons = []
            data_source = "FinMind真實數據"

            # 使用FinMind真實數據
            dividend_yield = finmind_data.get('dividend_yield', 0)
            operating_margin = finmind_data.get('operating_margin', 0)

            # 殖利率評分
            if dividend_yield >= 6:
                total_score += 40
                reasons.append(f"高殖利率: {dividend_yield:.1f}%")
            elif dividend_yield >= 4:
                total_score += 30
                reasons.append(f"中等殖利率: {dividend_yield:.1f}%")
            elif dividend_yield >= 2:
                total_score += 20
                reasons.append(f"基本殖利率: {dividend_yield:.1f}%")
            else:
                reasons.append(f"殖利率偏低: {dividend_yield:.1f}%")

            # 營業利益率評分
            if operating_margin >= 3:
                total_score += 25
                reasons.append(f"營業利益率良好: {operating_margin:.1f}%")
            elif operating_margin >= 0:
                total_score += 10
                reasons.append(f"營業利益率普通: {operating_margin:.1f}%")
            else:
                reasons.append(f"營業利益率不佳: {operating_margin:.1f}%")

            results['dividend_yield'] = dividend_yield
            results['operating_margin'] = operating_margin

            # 技術面評分
            if len(df) >= 20:
                ma20 = df['Close'].rolling(20).mean().iloc[-1]
                price_trend = (close_price / ma20 - 1) * 100

                if price_trend > 0:
                    total_score += 15
                    reasons.append(f"價格趨勢向上: +{price_trend:.1f}%")
                elif price_trend > -5:
                    total_score += 5
                    reasons.append(f"價格平穩: {price_trend:.1f}%")
                else:
                    reasons.append(f"價格疲弱: {price_trend:.1f}%")

                results['price_trend'] = price_trend

            # 成交量評分
            if 50000 <= volume <= 10000000:
                total_score += 10
                reasons.append(f"成交量適中: {volume/1000:.0f}張")
            elif volume > 0:
                reasons.append(f"成交量: {volume/1000:.0f}張")

            results['total_score'] = total_score
            results['close_price'] = close_price
            results['volume'] = volume
            results['data_source'] = data_source

            # 最終判斷（真實數據標準）
            min_score = 70

            if total_score >= min_score:
                reason = f"🐢 高殖利率烏龜策略符合！{stock_id} 評分: {total_score}/100 - " + ", ".join(reasons) + f" ({data_source})"
                return {
                    'suitable': True,
                    'reason': reason,
                    'details': results,
                    'score': total_score,
                    'strategy_name': self.name
                }
            else:
                reason = f"高殖利率烏龜策略不符合，{stock_id} 評分: {total_score}/100 - " + ", ".join(reasons) + f" ({data_source})"
                return {
                    'suitable': False,
                    'reason': reason,
                    'details': results,
                    'score': total_score,
                    'strategy_name': self.name
                }

        except Exception as e:
            self.logger.error(f"❌ FinMind數據分析失敗 {stock_id}: {e}")
            return {
                'suitable': False,
                'reason': f'FinMind數據分析錯誤: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }

    # 已移除所有模擬數據相關方法，確保投資分析的準確性
            
            # === 1. 殖利率檢查 (20分) ===
            dividend_pass, dividend_score, dividend_reason = self.check_dividend_yield(
                df, finmind_data, close_price
            )
            results['殖利率'] = {
                'pass': dividend_pass, 
                'score': dividend_score, 
                'reason': dividend_reason
            }
            total_score += dividend_score
            
            if finmind_data.get('dividend_data') and not finmind_data['dividend_data'].get('is_simulated', True):
                data_sources.append("殖利率-真實數據")
            else:
                data_sources.append("殖利率-模擬數據")
            
            # === 2. 股價多頭排列 (15分) ===
            trend_pass, trend_score, trend_reason = self.check_bullish_alignment(df)
            results['多頭排列'] = {
                'pass': trend_pass, 
                'score': trend_score, 
                'reason': trend_reason
            }
            total_score += trend_score
            data_sources.append("多頭排列-技術分析")
            
            # === 3. 營收成長 (15分) ===
            revenue_pass, revenue_score, revenue_reason = self.check_revenue_growth(
                df, finmind_data
            )
            results['營收成長'] = {
                'pass': revenue_pass, 
                'score': revenue_score, 
                'reason': revenue_reason
            }
            total_score += revenue_score
            
            if finmind_data.get('revenue_data') and not finmind_data['revenue_data'].get('is_simulated', True):
                data_sources.append("營收-真實數據")
            else:
                data_sources.append("營收-模擬數據")
            
            # === 4. 營業利益率 (15分) ===
            margin_pass, margin_score, margin_reason = self.check_operating_margin(
                df, finmind_data
            )
            results['營業利益率'] = {
                'pass': margin_pass, 
                'score': margin_score, 
                'reason': margin_reason
            }
            total_score += margin_score
            
            if finmind_data.get('financial_data') and not finmind_data['financial_data'].get('is_simulated', True):
                data_sources.append("營業利益率-真實數據")
            else:
                data_sources.append("營業利益率-模擬數據")
            
            # === 5. 董監持股 (15分) ===
            insider_pass, insider_score, insider_reason = self.check_insider_holding(
                df, finmind_data
            )
            results['董監持股'] = {
                'pass': insider_pass, 
                'score': insider_score, 
                'reason': insider_reason
            }
            total_score += insider_score
            data_sources.append("董監持股-模擬數據")  # 目前FinMind沒有此數據
            
            # === 6. 成交量適中 (20分) ===
            volume_pass, volume_score, volume_reason = self.check_volume_range(df)
            results['成交量'] = {
                'pass': volume_pass, 
                'score': volume_score, 
                'reason': volume_reason
            }
            total_score += volume_score
            data_sources.append("成交量-技術分析")
            
            # === 判斷結果 ===
            suitable = total_score >= self.min_score
            
            # 計算真實數據比例
            real_data_count = len([s for s in data_sources if "真實數據" in s])
            real_data_ratio = real_data_count / len(data_sources) if data_sources else 0
            
            # 生成總結
            passed_items = [k for k, v in results.items() if v['score'] > 0]
            reason = f"🐢 高殖利率烏龜評分: {total_score}/100 (通過: {','.join(passed_items)})"
            
            # 添加數據來源信息
            if real_data_count > 0:
                reason += f" | 📊 真實數據: {real_data_count}項, 模擬數據: {len(data_sources)-real_data_count}項"
            else:
                reason += f" | ⚠️ 全部使用模擬數據 ({len(data_sources)}項)"
            
            if suitable:
                reason += f" | ✅ 符合高殖利率烏龜策略"
            else:
                reason += f" | ❌ 未達{self.min_score}分門檻"
            
            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'total_score': total_score,
                'strategy_name': self.name,
                'data_sources': data_sources,
                'real_data_ratio': real_data_ratio
            }
            
        except Exception as e:
            self.logger.error(f"高殖利率烏龜策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }
    
    def check_dividend_yield(self, df, finmind_data, current_price):
        """檢查殖利率 (20分)"""
        try:
            # 優先使用FinMind真實數據
            dividend_data = finmind_data.get('dividend_data')
            if dividend_data and not dividend_data.get('is_simulated', True):
                cash_dividend = dividend_data.get('cash_dividend', 0)
                if current_price > 0 and cash_dividend > 0:
                    dividend_yield = (cash_dividend / current_price) * 100
                    
                    if dividend_yield >= self.min_dividend_yield:
                        return True, 20, f"✅ 高殖利率 {dividend_yield:.2f}% (真實數據)"
                    elif dividend_yield >= 4.0:
                        return True, 10, f"⚠️ 中等殖利率 {dividend_yield:.2f}% (真實數據)"
                    else:
                        return False, 0, f"❌ 殖利率偏低 {dividend_yield:.2f}% (真實數據)"
            
            # 如果沒有真實數據，使用模擬計算
            dividend_yield = self._calculate_dividend_yield_proxy(df)
            if dividend_yield >= self.min_dividend_yield:
                return True, 20, f"📊 高殖利率 {dividend_yield:.1f}% (模擬數據)"
            elif dividend_yield >= 4.0:
                return True, 10, f"📊 中等殖利率 {dividend_yield:.1f}% (模擬數據)"
            else:
                return False, 0, f"📊 殖利率偏低 {dividend_yield:.1f}% (模擬數據)"
                
        except Exception as e:
            return False, 0, f"殖利率檢查失敗: {str(e)}"
    
    def check_revenue_growth(self, df, finmind_data):
        """檢查營收成長 (15分)"""
        try:
            # 優先使用FinMind真實數據
            revenue_data = finmind_data.get('revenue_data')
            if revenue_data and not revenue_data.get('is_simulated', True):
                growth_rate = revenue_data.get('revenue_growth_rate', 0)
                yoy_growth = revenue_data.get('yoy_growth_rate', 0)
                
                if growth_rate > 10 or yoy_growth > 10:
                    return True, 15, f"✅ 營收強勁成長 月增{growth_rate:.1f}% 年增{yoy_growth:.1f}% (真實數據)"
                elif growth_rate > 0 or yoy_growth > 0:
                    return True, 8, f"⚠️ 營收正成長 月增{growth_rate:.1f}% 年增{yoy_growth:.1f}% (真實數據)"
                else:
                    return False, 0, f"❌ 營收負成長 月增{growth_rate:.1f}% 年增{yoy_growth:.1f}% (真實數據)"
            
            # 如果沒有真實數據，使用技術分析代理
            trend_up, trend_reason = self._check_revenue_trend_proxy(df)
            if trend_up:
                return True, 15, f"📊 營收趨勢向上 {trend_reason} (技術分析)"
            else:
                return False, 0, f"📊 營收趨勢不佳 {trend_reason} (技術分析)"
                
        except Exception as e:
            return False, 0, f"營收成長檢查失敗: {str(e)}"
    
    def check_operating_margin(self, df, finmind_data):
        """檢查營業利益率 (15分)"""
        try:
            # 優先使用FinMind真實數據
            financial_data = finmind_data.get('financial_data')
            if financial_data and not financial_data.get('is_simulated', True):
                revenue = financial_data.get('revenue', 0)
                net_income = financial_data.get('net_income', 0)
                
                if revenue > 0:
                    # 使用淨利率代理營業利益率
                    net_margin = (net_income / revenue) * 100
                    
                    if net_margin >= self.min_operating_margin:
                        return True, 15, f"✅ 獲利能力佳 淨利率{net_margin:.1f}% (真實數據)"
                    elif net_margin >= 1.0:
                        return True, 8, f"⚠️ 獲利能力普通 淨利率{net_margin:.1f}% (真實數據)"
                    else:
                        return False, 0, f"❌ 獲利能力差 淨利率{net_margin:.1f}% (真實數據)"
            
            # 如果沒有真實數據，使用價格穩定度代理
            operating_margin = self._calculate_operating_margin_proxy(df)
            if operating_margin >= self.min_operating_margin:
                return True, 15, f"📊 營業利益率 {operating_margin:.1f}% (模擬數據)"
            else:
                return False, 0, f"📊 營業利益率不足 {operating_margin:.1f}% (模擬數據)"
                
        except Exception as e:
            return False, 0, f"營業利益率檢查失敗: {str(e)}"
    
    def check_bullish_alignment(self, df):
        """檢查多頭排列 (15分)"""
        try:
            if len(df) < 60:
                return False, 0, "數據不足"
            
            close_price = df['Close'].iloc[-1]
            ma5 = df['Close'].rolling(5).mean().iloc[-1]
            ma20 = df['Close'].rolling(20).mean().iloc[-1]
            ma60 = df['Close'].rolling(60).mean().iloc[-1]
            
            if close_price > ma5 > ma20 > ma60:
                return True, 15, f"📈 完美多頭排列"
            elif close_price > ma20 > ma60:
                return True, 10, f"📈 基本多頭排列"
            else:
                return False, 0, f"📉 非多頭排列"
                
        except Exception as e:
            return False, 0, f"多頭排列檢查失敗: {str(e)}"
    
    def check_insider_holding(self, df, finmind_data):
        """檢查董監持股 (15分) - 目前使用模擬數據"""
        try:
            # FinMind目前沒有董監持股數據，使用成交量穩定度代理
            volume_stability = self._calculate_volume_stability(df)
            insider_holding = volume_stability * 20  # 0-20%範圍
            
            if insider_holding >= self.min_insider_holding:
                return True, 15, f"📊 董監持股 {insider_holding:.1f}% (模擬數據)"
            else:
                return False, 0, f"📊 董監持股不足 {insider_holding:.1f}% (模擬數據)"
                
        except Exception as e:
            return False, 0, f"董監持股檢查失敗: {str(e)}"
    
    def check_volume_range(self, df):
        """檢查成交量範圍 (20分)"""
        try:
            volume_5d = df['Volume'].tail(5).mean()
            
            if self.min_volume <= volume_5d <= self.max_volume:
                return True, 20, f"📊 成交量適中 {volume_5d/1000:.0f}張"
            elif volume_5d < self.min_volume:
                return False, 0, f"📊 成交量過低 {volume_5d/1000:.0f}張"
            else:
                return False, 0, f"📊 成交量過高 {volume_5d/1000:.0f}張"
                
        except Exception as e:
            return False, 0, f"成交量檢查失敗: {str(e)}"
    
    # === 模擬數據計算方法 ===
    
    def _calculate_dividend_yield_proxy(self, df):
        """計算殖利率代理指標"""
        try:
            high_252 = df['High'].rolling(252).max().iloc[-1]
            low_252 = df['Low'].rolling(252).min().iloc[-1]
            current_price = df['Close'].iloc[-1]
            
            if high_252 > low_252:
                price_position = (current_price - low_252) / (high_252 - low_252)
                dividend_yield = 10 * (1 - price_position)
                return max(0, min(dividend_yield, 10))
            return 5
        except Exception:
            return 5
    
    def _check_revenue_trend_proxy(self, df):
        """檢查營收趨勢代理"""
        try:
            if len(df) >= 90:
                recent_3m = df['Close'].tail(90).mean()
                recent_12m = df['Close'].tail(360).mean() if len(df) >= 360 else df['Close'].mean()
                
                if recent_3m > recent_12m:
                    growth = (recent_3m / recent_12m - 1) * 100
                    return True, f"趨勢向上{growth:.1f}%"
                else:
                    decline = (1 - recent_3m / recent_12m) * 100
                    return False, f"趨勢下滑{decline:.1f}%"
            return False, "數據不足"
        except Exception:
            return False, "計算錯誤"
    
    def _calculate_operating_margin_proxy(self, df):
        """計算營業利益率代理指標"""
        try:
            price_volatility = df['Close'].pct_change().tail(60).std()
            operating_margin = max(0, 8 - price_volatility * 100)
            return min(operating_margin, 8)
        except Exception:
            return 3
    
    def _calculate_volume_stability(self, df):
        """計算成交量穩定度"""
        try:
            if len(df) < 20:
                return 0.5
            
            recent_volumes = df['Volume'].tail(20)
            cv = recent_volumes.std() / recent_volumes.mean() if recent_volumes.mean() > 0 else 1
            stability = max(0, 1 - cv)
            return min(stability, 1)
        except Exception:
            return 0.5
