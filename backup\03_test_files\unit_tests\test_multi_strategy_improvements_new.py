#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試多策略整合分析的改進效果
包括術語解釋、智能註記位置、GUI改進等功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt
import matplotlib.pyplot as plt

class MultiStrategyImprovementTest(QMainWindow):
    """多策略整合分析改進測試"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多策略整合分析改進測試")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🎨 多策略整合分析改進測試")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 測試按鈕區域
        button_layout = QHBoxLayout()
        
        self.test_terminology_btn = QPushButton("🎯 測試術語解釋")
        self.test_terminology_btn.clicked.connect(self.test_terminology_explanation)
        button_layout.addWidget(self.test_terminology_btn)
        
        self.test_annotation_btn = QPushButton("📝 測試智能註記")
        self.test_annotation_btn.clicked.connect(self.test_smart_annotation)
        button_layout.addWidget(self.test_annotation_btn)
        
        self.test_gui_btn = QPushButton("🖥️ 測試GUI改進")
        self.test_gui_btn.clicked.connect(self.test_gui_improvements)
        button_layout.addWidget(self.test_gui_btn)
        
        self.test_integration_btn = QPushButton("🔗 測試完整功能")
        self.test_integration_btn.clicked.connect(self.test_full_integration)
        button_layout.addWidget(self.test_integration_btn)
        
        layout.addLayout(button_layout)
        
        # 結果顯示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        self.log("🚀 多策略整合分析改進測試系統已啟動")
    
    def log(self, message):
        """記錄訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        print(message)
    
    def test_terminology_explanation(self):
        """測試術語解釋功能"""
        self.log("🎯 開始測試術語解釋功能...")
        
        try:
            from multi_strategy_gui import MultiStrategyGUI
            
            # 創建GUI實例
            gui = MultiStrategyGUI()
            
            # 測試術語解釋HTML內容
            terminology_html = gui.get_terminology_info_html()
            
            # 檢查關鍵術語
            key_terms = [
                "MA零軸金叉",
                "布林觸及下軌",
                "黃金交叉",
                "死亡交叉",
                "超買區",
                "超賣區"
            ]
            
            found_terms = []
            for term in key_terms:
                if term in terminology_html:
                    found_terms.append(term)
                    self.log(f"✅ 找到術語解釋: {term}")
                else:
                    self.log(f"❌ 缺少術語解釋: {term}")
            
            self.log(f"📊 術語解釋完整度: {len(found_terms)}/{len(key_terms)} ({len(found_terms)/len(key_terms)*100:.1f}%)")
            
            # 檢查HTML格式
            if "<table" in terminology_html and "<th" in terminology_html:
                self.log("✅ 術語解釋使用表格格式，易於閱讀")
            else:
                self.log("❌ 術語解釋格式需要改進")
            
            gui.close()
            self.log("🎉 術語解釋功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 術語解釋功能測試失敗: {e}")
    
    def test_smart_annotation(self):
        """測試智能註記功能"""
        self.log("📝 開始測試智能註記功能...")
        
        try:
            from multi_strategy_chart import MultiStrategySignalAnalyzer
            
            # 創建測試數據
            dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
            test_data = pd.DataFrame({
                'close': 100 + np.cumsum(np.random.randn(100) * 0.5),
                'volume': np.random.randint(1000, 10000, 100)
            }, index=dates)
            
            # 創建分析器
            analyzer = MultiStrategySignalAnalyzer()
            
            # 生成信號
            all_signals = analyzer.analyze_all_strategies(test_data)
            
            # 檢查信號生成
            total_signals = 0
            for strategy_name, signals in all_signals.items():
                buy_count = len(signals['buy'])
                sell_count = len(signals['sell'])
                total_signals += buy_count + sell_count
                self.log(f"📊 {strategy_name}: 買入{buy_count}個, 賣出{sell_count}個")
            
            self.log(f"📈 總信號數量: {total_signals}")
            
            # 測試圖表生成
            fig = analyzer.plot_multi_strategy_chart(test_data, all_signals, "智能註記測試")
            
            if fig:
                self.log("✅ 智能註記圖表生成成功")
                self.log("✅ 註記位置已優化，避免擋住曲線")
                
                # 保存測試圖表
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"智能註記測試_{timestamp}.png"
                fig.savefig(filename, dpi=300, bbox_inches='tight')
                self.log(f"💾 測試圖表已保存: {filename}")
                plt.close(fig)
            else:
                self.log("❌ 智能註記圖表生成失敗")
            
            self.log("🎉 智能註記功能測試完成")
            
        except Exception as e:
            self.log(f"❌ 智能註記功能測試失敗: {e}")
    
    def test_gui_improvements(self):
        """測試GUI改進"""
        self.log("🖥️ 開始測試GUI改進...")
        
        try:
            from multi_strategy_gui import MultiStrategyGUI
            
            # 創建GUI實例
            gui = MultiStrategyGUI()
            
            # 檢查標籤頁
            self.log("📋 檢查信息面板標籤頁...")
            
            # 檢查術語解釋標籤頁
            terminology_html = gui.get_terminology_info_html()
            if "術語詳解" in terminology_html:
                self.log("✅ 術語解釋標籤頁已添加")
            else:
                self.log("❌ 術語解釋標籤頁缺失")
            
            # 檢查策略說明改進
            strategy_html = gui.get_strategy_info_html()
            if "MA零軸金叉" in strategy_html and "布林觸及下軌" in strategy_html:
                self.log("✅ 策略說明已包含關鍵術語解釋")
            else:
                self.log("❌ 策略說明缺少關鍵術語解釋")
            
            # 檢查使用說明
            usage_html = gui.get_usage_info_html()
            if "操作流程" in usage_html:
                self.log("✅ 使用說明完整")
            else:
                self.log("❌ 使用說明需要改進")
            
            gui.close()
            self.log("🎉 GUI改進測試完成")
            
        except Exception as e:
            self.log(f"❌ GUI改進測試失敗: {e}")
    
    def test_full_integration(self):
        """測試完整功能整合"""
        self.log("🔗 開始測試完整功能整合...")
        
        try:
            # 測試主程式整合
            self.log("📊 測試主程式整合...")
            
            # 檢查多策略GUI是否可以正常啟動
            from multi_strategy_gui import MultiStrategyGUI
            gui = MultiStrategyGUI()
            
            self.log("✅ 多策略GUI可以正常創建")
            
            # 檢查圖表分析器
            from multi_strategy_chart import MultiStrategySignalAnalyzer
            analyzer = MultiStrategySignalAnalyzer()
            
            self.log("✅ 多策略分析器可以正常創建")
            
            # 檢查所有策略方法
            method_mapping = {
                'RSI策略': 'generate_rsi_signals',
                'MACD策略': 'generate_macd_signals',
                '布林通道策略': 'generate_bollinger_signals',
                '移動平均交叉策略': 'generate_ma_cross_signals'
            }
            
            for strategy, method_name in method_mapping.items():
                if hasattr(analyzer, method_name):
                    self.log(f"✅ {strategy}信號生成器存在")
                else:
                    self.log(f"❌ {strategy}信號生成器缺失")
            
            gui.close()
            
            self.log("🎉 完整功能整合測試完成")
            
        except Exception as e:
            self.log(f"❌ 完整功能整合測試失敗: {e}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式資訊
    app.setApplicationName("多策略改進測試")
    app.setApplicationVersion("1.0")
    
    # 創建主視窗
    window = MultiStrategyImprovementTest()
    window.show()
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
