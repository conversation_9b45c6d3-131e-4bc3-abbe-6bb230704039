#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版除權息下載功能（包含開啟檔案功能）
"""

import os
import sys
import platform
import subprocess
from datetime import datetime

def test_file_operations():
    """測試檔案操作功能"""
    print("🧪 測試檔案操作功能...")
    
    # 創建測試檔案
    test_file = "test_file_operations.csv"
    test_content = """股票代碼,股票名稱,除權息日,現金股利
2330,台積電,2025-07-15,3.0
2317,鴻海,2025-07-20,4.5
2454,聯發科,2025-07-25,2.8"""
    
    try:
        with open(test_file, 'w', encoding='utf-8-sig') as f:
            f.write(test_content)
        
        print(f"✅ 測試檔案已創建: {os.path.abspath(test_file)}")
        
        # 測試開啟檔案功能
        test_open_file(test_file)
        
        # 測試開啟資料夾功能
        test_open_folder(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 檔案操作測試失敗: {e}")
        return False
    finally:
        # 清理測試檔案
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"🗑️ 已清理測試檔案: {test_file}")
            except:
                pass

def test_open_file(file_path):
    """測試開啟檔案功能"""
    try:
        system = platform.system()
        print(f"🖥️ 檢測到作業系統: {system}")
        
        if system == "Windows":
            print("✅ Windows系統 - 可使用 os.startfile() 開啟檔案")
            # 不實際開啟，只測試路徑
            if os.path.exists(file_path):
                print(f"✅ 檔案存在，可以開啟: {file_path}")
            else:
                print(f"❌ 檔案不存在: {file_path}")
                
        elif system == "Darwin":  # macOS
            print("✅ macOS系統 - 可使用 open 命令開啟檔案")
            
        else:  # Linux
            print("✅ Linux系統 - 可使用 xdg-open 命令開啟檔案")
            
        return True
        
    except Exception as e:
        print(f"❌ 開啟檔案測試失敗: {e}")
        return False

def test_open_folder(file_path):
    """測試開啟資料夾功能"""
    try:
        folder_path = os.path.dirname(os.path.abspath(file_path))
        system = platform.system()
        
        print(f"📁 資料夾路徑: {folder_path}")
        
        if system == "Windows":
            print("✅ Windows系統 - 可使用 explorer /select 開啟並選中檔案")
            
        elif system == "Darwin":  # macOS
            print("✅ macOS系統 - 可使用 open -R 開啟並選中檔案")
            
        else:  # Linux
            print("✅ Linux系統 - 可使用 xdg-open 開啟資料夾")
            
        if os.path.exists(folder_path):
            print(f"✅ 資料夾存在，可以開啟: {folder_path}")
        else:
            print(f"❌ 資料夾不存在: {folder_path}")
            
        return True
        
    except Exception as e:
        print(f"❌ 開啟資料夾測試失敗: {e}")
        return False

def test_message_box_simulation():
    """模擬訊息框選擇測試"""
    print("\n🎭 模擬用戶選擇測試...")
    
    choices = [
        ("📂 開啟檔案", "open_file"),
        ("📁 開啟資料夾", "open_folder"),
        ("❌ 關閉", "close")
    ]
    
    print("💬 下載完成後會顯示以下選項：")
    for i, (text, action) in enumerate(choices, 1):
        print(f"   {i}. {text}")
    
    print("\n🎯 各選項功能說明：")
    print("   📂 開啟檔案 - 使用預設程式開啟CSV檔案")
    print("   📁 開啟資料夾 - 開啟包含檔案的資料夾並選中檔案")
    print("   ❌ 關閉 - 僅關閉對話框，不執行其他動作")
    
    return True

def test_cross_platform_compatibility():
    """測試跨平台相容性"""
    print("\n🌐 跨平台相容性測試...")
    
    system = platform.system()
    version = platform.version()
    
    print(f"🖥️ 作業系統: {system}")
    print(f"📋 版本資訊: {version}")
    
    # 檢查必要模組
    modules_to_check = ['os', 'subprocess', 'platform']
    
    for module_name in modules_to_check:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 模組可用")
        except ImportError:
            print(f"❌ {module_name} 模組不可用")
            return False
    
    # 檢查檔案系統操作
    try:
        test_dir = os.getcwd()
        print(f"✅ 當前工作目錄: {test_dir}")
        
        # 測試檔案路徑操作
        test_path = os.path.join(test_dir, "test.csv")
        abs_path = os.path.abspath(test_path)
        dir_path = os.path.dirname(abs_path)
        
        print(f"✅ 檔案路徑操作正常")
        print(f"   相對路徑: {test_path}")
        print(f"   絕對路徑: {abs_path}")
        print(f"   目錄路徑: {dir_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檔案系統操作測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 增強版除權息下載功能測試")
    print("=" * 70)
    
    tests = [
        ("檔案操作功能", test_file_operations),
        ("訊息框選擇模擬", test_message_box_simulation),
        ("跨平台相容性", test_cross_platform_compatibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 執行測試: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
                
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結報告
    print("\n" + "=" * 70)
    print("📊 測試結果總結")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！增強版除權息下載功能準備就緒")
        print("💡 用戶現在可以在下載完成後選擇開啟檔案或資料夾")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print("=" * 70)
