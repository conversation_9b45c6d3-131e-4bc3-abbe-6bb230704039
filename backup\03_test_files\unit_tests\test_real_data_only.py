#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試只使用真實數據的策略
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 設置日誌級別
logging.basicConfig(level=logging.INFO)

# 添加strategies目錄到路徑
sys.path.append('strategies')

def create_test_price_data(stock_id, days=100):
    """創建測試用股價數據"""
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 模擬股價數據
    np.random.seed(42)
    base_price = 50
    prices = []
    current_price = base_price
    
    for i in range(days):
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        prices.append(current_price)
    
    # 創建OHLCV數據
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p * np.random.uniform(0.99, 1.01) for p in prices],
        'High': [p * np.random.uniform(1.00, 1.05) for p in prices],
        'Low': [p * np.random.uniform(0.95, 1.00) for p in prices],
        'Close': prices,
        'Volume': [np.random.randint(100000, 1000000) for _ in range(days)]
    })
    
    df.set_index('Date', inplace=True)
    return df

def test_real_data_only_strategy():
    """測試只使用真實數據的策略"""
    print("🛡️ 測試只使用真實數據的策略")
    print("=" * 60)
    print("⚠️ 為保護投資安全，已完全禁用模擬數據")
    print()
    
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        # 創建策略實例
        strategy = HighYieldTurtleStrategy()
        
        print(f"✅ 策略初始化成功")
        print(f"📊 PKL數據狀態: {'已載入' if strategy.pe_data_cache is not None else '未載入'}")
        
        if strategy.pe_data_cache is not None:
            print(f"📊 PKL數據大小: {strategy.pe_data_cache.shape}")
        
        # 測試不同情況
        test_cases = [
            ("✅ 有PKL真實數據的股票", "1108"),
            ("❌ 股票代碼為None", None),
            ("❌ 股票代碼為空字串", ""),
            ("❌ 不存在的股票代碼", "9999"),
        ]
        
        print(f"\n📊 測試結果:")
        print("-" * 50)
        
        real_data_count = 0
        rejected_count = 0
        
        for case_name, stock_id in test_cases:
            print(f"\n🧪 {case_name}: {stock_id}")
            
            try:
                test_df = create_test_price_data("1108")
                
                # 執行策略分析
                result = strategy.analyze_stock(test_df, stock_id=stock_id)
                
                # 分析結果
                details = result.get('details', {})
                data_source = details.get('data_source', '未知')
                error_type = details.get('error', None)
                
                if error_type:
                    print(f"  🛡️ 安全拒絕: {result['reason']}")
                    rejected_count += 1
                elif data_source == 'PKL真實數據':
                    status = "✅ 符合" if result['suitable'] else "❌ 不符合"
                    score = result.get('score', 0)
                    print(f"  {status} 真實數據分析: 評分 {score}/100")
                    print(f"  📊 數據來源: {data_source}")
                    real_data_count += 1
                else:
                    print(f"  ⚠️ 意外的數據來源: {data_source}")
                
            except Exception as e:
                print(f"  ❌ 測試失敗: {e}")
        
        print(f"\n📊 測試統計:")
        print(f"  ✅ 使用真實數據: {real_data_count} 個案例")
        print(f"  🛡️ 安全拒絕: {rejected_count} 個案例")
        print(f"  📊 模擬數據使用: 0 個案例 (已完全禁用)")
        
        # 測試PKL數據的實際使用
        if strategy.pe_data_cache is not None:
            print(f"\n🔍 PKL真實數據測試:")
            print("-" * 30)
            
            # 從PKL數據中選取幾支股票進行測試
            sample_stocks = strategy.pe_data_cache['股票代號'].head(5).tolist()
            
            for stock_id in sample_stocks:
                pkl_data = strategy.get_stock_pkl_data(stock_id)
                if pkl_data:
                    print(f"  ✅ {stock_id} ({pkl_data['stock_name']}): 殖利率{pkl_data['dividend_yield']:.1f}%, PE{pkl_data['pe_ratio']:.1f}")
                else:
                    print(f"  ❌ {stock_id}: 無PKL數據")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_investment_safety():
    """測試投資安全性"""
    print(f"\n🛡️ 投資安全性測試")
    print("=" * 60)
    
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        strategy = HighYieldTurtleStrategy()
        test_df = create_test_price_data("1108")
        
        # 測試各種可能導致使用模擬數據的情況
        unsafe_scenarios = [
            ("空的FinMind數據", {}),
            ("None的FinMind數據", None),
            ("無效股票代碼", "INVALID"),
        ]
        
        print("🔍 檢查是否會意外使用模擬數據:")
        
        all_safe = True
        for scenario_name, test_data in unsafe_scenarios:
            print(f"\n  📋 {scenario_name}:")
            
            if scenario_name == "無效股票代碼":
                result = strategy.analyze_stock(test_df, stock_id=test_data)
            else:
                result = strategy.analyze_with_finmind_data(test_df, test_data, "TEST")
            
            data_source = result.get('details', {}).get('data_source', '未知')
            
            if '模擬' in data_source or 'proxy' in data_source.lower():
                print(f"    ❌ 危險！使用了模擬數據: {data_source}")
                all_safe = False
            elif '無' in data_source or 'error' in result.get('details', {}):
                print(f"    ✅ 安全拒絕: {result['reason'][:60]}...")
            else:
                print(f"    ✅ 使用真實數據: {data_source}")
        
        if all_safe:
            print(f"\n🎉 投資安全性測試通過！")
            print(f"✅ 所有情況都不會使用模擬數據")
            print(f"✅ 無真實數據時會安全拒絕分析")
        else:
            print(f"\n⚠️ 投資安全性測試失敗！")
            print(f"❌ 發現仍有使用模擬數據的風險")
        
        return all_safe
        
    except Exception as e:
        print(f"❌ 安全性測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🛡️ 真實數據專用策略測試")
    print("=" * 80)
    print("🎯 目標: 確保投資分析只使用真實財務數據")
    print("⚠️ 拒絕任何可能誤導投資決策的模擬數據")
    print()
    
    # 測試真實數據策略
    strategy_test = test_real_data_only_strategy()
    
    # 測試投資安全性
    safety_test = test_investment_safety()
    
    print("\n" + "=" * 80)
    print("📊 測試總結")
    print("=" * 80)
    
    results = [
        ("真實數據策略功能", strategy_test),
        ("投資安全性", safety_test)
    ]
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 總體結果: {passed_count}/{total_count} 測試通過")
    
    if passed_count == total_count:
        print("🎉 投資安全保護成功！")
        print("\n💡 安全特性:")
        print("  ✅ 只使用PKL真實財務數據")
        print("  ✅ 無真實數據時拒絕分析")
        print("  ✅ 完全禁用模擬數據")
        print("  ✅ 明確標示數據來源")
        
        print("\n🚀 現在可以安全使用:")
        print("  1. 啟動主程序進行真實數據分析")
        print("  2. 只有PKL數據完整的股票會被分析")
        print("  3. 避免基於錯誤信息的投資決策")
    else:
        print("⚠️ 安全保護不完整，需要進一步修復")

if __name__ == "__main__":
    main()
