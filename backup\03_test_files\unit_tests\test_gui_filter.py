#!/usr/bin/env python3
"""
測試GUI中的股票篩選功能
"""

import sys
import os
import logging

# 添加當前目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_filter_initialization():
    """測試GUI中的股票篩選器初始化"""
    
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🔍 測試GUI股票篩選器初始化...")
    
    # 模擬GUI初始化股票篩選器的過程
    stock_filter = None
    try:
        from stock_filter import StockFilter
        stock_filter = StockFilter()
        logging.info("✅ 股票篩選器初始化完成")
        print("✅ 股票篩選器初始化完成")
    except ImportError as e:
        logging.warning(f"⚠️ 股票篩選器模組載入失敗: {e}")
        print(f"⚠️ 股票篩選器模組載入失敗: {e}")
        stock_filter = None
    
    # 測試篩選邏輯
    test_stocks = ['0050', '0051', '0052', '0053', '0054', '0055', '0056', '1101', '2330']
    print(f"📊 測試股票清單: {test_stocks}")
    
    # 模擬GUI中的篩選邏輯
    original_count = len(test_stocks)
    if hasattr(sys.modules[__name__], 'stock_filter') and stock_filter:
        print("🎯 執行股票篩選...")
        # 篩選股票代碼
        valid_codes = filter_stock_codes(stock_filter, test_stocks)
        filtered_stocks = [code for code in test_stocks if code in valid_codes]
        filtered_count = len(filtered_stocks)
        
        if filtered_count < original_count:
            logging.info(f"📊 股票清單篩選：原始 {original_count} 支 → 篩選後 {filtered_count} 支")
            print(f"📊 股票清單篩選：原始 {original_count} 支 → 篩選後 {filtered_count} 支")
            
        print(f"篩選後股票清單: {filtered_stocks}")
        
        # 檢查0054
        if '0054' in test_stocks and '0054' not in filtered_stocks:
            print("✅ 0054 已被正確篩選掉")
        elif '0054' in filtered_stocks:
            print("❌ 0054 沒有被篩選掉！")
    else:
        print("❌ 股票篩選器未初始化或為None")

def filter_stock_codes(stock_filter, stock_codes):
    """
    篩選股票代碼清單（模擬GUI方法）
    """
    if not stock_codes:
        return []

    if stock_filter:
        return stock_filter.filter_stock_list(stock_codes)

    # 備用邏輯：如果股票篩選器未載入，返回原始清單
    return stock_codes

if __name__ == "__main__":
    test_gui_filter_initialization()
