#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新檢查來源 bargin_report 檔案
"""

import pandas as pd
import os
from datetime import datetime

def check_source_files():
    """檢查來源檔案的實際情況"""
    print("🔍 重新檢查來源 bargin_report 檔案")
    print("=" * 60)
    
    source_dir = r"D:\□Finlab學習\用 Python 理財：打造自己的 AI 股票理專_原始檔案\202507_新資料庫\history\tables"
    
    files_to_check = [
        ("bargin_report.pkl", os.path.join(source_dir, "bargin_report.pkl")),
        ("bargin_report_old.pkl", os.path.join(source_dir, "bargin_report_old.pkl"))
    ]
    
    file_infos = []
    
    for file_name, file_path in files_to_check:
        print(f"\n📊 檢查 {file_name}...")
        
        if not os.path.exists(file_path):
            print(f"❌ 檔案不存在: {file_path}")
            continue
        
        try:
            # 檔案大小
            file_size = os.path.getsize(file_path)
            file_mtime = os.path.getmtime(file_path)
            
            print(f"   檔案大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
            print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
            
            # 讀取資料
            print("   正在讀取資料...")
            data = pd.read_pickle(file_path)
            
            print(f"   資料筆數: {len(data):,}")
            print(f"   索引類型: {type(data.index)}")
            
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
            
            if hasattr(data, 'columns'):
                print(f"   欄位數量: {len(data.columns)}")
                print(f"   欄位: {list(data.columns)[:3]}...")
            
            # 檢查日期
            if 'date' in data.index.names:
                dates = data.index.get_level_values('date')
                start_date = dates.min()
                end_date = dates.max()
                unique_dates = len(dates.unique())
                
                print(f"   ✅ 日期範圍: {start_date} 至 {end_date}")
                print(f"   唯一日期數: {unique_dates}")
                
                file_infos.append({
                    'name': file_name,
                    'path': file_path,
                    'size_mb': file_size/1024/1024,
                    'record_count': len(data),
                    'start_date': start_date,
                    'end_date': end_date,
                    'unique_dates': unique_dates
                })
            else:
                print("   ⚠️ 沒有日期索引")
                
        except Exception as e:
            print(f"   ❌ 讀取失敗: {str(e)}")
            import traceback
            print(f"   詳細錯誤: {traceback.format_exc()}")
    
    # 分析結果
    print("\n" + "=" * 60)
    print("📊 檔案比較分析")
    print("=" * 60)
    
    if len(file_infos) >= 2:
        file1, file2 = file_infos[0], file_infos[1]
        
        print(f"📋 檔案對比:")
        print(f"   {file1['name']}:")
        print(f"     大小: {file1['size_mb']:.1f} MB")
        print(f"     筆數: {file1['record_count']:,}")
        print(f"     日期: {file1['start_date']} 至 {file1['end_date']}")
        
        print(f"   {file2['name']}:")
        print(f"     大小: {file2['size_mb']:.1f} MB")
        print(f"     筆數: {file2['record_count']:,}")
        print(f"     日期: {file2['start_date']} 至 {file2['end_date']}")
        
        # 檢查時間關係
        print(f"\n🔍 時間關係分析:")
        
        if file1['end_date'] < file2['start_date']:
            gap_days = (file2['start_date'] - file1['end_date']).days
            print(f"   ✅ 時間連續: {file1['name']} 結束 → {file2['name']} 開始")
            print(f"   間隔: {gap_days} 天")
            print(f"   建議: 可以合併，預期大小約 {file1['size_mb'] + file2['size_mb']:.1f} MB")
            
        elif file2['end_date'] < file1['start_date']:
            gap_days = (file1['start_date'] - file2['end_date']).days
            print(f"   ✅ 時間連續: {file2['name']} 結束 → {file1['name']} 開始")
            print(f"   間隔: {gap_days} 天")
            print(f"   建議: 可以合併，預期大小約 {file1['size_mb'] + file2['size_mb']:.1f} MB")
            
        else:
            # 檢查重疊
            overlap_start = max(file1['start_date'], file2['start_date'])
            overlap_end = min(file1['end_date'], file2['end_date'])
            
            if overlap_start <= overlap_end:
                overlap_days = (overlap_end - overlap_start).days + 1
                print(f"   ⚠️ 時間重疊: {overlap_start} 至 {overlap_end}")
                print(f"   重疊天數: {overlap_days}")
                print(f"   建議: 需要去重合併")
            else:
                print(f"   ❌ 時間關係複雜，需要詳細檢查")
    
    return file_infos

def main():
    """主函數"""
    file_infos = check_source_files()
    
    if len(file_infos) >= 2:
        total_size = sum(info['size_mb'] for info in file_infos)
        total_records = sum(info['record_count'] for info in file_infos)
        
        print(f"\n💡 合併預期:")
        print(f"   總大小: 約 {total_size:.1f} MB")
        print(f"   總筆數: 約 {total_records:,} 筆 (去重前)")
        
        # 檢查當前目標檔案
        target_file = "history/tables/bargin_report.pkl"
        if os.path.exists(target_file):
            target_size = os.path.getsize(target_file) / 1024 / 1024
            print(f"\n🔍 當前目標檔案:")
            print(f"   大小: {target_size:.1f} MB")
            
            if target_size < total_size * 0.8:  # 如果目標檔案明顯小於預期
                print(f"   ⚠️ 目標檔案可能不完整 (預期約 {total_size:.1f} MB)")
                print(f"   建議重新合併")
            else:
                print(f"   ✅ 目標檔案大小合理")

if __name__ == "__main__":
    main()
