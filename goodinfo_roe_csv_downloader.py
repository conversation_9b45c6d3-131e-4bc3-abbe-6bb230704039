#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GoodInfo ROE資料CSV下載器
使用Selenium模擬點擊網站的"匯出CSV"功能
類似除權息資料下載的實現方式
"""

import os
import time
import pandas as pd
import sqlite3
from datetime import datetime
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class GoodinfoROECSVDownloader:
    def __init__(self):
        self.base_url = "https://goodinfo.tw/tw2/StockList.asp"
        self.download_dir = "D:/Finlab/history/tables"
        self.db_path = os.path.join(self.download_dir, "roe_data.db")
        
        # 確保下載目錄存在
        os.makedirs(self.download_dir, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """設置Chrome瀏覽器驅動"""
        try:
            options = Options()
            
            # 設置下載目錄
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            options.add_experimental_option("prefs", prefs)
            
            # 其他選項
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 啟用無頭模式 - 不顯示瀏覽器視窗
            options.add_argument('--headless')

            # 額外的無頭模式優化選項
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript-harmony-shipping')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-web-security')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--no-first-run')
            options.add_argument('--safebrowsing-disable-auto-update')
            options.add_argument('--enable-automation')
            options.add_argument('--password-store=basic')
            options.add_argument('--use-mock-keychain')

            # 減少Google APIs相關警告
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-component-extensions-with-background-pages')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-logging')
            options.add_argument('--disable-notifications')
            options.add_argument('--log-level=3')
            options.add_argument('--silent')

            # 禁用Google服務
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)
            
            driver = webdriver.Chrome(options=options)
            self.logger.info("🔧 Chrome瀏覽器已啟動 (無頭模式 - 不顯示視窗)")
            return driver
            
        except Exception as e:
            self.logger.error(f"❌ 設置瀏覽器驅動失敗: {e}")
            return None
    
    def download_roe_csv_segments(self, year=None, segments=None):
        """分段下載ROE CSV資料"""
        if segments is None:
            # 預設分段：1-300, 301-600, 601-900, 901-1200, 1201-1500, 1501-1800, 1801-1898
            segments = [
                (1, 300), (301, 600), (601, 900), (901, 1200),
                (1201, 1500), (1501, 1800), (1801, 1898)
            ]

        all_csv_files = []

        for start, end in segments:
            self.logger.info(f"📥 下載第 {start}-{end} 筆資料...")
            csv_file = self.download_roe_csv_segment(year, start, end)
            if csv_file:
                all_csv_files.append(csv_file)
            else:
                self.logger.warning(f"⚠️ 第 {start}-{end} 筆資料下載失敗")

            # 分段之間等待，避免被網站封鎖
            time.sleep(3)

        return all_csv_files

    def download_roe_csv_segment(self, year=None, start_row=1, end_row=300):
        """下載單一分段的ROE CSV資料"""
        driver = None
        try:
            self.logger.info(f"🚀 開始下載ROE CSV資料 (第{start_row}-{end_row}筆)...")

            # 設置瀏覽器
            driver = self.setup_driver()
            if not driver:
                return None

            # 訪問ROE頁面
            url = f"{self.base_url}?MARKET_CAT=熱門排行&INDUSTRY_CAT=年度ROE最高"
            self.logger.info(f"📱 訪問頁面: {url}")
            driver.get(url)

            # 等待頁面載入
            wait = WebDriverWait(driver, 30)

            # 等待表格出現
            try:
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
                self.logger.info("✅ 頁面載入成功")
            except TimeoutException:
                self.logger.warning("⚠️ 頁面載入超時，繼續嘗試...")

            # 等待額外時間確保頁面完全載入
            time.sleep(5)

            # 如果指定年份，嘗試選擇年份
            if year:
                self.select_year(driver, year)

            # 設置顯示範圍
            if not self.set_display_range(driver, start_row, end_row):
                self.logger.warning(f"⚠️ 無法設置顯示範圍 {start_row}-{end_row}")

            # 尋找並點擊匯出CSV按鈕
            csv_downloaded = self.click_export_csv(driver)

            if csv_downloaded:
                # 等待下載完成
                downloaded_file = self.wait_for_download()
                if downloaded_file:
                    # 重新命名檔案以包含分段資訊
                    segment_file = self.rename_segment_file(downloaded_file, start_row, end_row)
                    self.logger.info(f"✅ CSV分段下載成功: {segment_file}")
                    return segment_file
                else:
                    self.logger.warning("⚠️ 未找到下載的CSV文件")
            
            # 如果CSV下載失敗，嘗試抓取頁面資料
            self.logger.warning("⚠️ CSV下載失敗，嘗試抓取頁面資料...")
            return self.scrape_page_data(driver)

        except Exception as e:
            self.logger.error(f"❌ 下載ROE CSV分段失敗: {e}")
            return None
        finally:
            if driver:
                driver.quit()

    def set_display_range(self, driver, start_row, end_row):
        """設置頁面顯示範圍"""
        try:
            # 尋找顯示範圍的下拉選單
            range_selects = driver.find_elements(By.TAG_NAME, "select")

            for select_element in range_selects:
                options = select_element.find_elements(By.TAG_NAME, "option")
                for option in options:
                    option_text = option.text.strip()
                    # 尋找包含數字範圍的選項
                    if f"{start_row}~{end_row}" in option_text or f"{start_row}-{end_row}" in option_text:
                        self.logger.info(f"🎯 設置顯示範圍: {option_text}")
                        option.click()
                        time.sleep(2)
                        return True
                    # 也檢查其他可能的格式
                    elif str(start_row) in option_text and str(end_row) in option_text:
                        self.logger.info(f"🎯 設置顯示範圍: {option_text}")
                        option.click()
                        time.sleep(2)
                        return True

            self.logger.warning(f"⚠️ 未找到範圍 {start_row}-{end_row} 的選項")
            return False

        except Exception as e:
            self.logger.error(f"❌ 設置顯示範圍失敗: {e}")
            return False

    def rename_segment_file(self, original_file, start_row, end_row):
        """重新命名分段檔案"""
        try:
            if not os.path.exists(original_file):
                return original_file

            # 獲取檔案路徑和擴展名
            file_dir = os.path.dirname(original_file)
            file_name, file_ext = os.path.splitext(os.path.basename(original_file))

            # 創建新的檔案名稱
            new_file_name = f"{file_name}_segment_{start_row}_{end_row}{file_ext}"
            new_file_path = os.path.join(file_dir, new_file_name)

            # 重新命名檔案
            os.rename(original_file, new_file_path)
            return new_file_path

        except Exception as e:
            self.logger.error(f"❌ 重新命名檔案失敗: {e}")
            return original_file

    def download_roe_csv(self, year=None):
        """下載ROE CSV資料 (保持向後相容性)"""
        return self.download_roe_csv_segment(year, 1, 300)
    
    def select_year(self, driver, year):
        """選擇年份"""
        try:
            self.logger.info(f"📅 嘗試選擇年份: {year}")

            # 等待頁面完全載入
            time.sleep(3)

            # 根據用戶截圖更新的年份選擇器列表
            year_selectors = [
                # 基於截圖的特定選擇器
                "select:has(option[value='2024'])",  # 包含2024選項的select
                "select:has(option[value='2023'])",  # 包含2023選項的select
                "select:has(option[value='2022'])",  # 包含2022選項的select
                # 通用年份選擇器
                "select[name='YEAR']",
                "select[name='year']",
                "select[id*='year']",
                "select[id*='Year']",
                "select[class*='year']",
                "select[class*='Year']",
                # 所有select元素（最後嘗試）
                "select",
                "input[name*='year']",
                "input[id*='year']"
            ]

            # 嘗試每個選擇器
            for i, selector in enumerate(year_selectors):
                try:
                    self.logger.info(f"🔍 嘗試選擇器 {i+1}/{len(year_selectors)}: {selector}")

                    # 等待元素出現
                    wait = WebDriverWait(driver, 5)
                    year_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))

                    # 如果是select元素
                    if year_element.tag_name == 'select':
                        select = Select(year_element)

                        # 獲取所有選項
                        options = [option.text.strip() for option in select.options]
                        option_values = [option.get_attribute('value') for option in select.options]

                        self.logger.info(f"📋 找到select選項: {options[:10]}...")  # 只顯示前10個

                        # 檢查是否包含年份選項
                        year_str = str(year)
                        has_year_options = any(year_str in opt for opt in options + option_values)

                        if has_year_options:
                            self.logger.info(f"✅ 確認這是年份選擇器，包含 {year} 年選項")

                            # 嘗試不同的選擇方式
                            success = False

                            # 方法1: 按值選擇
                            try:
                                select.select_by_value(year_str)
                                self.logger.info(f"✅ 成功選擇年份: {year} (by value)")
                                success = True
                            except Exception as e:
                                self.logger.debug(f"按值選擇失敗: {e}")

                            # 方法2: 按可見文字選擇
                            if not success:
                                try:
                                    select.select_by_visible_text(year_str)
                                    self.logger.info(f"✅ 成功選擇年份: {year} (by text)")
                                    success = True
                                except Exception as e:
                                    self.logger.debug(f"按文字選擇失敗: {e}")

                            # 方法3: 嘗試包含年份的選項
                            if not success:
                                for option in select.options:
                                    if year_str in option.text or year_str in option.get_attribute('value'):
                                        try:
                                            select.select_by_visible_text(option.text)
                                            self.logger.info(f"✅ 成功選擇年份: {year} (by matching text: {option.text})")
                                            success = True
                                            break
                                        except Exception as e:
                                            self.logger.debug(f"匹配選項選擇失敗: {e}")

                            if success:
                                time.sleep(3)  # 等待頁面更新
                                return True
                        else:
                            self.logger.debug(f"此select不包含年份選項，跳過")
                            continue

                    # 如果是input元素
                    elif year_element.tag_name == 'input':
                        year_element.clear()
                        year_element.send_keys(str(year))
                        year_element.send_keys(Keys.ENTER)
                        self.logger.info(f"✅ 成功輸入年份: {year}")
                        time.sleep(2)
                        return True

                except TimeoutException:
                    self.logger.debug(f"⏰ 選擇器超時: {selector}")
                    continue
                except Exception as e:
                    self.logger.debug(f"⚠️ 選擇器失敗: {selector}, 錯誤: {e}")
                    continue

            # 如果所有選擇器都失敗，嘗試檢查頁面內容
            self.logger.info("🔍 檢查頁面中的年份相關元素...")
            try:
                # 查找所有包含年份的元素
                all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '年') or contains(@name, 'year') or contains(@id, 'year')]")
                if all_elements:
                    self.logger.info(f"📋 找到 {len(all_elements)} 個年份相關元素")
                    for elem in all_elements[:5]:  # 只顯示前5個
                        try:
                            self.logger.info(f"  • {elem.tag_name}: {elem.get_attribute('name')} | {elem.get_attribute('id')} | {elem.text[:50]}")
                        except:
                            pass
            except:
                pass

            # 檢查頁面是否已經顯示目標年份的資料
            current_year = datetime.now().year

            # 檢查是否有年份選擇器存在
            year_selector_exists = False
            try:
                year_selectors_check = [
                    "select:has(option[value*='202'])",  # 包含20xx年的select
                    "select:has(option[text*='202'])"   # 文字包含20xx年的select
                ]

                for selector in year_selectors_check:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            year_selector_exists = True
                            break
                    except:
                        continue
            except:
                pass

            if year == current_year or year == current_year - 1:
                if year_selector_exists:
                    self.logger.info(f"✅ 找到年份選擇器但目標年份 {year} 可能是預設值，使用當前頁面資料")
                else:
                    self.logger.info(f"✅ GoodInfo預設顯示最新年度資料，當前頁面應該是 {year} 年度資料")
                return True
            else:
                if year_selector_exists:
                    self.logger.warning(f"⚠️ 找到年份選擇器但無法選擇 {year} 年，可能該年份不在可選範圍內")
                    self.logger.info(f"💡 建議：請檢查GoodInfo網站上的可用年份選項")
                else:
                    self.logger.warning(f"⚠️ 未找到年份選擇器，GoodInfo可能不支援 {year} 年度資料查詢")
                    self.logger.info(f"💡 建議：GoodInfo通常只顯示最近1-2年的ROE資料")

                # 對於不支援的年份，仍然嘗試使用預設資料
                self.logger.info(f"🔄 將使用GoodInfo預設的最新年度資料代替")
                return True

        except Exception as e:
            self.logger.warning(f"⚠️ 年份選擇失敗: {e}")
            return False
    
    def click_export_csv(self, driver):
        """點擊匯出CSV按鈕"""
        try:
            self.logger.info("🔍 尋找匯出CSV按鈕...")

            # 等待頁面完全載入
            time.sleep(5)

            # 更精確的CSV匯出按鈕選擇器
            csv_selectors = [
                "input[value='匯出CSV']",
                "input[onclick*='export2csv']",
                "input[type='button'][value*='CSV']",
                "button[onclick*='export2csv']",
                "a[onclick*='export2csv']",
                "input[value*='匯出']",
                "button[value*='匯出']",
                "input[onclick*='CSV']",
                "button[onclick*='CSV']"
            ]

            for i, selector in enumerate(csv_selectors):
                try:
                    self.logger.info(f"🔍 嘗試選擇器 {i+1}/{len(csv_selectors)}: {selector}")

                    # 等待元素出現
                    wait = WebDriverWait(driver, 10)
                    csv_button = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))

                    if csv_button.is_displayed() and csv_button.is_enabled():
                        self.logger.info(f"✅ 找到CSV按鈕: {selector}")

                        # 嘗試多種點擊策略
                        click_success = False

                        # 策略1: JavaScript點擊（最可靠）
                        try:
                            self.logger.info("🔧 嘗試JavaScript點擊...")
                            driver.execute_script("arguments[0].click();", csv_button)
                            self.logger.info("✅ JavaScript點擊成功")
                            time.sleep(3)
                            click_success = True
                        except Exception as e:
                            self.logger.debug(f"⚠️ JavaScript點擊失敗: {e}")

                        # 策略2: ActionChains點擊
                        if not click_success:
                            try:
                                self.logger.info("🔧 嘗試ActionChains點擊...")
                                from selenium.webdriver.common.action_chains import ActionChains
                                actions = ActionChains(driver)
                                actions.move_to_element(csv_button).click().perform()
                                self.logger.info("✅ ActionChains點擊成功")
                                time.sleep(3)
                                click_success = True
                            except Exception as e:
                                self.logger.debug(f"⚠️ ActionChains點擊失敗: {e}")

                        # 策略3: 滾動後點擊
                        if not click_success:
                            try:
                                self.logger.info("🔧 嘗試滾動後點擊...")
                                # 滾動到元素中心位置
                                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", csv_button)
                                time.sleep(2)

                                # 確保元素可見後點擊
                                if csv_button.is_displayed() and csv_button.is_enabled():
                                    csv_button.click()
                                    self.logger.info("✅ 滾動後點擊成功")
                                    time.sleep(3)
                                    click_success = True
                            except Exception as e:
                                self.logger.debug(f"⚠️ 滾動後點擊失敗: {e}")

                        if click_success:
                            return True

                except TimeoutException:
                    self.logger.debug(f"⏰ 選擇器超時: {selector}")
                    continue
                except Exception as e:
                    self.logger.debug(f"⚠️ 選擇器失敗: {selector}, 錯誤: {e}")
                    continue
            
            # 如果找不到按鈕，嘗試JavaScript執行
            self.logger.info("🔄 嘗試JavaScript方式匯出...")
            try:
                # 更全面的JavaScript匯出函數列表
                js_functions = [
                    "export2csv_StockList();",  # GoodInfo特定函數
                    "ExportCSV();",
                    "exportCSV();",
                    "downloadCSV();",
                    "ExportToCSV();",
                    "export_csv();",
                    "exportToCSV();",
                    "doExportCSV();",
                    "csv_export();",
                    "StockList_ExportCSV();"
                ]

                for i, js_func in enumerate(js_functions):
                    try:
                        self.logger.info(f"🔧 嘗試JavaScript函數 {i+1}/{len(js_functions)}: {js_func}")
                        driver.execute_script(js_func)
                        self.logger.info(f"✅ 執行JavaScript成功: {js_func}")
                        time.sleep(5)  # 增加等待時間
                        return True
                    except Exception as e:
                        self.logger.debug(f"⚠️ JavaScript函數失敗: {js_func}, 錯誤: {e}")
                        continue

            except Exception as e:
                self.logger.warning(f"⚠️ JavaScript執行失敗: {e}")

            self.logger.warning("❌ 未找到可用的CSV匯出方式")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ CSV匯出失敗: {e}")
            return False
    
    def wait_for_download(self, timeout=30):
        """等待下載完成"""
        try:
            self.logger.info("⏳ 等待CSV文件下載...")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                # 檢查下載目錄中的新文件
                files = os.listdir(self.download_dir)
                csv_files = [f for f in files if f.endswith('.csv') and 'roe' in f.lower()]
                
                if csv_files:
                    # 找到最新的CSV文件
                    latest_file = max(csv_files, key=lambda f: os.path.getctime(os.path.join(self.download_dir, f)))
                    file_path = os.path.join(self.download_dir, latest_file)
                    
                    # 檢查文件是否下載完成（大小穩定）
                    if self.is_download_complete(file_path):
                        return file_path
                
                time.sleep(1)
            
            self.logger.warning("⚠️ 下載超時")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 等待下載失敗: {e}")
            return None
    
    def is_download_complete(self, file_path):
        """檢查下載是否完成"""
        try:
            if not os.path.exists(file_path):
                return False
            
            # 檢查文件大小是否穩定
            size1 = os.path.getsize(file_path)
            time.sleep(1)
            size2 = os.path.getsize(file_path)
            
            return size1 == size2 and size1 > 0
            
        except:
            return False
    
    def scrape_page_data(self, driver):
        """抓取頁面資料作為備用方案"""
        try:
            self.logger.info("📊 抓取頁面ROE資料...")
            
            # 獲取頁面HTML
            html = driver.page_source

            # 使用pandas讀取表格 (修正FutureWarning)
            from io import StringIO
            tables = pd.read_html(StringIO(html))
            
            for i, table in enumerate(tables):
                if self.is_roe_table(table):
                    self.logger.info(f"✅ 找到ROE資料表格 (表格 {i+1})")
                    
                    # 清理和處理資料
                    cleaned_data = self.clean_roe_data(table)
                    
                    # 儲存為CSV
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    csv_filename = f"roe_data_scraped_{timestamp}.csv"
                    csv_path = os.path.join(self.download_dir, csv_filename)
                    
                    cleaned_data.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    self.logger.info(f"✅ 資料已儲存: {csv_path}")
                    
                    return csv_path
            
            self.logger.warning("❌ 未找到ROE資料表格")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 頁面資料抓取失敗: {e}")
            return None
    
    def is_roe_table(self, table):
        """判斷是否為ROE資料表格"""
        try:
            # 檢查表格欄位
            columns = table.columns.tolist()
            column_text = ' '.join(str(col) for col in columns)
            
            # ROE相關關鍵字
            roe_keywords = ['ROE', '代號', '名稱', 'EPS', '排名']
            keyword_count = sum(1 for keyword in roe_keywords if keyword in column_text)
            
            # 檢查資料行數
            return keyword_count >= 2 and len(table) >= 5
            
        except:
            return False
    
    def clean_roe_data(self, table):
        """清理ROE資料"""
        try:
            # 重新命名欄位
            column_mapping = {
                '排名': 'rank',
                '代號': 'stock_code', 
                '名稱': 'stock_name',
                'ROE(%)': 'roe_value',
                'ROE變化': 'roe_change',
                'EPS(元)': 'eps_value'
            }
            
            # 找到對應的欄位
            renamed_columns = {}
            for old_name, new_name in column_mapping.items():
                for col in table.columns:
                    if old_name in str(col):
                        renamed_columns[col] = new_name
                        break
            
            # 重新命名
            if renamed_columns:
                table = table.rename(columns=renamed_columns)
            
            # 添加時間戳
            table['crawl_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            table['data_source'] = 'goodinfo_scraped'
            
            return table
            
        except Exception as e:
            self.logger.error(f"❌ 資料清理失敗: {e}")
            return table
    
    def clean_csv_data(self, csv_file):
        """清理CSV資料，移除重複的標題行"""
        try:
            self.logger.info(f"🧹 清理CSV資料: {csv_file}")

            # 讀取原始CSV檔案
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()

            if not lines:
                self.logger.warning("⚠️ CSV檔案為空")
                return None

            # 找到第一行標題
            header_line = None
            cleaned_lines = []

            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                # 檢查是否為標題行（包含"代號"、"名稱"等關鍵字）
                if self.is_header_line(line):
                    if header_line is None:
                        # 第一次遇到標題行，保留它
                        header_line = line
                        cleaned_lines.append(line + '\n')
                        self.logger.info(f"📋 找到標題行: {line[:50]}...")
                    else:
                        # 後續的標題行跳過
                        self.logger.debug(f"🗑️ 跳過重複標題行: {line[:50]}...")
                        continue
                else:
                    # 資料行，保留
                    if header_line is not None:  # 確保已經有標題行
                        cleaned_lines.append(line + '\n')

            if not header_line:
                self.logger.warning("⚠️ 未找到有效的標題行")
                return None

            # 寫入清理後的檔案
            cleaned_file = csv_file.replace('.csv', '_cleaned.csv')
            with open(cleaned_file, 'w', encoding='utf-8-sig') as f:
                f.writelines(cleaned_lines)

            self.logger.info(f"✅ CSV清理完成: {cleaned_file} (原始: {len(lines)} 行 -> 清理後: {len(cleaned_lines)} 行)")
            return cleaned_file

        except Exception as e:
            self.logger.error(f"❌ CSV清理失敗: {e}")
            return None

    def is_header_line(self, line):
        """判斷是否為標題行"""
        # 擴展關鍵字列表，包含英文欄位名
        header_keywords = [
            '代號', '名稱', 'ROE', 'EPS', '財報', '營收', '毛利', '淨利',
            'stock_code', 'stock_name', 'roe_value', 'eps_value',
            'report_year', 'rank_position', 'crawl_date', 'data_source'
        ]
        line_lower = line.lower()

        # 檢查是否包含標題關鍵字
        keyword_count = sum(1 for keyword in header_keywords if keyword.lower() in line_lower or keyword in line)

        # 降低門檻：至少包含2個關鍵字，或者包含特定的組合
        if keyword_count >= 2:
            return True

        # 特殊檢查：如果包含逗號且有代號或stock_code，很可能是標題行
        if ',' in line and ('代號' in line or 'stock_code' in line_lower):
            return True

        return False

    def import_csv_to_database(self, csv_file):
        """將CSV資料匯入數據庫"""
        try:
            self.logger.info(f"💾 匯入CSV到數據庫: {csv_file}")

            # 先清理CSV資料
            cleaned_csv = self.clean_csv_data(csv_file)
            if not cleaned_csv:
                self.logger.error("❌ CSV清理失敗")
                return False

            # 讀取清理後的CSV
            df = pd.read_csv(cleaned_csv, encoding='utf-8-sig')

            # 連接數據庫
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 創建表格
            self.create_roe_table(conn)

            # 清理和標準化資料
            df_cleaned = self.standardize_data(df)

            if len(df_cleaned) == 0:
                self.logger.warning("⚠️ 沒有資料可以匯入")
                conn.close()
                return False

            # 使用INSERT OR REPLACE避免UNIQUE約束錯誤
            self.logger.info(f"📥 開始匯入 {len(df_cleaned)} 筆資料...")

            success_count = 0
            error_count = 0

            for index, row in df_cleaned.iterrows():
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO roe_data
                        (stock_code, stock_name, roe_value, roe_change, eps_value,
                         report_year, rank_position, crawl_date, data_source)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row.get('stock_code'),
                        row.get('stock_name'),
                        row.get('roe_value'),
                        row.get('roe_change'),
                        row.get('eps_value'),
                        row.get('report_year'),
                        row.get('rank_position'),
                        row.get('crawl_date'),
                        row.get('data_source')
                    ))
                    success_count += 1

                except Exception as e:
                    error_count += 1
                    self.logger.warning(f"⚠️ 匯入第 {index+1} 筆資料失敗: {e}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 匯入完成: 成功 {success_count} 筆，失敗 {error_count} 筆")

            # 清理臨時檔案
            try:
                os.remove(cleaned_csv)
            except:
                pass

            # 只要有成功匯入的資料就算成功
            return success_count > 0

        except Exception as e:
            self.logger.error(f"❌ 數據庫匯入失敗: {e}")
            return False

    def import_multiple_csv_to_database(self, csv_files):
        """將多個CSV檔案匯入數據庫"""
        try:
            self.logger.info(f"💾 批量匯入 {len(csv_files)} 個CSV檔案到數據庫")

            total_success = 0
            total_error = 0

            for i, csv_file in enumerate(csv_files, 1):
                self.logger.info(f"📥 處理第 {i}/{len(csv_files)} 個檔案: {os.path.basename(csv_file)}")

                if self.import_csv_to_database(csv_file):
                    total_success += 1
                    self.logger.info(f"✅ 第 {i} 個檔案匯入成功")
                else:
                    total_error += 1
                    self.logger.warning(f"⚠️ 第 {i} 個檔案匯入失敗")

            self.logger.info(f"🎯 批量匯入完成: 成功 {total_success} 個檔案，失敗 {total_error} 個檔案")
            return total_success > 0

        except Exception as e:
            self.logger.error(f"❌ 批量匯入失敗: {e}")
            return False

    def download_and_import_all_segments(self, year=None):
        """下載所有分段並匯入數據庫"""
        try:
            self.logger.info("🚀 開始完整的ROE資料下載和匯入流程...")

            # 下載所有分段
            csv_files = self.download_roe_csv_segments(year)

            if not csv_files:
                self.logger.error("❌ 沒有成功下載任何CSV檔案")
                return False

            self.logger.info(f"📥 成功下載 {len(csv_files)} 個分段檔案")

            # 匯入所有檔案到數據庫
            success = self.import_multiple_csv_to_database(csv_files)

            # 清理下載的檔案（可選）
            self.cleanup_downloaded_files(csv_files)

            if success:
                self.logger.info("🎉 完整的ROE資料下載和匯入流程完成！")
            else:
                self.logger.error("❌ ROE資料匯入失敗")

            return success

        except Exception as e:
            self.logger.error(f"❌ 完整流程執行失敗: {e}")
            return False

    def cleanup_downloaded_files(self, csv_files, keep_files=False):
        """清理下載的檔案"""
        if keep_files:
            self.logger.info("📁 保留下載的CSV檔案")
            return

        try:
            for csv_file in csv_files:
                if os.path.exists(csv_file):
                    os.remove(csv_file)
                    self.logger.debug(f"🗑️ 已刪除: {os.path.basename(csv_file)}")

            self.logger.info(f"🧹 已清理 {len(csv_files)} 個下載檔案")

        except Exception as e:
            self.logger.warning(f"⚠️ 清理檔案時發生錯誤: {e}")
    
    def create_roe_table(self, conn):
        """創建ROE資料表格"""
        cursor = conn.cursor()

        # 創建表格
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roe_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                roe_value REAL,
                roe_change REAL,
                eps_value REAL,
                report_year INTEGER,
                rank_position INTEGER,
                crawl_date TEXT,
                data_source TEXT DEFAULT 'goodinfo_csv',
                UNIQUE(stock_code, report_year)
            )
        """)

        # 檢查並添加缺失的欄位
        cursor.execute("PRAGMA table_info(roe_data)")
        existing_columns = [column[1] for column in cursor.fetchall()]

        required_columns = {
            'roe_change': 'REAL',
            'eps_value': 'REAL',
            'report_year': 'INTEGER',
            'rank_position': 'INTEGER',
            'data_source': 'TEXT DEFAULT "goodinfo_csv"'
        }

        for column_name, column_type in required_columns.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE roe_data ADD COLUMN {column_name} {column_type}")
                    self.logger.info(f"✅ 添加數據庫欄位: {column_name}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 添加欄位 {column_name} 失敗: {e}")

        # 驗證表格結構
        cursor.execute("PRAGMA table_info(roe_data)")
        final_columns = [column[1] for column in cursor.fetchall()]
        self.logger.info(f"📊 數據庫表格欄位: {final_columns}")
    
    def standardize_data(self, df):
        """標準化資料格式"""
        try:
            self.logger.info(f"📊 開始標準化資料，原始欄位: {list(df.columns)}")

            # 標準化欄位名稱 - 根據實際CSV欄位
            column_mapping = {
                '排 名': 'rank_position',
                'stock_code': 'stock_code',  # 已經是正確名稱
                'stock_name': 'stock_name',  # 已經是正確名稱
                'ROE (%)': 'roe_value',
                'ROE 增減': 'roe_change',
                'EPS (元)': 'eps_value',
                '財報 年度': 'report_year',
                'crawl_date': 'crawl_date',  # 已經是正確名稱
                'data_source': 'data_source'  # 已經是正確名稱
            }

            # 重新命名欄位
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df = df.rename(columns={old_col: new_col})
                    self.logger.info(f"✅ 重新命名欄位: {old_col} -> {new_col}")

            # 確保必要欄位存在
            if 'rank_position' not in df.columns and '排 名' in df.columns:
                df['rank_position'] = df['排 名']

            if 'report_year' not in df.columns:
                if '財報 年度' in df.columns:
                    df['report_year'] = df['財報 年度']
                else:
                    df['report_year'] = datetime.now().year
                    self.logger.info(f"✅ 添加預設年份: {datetime.now().year}")

            if 'crawl_date' not in df.columns:
                df['crawl_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.logger.info("✅ 添加爬取時間")

            if 'data_source' not in df.columns:
                df['data_source'] = 'goodinfo_csv'
                self.logger.info("✅ 添加資料來源")

            # 清理數值欄位
            numeric_columns = ['roe_value', 'roe_change', 'eps_value', 'rank_position', 'report_year']
            for col in numeric_columns:
                if col in df.columns:
                    original_values = df[col].head(3).tolist()
                    # 清理數值：移除+號、%號、逗號，轉換為數值
                    df[col] = pd.to_numeric(
                        df[col].astype(str).str.replace('+', '').str.replace('%', '').str.replace(',', '').str.strip(),
                        errors='coerce'
                    )
                    cleaned_values = df[col].head(3).tolist()
                    self.logger.info(f"✅ 清理數值欄位 {col}: {original_values} -> {cleaned_values}")

            # 只保留需要的欄位
            required_columns = ['stock_code', 'stock_name', 'roe_value', 'roe_change', 'eps_value',
                              'report_year', 'rank_position', 'crawl_date', 'data_source']

            available_columns = [col for col in required_columns if col in df.columns]
            df_cleaned = df[available_columns].copy()

            self.logger.info(f"✅ 標準化完成，最終欄位: {list(df_cleaned.columns)}")
            self.logger.info(f"📊 資料筆數: {len(df_cleaned)}")

            # 顯示前3筆資料作為驗證
            if len(df_cleaned) > 0:
                self.logger.info("📋 前3筆資料預覽:")
                for i, row in df_cleaned.head(3).iterrows():
                    self.logger.info(f"  {i+1}. {row.get('stock_code', 'N/A')} {row.get('stock_name', 'N/A')} - ROE: {row.get('roe_value', 'N/A')}%")

            return df_cleaned

        except Exception as e:
            self.logger.error(f"❌ 資料標準化失敗: {e}")
            import traceback
            self.logger.error(f"錯誤詳情: {traceback.format_exc()}")
            return df

def test_roe_csv_downloader():
    """測試ROE CSV下載器"""
    print("🚀 測試ROE CSV下載器 - 分段下載版本")
    print("=" * 60)

    downloader = GoodinfoROECSVDownloader()

    # 測試選項
    test_mode = input("選擇測試模式 (1: 單一分段, 2: 完整分段下載): ").strip()

    if test_mode == "1":
        # 測試單一分段下載
        print("📥 測試單一分段下載 (1-300筆)...")
        current_year = datetime.now().year
        csv_file = downloader.download_roe_csv_segment(year=current_year, start_row=1, end_row=300)

        if csv_file:
            print(f"✅ 分段下載成功: {csv_file}")

            # 匯入數據庫
            if downloader.import_csv_to_database(csv_file):
                print("✅ 數據庫匯入成功")
                return True
        else:
            print("❌ 分段下載失敗")
            return False

    elif test_mode == "2":
        # 測試完整分段下載
        print("📥 測試完整分段下載 (所有分段)...")
        current_year = datetime.now().year

        if downloader.download_and_import_all_segments(year=current_year):
            print("🎉 完整分段下載和匯入成功！")
            return True
        else:
            print("❌ 完整分段下載失敗")
            return False

    else:
        print("❌ 無效的選擇")
        return False

def test_csv_cleaning():
    """測試CSV清理功能"""
    print("🧹 測試CSV清理功能")
    print("=" * 40)

    downloader = GoodinfoROECSVDownloader()

    # 創建測試CSV檔案
    test_csv_content = """代號,名稱,ROE (%),EPS (元)
2330,台積電,25.5,22.0
2317,鴻海,15.2,8.5
代號,名稱,ROE (%),EPS (元)
2454,聯發科,20.1,35.2
1301,台塑,12.8,4.1
代號,名稱,ROE (%),EPS (元)
2412,中華電,18.5,4.8"""

    test_file = os.path.join(downloader.download_dir, "test_roe_data.csv")

    try:
        # 寫入測試檔案
        with open(test_file, 'w', encoding='utf-8-sig') as f:
            f.write(test_csv_content)

        print(f"📝 創建測試檔案: {test_file}")

        # 清理CSV
        cleaned_file = downloader.clean_csv_data(test_file)

        if cleaned_file:
            print(f"✅ CSV清理成功: {cleaned_file}")

            # 讀取清理後的檔案
            with open(cleaned_file, 'r', encoding='utf-8-sig') as f:
                cleaned_content = f.read()

            print("清理後的內容:")
            print(cleaned_content)

            # 清理測試檔案
            os.remove(test_file)
            os.remove(cleaned_file)

            return True
        else:
            print("❌ CSV清理失敗")
            return False

    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GoodInfo ROE CSV下載器測試")
    print("=" * 50)
    print("1. 測試ROE下載器")
    print("2. 測試CSV清理功能")

    choice = input("請選擇測試項目 (1 或 2): ").strip()

    if choice == "1":
        test_roe_csv_downloader()
    elif choice == "2":
        test_csv_cleaning()
    else:
        print("❌ 無效選擇，執行預設測試...")
        test_roe_csv_downloader()
