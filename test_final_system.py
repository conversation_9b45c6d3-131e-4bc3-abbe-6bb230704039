#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的完整系統
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_final_system():
    """測試修正後的完整系統"""
    
    print("=" * 80)
    print("🧪 測試修正後的完整系統")
    print("=" * 80)
    
    try:
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        # 測試一個已知有資料的日期
        test_date = datetime(2023, 9, 20)  # 週三，應該有資料
        
        print(f"🔍 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 步驟1：測試爬蟲輸出
        print(f"\n📡 步驟1：測試爬蟲輸出")
        df = crawl_price(test_date)
        
        if df is None or df.empty:
            print(f"❌ 爬取失敗或無資料")
            return False
        
        print(f"✅ 成功爬取 {len(df)} 筆資料")
        
        # 檢查欄位名稱
        df_reset = df.reset_index()
        columns = list(df_reset.columns)
        print(f"📋 爬取的欄位: {columns}")
        
        # 檢查是否符合標準格式
        expected_columns = ['stock_id', 'date', 'Volume', 'Transaction', 'TradeValue', 
                           'Open', 'High', 'Low', 'Close', 'stock_name', 'listing_status', 'industry']
        
        missing_columns = []
        extra_columns = []
        
        for col in expected_columns:
            if col not in columns:
                missing_columns.append(col)
        
        for col in columns:
            if col not in expected_columns:
                extra_columns.append(col)
        
        print(f"\n🔍 欄位檢查:")
        if not missing_columns and not extra_columns:
            print(f"   ✅ 欄位完全符合標準格式")
        else:
            if missing_columns:
                print(f"   ❌ 缺少欄位: {missing_columns}")
            if extra_columns:
                print(f"   ⚠️ 額外欄位: {extra_columns}")
        
        # 檢查 0050 資料
        etf_0050 = df_reset[df_reset['stock_id'] == '0050']
        if not etf_0050.empty:
            row = etf_0050.iloc[0]
            print(f"\n📊 0050 爬取資料:")
            print(f"   股票代碼: {row['stock_id']}")
            print(f"   日期: {row['date']}")
            print(f"   股票名稱: {row.get('stock_name', 'N/A')}")
            print(f"   上市狀態: {row.get('listing_status', 'N/A')}")
            print(f"   產業: {row.get('industry', 'N/A')}")
            print(f"   Volume: {row.get('Volume', 'N/A')}")
            print(f"   Close: {row.get('Close', 'N/A')}")
            print(f"   Open: {row.get('Open', 'N/A')}")
        
        # 步驟2：測試存儲功能
        print(f"\n💾 步驟2：測試存儲功能")
        db_file = r'D:\Finlab\history\tables\newprice.db'
        
        # 檢查存儲前的記錄數
        conn = sqlite3.connect(db_file)
        query_before = f"SELECT COUNT(*) as count FROM stock_daily_data WHERE date = '{test_date.strftime('%Y-%m-%d')}'"
        result_before = pd.read_sql_query(query_before, conn)
        count_before = result_before.iloc[0]['count']
        conn.close()
        
        print(f"📊 存儲前該日期記錄數: {count_before}")
        
        # 存儲資料
        success = save_price_to_newprice_database(df, db_file)
        
        if not success:
            print(f"❌ 存儲失敗")
            return False
        
        print(f"✅ 存儲成功")
        
        # 步驟3：驗證存儲結果
        print(f"\n🔍 步驟3：驗證存儲結果")
        
        conn = sqlite3.connect(db_file)
        
        # 檢查存儲後的記錄數
        query_after = f"SELECT COUNT(*) as count FROM stock_daily_data WHERE date = '{test_date.strftime('%Y-%m-%d')}'"
        result_after = pd.read_sql_query(query_after, conn)
        count_after = result_after.iloc[0]['count']
        
        print(f"📊 存儲後該日期記錄數: {count_after}")
        
        if count_after > count_before:
            print(f"✅ 成功新增 {count_after - count_before} 筆記錄")
        elif count_after == count_before and count_before > 0:
            print(f"✅ 資料已存在，更新完成")
        else:
            print(f"⚠️ 記錄數異常")
        
        # 檢查 0050 的存儲結果
        query_0050 = f'''
            SELECT stock_id, date, Volume, [Transaction], TradeValue, [Open], High, Low, [Close], [Change], stock_name, listing_status, industry
            FROM stock_daily_data 
            WHERE stock_id = '0050' AND date = '{test_date.strftime('%Y-%m-%d')}'
        '''
        
        df_0050_stored = pd.read_sql_query(query_0050, conn)
        
        if not df_0050_stored.empty:
            row = df_0050_stored.iloc[0]
            print(f"\n📊 0050 存儲結果:")
            print(f"   股票代碼: {row['stock_id']}")
            print(f"   日期: {row['date']}")
            print(f"   股票名稱: {row['stock_name']}")
            print(f"   上市狀態: {row['listing_status']}")
            print(f"   產業: {row['industry']}")
            print(f"   Volume: {row['Volume']}")
            print(f"   Transaction: {row['Transaction']}")
            print(f"   TradeValue: {row['TradeValue']}")
            print(f"   Open: {row['Open']}")
            print(f"   High: {row['High']}")
            print(f"   Low: {row['Low']}")
            print(f"   Close: {row['Close']}")
            print(f"   Change: {row['Change']}")
        else:
            print(f"❌ 未找到 0050 的存儲資料")
            return False
        
        conn.close()
        
        print(f"\n✅ 完整系統測試成功！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_system()
    
    if success:
        print(f"\n🎉 系統修正完成！")
        print(f"📋 修正總結:")
        print(f"   ✅ 爬蟲輸出標準英文欄位名稱")
        print(f"   ✅ 存儲函數處理標準欄位")
        print(f"   ✅ 資料庫結構與 price.db 完全一致")
        print(f"   ✅ 欄位名稱完全統一")
        print(f"\n🚀 現在可以安全執行:")
        print(f"   python auto_update.py price")
    else:
        print(f"\n❌ 仍有問題需要修正")
