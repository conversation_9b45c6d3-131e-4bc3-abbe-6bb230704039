#!/usr/bin/env python3
"""
測試股票篩選功能
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stock_filter():
    """測試股票篩選功能"""
    try:
        from stock_filter import StockFilter
        
        # 創建篩選器實例
        filter_obj = StockFilter()
        print("✅ 股票篩選器載入成功")
        
        # 測試股票清單
        test_stocks = ['0050', '0051', '0052', '0053', '0054', '0055', '0056', '1101', '2330']
        print(f"📊 測試股票清單: {test_stocks}")
        
        # 執行篩選
        filtered_stocks = filter_obj.filter_stock_list(test_stocks)
        print(f"📊 篩選後股票清單: {filtered_stocks}")
        
        # 檢查0054是否被篩選掉
        if '0054' in test_stocks and '0054' not in filtered_stocks:
            print("✅ 0054 已被正確篩選掉")
        elif '0054' in filtered_stocks:
            print("❌ 0054 沒有被篩選掉！")
        else:
            print("ℹ️ 0054 不在測試清單中")
            
        # 顯示篩選統計
        original_count = len(test_stocks)
        filtered_count = len(filtered_stocks)
        excluded_count = original_count - filtered_count
        
        print(f"📈 篩選統計:")
        print(f"   原始股票數: {original_count}")
        print(f"   篩選後股票數: {filtered_count}")
        print(f"   排除股票數: {excluded_count}")
        
        if excluded_count > 0:
            excluded_stocks = [stock for stock in test_stocks if stock not in filtered_stocks]
            print(f"   排除的股票: {excluded_stocks}")
            
    except ImportError as e:
        print(f"❌ 股票篩選器載入失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stock_filter()
