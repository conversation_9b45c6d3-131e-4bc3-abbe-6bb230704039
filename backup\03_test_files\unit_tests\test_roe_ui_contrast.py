#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試年度ROE資料下載器的UI對比度
"""

import sys
from PyQt6.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget

def test_roe_downloader_ui():
    """測試ROE下載器UI對比度"""
    app = QApplication(sys.argv)
    
    # 創建測試視窗
    test_window = QWidget()
    test_window.setWindowTitle("ROE UI對比度測試")
    test_window.setGeometry(200, 200, 400, 300)
    
    layout = QVBoxLayout(test_window)
    
    # 測試ROE下載器
    def test_downloader():
        try:
            from roe_data_downloader import ROEDataDownloader
            downloader = ROEDataDownloader()
            downloader.show()
            print("✅ ROE下載器界面已開啟")
        except Exception as e:
            print(f"❌ ROE下載器測試失敗: {e}")
    
    # 測試自定義MessageBox
    def test_message_boxes():
        try:
            from roe_data_downloader import CustomMessageBox
            
            # 測試資訊對話框
            CustomMessageBox.information(test_window, "測試資訊", "這是一個資訊對話框測試\n文字應該清晰可讀")
            
            # 測試警告對話框
            CustomMessageBox.warning(test_window, "測試警告", "這是一個警告對話框測試\n文字應該清晰可讀")
            
            # 測試錯誤對話框
            CustomMessageBox.critical(test_window, "測試錯誤", "這是一個錯誤對話框測試\n文字應該清晰可讀")
            
            print("✅ 自定義MessageBox測試完成")
        except Exception as e:
            print(f"❌ MessageBox測試失敗: {e}")
    
    # 添加測試按鈕
    btn1 = QPushButton("🧪 測試ROE下載器界面")
    btn1.clicked.connect(test_downloader)
    btn1.setStyleSheet("""
        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
    """)
    layout.addWidget(btn1)
    
    btn2 = QPushButton("💬 測試對話框對比度")
    btn2.clicked.connect(test_message_boxes)
    btn2.setStyleSheet("""
        QPushButton {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #1976D2;
        }
    """)
    layout.addWidget(btn2)
    
    btn3 = QPushButton("❌ 關閉測試")
    btn3.clicked.connect(test_window.close)
    btn3.setStyleSheet("""
        QPushButton {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d32f2f;
        }
    """)
    layout.addWidget(btn3)
    
    # 設置測試視窗樣式
    test_window.setStyleSheet("""
        QWidget {
            background-color: #f5f5f5;
            color: #333333;
        }
    """)
    
    test_window.show()
    
    print("🚀 ROE UI對比度測試已啟動")
    print("請檢查以下項目：")
    print("1. 主界面文字是否清晰可讀（深色文字在淺色背景上）")
    print("2. 對話框文字是否清晰可讀")
    print("3. 按鈕文字是否清晰可讀")
    print("4. 沒有白底白字或黑底深色字的問題")
    
    return app.exec()

if __name__ == "__main__":
    test_roe_downloader_ui()
