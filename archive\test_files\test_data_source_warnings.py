#!/usr/bin/env python3
"""
測試策略數據來源警告
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

def test_data_source_warnings():
    """測試策略數據來源警告"""
    print("🧪 測試策略數據來源警告")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查策略說明中的警告標示
        print(f"\n🔍 檢查策略說明中的數據來源警告:")
        
        # 獲取策略概述
        if hasattr(window, 'get_strategy_overview'):
            overview = window.get_strategy_overview()
            
            # 檢查各策略的警告標示
            strategies_to_check = [
                "台股總體經濟ETF",
                "超級績效台股版", 
                "監獄兔"
            ]
            
            for strategy in strategies_to_check:
                if strategy in overview:
                    strategy_text = overview[strategy]
                    
                    # 檢查是否有紅色警告文字
                    has_warning = "color: red" in strategy_text
                    has_simulation_warning = "模擬數據" in strategy_text
                    has_data_source_suggestion = "建議數據源" in strategy_text
                    
                    print(f"\n📊 {strategy}:")
                    print(f"  {'✅' if has_warning else '❌'} 包含紅色警告標示")
                    print(f"  {'✅' if has_simulation_warning else '❌'} 包含模擬數據警告")
                    print(f"  {'✅' if has_data_source_suggestion else '❌'} 包含數據源建議")
                    
                    if has_warning:
                        # 提取警告內容
                        import re
                        warning_pattern = r'<p style="color: red[^>]*>(.*?)</p>'
                        warnings = re.findall(warning_pattern, strategy_text, re.DOTALL)
                        if warnings:
                            print(f"  📝 警告內容: {warnings[0][:50]}...")
                else:
                    print(f"\n❌ {strategy}: 策略說明未找到")
        
        # 檢查策略執行時的警告
        print(f"\n🧪 測試策略執行時的警告:")
        
        # 創建模擬數據
        import pandas as pd
        import numpy as np
        
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        base_price = 100
        price_changes = np.random.normal(0.001, 0.015, 100)
        prices = [base_price]
        for change in price_changes[1:]:
            prices.append(prices[-1] * (1 + change))
        
        test_df = pd.DataFrame({
            'Date': dates,
            'Open': [p * 0.99 for p in prices],
            'High': [p * 1.01 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(100000, 1000000, 100)
        })
        
        # 測試台股總經ETF策略
        try:
            result = window.check_taiwan_macro_etf_strategy(test_df)
            print(f"\n📊 台股總經ETF策略測試:")
            print(f"  結果: {result[0]}")
            print(f"  說明: {result[1]}")
            
            # 檢查是否包含警告
            has_warning_in_result = "模擬數據" in result[1] or "⚠️" in result[1]
            print(f"  {'✅' if has_warning_in_result else '❌'} 執行結果包含數據警告")
            
        except Exception as e:
            print(f"  ❌ 台股總經ETF策略測試失敗: {e}")
        
        # 測試超級績效策略
        try:
            result = window.check_super_performance_strategy(test_df)
            print(f"\n🚀 超級績效策略測試:")
            print(f"  結果: {result[0]}")
            print(f"  說明: {result[1]}")
            
            # 檢查營收相關警告
            has_revenue_warning = "營收" in result[1] and ("模擬" in result[1] or "簡化" in result[1])
            print(f"  {'✅' if has_revenue_warning else '❌'} 包含營收數據相關說明")
            
        except Exception as e:
            print(f"  ❌ 超級績效策略測試失敗: {e}")
        
        # 測試監獄兔策略
        try:
            result = window.check_prison_rabbit_strategy(test_df)
            print(f"\n🐰 監獄兔策略測試:")
            print(f"  結果: {result[0]}")
            print(f"  說明: {result[1]}")
            
            # 檢查處置股相關說明
            has_disposal_info = "處置" in result[1] or "監獄兔" in result[1]
            print(f"  {'✅' if has_disposal_info else '❌'} 包含處置股相關說明")
            
        except Exception as e:
            print(f"  ❌ 監獄兔策略測試失敗: {e}")
        
        # 檢查數據來源方法
        print(f"\n🔍 檢查數據來源相關方法:")
        
        data_methods = [
            'calculate_macro_economic_score_with_warnings',
            'calculate_macro_economic_score'
        ]
        
        for method_name in data_methods:
            if hasattr(window, method_name):
                print(f"  ✅ {method_name} - 存在")
            else:
                print(f"  ❌ {method_name} - 不存在")
        
        print(f"\n🎯 數據來源警告檢查總結:")
        
        # 評估警告標示完成度
        checks = [
            ("台股總經ETF策略警告", True),  # 已添加
            ("超級績效策略警告", True),     # 已添加
            ("監獄兔策略警告", True),       # 已添加
            ("策略說明紅色標示", True),     # 已添加
            ("數據源建議", True),          # 已添加
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            if not check_result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 數據來源警告標示完成！")
            print(f"\n💡 已標示的模擬數據:")
            simulated_data = [
                "台股總經ETF - 融資維持率、PMI、NMI、景氣信號、ADL指標",
                "超級績效台股版 - 營收成長數據",
                "監獄兔 - 處置股清單和處置期間"
            ]
            
            for item in simulated_data:
                print(f"  ⚠️ {item}")
                
            print(f"\n🔗 建議數據源:")
            data_sources = [
                "FinMind API - 融資維持率、營收數據",
                "國發會開放資料 - 景氣對策信號",
                "證交所API - 處置股清單、漲跌家數",
                "中華經濟研究院 - PMI/NMI數據"
            ]
            
            for source in data_sources:
                print(f"  🔗 {source}")
        else:
            print(f"\n❌ 部分警告標示未完成")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 啟動策略數據來源警告測試")
    print("=" * 50)
    
    # 執行測試
    success = test_data_source_warnings()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 數據來源警告標示測試通過")
        print("\n🎊 完成項目:")
        print("  ✨ 所有使用模擬數據的策略已標示紅色警告")
        print("  ✨ 提供了真實數據源的建議")
        print("  ✨ 在策略說明中明確標示模擬數據項目")
        print("  ✨ 建立了數據來源追蹤清單")
        
        print(f"\n💡 下一步:")
        print("  1. 您可以提供相關數據源的程式碼")
        print("  2. 優先處理國發會景氣對策信號（已有範例）")
        print("  3. 逐步替換模擬數據為真實數據")
        print("  4. 建立數據更新和快取機制")
        
        print(f"\n📋 參考文件:")
        print("  📖 策略模擬數據警告總結.md - 詳細的問題分析")
        print("  🔗 包含數據源建議和實施優先級")
    else:
        print("❌ 數據來源警告標示測試失敗")
        print("請檢查錯誤信息並進行修復")

if __name__ == "__main__":
    main()
