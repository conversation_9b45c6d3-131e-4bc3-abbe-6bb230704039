#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的月營收抓取測試
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import sqlite3
import os

def test_goodinfo_connection():
    """測試 GoodInfo 連接"""
    try:
        print("🔗 測試 GoodInfo 連接...")
        
        # 設置請求頭
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # 測試訪問台積電月營收頁面
        url = "https://goodinfo.tw/tw/StockInfo/StockSalesMon.asp?STOCK_ID=2330"
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 連接成功，狀態碼: {response.status_code}")
            print(f"📄 頁面大小: {len(response.text)} 字符")
            
            # 檢查是否包含營收相關內容
            if '營收' in response.text or '月增率' in response.text:
                print("✅ 頁面包含營收相關內容")
                return True
            else:
                print("⚠️ 頁面不包含營收內容，可能需要處理JavaScript")
                return False
        else:
            print(f"❌ 連接失敗，狀態碼: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 連接測試失敗: {e}")
        return False

def test_table_parsing():
    """測試表格解析"""
    try:
        print("\n📊 測試表格解析...")
        
        # 創建示例HTML表格
        sample_html = """
        <table>
            <tr><th>年月</th><th>營收</th><th>月增率</th><th>年增率</th></tr>
            <tr><td>2024/01</td><td>1,000,000</td><td>5.2%</td><td>12.3%</td></tr>
            <tr><td>2024/02</td><td>1,100,000</td><td>10.0%</td><td>15.5%</td></tr>
        </table>
        """
        
        # 使用pandas解析
        tables = pd.read_html(sample_html)
        
        if tables:
            print(f"✅ 成功解析 {len(tables)} 個表格")
            print("📋 表格內容:")
            print(tables[0])
            return True
        else:
            print("❌ 表格解析失敗")
            return False
            
    except Exception as e:
        print(f"❌ 表格解析測試失敗: {e}")
        return False

def test_database_creation():
    """測試數據庫創建"""
    try:
        print("\n🗄️ 測試數據庫創建...")
        
        # 確保目錄存在
        db_path = "db/test_monthly_revenue.db"
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 創建數據庫
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 創建表格
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monthly_revenue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_id TEXT NOT NULL,
                stock_name TEXT,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                revenue REAL,
                revenue_mom REAL,
                revenue_yoy REAL,
                crawl_date TEXT,
                UNIQUE(stock_id, year, month)
            )
        ''')
        
        # 插入測試數據
        test_data = [
            ('2330', '台積電', 2024, 1, 1000000, 5.2, 12.3, '2024-01-15'),
            ('2330', '台積電', 2024, 2, 1100000, 10.0, 15.5, '2024-02-15')
        ]
        
        cursor.executemany('''
            INSERT OR REPLACE INTO monthly_revenue 
            (stock_id, stock_name, year, month, revenue, revenue_mom, revenue_yoy, crawl_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_data)
        
        conn.commit()
        
        # 驗證數據
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
        count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ 數據庫創建成功，插入 {count} 筆測試數據")
        return True
        
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        return False

def create_monthly_revenue_database():
    """創建正式的月營收數據庫"""
    try:
        print("\n🏗️ 創建正式月營收數據庫...")
        
        db_path = "db/monthly_revenue.db"
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 創建完整的月營收表格
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monthly_revenue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_id TEXT NOT NULL,
                stock_name TEXT,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                revenue REAL,                    -- 當月營收(千元)
                revenue_mom REAL,               -- 月增率(%)
                revenue_yoy REAL,               -- 年增率(%)
                cumulative_revenue REAL,        -- 累計營收(千元)
                cumulative_yoy REAL,            -- 累計年增率(%)
                data_source TEXT DEFAULT 'goodinfo',
                crawl_date TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_id, year, month)
            )
        ''')
        
        # 創建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_year_month ON monthly_revenue(stock_id, year, month)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_crawl_date ON monthly_revenue(crawl_date)')
        
        conn.commit()
        conn.close()
        
        print("✅ 正式月營收數據庫創建完成")
        print(f"📍 數據庫位置: {os.path.abspath(db_path)}")
        return True
        
    except Exception as e:
        print(f"❌ 正式數據庫創建失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 月營收抓取功能測試")
    print("=" * 60)
    
    # 測試1: 網絡連接
    connection_ok = test_goodinfo_connection()
    
    # 測試2: 表格解析
    parsing_ok = test_table_parsing()
    
    # 測試3: 數據庫操作
    database_ok = test_database_creation()
    
    # 測試4: 創建正式數據庫
    production_db_ok = create_monthly_revenue_database()
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    print(f"🔗 網絡連接: {'✅ 通過' if connection_ok else '❌ 失敗'}")
    print(f"📊 表格解析: {'✅ 通過' if parsing_ok else '❌ 失敗'}")
    print(f"🗄️ 數據庫操作: {'✅ 通過' if database_ok else '❌ 失敗'}")
    print(f"🏗️ 正式數據庫: {'✅ 通過' if production_db_ok else '❌ 失敗'}")
    
    all_passed = all([connection_ok, parsing_ok, database_ok, production_db_ok])
    
    if all_passed:
        print("\n🎉 所有測試通過！可以進行下一步開發。")
        print("\n📋 下一步建議:")
        print("1. 完善 GoodInfo 頁面解析邏輯")
        print("2. 實現批量股票數據抓取")
        print("3. 整合到現有策略系統")
    else:
        print("\n⚠️ 部分測試失敗，需要修復後再繼續。")
    
    return all_passed

if __name__ == "__main__":
    main()
