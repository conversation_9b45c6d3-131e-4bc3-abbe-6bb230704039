2025-07-29 19:00:07,768 - INFO - ✅ yfinance模組載入成功，開盤區間突破策略可用
2025-07-29 19:00:08,011 - INFO - ✅ 開盤前監控模組載入成功
2025-07-29 19:00:08,140 - INFO - ✅ twstock 台股數據模組載入成功
2025-07-29 19:00:08,142 - INFO - ✅ 智能交易策略模組載入成功
2025-07-29 19:00:08,146 - INFO - ✅ 所有策略模組載入成功
2025-07-29 19:00:08,147 - INFO - ✅ 監控模組載入成功
2025-07-29 19:00:08,148 - INFO - ✅ 圖表組件模組載入成功
2025-07-29 19:00:08,173 - INFO - ✅ 策略配置模組載入成功
2025-07-29 19:00:08,178 - INFO - ✅ 對話框模組載入成功
2025-07-29 19:00:08,179 - INFO - ============================================================
2025-07-29 19:00:08,179 - INFO - 🚀 台股智能選股系統啟動
2025-07-29 19:00:08,179 - INFO - 📅 啟動時間: 2025-07-29 19:00:08
2025-07-29 19:00:08,179 - INFO - 📁 日誌文件: D:\Finlab\backup\O3mh_strategy2AA\logs\app_20250729.log
2025-07-29 19:00:08,179 - INFO - ============================================================
2025-07-29 19:00:08,202 - INFO - ✅ 載入監控股票: 10 支
2025-07-29 19:00:08,202 - INFO - ✅ 統一監控管理器初始化完成
2025-07-29 19:00:08,203 - INFO - ✅ 增強版市場掃描器初始化完成
2025-07-29 19:00:08,204 - INFO - ✅ 增強版市場掃描器初始化完成
2025-07-29 19:00:08,204 - INFO - ✅ 增強版市場掃描系統初始化完成
2025-07-29 19:00:08,205 - INFO - ✅ 增強版yfinance獲取器初始化完成
2025-07-29 19:00:08,205 - INFO - ✅ 增強版yfinance獲取器初始化完成
2025-07-29 19:00:08,205 - INFO - ✅ 增強版yfinance獲取器初始化完成
2025-07-29 19:00:08,206 - INFO - ✅ 優化市場掃描器初始化完成
2025-07-29 19:00:08,206 - INFO - ✅ 優化市場掃描器初始化完成
2025-07-29 19:00:08,206 - INFO - ✅ 優化市場掃描系統初始化完成（備用）
2025-07-29 19:00:08,207 - DEBUG - ⚠️ twse-crawler 模組不可用，將使用備用數據源
2025-07-29 19:00:08,207 - INFO - ✅ 真實數據開盤前監控系統初始化完成
2025-07-29 19:00:08,208 - INFO - ✅ 真實數據獲取器載入成功
2025-07-29 19:00:08,208 - INFO - ✅ 真實數據獲取器初始化完成（已啟用反爬蟲機制）
2025-07-29 19:00:08,208 - INFO - ✅ 真實數據獲取器初始化成功
2025-07-29 19:00:08,208 - INFO - ✅ 使用安全市場掃描器（避免QPainter崩潰問題）
2025-07-29 19:00:08,208 - INFO - ✅ 開盤前監控器作為備用
2025-07-29 19:00:08,210 - DEBUG - 股票篩選器快取初始化完成
2025-07-29 19:00:08,211 - INFO - ✅ 股票篩選器初始化完成
2025-07-29 19:00:08,211 - DEBUG - 測試0054篩選結果: False (False表示被正確篩選掉)
2025-07-29 19:00:08,859 - INFO - ✅ 智能Yahoo獲取器已載入到盤中數據獲取器
2025-07-29 19:00:08,859 - INFO - ✅ 智能Yahoo獲取器已初始化（盤中）
2025-07-29 19:00:08,861 - INFO - 📊 Twelve Data API 初始化完成
2025-07-29 19:00:08,862 - INFO - 📊 Twelve Data API 初始化完成
2025-07-29 19:00:08,862 - INFO - ✅ Twelve Data API 已初始化
2025-07-29 19:00:08,873 - INFO - ✅ 智能掃描功能已就緒
2025-07-29 19:00:08,876 - INFO - 嘗試連接price數據庫: D:/Finlab/history/tables\price.db
2025-07-29 19:00:08,877 - INFO - ✅ 成功連接price數據庫: D:/Finlab/history/tables\price.db
2025-07-29 19:00:08,877 - INFO - 檢測到的欄位: ['stock_id', 'stock_name', 'listing_status', 'industry', 'Volume', 'Transaction', 'TradeValue', 'Open', 'High', 'Low', 'Close', 'Change', 'date']
2025-07-29 19:00:08,877 - INFO - ✅ 價格數據表結構驗證通過
2025-07-29 19:00:08,878 - INFO - ✅ 成功連接PE數據庫: D:/Finlab/history/tables\pe_data.db
2025-07-29 19:00:08,878 - INFO - ✅ 找到PE數據表: pe_data
2025-07-29 19:00:08,878 - INFO - 表 stock_daily_data 的欄位: ['stock_id', 'stock_name', 'listing_status', 'industry', 'Volume', 'Transaction', 'TradeValue', 'Open', 'High', 'Low', 'Close', 'Change', 'date']
2025-07-29 19:00:08,879 - INFO - 包含股票名稱: True, 上市狀態: True, 行業: True
2025-07-29 19:00:08,879 - INFO - 執行直接查詢（表中已有完整資料）: 
                        SELECT stock_id,
                               stock_name,
                               listing_status,
                               industry
                        FROM (
                            SELECT stock_id,
                                   COALESCE(stock_name, '') as stock_name,
                                   COALESCE(listing_status, '') as listing_status,
                                   COALESCE(industry, '') as industry,
                                   ROW_NUMBER() OVER (
                                       PARTITION BY stock_id
                                       ORDER BY
                                           CASE WHEN industry != '' THEN 1 ELSE 2 END,
                                           CASE WHEN stock_name != '' THEN 1 ELSE 2 END,
                                           CASE WHEN listing_status != '' THEN 1 ELSE 2 END
                                   ) as rn
                            FROM stock_daily_data
                            WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]*'
                              AND LENGTH(stock_id) BETWEEN 4 AND 6
                        ) ranked
                        WHERE rn = 1
                        ORDER BY
                            CASE
                                WHEN LENGTH(stock_id) = 4 THEN 1
                                WHEN LENGTH(stock_id) = 5 THEN 2
                                WHEN LENGTH(stock_id) = 6 THEN 3
                            END,
                            CAST(stock_id AS INTEGER)
                    
2025-07-29 19:00:21,636 - INFO - 查詢到 2407 支股票
2025-07-29 19:00:21,641 - DEBUG - 📊 股票篩選完成：原始 2407 支，保留 2137 支，排除 270 支
2025-07-29 19:00:21,642 - INFO - 📊 股票清單篩選：保留 2137 支，排除 270 支
2025-07-29 19:00:21,642 - DEBUG - ✅ 0054已被成功篩選掉
2025-07-29 19:00:21,708 - DEBUG - 補充股票名稱: 00925 -> ETF
2025-07-29 19:00:21,714 - DEBUG - 補充股票名稱: 009811 -> ETF
2025-07-29 19:00:21,716 - INFO - 🎯 智能策略管理器初始化完成
2025-07-29 19:00:21,716 - INFO - 載入策略配置（包含實用策略）
2025-07-29 19:00:21,717 - INFO - 已刪除舊策略文件
2025-07-29 19:00:21,717 - INFO - 已保存新策略文件
2025-07-29 19:00:21,718 - INFO - 🔄 策略已切換到: '▼ 📈 經典策略'
2025-07-29 19:00:21,718 - INFO - ℹ️ 沒有當前股票，等待用戶選擇股票
2025-07-29 19:00:21,719 - INFO - 🔄 策略已切換到: '阿水一式'
2025-07-29 19:00:21,719 - INFO - ℹ️ 沒有當前股票，等待用戶選擇股票
2025-07-29 19:00:21,719 - INFO - 🎯 策略載入完成，共 33 個策略
2025-07-29 19:00:21,721 - INFO - ✅ 策略交集分析器初始化完成
2025-07-29 19:00:21,722 - INFO - PE數據欄位映射: {'stock_id': 'stock_id', 'date': 'date', 'pe_ratio': 'pe_ratio', 'dividend_yield': 'dividend_yield', 'pb_ratio': 'pb_ratio', 'market_cap': 'market_cap'}
2025-07-29 19:00:22,404 - INFO - ✅ PE數據提供器初始化成功
2025-07-29 19:00:22,405 - INFO - 📊 PE數據股票數量: 1993
2025-07-29 19:00:22,405 - INFO - 📅 PE數據日期範圍: 2018-01-02 到 2025-07-25
2025-07-29 19:00:22,407 - INFO - ✅ 緩存數據庫初始化完成
2025-07-29 19:00:22,408 - INFO - ✅ 配置載入完成
2025-07-29 19:00:22,409 - INFO - ✅ FinMind數據提供器初始化成功
2025-07-29 19:00:22,409 - INFO - 📊 API配額: 0/600
2025-07-29 19:00:22,409 - INFO - 載入股票資訊檔案: D:\Finlab\history\holidays\stock_info.xlsx
2025-07-29 19:00:22,760 - INFO - ✅ 載入了 2035 支股票的基本資料
2025-07-29 19:00:22,760 - INFO - GUI 初始化完成
