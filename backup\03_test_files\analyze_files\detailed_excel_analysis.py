#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
詳細分析Excel文件結構
找出正確的數據起始行和格式
"""

import pandas as pd
import os
import glob
import re

def detailed_excel_analysis():
    """詳細分析Excel文件"""
    print("🔍 詳細Excel文件結構分析")
    print("=" * 50)
    
    # 尋找最新下載的Excel文件
    download_dir = "D:/Finlab/history/tables/monthly_revenue"
    excel_files = glob.glob(os.path.join(download_dir, "*.xls*"))
    
    if not excel_files:
        print("❌ 未找到下載的Excel文件")
        return False
    
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📊 分析文件: {latest_file}")
    
    try:
        # 讀取Excel文件
        df = pd.read_excel(latest_file, header=None)
        print(f"📋 文件包含 {len(df)} 行, {len(df.columns)} 列")
        
        # 分析前50行，尋找數據模式
        print("\n🔍 分析前50行數據...")
        
        potential_data_rows = []
        
        for index in range(min(50, len(df))):
            row = df.iloc[index]
            first_cell = str(row.iloc[0]).strip()
            
            print(f"行{index:2d}: {first_cell[:30]:<30} | {str(row.iloc[1])[:15]:<15} | {str(row.iloc[2])[:15]:<15}")
            
            # 檢查各種可能的日期格式
            date_patterns = [
                r'^\d{4}/\d{1,2}$',      # 2025/06
                r'^\d{4}\.\d{1,2}$',     # 2025.06
                r'^[A-Za-z]{3}-\d{2}$',  # Jun-25
                r'^\d{4}-\d{1,2}$',      # 2025-06
            ]
            
            for pattern in date_patterns:
                if re.match(pattern, first_cell):
                    potential_data_rows.append((index, first_cell, pattern))
                    break
        
        if potential_data_rows:
            print(f"\n✅ 找到 {len(potential_data_rows)} 個潛在數據行:")
            for row_idx, date_str, pattern in potential_data_rows[:10]:  # 顯示前10個
                print(f"   行{row_idx}: {date_str} (格式: {pattern})")
            
            # 選擇第一個作為數據起始行
            data_start_row = potential_data_rows[0][0]
            print(f"\n📍 建議數據起始行: {data_start_row}")
            
            # 分析該行及後續幾行的完整數據
            print(f"\n📊 分析從行{data_start_row}開始的數據結構:")
            
            for i in range(5):  # 分析5行
                if data_start_row + i < len(df):
                    row = df.iloc[data_start_row + i]
                    print(f"\n行{data_start_row + i}:")
                    for j, cell in enumerate(row[:12]):  # 前12列
                        print(f"   列{j}: '{cell}'")
            
            # 嘗試解析第一行數據
            print(f"\n🧪 嘗試解析第一行數據...")
            first_data_row = df.iloc[data_start_row]
            
            # 解析月份
            month_str = str(first_data_row.iloc[0]).strip()
            print(f"月份字符串: '{month_str}'")
            
            # 嘗試解析年月
            if '/' in month_str:
                parts = month_str.split('/')
                if len(parts) == 2:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        print(f"✅ 解析結果: {year}年{month}月")
                    except ValueError:
                        print("❌ 年月解析失敗")
            
            # 解析數值
            print("\n數值解析測試:")
            for i in range(1, min(12, len(first_data_row))):
                cell_value = first_data_row.iloc[i]
                cleaned = str(cell_value).replace(',', '').replace('+', '').strip()
                
                try:
                    if cleaned and cleaned != 'nan':
                        float_value = float(cleaned)
                        print(f"   列{i}: '{cell_value}' -> {float_value}")
                    else:
                        print(f"   列{i}: '{cell_value}' -> None")
                except ValueError:
                    print(f"   列{i}: '{cell_value}' -> 無法轉換")
            
            return data_start_row
        else:
            print("❌ 未找到任何潛在的數據行")
            
            # 搜索包含數字的行
            print("\n🔍 搜索包含數字的行...")
            for index in range(min(20, len(df))):
                row = df.iloc[index]
                numeric_count = 0
                
                for cell in row:
                    cell_str = str(cell).replace(',', '').replace('+', '').replace('-', '').strip()
                    if cell_str.replace('.', '').isdigit():
                        numeric_count += 1
                
                if numeric_count >= 5:  # 至少5個數字列
                    print(f"   行{index}: 包含{numeric_count}個數字")
                    print(f"     內容: {[str(x)[:10] for x in row[:8]]}")
            
            return None
            
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        return None

def create_fixed_parser_v2(data_start_row):
    """基於分析結果創建修復的解析器v2"""
    if data_start_row is None:
        print("❌ 無法創建解析器，未找到數據起始行")
        return
    
    print(f"\n🔧 基於行{data_start_row}創建修復的解析器...")
    
    # 創建修復代碼
    fix_code = f'''
def find_data_start_row_fixed(self, df):
    """修復的數據起始行檢測"""
    # 基於分析結果，直接返回找到的起始行
    return {data_start_row}
'''
    
    print("📋 修復建議:")
    print(fix_code)
    
    print("\n💡 或者可以使用更靈活的檢測邏輯:")
    print('''
def find_data_start_row_flexible(self, df):
    """靈活的數據起始行檢測"""
    import re
    
    for index, row in df.iterrows():
        first_cell = str(row.iloc[0]).strip()
        
        # 檢查多種日期格式
        date_patterns = [
            r'^\d{4}/\d{1,2}$',      # 2025/06
            r'^\d{4}\.\d{1,2}$',     # 2025.06
            r'^[A-Za-z]{3}-\d{2}$',  # Jun-25
        ]
        
        for pattern in date_patterns:
            if re.match(pattern, first_cell):
                return index
    
    return None
''')

def main():
    """主函數"""
    data_start_row = detailed_excel_analysis()
    
    if data_start_row is not None:
        create_fixed_parser_v2(data_start_row)
        print(f"\n🎉 分析完成！建議的數據起始行: {data_start_row}")
    else:
        print("\n❌ 分析失敗，無法確定數據結構")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
