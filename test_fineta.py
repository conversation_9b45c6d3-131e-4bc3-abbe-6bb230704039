#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Fineta 財務報表爬蟲
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加 Fineta 路徑
fineta_path = os.path.join(os.getcwd(), 'Fineta')
if fineta_path not in sys.path:
    sys.path.insert(0, fineta_path)

def test_fineta_import():
    """測試 Fineta 模組導入"""
    
    print("=" * 80)
    print("📦 測試 Fineta 模組導入")
    print("=" * 80)
    
    try:
        from stock import Stock, Portfolio
        print("✅ 成功導入 Stock, Portfolio")
        
        from crawler.financial_report import FinancialReportScraper
        print("✅ 成功導入 FinancialReportScraper")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False

def test_basic_functionality():
    """測試基本功能"""
    
    print(f"\n" + "=" * 80)
    print("🧪 測試基本功能")
    print("=" * 80)
    
    try:
        from stock import Stock, Portfolio
        from crawler.financial_report import FinancialReportScraper
        
        # 創建股票和投資組合
        print("📊 創建測試投資組合...")
        stock = Stock("2330")  # 台積電
        portfolio = Portfolio(stock)
        print(f"✅ 投資組合創建成功: {portfolio.get_all_stock_ids()}")
        
        # 創建爬蟲 (測試較短的時間範圍)
        print("🔍 創建財務報表爬蟲...")
        scraper = FinancialReportScraper(
            portfolio=portfolio,
            start_date="2024-01-01",
            end_date="2024-03-31"  # 只測試一季
        )
        print("✅ 財務報表爬蟲創建成功")
        
        return scraper
        
    except Exception as e:
        print(f"❌ 基本功能測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_financial_statements(scraper):
    """測試財務報表爬取"""
    
    print(f"\n" + "=" * 80)
    print("📊 測試財務報表爬取")
    print("=" * 80)
    
    if not scraper:
        print("❌ 爬蟲物件無效，跳過測試")
        return
    
    # 測試三大財務報表
    statements = ["資產負債表", "綜合損益表", "現金流量表"]
    results = {}
    
    for statement_type in statements:
        print(f"\n🔍 測試 {statement_type}...")
        
        try:
            # 設定較短的超時時間
            data = scraper.get_portfolio_financial_statements(statement_type)
            
            if data and len(data) > 0:
                print(f"✅ {statement_type} 爬取成功")
                
                for stock_id, df in data.items():
                    print(f"   📊 {stock_id}: {df.shape[0]} 行 x {df.shape[1]} 列")
                    
                    # 顯示部分資料
                    if not df.empty:
                        print(f"   📋 欄位: {list(df.columns)}")
                        print(f"   📈 前3個項目:")
                        for i, (index, row) in enumerate(df.head(3).iterrows()):
                            print(f"      {i+1}. {index}: {dict(row)}")
                
                results[statement_type] = data
            else:
                print(f"⚠️ {statement_type} 無資料")
                results[statement_type] = None
                
        except Exception as e:
            print(f"❌ {statement_type} 爬取失敗: {e}")
            results[statement_type] = None
    
    return results

def test_data_quality(results):
    """測試資料品質"""
    
    print(f"\n" + "=" * 80)
    print("📈 測試資料品質")
    print("=" * 80)
    
    if not results:
        print("❌ 無測試結果，跳過品質檢查")
        return
    
    for statement_type, data in results.items():
        print(f"\n📊 {statement_type} 品質檢查:")
        
        if data:
            for stock_id, df in data.items():
                if not df.empty:
                    print(f"   ✅ {stock_id}:")
                    print(f"      📊 資料形狀: {df.shape}")
                    print(f"      📋 欄位數: {len(df.columns)}")
                    print(f"      📈 非空值比例: {(df.notna().sum().sum() / df.size * 100):.1f}%")
                    
                    # 檢查是否有數值資料
                    numeric_cols = df.select_dtypes(include=['number']).columns
                    print(f"      🔢 數值欄位: {len(numeric_cols)}")
                else:
                    print(f"   ⚠️ {stock_id}: 空資料")
        else:
            print(f"   ❌ 無資料")

def compare_with_current_system():
    """與現有系統比較"""
    
    print(f"\n" + "=" * 80)
    print("🔍 與現有系統比較")
    print("=" * 80)
    
    # 檢查現有系統的資料
    current_financial_db = r'D:\Finlab\history\tables\financial_data.db'
    current_cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    print("📊 現有系統狀況:")
    
    if os.path.exists(current_financial_db):
        import sqlite3
        conn = sqlite3.connect(current_financial_db)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        count = cursor.fetchone()[0]
        print(f"   ✅ 統一財務資料: {count:,} 筆")
        conn.close()
    else:
        print(f"   ❌ 統一財務資料: 不存在")
    
    if os.path.exists(current_cash_flows_db):
        import sqlite3
        conn = sqlite3.connect(current_cash_flows_db)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        count = cursor.fetchone()[0]
        cursor.execute("SELECT MAX(date) FROM cash_flows")
        latest_date = cursor.fetchone()[0]
        print(f"   ⚠️ 現金流量表: {count:,} 筆 (最新: {latest_date})")
        conn.close()
    else:
        print(f"   ❌ 現金流量表: 不存在")

def provide_recommendation(results):
    """提供建議"""
    
    print(f"\n" + "=" * 80)
    print("💡 測試結果與建議")
    print("=" * 80)
    
    # 統計成功率
    total_tests = len(results) if results else 0
    successful_tests = sum(1 for data in results.values() if data) if results else 0
    
    print(f"📊 測試統計:")
    print(f"   總測試項目: {total_tests}")
    print(f"   成功項目: {successful_tests}")
    print(f"   成功率: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "   成功率: 0%")
    
    print(f"\n🎯 建議:")
    
    if successful_tests >= 2:
        print("✅ **Fineta 表現良好，建議整合使用**")
        print("   1. 可以補充現有系統缺失的現金流量表")
        print("   2. 提供完整的三大財務報表")
        print("   3. 建議作為現有系統的補充")
        
        print(f"\n🚀 整合方案:")
        print("   - 保留現有 TWSE OpenAPI (損益表+資產負債表)")
        print("   - 使用 Fineta 補充現金流量表")
        print("   - 建立混合架構")
        
    elif successful_tests == 1:
        print("⚠️ **Fineta 部分可用，謹慎使用**")
        print("   1. 只有部分功能正常")
        print("   2. 可以作為特定用途的補充")
        print("   3. 需要進一步測試和優化")
        
    else:
        print("❌ **Fineta 目前不可用**")
        print("   1. 可能是網站結構變更")
        print("   2. 或是反爬蟲機制")
        print("   3. 建議繼續使用現有系統")
        print("   4. 或尋找其他替代方案")

def main():
    """主函數"""
    
    print("🧪 Fineta 財務報表爬蟲測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: 模組導入
    import_success = test_fineta_import()
    
    if not import_success:
        print("\n❌ 模組導入失敗，無法繼續測試")
        return
    
    # 測試2: 基本功能
    scraper = test_basic_functionality()
    
    # 測試3: 財務報表爬取
    results = test_financial_statements(scraper)
    
    # 測試4: 資料品質
    test_data_quality(results)
    
    # 測試5: 與現有系統比較
    compare_with_current_system()
    
    # 測試6: 提供建議
    provide_recommendation(results)
    
    print(f"\n" + "=" * 80)
    print("🎉 Fineta 測試完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
