#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試右鍵選單功能
"""

import sys
import os
from datetime import datetime, date

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_context_menu():
    """調試右鍵選單功能"""
    print("=" * 60)
    print("🐛 調試月營收排行榜右鍵選單")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QTableWidgetItem
        from PyQt6.QtCore import Qt
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查右鍵選單設置
        print(f"\n🔍 檢查右鍵選單設置:")
        policy = gui.result_table.contextMenuPolicy()
        print(f"  右鍵政策: {policy}")
        print(f"  是否為CustomContextMenu: {policy == Qt.ContextMenuPolicy.CustomContextMenu}")
        
        # 檢查信號連接
        receivers = gui.result_table.receivers(gui.result_table.customContextMenuRequested)
        print(f"  信號接收器數量: {receivers}")
        
        # 模擬月營收排行榜資料
        print(f"\n📊 設置月營收排行榜測試資料:")
        gui.result_table.setColumnCount(13)
        gui.result_table.setRowCount(3)
        gui.result_table.setHorizontalHeaderLabels([
            "排名", "股票代碼", "股票名稱", "西元年月", "當月營收(千元)",
            "上個月營收(千元)", "去年同月營收(千元)", "YoY%", "MoM%",
            "殖利率(%)", "本益比", "股價淨值比", "EPS"
        ])
        
        # 填入測試資料
        test_data = [
            ["1", "2330", "台積電", "202507", "120,000,000", "115,000,000", "100,000,000", 
             "+20.00%", "*****%", "3.25", "15.80", "2.45", "36.71"],
            ["2", "2317", "鴻海", "202507", "95,000,000", "92,000,000", "88,000,000", 
             "*****%", "*****%", "4.50", "12.50", "1.85", "9.60"],
            ["3", "2454", "聯發科", "202507", "85,000,000", "80,000,000", "75,000,000", 
             "+13.33%", "*****%", "2.80", "18.20", "3.20", "46.70"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                gui.result_table.setItem(row, col, item)
        
        print("✅ 測試資料填入完成")
        
        # 測試月營收排行榜檢測
        print(f"\n🔍 測試月營收排行榜檢測:")
        is_monthly = gui.is_monthly_revenue_ranking()
        print(f"  檢測結果: {'✅ 是月營收排行榜' if is_monthly else '❌ 不是月營收排行榜'}")
        
        # 檢查表格狀態
        print(f"\n📋 表格狀態:")
        print(f"  欄位數量: {gui.result_table.columnCount()}")
        print(f"  行數: {gui.result_table.rowCount()}")
        
        # 檢查第一個欄位標題
        header_item = gui.result_table.horizontalHeaderItem(0)
        if header_item:
            print(f"  第一欄標題: '{header_item.text()}'")
        else:
            print(f"  第一欄標題: None")
        
        # 測試股票資料提取
        print(f"\n📊 測試股票資料提取:")
        stock_data = gui.get_monthly_revenue_stock_data(0)
        if stock_data:
            print(f"  ✅ 成功提取第一行資料:")
            print(f"    股票: {stock_data['股票代碼']} {stock_data['股票名稱']}")
            print(f"    排名: {stock_data['排名']}")
        else:
            print(f"  ❌ 無法提取股票資料")
        
        # 手動測試右鍵選單函數
        print(f"\n🖱️ 手動測試右鍵選單函數:")
        try:
            from PyQt6.QtCore import QPoint
            # 模擬右鍵點擊位置
            position = QPoint(50, 50)
            
            # 直接調用右鍵選單函數
            print("  正在調用 show_stock_context_menu...")
            gui.show_stock_context_menu(position)
            print("  ✅ 右鍵選單函數調用成功")
            
        except Exception as e:
            print(f"  ❌ 右鍵選單函數調用失敗: {e}")
            import traceback
            traceback.print_exc()
        
        # 顯示GUI（可選）
        print(f"\n🖥️ 顯示GUI進行手動測試...")
        print("  請在表格中右鍵點擊股票，檢查是否出現「月營收綜合評估」選項")
        
        gui.show()
        
        # 不執行事件循環，避免阻塞
        # app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 開始調試月營收排行榜右鍵選單...")
    
    success = debug_context_menu()
    
    if success:
        print("\n🎉 調試完成！")
        print("\n📋 檢查清單:")
        print("  ✅ 主程式載入")
        print("  ✅ 右鍵選單設置")
        print("  ✅ 測試資料填入")
        print("  ✅ 月營收排行榜檢測")
        print("  ✅ 股票資料提取")
        print("  ✅ 右鍵選單函數調用")
        print("\n🔧 如果右鍵選單仍未顯示，請檢查:")
        print("  1. 表格是否有資料")
        print("  2. 是否正確識別為月營收排行榜")
        print("  3. 右鍵點擊位置是否在有效的表格項目上")
    else:
        print("\n❌ 調試失敗，請檢查錯誤訊息。")
