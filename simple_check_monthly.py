#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單檢查 monthly_report.pkl 的更新結果
"""

import pandas as pd
import datetime
import os

def simple_check():
    """簡單檢查 monthly_report.pkl"""
    file_path = "history/tables/monthly_report.pkl"
    
    print(f"🔍 檢查 monthly_report.pkl")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"📁 檔案資訊:")
        print(f"   大小: {file_size:.1f} MB")
        print(f"   修改時間: {file_mtime}")
        
        # 讀取資料
        print(f"\n📖 讀取資料...")
        df = pd.read_pickle(file_path)
        
        print(f"✅ 讀取成功:")
        print(f"   總筆數: {len(df):,}")
        print(f"   索引名稱: {df.index.names}")
        print(f"   欄位數: {len(df.columns)}")
        print(f"   欄位: {list(df.columns)}")
        
        # 檢查日期 (安全方式)
        print(f"\n📅 日期檢查:")
        try:
            dates = df.index.get_level_values('date')
            print(f"   日期數量: {len(dates.unique())}")
            
            # 安全地獲取日期範圍
            date_list = sorted(dates.unique())
            if len(date_list) > 0:
                print(f"   最早日期: {date_list[0]}")
                print(f"   最新日期: {date_list[-1]}")
                
                # 檢查最近幾個月
                print(f"\n📊 最近幾個月的資料:")
                for date in date_list[-5:]:  # 最近5個月
                    count = len(df[df.index.get_level_values('date') == date])
                    print(f"   {date}: {count:,} 筆")
            
        except Exception as e:
            print(f"   ⚠️ 日期檢查失敗: {str(e)}")
        
        # 檢查股票
        print(f"\n📈 股票檢查:")
        try:
            stocks = df.index.get_level_values('stock_id')
            unique_stocks = stocks.unique()
            print(f"   股票數量: {len(unique_stocks)}")
            print(f"   前5檔股票: {list(unique_stocks[:5])}")
        except Exception as e:
            print(f"   ⚠️ 股票檢查失敗: {str(e)}")
        
        # 檢查營收欄位
        print(f"\n💰 營收檢查:")
        try:
            if '當月營收' in df.columns:
                revenue_data = df['當月營收'].dropna()
                print(f"   有效營收筆數: {len(revenue_data):,}")
                print(f"   營收範圍: {revenue_data.min():,.0f} ~ {revenue_data.max():,.0f} 千元")
                print(f"   平均營收: {revenue_data.mean():,.0f} 千元")
            else:
                print(f"   ⚠️ 找不到 '當月營收' 欄位")
        except Exception as e:
            print(f"   ⚠️ 營收檢查失敗: {str(e)}")
        
        print(f"\n🎉 檢查完成！")
        
        # 判斷更新狀態
        if file_mtime.date() == datetime.date.today():
            print(f"✅ 檔案今天有更新")
        else:
            days_old = (datetime.date.today() - file_mtime.date()).days
            print(f"⚠️ 檔案已 {days_old} 天未更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

if __name__ == "__main__":
    simple_check()
