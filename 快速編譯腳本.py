#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速編譯腳本 - 簡化版
專門用於編譯 O3mh_gui_v21_optimized.py
"""

import os
import sys
import subprocess

def install_pyinstaller():
    """安裝 PyInstaller"""
    print("📦 安裝 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ PyInstaller 安裝成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller 安裝失敗")
        return False

def quick_compile():
    """快速編譯"""
    print("🚀 開始快速編譯...")
    
    # 檢查主程式檔案
    if not os.path.exists('O3mh_gui_v21_optimized.py'):
        print("❌ 找不到主程式檔案")
        return False
    
    # 嘗試安裝 PyInstaller
    try:
        import PyInstaller
    except ImportError:
        if not install_pyinstaller():
            return False
    
    # 編譯命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 單一執行檔
        '--windowed',  # 無控制台視窗
        '--name=台股智能選股系統',
        '--add-data=stock_name_mapping.py;.',  # 包含股票名稱映射
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=sqlite3',
        '--hidden-import=pyqtgraph',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=yaml',
        '--hidden-import=json',
        '--hidden-import=logging',
        '--hidden-import=threading',
        '--hidden-import=datetime',
        '--hidden-import=concurrent.futures',
        'O3mh_gui_v21_optimized.py'
    ]
    
    print("執行編譯命令...")
    print(f"命令: {' '.join(cmd[:5])} ... (省略部分參數)")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            print("📁 執行檔位置: dist/台股智能選股系統.exe")
            return True
        else:
            print("❌ 編譯失敗")
            print("錯誤訊息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def main():
    """主函數"""
    print("⚡ 台股智能選股系統 - 快速編譯工具")
    print("=" * 50)
    
    if quick_compile():
        print("\n🎉 編譯完成！")
        print("💡 可以在 dist/ 目錄找到執行檔")
    else:
        print("\n❌ 編譯失敗")
        print("💡 請檢查錯誤訊息並重試")

if __name__ == "__main__":
    main()
