#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試股價和EPS計算修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_stock_price_eps_calculation():
    """測試股價和EPS計算修復"""
    print("🧪 測試股價和EPS計算修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox, QTextEdit
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試股價和EPS計算修復")
        dialog.setFixedSize(1100, 1000)
        
        layout = QVBoxLayout(dialog)
        
        # 修復說明
        intro_group = QGroupBox("📊 股價和EPS計算修復")
        intro_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        intro_layout = QVBoxLayout(intro_group)
        
        intro_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; text-align: center;">📊 股價和EPS計算功能</h3>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #e8f5e8; border-radius: 6px; border-left: 4px solid #4caf50;">
                <h4 style="color: #2e7d32; margin: 0 0 8px 0;">✅ 新增功能：</h4>
                <ul style="margin: 0; padding-left: 20px; color: #2e7d32; font-size: 11px;">
                    <li><strong>股價欄位：</strong>從price.db獲取指定日期的股價</li>
                    <li><strong>智能日期查詢：</strong>支援指定日期或最新日期</li>
                    <li><strong>EPS自動計算：</strong>EPS = 股價 ÷ 本益比</li>
                    <li><strong>多資料庫支援：</strong>自動偵測不同的股價資料庫</li>
                    <li><strong>錯誤處理：</strong>避免除以零和無效數據</li>
                </ul>
            </div>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #fff3e0; border-radius: 6px; border-left: 4px solid #ff9800;">
                <h4 style="color: #e65100; margin: 0 0 8px 0;">🔢 計算公式：</h4>
                <div style="text-align: center; font-family: 'Consolas', monospace; font-size: 14px; color: #d84315; background-color: #ffecb3; padding: 8px; border-radius: 4px;">
                    <strong>EPS = 股價 ÷ 本益比</strong>
                </div>
                <p style="margin: 8px 0 0 0; font-size: 10px; color: #bf360c; text-align: center;">
                    例如：股價 49.8元，本益比 19.9，則 EPS = 49.8 ÷ 19.9 = 2.50元
                </p>
            </div>
        </div>
        """
        
        intro_label = QLabel()
        intro_label.setTextFormat(Qt.TextFormat.RichText)
        intro_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        intro_label.setWordWrap(True)
        intro_label.setText(intro_text)
        intro_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        intro_layout.addWidget(intro_label)
        layout.addWidget(intro_group)
        
        # 實際測試結果
        test_group = QGroupBox("🧪 實際測試結果")
        test_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        test_layout = QVBoxLayout(test_group)
        
        test_results_text = QTextEdit()
        test_results_text.setMaximumHeight(350)
        test_results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #333333;
            }
        """)
        
        # 執行實際測試
        test_output = "🔍 股價和EPS計算測試結果:\n\n"
        
        # 測試股票列表（包含2301光寶科）
        test_stocks = ['2301', '2330', '2317', '1101', '8021']
        
        for stock_code in test_stocks:
            test_output += f"📊 {stock_code}:\n"
            try:
                # 測試股價獲取
                stock_price = gui.get_stock_price_by_date(stock_code)
                if stock_price:
                    test_output += f"  股價: {stock_price:.2f}元\n"
                else:
                    test_output += f"  股價: 無法獲取\n"
                
                # 測試完整財務資訊（包含EPS計算）
                financial_info = gui.get_real_financial_info(stock_code)
                for key, value in financial_info.items():
                    test_output += f"  {key}: {value}\n"
                
                # 如果有股價和本益比，驗證EPS計算
                if '股價' in financial_info and '本益比' in financial_info and financial_info['股價'] != 'N/A' and financial_info['本益比'] != 'N/A':
                    try:
                        price = float(financial_info['股價'])
                        pe = float(financial_info['本益比'])
                        calculated_eps = price / pe
                        test_output += f"  驗證計算: {price} ÷ {pe} = {calculated_eps:.2f}元\n"
                    except:
                        test_output += f"  驗證計算: 計算失敗\n"
                
                test_output += f"  ✅ 測試完成\n\n"
                
            except Exception as e:
                test_output += f"  ❌ 測試失敗: {e}\n\n"
        
        test_results_text.setPlainText(test_output)
        test_layout.addWidget(test_results_text)
        layout.addWidget(test_group)
        
        # 技術實現說明
        technical_group = QGroupBox("🔧 技術實現說明")
        technical_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        technical_layout = QVBoxLayout(technical_group)

        technical_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h4 style="color: #2c3e50; margin: 0 0 10px 0;">實現細節：</h4>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 4px; border-left: 4px solid #2196f3;">
                <p style="margin: 0; font-size: 11px; color: #0d47a1;">
                    <strong>1. 股價獲取函數：</strong><br>
                    • <code>get_stock_price_by_date(stock_code, target_date)</code><br>
                    • 支援指定日期或最新日期查詢<br>
                    • 自動偵測不同的資料庫表格結構<br>
                    • 處理欄位名稱差異（Close vs "Close"）
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #fff3e0; border-radius: 4px; border-left: 4px solid #ff9800;">
                <p style="margin: 0; font-size: 11px; color: #e65100;">
                    <strong>2. EPS計算邏輯：</strong><br>
                    • 檢查股價和本益比是否存在且有效<br>
                    • 避免除以零的錯誤處理<br>
                    • 自動格式化為兩位小數<br>
                    • 記錄計算過程供除錯
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #f3e5f5; border-radius: 4px; border-left: 4px solid #9c27b0;">
                <p style="margin: 0; font-size: 11px; color: #4a148c;">
                    <strong>3. 資料庫查詢策略：</strong><br>
                    • 優先查詢指定日期或最接近日期<br>
                    • 備用查詢最新股價<br>
                    • 支援多種資料庫路徑<br>
                    • 智能錯誤恢復機制
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #4caf50;">
                <p style="margin: 0; font-size: 11px; color: #2e7d32;">
                    <strong>4. 整合到財務資訊：</strong><br>
                    • 新增股價欄位到財務指標字典<br>
                    • 自動計算並填入EPS欄位<br>
                    • 保持與現有功能的兼容性<br>
                    • 完整的錯誤處理和日誌記錄
                </p>
            </div>
        </div>
        """

        technical_label = QLabel()
        technical_label.setTextFormat(Qt.TextFormat.RichText)
        technical_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        technical_label.setWordWrap(True)
        technical_label.setText(technical_text)
        technical_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        technical_layout.addWidget(technical_label)
        layout.addWidget(technical_group)
        
        print("✅ 對話框創建成功")
        print("📋 修復內容：")
        print("  1. 新增股價欄位獲取功能")
        print("  2. 實現EPS自動計算（股價÷本益比）")
        print("  3. 支援日期選擇的股價查詢")
        print("  4. 完整的錯誤處理機制")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 股價和EPS計算修復測試")
    print("=" * 50)
    
    result = test_stock_price_eps_calculation()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 股價和EPS計算功能已實現")
        print("📊 現在可以自動計算EPS = 股價 ÷ 本益比")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
