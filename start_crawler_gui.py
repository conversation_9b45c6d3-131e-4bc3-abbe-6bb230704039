#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab 爬蟲系統 GUI 啟動器

自動檢測可用的GUI框架並啟動相應的界面
"""

import sys
import os
import subprocess

def check_gui_framework():
    """檢查可用的GUI框架"""
    frameworks = []

    # 檢查 PyQt6
    try:
        import PyQt6
        frameworks.append(('PyQt6', 'u02_crawlers_gui.py', '功能最完整，界面最美觀'))
        print("✅ 檢測到 PyQt6")
    except ImportError:
        print("❌ PyQt6 未安裝")

    # 檢查 tkinter (通常內建)
    try:
        import tkinter
        frameworks.append(('tkinter', 'u02_crawlers_gui_simple.py', '相容性最佳'))
        frameworks.append(('tkinter (高對比度)', 'u02_crawlers_gui_accessible.py', '視覺可讀性最佳'))
        print("✅ 檢測到 tkinter")
    except ImportError:
        print("❌ tkinter 未安裝")

    return frameworks

def main():
    """主函數"""
    print("🚀 Finlab 爬蟲系統 GUI 啟動器")
    print("=" * 50)
    
    # 檢查可用的GUI框架
    frameworks = check_gui_framework()
    
    if not frameworks:
        print("❌ 沒有可用的GUI框架")
        print("💡 請安裝以下任一GUI框架：")
        print("   - PyQt6: pip install PyQt6")
        print("   - tkinter: 通常已內建，如未安裝請查閱系統文檔")
        return False
    
    print(f"\n📊 找到 {len(frameworks)} 個可用的GUI版本")

    # 顯示所有可用選項
    if len(frameworks) > 1:
        print("\n可用的GUI版本:")
        for i, (name, script, desc) in enumerate(frameworks, 1):
            print(f"  {i}. {name} - {desc}")

        print("\n請選擇要使用的版本 (直接按Enter使用預設版本):")
        try:
            choice = input("輸入選項編號: ").strip()
            if choice and choice.isdigit():
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(frameworks):
                    selected_framework, selected_script, selected_desc = frameworks[choice_idx]
                else:
                    print("⚠️ 無效選項，使用預設版本")
                    selected_framework, selected_script, selected_desc = frameworks[0]
            else:
                selected_framework, selected_script, selected_desc = frameworks[0]
        except (KeyboardInterrupt, EOFError):
            print("\n⏹️ 用戶取消")
            return False
    else:
        selected_framework, selected_script, selected_desc = frameworks[0]

    print(f"\n🎯 使用 {selected_framework} 啟動GUI界面")
    print(f"📄 執行腳本: {selected_script}")
    print(f"💡 特色: {selected_desc}")
    
    try:
        # 啟動GUI程式
        print(f"\n🔄 正在啟動 {selected_script}...")
        subprocess.run([sys.executable, selected_script], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ GUI程式執行失敗: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷程式")
        return True
    except Exception as e:
        print(f"❌ 未知錯誤: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按 Enter 鍵退出...")
        sys.exit(1)
