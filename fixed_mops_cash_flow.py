#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的 MOPS 現金流量表爬蟲 (處理重定向問題)
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import logging
from typing import Dict, Optional
from datetime import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class FixedMOPSCashFlowScraper:
    """修正的 MOPS 現金流量表爬蟲"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        self.session.verify = False
    
    def get_cash_flow_statement(self, stock_code: str, year: int, season: int) -> Optional[pd.DataFrame]:
        """
        獲取現金流量表 (處理重定向問題)
        """
        logger.info(f"🔍 獲取 {stock_code} {year}年第{season}季現金流量表...")
        
        try:
            # 步驟1: 先訪問主頁面建立 session
            main_url = "https://mops.twse.com.tw/mops/web/index"
            logger.info(f"📡 訪問主頁面: {main_url}")
            
            response = self.session.get(main_url, timeout=30)
            if response.status_code != 200:
                logger.error(f"❌ 主頁面訪問失敗: {response.status_code}")
                return None
            
            # 步驟2: 訪問現金流量表頁面
            cash_flow_url = "https://mops.twse.com.tw/mops/web/t164sb05"
            logger.info(f"📡 訪問現金流量表頁面: {cash_flow_url}")
            
            response = self.session.get(cash_flow_url, timeout=30)
            if response.status_code != 200:
                logger.error(f"❌ 現金流量表頁面訪問失敗: {response.status_code}")
                return None
            
            # 步驟3: 提交查詢表單
            roc_year = year - 1911
            
            payload = {
                'encodeURIComponent': '1',
                'step': '1',
                'firstin': '1',
                'off': '1',
                'queryName': 'co_id',
                'TYPEK': 'sii',
                'co_id': stock_code,
                'year': str(roc_year),
                'season': str(season)
            }
            
            logger.info(f"📊 提交查詢: 股票={stock_code}, 民國年={roc_year}, 季別={season}")
            
            # 設置正確的 Referer
            self.session.headers['Referer'] = cash_flow_url
            
            response = self.session.post(cash_flow_url, data=payload, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                logger.info(f"✅ 查詢請求成功")
                return self._parse_response_with_redirect_handling(response.text, stock_code)
            else:
                logger.error(f"❌ 查詢請求失敗: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 獲取現金流量表失敗: {e}")
            return None
    
    def _parse_response_with_redirect_handling(self, html_content: str, stock_code: str) -> Optional[pd.DataFrame]:
        """解析回應並處理重定向"""
        try:
            # 檢查是否有 JavaScript 重定向
            if 'location.href' in html_content and '/mops' in html_content:
                logger.warning(f"⚠️ 檢測到重定向，嘗試跟隨重定向...")
                
                # 嘗試直接訪問重定向後的頁面
                redirect_url = "https://mops.twse.com.tw/mops"
                response = self.session.get(redirect_url, timeout=30)
                
                if response.status_code == 200:
                    html_content = response.text
                    logger.info(f"✅ 重定向成功")
                else:
                    logger.error(f"❌ 重定向失敗: {response.status_code}")
                    return None
            
            # 檢查是否有錯誤訊息
            if '查無資料' in html_content or '無此資料' in html_content:
                logger.warning(f"⚠️ {stock_code}: 查無資料")
                return None
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 尋找所有表格
            tables = soup.find_all('table')
            logger.info(f"📊 找到 {len(tables)} 個表格")
            
            # 尋找現金流量表
            for i, table in enumerate(tables):
                table_text = table.get_text()
                
                # 檢查是否包含現金流量相關內容
                cash_flow_keywords = ['現金流量', '營業活動', '投資活動', '籌資活動', '現金及約當現金']
                
                if any(keyword in table_text for keyword in cash_flow_keywords):
                    logger.info(f"   📊 表格 {i+1} 包含現金流量資料")
                    
                    try:
                        # 使用 pandas 讀取表格
                        df_list = pd.read_html(str(table), header=0)
                        
                        if df_list:
                            df = df_list[0]
                            logger.info(f"   ✅ 成功解析表格: {df.shape}")
                            
                            # 清理資料
                            df['stock_id'] = stock_code
                            df = df.dropna(how='all').reset_index(drop=True)
                            
                            return df
                        
                    except Exception as e:
                        logger.warning(f"   ⚠️ 表格 {i+1} 解析失敗: {e}")
                        continue
            
            logger.warning(f"⚠️ {stock_code}: 未找到現金流量表")
            return None
            
        except Exception as e:
            logger.error(f"❌ 解析回應失敗: {e}")
            return None

def test_alternative_approach():
    """測試替代方法"""
    
    print("=" * 80)
    print("🧪 測試替代的 MOPS 現金流量表獲取方法")
    print("=" * 80)
    
    # 嘗試直接訪問不同的 URL 格式
    alternative_urls = [
        "https://mops.twse.com.tw/mops/web/ajax_t164sb05",
        "https://mops.twse.com.tw/mops/web/t164sb05_ajax",
        "https://mops.twse.com.tw/server-java/t164sb05",
        "https://mops.twse.com.tw/mops/web/t164sb05?step=0",
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    session.verify = False
    
    for url in alternative_urls:
        print(f"\n🔍 測試 URL: {url}")
        
        try:
            # 先嘗試 GET
            response = session.get(url, timeout=10)
            print(f"   GET 狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if len(content) > 100:  # 有實際內容
                    print(f"   ✅ GET 成功，內容長度: {len(content)}")
                    
                    # 檢查是否包含表單或現金流量相關內容
                    if 'form' in content.lower():
                        print(f"   📝 包含表單")
                    if any(kw in content for kw in ['現金流量', '營業活動']):
                        print(f"   💰 包含現金流量相關內容")
                else:
                    print(f"   ⚠️ 內容太短: {len(content)} 字符")
            
            # 再嘗試 POST
            payload = {
                'co_id': '2330',
                'year': '112',
                'season': '4'
            }
            
            response = session.post(url, data=payload, timeout=10)
            print(f"   POST 狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                if len(content) > 100:
                    print(f"   ✅ POST 成功，內容長度: {len(content)}")
                    
                    # 檢查現金流量內容
                    if any(kw in content for kw in ['現金流量', '營業活動']):
                        print(f"   💰 POST 回應包含現金流量資料")
                        
                        # 嘗試解析表格
                        soup = BeautifulSoup(content, 'html.parser')
                        tables = soup.find_all('table')
                        print(f"   📊 找到 {len(tables)} 個表格")
                        
                        if tables:
                            print(f"   🎯 這個 URL 可能有效！")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")

def test_selenium_approach():
    """測試是否需要使用 Selenium"""
    
    print(f"\n" + "=" * 80)
    print("💡 Selenium 方法建議")
    print("=" * 80)
    
    print("🤔 基於測試結果，MOPS 網站可能需要 JavaScript 執行")
    print()
    print("📋 Selenium 方法的優勢:")
    print("   1. 可以執行 JavaScript")
    print("   2. 可以處理動態內容")
    print("   3. 可以模擬真實瀏覽器行為")
    print("   4. 可以處理複雜的表單提交")
    print()
    print("⚠️ Selenium 方法的缺點:")
    print("   1. 需要安裝瀏覽器驅動")
    print("   2. 執行速度較慢")
    print("   3. 資源消耗較大")
    print("   4. 維護成本較高")
    print()
    print("🎯 建議:")
    print("   1. 如果替代 URL 測試成功，使用 requests")
    print("   2. 如果都失敗，考慮使用 Selenium")
    print("   3. 或繼續使用現有的 TWSE OpenAPI 系統")

def main():
    """主函數"""
    
    print("🐍 修正的 MOPS 現金流量表爬蟲測試")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試修正的爬蟲
    scraper = FixedMOPSCashFlowScraper()
    
    print("\n📊 測試修正的爬蟲...")
    df = scraper.get_cash_flow_statement("2330", 2023, 4)
    
    if df is not None and not df.empty:
        print(f"✅ 修正的爬蟲成功: {df.shape}")
        print(df.head())
    else:
        print(f"❌ 修正的爬蟲仍然失敗")
    
    # 測試替代方法
    test_alternative_approach()
    
    # Selenium 建議
    test_selenium_approach()
    
    print(f"\n" + "=" * 80)
    print("📊 最終建議")
    print("=" * 80)
    print("🎯 基於所有測試結果:")
    print("   1. MOPS 網站結構複雜，可能需要 JavaScript")
    print("   2. 多個現金流量表爬蟲都遇到類似問題")
    print("   3. 現有的 TWSE OpenAPI 系統更穩定可靠")
    print()
    print("💡 最終建議:")
    print("   ✅ 繼續使用現有的 TWSE OpenAPI 系統")
    print("   📊 1,008家上市公司 + 835家上櫃公司")
    print("   🏛️ 官方支援，穩定可靠")
    print("   💰 使用現有的 38,299筆現金流量歷史資料")

if __name__ == "__main__":
    main()
