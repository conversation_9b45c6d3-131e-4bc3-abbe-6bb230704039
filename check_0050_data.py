#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專門檢查 0050 ETF 的資料
"""

import sqlite3
import pandas as pd
import os

def check_0050_data():
    """檢查 0050 ETF 的資料"""
    
    print("=" * 60)
    print("📈 檢查 0050 ETF 資料")
    print("=" * 60)
    
    # 檢查主要的 newprice.db
    db_file = 'D:/Finlab/history/tables/newprice.db'
    
    if os.path.exists(db_file):
        print(f"🔍 檢查資料庫: {db_file}")
        
        # 檢查檔案資訊
        file_size = os.path.getsize(db_file)
        import datetime
        mod_time = os.path.getmtime(db_file)
        mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
        print(f"   📊 檔案大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        print(f"   🕒 最後修改: {mod_time_str}")
        
        try:
            conn = sqlite3.connect(db_file)
            
            # 檢查 0050 的所有資料
            query_0050_all = '''
                SELECT date, [Close], Volume, [Open], High, Low, [Transaction], TradeValue, stock_name, listing_status, industry
                FROM stock_daily_data 
                WHERE stock_id = '0050'
                ORDER BY date DESC
                LIMIT 20
            '''
            
            df_0050 = pd.read_sql_query(query_0050_all, conn)
            
            if not df_0050.empty:
                print(f"\n📈 0050 最新20筆資料:")
                print(f"   總共找到 {len(df_0050)} 筆 0050 資料")
                
                for i, row in df_0050.iterrows():
                    close_val = row['Close'] if pd.notna(row['Close']) else 'NULL'
                    volume_val = row['Volume'] if pd.notna(row['Volume']) else 'NULL'
                    open_val = row['Open'] if pd.notna(row['Open']) else 'NULL'
                    high_val = row['High'] if pd.notna(row['High']) else 'NULL'
                    low_val = row['Low'] if pd.notna(row['Low']) else 'NULL'
                    
                    print(f"   {row['date']}: 開盤={open_val}, 最高={high_val}, 最低={low_val}, 收盤={close_val}, 成交量={volume_val}")
                
                # 檢查最新和最舊的日期
                latest_date = df_0050.iloc[0]['date']
                oldest_date = df_0050.iloc[-1]['date']
                print(f"\n📅 0050 資料範圍:")
                print(f"   最新日期: {latest_date}")
                print(f"   最舊日期: {oldest_date}")
                
            else:
                print(f"\n❌ 未找到 0050 資料")
            
            # 檢查 0050 的總記錄數
            query_count = '''
                SELECT COUNT(*) as total_count
                FROM stock_daily_data 
                WHERE stock_id = '0050'
            '''
            
            result = pd.read_sql_query(query_count, conn)
            total_count = result.iloc[0]['total_count']
            print(f"\n📊 0050 總記錄數: {total_count}")
            
            # 檢查最近一個月的 0050 資料
            query_recent = '''
                SELECT date, COUNT(*) as count
                FROM stock_daily_data 
                WHERE stock_id = '0050' AND date >= '2022-08-15'
                GROUP BY date
                ORDER BY date DESC
            '''
            
            df_recent = pd.read_sql_query(query_recent, conn)
            
            if not df_recent.empty:
                print(f"\n📋 0050 最近資料 (從 2022-08-15 開始):")
                for _, row in df_recent.iterrows():
                    print(f"   {row['date']}: {row['count']} 筆")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取資料庫失敗: {e}")
    else:
        print(f"❌ 資料庫檔案不存在: {db_file}")
    
    # 也檢查其他可能包含 0050 資料的資料庫
    other_dbs = [
        './daily_trading.db',
        './stock_data.db',
        './goodinfo_data.db'
    ]
    
    for db_path in other_dbs:
        if os.path.exists(db_path):
            print(f"\n🔍 檢查其他資料庫: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                table_names = [table[0] for table in tables]
                
                for table_name in table_names:
                    try:
                        # 檢查是否有 0050 相關資料
                        cursor.execute(f"SELECT * FROM {table_name} WHERE StockID = '0050' OR stock_id = '0050' OR symbol = '0050' OR stock_code = '0050' LIMIT 5")
                        rows = cursor.fetchall()
                        
                        if rows:
                            print(f"   ✅ 在表格 {table_name} 找到 0050 資料:")
                            
                            # 獲取欄位名稱
                            cursor.execute(f"PRAGMA table_info({table_name})")
                            columns = cursor.fetchall()
                            column_names = [col[1] for col in columns]
                            print(f"      欄位: {column_names}")
                            
                            for row in rows[:3]:  # 只顯示前3筆
                                print(f"      資料: {row}")
                    
                    except Exception as e:
                        # 忽略不相關的表格錯誤
                        pass
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 讀取失敗: {e}")

if __name__ == "__main__":
    check_0050_data()
