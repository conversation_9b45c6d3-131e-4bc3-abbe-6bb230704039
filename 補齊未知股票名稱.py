#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
補齊未知股票名稱
根據股票代碼查詢並更新股票名稱資訊
"""

import sys
import os
import sqlite3
import requests
import re
import time
from datetime import datetime

# 從圖片中提取的未知股票代碼
UNKNOWN_STOCKS = [
    '00694B', '00720B', '00723B', '00726B', '00740B', '00746B', 
    '00751B', '00756B', '00761B', '00768B', '00772B', '00773B'
]

def get_stock_info_from_twse():
    """從證交所網站獲取股票資訊"""
    stock_info = {}
    
    try:
        print("🔍 從證交所網站查詢股票資訊...")
        
        # 查詢上市股票
        url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=2"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'big5'
        
        if response.status_code == 200:
            # 解析股票資訊 - 包含ETF和債券
            pattern = r'<td bgcolor=#FAFAD2>(\w+)　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>([^<]*)</td><td[^>]*>([^<]*)</td>'
            matches = re.findall(pattern, response.text)
            
            for code, name, listing_type, industry in matches:
                if code:
                    stock_info[code] = {
                        'name': name.strip(),
                        'listing_type': listing_type.strip(),
                        'industry': industry.strip()
                    }
            
            print(f"✅ 從證交所獲取 {len(stock_info)} 檔股票資訊")
        
        time.sleep(1)  # 避免請求過於頻繁
        
        # 查詢上櫃股票
        url2 = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=4"
        response2 = requests.get(url2, headers=headers, timeout=30)
        response2.encoding = 'big5'
        
        if response2.status_code == 200:
            matches2 = re.findall(pattern, response2.text)
            
            for code, name, listing_type, industry in matches2:
                if code:
                    stock_info[code] = {
                        'name': name.strip(),
                        'listing_type': listing_type.strip(),
                        'industry': industry.strip()
                    }
            
            print(f"✅ 總共獲取 {len(stock_info)} 檔股票資訊")
        
    except Exception as e:
        print(f"❌ 從證交所查詢失敗: {e}")
    
    return stock_info

def get_stock_info_from_database():
    """從本地資料庫查詢股票資訊"""
    stock_info = {}
    
    # 檢查多個可能的資料庫
    db_paths = [
        'D:/Finlab/history/tables/pe_data.db',
        'D:/Finlab/history/tables/price.db',
        'D:/Finlab/history/tables/stock_info.db',
        'history/tables/pe_data.db',
        'history/tables/price.db'
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            try:
                print(f"🔍 檢查資料庫: {db_path}")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 檢查可能的表格
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                for table in tables:
                    try:
                        # 嘗試查詢股票資訊
                        cursor.execute(f"PRAGMA table_info({table})")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        if 'stock_id' in columns and 'stock_name' in columns:
                            cursor.execute(f"SELECT DISTINCT stock_id, stock_name FROM {table} WHERE stock_name IS NOT NULL AND stock_name != ''")
                            for stock_id, stock_name in cursor.fetchall():
                                if stock_id and stock_name:
                                    stock_info[str(stock_id)] = {
                                        'name': stock_name,
                                        'source': f'{db_path}:{table}'
                                    }
                    except:
                        continue
                
                conn.close()
                
            except Exception as e:
                print(f"⚠️ 讀取 {db_path} 失敗: {e}")
    
    print(f"✅ 從資料庫獲取 {len(stock_info)} 檔股票資訊")
    return stock_info

def lookup_unknown_stocks():
    """查詢未知股票的名稱"""
    print("🎯 開始查詢未知股票名稱...")
    print("=" * 50)
    
    # 先從資料庫查詢
    db_stock_info = get_stock_info_from_database()
    
    # 再從網路查詢
    web_stock_info = get_stock_info_from_twse()
    
    # 合併資訊
    all_stock_info = {**db_stock_info, **web_stock_info}
    
    print(f"\n📊 查詢結果:")
    print("=" * 50)
    
    found_stocks = []
    not_found_stocks = []
    
    for stock_code in UNKNOWN_STOCKS:
        if stock_code in all_stock_info:
            info = all_stock_info[stock_code]
            stock_name = info['name']
            found_stocks.append((stock_code, stock_name))
            print(f"✅ {stock_code}: {stock_name}")
        else:
            not_found_stocks.append(stock_code)
            print(f"❌ {stock_code}: 未找到")
    
    print(f"\n📈 統計結果:")
    print(f"✅ 找到: {len(found_stocks)} 檔")
    print(f"❌ 未找到: {len(not_found_stocks)} 檔")
    
    if found_stocks:
        print(f"\n📋 完整清單:")
        print("-" * 30)
        for code, name in found_stocks:
            print(f"{code} {name}")
    
    if not_found_stocks:
        print(f"\n⚠️ 仍未找到的股票:")
        print("-" * 30)
        for code in not_found_stocks:
            print(f"{code} 未知股票")
    
    return found_stocks, not_found_stocks

def update_stock_database(found_stocks):
    """更新股票資料庫"""
    if not found_stocks:
        print("⚠️ 沒有需要更新的股票資訊")
        return
    
    try:
        # 更新到主要的股票資料庫
        db_path = 'D:/Finlab/history/tables/stock_info.db'
        
        # 確保目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 創建股票資訊表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                stock_id TEXT PRIMARY KEY,
                stock_name TEXT,
                updated_time TEXT
            )
        ''')
        
        # 插入或更新股票資訊
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for stock_code, stock_name in found_stocks:
            cursor.execute('''
                INSERT OR REPLACE INTO stock_info 
                (stock_id, stock_name, updated_time) 
                VALUES (?, ?, ?)
            ''', (stock_code, stock_name, current_time))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已更新 {len(found_stocks)} 檔股票資訊到資料庫")
        
    except Exception as e:
        print(f"❌ 更新資料庫失敗: {e}")

def main():
    """主函數"""
    print("🚀 補齊未知股票名稱工具")
    print("=" * 50)
    print(f"📋 待查詢股票: {len(UNKNOWN_STOCKS)} 檔")
    print(f"📋 股票代碼: {', '.join(UNKNOWN_STOCKS)}")
    print()
    
    # 查詢股票名稱
    found_stocks, not_found_stocks = lookup_unknown_stocks()
    
    # 更新資料庫
    if found_stocks:
        update_stock_database(found_stocks)
    
    print(f"\n🎉 查詢完成！")
    print(f"✅ 成功找到 {len(found_stocks)} 檔股票名稱")
    if not_found_stocks:
        print(f"⚠️ 仍有 {len(not_found_stocks)} 檔股票需要手動查詢")

if __name__ == "__main__":
    main()
