#!/usr/bin/env python3
"""
重構測試腳本
測試重構後的程式是否正常運行
"""
import sys
import os

def test_import():
    """測試模組導入"""
    try:
        import O3mh_gui_v21_optimized
        print("✅ 主程式模組導入成功")
        return True
    except Exception as e:
        print(f"❌ 主程式模組導入失敗: {e}")
        return False

def test_strategy_classes():
    """測試策略類別是否正常"""
    try:
        from O3mh_gui_v21_optimized import (
            TripleRSIStrategy,
            SmallInvestorEliteStrategy,
            SecondHighStrategy,
            TimidCatStrategy,
            CANSLIMStrategy
        )

        # 測試策略實例化
        strategies = {
            "三頻率RSI策略": TripleRSIStrategy(),
            "小資族資優生策略": SmallInvestorEliteStrategy(),
            "二次創高策略": SecondHighStrategy(),
            "膽小貓策略": TimidCatStrategy(),
            "CANSLIM策略": CANSLIMStrategy()
        }

        for name, strategy in strategies.items():
            if hasattr(strategy, 'name') and hasattr(strategy, 'analyze_stock'):
                print(f"✅ {name} 類別正常")
            else:
                print(f"❌ {name} 類別缺少必要方法")
                return False

        return True

    except Exception as e:
        print(f"❌ 策略類別測試失敗: {e}")
        return False

def test_no_duplicate_classes():
    """測試是否還有重複的類別定義"""
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 檢查TripleRSIStrategy定義次數
        triple_rsi_count = content.count('class TripleRSIStrategy:')

        if triple_rsi_count == 1:
            print("✅ TripleRSIStrategy 重複定義已移除")
            return True
        else:
            print(f"❌ TripleRSIStrategy 仍有 {triple_rsi_count} 個定義")
            return False

    except Exception as e:
        print(f"❌ 重複檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始重構測試...")
    print("=" * 50)

    tests = [
        ("模組導入測試", test_import),
        ("策略類別測試", test_strategy_classes),
        ("重複定義檢查", test_no_duplicate_classes)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 執行 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"⚠️ {test_name} 失敗")

    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")

    if passed == total:
        print("🎉 所有測試通過！重構第一階段成功")
        return True
    else:
        print("❌ 部分測試失敗，需要檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)