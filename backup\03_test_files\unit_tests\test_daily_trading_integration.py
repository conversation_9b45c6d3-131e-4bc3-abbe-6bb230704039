"""
測試每日交易資料爬蟲整合功能
"""

import sys
import os

def test_module_imports():
    """測試模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        from taiwan_stock_price_crawler import (
            DailyTradingDatabase, 
            gen_task_parameter_list, 
            crawler
        )
        print("✅ 核心爬蟲模組導入成功")
        
        from daily_trading_crawler_gui import DailyTradingCrawlerGUI
        print("✅ GUI模組導入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_database_functionality():
    """測試資料庫功能"""
    print("\n🔍 測試資料庫功能...")
    
    try:
        from taiwan_stock_price_crawler import DailyTradingDatabase
        
        # 創建測試資料庫
        db = DailyTradingDatabase('test_integration.db')
        print("✅ 資料庫初始化成功")
        
        # 測試統計功能
        count = db.get_data_count()
        print(f"✅ 資料庫統計功能正常，目前有 {count} 筆資料")
        
        return True
    except Exception as e:
        print(f"❌ 資料庫功能測試失敗: {e}")
        return False

def test_task_generation():
    """測試任務生成功能"""
    print("\n🔍 測試任務生成功能...")
    
    try:
        from taiwan_stock_price_crawler import gen_task_parameter_list
        
        tasks = gen_task_parameter_list('2024-01-01', '2024-01-03')
        print(f"✅ 任務生成成功，共 {len(tasks)} 個任務")
        
        # 顯示前幾個任務
        for i, task in enumerate(tasks[:3]):
            print(f"   任務 {i+1}: {task}")
        
        return True
    except Exception as e:
        print(f"❌ 任務生成測試失敗: {e}")
        return False

def test_menu_integration():
    """測試選單整合"""
    print("\n🔍 測試選單整合...")
    
    try:
        # 檢查主程式是否包含新的選單項目
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'open_daily_trading_crawler' in content:
            print("✅ 主程式包含每日交易資料爬蟲方法")
        else:
            print("❌ 主程式缺少每日交易資料爬蟲方法")
            return False
        
        if '每日交易資料' in content:
            print("✅ 選單包含每日交易資料項目")
        else:
            print("❌ 選單缺少每日交易資料項目")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 選單整合測試失敗: {e}")
        return False

def test_file_structure():
    """測試檔案結構"""
    print("\n🔍 測試檔案結構...")
    
    required_files = [
        'taiwan_stock_price_crawler.py',
        'daily_trading_crawler_gui.py',
        'O3mh_gui_v21_optimized.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            missing_files.append(file)
    
    return len(missing_files) == 0

def main():
    """主測試函數"""
    print("🚀 開始每日交易資料爬蟲整合測試")
    print("=" * 50)
    
    tests = [
        ("檔案結構", test_file_structure),
        ("模組導入", test_module_imports),
        ("資料庫功能", test_database_functionality),
        ("任務生成", test_task_generation),
        ("選單整合", test_menu_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 測試未通過")
        except Exception as e:
            print(f"❌ {test_name} 測試發生錯誤: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！每日交易資料爬蟲整合成功！")
        print("\n📋 使用說明:")
        print("1. 啟動主程式 O3mh_gui_v21_optimized.py")
        print("2. 點擊選單：🕷️ 爬蟲 → 📊 每日交易資料")
        print("3. 設定日期範圍並開始爬取")
        print("4. 資料將儲存到 daily_trading.db 資料庫")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
