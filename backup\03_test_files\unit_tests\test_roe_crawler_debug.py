#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試ROE爬蟲的詳細調試
"""

import requests
from bs4 import BeautifulSoup
import logging
import os

# 設置詳細日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_goodinfo_direct():
    """直接測試GoodInfo網站"""
    print("🔍 直接測試GoodInfo網站...")
    
    try:
        # 設置請求標頭
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://goodinfo.tw/',
        }
        
        # 構建URL
        base_url = "https://goodinfo.tw/tw2/StockList.asp"
        params = {
            'MARKET_CAT': '熱門排行',
            'INDUSTRY_CAT': '年度ROE最高'
        }
        
        print(f"📡 請求URL: {base_url}")
        print(f"📋 請求參數: {params}")
        
        # 發送請求
        session = requests.Session()
        response = session.get(base_url, params=params, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        print(f"✅ 請求成功，狀態碼: {response.status_code}")
        print(f"📄 內容長度: {len(response.text)} 字符")
        
        # 保存HTML用於分析
        debug_file = "goodinfo_roe_debug.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"💾 HTML內容已保存至: {debug_file}")
        
        # 檢查內容
        if '初始化中' in response.text:
            print("⚠️ 檢測到反爬蟲機制（初始化中）")
        elif 'JavaScript' in response.text:
            print("⚠️ 檢測到JavaScript重定向")
        else:
            print("✅ 內容看起來正常")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 尋找表格
        tables = soup.find_all('table')
        print(f"📊 找到 {len(tables)} 個表格")
        
        # 分析每個表格
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            if len(rows) > 1:
                header_row = rows[0]
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
                print(f"📋 表格 {i+1}: {len(rows)} 行，表頭: {headers[:5]}...")  # 只顯示前5個表頭
                
                # 檢查是否包含ROE相關欄位
                if any('ROE' in header or '股東權益報酬率' in header or '報酬率' in header for header in headers):
                    print(f"✅ 表格 {i+1} 包含ROE相關欄位")
                    
                    # 分析前幾行資料
                    for j, row in enumerate(rows[1:6], 1):  # 只檢查前5行
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 3:
                            cell_texts = [cell.get_text().strip() for cell in cells[:5]]  # 只顯示前5個欄位
                            print(f"  行 {j}: {cell_texts}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_roe_crawler_module():
    """測試ROE爬蟲模組"""
    print("\n🔍 測試ROE爬蟲模組...")
    
    try:
        from roe_data_crawler import ROEDataCrawler
        
        # 創建爬蟲實例
        crawler = ROEDataCrawler()
        print("✅ ROE爬蟲實例創建成功")
        
        # 測試抓取2023年資料
        print("🚀 開始抓取2023年ROE資料...")
        roe_data = crawler.fetch_roe_data(2023)
        
        if roe_data:
            print(f"✅ 成功獲取 {len(roe_data)} 筆ROE資料")
            print("📊 前5筆資料:")
            for i, record in enumerate(roe_data[:5], 1):
                print(f"  {i}. {record['stock_code']} {record['stock_name']} - ROE: {record['roe_value']}% (來源: {record['data_source']})")
            
            # 檢查資料來源
            data_sources = set(record['data_source'] for record in roe_data)
            print(f"📋 資料來源: {data_sources}")
            
            return True
        else:
            print("❌ 未能獲取ROE資料")
            return False
            
    except Exception as e:
        print(f"❌ ROE爬蟲模組測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_html_file():
    """分析保存的HTML文件"""
    print("\n🔍 分析HTML文件...")
    
    debug_file = "goodinfo_roe_debug.html"
    if not os.path.exists(debug_file):
        print(f"❌ 找不到調試文件: {debug_file}")
        return False
    
    try:
        with open(debug_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 檢查關鍵字
        keywords = ['ROE', '股東權益報酬率', '報酬率', '台積電', '2330', '初始化中', 'JavaScript']
        for keyword in keywords:
            count = content.count(keyword)
            if count > 0:
                print(f"🔍 找到關鍵字 '{keyword}': {count} 次")
        
        # 檢查表格結構
        soup = BeautifulSoup(content, 'html.parser')
        tables = soup.find_all('table')
        
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            if len(rows) > 5:  # 只分析有足夠行數的表格
                print(f"\n📊 詳細分析表格 {i+1}:")
                
                # 分析表頭
                if rows:
                    header_cells = rows[0].find_all(['th', 'td'])
                    headers = [cell.get_text().strip() for cell in header_cells]
                    print(f"  表頭 ({len(headers)} 欄): {headers}")
                
                # 分析前幾行資料
                for j, row in enumerate(rows[1:4], 1):
                    cells = row.find_all(['td', 'th'])
                    if cells:
                        cell_texts = [cell.get_text().strip() for cell in cells]
                        print(f"  資料行 {j} ({len(cell_texts)} 欄): {cell_texts}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析HTML文件失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始ROE爬蟲詳細調試")
    print("=" * 60)
    
    # 測試1: 直接請求GoodInfo
    success1 = test_goodinfo_direct()
    
    # 測試2: 分析HTML文件
    success2 = analyze_html_file()
    
    # 測試3: 測試ROE爬蟲模組
    success3 = test_roe_crawler_module()
    
    print("\n" + "=" * 60)
    print("📋 測試結果摘要:")
    print(f"  直接請求GoodInfo: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"  HTML文件分析: {'✅ 成功' if success2 else '❌ 失敗'}")
    print(f"  ROE爬蟲模組: {'✅ 成功' if success3 else '❌ 失敗'}")
    
    if success1 and success2:
        print("\n💡 建議:")
        print("1. 檢查goodinfo_roe_debug.html文件，查看實際的網頁內容")
        print("2. 如果看到'初始化中'，表示網站有反爬蟲機制")
        print("3. 如果看到正常的表格，可能需要調整解析邏輯")
        print("4. 考慮使用Selenium等工具處理JavaScript")
    
    return success1 and success2 and success3

if __name__ == "__main__":
    main()
