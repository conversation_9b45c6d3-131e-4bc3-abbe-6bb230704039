#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google股票新聞爬蟲 - 加速版本
主要優化：
1. 限制完整內容下載數量
2. 並發處理
3. 減少延遲
4. 優先處理最新新聞
"""

import sys
import os
import logging
import requests
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import re
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

class GoogleNewsFastCrawler:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.progress_callback = None
        self.db_lock = Lock()  # 資料庫寫入鎖
        
        # 加速設定
        self.max_full_content = 15  # 只下載前15篇的完整內容
        self.max_workers = 3  # 並發執行緒數
        self.request_timeout = 8  # 減少超時時間
        
    def set_progress_callback(self, callback):
        """設置進度回調函數"""
        self.progress_callback = callback
        
    def _report_progress(self, message):
        """報告進度"""
        if self.progress_callback:
            self.progress_callback(message)
        else:
            print(message)
            
    def _get_stock_name(self, stock_code):
        """獲取股票名稱"""
        stock_names = {
            '2330': '台積電', '2317': '鴻海', '2454': '聯發科', '2881': '富邦金',
            '2882': '國泰金', '2886': '兆豐金', '2891': '中信金', '2892': '第一金',
            '2884': '玉山金', '2885': '元大金', '2303': '聯電', '2002': '中鋼',
            '1301': '台塑', '1303': '南亞', '1326': '台化', '2207': '和泰車',
            '2408': '南亞科', '3008': '大立光', '2412': '中華電', '4938': '和碩',
            '2357': '華碩', '2382': '廣達', '6505': '台塑化', '2308': '台達電',
            '2395': '研華', '3711': '日月光投控', '2327': '國巨', '2409': '友達',
            '2474': '可成', '2301': '光寶科', '2379': '瑞昱', '3034': '聯詠',
            '2344': '華邦電', '2324': '仁寶', '2356': '英業達', '2377': '微星',
            '3045': '台灣大', '4904': '遠傳', '2880': '華南金', '2887': '台新金',
            '0050': '元大台灣50', '0056': '元大高股息', '00878': '國泰永續高股息',
            '00692': '富邦公司治理', '00881': '國泰台灣5G+', '00900': '富邦特選高股息30',
            '00919': '群益台灣精選高息', '00929': '復華台灣科技優息', '00934': '中信成長高股息',
            '00936': '台新永續高息中小', '00940': '元大台灣價值高息', '00946': '統一超商',
        }
        return stock_names.get(stock_code, '')

    def search_stock_news(self, stock_code: str, days: int = 7):
        """搜尋特定股票的新聞 - 加速版本"""
        try:
            self._report_progress(f"🚀 開始快速搜尋 {stock_code} 的Google新聞...")

            # 獲取股票名稱並構建搜尋關鍵字
            stock_name = self._get_stock_name(stock_code)
            search_keyword = f"{stock_code}{stock_name}" if stock_name else stock_code

            self._report_progress(f"🔍 搜尋關鍵字: {search_keyword}")

            # 建立搜尋網址
            url = f'https://news.google.com/news/rss/search/section/q/{search_keyword}/?hl=zh-tw&gl=TW&ned=zh-tw_tw'

            # 取得RSS資料
            response = requests.get(url, timeout=self.request_timeout)
            soup = BeautifulSoup(response.text, 'xml')
            items = soup.find_all('item')

            if not items:
                self._report_progress("❌ 沒有找到相關新聞")
                return []

            self._report_progress(f"📰 找到 {len(items)} 筆RSS新聞項目")

            # 解析新聞項目
            news_data = []
            cutoff_date = datetime.now() - timedelta(days=days)

            for item in items:
                try:
                    # 解析發布日期
                    pub_date_str = item.pubDate.text
                    pub_date = datetime.strptime(pub_date_str, '%a, %d %b %Y %H:%M:%S %Z')
                    
                    # 檢查日期範圍
                    if pub_date < cutoff_date:
                        continue

                    # 解析新聞資訊
                    title = item.title.text
                    link = item.link.text
                    description = item.description.text if item.description else ""
                    source = item.source.text if item.source else "Google新聞"

                    news_data.append({
                        'search_time': datetime.now().strftime('%Y%m%d%H%M%S'),
                        'search_key': search_keyword,
                        'title': title,
                        'link': link,
                        'pub_date': pub_date,
                        'description': description,
                        'source': source
                    })

                except Exception as e:
                    self.logger.warning(f"解析新聞項目失敗: {e}")
                    continue

            if not news_data:
                self._report_progress("❌ 沒有找到符合日期範圍的新聞")
                return []

            # 轉換為DataFrame並按日期排序
            df = pd.DataFrame(news_data)
            df = df.sort_values('pub_date', ascending=False)  # 最新的在前
            
            self._report_progress(f"✅ 篩選出 {len(df)} 筆最近 {days} 天的新聞")

            # 快速爬取新聞內容
            return self._crawl_news_content_fast(df, stock_code)

        except Exception as e:
            self.logger.error(f"❌ 搜尋 {stock_code} 新聞失敗: {e}")
            return []

    def _crawl_news_content_fast(self, df, stock_code):
        """快速爬取新聞內容 - 並發版本"""
        news_list = []
        
        self._report_progress(f"⚡ 快速模式：前 {min(self.max_full_content, len(df))} 篇下載完整內容，其餘使用摘要")
        
        # 分為兩組：需要下載完整內容的和只用摘要的
        df_full_content = df.head(self.max_full_content)
        df_summary_only = df.iloc[self.max_full_content:]
        
        # 並發下載完整內容
        if not df_full_content.empty:
            self._report_progress(f"🔄 並發下載 {len(df_full_content)} 篇新聞內容...")
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任務
                future_to_row = {
                    executor.submit(self._fetch_single_news_content, row, stock_code): idx 
                    for idx, row in df_full_content.iterrows()
                }
                
                # 收集結果
                completed = 0
                for future in as_completed(future_to_row):
                    completed += 1
                    try:
                        news_item = future.result()
                        if news_item:
                            news_list.append(news_item)
                        self._report_progress(f"📰 已完成 {completed}/{len(df_full_content)} 篇內容下載")
                    except Exception as e:
                        self.logger.warning(f"下載新聞內容失敗: {e}")
        
        # 快速處理摘要新聞
        if not df_summary_only.empty:
            self._report_progress(f"📝 快速處理 {len(df_summary_only)} 篇摘要新聞...")
            
            for idx, row in df_summary_only.iterrows():
                news_item = {
                    'search_time': row['search_time'],
                    'search_keyword': row['search_key'],
                    'title': row['title'],
                    'link': row['link'],
                    'pub_date': row['pub_date'].strftime('%Y-%m-%d'),
                    'description': row['description'],
                    'source': row['source'],
                    'news_url': row['link'],
                    'content': row['description'],  # 使用摘要作為內容
                    'stock_code': stock_code
                }
                news_list.append(news_item)
        
        self._report_progress(f"✅ 快速處理完成！共 {len(news_list)} 篇新聞")
        return news_list

    def _fetch_single_news_content(self, row, stock_code):
        """獲取單篇新聞內容"""
        try:
            # 使用簡化的內容獲取
            news_url, content = self._beautiful_soup_news_fast(row['link'])
            
            if not content or content == 'unknow domain':
                content = row['description']
                
            return {
                'search_time': row['search_time'],
                'search_keyword': row['search_key'],
                'title': row['title'],
                'link': row['link'],
                'pub_date': row['pub_date'].strftime('%Y-%m-%d'),
                'description': row['description'],
                'source': row['source'],
                'news_url': news_url,
                'content': content,
                'stock_code': stock_code
            }
            
        except Exception as e:
            self.logger.warning(f"獲取新聞內容失敗: {e}")
            # 返回基本資訊
            return {
                'search_time': row['search_time'],
                'search_keyword': row['search_key'],
                'title': row['title'],
                'link': row['link'],
                'pub_date': row['pub_date'].strftime('%Y-%m-%d'),
                'description': row['description'],
                'source': row['source'],
                'news_url': row['link'],
                'content': row['description'],
                'stock_code': stock_code
            }

    def _beautiful_soup_news_fast(self, url):
        """快速版本的新聞內容擷取"""
        # 設定headers
        headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36'
        }
        
        # 解析Google新聞重定向URL
        try:
            if 'news.google.com' in url:
                # 從Google新聞URL中提取真實URL
                if 'url=' in url:
                    import urllib.parse
                    parsed = urllib.parse.parse_qs(urllib.parse.urlparse(url).query)
                    if 'url' in parsed:
                        news_url = parsed['url'][0]
                    else:
                        news_url = url
                else:
                    news_url = url
            else:
                news_url = url
        except:
            news_url = url

        # 取得新聞內容
        try:
            response = requests.get(news_url, headers=headers, timeout=self.request_timeout)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
        except Exception as e:
            raise Exception(f"無法取得新聞內容: {e}")

        # 簡化的內容擷取 - 只處理主要媒體
        try:
            domain = re.findall('https://[^/]*', news_url)[0].replace('https://', '')
        except:
            return news_url, 'unknow domain'

        # 快速內容擷取
        content = ""
        try:
            if 'udn.com' in domain:
                items = soup.find_all('section', class_='article-content__editor')
                if items:
                    content = ' '.join([p.get_text() for p in items[0].find_all('p')])
            elif 'chinatimes.com' in domain:
                items = soup.find_all('div', class_='article-body')
                if items:
                    content = ' '.join([p.get_text() for p in items[0].find_all('p')])
            elif 'yahoo.com' in domain:
                items = soup.find_all('div', class_='caas-body')
                if items:
                    content = ' '.join([p.get_text() for p in items[0].find_all('p')])
            else:
                # 通用方法：尋找文章內容
                article = soup.find('article') or soup.find('div', class_=re.compile('content|article|body'))
                if article:
                    content = ' '.join([p.get_text() for p in article.find_all('p')])
                    
            if not content:
                content = 'unknow domain'
            else:
                # 清理內容
                content = content.replace('\r', ' ').replace('\n', ' ')
                content = re.sub(r'\s+', ' ', content).strip()
                # 限制長度
                if len(content) > 1000:
                    content = content[:1000] + "..."
                    
        except Exception as e:
            content = 'unknow domain'

        return news_url, content

    def save_to_database(self, news_list, db_path):
        """儲存新聞到資料庫"""
        if not news_list:
            self._report_progress("⚠️ 沒有新聞資料需要儲存")
            return

        with self.db_lock:
            conn = None
            try:
                self._report_progress(f"📂 連接資料庫: {db_path}")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # 創建表格
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        search_time TEXT,
                        search_keyword TEXT,
                        title TEXT,
                        link TEXT UNIQUE,
                        pub_date TEXT,
                        description TEXT,
                        source TEXT,
                        news_url TEXT,
                        content TEXT,
                        stock_code TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 插入資料
                saved_count = 0
                for news in news_list:
                    try:
                        cursor.execute('''
                            INSERT OR REPLACE INTO news
                            (search_time, search_keyword, title, link, pub_date, description, source, news_url, content, stock_code)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            news['search_time'], news['search_keyword'], news['title'],
                            news['link'], news['pub_date'], news['description'],
                            news['source'], news['news_url'], news['content'], news['stock_code']
                        ))
                        if cursor.rowcount > 0:
                            saved_count += 1
                    except Exception as e:
                        self.logger.warning(f"插入單筆新聞失敗: {e}")
                        continue

                conn.commit()
                self._report_progress(f"💾 已儲存 {saved_count} 筆新聞到資料庫 ({db_path})")

            except Exception as e:
                error_msg = f"儲存資料庫失敗: {e}"
                self.logger.error(error_msg)
                self._report_progress(f"❌ {error_msg}")
            finally:
                if conn:
                    conn.close()
