#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試單筆存檔功能
"""

import sys
import os
import types
from datetime import datetime
import urllib3
import pandas as pd

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_single_crawl_and_save():
    """測試單筆爬取和存檔"""
    print("🧪 測試單筆爬取和存檔...")
    
    try:
        from crawler import crawl_price, to_pickle
        
        # 測試一個日期
        test_date = datetime(2022, 9, 1)
        print(f"測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("\n1️⃣ 爬取資料...")
        data = crawl_price(test_date)
        
        if data is None or len(data) == 0:
            print("❌ 爬取失敗或無資料")
            return False
        
        print(f"✅ 成功爬取 {len(data)} 筆資料")
        print(f"   資料欄位: {list(data.columns)[:5]}...")
        print(f"   索引層級: {data.index.names}")
        
        print("\n2️⃣ 測試存檔...")
        
        # 檢查存檔前的檔案狀態
        price_file = "history/tables/price.pkl"
        if os.path.exists(price_file):
            old_size = os.path.getsize(price_file)
            old_time = os.path.getmtime(price_file)
            print(f"   存檔前: {old_size} bytes, 修改時間: {datetime.fromtimestamp(old_time)}")
        else:
            print("   存檔前: 檔案不存在")
            old_size = 0
            old_time = 0
        
        # 執行存檔
        to_pickle(data, 'price')
        
        # 檢查存檔後的檔案狀態
        if os.path.exists(price_file):
            new_size = os.path.getsize(price_file)
            new_time = os.path.getmtime(price_file)
            print(f"   存檔後: {new_size} bytes, 修改時間: {datetime.fromtimestamp(new_time)}")
            
            if new_time > old_time:
                print("✅ 檔案確實有更新")
                
                # 驗證檔案內容
                try:
                    saved_data = pd.read_pickle(price_file)
                    print(f"✅ 檔案可以正常讀取，共 {len(saved_data)} 筆資料")
                    
                    # 檢查最新日期
                    latest_date = saved_data.index.get_level_values('date').max()
                    print(f"   最新日期: {latest_date}")
                    
                    return True
                except Exception as e:
                    print(f"❌ 檔案讀取失敗: {str(e)}")
                    return False
            else:
                print("❌ 檔案沒有更新")
                return False
        else:
            print("❌ 存檔後檔案仍不存在")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_batch_count_logic():
    """測試批次計數邏輯"""
    print("\n🧪 測試批次計數邏輯...")
    
    # 模擬批次計數
    batch_size = 10
    batch_count = 0
    
    print("模擬處理 15 筆資料:")
    for i in range(1, 16):
        batch_count += 1
        print(f"   第 {i} 筆, batch_count = {batch_count}")
        
        if batch_count >= batch_size:
            print(f"   → 觸發存檔 (第 {i} 筆)")
            batch_count = 0  # 重置計數
            print(f"   → 重置 batch_count = {batch_count}")
    
    print("✅ 批次計數邏輯正常")

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 單筆存檔測試")
    print("=" * 60)
    
    # 測試1: 批次計數邏輯
    test_batch_count_logic()
    
    # 測試2: 實際爬取和存檔
    success = test_single_crawl_and_save()
    
    print("\n" + "=" * 60)
    print("📊 測試結果")
    print("=" * 60)
    
    if success:
        print("✅ 單筆存檔功能正常")
        print("🔍 如果您的程式每1筆就存檔，可能的原因:")
        print("   1. to_pickle 函數執行時出錯")
        print("   2. batch_count 沒有正確重置")
        print("   3. df 沒有正確清空")
    else:
        print("❌ 存檔功能有問題")
        print("🔧 需要進一步檢查 to_pickle 函數")
    
    return success

if __name__ == "__main__":
    main()
