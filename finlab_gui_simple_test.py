#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版 Finlab GUI 測試
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
import pandas as pd
import os

class SimpleFinlabGUI:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        self.check_pe_data()
    
    def setup_ui(self):
        """設置界面"""
        self.root.title("簡化版 Finlab GUI 測試")
        self.root.geometry("1000x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 標題
        title = ttk.Label(main_frame, text="PE.PKL 測試界面", font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # PE資料狀態
        self.status_frame = ttk.LabelFrame(main_frame, text="PE資料狀態", padding="10")
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="檢查中...")
        self.status_label.pack()
        
        # 按鈕區域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.test_btn = ttk.Button(btn_frame, text="測試PE更新", command=self.test_pe_update)
        self.test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_btn = ttk.Button(btn_frame, text="清除日誌", command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT)
        
        # 日誌區域 - 使用最簡單的方式
        log_frame = ttk.LabelFrame(main_frame, text="執行日誌", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 創建帶捲軸的文本框
        text_container = ttk.Frame(log_frame)
        text_container.pack(fill=tk.BOTH, expand=True)
        
        # 文本框
        self.log_text = tk.Text(text_container, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 垂直捲軸
        scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 初始日誌
        self.add_log("簡化版 Finlab GUI 已啟動")
    
    def add_log(self, message):
        """添加日誌 - 最簡單的方式"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()  # 立即更新界面
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("日誌已清除")
    
    def check_pe_data(self):
        """檢查PE資料"""
        self.add_log("🔍 檢查PE資料檔案...")
        
        pe_file = 'pe.pkl'
        if os.path.exists(pe_file):
            try:
                data = pd.read_pickle(pe_file)
                
                # 獲取資料資訊
                data_count = len(data)
                
                if hasattr(data, 'index') and len(data.index) > 0:
                    if hasattr(data.index, 'levels') and len(data.index.levels) > 1:
                        # MultiIndex
                        dates = data.index.get_level_values(0)
                        last_date = dates.max().strftime('%Y-%m-%d')
                        first_date = dates.min().strftime('%Y-%m-%d')
                    else:
                        # 單層索引
                        last_date = data.index.max().strftime('%Y-%m-%d')
                        first_date = data.index.min().strftime('%Y-%m-%d')
                    
                    status_text = f"✅ PE資料檔案存在\n資料筆數: {data_count}\n日期範圍: {first_date} 至 {last_date}"
                    self.add_log(f"✅ PE資料檔案讀取成功")
                    self.add_log(f"   資料筆數: {data_count}")
                    self.add_log(f"   日期範圍: {first_date} 至 {last_date}")
                else:
                    status_text = f"⚠️ PE資料檔案存在但無法獲取日期資訊\n資料筆數: {data_count}"
                    self.add_log(f"⚠️ PE資料檔案存在但無法獲取日期資訊")
                
            except Exception as e:
                status_text = f"❌ PE資料檔案讀取失敗: {str(e)}"
                self.add_log(f"❌ PE資料檔案讀取失敗: {str(e)}")
        else:
            status_text = "❌ PE資料檔案不存在"
            self.add_log("❌ PE資料檔案不存在")
        
        self.status_label.config(text=status_text)
    
    def test_pe_update(self):
        """測試PE更新功能"""
        self.add_log("🧪 開始測試PE更新功能...")
        
        try:
            # 測試爬蟲模組導入
            self.add_log("🔍 測試爬蟲模組導入...")
            
            import sys
            ai_finlab_path = os.path.join(os.getcwd(), 'AI_finlab')
            if ai_finlab_path not in sys.path:
                sys.path.insert(0, ai_finlab_path)
            
            from crawler import crawl_pe
            self.add_log("✅ crawl_pe 函數導入成功")
            
            # 測試函數調用（使用datetime對象）
            from datetime import datetime
            test_date = datetime(2025, 7, 18)
            
            self.add_log(f"🔄 測試爬取 {test_date.strftime('%Y-%m-%d')} 的資料...")
            
            # 這裡會因為網路問題失敗，但我們可以看到錯誤訊息
            try:
                result = crawl_pe(test_date)
                if result is not None and not result.empty:
                    self.add_log(f"✅ 爬取成功，獲取 {len(result)} 筆資料")
                else:
                    self.add_log("⚠️ 爬取成功但無資料（可能是非交易日）")
            except Exception as crawl_error:
                self.add_log(f"❌ 爬取失敗: {str(crawl_error)}")
                self.add_log("   這通常是因為網路限制或IP被封鎖")
            
        except ImportError as e:
            self.add_log(f"❌ 爬蟲模組導入失敗: {e}")
        except Exception as e:
            self.add_log(f"❌ 測試過程發生錯誤: {e}")
        
        self.add_log("🏁 測試完成")

def main():
    root = tk.Tk()
    app = SimpleFinlabGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
