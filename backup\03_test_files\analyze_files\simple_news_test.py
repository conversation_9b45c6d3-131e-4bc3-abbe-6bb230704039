#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的新聞爬蟲測試
"""

import sqlite3
import os

def check_news_database():
    """檢查新聞資料庫"""
    print("🔍 檢查新聞資料庫...")
    
    # 檢查當前目錄的news.db
    if os.path.exists("news.db"):
        print("✅ 找到 news.db")
        
        try:
            conn = sqlite3.connect("news.db")
            cursor = conn.cursor()
            
            # 檢查表格結構
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📊 資料庫表格: {[t[0] for t in tables]}")
            
            # 檢查是否有news表格
            if any('news' in str(t) for t in tables):
                # 檢查6667的新聞
                cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = '6667'")
                count = cursor.fetchone()[0]
                print(f"📰 6667新聞數量: {count}")
                
                if count > 0:
                    # 顯示最新的新聞
                    cursor.execute("""
                        SELECT title, source, pub_date, created_at 
                        FROM news 
                        WHERE stock_code = '6667' 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    """)
                    news_list = cursor.fetchall()
                    
                    print("\n📋 最新3筆6667新聞:")
                    for i, (title, source, pub_date, created_at) in enumerate(news_list):
                        print(f"{i+1}. {title}")
                        print(f"   來源: {source}")
                        print(f"   發布: {pub_date}")
                        print(f"   儲存: {created_at}")
                        print()
                else:
                    print("❌ 資料庫中沒有6667的新聞")
            else:
                print("❌ 資料庫中沒有news表格")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 讀取資料庫失敗: {e}")
    else:
        print("❌ 找不到 news.db")

def create_test_news():
    """創建測試新聞資料"""
    print("\n🔧 創建測試新聞資料...")
    
    try:
        conn = sqlite3.connect("test_news.db")
        cursor = conn.cursor()
        
        # 創建表格
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS news (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                search_time TEXT,
                search_keyword TEXT,
                title TEXT,
                link TEXT UNIQUE,
                pub_date TEXT,
                description TEXT,
                source TEXT,
                news_url TEXT,
                content TEXT,
                stock_code TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入測試資料
        test_news = [
            ('20250718120000', '6667信紘科', '信紘科技股價創新高', 'http://test1.com', '2025-07-18', '信紘科技今日股價表現亮眼', '測試來源1', 'http://test1.com', '測試內容1', '6667'),
            ('20250718120001', '6667信紘科', '信紘科技獲利成長', 'http://test2.com', '2025-07-18', '信紘科技第二季獲利成長', '測試來源2', 'http://test2.com', '測試內容2', '6667'),
            ('20250718120002', '6667信紘科', '信紘科技展望樂觀', 'http://test3.com', '2025-07-18', '信紘科技對未來展望樂觀', '測試來源3', 'http://test3.com', '測試內容3', '6667')
        ]
        
        for news in test_news:
            cursor.execute('''
                INSERT OR REPLACE INTO news 
                (search_time, search_keyword, title, link, pub_date, description, source, news_url, content, stock_code)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', news)
        
        conn.commit()
        conn.close()
        
        print("✅ 測試新聞資料創建成功")
        
        # 驗證
        conn = sqlite3.connect("test_news.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM news WHERE stock_code = '6667'")
        count = cursor.fetchone()[0]
        print(f"📊 測試資料庫中有 {count} 筆6667新聞")
        conn.close()
        
    except Exception as e:
        print(f"❌ 創建測試資料失敗: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("新聞資料庫檢查工具")
    print("=" * 50)
    
    check_news_database()
    create_test_news()
    
    print("\n" + "=" * 50)
    print("檢查完成")
    print("=" * 50)
