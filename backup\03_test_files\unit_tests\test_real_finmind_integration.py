#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試真實FinMind API與高殖利率烏龜策略的整合
使用真實API數據進行測試
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sqlite3

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_realistic_stock_data():
    """創建更真實的股價數據（基於台積電歷史走勢）"""
    dates = pd.date_range(start='2024-01-01', end='2024-12-10', freq='D')
    dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
    
    # 模擬台積電的價格走勢
    np.random.seed(42)
    base_price = 600  # 台積電價格範圍
    prices = []
    volumes = []
    
    for i, date in enumerate(dates):
        # 模擬真實的價格波動
        if i == 0:
            price = base_price
        else:
            # 加入一些趨勢和隨機性
            trend = 0.0002 if i < len(dates) * 0.6 else -0.0001  # 前期上漲，後期調整
            volatility = np.random.normal(0, 0.025)  # 2.5%波動率
            price = prices[-1] * (1 + trend + volatility)
        
        prices.append(max(price, 100))  # 最低100元
        
        # 模擬成交量
        base_volume = 50000000  # 5萬張
        volume_multiplier = np.random.uniform(0.5, 2.0)
        volumes.append(int(base_volume * volume_multiplier))
    
    # 創建OHLC數據
    df_data = []
    for i, (date, close, volume) in enumerate(zip(dates, prices, volumes)):
        high = close * np.random.uniform(1.0, 1.03)
        low = close * np.random.uniform(0.97, 1.0)
        open_price = np.random.uniform(low, high)
        
        df_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': volume
        })
    
    return pd.DataFrame(df_data)

def test_real_finmind_api():
    """測試真實FinMind API數據獲取"""
    print("🧪 測試真實FinMind API數據獲取")
    print("=" * 50)
    
    try:
        from finmind_data_provider import FinMindDataProvider
        
        # 創建數據提供器
        provider = FinMindDataProvider()
        
        # 測試股票
        test_stock = "2330"  # 台積電
        
        print(f"📊 測試股票: {test_stock} (台積電)")
        
        # 檢查API配額
        usage = provider.get_api_usage_stats()
        print(f"📈 API配額: {usage.get('hourly_usage', 0)}/{usage.get('hourly_limit', 600)}")
        
        if not usage.get('can_make_request', False):
            print("⚠️ API配額不足，跳過真實API測試")
            return None
        
        # 獲取股利數據
        print("\n💰 獲取股利數據...")
        dividend_data = provider.get_dividend_data(test_stock)
        if dividend_data:
            print(f"  ✅ 現金股利: {dividend_data.get('cash_dividend', 0)} 元")
            print(f"  ✅ 除息日: {dividend_data.get('ex_dividend_date', 'N/A')}")
            print(f"  ✅ 數據來源: {'真實' if not dividend_data.get('is_simulated', True) else '模擬'}")
        else:
            print("  ❌ 無法獲取股利數據")
        
        # 獲取本益比數據
        print("\n📊 獲取本益比數據...")
        per_data = provider.get_per_data(test_stock, days=7)
        if per_data:
            print(f"  ✅ 本益比: {per_data.get('pe_ratio', 0):.2f}")
            print(f"  ✅ 股價: {per_data.get('price', 0):.2f}")
            print(f"  ✅ 數據來源: {'真實' if not per_data.get('is_simulated', True) else '模擬'}")
        else:
            print("  ❌ 無法獲取本益比數據")
        
        # 獲取營收數據
        print("\n💹 獲取營收數據...")
        revenue_data = provider.get_revenue_data(test_stock, months=6)
        if revenue_data:
            print(f"  ✅ 營收: {revenue_data.get('revenue', 0):,} 千元")
            print(f"  ✅ 月成長率: {revenue_data.get('revenue_growth_rate', 0):.2f}%")
            print(f"  ✅ 年成長率: {revenue_data.get('yoy_growth_rate', 0):.2f}%")
            print(f"  ✅ 數據來源: {'真實' if not revenue_data.get('is_simulated', True) else '模擬'}")
        else:
            print("  ❌ 無法獲取營收數據")
        
        # 獲取財務數據
        print("\n📋 獲取財務數據...")
        financial_data = provider.get_financial_statements(test_stock)
        if financial_data:
            print(f"  ✅ 總資產: {financial_data.get('total_assets', 0):,}")
            print(f"  ✅ 淨利: {financial_data.get('net_income', 0):,}")
            print(f"  ✅ EPS: {financial_data.get('eps', 0)}")
            print(f"  ✅ ROE: {financial_data.get('roe', 0):.2f}%")
            print(f"  ✅ 數據來源: {'真實' if not financial_data.get('is_simulated', True) else '模擬'}")
        else:
            print("  ❌ 無法獲取財務數據")
        
        # 組合數據
        finmind_data = {
            'dividend_data': dividend_data,
            'revenue_data': revenue_data,
            'financial_data': financial_data
        }
        
        # 檢查最終API使用
        final_usage = provider.get_api_usage_stats()
        api_used = final_usage.get('hourly_usage', 0) - usage.get('hourly_usage', 0)
        print(f"\n📊 API使用統計:")
        print(f"  本次使用: {api_used} 次")
        print(f"  剩餘額度: {final_usage.get('hourly_remaining', 0)} 次")
        
        return finmind_data
        
    except Exception as e:
        print(f"❌ 真實API測試失敗: {e}")
        return None

def test_turtle_with_real_api():
    """測試高殖利率烏龜策略使用真實API數據"""
    print("\n🐢 測試高殖利率烏龜策略 - 真實API數據")
    print("=" * 60)
    
    try:
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        # 創建策略實例
        turtle = HighYieldTurtleStrategy()
        
        # 創建股價數據
        df = create_realistic_stock_data()
        
        # 獲取真實FinMind數據
        finmind_data = test_real_finmind_api()
        
        if not finmind_data:
            print("❌ 無法獲取FinMind數據，跳過策略測試")
            return None
        
        print(f"\n📊 策略分析:")
        print(f"  股價數據: {len(df)} 天")
        print(f"  最新價格: {df['Close'].iloc[-1]:.2f}")
        
        # 執行策略分析
        result = turtle.analyze_stock(df, finmind_data=finmind_data, stock_id='2330')
        
        print(f"\n📈 分析結果:")
        print(f"  是否符合: {result['suitable']}")
        print(f"  總分: {result['score']}/100")
        print(f"  真實數據比例: {result.get('real_data_ratio', 0)*100:.0f}%")
        print(f"  原因: {result['reason']}")
        
        # 詳細分析
        print(f"\n📋 詳細分析:")
        details = result.get('details', {})
        for key, value in details.items():
            status = "✅" if value['score'] > 0 else "❌"
            print(f"  {status} {key}: {value['reason']} ({value['score']}分)")
        
        # 計算真實殖利率
        if finmind_data.get('dividend_data'):
            dividend_data = finmind_data['dividend_data']
            if not dividend_data.get('is_simulated', True):
                current_price = df['Close'].iloc[-1]
                cash_dividend = dividend_data.get('cash_dividend', 0)
                if current_price > 0 and cash_dividend > 0:
                    real_yield = (cash_dividend / current_price) * 100
                    print(f"\n💰 真實殖利率計算:")
                    print(f"  現金股利: {cash_dividend} 元")
                    print(f"  模擬股價: {current_price:.2f} 元")
                    print(f"  殖利率: {real_yield:.2f}%")
        
        return result
        
    except Exception as e:
        print(f"❌ 策略測試失敗: {e}")
        return None

def test_main_program_integration():
    """測試主程序整合"""
    print("\n🔧 測試主程序整合")
    print("=" * 50)
    
    try:
        # 模擬主程序的調用方式
        print("📋 模擬主程序調用高殖利率烏龜策略...")
        
        # 創建測試數據
        df = create_realistic_stock_data()
        
        # 模擬主程序的check_high_yield_turtle_strategy調用
        # 這裡我們直接調用策略類，因為主程序的方法需要完整的GUI環境
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        turtle = HighYieldTurtleStrategy()
        
        # 不使用FinMind數據的測試
        result_no_api = turtle.analyze_stock(df)
        print(f"  無API數據結果: {result_no_api['suitable']} (分數: {result_no_api['score']})")
        
        # 使用模擬FinMind數據的測試
        mock_finmind = {
            'dividend_data': {'cash_dividend': 2.0, 'is_simulated': True},
            'revenue_data': {'revenue_growth_rate': 8.0, 'is_simulated': True},
            'financial_data': {'net_income': 100000, 'revenue': 500000, 'is_simulated': True}
        }
        result_mock_api = turtle.analyze_stock(df, finmind_data=mock_finmind)
        print(f"  模擬API數據結果: {result_mock_api['suitable']} (分數: {result_mock_api['score']})")
        
        print("✅ 主程序整合測試完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序整合測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🎯 高殖利率烏龜策略真實FinMind API整合測試")
    print("=" * 70)
    
    # 1. 測試真實API數據獲取
    api_result = test_real_finmind_api()
    
    # 2. 測試策略與真實API的整合
    if api_result:
        strategy_result = test_turtle_with_real_api()
    else:
        print("⚠️ 跳過策略測試（API數據獲取失敗）")
        strategy_result = None
    
    # 3. 測試主程序整合
    integration_result = test_main_program_integration()
    
    # 總結
    print(f"\n🎉 測試總結")
    print("=" * 30)
    
    if api_result:
        print("✅ FinMind API數據獲取: 成功")
    else:
        print("❌ FinMind API數據獲取: 失敗")
    
    if strategy_result:
        real_ratio = strategy_result.get('real_data_ratio', 0)
        print(f"✅ 策略真實數據整合: 成功 ({real_ratio*100:.0f}%真實數據)")
    else:
        print("❌ 策略真實數據整合: 失敗")
    
    if integration_result:
        print("✅ 主程序整合: 成功")
    else:
        print("❌ 主程序整合: 失敗")
    
    # 最終建議
    if api_result and strategy_result and integration_result:
        print(f"\n🎉 恭喜！高殖利率烏龜策略已成功整合FinMind API")
        print(f"📊 策略現在可以使用真實的股利、營收、財務數據")
        print(f"🚀 建議在主程序中啟用此策略進行實際選股測試")
    else:
        print(f"\n⚠️ 整合過程中遇到問題，建議檢查:")
        print(f"   - FinMind API Token是否正確設定")
        print(f"   - 網路連接是否正常")
        print(f"   - API配額是否充足")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
