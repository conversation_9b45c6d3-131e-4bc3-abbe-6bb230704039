#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試阿水一式策略的調試輸出
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data():
    """創建測試用的股票數據"""
    print("📊 創建測試數據...")
    
    # 創建150天的測試數據
    dates = pd.date_range(start='2024-01-01', periods=150, freq='D')
    
    # 模擬股票數據
    np.random.seed(42)  # 確保結果可重現
    
    base_price = 100
    prices = [base_price]
    volumes = []
    
    # 先生成第一天的成交量
    volumes.append(np.random.normal(100000, 20000))

    for i in range(1, 150):
        # 前100天：盤整
        if i < 100:
            change = np.random.normal(0, 0.5)
            volume = np.random.normal(100000, 20000)
        # 100-140天：壓縮
        elif i < 140:
            change = np.random.normal(0, 0.2)
            volume = np.random.normal(60000, 10000)
        # 最後10天：突破
        else:
            change = np.random.normal(1.0, 0.3)
            volume = np.random.normal(300000, 50000)

        new_price = max(prices[-1] + change, 10)
        prices.append(new_price)
        volumes.append(max(volume, 10000))
    
    # 最後一天加入大量突破
    volumes[-1] = 500000
    prices[-1] = prices[-2] * 1.05  # 5%突破
    
    df = pd.DataFrame({
        'date': dates,
        'Open': [p * 0.99 for p in prices],
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': volumes
    })
    
    return df

def test_ashui_calculation():
    """測試阿水一式計算邏輯"""
    print("🧮 測試阿水一式計算邏輯...")
    
    df = create_test_data()
    
    if len(df) < 20:
        print("❌ 數據不足")
        return False
    
    latest = df.iloc[-1]
    close_price = latest['Close']
    
    print(f"📈 最新收盤價: {close_price:.2f}")
    print(f"📊 最新成交量: {latest['Volume']:,.0f}")
    
    # 計算技術指標
    try:
        # 成交金額
        volume = latest['Volume']
        turnover = close_price * volume / 10000
        print(f"💰 成交金額: {turnover:.0f}萬")
        
        # 布林帶
        ma20 = df['Close'].rolling(window=20).mean().iloc[-1]
        std20 = df['Close'].rolling(window=20).std().iloc[-1]
        
        if ma20 > 0 and std20 > 0:
            bb_upper = ma20 + (std20 * 2.1)
            bb_lower = ma20 - (std20 * 2.1)
            bb_width = (bb_upper - bb_lower) / ma20
            print(f"📏 布林帶寬: {bb_width:.4f}")
            
            # 突破幅度
            breakout_pct = 0
            if close_price > bb_upper:
                breakout_pct = ((close_price - bb_upper) / bb_upper) * 100
                print(f"🚀 突破幅度: {breakout_pct:.2f}%")
        
        # 成交量比例
        volume_ma5 = df['Volume'].rolling(window=5).mean().iloc[-1]
        volume_ratio = volume / volume_ma5 if volume_ma5 > 0 else 0
        print(f"📈 成交量比例: {volume_ratio:.2f}")
        
        # 壓縮天數
        bb_widths = []
        for i in range(len(df) - 19, len(df)):
            if i >= 19:
                ma = df['Close'].iloc[i-19:i+1].mean()
                std = df['Close'].iloc[i-19:i+1].std()
                if ma > 0 and std > 0:
                    width = (2 * 2.1 * std) / ma
                    bb_widths.append(width)
        
        compression_days = 0
        if len(bb_widths) >= 2:
            for i in range(len(bb_widths) - 1, 0, -1):
                if bb_widths[i] < bb_widths[i-1]:
                    compression_days += 1
                else:
                    break
        
        print(f"📉 壓縮天數: {compression_days}")
        
        # 評分邏輯
        score = 50  # 基礎分數
        
        if turnover > 1000:  # 成交金額超過1000萬
            score += 10
        if bb_width < 0.1:  # 布林帶收縮
            score += 10
        if volume_ratio > 2:  # 成交量放大
            score += 15
        if compression_days >= 5:  # 壓縮天數足夠
            score += 10
        if breakout_pct > 0:  # 有突破
            score += 5
        
        print(f"⭐ 阿水一式評分: {score}")
        
        return True
        
    except Exception as e:
        print(f"❌ 計算失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_gui_call():
    """模擬GUI調用阿水一式策略"""
    print("🖥️ 模擬GUI調用...")
    
    # 模擬結果數據
    test_results = [
        {
            'stock_id': '1608',
            'stock_name': '華榮',
            'Close': 27.15,
            'Volume': 150000,
            'message': '✅ 符合阿水一式條件'
        },
        {
            'stock_id': '1618',
            'stock_name': '合機',
            'Close': 51.60,
            'Volume': 200000,
            'message': '✅ 符合阿水一式條件'
        }
    ]
    
    matching_stocks = ['1608', '1618']
    
    print("📋 模擬表格設置...")
    for result in test_results:
        stock_id = result['stock_id']
        close_price = result['Close']
        
        print(f"\n🔍 處理股票: {stock_id} - {result['stock_name']}")
        print(f"💰 收盤價: {close_price}")
        
        # 模擬計算過程
        if stock_id in matching_stocks:
            # 簡化計算
            volume = result.get('Volume', 0)
            if volume > 0:
                turnover = close_price * volume / 10000
                print(f"💰 成交金額: {turnover:.0f}萬")
                
                # 模擬其他指標
                bb_width = 0.08  # 模擬布林帶寬
                compression_days = 7  # 模擬壓縮天數
                volume_ratio = 2.5  # 模擬成交量比例
                breakout_pct = 3.2  # 模擬突破幅度
                ashui_score = 85  # 模擬評分
                
                print(f"📏 布林帶寬: {bb_width:.4f}")
                print(f"📉 壓縮天數: {compression_days}")
                print(f"📈 成交量比例: {volume_ratio:.2f}")
                print(f"🚀 突破幅度: {breakout_pct:.2f}%")
                print(f"⭐ 評分: {ashui_score}")
                print("✅ 計算成功")
            else:
                print("❌ 缺少成交量數據")
        else:
            print("⚠️ 不在符合條件列表中")

def main():
    """主函數"""
    print("=" * 60)
    print("🧪 阿水一式策略調試測試")
    print("=" * 60)
    
    # 測試計算邏輯
    calc_success = test_ashui_calculation()
    
    print("\n" + "=" * 60)
    
    # 模擬GUI調用
    simulate_gui_call()
    
    print("\n" + "=" * 60)
    print("📝 測試總結:")
    if calc_success:
        print("✅ 計算邏輯測試通過")
    else:
        print("❌ 計算邏輯測試失敗")
    
    print("✅ GUI模擬測試完成")
    print("\n🎯 建議:")
    print("1. 檢查實際程序中的調試輸出")
    print("2. 確認數據庫連接是否正常")
    print("3. 驗證 fetch_strategy_data 方法")
    print("4. 如果仍有問題，考慮使用備用計算邏輯")

if __name__ == "__main__":
    main()
