#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財報爬蟲函數
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_finance_crawler():
    """測試財報爬蟲函數"""
    print("🔧 測試財報爬蟲函數")
    print("=" * 60)
    
    try:
        from crawler import crawl_finance_statement_by_date
        
        # 測試一個應該有資料的日期
        test_date = datetime.datetime(2023, 5, 15)  # 2023年Q1財報
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"🔄 開始測試財報爬蟲...")
        
        result = crawl_finance_statement_by_date(test_date)
        
        if result is not None and not result.empty:
            print(f"✅ 財報爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位數: {len(result.columns)}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(2))
            
            return True
        else:
            print(f"❌ 財報爬蟲無資料")
            return False
            
    except ImportError as e:
        print(f"❌ 無法導入財報爬蟲: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 財報爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def test_multiple_dates():
    """測試多個日期"""
    print(f"\n🔧 測試多個財報日期")
    print("=" * 60)
    
    test_dates = [
        datetime.datetime(2023, 3, 31),  # 2022年Q4
        datetime.datetime(2023, 5, 15),  # 2023年Q1
        datetime.datetime(2023, 8, 14),  # 2023年Q2
        datetime.datetime(2023, 11, 14), # 2023年Q3
    ]
    
    try:
        from crawler import crawl_finance_statement_by_date
        
        results = []
        
        for i, test_date in enumerate(test_dates, 1):
            print(f"\n📅 測試 {i}: {test_date.strftime('%Y-%m-%d')}")
            
            try:
                result = crawl_finance_statement_by_date(test_date)
                
                if result is not None and not result.empty:
                    print(f"   ✅ 成功: {len(result)} 筆資料")
                    results.append((test_date, len(result)))
                else:
                    print(f"   ❌ 無資料")
                    results.append((test_date, 0))
                    
            except Exception as e:
                print(f"   ❌ 失敗: {str(e)[:50]}...")
                results.append((test_date, -1))
        
        print(f"\n📊 測試結果總結:")
        success_count = 0
        for date, count in results:
            if count > 0:
                status = f"✅ {count} 筆"
                success_count += 1
            elif count == 0:
                status = "❌ 無資料"
            else:
                status = "❌ 錯誤"
            
            print(f"   {date.strftime('%Y-%m-%d')}: {status}")
        
        print(f"\n🎯 成功率: {success_count}/{len(test_dates)} ({success_count/len(test_dates)*100:.1f}%)")
        
        return success_count > 0
        
    except ImportError as e:
        print(f"❌ 無法導入財報爬蟲: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 財報爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 確認財報爬蟲函數是否正常工作")
    print("=" * 60)
    
    # 測試1: 單一日期
    single_success = test_finance_crawler()
    
    # 測試2: 多個日期
    multiple_success = test_multiple_dates()
    
    print(f"\n📊 最終結論:")
    if single_success or multiple_success:
        print(f"✅ 財報爬蟲基本可以工作")
        print(f"💡 auto_update.py 失敗可能是其他原因")
    else:
        print(f"❌ 財報爬蟲完全不工作")
        print(f"💡 這解釋了為什麼 auto_update.py 沒有新資料")

if __name__ == "__main__":
    main()
