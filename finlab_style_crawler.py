#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於 FinLab 超簡單方法的改進版月營收爬蟲
支援新網站 + 國內外公司 + 上市上櫃
"""

import pandas as pd
import requests
from io import StringIO
import time
import datetime
import random
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def monthly_report_finlab_style(year, month, market='sii', foreign=False):
    """
    基於 FinLab 方法的月營收爬蟲 (改進版)
    
    Args:
        year: 年份 (西元年或民國年)
        month: 月份
        market: 'sii' (上市) 或 'otc' (上櫃)
        foreign: False=國內公司(_0), True=國外公司(_1)
    
    Returns:
        pandas.DataFrame: 月營收資料
    """
    # 假如是西元，轉成民國
    if year > 1990:
        year -= 1911
    
    # 決定後綴
    suffix = '1' if foreign else '0'
    type_name = '國外' if foreign else '國內'
    market_name = '上市' if market == 'sii' else '上櫃'
    
    print(f"🔄 爬取{type_name}{market_name} (民國{year}年{month}月)")
    
    # 構建 URL - 優先使用新網站
    new_url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}_{suffix}.html'
    old_url = f'https://mops.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}_{suffix}.html'
    
    # 對於較舊的年份，使用舊格式
    if year <= 98:
        new_url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}.html'
        old_url = f'https://mops.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}.html'
    
    # 偽瀏覽器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 嘗試新網站
    for attempt, url in enumerate([new_url, old_url], 1):
        try:
            print(f"   📄 嘗試 {attempt}: {url}")
            
            # 下載該年月的網站，並用pandas轉換成 dataframe
            r = requests.get(url, headers=headers, timeout=30, verify=False)
            
            if r.status_code != 200:
                print(f"   ❌ HTTP錯誤: {r.status_code}")
                continue
            
            # 檢查是否被封鎖
            if '您的網頁IP已經被證交所封鎖' in r.text:
                print(f"   ⚠️ IP被封鎖")
                continue
            
            r.encoding = 'big5'
            
            # 使用 FinLab 的方法解析表格
            dfs = pd.read_html(StringIO(r.text), encoding='big-5')
            print(f"   📊 找到 {len(dfs)} 個表格")
            
            if not dfs:
                print(f"   ⚠️ 無表格資料")
                continue
            
            # FinLab 的表格篩選邏輯
            valid_dfs = [df for df in dfs if df.shape[1] <= 11 and df.shape[1] > 5]
            
            if not valid_dfs:
                print(f"   ⚠️ 無有效表格")
                continue
            
            df = pd.concat(valid_dfs)
            print(f"   📋 合併後表格形狀: {df.shape}")
            
            # FinLab 的欄位處理邏輯
            if 'levels' in dir(df.columns):
                df.columns = df.columns.get_level_values(1)
                df.columns = df.columns.str.replace(' ', '')
            else:
                df = df[list(range(0, min(10, len(df.columns))))]
                # 尋找包含 '公司代號' 的行作為欄位名稱
                column_indices = df.index[df.iloc[:, 0] == '公司代號']
                if len(column_indices) > 0:
                    column_index = column_indices[0]
                    df.columns = df.iloc[column_index]
                    df = df.drop(df.index[column_index])
            
            # 清理資料
            df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
            df = df[df.iloc[:, 0] != '公司代號']  # 移除重複的表頭行
            
            # 確保有必要的欄位
            if '當月營收' not in df.columns or '公司代號' not in df.columns:
                print(f"   ⚠️ 缺少必要欄位")
                continue
            
            # FinLab 的數值處理
            df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
            df = df[~df['當月營收'].isnull()]
            df = df[df['公司代號'] != '合計']
            df = df[df['公司代號'].notna()]
            
            # 處理其他數值欄位
            numeric_columns = ['上月營收', '去年當月營收', '上月比較增減(%)', '去年同月增減(%)']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            print(f"   ✅ 成功處理 {len(df)} 筆資料")
            
            # 偽停頓 (FinLab 風格)
            time.sleep(random.uniform(3, 6))
            
            return df
            
        except Exception as e:
            print(f"   ❌ 嘗試 {attempt} 失敗: {str(e)[:50]}...")
            continue
    
    print(f"   ❌ 所有嘗試都失敗")
    return pd.DataFrame()

def get_all_monthly_report_finlab_style(year, month):
    """
    獲取所有月營收資料 (上市+上櫃，國內+國外)
    
    Args:
        year: 年份 (西元年或民國年)
        month: 月份
    
    Returns:
        pandas.DataFrame: 完整的月營收資料
    """
    print(f"🚀 開始爬取完整月營收 (FinLab風格) - {year}年{month}月")
    
    all_data = []
    
    # 爬取順序: 上市國內 -> 上市國外 -> 上櫃國內 -> 上櫃國外
    tasks = [
        ('sii', False, '上市國內'),
        ('sii', True, '上市國外'),
        ('otc', False, '上櫃國內'),
        ('otc', True, '上櫃國外')
    ]
    
    for market, foreign, desc in tasks:
        print(f"\n📊 {desc}")
        df = monthly_report_finlab_style(year, month, market, foreign)
        
        if len(df) > 0:
            # 添加標識欄位
            df['市場'] = '上市' if market == 'sii' else '上櫃'
            df['類型'] = '國外' if foreign else '國內'
            all_data.append(df)
            print(f"   ✅ {desc}: {len(df)} 筆")
        else:
            print(f"   ⚠️ {desc}: 無資料")
    
    # 合併所有資料
    if all_data:
        final_df = pd.concat(all_data, ignore_index=True)
        
        # 創建股票ID (FinLab風格)
        if '公司名稱' in final_df.columns:
            final_df['stock_id'] = final_df['公司代號'].astype(str) + ' ' + final_df['公司名稱'].astype(str)
        else:
            final_df['stock_id'] = final_df['公司代號'].astype(str)
        
        # 添加日期
        if year > 1990:
            date_year = year
        else:
            date_year = year + 1911
        
        final_df['date'] = pd.to_datetime(f'{date_year}-{month:02d}-01')
        
        print(f"\n🎉 完整月營收爬取成功: {len(final_df)} 筆資料")
        print(f"   📊 統計:")
        if '市場' in final_df.columns and '類型' in final_df.columns:
            summary = final_df.groupby(['市場', '類型']).size()
            for (market, type_), count in summary.items():
                print(f"     {market}{type_}: {count} 筆")
        
        return final_df
    else:
        print(f"\n❌ 完整月營收爬取失敗")
        return pd.DataFrame()

def test_finlab_style():
    """測試 FinLab 風格爬蟲"""
    print("🧪 測試 FinLab 風格月營收爬蟲")
    print("=" * 60)
    
    # 測試 2024年11月 (西元年)
    result = get_all_monthly_report_finlab_style(2024, 11)
    
    if len(result) > 0:
        print(f"\n📊 測試結果:")
        print(f"   總筆數: {len(result)}")
        print(f"   欄位: {list(result.columns)}")
        
        # 顯示前幾筆資料
        print(f"\n   前5筆資料:")
        display_columns = ['公司代號', '公司名稱', '當月營收', '市場', '類型']
        available_columns = [col for col in display_columns if col in result.columns]
        print(result[available_columns].head())
        
        # 保存結果
        result.to_csv('finlab_style_monthly_revenue.csv', encoding='utf-8-sig', index=False)
        print(f"\n   💾 已保存: finlab_style_monthly_revenue.csv")
        
        # 營收統計
        if '當月營收' in result.columns:
            revenue_stats = result['當月營收'].describe()
            print(f"\n   📈 營收統計:")
            print(f"     總筆數: {revenue_stats['count']:.0f}")
            print(f"     平均值: {revenue_stats['mean']:,.0f} 千元")
            print(f"     中位數: {revenue_stats['50%']:,.0f} 千元")
            print(f"     最大值: {revenue_stats['max']:,.0f} 千元")
    else:
        print(f"\n❌ 測試失敗")

if __name__ == "__main__":
    test_finlab_style()
