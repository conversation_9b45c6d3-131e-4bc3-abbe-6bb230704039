#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收Excel/CSV匯出功能
"""

from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
import os

def test_excel_export():
    """測試Excel和CSV匯出功能"""
    
    print("🚀 測試月營收Excel/CSV匯出功能")
    print("=" * 60)
    
    # 使用正確的資料庫路徑
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    downloader = MopsBulkRevenueDownloader(db_path)
    
    # 測試2024年6月
    print("📅 下載並匯出 2024年6月 數據...")
    results = downloader.download_all_markets(2024, 6)
    
    print("\n📊 下載結果:")
    total = 0
    for market, count in results.items():
        print(f"  {market}: {count} 家公司")
        total += count
    
    print(f"\n✅ 總計: {total} 家公司")
    
    # 檢查匯出的檔案
    output_dir = "D:/Finlab/history/tables"
    excel_file = os.path.join(output_dir, "monthly_revenue_2024_06.xlsx")
    csv_file = os.path.join(output_dir, "monthly_revenue_2024_06.csv")
    
    print(f"\n📁 檢查匯出檔案:")
    print(f"  Excel檔案: {excel_file}")
    print(f"    存在: {'✅' if os.path.exists(excel_file) else '❌'}")
    if os.path.exists(excel_file):
        size = os.path.getsize(excel_file)
        print(f"    大小: {size:,} 位元組")
    
    print(f"  CSV檔案: {csv_file}")
    print(f"    存在: {'✅' if os.path.exists(csv_file) else '❌'}")
    if os.path.exists(csv_file):
        size = os.path.getsize(csv_file)
        print(f"    大小: {size:,} 位元組")
    
    return results

if __name__ == "__main__":
    test_excel_export()
