#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
包裝啟動器 - 修復模組問題後啟動主程式
"""

import sys
import os
import subprocess
import time

def show_progress():
    """顯示簡單的進度"""
    print("🚀 台股智能選股系統 v22.0")
    print("=" * 50)
    print("正在修復系統模組...")
    
    # 載入模組補丁
    try:
        import module_patch
        print("✅ 模組補丁載入成功")
    except Exception as e:
        print(f"⚠️ 模組補丁載入失敗: {e}")
    
    print("正在啟動主程式...")
    
    # 進度條效果
    for i in range(10):
        print(f"載入中... {'█' * (i+1)}{'░' * (9-i)} {(i+1)*10}%", end='\r')
        time.sleep(0.3)
    
    print("\n✅ 準備完成，正在啟動...")

def main():
    """主函數"""
    show_progress()
    
    # 確定主程式路徑
    exe_path = "dist/台股智能選股系統_修復版.exe"
    
    if not os.path.exists(exe_path):
        print(f"❌ 找不到主程式: {exe_path}")
        print("請確認文件是否存在")
        input("按 Enter 鍵退出...")
        return
    
    try:
        # 啟動主程式
        print(f"🔄 啟動: {exe_path}")
        subprocess.Popen([exe_path])
        print("✅ 主程式已啟動！")
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        input("按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
