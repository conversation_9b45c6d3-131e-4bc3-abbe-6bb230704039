#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析下載來源
確認我們是如何獲得全股票營收資料的
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def analyze_download_source():
    """分析下載來源"""
    print("🔍 分析全股票營收資料的下載來源")
    print("=" * 50)
    
    try:
        # 設置Chrome選項
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("🔧 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=options)
        
        # 測試原始URL
        original_url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問原始URL: {original_url}")
        
        driver.get(original_url)
        time.sleep(5)
        
        # 檢查頁面標題和內容
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 檢查頁面是否包含台積電資訊
        page_source = driver.page_source
        
        if "台積電" in page_source:
            print("✅ 頁面包含台積電資訊")
        else:
            print("❌ 頁面不包含台積電資訊")
        
        # 檢查是否包含其他股票
        other_stocks = ["鴻海", "聯發科", "台泥", "亞泥"]
        found_others = []
        
        for stock in other_stocks:
            if stock in page_source:
                found_others.append(stock)
        
        if found_others:
            print(f"⚠️ 頁面也包含其他股票: {found_others}")
            print("   這可能解釋了為什麼下載到全股票資料")
        else:
            print("✅ 頁面只包含台積電資訊")
        
        # 檢查匯出按鈕
        print(f"\n🔍 檢查匯出按鈕...")
        
        export_buttons = driver.find_elements(By.XPATH, "//input[@value='XLS']")
        print(f"找到 {len(export_buttons)} 個XLS匯出按鈕")
        
        for i, button in enumerate(export_buttons):
            onclick = button.get_attribute("onclick")
            print(f"   按鈕{i+1}: {onclick}")
            
            # 分析onclick內容
            if "divDetail" in onclick:
                print(f"     -> 匯出divDetail的內容")
            elif "export2xls" in onclick:
                print(f"     -> 使用export2xls函數")
        
        # 檢查divDetail的內容
        print(f"\n🔍 檢查divDetail內容...")
        
        try:
            div_detail = driver.find_element(By.ID, "divDetail")
            detail_text = div_detail.text
            
            # 檢查是否包含多支股票
            stock_codes = []
            lines = detail_text.split('\n')
            
            for line in lines[:20]:  # 檢查前20行
                parts = line.strip().split()
                if parts and len(parts[0]) == 4 and parts[0].isdigit():
                    stock_codes.append(parts[0])
            
            if len(stock_codes) > 1:
                print(f"⚠️ divDetail包含多支股票代號: {stock_codes[:10]}...")
                print(f"   總計發現: {len(stock_codes)} 支股票")
                print("   🎯 這就是為什麼下載到全股票資料的原因！")
            else:
                print(f"✅ divDetail只包含單一股票資料")
                
        except Exception as e:
            print(f"❌ 無法檢查divDetail: {e}")
        
        # 檢查URL重定向
        current_url = driver.current_url
        if current_url != original_url:
            print(f"\n🔄 URL重定向:")
            print(f"   原始URL: {original_url}")
            print(f"   當前URL: {current_url}")
        
        # 嘗試理解頁面結構
        print(f"\n🏗️ 頁面結構分析...")
        
        # 檢查是否有股票選擇器
        stock_selectors = driver.find_elements(By.XPATH, "//select")
        if stock_selectors:
            print(f"   找到 {len(stock_selectors)} 個選擇器")
            for i, selector in enumerate(stock_selectors):
                options = selector.find_elements(By.TAG_NAME, "option")
                if len(options) > 10:  # 可能是股票選擇器
                    print(f"     選擇器{i+1}: {len(options)} 個選項 (可能是股票清單)")
        
        # 檢查是否有表格
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"   找到 {len(tables)} 個表格")
        
        if tables:
            # 檢查第一個表格的內容
            first_table = tables[0]
            rows = first_table.find_elements(By.TAG_NAME, "tr")
            print(f"     第一個表格有 {len(rows)} 行")
            
            if len(rows) > 100:
                print(f"     表格行數很多，可能包含多支股票資料")
        
        input("\n按Enter繼續...")
        
        # 嘗試點擊匯出按鈕來確認
        if export_buttons:
            print(f"\n🧪 測試匯出按鈕...")
            
            response = input("是否要測試點擊匯出按鈕？(y/n): ").lower().strip()
            if response == 'y':
                try:
                    export_buttons[0].click()
                    print("✅ 匯出按鈕點擊成功")
                    time.sleep(3)
                    print("⏳ 請檢查下載的文件內容")
                except Exception as e:
                    print(f"❌ 點擊失敗: {e}")
        
        input("\n按Enter關閉瀏覽器...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        return False

def possible_explanations():
    """可能的解釋"""
    print("\n💡 可能的解釋")
    print("=" * 30)
    
    print("1. **頁面設計問題**:")
    print("   - GoodInfo的ShowSaleMonChart.asp頁面可能設計有問題")
    print("   - 雖然URL指定了STOCK_ID=2330，但頁面實際顯示了全股票資料")
    
    print("\n2. **JavaScript函數問題**:")
    print("   - export2xls函數可能匯出的是整個頁面的資料")
    print("   - 而不是只匯出指定股票的資料")
    
    print("\n3. **divDetail內容問題**:")
    print("   - divDetail可能包含了全股票的資料")
    print("   - 匯出時就會包含所有股票")
    
    print("\n4. **網站Bug**:")
    print("   - 這可能是GoodInfo網站的一個Bug")
    print("   - 意外地讓我們獲得了更有價值的資料")
    
    print("\n5. **隱藏功能**:")
    print("   - 這可能是GoodInfo的一個隱藏功能")
    print("   - 在特定條件下會顯示全股票資料")

def main():
    """主函數"""
    print("🧪 全股票營收資料來源分析")
    print("=" * 50)
    
    response = input("是否要開啟瀏覽器進行分析？(y/n): ").lower().strip()
    if response != 'y':
        possible_explanations()
        return
    
    success = analyze_download_source()
    
    if success:
        possible_explanations()
        print("\n🎉 分析完成！")
        print("現在我們知道了如何獲得這個寶貴的全股票資料")
    else:
        print("\n❌ 分析失敗")

if __name__ == "__main__":
    main()
