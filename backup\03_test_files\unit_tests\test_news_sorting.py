#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新聞排序修正
"""

import sqlite3
import os
from datetime import datetime

def test_news_sorting():
    """測試新聞排序功能"""
    print("📅 新聞排序測試")
    print("=" * 60)
    
    # 檢查資料庫
    db_path = "D:/Finlab/history/tables/news.db"
    if not os.path.exists(db_path):
        print(f"❌ 資料庫不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📊 可用表格: {tables}")
        
        stock_code = "1101"
        
        if 'news' in tables:
            print(f"\n✅ 使用news表格查詢 {stock_code} 的新聞...")
            
            # 使用修正後的排序邏輯
            cursor.execute('''
                SELECT id, search_time, search_keyword, title, source, 
                       pub_date, description, link, content, created_at
                FROM news
                WHERE stock_code = ?
                ORDER BY 
                    CASE 
                        WHEN pub_date IS NOT NULL AND pub_date != '' THEN pub_date 
                        ELSE created_at 
                    END DESC,
                    created_at DESC
                LIMIT 10
            ''', (stock_code,))
            
            results = cursor.fetchall()
            
            if results:
                print(f"📰 找到 {len(results)} 筆新聞，按時間排序（最新在上）:")
                print("-" * 80)
                
                for i, news in enumerate(results):
                    title = news[3]
                    source = news[4]
                    pub_date = news[5]
                    created_at = news[9]
                    
                    # 格式化日期
                    if pub_date:
                        display_date = pub_date[:10] if len(pub_date) >= 10 else pub_date
                    else:
                        display_date = created_at[:10] if created_at and len(created_at) >= 10 else "未知"
                    
                    print(f"{i+1:2d}. 📅 {display_date} | 📺 {source}")
                    print(f"    📰 {title}")
                    print(f"    🕒 儲存時間: {created_at}")
                    print()
                
                # 驗證排序是否正確
                print("🔍 排序驗證:")
                dates = []
                for news in results:
                    pub_date = news[5]
                    created_at = news[9]
                    sort_date = pub_date if pub_date else created_at
                    if sort_date:
                        dates.append(sort_date[:10] if len(sort_date) >= 10 else sort_date)
                
                if dates:
                    is_sorted = all(dates[i] >= dates[i+1] for i in range(len(dates)-1))
                    if is_sorted:
                        print("✅ 排序正確：最新日期在最上面")
                    else:
                        print("❌ 排序錯誤：日期順序不正確")
                        print(f"   日期順序: {dates}")
                
            else:
                print(f"❌ 沒有找到 {stock_code} 的新聞")
        
        elif 'news_content' in tables:
            print(f"\n✅ 使用news_content表格查詢 {stock_code} 的新聞...")
            
            # 使用修正後的排序邏輯
            cursor.execute('''
                SELECT news_id, date, time, source, title, reporter, link, 
                       search_keyword, news_summary, crawl_time
                FROM news_content
                WHERE stock_code = ? AND news_id LIKE 'google_%'
                ORDER BY 
                    CASE 
                        WHEN date IS NOT NULL AND date != '' THEN date 
                        ELSE crawl_time 
                    END DESC,
                    CASE 
                        WHEN time IS NOT NULL AND time != '' THEN time 
                        ELSE '23:59:59' 
                    END DESC
                LIMIT 10
            ''', (stock_code,))
            
            results = cursor.fetchall()
            
            if results:
                print(f"📰 找到 {len(results)} 筆新聞，按時間排序（最新在上）:")
                print("-" * 80)
                
                for i, news in enumerate(results):
                    title = news[4]
                    source = news[3]
                    date = news[1]
                    time = news[2]
                    
                    print(f"{i+1:2d}. 📅 {date} {time} | 📺 {source}")
                    print(f"    📰 {title}")
                    print()
                
                # 驗證排序是否正確
                print("🔍 排序驗證:")
                dates = [news[1] for news in results if news[1]]
                if dates:
                    is_sorted = all(dates[i] >= dates[i+1] for i in range(len(dates)-1))
                    if is_sorted:
                        print("✅ 排序正確：最新日期在最上面")
                    else:
                        print("❌ 排序錯誤：日期順序不正確")
                        print(f"   日期順序: {dates}")
            else:
                print(f"❌ 沒有找到 {stock_code} 的Google新聞")
        
        else:
            print("❌ 找不到合適的新聞表格")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_formatting():
    """測試日期格式化功能"""
    print("\n📅 日期格式化測試")
    print("=" * 60)
    
    test_dates = [
        "2025-07-18",           # 標準格式
        "20250718",             # 緊湊格式
        "2025-07-18 10:30:00",  # 包含時間
        "",                     # 空字串
        None,                   # None值
        "2025-7-8",            # 單位數月日
    ]
    
    for test_date in test_dates:
        print(f"輸入: {repr(test_date)}")
        
        # 模擬格式化邏輯
        if isinstance(test_date, str) and test_date:
            if len(test_date) == 10 and '-' in test_date:  # YYYY-MM-DD 格式
                formatted_date = test_date
            elif len(test_date) == 8:  # YYYYMMDD 格式
                formatted_date = f"{test_date[:4]}-{test_date[4:6]}-{test_date[6:8]}"
            elif len(test_date) >= 10:  # 包含時間的格式
                formatted_date = test_date[:10]  # 只取日期部分
            else:
                formatted_date = test_date
        else:
            # 如果沒有發布日期，使用今天的日期
            formatted_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"輸出: {formatted_date}")
        print()

if __name__ == "__main__":
    print("🔧 新聞排序修正測試")
    print("=" * 80)
    
    # 測試新聞排序
    sorting_test_passed = test_news_sorting()
    
    # 測試日期格式化
    test_date_formatting()
    
    print("=" * 80)
    print("📊 測試結果總結:")
    print(f"   新聞排序: {'✅ 通過' if sorting_test_passed else '❌ 失敗'}")
    
    if sorting_test_passed:
        print("\n🎉 排序修正成功！")
        print("\n💡 修正效果:")
        print("   • 最新的新聞現在顯示在最上面")
        print("   • 使用發布日期優先，儲存時間作為備選")
        print("   • 改善日期格式化顯示")
        print("   • 支援新舊兩種資料庫結構")
    else:
        print("\n⚠️ 排序測試未通過，需要進一步檢查")
    
    print("=" * 80)
