#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫整合測試腳本
測試所有數據庫是否正確整合到統一目錄
"""

import os
import sys
import sqlite3
import json
from database_integration_config import DatabaseConfig

def test_database_paths():
    """測試數據庫路徑配置"""
    print("🔍 測試數據庫路徑配置")
    print("=" * 50)
    
    db_config = DatabaseConfig()
    
    print(f"📁 數據庫根目錄: {db_config.DB_ROOT_DIR}")
    print(f"✅ 目錄存在: {os.path.exists(db_config.DB_ROOT_DIR)}")
    
    print("\n📊 配置的數據庫路徑:")
    for db_name, db_path in db_config.DATABASE_PATHS.items():
        exists = os.path.exists(db_path)
        status = "✅" if exists else "❌"
        size = ""
        if exists:
            try:
                size_bytes = os.path.getsize(db_path)
                size = f" ({size_bytes / 1024 / 1024:.2f} MB)"
            except:
                pass
        
        print(f"  {status} {db_name}: {db_path}{size}")
    
    return True

def test_app_config_update():
    """測試主程序配置更新"""
    print("\n🔍 測試主程序配置更新")
    print("=" * 50)
    
    config_file = "app_config.json"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            db_config = config.get('database', {})
            
            print("📋 主程序數據庫配置:")
            for key, value in db_config.items():
                if key.endswith('_db_path'):
                    exists = os.path.exists(value) if value else False
                    status = "✅" if exists else "❌"
                    print(f"  {status} {key}: {value}")
                elif key.endswith('_enabled'):
                    status = "✅" if value else "❌"
                    print(f"  {status} {key}: {value}")
            
            return True
            
        except Exception as e:
            print(f"❌ 讀取配置文件失敗: {e}")
            return False
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False

def test_finlab_system_integration():
    """測試Finlab系統整合"""
    print("\n🔍 測試Finlab系統整合")
    print("=" * 50)
    
    try:
        from finlab_tables_analysis_and_migration import FinlabTablesAnalyzer
        
        # 測試分析器初始化
        analyzer = FinlabTablesAnalyzer()
        
        print(f"📁 Finlab數據路徑: {analyzer.finlab_tables_path}")
        print(f"📁 本地數據路徑: {analyzer.local_data_path}")
        print(f"🗄️ 數據庫路徑: {analyzer.db_path}")
        
        # 檢查路徑
        local_path_exists = os.path.exists(analyzer.local_data_path)
        db_dir_exists = os.path.exists(os.path.dirname(analyzer.db_path))
        
        print(f"✅ 本地數據目錄存在: {local_path_exists}")
        print(f"✅ 數據庫目錄存在: {db_dir_exists}")
        
        # 檢查是否與其他數據庫在同一目錄
        db_config = DatabaseConfig()
        same_directory = os.path.dirname(analyzer.db_path) == db_config.DB_ROOT_DIR
        print(f"✅ 與其他數據庫在同一目錄: {same_directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Finlab系統整合測試失敗: {e}")
        return False

def test_database_connections():
    """測試數據庫連接"""
    print("\n🔍 測試數據庫連接")
    print("=" * 50)
    
    db_config = DatabaseConfig()
    available_dbs = db_config.get_available_databases()
    
    connection_results = {}
    
    for db_name, db_info in available_dbs.items():
        try:
            conn = sqlite3.connect(db_info['path'])
            cursor = conn.cursor()
            
            # 獲取表格列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 獲取第一個表的記錄數（如果有表的話）
            record_count = 0
            if tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {tables[0]}")
                    record_count = cursor.fetchone()[0]
                except:
                    pass
            
            conn.close()
            
            connection_results[db_name] = {
                'status': 'success',
                'tables': len(tables),
                'sample_table': tables[0] if tables else None,
                'sample_records': record_count
            }
            
            print(f"  ✅ {db_name}: {len(tables)} 個表格, 樣本記錄數: {record_count:,}")
            
        except Exception as e:
            connection_results[db_name] = {
                'status': 'error',
                'error': str(e)
            }
            print(f"  ❌ {db_name}: 連接失敗 - {e}")
    
    success_count = sum(1 for result in connection_results.values() if result['status'] == 'success')
    total_count = len(connection_results)
    
    print(f"\n📊 連接測試結果: {success_count}/{total_count} 成功")
    
    return success_count > 0

def test_finlab_gui_integration():
    """測試Finlab GUI整合"""
    print("\n🔍 測試Finlab GUI整合")
    print("=" * 50)
    
    try:
        # 測試GUI模組導入
        from finlab_tables_gui import FinlabTablesGUI
        print("✅ GUI模組導入成功")
        
        # 測試GUI初始化（不實際顯示窗口）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隱藏窗口
        
        app = FinlabTablesGUI(root)
        
        # 檢查數據庫路徑
        print(f"🗄️ GUI數據庫路徑: {app.db_path}")
        
        db_config = DatabaseConfig()
        correct_path = app.db_path == db_config.get_database_path('finlab_financial_data')
        print(f"✅ 數據庫路徑正確: {correct_path}")
        
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI整合測試失敗: {e}")
        return False

def create_integration_summary():
    """創建整合摘要報告"""
    print("\n📋 創建整合摘要報告")
    print("=" * 50)
    
    db_config = DatabaseConfig()
    summary = db_config.create_database_summary()
    
    report = f"""# 📊 數據庫整合摘要報告

## 🎯 整合結果
- **統一目錄**: {summary['database_root']}
- **整合數據庫數量**: {summary['total_databases']}
- **總大小**: {summary['total_size_mb']:.2f} MB

## 📋 整合的數據庫

| 數據庫名稱 | 大小(MB) | 描述 |
|-----------|---------|------|
"""
    
    for db_name, db_info in summary['databases'].items():
        report += f"| {db_name} | {db_info['size_mb']} | {db_info['description']} |\n"
    
    report += f"""
## ✅ 整合狀態
- **Finlab財務數據庫**: 已整合到統一目錄
- **主程序配置**: 已更新數據庫路徑
- **GUI界面**: 已配置使用統一路徑
- **系統兼容性**: 完全兼容現有功能

## 🎯 使用方法
1. 所有數據庫現在統一存放在 `{summary['database_root']}`
2. 主程序會自動使用新的數據庫路徑
3. Finlab財務數據管理系統已整合到統一目錄
4. 可以通過 `python run_finlab_system.py gui` 啟動財務數據管理界面

## 📝 注意事項
- 舊的數據庫路徑已自動遷移
- 配置文件已自動更新
- 所有功能保持完全兼容
"""
    
    # 保存報告
    report_path = "database_integration_summary.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 整合摘要報告已生成: {report_path}")
    
    return report_path

def main():
    """主測試函數"""
    print("🧪 數據庫整合測試套件")
    print("=" * 80)
    
    test_results = {}
    
    # 執行各項測試
    test_results['database_paths'] = test_database_paths()
    test_results['app_config'] = test_app_config_update()
    test_results['finlab_integration'] = test_finlab_system_integration()
    test_results['database_connections'] = test_database_connections()
    test_results['gui_integration'] = test_finlab_gui_integration()
    
    # 統計測試結果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print("\n" + "=" * 80)
    print("📊 測試結果摘要")
    print("=" * 80)
    
    for test_name, result in test_results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 總體結果: {passed_tests}/{total_tests} 測試通過")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！數據庫整合成功！")
        
        # 創建整合摘要
        create_integration_summary()
        
        print("\n📖 下一步:")
        print("  1. 使用 python run_finlab_system.py gui 啟動財務數據管理界面")
        print("  2. 所有數據庫現在統一存放在 D:/Finlab/history/tables")
        print("  3. 主程序會自動使用新的數據庫配置")
        
    else:
        print("⚠️ 部分測試失敗，請檢查相關配置")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    main()
