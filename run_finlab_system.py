#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab系統啟動腳本
提供統一的入口點來運行不同的功能模組
"""

import os
import sys
import argparse
import logging
from datetime import datetime

def setup_logging():
    """設置日誌"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"finlab_system_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def run_gui():
    """啟動GUI界面"""
    print("🖥️ 啟動Finlab財務數據管理GUI...")
    try:
        from finlab_tables_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"❌ GUI模組導入失敗: {e}")
        print("請確保已安裝所需的GUI依賴包：tkinter, matplotlib")
    except Exception as e:
        print(f"❌ GUI啟動失敗: {e}")

def run_analysis(finlab_path):
    """執行數據分析"""
    print("🔍 執行Finlab數據分析...")
    try:
        from finlab_tables_analysis_and_migration import FinlabTablesAnalyzer
        
        analyzer = FinlabTablesAnalyzer(finlab_path)
        
        # 分析數據
        results = analyzer.analyze_all_tables()
        if results:
            print(f"✅ 分析完成，發現 {len(results)} 個數據表格")
            
            # 生成報告
            report_path = analyzer.generate_analysis_report()
            print(f"📄 分析報告已生成: {report_path}")
        else:
            print("❌ 未發現任何數據表格")
            
    except Exception as e:
        print(f"❌ 分析失敗: {e}")

def run_migration(finlab_path):
    """執行數據移植"""
    print("🚀 執行Finlab數據移植...")
    try:
        from finlab_tables_analysis_and_migration import FinlabTablesAnalyzer
        
        analyzer = FinlabTablesAnalyzer(finlab_path)
        
        # 移植數據
        success = analyzer.migrate_all_tables()
        if success:
            print("✅ 數據移植成功")
            
            # 創建摘要
            summary = analyzer.create_data_summary()
            if summary:
                print(f"📊 數據庫包含 {summary['total_tables']} 個表格")
        else:
            print("❌ 數據移植失敗")
            
    except Exception as e:
        print(f"❌ 移植失敗: {e}")

def run_crawler():
    """執行數據爬取"""
    print("🕷️ 執行Finlab數據爬取...")
    try:
        from finlab_tables_analysis_and_migration import FinlabDataCrawler
        
        crawler = FinlabDataCrawler()
        
        # 爬取數據
        success = crawler.crawl_all_data()
        if success:
            print("✅ 數據爬取成功")
        else:
            print("❌ 數據爬取失敗")
            
    except Exception as e:
        print(f"❌ 爬取失敗: {e}")

def run_tests():
    """執行測試"""
    print("🧪 執行系統測試...")
    try:
        from test_finlab_system import main as test_main
        test_main()
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def show_status():
    """顯示系統狀態"""
    print("📊 Finlab系統狀態檢查")
    print("=" * 50)

    # 檢查數據目錄
    data_dir = "D:/Finlab/history/tables"
    if os.path.exists(data_dir):
        print(f"✅ 數據目錄存在: {data_dir}")
        
        # 檢查數據庫
        db_path = os.path.join(data_dir, "finlab_financial_data.db")
        if os.path.exists(db_path):
            print(f"✅ 數據庫存在: {db_path}")
            
            # 檢查表格數量
            try:
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                conn.close()
                print(f"📊 數據表格數量: {len(tables)}")
                
                if tables:
                    print("📋 可用表格:")
                    for (table_name,) in tables:
                        print(f"   - {table_name}")
            except Exception as e:
                print(f"⚠️ 數據庫檢查失敗: {e}")
        else:
            print(f"❌ 數據庫不存在: {db_path}")
    else:
        print(f"❌ 數據目錄不存在: {data_dir}")
    
    # 檢查日誌目錄
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        print(f"📝 日誌檔案數量: {len(log_files)}")
    else:
        print("❌ 日誌目錄不存在")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="Finlab財務數據管理系統")
    parser.add_argument('command', choices=['gui', 'analyze', 'migrate', 'crawl', 'test', 'status'],
                       help='要執行的命令')
    parser.add_argument('--finlab-path', 
                       default="D:/□Finlab學習/用 Python 理財：打造自己的 AI 股票理專_原始檔案/finlab_ml_course/finlab_ml_course/history/tables",
                       help='Finlab數據表格路徑')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='詳細輸出')
    
    args = parser.parse_args()
    
    # 設置日誌
    if args.verbose:
        setup_logging()
    
    print("🚀 Finlab財務數據管理系統")
    print("=" * 60)
    print(f"📅 執行時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 執行命令: {args.command}")
    
    if args.finlab_path:
        print(f"📁 數據路徑: {args.finlab_path}")
    
    print("-" * 60)
    
    try:
        if args.command == 'gui':
            run_gui()
        elif args.command == 'analyze':
            run_analysis(args.finlab_path)
        elif args.command == 'migrate':
            run_migration(args.finlab_path)
        elif args.command == 'crawl':
            run_crawler()
        elif args.command == 'test':
            run_tests()
        elif args.command == 'status':
            show_status()
        
        print("\n✅ 命令執行完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷執行")
    except Exception as e:
        print(f"\n❌ 執行失敗: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
