#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所有資料表的分批存檔功能
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_table_batch_save(table_name, crawl_func, test_dates):
    """測試單個資料表的分批存檔"""
    print(f"\n🧪 測試 {table_name} 分批存檔...")
    
    try:
        from crawler import update_table
        
        print(f"   測試日期數量: {len(test_dates)}")
        print(f"   預期: 每 10 筆存檔一次")
        
        # 只測試前幾個日期
        test_count = min(12, len(test_dates))
        update_table(table_name, crawl_func, test_dates[:test_count])
        
        print(f"✅ {table_name} 分批存檔測試完成")
        return True
        
    except Exception as e:
        print(f"❌ {table_name} 測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 所有資料表分批存檔測試")
    print("=" * 60)
    
    try:
        from crawler import crawl_price, crawl_bargin, crawl_pe, crawl_benchmark, crawl_monthly_report
        
        # 準備測試日期
        start_date = datetime(2022, 9, 1)
        test_dates = []
        
        for i in range(20):  # 20 個日期
            test_date = start_date + timedelta(days=i)
            # 跳過週末
            if test_date.weekday() < 5:  # 0-4 是週一到週五
                test_dates.append(test_date)
        
        print(f"準備測試日期: {len(test_dates)} 個")
        print(f"日期範圍: {test_dates[0].strftime('%Y-%m-%d')} 到 {test_dates[-1].strftime('%Y-%m-%d')}")
        
        # 測試各個資料表
        test_results = {}
        
        # 1. 測試 price (最重要的)
        test_results['price'] = test_table_batch_save('price', crawl_price, test_dates)
        
        # 2. 測試 benchmark (比較簡單)
        test_results['benchmark'] = test_table_batch_save('benchmark', crawl_benchmark, test_dates)
        
        # 3. 測試 pe (中等複雜度)
        # test_results['pe'] = test_table_batch_save('pe', crawl_pe, test_dates)
        
        # 4. 測試 bargin_report (中等複雜度)
        # test_results['bargin_report'] = test_table_batch_save('bargin_report', crawl_bargin, test_dates)
        
        # 5. 測試 monthly_report (月度資料，需要不同的日期)
        # monthly_dates = [datetime(2022, 9, 1), datetime(2022, 10, 1), datetime(2022, 11, 1)]
        # test_results['monthly_report'] = test_table_batch_save('monthly_report', crawl_monthly_report, monthly_dates)
        
        print("\n" + "=" * 60)
        print("📊 測試結果總結")
        print("=" * 60)
        
        success_count = 0
        for table, result in test_results.items():
            status = "✅ 成功" if result else "❌ 失敗"
            print(f"{table}: {status}")
            if result:
                success_count += 1
        
        total_count = len(test_results)
        print(f"\n🎯 總體結果: {success_count}/{total_count} 個資料表測試成功")
        
        if success_count == total_count:
            print("\n🎉 所有資料表的分批存檔功能都正常！")
            print("✨ 現在可以安全地運行 auto_update.py 了！")
        else:
            print("\n⚠️ 部分資料表需要進一步檢查")
            
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
