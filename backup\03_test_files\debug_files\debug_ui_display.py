#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
偵測界面顯示問題
檢查為什麼掃描結果沒有顯示在界面上
"""

import sys
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_ui_callback_mechanism():
    """測試界面回調機制"""
    print("🔍 測試界面回調機制")
    print("=" * 80)
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        # 創建監控器並獲取數據
        monitor = PreMarketMonitor()
        
        print("📊 獲取掃描數據...")
        scan_results = monitor.run_full_scan()
        
        if not scan_results:
            print("❌ 無法獲取掃描數據，無法測試界面")
            return False
        
        print(f"✅ 成功獲取掃描數據")
        print(f"   數據結構: {list(scan_results.keys())}")
        
        # 模擬界面回調測試
        print("\n🖥️ 模擬界面回調測試:")
        print("-" * 60)
        
        # 1. 測試 update_market_dashboard 邏輯
        print("1️⃣ 測試市場儀表板更新邏輯:")
        
        # 檢查美股數據
        us_data = scan_results.get('us_indices', {})
        if us_data:
            print(f"   美股數據: {len(us_data)} 項")
            us_changes = []
            for name, data in us_data.items():
                if isinstance(data, dict) and 'change_pct' in data:
                    change_pct = data.get('change_pct', 0)
                    us_changes.append(change_pct)
                    print(f"     • {name}: {change_pct:+.2f}%")
            
            if us_changes:
                avg_change = sum(us_changes) / len(us_changes)
                trend = "📈" if avg_change > 0 else "📉" if avg_change < 0 else "➡️"
                print(f"   平均變化: {avg_change:+.2f}% {trend}")
        else:
            print("   ❌ 沒有美股數據")
        
        # 檢查台指期數據
        tw_data = scan_results.get('taiwan_futures', {})
        if tw_data:
            print(f"   台指期數據: {len(tw_data)} 項")
            for name, data in tw_data.items():
                if isinstance(data, dict):
                    price = data.get('price', 0)
                    change_pct = data.get('change_pct', 0)
                    print(f"     • {name}: {price} ({change_pct:+.2f}%)")
        else:
            print("   ❌ 沒有台指期數據")
        
        # 2. 測試市場情緒
        print("\n2️⃣ 測試市場情緒計算:")
        try:
            sentiment = monitor.get_market_sentiment()
            print(f"   市場情緒: {sentiment}")
        except Exception as e:
            print(f"   ❌ 市場情緒計算失敗: {e}")
        
        # 3. 測試數據格式兼容性
        print("\n3️⃣ 測試數據格式兼容性:")
        
        # 檢查是否符合界面期望的格式
        expected_keys = ['us_indices', 'asia_indices', 'commodities', 'fx_rates', 'taiwan_futures', 'crypto']
        missing_keys = []
        present_keys = []
        
        for key in expected_keys:
            if key in scan_results:
                data = scan_results[key]
                if isinstance(data, dict) and data:
                    present_keys.append(key)
                    print(f"   ✅ {key}: {len(data)} 項")
                else:
                    missing_keys.append(key)
                    print(f"   ⚠️ {key}: 空數據")
            else:
                missing_keys.append(key)
                print(f"   ❌ {key}: 缺失")
        
        print(f"\n   總結: {len(present_keys)}/{len(expected_keys)} 個數據類別可用")
        
        return True, scan_results, present_keys, missing_keys
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False, None, [], []

def simulate_ui_update(scan_results):
    """模擬界面更新過程"""
    print(f"\n🎯 模擬界面更新過程")
    print("=" * 80)
    
    try:
        # 模擬 _on_scan_success 的邏輯
        print("📋 模擬 _on_scan_success 回調:")
        
        if scan_results:
            print("   ✅ 掃描結果不為空")
            
            # 模擬 update_market_dashboard
            print("   🎯 模擬 update_market_dashboard:")
            
            # 市場情緒
            sentiment = "中性"  # 預設值
            if 'market_summary' in scan_results:
                sentiment = scan_results['market_summary'].get('market_sentiment', '中性')
            print(f"     • 市場情緒: {sentiment}")
            
            # 美股狀態
            us_data = scan_results.get('us_indices', {})
            if us_data:
                us_changes = [data.get('change_pct', 0) for data in us_data.values() 
                            if isinstance(data, dict) and 'change_pct' in data]
                if us_changes:
                    avg_change = sum(us_changes) / len(us_changes)
                    trend = "📈" if avg_change > 0 else "📉" if avg_change < 0 else "➡️"
                    print(f"     • 美股狀態: {trend} {avg_change:+.2f}%")
            
            # 台指期狀態
            tw_data = scan_results.get('taiwan_futures', {})
            if tw_data:
                tw_index = tw_data.get('台股加權指數', {})
                if tw_index and isinstance(tw_index, dict):
                    price = tw_index.get('price', 0)
                    change_pct = tw_index.get('change_pct', 0)
                    trend = "📈" if change_pct > 0 else "📉" if change_pct < 0 else "➡️"
                    print(f"     • 台指期狀態: {trend} {price} ({change_pct:+.2f}%)")
            
            # 模擬 update_market_details
            print("   📊 模擬 update_market_details:")
            
            total_items = 0
            for category, data in scan_results.items():
                if category not in ['timestamp'] and isinstance(data, dict):
                    item_count = len(data)
                    total_items += item_count
                    print(f"     • {category}: {item_count} 項")
            
            print(f"     • 總計: {total_items} 項數據")
            
            print("   ✅ 界面更新模擬完成")
            return True
        else:
            print("   ❌ 掃描結果為空，界面顯示錯誤信息")
            return False
            
    except Exception as e:
        print(f"❌ 界面更新模擬失敗: {e}")
        return False

def check_ui_components():
    """檢查界面組件是否存在"""
    print(f"\n🖥️ 檢查界面組件")
    print("=" * 80)
    
    print("📋 需要檢查的界面組件:")
    required_components = [
        'market_sentiment_label',
        'us_status_label', 
        'tw_status_label',
        'premarket_status_label',
        'scan_btn'
    ]
    
    for component in required_components:
        print(f"   • {component}: 需要在主界面中存在")
    
    print("\n💡 如果界面沒有顯示，可能的原因:")
    print("   1. 界面組件不存在（hasattr檢查失敗）")
    print("   2. 回調函數沒有被正確調用")
    print("   3. 數據格式不匹配界面期望")
    print("   4. 線程安全問題（回調在錯誤線程執行）")
    print("   5. 異常被捕獲但沒有記錄")
    
    return True

def main():
    """主測試函數"""
    print("🔍 界面顯示問題偵測")
    print("=" * 90)
    print(f"📅 偵測時間: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 測試界面回調機制
    success, scan_results, present_keys, missing_keys = test_ui_callback_mechanism()
    
    if success and scan_results:
        # 2. 模擬界面更新
        ui_success = simulate_ui_update(scan_results)
        
        # 3. 檢查界面組件
        component_check = check_ui_components()
        
        # 總結
        print(f"\n🎯 偵測結果總結")
        print("=" * 90)
        
        print("✅ 數據獲取正常")
        print(f"📊 可用數據類別: {len(present_keys)}/{len(present_keys) + len(missing_keys)}")
        print(f"   • 可用: {', '.join(present_keys)}")
        if missing_keys:
            print(f"   • 缺失: {', '.join(missing_keys)}")
        
        if ui_success:
            print("✅ 界面更新邏輯正常")
        else:
            print("❌ 界面更新邏輯有問題")
        
        print("\n🔧 可能的解決方案:")
        print("   1. 檢查主程式中的界面組件是否正確初始化")
        print("   2. 確認回調函數是否被正確調用")
        print("   3. 添加更多日誌來追蹤界面更新過程")
        print("   4. 檢查線程安全問題")
        
        print("\n💡 建議的調試步驟:")
        print("   1. 在 _on_scan_success 開頭添加 print 語句")
        print("   2. 在 update_market_dashboard 開頭添加 print 語句")
        print("   3. 檢查 hasattr 檢查是否通過")
        print("   4. 確認數據格式是否正確")
    else:
        print("❌ 數據獲取失敗，無法進行界面測試")
    
    print("=" * 90)
    print("🔍 界面顯示偵測完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 偵測已停止")
    except Exception as e:
        print(f"\n❌ 偵測失敗: {e}")
    
    print(f"\n👋 偵測結束")
