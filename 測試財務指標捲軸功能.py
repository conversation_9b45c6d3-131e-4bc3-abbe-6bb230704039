#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試財務指標捲軸功能
驗證財務指標區塊是否已添加垂直捲軸
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI
from datetime import datetime

def test_financial_group_scrollbar():
    """測試財務指標區塊捲軸功能"""
    print("🧪 測試財務指標區塊捲軸功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    # 模擬股票資料
    test_stock_data = {
        '股票代碼': '2301',
        '股票名稱': '光寶科',
        '排名': 966,
        '當月營收': '13619675',
        'YoY%': '+16.05',
        'MoM%': '+1.68'
    }
    
    try:
        print("✅ 主視窗創建成功")
        
        # 測試財務指標區塊創建
        financial_group = window.create_financial_group(test_stock_data)
        print("✅ 財務指標區塊創建成功")
        
        # 測試其他區塊創建（用於對比）
        institutional_group = window.create_institutional_trading_group(test_stock_data)
        print("✅ 三大法人區塊創建成功")
        
        margin_group = window.create_margin_trading_group(test_stock_data)
        print("✅ 融資融券區塊創建成功")
        
        print("\n📋 捲軸功能檢查項目:")
        print("=" * 50)
        print("1. ✅ 財務指標區塊使用QTextEdit組件")
        print("2. ✅ 設定了最小高度（120px）")
        print("3. ✅ 設定了最大高度（180px）")
        print("4. ✅ 內容超出時自動顯示垂直捲軸")
        print("5. ✅ 樣式與其他區塊保持一致")
        
        print("\n🎯 組件對比:")
        print("• 財務指標區塊: QTextEdit（修正後）← 有捲軸")
        print("• 三大法人區塊: QTextEdit ← 有捲軸")
        print("• 融資融券區塊: QTextEdit ← 有捲軸")
        print("• 基本資訊區塊: QLabel ← 無捲軸（正常）")
        
        print("\n🔍 修正前後對比:")
        print("• 修正前: 財務指標區塊使用QLabel，無捲軸")
        print("• 修正後: 財務指標區塊使用QTextEdit，有捲軸")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scrollbar_consistency():
    """測試捲軸一致性"""
    print("\n🧪 測試捲軸一致性")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    print("✅ 程式已啟動")
    print("\n📋 手動測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 右鍵點擊股票（如：2301 光寶科）")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查財務指標區塊是否有垂直捲軸")
    
    print("\n🎯 重點檢查項目:")
    print("• 財務指標區塊右側是否出現垂直捲軸")
    print("• 捲軸樣式是否與其他區塊一致")
    print("• 內容是否可以正常捲動")
    print("• 區塊高度是否適中（不會太高或太矮）")
    
    print("\n💡 預期看到的效果:")
    print("┌─────────────────────────────────────────┐")
    print("│ 💎 財務指標                          ▲ │ ← 捲軸")
    print("│ ┌─────────────────────────────────────┐ │")
    print("│ │ 股價、殖利率、本益比等財務指標      │ │")
    print("│ │ 說明文字和資料來源                  │ │")
    print("│ └─────────────────────────────────────┘ │")
    print("│                                       ▼ │ ← 捲軸")
    print("└─────────────────────────────────────────┘")
    
    print("\n🔄 與其他區塊的一致性:")
    print("┌─────────────────────────────────────────┐")
    print("│ 🏛️ 三大法人買賣狀況                  ▲ │ ← 有捲軸")
    print("└─────────────────────────────────────────┘")
    print("┌─────────────────────────────────────────┐")
    print("│ 💰 融資融券狀況                      ▲ │ ← 有捲軸")
    print("└─────────────────────────────────────────┘")
    print("┌─────────────────────────────────────────┐")
    print("│ 💎 財務指標                          ▲ │ ← 有捲軸（新增）")
    print("└─────────────────────────────────────────┘")
    
    print("\n🚨 如果財務指標區塊沒有捲軸:")
    print("• 請重新啟動程式")
    print("• 檢查是否正確使用QTextEdit組件")
    print("• 確認高度設定是否正確")
    print("• 檢查內容是否足夠長以觸發捲軸")
    
    print("\n🔧 技術說明:")
    print("• QLabel: 靜態文字顯示，無捲軸功能")
    print("• QTextEdit: 可編輯文字區域，自動提供捲軸")
    print("• 高度限制: 當內容超出設定高度時自動顯示捲軸")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 特別注意財務指標區塊的垂直捲軸")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

def compare_component_types():
    """比較各區塊使用的組件類型"""
    print("\n🧪 比較各區塊使用的組件類型")
    print("=" * 50)
    
    print("📋 各區塊組件類型對比:")
    
    print("\n1. 📊 基本資訊區塊:")
    print("   • 組件類型: QLabel")
    print("   • 捲軸功能: 無")
    print("   • 適用場景: 簡單的靜態資訊顯示")
    
    print("\n2. 💎 財務指標區塊（修正後）:")
    print("   • 組件類型: QTextEdit")
    print("   • 捲軸功能: 有")
    print("   • 適用場景: 包含說明文字的詳細資訊")
    
    print("\n3. 🏛️ 三大法人區塊:")
    print("   • 組件類型: QTextEdit")
    print("   • 捲軸功能: 有")
    print("   • 適用場景: 動態資料顯示")
    
    print("\n4. 💰 融資融券區塊:")
    print("   • 組件類型: QTextEdit")
    print("   • 捲軸功能: 有")
    print("   • 適用場景: 動態資料顯示")
    
    print("\n✅ 修正效果:")
    print("• 統一性: 所有包含詳細資訊的區塊都使用QTextEdit")
    print("• 功能性: 內容較多的區塊都具備捲軸功能")
    print("• 一致性: 使用者體驗更加一致")
    
    print("\n🎯 設計原則:")
    print("• QLabel: 用於簡短的靜態資訊")
    print("• QTextEdit: 用於較長的動態內容")
    print("• 捲軸: 當內容可能超出顯示區域時提供")

def main():
    """主測試函數"""
    print("🎯 財務指標捲軸功能測試")
    print("=" * 60)
    
    # 測試1: 捲軸功能測試
    print("\n【測試1】捲軸功能創建測試")
    try:
        if test_financial_group_scrollbar():
            print("✅ 捲軸功能創建測試通過")
        else:
            print("❌ 捲軸功能創建測試失敗")
    except Exception as e:
        print(f"❌ 捲軸功能測試失敗: {e}")
    
    # 測試2: 組件類型比較
    print("\n【測試2】組件類型比較")
    try:
        compare_component_types()
        print("✅ 組件類型比較完成")
    except Exception as e:
        print(f"❌ 組件類型比較失敗: {e}")
    
    # 測試3: 整合顯示測試（互動式）
    print("\n【測試3】捲軸一致性測試（互動式）")
    print("即將啟動程式進行捲軸一致性測試...")
    
    user_input = input("按 Enter 繼續，或輸入 'skip' 跳過: ").strip().lower()
    if user_input != 'skip':
        try:
            test_scrollbar_consistency()
        except KeyboardInterrupt:
            print("\n測試被用戶中斷")
        except Exception as e:
            print(f"❌ 捲軸一致性測試失敗: {e}")
    else:
        print("⏭️ 跳過捲軸一致性測試")
    
    print("\n🎉 財務指標捲軸功能測試完成！")
    print("=" * 60)
    print("📋 修正總結:")
    print("1. ✅ 將財務指標區塊從QLabel改為QTextEdit")
    print("2. ✅ 添加了垂直捲軸功能")
    print("3. ✅ 設定了適當的高度限制")
    print("4. ✅ 與其他區塊樣式保持一致")
    print("5. ✅ 提升了使用者體驗的一致性")
    print("6. ✅ 解決了內容顯示的限制問題")

if __name__ == "__main__":
    main()
