#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 CANSLIM 策略除零錯誤修復
"""
import sys
import os
import pandas as pd
import numpy as np
import warnings
from datetime import datetime, timedelta

def create_problematic_data():
    """創建包含零價格的問題數據"""
    dates = pd.date_range(end=datetime.now(), periods=300, freq='D')
    
    # 創建包含零價格的數據
    prices = []
    volumes = []
    
    for i in range(300):
        if i < 50:
            # 前50天正常價格
            prices.append(100 + np.random.normal(0, 5))
            volumes.append(500000 + np.random.randint(-100000, 100000))
        elif i < 100:
            # 中間50天包含零價格
            if i % 10 == 0:
                prices.append(0)  # 故意設置零價格
            else:
                prices.append(max(0.1, 100 + np.random.normal(0, 5)))
            volumes.append(500000 + np.random.randint(-100000, 100000))
        else:
            # 後面正常價格
            prices.append(100 + np.random.normal(0, 5))
            volumes.append(500000 + np.random.randint(-100000, 100000))
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': [max(0.1, p * 0.99) for p in prices],
        'High': [p * 1.02 for p in prices],
        'Low': [max(0.1, p * 0.98) for p in prices],
        'Close': prices,
        'Volume': [max(1000, v) for v in volumes]
    })
    
    return df

def test_divide_by_zero_fixes():
    """測試除零錯誤修復"""
    print("🚀 測試 CANSLIM 策略除零錯誤修復...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 捕獲警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 導入策略模組
            from strategies.canslim_strategy import CANSLIMStrategy
            
            print("✅ CANSLIM 策略模組導入成功")
            
            # 創建策略實例
            canslim = CANSLIMStrategy()
            
            # 創建問題數據
            problematic_df = create_problematic_data()
            
            print("✅ 問題數據創建成功")
            print(f"  數據長度: {len(problematic_df)} 天")
            print(f"  零價格數量: {(problematic_df['Close'] == 0).sum()}")
            print(f"  最小價格: {problematic_df['Close'].min()}")
            print(f"  最大價格: {problematic_df['Close'].max()}")
            
            # 執行策略分析
            result = canslim.analyze_stock(problematic_df)
            
            print("✅ 策略分析執行成功")
            
            # 檢查是否有除零警告
            divide_by_zero_warnings = [warning for warning in w 
                                     if "divide by zero" in str(warning.message)]
            
            if divide_by_zero_warnings:
                print(f"❌ 仍然存在 {len(divide_by_zero_warnings)} 個除零警告:")
                for warning in divide_by_zero_warnings:
                    print(f"  - {warning.filename}:{warning.lineno}: {warning.message}")
                return False
            else:
                print("✅ 沒有除零警告")
            
            # 檢查結果
            print(f"\n📊 分析結果:")
            print(f"  是否符合: {result['suitable']}")
            print(f"  總分: {result.get('total_score', result.get('score', 0))}/100")
            print(f"  原因: {result['reason']}")
            
            # 檢查詳細結果
            if 'details' in result and result['details']:
                print(f"\n📋 詳細評分:")
                for key, detail in result['details'].items():
                    score = detail.get('score', 0)
                    reason = detail.get('reason', '')
                    status = "✅" if detail.get('pass', False) else "❌"
                    print(f"  {status} {key}: {score}分 - {reason}")
            
            return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """測試邊界情況"""
    print("\n🚀 測試邊界情況...")
    
    try:
        from strategies.canslim_strategy import CANSLIMStrategy
        canslim = CANSLIMStrategy()
        
        # 測試案例
        test_cases = [
            ("全零價格", [0] * 300),
            ("負價格", [-1, -2, -3] + [100] * 297),
            ("極小價格", [0.0001] * 300),
            ("混合異常", [0, -1, 0.0001, 100] * 75)
        ]
        
        for case_name, prices in test_cases:
            print(f"\n  測試案例: {case_name}")
            
            dates = pd.date_range(end=datetime.now(), periods=len(prices), freq='D')
            volumes = [500000] * len(prices)
            
            df = pd.DataFrame({
                'Date': dates,
                'Open': [max(0.1, p * 0.99) if p > 0 else 0.1 for p in prices],
                'High': [max(0.1, p * 1.02) if p > 0 else 0.1 for p in prices],
                'Low': [max(0.1, p * 0.98) if p > 0 else 0.1 for p in prices],
                'Close': prices,
                'Volume': volumes
            })
            
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                result = canslim.analyze_stock(df)
                
                # 檢查除零警告
                divide_warnings = [warning for warning in w 
                                 if "divide by zero" in str(warning.message)]
                
                if divide_warnings:
                    print(f"    ❌ 有 {len(divide_warnings)} 個除零警告")
                    return False
                else:
                    print(f"    ✅ 無除零警告，結果: {result['suitable']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 邊界測試失敗: {e}")
        return False

def test_individual_methods():
    """測試個別方法"""
    print("\n🚀 測試個別方法...")
    
    try:
        from strategies.canslim_strategy import CANSLIMStrategy
        canslim = CANSLIMStrategy()
        
        # 創建問題數據
        problematic_df = create_problematic_data()
        
        # 測試各個方法
        methods_to_test = [
            ('calculate_rps', lambda: canslim.calculate_rps(problematic_df)),
            ('check_current_earnings', lambda: canslim.check_current_earnings(problematic_df)),
            ('check_annual_earnings', lambda: canslim.check_annual_earnings(problematic_df)),
            ('check_new_products', lambda: canslim.check_new_products(problematic_df)),
            ('check_supply_demand', lambda: canslim.check_supply_demand(problematic_df)),
            ('check_leader_laggard', lambda: canslim.check_leader_laggard(problematic_df)),
            ('check_institutional_sponsorship', lambda: canslim.check_institutional_sponsorship(problematic_df)),
            ('check_market_direction', lambda: canslim.check_market_direction(problematic_df))
        ]
        
        for method_name, method_call in methods_to_test:
            print(f"  測試 {method_name}...")
            
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                result = method_call()
                
                # 檢查除零警告
                divide_warnings = [warning for warning in w 
                                 if "divide by zero" in str(warning.message)]
                
                if divide_warnings:
                    print(f"    ❌ {method_name} 有除零警告")
                    for warning in divide_warnings:
                        print(f"      {warning.filename}:{warning.lineno}: {warning.message}")
                    return False
                else:
                    print(f"    ✅ {method_name} 無除零警告")
        
        return True
        
    except Exception as e:
        print(f"❌ 個別方法測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 CANSLIM 策略除零錯誤修復測試")
    print("=" * 60)
    
    success1 = test_divide_by_zero_fixes()
    success2 = test_edge_cases()
    success3 = test_individual_methods()
    
    overall_success = success1 and success2 and success3
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 CANSLIM 策略除零錯誤修復測試通過！")
        print("✅ 所有除零錯誤已修復")
        print("✅ 邊界情況處理正常")
        print("✅ 個別方法運作正常")
        print("✅ 不再出現 RuntimeWarning")
    else:
        print("❌ CANSLIM 策略除零錯誤修復測試失敗")
        print("需要進一步檢查問題")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
