# 🐰 監獄兔策略說明

## 策略概述

監獄兔策略是一個專門針對被處置股票的反向操作策略，透過精心設計的條件篩選和持倉時間控制，來捕捉市場反應過度的機會。

## 策略理念

### 核心思想
- **市場心理學應用**：利用投資者對處置股的恐懼心理，在市場過度反應時尋找機會
- **賽局理論**：預測其他投資者的行為和可能的過度反應來尋找利潤空間
- **反向操作**：在處置開始時間進入市場，在處置結束時退出

### 策略命名
透過將此策略命名為「監獄兔」，巧妙地暗示了策略的狡猾和機敏，正如兔子在逆境中找到出路一般。

## 技術實現

### 篩選條件

#### 1. 股票基本篩選
- **普通股限制**：只針對4位數股票代碼的標的（以普通股為主，排除特殊金融商品）
- **非分盤交易**：過濾掉分盤交易的處置雜訊

#### 2. 核心技術指標

##### 波動率分析
- **高波動率**：年化波動率 > 40%（25分）
- **中等波動率**：年化波動率 > 30%（15分）
- 處置股通常伴隨高波動率

##### 成交量異常檢測
- **異常放大**：成交量 > 2倍平均（20分）
- **放大**：成交量 > 1.5倍平均（10分）
- 處置期間通常有異常成交量

##### 價格位置評估
- **相對低位**：價格 < 0.9 × MA20（20分）
- **略低於均線**：價格 < 0.95 × MA20（10分）
- 處置期間股價容易被過度拋售

##### RSI超賣指標
- **嚴重超賣**：RSI < 30（25分）
- **輕度超賣**：RSI < 40（15分）
- 處置股容易出現超賣現象

##### 反彈跡象
- **明顯反彈**：近5日上漲 > 3%（10分）
- **止跌企穩**：近5日上漲 > 0%（5分）

### 評分系統
- **總分範圍**：0-100分
- **通過門檻**：≥ 70分
- **部分符合**：50-69分
- **不符合**：< 50分

## 交易參數

### 建議參數設置
```python
trade_at_price = "open"        # 以開盤價交易
fee_ratio = 1.425/1000/3      # 手續費比率
position_limit = 0.2          # 持倉限制20%
```

### 進出場時機
- **進場時機**：處置開始時間
- **出場時機**：處置結束時間
- **風險控制**：設定持倉限制，控制單一標的風險

## 策略優勢

### 1. 市場機會捕捉
- 利用市場對處置股的過度恐懼
- 在非理性拋售中尋找價值回歸機會

### 2. 風險控制
- 明確的進出場時間點
- 嚴格的持倉限制
- 多重技術指標確認

### 3. 心理優勢
- 反向思維，避免跟風操作
- 利用群眾心理的弱點

## 風險提醒

### 主要風險
1. **處置原因風險**：需要了解股票被處置的具體原因
2. **流動性風險**：處置股可能面臨流動性不足
3. **基本面風險**：公司可能存在實質性問題
4. **時間風險**：處置期間可能延長

### 風險控制措施
1. **嚴格篩選**：使用多重技術指標確認
2. **分散投資**：不要集中投資單一處置股
3. **及時止損**：設定明確的止損點
4. **持續監控**：密切關注處置狀態變化

## 使用方法

### 在系統中使用
1. 選擇「🚀 進階策略」類別
2. 選擇「監獄兔」策略
3. **點擊「📖 策略說明」按鈕查看詳細說明**
4. 設定篩選日期
5. 執行選股分析
6. 查看評分結果和詳細分析

### 策略說明功能
系統內建完整的策略說明功能，包含三個標籤頁：
- **📋 策略概述**：核心理念、適用時機、策略特色
- **📖 詳細說明**：技術實現、評分系統、程式邏輯
- **💡 使用建議**：實戰指南、風險提醒、最佳實踐

### 結果解讀
- **🐰 監獄兔信號**：符合策略條件的股票
- **評分**：顯示具體評分和原因
- **技術指標**：波動率、成交量比率、RSI等詳細數據

## 參考資料

- **進階訊息**：https://www.finlab.tw/alerting_stock/
- **處置股相關規定**：請參考證交所公告
- **風險管理**：建議搭配其他技術分析工具使用

## 免責聲明

本策略僅供參考，不構成投資建議。投資有風險，請根據自身風險承受能力謹慎投資。使用本策略前請充分了解處置股的相關風險。

---

**策略版本**：v1.0  
**更新日期**：2024-07-08  
**開發者**：O3mh 台股智能選股系統
