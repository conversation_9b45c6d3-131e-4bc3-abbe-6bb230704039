#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 pe.pkl 檔案的狀態
"""

import pandas as pd
import datetime
import os

def check_pe_file():
    """檢查 pe.pkl 檔案"""
    file_path = "history/tables/pe.pkl"
    
    print(f"🔍 檢查 pe.pkl 檔案")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        
        # 檢查目錄是否存在
        dir_path = os.path.dirname(file_path)
        if os.path.exists(dir_path):
            print(f"📁 目錄存在: {dir_path}")
            files = os.listdir(dir_path)
            print(f"📋 目錄中的檔案:")
            for file in sorted(files):
                if file.endswith('.pkl'):
                    file_size = os.path.getsize(os.path.join(dir_path, file)) / (1024 * 1024)
                    print(f"   {file} ({file_size:.1f} MB)")
        else:
            print(f"❌ 目錄不存在: {dir_path}")
        return
    
    try:
        # 檔案基本資訊
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        file_mtime = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
        
        print(f"📁 檔案資訊:")
        print(f"   路徑: {file_path}")
        print(f"   大小: {file_size:.1f} MB")
        print(f"   修改時間: {file_mtime}")
        
        # 讀取資料
        print(f"\n📖 讀取資料...")
        df = pd.read_pickle(file_path)
        
        print(f"✅ 讀取成功:")
        print(f"   總筆數: {len(df):,}")
        print(f"   資料類型: {type(df)}")
        print(f"   索引名稱: {df.index.names}")
        print(f"   欄位數: {len(df.columns)}")
        print(f"   欄位: {list(df.columns)}")
        
        # 檢查日期範圍 (安全方式)
        print(f"\n📅 日期檢查:")
        try:
            if 'date' in df.index.names:
                dates = df.index.get_level_values('date')
                date_list = sorted(dates.unique())
                if len(date_list) > 0:
                    print(f"   日期數量: {len(date_list)}")
                    print(f"   最早日期: {date_list[0]}")
                    print(f"   最新日期: {date_list[-1]}")
                    
                    # 檢查最近幾個日期
                    print(f"\n📊 最近幾個日期的資料:")
                    for date in date_list[-5:]:  # 最近5個日期
                        count = len(df[df.index.get_level_values('date') == date])
                        print(f"   {date}: {count:,} 筆")
            else:
                print(f"   ⚠️ 索引中沒有 'date' 欄位")
                
        except Exception as e:
            print(f"   ⚠️ 日期檢查失敗: {str(e)}")
        
        # 檢查股票
        print(f"\n📈 股票檢查:")
        try:
            if 'stock_id' in df.index.names:
                stocks = df.index.get_level_values('stock_id')
                unique_stocks = stocks.unique()
                print(f"   股票數量: {len(unique_stocks)}")
                print(f"   前5檔股票: {list(unique_stocks[:5])}")
            else:
                print(f"   ⚠️ 索引中沒有 'stock_id' 欄位")
        except Exception as e:
            print(f"   ⚠️ 股票檢查失敗: {str(e)}")
        
        # 檢查 PE 相關欄位
        print(f"\n💹 PE 資料檢查:")
        try:
            pe_columns = [col for col in df.columns if 'pe' in col.lower() or 'PE' in col or '本益比' in col]
            if pe_columns:
                print(f"   PE 相關欄位: {pe_columns}")
                for col in pe_columns:
                    pe_data = df[col].dropna()
                    if len(pe_data) > 0:
                        print(f"   {col}:")
                        print(f"     有效筆數: {len(pe_data):,}")
                        print(f"     範圍: {pe_data.min():.2f} ~ {pe_data.max():.2f}")
                        print(f"     平均: {pe_data.mean():.2f}")
            else:
                print(f"   ⚠️ 找不到 PE 相關欄位")
                print(f"   所有欄位: {list(df.columns)}")
        except Exception as e:
            print(f"   ⚠️ PE 檢查失敗: {str(e)}")
        
        # 檢查資料完整性
        print(f"\n🔍 資料完整性檢查:")
        try:
            # 檢查空值
            null_counts = df.isnull().sum()
            total_cells = len(df) * len(df.columns)
            total_nulls = null_counts.sum()
            null_percentage = (total_nulls / total_cells) * 100
            
            print(f"   總格數: {total_cells:,}")
            print(f"   空值數: {total_nulls:,} ({null_percentage:.1f}%)")
            
            if total_nulls > 0:
                print(f"   各欄位空值:")
                for col, null_count in null_counts.items():
                    if null_count > 0:
                        percentage = (null_count / len(df)) * 100
                        print(f"     {col}: {null_count:,} ({percentage:.1f}%)")
        except Exception as e:
            print(f"   ⚠️ 完整性檢查失敗: {str(e)}")
        
        print(f"\n🎉 檢查完成！")
        
        # 判斷更新狀態
        if file_mtime.date() == datetime.date.today():
            print(f"✅ 檔案今天有更新")
        else:
            days_old = (datetime.date.today() - file_mtime.date()).days
            print(f"⚠️ 檔案已 {days_old} 天未更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def check_pe_crawler_status():
    """檢查 PE 爬蟲在 auto_update.py 中的狀態"""
    print(f"\n🔧 檢查 PE 爬蟲狀態")
    print("=" * 50)
    
    try:
        with open('auto_update.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查 PE 相關的設定
        if "'pe'" in content and "crawl_pe" in content:
            print(f"📋 在 auto_update.py 中找到 PE 相關設定")
            
            # 檢查是否被註解
            lines = content.split('\n')
            pe_lines = [line for line in lines if "'pe'" in line and "crawl_pe" in line]
            
            for line in pe_lines:
                line = line.strip()
                if line.startswith('#'):
                    print(f"⚠️ PE 爬蟲被停用: {line}")
                else:
                    print(f"✅ PE 爬蟲已啟用: {line}")
        else:
            print(f"❌ 在 auto_update.py 中找不到 PE 相關設定")
        
    except Exception as e:
        print(f"❌ 檢查 auto_update.py 失敗: {str(e)}")

if __name__ == "__main__":
    print("🔍 PE 檔案檢查工具")
    print("=" * 60)
    print("🎯 目標: 檢查 pe.pkl 檔案的狀態和內容")
    print("=" * 60)
    
    # 檢查檔案
    check_pe_file()
    
    # 檢查爬蟲狀態
    check_pe_crawler_status()
