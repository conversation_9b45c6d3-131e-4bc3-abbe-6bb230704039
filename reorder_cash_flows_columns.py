#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新排列 cash_flows.db 的欄位順序，讓 stock_name 緊鄰 stock_id
"""

import sqlite3
import pandas as pd

def reorder_cash_flows_columns():
    """重新排列 cash_flows.db 的欄位順序"""
    
    print("=" * 80)
    print("🔄 重新排列 cash_flows.db 的欄位順序")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    try:
        # 1. 連接資料庫並檢查現有結構
        print("📖 檢查現有資料庫結構...")
        
        conn = sqlite3.connect(cash_flows_db)
        cursor = conn.cursor()
        
        # 檢查現有欄位
        cursor.execute("PRAGMA table_info(cash_flows)")
        columns = cursor.fetchall()
        
        print(f"📋 現有欄位數: {len(columns)}")
        print(f"📋 前10個欄位:")
        for i, (cid, name, type_, notnull, default, pk) in enumerate(columns[:10], 1):
            print(f"   {i:2d}. {name}")
        
        # 2. 讀取所有資料
        print(f"\n📖 讀取所有資料...")
        df = pd.read_sql_query("SELECT * FROM cash_flows", conn)
        
        print(f"✅ 成功讀取 {len(df):,} 筆記錄")
        print(f"📊 現有欄位順序: {list(df.columns[:5])}...")
        
        # 3. 重新排列欄位順序
        print(f"\n🔄 重新排列欄位順序...")
        
        # 獲取所有欄位名稱
        all_columns = list(df.columns)
        
        # 創建新的欄位順序：stock_id, stock_name, date, 然後是其他欄位
        new_column_order = []
        
        # 首先添加主要識別欄位
        if 'stock_id' in all_columns:
            new_column_order.append('stock_id')
        if 'stock_name' in all_columns:
            new_column_order.append('stock_name')
        if 'date' in all_columns:
            new_column_order.append('date')
        
        # 然後添加其他欄位（按原順序，但排除已添加的）
        for col in all_columns:
            if col not in new_column_order:
                new_column_order.append(col)
        
        print(f"📋 新的欄位順序前5個: {new_column_order[:5]}")
        
        # 重新排列 DataFrame
        df_reordered = df[new_column_order]
        
        # 4. 備份原始表格
        print(f"\n💾 備份原始表格...")
        cursor.execute("ALTER TABLE cash_flows RENAME TO cash_flows_backup")
        conn.commit()
        print("✅ 原始表格已備份為 cash_flows_backup")
        
        # 5. 創建新表格並插入重新排列的資料
        print(f"\n🔄 創建新表格並插入資料...")
        
        # 將重新排列的資料寫入新表格
        df_reordered.to_sql('cash_flows', conn, if_exists='replace', index=False)
        
        print("✅ 新表格創建完成")
        
        # 6. 驗證新表格結構
        print(f"\n🔍 驗證新表格結構...")
        
        cursor.execute("PRAGMA table_info(cash_flows)")
        new_columns = cursor.fetchall()
        
        print(f"📋 新表格欄位數: {len(new_columns)}")
        print(f"📋 新表格前10個欄位:")
        for i, (cid, name, type_, notnull, default, pk) in enumerate(new_columns[:10], 1):
            print(f"   {i:2d}. {name:<20} {type_}")
        
        # 檢查記錄數
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        count = cursor.fetchone()[0]
        print(f"📊 新表格記錄數: {count:,}")
        
        # 檢查範例資料
        cursor.execute("SELECT stock_id, stock_name, date FROM cash_flows WHERE stock_name IS NOT NULL LIMIT 5")
        sample_data = cursor.fetchall()
        print(f"\n📊 範例資料:")
        for stock_id, stock_name, date in sample_data:
            print(f"   {stock_id} | {stock_name} | {date}")
        
        # 7. 刪除備份表格（可選）
        print(f"\n🗑️  是否刪除備份表格？")
        # 為了安全起見，先保留備份表格
        print("💡 備份表格 'cash_flows_backup' 已保留，如確認無誤可手動刪除")
        
        conn.close()
        
        print(f"\n✅ 欄位重新排列完成！")
        print(f"📋 新的欄位順序: stock_id, stock_name, date, ...")
        return True
        
    except Exception as e:
        print(f"❌ 重新排列失敗: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果失敗，嘗試恢復備份
        try:
            conn = sqlite3.connect(cash_flows_db)
            cursor = conn.cursor()
            
            # 檢查是否有備份表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cash_flows_backup'")
            if cursor.fetchone():
                print("🔄 嘗試從備份恢復...")
                cursor.execute("DROP TABLE IF EXISTS cash_flows")
                cursor.execute("ALTER TABLE cash_flows_backup RENAME TO cash_flows")
                conn.commit()
                print("✅ 已從備份恢復原始表格")
            
            conn.close()
        except:
            pass
        
        return False

if __name__ == "__main__":
    success = reorder_cash_flows_columns()
    
    if success:
        print(f"\n🎉 cash_flows.db 欄位順序調整完成")
        print(f"📋 新順序: stock_id → stock_name → date → 其他欄位")
    else:
        print(f"\n❌ 欄位順序調整失敗")
