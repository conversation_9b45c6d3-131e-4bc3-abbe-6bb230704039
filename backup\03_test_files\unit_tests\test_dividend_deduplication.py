#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_duplicate_removal():
    """測試重複資料移除效果"""
    
    dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"
    
    if not os.path.exists(dividend_db_path):
        print(f"❌ 除權息資料庫不存在: {dividend_db_path}")
        return
    
    conn = sqlite3.connect(dividend_db_path)
    cursor = conn.cursor()
    
    print("🔍 檢查重複資料情況...")
    
    # 1. 檢查原始重複資料
    print("\n1️⃣ 原始查詢（可能有重複）:")
    cursor.execute("""
        SELECT stock_code, stock_name, ex_dividend_date, cash_dividend, data_source
        FROM dividend_data
        WHERE ex_dividend_date >= '2025-07-15' 
        AND ex_dividend_date <= '2025-07-22'
        AND (data_source IS NULL OR data_source != 'sample_data')
        ORDER BY stock_code, ex_dividend_date
    """)
    
    original_results = cursor.fetchall()
    print(f"原始查詢結果: {len(original_results)} 筆")
    
    # 顯示前10筆
    for i, row in enumerate(original_results[:10]):
        print(f"  {i+1}. {row[0]} {row[1]} {row[2]} {row[3]} ({row[4]})")
    
    # 2. 檢查去重後的資料
    print("\n2️⃣ 去重查詢（使用GROUP BY）:")
    cursor.execute("""
        SELECT stock_code, stock_name, ex_dividend_date, 
               MAX(cash_dividend) as cash_dividend, 
               MAX(data_source) as data_source
        FROM dividend_data
        WHERE ex_dividend_date >= '2025-07-15' 
        AND ex_dividend_date <= '2025-07-22'
        AND (data_source IS NULL OR data_source != 'sample_data')
        GROUP BY stock_code, ex_dividend_date
        ORDER BY stock_code, ex_dividend_date
    """)
    
    dedup_results = cursor.fetchall()
    print(f"去重查詢結果: {len(dedup_results)} 筆")
    
    # 顯示前10筆
    for i, row in enumerate(dedup_results[:10]):
        print(f"  {i+1}. {row[0]} {row[1]} {row[2]} {row[3]} ({row[4]})")
    
    # 3. 分析重複情況
    print(f"\n3️⃣ 重複分析:")
    print(f"原始記錄數: {len(original_results)}")
    print(f"去重記錄數: {len(dedup_results)}")
    print(f"重複記錄數: {len(original_results) - len(dedup_results)}")
    
    if len(original_results) > len(dedup_results):
        print("✅ 成功移除重複資料")
    else:
        print("ℹ️ 沒有發現重複資料")
    
    # 4. 檢查具體重複的股票
    print("\n4️⃣ 重複股票詳情:")
    cursor.execute("""
        SELECT stock_code, stock_name, ex_dividend_date, COUNT(*) as count
        FROM dividend_data
        WHERE ex_dividend_date >= '2025-07-15' 
        AND ex_dividend_date <= '2025-07-22'
        AND (data_source IS NULL OR data_source != 'sample_data')
        GROUP BY stock_code, ex_dividend_date
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    """)
    
    duplicates = cursor.fetchall()
    if duplicates:
        print(f"發現 {len(duplicates)} 組重複記錄:")
        for dup in duplicates:
            print(f"  {dup[0]} {dup[1]} {dup[2]} - 重複 {dup[3]} 次")
    else:
        print("✅ 沒有發現重複記錄")
    
    conn.close()

if __name__ == "__main__":
    test_duplicate_removal()
