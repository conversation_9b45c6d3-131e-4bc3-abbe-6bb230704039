#!/usr/bin/env python3
"""
測試調整後的二次創高策略參數
"""

def test_adjusted_parameters():
    """測試調整後的參數設定"""
    try:
        print("🔧 二次創高策略參數調整")
        print("=" * 50)
        
        # 原始參數 vs 調整後參數
        original_params = {
            "創新高期間": 60,
            "前期檢查": 30,
            "早期檢查": 25,
            "長期趨勢1": 120,
            "長期趨勢2": 60,
            "營收短期": 3,
            "營收長期": 12,
            "成交量短期": 5,
            "成交量長期": 20,
            "均線期間": 20
        }
        
        adjusted_params = {
            "創新高期間": 40,
            "前期檢查": 20,
            "早期檢查": 15,
            "長期趨勢1": 60,
            "長期趨勢2": 30,
            "營收短期": 2,
            "營收長期": 6,
            "成交量短期": 3,
            "成交量長期": 10,
            "均線期間": 10
        }
        
        print("📊 參數對比:")
        print(f"{'參數名稱':<12} {'原始值':<8} {'調整值':<8} {'變化':<10}")
        print("-" * 45)
        
        for param_name in original_params.keys():
            original = original_params[param_name]
            adjusted = adjusted_params[param_name]
            change = f"{adjusted-original:+d}日" if original != adjusted else "無變化"
            print(f"{param_name:<12} {original:<8} {adjusted:<8} {change:<10}")
        
        print(f"\n🎯 調整理由:")
        adjustments = [
            "創新高期間：60→40日，降低數據要求",
            "前期檢查：30→20日，縮短整理期間要求", 
            "早期檢查：25→15日，減少歷史數據需求",
            "長期趨勢1：120→60日，從4個月縮短為2個月",
            "長期趨勢2：60→30日，從2個月縮短為1個月",
            "營收比較：3vs12月→2vs6月，降低營收數據要求",
            "成交量比較：5vs20日→3vs10日，縮短成交量比較期間",
            "均線期間：20→10日，提高賣出信號敏感度"
        ]
        
        for i, adjustment in enumerate(adjustments, 1):
            print(f"{i}. {adjustment}")
        
        print(f"\n📈 條件寬鬆化:")
        relaxations = [
            "評分制：從8個條件全部通過→6分以上通過",
            "核心條件：只要求「近日創高」+「價格突破」必須通過",
            "數據要求：最少需要60日數據（原120日）",
            "營收檢查：使用代理指標，更容易通過",
            "容錯機制：部分條件不通過仍可能符合策略"
        ]
        
        for i, relaxation in enumerate(relaxations, 1):
            print(f"{i}. {relaxation}")
        
        print(f"\n⚡ 預期效果:")
        effects = [
            "✅ 降低數據要求：從120日→60日",
            "✅ 提高通過率：評分制取代全通過制",
            "✅ 保持核心邏輯：二次創高概念不變",
            "✅ 增加靈活性：部分條件可以不滿足",
            "✅ 更適合台股：參數更符合台股特性"
        ]
        
        for effect in effects:
            print(f"  {effect}")
        
        # 計算理論通過率提升
        print(f"\n📊 理論通過率分析:")
        print(f"原始策略：8個條件全部通過 = (假設每個條件50%通過率)")
        original_rate = 0.5 ** 8 * 100
        print(f"          理論通過率 ≈ {original_rate:.3f}%")
        
        print(f"調整策略：6分以上 + 核心條件通過")
        # 假設核心條件通過率60%，其他條件平均通過率提升到60%
        # 需要至少6個條件通過的組合
        adjusted_rate = 0.6 * 0.6 * 15  # 簡化計算
        print(f"          理論通過率 ≈ {adjusted_rate:.1f}%")
        print(f"          提升倍數 ≈ {adjusted_rate/original_rate:.0f}倍")
        
        print(f"\n🎯 建議使用場景:")
        scenarios = [
            "多頭市場：大盤處於上升趨勢",
            "突破行情：個股開始突破整理區間", 
            "成長股行情：市場偏好成長股",
            "中短期操作：持有週期1-3個月",
            "動能投資：追求價格動能的投資者"
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"{i}. {scenario}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_strategy_logic():
    """測試策略邏輯"""
    print(f"\n🧠 策略邏輯測試:")
    print("=" * 50)
    
    # 模擬條件通過情況
    test_cases = [
        {
            "name": "完美案例",
            "conditions": [True, True, True, True, True, True, True, True],
            "sell_signal": False,
            "expected": True
        },
        {
            "name": "核心條件通過，6分",
            "conditions": [True, True, False, True, True, True, False, False],
            "sell_signal": False, 
            "expected": True
        },
        {
            "name": "核心條件通過，5分",
            "conditions": [True, False, False, True, True, True, False, False],
            "sell_signal": False,
            "expected": False
        },
        {
            "name": "缺少核心條件",
            "conditions": [False, True, True, True, True, True, True, True],
            "sell_signal": False,
            "expected": False
        },
        {
            "name": "有賣出信號",
            "conditions": [True, True, True, True, True, True, True, True],
            "sell_signal": True,
            "expected": False
        }
    ]
    
    condition_names = ["近日創高", "前期非新高", "早期創高", "價格突破", 
                      "120日趨勢", "60日趨勢", "營收成長", "成交量確認"]
    
    for case in test_cases:
        print(f"\n📋 {case['name']}:")
        
        # 計算分數
        score = sum(case['conditions'])
        
        # 檢查核心條件
        core_pass = case['conditions'][0] and case['conditions'][3]  # 近日創高 + 價格突破
        
        # 判斷結果
        suitable = (score >= 6) and core_pass and not case['sell_signal']
        
        print(f"  總分: {score}/8")
        print(f"  核心條件: {'✅' if core_pass else '❌'}")
        print(f"  賣出信號: {'⚠️' if case['sell_signal'] else '✅'}")
        print(f"  預期結果: {'✅通過' if case['expected'] else '❌不通過'}")
        print(f"  實際結果: {'✅通過' if suitable else '❌不通過'}")
        print(f"  測試結果: {'✅正確' if suitable == case['expected'] else '❌錯誤'}")
        
        # 顯示通過的條件
        passed_conditions = [name for name, passed in zip(condition_names, case['conditions']) if passed]
        print(f"  通過條件: {', '.join(passed_conditions) if passed_conditions else '無'}")

if __name__ == "__main__":
    success = test_adjusted_parameters()
    if success:
        test_strategy_logic()
        print(f"\n🎉 二次創高策略調整完成！")
        print(f"現在策略應該更容易找到符合條件的股票了！")
    else:
        print(f"\n💥 參數調整測試失敗！")
