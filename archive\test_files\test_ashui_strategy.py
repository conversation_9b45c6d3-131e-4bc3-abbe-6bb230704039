#!/usr/bin/env python3
"""
阿水一式策略测试脚本
测试新实现的阿水一式策略是否能正常工作
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AshuiStrategyTester:
    """阿水一式策略测试器"""
    
    def __init__(self):
        self.name = "阿水一式策略测试"
    
    def generate_test_data(self, days=150):
        """生成测试数据"""
        # 创建日期范围
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        
        # 生成基础价格数据（模拟股票走势）
        np.random.seed(42)  # 固定随机种子以便重现
        
        # 模拟一个先盘整后突破的股票
        base_price = 50
        prices = []
        volumes = []
        
        for i in range(days):
            if i < 100:  # 前100天盘整
                # 盘整期：小幅波动
                price_change = np.random.normal(0, 0.3)
                volume = np.random.normal(100000, 20000)
            elif i < 140:  # 压缩期
                # 压缩期：更小波动，为突破做准备
                price_change = np.random.normal(0, 0.1)
                volume = np.random.normal(60000, 10000)  # 成交量萎缩
            else:  # 突破期 - 最后10天
                # 突破期：强力向上突破 + 大量
                if i == days - 1:  # 最后一天：完美的阿水一式信号
                    price_change = 3.0  # 强力突破
                    volume = np.random.normal(500000, 50000)  # 大量突破
                else:
                    price_change = np.random.normal(0.8, 0.5)
                    volume = np.random.normal(200000, 30000)
            
            if i == 0:
                price = base_price
            else:
                price = max(prices[-1] + price_change, 10)  # 确保价格不低于10
            
            prices.append(price)
            volumes.append(max(volume, 10000))  # 确保成交量不为负
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': dates,
            'Open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
            'High': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
            'Low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
            'Close': prices,
            'Volume': volumes
        })
        
        # 确保OHLC逻辑正确
        for i in range(len(df)):
            high = max(df.iloc[i]['Open'], df.iloc[i]['Close'])
            low = min(df.iloc[i]['Open'], df.iloc[i]['Close'])
            df.at[i, 'High'] = max(df.iloc[i]['High'], high)
            df.at[i, 'Low'] = min(df.iloc[i]['Low'], low)
        
        return df
    
    def calculate_ashui_indicators(self, df):
        """计算阿水一式所需的技术指标"""
        # 移动平均线系统
        df['MA20'] = df['Close'].rolling(window=20).mean()
        df['MA60'] = df['Close'].rolling(window=60).mean()
        df['MA120'] = df['Close'].rolling(window=120).mean()
        
        # 20日标准差
        df['STD20'] = df['Close'].rolling(window=20).std()
        
        # 布林带（使用2.1倍标准差）
        df['BB_Upper'] = df['MA20'] + (df['STD20'] * 2.1)
        df['BB_Lower'] = df['MA20'] - (df['STD20'] * 2.1)
        df['BB_Width_Ratio'] = (df['BB_Upper'] - df['BB_Lower']) / df['MA20']
        
        # 成交量移动平均
        df['Volume_MA5'] = df['Volume'].rolling(window=5).mean()
        
        # 新高检查
        df['High_20'] = df['High'].rolling(window=20).max()
        df['High_60'] = df['High'].rolling(window=60).max()
        
        # MA趋势检查（20MA翻扬）
        df['MA20_Rising'] = df['MA20'] > df['MA20'].shift(1)
        df['MA20_Rising_3Days'] = (
            (df['MA20'] > df['MA20'].shift(1)) & 
            (df['MA20'].shift(1) > df['MA20'].shift(2)) &
            (df['MA20'].shift(2) > df['MA20'].shift(3))
        )
        
        return df
    
    def check_ashui_basic_filter(self, df):
        """基本筛选"""
        latest = df.iloc[-1]
        close_price = latest['Close']
        volume = latest['Volume']
        volume_ma5 = latest['Volume_MA5']
        
        # 总成交额 > 1000万
        turnover = close_price * volume / 10000
        if turnover < 1000:
            return False, f"成交额{turnover:.0f}万 < 1000万"
        
        # 成交量 > 五日均量 2倍以上
        if volume_ma5 <= 0:
            return False, "五日均量数据异常"
        
        volume_ratio = volume / volume_ma5
        if volume_ratio < 2.0:
            return False, f"成交量{volume_ratio:.1f}倍 < 2倍"
        
        # 避免过低价股
        if close_price < 10:
            return False, f"股价{close_price:.2f} < 10元"
        
        return True, f"基本筛选✓(额{turnover:.0f}万, {volume_ratio:.1f}倍量)"
    
    def check_bollinger_breakout(self, df):
        """布林通道突破"""
        latest = df.iloc[-1]
        close_price = latest['Close']
        bb_upper = latest['BB_Upper']
        
        if close_price < bb_upper:
            return False, f"收盘{close_price:.2f}未突破上轨{bb_upper:.2f}"
        
        breakout_pct = ((close_price - bb_upper) / bb_upper) * 100
        return True, f"布林突破✓(+{breakout_pct:.2f}%)"
    
    def check_ashui_volume(self, df):
        """成交量检查"""
        latest = df.iloc[-1]
        current_volume = latest['Volume']
        volume_ma5 = latest['Volume_MA5']
        
        if volume_ma5 <= 0:
            return False, "5日均量数据异常"
        
        volume_ratio = current_volume / volume_ma5
        
        if volume_ratio <= 1.0:
            return False, f"成交量{volume_ratio:.1f}倍 <= 1倍"
        
        if volume_ratio > 10:
            return False, f"成交量{volume_ratio:.1f}倍 > 10倍（可能过多人追涨）"
        
        if volume_ratio >= 2 and volume_ratio <= 5:
            quality = "理想"
        elif volume_ratio > 5 and volume_ratio <= 10:
            quality = "可接受"
        else:
            quality = "一般"
        
        return True, f"成交量✓({volume_ratio:.1f}倍, {quality})"
    
    def check_ashui_technical(self, df):
        """技术面检查"""
        latest = df.iloc[-1]
        ma20 = latest['MA20']
        ma60 = latest['MA60']
        ma120 = latest['MA120']
        ma20_rising = latest['MA20_Rising']
        
        conditions_met = []
        
        # 多头排列
        if ma20 > ma60 > ma120:
            conditions_met.append("多头排列")
        elif ma20 > ma60:
            conditions_met.append("短期多头")
        else:
            return False, f"均线排列不佳：MA20({ma20:.2f}) <= MA60({ma60:.2f})"
        
        # 20MA翻扬
        if ma20_rising:
            conditions_met.append("20MA翻扬")
        else:
            return False, "20MA未翻扬"
        
        # 3日连续翻扬
        if latest['MA20_Rising_3Days']:
            conditions_met.append("3日连扬")
        
        return True, f"技术面✓({', '.join(conditions_met)})"
    
    def check_new_high(self, df):
        """创新高检查"""
        latest = df.iloc[-1]
        current_high = latest['High']
        high_20 = latest['High_20']
        high_60 = latest['High_60']
        
        if current_high >= high_20:
            if current_high >= high_60:
                return True, "创60日新高"
            else:
                return True, "创20日新高"
        
        return False, "未创新高"
    
    def test_ashui_strategy(self, df):
        """测试阿水一式策略"""
        try:
            print("=" * 60)
            print("🌟 阿水一式策略测试")
            print("=" * 60)
            
            # 计算指标
            df = self.calculate_ashui_indicators(df)
            
            # 显示最新数据
            latest = df.iloc[-1]
            print(f"📊 最新数据：")
            print(f"   收盘价: {latest['Close']:.2f}")
            print(f"   成交量: {latest['Volume']:,.0f}")
            print(f"   布林上轨: {latest['BB_Upper']:.2f}")
            print(f"   MA20: {latest['MA20']:.2f}")
            print(f"   MA60: {latest['MA60']:.2f}")
            print()
            
            # 逐步检查条件
            print("🔍 条件检查：")
            
            # 1. 基本筛选
            basic_result = self.check_ashui_basic_filter(df)
            print(f"   1. 基本筛选: {'✅' if basic_result[0] else '❌'} {basic_result[1]}")
            
            # 2. 布林突破
            breakout_result = self.check_bollinger_breakout(df)
            print(f"   2. 布林突破: {'✅' if breakout_result[0] else '❌'} {breakout_result[1]}")
            
            # 3. 成交量
            volume_result = self.check_ashui_volume(df)
            print(f"   3. 成交量: {'✅' if volume_result[0] else '❌'} {volume_result[1]}")
            
            # 4. 技术面
            technical_result = self.check_ashui_technical(df)
            print(f"   4. 技术面: {'✅' if technical_result[0] else '❌'} {technical_result[1]}")
            
            # 5. 创新高
            new_high_result = self.check_new_high(df)
            print(f"   5. 创新高: {'✅' if new_high_result[0] else '❌'} {new_high_result[1]}")
            
            # 综合判断
            all_core_conditions = all([
                basic_result[0],
                breakout_result[0], 
                volume_result[0],
                technical_result[0]
            ])
            
            print()
            print("📈 最终结果：")
            if all_core_conditions:
                print("🎉 ✅ 符合阿水一式策略！这是一个潜在的飙股信号！")
                if new_high_result[0]:
                    print(f"🚀 额外加分：{new_high_result[1]}")
            else:
                print("❌ 不符合阿水一式策略")
                failed_conditions = []
                if not basic_result[0]: failed_conditions.append("基本筛选")
                if not breakout_result[0]: failed_conditions.append("布林突破")
                if not volume_result[0]: failed_conditions.append("成交量")
                if not technical_result[0]: failed_conditions.append("技术面")
                print(f"   未满足条件: {', '.join(failed_conditions)}")
            
            print("=" * 60)
            return all_core_conditions
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 启动阿水一式策略测试")
    
    tester = AshuiStrategyTester()
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_df = tester.generate_test_data(150)
    
    # 运行测试
    result = tester.test_ashui_strategy(test_df)
    
    print(f"\n🏁 测试完成，结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    main()
