#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專案清理維護腳本
用於定期清理專案中的臨時檔案、快取檔案和測試檔案
"""

import os
import shutil
import glob
from datetime import datetime

def cleanup_cache_files():
    """清理快取檔案"""
    print("🧹 清理快取檔案...")
    
    # 清理Python快取
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print(f"  ✅ 已清理: {pycache_path}")
            except Exception as e:
                print(f"  ❌ 清理失敗: {pycache_path} - {e}")
    
    # 清理.pyc檔案
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  ✅ 已清理: {pyc_file}")
        except Exception as e:
            print(f"  ❌ 清理失敗: {pyc_file} - {e}")

def cleanup_log_files():
    """清理舊的日誌檔案（保留最近7天）"""
    print("📋 清理舊日誌檔案...")
    
    log_patterns = ['*.log', 'logs/*.log', 'logs/**/*.log']
    for pattern in log_patterns:
        log_files = glob.glob(pattern, recursive=True)
        for log_file in log_files:
            try:
                # 檢查檔案修改時間
                file_time = os.path.getmtime(log_file)
                current_time = datetime.now().timestamp()
                
                # 如果檔案超過7天，則刪除
                if (current_time - file_time) > (7 * 24 * 3600):
                    os.remove(log_file)
                    print(f"  ✅ 已清理舊日誌: {log_file}")
            except Exception as e:
                print(f"  ❌ 清理失敗: {log_file} - {e}")

def cleanup_temp_files():
    """清理臨時檔案"""
    print("🗂️ 清理臨時檔案...")
    
    temp_patterns = [
        '*.tmp',
        '*.temp',
        '*~',
        '.DS_Store',
        'Thumbs.db',
        '*.bak'
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern, recursive=True)
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"  ✅ 已清理臨時檔案: {temp_file}")
            except Exception as e:
                print(f"  ❌ 清理失敗: {temp_file} - {e}")

def check_backup_integrity():
    """檢查備份區完整性"""
    print("🔍 檢查備份區完整性...")
    
    backup_dirs = [
        'backup/01_main_program_backups',
        'backup/02_strategy_backups',
        'backup/03_test_files',
        'backup/04_reports_and_docs',
        'backup/05_data_backups',
        'backup/06_build_and_deploy',
        'backup/07_logs_and_cache',
        'backup/08_standalone_tools',
        'backup/09_component_backups',
        'backup/10_temp_and_working',
        'backup/11_external_resources'
    ]
    
    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            file_count = sum([len(files) for r, d, files in os.walk(backup_dir)])
            print(f"  ✅ {backup_dir}: {file_count} 個檔案")
        else:
            print(f"  ❌ 備份目錄不存在: {backup_dir}")

def generate_project_summary():
    """生成專案摘要"""
    print("📊 生成專案摘要...")
    
    # 統計核心檔案
    core_files = []
    for root, dirs, files in os.walk('.'):
        # 跳過備份區和快取目錄
        if 'backup' in root or '__pycache__' in root or 'archive' in root:
            continue
        
        for file in files:
            if file.endswith('.py'):
                core_files.append(os.path.join(root, file))
    
    # 統計備份檔案
    backup_files = []
    if os.path.exists('backup'):
        for root, dirs, files in os.walk('backup'):
            backup_files.extend(files)
    
    summary = f"""
# 專案結構摘要 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 檔案統計
- 核心Python檔案: {len(core_files)} 個
- 備份檔案: {len(backup_files)} 個
- 清理比例: {len(backup_files)/(len(core_files)+len(backup_files))*100:.1f}%

## 🎯 核心模組
- 主程式: O3mh_gui_v21_optimized.py
- 策略模組: {len(glob.glob('strategies/*.py'))} 個
- 對話框模組: {len(glob.glob('dialogs/*.py'))} 個
- 核心工具: {len(glob.glob('core/*.py'))} 個
- 數據模組: {len(glob.glob('data/*.py'))} 個

## 🗂️ 備份區狀態
"""
    
    for i, backup_dir in enumerate(['01_main_program_backups', '02_strategy_backups', 
                                   '03_test_files', '04_reports_and_docs', '05_data_backups'], 1):
        backup_path = f'backup/{backup_dir}'
        if os.path.exists(backup_path):
            file_count = sum([len(files) for r, d, files in os.walk(backup_path)])
            summary += f"- {backup_dir}: {file_count} 個檔案\n"
    
    # 寫入摘要檔案
    with open('專案結構摘要.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"  ✅ 專案摘要已生成: 專案結構摘要.md")
    print(f"  📊 核心檔案: {len(core_files)} 個")
    print(f"  📁 備份檔案: {len(backup_files)} 個")

def main():
    """主函數"""
    print("🚀 開始專案清理維護...")
    print("=" * 50)
    
    try:
        cleanup_cache_files()
        print()
        
        cleanup_log_files()
        print()
        
        cleanup_temp_files()
        print()
        
        check_backup_integrity()
        print()
        
        generate_project_summary()
        print()
        
        print("=" * 50)
        print("✅ 專案清理維護完成！")
        
    except Exception as e:
        print(f"❌ 清理過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
