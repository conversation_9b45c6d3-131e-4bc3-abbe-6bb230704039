#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
月營收下載器診斷工具
幫助找出下載失敗的原因
"""

import os
import sys
import time
import logging
from datetime import datetime

def setup_logging():
    """設置詳細日誌"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('monthly_revenue_debug.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def test_selenium_basic():
    """測試基本Selenium功能"""
    logger = logging.getLogger(__name__)
    logger.info("🔧 測試基本Selenium功能...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # 設置Chrome選項
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        logger.info("正在啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=options)
        
        # 測試訪問Google
        logger.info("測試訪問Google...")
        driver.get("https://www.google.com")
        
        if "Google" in driver.title:
            logger.info("✅ Chrome瀏覽器工作正常")
            driver.quit()
            return True
        else:
            logger.error("❌ Chrome瀏覽器無法正常訪問網站")
            driver.quit()
            return False
            
    except Exception as e:
        logger.error(f"❌ Selenium測試失敗: {e}")
        return False

def test_goodinfo_access():
    """測試GoodInfo網站訪問"""
    logger = logging.getLogger(__name__)
    logger.info("🌐 測試GoodInfo網站訪問...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # 設置Chrome選項
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        driver = webdriver.Chrome(options=options)
        
        # 訪問台積電月營收頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        logger.info(f"正在訪問: {url}")
        
        driver.get(url)
        time.sleep(5)  # 等待頁面載入
        
        # 檢查頁面標題
        title = driver.title
        logger.info(f"頁面標題: {title}")
        
        # 檢查是否包含營收相關內容
        page_source = driver.page_source
        
        if "營收" in page_source or "月營收" in page_source:
            logger.info("✅ 頁面包含營收相關內容")
            
            # 尋找匯出按鈕
            export_buttons = []
            
            # 嘗試不同的選擇器
            selectors = [
                "//input[@value='XLS']",
                "//input[contains(@value, 'XLS')]",
                "//input[contains(@value, '匯出')]",
                "//button[contains(text(), 'XLS')]",
                "//a[contains(text(), 'XLS')]"
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        export_buttons.extend(elements)
                        logger.info(f"找到匯出按鈕: {selector}")
                except:
                    continue
            
            if export_buttons:
                logger.info(f"✅ 找到 {len(export_buttons)} 個匯出按鈕")
                
                # 顯示按鈕資訊
                for i, btn in enumerate(export_buttons):
                    try:
                        value = btn.get_attribute("value")
                        text = btn.text
                        tag = btn.tag_name
                        logger.info(f"按鈕 {i+1}: 標籤={tag}, 值={value}, 文字={text}")
                    except:
                        pass
                
                driver.quit()
                return True
            else:
                logger.warning("⚠️ 未找到匯出按鈕")
                
                # 保存頁面源碼用於調試
                with open("goodinfo_page_source.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                logger.info("頁面源碼已保存到 goodinfo_page_source.html")
                
                driver.quit()
                return False
        else:
            logger.error("❌ 頁面不包含營收內容，可能被重定向或阻擋")
            
            # 保存頁面源碼
            with open("goodinfo_error_page.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            logger.info("錯誤頁面源碼已保存到 goodinfo_error_page.html")
            
            driver.quit()
            return False
            
    except Exception as e:
        logger.error(f"❌ GoodInfo訪問測試失敗: {e}")
        return False

def test_download_directory():
    """測試下載目錄"""
    logger = logging.getLogger(__name__)
    logger.info("📁 測試下載目錄...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        download_dir = downloader.download_dir
        
        logger.info(f"下載目錄: {download_dir}")
        
        # 檢查目錄是否存在
        if os.path.exists(download_dir):
            logger.info("✅ 下載目錄存在")
            
            # 檢查寫入權限
            test_file = os.path.join(download_dir, "test_write.txt")
            try:
                with open(test_file, "w") as f:
                    f.write("test")
                os.remove(test_file)
                logger.info("✅ 下載目錄有寫入權限")
                return True
            except Exception as e:
                logger.error(f"❌ 下載目錄無寫入權限: {e}")
                return False
        else:
            logger.warning("⚠️ 下載目錄不存在，嘗試創建...")
            try:
                os.makedirs(download_dir, exist_ok=True)
                logger.info("✅ 下載目錄創建成功")
                return True
            except Exception as e:
                logger.error(f"❌ 無法創建下載目錄: {e}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 下載目錄測試失敗: {e}")
        return False

def test_database_connection():
    """測試數據庫連接"""
    logger = logging.getLogger(__name__)
    logger.info("🗄️ 測試數據庫連接...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        db_path = downloader.db_path
        
        logger.info(f"數據庫路徑: {db_path}")
        
        # 檢查數據庫文件
        if os.path.exists(db_path):
            logger.info("✅ 數據庫文件存在")
        else:
            logger.info("ℹ️ 數據庫文件不存在，將自動創建")
        
        # 測試數據庫連接
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_revenue'")
        if cursor.fetchone():
            logger.info("✅ monthly_revenue 表格存在")
            
            # 檢查記錄數
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
            count = cursor.fetchone()[0]
            logger.info(f"✅ 數據庫包含 {count} 筆記錄")
        else:
            logger.info("ℹ️ monthly_revenue 表格不存在，將自動創建")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 數據庫測試失敗: {e}")
        return False

def run_enhanced_download_test():
    """運行增強的下載測試"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 運行增強的下載測試...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 設置更詳細的日誌
        downloader.logger.setLevel(logging.DEBUG)
        
        # 嘗試下載台積電數據
        logger.info("正在嘗試下載台積電(2330)月營收數據...")
        
        result = downloader.download_stock_revenue('2330')
        
        if result:
            logger.info(f"✅ 下載成功，獲得 {result} 筆數據")
            return True
        else:
            logger.error("❌ 下載失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 增強下載測試失敗: {e}")
        return False

def main():
    """主診斷函數"""
    print("🔍 月營收下載器診斷工具")
    print("=" * 60)
    
    # 設置日誌
    logger = setup_logging()
    logger.info("開始診斷...")
    
    tests = [
        ("Selenium基本功能", test_selenium_basic),
        ("GoodInfo網站訪問", test_goodinfo_access),
        ("下載目錄", test_download_directory),
        ("數據庫連接", test_database_connection),
        ("增強下載測試", run_enhanced_download_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 通過")
            else:
                logger.error(f"❌ {test_name} 失敗")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 異常: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # 短暫延遲
    
    # 總結
    logger.info("\n" + "=" * 60)
    logger.info("📊 診斷結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 總計: {passed}/{len(results)} 項測試通過")
    
    if passed < len(results):
        logger.info("\n🔧 可能的解決方案:")
        logger.info("1. 確認Chrome瀏覽器已安裝")
        logger.info("2. 更新ChromeDriver: pip install --upgrade selenium")
        logger.info("3. 檢查網絡連接")
        logger.info("4. 檢查防火牆設置")
        logger.info("5. 嘗試使用VPN")
        logger.info("6. 檢查GoodInfo網站是否有反爬蟲機制")
    
    logger.info(f"\n📝 詳細日誌已保存到: monthly_revenue_debug.log")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    input("\n按Enter鍵退出...")
    sys.exit(0 if success else 1)
