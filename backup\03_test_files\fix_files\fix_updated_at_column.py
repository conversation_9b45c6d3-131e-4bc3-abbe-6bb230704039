#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專門修復 updated_at 欄位的 DEFAULT 問題
"""

import sqlite3
import os

def fix_updated_at_column():
    """修復 updated_at 欄位問題"""
    
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    
    print("🔧 修復 updated_at 欄位問題")
    print("=" * 50)
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫檔案不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查現有表格結構
        cursor.execute("PRAGMA table_info(monthly_revenue)")
        columns_info = cursor.fetchall()
        existing_columns = [col[1] for col in columns_info]
        
        print(f"📋 現有欄位: {existing_columns}")
        
        # 檢查是否已有 updated_at 欄位
        if 'updated_at' in existing_columns:
            print("✅ updated_at 欄位已存在")
            
            # 檢查是否有數據沒有 updated_at 值
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue WHERE updated_at IS NULL")
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"🔄 發現 {null_count} 筆記錄的 updated_at 為空，正在修復...")
                
                # 為空的 updated_at 設定當前時間
                cursor.execute('''
                    UPDATE monthly_revenue 
                    SET updated_at = CURRENT_TIMESTAMP 
                    WHERE updated_at IS NULL
                ''')
                
                conn.commit()
                print(f"✅ 已修復 {null_count} 筆記錄的 updated_at 欄位")
            else:
                print("✅ 所有記錄的 updated_at 都有值")
        else:
            print("🔄 添加 updated_at 欄位...")
            
            # 添加 updated_at 欄位（不使用 DEFAULT）
            cursor.execute('ALTER TABLE monthly_revenue ADD COLUMN updated_at TIMESTAMP')
            
            # 為所有現有記錄設定 updated_at
            cursor.execute('''
                UPDATE monthly_revenue 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE updated_at IS NULL
            ''')
            
            conn.commit()
            print("✅ 成功添加 updated_at 欄位並設定初始值")
        
        # 驗證結果
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue WHERE updated_at IS NOT NULL")
        valid_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
        total_count = cursor.fetchone()[0]
        
        print(f"\n📊 驗證結果:")
        print(f"  總記錄數: {total_count:,}")
        print(f"  有 updated_at 的記錄: {valid_count:,}")
        print(f"  完整性: {valid_count/total_count*100:.1f}%" if total_count > 0 else "  完整性: N/A")
        
        # 顯示範例數據
        cursor.execute('''
            SELECT stock_id, stock_name, revenue, updated_at 
            FROM monthly_revenue 
            WHERE updated_at IS NOT NULL 
            LIMIT 3
        ''')
        
        samples = cursor.fetchall()
        if samples:
            print(f"\n🔍 範例數據:")
            for stock_id, stock_name, revenue, updated_at in samples:
                print(f"  {stock_id} {stock_name} 營收:{revenue:,} 更新:{updated_at}")
        
        conn.close()
        
        print(f"\n🎉 updated_at 欄位修復完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_insert_with_updated_at():
    """測試插入數據時使用 updated_at"""
    
    print(f"\n🧪 測試 updated_at 欄位功能")
    print("=" * 40)
    
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試插入數據
        test_data = {
            'stock_id': 'TEST2',
            'stock_name': '測試股票2',
            'industry': '測試產業',
            'market': '測試市場',
            'year': 2024,
            'month': 12,
            'revenue': 2000000,
            'revenue_mom': 3.5,
            'revenue_yoy': 8.2,
            'cumulative_revenue': 24000000,
            'cumulative_revenue_yoy': 6.8,
            'remark': '測試備註2'
        }
        
        # 使用手動設定 CURRENT_TIMESTAMP 的方式
        cursor.execute('''
            INSERT OR REPLACE INTO monthly_revenue
            (stock_id, stock_name, industry, market, year, month, revenue,
             revenue_mom, revenue_yoy, cumulative_revenue, cumulative_revenue_yoy,
             remark, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            test_data['stock_id'],
            test_data['stock_name'],
            test_data['industry'],
            test_data['market'],
            test_data['year'],
            test_data['month'],
            test_data['revenue'],
            test_data['revenue_mom'],
            test_data['revenue_yoy'],
            test_data['cumulative_revenue'],
            test_data['cumulative_revenue_yoy'],
            test_data['remark']
        ))
        
        conn.commit()
        
        # 驗證插入結果
        cursor.execute('''
            SELECT stock_id, stock_name, revenue, updated_at
            FROM monthly_revenue 
            WHERE stock_id = 'TEST2'
        ''')
        
        result = cursor.fetchone()
        if result:
            stock_id, stock_name, revenue, updated_at = result
            print(f"✅ 測試插入成功:")
            print(f"  股票代號: {stock_id}")
            print(f"  股票名稱: {stock_name}")
            print(f"  營收: {revenue:,}")
            print(f"  更新時間: {updated_at}")
            
            # 清理測試數據
            cursor.execute("DELETE FROM monthly_revenue WHERE stock_id = 'TEST2'")
            conn.commit()
            print(f"✅ 測試數據已清理")
        else:
            print(f"❌ 測試插入失敗")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🎯 修復 updated_at 欄位問題")
    print("=" * 60)
    
    # 修復 updated_at 欄位
    success1 = fix_updated_at_column()
    
    if success1:
        # 測試功能
        success2 = test_insert_with_updated_at()
        
        if success2:
            print(f"\n🎊 修復完成！")
            print(f"💡 現在可以正常使用月營收下載功能了")
            print(f"  • MOPS批量下載器不再出現 updated_at 錯誤")
            print(f"  • 所有數據都有正確的更新時間戳")
        else:
            print(f"\n⚠️ 修復完成但測試失敗")
    else:
        print(f"\n❌ 修復失敗")

if __name__ == "__main__":
    main()
