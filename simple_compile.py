#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單編譯腳本 - 直接編譯主程式
"""

import os
import sys
import subprocess
import shutil

def simple_compile():
    """簡單編譯"""
    print("🚀 簡單編譯台股智能選股系統")
    print("=" * 50)
    
    # 檢查主程式文件
    if not os.path.exists('O3mh_gui_v21_optimized.py'):
        print("❌ 找不到主程式文件")
        return False
    
    print("✅ 找到主程式文件")
    
    # 清理舊文件
    if os.path.exists('build'):
        try:
            shutil.rmtree('build')
            print("🧹 清理 build 目錄")
        except:
            print("⚠️ 無法清理 build 目錄")
    
    # 編譯命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=台股智能選股系統_最終修復版',
        '--hidden-import=inspect',
        '--hidden-import=pydoc',
        '--hidden-import=doctest',
        '--hidden-import=difflib',
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=sqlite3',
        '--hidden-import=pyqtgraph',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--exclude-module=PyQt5',
        '--exclude-module=tkinter',
        'O3mh_gui_v21_optimized.py'
    ]
    
    print("🔨 開始編譯...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/台股智能選股系統_最終修復版.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_final_launcher()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            if result.stderr:
                print("錯誤信息:")
                print(result.stderr[:1000])  # 只顯示前1000個字符
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_final_launcher():
    """創建最終啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最終修復版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         最終修復版 - 模組問題已解決
echo ========================================
echo.

if exist "dist\\台股智能選股系統_最終修復版.exe" (
    echo ✅ 找到最終修復版
    echo 🚀 正在啟動...
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_最終修復版.exe"
    
    echo ✅ 最終修復版已啟動！
    echo.
    echo 💡 修復內容：
    echo    ✓ 解決 inspect 模組問題
    echo    ✓ 解決 pydoc 模組問題
    echo    ✓ 解決 doctest 模組問題
    echo    ✓ 優化啟動流程
    echo.
    
) else (
    echo ❌ 錯誤：找不到最終修復版
    echo.
    echo 請重新編譯：
    echo    python simple_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 如果程式無法正常運行，請檢查：
echo    1. 系統環境是否正常
echo    2. 防毒軟體是否阻擋
echo    3. 執行權限是否足夠
echo.

timeout /t 5 >nul
'''
    
    with open('啟動最終修復版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建最終啟動腳本: 啟動最終修復版.bat")

def main():
    """主函數"""
    if simple_compile():
        print("\n🎉 編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/台股智能選股系統_最終修復版.exe")
        print("   - 啟動最終修復版.bat")
        print("\n🚀 使用方法:")
        print("   雙擊執行: 啟動最終修復版.bat")
        print("\n✨ 修復內容:")
        print("   ✓ 在主程式開頭直接修復所有模組問題")
        print("   ✓ 確保 inspect、pydoc、doctest 等模組可用")
        print("   ✓ 優化編譯配置")
    else:
        print("\n❌ 編譯失敗！")
        print("請檢查錯誤信息並重試")

if __name__ == "__main__":
    main()
