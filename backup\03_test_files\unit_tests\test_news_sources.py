#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試各個財經新聞網站的爬取難易度
"""

import requests
import time
import json
from bs4 import BeautifulSoup
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def test_anue_api():
    """測試鉅亨網API"""
    print("🔍 測試鉅亨網 (cnyes.com) API...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "accept": "application/json, text/plain, */*",
            "referer": "https://news.cnyes.com/"
        }
        
        # 測試台股新聞API
        url = "https://api.cnyes.com/media/api/v1/newslist/category/tw_stock?limit=5&page=1"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            news_count = len(data.get('items', {}).get('data', []))
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {news_count} 筆")
            print(f"  🔗 API格式: JSON (結構化)")
            print(f"  🛡️ 反爬蟲: 低")
            return {"success": True, "time": end_time - start_time, "count": news_count, "difficulty": "簡單"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def test_udn_news():
    """測試聯合新聞網"""
    print("\n🔍 測試聯合新聞網 (udn.com)...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 測試財經新聞頁面
        url = "https://udn.com/news/cate/2/6644"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = soup.find_all('h3', class_='story-list__title')
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {len(news_items)} 筆")
            print(f"  🔗 格式: HTML解析")
            print(f"  🛡️ 反爬蟲: 中等")
            return {"success": True, "time": end_time - start_time, "count": len(news_items), "difficulty": "中等"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def test_ctee_news():
    """測試工商時報"""
    print("\n🔍 測試工商時報 (ctee.com.tw)...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 測試財經新聞
        url = "https://www.ctee.com.tw/news/stock"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = soup.find_all('h3') + soup.find_all('h2')
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {len(news_items)} 筆")
            print(f"  🔗 格式: HTML解析")
            print(f"  🛡️ 反爬蟲: 低")
            return {"success": True, "time": end_time - start_time, "count": len(news_items), "difficulty": "簡單"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def test_moneydj_news():
    """測試MoneyDJ"""
    print("\n🔍 測試MoneyDJ (moneydj.com)...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 測試台股新聞
        url = "https://www.moneydj.com/kmdj/news/newslist.aspx?index1=1"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = soup.find_all('a', href=True)
            news_count = len([item for item in news_items if 'newsviewer' in item.get('href', '')])
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {news_count} 筆")
            print(f"  🔗 格式: HTML解析")
            print(f"  🛡️ 反爬蟲: 中等")
            return {"success": True, "time": end_time - start_time, "count": news_count, "difficulty": "中等"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def test_yahoo_finance():
    """測試Yahoo財經"""
    print("\n🔍 測試Yahoo財經 (tw.finance.yahoo.com)...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 測試台股新聞
        url = "https://tw.finance.yahoo.com/news/"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = soup.find_all('h3') + soup.find_all('a', {'data-test-locator': 'mega'})
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {len(news_items)} 筆")
            print(f"  🔗 格式: HTML解析")
            print(f"  🛡️ 反爬蟲: 高 (動態載入)")
            return {"success": True, "time": end_time - start_time, "count": len(news_items), "difficulty": "困難"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def test_economic_daily():
    """測試經濟日報"""
    print("\n🔍 測試經濟日報 (money.udn.com)...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 測試股市新聞
        url = "https://money.udn.com/money/cate/5590"
        
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = soup.find_all('h3') + soup.find_all('h2')
            
            print(f"  ✅ 成功！回應時間: {end_time - start_time:.2f}秒")
            print(f"  📰 獲取新聞數: {len(news_items)} 筆")
            print(f"  🔗 格式: HTML解析")
            print(f"  🛡️ 反爬蟲: 低")
            return {"success": True, "time": end_time - start_time, "count": len(news_items), "difficulty": "簡單"}
        else:
            print(f"  ❌ 失敗！狀態碼: {response.status_code}")
            return {"success": False, "difficulty": "困難"}
            
    except Exception as e:
        print(f"  ❌ 錯誤: {e}")
        return {"success": False, "difficulty": "困難"}

def main():
    """主測試函數"""
    print("🚀 財經新聞網站爬取難易度測試")
    print("=" * 60)
    
    # 測試各個網站
    results = {}
    
    results["鉅亨網"] = test_anue_api()
    time.sleep(1)  # 避免請求過於頻繁
    
    results["聯合新聞網"] = test_udn_news()
    time.sleep(1)
    
    results["工商時報"] = test_ctee_news()
    time.sleep(1)
    
    results["MoneyDJ"] = test_moneydj_news()
    time.sleep(1)
    
    results["Yahoo財經"] = test_yahoo_finance()
    time.sleep(1)
    
    results["經濟日報"] = test_economic_daily()
    
    # 分析結果
    print("\n" + "=" * 60)
    print("📊 測試結果分析")
    print("=" * 60)
    
    successful_sites = []
    for site, result in results.items():
        if result["success"]:
            successful_sites.append((site, result))
            print(f"✅ {site:12} | 回應: {result['time']:.2f}s | 新聞: {result['count']:2d}筆 | 難度: {result['difficulty']}")
        else:
            print(f"❌ {site:12} | 爬取失敗 | 難度: {result['difficulty']}")
    
    # 推薦排序
    print("\n🏆 推薦排序 (綜合考量速度、成功率、難度):")
    
    # 按難度和速度排序
    easy_sites = [(site, result) for site, result in successful_sites if result["difficulty"] == "簡單"]
    medium_sites = [(site, result) for site, result in successful_sites if result["difficulty"] == "中等"]
    
    easy_sites.sort(key=lambda x: x[1]["time"])
    medium_sites.sort(key=lambda x: x[1]["time"])
    
    rank = 1
    for site, result in easy_sites:
        print(f"  {rank}. 🥇 {site} - 簡單易爬，速度快")
        rank += 1
    
    for site, result in medium_sites:
        print(f"  {rank}. 🥈 {site} - 中等難度，需要更多處理")
        rank += 1
    
    print("\n💡 建議:")
    if easy_sites:
        best_site = easy_sites[0][0]
        print(f"  🎯 最佳選擇: {best_site}")
        print(f"  📈 優點: 爬取簡單、速度快、系統負擔小")
        print(f"  🔧 實作: 可以作為主要新聞來源")
    
    if len(successful_sites) > 1:
        print(f"  🔄 備用方案: 可以實作多個來源的輪替機制")
        print(f"  ⚖️ 負載分散: 避免單一網站過度請求")

if __name__ == "__main__":
    main()
