#!/usr/bin/env python3
import os
import sys
import json
import logging
import traceback
import sqlite3
from datetime import datetime, timedelta
import time

import numpy as np
import pandas as pd
import pyqtgraph as pg
from PyQt6.QtCore import Qt, QDate, QPointF, QRectF, QTimer, QThread, pyqtSignal, QThreadPool, QRunnable
from PyQt6.QtGui import QColor, QBrush, QPainter, QPicture, QPen, QFont
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QTabWidget, QGroupBox, QScrollArea, QMessageBox, QDialog, QLineEdit, QFileDialog,
    QListWidget, QComboBox, QCalendarWidget, QTableWidget, QTableWidgetItem,
    QStatusBar, QGridLayout, QSpinBox, QListWidgetItem, QProgressBar, QSplitter, QCheckBox,
    QDialogButtonBox, QFormLayout
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='app.log',
    filemode='a'
)

# ===================== CandlestickItem =====================
class CandlestickItem(pg.GraphicsObject):
    """
    根據提供的數據繪製K線圖
    資料格式：每一列 [timestamp, open, close, low, high]
    """
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = QPicture()
        self.generate_picture()
        
    def generate_picture(self):
        """生成K線圖的繪圖指令"""
        painter = QPainter(self.picture)
        painter.setPen(pg.mkPen('k'))  # 黑色邊框
        w = 0.6  # K線寬度
        
        for i in range(len(self.data)):
            # 獲取OHLC數據
            open_price = float(self.data['Open'].iloc[i])
            high_price = float(self.data['High'].iloc[i])
            low_price = float(self.data['Low'].iloc[i])
            close_price = float(self.data['Close'].iloc[i])
            
            # 台股的顏色習慣：紅色表示上漲，綠色表示下跌
            if close_price > open_price:
                # 上漲，畫紅色K線
                painter.setBrush(pg.mkBrush('r'))
                painter.drawRect(QRectF(i-w/2, open_price, w, close_price-open_price))
            else:
                # 下跌，畫綠色K線
                painter.setBrush(pg.mkBrush('g'))
                painter.drawRect(QRectF(i-w/2, close_price, w, open_price-close_price))
            
            # 畫上下影線
            painter.drawLine(QPointF(i, low_price), QPointF(i, high_price))
        
        painter.end()
        
    def paint(self, painter, option, widget):
        painter.drawPicture(0, 0, self.picture)
        
    def boundingRect(self):
        return QRectF(self.picture.boundingRect())

# ===================== 策略參數與策略類別 =====================
class StrategyParams:
    def __init__(self, name="Default", description="",
                 ma240_days=240, trend_days=30,
                 ma60_days=60, ma20_days=20,
                 volume_short=3, volume_long=18, cross_days=3,
                 rsi_long=13, rsi_long_threshold=50,
                 rsi_short=6, rsi_short_threshold=70):
        self.name = name
        self.description = description
        self.ma240_days = ma240_days
        self.trend_days = trend_days
        self.ma60_days = ma60_days
        self.ma20_days = ma20_days
        self.volume_short = volume_short
        self.volume_long = volume_long
        self.cross_days = cross_days
        self.rsi_long = rsi_long
        self.rsi_long_threshold = rsi_long_threshold
        self.rsi_short = rsi_short
        self.rsi_short_threshold = rsi_short_threshold

    @staticmethod
    def default():
        return StrategyParams()

    def save(self, filename):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.__dict__, f, ensure_ascii=False, indent=4)

    @classmethod
    def load(cls, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if "parameters" in data:
                params = data["parameters"]
            else:
                params = data
            return cls(
                name=data.get("name", "Default"),
                description=data.get("description", ""),
                ma240_days=params.get("ma240_days", 240),
                trend_days=params.get("trend_days", 30),
                ma60_days=params.get("ma60_days", 60),
                ma20_days=params.get("ma20_days", 20),
                volume_short=params.get("volume_short", 3),
                volume_long=params.get("volume_long", 18),
                cross_days=params.get("cross_days", 3),
                rsi_long=params.get("rsi_long", 13),
                rsi_long_threshold=params.get("rsi_long_threshold", 50),
                rsi_short=params.get("rsi_short", 6),
                rsi_short_threshold=params.get("rsi_short_threshold", 70)
            )
        except Exception as e:
            logging.error(f"載入策略參數時出錯: {e}")
            logging.error(traceback.format_exc())
            return cls.default()

class Strategy:
    def __init__(self, params):
        self.params = params

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        delta = df['Close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        df['Signal'] = 0
        for i in range(1, len(df)):
            if df['MA5'].iloc[i] > df['MA20'].iloc[i] and df['MA5'].iloc[i-1] <= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = 1
            elif df['MA5'].iloc[i] < df['MA20'].iloc[i] and df['MA5'].iloc[i-1] >= df['MA20'].iloc[i-1]:
                df.at[df.index[i], 'Signal'] = -1
        return df

# ===================== 策略設定對話框 =====================
class StrategyDialog(QDialog):
    def __init__(self, params: StrategyParams = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("策略設置")
        self.setModal(True)
        layout = QVBoxLayout(self)
        
        # 策略名稱與描述
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("策略名稱:"))
        self.name_edit = QLineEdit()
        if params:
            self.name_edit.setText(params.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("策略描述:"))
        self.desc_edit = QLineEdit()
        if params:
            self.desc_edit.setText(params.description)
        desc_layout.addWidget(self.desc_edit)
        layout.addLayout(desc_layout)
        
        # 參數設置群組（這裡僅示意，可根據需求增加設定項目）
        params_group = QGroupBox("參數設置")
        params_layout = QVBoxLayout(params_group)
        params_layout.addWidget(QLabel("240日線天數:"))
        self.ma240_days_input = QSpinBox()
        self.ma240_days_input.setValue(params.ma240_days if params else 240)
        params_layout.addWidget(self.ma240_days_input)
        params_layout.addWidget(QLabel("趨勢天數:"))
        self.trend_days_input = QSpinBox()
        self.trend_days_input.setValue(params.trend_days if params else 30)
        params_layout.addWidget(self.trend_days_input)
        params_layout.addWidget(QLabel("60日線天數:"))
        self.ma60_days_input = QSpinBox()
        self.ma60_days_input.setValue(params.ma60_days if params else 60)
        params_layout.addWidget(self.ma60_days_input)
        params_layout.addWidget(QLabel("20日線天數:"))
        self.ma20_days_input = QSpinBox()
        self.ma20_days_input.setValue(params.ma20_days if params else 20)
        params_layout.addWidget(self.ma20_days_input)
        params_layout.addWidget(QLabel("短期成交量天數:"))
        self.volume_short_input = QSpinBox()
        self.volume_short_input.setValue(params.volume_short if params else 3)
        params_layout.addWidget(self.volume_short_input)
        params_layout.addWidget(QLabel("長期成交量天數:"))
        self.volume_long_input = QSpinBox()
        self.volume_long_input.setValue(params.volume_long if params else 18)
        params_layout.addWidget(self.volume_long_input)
        params_layout.addWidget(QLabel("交叉天數:"))
        self.cross_days_input = QSpinBox()
        self.cross_days_input.setValue(params.cross_days if params else 3)
        params_layout.addWidget(self.cross_days_input)
        params_layout.addWidget(QLabel("長期RSI天數:"))
        self.rsi_long_input = QSpinBox()
        self.rsi_long_input.setValue(params.rsi_long if params else 13)
        params_layout.addWidget(self.rsi_long_input)
        params_layout.addWidget(QLabel("長期RSI閾值:"))
        self.rsi_long_threshold_input = QSpinBox()
        self.rsi_long_threshold_input.setValue(params.rsi_long_threshold if params else 50)
        params_layout.addWidget(self.rsi_long_threshold_input)
        params_layout.addWidget(QLabel("短期RSI天數:"))
        self.rsi_short_input = QSpinBox()
        self.rsi_short_input.setValue(params.rsi_short if params else 6)
        params_layout.addWidget(self.rsi_short_input)
        params_layout.addWidget(QLabel("短期RSI閾值:"))
        self.rsi_short_threshold_input = QSpinBox()
        self.rsi_short_threshold_input.setValue(params.rsi_short_threshold if params else 70)
        params_layout.addWidget(self.rsi_short_threshold_input)
        
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存")
        save_button.clicked.connect(self.accept)
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def get_strategy_params(self) -> StrategyParams:
        return StrategyParams(
            name=self.name_edit.text(),
            description=self.desc_edit.text(),
            ma240_days=self.ma240_days_input.value(),
            trend_days=self.trend_days_input.value(),
            ma60_days=self.ma60_days_input.value(),
            ma20_days=self.ma20_days_input.value(),
            volume_short=self.volume_short_input.value(),
            volume_long=self.volume_long_input.value(),
            cross_days=self.cross_days_input.value(),
            rsi_long=self.rsi_long_input.value(),
            rsi_long_threshold=self.rsi_long_threshold_input.value(),
            rsi_short=self.rsi_short_input.value(),
            rsi_short_threshold=self.rsi_short_threshold_input.value()
        )

# ===================== 主 GUI 類別 =====================
class MultiDBWorker(QThread):
    initialized = pyqtSignal(dict)  # 發送連接字典 {'price': conn1, 'pe': conn2}
    error = pyqtSignal(str)

    def __init__(self, db_config):
        super().__init__()
        self.db_config = db_config  # {'price': path1, 'pe': path2}

    def run(self):
        connections = {}
        try:
            # 連接價格數據庫
            if 'price' in self.db_config:
                conn = sqlite3.connect(
                    self.db_config['price'],
                    timeout=15,
                    check_same_thread=False
                )
                conn.execute("PRAGMA journal_mode = WAL")
                connections['price'] = conn

            # 連接PE數據庫
            if 'pe' in self.db_config:
                pe_conn = sqlite3.connect(
                    self.db_config['pe'],
                    timeout=15,
                    check_same_thread=False
                )
                pe_conn.execute("PRAGMA journal_mode = WAL")
                connections['pe'] = pe_conn

            self.initialized.emit(connections)
        except Exception as e:
            self.error.emit(f"數據庫連接失敗: {str(e)}")
            logging.error(traceback.format_exc())

class ScreenerWorker(QThread):
    progress_updated = pyqtSignal(int, int, str)  # (current, total, stock_id)
    result_ready = pyqtSignal(pd.DataFrame)

    def __init__(self, stock_ids, strategy, db_connector):
        super().__init__()
        self.stock_ids = stock_ids
        self.strategy = strategy
        self.db_connector = db_connector
        self._is_running = True

    def run(self):
        results = []
        try:
            total = len(self.stock_ids)
            for idx, stock_id in enumerate(self.stock_ids, 1):
                if not self._is_running:
                    break
                
                self.progress_updated.emit(idx, total, stock_id)
                
                try:
                    data = self.db_connector.get_combined_stock_data(stock_id)
                    if not data.empty:
                        # 執行策略分析...
                        if self.apply_strategy(data):  # 替換為實際策略條件
                            results.append({
                                '股票代碼': stock_id,
                                '收盤價': data['Close'].iloc[-1],
                                '條件符合': '是'
                            })
                except Exception as e:
                    logging.error(f"處理 {stock_id} 時出錯: {str(e)}")
                
            self.result_ready.emit(pd.DataFrame(results))
        
        except Exception as e:
            logging.error(f"選股過程出錯: {str(e)}")
            self.result_ready.emit(pd.DataFrame())

    def apply_strategy(self, data):
        """示例策略條件"""
        # 這裡替換為實際策略邏輯
        return len(data) > 100  # 示例條件

    def stop(self):
        self._is_running = False

class WorkerRunnable(QRunnable):
    """通用異步任務執行器"""
    def __init__(self, fn, callback, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.callback = callback
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            result = self.fn(*self.args, **self.kwargs)
            QTimer.singleShot(0, lambda: self.callback(result))
        except Exception as e:
            logging.error(f"執行任務出錯: {str(e)}")
            QTimer.singleShot(0, lambda: self.callback(None))

class StockScreenerGUI(QMainWindow):
    CONFIG_FILE = "app_config.json"
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("台股智能選股系統")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化數據
        self.config = self.load_config()
        self.db_connections = {}
        self.db_tables = {'price': 'stock_daily_data', 'pe': None}
        self.thread_pool = QThreadPool()
        self.period_map = {'1M': 22, '3M': 66, '6M': 132, '1Y': 252, '3Y': 756, '5Y': 1260}
        
        # 初始化UI
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # 初始化狀態列
        self.init_status_bar()
        
        # 初始化主UI元件
        self.init_ui_components()
        
        # 連接數據庫
        self.init_database()
        
        logging.info("GUI 初始化完成")
    
    def init_status_bar(self):
        """初始化狀態列和相關元件"""
        self.status_bar = self.statusBar()
        
        # 數據庫狀態標籤
        self.db_status_label = QLabel("數據庫未連接")
        self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.db_status_label)
        
        # 重新連接按鈕
        self.reconnect_btn = QPushButton("重新連接數據庫")
        self.reconnect_btn.clicked.connect(self.reconnect_db)
        self.status_bar.addPermanentWidget(self.reconnect_btn)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def show_status(self, message, timeout=5000, is_error=False):
        """顯示狀態欄訊息"""
        self.status_bar.showMessage(message, timeout)
        if is_error:
            self.status_bar.setStyleSheet("color: red;")
        else:
            self.status_bar.setStyleSheet("")
        # 確保UI立即更新
        QApplication.processEvents()
    
    def update_db_status(self, connected=True):
        """更新數據庫連接狀態"""
        if connected and 'price' in self.db_connections:
            text = "價格DB: ✓"
            if 'pe' in self.db_connections:
                text += " | PE DB: ✓"
            self.db_status_label.setText(text)
            self.db_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.db_status_label.setText("數據庫未連接")
            self.db_status_label.setStyleSheet("color: red; font-weight: bold;")
    
    def init_ui_components(self):
        """初始化主要UI元件"""
        # 示例：創建一個簡單的選股介面
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(main_splitter)
        
        # 左側面板 - 股票列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 股票列表標題
        list_label = QLabel("股票列表")
        left_layout.addWidget(list_label)
        
        # 股票列表
        self.all_stocks_list = QListWidget()
        self.all_stocks_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        # 調整列表外觀
        self.all_stocks_list.setMinimumWidth(180)
        self.all_stocks_list.setFont(QFont("Microsoft JhengHei", 10))
        self.all_stocks_list.itemDoubleClicked.connect(self.on_stock_double_clicked)
        left_layout.addWidget(self.all_stocks_list)
        
        # 添加到主分割器
        main_splitter.addWidget(left_panel)
        
        # 右側面板 - 選股與圖表
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 添加頁籤
        self.tab_widget = QTabWidget()
        right_layout.addWidget(self.tab_widget)
        
        # 選股頁籤
        screener_tab = QWidget()
        screener_layout = QVBoxLayout(screener_tab)
        
        # 策略選擇
        strategy_layout = QHBoxLayout()
        strategy_label = QLabel("選股策略:")
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItem("均線突破", "ma_cross")
        self.strategy_combo.addItem("成交量放大", "volume_surge")
        strategy_layout.addWidget(strategy_label)
        strategy_layout.addWidget(self.strategy_combo)
        strategy_layout.addStretch()
        
        # 執行選股按鈕
        run_button = QPushButton("執行選股")
        run_button.clicked.connect(self.run_screener)
        strategy_layout.addWidget(run_button)
        
        screener_layout.addLayout(strategy_layout)
        
        # 結果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(4)
        self.result_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "收盤價", "條件"])
        screener_layout.addWidget(self.result_table)
        
        # K線圖頁籤
        chart_tab = QWidget()
        chart_layout = QVBoxLayout(chart_tab)
        
        # 圖表控制區
        chart_control = QHBoxLayout()
        self.period_combo = QComboBox()
        for period in ["1M", "3M", "6M", "1Y", "3Y", "5Y"]:
            self.period_combo.addItem(period)
        self.period_combo.setCurrentText("6M")
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        
        chart_control.addWidget(QLabel("週期:"))
        chart_control.addWidget(self.period_combo)
        chart_control.addStretch()
        chart_layout.addLayout(chart_control)
        
        # 創建一個容器來放置K線圖
        self.chart_container = QWidget()
        self.chart_container_layout = QVBoxLayout(self.chart_container)
        self.chart_container_layout.setContentsMargins(0, 0, 0, 0)
        chart_layout.addWidget(self.chart_container)
        
        # 添加兩個頁籤到TabWidget
        self.tab_widget.addTab(screener_tab, "選股")
        self.tab_widget.addTab(chart_tab, "K線圖")
        
        # 添加到主分割器
        main_splitter.addWidget(right_panel)
        
        # 設定比例
        main_splitter.setSizes([300, 900])
    
    def init_database(self):
        """初始化數據庫連接"""
        try:
            price_db_path = self.resolve_db_path('price')
            pe_db_path = self.resolve_db_path('pe')
            
            self.show_status("正在連接數據庫...")
            
            # 直接在主線程連接數據庫
            self.db_connections = {}
            
            # 連接price.db
            if os.path.exists(price_db_path):
                self.db_connections['price'] = sqlite3.connect(price_db_path)
                # 明確設置表名為stock_daily_data
                self.db_tables['price'] = 'stock_daily_data'
                logging.info(f"成功連接price數據庫: {price_db_path}")
                
                # 驗證表結構
                cursor = self.db_connections['price'].cursor()
                cursor.execute("PRAGMA table_info(stock_daily_data)")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"檢測到的欄位: {columns}")
                
                # 檢查必要欄位
                required_cols = ['stock_id', 'Open', 'High', 'Low', 'Close', 'Volume', 'date']
                missing = [col for col in required_cols if col not in columns]
                if missing:
                    logging.error(f"數據表缺少必要欄位: {missing}")
                else:
                    logging.info("數據表結構驗證通過")
                
            # 連接pe_data.db
            if os.path.exists(pe_db_path):
                self.db_connections['pe'] = sqlite3.connect(pe_db_path)
                self.detect_db_tables('pe')
                
            # 更新狀態並載入股票列表
            if 'price' in self.db_connections:
                self.update_db_status(True)
                self.load_stock_list()
                self.show_status("數據庫連接成功")
            else:
                self.update_db_status(False)
                self.show_status("無法連接到價格數據庫", is_error=True)
                
        except Exception as e:
            self.update_db_status(False)
            self.show_status(f"數據庫連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def detect_db_tables(self, db_type):
        """檢測數據庫表結構"""
        try:
            conn = self.db_connections[db_type]
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            if tables:
                # 使用第一個表作為主表
                self.db_tables[db_type] = tables[0][0]
                
                # 檢查表結構
                cursor.execute(f"PRAGMA table_info({self.db_tables[db_type]})")
                columns = [col[1] for col in cursor.fetchall()]
                logging.info(f"{db_type}數據庫表 {self.db_tables[db_type]} 的欄位: {columns}")
                
            return len(tables) > 0
        except Exception as e:
            logging.error(f"檢測{db_type}數據庫表失敗: {str(e)}")
            return False
    
    def load_stock_list(self):
        """載入股票清單"""
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法載入股票清單", is_error=True)
                return
            
            conn = self.db_connections['price']
            table = self.db_tables['price']
            
            # SQLite不支持原生REGEXP，改用GLOB
            query = f"""
                SELECT DISTINCT stock_id, stock_name 
                FROM {table}
                WHERE stock_id GLOB '[0-9][0-9][0-9][0-9]'
                ORDER BY stock_id
            """
            
            logging.info(f"執行股票列表查詢: {query}")
            df = pd.read_sql_query(query, conn)
            logging.info(f"查詢到 {len(df)} 支股票")
            
            if df.empty:
                self.show_status("未找到股票資料", is_error=True)
                return
            
            # 清空並添加股票列表
            self.all_stocks_list.clear()
            for _, row in df.iterrows():
                # 將股票代號和名稱一起顯示
                item_text = f"{row['stock_id']} {row['stock_name']}"
                self.all_stocks_list.addItem(item_text)
            
            self.show_status(f"載入了 {len(df)} 支股票")
            
        except Exception as e:
            self.show_status(f"載入股票列表失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def get_selected_stocks(self):
        """獲取選中的股票，若無選中則返回全部"""
        selected = []
        for index in range(self.all_stocks_list.count()):
            item = self.all_stocks_list.item(index)
            if item.isSelected():
                selected.append(item.text())
        
        # 若未選中則返回全部
        if not selected:
            for index in range(self.all_stocks_list.count()):
                item = self.all_stocks_list.item(index)
                selected.append(item.text())
                
        return selected
    
    def run_screener(self):
        """執行選股"""
        try:
            if 'price' not in self.db_connections:
                self.show_status("數據庫未連接，無法執行選股", is_error=True)
                return
                
            # 獲取選股策略
            strategy = self.strategy_combo.currentData()
            
            # 獲取股票列表
            stock_ids = self.get_selected_stocks()
            if not stock_ids:
                self.show_status("無可用股票", is_error=True)
                return
                
            self.show_status(f"正在分析 {len(stock_ids)} 支股票...")
            
            # 設置進度條
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(stock_ids))
            self.progress_bar.setValue(0)
            
            # 清空結果表格
            self.result_table.setRowCount(0)
            
            # 依序分析每支股票
            results = []
            for idx, stock_id in enumerate(stock_ids):
                # 更新進度
                self.progress_bar.setValue(idx + 1)
                self.show_status(f"分析中: {stock_id} ({idx+1}/{len(stock_ids)})")
                QApplication.processEvents()  # 確保UI更新
                
                # 獲取股票數據
                try:
                    data = self.get_stock_data(stock_id)
                    
                    # 應用策略
                    if self.apply_strategy(data, strategy):
                        results.append({
                            'stock_id': stock_id,
                            'close': data['Close'].iloc[-1] if not data.empty else 0,
                        })
                except Exception as e:
                    logging.error(f"處理 {stock_id} 時出錯: {str(e)}")
            
            # 顯示結果
            self.show_results(results)
            
            # 隱藏進度條
            self.progress_bar.setVisible(False)
            self.show_status(f"選股完成，找到 {len(results)} 支符合條件的股票")
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.show_status(f"選股過程出錯: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def get_stock_data(self, stock_id, period='6M'):
        """獲取股票數據"""
        try:
            if 'price' not in self.db_connections:
                logging.error("price數據庫未連接")
                return pd.DataFrame()
            
            conn = self.db_connections['price']
            table_name = self.db_tables['price']  # 'stock_daily_data'
            limit = self.period_map.get(period, 132)
            
            # 直接使用SQLite的日期函數
            query = f"""
                SELECT 
                    date,
                    stock_id,
                    stock_name,
                    Open, High, Low, Close,
                    Volume
                FROM {table_name}
                WHERE stock_id = ?
                ORDER BY date DESC
                LIMIT {limit}
            """
            
            logging.info(f"執行查詢: {stock_id}, 期間: {period}")
            df = pd.read_sql_query(query, conn, params=(stock_id,))
            
            if df.empty:
                return pd.DataFrame()
            
            # 處理數據類型
            df['date'] = pd.to_datetime(df['date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 反轉為時間順序
            df = df.iloc[::-1].reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logging.error(f"獲取股票資料失敗: {str(e)}")
            logging.error(traceback.format_exc())
            return pd.DataFrame()
    
    def apply_strategy(self, data, strategy):
        """應用選股策略"""
        if data.empty or len(data) < 20:
            return False
            
        if strategy == 'ma_cross':
            # 計算5日均線和20日均線
            data['MA5'] = data['Close'].rolling(5).mean()
            data['MA20'] = data['Close'].rolling(20).mean()
            
            # 當前5日均線是否上穿20日均線
            if len(data) < 2:
                return False
                
            current = data.iloc[-1]
            previous = data.iloc[-2]
            
            cross_up = previous['MA5'] <= previous['MA20'] and current['MA5'] > current['MA20']
            return cross_up
            
        elif strategy == 'volume_surge':
            # 判斷成交量是否放大
            if len(data) < 5:
                return False
                
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].iloc[-6:-1].mean()
            
            return current_volume > avg_volume * 2  # 成交量是否放大2倍
            
        return False
    
    def show_results(self, results):
        """顯示選股結果"""
        # 清空表格
        self.result_table.setRowCount(0)
        
        if not results:
            return
            
        # 添加結果到表格
        self.result_table.setRowCount(len(results))
        for row, stock in enumerate(results):
            self.result_table.setItem(row, 0, QTableWidgetItem(stock['stock_id']))
            # 添加股票名稱
            if 'stock_name' in stock:
                self.result_table.setItem(row, 1, QTableWidgetItem(stock['stock_name']))
            self.result_table.setItem(row, 2, QTableWidgetItem(str(stock['close'])))
            self.result_table.setItem(row, 3, QTableWidgetItem("符合"))
    
    def resolve_db_path(self, db_type):
        """解析數據庫路徑，優先使用配置文件中的路徑"""
        # 從配置中獲取路徑
        if db_type in self.config and 'path' in self.config[db_type]:
            db_path = self.config[db_type]['path']
            if os.path.exists(db_path):
                return db_path
                
        # 若配置中無路徑或路徑無效，彈出文件選擇對話框
        title = f"選擇 {db_type}.db 數據庫"
        selected_path, _ = QFileDialog.getOpenFileName(
            self, title, "", "SQLite數據庫 (*.db)"
        )
        
        if selected_path:
            # 更新配置
            if db_type not in self.config:
                self.config[db_type] = {}
            self.config[db_type]['path'] = selected_path
            self.save_config()
            return selected_path
            
        return ""
    
    def load_config(self):
        """載入配置文件"""
        if os.path.exists(self.CONFIG_FILE):
            try:
                with open(self.CONFIG_FILE, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_config(self):
        """保存配置文件"""
        with open(self.CONFIG_FILE, 'w') as f:
            json.dump(self.config, f, indent=4)
    
    def reconnect_db(self):
        """重新連接數據庫"""
        try:
            # 關閉現有連接
            for db in self.db_connections.values():
                db.close()
            self.db_connections.clear()
            
            # 重新初始化
            self.init_database()
            
        except Exception as e:
            self.show_status(f"重新連接失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())
    
    def closeEvent(self, event):
        """關閉視窗時釋放資源"""
        # 關閉數據庫連接
        for db in self.db_connections.values():
            try:
                db.close()
            except:
                pass
        
        super().closeEvent(event)

    def on_stock_double_clicked(self, item):
        """雙擊股票時顯示K線圖"""
        # 從列表項提取股票代號（前四位）
        stock_id = item.text().split()[0]
        self.show_status(f"載入 {stock_id} 的K線圖...")
        
        # 切換到K線圖頁籤
        self.tab_widget.setCurrentIndex(1)
        
        # 獲取股票數據
        period = self.period_combo.currentText()
        df = self.get_stock_data(stock_id, period)
        
        if df.empty:
            self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
            return
        
        # 繪製K線圖
        self.plot_candlestick(df, stock_id)
    
    def on_period_changed(self):
        """週期變更時更新圖表"""
        # 如果當前已有選中的股票，則重新繪製
        selected_items = self.all_stocks_list.selectedItems()
        if selected_items:
            self.on_stock_double_clicked(selected_items[0])
    
    def plot_candlestick(self, df, stock_id):
        """繪製K線圖"""
        try:
            # 清空圖表容器
            while self.chart_container_layout.count():
                item = self.chart_container_layout.takeAt(0)
                widget = item.widget()
                if widget:
                    widget.deleteLater()
            
            if df.empty:
                self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
                return
            
            # 獲取股票名稱
            stock_name = df['stock_name'].iloc[0] if 'stock_name' in df.columns else ''
            
            # 設置背景為深色
            pg.setConfigOption('background', 'k')  # 黑色背景
            pg.setConfigOption('foreground', 'w')  # 白色前景
            
            # 創建圖表佈局
            chart_layout_widget = pg.GraphicsLayoutWidget()
            self.chart_container_layout.addWidget(chart_layout_widget)
            
            # 創建K線圖區域 (上方佔比較大)
            p1 = chart_layout_widget.addPlot(row=0, col=0)
            p1.showGrid(x=True, y=True, alpha=0.3)
            p1.setLabel('left', '價格')
            
            # 在原圖下方添加成交量圖 (下方較小)
            p2 = chart_layout_widget.addPlot(row=1, col=0)
            p2.showGrid(x=True, y=True, alpha=0.3)
            p2.setLabel('left', '成交量(張)')
            p2.setXLink(p1)  # 連結X軸
            
            # 設置比例 (K線圖佔75%，成交量圖佔25%)
            chart_layout_widget.ci.layout.setRowStretchFactor(0, 3)
            chart_layout_widget.ci.layout.setRowStretchFactor(1, 1)
            
            # 準備X軸日期標籤
            dates = [d.strftime('%Y-%m-%d') for d in df['date']]
            xdict = dict(enumerate(dates))
            x_axis_1 = p1.getAxis('bottom')
            x_axis_2 = p2.getAxis('bottom')
            
            # 設置標題
            title = f"{stock_id} {stock_name} K線圖 (最後交易日: {dates[-1]})"
            p1.setTitle(title, color='w')
            
            # 設置X軸為日期
            if len(dates) > 10:
                # 選擇適當的日期間隔
                step = max(1, len(dates) // 10)
                ticks = [(i, dates[i]) for i in range(0, len(dates), step)]
                x_axis_1.setTicks([ticks, []])
                x_axis_2.setTicks([ticks, []])
            else:
                # 如果日期較少，全部顯示
                ticks = [(i, date) for i, date in enumerate(dates)]
                x_axis_1.setTicks([ticks, []])
                x_axis_2.setTicks([ticks, []])
            
            # 繪製K線圖
            for i in range(len(df)):
                # 獲取當前K線數據
                open_p = df['Open'].iloc[i]
                close_p = df['Close'].iloc[i]
                low_p = df['Low'].iloc[i]
                high_p = df['High'].iloc[i]
                
                # 根據收盤價與開盤價的關係決定顏色（紅漲綠跌）
                if close_p > open_p:
                    # 上漲，紅色
                    color = (255, 0, 0)
                else:
                    # 下跌或平盤，綠色
                    color = (0, 255, 0)
                
                # 繪製K線實體
                bar = pg.BarGraphItem(
                    x=[i], height=[abs(close_p - open_p)], 
                    width=0.8, brush=color, pen=color
                )
                
                # 設置實體位置
                y_base = min(close_p, open_p)
                bar.setPos(0, y_base)
                p1.addItem(bar)
                
                # 繪製上下影線
                p1.plot(
                    [i, i], [low_p, high_p],
                    pen=pg.mkPen(color, width=1)
                )
            
            # 繪製成交量柱狀圖
            volume = df['Volume'].values
            for i in range(len(df)):
                # 使用與K線相同的顏色邏輯
                if df['Close'].iloc[i] > df['Open'].iloc[i]:
                    color = (255, 0, 0, 150)  # 紅色半透明
                else:
                    color = (0, 255, 0, 150)  # 綠色半透明
                
                # 繪製成交量柱
                vol_bar = pg.BarGraphItem(
                    x=[i], height=[volume[i]], width=0.8, 
                    brush=color, pen=color
                )
                p2.addItem(vol_bar)
            
            # 設置Y軸範圍
            p1.setYRange(df['Low'].min() * 0.99, df['High'].max() * 1.01)
            p2.setYRange(0, df['Volume'].max() * 1.1)
            
            # 設置X軸範圍
            p1.setXRange(-0.5, len(df) - 0.5)
            p2.setXRange(-0.5, len(df) - 0.5)
            
            self.show_status(f"{stock_id} K線圖已更新")
            
        except Exception as e:
            self.show_status(f"繪製K線圖失敗: {str(e)}", is_error=True)
            logging.error(traceback.format_exc())

def main():
    # 確保目錄存在
    os.makedirs(os.path.dirname(os.path.abspath(__file__)), exist_ok=True)
    
    app = QApplication(sys.argv)
    win = StockScreenerGUI()
    win.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
