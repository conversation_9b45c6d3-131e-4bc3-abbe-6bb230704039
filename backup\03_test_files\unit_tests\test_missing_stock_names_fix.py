#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試缺失股票名稱修復
驗證之前顯示為"股票代碼"的股票現在是否有正確名稱
"""

import sys
import pandas as pd
from strategy_intersection_analyzer import StrategyIntersectionAnalyzer

def test_missing_stock_names():
    """測試缺失股票名稱的修復"""
    print("🔍 測試缺失股票名稱修復")
    print("="*50)
    
    # 台灣股票完整名稱對照表（修復後的版本）
    taiwan_stocks = {
        # 權值股 & 科技股
        "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2308": "台達電", "2382": "廣達",
        "2303": "聯電", "2357": "華碩", "2409": "友達", "3008": "大立光", "3711": "日月光投控",
        "2379": "瑞昱", "3034": "聯詠", "3481": "群創", "4938": "和碩", "2327": "國巨",
        "2474": "可成", "6669": "緯穎", "2376": "技嘉", "2395": "研華", "3037": "欣興",
        "2408": "南亞科", "3443": "創意", "6415": "矽力-KY", "3661": "世芯-KY", "6770": "力積電",
        "2449": "京元電子", "2458": "義隆", "2301": "光寶科", "2324": "仁寶", "2356": "英業達",
        "2377": "微星", "2412": "中華電", "3045": "台灣大", "4904": "遠傳", "6505": "台塑化",
        
        # 金融股
        "2881": "富邦金", "2882": "國泰金", "2883": "開發金", "2884": "玉山金", "2885": "元大金",
        "2886": "兆豐金", "2887": "台新金", "2888": "新光金", "2889": "國票金", "2890": "永豐金",
        "2891": "中信金", "2892": "第一金", "2880": "華南金", "5880": "合庫金", "2823": "中壽",
        
        # 傳統產業
        "1101": "台泥", "1102": "亞泥", "1103": "嘉泥", "1216": "統一", "1301": "台塑",
        "1303": "南亞", "1326": "台化", "1402": "遠東新", "2002": "中鋼", "2105": "正新",
        "2207": "和泰車", "2311": "日月光", "2312": "金寶", "2313": "華通", "2314": "台揚",
        "2328": "廣宇", "2329": "華碩", "2603": "長榮", "2609": "陽明", "2610": "華航",
        "2633": "台灣高鐵", "2801": "彰銀", "8454": "富邦媒", "8996": "高力",
        
        # ETF
        "0050": "元大台灣50", "0051": "元大中型100", "0052": "富邦科技", "0053": "元大電子",
        "0054": "元大台商50", "0055": "元大MSCI金融", "0056": "元大高股息", "0057": "富邦摩台",
        "006208": "富邦台50", "00692": "富邦公司治理", "00701": "國泰股利精選30",
        
        # 電子零組件與設備（新增的缺失股票）
        "1519": "中興電", "3289": "宜特", "5278": "尚凡", "6290": "良維", "6196": "帆宣", 
        "6667": "信紘科", "6698": "旭暉應材", "8039": "台虹", "4414": "如興", "2414": "精技",
        "8499": "鼎炫-KY", "0885": "富邦越南",
        
        # 其他重要股票
        "4938": "和碩", "5871": "中租-KY", "5876": "上海商銀", "6505": "台塑化", 
        "6669": "緯穎"
    }
    
    def get_stock_names_for_codes(stock_codes):
        """獲取股票名稱（修復後的版本）"""
        stock_names = {}
        for code in stock_codes:
            if code in taiwan_stocks:
                stock_names[code] = taiwan_stocks[code]
            else:
                stock_names[code] = f"股票{code}"
        return stock_names
    
    # 測試之前有問題的股票代碼
    problem_codes = ["1519", "5278", "6667", "6698", "8039", "6290"]
    
    print("📋 測試之前有問題的股票代碼:")
    stock_names = get_stock_names_for_codes(problem_codes)
    
    fixed_count = 0
    for code in problem_codes:
        name = stock_names[code]
        status = "✅" if not name.startswith("股票") else "❌"
        print(f"  {status} {code} → {code}({name})")
        if not name.startswith("股票"):
            fixed_count += 1
    
    print(f"\n📊 修復結果: {fixed_count}/{len(problem_codes)} 個股票有正確名稱")
    
    return fixed_count == len(problem_codes)

def test_intersection_display_with_fixed_names():
    """測試修復後的交集顯示"""
    print("\n🔍 測試修復後的交集顯示")
    print("="*40)
    
    # 創建分析器
    analyzer = StrategyIntersectionAnalyzer()
    
    # 模擬包含之前有問題股票的策略結果
    strategy_results = {
        "破底反彈高量": pd.DataFrame({
            "股票代碼": ["1519", "5278", "6667", "6698"],
            "股票名稱": ["中興電", "尚凡", "信紘科", "旭暉應材"],
            "評分": [85, 78, 82, 75]
        }),
        "阿水一式": pd.DataFrame({
            "股票代碼": ["1519", "6290", "6667", "8039"],
            "股票名稱": ["中興電", "良維", "信紘科", "台虹"],
            "評分": [90, 85, 88, 82]
        }),
        "二次創高股票": pd.DataFrame({
            "股票代碼": ["1519", "5278", "6667", "6698"],
            "股票名稱": ["中興電", "尚凡", "信紘科", "旭暉應材"],
            "評分": [92, 87, 85, 90]
        })
    }
    
    # 添加策略結果到分析器
    for strategy, df in strategy_results.items():
        analyzer.add_strategy_result(strategy, df, "股票代碼")
    
    # 分析所有組合
    all_results = analyzer.analyze_all_combinations()
    
    # 獲取最佳組合
    top_combinations = analyzer.get_top_intersection_combinations()
    
    # 台股名稱對照表
    taiwan_stocks = {
        "1519": "中興電", "5278": "尚凡", "6667": "信紘科", "6698": "旭暉應材",
        "6290": "良維", "8039": "台虹"
    }
    
    def get_stock_names_for_codes(stock_codes):
        stock_names = {}
        for code in stock_codes:
            if code in taiwan_stocks:
                stock_names[code] = taiwan_stocks[code]
            else:
                stock_names[code] = f"股票{code}"
        return stock_names
    
    # 生成報告
    print("📊 修復後的交集分析結果:")
    
    if top_combinations:
        for i, (combo_name, count) in enumerate(top_combinations[:5], 1):
            print(f"  {i:2d}. {combo_name}: {count} 支共同股票")

            # 顯示詳細股票（包含修復後的名稱）
            if combo_name in all_results:
                stocks = all_results[combo_name]['intersection_stocks']
                if stocks:
                    stock_names = get_stock_names_for_codes(list(stocks))
                    stock_info = [f"{code}({stock_names[code]})" for code in sorted(stocks)]
                    print(f"      股票: {', '.join(stock_info)}")
                    
                    # 檢查是否還有"股票代碼"格式
                    has_stock_prefix = any(name.startswith("股票") for name in stock_names.values())
                    if has_stock_prefix:
                        print(f"      ⚠️  仍有股票顯示為'股票代碼'格式")
                    else:
                        print(f"      ✅ 所有股票都有正確名稱")
                else:
                    print(f"      股票: 無共同股票")
                print("")
    
    return True

def main():
    """主函數"""
    try:
        # 測試缺失股票名稱修復
        names_fixed = test_missing_stock_names()
        
        # 測試修復後的交集顯示
        display_ok = test_intersection_display_with_fixed_names()
        
        if names_fixed and display_ok:
            print(f"\n🎉 缺失股票名稱修復驗證通過！")
            print(f"   ✅ 1519 → 1519(中興電)")
            print(f"   ✅ 5278 → 5278(尚凡)")
            print(f"   ✅ 6698 → 6698(旭暉應材)")
            print(f"   ✅ 8039 → 8039(台虹)")
            print(f"   ✅ 交集結果正確顯示股票名稱")
        else:
            print(f"\n❌ 缺失股票名稱修復驗證失敗！")
        
        return 0 if (names_fixed and display_ok) else 1
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
