#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證策略交集分析修復
模擬GUI中的策略交集分析流程
"""

import sys
import pandas as pd
from strategy_intersection_analyzer import StrategyIntersectionAnalyzer

class MockIntersectionAnalysis:
    """模擬策略交集分析"""
    
    def __init__(self):
        self.intersection_analyzer = StrategyIntersectionAnalyzer()
        self.strategy_results_cache = {}
        self.intersection_strategy_vars = {}
        
        # 模擬策略選擇框狀態
        self.setup_mock_checkboxes()
        
        # 模擬策略結果緩存
        self.setup_mock_strategy_results()
    
    def setup_mock_checkboxes(self):
        """設置模擬的策略選擇框"""
        class MockCheckBox:
            def __init__(self, checked=False):
                self._checked = checked
            
            def isChecked(self):
                return self._checked
            
            def setChecked(self, checked):
                self._checked = checked
        
        # 模擬4個策略都被勾選
        self.intersection_strategy_vars = {
            "勝率73.45%": <PERSON>ck<PERSON><PERSON>ck<PERSON><PERSON>(True),
            "阿水一式": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(True), 
            "機小型": <PERSON>ck<PERSON>heckBox(True),
            "三檔+RSI策略": MockCheckBox(True)
        }
    
    def setup_mock_strategy_results(self):
        """設置模擬的策略結果"""
        self.strategy_results_cache = {
            "勝率73.45%": pd.DataFrame({
                "股票代碼": ["2330", "2317", "2454", "1301", "2881"],
                "股票名稱": ["台積電", "鴻海", "聯發科", "台塑", "富邦金"],
                "評分": [85, 78, 82, 75, 80]
            }),
            "阿水一式": pd.DataFrame({
                "股票代碼": ["2330", "2317", "2382", "2303", "2412"],
                "股票名稱": ["台積電", "鴻海", "廣達", "聯電", "中華電"],
                "評分": [90, 85, 88, 82, 79]
            }),
            "機小型": pd.DataFrame({
                "股票代碼": ["2330", "2454", "2382", "3008", "2327"],
                "股票名稱": ["台積電", "聯發科", "廣達", "大立光", "國巨"],
                "評分": [92, 87, 85, 90, 83]
            }),
            "三檔+RSI策略": pd.DataFrame({
                "股票代碼": ["2317", "2454", "2382", "2303", "2327"],
                "股票名稱": ["鴻海", "聯發科", "廣達", "聯電", "國巨"],
                "評分": [88, 91, 86, 84, 87]
            })
        }
    
    def get_stock_code_column_name(self, strategy_name):
        """根據策略名稱獲取股票代碼欄位名稱"""
        return "股票代碼"
    
    def analyze_all_strategy_combinations(self):
        """模擬修復後的策略組合分析方法"""
        print("🔍 開始策略組合分析...")
        
        # 獲取選中的策略
        selected_strategies = []
        for strategy, checkbox in self.intersection_strategy_vars.items():
            if checkbox.isChecked():
                selected_strategies.append(strategy)
        
        print(f"📊 選中策略: {selected_strategies}")
        
        # 檢查是否有足夠的策略
        if len(selected_strategies) < 2:
            print("❌ 錯誤: 請至少選擇2個策略進行組合分析")
            return False
        
        print(f"✅ 選中 {len(selected_strategies)} 個策略，滿足最少2個策略的要求")
        
        # 檢查策略是否有結果
        missing_strategies = []
        for strategy in selected_strategies:
            if strategy not in self.strategy_results_cache:
                missing_strategies.append(strategy)
        
        if missing_strategies:
            print(f"⚠️  缺失策略結果: {missing_strategies}")
            return False
        
        print(f"✅ 所有選中策略都有結果")
        
        try:
            # 重新初始化交集分析器
            self.intersection_analyzer = StrategyIntersectionAnalyzer()
            
            # 只添加選中策略的結果
            for strategy in selected_strategies:
                if strategy in self.strategy_results_cache:
                    result_df = self.strategy_results_cache[strategy]
                    stock_code_column = self.get_stock_code_column_name(strategy)
                    self.intersection_analyzer.add_strategy_result(strategy, result_df, stock_code_column)
            
            # 分析所有組合
            all_results = self.intersection_analyzer.analyze_all_combinations()
            
            # 獲取最佳組合
            top_combinations = self.intersection_analyzer.get_top_intersection_combinations()
            
            # 生成報告
            print(f"\n📈 組合分析結果:")
            print(f"  找到 {len(top_combinations)} 個有效組合")
            
            for i, (combo_name, count) in enumerate(top_combinations[:10], 1):
                print(f"    {i:2d}. {combo_name}: {count} 支共同股票")
            
            print(f"\n🎉 組合分析完成！找到 {len(top_combinations)} 個有效組合")
            return True
            
        except Exception as e:
            print(f"❌ 分析組合失敗: {e}")
            return False

def test_original_vs_fixed():
    """測試原始邏輯 vs 修復後邏輯"""
    print("🧪 測試策略交集分析修復")
    print("="*50)
    
    mock = MockIntersectionAnalysis()
    
    # 測試修復後的邏輯
    print("🔧 測試修復後的邏輯:")
    success = mock.analyze_all_strategy_combinations()
    
    if success:
        print("✅ 修復後的邏輯工作正常")
    else:
        print("❌ 修復後的邏輯仍有問題")
    
    # 測試邊界情況：只選擇1個策略
    print(f"\n🎯 測試邊界情況：只選擇1個策略")
    mock.intersection_strategy_vars["阿水一式"].setChecked(False)
    mock.intersection_strategy_vars["機小型"].setChecked(False)
    mock.intersection_strategy_vars["三檔+RSI策略"].setChecked(False)
    
    success_single = mock.analyze_all_strategy_combinations()
    
    if not success_single:
        print("✅ 正確拒絕了只有1個策略的情況")
    else:
        print("❌ 錯誤接受了只有1個策略的情況")
    
    return success and not success_single

def main():
    """主函數"""
    try:
        success = test_original_vs_fixed()
        
        if success:
            print(f"\n🎉 策略交集分析修復驗證通過！")
            print(f"   ✅ 正確處理4個策略的組合分析")
            print(f"   ✅ 正確拒絕少於2個策略的情況")
        else:
            print(f"\n❌ 策略交集分析修復驗證失敗！")
        
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
