#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試修復後的 price 函數
繞過有問題的 requests_get 函數
"""

import sys
import os
import types
from datetime import datetime
import requests
import pandas as pd
import random
import time

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_direct_twse():
    """直接測試 TWSE API"""
    print("🧪 直接測試 TWSE API...")
    
    try:
        date_str = "20220830"
        url = "https://www.twse.com.tw/exchangeReport/MI_INDEX"
        params = {
            "response": "json",
            "date": date_str,
            "type": "ALLBUT0999"
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        print(f"   請求 URL: {url}")
        print(f"   參數: {params}")
        
        # 隨機延遲
        time.sleep(random.uniform(2, 5))

        # 跳過 SSL 驗證 (解決證書問題)
        res = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        print(f"   狀態碼: {res.status_code}")
        
        if res.status_code == 200:
            response_json = res.json()
            print(f"   回應狀態: {response_json.get('stat', 'N/A')}")
            
            if "tables" in response_json:
                print(f"   找到 tables 結構，共 {len(response_json['tables'])} 個表格")
                for i, table in enumerate(response_json["tables"]):
                    title = table.get("title", "")
                    print(f"     表格 {i}: {title}")
                    if "每日收盤行情" in title or "報價行情" in title:
                        data = table.get("data", [])
                        fields = table.get("fields", [])
                        print(f"       ✅ 找到目標表格，資料筆數: {len(data)}, 欄位數: {len(fields)}")
                        if fields:
                            print(f"       欄位: {fields[:5]}...")  # 只顯示前5個欄位
                        return True
            elif "data9" in response_json:
                data = response_json["data9"]
                fields = response_json.get("fields9", [])
                print(f"   找到 data9 結構，資料筆數: {len(data)}, 欄位數: {len(fields)}")
                return True
            elif "data8" in response_json:
                data = response_json["data8"]
                fields = response_json.get("fields8", [])
                print(f"   找到 data8 結構，資料筆數: {len(data)}, 欄位數: {len(fields)}")
                return True
            else:
                print(f"   ⚠️ 未知的回應結構: {list(response_json.keys())}")
                return False
        else:
            print(f"   ❌ HTTP 錯誤: {res.status_code}")
            print(f"   回應內容: {res.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {str(e)}")
        import traceback
        print(f"   詳細錯誤: {traceback.format_exc()}")
        return False

def test_direct_tpex():
    """直接測試 TPEX API"""
    print("\n🧪 直接測試 TPEX API...")
    
    try:
        # 轉換為民國年格式
        date = datetime(2022, 8, 30)
        roc_year = date.year - 1911
        roc_date = f"{roc_year}/{date.month:02d}/{date.day:02d}"
        
        url = (
            "https://www.tpex.org.tw/web/stock/aftertrading/"
            "otc_quotes_no1430/stk_wn1430_result.php?"
            f"l=zh-tw&d={roc_date}&se=AL"
        )
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        print(f"   請求 URL: {url}")
        print(f"   民國年日期: {roc_date}")
        
        # 隨機延遲
        time.sleep(random.uniform(2, 5))

        # 跳過 SSL 驗證 (解決證書問題)
        res = requests.get(url, headers=headers, timeout=30, allow_redirects=False, verify=False)
        
        print(f"   狀態碼: {res.status_code}")
        
        if res.status_code == 200:
            response_json = res.json()
            
            tables = response_json.get("tables", [])
            print(f"   找到 {len(tables)} 個表格")
            
            for i, table in enumerate(tables):
                title = table.get("title", "")
                print(f"     表格 {i}: {title}")
                if "上櫃股票每日收盤行情" in title:
                    data = table.get("data", [])
                    fields = table.get("fields", [])
                    print(f"       ✅ 找到目標表格，資料筆數: {len(data)}, 欄位數: {len(fields)}")
                    if fields:
                        print(f"       欄位: {fields[:5]}...")  # 只顯示前5個欄位
                    return True
                    
            print("   ⚠️ 未找到目標表格")
            return False
            
        elif res.status_code == 302:
            print("   ⚠️ 302 redirect (可能無該日期資料)")
            return True  # 這也算正常
        else:
            print(f"   ❌ HTTP 錯誤: {res.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {str(e)}")
        import traceback
        print(f"   詳細錯誤: {traceback.format_exc()}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 直接 API 測試")
    print("=" * 60)
    print("繞過 requests_get 函數，直接測試 API")
    print()
    
    # 測試1: TWSE API
    success1 = test_direct_twse()
    
    # 測試2: TPEX API
    success2 = test_direct_tpex()
    
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 60)
    print(f"1. TWSE API: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"2. TPEX API: {'✅ 成功' if success2 else '❌ 失敗'}")
    
    overall_success = success1 and success2
    print(f"\n🎯 整體測試結果: {'✅ 全部成功' if overall_success else '❌ 部分失敗'}")
    
    if overall_success:
        print("\n✨ 結論:")
        print("   • API 本身是正常的")
        print("   • 問題出在 finlab 的 requests_get 函數")
        print("   • 修復方案：使用標準的 requests.get()")
        print("\n🚀 現在可以更新 auto_update.py 了！")
    else:
        print("\n⚠️ 需要進一步檢查網路或 API 狀態")

if __name__ == "__main__":
    main()
