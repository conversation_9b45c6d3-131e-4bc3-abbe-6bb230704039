#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試特定爬蟲功能，比較不同爬蟲的網路連接狀況
"""

import sys
import os
import types
from datetime import datetime, timedelta
import urllib3
import requests

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_direct_url_access():
    """直接測試各種 URL 的訪問狀況"""
    print("🌐 直接測試 URL 訪問狀況")
    print("=" * 50)
    
    # 測試日期 - 使用最近的工作日
    test_date = datetime.now()
    while test_date.weekday() >= 5:  # 跳過週末
        test_date -= timedelta(days=1)
    
    date_str = test_date.strftime('%Y%m%d')
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} ({date_str})")
    
    # 測試不同的 URL
    urls_to_test = [
        {
            'name': 'benchmark (MI_5MINS_INDEX)',
            'url': f"https://www.twse.com.tw/rwd/zh/TAIEX/MI_5MINS_INDEX?response=csv&date={date_str}&_=1544020420045",
            'expected_content': '時間'
        },
        {
            'name': 'price (MI_INDEX)',
            'url': f"https://www.twse.com.tw/rwd/zh/afterTrading/MI_INDEX?date={date_str}&type=ALLBUT0999&response=csv",
            'expected_content': '證券代號'
        },
        {
            'name': 'bargin (MI_MARGN)',
            'url': f"https://www.twse.com.tw/rwd/zh/marginTrading/MI_MARGN?date={date_str}&selectType=ALL&response=csv",
            'expected_content': '證券代號'
        }
    ]
    
    results = []
    
    for test_case in urls_to_test:
        print(f"\n🔍 測試 {test_case['name']}...")
        print(f"   URL: {test_case['url'][:80]}...")
        
        try:
            response = requests.get(test_case['url'], timeout=10, verify=False)
            
            print(f"   狀態碼: {response.status_code}")
            print(f"   回應長度: {len(response.text)} 字元")
            
            if response.status_code == 200:
                if test_case['expected_content'] in response.text:
                    print(f"   ✅ 成功 - 包含預期內容 '{test_case['expected_content']}'")
                    results.append({'name': test_case['name'], 'status': 'success'})
                else:
                    print(f"   ⚠️ 回應異常 - 不包含預期內容")
                    print(f"   前100字元: {response.text[:100]}")
                    results.append({'name': test_case['name'], 'status': 'no_content'})
            else:
                print(f"   ❌ HTTP 錯誤: {response.status_code}")
                results.append({'name': test_case['name'], 'status': 'http_error'})
                
        except requests.exceptions.Timeout:
            print(f"   ❌ 連接超時")
            results.append({'name': test_case['name'], 'status': 'timeout'})
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 連接錯誤")
            results.append({'name': test_case['name'], 'status': 'connection_error'})
        except Exception as e:
            print(f"   ❌ 其他錯誤: {str(e)}")
            results.append({'name': test_case['name'], 'status': 'other_error'})
    
    return results

def test_finlab_crawlers():
    """測試 finlab 爬蟲函數"""
    print("\n🧪 測試 finlab 爬蟲函數")
    print("=" * 50)
    
    try:
        from crawler import crawl_price, crawl_bargin, crawl_benchmark
        
        # 測試日期
        test_date = datetime.now() - timedelta(days=1)
        while test_date.weekday() >= 5:  # 跳過週末
            test_date -= timedelta(days=1)
        
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        crawlers_to_test = [
            ('price', crawl_price),
            ('bargin', crawl_bargin),
            ('benchmark', crawl_benchmark)
        ]
        
        results = []
        
        for name, crawler_func in crawlers_to_test:
            print(f"\n🔍 測試 {name} 爬蟲...")
            
            try:
                result = crawler_func(test_date)
                
                if result is not None and len(result) > 0:
                    print(f"   ✅ 成功: {len(result)} 筆資料")
                    print(f"   欄位: {list(result.columns)[:3]}...")
                    print(f"   索引: {result.index.names}")
                    results.append({'name': name, 'status': 'success', 'records': len(result)})
                else:
                    print(f"   ⚠️ 無資料 (可能是假日)")
                    results.append({'name': name, 'status': 'no_data'})
                    
            except Exception as e:
                print(f"   ❌ 失敗: {str(e)}")
                results.append({'name': name, 'status': 'error', 'error': str(e)})
        
        return results
        
    except ImportError as e:
        print(f"❌ 無法導入爬蟲模組: {str(e)}")
        return []

def analyze_results(url_results, crawler_results):
    """分析測試結果"""
    print("\n📊 測試結果分析")
    print("=" * 50)
    
    print("🌐 URL 直接訪問結果:")
    for result in url_results:
        status_icon = {
            'success': '✅',
            'no_content': '⚠️',
            'http_error': '❌',
            'timeout': '⏰',
            'connection_error': '🔌',
            'other_error': '❌'
        }.get(result['status'], '❓')
        
        print(f"   {status_icon} {result['name']}: {result['status']}")
    
    print("\n🧪 爬蟲函數測試結果:")
    for result in crawler_results:
        status_icon = {
            'success': '✅',
            'no_data': '⚠️',
            'error': '❌'
        }.get(result['status'], '❓')
        
        extra_info = ""
        if result['status'] == 'success':
            extra_info = f" ({result['records']} 筆)"
        elif result['status'] == 'error':
            extra_info = f" ({result.get('error', '')[:30]}...)"
        
        print(f"   {status_icon} {result['name']}: {result['status']}{extra_info}")
    
    # 給出建議
    print("\n💡 建議:")
    
    # 檢查是否有成功的爬蟲
    successful_crawlers = [r for r in crawler_results if r['status'] == 'success']
    failed_crawlers = [r for r in crawler_results if r['status'] == 'error']
    
    if len(successful_crawlers) > 0 and len(failed_crawlers) > 0:
        print("   ✅ 部分爬蟲正常工作，IP 沒有被全面封鎖")
        print("   🔧 benchmark 爬蟲可能有特定問題，建議:")
        print("      • 檢查 benchmark API 是否有變更")
        print("      • 嘗試不同的日期")
        print("      • 檢查 API 參數")
        
        if any(r['name'] == 'benchmark' for r in failed_crawlers):
            print("   ⚠️ benchmark 爬蟲確實有問題")
            
            # 檢查 URL 測試結果
            benchmark_url_result = next((r for r in url_results if 'benchmark' in r['name']), None)
            if benchmark_url_result and benchmark_url_result['status'] == 'success':
                print("   🔍 URL 直接訪問正常，問題可能在資料解析")
            else:
                print("   🔍 URL 直接訪問也有問題，可能是 API 端點問題")
    
    elif len(successful_crawlers) == 0:
        print("   ❌ 所有爬蟲都失敗，可能是網路問題")
    else:
        print("   ✅ 所有爬蟲都正常工作")

def main():
    """主函數"""
    print("🔧 爬蟲網路連接測試工具")
    print("=" * 60)
    print("🎯 目標: 比較不同爬蟲的網路連接狀況")
    print("=" * 60)
    
    # 測試1: 直接 URL 訪問
    url_results = test_direct_url_access()
    
    # 測試2: finlab 爬蟲函數
    crawler_results = test_finlab_crawlers()
    
    # 分析結果
    analyze_results(url_results, crawler_results)

if __name__ == "__main__":
    main()
