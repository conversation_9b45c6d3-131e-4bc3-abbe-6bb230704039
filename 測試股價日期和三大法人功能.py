#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from datetime import datetime

def test_stock_price_with_date():
    """測試股價顯示是否包含計算日期"""
    print("🧪 測試股價顯示功能")
    print("=" * 50)
    
    # 模擬選擇的計算日期
    selected_date = datetime(2025, 7, 29).date()
    print(f"選擇的計算日期: {selected_date.strftime('%Y-%m-%d')}")
    
    # 檢查股價資料庫
    price_db_path = 'D:/Finlab/history/tables/price.db'
    if os.path.exists(price_db_path):
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 查找最接近的股價資料日期
            cursor.execute("""
                SELECT DISTINCT date
                FROM stock_daily_data
                WHERE date <= ?
                ORDER BY date DESC
                LIMIT 7
            """, (selected_date.strftime('%Y-%m-%d'),))
            
            price_dates = cursor.fetchall()
            if price_dates:
                price_date = price_dates[0][0]
                print(f"使用股價資料日期: {price_date}")
                
                # 獲取台積電股價
                cursor.execute("""
                    SELECT stock_id, stock_name, Close
                    FROM stock_daily_data
                    WHERE date = ? AND stock_id = '2330'
                """, (price_date,))
                
                result = cursor.fetchone()
                if result:
                    stock_id, stock_name, close_price = result
                    print(f"✅ {stock_id} {stock_name} 股價: {close_price}元")
                    print(f"✅ 股價顯示格式: 股價 ({selected_date.strftime('%Y-%m-%d')})：{close_price}元")
                else:
                    print("❌ 找不到台積電股價資料")
            else:
                print("❌ 找不到股價資料")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 股價測試失敗: {e}")
    else:
        print("❌ 股價資料庫不存在")

def test_institutional_trading_data():
    """測試三大法人買賣資料獲取"""
    print("\n🧪 測試三大法人買賣資料功能")
    print("=" * 50)
    
    # 模擬選擇的計算日期
    selected_date = datetime(2025, 7, 29).date()
    stock_code = '2330'
    
    # 檢查三大法人資料庫
    bargin_db_path = 'D:/Finlab/history/tables/bargin_report.db'
    if os.path.exists(bargin_db_path):
        try:
            conn = sqlite3.connect(bargin_db_path)
            cursor = conn.cursor()
            
            # 查找最接近的三大法人資料日期
            cursor.execute("""
                SELECT DISTINCT date
                FROM bargin_report
                WHERE date <= ? AND stock_id = ?
                ORDER BY date DESC
                LIMIT 7
            """, (selected_date.strftime('%Y-%m-%d'), stock_code))
            
            bargin_dates = cursor.fetchall()
            if bargin_dates:
                bargin_date = bargin_dates[0][0]
                print(f"使用三大法人資料日期: {bargin_date}")
                
                # 獲取三大法人資料
                cursor.execute("""
                    SELECT 
                        `外陸資買進股數(不含外資自營商)`,
                        `外陸資賣出股數(不含外資自營商)`,
                        `外陸資買賣超股數(不含外資自營商)`,
                        `投信買進股數`,
                        `投信賣出股數`,
                        `投信買賣超股數`,
                        `自營商買進股數(自行買賣)`,
                        `自營商賣出股數(自行買賣)`,
                        `自營商買賣超股數(自行買賣)`
                    FROM bargin_report
                    WHERE date = ? AND stock_id = ?
                """, (bargin_date, stock_code))
                
                result = cursor.fetchone()
                if result:
                    (foreign_buy, foreign_sell, foreign_net,
                     trust_buy, trust_sell, trust_net,
                     dealer_buy, dealer_sell, dealer_net) = result
                    
                    # 處理數值轉換
                    def safe_int(value):
                        try:
                            if value and str(value).strip() and str(value).strip() != '0.0':
                                return int(float(str(value).replace(',', '')))
                            return 0
                        except:
                            return 0
                    
                    # 格式化顯示
                    def format_shares(value):
                        if value == 0:
                            return '0股'
                        shares = safe_int(value)
                        if shares >= 1000:
                            return f"{shares/1000:.1f}千股"
                        else:
                            return f"{shares}股"
                    
                    def format_net_shares(value):
                        shares = safe_int(value)
                        if shares > 0:
                            if shares >= 1000:
                                return f"+{shares/1000:.1f}千股 (買超)"
                            else:
                                return f"+{shares}股 (買超)"
                        elif shares < 0:
                            if abs(shares) >= 1000:
                                return f"{shares/1000:.1f}千股 (賣超)"
                            else:
                                return f"{shares}股 (賣超)"
                        else:
                            return "持平"
                    
                    print(f"✅ {stock_code} 台積電三大法人買賣資料:")
                    print(f"🌍 外資：")
                    print(f"   買進：{format_shares(foreign_buy)}")
                    print(f"   賣出：{format_shares(foreign_sell)}")
                    print(f"   買賣超：{format_net_shares(foreign_net)}")
                    print(f"🏦 投信：")
                    print(f"   買進：{format_shares(trust_buy)}")
                    print(f"   賣出：{format_shares(trust_sell)}")
                    print(f"   買賣超：{format_net_shares(trust_net)}")
                    print(f"🏢 自營商：")
                    print(f"   買進：{format_shares(dealer_buy)}")
                    print(f"   賣出：{format_shares(dealer_sell)}")
                    print(f"   買賣超：{format_net_shares(dealer_net)}")
                    
                else:
                    print(f"❌ 找不到 {stock_code} 的三大法人資料")
            else:
                print(f"❌ 找不到 {stock_code} 的三大法人資料日期")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 三大法人測試失敗: {e}")
    else:
        print("❌ 三大法人資料庫不存在")

def test_integration():
    """測試整合效果"""
    print("\n🧪 測試整合效果")
    print("=" * 50)
    
    print("✅ 修改完成的功能:")
    print("1. ✅ 股價顯示已修改為使用選擇的計算日期")
    print("2. ✅ 股價旁邊會顯示計算日期，格式：股價 (2025-07-29)：XXX元")
    print("3. ✅ 新增三大法人買賣狀況區塊")
    print("4. ✅ 三大法人資料包含外資、投信、自營商的買進、賣出、買賣超")
    print("5. ✅ 數值格式化顯示（千股為單位）")
    print("6. ✅ 買賣超有顏色標示（紅色買超、綠色賣超）")
    print("7. ✅ 資料日期顯示確保用戶知道資料來源日期")
    
    print("\n📋 使用方法:")
    print("1. 在主程式中選擇計算日期")
    print("2. 執行月營收排行榜查詢")
    print("3. 在結果表格中右鍵點擊任一股票")
    print("4. 選擇「月營收綜合評估」")
    print("5. 查看包含股價日期和三大法人資訊的詳細報告")

if __name__ == '__main__':
    test_stock_price_with_date()
    test_institutional_trading_data()
    test_integration()
