#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試超簡單版圖表修復
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class SimpleChartTestWindow(QMainWindow):
    """超簡單版圖表測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("⚡ 超簡單版圖表修復測試")
        self.setGeometry(100, 100, 600, 400)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow { background-color: #2b2b2b; color: white; }
            QPushButton { 
                background-color: #4CAF50; 
                color: white; 
                border: none; 
                padding: 15px; 
                font-size: 16px; 
                border-radius: 5px; 
                margin: 10px;
            }
            QPushButton:hover { background-color: #45a049; }
            QLabel { color: white; font-size: 14px; margin: 10px; }
        """)
        
        # 創建界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("⚡ 超簡單版圖表修復測試")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; color: #4CAF50;")
        layout.addWidget(title)
        
        # 說明
        info = QLabel("""
🎯 修復策略：
• 移除所有複雜邏輯
• 使用最簡單的 setTicks() 方法
• 直接設置 MM/DD 格式日期標籤
• 不依賴任何複雜的軸設置

📊 測試步驟：
1. 點擊下方按鈕開啟爬蟲
2. 切換到「圖表分析」
3. 選擇「歷史指數資料」
4. 點擊「生成圖表」
5. 觀察橫軸是否顯示日期
        """)
        info.setStyleSheet("background-color: #3a3a3a; padding: 15px; border-radius: 5px;")
        layout.addWidget(info)
        
        # 測試按鈕
        test_btn = QPushButton("⚡ 開啟台股爬蟲 (測試超簡單版修復)")
        test_btn.clicked.connect(self.open_crawler)
        layout.addWidget(test_btn)
        
        # 結果提示
        result = QLabel("✅ 現在應該能看到橫軸日期了！")
        result.setAlignment(Qt.AlignmentFlag.AlignCenter)
        result.setStyleSheet("color: #4CAF50; font-weight: bold; font-size: 16px;")
        layout.addWidget(result)
    
    def open_crawler(self):
        """開啟爬蟲"""
        try:
            print("⚡ 開啟台股爬蟲...")
            dialog = TWSEMarketDataDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"❌ 錯誤: {e}")

def main():
    app = QApplication(sys.argv)
    
    print("⚡ 超簡單版圖表修復測試")
    print("🎯 移除所有複雜邏輯，使用最直接的方法")
    print("📊 現在橫軸應該能正確顯示日期了！")
    
    window = SimpleChartTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
