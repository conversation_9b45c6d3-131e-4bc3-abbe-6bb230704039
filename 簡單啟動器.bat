@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 簡單啟動器

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo         簡單啟動器 - 直接啟動
echo ========================================
echo.

echo 🔍 檢查程式文件...

if exist "dist\台股智能選股系統_修復版.exe" (
    echo ✅ 找到修復版程式
    echo 🚀 正在啟動修復版...
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_修復版.exe"
    
    echo ✅ 修復版已啟動！
    echo.
    echo 💡 修復版特點：
    echo    ✓ 解決模組缺失問題
    echo    ✓ 優化啟動流程
    echo    ✓ 完整功能保留
    echo.
    
) else if exist "dist\台股智能選股系統.exe" (
    echo ✅ 找到標準版程式
    echo 🚀 正在啟動標準版...
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統.exe"
    
    echo ✅ 標準版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到程式文件
    echo.
    echo 💡 請檢查以下文件是否存在：
    echo    - dist\台股智能選股系統_修復版.exe
    echo    - dist\台股智能選股系統.exe
    echo.
    echo 🔧 如果文件不存在，請重新編譯：
    echo    python quick_recompile.py
    echo.
    pause
    exit /b 1
)

echo 📋 使用提示：
echo    - 如果程式無法正常運行，請檢查系統環境
echo    - 如果遇到錯誤，可以嘗試其他啟動方式
echo.

timeout /t 5 >nul
echo 啟動器將在 5 秒後關閉...
