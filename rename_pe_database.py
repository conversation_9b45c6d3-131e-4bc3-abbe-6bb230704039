#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新命名 PE 資料庫檔案：從 pe.db 改為 pe_data.db
"""

import os
import shutil
import sqlite3
from datetime import datetime

def rename_pe_database():
    """重新命名 PE 資料庫檔案"""
    print("=" * 80)
    print("🔄 重新命名 PE 資料庫檔案")
    print("=" * 80)
    
    # 定義檔案路徑
    old_path = 'D:/Finlab/history/tables/pe.db'
    new_path = 'D:/Finlab/history/tables/pe_data.db'
    backup_path = f'D:/Finlab/history/tables/pe_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    
    print(f"📂 舊檔案路徑: {old_path}")
    print(f"📂 新檔案路徑: {new_path}")
    print(f"📂 備份路徑: {backup_path}")
    
    # 檢查舊檔案是否存在
    if not os.path.exists(old_path):
        print(f"⚠️ 舊檔案不存在: {old_path}")
        
        # 檢查新檔案是否已經存在
        if os.path.exists(new_path):
            print(f"✅ 新檔案已存在: {new_path}")
            print(f"🎯 無需重新命名")
            return True
        else:
            print(f"❌ 新舊檔案都不存在")
            return False
    
    # 檢查新檔案是否已經存在
    if os.path.exists(new_path):
        print(f"⚠️ 目標檔案已存在: {new_path}")
        
        # 比較檔案大小和修改時間
        old_size = os.path.getsize(old_path)
        new_size = os.path.getsize(new_path)
        old_mtime = os.path.getmtime(old_path)
        new_mtime = os.path.getmtime(new_path)
        
        print(f"📊 舊檔案: {old_size:,} bytes, 修改時間: {datetime.fromtimestamp(old_mtime)}")
        print(f"📊 新檔案: {new_size:,} bytes, 修改時間: {datetime.fromtimestamp(new_mtime)}")
        
        # 詢問用戶如何處理
        print(f"\n選擇處理方式:")
        print(f"1. 用舊檔案覆蓋新檔案")
        print(f"2. 保留新檔案，備份舊檔案")
        print(f"3. 取消操作")
        
        choice = input("請選擇 (1/2/3): ").strip()
        
        if choice == '1':
            # 備份新檔案，然後用舊檔案覆蓋
            backup_new_path = f'D:/Finlab/history/tables/pe_data_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
            shutil.copy2(new_path, backup_new_path)
            print(f"📦 已備份新檔案到: {backup_new_path}")
            
            shutil.move(old_path, new_path)
            print(f"✅ 已用舊檔案覆蓋新檔案")
            
        elif choice == '2':
            # 備份舊檔案
            shutil.copy2(old_path, backup_path)
            print(f"📦 已備份舊檔案到: {backup_path}")
            
            os.remove(old_path)
            print(f"🗑️ 已刪除舊檔案")
            print(f"✅ 保留新檔案")
            
        else:
            print(f"❌ 取消操作")
            return False
    
    else:
        # 直接重新命名
        try:
            shutil.move(old_path, new_path)
            print(f"✅ 成功重新命名檔案")
            print(f"   從: {old_path}")
            print(f"   到: {new_path}")
            
        except Exception as e:
            print(f"❌ 重新命名失敗: {e}")
            return False
    
    # 驗證新檔案
    if os.path.exists(new_path):
        try:
            # 檢查資料庫是否可以正常開啟
            conn = sqlite3.connect(new_path)
            cursor = conn.cursor()
            
            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pe_data'")
            table_exists = cursor.fetchone() is not None
            
            if table_exists:
                # 檢查記錄數
                cursor.execute("SELECT COUNT(*) FROM pe_data")
                record_count = cursor.fetchone()[0]
                
                # 檢查日期範圍
                cursor.execute("SELECT MIN(date), MAX(date) FROM pe_data")
                date_range = cursor.fetchone()
                
                print(f"\n📊 新檔案驗證結果:")
                print(f"   檔案大小: {os.path.getsize(new_path):,} bytes")
                print(f"   記錄數: {record_count:,}")
                print(f"   日期範圍: {date_range[0]} 至 {date_range[1]}")
                print(f"✅ 資料庫驗證成功")
                
            else:
                print(f"⚠️ 資料庫中沒有 pe_data 表格")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 資料庫驗證失敗: {e}")
            return False
    
    return True

def update_config_files():
    """更新配置檔案中的路徑"""
    print(f"\n🔧 更新配置檔案...")
    
    config_files = [
        'app_config.json',
        'database_config.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替換路徑
                old_content = content
                content = content.replace('pe.db', 'pe_data.db')
                
                if content != old_content:
                    # 備份原檔案
                    backup_config = f"{config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(config_file, backup_config)
                    print(f"📦 已備份配置檔案: {backup_config}")
                    
                    # 寫入新內容
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ 已更新配置檔案: {config_file}")
                else:
                    print(f"ℹ️ 配置檔案無需更新: {config_file}")
                    
            except Exception as e:
                print(f"❌ 更新配置檔案失敗 {config_file}: {e}")
        else:
            print(f"⚠️ 配置檔案不存在: {config_file}")

def main():
    """主函數"""
    print("🚀 開始 PE 資料庫重新命名程序")
    
    # 重新命名資料庫檔案
    if rename_pe_database():
        print(f"\n✅ PE 資料庫重新命名完成")
        
        # 更新配置檔案
        update_config_files()
        
        print(f"\n🎉 所有操作完成！")
        print(f"")
        print(f"📋 變更摘要:")
        print(f"   ✅ PE 資料庫檔名已從 pe.db 改為 pe_data.db")
        print(f"   ✅ 相關配置檔案已更新")
        print(f"   ✅ 程式碼中的路徑已修改")
        print(f"")
        print(f"🚀 現在可以正常使用:")
        print(f"   python auto_update.py pe")
        print(f"   python auto_update.py")
        
    else:
        print(f"\n❌ PE 資料庫重新命名失敗")

if __name__ == "__main__":
    main()
