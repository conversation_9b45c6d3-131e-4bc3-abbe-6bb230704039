#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查所有可能的資料庫檔案
"""

import sqlite3
import pandas as pd
import os

def check_all_db_files():
    """檢查所有可能的資料庫檔案"""
    
    print("=" * 60)
    print("📂 檢查所有資料庫檔案")
    print("=" * 60)
    
    # 檢查當前目錄的所有 .db 檔案
    current_dir_dbs = []
    for file in os.listdir('.'):
        if file.endswith('.db'):
            current_dir_dbs.append(file)
    
    print(f"📋 當前目錄的 .db 檔案: {current_dir_dbs}")
    
    # 檢查每個資料庫檔案
    all_db_paths = [
        'D:/Finlab/history/tables/newprice.db',
        'D:/Finlab/history/tables/price.pkl',  # 檢查是否有 pkl 檔案
    ] + current_dir_dbs
    
    for db_path in all_db_paths:
        if not os.path.exists(db_path):
            continue
            
        print(f"\n🔍 檢查: {db_path}")
        
        # 檢查檔案大小
        file_size = os.path.getsize(db_path)
        print(f"   📊 檔案大小: {file_size:,} bytes")
        
        if file_size == 0:
            print(f"   ⚠️ 檔案為空")
            continue
        
        # 檢查檔案修改時間
        import datetime
        mod_time = os.path.getmtime(db_path)
        mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
        print(f"   🕒 最後修改: {mod_time_str}")
        
        if db_path.endswith('.pkl'):
            print(f"   📋 這是 pickle 檔案，跳過")
            continue
            
        try:
            # 連接資料庫
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 檢查表格
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            print(f"   📋 表格: {table_names}")
            
            # 檢查是否有股價相關的表格
            price_tables = []
            for table_name in table_names:
                if any(keyword in table_name.lower() for keyword in ['stock', 'price', 'daily', 'trading']):
                    price_tables.append(table_name)
            
            if price_tables:
                print(f"   💰 股價相關表格: {price_tables}")
                
                for table in price_tables:
                    try:
                        # 檢查表格結構
                        cursor.execute(f"PRAGMA table_info({table})")
                        columns = cursor.fetchall()
                        column_names = [col[1] for col in columns]
                        print(f"      📊 {table} 欄位: {column_names}")
                        
                        # 檢查記錄數
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"      📈 {table} 記錄數: {count:,}")
                        
                        if count > 0:
                            # 檢查日期欄位
                            date_columns = [col for col in column_names if 'date' in col.lower()]
                            if date_columns:
                                date_col = date_columns[0]
                                cursor.execute(f"SELECT MAX({date_col}) FROM {table}")
                                max_date = cursor.fetchone()[0]
                                print(f"      📅 {table} 最新日期: {max_date}")
                                
                                # 檢查最近幾天的資料
                                cursor.execute(f"""
                                    SELECT {date_col}, COUNT(*) 
                                    FROM {table} 
                                    GROUP BY {date_col} 
                                    ORDER BY {date_col} DESC 
                                    LIMIT 5
                                """)
                                recent_data = cursor.fetchall()
                                print(f"      📋 {table} 最近5天:")
                                for date, count in recent_data:
                                    print(f"         {date}: {count} 筆")
                    
                    except Exception as e:
                        print(f"      ❌ 檢查表格 {table} 失敗: {e}")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ 讀取資料庫失敗: {e}")
    
    # 特別檢查是否有其他可能的 newprice.db 位置
    print(f"\n🔍 搜尋其他可能的 newprice.db 位置:")
    
    search_paths = [
        'D:/Finlab/',
        'D:/Finlab/history/',
        'D:/Finlab/backup/',
        'D:/Finlab/backup/O3mh_strategy2AA/',
    ]
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"   📂 搜尋: {search_path}")
            try:
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file == 'newprice.db':
                            full_path = os.path.join(root, file)
                            file_size = os.path.getsize(full_path)
                            mod_time = os.path.getmtime(full_path)
                            mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                            print(f"      ✅ 找到: {full_path}")
                            print(f"         大小: {file_size:,} bytes, 修改時間: {mod_time_str}")
            except Exception as e:
                print(f"      ❌ 搜尋失敗: {e}")

if __name__ == "__main__":
    check_all_db_files()
