#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試間距修復和圖表日期軸修復
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class SpacingAndChartFixTestWindow(QMainWindow):
    """間距和圖表修復測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 間距和圖表修復測試")
        self.setGeometry(100, 100, 1200, 800)
        
        # 設置黑底主題
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
        """)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🔧 界面修復完成")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 20px; padding: 20px;")
        layout.addWidget(title_label)
        
        # 修復說明
        fix_info = QTextEdit()
        fix_info.setMaximumHeight(400)
        fix_info.setStyleSheet("""
            QTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
            }
        """)
        fix_info.setHtml("""
        <h2 style="color: #00d4ff;">🔧 界面修復完成報告</h2>
        
        <h3 style="color: #ffffff;">✅ 問題1：複選框間距修復</h3>
        
        <h4 style="color: #cccccc;">🎯 問題描述</h4>
        <ul>
            <li>複選框文字擠在一起，缺乏視覺分隔</li>
            <li>用戶反饋界面看起來擁擠</li>
            <li>需要更好的視覺層次</li>
        </ul>
        
        <h4 style="color: #cccccc;">🔧 修復方案</h4>
        <ul>
            <li><b>增加垂直間距</b>：data_layout.setSpacing(8) - 複選框間距從3px增加到8px</li>
            <li><b>增加內邊距</b>：padding從8px 12px增加到12px 15px</li>
            <li><b>增加最小高度</b>：min-height從25px增加到35px</li>
            <li><b>增加底部邊距</b>：margin-bottom從5px增加到10px</li>
            <li><b>增加間距</b>：spacing從8px增加到12px</li>
            <li><b>調整群組邊距</b>：setContentsMargins(15, 15, 15, 15)</li>
        </ul>
        
        <h4 style="color: #cccccc;">📊 修復效果</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; color: #ffffff;">
            <tr style="background-color: #505050;">
                <th style="padding: 8px;">屬性</th>
                <th style="padding: 8px;">修復前</th>
                <th style="padding: 8px;">修復後</th>
                <th style="padding: 8px;">改善效果</th>
            </tr>
            <tr>
                <td style="padding: 8px;">垂直間距</td>
                <td style="padding: 8px;">3px</td>
                <td style="padding: 8px;">8px</td>
                <td style="padding: 8px;">+167%</td>
            </tr>
            <tr>
                <td style="padding: 8px;">內邊距</td>
                <td style="padding: 8px;">8px 12px</td>
                <td style="padding: 8px;">12px 15px</td>
                <td style="padding: 8px;">+50% +25%</td>
            </tr>
            <tr>
                <td style="padding: 8px;">最小高度</td>
                <td style="padding: 8px;">25px</td>
                <td style="padding: 8px;">35px</td>
                <td style="padding: 8px;">+40%</td>
            </tr>
            <tr>
                <td style="padding: 8px;">底部邊距</td>
                <td style="padding: 8px;">5px</td>
                <td style="padding: 8px;">10px</td>
                <td style="padding: 8px;">+100%</td>
            </tr>
        </table>
        
        <h3 style="color: #ffffff;">✅ 問題2：圖表橫軸日期修復</h3>
        
        <h4 style="color: #cccccc;">🎯 問題描述</h4>
        <ul>
            <li>圖表橫軸沒有正確顯示日期</li>
            <li>歷史指數圖表只顯示天數，不是實際日期</li>
            <li>用戶無法直觀看到時間信息</li>
        </ul>
        
        <h4 style="color: #cccccc;">🔧 修復方案</h4>
        <ul>
            <li><b>歷史指數圖表</b>：
                <ul>
                    <li>使用 pd.to_datetime() 正確解析日期</li>
                    <li>轉換為時間戳：[d.timestamp() for d in valid_dates]</li>
                    <li>使用 pg.DateAxisItem() 創建日期軸</li>
                    <li>設置軸標籤為 '日期' 而不是 '日期 (天數)'</li>
                </ul>
            </li>
            <li><b>市場指數圖表</b>：
                <ul>
                    <li>檢查 crawl_time 欄位</li>
                    <li>如果有時間數據，使用日期軸</li>
                    <li>如果沒有，回退到序號軸</li>
                </ul>
            </li>
            <li><b>融資融券圖表</b>：
                <ul>
                    <li>同樣支持時間軸</li>
                    <li>智能檢測數據類型</li>
                    <li>自動選擇合適的軸類型</li>
                </ul>
            </li>
        </ul>
        
        <h4 style="color: #cccccc;">📈 技術實現</h4>
        <ul>
            <li><b>日期解析</b>：pd.to_datetime(df['Date'], format='%Y%m%d', errors='coerce')</li>
            <li><b>時間戳轉換</b>：[d.timestamp() for d in valid_dates]</li>
            <li><b>日期軸設置</b>：axis = pg.DateAxisItem(orientation='bottom')</li>
            <li><b>軸項目設置</b>：self.chart_widget.setAxisItems({'bottom': axis})</li>
            <li><b>柱狀圖寬度</b>：width = 86400 (1天的秒數) for 時間軸</li>
        </ul>
        
        <h3 style="color: #ffffff;">🎯 修復成果</h3>
        <ul>
            <li><b>視覺改善</b> - 複選框不再擠在一起，有清晰的視覺分隔</li>
            <li><b>可讀性提升</b> - 圖表橫軸正確顯示日期，便於理解</li>
            <li><b>用戶體驗</b> - 界面更加專業和易用</li>
            <li><b>功能完整</b> - 圖表功能完全可用，數據展示清晰</li>
        </ul>
        
        <h3 style="color: #ffffff;">🚀 下一步建議</h3>
        <ul>
            <li>測試不同數據集的圖表顯示</li>
            <li>驗證日期格式在不同情況下的兼容性</li>
            <li>考慮添加圖表縮放和平移功能</li>
            <li>優化大數據集的圖表性能</li>
        </ul>
        """)
        layout.addWidget(fix_info)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 測試修復後的界面")
        test_btn.clicked.connect(self.test_fixed_interface)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #00d4ff;
                color: #000000;
                border: none;
                padding: 15px 35px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #00b8e6;
            }
        """)
        layout.addWidget(test_btn)
        
        # 成功提示
        success_label = QLabel("🎉 修復完成：間距更合理，圖表橫軸正確顯示日期")
        success_label.setStyleSheet("color: #00ff00; font-size: 16px; font-weight: bold; margin: 15px; padding: 15px; background-color: #2d4a2d; border: 1px solid #4a7c4a; border-radius: 8px; text-align: center;")
        layout.addWidget(success_label)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #00d4ff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("🔧 間距和圖表修復測試程式已啟動")
        self.log("✅ 複選框間距已優化")
        self.log("✅ 圖表日期軸已修復")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_fixed_interface(self):
        """測試修復後的界面"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟修復後的界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 修復後的界面已成功創建")
            self.log("🔧 請檢查複選框間距是否合理")
            self.log("📈 請測試圖表分析的日期軸顯示")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = SpacingAndChartFixTestWindow()
    window.show()
    
    print("🔧 間距和圖表修復測試程式已啟動")
    print("✅ 複選框間距已優化")
    print("✅ 圖表日期軸已修復")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
