#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新後的月營收GUI功能
"""

import sys
import os

def test_gui():
    """測試月營收GUI"""
    
    print("🧪 測試月營收GUI功能")
    print("=" * 40)
    
    try:
        # 導入GUI模組
        from unified_monthly_revenue_gui import UnifiedMonthlyRevenueGUI
        
        print("✅ 成功導入 UnifiedMonthlyRevenueGUI")
        
        # 檢查CSV檔案是否存在
        csv_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.csv"
        if os.path.exists(csv_file):
            print(f"✅ 找到CSV檔案: {csv_file}")
        else:
            print(f"⚠️ CSV檔案不存在: {csv_file}")
        
        # 檢查資料庫檔案
        db_file = "D:/Finlab/history/tables/monthly_revenue.db"
        if os.path.exists(db_file):
            print(f"✅ 找到資料庫檔案: {db_file}")
            
            # 檢查資料庫內容
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
            count = cursor.fetchone()[0]
            print(f"📊 資料庫記錄數: {count:,}")
            
            conn.close()
        else:
            print(f"⚠️ 資料庫檔案不存在: {db_file}")
        
        print(f"\n🚀 啟動GUI...")
        print(f"💡 使用方式:")
        print(f"  1. 點擊 '🆕 新建資料庫' 創建新的資料庫")
        print(f"  2. 點擊 '📥 匯入CSV' 匯入月營收數據")
        print(f"  3. 點擊 '📊 查看數據庫' 檢視匯入結果")
        print(f"  4. 使用其他功能進行數據下載")
        
        # 創建並運行GUI
        app = UnifiedMonthlyRevenueGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        print(f"請確認以下檔案存在:")
        print(f"  • unified_monthly_revenue_gui.py")
        print(f"  • enhanced_monthly_revenue_downloader.py 或")
        print(f"  • goodinfo_monthly_revenue_downloader.py")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui()
