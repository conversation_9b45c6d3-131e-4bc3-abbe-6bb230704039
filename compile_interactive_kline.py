#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
編譯互動式K線圖版本
恢復原來的滑鼠懸停功能和詳細資訊顯示
"""

import PyInstaller.__main__
import os
import sys
import shutil
import time

def clean_build_files():
    """清理編譯文件"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已清理目錄: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"✅ 已清理文件: {file}")

def compile_interactive_kline():
    """編譯互動式K線圖版本"""
    print("🚀 開始編譯互動式K線圖版本...")
    print("=" * 60)
    
    # 清理舊文件
    clean_build_files()
    
    # 編譯參數
    args = [
        'O3mh_gui_v21_optimized.py',
        '--onefile',
        '--windowed',
        '--name=StockAnalyzer_Interactive',
        '--icon=icon.ico' if os.path.exists('icon.ico') else '',
        '--add-data=app_config.json;.',
        '--add-data=finmind_config.json;.',
        '--exclude-module=PyQt5',
        '--exclude-module=PySide2',
        '--exclude-module=PySide6',
        '--exclude-module=tkinter',
        '--hidden-import=PyQt6',
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        '--hidden-import=pyqtgraph',
        '--hidden-import=pyqtgraph.graphicsItems',
        '--hidden-import=pyqtgraph.graphicsItems.PlotItem',
        '--hidden-import=pyqtgraph.graphicsItems.ViewBox',
        '--hidden-import=pyqtgraph.graphicsItems.AxisItem',
        '--hidden-import=pyqtgraph.graphicsItems.LegendItem',
        '--hidden-import=pyqtgraph.graphicsItems.TextItem',
        '--hidden-import=pyqtgraph.graphicsItems.InfiniteLine',
        '--hidden-import=pyqtgraph.graphicsItems.ScatterPlotItem',
        '--hidden-import=pyqtgraph.graphicsItems.BarGraphItem',
        '--hidden-import=pyqtgraph.SignalProxy',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=sqlite3',
        '--hidden-import=requests',
        '--hidden-import=yfinance',
        '--hidden-import=finmind',
        '--hidden-import=selenium',
        '--hidden-import=bs4',
        '--hidden-import=lxml',
        '--hidden-import=openpyxl',
        '--hidden-import=xlsxwriter',
        '--collect-all=pyqtgraph',
        '--collect-all=PyQt6',
        '--noconfirm',
        '--clean'
    ]
    
    # 移除空的icon參數
    args = [arg for arg in args if arg]
    
    try:
        print("📦 開始PyInstaller編譯...")
        PyInstaller.__main__.run(args)
        
        # 檢查編譯結果
        exe_path = "dist/StockAnalyzer_Interactive.exe"
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path)
            print(f"\n✅ 編譯成功！")
            print(f"📁 檔案: {exe_path}")
            print(f"📊 大小: {size / (1024*1024):.2f} MB")
            
            # 創建啟動腳本
            create_launch_script()
            
            print(f"\n🎉 互動式K線圖版編譯完成！")
            print(f"\n📁 輸出文件:")
            print(f"   - {exe_path}")
            print(f"   - 啟動互動式K線圖版.bat")
            
            print(f"\n🚀 使用方法:")
            print(f"   雙擊執行: 啟動互動式K線圖版.bat")
            
            print(f"\n✨ 互動式K線圖版特點:")
            print(f"   ✓ 滑鼠懸停顯示詳細資訊")
            print(f"   ✓ 完整的K線圖和移動平均線")
            print(f"   ✓ 包含雙週線MA10指標")
            print(f"   ✓ 互動式十字線和價格顯示")
            print(f"   ✓ 成交量、RSI等技術指標")
            print(f"   ✓ 專業的技術分析工具")
            
            print(f"\n🎯 現在您的K線圖功能完全恢復了！")
            return True
            
        else:
            print("❌ 編譯失敗：找不到輸出文件")
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程中出現錯誤: {e}")
        return False

def create_launch_script():
    """創建啟動腳本"""
    script_content = '''@echo off
chcp 65001 > nul
title 台股智能選股系統 - 互動式K線圖版

echo.
echo ========================================
echo   台股智能選股系統 - 互動式K線圖版
echo ========================================
echo.
echo 🚀 正在啟動互動式K線圖版...
echo.

if exist "dist\\StockAnalyzer_Interactive.exe" (
    start "" "dist\\StockAnalyzer_Interactive.exe"
    echo ✅ 互動式K線圖版已啟動！
    echo.
    echo 💡 特色功能:
    echo    ✓ 滑鼠懸停顯示詳細資訊
    echo    ✓ 互動式十字線和價格顯示  
    echo    ✓ 完整的技術指標
    echo    ✓ 雙週線MA10功能
    echo.
) else (
    echo ❌ 找不到執行檔: dist\\StockAnalyzer_Interactive.exe
    echo 請先執行編譯腳本
    pause
)
'''
    
    with open('啟動互動式K線圖版.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 創建互動式K線圖啟動腳本: 啟動互動式K線圖版.bat")

def main():
    """主函數"""
    print("🔧 台股智能選股系統 - 互動式K線圖版編譯器")
    print("=" * 60)
    
    if compile_interactive_kline():
        print("\n🎊 編譯完成！")
        print("\n✅ 互動式K線圖功能已恢復:")
        print("   ✓ 滑鼠懸停顯示完整股票資訊")
        print("   ✓ 互動式十字線跟隨滑鼠")
        print("   ✓ 實時價格和技術指標顯示")
        print("   ✓ 包含雙週線MA10等移動平均線")
        print("   ✓ RSI、成交量等技術分析工具")
        
        print("\n🚀 立即測試:")
        print("   雙擊執行: 啟動互動式K線圖版.bat")
        
        print("\n🎯 您的互動式K線圖功能完全恢復了！")
        return True
    else:
        print("\n❌ 編譯失敗！")
        return False

if __name__ == "__main__":
    main()
