#!/usr/bin/env python3
"""
膽小貓策略模組
從原始程式中提取的TimidCatStrategy類別
"""
import logging
import pandas as pd
import numpy as np
from .base_strategy import BaseStrategy


class TimidCatStrategy(BaseStrategy):
    """膽小貓策略 - 保守型投資策略，重視風險控制"""

    def __init__(self):
        super().__init__(
            name="膽小貓策略",
            description="保守型投資策略，重視風險控制，適合穩健投資者",
            strategy_type="conservative_value"
        )

        # 策略參數
        self.max_volatility = 0.3      # 最大波動率30%
        self.min_volume = 100000       # 最小成交量
        self.rsi_lower = 30           # RSI下限
        self.rsi_upper = 70           # RSI上限
        self.ma_period = 20           # 移動平均線週期
        self.lookback_period = 60     # 回看期間

    def calculate_volatility(self, df, period=20):
        """計算價格波動率"""
        try:
            if len(df) < period:
                return None

            returns = df['Close'].pct_change().dropna()
            volatility = returns.rolling(period).std() * np.sqrt(252)  # 年化波動率
            return volatility.iloc[-1] if not volatility.empty else None

        except Exception as e:
            logging.error(f"計算波動率失敗: {e}")
            return None

    def check_low_volatility(self, df):
        """檢查低波動率條件"""
        try:
            volatility = self.calculate_volatility(df)

            if volatility is None:
                return False, "無法計算波動率"

            if volatility <= self.max_volatility:
                return True, f"波動率 {volatility:.1%} <= {self.max_volatility:.1%}"
            else:
                return False, f"波動率過高 {volatility:.1%} > {self.max_volatility:.1%}"

        except Exception as e:
            return False, f"波動率檢查失敗: {str(e)}"

    def check_stable_trend(self, df):
        """檢查穩定趨勢"""
        try:
            if len(df) < self.ma_period + 5:
                return False, "數據不足以檢查趨勢"

            close = df['Close']
            ma = close.rolling(self.ma_period).mean()

            current_price = close.iloc[-1]
            current_ma = ma.iloc[-1]

            # 檢查價格是否在均線附近（±5%）
            price_deviation = abs(current_price - current_ma) / current_ma

            if price_deviation <= 0.05:
                return True, f"價格穩定 偏離MA{self.ma_period}: {price_deviation:.1%}"
            else:
                return False, f"價格偏離過大 偏離MA{self.ma_period}: {price_deviation:.1%}"

        except Exception as e:
            return False, f"趨勢檢查失敗: {str(e)}"

    def check_moderate_rsi(self, df):
        """檢查適中的RSI"""
        try:
            rsi = self.calculate_rsi(df['Close'])

            if rsi.empty:
                return False, "無法計算RSI"

            current_rsi = rsi.iloc[-1]

            if self.rsi_lower <= current_rsi <= self.rsi_upper:
                return True, f"RSI適中 {current_rsi:.1f} 在 {self.rsi_lower}-{self.rsi_upper} 區間"
            else:
                return False, f"RSI極端 {current_rsi:.1f} 不在 {self.rsi_lower}-{self.rsi_upper} 區間"

        except Exception as e:
            return False, f"RSI檢查失敗: {str(e)}"

    def check_adequate_liquidity(self, df):
        """檢查充足流動性"""
        try:
            current_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume'].iloc[-20:].mean()

            if current_volume >= self.min_volume and avg_volume >= self.min_volume * 0.8:
                return True, f"流動性充足 當前:{current_volume/1000:.0f}張 平均:{avg_volume/1000:.0f}張"
            else:
                return False, f"流動性不足 當前:{current_volume/1000:.0f}張 < {self.min_volume/1000:.0f}張"

        except Exception as e:
            return False, f"流動性檢查失敗: {str(e)}"

    def analyze_stock(self, df, **kwargs):
        """分析單一股票是否符合膽小貓策略"""
        try:
            if df.empty or len(df) < self.lookback_period:
                return {
                    'suitable': False,
                    'reason': f'數據不足，需要至少{self.lookback_period}日數據',
                    'details': {},
                    'score': 0,
                    'strategy_name': self.name
                }

            results = {}
            total_score = 0
            max_score = 4  # 四個主要條件

            # 1. 低波動率
            volatility_pass, volatility_reason = self.check_low_volatility(df)
            results['低波動率'] = {'pass': volatility_pass, 'reason': volatility_reason}
            if volatility_pass:
                total_score += 1

            # 2. 穩定趨勢
            trend_pass, trend_reason = self.check_stable_trend(df)
            results['穩定趨勢'] = {'pass': trend_pass, 'reason': trend_reason}
            if trend_pass:
                total_score += 1

            # 3. 適中RSI
            rsi_pass, rsi_reason = self.check_moderate_rsi(df)
            results['適中RSI'] = {'pass': rsi_pass, 'reason': rsi_reason}
            if rsi_pass:
                total_score += 1

            # 4. 充足流動性
            liquidity_pass, liquidity_reason = self.check_adequate_liquidity(df)
            results['充足流動性'] = {'pass': liquidity_pass, 'reason': liquidity_reason}
            if liquidity_pass:
                total_score += 1

            # 判斷是否符合條件（保守策略，需要所有條件都通過）
            suitable = total_score >= 3  # 至少3個條件通過

            # 生成總結
            passed_items = [k for k, v in results.items() if v['pass']]
            reason = f"膽小貓評分: {total_score}/{max_score} (通過: {','.join(passed_items)})"

            if total_score < 3:
                reason += " | ⚠️風險過高，不適合保守投資"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'strategy_name': self.name
            }

        except Exception as e:
            logging.error(f"膽小貓策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }