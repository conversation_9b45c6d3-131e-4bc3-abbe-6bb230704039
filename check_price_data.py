#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查股價資料庫結構
"""

import sqlite3
import pandas as pd
from datetime import datetime

def check_price_database():
    """檢查股價資料庫"""
    try:
        price_db_path = 'D:/Finlab/history/tables/price.db'
        conn = sqlite3.connect(price_db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("📊 股價資料庫表格:")
        for table in tables:
            print(f"   {table[0]}")
        
        # 檢查主要表格的結構
        if tables:
            main_table = tables[0][0]  # 使用第一個表格
            cursor.execute(f"PRAGMA table_info({main_table})")
            columns = cursor.fetchall()
            print(f"\n📋 {main_table} 表格結構:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            # 檢查2025年7月資料
            cursor.execute(f"SELECT COUNT(*) FROM {main_table} WHERE date LIKE '2025-07%'")
            count_2025_07 = cursor.fetchone()[0]
            print(f"\n📅 2025年7月資料筆數: {count_2025_07:,}")
            
            # 檢查最新資料日期
            cursor.execute(f"SELECT MAX(date) FROM {main_table}")
            latest_date = cursor.fetchone()[0]
            print(f"📅 最新資料日期: {latest_date}")
            
            # 檢查樣本資料
            cursor.execute(f"""
                SELECT * FROM {main_table} 
                WHERE date LIKE '2025-07%' 
                LIMIT 5
            """)
            samples = cursor.fetchall()
            print(f"\n📋 2025年7月樣本資料:")
            for sample in samples:
                print(f"   {sample}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查股價資料庫失敗: {e}")
        return False

def test_eps_calculation_with_price():
    """測試使用股價計算EPS"""
    print("\n" + "=" * 60)
    print("🧮 測試EPS計算 (股價 / 本益比)")
    print("=" * 60)
    
    # 模擬資料
    test_data = [
        ("2330", "台積電", 580.0, 15.2),  # 股價580, PE 15.2
        ("2317", "鴻海", 120.0, 12.8),    # 股價120, PE 12.8
        ("2454", "聯發科", 850.0, 18.5),  # 股價850, PE 18.5
    ]
    
    print("股票代碼 | 股票名稱 | 股價   | 本益比 | 計算EPS")
    print("-" * 50)
    
    for stock_id, stock_name, price, pe_ratio in test_data:
        if pe_ratio and pe_ratio > 0:
            eps = price / pe_ratio
            print(f"{stock_id:<8} | {stock_name:<8} | {price:>6.1f} | {pe_ratio:>6.1f} | {eps:>7.2f}")
        else:
            print(f"{stock_id:<8} | {stock_name:<8} | {price:>6.1f} | {pe_ratio:>6.1f} | {'N/A':>7}")
    
    print("\n✅ EPS計算公式: EPS = 股價 / 本益比")
    print("✅ 這樣計算出的EPS更準確，反映當前股價水準")
    return True

def check_price_pe_integration():
    """檢查股價和PE資料的整合可能性"""
    print("\n" + "=" * 60)
    print("🔗 檢查股價和PE資料整合")
    print("=" * 60)
    
    try:
        # 檢查PE資料
        pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
        pe_conn = sqlite3.connect(pe_db_path)
        
        pe_query = """
        SELECT stock_id, stock_name, pe_ratio
        FROM pe_data 
        WHERE date = '2025-07-25'
        LIMIT 5
        """
        pe_df = pd.read_sql_query(pe_query, pe_conn)
        pe_conn.close()
        
        print("📊 PE資料樣本:")
        for _, row in pe_df.iterrows():
            print(f"   {row['stock_id']} {row['stock_name']} PE:{row['pe_ratio']:.2f}")
        
        # 檢查股價資料
        price_db_path = 'D:/Finlab/history/tables/price.db'
        price_conn = sqlite3.connect(price_db_path)
        
        # 先檢查表格結構
        cursor = price_conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if tables:
            main_table = tables[0][0]
            print(f"\n📊 股價資料表格: {main_table}")
            
            # 嘗試獲取股價資料
            try:
                price_query = f"""
                SELECT * FROM {main_table}
                WHERE date LIKE '2025-07%'
                LIMIT 3
                """
                price_df = pd.read_sql_query(price_query, price_conn)
                print(f"📊 股價資料樣本:")
                print(price_df.head())
                
            except Exception as e:
                print(f"⚠️ 無法獲取股價資料: {e}")
        
        price_conn.close()
        
        print("\n✅ 整合策略:")
        print("1. 從PE資料庫獲取本益比")
        print("2. 從股價資料庫獲取對應日期的收盤價")
        print("3. 計算EPS = 收盤價 / 本益比")
        
        return True
        
    except Exception as e:
        print(f"❌ 整合檢查失敗: {e}")
        return False

if __name__ == "__main__":
    print("🔍 檢查股價資料庫...")
    
    success1 = check_price_database()
    success2 = test_eps_calculation_with_price()
    success3 = check_price_pe_integration()
    
    if success1 and success2 and success3:
        print("\n🎉 股價資料庫檢查完成！")
    else:
        print("\n❌ 檢查過程中發生錯誤。")
