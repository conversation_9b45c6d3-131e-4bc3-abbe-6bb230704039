@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 最小化穩定版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        最小化穩定版 - 核心功能
echo ========================================
echo.

if exist "dist\StockAnalyzer_Minimal.exe" (
    echo ✅ 找到最小化穩定版
    echo 🚀 正在啟動...
    echo.
    echo 💡 最小化穩定版特點：
    echo    ✓ 只包含最核心的功能模組
    echo    ✓ 排除所有有問題的依賴
    echo    ✓ 最高的穩定性和兼容性
    echo    ✓ 最小的檔案大小
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Minimal.exe"
    
    echo ✅ 最小化穩定版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到最小化穩定版
    echo.
    echo 請重新編譯：
    echo    python minimal_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 功能說明：
echo    ✓ 基本股票列表和篩選功能
echo    ✓ 數據查詢和顯示功能
echo    ✓ Excel 報告導出功能
echo    ✓ 基本的用戶界面操作
echo.
echo ⚠️ 注意：
echo    - 圖表功能可能受限
echo    - 部分進階功能不可用
echo    - 但核心選股功能完全保留
echo.

timeout /t 8 >nul
