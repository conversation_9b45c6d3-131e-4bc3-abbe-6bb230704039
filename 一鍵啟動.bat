@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 一鍵啟動

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo          一鍵啟動 - 最簡單方式
echo ========================================
echo.

echo 🔄 正在啟動 Python 啟動器...
python simple_launcher.py

if errorlevel 1 (
    echo.
    echo ❌ Python 啟動器失敗，嘗試直接啟動...
    echo.
    
    if exist "dist\台股智能選股系統_修復版.exe" (
        echo ✅ 找到修復版，直接啟動...
        cd /d "dist"
        start "" "台股智能選股系統_修復版.exe"
        echo ✅ 已啟動修復版！
    ) else if exist "dist\台股智能選股系統.exe" (
        echo ✅ 找到標準版，直接啟動...
        cd /d "dist"
        start "" "台股智能選股系統.exe"
        echo ✅ 已啟動標準版！
    ) else (
        echo ❌ 找不到任何可執行檔！
        echo.
        echo 💡 請檢查：
        echo    1. 程式是否已正確編譯
        echo    2. dist 目錄是否存在
        echo    3. 執行權限是否足夠
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ✅ 啟動完成！
timeout /t 3 >nul
