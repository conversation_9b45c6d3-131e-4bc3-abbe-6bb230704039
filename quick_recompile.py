#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速重新編譯腳本
修復 inspect 模組問題並重新編譯
"""

import os
import sys
import subprocess
import shutil

def quick_recompile():
    """快速重新編譯"""
    print("🚀 快速重新編譯台股智能選股系統")
    print("=" * 50)
    
    # 檢查文件
    if os.path.exists('O3mh_gui_v21_optimized_fixed.py'):
        main_file = 'O3mh_gui_v21_optimized_fixed.py'
        print("✅ 使用修復版本")
    elif os.path.exists('O3mh_gui_v21_optimized.py'):
        main_file = 'O3mh_gui_v21_optimized.py'
        print("⚠️ 使用原始版本")
    else:
        print("❌ 找不到主程式文件")
        return False
    
    # 創建簡化的 spec 文件
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{main_file}'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('strategies', 'strategies'),
        ('*.json', '.'),
        ('*.yaml', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'pyqtgraph',
        'pandas',
        'numpy',
        'sqlite3',
        'requests',
        'bs4',
        'selenium',
        'matplotlib',
        'openpyxl',
        'xlsxwriter',
        'inspect',
        'importlib',
        'importlib.util',
        'json',
        'csv',
        'datetime',
        'threading',
        'concurrent.futures',
        'logging',
        'traceback',
        'calendar',
        'stock_filter',
        'enhanced_market_scanner',
        'real_market_data_fetcher',
        'smart_trading_strategies',
        'roe_data_crawler',
        'market_data_crawler',
        'unified_monitor_manager',
        'auto_update',
        'enhanced_data_fetcher',
        'finmind_data_provider',
        'pe_data_provider',
        'real_data_sources',
        'safe_market_scanner',
        'optimized_market_scanner',
        'intraday_data_fetcher',
        'smart_yahoo_fetcher',
        'twelvedata_fetcher',
        'enhanced_yfinance_fetcher',
        'finmind_quota_manager',
        'stock_name_mapping',
        'strategy_intersection_analyzer',
        'multi_strategy_chart',
        'stock_basic_info_crawler',
        'google_stock_news_gui',
        'improved_roe_downloader',
        'roe_data_downloader_gui',
        'twse_market_data_dialog',
        'market_data_crawler_dialog',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'difflib',
        'pydoc',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統_修復版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    # 寫入 spec 文件
    with open('quick_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 創建快速編譯規格文件")
    
    # 清理 build 目錄
    if os.path.exists('build'):
        try:
            shutil.rmtree('build')
            print("🧹 清理 build 目錄")
        except:
            print("⚠️ 無法清理 build 目錄")
    
    # 執行編譯
    try:
        cmd = ['pyinstaller', '--clean', '--noconfirm', 'quick_compile.spec']
        print(f"🔨 執行編譯: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            
            # 檢查輸出文件
            exe_path = 'dist/台股智能選股系統_修復版.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"📁 可執行檔: {exe_path}")
                print(f"📊 檔案大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_startup_script()
                
                return True
            else:
                print("❌ 找不到編譯後的可執行檔")
                return False
        else:
            print("❌ 編譯失敗！")
            print("錯誤輸出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_startup_script():
    """創建啟動腳本"""
    script_content = '''@echo off
chcp 65001 >nul
title 台股智能選股系統 - 修復版啟動器

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo          修復版 - 解決模組問題
echo ========================================
echo.

if exist "dist\\台股智能選股系統_修復版.exe" (
    echo ✅ 找到修復版程式
    echo 🔄 正在啟動...
    echo.
    
    cd /d "dist"
    start "" "台股智能選股系統_修復版.exe"
    
    echo ✅ 程式已啟動！
    echo.
    echo 💡 修復內容：
    echo    ✓ 解決 inspect 模組缺失問題
    echo    ✓ 添加模組兼容性層
    echo    ✓ 優化啟動流程
    echo.
    
) else (
    echo ❌ 錯誤：找不到修復版程式
    echo.
    echo 請重新執行編譯：
    echo    python quick_recompile.py
    echo.
)

echo 按任意鍵關閉此視窗...
pause >nul
'''
    
    with open('啟動修復版.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 創建啟動腳本: 啟動修復版.bat")

def main():
    """主函數"""
    if quick_recompile():
        print("\n🎉 快速重新編譯完成！")
        print("\n📁 輸出文件:")
        print("   - dist/台股智能選股系統_修復版.exe")
        print("   - 啟動修復版.bat")
        print("\n🚀 使用方法:")
        print("   雙擊執行: 啟動修復版.bat")
    else:
        print("\n❌ 編譯失敗！")
        return False

if __name__ == "__main__":
    main()
