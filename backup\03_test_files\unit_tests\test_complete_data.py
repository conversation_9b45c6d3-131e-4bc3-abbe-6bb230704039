#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完整的月營收數據下載功能
"""

from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
import pandas as pd
import os

def test_complete_data():
    """測試完整數據下載"""
    
    print("🚀 測試完整月營收數據下載功能")
    print("=" * 60)
    
    # 使用正確的資料庫路徑
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    downloader = MopsBulkRevenueDownloader(db_path)
    
    # 測試2024年6月
    print("📅 下載並匯出 2024年6月 完整數據...")
    results = downloader.download_all_markets(2024, 6)
    
    print("\n📊 下載結果:")
    total = 0
    for market, count in results.items():
        print(f"  {market}: {count} 家公司")
        total += count
    
    print(f"\n✅ 總計: {total} 家公司")
    
    # 檢查資料庫內容
    print("\n💾 檢查資料庫內容:")
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        
        # 檢查表格結構
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(monthly_revenue)")
        columns = cursor.fetchall()
        
        print("  📋 資料庫欄位:")
        for col in columns:
            print(f"    {col[1]} ({col[2]})")
        
        # 檢查數據樣本
        cursor.execute("""
            SELECT stock_id, stock_name, industry, revenue, revenue_mom, revenue_yoy, 
                   cumulative_revenue, cumulative_revenue_yoy, remark, market
            FROM monthly_revenue 
            WHERE year = 2024 AND month = 6 
            ORDER BY revenue DESC 
            LIMIT 5
        """)
        
        sample_data = cursor.fetchall()
        print("\n  🔍 前5筆完整數據:")
        for row in sample_data:
            stock_id, stock_name, industry, revenue, revenue_mom, revenue_yoy, cum_revenue, cum_yoy, remark, market = row
            print(f"    {stock_id} {stock_name} ({industry}) [{market}]:")
            print(f"      營收: {revenue:,} 千元")
            print(f"      月增率: {revenue_mom}% | 年增率: {revenue_yoy}%")
            print(f"      累計營收: {cum_revenue:,} 千元 | 累計年增率: {cum_yoy}%")
            print(f"      備註: {remark}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 資料庫檢查失敗: {e}")
    
    # 檢查Excel檔案
    excel_file = "D:/Finlab/history/tables/monthly_revenue_2024_06.xlsx"
    if os.path.exists(excel_file):
        try:
            df = pd.read_excel(excel_file, sheet_name='月營收資料')
            
            print("📊 Excel檔案檢查:")
            print(f"  記錄數: {len(df):,}")
            print(f"  欄位: {list(df.columns)}")
            
            # 顯示前3筆完整資料
            print("\n  🔍 前3筆Excel資料:")
            for i, row in df.head(3).iterrows():
                print(f"    {row['股票代號']} {row['股票名稱']} ({row['產業別']}) [{row['市場別']}]:")
                print(f"      營收: {row['營收(千元)']} | 月增率: {row['營收月增率(%)']}% | 年增率: {row['營收年增率(%)']}%")
                print(f"      累計營收: {row['累計營收(千元)']} | 累計年增率: {row['累計營收年增率(%)']}%")
                print(f"      備註: {row['備註']}")
                print()
                
        except Exception as e:
            print(f"❌ Excel檔案檢查失敗: {e}")
    else:
        print("❌ Excel檔案不存在")

if __name__ == "__main__":
    test_complete_data()
