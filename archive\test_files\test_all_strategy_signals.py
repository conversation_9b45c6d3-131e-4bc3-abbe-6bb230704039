#!/usr/bin/env python3
"""
測試所有策略的K線圖信號功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_all_strategy_signals():
    """測試所有策略的K線圖信號功能"""
    print("🧪 測試所有策略K線圖信號功能")
    print("=" * 80)
    
    # 策略列表
    strategies = {
        "勝率73.45%": {
            "color": "blue",
            "type": "多重確認",
            "conditions": 6,
            "description": "MA240+MA20雙重趨勢，RSI雙重動量確認"
        },
        "破底反彈高量": {
            "color": "purple", 
            "type": "逆勢抄底",
            "conditions": 3,
            "description": "創新低後放量反彈"
        },
        "阿水一式": {
            "color": "orange",
            "type": "布林突破", 
            "conditions": 3,
            "description": "布林帶突破配合成交量放大"
        },
        "阿水二式": {
            "color": "brown",
            "type": "空方策略",
            "conditions": 2,
            "description": "高檔整理後的空方信號"
        },
        "藏獒": {
            "color": "gold",
            "type": "動能策略",
            "conditions": 3,
            "description": "動能突破配合爆量"
        },
        "CANSLIM量價齊升": {
            "color": "cyan",
            "type": "成長股策略",
            "conditions": 3,
            "description": "創新高配合量價齊升"
        },
        "膽小貓": {
            "color": "pink",
            "type": "低風險策略",
            "conditions": 3,
            "description": "低價低波動創新高"
        },
        "二次創高股票": {
            "color": "green",
            "type": "技術+基本面",
            "conditions": 8,
            "description": "二次創高的強勢突破"
        },
        "三頻率RSI策略": {
            "color": "teal",
            "type": "多時間框架",
            "conditions": 4,
            "description": "三個時間週期RSI確認"
        }
    }
    
    print("📊 策略信號功能總覽")
    print("-" * 80)
    
    for i, (name, info) in enumerate(strategies.items(), 1):
        print(f"{i:2d}. 📈 {name}")
        print(f"    🎨 顏色標識: {info['color']}")
        print(f"    🎯 策略類型: {info['type']}")
        print(f"    📋 買入條件: {info['conditions']}個條件")
        print(f"    💡 策略描述: {info['description']}")
        print()
    
    # 測試信號邏輯
    print("🔍 信號邏輯測試")
    print("-" * 80)
    
    test_signal_logic()
    
    # 測試視覺標記
    print("\n🎨 視覺標記測試")
    print("-" * 80)
    
    test_visual_markers()
    
    print("\n🎉 所有策略K線圖信號功能測試完成！")

def test_signal_logic():
    """測試信號邏輯"""
    
    # 買入信號邏輯
    buy_signals = {
        "勝率73.45%": "MA240↑ + MA20↑ + 量價配合 + RSI雙重確認",
        "破底反彈高量": "創新低 + 反彈5% + 爆量2倍",
        "阿水一式": "布林突破 + 量增1.5倍 + MA20↑ + RSI適中",
        "阿水二式": "跌破MA20後反彈3%",
        "藏獒": "突破MA20 + 爆量2倍 + RSI>60 + 均線多頭",
        "CANSLIM量價齊升": "創50日新高 + 量增1.5倍 + RSI>70 + MA20支撐",
        "膽小貓": "低價股 + 低波動 + 創100日新高 + 量適中",
        "二次創高股票": "8個條件全滿足（技術+基本面）",
        "三頻率RSI策略": "RSI120>55 + RSI60<75 + RSI20上漲 + 高檔頓化"
    }
    
    print("🟢 買入信號邏輯:")
    for strategy, logic in buy_signals.items():
        print(f"   📈 {strategy}: {logic}")
    
    print()
    
    # 賣出信號邏輯
    sell_signals = {
        "勝率73.45%": "RSI6>80 或 MA20下彎",
        "破底反彈高量": "跌破MA10 或 RSI>75",
        "阿水一式": "跌破布林中軌",
        "阿水二式": "高檔整理 + 量縮 + RSI>70",
        "藏獒": "RSI<40 或 跌破MA10",
        "CANSLIM量價齊升": "跌破MA20的8%",
        "膽小貓": "3%停損",
        "二次創高股票": "20MA下彎",
        "三頻率RSI策略": "跌破季線MA60"
    }
    
    print("🔴 賣出信號邏輯:")
    for strategy, logic in sell_signals.items():
        print(f"   📉 {strategy}: {logic}")

def test_visual_markers():
    """測試視覺標記"""
    
    print("🎨 K線圖視覺標記設計:")
    print()
    
    # 買入標記
    print("🟢 買入信號標記:")
    print("   📍 位置: 收盤價98%位置")
    print("   🎨 樣式: 綠色向上箭頭")
    print("   📝 標籤: [策略名稱]買入")
    print("   🎯 觸發: 滿足該策略的買入條件")
    print()
    
    # 賣出標記
    print("🔴 賣出信號標記:")
    print("   📍 位置: 收盤價102%位置")
    print("   🎨 樣式: 紅色向下箭頭")
    print("   📝 標籤: [策略名稱]賣出")
    print("   🎯 觸發: 滿足該策略的賣出條件")
    print()
    
    # 顯示特色
    print("✨ 顯示特色:")
    features = [
        "🎯 智能顯示: 只顯示最近5個信號",
        "🎨 顏色區分: 每個策略有獨特顏色",
        "📊 狀態統計: 底部顯示信號統計",
        "🔄 實時更新: 根據選擇策略動態顯示",
        "📈 歷史回顧: 可查看所有歷史信號",
        "🎪 清晰標註: 箭頭和標籤精確定位"
    ]
    
    for feature in features:
        print(f"   {feature}")

def test_strategy_comparison():
    """測試策略比較"""
    print("\n📊 策略特色比較")
    print("-" * 80)
    
    # 進攻型策略
    print("⚔️ 進攻型策略 (適合多頭市場):")
    aggressive = ["藏獒", "CANSLIM量價齊升", "阿水一式"]
    for strategy in aggressive:
        print(f"   🚀 {strategy}: 追求高報酬，適合強勢股")
    
    print()
    
    # 防守型策略
    print("🛡️ 防守型策略 (適合震盪/空頭市場):")
    defensive = ["膽小貓", "勝率73.45%"]
    for strategy in defensive:
        print(f"   🏰 {strategy}: 注重風險控制，穩健獲利")
    
    print()
    
    # 平衡型策略
    print("⚖️ 平衡型策略 (適合各種市場):")
    balanced = ["二次創高股票", "三頻率RSI策略", "破底反彈高量"]
    for strategy in balanced:
        print(f"   ⚖️ {strategy}: 攻守兼備，靈活應對")
    
    print()
    
    # 特殊策略
    print("🎯 特殊策略:")
    print("   📉 阿水二式: 空方策略，適合高檔放空")

def test_usage_guide():
    """測試使用指南"""
    print("\n📱 使用指南")
    print("-" * 80)
    
    steps = [
        "1️⃣ 選擇策略: 在策略下拉選單中選擇想要的策略",
        "2️⃣ 執行選股: 點擊選股按鈕，系統會根據策略篩選股票",
        "3️⃣ 選擇股票: 在選股結果中選擇想查看的股票",
        "4️⃣ 雙擊查看: 雙擊股票項目開啟K線圖",
        "5️⃣ 觀察信號: 圖表自動顯示該策略的買賣信號",
        "6️⃣ 分析決策: 根據信號標記做出投資決策"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print()
    print("💡 小提示:")
    tips = [
        "🎯 不同策略會顯示不同顏色的信號標記",
        "📊 狀態欄會顯示當前策略的信號統計",
        "🔄 切換策略後重新查看K線圖會顯示新策略信號",
        "📈 只顯示最近5個信號，保持圖表清晰",
        "⚠️ 信號僅供參考，投資需謹慎"
    ]
    
    for tip in tips:
        print(f"   {tip}")

if __name__ == "__main__":
    try:
        test_all_strategy_signals()
        test_strategy_comparison()
        test_usage_guide()
        
        print(f"\n🚀 測試完成！")
        print("💰 現在您擁有9個完整的策略，每個都有獨特的K線圖信號顯示！")
        print("🎯 選擇策略 → 選股 → 雙擊股票 → 查看信號 → 投資決策")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
