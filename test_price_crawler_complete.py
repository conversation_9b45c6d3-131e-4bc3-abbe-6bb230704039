#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完整的 price 爬蟲流程
包括爬取、股票資訊添加、存檔到 price.db
"""

import os
import sys
import sqlite3
import datetime

# 添加 finlab 路徑
sys.path.insert(0, 'finlab')

def test_complete_price_crawler():
    """測試完整的 price 爬蟲流程"""
    
    print("=" * 80)
    print("🧪 測試完整的 price 爬蟲流程")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        # 測試日期 (使用已知有資料的日期)
        test_date = datetime.date(2025, 7, 25)
        print(f"🔍 測試日期: {test_date}")
        
        # 1. 測試爬取
        print(f"\n📡 步驟 1: 爬取股價資料...")
        df = crawl_price(test_date)
        
        if df is None or df.empty:
            print("⚠️ 爬取結果為空，可能是週末或假日")
            return False
        
        print(f"✅ 成功爬取 {len(df)} 筆資料")
        
        # 檢查資料結構
        df_reset = df.reset_index()
        print(f"📊 資料欄位: {list(df_reset.columns)}")
        
        # 檢查股票資訊欄位
        required_columns = ['stock_name', 'listing_status', 'industry']
        missing_columns = [col for col in required_columns if col not in df_reset.columns]
        
        if missing_columns:
            print(f"❌ 缺少必要欄位: {missing_columns}")
            return False
        
        print(f"✅ 包含所有必要的股票資訊欄位")
        
        # 檢查股票資訊完整性
        for col in required_columns:
            non_empty = df_reset[col].notna() & (df_reset[col] != '')
            complete_rate = non_empty.sum() / len(df_reset) * 100
            print(f"   {col}: {complete_rate:.1f}% 完整")
        
        # 顯示範例資料
        print(f"\n📋 範例資料 (前5筆):")
        sample_data = df_reset[['stock_id'] + required_columns].head(5)
        for _, row in sample_data.iterrows():
            print(f"   {row['stock_id']}: {row['stock_name']} | {row['listing_status']} | {row['industry']}")
        
        # 2. 測試存檔
        print(f"\n💾 步驟 2: 測試存檔到資料庫...")
        
        # 使用測試資料庫檔案
        test_db_file = 'test_price.db'
        
        # 如果測試檔案存在，先刪除
        if os.path.exists(test_db_file):
            os.remove(test_db_file)
        
        # 存檔測試
        success = save_price_to_newprice_database(df, test_db_file)
        
        if not success:
            print("❌ 存檔失敗")
            return False
        
        print(f"✅ 成功存檔到測試資料庫")
        
        # 3. 驗證存檔結果
        print(f"\n🔍 步驟 3: 驗證存檔結果...")
        
        conn = sqlite3.connect(test_db_file)
        cursor = conn.cursor()
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        saved_count = cursor.fetchone()[0]
        print(f"📊 資料庫中的資料筆數: {saved_count}")
        
        # 檢查股票資訊完整性
        cursor.execute("""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        """)
        name_count = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE listing_status IS NOT NULL AND listing_status != ''
        """)
        listing_count = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE industry IS NOT NULL AND industry != ''
        """)
        industry_count = cursor.fetchone()[0]
        
        print(f"📊 資料庫中的股票資訊完整性:")
        print(f"   股票名稱: {name_count}/{saved_count} ({name_count/saved_count*100:.1f}%)")
        print(f"   上市狀態: {listing_count}/{saved_count} ({listing_count/saved_count*100:.1f}%)")
        print(f"   產業資訊: {industry_count}/{saved_count} ({industry_count/saved_count*100:.1f}%)")
        
        # 顯示資料庫中的範例資料
        cursor.execute("""
        SELECT stock_id, stock_name, listing_status, industry 
        FROM stock_daily_data 
        LIMIT 5
        """)
        db_samples = cursor.fetchall()
        
        print(f"\n📋 資料庫中的範例資料:")
        for row in db_samples:
            print(f"   {row[0]}: {row[1]} | {row[2]} | {row[3]}")
        
        conn.close()
        
        # 清理測試檔案
        os.remove(test_db_file)
        
        print(f"\n✅ 完整流程測試成功！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_database_update():
    """測試實際資料庫的更新"""
    
    print("\n" + "=" * 80)
    print("🔄 測試實際資料庫更新")
    print("=" * 80)
    
    try:
        # 檢查 price.db 的當前狀態
        price_db = 'D:/Finlab/history/tables/price.db'
        
        if not os.path.exists(price_db):
            print(f"❌ 找不到 price.db: {price_db}")
            return False
        
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        # 檢查最後日期
        cursor.execute("SELECT MAX(date) FROM stock_daily_data")
        last_date = cursor.fetchone()[0]
        print(f"📅 price.db 最後日期: {last_date}")
        
        # 檢查股票資訊完整性
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        AND listing_status IS NOT NULL AND listing_status != ''
        AND industry IS NOT NULL AND industry != ''
        """)
        complete_stocks = cursor.fetchone()[0]
        
        complete_rate = complete_stocks / total_stocks * 100 if total_stocks > 0 else 0
        
        print(f"📊 price.db 股票資訊完整性:")
        print(f"   總股票數: {total_stocks}")
        print(f"   完整資訊: {complete_stocks} ({complete_rate:.1f}%)")
        
        # 檢查最新日期的資料
        cursor.execute(f"""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE date = '{last_date}'
        """)
        latest_count = cursor.fetchone()[0]
        
        cursor.execute(f"""
        SELECT COUNT(*) FROM stock_daily_data 
        WHERE date = '{last_date}'
        AND stock_name IS NOT NULL AND stock_name != ''
        AND listing_status IS NOT NULL AND listing_status != ''
        AND industry IS NOT NULL AND industry != ''
        """)
        latest_complete = cursor.fetchone()[0]
        
        latest_rate = latest_complete / latest_count * 100 if latest_count > 0 else 0
        
        print(f"📊 最新日期 ({last_date}) 資料完整性:")
        print(f"   總筆數: {latest_count}")
        print(f"   完整資訊: {latest_complete} ({latest_rate:.1f}%)")
        
        conn.close()
        
        print(f"\n✅ price.db 狀態檢查完成")
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🧪 price 爬蟲完整流程測試")
    
    # 測試完整流程
    if test_complete_price_crawler():
        print("\n✅ 爬蟲流程測試通過")
    else:
        print("\n❌ 爬蟲流程測試失敗")
        return
    
    # 測試實際資料庫
    if test_real_database_update():
        print("\n✅ 實際資料庫檢查通過")
    else:
        print("\n❌ 實際資料庫檢查失敗")
        return
    
    print(f"\n🎉 所有測試完成！")
    print(f"\n💡 結論:")
    print(f"   1. ✅ crawl_price 函數能正確爬取並添加股票資訊")
    print(f"   2. ✅ save_price_to_newprice_database 函數能正確存檔")
    print(f"   3. ✅ price.db 已包含完整的股票資訊")
    print(f"   4. ✅ 未來的爬蟲會自動包含股票資訊")

if __name__ == "__main__":
    main()
