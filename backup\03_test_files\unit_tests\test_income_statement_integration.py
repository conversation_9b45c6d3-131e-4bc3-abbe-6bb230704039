#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
綜合損益表功能整合測試
"""

import sys
import os
import sqlite3
import pandas as pd

def test_downloader():
    """測試下載器功能"""
    print("🔍 測試綜合損益表下載器...")
    
    try:
        from mops_income_statement_downloader import MopsIncomeStatementDownloader
        
        downloader = MopsIncomeStatementDownloader()
        print("✅ 下載器初始化成功")
        
        # 測試下載功能
        print("📡 測試下載2024Q3數據...")
        result = downloader.download_batch(2024, 3, 2024, 3)
        print(f"✅ 下載完成，保存 {result} 筆數據")
        
        # 測試統計功能
        stats = downloader.get_statistics()
        if stats:
            print(f"📊 統計資訊:")
            print(f"  總記錄數: {stats['total_count']}")
            print(f"  年份分布: {dict(stats['year_stats'])}")
            print(f"  市場分布: {dict(stats['market_stats'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下載器測試失敗: {e}")
        return False

def test_database():
    """測試資料庫結構"""
    print("\n🗄️ 測試資料庫結構...")
    
    try:
        db_path = "D:/Finlab/history/tables/income_statement.db"
        
        if not os.path.exists(db_path):
            print("⚠️ 資料庫檔案不存在，將自動創建")
            return True
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(income_statement)")
        columns = cursor.fetchall()
        
        print("📋 資料庫欄位:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 檢查數據
        cursor.execute("SELECT COUNT(*) FROM income_statement")
        count = cursor.fetchone()[0]
        print(f"📊 總記錄數: {count}")
        
        if count > 0:
            # 顯示最新數據
            cursor.execute("""
                SELECT stock_id, stock_name, year, quarter, eps, revenue 
                FROM income_statement 
                ORDER BY year DESC, quarter DESC, stock_id 
                LIMIT 5
            """)
            
            recent_data = cursor.fetchall()
            print("📈 最新5筆數據:")
            for row in recent_data:
                print(f"  {row[0]} {row[1]} {row[2]}Q{row[3]} EPS:{row[4]} 營收:{row[5]:,}")
        
        conn.close()
        print("✅ 資料庫測試成功")
        return True
        
    except Exception as e:
        print(f"❌ 資料庫測試失敗: {e}")
        return False

def test_gui_import():
    """測試GUI模組導入"""
    print("\n🖥️ 測試GUI模組...")
    
    try:
        from mops_income_statement_gui import MopsIncomeStatementGUI
        print("✅ GUI模組導入成功")
        
        # 測試創建GUI（不運行）
        # gui = MopsIncomeStatementGUI()
        # print("✅ GUI創建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI模組測試失敗: {e}")
        return False

def test_main_integration():
    """測試主程式整合"""
    print("\n🔗 測試主程式整合...")
    
    try:
        # 檢查主程式是否包含新的選單項目
        with open("O3mh_gui_v21_optimized.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "綜合損益表" in content:
            print("✅ 主程式包含綜合損益表選單")
        else:
            print("❌ 主程式缺少綜合損益表選單")
            return False
        
        if "open_income_statement_downloader" in content:
            print("✅ 主程式包含綜合損益表處理方法")
        else:
            print("❌ 主程式缺少綜合損益表處理方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主程式整合測試失敗: {e}")
        return False

def export_sample_data():
    """匯出樣本數據"""
    print("\n📤 匯出樣本數據...")
    
    try:
        db_path = "D:/Finlab/history/tables/income_statement.db"
        
        if not os.path.exists(db_path):
            print("⚠️ 資料庫檔案不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        
        query = """
            SELECT stock_id, stock_name, industry, year, quarter, market,
                   revenue, operating_income, net_income, eps,
                   operating_margin, net_margin, roe, roa
            FROM income_statement
            ORDER BY year DESC, quarter DESC, stock_id
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if not df.empty:
            # 匯出到Excel
            output_file = "income_statement_sample.xlsx"
            df.to_excel(output_file, index=False, engine='openpyxl')
            print(f"✅ 樣本數據已匯出到: {output_file}")
            print(f"📊 匯出 {len(df)} 筆記錄")
            
            # 顯示統計
            print("\n📈 數據統計:")
            print(f"  年份範圍: {df['year'].min()} - {df['year'].max()}")
            print(f"  市場分布: {df['market'].value_counts().to_dict()}")
            print(f"  平均EPS: {df['eps'].mean():.2f}")
            print(f"  平均營收: {df['revenue'].mean():,.0f}")
            
        else:
            print("⚠️ 沒有數據可以匯出")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 數據匯出失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 綜合損益表功能整合測試")
    print("=" * 50)
    
    tests = [
        ("下載器功能", test_downloader),
        ("資料庫結構", test_database),
        ("GUI模組", test_gui_import),
        ("主程式整合", test_main_integration),
        ("數據匯出", export_sample_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 測試通過")
        else:
            print(f"❌ {test_name} 測試失敗")
    
    print(f"\n{'='*50}")
    print(f"🎯 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！綜合損益表功能已成功整合")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print("\n📋 功能說明:")
    print("• 在主程式選單→爬蟲→綜合損益表 可開啟下載器")
    print("• 支援批次下載上市、上櫃公司綜合損益表數據")
    print("• 包含營收、EPS、財務比率等關鍵指標")
    print("• 數據自動存入SQLite資料庫")
    print("• 支援數據統計和Excel匯出功能")

if __name__ == "__main__":
    main()
