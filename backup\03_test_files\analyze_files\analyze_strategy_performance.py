#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析高殖利率烏龜策略的性能問題
"""

import sys
import time
import pandas as pd
from datetime import datetime
import cProfile
import pstats
from io import StringIO

def analyze_strategy_performance():
    """分析策略性能"""
    
    print("⏱️ 分析高殖利率烏龜策略性能")
    print("=" * 50)
    
    # 1. 載入策略
    print("1️⃣ 載入策略...")
    sys.path.append('strategies')
    
    try:
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        strategy = HighYieldTurtleStrategy()
        print(f"   ✅ 策略載入成功")
        
        if strategy.pe_data_cache is not None:
            print(f"   📊 PE數據筆數: {len(strategy.pe_data_cache):,}")
            print(f"   📈 涵蓋股票數: {strategy.pe_data_cache.index.get_level_values('stock_id').nunique():,} 支")
        else:
            print(f"   ❌ PE數據未載入")
            return
            
    except Exception as e:
        print(f"   ❌ 策略載入失敗: {e}")
        return
    
    # 2. 創建測試數據
    print("\n2️⃣ 創建測試數據...")
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    print(f"   ✅ 測試數據準備完成")
    
    # 3. 性能基準測試
    print("\n3️⃣ 性能基準測試...")
    test_stocks = ['2881', '2882', '1108', '2330', '1101']
    
    # 單次測試
    print("   📊 單次分析測試:")
    for stock_id in test_stocks[:3]:  # 只測試前3個
        start_time = time.time()
        result = strategy.analyze_stock(test_df, stock_id=stock_id)
        end_time = time.time()
        
        elapsed = end_time - start_time
        status = "✅" if result['suitable'] else "❌"
        print(f"      {status} {stock_id}: {elapsed:.3f}秒 (評分{result['score']}/100)")
    
    # 批量測試
    print("\n   📊 批量分析測試:")
    start_time = time.time()
    
    results = []
    for stock_id in test_stocks:
        result = strategy.analyze_stock(test_df, stock_id=stock_id)
        results.append((stock_id, result))
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / len(test_stocks)
    
    print(f"      總時間: {total_time:.3f}秒")
    print(f"      平均時間: {avg_time:.3f}秒/股票")
    print(f"      處理速度: {len(test_stocks)/total_time:.1f}股票/秒")
    
    # 4. 詳細性能分析
    print("\n4️⃣ 詳細性能分析...")
    
    def profile_strategy_analysis():
        """用於性能分析的函數"""
        for stock_id in ['2881', '2882', '1108']:
            strategy.analyze_stock(test_df, stock_id=stock_id)
    
    # 使用cProfile進行詳細分析
    pr = cProfile.Profile()
    pr.enable()
    profile_strategy_analysis()
    pr.disable()
    
    # 分析結果
    s = StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # 顯示前20個最耗時的函數
    
    profile_output = s.getvalue()
    print("   📋 性能分析結果 (前10個最耗時的函數):")
    
    lines = profile_output.split('\n')
    header_found = False
    count = 0
    for line in lines:
        if 'cumulative' in line and 'filename:lineno(function)' in line:
            header_found = True
            print(f"      {line}")
            continue
        if header_found and line.strip() and count < 10:
            print(f"      {line}")
            count += 1
    
    # 5. 識別性能瓶頸
    print("\n5️⃣ 識別性能瓶頸...")
    
    # 測試各個組件的性能
    print("   📊 組件性能測試:")
    
    # 測試PKL數據載入
    start_time = time.time()
    strategy.load_pkl_data()
    pkl_load_time = time.time() - start_time
    print(f"      PKL數據載入: {pkl_load_time:.3f}秒")
    
    # 測試股票查找
    start_time = time.time()
    for stock_id in test_stocks:
        strategy.find_stock_in_pkl(stock_id)
    find_time = time.time() - start_time
    print(f"      股票查找 (5支): {find_time:.3f}秒 (平均{find_time/len(test_stocks):.3f}秒/股票)")
    
    # 測試PKL數據獲取
    start_time = time.time()
    for stock_id in test_stocks:
        strategy.get_stock_pkl_data(stock_id)
    pkl_get_time = time.time() - start_time
    print(f"      PKL數據獲取 (5支): {pkl_get_time:.3f}秒 (平均{pkl_get_time/len(test_stocks):.3f}秒/股票)")
    
    # 測試技術分析
    start_time = time.time()
    for _ in range(5):
        strategy.calculate_technical_score(test_df)
    tech_time = time.time() - start_time
    print(f"      技術分析 (5次): {tech_time:.3f}秒 (平均{tech_time/5:.3f}秒/次)")
    
    # 6. 與其他策略比較
    print("\n6️⃣ 與其他策略比較...")
    
    # 模擬簡單策略
    def simple_strategy_analysis(df, stock_id):
        """模擬簡單策略"""
        if len(df) < 20:
            return False, "數據不足"
        
        # 簡單的移動平均策略
        ma5 = df['Close'].rolling(5).mean().iloc[-1]
        ma20 = df['Close'].rolling(20).mean().iloc[-1]
        current_price = df['Close'].iloc[-1]
        
        if current_price > ma5 > ma20:
            return True, "價格在均線之上"
        else:
            return False, "價格在均線之下"
    
    # 測試簡單策略性能
    start_time = time.time()
    for stock_id in test_stocks:
        simple_strategy_analysis(test_df, stock_id)
    simple_time = time.time() - start_time
    
    print(f"   📊 性能比較:")
    print(f"      高殖利率烏龜策略: {avg_time:.3f}秒/股票")
    print(f"      簡單移動平均策略: {simple_time/len(test_stocks):.3f}秒/股票")
    print(f"      性能差異: {avg_time/(simple_time/len(test_stocks)):.1f}倍")
    
    # 7. 優化建議
    print("\n7️⃣ 優化建議...")
    
    if avg_time > 1.0:
        print("   ⚠️ 策略性能需要優化！")
        print("   💡 優化建議:")
        print("      1. 預處理PE數據，建立股票代碼索引")
        print("      2. 快取常用股票的PE數據")
        print("      3. 使用更高效的數據查找算法")
        print("      4. 減少不必要的數據複製操作")
        print("      5. 優化數值計算邏輯")
    elif avg_time > 0.5:
        print("   ⚠️ 策略性能可以進一步優化")
        print("   💡 優化建議:")
        print("      1. 建立PE數據快取機制")
        print("      2. 優化股票查找邏輯")
    else:
        print("   ✅ 策略性能良好")
    
    print(f"\n" + "=" * 50)
    print(f"✅ 性能分析完成！")
    
    return avg_time

def analyze_pe_data_access_pattern():
    """分析PE數據訪問模式"""
    
    print("\n🔍 分析PE數據訪問模式")
    print("=" * 30)
    
    try:
        sys.path.append('strategies')
        from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategy
        strategy = HighYieldTurtleStrategy()
        
        if strategy.pe_data_cache is None:
            print("❌ PE數據未載入")
            return
        
        pe_data = strategy.pe_data_cache
        
        print(f"📊 PE數據基本信息:")
        print(f"   數據筆數: {len(pe_data):,}")
        print(f"   記憶體使用: {pe_data.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")
        print(f"   索引類型: {type(pe_data.index)}")
        
        # 測試不同查找方式的性能
        test_stocks = ['1101 台泥', '2330 台積電', '2881 富邦金']
        
        print(f"\n📊 查找性能測試:")
        
        # 方法1: 直接索引查找
        start_time = time.time()
        for stock_id in test_stocks:
            if stock_id in pe_data.index.get_level_values('stock_id'):
                data = pe_data.loc[stock_id]
        method1_time = time.time() - start_time
        print(f"   方法1 (直接索引): {method1_time:.3f}秒")
        
        # 方法2: 遍歷查找
        start_time = time.time()
        for target in ['1101', '2330', '2881']:
            for stock_id in pe_data.index.get_level_values('stock_id').unique():
                if stock_id.startswith(target):
                    data = pe_data.loc[stock_id]
                    break
        method2_time = time.time() - start_time
        print(f"   方法2 (遍歷查找): {method2_time:.3f}秒")
        
        print(f"   性能差異: {method2_time/method1_time:.1f}倍")
        
    except Exception as e:
        print(f"❌ PE數據訪問分析失敗: {e}")

if __name__ == "__main__":
    # 分析策略性能
    avg_time = analyze_strategy_performance()
    
    # 分析PE數據訪問模式
    analyze_pe_data_access_pattern()
    
    print(f"\n🎯 總結:")
    if avg_time and avg_time > 1.0:
        print(f"⚠️ 高殖利率烏龜策略的計算時間 ({avg_time:.3f}秒/股票) 確實較長")
        print(f"📋 主要原因:")
        print(f"   1. 需要從4,854,042筆PE數據中查找特定股票")
        print(f"   2. 每次都要遍歷股票代碼進行匹配")
        print(f"   3. 數據結構未針對查找進行優化")
        print(f"💡 這是合理的，但可以通過優化來改善")
    else:
        print(f"✅ 策略性能在可接受範圍內")
