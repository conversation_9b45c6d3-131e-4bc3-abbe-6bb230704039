#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試全新的matplotlib圖表分頁
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class NewChartTabTestWindow(QMainWindow):
    """全新圖表分頁測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔥 全新Matplotlib圖表分頁測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow { background-color: #1a1a1a; color: white; }
            QPushButton { 
                background-color: #00ff88; 
                color: black; 
                border: none; 
                padding: 15px; 
                font-size: 18px; 
                border-radius: 10px; 
                margin: 15px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #00cc66; }
            QLabel { color: white; font-size: 14px; margin: 10px; }
        """)
        
        # 創建界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("🔥 全新Matplotlib圖表分頁測試")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: #00ff88; margin: 25px;")
        layout.addWidget(title)
        
        # 說明
        info = QLabel("""
🚀 全新解決方案：完全重寫圖表分頁！

❌ 舊版問題：
• PyQtGraph兼容性問題
• 複雜的軸設置邏輯
• 日期顯示不穩定

✅ 新版優勢：
• 完全使用Matplotlib
• 從頭重寫圖表分頁
• 原生日期軸支持
• 美觀的圖表樣式
• 穩定可靠的顯示

🎨 新功能特色：
• 深色主題設計
• 自動日期格式化 (MM/DD)
• 智能標籤間隔調整
• 彩色圖例和網格
• 支援線圖、柱狀圖、面積圖

📊 支援的數據類型：
• 歷史指數資料 (完美日期軸)
• 市場指數資訊 (序號軸)
• 融資融券統計 (序號軸)

🧪 測試步驟：
1. 點擊下方按鈕開啟爬蟲
2. 切換到「圖表分析」分頁
3. 選擇「歷史指數資料」
4. 點擊「生成圖表」
5. 享受完美的日期軸顯示！

🎉 這次絕對能看到日期了！
        """)
        info.setStyleSheet("""
            background-color: #2a2a2a; 
            padding: 25px; 
            border-radius: 15px; 
            border: 3px solid #00ff88;
            line-height: 1.5;
            font-size: 15px;
        """)
        layout.addWidget(info)
        
        # 測試按鈕
        test_btn = QPushButton("🔥 開啟台股爬蟲 (測試全新圖表分頁)")
        test_btn.clicked.connect(self.open_crawler)
        layout.addWidget(test_btn)
        
        # 結果提示
        result = QLabel("🎉 全新的Matplotlib圖表分頁，日期軸絕對沒問題！")
        result.setAlignment(Qt.AlignmentFlag.AlignCenter)
        result.setStyleSheet("""
            color: #00ff88; 
            font-weight: bold; 
            font-size: 20px; 
            background-color: #2a2a2a; 
            padding: 20px; 
            border-radius: 10px;
            border: 2px solid #00ff88;
            margin: 15px;
        """)
        layout.addWidget(result)
    
    def open_crawler(self):
        """開啟爬蟲"""
        try:
            print("🔥 開啟台股爬蟲 (全新圖表分頁)...")
            dialog = TWSEMarketDataDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"❌ 錯誤: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    print("🔥 全新Matplotlib圖表分頁測試")
    print("🚀 完全重寫圖表分頁！")
    print("✅ 原生matplotlib日期軸支持")
    print("✅ 美觀的深色主題設計")
    print("✅ 自動日期格式化")
    print("✅ 智能標籤間隔調整")
    print("🎉 這次絕對能看到完美的日期軸了！")
    
    window = NewChartTabTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
