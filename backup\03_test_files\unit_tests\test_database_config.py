#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試數據庫配置功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from dialogs.database_config_dialog import DatabaseConfigDialog

def test_database_config():
    """測試數據庫配置對話框"""
    app = QApplication(sys.argv)
    
    # 模擬當前配置
    current_config = {
        'database': {
            'price_db_path': 'D:/Finlab/history/tables/price.db',
            'pe_db_path': 'D:/Finlab/history/tables/pe_data.db',
            'pe_enabled': True,
            'auto_detect': True
        }
    }
    
    # 創建對話框
    dialog = DatabaseConfigDialog(None, current_config)
    
    # 顯示對話框
    if dialog.exec() == dialog.DialogCode.Accepted:
        print("✅ 配置已保存")
        new_config = dialog.get_config()
        print(f"📊 新配置: {new_config}")
    else:
        print("❌ 配置已取消")
    
    app.quit()

if __name__ == '__main__':
    test_database_config()
