#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
診斷PKL策略問題
"""

import sys
import os
import pandas as pd

# 添加strategies目錄到路徑
sys.path.append('strategies')

def debug_pkl_strategy():
    """診斷PKL策略問題"""
    print("🔍 診斷PKL策略問題")
    print("=" * 60)
    
    # 1. 檢查PKL檔案
    pkl_path = 'D:/Finlab/pe.pkl'
    print(f"📊 檢查PKL檔案: {pkl_path}")
    print(f"📁 檔案存在: {os.path.exists(pkl_path)}")
    
    if os.path.exists(pkl_path):
        try:
            pe_data = pd.read_pickle(pkl_path)
            print(f"✅ PKL載入成功: {pe_data.shape}")
            print(f"📝 欄位: {list(pe_data.columns)}")
            
            # 檢查樣本股票
            sample_stocks = pe_data['股票代號'].head(10).tolist()
            print(f"📊 樣本股票: {sample_stocks}")
            
        except Exception as e:
            print(f"❌ PKL載入失敗: {e}")
    
    # 2. 測試策略初始化
    print(f"\n🔧 測試策略初始化:")
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        strategy = HighYieldTurtleStrategy()
        print(f"✅ 策略初始化成功")
        print(f"📊 PKL數據快取狀態: {strategy.pe_data_cache is not None}")
        
        if strategy.pe_data_cache is not None:
            print(f"📊 快取數據形狀: {strategy.pe_data_cache.shape}")
        else:
            print(f"❌ PKL數據快取為空")
            
    except Exception as e:
        print(f"❌ 策略初始化失敗: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 測試股票代碼獲取
    print(f"\n🔍 測試股票代碼獲取:")
    test_stock = '1101'
    
    try:
        pkl_data = strategy.get_stock_pkl_data(test_stock)
        if pkl_data:
            print(f"✅ {test_stock}: {pkl_data['stock_name']}")
            print(f"    殖利率: {pkl_data['dividend_yield']:.1f}%")
            print(f"    PE: {pkl_data['pe_ratio']:.1f}")
        else:
            print(f"❌ {test_stock}: 無PKL數據")
            
    except Exception as e:
        print(f"❌ 股票數據獲取失敗: {e}")
    
    # 4. 檢查主程序中的股票代碼傳遞
    print(f"\n🔍 檢查主程序股票代碼傳遞:")
    
    # 檢查主程序中的相關代碼
    main_file = "O3mh_gui_v21_optimized.py"
    if os.path.exists(main_file):
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找高殖利率烏龜策略調用
        if 'check_high_yield_turtle_strategy' in content:
            print("✅ 找到策略調用函數")
            
            # 檢查stock_id傳遞
            if 'stock_id=' in content:
                print("✅ 找到stock_id參數傳遞")
            else:
                print("❌ 未找到stock_id參數傳遞")
                
        else:
            print("❌ 未找到策略調用函數")
    
    return True

if __name__ == "__main__":
    debug_pkl_strategy()
