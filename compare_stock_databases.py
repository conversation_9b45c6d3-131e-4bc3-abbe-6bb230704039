#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比較 newprice.db 和 price.db 的股票數差異
"""

import sqlite3
import pandas as pd
from collections import defaultdict

def compare_stock_databases():
    """比較兩個資料庫的股票差異"""
    
    print("=" * 80)
    print("📊 比較 newprice.db 和 price.db 的股票數差異")
    print("=" * 80)
    
    # 資料庫路徑
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    price_db = 'D:/Finlab/history/tables/price.db'
    
    try:
        # 連接 newprice.db
        print("📡 連接 newprice.db...")
        conn_new = sqlite3.connect(newprice_db)
        
        # 獲取 newprice.db 的股票清單
        query_new = '''
            SELECT DISTINCT stock_id 
            FROM stock_daily_data 
            ORDER BY stock_id
        '''
        df_new = pd.read_sql_query(query_new, conn_new)
        newprice_stocks = set(df_new['stock_id'].tolist())
        
        # 獲取最新日期的資料統計
        query_new_latest = '''
            SELECT date, COUNT(DISTINCT stock_id) as stock_count
            FROM stock_daily_data 
            GROUP BY date 
            ORDER BY date DESC 
            LIMIT 10
        '''
        df_new_latest = pd.read_sql_query(query_new_latest, conn_new)
        
        conn_new.close()
        print(f"✅ newprice.db: {len(newprice_stocks)} 檔股票")
        
        # 連接 price.db
        print("📡 連接 price.db...")
        conn_old = sqlite3.connect(price_db)
        
        # 獲取 price.db 的股票清單
        query_old = '''
            SELECT DISTINCT stock_id 
            FROM stock_daily_data 
            ORDER BY stock_id
        '''
        df_old = pd.read_sql_query(query_old, conn_old)
        price_stocks = set(df_old['stock_id'].tolist())
        
        # 獲取最新日期的資料統計
        query_old_latest = '''
            SELECT date, COUNT(DISTINCT stock_id) as stock_count
            FROM stock_daily_data 
            GROUP BY date 
            ORDER BY date DESC 
            LIMIT 10
        '''
        df_old_latest = pd.read_sql_query(query_old_latest, conn_old)
        
        conn_old.close()
        print(f"✅ price.db: {len(price_stocks)} 檔股票")
        
        # 比較分析
        print(f"\n📊 基本統計比較:")
        print(f"  newprice.db 股票數: {len(newprice_stocks):,}")
        print(f"  price.db 股票數: {len(price_stocks):,}")
        print(f"  差異: {len(newprice_stocks) - len(price_stocks):+,}")
        
        # 找出差異股票
        only_in_newprice = newprice_stocks - price_stocks
        only_in_price = price_stocks - newprice_stocks
        common_stocks = newprice_stocks & price_stocks
        
        print(f"\n📋 股票分布分析:")
        print(f"  共同股票: {len(common_stocks):,} 檔")
        print(f"  僅在 newprice.db: {len(only_in_newprice):,} 檔")
        print(f"  僅在 price.db: {len(only_in_price):,} 檔")
        
        # 分析股票族群（按代碼開頭分類）
        def analyze_stock_groups(stocks, db_name):
            print(f"\n📊 {db_name} 股票族群分析:")
            groups = defaultdict(list)
            
            for stock in stocks:
                if stock.startswith('00'):
                    if len(stock) == 4:
                        groups['ETF (00xx)'].append(stock)
                    else:
                        groups['ETF (其他)'].append(stock)
                elif stock.startswith('0'):
                    groups['ETF/基金 (0x)'].append(stock)
                elif stock.startswith('1'):
                    groups['傳統產業 (1xxx)'].append(stock)
                elif stock.startswith('2'):
                    groups['電子股 (2xxx)'].append(stock)
                elif stock.startswith('3'):
                    groups['電子股 (3xxx)'].append(stock)
                elif stock.startswith('4'):
                    groups['傳統產業 (4xxx)'].append(stock)
                elif stock.startswith('5'):
                    groups['電子股 (5xxx)'].append(stock)
                elif stock.startswith('6'):
                    groups['電子股 (6xxx)'].append(stock)
                elif stock.startswith('7'):
                    groups['權證 (7xxx)'].append(stock)
                elif stock.startswith('8'):
                    groups['電子股 (8xxx)'].append(stock)
                elif stock.startswith('9'):
                    groups['其他 (9xxx)'].append(stock)
                else:
                    groups['其他'].append(stock)
            
            for group_name, stock_list in sorted(groups.items()):
                print(f"    {group_name}: {len(stock_list):,} 檔")
                if len(stock_list) <= 10:
                    print(f"      範例: {', '.join(sorted(stock_list))}")
                else:
                    print(f"      範例: {', '.join(sorted(stock_list)[:10])}...")
            
            return groups
        
        # 分析各資料庫的股票族群
        newprice_groups = analyze_stock_groups(newprice_stocks, "newprice.db")
        price_groups = analyze_stock_groups(price_stocks, "price.db")
        
        # 分析差異股票的族群
        if only_in_newprice:
            print(f"\n🔍 僅在 newprice.db 的股票族群分析:")
            newprice_only_groups = analyze_stock_groups(only_in_newprice, "newprice.db 獨有")
        
        if only_in_price:
            print(f"\n🔍 僅在 price.db 的股票族群分析:")
            price_only_groups = analyze_stock_groups(only_in_price, "price.db 獨有")
        
        # 最新日期資料比較
        print(f"\n📅 最新日期資料比較:")
        print(f"\n  newprice.db 最近10天:")
        for _, row in df_new_latest.iterrows():
            print(f"    {row['date']}: {row['stock_count']:,} 檔股票")
        
        print(f"\n  price.db 最近10天:")
        for _, row in df_old_latest.iterrows():
            print(f"    {row['date']}: {row['stock_count']:,} 檔股票")
        
        # 重要股票檢查
        print(f"\n🔍 重要股票存在性檢查:")
        important_stocks = ['2330', '2317', '2454', '0050', '0056', '1101', '1216']
        
        for stock in important_stocks:
            in_new = stock in newprice_stocks
            in_old = stock in price_stocks
            status = "✅ 都有" if in_new and in_old else "⚠️ 缺少" if not in_new or not in_old else "❌ 都無"
            print(f"    {stock}: newprice({in_new}) price({in_old}) {status}")
        
        # 總結建議
        print(f"\n📋 總結與建議:")
        
        if len(only_in_newprice) > len(only_in_price):
            print(f"  ✅ newprice.db 包含更多股票 (+{len(only_in_newprice) - len(only_in_price)})")
        elif len(only_in_price) > len(only_in_newprice):
            print(f"  ⚠️ price.db 包含更多股票 (+{len(only_in_price) - len(only_in_newprice)})")
        else:
            print(f"  📊 兩個資料庫股票數量相當")
        
        # 檢查是否有明顯缺失的族群
        if 'ETF (00xx)' in newprice_groups and 'ETF (00xx)' in price_groups:
            etf_diff = len(newprice_groups['ETF (00xx)']) - len(price_groups['ETF (00xx)'])
            if abs(etf_diff) > 10:
                print(f"  📊 ETF 數量差異較大: {etf_diff:+}")
        
        if '權證 (7xxx)' in newprice_groups and '權證 (7xxx)' in price_groups:
            warrant_diff = len(newprice_groups['權證 (7xxx)']) - len(price_groups['權證 (7xxx)'])
            if abs(warrant_diff) > 100:
                print(f"  📊 權證數量差異較大: {warrant_diff:+}")
        
        print(f"\n✅ 比較分析完成！")
        
    except Exception as e:
        print(f"❌ 比較失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    compare_stock_databases()
