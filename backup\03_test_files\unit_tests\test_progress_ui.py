#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試進度條和界面更新功能
"""

import sys
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QProgressBar
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TestProgressWindow(QMainWindow):
    """測試進度條窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("進度條和界面更新測試")
        self.setGeometry(100, 100, 600, 400)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建界面組件
        self.market_sentiment_label = QLabel("📈 市場情緒：等待掃描...")
        self.us_status_label = QLabel("🇺🇸 美股：等待數據...")
        self.tw_status_label = QLabel("🇹🇼 台指期：等待數據...")
        self.premarket_status_label = QLabel("📊 開盤前監控：準備中...")
        
        # 進度條
        self.scan_progress_bar = QProgressBar()
        self.scan_progress_bar.setVisible(False)
        self.scan_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 4px;
                text-align: center;
                font-size: 10px;
                font-weight: bold;
                background-color: #f0f0f0;
                height: 20px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4caf50, stop:1 #81c784);
                border-radius: 3px;
            }
        """)
        
        # 測試按鈕
        self.test_btn = QPushButton("🔍 開始測試掃描")
        self.test_btn.clicked.connect(self.test_scan_with_progress)
        
        # 添加到布局
        layout.addWidget(QLabel("=== 進度條和界面更新測試 ==="))
        layout.addWidget(self.market_sentiment_label)
        layout.addWidget(self.us_status_label)
        layout.addWidget(self.tw_status_label)
        layout.addWidget(self.premarket_status_label)
        layout.addWidget(self.scan_progress_bar)
        layout.addWidget(self.test_btn)
        
        # 初始化監控器
        self.init_monitor()
        
        # 掃描狀態
        self._is_scanning = False
        
    def init_monitor(self):
        """初始化監控器"""
        try:
            from monitoring.pre_market_monitor import PreMarketMonitor
            self.pre_market_monitor = PreMarketMonitor()
            print("✅ 監控器初始化成功")
        except Exception as e:
            print(f"❌ 監控器初始化失敗: {e}")
            self.pre_market_monitor = None
    
    def test_scan_with_progress(self):
        """測試帶進度條的掃描"""
        if self._is_scanning:
            print("⚠️ 掃描已在進行中")
            return
            
        if not self.pre_market_monitor:
            print("❌ 監控器未初始化")
            return
        
        print("🚀 開始測試帶進度條的掃描...")
        self._is_scanning = True
        self.test_btn.setText("🔍 掃描中...")
        self.test_btn.setEnabled(False)
        
        # 顯示進度條
        self.scan_progress_bar.setVisible(True)
        self.scan_progress_bar.setRange(0, 100)
        self.scan_progress_bar.setValue(0)
        self.scan_progress_bar.setFormat("正在初始化掃描...")
        
        # 使用定時器模擬異步掃描
        QTimer.singleShot(100, self.run_scan_with_progress)
    
    def run_scan_with_progress(self):
        """運行帶進度的掃描"""
        try:
            # 導入 ScanWorker
            from O3mh_gui_v21_optimized import ScanWorker
            
            print("📊 創建 ScanWorker...")
            
            # 創建 ScanWorker
            scan_worker = ScanWorker(
                self.pre_market_monitor,
                callback_success=self._on_scan_success,
                callback_error=self._on_scan_error,
                progress_callback=self._on_scan_progress
            )
            
            print("🚀 啟動掃描...")
            scan_worker.start_scan()
            
        except Exception as e:
            print(f"❌ 掃描啟動失敗: {e}")
            self._on_scan_error(str(e))
    
    def _on_scan_progress(self, value, message=""):
        """掃描進度回調"""
        try:
            self.scan_progress_bar.setValue(value)
            if message:
                self.scan_progress_bar.setFormat(f"{message} ({value}%)")
            else:
                self.scan_progress_bar.setFormat(f"掃描進度: {value}%")
            
            # 強制更新UI
            QApplication.processEvents()
            
            print(f"🔍 PROGRESS: {value}% - {message}")
        except Exception as e:
            print(f"❌ 進度更新失敗: {e}")
    
    def _on_scan_success(self, results):
        """掃描成功回調"""
        try:
            print(f"🎉 掃描成功！結果類型: {type(results)}")
            
            # 重置狀態
            self._is_scanning = False
            self.test_btn.setText("🔍 開始測試掃描")
            self.test_btn.setEnabled(True)
            
            # 隱藏進度條
            self.scan_progress_bar.setVisible(False)
            
            if results:
                print(f"📊 結果鍵: {list(results.keys()) if isinstance(results, dict) else 'Not dict'}")
                
                # 更新市場情緒
                try:
                    sentiment = self.pre_market_monitor.get_market_sentiment()
                    self.market_sentiment_label.setText(f"📈 市場情緒：{sentiment}")
                    print(f"✅ 更新市場情緒: {sentiment}")
                except Exception as e:
                    print(f"❌ 市場情緒更新失敗: {e}")
                
                # 更新美股狀態
                us_data = results.get('us_indices', {})
                if us_data:
                    us_changes = [data.get('change_pct', 0) for data in us_data.values() 
                                if isinstance(data, dict) and 'change_pct' in data]
                    if us_changes:
                        avg_change = sum(us_changes) / len(us_changes)
                        trend = "📈" if avg_change > 0 else "📉" if avg_change < 0 else "➡️"
                        self.us_status_label.setText(f"🇺🇸 美股：{trend} {avg_change:+.2f}%")
                        print(f"✅ 更新美股狀態: {trend} {avg_change:+.2f}%")
                
                # 更新台指期狀態
                tw_data = results.get('taiwan_futures', {})
                if tw_data:
                    tw_index = tw_data.get('台股加權指數', {})
                    if tw_index and isinstance(tw_index, dict):
                        price = tw_index.get('price', 0)
                        change_pct = tw_index.get('change_pct', 0)
                        trend = "📈" if change_pct > 0 else "📉" if change_pct < 0 else "➡️"
                        self.tw_status_label.setText(f"🇹🇼 台指期：{trend} {price} ({change_pct:+.2f}%)")
                        print(f"✅ 更新台指期狀態: {trend} {price} ({change_pct:+.2f}%)")
                
                # 更新總體狀態
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                timestamp = results.get('timestamp', '未知時間')
                self.premarket_status_label.setText(f"✅ 掃描完成 ({timestamp}) - {total_items} 項數據")
                print(f"✅ 更新總體狀態: {total_items} 項數據")
                
                print("🎉 界面更新完成！")
            else:
                print("❌ 沒有掃描結果")
                self.premarket_status_label.setText("❌ 掃描失敗，沒有結果")
                
        except Exception as e:
            print(f"❌ 成功回調處理失敗: {e}")
    
    def _on_scan_error(self, error_message):
        """掃描失敗回調"""
        print(f"❌ 掃描失敗: {error_message}")
        
        # 重置狀態
        self._is_scanning = False
        self.test_btn.setText("🔍 開始測試掃描")
        self.test_btn.setEnabled(True)
        
        # 隱藏進度條
        self.scan_progress_bar.setVisible(False)
        
        # 顯示錯誤
        self.premarket_status_label.setText(f"❌ 掃描失敗: {error_message}")

def main():
    """主函數"""
    print("進度條和界面更新測試")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建測試窗口
    window = TestProgressWindow()
    window.show()
    
    print("🖥️ 測試窗口已顯示")
    print("💡 點擊「開始測試掃描」按鈕來測試進度條和界面更新")
    print("📋 觀察控制台輸出和界面變化")
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
