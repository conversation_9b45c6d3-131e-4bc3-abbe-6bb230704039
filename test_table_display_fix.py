#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試表格顯示修復和數據量問題
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout
)
from PyQt6.QtCore import Qt
import pandas as pd

class TestTableWindow(QMainWindow):
    """測試表格顯示的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 表格顯示測試 - 深色主題")
        self.setGeometry(100, 100, 1000, 600)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        # 測試按鈕
        test_light_btn = QPushButton("🔆 測試淺色表格")
        test_light_btn.clicked.connect(self.test_light_table)
        button_layout.addWidget(test_light_btn)
        
        test_dark_btn = QPushButton("🌙 測試深色表格")
        test_dark_btn.clicked.connect(self.test_dark_table)
        button_layout.addWidget(test_dark_btn)
        
        test_data_btn = QPushButton("📊 測試實際數據")
        test_data_btn.clicked.connect(self.test_real_data)
        button_layout.addWidget(test_data_btn)
        
        layout.addLayout(button_layout)
        
        # 創建表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        layout.addWidget(self.table)
        
        # 預設顯示深色表格
        self.test_dark_table()
    
    def test_light_table(self):
        """測試淺色表格（原來的問題樣式）"""
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                alternate-background-color: #f5f5f5;
                gridline-color: #d0d0d0;
                selection-background-color: #3daee9;
                selection-color: white;
            }
            QHeaderView::section {
                background-color: #e0e0e0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
        self.populate_test_data("淺色主題 - 可能有白底白字問題")
    
    def test_dark_table(self):
        """測試深色表格（修復後的樣式）"""
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                alternate-background-color: #3c3c3c;
                gridline-color: #555555;
                selection-background-color: #0078d4;
                selection-color: #ffffff;
                border: 1px solid #555555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #555555;
                color: #ffffff;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background-color: #505050;
            }
        """)
        
        self.populate_test_data("深色主題 - 修復後的樣式")
    
    def test_real_data(self):
        """測試實際的台灣證交所數據格式"""
        # 模擬實際的市場指數資料
        data = [
            {
                '日期': '1140725',
                '指數': '特選臺灣中小型精選動能100報酬指數',
                '收盤指數': '4597.12',
                '漲跌': '-',
                '漲跌點數': '1.09',
                '漲跌百分比': '-0.02%',
                '特殊處理註記': '',
                'crawl_time': '2025-07-27 14:30:00'
            },
            {
                '日期': '1140725',
                '指數': '台灣50指數',
                '收盤指數': '16234.56',
                '漲跌': '+',
                '漲跌點數': '23.45',
                '漲跌百分比': '+0.14%',
                '特殊處理註記': '',
                'crawl_time': '2025-07-27 14:30:00'
            },
            {
                '日期': '1140725',
                '指數': '發行量加權股價指數',
                '收盤指數': '22456.78',
                '漲跌': '+',
                '漲跌點數': '156.23',
                '漲跌百分比': '+0.70%',
                '特殊處理註記': '',
                'crawl_time': '2025-07-27 14:30:00'
            }
        ]
        
        # 設置表格
        df = pd.DataFrame(data)
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns))
        self.table.setHorizontalHeaderLabels(df.columns.tolist())
        
        # 填充數據
        for row in range(len(df)):
            for col in range(len(df.columns)):
                item = QTableWidgetItem(str(df.iloc[row, col]))
                self.table.setItem(row, col, item)
        
        # 調整列寬
        self.table.resizeColumnsToContents()
        
        print("✅ 已載入模擬的台灣證交所市場指數數據")
        print(f"📊 數據筆數: {len(df)}")
        print(f"📋 欄位: {list(df.columns)}")
    
    def populate_test_data(self, theme_name):
        """填充測試數據"""
        # 測試數據
        data = [
            ["項目1", "數值A", "123.45", "正常"],
            ["項目2", "數值B", "678.90", "異常"],
            ["項目3", "數值C", "234.56", "正常"],
            ["項目4", "數值D", "789.01", "警告"],
            ["項目5", "數值E", "345.67", "正常"]
        ]
        
        headers = ["項目", "類型", "數值", "狀態"]
        
        # 設置表格
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # 填充數據
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                self.table.setItem(row, col, item)
        
        # 調整列寬
        self.table.resizeColumnsToContents()
        
        print(f"✅ 已套用 {theme_name}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = TestTableWindow()
    window.show()
    
    print("🚀 表格顯示測試程式已啟動")
    print("📋 測試項目：")
    print("1. 點擊「測試淺色表格」- 查看原來可能有問題的樣式")
    print("2. 點擊「測試深色表格」- 查看修復後的樣式")
    print("3. 點擊「測試實際數據」- 查看台灣證交所數據格式")
    print("🎯 重點檢查：文字是否清楚可見，沒有白底白字問題")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
