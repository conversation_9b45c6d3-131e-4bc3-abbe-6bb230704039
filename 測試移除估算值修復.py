#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試移除估算值修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_remove_estimated_values():
    """測試移除估算值修復"""
    print("🧪 測試移除估算值修復...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QGroupBox
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 創建測試對話框
        dialog = QDialog()
        dialog.setWindowTitle("測試移除估算值修復")
        dialog.setFixedSize(900, 800)
        
        layout = QVBoxLayout(dialog)
        
        # 測試修復前後對比
        comparison_group = QGroupBox("🚫 移除估算值修復對比")
        comparison_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        comparison_layout = QVBoxLayout(comparison_group)

        comparison_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; text-align: center;">🚫 移除誤導性估算值</h3>
            
            <div style="display: flex; margin: 15px 0;">
                <div style="flex: 1; margin-right: 10px;">
                    <div style="background-color: #ffebee; border: 2px solid #f44336; border-radius: 8px; padding: 12px;">
                        <h4 style="color: #d32f2f; margin: 0 0 10px 0; text-align: center;">❌ 修復前 (使用估算值)</h4>
                        <div style="background-color: #ffcdd2; padding: 8px; border-radius: 4px; margin: 8px 0;">
                            <p style="margin: 0; font-size: 11px; color: #b71c1c;">
                                <strong>函數：</strong><code>get_estimated_financial_info()</code><br>
                                <strong>問題：</strong>使用hardcode的估算值
                            </p>
                        </div>
                        <table style="width: 100%; font-family: 'Microsoft JhengHei'; font-size: 10px;">
                            <tr><td style="padding: 3px; font-weight: bold;">台積電(2330)：</td>
                                <td style="padding: 3px; color: #d32f2f;">EPS: 36.7元 (估算)</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">鴻海(2317)：</td>
                                <td style="padding: 3px; color: #d32f2f;">EPS: 8.4元 (估算)</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">緯穎(6669)：</td>
                                <td style="padding: 3px; color: #d32f2f;">EPS: 126.57元 (估算)</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">其他股票：</td>
                                <td style="padding: 3px; color: #d32f2f;">EPS: 2.5元 (預設)</td></tr>
                        </table>
                        <p style="margin: 8px 0 0 0; font-size: 10px; color: #d32f2f; text-align: center;">
                            <strong>⚠️ 問題：</strong>誤導投資決策的假數據
                        </p>
                    </div>
                </div>
                
                <div style="flex: 1; margin-left: 10px;">
                    <div style="background-color: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 12px;">
                        <h4 style="color: #2e7d32; margin: 0 0 10px 0; text-align: center;">✅ 修復後 (真實數據)</h4>
                        <div style="background-color: #c8e6c9; padding: 8px; border-radius: 4px; margin: 8px 0;">
                            <p style="margin: 0; font-size: 11px; color: #1b5e20;">
                                <strong>函數：</strong><code>get_real_financial_info()</code><br>
                                <strong>改進：</strong>從真實資料庫獲取數據
                            </p>
                        </div>
                        <table style="width: 100%; font-family: 'Microsoft JhengHei'; font-size: 10px;">
                            <tr><td style="padding: 3px; font-weight: bold;">台積電(2330)：</td>
                                <td style="padding: 3px; color: #2e7d32;">從財務DB獲取</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">鴻海(2317)：</td>
                                <td style="padding: 3px; color: #2e7d32;">從財務DB獲取</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">緯穎(6669)：</td>
                                <td style="padding: 3px; color: #2e7d32;">從財務DB獲取</td></tr>
                            <tr><td style="padding: 3px; font-weight: bold;">無數據股票：</td>
                                <td style="padding: 3px; color: #2e7d32;">顯示 N/A</td></tr>
                        </table>
                        <p style="margin: 8px 0 0 0; font-size: 10px; color: #2e7d32; text-align: center;">
                            <strong>✅ 改進：</strong>誠實顯示真實數據或N/A
                        </p>
                    </div>
                </div>
            </div>
            
            <div style="margin: 15px 0; padding: 12px; background-color: #fff3e0; border-radius: 6px; border-left: 4px solid #ff9800;">
                <h4 style="color: #e65100; margin: 0 0 8px 0;">⚠️ 為什麼要移除估算值？</h4>
                <ul style="margin: 0; padding-left: 20px; color: #bf360c; font-size: 11px;">
                    <li><strong>誤導投資決策：</strong>假數據會讓投資人做出錯誤判斷</li>
                    <li><strong>缺乏時效性：</strong>hardcode的數值無法反映最新財務狀況</li>
                    <li><strong>不同股票差異：</strong>每家公司財務狀況差異極大，不能用統一估算</li>
                    <li><strong>法規風險：</strong>提供不實財務資訊可能涉及法律問題</li>
                    <li><strong>專業原則：</strong>投資分析應基於真實數據，不是猜測</li>
                </ul>
            </div>
        </div>
        """

        comparison_label = QLabel()
        comparison_label.setTextFormat(Qt.TextFormat.RichText)
        comparison_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        comparison_label.setWordWrap(True)
        comparison_label.setText(comparison_text)
        comparison_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        comparison_layout.addWidget(comparison_label)
        layout.addWidget(comparison_group)
        
        # 添加技術實現說明
        technical_group = QGroupBox("🔧 技術實現說明")
        technical_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                background-color: #f9f9f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
                background-color: #f9f9f9;
            }
        """)
        technical_layout = QVBoxLayout(technical_group)

        technical_text = """
        <div style="background-color: #ffffff; color: #333333; padding: 12px;">
            <h4 style="color: #2c3e50; margin: 0 0 10px 0;">修復實現：</h4>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #6c757d;">
                <p style="margin: 0; font-size: 11px; color: #495057;">
                    <strong>1. 函數重命名：</strong><br>
                    <code>get_estimated_financial_info()</code> → <code>get_real_financial_info()</code>
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 4px; border-left: 4px solid #2196f3;">
                <p style="margin: 0; font-size: 11px; color: #0d47a1;">
                    <strong>2. 數據來源改變：</strong><br>
                    • 從 <code>financial_statements.db</code> 獲取真實財務數據<br>
                    • 從 <code>price.db</code> 獲取股價相關數據<br>
                    • 計算真實的 EPS、ROE、ROA 等指標
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #fff3e0; border-radius: 4px; border-left: 4px solid #ff9800;">
                <p style="margin: 0; font-size: 11px; color: #e65100;">
                    <strong>3. 無數據處理：</strong><br>
                    • 如果無法獲取真實數據，顯示 "N/A"<br>
                    • 不再提供誤導性的預設值<br>
                    • 讓用戶知道數據不可用，而非假數據
                </p>
            </div>
            
            <div style="margin: 10px 0; padding: 10px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #4caf50;">
                <p style="margin: 0; font-size: 11px; color: #2e7d32;">
                    <strong>4. 調用點更新：</strong><br>
                    • 第4865行：月營收股票資料獲取<br>
                    • 第4973行：資料庫查詢結果補充<br>
                    • 第5138行：基本財務資訊獲取
                </p>
            </div>
        </div>
        """

        technical_label = QLabel()
        technical_label.setTextFormat(Qt.TextFormat.RichText)
        technical_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        technical_label.setWordWrap(True)
        technical_label.setText(technical_text)
        technical_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                color: #333333;
            }
        """)
        technical_layout.addWidget(technical_label)
        layout.addWidget(technical_group)
        
        # 添加測試確認
        test_info_label = QLabel()
        test_info_label.setText("""
        <div style="padding: 10px; background-color: #e8f5e8; border-radius: 4px; border: 1px solid #c3e6cb;">
            <p style="margin: 0; font-size: 11px; color: #155724;">
                <strong>🧪 修復確認項目：</strong><br>
                ✅ 移除所有hardcode的估算財務數據<br>
                ✅ 改為從真實資料庫獲取數據<br>
                ✅ 無數據時誠實顯示"N/A"而非假數據<br>
                ✅ 更新所有調用點使用新函數<br>
                ✅ 避免誤導投資決策的風險
            </p>
        </div>
        """)
        test_info_label.setTextFormat(Qt.TextFormat.RichText)
        test_info_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        test_info_label.setWordWrap(True)
        layout.addWidget(test_info_label)
        
        print("✅ 對話框創建成功")
        print("📋 修復內容：")
        print("  1. 移除所有估算值和模擬值")
        print("  2. 改為從真實資料庫獲取數據")
        print("  3. 無數據時顯示N/A")
        print("  4. 避免誤導投資決策")
        
        # 顯示對話框
        dialog.show()
        
        # 運行應用程式
        return app.exec()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函數"""
    print("🔍 移除估算值修復測試")
    print("=" * 50)
    
    result = test_remove_estimated_values()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("🎉 測試完成")
        print("💡 估算值已成功移除，改為使用真實數據")
    else:
        print("❌ 測試失敗")
        print("💡 請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    main()
