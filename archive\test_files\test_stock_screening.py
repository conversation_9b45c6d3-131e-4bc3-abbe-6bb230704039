#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試選股功能的獨立腳本
"""

import sqlite3
import pandas as pd
import sys
import os

def test_database_connection():
    """測試資料庫連接"""
    print("=== 測試資料庫連接 ===")
    
    db_path = "D:/Finlab/history/tables/price.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ 找到表格: {[t[0] for t in tables]}")
        
        # 檢查記錄數
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        total_records = cursor.fetchone()[0]
        print(f"✅ 總記錄數: {total_records:,}")
        
        # 檢查股票數
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        stock_count = cursor.fetchone()[0]
        print(f"✅ 股票數量: {stock_count}")
        
        # 檢查最新日期
        cursor.execute("SELECT MAX(date) FROM stock_daily_data")
        latest_date = cursor.fetchone()[0]
        print(f"✅ 最新日期: {latest_date}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return False

def test_stock_data_query():
    """測試股票數據查詢"""
    print("\n=== 測試股票數據查詢 ===")
    
    db_path = "D:/Finlab/history/tables/price.db"
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 查詢台積電數據
        query = """
            SELECT date, Open, High, Low, Close, Volume
            FROM stock_daily_data 
            WHERE stock_id = '2330'
            ORDER BY date DESC 
            LIMIT 10
        """
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            print(f"✅ 成功查詢台積電最近10天數據")
            print(df.head())
        else:
            print("❌ 未找到台積電數據")
            
        conn.close()
        return not df.empty
        
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")
        return False

def test_technical_indicators():
    """測試技術指標計算"""
    print("\n=== 測試技術指標計算 ===")
    
    try:
        # 創建模擬數據
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        prices = [100 + i + (i % 10) for i in range(50)]  # 模擬價格數據
        
        df = pd.DataFrame({
            'date': dates,
            'Close': prices,
            'Volume': [10000] * 50
        })
        
        # 計算移動平均
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        
        # 計算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        print("✅ 技術指標計算成功")
        print(f"最新MA5: {df['MA5'].iloc[-1]:.2f}")
        print(f"最新MA20: {df['MA20'].iloc[-1]:.2f}")
        print(f"最新RSI: {df['RSI'].iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 技術指標計算失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試選股系統功能")
    print("=" * 50)
    
    # 測試項目
    tests = [
        ("資料庫連接", test_database_connection),
        ("股票數據查詢", test_stock_data_query),
        ("技術指標計算", test_technical_indicators),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n>>> 測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 測試 {test_name} 發生異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 50)
    print("🏁 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{len(tests)} 個測試通過")
    
    if passed == len(tests):
        print("🎉 所有測試通過！選股系統應該可以正常運作")
    else:
        print("⚠️  部分測試失敗，請檢查相關問題")
    
    print("\n💡 提示: 如果所有測試都通過，請在GUI中點擊「執行選股」按鈕來進行實際選股")

if __name__ == "__main__":
    main() 