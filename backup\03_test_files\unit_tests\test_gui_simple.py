#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版GUI測試 - 只測試智能掃描功能
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import Qt

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleGUI(QMainWindow):
    """簡化版GUI - 只測試智能掃描"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能掃描功能測試")
        self.setGeometry(300, 300, 800, 600)
        
        # 初始化掃描器
        self.init_scanner()
        
        # 設置UI
        self.setup_ui()
        
    def init_scanner(self):
        """初始化掃描器"""
        try:
            from real_data_sources import RealDataSources
            from O3mh_gui_v21_optimized import MarketScanWorker
            
            self.pre_market_monitor = RealDataSources()
            self.market_scan_worker = MarketScanWorker(self.pre_market_monitor)
            
            logging.info("✅ 掃描器初始化成功")
            
        except Exception as e:
            logging.error(f"❌ 掃描器初始化失敗: {e}")
            self.pre_market_monitor = None
            self.market_scan_worker = None
    
    def setup_ui(self):
        """設置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("🔍 智能掃描功能測試")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)
        
        # 狀態標籤
        self.status_label = QLabel("準備就緒")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; margin: 10px;")
        layout.addWidget(self.status_label)
        
        # 智能掃描按鈕
        self.scan_btn = QPushButton("🔍 智能掃描\n市場情報")
        self.scan_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                margin: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66bb6a, stop:1 #4caf50);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #388e3c, stop:1 #2e7d32);
            }
            QPushButton:disabled {
                background: #cccccc;
                color: #666666;
            }
        """)
        self.scan_btn.clicked.connect(self.run_smart_scan)
        layout.addWidget(self.scan_btn)
        
        # 結果顯示區域
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("background-color: #f5f5f5; font-family: 'Consolas'; font-size: 12px;")
        layout.addWidget(self.result_text)
        
        # 連接信號（如果掃描器可用）
        if self.market_scan_worker:
            self.market_scan_worker.scan_started.connect(self.on_scan_started)
            self.market_scan_worker.scan_progress.connect(self.on_scan_progress)
            self.market_scan_worker.scan_completed.connect(self.on_scan_completed)
            self.market_scan_worker.scan_error.connect(self.on_scan_error)
            
            self.status_label.setText("✅ 智能掃描已就緒")
        else:
            self.status_label.setText("❌ 掃描器初始化失敗")
            self.scan_btn.setEnabled(False)
    
    def run_smart_scan(self):
        """執行智能掃描"""
        if not self.market_scan_worker:
            self.result_text.append("❌ 掃描器未初始化")
            return
        
        try:
            self.market_scan_worker.start_scan()
        except Exception as e:
            self.result_text.append(f"❌ 掃描啟動失敗: {e}")
    
    def on_scan_started(self):
        """掃描開始"""
        self.scan_btn.setEnabled(False)
        self.scan_btn.setText("🔍 掃描中...")
        self.status_label.setText("🔍 正在執行智能掃描...")
        self.result_text.append("🚀 開始智能掃描...")
    
    def on_scan_progress(self, message):
        """掃描進度"""
        self.status_label.setText(message)
        self.result_text.append(f"📊 {message}")
        QApplication.processEvents()
    
    def on_scan_completed(self, results):
        """掃描完成"""
        self.scan_btn.setEnabled(True)
        self.scan_btn.setText("🔍 智能掃描\n市場情報")
        
        if results:
            self.status_label.setText("✅ 智能掃描完成")
            self.result_text.append("✅ 掃描完成！")
            self.result_text.append("=" * 50)
            
            # 顯示結果統計
            self.result_text.append(f"📅 時間戳: {results.get('timestamp', 'N/A')}")
            
            categories = ['us_indices', 'commodities', 'fx_rates', 'crypto', 'taiwan_futures']
            total_items = 0
            
            for category in categories:
                if category in results and results[category]:
                    count = len(results[category])
                    total_items += count
                    self.result_text.append(f"📊 {category}: {count} 項")
                    
                    # 顯示部分數據
                    if count > 0:
                        sample_data = list(results[category].items())[:2]
                        for key, data in sample_data:
                            if isinstance(data, dict):
                                price = data.get('price', 'N/A')
                                change_pct = data.get('change_pct', 'N/A')
                                status = data.get('status', 'N/A')
                                self.result_text.append(f"  • {key}: {price} ({change_pct}%) [{status}]")
            
            self.result_text.append(f"📈 總計: {total_items} 項數據")
            self.result_text.append(f"🔗 數據源: {', '.join(results.get('data_sources_used', []))}")
            
            # 測試市場情緒
            if hasattr(self.pre_market_monitor, 'get_market_sentiment'):
                sentiment = self.pre_market_monitor.get_market_sentiment()
                self.result_text.append(f"🧠 市場情緒: {sentiment}")
            
        else:
            self.status_label.setText("❌ 掃描失敗")
            self.result_text.append("❌ 掃描失敗 - 未獲取到數據")
    
    def on_scan_error(self, error_message):
        """掃描錯誤"""
        self.scan_btn.setEnabled(True)
        self.scan_btn.setText("🔍 智能掃描\n市場情報")
        
        self.status_label.setText("❌ 掃描失敗")
        self.result_text.append(f"❌ 掃描錯誤: {error_message}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = SimpleGUI()
    window.show()
    
    print("🔍 智能掃描功能測試程式已啟動")
    print("📋 功能:")
    print("  • 測試 RealDataSources 掃描器")
    print("  • 測試 MarketScanWorker 多線程掃描")
    print("  • 測試信號連接機制")
    print("  • 驗證智能掃描按鈕功能")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
