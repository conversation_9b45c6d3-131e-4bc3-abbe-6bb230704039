#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合併 bargin_report.pkl 和 bargin_report_old.pkl 檔案
"""

import pandas as pd
import os
import shutil
from datetime import datetime
import gc

def check_bargin_file(file_path, file_name):
    """檢查單個 bargin_report 檔案"""
    print(f"\n📊 檢查 {file_name}...")
    
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return None
    
    try:
        # 檢查檔案資訊
        file_size = os.path.getsize(file_path)
        file_mtime = os.path.getmtime(file_path)
        
        print(f"   檔案大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
        print(f"   修改時間: {datetime.fromtimestamp(file_mtime)}")
        
        # 讀取資料
        print("   正在讀取資料...")
        data = pd.read_pickle(file_path)
        
        print(f"   資料筆數: {len(data):,}")
        print(f"   資料結構: {type(data)}")
        
        if hasattr(data, 'index'):
            print(f"   索引類型: {type(data.index)}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
        
        if hasattr(data, 'columns'):
            print(f"   欄位數量: {len(data.columns)}")
            print(f"   欄位名稱: {list(data.columns)[:5]}...")
        
        # 檢查日期範圍
        if hasattr(data, 'index') and hasattr(data.index, 'names') and 'date' in data.index.names:
            dates = data.index.get_level_values('date')
            start_date = dates.min()
            end_date = dates.max()
            unique_dates = len(dates.unique())
            
            print(f"   ✅ 日期範圍: {start_date} 至 {end_date}")
            print(f"   唯一日期數: {unique_dates}")
            
            return {
                'file_path': file_path,
                'file_name': file_name,
                'data': data,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(data),
                'unique_dates': unique_dates
            }
        else:
            print("   ⚠️ 未找到日期索引")
            return None
            
    except Exception as e:
        print(f"   ❌ 讀取失敗: {str(e)}")
        import traceback
        print(f"   詳細錯誤: {traceback.format_exc()}")
        return None

def merge_bargin_files():
    """合併 bargin_report 檔案"""
    print("🔄 開始合併 bargin_report 檔案")
    print("=" * 60)
    
    # 檔案路徑
    source_dir = r"D:\□Finlab學習\用 Python 理財：打造自己的 AI 股票理專_原始檔案\202507_新資料庫\history\tables"
    target_dir = "history/tables"
    
    source_main = os.path.join(source_dir, "bargin_report.pkl")
    source_old = os.path.join(source_dir, "bargin_report_old.pkl")
    target_file = os.path.join(target_dir, "bargin_report.pkl")
    
    # 檢查來源檔案
    main_info = check_bargin_file(source_main, "bargin_report.pkl (來源)")
    old_info = check_bargin_file(source_old, "bargin_report_old.pkl (來源)")
    
    if not main_info and not old_info:
        print("❌ 沒有可用的來源檔案")
        return False
    
    # 檢查目標檔案
    target_info = None
    if os.path.exists(target_file):
        target_info = check_bargin_file(target_file, "bargin_report.pkl (目標)")
    
    print("\n" + "=" * 60)
    print("📊 分析結果")
    print("=" * 60)
    
    # 決定合併策略
    files_to_merge = []
    
    if old_info:
        files_to_merge.append(old_info)
        print(f"✅ 包含舊檔案: {old_info['start_date']} 至 {old_info['end_date']} ({old_info['record_count']:,} 筆)")
    
    if main_info:
        files_to_merge.append(main_info)
        print(f"✅ 包含主檔案: {main_info['start_date']} 至 {main_info['end_date']} ({main_info['record_count']:,} 筆)")
    
    if target_info:
        files_to_merge.append(target_info)
        print(f"✅ 包含目標檔案: {target_info['start_date']} 至 {target_info['end_date']} ({target_info['record_count']:,} 筆)")
    
    if len(files_to_merge) < 2:
        print("⚠️ 只有一個檔案，無需合併")
        if len(files_to_merge) == 1:
            # 直接複製到目標位置
            source_file = files_to_merge[0]['file_path']
            if source_file != target_file:
                print(f"🔄 複製檔案到目標位置...")
                shutil.copy2(source_file, target_file)
                print(f"✅ 已複製到: {target_file}")
        return True
    
    # 確認合併
    print(f"\n💡 將合併 {len(files_to_merge)} 個檔案")
    response = input("是否繼續合併？(y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ 操作已取消")
        return False
    
    try:
        # 創建備份
        if os.path.exists(target_file):
            backup_file = f"{target_file}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            shutil.copy2(target_file, backup_file)
            print(f"✅ 備份創建: {backup_file}")
        
        # 合併資料
        print("🔄 開始合併資料...")
        all_data = []
        all_dates = set()
        
        # 按日期排序檔案
        files_to_merge.sort(key=lambda x: x['start_date'])
        
        for file_info in files_to_merge:
            print(f"   處理: {file_info['file_name']}")
            data = file_info['data']
            
            # 檢查重疊
            file_dates = set(data.index.get_level_values('date'))
            overlap = all_dates.intersection(file_dates)
            
            if overlap:
                print(f"   ⚠️ 發現重疊日期: {len(overlap)} 個")
                print(f"   重疊範圍: {min(overlap)} 至 {max(overlap)}")
                # 移除重疊日期（保留後面檔案的資料）
                data = data[~data.index.get_level_values('date').isin(overlap)]
                print(f"   去重後: {len(data):,} 筆")
            
            all_data.append(data)
            all_dates.update(data.index.get_level_values('date'))
        
        # 合併所有資料
        print("🔄 合併所有資料...")
        merged_data = pd.concat(all_data)
        print(f"✅ 初步合併: {len(merged_data):,} 筆")
        
        # 清理記憶體
        del all_data
        gc.collect()
        
        # 最終去重並排序
        print("🔄 最終去重並排序...")
        merged_data = merged_data[~merged_data.index.duplicated(keep='last')]
        merged_data = merged_data.sort_index()
        
        final_dates = merged_data.index.get_level_values('date')
        print(f"✅ 最終資料: {len(merged_data):,} 筆")
        print(f"   日期範圍: {final_dates.min()} 至 {final_dates.max()}")
        print(f"   唯一日期數: {len(final_dates.unique())}")
        
        # 保存合併結果
        print("💾 保存合併結果...")
        merged_data.to_pickle(target_file)
        print(f"✅ 已保存到: {target_file}")
        
        # 驗證結果
        print("🔍 驗證合併結果...")
        verify_data = pd.read_pickle(target_file)
        verify_dates = verify_data.index.get_level_values('date')
        
        print(f"✅ 驗證成功:")
        print(f"   檔案大小: {os.path.getsize(target_file):,} bytes")
        print(f"   資料筆數: {len(verify_data):,}")
        print(f"   日期範圍: {verify_dates.min()} 至 {verify_dates.max()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合併失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def update_date_range():
    """更新 date_range.pickle"""
    print("\n🔄 更新日期範圍記錄...")
    
    try:
        target_file = "history/tables/bargin_report.pkl"
        date_range_file = "history/date_range.pickle"
        
        # 讀取合併後的資料
        data = pd.read_pickle(target_file)
        dates = data.index.get_level_values('date')
        start_date = dates.min()
        end_date = dates.max()
        
        # 更新記錄
        if os.path.exists(date_range_file):
            import pickle
            with open(date_range_file, 'rb') as f:
                date_ranges = pickle.load(f)
        else:
            date_ranges = {}
        
        date_ranges['bargin_report'] = (start_date, end_date)
        
        with open(date_range_file, 'wb') as f:
            pickle.dump(date_ranges, f)
        
        print(f"✅ 日期範圍已更新: {start_date} 至 {end_date}")
        return True
        
    except Exception as e:
        print(f"❌ 更新日期範圍失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 Bargin Report 檔案合併工具")
    print("=" * 60)
    print("🎯 目標: 合併來源目錄中的 bargin_report 檔案")
    print("=" * 60)
    
    # 執行合併
    success = merge_bargin_files()
    
    if success:
        # 更新日期範圍記錄
        update_date_range()
        
        print("\n🎉 合併完成！")
        print("💡 後續建議:")
        print("   • 檢查合併後的 bargin_report.pkl")
        print("   • 可以重新運行 auto_update.py 繼續更新")
        print("   • 備份檔案已自動創建")
    else:
        print("\n❌ 合併失敗")

if __name__ == "__main__":
    main()
