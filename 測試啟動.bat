@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 啟動測試

echo.
echo ========================================
echo    🧪 台股智能選股系統啟動測試 🧪
echo ========================================
echo.

echo 🔍 檢查可用的版本...
echo.

REM 檢查最小化版本
if exist "dist\StockAnalyzer_Minimal.exe" (
    echo ✅ 最小化版本: dist\StockAnalyzer_Minimal.exe
    for %%A in ("dist\StockAnalyzer_Minimal.exe") do echo    大小: %%~zA bytes
    echo    狀態: 可用
    set "MINIMAL_AVAILABLE=1"
) else (
    echo ❌ 最小化版本: 不存在
    set "MINIMAL_AVAILABLE=0"
)

echo.

REM 檢查其他版本
if exist "dist\StockAnalyzer_Final.exe" (
    echo ✅ 最終版本: dist\StockAnalyzer_Final.exe
    for %%A in ("dist\StockAnalyzer_Final.exe") do echo    大小: %%~zA bytes
) else (
    echo ❌ 最終版本: 不存在
)

if exist "dist\台股智能選股系統_穩定版.exe" (
    echo ✅ 穩定版本: dist\台股智能選股系統_穩定版.exe
    for %%A in ("dist\台股智能選股系統_穩定版.exe") do echo    大小: %%~zA bytes
) else (
    echo ❌ 穩定版本: 不存在
)

echo.
echo ========================================
echo.

if "%MINIMAL_AVAILABLE%"=="1" (
    echo 🚀 啟動最小化版本（推薦）
    echo.
    echo 💡 最小化版本特點：
    echo    ✓ 最高穩定性
    echo    ✓ 最小檔案大小
    echo    ✓ 最快啟動速度
    echo    ✓ 核心功能完整
    echo.
    
    echo 🔄 正在啟動...
    cd /d "dist"
    start "" "StockAnalyzer_Minimal.exe"
    
    echo ✅ 最小化版本已啟動！
    echo.
    echo 📋 如果程式正常顯示，表示測試成功！
    echo.
    echo 🎉 成功指標：
    echo    ✓ 程式視窗正常開啟
    echo    ✓ 股票列表正常載入
    echo    ✓ 無錯誤提示
    echo    ✓ 功能選單可用
    echo.
    
) else (
    echo ❌ 沒有可用的版本！
    echo.
    echo 💡 解決方案：
    echo    1. 重新編譯: python minimal_compile.py
    echo    2. 檢查編譯錯誤
    echo    3. 確認 Python 環境
    echo.
    pause
    exit /b 1
)

echo 📊 測試完成！
echo.
echo 如果程式成功啟動並正常顯示，表示：
echo ✅ 編譯成功
echo ✅ 所有模組問題已解決
echo ✅ 程式可以正常使用
echo.

timeout /t 10 >nul
echo 測試腳本將在 10 秒後關閉...
