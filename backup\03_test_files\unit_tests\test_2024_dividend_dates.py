#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試2024年除權息日期獲取
"""

import logging
from enhanced_dividend_crawler import EnhancedDividendCrawler

def test_2024_dividend_dates():
    """測試2024年除權息日期獲取"""
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 創建爬蟲實例
    crawler = EnhancedDividendCrawler()
    
    print("🔍 測試獲取2024年除權息資料（包含日期）...")
    
    # 分別測試各個資料源
    print("\n📊 測試證交所2024年資料...")
    twse_data = crawler.fetch_twse_dividend_data(2024)
    print(f"證交所獲取資料筆數: {len(twse_data)}")
    
    if twse_data:
        print("證交所資料範例（前5筆）:")
        for i, record in enumerate(twse_data[:5]):
            print(f"  {record['stock_code']} {record['stock_name']}: "
                  f"除權息日期 {record.get('ex_dividend_date', 'N/A')}, "
                  f"現金股利 {record['cash_dividend']}")
    
    print("\n📊 測試櫃買中心2024年資料...")
    tpex_data = crawler.fetch_tpex_dividend_data(2024)
    print(f"櫃買中心獲取資料筆數: {len(tpex_data)}")
    
    if tpex_data:
        print("櫃買中心資料範例（前5筆）:")
        for i, record in enumerate(tpex_data[:5]):
            print(f"  {record['stock_code']} {record['stock_name']}: "
                  f"除權息日期 {record.get('ex_dividend_date', 'N/A')}, "
                  f"現金股利 {record['cash_dividend']}")
    
    # 合併所有資料並儲存
    all_data = twse_data + tpex_data
    if all_data:
        print(f"\n💾 儲存合併資料...")
        crawler.save_dividend_data(all_data)
        
        # 匯出包含日期的CSV
        csv_file = crawler.export_to_csv(2024, "dividend_2024_with_official_dates.csv")
        if csv_file:
            print(f"✅ 2024年除權息資料（含官方日期）已匯出至: {csv_file}")
            
            # 統計有日期的資料筆數
            with_dates = sum(1 for record in all_data if record.get('ex_dividend_date'))
            print(f"📈 包含除權息日期的資料: {with_dates}/{len(all_data)} 筆")

if __name__ == "__main__":
    test_2024_dividend_dates()
