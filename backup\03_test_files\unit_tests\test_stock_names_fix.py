#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試策略交集分析中股票名稱顯示修正
"""

def test_stock_name_mapping():
    """測試股票名稱映射功能"""
    print("🔍 測試股票名稱映射功能")
    print("=" * 40)
    
    # 從主程式中複製的股票名稱對照表（包含新增的股票）
    taiwan_stocks = {
        # 權值股 & 科技股
        "2330": "台積電", "2317": "鴻海", "2454": "聯發科", "2308": "台達電", "2382": "廣達",
        "2303": "聯電", "2357": "華碩", "2409": "友達", "3008": "大立光", "3711": "日月光投控",
        "2379": "瑞昱", "3034": "聯詠", "3481": "群創", "4938": "和碩", "2327": "國巨",
        "2474": "可成", "6669": "緯穎", "2376": "技嘉", "2395": "研華", "3037": "欣興",
        "2408": "南亞科", "3443": "創意", "6415": "矽力-KY", "3661": "世芯-KY", "6770": "力積電",
        "2449": "京元電子", "2458": "義隆", "2301": "光寶科", "2324": "仁寶", "2356": "英業達",
        "2377": "微星", "2412": "中華電", "3045": "台灣大", "4904": "遠傳", "6505": "台塑化",

        # 金融股
        "2881": "富邦金", "2882": "國泰金", "2883": "開發金", "2884": "玉山金", "2885": "元大金",
        "2886": "兆豐金", "2887": "台新金", "2888": "新光金", "2889": "國票金", "2890": "永豐金",
        "2891": "中信金", "2892": "第一金", "2880": "華南金", "5880": "合庫金", "2823": "中壽",

        # 傳統產業（包含新增的股票）
        "1101": "台泥", "1102": "亞泥", "1103": "嘉泥", "1216": "統一", "1301": "台塑",
        "1303": "南亞", "1326": "台化", "1402": "遠東新", "1815": "富喬", "2002": "中鋼", 
        "2105": "正新", "2207": "和泰車", "2311": "日月光", "2312": "金寶", "2313": "華通", 
        "2314": "台揚", "2328": "廣宇", "2329": "華碩", "2603": "長榮", "2609": "陽明", 
        "2610": "華航", "2633": "台灣高鐵", "2801": "彰銀", "3017": "奇鋐", "4991": "環宇-KY",
        "8358": "金居", "8454": "富邦媒", "8996": "高力",

        # ETF（包含新增的ETF）
        "0050": "元大台灣50", "0051": "元大中型100", "0052": "富邦科技", "0053": "元大電子",
        "0054": "元大台商50", "0055": "元大MSCI金融", "0056": "元大高股息", "0057": "富邦摩台",
        "006208": "富邦台50", "00692": "富邦公司治理", "00701": "國泰股利精選30",
        "00938": "中國信託臺灣ESG永續", "00939": "統一台灣高息動能",

        # 電子零組件與設備
        "1519": "中興電", "3289": "宜特", "5278": "尚凡", "6290": "良維", "6196": "帆宣",
        "6667": "信紘科", "6698": "旭暉應材", "8039": "台虹", "4414": "如興", "2414": "精技",
        "8499": "鼎炫-KY", "0885": "富邦越南",

        # 其他重要股票
        "4938": "和碩", "5871": "中租-KY", "5876": "上海商銀", "6505": "台塑化",
        "6669": "緯穎"
    }
    
    def get_stock_names_for_codes(stock_codes):
        """模擬主程式中的股票名稱獲取函數"""
        stock_names = {}
        for code in stock_codes:
            if code in taiwan_stocks:
                stock_names[code] = taiwan_stocks[code]
            else:
                stock_names[code] = f"股票{code}"
        return stock_names
    
    # 測試圖片中出現的股票代碼
    test_codes = [
        "00938", "1815", "2308", "3017", "4991", "8358",  # 圖片中的股票
        "2330", "2317", "2454", "2884", "2801",          # 常見股票
        "9999", "1234"                                    # 不存在的股票
    ]
    
    print("📊 測試股票代碼轉換:")
    stock_names = get_stock_names_for_codes(test_codes)
    
    for code in test_codes:
        name = stock_names[code]
        if name.startswith("股票"):
            status = "❌"
            print(f"  {status} {code} → {name} (缺失)")
        else:
            status = "✅"
            print(f"  {status} {code} → {name}")
    
    # 統計結果
    correct_count = sum(1 for name in stock_names.values() if not name.startswith("股票"))
    total_count = len(test_codes)
    missing_count = total_count - correct_count
    
    print(f"\n📈 統計結果:")
    print(f"  總測試股票: {total_count}")
    print(f"  有正確名稱: {correct_count}")
    print(f"  缺失名稱: {missing_count}")
    print(f"  成功率: {correct_count/total_count*100:.1f}%")
    
    return missing_count == 2  # 預期只有2個測試用的不存在股票

def simulate_intersection_display():
    """模擬策略交集結果顯示"""
    print("\n🔍 模擬策略交集結果顯示")
    print("=" * 40)
    
    # 模擬交集結果（基於圖片中的數據）
    mock_results = {
        "藏獒 + CANSLIM量價齊升": {
            "intersection_stocks": ["00938", "1815", "2308", "3017", "4991", "8358"]
        },
        "藏獒 + 二次創高股票": {
            "intersection_stocks": ["00938", "1815", "2308", "3017", "4991", "8358"]
        },
        "CANSLIM量價齊升 + 二次創高股票": {
            "intersection_stocks": ["00938", "1815", "2308", "4991", "8358"]
        }
    }
    
    # 股票名稱對照表
    taiwan_stocks = {
        "00938": "中國信託臺灣ESG永續", "1815": "富喬", "2308": "台達電", 
        "3017": "奇鋐", "4991": "環宇-KY", "8358": "金居"
    }
    
    def get_stock_names_for_codes(stock_codes):
        stock_names = {}
        for code in stock_codes:
            if code in taiwan_stocks:
                stock_names[code] = taiwan_stocks[code]
            else:
                stock_names[code] = f"股票{code}"
        return stock_names
    
    # 模擬顯示結果
    print("📊 修正後的交集分析結果:")
    
    for i, (combo_name, data) in enumerate(mock_results.items(), 1):
        stocks = data['intersection_stocks']
        count = len(stocks)
        print(f"  {i:2d}. {combo_name}: {count} 支共同股票")
        
        if stocks:
            # 獲取股票名稱
            stock_names = get_stock_names_for_codes(stocks)
            stock_info = [f"{code}({stock_names[code]})" for code in sorted(stocks)]
            print(f"      股票: {', '.join(stock_info)}")
            
            # 檢查是否還有"股票代碼"格式
            has_stock_prefix = any(name.startswith("股票") for name in stock_names.values())
            if has_stock_prefix:
                print(f"      ⚠️  仍有股票顯示為'股票代碼'格式")
            else:
                print(f"      ✅ 所有股票都有正確名稱")
        print("")
    
    return True

def main():
    """主測試函數"""
    print("🚀 策略交集股票名稱顯示修正測試")
    print("=" * 50)
    
    # 測試1: 股票名稱映射
    success1 = test_stock_name_mapping()
    
    # 測試2: 模擬交集結果顯示
    success2 = simulate_intersection_display()
    
    print("=" * 50)
    print("📋 測試結果摘要:")
    print(f"  股票名稱映射: {'✅ 通過' if success1 else '❌ 失敗'}")
    print(f"  交集結果顯示: {'✅ 通過' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print("\n🎉 所有測試通過！股票名稱顯示問題已修正")
        print("\n💡 修正內容:")
        print("  • 新增 00938 (中國信託臺灣ESG永續)")
        print("  • 新增 1815 (富喬)")
        print("  • 新增 3017 (奇鋐)")
        print("  • 新增 4991 (環宇-KY)")
        print("  • 新增 8358 (金居)")
        print("  • 新增 00939 (統一台灣高息動能)")
        print("\n📊 現在策略交集分析中的股票都會正確顯示名稱而不是代碼！")
    else:
        print("\n❌ 部分測試失敗，需要進一步檢查")
    
    return success1 and success2

if __name__ == "__main__":
    main()
