#!/usr/bin/env python3
"""
測試工具選單清理效果
驗證重複的AI技術指標回測功能已從工具選單中移除
"""

import sys
import os
from datetime import datetime

def test_menu_structure_cleanup():
    """測試選單結構清理"""
    print("🔍 檢查工具選單清理效果...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查工具選單中是否還有AI技術指標回測
        tools_menu_has_ai_backtest = "tools_menu.addAction('🤖 AI技術指標回測')" in content
        
        # 檢查回測選單中是否保留AI技術指標回測
        backtest_menu_has_ai_backtest = "backtest_menu.addAction('🤖 AI技術指標回測')" in content
        
        # 檢查是否移除了對應的方法調用
        tools_ai_backtest_connect = "ai_backtest_action.triggered.connect(self.open_ai_backtest_window)" in content
        
        # 檢查是否移除了 open_ai_backtest_window 方法
        open_ai_backtest_window_method = "def open_ai_backtest_window(self):" in content
        
        print("\n❌ 工具選單清理檢查:")
        print(f"   {'❌ 仍存在' if tools_menu_has_ai_backtest else '✅ 已移除'} 工具選單中的AI技術指標回測項目")
        print(f"   {'❌ 仍存在' if tools_ai_backtest_connect else '✅ 已移除'} 工具選單中的方法連接")
        print(f"   {'❌ 仍存在' if open_ai_backtest_window_method else '✅ 已移除'} open_ai_backtest_window 方法")
        
        print("\n✅ 回測選單保留檢查:")
        print(f"   {'✅ 保留' if backtest_menu_has_ai_backtest else '❌ 缺失'} 回測選單中的AI技術指標回測項目")
        
        # 檢查是否保留了正確的回測選單方法
        open_ai_technical_backtest_method = "def open_ai_technical_backtest(self):" in content
        print(f"   {'✅ 保留' if open_ai_technical_backtest_method else '❌ 缺失'} open_ai_technical_backtest 方法")
        
        # 總體評估
        cleanup_success = (
            not tools_menu_has_ai_backtest and 
            not tools_ai_backtest_connect and 
            not open_ai_backtest_window_method and
            backtest_menu_has_ai_backtest and
            open_ai_technical_backtest_method
        )
        
        print(f"\n🎯 清理結果: {'✅ 成功' if cleanup_success else '❌ 不完整'}")
        
        return cleanup_success
        
    except Exception as e:
        print(f"❌ 檢查選單結構清理失敗: {e}")
        return False

def test_current_menu_structure():
    """測試當前選單結構"""
    print("\n📋 檢查當前選單結構...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析回測選單結構
        print("\n📊 回測選單結構:")
        backtest_items = [
            ("🎯 阿水一式回測分析", "ashui_backtest_action"),
            ("🤖 AI技術指標回測", "ai_backtest_action"),
            ("🎨 多策略整合分析", "multi_strategy_action"),
            ("📖 策略解讀指南", "strategy_guide_action"),
            ("🧪 信號強度測試", "signal_test_action")
        ]
        
        for item_name, action_var in backtest_items:
            found = action_var in content
            status = "✅" if found else "❌"
            print(f"   {status} {item_name}")
        
        # 分析工具選單結構
        print("\n🔧 工具選單結構:")
        tools_items = [
            ("🎯 策略參數優化", "optimize_action"),
            ("📊 資料庫配置", "db_config_action"),
            ("🔍 系統診斷", "diagnostic_action"),
            ("ℹ️ 關於系統", "about_action")
        ]
        
        for item_name, action_var in tools_items:
            found = action_var in content
            status = "✅" if found else "❌"
            print(f"   {status} {item_name}")
        
        # 檢查是否還有其他重複項目
        print("\n🔍 重複項目檢查:")
        potential_duplicates = [
            ("回測", "backtest"),
            ("AI", "ai"),
            ("技術指標", "technical")
        ]
        
        for keyword_zh, keyword_en in potential_duplicates:
            zh_count = content.count(keyword_zh)
            en_count = content.lower().count(keyword_en)
            print(f"   '{keyword_zh}' 出現次數: {zh_count}")
            print(f"   '{keyword_en}' 出現次數: {en_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查當前選單結構失敗: {e}")
        return False

def test_functionality_preservation():
    """測試功能保留情況"""
    print("\n🧪 測試功能保留情況...")
    
    try:
        from O3mh_gui_v21_optimized import MainWindow
        
        # 創建主窗口實例
        app = None
        try:
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
        except:
            pass
        
        main_window = MainWindow()
        print("✅ 主程式實例創建成功")
        
        # 檢查回測選單相關方法是否存在
        backtest_methods = [
            'open_ashui_backtest_window',
            'open_ai_technical_backtest',
            'open_multi_strategy_analysis',
            'open_strategy_interpretation_guide',
            'open_signal_strength_test'
        ]
        
        print("\n📊 回測選單方法檢查:")
        for method_name in backtest_methods:
            if hasattr(main_window, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
        
        # 檢查工具選單相關方法是否存在
        tools_methods = [
            'open_strategy_optimizer',
            'open_database_config',
            'open_system_diagnostic',
            'show_about'
        ]
        
        print("\n🔧 工具選單方法檢查:")
        for method_name in tools_methods:
            if hasattr(main_window, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ⚠️ {method_name} 方法缺失（可能正常）")
        
        # 檢查已移除的方法是否確實不存在
        removed_methods = [
            'open_ai_backtest_window'
        ]
        
        print("\n🗑️ 已移除方法檢查:")
        for method_name in removed_methods:
            if hasattr(main_window, method_name):
                print(f"   ❌ {method_name} 仍然存在（應該已移除）")
            else:
                print(f"   ✅ {method_name} 已成功移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試功能保留情況失敗: {e}")
        return False

def analyze_code_optimization():
    """分析代碼優化效果"""
    print("\n📊 分析代碼優化效果...")
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        
        # 統計選單相關的行數
        menu_lines = 0
        backtest_lines = 0
        tools_lines = 0
        
        for line in lines:
            if 'menu' in line.lower() or '選單' in line:
                menu_lines += 1
            if 'backtest' in line.lower() or '回測' in line:
                backtest_lines += 1
            if 'tools_menu' in line or '工具' in line:
                tools_lines += 1
        
        print(f"   📄 總行數: {total_lines}")
        print(f"   📋 選單相關行數: {menu_lines}")
        print(f"   📊 回測相關行數: {backtest_lines}")
        print(f"   🔧 工具相關行數: {tools_lines}")
        
        # 檢查重複功能的減少
        ai_backtest_mentions = 0
        for line in lines:
            if 'AI技術指標回測' in line or 'ai_backtest' in line:
                ai_backtest_mentions += 1
        
        print(f"   🤖 AI技術指標回測提及次數: {ai_backtest_mentions}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析代碼優化效果失敗: {e}")
        return False

def generate_cleanup_report():
    """生成清理報告"""
    print("\n📊 生成清理報告...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"tools_menu_cleanup_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("工具選單清理報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"清理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("🗑️ 移除的重複功能:\n")
            f.write("1. 工具選單中的 '🤖 AI技術指標回測' 項目\n")
            f.write("2. 對應的方法調用: ai_backtest_action.triggered.connect(self.open_ai_backtest_window)\n")
            f.write("3. 無用的方法: open_ai_backtest_window()\n\n")
            
            f.write("✅ 保留的功能:\n")
            f.write("1. 回測選單中的 '🤖 AI技術指標回測' 項目\n")
            f.write("2. 對應的方法: open_ai_technical_backtest()\n")
            f.write("3. 完整的回測功能集合\n\n")
            
            f.write("🎯 清理原因:\n")
            f.write("• 回測功能已獨立成為頂層選單項目\n")
            f.write("• 工具選單中的項目造成功能重複\n")
            f.write("• 用戶容易混淆兩個相同功能的入口\n")
            f.write("• 簡化選單結構，提高用戶體驗\n\n")
            
            f.write("📋 當前選單結構:\n")
            f.write("📊 回測 (頂層選單)\n")
            f.write("├── 🎯 阿水一式回測分析\n")
            f.write("├── 🤖 AI技術指標回測\n")
            f.write("├── 🎨 多策略整合分析\n")
            f.write("├── 📖 策略解讀指南\n")
            f.write("└── 🧪 信號強度測試\n\n")
            f.write("🔧 工具 (頂層選單)\n")
            f.write("├── 🎯 策略參數優化\n")
            f.write("├── 📊 資料庫配置\n")
            f.write("├── 🔍 系統診斷\n")
            f.write("└── ℹ️ 關於系統\n\n")
            
            f.write("💡 用戶體驗改善:\n")
            f.write("• 不再有重複的功能入口\n")
            f.write("• 回測功能統一在回測選單中\n")
            f.write("• 工具選單專注於系統工具功能\n")
            f.write("• 選單結構更加清晰和邏輯化\n\n")
            
            f.write("🚀 使用方式:\n")
            f.write("• 回測功能: 📊 回測 -> 🤖 AI技術指標回測\n")
            f.write("• 系統工具: 🔧 工具 -> 選擇相應工具\n")
        
        print(f"✅ 清理報告已生成: {report_filename}")
        return report_filename
        
    except Exception as e:
        print(f"❌ 生成清理報告失敗: {e}")
        return None

def demonstrate_improvements():
    """展示改進效果"""
    print("\n💡 工具選單清理改進效果:")
    print("=" * 60)
    
    print("🔧 清理前的問題:")
    problems = [
        "工具選單和回測選單都有AI技術指標回測功能",
        "用戶不知道應該使用哪個入口",
        "功能重複造成維護困難",
        "選單結構不夠清晰"
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"   {i}. {problem}")
    
    print("\n✅ 清理後的改善:")
    improvements = [
        "移除工具選單中的重複項目",
        "回測功能統一在回測選單中",
        "工具選單專注於系統工具",
        "選單結構更加邏輯化",
        "減少用戶選擇困擾",
        "提高代碼維護性"
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"   {i}. {improvement}")
    
    print("\n📋 功能分工:")
    print("   📊 回測選單:")
    print("     • 專門負責所有回測相關功能")
    print("     • 包含策略回測、多策略分析、解讀指南等")
    print("     • 功能完整且集中")
    print()
    print("   🔧 工具選單:")
    print("     • 專門負責系統工具和配置")
    print("     • 包含參數優化、資料庫配置、系統診斷等")
    print("     • 輔助性功能集合")

def main():
    """主函數"""
    print("🚀 工具選單清理測試程式")
    print("=" * 60)
    
    # 運行所有測試
    tests = [
        ("選單結構清理檢查", test_menu_structure_cleanup),
        ("當前選單結構檢查", test_current_menu_structure),
        ("功能保留情況測試", test_functionality_preservation),
        ("代碼優化效果分析", analyze_code_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 執行測試: {test_name}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行失敗: {e}")
            results.append(False)
    
    # 展示改進效果
    demonstrate_improvements()
    
    # 生成報告
    report_file = generate_cleanup_report()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通過" if results[i] else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed}/{total} 測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！工具選單清理成功")
        print("\n💡 主要改善:")
        print("   • 移除了工具選單中的重複AI技術指標回測功能")
        print("   • 回測功能統一在回測選單中")
        print("   • 選單結構更加清晰和邏輯化")
        print("   • 減少了用戶的選擇困擾")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    if report_file:
        print(f"\n📊 詳細報告已保存至: {report_file}")

if __name__ == '__main__':
    main()
