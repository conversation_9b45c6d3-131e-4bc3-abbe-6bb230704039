#!/usr/bin/env python3
"""
CANSLIM量價齊升策略測試腳本
測試威廉·歐尼爾經典選股法的實作效果
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加主程式路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_data(stock_id, days=300):
    """創建測試用的股票數據"""
    np.random.seed(hash(stock_id) % 2**32)  # 根據股票代碼設定種子
    
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 基礎價格走勢
    base_price = 50 + np.random.uniform(-20, 50)
    price_trend = np.random.uniform(-0.001, 0.003)  # 日均漲跌幅
    volatility = 0.02
    
    prices = []
    volumes = []
    
    for i in range(days):
        if i == 0:
            price = base_price
        else:
            # 價格隨機遊走 + 趨勢
            change = np.random.normal(price_trend, volatility)
            price = prices[-1] * (1 + change)
            price = max(price, 1.0)  # 最低1元
        
        prices.append(price)
        
        # 成交量：基礎量 + 隨機變動
        base_volume = 100000 + np.random.uniform(0, 500000)
        if i > 0 and prices[i] > prices[i-1]:  # 上漲時放量
            volume_multiplier = np.random.uniform(1.2, 3.0)
        else:
            volume_multiplier = np.random.uniform(0.8, 1.5)
        
        volume = int(base_volume * volume_multiplier)
        volumes.append(volume)
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p * np.random.uniform(0.98, 1.02) for p in prices],
        'High': [p * np.random.uniform(1.0, 1.05) for p in prices],
        'Low': [p * np.random.uniform(0.95, 1.0) for p in prices],
        'Close': prices,
        'Volume': volumes
    })
    
    return df

def test_canslim_strategy():
    """測試CANSLIM策略"""
    try:
        # 導入CANSLIM策略類（使用獨立版本）
        from canslim_standalone import CANSLIMStrategy
        
        # 初始化策略
        canslim = CANSLIMStrategy()
        
        # 測試股票列表
        test_stocks = ['2330', '2317', '2454', '1301', '2303', '2382', '2412', '2881', '2886', '3008']
        
        print("🚀 CANSLIM量價齊升策略測試")
        print("=" * 60)
        
        results = []
        
        for stock_id in test_stocks:
            print(f"\n📊 測試股票: {stock_id}")
            
            # 創建測試數據
            df = create_sample_data(stock_id)
            
            # 執行CANSLIM分析
            result = canslim.analyze_stock(df)
            
            if result['suitable']:
                print(f"✅ 符合CANSLIM條件")
                print(f"   總分: {result['total_score']}/100")
                print(f"   原因: {result['reason']}")
                
                # 顯示詳細評分
                details = result['details']
                for key, value in details.items():
                    status = "✓" if value['pass'] else "✗"
                    print(f"   {key}: {status} {value['score']}分 - {value['reason']}")
                
                results.append({
                    'stock_id': stock_id,
                    'score': result['total_score'],
                    'suitable': True
                })
            else:
                print(f"❌ 不符合CANSLIM條件")
                print(f"   總分: {result['total_score']}/100")
                print(f"   原因: {result['reason']}")
                
                results.append({
                    'stock_id': stock_id,
                    'score': result['total_score'],
                    'suitable': False
                })
        
        # 統計結果
        suitable_stocks = [r for r in results if r['suitable']]
        avg_score = np.mean([r['score'] for r in results])
        
        print("\n" + "=" * 60)
        print("📈 測試結果統計")
        print(f"測試股票數: {len(test_stocks)}")
        print(f"符合條件: {len(suitable_stocks)} 支")
        print(f"通過率: {len(suitable_stocks)/len(test_stocks)*100:.1f}%")
        print(f"平均評分: {avg_score:.1f}/100")
        
        if suitable_stocks:
            print(f"\n🎯 符合條件的股票:")
            for stock in sorted(suitable_stocks, key=lambda x: x['score'], reverse=True):
                print(f"   {stock['stock_id']}: {stock['score']}/100分")
        
        print("\n💡 CANSLIM策略說明:")
        print("• C - 當季盈餘增長 (25分)")
        print("• A - 年度盈餘增長 (20分)")
        print("• N - 新產品/服務 (15分)")
        print("• S - 供需關係/量價齊升 (20分) ⭐必要條件⭐")
        print("• L - 領導股地位 (10分)")
        print("• I - 機構認同度 (5分)")
        print("• M - 市場方向 (5分)")
        print("• 通過標準: 總分≥60分 且 S項目必須通過")
        
        return results
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        print("請確保 O3mh_gui_v21_optimized.py 文件存在且包含 CANSLIMStrategy 類")
        return []
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_canslim_strategy()
