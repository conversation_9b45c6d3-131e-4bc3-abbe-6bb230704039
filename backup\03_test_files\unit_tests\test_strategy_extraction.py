#!/usr/bin/env python3
"""
策略提取測試腳本
測試第一個策略提取是否成功
"""
import sys
import os

def test_strategy_module():
    """測試策略模組"""
    try:
        from strategies.triple_rsi_strategy import TripleRSIStrategy
        from strategies.small_investor_strategy import SmallInvestorEliteStrategy
        from strategies.second_high_strategy import SecondHighStrategy
        from strategies.timid_cat_strategy import TimidCatStrategy
        from strategies.canslim_strategy import CANSLIMStrategy
        from strategies.intraday_strategy import IntradayOpeningRangeStrategy

        print("✅ 所有策略模組導入成功")

        # 測試實例化
        strategies = [
            TripleRSIStrategy(),
            SmallInvestorEliteStrategy(),
            SecondHighStrategy(),
            TimidCatStrategy(),
            CANSLIMStrategy(),
            IntradayOpeningRangeStrategy()
        ]

        for i, strategy in enumerate(strategies, 1):
            print(f"✅ 策略{i}實例化成功: {strategy.name}")

        # 測試基本屬性
        all_have_analyze = all(hasattr(s, 'analyze_stock') for s in strategies)
        if all_have_analyze:
            print("✅ 所有策略都有 analyze_stock 方法")

        return True
    except Exception as e:
        print(f"❌ 策略模組測試失敗: {e}")
        return False

def test_main_program():
    """測試主程式是否正常"""
    try:
        import O3mh_gui_v21_optimized
        print("✅ 主程式模組導入成功")

        # 測試策略類別是否可用
        from O3mh_gui_v21_optimized import (TripleRSIStrategy, SmallInvestorEliteStrategy,
                                           SecondHighStrategy, TimidCatStrategy,
                                           CANSLIMStrategy, IntradayOpeningRangeStrategy)

        strategies = [
            TripleRSIStrategy(),
            SmallInvestorEliteStrategy(),
            SecondHighStrategy(),
            TimidCatStrategy(),
            CANSLIMStrategy(),
            IntradayOpeningRangeStrategy()
        ]

        for i, strategy in enumerate(strategies, 1):
            print(f"✅ 主程式中的策略{i}正常: {strategy.name}")

        return True
    except Exception as e:
        print(f"❌ 主程式測試失敗: {e}")
        return False

def check_file_size_reduction():
    """檢查文件大小減少"""
    try:
        import os

        current_size = os.path.getsize('O3mh_gui_v21_optimized.py')
        backup_size = os.path.getsize('O3mh_gui_v21_optimized_BEFORE_STRATEGY_EXTRACTION.py')

        reduction = backup_size - current_size
        percentage = (reduction / backup_size) * 100

        print(f"📊 文件大小變化:")
        print(f"   原始: {backup_size:,} 字節")
        print(f"   現在: {current_size:,} 字節")
        print(f"   減少: {reduction:,} 字節 ({percentage:.1f}%)")

        return reduction > 0
    except Exception as e:
        print(f"❌ 文件大小檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始策略提取測試...")
    print("=" * 50)

    tests = [
        ("策略模組測試", test_strategy_module),
        ("主程式兼容性測試", test_main_program),
        ("文件大小減少檢查", check_file_size_reduction)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 執行 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")

    if passed == total:
        print("🎉 所有策略提取成功！")
        print("✅ TripleRSIStrategy 已成功模組化")
        print("✅ SmallInvestorEliteStrategy 已成功模組化")
        print("✅ SecondHighStrategy 已成功模組化")
        print("✅ TimidCatStrategy 已成功模組化")
        print("✅ CANSLIMStrategy 已成功模組化")
        print("✅ IntradayOpeningRangeStrategy 已成功模組化")
        print("✅ 主程式功能保持完整")
        print("✅ 文件大小已減少")
        return True
    else:
        print("❌ 部分測試失敗，需要檢查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)