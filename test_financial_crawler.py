#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 financial_data 爬蟲
"""

import sys
import os
import datetime

# 添加 finlab 路徑
sys.path.insert(0, 'finlab')

def test_financial_crawler():
    """測試財務資料爬蟲"""
    
    print("=" * 80)
    print("🧪 測試 financial_data 爬蟲")
    print("=" * 80)
    
    try:
        # 導入爬蟲函數
        from crawler import crawl_unified_financial_data
        
        # 測試單一日期
        test_date = datetime.date(2025, 5, 15)  # Q1 財報發布日
        print(f"📅 測試日期: {test_date}")
        
        # 執行爬蟲
        print(f"🔄 開始執行爬蟲...")
        df = crawl_unified_financial_data(test_date)
        
        if df is not None and not df.empty:
            print(f"✅ 爬蟲執行成功")
            print(f"   資料筆數: {len(df):,}")
            print(f"   欄位數量: {len(df.columns)}")
            
            # 顯示欄位結構
            print(f"\n📊 欄位結構:")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i:2d}. {col}")
            
            # 顯示前幾筆資料
            print(f"\n📋 前3筆資料:")
            for i, (idx, row) in enumerate(df.head(3).iterrows(), 1):
                print(f"   {i}. {row['stock_id']} - {row['stock_name']}")
            
            # 檢查關鍵欄位
            key_fields = ['stock_id', 'stock_name', 'report_date', 'date']
            print(f"\n🔍 關鍵欄位檢查:")
            for field in key_fields:
                if field in df.columns:
                    print(f"   ✅ {field}: 存在")
                    if len(df) > 0:
                        sample_value = df[field].iloc[0]
                        print(f"      範例值: {sample_value}")
                else:
                    print(f"   ❌ {field}: 不存在")
            
            return df
        else:
            print(f"❌ 爬蟲執行失敗或無資料")
            return None
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_season_range():
    """測試 season_range 函數"""
    
    print(f"\n" + "=" * 80)
    print("🧪 測試 season_range 函數")
    print("=" * 80)
    
    try:
        from crawler import season_range
        
        # 測試從 2018 到現在
        start_date = datetime.date(2018, 1, 1)
        end_date = datetime.date.today()
        
        print(f"📅 測試範圍: {start_date} 到 {end_date}")
        
        # 生成季度日期
        season_dates = season_range(start_date, end_date)
        
        print(f"📊 生成的季度日期:")
        print(f"   總數: {len(season_dates)} 個")
        
        if season_dates:
            print(f"   第一個: {season_dates[0]}")
            print(f"   最後一個: {season_dates[-1]}")
            
            # 顯示所有日期
            print(f"\n📋 所有季度日期:")
            for i, date in enumerate(season_dates, 1):
                print(f"   {i:2d}. {date}")
        else:
            print(f"   ⚠️ 沒有生成任何日期")
        
        return season_dates
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函數"""
    
    print("🧪 financial_data 爬蟲測試工具")
    
    # 測試 season_range 函數
    season_dates = test_season_range()
    
    # 測試財務爬蟲
    df = test_financial_crawler()
    
    # 總結
    print(f"\n" + "=" * 80)
    print("📊 測試總結")
    print("=" * 80)
    
    if season_dates:
        print(f"✅ season_range 函數正常，生成 {len(season_dates)} 個季度日期")
    else:
        print(f"❌ season_range 函數異常，沒有生成日期")
    
    if df is not None and not df.empty:
        print(f"✅ 財務爬蟲正常，獲取 {len(df)} 筆資料")
    else:
        print(f"❌ 財務爬蟲異常，沒有獲取資料")
    
    # 建議
    print(f"\n💡 建議:")
    if not season_dates:
        print(f"   - 檢查 season_range 函數的日期範圍邏輯")
    if df is None or df.empty:
        print(f"   - 檢查 crawl_unified_financial_data 函數的實作")
        print(f"   - 檢查網路連線和 API 可用性")

if __name__ == "__main__":
    main()
