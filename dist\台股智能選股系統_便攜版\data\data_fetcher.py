#!/usr/bin/env python3
"""
數據獲取模組
從原始程式中提取的數據獲取功能
"""
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Optional, Dict, Any

# 檢查模組可用性
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False

try:
    import twstock
    TWSTOCK_AVAILABLE = True
except ImportError:
    TWSTOCK_AVAILABLE = False


class DataFetcher:
    """數據獲取器 - 統一的數據獲取接口"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分鐘緩存

    def fetch_stock_data(self, stock_id: str, period_days: int = 90) -> pd.DataFrame:
        """
        獲取股票數據的主要接口

        Args:
            stock_id: 股票代碼
            period_days: 數據期間（天數）

        Returns:
            包含OHLCV數據的DataFrame
        """
        # 檢查緩存
        cache_key = f"{stock_id}_{period_days}"
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']

        # 嘗試不同的數據源
        df = pd.DataFrame()

        # 1. 優先使用 twstock (台股專用)
        if TWSTOCK_AVAILABLE:
            df = self._fetch_twstock_data(stock_id, period_days)

        # 2. 備用 yfinance
        if df.empty and YFINANCE_AVAILABLE:
            df = self._fetch_yfinance_data(stock_id, period_days)

        # 3. 緩存結果
        if not df.empty:
            self._cache_data(cache_key, df)

        return df

    def _fetch_twstock_data(self, stock_id: str, period_days: int) -> pd.DataFrame:
        """使用 twstock 獲取數據"""
        try:
            stock = twstock.Stock(stock_id)

            # 計算需要獲取的日期範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days * 2)  # 多取一些以確保足夠數據

            # 獲取歷史數據
            data = stock.fetch_from(start_date.year, start_date.month)

            if not data:
                return pd.DataFrame()

            # 轉換為DataFrame
            df = pd.DataFrame(data)
            df.columns = ['Date', 'Volume', 'Open', 'High', 'Low', 'Close']

            # 數據清理和格式化
            df['Date'] = pd.to_datetime(df['Date'])
            df = df.set_index('Date')
            df = df.sort_index()

            # 確保數據類型正確
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 移除無效數據
            df = df.dropna()

            # 限制到指定期間
            if len(df) > period_days:
                df = df.tail(period_days)

            logging.info(f"✅ twstock 獲取 {stock_id} 數據成功: {len(df)} 筆")
            return df

        except Exception as e:
            logging.error(f"❌ twstock 獲取 {stock_id} 數據失敗: {e}")
            return pd.DataFrame()

    def _fetch_yfinance_data(self, stock_id: str, period_days: int) -> pd.DataFrame:
        """使用 yfinance 獲取數據"""
        try:
            # 台股代碼需要加上 .TW 後綴
            symbol = f"{stock_id}.TW"

            # 計算期間
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days + 30)  # 多取一些數據

            # 獲取數據
            ticker = yf.Ticker(symbol)
            df = ticker.history(start=start_date, end=end_date)

            if df.empty:
                return pd.DataFrame()

            # 重命名列以保持一致性
            df = df.rename(columns={
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })

            # 只保留需要的列
            df = df[['Open', 'High', 'Low', 'Close', 'Volume']]

            # 移除無效數據
            df = df.dropna()

            # 限制到指定期間
            if len(df) > period_days:
                df = df.tail(period_days)

            logging.info(f"✅ yfinance 獲取 {stock_id} 數據成功: {len(df)} 筆")
            return df

        except Exception as e:
            logging.error(f"❌ yfinance 獲取 {stock_id} 數據失敗: {e}")
            return pd.DataFrame()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """檢查緩存是否有效"""
        if cache_key not in self.cache:
            return False

        cache_time = self.cache[cache_key]['timestamp']
        return (time.time() - cache_time) < self.cache_timeout

    def _cache_data(self, cache_key: str, data: pd.DataFrame):
        """緩存數據"""
        self.cache[cache_key] = {
            'data': data.copy(),
            'timestamp': time.time()
        }

        # 限制緩存大小
        if len(self.cache) > 100:
            # 移除最舊的緩存
            oldest_key = min(self.cache.keys(),
                           key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]

    def clear_cache(self):
        """清除緩存"""
        self.cache.clear()
        logging.info("📝 數據緩存已清除")


# 全局數據獲取器實例
data_fetcher = DataFetcher()