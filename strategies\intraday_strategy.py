#!/usr/bin/env python3
"""
日內開盤區間策略模組
從原始程式中提取的IntradayOpeningRangeStrategy類別
"""
import logging
import pandas as pd
import numpy as np
from datetime import datetime, time
from .base_strategy import BaseStrategy


class IntradayOpeningRangeStrategy(BaseStrategy):
    """日內開盤區間突破策略 - 基於開盤後30分鐘區間的突破交易"""

    def __init__(self):
        super().__init__(
            name="日內開盤區間策略",
            description="基於開盤後30分鐘價格區間的突破交易策略，適合日內交易",
            strategy_type="intraday_breakout"
        )

        # 策略參數
        self.opening_range_minutes = 30    # 開盤區間時間（分鐘）
        self.min_range_percent = 0.005     # 最小區間百分比0.5%
        self.max_range_percent = 0.03      # 最大區間百分比3%
        self.volume_threshold = 1.5        # 成交量門檻倍數
        self.profit_target = 0.02          # 獲利目標2%
        self.stop_loss = 0.01              # 停損1%

    def simulate_intraday_data(self, df):
        """模擬日內數據（因為缺少真實分鐘數據）"""
        try:
            if df.empty:
                return None

            # 使用日線數據模擬分鐘數據
            latest = df.iloc[-1]

            # 模擬開盤後30分鐘的價格區間
            open_price = latest['Open']
            high_price = latest['High']
            low_price = latest['Low']
            close_price = latest['Close']

            # 假設開盤區間在開盤價附近波動
            range_size = (high_price - low_price) * 0.3  # 假設開盤區間佔全日區間30%

            opening_high = min(open_price + range_size/2, high_price)
            opening_low = max(open_price - range_size/2, low_price)

            return {
                'open': open_price,
                'opening_high': opening_high,
                'opening_low': opening_low,
                'current_price': close_price,
                'volume': latest['Volume'],
                'range_size': range_size,
                'range_percent': range_size / open_price
            }

        except Exception as e:
            logging.error(f"模擬日內數據失敗: {e}")
            return None

    def check_opening_range_breakout(self, df):
        """檢查開盤區間突破"""
        try:
            intraday_data = self.simulate_intraday_data(df)

            if intraday_data is None:
                return False, "無法獲取日內數據"

            current_price = intraday_data['current_price']
            opening_high = intraday_data['opening_high']
            opening_low = intraday_data['opening_low']
            range_percent = intraday_data['range_percent']

            # 檢查區間大小是否合適
            if range_percent < self.min_range_percent:
                return False, f"開盤區間過小 {range_percent:.2%} < {self.min_range_percent:.2%}"

            if range_percent > self.max_range_percent:
                return False, f"開盤區間過大 {range_percent:.2%} > {self.max_range_percent:.2%}"

            # 檢查是否突破開盤區間
            if current_price > opening_high:
                breakout_percent = (current_price - opening_high) / opening_high
                return True, f"向上突破 {current_price:.2f} > {opening_high:.2f} (+{breakout_percent:.2%})"
            elif current_price < opening_low:
                breakout_percent = (opening_low - current_price) / opening_low
                return True, f"向下突破 {current_price:.2f} < {opening_low:.2f} (-{breakout_percent:.2%})"
            else:
                return False, f"未突破區間 {opening_low:.2f} <= {current_price:.2f} <= {opening_high:.2f}"

        except Exception as e:
            return False, f"開盤區間突破檢查失敗: {str(e)}"

    def check_volume_confirmation(self, df):
        """檢查成交量確認"""
        try:
            if len(df) < 20:
                return False, "數據不足以檢查成交量"

            current_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume'].iloc[-20:].mean()

            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            if volume_ratio >= self.volume_threshold:
                return True, f"成交量確認 {volume_ratio:.1f}倍 >= {self.volume_threshold}倍"
            else:
                return False, f"成交量不足 {volume_ratio:.1f}倍 < {self.volume_threshold}倍"

        except Exception as e:
            return False, f"成交量確認失敗: {str(e)}"

    def check_market_timing(self):
        """檢查市場時間（模擬）"""
        try:
            # 模擬檢查是否在交易時間內
            # 實際應用中需要檢查當前時間是否在開盤後30分鐘到收盤前1小時

            current_time = datetime.now().time()
            market_open = time(9, 30)  # 台股開盤時間
            range_end = time(10, 0)    # 開盤區間結束時間
            market_close = time(13, 30)  # 台股收盤時間

            # 簡化的時間檢查
            return True, "交易時間內（模擬）"

        except Exception as e:
            return False, f"市場時間檢查失敗: {str(e)}"

    def calculate_risk_reward(self, df):
        """計算風險報酬比"""
        try:
            intraday_data = self.simulate_intraday_data(df)

            if intraday_data is None:
                return False, "無法計算風險報酬比"

            current_price = intraday_data['current_price']

            # 計算潛在獲利和風險
            profit_target_price = current_price * (1 + self.profit_target)
            stop_loss_price = current_price * (1 - self.stop_loss)

            potential_profit = profit_target_price - current_price
            potential_loss = current_price - stop_loss_price

            risk_reward_ratio = potential_profit / potential_loss if potential_loss > 0 else 0

            if risk_reward_ratio >= 2.0:  # 至少2:1的風險報酬比
                return True, f"風險報酬比良好 {risk_reward_ratio:.1f}:1 >= 2:1"
            else:
                return False, f"風險報酬比不佳 {risk_reward_ratio:.1f}:1 < 2:1"

        except Exception as e:
            return False, f"風險報酬比計算失敗: {str(e)}"

    def analyze_stock(self, df, **kwargs):
        """分析單一股票是否符合日內開盤區間策略"""
        try:
            if df.empty or len(df) < 20:
                return {
                    'suitable': False,
                    'reason': '數據不足，需要至少20日數據',
                    'details': {},
                    'score': 0,
                    'strategy_name': self.name
                }

            results = {}
            total_score = 0
            max_score = 4  # 四個主要條件

            # 1. 開盤區間突破
            breakout_pass, breakout_reason = self.check_opening_range_breakout(df)
            results['開盤區間突破'] = {'pass': breakout_pass, 'reason': breakout_reason}
            if breakout_pass:
                total_score += 1

            # 2. 成交量確認
            volume_pass, volume_reason = self.check_volume_confirmation(df)
            results['成交量確認'] = {'pass': volume_pass, 'reason': volume_reason}
            if volume_pass:
                total_score += 1

            # 3. 市場時間
            timing_pass, timing_reason = self.check_market_timing()
            results['市場時間'] = {'pass': timing_pass, 'reason': timing_reason}
            if timing_pass:
                total_score += 1

            # 4. 風險報酬比
            risk_reward_pass, risk_reward_reason = self.calculate_risk_reward(df)
            results['風險報酬比'] = {'pass': risk_reward_pass, 'reason': risk_reward_reason}
            if risk_reward_pass:
                total_score += 1

            # 判斷是否符合條件（日內策略要求較嚴格）
            suitable = breakout_pass and volume_pass and total_score >= 3

            # 生成總結
            passed_items = [k for k, v in results.items() if v['pass']]
            reason = f"日內策略評分: {total_score}/{max_score} (通過: {','.join(passed_items)})"

            if not breakout_pass:
                reason += " | ⚠️未發生開盤區間突破"

            # 添加日內交易警告
            reason += " | 📊 注意：此為日內交易策略，需要分鐘級數據和即時監控"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'strategy_name': self.name
            }

        except Exception as e:
            logging.error(f"日內開盤區間策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }