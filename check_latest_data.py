#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查最新資料是否已存入資料庫
"""

import sqlite3
import pandas as pd

def check_latest_data():
    """檢查最新資料"""
    
    print("=" * 60)
    print("📊 檢查資料庫最新資料")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    conn = sqlite3.connect(newprice_db)
    
    # 檢查最新日期
    query_latest = '''
        SELECT MAX(date) as latest_date, COUNT(*) as total_records
        FROM stock_daily_data
    '''
    
    result = pd.read_sql_query(query_latest, conn)
    latest_date = result.iloc[0]['latest_date']
    total_records = result.iloc[0]['total_records']
    
    print(f"📅 資料庫最新日期: {latest_date}")
    print(f"📊 總記錄數: {total_records}")
    
    # 檢查最近幾天的資料量
    query_recent = '''
        SELECT date, COUNT(*) as record_count
        FROM stock_daily_data
        WHERE date >= '2022-09-01'
        GROUP BY date
        ORDER BY date DESC
        LIMIT 15
    '''
    
    df_recent = pd.read_sql_query(query_recent, conn)
    
    print(f"\n📋 最近15天的資料量:")
    for _, row in df_recent.iterrows():
        print(f"   {row['date']}: {row['record_count']} 筆記錄")
    
    # 檢查 0050 最新資料
    query_0050 = '''
        SELECT date, [Close], Volume, [Transaction], TradeValue, stock_name, listing_status, industry
        FROM stock_daily_data 
        WHERE stock_id = '0050'
        ORDER BY date DESC
        LIMIT 10
    '''
    
    df_0050 = pd.read_sql_query(query_0050, conn)
    
    print(f"\n📈 0050 最新10筆資料:")
    for _, row in df_0050.iterrows():
        close_val = row['Close'] if pd.notna(row['Close']) else 'NULL'
        volume_val = row['Volume'] if pd.notna(row['Volume']) else 'NULL'
        print(f"   {row['date']}: 收盤={close_val}, 成交量={volume_val}")
    
    # 檢查是否有新增的日期
    target_dates = ['2022-09-08', '2022-09-09', '2022-09-12', '2022-09-13', '2022-09-14']
    
    print(f"\n🔍 檢查目標日期是否存在:")
    for target_date in target_dates:
        query_check = '''
            SELECT COUNT(*) as count
            FROM stock_daily_data
            WHERE date = ?
        '''
        result = pd.read_sql_query(query_check, conn, params=[target_date])
        count = result.iloc[0]['count']
        status = "✅ 存在" if count > 0 else "❌ 不存在"
        print(f"   {target_date}: {status} ({count} 筆)")
    
    conn.close()

if __name__ == "__main__":
    check_latest_data()
