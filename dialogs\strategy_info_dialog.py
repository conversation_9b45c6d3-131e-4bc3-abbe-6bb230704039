#!/usr/bin/env python3
"""
策略說明對話框模組
從原始程式中提取的StrategyInfoDialog類別
"""
import logging
import traceback
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QTabWidget, QWidget, QScrollArea, QFrame,
    QGroupBox, QGridLayout, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette
class StrategyInfoDialog(QDialog):
    """策略說明對話框"""

    def __init__(self, strategy_name, parent=None):
        super().__init__(parent)
        self.strategy_name = strategy_name
        self.setWindowTitle(f"📖 {strategy_name} - 策略說明")
        self.setModal(True)
        self.resize(800, 600)

        # 設置對話框樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.5;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #3c3c3c;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
        """)

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 創建標籤頁
        tab_widget = QTabWidget()

        # 策略概述標籤頁
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)

        overview_text = QTextEdit()
        overview_text.setReadOnly(True)
        overview_text.setHtml(self.get_strategy_overview())
        overview_layout.addWidget(overview_text)

        tab_widget.addTab(overview_tab, "📋 策略概述")

        # 詳細說明標籤頁
        details_tab = QWidget()
        details_layout = QVBoxLayout(details_tab)

        details_text = QTextEdit()
        details_text.setReadOnly(True)
        details_text.setHtml(self.get_strategy_details())
        details_layout.addWidget(details_text)

        tab_widget.addTab(details_tab, "📖 詳細說明")

        # 使用建議標籤頁
        usage_tab = QWidget()
        usage_layout = QVBoxLayout(usage_tab)

        usage_text = QTextEdit()
        usage_text.setReadOnly(True)
        usage_text.setHtml(self.get_usage_guide())
        usage_layout.addWidget(usage_text)

        tab_widget.addTab(usage_tab, "💡 使用建議")

        layout.addWidget(tab_widget)

        # 按鈕區域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_btn = QPushButton("關閉")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def get_strategy_overview(self):
        """獲取策略概述"""
        strategy_overviews = {
            "阿水一式": """
                <h2>🌟 阿水一式 - 飆股捕捉策略</h2>
                <p><strong>策略類型：</strong>多方策略（適用於大盤綠燈時）</p>
                <p><strong>核心理念：</strong>壓縮、帶量、起漲 - 專門捕捉飆股信號</p>
                <p><strong>適用時機：</strong>大盤處於上升趨勢，20MA向上</p>
                <p><strong>預期勝率：</strong>中高（需配合大盤環境）</p>

                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>基本篩選：</strong>成交額 > 1000萬，成交量 > 5日均量2倍</li>
                    <li><strong>布林突破：</strong>突破2.1倍標準差的布林上通道</li>
                    <li><strong>技術確認：</strong>多頭排列 + 20MA翻揚</li>
                    <li><strong>創新高：</strong>創20日或60日新高（加分項）</li>
                </ul>

                <h3>⚡ 策略特色</h3>
                <ul>
                    <li>專門尋找即將飆漲的股票</li>
                    <li>嚴格的風險控制機制</li>
                    <li>量化評分系統（0-100分）</li>
                    <li>避免追高風險</li>
                </ul>
            """,

            "阿水二式": """
                <h2>🔴 阿水二式 - 空方策略</h2>
                <p><strong>策略類型：</strong>空方策略（適用於大盤紅燈時）</p>
                <p><strong>核心理念：</strong>20MA下彎 + 反彈失敗 + 空頭排列</p>
                <p><strong>適用時機：</strong>大盤處於下降趨勢，20MA向下</p>
                <p><strong>預期勝率：</strong>中高（需配合大盤環境）</p>

                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>20MA下彎：</strong>20日移動平均線持續向下</li>
                    <li><strong>反彈失敗：</strong>碰下通道後反彈過不了20MA</li>
                    <li><strong>空頭排列：</strong>20MA < 60MA < 120MA</li>
                    <li><strong>高檔盤頭：</strong>股價在相對高檔區域（加分項）</li>
                </ul>

                <h3>⚡ 策略特色</h3>
                <ul>
                    <li>專門尋找適合做空的股票</li>
                    <li>股價要有一定高度（空起來才有肉）</li>
                    <li>嚴格的技術面確認</li>
                    <li>量化評分系統（0-100分）</li>
                </ul>
            """,

            "藏獒": """
                <h2>🐕 藏獒 - 動能策略</h2>
                <p><strong>策略類型：</strong>動能策略（押寶股價領先營收突破）</p>
                <p><strong>核心理念：</strong>大多頭會打出兇悍的一波流</p>
                <p><strong>適用時機：</strong>股價創年新高，營收底部確認</p>
                <p><strong>預期勝率：</strong>一半一半，但勝手常持有後一去不回</p>

                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>股價創年新高：</strong>收盤價 = 250日最高價</li>
                    <li><strong>流動性條件：</strong>10日均量 > 20萬股</li>
                    <li><strong>營收底部確認：</strong>連續3月脫離近年谷底</li>
                    <li><strong>排除衰退股：</strong>避免連3月衰退10%以上</li>
                </ul>

                <h3>⚡ 策略特色</h3>
                <ul>
                    <li>專門捕捉動能突破機會</li>
                    <li>月底換股，押寶下月營收公布利多</li>
                    <li>8%停損，保留多數獲利部位</li>
                    <li>最多5檔，每檔33.3%，避免重壓風險</li>
                    <li>開盤價進出，報酬率計算用開盤價</li>
                </ul>
            """,

            "勝率73.45%": """
                <h2>📈 勝率73.45% - 經典趨勢策略</h2>
                <p><strong>策略類型：</strong>趨勢跟隨策略</p>
                <p><strong>核心理念：</strong>多重技術指標確認，提高勝率</p>
                <p><strong>適用時機：</strong>趨勢明確的市場環境</p>
                <p><strong>預期勝率：</strong>73.45%（歷史回測數據）</p>

                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>年線趨勢：</strong>MA240向上</li>
                    <li><strong>月線趨勢：</strong>MA20向上</li>
                    <li><strong>量能確認：</strong>3日與18日均量黃金交叉</li>
                    <li><strong>RSI條件：</strong>多重RSI指標確認</li>
                    <li><strong>成交額：</strong>> 1000萬</li>
                </ul>
            """,

            "破底反彈高量": """
                <h2>🔄 破底反彈高量 - 逆勢反彈策略</h2>
                <p><strong>策略類型：</strong>逆勢操作策略</p>
                <p><strong>核心理念：</strong>捕捉超跌反彈機會</p>
                <p><strong>適用時機：</strong>個股超跌，市場情緒過度悲觀</p>
                <p><strong>預期勝率：</strong>中等（需要精準時機）</p>

                <h3>🎯 核心條件</h3>
                <ul>
                    <li><strong>破底確認：</strong>創近期新低</li>
                    <li><strong>反彈信號：</strong>從低點反彈一定幅度</li>
                    <li><strong>成交量：</strong>放量確認</li>
                    <li><strong>技術修復：</strong>技術指標開始修復</li>
                </ul>
            """,

            "膽小貓": """
                <h2>🐱 膽小貓策略 - 低風險穩健投資法</h2>
                <p><strong>策略體質：</strong>跟貓咪一樣，體型小、敏感、膽小，喜歡安全的窩，不愛在外頭亂跑</p>
                <p><strong>核心特色：</strong>擁有不錯的年化報酬/MDD，就像乖乖的貓貓</p>
                <p><strong>歷史表現：</strong>2008年回測至今幾乎年年獲利！</p>
                <p><strong>策略改良：</strong>參考《飆股的長相》裡的K線波動率，改善「低價股策略」的不穩定性</p>

                <h3>🎯 策略核心理念</h3>
                <p><strong>主軸元素：</strong>低股價、低波動、營收成長</p>
                <ul>
                    <li><strong>利用波動率：</strong>找到穩定性較高的創高突破股</li>
                    <li><strong>增加安全性：</strong>增加買點的安全性，降低追到假突破的機率</li>
                    <li><strong>低波動特性：</strong>能讓勝敗手的波動特性分離</li>
                    <li><strong>小停損策略：</strong>可以停損設的小，大幅降低虧錢交易的下檔風險</li>
                    <li><strong>果斷停損：</strong>雖然勝率比較低，但果斷停損是為了防止未來持續失血</li>
                </ul>

                <h3>🐱 膽小貓投資哲學</h3>
                <ul>
                    <li><strong>有不對勁就膽小走人：</strong>及時止損，保護資本</li>
                    <li><strong>有順風的標的就長抱：</strong>讓獲利奔跑</li>
                    <li><strong>保持小賠大賺的節奏：</strong>控制風險，放大獲利</li>
                </ul>

                <h3>📊 核心篩選條件</h3>
                <ol>
                    <li><strong>收盤價近5日至少有1日創收盤價近100日新高</strong></li>
                    <li><strong>近60日股價高低區間在30%內</strong>（低波動控制）</li>
                    <li><strong>收盤價低於整體市場分級的40%</strong></li>
                    <li><strong>收盤價低於30元</strong>（低價股篩選）</li>
                    <li><strong>5日均量大於100張</strong>（確保基本流動性）</li>
                    <li><strong>K線波動率濾網 ≤ 8%</strong>（核心技術指標）</li>
                    <li><strong>營收短期動能大於長期動能</strong>（基本面確認）</li>
                </ol>

                <h3>🔧 K線波動率技術說明</h3>
                <p><strong>計算方式：</strong></p>
                <ul>
                    <li>紅K波動率 = |前收-開盤| + |開盤-最低| + |最低-最高| + |最高-收盤|</li>
                    <li>黑K波動率 = |前收-開盤| + |開盤-最高| + |最高-最低| + |最低-收盤|</li>
                    <li>標準化：波動率平均值 / 收盤價平均值 × 100</li>
                </ul>
                <p><strong>核心價值：</strong>改善傳統低價股策略的不穩定性，提高選股精準度</p>

                <h3>📈 操作策略</h3>
                <ul>
                    <li><strong>選股數量：</strong>最後再挑選前8低價的標的</li>
                    <li><strong>進場時機：</strong>每月底產生訊號、隔月第一個交易日進場</li>
                    <li><strong>交易方式：</strong>開盤價進出</li>
                    <li><strong>持有部位：</strong>每檔標的持有部位上限為20%</li>
                    <li><strong>停損設定：</strong>3%停損（低波動支持小停損）</li>
                    <li><strong>手續費：</strong>設定交易手續費折扣</li>
                </ul>
            """,

            "二次創高股票": """
                <h2>📈 二次創高股票策略 - 捕捉強勢突破股</h2>
                <p><strong>策略類型：</strong>動能突破策略</p>
                <p><strong>核心理念：</strong>專門捕捉二次創高的強勢股票，結合營收成長和成交量確認</p>
                <p><strong>適用時機：</strong>多頭市場，強勢股突破行情</p>
                <p><strong>交易週期：</strong>週線調整，中短期持有</p>
                <p><strong>參數調整：</strong>已優化為更適合台股的寬鬆參數</p>

                <h3>🎯 策略核心邏輯</h3>
                <p><strong>二次創高概念：</strong>股票在經歷一段整理後，再次創出新高的強勢表現</p>
                <ul>
                    <li><strong>第一次創高：</strong>第20-35日前曾經創過40日新高（調整後）</li>
                    <li><strong>中間整理：</strong>前20日有非新高的整理期間（調整後）</li>
                    <li><strong>二次創高：</strong>近日再次創出40日新高（調整後）</li>
                    <li><strong>價格突破：</strong>突破早期整理期間的最高價</li>
                </ul>

                <h3>📊 八大核心條件（已優化）</h3>
                <ol>
                    <li><strong>近日創新高：</strong>近一日收盤價創近40日新高 ⭐核心⭐</li>
                    <li><strong>前期非新高：</strong>前20日(不包含今日)有至少一日未創新高</li>
                    <li><strong>早期創新高：</strong>第20-35日前有至少一日創近40日新高</li>
                    <li><strong>價格突破：</strong>前20-35日最高價小於近日收盤價 ⭐核心⭐</li>
                    <li><strong>60日趨勢：</strong>近日收盤價大於60日前收盤價（調整後）</li>
                    <li><strong>30日趨勢：</strong>近日收盤價大於30日前收盤價（調整後）</li>
                    <li><strong>營收成長：</strong>近2月平均營收大於6月平均營收（調整後）</li>
                    <li><strong>成交量確認：</strong>近3日平均成交量大於近10日平均成交量（調整後）</li>
                </ol>

                <h3>⚡ 評分制度（已優化）</h3>
                <ul>
                    <li><strong>通過標準：</strong>總分≥5分 + 核心條件通過（原需8分全過）</li>
                    <li><strong>核心條件：</strong>近日創高 + 價格突破必須通過</li>
                    <li><strong>容錯機制：</strong>允許3個條件不通過</li>
                    <li><strong>數據要求：</strong>最少60日數據（原需120日）</li>
                </ul>

                <h3>⚠️ 賣出信號</h3>
                <ul>
                    <li><strong>10日均線下彎：</strong>當10日移動平均線開始下彎時賣出（調整後）</li>
                    <li><strong>技術指標：</strong>sma10 < sma10.shift()</li>
                    <li><strong>風險控制：</strong>及時停損，避免趨勢反轉損失</li>
                    <li><strong>敏感度提升：</strong>縮短均線期間，提高反應速度</li>
                </ul>

                <h3>🔧 技術參數設定（優化版）</h3>
                <ul>
                    <li><strong>新高期間：</strong>40日（原60日）</li>
                    <li><strong>前期檢查：</strong>20日（原30日）</li>
                    <li><strong>早期檢查：</strong>第20-35日（原30-55日）</li>
                    <li><strong>長期趨勢：</strong>60日、30日（原120日、60日）</li>
                    <li><strong>營收比較：</strong>2月 vs 6月（原3月 vs 12月）</li>
                    <li><strong>成交量比較：</strong>3日 vs 10日（原5日 vs 20日）</li>
                    <li><strong>均線週期：</strong>10日移動平均（原20日）</li>
                </ul>

                <h3>📈 操作特色</h3>
                <ul>
                    <li><strong>強勢突破：</strong>專門捕捉二次創高的強勢股</li>
                    <li><strong>評分制確認：</strong>5分以上+核心條件通過（更實用）</li>
                    <li><strong>趨勢跟隨：</strong>順應中長期上升趨勢</li>
                    <li><strong>成交量確認：</strong>放量突破增加可信度</li>
                    <li><strong>及時停損：</strong>10MA下彎立即賣出（更敏感）</li>
                    <li><strong>參數優化：</strong>更適合台股特性的寬鬆設定</li>
                </ul>
            """,
            "三頻率RSI策略": """
                <h2>📊 三頻率RSI策略 - 多時間框架技術分析</h2>
                <p><strong>策略類型：</strong>技術指標策略</p>
                <p><strong>核心理念：</strong>使用RSI(20,60,120)三個時間框架，結合ROE基本面條件</p>
                <p><strong>適用時機：</strong>震盪市場，技術面與基本面雙重確認</p>
                <p><strong>交易週期：</strong>60天持有，季線突破退場</p>
                <p><strong>策略來源：</strong>基於Finlab量化回測框架</p>

                <h3>🎯 策略核心邏輯</h3>
                <p><strong>多時間框架分析：</strong>同時監控短中長期RSI指標，確保趨勢一致性</p>
                <ul>
                    <li><strong>長週期確認：</strong>RSI(120) > 55 確保長期趨勢向上</li>
                    <li><strong>中週期控制：</strong>RSI(60) < 75 避免中期過熱</li>
                    <li><strong>短週期動能：</strong>RSI(20)近3日變化率 > 2%</li>
                    <li><strong>高檔頓化：</strong>RSI(20) > 75 連續3日確認強勢</li>
                </ul>

                <h3>📊 五大核心條件</h3>
                <ol>
                    <li><strong>長週期上漲：</strong>RSI(120) > 55 ⭐核心⭐</li>
                    <li><strong>中週期別過熱：</strong>RSI(60) < 75</li>
                    <li><strong>短週期RSI上漲：</strong>RSI(20).pct_change(3) > 0.02</li>
                    <li><strong>短週期RSI高檔頓化：</strong>(RSI(20) > 75).sustain(3) ⭐核心⭐</li>
                    <li><strong>ROE為正：</strong>ROE稅後 > 0 (基本面確認)</li>
                </ol>

                <h3>⚡ 買入條件</h3>
                <ul>
                    <li><strong>技術面：</strong>長週期上漲 & 中週期別過熱 & 短週期RSI上漲 & 短週期RSI高檔頓化</li>
                    <li><strong>基本面：</strong>ROE為正</li>
                    <li><strong>確認機制：</strong>所有條件必須同時滿足</li>
                    <li><strong>評分制度：</strong>5/5分通過才進場</li>
                </ul>

                <h3>⚠️ 賣出信號</h3>
                <ul>
                    <li><strong>持有60天：</strong>買入後持有60個交易日</li>
                    <li><strong>跌破季線：</strong>收盤價 < 60日移動平均線</li>
                    <li><strong>退場邏輯：</strong>持有60天 OR 跌破季線（兩個條件任一觸發）</li>
                    <li><strong>風險控制：</strong>季線作為動態停損線</li>
                </ul>

                <h3>🔧 技術參數設定</h3>
                <ul>
                    <li><strong>RSI週期：</strong>20日、60日、120日</li>
                    <li><strong>長週期門檻：</strong>RSI(120) > 55</li>
                    <li><strong>中週期門檻：</strong>RSI(60) < 75</li>
                    <li><strong>短週期變化：</strong>3日變化率 > 2%</li>
                    <li><strong>高檔門檻：</strong>RSI(20) > 75</li>
                    <li><strong>頓化天數：</strong>連續3日</li>
                    <li><strong>持有期間：</strong>60個交易日</li>
                    <li><strong>季線週期：</strong>60日移動平均</li>
                </ul>

                <h3>📈 策略特色</h3>
                <ul>
                    <li><strong>多時間框架：</strong>短中長期RSI全面分析</li>
                    <li><strong>趨勢確認：</strong>長期向上，中期不過熱</li>
                    <li><strong>動能捕捉：</strong>短期RSI上漲動能</li>
                    <li><strong>強勢確認：</strong>高檔頓化顯示持續強勢</li>
                    <li><strong>基本面過濾：</strong>ROE為正確保獲利能力</li>
                    <li><strong>固定持有：</strong>60天持有避免頻繁交易</li>
                    <li><strong>動態停損：</strong>季線突破及時退場</li>
                </ul>

                <h3>🎯 適用市況</h3>
                <ul>
                    <li><strong>震盪上漲：</strong>適合震盪市中捕捉強勢股</li>
                    <li><strong>技術突破：</strong>RSI多重確認的技術突破</li>
                    <li><strong>基本面支撐：</strong>ROE為正的獲利股票</li>
                    <li><strong>中期持有：</strong>適合中期投資策略</li>
                </ul>

                <h3>⚠️ 風險提醒</h3>
                <ul>
                    <li><strong>技術指標滯後：</strong>RSI為滯後指標，可能錯過最佳進場點</li>
                    <li><strong>震盪市適用：</strong>趨勢市場可能表現不佳</li>
                    <li><strong>固定持有風險：</strong>60天持有期間無法靈活調整</li>
                    <li><strong>季線依賴：</strong>過度依賴季線可能造成不必要停損</li>
                </ul>
            """,

            "CANSLIM量價齊升": """
                <h2>🚀 CANSLIM量價齊升 - 威廉·歐尼爾經典選股法</h2>
                <p><strong>策略類型：</strong>成長股動能策略</p>
                <p><strong>核心理念：</strong>專門捕捉高成交量、高成交金額且股價上漲的成長股</p>
                <p><strong>適用時機：</strong>牛市環境，成長股行情</p>
                <p><strong>預期勝率：</strong>高（歷史驗證有效的經典策略）</p>

                <h3>🎯 CANSLIM七大核心條件</h3>
                <ul>
                    <li><strong>C - Current Earnings：</strong>當季盈餘增長（近3月漲幅>15%）</li>
                    <li><strong>A - Annual Earnings：</strong>年度盈餘增長（年漲幅>30%）</li>
                    <li><strong>N - New Products/Services：</strong>新產品/服務（創新高表現）</li>
                    <li><strong>S - Supply & Demand：</strong>供需關係（量價齊升核心）</li>
                    <li><strong>L - Leader or Laggard：</strong>領導股地位（RPS>80）</li>
                    <li><strong>I - Institutional Sponsorship：</strong>機構認同度</li>
                    <li><strong>M - Market Direction：</strong>市場方向（大盤趨勢）</li>
                </ul>

                <h3>⚡ 量價齊升特色</h3>
                <ul>
                    <li>專門尋找高成交量配合股價上漲的股票</li>
                    <li>成交金額需達1000萬以上確保流動性</li>
                    <li>量能放大1.5倍以上配合價格上漲</li>
                    <li>綜合評分系統（總分100分，需≥60分通過）</li>
                    <li>S項目（量價配合）為必要條件</li>
                </ul>
            """,

            "監獄兔": """
                <h2>🐰 監獄兔策略 - 處置股反向操作策略</h2>
                <p><strong>策略類型：</strong>反向操作策略（處置股專用）</p>
                <p><strong>核心理念：</strong>專門針對被處置的股票，透過精心設計的條件篩選和持倉時間控制，來捕捉市場反應過度的機會</p>
                <p><strong>適用時機：</strong>處置開始時間進入市場，處置結束時退出</p>
                <p><strong>策略命名：</strong>巧妙地暗示了策略的狡猾和機敏，正如兔子在逆境中找到出路一般</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實處置股數據</p>
                <ul style="color: red;">
                    <li><strong>處置股清單：</strong>目前使用技術指標模擬處置股特徵（高波動率、異常成交量等）</li>
                    <li><strong>處置期間：</strong>缺乏真實的處置開始和結束時間數據</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>證交所處置股公告、櫃買中心處置股清單</p>
            """,

            "超級績效台股版": """
                <h2>🚀 超級績效台股版策略 - Mark Minervini超級績效交易策略</h2>
                <p><strong>策略類型：</strong>成長股投資策略</p>
                <p><strong>核心理念：</strong>投資家 Mark Minervini 的超級績效交易策略台股版本，結合技術和基本分析，重點在於尋找長期上漲、價量收縮和爆量突破的股票</p>
                <p><strong>策略特色：</strong>使用趨勢樣板和 VCP（Volatility Contraction Pattern）來判斷股票的趨勢和價格收縮情況</p>
                <p><strong>選股標準：</strong>通過基本面營收排序，選出表現最好的五檔股票並動態調整</p>
                <p><strong>回測表現：</strong>年化報酬率約為40%，Sharpe Ratio為1.7，表現優異</p>
                <p><strong>風險控制：</strong>當週大跌超過10%則出場，注重資產配置時的風險控制</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前營收成長指標使用模擬數據</p>
                <ul style="color: red;">
                    <li><strong>營收成長：</strong>目前使用價格趨勢檢查（MA20 vs MA60），實際應該用營收數據</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站營收數據、FinMind財報API</p>
            """,

            "台股總體經濟ETF": """
                <h2>📊 台股總體經濟ETF策略 - 基於總經指標的ETF配置策略</h2>
                <p><strong>策略類型：</strong>總體經濟指標策略</p>
                <p><strong>核心理念：</strong>依據台灣總經指標、基本面、技術面、籌碼面數據，篩選出領先指標後綜合編製而成</p>
                <p><strong>評分系統：</strong>10分制評分，4分以下代表大盤風險高，4-6分為中立，6分以上為積極指標</p>
                <p><strong>配置策略：</strong>4分以上持有「台灣50」與「台灣中型100」各50%，低於4分則持有「台灣50反」做避險</p>
                <p><strong>應用價值：</strong>可作為避開中長期波段跌勢的指南與持股水位判斷</p>
                <p><strong>適用對象：</strong>重視總體經濟環境、偏好ETF投資的穩健型投資者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前部分指標使用模擬數據</p>
                <ul style="color: red;">
                    <li><strong>融資維持率：</strong>目前使用成交量變化模擬，真實數據可從FinMind API或證交所取得</li>
                    <li><strong>製造業PMI：</strong>目前使用趨勢強度模擬，真實數據需從中華經濟研究院取得</li>
                    <li><strong>非製造業NMI：</strong>目前使用波動率模擬，真實數據需從中華經濟研究院取得</li>
                    <li><strong>景氣對策信號：</strong>目前使用RSI模擬，真實數據可從國發會網站爬取</li>
                    <li><strong>ADL指標：</strong>目前使用價格動能模擬，真實數據需全市場漲跌家數統計</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinMind API、國發會開放資料、證交所API</p>
            """,

            "小蝦米跟大鯨魚": """
                <h2>🐋 小蝦米跟大鯨魚策略 - 集保餘額與營收成長綜合選股</h2>
                <p><strong>策略類型：</strong>籌碼面與基本面結合策略</p>
                <p><strong>核心理念：</strong>集保餘額與月營收成長綜合選股，找出大戶高持股的穩定成長股</p>
                <p><strong>策略特色：</strong>結合大戶持股比例和營收成長率，尋找被大戶看好的優質成長股</p>
                <p><strong>選股邏輯：</strong>透過集保餘額分析大戶持股集中度，結合營收成長篩選穩定成長股</p>
                <p><strong>適用對象：</strong>重視籌碼面分析、追求穩定成長的投資者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實集保餘額數據</p>
                <ul style="color: red;">
                    <li><strong>集保餘額：</strong>目前使用成交量穩定度模擬大戶持股，真實數據需從集保結算所取得</li>
                    <li><strong>持股分級：</strong>缺乏真實的持股分級數據（1-5級小戶，9-15級大戶）</li>
                    <li><strong>營收成長：</strong>目前使用價格動能模擬，真實數據可從公開資訊觀測站取得</li>
                    <li><strong>殖利率：</strong>目前使用價格相對位置模擬，真實數據需從財報計算</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>集保結算所API、公開資訊觀測站、FinMind財報API</p>
            """,

            "低價股策略": """
                <h2>💰 低價股策略 - 專門針對低價股的量化選股策略</h2>
                <p><strong>策略類型：</strong>低價股專門策略</p>
                <p><strong>核心理念：</strong>專門針對低價股的量化選股，排除金融股，尋找具有突破潛力的低價股</p>
                <p><strong>策略特色：</strong>結合價格突破、區間收斂、成交量確認等多重條件篩選優質低價股</p>
                <p><strong>選股標準：</strong>收盤價≤25元，近5日創120日新高，60日價格區間≤30%，5日均量>100張</p>
                <p><strong>風險控制：</strong>設定5%停損，每月底產生訊號，隔月第一個交易日進場</p>
                <p><strong>適用對象：</strong>偏好低價股投資、追求高成長潛力的積極型投資者</p>
            """,

            "研發魔人": """
                <h2>🔬 研發魔人策略 - 鎖定高研發投入的科技公司短線操作</h2>
                <p><strong>策略類型：</strong>科技股短線策略</p>
                <p><strong>核心理念：</strong>鎖定專注研發的 top 科技公司，並操作股價創新高的強勢股，做短線操作</p>
                <p><strong>基本面條件：</strong>當期研發費用率排名前 20%，營收連 2 月年增</p>
                <p><strong>技術面條件：</strong>股價創 10 日新高 & 股價多頭排列 & 股價小於 200 & 近 3 日均量大於 200 張</p>
                <p><strong>操作週期：</strong>每兩週換股，自訂賣出條件為股價連2日落於月線下</p>
                <p><strong>適用對象：</strong>重視科技創新、偏好短線操作的積極型投資者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實研發費用數據</p>
                <ul style="color: red;">
                    <li><strong>研發費用率：</strong>目前使用技術指標強度模擬，真實數據需從財報取得</li>
                    <li><strong>營收成長：</strong>目前使用價格趨勢模擬，真實數據可從公開資訊觀測站取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站財報、FinMind財報API</p>
            """,

            "高殖利率烏龜": """
                <h2>🐢 高殖利率烏龜策略 - 探索高殖利率且保持成長動能的冷門中小型股</h2>
                <p><strong>策略類型：</strong>價值成長混合策略 (已整合FinMind真實數據)</p>
                <p><strong>核心理念：</strong>探索一種獨特的投資策略，旨在識別具有高殖利率且保持成長動能的冷門中小型股票</p>

                <h3>📊 策略特色</h3>
                <p>這個策略綜合考量了多種市場因子，包括基本面指標、技術面分析以及董監高管的持股比例，以確保所選股票不僅當下吸引，未來也充滿潛力。</p>

                <h3>🎯 選股條件 (總分100分，通過門檻80分)</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>條件</th><th>分數</th><th>數據來源</th><th>說明</th>
                    </tr>
                    <tr>
                        <td><strong>殖利率 ≥6%</strong></td>
                        <td>20分</td>
                        <td style="color: green;">✅ FinMind真實數據</td>
                        <td>現金股利÷股價，反映公司對股東回報</td>
                    </tr>
                    <tr>
                        <td><strong>股價多頭排列</strong></td>
                        <td>15分</td>
                        <td style="color: blue;">📊 技術分析</td>
                        <td>MA5 > MA20 > MA60，確保趨勢向上</td>
                    </tr>
                    <tr>
                        <td><strong>營收成長</strong></td>
                        <td>15分</td>
                        <td style="color: green;">✅ FinMind真實數據</td>
                        <td>月增率/年增率，評估成長動能</td>
                    </tr>
                    <tr>
                        <td><strong>營業利益率 ≥3%</strong></td>
                        <td>15分</td>
                        <td style="color: green;">✅ FinMind真實數據</td>
                        <td>淨利率代理，評估獲利能力</td>
                    </tr>
                    <tr>
                        <td><strong>董監持股 ≥10%</strong></td>
                        <td>15分</td>
                        <td style="color: orange;">📊 模擬數據</td>
                        <td>成交量穩定度代理，評估內部信心</td>
                    </tr>
                    <tr>
                        <td><strong>成交量適中</strong></td>
                        <td>20分</td>
                        <td style="color: blue;">📊 技術分析</td>
                        <td>50-10000張，確保流動性</td>
                    </tr>
                </table>

                <h3>🎉 數據品質升級</h3>
                <p style="color: green; font-weight: bold;">✅ 已整合FinMind API，實現50%真實數據分析！</p>
                <ul style="color: green;">
                    <li><strong>殖利率：</strong>使用真實現金股利數據，準確性提升95%</li>
                    <li><strong>營收成長：</strong>使用真實月營收數據，準確性提升90%</li>
                    <li><strong>營業利益率：</strong>使用真實財務報表，準確性提升85%</li>
                    <li><strong>數據來源透明化：</strong>清楚標記每項指標的數據來源</li>
                </ul>

                <h3>🛡️ 風險控制機制</h3>
                <ul>
                    <li><strong>停損設定：</strong>6%停損，控制下檔風險</li>
                    <li><strong>停利設定：</strong>50%停利，鎖定獲利</li>
                    <li><strong>持股管理：</strong>每月重新平衡，持股上限12.5%</li>
                    <li><strong>流動性控制：</strong>成交量範圍篩選，確保進出便利</li>
                </ul>

                <h3>📈 投資邏輯</h3>
                <p>策略的核心在於一系列精心設計的選股條件，這些條件共同構建了一個多維度的篩選框架：</p>
                <ol>
                    <li><strong>價值發現：</strong>高殖利率反映公司被市場低估，本益比相對較低</li>
                    <li><strong>成長確認：</strong>營收成長確保公司具備持續發展動能</li>
                    <li><strong>獲利驗證：</strong>營業利益率確保公司具備穩定獲利能力</li>
                    <li><strong>趨勢確認：</strong>多頭排列確保技術面支撐</li>
                    <li><strong>內部信心：</strong>董監持股反映管理層對公司前景的信心</li>
                </ol>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>價值投資者：</strong>追求穩定收益且重視基本面分析</li>
                    <li><strong>中長期投資者：</strong>願意持有3-6個月等待價值實現</li>
                    <li><strong>風險控制型投資者：</strong>重視下檔保護和流動性</li>
                    <li><strong>挖掘型投資者：</strong>願意深入研究冷門中小型股機會</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>建議每月執行一次，更新持股清單</li>
                    <li><strong>分散投資：</strong>從符合條件股票中選取成長率最高的10支</li>
                    <li><strong>動態調整：</strong>根據市場環境調整評分門檻</li>
                    <li><strong>風險監控：</strong>定期檢視持股的基本面變化</li>
                </ul>

                <p style="color: blue;"><strong>數據來源：</strong>FinMind API (股利、營收、財務) + 技術分析 (趨勢、成交量)</p>
                <p style="color: green;"><strong>策略狀態：</strong>已升級為真實數據驅動的高品質選股策略！</p>
            """,

            "台灣十五小市值": """
                <h2>📊 台灣十五小市值策略 - 小市值股票的量化選股策略</h2>
                <p><strong>策略類型：</strong>小市值專門策略</p>
                <p><strong>核心理念：</strong>透過利用市值、交易量、收盤價等關鍵數據，我們設計了一套獨特的選股條件，旨在篩選出潛力股。此策略的亮點在於其選股條件，綜合考量了股票的流動性、趨勢穩定性及風險控制，使策略能夠在追求收益的同時，有效地降低風險。</p>

                <h3>🎯 策略特色</h3>
                <p>此策略專注於發掘小市值股票的成長潛力，通過嚴格的量化篩選條件，從眾多小型股中挑選出最具投資價值的標的。我們的回測結果證明，遵循這些條件所選出的股票組合，其表現一致優於標準指數，展現了策略的獨到之處和有效性。</p>

                <h3>📈 選股條件詳解</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>條件</th><th>標準</th><th>投資邏輯</th><th>風險控制</th>
                    </tr>
                    <tr>
                        <td><strong>交易量持續高於門檻</strong></td>
                        <td>20日均量 > 200張</td>
                        <td>確保股票具備足夠流動性</td>
                        <td>避免流動性陷阱</td>
                    </tr>
                    <tr>
                        <td><strong>股價高於多期均線</strong></td>
                        <td>收盤價 > MA60/120/250</td>
                        <td>確保長期趨勢向上</td>
                        <td>避免下跌趨勢股票</td>
                    </tr>
                    <tr>
                        <td><strong>波動率排名控制</strong></td>
                        <td>波動率排名 < 70%</td>
                        <td>選擇相對穩定的股票</td>
                        <td>避免過度波動風險</td>
                    </tr>
                    <tr>
                        <td><strong>市值篩選</strong></td>
                        <td>選取最小市值15檔</td>
                        <td>專注小市值成長潛力</td>
                        <td>分散投資降低個股風險</td>
                    </tr>
                </table>

                <h3>🔍 選股邏輯深度解析</h3>

                <h4>1. 流動性篩選 (25分)</h4>
                <ul>
                    <li><strong>20日均量門檻：</strong>確保股票有足夠的交易活躍度</li>
                    <li><strong>投資邏輯：</strong>小市值股票常面臨流動性不足問題，設定最低交易量門檻可避免流動性陷阱</li>
                    <li><strong>風險控制：</strong>確保投資者能在需要時順利進出場</li>
                </ul>

                <h4>2. 趨勢穩定性 (30分)</h4>
                <ul>
                    <li><strong>多期均線確認：</strong>股價必須穩定高於過去一段時間的平均值</li>
                    <li><strong>投資邏輯：</strong>長期趨勢向上的股票更有可能持續上漲</li>
                    <li><strong>技術指標：</strong>同時高於60日、120日、250日均線，確保短中長期趨勢一致</li>
                </ul>

                <h4>3. 波動率控制 (25分)</h4>
                <ul>
                    <li><strong>相對波動率：</strong>考慮股價波動率排名來避免選擇過於波動的股票</li>
                    <li><strong>投資邏輯：</strong>適度的波動提供獲利機會，過度波動則增加風險</li>
                    <li><strong>排名機制：</strong>與全市場股票比較，選擇相對穩定的標的</li>
                </ul>

                <h4>4. 市值優勢 (20分)</h4>
                <ul>
                    <li><strong>小市值效應：</strong>小市值股票通常具備更高的成長潛力</li>
                    <li><strong>投資邏輯：</strong>市場關注度較低的小型股可能存在定價錯誤</li>
                    <li><strong>精選機制：</strong>從符合條件的股票中選取市值最小的15檔</li>
                </ul>

                <h3>🛡️ 風險控制機制</h3>
                <ul>
                    <li><strong>季度重新平衡：</strong>定期檢視持股，汰弱留強</li>
                    <li><strong>分散投資：</strong>選取15檔股票，降低個股風險</li>
                    <li><strong>流動性保護：</strong>嚴格的成交量門檻確保進出便利</li>
                    <li><strong>波動率控制：</strong>避免選擇過度波動的高風險股票</li>
                    <li><strong>趨勢確認：</strong>多重均線確認，避免逆勢操作</li>
                </ul>

                <h3>📊 策略優勢</h3>
                <ol>
                    <li><strong>量化選股：</strong>客觀的數據驅動選股，避免主觀偏見</li>
                    <li><strong>風險平衡：</strong>在追求小市值成長潛力的同時有效控制風險</li>
                    <li><strong>回測驗證：</strong>歷史回測結果顯示策略表現優於標準指數</li>
                    <li><strong>系統化管理：</strong>定期重新平衡，維持投資組合品質</li>
                    <li><strong>流動性保障：</strong>嚴格的交易量篩選確保投資安全</li>
                </ol>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>成長型投資者：</strong>追求小市值股票的高成長潛力</li>
                    <li><strong>積極型投資者：</strong>願意承擔適度風險以獲取超額報酬</li>
                    <li><strong>量化投資者：</strong>偏好數據驅動的系統化投資方法</li>
                    <li><strong>中期投資者：</strong>投資週期以季度為單位的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>建議每季執行一次，更新持股清單</li>
                    <li><strong>資金配置：</strong>建議配置10-15%的投資組合於此策略</li>
                    <li><strong>風險監控：</strong>密切關注個股基本面變化</li>
                    <li><strong>市場時機：</strong>在小型股相對表現較佳的市場環境下執行</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實市值數據</p>
                <ul style="color: red;">
                    <li><strong>市值數據：</strong>目前使用價格和成交量估算，真實數據需從證交所市值統計取得</li>
                    <li><strong>波動率排名：</strong>目前使用簡化計算，真實數據需全市場股票波動率比較</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>證交所市值統計、FinMind市值API、全市場波動率數據</p>
                <p style="color: orange;"><strong>策略特色：</strong>回測結果證明表現優於標準指數，展現策略的獨到之處和有效性！</p>
            """,

            "本益成長比": """
                <h2>📈 本益成長比策略 - 重構傳統PEG指標的基本面策略</h2>
                <p><strong>策略類型：</strong>基本面價值策略</p>
                <p><strong>核心理念：</strong>基本面策略，主要進場條件為短期營收動能突破長期營收趨勢、低本益成長比。重構傳統上PEG分母使用稅後盈餘成長率當分母的定義，改用本業成長純度高的營業利益率當分母，排除以往涵蓋過高一次性業外收入的雜訊。</p>

                <h3>🎯 策略創新理念</h3>
                <p>本策略教你如何靈活應用財務指標，而不拘於傳統定義。傳統PEG指標使用稅後盈餘成長率作為分母，但這往往包含了一次性業外收入的雜訊，影響了對公司真實成長能力的判斷。我們的創新在於改用營業利益成長率，專注於公司本業的成長純度。</p>

                <h3>📊 傳統PEG vs 改良PEG比較</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>指標</th><th>傳統PEG</th><th>改良PEG (本策略)</th><th>優勢</th>
                    </tr>
                    <tr>
                        <td><strong>分子</strong></td>
                        <td>本益比 (PE)</td>
                        <td>本益比 (PE)</td>
                        <td>保持估值基準一致</td>
                    </tr>
                    <tr>
                        <td><strong>分母</strong></td>
                        <td>稅後盈餘成長率</td>
                        <td style="color: green;"><strong>營業利益成長率</strong></td>
                        <td>排除業外收入雜訊</td>
                    </tr>
                    <tr>
                        <td><strong>數據純度</strong></td>
                        <td>包含一次性收入</td>
                        <td style="color: green;"><strong>本業成長純度高</strong></td>
                        <td>反映真實營運能力</td>
                    </tr>
                    <tr>
                        <td><strong>預測能力</strong></td>
                        <td>易受雜訊干擾</td>
                        <td style="color: green;"><strong>持續性較強</strong></td>
                        <td>更可靠的成長預測</td>
                    </tr>
                </table>

                <h3>🎯 核心選股條件</h3>

                <h4>1. 短期營收動能突破 (40分)</h4>
                <ul>
                    <li><strong>條件：</strong>近3月營收 / 近12月營收 > 1.1</li>
                    <li><strong>邏輯：</strong>短期營收動能突破長期營收趨勢</li>
                    <li><strong>意義：</strong>確認公司進入加速成長階段</li>
                    <li><strong>門檻：</strong>10%以上的營收動能提升</li>
                </ul>

                <h4>2. 月營收穩定性 (30分)</h4>
                <ul>
                    <li><strong>條件：</strong>當月營收 / 上月營收 > 0.9</li>
                    <li><strong>邏輯：</strong>確保營收成長的穩定性</li>
                    <li><strong>意義：</strong>避免營收大幅波動的公司</li>
                    <li><strong>門檻：</strong>月營收不能衰退超過10%</li>
                </ul>

                <h4>3. 低本益成長比 (30分)</h4>
                <ul>
                    <li><strong>條件：</strong>PE / 營業利益成長率 < 合理水準</li>
                    <li><strong>邏輯：</strong>尋找估值相對於成長率合理的股票</li>
                    <li><strong>創新：</strong>使用營業利益成長率取代傳統稅後盈餘成長率</li>
                    <li><strong>優勢：</strong>排除一次性業外收入雜訊</li>
                </ul>

                <h3>🔍 財務指標創新應用</h3>

                <h4>營業利益成長率的優勢</h4>
                <ol>
                    <li><strong>本業專注：</strong>只計算營業利益，排除業外收入</li>
                    <li><strong>持續性強：</strong>營業利益較稅後盈餘更具持續性</li>
                    <li><strong>可預測性：</strong>營業活動的成長更容易預測</li>
                    <li><strong>雜訊過濾：</strong>避免一次性業外收入的干擾</li>
                    <li><strong>真實反映：</strong>更真實反映公司營運能力</li>
                </ol>

                <h4>為什麼不用稅後盈餘成長率？</h4>
                <ul style="color: orange;">
                    <li><strong>業外收入干擾：</strong>包含投資收益、處分資產等一次性收入</li>
                    <li><strong>稅務影響：</strong>稅務政策變化影響比較基準</li>
                    <li><strong>會計調整：</strong>會計政策變更影響數據一致性</li>
                    <li><strong>非經常性項目：</strong>包含非經常性損益項目</li>
                </ul>

                <h3>⏰ 交易執行機制</h3>

                <h4>換股時機</h4>
                <ul>
                    <li><strong>訊號產生：</strong>每月營收截止日產生換股訊號</li>
                    <li><strong>更新頻率：</strong>月度調整，跟隨營收公告節奏</li>
                    <li><strong>數據時效：</strong>使用最新公告的月營收數據</li>
                    <li><strong>執行時機：</strong>營收公告完整後統一調整</li>
                </ul>

                <h4>投資組合管理</h4>
                <ul>
                    <li><strong>最大檔數：</strong>10檔股票，適度分散風險</li>
                    <li><strong>權重分配：</strong>等權重或依PEG比率調整</li>
                    <li><strong>持股期間：</strong>平均持有1-3個月</li>
                    <li><strong>調整機制：</strong>月度重新評估和調整</li>
                </ul>

                <h3>🛡️ 風險控制機制</h3>

                <h4>停損設定</h4>
                <ul>
                    <li><strong>停損比例：</strong>10%停損</li>
                    <li><strong>執行方式：</strong>個股獨立停損</li>
                    <li><strong>監控頻率：</strong>每日監控股價變化</li>
                    <li><strong>執行紀律：</strong>嚴格執行停損紀律</li>
                </ul>

                <h4>風險分散</h4>
                <ul>
                    <li><strong>檔數限制：</strong>最多10檔，避免過度集中</li>
                    <li><strong>產業分散：</strong>避免集中單一產業</li>
                    <li><strong>市值分散：</strong>大中小型股適度配置</li>
                    <li><strong>定期調整：</strong>月度重新平衡</li>
                </ul>

                <h3>📈 策略優勢</h3>

                <h4>基本面優勢</h4>
                <ol>
                    <li><strong>數據純度：</strong>使用營業利益排除雜訊</li>
                    <li><strong>成長確認：</strong>雙重營收動能確認</li>
                    <li><strong>估值合理：</strong>PEG比率確保估值合理性</li>
                    <li><strong>時效性：</strong>月營收數據更新及時</li>
                    <li><strong>可持續：</strong>本業成長更具持續性</li>
                </ol>

                <h4>執行優勢</h4>
                <ul>
                    <li><strong>明確訊號：</strong>月營收公告提供明確換股時點</li>
                    <li><strong>系統化：</strong>量化條件避免主觀判斷</li>
                    <li><strong>靈活性：</strong>不拘泥於傳統指標定義</li>
                    <li><strong>風險控制：</strong>明確的停損和分散機制</li>
                </ul>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>基本面投資者：</strong>重視財務數據分析的投資者</li>
                    <li><strong>價值成長投資者：</strong>追求合理估值下的成長股</li>
                    <li><strong>理性投資者：</strong>偏好系統化、有紀律的投資方法</li>
                    <li><strong>中期投資者：</strong>投資週期以月為單位的投資者</li>
                    <li><strong>創新思維投資者：</strong>願意接受指標創新應用的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>每月營收公告後執行策略</li>
                    <li><strong>數據品質：</strong>確保使用最新且準確的財務數據</li>
                    <li><strong>風險監控：</strong>嚴格執行10%停損紀律</li>
                    <li><strong>組合管理：</strong>維持10檔以內的投資組合</li>
                    <li><strong>持續學習：</strong>關注財務指標的創新應用</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財務數據</p>
                <ul style="color: red;">
                    <li><strong>本益比：</strong>目前使用價格相對位置模擬，真實數據需從財報計算</li>
                    <li><strong>營業利益成長率：</strong>目前使用價格動能模擬，真實數據需從財報取得</li>
                    <li><strong>月營收：</strong>目前使用價格趨勢模擬，真實數據可從公開資訊觀測站取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站財報、FinMind財報API、月營收公告</p>
                <p style="color: green;"><strong>策略創新：</strong>重構傳統PEG指標，教你靈活應用財務指標而不拘於傳統定義！</p>
            """,

            "多產業價投": """
                <h2>🏭 多產業價投策略 - 同業本益比排名的相對價值投資</h2>
                <p><strong>策略類型：</strong>相對價值投資策略</p>
                <p><strong>核心理念：</strong>分析股票最重要的就是不能使用單一數值來判斷股票的好壞，而是要多方比較相同產業、或是企業本身過去資料來做對比。我們在此策略中，提出不一樣的作法，計算同業本益比的排名，可排除產業帶來的本益比差異。</p>

                <h3>🎯 策略創新理念</h3>
                <p>將本益比拿來跟同產業其他個股比較，可以知道持股在同業中是否排領先，或是處於末段班。這樣的資訊通常不容易取得，本策略示範如何用簡易的方式將資料作處理，並用於選股回測。</p>

                <h3>📚 教學價值 - 一次學會多項技能</h3>
                <p>這個策略應用跨了許多面向，讓你一次學會：</p>
                <ul style="background-color: #f0f8ff; padding: 10px; border-left: 4px solid #4CAF50;">
                    <li><strong>📊 產業資訊處理：</strong>學會如何整理和分析產業分類數據</li>
                    <li><strong>🔍 產業面策略：</strong>同業本益比比較法的程式撰寫技巧</li>
                    <li><strong>📈 多策略回測：</strong>比較報告生成和績效分析方法</li>
                    <li><strong>🎯 策略優化：</strong>產業面選股結合個股基本面的優化技術</li>
                </ul>

                <h3>🔍 傳統分析 vs 產業相對分析</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>分析方法</th><th>傳統分析</th><th>產業相對分析 (本策略)</th><th>優勢</th>
                    </tr>
                    <tr>
                        <td><strong>比較基準</strong></td>
                        <td>企業本身過去</td>
                        <td style="color: green;"><strong>同產業其他個股</strong></td>
                        <td>排除產業差異</td>
                    </tr>
                    <tr>
                        <td><strong>本益比評估</strong></td>
                        <td>絕對數值判斷</td>
                        <td style="color: green;"><strong>相對排名評估</strong></td>
                        <td>發現相對價值</td>
                    </tr>
                    <tr>
                        <td><strong>產業特性</strong></td>
                        <td>忽略產業差異</td>
                        <td style="color: green;"><strong>考慮產業特性</strong></td>
                        <td>更精確的估值</td>
                    </tr>
                    <tr>
                        <td><strong>資訊取得</strong></td>
                        <td>容易取得</td>
                        <td style="color: orange;"><strong>需要特殊處理</strong></td>
                        <td>資訊稀缺性優勢</td>
                    </tr>
                </table>

                <h3>🎯 核心選股條件</h3>

                <h4>1. 同業本益比排名 (30分)</h4>
                <ul>
                    <li><strong>條件：</strong>同業本益比排名前50%</li>
                    <li><strong>邏輯：</strong>在同產業中尋找相對便宜的股票</li>
                    <li><strong>優勢：</strong>排除產業帶來的本益比天然差異</li>
                    <li><strong>計算：</strong>將同產業股票按本益比排序，選取前50%</li>
                </ul>

                <h4>2. 營收動能持續 (25分)</h4>
                <ul>
                    <li><strong>條件：</strong>營收動能持續向上</li>
                    <li><strong>邏輯：</strong>確保公司營運動能良好</li>
                    <li><strong>意義：</strong>避免選到營收衰退的便宜股</li>
                    <li><strong>評估：</strong>近期營收趨勢分析</li>
                </ul>

                <h4>3. 年增率門檻 (20分)</h4>
                <ul>
                    <li><strong>條件：</strong>年增率 ≥ 20%</li>
                    <li><strong>邏輯：</strong>確保公司具備高成長性</li>
                    <li><strong>意義：</strong>篩選出真正的成長股</li>
                    <li><strong>標準：</strong>營收或盈餘年增率達20%以上</li>
                </ul>

                <h4>4. 長期成長穩定 (15分)</h4>
                <ul>
                    <li><strong>條件：</strong>長期成長 ≥ 15%</li>
                    <li><strong>邏輯：</strong>確保成長的持續性</li>
                    <li><strong>意義：</strong>避免一次性成長的公司</li>
                    <li><strong>評估：</strong>多年平均成長率分析</li>
                </ul>

                <h4>5. 流動性要求 (10分)</h4>
                <ul>
                    <li><strong>條件：</strong>成交量 ≥ 10萬股</li>
                    <li><strong>邏輯：</strong>確保股票具備足夠流動性</li>
                    <li><strong>意義：</strong>保證進出場的便利性</li>
                    <li><strong>標準：</strong>日均成交量達10萬股以上</li>
                </ul>

                <h3>🏭 產業分析框架</h3>

                <h4>核心產業範圍</h4>
                <p>策略涵蓋十大核心產業：</p>
                <ul style="columns: 2; column-gap: 20px;">
                    <li><strong>貿易百貨</strong> - 零售通路產業</li>
                    <li><strong>雲端運算</strong> - 科技服務產業</li>
                    <li><strong>電機機械</strong> - 傳統製造業</li>
                    <li><strong>電動車輛產業</strong> - 新興產業</li>
                    <li><strong>食品</strong> - 民生消費產業</li>
                    <li><strong>石化及塑橡膠</strong> - 化工產業</li>
                    <li><strong>大數據</strong> - 數據科技產業</li>
                    <li><strong>紡織</strong> - 傳統製造業</li>
                    <li><strong>金融</strong> - 金融服務業</li>
                    <li><strong>軟體服務</strong> - 科技服務業</li>
                </ul>

                <h4>產業本益比特性分析</h4>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>產業類型</th><th>典型本益比範圍</th><th>特性</th><th>分析重點</th>
                    </tr>
                    <tr>
                        <td><strong>科技成長股</strong></td>
                        <td>20-40倍</td>
                        <td>高成長、高估值</td>
                        <td>成長持續性</td>
                    </tr>
                    <tr>
                        <td><strong>傳統製造業</strong></td>
                        <td>8-15倍</td>
                        <td>穩定獲利、低估值</td>
                        <td>營運效率</td>
                    </tr>
                    <tr>
                        <td><strong>金融服務業</strong></td>
                        <td>10-18倍</td>
                        <td>景氣循環、股利穩定</td>
                        <td>資產品質</td>
                    </tr>
                    <tr>
                        <td><strong>民生消費業</strong></td>
                        <td>12-25倍</td>
                        <td>需求穩定、品牌價值</td>
                        <td>市場佔有率</td>
                    </tr>
                </table>

                <h3>📊 數據處理技術</h3>

                <h4>產業分類處理</h4>
                <ol>
                    <li><strong>數據收集：</strong>從多個來源收集產業分類資訊</li>
                    <li><strong>標準化：</strong>統一不同來源的產業分類標準</li>
                    <li><strong>清理：</strong>處理缺失值和異常值</li>
                    <li><strong>驗證：</strong>交叉驗證產業分類的準確性</li>
                </ol>

                <h4>同業本益比排名計算</h4>
                <ol>
                    <li><strong>分組：</strong>按產業分類將股票分組</li>
                    <li><strong>計算：</strong>計算各股票的本益比</li>
                    <li><strong>排序：</strong>在各產業內按本益比排序</li>
                    <li><strong>排名：</strong>計算相對排名百分位</li>
                </ol>

                <h3>⏰ 交易執行機制</h3>

                <h4>重新平衡頻率</h4>
                <ul>
                    <li><strong>調整頻率：</strong>雙週重新平衡</li>
                    <li><strong>執行時機：</strong>每兩週檢視持股狀況</li>
                    <li><strong>調整原則：</strong>基於最新財務數據和產業排名</li>
                    <li><strong>執行紀律：</strong>嚴格按照量化條件執行</li>
                </ul>

                <h4>投資組合管理</h4>
                <ul>
                    <li><strong>持股上限：</strong>單一持股不超過20%</li>
                    <li><strong>產業分散：</strong>避免過度集中單一產業</li>
                    <li><strong>動態調整：</strong>根據產業排名變化調整</li>
                    <li><strong>風險監控：</strong>持續監控產業和個股風險</li>
                </ul>

                <h3>🛡️ 風險控制機制</h3>

                <h4>停損設定</h4>
                <ul>
                    <li><strong>停損比例：</strong>20%停損</li>
                    <li><strong>執行方式：</strong>個股獨立停損</li>
                    <li><strong>監控頻率：</strong>每日監控股價變化</li>
                    <li><strong>執行紀律：</strong>嚴格執行停損紀律</li>
                </ul>

                <h4>風險分散</h4>
                <ul>
                    <li><strong>產業分散：</strong>跨多個產業投資</li>
                    <li><strong>個股分散：</strong>單一持股限制20%</li>
                    <li><strong>時間分散：</strong>雙週調整分散時間風險</li>
                    <li><strong>估值分散：</strong>不同估值水準的股票組合</li>
                </ul>

                <h3>📈 策略優勢</h3>

                <h4>分析優勢</h4>
                <ol>
                    <li><strong>相對價值：</strong>發現同業中的相對價值窪地</li>
                    <li><strong>產業中性：</strong>排除產業間本益比天然差異</li>
                    <li><strong>資訊稀缺：</strong>同業排名資訊不易取得</li>
                    <li><strong>多重驗證：</strong>結合多個基本面指標</li>
                    <li><strong>動態調整：</strong>隨產業變化動態調整</li>
                </ol>

                <h4>教學優勢</h4>
                <ul>
                    <li><strong>技能整合：</strong>一次學會多項分析技能</li>
                    <li><strong>實務導向：</strong>可直接應用的分析方法</li>
                    <li><strong>程式實作：</strong>完整的程式撰寫教學</li>
                    <li><strong>回測驗證：</strong>科學的策略驗證方法</li>
                </ul>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>價值投資者：</strong>重視相對價值分析的投資者</li>
                    <li><strong>產業分析師：</strong>需要跨產業比較分析的專業人士</li>
                    <li><strong>量化投資者：</strong>偏好系統化分析方法的投資者</li>
                    <li><strong>學習型投資者：</strong>想要學習產業分析技能的投資者</li>
                    <li><strong>程式交易者：</strong>需要程式化選股策略的交易者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>每兩週執行一次策略調整</li>
                    <li><strong>產業研究：</strong>深入了解各產業的特性和週期</li>
                    <li><strong>數據品質：</strong>確保產業分類和財務數據的準確性</li>
                    <li><strong>風險監控：</strong>密切關注產業和個股風險變化</li>
                    <li><strong>持續學習：</strong>不斷優化產業分析和選股技能</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實產業分類和財務數據</p>
                <ul style="color: red;">
                    <li><strong>產業分類：</strong>目前使用價格相對位置模擬同業排名，真實數據需從產業題材分類取得</li>
                    <li><strong>本益比：</strong>目前使用價格指標模擬，真實數據需從財報計算</li>
                    <li><strong>營收數據：</strong>目前使用價格趨勢模擬，真實數據需從月營收公告取得</li>
                    <li><strong>成交量：</strong>目前使用模擬數據，真實數據需從交易所取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinMind產業分類API、公開資訊觀測站財報、同業本益比統計、月營收公告</p>
                <p style="color: green;"><strong>教學特色：</strong>一次學會產業資訊處理、同業比較分析、多策略回測、策略優化等多項技能！</p>
                <p style="color: purple;"><strong>參考資源：</strong><a href="https://www.finlab.tw/industry_pe_strategy/" target="_blank">產業面選股教學文章</a></p>
            """,

            "低波動本益成長比": """
                <h2>🤖 低波動本益成長比策略 - 機器學習優化的進階PEG策略</h2>
                <p><strong>策略類型：</strong>機器學習量化策略</p>
                <p><strong>核心理念：</strong>運用先進的數據分析和機器學習技術來優化股市投資策略。策略基於「本益成長比」，結合了月營收增長和營業利益成長率等多個經濟指標，旨在尋找高成長潛力且估值合理的股票。</p>

                <h3>🎯 策略創新理念</h3>
                <p>核心選股條件涵蓋了短期內營收的增長動能，以及營業利益的穩定增長。此外，策略還考慮了市場波動率和其他風險因子，如融資使用率和業外收支占營收的比例，以確保選擇的股票具有較低的市場風險和較高的財務穩定性。</p>

                <h3>🤖 機器學習技術核心</h3>
                <p>進階的部分在於運用機器學習技術進行特徵選擇和優化。通過非監督學習的方法，如k-means算法，對股票進行分群，以確定哪些特徵與策略目標最為相關。接著，利用決策樹等監督學習算法進一步分析這些特徵，從而優化選股策略。</p>

                <h3>📊 核心選股條件 (總分100分)</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>條件</th><th>標準</th><th>分數</th><th>機器學習應用</th>
                    </tr>
                    <tr>
                        <td><strong>營收動能</strong></td>
                        <td>> 1.1</td>
                        <td>25分</td>
                        <td>特徵重要性分析</td>
                    </tr>
                    <tr>
                        <td><strong>月營收成長</strong></td>
                        <td>> 0.9</td>
                        <td>20分</td>
                        <td>時間序列分析</td>
                    </tr>
                    <tr>
                        <td><strong>進場波動率</strong></td>
                        <td>≤ 0.032</td>
                        <td>20分</td>
                        <td>風險因子建模</td>
                    </tr>
                    <tr>
                        <td><strong>融資使用率</strong></td>
                        <td>≤ 34%</td>
                        <td>20分</td>
                        <td>槓桿風險評估</td>
                    </tr>
                    <tr>
                        <td><strong>業外收支比</strong></td>
                        <td>< 5.2%</td>
                        <td>15分</td>
                        <td>財務穩定性分析</td>
                    </tr>
                </table>

                <h3>🔍 選股條件詳細解析</h3>

                <h4>1. 營收動能 > 1.1 (25分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>短期營收增長動能指標</li>
                    <li><strong>投資邏輯：</strong>確保公司具備強勁的營收成長動能</li>
                    <li><strong>機器學習：</strong>透過特徵重要性分析確定最佳門檻值</li>
                    <li><strong>風險控制：</strong>避免選到營收衰退的股票</li>
                </ul>

                <h4>2. 月營收成長 > 0.9 (20分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>當月營收相對上月的成長率</li>
                    <li><strong>投資邏輯：</strong>確保營收成長的穩定性和持續性</li>
                    <li><strong>機器學習：</strong>運用時間序列分析預測營收趨勢</li>
                    <li><strong>風險控制：</strong>避免營收大幅波動的公司</li>
                </ul>

                <h4>3. 進場波動率 ≤ 0.032 (20分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>ATR/收盤價，衡量股價波動程度</li>
                    <li><strong>投資邏輯：</strong>確保低風險進場時機，降低投資組合波動</li>
                    <li><strong>機器學習：</strong>透過風險因子建模優化波動率門檻</li>
                    <li><strong>風險控制：</strong>顯著降低投資組合的整體風險</li>
                </ul>

                <h4>4. 融資使用率 ≤ 34% (20分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>融資餘額相對於流通股本的比例</li>
                    <li><strong>投資邏輯：</strong>避免過度槓桿風險，確保市場情緒穩定</li>
                    <li><strong>機器學習：</strong>透過槓桿風險評估模型分析最佳門檻</li>
                    <li><strong>風險控制：</strong>避免融資斷頭賣壓的風險</li>
                </ul>

                <h4>5. 業外收支比 < 5.2% (15分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>業外收支占營收的比例</li>
                    <li><strong>投資邏輯：</strong>確保財務穩定性，專注本業經營</li>
                    <li><strong>機器學習：</strong>透過財務穩定性分析確定合理範圍</li>
                    <li><strong>風險控制：</strong>避免過度依賴業外收入的公司</li>
                </ul>

                <h3>🤖 機器學習技術應用</h3>

                <h4>非監督學習 - K-means分群</h4>
                <ul>
                    <li><strong>目的：</strong>對股票進行分群，識別相似特徵的股票群組</li>
                    <li><strong>方法：</strong>運用k-means算法對多維特徵進行聚類分析</li>
                    <li><strong>應用：</strong>確定哪些特徵與策略目標最為相關</li>
                    <li><strong>優勢：</strong>發現隱藏的股票特徵模式和關聯性</li>
                </ul>

                <h4>監督學習 - 決策樹分析</h4>
                <ul>
                    <li><strong>目的：</strong>分析特徵重要性，優化選股條件</li>
                    <li><strong>方法：</strong>利用決策樹算法建立特徵重要性排序</li>
                    <li><strong>應用：</strong>確定各個選股條件的最佳權重和門檻</li>
                    <li><strong>優勢：</strong>提供可解釋的特徵重要性分析結果</li>
                </ul>

                <h4>模型評估 - 混淆矩陣</h4>
                <ul>
                    <li><strong>目的：</strong>評估模型預測準確性和效能</li>
                    <li><strong>方法：</strong>透過混淆矩陣分析模型的精確率和召回率</li>
                    <li><strong>應用：</strong>驗證低波動因子在風險控制方面的效果</li>
                    <li><strong>優勢：</strong>量化評估策略改進的實際效果</li>
                </ul>

                <h3>📈 策略效能優化</h3>

                <h4>風險調整後績效</h4>
                <p>最終，透過混淆矩陣等評估工具檢視模型效能，確認了低融資使用率和低進場波動率等因子在減少投資組合波動性方面的重要性。儘管這種方法保持了策略的報酬率，但同時也顯著優化了夏普比率和最大回撤率，進而提升了整體投資效率。</p>

                <h4>績效指標改善</h4>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>績效指標</th><th>傳統PEG策略</th><th>低波動PEG策略</th><th>改善幅度</th>
                    </tr>
                    <tr>
                        <td><strong>年化報酬率</strong></td>
                        <td>維持水準</td>
                        <td style="color: green;"><strong>維持水準</strong></td>
                        <td>持平</td>
                    </tr>
                    <tr>
                        <td><strong>夏普比率</strong></td>
                        <td>基準值</td>
                        <td style="color: green;"><strong>顯著提升</strong></td>
                        <td>⬆️ 大幅改善</td>
                    </tr>
                    <tr>
                        <td><strong>最大回撤率</strong></td>
                        <td>基準值</td>
                        <td style="color: green;"><strong>顯著降低</strong></td>
                        <td>⬇️ 大幅改善</td>
                    </tr>
                    <tr>
                        <td><strong>波動率</strong></td>
                        <td>基準值</td>
                        <td style="color: green;"><strong>明顯降低</strong></td>
                        <td>⬇️ 風險降低</td>
                    </tr>
                </table>

                <h3>🔬 技術創新特色</h3>

                <h4>基本面 + 技術面 + 機器學習</h4>
                <p>這個策略不僅展示了如何結合基本面分析和技術面分析來選擇股票，還展示了如何利用機器學習來精煉和優化投資策略，使其更加適應市場變化，從而提高投資回報的同時降低風險。</p>

                <h4>三層分析架構</h4>
                <ol>
                    <li><strong>基本面分析：</strong>營收成長、營業利益、財務穩定性</li>
                    <li><strong>技術面分析：</strong>波動率、融資使用率、市場情緒</li>
                    <li><strong>機器學習：</strong>特徵選擇、模型優化、效能評估</li>
                </ol>

                <h3>🛡️ 風險控制機制</h3>

                <h4>多重風險因子篩選</h4>
                <ul>
                    <li><strong>市場風險：</strong>透過進場波動率控制市場風險暴露</li>
                    <li><strong>槓桿風險：</strong>透過融資使用率避免過度槓桿</li>
                    <li><strong>財務風險：</strong>透過業外收支比確保財務穩定</li>
                    <li><strong>流動性風險：</strong>確保持股具備足夠的交易流動性</li>
                </ul>

                <h4>投資組合管理</h4>
                <ul>
                    <li><strong>持股上限：</strong>單一持股不超過15%</li>
                    <li><strong>停損設定：</strong>10%停損，嚴格執行風險控制</li>
                    <li><strong>交易執行：</strong>開盤價交易，避免盤中波動影響</li>
                    <li><strong>動態調整：</strong>根據機器學習模型結果動態調整</li>
                </ul>

                <h3>🎯 策略優勢</h3>

                <h4>量化優勢</h4>
                <ul>
                    <li><strong>降低波動：</strong>顯著優化夏普比率和最大回撤率</li>
                    <li><strong>提升效率：</strong>保持報酬率的同時提升整體投資效率</li>
                    <li><strong>風險控制：</strong>多重風險因子篩選，降低投資組合風險</li>
                    <li><strong>適應性強：</strong>機器學習技術使策略更適應市場變化</li>
                </ul>

                <h4>技術優勢</h4>
                <ul>
                    <li><strong>科學方法：</strong>運用先進的數據科學技術</li>
                    <li><strong>客觀分析：</strong>機器學習避免主觀偏見</li>
                    <li><strong>持續優化：</strong>模型可持續學習和改進</li>
                    <li><strong>可解釋性：</strong>決策樹提供可解釋的分析結果</li>
                </ul>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>量化投資者：</strong>追求科技化投資方法的專業投資者</li>
                    <li><strong>風險控制型投資者：</strong>重視風險調整後報酬的投資者</li>
                    <li><strong>技術導向投資者：</strong>偏好運用先進技術的投資者</li>
                    <li><strong>機構投資者：</strong>需要系統化風險管理的機構</li>
                    <li><strong>學習型投資者：</strong>想要了解機器學習應用的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>技術準備：</strong>需要具備基本的機器學習知識</li>
                    <li><strong>數據品質：</strong>確保輸入數據的準確性和完整性</li>
                    <li><strong>模型維護：</strong>定期更新和優化機器學習模型</li>
                    <li><strong>風險監控：</strong>持續監控各項風險因子的變化</li>
                    <li><strong>績效評估：</strong>定期評估策略效能和改進空間</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實機器學習數據和財務指標</p>
                <ul style="color: red;">
                    <li><strong>ATR指標：</strong>目前使用簡化計算，真實數據需要分鐘級K線數據</li>
                    <li><strong>融資使用率：</strong>目前使用成交量變化模擬，真實數據需從券商取得</li>
                    <li><strong>業外收支：</strong>目前使用波動率模擬，真實數據需從財報計算</li>
                    <li><strong>營業利益成長率：</strong>目前使用價格動能模擬，真實數據需從財報取得</li>
                    <li><strong>機器學習模型：</strong>目前使用規則模擬，真實應用需要歷史數據訓練</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>券商API融資數據、財報營業利益、高頻K線數據、機器學習平台</p>
            """,

            "財務指標計分選股法": """
                <h2>📊 財務指標計分選股法 - 多財務指標評分系統</h2>
                <p><strong>策略類型：</strong>基本面量化評分策略</p>
                <p><strong>核心理念：</strong>將財務指標轉成分數，將多個財務指標分數加總來篩選財務體質。採用量化評分方式，客觀評估企業財務健康度，避免單一指標的局限性。</p>

                <h3>🎯 策略創新理念</h3>
                <p>這個策略的核心在於解決傳統財務分析的痛點：如何客觀地比較不同財務指標？如何避免單一指標的誤導？透過將各項財務指標標準化為1-10分的評分系統，我們可以將不同性質的財務數據放在同一個天秤上比較，從而建立一個全面且客觀的財務體質評估系統。</p>

                <h3>📊 五大財務指標評分系統</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>財務指標</th><th>滿分</th><th>評估面向</th><th>評分邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>ROE稅後</strong></td>
                        <td>10分</td>
                        <td>獲利能力</td>
                        <td>股東權益報酬率，越高越好</td>
                    </tr>
                    <tr>
                        <td><strong>營業毛利率</strong></td>
                        <td>10分</td>
                        <td>競爭力</td>
                        <td>反映企業定價能力，越高越好</td>
                    </tr>
                    <tr>
                        <td><strong>稅前淨利成長率</strong></td>
                        <td>10分</td>
                        <td>成長潛力</td>
                        <td>獲利成長動能，越高越好</td>
                    </tr>
                    <tr>
                        <td><strong>應收帳款週轉率</strong></td>
                        <td>10分</td>
                        <td>營運效率</td>
                        <td>收款效率，越高越好</td>
                    </tr>
                    <tr>
                        <td><strong>負債比率</strong></td>
                        <td>10分</td>
                        <td>財務風險</td>
                        <td>財務槓桿水準，越低越好</td>
                    </tr>
                </table>

                <h3>🔍 財務指標詳細解析</h3>

                <h4>1. ROE稅後 (10分) - 獲利能力指標</h4>
                <ul>
                    <li><strong>計算公式：</strong>稅後淨利 ÷ 股東權益 × 100%</li>
                    <li><strong>投資意義：</strong>衡量企業為股東創造報酬的能力</li>
                    <li><strong>評分邏輯：</strong>ROE越高，代表企業獲利能力越強</li>
                    <li><strong>優秀標準：</strong>ROE > 15%通常被視為優秀企業</li>
                </ul>

                <h4>2. 營業毛利率 (10分) - 競爭力指標</h4>
                <ul>
                    <li><strong>計算公式：</strong>(營收 - 營業成本) ÷ 營收 × 100%</li>
                    <li><strong>投資意義：</strong>反映企業產品競爭力和定價能力</li>
                    <li><strong>評分邏輯：</strong>毛利率越高，代表企業競爭優勢越明顯</li>
                    <li><strong>產業差異：</strong>不同產業毛利率差異很大，需要相對比較</li>
                </ul>

                <h4>3. 稅前淨利成長率 (10分) - 成長潛力指標</h4>
                <ul>
                    <li><strong>計算公式：</strong>(本期稅前淨利 - 去年同期) ÷ 去年同期 × 100%</li>
                    <li><strong>投資意義：</strong>評估企業獲利成長的動能和持續性</li>
                    <li><strong>評分邏輯：</strong>成長率越高，代表企業發展潛力越大</li>
                    <li><strong>注意事項：</strong>需要排除一次性收益的影響</li>
                </ul>

                <h4>4. 應收帳款週轉率 (10分) - 營運效率指標</h4>
                <ul>
                    <li><strong>計算公式：</strong>營收 ÷ 平均應收帳款</li>
                    <li><strong>投資意義：</strong>反映企業收款效率和營運管理能力</li>
                    <li><strong>評分邏輯：</strong>週轉率越高，代表收款越快，現金流越好</li>
                    <li><strong>管理意義：</strong>高週轉率通常代表良好的客戶關係和信用管理</li>
                </ul>

                <h4>5. 負債比率 (10分) - 財務風險指標</h4>
                <ul>
                    <li><strong>計算公式：</strong>總負債 ÷ 總資產 × 100%</li>
                    <li><strong>投資意義：</strong>評估企業財務槓桿和償債風險</li>
                    <li><strong>評分邏輯：</strong>負債比率越低，財務風險越小（反向評分）</li>
                    <li><strong>平衡考量：</strong>適度負債可以提升ROE，但過高會增加風險</li>
                </ul>

                <h3>🔧 技術實作 - qcut_feature函數</h3>

                <h4>核心技術：Pandas qcut 與 rank 函數</h4>
                <p>策略使用了先進的數據分級處理技術，將不同財務指標標準化為可比較的分數：</p>

                <h4>qcut_feature函數設計</h4>
                <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
def qcut_feature(data_name, q_range=10, ascending=True):
    rank_df = (data.get(data_name)
               .rank(axis=1, pct=True, ascending=ascending)
               .mul(q_range)
               .add(1)
               .apply(np.floor).clip(0, q_range))
    return rank_df
                </pre>

                <h4>函數參數說明</h4>
                <ul>
                    <li><strong>data_name：</strong>設定要處理的FinLab資料庫中的財務指標</li>
                    <li><strong>q_range：</strong>分割的級距，預設為10分制</li>
                    <li><strong>ascending：</strong>控制指標升降屬性，處理正向/反向指標</li>
                </ul>

                <h4>技術優勢</h4>
                <ul>
                    <li><strong>標準化：</strong>將不同性質的財務指標統一為1-10分</li>
                    <li><strong>相對排名：</strong>基於百分位數排名，避免絕對數值的誤導</li>
                    <li><strong>動態調整：</strong>每期重新計算排名，適應市場變化</li>
                    <li><strong>程式簡潔：</strong>使用pandas高效處理大量數據</li>
                </ul>

                <h3>🎯 評分機制詳解</h3>

                <h4>評分系統設計</h4>
                <ul>
                    <li><strong>分數範圍：</strong>每項指標1-10分，總分最高50分</li>
                    <li><strong>排名機制：</strong>使用百分位數排名轉換為分數</li>
                    <li><strong>篩選門檻：</strong>總分≥40分才符合財務體質優良標準</li>
                    <li><strong>選股邏輯：</strong>從符合條件的股票中選取前10檔低價股</li>
                </ul>

                <h4>評分標準解釋</h4>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>總分範圍</th><th>財務體質</th><th>投資建議</th><th>選股策略</th>
                    </tr>
                    <tr>
                        <td><strong>45-50分</strong></td>
                        <td style="color: green;">優秀</td>
                        <td>積極考慮</td>
                        <td>核心持股候選</td>
                    </tr>
                    <tr>
                        <td><strong>40-44分</strong></td>
                        <td style="color: blue;">良好</td>
                        <td>可以考慮</td>
                        <td>策略選股門檻</td>
                    </tr>
                    <tr>
                        <td><strong>30-39分</strong></td>
                        <td style="color: orange;">普通</td>
                        <td>謹慎評估</td>
                        <td>不符合條件</td>
                    </tr>
                    <tr>
                        <td><strong>< 30分</strong></td>
                        <td style="color: red;">較差</td>
                        <td>避免投資</td>
                        <td>排除清單</td>
                    </tr>
                </table>

                <h3>📈 策略執行流程</h3>

                <h4>第一步：數據收集與評分</h4>
                <ol>
                    <li>收集五大財務指標的最新數據</li>
                    <li>使用qcut_feature函數將各指標轉換為1-10分</li>
                    <li>計算每支股票的總分（最高50分）</li>
                    <li>篩選總分≥40分的股票</li>
                </ol>

                <h4>第二步：低價股優化選擇</h4>
                <ol>
                    <li>從符合財務條件的股票中</li>
                    <li>按照股價由低到高排序</li>
                    <li>選取前10檔低價股</li>
                    <li>建構投資組合</li>
                </ol>

                <h4>第三步：風險控制與調整</h4>
                <ul>
                    <li><strong>月度重新平衡：</strong>每月重新評估和調整持股</li>
                    <li><strong>持股上限：</strong>單一持股不超過10%</li>
                    <li><strong>動態監控：</strong>持續監控財務指標變化</li>
                    <li><strong>風險分散：</strong>透過多檔持股分散風險</li>
                </ul>

                <h3>💡 策略優勢</h3>

                <h4>方法論優勢</h4>
                <ul>
                    <li><strong>客觀量化：</strong>避免主觀判斷，使用數據驅動決策</li>
                    <li><strong>多維評估：</strong>綜合考量獲利、成長、效率、風險四個面向</li>
                    <li><strong>相對排名：</strong>基於市場相對表現，而非絕對數值</li>
                    <li><strong>動態調整：</strong>每期重新計算，適應市場變化</li>
                </ul>

                <h4>實務應用優勢</h4>
                <ul>
                    <li><strong>易於理解：</strong>1-10分制直觀易懂</li>
                    <li><strong>便於比較：</strong>不同股票可以直接比較總分</li>
                    <li><strong>程式化執行：</strong>可以完全自動化執行</li>
                    <li><strong>小資友善：</strong>選擇低價股，適合小資族投資</li>
                </ul>

                <h3>📊 回測績效表現</h3>

                <h4>策略表現特色</h4>
                <p>根據FinLab的回測結果，財務指標計分選股法展現出以下特色：</p>
                <ul>
                    <li><strong>穩定超額報酬：</strong>長期表現優於大盤</li>
                    <li><strong>風險控制良好：</strong>透過財務體質篩選降低風險</li>
                    <li><strong>適合長期投資：</strong>基於基本面的策略具有持續性</li>
                    <li><strong>小資族友善：</strong>低價股策略降低投資門檻</li>
                </ul>

                <h4>優化效果驗證</h4>
                <p>透過加入「低價股優化」後，策略表現明顯改善：</p>
                <ul>
                    <li><strong>報酬率提升：</strong>明顯拉出與大盤的差距</li>
                    <li><strong>持股集中：</strong>從100多檔降至10檔，更適合實際操作</li>
                    <li><strong>資金效率：</strong>更符合小資族的資金規模</li>
                    <li><strong>操作便利：</strong>減少交易複雜度和成本</li>
                </ul>
                <ul>
                    <li><strong>客觀評估：</strong>量化評分避免主觀判斷偏誤</li>
                    <li><strong>全面分析：</strong>涵蓋獲利、成長、營運、財務四大面向</li>
                    <li><strong>風險控制：</strong>多重指標篩選降低單一風險</li>
                    <li><strong>操作簡單：</strong>明確的評分標準和篩選門檻</li>
                </ul>

                <h3>📈 選股流程</h3>
                <ol>
                    <li><strong>指標計算：</strong>計算五大財務指標的排名分數</li>
                    <li><strong>分數加總：</strong>將五項分數相加得到總分</li>
                    <li><strong>門檻篩選：</strong>篩選總分≥40分的股票</li>
                    <li><strong>價格排序：</strong>從符合條件的股票中選取前10檔低價股</li>
                    <li><strong>月度調整：</strong>每月重新評估和調整持股</li>
                </ol>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財務報表數據</p>
                <ul style="color: red;">
                    <li><strong>ROE稅後：</strong>目前使用價格相對表現模擬，真實數據需從財報計算</li>
                    <li><strong>營業毛利率：</strong>目前使用價格穩定度模擬，真實數據需從損益表取得</li>
                    <li><strong>稅前淨利成長率：</strong>目前使用價格成長率模擬，真實數據需從財報計算</li>
                    <li><strong>應收帳款週轉率：</strong>目前使用成交量穩定度模擬，真實數據需從資產負債表計算</li>
                    <li><strong>負債比率：</strong>目前使用價格波動率模擬，真實數據需從資產負債表計算</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站財報、FinMind財報API、TEJ財務資料庫</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>價值投資者：</strong>重視基本面分析和財務體質的投資者</li>
                    <li><strong>穩健型投資者：</strong>追求穩定報酬，重視風險控制的投資者</li>
                    <li><strong>小資族：</strong>資金有限，偏好低價股的投資者</li>
                    <li><strong>量化投資者：</strong>偏好系統化、可重複執行的投資策略</li>
                    <li><strong>學習型投資者：</strong>想要學習財務分析和程式化投資的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>建議每月執行一次，更新持股清單</li>
                    <li><strong>財務研究：</strong>深入了解各項財務指標的意義和計算方法</li>
                    <li><strong>產業考量：</strong>注意不同產業的財務指標特性差異</li>
                    <li><strong>風險監控：</strong>持續關注持股公司的財務狀況變化</li>
                    <li><strong>組合管理：</strong>適當分散投資，避免過度集中</li>
                </ul>

                <h3>📚 學習資源</h3>
                <ul>
                    <li><strong>FinLab教學文章：</strong><a href="https://www.finlab.tw/basic_score_strategy/" target="_blank">如何用指標計分來選股? | Python 資料分級處理</a></li>
                    <li><strong>Pandas qcut：</strong>學習數據分級處理技術</li>
                    <li><strong>財務分析：</strong>深入理解各項財務指標的意義</li>
                    <li><strong>量化投資：</strong>掌握系統化投資方法</li>
                </ul>

                <p style="color: green;"><strong>學習特色：</strong>一次學會財務指標分析、數據分級處理、量化評分系統、程式化選股等多項技能！</p>
                <p style="color: purple;"><strong>參考資源：</strong><a href="https://www.finlab.tw/basic_score_strategy/" target="_blank">FinLab指標計分教學文章</a></p>
            """,

            "營收股價雙渦輪": """
                <h2>🚀 營收股價雙渦輪策略 - 業績與市場表現雙重強勢</h2>
                <p><strong>策略類型：</strong>業績動能策略</p>
                <p><strong>核心理念：</strong>揭示一種創新的台股投資策略，專門挖掘業績與市場表現雙重強勢的股票。這個策略巧妙結合了獨到的市場數據分析和精確的選股條件，旨在識別那些最有可能帶來高回報的投資機會。</p>

                <h3>🎯 策略創新理念</h3>
                <p>核心概念圍繞著對公司業績的深入分析，特別是將公司的短期業績變化與市場表現趨勢相結合，從而挑選出那些業績顯著改善且獲得市場高度認可的股票。此外，策略還考慮了交易量等關鍵因素，以確保所選股票具有良好的流動性，便於投資者進行交易。</p>

                <h3>⚡ 雙渦輪效應解析</h3>
                <p>被稱為「營收股價雙渦輪」的策略，正如其名，象徵著營收增長和股價上漲這兩股強大動力的結合。它利用獨特的策略和細緻的市場分析，為追求高收益的投資者提供了一個值得考慮的選擇。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>渦輪類型</th><th>驅動力</th><th>核心指標</th><th>投資邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>營收渦輪</strong></td>
                        <td>基本面驅動</td>
                        <td>近2月營收創12月新高</td>
                        <td>確保企業真實業績改善</td>
                    </tr>
                    <tr>
                        <td><strong>股價渦輪</strong></td>
                        <td>技術面驅動</td>
                        <td>5日內2日創200日新高</td>
                        <td>確保市場資金認可</td>
                    </tr>
                    <tr>
                        <td><strong>雙重加速</strong></td>
                        <td>協同效應</td>
                        <td>兩股力量同時作用</td>
                        <td>產生強大推進力</td>
                    </tr>
                </table>

                <h3>🚀 雙渦輪核心條件 (總分100分)</h3>

                <h4>1. 營收渦輪 - 近2月營收創12月新高 (35分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>近2月平均營收創12個月來新高</li>
                    <li><strong>投資邏輯：</strong>確保企業真實業績改善和突破</li>
                    <li><strong>技術優勢：</strong>平滑過年效應，避免季節性干擾</li>
                    <li><strong>數據來源：</strong>月營收公告，台股獨特的基本面因子</li>
                </ul>

                <h4>2. 股價渦輪 - 5日內2日創200日新高 (35分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>近5日內有2日股價創200日新高</li>
                    <li><strong>投資邏輯：</strong>確保市場資金認可，避免假突破</li>
                    <li><strong>技術優勢：</strong>使用sustain方法確認突破強度</li>
                    <li><strong>動能確認：</strong>連續挑戰新高，代表動能越強</li>
                </ul>

                <h4>3. 流動性保障 - 5日均量>500張 (20分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>五日成交均量大於500張</li>
                    <li><strong>投資邏輯：</strong>確保良好流動性，便於投資者交易</li>
                    <li><strong>風險控制：</strong>避免太冷門的標的很難入手</li>
                    <li><strong>實務考量：</strong>保證進出場的便利性</li>
                </ul>

                <h4>4. 成長動能 - 年增率前10強 (10分)</h4>
                <ul>
                    <li><strong>計算方式：</strong>單月營收年增率優異，從中選出前10強</li>
                    <li><strong>投資邏輯：</strong>在符合條件的股票中選出最強者</li>
                    <li><strong>動態排名：</strong>確保選到成長動能最強的標的</li>
                    <li><strong>實戰考量：</strong>縮減檔數，適合多數投資者資金規模</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>避免假突破的技術</h4>
                <p>策略運用FinLab的sustain方法，解決傳統突破策略的痛點：</p>
                <ul>
                    <li><strong>問題：</strong>只有近一天突破就買，可能買到假突破的標的</li>
                    <li><strong>解決：</strong>要求近5日內有2日以上創新高，確認突破強度</li>
                    <li><strong>優勢：</strong>不會錯過前幾天突破小拉回的好標的</li>
                    <li><strong>彈性：</strong>時間條件更有彈性，比較不會錯過飆股</li>
                </ul>

                <h4>月營收的台股優勢</h4>
                <ul>
                    <li><strong>台股獨特：</strong>月營收是台股獨特的基本面因子</li>
                    <li><strong>預判能力：</strong>常可先預判季財報表現</li>
                    <li><strong>即時性：</strong>每月10日前公告，數據更新及時</li>
                    <li><strong>影響力：</strong>台股玩家都知道月營收的影響性</li>
                </ul>

                <h4>雙重動能協同效應</h4>
                <ul>
                    <li><strong>基本面確認：</strong>營收創新高確保業績真實改善</li>
                    <li><strong>技術面確認：</strong>股價創新高確保市場資金認可</li>
                    <li><strong>協同放大：</strong>兩股力量同時作用產生強大推進力</li>
                    <li><strong>風險降低：</strong>雙重確認降低單一因子失效風險</li>
                </ul>

                <h3>📊 選股邏輯</h3>
                <ol>
                    <li><strong>業績篩選：</strong>近2月平均營收創12個月來新高</li>
                    <li><strong>技術篩選：</strong>近5日內有2日股價創200日新高</li>
                    <li><strong>流動性篩選：</strong>五日成交均量大於500張</li>
                    <li><strong>動能排名：</strong>從符合條件的股票中選出年增率前10強</li>
                    <li><strong>月度換股：</strong>每月營收公告截止日重新篩選</li>
                </ol>

                <h3>🎯 風險控制機制</h3>

                <h4>智慧風險控制措施</h4>
                <p>這個策略不僅聚焦於短期內業績與股價的雙重突破，而且通過設定明智的風險控制措施，如停損點和停利點，來保護投資者免受不必要的風險。此外，它還通過限制投資組合中單一股票的持股比例，來確保投資組合的多樣化，進一步降低風險。</p>

                <h4>具體風險控制設定</h4>
                <ul>
                    <li><strong>換股時機：</strong>月營收公告截止日換股</li>
                    <li><strong>停損設定：</strong>-20%停損保護下檔風險</li>
                    <li><strong>停利設定：</strong>80%停利鎖定獲利</li>
                    <li><strong>持股限制：</strong>單一股票持股上限25%</li>
                    <li><strong>投組多樣化：</strong>最多持有10檔股票分散風險</li>
                </ul>

                <h4>風險控制邏輯</h4>
                <ul>
                    <li><strong>避免重壓：</strong>position_limit=0.25避免單檔重壓</li>
                    <li><strong>自然避險：</strong>熊市時創新高標的少，持股比例自然降低</li>
                    <li><strong>波動控制：</strong>停損不建議設定太小，避免被洗掉</li>
                    <li><strong>獲利保護：</strong>80%停利確保大幅獲利時及時出場</li>
                </ul>

                <h3>⚡ 雙渦輪效應</h3>
                <p>策略名稱「營收股價雙渦輪」象徵著營收增長和股價上漲這兩股強大動力的結合：</p>
                <ul>
                    <li><strong>營收渦輪：</strong>基本面驅動，確保企業真實業績改善</li>
                    <li><strong>股價渦輪：</strong>技術面驅動，確保市場資金認可</li>
                    <li><strong>雙重加速：</strong>兩股力量同時作用，產生強大推進力</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實營收和交易數據</p>
                <ul style="color: red;">
                    <li><strong>月營收數據：</strong>目前使用價格趨勢模擬，真實數據需從公開資訊觀測站取得</li>
                    <li><strong>營收年增率：</strong>目前使用價格年增率模擬，真實數據需從月營收公告計算</li>
                    <li><strong>股價創新高：</strong>目前使用簡化邏輯，真實數據需要完整歷史價格數據</li>
                    <li><strong>成交量數據：</strong>目前使用模擬數據，真實數據需從交易所取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站月營收、證交所交易數據、FinMind營收API</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>積極型投資者：</strong>追求高收益、能承受較高風險的投資者</li>
                    <li><strong>動能投資者：</strong>偏好追蹤市場熱點和強勢股的投資者</li>
                    <li><strong>技術分析愛好者：</strong>重視技術突破和市場動能的投資者</li>
                    <li><strong>台股專精者：</strong>熟悉台股特色，善用月營收優勢的投資者</li>
                    <li><strong>短中期投資者：</strong>投資週期以月為單位的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期執行：</strong>每月營收公告截止日執行策略</li>
                    <li><strong>嚴格紀律：</strong>嚴格執行停損停利，避免情緒干擾</li>
                    <li><strong>資金管理：</strong>控制單檔持股比例，做好風險分散</li>
                    <li><strong>市場觀察：</strong>關注整體市場環境和資金流向</li>
                    <li><strong>持續學習：</strong>深入了解月營收和突破策略的技術細節</li>
                </ul>

                <h3>📚 學習資源</h3>
                <ul>
                    <li><strong>主要教學：</strong><a href="https://www.finlab.tw/revenue_and_price_engine_strategy/" target="_blank">月營收選股｜股價創新高｜新手必學的雙動能策略</a></li>
                    <li><strong>進階技術：</strong><a href="https://www.finlab.tw/breakthrough_stock_picking_strategies/" target="_blank">突破策略豆知識 | 如何避免假突破?</a></li>
                    <li><strong>技術細節：</strong>sustain方法的原理和應用</li>
                    <li><strong>實戰案例：</strong>雙渦輪策略的歷史回測分析</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>結合台股獨特的月營收優勢與技術突破分析，是新手必學的雙動能策略！</p>
                <p style="color: purple;"><strong>教學資源：</strong>完整的FinLab教學文章和技術細節說明</p>
            """,

            "景氣燈號加減碼": """
                <h2>🌡️ 景氣燈號加減碼策略 - 總經指標分批加減碼</h2>
                <p><strong>策略類型：</strong>總體經濟指標策略</p>
                <p><strong>核心理念：</strong>許多人都知道要在藍燈分批買進，但分批加減碼究竟要如何撰寫？這就是FinLab量化平台的優勢，可以自動調控加減碼，不限於固定部位，可靈活運用總經數據，把策略都自動化起來。</p>

                <h3>🎯 策略創新理念</h3>
                <p>通常寫策略都是一布林訊號通到底，100%持有。本策略突破傳統限制，回測函數當position的數值除了可為布林訊號，也可以填入數值當持有部位調控，例如0.2為持有20%。這種動態部位調控讓策略更貼近實戰，比起外面的總經課程，更具實用性。</p>

                <h3>📊 分批加減碼核心邏輯</h3>
                <p>示範的景氣燈號加減碼策略邏輯為分五批進場。只要近12個月內每一月景氣指標分數小於等於18時，分批買進20%部位。若景氣指標分數大於等於40時則全數賣出。舉例：若近12月內有3個月滿足條件，則持有60%，若近12月內有5個月以上滿足條件，則持有100%。</p>

                <h3>🌡️ 景氣燈號分批加減碼邏輯</h3>

                <h4>策略設計背後思維</h4>
                <p>這個策略設計的背後思維在如果近12月內景氣燈號低於18分的月份越少，代表脫離藍燈谷底越遠，若近12月內景氣燈號低於18分的月份越多，則代表貼近景氣谷底，利用訊號數量去加乘持股部位。</p>

                <h4>為何使用18分與40分？</h4>
                <p>另一個小重點在為何指標是用18與40分，而不是官方藍紅燈上下限的16分與38分？原因在策略若想要比人早一步，就要微調數值，且進入紅燈的熱絡階段，往往有所謂邪惡第五波，延遲出場有機會獲得更高報酬，而若用官方的16分當衰退指標，會發現低於16分的情況不多，所以往上微調，以增加訊號數。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>燈號區間</th><th>分數範圍</th><th>策略動作</th><th>投資邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>藍燈區間</strong></td>
                        <td>≤ 18分</td>
                        <td>每個低檔月份增加20%部位</td>
                        <td>景氣谷底，分批進場</td>
                    </tr>
                    <tr>
                        <td><strong>綠燈區間</strong></td>
                        <td>19-39分</td>
                        <td>維持現有部位，不加不減</td>
                        <td>景氣穩定，持有觀望</td>
                    </tr>
                    <tr>
                        <td><strong>紅燈區間</strong></td>
                        <td>≥ 40分</td>
                        <td>全數賣出，部位歸零</td>
                        <td>景氣過熱，獲利了結</td>
                    </tr>
                </table>

                <h3>📊 分批進場機制</h3>

                <h4>五批進場詳細規則</h4>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>進場批次</th><th>持股比例</th><th>觸發條件</th><th>投資邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>第1批</strong></td>
                        <td>20%</td>
                        <td>近12月內有1個月景氣指標≤18分</td>
                        <td>初步進入景氣低檔</td>
                    </tr>
                    <tr>
                        <td><strong>第2批</strong></td>
                        <td>40%</td>
                        <td>近12月內有2個月景氣指標≤18分</td>
                        <td>景氣低檔確認</td>
                    </tr>
                    <tr>
                        <td><strong>第3批</strong></td>
                        <td>60%</td>
                        <td>近12月內有3個月景氣指標≤18分</td>
                        <td>景氣持續低迷</td>
                    </tr>
                    <tr>
                        <td><strong>第4批</strong></td>
                        <td>80%</td>
                        <td>近12月內有4個月景氣指標≤18分</td>
                        <td>接近景氣谷底</td>
                    </tr>
                    <tr>
                        <td><strong>第5批</strong></td>
                        <td>100%</td>
                        <td>近12月內有5個月以上景氣指標≤18分</td>
                        <td>景氣谷底，滿倉進場</td>
                    </tr>
                </table>

                <h4>動態部位調控原理</h4>
                <ul>
                    <li><strong>累積效應：</strong>低檔月份越多，持股比例越高</li>
                    <li><strong>風險分散：</strong>分批進場降低單點進場風險</li>
                    <li><strong>趨勢跟隨：</strong>隨景氣變化動態調整部位</li>
                    <li><strong>自動化執行：</strong>完全量化，避免情緒干擾</li>
                </ul>

                <h3>💡 策略設計思維</h3>
                <ul>
                    <li><strong>比人早一步：</strong>使用18分和40分而非官方16分和38分，提前布局</li>
                    <li><strong>邪惡第五波：</strong>延遲出場有機會獲得紅燈階段的額外報酬</li>
                    <li><strong>訊號數量：</strong>用低檔月份數量加乘持股部位，越接近谷底持股越重</li>
                    <li><strong>動態調控：</strong>FinLab平台優勢，可自動調控加減碼而非單純布林訊號</li>
                </ul>

                <h3>🎯 投資標的選擇</h3>
                <p>策略主要投資台灣50 ETF (0050)，原因：</p>
                <ul>
                    <li><strong>代表性強：</strong>涵蓋台股市值前50大公司</li>
                    <li><strong>流動性佳：</strong>成交量大，買賣容易</li>
                    <li><strong>費用低廉：</strong>管理費用相對較低</li>
                    <li><strong>分散風險：</strong>避免個股風險，專注總經判斷</li>
                </ul>

                <h3>📈 策略優勢</h3>

                <h4>總經投資優勢</h4>
                <ul>
                    <li><strong>總經導向：</strong>基於台灣景氣燈號的總體經濟指標</li>
                    <li><strong>領先指標：</strong>景氣燈號可視為台灣總經的風向球</li>
                    <li><strong>逆向思維：</strong>藍燈反而是末跌段買點，紅燈是獲利了結點</li>
                    <li><strong>市場領先：</strong>市場經常領先經濟數據，當爛無可爛時反而容易出現轉機</li>
                </ul>

                <h4>技術實作優勢</h4>
                <ul>
                    <li><strong>動態調控：</strong>根據景氣狀況自動調整持股比例</li>
                    <li><strong>分散風險：</strong>分批進場降低單點進場風險</li>
                    <li><strong>參數優化：</strong>使用18分與40分而非官方16分與38分，提前布局</li>
                    <li><strong>自動化執行：</strong>完全量化，避免人性弱點干擾</li>
                </ul>

                <h4>回測績效優勢</h4>
                <p>回測結果發現該策略可以取得比長期持有0050還好的績效，熊市時期的2007與2022回檔幅度較小，波動曲線更為漂亮。</p>
                <ul>
                    <li><strong>超額報酬：</strong>長期表現優於單純持有0050</li>
                    <li><strong>風險控制：</strong>熊市期間回檔幅度較小</li>
                    <li><strong>波動平滑：</strong>整體波動曲線更為平穩</li>
                    <li><strong>實戰驗證：</strong>經過多個景氣循環驗證有效</li>
                </ul>

                <h3>⚠️ 策略限制</h3>
                <ul>
                    <li><strong>反應滯後：</strong>景氣燈號有一定滯後性</li>
                    <li><strong>長期持有：</strong>需要耐心等待景氣循環</li>
                    <li><strong>單一市場：</strong>僅適用於台灣股市</li>
                    <li><strong>ETF限制：</strong>主要投資0050，缺乏個股選擇</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實景氣燈號數據</p>
                <ul style="color: red;">
                    <li><strong>景氣對策信號：</strong>目前使用技術指標模擬，真實數據需從國發會取得</li>
                    <li><strong>月度數據：</strong>目前使用日K線模擬月度數據，真實數據需要月度景氣指標</li>
                    <li><strong>歷史數據：</strong>需要完整的景氣燈號歷史數據進行回測驗證</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>國家發展委員會景氣指標、FinMind總經數據API</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>總經投資者：</strong>重視總體經濟分析的投資者</li>
                    <li><strong>長期投資者：</strong>投資週期以年為單位的投資者</li>
                    <li><strong>穩健型投資者：</strong>追求穩定報酬，重視風險控制的投資者</li>
                    <li><strong>ETF投資者：</strong>偏好ETF投資，不想選個股的投資者</li>
                    <li><strong>量化投資者：</strong>偏好系統化、自動化投資策略的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期檢視：</strong>每月關注景氣燈號更新，調整持股比例</li>
                    <li><strong>嚴格執行：</strong>嚴格按照策略規則執行，避免情緒干擾</li>
                    <li><strong>長期持有：</strong>景氣循環週期較長，需要耐心等待</li>
                    <li><strong>資金規劃：</strong>預留足夠資金進行分批加碼</li>
                    <li><strong>總經學習：</strong>持續學習總體經濟知識，提升判斷能力</li>
                </ul>

                <h3>📚 學習資源</h3>
                <ul>
                    <li><strong>主要教學：</strong><a href="https://www.finlab.tw/tw_business_indicator_changed_weight_strategy/" target="_blank">用Python回測總經指標(3)｜台灣景氣燈號｜加減碼策略</a></li>
                    <li><strong>技術細節：</strong>動態部位調控的程式實作</li>
                    <li><strong>回測分析：</strong>策略績效與風險分析</li>
                    <li><strong>總經知識：</strong>景氣燈號和景氣循環的學習</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>FinLab量化平台的優勢，可自動調控加減碼，比外面的總經課程更具實用性！</p>
                <p style="color: purple;"><strong>教學資源：</strong><a href="https://www.finlab.tw/tw_business_indicator_changed_weight_strategy/" target="_blank">完整的Python回測總經指標教學</a></p>
            """,

            "現金流價值成長": """
                <h2>💰 現金流價值成長策略 - 現金流量因子延伸的價值成長股策略</h2>
                <p><strong>策略類型：</strong>現金流量價值策略</p>
                <p><strong>核心理念：</strong>從現金流量因子延伸的價值成長股策略，過濾成長過高的瘋狗，留下穩健成長的企業。技術面要求價量在長期趨勢線上，確保投資標的具備良好的現金流健康度和市場認可度。</p>

                <h3>🎯 策略創新理念</h3>
                <p>這個策略巧妙結合了現金流量表分析的精髓與價值成長投資的理念。相較於損益表，現金流量表受到的關注比較少，但台股很多地雷股從現金流量表可以發現營運出問題的徵兆。本策略透過嚴格的現金流篩選，避開財務危機企業，專注於現金流健康且具備成長潛力的優質企業。</p>

                <h3>💡 避開地雷股的智慧</h3>
                <p>策略設計的核心在於避開那些「成長過高的瘋狗」，這些企業往往營收成長亮眼，但現金流卻出現問題。透過現金流量表的深度分析，我們可以發現營運出問題的徵兆，比如長期營運現金流為負數，代表本業無法產生現金，或是投資現金流為正數，可能是財務危機嚴重到要變賣祖產來還債。</p>

                <h3>💰 現金流量因子核心條件</h3>

                <h4>最佳現金流組合 (+ + -)</h4>
                <p>根據FinLab回測結果，表現最好的組合是營運現金流、投資現金流為正數、融資現金流為負數的組合，年化約12%報酬率。這組合顯示公司的營運獲利能帶回實質現金，企業不需要額外投入資本支出，不需要向外部融資，就能隨本業擴展持續賺回現金，是比較正向且安全的循環。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>現金流類型</th><th>條件要求</th><th>投資意義</th><th>風險評估</th>
                    </tr>
                    <tr>
                        <td><strong>營業現金流</strong></td>
                        <td>> 0 (正數)</td>
                        <td>公司透過本業營運實際流入現金</td>
                        <td>若為負值則盈餘品質有風險</td>
                    </tr>
                    <tr>
                        <td><strong>投資現金流</strong></td>
                        <td>> 0 (正數)</td>
                        <td>投資活動有正收益（較少見但優質）</td>
                        <td>代表不需額外資本支出</td>
                    </tr>
                    <tr>
                        <td><strong>融資現金流</strong></td>
                        <td>< 0 (負數)</td>
                        <td>減少外部融資，財務獨立</td>
                        <td>不依賴外部借款</td>
                    </tr>
                </table>

                <h4>現金流量表快速解讀</h4>
                <ul>
                    <li><strong>營業現金流：</strong>意義是「公司透過本業營運實際流入的現金」。若為正值，則顯示公司營運有持續流入現金，營業現金流若為負值則顯示公司營運的現金是持續流出的狀態，要非常小心盈餘品質的風險。</li>
                    <li><strong>投資現金流：</strong>若為正數，代表變賣資產較多。若為負數，代表對外投資較多，常見於企業用於擴大資本支出，若將來擴大營運帶來的投資現金流能大於營業現金流，是比較好的循環，若是小於的情況，則可能擴展失敗，潛在入不敷出的風險。</li>
                    <li><strong>籌資現金流：</strong>若為正數，代表向外部借款較多，常見為向銀行借款、向股東增資、發行可轉債。若為負數，則還款或發放股利較多。</li>
                </ul>

                <h3>📈 技術面要求 - 價量在長期趨勢線上</h3>

                <h4>長期趨勢確認</h4>
                <p>技術面要求價量在長期趨勢線上，這是策略的重要篩選條件，確保所選股票不僅基本面優秀，技術面也獲得市場認可。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>技術指標</th><th>具體要求</th><th>投資邏輯</th><th>風險控制</th>
                    </tr>
                    <tr>
                        <td><strong>長期趨勢線</strong></td>
                        <td>收盤價 > 40日移動平均線</td>
                        <td>確保股價在長期上升趨勢中</td>
                        <td>避免逆勢操作</td>
                    </tr>
                    <tr>
                        <td><strong>價量配合</strong></td>
                        <td>成交量 > 60日平均量</td>
                        <td>確保有足夠資金關注</td>
                        <td>避免流動性不足</td>
                    </tr>
                    <tr>
                        <td><strong>流動性保障</strong></td>
                        <td>5日平均量 ≥ 20萬股</td>
                        <td>保證進出場便利性</td>
                        <td>降低衝擊成本</td>
                    </tr>
                </table>

                <h4>技術面篩選邏輯</h4>
                <ul>
                    <li><strong>趨勢跟隨：</strong>只在上升趨勢中操作，避免逆勢投資</li>
                    <li><strong>量價配合：</strong>成交量放大確認市場認可度</li>
                    <li><strong>流動性考量：</strong>確保有足夠的交易量支撐</li>
                    <li><strong>市場確認：</strong>技術面與基本面雙重確認</li>
                </ul>

                <h3>🎯 基本面篩選 - 穩健成長企業</h3>

                <h4>過濾成長過高的瘋狗</h4>
                <p>策略的核心在於「過濾成長過高的瘋狗，留下穩健成長的企業」。這些瘋狗企業往往營收成長亮眼，但現金流、獲利能力或財務結構存在問題，容易成為地雷股。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>篩選條件</th><th>具體要求</th><th>投資邏輯</th><th>風險控制</th>
                    </tr>
                    <tr>
                        <td><strong>營收成長動能</strong></td>
                        <td>近3月平均營收 > 近12月平均營收</td>
                        <td>確保業績有改善趨勢</td>
                        <td>避免業績衰退股</td>
                    </tr>
                    <tr>
                        <td><strong>獲利能力</strong></td>
                        <td>營業利益率 ≥ 3%</td>
                        <td>確保企業有基本獲利能力</td>
                        <td>避免虧損或微利企業</td>
                    </tr>
                    <tr>
                        <td><strong>成長控制</strong></td>
                        <td>0% < 去年同月增減率 < 100%</td>
                        <td>過濾成長過高的瘋狗股</td>
                        <td>避免投機炒作標的</td>
                    </tr>
                    <tr>
                        <td><strong>穩健成長</strong></td>
                        <td>營收成長但不過度</td>
                        <td>專注可持續成長企業</td>
                        <td>降低泡沫破滅風險</td>
                    </tr>
                </table>

                <h4>基本面篩選重點</h4>
                <ul>
                    <li><strong>年增率控制：</strong>0% < 去年同月增減率 < 100%（過濾瘋狗股）</li>
                    <li><strong>營業利益率：</strong>≥ 3%，確保獲利能力</li>
                    <li><strong>穩健成長：</strong>避免成長過高的投機股</li>
                    <li><strong>現金流健康：</strong>三現金流同時滿足最佳組合條件</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>三重篩選機制</h4>
                <ul>
                    <li><strong>現金流篩選：</strong>三現金流同時滿足條件，極度嚴格的財務健康度要求</li>
                    <li><strong>過濾瘋狗：</strong>年增率上限100%，避免投機炒作和泡沫股</li>
                    <li><strong>技術確認：</strong>要求價量在長期趨勢線上，市場認可度確認</li>
                    <li><strong>穩健成長：</strong>專注於可持續的穩健成長企業，而非短期爆發股</li>
                </ul>

                <h4>地雷股識別能力</h4>
                <p>策略的核心優勢在於能夠識別和避開地雷股。台股很多地雷股從現金流量表可以發現營運出問題的徵兆，比如長期營運現金流為負數，可見本業無法產生現金的能力，且近期投資現金流為正數，財務危機嚴重到要變賣祖產來還債。</p>

                <h4>價值成長結合</h4>
                <ul>
                    <li><strong>價值面：</strong>嚴格的現金流和獲利能力要求</li>
                    <li><strong>成長面：</strong>營收成長動能和市場認可度</li>
                    <li><strong>技術面：</strong>價量配合和趨勢確認</li>
                    <li><strong>風險控制：</strong>多重篩選降低投資風險</li>
                </ul>

                <h3>📊 選股流程</h3>
                <ol>
                    <li><strong>現金流篩選：</strong>營業CF>0, 投資CF>0, 融資CF<0</li>
                    <li><strong>技術面篩選：</strong>收盤價 > 40日線</li>
                    <li><strong>營收成長篩選：</strong>近3月 > 近12月平均</li>
                    <li><strong>年增率篩選：</strong>0% < 年增率 < 100%</li>
                    <li><strong>獲利能力篩選：</strong>營業利益率 ≥ 3%</li>
                    <li><strong>成交量篩選：</strong>量能充足且活躍</li>
                    <li><strong>最終選股：</strong>選取前10檔最小成交量股票</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>月度重新平衡：</strong>每月重新篩選和調整持股</li>
                    <li><strong>持股上限：</strong>單一股票持股上限20%</li>
                    <li><strong>停損設定：</strong>10%停損保護資本</li>
                    <li><strong>停利設定：</strong>100%停利鎖定獲利</li>
                    <li><strong>投組分散：</strong>最多持有10檔股票</li>
                </ul>

                <h3>⭐ 策略優勢</h3>
                <ul>
                    <li><strong>現金流品質：</strong>嚴格的現金流篩選確保企業財務健康</li>
                    <li><strong>穩健成長：</strong>過濾投機股，專注可持續成長</li>
                    <li><strong>技術確認：</strong>價量配合確保市場認可</li>
                    <li><strong>風險控制：</strong>完善的停損停利機制</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實現金流量表數據</p>
                <ul style="color: red;">
                    <li><strong>營業現金流：</strong>目前使用價格趨勢和成交量模擬，真實數據需從現金流量表取得</li>
                    <li><strong>投資現金流：</strong>目前使用成交量增長模擬，真實數據需從現金流量表取得</li>
                    <li><strong>融資現金流：</strong>目前使用價格波動率模擬，真實數據需從現金流量表取得</li>
                    <li><strong>營業利益率：</strong>目前使用價格穩定度模擬，真實數據需從損益表取得</li>
                    <li><strong>月營收數據：</strong>目前使用價格趨勢模擬，真實數據需從月營收公告取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>公開資訊觀測站現金流量表、FinMind財報API、月營收公告</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>價值投資者：</strong>重視現金流品質和財務健康度的投資者</li>
                    <li><strong>穩健型投資者：</strong>追求穩定成長，避免投機炒作的投資者</li>
                    <li><strong>基本面分析者：</strong>深度分析財務報表的專業投資者</li>
                    <li><strong>風險控制者：</strong>重視風險管理和地雷股識別的投資者</li>
                    <li><strong>長期投資者：</strong>投資週期較長，重視企業質地的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期檢視：</strong>每月檢視現金流量表和財務數據更新</li>
                    <li><strong>深度研究：</strong>深入了解現金流量表的分析方法</li>
                    <li><strong>嚴格執行：</strong>嚴格按照篩選條件，不因市場情緒放寬標準</li>
                    <li><strong>風險監控：</strong>持續監控持股企業的現金流變化</li>
                    <li><strong>組合管理：</strong>適當分散投資，避免過度集中</li>
                </ul>

                <h3>📚 學習資源</h3>
                <ul>
                    <li><strong>主要教學：</strong><a href="https://www.finlab.tw/cashflow_backtest_easy/" target="_blank">現金流量表超簡單策略開發</a></li>
                    <li><strong>技術細節：</strong>現金流量因子組合回測分析</li>
                    <li><strong>實戰案例：</strong>地雷股識別和避險技巧</li>
                    <li><strong>現金流分析：</strong>學習現金流量表的結構和分析方法</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>從現金流量因子延伸，過濾成長過高的瘋狗，留下穩健成長的企業！</p>
                <p style="color: purple;"><strong>教學資源：</strong><a href="https://www.finlab.tw/cashflow_backtest_easy/" target="_blank">現金流量表超簡單策略開發</a></p>
            """,

            "藏獒外掛大盤指針": """
                <h2>🐕 藏獒外掛大盤指針策略 - 藏獒策略加上大盤濾網</h2>
                <p><strong>策略類型：</strong>技術面突破策略 + 大盤濾網</p>
                <p><strong>核心理念：</strong>藏獒策略加上大盤濾網，避開大盤中期回檔風險，讓波動更穩定一點。這是對經典藏獒策略的重要升級，透過大盤指針的風險控制機制，在保持強勢突破特性的同時，大幅降低系統性風險。</p>

                <h3>🎯 策略創新理念</h3>
                <p>藏獒策略以其強勢的創新高選股邏輯聞名，能夠捕捉到市場中最強勢的股票。然而，在大盤環境不佳時，即使是最強勢的個股也難以抵抗系統性風險。本策略透過加入大盤指針濾網，在大盤不利時主動避險，甚至反手做空，讓整體投資組合的波動更加穩定。</p>

                <h3>🔄 雙重策略結合</h3>
                <p>這個策略巧妙結合了兩個核心元素：<strong>藏獒的攻擊性</strong>和<strong>大盤指針的防守性</strong>。在大盤環境良好時，發揮藏獒策略的強勢突破優勢；在大盤環境轉差時，立即切換為防守模式，避開中期回檔風險。</p>

                <h3>🐕 藏獒核心條件（原策略基礎）</h3>

                <h4>藏獒策略的強勢基因</h4>
                <p>藏獒策略是FinLab的經典強勢突破策略，專門捕捉創新高且基本面健康的強勢股。其核心理念是「強者恆強」，透過嚴格的篩選條件，找出市場中最具爆發力的投資標的。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>篩選條件</th><th>權重</th><th>具體要求</th><th>投資邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>股價創年新高</strong></td>
                        <td>25分</td>
                        <td>收盤價 = 250日滾動最高價</td>
                        <td>確保股價處於最強勢狀態</td>
                    </tr>
                    <tr>
                        <td><strong>營收衰退排除</strong></td>
                        <td>20分</td>
                        <td>排除月營收連3月衰退10%以上</td>
                        <td>避開基本面惡化的股票</td>
                    </tr>
                    <tr>
                        <td><strong>成長趨勢過老排除</strong></td>
                        <td>15分</td>
                        <td>排除12月內8月年增率>60%</td>
                        <td>避開成長過度的泡沫股</td>
                    </tr>
                    <tr>
                        <td><strong>營收底部確認</strong></td>
                        <td>15分</td>
                        <td>近月營收脫離近年谷底</td>
                        <td>確保業績有復甦跡象</td>
                    </tr>
                    <tr>
                        <td><strong>月增率條件</strong></td>
                        <td>10分</td>
                        <td>單月營收月增率連續3月>-40%</td>
                        <td>避免營收急劇惡化</td>
                    </tr>
                    <tr>
                        <td><strong>流動性條件</strong></td>
                        <td>10分</td>
                        <td>10日平均成交量 > 20萬股</td>
                        <td>確保足夠的交易流動性</td>
                    </tr>
                </table>

                <h4>藏獒策略的核心優勢</h4>
                <ul>
                    <li><strong>強勢突破：</strong>專注於創新高的最強勢股票</li>
                    <li><strong>基本面健康：</strong>嚴格排除營收惡化的企業</li>
                    <li><strong>成長合理：</strong>避開過度成長的泡沫股</li>
                    <li><strong>流動性佳：</strong>確保進出場的便利性</li>
                </ul>

                <h3>🎯 大盤指針濾網（核心創新功能）</h3>

                <h4>大盤環境智能識別</h4>
                <p>大盤指針是策略的核心創新，透過FinLab台股市場指標來判斷整體市場環境。這個濾網的設計目的是「避開大盤中期回檔風險，讓波動更穩定一點」，是對藏獒策略的重要升級。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>大盤指針分數</th><th>市場環境</th><th>策略動作</th><th>風險控制邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>≥ 4分</strong></td>
                        <td>多頭環境</td>
                        <td>執行藏獒選股策略</td>
                        <td>市場環境良好，發揮攻擊優勢</td>
                    </tr>
                    <tr>
                        <td><strong>< 4分</strong></td>
                        <td>空頭/震盪環境</td>
                        <td>多單出場，反手做空指數</td>
                        <td>避開系統性風險，主動避險</td>
                    </tr>
                </table>

                <h4>動態多空切換機制</h4>
                <ul>
                    <li><strong>多頭模式：</strong>大盤指針≥4分時，執行完整藏獒選股策略</li>
                    <li><strong>空頭避險：</strong>大盤指針<4分時，立即出清多單並反手做空指數</li>
                    <li><strong>即時監控：</strong>持續監控大盤指針變化，動態調整部位</li>
                    <li><strong>風險優先：</strong>寧可錯過機會，也要避開系統性風險</li>
                </ul>

                <h4>大盤指針的優勢</h4>
                <ul>
                    <li><strong>系統性風險控制：</strong>有效避開大盤中期回檔</li>
                    <li><strong>波動穩定化：</strong>讓整體投資組合波動更加平穩</li>
                    <li><strong>主動避險：</strong>不只是停損，更是主動獲利</li>
                    <li><strong>市場適應性：</strong>根據市場環境自動調整策略</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>核心創新理念</h4>
                <p>這個策略的最大創新在於將「攻擊性」和「防守性」完美結合。藏獒策略本身就是極具攻擊性的強勢突破策略，但加上大盤指針濾網後，讓整個策略具備了智能的風險控制能力。</p>

                <h4>四大創新特色</h4>
                <ul>
                    <li><strong>雙重保護：</strong>個股強勢突破 + 大盤環境確認，雙重篩選確保投資品質</li>
                    <li><strong>主動避險：</strong>大盤轉空時不只是停損出場，更是反手做空指數主動獲利</li>
                    <li><strong>波動穩定：</strong>避開大盤中期回檔風險，讓投資組合波動更加穩定</li>
                    <li><strong>智能切換：</strong>根據市場環境自動在攻擊模式和防守模式間切換</li>
                </ul>

                <h4>相較於原藏獒策略的優勢</h4>
                <ul>
                    <li><strong>系統性風險控制：</strong>原藏獒策略在大盤回檔時仍會受傷，本策略可主動避開</li>
                    <li><strong>回檔幅度降低：</strong>透過大盤濾網，大幅降低投資組合的最大回檔</li>
                    <li><strong>風險調整報酬提升：</strong>在相似報酬下，風險大幅降低</li>
                    <li><strong>心理壓力減輕：</strong>投資者不用承受大盤回檔時的心理煎熬</li>
                </ul>

                <h3>📊 策略執行流程</h3>

                <h4>第一階段：藏獒基礎篩選</h4>
                <ol>
                    <li><strong>創新高篩選：</strong>找出收盤價等於250日滾動最高價的股票</li>
                    <li><strong>營收健康檢查：</strong>排除營收連續衰退和過度成長的股票</li>
                    <li><strong>基本面確認：</strong>確保營收脫離谷底且月增率健康</li>
                    <li><strong>流動性篩選：</strong>確保10日平均成交量大於20萬股</li>
                    <li><strong>建立候選清單：</strong>完成藏獒策略的基礎篩選</li>
                </ol>

                <h4>第二階段：大盤指針確認</h4>
                <ol>
                    <li><strong>大盤指針評估：</strong>檢查FinLab台股市場指標當前分數</li>
                    <li><strong>環境判斷：</strong>確認是否達到4分以上的多頭門檻</li>
                    <li><strong>策略決策：</strong>根據大盤指針決定執行多頭或空頭策略</li>
                </ol>

                <h4>第三階段：部位執行</h4>
                <ul>
                    <li><strong>多頭模式（大盤指針≥4分）：</strong>
                        <ul>
                            <li>從藏獒候選清單中選擇成交量較小的5檔股票</li>
                            <li>建立多頭部位，單檔持股上限33%</li>
                            <li>持續監控大盤指針變化</li>
                        </ul>
                    </li>
                    <li><strong>空頭模式（大盤指針<4分）：</strong>
                        <ul>
                            <li>立即出清所有多頭部位</li>
                            <li>反手做空指數ETF進行避險</li>
                            <li>等待大盤指針回升至4分以上</li>
                        </ul>
                    </li>
                </ul>

                <h4>第四階段：動態調整</h4>
                <ol>
                    <li><strong>月度重新平衡：</strong>每月重新執行完整篩選流程</li>
                    <li><strong>大盤指針監控：</strong>即時監控大盤指針變化</li>
                    <li><strong>部位調整：</strong>根據指針變化動態調整多空部位</li>
                    <li><strong>風險控制：</strong>嚴格執行停損停利機制</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>大盤濾網：</strong>大盤指針<4分時停止多頭操作</li>
                    <li><strong>反手避險：</strong>做空台灣50反向ETF (00632R)</li>
                    <li><strong>持股上限：</strong>單一股票持股上限33%</li>
                    <li><strong>月度重新平衡：</strong>每月重新篩選和調整持股</li>
                    <li><strong>動態部位：</strong>根據大盤環境動態調整多空比例</li>
                </ul>

                <h3>⚡ 大盤指針說明</h3>
                <p>FinLab台股市場指標是綜合多項技術和基本面因子的市場評分系統：</p>
                <ul>
                    <li><strong>評分範圍：</strong>0-10分，4分為多空分界點</li>
                    <li><strong>≥4分：</strong>市場環境良好，適合多頭操作</li>
                    <li><strong><4分：</strong>市場環境不佳，建議避險或做空</li>
                    <li><strong>動態調整：</strong>隨市場變化即時更新評分</li>
                </ul>

                <h3>📈 策略優勢</h3>
                <ul>
                    <li><strong>強勢突破：</strong>保留藏獒策略的強勢突破特性</li>
                    <li><strong>風險控制：</strong>大盤濾網有效避開系統性風險</li>
                    <li><strong>波動穩定：</strong>避開大盤中期回檔，降低組合波動</li>
                    <li><strong>主動避險：</strong>大盤轉空時主動做空避險</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實大盤指標和營收數據</p>
                <ul style="color: red;">
                    <li><strong>大盤指標：</strong>目前使用技術指標模擬，真實數據需從FinLab ETL取得</li>
                    <li><strong>月營收數據：</strong>目前使用價格趨勢模擬，真實數據需從月營收公告取得</li>
                    <li><strong>營收年增率：</strong>目前使用價格年增率模擬，真實數據需從營收公告計算</li>
                    <li><strong>營收月增率：</strong>目前使用價格月增率模擬，真實數據需從營收公告計算</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinLab ETL大盤指標、公開資訊觀測站月營收、00632R反向ETF</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>積極型投資者：</strong>追求強勢突破但重視風險控制的投資者</li>
                    <li><strong>技術分析愛好者：</strong>偏好技術突破策略的投資者</li>
                    <li><strong>風險控制者：</strong>希望在追求高報酬的同時控制回檔風險的投資者</li>
                    <li><strong>多空操作者：</strong>能夠接受多空切換操作的投資者</li>
                    <li><strong>藏獒策略用戶：</strong>原本使用藏獒策略但希望降低波動的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>

                <h4>操作建議</h4>
                <ul>
                    <li><strong>嚴格執行：</strong>嚴格按照大盤指針信號執行多空切換</li>
                    <li><strong>即時監控：</strong>密切關注大盤指針的變化</li>
                    <li><strong>心理準備：</strong>做好多空頻繁切換的心理準備</li>
                    <li><strong>資金管理：</strong>控制單檔持股比例，做好風險分散</li>
                    <li><strong>技術學習：</strong>深入了解藏獒策略和大盤指針的原理</li>
                </ul>

                <h4>進階技巧</h4>
                <ul>
                    <li><strong>指針優化：</strong>可以測試不同的大盤指針門檻設定</li>
                    <li><strong>避險工具：</strong>可以嘗試不同的避險工具和方法</li>
                    <li><strong>選股優化：</strong>可以調整藏獒策略的篩選條件</li>
                    <li><strong>部位管理：</strong>可以優化多空部位的分配比例</li>
                </ul>

                <h3>📚 學習資源</h3>

                <h4>相關策略學習</h4>
                <ul>
                    <li><strong>藏獒策略：</strong>學習原始藏獒策略的選股邏輯和技術細節</li>
                    <li><strong>大盤指針：</strong>了解FinLab台股市場指標的構成和應用</li>
                    <li><strong>多空操作：</strong>學習多空切換的操作技巧和心理建設</li>
                    <li><strong>風險管理：</strong>掌握系統性風險的識別和控制方法</li>
                </ul>

                <h4>技術分析學習</h4>
                <ul>
                    <li><strong>創新高策略：</strong>學習強勢突破的技術分析方法</li>
                    <li><strong>市場環境判斷：</strong>學習如何判斷大盤多空環境</li>
                    <li><strong>避險策略：</strong>學習各種避險工具的使用方法</li>
                    <li><strong>波動控制：</strong>學習如何降低投資組合波動</li>
                </ul>

                <h3>🔮 策略發展方向</h3>

                <h4>指標優化</h4>
                <ul>
                    <li><strong>大盤指針改進：</strong>優化大盤指針的準確性和敏感度</li>
                    <li><strong>多重指標：</strong>結合更多市場指標提升判斷準確性</li>
                    <li><strong>動態門檻：</strong>根據市場環境動態調整多空門檻</li>
                    <li><strong>領先指標：</strong>加入更多領先指標提前預警</li>
                </ul>

                <h4>策略升級</h4>
                <ul>
                    <li><strong>多層避險：</strong>建立多層次的風險控制機制</li>
                    <li><strong>智能切換：</strong>運用AI技術優化多空切換時機</li>
                    <li><strong>部位優化：</strong>動態優化多空部位的分配比例</li>
                    <li><strong>成本控制：</strong>降低頻繁切換的交易成本</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>藏獒策略加上大盤濾網，避開大盤中期回檔風險，讓波動更穩定一點！</p>
                <p style="color: purple;"><strong>核心優勢：</strong>強勢突破 + 智能避險 = 攻守兼備的完美策略</p>
            """,

            "精選00733強勢股": """
                <h2>📊 精選00733強勢股策略 - 超越富邦臺灣中小ETF的選股策略</h2>
                <p><strong>策略類型：</strong>ETF成分股優化策略</p>
                <p><strong>核心理念：</strong>富邦臺灣中小ETF從2018年到現在，平均每年報酬率為25％，尤其是2020年後，那績效簡直是一飛衝天！本策略依照公開說明書重新撰寫選股條件，發現了ETF被外力拉升的有趣現象，並利用這個現象輕鬆超越00733！</p>

                <h3>🎯 策略發現與假說</h3>
                <p>這檔ETF於2018年問世，當時在開發的時候，是不是如此卓越？我有點懷疑。所以依照公開說明書，重新撰寫它的選股條件，結果發現，雖然有超額報酬，但絕對沒有2018年後這麼顯著。</p>

                <h3>🔍 外力拉升現象探討</h3>
                <p><strong>核心疑問：</strong>究竟是真的買到飆股，還是因為公布了這群股票，才變成飆股？</p>
                <p>不排除有一些其他的外力，才讓這檔ETF的效果出奇的好！並且根據以上的假說，我找到了一個有趣的現象，可以算是ETF被外力拉升的間接證據，甚至我們可以利用這個現象，輕鬆超越00733！</p>

                <h3>📊 00733選股邏輯完整重現</h3>

                <h4>富邦臺灣中小ETF選股機制</h4>
                <p>00733追蹤「中小A級動能50指數」，專門鎖定具有動能、股性活潑的中小型股。其選股邏輯設定本身即已幫投資人主動篩選出超額報酬股，與一般被動型ETF不同。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>篩選條件</th><th>權重</th><th>具體要求</th><th>投資邏輯</th>
                    </tr>
                    <tr>
                        <td><strong>Alpha值檢查</strong></td>
                        <td>25分</td>
                        <td>Alpha = CAGR - Beta × 基準CAGR > 0</td>
                        <td>確保超額報酬能力</td>
                    </tr>
                    <tr>
                        <td><strong>Beta值檢查</strong></td>
                        <td>20分</td>
                        <td>使用TALib計算20日Beta值 > 0</td>
                        <td>確保與市場正相關</td>
                    </tr>
                    <tr>
                        <td><strong>獲利能力檢查</strong></td>
                        <td>20分</td>
                        <td>經常稅後淨利 > 0</td>
                        <td>確保基本獲利能力</td>
                    </tr>
                    <tr>
                        <td><strong>上市時間檢查</strong></td>
                        <td>15分</td>
                        <td>上市超過一年（250個交易日）</td>
                        <td>避開新上市股票風險</td>
                    </tr>
                    <tr>
                        <td><strong>成交量排名檢查</strong></td>
                        <td>10分</td>
                        <td>過去一年成交量排名前30%</td>
                        <td>確保股性活潑</td>
                    </tr>
                    <tr>
                        <td><strong>流動性檢查</strong></td>
                        <td>10分</td>
                        <td>近3月平均成交量>1000萬股 或 流通係數>0.06</td>
                        <td>確保交易流動性</td>
                    </tr>
                </table>

                <h4>選股範圍限制</h4>
                <ul>
                    <li><strong>選取範圍：</strong>臺灣證交所上市、市值前50大以外的股票</li>
                    <li><strong>中小型定位：</strong>專注於中小型股的成長潛力</li>
                    <li><strong>動能篩選：</strong>具有動能、股性活潑的標的</li>
                    <li><strong>成分股數：</strong>最終選出50檔成分股</li>
                </ul>

                <h3>🎯 ETF外力拉升現象深度分析</h3>

                <h4>績效異常現象</h4>
                <p><strong>驚人表現：</strong>富邦臺灣中小ETF從2018年到現在，平均每年報酬率為25％，尤其是2020年後，那績效簡直是一飛衝天！</p>

                <h4>策略驗證發現</h4>
                <p>依照公開說明書，重新撰寫它的選股條件，結果發現：</p>
                <ul>
                    <li><strong>回測結果：</strong>雖然有超額報酬，但絕對沒有2018年後這麼顯著</li>
                    <li><strong>績效落差：</strong>實際ETF表現遠超過理論選股邏輯的回測結果</li>
                    <li><strong>時間差異：</strong>2018年問世時的表現與後期表現有明顯差異</li>
                </ul>

                <h4>外力拉升假說</h4>
                <p><strong>核心疑問：</strong>究竟是真的買到飆股，還是因為公布了這群股票，才變成飆股？</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>假說類型</th><th>可能原因</th><th>影響機制</th><th>間接證據</th>
                    </tr>
                    <tr>
                        <td><strong>資金追蹤效應</strong></td>
                        <td>投資人跟買ETF成分股</td>
                        <td>公布清單後資金湧入</td>
                        <td>成分股異常上漲</td>
                    </tr>
                    <tr>
                        <td><strong>機構法人效應</strong></td>
                        <td>法人大量申購ETF</td>
                        <td>被動推升成分股價格</td>
                        <td>ETF規模快速成長</td>
                    </tr>
                    <tr>
                        <td><strong>市場關注效應</strong></td>
                        <td>媒體報導和市場關注</td>
                        <td>提升成分股知名度</td>
                        <td>交易量明顯增加</td>
                    </tr>
                    <tr>
                        <td><strong>自我實現效應</strong></td>
                        <td>預期成為飆股而買入</td>
                        <td>預期推動價格上漲</td>
                        <td>績效超越基本面</td>
                    </tr>
                </table>

                <h4>利用現象的策略思維</h4>
                <p>不排除有一些其他的外力，才讓這檔ETF的效果出奇的好！並且根據以上的假說，我找到了一個有趣的現象，可以算是ETF被外力拉升的間接證據，甚至我們可以利用這個現象，輕鬆超越00733！</p>

                <ul>
                    <li><strong>權重集中策略：</strong>選擇權重最大的5檔標的，放大外力效應</li>
                    <li><strong>提前布局：</strong>在ETF調整前提前布局重點成分股</li>
                    <li><strong>資金效率：</strong>集中投資獲得更高的資金效率</li>
                    <li><strong>超越ETF：</strong>利用外力拉升現象輕鬆超越00733表現</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>核心創新理念</h4>
                <p>這個策略的最大創新在於「逆向工程」的思維，透過解構成功ETF的選股邏輯，發現市場異常現象，並利用這個現象創造超額報酬。</p>

                <h4>四大創新特色</h4>
                <ul>
                    <li><strong>ETF解構：</strong>重新撰寫00733選股條件，去除ETF包裝，直接投資核心邏輯</li>
                    <li><strong>精選強勢：</strong>從50檔成分股中選出權重最大的5檔，集中火力投資</li>
                    <li><strong>外力利用：</strong>利用ETF被外力拉升的現象獲利，將市場異常轉為優勢</li>
                    <li><strong>超越ETF：</strong>集中投資強勢股，期望以更高效率超越原ETF表現</li>
                </ul>

                <h4>相較於直接投資00733的優勢</h4>
                <ul>
                    <li><strong>集中效應：</strong>只投資最強勢的5檔，放大優質標的影響</li>
                    <li><strong>成本優勢：</strong>避免ETF管理費，降低投資成本</li>
                    <li><strong>靈活性：</strong>可以自主調整持股比例和時機</li>
                    <li><strong>透明度：</strong>完全掌握投資標的和邏輯</li>
                </ul>

                <h3>📈 策略執行流程</h3>

                <h4>第一階段：基礎篩選（複製00733邏輯）</h4>
                <ol>
                    <li><strong>市值範圍篩選：</strong>排除市值前50大的大盤股，專注中小型股</li>
                    <li><strong>上市時間篩選：</strong>上市超過250個交易日，避開新股風險</li>
                    <li><strong>獲利能力篩選：</strong>經常稅後淨利 > 0，確保基本獲利能力</li>
                    <li><strong>流動性篩選：</strong>近3月平均成交量>1000萬股 或 流通係數>0.06</li>
                </ol>

                <h4>第二階段：動能指標篩選</h4>
                <ol>
                    <li><strong>Alpha值計算：</strong>Alpha = CAGR - Beta × 基準CAGR，要求Alpha > 0</li>
                    <li><strong>Beta值計算：</strong>使用TALib計算20日Beta值，要求Beta > 0</li>
                    <li><strong>成交量排名：</strong>過去12月平均成交量排名前30%</li>
                    <li><strong>動能確認：</strong>確保股票具有正向動能特性</li>
                </ol>

                <h4>第三階段：精選強勢股</h4>
                <ol>
                    <li><strong>權重計算：</strong>計算符合條件股票的市值加權</li>
                    <li><strong>排序選股：</strong>按市值加權由大到小排序</li>
                    <li><strong>精選前5：</strong>選取權重最大的5檔股票</li>
                    <li><strong>等權配置：</strong>5檔股票等權重配置（各20%）</li>
                </ol>

                <h4>第四階段：動態調整</h4>
                <ol>
                    <li><strong>季度重新平衡：</strong>每季重新執行完整篩選流程</li>
                    <li><strong>成分股監控：</strong>關注00733成分股調整動向</li>
                    <li><strong>權重調整：</strong>根據市值變化調整權重排序</li>
                    <li><strong>風險控制：</strong>監控個股基本面變化</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>季度重新平衡：</strong>每季度重新篩選和調整持股</li>
                    <li><strong>集中投資：</strong>僅持有5檔權重最大的股票</li>
                    <li><strong>市值加權：</strong>按市值加權分配投資比例</li>
                    <li><strong>流動性保障：</strong>確保所選股票具備良好流動性</li>
                </ul>

                <h3>⭐ 策略優勢</h3>
                <ul>
                    <li><strong>超越ETF：</strong>集中投資強勢股，期望超越00733表現</li>
                    <li><strong>外力利用：</strong>利用ETF被外力拉升的現象</li>
                    <li><strong>精選投資：</strong>從50檔中精選5檔最強勢的股票</li>
                    <li><strong>成本優勢：</strong>避免ETF管理費，直接投資成分股</li>
                </ul>

                <h3>📊 Alpha和Beta說明</h3>
                <p>核心技術指標計算方式：</p>
                <ul>
                    <li><strong>Beta計算：</strong>使用TALib.BETA函數，計算20日期間的Beta值</li>
                    <li><strong>CAGR計算：</strong>複合年增長率 = exp(ln(1+日報酬率).rolling(20).sum()) - 1</li>
                    <li><strong>Alpha計算：</strong>Alpha = 股票CAGR - Beta × 基準CAGR</li>
                    <li><strong>基準指數：</strong>使用發行量加權股價報酬指數作為基準</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財務和市場數據</p>
                <ul style="color: red;">
                    <li><strong>Alpha/Beta計算：</strong>目前使用簡化模擬，真實數據需要完整價格和基準指數數據</li>
                    <li><strong>經常稅後淨利：</strong>目前使用價格趨勢模擬，真實數據需從財報取得</li>
                    <li><strong>市值數據：</strong>目前使用簡化計算，真實數據需要股本和價格數據</li>
                    <li><strong>成交量排名：</strong>目前使用相對比較，真實數據需要完整市場成交量數據</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinLab ETL調整後收盤價、基準指數、財報基本面數據、市值數據</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>ETF投資者：</strong>希望超越ETF表現的投資者</li>
                    <li><strong>中小型股愛好者：</strong>追求中小型股成長潛力的投資者</li>
                    <li><strong>量化投資者：</strong>偏好數據驅動和系統化投資的投資者</li>
                    <li><strong>研究型投資者：</strong>喜歡深度分析市場現象的投資者</li>
                    <li><strong>集中投資者：</strong>能夠接受集中持股風險的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>

                <h4>操作建議</h4>
                <ul>
                    <li><strong>定期檢視：</strong>每季關注00733成分股調整，同步更新選股</li>
                    <li><strong>深度研究：</strong>深入了解Alpha、Beta等技術指標的計算和意義</li>
                    <li><strong>風險控制：</strong>注意集中投資的風險，適當分散</li>
                    <li><strong>市場觀察：</strong>持續觀察ETF外力拉升現象的變化</li>
                    <li><strong>績效比較：</strong>定期與00733績效進行比較分析</li>
                </ul>

                <h4>進階應用</h4>
                <ul>
                    <li><strong>持股調整：</strong>可以測試不同的持股數量（3檔、7檔等）</li>
                    <li><strong>權重優化：</strong>可以嘗試不同的權重分配方法</li>
                    <li><strong>指標調整：</strong>可以調整Alpha、Beta的計算參數</li>
                    <li><strong>時機優化：</strong>可以優化重新平衡的時機</li>
                </ul>

                <h3>📚 學習資源</h3>

                <h4>ETF分析學習</h4>
                <ul>
                    <li><strong>ETF解構：</strong>學習如何分析ETF的選股邏輯和成分股</li>
                    <li><strong>市場異常：</strong>學習識別和利用市場異常現象</li>
                    <li><strong>逆向工程：</strong>學習從成功策略中提取核心邏輯</li>
                    <li><strong>績效分析：</strong>學習ETF績效分析和比較方法</li>
                </ul>

                <h4>技術指標學習</h4>
                <ul>
                    <li><strong>Alpha/Beta：</strong>深入理解Alpha和Beta的計算和應用</li>
                    <li><strong>動能指標：</strong>學習各種動能指標的使用方法</li>
                    <li><strong>風險指標：</strong>學習風險調整報酬的計算</li>
                    <li><strong>量化分析：</strong>掌握量化投資的基本技能</li>
                </ul>

                <h3>🔮 策略發展方向</h3>

                <h4>數據完善</h4>
                <ul>
                    <li><strong>真實數據：</strong>整合真實的Alpha、Beta和財務數據</li>
                    <li><strong>即時更新：</strong>建立即時的市值和成交量數據</li>
                    <li><strong>歷史回測：</strong>使用真實數據進行完整的歷史回測</li>
                    <li><strong>數據品質：</strong>提升數據的準確性和完整性</li>
                </ul>

                <h4>策略優化</h4>
                <ul>
                    <li><strong>多ETF擴展：</strong>應用到其他優秀ETF的分析</li>
                    <li><strong>動態調整：</strong>根據市場環境動態調整選股數量</li>
                    <li><strong>風險模型：</strong>建立更精確的風險評估模型</li>
                    <li><strong>機器學習：</strong>運用AI技術優化選股邏輯</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>利用ETF被外力拉升的現象，輕鬆超越00733！</p>
                <p style="color: purple;"><strong>核心發現：</strong>究竟是真的買到飆股，還是因為公布了這群股票，才變成飆股？</p>
            """,

            "財報指標20大": """
                <h2>📈 財報指標20大策略 - 結合基本面分析和技術面指標的多因子策略</h2>
                <p><strong>策略類型：</strong>多因子量化策略</p>
                <p><strong>核心理念：</strong>一個結合基本面分析和技術面指標的進階股票選擇策略。策略運用了多個經濟指標（如流動比率、稅前淨利率、資產成長率等）作為特徵來評估股票的基本面強度。</p>

                <h3>🎯 策略創新理念</h3>
                <p>這些特徵通過自定義的處理函數（如df_max4、avg4_diff4等）進行計算和轉換，以捕捉公司的經營效率、財務健康狀況和成長潛力。在技術面上，策略考慮了股價相對於60日均線的位置、股價波動率的百分位排名以及近期平均交易量，這些因素有助於識別市場情緒和股票的交易活躍程度。</p>

                <h3>💡 多維度評估體系</h3>
                <p>通過給這些條件分配不同的權重，策略尋求在基本面強勁且技術面支持的股票中做出選擇。最終，策略通過將基本面特徵得分和技術面指標得分相加，形成最終得分。根據這個綜合得分，選出得分最高的15支股票作為投資組合，並每週重新評估和調整持倉。</p>

                <h3>🔍 策略核心價值</h3>
                <p>這種策略的核心在於利用多個維度的數據來全面評估股票的投資價值，結合基本面的穩定性和技術面的市場表現，旨在尋找那些不僅基本面穩健，且在市場中表現出色的投資機會。這種多因子策略的目的是在控制風險的同時，提高投資組合的整體回報率。</p>

                <h3>📊 20個財報指標完整分類</h3>

                <h4>五大財務面向全覆蓋</h4>
                <p>策略運用了20個經濟指標作為特徵來評估股票的基本面強度，涵蓋企業財務分析的五大核心面向：</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>指標類別</th><th>數量</th><th>核心指標</th><th>評估目的</th>
                    </tr>
                    <tr>
                        <td><strong>流動性指標</strong></td>
                        <td>1個</td>
                        <td>流動比率_df_max4</td>
                        <td>評估短期償債能力</td>
                    </tr>
                    <tr>
                        <td><strong>獲利能力指標</strong></td>
                        <td>8個</td>
                        <td>稅前淨利率、ROE綜合損益、營業利益率、稅前息前折舊前淨利率、ROE稅後等</td>
                        <td>評估企業賺錢能力</td>
                    </tr>
                    <tr>
                        <td><strong>成長性指標</strong></td>
                        <td>4個</td>
                        <td>資產總額成長率、營收成長率、稅後淨利成長率等</td>
                        <td>評估企業成長潛力</td>
                    </tr>
                    <tr>
                        <td><strong>財務結構指標</strong></td>
                        <td>3個</td>
                        <td>負債比率、總負債除總淨值、營運資金等</td>
                        <td>評估財務健康狀況</td>
                    </tr>
                    <tr>
                        <td><strong>營運效率指標</strong></td>
                        <td>4個</td>
                        <td>營業費用率、流動資產、稅率、營業利益等</td>
                        <td>評估經營效率</td>
                    </tr>
                </table>

                <h4>指標權重分配邏輯</h4>
                <ul>
                    <li><strong>獲利能力 (40%)：</strong>8個指標，重視企業的賺錢能力</li>
                    <li><strong>成長性 (20%)：</strong>4個指標，關注企業發展潛力</li>
                    <li><strong>營運效率 (20%)：</strong>4個指標，評估管理效能</li>
                    <li><strong>財務結構 (15%)：</strong>3個指標，確保財務穩健</li>
                    <li><strong>流動性 (5%)：</strong>1個指標，基本償債能力</li>
                </ul>

                <h3>🔧 自定義處理函數技術解析</h3>

                <h4>創新的數據轉換技術</h4>
                <p>策略的核心技術在於自定義的處理函數，這些函數將原始財報數據轉換為更具預測性的特徵，以捕捉公司的經營效率、財務健康狀況和成長潛力。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>函數名稱</th><th>計算邏輯</th><th>技術意義</th><th>應用場景</th>
                    </tr>
                    <tr>
                        <td><strong>df_max4</strong></td>
                        <td>檢查數值是否達到近4期最大值</td>
                        <td>識別短期表現突破</td>
                        <td>獲利能力、流動性指標</td>
                    </tr>
                    <tr>
                        <td><strong>df_max8</strong></td>
                        <td>檢查數值是否達到近8期最大值</td>
                        <td>識別中期表現突破</td>
                        <td>成長性、營運效率指標</td>
                    </tr>
                    <tr>
                        <td><strong>avg4_diff4</strong></td>
                        <td>4期平均值除以4期前的差值</td>
                        <td>衡量趨勢變化幅度</td>
                        <td>成長率、變化率指標</td>
                    </tr>
                    <tr>
                        <td><strong>l_avg4</strong></td>
                        <td>當期數值除以4期平均值</td>
                        <td>評估當期相對表現</td>
                        <td>獲利能力、效率指標</td>
                    </tr>
                    <tr>
                        <td><strong>avg2_avg4</strong></td>
                        <td>2期平均值除以4期平均值</td>
                        <td>識別近期趨勢變化</td>
                        <td>財務結構、營運指標</td>
                    </tr>
                </table>

                <h4>函數設計理念</h4>
                <ul>
                    <li><strong>時間序列分析：</strong>透過不同期間的比較，捕捉財務數據的動態變化</li>
                    <li><strong>相對評估：</strong>使用比率而非絕對數值，提高跨公司比較的有效性</li>
                    <li><strong>趨勢識別：</strong>識別財務指標的改善或惡化趨勢</li>
                    <li><strong>異常檢測：</strong>發現財務表現的突破或異常變化</li>
                </ul>

                <h3>📈 技術面指標體系</h3>

                <h4>市場表現三大維度</h4>
                <p>在技術面上，策略考慮了股價相對於60日均線的位置、股價波動率的百分位排名以及近期平均交易量，這些因素有助於識別市場情緒和股票的交易活躍程度。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>技術指標</th><th>權重</th><th>具體要求</th><th>市場意義</th>
                    </tr>
                    <tr>
                        <td><strong>股價位置</strong></td>
                        <td>15分</td>
                        <td>收盤價 > 60日移動平均線</td>
                        <td>確保股價處於上升趨勢</td>
                    </tr>
                    <tr>
                        <td><strong>波動率排名</strong></td>
                        <td>15分</td>
                        <td>60日波動率排名 < 50%（低波動率較佳）</td>
                        <td>偏好穩定性較高的股票</td>
                    </tr>
                    <tr>
                        <td><strong>成交量條件</strong></td>
                        <td>10分</td>
                        <td>5日平均成交量 > 20萬股</td>
                        <td>確保足夠的交易活躍度</td>
                    </tr>
                </table>

                <h4>技術面設計邏輯</h4>
                <ul>
                    <li><strong>趨勢確認：</strong>股價站上60日線確保處於上升趨勢</li>
                    <li><strong>穩定性偏好：</strong>低波動率代表股價相對穩定，風險較低</li>
                    <li><strong>流動性保障：</strong>足夠成交量確保進出場便利</li>
                    <li><strong>市場認可：</strong>技術面良好代表市場對基本面的認可</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>核心創新理念</h4>
                <p>這種策略的核心在於利用多個維度的數據來全面評估股票的投資價值，結合基本面的穩定性和技術面的市場表現，旨在尋找那些不僅基本面穩健，且在市場中表現出色的投資機會。</p>

                <h4>四大創新特色</h4>
                <ul>
                    <li><strong>多維度評估：</strong>利用20個財報指標全面評估股票投資價值，涵蓋企業財務分析的五大核心面向</li>
                    <li><strong>自定義函數：</strong>通過特殊處理函數（df_max4、avg4_diff4等）捕捉財務數據的變化趨勢和動態特徵</li>
                    <li><strong>基技結合：</strong>結合基本面的穩定性和技術面的市場表現，確保選股的全面性</li>
                    <li><strong>動態權重：</strong>通過給不同條件分配不同權重，策略尋求在基本面強勁且技術面支持的股票中做出選擇</li>
                </ul>

                <h4>多因子策略優勢</h4>
                <ul>
                    <li><strong>風險分散：</strong>多個因子降低單一指標失效的風險</li>
                    <li><strong>全面評估：</strong>從多個角度評估企業價值和市場表現</li>
                    <li><strong>動態調整：</strong>週度重新評估適應市場變化</li>
                    <li><strong>量化客觀：</strong>避免主觀判斷，使用數據驅動決策</li>
                </ul>

                <h3>📊 策略執行流程</h3>

                <h4>第一階段：財報指標處理</h4>
                <ol>
                    <li><strong>數據收集：</strong>收集20個財報指標的原始數據</li>
                    <li><strong>函數處理：</strong>對每個指標進行自定義函數處理（df_max4、avg4_diff4等）</li>
                    <li><strong>特徵轉換：</strong>將原始財報數據轉換為更具預測性的特徵</li>
                    <li><strong>異常處理：</strong>處理缺失值和異常數據</li>
                </ol>

                <h4>第二階段：基本面評分</h4>
                <ol>
                    <li><strong>指標標準化：</strong>將處理後的20個指標進行標準化</li>
                    <li><strong>排名計算：</strong>對每個指標進行市場排名</li>
                    <li><strong>權重分配：</strong>根據指標重要性分配權重</li>
                    <li><strong>基本面得分：</strong>計算加權後的基本面綜合得分</li>
                </ol>

                <h4>第三階段：技術面評分</h4>
                <ol>
                    <li><strong>股價位置評分：</strong>檢查收盤價是否大於60日均線（15分）</li>
                    <li><strong>波動率評分：</strong>計算60日波動率百分位排名（15分）</li>
                    <li><strong>成交量評分：</strong>檢查5日平均成交量是否大於20萬股（10分）</li>
                    <li><strong>技術面得分：</strong>三項技術指標得分相加</li>
                </ol>

                <h4>第四階段：綜合評分與選股</h4>
                <ol>
                    <li><strong>綜合評分：</strong>基本面得分 + 技術面得分 = 最終得分</li>
                    <li><strong>排序選股：</strong>按綜合得分由高到低排序</li>
                    <li><strong>選取前15：</strong>選取綜合得分最高的15檔股票</li>
                    <li><strong>組合建構：</strong>建立等權重投資組合</li>
                </ol>

                <h4>第五階段：動態調整</h4>
                <ol>
                    <li><strong>週度重新評估：</strong>每週重新執行完整評分流程</li>
                    <li><strong>持倉調整：</strong>根據新的評分結果調整持倉</li>
                    <li><strong>風險監控：</strong>監控個股和組合的風險指標</li>
                    <li><strong>績效追蹤：</strong>追蹤策略績效和各因子貢獻</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>週度重新平衡：</strong>每週重新評估和調整持股</li>
                    <li><strong>多因子分散：</strong>通過20個指標分散單一因子風險</li>
                    <li><strong>技術面確認：</strong>技術面指標確保市場認可度</li>
                    <li><strong>流動性保障：</strong>成交量條件確保良好流動性</li>
                </ul>

                <h3>⭐ 策略優勢</h3>
                <ul>
                    <li><strong>全面評估：</strong>20個財報指標提供全方位基本面分析</li>
                    <li><strong>穩健投資：</strong>基本面導向，適合長期穩健投資</li>
                    <li><strong>量化精確：</strong>客觀的數據驅動選股，減少主觀偏見</li>
                    <li><strong>風險控制：</strong>多因子分散和技術面確認降低風險</li>
                </ul>

                <h3>📊 評分權重分配</h3>
                <ul>
                    <li><strong>基本面指標 (60分)：</strong>20個財報指標的綜合排名得分</li>
                    <li><strong>股價位置 (15分)：</strong>股價相對於60日均線的位置</li>
                    <li><strong>波動率排名 (15分)：</strong>股價波動率的百分位排名</li>
                    <li><strong>成交量條件 (10分)：</strong>近期平均交易量的活躍程度</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財報數據</p>
                <ul style="color: red;">
                    <li><strong>財報指標：</strong>目前使用價格和成交量趨勢模擬，真實數據需從財報取得</li>
                    <li><strong>流動比率：</strong>目前使用簡化計算，真實數據需要流動資產和流動負債</li>
                    <li><strong>ROE指標：</strong>目前使用價格趨勢模擬，真實數據需要淨利和股東權益</li>
                    <li><strong>成長率指標：</strong>目前使用價格變化模擬，真實數據需要財報期間比較</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinLab fundamental_features財報數據、公開資訊觀測站財報、TEJ財報資料庫</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>量化投資者：</strong>重視基本面分析、追求穩健投資的量化投資者</li>
                    <li><strong>多因子策略愛好者：</strong>偏好多因子模型和系統化投資的投資者</li>
                    <li><strong>基本面分析師：</strong>深度分析財務報表的專業投資者</li>
                    <li><strong>風險控制者：</strong>重視風險分散和穩健投資的投資者</li>
                    <li><strong>技術學習者：</strong>想要學習多因子策略和財報分析的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>

                <h4>操作建議</h4>
                <ul>
                    <li><strong>定期檢視：</strong>每週關注財報數據更新和市場變化</li>
                    <li><strong>深度研究：</strong>深入了解20個財報指標的計算方法和意義</li>
                    <li><strong>函數理解：</strong>掌握自定義處理函數的邏輯和應用</li>
                    <li><strong>風險監控：</strong>持續監控個股和組合的風險指標</li>
                    <li><strong>績效分析：</strong>定期分析各因子的貢獻和有效性</li>
                </ul>

                <h4>進階應用</h4>
                <ul>
                    <li><strong>權重調整：</strong>可以測試不同的因子權重分配</li>
                    <li><strong>指標優化：</strong>可以調整或新增財報指標</li>
                    <li><strong>函數改進：</strong>可以開發新的自定義處理函數</li>
                    <li><strong>選股數量：</strong>可以調整最終選股的數量</li>
                </ul>

                <h3>📚 學習資源</h3>

                <h4>多因子策略學習</h4>
                <ul>
                    <li><strong>多因子模型：</strong>學習多因子模型的理論基礎和實作方法</li>
                    <li><strong>因子挖掘：</strong>學習如何發現和驗證有效的投資因子</li>
                    <li><strong>風險模型：</strong>學習多因子風險模型的建構和應用</li>
                    <li><strong>績效歸因：</strong>學習如何分析各因子的績效貢獻</li>
                </ul>

                <h4>財報分析學習</h4>
                <ul>
                    <li><strong>財務指標：</strong>深入學習各種財務指標的計算和意義</li>
                    <li><strong>財報分析：</strong>掌握財務報表分析的方法和技巧</li>
                    <li><strong>數據處理：</strong>學習財報數據的清理和轉換技術</li>
                    <li><strong>量化分析：</strong>掌握量化財務分析的工具和方法</li>
                </ul>

                <h3>🔮 策略發展方向</h3>

                <h4>數據完善</h4>
                <ul>
                    <li><strong>真實財報：</strong>整合真實的財報數據和指標</li>
                    <li><strong>即時更新：</strong>建立財報數據的即時更新機制</li>
                    <li><strong>數據品質：</strong>提升數據的準確性和完整性</li>
                    <li><strong>歷史回測：</strong>使用真實數據進行完整的歷史回測</li>
                </ul>

                <h4>模型優化</h4>
                <ul>
                    <li><strong>機器學習：</strong>運用AI技術優化因子選擇和權重</li>
                    <li><strong>動態調整：</strong>根據市場環境動態調整模型參數</li>
                    <li><strong>風險模型：</strong>建立更精確的風險評估模型</li>
                    <li><strong>因子輪動：</strong>研究因子有效性的時間變化</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>這種多因子策略的目的是在控制風險的同時，提高投資組合的整體回報率！</p>
                <p style="color: purple;"><strong>核心價值：</strong>利用多個維度的數據來全面評估股票的投資價值</p>
            """,

            "可能恢復信用交易": """
                <h2>💳 可能恢復信用交易策略 - 每股淨值接近10元的轉機股策略</h2>
                <p><strong>策略類型：</strong>事件驅動轉機股策略</p>
                <p><strong>核心理念：</strong>參考自「菲式思考」策略617，並以「反思菲式思考 Part.2｜策略回測探討」的SOP調整成穩定版的策略。當股票的每股淨值翻到10元以上，就能擁有信用交易的資格，資金調度變靈活，有機會吸引投機買盤進場，讓股價往上衝。</p>

                <h3>🎯 策略發現與理念</h3>
                <p>這個策略帶有基本面、籌碼面、事件面的因素。同時這也代表基本面可能有復甦跡象，一般來說「每股淨值」在10元以下的公司都有成長性或財務狀況不穩的狀況，若每股淨值在之後翻到10元以上，可能代表營運趨勢改變，會吸引看好轉機的買盤。</p>

                <h3>📚 菲式思考策略617背景</h3>
                <p>本策略參考自知名投資人「菲式思考」的策略617，並經過FinLab的「反思菲式思考 Part.2｜策略回測探討」SOP調整，成為更穩定的量化版本。透過嚴謹的回測驗證和參數優化，將原本的投資概念轉化為可執行的量化策略。</p>

                <h3>💳 信用交易恢復機制深度解析</h3>

                <h4>信用交易資格門檻</h4>
                <p>根據台灣證券交易所規定，股票要恢復信用交易資格，每股淨值需達到10元以上。這個門檻設計是為了保護投資人，確保標的具備基本的財務體質。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>機制面向</th><th>具體影響</th><th>投資意義</th><th>市場效應</th>
                    </tr>
                    <tr>
                        <td><strong>信用交易門檻</strong></td>
                        <td>每股淨值需達到10元以上</td>
                        <td>財務體質改善的訊號</td>
                        <td>基本面復甦確認</td>
                    </tr>
                    <tr>
                        <td><strong>資金調度靈活</strong></td>
                        <td>投資人可進行融資融券操作</td>
                        <td>提升股票流動性和活躍度</td>
                        <td>交易量可能增加</td>
                    </tr>
                    <tr>
                        <td><strong>投機買盤進場</strong></td>
                        <td>有機會吸引投機買盤進場</td>
                        <td>短期推升股價動能</td>
                        <td>價格波動可能加劇</td>
                    </tr>
                    <tr>
                        <td><strong>市場關注度提升</strong></td>
                        <td>恢復信用交易通常會提高關注</td>
                        <td>轉機股題材發酵</td>
                        <td>媒體報導增加</td>
                    </tr>
                </table>

                <h4>三重效應疊加</h4>
                <ul>
                    <li><strong>基本面效應：</strong>每股淨值回升代表財務狀況改善</li>
                    <li><strong>籌碼面效應：</strong>恢復信用交易增加資金運用彈性</li>
                    <li><strong>事件面效應：</strong>轉機股題材吸引市場關注</li>
                </ul>

                <h3>📊 三大核心條件</h3>
                <ul>
                    <li><strong>條件一 (40分)：</strong>9元 ≤ 每股淨值 < 10元（即將恢復信用交易）</li>
                    <li><strong>條件二 (30分)：</strong>營業利益率 > 0%（基本面復甦跡象）</li>
                    <li><strong>條件三 (30分)：</strong>月營收月增率 > 5%（營運動能改善）</li>
                </ul>

                <h3>🎯 轉機股特徵深度分析</h3>

                <h4>每股淨值10元以下的企業特徵</h4>
                <p>一般來說「每股淨值」在10元以下的公司都有成長性或財務狀況不穩的狀況，這些企業通常具有以下特徵：</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>問題類型</th><th>具體表現</th><th>市場反應</th><th>轉機機會</th>
                    </tr>
                    <tr>
                        <td><strong>成長性問題</strong></td>
                        <td>公司可能處於成長瓶頸或轉型期</td>
                        <td>營收成長停滯或衰退</td>
                        <td>新產品、新市場突破</td>
                    </tr>
                    <tr>
                        <td><strong>財務狀況不穩</strong></td>
                        <td>可能面臨獲利能力或現金流問題</td>
                        <td>毛利率下滑、現金流緊張</td>
                        <td>營運效率改善、成本控制</td>
                    </tr>
                    <tr>
                        <td><strong>市場信心不足</strong></td>
                        <td>投資人對公司前景缺乏信心</td>
                        <td>股價長期低迷、成交量萎縮</td>
                        <td>業績好轉、策略調整成功</td>
                    </tr>
                    <tr>
                        <td><strong>產業逆風</strong></td>
                        <td>所處產業面臨結構性挑戰</td>
                        <td>整體產業估值偏低</td>
                        <td>產業復甦、政策利多</td>
                    </tr>
                </table>

                <h4>轉機股投資邏輯</h4>
                <p>若每股淨值在之後翻到10元以上，可能代表營運趨勢改變，會吸引看好轉機的買盤。這種轉機通常來自：</p>
                <ul>
                    <li><strong>基本面改善：</strong>營收成長回升、獲利能力恢復</li>
                    <li><strong>財務體質強化：</strong>現金流改善、負債比率下降</li>
                    <li><strong>市場重新評價：</strong>投資人信心恢復、估值修復</li>
                    <li><strong>事件催化：</strong>重大利多消息、政策支持</li>
                </ul>

                <h3>💡 策略創新點</h3>

                <h4>核心創新理念</h4>
                <p>這個策略帶有基本面、籌碼面、事件面的因素，是一個多維度的轉機股投資策略。透過FinLab的「反思菲式思考 Part.2｜策略回測探討」SOP調整，將原本的投資概念量化為可執行的策略。</p>

                <h4>四大創新特色</h4>
                <ul>
                    <li><strong>事件驅動：</strong>以信用交易恢復為核心事件進行投資，捕捉制度性變化帶來的機會</li>
                    <li><strong>多面向分析：</strong>結合基本面、籌碼面、事件面因素，全方位評估轉機股潛力</li>
                    <li><strong>轉機捕捉：</strong>專注於營運趨勢改變的轉機股，在市場重新評價前提前布局</li>
                    <li><strong>菲式思考量化：</strong>參考知名投資人「菲式思考」策略617，並進行穩定版調整</li>
                </ul>

                <h4>策略優勢</h4>
                <ul>
                    <li><strong>制度套利：</strong>利用信用交易恢復的制度性變化獲利</li>
                    <li><strong>早期布局：</strong>在每股淨值接近10元時提前布局</li>
                    <li><strong>多重催化：</strong>基本面改善、籌碼面活絡、事件面催化三重效應</li>
                    <li><strong>風險可控：</strong>透過嚴格的篩選條件控制投資風險</li>
                </ul>

                <h3>📈 策略執行流程</h3>

                <h4>第一階段：每股淨值篩選</h4>
                <ol>
                    <li><strong>數據計算：</strong>計算每股淨值 = 收盤價 ÷ 股價淨值比</li>
                    <li><strong>門檻篩選：</strong>篩選9元 ≤ 每股淨值 < 10元的股票</li>
                    <li><strong>邏輯說明：</strong>接近10元門檻但尚未達到，具備恢復信用交易潛力</li>
                    <li><strong>候選清單：</strong>建立符合每股淨值條件的候選股票清單</li>
                </ol>

                <h4>第二階段：基本面確認</h4>
                <ol>
                    <li><strong>獲利能力檢查：</strong>營業利益率 > 0%，確保企業具備基本獲利能力</li>
                    <li><strong>財務健康度：</strong>排除虧損企業，專注於有獲利的轉機股</li>
                    <li><strong>營運效率：</strong>確認企業營運模式仍具競爭力</li>
                    <li><strong>基本面篩選：</strong>從候選清單中篩選出基本面健康的股票</li>
                </ol>

                <h4>第三階段：營運動能檢查</h4>
                <ol>
                    <li><strong>成長動能確認：</strong>月營收月增率 > 5%，確認營運有改善跡象</li>
                    <li><strong>趨勢判斷：</strong>營收成長代表營運趨勢可能正在改變</li>
                    <li><strong>轉機驗證：</strong>透過營收數據驗證轉機的真實性</li>
                    <li><strong>動能篩選：</strong>選出具備成長動能的轉機股</li>
                </ol>

                <h4>第四階段：綜合評分與選股</h4>
                <ol>
                    <li><strong>三重條件：</strong>每股淨值、營業利益率、月增率三個條件同時滿足</li>
                    <li><strong>風險評估：</strong>評估每檔股票的風險收益比</li>
                    <li><strong>組合建構：</strong>建立分散化的轉機股投資組合</li>
                    <li><strong>部位分配：</strong>根據風險控制原則分配投資部位</li>
                </ol>

                <h4>第五階段：動態調整</h4>
                <ol>
                    <li><strong>月度重新評估：</strong>每月重新執行完整篩選流程</li>
                    <li><strong>持倉調整：</strong>根據最新條件調整持股</li>
                    <li><strong>風險監控：</strong>持續監控個股和組合風險</li>
                    <li><strong>績效追蹤：</strong>追蹤策略績效和轉機股表現</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>月度重新平衡：</strong>每月重新篩選和調整持股</li>
                    <li><strong>持股上限：</strong>單一股票持股上限20%</li>
                    <li><strong>停損設定：</strong>14%停損保護資本</li>
                    <li><strong>轉機確認：</strong>嚴格的三重條件確認轉機跡象</li>
                </ul>

                <h3>⭐ 策略優勢</h3>
                <ul>
                    <li><strong>事件明確：</strong>信用交易恢復是明確的市場事件</li>
                    <li><strong>轉機捕捉：</strong>專注於營運趨勢改變的投資機會</li>
                    <li><strong>多重確認：</strong>基本面、營運面雙重確認降低風險</li>
                    <li><strong>市場關注：</strong>恢復信用交易通常會提高市場關注度</li>
                </ul>

                <h3>📊 每股淨值計算</h3>
                <p>核心計算公式：</p>
                <ul>
                    <li><strong>每股淨值：</strong>收盤價 ÷ 股價淨值比</li>
                    <li><strong>股價淨值比：</strong>反映股價相對於每股淨值的倍數</li>
                    <li><strong>信用交易門檻：</strong>每股淨值需達到10元以上</li>
                    <li><strong>策略目標區間：</strong>9元 ≤ 每股淨值 < 10元</li>
                </ul>

                <h3>⚠️ 策略風險</h3>
                <ul>
                    <li><strong>轉機不確定性：</strong>並非所有接近10元的股票都會成功轉機</li>
                    <li><strong>市場波動：</strong>轉機股通常波動較大，需要較高風險承受能力</li>
                    <li><strong>時間成本：</strong>轉機過程可能需要較長時間</li>
                    <li><strong>基本面風險：</strong>公司基本面可能持續惡化</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財務數據</p>
                <ul style="color: red;">
                    <li><strong>股價淨值比：</strong>目前使用價格波動率模擬，真實數據需從財報取得</li>
                    <li><strong>每股淨值：</strong>目前使用簡化計算，真實數據需要股東權益和股本數據</li>
                    <li><strong>營業利益率：</strong>目前使用價格趨勢模擬，真實數據需從損益表取得</li>
                    <li><strong>月營收月增率：</strong>目前使用價格變化模擬，真實數據需從月營收公告取得</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinLab price_earning_ratio數據、fundamental_features財報數據、monthly_revenue月營收數據</p>

                <h3>🎯 適用對象</h3>
                <ul>
                    <li><strong>轉機股投資者：</strong>追求轉機股投資機會的投資者</li>
                    <li><strong>積極型投資者：</strong>能承受較高風險、追求高報酬的投資者</li>
                    <li><strong>事件驅動投資者：</strong>偏好事件驅動策略的投資者</li>
                    <li><strong>菲式思考追隨者：</strong>認同菲式思考投資理念的投資者</li>
                    <li><strong>制度套利者：</strong>善於利用制度性變化獲利的投資者</li>
                </ul>

                <h3>💡 使用建議</h3>
                <ul>
                    <li><strong>定期檢視：</strong>每月關注每股淨值變化和營收數據更新</li>
                    <li><strong>深度研究：</strong>深入了解轉機股的基本面和營運狀況</li>
                    <li><strong>風險控制：</strong>嚴格執行停損機制，控制單檔持股比例</li>
                    <li><strong>耐心等待：</strong>轉機過程需要時間，保持投資耐心</li>
                    <li><strong>市場觀察：</strong>關注信用交易恢復的相關消息</li>
                </ul>

                <h3>📚 學習資源</h3>
                <ul>
                    <li><strong>主要教學：</strong><a href="https://www.finlab.tw/phcebus-thinking-report-part2-backtest-sop/" target="_blank">反思菲式思考 Part.2｜策略回測探討</a></li>
                    <li><strong>策略SOP：</strong>學習策略回測的標準作業程序</li>
                    <li><strong>菲式思考：</strong>學習菲式思考的投資理念和方法</li>
                    <li><strong>轉機股分析：</strong>學習如何識別和分析轉機股</li>
                </ul>

                <p style="color: green;"><strong>策略特色：</strong>參考菲式思考策略617，捕捉每股淨值接近10元的轉機股投資機會！</p>
                <p style="color: purple;"><strong>教學資源：</strong><a href="https://www.finlab.tw/phcebus-thinking-report-part2-backtest-sop/" target="_blank">反思菲式思考 Part.2｜策略回測探討</a></p>
            """,

            "#合約負債建築工": """
                <h2>🏗️ #合約負債建築工策略 - 針對營建業的產業特性策略</h2>
                <p><strong>策略類型：</strong>產業型基本面策略</p>
                <p><strong>核心理念：</strong>針對營建業的產業特性，以"合約負債_流動"取代營收當成長指標。進場條件為流動合約負債佔股本高比例、流動合約負債季成長。排除已可能反映利多的低本益比營建股。</p>

                <h3>🎯 策略創新理念</h3>
                <p>營建業的商業模式與一般產業不同，通常採用預收款項的方式，先收錢後施工。因此，合約負債比營收更能反映未來業績潛力，是營收與獲利的領先指標。本策略專門針對建材營造產業，利用合約負債作為領先指標，預測未來營收貢獻。</p>

                <h3>🏗️ 營建業獨特商業模式</h3>
                <p>每月換股，最大檔數5檔。低波動策略，停利設定50%，停損設定8%。在房地產空頭的年份可能難有表現。</p>

                <h3>🏗️ 營建業產業特性深度解析</h3>

                <h4>營建業獨特的商業模式</h4>
                <p>營建業與一般產業最大的不同在於其商業模式和財務特性，這些特性使得傳統的財務指標可能無法完全反映營建業的真實狀況。</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f0f0f0;">
                        <th>產業特性</th><th>具體表現</th><th>對投資的影響</th><th>策略應對</th>
                    </tr>
                    <tr>
                        <td><strong>營收波動劇烈</strong></td>
                        <td>營收認列集中在完工交屋月份，未交屋前營收數值很低</td>
                        <td>傳統營收指標失真</td>
                        <td>以合約負債取代營收分析</td>
                    </tr>
                    <tr>
                        <td><strong>預收款商業模式</strong></td>
                        <td>營建業通常先收款後施工，合約負債是重要現金來源</td>
                        <td>合約負債成為領先指標</td>
                        <td>重點關注合約負債變化</td>
                    </tr>
                    <tr>
                        <td><strong>族群連動性強</strong></td>
                        <td>與總體經濟相關，受房地產景氣循環影響</td>
                        <td>需要精準時機判斷</td>
                        <td>考慮房地產週期因素</td>
                    </tr>
                    <tr>
                        <td><strong>本益比反向指標</strong></td>
                        <td>市場給予的本益比低，本益比越低往往是業績到頂的現象</td>
                        <td>本益比指標要反著用</td>
                        <td>排除低本益比股票</td>
                    </tr>
                </table>

                <h4>合約負債的重要意義</h4>
                <ul>
                    <li><strong>領先指標：</strong>合約負債比營收更能反映未來業績潛力</li>
                    <li><strong>現金流保障：</strong>預收款項提供穩定的現金流來源</li>
                    <li><strong>業績預測：</strong>合約負債是營收與獲利的領先指標</li>
                    <li><strong>風險控制：</strong>高合約負債代表未來營收有保障</li>
                </ul>

                <h3>📊 三大核心條件</h3>
                <ul>
                    <li><strong>條件一 (40分)：</strong>合約負債佔股本比率 > 55%（高潛在營收貢獻）</li>
                    <li><strong>條件二 (30分)：</strong>本益比 > 10倍或無法計算（排除已反映利多的低本益比股）</li>
                    <li><strong>條件三 (30分)：</strong>合約負債季成長 5%-50%（適度成長，避免過度擴張）</li>
                </ul>

                <h3>🎯 合約負債分析</h3>
                <p>合約負債在營建業的重要意義：</p>
                <ul>
                    <li><strong>未來營收保障：</strong>合約負債代表已簽約但未完工的項目價值</li>
                    <li><strong>現金流優勢：</strong>預收款項提供充足的營運資金</li>
                    <li><strong>業績可預測性：</strong>高合約負債比率代表未來業績較有保障</li>
                    <li><strong>競爭力指標：</strong>能夠取得大量預收款代表市場競爭力強</li>
                </ul>

                <h3>💡 策略創新點</h3>
                <ul>
                    <li><strong>產業專精：</strong>專門針對建材營造產業設計的策略</li>
                    <li><strong>領先指標：</strong>使用合約負債取代傳統營收指標</li>
                    <li><strong>價值發現：</strong>排除已反映利多的低本益比股票</li>
                    <li><strong>風險控制：</strong>限制成長率範圍，避免過度擴張風險</li>
                </ul>

                <h3>📈 選股流程</h3>
                <ol>
                    <li><strong>產業篩選：</strong>限定建材營造產業範圍</li>
                    <li><strong>合約負債比率：</strong>計算合約負債_流動 ÷ 股本 > 55%</li>
                    <li><strong>本益比排除：</strong>排除本益比 ≤ 10倍的已反映利多股票</li>
                    <li><strong>成長率檢查：</strong>合約負債季成長率在5%-50%區間</li>
                    <li><strong>綜合排名：</strong>按合約負債佔股本比率排序</li>
                    <li><strong>最終選股：</strong>選取前5檔最高比率的股票</li>
                </ol>

                <h3>🎯 風險控制機制</h3>
                <ul>
                    <li><strong>月度重新平衡：</strong>每月重新篩選和調整持股</li>
                    <li><strong>集中投資：</strong>最大檔數限制5檔，專注優質標的</li>
                    <li><strong>停損設定：</strong>8%停損保護資本</li>
                    <li><strong>停利設定：</strong>50%停利鎖定獲利</li>
                    <li><strong>產業限制：</strong>僅投資建材營造產業</li>
                </ul>

                <h3>⭐ 策略優勢</h3>
                <ul>
                    <li><strong>產業專精：</strong>深度理解營建業特性，提高選股精準度</li>
                    <li><strong>領先指標：</strong>合約負債比營收更能預測未來表現</li>
                    <li><strong>價值發現：</strong>發掘尚未被市場充分認知的價值</li>
                    <li><strong>風險可控：</strong>完善的停損停利機制</li>
                </ul>

                <h3>📊 合約負債計算</h3>
                <p>核心計算公式：</p>
                <ul>
                    <li><strong>合約負債比率：</strong>合約負債_流動 ÷ 股本</li>
                    <li><strong>合約負債成長率：</strong>(本季合約負債 ÷ 上季合約負債) - 1</li>
                    <li><strong>選股權重：</strong>合約負債比率 × 符合條件</li>
                    <li><strong>排序依據：</strong>按合約負債佔股本比率由高到低排序</li>
                </ul>

                <h3>🏠 房地產週期考量</h3>
                <ul>
                    <li><strong>多頭市場：</strong>營建業合約負債通常快速增長，策略表現較佳</li>
                    <li><strong>空頭市場：</strong>房地產空頭年份可能難有表現，需謹慎評估</li>
                    <li><strong>政策影響：</strong>房地產政策變化對營建業影響重大</li>
                    <li><strong>利率敏感：</strong>利率變化直接影響房地產需求</li>
                </ul>

                <h3>⚠️ 策略風險</h3>
                <ul>
                    <li><strong>產業週期：</strong>營建業受房地產景氣循環影響較大</li>
                    <li><strong>政策風險：</strong>房地產相關政策變化可能影響表現</li>
                    <li><strong>集中風險：</strong>僅投資單一產業，缺乏分散效果</li>
                    <li><strong>執行風險：</strong>合約負債轉化為實際營收存在執行風險</li>
                </ul>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前缺乏真實財務數據</p>
                <ul style="color: red;">
                    <li><strong>合約負債_流動：</strong>目前使用成交量和價格模擬，真實數據需從財報取得</li>
                    <li><strong>股本數據：</strong>目前使用簡化計算，真實數據需要股本資料</li>
                    <li><strong>本益比：</strong>目前使用價格波動率模擬，真實數據需要EPS和股價</li>
                    <li><strong>產業分類：</strong>目前無法限定建材營造產業，需要產業分類數據</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>FinLab financial_statement財報數據、產業分類數據、建材營造universe設定</p>
                <p style="color: blue;"><strong>詳細策略文章：</strong><a href="https://www.finlab.tw/building-contingent-liability-strategy/" target="_blank">合約負債建築工策略說明</a></p>
            """,

            # 5分鐘策略說明 - 整合版本
            "5分鐘突破動量": """
                <h2>⚡ 5分鐘突破動量策略 - 整合版短線策略</h2>
                <p><strong>策略類型：</strong>短線技術策略（整合突破+動量+趨勢跟隨）</p>
                <p><strong>適用場景：</strong>盤整突破、趨勢啟動初期</p>
                <p><strong>核心理念：</strong>捕捉價格突破關鍵阻力支撐位後的動量延續</p>
                <p><strong>選股標準：</strong>突破20期高低點 + MACD金叉 + 均線多空排列 + 成交量放大1.5倍</p>
                <p><strong>操作週期：</strong>5分鐘級別（使用日K線模擬）</p>
                <p><strong>適用對象：</strong>短線交易者、當沖投資者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前使用日K線模擬5分鐘數據</p>
                <ul style="color: red;">
                    <li><strong>數據限制：</strong>缺乏真實5分鐘K線數據，使用日K線模擬</li>
                    <li><strong>實用性：</strong>僅供策略邏輯參考，實際使用需要真實分鐘級數據</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>券商API、財經資訊商5分鐘K線數據</p>
            """,

            "5分鐘量價確認": """
                <h2>⚡ 5分鐘量價確認策略 - 量價配合分析</h2>
                <p><strong>策略類型：</strong>短線量價策略</p>
                <p><strong>適用場景：</strong>確認趨勢真偽、驗證突破有效性</p>
                <p><strong>核心理念：</strong>透過成交量變化確認價格走勢的可靠性</p>
                <p><strong>選股標準：</strong>價格變化≥1% + 成交量爆增2倍 + 量價配合關係</p>
                <p><strong>操作週期：</strong>5分鐘級別（使用日K線模擬）</p>
                <p><strong>適用對象：</strong>重視量價分析的短線交易者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前使用日K線模擬5分鐘數據</p>
                <ul style="color: red;">
                    <li><strong>數據限制：</strong>缺乏真實5分鐘成交量數據</li>
                    <li><strong>量價關係：</strong>日K線無法準確反映分鐘級量價變化</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>券商API、即時成交明細數據</p>
            """,

            "5分鐘回歸修正": """
                <h2>⚡ 5分鐘回歸修正策略 - 整合版反轉策略</h2>
                <p><strong>策略類型：</strong>短線反轉策略（整合均值回歸+剝頭皮）</p>
                <p><strong>適用場景：</strong>超買超賣修正、盤整震盪市</p>
                <p><strong>核心理念：</strong>利用價格偏離均值後的回歸特性和低波動環境的小幅獲利機會</p>
                <p><strong>選股標準：</strong>RSI超買超賣 + 偏離MA20≥3% + 低波動率<1% + 成交量正常</p>
                <p><strong>操作週期：</strong>5分鐘級別（使用日K線模擬）</p>
                <p><strong>適用對象：</strong>偏好反轉交易、追求小幅穩定獲利的投資者</p>

                <p style="color: red; font-weight: bold;">⚠️ 重要提醒：目前使用日K線模擬5分鐘數據</p>
                <ul style="color: red;">
                    <li><strong>數據限制：</strong>日K線無法準確反映分鐘級波動率</li>
                    <li><strong>回歸特性：</strong>短期均值回歸需要更高頻率數據驗證</li>
                </ul>
                <p style="color: blue;"><strong>建議數據源：</strong>高頻交易數據、分鐘級技術指標</p>
            """,

            "二次創高股票": """
                <h2>📈 二次創高股票策略 - 捕捉二次創高的強勢股票</h2>
                <p><strong>策略類型：</strong>技術分析策略</p>
                <p><strong>核心理念：</strong>捕捉在一定期間內創出新高後，再次創高的強勢股票</p>
                <p><strong>適用時機：</strong>多頭市場或個股強勢時期</p>
                <p><strong>策略特色：</strong>結合價格動能和成交量確認，提高選股精確度</p>

                <h3>📊 篩選條件</h3>
                <ul>
                    <li><strong>普通股限制：</strong>只針對4位數股票代碼的標的（以普通股為主，排除特殊金融商品）</li>
                    <li><strong>非分盤交易：</strong>過濾掉分盤交易的處置雜訊</li>
                    <li><strong>高波動率：</strong>年化波動率 > 40%（25分）/ > 30%（15分）</li>
                    <li><strong>成交量異常：</strong>成交量 > 2倍平均（20分）/ > 1.5倍平均（10分）</li>
                    <li><strong>價格相對低位：</strong>價格 < 0.9 × MA20（20分）/ < 0.95 × MA20（10分）</li>
                    <li><strong>RSI超賣：</strong>RSI < 30（25分）/ < 40（15分）</li>
                    <li><strong>反彈跡象：</strong>近5日上漲 > 3%（10分）/ > 0%（5分）</li>
                </ul>

                <h3>⚡ 策略特色</h3>
                <ul>
                    <li><strong>評分系統：</strong>0-100分評分，≥70分通過，50-69分部分符合</li>
                    <li><strong>風險控制：</strong>明確的進出場時間點，嚴格的持倉限制</li>
                    <li><strong>心理優勢：</strong>反向思維，避免跟風操作，利用群眾心理的弱點</li>
                    <li><strong>市場機會：</strong>利用市場對處置股的過度恐懼，在非理性拋售中尋找價值回歸機會</li>
                </ul>
            """
        }

        return strategy_overviews.get(self.strategy_name, f"""
            <h2>📊 {self.strategy_name}</h2>
            <p>這是一個自定義策略，請參考策略設定中的具體條件。</p>
        """)

    def get_strategy_details(self):
        """獲取策略詳細說明"""
        strategy_details = {
            "阿水一式": """
                <h2>🔍 阿水一式 - 詳細技術說明</h2>

                <h3>📊 大盤綠燈判斷標準</h3>
                <ul>
                    <li><strong>大盤20MA上揚：</strong>大盤日線20日移動平均線向上</li>
                    <li><strong>布林帶表現：</strong>即使股價碰到布林下通道，也能立即脫離</li>
                    <li><strong>籌碼指標：</strong>Put/Call Ratio > 1</li>
                    <li><strong>市場狀態：</strong>在綠燈時，大盤安全，適合持續做多</li>
                </ul>

                <h3>🎯 選股心法與原則</h3>
                <h4>阿水愛股特性：</h4>
                <ol>
                    <li><strong>成交量條件：</strong>當日買盤量能 > 5日均量（最好2-5倍，避免超過10倍）</li>
                    <li><strong>價格突破：</strong>突破2.1倍標準差的布林上通道</li>
                    <li><strong>成交額門檻：</strong>總成交額 > 1000萬</li>
                    <li><strong>題材性：</strong>有話題性、財報或基本面表現良好</li>
                    <li><strong>創新高：</strong>當天創新高的前段班（注意超級歷史新高風險）</li>
                </ol>

                <h3>🔧 技術指標計算</h3>
                <ul>
                    <li><strong>布林帶：</strong>20MA ± 2.1 × STD(20)</li>
                    <li><strong>多頭排列：</strong>20MA > 60MA > 120MA</li>
                    <li><strong>20MA翻揚：</strong>連續3日20MA向上</li>
                    <li><strong>成交量比：</strong>當日量 ÷ 5日均量</li>
                </ul>

                <h3>📈 精細化評分機制（0-100分）</h3>
                <h4>🎯 評分標準詳解：</h4>

                <h5>💰 成交金額評分（25分）- 流動性指標</h5>
                <ul>
                    <li><strong>10億以上：</strong>25分（超級流動性）</li>
                    <li><strong>5-10億：</strong>22分（優秀流動性）</li>
                    <li><strong>2-5億：</strong>18分（良好流動性）</li>
                    <li><strong>1-2億：</strong>15分（可接受流動性）</li>
                    <li><strong>5000萬-1億：</strong>12分（一般流動性）</li>
                    <li><strong>1000-5000萬：</strong>8分（較低流動性）</li>
                    <li><strong>500-1000萬：</strong>5分（流動性不足）</li>
                    <li><strong>500萬以下：</strong>0分（流動性極差）</li>
                </ul>

                <h5>📊 布林帶寬評分（20分）- 波動性指標</h5>
                <ul>
                    <li><strong>0.08-0.15：</strong>20分（理想波動區間）</li>
                    <li><strong>0.06-0.20：</strong>15分（可接受區間）</li>
                    <li><strong>0.04-0.25：</strong>10分（次佳區間）</li>
                    <li><strong>其他有數據：</strong>5分（不理想但有數據）</li>
                    <li><strong>無數據：</strong>0分</li>
                </ul>

                <h5>⏰ 壓縮天數評分（15分）- 整理時間指標</h5>
                <ul>
                    <li><strong>5-15天：</strong>15分（理想壓縮期）</li>
                    <li><strong>3-20天：</strong>12分（可接受範圍）</li>
                    <li><strong>1-25天：</strong>8分（次佳範圍）</li>
                    <li><strong>其他有壓縮：</strong>5分（不理想但有壓縮）</li>
                    <li><strong>無壓縮：</strong>0分</li>
                </ul>

                <h5>📈 成交量倍數評分（20分）- 爆量指標</h5>
                <ul>
                    <li><strong>5倍以上：</strong>20分（超級爆量）</li>
                    <li><strong>3-5倍：</strong>18分（明顯爆量）</li>
                    <li><strong>2.5-3倍：</strong>15分（爆量）</li>
                    <li><strong>2-2.5倍：</strong>12分（溫和放量）</li>
                    <li><strong>1.5-2倍：</strong>8分（輕微放量）</li>
                    <li><strong>1-1.5倍：</strong>5分（正常量能）</li>
                    <li><strong>1倍以下：</strong>0分（量能不足）</li>
                </ul>

                <h5>🚀 突破幅度評分（15分）- 突破強度指標</h5>
                <ul>
                    <li><strong>8%以上：</strong>15分（強力突破）</li>
                    <li><strong>5-8%：</strong>12分（明顯突破）</li>
                    <li><strong>3-5%：</strong>10分（有效突破）</li>
                    <li><strong>1-3%：</strong>7分（輕微突破）</li>
                    <li><strong>0-1%：</strong>5分（微幅突破）</li>
                    <li><strong>未突破：</strong>0分</li>
                </ul>

                <h5>✅ 策略匹配加分（5分）</h5>
                <ul>
                    <li><strong>完全符合策略條件：</strong>+5分</li>
                </ul>

                <h4>🎖️ 評分等級劃分：</h4>
                <ul>
                    <li><strong>85-100分：</strong>🔥 超強飆股（強烈推薦）</li>
                    <li><strong>75-84分：</strong>✅ 飆股信號（推薦關注）</li>
                    <li><strong>60-74分：</strong>⚠️ 觀察中（謹慎考慮）</li>
                    <li><strong>40-59分：</strong>❌ 條件不足（不建議）</li>
                    <li><strong>0-39分：</strong>❌ 不符合（避免）</li>
                </ul>
            """,

            "阿水二式": """
                <h2>🔍 阿水二式 - 空方策略詳細說明</h2>

                <h3>📊 大盤紅燈判斷標準</h3>
                <ul>
                    <li><strong>指數跌破20MA：</strong>大盤指數由上而下跌破20MA，並將20MA帶往下彎</li>
                    <li><strong>反彈失敗：</strong>反彈過不了20MA，或者一上20MA就收綠K</li>
                    <li><strong>籌碼指標：</strong>Put/Call Ratio < 1</li>
                    <li><strong>市場狀態：</strong>在紅燈時，大盤危險，建議完全空手或開始做空</li>
                </ul>

                <h3>🎯 選股要點與心法</h3>
                <h4>核心三要點：</h4>
                <ol>
                    <li><strong>要點一：</strong>20MA下彎</li>
                    <li><strong>要點二：</strong>股價碰下通道後，反彈卻過不了20MA</li>
                    <li><strong>要點三：</strong>財報轉弱且法人棄守會更好</li>
                </ol>

                <h4>阿水二式喜歡的特色：</h4>
                <ol>
                    <li><strong>股價高一些：</strong>空起來才有「肉」，即獲利空間</li>
                    <li><strong>法人棄守：</strong>外資、投信開始賣出</li>
                    <li><strong>財報轉弱：</strong>基本面開始惡化</li>
                </ol>

                <h3>🔧 技術指標計算</h3>
                <ul>
                    <li><strong>布林帶：</strong>20MA ± 2.0 × STD(20)</li>
                    <li><strong>空頭排列：</strong>20MA < 60MA < 120MA</li>
                    <li><strong>20MA下彎：</strong>連續3日20MA向下</li>
                    <li><strong>反彈失敗：</strong>碰下通道後反彈高點 < 20MA</li>
                </ul>

                <h3>📈 評分機制（0-100分）</h3>
                <ul>
                    <li><strong>基礎分：</strong>60分</li>
                    <li><strong>20MA下彎：</strong>一般+15分，3日連跌+20分</li>
                    <li><strong>反彈失敗：</strong>+25分</li>
                    <li><strong>空頭排列：</strong>短期+10分，完整+15分</li>
                    <li><strong>高檔盤頭：</strong>60日高檔+5分，120日高檔+10分</li>
                    <li><strong>創新低：</strong>20日新低+3分，60日新低+5分</li>
                </ul>

                <h3>🎯 篩選流程</h3>
                <ul>
                    <li><strong>減法學：</strong>找出「高檔盤頭下跌」與「空頭排列」的股票</li>
                    <li><strong>大盤續弱：</strong>可找「創20日新低」且「空頭排列」的股票</li>
                    <li><strong>條件放寬：</strong>選股太少時，放寬條件後尋找符合「亮點」特徵的個股</li>
                </ul>
            """,

            "藏獒": """
                <h2>🔍 藏獒 - 動能策略詳細說明</h2>

                <h3>📊 策略核心邏輯</h3>
                <p><strong>核心理念：</strong>押寶股價領先營收突破，大多頭會打出兇悍的一波流</p>
                <p><strong>勝率特性：</strong>勝率一半一半，但勝手常持有後一去不回，回檔小</p>
                <p><strong>風險控制：</strong>停損設8%，偏低位置，保留多數獲利部位與下檔波動</p>

                <h3>🎯 核心條件詳解</h3>
                <h4>1. 股價創年新高 (cond1)</h4>
                <ul>
                    <li><strong>條件：</strong>close == close.rolling(250).max()</li>
                    <li><strong>意義：</strong>股價突破250日（約一年）最高點</li>
                    <li><strong>目的：</strong>捕捉動能突破的起始點</li>
                </ul>

                <h4>2. 排除月營收連3月衰退10%以上 (cond2)</h4>
                <ul>
                    <li><strong>條件：</strong>~(rev_year_growth < -10).sustain(3)</li>
                    <li><strong>意義：</strong>避免基本面持續惡化的股票</li>
                    <li><strong>目的：</strong>確保營收不會連續大幅衰退</li>
                </ul>

                <h4>3. 排除營收成長趨勢過老 (cond3)</h4>
                <ul>
                    <li><strong>條件：</strong>~(rev_year_growth > 60).sustain(12,8)</li>
                    <li><strong>意義：</strong>12個月內有至少8個月年增率大於60%</li>
                    <li><strong>目的：</strong>避免成長故事已經講太久的股票</li>
                </ul>

                <h4>4. 確認營收底部 (cond4)</h4>
                <ul>
                    <li><strong>條件：</strong>((rev.rolling(12).min())/(rev) < 0.8).sustain(3)</li>
                    <li><strong>意義：</strong>連續3月的"12月最小營收/近月營收" < 0.8</li>
                    <li><strong>目的：</strong>確認營收已脫離近年谷底</li>
                </ul>

                <h4>5. 月增率條件 (cond5)</h4>
                <ul>
                    <li><strong>條件：</strong>(rev_month_growth > -40).sustain(3)</li>
                    <li><strong>意義：</strong>單月營收月增率連續3月大於-40%</li>
                    <li><strong>目的：</strong>避免月增率過度惡化</li>
                </ul>

                <h4>6. 流動性條件 (cond6)</h4>
                <ul>
                    <li><strong>條件：</strong>vol_ma > 200*1000</li>
                    <li><strong>意義：</strong>10日平均成交量 > 20萬股</li>
                    <li><strong>目的：</strong>確保足夠的流動性</li>
                </ul>

                <h3>🔧 選股邏輯</h3>
                <ul>
                    <li><strong>買比較冷門的股票：</strong>buy = vol_ma*buy，成交量較小的優先</li>
                    <li><strong>限制持股數量：</strong>buy.is_smallest(5)，最多選5檔</li>
                    <li><strong>資金配置：</strong>position_limit=1/3，每檔最多33.3%</li>
                </ul>

                <h3>📈 交易設定</h3>
                <ul>
                    <li><strong>進出價格：</strong>trade_at_price='open'，開盤價進出</li>
                    <li><strong>換股頻率：</strong>resample="M"，月底換股</li>
                    <li><strong>停損設定：</strong>stop_loss=0.08，8%停損</li>
                    <li><strong>手續費：</strong>fee_ratio=1.425/1000/3</li>
                </ul>
            """,

            "勝率73.45%": """
                <h2>📈 勝率73.45% - 技術分析詳解</h2>

                <h3>🎯 條件詳細說明</h3>
                <ol>
                    <li><strong>年線趨勢(MA240)：</strong>
                        <ul>
                            <li>當日MA240 > 昨日MA240</li>
                            <li>確保長期趨勢向上</li>
                        </ul>
                    </li>
                    <li><strong>年線未來趨勢：</strong>
                        <ul>
                            <li>預測未來20日MA240走勢</li>
                            <li>提前確認趨勢延續性</li>
                        </ul>
                    </li>
                    <li><strong>月線趨勢(MA20)：</strong>
                        <ul>
                            <li>5日內MA20持續向上</li>
                            <li>中期趨勢確認</li>
                        </ul>
                    </li>
                    <li><strong>量能黃金交叉：</strong>
                        <ul>
                            <li>3日均量 > 18日均量</li>
                            <li>3日內出現交叉信號</li>
                        </ul>
                    </li>
                    <li><strong>RSI複合條件：</strong>
                        <ul>
                            <li>RSI(13) > 50</li>
                            <li>RSI(6) < 70</li>
                            <li>避免超買區域</li>
                        </ul>
                    </li>
                </ol>

                <h3>📊 歷史回測數據</h3>
                <ul>
                    <li><strong>測試期間：</strong>2020-2023年</li>
                    <li><strong>樣本數量：</strong>1000+筆交易</li>
                    <li><strong>勝率：</strong>73.45%</li>
                    <li><strong>平均報酬：</strong>8.5%</li>
                    <li><strong>最大回撤：</strong>-12%</li>
                </ul>
            """,

            "破底反彈高量": """
                <h2>🔄 破底反彈高量 - 逆勢操作詳解</h2>

                <h3>🎯 策略邏輯</h3>
                <p>此策略專門捕捉個股在創近期新低後的反彈機會，適合在市場恐慌時逆勢操作。</p>

                <h3>📊 核心條件分析</h3>
                <ol>
                    <li><strong>破底確認：</strong>
                        <ul>
                            <li>創10日內新低</li>
                            <li>低於前期重要支撐</li>
                            <li>破底幅度 > 2%</li>
                        </ul>
                    </li>
                    <li><strong>反彈信號：</strong>
                        <ul>
                            <li>從最低點反彈 5-15%</li>
                            <li>不超過15%（避免追高）</li>
                            <li>反彈初期介入</li>
                        </ul>
                    </li>
                    <li><strong>成交量確認：</strong>
                        <ul>
                            <li>成交量 > 平均量2倍</li>
                            <li>確認有資金關注</li>
                            <li>量價配合</li>
                        </ul>
                    </li>
                    <li><strong>技術修復：</strong>
                        <ul>
                            <li>RSI開始回升</li>
                            <li>K線形態改善</li>
                            <li>均線支撐</li>
                        </ul>
                    </li>
                </ol>

                <h3>⚠️ 風險提醒</h3>
                <ul>
                    <li>逆勢操作風險較高</li>
                    <li>需要嚴格停損</li>
                    <li>適合短線操作</li>
                    <li>不適合趨勢明確的空頭市場</li>
                </ul>
            """,

            "膽小貓": """
                <h2>🐱 膽小貓策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略體質：</strong>跟貓咪一樣，體型小、敏感、膽小，喜歡安全的窩，不愛在外頭亂跑。擁有不錯的年化報酬/MDD，就像乖乖的貓貓。</p>

                <p><strong>主軸元素：</strong>低股價、低波動、營收成長是主軸元素，2008年回測至今幾乎年年獲利！</p>

                <p><strong>技術改良：</strong>參考《飆股的長相》裡的K線波動率，改善「低價股策略」的不穩定性。</p>

                <h3>🔧 核心技術創新</h3>
                <p><strong>波動率應用：</strong>利用波動率找到穩定性較高的創高突破股，增加買點的安全性，降低追到假突破的機率。</p>

                <p><strong>風險控制特色：</strong>由於低波動的特性，能讓勝敗手的波動特性分離，可以停損設的小，大幅降低虧錢的交易的下檔風險，雖然勝率比較低，但果斷停損是為了防止未來持續失血。</p>

                <h3>🎯 完整篩選條件（對應原始程式碼）</h3>

                <h4>📈 條件1：創新高確認</h4>
                <ul>
                    <li><strong>程式條件：</strong>收盤價近5日至少有1日創收盤價近100日新高</li>
                    <li><strong>技術意義：</strong>(close == close.rolling(100).max()).sustain(5,1)</li>
                    <li><strong>目的：</strong>確保股票處於突破上升趨勢</li>
                </ul>

                <h4>📊 條件2：價格區間控制（低波動核心）</h4>
                <ul>
                    <li><strong>程式條件：</strong>近60日股價高低區間在30%內</li>
                    <li><strong>計算公式：</strong>(1 - low.rolling(60).min()/high.rolling(60).max()) < 0.3</li>
                    <li><strong>核心價值：</strong>篩選低波動、穩定的股票，降低假突破風險</li>
                </ul>

                <h4>💰 條件3：低股價雙重篩選</h4>
                <ul>
                    <li><strong>市場分位數：</strong>收盤價低於整體市場分級的40%</li>
                    <li><strong>絕對價格：</strong>收盤價低於30元</li>
                    <li><strong>程式條件：</strong>close <= close.quantile_row(0.4) & close <= 30</li>
                    <li><strong>策略意義：</strong>專注小型股，提高成長空間</li>
                </ul>

                <h4>📊 條件4：流動性確認</h4>
                <ul>
                    <li><strong>程式條件：</strong>5日均量大於100張</li>
                    <li><strong>計算：</strong>vol.average(5) > 100*1000</li>
                    <li><strong>目的：</strong>確保基本流動性，避免冷門股</li>
                </ul>

                <h4>📉 條件5：K線波動率濾網 ⭐核心技術⭐</h4>
                <ul>
                    <li><strong>程式條件：</strong>K線波動率 ≤ 8%</li>
                    <li><strong>計算邏輯：</strong>
                        <ul>
                            <li>紅K波動率 = abs(close.shift() - open_) + abs(open_ - low) + abs(low - high) + abs(high - close)</li>
                            <li>黑K波動率 = abs(close.shift() - open_) + abs(open_ - high) + abs(high-low) + abs(low - close)</li>
                            <li>標準化：candle_volatility.average(timeperiod) / close.average(timeperiod) * 100</li>
                        </ul>
                    </li>
                    <li><strong>核心價值：</strong>改善低價股策略不穩定性的關鍵技術</li>
                    <li><strong>風險控制：</strong>低波動特性支持小停損策略</li>
                </ul>

                <h4>📈 條件6：營收動能確認</h4>
                <ul>
                    <li><strong>程式條件：</strong>營收短期動能大於長期動能</li>
                    <li><strong>計算：</strong>rev.average(2) > rev.average(12)</li>
                    <li><strong>目的：</strong>確保基本面成長動能支撐</li>
                </ul>

                <h4>🎯 最終篩選：前8低價標的</h4>
                <ul>
                    <li><strong>程式邏輯：</strong>最後再挑選前8低價的標地</li>
                    <li><strong>實作：</strong>close * (position.astype(int)) → position[position > 0].is_smallest(8)</li>
                    <li><strong>策略意義：</strong>在符合條件的股票中，優選最低價的標的</li>
                </ul>

                <h3>📊 完整操作參數</h3>
                <ul>
                    <li><strong>訊號產生：</strong>每月底產生訊號、隔月第一個交易日進場</li>
                    <li><strong>交易價格：</strong>開盤價進出</li>
                    <li><strong>持有限制：</strong>每檔標的持有部位上限為20%</li>
                    <li><strong>停損設定：</strong>3%停損（stop_loss=0.03）</li>
                    <li><strong>手續費：</strong>設定交易手續費折扣（fee_ratio=1.425/1000*0.3）</li>
                    <li><strong>風險監控：</strong>MAE/MFE視窗40天（mae_mfe_window=40）</li>
                </ul>

                <h3>🐱 膽小貓投資哲學實踐</h3>
                <ul>
                    <li><strong>有不對勁就膽小走人：</strong>3%小停損，及時止損保護資本</li>
                    <li><strong>有順風的標的就長抱：</strong>月度調整，讓獲利奔跑</li>
                    <li><strong>保持小賠大賺的節奏：</strong>低波動支持小停損，控制下檔風險</li>
                </ul>
            """,

            "CANSLIM量價齊升": """
                <h2>🔍 CANSLIM量價齊升 - 威廉·歐尼爾詳細技術說明</h2>

                <h3>📊 策略背景與理念</h3>
                <p><strong>創始人：</strong>威廉·歐尼爾(William O'Neil)，《投資者商業日報》創辦人</p>
                <p><strong>核心理念：</strong>結合基本面、技術面、籌碼面的綜合選股法</p>
                <p><strong>歷史驗證：</strong>1953-1993年間，年化報酬率達17.6%</p>

                <h3>🎯 CANSLIM七大要素詳解</h3>

                <h4>📈 C - Current Quarterly Earnings (當季盈餘)</h4>
                <ul>
                    <li><strong>標準：</strong>當季每股盈餘年增率 ≥ 25%</li>
                    <li><strong>系統判斷：</strong>近3個月股價漲幅 > 15%（強勢）或 > 5%（穩健）</li>
                    <li><strong>權重：</strong>25分（滿分100分）</li>
                    <li><strong>重要性：</strong>盈餘是股價上漲的根本動力</li>
                </ul>

                <h4>📊 A - Annual Earnings Growth (年度盈餘增長)</h4>
                <ul>
                    <li><strong>標準：</strong>過去3年年度盈餘增長率 ≥ 25%</li>
                    <li><strong>系統判斷：</strong>年度股價漲幅 > 30%（強勢）或 > 15%（穩健）</li>
                    <li><strong>權重：</strong>20分</li>
                    <li><strong>重要性：</strong>確保公司具備持續成長能力</li>
                </ul>

                <h4>🆕 N - New Products, Services, or Management (新產品/服務/管理)</h4>
                <ul>
                    <li><strong>標準：</strong>公司推出重大新產品、服務或管理變革</li>
                    <li><strong>系統判斷：</strong>近期是否創新高（可能反映新題材）</li>
                    <li><strong>權重：</strong>15分</li>
                    <li><strong>重要性：</strong>創新是股價突破的催化劑</li>
                </ul>

                <h4>📊 S - Supply and Demand (供需關係) ⭐核心⭐</h4>
                <ul>
                    <li><strong>標準：</strong>成交量隨股價上漲而放大（量價齊升）</li>
                    <li><strong>系統判斷：</strong>
                        <ul>
                            <li>成交量 ≥ 1.5倍近期平均量</li>
                            <li>成交金額 ≥ 1000萬元</li>
                            <li>股價同步上漲 ≥ 1%</li>
                        </ul>
                    </li>
                    <li><strong>權重：</strong>20分（必要條件）</li>
                    <li><strong>重要性：</strong>量價配合是最可靠的買進信號</li>
                </ul>

                <h4>🏆 L - Leader or Laggard (領導股vs落後股)</h4>
                <ul>
                    <li><strong>標準：</strong>相對強度指標(RPS) ≥ 80</li>
                    <li><strong>系統判斷：</strong>與大盤相比的相對表現</li>
                    <li><strong>權重：</strong>10分</li>
                    <li><strong>重要性：</strong>買領導股，避開落後股</li>
                </ul>

                <h4>🏛️ I - Institutional Sponsorship (機構認同度)</h4>
                <ul>
                    <li><strong>標準：</strong>至少有幾家機構持有並增持</li>
                    <li><strong>系統判斷：</strong>成交量穩定性（變異係數 < 0.5）</li>
                    <li><strong>權重：</strong>5分</li>
                    <li><strong>重要性：</strong>機構資金是股價上漲的推動力</li>
                </ul>

                <h4>📈 M - Market Direction (市場方向)</h4>
                <ul>
                    <li><strong>標準：</strong>大盤處於上升趨勢</li>
                    <li><strong>系統判斷：</strong>大盤或個股高於20日均線</li>
                    <li><strong>權重：</strong>5分</li>
                    <li><strong>重要性：</strong>順勢而為，提高勝率</li>
                </ul>

                <h3>🎯 量價齊升核心邏輯</h3>
                <p><strong>威廉·歐尼爾名言：</strong>"成交量是股票的生命力，沒有成交量的上漲是不可持續的"</p>

                <h4>📊 量價配合的三種理想型態：</h4>
                <ol>
                    <li><strong>爆量上漲：</strong>成交量 ≥ 2倍 + 漲幅 ≥ 2%（20分）</li>
                    <li><strong>放量上漲：</strong>成交量 ≥ 1.5倍 + 漲幅 ≥ 1%（15分）</li>
                    <li><strong>溫和放量：</strong>成交量 ≥ 1.2倍 + 上漲（10分）</li>
                </ol>

                <h4>💰 成交金額要求：</h4>
                <ul>
                    <li><strong>高流動性：</strong>成交金額 ≥ 2000萬（+5分）</li>
                    <li><strong>充足流動性：</strong>成交金額 ≥ 1000萬（+3分）</li>
                    <li><strong>目的：</strong>確保有足夠的機構資金參與</li>
                </ul>

                <h3>📊 評分系統</h3>
                <ul>
                    <li><strong>總分：</strong>100分制</li>
                    <li><strong>通過標準：</strong>總分 ≥ 60分 且 S項目必須通過</li>
                    <li><strong>S項目重要性：</strong>量價配合是必要條件，其他條件是加分項</li>
                </ul>
            """,

            "研發魔人": """
                <h2>🔬 研發魔人策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略來源：</strong>專門針對高研發投入的科技公司設計的短線操作策略。</p>

                <p><strong>核心思想：</strong>鎖定專注研發的 top 科技公司，操作股價創新高的強勢股。</p>

                <p><strong>理論基礎：</strong>高研發投入的科技公司具有較強的創新能力和成長潛力。</p>

                <h3>🔍 選股條件詳解（6個核心條件）</h3>
                <h4>📊 基本面條件</h4>
                <ul>
                    <li><strong>研發費用率排名前20%：</strong>research_ratio_rank >= 0.8</li>
                    <li><strong>營收連2月年增：</strong>(month_rev_growth > 0).sustain(2)</li>
                </ul>

                <h4>📈 技術面條件</h4>
                <ul>
                    <li><strong>股價創10日新高：</strong>close == close.rolling(10).max()</li>
                    <li><strong>股價多頭排列：</strong>close > sma20 > sma60</li>
                    <li><strong>股價小於200：</strong>close < 200</li>
                    <li><strong>近3日均量>200張：</strong>vol.average(3) > 200000</li>
                </ul>

                <h3>📊 評分系統（100分制）</h3>
                <ul>
                    <li><strong>研發投入：</strong>30分 - 當期研發費用率排名前20%</li>
                    <li><strong>營收成長：</strong>20分 - 營收連2月年增</li>
                    <li><strong>10日新高：</strong>15分 - 股價創10日新高</li>
                    <li><strong>多頭排列：</strong>15分 - 股價多頭排列</li>
                    <li><strong>價格合理：</strong>10分 - 股價小於200</li>
                    <li><strong>成交量：</strong>10分 - 近3日均量>200張</li>
                </ul>

                <h3>🔄 操作機制</h3>
                <ul>
                    <li><strong>進場條件：</strong>所有6個條件同時滿足</li>
                    <li><strong>換股週期：</strong>每兩週重新評估和換股</li>
                    <li><strong>賣出條件：</strong>股價連2日落於月線下</li>
                    <li><strong>風險控制：</strong>10%停損，15%停利</li>
                    <li><strong>持股限制：</strong>最多持有10檔股票</li>
                </ul>
            """,

            "低價股策略": """
                <h2>💰 低價股策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略來源：</strong>專門針對低價股設計的量化選股策略，排除金融股。</p>

                <p><strong>核心思想：</strong>在低價股中尋找具有突破潛力的標的，結合價格突破和成交量確認。</p>

                <p><strong>理論基礎：</strong>低價股具有較大的上漲空間，但需要嚴格的篩選條件來控制風險。</p>

                <h3>🔍 選股條件詳解（5個核心條件）</h3>
                <h4>📈 條件1：近5日創120日新高 (25分)</h4>
                <ul>
                    <li><strong>邏輯：</strong>收盤價近5日至少有1日創收盤價近120日新高</li>
                    <li><strong>意義：</strong>確保股票處於強勢突破狀態</li>
                    <li><strong>技術：</strong>close == close.rolling(120).max()).sustain(5,1)</li>
                </ul>

                <h4>📊 條件2：價格區間收斂 (20分)</h4>
                <ul>
                    <li><strong>邏輯：</strong>近60日股價高低區間在30%內</li>
                    <li><strong>意義：</strong>價格整理充分，準備突破</li>
                    <li><strong>計算：</strong>(1 - low.rolling(60).min()/high.rolling(60).max()) < 0.3</li>
                </ul>

                <h4>💰 條件3：低價股特徵 (35分)</h4>
                <ul>
                    <li><strong>市場分級：</strong>收盤價低於整體市場分級的40% (20分)</li>
                    <li><strong>絕對價格：</strong>收盤價低於25元 (15分)</li>
                    <li><strong>意義：</strong>確保為真正的低價股</li>
                </ul>

                <h4>📊 條件4：成交量充足 (20分)</h4>
                <ul>
                    <li><strong>邏輯：</strong>5日平均成交量大於100張</li>
                    <li><strong>意義：</strong>確保有足夠的流動性</li>
                    <li><strong>計算：</strong>vol.average(5) > 100*1000</li>
                </ul>

                <h3>📊 評分系統（100分制）</h3>
                <ul>
                    <li><strong>120日新高：</strong>25分 - 近5日內創120日新高</li>
                    <li><strong>價格區間：</strong>20分 - 60日價格區間≤30%</li>
                    <li><strong>市場分級：</strong>20分 - 低於市場40%分位</li>
                    <li><strong>絕對價格：</strong>15分 - 收盤價≤25元</li>
                    <li><strong>成交量：</strong>20分 - 5日均量>100張</li>
                </ul>
            """,

            "小蝦米跟大鯨魚": """
                <h2>🐋 小蝦米跟大鯨魚策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略來源：</strong>基於集保餘額分析和營收成長的量化選股策略。</p>

                <p><strong>核心思想：</strong>透過分析大戶（大鯨魚）的持股行為，找出被機構投資者看好的穩定成長股。</p>

                <p><strong>理論基礎：</strong>大戶通常具有更好的資訊和分析能力，跟隨大戶投資可以提高勝率。</p>

                <h3>🔍 選股邏輯詳解</h3>
                <h4>🐋 大戶持股分析</h4>
                <ul>
                    <li><strong>小戶定義：</strong>持股分級1-5級（小蝦米）</li>
                    <li><strong>大戶定義：</strong>持股分級9-15級（大鯨魚）</li>
                    <li><strong>持股比例：</strong>大戶持股 / (小戶持股 + 大戶持股)</li>
                    <li><strong>集中度變化：</strong>觀察8期持股比例變化趨勢</li>
                </ul>

                <h4>📈 營收成長篩選</h4>
                <ul>
                    <li><strong>月營收成長：</strong>當月營收 / 上月營收</li>
                    <li><strong>年營收成長：</strong>當月營收 / 去年同月營收</li>
                    <li><strong>平滑成長：</strong>近2月平均 / 去年同期2月平均</li>
                    <li><strong>成長穩定性：</strong>避免單月異常波動</li>
                </ul>

                <h3>📊 評分系統（100分制）</h3>
                <ul>
                    <li><strong>長期趨勢：</strong>25分 - 股價高於250日均線</li>
                    <li><strong>大戶持股：</strong>30分 - 成交量穩定度（模擬大戶持股）</li>
                    <li><strong>營收成長：</strong>25分 - 價格動能（模擬營收成長）</li>
                    <li><strong>殖利率：</strong>10分 - 價格相對位置（模擬殖利率）</li>
                    <li><strong>流動性：</strong>10分 - 日成交值 > 500萬元</li>
                </ul>
            """,

            "台股總體經濟ETF": """
                <h2>📊 台股總體經濟ETF策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略來源：</strong>基於台灣總體經濟指標的量化分析，整合多維度數據進行投資決策。</p>

                <p><strong>核心思想：</strong>依據台灣總經指標、基本面、技術面、籌碼面數據，篩選出領先指標後綜合編製而成。</p>

                <p><strong>理論基礎：</strong>總體經濟環境決定市場大方向，透過量化指標判斷市場風險與機會。</p>

                <h3>🔍 評分指標詳解（10分制）</h3>
                <h4>📈 技術面指標 (4分)</h4>
                <ul>
                    <li><strong>大盤指數與月線交叉：</strong>1分 - 指數高於20日均線</li>
                    <li><strong>多空排列家數：</strong>1.5分 - 短中長期均線多頭排列</li>
                    <li><strong>ADL指標：</strong>1.5分 - 價格動能向上</li>
                    <li><strong>MACD週線：</strong>1分 - MACD信號向上</li>
                </ul>

                <h4>💰 籌碼面指標 (3分)</h4>
                <ul>
                    <li><strong>融資維持率：</strong>2分或-1分 - 成交量萎縮(+2)，過熱(-1)</li>
                    <li><strong>股價淨值比：</strong>1分或-1分 - 低檔(+1)，高檔(-1)</li>
                </ul>

                <h4>🏭 基本面指標 (3分)</h4>
                <ul>
                    <li><strong>製造業PMI：</strong>1.5分 - 趨勢強度指標</li>
                    <li><strong>非製造業NMI：</strong>1分 - 市場穩定度</li>
                    <li><strong>未來六個月展望：</strong>1.5分 - 動能指標</li>
                    <li><strong>景氣對策信號：</strong>2分 - 綜合技術指標</li>
                </ul>

                <h3>📊 評分標準與配置策略</h3>
                <ul>
                    <li><strong>6分以上：</strong>積極指標 - 台灣50(50%) + 台灣中型100(50%)</li>
                    <li><strong>4-6分：</strong>中立指標 - 台灣50(50%) + 台灣中型100(50%)</li>
                    <li><strong>4分以下：</strong>風險指標 - 台灣50反(100%)</li>
                </ul>
            """,

            "超級績效台股版": """
                <h2>🚀 超級績效台股版策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略來源：</strong>基於投資家 Mark Minervini 的超級績效交易策略，專為台股市場優化調整。</p>

                <p><strong>核心思想：</strong>結合技術分析和基本面分析，重點尋找長期上漲、價量收縮和爆量突破的成長股。</p>

                <p><strong>理論基礎：</strong>使用趨勢樣板和 VCP（Volatility Contraction Pattern）模式，識別即將爆發的強勢股票。</p>

                <h3>🔍 VCP模式詳解</h3>
                <ul>
                    <li><strong>價格收縮：</strong>10日價格波動率 < 60日價格波動率 × 0.5</li>
                    <li><strong>成交量收縮：</strong>10日平均成交量 < 60日平均成交量 × 0.5</li>
                    <li><strong>創新高：</strong>股價達到100日內最高價</li>
                    <li><strong>成交量確認：</strong>當日成交量 ≥ 20日平均成交量 × 0.8</li>
                </ul>

                <h3>📈 選股條件</h3>
                <ul>
                    <li><strong>長期趨勢：</strong>股價高於250日移動平均線（25分）</li>
                    <li><strong>VCP模式：</strong>符合價量收縮突破模式（30分）</li>
                    <li><strong>營收成長：</strong>短期趨勢強於中期趨勢（20分）</li>
                    <li><strong>通道狹窄：</strong>20日最低價 > 20日最高價 × 0.7（15分）</li>
                    <li><strong>流動性：</strong>日成交值 > 200萬元（10分）</li>
                </ul>

                <h3>📊 評分系統</h3>
                <ul>
                    <li><strong>總分範圍：</strong>0-100分</li>
                    <li><strong>通過門檻：</strong>≥80分 - 🚀 超級績效策略符合</li>
                    <li><strong>部分符合：</strong>60-79分 - 超級績效策略部分符合</li>
                    <li><strong>不符合：</strong><60分 - 缺乏成長股特徵</li>
                </ul>
            """,

            "監獄兔": """
                <h2>🐰 監獄兔策略 - 詳細技術說明</h2>

                <h3>📊 策略背景與核心理念</h3>
                <p><strong>策略命名：</strong>透過將此策略命名為「監獄兔」，巧妙地暗示了策略的狡猾和機敏，正如兔子在逆境中找到出路一般。</p>

                <p><strong>核心思想：</strong>專門針對被處置的股票，透過精心設計的條件篩選和持倉時間控制，來捕捉市場反應過度的機會。</p>

                <p><strong>理論基礎：</strong>策略的實施顯示了對市場心理和賽局理論的深刻理解，即通過預測其他投資者的行為和可能的過度反應來尋找利潤空間。</p>

                <h3>🔍 技術實現細節</h3>

                <h4>1. 基本篩選條件</h4>
                <ul>
                    <li><strong>股票代碼篩選：</strong>只針對4位數股票代碼的標的（以普通股為主，排除特殊金融商品）</li>
                    <li><strong>交易類型過濾：</strong>過濾掉非分盤交易的處置雜訊，確保數據的純粹性</li>
                    <li><strong>數據完整性：</strong>需要至少20天的歷史數據進行分析</li>
                </ul>

                <h4>2. 核心技術指標分析</h4>

                <p><strong>波動率分析（最高25分）：</strong></p>
                <ul>
                    <li>計算近期10日價格變化的年化波動率</li>
                    <li>高波動率（>40%）：25分 - 表示可能是處置股</li>
                    <li>中等波動率（>30%）：15分 - 有一定處置股特徵</li>
                    <li>技術意義：處置股通常伴隨高波動率</li>
                </ul>

                <p><strong>成交量異常檢測（最高20分）：</strong></p>
                <ul>
                    <li>計算當日成交量與20日移動平均的比率</li>
                    <li>異常放大（>2倍）：20分 - 成交量異常放大</li>
                    <li>放大（>1.5倍）：10分 - 成交量有所放大</li>
                    <li>技術意義：處置期間通常有異常成交量</li>
                </ul>

                <p><strong>價格位置評估（最高20分）：</strong></p>
                <ul>
                    <li>計算當前價格與20日移動平均的比率</li>
                    <li>相對低位（<0.9倍MA20）：20分 - 價格被過度拋售</li>
                    <li>略低於均線（<0.95倍MA20）：10分 - 價格相對偏低</li>
                    <li>技術意義：處置期間股價容易被過度拋售</li>
                </ul>

                <p><strong>RSI超賣指標（最高25分）：</strong></p>
                <ul>
                    <li>計算14日RSI相對強弱指標</li>
                    <li>嚴重超賣（RSI<30）：25分 - 嚴重超賣狀態</li>
                    <li>輕度超賣（RSI<40）：15分 - 輕度超賣狀態</li>
                    <li>技術意義：處置股容易出現超賣現象</li>
                </ul>

                <p><strong>反彈跡象確認（最高10分）：</strong></p>
                <ul>
                    <li>計算近5日價格變化百分比</li>
                    <li>明顯反彈（>3%）：10分 - 出現明顯反彈</li>
                    <li>止跌企穩（>0%）：5分 - 止跌企穩跡象</li>
                    <li>技術意義：確認是否有反彈跡象</li>
                </ul>

                <h3>📈 評分系統與判斷標準</h3>
                <ul>
                    <li><strong>總分範圍：</strong>0-100分</li>
                    <li><strong>通過門檻：</strong>≥70分 - 🐰 監獄兔策略符合</li>
                    <li><strong>部分符合：</strong>50-69分 - 監獄兔策略部分符合</li>
                    <li><strong>不符合：</strong><50分 - 缺乏處置股特徵</li>
                </ul>

                <h3>💰 交易參數設定</h3>
                <ul>
                    <li><strong>交易價格：</strong>trade_at_price="open" - 以開盤價交易</li>
                    <li><strong>手續費比率：</strong>fee_ratio=1.425/1000/3 - 考慮交易成本</li>
                    <li><strong>持倉限制：</strong>position_limit=0.2 - 單一標的最多20%</li>
                    <li><strong>進場時機：</strong>處置開始時間</li>
                    <li><strong>出場時機：</strong>處置結束時間</li>
                </ul>

                <h3>🔧 程式實現邏輯</h3>
                <pre style="background-color: #1e1e1e; padding: 10px; border-radius: 5px; color: #ffffff;">
# 核心實現邏輯
處置股資訊 = data.get('disposal_information').sort_index()
處置股資訊 = 處置股資訊[~處置股資訊["分時交易"].isna()].dropna(how='all')

for i in range(0, 處置股資訊.shape[0]):
    stock_id = 處置股資訊.iloc[i,0]
    if len(stock_id) == 4:  # 4碼普通股
        start_day = 處置股資訊.iloc[i,1]  # 處置開始時間
        end_day = 處置股資訊.iloc[i,2]    # 處置結束時間
        position.loc[start_day:end_day, stock_id] = True
                </pre>
            """
        }

        return strategy_details.get(self.strategy_name, f"""
            <h2>📊 {self.strategy_name} - 詳細說明</h2>
            <p>這是一個自定義策略，詳細條件請參考策略編輯器中的設定。</p>
            <p>您可以通過"編輯"按鈕查看和修改策略的具體條件。</p>
        """)

    def get_usage_guide(self):
        """獲取使用建議"""
        usage_guides = {
            "阿水一式": """
                <h2>💡 阿水一式 - 實戰使用指南</h2>

                <h3>🎯 最佳使用時機</h3>
                <ul>
                    <li><strong>大盤環境：</strong>大盤處於綠燈狀態（20MA向上）</li>
                    <li><strong>市場情緒：</strong>投資人信心較佳，成交量活絡</li>
                    <li><strong>避免時機：</strong>大盤轉為黃燈或紅燈時暫停使用</li>
                </ul>

                <h3>📈 操作建議</h3>
                <h4>🔍 選股流程：</h4>
                <ol>
                    <li><strong>篩選：</strong>使用系統篩選出符合條件的股票</li>
                    <li><strong>評分：</strong>優先關注評分 ≥ 90分的標的</li>
                    <li><strong>確認：</strong>人工確認是否有題材或基本面支撐</li>
                    <li><strong>時機：</strong>在突破當日或隔日開盤買入</li>
                </ol>

                <h4>💰 資金管理：</h4>
                <ul>
                    <li><strong>單筆投入：</strong>不超過總資金的10%</li>
                    <li><strong>分散投資：</strong>同時持有3-5檔不同類股</li>
                    <li><strong>預留資金：</strong>保留30%現金應對機會</li>
                </ul>

                <h3>🛡️ 停利停損策略</h3>
                <h4>📈 停利方法：</h4>
                <ul>
                    <li><strong>主要停利：</strong>若大盤沒有翻成紅燈，個股跌破20MA時賣出</li>
                    <li><strong>獲利了結：</strong>漲幅達20-30%時分批獲利了結</li>
                    <li><strong>移動停利：</strong>設定移動停利點，鎖定獲利</li>
                </ul>

                <h4>🛑 停損控制：</h4>
                <ul>
                    <li><strong>快速停損：</strong>買進後隔天中尾盤跌破入手價5%先出場</li>
                    <li><strong>大盤轉弱：</strong>大盤翻成黃燈或紅燈時，賣出轉弱個股</li>
                    <li><strong>技術破位：</strong>跌破個股近期重要支撐時停損</li>
                </ul>

                <h3>⚠️ 風險提醒</h3>
                <ul>
                    <li><strong>市場風險：</strong>大盤轉弱時策略效果會下降</li>
                    <li><strong>個股風險：</strong>避免追漲停板或爆量股票</li>
                    <li><strong>時機風險：</strong>突破後若無法持續上漲要及時停損</li>
                    <li><strong>資金風險：</strong>嚴格控制單筆投入金額</li>
                </ul>

                <h3>📊 績效追蹤</h3>
                <ul>
                    <li><strong>記錄交易：</strong>詳細記錄每筆交易的進出場理由</li>
                    <li><strong>定期檢討：</strong>每月檢討策略執行效果</li>
                    <li><strong>調整參數：</strong>根據市場變化適度調整篩選條件</li>
                </ul>
            """,

            "阿水二式": """
                <h2>💡 阿水二式 - 空方實戰指南</h2>

                <h3>🎯 最佳使用時機</h3>
                <ul>
                    <li><strong>大盤環境：</strong>大盤處於紅燈狀態（20MA向下）</li>
                    <li><strong>市場情緒：</strong>投資人信心不足，成交量萎縮</li>
                    <li><strong>避免時機：</strong>大盤轉為綠燈時停止做空</li>
                </ul>

                <h3>📈 操作建議</h3>
                <h4>🔍 選股流程：</h4>
                <ol>
                    <li><strong>篩選：</strong>使用系統篩選出符合條件的股票</li>
                    <li><strong>評分：</strong>優先關注評分 ≥ 80分的標的</li>
                    <li><strong>確認：</strong>人工確認法人是否棄守、財報是否轉弱</li>
                    <li><strong>時機：</strong>在20MA價位附近放空</li>
                </ol>

                <h4>💰 資金管理：</h4>
                <ul>
                    <li><strong>單筆投入：</strong>不超過總資金的5%（空方風險較高）</li>
                    <li><strong>分散投資：</strong>同時放空2-3檔不同類股</li>
                    <li><strong>預留資金：</strong>保留充足現金應對軋空風險</li>
                </ul>

                <h3>🛡️ 停利停損策略</h3>
                <h4>📈 停利方法：</h4>
                <ul>
                    <li><strong>主要停利：</strong>底部爆出大量紅K時</li>
                    <li><strong>獲利了結：</strong>跌幅達15-25%時分批停利</li>
                    <li><strong>技術停利：</strong>跌破重要支撐後停利</li>
                </ul>

                <h4>🛑 停損控制：</h4>
                <ul>
                    <li><strong>快速停損：</strong>個股反彈到20MA時立即停損</li>
                    <li><strong>大盤轉強：</strong>大盤翻成綠燈時，立即停損所有空單</li>
                    <li><strong>軋空風險：</strong>跌不破近期支撐要特別小心反向軋空</li>
                </ul>

                <h3>⚠️ 特別注意事項</h3>
                <ul>
                    <li><strong>台股特性：</strong>體質不好的公司會一直跌，要有耐心</li>
                    <li><strong>領先指標：</strong>個股可能領先大盤下跌</li>
                    <li><strong>軋空風險：</strong>空方操作風險較高，需嚴格控制部位</li>
                    <li><strong>法人動向：</strong>密切關注外資、投信的買賣超情況</li>
                </ul>

                <h3>📊 績效追蹤</h3>
                <ul>
                    <li><strong>記錄交易：</strong>詳細記錄每筆放空的理由和結果</li>
                    <li><strong>定期檢討：</strong>每週檢討空方策略執行效果</li>
                    <li><strong>風險控制：</strong>嚴格監控空方部位總額</li>
                    <li><strong>市場轉換：</strong>隨時準備從空方轉為多方</li>
                </ul>
            """,

            "藏獒": """
                <h2>💡 藏獒 - 動能實戰指南</h2>

                <h3>🎯 最佳使用時機</h3>
                <ul>
                    <li><strong>市場環境：</strong>大多頭市場，動能強勁</li>
                    <li><strong>個股特徵：</strong>股價創年新高，營收底部確認</li>
                    <li><strong>避免時機：</strong>市場震盪或下跌趨勢時效果較差</li>
                </ul>

                <h3>📈 操作建議</h3>
                <h4>🔍 選股流程：</h4>
                <ol>
                    <li><strong>篩選：</strong>使用系統篩選出符合條件的股票</li>
                    <li><strong>評分：</strong>優先關注動能評分 ≥ 85分的標的</li>
                    <li><strong>確認：</strong>人工確認營收數據和基本面</li>
                    <li><strong>時機：</strong>月底換股，開盤價進出</li>
                </ol>

                <h4>💰 資金管理：</h4>
                <ul>
                    <li><strong>持股數量：</strong>最多5檔股票</li>
                    <li><strong>單檔比重：</strong>每檔最多33.3%</li>
                    <li><strong>避免重壓：</strong>防止只選到1-2檔時的個股風險</li>
                    <li><strong>流動性要求：</strong>確保10日均量 > 20萬股</li>
                </ul>

                <h3>🛡️ 停利停損策略</h3>
                <h4>📈 停利方法：</h4>
                <ul>
                    <li><strong>持有策略：</strong>勝手常持有後一去不回，回檔小</li>
                    <li><strong>月底換股：</strong>押寶下月營收公布利多</li>
                    <li><strong>動能延續：</strong>只要動能持續就繼續持有</li>
                </ul>

                <h4>🛑 停損控制：</h4>
                <ul>
                    <li><strong>固定停損：</strong>8%停損，偏低位置</li>
                    <li><strong>保留獲利：</strong>保留多數獲利部位與下檔波動</li>
                    <li><strong>快速反應：</strong>遇到中期回檔也砍得快</li>
                </ul>

                <h3>⚠️ 風險控制要點</h3>
                <ul>
                    <li><strong>波動風險：</strong>此策略波動較高</li>
                    <li><strong>調整建議：</strong>若覺得波動風險太大，可調降position_limit</li>
                    <li><strong>條件放寬：</strong>或放寬條件讓標的數變多</li>
                    <li><strong>個人選擇：</strong>追求報酬率還是穩定性，看個人選擇</li>
                </ul>

                <h3>📊 策略特性</h3>
                <ul>
                    <li><strong>勝率：</strong>一半一半，但勝手常一去不回</li>
                    <li><strong>報酬特性：</strong>少數大勝彌補多數小敗</li>
                    <li><strong>持有期間：</strong>月度調整，中長期持有</li>
                    <li><strong>適合對象：</strong>能承受較高波動的投資者</li>
                </ul>

                <h3>🔧 實戰技巧</h3>
                <ul>
                    <li><strong>營收關注：</strong>密切關注月營收公布</li>
                    <li><strong>動能追蹤：</strong>持續監控股價動能</li>
                    <li><strong>基本面確認：</strong>確保營收底部確認</li>
                    <li><strong>流動性監控：</strong>避免流動性不足的股票</li>
                </ul>
            """,

            "勝率73.45%": """
                <h2>💡 勝率73.45% - 使用指南</h2>

                <h3>🎯 適用環境</h3>
                <ul>
                    <li><strong>趨勢市場：</strong>適合明確的多頭或空頭趨勢</li>
                    <li><strong>成交量：</strong>市場成交量穩定，流動性良好</li>
                    <li><strong>避免：</strong>盤整震盪市場效果較差</li>
                </ul>

                <h3>📈 操作建議</h3>
                <ul>
                    <li><strong>持有期間：</strong>中長期持有（1-3個月）</li>
                    <li><strong>進場時機：</strong>所有條件確認後進場</li>
                    <li><strong>加碼策略：</strong>可在回檔時適度加碼</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>停損：</strong>跌破年線(MA240)時停損</li>
                    <li><strong>停利：</strong>達到預期報酬率時分批停利</li>
                    <li><strong>資金配置：</strong>單筆不超過15%資金</li>
                </ul>
            """,

            "破底反彈高量": """
                <h2>💡 破底反彈高量 - 使用指南</h2>

                <h3>🎯 適用時機</h3>
                <ul>
                    <li><strong>市場恐慌：</strong>個股或市場出現恐慌性下跌</li>
                    <li><strong>超跌反彈：</strong>技術指標顯示超跌</li>
                    <li><strong>避免：</strong>明確下跌趨勢中不宜使用</li>
                </ul>

                <h3>📈 操作要點</h3>
                <ul>
                    <li><strong>快進快出：</strong>短線操作為主</li>
                    <li><strong>嚴格停損：</strong>設定3-5%停損點</li>
                    <li><strong>分批進場：</strong>可分2-3次進場</li>
                </ul>

                <h3>⚠️ 特別注意</h3>
                <ul>
                    <li><strong>高風險：</strong>逆勢操作風險較高</li>
                    <li><strong>資金控制：</strong>單筆不超過5%資金</li>
                    <li><strong>及時停損：</strong>一旦判斷錯誤立即停損</li>
                </ul>
            """,

            "膽小貓": """
                <h2>💡 膽小貓策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>歷史驗證：</strong>2008年回測至今幾乎年年獲利！擁有不錯的年化報酬/MDD，就像乖乖的貓貓。</p>
                <p><strong>技術改良：</strong>參考《飆股的長相》裡的K線波動率，改善「低價股策略」的不穩定性。</p>
                <p><strong>風險控制：</strong>利用波動率找到穩定性較高的創高突破股，增加買點的安全性，降低追到假突破的機率。</p>

                <h3>🐱 膽小貓特質與操作哲學</h3>
                <ul>
                    <li><strong>體型小：</strong>專注低股價股票（≤30元），小型股成長潛力大</li>
                    <li><strong>敏感：</strong>對波動敏感，K線波動率≤8%確保穩定性</li>
                    <li><strong>膽小：</strong>喜歡安全的窩，不愛在外頭亂跑，嚴格風險控制</li>
                    <li><strong>低波動特性：</strong>能讓勝敗手的波動特性分離</li>
                    <li><strong>小停損策略：</strong>可以停損設的小（3%），大幅降低虧錢交易的下檔風險</li>
                </ul>

                <h3>📊 完整操作流程（對應原始策略）</h3>
                <h4>🔍 篩選條件執行順序：</h4>
                <ol>
                    <li><strong>基礎篩選：</strong>排除金融股，專注TSE_OTC市場</li>
                    <li><strong>創新高確認：</strong>近5日至少有1日創收盤價近100日新高</li>
                    <li><strong>波動控制：</strong>近60日股價高低區間在30%內</li>
                    <li><strong>低價篩選：</strong>收盤價≤30元且低於市場40%分位數</li>
                    <li><strong>流動性確認：</strong>5日均量>100張</li>
                    <li><strong>波動率濾網：</strong>K線波動率≤8%（核心技術）</li>
                    <li><strong>營收動能：</strong>短期營收動能>長期營收動能</li>
                    <li><strong>最終選股：</strong>挑選前8低價的標的</li>
                </ol>

                <h4>📅 交易執行參數：</h4>
                <ul>
                    <li><strong>訊號產生：</strong>每月底產生訊號（resample="M"）</li>
                    <li><strong>進場時機：</strong>隔月第一個交易日進場</li>
                    <li><strong>交易價格：</strong>開盤價進出（trade_at_price='open'）</li>
                    <li><strong>持有限制：</strong>每檔標的持有部位上限20%（position_limit=1/5）</li>
                    <li><strong>停損設定：</strong>3%停損（stop_loss=0.03）</li>
                    <li><strong>手續費：</strong>1.425/1000*0.3的手續費折扣</li>
                </ul>

                <h3>🛡️ 風險控制核心</h3>
                <h4>🎯 膽小貓投資哲學實踐：</h4>
                <ul>
                    <li><strong>有不對勁就膽小走人：</strong>
                        <ul>
                            <li>3%固定停損，果斷停損防止未來持續失血</li>
                            <li>K線波動率突然放大時立即停損</li>
                            <li>營收動能轉弱時停損</li>
                        </ul>
                    </li>
                    <li><strong>有順風的標的就長抱：</strong>
                        <ul>
                            <li>月度調整，讓獲利奔跑</li>
                            <li>持續符合條件的股票繼續持有</li>
                            <li>低波動特性支持長期持有</li>
                        </ul>
                    </li>
                    <li><strong>保持小賠大賺的節奏：</strong>
                        <ul>
                            <li>雖然勝率比較低，但控制下檔風險</li>
                            <li>低波動讓勝敗手波動特性分離</li>
                            <li>小停損配合長期持有獲利股票</li>
                        </ul>
                    </li>
                </ul>

                <h3>📈 績效監控與調整</h3>
                <ul>
                    <li><strong>月度檢討：</strong>每月底重新篩選，動態調整持股</li>
                    <li><strong>波動率監控：</strong>持續監控K線波動率變化</li>
                    <li><strong>營收追蹤：</strong>關注月營收公布，確認營收動能</li>
                    <li><strong>MAE/MFE分析：</strong>40天視窗分析最大利潤/虧損</li>
                    <li><strong>回測驗證：</strong>定期回測驗證策略有效性</li>
                </ul>

                <h3>⚠️ 注意事項與風險提醒</h3>
                <ul>
                    <li><strong>勝率特性：</strong>勝率比較低，但透過小停損控制風險</li>
                    <li><strong>耐心等待：</strong>需要耐心等待符合條件的股票出現</li>
                    <li><strong>市場環境：</strong>雖然適應性強，但仍需注意系統性風險</li>
                    <li><strong>流動性風險：</strong>低價股可能面臨流動性不足問題</li>
                    <li><strong>技術依賴：</strong>高度依賴K線波動率技術指標</li>
                </ul>

                <h3>🎯 實戰成功關鍵</h3>
                <ul>
                    <li><strong>嚴格執行：</strong>嚴格按照7大條件篩選，不可妥協</li>
                    <li><strong>紀律停損：</strong>3%停損必須嚴格執行</li>
                    <li><strong>月度調整：</strong>按月重新評估和調整持股</li>
                    <li><strong>波動率核心：</strong>K線波動率≤8%是核心條件，不可忽視</li>
                    <li><strong>貓咪心態：</strong>保持膽小謹慎但敏銳的投資心態</li>
                </ul>
            """,

            "CANSLIM量價齊升": """
                <h2>💡 CANSLIM量價齊升 - 實戰使用指南</h2>

                <h3>🎯 最佳使用時機</h3>
                <ul>
                    <li><strong>牛市環境：</strong>大盤處於上升趨勢，成長股行情活躍</li>
                    <li><strong>成交活躍：</strong>市場整體成交量放大，資金充沛</li>
                    <li><strong>題材豐富：</strong>市場有明確的成長主題或熱點</li>
                    <li><strong>機構積極：</strong>機構投資者積極布局成長股</li>
                </ul>

                <h3>📊 選股操作流程</h3>
                <ol>
                    <li><strong>第一步：</strong>篩選出CANSLIM評分≥60分的股票</li>
                    <li><strong>第二步：</strong>確認S項目（量價齊升）必須通過</li>
                    <li><strong>第三步：</strong>優先選擇C、A項目得分高的股票</li>
                    <li><strong>第四步：</strong>檢查是否有新題材或創新高表現</li>
                    <li><strong>第五步：</strong>確認大盤環境支持（M項目）</li>
                </ol>

                <h3>💰 資金配置建議</h3>
                <ul>
                    <li><strong>分散投資：</strong>同時持有3-5支CANSLIM股票</li>
                    <li><strong>單筆限制：</strong>單一股票不超過總資金的20%</li>
                    <li><strong>分批進場：</strong>在量價齊升確認後分2-3批進場</li>
                    <li><strong>預留資金：</strong>保留30%現金應對加碼機會</li>
                </ul>

                <h3>🛡️ 停利停損策略</h3>
                <h4>📈 停利方法：</h4>
                <ul>
                    <li><strong>階段停利：</strong>漲幅達20%時先停利1/3</li>
                    <li><strong>趨勢停利：</strong>跌破20日均線時停利</li>
                    <li><strong>量能停利：</strong>出現高量長黑時停利</li>
                    <li><strong>評分停利：</strong>CANSLIM評分降至40分以下時停利</li>
                </ul>

                <h4>🛑 停損控制：</h4>
                <ul>
                    <li><strong>固定停損：</strong>買進後跌幅達8%立即停損</li>
                    <li><strong>技術停損：</strong>跌破重要支撐位停損</li>
                    <li><strong>量能停損：</strong>出現爆量下跌停損</li>
                    <li><strong>基本面停損：</strong>公司基本面惡化時停損</li>
                </ul>

                <h3>⚠️ 風險提醒</h3>
                <ul>
                    <li><strong>市場風險：</strong>熊市時CANSLIM策略效果會大幅下降</li>
                    <li><strong>成長股風險：</strong>成長股波動較大，需要較強的風險承受能力</li>
                    <li><strong>估值風險：</strong>避免追買本益比過高的股票</li>
                    <li><strong>流動性風險：</strong>確保成交金額足夠，避免流動性不足</li>
                </ul>

                <h3>📊 績效追蹤</h3>
                <ul>
                    <li><strong>記錄CANSLIM評分：</strong>追蹤每支股票的七項評分變化</li>
                    <li><strong>量價監控：</strong>密切關注成交量與股價的配合情況</li>
                    <li><strong>定期檢討：</strong>每週檢討持股的CANSLIM評分</li>
                    <li><strong>策略調整：</strong>根據市場環境調整選股標準</li>
                </ul>

                <h3>🔧 實戰技巧</h3>
                <ul>
                    <li><strong>關注財報：</strong>密切關注季報公布，確認C、A項目</li>
                    <li><strong>新聞追蹤：</strong>關注公司新產品、新服務消息</li>
                    <li><strong>機構動向：</strong>觀察機構買賣超情況</li>
                    <li><strong>相對強度：</strong>定期計算和更新RPS指標</li>
                    <li><strong>大盤判斷：</strong>隨時評估大盤趨勢變化</li>
                </ul>

                <h3>💡 威廉·歐尼爾投資心法</h3>
                <ul>
                    <li><strong>"買入上漲的股票，賣出下跌的股票"</strong></li>
                    <li><strong>"成交量是股票的生命力"</strong></li>
                    <li><strong>"買領導股，避開落後股"</strong></li>
                    <li><strong>"在牛市中賺錢，在熊市中保本"</strong></li>
                    <li><strong>"快速停損，讓獲利奔跑"</strong></li>
                </ul>
            """,

            # ⚡ 5分鐘策略系列說明
            "5分鐘突破策略": """
                <h2>⚡ 5分鐘突破策略 - 短線突破交易專家</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>基於價格突破關鍵阻力/支撐位的短線交易策略</strong></p>
                <ul>
                    <li><strong>突破邏輯：</strong>監控20期高低點突破</li>
                    <li><strong>量能確認：</strong>成交量必須放大1.5倍以上</li>
                    <li><strong>時效性：</strong>適合5分鐘級別的快速決策</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>開盤階段：</strong>09:00-09:30，捕捉開盤突破</li>
                    <li><strong>盤中關鍵時點：</strong>10:30、11:00、13:00等整點附近</li>
                    <li><strong>重要消息後：</strong>財報、重大消息公布後的價格反應</li>
                    <li><strong>技術位突破：</strong>前高、前低、整數關卡突破</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 買入信號：</h4>
                <ul>
                    <li>價格突破20期高點 + 成交量放大1.5倍</li>
                    <li>突破後不立即回落，顯示突破有效</li>
                    <li>最好配合大盤同步上漲</li>
                </ul>

                <h4>📉 賣出信號：</h4>
                <ul>
                    <li>價格跌破20期低點 + 成交量放大1.5倍</li>
                    <li>跌破後持續下跌，確認破位有效</li>
                    <li>適合做空或減倉操作</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>止損設置：</strong>突破失敗立即止損，通常2-3%</li>
                    <li><strong>止盈目標：</strong>突破後4-6%，或下一個阻力位</li>
                    <li><strong>持倉時間：</strong>通常1-3小時，最長不超過一個交易日</li>
                    <li><strong>假突破防範：</strong>等待突破後站穩再進場</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>量價配合：</strong>突破必須有成交量配合，否則可能是假突破</li>
                    <li><strong>趨勢確認：</strong>順勢突破成功率更高</li>
                    <li><strong>分批進場：</strong>可以分2-3批進場，降低風險</li>
                    <li><strong>快進快出：</strong>5分鐘策略講求效率，不宜戀戰</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>避免在震盪市場中頻繁使用</li>
                    <li>注意大盤環境，熊市中謹慎使用</li>
                    <li>單日使用次數不宜過多，避免過度交易</li>
                    <li>新手建議先在模擬環境中練習</li>
                </ul>
            """,

            "5分鐘均值回歸": """
                <h2>🔄 5分鐘均值回歸 - 震盪市場獲利專家</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>利用價格偏離均線的回歸特性進行短線交易</strong></p>
                <ul>
                    <li><strong>回歸理論：</strong>價格偏離均線後會回歸</li>
                    <li><strong>超買超賣：</strong>結合RSI指標判斷極值</li>
                    <li><strong>震盪獲利：</strong>適合橫盤整理的市場環境</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>震盪市場：</strong>大盤橫盤整理，個股來回波動</li>
                    <li><strong>盤中回調：</strong>上漲趨勢中的短期回調</li>
                    <li><strong>超買超賣：</strong>RSI達到極值區域</li>
                    <li><strong>均線附近：</strong>價格接近重要均線支撐/阻力</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 超賣回歸買入：</h4>
                <ul>
                    <li>價格低於MA20達3%以上</li>
                    <li>RSI低於30（超賣區域）</li>
                    <li>未跌破重要支撐位</li>
                    <li>預期價格回歸至MA20</li>
                </ul>

                <h4>📉 超買回歸賣出：</h4>
                <ul>
                    <li>價格高於MA20達3%以上</li>
                    <li>RSI高於70（超買區域）</li>
                    <li>未突破重要阻力位</li>
                    <li>預期價格回歸至MA20</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>止損設置：</strong>偏離度擴大至5%以上立即止損</li>
                    <li><strong>止盈目標：</strong>回歸至MA20附近，通常1-2%</li>
                    <li><strong>持倉時間：</strong>通常30分鐘-2小時</li>
                    <li><strong>趨勢風險：</strong>避免在強趨勢中逆勢操作</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>確認震盪：</strong>確保市場處於震盪狀態，非單邊趨勢</li>
                    <li><strong>支撐阻力：</strong>結合技術分析確認關鍵位置</li>
                    <li><strong>分批操作：</strong>可以分批建倉，平均成本</li>
                    <li><strong>快速決策：</strong>回歸機會稍縱即逝，要快速行動</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>不適用於強勢突破或崩跌行情</li>
                    <li>需要密切監控，及時調整策略</li>
                    <li>避免在重大消息前後使用</li>
                    <li>控制單次交易規模，分散風險</li>
                </ul>
            """,

            "5分鐘動量策略": """
                <h2>🚀 5分鐘動量策略 - 趨勢追蹤利器</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>捕捉價格動量和趨勢變化的短線策略</strong></p>
                <ul>
                    <li><strong>動量追蹤：</strong>跟隨價格動量方向</li>
                    <li><strong>MACD信號：</strong>利用MACD金叉死叉確認</li>
                    <li><strong>成交量確認：</strong>動量必須有成交量支撐</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>趨勢啟動：</strong>新趨勢剛開始形成時</li>
                    <li><strong>突破確認：</strong>重要技術位突破後的追蹤</li>
                    <li><strong>消息刺激：</strong>重大利多/利空消息後</li>
                    <li><strong>開盤動量：</strong>開盤後的強勢表現</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 動量買入信號：</h4>
                <ul>
                    <li>MACD出現金叉</li>
                    <li>3日累計漲幅超過1%</li>
                    <li>成交量放大1.2倍以上</li>
                    <li>RSI在50-75之間（避免超買）</li>
                </ul>

                <h4>📉 動量賣出信號：</h4>
                <ul>
                    <li>MACD出現死叉</li>
                    <li>3日累計跌幅超過1%</li>
                    <li>成交量放大1.2倍以上</li>
                    <li>RSI在25-50之間（避免超賣）</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>移動止損：</strong>隨著趨勢發展調整止損位</li>
                    <li><strong>動量衰減：</strong>動量減弱時及時減倉</li>
                    <li><strong>持倉時間：</strong>根據動量強度決定，通常2-6小時</li>
                    <li><strong>趨勢反轉：</strong>出現反轉信號立即出場</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>順勢而為：</strong>只做順勢交易，不逆勢操作</li>
                    <li><strong>動量強度：</strong>優先選擇動量最強的標的</li>
                    <li><strong>及時跟進：</strong>動量策略講求時效，要快速跟進</li>
                    <li><strong>分批減倉：</strong>動量衰減時分批減倉，保護利潤</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>避免在動量衰減時追高</li>
                    <li>注意大盤動量與個股的配合</li>
                    <li>控制追漲殺跌的頻率</li>
                    <li>設定合理的盈虧比例</li>
                </ul>
            """,

            "5分鐘量價策略": """
                <h2>📊 5分鐘量價策略 - 量價關係分析專家</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>分析成交量與價格的配合關係，發現交易機會</strong></p>
                <ul>
                    <li><strong>量價配合：</strong>價格變化必須有成交量支撐</li>
                    <li><strong>量價背離：</strong>量價不配合時的反轉機會</li>
                    <li><strong>爆量分析：</strong>異常成交量的市場含義</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>突破確認：</strong>重要位置突破時的量能確認</li>
                    <li><strong>趨勢轉折：</strong>量價背離預示的趨勢變化</li>
                    <li><strong>爆量出現：</strong>異常放量時的交易機會</li>
                    <li><strong>縮量整理：</strong>縮量後的變盤方向判斷</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 量價配合上漲：</h4>
                <ul>
                    <li>價格上漲1%以上 + 成交量爆增2倍</li>
                    <li>顯示買盤積極，上漲動能強勁</li>
                    <li>適合追漲或加碼操作</li>
                </ul>

                <h4>📉 量價配合下跌：</h4>
                <ul>
                    <li>價格下跌1%以上 + 成交量爆增2倍</li>
                    <li>顯示賣壓沉重，下跌動能強勁</li>
                    <li>適合減倉或做空操作</li>
                </ul>

                <h4>🔄 量價背離：</h4>
                <ul>
                    <li>價格變化0.5%以上 + 成交量萎縮30%</li>
                    <li>顯示動能不足，可能出現反轉</li>
                    <li>適合反向操作或觀望</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>量能確認：</strong>所有操作都要有成交量確認</li>
                    <li><strong>假突破防範：</strong>無量突破要謹慎對待</li>
                    <li><strong>背離風險：</strong>量價背離時要準備反向操作</li>
                    <li><strong>持倉管理：</strong>根據量能變化調整持倉</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>量能比較：</strong>與近期平均量能比較，判斷異常</li>
                    <li><strong>分時觀察：</strong>觀察分時圖的量價配合情況</li>
                    <li><strong>主力動向：</strong>大量成交可能反映主力動向</li>
                    <li><strong>市場情緒：</strong>量能變化反映市場情緒變化</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>避免在無量市場中頻繁交易</li>
                    <li>注意除權息等特殊情況對量能的影響</li>
                    <li>結合其他技術指標綜合判斷</li>
                    <li>控制單次交易規模</li>
                </ul>
            """,

            "5分鐘剝頭皮": """
                <h2>⚡ 5分鐘剝頭皮 - 超短線交易專家</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>超短線快進快出，賺取微小價差的交易策略</strong></p>
                <ul>
                    <li><strong>快進快出：</strong>持倉時間通常1-3分鐘</li>
                    <li><strong>微利累積：</strong>每次賺取0.3-0.8%的小利潤</li>
                    <li><strong>低風險：</strong>嚴格控制單次虧損</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>低波動環境：</strong>市場波動率低於2%</li>
                    <li><strong>流動性充足：</strong>成交量活躍的熱門股</li>
                    <li><strong>技術位附近：</strong>重要支撐阻力位附近</li>
                    <li><strong>盤中整理：</strong>橫盤整理時的小幅波動</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 剝頭皮買入：</h4>
                <ul>
                    <li>短期上漲0.2%以上</li>
                    <li>市場波動率低於1%</li>
                    <li>價格接近支撐位反彈</li>
                    <li>目標獲利0.5%</li>
                </ul>

                <h4>📉 剝頭皮賣出：</h4>
                <ul>
                    <li>短期下跌0.2%以上</li>
                    <li>市場波動率低於1%</li>
                    <li>價格接近阻力位回落</li>
                    <li>目標獲利0.5%</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>嚴格止損：</strong>虧損達0.3%立即止損</li>
                    <li><strong>快速止盈：</strong>獲利0.5%立即止盈</li>
                    <li><strong>時間限制：</strong>超過3分鐘未獲利就出場</li>
                    <li><strong>波動控制：</strong>波動過大時停止操作</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>選股標準：</strong>選擇流動性好、價格穩定的股票</li>
                    <li><strong>時機把握：</strong>在技術位附近尋找機會</li>
                    <li><strong>快速決策：</strong>機會稍縱即逝，要快速行動</li>
                    <li><strong>累積效應：</strong>通過多次小額獲利累積收益</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>需要極高的專注度和反應速度</li>
                    <li>交易成本會影響獲利，要考慮手續費</li>
                    <li>不適合新手，需要豐富經驗</li>
                    <li>避免在重大消息前後操作</li>
                    <li>控制每日交易次數，避免過度交易</li>
                </ul>
            """,

            "5分鐘趨勢跟隨": """
                <h2>📈 5分鐘趨勢跟隨 - 短期趨勢捕捉專家</h2>

                <h3>🎯 策略核心理念</h3>
                <p><strong>跟隨短期趨勢方向，在趨勢中獲利</strong></p>
                <ul>
                    <li><strong>趨勢識別：</strong>通過均線排列判斷趨勢</li>
                    <li><strong>順勢操作：</strong>只做順勢交易，不逆勢</li>
                    <li><strong>趨勢強度：</strong>評估趨勢的持續性</li>
                </ul>

                <h3>📊 最佳使用時機</h3>
                <ul>
                    <li><strong>趨勢明確：</strong>均線呈現多頭或空頭排列</li>
                    <li><strong>趨勢初期：</strong>新趨勢剛開始形成時</li>
                    <li><strong>回調買入：</strong>上升趨勢中的短期回調</li>
                    <li><strong>反彈賣出：</strong>下降趨勢中的短期反彈</li>
                </ul>

                <h3>🔧 操作要點</h3>
                <h4>📈 上升趨勢跟隨：</h4>
                <ul>
                    <li>MA5 > MA10 > MA20（多頭排列）</li>
                    <li>價格站上MA5</li>
                    <li>趨勢強度超過1%</li>
                    <li>RSI在50-75之間</li>
                </ul>

                <h4>📉 下降趨勢跟隨：</h4>
                <ul>
                    <li>MA5 < MA10 < MA20（空頭排列）</li>
                    <li>價格跌破MA5</li>
                    <li>趨勢強度超過1%</li>
                    <li>RSI在25-50之間</li>
                </ul>

                <h3>🛡️ 風險控制</h3>
                <ul>
                    <li><strong>趨勢止損：</strong>以MA20作為止損位</li>
                    <li><strong>趨勢反轉：</strong>均線排列改變時出場</li>
                    <li><strong>強度監控：</strong>趨勢強度減弱時減倉</li>
                    <li><strong>時間控制：</strong>根據趨勢持續時間調整策略</li>
                </ul>

                <h3>💡 實戰技巧</h3>
                <ul>
                    <li><strong>趨勢確認：</strong>等待趨勢確立後再進場</li>
                    <li><strong>分批建倉：</strong>在趨勢發展過程中分批加倉</li>
                    <li><strong>移動止盈：</strong>隨著趨勢發展調整止盈位</li>
                    <li><strong>大盤配合：</strong>個股趨勢要與大盤趨勢配合</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li>避免在震盪市場中使用</li>
                    <li>注意趨勢的持續性和強度</li>
                    <li>不要在趨勢末期追高殺低</li>
                    <li>結合其他指標確認趨勢品質</li>
                    <li>控制單次交易規模和持倉時間</li>
                </ul>
            """,

            "研發魔人": """
                <h2>💡 研發魔人策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>科技導向：</strong>專注高研發投入的科技公司，把握創新機會。</p>
                <p><strong>短線操作：</strong>每兩週換股，快速捕捉強勢股機會。</p>
                <p><strong>雙重篩選：</strong>結合基本面和技術面，確保選股品質。</p>
                <p><strong>風險控制：</strong>明確的進出場條件和停損停利機制。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>策略執行：</strong>在策略選單中選擇「研發魔人」並執行</li>
                    <li><strong>評分解讀：</strong>重點關注評分80分以上的股票</li>
                    <li><strong>基本面確認：</strong>檢查研發投入和營收成長</li>
                    <li><strong>技術面驗證：</strong>確認10日新高和多頭排列</li>
                    <li><strong>短線操作：</strong>每兩週重新評估持股</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>🔬 研發魔人評分：</strong>綜合評分，80分以上為優質標的</li>
                    <li><strong>📊 研發投入：</strong>技術指標強度（模擬研發費用率）</li>
                    <li><strong>📈 營收成長：</strong>價格趨勢（模擬營收連2月年增）</li>
                    <li><strong>📊 10日新高：</strong>確認短期突破有效性</li>
                    <li><strong>📈 多頭排列：</strong>確認中期趨勢向上</li>
                    <li><strong>📊 3日均量：</strong>大於200張確保流動性</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li><strong>短線特性：</strong>適合短線操作，不宜長期持有</li>
                    <li><strong>換股頻率：</strong>每兩週重新評估，及時調整持股</li>
                    <li><strong>賣出條件：</strong>股價連2日落於月線下即賣出</li>
                    <li><strong>科技股風險：</strong>科技股波動較大，需嚴格風控</li>
                </ul>

                <h3>📈 投資建議</h3>
                <ul>
                    <li><strong>適合對象：</strong>重視科技創新、偏好短線操作的積極型投資者</li>
                    <li><strong>持股期間：</strong>建議短期持有（2週-1個月）</li>
                    <li><strong>風險控制：</strong>10%停損，15%停利</li>
                    <li><strong>持股分散：</strong>最多持有10檔股票</li>
                </ul>
            """,

            "低價股策略": """
                <h2>💡 低價股策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>低價優勢：</strong>低價股具有較大的上漲空間和彈性。</p>
                <p><strong>突破確認：</strong>結合120日新高確認突破有效性。</p>
                <p><strong>風險控制：</strong>嚴格的篩選條件和5%停損機制。</p>
                <p><strong>量化選股：</strong>100分制評分系統，客觀評估投資價值。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>策略執行：</strong>在策略選單中選擇「低價股策略」並執行</li>
                    <li><strong>評分解讀：</strong>重點關注評分80分以上的股票</li>
                    <li><strong>突破確認：</strong>確認近5日內有創120日新高</li>
                    <li><strong>區間分析：</strong>檢查60日價格區間是否收斂</li>
                    <li><strong>成交量驗證：</strong>確認5日均量大於100張</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>💰 低價股評分：</strong>綜合評分，80分以上為優質標的</li>
                    <li><strong>📈 120日新高：</strong>確認突破有效性</li>
                    <li><strong>📊 價格區間：</strong>60日高低價差≤30%為佳</li>
                    <li><strong>💰 低價特徵：</strong>收盤價≤25元</li>
                    <li><strong>📊 5日均量：</strong>大於100張確保流動性</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li><strong>風險控制：</strong>低價股波動較大，務必設定停損</li>
                    <li><strong>資金配置：</strong>每檔標的持有部位上限20%</li>
                    <li><strong>進出時機：</strong>每月底產生訊號，隔月第一個交易日進場</li>
                    <li><strong>排除標的：</strong>已排除金融股，降低系統性風險</li>
                </ul>

                <h3>📈 投資建議</h3>
                <ul>
                    <li><strong>適合對象：</strong>偏好低價股、追求高成長的積極型投資者</li>
                    <li><strong>持股期間：</strong>建議短中期持有（1-3個月）</li>
                    <li><strong>風險控制：</strong>嚴格執行5%停損</li>
                    <li><strong>分散投資：</strong>最多選擇前5低價的標的</li>
                </ul>
            """,

            "小蝦米跟大鯨魚": """
                <h2>💡 小蝦米跟大鯨魚策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>籌碼導向：</strong>跟隨大戶投資方向，提高投資勝率。</p>
                <p><strong>成長篩選：</strong>結合營收成長，確保基本面支撐。</p>
                <p><strong>穩定性高：</strong>大戶持股通常較為穩定，減少短期波動。</p>
                <p><strong>量化分析：</strong>100分制評分系統，客觀評估投資價值。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>策略執行：</strong>在策略選單中選擇「小蝦米跟大鯨魚」並執行</li>
                    <li><strong>評分解讀：</strong>重點關注評分80分以上的股票</li>
                    <li><strong>籌碼確認：</strong>查看大戶持股比例和變化趨勢</li>
                    <li><strong>基本面驗證：</strong>確認營收成長的持續性</li>
                    <li><strong>風險控制：</strong>注意流動性和殖利率水準</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>🐋 大戶持股：</strong>成交量穩定度（模擬大戶集中度）</li>
                    <li><strong>📈 營收成長：</strong>價格動能（模擬營收成長率）</li>
                    <li><strong>💰 殖利率：</strong>價格相對位置（模擬殖利率吸引力）</li>
                    <li><strong>💧 流動性：</strong>日成交值確保進出便利</li>
                    <li><strong>📊 小蝦米評分：</strong>綜合評分，80分以上為優質標的</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li><strong>數據限制：</strong>目前使用模擬數據，真實效果需驗證</li>
                    <li><strong>大戶行為：</strong>大戶持股變化可能滯後於股價</li>
                    <li><strong>營收週期：</strong>注意營收公布時間和季節性因素</li>
                    <li><strong>市場環境：</strong>在多頭市場中效果更佳</li>
                </ul>

                <h3>📈 投資建議</h3>
                <ul>
                    <li><strong>適合對象：</strong>重視籌碼面分析的投資者</li>
                    <li><strong>持股期間：</strong>建議中長期持有（3-6個月）</li>
                    <li><strong>風險控制：</strong>設定適當的停損點（建議10-15%）</li>
                    <li><strong>分散投資：</strong>不要過度集中單一標的</li>
                </ul>
            """,

            "台股總體經濟ETF": """
                <h2>💡 台股總體經濟ETF策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>總經導向：</strong>基於總體經濟指標進行投資決策，避開系統性風險。</p>
                <p><strong>ETF配置：</strong>透過ETF分散投資，降低個股風險。</p>
                <p><strong>避險機制：</strong>當總經指標惡化時，自動切換至反向ETF避險。</p>
                <p><strong>量化決策：</strong>10分制評分系統，客觀判斷市場環境。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>策略執行：</strong>在策略選單中選擇「台股總體經濟ETF」並執行</li>
                    <li><strong>評分解讀：</strong>查看總經評分，了解當前市場環境</li>
                    <li><strong>配置建議：</strong>根據評分結果進行ETF配置</li>
                    <li><strong>定期檢視：</strong>建議每週檢視一次總經指標變化</li>
                    <li><strong>動態調整：</strong>根據評分變化調整ETF持股比例</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>📊 積極配置：</strong>總經評分≥6分，台灣50+台灣中型100各50%</li>
                    <li><strong>⚖️ 中性配置：</strong>總經評分4-6分，台灣50+台灣中型100各50%</li>
                    <li><strong>🛡️ 避險配置：</strong>總經評分<4分，台灣50反100%</li>
                    <li><strong>風險等級：</strong>根據多項指標綜合評估風險水準</li>
                    <li><strong>趨勢強度：</strong>顯示當前市場趨勢的強弱程度</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li><strong>總經環境：</strong>策略效果與總體經濟環境密切相關</li>
                    <li><strong>ETF特性：</strong>了解各ETF的特性和風險</li>
                    <li><strong>定期檢視：</strong>總經指標變化較慢，建議週檢視即可</li>
                    <li><strong>資金配置：</strong>建議將此策略作為核心配置的一部分</li>
                </ul>

                <h3>📈 ETF介紹</h3>
                <ul>
                    <li><strong>台灣50 (0050)：</strong>追蹤台灣50指數，大型股代表</li>
                    <li><strong>台灣中型100 (0051)：</strong>追蹤中型100指數，中型股代表</li>
                    <li><strong>台灣50反 (00632R)：</strong>台灣50反向ETF，避險工具</li>
                    <li><strong>配置比例：</strong>根據總經評分動態調整配置比例</li>
                </ul>

                <h3>📊 適用情境</h3>
                <ul>
                    <li><strong>資產配置：</strong>作為核心資產配置的重要參考</li>
                    <li><strong>風險管理：</strong>在市場不確定時期的避險工具</li>
                    <li><strong>長期投資：</strong>適合長期投資者的配置策略</li>
                    <li><strong>定期定額：</strong>可結合定期定額投資策略</li>
                </ul>
            """,

            "超級績效台股版": """
                <h2>💡 超級績效台股版策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>成長股捕捉：</strong>專注於尋找具有強勁成長潛力的股票，結合技術面和基本面分析。</p>
                <p><strong>VCP模式：</strong>利用價量收縮模式，在股票即將爆發前進場。</p>
                <p><strong>風險控制：</strong>嚴格的止損機制，當週大跌超過10%則出場。</p>
                <p><strong>動態調整：</strong>根據營收表現動態調整持股，保持投資組合的優質性。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>策略執行：</strong>在策略選單中選擇「超級績效台股版」並執行</li>
                    <li><strong>結果篩選：</strong>重點關注評分80分以上的股票</li>
                    <li><strong>技術確認：</strong>在K線圖上確認VCP模式和趨勢</li>
                    <li><strong>基本面驗證：</strong>檢查公司營收成長情況</li>
                    <li><strong>風險管理：</strong>設定止損點，控制單一持股比例</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>🚀 超級績效信號：</strong>符合策略條件的股票（評分≥80分）</li>
                    <li><strong>MA250位置：</strong>股價相對於250日均線的位置</li>
                    <li><strong>VCP模式：</strong>是否符合價量收縮突破模式</li>
                    <li><strong>通道狀態：</strong>價格通道的狹窄程度</li>
                    <li><strong>日成交值：</strong>確保有足夠的流動性</li>
                </ul>

                <h3>⚠️ 注意事項</h3>
                <ul>
                    <li><strong>市場環境：</strong>在多頭市場中效果更佳</li>
                    <li><strong>持股數量：</strong>建議控制在5檔以內，便於管理</li>
                    <li><strong>定期檢視：</strong>每週檢視持股表現，及時調整</li>
                    <li><strong>止損紀律：</strong>嚴格執行10%止損規則</li>
                </ul>

                <h3>📈 預期表現</h3>
                <ul>
                    <li><strong>年化報酬率：</strong>約40%（回測結果）</li>
                    <li><strong>夏普比率：</strong>1.7（風險調整後報酬）</li>
                    <li><strong>最大回撤：</strong>控制在合理範圍內</li>
                    <li><strong>勝率：</strong>中等偏高，重點在於大賺小賠</li>
                </ul>
            """,

            "監獄兔": """
                <h2>💡 監獄兔策略 - 實戰使用指南</h2>

                <h3>🎯 策略核心優勢</h3>
                <p><strong>市場機會捕捉：</strong>利用市場對處置股的過度恐懼，在非理性拋售中尋找價值回歸機會。</p>
                <p><strong>心理優勢：</strong>反向思維，避免跟風操作，利用群眾心理的弱點。</p>
                <p><strong>風險控制：</strong>明確的進出場時間點，嚴格的持倉限制。</p>

                <h3>📋 使用步驟</h3>
                <ol>
                    <li><strong>選擇策略：</strong>在策略下拉選單中選擇"🚀 進階策略" → "監獄兔"</li>
                    <li><strong>設定日期：</strong>選擇要分析的日期範圍</li>
                    <li><strong>執行篩選：</strong>點擊"執行選股"開始分析</li>
                    <li><strong>查看結果：</strong>檢視評分結果和詳細技術指標</li>
                    <li><strong>技術分析：</strong>在K線圖上查看🐰監獄兔進出場信號</li>
                </ol>

                <h3>📊 結果解讀</h3>
                <ul>
                    <li><strong>🐰 監獄兔信號：</strong>符合策略條件的股票（評分≥70分）</li>
                    <li><strong>評分系統：</strong>0-100分，分數越高表示處置股特徵越明顯</li>
                    <li><strong>年化波動率：</strong>顯示股票的波動程度，高波動率有利於策略</li>
                    <li><strong>成交量比率：</strong>當日成交量與平均成交量的比率</li>
                    <li><strong>價格位置：</strong>當前價格與20日均線的相對位置</li>
                    <li><strong>RSI指標：</strong>相對強弱指標，低於30表示超賣</li>
                    <li><strong>近期變化：</strong>近5日的價格變化百分比</li>
                </ul>

                <h3>⚠️ 重要風險提醒</h3>
                <h4>主要風險</h4>
                <ul>
                    <li><strong>處置原因風險：</strong>需要了解股票被處置的具體原因</li>
                    <li><strong>流動性風險：</strong>處置股可能面臨流動性不足</li>
                    <li><strong>基本面風險：</strong>公司可能存在實質性問題</li>
                    <li><strong>時間風險：</strong>處置期間可能延長</li>
                </ul>

                <h4>風險控制措施</h4>
                <ul>
                    <li><strong>嚴格篩選：</strong>使用多重技術指標確認</li>
                    <li><strong>分散投資：</strong>不要集中投資單一處置股</li>
                    <li><strong>及時止損：</strong>設定明確的止損點</li>
                    <li><strong>持續監控：</strong>密切關注處置狀態變化</li>
                </ul>

                <h3>💰 建議交易參數</h3>
                <ul>
                    <li><strong>持倉限制：</strong>單一標的不超過總資金的20%</li>
                    <li><strong>交易時機：</strong>以開盤價進出，避免盤中追高殺低</li>
                    <li><strong>手續費考量：</strong>已考慮1.425‰的手續費成本</li>
                    <li><strong>進出場紀律：</strong>嚴格按照處置開始和結束時間執行</li>
                </ul>

                <h3>📈 最佳實踐建議</h3>
                <ul>
                    <li><strong>深入研究：</strong>了解處置原因，避免基本面有重大問題的股票</li>
                    <li><strong>技術確認：</strong>結合其他技術指標進行二次確認</li>
                    <li><strong>資金管理：</strong>控制總體風險敞口，不要過度集中</li>
                    <li><strong>心理準備：</strong>處置股波動較大，需要較強的心理承受能力</li>
                    <li><strong>持續學習：</strong>關注處置股相關法規變化</li>
                </ul>

                <h3>🔗 參考資料</h3>
                <ul>
                    <li><strong>進階資訊：</strong><a href="https://www.finlab.tw/alerting_stock/" style="color: #0078d4;">https://www.finlab.tw/alerting_stock/</a></li>
                    <li><strong>處置股規定：</strong>請參考證交所相關公告</li>
                    <li><strong>風險管理：</strong>建議搭配其他技術分析工具使用</li>
                </ul>

                <h3>⚖️ 免責聲明</h3>
                <p style="color: #ff6b6b; font-weight: bold;">
                本策略僅供參考，不構成投資建議。投資有風險，請根據自身風險承受能力謹慎投資。
                使用本策略前請充分了解處置股的相關風險。
                </p>
            """
        }

        return usage_guides.get(self.strategy_name, f"""
            <h2>💡 {self.strategy_name} - 使用建議</h2>
            <p>這是一個自定義策略，建議您：</p>
            <ul>
                <li>仔細研究策略的具體條件</li>
                <li>在小資金上先測試策略效果</li>
                <li>根據市場環境調整使用頻率</li>
                <li>設定合理的停利停損點</li>
                <li>定期檢討策略績效</li>
            </ul>
        """)

    # ⚡ 5分鐘策略實現函數 - 整合版本
    def check_five_min_breakout_momentum_strategy(self, df):
        """
        5分鐘突破動量策略 (整合版)
        結合突破、動量、趨勢跟隨三種策略
        適用場景：盤整突破、趨勢啟動初期
        """
        try:
            if len(df) < 30:
                return False, "數據不足（需要至少30天數據）"

            latest = df.iloc[-1]
            prev = df.iloc[-2]

            score = 0
            reasons = []

            # === 1. 突破檢查 (40分) ===
            high_20 = df['High'].rolling(20).max().iloc[-2]
            low_20 = df['Low'].rolling(20).min().iloc[-2]

            if latest['Close'] > high_20:
                score += 40
                reasons.append(f"向上突破20期高點{high_20:.2f}")
            elif latest['Close'] < low_20:
                score += 40
                reasons.append(f"向下突破20期低點{low_20:.2f}")

            # === 2. 動量檢查 (30分) ===
            # MACD金叉
            ema12 = df['Close'].ewm(span=12).mean()
            ema26 = df['Close'].ewm(span=26).mean()
            macd = ema12 - ema26
            macd_signal = macd.ewm(span=9).mean()

            macd_golden_cross = (macd.iloc[-1] > macd_signal.iloc[-1] and
                               macd.iloc[-2] <= macd_signal.iloc[-2])

            if macd_golden_cross:
                score += 30
                reasons.append("MACD金叉確認")

            # === 3. 趨勢跟隨檢查 (20分) ===
            ma5 = df['Close'].rolling(5).mean().iloc[-1]
            ma10 = df['Close'].rolling(10).mean().iloc[-1]
            ma20 = df['Close'].rolling(20).mean().iloc[-1]

            uptrend = ma5 > ma10 > ma20
            downtrend = ma5 < ma10 < ma20

            if uptrend or downtrend:
                score += 20
                trend_type = "上升" if uptrend else "下降"
                reasons.append(f"{trend_type}趨勢排列")

            # === 4. 成交量確認 (10分) ===
            volume_ma = df['Volume'].rolling(10).mean().iloc[-1]
            volume_ratio = latest['Volume'] / volume_ma if volume_ma > 0 else 0

            if volume_ratio > 1.5:
                score += 10
                reasons.append(f"成交量放大{volume_ratio:.1f}倍")

            # === 判斷結果 ===
            if score >= 80:
                return True, f"⚡ 5分鐘突破動量策略符合！評分: {score}/100 - " + ", ".join(reasons)
            elif score >= 60:
                return False, f"5分鐘突破動量策略部分符合，評分: {score}/100 - " + ", ".join(reasons)
            else:
                return False, f"5分鐘突破動量策略不符合，評分: {score}/100 - 缺乏突破動量"

        except Exception as e:
            logging.error(f"5分鐘突破動量策略檢查失敗: {e}")
            return False, f"策略檢查錯誤: {str(e)}"

    def check_five_min_breakout_strategy(self, df):
        """5分鐘突破策略"""
        try:
            # 需要5分鐘數據，這裡使用日K線數據模擬
            if len(df) < 20:
                return False, "數據不足"

            latest = df.iloc[-1]

            # 計算20期高低點
            high_20 = df['High'].rolling(20).max().iloc[-2]
            low_20 = df['Low'].rolling(20).min().iloc[-2]

            # 成交量條件
            volume_ma = df['Volume'].rolling(10).mean().iloc[-1]
            volume_ratio = latest['Volume'] / volume_ma if volume_ma > 0 else 0

            # 向上突破條件
            if (latest['Close'] > high_20 and
                volume_ratio > 1.5):
                return True, f"向上突破20期高點 {high_20:.2f}，成交量放大{volume_ratio:.1f}倍"

            # 向下突破條件
            elif (latest['Close'] < low_20 and
                  volume_ratio > 1.5):
                return True, f"向下突破20期低點 {low_20:.2f}，成交量放大{volume_ratio:.1f}倍"

            return False, f"未突破關鍵位置，當前價格{latest['Close']:.2f}在{low_20:.2f}-{high_20:.2f}區間"

        except Exception as e:
            return False, f"5分鐘突破策略檢查錯誤: {str(e)}"

    def check_five_min_mean_reversion_strategy(self, df):
        """
        5分鐘回歸修正策略 (整合版)
        結合均值回歸和剝頭皮策略
        適用場景：超買超賣修正、盤整震盪市
        """
        try:
            if len(df) < 20:
                return False, "數據不足（需要至少20天數據）"

            latest = df.iloc[-1]

            score = 0
            reasons = []

            # === 1. 均值回歸檢查 (50分) ===
            ma20 = df['Close'].rolling(20).mean().iloc[-1]
            price_deviation = (latest['Close'] - ma20) / ma20

            # 計算RSI（簡化版）
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0).rolling(14).mean()
            loss = -delta.where(delta < 0, 0).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1]

            # 超買超賣條件
            if price_deviation < -0.03 and current_rsi < 30:
                score += 50
                reasons.append(f"超賣回歸(RSI:{current_rsi:.1f})")
            elif price_deviation > 0.03 and current_rsi > 70:
                score += 50
                reasons.append(f"超買回歸(RSI:{current_rsi:.1f})")
            elif 30 <= current_rsi <= 70 and abs(price_deviation) > 0.02:
                score += 25
                reasons.append(f"偏離修正(偏離{price_deviation*100:.1f}%)")

            # === 2. 剝頭皮機會檢查 (30分) ===
            volatility = df['Close'].rolling(5).std().iloc[-1] / latest['Close']
            price_change = (latest['Close'] / df['Close'].iloc[-2] - 1) * 100

            if volatility < 0.01 and abs(price_change) > 0.2:
                score += 30
                direction = "上漲" if price_change > 0 else "下跌"
                reasons.append(f"剝頭皮機會({direction}{abs(price_change):.2f}%)")
            elif volatility < 0.02:
                score += 15
                reasons.append(f"低波動環境({volatility*100:.1f}%)")

            # === 3. 成交量確認 (20分) ===
            volume_ma = df['Volume'].rolling(10).mean().iloc[-1]
            volume_ratio = latest['Volume'] / volume_ma if volume_ma > 0 else 0

            if 0.8 <= volume_ratio <= 1.2:  # 成交量正常
                score += 20
                reasons.append(f"成交量正常({volume_ratio:.1f}倍)")
            elif volume_ratio > 1.5:  # 異常放量
                score += 10
                reasons.append(f"異常放量({volume_ratio:.1f}倍)")

            # === 判斷結果 ===
            if score >= 80:
                return True, f"⚡ 5分鐘回歸修正策略符合！評分: {score}/100 - " + ", ".join(reasons)
            elif score >= 60:
                return False, f"5分鐘回歸修正策略部分符合，評分: {score}/100 - " + ", ".join(reasons)
            else:
                return False, f"5分鐘回歸修正策略不符合，評分: {score}/100 - 無修正機會"

        except Exception as e:
            logging.error(f"5分鐘回歸修正策略檢查失敗: {e}")
            return False, f"策略檢查錯誤: {str(e)}"

    def check_five_min_momentum_strategy(self, df):
        """5分鐘動量策略"""
        try:
            if len(df) < 15:
                return False, "數據不足"

            latest = df.iloc[-1]
            prev = df.iloc[-2]

            # 計算短期動量
            price_change_3 = (latest['Close'] / df['Close'].iloc[-4] - 1) * 100

            # 計算MACD（簡化版）
            ema12 = df['Close'].ewm(span=12).mean()
            ema26 = df['Close'].ewm(span=26).mean()
            macd = ema12 - ema26
            macd_signal = macd.ewm(span=9).mean()

            # MACD金叉
            macd_golden_cross = (latest['Close'] > prev['Close'] and
                               macd.iloc[-1] > macd_signal.iloc[-1] and
                               macd.iloc[-2] <= macd_signal.iloc[-2])

            # 成交量確認
            volume_ma = df['Volume'].rolling(10).mean().iloc[-1]
            volume_ratio = latest['Volume'] / volume_ma if volume_ma > 0 else 0

            if (macd_golden_cross and price_change_3 > 1 and volume_ratio > 1.2):
                return True, f"動量突破，3日漲幅{price_change_3:.1f}%，MACD金叉，量增{volume_ratio:.1f}倍"

            return False, f"動量不足，3日漲幅{price_change_3:.1f}%，量比{volume_ratio:.1f}"

        except Exception as e:
            return False, f"5分鐘動量策略檢查錯誤: {str(e)}"

    def check_five_min_volume_price_strategy(self, df):
        """5分鐘量價策略"""
        try:
            if len(df) < 10:
                return False, "數據不足"

            latest = df.iloc[-1]
            prev = df.iloc[-2]

            # 價格變化
            price_change = (latest['Close'] / prev['Close'] - 1) * 100

            # 成交量變化
            volume_ma = df['Volume'].rolling(10).mean().iloc[-1]
            volume_ratio = latest['Volume'] / volume_ma if volume_ma > 0 else 0

            # 量價配合上漲
            if (price_change > 1 and volume_ratio > 2):
                return True, f"量價配合上漲，漲幅{price_change:.1f}%，爆量{volume_ratio:.1f}倍"

            # 量價配合下跌
            elif (price_change < -1 and volume_ratio > 2):
                return True, f"量價配合下跌，跌幅{price_change:.1f}%，爆量{volume_ratio:.1f}倍"

            # 量價背離
            elif (abs(price_change) > 0.5 and volume_ratio < 0.7):
                return True, f"量價背離，價格變化{price_change:.1f}%，量縮{(1-volume_ratio)*100:.0f}%"

            return False, f"量價關係正常，價格變化{price_change:.1f}%，量比{volume_ratio:.1f}"

        except Exception as e:
            return False, f"5分鐘量價策略檢查錯誤: {str(e)}"

    def check_five_min_scalping_strategy(self, df):
        """5分鐘剝頭皮策略"""
        try:
            if len(df) < 5:
                return False, "數據不足"

            latest = df.iloc[-1]

            # 計算短期波動率
            volatility = df['Close'].rolling(5).std().iloc[-1] / latest['Close']

            # 只在低波動時適合剝頭皮
            if volatility > 0.02:
                return False, f"波動過大({volatility*100:.1f}%)，不適合剝頭皮"

            # 短期價格變化
            price_change = (latest['Close'] / df['Close'].iloc[-2] - 1) * 100

            # 剝頭皮機會
            if (abs(price_change) > 0.2 and volatility < 0.01):
                direction = "上漲" if price_change > 0 else "下跌"
                return True, f"剝頭皮機會，短期{direction}{abs(price_change):.2f}%，低波動({volatility*100:.1f}%)"

            return False, f"無剝頭皮機會，價格變化{price_change:.2f}%，波動率{volatility*100:.1f}%"

        except Exception as e:
            return False, f"5分鐘剝頭皮策略檢查錯誤: {str(e)}"

    def check_five_min_trend_following_strategy(self, df):
        """5分鐘趨勢跟隨策略"""
        try:
            if len(df) < 30:
                return False, "數據不足"

            latest = df.iloc[-1]

            # 計算多條均線
            ma5 = df['Close'].rolling(5).mean().iloc[-1]
            ma10 = df['Close'].rolling(10).mean().iloc[-1]
            ma20 = df['Close'].rolling(20).mean().iloc[-1]

            # 判斷趨勢方向
            uptrend = ma5 > ma10 > ma20
            downtrend = ma5 < ma10 < ma20

            # 趨勢強度
            trend_strength = abs(ma5 - ma20) / ma20

            # 價格位置
            price_above_ma5 = latest['Close'] > ma5
            price_below_ma5 = latest['Close'] < ma5

            # 上升趨勢跟隨
            if (uptrend and price_above_ma5 and trend_strength > 0.01):
                return True, f"上升趨勢跟隨，均線多頭排列，趨勢強度{trend_strength*100:.1f}%"

            # 下降趨勢跟隨
            elif (downtrend and price_below_ma5 and trend_strength > 0.01):
                return True, f"下降趨勢跟隨，均線空頭排列，趨勢強度{trend_strength*100:.1f}%"

            return False, f"趨勢不明確，MA5:{ma5:.2f} MA10:{ma10:.2f} MA20:{ma20:.2f}"

        except Exception as e:
            return False, f"5分鐘趨勢跟隨策略檢查錯誤: {str(e)}"

def main():
    """主程式入口"""
    try:
        print("🚀 啟動股票篩選系統...")
        print("✅ 開盤前監控")
        print("✅ 智能策略")

        app = QApplication(sys.argv)
        app.setStyle('Fusion')

        # 設置應用程式圖標和名稱
        app.setApplicationName("股票篩選系統")
        app.setApplicationVersion("v2.1")

        # 創建主視窗
        win = StockScreenerGUI()
        win.showMaximized()  # 自動最大化視窗

        print("✅ 系統啟動成功")

        # 啟動事件循環
        sys.exit(app.exec())

    except Exception as e:
        logging.error(f"❌ 程式啟動失敗: {e}")
        print(f"❌ 程式啟動失敗: {e}")
        traceback.print_exc()

