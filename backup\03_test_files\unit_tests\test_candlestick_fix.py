#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 K線圖修復
驗證 PyQt6 兼容性和圖表顯示
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_stock_data():
    """創建測試用的股票數據"""
    np.random.seed(42)
    
    # 創建60天的股票數據
    dates = pd.date_range(end=datetime.now(), periods=60, freq='D')
    
    # 生成價格數據
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(60):
        if i == 0:
            open_price = base_price
        else:
            # 隨機波動
            change = np.random.normal(0, 0.02)
            open_price = max(prices[-1]['Close'] * (1 + change), 1)
        
        # 生成當日OHLC
        high_price = open_price * (1 + abs(np.random.normal(0, 0.015)))
        low_price = open_price * (1 - abs(np.random.normal(0, 0.015)))
        close_price = open_price + np.random.normal(0, open_price * 0.01)
        close_price = max(min(close_price, high_price), low_price)
        
        # 確保High是最高，Low是最低
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        prices.append({
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': close_price
        })
        
        # 生成成交量
        volume = np.random.randint(100000, 1000000)
        volumes.append(volume)
    
    # 創建DataFrame
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p['Open'] for p in prices],
        'High': [p['High'] for p in prices],
        'Low': [p['Low'] for p in prices],
        'Close': [p['Close'] for p in prices],
        'Volume': volumes
    })
    
    return df

def test_candlestick_import():
    """測試 CandlestickItem 導入"""
    print("🚀 測試 CandlestickItem 導入...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 測試模組導入
        from charts.candlestick import CandlestickItem
        
        print("✅ CandlestickItem 模組導入成功")
        
        # 檢查類別屬性
        print(f"  類別名稱: {CandlestickItem.__name__}")
        print(f"  模組路徑: {CandlestickItem.__module__}")
        
        # 檢查關鍵方法
        required_methods = ['__init__', 'generate_picture', 'paint', 'boundingRect']
        for method in required_methods:
            if hasattr(CandlestickItem, method):
                print(f"  ✅ {method} 方法存在")
            else:
                print(f"  ❌ {method} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ CandlestickItem 導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_candlestick_creation():
    """測試 CandlestickItem 創建"""
    print("\n🚀 測試 CandlestickItem 創建...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入必要模組
        from PyQt6.QtWidgets import QApplication
        from charts.candlestick import CandlestickItem
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 創建測試數據
        test_df = create_test_stock_data()
        
        print("✅ 測試數據創建成功")
        print(f"  數據長度: {len(test_df)} 天")
        print(f"  價格範圍: {test_df['Close'].min():.2f} - {test_df['Close'].max():.2f}")
        
        # 創建 CandlestickItem
        candlestick = CandlestickItem(test_df)
        
        print("✅ CandlestickItem 創建成功")
        
        # 檢查圖片是否生成
        if hasattr(candlestick, 'picture') and candlestick.picture:
            print("✅ K線圖片生成成功")
            
            # 檢查邊界矩形
            bounding_rect = candlestick.boundingRect()
            print(f"  邊界矩形: {bounding_rect}")
            
            if not bounding_rect.isEmpty():
                print("✅ 邊界矩形有效")
            else:
                print("❌ 邊界矩形為空")
                return False
        else:
            print("❌ K線圖片生成失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CandlestickItem 創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_program_integration():
    """測試主程式整合"""
    print("\n🚀 測試主程式整合...")
    
    try:
        # 設置環境變量
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        # 導入必要模組
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 導入主程式
        import O3mh_gui_v21_optimized
        
        print("✅ 主程式導入成功")
        
        # 檢查 CandlestickItem 是否可用
        if hasattr(O3mh_gui_v21_optimized, 'CandlestickItem'):
            print("✅ 主程式中 CandlestickItem 可用")
            
            # 測試創建
            test_df = create_test_stock_data()
            candlestick = O3mh_gui_v21_optimized.CandlestickItem(test_df)
            print("✅ 主程式中 CandlestickItem 創建成功")
        else:
            print("❌ 主程式中 CandlestickItem 不可用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主程式整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pyqt_compatibility():
    """測試 PyQt6 兼容性"""
    print("\n🚀 測試 PyQt6 兼容性...")
    
    try:
        # 測試 PyQt6 導入
        from PyQt6.QtCore import QRectF, QPointF, Qt
        from PyQt6.QtGui import QPainter, QPicture, QPen, QBrush
        from PyQt6.QtWidgets import QApplication
        
        print("✅ PyQt6 核心模組導入成功")
        
        # 測試顏色常數
        try:
            black_color = Qt.GlobalColor.black
            red_color = Qt.GlobalColor.red
            green_color = Qt.GlobalColor.green
            print("✅ PyQt6 顏色常數可用")
        except AttributeError:
            # 嘗試舊版本格式
            black_color = Qt.black
            red_color = Qt.red
            green_color = Qt.green
            print("✅ PyQt5 兼容顏色常數可用")
        
        # 測試 pyqtgraph
        import pyqtgraph as pg
        
        try:
            pen = pg.mkPen('k')
            brush = pg.mkBrush('r')
            print("✅ pyqtgraph 繪圖工具可用")
        except Exception as e:
            print(f"⚠️ pyqtgraph 繪圖工具有問題: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ PyQt6 兼容性測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("🔧 K線圖修復測試")
    print("=" * 60)
    
    success1 = test_pyqt_compatibility()
    success2 = test_candlestick_import()
    success3 = test_candlestick_creation()
    success4 = test_main_program_integration()
    
    overall_success = success1 and success2 and success3 and success4
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 K線圖修復測試通過！")
        print("✅ PyQt6 兼容性正常")
        print("✅ CandlestickItem 導入成功")
        print("✅ K線圖創建正常")
        print("✅ 主程式整合成功")
        print("✅ K線圖應該能正常顯示")
    else:
        print("❌ K線圖修復測試失敗")
        print("需要進一步檢查問題")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
