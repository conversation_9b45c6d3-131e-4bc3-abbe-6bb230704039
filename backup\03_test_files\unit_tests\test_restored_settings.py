#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試恢復後的設定
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_restored_performance():
    """測試恢復後的性能"""
    print("🔄 測試恢復後的設定")
    print("=" * 50)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 檢查設定
        print(f"📊 請求間隔: {fetcher.min_request_interval}秒")
        print(f"📊 最大重試次數: 2次")
        
        # 測試美股指數（快速測試）
        print("\n📈 快速測試美股指數...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({us_time:.1f}秒):")
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
        
        # 測試外匯匯率（快速測試）
        print("\n💱 快速測試外匯匯率...")
        fx_start = time.time()
        fx_data = fetcher.get_enhanced_fx_rates()
        fx_time = time.time() - fx_start
        
        if fx_data:
            print(f"✅ 外匯匯率獲取成功 ({fx_time:.1f}秒):")
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {rate} ({change_pct:+.2f}%) - {status}")
        else:
            print(f"❌ 外匯匯率獲取失敗 ({fx_time:.1f}秒)")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        # 檢查是否有頻率限制檢測
        if hasattr(fetcher, 'rate_limit_detected'):
            print(f"⚠️ 頻率限制檢測: {fetcher.rate_limit_detected}")
        else:
            print("✅ 已移除過度的頻率限制檢測")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_safe_scanner_restored():
    """測試安全掃描器恢復狀態"""
    print("\n🔍 測試安全掃描器恢復狀態...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 快速測試部分數據
        print("📊 快速測試美股指數...")
        start_time = time.time()
        
        us_data = scanner.get_us_indices()
        
        scan_time = time.time() - start_time
        
        if us_data:
            success_count = 0
            for name, data in us_data.items():
                status = data.get('status', '')
                if '真實數據' in status:
                    success_count += 1
                    price = data.get('price', 0)
                    change_pct = data.get('change_pct', 0)
                    print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
            
            print(f"\n📊 成功獲取: {success_count}/{len(us_data)} 項")
            print(f"⏱️ 掃描耗時: {scan_time:.1f}秒")
            
            return success_count > 0
        else:
            print("❌ 美股指數獲取失敗")
            return False
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔄 恢復設定測試")
    print("=" * 60)
    
    tests = [
        ("恢復後性能測試", test_restored_performance),
        ("安全掃描器恢復測試", test_safe_scanner_restored)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 恢復設定總結:")
    
    passed = sum(1 for _, result in results if result)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"• {test_name}: {status}")
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    print("\n🔄 恢復的設定:")
    print("• 請求間隔：恢復到1.0秒")
    print("• 隨機延遲：恢復到0.5-1.5秒")
    print("• 重試次數：簡化為2次")
    print("• 移除過度的頻率限制檢測")
    print("• 保留網頁爬取備援功能")
    print("• 保留顏色顯示功能")
    
    print("\n💡 保留的改善:")
    print("• ✅ 網頁爬取備援：三層數據源保障")
    print("• ✅ 顏色顯示：漲紅跌綠視覺效果")
    print("• ✅ 線程處理：避免UI卡頓")
    print("• ✅ 多代碼備援：提高成功率")
    
    if passed > 0:
        print("\n🎉 設定恢復成功！")
        print("現在系統應該能快速穩定地獲取數據。")
    else:
        print("\n⚠️ 需要進一步檢查系統狀態")

if __name__ == "__main__":
    main()
