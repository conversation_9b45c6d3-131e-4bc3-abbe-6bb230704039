#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查台積電資料
"""

import sqlite3

def check_tsmc_data():
    """檢查台積電資料"""
    
    print("=" * 60)
    print("🔍 檢查台積電資料")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    # 檢查台積電的資料
    print("🔍 檢查台積電 (2330) 的資料:")
    cursor.execute('''
        SELECT stock_id, [Close], Volume, date 
        FROM stock_daily_data 
        WHERE stock_id LIKE "2330%" AND date >= "2022-08-30" 
        ORDER BY stock_id, date
    ''')
    tsmc_data = cursor.fetchall()
    
    if tsmc_data:
        for sid, close, volume, date in tsmc_data:
            print(f"  {sid}: {date} 收盤 {close}, 量 {volume:,}")
    else:
        print("  無台積電資料")
    
    # 檢查是否有其他 2330 開頭的股票
    print(f"\n🔍 檢查所有 2330 開頭的股票:")
    cursor.execute('''
        SELECT DISTINCT stock_id 
        FROM stock_daily_data 
        WHERE stock_id LIKE "2330%" 
        ORDER BY stock_id
    ''')
    stocks_2330 = cursor.fetchall()
    
    for stock_id, in stocks_2330:
        print(f"  {stock_id}")
    
    # 檢查新增日期中有哪些股票
    print(f"\n🔍 檢查新增日期中的股票範例:")
    cursor.execute('''
        SELECT stock_id, [Close], Volume 
        FROM stock_daily_data 
        WHERE date = "2022-09-01" AND [Close] > 0
        ORDER BY Volume DESC
        LIMIT 10
    ''')
    top_stocks = cursor.fetchall()
    
    print("  成交量最大的10檔股票:")
    for stock_id, close, volume in top_stocks:
        print(f"    {stock_id}: 收盤 {close}, 量 {volume:,}")
    
    conn.close()

if __name__ == "__main__":
    check_tsmc_data()
