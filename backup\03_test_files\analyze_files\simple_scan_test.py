#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的掃描測試
"""

import sys
import time
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_scan():
    """測試掃描功能"""
    print("Testing scan function...")
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        print("Running scan...")
        results = monitor.run_full_scan()
        
        print(f"Results type: {type(results)}")
        print(f"Results bool: {bool(results)}")
        print(f"Results is None: {results is None}")
        
        if isinstance(results, dict):
            print(f"Results keys: {list(results.keys())}")
            print(f"Results length: {len(results)}")
            
            # 檢查實際數據
            total_items = 0
            for key, value in results.items():
                if isinstance(value, dict) and value:
                    item_count = len(value)
                    total_items += item_count
                    print(f"  {key}: {item_count} items")
            
            print(f"Total items: {total_items}")
            
            # 測試判斷邏輯
            print(f"if results: {bool(results)}")
            print(f"if results is not None: {results is not None}")
        
        return results
        
    except Exception as e:
        print(f"Scan failed: {e}")
        return None

def test_scan_worker():
    """測試 ScanWorker"""
    print("\nTesting ScanWorker...")
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        monitor = PreMarketMonitor()
        
        # 回調函數
        def success_callback(results):
            print("SUCCESS CALLBACK CALLED!")
            print(f"  Type: {type(results)}")
            if isinstance(results, dict):
                total = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"  Total items: {total}")
        
        def error_callback(error_msg):
            print("ERROR CALLBACK CALLED!")
            print(f"  Error: {error_msg}")
        
        # 創建 ScanWorker
        scan_worker = ScanWorker(
            monitor,
            callback_success=success_callback,
            callback_error=error_callback
        )
        
        print("Starting scan worker...")
        scan_worker.start_scan()
        
        # 等待完成
        print("Waiting for completion...")
        time.sleep(15)
        
        print("ScanWorker test completed")
        
    except Exception as e:
        print(f"ScanWorker test failed: {e}")

def main():
    """主函數"""
    print("Scan Debug Test")
    print("=" * 50)
    
    # 1. 直接測試
    results = test_scan()
    
    # 2. 測試 ScanWorker
    if results is not None:
        test_scan_worker()
    else:
        print("Direct scan failed, skipping ScanWorker test")
    
    print("\nTest completed")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Test failed: {e}")
