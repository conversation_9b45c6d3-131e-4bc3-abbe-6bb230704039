#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的除權息下載功能 - 確保包含完整日期資訊
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_fixed_dividend_download():
    """測試修正後的除權息下載功能"""
    try:
        print("🚀 測試修正後的除權息下載功能...")
        
        # 導入修正後的爬蟲
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        # 創建爬蟲實例
        crawler = EnhancedDividendCrawler()
        
        # 測試2025年資料
        year = 2025
        print(f"\n📊 獲取 {year} 年除權息資料...")
        
        # 獲取資料
        dividend_data = crawler.fetch_all_dividend_data(year)
        
        if dividend_data:
            print(f"✅ 成功獲取 {len(dividend_data)} 筆除權息資料")
            
            # 檢查日期資訊
            with_dates = sum(1 for record in dividend_data if record.get('ex_dividend_date'))
            print(f"📅 包含除權息日期的資料: {with_dates}/{len(dividend_data)} 筆")
            
            # 顯示前10筆資料範例
            print(f"\n📋 資料範例（前10筆）:")
            print("-" * 100)
            print(f"{'股票代碼':<8} {'股票名稱':<12} {'除權息日期':<12} {'現金股利':<8} {'股票股利':<8} {'總股利':<8} {'資料來源':<10}")
            print("-" * 100)
            
            for i, record in enumerate(dividend_data[:10]):
                ex_date = record.get('ex_dividend_date') or 'N/A'
                print(f"{record['stock_code']:<8} {record['stock_name']:<12} {ex_date:<12} "
                      f"{record['cash_dividend']:<8.2f} {record['stock_dividend']:<8.2f} {record['total_dividend']:<8.2f} "
                      f"{record['data_source']:<10}")
            
            # 匯出CSV檔案
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            csv_filename = f"fixed_dividend_data_{year}_{timestamp}.csv"
            
            df = pd.DataFrame(dividend_data)
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            
            print(f"\n💾 資料已匯出至: {csv_filename}")
            
            # 統計分析
            print(f"\n📈 資料統計:")
            print(f"  • 總筆數: {len(dividend_data)}")
            print(f"  • 有日期資料: {with_dates} 筆 ({with_dates/len(dividend_data)*100:.1f}%)")
            
            # 按資料來源統計
            sources = {}
            for record in dividend_data:
                source = record['data_source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"  • 資料來源分布:")
            for source, count in sources.items():
                print(f"    - {source}: {count} 筆")
            
            # 檢查日期格式
            date_formats = {}
            for record in dividend_data:
                date_str = record.get('ex_dividend_date')
                if date_str:
                    if '-' in date_str and len(date_str) == 10:
                        date_formats['YYYY-MM-DD'] = date_formats.get('YYYY-MM-DD', 0) + 1
                    elif '/' in date_str:
                        date_formats['YYYY/MM/DD'] = date_formats.get('YYYY/MM/DD', 0) + 1
                    else:
                        date_formats['其他格式'] = date_formats.get('其他格式', 0) + 1
            
            if date_formats:
                print(f"  • 日期格式分布:")
                for format_type, count in date_formats.items():
                    print(f"    - {format_type}: {count} 筆")
            
            return True
            
        else:
            print("❌ 未獲取到任何除權息資料")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 修正後除權息下載功能測試")
    print("=" * 60)
    
    success = test_fixed_dividend_download()
    
    if success:
        print("\n✅ 測試完成！除權息資料已包含完整日期資訊")
    else:
        print("\n❌ 測試失敗！請檢查錯誤訊息")
