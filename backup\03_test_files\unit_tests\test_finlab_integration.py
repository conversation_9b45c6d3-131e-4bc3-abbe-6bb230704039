#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Finlab整合功能
驗證整合模組是否正常工作
"""

import sys
import os
import datetime
import sqlite3
from pathlib import Path

def test_module_imports():
    """測試模組導入"""
    print("🧪 測試模組導入")
    print("=" * 50)
    
    try:
        # 測試核心模組
        from finlab_integration.core.crawler_adapter import FinlabCrawlerAdapter
        print("✅ FinlabCrawlerAdapter 導入成功")
        
        from finlab_integration.core.data_manager import FinlabDataManager
        print("✅ FinlabDataManager 導入成功")
        
        # 測試GUI模組
        from finlab_integration.gui.crawler_dialog import FinlabCrawlerDialog
        print("✅ FinlabCrawlerDialog 導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
        return False

def test_crawler_adapter():
    """測試爬蟲適配器"""
    print("\n🕷️ 測試爬蟲適配器")
    print("=" * 50)
    
    try:
        from finlab_integration.core.crawler_adapter import FinlabCrawlerAdapter
        
        # 創建測試數據庫
        test_db_path = "test_finlab.db"
        conn = sqlite3.connect(test_db_path)
        
        # 創建測試表
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_prices (
                stock_id TEXT,
                stock_name TEXT,
                date TEXT,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Volume INTEGER
            )
        ''')
        conn.commit()
        
        # 創建爬蟲適配器
        db_connections = {'price': conn}
        crawler = FinlabCrawlerAdapter(db_connections)
        
        print("✅ 爬蟲適配器創建成功")
        
        # 測試HTTP請求功能
        try:
            response = crawler.requests_get("https://httpbin.org/get", timeout=5)
            if response.status_code == 200:
                print("✅ HTTP請求功能正常")
            else:
                print(f"⚠️ HTTP請求返回狀態碼: {response.status_code}")
        except Exception as e:
            print(f"⚠️ HTTP請求測試失敗: {e}")
        
        # 測試數據庫保存功能
        import pandas as pd
        test_data = pd.DataFrame({
            'stock_id': ['TEST001'],
            'stock_name': ['測試股票'],
            'date': [datetime.date.today()],
            'Open': [100.0],
            'High': [105.0],
            'Low': [98.0],
            'Close': [103.0],
            'Volume': [1000000]
        })
        
        if crawler.save_to_database(test_data, 'stock_prices'):
            print("✅ 數據庫保存功能正常")
        else:
            print("❌ 數據庫保存功能失敗")
        
        # 清理測試數據庫
        conn.close()
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 爬蟲適配器測試失敗: {e}")
        return False

def test_data_manager():
    """測試數據管理器"""
    print("\n📊 測試數據管理器")
    print("=" * 50)
    
    try:
        from finlab_integration.core.data_manager import FinlabDataManager
        
        # 創建測試數據庫
        test_db_path = "test_finlab_data.db"
        conn = sqlite3.connect(test_db_path)
        
        # 創建測試表和數據
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_prices (
                stock_id TEXT,
                date TEXT,
                Open REAL,
                High REAL,
                Low REAL,
                Close REAL,
                Volume INTEGER
            )
        ''')
        
        # 插入測試數據
        test_dates = [
            (datetime.date.today() - datetime.timedelta(days=i)).strftime('%Y-%m-%d')
            for i in range(30, 0, -1)
        ]
        
        for i, date in enumerate(test_dates):
            price = 100 + i * 0.5
            cursor.execute('''
                INSERT INTO stock_prices (stock_id, date, Open, High, Low, Close, Volume)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', ('2330', date, price, price+2, price-1, price+1, 1000000))
        
        conn.commit()
        
        # 創建數據管理器
        db_connections = {'price': conn}
        data_manager = FinlabDataManager(db_connections=db_connections)
        
        print("✅ 數據管理器創建成功")
        
        # 測試股票數據獲取
        df = data_manager.get_stock_data('2330', 10)
        if not df.empty:
            print(f"✅ 股票數據獲取成功: {len(df)} 筆記錄")
            
            # 檢查技術指標
            if 'MA5' in df.columns:
                print("✅ 技術指標計算成功")
            else:
                print("⚠️ 技術指標計算可能有問題")
        else:
            print("❌ 股票數據獲取失敗")
        
        # 測試數據摘要
        summary = data_manager.get_data_summary()
        if summary:
            print(f"✅ 數據摘要獲取成功: {summary}")
        else:
            print("❌ 數據摘要獲取失敗")
        
        # 清理測試數據庫
        conn.close()
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 數據管理器測試失敗: {e}")
        return False

def test_gui_components():
    """測試GUI組件"""
    print("\n🖥️ 測試GUI組件")
    print("=" * 50)
    
    try:
        # 檢查PyQt6是否可用
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QDate
        
        # 創建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from finlab_integration.gui.crawler_dialog import FinlabCrawlerDialog
        
        # 創建對話框（不顯示）
        dialog = FinlabCrawlerDialog()
        
        # 檢查基本屬性
        if hasattr(dialog, 'start_date') and hasattr(dialog, 'end_date'):
            print("✅ GUI組件創建成功")
            
            # 檢查日期設定
            start_date = dialog.start_date.date()
            end_date = dialog.end_date.date()
            print(f"✅ 日期控件正常: {start_date.toString()} ~ {end_date.toString()}")
            
        else:
            print("❌ GUI組件缺少必要屬性")
            return False
        
        return True
        
    except ImportError as e:
        print(f"⚠️ GUI測試跳過（PyQt6不可用）: {e}")
        return True  # GUI測試失敗不影響整體
    except Exception as e:
        print(f"❌ GUI組件測試失敗: {e}")
        return False

def test_directory_structure():
    """測試目錄結構"""
    print("\n📁 測試目錄結構")
    print("=" * 50)
    
    required_files = [
        "finlab_integration/__init__.py",
        "finlab_integration/core/__init__.py",
        "finlab_integration/core/crawler_adapter.py",
        "finlab_integration/core/data_manager.py",
        "finlab_integration/gui/__init__.py",
        "finlab_integration/gui/crawler_dialog.py",
        "finlab_integration/data/__init__.py",
        "finlab_integration/utils/__init__.py",
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            all_exist = False
    
    # 檢查數據目錄
    data_dirs = [
        "data/finlab_data",
        "data/finlab_data/history",
        "data/finlab_data/history/items",
    ]
    
    for dir_path in data_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ (缺失)")
            all_exist = False
    
    return all_exist

def check_dependencies():
    """檢查依賴套件"""
    print("\n📦 檢查依賴套件")
    print("=" * 50)
    
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('sqlite3', 'sqlite3'),
        ('datetime', 'datetime'),
    ]
    
    optional_packages = [
        ('talib', 'TA-Lib'),
        ('PyQt6', 'PyQt6'),
        ('lxml', 'lxml'),
    ]
    
    all_required = True
    
    print("必要套件:")
    for module, name in required_packages:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} (缺失)")
            all_required = False
    
    print("\n可選套件:")
    for module, name in optional_packages:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"⚠️ {name} (建議安裝)")
    
    return all_required

def main():
    """主測試函數"""
    print("🧪 Finlab整合功能測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("目錄結構", test_directory_structure()))
    test_results.append(("依賴套件", check_dependencies()))
    test_results.append(("模組導入", test_module_imports()))
    test_results.append(("爬蟲適配器", test_crawler_adapter()))
    test_results.append(("數據管理器", test_data_manager()))
    test_results.append(("GUI組件", test_gui_components()))
    
    # 顯示測試結果
    print("\n" + "=" * 60)
    print("📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！Finlab整合功能準備就緒。")
        print("\n🚀 下一步:")
        print("1. 啟動主程式: python O3mh_gui_v21_optimized.py")
        print("2. 點擊選單「🎓 Finlab專業分析」")
        print("3. 選擇「🕷️ 專業數據爬取」開始使用")
    else:
        print("⚠️ 部分測試失敗，請檢查相關問題後重新測試。")
        
        if passed >= total * 0.8:
            print("💡 大部分功能正常，可以嘗試基本使用。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
