"""
統一股票資料爬蟲系統
整合每日交易資料爬蟲、上市櫃基本資料爬蟲的統一管理系統
可完全替代原有的price_crawler_auto(HL).py
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import datetime
import os
import pandas as pd
import logging
from typing import Dict, List, Any

# 匯入現有的爬蟲模組
from taiwan_stock_price_crawler import (
    gen_task_parameter_list,
    crawler as daily_crawler,
    DailyTradingDatabase
)
from stock_basic_info_crawler import (
    crawler_stock_basic_info,
    get_market_types,
    StockBasicInfoDatabase
)
from unified_stock_database import UnifiedStockDatabase

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnifiedStockCrawlerGUI:
    """統一股票資料爬蟲GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 統一股票資料爬蟲系統 - 替代price.db")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 設定視窗樣式
        self.setup_style()
        
        # 初始化變數
        self.is_running = False
        self.unified_db = UnifiedStockDatabase()

        # 🎯 初始化股票篩選器
        self.stock_filter = None
        self.init_stock_filter()

        # 建立GUI元件
        self.create_widgets()
        
        # 更新統計資訊
        self.update_stats()

    def init_stock_filter(self):
        """初始化股票篩選器"""
        try:
            from stock_filter import StockFilter
            self.stock_filter = StockFilter()
            logger.info("✅ 統一爬蟲系統：股票篩選器初始化完成")
        except ImportError as e:
            logger.warning(f"⚠️ 統一爬蟲系統：股票篩選器模組載入失敗: {e}")
            self.stock_filter = None

    def set_stock_filter(self, filter_rules):
        """
        設定股票篩選規則（供外部調用）

        Args:
            filter_rules (dict): 篩選規則字典
        """
        if self.stock_filter:
            logger.info("📊 統一爬蟲系統：已設定股票篩選規則（4碼、5碼及前綴為00的6碼ETF）")
        else:
            logger.warning("⚠️ 統一爬蟲系統：股票篩選器未初始化，無法設定篩選規則")

    def filter_stock_codes(self, stock_codes):
        """
        篩選股票代碼清單

        Args:
            stock_codes (list): 原始股票代碼清單

        Returns:
            list: 篩選後的股票代碼清單
        """
        if not self.stock_filter or not stock_codes:
            return stock_codes

        filtered_codes = self.stock_filter.filter_stock_list(stock_codes)
        logger.info(f"📊 統一爬蟲系統股票篩選：原始 {len(stock_codes)} 支 → 篩選後 {len(filtered_codes)} 支")
        return filtered_codes

    def setup_style(self):
        """設定視窗樣式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 設定顏色主題
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'), foreground='#2E86AB')
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'), foreground='#A23B72')
        style.configure('Success.TLabel', foreground='#28A745')
        style.configure('Warning.TLabel', foreground='#FFC107')
        style.configure('Error.TLabel', foreground='#DC3545')
    
    def create_widgets(self):
        """建立GUI元件"""
        # 主標題
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(title_frame, text="🚀 統一股票資料爬蟲系統", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Label(title_frame, text="完全替代price.db的新一代爬蟲系統", 
                 font=('Arial', 9), foreground='gray').pack(side='left', padx=(10, 0))
        
        # 建立筆記本控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 建立各個頁籤
        self.create_daily_trading_tab()
        self.create_basic_info_tab()
        self.create_unified_crawler_tab()
        self.create_database_management_tab()
        self.create_statistics_tab()
    
    def create_daily_trading_tab(self):
        """建立每日交易資料頁籤"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📊 每日交易資料")
        
        # 日期設定區域
        date_frame = ttk.LabelFrame(frame, text="📅 日期範圍設定")
        date_frame.pack(fill='x', padx=10, pady=5)
        
        # 開始日期
        ttk.Label(date_frame, text="開始日期:").grid(row=0, column=0, padx=5, pady=5, sticky='w')
        self.start_date_var = tk.StringVar(value=(datetime.date.today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))
        self.start_date_entry = ttk.Entry(date_frame, textvariable=self.start_date_var, width=12)
        self.start_date_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 結束日期
        ttk.Label(date_frame, text="結束日期:").grid(row=0, column=2, padx=5, pady=5, sticky='w')
        self.end_date_var = tk.StringVar(value=datetime.date.today().strftime('%Y-%m-%d'))
        self.end_date_entry = ttk.Entry(date_frame, textvariable=self.end_date_var, width=12)
        self.end_date_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # 快速選擇按鈕
        quick_frame = ttk.Frame(date_frame)
        quick_frame.grid(row=1, column=0, columnspan=4, pady=5)
        
        ttk.Button(quick_frame, text="最近7天", 
                  command=lambda: self.set_date_range(7)).pack(side='left', padx=2)
        ttk.Button(quick_frame, text="最近30天", 
                  command=lambda: self.set_date_range(30)).pack(side='left', padx=2)
        ttk.Button(quick_frame, text="本月", 
                  command=self.set_current_month).pack(side='left', padx=2)
        
        # 控制按鈕
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.daily_start_button = ttk.Button(control_frame, text="🚀 開始爬取每日資料", 
                                           command=self.start_daily_crawling)
        self.daily_start_button.pack(side='left', padx=5)
        
        self.daily_stop_button = ttk.Button(control_frame, text="⏹️ 停止", 
                                          command=self.stop_crawling, state='disabled')
        self.daily_stop_button.pack(side='left', padx=5)
        
        # 進度顯示
        progress_frame = ttk.LabelFrame(frame, text="📈 爬取進度")
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.daily_progress_var = tk.DoubleVar()
        self.daily_progress = ttk.Progressbar(progress_frame, variable=self.daily_progress_var, 
                                            maximum=100, length=400)
        self.daily_progress.pack(padx=10, pady=5)
        
        self.daily_progress_label = ttk.Label(progress_frame, text="準備就緒")
        self.daily_progress_label.pack(pady=2)
        
        # 日誌顯示
        log_frame = ttk.LabelFrame(frame, text="📝 執行日誌")
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.daily_log_text = tk.Text(log_frame, height=10, wrap='word')
        daily_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.daily_log_text.yview)
        self.daily_log_text.configure(yscrollcommand=daily_scrollbar.set)
        
        self.daily_log_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        daily_scrollbar.pack(side='right', fill='y', pady=5)
    
    def create_basic_info_tab(self):
        """建立基本資料頁籤"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🏢 基本資料")
        
        # 市場選擇區域
        market_frame = ttk.LabelFrame(frame, text="🏛️ 市場別選擇")
        market_frame.pack(fill='x', padx=10, pady=5)
        
        self.market_var = tk.StringVar(value="all")
        
        ttk.Radiobutton(market_frame, text="全部市場", variable=self.market_var, 
                       value="all").pack(side='left', padx=10, pady=5)
        
        market_types = get_market_types()
        for code, name in market_types.items():
            ttk.Radiobutton(market_frame, text=name, variable=self.market_var, 
                           value=code).pack(side='left', padx=10, pady=5)
        
        # 資料模式選擇
        mode_frame = ttk.LabelFrame(frame, text="📋 資料模式")
        mode_frame.pack(fill='x', padx=10, pady=5)
        
        self.use_sample_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(mode_frame, text="使用範例資料 (測試模式)", 
                       variable=self.use_sample_var).pack(side='left', padx=10, pady=5)
        
        # 控制按鈕
        basic_control_frame = ttk.Frame(frame)
        basic_control_frame.pack(fill='x', padx=10, pady=5)
        
        self.basic_start_button = ttk.Button(basic_control_frame, text="🚀 開始爬取基本資料", 
                                           command=self.start_basic_crawling)
        self.basic_start_button.pack(side='left', padx=5)
        
        self.basic_stop_button = ttk.Button(basic_control_frame, text="⏹️ 停止", 
                                          command=self.stop_crawling, state='disabled')
        self.basic_stop_button.pack(side='left', padx=5)
        
        # 進度顯示
        basic_progress_frame = ttk.LabelFrame(frame, text="📈 爬取進度")
        basic_progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.basic_progress_var = tk.DoubleVar()
        self.basic_progress = ttk.Progressbar(basic_progress_frame, variable=self.basic_progress_var, 
                                            maximum=100, length=400)
        self.basic_progress.pack(padx=10, pady=5)
        
        self.basic_progress_label = ttk.Label(basic_progress_frame, text="準備就緒")
        self.basic_progress_label.pack(pady=2)
        
        # 日誌顯示
        basic_log_frame = ttk.LabelFrame(frame, text="📝 執行日誌")
        basic_log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.basic_log_text = tk.Text(basic_log_frame, height=10, wrap='word')
        basic_scrollbar = ttk.Scrollbar(basic_log_frame, orient='vertical', command=self.basic_log_text.yview)
        self.basic_log_text.configure(yscrollcommand=basic_scrollbar.set)
        
        self.basic_log_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        basic_scrollbar.pack(side='right', fill='y', pady=5)
    
    def create_unified_crawler_tab(self):
        """建立統一爬蟲頁籤"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🔄 統一爬蟲")
        
        # 說明文字
        info_frame = ttk.LabelFrame(frame, text="ℹ️ 功能說明")
        info_frame.pack(fill='x', padx=10, pady=5)
        
        info_text = """
        統一爬蟲功能會依序執行：
        1. 爬取上市櫃基本資料 (建立股票清單)
        2. 爬取每日交易資料 (根據日期範圍)
        3. 整合資料到統一資料庫
        4. 提供與price.db相容的查詢介面
        """
        ttk.Label(info_frame, text=info_text, justify='left').pack(padx=10, pady=5)
        
        # 設定區域
        settings_frame = ttk.LabelFrame(frame, text="⚙️ 爬取設定")
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # 日期範圍
        ttk.Label(settings_frame, text="日期範圍:").grid(row=0, column=0, padx=5, pady=5, sticky='w')
        self.unified_start_date_var = tk.StringVar(value=(datetime.date.today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))
        ttk.Entry(settings_frame, textvariable=self.unified_start_date_var, width=12).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="至").grid(row=0, column=2, padx=5, pady=5)
        self.unified_end_date_var = tk.StringVar(value=datetime.date.today().strftime('%Y-%m-%d'))
        ttk.Entry(settings_frame, textvariable=self.unified_end_date_var, width=12).grid(row=0, column=3, padx=5, pady=5)
        
        # 選項
        self.include_basic_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="包含基本資料", 
                       variable=self.include_basic_var).grid(row=1, column=0, padx=5, pady=5, sticky='w')
        
        self.include_daily_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="包含每日交易資料", 
                       variable=self.include_daily_var).grid(row=1, column=1, padx=5, pady=5, sticky='w')
        
        # 控制按鈕
        unified_control_frame = ttk.Frame(frame)
        unified_control_frame.pack(fill='x', padx=10, pady=5)
        
        self.unified_start_button = ttk.Button(unified_control_frame, text="🚀 開始統一爬取", 
                                             command=self.start_unified_crawling)
        self.unified_start_button.pack(side='left', padx=5)
        
        self.unified_stop_button = ttk.Button(unified_control_frame, text="⏹️ 停止", 
                                            command=self.stop_crawling, state='disabled')
        self.unified_stop_button.pack(side='left', padx=5)
        
        # 進度顯示
        unified_progress_frame = ttk.LabelFrame(frame, text="📈 整體進度")
        unified_progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.unified_progress_var = tk.DoubleVar()
        self.unified_progress = ttk.Progressbar(unified_progress_frame, variable=self.unified_progress_var, 
                                              maximum=100, length=400)
        self.unified_progress.pack(padx=10, pady=5)
        
        self.unified_progress_label = ttk.Label(unified_progress_frame, text="準備就緒")
        self.unified_progress_label.pack(pady=2)
        
        # 日誌顯示
        unified_log_frame = ttk.LabelFrame(frame, text="📝 執行日誌")
        unified_log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.unified_log_text = tk.Text(unified_log_frame, height=10, wrap='word')
        unified_scrollbar = ttk.Scrollbar(unified_log_frame, orient='vertical', command=self.unified_log_text.yview)
        self.unified_log_text.configure(yscrollcommand=unified_scrollbar.set)
        
        self.unified_log_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        unified_scrollbar.pack(side='right', fill='y', pady=5)
    
    def create_database_management_tab(self):
        """建立資料庫管理頁籤"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🗄️ 資料庫管理")
        
        # 資料庫路徑資訊
        path_frame = ttk.LabelFrame(frame, text="📁 資料庫路徑")
        path_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(path_frame, text=f"統一資料庫: {self.unified_db.db_path}").pack(padx=10, pady=5, anchor='w')
        
        # 管理功能
        management_frame = ttk.LabelFrame(frame, text="🔧 管理功能")
        management_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(management_frame, text="📂 開啟資料庫資料夾", 
                  command=self.open_database_folder).pack(side='left', padx=5, pady=5)
        
        ttk.Button(management_frame, text="📊 匯出CSV", 
                  command=self.export_to_csv).pack(side='left', padx=5, pady=5)
        
        ttk.Button(management_frame, text="🔄 重新整理統計", 
                  command=self.update_stats).pack(side='left', padx=5, pady=5)
        
        # 資料遷移功能
        migration_frame = ttk.LabelFrame(frame, text="🚚 資料遷移")
        migration_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(migration_frame, text="📥 從price.db匯入", 
                  command=self.import_from_price_db).pack(side='left', padx=5, pady=5)
        
        ttk.Button(migration_frame, text="📥 從daily_trading.db匯入", 
                  command=self.import_from_daily_trading_db).pack(side='left', padx=5, pady=5)
        
        ttk.Button(migration_frame, text="📥 從stock_basic_info.db匯入", 
                  command=self.import_from_basic_info_db).pack(side='left', padx=5, pady=5)
    
    def create_statistics_tab(self):
        """建立統計資訊頁籤"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📈 統計資訊")
        
        # 統計顯示區域
        self.stats_frame = ttk.LabelFrame(frame, text="📊 資料庫統計")
        self.stats_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 統計資訊會在update_stats方法中動態建立
    
    def set_date_range(self, days: int):
        """設定日期範圍"""
        end_date = datetime.date.today()
        start_date = end_date - datetime.timedelta(days=days)
        
        self.start_date_var.set(start_date.strftime('%Y-%m-%d'))
        self.end_date_var.set(end_date.strftime('%Y-%m-%d'))
    
    def set_current_month(self):
        """設定為本月"""
        today = datetime.date.today()
        start_date = today.replace(day=1)
        
        self.start_date_var.set(start_date.strftime('%Y-%m-%d'))
        self.end_date_var.set(today.strftime('%Y-%m-%d'))
    
    def log_message(self, message: str, level: str = 'INFO', tab: str = 'daily'):
        """記錄日誌訊息"""
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 選擇對應的日誌文字框
        if tab == 'daily':
            log_widget = self.daily_log_text
        elif tab == 'basic':
            log_widget = self.basic_log_text
        elif tab == 'unified':
            log_widget = self.unified_log_text
        else:
            log_widget = self.daily_log_text
        
        log_widget.insert('end', formatted_message)
        log_widget.see('end')
        self.root.update_idletasks()
    
    def start_daily_crawling(self):
        """開始每日交易資料爬取"""
        if self.is_running:
            return
        
        try:
            start_date = datetime.datetime.strptime(self.start_date_var.get(), '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(self.end_date_var.get(), '%Y-%m-%d').date()
            
            if start_date > end_date:
                messagebox.showerror("錯誤", "開始日期不能晚於結束日期")
                return
                
        except ValueError:
            messagebox.showerror("錯誤", "日期格式錯誤，請使用 YYYY-MM-DD 格式")
            return
        
        self.is_running = True
        self.daily_start_button.config(state='disabled')
        self.daily_stop_button.config(state='normal')
        
        # 在新線程中執行爬取
        thread = threading.Thread(target=self.daily_crawl_worker, 
                                 args=(start_date.strftime('%Y-%m-%d'), 
                                      end_date.strftime('%Y-%m-%d')))
        thread.daemon = True
        thread.start()
    
    def daily_crawl_worker(self, start_date: str, end_date: str):
        """每日交易資料爬取工作線程"""
        try:
            self.log_message(f"🚀 開始爬取每日交易資料: {start_date} 至 {end_date}", tab='daily')
            
            # 生成任務列表
            task_list = gen_task_parameter_list(start_date, end_date)
            total_tasks = len(task_list)
            
            self.log_message(f"📋 共 {total_tasks} 個爬取任務", tab='daily')
            
            success_count = 0
            error_count = 0
            
            for i, task in enumerate(task_list):
                if not self.is_running:
                    self.log_message("⏹️ 使用者要求停止爬取", tab='daily')
                    break
                
                # 更新進度
                progress = (i / total_tasks) * 100
                self.daily_progress_var.set(progress)
                self.daily_progress_label.config(text=f"{i+1}/{total_tasks}")
                
                # 執行爬取
                try:
                    df = daily_crawler(task)
                    if not df.empty:
                        # 🎯 應用股票篩選功能
                        original_count = len(df)
                        if self.stock_filter and '股票代碼' in df.columns:
                            # 篩選股票代碼
                            valid_codes = self.filter_stock_codes(df['股票代碼'].tolist())
                            df = df[df['股票代碼'].isin(valid_codes)]
                            filtered_count = len(df)

                            if filtered_count < original_count:
                                self.log_message(
                                    f"📊 {task['data_source'].upper()} {task['date']}: "
                                    f"篩選 {original_count} → {filtered_count} 筆", tab='daily'
                                )

                        if not df.empty:
                            saved_count = self.unified_db.save_daily_data(df, task['data_source'])
                            if saved_count > 0:
                                success_count += 1
                                self.log_message(f"✅ {task['data_source'].upper()} {task['date']}: {saved_count} 筆", tab='daily')
                            else:
                                error_count += 1
                                self.log_message(f"❌ {task['data_source'].upper()} {task['date']}: 儲存失敗", tab='daily')
                        else:
                            self.log_message(f"⚠️ {task['data_source'].upper()} {task['date']}: 篩選後無有效資料", tab='daily')
                    else:
                        self.log_message(f"⚠️ {task['data_source'].upper()} {task['date']}: 無資料", tab='daily')
                        
                except Exception as e:
                    error_count += 1
                    self.log_message(f"❌ {task['data_source'].upper()} {task['date']}: {e}", tab='daily')
            
            # 完成
            self.daily_progress_var.set(100)
            self.log_message(f"🎉 每日交易資料爬取完成！成功: {success_count}, 錯誤: {error_count}", tab='daily')
            
        except Exception as e:
            self.log_message(f"❌ 爬取過程發生錯誤: {e}", tab='daily')
        finally:
            # 確保在主線程中更新UI
            self.root.after(0, self.stop_crawling)
            self.root.after(0, self.update_stats)
    
    def start_basic_crawling(self):
        """開始基本資料爬取"""
        if self.is_running:
            return
        
        self.is_running = True
        self.basic_start_button.config(state='disabled')
        self.basic_stop_button.config(state='normal')
        
        # 在新線程中執行爬取
        thread = threading.Thread(target=self.basic_crawl_worker)
        thread.daemon = True
        thread.start()
    
    def basic_crawl_worker(self):
        """基本資料爬取工作線程"""
        try:
            market_selection = self.market_var.get()
            use_sample = self.use_sample_var.get()
            
            mode_text = "範例資料" if use_sample else "實際資料"
            self.log_message(f"🚀 開始爬取基本資料 ({mode_text})", tab='basic')
            
            if market_selection == "all":
                # 爬取所有市場
                market_types = get_market_types()
                total_markets = len(market_types)
                total_saved = 0
                
                for i, (market_code, market_name) in enumerate(market_types.items()):
                    if not self.is_running:
                        self.log_message("⏹️ 使用者要求停止爬取", tab='basic')
                        break
                    
                    # 更新進度
                    progress = (i / total_markets) * 100
                    self.basic_progress_var.set(progress)
                    self.basic_progress_label.config(text=f"{market_name} ({i+1}/{total_markets})")
                    
                    self.log_message(f"🏛️ 爬取 {market_name} {mode_text}...", tab='basic')
                    
                    df = crawler_stock_basic_info(market_code, use_sample)

                    if not df.empty and self.is_running:
                        # 🎯 應用股票篩選功能
                        original_count = len(df)
                        if self.stock_filter and '股票代碼' in df.columns:
                            # 篩選股票代碼
                            valid_codes = self.filter_stock_codes(df['股票代碼'].tolist())
                            df = df[df['股票代碼'].isin(valid_codes)]
                            filtered_count = len(df)

                            if filtered_count < original_count:
                                self.log_message(
                                    f"📊 {market_name}: 篩選 {original_count} → {filtered_count} 筆",
                                    tab='basic'
                                )

                        if not df.empty:
                            saved_count = self.unified_db.save_basic_info(df)
                            total_saved += saved_count
                            self.log_message(f"✅ 成功儲存 {saved_count} 筆 {market_name} 資料", tab='basic')
                        else:
                            self.log_message(f"⚠️ {market_name} 篩選後無有效資料", tab='basic')
                    elif df.empty:
                        self.log_message(f"⚠️ {market_name} 無資料", tab='basic')
                
                self.log_message(f"🎉 全部市場爬取完成，總共儲存 {total_saved} 筆資料", tab='basic')
            else:
                # 爬取單一市場
                market_types = get_market_types()
                market_name = market_types.get(market_selection, market_selection)
                
                self.log_message(f"🏛️ 開始爬取 {market_name} {mode_text}...", tab='basic')
                
                df = crawler_stock_basic_info(market_selection, use_sample)
                
                if not df.empty and self.is_running:
                    saved_count = self.unified_db.save_basic_info(df)
                    self.log_message(f"✅ 成功儲存 {saved_count} 筆 {market_name} 資料", tab='basic')
                elif df.empty:
                    self.log_message(f"⚠️ {market_name} 無資料", tab='basic')
                
                self.log_message(f"🎉 {market_name} 爬取完成", tab='basic')
            
            # 完成
            self.basic_progress_var.set(100)
            
        except Exception as e:
            self.log_message(f"❌ 爬取過程發生錯誤: {e}", tab='basic')
        finally:
            # 確保在主線程中更新UI
            self.root.after(0, self.stop_crawling)
            self.root.after(0, self.update_stats)
    
    def start_unified_crawling(self):
        """開始統一爬取"""
        if self.is_running:
            return
        
        self.is_running = True
        self.unified_start_button.config(state='disabled')
        self.unified_stop_button.config(state='normal')
        
        # 在新線程中執行爬取
        thread = threading.Thread(target=self.unified_crawl_worker)
        thread.daemon = True
        thread.start()
    
    def unified_crawl_worker(self):
        """統一爬取工作線程"""
        try:
            self.log_message("🚀 開始統一爬取流程", tab='unified')
            
            total_steps = 0
            current_step = 0
            
            # 計算總步驟數
            if self.include_basic_var.get():
                total_steps += 1
            if self.include_daily_var.get():
                total_steps += 1
            
            # 步驟1: 爬取基本資料
            if self.include_basic_var.get() and self.is_running:
                current_step += 1
                progress = (current_step / total_steps) * 50  # 基本資料佔50%
                self.unified_progress_var.set(progress)
                self.unified_progress_label.config(text=f"步驟 {current_step}/{total_steps}: 爬取基本資料")
                
                self.log_message("📋 步驟1: 爬取上市櫃基本資料", tab='unified')
                
                market_types = get_market_types()
                total_saved = 0
                
                for market_code, market_name in market_types.items():
                    if not self.is_running:
                        break
                    
                    self.log_message(f"🏛️ 爬取 {market_name} 基本資料...", tab='unified')
                    df = crawler_stock_basic_info(market_code, False)
                    
                    if not df.empty:
                        saved_count = self.unified_db.save_basic_info(df)
                        total_saved += saved_count
                        self.log_message(f"✅ {market_name}: {saved_count} 筆", tab='unified')
                
                self.log_message(f"✅ 基本資料爬取完成，共 {total_saved} 筆", tab='unified')
            
            # 步驟2: 爬取每日交易資料
            if self.include_daily_var.get() and self.is_running:
                current_step += 1
                self.unified_progress_label.config(text=f"步驟 {current_step}/{total_steps}: 爬取每日交易資料")
                
                self.log_message("📊 步驟2: 爬取每日交易資料", tab='unified')
                
                start_date = self.unified_start_date_var.get()
                end_date = self.unified_end_date_var.get()
                
                task_list = gen_task_parameter_list(start_date, end_date)
                total_tasks = len(task_list)
                success_count = 0
                
                for i, task in enumerate(task_list):
                    if not self.is_running:
                        break
                    
                    # 更新進度 (50% + 每日資料進度的50%)
                    daily_progress = (i / total_tasks) * 50
                    total_progress = 50 + daily_progress
                    self.unified_progress_var.set(total_progress)
                    
                    try:
                        df = daily_crawler(task)
                        if not df.empty:
                            saved_count = self.unified_db.save_daily_data(df, task['data_source'])
                            if saved_count > 0:
                                success_count += 1
                    except Exception as e:
                        self.log_message(f"⚠️ {task['data_source']} {task['date']}: {e}", tab='unified')
                
                self.log_message(f"✅ 每日交易資料爬取完成，成功 {success_count} 個任務", tab='unified')
            
            # 完成
            self.unified_progress_var.set(100)
            self.unified_progress_label.config(text="統一爬取完成")
            self.log_message("🎉 統一爬取流程全部完成！", tab='unified')
            
        except Exception as e:
            self.log_message(f"❌ 統一爬取過程發生錯誤: {e}", tab='unified')
        finally:
            # 確保在主線程中更新UI
            self.root.after(0, self.stop_crawling)
            self.root.after(0, self.update_stats)
    
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        
        # 恢復按鈕狀態
        self.daily_start_button.config(state='normal')
        self.daily_stop_button.config(state='disabled')
        self.basic_start_button.config(state='normal')
        self.basic_stop_button.config(state='disabled')
        self.unified_start_button.config(state='normal')
        self.unified_stop_button.config(state='disabled')
    
    def open_database_folder(self):
        """開啟資料庫資料夾"""
        import subprocess
        import platform
        
        folder_path = os.path.dirname(self.unified_db.db_path)
        
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("錯誤", f"無法開啟資料夾: {e}")
    
    def export_to_csv(self):
        """匯出資料到CSV"""
        try:
            folder_path = filedialog.askdirectory(title="選擇匯出資料夾")
            if not folder_path:
                return
            
            # 匯出每日交易資料
            conn = sqlite3.connect(self.unified_db.db_path)
            
            daily_df = pd.read_sql_query('''
                SELECT stock_id, date, open_price, high_price, low_price, 
                       close_price, volume, data_source
                FROM daily_trading_data 
                ORDER BY stock_id, date
            ''', conn)
            
            if not daily_df.empty:
                daily_file = os.path.join(folder_path, f"daily_trading_data_{datetime.date.today().strftime('%Y%m%d')}.csv")
                daily_df.to_csv(daily_file, index=False, encoding='utf-8-sig')
                
            # 匯出基本資料
            basic_df = pd.read_sql_query('''
                SELECT stock_id, company_name, industry_category, market_type,
                       chairman, general_manager, address, phone, website
                FROM stock_basic_info 
                ORDER BY stock_id
            ''', conn)
            
            if not basic_df.empty:
                basic_file = os.path.join(folder_path, f"stock_basic_info_{datetime.date.today().strftime('%Y%m%d')}.csv")
                basic_df.to_csv(basic_file, index=False, encoding='utf-8-sig')
            
            conn.close()
            
            messagebox.showinfo("成功", f"資料已匯出到: {folder_path}")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"匯出失敗: {e}")
    
    def import_from_price_db(self):
        """從price.db匯入資料"""
        try:
            price_db_path = "db/price.db"
            if not os.path.exists(price_db_path):
                messagebox.showerror("錯誤", f"找不到price.db: {price_db_path}")
                return
            
            conn = sqlite3.connect(price_db_path)
            df = pd.read_sql_query('SELECT * FROM stock_daily_data', conn)
            conn.close()
            
            if not df.empty:
                saved_count = self.unified_db.save_daily_data(df, 'price_db_import')
                messagebox.showinfo("成功", f"從price.db匯入 {saved_count} 筆資料")
                self.update_stats()
            else:
                messagebox.showwarning("警告", "price.db中沒有資料")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"匯入失敗: {e}")
    
    def import_from_daily_trading_db(self):
        """從daily_trading.db匯入資料"""
        try:
            daily_db_path = "daily_trading.db"
            if not os.path.exists(daily_db_path):
                messagebox.showerror("錯誤", f"找不到daily_trading.db: {daily_db_path}")
                return
            
            conn = sqlite3.connect(daily_db_path)
            df = pd.read_sql_query('SELECT * FROM daily_trading_data', conn)
            conn.close()
            
            if not df.empty:
                saved_count = self.unified_db.save_daily_data(df, 'daily_trading_import')
                messagebox.showinfo("成功", f"從daily_trading.db匯入 {saved_count} 筆資料")
                self.update_stats()
            else:
                messagebox.showwarning("警告", "daily_trading.db中沒有資料")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"匯入失敗: {e}")
    
    def import_from_basic_info_db(self):
        """從stock_basic_info.db匯入資料"""
        try:
            basic_db_path = "D:/Finlab/history/tables/stock_basic_info.db"
            if not os.path.exists(basic_db_path):
                messagebox.showerror("錯誤", f"找不到stock_basic_info.db: {basic_db_path}")
                return
            
            conn = sqlite3.connect(basic_db_path)
            df = pd.read_sql_query('SELECT * FROM stock_basic_info', conn)
            conn.close()
            
            if not df.empty:
                saved_count = self.unified_db.save_basic_info(df)
                messagebox.showinfo("成功", f"從stock_basic_info.db匯入 {saved_count} 筆資料")
                self.update_stats()
            else:
                messagebox.showwarning("警告", "stock_basic_info.db中沒有資料")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"匯入失敗: {e}")
    
    def update_stats(self):
        """更新統計資訊"""
        try:
            stats = self.unified_db.get_database_stats()
            
            # 清除現有統計顯示
            for widget in self.stats_frame.winfo_children():
                widget.destroy()
            
            # 建立統計顯示
            row = 0
            
            # 基本統計
            ttk.Label(self.stats_frame, text="📊 基本統計", 
                     style='Header.TLabel').grid(row=row, column=0, columnspan=2, padx=10, pady=5, sticky='w')
            row += 1
            
            ttk.Label(self.stats_frame, text="每日交易資料筆數:").grid(row=row, column=0, padx=10, pady=2, sticky='w')
            ttk.Label(self.stats_frame, text=f"{stats.get('daily_records', 0):,}", 
                     style='Success.TLabel').grid(row=row, column=1, padx=10, pady=2, sticky='w')
            row += 1
            
            ttk.Label(self.stats_frame, text="有資料的股票數:").grid(row=row, column=0, padx=10, pady=2, sticky='w')
            ttk.Label(self.stats_frame, text=f"{stats.get('stocks_with_data', 0):,}", 
                     style='Success.TLabel').grid(row=row, column=1, padx=10, pady=2, sticky='w')
            row += 1
            
            ttk.Label(self.stats_frame, text="基本資料筆數:").grid(row=row, column=0, padx=10, pady=2, sticky='w')
            ttk.Label(self.stats_frame, text=f"{stats.get('basic_info_records', 0):,}", 
                     style='Success.TLabel').grid(row=row, column=1, padx=10, pady=2, sticky='w')
            row += 1
            
            # 日期範圍
            date_range = stats.get('date_range', {})
            if date_range.get('start') and date_range.get('end'):
                ttk.Label(self.stats_frame, text="資料時間範圍:").grid(row=row, column=0, padx=10, pady=2, sticky='w')
                ttk.Label(self.stats_frame, text=f"{date_range['start']} 至 {date_range['end']}", 
                         style='Success.TLabel').grid(row=row, column=1, padx=10, pady=2, sticky='w')
                row += 1
            
            # 資料來源統計
            data_sources = stats.get('data_sources', {})
            if data_sources:
                row += 1
                ttk.Label(self.stats_frame, text="📈 資料來源統計", 
                         style='Header.TLabel').grid(row=row, column=0, columnspan=2, padx=10, pady=5, sticky='w')
                row += 1
                
                for source, count in data_sources.items():
                    ttk.Label(self.stats_frame, text=f"{source.upper()}:").grid(row=row, column=0, padx=20, pady=2, sticky='w')
                    ttk.Label(self.stats_frame, text=f"{count:,} 筆", 
                             style='Success.TLabel').grid(row=row, column=1, padx=10, pady=2, sticky='w')
                    row += 1
            
            # 相容性說明
            row += 1
            ttk.Label(self.stats_frame, text="🔄 相容性說明", 
                     style='Header.TLabel').grid(row=row, column=0, columnspan=2, padx=10, pady=5, sticky='w')
            row += 1
            
            compatibility_text = """
            ✅ 完全相容price.db的查詢方式
            ✅ 支援原有策略程式直接使用
            ✅ 提供更豐富的資料欄位
            ✅ 更好的資料品質和完整性
            """
            
            ttk.Label(self.stats_frame, text=compatibility_text, 
                     justify='left').grid(row=row, column=0, columnspan=2, padx=10, pady=5, sticky='w')
            
        except Exception as e:
            logger.error(f"更新統計資訊失敗: {e}")
    
    def run(self):
        """執行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = UnifiedStockCrawlerGUI()
    app.run()
