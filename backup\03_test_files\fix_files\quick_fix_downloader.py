#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速修復月營收下載器
針對剛剛還能用，現在突然不行的問題
"""

import subprocess
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def kill_chrome_processes():
    """強制關閉所有Chrome相關程序"""
    print("🔧 清理Chrome相關程序...")
    
    try:
        # 關閉Chrome程序
        subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                      capture_output=True, text=True)
        
        # 關閉ChromeDriver程序
        subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                      capture_output=True, text=True)
        
        print("✅ Chrome程序已清理")
        time.sleep(2)
        
    except Exception as e:
        print(f"⚠️ 清理程序時發生錯誤: {e}")

def test_basic_connection():
    """測試基本網路連線"""
    print("🌐 測試網路連線...")
    
    try:
        import requests
        
        # 測試Google
        response = requests.get('https://www.google.com', timeout=5)
        if response.status_code == 200:
            print("✅ 網路連線正常")
        else:
            print(f"⚠️ Google回應異常: {response.status_code}")
        
        # 測試GoodInfo
        response = requests.get('https://goodinfo.tw', timeout=10)
        if response.status_code == 200:
            print("✅ GoodInfo網站可訪問")
            return True
        else:
            print(f"❌ GoodInfo回應異常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 網路連線測試失敗: {e}")
        return False

def test_selenium_basic():
    """測試Selenium基本功能"""
    print("🔧 測試Selenium基本功能...")
    
    try:
        # 設置Chrome選項
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        
        print("🚀 啟動Chrome...")
        driver = webdriver.Chrome(options=options)
        
        # 測試訪問Google
        print("📱 測試訪問Google...")
        driver.get('https://www.google.com')
        
        if "Google" in driver.title:
            print("✅ Selenium基本功能正常")
            
            # 測試訪問GoodInfo
            print("📱 測試訪問GoodInfo...")
            driver.get('https://goodinfo.tw')
            time.sleep(3)
            
            if "goodinfo" in driver.title.lower():
                print("✅ GoodInfo訪問正常")
                result = True
            else:
                print(f"⚠️ GoodInfo標題異常: {driver.title}")
                result = False
        else:
            print("❌ Selenium基本功能異常")
            result = False
        
        driver.quit()
        return result
        
    except Exception as e:
        print(f"❌ Selenium測試失敗: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def test_goodinfo_page():
    """測試GoodInfo月營收頁面"""
    print("📊 測試GoodInfo月營收頁面...")
    
    try:
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        driver = webdriver.Chrome(options=options)
        
        # 訪問台積電月營收頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問: {url}")
        
        driver.get(url)
        
        # 等待頁面載入
        print("⏳ 等待頁面載入...")
        time.sleep(8)  # 增加等待時間
        
        # 檢查標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        if "台積電" in title and "營收" in title:
            print("✅ 頁面標題正確")
            
            # 檢查divDetail
            try:
                wait = WebDriverWait(driver, 10)
                div_detail = wait.until(EC.presence_of_element_located((By.ID, "divDetail")))
                
                detail_text = div_detail.text
                print(f"📊 divDetail長度: {len(detail_text)} 字符")
                
                if len(detail_text) > 1000:  # 應該有足夠的內容
                    print("✅ divDetail內容充足")
                    
                    # 檢查是否包含股票代號
                    import re
                    stock_codes = re.findall(r'\b\d{4}\b', detail_text)
                    unique_codes = len(set(stock_codes))
                    
                    print(f"📈 找到股票代號: {unique_codes} 個")
                    
                    if unique_codes > 1000:  # 應該有很多股票
                        print("✅ 包含大量股票資料")
                        
                        # 檢查匯出按鈕
                        export_buttons = driver.find_elements(By.XPATH, "//input[@value='XLS']")
                        print(f"📤 找到匯出按鈕: {len(export_buttons)} 個")
                        
                        if export_buttons:
                            print("✅ 匯出按鈕存在")
                            result = True
                        else:
                            print("❌ 未找到匯出按鈕")
                            result = False
                    else:
                        print("❌ 股票資料不足")
                        result = False
                else:
                    print("❌ divDetail內容不足")
                    result = False
                    
            except Exception as e:
                print(f"❌ 檢查divDetail失敗: {e}")
                result = False
        else:
            print("❌ 頁面標題不正確")
            result = False
        
        driver.quit()
        return result
        
    except Exception as e:
        print(f"❌ 測試GoodInfo頁面失敗: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def try_download_test():
    """嘗試實際下載測試"""
    print("🧪 嘗試實際下載測試...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        print("🚀 開始測試下載...")
        result = downloader.download_stock_revenue('2330')
        
        if result:
            print("✅ 下載測試成功！")
            
            # 檢查下載文件
            import glob
            excel_files = glob.glob(os.path.join(downloader.download_dir, "*.xls*"))
            
            if excel_files:
                latest_file = max(excel_files, key=os.path.getctime)
                file_size = os.path.getsize(latest_file)
                print(f"📄 下載文件: {os.path.basename(latest_file)} ({file_size:,} bytes)")
                
                if file_size > 100000:  # 至少100KB
                    print("✅ 文件大小正常")
                    return True
                else:
                    print("⚠️ 文件大小異常")
                    return False
            else:
                print("❌ 未找到下載文件")
                return False
        else:
            print("❌ 下載測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 下載測試異常: {e}")
        return False

def main():
    """主函數"""
    print("🔧 快速修復月營收下載器")
    print("=" * 50)
    print("針對剛剛還能用，現在突然不行的問題")
    
    # 步驟1：清理程序
    kill_chrome_processes()
    
    # 步驟2：測試網路
    if not test_basic_connection():
        print("❌ 網路連線有問題，請檢查網路設定")
        return
    
    # 步驟3：測試Selenium
    if not test_selenium_basic():
        print("❌ Selenium有問題，請重新安裝ChromeDriver")
        return
    
    # 步驟4：測試GoodInfo頁面
    if not test_goodinfo_page():
        print("❌ GoodInfo頁面有問題")
        print("💡 建議：等待幾分鐘後重試，可能是伺服器暫時忙碌")
        return
    
    # 步驟5：嘗試實際下載
    if try_download_test():
        print("\n🎉 修復成功！")
        print("✅ 月營收下載器已恢復正常")
        print("💡 建議：現在可以使用GUI重新下載")
    else:
        print("\n❌ 仍有問題")
        print("💡 建議：")
        print("   1. 重新啟動電腦")
        print("   2. 檢查防毒軟體是否阻擋")
        print("   3. 等待一段時間後重試")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()
