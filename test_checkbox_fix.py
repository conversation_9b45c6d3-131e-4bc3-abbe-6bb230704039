#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試複選框對比度修復
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QPushButton, QTextEdit, QLabel, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class CheckboxTestWindow(QMainWindow):
    """測試複選框對比度修復的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("☑️ 複選框對比度修復測試")
        self.setGeometry(100, 100, 1000, 800)
        
        # 創建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("☑️ 複選框對比度修復測試")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2E86AB; margin: 15px; padding: 15px;")
        layout.addWidget(title_label)
        
        # 修復說明
        fix_info = QTextEdit()
        fix_info.setMaximumHeight(200)
        fix_info.setHtml("""
        <h3>☑️ 複選框對比度修復</h3>
        <p><b>修復內容：</b></p>
        <ul>
            <li>✅ <b>文字顏色</b> - 改為深色 #212529 並使用 !important 確保優先級</li>
            <li>✅ <b>字體粗細</b> - 設為 bold 增加可讀性</li>
            <li>✅ <b>背景透明</b> - 確保不會有白底白字問題</li>
            <li>✅ <b>間距優化</b> - 增加 padding 和 spacing 改善視覺效果</li>
        </ul>
        """)
        layout.addWidget(fix_info)
        
        # 測試複選框區域
        test_group = QGroupBox("📋 複選框對比度測試")
        test_layout = QVBoxLayout(test_group)
        
        # 測試複選框 1
        test_cb1 = QCheckBox("📈 測試複選框 1 - 市場指數資訊")
        test_cb1.setChecked(True)
        test_cb1.setStyleSheet("""
            QCheckBox { 
                font-size: 14px; 
                padding: 8px; 
                color: #212529 !important; 
                font-weight: bold;
                background-color: transparent;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #6c757d;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #007bff;
                background-color: #007bff;
            }
        """)
        test_layout.addWidget(test_cb1)
        
        # 測試複選框 2
        test_cb2 = QCheckBox("💰 測試複選框 2 - 融資融券統計")
        test_cb2.setChecked(True)
        test_cb2.setStyleSheet("""
            QCheckBox { 
                font-size: 14px; 
                padding: 8px; 
                color: #212529 !important; 
                font-weight: bold;
                background-color: transparent;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #6c757d;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #007bff;
                background-color: #007bff;
            }
        """)
        test_layout.addWidget(test_cb2)
        
        # 測試複選框 3
        test_cb3 = QCheckBox("📊 測試複選框 3 - 歷史指數資料")
        test_cb3.setChecked(False)
        test_cb3.setStyleSheet("""
            QCheckBox { 
                font-size: 14px; 
                padding: 8px; 
                color: #212529 !important; 
                font-weight: bold;
                background-color: transparent;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #6c757d;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #007bff;
                background-color: #007bff;
            }
        """)
        test_layout.addWidget(test_cb3)
        
        # 對比說明
        contrast_label = QLabel("✅ 如果您能清楚看到上面的複選框文字，說明對比度修復成功！")
        contrast_label.setStyleSheet("color: #28a745; font-size: 14px; font-weight: bold; margin: 10px; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;")
        test_layout.addWidget(contrast_label)
        
        layout.addWidget(test_group)
        
        # 測試按鈕
        test_btn = QPushButton("🚀 開啟修復後的爬蟲界面")
        test_btn.clicked.connect(self.test_main_interface)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(test_btn)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.log("☑️ 複選框對比度修復測試程式已啟動")
        self.log("✅ 複選框文字顏色已設為深色 #212529")
        self.log("✅ 使用 !important 確保樣式優先級")
        self.log("✅ 字體設為粗體增加可讀性")
        self.log("✅ 背景設為透明避免衝突")
    
    def log(self, message):
        """添加日誌訊息"""
        from datetime import datetime
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def test_main_interface(self):
        """測試主界面"""
        try:
            from twse_market_data_dialog import TWSEMarketDataDialog
            
            self.log("🚀 正在開啟修復後的台灣證交所爬蟲界面...")
            
            # 創建對話框
            dialog = TWSEMarketDataDialog(self)
            
            self.log("✅ 界面已成功創建")
            self.log("🎯 請特別檢查複選框文字是否清晰可見")
            self.log("📋 檢查項目：")
            self.log("1. 複選框文字是否為深色")
            self.log("2. 文字是否清晰可讀")
            self.log("3. 沒有白底白字問題")
            
            dialog.show()
            
        except Exception as e:
            self.log(f"❌ 測試失敗: {e}")
            import traceback
            self.log(f"詳細錯誤: {traceback.format_exc()}")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    window = CheckboxTestWindow()
    window.show()
    
    print("☑️ 複選框對比度修復測試程式已啟動")
    print("✅ 主要修復：")
    print("1. 複選框文字改為深色 #212529")
    print("2. 使用 !important 確保樣式優先級")
    print("3. 字體設為粗體增加可讀性")
    print("4. 背景設為透明避免衝突")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
