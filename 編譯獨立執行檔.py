#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
編譯 O3mh_gui_v21_optimized.py 為獨立執行檔
使用 PyInstaller 進行編譯打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """檢查編譯所需的依賴"""
    print("🔍 檢查編譯依賴...")
    
    required_packages = [
        'pyinstaller',
        'PyQt6',
        'pandas',
        'numpy',
        'pyqtgraph',
        'requests',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PyQt6':
                import PyQt6
            elif package == 'pyinstaller':
                import PyInstaller
            elif package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下套件，請先安裝：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依賴檢查通過")
    return True

def create_spec_file():
    """創建 PyInstaller 規格檔案"""
    print("📝 創建 PyInstaller 規格檔案...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 獲取專案根目錄
project_root = Path(__file__).parent

block_cipher = None

# 主程式分析
a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=[
        # 包含所有必要的模組目錄
        ('strategies', 'strategies'),
        ('config', 'config'),
        ('core', 'core'),
        ('data', 'data'),
        ('dialogs', 'dialogs'),
        ('monitoring', 'monitoring'),
        ('charts', 'charts'),
        ('gui', 'gui'),
        
        # 包含配置檔案
        ('requirements.txt', '.'),
        ('config.yaml', '.'),
        ('app_config.json', '.'),
        ('database_config.json', '.'),
        ('strategies.json', '.'),
        
        # 包含股票名稱映射
        ('stock_name_mapping.py', '.'),
        
        # 包含重要的工具模組
        ('unified_monitor_manager.py', '.'),
        ('smart_trading_strategies.py', '.'),
        ('enhanced_market_scanner.py', '.'),
        ('enhanced_yfinance_fetcher.py', '.'),
        ('simple_incremental_updater.py', '.'),
        ('enhanced_data_fetcher.py', '.'),
        ('optimized_market_scanner.py', '.'),
        ('safe_market_scanner.py', '.'),
        ('real_data_sources.py', '.'),
        ('stock_filter.py', '.'),
        ('pe_data_provider.py', '.'),
        ('signal_strength_analyzer.py', '.'),
        ('intraday_monitor.py', '.'),
        ('intraday_data_fetcher.py', '.'),
        ('strategy_intersection_analyzer.py', '.'),
        ('strategy_descriptions.py', '.'),
    ],
    hiddenimports=[
        # PyQt6 相關
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        
        # 數據處理
        'pandas',
        'numpy',
        'sqlite3',
        
        # 圖表
        'pyqtgraph',
        'matplotlib',
        'seaborn',
        
        # 網路請求
        'requests',
        'urllib3',
        'bs4',
        
        # 其他依賴
        'yaml',
        'json',
        'csv',
        'datetime',
        'threading',
        'concurrent.futures',
        'logging',
        'traceback',
        'os',
        'sys',
        'time',
        'random',
        'calendar',
        
        # 可選依賴
        'yfinance',
        'twstock',
        'apscheduler',
        'schedule',
        'tqdm',
        'colorlog',
        'openpyxl',
        'xlsxwriter',
        'sqlalchemy',
        'cryptography',
        'numba',
        'psutil',
        'python-dateutil',
        'jsonschema',
        'python-dotenv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模組以減小檔案大小
        'tkinter',
        'matplotlib.tests',
        'numpy.tests',
        'pandas.tests',
        'test',
        'tests',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集所有檔案
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 創建執行檔
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='台股智能選股系統',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 設為 False 隱藏控制台視窗
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加圖示檔案路徑
)
'''
    
    with open('O3mh_gui_v21_optimized.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 規格檔案創建完成")

def compile_executable():
    """編譯執行檔"""
    print("🚀 開始編譯執行檔...")
    print("⏳ 這可能需要幾分鐘時間，請耐心等待...")
    
    try:
        # 使用 PyInstaller 編譯
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',  # 清理之前的編譯檔案
            '--noconfirm',  # 不詢問覆蓋
            'O3mh_gui_v21_optimized.spec'
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ 編譯成功！")
            print(f"📁 執行檔位置: dist/台股智能選股系統.exe")
            return True
        else:
            print("❌ 編譯失敗")
            print("錯誤輸出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程發生錯誤: {e}")
        return False

def create_portable_package():
    """創建便攜版套件"""
    print("📦 創建便攜版套件...")
    
    try:
        # 創建便攜版目錄
        portable_dir = Path("台股智能選股系統_便攜版")
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        
        portable_dir.mkdir()
        
        # 複製執行檔
        exe_path = Path("dist/台股智能選股系統.exe")
        if exe_path.exists():
            shutil.copy2(exe_path, portable_dir / "台股智能選股系統.exe")
        
        # 複製必要的配置檔案
        config_files = [
            'requirements.txt',
            'README.md',
            '使用說明.md',
            '股票名稱補齊完成報告.md',
            '修復完成總結報告.md'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, portable_dir / config_file)
        
        # 創建啟動腳本
        startup_script = '''@echo off
chcp 65001 > nul
echo 🚀 啟動台股智能選股系統...
echo.
echo 📍 系統資訊:
echo    版本: v21.0 優化增強版
echo    編譯日期: %date%
echo    執行環境: Windows 獨立版
echo.
echo 🔄 正在啟動程式...
"台股智能選股系統.exe"
if errorlevel 1 (
    echo.
    echo ❌ 程式執行失敗
    echo 💡 請檢查:
    echo    1. 是否有足夠的系統權限
    echo    2. 防毒軟體是否阻擋執行
    echo    3. 系統是否支援 Windows 10/11
    echo.
    pause
)
'''
        
        with open(portable_dir / "啟動程式.bat", 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        # 創建說明檔案
        readme_content = '''# 台股智能選股系統 - 便攜版

## 🚀 快速開始

1. **直接執行**: 雙擊 `台股智能選股系統.exe` 或 `啟動程式.bat`
2. **首次使用**: 程式會自動創建必要的資料庫和配置檔案
3. **資料更新**: 程式會自動檢查和更新股票資料

## 📋 系統需求

- **作業系統**: Windows 10/11 (64位元)
- **記憶體**: 建議 4GB 以上
- **硬碟空間**: 建議 2GB 以上可用空間
- **網路連線**: 需要網路連線以獲取最新股票資料

## 🎯 主要功能

- ✅ 多策略智能選股
- ✅ 實時K線圖表分析  
- ✅ 月營收綜合評估
- ✅ 三大法人買賣狀況
- ✅ 開盤前市場監控
- ✅ Excel報告導出

## 🔧 故障排除

### 程式無法啟動
1. 檢查是否有防毒軟體阻擋
2. 以系統管理員身分執行
3. 檢查 Windows 版本是否支援

### 資料無法更新
1. 檢查網路連線
2. 檢查防火牆設定
3. 重新啟動程式

## 📞 技術支援

如有問題請參考完整的使用說明文檔或聯繫技術支援。

---
**版本**: v21.0 優化增強版  
**編譯日期**: ''' + str(datetime.now().strftime('%Y-%m-%d')) + '''  
**檔案大小**: 約 200-300MB
'''
        
        with open(portable_dir / "README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 便攜版套件創建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 創建便攜版套件失敗: {e}")
        return False

def main():
    """主函數"""
    print("🎯 台股智能選股系統 - 獨立執行檔編譯工具")
    print("=" * 60)
    
    # 檢查當前目錄
    if not os.path.exists('O3mh_gui_v21_optimized.py'):
        print("❌ 找不到主程式檔案 O3mh_gui_v21_optimized.py")
        print("請確保在正確的專案目錄中執行此腳本")
        return False
    
    # 步驟1: 檢查依賴
    if not check_dependencies():
        return False
    
    # 步驟2: 創建規格檔案
    create_spec_file()
    
    # 步驟3: 編譯執行檔
    if not compile_executable():
        return False
    
    # 步驟4: 創建便攜版套件
    create_portable_package()
    
    print("\n🎉 編譯完成！")
    print("=" * 60)
    print("📁 輸出檔案:")
    print("   • dist/台股智能選股系統.exe - 主執行檔")
    print("   • 台股智能選股系統_便攜版/ - 完整便攜版套件")
    print("\n💡 使用建議:")
    print("   • 可以直接執行 .exe 檔案")
    print("   • 便攜版套件包含完整的說明文檔")
    print("   • 首次執行會自動創建必要的資料庫")
    
    return True

if __name__ == "__main__":
    from datetime import datetime
    success = main()
    if not success:
        print("\n❌ 編譯失敗，請檢查錯誤訊息並重試")
        sys.exit(1)
