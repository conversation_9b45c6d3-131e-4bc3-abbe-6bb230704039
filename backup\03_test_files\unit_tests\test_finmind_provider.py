#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試FinMind數據提供器
"""

import logging
from datetime import datetime
from finmind_data_provider import FinMindDataProvider

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_finmind_provider():
    """測試FinMind數據提供器"""
    print("🧪 測試FinMind數據提供器")
    print("=" * 50)
    
    # 創建數據提供器
    provider = FinMindDataProvider()
    
    # 測試股票列表
    test_stocks = ["2330", "2317", "2454", "2412"]  # 台積電、鴻海、聯發科、中華電
    
    print(f"📊 測試股票: {', '.join(test_stocks)}")
    print()
    
    for stock_id in test_stocks:
        print(f"🔍 測試股票: {stock_id}")
        print("-" * 30)
        
        # 測試財務數據
        print("📈 財務報表數據:")
        financial = provider.get_financial_statements(stock_id)
        if financial:
            print(f"  ✅ 總資產: {financial.get('total_assets', 0):,}")
            print(f"  ✅ 淨利: {financial.get('net_income', 0):,}")
            print(f"  ✅ EPS: {financial.get('eps', 0)}")
            print(f"  ✅ ROE: {financial.get('roe', 0):.2f}%")
        else:
            print("  ❌ 無法獲取財務數據")
        
        # 測試股利數據
        print("💰 股利數據:")
        dividend = provider.get_dividend_data(stock_id)
        if dividend:
            print(f"  ✅ 現金股利: {dividend.get('cash_dividend', 0)}")
            print(f"  ✅ 股票股利: {dividend.get('stock_dividend', 0)}")
            print(f"  ✅ 除息日: {dividend.get('ex_dividend_date', 'N/A')}")
        else:
            print("  ❌ 無法獲取股利數據")
        
        # 測試本益比數據
        print("📊 本益比數據:")
        per = provider.get_per_data(stock_id)
        if per:
            print(f"  ✅ 本益比: {per.get('pe_ratio', 0):.2f}")
            print(f"  ✅ 股價: {per.get('price', 0):.2f}")
            print(f"  ✅ 日期: {per.get('date', 'N/A')}")
        else:
            print("  ❌ 無法獲取本益比數據")
        
        # 測試營收數據
        print("💹 營收數據:")
        revenue = provider.get_revenue_data(stock_id)
        if revenue:
            print(f"  ✅ 營收: {revenue.get('revenue', 0):,} 千元")
            print(f"  ✅ 月成長率: {revenue.get('revenue_growth_rate', 0):.2f}%")
            print(f"  ✅ 年成長率: {revenue.get('yoy_growth_rate', 0):.2f}%")
            print(f"  ✅ 趨勢: {revenue.get('revenue_trend', 'N/A')}")
        else:
            print("  ❌ 無法獲取營收數據")
        
        # 測試綜合數據
        print("🎯 綜合數據:")
        comprehensive = provider.get_comprehensive_data(stock_id)
        if comprehensive.get('is_real_data'):
            data_sources = comprehensive.get('data_sources', [])
            print(f"  ✅ 數據來源: {', '.join(data_sources)}")
            print(f"  ✅ 真實數據: {comprehensive.get('is_real_data')}")
        else:
            print("  ❌ 無法獲取綜合數據")
        
        print()
    
    # 顯示API使用統計
    print("📊 API使用統計:")
    stats = provider.get_api_usage_stats()
    print(f"  今日使用: {stats['api_calls_today']}/{stats['daily_limit']}")
    print(f"  使用率: {stats['usage_percentage']:.1f}%")
    print(f"  剩餘額度: {stats['remaining_calls']}")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def test_strategy_integration():
    """測試策略整合"""
    print("\n🎯 測試策略整合")
    print("=" * 50)
    
    provider = FinMindDataProvider()
    
    # 模擬高殖利率烏龜策略需要的數據
    print("🐢 高殖利率烏龜策略數據需求:")
    
    test_stock = "2330"
    
    # 獲取股利數據
    dividend = provider.get_dividend_data(test_stock)
    per = provider.get_per_data(test_stock)
    
    if dividend and per:
        cash_dividend = dividend.get('cash_dividend', 0)
        current_price = per.get('price', 0)
        
        if current_price > 0:
            dividend_yield = (cash_dividend / current_price) * 100
            print(f"  ✅ {test_stock} 殖利率: {dividend_yield:.2f}%")
            
            # 判斷是否符合高殖利率條件
            if dividend_yield >= 4.0:
                print(f"  🎉 符合高殖利率條件 (≥4%)")
            else:
                print(f"  ⚠️ 不符合高殖利率條件 (<4%)")
        else:
            print(f"  ❌ 無法計算殖利率（缺少股價數據）")
    else:
        print(f"  ❌ 無法獲取必要數據")
    
    # 模擬超級績效策略需要的數據
    print("\n🚀 超級績效策略數據需求:")
    
    revenue = provider.get_revenue_data(test_stock)
    if revenue:
        growth_rate = revenue.get('revenue_growth_rate', 0)
        print(f"  ✅ {test_stock} 營收成長率: {growth_rate:.2f}%")
        
        # 判斷是否符合營收成長條件
        if growth_rate >= 10.0:
            print(f"  🎉 符合營收成長條件 (≥10%)")
        elif growth_rate >= 0:
            print(f"  ⚠️ 營收正成長但不夠強勁 (<10%)")
        else:
            print(f"  ❌ 營收負成長")
    else:
        print(f"  ❌ 無法獲取營收數據")

def main():
    """主函數"""
    test_finmind_provider()
    test_strategy_integration()

if __name__ == '__main__':
    main()
