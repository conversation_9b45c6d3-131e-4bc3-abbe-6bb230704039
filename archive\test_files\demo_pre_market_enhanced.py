#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示改進後的開盤前監控系統
"""

import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入開盤前監控系統
from O3mh_gui_v21_optimized import PreMarketMonitor

def demo_enhanced_monitor():
    """展示改進後的監控系統"""
    print("🚀 改進後的開盤前監控系統展示")
    print("=" * 50)
    
    # 初始化監控系統
    monitor = PreMarketMonitor()
    print(f"✅ 系統版本: {monitor.version}")
    print(f"📊 緩存機制: 已啟用")
    print(f"⏱️ API冷卻時間: {monitor.api_cooldown}秒")
    
    print("\n📈 獲取市場概況...")
    print("-" * 30)
    
    # 獲取關鍵指標
    try:
        # 美股指數 (重點關注)
        us_data = monitor.get_us_indices()
        print("\n🇺🇸 美股指數:")
        for name, data in us_data.items():
            trend = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
            data_quality = "🟢" if "真實" in data['status'] else "🟡"
            print(f"  {trend} {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) {data_quality}")
        
        # 台灣市場 (本地重點)
        taiwan_data = monitor.get_taiwan_futures()
        print("\n🇹🇼 台灣市場:")
        for name, data in taiwan_data.items():
            trend = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
            data_quality = "🟢" if "真實" in data['status'] else "🟡"
            print(f"  {trend} {name}: {data['price']:.2f} ({data['change_pct']:+.2f}%) {data_quality}")
        
        # 商品價格 (通膨指標)
        commodity_data = monitor.get_commodity_prices()
        print("\n🛢️ 關鍵商品:")
        for name, data in commodity_data.items():
            trend = "📈" if data['change_pct'] > 0 else "📉" if data['change_pct'] < 0 else "➡️"
            data_quality = "🟢" if "真實" in data['status'] else "🟡"
            print(f"  {trend} {name}: ${data['price']:.2f} ({data['change_pct']:+.2f}%) {data_quality}")
        
        print("\n💡 圖例:")
        print("  🟢 真實數據  🟡 智能模擬")
        print("  📈 上漲  📉 下跌  ➡️ 平盤")
        
    except Exception as e:
        print(f"❌ 獲取數據時發生錯誤: {e}")
    
    print("\n" + "=" * 50)
    print("✨ 系統特色:")
    print("  • 智能API限制處理")
    print("  • 數據緩存機制")
    print("  • 真實數據優先，智能模擬備用")
    print("  • 多市場全覆蓋")
    print("=" * 50)

def demo_market_analysis():
    """展示市場分析功能"""
    print("\n🔍 市場分析功能展示")
    print("-" * 30)
    
    monitor = PreMarketMonitor()
    
    # 執行完整掃描
    scan_results = monitor.run_full_scan()
    
    if scan_results:
        # 統計分析
        total_up = 0
        total_down = 0
        total_flat = 0
        
        categories = ['us_indices', 'asia_indices', 'commodities', 'fx_rates', 'taiwan_futures', 'crypto']
        
        for category in categories:
            data = scan_results.get(category, {})
            for item_name, item_data in data.items():
                if isinstance(item_data, dict) and 'change_pct' in item_data:
                    change = item_data['change_pct']
                    if change > 0.1:
                        total_up += 1
                    elif change < -0.1:
                        total_down += 1
                    else:
                        total_flat += 1
        
        total_items = total_up + total_down + total_flat
        
        print(f"📊 市場動向統計:")
        print(f"  上漲項目: {total_up} ({total_up/total_items*100:.1f}%)")
        print(f"  下跌項目: {total_down} ({total_down/total_items*100:.1f}%)")
        print(f"  平盤項目: {total_flat} ({total_flat/total_items*100:.1f}%)")
        
        # 市場情緒
        if total_up > total_down:
            sentiment = "樂觀 📈"
        elif total_down > total_up:
            sentiment = "謹慎 📉"
        else:
            sentiment = "中性 ➡️"
        
        print(f"\n🎯 整體市場情緒: {sentiment}")
        
        # 風險提示
        high_volatility_items = []
        for category in categories:
            data = scan_results.get(category, {})
            for item_name, item_data in data.items():
                if isinstance(item_data, dict) and 'change_pct' in item_data:
                    if abs(item_data['change_pct']) > 2.0:
                        high_volatility_items.append(f"{item_name} ({item_data['change_pct']:+.2f}%)")
        
        if high_volatility_items:
            print(f"\n⚠️ 高波動項目:")
            for item in high_volatility_items[:3]:  # 只顯示前3個
                print(f"  • {item}")
    
    else:
        print("❌ 無法獲取掃描結果")

if __name__ == "__main__":
    # 設置簡潔的日誌
    logging.basicConfig(
        level=logging.WARNING,  # 只顯示警告和錯誤
        format='%(levelname)s: %(message)s'
    )
    
    try:
        # 主要展示
        demo_enhanced_monitor()
        
        # 分析展示
        demo_market_analysis()
        
        print(f"\n⏰ 展示完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 展示過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
