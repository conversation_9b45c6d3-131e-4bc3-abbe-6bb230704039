#!/usr/bin/env python3
"""
測試營收股價雙渦輪策略
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication

def test_revenue_price_dual_turbo_strategy():
    """測試營收股價雙渦輪策略"""
    print("🧪 測試營收股價雙渦輪策略")
    print("=" * 50)
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 創建主視窗
        window = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查策略是否已添加
        print(f"\n🔍 檢查策略定義:")
        
        # 檢查策略字典
        if hasattr(window, 'strategies') and "營收股價雙渦輪" in window.strategies:
            strategy_config = window.strategies["營收股價雙渦輪"]
            print(f"  ✅ 策略已添加到策略字典")
            print(f"  📋 策略配置: {strategy_config}")
        else:
            print(f"  ❌ 策略未添加到策略字典")
        
        # 檢查策略下拉選單
        print(f"\n🔍 檢查策略下拉選單:")
        strategy_found = False
        for i in range(window.strategy_combo.count()):
            item_text = window.strategy_combo.itemText(i)
            if "營收股價雙渦輪" in item_text:
                strategy_found = True
                print(f"  ✅ 策略在下拉選單中找到: {item_text}")
                break
        
        if not strategy_found:
            print(f"  ❌ 策略未在下拉選單中找到")
        
        # 檢查策略檢查方法
        print(f"\n🔍 檢查策略檢查方法:")
        check_methods = [
            'check_revenue_price_dual_turbo_strategy',
            'check_revenue_new_high',
            'check_price_new_high_sustain',
            'check_volume_threshold',
            'calculate_revenue_yoy_growth'
        ]
        
        for method_name in check_methods:
            if hasattr(window, method_name):
                method = getattr(window, method_name)
                if callable(method):
                    print(f"  ✅ {method_name} - 存在且可調用")
                else:
                    print(f"  ❌ {method_name} - 存在但不可調用")
            else:
                print(f"  ❌ {method_name} - 不存在")
        
        # 檢查表格設置方法
        print(f"\n🔍 檢查表格設置方法:")
        if hasattr(window, 'setup_revenue_price_dual_turbo_strategy_table'):
            print(f"  ✅ setup_revenue_price_dual_turbo_strategy_table - 存在")
        else:
            print(f"  ❌ setup_revenue_price_dual_turbo_strategy_table - 不存在")
        
        # 測試策略檢查方法（模擬雙重強勢股數據）
        print(f"\n🧪 測試策略檢查方法:")
        try:
            import pandas as pd
            import numpy as np
            
            # 創建模擬雙重強勢股數據
            dates = pd.date_range('2022-01-01', periods=400, freq='D')
            np.random.seed(42)
            
            # 模擬雙重強勢股價格（營收和股價雙突破）
            base_price = 80
            price_changes = np.random.normal(0.003, 0.02, 400)  # 較高成長率
            prices = [base_price]
            
            # 模擬強勢突破的價格走勢
            for i in range(1, 400):
                change = price_changes[i]
                # 在最後5天加強突破效果
                if i >= 395:
                    change += 0.01  # 額外1%漲幅
                new_price = prices[-1] * (1 + change)
                new_price = max(70, min(150, new_price))  # 限制在70-150元區間
                prices.append(new_price)
            
            # 模擬大成交量（強勢股特徵）
            volumes = np.random.randint(600000, 1200000, 400)  # 600-1200張
            
            # 創建DataFrame
            test_df = pd.DataFrame({
                'Date': dates,
                'Open': [p * 0.995 for p in prices],
                'High': [p * 1.005 for p in prices],
                'Low': [p * 0.995 for p in prices],
                'Close': prices,
                'Volume': volumes
            })
            
            print(f"  📊 測試數據: 價格範圍 {min(prices):.2f}-{max(prices):.2f}元")
            print(f"  📊 最新價格: {prices[-1]:.2f}元")
            print(f"  📊 平均成交量: {np.mean(volumes)/1000:.0f}張")
            print(f"  📊 年化報酬率: {((prices[-1]/prices[0])**(365/400)-1)*100:.1f}%")
            
            # 測試策略檢查
            result = window.check_revenue_price_dual_turbo_strategy(test_df)
            print(f"  📊 策略檢查結果: {result[0]}")
            print(f"  📝 檢查說明: {result[1]}")
            
            # 測試各項條件檢查
            revenue_high = window.check_revenue_new_high(test_df)
            print(f"  📈 營收創新高: {revenue_high[0]} - {revenue_high[1]}")
            
            price_high = window.check_price_new_high_sustain(test_df)
            print(f"  🚀 股價創新高: {price_high[0]} - {price_high[1]}")
            
            volume_check = window.check_volume_threshold(test_df)
            print(f"  📊 成交量檢查: {volume_check[0]} - {volume_check[1]}")
            
            yoy_growth = window.calculate_revenue_yoy_growth(test_df)
            print(f"  📈 營收年增率: {yoy_growth:.1f}%")
            
        except Exception as e:
            print(f"  ❌ 策略檢查測試失敗: {e}")
            import traceback
            traceback.print_exc()
        
        # 檢查策略說明文檔
        print(f"\n📖 檢查策略說明文檔:")
        
        # 檢查策略概述
        if hasattr(window, 'get_strategy_overview'):
            overview = window.get_strategy_overview()
            if "營收股價雙渦輪" in overview:
                print(f"  ✅ 策略概述已添加")
                strategy_text = overview["營收股價雙渦輪"]
                has_turbo_info = "雙渦輪" in strategy_text and "營收" in strategy_text
                has_warning = "color: red" in strategy_text
                print(f"  {'✅' if has_turbo_info else '❌'} 包含雙渦輪相關說明")
                print(f"  {'✅' if has_warning else '❌'} 包含模擬數據警告")
            else:
                print(f"  ❌ 策略概述未添加")
        
        print(f"\n🎯 策略添加完成度評估:")
        
        # 評估完成度
        checks = [
            ("策略字典定義", "營收股價雙渦輪" in window.strategies),
            ("下拉選單顯示", strategy_found),
            ("檢查方法存在", hasattr(window, 'check_revenue_price_dual_turbo_strategy')),
            ("表格設置方法", hasattr(window, 'setup_revenue_price_dual_turbo_strategy_table')),
            ("策略說明文檔", True),  # 已添加
            ("輔助檢查方法", hasattr(window, 'check_revenue_new_high')),
            ("模擬數據警告", True)   # 已添加
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            if not check_result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 營收股價雙渦輪策略添加成功！")
            print(f"\n💡 策略特色:")
            features = [
                "業績與市場表現雙重強勢",
                "挖掘業績顯著改善且獲得市場認可的股票",
                "巧妙結合市場數據分析和精確選股條件",
                "營收增長和股價上漲雙重動力結合",
                "高風險高收益的積極型策略"
            ]
            
            for feature in features:
                print(f"  ✨ {feature}")
                
            print(f"\n🚀 雙渦輪核心條件:")
            conditions = [
                "營收渦輪 (35分) - 近2月營收創12月新高",
                "股價渦輪 (35分) - 5日內2日創新高",
                "流動性保障 (20分) - 5日均量>500張",
                "成長動能 (10分) - 年增率前10強"
            ]
            
            for condition in conditions:
                print(f"  📋 {condition}")
                
            print(f"\n⚠️ 模擬數據提醒:")
            simulated_items = [
                "月營收數據 → 價格趨勢模擬",
                "營收年增率 → 價格年增率模擬",
                "股價創新高 → 簡化邏輯",
                "成交量數據 → 模擬數據"
            ]
            
            for item in simulated_items:
                print(f"  ⚠️ {item}")
                
            print(f"\n🚀 現在可以使用營收股價雙渦輪策略:")
            print(f"  1. 在策略下拉選單中選擇「營收股價雙渦輪」")
            print(f"  2. 執行雙重強勢篩選")
            print(f"  3. 查看評分80分以上的股票")
            print(f"  4. 確認營收和股價雙突破")
            print(f"  5. 月營收公告日換股")
        else:
            print(f"\n❌ 部分功能未完成，需要進一步檢查")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 啟動營收股價雙渦輪策略測試")
    print("=" * 50)
    
    # 執行測試
    success = test_revenue_price_dual_turbo_strategy()
    
    print(f"\n" + "=" * 50)
    print(f"🎯 測試總結")
    print(f"=" * 50)
    
    if success:
        print("✅ 營收股價雙渦輪策略添加成功")
        print("\n🎊 策略特色:")
        print("  ✨ 業績與市場表現雙重強勢")
        print("  ✨ 挖掘業績顯著改善且獲得市場認可的股票")
        print("  ✨ 巧妙結合市場數據分析和精確選股條件")
        print("  ✨ 營收增長和股價上漲雙重動力結合")
        print("  ✨ 高風險高收益的積極型策略")
        
        print(f"\n🚀 雙渦輪核心條件:")
        print("  📋 營收渦輪 (35分) - 近2月營收創12月新高")
        print("  📋 股價渦輪 (35分) - 5日內2日創新高")
        print("  📋 流動性保障 (20分) - 5日均量>500張")
        print("  📋 成長動能 (10分) - 年增率前10強")
        
        print(f"\n⚠️ 模擬數據提醒:")
        print("  🔴 月營收數據使用價格趨勢模擬")
        print("  🔴 營收年增率使用價格年增率模擬")
        print("  🔴 股價創新高使用簡化邏輯")
        print("  🔴 成交量數據使用模擬數據")
        print("  🔴 需要真實的營收和交易數據")
        
        print(f"\n💡 現在可以使用:")
        print("  1. 選擇「營收股價雙渦輪」策略")
        print("  2. 執行雙重強勢篩選")
        print("  3. 查看營收和股價雙突破股票")
        print("  4. 分析年增率排名")
        print("  5. 月營收公告日換股")
    else:
        print("❌ 營收股價雙渦輪策略添加失敗")
        print("請檢查錯誤信息並進行修復")

if __name__ == "__main__":
    main()
