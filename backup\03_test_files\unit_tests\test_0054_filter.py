#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試0054已下市ETF篩選功能
"""

import sys
import logging

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_0054_filter():
    """測試0054已下市ETF篩選功能"""
    print("🔍 測試0054已下市ETF篩選功能")
    print("=" * 50)
    
    try:
        from stock_filter import StockFilter
        
        # 創建篩選器
        filter = StockFilter()
        
        # 測試ETF清單（包含已下市的0054）
        test_etfs = [
            # 正常ETF
            '0050',    # 元大台灣50 - 有效
            '0051',    # 元大中型100 - 有效
            '0052',    # 富邦科技 - 有效
            '0053',    # 元大電子 - 有效
            '0054',    # 元大台商50 - 已下市，應被排除
            '0055',    # 元大MSCI金融 - 有效
            '0056',    # 元大高股息 - 有效
        ]
        
        print("📊 測試ETF清單:")
        print("-" * 50)
        
        for etf in test_etfs:
            is_valid = filter.is_valid_stock_code(etf)
            status = "✅ 有效" if is_valid else "❌ 無效 (已下市)"
            print(f"  {etf:<6}: {status}")
        
        # 執行批量篩選
        print(f"\n📋 批量篩選測試:")
        print("-" * 50)
        
        valid_etfs = filter.filter_stock_list(test_etfs)
        
        print(f"原始ETF數量: {len(test_etfs)}")
        print(f"有效ETF數量: {len(valid_etfs)}")
        print(f"排除ETF數量: {len(test_etfs) - len(valid_etfs)}")
        print(f"有效ETF清單: {valid_etfs}")
        
        # 檢查0054是否被正確排除
        is_0054_excluded = '0054' not in valid_etfs
        print(f"\n🚫 0054排除檢查:")
        print("-" * 50)
        print(f"0054是否被排除: {'✅ 是' if is_0054_excluded else '❌ 否'}")
        
        if is_0054_excluded:
            print("✅ 0054 (元大台商50) 已被正確識別為已下市ETF並排除")
        else:
            print("❌ 0054 (元大台商50) 仍然出現在有效清單中，需要修正")
        
        # 檢查其他正常ETF是否被保留
        normal_etfs = ['0050', '0051', '0052', '0053', '0055', '0056']
        kept_normal = [etf for etf in normal_etfs if etf in valid_etfs]
        
        print(f"\n✅ 正常ETF保留檢查:")
        print("-" * 50)
        print(f"正常ETF: {normal_etfs}")
        print(f"成功保留: {kept_normal}")
        print(f"保留率: {len(kept_normal)}/{len(normal_etfs)} ({len(kept_normal)/len(normal_etfs)*100:.1f}%)")
        
        # 檢查已下市股票清單
        delisted_list = filter.get_delisted_stocks()
        print(f"\n🚫 已下市股票/ETF清單:")
        print("-" * 50)
        print(f"已下市數量: {len(delisted_list)}")
        print(f"包含0054: {'✅ 是' if '0054' in delisted_list else '❌ 否'}")
        print(f"已下市清單: {delisted_list}")
        
        # 判斷測試結果
        success = (
            is_0054_excluded and                                    # 0054被正確排除
            len(kept_normal) == len(normal_etfs) and               # 所有正常ETF都被保留
            '0054' in delisted_list                                # 0054在已下市清單中
        )
        
        if success:
            print(f"\n🎉 測試通過！0054已下市ETF篩選功能正常工作。")
            return True
        else:
            print(f"\n⚠️ 測試未完全通過，請檢查篩選邏輯。")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_etf_display_list():
    """測試ETF顯示清單"""
    print("\n🔍 測試ETF顯示清單")
    print("=" * 50)
    
    try:
        # 模擬從截圖看到的ETF清單
        etf_list = [
            '0050',  # 元大台灣50 (上市)
            '0051',  # 元大中型100 (上市)
            '0052',  # 富邦科技 (上市)
            '0053',  # 元大電子 (上市)
            '0054',  # 元大台商50 (已下市/ETF) - 應被排除
            '0055',  # 元大MSCI金融 (上市)
            '0056',  # 元大高股息 (上市)
        ]
        
        from stock_filter import StockFilter
        filter = StockFilter()
        
        print("📊 ETF清單篩選結果:")
        print("-" * 50)
        
        valid_etfs = []
        invalid_etfs = []
        
        for etf in etf_list:
            is_valid = filter.is_valid_stock_code(etf)
            if is_valid:
                valid_etfs.append(etf)
                print(f"  {etf}: ✅ 保留")
            else:
                invalid_etfs.append(etf)
                print(f"  {etf}: ❌ 排除（已下市）")
        
        print(f"\n📈 篩選統計:")
        print(f"原始ETF: {len(etf_list)} 支")
        print(f"保留ETF: {len(valid_etfs)} 支")
        print(f"排除ETF: {len(invalid_etfs)} 支")
        print(f"排除率: {len(invalid_etfs)/len(etf_list)*100:.1f}%")
        
        print(f"\n✅ 修正效果：")
        print("• 0054 (元大台商50) 已被識別為已下市ETF")
        print("• 該ETF將不會出現在「全部股票」清單中")
        print("• 用戶將看到更準確的ETF清單")
        
        return len(invalid_etfs) == 1 and '0054' in invalid_etfs
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🚀 0054已下市ETF篩選功能測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("0054篩選功能", test_0054_filter()))
    test_results.append(("ETF顯示清單", test_etf_display_list()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！0054已下市ETF篩選功能已成功修正。")
        print("\n📋 修正說明:")
        print("• 0054 (元大台商50) 已添加到已下市股票黑名單")
        print("• 該ETF將自動從股票清單中排除")
        print("• 確保用戶只看到有效的ETF投資標的")
        print("• 提升系統資料品質和用戶體驗")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能的實現。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
