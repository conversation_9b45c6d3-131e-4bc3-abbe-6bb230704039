#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的增強版除權息功能
"""

import os
import sys
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_enhanced_dividend_crawler():
    """測試修正後的增強版除權息爬蟲"""
    print("🧪 測試修正後的增強版除權息功能")
    print("=" * 50)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        # 創建爬蟲實例
        crawler = EnhancedDividendCrawler()
        print("✅ 爬蟲實例創建成功")
        
        # 測試資料庫中現有資料
        print("\n📊 檢查資料庫中的現有資料...")
        
        # 檢查2024年資料
        existing_2024 = crawler.get_dividend_data_by_year(2024)
        print(f"📋 2024年資料: {len(existing_2024)} 筆")
        
        # 檢查2025年資料
        existing_2025 = crawler.get_dividend_data_by_year(2025)
        print(f"📋 2025年資料: {len(existing_2025)} 筆")
        
        # 顯示一些樣本資料
        if existing_2024:
            print("\n📋 2024年資料樣本:")
            for i, record in enumerate(existing_2024[:5]):
                ex_date = record.get('ex_dividend_date', '無日期')
                print(f"  {i+1}. {record['stock_code']} {record['stock_name']}: 現金股利={record['cash_dividend']}, 除權息日期={ex_date}")
        
        if existing_2025:
            print("\n📋 2025年資料樣本:")
            for i, record in enumerate(existing_2025[:5]):
                ex_date = record.get('ex_dividend_date', '無日期')
                print(f"  {i+1}. {record['stock_code']} {record['stock_name']}: 現金股利={record['cash_dividend']}, 除權息日期={ex_date}")
        
        # 測試獲取功能（使用現有資料）
        print("\n🚀 測試除權息資料獲取功能...")
        result_2024 = crawler.fetch_all_dividend_data(2024)
        print(f"✅ 2024年獲取結果: {len(result_2024)} 筆資料")
        
        result_2025 = crawler.fetch_all_dividend_data(2025)
        print(f"✅ 2025年獲取結果: {len(result_2025)} 筆資料")
        
        # 測試匯出功能
        print("\n📁 測試CSV匯出功能...")
        if result_2024:
            csv_file = crawler.export_to_csv(2024)
            if csv_file and os.path.exists(csv_file):
                print(f"✅ 2024年CSV檔案已匯出: {csv_file}")
                print(f"📊 檔案大小: {os.path.getsize(csv_file)} bytes")
            else:
                print("⚠️ CSV匯出失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_specific_stock_queries():
    """測試特定股票查詢功能"""
    print("\n🎯 測試特定股票查詢功能")
    print("=" * 40)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        crawler = EnhancedDividendCrawler()
        
        # 測試知名股票
        test_stocks = [
            ('2330', '台積電'),
            ('2317', '鴻海'),
            ('2454', '聯發科'),
            ('2412', '中華電'),
            ('2881', '富邦金')
        ]
        
        for stock_code, stock_name in test_stocks:
            print(f"\n📊 查詢 {stock_code} {stock_name} 的歷史除權息資料...")
            history = crawler.get_stock_dividend_history(stock_code, years=3)
            
            if history:
                print(f"✅ 找到 {len(history)} 筆歷史資料:")
                for record in history:
                    ex_date = record.get('ex_dividend_date', '無日期')
                    cash_div = record['cash_dividend']
                    stock_div = record['stock_dividend']
                    print(f"  • {record['year']}: 現金股利={cash_div}, 股票股利={stock_div}, 除權息日期={ex_date}")
            else:
                print(f"⚠️ 未找到 {stock_code} 的歷史資料")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定股票查詢測試失敗: {e}")
        return False

def test_csv_import_functionality():
    """測試CSV匯入功能"""
    print("\n📁 測試CSV匯入功能")
    print("=" * 30)
    
    try:
        from enhanced_dividend_crawler import EnhancedDividendCrawler
        
        crawler = EnhancedDividendCrawler()
        
        # 創建測試CSV檔案
        test_csv_content = """stock_code,stock_name,year,ex_dividend_date,cash_dividend,stock_dividend
2330,台積電,2023,2023/06/15,2.75,0
2317,鴻海,2023,2023/07/20,4.2,0
2454,聯發科,2023,2023/06/30,50.0,0
2412,中華電,2023,2023/07/10,4.5,0"""
        
        test_csv_file = "test_dividend_import.csv"
        
        try:
            with open(test_csv_file, 'w', encoding='utf-8-sig') as f:
                f.write(test_csv_content)
            
            print(f"✅ 測試CSV檔案已創建: {test_csv_file}")
            
            # 測試匯入功能
            print("📥 測試CSV匯入...")
            import_result = crawler.import_from_csv(test_csv_file, year=2023)
            
            if import_result:
                print("✅ CSV匯入成功")
                
                # 驗證匯入的資料
                imported_data = crawler.get_dividend_data_by_year(2023)
                csv_imported = [r for r in imported_data if r['data_source'] == 'csv_import']
                print(f"📊 匯入的資料: {len(csv_imported)} 筆")
                
                for record in csv_imported:
                    print(f"  • {record['stock_code']} {record['stock_name']}: {record['cash_dividend']}")
            else:
                print("❌ CSV匯入失敗")
            
            return import_result
            
        finally:
            # 清理測試檔案
            if os.path.exists(test_csv_file):
                os.remove(test_csv_file)
                print(f"🗑️ 已清理測試檔案: {test_csv_file}")
        
    except Exception as e:
        print(f"❌ CSV匯入測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🔧 修正後的增強版除權息功能測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 執行測試
    test_results = []
    
    print("1️⃣ 測試基本功能")
    result1 = test_enhanced_dividend_crawler()
    test_results.append(("基本功能測試", result1))
    
    print("\n2️⃣ 測試股票查詢功能")
    result2 = test_specific_stock_queries()
    test_results.append(("股票查詢測試", result2))
    
    print("\n3️⃣ 測試CSV匯入功能")
    result3 = test_csv_import_functionality()
    test_results.append(("CSV匯入測試", result3))
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結")
    print("=" * 30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(test_results)} 項測試通過")
    
    if passed == len(test_results):
        print("🎉 所有測試通過！修正後的除權息功能正常運作")
        print("💡 現在可以使用以下功能:")
        print("   • 查詢資料庫中的現有除權息資料")
        print("   • 匯出CSV檔案")
        print("   • 匯入外部CSV資料")
        print("   • 查詢特定股票的歷史資料")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🚀 測試成功！修正後的除權息功能可以正常使用")
        print("💡 雖然GoodInfo爬蟲暫時無法使用，但現有功能已足夠實用")
    else:
        print("🔧 請根據錯誤訊息修正問題後重新測試")
    
    input("\n按 Enter 鍵結束...")
