# 💾 監控股票記憶功能使用指南

## 📋 功能概述

監控股票記憶功能讓您的台股智能選股系統能夠記住上次設置的監控股票列表，重新啟動程式後自動恢復，無需重複設置。

## 🎯 核心特性

### ✨ 自動記憶
- **無感知保存**: 修改監控股票時自動保存到配置文件
- **即時生效**: 配置變更立即生效，重啟後自動載入
- **智能合併**: 匯入策略結果時智能合併現有股票，避免重複

### 🔄 持久化存儲
- **配置文件**: 使用 `app_config.json` 存儲監控設置
- **時間戳**: 記錄最後更新時間，便於追蹤變更
- **錯誤容忍**: 配置錯誤時使用預設值，不影響程式運行

## 🚀 使用方法

### 1. 基本使用

#### 📝 設置監控股票
1. 在「盤中監控」標籤頁找到監控股票輸入框
2. 輸入要監控的股票代碼，用逗號分隔（例：`2330,2317,2454`）
3. 系統自動保存您的設置

#### 🔄 程式重啟恢復
1. 關閉程式
2. 重新啟動程式
3. 監控股票列表自動恢復到上次設置的狀態

### 2. 進階功能

#### 📥 策略結果匯入
1. 執行任何策略選股
2. 點擊「📥 匯入策略結果」按鈕
3. 新股票自動合併到現有監控列表
4. 系統自動保存更新後的列表

#### ⚙️ 股票管理
1. 點擊「⚙️ 管理股票」按鈕
2. 在對話框中添加或移除股票
3. 點擊「確定」後自動保存變更

#### 🗑️ 移除股票
1. 在監控表格中找到要移除的股票
2. 點擊該行的「🗑️」按鈕
3. 確認移除後自動保存更新

#### 🧹 清空列表
1. 點擊「🧹 清空列表」按鈕
2. 確認清空後自動保存空狀態

## 📊 配置文件結構

### 配置位置
```
app_config.json
```

### 監控配置部分
```json
{
  "monitor": {
    "stocks": "2330,2317,2454,3008,2412",
    "update_interval": 30,
    "auto_start": false,
    "last_updated": "2025-07-11 09:57:54"
  }
}
```

### 配置說明
| 欄位 | 說明 | 範例 |
|------|------|------|
| `stocks` | 監控股票列表 | `"2330,2317,2454"` |
| `update_interval` | 更新間隔（秒） | `30` |
| `auto_start` | 自動開始監控 | `false` |
| `last_updated` | 最後更新時間 | `"2025-07-11 09:57:54"` |

## 🔄 自動保存觸發場景

### 1. 📝 文本變更事件
- **觸發**: 用戶在輸入框中修改股票代碼
- **動作**: 自動保存到配置文件
- **範例**: 輸入 `2330,2317` → 自動保存

### 2. 📥 匯入策略結果
- **觸發**: 從策略結果匯入股票
- **動作**: 合併股票後自動保存
- **範例**: 匯入阿水一式結果 → 自動保存新列表

### 3. 🧹 清空股票列表
- **觸發**: 點擊清空按鈕
- **動作**: 自動保存空狀態
- **範例**: 清空列表 → 自動保存空狀態

### 4. ⚙️ 股票管理對話框
- **觸發**: 通過管理對話框修改
- **動作**: 確認後自動保存
- **範例**: 添加/移除股票 → 自動保存

### 5. 🗑️ 移除單個股票
- **觸發**: 從監控表格移除股票
- **動作**: 移除後自動保存
- **範例**: 點擊移除按鈕 → 自動保存

## 🛡️ 錯誤處理

### 常見情況處理

#### 📁 配置文件不存在
- **處理**: 使用預設股票列表，創建新配置文件
- **結果**: 程式正常啟動，顯示預設監控股票

#### 💥 配置文件損壞
- **處理**: 載入預設配置，記錄錯誤日誌
- **結果**: 程式正常啟動，使用預設設置

#### 🔒 保存權限不足
- **處理**: 記錄錯誤日誌，繼續運行
- **結果**: 功能正常，但配置不會持久化

#### ❌ 無效股票代碼
- **處理**: 保存原始輸入，由監控模組處理
- **結果**: 保存用戶輸入，監控時過濾無效代碼

## 💡 使用技巧

### 🎯 最佳實踐

1. **定期檢查**: 定期檢查監控股票列表是否符合需求
2. **策略結合**: 結合策略選股結果更新監控列表
3. **分類管理**: 可以根據不同策略或主題分組管理股票
4. **備份配置**: 重要設置可以手動備份配置文件

### ⚡ 效率提升

1. **批量操作**: 使用股票管理對話框進行批量添加/移除
2. **策略匯入**: 利用策略匯入功能快速更新監控列表
3. **智能合併**: 讓系統自動處理重複股票，保持列表整潔

## 🔍 故障排除

### 問題診斷

#### 監控股票沒有保存
1. 檢查程式是否有寫入權限
2. 查看日誌中的錯誤信息
3. 確認配置文件路徑正確

#### 重啟後監控列表消失
1. 檢查 `app_config.json` 文件是否存在
2. 確認配置文件格式正確
3. 查看程式啟動日誌

#### 配置文件損壞
1. 刪除 `app_config.json` 文件
2. 重新啟動程式
3. 系統會自動創建新的配置文件

### 手動修復

如果需要手動修復配置：

1. **備份現有配置**
   ```bash
   copy app_config.json app_config_backup.json
   ```

2. **重置配置**
   ```bash
   del app_config.json
   ```

3. **重新啟動程式**，系統會自動創建新配置

## 📊 功能優勢

### 🎨 用戶體驗
- ✅ **無感知操作**: 自動保存，無需手動操作
- ✅ **即時生效**: 設置立即生效，重啟自動恢復
- ✅ **智能處理**: 自動合併和去重，保持列表整潔

### 🔧 技術特性
- ✅ **持久化存儲**: 配置永久保存，不會丟失
- ✅ **錯誤容忍**: 完善的錯誤處理機制
- ✅ **性能優化**: 高效的配置讀寫操作

### 📈 實用價值
- ✅ **提升效率**: 避免重複設置，節省時間
- ✅ **保持連續性**: 監控設置在會話間保持一致
- ✅ **降低出錯**: 減少手動設置錯誤的可能性

## 🎉 總結

監控股票記憶功能為您提供：

1. **🎯 完整的記憶能力** - 記住所有監控設置
2. **💾 自動保存機制** - 無需手動操作
3. **🔄 無縫恢復體驗** - 重啟後自動載入
4. **🛡️ 可靠的錯誤處理** - 確保程式穩定運行
5. **✨ 優化的用戶體驗** - 智能、便捷、高效

現在您可以放心設置監控股票，系統會自動記住您的選擇，讓您專注於交易決策而不是重複設置！

---

**💡 提示**: 如有任何問題，請查看程式日誌或聯繫技術支援。
