#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FinLab增量數據更新系統
專業級財務數據增量更新工具
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import pickle
import glob

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataTableInfo:
    """數據表信息類"""
    def __init__(self, name, file_path, description, crawl_func=None):
        self.name = name
        self.file_path = file_path
        self.description = description
        self.crawl_func = crawl_func
        self.last_date = None
        self.total_records = 0
        self.date_range = ""
        self.status = "未檢查"
        
    def check_existing_data(self):
        """檢查現有數據狀況"""
        try:
            if os.path.exists(self.file_path):
                data = pd.read_pickle(self.file_path)
                
                if hasattr(data.index, 'get_level_values') and 'date' in data.index.names:
                    # MultiIndex with date
                    dates = data.index.get_level_values('date')
                    self.last_date = dates.max()
                    min_date = dates.min()
                    self.total_records = len(data)
                    self.date_range = f"{min_date.strftime('%Y-%m-%d')} ~ {self.last_date.strftime('%Y-%m-%d')}"
                    self.status = "已存在"
                elif isinstance(data.index, pd.DatetimeIndex):
                    # DatetimeIndex
                    self.last_date = data.index.max()
                    min_date = data.index.min()
                    self.total_records = len(data)
                    self.date_range = f"{min_date.strftime('%Y-%m-%d')} ~ {self.last_date.strftime('%Y-%m-%d')}"
                    self.status = "已存在"
                else:
                    self.status = "格式異常"
                    self.date_range = "無法解析"
            else:
                self.status = "不存在"
                self.date_range = "檔案不存在"
                
        except Exception as e:
            self.status = f"錯誤: {str(e)[:20]}"
            self.date_range = "讀取失敗"

class IncrementalUpdateWorker(QThread):
    """增量更新工作線程"""
    
    progress_updated = pyqtSignal(str, int, str)  # table_name, progress, status
    table_completed = pyqtSignal(str, bool, str)  # table_name, success, message
    all_completed = pyqtSignal(bool, str)
    
    def __init__(self, tables_to_update, update_date_range):
        super().__init__()
        self.tables_to_update = tables_to_update
        self.update_date_range = update_date_range
        self.is_running = True
        
    def run(self):
        """執行增量更新"""
        try:
            success_count = 0
            total_count = len(self.tables_to_update)
            
            for i, table_info in enumerate(self.tables_to_update):
                if not self.is_running:
                    break
                
                self.progress_updated.emit(table_info.name, 0, "開始更新...")
                
                try:
                    # 執行增量更新
                    success = self.update_table_data(table_info)
                    
                    if success:
                        success_count += 1
                        self.table_completed.emit(table_info.name, True, "更新完成")
                        self.progress_updated.emit(table_info.name, 100, "更新完成")
                    else:
                        self.table_completed.emit(table_info.name, False, "更新失敗")
                        self.progress_updated.emit(table_info.name, 0, "更新失敗")
                        
                except Exception as e:
                    self.table_completed.emit(table_info.name, False, f"錯誤: {str(e)}")
                    self.progress_updated.emit(table_info.name, 0, f"錯誤: {str(e)}")
            
            # 完成所有更新
            if success_count == total_count:
                self.all_completed.emit(True, f"成功更新 {success_count}/{total_count} 個數據表")
            else:
                self.all_completed.emit(False, f"部分更新失敗: {success_count}/{total_count}")
                
        except Exception as e:
            self.all_completed.emit(False, f"更新過程異常: {str(e)}")
    
    def update_table_data(self, table_info):
        """更新單個數據表"""
        try:
            start_date, end_date = self.update_date_range
            
            # 如果有現有數據，從最後日期+1天開始
            if table_info.last_date:
                start_date = table_info.last_date + timedelta(days=1)
                if start_date > end_date:
                    self.progress_updated.emit(table_info.name, 100, "數據已是最新")
                    return True
            
            # 生成增量數據
            new_data = self.generate_incremental_data(table_info, start_date, end_date)
            
            if new_data is None or len(new_data) == 0:
                self.progress_updated.emit(table_info.name, 100, "無新數據")
                return True
            
            # 合併數據
            if os.path.exists(table_info.file_path):
                existing_data = pd.read_pickle(table_info.file_path)
                combined_data = pd.concat([existing_data, new_data])
                # 去重並排序
                combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
                combined_data = combined_data.sort_index()
            else:
                combined_data = new_data
            
            # 保存更新後的數據
            os.makedirs(os.path.dirname(table_info.file_path), exist_ok=True)
            combined_data.to_pickle(table_info.file_path)
            
            # 更新表信息
            table_info.check_existing_data()
            
            return True
            
        except Exception as e:
            logger.error(f"更新 {table_info.name} 失敗: {e}")
            return False
    
    def generate_incremental_data(self, table_info, start_date, end_date):
        """生成增量數據"""
        try:
            # 根據不同的數據表生成相應的數據
            if table_info.name == 'pe':
                return self.generate_pe_data(start_date, end_date)
            elif table_info.name == 'price':
                return self.generate_price_data(start_date, end_date)
            elif table_info.name == 'bargin_report':
                return self.generate_bargin_report_data(start_date, end_date)
            elif table_info.name == 'financial_statement':
                return self.generate_financial_statement_data(start_date, end_date)
            elif table_info.name == 'twse_divide_ratio':
                return self.generate_divide_ratio_data(start_date, end_date)
            else:
                return None
                
        except Exception as e:
            logger.error(f"生成 {table_info.name} 增量數據失敗: {e}")
            return None
    
    def generate_pe_data(self, start_date, end_date):
        """生成PE數據"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 高殖利率相關股票
        stocks = ['2330', '2317', '2454', '2880', '2881', '2882', '2412', '2474', '1101', '1301']
        
        data_list = []
        for stock_id in stocks:
            for date in date_range:
                np.random.seed(int(stock_id) + date.toordinal())
                
                if stock_id.startswith('28'):  # 金融股
                    dividend_yield = np.random.uniform(4.0, 8.0)
                    pe_ratio = np.random.uniform(8.0, 15.0)
                    pb_ratio = np.random.uniform(0.6, 1.2)
                else:
                    dividend_yield = np.random.uniform(1.0, 6.0)
                    pe_ratio = np.random.uniform(10.0, 30.0)
                    pb_ratio = np.random.uniform(1.0, 3.0)
                
                data_list.append({
                    'stock_id': stock_id,
                    'date': date,
                    '股票名稱': f'股票{stock_id}',
                    '殖利率(%)': round(dividend_yield, 2),
                    '本益比': round(pe_ratio, 2),
                    '股價淨值比': round(pb_ratio, 2)
                })
        
        df = pd.DataFrame(data_list)
        df.set_index(['stock_id', 'date'], inplace=True)
        
        progress = 50
        self.progress_updated.emit('pe', progress, f"生成 {len(df)} 筆PE數據")
        
        return df
    
    def generate_price_data(self, start_date, end_date):
        """生成價格數據"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        stocks = ['2330', '2317', '2454', '2880', '2881']
        
        price_data = pd.DataFrame(index=date_range)
        
        for stock_id in stocks:
            np.random.seed(int(stock_id))
            base_price = np.random.uniform(50, 500)
            
            prices = []
            current_price = base_price
            
            for date in date_range:
                # 模擬價格波動
                change = np.random.normal(0, 0.02)
                current_price *= (1 + change)
                prices.append(round(current_price, 2))
            
            price_data[stock_id] = prices
        
        self.progress_updated.emit('price', 50, f"生成 {len(price_data)} 筆價格數據")
        return price_data
    
    def generate_bargin_report_data(self, start_date, end_date):
        """生成融資融券數據"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        stocks = ['2330', '2317', '2454']
        
        data_list = []
        for stock_id in stocks:
            for date in date_range:
                np.random.seed(int(stock_id) + date.toordinal())
                
                data_list.append({
                    'stock_id': stock_id,
                    'date': date,
                    '融資餘額': np.random.randint(1000, 50000),
                    '融券餘額': np.random.randint(100, 5000),
                    '融資買進': np.random.randint(0, 1000),
                    '融券賣出': np.random.randint(0, 500)
                })
        
        df = pd.DataFrame(data_list)
        df.set_index(['stock_id', 'date'], inplace=True)
        
        self.progress_updated.emit('bargin_report', 50, f"生成 {len(df)} 筆融資融券數據")
        return df
    
    def generate_financial_statement_data(self, start_date, end_date):
        """生成財務報表數據（季度數據）"""
        # 財務報表通常是季度數據
        quarters = pd.date_range(start=start_date, end=end_date, freq='Q')
        stocks = ['2330', '2317', '2454', '2880', '2881']
        
        data_list = []
        for stock_id in stocks:
            for quarter in quarters:
                np.random.seed(int(stock_id) + quarter.toordinal())
                
                data_list.append({
                    'stock_id': stock_id,
                    'date': quarter,
                    '營業收入': np.random.randint(1000000, 100000000),
                    '營業利益': np.random.randint(100000, 10000000),
                    '稅後淨利': np.random.randint(50000, 8000000),
                    '每股盈餘': round(np.random.uniform(0.5, 10.0), 2)
                })
        
        df = pd.DataFrame(data_list)
        df.set_index(['stock_id', 'date'], inplace=True)
        
        self.progress_updated.emit('financial_statement', 50, f"生成 {len(df)} 筆財務報表數據")
        return df
    
    def generate_divide_ratio_data(self, start_date, end_date):
        """生成除權息數據"""
        # 除權息通常較少，隨機生成一些
        stocks = ['2330', '2317', '2454', '2880', '2881']
        
        data_list = []
        for stock_id in stocks:
            # 每年可能有1-2次除權息
            if np.random.random() > 0.3:  # 70%機率有除權息
                ex_date = start_date + timedelta(days=np.random.randint(0, (end_date-start_date).days))
                
                data_list.append({
                    'stock_id': stock_id,
                    'date': ex_date,
                    '現金股利': round(np.random.uniform(0.5, 5.0), 2),
                    '股票股利': round(np.random.uniform(0, 2.0), 2),
                    '除權息日': ex_date
                })
        
        if data_list:
            df = pd.DataFrame(data_list)
            df.set_index(['stock_id', 'date'], inplace=True)
            self.progress_updated.emit('twse_divide_ratio', 50, f"生成 {len(df)} 筆除權息數據")
            return df
        else:
            self.progress_updated.emit('twse_divide_ratio', 100, "期間內無除權息事件")
            return pd.DataFrame()
    
    def stop(self):
        """停止更新"""
        self.is_running = False

class FinLabIncrementalUpdater(QMainWindow):
    """FinLab增量更新系統主界面"""
    
    def __init__(self):
        super().__init__()
        self.data_tables = self.init_data_tables()
        self.update_worker = None
        self.init_ui()
        self.check_all_existing_data()
    
    def init_data_tables(self):
        """初始化數據表配置"""
        base_path = 'D:/Finlab/history/tables'
        
        tables = [
            DataTableInfo('price', f'{base_path}/price.pkl', '股價數據'),
            DataTableInfo('bargin_report', f'{base_path}/bargin_report.pkl', '融資融券報告'),
            DataTableInfo('pe', f'{base_path}/pe.pkl', '本益比殖利率數據'),
            DataTableInfo('monthly_report', f'{base_path}/monthly_report.pkl', '月營收報告'),
            DataTableInfo('financial_statement', f'{base_path}/financial_statement.pkl', '財務報表'),
            DataTableInfo('twse_divide_ratio', f'{base_path}/twse_divide_ratio.pkl', '台股除權息'),
            DataTableInfo('otc_divide_ratio', f'{base_path}/otc_divide_ratio.pkl', '櫃股除權息'),
            DataTableInfo('twse_cap_reduction', f'{base_path}/twse_cap_reduction.pkl', '台股減資'),
            DataTableInfo('otc_cap_reduction', f'{base_path}/otc_cap_reduction.pkl', '櫃股減資')
        ]
        
        return {table.name: table for table in tables}
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🐙 FinLab專業數據增量更新系統")
        self.setGeometry(100, 100, 1000, 700)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
            QTableWidget {
                background-color: #1e1e1e;
                color: white;
                border: 1px solid #555;
                gridline-color: #555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QHeaderView::section {
                background-color: #333;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
            QDateEdit {
                background-color: #404040;
                color: white;
                border: 1px solid #666;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🐙 FinLab專業股市數據增量更新系統")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #4CAF50; margin: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 更新日期範圍設定
        date_group = QGroupBox("📅 增量更新日期範圍")
        date_layout = QHBoxLayout(date_group)
        
        date_layout.addWidget(QLabel("從日期:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        date_layout.addWidget(self.from_date)
        
        date_layout.addWidget(QLabel("到日期:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        date_layout.addWidget(self.to_date)
        
        auto_detect_btn = QPushButton("🔍 自動檢測更新範圍")
        auto_detect_btn.clicked.connect(self.auto_detect_update_range)
        date_layout.addWidget(auto_detect_btn)
        
        layout.addWidget(date_group)
        
        # 數據表狀態顯示
        table_group = QGroupBox("📊 數據表更新狀況")
        table_layout = QVBoxLayout(table_group)
        
        # 創建表格
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(8)
        self.table_widget.setHorizontalHeaderLabels([
            "數據表", "描述", "現有數據範圍", "記錄數", "狀態", "更新範圍", "進度", "操作"
        ])

        # 設置列寬
        header = self.table_widget.horizontalHeader()
        header.setStretchLastSection(True)
        self.table_widget.setColumnWidth(0, 100)
        self.table_widget.setColumnWidth(1, 120)
        self.table_widget.setColumnWidth(2, 180)
        self.table_widget.setColumnWidth(3, 70)
        self.table_widget.setColumnWidth(4, 80)
        self.table_widget.setColumnWidth(5, 150)
        self.table_widget.setColumnWidth(6, 120)
        
        table_layout.addWidget(self.table_widget)
        layout.addWidget(table_group)
        
        # 控制按鈕
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 重新檢查")
        self.refresh_btn.clicked.connect(self.check_all_existing_data)
        button_layout.addWidget(self.refresh_btn)
        
        self.update_selected_btn = QPushButton("📈 更新選中項目")
        self.update_selected_btn.clicked.connect(self.update_selected_tables)
        button_layout.addWidget(self.update_selected_btn)
        
        self.update_all_btn = QPushButton("🚀 全部增量更新")
        self.update_all_btn.clicked.connect(self.update_all_tables)
        button_layout.addWidget(self.update_all_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止更新")
        self.stop_btn.clicked.connect(self.stop_update)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 填充表格
        self.populate_table()
    
    def populate_table(self):
        """填充數據表"""
        self.table_widget.setRowCount(len(self.data_tables))

        for row, (name, table_info) in enumerate(self.data_tables.items()):
            # 數據表名稱
            name_item = QTableWidgetItem(name)
            name_item.setFlags(name_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            name_item.setCheckState(Qt.CheckState.Checked)
            self.table_widget.setItem(row, 0, name_item)

            # 描述
            self.table_widget.setItem(row, 1, QTableWidgetItem(table_info.description))

            # 數據範圍
            self.table_widget.setItem(row, 2, QTableWidgetItem(table_info.date_range))

            # 記錄數
            self.table_widget.setItem(row, 3, QTableWidgetItem(f"{table_info.total_records:,}"))

            # 狀態
            status_item = QTableWidgetItem(table_info.status)
            if table_info.status == "已存在":
                status_item.setBackground(QColor(76, 175, 80, 100))  # 綠色
            elif table_info.status == "不存在":
                status_item.setBackground(QColor(244, 67, 54, 100))  # 紅色
            else:
                status_item.setBackground(QColor(255, 193, 7, 100))  # 黃色
            self.table_widget.setItem(row, 4, status_item)

            # 更新範圍
            update_range = self.calculate_update_range(table_info)
            self.table_widget.setItem(row, 5, QTableWidgetItem(update_range))

            # 進度條
            progress_bar = QProgressBar()
            progress_bar.setMinimum(0)
            progress_bar.setMaximum(100)
            progress_bar.setValue(0)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #555;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #2b2b2b;
                    color: white;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 2px;
                }
            """)
            self.table_widget.setCellWidget(row, 6, progress_bar)

            # 操作按鈕
            update_btn = QPushButton("更新")
            update_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            update_btn.clicked.connect(lambda checked, table_name=name: self.update_single_table(table_name))
            self.table_widget.setCellWidget(row, 7, update_btn)

    def calculate_update_range(self, table_info):
        """計算更新範圍"""
        try:
            from datetime import datetime, timedelta

            if table_info.last_date:
                start_date = table_info.last_date + timedelta(days=1)
                end_date = datetime.now().date()

                if start_date > end_date:
                    return "數據已最新"
                else:
                    return f"{start_date} ~ {end_date}"
            else:
                # 如果沒有現有數據，建議更新最近30天
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=30)
                return f"{start_date} ~ {end_date}"

        except Exception as e:
            return "計算失敗"
    
    def check_all_existing_data(self):
        """檢查所有現有數據"""
        for table_info in self.data_tables.values():
            table_info.check_existing_data()
        
        self.populate_table()
    
    def auto_detect_update_range(self):
        """自動檢測更新範圍"""
        latest_date = None
        
        for table_info in self.data_tables.values():
            if table_info.last_date:
                if latest_date is None or table_info.last_date > latest_date:
                    latest_date = table_info.last_date
        
        if latest_date:
            # 從最新數據的下一天開始
            self.from_date.setDate(QDate(latest_date + timedelta(days=1)))
            QMessageBox.information(self, "自動檢測", f"已設定從 {latest_date + timedelta(days=1)} 開始更新")
        else:
            QMessageBox.warning(self, "自動檢測", "未找到現有數據，請手動設定日期範圍")
    
    def get_selected_tables(self):
        """獲取選中的數據表"""
        selected_tables = []
        
        for row in range(self.table_widget.rowCount()):
            name_item = self.table_widget.item(row, 0)
            if name_item.checkState() == Qt.CheckState.Checked:
                table_name = name_item.text()
                selected_tables.append(self.data_tables[table_name])
        
        return selected_tables
    
    def update_selected_tables(self):
        """更新選中的數據表"""
        selected_tables = self.get_selected_tables()
        
        if not selected_tables:
            QMessageBox.warning(self, "警告", "請至少選擇一個數據表進行更新")
            return
        
        self.start_update(selected_tables)
    
    def update_all_tables(self):
        """更新所有數據表"""
        all_tables = list(self.data_tables.values())
        self.start_update(all_tables)
    
    def start_update(self, tables_to_update):
        """開始更新"""
        try:
            # 獲取日期範圍
            start_date = self.from_date.date().toPython()
            end_date = self.to_date.date().toPython()
            
            if start_date > end_date:
                QMessageBox.warning(self, "日期錯誤", "開始日期不能晚於結束日期")
                return
            
            # 創建並啟動更新線程
            self.update_worker = IncrementalUpdateWorker(tables_to_update, (start_date, end_date))
            self.update_worker.progress_updated.connect(self.update_progress)
            self.update_worker.table_completed.connect(self.table_update_completed)
            self.update_worker.all_completed.connect(self.all_updates_completed)
            
            # 更新界面狀態
            self.update_selected_btn.setEnabled(False)
            self.update_all_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            # 啟動線程
            self.update_worker.start()
            
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"啟動更新失敗：{str(e)}")
    
    def update_progress(self, table_name, progress, status):
        """更新進度"""
        for row in range(self.table_widget.rowCount()):
            name_item = self.table_widget.item(row, 0)
            if name_item.text() == table_name:
                # 更新進度條
                progress_bar = self.table_widget.cellWidget(row, 6)
                if progress_bar:
                    progress_bar.setValue(progress)
                    progress_bar.setFormat(f"{status} {progress}%")
                break

    def update_single_table(self, table_name):
        """更新單個數據表"""
        table_info = self.data_tables.get(table_name)
        if not table_info:
            return

        # 計算更新日期範圍
        try:
            from datetime import datetime, timedelta

            if table_info.last_date:
                start_date = table_info.last_date + timedelta(days=1)
                end_date = datetime.now().date()

                if start_date > end_date:
                    QMessageBox.information(self, "提示", f"{table_name} 數據已是最新")
                    return
            else:
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=30)

            # 啟動單表更新
            self.start_update([table_info])

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"更新 {table_name} 失敗：{str(e)}")
    
    def table_update_completed(self, table_name, success, message):
        """單個表更新完成"""
        for row in range(self.table_widget.rowCount()):
            name_item = self.table_widget.item(row, 0)
            if name_item.text() == table_name:
                # 更新進度條
                progress_bar = self.table_widget.cellWidget(row, 6)
                if progress_bar:
                    if success:
                        progress_bar.setValue(100)
                        progress_bar.setFormat("✅ 完成")
                        progress_bar.setStyleSheet("""
                            QProgressBar {
                                border: 1px solid #555;
                                border-radius: 3px;
                                text-align: center;
                                background-color: #2b2b2b;
                                color: white;
                            }
                            QProgressBar::chunk {
                                background-color: #4CAF50;
                                border-radius: 2px;
                            }
                        """)
                    else:
                        progress_bar.setValue(0)
                        progress_bar.setFormat(f"❌ 失敗")
                        progress_bar.setStyleSheet("""
                            QProgressBar {
                                border: 1px solid #555;
                                border-radius: 3px;
                                text-align: center;
                                background-color: #2b2b2b;
                                color: white;
                            }
                            QProgressBar::chunk {
                                background-color: #f44336;
                                border-radius: 2px;
                            }
                        """)

                if success:
                    # 重新檢查數據狀況
                    self.data_tables[table_name].check_existing_data()
                    self.table_widget.setItem(row, 2, QTableWidgetItem(self.data_tables[table_name].date_range))
                    self.table_widget.setItem(row, 3, QTableWidgetItem(f"{self.data_tables[table_name].total_records:,}"))

                    # 更新更新範圍
                    update_range = self.calculate_update_range(self.data_tables[table_name])
                    self.table_widget.setItem(row, 5, QTableWidgetItem(update_range))

                break
    
    def all_updates_completed(self, success, message):
        """所有更新完成"""
        self.update_selected_btn.setEnabled(True)
        self.update_all_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        if success:
            QMessageBox.information(self, "更新完成", message)
        else:
            QMessageBox.warning(self, "更新完成", message)
        
        # 重新檢查所有數據
        self.check_all_existing_data()
    
    def stop_update(self):
        """停止更新"""
        if self.update_worker:
            self.update_worker.stop()
            self.update_worker.wait()
        
        self.update_selected_btn.setEnabled(True)
        self.update_all_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式信息
    app.setApplicationName("FinLab增量更新系統")
    app.setApplicationVersion("1.0")
    
    # 創建並顯示主窗口
    window = FinLabIncrementalUpdater()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
