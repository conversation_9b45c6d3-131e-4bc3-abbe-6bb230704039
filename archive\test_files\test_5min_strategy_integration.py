#!/usr/bin/env python3
"""
測試5分鐘策略整合到主程式
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 設置日誌
logging.basicConfig(level=logging.INFO)

def test_strategy_integration():
    """測試策略整合"""
    print("🧪 測試5分鐘策略整合到主程式...")
    
    try:
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI

        # 創建篩選器實例
        screener = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 檢查策略是否已添加到策略字典中
        strategies = screener.strategies
        
        print(f"\n📊 檢查策略字典:")
        five_min_strategies = [
            "5分鐘突破策略",
            "5分鐘均值回歸", 
            "5分鐘動量策略",
            "5分鐘量價策略",
            "5分鐘剝頭皮",
            "5分鐘趨勢跟隨"
        ]
        
        for strategy_name in five_min_strategies:
            if strategy_name in strategies:
                print(f"   ✅ {strategy_name}: 已添加")
            else:
                print(f"   ❌ {strategy_name}: 未找到")
        
        # 生成測試數據
        test_data = generate_test_data()
        
        print(f"\n🔬 測試策略執行:")
        
        # 測試每個5分鐘策略
        strategy_types = [
            "five_min_breakout_strategy",
            "five_min_mean_reversion_strategy",
            "five_min_momentum_strategy", 
            "five_min_volume_price_strategy",
            "five_min_scalping_strategy",
            "five_min_trend_following_strategy"
        ]
        
        for strategy_type in strategy_types:
            try:
                # 創建條件字典
                condition = {"type": strategy_type}
                
                # 執行策略檢查
                result, message = screener.check_condition(test_data, condition)
                
                status = "✅ 通過" if result else "❌ 未通過"
                print(f"   {strategy_type}: {status}")
                print(f"      訊息: {message}")
                
            except Exception as e:
                print(f"   {strategy_type}: ❌ 執行錯誤 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_test_data():
    """生成測試數據"""
    # 生成30天的模擬日K線數據
    dates = pd.date_range(start='2025-06-01', end='2025-07-08', freq='D')
    
    # 基礎價格
    base_price = 100
    
    # 生成價格數據
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, len(dates))  # 日收益率
    
    prices = [base_price]
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # 生成OHLCV數據
    data = []
    for i, (date, close_price) in enumerate(zip(dates, prices)):
        # 生成開高低價
        volatility = abs(returns[i]) * 2
        high = close_price * (1 + volatility * np.random.uniform(0.2, 0.8))
        low = close_price * (1 - volatility * np.random.uniform(0.2, 0.8))
        
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1]
        
        # 確保價格邏輯正確
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        # 生成成交量
        base_volume = 10000
        volume_multiplier = 1 + abs(returns[i]) * 5
        volume = int(base_volume * volume_multiplier * np.random.uniform(0.5, 2.0))
        
        data.append({
            'Date': date,
            'Open': round(open_price, 2),
            'High': round(high, 2), 
            'Low': round(low, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    df['Date'] = pd.to_datetime(df['Date'])
    
    return df

def test_strategy_menu():
    """測試策略選單"""
    print(f"\n🧪 測試策略選單...")
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI

        screener = StockScreenerGUI()
        strategies = screener.strategies
        
        print(f"📋 策略選單內容:")
        print(f"   總策略數: {len(strategies)}")
        
        # 檢查5分鐘策略
        five_min_count = 0
        for name, conditions in strategies.items():
            if "5分鐘" in name:
                five_min_count += 1
                print(f"   ✅ {name}: {conditions}")
        
        print(f"\n📊 5分鐘策略統計:")
        print(f"   5分鐘策略數量: {five_min_count}")
        
        if five_min_count == 6:
            print("   ✅ 所有5分鐘策略都已正確添加")
            return True
        else:
            print("   ❌ 5分鐘策略數量不正確")
            return False
        
    except Exception as e:
        print(f"❌ 策略選單測試失敗: {e}")
        return False

def test_strategy_descriptions():
    """測試策略描述"""
    print(f"\n🧪 測試策略描述...")
    
    strategy_descriptions = {
        "5分鐘突破策略": "基於價格突破關鍵阻力/支撐位的短線交易策略",
        "5分鐘均值回歸": "利用價格偏離均線的回歸特性進行短線交易",
        "5分鐘動量策略": "捕捉價格動量和趨勢變化的短線策略",
        "5分鐘量價策略": "分析成交量與價格配合關係的短線策略",
        "5分鐘剝頭皮": "超短線快進快出的交易策略",
        "5分鐘趨勢跟隨": "跟隨中短期趨勢方向的交易策略"
    }
    
    for name, description in strategy_descriptions.items():
        print(f"   📝 {name}:")
        print(f"      {description}")
    
    return True

def test_integration_with_real_data():
    """使用真實數據測試整合"""
    print(f"\n🧪 使用真實數據測試整合...")
    
    try:
        # 嘗試使用TWSE數據
        try:
            from twse_realtime_crawler import TWSERealtimeCrawler
            crawler = TWSERealtimeCrawler()
            
            # 獲取5分鐘數據
            df_5min = crawler.get_5min_kline_data('2330')
            
            if not df_5min.empty:
                print(f"   ✅ 成功獲取TWSE 5分鐘數據: {len(df_5min)} 筆")
                
                # 轉換為日K線格式進行測試
                if len(df_5min) >= 20:
                    # 取最近20筆5分鐘數據，模擬為日K線
                    test_df = df_5min.tail(20).copy()
                    test_df.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'tick_count']
                    test_df = test_df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']]
                    test_df['Date'] = pd.to_datetime(test_df['Date'])
                    
                    # 測試策略
                    from O3mh_gui_v21_optimized import StockScreenerGUI
                    screener = StockScreenerGUI()
                    
                    condition = {"type": "five_min_breakout_strategy"}
                    result, message = screener.check_condition(test_df, condition)
                    
                    print(f"   📊 真實數據測試結果: {result}")
                    print(f"   📝 訊息: {message}")
                    
                    return True
                else:
                    print(f"   ⚠️ 5分鐘數據不足，使用模擬數據")
            else:
                print(f"   ⚠️ 無法獲取5分鐘數據，使用模擬數據")
                
        except ImportError:
            print(f"   ⚠️ TWSE模組不可用，使用模擬數據")
        
        # 使用模擬數據測試
        test_data = generate_test_data()
        
        from O3mh_gui_v21_optimized import StockScreenerGUI
        screener = StockScreenerGUI()
        
        condition = {"type": "five_min_breakout_strategy"}
        result, message = screener.check_condition(test_data, condition)
        
        print(f"   📊 模擬數據測試結果: {result}")
        print(f"   📝 訊息: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真實數據測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 開始測試5分鐘策略整合...")
    
    results = []
    
    # 執行各項測試
    results.append(("策略整合", test_strategy_integration()))
    results.append(("策略選單", test_strategy_menu()))
    results.append(("策略描述", test_strategy_descriptions()))
    results.append(("真實數據測試", test_integration_with_real_data()))
    
    # 總結結果
    print(f"\n🎉 測試完成！")
    print(f"\n📋 測試結果總結:")
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎊 所有測試都通過！5分鐘策略已成功整合到主程式！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能")
    
    print(f"\n💡 5分鐘策略使用說明:")
    print(f"   1. 在主程式中選擇任一5分鐘策略")
    print(f"   2. 策略會自動分析股票的短線交易機會")
    print(f"   3. 適合日內交易和短線操作")
    print(f"   4. 建議配合5分鐘實時數據使用")
    print(f"   5. 注意風險控制和資金管理")
    
    print(f"\n🎯 策略特色:")
    print(f"   ⚡ 突破策略: 捕捉關鍵位突破")
    print(f"   🔄 均值回歸: 利用價格回歸特性")
    print(f"   🚀 動量策略: 追蹤價格動量變化")
    print(f"   📊 量價策略: 分析量價配合關係")
    print(f"   ⚡ 剝頭皮: 超短線快進快出")
    print(f"   📈 趨勢跟隨: 跟隨短期趨勢方向")
