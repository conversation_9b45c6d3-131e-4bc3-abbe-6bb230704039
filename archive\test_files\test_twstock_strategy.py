#!/usr/bin/env python3
"""
測試使用 twstock 的日內交易策略
"""

import sys
import os
sys.path.append('.')

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy
import datetime

def test_twstock_basic():
    """測試 twstock 基本功能"""
    print("🧪 測試 twstock 基本功能...")
    
    try:
        import twstock
        print(f"✅ twstock 版本: {twstock.__version__}")
        
        # 測試獲取股票資訊
        stock_id = '2330'
        print(f"\n📊 測試獲取 {stock_id} 資訊:")
        
        # 獲取基本資訊
        stock = twstock.Stock(stock_id)
        print(f"  股票名稱: {twstock.codes[stock_id] if stock_id in twstock.codes else '未知'}")
        
        # 獲取歷史數據 (從本月開始)
        import datetime
        now = datetime.datetime.now()
        stock.fetch_from(now.year, now.month)
        if stock.data:
            latest = stock.data[-1]
            print(f"  最新日期: {latest.date}")
            print(f"  收盤價: {latest.close}")
            print(f"  成交量: {latest.capacity:,}")
            print(f"  歷史數據筆數: {len(stock.data)}")
        else:
            print("  ❌ 無法獲取歷史數據")
        
        # 測試即時數據
        print(f"\n📈 測試即時數據:")
        try:
            realtime = twstock.realtime.get(stock_id)
            if realtime and realtime['success']:
                data = realtime['realtime']
                print(f"  即時價格: {data.get('latest_trade_price', 'N/A')}")
                print(f"  開盤價: {data.get('open', 'N/A')}")
                print(f"  最高價: {data.get('high', 'N/A')}")
                print(f"  最低價: {data.get('low', 'N/A')}")
                print(f"  成交量: {data.get('accumulate_trade_volume', 'N/A')}")
            else:
                print("  ⚠️ 即時數據不可用 (可能非交易時間)")
        except Exception as e:
            print(f"  ❌ 即時數據錯誤: {e}")
        
        return True
        
    except ImportError:
        print("❌ twstock 未安裝")
        return False
    except Exception as e:
        print(f"❌ twstock 測試失敗: {e}")
        return False

def test_strategy_with_twstock():
    """測試使用 twstock 的策略"""
    print("\n🚀 測試 twstock 策略...")
    
    try:
        # 初始化策略
        strategy = IntradayOpeningRangeStrategy()
        print(f"✅ 策略初始化: {strategy.name}")
        print(f"  數據源: {strategy.data_source}")
        
        # 測試股票清單
        test_stocks = ['2330', '2317', '2454', '1301', '2303']
        
        for stock_id in test_stocks:
            print(f"\n📊 測試 {stock_id}:")
            
            # 測試日線數據獲取
            daily_data = strategy.fetch_daily_data(stock_id, 10)
            if not daily_data.empty:
                print(f"  ✅ 日線數據: {len(daily_data)} 筆")
                print(f"  日期範圍: {daily_data['Date'].min()} ~ {daily_data['Date'].max()}")
                
                # 計算技術指標
                daily_data = strategy.calculate_indicators(daily_data)
                
                # 測試盤前篩選
                pre_market_ok, reason = strategy.check_pre_market_conditions(daily_data)
                print(f"  盤前篩選: {'✅ 通過' if pre_market_ok else '❌ 未通過'}")
                print(f"  篩選原因: {reason}")
                
                if pre_market_ok:
                    # 測試即時數據
                    if strategy.use_twstock:
                        realtime_data = strategy.fetch_realtime_data_twstock(stock_id)
                        if realtime_data:
                            print(f"  ✅ 即時數據: 價格 {realtime_data['latest_price']}")
                        else:
                            print("  ⚠️ 即時數據不可用")
                    
                    # 測試盤中數據模擬
                    intraday_data = strategy.fetch_intraday_data(stock_id, datetime.datetime.now())
                    if not intraday_data.empty:
                        print(f"  ✅ 盤中數據: {len(intraday_data)} 筆")
                        
                        # 測試開盤區間計算
                        range_high, range_low, range_info = strategy.opening_range_breakout(intraday_data)
                        if range_high is not None:
                            print(f"  ✅ {range_info}")
                        else:
                            print(f"  ❌ 開盤區間失敗: {range_info}")
                    else:
                        print("  ❌ 無盤中數據")
            else:
                print(f"  ❌ 無法獲取日線數據")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_strategy():
    """測試完整策略流程"""
    print("\n🎯 測試完整策略流程...")
    
    try:
        strategy = IntradayOpeningRangeStrategy()
        
        # 測試幾支熱門股票
        test_stocks = ['2330', '2317']
        
        for stock_id in test_stocks:
            print(f"\n完整測試 {stock_id}:")
            
            result = strategy.simulate_breakout_trading(stock_id, datetime.datetime.now())
            
            if result:
                print(f"  結果: {result.get('result', 'unknown')}")
                print(f"  階段: {result.get('stage', 'unknown')}")
                print(f"  原因: {result.get('reason', 'unknown')}")
                
                if 'signal_details' in result:
                    signal = result['signal_details']
                    print(f"  信號類型: {signal.get('breakout_type', 'none')}")
                    print(f"  信心度: {signal.get('confidence', 0)}%")
                    
                if result.get('result') == 'buy_signal':
                    print("  🎯 發現買入機會!")
                elif result.get('result') == 'wait_signal':
                    print("  ⏳ 等待突破確認")
            else:
                print("  ❌ 無分析結果")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整策略測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 twstock 日內交易策略測試")
    print("=" * 50)
    
    # 測試 twstock 基本功能
    if not test_twstock_basic():
        print("\n💥 twstock 基本功能測試失敗!")
        return
    
    # 測試策略功能
    if not test_strategy_with_twstock():
        print("\n💥 策略功能測試失敗!")
        return
    
    # 測試完整流程
    if not test_complete_strategy():
        print("\n💥 完整流程測試失敗!")
        return
    
    print("\n🎉 所有測試通過!")
    print("✅ twstock 日內交易策略已準備就緒")

if __name__ == "__main__":
    main()
