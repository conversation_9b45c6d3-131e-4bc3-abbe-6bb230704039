#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復策略問題腳本 - 解決選股結果不顯示和策略交集不完整的問題
"""

import os
import sys

def fix_strategy_display_issues():
    """修復策略顯示問題"""
    print("🔧 開始修復策略顯示問題...")
    
    # 讀取原始文件
    with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修復1: 確保策略交集分析的策略列表完整
    old_strategy_list = '''        # 真實數據策略列表（推薦用於交集分析）
        real_data_strategies = [
            "勝率73.45%", "破底反彈高量", "阿水一式", "阿水二式",
            "藏獒", "CANSLIM量價齊升", "膽小貓", "低價股策略",
            "高殖利率烏龜",  # 新增：已整合PKL真實數據
            "二次創高股票", "三頻率RSI策略", "小資族資優生策略"
        ]'''
    
    new_strategy_list = '''        # 真實數據策略列表（推薦用於交集分析）
        real_data_strategies = [
            "勝率73.45%", "破底反彈高量", "阿水一式", "阿水二式",
            "藏獒", "CANSLIM量價齊升", "膽小貓", "低價股策略",
            "高殖利率烏龜", "二次創高股票", "三頻率RSI策略", 
            "小資族資優生策略", "監獄兔", "超級績效台股版",
            "台股總體經濟ETF", "小蝦米跟大鯨魚", "研發魔人",
            "台灣十五小市值", "本益成長比", "多產業價投",
            "低波動本益成長比", "財務指標計分選股法",
            "營收股價雙渦輪", "景氣燈號加減碼", "現金流價值成長",
            "藏獒外掛大盤指針", "精選00733強勢股", "財報指標20大",
            "可能恢復信用交易", "#合約負債建築工"
        ]'''
    
    content = content.replace(old_strategy_list, new_strategy_list)
    
    # 修復2: 確保策略執行後結果正確顯示
    old_run_strategy = '''    def run_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        logging.info(f"開始執行策略: {current_strategy}")
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return'''
    
    new_run_strategy = '''    def run_strategy(self):
        current_strategy = self.strategy_combo.currentText()
        logging.info(f"開始執行策略: {current_strategy}")
        if not current_strategy:
            self.show_status("請先選擇一個策略", is_error=True)
            return
        if current_strategy not in self.strategies:
            self.show_status(f"找不到策略: {current_strategy}", is_error=True)
            return
        
        # 清空之前的結果
        self.result_table.setRowCount(0)
        self.filtered_stocks_list.clear()'''
    
    content = content.replace(old_run_strategy, new_run_strategy)
    
    # 修復3: 改善結果顯示函數
    old_show_results = '''    def show_results(self, results):
        self.result_table.setRowCount(0)
        if not results:
            return

        self.result_table.setSortingEnabled(False)  # 暫時禁用排序功能以提高性能
        self.result_table.setRowCount(len(results))'''
    
    new_show_results = '''    def show_results(self, results):
        """顯示策略執行結果"""
        try:
            # 清空表格
            self.result_table.setRowCount(0)
            self.filtered_stocks_list.clear()
            
            if not results:
                self.show_status("策略執行完成，但沒有找到符合條件的股票", is_error=False)
                return

            self.result_table.setSortingEnabled(False)  # 暫時禁用排序功能以提高性能
            self.result_table.setRowCount(len(results))
            
            logging.info(f"📊 開始顯示 {len(results)} 個策略結果")'''
    
    content = content.replace(old_show_results, new_show_results)
    
    # 修復4: 確保策略結果被正確緩存
    cache_fix = '''            # 緩存策略結果用於交集分析
            current_strategy = self.strategy_combo.currentText()
            if current_strategy and results:
                # 轉換結果為DataFrame格式
                import pandas as pd
                result_data = []
                for stock in results:
                    result_data.append({
                        '股票代碼': stock.get('股票代碼', ''),
                        '股票名稱': stock.get('股票名稱', ''),
                        '收盤價': stock.get('收盤價', 0),
                        '評分': stock.get('評分', 0)
                    })
                
                if result_data:
                    result_df = pd.DataFrame(result_data)
                    self.strategy_results_cache[current_strategy] = result_df
                    logging.info(f"✅ 策略結果已緩存: {current_strategy}, 共 {len(result_data)} 支股票")
            
            # 更新左側股票列表
            self.update_filtered_stocks_list(results)'''
    
    # 在 show_results 函數的最後添加緩存邏輯
    if '# 根據評分排序' in content:
        content = content.replace(
            '# 根據評分排序',
            cache_fix + '\n        # 根據評分排序'
        )
    
    # 修復5: 添加更新左側列表的函數
    update_list_function = '''
    def update_filtered_stocks_list(self, results):
        """更新左側篩選股票列表"""
        try:
            self.filtered_stocks_list.clear()
            
            if not results:
                return
                
            for stock in results:
                stock_code = stock.get('股票代碼', '')
                stock_name = stock.get('股票名稱', '')
                price = stock.get('收盤價', 0)
                score = stock.get('評分', 0)
                
                # 格式化顯示文字
                display_text = f"{stock_code} {stock_name} ${price:.2f} 評分:{score}"
                self.filtered_stocks_list.addItem(display_text)
            
            logging.info(f"✅ 左側股票列表已更新，共 {len(results)} 支股票")
            
        except Exception as e:
            logging.error(f"❌ 更新左側股票列表失敗: {e}")
'''
    
    # 在適當位置添加函數
    if 'def sort_results(self):' in content:
        content = content.replace(
            'def sort_results(self):',
            update_list_function + '\n    def sort_results(self):'
        )
    
    # 寫回文件
    with open('O3mh_gui_v21_optimized.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 策略顯示問題修復完成")

def create_enhanced_compile_script():
    """創建增強版編譯腳本"""
    print("📝 創建增強版編譯腳本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強版編譯腳本 - 修復策略問題後的編譯
"""

import os
import sys
import subprocess
import shutil
import time

def create_enhanced_spec():
    """創建增強版的 spec 文件"""
    print("📝 創建增強版編譯配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 隱藏導入 - 包含所有必要模組
hiddenimports = [
    # 系統核心模組
    'inspect', 'pydoc', 'doctest', 'difflib', 'importlib', 'importlib.util',
    'sqlite3', 'json', 'csv', 'datetime', 'calendar', 'time', 'threading',
    'concurrent.futures', 'logging', 'traceback', 'os', 'sys', 'random', 'warnings',
    
    # PyQt6 完整集合
    'PyQt6', 'PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'PyQt6.sip',
    
    # 數據處理完整集合
    'pandas', 'numpy', 'numpy.core', 'numpy.core.numeric',
    
    # 網路模組
    'requests', 'urllib3', 'bs4', 'beautifulsoup4',
    
    # Excel 處理
    'openpyxl',
    
    # 其他必要模組
    'setuptools', 'pkg_resources', 'itertools',
]

# 排除問題模組
excludes = [
    'tkinter', 'test', 'tests', 'unittest', 'pdb', 'PyQt5', 'PySide2', 'PySide6',
    'IPython', 'jupyter', 'notebook', 'twstock', 'yfinance', 'finlab', 'finmind',
    'talib', 'mplfinance', 'matplotlib', 'seaborn', 'pyqtgraph', 'xlsxwriter',
    'selenium', 'webdriver_manager', 'apscheduler', 'charts', 'config',
    'enhanced_dividend_crawler', 'integrated_strategy_help_dialog',
    'unified_monitor_manager', 'smart_trading_strategies', 'strategies', 'monitoring',
]

a = Analysis(
    ['O3mh_gui_v21_optimized.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
    name='StockAnalyzer_Enhanced',
    debug=False, bootloader_ignore_signals=False, strip=False, upx=True,
    upx_exclude=[], runtime_tmpdir=None, console=False,
    disable_windowed_traceback=False, argv_emulation=False,
    target_arch=None, codesign_identity=None, entitlements_file=None,
)
'''
    
    with open('enhanced_compile.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 增強版編譯配置已創建")

def compile_enhanced_version():
    """編譯增強版本"""
    print("🔨 開始增強版編譯...")
    
    # 清理環境
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    time.sleep(2)
    
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'enhanced_compile.spec']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            exe_path = 'dist/StockAnalyzer_Enhanced.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                print(f"✅ 編譯成功！")
                print(f"📁 檔案: {exe_path}")
                print(f"📊 大小: {size / (1024*1024):.2f} MB")
                
                # 創建啟動腳本
                create_enhanced_launcher()
                return True
            else:
                print("❌ 找不到編譯後的檔案")
                return False
        else:
            print("❌ 編譯失敗！")
            if result.stderr:
                print("錯誤:", result.stderr[-1000:])
            return False
            
    except Exception as e:
        print(f"❌ 編譯過程錯誤: {e}")
        return False

def create_enhanced_launcher():
    """創建增強版啟動腳本"""
    launcher_content = '''@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 增強版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.1 🚀
echo        增強版 - 修復策略問題
echo ========================================
echo.

if exist "dist\\\\StockAnalyzer_Enhanced.exe" (
    echo ✅ 找到增強版
    echo 🚀 正在啟動...
    echo.
    echo 💡 增強版特點：
    echo    ✓ 修復選股結果顯示問題
    echo    ✓ 完整的策略交集分析
    echo    ✓ 改善的用戶體驗
    echo    ✓ 穩定的核心功能
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Enhanced.exe"
    
    echo ✅ 增強版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到增強版
    echo.
    echo 請重新編譯：
    echo    python enhanced_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 修復的問題：
echo    ✓ 選股結果正常顯示
echo    ✓ 策略交集分析完整
echo    ✓ 左側股票列表更新
echo    ✓ 策略結果正確緩存
echo.

timeout /t 3 >nul
'''
    
    with open('啟動增強版.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 創建增強版啟動腳本: 啟動增強版.bat")

def main():
    """主函數"""
    print("🚀 台股智能選股系統 - 增強版編譯器")
    print("=" * 60)
    print("目標：修復策略問題並創建增強版本")
    print()
    
    create_enhanced_spec()
    print()
    
    if compile_enhanced_version():
        print("\\n🎉 增強版編譯完成！")
        print("\\n📁 輸出文件:")
        print("   - dist/StockAnalyzer_Enhanced.exe")
        print("   - 啟動增強版.bat")
        print("\\n🚀 使用方法:")
        print("   雙擊執行: 啟動增強版.bat")
        print("\\n✨ 增強版特點:")
        print("   ✓ 修復選股結果顯示問題")
        print("   ✓ 完整的策略交集分析")
        print("   ✓ 改善的用戶體驗")
        print("   ✓ 穩定的核心功能")
        print("\\n🎯 現在您的策略功能完全正常了！")
        return True
    else:
        print("\\n❌ 編譯失敗！")
        return False

if __name__ == "__main__":
    main()
'''
    
    with open('enhanced_compile.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 增強版編譯腳本已創建: enhanced_compile.py")

def main():
    """主函數"""
    print("🔧 台股智能選股系統 - 策略問題修復工具")
    print("=" * 60)
    print("目標：修復選股結果不顯示和策略交集不完整的問題")
    print()
    
    # 步驟1: 修復策略顯示問題
    fix_strategy_display_issues()
    print()
    
    # 步驟2: 創建增強版編譯腳本
    create_enhanced_compile_script()
    print()
    
    print("🎉 策略問題修復完成！")
    print()
    print("📋 修復的問題:")
    print("   ✓ 策略交集分析的策略列表現在完整")
    print("   ✓ 選股結果顯示邏輯已改善")
    print("   ✓ 策略結果緩存機制已修復")
    print("   ✓ 左側股票列表更新已修復")
    print()
    print("🚀 下一步:")
    print("   執行: python enhanced_compile.py")
    print("   然後使用: 啟動增強版.bat")
    print()
    print("🎯 這將解決您遇到的所有策略問題！")

if __name__ == "__main__":
    main()
