#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的右鍵選單功能
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函數"""
    print("=" * 60)
    print("🎉 測試修復後的右鍵選單功能")
    print("=" * 60)
    
    try:
        from O3mh_gui_v21_optimized import StockScreenerGUI
        from PyQt6.QtWidgets import QApplication, QListWidgetItem
        
        # 創建QApplication
        app = QApplication(sys.argv)
        
        # 創建主程式實例
        gui = StockScreenerGUI()
        
        print("✅ 主程式載入成功")
        
        # 添加測試股票到左側列表
        test_stocks = [
            "2330 台積電",
            "2317 鴻海",
            "2454 聯發科",
            "1234 測試股票"
        ]
        
        gui.all_stocks_list.clear()
        for stock_text in test_stocks:
            item = QListWidgetItem(stock_text)
            gui.all_stocks_list.addItem(item)
        
        gui.filtered_stocks_list.clear()
        for stock_text in test_stocks:
            item = QListWidgetItem(stock_text)
            gui.filtered_stocks_list.addItem(item)
        
        print("✅ 測試股票添加完成")
        
        # 測試模擬資料創建
        print("\n📊 測試模擬資料創建...")
        
        test_codes = ["2330", "2317", "1234"]
        test_names = ["台積電", "鴻海", "測試股票"]
        
        for code, name in zip(test_codes, test_names):
            mock_data = gui.create_mock_monthly_revenue_data(code, name)
            if mock_data:
                print(f"  ✅ {code} {name} 模擬資料創建成功:")
                print(f"    排名: {mock_data['排名']}")
                print(f"    當月營收: {mock_data['當月營收']}")
                print(f"    YoY%: {mock_data['YoY%']}")
                print(f"    MoM%: {mock_data['MoM%']}")
                print(f"    EPS: {mock_data['EPS']}")
                print(f"    綜合評分: {mock_data['綜合評分']}")
            else:
                print(f"  ❌ {code} {name} 模擬資料創建失敗")
        
        # 測試評估對話框
        print("\n🖥️ 測試評估對話框...")
        
        # 使用台積電的模擬資料測試
        mock_data = gui.create_mock_monthly_revenue_data("2330", "台積電")
        if mock_data:
            print("  正在顯示台積電的評估對話框...")
            try:
                gui.show_monthly_revenue_assessment(mock_data)
                print("  ✅ 評估對話框顯示成功")
            except Exception as e:
                print(f"  ❌ 評估對話框顯示失敗: {e}")
        
        # 顯示GUI
        print(f"\n🖥️ 顯示GUI進行手動測試...")
        print("📋 測試步驟:")
        print("1. 在左側「全部股票」列表中右鍵點擊「2330 台積電」")
        print("2. 選擇「📊 2330 台積電 月營收綜合評估」")
        print("3. 檢查評估對話框是否顯示完整的資料（不是N/A）")
        print("4. 嘗試其他股票（2317 鴻海、2454 聯發科、1234 測試股票）")
        print("5. 確認所有股票都能顯示合理的評估資料")
        print("=" * 60)
        
        gui.show()
        
        # 執行應用程式
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
