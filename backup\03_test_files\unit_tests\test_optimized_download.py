#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化後的月營收下載（只下載上市上櫃）
"""

from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader

def test_optimized_download():
    """測試優化版本"""
    
    print("🚀 測試優化版本（只下載上市上櫃）")
    print("=" * 50)
    
    # 初始化下載器
    downloader = MopsBulkRevenueDownloader('test_optimized.db')
    
    # 測試2024年6月
    print("📅 下載 2024年6月 數據...")
    results = downloader.download_all_markets(2024, 6)
    
    print("\n📊 下載結果:")
    total = 0
    for market, count in results.items():
        print(f"  {market}: {count} 家公司")
        total += count
    
    print(f"\n✅ 總計: {total} 家公司")
    print("💡 優化效果: 跳過興櫃股票，專注於上市上櫃")
    
    return results

if __name__ == "__main__":
    test_optimized_download()
