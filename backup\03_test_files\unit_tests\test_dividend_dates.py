#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試除權息日期獲取功能
"""

import logging
from enhanced_dividend_crawler import EnhancedDividendCrawler

def test_dividend_dates():
    """測試除權息日期獲取"""
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 創建爬蟲實例
    crawler = EnhancedDividendCrawler()
    
    # 測試2024年資料（應該有完整的除權息日期）
    print("🔍 測試獲取2024年除權息資料...")
    
    # 獲取2024年資料
    dividend_data_2024 = crawler.fetch_all_dividend_data(2024)
    
    if dividend_data_2024:
        # 匯出2024年資料
        csv_file_2024 = crawler.export_to_csv(2024, "dividend_data_2024_with_dates.csv")
        if csv_file_2024:
            print(f"✅ 2024年除權息資料已匯出至: {csv_file_2024}")
        
        # 顯示一些有除權息日期的範例
        print("\n📊 2024年除權息資料範例 (含日期):")
        count = 0
        for record in dividend_data_2024:
            if record.get('ex_dividend_date') and count < 10:
                print(f"  {record['stock_code']} {record['stock_name']}: "
                      f"除權息日期 {record['ex_dividend_date']}, "
                      f"現金股利 {record['cash_dividend']}")
                count += 1
        
        if count == 0:
            print("  ⚠️ 未找到包含除權息日期的資料")
    
    # 測試查詢台積電歷史資料
    print(f"\n📈 台積電除權息歷史:")
    tsmc_history = crawler.get_stock_dividend_history('2330', years=3)
    for record in tsmc_history:
        print(f"  {record['year']}: 現金股利 {record['cash_dividend']}, "
              f"股票股利 {record['stock_dividend']}, "
              f"資料來源 {record['data_source']}")

if __name__ == "__main__":
    test_dividend_dates()
