#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面修復 date_range.pickle 檔案
掃描所有 pkl 檔案並更新正確的日期範圍記錄
"""

import pandas as pd
import pickle
import os
import glob
from datetime import datetime
import traceback

def scan_pkl_files():
    """掃描所有 pkl 檔案"""
    print("🔍 掃描 history/tables/ 目錄中的所有 pkl 檔案...")
    
    tables_dir = "history/tables"
    if not os.path.exists(tables_dir):
        print(f"❌ 目錄不存在: {tables_dir}")
        return []
    
    # 找到所有 pkl 檔案
    pkl_files = glob.glob(os.path.join(tables_dir, "*.pkl"))
    
    # 過濾掉備份檔案和測試檔案
    exclude_patterns = [
        'backup', 'test', 'new', 'temp', 'tmp', 
        '_v2', '_old', '_bak', 'before_merge'
    ]
    
    filtered_files = []
    for file_path in pkl_files:
        filename = os.path.basename(file_path)
        table_name = filename.replace('.pkl', '')
        
        # 檢查是否包含排除的模式
        should_exclude = any(pattern in filename.lower() for pattern in exclude_patterns)
        
        if not should_exclude:
            filtered_files.append((file_path, table_name))
    
    print(f"✅ 找到 {len(filtered_files)} 個主要 pkl 檔案")
    for file_path, table_name in filtered_files:
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        print(f"   {table_name}: {file_size:.1f} MB")
    
    return filtered_files

def analyze_pkl_file(file_path, table_name):
    """分析單個 pkl 檔案的日期範圍"""
    print(f"\n📊 分析 {table_name}...")
    
    try:
        # 讀取檔案
        data = pd.read_pickle(file_path)
        
        if len(data) == 0:
            print(f"   ⚠️ 檔案為空")
            return None
        
        print(f"   資料筆數: {len(data):,}")
        print(f"   資料結構: {type(data)}")
        
        # 檢查索引結構
        if hasattr(data, 'index'):
            print(f"   索引類型: {type(data.index)}")
            if hasattr(data.index, 'names'):
                print(f"   索引名稱: {data.index.names}")
        
        # 嘗試找到日期欄位
        date_column = None
        dates = None
        
        # 方法1: 檢查多層索引中的 'date' 層級
        if hasattr(data, 'index') and hasattr(data.index, 'names'):
            if 'date' in data.index.names:
                dates = data.index.get_level_values('date')
                date_column = 'index:date'
        
        # 方法2: 檢查是否有 'date' 欄位
        if dates is None and hasattr(data, 'columns') and 'date' in data.columns:
            dates = pd.to_datetime(data['date'])
            date_column = 'column:date'
        
        # 方法3: 檢查索引是否為日期類型
        if dates is None and hasattr(data, 'index'):
            try:
                if pd.api.types.is_datetime64_any_dtype(data.index):
                    dates = data.index
                    date_column = 'index'
            except:
                pass
        
        # 方法4: 嘗試找到其他可能的日期欄位
        if dates is None and hasattr(data, 'columns'):
            date_like_columns = [col for col in data.columns if 
                               any(keyword in str(col).lower() for keyword in 
                                   ['date', 'time', '日期', '時間'])]
            
            for col in date_like_columns:
                try:
                    dates = pd.to_datetime(data[col])
                    date_column = f'column:{col}'
                    break
                except:
                    continue
        
        if dates is not None and len(dates) > 0:
            # 移除 NaT 值
            dates = dates.dropna()
            if len(dates) > 0:
                start_date = dates.min()
                end_date = dates.max()
                unique_dates = len(dates.unique())
                
                print(f"   ✅ 找到日期資料 ({date_column})")
                print(f"   日期範圍: {start_date} 至 {end_date}")
                print(f"   唯一日期數: {unique_dates}")
                
                return (start_date, end_date)
        
        print(f"   ⚠️ 未找到日期欄位")
        return None
        
    except Exception as e:
        print(f"   ❌ 分析失敗: {str(e)[:100]}...")
        print(f"   詳細錯誤: {traceback.format_exc()}")
        return None

def load_current_date_ranges():
    """載入當前的日期範圍記錄"""
    date_range_file = "history/date_range.pickle"
    
    print(f"\n📋 載入當前的日期範圍記錄...")
    
    if os.path.exists(date_range_file):
        try:
            with open(date_range_file, 'rb') as f:
                current_ranges = pickle.load(f)
            
            print(f"✅ 載入成功，包含 {len(current_ranges)} 個表格記錄")
            return current_ranges
            
        except Exception as e:
            print(f"❌ 載入失敗: {str(e)}")
            print("🆕 將創建新的記錄檔案")
            return {}
    else:
        print("⚠️ 檔案不存在，將創建新的記錄檔案")
        return {}

def save_date_ranges(date_ranges):
    """保存日期範圍記錄"""
    date_range_file = "history/date_range.pickle"
    backup_file = f"history/date_range_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pickle"
    
    print(f"\n💾 保存日期範圍記錄...")
    
    try:
        # 創建備份
        if os.path.exists(date_range_file):
            import shutil
            shutil.copy2(date_range_file, backup_file)
            print(f"✅ 備份創建: {backup_file}")
        
        # 保存新記錄
        with open(date_range_file, 'wb') as f:
            pickle.dump(date_ranges, f)
        
        print(f"✅ 記錄已保存: {date_range_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存失敗: {str(e)}")
        return False

def compare_and_update_ranges(current_ranges, new_ranges):
    """比較並更新日期範圍"""
    print(f"\n🔄 比較並更新日期範圍...")
    
    updated_count = 0
    added_count = 0
    unchanged_count = 0
    
    print(f"\n{'表格名稱':<20} {'狀態':<8} {'舊範圍':<50} {'新範圍'}")
    print("=" * 120)
    
    # 更新現有記錄
    for table_name, new_range in new_ranges.items():
        if new_range is None:
            continue
            
        start_date, end_date = new_range
        new_range_str = f"{start_date} 至 {end_date}"
        
        if table_name in current_ranges:
            old_start, old_end = current_ranges[table_name]
            old_range_str = f"{old_start} 至 {old_end}"
            
            if old_start != start_date or old_end != end_date:
                print(f"{table_name:<20} {'更新':<8} {old_range_str:<50} {new_range_str}")
                current_ranges[table_name] = new_range
                updated_count += 1
            else:
                print(f"{table_name:<20} {'無變化':<8} {old_range_str}")
                unchanged_count += 1
        else:
            print(f"{table_name:<20} {'新增':<8} {'無':<50} {new_range_str}")
            current_ranges[table_name] = new_range
            added_count += 1
    
    print("\n📊 更新統計:")
    print(f"   新增: {added_count} 個")
    print(f"   更新: {updated_count} 個") 
    print(f"   無變化: {unchanged_count} 個")
    print(f"   總計: {len(current_ranges)} 個表格")
    
    return current_ranges, (added_count + updated_count) > 0

def main():
    """主函數"""
    print("🔧 全面修復 date_range.pickle 工具")
    print("=" * 60)
    print("🎯 功能: 掃描所有 pkl 檔案並修復日期範圍記錄")
    print("=" * 60)
    
    # 1. 掃描所有 pkl 檔案
    pkl_files = scan_pkl_files()
    if not pkl_files:
        print("❌ 沒有找到 pkl 檔案")
        return
    
    # 2. 載入當前記錄
    current_ranges = load_current_date_ranges()
    
    # 3. 分析每個檔案
    new_ranges = {}
    success_count = 0
    
    for file_path, table_name in pkl_files:
        date_range = analyze_pkl_file(file_path, table_name)
        if date_range:
            new_ranges[table_name] = date_range
            success_count += 1
    
    print(f"\n📊 分析結果:")
    print(f"   成功分析: {success_count}/{len(pkl_files)} 個檔案")
    
    if success_count == 0:
        print("❌ 沒有成功分析任何檔案")
        return
    
    # 4. 比較並更新
    updated_ranges, has_changes = compare_and_update_ranges(current_ranges, new_ranges)
    
    # 5. 保存結果
    if has_changes:
        response = input(f"\n是否保存更新後的日期範圍記錄？(y/N): ").strip().lower()
        if response == 'y':
            if save_date_ranges(updated_ranges):
                print("\n🎉 修復完成！")
                print("💡 建議:")
                print("   • 重新運行 auto_update.py 測試")
                print("   • 檢查日期範圍是否正確顯示")
                print("   • 備份檔案已自動創建")
            else:
                print("\n❌ 保存失敗")
        else:
            print("\n❌ 操作已取消")
    else:
        print("\n✅ 所有記錄都是最新的，無需更新")

if __name__ == "__main__":
    main()
