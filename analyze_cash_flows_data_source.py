#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 cash_flows.db 的資料來源和空值情況
"""

import sqlite3
import pandas as pd
import numpy as np

def analyze_cash_flows_data():
    """分析現金流量資料的來源和空值情況"""
    
    print("=" * 80)
    print("🔍 分析 cash_flows.db 的資料來源和空值情況")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(cash_flows_db)
        
        # 1. 基本資訊
        print("📊 基本資訊:")
        cursor = conn.cursor()
        
        # 總記錄數
        cursor.execute("SELECT COUNT(*) FROM cash_flows")
        total_records = cursor.fetchone()[0]
        print(f"   總記錄數: {total_records:,}")
        
        # 唯一股票數
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM cash_flows")
        unique_stocks = cursor.fetchone()[0]
        print(f"   唯一股票數: {unique_stocks:,}")
        
        # 日期範圍
        cursor.execute("SELECT MIN(date), MAX(date) FROM cash_flows")
        date_range = cursor.fetchone()
        print(f"   日期範圍: {date_range[0]} ~ {date_range[1]}")
        
        # 2. 欄位分析
        print(f"\n📋 欄位分析:")
        cursor.execute("PRAGMA table_info(cash_flows)")
        columns = cursor.fetchall()
        
        total_columns = len(columns)
        cash_flow_columns = [col[1] for col in columns if col[1] not in ['stock_id', 'stock_name', 'date']]
        
        print(f"   總欄位數: {total_columns}")
        print(f"   現金流量項目欄位數: {len(cash_flow_columns)}")
        
        # 3. 空值分析
        print(f"\n🔍 空值分析:")
        
        # 讀取所有資料進行分析
        df = pd.read_sql_query("SELECT * FROM cash_flows", conn)
        
        # 計算每個欄位的空值比例
        null_stats = []
        for col in cash_flow_columns:
            null_count = df[col].isnull().sum()
            null_percentage = (null_count / len(df)) * 100
            non_null_count = len(df) - null_count
            
            null_stats.append({
                'column': col,
                'null_count': null_count,
                'non_null_count': non_null_count,
                'null_percentage': null_percentage
            })
        
        # 排序並顯示空值統計
        null_stats_df = pd.DataFrame(null_stats)
        null_stats_df = null_stats_df.sort_values('null_percentage')
        
        print(f"   空值比例最低的前10個欄位:")
        for i, row in null_stats_df.head(10).iterrows():
            print(f"      {row['column']:<30} 非空值: {row['non_null_count']:>6,} ({100-row['null_percentage']:>5.1f}%)")
        
        print(f"\n   空值比例最高的前10個欄位:")
        for i, row in null_stats_df.tail(10).iterrows():
            print(f"      {row['column']:<30} 空值: {row['null_count']:>6,} ({row['null_percentage']:>5.1f}%)")
        
        # 4. 按股票分析空值情況
        print(f"\n📊 按股票分析空值情況:")
        
        # 計算每支股票的非空值欄位數
        stock_stats = []
        for stock_id in df['stock_id'].unique()[:20]:  # 只分析前20支股票
            stock_data = df[df['stock_id'] == stock_id]
            
            for _, row in stock_data.iterrows():
                non_null_count = 0
                for col in cash_flow_columns:
                    if pd.notna(row[col]) and row[col] != 0:
                        non_null_count += 1
                
                stock_stats.append({
                    'stock_id': stock_id,
                    'stock_name': row['stock_name'],
                    'date': row['date'],
                    'non_null_fields': non_null_count,
                    'total_fields': len(cash_flow_columns)
                })
        
        stock_stats_df = pd.DataFrame(stock_stats)
        
        print(f"   範例股票的現金流量項目填寫情況:")
        for stock_id in stock_stats_df['stock_id'].unique()[:5]:
            stock_data = stock_stats_df[stock_stats_df['stock_id'] == stock_id]
            avg_fields = stock_data['non_null_fields'].mean()
            stock_name = stock_data['stock_name'].iloc[0]
            print(f"      {stock_id} ({stock_name}): 平均 {avg_fields:.1f}/{len(cash_flow_columns)} 個項目有數值")
        
        # 5. 按日期分析資料完整性
        print(f"\n📅 按日期分析資料完整性:")
        
        date_stats = []
        for date in df['date'].unique():
            date_data = df[df['date'] == date]
            
            total_cells = len(date_data) * len(cash_flow_columns)
            non_null_cells = 0
            
            for col in cash_flow_columns:
                non_null_cells += date_data[col].notna().sum()
            
            completeness = (non_null_cells / total_cells) * 100
            
            date_stats.append({
                'date': date,
                'stocks_count': len(date_data),
                'completeness': completeness
            })
        
        date_stats_df = pd.DataFrame(date_stats).sort_values('date')
        
        print(f"   各報告期的資料完整性:")
        for _, row in date_stats_df.iterrows():
            print(f"      {row['date']}: {row['stocks_count']:>4} 家公司, 完整性 {row['completeness']:>5.1f}%")
        
        # 6. 常見的現金流量項目分析
        print(f"\n💰 常見現金流量項目分析:")
        
        common_items = [
            '本期稅前淨利（淨損）',
            '營業活動之淨現金流入（流出）',
            '投資活動之淨現金流入（流出）',
            '籌資活動之淨現金流入（流出）',
            '期初現金及約當現金餘額',
            '期末現金及約當現金餘額',
            '折舊費用',
            '攤銷費用'
        ]
        
        for item in common_items:
            if item in df.columns:
                non_null_count = df[item].notna().sum()
                percentage = (non_null_count / len(df)) * 100
                print(f"      {item:<25} 有數值: {non_null_count:>6,} ({percentage:>5.1f}%)")
        
        # 7. 資料來源推測
        print(f"\n🌐 資料來源分析:")
        print(f"   根據程式碼分析，現金流量資料來源為:")
        print(f"   📡 MOPS 公開資訊觀測站 (https://mops.twse.com.tw/)")
        print(f"   📋 具體來源: 各公司季報/年報的現金流量表")
        print(f"   🔗 URL 格式: https://mops.twse.com.tw/server-java/t164sb01")
        print(f"   📊 資料類型: 季報財務報表第四部分 (現金流量表)")
        
        # 8. 空值原因分析
        print(f"\n❓ 空值原因分析:")
        print(f"   1. 📋 會計科目差異: 不同公司使用不同的會計科目")
        print(f"   2. 🏢 行業特性: 不同行業的現金流量項目不同")
        print(f"      - 金融業: 有特殊的金融相關現金流量項目")
        print(f"      - 製造業: 有存貨、應收帳款等營運資金項目")
        print(f"      - 服務業: 現金流量結構相對簡單")
        print(f"   3. 📅 會計準則變更: 不同年度的會計科目可能有調整")
        print(f"   4. 💼 公司規模: 小公司可能沒有某些複雜的財務活動")
        print(f"   5. ⚡ 實際發生: 只有當該項目實際發生時才會有數值")
        
        # 9. 範例資料展示
        print(f"\n📊 範例資料展示 (台積電 2330):")
        tsmc_data = df[df['stock_id'] == '2330'].head(3)
        
        if not tsmc_data.empty:
            for _, row in tsmc_data.iterrows():
                print(f"\n   📅 {row['date']} ({row['stock_name']}):")
                
                # 顯示有數值的主要項目
                main_items = [
                    '本期稅前淨利（淨損）',
                    '營業活動之淨現金流入（流出）',
                    '投資活動之淨現金流入（流出）',
                    '籌資活動之淨現金流入（流出）',
                    '期末現金及約當現金餘額'
                ]
                
                for item in main_items:
                    if item in row and pd.notna(row[item]):
                        value = row[item]
                        if abs(value) >= 1000:
                            print(f"      {item:<25} {value:>15,.0f}")
                        else:
                            print(f"      {item:<25} {value:>15}")
        
        conn.close()
        
        print(f"\n✅ 分析完成！")
        return True
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_cash_flows_data()
