#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能排名功能展示
展示勝率73.45%策略中符合條件股票的智能排名
"""

import random

def demo_smart_ranking():
    """展示智能排名功能"""
    print("🎯 勝率73.45%策略 - 智能排名功能展示")
    print("=" * 60)
    
    # 模擬符合條件的股票數據
    qualified_stocks = [
        {
            "股票代碼": "2330",
            "股票名稱": "台積電",
            "收盤價": 580.0,
            "條件結果": [
                {"matched": True, "message": "MA240 582.5 > 昨日 580.2"},
                {"matched": True, "message": "MA240未來趨勢 強勢上升"},
                {"matched": True, "message": "MA20 585.3 > 5日前 578.1"},
                {"matched": True, "message": "成交量 大幅放大 1.8倍"},
                {"matched": True, "message": "RSI 強勢區間 65.2"},
                {"matched": True, "message": "成交額 158億 > 100億"}
            ]
        },
        {
            "股票代碼": "2317",
            "股票名稱": "鴻海",
            "收盤價": 110.5,
            "條件結果": [
                {"matched": True, "message": "MA240 111.2 > 昨日 110.8"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 112.1 > 5日前 109.5"},
                {"matched": True, "message": "成交量 放大 1.4倍"},
                {"matched": True, "message": "RSI 適中 58.7"},
                {"matched": True, "message": "成交額 125億 > 100億"}
            ]
        },
        {
            "股票代碼": "2454",
            "股票名稱": "聯發科",
            "收盤價": 1200.0,
            "條件結果": [
                {"matched": True, "message": "MA240 1205.3 > 昨日 1202.1"},
                {"matched": True, "message": "MA240未來趨勢 明顯上升"},
                {"matched": True, "message": "MA20 1210.5 > 5日前 1195.2"},
                {"matched": True, "message": "成交量 明顯放大 2.1倍"},
                {"matched": True, "message": "RSI 強勢 68.9"},
                {"matched": True, "message": "成交額 180億 > 100億"}
            ]
        },
        {
            "股票代碼": "1301",
            "股票名稱": "台塑",
            "收盤價": 85.3,
            "條件結果": [
                {"matched": True, "message": "MA240 85.8 > 昨日 85.6"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 86.2 > 5日前 84.1"},
                {"matched": True, "message": "成交量 增加 1.2倍"},
                {"matched": True, "message": "RSI 適中 55.3"},
                {"matched": True, "message": "成交額 105億 > 100億"}
            ]
        },
        {
            "股票代碼": "2412",
            "股票名稱": "中華電",
            "收盤價": 125.6,
            "條件結果": [
                {"matched": True, "message": "MA240 126.1 > 昨日 125.9"},
                {"matched": True, "message": "MA240未來趨勢 上升"},
                {"matched": True, "message": "MA20 126.8 > 5日前 124.2"},
                {"matched": True, "message": "成交量 放大 1.3倍"},
                {"matched": True, "message": "RSI 適中 52.1"},
                {"matched": True, "message": "成交額 112億 > 100億"}
            ]
        }
    ]
    
    print("📊 原始符合條件的股票 (未排名):")
    print("-" * 40)
    for i, stock in enumerate(qualified_stocks, 1):
        print(f"{i}. {stock['股票代碼']} {stock['股票名稱']} - ${stock['收盤價']}")
    
    # 應用智能排名算法
    ranked_stocks = calculate_smart_ranking(qualified_stocks)
    
    print(f"\n🏆 智能排名結果:")
    print("-" * 40)
    print(f"{'排名':<4} {'代碼':<6} {'名稱':<8} {'價格':<8} {'評分':<6} {'優勢分析'}")
    print("-" * 60)
    
    for i, stock in enumerate(ranked_stocks, 1):
        score = stock.get('smart_score', 0)
        advantages = analyze_advantages(stock)
        print(f"#{i:<3} {stock['股票代碼']:<6} {stock['股票名稱']:<8} ${stock['收盤價']:<7.1f} {score:<6.1f} {advantages}")
    
    return ranked_stocks

def calculate_smart_ranking(qualified_stocks):
    """計算智能排名 (模擬主程式邏輯)"""
    for stock in qualified_stocks:
        score = 0
        stock_id = stock["股票代碼"]
        price = stock.get("收盤價", 0)
        
        # 評分標準1: 價格合理性 (0-25分)
        if 20 <= price <= 100:
            score += 25  # 中價股
        elif 100 < price <= 300:
            score += 20  # 高價股
        elif 10 <= price < 20:
            score += 15  # 低價股
        else:
            score += 10  # 極端價格
        
        # 評分標準2: 條件符合強度 (0-30分)
        condition_strength = 0
        for condition_result in stock["條件結果"]:
            if condition_result["matched"]:
                message = condition_result["message"]
                if "強勢" in message or "大幅" in message or "明顯" in message:
                    condition_strength += 6
                elif "上升" in message or "增加" in message or "放大" in message:
                    condition_strength += 5
                else:
                    condition_strength += 4
        score += min(condition_strength, 30)
        
        # 評分標準3: 技術指標優勢 (0-25分)
        tech_score = 0
        for condition_result in stock["條件結果"]:
            message = condition_result["message"]
            if "RSI" in message and "強勢" in message:
                tech_score += 8
            elif "成交量" in message and ("大幅" in message or "明顯" in message):
                tech_score += 6
            elif "MA240" in message and "明顯" in message:
                tech_score += 6
        score += min(tech_score, 25)
        
        # 評分標準4: 股票代碼偏好 (0-10分)
        if stock_id.startswith("23"):  # 電子股
            score += 8
        elif stock_id.startswith("24"):  # 半導體
            score += 10
        elif stock_id.startswith("13"):  # 塑化
            score += 6
        else:
            score += 4
        
        # 評分標準5: 隨機因子 (0-10分)
        random.seed(hash(stock_id) % 1000)
        score += random.randint(0, 10)
        
        stock["smart_score"] = min(score, 100)
    
    # 按評分排序
    qualified_stocks.sort(key=lambda x: x.get("smart_score", 0), reverse=True)
    return qualified_stocks

def analyze_advantages(stock):
    """分析股票優勢"""
    advantages = []
    
    # 分析條件強度
    strong_conditions = 0
    for condition in stock["條件結果"]:
        message = condition["message"]
        if "強勢" in message or "大幅" in message or "明顯" in message:
            strong_conditions += 1
    
    if strong_conditions >= 3:
        advantages.append("多項強勢指標")
    elif strong_conditions >= 2:
        advantages.append("雙重強勢")
    
    # 分析價格優勢
    price = stock["收盤價"]
    if 50 <= price <= 200:
        advantages.append("價格合理")
    elif price > 500:
        advantages.append("龍頭股")
    
    # 分析行業優勢
    stock_id = stock["股票代碼"]
    if stock_id.startswith("23"):
        advantages.append("電子龍頭")
    elif stock_id.startswith("24"):
        advantages.append("半導體")
    
    return " | ".join(advantages) if advantages else "穩健標的"

def show_ranking_benefits():
    """展示排名功能的好處"""
    print(f"\n💡 智能排名功能的價值")
    print("-" * 30)
    
    benefits = [
        "🎯 精準篩選 - 從符合條件的股票中挑出最優質的",
        "📊 量化評分 - 客觀的評分標準，避免主觀判斷",
        "🏆 優先順序 - 清楚知道應該優先關注哪些股票",
        "⚡ 節省時間 - 不用逐一分析，直接看排名前幾名",
        "🎪 多重考量 - 綜合價格、技術指標、行業等因素",
        "🔄 動態調整 - 每次篩選都會重新排名",
        "💰 提高勝率 - 優先投資排名靠前的優質標的"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")

def show_ranking_criteria():
    """展示排名標準"""
    print(f"\n📋 智能排名評分標準")
    print("-" * 25)
    
    criteria = [
        {
            "標準": "價格合理性",
            "權重": "25%",
            "說明": "中價股(20-100)最高分，極端價格扣分"
        },
        {
            "標準": "條件符合強度", 
            "權重": "30%",
            "說明": "強勢/大幅/明顯 > 上升/放大 > 一般符合"
        },
        {
            "標準": "技術指標優勢",
            "權重": "25%", 
            "說明": "RSI強勢、成交量大幅放大、趨勢明顯加分"
        },
        {
            "標準": "行業偏好",
            "權重": "10%",
            "說明": "半導體>電子>其他行業"
        },
        {
            "標準": "隨機因子",
            "權重": "10%",
            "說明": "確保同股票分數一致的隨機調整"
        }
    ]
    
    for criterion in criteria:
        print(f"📊 {criterion['標準']} ({criterion['權重']})")
        print(f"   {criterion['說明']}")
        print()

def main():
    """主函數"""
    print("🚀 智能排名功能完整展示")
    print("=" * 50)
    
    # 展示排名功能
    ranked_stocks = demo_smart_ranking()
    
    # 展示排名標準
    show_ranking_criteria()
    
    # 展示功能價值
    show_ranking_benefits()
    
    # 使用建議
    print(f"\n🎯 使用建議")
    print("-" * 15)
    print("1. 🥇 優先關注排名前3名的股票")
    print("2. 📊 參考評分差異，分數相近可同時考慮")
    print("3. 💰 建議投資排名前5名，分散風險")
    print("4. 🔄 每次篩選後重新檢視排名變化")
    print("5. ⚠️ 排名僅供參考，仍需結合基本面分析")
    
    print(f"\n🎉 現在您可以在右側表格看到智能排名了！")
    print("✨ 符合條件的股票會顯示金色的排名編號")
    print("🏆 排名越前面，投資價值越高！")

if __name__ == "__main__":
    main()
