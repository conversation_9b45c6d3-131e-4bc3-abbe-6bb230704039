"""
統一股票資料庫系統
整合每日交易資料、基本資料和歷史價格資料的統一管理系統
可完全替代原有的price.db
"""

import sqlite3
import pandas as pd
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UnifiedStockDatabase:
    """統一股票資料庫管理類"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # 預設路徑：與price.db相同目錄
            default_dir = r"D:\Finlab\history\tables"
            os.makedirs(default_dir, exist_ok=True)
            self.db_path = os.path.join(default_dir, "unified_stock_data.db")
        else:
            self.db_path = db_path
        
        self.init_database()
        logger.info(f"統一股票資料庫初始化完成: {self.db_path}")
    
    def init_database(self):
        """初始化資料庫結構 - 完全相容price.db格式"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 1. 主要交易資料表 (完全相容price.db的stock_daily_data表 - 簡化版)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT NOT NULL,
                date TEXT NOT NULL,
                "Open" REAL,
                High REAL,
                Low REAL,
                "Close" REAL,
                Volume REAL,
                PRIMARY KEY (stock_id, date)
            )
        ''')

        # 2. 股票基本資料表 (新增 - 簡化版，只保留必要欄位)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                stock_id TEXT PRIMARY KEY,
                stock_name TEXT,
                listing_status TEXT,
                industry TEXT
            )
        ''')



        # 建立索引優化查詢效能 (使用price.db相容的欄位名稱)
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_daily_data(stock_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_date ON stock_daily_data(date)",
            "CREATE INDEX IF NOT EXISTS idx_stock_id ON stock_daily_data(stock_id)",
            "CREATE INDEX IF NOT EXISTS idx_stock_info_id ON stock_info(stock_id)",
            "CREATE INDEX IF NOT EXISTS idx_industry ON stock_info(industry)"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

        conn.commit()
        conn.close()
    
    def save_daily_data(self, df: pd.DataFrame, data_source: str = 'unified') -> int:
        """儲存每日交易資料 - 使用price.db相容格式"""
        if df.empty:
            return 0

        conn = sqlite3.connect(self.db_path)
        try:
            # 標準化欄位名稱為price.db格式
            df_clean = self._standardize_daily_columns(df.copy())

            # 使用REPLACE INTO避免重複資料，只保留price.db的核心欄位
            cursor = conn.cursor()
            for _, row in df_clean.iterrows():
                cursor.execute('''
                    INSERT OR REPLACE INTO stock_daily_data
                    (stock_id, date, "Open", High, Low, "Close", Volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row.get('stock_id', ''),
                    row.get('date', ''),
                    row.get('Open'),
                    row.get('High'),
                    row.get('Low'),
                    row.get('Close'),
                    row.get('Volume')
                ))

            conn.commit()
            saved_count = len(df_clean)
            logger.info(f"成功儲存 {saved_count} 筆每日交易資料")

            return saved_count

        except Exception as e:
            logger.error(f"儲存每日交易資料失敗: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def save_basic_info(self, df: pd.DataFrame) -> int:
        """儲存股票基本資料"""
        if df.empty:
            return 0

        conn = sqlite3.connect(self.db_path)
        try:
            # 標準化欄位名稱
            df_clean = self._standardize_basic_columns(df.copy())

            # 使用REPLACE INTO避免重複資料，只保留必要欄位
            cursor = conn.cursor()
            for _, row in df_clean.iterrows():
                cursor.execute('''
                    INSERT OR REPLACE INTO stock_info
                    (stock_id, stock_name, listing_status, industry)
                    VALUES (?, ?, ?, ?)
                ''', (
                    row.get('stock_id', ''),
                    row.get('stock_name', ''),
                    row.get('listing_status', ''),
                    row.get('industry', '')
                ))

            conn.commit()
            saved_count = len(df_clean)
            logger.info(f"成功儲存 {saved_count} 筆基本資料")

            return saved_count

        except Exception as e:
            logger.error(f"儲存基本資料失敗: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def _standardize_daily_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """標準化每日交易資料欄位名稱 - 保持price.db格式"""
        column_mapping = {
            # daily_trading.db格式轉換為price.db格式
            'StockID': 'stock_id',
            'Date': 'date',
            'Max': 'High',
            'Min': 'Low',
            'TradeVolume': 'Volume',
            'TradeValue': 'TradeValue',
            'TransactionCount': 'Transaction',
            'Change': 'Change',

            # 其他可能的格式
            'open_price': 'Open',
            'high_price': 'High',
            'low_price': 'Low',
            'close_price': 'Close',
            'volume': 'Volume',
            'trade_value': 'TradeValue',
            'transaction_count': 'Transaction',
            'change_amount': 'Change'
        }

        df_clean = df.copy()

        # 重新命名欄位為price.db格式
        for old_name, new_name in column_mapping.items():
            if old_name in df_clean.columns:
                df_clean = df_clean.rename(columns={old_name: new_name})

        # 確保必要欄位存在，使用price.db的欄位名稱
        required_columns = {
            'stock_id': '',
            'stock_name': '',
            'listing_status': '',
            'industry': '',
            'Volume': 0,
            'Transaction': 0,
            'TradeValue': 0,
            'Open': 0.0,
            'High': 0.0,
            'Low': 0.0,
            'Close': 0.0,
            'Change': 0.0,
            'date': ''
        }

        for col, default_value in required_columns.items():
            if col not in df_clean.columns:
                df_clean[col] = default_value

        return df_clean
    
    def _standardize_basic_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """標準化基本資料欄位名稱 - 簡化版"""
        column_mapping = {
            'company_code': 'stock_id',
            'company_name': 'stock_name',
            'industry_category': 'industry',
            'market_type': 'listing_status'
        }

        df_clean = df.copy()

        # 重新命名欄位
        for old_name, new_name in column_mapping.items():
            if old_name in df_clean.columns:
                df_clean = df_clean.rename(columns={old_name: new_name})

        return df_clean
    

    def get_stock_data(self, stock_id: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """獲取股票交易資料 (完全相容price.db的查詢方式)"""
        conn = sqlite3.connect(self.db_path)

        # 使用price.db的確切欄位名稱和表名
        query = '''
            SELECT stock_id, date, "Open", High, Low, "Close", Volume
            FROM stock_daily_data
            WHERE stock_id = ?
        '''
        params = [stock_id]

        if start_date:
            query += ' AND date >= ?'
            params.append(start_date)

        if end_date:
            query += ' AND date <= ?'
            params.append(end_date)

        query += ' ORDER BY date'

        try:
            df = pd.read_sql_query(query, conn, params=params)
            return df
        except Exception as e:
            logger.error(f"查詢股票資料失敗: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """獲取股票清單 - 相容price.db格式"""
        conn = sqlite3.connect(self.db_path)

        query = '''
            SELECT DISTINCT s.stock_id,
                   COALESCE(i.stock_name, '') as stock_name,
                   COALESCE(i.listing_status, '') as listing_status,
                   COALESCE(i.industry, '') as industry
            FROM stock_daily_data s
            LEFT JOIN stock_info i ON s.stock_id = i.stock_id
            ORDER BY s.stock_id
        '''

        try:
            df = pd.read_sql_query(query, conn)
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"查詢股票清單失敗: {e}")
            return []
        finally:
            conn.close()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """獲取資料庫統計資訊 - 使用price.db相容的表名"""
        conn = sqlite3.connect(self.db_path)
        stats = {}

        try:
            cursor = conn.cursor()

            # 每日交易資料統計 (使用stock_daily_data表)
            cursor.execute('SELECT COUNT(*) FROM stock_daily_data')
            stats['daily_records'] = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data')
            stats['stocks_with_data'] = cursor.fetchone()[0]

            cursor.execute('SELECT MIN(date), MAX(date) FROM stock_daily_data')
            date_range = cursor.fetchone()
            stats['date_range'] = {'start': date_range[0], 'end': date_range[1]}

            # 基本資料統計 (使用stock_info表)
            try:
                cursor.execute('SELECT COUNT(*) FROM stock_info')
                stats['basic_info_records'] = cursor.fetchone()[0]
            except:
                stats['basic_info_records'] = 0

            # 資料來源統計
            try:
                cursor.execute('''
                    SELECT data_source, COUNT(*) as count
                    FROM stock_daily_data
                    WHERE data_source IS NOT NULL
                    GROUP BY data_source
                ''')
                stats['data_sources'] = dict(cursor.fetchall())
            except:
                stats['data_sources'] = {}

            return stats

        except Exception as e:
            logger.error(f"獲取統計資訊失敗: {e}")
            return {}
        finally:
            conn.close()


if __name__ == "__main__":
    # 測試統一資料庫
    db = UnifiedStockDatabase()
    stats = db.get_database_stats()
    print("統一股票資料庫統計:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
