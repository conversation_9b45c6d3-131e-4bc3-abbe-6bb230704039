#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據庫配置對話框
用於設定價格數據庫和PE數據庫的路徑
"""

import os
import sqlite3
import json
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QPushButton, QLabel, QLineEdit, QFileDialog,
                            QGroupBox, QTextEdit, QMessageBox, QTabWidget,
                            QWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QAbstractItemView)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

class DatabaseConfigDialog(QDialog):
    """數據庫配置對話框"""
    
    # 信號：當數據庫配置更新時發出
    database_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None, current_config=None):
        super().__init__(parent)
        self.current_config = current_config or {}
        self.init_ui()
        self.load_current_config()

    def create_custom_title_bar(self):
        """創建自定義標題欄"""
        # 移除系統標題欄
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)

        # 創建標題欄容器
        title_bar = QWidget()
        title_bar.setFixedHeight(40)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: #2a2a2a;
                border-bottom: 2px solid #555555;
            }
        """)

        # 標題欄佈局
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 5, 0)
        title_layout.setSpacing(10)

        # 標題文字
        self.title_label = QLabel("🗄️ 數據庫配置管理")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 15px;
                font-weight: 700;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                background: transparent;
                border: none;
                padding: 0px 5px;
            }
        """)
        title_layout.addWidget(self.title_label)

        # 設置標題欄可拖拽
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        self.title_label.mousePressEvent = self.title_bar_mouse_press
        self.title_label.mouseMoveEvent = self.title_bar_mouse_move

        # 彈性空間
        title_layout.addStretch()

        # 窗口控制按鈕
        self.create_window_controls(title_layout)

        # 保存標題欄組件，稍後添加到主佈局
        self.title_bar_widget = title_bar

    def create_window_controls(self, layout):
        """創建窗口控制按鈕"""
        # 最小化按鈕
        minimize_btn = QPushButton("🗕")
        minimize_btn.setFixedSize(32, 30)
        minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                font-family: 'Segoe UI Symbol', 'Microsoft JhengHei UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #505050;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
        """)
        minimize_btn.clicked.connect(self.showMinimized)
        minimize_btn.setToolTip("最小化")
        layout.addWidget(minimize_btn)

        # 最大化/還原按鈕
        self.maximize_btn = QPushButton("🗖")
        self.maximize_btn.setFixedSize(32, 30)
        self.maximize_btn.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                font-family: 'Segoe UI Symbol', 'Microsoft JhengHei UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #505050;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
        """)
        self.maximize_btn.clicked.connect(self.toggle_maximize)
        self.maximize_btn.setToolTip("最大化")
        layout.addWidget(self.maximize_btn)

        # 關閉按鈕
        close_btn = QPushButton("🗙")
        close_btn.setFixedSize(32, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: #ffffff;
                border: 1px solid #b71c1c;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                font-family: 'Segoe UI Symbol', 'Microsoft JhengHei UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #f44336;
                border-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        close_btn.clicked.connect(self.close)
        close_btn.setToolTip("關閉")
        layout.addWidget(close_btn)

    def toggle_maximize(self):
        """切換最大化/還原狀態"""
        if self.isMaximized():
            self.showNormal()
            self.maximize_btn.setText("🗖")
            self.maximize_btn.setToolTip("最大化")
        else:
            self.showMaximized()
            self.maximize_btn.setText("🗗")
            self.maximize_btn.setToolTip("還原")

    def title_bar_mouse_press(self, event):
        """標題欄滑鼠按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def title_bar_mouse_move(self, event):
        """標題欄滑鼠移動事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def init_ui(self):
        """初始化用戶界面"""
        self.setWindowTitle("數據庫配置管理")
        self.setGeometry(200, 200, 900, 700)
        self.setMinimumSize(800, 600)  # 設置最小尺寸

        # 創建自定義標題欄
        self.create_custom_title_bar()

        # 設置深色主題樣式
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
                color: #ffffff;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                font-size: 13px;
                font-weight: 500;
            }
            QTabWidget {
                background-color: #1a1a1a;
                color: #ffffff;
                font-size: 13px;
            }
            QTabWidget::pane {
                border: 2px solid #555555;
                background-color: #2a2a2a;
                border-radius: 8px;
                top: -1px;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background-color: #333333;
                color: #ffffff;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 13px;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
                color: #ffffff;
                font-weight: 700;
            }
            QTabBar::tab:hover {
                background-color: #444444;
            }
            QGroupBox {
                font-weight: 600;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: #2a2a2a;
                color: #ffffff;
                font-size: 13px;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #00aaff;
                font-size: 14px;
                font-weight: 700;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                background-color: #2a2a2a;
            }
            QLabel {
                color: #ffffff;
                font-weight: 500;
                background-color: transparent;
                font-size: 13px;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                padding: 4px;
            }
            QLineEdit {
                background-color: #333333;
                border: 2px solid #555555;
                border-radius: 6px;
                padding: 10px;
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
                background-color: #404040;
            }
            QLineEdit:hover {
                border: 2px solid #777777;
            }
            QPushButton {
                background-color: #0078d4;
                color: #ffffff;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: 600;
                font-size: 13px;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QCheckBox {
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555555;
                border-radius: 4px;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 2px solid #0078d4;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #777777;
            }
            QTextEdit {
                background-color: #1f1f1f;
                color: #ffffff;
                border: 2px solid #555555;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.4;
            }
            QTableWidget {
                background-color: #1f1f1f;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #404040;
                border: 2px solid #555555;
                border-radius: 6px;
                font-size: 12px;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 10px;
                border: 1px solid #555555;
                font-weight: 600;
                font-size: 12px;
                font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
            }
        """)
        
        # 主佈局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # 如果已經有標題欄，先添加它
        if hasattr(self, 'title_bar_widget'):
            self.main_layout.addWidget(self.title_bar_widget)

        # 內容區域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # 創建標籤頁
        self.tab_widget = QTabWidget()
        content_layout.addWidget(self.tab_widget)

        self.main_layout.addWidget(content_widget)
        
        # 1. 數據庫路徑配置標籤
        self.create_path_config_tab()

        # 2. 數據庫信息查看標籤
        self.create_database_info_tab()

        # 3. 價格數據庫預覽標籤
        self.create_price_data_tab()

        # 4. PE數據預覽標籤
        self.create_pe_data_tab()

        # 5. 新聞資料庫標籤
        self.create_news_database_tab()

        # 6. 除權息資料庫標籤
        self.create_dividend_database_tab()

        # 7. 月營收資料庫標籤
        self.create_monthly_revenue_database_tab()

        # 按鈕區域
        button_layout = QHBoxLayout()

        self.test_btn = QPushButton("🔍 測試連接")
        self.test_btn.clicked.connect(self.test_connections)
        button_layout.addWidget(self.test_btn)

        self.save_btn = QPushButton("💾 保存配置")
        self.save_btn.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        content_layout.addLayout(button_layout)
        
    def create_path_config_tab(self):
        """創建數據庫路徑配置標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 價格數據庫配置
        price_group = QGroupBox("價格數據庫 (price.db)")
        price_layout = QGridLayout(price_group)
        
        price_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.price_db_path = QLineEdit()
        self.price_db_path.setPlaceholderText("D:/Finlab/history/tables/price.db")
        price_layout.addWidget(self.price_db_path, 0, 1)
        
        self.price_browse_btn = QPushButton("瀏覽...")
        self.price_browse_btn.clicked.connect(lambda: self.browse_database('price'))
        price_layout.addWidget(self.price_browse_btn, 0, 2)
        
        self.price_status_label = QLabel("狀態: 未檢測")
        price_layout.addWidget(self.price_status_label, 1, 0, 1, 3)
        
        layout.addWidget(price_group)
        
        # PE數據庫配置
        pe_group = QGroupBox("PE數據庫 (pe_data.db)")
        pe_layout = QGridLayout(pe_group)
        
        pe_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.pe_db_path = QLineEdit()
        self.pe_db_path.setPlaceholderText("D:/Finlab/history/tables/pe_data.db")
        pe_layout.addWidget(self.pe_db_path, 0, 1)
        
        self.pe_browse_btn = QPushButton("瀏覽...")
        self.pe_browse_btn.clicked.connect(lambda: self.browse_database('pe'))
        pe_layout.addWidget(self.pe_browse_btn, 0, 2)
        
        self.pe_status_label = QLabel("狀態: 未檢測")
        pe_layout.addWidget(self.pe_status_label, 1, 0, 1, 3)
        
        # PE數據庫啟用選項
        self.pe_enabled_checkbox = QCheckBox("啟用PE數據庫（為策略提供完整基本面數據）")
        self.pe_enabled_checkbox.setChecked(True)
        pe_layout.addWidget(self.pe_enabled_checkbox, 2, 0, 1, 3)
        
        layout.addWidget(pe_group)

        # 新聞資料庫配置
        news_group = QGroupBox("新聞資料庫 (news.db)")
        news_layout = QGridLayout(news_group)

        news_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.news_db_path = QLineEdit()
        self.news_db_path.setPlaceholderText("D:/Finlab/history/tables/news.db")
        news_layout.addWidget(self.news_db_path, 0, 1)

        self.news_browse_btn = QPushButton("瀏覽...")
        self.news_browse_btn.clicked.connect(lambda: self.browse_database('news'))
        news_layout.addWidget(self.news_browse_btn, 0, 2)

        self.news_status_label = QLabel("狀態: 未檢測")
        news_layout.addWidget(self.news_status_label, 1, 0, 1, 3)

        # 新聞資料庫啟用選項
        self.news_enabled_checkbox = QCheckBox("啟用新聞資料庫（提供財經新聞爬蟲功能）")
        self.news_enabled_checkbox.setChecked(True)
        news_layout.addWidget(self.news_enabled_checkbox, 2, 0, 1, 3)

        layout.addWidget(news_group)

        # 除權息資料庫配置
        dividend_group = QGroupBox("除權息資料庫 (dividend_data.db)")
        dividend_layout = QGridLayout(dividend_group)

        dividend_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.dividend_db_path = QLineEdit()
        self.dividend_db_path.setPlaceholderText("D:/Finlab/history/tables/dividend_data.db")
        dividend_layout.addWidget(self.dividend_db_path, 0, 1)

        self.dividend_browse_btn = QPushButton("瀏覽...")
        self.dividend_browse_btn.clicked.connect(lambda: self.browse_database('dividend'))
        dividend_layout.addWidget(self.dividend_browse_btn, 0, 2)

        self.dividend_status_label = QLabel("狀態: 未檢測")
        dividend_layout.addWidget(self.dividend_status_label, 1, 0, 1, 3)

        # 除權息資料庫啟用選項
        self.dividend_enabled_checkbox = QCheckBox("啟用除權息資料庫（提供除權息交易系統數據支援）")
        self.dividend_enabled_checkbox.setChecked(True)
        dividend_layout.addWidget(self.dividend_enabled_checkbox, 2, 0, 1, 3)

        layout.addWidget(dividend_group)

        # 月營收資料庫配置
        monthly_revenue_group = QGroupBox("月營收資料庫 (monthly_revenue.db)")
        monthly_revenue_layout = QGridLayout(monthly_revenue_group)

        monthly_revenue_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.monthly_revenue_db_path = QLineEdit()
        self.monthly_revenue_db_path.setPlaceholderText("D:/Finlab/history/tables/monthly_revenue.db")
        monthly_revenue_layout.addWidget(self.monthly_revenue_db_path, 0, 1)

        self.monthly_revenue_browse_btn = QPushButton("瀏覽...")
        self.monthly_revenue_browse_btn.clicked.connect(lambda: self.browse_database('monthly_revenue'))
        monthly_revenue_layout.addWidget(self.monthly_revenue_browse_btn, 0, 2)

        self.monthly_revenue_status_label = QLabel("狀態: 未檢測")
        monthly_revenue_layout.addWidget(self.monthly_revenue_status_label, 1, 0, 1, 3)

        # 月營收資料庫啟用選項
        self.monthly_revenue_enabled_checkbox = QCheckBox("啟用月營收資料庫（提供月營收資料查詢和分析功能）")
        self.monthly_revenue_enabled_checkbox.setChecked(True)
        monthly_revenue_layout.addWidget(self.monthly_revenue_enabled_checkbox, 2, 0, 1, 3)

        layout.addWidget(monthly_revenue_group)

        # 自動檢測按鈕
        auto_detect_btn = QPushButton("🔍 自動檢測數據庫")
        auto_detect_btn.clicked.connect(self.auto_detect_databases)
        layout.addWidget(auto_detect_btn)
        
        # 說明文字
        info_text = QLabel("""
📋 數據庫說明：
• 價格數據庫：包含股票的OHLCV數據，用於技術分析
• PE數據庫：包含本益比、殖利率等基本面數據，用於價值分析
• 新聞資料庫：包含財經新聞資料，用於新聞爬蟲和情報分析
• 啟用PE數據庫後，策略將使用真實基本面數據而非模擬數據
• 建議將所有數據庫都放在同一目錄下便於管理
        """)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("""
            background-color: #2a2a2a;
            color: #ffffff;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #555555;
            font-size: 13px;
            font-weight: 500;
            font-family: 'Microsoft JhengHei UI', 'Microsoft JhengHei', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
        """)
        layout.addWidget(info_text)
        
        self.tab_widget.addTab(tab, "📁 路徑配置")
        
    def create_database_info_tab(self):
        """創建數據庫信息查看標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.db_info_text = QTextEdit()
        self.db_info_text.setReadOnly(True)
        self.db_info_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.db_info_text)
        
        refresh_btn = QPushButton("🔄 刷新信息")
        refresh_btn.clicked.connect(self.refresh_database_info)
        layout.addWidget(refresh_btn)
        
        self.tab_widget.addTab(tab, "📊 數據庫信息")
        
    def create_pe_data_tab(self):
        """創建PE數據預覽標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 說明標籤
        info_label = QLabel("PE數據庫預覽（前20筆記錄）")
        info_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(info_label)
        
        # PE數據表格
        self.pe_data_table = QTableWidget()
        layout.addWidget(self.pe_data_table)
        
        # 刷新按鈕
        refresh_pe_btn = QPushButton("🔄 刷新PE數據")
        refresh_pe_btn.clicked.connect(self.refresh_pe_data)
        layout.addWidget(refresh_pe_btn)
        
        self.tab_widget.addTab(tab, "📈 PE數據預覽")
        
    def load_current_config(self):
        """載入當前配置"""
        if self.current_config:
            db_config = self.current_config.get('database', {})
            self.price_db_path.setText(db_config.get('price_db_path', ''))
            self.pe_db_path.setText(db_config.get('pe_db_path', ''))
            self.pe_enabled_checkbox.setChecked(db_config.get('pe_enabled', True))
            self.news_db_path.setText(db_config.get('news_db_path', ''))
            self.news_enabled_checkbox.setChecked(db_config.get('news_enabled', True))
            self.monthly_revenue_db_path.setText(db_config.get('monthly_revenue_db_path', ''))
            self.monthly_revenue_enabled_checkbox.setChecked(db_config.get('monthly_revenue_enabled', True))

        # 自動檢測
        self.auto_detect_databases()
        
    def browse_database(self, db_type):
        """瀏覽選擇數據庫文件"""
        titles = {
            'price': "選擇價格數據庫",
            'pe': "選擇PE數據庫",
            'news': "選擇新聞資料庫",
            'dividend': "選擇除權息資料庫",
            'monthly_revenue': "選擇月營收資料庫"
        }
        title = titles.get(db_type, "選擇數據庫")

        file_path, _ = QFileDialog.getOpenFileName(
            self, title, "D:/Finlab/history/tables/",
            "Database files (*.db);;All files (*)")

        if file_path:
            if db_type == 'price':
                self.price_db_path.setText(file_path)
            elif db_type == 'pe':
                self.pe_db_path.setText(file_path)
            elif db_type == 'news':
                self.news_db_path.setText(file_path)
            elif db_type == 'dividend':
                self.dividend_db_path.setText(file_path)
            elif db_type == 'monthly_revenue':
                self.monthly_revenue_db_path.setText(file_path)

            self.test_single_database(db_type, file_path)
            
    def auto_detect_databases(self):
        """自動檢測數據庫"""
        base_paths = [
            "D:/Finlab/history/tables/",
            "./data/",
            "./db/",
            "./"
        ]
        
        # 檢測價格數據庫
        for base_path in base_paths:
            price_path = os.path.join(base_path, "price.db")
            if os.path.exists(price_path):
                self.price_db_path.setText(price_path)
                break
        
        # 檢測PE數據庫
        for base_path in base_paths:
            pe_path = os.path.join(base_path, "pe_data.db")
            if os.path.exists(pe_path):
                self.pe_db_path.setText(pe_path)
                break

        # 檢測新聞資料庫
        for base_path in base_paths:
            news_path = os.path.join(base_path, "news.db")
            if os.path.exists(news_path):
                self.news_db_path.setText(news_path)
                break

        # 檢測除權息資料庫
        for base_path in base_paths:
            dividend_path = os.path.join(base_path, "dividend_data.db")
            if os.path.exists(dividend_path):
                self.dividend_db_path.setText(dividend_path)
                break

        # 檢測月營收資料庫
        monthly_revenue_paths = [
            "D:/Finlab/history/tables/monthly_revenue/monthly_revenue.db",
            "./monthly_revenue/monthly_revenue.db",
            "./data/monthly_revenue.db",
            "./monthly_revenue.db"
        ]
        for monthly_revenue_path in monthly_revenue_paths:
            if os.path.exists(monthly_revenue_path):
                self.monthly_revenue_db_path.setText(monthly_revenue_path)
                break

        # 測試連接
        self.test_connections()
        
    def test_single_database(self, db_type, db_path):
        """測試單個數據庫連接"""
        if not os.path.exists(db_path):
            status = "❌ 文件不存在"
        else:
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 獲取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                if tables:
                    # 檢查主要表的記錄數
                    main_table = tables[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {main_table}")
                    count = cursor.fetchone()[0]
                    status = f"✅ 連接成功 ({len(tables)} 個表, {count:,} 筆記錄)"
                else:
                    status = "⚠️ 數據庫為空"
                
                conn.close()
                
            except Exception as e:
                status = f"❌ 連接失敗: {str(e)[:30]}..."
        
        # 更新狀態顯示
        if db_type == 'price':
            self.price_status_label.setText(f"狀態: {status}")
        elif db_type == 'pe':
            self.pe_status_label.setText(f"狀態: {status}")
        elif db_type == 'news':
            self.news_status_label.setText(f"狀態: {status}")
        elif db_type == 'dividend':
            self.dividend_status_label.setText(f"狀態: {status}")
        elif db_type == 'monthly_revenue':
            self.monthly_revenue_status_label.setText(f"狀態: {status}")
            
    def test_connections(self):
        """測試所有數據庫連接"""
        price_path = self.price_db_path.text().strip()
        pe_path = self.pe_db_path.text().strip()
        news_path = self.news_db_path.text().strip()
        dividend_path = self.dividend_db_path.text().strip()
        monthly_revenue_path = self.monthly_revenue_db_path.text().strip()

        if price_path:
            self.test_single_database('price', price_path)

        if pe_path:
            self.test_single_database('pe', pe_path)

        if news_path:
            self.test_single_database('news', news_path)

        if monthly_revenue_path:
            self.test_single_database('monthly_revenue', monthly_revenue_path)

        if dividend_path:
            self.test_single_database('dividend', dividend_path)

        # 刷新數據庫信息
        self.refresh_database_info()
        self.refresh_price_database_status()
        self.refresh_pe_data()
        self.refresh_news_database_status()
        self.refresh_dividend_database_status()
        
    def refresh_database_info(self):
        """刷新數據庫信息顯示"""
        info_text = "📊 數據庫詳細信息\n"
        info_text += "=" * 50 + "\n\n"
        
        # 價格數據庫信息
        price_path = self.price_db_path.text().strip()
        if price_path and os.path.exists(price_path):
            info_text += self.get_database_info("價格數據庫", price_path)
        else:
            info_text += "❌ 價格數據庫未配置或不存在\n\n"
        
        # PE數據庫信息
        pe_path = self.pe_db_path.text().strip()
        if pe_path and os.path.exists(pe_path):
            info_text += self.get_database_info("PE數據庫", pe_path)
        else:
            info_text += "❌ PE數據庫未配置或不存在\n\n"
        
        self.db_info_text.setPlainText(info_text)
        
    def get_database_info(self, db_name, db_path):
        """獲取數據庫詳細信息"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            info = f"📁 {db_name}\n"
            info += f"路徑: {db_path}\n"
            info += f"大小: {os.path.getsize(db_path) / 1024 / 1024:.2f} MB\n"
            
            # 獲取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            info += f"表數量: {len(tables)}\n"
            
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                info += f"  • {table}: {count:,} 筆記錄\n"
                
                # 如果是主要表，顯示欄位信息
                if 'stock' in table.lower() or table in ['pe_data', 'stock_daily_data']:
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    info += f"    欄位: {', '.join(columns)}\n"
            
            info += "\n"
            conn.close()
            
        except Exception as e:
            info = f"❌ {db_name} 讀取失敗: {str(e)}\n\n"
            
        return info

    def refresh_pe_data(self):
        """刷新PE數據預覽"""
        pe_path = self.pe_db_path.text().strip()

        if not pe_path or not os.path.exists(pe_path):
            self.pe_data_table.setRowCount(0)
            self.pe_data_table.setColumnCount(1)
            self.pe_data_table.setHorizontalHeaderLabels(["PE數據庫未配置或不存在"])
            return

        try:
            conn = sqlite3.connect(pe_path)
            cursor = conn.cursor()

            # 查找PE數據表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            pe_table = None
            for table in tables:
                if 'pe' in table.lower() or table.lower() in ['pe_data', 'fundamental_data']:
                    pe_table = table
                    break

            if not pe_table:
                pe_table = tables[0] if tables else None

            if pe_table:
                # 獲取表結構
                cursor.execute(f"PRAGMA table_info({pe_table})")
                columns_info = cursor.fetchall()
                column_names = [col[1] for col in columns_info]

                # 獲取前20筆數據
                cursor.execute(f"SELECT * FROM {pe_table} LIMIT 20")
                data = cursor.fetchall()

                # 設置表格
                self.pe_data_table.setRowCount(len(data))
                self.pe_data_table.setColumnCount(len(column_names))
                self.pe_data_table.setHorizontalHeaderLabels(column_names)

                # 填充數據
                for row_idx, row_data in enumerate(data):
                    for col_idx, cell_data in enumerate(row_data):
                        item = QTableWidgetItem(str(cell_data) if cell_data is not None else "NULL")
                        self.pe_data_table.setItem(row_idx, col_idx, item)

                # 調整欄位寬度
                self.pe_data_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

            else:
                self.pe_data_table.setRowCount(0)
                self.pe_data_table.setColumnCount(1)
                self.pe_data_table.setHorizontalHeaderLabels(["未找到PE數據表"])

            conn.close()

        except Exception as e:
            self.pe_data_table.setRowCount(1)
            self.pe_data_table.setColumnCount(1)
            self.pe_data_table.setHorizontalHeaderLabels(["錯誤"])
            self.pe_data_table.setItem(0, 0, QTableWidgetItem(f"讀取PE數據失敗: {str(e)}"))

    def save_config(self):
        """保存配置"""
        price_path = self.price_db_path.text().strip()
        pe_path = self.pe_db_path.text().strip()
        news_path = self.news_db_path.text().strip()
        dividend_path = self.dividend_db_path.text().strip()
        monthly_revenue_path = self.monthly_revenue_db_path.text().strip()

        # 驗證路徑
        if not price_path:
            QMessageBox.warning(self, "配置錯誤", "請設定價格數據庫路徑")
            return

        if not os.path.exists(price_path):
            QMessageBox.warning(self, "配置錯誤", f"價格數據庫文件不存在:\n{price_path}")
            return

        if self.pe_enabled_checkbox.isChecked() and pe_path and not os.path.exists(pe_path):
            QMessageBox.warning(self, "配置錯誤", f"PE數據庫文件不存在:\n{pe_path}")
            return

        if self.news_enabled_checkbox.isChecked() and news_path and not os.path.exists(news_path):
            QMessageBox.warning(self, "配置錯誤", f"新聞資料庫文件不存在:\n{news_path}")
            return

        if self.dividend_enabled_checkbox.isChecked() and dividend_path and not os.path.exists(dividend_path):
            QMessageBox.warning(self, "配置錯誤", f"除權息資料庫文件不存在:\n{dividend_path}")
            return

        if self.monthly_revenue_enabled_checkbox.isChecked() and monthly_revenue_path and not os.path.exists(monthly_revenue_path):
            QMessageBox.warning(self, "配置錯誤", f"月營收資料庫文件不存在:\n{monthly_revenue_path}")
            return

        # 構建配置
        new_config = {
            'database': {
                'price_db_path': price_path,
                'pe_db_path': pe_path,
                'pe_enabled': self.pe_enabled_checkbox.isChecked(),
                'news_db_path': news_path,
                'news_enabled': self.news_enabled_checkbox.isChecked(),
                'dividend_db_path': dividend_path,
                'dividend_enabled': self.dividend_enabled_checkbox.isChecked(),
                'monthly_revenue_db_path': monthly_revenue_path,
                'monthly_revenue_enabled': self.monthly_revenue_enabled_checkbox.isChecked(),
                'auto_detect': True
            }
        }

        # 合併現有配置
        if self.current_config:
            for key, value in self.current_config.items():
                if key != 'database':
                    new_config[key] = value

        try:
            # 保存到配置文件
            config_file = "app_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(new_config, f, indent=2, ensure_ascii=False)

            # 發出信號
            self.database_updated.emit(new_config)

            QMessageBox.information(self, "配置成功",
                                  f"數據庫配置已保存到 {config_file}\n\n"
                                  f"價格數據庫: {'✅' if os.path.exists(price_path) else '❌'}\n"
                                  f"PE數據庫: {'✅' if pe_path and os.path.exists(pe_path) else '❌'}\n"
                                  f"PE數據啟用: {'是' if self.pe_enabled_checkbox.isChecked() else '否'}")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "保存失敗", f"保存配置失敗:\n{str(e)}")

    def get_config(self):
        """獲取當前配置"""
        return {
            'database': {
                'price_db_path': self.price_db_path.text().strip(),
                'pe_db_path': self.pe_db_path.text().strip(),
                'pe_enabled': self.pe_enabled_checkbox.isChecked(),
                'news_db_path': self.news_db_path.text().strip(),
                'news_enabled': self.news_enabled_checkbox.isChecked(),
                'auto_detect': True
            }
        }

    def create_news_database_tab(self):
        """創建新聞資料庫標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 標題
        title_label = QLabel("📰 新聞資料庫管理")
        title_label.setFont(QFont("Microsoft JhengHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 10px;")
        layout.addWidget(title_label)

        # 資料庫狀態
        status_group = QGroupBox("資料庫狀態")
        status_layout = QVBoxLayout(status_group)

        self.news_db_status = QLabel("正在檢查新聞資料庫...")
        status_layout.addWidget(self.news_db_status)

        # 重新整理按鈕
        refresh_btn = QPushButton("🔄 重新整理狀態")
        refresh_btn.clicked.connect(self.refresh_news_database_status)
        status_layout.addWidget(refresh_btn)

        layout.addWidget(status_group)

        # 新聞統計
        stats_group = QGroupBox("新聞統計")
        stats_layout = QVBoxLayout(stats_group)

        self.news_stats_table = QTableWidget()
        self.news_stats_table.setColumnCount(3)
        self.news_stats_table.setHorizontalHeaderLabels(["項目", "數量", "說明"])
        self.news_stats_table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(self.news_stats_table)

        layout.addWidget(stats_group)

        # 操作按鈕
        action_group = QGroupBox("資料庫操作")
        action_layout = QHBoxLayout(action_group)

        # 查看新聞按鈕
        view_news_btn = QPushButton("📰 查看最新新聞")
        view_news_btn.clicked.connect(self.view_latest_news)
        action_layout.addWidget(view_news_btn)

        # 清理資料庫按鈕
        clean_btn = QPushButton("🧹 清理舊新聞")
        clean_btn.clicked.connect(self.clean_old_news)
        action_layout.addWidget(clean_btn)

        # 匯出新聞按鈕
        export_btn = QPushButton("📤 匯出新聞")
        export_btn.clicked.connect(self.export_news)
        action_layout.addWidget(export_btn)

        layout.addWidget(action_group)

        # 加入標籤頁
        self.tab_widget.addTab(tab, "📰 新聞資料庫")

        # 初始化狀態
        self.refresh_news_database_status()

    def refresh_news_database_status(self):
        """重新整理新聞資料庫狀態"""
        try:
            news_db_path = self.news_db_path.text().strip()
            if not news_db_path:
                news_db_path = "D:/Finlab/history/tables/news.db"

            if not os.path.exists(news_db_path):
                self.news_db_status.setText("❌ 新聞資料庫不存在")
                self.news_db_status.setStyleSheet("color: #ff6b6b;")
                return

            # 連接資料庫並獲取統計資訊
            import sqlite3
            with sqlite3.connect(news_db_path) as conn:
                cursor = conn.cursor()

                # 檢查表格是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news_content'")
                if not cursor.fetchone():
                    self.news_db_status.setText("⚠️ 新聞資料表不存在")
                    self.news_db_status.setStyleSheet("color: #ffa500;")
                    return

                # 獲取統計資訊
                cursor.execute("SELECT COUNT(*) FROM news_content")
                total_news = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM news_content WHERE stock_code IS NOT NULL AND stock_code != ''")
                stock_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(DISTINCT source) FROM news_content")
                source_count = cursor.fetchone()[0]

                cursor.execute("SELECT MIN(date), MAX(date) FROM news_content")
                date_range = cursor.fetchone()

                # 更新狀態
                self.news_db_status.setText(f"✅ 新聞資料庫正常 - 共 {total_news} 筆新聞")
                self.news_db_status.setStyleSheet("color: #4caf50;")

                # 更新統計表格
                self.news_stats_table.setRowCount(5)

                stats_data = [
                    ("總新聞數", str(total_news), "所有來源的新聞總數"),
                    ("股票數量", str(stock_count), "有相關新聞的股票數量"),
                    ("新聞來源", str(source_count), "不同新聞來源數量"),
                    ("最早日期", date_range[0] or "無", "資料庫中最早的新聞日期"),
                    ("最新日期", date_range[1] or "無", "資料庫中最新的新聞日期")
                ]

                for row, (item, count, desc) in enumerate(stats_data):
                    self.news_stats_table.setItem(row, 0, QTableWidgetItem(item))
                    self.news_stats_table.setItem(row, 1, QTableWidgetItem(count))
                    self.news_stats_table.setItem(row, 2, QTableWidgetItem(desc))

        except Exception as e:
            self.news_db_status.setText(f"❌ 檢查失敗: {str(e)}")
            self.news_db_status.setStyleSheet("color: #ff6b6b;")

    def refresh_dividend_database_status(self):
        """重新整理除權息資料庫狀態"""
        try:
            dividend_db_path = self.dividend_db_path.text().strip()
            if not dividend_db_path:
                dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"

            if not os.path.exists(dividend_db_path):
                return  # 如果沒有設定路徑，不顯示錯誤

            # 連接資料庫並獲取統計資訊
            import sqlite3
            with sqlite3.connect(dividend_db_path) as conn:
                cursor = conn.cursor()

                # 檢查表格是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dividend_data'")
                if not cursor.fetchone():
                    return

                # 獲取統計資訊
                cursor.execute("SELECT COUNT(*) FROM dividend_data")
                total_records = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM dividend_data")
                stock_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM dividend_data WHERE ex_dividend_date IS NOT NULL")
                with_dates = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(DISTINCT year) FROM dividend_data")
                year_count = cursor.fetchone()[0]

                cursor.execute("SELECT MIN(year), MAX(year) FROM dividend_data")
                year_range = cursor.fetchone()

                print(f"✅ 除權息資料庫: {total_records} 筆資料, {stock_count} 檔股票, {with_dates} 筆有日期")

        except Exception as e:
            print(f"❌ 除權息資料庫錯誤: {str(e)[:30]}...")

    def create_dividend_database_tab(self):
        """創建除權息資料庫標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 狀態顯示
        status_group = QGroupBox("📊 除權息資料庫狀態")
        status_layout = QVBoxLayout(status_group)

        self.dividend_db_status = QLabel("正在檢查除權息資料庫...")
        self.dividend_db_status.setStyleSheet("color: #2196f3; font-weight: bold;")
        status_layout.addWidget(self.dividend_db_status)

        layout.addWidget(status_group)

        # 統計資訊表格
        stats_group = QGroupBox("📈 資料統計")
        stats_layout = QVBoxLayout(stats_group)

        self.dividend_stats_table = QTableWidget()
        self.dividend_stats_table.setColumnCount(3)
        self.dividend_stats_table.setHorizontalHeaderLabels(["項目", "數量", "說明"])
        self.dividend_stats_table.horizontalHeader().setStretchLastSection(True)
        self.dividend_stats_table.setAlternatingRowColors(True)
        self.dividend_stats_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        stats_layout.addWidget(self.dividend_stats_table)
        layout.addWidget(stats_group)

        # 資料預覽
        preview_group = QGroupBox("👁️ 資料預覽")
        preview_layout = QVBoxLayout(preview_group)

        self.dividend_preview_table = QTableWidget()
        self.dividend_preview_table.setAlternatingRowColors(True)
        self.dividend_preview_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        preview_layout.addWidget(self.dividend_preview_table)
        layout.addWidget(preview_group)

        # 操作按鈕
        button_layout = QHBoxLayout()

        refresh_btn = QPushButton("🔄 重新整理")
        refresh_btn.clicked.connect(self.refresh_dividend_database_status)
        button_layout.addWidget(refresh_btn)

        export_btn = QPushButton("📤 匯出資料")
        export_btn.clicked.connect(self.export_dividend_data)
        button_layout.addWidget(export_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        self.tab_widget.addTab(tab, "💰 除權息資料庫")

        # 初始化狀態
        self.refresh_dividend_database_status()

    def export_dividend_data(self):
        """匯出除權息資料"""
        try:
            dividend_db_path = self.dividend_db_path.text().strip()
            if not dividend_db_path:
                dividend_db_path = "D:/Finlab/history/tables/dividend_data.db"

            if not os.path.exists(dividend_db_path):
                QMessageBox.warning(self, "警告", "除權息資料庫不存在")
                return

            # 選擇匯出檔案
            from PyQt6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "匯出除權息資料",
                f"dividend_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV files (*.csv);;All files (*)"
            )

            if file_path:
                import sqlite3
                import pandas as pd

                with sqlite3.connect(dividend_db_path) as conn:
                    df = pd.read_sql_query("SELECT * FROM dividend_data ORDER BY stock_code, year", conn)
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')

                QMessageBox.information(self, "成功", f"除權息資料已匯出至:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"匯出失敗:\n{str(e)}")

    def view_latest_news(self):
        """查看最新新聞"""
        try:
            news_db_path = self.news_db_path.text().strip()
            if not news_db_path:
                news_db_path = "D:/Finlab/history/tables/news.db"

            if not os.path.exists(news_db_path):
                QMessageBox.warning(self, "警告", "新聞資料庫不存在")
                return

            # 顯示新聞列表對話框
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("📰 最新新聞")
            dialog.setGeometry(300, 300, 800, 600)

            layout = QVBoxLayout(dialog)

            # 新聞表格
            news_table = QTableWidget()
            news_table.setColumnCount(5)
            news_table.setHorizontalHeaderLabels(["日期", "股票代碼", "來源", "標題", "連結"])

            # 從資料庫獲取最新新聞
            import sqlite3
            with sqlite3.connect(news_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT date, stock_code, source, title, link
                    FROM news_content
                    ORDER BY date DESC, time DESC
                    LIMIT 50
                """)

                news_data = cursor.fetchall()
                news_table.setRowCount(len(news_data))

                for row, (date, stock_code, source, title, link) in enumerate(news_data):
                    # 格式化日期
                    if date and len(date) == 8:
                        formatted_date = f"{date[:4]}/{date[4:6]}/{date[6:]}"
                    else:
                        formatted_date = date or ""

                    news_table.setItem(row, 0, QTableWidgetItem(formatted_date))
                    news_table.setItem(row, 1, QTableWidgetItem(stock_code or ""))
                    news_table.setItem(row, 2, QTableWidgetItem(source or ""))
                    news_table.setItem(row, 3, QTableWidgetItem(title or ""))
                    news_table.setItem(row, 4, QTableWidgetItem(link or ""))

            # 設置表格屬性
            news_table.resizeColumnsToContents()
            news_table.setAlternatingRowColors(True)
            news_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

            layout.addWidget(news_table)

            # 關閉按鈕
            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"查看新聞失敗：{str(e)}")

    def clean_old_news(self):
        """清理舊新聞"""
        try:
            from PyQt6.QtWidgets import QInputDialog

            days, ok = QInputDialog.getInt(
                self, "清理舊新聞",
                "請輸入要保留的天數（超過此天數的新聞將被刪除）：",
                30, 1, 365
            )

            if not ok:
                return

            news_db_path = self.news_db_path.text().strip()
            if not news_db_path:
                news_db_path = "D:/Finlab/history/tables/news.db"

            if not os.path.exists(news_db_path):
                QMessageBox.warning(self, "警告", "新聞資料庫不存在")
                return

            # 計算截止日期
            from datetime import datetime, timedelta
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

            # 刪除舊新聞
            import sqlite3
            with sqlite3.connect(news_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM news_content WHERE date < ?", (cutoff_date,))
                old_count = cursor.fetchone()[0]

                if old_count == 0:
                    QMessageBox.information(self, "清理完成", "沒有需要清理的舊新聞")
                    return

                cursor.execute("DELETE FROM news_content WHERE date < ?", (cutoff_date,))
                conn.commit()

                QMessageBox.information(self, "清理完成", f"已刪除 {old_count} 筆超過 {days} 天的舊新聞")

                # 重新整理狀態
                self.refresh_news_database_status()

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"清理新聞失敗：{str(e)}")

    def export_news(self):
        """匯出新聞"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            import csv

            news_db_path = self.news_db_path.text().strip()
            if not news_db_path:
                news_db_path = "D:/Finlab/history/tables/news.db"

            if not os.path.exists(news_db_path):
                QMessageBox.warning(self, "警告", "新聞資料庫不存在")
                return

            # 選擇匯出檔案
            file_path, _ = QFileDialog.getSaveFileName(
                self, "匯出新聞",
                f"news_export_{datetime.now().strftime('%Y%m%d')}.csv",
                "CSV files (*.csv)"
            )

            if not file_path:
                return

            # 匯出資料
            import sqlite3
            with sqlite3.connect(news_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT date, time, stock_code, source, title, link, content
                    FROM news_content
                    ORDER BY date DESC, time DESC
                """)

                news_data = cursor.fetchall()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['日期', '時間', '股票代碼', '來源', '標題', '連結', '內容'])
                    writer.writerows(news_data)

                QMessageBox.information(self, "匯出完成", f"已匯出 {len(news_data)} 筆新聞到：\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"匯出新聞失敗：{str(e)}")

    def create_price_data_tab(self):
        """創建價格數據庫預覽標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 標題
        title_label = QLabel("📊 價格數據庫預覽")
        title_label.setFont(QFont("Microsoft JhengHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 10px;")
        layout.addWidget(title_label)

        # 數據庫狀態
        status_group = QGroupBox("數據庫狀態")
        status_layout = QVBoxLayout(status_group)

        self.price_db_status = QLabel("正在檢查價格數據庫...")
        status_layout.addWidget(self.price_db_status)

        # 重新整理按鈕
        refresh_btn = QPushButton("🔄 重新整理狀態")
        refresh_btn.clicked.connect(self.refresh_price_database_status)
        status_layout.addWidget(refresh_btn)

        layout.addWidget(status_group)

        # 數據統計
        stats_group = QGroupBox("數據統計")
        stats_layout = QVBoxLayout(stats_group)

        self.price_stats_table = QTableWidget()
        self.price_stats_table.setColumnCount(3)
        self.price_stats_table.setHorizontalHeaderLabels(["項目", "數量", "說明"])
        self.price_stats_table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(self.price_stats_table)

        layout.addWidget(stats_group)

        # 最新數據預覽
        preview_group = QGroupBox("最新數據預覽")
        preview_layout = QVBoxLayout(preview_group)

        self.price_preview_table = QTableWidget()
        self.price_preview_table.setColumnCount(7)
        self.price_preview_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "日期", "開盤", "最高", "最低", "收盤"])
        self.price_preview_table.horizontalHeader().setStretchLastSection(True)
        preview_layout.addWidget(self.price_preview_table)

        layout.addWidget(preview_group)

        # 操作按鈕
        action_group = QGroupBox("數據庫操作")
        action_layout = QHBoxLayout(action_group)

        # 查看股票列表按鈕
        view_stocks_btn = QPushButton("📋 查看股票列表")
        view_stocks_btn.clicked.connect(self.view_stock_list)
        action_layout.addWidget(view_stocks_btn)

        # 數據完整性檢查按鈕
        check_btn = QPushButton("🔍 數據完整性檢查")
        check_btn.clicked.connect(self.check_data_integrity)
        action_layout.addWidget(check_btn)

        # 匯出數據按鈕
        export_btn = QPushButton("📤 匯出數據")
        export_btn.clicked.connect(self.export_price_data)
        action_layout.addWidget(export_btn)

        layout.addWidget(action_group)

        # 加入標籤頁
        self.tab_widget.addTab(tab, "📊 價格數據庫")

        # 初始化狀態
        self.refresh_price_database_status()

    def refresh_price_database_status(self):
        """重新整理價格數據庫狀態"""
        try:
            price_db_path = self.price_db_path.text().strip()
            if not price_db_path:
                price_db_path = "D:/Finlab/history/tables/price.db"

            if not os.path.exists(price_db_path):
                self.price_db_status.setText("❌ 價格數據庫不存在")
                self.price_db_status.setStyleSheet("color: #ff6b6b;")
                return

            # 連接數據庫並獲取統計資訊
            import sqlite3
            with sqlite3.connect(price_db_path) as conn:
                cursor = conn.cursor()

                # 檢查表格是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_daily_data'")
                if not cursor.fetchone():
                    self.price_db_status.setText("⚠️ 價格數據表不存在")
                    self.price_db_status.setStyleSheet("color: #ffa500;")
                    return

                # 獲取統計資訊
                cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
                total_records = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
                stock_count = cursor.fetchone()[0]

                cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data")
                date_range = cursor.fetchone()

                cursor.execute("SELECT COUNT(DISTINCT date) FROM stock_daily_data")
                trading_days = cursor.fetchone()[0]

                # 更新狀態
                self.price_db_status.setText(f"✅ 價格數據庫正常 - 共 {total_records:,} 筆記錄")
                self.price_db_status.setStyleSheet("color: #4caf50;")

                # 更新統計表格
                self.price_stats_table.setRowCount(5)

                stats_data = [
                    ("總記錄數", f"{total_records:,}", "所有股票的價格記錄總數"),
                    ("股票數量", f"{stock_count:,}", "數據庫中的股票數量"),
                    ("交易日數", f"{trading_days:,}", "包含數據的交易日數量"),
                    ("最早日期", date_range[0] or "無", "數據庫中最早的交易日期"),
                    ("最新日期", date_range[1] or "無", "數據庫中最新的交易日期")
                ]

                for row, (item, count, desc) in enumerate(stats_data):
                    self.price_stats_table.setItem(row, 0, QTableWidgetItem(item))
                    self.price_stats_table.setItem(row, 1, QTableWidgetItem(count))
                    self.price_stats_table.setItem(row, 2, QTableWidgetItem(desc))

                # 更新最新數據預覽
                cursor.execute("""
                    SELECT stock_id, stock_name, date, open, high, low, close
                    FROM stock_daily_data
                    ORDER BY date DESC, stock_id
                    LIMIT 20
                """)

                preview_data = cursor.fetchall()
                self.price_preview_table.setRowCount(len(preview_data))

                for row, (stock_id, stock_name, date, open_price, high, low, close) in enumerate(preview_data):
                    # 格式化日期
                    if date and len(str(date)) == 8:
                        formatted_date = f"{str(date)[:4]}/{str(date)[4:6]}/{str(date)[6:]}"
                    else:
                        formatted_date = str(date) if date else ""

                    self.price_preview_table.setItem(row, 0, QTableWidgetItem(str(stock_id) if stock_id else ""))
                    self.price_preview_table.setItem(row, 1, QTableWidgetItem(str(stock_name) if stock_name else ""))
                    self.price_preview_table.setItem(row, 2, QTableWidgetItem(formatted_date))
                    self.price_preview_table.setItem(row, 3, QTableWidgetItem(f"{open_price:.2f}" if open_price else ""))
                    self.price_preview_table.setItem(row, 4, QTableWidgetItem(f"{high:.2f}" if high else ""))
                    self.price_preview_table.setItem(row, 5, QTableWidgetItem(f"{low:.2f}" if low else ""))
                    self.price_preview_table.setItem(row, 6, QTableWidgetItem(f"{close:.2f}" if close else ""))

        except Exception as e:
            self.price_db_status.setText(f"❌ 檢查失敗: {str(e)}")
            self.price_db_status.setStyleSheet("color: #ff6b6b;")

    def view_stock_list(self):
        """查看股票列表"""
        try:
            price_db_path = self.price_db_path.text().strip()
            if not price_db_path:
                price_db_path = "D:/Finlab/history/tables/price.db"

            if not os.path.exists(price_db_path):
                QMessageBox.warning(self, "警告", "價格數據庫不存在")
                return

            # 顯示股票列表對話框
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("📋 股票列表")
            dialog.setGeometry(300, 300, 600, 500)

            layout = QVBoxLayout(dialog)

            # 股票表格
            stock_table = QTableWidget()
            stock_table.setColumnCount(4)
            stock_table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "記錄數", "最新日期"])

            # 從數據庫獲取股票列表
            import sqlite3
            with sqlite3.connect(price_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT stock_id, stock_name, COUNT(*) as record_count, MAX(date) as latest_date
                    FROM stock_daily_data
                    GROUP BY stock_id, stock_name
                    ORDER BY stock_id
                """)

                stock_data = cursor.fetchall()
                stock_table.setRowCount(len(stock_data))

                for row, (stock_id, stock_name, count, latest_date) in enumerate(stock_data):
                    # 格式化日期
                    if latest_date and len(str(latest_date)) == 8:
                        formatted_date = f"{str(latest_date)[:4]}/{str(latest_date)[4:6]}/{str(latest_date)[6:]}"
                    else:
                        formatted_date = str(latest_date) if latest_date else ""

                    stock_table.setItem(row, 0, QTableWidgetItem(str(stock_id) if stock_id else ""))
                    stock_table.setItem(row, 1, QTableWidgetItem(str(stock_name) if stock_name else ""))
                    stock_table.setItem(row, 2, QTableWidgetItem(f"{count:,}"))
                    stock_table.setItem(row, 3, QTableWidgetItem(formatted_date))

            # 設置表格屬性
            stock_table.resizeColumnsToContents()
            stock_table.setAlternatingRowColors(True)
            stock_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

            layout.addWidget(stock_table)

            # 關閉按鈕
            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"查看股票列表失敗：{str(e)}")

    def check_data_integrity(self):
        """數據完整性檢查"""
        try:
            price_db_path = self.price_db_path.text().strip()
            if not price_db_path:
                price_db_path = "D:/Finlab/history/tables/price.db"

            if not os.path.exists(price_db_path):
                QMessageBox.warning(self, "警告", "價格數據庫不存在")
                return

            # 執行數據完整性檢查
            import sqlite3
            issues = []

            with sqlite3.connect(price_db_path) as conn:
                cursor = conn.cursor()

                # 檢查空值
                cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id IS NULL OR stock_id = ''")
                null_stock_id = cursor.fetchone()[0]
                if null_stock_id > 0:
                    issues.append(f"發現 {null_stock_id} 筆記錄的股票代碼為空")

                # 檢查價格異常
                cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE close <= 0 OR open <= 0")
                invalid_prices = cursor.fetchone()[0]
                if invalid_prices > 0:
                    issues.append(f"發現 {invalid_prices} 筆記錄的價格異常（≤0）")

                # 檢查日期格式
                cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE LENGTH(CAST(date AS TEXT)) != 8")
                invalid_dates = cursor.fetchone()[0]
                if invalid_dates > 0:
                    issues.append(f"發現 {invalid_dates} 筆記錄的日期格式異常")

                # 檢查重複記錄
                cursor.execute("""
                    SELECT stock_id, date, COUNT(*) as count
                    FROM stock_daily_data
                    GROUP BY stock_id, date
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                if duplicates:
                    issues.append(f"發現 {len(duplicates)} 組重複記錄")

            # 顯示檢查結果
            if issues:
                result_msg = "⚠️ 發現以下數據完整性問題：\n\n" + "\n".join(f"• {issue}" for issue in issues)
            else:
                result_msg = "✅ 數據完整性檢查通過，未發現問題"

            QMessageBox.information(self, "數據完整性檢查結果", result_msg)

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"數據完整性檢查失敗：{str(e)}")

    def export_price_data(self):
        """匯出價格數據"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            import csv

            price_db_path = self.price_db_path.text().strip()
            if not price_db_path:
                price_db_path = "D:/Finlab/history/tables/price.db"

            if not os.path.exists(price_db_path):
                QMessageBox.warning(self, "警告", "價格數據庫不存在")
                return

            # 選擇匯出檔案
            file_path, _ = QFileDialog.getSaveFileName(
                self, "匯出價格數據",
                f"price_data_export_{datetime.now().strftime('%Y%m%d')}.csv",
                "CSV files (*.csv)"
            )

            if not file_path:
                return

            # 匯出資料
            import sqlite3
            with sqlite3.connect(price_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT stock_id, stock_name, date, open, high, low, close, volume
                    FROM stock_daily_data
                    ORDER BY stock_id, date
                """)

                price_data = cursor.fetchall()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['股票代碼', '股票名稱', '日期', '開盤', '最高', '最低', '收盤', '成交量'])
                    writer.writerows(price_data)

                QMessageBox.information(self, "匯出完成", f"已匯出 {len(price_data):,} 筆價格數據到：\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"匯出價格數據失敗：{str(e)}")

    def create_monthly_revenue_database_tab(self):
        """創建月營收資料庫標籤"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 標題
        title_label = QLabel("📊 月營收資料庫管理")
        title_label.setFont(QFont("Microsoft JhengHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; margin: 10px;")
        layout.addWidget(title_label)

        # 資料庫狀態
        status_group = QGroupBox("資料庫狀態")
        status_layout = QVBoxLayout(status_group)

        self.monthly_revenue_db_status = QLabel("正在檢查月營收資料庫...")
        status_layout.addWidget(self.monthly_revenue_db_status)

        # 重新整理按鈕
        refresh_btn = QPushButton("🔄 重新整理狀態")
        refresh_btn.clicked.connect(self.refresh_monthly_revenue_database_status)
        status_layout.addWidget(refresh_btn)

        layout.addWidget(status_group)

        # 資料庫操作
        action_group = QGroupBox("資料庫操作")
        action_layout = QHBoxLayout(action_group)

        # 新建資料庫按鈕
        create_btn = QPushButton("🆕 新建資料庫")
        create_btn.clicked.connect(self.create_monthly_revenue_database)
        action_layout.addWidget(create_btn)

        # 查看月營收按鈕
        view_btn = QPushButton("👁️ 查看月營收")
        view_btn.clicked.connect(self.view_monthly_revenue)
        action_layout.addWidget(view_btn)

        # 匯出月營收按鈕
        export_btn = QPushButton("📤 匯出月營收")
        export_btn.clicked.connect(self.export_monthly_revenue)
        action_layout.addWidget(export_btn)

        layout.addWidget(action_group)

        # 加入標籤頁
        self.tab_widget.addTab(tab, "📊 月營收資料庫")

        # 初始化狀態
        self.refresh_monthly_revenue_database_status()

    def refresh_monthly_revenue_database_status(self):
        """重新整理月營收資料庫狀態"""
        try:
            monthly_revenue_db_path = self.monthly_revenue_db_path.text().strip()
            if not monthly_revenue_db_path:
                monthly_revenue_db_path = "D:/Finlab/history/tables/monthly_revenue.db"

            if not os.path.exists(monthly_revenue_db_path):
                self.monthly_revenue_db_status.setText("❌ 月營收資料庫不存在")
                self.monthly_revenue_db_status.setStyleSheet("color: #ff6b6b;")
                return

            # 連接資料庫並獲取統計資訊
            conn = sqlite3.connect(monthly_revenue_db_path)
            cursor = conn.cursor()

            # 獲取表格資訊
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            if not tables:
                self.monthly_revenue_db_status.setText("⚠️ 月營收資料庫為空")
                self.monthly_revenue_db_status.setStyleSheet("color: #ffa500;")
                conn.close()
                return

            # 獲取月營收記錄數
            cursor.execute("SELECT COUNT(*) FROM monthly_revenue")
            total_records = cursor.fetchone()[0]

            # 獲取股票數量
            cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM monthly_revenue")
            stock_count = cursor.fetchone()[0]

            # 獲取最新日期
            cursor.execute("SELECT MAX(date) FROM monthly_revenue")
            latest_date = cursor.fetchone()[0]

            conn.close()

            status_text = f"✅ 月營收資料庫正常\n"
            status_text += f"📊 總記錄數: {total_records:,} 筆\n"
            status_text += f"🏢 股票數量: {stock_count} 支\n"
            status_text += f"📅 最新資料: {latest_date or '無'}"

            self.monthly_revenue_db_status.setText(status_text)
            self.monthly_revenue_db_status.setStyleSheet("color: #4CAF50;")

        except Exception as e:
            self.monthly_revenue_db_status.setText(f"❌ 檢查月營收資料庫時發生錯誤:\n{str(e)}")
            self.monthly_revenue_db_status.setStyleSheet("color: #ff6b6b;")

    def view_monthly_revenue(self):
        """查看月營收資料"""
        try:
            monthly_revenue_db_path = self.monthly_revenue_db_path.text().strip()
            if not monthly_revenue_db_path:
                monthly_revenue_db_path = "D:/Finlab/history/tables/monthly_revenue.db"

            if not os.path.exists(monthly_revenue_db_path):
                QMessageBox.warning(self, "資料庫不存在", "月營收資料庫檔案不存在")
                return

            # 創建查看視窗
            dialog = QDialog(self)
            dialog.setWindowTitle("月營收資料查看")
            dialog.setGeometry(200, 200, 800, 600)

            layout = QVBoxLayout(dialog)

            # 查詢控制
            control_layout = QHBoxLayout()
            control_layout.addWidget(QLabel("股票代號:"))

            stock_input = QLineEdit()
            stock_input.setPlaceholderText("輸入股票代號，留空顯示全部")
            control_layout.addWidget(stock_input)

            search_btn = QPushButton("🔍 查詢")
            control_layout.addWidget(search_btn)

            layout.addLayout(control_layout)

            # 資料表格
            table = QTableWidget()
            layout.addWidget(table)

            def load_data(stock_id=""):
                conn = sqlite3.connect(monthly_revenue_db_path)

                if stock_id:
                    query = "SELECT * FROM monthly_revenue WHERE stock_id = ? ORDER BY date DESC LIMIT 100"
                    cursor = conn.cursor()
                    cursor.execute(query, [stock_id])
                    data = cursor.fetchall()
                    columns = [description[0] for description in cursor.description]
                else:
                    query = "SELECT * FROM monthly_revenue ORDER BY date DESC LIMIT 100"
                    cursor = conn.cursor()
                    cursor.execute(query)
                    data = cursor.fetchall()
                    columns = [description[0] for description in cursor.description]

                conn.close()

                # 設置表格
                table.setRowCount(len(data))
                table.setColumnCount(len(columns))
                table.setHorizontalHeaderLabels(columns)

                # 填充資料
                for i, row in enumerate(data):
                    for j, value in enumerate(row):
                        table.setItem(i, j, QTableWidgetItem(str(value)))

                # 調整欄位寬度
                table.horizontalHeader().setStretchLastSection(True)

            search_btn.clicked.connect(lambda: load_data(stock_input.text().strip()))

            # 初始載入
            load_data()

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "查看失敗", f"查看月營收資料失敗:\n{str(e)}")

    def export_monthly_revenue(self):
        """匯出月營收資料"""
        try:
            monthly_revenue_db_path = self.monthly_revenue_db_path.text().strip()
            if not monthly_revenue_db_path:
                monthly_revenue_db_path = "D:/Finlab/history/tables/monthly_revenue.db"

            if not os.path.exists(monthly_revenue_db_path):
                QMessageBox.warning(self, "資料庫不存在", "月營收資料庫檔案不存在")
                return

            # 選擇匯出檔案
            file_path, _ = QFileDialog.getSaveFileName(
                self, "匯出月營收資料", "monthly_revenue_export.csv",
                "CSV files (*.csv);;All files (*)")

            if not file_path:
                return

            # 匯出資料
            conn = sqlite3.connect(monthly_revenue_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM monthly_revenue ORDER BY stock_id, date")
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            conn.close()

            import csv
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(columns)
                writer.writerows(data)

            QMessageBox.information(self, "匯出成功",
                                  f"月營收資料已匯出到:\n{file_path}\n\n"
                                  f"共匯出 {len(data)} 筆記錄")

        except Exception as e:
            QMessageBox.critical(self, "匯出失敗", f"匯出月營收資料失敗:\n{str(e)}")

    def create_monthly_revenue_database(self):
        """新建月營收資料庫"""
        try:
            # 讓用戶選擇資料庫保存位置
            file_path, _ = QFileDialog.getSaveFileName(
                self, "新建月營收資料庫",
                "D:/Finlab/history/tables/monthly_revenue.db",
                "Database files (*.db);;All files (*)")

            if not file_path:
                return

            # 確保目錄存在
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 創建資料庫並建立表格
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()

            # 創建月營收表格
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    date TEXT NOT NULL,
                    revenue REAL,
                    revenue_mom REAL,
                    revenue_yoy REAL,
                    cumulative_revenue REAL,
                    cumulative_revenue_yoy REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, date)
                )
            ''')

            # 創建索引以提高查詢效能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_stock_date ON monthly_revenue(stock_id, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_date ON monthly_revenue(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_stock ON monthly_revenue(stock_id)')

            # 創建元數據表格
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS database_info (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入資料庫資訊
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('version', '1.0')
            ''')
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('description', '月營收資料庫 - 儲存台股上市櫃公司月營收數據')
            ''')
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('created_by', 'O3mh策略系統')
            ''')

            conn.commit()
            conn.close()

            # 更新路徑設定
            self.monthly_revenue_db_path.setText(file_path)

            # 測試新建的資料庫
            self.test_single_database('monthly_revenue', file_path)

            # 刷新狀態
            self.refresh_monthly_revenue_database_status()

            QMessageBox.information(self, "新建成功",
                                  f"月營收資料庫已成功創建:\n{file_path}\n\n"
                                  f"✅ 已建立 monthly_revenue 表格\n"
                                  f"✅ 已建立索引以提高查詢效能\n"
                                  f"✅ 已建立元數據表格\n\n"
                                  f"現在可以開始下載月營收數據了！")

        except Exception as e:
            QMessageBox.critical(self, "新建失敗", f"新建月營收資料庫失敗:\n{str(e)}")


if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    dialog = DatabaseConfigDialog()
    dialog.show()
    sys.exit(app.exec())
