#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PE數據提供器
為策略提供基本面數據，包括本益比、殖利率等
"""

import sqlite3
import pandas as pd
import logging
from typing import Optional, Dict, List
from datetime import datetime, timedelta

class PEDataProvider:
    """PE數據提供器"""
    
    def __init__(self, db_path: str, table_name: str = None):
        """
        初始化PE數據提供器
        
        Args:
            db_path: PE數據庫路徑
            table_name: PE數據表名稱，如果為None則自動檢測
        """
        self.db_path = db_path
        self.table_name = table_name
        self.logger = logging.getLogger(__name__)
        
        # 自動檢測表名稱
        if not self.table_name:
            self.table_name = self._detect_table_name()
        
        # 檢測欄位映射
        self.column_mapping = self._detect_column_mapping()
        
    def _detect_table_name(self) -> Optional[str]:
        """自動檢測PE數據表名稱"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 優先尋找包含pe的表名
            for table in tables:
                if 'pe' in table.lower():
                    conn.close()
                    self.logger.info(f"檢測到PE數據表: {table}")
                    return table
            
            # 如果沒有，使用第一個表
            if tables:
                conn.close()
                self.logger.info(f"使用第一個表作為PE數據表: {tables[0]}")
                return tables[0]
            
            conn.close()
            return None
            
        except Exception as e:
            self.logger.error(f"檢測PE數據表失敗: {e}")
            return None
    
    def _detect_column_mapping(self) -> Dict[str, str]:
        """檢測欄位映射"""
        mapping = {
            'stock_id': 'stock_id',
            'date': 'date',
            'pe_ratio': 'pe_ratio',
            'dividend_yield': 'dividend_yield',
            'pb_ratio': 'pb_ratio',
            'market_cap': 'market_cap'
        }
        
        if not self.table_name:
            return mapping
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(f"PRAGMA table_info({self.table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            
            # 檢測股票代碼欄位
            for col in ['股票代號', 'stock_id', 'code', 'symbol']:
                if col in columns:
                    mapping['stock_id'] = col
                    break
            
            # 檢測日期欄位
            for col in ['date', 'trading_date', 'dt']:
                if col in columns:
                    mapping['date'] = col
                    break
            
            # 檢測本益比欄位
            for col in ['本益比', 'pe_ratio', 'pe', 'price_earnings']:
                if col in columns:
                    mapping['pe_ratio'] = col
                    break
            
            # 檢測殖利率欄位
            for col in ['殖利率', 'dividend_yield', 'yield', 'div_yield']:
                if col in columns:
                    mapping['dividend_yield'] = col
                    break
            
            # 檢測股價淨值比欄位
            for col in ['股價淨值比', 'pb_ratio', 'pb', 'price_book']:
                if col in columns:
                    mapping['pb_ratio'] = col
                    break
            
            # 檢測市值欄位
            for col in ['市值', 'market_cap', 'market_value']:
                if col in columns:
                    mapping['market_cap'] = col
                    break
            
            conn.close()
            self.logger.info(f"PE數據欄位映射: {mapping}")
            
        except Exception as e:
            self.logger.error(f"檢測PE數據欄位映射失敗: {e}")
        
        return mapping
    
    def get_pe_data(self, stock_id: str, date: str = None) -> Optional[Dict]:
        """
        獲取指定股票的PE數據
        
        Args:
            stock_id: 股票代碼
            date: 指定日期，如果為None則獲取最新數據
            
        Returns:
            包含PE數據的字典，如果沒有數據則返回None
        """
        if not self.table_name:
            return None
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 構建查詢
            stock_col = self.column_mapping['stock_id']
            date_col = self.column_mapping['date']
            
            if date:
                # 獲取指定日期或最接近的數據
                query = f"""
                SELECT * FROM {self.table_name}
                WHERE {stock_col} = ? AND {date_col} <= ?
                ORDER BY {date_col} DESC
                LIMIT 1
                """
                params = [stock_id, date]
            else:
                # 獲取最新數據
                query = f"""
                SELECT * FROM {self.table_name}
                WHERE {stock_col} = ?
                ORDER BY {date_col} DESC
                LIMIT 1
                """
                params = [stock_id]
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if df.empty:
                return None
            
            # 轉換為字典並使用標準欄位名
            row = df.iloc[0]
            result = {}
            
            for standard_name, actual_name in self.column_mapping.items():
                if actual_name in df.columns:
                    value = row[actual_name]
                    # 處理NULL值
                    if pd.isna(value):
                        value = None
                    result[standard_name] = value
            
            return result
            
        except Exception as e:
            self.logger.error(f"獲取PE數據失敗 {stock_id}: {e}")
            return None
    
    def get_pe_data_batch(self, stock_ids: List[str], date: str = None) -> Dict[str, Dict]:
        """
        批量獲取多支股票的PE數據
        
        Args:
            stock_ids: 股票代碼列表
            date: 指定日期，如果為None則獲取最新數據
            
        Returns:
            以股票代碼為key的PE數據字典
        """
        if not self.table_name or not stock_ids:
            return {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 構建查詢
            stock_col = self.column_mapping['stock_id']
            date_col = self.column_mapping['date']
            
            # 使用IN查詢批量獲取
            placeholders = ','.join(['?' for _ in stock_ids])
            
            if date:
                query = f"""
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER (PARTITION BY {stock_col} ORDER BY {date_col} DESC) as rn
                    FROM {self.table_name}
                    WHERE {stock_col} IN ({placeholders}) AND {date_col} <= ?
                ) WHERE rn = 1
                """
                params = stock_ids + [date]
            else:
                query = f"""
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER (PARTITION BY {stock_col} ORDER BY {date_col} DESC) as rn
                    FROM {self.table_name}
                    WHERE {stock_col} IN ({placeholders})
                ) WHERE rn = 1
                """
                params = stock_ids
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            # 轉換為字典格式
            result = {}
            for _, row in df.iterrows():
                stock_id = row[self.column_mapping['stock_id']]
                stock_data = {}
                
                for standard_name, actual_name in self.column_mapping.items():
                    if actual_name in df.columns:
                        value = row[actual_name]
                        # 處理NULL值
                        if pd.isna(value):
                            value = None
                        stock_data[standard_name] = value
                
                result[stock_id] = stock_data
            
            return result
            
        except Exception as e:
            self.logger.error(f"批量獲取PE數據失敗: {e}")
            return {}
    
    def get_available_stocks(self) -> List[str]:
        """獲取PE數據庫中可用的股票列表"""
        if not self.table_name:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            stock_col = self.column_mapping['stock_id']
            
            query = f"SELECT DISTINCT {stock_col} FROM {self.table_name} ORDER BY {stock_col}"
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            return df[stock_col].tolist()
            
        except Exception as e:
            self.logger.error(f"獲取可用股票列表失敗: {e}")
            return []
    
    def get_data_date_range(self) -> tuple:
        """獲取PE數據的日期範圍"""
        if not self.table_name:
            return None, None
        
        try:
            conn = sqlite3.connect(self.db_path)
            date_col = self.column_mapping['date']
            
            query = f"SELECT MIN({date_col}), MAX({date_col}) FROM {self.table_name}"
            result = pd.read_sql_query(query, conn)
            conn.close()
            
            min_date = result.iloc[0, 0]
            max_date = result.iloc[0, 1]
            
            return min_date, max_date
            
        except Exception as e:
            self.logger.error(f"獲取數據日期範圍失敗: {e}")
            return None, None
    
    def is_available(self) -> bool:
        """檢查PE數據是否可用"""
        return self.table_name is not None and len(self.get_available_stocks()) > 0
