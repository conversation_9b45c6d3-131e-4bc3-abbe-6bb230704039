#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於 GitHub 教學改進的月營收爬蟲
使用 BeautifulSoup 直接解析 HTML 表格
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import datetime
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_company_basic_data(company_id, url):
    """
    基於 GitHub 教學的單一公司資料爬取函數
    
    Args:
        company_id: 公司代號 (如果為空字串，則爬取所有公司)
        url: 目標 URL
    
    Returns:
        list: 包含所有符合條件的公司資料
    """
    data_list = []
    
    try:
        print(f"   📄 訪問: {url}")
        
        # 發送 HTTP 請求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30, verify=False)
        response.raise_for_status()  # 如果請求不成功，會拋出異常

        # 設置正確的編碼
        response.encoding = 'big5'
        
        print(f"   ✅ HTTP 狀態: {response.status_code}")
        print(f"   📊 內容長度: {len(response.content)} bytes")
        
        # 檢查是否被封鎖
        if '您的網頁IP已經被證交所封鎖' in response.text:
            print(f"   ⚠️ IP被封鎖")
            return []
        
        # 使用 BeautifulSoup 解析 HTML 內容
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 找到指定的表格 (第一個 table)
        target_table = soup.find('table')
        
        if not target_table:
            print(f"   ⚠️ 未找到表格")
            return []
        
        # 提取表格中的數據
        rows = target_table.find_all('tr')[2:]  # 忽略前兩行，因為它們是表頭
        print(f"   📋 找到 {len(rows)} 行資料")
        
        for row in rows:
            columns = row.find_all('td')
            if columns and len(columns) >= 7:  # 確保這是一行完整數據
                try:
                    # 提取各欄位資料
                    fetched_company_id = columns[0].text.strip()
                    
                    # 如果指定了公司代號，只處理該公司；否則處理所有公司
                    if company_id and fetched_company_id != company_id:
                        continue
                    
                    # 跳過合計行
                    if fetched_company_id == '合計' or not fetched_company_id:
                        continue
                    
                    # 提取公司資料
                    company_name = columns[1].text.strip()
                    monthly_revenue = columns[2].text.strip().replace(',', '')
                    last_month_revenue = columns[3].text.strip().replace(',', '')
                    last_year_month_revenue = columns[4].text.strip().replace(',', '')
                    monthly_growth_rate = columns[5].text.strip().replace('%', '')
                    last_year_growth_rate = columns[6].text.strip().replace('%', '')
                    
                    # 處理累計營收 (如果存在)
                    current_cumulative = columns[7].text.strip().replace(',', '') if len(columns) > 7 else ''
                    last_year_cumulative = columns[8].text.strip().replace(',', '') if len(columns) > 8 else ''
                    cumulative_growth_rate = columns[9].text.strip().replace('%', '') if len(columns) > 9 else ''
                    
                    # 處理備註 (如果存在)
                    remark = columns[10].text.strip() if len(columns) > 10 else ''
                    
                    # 清理數值 (將 '--' 轉為 0)
                    monthly_revenue = monthly_revenue.replace('--', '0')
                    last_month_revenue = last_month_revenue.replace('--', '0')
                    last_year_month_revenue = last_year_month_revenue.replace('--', '0')
                    monthly_growth_rate = monthly_growth_rate.replace('--', '0')
                    last_year_growth_rate = last_year_growth_rate.replace('--', '0')
                    current_cumulative = current_cumulative.replace('--', '0')
                    last_year_cumulative = last_year_cumulative.replace('--', '0')
                    cumulative_growth_rate = cumulative_growth_rate.replace('--', '0')
                    
                    # 建立資料字典
                    data = {
                        '公司代號': fetched_company_id,
                        '公司名稱': company_name,
                        '當月營收': monthly_revenue,
                        '上月營收': last_month_revenue,
                        '去年當月營收': last_year_month_revenue,
                        '上月比較增減(%)': monthly_growth_rate,
                        '去年同月增減(%)': last_year_growth_rate,
                        '當月累計營收': current_cumulative,
                        '去年累計營收': last_year_cumulative,
                        '前期比較增減(%)': cumulative_growth_rate,
                        '備註': remark
                    }
                    
                    data_list.append(data)
                    
                except Exception as e:
                    print(f"   ⚠️ 處理行資料時出錯: {str(e)[:50]}...")
                    continue
        
        print(f"   ✅ 成功提取 {len(data_list)} 筆資料")
        return data_list
        
    except requests.RequestException as e:
        print(f"   ❌ 請求錯誤: {e}")
        return []
    except Exception as e:
        print(f"   ❌ 解析錯誤: {str(e)[:50]}...")
        return []

def get_monthly_revenue_improved(market, year, month, date, foreign=False):
    """
    改進版月營收爬取函數
    
    Args:
        market: 'sii' (上市) 或 'otc' (上櫃)
        year: 民國年
        month: 月份
        date: 日期物件
        foreign: False=國內公司(_0), True=國外公司(_1)
    
    Returns:
        pandas.DataFrame: 月營收資料
    """
    suffix = '1' if foreign else '0'
    type_name = '國外' if foreign else '國內'
    market_name = '上市' if market == 'sii' else '上櫃'
    
    url = f'https://mopsov.twse.com.tw/nas/t21/{market}/t21sc03_{year}_{month}_{suffix}.html'
    
    print(f"🔄 爬取{type_name}{market_name} ({year}年{month}月)")
    
    # 爬取所有公司資料 (company_id 設為空字串)
    company_data_list = get_company_basic_data('', url)
    
    if not company_data_list:
        print(f"   ❌ 無資料")
        return pd.DataFrame()
    
    # 轉換為 DataFrame
    df = pd.DataFrame(company_data_list)
    
    # 數值欄位轉換
    numeric_columns = ['當月營收', '上月營收', '去年當月營收', '上月比較增減(%)', 
                      '去年同月增減(%)', '當月累計營收', '去年累計營收', '前期比較增減(%)']
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 添加日期和股票ID
    df['date'] = date
    df['stock_id'] = df['公司代號'].astype(str) + ' ' + df['公司名稱'].astype(str)
    
    # 設置索引
    df = df.set_index(['stock_id', 'date'])
    
    # 移除不需要的欄位
    df = df.drop(['公司代號', '公司名稱'], axis=1)
    
    print(f"   ✅ 成功處理 {len(df)} 筆資料")
    return df

def crawl_monthly_revenue_improved(market, year, month, date):
    """
    完整的月營收爬蟲 (國內+國外)
    
    Args:
        market: 'sii' (上市) 或 'otc' (上櫃)
        year: 民國年
        month: 月份
        date: 日期物件
    
    Returns:
        pandas.DataFrame: 合併後的月營收資料
    """
    market_name = '上市' if market == 'sii' else '上櫃'
    print(f"📊 開始爬取{market_name}月營收 ({year}年{month}月)")
    
    all_data = []
    
    # 爬取國內公司
    domestic_data = get_monthly_revenue_improved(market, year, month, date, foreign=False)
    if len(domestic_data) > 0:
        all_data.append(domestic_data)
    
    # 延遲避免被封鎖
    time.sleep(random.uniform(2, 4))
    
    # 爬取國外公司
    foreign_data = get_monthly_revenue_improved(market, year, month, date, foreign=True)
    if len(foreign_data) > 0:
        all_data.append(foreign_data)
    
    # 合併資料
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=False)
        print(f"📈 {market_name}總計: {len(combined_df)} 筆資料")
        return combined_df
    else:
        print(f"❌ {market_name}無資料")
        return pd.DataFrame()

def crawl_all_monthly_revenue_improved(date):
    """
    爬取所有月營收資料 (上市+上櫃，國內+國外)
    
    Args:
        date: 日期物件
    
    Returns:
        pandas.DataFrame: 完整的月營收資料
    """
    # 計算民國年月
    year = date.year - 1911
    month = (date.month + 10) % 12 + 1
    if month == 12:
        year -= 1
    
    print(f"🚀 開始爬取完整月營收 - {date.strftime('%Y-%m-%d')} (民國{year}年{month}月)")
    
    all_data = []
    
    # 爬取上市
    sii_data = crawl_monthly_revenue_improved('sii', year, month, date)
    if len(sii_data) > 0:
        all_data.append(sii_data)
    
    # 延遲避免被封鎖
    time.sleep(random.uniform(3, 6))
    
    # 爬取上櫃
    otc_data = crawl_monthly_revenue_improved('otc', year, month, date)
    if len(otc_data) > 0:
        all_data.append(otc_data)
    
    # 合併所有資料
    if all_data:
        final_df = pd.concat(all_data, ignore_index=False)
        print(f"🎉 完整月營收爬取成功: {len(final_df)} 筆資料")
        return final_df
    else:
        print(f"❌ 完整月營收爬取失敗")
        return pd.DataFrame()

if __name__ == "__main__":
    # 測試改進版爬蟲
    test_date = datetime.date(2024, 11, 1)
    result = crawl_all_monthly_revenue_improved(test_date)
    
    if len(result) > 0:
        print(f"\n📊 測試結果:")
        print(f"   總筆數: {len(result)}")
        print(f"   欄位: {list(result.columns)}")
        print(f"   前5筆:")
        print(result.head())
        
        # 保存結果
        result.to_csv('improved_monthly_revenue_test.csv', encoding='utf-8-sig')
        print(f"   💾 已保存: improved_monthly_revenue_test.csv")
    else:
        print(f"❌ 測試失敗")
