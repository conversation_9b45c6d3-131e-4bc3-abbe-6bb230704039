#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的日期重疊檢查功能測試
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta

def test_date_overlap_logic():
    """測試日期重疊檢查邏輯"""
    print("=" * 60)
    print("📅 測試日期重疊檢查邏輯")
    print("=" * 60)
    
    # 模擬現有數據庫中的日期
    existing_dates = []
    base_date = datetime(2025, 7, 1)
    for i in range(20):  # 模擬20天的現有數據
        date_str = (base_date + timedelta(days=i)).strftime('%Y%m%d')
        existing_dates.append(date_str)
    
    print(f"📊 模擬現有數據: {len(existing_dates)} 天")
    print(f"📅 範圍: {existing_dates[0]} 至 {existing_dates[-1]}")
    
    # 測試不同的請求範圍
    test_cases = [
        ("2025-07-15", "2025-07-25", "部分重疊"),
        ("2025-07-01", "2025-07-10", "完全重疊"),
        ("2025-07-25", "2025-08-05", "部分重疊"),
        ("2025-08-01", "2025-08-10", "無重疊"),
    ]
    
    for start_date, end_date, description in test_cases:
        print(f"\n🔍 測試案例: {description}")
        print(f"📅 請求範圍: {start_date} 至 {end_date}")
        
        # 模擬 find_date_overlaps 邏輯
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        requested_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            requested_dates.append(current_date.strftime('%Y%m%d'))
            current_date += timedelta(days=1)
        
        overlapping_dates = [date for date in requested_dates if date in existing_dates]
        new_dates = [date for date in requested_dates if date not in existing_dates]
        
        print(f"📊 請求天數: {len(requested_dates)}")
        print(f"⚠️ 重疊天數: {len(overlapping_dates)}")
        print(f"🆕 新增天數: {len(new_dates)}")
        
        if overlapping_dates:
            print(f"📋 重疊日期: {', '.join(overlapping_dates[:5])}{'...' if len(overlapping_dates) > 5 else ''}")
        
        if new_dates:
            print(f"📋 新增日期: {', '.join(new_dates[:5])}{'...' if len(new_dates) > 5 else ''}")
        
        # 模擬用戶選擇
        if overlapping_dates:
            print(f"💡 建議: 選擇「跳過重疊」可節省 {len(overlapping_dates)} 天的下載時間")

def check_actual_database():
    """檢查實際數據庫狀態"""
    print("\n" + "=" * 60)
    print("📊 檢查實際數據庫狀態")
    print("=" * 60)
    
    db_files = {
        'market_index': ('市場指數資訊', 'market_index.db', 'market_index_info'),
        'margin_trading': ('融資融券統計', 'margin_trading.db', 'margin_trading_info'),
        'historical_index': ('歷史指數資料', 'market_historical.db', 'market_historical_index')
    }
    
    for data_type, (display_name, db_file, table_name) in db_files.items():
        print(f"\n📊 {display_name}")
        print("-" * 40)
        
        db_path = f"D:/Finlab/history/tables/{db_file}"
        
        try:
            conn = sqlite3.connect(db_path)
            
            if data_type == 'historical_index':
                # 歷史數據使用 Date 欄位
                query = f"""
                SELECT 
                    MIN(Date) as min_date, 
                    MAX(Date) as max_date, 
                    COUNT(DISTINCT Date) as date_count,
                    COUNT(*) as total_records
                FROM {table_name}
                """
                cursor = conn.execute(query)
                result = cursor.fetchone()
                
                if result and result[0]:
                    min_date, max_date, date_count, total_records = result
                    
                    # 轉換日期格式顯示
                    try:
                        min_date_formatted = datetime.strptime(min_date, '%Y%m%d').strftime('%Y-%m-%d')
                        max_date_formatted = datetime.strptime(max_date, '%Y%m%d').strftime('%Y-%m-%d')
                    except:
                        min_date_formatted = min_date
                        max_date_formatted = max_date
                    
                    print(f"✅ 數據庫存在")
                    print(f"📅 日期範圍: {min_date_formatted} 至 {max_date_formatted}")
                    print(f"📊 總天數: {date_count} 天")
                    print(f"📈 總記錄數: {total_records:,} 筆")
                    
                    # 檢查最近的數據
                    recent_query = f"SELECT Date FROM {table_name} ORDER BY Date DESC LIMIT 3"
                    recent_dates = [row[0] for row in conn.execute(recent_query).fetchall()]
                    if recent_dates:
                        formatted_recent = []
                        for date_str in recent_dates:
                            try:
                                formatted_recent.append(datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d'))
                            except:
                                formatted_recent.append(date_str)
                        print(f"🕒 最近3天: {', '.join(formatted_recent)}")
                else:
                    print(f"⚠️ 數據庫存在但無數據")
            else:
                # 其他數據使用 crawl_time 欄位
                query = f"""
                SELECT 
                    MIN(DATE(crawl_time)) as min_date, 
                    MAX(DATE(crawl_time)) as max_date, 
                    COUNT(DISTINCT DATE(crawl_time)) as date_count,
                    COUNT(*) as total_records
                FROM {table_name}
                """
                cursor = conn.execute(query)
                result = cursor.fetchone()
                
                if result and result[0]:
                    min_date, max_date, date_count, total_records = result
                    print(f"✅ 數據庫存在")
                    print(f"📅 日期範圍: {min_date} 至 {max_date}")
                    print(f"📊 總天數: {date_count} 天")
                    print(f"📈 總記錄數: {total_records:,} 筆")
                else:
                    print(f"⚠️ 數據庫存在但無數據")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 無法訪問數據庫: {str(e)}")

def main():
    """主函數"""
    print("📅 日期重疊檢查功能測試")
    print("🎯 測試智能日期重疊檢測邏輯")
    
    # 測試邏輯
    test_date_overlap_logic()
    
    # 檢查實際數據庫
    check_actual_database()
    
    print("\n" + "=" * 60)
    print("🎉 測試完成")
    print("=" * 60)
    print("✅ 日期重疊檢查邏輯正常")
    print("✅ 數據庫狀態檢查正常")
    print("💡 建議: 使用GUI界面測試完整功能")

if __name__ == "__main__":
    main()
