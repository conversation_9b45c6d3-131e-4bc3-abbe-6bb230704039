#!/usr/bin/env python3
"""
測試增強版GUI的功能
Test Enhanced GUI Features
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta

def test_database_connection():
    """測試資料庫連接"""
    print("🔍 測試資料庫連接...")
    
    db_path = "db/price.db"
    if not os.path.exists(db_path):
        print(f"❌ 資料庫文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表結構
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"✅ 找到表格: {[t[0] for t in tables]}")
        
        # 檢查數據
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        count = cursor.fetchone()[0]
        print(f"✅ 股價數據記錄數: {count:,}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return False

def test_strategy_files():
    """測試策略文件"""
    print("\n🔍 測試策略文件...")
    
    strategy_dir = "strategies"
    if not os.path.exists(strategy_dir):
        print(f"❌ 策略目錄不存在: {strategy_dir}")
        return False
    
    strategy_files = [
        "勝率73%.json",
        "MA趨勢與RSI動能.json", 
        "智能多空策略.json",
        "動態停損策略.json",
        "突破回踩策略.json",
        "智能波段策略.json"
    ]
    
    for strategy_file in strategy_files:
        file_path = os.path.join(strategy_dir, strategy_file)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    strategy_data = json.load(f)
                print(f"✅ {strategy_file} - {strategy_data.get('name', '未知')}")
            except Exception as e:
                print(f"❌ {strategy_file} 讀取失敗: {e}")
        else:
            print(f"⚠️ {strategy_file} 不存在")
    
    return True

def test_imports():
    """測試模組導入"""
    print("\n🔍 測試模組導入...")
    
    try:
        import PyQt6
        print("✅ PyQt6")
    except ImportError as e:
        print(f"❌ PyQt6: {e}")
        return False
    
    try:
        import pandas
        print("✅ pandas")
    except ImportError as e:
        print(f"❌ pandas: {e}")
        return False
    
    try:
        import numpy
        print("✅ numpy")
    except ImportError as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        # 嘗試導入enhanced_strategies模組
        import enhanced_strategies
        print("✅ enhanced_strategies")
    except ImportError as e:
        print(f"⚠️ enhanced_strategies: {e} (將使用內建策略)")
    
    try:
        from backtest_engine import BacktestEngine
        print("✅ backtest_engine")
    except ImportError as e:
        print(f"❌ backtest_engine: {e}")
        return False
    
    return True

def create_sample_data():
    """創建樣本數據（如果不存在）"""
    print("\n🔍 檢查樣本數據...")
    
    db_path = "db/price.db"
    if not os.path.exists("db"):
        os.makedirs("db")
        print("✅ 創建 db 目錄")
    
    if not os.path.exists(db_path):
        print("🔧 創建樣本資料庫...")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 創建表格
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS stock_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    date TEXT NOT NULL,
                    Open REAL,
                    High REAL,
                    Low REAL,
                    Close REAL,
                    Volume INTEGER,
                    UNIQUE(stock_id, date)
                )
            """)
            
            # 創建樣本數據
            import random
            sample_stocks = ['2330', '2317', '2454', '2303', '1301']
            
            for stock_id in sample_stocks:
                base_price = random.uniform(100, 500)
                for i in range(100):  # 100天的數據
                    date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
                    
                    # 生成簡單的價格數據
                    price_change = random.uniform(-0.05, 0.05)  # ±5%變動
                    base_price *= (1 + price_change)
                    
                    open_price = base_price * random.uniform(0.98, 1.02)
                    high_price = max(open_price, base_price) * random.uniform(1.0, 1.05)
                    low_price = min(open_price, base_price) * random.uniform(0.95, 1.0)
                    close_price = base_price
                    volume = random.randint(10000, 1000000)
                    
                    cursor.execute("""
                        INSERT OR IGNORE INTO stock_daily_data 
                        (stock_id, date, Open, High, Low, Close, Volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (stock_id, date, open_price, high_price, low_price, close_price, volume))
            
            conn.commit()
            conn.close()
            print("✅ 樣本資料庫創建完成")
            
        except Exception as e:
            print(f"❌ 創建樣本資料庫失敗: {e}")
            return False
    
    return True

def main():
    """主測試函數"""
    print("🚀 O3mh增強版GUI測試開始")
    print("=" * 50)
    
    # 測試各個組件
    tests = [
        test_imports,
        create_sample_data,
        test_database_connection,
        test_strategy_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！可以執行GUI程式")
        print("\n啟動指令:")
        print("python run_enhanced_gui.py")
        print("\n或直接運行:")
        print("python O3mh_gui_v22_enhanced.py")
        
        print("\n✨ 新功能說明:")
        print("1. 🎯 策略懸停提示 - 滑鼠懸停策略下拉選單查看詳細信息")
        print("2. 📊 專業回測系統 - 完整的策略回測和績效分析")
        print("3. 🎨 精緻化界面 - 優化按鈕尺寸和視覺效果")
        print("4. 🔧 修正計算邏輯 - 確保選股功能正常運作")
        
    else:
        print("⚠️ 部分測試失敗，請檢查環境配置")
        print("\n常見問題解決:")
        print("1. 確保安裝所有依賴: pip install PyQt6 pandas numpy talib")
        print("2. 確保資料庫文件存在: db/price.db")
        print("3. 確保策略文件存在: strategies/*.json")

if __name__ == "__main__":
    main() 