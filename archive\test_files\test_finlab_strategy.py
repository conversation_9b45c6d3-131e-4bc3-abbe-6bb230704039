#!/usr/bin/env python3
"""
測試 小資族資優生策略
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入策略類別
from O3mh_gui_v21_optimized import SmallInvestorEliteStrategy

def create_test_data():
    """創建測試數據"""
    # 創建60天的測試數據
    dates = pd.date_range(start='2024-01-01', periods=60, freq='D')
    
    # 模擬股價數據
    np.random.seed(42)
    base_price = 50
    price_changes = np.random.normal(0, 0.02, 60)  # 2%的日波動
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))  # 確保價格不為負
    
    # 創建OHLCV數據
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = price * (1 + np.random.normal(0, 0.005))
        volume = np.random.randint(100000, 500000)  # 10萬到50萬股
        
        data.append({
            'Date': date,
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    return df

def test_small_investor_elite_strategy():
    """測試 小資族資優生策略"""
    print("🚀 開始測試 小資族資優生策略")
    print("=" * 50)

    # 創建測試數據
    df = create_test_data()
    print(f"✅ 創建測試數據: {len(df)} 天")
    print(f"📊 價格範圍: {df['Close'].min():.2f} - {df['Close'].max():.2f}")
    print(f"📈 成交量範圍: {df['Volume'].min():,} - {df['Volume'].max():,}")

    # 創建策略實例
    strategy = SmallInvestorEliteStrategy()
    print(f"✅ 創建策略實例: {strategy.name}")
    
    # 測試各個條件檢查
    print("\n🔍 測試各個條件:")
    print("-" * 50)

    # 1. 市值條件
    result1, reason1 = strategy.check_market_value_condition(df)
    print(f"1. 市值條件: {'✅' if result1 else '❌'} - {reason1}")

    # 2. 自由現金流條件
    result2, reason2 = strategy.check_free_cash_flow_condition()
    print(f"2. 自由現金流: {'✅' if result2 else '❌'} - {reason2}")

    # 3. ROE條件
    result3, reason3 = strategy.check_roe_condition()
    print(f"3. ROE條件: {'✅' if result3 else '❌'} - {reason3}")

    # 4. 營業利益成長率條件
    result4, reason4 = strategy.check_operating_growth_condition()
    print(f"4. 營業利益成長率: {'✅' if result4 else '❌'} - {reason4}")

    # 5. 市值營收比條件
    result5, reason5 = strategy.check_ps_ratio_condition(df)
    print(f"5. 市值營收比: {'✅' if result5 else '❌'} - {reason5}")

    # 6. 成交量條件
    result6, reason6 = strategy.check_volume_condition(df)
    print(f"6. 成交量條件: {'✅' if result6 else '❌'} - {reason6}")

    # 7. RSV技術指標
    result7, reason7 = strategy.check_rsv_ranking_condition(df)
    print(f"7. RSV技術指標: {'✅' if result7 else '❌'} - {reason7}")

    print("\n⚠️  重要提醒:")
    print("=" * 50)
    print("🚨 小資族資優生策略需要以下真實財務數據才能正確運作:")
    print("   • 市值數據 (股本 × 股價)")
    print("   • 自由現金流 (投資活動現金流 + 營業活動現金流)")
    print("   • ROE數據 (稅後淨利 / 權益總計)")
    print("   • 營業利益成長率")
    print("   • 月營收數據 (計算當季營收)")
    print("   • 全市場股票數據 (進行RSV排名)")
    print("\n💡 建議整合以下數據源:")
    print("   • TEJ台灣經濟新報")
    print("   • 財報狗")
    print("   • 公開資訊觀測站")
    print("   • finlab數據庫")
    
    # 測試完整分析
    print("\n📊 完整策略分析:")
    print("-" * 50)

    analysis_result = strategy.analyze_stock(df)

    print(f"策略名稱: {analysis_result.get('strategy_name', 'N/A')}")
    print(f"是否適合: {'✅ 適合' if analysis_result['suitable'] else '❌ 不適合'}")
    print(f"評分: {analysis_result['score']}/6")
    print(f"原因: {analysis_result['reason']}")

    # 顯示缺少的數據
    if analysis_result.get('missing_data'):
        print(f"\n🚨 缺少的關鍵數據:")
        for data_type in analysis_result['missing_data']:
            print(f"   • {data_type}")

    # 顯示詳細結果
    if analysis_result.get('details'):
        print("\n📋 詳細條件檢查:")
        for condition, result in analysis_result['details'].items():
            status = '✅' if result['pass'] else '❌'
            print(f"  {status} {condition}: {result['reason']}")

    print("\n" + "=" * 50)
    print("🎯 測試完成!")

    return analysis_result

if __name__ == "__main__":
    try:
        result = test_small_investor_elite_strategy()

        # 顯示最終結果
        print("\n" + "=" * 60)
        if result['suitable']:
            print("🎉 測試結果：該股票符合 小資族資優生策略條件")
            print("   (注意：此為模擬測試，實際應用需要真實財務數據)")
        else:
            print("❌ 測試結果：該股票不符合 小資族資優生策略條件")
            if result.get('missing_data'):
                print("🚨 主要原因：缺少關鍵財務數據")
                print("💡 建議：整合真實財務數據庫後重新測試")
            else:
                print("💡 建議：可考慮其他策略或等待條件改善")

        print("\n📌 下一步行動:")
        print("1. 整合真實財務數據源 (TEJ、財報狗等)")
        print("2. 建立完整的數據管道")
        print("3. 進行歷史回測驗證")
        print("4. 設定定期選股排程")

    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
