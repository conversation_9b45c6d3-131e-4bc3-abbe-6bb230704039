#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 price 爬蟲保存到 price.db 的功能
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

# 添加 auto_update.py 的路徑
sys.path.insert(0, current_dir)

def test_price_save():
    """測試 price 爬蟲保存到 price.db"""
    print("=" * 80)
    print("🧪 測試 price 爬蟲保存到 price.db")
    print("=" * 80)
    
    try:
        # 導入必要的函數
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        print("✅ 成功導入爬蟲函數")
        
        # 使用最近的交易日
        test_date = datetime(2024, 12, 30)  # 使用一個已知的交易日
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 檢查清理前的資料庫狀態
        price_db_path = 'D:/Finlab/history/tables/price.db'
        
        if os.path.exists(price_db_path):
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 檢查該日期是否已有資料
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            existing_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_before = cursor.fetchone()[0]
            
            cursor.execute("SELECT MAX(date) FROM stock_daily_data")
            last_date = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"📊 資料庫狀態:")
            print(f"   總記錄數: {total_before:,}")
            print(f"   最後日期: {last_date}")
            print(f"   測試日期現有記錄: {existing_count:,}")
        else:
            print("❌ price.db 不存在")
            return
        
        print(f"\n🚀 執行 price 爬蟲...")
        
        # 執行爬蟲
        df = crawl_price(test_date)
        
        if df.empty:
            print("⚠️ 爬蟲返回空資料，可能是非交易日或網路問題")
            return
        
        print(f"✅ 爬蟲成功，獲取 {len(df)} 筆資料")
        
        # 檢查資料內容
        df_reset = df.reset_index()
        print(f"\n📊 爬取資料分析:")
        print(f"   欄位: {list(df_reset.columns)}")
        
        if 'listing_status' in df_reset.columns:
            listing_stats = df_reset['listing_status'].value_counts()
            print(f"   市場別分布:")
            for status, count in listing_stats.items():
                status_name = status if status else 'ETF/未分類'
                print(f"     {status_name}: {count} 檔")
            
            # 檢查是否有興櫃股票
            rotc_count = len(df_reset[df_reset['listing_status'] == '興櫃'])
            if rotc_count == 0:
                print(f"   ✅ 確認：爬取資料中無興櫃股票")
            else:
                print(f"   ⚠️ 警告：爬取資料中仍有 {rotc_count} 檔興櫃股票")
        
        print(f"\n💾 保存資料到 price.db...")
        
        # 保存到資料庫
        if save_price_to_newprice_database(df, price_db_path):
            print(f"✅ 資料保存成功")
            
            # 驗證保存結果
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 檢查該日期的資料
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ?", (test_date.strftime('%Y-%m-%d'),))
            saved_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_after = cursor.fetchone()[0]
            
            # 檢查是否有興櫃股票被保存
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ? AND listing_status = '興櫃'", (test_date.strftime('%Y-%m-%d'),))
            saved_rotc = cursor.fetchone()[0]
            
            # 檢查是否有權證被保存
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE date = ? AND stock_id LIKE '7%'", (test_date.strftime('%Y-%m-%d'),))
            saved_warrant = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"\n📊 保存驗證:")
            print(f"   該日期保存記錄: {saved_count:,}")
            print(f"   資料庫總記錄: {total_after:,}")
            print(f"   該日期興櫃記錄: {saved_rotc:,}")
            print(f"   該日期權證記錄: {saved_warrant:,}")
            
            if saved_rotc == 0 and saved_warrant == 0:
                print(f"   ✅ 驗證通過：沒有興櫃股票和權證被保存")
            else:
                print(f"   ⚠️ 驗證失敗：仍有不應保存的資料")
            
            # 顯示保存的資料範例
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT stock_id, stock_name, listing_status, Close 
                FROM stock_daily_data 
                WHERE date = ? 
                ORDER BY stock_id 
                LIMIT 10
            """, (test_date.strftime('%Y-%m-%d'),))
            
            sample_data = cursor.fetchall()
            conn.close()
            
            print(f"\n📋 保存資料範例:")
            for row in sample_data:
                print(f"   {row}")
            
        else:
            print(f"❌ 資料保存失敗")
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_price_save()
