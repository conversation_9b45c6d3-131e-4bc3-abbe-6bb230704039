#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重現導入錯誤測試
"""

import sys
import pandas as pd
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(level=logging.DEBUG)

def test_import_error_reproduction():
    """重現導入錯誤"""
    
    print("🔍 重現導入錯誤測試")
    print("=" * 40)
    
    # 1. 測試當前的導入
    print("1️⃣ 測試當前的導入...")
    
    try:
        # 模擬GUI中的導入邏輯
        if not hasattr(test_import_error_reproduction, '_turtle_strategy_instance'):
            from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
            test_import_error_reproduction._turtle_strategy_instance = HighYieldTurtleStrategyOptimized()
            print("   ✅ 成功導入和創建實例")
        
        turtle_strategy = test_import_error_reproduction._turtle_strategy_instance
        print(f"   📊 策略實例: {turtle_strategy.name}")
        
    except ImportError as e:
        print(f"   ❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他錯誤: {e}")
        return False
    
    # 2. 測試可能的拼寫錯誤
    print("\n2️⃣ 測試可能的拼寫錯誤...")
    
    possible_typos = [
        'HighYieldTurtleStraategy',  # 多一個 a
        'HighYieldTurtleStrrategy',  # 多一個 r
        'HighYieldTurtleStrtegy',    # 少一個 a
        'HighYieldTurtleStrategy',   # 原始名稱
    ]
    
    for typo in possible_typos:
        try:
            exec(f"from strategies.high_yield_turtle_strategy import {typo}")
            print(f"   ✅ 意外成功: {typo}")
        except ImportError as e:
            print(f"   ❌ 預期失敗: {typo} - {e}")
    
    # 3. 檢查策略文件中的實際類名
    print("\n3️⃣ 檢查策略文件中的實際類名...")
    
    try:
        import strategies.high_yield_turtle_strategy as strategy_module
        
        # 獲取模組中的所有類
        classes = [name for name in dir(strategy_module) if name.startswith('HighYield')]
        print(f"   📋 找到的類: {classes}")
        
        # 檢查每個類
        for class_name in classes:
            cls = getattr(strategy_module, class_name)
            if hasattr(cls, '__init__'):
                print(f"   ✅ 有效類: {class_name}")
            
    except Exception as e:
        print(f"   ❌ 檢查失敗: {e}")
    
    # 4. 測試完整的策略分析流程
    print("\n4️⃣ 測試完整的策略分析流程...")
    
    try:
        # 創建測試數據
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        prices = [100 * (1.005 ** i) for i in range(100)]
        test_df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': [100000] * 100
        }, index=dates)
        
        # 測試分析
        result = turtle_strategy.analyze_stock(test_df, stock_id='2881')
        
        print(f"   ✅ 分析成功")
        print(f"   📊 結果: {result['suitable']}")
        print(f"   📈 評分: {result['score']}")
        print(f"   💬 原因: {result['reason']}")
        
    except Exception as e:
        print(f"   ❌ 分析失敗: {e}")
        return False
    
    print(f"\n" + "=" * 40)
    print(f"✅ 導入錯誤重現測試完成！")
    
    return True

def test_gui_import_simulation():
    """模擬GUI中的導入過程"""
    
    print("\n🖥️ 模擬GUI中的導入過程")
    print("=" * 40)
    
    class MockGUIClass:
        def __init__(self):
            pass
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬GUI中的策略檢查方法"""
            try:
                if len(df) < 60:
                    return False, "數據不足，需要至少60天數據"

                # 使用單例模式避免重複載入PKL數據
                if not hasattr(self, '_turtle_strategy_instance'):
                    print("   🔄 首次載入策略...")
                    from strategies.high_yield_turtle_strategy import HighYieldTurtleStrategyOptimized
                    self._turtle_strategy_instance = HighYieldTurtleStrategyOptimized()
                    print("   ✅ 策略載入成功")

                turtle_strategy = self._turtle_strategy_instance

                # 執行高殖利率烏龜分析
                result = turtle_strategy.analyze_stock(df, stock_id=stock_id)

                # 提取詳細信息用於表格顯示
                details = result.get('details', {})
                
                # 構建詳細的返回信息
                detailed_info = {
                    'suitable': result['suitable'],
                    'reason': result['reason'],
                    'score': result['score'],
                    'dividend_yield': details.get('dividend_yield', 0),
                    'pe_ratio': details.get('pe_ratio', 0),
                    'pb_ratio': details.get('pb_ratio', 0),
                    'close_price': details.get('close_price', 0),
                    'volume': details.get('volume', 0),
                    'data_source': details.get('data_source', 'FinLab PKL')
                }

                # 轉換為舊格式的返回值，但附加詳細信息
                if result['suitable']:
                    return True, result['reason'], detailed_info
                else:
                    return False, result['reason'], detailed_info

            except ImportError as e:
                logging.warning(f"⚠️ 高殖利率烏龜策略模組未找到: {e}")
                return False, f"導入錯誤: {str(e)}", {}
            except Exception as e:
                logging.error(f"高殖利率烏龜策略檢查失敗: {e}")
                return False, f"策略檢查錯誤: {str(e)}", {}
    
    # 測試模擬GUI
    mock_gui = MockGUIClass()
    
    # 創建測試數據
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    prices = [100 * (1.005 ** i) for i in range(100)]
    test_df = pd.DataFrame({
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': [100000] * 100
    }, index=dates)
    
    # 測試多次調用（模擬GUI中的多次選股）
    test_stocks = ['2881', '2882', '1108', '2330', '1101']
    
    for i, stock_id in enumerate(test_stocks):
        print(f"   📊 測試股票 {i+1}/5: {stock_id}")
        try:
            result = mock_gui.check_high_yield_turtle_strategy(test_df, stock_id=stock_id)
            
            if len(result) == 3:
                matched, message, detailed_info = result
                score = detailed_info.get('score', 0)
                dividend = detailed_info.get('dividend_yield', 0)
                status = "✅ 符合" if matched else "❌ 不符合"
                print(f"      {status} - 評分: {score}, 殖利率: {dividend:.2f}%")
            else:
                matched, message = result
                status = "✅ 符合" if matched else "❌ 不符合"
                print(f"      {status} - {message}")
                
        except Exception as e:
            print(f"      ❌ 測試失敗: {e}")
    
    return True

if __name__ == "__main__":
    success1 = test_import_error_reproduction()
    success2 = test_gui_import_simulation()
    
    if success1 and success2:
        print(f"\n🎉 測試總結:")
        print(f"✅ 導入錯誤已修復")
        print(f"✅ GUI模擬測試成功")
        print(f"✅ 策略分析功能正常")
        print(f"🚀 高殖利率烏龜策略可以正常使用了！")
    else:
        print(f"\n❌ 測試失敗，需要進一步檢查")
