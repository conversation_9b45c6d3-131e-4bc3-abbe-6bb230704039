#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試排序功能和成交量單位修正
"""

def test_sorting_and_volume_fixes():
    """測試排序功能和成交量單位修正"""
    print("🔍 測試排序功能和成交量單位修正")
    print("=" * 50)
    
    try:
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查1: 成交量單位修正
        print("\n1. 檢查成交量單位修正...")
        if '成交量(張)' in content:
            print("   ✅ 右側表格標題已改為「成交量(張)」")
        else:
            print("   ❌ 右側表格標題未修正")
        
        if 'volume_lots = volume_shares / 1000' in content:
            print("   ✅ 成交量轉換邏輯已添加（股 → 張）")
        else:
            print("   ❌ 成交量轉換邏輯未添加")
        
        if 'M張' in content and 'K張' in content:
            print("   ✅ 左側列表成交量單位已修正為「張」")
        else:
            print("   ❌ 左側列表成交量單位未修正")
        
        # 檢查2: 排序功能改進
        print("\n2. 檢查排序功能改進...")
        if 'def setup_custom_sorting(self):' in content:
            print("   ✅ 自定義排序方法已添加")
        else:
            print("   ❌ 自定義排序方法未添加")
        
        if 'def handle_header_click(self, logical_index):' in content:
            print("   ✅ 標題點擊處理方法已添加")
        else:
            print("   ❌ 標題點擊處理方法未添加")
        
        if 'def sort_by_numeric_column(self, column, order):' in content:
            print("   ✅ 數值欄位排序方法已添加")
        else:
            print("   ❌ 數值欄位排序方法未添加")
        
        # 檢查3: 數值存儲邏輯
        print("\n3. 檢查數值存儲邏輯...")
        if 'Qt.ItemDataRole.UserRole' in content:
            print("   ✅ 原始數值存儲邏輯已添加")
        else:
            print("   ❌ 原始數值存儲邏輯未添加")
        
        if 'setSortingEnabled(False)' in content and 'setSortingEnabled(True)' in content:
            print("   ✅ 排序狀態控制邏輯已添加")
        else:
            print("   ❌ 排序狀態控制邏輯未添加")
        
        # 檢查4: 表格設置改進
        print("\n4. 檢查表格設置改進...")
        if 'horizontalHeader().setSectionsClickable(True)' in content:
            print("   ✅ 表格標題可點擊設置已添加")
        else:
            print("   ❌ 表格標題可點擊設置未添加")
        
        if 'sectionClicked.connect' in content:
            print("   ✅ 標題點擊事件連接已添加")
        else:
            print("   ❌ 標題點擊事件連接未添加")
        
        print("\n" + "=" * 50)
        print("✅ 排序功能和成交量單位修正檢查完成！")
        
        print("\n📋 修正摘要:")
        print("🔧 **排序功能修正**:")
        print("• 添加自定義排序邏輯，確保數值欄位正確排序")
        print("• 收盤價、漲跌幅、成交量按數值大小排序")
        print("• 股票代碼、股票名稱按文字順序排序")
        print("• 支援升序/降序切換")
        
        print("\n💰 **成交量單位修正**:")
        print("• 右側表格標題：「成交量(張)」")
        print("• 左側列表顯示：「量:XXX張」")
        print("• 轉換邏輯：1張 = 1000股")
        print("• 格式化顯示：K張、M張單位")
        
        print("\n🎯 **技術改進**:")
        print("• 使用Qt.ItemDataRole.UserRole存儲原始數值")
        print("• 暫時禁用排序避免數據填入時的問題")
        print("• 自定義標題點擊處理邏輯")
        print("• 保持顏色和格式化顯示")
        
        print("\n📊 **顯示效果**:")
        print("右側表格欄位：")
        print("┌──────────┬──────────┬────────┬──────────┬──────────┐")
        print("│ 股票代碼 │ 股票名稱 │ 收盤價 │ 漲跌幅(%)│成交量(張)│")
        print("├──────────┼──────────┼────────┼──────────┼──────────┤")
        print("│   8086   │  宏捷科  │  94.50 │  +6.18%  │  16.1M   │")
        print("│   1723   │   中碳   │  91.70 │  +5.28%  │   1.5M   │")
        print("└──────────┴──────────┴────────┴──────────┴──────────┘")
        
        print("\n左側列表格式：")
        print(" 1. 8086 宏捷科 94.50 量:16.1M張")
        print(" 2. 1723 中碳 91.70 量:1.5M張")
        print(" 3. 6923 中台 90.50 量:645K張")
        
        return True
        
    except FileNotFoundError:
        print("❌ 找不到 O3mh_gui_v21_optimized.py 檔案")
        return False
    except Exception as e:
        print(f"❌ 檢查過程發生錯誤: {e}")
        return False

if __name__ == "__main__":
    test_sorting_and_volume_fixes()
