#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
調試GoodInfo URL和頁面結構
確認正確的台積電月營收頁面
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def debug_goodinfo_urls():
    """調試不同的GoodInfo URL"""
    print("🔍 調試GoodInfo URL和頁面結構")
    print("=" * 50)
    
    # 測試不同的URL
    test_urls = [
        ("當前使用的URL", "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"),
        ("月營收圖表", "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330&CHT_CAT=MONTH"),
        ("基本資料", "https://goodinfo.tw/tw/StockDetail.asp?STOCK_ID=2330"),
        ("財務報表", "https://goodinfo.tw/tw/ShowFinData.asp?STOCK_ID=2330"),
    ]
    
    try:
        # 設置Chrome選項
        options = Options()
        # 不使用無頭模式，這樣我們可以看到頁面
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("🔧 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=options)
        
        for url_name, url in test_urls:
            print(f"\n📱 測試 {url_name}: {url}")
            
            try:
                driver.get(url)
                time.sleep(3)
                
                # 檢查頁面標題
                title = driver.title
                print(f"   頁面標題: {title}")
                
                # 檢查是否包含台積電相關內容
                page_source = driver.page_source
                
                if "台積電" in page_source or "2330" in page_source:
                    print("   ✅ 包含台積電相關內容")
                else:
                    print("   ❌ 不包含台積電相關內容")
                
                # 檢查是否包含月營收數據
                month_indicators = ["2025/", "2024/", "營收", "億"]
                found_indicators = []
                
                for indicator in month_indicators:
                    if indicator in page_source:
                        found_indicators.append(indicator)
                
                if found_indicators:
                    print(f"   ✅ 包含月營收指標: {found_indicators}")
                else:
                    print("   ❌ 不包含月營收指標")
                
                # 檢查匯出按鈕
                export_buttons = driver.find_elements(By.XPATH, "//input[@value='XLS']")
                print(f"   匯出按鈕數量: {len(export_buttons)}")
                
                for i, button in enumerate(export_buttons):
                    onclick = button.get_attribute("onclick")
                    print(f"     按鈕{i+1}: {onclick}")
                
                # 檢查divDetail
                try:
                    div_detail = driver.find_element(By.ID, "divDetail")
                    detail_text = div_detail.text[:200]
                    print(f"   divDetail內容預覽: {detail_text}")
                except:
                    print("   ❌ 未找到divDetail")
                
                # 如果是主要測試URL，等待用戶檢查
                if url_name == "當前使用的URL":
                    print(f"\n⚠️ 請檢查瀏覽器中的頁面內容")
                    print("   1. 是否顯示台積電的歷史月營收數據？")
                    print("   2. 是否有2025/06, 2025/05等月份？")
                    print("   3. 匯出按鈕會下載什麼內容？")
                    
                    response = input("   頁面是否顯示台積電歷史月營收？(y/n): ").lower().strip()
                    
                    if response == 'y':
                        print("   ✅ 頁面正確，問題可能在匯出邏輯")
                        
                        # 嘗試點擊匯出按鈕
                        if export_buttons:
                            print("   🔄 嘗試點擊匯出按鈕...")
                            try:
                                export_buttons[0].click()
                                print("   ✅ 匯出按鈕點擊成功")
                                time.sleep(3)
                            except Exception as e:
                                print(f"   ❌ 點擊失敗: {e}")
                    else:
                        print("   ❌ 頁面內容不正確，需要尋找正確的URL")
                
            except Exception as e:
                print(f"   ❌ 訪問失敗: {e}")
                continue
        
        input("\n按Enter關閉瀏覽器...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        return False

def suggest_solutions():
    """建議解決方案"""
    print("\n💡 可能的解決方案:")
    print("=" * 30)
    
    print("1. **URL問題**:")
    print("   - 可能需要不同的URL參數")
    print("   - 可能需要先登入或設置cookie")
    
    print("\n2. **頁面操作問題**:")
    print("   - 可能需要先選擇時間範圍")
    print("   - 可能需要點擊特定的選項")
    print("   - 可能有多個匯出按鈕，我們點錯了")
    
    print("\n3. **JavaScript問題**:")
    print("   - export2xls函數可能需要不同的參數")
    print("   - 可能需要先執行其他JavaScript函數")
    
    print("\n4. **替代方案**:")
    print("   - 使用網頁抓取直接解析HTML表格")
    print("   - 手動複製數據")
    print("   - 尋找其他數據源")

def main():
    """主函數"""
    print("🧪 GoodInfo URL和頁面結構調試")
    print("=" * 50)
    
    response = input("是否開啟瀏覽器進行調試？(y/n): ").lower().strip()
    if response != 'y':
        print("❌ 用戶取消")
        suggest_solutions()
        return
    
    success = debug_goodinfo_urls()
    
    if success:
        suggest_solutions()
        print("\n🎉 調試完成！")
    else:
        print("\n❌ 調試失敗")

if __name__ == "__main__":
    main()
