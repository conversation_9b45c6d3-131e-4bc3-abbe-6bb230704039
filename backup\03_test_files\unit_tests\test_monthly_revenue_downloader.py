#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試月營收下載器功能
"""

import sys
import os

def test_imports():
    """測試模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        import pandas as pd
        print("✅ pandas 可用")
    except ImportError:
        print("❌ pandas 未安裝")
        return False
    
    try:
        from selenium import webdriver
        print("✅ selenium 可用")
    except ImportError:
        print("❌ selenium 未安裝，需要: pip install selenium")
        return False
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        print("✅ 月營收下載器核心模組可用")
    except ImportError as e:
        print(f"❌ 月營收下載器核心模組導入失敗: {e}")
        return False
    
    try:
        from monthly_revenue_downloader_gui import MonthlyRevenueDownloaderGUI
        print("✅ 月營收下載器GUI模組可用")
    except ImportError as e:
        print(f"❌ 月營收下載器GUI模組導入失敗: {e}")
        return False
    
    return True

def test_database_creation():
    """測試數據庫創建"""
    print("\n🗄️ 測試數據庫創建...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        print(f"✅ 數據庫路徑: {downloader.db_path}")
        
        # 檢查數據庫文件是否存在
        if os.path.exists(downloader.db_path):
            print("✅ 數據庫文件已創建")
            
            # 檢查表格結構
            import sqlite3
            conn = sqlite3.connect(downloader.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_revenue'")
            if cursor.fetchone():
                print("✅ monthly_revenue 表格已創建")
                
                # 檢查表格結構
                cursor.execute("PRAGMA table_info(monthly_revenue)")
                columns = cursor.fetchall()
                print(f"✅ 表格包含 {len(columns)} 個欄位")
                
                # 顯示主要欄位
                key_columns = ['stock_id', 'year', 'month', 'revenue', 'revenue_yoy']
                for col_info in columns:
                    col_name = col_info[1]
                    if col_name in key_columns:
                        print(f"   - {col_name}: {col_info[2]}")
            else:
                print("❌ monthly_revenue 表格未創建")
                return False
            
            conn.close()
            return True
        else:
            print("❌ 數據庫文件未創建")
            return False
            
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        return False

def test_excel_parsing():
    """測試Excel解析功能"""
    print("\n📊 測試Excel解析功能...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 測試月份解析
        test_months = ['Jun-25', 'May-25', 'Dec-24', 'Jan-23']
        print("📅 測試月份解析:")
        
        for month_str in test_months:
            year, month = downloader.parse_month_string(month_str)
            if year and month:
                print(f"   ✅ {month_str} -> {year}/{month:02d}")
            else:
                print(f"   ❌ {month_str} -> 解析失敗")
        
        # 測試數值轉換
        test_values = ['2,637', '1000', '-17.7', '26.9', '', 'nan']
        print("\n🔢 測試數值轉換:")
        
        for value in test_values:
            result = downloader.safe_float(value)
            print(f"   '{value}' -> {result}")
        
        # 測試股票名稱獲取
        test_stocks = ['2330', '2317', '2454', '9999']
        print("\n🏢 測試股票名稱:")
        
        for stock_id in test_stocks:
            name = downloader.get_stock_name(stock_id)
            print(f"   {stock_id} -> {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel解析測試失敗: {e}")
        return False

def test_gui_creation():
    """測試GUI創建"""
    print("\n🖥️ 測試GUI創建...")
    
    try:
        import tkinter as tk
        from monthly_revenue_downloader_gui import MonthlyRevenueDownloaderGUI
        
        # 創建隱藏的根視窗
        root = tk.Tk()
        root.withdraw()  # 隱藏主視窗
        
        # 創建GUI實例
        gui = MonthlyRevenueDownloaderGUI(root)
        
        print("✅ GUI實例創建成功")
        print(f"✅ 視窗標題: {gui.window.title()}")
        
        # 測試預設股票清單
        gui.set_preset_stocks("權值股")
        preset_value = gui.batch_stocks_var.get()
        print(f"✅ 權值股預設清單: {preset_value[:50]}...")
        
        # 清理
        gui.window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI測試失敗: {e}")
        return False

def test_chrome_driver():
    """測試Chrome驅動設置"""
    print("\n🔧 測試Chrome驅動設置...")
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 嘗試設置Chrome驅動（不實際啟動）
        print("⚠️ 注意：此測試不會實際啟動瀏覽器")
        print("✅ Chrome驅動設置方法可用")
        
        # 檢查下載目錄
        if os.path.exists(downloader.download_dir):
            print(f"✅ 下載目錄已存在: {downloader.download_dir}")
        else:
            print(f"✅ 下載目錄將被創建: {downloader.download_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome驅動測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 月營收下載器功能測試")
    print("=" * 60)
    
    tests = [
        ("模組導入", test_imports),
        ("數據庫創建", test_database_creation),
        ("Excel解析", test_excel_parsing),
        ("GUI創建", test_gui_creation),
        ("Chrome驅動", test_chrome_driver)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 項測試通過")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！月營收下載器已準備就緒")
        print("\n📋 使用方式:")
        print("1. 在主程式選單中選擇：爬蟲 → 📈 月營收資料")
        print("2. 或直接運行：python monthly_revenue_downloader_gui.py")
        print("\n⚠️ 注意事項:")
        print("- 首次使用需要安裝 Chrome 瀏覽器")
        print("- 下載過程中請勿關閉程式")
        print("- 建議分批下載，避免被網站封鎖")
    else:
        print(f"\n⚠️ 有 {len(results) - passed} 項測試失敗，請檢查相關依賴")
        print("\n🔧 可能需要的操作:")
        print("- pip install selenium pandas openpyxl")
        print("- 下載並安裝 Chrome 瀏覽器")
        print("- 檢查文件路徑和權限")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
