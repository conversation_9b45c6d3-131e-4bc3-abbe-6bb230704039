#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
專門測試歷史指數圖表
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt6.QtCore import Qt

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from twse_market_data_dialog import TWSEMarketDataDialog
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

class HistoricalChartTestWindow(QMainWindow):
    """歷史指數圖表專門測試窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📈 歷史指數圖表專門測試")
        self.setGeometry(100, 100, 900, 700)
        
        # 設置樣式
        self.setStyleSheet("""
            QMainWindow { background-color: #1a1a1a; color: white; }
            QPushButton { 
                background-color: #4CAF50; 
                color: white; 
                border: none; 
                padding: 20px; 
                font-size: 20px; 
                border-radius: 12px; 
                margin: 20px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #45a049; }
            QLabel { color: white; font-size: 16px; margin: 15px; }
        """)
        
        # 創建界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title = QLabel("📈 歷史指數圖表專門測試")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 32px; font-weight: bold; color: #4CAF50; margin: 30px;")
        layout.addWidget(title)
        
        # 說明
        info = QLabel("""
🎯 專門測試歷史指數資料的日期軸顯示

❗ 重要提醒：
• 只有「歷史指數資料」才有日期軸
• 「市場指數資訊」和「融資融券統計」都是當前快照，沒有歷史日期

📊 三種數據類型的區別：

1️⃣ 歷史指數資料 ✅
   • 有完整的歷史時間序列
   • 包含每日的收盤指數
   • 橫軸顯示日期 (MM/DD)
   • 適合看趨勢變化

2️⃣ 市場指數資訊 ❌
   • 當前市場指數快照
   • 沒有歷史日期數據
   • 橫軸顯示數據序號
   • 適合看當前狀態

3️⃣ 融資融券統計 ❌
   • 當前融資融券餘額
   • 沒有歷史日期數據
   • 橫軸顯示股票序號
   • 適合看當前分布

🧪 測試步驟：
1. 點擊下方按鈕開啟爬蟲
2. 切換到「圖表分析」分頁
3. ⚠️ 重要：選擇「歷史指數資料」
4. 點擊「生成圖表」
5. 觀察橫軸是否顯示日期

💡 如果還是沒有日期，可能是數據問題，我們會看到調試信息。
        """)
        info.setStyleSheet("""
            background-color: #2a2a2a; 
            padding: 30px; 
            border-radius: 15px; 
            border: 3px solid #4CAF50;
            line-height: 1.6;
            font-size: 16px;
        """)
        layout.addWidget(info)
        
        # 測試按鈕
        test_btn = QPushButton("📈 開啟爬蟲 - 專門測試歷史指數圖表")
        test_btn.clicked.connect(self.open_crawler)
        layout.addWidget(test_btn)
        
        # 提醒
        reminder = QLabel("⚠️ 記住：一定要選擇「歷史指數資料」才能看到日期軸！")
        reminder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        reminder.setStyleSheet("""
            color: #ff6b35; 
            font-weight: bold; 
            font-size: 22px; 
            background-color: #2a2a2a; 
            padding: 25px; 
            border-radius: 12px;
            border: 2px solid #ff6b35;
            margin: 20px;
        """)
        layout.addWidget(reminder)
    
    def open_crawler(self):
        """開啟爬蟲"""
        try:
            print("📈 開啟台股爬蟲 - 專門測試歷史指數圖表...")
            print("⚠️ 請記住選擇「歷史指數資料」！")
            dialog = TWSEMarketDataDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"❌ 錯誤: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    print("📈 歷史指數圖表專門測試")
    print("🎯 專門測試歷史指數資料的日期軸顯示")
    print("⚠️ 重要：只有「歷史指數資料」才有日期軸")
    print("📊 其他數據類型都是當前快照，沒有歷史日期")
    print("🧪 請在程式中選擇「歷史指數資料」進行測試")
    
    window = HistoricalChartTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
