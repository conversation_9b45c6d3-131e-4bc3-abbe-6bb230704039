#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試銅期貨添加
"""

import sys
import os

def test_copper_in_scanner():
    """測試安全掃描器中的銅期貨"""
    print("🔍 測試安全掃描器中的銅期貨...")
    
    try:
        # 檢查文件內容
        if os.path.exists('safe_market_scanner.py'):
            with open('safe_market_scanner.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '銅期貨' in content:
                print("✅ safe_market_scanner.py 包含銅期貨")
                
                # 檢查是否在 get_commodities 方法中
                if "'銅期貨':" in content:
                    print("✅ 銅期貨已添加到商品數據中")
                else:
                    print("❌ 銅期貨未正確添加到商品數據")
            else:
                print("❌ safe_market_scanner.py 不包含銅期貨")
        else:
            print("❌ safe_market_scanner.py 文件不存在")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_copper_in_real_fetcher():
    """測試真實數據獲取器中的銅期貨"""
    print("\n🔍 測試真實數據獲取器中的銅期貨...")
    
    try:
        if os.path.exists('real_market_data_fetcher.py'):
            with open('real_market_data_fetcher.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ('銅期貨', '銅期貨名稱'),
                ('HG=F', '銅期貨代碼'),
                ('get_commodities_real', '商品獲取方法')
            ]
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"✅ 包含 {description}: {keyword}")
                else:
                    print(f"❌ 缺少 {description}: {keyword}")
        else:
            print("❌ real_market_data_fetcher.py 文件不存在")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_copper_in_dialog():
    """測試對話框中的銅期貨"""
    print("\n🔍 測試對話框中的銅期貨...")
    
    try:
        if os.path.exists('market_data_crawler_dialog.py'):
            with open('market_data_crawler_dialog.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '銅期貨' in content:
                print("✅ market_data_crawler_dialog.py 包含銅期貨")
                
                # 檢查是否在說明文字中
                if '白銀, 銅期貨' in content:
                    print("✅ 銅期貨已添加到說明文字中")
                else:
                    print("❌ 銅期貨未正確添加到說明文字")
            else:
                print("❌ market_data_crawler_dialog.py 不包含銅期貨")
        else:
            print("❌ market_data_crawler_dialog.py 文件不存在")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def test_syntax():
    """測試語法正確性"""
    print("\n🔍 測試語法正確性...")
    
    try:
        import ast
        
        files_to_check = [
            'safe_market_scanner.py',
            'real_market_data_fetcher.py',
            'market_data_crawler_dialog.py'
        ]
        
        for filename in files_to_check:
            if os.path.exists(filename):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    ast.parse(content)
                    print(f"✅ {filename} 語法正確")
                except SyntaxError as e:
                    print(f"❌ {filename} 語法錯誤: {e}")
                except Exception as e:
                    print(f"⚠️ {filename} 檢查異常: {e}")
            else:
                print(f"❌ {filename} 不存在")
                
    except Exception as e:
        print(f"❌ 語法檢查失敗: {e}")

def simulate_scanner_test():
    """模擬掃描器測試"""
    print("\n🧪 模擬掃描器測試...")
    
    try:
        # 模擬商品數據結構
        commodity_data = {
            'WTI原油': {'price': 66.94, 'change_pct': 0.63},
            '黃金期貨': {'price': 3347.90, 'change_pct': 0.54},
            '白銀期貨': {'price': 38.17, 'change_pct': 0.89},
            '銅期貨': {'price': 4.25, 'change_pct': 1.45}
        }
        
        print("📊 模擬商品數據:")
        for name, data in commodity_data.items():
            price = data['price']
            change_pct = data['change_pct']
            change_str = f"📈 +{change_pct:.2f}%" if change_pct > 0 else f"📉 {change_pct:.2f}%"
            print(f"  • {name}: {price} {change_str}")
        
        if '銅期貨' in commodity_data:
            print("✅ 銅期貨成功包含在商品數據中")
            copper_data = commodity_data['銅期貨']
            print(f"   銅期貨價格: ${copper_data['price']}")
            print(f"   變化: {copper_data['change_pct']:+.2f}%")
        else:
            print("❌ 銅期貨未包含在商品數據中")
            
    except Exception as e:
        print(f"❌ 模擬測試失敗: {e}")

def main():
    """主函數"""
    print("🥉 銅期貨添加驗證")
    print("=" * 40)
    
    # 執行所有測試
    tests = [
        ("安全掃描器", test_copper_in_scanner),
        ("真實數據獲取器", test_copper_in_real_fetcher),
        ("對話框", test_copper_in_dialog),
        ("語法檢查", test_syntax),
        ("模擬測試", simulate_scanner_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            test_func()
            results.append((test_name, True))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 40)
    print("📋 銅期貨添加總結:")
    
    print("✅ 新增銅期貨到商品價格類別")
    print("✅ 使用 yfinance 代碼: HG=F")
    print("✅ 整合到真實數據獲取器")
    print("✅ 更新對話框說明文字")
    print("✅ 支持真實數據和模擬數據")
    
    print("\n🎯 銅期貨特點:")
    print("• 代碼: HG=F (COMEX銅期貨)")
    print("• 單位: 美元/磅")
    print("• 數據源: yfinance API")
    print("• 顯示: 價格 + 變化百分比")
    
    print("\n🚀 現在商品價格包含:")
    print("• WTI原油")
    print("• 黃金期貨") 
    print("• 白銀期貨")
    print("• 銅期貨 (新增)")

if __name__ == "__main__":
    main()
