#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查日期範圍記錄檔案
"""

import pickle
import os
import datetime

def check_date_range_file():
    """檢查日期範圍記錄檔案"""
    print("🔍 檢查日期範圍記錄檔案")
    print("=" * 60)
    
    date_range_file = 'history/date_range.pickle'
    
    if not os.path.exists(date_range_file):
        print(f"❌ 日期範圍檔案不存在: {date_range_file}")
        return None
    
    try:
        with open(date_range_file, 'rb') as f:
            dates = pickle.load(f)
        
        print(f"✅ 日期範圍檔案存在: {date_range_file}")
        print(f"📊 包含的表格: {list(dates.keys())}")
        
        for table_name, (start_date, end_date) in dates.items():
            print(f"\n📋 {table_name}:")
            print(f"   開始日期: {start_date}")
            print(f"   結束日期: {end_date}")
            
            if end_date:
                if isinstance(end_date, str):
                    try:
                        end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
                    except:
                        pass
                
                if hasattr(end_date, 'date'):
                    end_date = end_date.date()
                
                today = datetime.date.today()
                if isinstance(end_date, datetime.date):
                    days_behind = (today - end_date).days
                    print(f"   落後天數: {days_behind} 天")
        
        return dates
        
    except Exception as e:
        print(f"❌ 讀取日期範圍檔案失敗: {str(e)}")
        return None

def check_income_sheet_actual_range():
    """檢查 income_sheet.pkl 的實際日期範圍"""
    print(f"\n🔍 檢查 income_sheet.pkl 實際日期範圍")
    print("=" * 60)
    
    try:
        import pandas as pd
        df = pd.read_pickle('history/tables/income_sheet.pkl')
        
        dates = df.index.get_level_values('date')
        actual_start = dates.min()
        actual_end = dates.max()
        
        print(f"📊 實際日期範圍:")
        print(f"   開始日期: {actual_start}")
        print(f"   結束日期: {actual_end}")
        
        return actual_start, actual_end
        
    except Exception as e:
        print(f"❌ 檢查實際日期範圍失敗: {str(e)}")
        return None, None

def update_date_range_record():
    """更新日期範圍記錄"""
    print(f"\n🔧 更新日期範圍記錄")
    print("=" * 60)
    
    # 獲取實際日期範圍
    actual_start, actual_end = check_income_sheet_actual_range()
    
    if actual_start is None or actual_end is None:
        print("❌ 無法獲取實際日期範圍")
        return False
    
    try:
        date_range_file = 'history/date_range.pickle'
        
        # 讀取現有記錄
        if os.path.exists(date_range_file):
            with open(date_range_file, 'rb') as f:
                dates = pickle.load(f)
        else:
            dates = {}
        
        # 更新 income_sheet 的記錄
        dates['income_sheet'] = (actual_start, actual_end)
        
        # 保存更新後的記錄
        with open(date_range_file, 'wb') as f:
            pickle.dump(dates, f)
        
        print(f"✅ 日期範圍記錄已更新:")
        print(f"   income_sheet: {actual_start} 至 {actual_end}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新日期範圍記錄失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 日期範圍記錄檢查工具")
    print("=" * 60)
    print("🎯 目標: 檢查和修復日期範圍記錄問題")
    print("=" * 60)
    
    # 檢查現有記錄
    dates = check_date_range_file()
    
    # 檢查實際範圍
    actual_start, actual_end = check_income_sheet_actual_range()
    
    # 比較和修復
    if dates and 'income_sheet' in dates:
        recorded_start, recorded_end = dates['income_sheet']
        print(f"\n🔍 比較記錄與實際:")
        print(f"   記錄的結束日期: {recorded_end}")
        print(f"   實際的結束日期: {actual_end}")
        
        if str(recorded_end) != str(actual_end):
            print(f"   ⚠️ 記錄與實際不符，需要更新")
            if update_date_range_record():
                print(f"   ✅ 記錄已修復")
            else:
                print(f"   ❌ 記錄修復失敗")
        else:
            print(f"   ✅ 記錄與實際一致")
    else:
        print(f"\n⚠️ 沒有 income_sheet 的記錄，需要創建")
        if update_date_range_record():
            print(f"   ✅ 記錄已創建")
        else:
            print(f"   ❌ 記錄創建失敗")
    
    print(f"\n💡 修復完成後，auto_update.py 應該能正確識別需要更新的日期範圍")

if __name__ == "__main__":
    main()
