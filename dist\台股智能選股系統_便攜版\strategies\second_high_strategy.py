#!/usr/bin/env python3
"""
二次創高策略模組
從原始程式中提取的SecondHighStrategy類別
"""
import logging
import pandas as pd
import numpy as np
from .base_strategy import BaseStrategy


class SecondHighStrategy(BaseStrategy):
    """二次創高股票策略 - 捕捉二次創高的強勢股票"""

    def __init__(self):
        super().__init__(
            name="二次創高股票策略",
            description="捕捉二次創高的強勢股票，基於價格突破、成交量放大、技術指標等多重條件",
            strategy_type="technical_momentum"
        )

        # 策略參數
        self.lookback_days = 60        # 回看天數
        self.min_volume_ratio = 1.5    # 最小成交量比率
        self.min_price_increase = 0.02 # 最小價格漲幅2%
        self.rsi_threshold = 70        # RSI門檻
        self.ma_periods = [5, 10, 20]  # 移動平均線週期

    def find_recent_high(self, df, days=60):
        """找到最近N天的最高價"""
        try:
            if len(df) < days:
                return None, None

            recent_data = df.tail(days)
            max_idx = recent_data['High'].idxmax()
            max_price = recent_data['High'].max()

            return max_price, max_idx
        except Exception as e:
            logging.error(f"尋找最近高點失敗: {e}")
            return None, None

    def check_second_high_condition(self, df):
        """檢查二次創高條件"""
        try:
            if len(df) < self.lookback_days:
                return False, f"數據不足，需要至少{self.lookback_days}日數據"

            # 找到最近60天的最高價
            recent_high, high_date = self.find_recent_high(df, self.lookback_days)
            if recent_high is None:
                return False, "無法找到最近高點"

            current_price = df['Close'].iloc[-1]
            current_high = df['High'].iloc[-1]

            # 檢查是否接近或突破前高
            near_high_threshold = recent_high * 0.98  # 接近前高的門檻（98%）

            if current_high >= recent_high:
                return True, f"突破前高 {current_high:.2f} >= {recent_high:.2f}"
            elif current_price >= near_high_threshold:
                return True, f"接近前高 {current_price:.2f} >= {near_high_threshold:.2f} (98%前高)"
            else:
                return False, f"未達前高 {current_price:.2f} < {near_high_threshold:.2f}"

        except Exception as e:
            return False, f"二次創高檢查失敗: {str(e)}"

    def check_volume_surge(self, df):
        """檢查成交量放大"""
        try:
            if len(df) < 20:
                return False, "數據不足以檢查成交量"

            current_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume'].iloc[-20:-1].mean()  # 前19日平均成交量

            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            if volume_ratio >= self.min_volume_ratio:
                return True, f"成交量放大 {volume_ratio:.1f}倍 >= {self.min_volume_ratio}倍"
            else:
                return False, f"成交量未放大 {volume_ratio:.1f}倍 < {self.min_volume_ratio}倍"

        except Exception as e:
            return False, f"成交量檢查失敗: {str(e)}"

    def check_price_momentum(self, df):
        """檢查價格動能"""
        try:
            if len(df) < 2:
                return False, "數據不足以檢查價格動能"

            current_close = df['Close'].iloc[-1]
            prev_close = df['Close'].iloc[-2]

            # 避免除零錯誤
            if prev_close == 0:
                return False, "前一日收盤價為0，無法計算漲幅"

            price_change = (current_close - prev_close) / prev_close

            if price_change >= self.min_price_increase:
                return True, f"價格上漲 {price_change*100:.1f}% >= {self.min_price_increase*100:.1f}%"
            else:
                return False, f"價格漲幅不足 {price_change*100:.1f}% < {self.min_price_increase*100:.1f}%"

        except Exception as e:
            return False, f"價格動能檢查失敗: {str(e)}"

    def check_technical_indicators(self, df):
        """檢查技術指標"""
        try:
            if len(df) < max(self.ma_periods) + 14:
                return False, "數據不足以計算技術指標"

            close = df['Close']
            current_price = close.iloc[-1]

            # 檢查移動平均線排列
            ma_signals = []
            for period in self.ma_periods:
                ma = close.rolling(period).mean()
                current_ma = ma.iloc[-1]

                if current_price > current_ma:
                    ma_signals.append(f"MA{period}({current_ma:.2f}) ✓")
                else:
                    ma_signals.append(f"MA{period}({current_ma:.2f}) ✗")

            # 檢查RSI
            rsi = self.calculate_rsi(close)
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50

            rsi_signal = "強勢" if current_rsi > 50 else "弱勢"

            # 判斷技術面是否良好
            ma_above_count = sum(1 for signal in ma_signals if "✓" in signal)
            technical_good = ma_above_count >= 2 and current_rsi > 40

            reason = f"技術指標: {', '.join(ma_signals)}, RSI({current_rsi:.1f}){rsi_signal}"

            return technical_good, reason

        except Exception as e:
            return False, f"技術指標檢查失敗: {str(e)}"

    def analyze_stock(self, df, **kwargs):
        """分析單一股票是否符合二次創高策略"""
        try:
            if df.empty or len(df) < self.lookback_days:
                return {
                    'suitable': False,
                    'reason': f'數據不足，需要至少{self.lookback_days}日數據',
                    'details': {},
                    'score': 0,
                    'strategy_name': self.name
                }

            results = {}
            total_score = 0
            max_score = 4  # 四個主要條件

            # 1. 二次創高條件
            high_pass, high_reason = self.check_second_high_condition(df)
            results['二次創高'] = {'pass': high_pass, 'reason': high_reason}
            if high_pass:
                total_score += 1

            # 2. 成交量放大
            volume_pass, volume_reason = self.check_volume_surge(df)
            results['成交量放大'] = {'pass': volume_pass, 'reason': volume_reason}
            if volume_pass:
                total_score += 1

            # 3. 價格動能
            momentum_pass, momentum_reason = self.check_price_momentum(df)
            results['價格動能'] = {'pass': momentum_pass, 'reason': momentum_reason}
            if momentum_pass:
                total_score += 1

            # 4. 技術指標
            technical_pass, technical_reason = self.check_technical_indicators(df)
            results['技術指標'] = {'pass': technical_pass, 'reason': technical_reason}
            if technical_pass:
                total_score += 1

            # 判斷是否符合條件（核心條件：二次創高必須通過）
            suitable = high_pass and total_score >= 3

            # 生成總結
            passed_items = [k for k, v in results.items() if v['pass']]
            reason = f"二次創高評分: {total_score}/{max_score} (通過: {','.join(passed_items)})"

            if not high_pass:
                reason += " | ⚠️未達二次創高條件"

            return {
                'suitable': suitable,
                'reason': reason,
                'details': results,
                'score': total_score,
                'strategy_name': self.name
            }

        except Exception as e:
            logging.error(f"二次創高策略分析失敗: {e}")
            return {
                'suitable': False,
                'reason': f'分析失敗: {str(e)}',
                'details': {},
                'score': 0,
                'strategy_name': self.name
            }