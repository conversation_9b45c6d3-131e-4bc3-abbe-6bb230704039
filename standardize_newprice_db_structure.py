#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將 newprice.db 的結構調整為與 price.db 一致的格式
"""

import sqlite3
import os
import shutil

def standardize_newprice_db_structure():
    """將 newprice.db 調整為與 price.db 一致的結構"""
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    backup_file = 'D:/Finlab/history/tables/newprice_structure_backup.db'
    
    print("=" * 80)
    print("🔄 將 newprice.db 結構調整為與 price.db 一致")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(newprice_db):
        print(f"❌ 檔案不存在: {newprice_db}")
        return False
    
    # 檢查原始檔案大小
    original_size = os.path.getsize(newprice_db) / (1024 * 1024)  # MB
    print(f"📁 原始檔案: {newprice_db}")
    print(f"📊 原始檔案大小: {original_size:.2f} MB")
    
    try:
        # 創建備份
        print(f"\n💾 創建備份檔案...")
        shutil.copy2(newprice_db, backup_file)
        backup_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
        print(f"✅ 備份完成: {backup_file} ({backup_size:.2f} MB)")
        
        # 連接資料庫
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 檢查原始資料統計
        print(f"\n📊 原始資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        original_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {original_count:,}")
        
        # 檢查原始表格結構
        cursor.execute("PRAGMA table_info(price)")
        original_columns = cursor.fetchall()
        print(f"   原始欄位:")
        for col in original_columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 創建新的標準表格結構
        print(f"\n🔧 創建標準表格結構...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                stock_id TEXT,
                stock_name TEXT,
                listing_status TEXT,
                industry TEXT,
                Volume INTEGER,
                [Transaction] INTEGER,
                TradeValue INTEGER,
                [Open] REAL,
                High REAL,
                Low REAL,
                [Close] REAL,
                [Change] REAL,
                date TEXT
            )
        """)
        print(f"✅ 標準表格結構已創建")
        
        # 顯示欄位對應關係
        print(f"\n📋 欄位對應關係:")
        mapping = {
            'stock_id': 'stock_id',
            'stock_name': 'stock_name', 
            'listing_status': 'NULL',  # 新增欄位，暫時設為 NULL
            'industry': 'NULL',        # 新增欄位，暫時設為 NULL
            'Volume': '成交股數',
            '[Transaction]': '成交筆數',
            'TradeValue': '成交金額',
            '[Open]': '開盤價',
            'High': '最高價',
            'Low': '最低價',
            '[Close]': '收盤價',
            '[Change]': 'NULL',          # 新增欄位，暫時設為 NULL
            'date': 'date'
        }
        
        for new_col, old_col in mapping.items():
            print(f"   {new_col} ← {old_col}")
        
        # 分批轉換資料
        batch_size = 50000
        processed = 0
        
        print(f"\n⏳ 開始轉換資料，批次大小: {batch_size:,}")
        
        offset = 0
        while True:
            # 讀取一批資料
            cursor.execute(f"""
                SELECT stock_id, stock_name, date, 成交股數, 成交筆數, 成交金額, 
                       開盤價, 最高價, 最低價, 收盤價
                FROM price 
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            batch_data = cursor.fetchall()
            if not batch_data:
                break
            
            # 處理這批資料
            processed_batch = []
            for row in batch_data:
                stock_id, stock_name, date, volume, transaction, trade_value, open_price, high, low, close = row
                
                # 資料類型轉換
                try:
                    volume_int = int(float(volume)) if volume and volume != '' else 0
                except:
                    volume_int = 0
                
                try:
                    transaction_int = int(float(transaction)) if transaction and transaction != '' else 0
                except:
                    transaction_int = 0
                
                try:
                    trade_value_int = int(float(trade_value)) if trade_value and trade_value != '' else 0
                except:
                    trade_value_int = 0
                
                try:
                    open_real = float(open_price) if open_price and open_price != '' else 0.0
                except:
                    open_real = 0.0
                
                try:
                    high_real = float(high) if high and high != '' else 0.0
                except:
                    high_real = 0.0
                
                try:
                    low_real = float(low) if low and low != '' else 0.0
                except:
                    low_real = 0.0
                
                try:
                    close_real = float(close) if close and close != '' else 0.0
                except:
                    close_real = 0.0
                
                # 計算漲跌 (Change) - 這裡暫時設為 0，因為需要前一日收盤價
                change_real = 0.0
                
                # 構建新的行 (按照標準格式)
                new_row = (
                    stock_id,           # stock_id
                    stock_name,         # stock_name
                    None,               # listing_status (暫時為 NULL)
                    None,               # industry (暫時為 NULL)
                    volume_int,         # Volume
                    transaction_int,    # Transaction
                    trade_value_int,    # TradeValue
                    open_real,          # Open
                    high_real,          # High
                    low_real,           # Low
                    close_real,         # Close
                    change_real,        # Change
                    date                # date
                )
                processed_batch.append(new_row)
            
            # 插入處理後的資料
            cursor.executemany("""
                INSERT INTO stock_daily_data (
                    stock_id, stock_name, listing_status, industry,
                    Volume, [Transaction], TradeValue, [Open], High, Low, [Close], [Change], date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, processed_batch)
            
            processed += len(batch_data)
            progress = (processed / original_count) * 100
            print(f"   處理進度: {processed:,}/{original_count:,} ({progress:.1f}%)")
            
            offset += batch_size
        
        # 提交事務
        conn.commit()
        print(f"✅ 資料轉換完成，共處理 {processed:,} 筆")
        
        # 驗證新表格
        print(f"\n🔍 驗證新表格:")
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        new_count = cursor.fetchone()[0]
        print(f"   新表格資料筆數: {new_count:,}")
        
        # 檢查新表格結構
        cursor.execute("PRAGMA table_info(stock_daily_data)")
        new_columns = cursor.fetchall()
        print(f"   新表格欄位:")
        for col in new_columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 檢查轉換結果範例
        cursor.execute("SELECT * FROM stock_daily_data LIMIT 3")
        sample_new = cursor.fetchall()
        print(f"   轉換結果範例:")
        for i, row in enumerate(sample_new, 1):
            print(f"     第{i}筆: {row}")
        
        # 刪除舊表格
        print(f"\n🗑️ 刪除舊表格...")
        cursor.execute("DROP TABLE price")
        print(f"✅ 舊表格已刪除")
        
        # 創建索引以提升查詢效能
        print(f"\n🔧 創建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_id ON stock_daily_data(stock_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_date ON stock_daily_data(date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_date ON stock_daily_data(stock_id, date)")
        print(f"✅ 索引創建完成")
        
        # 關閉連接後重新開啟進行 VACUUM
        conn.close()
        
        # 優化資料庫
        print(f"\n🔧 優化資料庫...")
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 最終驗證
        print(f"\n📊 最終驗證:")
        cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
        final_count = cursor.fetchone()[0]
        print(f"   最終資料筆數: {final_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        final_unique = cursor.fetchone()[0]
        print(f"   最終不重複股票數: {final_unique:,}")
        
        # 檢查表格列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"   最終表格: {[table[0] for table in tables]}")
        
        # 檢查最終範例
        cursor.execute("SELECT stock_id, stock_name, Volume, [Open], High, Low, [Close], date FROM stock_daily_data LIMIT 5")
        final_sample = cursor.fetchall()
        print(f"   最終資料範例:")
        for i, row in enumerate(final_sample, 1):
            stock_id, stock_name, volume, open_p, high, low, close, date = row
            print(f"     第{i}筆: {stock_id} {stock_name} | {date} | 量:{volume} 開:{open_p} 高:{high} 低:{low} 收:{close}")
        
        conn.close()
        
        # 檢查最終檔案大小
        final_size = os.path.getsize(newprice_db) / (1024 * 1024)  # MB
        
        print(f"\n💾 檔案大小變化:")
        print(f"   原始大小: {original_size:.2f} MB")
        print(f"   最終大小: {final_size:.2f} MB")
        print(f"   大小變化: {final_size - original_size:.2f} MB")
        
        print(f"\n✅ newprice.db 結構標準化完成！")
        print(f"📁 主檔案: {newprice_db}")
        print(f"📁 備份檔案: {backup_file}")
        print(f"\n🎯 現在 newprice.db 與 price.db 具有相同的結構:")
        print(f"   - 表格名稱: stock_daily_data")
        print(f"   - 欄位名稱: 全部英文化")
        print(f"   - 資料類型: 與標準一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 標準化過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出錯，嘗試從備份恢復
        if os.path.exists(backup_file):
            print(f"\n🔄 嘗試從備份恢復...")
            try:
                shutil.copy2(backup_file, newprice_db)
                print(f"✅ 已從備份恢復")
            except Exception as restore_error:
                print(f"❌ 恢復失敗: {restore_error}")
        
        return False

if __name__ == "__main__":
    standardize_newprice_db_structure()
