#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試開啟除權息目錄功能
"""

import os
import platform
import subprocess

def test_folder_open():
    """測試開啟目錄功能"""
    print("🧪 測試開啟除權息目錄功能...")
    
    # 測試路徑
    dividend_folder = r"D:\Finlab\history\tables"
    
    print(f"📁 目標目錄: {dividend_folder}")
    print(f"💻 作業系統: {platform.system()}")
    
    # 檢查目錄是否存在
    if os.path.exists(dividend_folder):
        print("✅ 目錄存在")
        
        # 列出目錄內容
        try:
            files = os.listdir(dividend_folder)
            print(f"📋 目錄內容 ({len(files)} 個項目):")
            for file in files[:10]:  # 只顯示前10個
                print(f"  - {file}")
            if len(files) > 10:
                print(f"  ... 還有 {len(files) - 10} 個項目")
        except Exception as e:
            print(f"❌ 無法讀取目錄內容: {e}")
    else:
        print("❌ 目錄不存在")
        
        # 檢查替代路徑
        alt_folder = os.path.join(os.getcwd(), "history", "tables")
        print(f"🔍 檢查替代路徑: {alt_folder}")
        if os.path.exists(alt_folder):
            print("✅ 替代路徑存在")
            dividend_folder = alt_folder
        else:
            print("❌ 替代路徑也不存在")
            return False
    
    # 測試開啟目錄
    try:
        system = platform.system()
        dividend_folder = os.path.normpath(dividend_folder)
        
        print(f"\n🚀 嘗試開啟目錄: {dividend_folder}")
        
        if system == "Windows":
            print("🪟 使用 Windows explorer...")
            result = subprocess.run(['explorer', dividend_folder], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("✅ explorer 命令成功")
                return True
            else:
                print(f"⚠️ explorer 返回碼: {result.returncode}")
                print(f"stderr: {result.stderr}")
                
                # 嘗試使用 start 命令
                print("🔄 嘗試使用 start 命令...")
                result2 = subprocess.run(['cmd', '/c', 'start', '', dividend_folder], 
                                       shell=True, capture_output=True, text=True, timeout=5)
                
                if result2.returncode == 0:
                    print("✅ start 命令成功")
                    return True
                else:
                    print(f"❌ start 命令也失敗: {result2.returncode}")
                    print(f"stderr: {result2.stderr}")
                    
        elif system == "Darwin":  # macOS
            print("🍎 使用 macOS open...")
            result = subprocess.run(['open', dividend_folder], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ open 命令成功")
                return True
            else:
                print(f"❌ open 命令失敗: {result.returncode}")
                
        else:  # Linux
            print("🐧 使用 Linux xdg-open...")
            result = subprocess.run(['xdg-open', dividend_folder], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ xdg-open 命令成功")
                return True
            else:
                print(f"❌ xdg-open 命令失敗: {result.returncode}")
        
        return False
        
    except subprocess.TimeoutExpired:
        print("⏰ 命令執行超時")
        return False
    except Exception as e:
        print(f"❌ 開啟目錄失敗: {e}")
        return False

def test_alternative_methods():
    """測試替代開啟方法"""
    print("\n🔧 測試替代開啟方法...")
    
    dividend_folder = r"D:\Finlab\history\tables"
    
    if not os.path.exists(dividend_folder):
        print("❌ 目錄不存在，跳過測試")
        return
    
    try:
        # 方法1: 使用 os.startfile (Windows only)
        if platform.system() == "Windows":
            print("🪟 嘗試使用 os.startfile...")
            os.startfile(dividend_folder)
            print("✅ os.startfile 成功")
            return True
            
    except Exception as e:
        print(f"❌ os.startfile 失敗: {e}")
    
    try:
        # 方法2: 使用 webbrowser (跨平台)
        import webbrowser
        print("🌐 嘗試使用 webbrowser...")
        webbrowser.open(f"file://{dividend_folder}")
        print("✅ webbrowser 成功")
        return True
        
    except Exception as e:
        print(f"❌ webbrowser 失敗: {e}")
    
    return False

def main():
    """主測試函數"""
    print("🚀 除權息目錄開啟功能測試")
    print("=" * 50)
    
    # 基本測試
    basic_ok = test_folder_open()
    
    # 替代方法測試
    if not basic_ok:
        alt_ok = test_alternative_methods()
    else:
        alt_ok = True
    
    print("\n📊 測試結果摘要:")
    print("=" * 50)
    print(f"基本開啟方法: {'✅ 成功' if basic_ok else '❌ 失敗'}")
    print(f"替代開啟方法: {'✅ 成功' if alt_ok else '❌ 失敗'}")
    
    if basic_ok or alt_ok:
        print("\n🎉 至少有一種方法可以開啟目錄！")
    else:
        print("\n⚠️ 所有方法都失敗，請手動檢查目錄權限和路徑。")

if __name__ == "__main__":
    main()
