#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 season_range 函數生成的日期
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

from crawler import season_range

def test_season_range():
    """測試 season_range 函數"""
    print("🔍 測試 season_range 函數")
    print("=" * 60)
    
    # 從 2022-11-14 到現在
    start_date = datetime.date(2022, 11, 14)
    end_date = datetime.date.today()
    
    print(f"📅 測試範圍:")
    print(f"   開始日期: {start_date}")
    print(f"   結束日期: {end_date}")
    
    # 生成季度日期
    season_dates = season_range(start_date, end_date)
    
    print(f"\n📊 生成的季度日期:")
    print(f"   總數: {len(season_dates)} 個")
    
    for i, date in enumerate(season_dates, 1):
        print(f"   {i}. {date}")
    
    if len(season_dates) == 0:
        print(f"   ⚠️ 沒有生成任何日期！")
        print(f"   💡 這解釋了為什麼 auto_update.py 沒有更新任何資料")
    
    return season_dates

def test_extended_range():
    """測試更大的日期範圍"""
    print(f"\n🔍 測試更大的日期範圍")
    print("=" * 60)
    
    # 從 2022-01-01 到現在
    start_date = datetime.date(2022, 1, 1)
    end_date = datetime.date.today()
    
    print(f"📅 擴展測試範圍:")
    print(f"   開始日期: {start_date}")
    print(f"   結束日期: {end_date}")
    
    # 生成季度日期
    season_dates = season_range(start_date, end_date)
    
    print(f"\n📊 擴展範圍的季度日期:")
    print(f"   總數: {len(season_dates)} 個")
    
    for i, date in enumerate(season_dates, 1):
        print(f"   {i}. {date}")
    
    return season_dates

def analyze_season_range_logic():
    """分析 season_range 函數的邏輯"""
    print(f"\n🔍 分析 season_range 函數邏輯")
    print("=" * 60)
    
    # 手動計算應該生成的日期
    start_date = datetime.date(2022, 11, 14)
    end_date = datetime.date.today()
    
    print(f"📅 分析範圍: {start_date} 到 {end_date}")
    
    # 根據 season_range 的邏輯
    expected_dates = []
    for year in range(start_date.year-1, end_date.year+1):
        candidates = [
            datetime.date(year, 5, 15),      # Q1 財報
            datetime.date(year, 8, 14),      # Q2 財報
            datetime.date(year, 11, 14),     # Q3 財報
            datetime.date(year+1, 3, 31)    # Q4 財報
        ]
        
        for candidate in candidates:
            if start_date < candidate < end_date:
                expected_dates.append(candidate)
    
    print(f"\n📊 預期應該生成的日期:")
    print(f"   總數: {len(expected_dates)} 個")
    
    for i, date in enumerate(expected_dates, 1):
        print(f"   {i}. {date}")
    
    return expected_dates

def main():
    """主函數"""
    print("🔧 Season Range 測試工具")
    print("=" * 60)
    print("🎯 目標: 理解為什麼 income_sheet 沒有更新")
    print("=" * 60)
    
    # 測試1: 實際的 season_range
    actual_dates = test_season_range()
    
    # 測試2: 擴展範圍
    extended_dates = test_extended_range()
    
    # 測試3: 分析邏輯
    expected_dates = analyze_season_range_logic()
    
    print(f"\n📊 總結:")
    print(f"   實際生成: {len(actual_dates)} 個日期")
    print(f"   擴展範圍: {len(extended_dates)} 個日期")
    print(f"   預期生成: {len(expected_dates)} 個日期")
    
    if len(actual_dates) == 0:
        print(f"\n💡 結論:")
        print(f"   ✅ 找到問題根源: season_range 沒有生成任何日期")
        print(f"   ⚠️ 原因: 從 2022-11-14 到現在，沒有符合條件的季度日期")
        print(f"   🔧 解決方案: 需要手動指定更新範圍或修改邏輯")
    else:
        print(f"\n💡 結論:")
        print(f"   ✅ season_range 生成了 {len(actual_dates)} 個日期")
        print(f"   🤔 需要檢查為什麼這些日期沒有被處理")

if __name__ == "__main__":
    main()
