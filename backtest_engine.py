#!/usr/bin/env python3
"""
專業級回測引擎
Professional Backtesting Engine

支援多策略回測、績效分析、風險指標計算
"""

import pandas as pd
import numpy as np
import sqlite3
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    """交易記錄"""
    symbol: str
    entry_date: datetime
    exit_date: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    quantity: int
    trade_type: str  # 'BUY' or 'SELL'
    strategy_signal: str
    pnl: Optional[float] = None
    pnl_pct: Optional[float] = None
    holding_days: Optional[int] = None
    exit_reason: Optional[str] = None

@dataclass
class PerformanceMetrics:
    """績效指標"""
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    avg_holding_days: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float

class BacktestEngine:
    """回測引擎"""
    
    def __init__(self, initial_capital: float = 1000000, commission: float = 0.001425):
        self.initial_capital = initial_capital
        self.commission = commission  # 手續費率
        self.tax_rate = 0.003  # 證交稅（賣出時）
        self.reset()
        
    def reset(self):
        """重置回測狀態"""
        self.current_capital = self.initial_capital
        self.positions = {}  # {symbol: {'quantity': int, 'avg_price': float, 'entry_date': datetime}}
        self.trades = []
        self.daily_portfolio_value = []
        self.equity_curve = pd.Series(dtype=float)
        
    def calculate_position_size(self, price: float, risk_pct: float = 0.02) -> int:
        """計算建議倉位大小"""
        risk_amount = self.current_capital * risk_pct
        max_position_value = self.current_capital * 0.1  # 單一持股不超過10%
        
        position_value = min(risk_amount / 0.05, max_position_value)  # 假設5%風險
        quantity = int(position_value / price / 1000) * 1000  # 以張為單位
        
        return max(quantity, 1000)  # 最小1張
    
    def buy_stock(self, symbol: str, price: float, date: datetime, 
                  quantity: Optional[int] = None, strategy_signal: str = ""):
        """買入股票"""
        if quantity is None:
            quantity = self.calculate_position_size(price)
        
        total_cost = price * quantity
        commission_cost = total_cost * self.commission
        total_cost_with_fee = total_cost + commission_cost
        
        if total_cost_with_fee > self.current_capital:
            # 資金不足，調整數量
            available_capital = self.current_capital * 0.95  # 保留5%現金
            quantity = int(available_capital / (price * (1 + self.commission)) / 1000) * 1000
            
            if quantity < 1000:  # 少於1張則不買入
                return False
            
            total_cost = price * quantity
            commission_cost = total_cost * self.commission
            total_cost_with_fee = total_cost + commission_cost
        
        # 更新持倉
        if symbol in self.positions:
            # 加倉
            old_qty = self.positions[symbol]['quantity']
            old_price = self.positions[symbol]['avg_price']
            new_avg_price = (old_qty * old_price + quantity * price) / (old_qty + quantity)
            
            self.positions[symbol]['quantity'] += quantity
            self.positions[symbol]['avg_price'] = new_avg_price
        else:
            # 新建倉位
            self.positions[symbol] = {
                'quantity': quantity,
                'avg_price': price,
                'entry_date': date
            }
        
        # 更新資金
        self.current_capital -= total_cost_with_fee
        
        # 記錄交易
        trade = Trade(
            symbol=symbol,
            entry_date=date,
            exit_date=None,
            entry_price=price,
            exit_price=None,
            quantity=quantity,
            trade_type='BUY',
            strategy_signal=strategy_signal
        )
        self.trades.append(trade)
        
        return True
    
    def sell_stock(self, symbol: str, price: float, date: datetime, 
                   quantity: Optional[int] = None, reason: str = ""):
        """賣出股票"""
        if symbol not in self.positions:
            return False
        
        current_position = self.positions[symbol]
        
        if quantity is None:
            quantity = current_position['quantity']  # 全部賣出
        
        quantity = min(quantity, current_position['quantity'])
        
        if quantity <= 0:
            return False
        
        # 計算收入
        total_income = price * quantity
        commission_cost = total_income * self.commission
        tax_cost = total_income * self.tax_rate
        net_income = total_income - commission_cost - tax_cost
        
        # 計算損益
        entry_price = current_position['avg_price']
        pnl = (price - entry_price) * quantity - commission_cost - tax_cost
        pnl_pct = (price - entry_price) / entry_price
        
        # 更新持倉
        self.positions[symbol]['quantity'] -= quantity
        if self.positions[symbol]['quantity'] <= 0:
            entry_date = self.positions[symbol]['entry_date']
            del self.positions[symbol]
        else:
            entry_date = current_position['entry_date']
        
        # 更新資金
        self.current_capital += net_income
        
        # 記錄交易
        holding_days = (date - entry_date).days
        trade = Trade(
            symbol=symbol,
            entry_date=entry_date,
            exit_date=date,
            entry_price=entry_price,
            exit_price=price,
            quantity=quantity,
            trade_type='SELL',
            strategy_signal=reason,
            pnl=pnl,
            pnl_pct=pnl_pct,
            holding_days=holding_days,
            exit_reason=reason
        )
        self.trades.append(trade)
        
        return True
    
    def get_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """計算當前投資組合價值"""
        cash = self.current_capital
        stock_value = 0
        
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                stock_value += position['quantity'] * current_prices[symbol]
        
        return cash + stock_value
    
    def update_equity_curve(self, date: datetime, current_prices: Dict[str, float]):
        """更新淨值曲線"""
        portfolio_value = self.get_portfolio_value(current_prices)
        self.daily_portfolio_value.append({
            'date': date,
            'portfolio_value': portfolio_value,
            'cash': self.current_capital,
            'stock_value': portfolio_value - self.current_capital
        })

class StrategyBacktest:
    """策略回測器"""
    
    def __init__(self, db_path: str, strategy_config: Dict):
        self.db_path = db_path
        self.strategy_config = strategy_config
        self.strategy_name = strategy_config.get('name', 'Unknown')
        
    def load_stock_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """載入股票數據"""
        stock_data = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            for symbol in symbols:
                query = """
                    SELECT date, Open, High, Low, Close, Volume, stock_id
                    FROM stock_daily_data 
                    WHERE stock_id = ? AND date BETWEEN ? AND ?
                    ORDER BY date
                """
                
                df = pd.read_sql_query(query, conn, params=(symbol, start_date, end_date))
                
                if not df.empty:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    df = df.astype(float, errors='ignore')
                    stock_data[symbol] = df
                
            conn.close()
            
        except Exception as e:
            logging.error(f"載入數據失敗: {e}")
            
        return stock_data
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        # 移動平均線
        for period in [5, 10, 20, 50, 60, 240]:
            if len(df) >= period:
                df[f'MA{period}'] = df['Close'].rolling(window=period).mean()
        
        # RSI
        if len(df) >= 14:
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0).rolling(window=14).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
        
        # 布林通道
        if len(df) >= 20:
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        
        # MACD
        if len(df) >= 26:
            exp1 = df['Close'].ewm(span=12).mean()
            exp2 = df['Close'].ewm(span=26).mean()
            df['MACD'] = exp1 - exp2
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # 成交量指標
        for period in [10, 20]:
            if len(df) >= period:
                df[f'Volume_MA{period}'] = df['Volume'].rolling(window=period).mean()
        
        # ATR
        if len(df) >= 14:
            high_low = df['High'] - df['Low']
            high_close = (df['High'] - df['Close'].shift()).abs()
            low_close = (df['Low'] - df['Close'].shift()).abs()
            df['TR'] = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            df['ATR'] = df['TR'].rolling(window=14).mean()
        
        return df
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """根據策略配置生成信號"""
        df['Buy_Signal'] = False
        df['Sell_Signal'] = False
        df['Signal_Strength'] = 0.0
        
        if len(df) < 50:  # 數據不足
            return df
        
        # 根據策略類型生成信號
        strategy_name = self.strategy_name.lower()
        
        if "智能多空" in self.strategy_name:
            df = self._generate_intelligent_signals(df)
        elif "動態停損" in self.strategy_name:
            df = self._generate_dynamic_stop_signals(df)
        elif "突破回踩" in self.strategy_name:
            df = self._generate_breakout_signals(df)
        elif "智能波段" in self.strategy_name:
            df = self._generate_swing_signals(df)
        else:
            df = self._generate_default_signals(df)
        
        return df
    
    def _generate_intelligent_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """智能多空策略信號"""
        # 買入條件
        ma_condition = (df['MA10'] > df['MA20']) & (df['MA20'] > df['MA50'])
        rsi_condition = (df['RSI'] > 30) & (df['RSI'] < 70)
        bb_condition = df['Close'] > df['BB_Lower']
        volume_condition = df['Volume'] > df['Volume_MA10'] * 1.2
        
        buy_signal = ma_condition & rsi_condition & bb_condition & volume_condition
        
        # 賣出條件
        ma_sell = (df['MA10'] < df['MA20']) | (df['MA20'] < df['MA50'])
        rsi_sell = df['RSI'] > 75
        bb_sell = df['Close'] > df['BB_Upper']
        
        sell_signal = ma_sell | rsi_sell | bb_sell
        
        df['Buy_Signal'] = buy_signal
        df['Sell_Signal'] = sell_signal
        
        return df
    
    def _generate_dynamic_stop_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """動態停損策略信號"""
        # 趨勢確認
        trend_up = (df['MA20'] > df['MA50']) & (df['Close'] > df['MA20'])
        momentum_ok = (df['RSI'] > 45) & (df['RSI'] < 80)
        volume_ok = df['Volume'] > df['Volume_MA10'] * 1.2
        
        df['Buy_Signal'] = trend_up & momentum_ok & volume_ok
        
        # 動態停損賣出
        df['Stop_Loss'] = df['Close'] - (df['ATR'] * 2)
        df['Sell_Signal'] = (df['Close'] < df['Stop_Loss'].shift(1)) | (df['RSI'] > 80)
        
        return df
    
    def _generate_breakout_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """突破回踩策略信號"""
        # 突破檢測
        df['Resistance'] = df['High'].rolling(window=20).max()
        breakout = df['Close'] > df['Resistance'].shift(1) * 1.02
        volume_surge = df['Volume'] > df['Volume_MA10'] * 1.5
        
        # 回踩檢測
        df['Support'] = df['Low'].rolling(window=20).min()
        pullback = (df['Close'] < df['Resistance'] * 0.97) & (df['Close'] > df['Support'])
        
        df['Buy_Signal'] = (breakout & volume_surge) | pullback
        df['Sell_Signal'] = df['Close'] < df['Support'] * 0.98
        
        return df
    
    def _generate_swing_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """智能波段策略信號"""
        # 波段買入：趨勢 + 超賣反彈
        trend_up = df['MA20'] > df['MA50']
        oversold_bounce = (df['RSI'] < 40) & (df['RSI'].shift(1) < df['RSI'])
        bb_bounce = df['Close'] > df['BB_Lower']
        
        df['Buy_Signal'] = trend_up & oversold_bounce & bb_bounce
        
        # 波段賣出：獲利了結 + 趨勢轉弱
        overbought = df['RSI'] > 70
        trend_weak = df['MA20'] < df['MA50']
        
        df['Sell_Signal'] = overbought | trend_weak
        
        return df
    
    def _generate_default_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """預設策略信號（原勝率73%策略）"""
        # MA240 趨勢向上
        ma240_up = df['MA240'] > df['MA240'].shift(1)
        ma240_trend = df['MA240'] > df['MA240'].shift(30)
        
        # MA60 或 MA20 向上
        ma_up = (df['MA60'] > df['MA60'].shift(1)) | (df['MA20'] > df['MA20'].shift(1))
        
        # 成交量黃金交叉
        volume_cross = (df['Volume_MA10'] > df['Volume_MA20']) & \
                      (df['Volume_MA10'].shift(1) <= df['Volume_MA20'].shift(1))
        
        # RSI條件
        rsi_condition = (df['RSI'] > 50) & (df['RSI'] < 80)
        
        df['Buy_Signal'] = ma240_up & ma240_trend & ma_up & rsi_condition
        df['Sell_Signal'] = (df['RSI'] > 80) | (df['MA20'] < df['MA50'])
        
        return df
    
    def run_backtest(self, symbols: List[str], start_date: str, end_date: str, 
                    initial_capital: float = 1000000) -> Tuple[BacktestEngine, pd.DataFrame]:
        """執行回測"""
        print(f"🚀 開始回測策略: {self.strategy_name}")
        print(f"📅 回測期間: {start_date} 至 {end_date}")
        print(f"💰 初始資金: {initial_capital:,}")
        print(f"📈 股票數量: {len(symbols)}")
        
        # 初始化回測引擎
        engine = BacktestEngine(initial_capital)
        
        # 載入數據
        print("📊 載入股票數據...")
        stock_data = self.load_stock_data(symbols, start_date, end_date)
        
        if not stock_data:
            print("❌ 無法載入股票數據")
            return engine, pd.DataFrame()
        
        print(f"✅ 成功載入 {len(stock_data)} 支股票數據")
        
        # 計算技術指標
        print("🔧 計算技術指標...")
        for symbol in stock_data:
            stock_data[symbol] = self.calculate_technical_indicators(stock_data[symbol])
            stock_data[symbol] = self.generate_signals(stock_data[symbol])
        
        # 獲取所有交易日期
        all_dates = set()
        for df in stock_data.values():
            all_dates.update(df.index)
        all_dates = sorted(all_dates)
        
        print(f"📅 回測交易日數: {len(all_dates)}")
        
        # 逐日回測
        print("⚡ 開始逐日回測...")
        for i, date in enumerate(all_dates):
            if i % 50 == 0:
                progress = (i / len(all_dates)) * 100
                print(f"回測進度: {progress:.1f}% ({date.strftime('%Y-%m-%d')})")
            
            current_prices = {}
            
            # 處理每支股票的信號
            for symbol, df in stock_data.items():
                if date not in df.index:
                    continue
                
                row = df.loc[date]
                current_prices[symbol] = row['Close']
                
                # 檢查買入信號
                if row['Buy_Signal'] and symbol not in engine.positions:
                    engine.buy_stock(symbol, row['Close'], date, strategy_signal="買入信號")
                
                # 檢查賣出信號
                elif row['Sell_Signal'] and symbol in engine.positions:
                    engine.sell_stock(symbol, row['Close'], date, reason="賣出信號")
            
            # 更新投資組合價值
            engine.update_equity_curve(date, current_prices)
        
        # 強制平倉（回測結束）
        final_date = all_dates[-1]
        for symbol in list(engine.positions.keys()):
            if symbol in current_prices:
                engine.sell_stock(symbol, current_prices[symbol], final_date, reason="回測結束")
        
        # 生成結果DataFrame
        results_df = pd.DataFrame(engine.daily_portfolio_value)
        if not results_df.empty:
            results_df.set_index('date', inplace=True)
        
        print("✅ 回測完成！")
        return engine, results_df
    
    def calculate_performance_metrics(self, engine: BacktestEngine, results_df: pd.DataFrame) -> PerformanceMetrics:
        """計算績效指標"""
        if results_df.empty or len(engine.trades) == 0:
            return PerformanceMetrics(
                total_return=0, annual_return=0, sharpe_ratio=0, max_drawdown=0,
                win_rate=0, profit_factor=0, total_trades=0, winning_trades=0,
                losing_trades=0, avg_win=0, avg_loss=0, avg_holding_days=0,
                volatility=0, calmar_ratio=0, sortino_ratio=0
            )
        
        # 基本收益指標
        initial_value = engine.initial_capital
        final_value = results_df['portfolio_value'].iloc[-1]
        total_return = (final_value - initial_value) / initial_value
        
        # 年化收益率
        days = (results_df.index[-1] - results_df.index[0]).days
        years = days / 365.25
        annual_return = (final_value / initial_value) ** (1/years) - 1 if years > 0 else 0
        
        # 日收益率
        daily_returns = results_df['portfolio_value'].pct_change().dropna()
        
        # 波動率（年化）
        volatility = daily_returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.01  # 假設無風險利率1%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 最大回撤
        rolling_max = results_df['portfolio_value'].expanding().max()
        drawdown = (results_df['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 索提諾比率（下行波動率）
        negative_returns = daily_returns[daily_returns < 0]
        downside_volatility = negative_returns.std() * np.sqrt(252)
        sortino_ratio = (annual_return - risk_free_rate) / downside_volatility if downside_volatility > 0 else 0
        
        # 卡爾馬比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 交易統計
        completed_trades = [t for t in engine.trades if t.exit_date is not None]
        winning_trades = len([t for t in completed_trades if t.pnl > 0])
        losing_trades = len([t for t in completed_trades if t.pnl <= 0])
        total_trades = len(completed_trades)
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 平均盈虧
        wins = [t.pnl for t in completed_trades if t.pnl > 0]
        losses = [t.pnl for t in completed_trades if t.pnl <= 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # 獲利因子
        total_wins = sum(wins) if wins else 0
        total_losses = abs(sum(losses)) if losses else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        # 平均持有天數
        holding_days = [t.holding_days for t in completed_trades if t.holding_days is not None]
        avg_holding_days = np.mean(holding_days) if holding_days else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annual_return=annual_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            avg_holding_days=avg_holding_days,
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio
        )

def load_top_stocks(db_path: str, limit: int = 50) -> List[str]:
    """載入市值前N大股票"""
    try:
        conn = sqlite3.connect(db_path)
        query = """
            SELECT DISTINCT stock_id 
            FROM stock_daily_data 
            WHERE date >= date('now', '-1 year')
            AND Volume > 1000000
            ORDER BY Volume DESC 
            LIMIT ?
        """
        cursor = conn.cursor()
        cursor.execute(query, (limit,))
        stocks = [row[0] for row in cursor.fetchall()]
        conn.close()
        return stocks
    except Exception as e:
        logging.error(f"載入股票列表失敗: {e}")
        return []

def quick_backtest(strategy_name: str, db_path: str, months: int = 12) -> Dict:
    """快速回測（最近N個月）"""
    # 計算日期範圍
    end_date = datetime.now()
    start_date = end_date - timedelta(days=months*30)
    
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    # 載入策略配置
    strategy_file = f"strategies/{strategy_name}.json"
    if not os.path.exists(strategy_file):
        return {"error": f"策略文件不存在: {strategy_file}"}
    
    with open(strategy_file, 'r', encoding='utf-8') as f:
        strategy_config = json.load(f)
    
    # 載入股票列表
    stocks = load_top_stocks(db_path, 30)
    if not stocks:
        return {"error": "無法載入股票數據"}
    
    # 執行回測
    backtest = StrategyBacktest(db_path, strategy_config)
    engine, results_df = backtest.run_backtest(stocks, start_str, end_str)
    
    # 計算績效
    metrics = backtest.calculate_performance_metrics(engine, results_df)
    
    return {
        "strategy_name": strategy_name,
        "period": f"{start_str} 至 {end_str}",
        "metrics": metrics,
        "trades": len(engine.trades),
        "final_value": results_df['portfolio_value'].iloc[-1] if not results_df.empty else 0
    }

if __name__ == "__main__":
    # 測試回測功能
    print("🧪 回測引擎測試")
    
    # 測試快速回測
    db_path = "db/price.db"  # 請修改為實際路徑
    if os.path.exists(db_path):
        result = quick_backtest("勝率73%", db_path, 6)
        print(f"📊 回測結果: {result}")
    else:
        print("❌ 找不到數據庫文件")
    
    print("✅ 回測引擎測試完成") 