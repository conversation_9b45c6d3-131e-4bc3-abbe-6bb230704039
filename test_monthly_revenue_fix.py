#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試月營收排行榜功能修復
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, date
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_database_connection():
    """測試資料庫連接"""
    print("=" * 50)
    print("測試資料庫連接")
    print("=" * 50)
    
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 資料庫不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_report';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ monthly_report 表不存在")
            conn.close()
            return False
        
        # 檢查資料筆數
        cursor.execute("SELECT COUNT(*) FROM monthly_report;")
        total_count = cursor.fetchone()[0]
        print(f"✅ 資料庫連接成功，總記錄數: {total_count}")
        
        # 檢查最新日期
        cursor.execute("SELECT DISTINCT date FROM monthly_report ORDER BY date DESC LIMIT 5;")
        latest_dates = cursor.fetchall()
        print(f"✅ 最新日期: {[d[0] for d in latest_dates]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        return False

def test_monthly_data_fetch():
    """測試月營收資料獲取"""
    print("\n" + "=" * 50)
    print("測試月營收資料獲取")
    print("=" * 50)
    
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 測試 2025-07 月份資料
        test_date = '2025-07'
        query = """
            SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收
            FROM monthly_report
            WHERE date LIKE ?
            ORDER BY stock_id
            LIMIT 10
        """
        
        df = pd.read_sql_query(query, conn, params=(f"{test_date}%",))
        
        if df.empty:
            print(f"❌ 沒有找到 {test_date} 的資料")
            conn.close()
            return False
        
        print(f"✅ 成功獲取 {test_date} 月份資料，樣本:")
        print(df.to_string())
        
        # 檢查該月份總筆數
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM monthly_report WHERE date LIKE ?", (f"{test_date}%",))
        count = cursor.fetchone()[0]
        print(f"✅ {test_date} 月份總記錄數: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料獲取失敗: {e}")
        return False

def test_revenue_growth_calculation():
    """測試營收成長率計算"""
    print("\n" + "=" * 50)
    print("測試營收成長率計算")
    print("=" * 50)
    
    db_path = 'D:/Finlab/history/tables/monthly_report.db'
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 獲取測試資料
        query = """
            SELECT stock_id, stock_name, date, 當月營收, 上月營收, 去年當月營收
            FROM monthly_report
            WHERE date LIKE '2025-07%'
            ORDER BY stock_id
            LIMIT 5
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 沒有測試資料")
            return False
        
        # 轉換數值欄位
        df['當月營收'] = pd.to_numeric(df['當月營收'], errors='coerce')
        df['上月營收'] = pd.to_numeric(df['上月營收'], errors='coerce')
        df['去年當月營收'] = pd.to_numeric(df['去年當月營收'], errors='coerce')
        
        # 計算成長率
        df['YoY%'] = ((df['當月營收'] - df['去年當月營收']) / df['去年當月營收'] * 100).round(2)
        df['MoM%'] = ((df['當月營收'] - df['上月營收']) / df['上月營收'] * 100).round(2)
        
        print("✅ 成長率計算結果:")
        print(df[['stock_id', 'stock_name', '當月營收', 'YoY%', 'MoM%']].to_string())
        
        return True
        
    except Exception as e:
        print(f"❌ 成長率計算失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 月營收排行榜功能修復測試")
    print("=" * 60)
    
    # 執行測試
    tests = [
        ("資料庫連接測試", test_database_connection),
        ("月營收資料獲取測試", test_monthly_data_fetch),
        ("營收成長率計算測試", test_revenue_growth_calculation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 執行失敗: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("測試結果總結")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！月營收功能修復成功！")
    else:
        print("⚠️  部分測試失敗，需要進一步檢查")

if __name__ == "__main__":
    main()
