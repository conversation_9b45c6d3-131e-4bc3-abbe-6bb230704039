#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新的 MOPS 網站
"""

import requests
import urllib3
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_new_mops_main():
    """測試新的 MOPS 主頁"""
    print("🔍 測試新的 MOPS 網站")
    print("=" * 60)
    
    new_mops_url = "https://mopsov.twse.com.tw/mops/web/index"
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(new_mops_url, headers=headers, verify=False, timeout=30)
        
        print(f"🌐 新 MOPS 網站: {new_mops_url}")
        print(f"   HTTP 狀態: {response.status_code}")
        print(f"   回應長度: {len(response.content)} bytes")
        print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            print(f"   ✅ 新網站可以訪問")
            
            # 檢查是否包含財報相關內容
            content = response.text.lower()
            keywords = ['財報', '財務報表', 'financial', 'ifrs', '損益表', 'income']
            
            found_keywords = []
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"   💡 找到財報相關關鍵字: {found_keywords}")
            else:
                print(f"   ⚠️ 未找到明顯的財報相關內容")
            
            return True
        else:
            print(f"   ❌ 新網站無法訪問")
            return False
            
    except Exception as e:
        print(f"   ❌ 測試失敗: {str(e)}")
        return False

def test_selenium_new_mops():
    """使用 Selenium 測試新 MOPS 網站"""
    print(f"\n🔍 使用 Selenium 測試新 MOPS 網站")
    print("=" * 60)
    
    try:
        # 設置 Chrome 選項
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 反爬蟲設置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 隨機 User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # 創建 WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        
        # 執行反檢測腳本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        new_mops_url = "https://mopsov.twse.com.tw/mops/web/index"
        
        print(f"🌐 Selenium 訪問: {new_mops_url}")
        
        # 訪問頁面
        driver.get(new_mops_url)
        
        # 等待頁面載入
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # 額外等待 JavaScript 執行
        time.sleep(3)
        
        page_title = driver.title
        page_source = driver.page_source
        
        print(f"   頁面標題: {page_title}")
        print(f"   頁面長度: {len(page_source)} 字元")
        
        # 尋找財報相關連結
        try:
            # 尋找可能的財報連結
            links = driver.find_elements(By.TAG_NAME, "a")
            financial_links = []
            
            for link in links:
                try:
                    text = link.text.lower()
                    href = link.get_attribute('href')
                    
                    if any(keyword in text for keyword in ['財報', '財務', 'financial', 'ifrs', '損益', 'income']):
                        financial_links.append((text, href))
                except:
                    continue
            
            if financial_links:
                print(f"   💡 找到 {len(financial_links)} 個財報相關連結:")
                for i, (text, href) in enumerate(financial_links[:5], 1):
                    print(f"     {i}. {text}: {href}")
                if len(financial_links) > 5:
                    print(f"     ... 還有 {len(financial_links)-5} 個連結")
            else:
                print(f"   ⚠️ 未找到明顯的財報連結")
            
            # 保存頁面內容用於分析
            with open('new_mops_page.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            print(f"   💾 頁面內容已保存: new_mops_page.html")
            
        except Exception as e:
            print(f"   ⚠️ 分析頁面內容時出錯: {str(e)}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ Selenium 測試失敗: {str(e)}")
        return False

def search_financial_download_api():
    """搜尋可能的財報下載 API"""
    print(f"\n🔍 搜尋可能的財報下載 API")
    print("=" * 60)
    
    # 可能的新 API 端點
    possible_apis = [
        "https://mopsov.twse.com.tw/server-java/FileDownLoad",
        "https://mopsov.twse.com.tw/mops/web/ajax_download",
        "https://mopsov.twse.com.tw/api/download",
        "https://mopsov.twse.com.tw/download",
        "https://mopsov.twse.com.tw/mops/web/t164sb01",  # 可能的財報查詢頁面
        "https://mopsov.twse.com.tw/mops/web/t164sb02",
    ]
    
    for api_url in possible_apis:
        try:
            print(f"🌐 測試: {api_url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(api_url, headers=headers, verify=False, timeout=10)
            
            print(f"   狀態: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 可訪問")
                if 'json' in response.headers.get('content-type', ''):
                    print(f"   💡 返回 JSON 格式")
                elif 'html' in response.headers.get('content-type', ''):
                    print(f"   📄 返回 HTML 頁面")
            elif response.status_code == 404:
                print(f"   ❌ 不存在")
            else:
                print(f"   ⚠️ 其他狀態")
                
        except Exception as e:
            print(f"   ❌ 錯誤: {str(e)[:30]}...")

def main():
    """主函數"""
    print("🔧 新 MOPS 網站測試工具")
    print("=" * 60)
    print("🎯 目標: 探索新 MOPS 網站的財報資料獲取方式")
    print("=" * 60)
    
    # 測試1: 基本 HTTP 請求
    basic_success = test_new_mops_main()
    
    # 測試2: Selenium 瀏覽器
    selenium_success = test_selenium_new_mops()
    
    # 測試3: 搜尋可能的 API
    search_financial_download_api()
    
    print(f"\n📊 測試總結:")
    if basic_success:
        print(f"✅ 新 MOPS 網站可以訪問")
    else:
        print(f"❌ 新 MOPS 網站無法訪問")
    
    if selenium_success:
        print(f"✅ Selenium 可以載入新網站")
    else:
        print(f"❌ Selenium 無法載入新網站")
    
    print(f"\n💡 下一步:")
    print(f"   1. 檢查保存的 new_mops_page.html 了解網站結構")
    print(f"   2. 尋找財報下載的具體入口")
    print(f"   3. 分析新的 API 格式")

if __name__ == "__main__":
    main()
