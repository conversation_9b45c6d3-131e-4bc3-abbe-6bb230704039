#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
詳細偵測市場掃描過程
檢查實際數據獲取情況，不只看日誌
"""

import sys
import time
import logging
import json
from datetime import datetime

# 設置詳細日誌
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_pre_market_monitor():
    """詳細偵測開盤前監控器"""
    print("🔍 詳細偵測開盤前監控器")
    print("=" * 80)
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        print("📊 監控器基本資訊:")
        print(f"   • 名稱: {monitor.name}")
        print(f"   • 版本: {monitor.version}")
        print(f"   • 數據緩存: {len(monitor.data_cache)} 項")
        print()
        
        # 逐步測試各個數據獲取方法
        print("🔍 逐步測試各個數據獲取方法:")
        print("-" * 60)
        
        # 1. 測試美股指數
        print("\n1️⃣ 測試美股指數獲取:")
        try:
            us_data = monitor.get_us_indices()
            print(f"   返回類型: {type(us_data)}")
            print(f"   數據長度: {len(us_data) if us_data else 0}")
            
            if us_data:
                print("   實際數據內容:")
                for name, data in us_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到美股數據")
        except Exception as e:
            print(f"   ❌ 美股數據獲取異常: {e}")
        
        # 2. 測試亞洲指數
        print("\n2️⃣ 測試亞洲指數獲取:")
        try:
            asia_data = monitor.get_asia_indices()
            print(f"   返回類型: {type(asia_data)}")
            print(f"   數據長度: {len(asia_data) if asia_data else 0}")
            
            if asia_data:
                print("   實際數據內容:")
                for name, data in asia_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到亞洲指數數據")
        except Exception as e:
            print(f"   ❌ 亞洲指數獲取異常: {e}")
        
        # 3. 測試商品價格
        print("\n3️⃣ 測試商品價格獲取:")
        try:
            commodity_data = monitor.get_commodity_prices()
            print(f"   返回類型: {type(commodity_data)}")
            print(f"   數據長度: {len(commodity_data) if commodity_data else 0}")
            
            if commodity_data:
                print("   實際數據內容:")
                for name, data in commodity_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到商品價格數據")
        except Exception as e:
            print(f"   ❌ 商品價格獲取異常: {e}")
        
        # 4. 測試外匯匯率
        print("\n4️⃣ 測試外匯匯率獲取:")
        try:
            fx_data = monitor.get_fx_rates()
            print(f"   返回類型: {type(fx_data)}")
            print(f"   數據長度: {len(fx_data) if fx_data else 0}")
            
            if fx_data:
                print("   實際數據內容:")
                for name, data in fx_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到外匯數據")
        except Exception as e:
            print(f"   ❌ 外匯數據獲取異常: {e}")
        
        # 5. 測試台灣期貨
        print("\n5️⃣ 測試台灣期貨獲取:")
        try:
            tw_data = monitor.get_taiwan_futures()
            print(f"   返回類型: {type(tw_data)}")
            print(f"   數據長度: {len(tw_data) if tw_data else 0}")
            
            if tw_data:
                print("   實際數據內容:")
                for name, data in tw_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到台灣期貨數據")
        except Exception as e:
            print(f"   ❌ 台灣期貨獲取異常: {e}")
        
        # 6. 測試加密貨幣
        print("\n6️⃣ 測試加密貨幣獲取:")
        try:
            crypto_data = monitor.get_crypto_prices()
            print(f"   返回類型: {type(crypto_data)}")
            print(f"   數據長度: {len(crypto_data) if crypto_data else 0}")
            
            if crypto_data:
                print("   實際數據內容:")
                for name, data in crypto_data.items():
                    print(f"     • {name}: {json.dumps(data, indent=8, ensure_ascii=False)}")
            else:
                print("   ❌ 沒有獲取到加密貨幣數據")
        except Exception as e:
            print(f"   ❌ 加密貨幣獲取異常: {e}")
        
        # 7. 測試完整掃描
        print("\n7️⃣ 測試完整掃描:")
        try:
            print("🚀 執行完整掃描...")
            start_time = time.time()
            
            scan_results = monitor.run_full_scan()
            
            scan_time = time.time() - start_time
            print(f"   掃描耗時: {scan_time:.2f}秒")
            print(f"   返回類型: {type(scan_results)}")
            
            if scan_results:
                print("   掃描結果結構:")
                for key, value in scan_results.items():
                    if isinstance(value, dict):
                        print(f"     • {key}: {len(value)} 項")
                        # 檢查每項是否有實際數據
                        real_data_count = 0
                        for item_name, item_data in value.items():
                            if isinstance(item_data, dict) and item_data.get('price', 0) > 0:
                                real_data_count += 1
                        print(f"       └─ 有效數據: {real_data_count} 項")
                    elif isinstance(value, list):
                        print(f"     • {key}: {len(value)} 項")
                    else:
                        print(f"     • {key}: {value}")
                
                # 詳細檢查數據品質
                print("\n   數據品質詳細檢查:")
                total_items = 0
                valid_items = 0
                
                for category, data in scan_results.items():
                    if category in ['timestamp']:
                        continue
                    if isinstance(data, dict):
                        for item_name, item_data in data.items():
                            total_items += 1
                            if isinstance(item_data, dict):
                                price = item_data.get('price', item_data.get('rate', 0))
                                if price > 0:
                                    valid_items += 1
                                    print(f"     ✅ {category}.{item_name}: {price}")
                                else:
                                    print(f"     ❌ {category}.{item_name}: 無有效價格數據")
                
                print(f"\n   總結: {valid_items}/{total_items} 項有有效數據")
                
                return True, scan_results, valid_items, total_items
            else:
                print("   ❌ 完整掃描返回空結果")
                return False, None, 0, 0
                
        except Exception as e:
            print(f"   ❌ 完整掃描異常: {e}")
            return False, None, 0, 0
            
    except Exception as e:
        print(f"❌ 監控器初始化失敗: {e}")
        return False, None, 0, 0

def main():
    """主偵測函數"""
    print("🔍 市場掃描詳細偵測")
    print("=" * 90)
    print(f"📅 偵測時間: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 執行詳細偵測
    success, results, valid_items, total_items = debug_pre_market_monitor()
    
    # 最終分析
    print(f"\n🎯 偵測結果分析")
    print("=" * 90)
    
    if success and results:
        print("✅ 掃描器可以執行")
        print(f"📊 數據統計:")
        print(f"   • 總項目: {total_items}")
        print(f"   • 有效數據: {valid_items}")
        print(f"   • 有效率: {(valid_items/total_items*100):.1f}%" if total_items > 0 else "   • 有效率: 0%")
        
        if valid_items == 0:
            print("\n❌ 問題診斷:")
            print("   • 掃描器可以運行，但沒有獲取到有效的價格數據")
            print("   • 可能是API連接問題")
            print("   • 可能是數據格式問題")
            print("   • 可能是網路連接問題")
            
            print("\n🔧 建議解決方案:")
            print("   1. 檢查網路連接")
            print("   2. 檢查API密鑰配置")
            print("   3. 檢查數據源可用性")
            print("   4. 檢查防火牆設置")
        else:
            print(f"\n✅ 成功獲取 {valid_items} 項有效數據")
            print("   市場掃描功能正常")
    else:
        print("❌ 掃描器無法正常執行")
        print("\n🔧 需要檢查:")
        print("   1. 模組導入問題")
        print("   2. 依賴套件安裝")
        print("   3. 程式碼錯誤")
        print("   4. 系統環境問題")
    
    print("=" * 90)
    print("🔍 詳細偵測完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 偵測已停止")
    except Exception as e:
        print(f"\n❌ 偵測失敗: {e}")
    
    print(f"\n👋 偵測結束")
