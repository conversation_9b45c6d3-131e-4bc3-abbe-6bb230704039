#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試權證過濾功能
"""

import sys
import os
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_warrant_filtering():
    """測試權證過濾功能"""
    
    print("=" * 60)
    print("🧪 測試權證過濾功能")
    print("=" * 60)
    
    try:
        from crawler import crawl_price
        
        # 測試爬取一天的資料
        test_date = datetime(2022, 8, 30)  # 使用一個新的日期
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        print("🔄 開始爬取資料...")
        df = crawl_price(test_date)
        
        if df is not None and not df.empty:
            print(f"✅ 爬取成功！資料筆數: {len(df)}")
            
            # 檢查是否有權證
            df_reset = df.reset_index()
            
            # 檢查股票代碼分布
            print(f"\n📊 股票代碼分布:")
            
            # 按第一個字符分類
            code_types = {}
            warrant_count = 0
            
            for _, row in df_reset.iterrows():
                stock_id = str(row['stock_id'])
                first_char = stock_id[0] if stock_id else 'Unknown'
                code_types[first_char] = code_types.get(first_char, 0) + 1
                
                # 檢查是否有權證
                if stock_id.startswith('7'):
                    warrant_count += 1
            
            for char, count in sorted(code_types.items()):
                print(f"   {char}開頭: {count} 檔")
            
            # 權證檢查結果
            print(f"\n🔍 權證檢查結果:")
            if warrant_count == 0:
                print(f"   ✅ 沒有權證資料 - 過濾功能正常工作")
            else:
                print(f"   ❌ 發現 {warrant_count} 檔權證 - 過濾功能有問題")
                
                # 顯示權證範例
                print(f"   權證範例:")
                warrant_examples = [row['stock_id'] for _, row in df_reset.iterrows() 
                                  if str(row['stock_id']).startswith('7')][:10]
                for warrant in warrant_examples:
                    print(f"     {warrant}")
            
            # 顯示範例資料
            print(f"\n📋 範例資料 (前10筆):")
            sample_data = df_reset[['stock_id', 'stock_name', 'listing_status', 'industry', '收盤價']].head(10)
            for _, row in sample_data.iterrows():
                print(f"   {row['stock_id']} {row['stock_name']} | {row['listing_status']} | {row['industry']} | 收盤:{row['收盤價']}")
            
            # 檢查股票族群分布
            print(f"\n📊 股票族群分布:")
            
            groups = {
                'ETF (00xx)': 0,
                'ETF (其他)': 0,
                '傳統產業 (1xxx)': 0,
                '電子股 (2xxx)': 0,
                '電子股 (3xxx)': 0,
                '傳統產業 (4xxx)': 0,
                '電子股 (5xxx)': 0,
                '電子股 (6xxx)': 0,
                '權證 (7xxx)': 0,
                '電子股 (8xxx)': 0,
                '其他 (9xxx)': 0
            }
            
            for _, row in df_reset.iterrows():
                stock_id = str(row['stock_id'])
                if stock_id.startswith('00') and len(stock_id) == 4:
                    groups['ETF (00xx)'] += 1
                elif stock_id.startswith('0'):
                    groups['ETF (其他)'] += 1
                elif stock_id.startswith('1'):
                    groups['傳統產業 (1xxx)'] += 1
                elif stock_id.startswith('2'):
                    groups['電子股 (2xxx)'] += 1
                elif stock_id.startswith('3'):
                    groups['電子股 (3xxx)'] += 1
                elif stock_id.startswith('4'):
                    groups['傳統產業 (4xxx)'] += 1
                elif stock_id.startswith('5'):
                    groups['電子股 (5xxx)'] += 1
                elif stock_id.startswith('6'):
                    groups['電子股 (6xxx)'] += 1
                elif stock_id.startswith('7'):
                    groups['權證 (7xxx)'] += 1
                elif stock_id.startswith('8'):
                    groups['電子股 (8xxx)'] += 1
                elif stock_id.startswith('9'):
                    groups['其他 (9xxx)'] += 1
            
            for group_name, count in groups.items():
                if count > 0:
                    status = "❌ 應該被過濾" if group_name == '權證 (7xxx)' else "✅"
                    print(f"   {group_name}: {count} 檔 {status}")
            
        else:
            print("❌ 爬取失敗或無資料")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_warrant_filtering()
