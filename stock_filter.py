#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台股股票代碼篩選器
=================

功能說明：
- 篩選4碼、5碼及前綴為00的6碼ETF股票
- 排除權證、ETN、TDR等衍生性商品
- 提供完整的ETF清單

使用方式：
```python
from stock_filter import StockFilter

filter = StockFilter()
valid_stocks = filter.filter_stock_list(['2330', '00631L', '030001', '2317'])
# 結果: ['2330', '00631L', '2317']
```
"""

import re
import logging
from typing import List, Dict, Any


class StockFilter:
    """台股股票代碼篩選器"""

    def __init__(self):
        """初始化股票篩選器"""
        self.logger = logging.getLogger(__name__)

        # 性能優化：預編譯正則表達式
        self._compiled_patterns = None
        self._etf_set = None
        self._delisted_set = None
        self._rules_cache = None

        # 初始化快取
        self._init_cache()

    def _init_cache(self):
        """初始化快取以提升性能"""
        try:
            rules = self.get_filter_rules()

            # 預編譯正則表達式
            self._compiled_patterns = {
                'include': [re.compile(rule['pattern']) for rule in rules['include_patterns']],
                'exclude': [re.compile(rule['pattern']) for rule in rules['exclude_patterns']]
            }

            # 轉換為集合以提升查找速度
            self._etf_set = set(rules['etf_whitelist'])
            self._delisted_set = set(rules['delisted_blacklist'])

            # 快取規則
            self._rules_cache = rules

            self.logger.debug("股票篩選器快取初始化完成")

        except Exception as e:
            self.logger.error(f"初始化快取失敗: {e}")
            # 設置空快取以避免錯誤
            self._compiled_patterns = {'include': [], 'exclude': []}
            self._etf_set = set()
            self._delisted_set = set()

    def get_filter_rules(self) -> Dict[str, Any]:
        """
        獲取股票篩選規則
        
        Returns:
            dict: 包含篩選規則的字典
        """
        return {
            'include_patterns': [
                {
                    'type': '4碼股票',
                    'pattern': r'^\d{4}$',
                    'description': '一般上市櫃股票',
                    'examples': ['2330', '2317', '2454', '3008', '2412']
                },
                {
                    'type': '5碼股票', 
                    'pattern': r'^\d{5}$',
                    'description': '5碼股票代碼',
                    'examples': ['12345', '67890']
                },
                {
                    'type': '6碼ETF',
                    'pattern': r'^00\d{4}[A-Z]?$',
                    'description': '前綴為00的6碼ETF（含字母後綴）',
                    'examples': ['006208', '00631L', '00632R', '00878', '00881']
                }
            ],
            'exclude_patterns': [
                {
                    'pattern': r'^03\d{4}[A-Z]*$',
                    'description': '前綴為03的權證'
                },
                {
                    'pattern': r'^02\d{4}[A-Z]*$',
                    'description': '前綴為02的ETN'
                },
                {
                    'pattern': r'^91\d{4}$',
                    'description': '前綴為91的TDR'
                },
                {
                    'pattern': r'^\d{4}[A-Z]$',
                    'description': '4碼數字+1個英文字母的權證或衍生性商品（如2881C、2891C）'
                },
                {
                    'pattern': r'^[1-9]\d{5}[A-Z]+$',
                    'description': '非00開頭的6碼+英文字母權證'
                },
                {
                    'pattern': r'^\d{5}[A-Z]{2,}$',
                    'description': '5碼+多個英文字母的權證'
                }
            ],
            'etf_whitelist': self.get_etf_list(),
            'delisted_blacklist': self.get_delisted_stocks()
        }
    
    def get_etf_list(self) -> List[str]:
        """
        獲取完整的ETF清單（前兩碼為00的6碼ETF）
        
        Returns:
            list: ETF代碼清單
        """
        return [
            # 基本ETF
            '00625K', '00631L', '00632R', '00633L', '00634R', '00635U',
            '00636K', '00637L', '00638R', '00640L', '00641R', '00642U',
            '00643K', '00647L', '00648R', '00650L', '00651R', '00653L',
            '00654R', '00655L', '00656R', '00657K',
            
            # 高股息ETF
            '00878', '00881', '00885', '00891', '00892', '00900',
            '00915', '00919', '00922', '00923', '00924', '00926',
            '00927', '00929', '00930', '00932', '00934', '00935',
            '00936', '00938', '00939', '00940', '00941', '00943',
            '00944', '00946', '00947', '00949', '00951', '00952',
            '00954', '00956', '00960', '00961', '00962', '00963',
            '00964', '00965', '00971', '00972',
            
            # 新增ETF（2025年）
            '009800', '009801', '009802', '009803', '009804', '009805',
            '009808', '00980A', '009810', '00981A', '00982A', '00983A',
            '00984A'
        ]

    def get_delisted_stocks(self) -> List[str]:
        """
        獲取已下市或不活躍股票清單

        Returns:
            list: 已下市股票代碼清單
        """
        return [
            # 已下市ETF
            '0054',  # 元大台商50 (已下市)

            # 已下市或長期停牌的股票
            '1258',  # 其祥-KY (已下市)
            '1333',  # 恩得利 (已下市)
            '1507',  # 永大 (已下市)
            '1592',  # 英瑞-KY (已下市)
            '1701',  # 中化 (已下市)
            '1724',  # 台硝 (長期停牌/不活躍)
            '5820',  # 日盛金 (可能已下市或不活躍)

            # 其他已知的已下市股票
            '1259',  # 安心 (已下市)
            '1264',  # 德麥 (已下市)
            '1336',  # 台翰 (已下市)
            '1565',  # 精華 (已下市)
            '1587',  # 吉茂 (已下市)
            '1595',  # 川寶 (已下市)
            '1707',  # 葡萄王 (已下市)
            '1711',  # 永光 (已下市)
        ]

    def is_valid_stock_code(self, stock_code: str) -> bool:
        """
        檢查股票代碼是否符合篩選規則（優化版本）

        Args:
            stock_code: 股票代碼

        Returns:
            bool: 是否為有效的股票代碼
        """
        if not stock_code or not isinstance(stock_code, str):
            return False

        stock_code = stock_code.strip().upper()

        # 使用快取的集合進行快速查找
        # 檢查已下市股票黑名單
        if stock_code in self._delisted_set:
            return False

        # 檢查排除規則（使用預編譯的正則表達式）
        for pattern in self._compiled_patterns['exclude']:
            if pattern.match(stock_code):
                return False

        # 檢查ETF白名單（使用集合快速查找）
        if stock_code in self._etf_set:
            return True

        # 檢查包含規則（使用預編譯的正則表達式）
        for pattern in self._compiled_patterns['include']:
            if pattern.match(stock_code):
                return True

        return False
    
    def filter_stock_list(self, stock_list: List[str]) -> List[str]:
        """
        篩選股票清單，只保留符合規則的股票（批量優化版本）

        Args:
            stock_list: 原始股票代碼清單

        Returns:
            list: 篩選後的股票代碼清單
        """
        if not stock_list:
            return []

        # 批量處理：預處理股票代碼
        processed_codes = [code.strip().upper() for code in stock_list if code and isinstance(code, str)]

        # 使用列表推導式和集合操作進行批量篩選
        filtered_list = []

        for stock_code in processed_codes:
            # 快速排除：已下市股票
            if stock_code in self._delisted_set:
                continue

            # 快速排除：排除規則
            excluded = False
            for pattern in self._compiled_patterns['exclude']:
                if pattern.match(stock_code):
                    excluded = True
                    break

            if excluded:
                continue

            # 快速包含：ETF白名單
            if stock_code in self._etf_set:
                filtered_list.append(stock_code)
                continue

            # 包含規則檢查
            for pattern in self._compiled_patterns['include']:
                if pattern.match(stock_code):
                    filtered_list.append(stock_code)
                    break

        # 只在DEBUG模式下輸出詳細日誌
        if self.logger.isEnabledFor(logging.DEBUG):
            excluded_count = len(processed_codes) - len(filtered_list)
            self.logger.debug(
                f"📊 股票篩選完成：原始 {len(stock_list)} 支，"
                f"保留 {len(filtered_list)} 支，排除 {excluded_count} 支"
            )

        return filtered_list
    
    def get_filter_summary(self) -> Dict[str, Any]:
        """
        獲取篩選規則摘要
        
        Returns:
            dict: 篩選規則摘要
        """
        rules = self.get_filter_rules()
        
        return {
            'description': '台股股票代碼篩選器',
            'include_rules': [
                f"{rule['type']}: {rule['description']}" 
                for rule in rules['include_patterns']
            ],
            'exclude_rules': [
                rule['description'] 
                for rule in rules['exclude_patterns']
            ],
            'etf_count': len(rules['etf_whitelist']),
            'examples': {
                'valid': ['2330', '2317', '00631L', '00878', '00881'],
                'invalid': ['030001', '020011', '912000', '2330A', '2881C', '2891C', '00631LA']
            }
        }


def main():
    """測試股票篩選器功能"""
    import logging
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建篩選器
    filter = StockFilter()
    
    # 測試股票清單
    test_stocks = [
        '2330',    # 台積電 - 有效
        '2317',    # 鴻海 - 有效
        '00631L',  # 元大台灣50正2 - 有效
        '00878',   # 國泰永續高股息 - 有效
        '030001',  # 權證 - 無效
        '020011',  # ETN - 無效
        '912000',  # TDR - 無效
        '2330A',   # 權證 - 無效
        '2881C',   # 4碼+1英文字母權證 - 無效
        '2891C',   # 4碼+1英文字母權證 - 無效
        '00631LA', # 權證 - 無效
    ]
    
    print("🔍 股票篩選器測試")
    print("=" * 50)
    
    # 顯示篩選規則摘要
    summary = filter.get_filter_summary()
    print(f"📋 {summary['description']}")
    print(f"📊 ETF清單數量: {summary['etf_count']}")
    print()
    
    # 執行篩選
    valid_stocks = filter.filter_stock_list(test_stocks)
    
    print("📈 篩選結果:")
    print(f"有效股票: {valid_stocks}")
    print()
    
    # 個別測試
    print("🔍 個別股票檢查:")
    for stock in test_stocks:
        is_valid = filter.is_valid_stock_code(stock)
        status = "✅ 有效" if is_valid else "❌ 無效"
        print(f"  {stock}: {status}")


if __name__ == "__main__":
    main()
