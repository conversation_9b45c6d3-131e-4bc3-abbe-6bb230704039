#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化版v2月營收爬蟲
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import datetime
import pandas as pd
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 從 auto_update.py 導入簡化版v2函數
from auto_update import crawl_monthly_report_simple_v2

def test_simple_v2_monthly_crawler():
    """測試簡化版v2月營收爬蟲"""
    print("🔧 測試簡化版v2月營收爬蟲")
    print("=" * 60)
    print("💡 基於您提供的最新程式改寫")
    print("💡 更簡潔的實現 + 完整錯誤處理")
    print("=" * 60)
    
    # 測試較舊的月份 (確定已發布)
    test_date = datetime.datetime(2024, 12, 10)  # 2024年11月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年11月營收)")
    
    try:
        print("🔄 開始測試簡化版v2爬蟲...")
        result = crawl_monthly_report_simple_v2(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ 簡化版v2爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 檢查資料格式是否符合 monthly_report.pkl
            print(f"\n🔍 格式檢查:")
            
            # 檢查索引
            if result.index.names == ['stock_id', 'date']:
                print(f"   ✅ 索引格式正確: {result.index.names}")
            else:
                print(f"   ❌ 索引格式錯誤: {result.index.names}")
            
            # 檢查必要欄位
            required_columns = ['當月營收']
            missing_columns = [col for col in required_columns if col not in result.columns]
            
            if not missing_columns:
                print(f"   ✅ 必要欄位完整")
            else:
                print(f"   ⚠️ 缺少欄位: {missing_columns}")
            
            # 檢查股票代碼格式
            stock_ids = result.index.get_level_values('stock_id')
            sample_ids = stock_ids[:3].tolist()
            print(f"   股票代碼樣本: {sample_ids}")
            
            # 檢查一些知名股票
            tsmc_candidates = [sid for sid in stock_ids if '2330' in str(sid)]
            if tsmc_candidates:
                tsmc_id = tsmc_candidates[0]
                tsmc_data = result.loc[tsmc_id]
                print(f"   找到台積電: {tsmc_id}")
                if '當月營收' in result.columns:
                    revenue = tsmc_data['當月營收'].iloc[0] if hasattr(tsmc_data['當月營收'], 'iloc') else tsmc_data['當月營收']
                    print(f"   台積電營收: {revenue:,}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('monthly_simple_v2_test.pkl')
            print(f"\n💾 測試資料已保存: monthly_simple_v2_test.pkl")
            
            return True
        else:
            print(f"\n⚠️ 簡化版v2爬蟲無資料")
            return False
            
    except Exception as e:
        print(f"\n❌ 簡化版v2爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def compare_with_existing():
    """與現有 monthly_report.pkl 比較格式"""
    print(f"\n🔍 與現有 monthly_report.pkl 格式比較")
    print("=" * 60)
    
    try:
        existing_file = 'history/tables/monthly_report.pkl'
        test_file = 'monthly_simple_v2_test.pkl'
        
        if os.path.exists(existing_file) and os.path.exists(test_file):
            existing_data = pd.read_pickle(existing_file)
            test_data = pd.read_pickle(test_file)
            
            print(f"📊 格式比較:")
            print(f"   現有資料索引: {existing_data.index.names}")
            print(f"   測試資料索引: {test_data.index.names}")
            
            print(f"   現有資料欄位: {list(existing_data.columns)}")
            print(f"   測試資料欄位: {list(test_data.columns)}")
            
            # 檢查格式相容性
            if existing_data.index.names == test_data.index.names:
                print(f"   ✅ 索引格式相容")
            else:
                print(f"   ❌ 索引格式不相容")
            
            common_columns = set(existing_data.columns).intersection(set(test_data.columns))
            print(f"   共同欄位: {len(common_columns)} 個 - {list(common_columns)}")
            
            if len(common_columns) >= 1:
                print(f"   ✅ 欄位基本相容")
                print(f"   💡 可以直接更新現有檔案")
            else:
                print(f"   ❌ 欄位差異太大")
                
        else:
            print(f"❌ 無法比較 (檔案不存在)")
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")

def test_url_accessibility():
    """測試 URL 可訪問性"""
    print(f"\n🔍 測試簡化版v2 URL 可訪問性")
    print("=" * 60)
    
    import requests
    
    # 測試參數
    year = 2024
    month = 11
    
    test_url = f'https://mops.twse.com.tw/nas/t21/sii/t21sc03_{year-1911}_{month}.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"🧪 測試 URL: {test_url}")
    
    try:
        response = requests.get(test_url, headers=headers, timeout=15, verify=False)
        print(f"   狀態碼: {response.status_code}")
        print(f"   回應長度: {len(response.text)} 字元")
        
        if response.status_code == 200:
            if '您的網頁IP已經被證交所封鎖' in response.text:
                print(f"   ⚠️ IP被封鎖")
            elif 'THE PAGE CANNOT BE ACCESSED' in response.text:
                print(f"   ❌ 頁面無法訪問")
            elif len(response.text) > 1000:
                print(f"   ✅ 可能有資料")
                # 檢查是否有表格
                if '<table' in response.text:
                    print(f"   ✅ 包含表格資料")
                else:
                    print(f"   ⚠️ 無表格資料")
            else:
                print(f"   ⚠️ 回應太短")
        else:
            print(f"   ❌ HTTP錯誤")
            
    except Exception as e:
        print(f"   ❌ 連接失敗: {str(e)[:50]}...")

def main():
    """主函數"""
    print("🔧 簡化版v2月營收爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 測試基於您最新程式的簡化版v2爬蟲")
    print("💡 特色: 更簡潔實現 + 完整錯誤處理 + 符合 monthly_report.pkl 格式")
    print("=" * 60)
    
    # 測試1: URL 可訪問性
    test_url_accessibility()
    
    # 測試2: 完整爬蟲
    success = test_simple_v2_monthly_crawler()
    
    # 測試3: 格式比較
    if success:
        compare_with_existing()
    
    if success:
        print(f"\n🎉 簡化版v2月營收爬蟲測試成功！")
        print(f"💡 現在可以在 auto_update.py 中使用:")
        print(f"   ('monthly_report', crawl_monthly_report, month_range),")
        print(f"🔧 系統會自動使用簡化版v2爬蟲進行直接更新")
    else:
        print(f"\n⚠️ 簡化版v2仍有問題，可能需要進一步調整")

if __name__ == "__main__":
    import os
    main()
