#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試真實數據獲取
"""

import yfinance as yf
from datetime import datetime

print("🔍 測試真實數據獲取")
print("=" * 40)

# 測試美元指數
print("📊 測試美元指數...")
try:
    ticker = yf.Ticker("DX-Y.NYB")
    hist = ticker.history(period="2d")
    
    if not hist.empty:
        current_price = hist['Close'].iloc[-1]
        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
        change_pct = ((current_price - prev_price) / prev_price) * 100
        
        print(f"✅ 美元指數: {current_price:.2f} ({change_pct:+.2f}%)")
        print(f"   真實數據，時間: {hist.index[-1]}")
    else:
        print("❌ 美元指數數據為空")
except Exception as e:
    print(f"❌ 美元指數獲取失敗: {e}")

# 測試USD/TWD
print("\n📊 測試USD/TWD...")
try:
    ticker = yf.Ticker("TWD=X")
    hist = ticker.history(period="2d")
    
    if not hist.empty:
        current_price = hist['Close'].iloc[-1]
        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
        change_pct = ((current_price - prev_price) / prev_price) * 100
        
        print(f"✅ USD/TWD: {current_price:.4f} ({change_pct:+.2f}%)")
        print(f"   真實數據，時間: {hist.index[-1]}")
    else:
        print("❌ USD/TWD數據為空")
except Exception as e:
    print(f"❌ USD/TWD獲取失敗: {e}")

# 測試WTI原油
print("\n📊 測試WTI原油...")
try:
    ticker = yf.Ticker("CL=F")
    hist = ticker.history(period="2d")
    
    if not hist.empty:
        current_price = hist['Close'].iloc[-1]
        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
        change_pct = ((current_price - prev_price) / prev_price) * 100
        
        print(f"✅ WTI原油: ${current_price:.2f} ({change_pct:+.2f}%)")
        print(f"   真實數據，時間: {hist.index[-1]}")
    else:
        print("❌ WTI原油數據為空")
except Exception as e:
    print(f"❌ WTI原油獲取失敗: {e}")

print("\n" + "=" * 40)
print("💡 如果上述數據顯示成功，表示yfinance可以獲取真實數據")
print("如果爬蟲仍顯示模擬數據，請檢查真實數據獲取器的初始化")
