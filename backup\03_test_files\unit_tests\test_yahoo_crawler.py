#!/usr/bin/env python3
"""
測試Yahoo即時報價爬蟲功能
驗證反爬蟲機制和數據獲取穩定性
"""

import sys
import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_single_stock():
    """測試單支股票數據獲取"""
    print("=" * 60)
    print("🧪 測試單支股票數據獲取")
    print("=" * 60)
    
    try:
        from yahoo_realtime_crawler import get_realtime_quote
        
        test_stock = "2330"  # 台積電
        print(f"📊 測試股票: {test_stock} (台積電)")
        
        start_time = time.time()
        df = get_realtime_quote(test_stock)
        end_time = time.time()
        
        if not df.empty:
            print("✅ 單支股票測試成功")
            print(f"⏱️ 耗時: {end_time - start_time:.2f} 秒")
            print("\n📈 獲取的數據:")
            
            latest = df.iloc[0]
            print(f"   股票代碼: {latest.get('stock_id', test_stock)}")
            print(f"   現價: ${latest['close']:.2f}")
            print(f"   開盤: ${latest['open']:.2f}")
            print(f"   最高: ${latest['high']:.2f}")
            print(f"   最低: ${latest['low']:.2f}")
            print(f"   成交量: {latest['volume']:,.0f}")
            print(f"   漲跌幅: {latest['pct_change']:+.2f}%")
            print(f"   昨收: ${latest['prev_close']:.2f}")
            print(f"   更新時間: {latest['datetime']}")
            print(f"   數據源: {latest.get('source', 'yahoo_crawler')}")
            
            return True
        else:
            print("❌ 單支股票測試失敗 - 無數據返回")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_multiple_stocks():
    """測試多支股票批量獲取"""
    print("\n" + "=" * 60)
    print("🧪 測試多支股票批量獲取")
    print("=" * 60)
    
    try:
        from yahoo_realtime_crawler import get_multiple_quotes
        
        test_stocks = ["2330", "2317", "2454", "3008", "2412"]
        stock_names = {
            "2330": "台積電",
            "2317": "鴻海",
            "2454": "聯發科", 
            "3008": "大立光",
            "2412": "中華電"
        }
        
        print(f"📊 測試股票列表: {test_stocks}")
        for stock in test_stocks:
            print(f"   {stock}: {stock_names.get(stock, '未知')}")
        
        start_time = time.time()
        results = get_multiple_quotes(test_stocks)
        end_time = time.time()
        
        success_count = len(results)
        total_count = len(test_stocks)
        
        print(f"\n✅ 批量獲取完成")
        print(f"⏱️ 總耗時: {end_time - start_time:.2f} 秒")
        print(f"📊 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if results:
            print("\n📈 獲取結果:")
            for stock_id, data in results.items():
                if not data.empty:
                    latest = data.iloc[0]
                    stock_name = stock_names.get(stock_id, "未知")
                    price = latest['close']
                    change = latest['pct_change']
                    volume = latest['volume']
                    
                    trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                    print(f"   {trend} {stock_id} ({stock_name}): ${price:.2f} ({change:+.2f}%) 量:{volume:,.0f}")
                else:
                    print(f"   ❌ {stock_id}: 數據獲取失敗")
            
            return success_count > 0
        else:
            print("❌ 批量獲取失敗 - 無任何數據返回")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_anti_crawler_mechanism():
    """測試反爬蟲機制"""
    print("\n" + "=" * 60)
    print("🛡️ 測試反爬蟲機制")
    print("=" * 60)
    
    try:
        from yahoo_realtime_crawler import YahooRealtimeCrawler
        
        crawler = YahooRealtimeCrawler()
        
        print("🔍 測試項目:")
        print("   1. 隨機User-Agent輪換")
        print("   2. 請求頻率控制")
        print("   3. 隨機延遲機制")
        print("   4. 失敗重試機制")
        
        # 測試連續請求
        test_stock = "2330"
        success_count = 0
        total_requests = 3
        
        print(f"\n🔄 連續請求測試 ({total_requests}次):")
        
        for i in range(total_requests):
            print(f"   第{i+1}次請求...")
            start_time = time.time()
            
            df = crawler.get_realtime_data(test_stock)
            
            end_time = time.time()
            
            if not df.empty:
                success_count += 1
                print(f"   ✅ 成功 (耗時: {end_time - start_time:.2f}秒)")
            else:
                print(f"   ❌ 失敗 (耗時: {end_time - start_time:.2f}秒)")
        
        success_rate = success_count / total_requests * 100
        print(f"\n📊 反爬蟲測試結果:")
        print(f"   成功率: {success_count}/{total_requests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("   ✅ 反爬蟲機制運作良好")
            return True
        elif success_rate >= 50:
            print("   ⚠️ 反爬蟲機制需要調整")
            return True
        else:
            print("   ❌ 反爬蟲機制需要改進")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_integration_with_monitor():
    """測試與盤中監控的整合"""
    print("\n" + "=" * 60)
    print("🔗 測試與盤中監控整合")
    print("=" * 60)
    
    try:
        from intraday_monitor import IntradayMonitor
        
        print("📊 創建盤中監控器...")
        monitor = IntradayMonitor(['2330', '2317'], update_interval=30)
        
        print("🔍 檢查Yahoo爬蟲整合狀態:")
        if hasattr(monitor, 'yahoo_crawler') and monitor.yahoo_crawler:
            print("   ✅ Yahoo爬蟲已整合")
            print(f"   ✅ 使用Yahoo爬蟲: {monitor.use_yahoo_crawler}")
        else:
            print("   ❌ Yahoo爬蟲未整合")
            return False
        
        print("\n🧪 測試數據更新方法...")
        try:
            # 測試數據更新（不啟動監控線程）
            monitor._update_all_data()
            
            if monitor.current_data:
                print(f"   ✅ 數據更新成功，獲取 {len(monitor.current_data)} 支股票數據")
                for stock_id, df in monitor.current_data.items():
                    if not df.empty:
                        print(f"      {stock_id}: {len(df)} 筆數據")
                return True
            else:
                print("   ⚠️ 數據更新完成但無數據返回")
                return False
                
        except Exception as e:
            print(f"   ❌ 數據更新失敗: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 Yahoo即時報價爬蟲測試程式")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("單支股票測試", test_single_stock()))
    test_results.append(("多支股票測試", test_multiple_stocks()))
    test_results.append(("反爬蟲機制測試", test_anti_crawler_mechanism()))
    test_results.append(("盤中監控整合測試", test_integration_with_monitor()))
    
    # 顯示測試總結
    print("\n" + "=" * 60)
    print("📋 測試總結")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有測試通過！Yahoo爬蟲運作正常")
        return True
    elif passed >= total * 0.75:
        print("⚠️ 大部分測試通過，建議檢查失敗項目")
        return True
    else:
        print("❌ 多項測試失敗，需要檢查和修復")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 測試程式異常: {e}")
        sys.exit(1)
