#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試HTML結構，了解實際的資料格式
"""

import requests
from bs4 import BeautifulSoup
import re

def debug_html_structure():
    """調試HTML結構"""
    
    print("🔍 調試公開資訊觀測站HTML結構")
    print("=" * 50)
    
    # 測試上市2024年6月的URL
    url = "https://mopsov.twse.com.tw/nas/t21/sii/t21sc03_113_6_0.html"
    
    try:
        response = requests.get(url, verify=False, timeout=30)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            print("✅ 網頁下載成功")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尋找表格
            tables = soup.find_all('table')
            print(f"📊 找到 {len(tables)} 個表格")
            
            for i, table in enumerate(tables):
                print(f"\n表格 {i+1}:")
                rows = table.find_all('tr')
                print(f"  行數: {len(rows)}")
                
                # 檢查前幾行的內容
                for j, row in enumerate(rows[:5]):
                    cells = row.find_all(['td', 'th'])
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    print(f"  第{j+1}行 ({len(cell_texts)}欄): {cell_texts}")
                
                # 尋找包含股票代號的行
                data_rows = []
                for row in rows:
                    cells = row.find_all('td')
                    if cells:
                        cell_texts = [cell.get_text(strip=True) for cell in cells]
                        if len(cell_texts) > 0 and re.match(r'^\d{4}$', cell_texts[0]):
                            data_rows.append(cell_texts)
                            if len(data_rows) <= 3:  # 只顯示前3筆
                                print(f"  數據行: {cell_texts}")
                
                print(f"  找到 {len(data_rows)} 筆股票數據")
                
                if data_rows:
                    print(f"  第一筆完整數據: {data_rows[0]}")
                    print(f"  欄位數量: {len(data_rows[0])}")
                    
                    # 分析欄位內容
                    if len(data_rows[0]) >= 8:
                        sample = data_rows[0]
                        print(f"  欄位分析:")
                        print(f"    [0] 股票代號: {sample[0]}")
                        print(f"    [1] 股票名稱: {sample[1]}")
                        print(f"    [2] 產業別: {sample[2]}")
                        print(f"    [3] 營業收入: {sample[3]}")
                        print(f"    [4] 營收月增率: {sample[4]}")
                        print(f"    [5] 營收年增率: {sample[5]}")
                        print(f"    [6] 累計營收: {sample[6]}")
                        print(f"    [7] 累計年增率: {sample[7]}")
                        if len(sample) > 8:
                            print(f"    [8] 備註: {sample[8]}")
                    
                    break  # 找到數據表格就停止
        
        else:
            print(f"❌ 網頁下載失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 調試失敗: {e}")

if __name__ == "__main__":
    debug_html_structure()
