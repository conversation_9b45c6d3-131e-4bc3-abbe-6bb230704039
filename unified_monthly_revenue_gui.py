#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
統一月營收資料下載器GUI
整合標準版和增強版功能，提供統一的用戶界面
基於tkinter的用戶界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import time
from datetime import datetime
import sqlite3
import pandas as pd
import logging

# 導入批量下載器
try:
    from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
    MOPS_BULK_AVAILABLE = True
except ImportError:
    MOPS_BULK_AVAILABLE = False

# 嘗試導入增強版下載器，如果失敗則使用標準版
try:
    from enhanced_monthly_revenue_downloader import EnhancedMonthlyRevenueDownloader as Downloader
    DOWNLOADER_TYPE = "enhanced"
except ImportError:
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader as Downloader
        DOWNLOADER_TYPE = "standard"
    except ImportError:
        Downloader = None
        DOWNLOADER_TYPE = "none"

class UnifiedMonthlyRevenueGUI:
    def __init__(self, parent=None):
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()
        
        self.setup_window()
        self.create_widgets()
        
        # 初始化下載器
        if Downloader:
            self.downloader = Downloader()
        else:
            self.downloader = None
            
    def setup_window(self):
        """設置視窗"""
        self.window.title("📊 統一月營收資料下載器")
        self.window.geometry("1000x800")
        self.window.resizable(True, True)
        
        # 設置圖標
        try:
            self.window.iconbitmap("icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """創建界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_text = f"📊 統一月營收資料下載器 ({DOWNLOADER_TYPE.upper()})"
        title_label = ttk.Label(main_frame, text=title_text,
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 說明文字
        if DOWNLOADER_TYPE == "enhanced":
            info_text = """
此工具整合了增強版功能，支援：
• 自動處理彈出視窗和廣告
• 指定日期範圍下載
• 無頭模式運行，提高穩定性
• 自動匯入SQLite數據庫
• 全股票批量下載
            """
        elif DOWNLOADER_TYPE == "standard":
            info_text = """
此工具使用標準版功能，支援：
• 基本月營收資料下載
• 單一股票下載
• 基本錯誤處理
• 數據庫匯入
            """
        else:
            info_text = """
⚠️ 警告：未找到可用的下載器模組
請確認以下檔案存在：
• enhanced_monthly_revenue_downloader.py 或
• goodinfo_monthly_revenue_downloader.py
            """
        
        info_label = ttk.Label(main_frame, text=info_text.strip(), 
                              font=('Arial', 10), foreground='gray')
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 下載設定區域
        settings_frame = ttk.LabelFrame(main_frame, text="下載設定", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)

        # 下載模式選擇
        ttk.Label(settings_frame, text="下載模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.download_mode_var = tk.StringVar(value="mops_bulk")
        mode_frame = ttk.Frame(settings_frame)
        mode_frame.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=(0, 10))

        single_radio = ttk.Radiobutton(mode_frame, text="單一股票", variable=self.download_mode_var,
                                      value="single", command=self.toggle_download_mode)
        single_radio.pack(side=tk.LEFT, padx=(0, 20))

        all_radio = ttk.Radiobutton(mode_frame, text="全股票下載 (GoodInfo)", variable=self.download_mode_var,
                                   value="all", command=self.toggle_download_mode)
        all_radio.pack(side=tk.LEFT, padx=(0, 20))

        # 新增：公開資訊觀測站批量下載
        mops_radio = ttk.Radiobutton(mode_frame, text="🚀 批量下載 (公開資訊觀測站)", variable=self.download_mode_var,
                                   value="mops_bulk", command=self.toggle_download_mode)
        mops_radio.pack(side=tk.LEFT)

        # 股票代號輸入（單一股票模式）
        self.stock_input_frame = ttk.Frame(settings_frame)
        self.stock_input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(self.stock_input_frame, text="股票代號:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.stock_var = tk.StringVar(value="2330")
        self.stock_entry = ttk.Entry(self.stock_input_frame, textvariable=self.stock_var, width=15)
        self.stock_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(self.stock_input_frame, text="(例如: 2330, 2317, 2454)",
                 font=('Arial', 9), foreground='gray').grid(row=0, column=2, sticky=tk.W)

        # 日期範圍設定（僅增強版支援）
        if DOWNLOADER_TYPE == "enhanced":
            self.use_date_range_var = tk.BooleanVar(value=True)
            date_range_check = ttk.Checkbutton(settings_frame, text="📅 指定日期範圍",
                                             variable=self.use_date_range_var,
                                             command=self.toggle_date_range)
            date_range_check.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))

            # 日期輸入框
            self.date_frame = ttk.Frame(settings_frame)
            self.date_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

            ttk.Label(self.date_frame, text="開始月份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
            self.start_date_var = tk.StringVar(value="2024-01")
            self.start_date_entry = ttk.Entry(self.date_frame, textvariable=self.start_date_var, width=10)
            self.start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

            ttk.Label(self.date_frame, text="結束月份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
            self.end_date_var = tk.StringVar(value="2025-07")
            self.end_date_entry = ttk.Entry(self.date_frame, textvariable=self.end_date_var, width=10)
            self.end_date_entry.grid(row=0, column=3, sticky=tk.W)

            ttk.Label(self.date_frame, text="(格式: YYYY-MM，如: 2024-01)",
                     font=('Arial', 8), foreground='gray').grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(2, 0))

        # 下載按鈕區域
        download_btn_frame = ttk.Frame(settings_frame)
        download_btn_frame.grid(row=3, column=0, columnspan=3, pady=(15, 0))

        # 主要下載按鈕
        if DOWNLOADER_TYPE != "none":
            self.download_btn = ttk.Button(download_btn_frame, text="🚀 開始下載",
                                         command=self.start_download, style="Accent.TButton")
            self.download_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 測試連接按鈕
            self.test_btn = ttk.Button(download_btn_frame, text="🔧 測試連接",
                                     command=self.test_connection)
            self.test_btn.pack(side=tk.LEFT, padx=(0, 10))
        else:
            error_label = ttk.Label(download_btn_frame, text="❌ 無可用的下載器模組", 
                                   foreground='red', font=('Arial', 12, 'bold'))
            error_label.pack()

        # 初始化界面狀態
        self.toggle_download_mode()
        if DOWNLOADER_TYPE == "enhanced":
            self.toggle_date_range()
        
        # 進度顯示區域
        progress_frame = ttk.LabelFrame(main_frame, text="下載進度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 狀態標籤
        status_text = f"準備就緒 - {DOWNLOADER_TYPE.upper()}版下載器"
        self.status_var = tk.StringVar(value=status_text)
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日誌顯示區域
        log_frame = ttk.LabelFrame(main_frame, text="執行日誌", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 日誌文字框
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 控制按鈕區域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))

        # 清除日誌按鈕
        clear_btn = ttk.Button(control_frame, text="🗑️ 清除日誌", command=self.clear_log)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 新建資料庫按鈕
        create_db_btn = ttk.Button(control_frame, text="🆕 新建資料庫", command=self.create_database)
        create_db_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 匯入CSV按鈕
        import_csv_btn = ttk.Button(control_frame, text="📥 匯入CSV", command=self.import_csv_data)
        import_csv_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 查看數據庫按鈕
        db_btn = ttk.Button(control_frame, text="📊 查看數據庫", command=self.show_database_stats)
        db_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 開啟下載資料夾按鈕
        folder_btn = ttk.Button(control_frame, text="📁 開啟下載資料夾", command=self.open_download_folder)
        folder_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 關閉按鈕
        close_btn = ttk.Button(control_frame, text="❌ 關閉", command=self.window.destroy)
        close_btn.pack(side=tk.RIGHT)
        
        # 初始化日誌
        self.log_message(f"✅ 統一月營收下載器已啟動 ({DOWNLOADER_TYPE.upper()}版)")
        if DOWNLOADER_TYPE == "none":
            self.log_message("❌ 警告：未找到可用的下載器模組")

        # 初始化模式切換（預設為批量下載模式，隱藏股票輸入框）
        self.toggle_download_mode()
    
    def toggle_download_mode(self):
        """切換下載模式"""
        mode = self.download_mode_var.get()
        if mode == "single":
            # 顯示股票輸入框
            for child in self.stock_input_frame.winfo_children():
                child.grid()
        else:
            # 隱藏股票輸入框
            for child in self.stock_input_frame.winfo_children():
                child.grid_remove()

    def toggle_date_range(self):
        """切換日期範圍設定的可用性"""
        if hasattr(self, 'date_frame'):
            state = tk.NORMAL if self.use_date_range_var.get() else tk.DISABLED
            for child in self.date_frame.winfo_children():
                if isinstance(child, ttk.Entry):
                    child.configure(state=state)
    
    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.window.update_idletasks()
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("📝 日誌已清除")
    
    def open_download_folder(self):
        """開啟下載資料夾"""
        try:
            download_path = os.path.join(os.getcwd(), "downloads")
            if not os.path.exists(download_path):
                os.makedirs(download_path)
            
            # 在Windows上開啟資料夾
            if os.name == 'nt':
                os.startfile(download_path)
            else:
                # 在其他系統上使用預設程式開啟
                import subprocess
                subprocess.run(['xdg-open', download_path])
                
            self.log_message(f"📁 已開啟下載資料夾: {download_path}")
        except Exception as e:
            self.log_message(f"❌ 開啟資料夾失敗: {str(e)}")
    
    def test_connection(self):
        """測試連接"""
        if not self.downloader:
            messagebox.showerror("錯誤", "無可用的下載器模組")
            return
            
        self.log_message("🔧 測試連接中...")
        self.status_var.set("測試連接中...")
        
        def test_thread():
            try:
                # 這裡可以添加實際的連接測試邏輯
                self.log_message("✅ 連接測試成功")
                self.status_var.set("連接測試成功")
            except Exception as e:
                self.log_message(f"❌ 連接測試失敗: {str(e)}")
                self.status_var.set("連接測試失敗")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def start_download(self):
        """開始下載"""
        if not self.downloader:
            messagebox.showerror("錯誤", "無可用的下載器模組")
            return

        download_mode = self.download_mode_var.get()

        # 檢查輸入
        if download_mode == "single":
            stock_code = self.stock_var.get().strip()
            if not stock_code:
                messagebox.showerror("錯誤", "請輸入股票代號")
                return

        # 禁用下載按鈕
        self.download_btn.configure(state=tk.DISABLED)
        self.test_btn.configure(state=tk.DISABLED)

        if download_mode == "single":
            self.log_message(f"🚀 開始下載股票 {stock_code} 的月營收資料...")
        elif download_mode == "all":
            self.log_message("🚀 開始下載全股票月營收資料 (GoodInfo)...")
        elif download_mode == "mops_bulk":
            self.log_message("🚀 開始批量下載月營收資料 (公開資訊觀測站)...")

        self.status_var.set("下載中...")
        self.progress_var.set(0)

        def download_thread():
            try:
                if download_mode == "all":
                    # 全股票下載 (GoodInfo)
                    self.download_all_stocks()
                elif download_mode == "mops_bulk":
                    # 批量下載 (公開資訊觀測站)
                    self.download_mops_bulk()
                else:
                    # 單一股票下載
                    self.download_single_stock(stock_code)

                self.progress_var.set(100)
                self.log_message("✅ 下載完成")
                self.status_var.set("下載完成")

                if download_mode == "single":
                    messagebox.showinfo("成功", f"股票 {stock_code} 的月營收資料下載完成！")
                elif download_mode == "mops_bulk":
                    messagebox.showinfo("成功", "批量月營收資料下載完成！\n\n使用公開資訊觀測站數據源，效率大幅提升！")
                else:
                    messagebox.showinfo("成功", "全股票月營收資料下載完成！")

            except Exception as e:
                self.log_message(f"❌ 下載失敗: {str(e)}")
                self.status_var.set("下載失敗")
                messagebox.showerror("錯誤", f"下載失敗：\n{str(e)}")
            finally:
                # 重新啟用按鈕
                self.download_btn.configure(state=tk.NORMAL)
                self.test_btn.configure(state=tk.NORMAL)

        threading.Thread(target=download_thread, daemon=True).start()

    def download_single_stock(self, stock_code):
        """下載單一股票"""
        self.log_message(f"📈 正在下載 {stock_code} 的月營收資料...")

        try:
            # 根據下載器類型調用不同的方法
            if DOWNLOADER_TYPE == "enhanced":
                if hasattr(self, 'use_date_range_var') and self.use_date_range_var.get():
                    start_date = self.start_date_var.get()
                    end_date = self.end_date_var.get()
                    self.log_message(f"📅 使用日期範圍: {start_date} 到 {end_date}")
                    # 調用增強版下載器的方法
                    result = self.downloader.download_stock_revenue_enhanced(stock_code, start_date, end_date)
                else:
                    # 不指定日期範圍
                    result = self.downloader.download_stock_revenue_enhanced(stock_code)
            else:
                # 標準版下載器
                if hasattr(self.downloader, 'download_stock_data'):
                    result = self.downloader.download_stock_data(stock_code)
                else:
                    # 嘗試其他可能的方法名稱
                    result = self.downloader.download_stock_revenue_enhanced(stock_code)

            # 檢查下載結果
            if result:
                self.log_message(f"✅ {stock_code} 下載成功，已匯入數據庫")
            else:
                self.log_message(f"⚠️ {stock_code} 下載完成，但可能沒有新數據")

            return result

        except Exception as e:
            self.log_message(f"❌ {stock_code} 下載失敗: {str(e)}")
            raise e

    def download_all_stocks(self):
        """下載全股票資料"""
        self.log_message("📋 正在獲取股票清單...")

        # 從資料庫獲取股票清單
        stock_list = self.get_stock_list()
        if not stock_list:
            raise Exception("無法獲取股票清單")

        total_stocks = len(stock_list)
        self.log_message(f"📊 找到 {total_stocks} 支股票，開始批量下載...")
        self.log_message("⚠️ 批量下載可能需要較長時間，請耐心等待...")

        success_count = 0
        failed_count = 0
        skipped_count = 0

        for i, stock_code in enumerate(stock_list):
            try:
                progress = (i / total_stocks) * 100
                self.progress_var.set(progress)
                self.status_var.set(f"下載中... ({i+1}/{total_stocks}) {stock_code}")

                # 更新GUI
                self.window.update_idletasks()

                # 下載單一股票
                result = self.download_single_stock(stock_code)

                if result:
                    success_count += 1
                else:
                    skipped_count += 1

                # 避免過於頻繁的請求，防止被封IP
                time.sleep(2)

            except Exception as e:
                failed_count += 1
                # 繼續下載其他股票
                continue

        self.log_message(f"📊 批量下載完成！")
        self.log_message(f"✅ 成功: {success_count} 支")
        self.log_message(f"⚠️ 跳過: {skipped_count} 支")
        self.log_message(f"❌ 失敗: {failed_count} 支")

        # 顯示數據庫統計
        self.show_database_stats()

    def get_stock_list(self):
        """獲取股票清單"""
        try:
            # 優先從價格資料庫獲取股票清單（支持多種命名）
            price_db_paths = [
                "D:/Finlab/history/tables/new_price.db",  # 用戶的實際資料庫
                "D:/Finlab/history/tables/price.db",
                "new_price.db",
                "price.db",
                "./data/new_price.db",
                "./data/price.db",
                "./db/new_price.db",
                "./db/price.db"
            ]

            for db_path in price_db_paths:
                self.log_message(f"🔍 檢查路徑: {db_path}")
                if os.path.exists(db_path):
                    self.log_message(f"✅ 找到數據庫: {db_path}")
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()

                        # 檢查是否有 stock_daily_data 表格（新格式）
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_daily_data'")
                        stock_daily_table = cursor.fetchone()

                        if stock_daily_table:
                            # 從 stock_daily_data 表格獲取股票清單
                            cursor.execute("SELECT DISTINCT stock_id FROM stock_daily_data WHERE stock_id IS NOT NULL AND LENGTH(stock_id) = 4 ORDER BY stock_id")
                            stocks = cursor.fetchall()
                            stock_list = [stock[0] for stock in stocks if stock[0].isdigit()]

                            if stock_list:
                                conn.close()
                                self.log_message(f"✅ 從 stock_daily_data 表格找到 {len(stock_list)} 支股票")
                                self.log_message(f"📊 股票範圍: {stock_list[0]} ~ {stock_list[-1]}")
                                return stock_list

                        # 檢查是否有以 _price 結尾的表格（舊格式）
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%_price'")
                        tables = cursor.fetchall()

                        if tables:
                            stock_list = []
                            for table in tables:
                                stock_code = table[0].replace('_price', '')
                                # 檢查是否為4位數字的股票代號
                                if stock_code.isdigit() and len(stock_code) == 4:
                                    stock_list.append(stock_code)

                            if stock_list:
                                conn.close()
                                stock_list = sorted(stock_list)
                                self.log_message(f"✅ 從 _price 表格找到 {len(stock_list)} 支股票")
                                self.log_message(f"📊 股票範圍: {stock_list[0]} ~ {stock_list[-1]}")
                                return stock_list

                        conn.close()
                        self.log_message(f"⚠️ 數據庫中沒有找到股票數據表格")

                    except Exception as e:
                        self.log_message(f"❌ 連接數據庫失敗: {str(e)}")
                        continue
                else:
                    self.log_message(f"❌ 路徑不存在: {db_path}")

            # 如果找不到 price.db，使用台股主要股票清單
            self.log_message("⚠️ 未找到 price.db，使用預設股票清單")
            default_stocks = [
                # 台積電及半導體龍頭
                "2330", "2454", "3034", "2379", "3711", "2408", "3443", "6770", "2382", "3481",
                # 金融股
                "2881", "2882", "2883", "2884", "2885", "2886", "2887", "2888", "2889", "2890",
                "2891", "2892", "2893", "2894", "2880", "5880", "2809", "2812", "2834", "2845",
                # 傳統產業
                "2317", "2412", "1301", "1303", "2002", "2207", "2301", "2303", "2308", "1216",
                "1101", "2105", "2357", "2603", "1102", "2201", "2204", "2206", "2227", "2615",
                # 電子股
                "2395", "2409", "2474", "2475", "2476", "2477", "2478", "2479", "3008", "2324",
                "2327", "2347", "2353", "2354", "2356", "2377", "2385", "2449", "2458", "2492",
                # 生技醫療
                "4904", "6505", "1789", "1795", "4174", "6446", "6547", "6589", "8454", "8467",
                # 其他重要股票
                "9910", "2912", "3045", "5269", "6239", "6285", "8046", "8454", "9921", "9958"
            ]

            self.log_message(f"📋 使用預設清單，共 {len(default_stocks)} 支股票")
            return default_stocks

        except Exception as e:
            self.log_message(f"❌ 獲取股票清單失敗: {str(e)}")
            # 返回最基本的股票清單
            basic_stocks = ["2330", "2317", "2454", "2412", "2881", "2882", "2883", "2884", "2885"]
            self.log_message(f"🔄 使用基本股票清單，共 {len(basic_stocks)} 支股票")
            return basic_stocks

    def create_database(self):
        """新建月營收資料庫"""
        try:
            from tkinter import filedialog, messagebox
            import sqlite3
            import os

            # 讓用戶選擇資料庫保存位置（與price.db放在同一目錄）
            file_path = filedialog.asksaveasfilename(
                title="新建月營收資料庫",
                defaultextension=".db",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                initialdir="D:/Finlab/history/tables/",
                initialfile="monthly_revenue.db"
            )

            if not file_path:
                return

            # 確保目錄存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 創建資料庫並建立表格
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()

            # 創建月營收表格（更新版本，包含完整欄位）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_revenue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_id TEXT NOT NULL,
                    stock_name TEXT,
                    industry TEXT,
                    market TEXT,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    revenue REAL,
                    revenue_mom REAL,
                    revenue_yoy REAL,
                    cumulative_revenue REAL,
                    cumulative_revenue_yoy REAL,
                    remark TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_id, year, month)
                )
            ''')

            # 創建索引以提高查詢效能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_stock_year_month ON monthly_revenue(stock_id, year, month)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_year_month ON monthly_revenue(year, month)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_stock ON monthly_revenue(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_industry ON monthly_revenue(industry)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_monthly_revenue_market ON monthly_revenue(market)')

            # 創建元數據表格
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS database_info (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入資料庫資訊
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('version', '1.0')
            ''')
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('description', '月營收資料庫 - 儲存台股上市櫃公司月營收數據')
            ''')
            cursor.execute('''
                INSERT OR REPLACE INTO database_info (key, value)
                VALUES ('created_by', '統一月營收下載器')
            ''')

            conn.commit()
            conn.close()

            self.log_message(f"✅ 月營收資料庫已成功創建: {file_path}")
            self.log_message("✅ 已建立 monthly_revenue 表格")
            self.log_message("✅ 已建立索引以提高查詢效能")
            self.log_message("✅ 已建立元數據表格")

            # 更新下載器的資料庫路徑（如果有的話）
            if self.downloader:
                self.downloader.db_path = file_path
                self.log_message(f"✅ 下載器資料庫路徑已更新為: {file_path}")

            messagebox.showinfo("新建成功",
                              f"月營收資料庫已成功創建:\n{file_path}\n\n"
                              f"✅ 已建立 monthly_revenue 表格\n"
                              f"✅ 已建立索引以提高查詢效能\n"
                              f"✅ 已建立元數據表格\n\n"
                              f"現在可以開始下載月營收數據了！")

        except Exception as e:
            self.log_message(f"❌ 新建資料庫失敗: {str(e)}")
            messagebox.showerror("新建失敗", f"新建月營收資料庫失敗:\n{str(e)}")

    def download_mops_bulk(self):
        """使用公開資訊觀測站批量下載月營收數據"""

        if not MOPS_BULK_AVAILABLE:
            self.log_message("❌ 批量下載器模組不可用")
            messagebox.showerror("錯誤", "批量下載器模組不可用，請檢查 mops_bulk_revenue_downloader.py 文件")
            return

        try:
            # 獲取日期範圍
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            if not start_date or not end_date:
                self.log_message("❌ 請設定開始和結束日期")
                return

            # 解析日期
            start_year, start_month = map(int, start_date.split('-'))
            end_year, end_month = map(int, end_date.split('-'))

            self.log_message(f"📅 批量下載日期範圍: {start_year}/{start_month:02d} ~ {end_year}/{end_month:02d}")

            # 初始化批量下載器（使用與price.db相同的目錄）
            default_db_path = "D:/Finlab/history/tables/monthly_revenue.db"
            db_path = getattr(self.downloader, 'db_path', default_db_path) if self.downloader else default_db_path
            bulk_downloader = MopsBulkRevenueDownloader(db_path)

            self.log_message("✅ 批量下載器初始化完成")

            # 計算總月數
            total_months = (end_year - start_year) * 12 + (end_month - start_month) + 1
            current_month_count = 0

            current_year = start_year
            current_month = start_month

            while (current_year < end_year) or (current_year == end_year and current_month <= end_month):

                self.log_message(f"\n📊 下載 {current_year}年{current_month}月 數據...")

                # 下載當月所有市場數據
                results = bulk_downloader.download_all_markets(current_year, current_month)

                # 統計結果
                total_companies = sum(results.values())
                self.log_message(f"✅ {current_year}年{current_month}月 完成：{total_companies} 家公司")

                for market, count in results.items():
                    if count > 0:
                        self.log_message(f"  {market}: {count} 家公司")

                # 更新進度
                current_month_count += 1
                progress = int((current_month_count / total_months) * 100)
                self.progress_var.set(progress)

                # 移動到下個月
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1

                # 避免請求過於頻繁
                time.sleep(1)

            self.log_message(f"\n🎉 批量下載完成！共處理 {total_months} 個月的數據")

        except Exception as e:
            self.log_message(f"❌ 批量下載失敗: {str(e)}")
            raise e

    def show_database_stats(self):
        """顯示數據庫統計資訊"""
        try:
            if not self.downloader or not hasattr(self.downloader, 'db_path'):
                return

            db_path = self.downloader.db_path
            if not os.path.exists(db_path):
                self.log_message("📊 數據庫檔案不存在")
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 獲取表格清單
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            self.log_message("📊 數據庫統計:")
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                self.log_message(f"   {table_name}: {count} 筆記錄")

            conn.close()

        except Exception as e:
            self.log_message(f"❌ 獲取數據庫統計失敗: {str(e)}")

    def import_csv_data(self):
        """匯入CSV數據到資料庫"""
        try:
            # 選擇CSV檔案
            file_path = filedialog.askopenfilename(
                title="選擇月營收CSV檔案",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialdir="D:/Finlab/history/tables/"
            )

            if not file_path:
                return

            self.log_message(f"📥 開始匯入CSV檔案: {file_path}")

            # 讀取CSV檔案
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            self.log_message(f"📊 讀取到 {len(df)} 筆數據")

            # 檢查必要欄位
            required_columns = ['股票代號', '股票名稱', '營收(千元)']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                error_msg = f"CSV檔案缺少必要欄位: {', '.join(missing_columns)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("匯入失敗", error_msg)
                return

            # 確定資料庫路徑
            if not self.downloader or not hasattr(self.downloader, 'db_path'):
                db_path = "D:/Finlab/history/tables/monthly_revenue.db"
                self.log_message(f"⚠️ 使用預設資料庫路徑: {db_path}")
            else:
                db_path = self.downloader.db_path

            # 確保資料庫存在
            if not os.path.exists(db_path):
                self.log_message("❌ 資料庫不存在，請先新建資料庫")
                messagebox.showerror("匯入失敗", "資料庫不存在，請先新建資料庫")
                return

            # 連接資料庫
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 從檔案名稱推斷年月（如果可能）
            import re
            filename = os.path.basename(file_path)
            year_month_match = re.search(r'(\d{4})_(\d{1,2})', filename)

            if year_month_match:
                default_year = int(year_month_match.group(1))
                default_month = int(year_month_match.group(2))
                self.log_message(f"📅 從檔案名稱推斷年月: {default_year}/{default_month:02d}")
            else:
                # 使用當前年月作為預設值
                from datetime import datetime
                now = datetime.now()
                default_year = now.year
                default_month = now.month
                self.log_message(f"📅 使用當前年月: {default_year}/{default_month:02d}")

            # 準備匯入數據
            success_count = 0
            error_count = 0

            for index, row in df.iterrows():
                try:
                    # 準備數據
                    stock_id = str(row['股票代號']).strip()
                    stock_name = str(row['股票名稱']).strip()
                    industry = str(row.get('產業別', '')).strip() if '產業別' in df.columns else ''
                    market = str(row.get('市場別', '')).strip() if '市場別' in df.columns else ''

                    # 營收數據
                    revenue = self.parse_number(row.get('營收(千元)', 0))
                    revenue_mom = self.parse_number(row.get('營收月增率(%)', None))
                    revenue_yoy = self.parse_number(row.get('營收年增率(%)', None))
                    cumulative_revenue = self.parse_number(row.get('累計營收(千元)', None))
                    cumulative_revenue_yoy = self.parse_number(row.get('累計營收年增率(%)', None))
                    remark = str(row.get('備註', '')).strip()

                    # 插入或更新數據
                    cursor.execute('''
                        INSERT OR REPLACE INTO monthly_revenue
                        (stock_id, stock_name, industry, market, year, month,
                         revenue, revenue_mom, revenue_yoy, cumulative_revenue,
                         cumulative_revenue_yoy, remark, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (stock_id, stock_name, industry, market, default_year, default_month,
                          revenue, revenue_mom, revenue_yoy, cumulative_revenue,
                          cumulative_revenue_yoy, remark))

                    success_count += 1

                    # 每100筆提交一次
                    if success_count % 100 == 0:
                        conn.commit()
                        self.log_message(f"📊 已匯入 {success_count} 筆數據...")

                except Exception as e:
                    error_count += 1
                    self.log_message(f"❌ 第{index+1}行匯入失敗: {str(e)}")
                    continue

            # 最終提交
            conn.commit()
            conn.close()

            # 顯示結果
            self.log_message(f"✅ 匯入完成！")
            self.log_message(f"✅ 成功: {success_count} 筆")
            self.log_message(f"❌ 失敗: {error_count} 筆")

            # 顯示數據庫統計
            self.show_database_stats()

            messagebox.showinfo("匯入完成",
                              f"CSV數據匯入完成！\n\n"
                              f"✅ 成功匯入: {success_count} 筆\n"
                              f"❌ 匯入失敗: {error_count} 筆\n\n"
                              f"年月: {default_year}/{default_month:02d}")

        except Exception as e:
            self.log_message(f"❌ 匯入CSV失敗: {str(e)}")
            messagebox.showerror("匯入失敗", f"匯入CSV檔案失敗:\n{str(e)}")

    def parse_number(self, value):
        """解析數字，處理各種格式"""
        if pd.isna(value) or value == '' or value == '-':
            return None

        try:
            # 移除逗號和百分號
            if isinstance(value, str):
                value = value.replace(',', '').replace('%', '').strip()

            return float(value)
        except:
            return None

    def run(self):
        """運行GUI"""
        self.window.mainloop()

if __name__ == "__main__":
    app = UnifiedMonthlyRevenueGUI()
    app.run()
