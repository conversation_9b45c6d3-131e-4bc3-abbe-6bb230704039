# 📈 二次創高股票策略 - 完整說明文檔

## 📊 策略概述

**二次創高股票策略**是一個專門捕捉二次創高的強勢股票策略，結合營收成長和成交量確認。這個策略基於您提供的程式碼，完整實現了8個核心條件和賣出信號機制。

## 🎯 策略核心理念

### 二次創高概念
**核心邏輯**：股票在經歷一段整理後，再次創出新高的強勢表現

1. **第一次創高**：第30-55日前曾經創過60日新高
2. **中間整理**：前30日有非新高的整理期間
3. **二次創高**：近日再次創出60日新高
4. **價格突破**：突破早期整理期間的最高價

### 🔥 買入信號條件
**買入信號觸發**：當以下8個條件**全部同時滿足**時產生買入信號

#### ⭐ 核心突破條件（必須滿足）
1. **近日創新高**：當日收盤價創近60日新高
2. **價格突破**：突破前30-55日整理期間的最高價

#### 📊 技術面確認條件
3. **前期非新高**：前30日有整理期間（避免連續創高）
4. **早期創新高**：第30-55日前曾創60日新高（確認第一次創高）
5. **長期趨勢1**：當前價格 > 120日前價格（4個月趨勢）
6. **長期趨勢2**：當前價格 > 60日前價格（2個月趨勢）

#### 💰 基本面與成交量條件
7. **營收成長**：近3月平均營收 > 12月平均營收
8. **成交量確認**：近5日平均成交量 > 20日平均成交量

### 策略特色
- **強勢突破**：專門捕捉二次創高的強勢股
- **多重確認**：8個條件全部通過才進場
- **趨勢跟隨**：順應長期上升趨勢
- **成交量確認**：放量突破增加可信度
- **及時停損**：20MA下彎立即賣出

## 📋 完整篩選條件（對應原始程式碼）

### 1. 近日創新高 (cond1)
```python
newhigh = close.rolling(60, min_periods=1).max() == close
cond1 = newhigh
```
- **條件**：近一日收盤價創近60日新高
- **目的**：確保股票處於突破狀態
- **技術意義**：當前價格是60日內的最高點

### 2. 前期非新高 (cond2)  
```python
cond2 = (newhigh.shift(1) == 0).rolling(30).sum() > 0
```
- **條件**：前30日(不包含今日)有至少一日的收盤價未創新高
- **目的**：確保有整理期間，避免連續創高
- **技術意義**：存在盤整或回調過程

### 3. 早期創新高 (cond3)
```python
cond3 = (newhigh.shift(30).rolling(25).sum() > 0)
```
- **條件**：第30日～第55日前的收盤價有至少一日收盤價創近60日新高
- **目的**：確認之前有過創高記錄
- **技術意義**：證明股票具備創高能力

### 4. 價格突破 (cond4)
```python
cond4 = (close.shift(30).rolling(25).max() < close)
```
- **條件**：前30日～55日的收盤價最高價小於近日收盤價
- **目的**：確認突破早期高點
- **技術意義**：價格創出新的突破高度

### 5. 120日長期趨勢 (cond5)
```python
cond5 = close > close.shift(120)
```
- **條件**：近日收盤價大於120前收盤價
- **目的**：確保長期上升趨勢
- **技術意義**：4個月趨勢向上

### 6. 60日中期趨勢 (cond6)
```python
cond6 = close > close.shift(60)
```
- **條件**：近日收盤價大於60前收盤價
- **目的**：確保中期上升趨勢
- **技術意義**：2個月趨勢向上

### 7. 營收成長確認 (cond7)
```python
rev = data.get('monthly_revenue:當月營收')
cond7 = rev.average(3) > rev.average(12)
```
- **條件**：近3月平均營收大於12月平均營收
- **目的**：確保基本面成長動能
- **技術意義**：營收呈現成長趨勢

### 8. 成交量確認 (cond8)
```python
cond8 = vol.average(5) > vol.average(20)
```
- **條件**：近5日平均成交量大於近20日平均成交量
- **目的**：確認突破伴隨放量
- **技術意義**：資金關注度提升

## ⚠️ 賣出信號機制

### 🔴 賣出信號觸發條件
```python
sma20 = close.average(20)
sell = sma20 < sma20.shift()
```
- **賣出條件**：20日移動平均線開始下彎
- **技術意義**：短期趨勢開始轉弱
- **風險控制**：及時停損，避免趨勢反轉損失
- **信號特點**：一旦20MA下彎立即賣出，不等待確認

## 📊 完整交易邏輯

### 🟢 買入信號生成
```python
buy = cond1 & cond2 & cond3 & cond4 & cond5 & cond6 & cond7 & cond8
```
**買入條件**：所有8個條件必須同時滿足

### 🔴 賣出信號生成
```python
sell = MA20今日 < MA20昨日
```
**賣出條件**：20日均線開始下彎即賣出

### 📈 K線圖信號顯示
- **🟢 綠色向上箭頭**：買入信號（8條件全滿足）
  - 標籤：「二次創高買入」
  - 位置：收盤價下方（98%位置）
  - 觸發：所有8個條件同時滿足
- **🔴 紅色向下箭頭**：賣出信號（20MA下彎）
  - 標籤：「20MA下彎賣出」
  - 位置：收盤價上方（102%位置）
  - 觸發：20日均線開始下彎
- **📊 信號統計**：狀態欄顯示買入/賣出次數
- **🎯 智能顯示**：只顯示最近5個信號，保持圖表清晰
- **💹 歷史回顧**：可查看過去所有信號的表現

### 部位管理
```python
position = pd.DataFrame(np.nan, index=buy.index, columns=buy.columns)
position[buy] = 1
position[sell] = 0
position = position.ffill().fillna(0)
```

### 回測設定
```python
report = backtest.sim(position.loc['2014':], 
                     resample="W",                    # 週線調整
                     name='二次創高股票', 
                     live_performance_start='2018-06-01')
```

## 🎯 策略優勢

### 技術面優勢
1. **多重確認**：8個條件層層把關
2. **趨勢跟隨**：順應長中短期趨勢
3. **突破確認**：真正的價格突破
4. **成交量配合**：放量突破更可靠

### 基本面優勢
1. **營收成長**：確保公司基本面向好
2. **成長動能**：短期營收優於長期平均
3. **雙重保障**：技術面+基本面雙重確認

### 風險控制
1. **明確停損**：20MA下彎立即賣出
2. **趨勢跟隨**：順勢而為，減少逆勢風險
3. **週線調整**：避免日線雜訊干擾

## 📈 適用環境

### 最佳使用時機
- **多頭市場**：大盤處於上升趨勢
- **突破行情**：個股突破整理區間
- **成長股行情**：市場偏好成長股
- **資金充沛**：市場流動性充足

### 避免使用時機
- **熊市環境**：大盤下跌趨勢
- **震盪整理**：缺乏明確方向
- **流動性緊縮**：成交量萎縮
- **基本面惡化**：營收衰退

## 🔧 實戰操作要點

### 選股重點
1. **嚴格篩選**：8個條件缺一不可
2. **關注營收**：定期檢查月營收數據
3. **成交量監控**：確保持續放量
4. **技術確認**：等待明確突破信號

### 風險管理
1. **及時停損**：20MA下彎立即賣出
2. **分散投資**：不要集中單一股票
3. **部位控制**：合理配置資金比例
4. **定期檢討**：週線調整，動態管理

### 績效追蹤
1. **條件監控**：持續追蹤8個條件變化
2. **技術分析**：關注20MA趨勢變化
3. **基本面追蹤**：月營收數據更新
4. **市場環境**：整體市場趨勢判斷

## 🎉 總結

二次創高股票策略是一個經過精心設計的動能突破策略，通過8個嚴格的篩選條件，專門捕捉具備二次創高能力的強勢股票。

**核心價值**：
- 完整的技術分析框架
- 基本面成長確認
- 明確的風險控制機制
- 適合中短期動能投資

**投資心法**：
- 順勢而為，跟隨趨勢
- 嚴格篩選，精選標的
- 及時停損，保護資本
- 讓獲利奔跑，控制風險

這個策略特別適合在多頭市場中捕捉強勢突破股，為投資者提供了一個科學、系統的選股工具！📈
