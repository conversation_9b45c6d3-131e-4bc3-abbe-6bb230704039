#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復ROE資料庫結構
添加缺少的year欄位並統一資料庫結構
"""

import sqlite3
import os
import logging
from datetime import datetime

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_roe_database():
    """修復ROE資料庫結構"""
    db_path = "D:/Finlab/history/tables/roe_data.db"
    
    try:
        # 確保目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查當前表結構
        cursor.execute("PRAGMA table_info(roe_data)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        logger.info(f"當前資料庫欄位: {column_names}")
        
        # 檢查是否需要添加year欄位
        if 'year' not in column_names:
            logger.info("🔧 添加year欄位...")
            cursor.execute("ALTER TABLE roe_data ADD COLUMN year INTEGER")
            
            # 如果有report_year資料，將其複製到year欄位
            cursor.execute("UPDATE roe_data SET year = report_year WHERE report_year IS NOT NULL")
            logger.info("✅ year欄位添加完成")
        
        # 檢查並添加其他缺少的欄位
        required_columns = {
            'market_cap': 'REAL',
            'industry': 'TEXT',
            'created_at': 'TIMESTAMP',
            'updated_at': 'TIMESTAMP'
        }
        
        for col_name, col_type in required_columns.items():
            if col_name not in column_names:
                logger.info(f"🔧 添加{col_name}欄位...")
                cursor.execute(f"ALTER TABLE roe_data ADD COLUMN {col_name} {col_type}")
                logger.info(f"✅ {col_name}欄位添加完成")
        
        # 創建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_roe_stock_year ON roe_data(stock_code, year)",
            "CREATE INDEX IF NOT EXISTS idx_roe_year ON roe_data(year)",
            "CREATE INDEX IF NOT EXISTS idx_roe_value ON roe_data(roe_value)",
            "CREATE INDEX IF NOT EXISTS idx_roe_rank ON roe_data(rank_position)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info("✅ 索引創建完成")
        
        # 提交變更
        conn.commit()
        
        # 檢查修復後的結構
        cursor.execute("PRAGMA table_info(roe_data)")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        
        logger.info(f"修復後資料庫欄位: {new_column_names}")
        
        # 檢查資料統計
        cursor.execute("SELECT COUNT(*) FROM roe_data")
        total_count = cursor.fetchone()[0]
        logger.info(f"📊 資料庫總筆數: {total_count}")
        
        if total_count > 0:
            # 檢查年份分布
            cursor.execute("SELECT year, COUNT(*) FROM roe_data WHERE year IS NOT NULL GROUP BY year ORDER BY year DESC")
            year_stats = cursor.fetchall()
            
            logger.info("📅 各年份資料統計:")
            for year, count in year_stats:
                logger.info(f"   {year}年: {count} 筆")
        
        conn.close()
        logger.info("🎉 資料庫修復完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 資料庫修復失敗: {e}")
        return False

def test_database_operations():
    """測試資料庫操作"""
    db_path = "D:/Finlab/history/tables/roe_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 測試插入資料
        test_data = {
            'stock_code': 'TEST',
            'stock_name': '測試公司',
            'year': 2024,
            'roe_value': 15.5,
            'rank_position': 999,
            'data_source': 'test'
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO roe_data 
            (stock_code, stock_name, year, roe_value, rank_position, data_source)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            test_data['stock_code'],
            test_data['stock_name'], 
            test_data['year'],
            test_data['roe_value'],
            test_data['rank_position'],
            test_data['data_source']
        ))
        
        conn.commit()
        
        # 測試查詢
        cursor.execute("SELECT * FROM roe_data WHERE stock_code = 'TEST'")
        result = cursor.fetchone()
        
        if result:
            logger.info("✅ 資料庫操作測試成功")
            # 清理測試資料
            cursor.execute("DELETE FROM roe_data WHERE stock_code = 'TEST'")
            conn.commit()
        else:
            logger.error("❌ 資料庫操作測試失敗")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 資料庫操作測試失敗: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 開始修復ROE資料庫...")
    
    # 修復資料庫結構
    if fix_roe_database():
        # 測試資料庫操作
        if test_database_operations():
            logger.info("🎉 資料庫修復和測試完成！")
        else:
            logger.error("💥 資料庫測試失敗！")
    else:
        logger.error("💥 資料庫修復失敗！")
