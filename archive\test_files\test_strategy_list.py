#!/usr/bin/env python3
"""
測試策略列表是否正確包含膽小貓策略
"""

import sys
import os
import json

def test_strategy_list():
    """測試策略列表"""
    try:
        print("🧪 測試策略列表配置")
        print("=" * 50)
        
        # 模擬策略配置（從主程式中提取）
        strategies = {
            # 🔵 原有策略保留
            "勝率73.45%": [
                {"type": "ma_trend", "ma": "MA240", "trend": "up", "days": 1},
                {"type": "ma_future_trend", "ma": "MA240", "days": 20},
                {"type": "ma_trend", "ma": "MA20", "trend": "up", "days": 5},
                {"type": "volume_ma_cross", "short_period": 3, "long_period": 18, "days": 3},
                {"type": "rsi_combined", "rsi_long": 13, "rsi_long_threshold": 50, "rsi_short": 6, "rsi_short_threshold": 70},
                {"type": "volume_value", "min_value": 10000000}
            ],
            "破底反彈高量": [
                {"type": "break_bottom_rebound", "lookback": 10, "rebound_threshold": 5, "volume_multiplier": 2}
            ],
            # 🌟 新增：阿水一式策略
            "阿水一式": [
                {"type": "ashui_strategy"}
            ],
            # 🌟 新增：阿水二式策略（空方策略）
            "阿水二式": [
                {"type": "ashui_short_strategy"}
            ],
            # 🐕 新增：藏獒策略（動能策略）
            "藏獒": [
                {"type": "tibetan_mastiff_strategy"}
            ],
            # 🚀 新增：CANSLIM量價齊升策略
            "CANSLIM量價齊升": [
                {"type": "canslim_strategy"}
            ],
            # 🐱 新增：膽小貓策略（低風險穩健投資法）
            "膽小貓": [
                {"type": "timid_cat_strategy"}
            ]
        }
        
        print("📊 策略列表:")
        for i, (strategy_name, conditions) in enumerate(strategies.items(), 1):
            print(f"{i:2d}. {strategy_name}")
            for condition in conditions:
                print(f"    - {condition['type']}")
        
        print(f"\n📈 統計信息:")
        print(f"總策略數: {len(strategies)}")
        print(f"原有策略: 2個")
        print(f"新增策略: {len(strategies) - 2}個")
        
        # 檢查膽小貓策略
        if "膽小貓" in strategies:
            print(f"\n🐱 膽小貓策略檢查:")
            timid_cat_strategy = strategies["膽小貓"]
            print(f"✅ 膽小貓策略已添加")
            print(f"   條件類型: {timid_cat_strategy[0]['type']}")
            print(f"   條件數量: {len(timid_cat_strategy)}")
            
            if timid_cat_strategy[0]['type'] == 'timid_cat_strategy':
                print(f"✅ 策略類型正確")
            else:
                print(f"❌ 策略類型錯誤")
        else:
            print(f"\n❌ 膽小貓策略未找到")
            
        # 檢查CANSLIM策略
        if "CANSLIM量價齊升" in strategies:
            print(f"\n🚀 CANSLIM策略檢查:")
            canslim_strategy = strategies["CANSLIM量價齊升"]
            print(f"✅ CANSLIM策略已添加")
            print(f"   條件類型: {canslim_strategy[0]['type']}")
            print(f"   條件數量: {len(canslim_strategy)}")
            
            if canslim_strategy[0]['type'] == 'canslim_strategy':
                print(f"✅ 策略類型正確")
            else:
                print(f"❌ 策略類型錯誤")
        else:
            print(f"\n❌ CANSLIM策略未找到")
        
        # 保存策略配置到文件
        config_file = "strategies_test.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(strategies, f, ensure_ascii=False, indent=4)
        print(f"\n💾 策略配置已保存到: {config_file}")
        
        # 檢查支援的條件類型
        print(f"\n🔧 支援的條件類型:")
        supported_conditions = [
            'ma_position', 'ma_trend', 'ma_trend_or', 'volume_increase', 'macd', 'rsi', 'rsi_combined',
            'volume_value', 'volume_ma_cross', 'ma_future_trend', 'break_bottom_rebound',
            # 保留的智能策略條件
            'ma_support', 'volume_confirmation', 'price_recovery', 'ma_golden_cross', 'trend_momentum', 'volume_trend',
            # 阿水策略系列
            'ashui_strategy', 'ashui_short_strategy',
            # 藏獒策略
            'tibetan_mastiff_strategy',
            # CANSLIM策略
            'canslim_strategy',
            # 膽小貓策略
            'timid_cat_strategy'
        ]
        
        new_strategies = ['ashui_strategy', 'ashui_short_strategy', 'tibetan_mastiff_strategy', 'canslim_strategy', 'timid_cat_strategy']
        
        for condition in new_strategies:
            if condition in supported_conditions:
                print(f"✅ {condition}")
            else:
                print(f"❌ {condition} - 未支援")
        
        print(f"\n🎯 測試結果:")
        if "膽小貓" in strategies and "CANSLIM量價齊升" in strategies:
            print(f"✅ 所有新策略都已正確添加")
            print(f"✅ 策略配置格式正確")
            print(f"✅ 條件類型匹配")
            return True
        else:
            print(f"❌ 部分策略缺失")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_strategy_list()
    if success:
        print(f"\n🎉 策略列表測試通過！")
    else:
        print(f"\n💥 策略列表測試失敗！")
