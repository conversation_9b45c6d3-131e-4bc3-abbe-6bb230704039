#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終版本的GoodInfo月營收下載器
結合參考程式碼的優點
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoAlertPresentException, TimeoutException
import time
import os
import shutil
import glob
import logging

class FinalGoodinfoDownloader:
    def __init__(self, download_dir="C:/Users/<USER>/Downloads"):
        self.download_dir = download_dir
        os.makedirs(download_dir, exist_ok=True)
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self):
        """設置Chrome驅動器"""
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("prefs", {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        })
        
        # 不使用無頭模式，方便調試
        # chrome_options.add_argument("--headless")
        
        return webdriver.Chrome(options=chrome_options)
    
    def handle_popups_and_ads(self, driver):
        """處理彈窗和廣告"""
        try:
            # 處理alert彈窗
            try:
                alert = driver.switch_to.alert
                alert.dismiss()
                self.logger.info("✅ 已處理alert彈窗")
            except NoAlertPresentException:
                self.logger.info("📝 無alert彈窗")
            except TimeoutException:
                self.logger.info("⚠️ 處理alert彈窗逾時")
            
            # 移除廣告iframe
            ad_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@id, 'google_ads_iframe')]")
            for iframe in ad_iframes:
                try:
                    driver.execute_script("arguments[0].remove();", iframe)
                    self.logger.info("🚫 移除Google廣告iframe")
                except:
                    pass
            
            # 移除其他廣告元素
            ad_selectors = [
                "#ats-interstitial-container",
                "[id*='google_ads']",
                "div[style*='position: fixed']"
            ]
            
            for selector in ad_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        driver.execute_script("arguments[0].remove();", element)
                        self.logger.info(f"🚫 移除廣告元素: {selector}")
                except:
                    pass
            
            time.sleep(2)
            
        except Exception as e:
            self.logger.warning(f"⚠️ 處理廣告時出錯: {e}")
    
    def set_date_range(self, driver, start_date="2023-01", end_date="2025-07"):
        """設置日期範圍"""
        try:
            self.logger.info(f"📅 設定日期範圍: {start_date} 到 {end_date}")
            
            # 查找日期輸入框
            date_inputs = driver.find_elements(By.XPATH, "//input[@type='month']")
            self.logger.info(f"找到 {len(date_inputs)} 個日期輸入框")
            
            if len(date_inputs) >= 2:
                # 設置開始日期
                driver.execute_script("arguments[0].value = arguments[1];", date_inputs[0], start_date)
                time.sleep(1)
                
                # 設置結束日期
                driver.execute_script("arguments[0].value = arguments[1];", date_inputs[1], end_date)
                time.sleep(1)
                
                # 處理廣告
                self.handle_popups_and_ads(driver)
                
                # 點擊查詢按鈕（多種方法）
                query_methods = [
                    ("JavaScript執行ReloadDetail", lambda: driver.execute_script("ReloadDetail();")),
                    ("JavaScript點擊查詢按鈕", lambda: driver.execute_script(
                        "arguments[0].click();", driver.find_element(By.XPATH, "//input[@value='查詢']"))),
                ]
                
                for method_name, method_func in query_methods:
                    try:
                        self.logger.info(f"嘗試 {method_name}...")
                        method_func()
                        self.logger.info(f"✅ {method_name} 成功")
                        time.sleep(5)  # 等待查詢結果
                        return True
                    except Exception as e:
                        self.logger.warning(f"⚠️ {method_name} 失敗: {e}")
                
                self.logger.warning("⚠️ 所有查詢方法都失敗")
                return False
            else:
                self.logger.warning("⚠️ 未找到足夠的日期輸入框")
                return False
                
        except Exception as e:
            self.logger.warning(f"⚠️ 設定日期範圍失敗: {e}")
            return False
    
    def click_xls_button(self, driver):
        """點擊XLS按鈕"""
        try:
            self.logger.info("🔍 尋找XLS按鈕...")
            
            # 多種方式尋找XLS按鈕
            xls_selectors = [
                "//input[@value='XLS']",
                "//*[contains(text(), 'XLS')]",
                "//input[contains(@onclick, 'export2xls')]"
            ]
            
            xls_button = None
            for selector in xls_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        xls_button = elements[0]
                        self.logger.info(f"✅ 找到XLS按鈕: {selector}")
                        break
                except:
                    continue
            
            if not xls_button:
                self.logger.error("❌ 未找到XLS按鈕")
                return False
            
            # 點擊XLS按鈕（多種方法）
            click_methods = [
                ("直接執行onclick", lambda: driver.execute_script(xls_button.get_attribute("onclick"))),
                ("JavaScript點擊", lambda: driver.execute_script("arguments[0].click();", xls_button)),
                ("直接點擊", lambda: xls_button.click())
            ]
            
            for method_name, method_func in click_methods:
                try:
                    self.logger.info(f"🖱️ 嘗試 {method_name}...")
                    method_func()
                    self.logger.info(f"✅ {method_name} 成功")
                    return True
                except Exception as e:
                    self.logger.warning(f"⚠️ {method_name} 失敗: {e}")
            
            self.logger.error("❌ 所有點擊方法都失敗")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ XLS按鈕處理失敗: {e}")
            return False
    
    def wait_for_download(self, stock_id="2330", timeout=60):
        """等待下載完成"""
        try:
            self.logger.info("⏳ 等待下載完成...")
            
            # 記錄初始文件
            initial_files = set(glob.glob(os.path.join(self.download_dir, "*.xls")))
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                current_files = set(glob.glob(os.path.join(self.download_dir, "*.xls")))
                new_files = current_files - initial_files
                
                # 檢查新文件
                for new_file in new_files:
                    filename = os.path.basename(new_file).lower()
                    if 'salemondetail' in filename:
                        self.logger.info(f"✅ 檢測到新文件: {os.path.basename(new_file)}")
                        
                        # 等待文件穩定
                        time.sleep(3)
                        if os.path.exists(new_file) and os.path.getsize(new_file) > 0:
                            # 重新命名文件
                            timestamp = time.strftime('%Y%m%d_%H%M%S')
                            new_filename = f"{stock_id}_台積電_monthly_revenue_{timestamp}.xls"
                            target_path = os.path.join(self.download_dir, new_filename)
                            
                            try:
                                shutil.move(new_file, target_path)
                                self.logger.info(f"🎉 文件已重新命名: {new_filename}")
                                return target_path
                            except Exception as e:
                                self.logger.warning(f"⚠️ 重新命名失敗: {e}")
                                return new_file
                
                # 檢查現有的最近文件（5分鐘內）
                for excel_file in current_files:
                    filename = os.path.basename(excel_file).lower()
                    if 'salemondetail' in filename:
                        file_mtime = os.path.getmtime(excel_file)
                        if time.time() - file_mtime < 300:  # 5分鐘內
                            self.logger.info(f"🔍 檢測到最近文件: {os.path.basename(excel_file)}")
                            return excel_file
                
                time.sleep(2)
            
            self.logger.warning("⏰ 下載超時")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 等待下載失敗: {e}")
            return None
    
    def download_stock_revenue(self, stock_id="2330", start_date="2023-01", end_date="2025-07"):
        """下載股票月營收資料"""
        driver = None
        try:
            self.logger.info(f"🚀 開始下載 {stock_id} 月營收資料...")
            
            # 設置驅動器
            driver = self.setup_driver()
            
            # 訪問頁面
            url = f"https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID={stock_id}"
            self.logger.info(f"📱 訪問頁面: {url}")
            driver.get(url)
            
            # 等待頁面載入
            time.sleep(5)
            
            # 處理彈窗和廣告
            self.handle_popups_and_ads(driver)
            
            # 設置日期範圍
            if start_date and end_date:
                self.set_date_range(driver, start_date, end_date)
            
            # 點擊XLS按鈕
            if not self.click_xls_button(driver):
                return None
            
            # 等待下載完成
            result = self.wait_for_download(stock_id)
            
            if result:
                self.logger.info(f"🎉 下載成功: {result}")
                return result
            else:
                self.logger.error("❌ 下載失敗")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 下載過程失敗: {e}")
            return None
            
        finally:
            if driver:
                input("按Enter鍵關閉瀏覽器...")
                driver.quit()

def main():
    """主函數"""
    downloader = FinalGoodinfoDownloader()
    result = downloader.download_stock_revenue("2330", "2023-01", "2025-07")
    
    if result:
        print(f"🎉 下載成功: {result}")
    else:
        print("❌ 下載失敗")

if __name__ == "__main__":
    main()
