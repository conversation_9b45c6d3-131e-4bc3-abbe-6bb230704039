#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查pe_data資料庫結構
"""

import sqlite3
import os

def check_pe_database():
    """檢查pe_data相關資料庫"""
    print("🔍 檢查財務指標資料庫...")
    
    # 可能的資料庫路徑
    possible_paths = [
        "D:/Finlab/history/tables/pe_data.db",
        "history/tables/pe_data.db",
        "history/tables/tpex_financial_ratios.db",
        "history/tables/financial_statements.db",
        "db/pe_data.db"
    ]
    
    for db_path in possible_paths:
        print(f"\n📂 檢查: {db_path}")
        
        if os.path.exists(db_path):
            print(f"✅ 資料庫存在: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 查看表結構
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"📊 表格列表:")
                for table in tables:
                    print(f"  - {table[0]}")
                
                # 查看主要表的結構
                if tables:
                    table_name = tables[0][0]
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    print(f"\n📋 {table_name} 表格結構:")
                    for col in columns:
                        print(f"  {col[1]} ({col[2]})")
                    
                    # 查看是否有2301的數據
                    try:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE stock_id='2301' LIMIT 3")
                        samples = cursor.fetchall()
                        if samples:
                            print(f"\n📝 2301光寶科 樣本數據:")
                            for sample in samples:
                                print(f"  {sample}")
                        else:
                            print(f"\n⚠️ 沒有找到2301的數據")
                    except Exception as e:
                        print(f"⚠️ 查詢2301數據失敗: {e}")
                        # 嘗試查看所有數據
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        samples = cursor.fetchall()
                        print(f"\n📝 樣本數據:")
                        for sample in samples:
                            print(f"  {sample}")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 讀取資料庫失敗: {e}")
        else:
            print(f"❌ 資料庫不存在: {db_path}")

def check_financial_data_for_2301():
    """專門檢查2301的財務數據"""
    print("\n" + "="*50)
    print("🔍 專門檢查2301光寶科的財務數據")
    print("="*50)
    
    # 檢查financial_statements.db
    db_path = "history/tables/financial_statements.db"
    if os.path.exists(db_path):
        print(f"✅ 檢查 {db_path}")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查詢2301的財務數據
            query = """
            SELECT stock_id, stock_name, date,
                   income_本期淨利（淨損）, balance_資產總額, balance_權益總額,
                   income_營業收入（淨額）
            FROM financial_data 
            WHERE stock_id = '2301' 
            ORDER BY date DESC 
            LIMIT 5
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            if results:
                print(f"📊 找到2301的財務數據:")
                for result in results:
                    stock_id, stock_name, date, net_income, total_assets, total_equity, revenue = result
                    print(f"  日期: {date}")
                    print(f"  淨利: {net_income}")
                    print(f"  總資產: {total_assets}")
                    print(f"  權益總額: {total_equity}")
                    print(f"  營業收入: {revenue}")
                    print(f"  ---")
                    
                    # 計算財務指標
                    if net_income and total_assets and total_equity:
                        try:
                            # 假設流通股數為1億股 (需要實際數據)
                            shares = 100000000  # 1億股
                            eps = float(net_income) / shares * 1000  # 轉換單位
                            roe = float(net_income) / float(total_equity) * 100
                            roa = float(net_income) / float(total_assets) * 100
                            
                            print(f"  計算指標:")
                            print(f"    EPS: {eps:.2f}元")
                            print(f"    ROE: {roe:.2f}%")
                            print(f"    ROA: {roa:.2f}%")
                            print(f"  ---")
                        except Exception as e:
                            print(f"  計算指標失敗: {e}")
            else:
                print(f"❌ 沒有找到2301的財務數據")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 查詢失敗: {e}")
    else:
        print(f"❌ 資料庫不存在: {db_path}")

def main():
    """主函數"""
    check_pe_database()
    check_financial_data_for_2301()
    
    print("\n" + "="*50)
    print("🎯 結論:")
    print("如果pe_data.db不存在，我們可以:")
    print("1. 從financial_statements.db計算財務指標")
    print("2. 從其他資料庫補充股價相關數據")
    print("3. 建立新的pe_data.db資料庫")
    print("="*50)

if __name__ == "__main__":
    main()
