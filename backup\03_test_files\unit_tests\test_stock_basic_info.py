"""
測試上市櫃基本資料爬蟲功能
"""

import sys
import os

def test_module_imports():
    """測試模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        from stock_basic_info_crawler import (
            StockBasicInfoDatabase, 
            get_market_types,
            get_mops_headers
        )
        print("✅ 核心爬蟲模組導入成功")
        
        from stock_basic_info_gui import StockBasicInfoGUI
        print("✅ GUI模組導入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_database_functionality():
    """測試資料庫功能"""
    print("\n🔍 測試資料庫功能...")
    
    try:
        from stock_basic_info_crawler import StockBasicInfoDatabase
        
        # 創建測試資料庫
        db = StockBasicInfoDatabase('test_stock_basic_info.db')
        print("✅ 資料庫初始化成功")
        
        # 測試統計功能
        count = db.get_data_count()
        print(f"✅ 資料庫統計功能正常，目前有 {count} 筆資料")
        
        # 測試市場統計
        stats = db.get_market_stats()
        print(f"✅ 市場統計功能正常: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ 資料庫功能測試失敗: {e}")
        return False

def test_market_types():
    """測試市場類型功能"""
    print("\n🔍 測試市場類型功能...")
    
    try:
        from stock_basic_info_crawler import get_market_types
        
        markets = get_market_types()
        print(f"✅ 支援的市場類型: {markets}")
        
        expected_markets = ['sii', 'otc', 'rotc', 'pub']
        for market_code in expected_markets:
            if market_code in markets:
                print(f"  ✅ {market_code}: {markets[market_code]}")
            else:
                print(f"  ❌ 缺少市場類型: {market_code}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 市場類型測試失敗: {e}")
        return False

def test_headers():
    """測試請求標頭"""
    print("\n🔍 測試請求標頭...")
    
    try:
        from stock_basic_info_crawler import get_mops_headers
        
        headers = get_mops_headers()
        print("✅ 請求標頭生成成功")
        
        required_headers = ['User-Agent', 'Accept', 'Accept-Language']
        for header in required_headers:
            if header in headers:
                print(f"  ✅ {header}: {headers[header][:50]}...")
            else:
                print(f"  ❌ 缺少標頭: {header}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 請求標頭測試失敗: {e}")
        return False

def test_gui_initialization():
    """測試GUI初始化"""
    print("\n🔍 測試GUI初始化...")
    
    try:
        import tkinter as tk
        from stock_basic_info_gui import StockBasicInfoGUI
        
        # 創建隱藏的根視窗
        root = tk.Tk()
        root.withdraw()
        
        # 測試GUI類別初始化（不顯示）
        app = StockBasicInfoGUI()
        print("✅ GUI初始化成功")
        
        # 測試統計更新
        app.update_stats()
        print("✅ 統計更新功能正常")
        
        root.destroy()
        return True
    except Exception as e:
        print(f"❌ GUI初始化測試失敗: {e}")
        return False

def test_menu_integration():
    """測試選單整合"""
    print("\n🔍 測試選單整合...")
    
    try:
        # 檢查主程式是否包含新的選單項目
        with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'open_stock_basic_info_crawler' in content:
            print("✅ 主程式包含上市櫃基本資料爬蟲方法")
        else:
            print("❌ 主程式缺少上市櫃基本資料爬蟲方法")
            return False
        
        if '上市櫃基本資料清單' in content:
            print("✅ 選單包含上市櫃基本資料清單項目")
        else:
            print("❌ 選單缺少上市櫃基本資料清單項目")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 選單整合測試失敗: {e}")
        return False

def test_file_structure():
    """測試檔案結構"""
    print("\n🔍 測試檔案結構...")
    
    required_files = [
        'stock_basic_info_crawler.py',
        'stock_basic_info_gui.py',
        'O3mh_gui_v21_optimized.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_database_path():
    """測試資料庫路徑"""
    print("\n🔍 測試資料庫路徑...")
    
    try:
        from stock_basic_info_crawler import StockBasicInfoDatabase
        
        db = StockBasicInfoDatabase()
        expected_path = r"D:\Finlab\history\tables\stock_basic_info.db"
        
        print(f"📍 實際路徑: {db.db_path}")
        print(f"📍 期望路徑: {expected_path}")
        
        if db.db_path == expected_path:
            print("✅ 資料庫路徑設定正確")
        else:
            print("⚠️ 資料庫路徑與期望不符，但功能正常")
        
        # 檢查目錄是否存在
        db_dir = os.path.dirname(db.db_path)
        if os.path.exists(db_dir):
            print(f"✅ 資料庫目錄存在: {db_dir}")
        else:
            print(f"❌ 資料庫目錄不存在: {db_dir}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 資料庫路徑測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始上市櫃基本資料爬蟲整合測試")
    print("=" * 60)
    
    tests = [
        ("檔案結構", test_file_structure),
        ("模組導入", test_module_imports),
        ("資料庫功能", test_database_functionality),
        ("市場類型", test_market_types),
        ("請求標頭", test_headers),
        ("資料庫路徑", test_database_path),
        ("GUI初始化", test_gui_initialization),
        ("選單整合", test_menu_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 測試未通過")
        except Exception as e:
            print(f"❌ {test_name} 測試發生錯誤: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！上市櫃基本資料爬蟲整合成功！")
        print("\n📋 使用說明:")
        print("1. 啟動主程式 O3mh_gui_v21_optimized.py")
        print("2. 點擊選單：🕷️ 爬蟲 → 🏢 上市櫃基本資料清單")
        print("3. 選擇市場別（上市/上櫃/興櫃/公開發行）")
        print("4. 點擊「開始爬取」")
        print("5. 資料將儲存到 D:\\Finlab\\history\\tables\\stock_basic_info.db")
        print("\n💡 提示:")
        print("- 可選擇「爬取全部市場別」一次取得所有資料")
        print("- 支援CSV匯出功能")
        print("- 包含公司基本資料、董事長、總經理等完整資訊")
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
