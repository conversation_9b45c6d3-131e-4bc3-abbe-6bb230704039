#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab 爬蟲系統 簡化版 GUI 界面

使用 tkinter 提供跨平台的圖形化界面
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime, timedelta
import threading

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class FinlabCrawlerGUI:
    """Finlab 爬蟲系統主界面"""
    
    def __init__(self, root):
        self.root = root
        self.crawler_funcs = None
        self.setup_ui()
        self.init_crawlers()
    
    def setup_ui(self):
        """設置用戶界面"""
        self.root.title("🚀 Finlab 新版資料庫爬蟲系統")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # 創建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="🚀 Finlab 新版資料庫爬蟲系統", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 創建爬蟲按鈕區域
        self.create_crawler_buttons(main_frame)
        
        # 創建參數設置區域
        self.create_parameter_section(main_frame)
        
        # 創建日誌顯示區域
        self.create_log_section(main_frame)
        
        # 創建進度條
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        self.progress.grid_remove()  # 初始隱藏
    
    def create_crawler_buttons(self, parent):
        """創建爬蟲功能按鈕"""
        # 爬蟲功能框架
        crawler_frame = ttk.LabelFrame(parent, text="📊 可用的爬蟲功能", padding="10")
        crawler_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        crawler_frame.columnconfigure(tuple(range(5)), weight=1)
        
        # 爬蟲功能列表
        self.crawlers = [
            ("股價資料", "price"),
            ("三大法人", "bargin_report"),
            ("本益比", "pe"),
            ("月營收", "monthly_report"),
            ("大盤指數", "benchmark"),
            ("財務報表", "financial_statement"),
            ("上市除權息", "twse_divide_ratio"),
            ("上櫃除權息", "otc_divide_ratio"),
            ("上市減資", "twse_cap_reduction"),
            ("上櫃減資", "otc_cap_reduction")
        ]
        
        self.crawler_buttons = {}
        
        # 創建按鈕（2列5行）
        for i, (name, key) in enumerate(self.crawlers):
            btn = ttk.Button(crawler_frame, text=f"{i+1}. {name}",
                           command=lambda n=name, k=key: self.run_crawler(n, k))

            # 設置按鈕樣式
            btn.configure(width=15)

            row = i // 5
            col = i % 5
            btn.grid(row=row, column=col, padx=8, pady=8, sticky=(tk.W, tk.E))
            self.crawler_buttons[key] = btn
    
    def create_parameter_section(self, parent):
        """創建參數設置區域"""
        param_frame = ttk.LabelFrame(parent, text="⚙️ 參數設置", padding="10")
        param_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        param_frame.columnconfigure(1, weight=1)
        param_frame.columnconfigure(3, weight=1)
        
        # 日期範圍設置
        ttk.Label(param_frame, text="📅 日期範圍:").grid(row=0, column=0, columnspan=4, sticky=tk.W)
        
        ttk.Label(param_frame, text="天數:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.days_var = tk.StringVar(value="30")
        days_combo = ttk.Combobox(param_frame, textvariable=self.days_var, 
                                 values=["7", "30", "60", "90", "180", "365"], width=10)
        days_combo.grid(row=1, column=1, sticky=tk.W, padx=(0, 20))
        
        # 財務報表參數
        ttk.Label(param_frame, text="📈 財務報表:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5))
        
        finance_frame = ttk.Frame(param_frame)
        finance_frame.grid(row=1, column=3, sticky=(tk.W, tk.E))
        
        ttk.Label(finance_frame, text="年度:").grid(row=0, column=0, sticky=tk.W)
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_combo = ttk.Combobox(finance_frame, textvariable=self.year_var,
                                 values=[str(y) for y in range(2020, 2031)], width=8)
        year_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(finance_frame, text="季度:").grid(row=0, column=2, sticky=tk.W)
        self.season_var = tk.StringVar(value="4")
        season_combo = ttk.Combobox(finance_frame, textvariable=self.season_var,
                                   values=["1", "2", "3", "4"], width=5)
        season_combo.grid(row=0, column=3, padx=(5, 0))
    
    def create_log_section(self, parent):
        """創建日誌顯示區域"""
        log_frame = ttk.LabelFrame(parent, text="📋 執行日誌", padding="10")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日誌文本區域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80,
                                                 font=('Consolas', 9),
                                                 bg='#2b2b2b', fg='#ffffff',
                                                 insertbackground='white')
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除按鈕
        clear_btn = ttk.Button(log_frame, text="🗑️ 清除日誌", command=self.clear_log)
        clear_btn.grid(row=1, column=0, pady=(5, 0))
        
        # 初始化日誌
        self.add_log("🚀 Finlab 爬蟲系統已啟動")
    
    def init_crawlers(self):
        """初始化爬蟲功能"""
        try:
            self.check_dependencies()
            self.load_crawler_modules()
        except Exception as e:
            self.add_log(f"❌ 初始化失敗: {str(e)}")
            messagebox.showerror("初始化錯誤", f"爬蟲系統初始化失敗:\n{str(e)}")
    
    def check_dependencies(self):
        """檢查依賴套件"""
        required_packages = ['pandas', 'requests']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            msg = f"缺少必要依賴: {', '.join(missing_packages)}"
            self.add_log(f"❌ {msg}")
            messagebox.showwarning("依賴檢查", f"{msg}\n請安裝: pip install {' '.join(missing_packages)}")
            return False
        
        self.add_log("✅ 依賴檢查通過")
        return True
    
    def load_crawler_modules(self):
        """載入爬蟲模組"""
        try:
            # 嘗試使用安裝版 finlab
            from finlab.crawler import (
                crawl_price, crawl_bargin, crawl_pe, crawl_monthly_report,
                crawl_finance_statement_by_date, crawl_twse_divide_ratio,
                crawl_otc_divide_ratio, crawl_twse_cap_reduction,
                crawl_otc_cap_reduction, crawl_benchmark
            )
            
            self.crawler_funcs = {
                'price': crawl_price,
                'bargin_report': crawl_bargin,
                'pe': crawl_pe,
                'monthly_report': crawl_monthly_report,
                'benchmark': crawl_benchmark,
                'financial_statement': crawl_finance_statement_by_date,
                'twse_divide_ratio': crawl_twse_divide_ratio,
                'otc_divide_ratio': crawl_otc_divide_ratio,
                'twse_cap_reduction': crawl_twse_cap_reduction,
                'otc_cap_reduction': crawl_otc_cap_reduction,
            }
            
            self.add_log("✅ 安裝版 Finlab 爬蟲模組載入成功")
            
        except ImportError:
            # 使用簡化版本
            self.crawler_funcs = self.create_simple_crawlers()
            self.add_log("⚠️ 使用簡化版爬蟲功能")
            self.add_log("💡 如需完整功能，請安裝: pip install finlab")
    
    def create_simple_crawlers(self):
        """創建簡化版爬蟲函數"""
        import pandas as pd
        
        def simple_crawler(*args, **kwargs):
            return pd.DataFrame({'message': ['簡化版功能 - 需要完整的 finlab 模組']})
        
        return {key: simple_crawler for key in [
            'price', 'bargin_report', 'pe', 'monthly_report', 'benchmark',
            'financial_statement', 'twse_divide_ratio', 'otc_divide_ratio',
            'twse_cap_reduction', 'otc_cap_reduction'
        ]}
    
    def run_crawler(self, crawler_name, crawler_key):
        """執行爬蟲"""
        if not self.crawler_funcs:
            messagebox.showerror("錯誤", "爬蟲模組未正確載入")
            return
        
        # 在新線程中執行爬蟲
        def crawler_thread():
            try:
                self.set_buttons_enabled(False)
                self.progress.grid()
                self.progress.start()
                
                self.add_log(f"🔄 開始執行 {crawler_name} 爬蟲...")
                
                # 準備參數
                crawler_func = self.crawler_funcs[crawler_key]
                
                if crawler_key in ['price', 'bargin_report', 'pe', 'benchmark', 'monthly_report']:
                    # 這些爬蟲函數只需要一個日期參數
                    days = int(self.days_var.get())
                    test_date = datetime.now() - timedelta(days=1)  # 使用昨天的日期
                    result = crawler_func(test_date)

                elif crawler_key == 'financial_statement':
                    # 財務報表爬蟲需要一個日期參數
                    test_date = datetime.now() - timedelta(days=1)
                    result = crawler_func(test_date)

                else:  # 除權息、減資
                    result = crawler_func()
                
                # 顯示結果
                if hasattr(result, 'shape'):
                    message = f"✅ {crawler_name} 爬蟲執行完成\n📊 獲取資料筆數: {result.shape[0]} 筆"
                else:
                    message = f"✅ {crawler_name} 爬蟲執行完成"
                
                self.add_log(message)
                messagebox.showinfo("執行完成", message)
                
            except Exception as e:
                error_msg = f"❌ {crawler_name} 爬蟲執行失敗: {str(e)}"
                self.add_log(error_msg)
                messagebox.showerror("執行失敗", error_msg)
                
            finally:
                self.progress.stop()
                self.progress.grid_remove()
                self.set_buttons_enabled(True)
        
        # 啟動線程
        thread = threading.Thread(target=crawler_thread, daemon=True)
        thread.start()
    
    def set_buttons_enabled(self, enabled):
        """設置按鈕啟用狀態"""
        state = 'normal' if enabled else 'disabled'
        for btn in self.crawler_buttons.values():
            btn.configure(state=state)
    
    def add_log(self, message):
        """添加日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)  # 自動滾動到底部
    
    def clear_log(self):
        """清除日誌"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("📋 日誌已清除")

def main():
    """主函數"""
    root = tk.Tk()
    app = FinlabCrawlerGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n程式已中斷")
    except Exception as e:
        print(f"程式執行錯誤: {e}")

if __name__ == "__main__":
    main()
