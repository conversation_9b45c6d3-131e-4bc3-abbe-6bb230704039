#!/usr/bin/env python3
"""
智能代理管理器
解決網路請求限制、地理位置限制和頻率限制問題
"""

import requests
import time
import logging
import random
import threading
from typing import List, Dict, Optional, Any
from collections import defaultdict, deque
import json

class ProxyManager:
    """智能代理管理器"""
    
    def __init__(self):
        self.proxies = []
        self.proxy_stats = defaultdict(lambda: {
            'success_count': 0,
            'error_count': 0,
            'last_used': 0,
            'response_time': 0,
            'status': 'active'  # active, failed, banned
        })
        
        self.current_proxy_index = 0
        self.lock = threading.Lock()
        
        # 代理配置
        self.proxy_timeout = 10
        self.max_retries = 3
        self.ban_threshold = 5  # 連續失敗次數閾值
        self.cooldown_time = 300  # 5分鐘冷卻時間
        
        # 載入代理列表
        self._load_proxy_list()
        
        logging.info(f"🌐 代理管理器初始化完成，載入 {len(self.proxies)} 個代理")
    
    def _load_proxy_list(self):
        """載入代理列表"""
        # 免費代理列表（示例）
        free_proxies = [
            # HTTP代理
            {'type': 'http', 'host': '***********', 'port': 80},
            {'type': 'http', 'host': '************', 'port': 8888},
            {'type': 'http', 'host': '*************', 'port': 3128},
            
            # HTTPS代理
            {'type': 'https', 'host': '***************', 'port': 7492},
            {'type': 'https', 'host': '***************', 'port': 7300},
            
            # SOCKS5代理
            {'type': 'socks5', 'host': '************', 'port': 31679},
            {'type': 'socks5', 'host': '************', 'port': 4145},
        ]
        
        # 付費代理列表（需要購買）
        premium_proxies = [
            # 示例：亮數據 (Bright Data)
            # {'type': 'http', 'host': 'brd.superproxy.io', 'port': 22225, 'auth': ('username', 'password')},
            
            # 示例：ProxyMesh
            # {'type': 'http', 'host': 'proxy.proxymesh.com', 'port': 31280, 'auth': ('username', 'password')},
        ]
        
        self.proxies = free_proxies + premium_proxies
        
        # 從配置文件載入（如果存在）
        try:
            with open('proxy_config.json', 'r', encoding='utf-8') as f:
                config_proxies = json.load(f)
                self.proxies.extend(config_proxies)
                logging.info(f"✅ 從配置文件載入 {len(config_proxies)} 個代理")
        except FileNotFoundError:
            logging.info("📄 代理配置文件不存在，使用默認代理列表")
        except Exception as e:
            logging.error(f"❌ 載入代理配置失敗: {e}")
    
    def get_proxy_config(self, proxy: Dict) -> Dict:
        """獲取代理配置"""
        if proxy['type'] in ['http', 'https']:
            config = {
                'http': f"http://{proxy['host']}:{proxy['port']}",
                'https': f"http://{proxy['host']}:{proxy['port']}"
            }
        elif proxy['type'] == 'socks5':
            config = {
                'http': f"socks5://{proxy['host']}:{proxy['port']}",
                'https': f"socks5://{proxy['host']}:{proxy['port']}"
            }
        else:
            return {}
        
        # 添加認證信息
        if 'auth' in proxy:
            username, password = proxy['auth']
            if proxy['type'] in ['http', 'https']:
                config = {
                    'http': f"http://{username}:{password}@{proxy['host']}:{proxy['port']}",
                    'https': f"http://{username}:{password}@{proxy['host']}:{proxy['port']}"
                }
            elif proxy['type'] == 'socks5':
                config = {
                    'http': f"socks5://{username}:{password}@{proxy['host']}:{proxy['port']}",
                    'https': f"socks5://{username}:{password}@{proxy['host']}:{proxy['port']}"
                }
        
        return config
    
    def get_next_proxy(self) -> Optional[Dict]:
        """獲取下一個可用代理"""
        with self.lock:
            if not self.proxies:
                return None
            
            # 過濾可用代理
            available_proxies = []
            current_time = time.time()
            
            for i, proxy in enumerate(self.proxies):
                proxy_key = f"{proxy['host']}:{proxy['port']}"
                stats = self.proxy_stats[proxy_key]
                
                # 檢查代理狀態
                if stats['status'] == 'active':
                    available_proxies.append((i, proxy))
                elif stats['status'] == 'banned':
                    # 檢查是否過了冷卻時間
                    if current_time - stats['last_used'] > self.cooldown_time:
                        stats['status'] = 'active'
                        stats['error_count'] = 0
                        available_proxies.append((i, proxy))
            
            if not available_proxies:
                logging.warning("⚠️ 沒有可用的代理")
                return None
            
            # 選擇最佳代理（基於成功率和響應時間）
            best_proxy = self._select_best_proxy(available_proxies)
            return best_proxy
    
    def _select_best_proxy(self, available_proxies: List) -> Dict:
        """選擇最佳代理"""
        if len(available_proxies) == 1:
            return available_proxies[0][1]
        
        # 計算代理評分
        proxy_scores = []
        for i, proxy in available_proxies:
            proxy_key = f"{proxy['host']}:{proxy['port']}"
            stats = self.proxy_stats[proxy_key]
            
            total_requests = stats['success_count'] + stats['error_count']
            success_rate = stats['success_count'] / max(total_requests, 1)
            response_time = stats['response_time'] or 1.0
            
            # 評分公式：成功率權重70%，響應時間權重30%
            score = success_rate * 0.7 + (1.0 / response_time) * 0.3
            proxy_scores.append((score, proxy))
        
        # 選擇評分最高的代理
        proxy_scores.sort(key=lambda x: x[0], reverse=True)
        return proxy_scores[0][1]
    
    def report_proxy_result(self, proxy: Dict, success: bool, response_time: float = 0):
        """報告代理使用結果"""
        with self.lock:
            proxy_key = f"{proxy['host']}:{proxy['port']}"
            stats = self.proxy_stats[proxy_key]
            
            stats['last_used'] = time.time()
            
            if success:
                stats['success_count'] += 1
                stats['response_time'] = response_time
                if stats['status'] == 'failed':
                    stats['status'] = 'active'
            else:
                stats['error_count'] += 1
                
                # 檢查是否需要標記為失敗
                if stats['error_count'] >= self.ban_threshold:
                    stats['status'] = 'banned'
                    logging.warning(f"⚠️ 代理 {proxy_key} 被標記為失敗")
    
    def get_stats(self) -> Dict:
        """獲取代理統計信息"""
        with self.lock:
            total_proxies = len(self.proxies)
            active_proxies = sum(1 for stats in self.proxy_stats.values() if stats['status'] == 'active')
            banned_proxies = sum(1 for stats in self.proxy_stats.values() if stats['status'] == 'banned')
            
            return {
                'total_proxies': total_proxies,
                'active_proxies': active_proxies,
                'banned_proxies': banned_proxies,
                'proxy_stats': dict(self.proxy_stats)
            }

class SmartRequestManager:
    """智能請求管理器"""
    
    def __init__(self):
        self.proxy_manager = ProxyManager()
        self.session = requests.Session()
        
        # 設置默認headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 請求配置
        self.timeout = 10
        self.max_retries = 3
        self.retry_delay = 1
        
        logging.info("🚀 智能請求管理器初始化完成")
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """發送智能請求"""
        for attempt in range(self.max_retries):
            try:
                # 獲取代理
                proxy = self.proxy_manager.get_next_proxy()
                
                if proxy:
                    proxy_config = self.proxy_manager.get_proxy_config(proxy)
                    kwargs['proxies'] = proxy_config
                    logging.debug(f"🌐 使用代理: {proxy['host']}:{proxy['port']}")
                else:
                    logging.debug("🌐 直接連接（無代理）")
                
                # 設置超時
                kwargs.setdefault('timeout', self.timeout)
                kwargs.setdefault('verify', False)  # 忽略SSL驗證
                
                # 發送請求
                start_time = time.time()
                
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支援的HTTP方法: {method}")
                
                response_time = time.time() - start_time
                
                # 檢查響應
                if response.status_code == 200:
                    if proxy:
                        self.proxy_manager.report_proxy_result(proxy, True, response_time)
                    logging.debug(f"✅ 請求成功: {url}")
                    return response
                elif response.status_code == 429:
                    # 請求頻率限制
                    if proxy:
                        self.proxy_manager.report_proxy_result(proxy, False)
                    logging.warning(f"⚠️ 請求頻率限制: {url}")
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    if proxy:
                        self.proxy_manager.report_proxy_result(proxy, False)
                    logging.warning(f"⚠️ HTTP錯誤 {response.status_code}: {url}")
                    
            except requests.exceptions.ProxyError as e:
                if proxy:
                    self.proxy_manager.report_proxy_result(proxy, False)
                logging.warning(f"⚠️ 代理錯誤: {e}")
                continue
                
            except requests.exceptions.Timeout as e:
                if proxy:
                    self.proxy_manager.report_proxy_result(proxy, False)
                logging.warning(f"⚠️ 請求超時: {e}")
                continue
                
            except Exception as e:
                if proxy:
                    self.proxy_manager.report_proxy_result(proxy, False)
                logging.error(f"❌ 請求失敗: {e}")
                continue
            
            # 重試延遲
            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay * (attempt + 1))
        
        logging.error(f"❌ 所有重試都失敗: {url}")
        return None
    
    def get_json(self, url: str, **kwargs) -> Optional[Dict]:
        """獲取JSON數據"""
        response = self.make_request(url, **kwargs)
        if response:
            try:
                return response.json()
            except json.JSONDecodeError as e:
                logging.error(f"❌ JSON解析失敗: {e}")
                return None
        return None
    
    def get_stats(self) -> Dict:
        """獲取統計信息"""
        return self.proxy_manager.get_stats()

# 創建全局實例
smart_request_manager = SmartRequestManager()

# 便捷函數
def make_smart_request(url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
    """便捷函數：發送智能請求"""
    return smart_request_manager.make_request(url, method, **kwargs)

def get_smart_json(url: str, **kwargs) -> Optional[Dict]:
    """便捷函數：獲取JSON數據"""
    return smart_request_manager.get_json(url, **kwargs)

def get_proxy_stats() -> Dict:
    """便捷函數：獲取代理統計"""
    return smart_request_manager.get_stats()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試智能代理管理器")
    
    # 測試請求
    test_urls = [
        'https://httpbin.org/ip',
        'https://mis.twse.com.tw/stock/api/getStockInfo.jsp?ex_ch=tse_2330.tw&json=1',
        'https://www.google.com'
    ]
    
    for url in test_urls:
        print(f"\n🔍 測試: {url}")
        response = make_smart_request(url)
        if response:
            print(f"✅ 成功: {response.status_code}")
        else:
            print(f"❌ 失敗")
    
    # 顯示統計
    stats = get_proxy_stats()
    print(f"\n📊 代理統計:")
    print(f"  總代理數: {stats['total_proxies']}")
    print(f"  可用代理: {stats['active_proxies']}")
    print(f"  失敗代理: {stats['banned_proxies']}")
