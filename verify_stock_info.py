#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證股票資訊是否正確保存
"""

import sqlite3

def verify_stock_info():
    """驗證股票資訊"""
    
    print("=" * 60)
    print("📊 驗證股票資訊是否正確保存")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    conn = sqlite3.connect(newprice_db)
    cursor = conn.cursor()
    
    # 檢查新增資料的股票資訊
    print("📊 新增資料的股票資訊統計:")
    cursor.execute('''
        SELECT listing_status, COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31" 
        GROUP BY listing_status 
        ORDER BY COUNT(*) DESC
    ''')
    listing_stats = cursor.fetchall()
    
    print("  上市狀態分布:")
    for status, count in listing_stats:
        status_name = status if status else '未分類'
        print(f"    {status_name}: {count:,} 筆")
    
    cursor.execute('''
        SELECT industry, COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31" 
        GROUP BY industry 
        ORDER BY COUNT(*) DESC
        LIMIT 10
    ''')
    industry_stats = cursor.fetchall()
    
    print("\n  產業別分布 (前10):")
    for industry, count in industry_stats:
        industry_name = industry if industry else '未分類'
        print(f"    {industry_name}: {count:,} 筆")
    
    # 檢查重要股票的資訊
    print(f"\n📊 重要股票資訊驗證:")
    important_stocks = ['2330', '2317', '0050', '0056']
    
    for stock_id in important_stocks:
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, [Close], date 
            FROM stock_daily_data 
            WHERE stock_id = ? AND date >= "2022-08-31" 
            ORDER BY date
            LIMIT 1
        ''', (stock_id,))
        stock_data = cursor.fetchone()
        
        if stock_data:
            sid, name, status, industry, close, date = stock_data
            print(f"  {sid}: {name} | {status} | {industry} | 收盤:{close}")
        else:
            print(f"  {stock_id}: 無新增資料")
    
    # 檢查有完整資訊的股票數量
    print(f"\n📊 資料完整性統計:")
    
    cursor.execute('''
        SELECT COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31" AND stock_name != ""
    ''')
    with_name = cursor.fetchone()[0]
    
    cursor.execute('''
        SELECT COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31" AND listing_status != ""
    ''')
    with_status = cursor.fetchone()[0]
    
    cursor.execute('''
        SELECT COUNT(*) 
        FROM stock_daily_data 
        WHERE date >= "2022-08-31"
    ''')
    total_new = cursor.fetchone()[0]
    
    print(f"  總新增記錄: {total_new:,} 筆")
    print(f"  有股票名稱: {with_name:,} 筆 ({with_name/total_new*100:.1f}%)")
    print(f"  有上市狀態: {with_status:,} 筆 ({with_status/total_new*100:.1f}%)")
    
    # 檢查範例資料
    print(f"\n📋 完整資訊範例 (有名稱、狀態、產業):")
    cursor.execute('''
        SELECT stock_id, stock_name, listing_status, industry, [Close] 
        FROM stock_daily_data 
        WHERE date = "2022-09-01" 
          AND stock_name != "" 
          AND listing_status != "" 
          AND industry != "ETF"
        ORDER BY [Close] DESC
        LIMIT 10
    ''')
    complete_info = cursor.fetchall()
    
    for stock_id, name, status, industry, close in complete_info:
        print(f"  {stock_id} {name} | {status} | {industry} | 收盤:{close}")
    
    conn.close()
    
    print(f"\n✅ 股票資訊驗證完成！")

if __name__ == "__main__":
    verify_stock_info()
