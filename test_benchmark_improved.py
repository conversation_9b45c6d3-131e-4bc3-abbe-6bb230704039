#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用改進後的邏輯測試 benchmark 更新
參考 pe_crawler 的成功經驗
"""

# 修復 ipywidgets 依賴問題
import sys
import types

# 創建 mock ipywidgets 模組
class MockOutput:
    def __init__(self, **kwargs):
        pass

    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

# 添加 finlab 路徑
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

print(f"🔍 finlab 路徑: {finlab_path}")
print(f"🔍 路徑存在: {os.path.exists(finlab_path)}")

# 導入必要模組
from crawler import (
    crawl_benchmark,
    crawl_pe,  # 參考成功的爬蟲
    table_date_range,
    to_pickle,
    date_range,
)

import datetime
import pandas as pd
import time
import random
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_pe_vs_benchmark():
    """比較 pe 和 benchmark 爬蟲的表現"""
    print("🔍 比較 PE 和 Benchmark 爬蟲表現")
    print("=" * 60)
    
    # 選擇測試日期 - 最近的工作日
    test_date = datetime.datetime.now() - datetime.timedelta(days=1)
    while test_date.weekday() >= 5:  # 跳過週末
        test_date -= datetime.timedelta(days=1)
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
    
    # 測試 PE 爬蟲 (已知正常工作)
    print(f"\n🧪 測試 PE 爬蟲...")
    try:
        pe_result = crawl_pe(test_date)
        if pe_result is not None and not pe_result.empty:
            print(f"   ✅ PE 爬蟲成功: {len(pe_result)} 筆資料")
            pe_success = True
        else:
            print(f"   ⚠️ PE 爬蟲無資料 (可能是假日)")
            pe_success = False
    except Exception as e:
        print(f"   ❌ PE 爬蟲失敗: {str(e)[:50]}...")
        pe_success = False
    
    # 測試 Benchmark 爬蟲
    print(f"\n🧪 測試 Benchmark 爬蟲...")
    try:
        benchmark_result = crawl_benchmark(test_date)
        if benchmark_result is not None and not benchmark_result.empty:
            print(f"   ✅ Benchmark 爬蟲成功: {len(benchmark_result)} 筆資料")
            print(f"   欄位: {list(benchmark_result.columns)[:3]}...")
            benchmark_success = True
        else:
            print(f"   ⚠️ Benchmark 爬蟲無資料")
            benchmark_success = False
    except Exception as e:
        print(f"   ❌ Benchmark 爬蟲失敗: {str(e)[:50]}...")
        import traceback
        print(f"   詳細錯誤: {traceback.format_exc()}")
        benchmark_success = False
    
    # 分析結果
    print(f"\n📊 測試結果分析:")
    print(f"   PE 爬蟲: {'✅ 成功' if pe_success else '❌ 失敗'}")
    print(f"   Benchmark 爬蟲: {'✅ 成功' if benchmark_success else '❌ 失敗'}")
    
    if pe_success and not benchmark_success:
        print(f"\n💡 分析: PE 正常但 Benchmark 失敗")
        print(f"   • 網路連接正常 (PE 可以工作)")
        print(f"   • Benchmark API 可能有特定問題")
        print(f"   • 建議檢查 Benchmark API 端點")
    elif pe_success and benchmark_success:
        print(f"\n💡 分析: 兩個爬蟲都正常")
        print(f"   • 可以安全進行 Benchmark 更新")
    elif not pe_success and not benchmark_success:
        print(f"\n💡 分析: 兩個爬蟲都失敗")
        print(f"   • 可能是網路問題或假日")
    
    return pe_success, benchmark_success

def test_benchmark_small_update():
    """測試 benchmark 小批量更新"""
    print(f"\n🔄 測試 Benchmark 小批量更新")
    print("=" * 60)
    
    try:
        # 獲取日期範圍
        first_date, last_date = table_date_range('benchmark')
        print(f"📅 當前範圍: {first_date} 至 {last_date}")
        
        if last_date:
            # 計算需要更新的日期 (限制為5個)
            dates = date_range(last_date, datetime.datetime.now())
            
            if dates:
                # 過濾工作日並限制數量
                trading_dates = [d for d in dates if d.weekday() < 5]
                test_dates = trading_dates[-5:] if len(trading_dates) > 5 else trading_dates
                
                if test_dates:
                    print(f"📈 準備更新 {len(test_dates)} 個交易日")
                    print(f"   範圍: {test_dates[0]} 至 {test_dates[-1]}")
                    
                    # 確認
                    response = input(f"\n是否開始小批量更新？(y/N): ").strip().lower()
                    if response != 'y':
                        print("❌ 更新已取消")
                        return False
                    
                    # 執行更新
                    return execute_benchmark_update(test_dates)
                else:
                    print("✅ 沒有需要更新的交易日")
                    return True
            else:
                print("✅ 已是最新")
                return True
        else:
            print("❌ 無法獲取日期範圍")
            return False
            
    except Exception as e:
        print(f"❌ 更新準備失敗: {str(e)}")
        return False

def execute_benchmark_update(dates):
    """執行 benchmark 更新 - 使用改進的邏輯"""
    print(f"\n🚀 開始執行 Benchmark 更新")
    print(f"   日期數: {len(dates)}")
    
    # 創建備份
    backup_file = f"history/tables/benchmark_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
    original_file = "history/tables/benchmark.pkl"
    
    if os.path.exists(original_file):
        import shutil
        shutil.copy2(original_file, backup_file)
        print(f"✅ 備份創建: {backup_file}")
    
    df_list = []
    success_count = 0
    
    for i, date in enumerate(dates):
        try:
            print(f"📅 爬取 {date.strftime('%Y-%m-%d')}: {i+1}/{len(dates)} [{(i+1)/len(dates)*100:5.1f}%]", end="")
            
            # 調用爬蟲 - 使用改進的錯誤處理
            df = crawl_benchmark(date)
            
            if df is not None and not df.empty:
                df_list.append(df)
                success_count += 1
                print("  ✅")
                
                # 每3筆存檔一次 (更頻繁的存檔)
                if len(df_list) % 3 == 0:
                    print(f"  💾 中間存檔 ({len(df_list)} 筆)...")
                    temp_df = pd.concat(df_list, ignore_index=True)
                    to_pickle(temp_df, 'benchmark')
                    
            else:
                print("  ⚠️ (無資料，可能是假日)")
            
            # 使用與 auto_update.py 相同的延遲策略
            if i < len(dates) - 1:
                delay = random.uniform(5, 10)
                print(f"  ⏳ 等待 {delay:.1f} 秒...")
                time.sleep(delay)
                
        except Exception as e:
            print(f"  ❌ 錯誤: {str(e)[:50]}...")
            continue
    
    # 最終保存
    if df_list:
        print(f"\n💾 最終保存...")
        combined_df = pd.concat(df_list, ignore_index=True)
        to_pickle(combined_df, 'benchmark')
        print(f"✅ 成功: {success_count}/{len(dates)} 個日期，共 {len(combined_df)} 筆資料")
        return True
    else:
        print(f"\n❌ 沒有成功爬取任何資料")
        return False

def main():
    """主函數"""
    print("🔧 Benchmark 改進測試工具")
    print("=" * 60)
    print("🎯 目標: 使用 PE 爬蟲的成功經驗改進 Benchmark")
    print("💡 特色: 參考成功爬蟲 + 改進的錯誤處理")
    print("=" * 60)
    
    # 步驟1: 比較測試
    pe_ok, benchmark_ok = test_pe_vs_benchmark()
    
    if benchmark_ok:
        print(f"\n✅ Benchmark 爬蟲測試通過！")
        
        # 步驟2: 小批量更新測試
        update_ok = test_benchmark_small_update()
        
        if update_ok:
            print(f"\n🎉 Benchmark 更新測試成功！")
            print(f"💡 現在可以在 auto_update.py 中安全使用:")
            print(f"   ('benchmark', crawl_benchmark, date_range),")
        else:
            print(f"\n⚠️ 更新測試失敗")
    else:
        print(f"\n❌ Benchmark 爬蟲基本測試失敗")
        print(f"💡 建議:")
        if pe_ok:
            print(f"   • PE 正常工作，網路連接沒問題")
            print(f"   • Benchmark API 可能需要檢查")
        else:
            print(f"   • 兩個爬蟲都失敗，可能是網路或假日問題")

if __name__ == "__main__":
    main()
