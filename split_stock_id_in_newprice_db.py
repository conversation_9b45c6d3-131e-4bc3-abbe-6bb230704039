#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
將 newprice.db 中的 stock_id 欄位分開成 stock_id 和 stock_name 兩個欄位
"""

import sqlite3
import pandas as pd
import os
import shutil
import re

def split_stock_id_in_newprice_db():
    """將 stock_id 欄位分開成 stock_id 和 stock_name"""
    
    db_file = 'D:/Finlab/history/tables/newprice.db'
    backup_file = 'D:/Finlab/history/tables/newprice_split_backup.db'
    
    print("=" * 80)
    print("🔄 分離 newprice.db 中的 stock_id 和 stock_name")
    print("=" * 80)
    
    # 檢查檔案是否存在
    if not os.path.exists(db_file):
        print(f"❌ 檔案不存在: {db_file}")
        return False
    
    # 檢查原始檔案大小
    original_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
    print(f"📁 原始檔案: {db_file}")
    print(f"📊 原始檔案大小: {original_size:.2f} MB")
    
    try:
        # 創建備份
        print(f"\n💾 創建備份檔案...")
        shutil.copy2(db_file, backup_file)
        backup_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
        print(f"✅ 備份完成: {backup_file} ({backup_size:.2f} MB)")
        
        # 連接資料庫
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 檢查原始資料統計
        print(f"\n📊 原始資料統計:")
        cursor.execute("SELECT COUNT(*) FROM price")
        total_count = cursor.fetchone()[0]
        print(f"   總資料筆數: {total_count:,}")
        
        # 檢查 stock_id 格式範例
        print(f"\n📋 stock_id 格式範例:")
        cursor.execute("SELECT DISTINCT stock_id FROM price LIMIT 10")
        sample_ids = cursor.fetchall()
        for stock_id, in sample_ids:
            print(f"   {stock_id}")
        
        # 分析 stock_id 格式
        print(f"\n🔍 分析 stock_id 格式:")
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        unique_stocks = cursor.fetchone()[0]
        print(f"   不重複股票數: {unique_stocks:,}")
        
        # 檢查各種格式
        cursor.execute("SELECT stock_id FROM price WHERE stock_id LIKE '% %' LIMIT 5")
        space_format = cursor.fetchall()
        print(f"   包含空格的格式: {len(space_format)} 個範例")
        for stock_id, in space_format:
            print(f"     {stock_id}")
        
        # 創建新的表格結構
        print(f"\n🔧 創建新的表格結構...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS price_new (
                stock_id TEXT,
                stock_name TEXT,
                date TIMESTAMP,
                成交股數 TEXT,
                成交筆數 TEXT,
                成交金額 TEXT,
                收盤價 TEXT,
                開盤價 TEXT,
                最低價 TEXT,
                最高價 TEXT,
                最後揭示買價 TEXT,
                最後揭示賣價 TEXT
            )
        """)
        print(f"✅ 新表格結構已創建")
        
        # 分批處理資料
        batch_size = 50000
        processed = 0
        
        print(f"\n⏳ 開始分離 stock_id，批次大小: {batch_size:,}")
        
        # 獲取總筆數用於進度顯示
        cursor.execute("SELECT COUNT(*) FROM price")
        total_rows = cursor.fetchone()[0]
        
        offset = 0
        while True:
            # 讀取一批資料
            cursor.execute(f"""
                SELECT stock_id, date, 成交股數, 成交筆數, 成交金額, 收盤價, 開盤價, 最低價, 最高價, 最後揭示買價, 最後揭示賣價
                FROM price 
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            batch_data = cursor.fetchall()
            if not batch_data:
                break
            
            # 處理這批資料
            processed_batch = []
            for row in batch_data:
                original_stock_id = row[0]
                
                # 分離 stock_id 和 stock_name
                if ' ' in original_stock_id:
                    # 格式: "0050 元大台灣50"
                    parts = original_stock_id.split(' ', 1)  # 只分割第一個空格
                    stock_id = parts[0].strip()
                    stock_name = parts[1].strip() if len(parts) > 1 else ''
                else:
                    # 沒有空格，整個作為 stock_id
                    stock_id = original_stock_id.strip()
                    stock_name = ''
                
                # 構建新的行
                new_row = (stock_id, stock_name) + row[1:]  # stock_id, stock_name + 其他欄位
                processed_batch.append(new_row)
            
            # 插入處理後的資料
            cursor.executemany("""
                INSERT INTO price_new (stock_id, stock_name, date, 成交股數, 成交筆數, 成交金額, 收盤價, 開盤價, 最低價, 最高價, 最後揭示買價, 最後揭示賣價)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, processed_batch)
            
            processed += len(batch_data)
            progress = (processed / total_rows) * 100
            print(f"   處理進度: {processed:,}/{total_rows:,} ({progress:.1f}%)")
            
            offset += batch_size
        
        # 提交事務
        conn.commit()
        print(f"✅ 資料處理完成，共處理 {processed:,} 筆")
        
        # 驗證新表格
        print(f"\n🔍 驗證新表格:")
        cursor.execute("SELECT COUNT(*) FROM price_new")
        new_count = cursor.fetchone()[0]
        print(f"   新表格資料筆數: {new_count:,}")
        
        # 檢查分離結果
        cursor.execute("SELECT stock_id, stock_name FROM price_new LIMIT 10")
        sample_new = cursor.fetchall()
        print(f"   分離結果範例:")
        for stock_id, stock_name in sample_new:
            print(f"     stock_id: '{stock_id}', stock_name: '{stock_name}'")
        
        # 檢查不重複股票數
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price_new")
        new_unique_stocks = cursor.fetchone()[0]
        print(f"   新的不重複股票數: {new_unique_stocks:,}")
        
        # 檢查空的 stock_name
        cursor.execute("SELECT COUNT(*) FROM price_new WHERE stock_name = ''")
        empty_names = cursor.fetchone()[0]
        print(f"   空的 stock_name: {empty_names:,} 筆")
        
        # 替換舊表格
        print(f"\n🔄 替換舊表格...")
        cursor.execute("DROP TABLE price")
        cursor.execute("ALTER TABLE price_new RENAME TO price")
        print(f"✅ 表格替換完成")
        
        # 創建索引以提升查詢效能
        print(f"\n🔧 創建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_id ON price(stock_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_date ON price(date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_date ON price(stock_id, date)")
        print(f"✅ 索引創建完成")
        
        # 優化資料庫
        conn.close()
        print(f"\n🔧 優化資料庫...")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("VACUUM")
        print(f"✅ 資料庫優化完成")
        
        # 最終驗證
        print(f"\n📊 最終驗證:")
        cursor.execute("SELECT COUNT(*) FROM price")
        final_count = cursor.fetchone()[0]
        print(f"   最終資料筆數: {final_count:,}")
        
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM price")
        final_unique = cursor.fetchone()[0]
        print(f"   最終不重複股票數: {final_unique:,}")
        
        # 檢查表格結構
        cursor.execute("PRAGMA table_info(price)")
        columns = cursor.fetchall()
        print(f"   最終表格結構:")
        for col in columns:
            print(f"     {col[1]} ({col[2]})")
        
        # 檢查最終範例
        cursor.execute("SELECT stock_id, stock_name, date, 收盤價 FROM price LIMIT 5")
        final_sample = cursor.fetchall()
        print(f"   最終資料範例:")
        for stock_id, stock_name, date, close_price in final_sample:
            print(f"     {stock_id} | {stock_name} | {date[:10]} | {close_price}")
        
        conn.close()
        
        # 檢查最終檔案大小
        final_size = os.path.getsize(db_file) / (1024 * 1024)  # MB
        
        print(f"\n💾 檔案大小變化:")
        print(f"   原始大小: {original_size:.2f} MB")
        print(f"   最終大小: {final_size:.2f} MB")
        print(f"   大小變化: {final_size - original_size:.2f} MB")
        
        print(f"\n✅ stock_id 分離完成！")
        print(f"📁 主檔案: {db_file}")
        print(f"📁 備份檔案: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分離過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出錯，嘗試從備份恢復
        if os.path.exists(backup_file):
            print(f"\n🔄 嘗試從備份恢復...")
            try:
                shutil.copy2(backup_file, db_file)
                print(f"✅ 已從備份恢復")
            except Exception as restore_error:
                print(f"❌ 恢復失敗: {restore_error}")
        
        return False

if __name__ == "__main__":
    split_stock_id_in_newprice_db()
