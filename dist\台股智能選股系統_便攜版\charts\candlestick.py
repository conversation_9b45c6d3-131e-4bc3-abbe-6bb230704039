#!/usr/bin/env python3
"""
K線圖組件模組 - 修復版
修復了數據驗證和錯誤處理問題
"""
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging

# PyQt6 兼容性修復
try:
    from PyQt6.QtCore import QRectF, QPointF
    from PyQt6.QtGui import QPainter, QPicture, QPen, QBrush
    from PyQt6.QtCore import Qt
    PYQT_VERSION = 6
except ImportError:
    try:
        from PyQt5.QtCore import QRectF, QPointF, Qt
        from PyQt5.QtGui import QPainter, QPicture, QPen, QBrush
        PYQT_VERSION = 5
    except ImportError:
        raise ImportError("需要安裝 PyQt5 或 PyQt6")


class CandlestickItem(pg.GraphicsObject):
    """
    根據提供的數據繪製K線圖 - 修復版
    資料格式：DataFrame with columns [date, Open, High, Low, Close, Volume]
    """
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = QPicture()
        self.valid_data_count = 0
        self.skipped_data_count = 0
        self.generate_picture()

    def validate_ohlc_data(self, open_price, high_price, low_price, close_price, index):
        """驗證OHLC數據的有效性"""
        try:
            # 檢查NaN值
            if pd.isna(open_price) or pd.isna(high_price) or pd.isna(low_price) or pd.isna(close_price):
                logging.warning(f"K線數據包含NaN值 (索引: {index})")
                return False, "包含NaN值"
            
            # 檢查負數
            if open_price <= 0 or high_price <= 0 or low_price <= 0 or close_price <= 0:
                logging.warning(f"K線數據包含非正數 (索引: {index})")
                return False, "包含非正數"
            
            # 檢查邏輯關係
            if high_price < low_price:
                logging.warning(f"最高價({high_price}) < 最低價({low_price}) (索引: {index})")
                return False, "最高價小於最低價"
            
            if high_price < max(open_price, close_price):
                logging.warning(f"最高價({high_price}) < max(開盤價, 收盤價) (索引: {index})")
                return False, "最高價小於開盤或收盤價"
            
            if low_price > min(open_price, close_price):
                logging.warning(f"最低價({low_price}) > min(開盤價, 收盤價) (索引: {index})")
                return False, "最低價大於開盤或收盤價"
            
            return True, "數據有效"
            
        except Exception as e:
            logging.error(f"驗證OHLC數據時出錯 (索引: {index}): {e}")
            return False, f"驗證錯誤: {str(e)}"

    def generate_picture(self):
        """生成K線圖的繪圖指令 - 修復版"""
        if self.data is None or len(self.data) == 0:
            logging.warning("K線數據為空，無法繪製")
            return
        
        painter = QPainter(self.picture)
        
        try:
            # 設置畫筆 - 兼容 PyQt5/PyQt6
            try:
                painter.setPen(pg.mkPen('k'))  # 黑色邊框
            except:
                # 如果 pyqtgraph 的 mkPen 有問題，使用 Qt 原生方法
                if PYQT_VERSION == 6:
                    painter.setPen(QPen(Qt.GlobalColor.black))
                else:
                    painter.setPen(QPen(Qt.black))

            w = 0.6  # K線寬度
            self.valid_data_count = 0
            self.skipped_data_count = 0

            for i in range(len(self.data)):
                try:
                    # 獲取OHLC數據
                    open_price = float(self.data['Open'].iloc[i])
                    high_price = float(self.data['High'].iloc[i])
                    low_price = float(self.data['Low'].iloc[i])
                    close_price = float(self.data['Close'].iloc[i])
                    
                    # 驗證數據有效性
                    is_valid, reason = self.validate_ohlc_data(open_price, high_price, low_price, close_price, i)
                    
                    if not is_valid:
                        self.skipped_data_count += 1
                        logging.debug(f"跳過無效K線數據 (索引: {i}): {reason}")
                        continue
                    
                    # 台股的顏色習慣：紅色表示上漲，綠色表示下跌
                    if close_price > open_price:
                        # 上漲 - 紅色
                        try:
                            painter.setBrush(pg.mkBrush('r'))
                        except:
                            if PYQT_VERSION == 6:
                                painter.setBrush(QBrush(Qt.GlobalColor.red))
                            else:
                                painter.setBrush(QBrush(Qt.red))
                        
                        # 繪製實體（開盤價到收盤價）
                        rect = QRectF(i - w/2, open_price, w, close_price - open_price)
                        painter.drawRect(rect)
                        
                    else:
                        # 下跌 - 綠色
                        try:
                            painter.setBrush(pg.mkBrush('g'))
                        except:
                            if PYQT_VERSION == 6:
                                painter.setBrush(QBrush(Qt.GlobalColor.green))
                            else:
                                painter.setBrush(QBrush(Qt.green))
                        
                        # 繪製實體（收盤價到開盤價）
                        rect = QRectF(i - w/2, close_price, w, open_price - close_price)
                        painter.drawRect(rect)

                    # 繪製上下影線
                    painter.drawLine(QPointF(i, low_price), QPointF(i, high_price))
                    
                    self.valid_data_count += 1
                    
                except Exception as e:
                    self.skipped_data_count += 1
                    logging.error(f"繪製K線時出錯 (索引: {i}): {e}")
                    continue

            logging.info(f"K線繪製完成: 成功 {self.valid_data_count} 根，跳過 {self.skipped_data_count} 根")
            
        except Exception as e:
            logging.error(f"K線圖繪製過程出錯: {e}")
        finally:
            painter.end()

    def paint(self, painter, option, widget):
        """繪製K線圖"""
        try:
            painter.drawPicture(0, 0, self.picture)
        except Exception as e:
            logging.error(f"K線圖繪製到畫布時出錯: {e}")

    def boundingRect(self):
        """返回邊界矩形"""
        try:
            return QRectF(self.picture.boundingRect())
        except Exception as e:
            logging.error(f"獲取K線圖邊界時出錯: {e}")
            return QRectF(0, 0, 100, 100)  # 返回默認邊界
    
    def get_statistics(self):
        """獲取繪製統計信息"""
        return {
            'total_data': len(self.data) if self.data is not None else 0,
            'valid_candlesticks': self.valid_data_count,
            'skipped_candlesticks': self.skipped_data_count,
            'success_rate': (self.valid_data_count / len(self.data) * 100) if len(self.data) > 0 else 0
        }
