#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試策略交集分析修復
"""

import sys
import pandas as pd
from strategy_intersection_analyzer import StrategyIntersectionAnalyzer

def test_strategy_intersection_logic():
    """測試策略交集分析邏輯"""
    print("🔍 測試策略交集分析邏輯")
    print("="*50)
    
    # 創建分析器
    analyzer = StrategyIntersectionAnalyzer()
    
    # 模擬4個策略的結果
    strategy_results = {
        "勝率73.45%": pd.DataFrame({
            "股票代碼": ["2330", "2317", "2454", "1301", "2881"],
            "股票名稱": ["台積電", "鴻海", "聯發科", "台塑", "富邦金"],
            "評分": [85, 78, 82, 75, 80]
        }),
        "阿水一式": pd.DataFrame({
            "股票代碼": ["2330", "2317", "2382", "2303", "2412"],
            "股票名稱": ["台積電", "鴻海", "廣達", "聯電", "中華電"],
            "評分": [90, 85, 88, 82, 79]
        }),
        "機小型": pd.DataFrame({
            "股票代碼": ["2330", "2454", "2382", "3008", "2327"],
            "股票名稱": ["台積電", "聯發科", "廣達", "大立光", "國巨"],
            "評分": [92, 87, 85, 90, 83]
        }),
        "三檔+RSI策略": pd.DataFrame({
            "股票代碼": ["2317", "2454", "2382", "2303", "2327"],
            "股票名稱": ["鴻海", "聯發科", "廣達", "聯電", "國巨"],
            "評分": [88, 91, 86, 84, 87]
        })
    }
    
    print(f"📊 模擬策略結果:")
    for strategy, df in strategy_results.items():
        print(f"  {strategy}: {len(df)} 支股票")
        print(f"    股票: {', '.join(df['股票代碼'].tolist())}")
    
    # 添加策略結果到分析器
    for strategy, df in strategy_results.items():
        analyzer.add_strategy_result(strategy, df, "股票代碼")
    
    # 測試不同組合的交集
    test_combinations = [
        ["勝率73.45%", "阿水一式"],
        ["勝率73.45%", "機小型"],
        ["勝率73.45%", "阿水一式", "機小型"],
        ["勝率73.45%", "阿水一式", "機小型", "三檔+RSI策略"]
    ]
    
    print(f"\n🎯 交集分析結果:")
    for combo in test_combinations:
        try:
            result = analyzer.calculate_intersection(combo)
            intersection_stocks = result.get('intersection_stocks', set())
            print(f"  {' + '.join(combo)}: {len(intersection_stocks)} 支共同股票")
            if intersection_stocks:
                print(f"    共同股票: {', '.join(sorted(intersection_stocks))}")
            else:
                print(f"    沒有共同股票")
        except Exception as e:
            print(f"    錯誤: {e}")
    
    # 測試所有組合分析
    print(f"\n🔍 所有組合分析:")
    try:
        all_results = analyzer.analyze_all_combinations()
        top_combinations = analyzer.get_top_intersection_combinations()
        
        print(f"  找到 {len(top_combinations)} 個有效組合:")
        for i, (combo_name, count) in enumerate(top_combinations[:10], 1):
            print(f"    {i:2d}. {combo_name}: {count} 支共同股票")
            
    except Exception as e:
        print(f"  分析失敗: {e}")

def test_selected_strategies_logic():
    """測試選中策略邏輯"""
    print(f"\n🎯 測試選中策略邏輯")
    print("="*30)
    
    # 模擬勾選4個策略的情況
    selected_strategies = ["勝率73.45%", "阿水一式", "機小型", "三檔+RSI策略"]
    
    print(f"✅ 選中策略數量: {len(selected_strategies)}")
    print(f"   策略清單: {selected_strategies}")
    
    # 檢查是否滿足最少2個策略的要求
    if len(selected_strategies) >= 2:
        print(f"✅ 滿足最少2個策略的要求")
    else:
        print(f"❌ 不滿足最少2個策略的要求")
    
    # 模擬策略結果緩存
    strategy_results_cache = {
        "勝率73.45%": pd.DataFrame({"股票代碼": ["2330", "2317"]}),
        "阿水一式": pd.DataFrame({"股票代碼": ["2330", "2382"]}),
        "機小型": pd.DataFrame({"股票代碼": ["2454", "3008"]}),
        "三檔+RSI策略": pd.DataFrame({"股票代碼": ["2317", "2382"]})
    }
    
    # 檢查缺失的策略
    missing_strategies = []
    for strategy in selected_strategies:
        if strategy not in strategy_results_cache:
            missing_strategies.append(strategy)
    
    if missing_strategies:
        print(f"⚠️  缺失策略: {missing_strategies}")
    else:
        print(f"✅ 所有選中策略都有結果")
    
    return len(selected_strategies) >= 2 and not missing_strategies

def main():
    """主函數"""
    try:
        test_strategy_intersection_logic()
        logic_ok = test_selected_strategies_logic()
        
        if logic_ok:
            print(f"\n🎉 策略交集分析邏輯測試通過！")
        else:
            print(f"\n❌ 策略交集分析邏輯測試失敗！")
        
        return 0 if logic_ok else 1
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
