#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查資料庫實際狀況
"""

import sqlite3

def check_database_status():
    """檢查資料庫狀況"""
    
    print("=" * 60)
    print("📊 檢查 newprice.db 實際狀況")
    print("=" * 60)
    
    newprice_db = 'D:/Finlab/history/tables/newprice.db'
    
    try:
        conn = sqlite3.connect(newprice_db)
        cursor = conn.cursor()
        
        # 檢查最近的資料
        print("📅 最近 10 天的資料:")
        cursor.execute('''
            SELECT date, COUNT(*) as records, COUNT(DISTINCT stock_id) as stocks
            FROM stock_daily_data 
            GROUP BY date 
            ORDER BY date DESC 
            LIMIT 10
        ''')
        recent_data = cursor.fetchall()
        
        for date, records, stocks in recent_data:
            print(f"   {date}: {records:,} 筆記錄, {stocks:,} 檔股票")
        
        # 檢查測試期間的詳細資料
        print(f"\n📊 測試期間詳細資料 (2022-09-02 之後):")
        cursor.execute('''
            SELECT date, COUNT(*) as records, COUNT(DISTINCT stock_id) as stocks,
                   MIN(stock_id) as min_stock, MAX(stock_id) as max_stock
            FROM stock_daily_data 
            WHERE date >= "2022-09-02"
            GROUP BY date 
            ORDER BY date
        ''')
        test_data = cursor.fetchall()
        
        for date, records, stocks, min_stock, max_stock in test_data:
            print(f"   {date}: {records:,} 筆, {stocks:,} 檔 (代碼範圍: {min_stock}-{max_stock})")
        
        # 檢查是否有重複資料
        print(f"\n🔍 檢查重複資料:")
        cursor.execute('''
            SELECT date, stock_id, COUNT(*) as count
            FROM stock_daily_data
            WHERE date >= "2022-09-02"
            GROUP BY date, stock_id
            HAVING COUNT(*) > 1
            LIMIT 10
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   ⚠️ 發現重複資料:")
            for date, stock_id, count in duplicates:
                print(f"     {date} {stock_id}: {count} 筆")
        else:
            print(f"   ✅ 無重複資料")
        
        # 檢查權證資料
        print(f"\n🔍 檢查權證資料:")
        cursor.execute('''
            SELECT date, COUNT(*) as warrant_count
            FROM stock_daily_data 
            WHERE date >= "2022-09-02" AND stock_id LIKE '7%'
            GROUP BY date
            ORDER BY date
        ''')
        warrants = cursor.fetchall()
        
        if warrants:
            print(f"   ⚠️ 發現權證資料:")
            for date, count in warrants:
                print(f"     {date}: {count} 筆權證")
        else:
            print(f"   ✅ 無權證資料")
        
        # 檢查股票資訊完整性
        print(f"\n📊 股票資訊完整性 (2022-09-02 之後):")
        cursor.execute('''
            SELECT date,
                   COUNT(*) as total,
                   SUM(CASE WHEN stock_name != '' THEN 1 ELSE 0 END) as with_name,
                   SUM(CASE WHEN listing_status != '' THEN 1 ELSE 0 END) as with_status
            FROM stock_daily_data 
            WHERE date >= "2022-09-02"
            GROUP BY date
            ORDER BY date
        ''')
        info_data = cursor.fetchall()
        
        for date, total, with_name, with_status in info_data:
            name_pct = with_name/total*100 if total > 0 else 0
            status_pct = with_status/total*100 if total > 0 else 0
            print(f"   {date}: 名稱 {with_name:,}/{total:,} ({name_pct:.1f}%), 狀態 {with_status:,}/{total:,} ({status_pct:.1f}%)")
        
        # 檢查範例資料
        print(f"\n📋 最新一天的範例資料:")
        cursor.execute('''
            SELECT stock_id, stock_name, listing_status, industry, [Close]
            FROM stock_daily_data 
            WHERE date = (SELECT MAX(date) FROM stock_daily_data WHERE date >= "2022-09-02")
            ORDER BY stock_id
            LIMIT 10
        ''')
        sample_data = cursor.fetchall()
        
        for stock_id, name, status, industry, close in sample_data:
            print(f"   {stock_id} {name} | {status} | {industry} | 收盤:{close}")
        
        conn.close()
        
        print(f"\n✅ 資料庫狀況檢查完成")
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_status()
