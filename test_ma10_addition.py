#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試雙週線MA10添加功能
"""

def test_ma10_addition():
    """測試MA10雙週線是否正確添加到K線圖中"""
    print("🔍 檢查雙週線MA10添加情況...")
    
    with open('O3mh_gui_v21_optimized.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查1: K線圖顯示中是否包含MA10
    ma_display_found = False
    if "(10, 'orange', '雙週線MA10')" in content:
        ma_display_found = True
        print("✅ K線圖顯示中已包含雙週線MA10")
    else:
        print("❌ K線圖顯示中未找到雙週線MA10")
    
    # 檢查2: 技術指標計算中是否包含MA10
    ma_calculation_found = False
    if "for period in [5, 10, 20, 60, 120, 240]:" in content:
        ma_calculation_found = True
        print("✅ 技術指標計算中已包含MA10")
    else:
        print("❌ 技術指標計算中未找到MA10")
    
    # 檢查3: 確認MA10在移動平均線列表中的位置正確
    ma_order_correct = False
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if "(5, 'w', '週線MA5')," in line:
            if i + 1 < len(lines) and "(10, 'orange', '雙週線MA10')," in lines[i + 1]:
                if i + 2 < len(lines) and "(20, 'y', '月線MA20')," in lines[i + 2]:
                    ma_order_correct = True
                    print("✅ 雙週線MA10位置正確（在MA5和MA20之間）")
                    break
    
    if not ma_order_correct:
        print("❌ 雙週線MA10位置不正確")
    
    # 總結
    if ma_display_found and ma_calculation_found and ma_order_correct:
        print("\n🎉 雙週線MA10添加成功！")
        print("\n📊 現在K線圖將顯示以下移動平均線：")
        print("   • MA5  (週線) - 白色")
        print("   • MA10 (雙週線) - 橙色 ⭐ 新增")
        print("   • MA20 (月線) - 黃色")
        print("   • MA60 (季線) - 青色")
        print("   • MA120 (半年線) - 洋紅色")
        print("   • MA240 (年線) - 綠色")
        print("\n🎯 雙週線MA10的作用：")
        print("   • 提供比週線更穩定的短期趨勢判斷")
        print("   • 在MA5和MA20之間提供中間參考線")
        print("   • 適合短中期交易策略的技術分析")
        print("   • 可作為支撐阻力位的重要參考")
        return True
    else:
        print("\n❌ 雙週線MA10添加不完整，需要檢查")
        return False

def create_ma10_usage_guide():
    """創建MA10使用指南"""
    print("\n📝 創建雙週線MA10使用指南...")
    
    guide_content = """# 🔍 雙週線MA10使用指南

## 📊 什麼是雙週線MA10？

雙週線MA10是10日移動平均線，代表過去10個交易日的平均價格。它介於週線MA5和月線MA20之間，提供了一個中間的趨勢參考。

## 🎯 MA10的技術分析意義

### 📈 趨勢判斷
- **股價在MA10之上**: 短期趨勢偏多
- **股價在MA10之下**: 短期趨勢偏空
- **MA10向上**: 短期動能向上
- **MA10向下**: 短期動能向下

### 🔄 支撐阻力
- **MA10作為支撐**: 股價回檔至MA10附近獲得支撐
- **MA10作為阻力**: 股價反彈至MA10附近遇到阻力
- **突破MA10**: 短期趨勢可能發生轉變

### 📊 與其他均線的配合
- **MA5 > MA10 > MA20**: 多頭排列，趨勢向上
- **MA5 < MA10 < MA20**: 空頭排列，趨勢向下
- **MA10與MA20交叉**: 中短期趨勢轉換信號

## 🎯 實戰應用策略

### 🚀 買入信號
1. **黃金交叉**: MA5向上突破MA10
2. **回檔支撐**: 股價回檔至MA10獲得支撐
3. **多頭排列**: MA5 > MA10 > MA20 > MA60

### 📉 賣出信號
1. **死亡交叉**: MA5向下跌破MA10
2. **反彈阻力**: 股價反彈至MA10遇到阻力
3. **空頭排列**: MA5 < MA10 < MA20 < MA60

### ⚖️ 風險控制
- **停損點**: 跌破MA10可考慮停損
- **加碼點**: 站穩MA10之上可考慮加碼
- **觀望區**: MA10附近震盪時保持觀望

## 📋 使用注意事項

### ✅ 適用情況
- 短中期交易策略
- 趨勢跟隨策略
- 波段操作策略

### ⚠️ 注意事項
- 在盤整市場中可能產生假信號
- 需要配合成交量分析
- 建議與其他技術指標結合使用

### 🎯 最佳實踐
1. **多時間框架分析**: 結合日線、週線、月線
2. **量價配合**: 注意成交量的配合
3. **基本面支撐**: 結合公司基本面分析
4. **風險管理**: 設定合理的停損停利點

## 🔧 在系統中的使用

### 📊 K線圖顯示
- 顏色: 橙色
- 位置: 在MA5和MA20之間
- 計算: 10日收盤價移動平均

### 🎯 策略應用
- 可用於各種技術分析策略
- 支援自動信號識別
- 整合在完整的技術分析體系中

---

**雙週線MA10為您的技術分析提供了更精確的短期趨勢判斷工具！** 📈✨
"""
    
    with open('MA10雙週線使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 已創建 MA10雙週線使用指南.md")

def main():
    """主函數"""
    print("🔍 台股智能選股系統 - 雙週線MA10功能測試")
    print("=" * 60)
    
    # 測試MA10添加情況
    success = test_ma10_addition()
    
    if success:
        # 創建使用指南
        create_ma10_usage_guide()
        
        print("\n🎉 雙週線MA10功能添加完成！")
        print("\n📋 功能特點：")
        print("   ✅ 在K線圖中顯示橙色的MA10線")
        print("   ✅ 位置在MA5和MA20之間")
        print("   ✅ 提供短期趨勢判斷參考")
        print("   ✅ 可作為支撐阻力位參考")
        
        print("\n🚀 使用方法：")
        print("   1. 啟動台股智能選股系統")
        print("   2. 選擇任意股票查看K線圖")
        print("   3. 觀察橙色的MA10雙週線")
        print("   4. 結合其他均線進行技術分析")
        
        print("\n📖 詳細使用說明請參考：MA10雙週線使用指南.md")
        
    else:
        print("\n❌ 雙週線MA10功能添加不完整")
        print("請檢查代碼修改是否正確")

if __name__ == "__main__":
    main()
