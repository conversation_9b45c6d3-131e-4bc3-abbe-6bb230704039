#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完善 cash_flows.db 的股票名稱，參考 price 爬蟲的方法獲取完整的股票資訊
"""

import sqlite3
import pandas as pd
import requests
import time
import re
from io import StringIO

def get_stock_info_from_twse_otc():
    """從台灣證交所和櫃買中心獲取完整的股票資訊"""
    
    print("🔍 從台灣證交所和櫃買中心獲取股票資訊...")
    
    stock_info = {}
    
    # 1. 獲取上市股票資訊
    print("📊 獲取上市股票資訊...")
    try:
        for attempt in range(3):
            try:
                url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=2"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                response.encoding = 'big5'

                if response.status_code == 200:
                    # 使用正則表達式解析 HTML 中的股票資訊
                    pattern = r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上市</td><td[^>]*>([^<]*)</td>'
                    matches = re.findall(pattern, response.text)

                    for stock_code, stock_name, industry in matches:
                        if stock_code.isdigit():
                            stock_info[stock_code] = {
                                'stock_name': stock_name.strip(),
                                'listing_status': '上市',
                                'industry': industry.strip() if industry.strip() else 'ETF'
                            }
                    print(f"✅ 上市股票: {len([k for k, v in stock_info.items() if v['listing_status'] == '上市'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上市股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)
    except Exception as e:
        print(f"❌ 上市股票獲取失敗: {e}")

    # 2. 獲取上櫃股票資訊
    print("📊 獲取上櫃股票資訊...")
    try:
        for attempt in range(3):
            try:
                url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=4"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                response.encoding = 'big5'

                if response.status_code == 200:
                    # 使用正則表達式解析 HTML 中的股票資訊
                    pattern = r'<td bgcolor=#FAFAD2>(\d{4})　([^<]+)</td><td[^>]*>[^<]*</td><td[^>]*>[^<]*</td><td[^>]*>上櫃</td><td[^>]*>([^<]*)</td>'
                    matches = re.findall(pattern, response.text)

                    for stock_code, stock_name, industry in matches:
                        if stock_code.isdigit():
                            stock_info[stock_code] = {
                                'stock_name': stock_name.strip(),
                                'listing_status': '上櫃',
                                'industry': industry.strip() if industry.strip() else 'ETF'
                            }
                    print(f"✅ 上櫃股票: {len([k for k, v in stock_info.items() if v['listing_status'] == '上櫃'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 上櫃股票獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)
    except Exception as e:
        print(f"❌ 上櫃股票獲取失敗: {e}")

    # 3. 獲取 ETF 資訊
    print("📊 獲取 ETF 資訊...")
    try:
        for attempt in range(3):
            try:
                url = "https://isin.twse.com.tw/isin/C_public.jsp?strMode=5"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                response.encoding = 'big5'

                if response.status_code == 200:
                    # ETF 的格式可能不同，使用更寬鬆的正則表達式
                    pattern = r'<td bgcolor=#FAFAD2>(\d{5})　([^<]+)</td>'
                    matches = re.findall(pattern, response.text)

                    for stock_code, stock_name in matches:
                        if stock_code.startswith('00'):  # ETF 通常以 00 開頭
                            stock_info[stock_code] = {
                                'stock_name': stock_name.strip(),
                                'listing_status': 'ETF',
                                'industry': 'ETF'
                            }
                    print(f"✅ ETF: {len([k for k, v in stock_info.items() if v['listing_status'] == 'ETF'])} 檔")
                    break

            except requests.exceptions.RequestException as e:
                print(f"⚠️ ETF 獲取失敗 (嘗試 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(10)
    except Exception as e:
        print(f"❌ ETF 獲取失敗: {e}")

    print(f"📊 總共獲取 {len(stock_info)} 檔股票資訊")
    return stock_info

def get_existing_stock_names():
    """從 newprice.db 獲取現有的股票名稱"""
    
    print("📖 從 newprice.db 獲取現有股票名稱...")
    
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    try:
        conn = sqlite3.connect(newprice_db)
        query = """
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name IS NOT NULL AND stock_name != ''
        ORDER BY stock_id
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        # 轉換為字典
        existing_names = dict(zip(df['stock_id'], df['stock_name']))
        print(f"✅ 從 newprice.db 獲取 {len(existing_names)} 個股票名稱")
        
        return existing_names
        
    except Exception as e:
        print(f"❌ 從 newprice.db 獲取股票名稱失敗: {e}")
        return {}

def complete_cash_flows_stock_names():
    """完善 cash_flows.db 的股票名稱"""
    
    print("=" * 80)
    print("🔄 完善 cash_flows.db 的股票名稱")
    print("=" * 80)
    
    cash_flows_db = r'D:\Finlab\history\tables\cash_flows.db'
    
    try:
        # 1. 獲取現有的股票名稱
        existing_names = get_existing_stock_names()
        
        # 2. 從官方網站獲取完整的股票資訊
        official_stock_info = get_stock_info_from_twse_otc()
        
        # 3. 合併股票名稱資訊（優先使用官方資訊）
        combined_names = {}
        
        # 先加入現有的名稱
        combined_names.update(existing_names)
        
        # 再加入官方資訊（會覆蓋現有的）
        for stock_id, info in official_stock_info.items():
            combined_names[stock_id] = info['stock_name']
        
        print(f"📊 合併後總共有 {len(combined_names)} 個股票名稱")
        
        # 4. 檢查 cash_flows.db 中需要更新的股票
        conn = sqlite3.connect(cash_flows_db)
        cursor = conn.cursor()
        
        # 獲取所有股票代碼
        cursor.execute("SELECT DISTINCT stock_id FROM cash_flows ORDER BY stock_id")
        cash_flows_stocks = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 cash_flows.db 中有 {len(cash_flows_stocks)} 個唯一股票")
        
        # 檢查匹配情況
        matched_stocks = []
        unmatched_stocks = []
        
        for stock_id in cash_flows_stocks:
            if stock_id in combined_names:
                matched_stocks.append(stock_id)
            else:
                unmatched_stocks.append(stock_id)
        
        print(f"📊 可以匹配的股票: {len(matched_stocks)} 個")
        print(f"📊 無法匹配的股票: {len(unmatched_stocks)} 個")
        
        if unmatched_stocks:
            print(f"⚠️  無法匹配的股票範例: {unmatched_stocks[:20]}")
        
        # 5. 更新股票名稱
        print(f"\n🔄 開始更新股票名稱...")
        
        update_count = 0
        for stock_id, stock_name in combined_names.items():
            if stock_id in cash_flows_stocks:
                cursor.execute("""
                UPDATE cash_flows 
                SET stock_name = ? 
                WHERE stock_id = ?
                """, (stock_name, stock_id))
                
                update_count += cursor.rowcount
        
        conn.commit()
        
        print(f"✅ 成功更新 {update_count} 筆記錄的股票名稱")
        
        # 6. 驗證結果
        print(f"\n🔍 驗證更新結果...")
        
        cursor.execute("""
        SELECT COUNT(*) as total_records,
               COUNT(stock_name) as records_with_name,
               COUNT(DISTINCT stock_id) as unique_stocks,
               COUNT(DISTINCT stock_name) as unique_names
        FROM cash_flows
        """)
        
        stats = cursor.fetchone()
        print(f"📊 總記錄數: {stats[0]:,}")
        print(f"📊 有股票名稱的記錄數: {stats[1]:,}")
        print(f"📊 唯一股票代碼數: {stats[2]:,}")
        print(f"📊 唯一股票名稱數: {stats[3]:,}")
        
        # 檢查仍然為空的股票名稱
        cursor.execute("""
        SELECT DISTINCT stock_id 
        FROM cash_flows 
        WHERE stock_name IS NULL OR stock_name = ''
        ORDER BY stock_id
        """)
        
        null_names = [row[0] for row in cursor.fetchall()]
        if null_names:
            print(f"⚠️  仍然沒有名稱的股票 ({len(null_names)} 個): {null_names[:20]}")
        
        # 顯示範例資料
        cursor.execute("""
        SELECT stock_id, stock_name, date 
        FROM cash_flows 
        WHERE stock_name IS NOT NULL 
        ORDER BY stock_id, date 
        LIMIT 10
        """)
        
        sample_data = cursor.fetchall()
        print(f"\n📊 更新後的範例資料:")
        for stock_id, stock_name, date in sample_data:
            print(f"   {stock_id} | {stock_name} | {date}")
        
        conn.close()
        
        print(f"\n✅ 股票名稱完善作業完成！")
        return True
        
    except Exception as e:
        print(f"❌ 完善股票名稱失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = complete_cash_flows_stock_names()
    
    if success:
        print(f"\n🎉 cash_flows.db 股票名稱已完善")
    else:
        print(f"\n❌ 股票名稱完善失敗")
