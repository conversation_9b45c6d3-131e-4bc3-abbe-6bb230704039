#!/usr/bin/env python3
"""
當日5分鐘數據獲取模組
提供台股盤中5分鐘K線數據
集成智能Yahoo獲取器，優化盤中數據獲取
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
from datetime import datetime, timedelta
import json
import re
import random
from bs4 import BeautifulSoup

# 導入智能Yahoo獲取器
try:
    from smart_yahoo_fetcher import SmartYahooFetcher
    SMART_YAHOO_AVAILABLE = True
    logging.info("✅ 智能Yahoo獲取器已載入到盤中數據獲取器")
except ImportError:
    SMART_YAHOO_AVAILABLE = False
    logging.warning("⚠️ 智能Yahoo獲取器不可用")

class IntradayDataFetcher:
    """當日5分鐘數據獲取器"""
    
    def __init__(self):
        self.session = requests.Session()

        # 🛡️ 反爬蟲機制：多個User-Agent輪換
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        # 🛡️ 基礎請求標頭（參考Goodinfo反爬蟲機制）
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://tw.stock.yahoo.com/'
        }

        # 🛡️ 請求頻率控制
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 最小請求間隔1秒
        self.failed_stocks = set()  # 記錄失敗的股票

        # 禁用SSL驗證以解決證書問題
        self.session.verify = False
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 初始化智能Yahoo獲取器
        if SMART_YAHOO_AVAILABLE:
            self.smart_yahoo = SmartYahooFetcher()
            logging.info("✅ 智能Yahoo獲取器已初始化（盤中）")
        else:
            self.smart_yahoo = None

        # 初始化 Twelve Data
        try:
            from twelvedata_fetcher import TwelveDataFetcher
            self.twelve_data = TwelveDataFetcher()
            logging.info("✅ Twelve Data API 已初始化")
        except ImportError:
            self.twelve_data = None
            logging.warning("⚠️ Twelve Data 模組不可用")

        # 數據源配置
        self.data_sources = {
            'yahoo_tw': {
                'url': 'https://tw.stock.yahoo.com/_td-stock/api/resource/StockServices.chart',
                'params_template': {
                    'symbol': '{stock_id}.TW',
                    'period': '1d',
                    'interval': '5m'
                }
            },
            'investing': {
                'url': 'https://tvc4.investing.com/api/v1/history',
                'headers': {
                    'Origin': 'https://tw.investing.com',
                    'Referer': 'https://tw.investing.com/'
                }
            },
            'twse_realtime': {
                'url': 'https://mis.twse.com.tw/stock/api/getStockInfo.jsp',
                'params_template': {
                    'ex_ch': 'tse_{stock_id}.tw',
                    'json': '1',
                    'delay': '0'
                }
            }
        }
        
        # 緩存設置
        self.cache = {}
        self.cache_timeout = 60  # 1分鐘緩存

    def get_random_headers(self):
        """🛡️ 獲取隨機請求標頭（反爬蟲機制）"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = self.user_agents[np.random.randint(0, len(self.user_agents))]
        return headers

    def make_safe_request(self, url, method='GET', **kwargs):
        """🛡️ 安全請求方法，包含完整反爬蟲機制"""
        # 檢查請求頻率
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            logging.debug(f"🛡️ 請求頻率控制，等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)

        # 隨機延遲（1-3秒）
        random_delay = random.uniform(1, 3)
        logging.debug(f"🛡️ 隨機延遲 {random_delay:.2f} 秒")
        time.sleep(random_delay)

        # 獲取隨機標頭
        headers = self.get_random_headers()

        try:
            if method.upper() == 'POST':
                response = self.session.post(url, headers=headers, timeout=15, **kwargs)
            else:
                response = self.session.get(url, headers=headers, timeout=15, **kwargs)

            # 更新最後請求時間
            self.last_request_time = time.time()

            # 設定正確的編碼
            if 'charset' in response.headers.get('content-type', ''):
                response.encoding = response.apparent_encoding
            else:
                response.encoding = 'utf-8'

            return response

        except Exception as e:
            logging.error(f"❌ 安全請求失敗: {e}")
            return None
        
    def get_intraday_data(self, stock_id, interval='5m'):
        """
        獲取當日盤中數據
        
        Args:
            stock_id: 股票代碼
            interval: 時間間隔 ('1m', '5m', '15m', '30m', '1h')
        
        Returns:
            pandas.DataFrame: 盤中數據
        """
        # 檢查緩存
        cache_key = f"{stock_id}_{interval}"
        if cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_timeout:
                logging.info(f"📦 使用緩存的盤中數據: {stock_id}")
                return data
        
        # 格式化股票代碼
        formatted_id = self._format_stock_id(stock_id)
        
        # 嘗試多個數據源 - Yahoo爬蟲優先（無API限制）
        for source_name in ['yahoo_crawler_realtime', 'smart_yahoo_intraday', 'twelvedata_intraday', 'twse_realtime', 'yahoo_tw', 'investing']:
            try:
                logging.info(f"🔄 嘗試 {source_name} 獲取 {stock_id} 盤中數據")
                data = self._fetch_from_source(formatted_id, interval, source_name)

                if not data.empty:
                    logging.info(f"✅ 成功從 {source_name} 獲取 {stock_id} 盤中數據 ({len(data)} 筆)")
                    # 緩存數據
                    self.cache[cache_key] = (time.time(), data)
                    return data

            except Exception as e:
                logging.warning(f"⚠️ {source_name} 獲取失敗: {e}")
                continue
        
        logging.error(f"❌ 所有數據源都無法獲取 {stock_id} 的盤中數據")
        return pd.DataFrame()
    
    def _format_stock_id(self, stock_id):
        """格式化股票代碼"""
        clean_id = str(stock_id).lstrip('0')
        if not clean_id:
            clean_id = '0'
        
        return {
            'original': stock_id,
            'clean': clean_id,
            'padded': f"{int(clean_id):04d}",
            'yahoo_format': f"{clean_id}.TW"
        }
    
    def _fetch_from_source(self, formatted_id, interval, source_name):
        """從指定數據源獲取數據"""
        if source_name == 'yahoo_crawler_realtime':
            return self._fetch_yahoo_crawler_realtime(formatted_id, interval)
        elif source_name == 'smart_yahoo_intraday':
            return self._fetch_smart_yahoo_intraday(formatted_id, interval)
        elif source_name == 'yfinance_intraday':
            return self._fetch_yfinance_intraday(formatted_id, interval)
        elif source_name == 'twelvedata_intraday':
            return self._fetch_twelvedata_intraday(formatted_id, interval)
        elif source_name == 'yahoo_tw':
            return self._fetch_yahoo_intraday(formatted_id, interval)
        elif source_name == 'twse_realtime':
            return self._fetch_twse_realtime(formatted_id, interval)
        elif source_name == 'investing':
            return self._fetch_investing_intraday(formatted_id, interval)
        else:
            raise ValueError(f"未知的數據源: {source_name}")

    def _fetch_smart_yahoo_intraday(self, formatted_id, interval):
        """使用智能Yahoo獲取器獲取盤中數據"""
        try:
            if not self.smart_yahoo:
                logging.warning("⚠️ 智能Yahoo獲取器未初始化")
                return pd.DataFrame()

            stock_id = formatted_id['clean']

            # 使用智能Yahoo獲取器獲取盤中數據
            df = self.smart_yahoo.get_intraday_data(stock_id, interval)

            if not df.empty:
                logging.info(f"✅ 智能Yahoo成功獲取 {stock_id} 盤中數據 ({len(df)} 筆)")
                return df
            else:
                logging.warning(f"⚠️ 智能Yahoo無法獲取 {stock_id} 盤中數據")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"❌ 智能Yahoo盤中數據獲取失敗 {formatted_id['clean']}: {e}")
            return pd.DataFrame()

    def _fetch_yfinance_intraday(self, formatted_id, interval):
        """使用yfinance獲取盤中數據 - 優化版本，添加錯誤過濾"""
        try:
            import yfinance as yf

            # 轉換時間間隔格式
            interval_map = {
                '1m': '1m',
                '5m': '5m',
                '15m': '15m',
                '30m': '30m',
                '1h': '1h'
            }

            yf_interval = interval_map.get(interval, '5m')
            stock_id = formatted_id['clean']

            # 智能格式選擇 - 根據股票代碼判斷市場
            if stock_id.startswith(('6', '8', '9')):
                # 櫃買市場優先
                suffixes = ['.TWO', '.TW']
            else:
                # 上市市場優先
                suffixes = ['.TW', '.TWO']

            # 錯誤過濾模式
            ignore_errors = [
                "possibly delisted",
                "No data found, symbol may be delisted",
                "HTTP Error 404"
            ]

            # 嘗試不同的股票代碼格式
            for suffix in suffixes:
                try:
                    ticker = formatted_id['clean'] + suffix
                    stock = yf.Ticker(ticker)

                    # 獲取當日數據
                    data = stock.history(period='1d', interval=yf_interval)

                    if not data.empty:
                        # 轉換格式
                        df = pd.DataFrame({
                            'datetime': data.index,
                            'open': data['Open'],
                            'high': data['High'],
                            'low': data['Low'],
                            'close': data['Close'],
                            'volume': data['Volume']
                        })

                        df = df.reset_index(drop=True)
                        df = df.dropna()  # 移除空值

                        if not df.empty:
                            logging.info(f"✅ yfinance 獲取到 {ticker} 盤中數據 ({len(df)} 筆)")
                            return df

                except Exception as e:
                    error_msg = str(e).lower()

                    # 檢查是否為可忽略的錯誤
                    should_ignore = any(pattern.lower() in error_msg for pattern in ignore_errors)

                    if should_ignore:
                        logging.debug(f"忽略yfinance盤中錯誤 {ticker}: {e}")
                    else:
                        logging.warning(f"yfinance {ticker} 盤中錯誤: {e}")
                    continue

            logging.warning(f"⚠️ yfinance 無法獲取 {stock_id} 盤中數據")
            return pd.DataFrame()

        except ImportError:
            logging.warning("yfinance 模組未安裝")
            return pd.DataFrame()
        except Exception as e:
            logging.error(f"yfinance盤中數據獲取失敗: {e}")
            return pd.DataFrame()

    def _fetch_twelvedata_intraday(self, formatted_id, interval):
        """使用 Twelve Data API 獲取盤中數據"""
        try:
            if not self.twelve_data:
                logging.warning("⚠️ Twelve Data 未初始化")
                return pd.DataFrame()

            stock_id = formatted_id['clean']

            # 檢查是否為台股（Twelve Data 免費版不支援台股）
            if self._is_taiwan_stock(stock_id):
                logging.debug(f"跳過台股 {stock_id}，Twelve Data 免費版不支援")
                return pd.DataFrame()

            # 轉換時間間隔格式
            interval_map = {
                '1m': '1min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '1h'
            }

            td_interval = interval_map.get(interval, '5min')

            # 獲取盤中數據
            df = self.twelve_data.get_intraday_data(stock_id, td_interval)

            if not df.empty:
                # 轉換為標準格式
                df = df.rename(columns={
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                })

                # 確保有datetime欄位
                if 'datetime' not in df.columns and 'date' in df.columns:
                    df['datetime'] = pd.to_datetime(df['date'])

                logging.info(f"✅ Twelve Data 成功獲取 {stock_id} 盤中數據 ({len(df)} 筆)")
                return df
            else:
                logging.warning(f"⚠️ Twelve Data 無法獲取 {stock_id} 盤中數據")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"Twelve Data 盤中數據獲取失敗: {e}")
            return pd.DataFrame()

    def _is_taiwan_stock(self, stock_id):
        """判斷是否為台股"""
        try:
            # 台股代碼通常是4位數字
            # 如果包含字母，則不是台股
            if not stock_id.isdigit():
                return False

            stock_num = int(stock_id)
            return 1000 <= stock_num <= 9999
        except:
            return False

    def _fetch_yahoo_intraday(self, formatted_id, interval):
        """從Yahoo台股獲取盤中數據"""
        try:
            # Yahoo台股API
            url = "https://tw.stock.yahoo.com/_td-stock/api/resource/StockServices.chart"
            
            params = {
                'symbol': formatted_id['yahoo_format'],
                'period': '1d',
                'interval': interval,
                'includePrePost': 'false'
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                    result = data['chart']['result'][0]
                    
                    if 'timestamp' in result and 'indicators' in result:
                        timestamps = result['timestamp']
                        quotes = result['indicators']['quote'][0]
                        
                        # 構建DataFrame
                        df_data = []
                        for i, ts in enumerate(timestamps):
                            if (quotes['open'][i] is not None and 
                                quotes['high'][i] is not None and 
                                quotes['low'][i] is not None and 
                                quotes['close'][i] is not None):
                                
                                df_data.append({
                                    'datetime': pd.to_datetime(ts, unit='s', utc=True).tz_convert('Asia/Taipei'),
                                    'open': float(quotes['open'][i]),
                                    'high': float(quotes['high'][i]),
                                    'low': float(quotes['low'][i]),
                                    'close': float(quotes['close'][i]),
                                    'volume': int(quotes['volume'][i]) if quotes['volume'][i] else 0
                                })
                        
                        if df_data:
                            df = pd.DataFrame(df_data)
                            df = df.sort_values('datetime').reset_index(drop=True)
                            logging.info(f"✅ Yahoo 獲取到 {len(df)} 筆盤中數據")
                            return df
            
            return pd.DataFrame()
            
        except Exception as e:
            logging.error(f"Yahoo盤中數據獲取失敗: {e}")
            return pd.DataFrame()
    
    def _fetch_twse_realtime(self, formatted_id, interval):
        """從證交所/櫃買中心API獲取數據 - 使用增強網路管理器"""
        try:
            clean_code = formatted_id['clean']

            # 判斷是否為櫃買中心股票
            is_otc_stock = self._is_otc_stock(clean_code)

            if is_otc_stock:
                # 櫃買股票，先嘗試TPEX API
                logging.debug(f"🏪 檢測到櫃買股票 {clean_code}，優先使用TPEX API")
                result = self._fetch_tpex_realtime_data(clean_code)
                if result is not None and not result.empty:
                    return result
                # 如果失敗，再嘗試TWSE API
                logging.debug(f"🔄 TPEX失敗，嘗試TWSE API for {clean_code}")
                result = self._fetch_twse_api_data(clean_code)
                if result is not None and not result.empty:
                    return result
            else:
                # 上市股票，先嘗試TWSE API
                logging.debug(f"🏢 檢測到上市股票 {clean_code}，優先使用TWSE API")
                result = self._fetch_twse_api_data(clean_code)
                if result is not None and not result.empty:
                    return result
                # 如果失敗，再嘗試TPEX API
                logging.debug(f"🔄 TWSE失敗，嘗試TPEX API for {clean_code}")
                result = self._fetch_tpex_realtime_data(clean_code)
                if result is not None and not result.empty:
                    return result

            logging.error(f"❌ 所有API都無法獲取 {clean_code} 的即時數據")
            return pd.DataFrame()

        except Exception as e:
            logging.error(f"❌ 獲取即時數據失敗 {formatted_id['clean']}: {e}")
            return pd.DataFrame()

    def _is_otc_stock(self, stock_code):
        """判斷是否為櫃買中心股票"""
        try:
            if not stock_code or len(stock_code) < 4:
                return False

            # 櫃買中心股票代碼規則
            first_digit = stock_code[0]
            return first_digit in ['5', '6', '7', '8']

        except Exception:
            return False

    def _fetch_twse_api_data(self, clean_code):
        """從證交所API獲取數據"""
        try:
            # 嘗試使用增強網路管理器
            try:
                from enhanced_network_manager import make_enhanced_request
                use_enhanced = True
            except ImportError:
                use_enhanced = False

            # 證交所即時報價API
            url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"

            params = {
                'ex_ch': f"tse_{clean_code}.tw",
                'json': '1',
                'delay': '0'
            }

            if use_enhanced:
                response = make_enhanced_request(url, params=params, timeout=10)
            else:
                response = self.session.get(url, params=params, timeout=10)
            
            if response and response.status_code == 200:
                data = response.json()

                if 'msgArray' in data and data['msgArray']:
                    stock_info = data['msgArray'][0]

                    # 安全解析數據，處理'-'字符
                    try:
                        open_price = float(stock_info.get('o', 0)) if stock_info.get('o') not in ['-', ''] else 0
                        high_price = float(stock_info.get('h', 0)) if stock_info.get('h') not in ['-', ''] else 0
                        low_price = float(stock_info.get('l', 0)) if stock_info.get('l') not in ['-', ''] else 0
                        close_price = float(stock_info.get('z', 0)) if stock_info.get('z') not in ['-', ''] else 0
                        volume = int(stock_info.get('v', 0)) if stock_info.get('v') not in ['-', ''] else 0

                        # 檢查數據有效性
                        if close_price > 0:
                            current_time = datetime.now()

                            # 構建當前K線數據
                            df_data = [{
                                'datetime': current_time,
                                'open': open_price,
                                'high': high_price,
                                'low': low_price,
                                'close': close_price,
                                'volume': volume
                            }]

                            df = pd.DataFrame(df_data)
                            logging.info(f"✅ 證交所API獲取 {clean_code} 成功")
                            return df
                        else:
                            logging.warning(f"⚠️ 證交所API {clean_code} 數據無效（價格為0）")
                            return pd.DataFrame()

                    except (ValueError, TypeError) as e:
                        logging.error(f"證交所API數據解析失敗 {clean_code}: could not convert string to float: '{stock_info.get('z', 'N/A')}'")
                        return pd.DataFrame()
                else:
                    logging.warning(f"⚠️ 證交所API無數據返回 {clean_code}")
                    return pd.DataFrame()
            else:
                logging.warning(f"⚠️ 證交所API請求失敗 {clean_code}")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"證交所API獲取失敗 {clean_code}: {e}")
            return pd.DataFrame()

    def _fetch_tpex_realtime_data(self, clean_code):
        """從櫃買中心API獲取即時數據"""
        try:
            # 嘗試使用增強網路管理器
            try:
                from enhanced_network_manager import make_enhanced_request
                use_enhanced = True
            except ImportError:
                use_enhanced = False

            # 櫃買中心的即時報價網址
            url = "https://www.tpex.org.tw/web/stock/aftertrading/otc_quotes_no1430/stk_wn1430_result.php"

            params = {
                'l': 'zh-tw',
                'd': datetime.now().strftime('%Y/%m/%d'),
                'se': 'EW',
                's': '0,asc,0'
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://www.tpex.org.tw/'
            }

            if use_enhanced:
                response = make_enhanced_request(url, params=params, headers=headers, timeout=10)
            else:
                response = self.session.get(url, params=params, headers=headers, timeout=10)

            if response and response.status_code == 200:
                try:
                    data = response.json()

                    if 'aaData' in data and data['aaData']:
                        for stock_data in data['aaData']:
                            if len(stock_data) > 0 and stock_data[0] == clean_code:
                                # 找到對應股票，解析數據
                                try:
                                    current_price = float(stock_data[2]) if len(stock_data) > 2 and stock_data[2] not in ['-', ''] else 0
                                    open_price = float(stock_data[4]) if len(stock_data) > 4 and stock_data[4] not in ['-', ''] else current_price
                                    high_price = float(stock_data[5]) if len(stock_data) > 5 and stock_data[5] not in ['-', ''] else current_price
                                    low_price = float(stock_data[6]) if len(stock_data) > 6 and stock_data[6] not in ['-', ''] else current_price
                                    volume = int(stock_data[7].replace(',', '')) if len(stock_data) > 7 and stock_data[7] not in ['-', ''] else 0

                                    if current_price > 0:
                                        current_time = datetime.now()

                                        # 構建當前K線數據
                                        df_data = [{
                                            'datetime': current_time,
                                            'open': open_price,
                                            'high': high_price,
                                            'low': low_price,
                                            'close': current_price,
                                            'volume': volume
                                        }]

                                        df = pd.DataFrame(df_data)
                                        logging.info(f"✅ 櫃買API獲取 {clean_code} 成功")
                                        return df
                                    else:
                                        logging.warning(f"⚠️ 櫃買API {clean_code} 數據無效（價格為0）")
                                        return pd.DataFrame()

                                except (ValueError, TypeError, IndexError) as e:
                                    logging.error(f"櫃買API數據解析失敗 {clean_code}: {e}")
                                    return pd.DataFrame()

                        # 沒有找到對應股票
                        logging.warning(f"⚠️ 櫃買API找不到股票 {clean_code}")
                        return pd.DataFrame()
                    else:
                        logging.warning(f"⚠️ 櫃買API無數據返回 {clean_code}")
                        return pd.DataFrame()

                except json.JSONDecodeError as e:
                    logging.error(f"櫃買API JSON解析失敗: {e}")
                    return pd.DataFrame()
            else:
                logging.warning(f"⚠️ 櫃買API請求失敗 {clean_code}")
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"❌ 櫃買API獲取失敗 {clean_code}: {e}")
            return pd.DataFrame()
    
    def _fetch_investing_intraday(self, formatted_id, interval):
        """從Investing.com獲取盤中數據"""
        try:
            # 這裡可以實現Investing.com的盤中數據獲取
            # 由於API限制，暫時返回空數據
            logging.info(f"Investing.com 盤中數據暫不可用")
            return pd.DataFrame()

        except Exception as e:
            logging.error(f"Investing.com盤中數據獲取失敗: {e}")
            return pd.DataFrame()

    def _fetch_yahoo_crawler_realtime(self, formatted_id, interval):
        """🛡️ 使用Yahoo爬蟲獲取即時報價數據 - 強化反爬蟲機制"""
        try:
            stock_id = formatted_id['clean']

            # 檢查是否為已知失敗股票
            if stock_id in self.failed_stocks:
                logging.debug(f"🛡️ 跳過已知失敗股票: {stock_id}")
                return pd.DataFrame()

            # 使用Yahoo爬蟲URL（添加隨機參數避免緩存）
            timestamp = int(time.time() * 1000)
            callback_id = f"jQuery{random.randint(111111111111111111, 999999999999999999)}_{timestamp}"
            url = f"https://tw.quote.finance.yahoo.net/quote/q?type=ta&perd=d&mkt=10&sym={stock_id}&v=1&callback={callback_id}&_={timestamp}"

            logging.info(f"🛡️ 使用強化Yahoo爬蟲獲取 {stock_id} 即時數據")

            # 使用安全請求方法
            response = self.make_safe_request(url)

            if response and response.status_code == 200:
                # 解析響應數據
                text = response.text

                # 檢查是否被反爬蟲機制阻擋
                if "初始化中" in text or "請稍候" in text or "驗證中" in text:
                    logging.warning(f"🛡️ {stock_id} 觸發反爬蟲機制，等待後重試")
                    time.sleep(5)
                    # 重新請求一次
                    response = self.make_safe_request(url)
                    if response:
                        text = response.text
                    else:
                        self.failed_stocks.add(stock_id)
                        return pd.DataFrame()

                # 最新價格數據
                current_data = [l for l in text.split('{') if len(l) >= 60]
                if len(current_data) >= 2:
                    current = current_data[-1].replace('"', '').split(',')

                    # 昨日價格
                    yday_match = re.search(':.*', current_data[-2].split(',')[4])
                    if yday_match:
                        yday = float(yday_match.group()[1:])

                        # 解析當前數據
                        open_match = re.search(':.*', current[1])
                        high_match = re.search(':.*', current[2])
                        low_match = re.search(':.*', current[3])
                        close_match = re.search(':.*', current[4])
                        volume_match = re.search(':.*', current[5].replace('}]', ''))

                        if all([open_match, high_match, low_match, close_match, volume_match]):
                            open_price = float(open_match.group()[1:])
                            high_price = float(high_match.group()[1:])
                            low_price = float(low_match.group()[1:])
                            close_price = float(close_match.group()[1:])
                            volume = float(volume_match.group()[1:])

                            # 計算漲跌幅
                            pct_change = round((close_price / yday - 1) * 100, 2)

                            # 創建DataFrame
                            df_data = [{
                                'datetime': datetime.now(),
                                'open': open_price,
                                'high': high_price,
                                'low': low_price,
                                'close': close_price,
                                'volume': volume,
                                'pct_change': pct_change,
                                'prev_close': yday
                            }]

                            df = pd.DataFrame(df_data)
                            logging.info(f"✅ 強化Yahoo爬蟲成功獲取 {stock_id} 即時數據")
                            return df
                        else:
                            logging.warning(f"⚠️ Yahoo爬蟲數據解析失敗 {stock_id}")
                            self.failed_stocks.add(stock_id)
                            return pd.DataFrame()
                    else:
                        logging.warning(f"⚠️ Yahoo爬蟲無法解析昨日價格 {stock_id}")
                        self.failed_stocks.add(stock_id)
                        return pd.DataFrame()
                else:
                    logging.warning(f"⚠️ Yahoo爬蟲數據格式異常 {stock_id}")
                    self.failed_stocks.add(stock_id)
                    return pd.DataFrame()
            else:
                status_code = response.status_code if response else "無回應"
                logging.warning(f"⚠️ Yahoo爬蟲請求失敗 {stock_id}: {status_code}")
                self.failed_stocks.add(stock_id)
                return pd.DataFrame()

        except Exception as e:
            logging.error(f"❌ Yahoo爬蟲獲取失敗 {formatted_id['clean']}: {e}")
            return pd.DataFrame()
    
    def get_market_status(self):
        """獲取市場開盤狀態"""
        try:
            now = datetime.now()
            
            # 台股交易時間：9:00-13:30
            market_open = now.replace(hour=9, minute=0, second=0, microsecond=0)
            market_close = now.replace(hour=13, minute=30, second=0, microsecond=0)
            
            # 檢查是否為交易日（週一到週五）
            if now.weekday() >= 5:  # 週六、週日
                return {
                    'is_open': False,
                    'status': '休市',
                    'next_open': self._get_next_trading_day(now)
                }
            
            # 檢查是否在交易時間內
            if market_open <= now <= market_close:
                return {
                    'is_open': True,
                    'status': '開盤中',
                    'time_to_close': market_close - now
                }
            elif now < market_open:
                return {
                    'is_open': False,
                    'status': '盤前',
                    'time_to_open': market_open - now
                }
            else:
                return {
                    'is_open': False,
                    'status': '收盤',
                    'next_open': self._get_next_trading_day(now)
                }
                
        except Exception as e:
            logging.error(f"獲取市場狀態失敗: {e}")
            return {'is_open': False, 'status': '未知', 'error': str(e)}
    
    def _get_next_trading_day(self, current_time):
        """獲取下一個交易日"""
        next_day = current_time + timedelta(days=1)
        
        # 跳過週末
        while next_day.weekday() >= 5:
            next_day += timedelta(days=1)
        
        return next_day.replace(hour=9, minute=0, second=0, microsecond=0)
    
    def get_multiple_stocks_intraday(self, stock_list, interval='5m'):
        """批量獲取多支股票的盤中數據"""
        results = {}
        
        for stock_id in stock_list:
            try:
                logging.info(f"📊 獲取 {stock_id} 盤中數據...")
                data = self.get_intraday_data(stock_id, interval)
                
                if not data.empty:
                    results[stock_id] = data
                    logging.info(f"✅ {stock_id} 盤中數據獲取成功")
                else:
                    logging.warning(f"⚠️ {stock_id} 盤中數據獲取失敗")
                
                # 避免請求過快
                time.sleep(0.2)
                
            except Exception as e:
                logging.error(f"❌ {stock_id} 盤中數據獲取錯誤: {e}")
                continue
        
        return results
    
    def save_intraday_data(self, stock_id, data, file_path=None):
        """保存盤中數據到文件"""
        try:
            if file_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M')
                file_path = f"intraday_data_{stock_id}_{timestamp}.csv"
            
            data.to_csv(file_path, index=False, encoding='utf-8-sig')
            logging.info(f"💾 盤中數據已保存到: {file_path}")
            return file_path
            
        except Exception as e:
            logging.error(f"保存盤中數據失敗: {e}")
            return None

# 創建全局實例
intraday_fetcher = IntradayDataFetcher()

def get_intraday_data(stock_id, interval='5m'):
    """便捷函數：獲取盤中數據"""
    return intraday_fetcher.get_intraday_data(stock_id, interval)

def get_market_status():
    """便捷函數：獲取市場狀態"""
    return intraday_fetcher.get_market_status()

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 測試當日5分鐘數據獲取...")
    
    # 測試市場狀態
    market_status = get_market_status()
    print(f"📊 市場狀態: {market_status}")
    
    # 測試獲取台積電5分鐘數據
    print(f"\n📈 測試獲取台積電(2330)5分鐘數據...")
    df = get_intraday_data('2330', '5m')
    
    if not df.empty:
        print(f"✅ 成功獲取 {len(df)} 筆5分鐘數據")
        print(f"時間範圍: {df['datetime'].min()} 到 {df['datetime'].max()}")
        print(f"最新價格: {df['close'].iloc[-1]:.2f}")
        print("\n前5筆數據:")
        print(df.head())
    else:
        print("❌ 獲取5分鐘數據失敗")
    
    print(f"\n🎉 測試完成！")
