#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原生K線圖實現 - 不依賴pyqtgraph的K線圖解決方案
"""

from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import pandas as pd
import numpy as np

class NativeKLineChart(QWidget):
    """原生K線圖組件 - 使用PyQt6原生繪圖"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(800, 600)
        self.setStyleSheet("background-color: black;")
        
        # 數據
        self.df = pd.DataFrame()
        self.stock_id = ""
        
        # 繪圖參數
        self.margin = 50
        self.chart_height = 400
        self.volume_height = 150
        
        # 顏色配置
        self.colors = {
            'background': QColor(0, 0, 0),
            'grid': QColor(64, 64, 64),
            'text': QColor(255, 255, 255),
            'up_candle': QColor(255, 0, 0),      # 紅色上漲
            'down_candle': QColor(0, 255, 0),    # 綠色下跌
            'ma5': QColor(255, 255, 255),        # 白色
            'ma10': QColor(255, 165, 0),         # 橙色
            'ma20': QColor(255, 255, 0),         # 黃色
            'ma60': QColor(0, 255, 255),         # 青色
            'ma120': QColor(255, 0, 255),        # 洋紅色
            'ma240': QColor(0, 255, 0),          # 綠色
            'volume': QColor(128, 128, 128),     # 灰色
        }
        
    def set_data(self, df, stock_id):
        """設置K線圖數據"""
        self.df = df.copy()
        self.stock_id = stock_id
        
        if not df.empty:
            # 計算移動平均線
            for period in [5, 10, 20, 60, 120, 240]:
                ma_col = f'MA{period}'
                self.df[ma_col] = self.df['Close'].rolling(window=period).mean()
        
        self.update()  # 觸發重繪
        
    def paintEvent(self, event):
        """繪製K線圖"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 清空背景
        painter.fillRect(self.rect(), self.colors['background'])
        
        if self.df.empty:
            self.draw_no_data_message(painter)
            return
            
        try:
            # 繪製K線圖
            self.draw_price_chart(painter)
            # 繪製成交量
            self.draw_volume_chart(painter)
            # 繪製標題
            self.draw_title(painter)
            
        except Exception as e:
            self.draw_error_message(painter, str(e))
    
    def draw_no_data_message(self, painter):
        """繪製無數據訊息"""
        painter.setPen(QPen(self.colors['text'], 2))
        font = QFont("Arial", 16)
        painter.setFont(font)
        
        rect = self.rect()
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, 
                        "無K線數據\n請選擇股票查看圖表")
    
    def draw_error_message(self, painter, error_msg):
        """繪製錯誤訊息"""
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        font = QFont("Arial", 12)
        painter.setFont(font)
        
        rect = self.rect()
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, 
                        f"K線圖繪製錯誤:\n{error_msg}")
    
    def draw_title(self, painter):
        """繪製標題"""
        painter.setPen(QPen(self.colors['text'], 2))
        font = QFont("Arial", 14, QFont.Weight.Bold)
        painter.setFont(font)
        
        title = f"{self.stock_id} K線圖 (含雙週線MA10)"
        painter.drawText(10, 25, title)
        
        # 繪製均線說明
        font = QFont("Arial", 10)
        painter.setFont(font)
        
        ma_info = [
            ("MA5", self.colors['ma5']),
            ("MA10", self.colors['ma10']),
            ("MA20", self.colors['ma20']),
            ("MA60", self.colors['ma60']),
            ("MA120", self.colors['ma120']),
            ("MA240", self.colors['ma240'])
        ]
        
        x_pos = 10
        for ma_name, color in ma_info:
            painter.setPen(QPen(color, 2))
            painter.drawText(x_pos, 45, ma_name)
            x_pos += 60
    
    def draw_price_chart(self, painter):
        """繪製價格圖表"""
        if len(self.df) < 2:
            return
            
        # 計算繪圖區域
        chart_rect = QRect(self.margin, 60, 
                          self.width() - 2 * self.margin, 
                          self.chart_height)
        
        # 繪製網格
        self.draw_grid(painter, chart_rect)
        
        # 計算價格範圍
        visible_df = self.df.tail(100)  # 顯示最近100個交易日
        if visible_df.empty:
            return
            
        price_min = visible_df[['High', 'Low', 'Open', 'Close']].min().min()
        price_max = visible_df[['High', 'Low', 'Open', 'Close']].max().max()
        
        # 添加一些邊距
        price_range = price_max - price_min
        price_min -= price_range * 0.1
        price_max += price_range * 0.1
        
        # 繪製K線
        self.draw_candles(painter, chart_rect, visible_df, price_min, price_max)
        
        # 繪製移動平均線
        self.draw_moving_averages(painter, chart_rect, visible_df, price_min, price_max)
        
        # 繪製價格軸
        self.draw_price_axis(painter, chart_rect, price_min, price_max)
    
    def draw_grid(self, painter, rect):
        """繪製網格"""
        painter.setPen(QPen(self.colors['grid'], 1))
        
        # 垂直網格線
        for i in range(1, 10):
            x = rect.left() + (rect.width() * i // 10)
            painter.drawLine(x, rect.top(), x, rect.bottom())
        
        # 水平網格線
        for i in range(1, 8):
            y = rect.top() + (rect.height() * i // 8)
            painter.drawLine(rect.left(), y, rect.right(), y)
    
    def draw_candles(self, painter, rect, df, price_min, price_max):
        """繪製K線蠟燭圖"""
        if df.empty:
            return
            
        candle_width = max(2, rect.width() // len(df) - 1)
        
        for i, (idx, row) in enumerate(df.iterrows()):
            x = rect.left() + (i * rect.width() // len(df))
            
            # 計算價格對應的y座標
            high_y = rect.top() + (price_max - row['High']) / (price_max - price_min) * rect.height()
            low_y = rect.top() + (price_max - row['Low']) / (price_max - price_min) * rect.height()
            open_y = rect.top() + (price_max - row['Open']) / (price_max - price_min) * rect.height()
            close_y = rect.top() + (price_max - row['Close']) / (price_max - price_min) * rect.height()
            
            # 判斷漲跌
            is_up = row['Close'] >= row['Open']
            color = self.colors['up_candle'] if is_up else self.colors['down_candle']
            
            # 繪製影線
            painter.setPen(QPen(color, 1))
            painter.drawLine(int(x + candle_width//2), int(high_y), 
                           int(x + candle_width//2), int(low_y))
            
            # 繪製實體
            body_top = min(open_y, close_y)
            body_height = abs(close_y - open_y)
            
            if body_height < 1:
                body_height = 1
                
            painter.fillRect(int(x), int(body_top), candle_width, int(body_height), color)
    
    def draw_moving_averages(self, painter, rect, df, price_min, price_max):
        """繪製移動平均線"""
        ma_configs = [
            ('MA5', self.colors['ma5']),
            ('MA10', self.colors['ma10']),
            ('MA20', self.colors['ma20']),
            ('MA60', self.colors['ma60']),
            ('MA120', self.colors['ma120']),
            ('MA240', self.colors['ma240'])
        ]
        
        for ma_name, color in ma_configs:
            if ma_name not in df.columns:
                continue
                
            painter.setPen(QPen(color, 2))
            
            points = []
            for i, (idx, row) in enumerate(df.iterrows()):
                if pd.isna(row[ma_name]):
                    continue
                    
                x = rect.left() + (i * rect.width() // len(df))
                y = rect.top() + (price_max - row[ma_name]) / (price_max - price_min) * rect.height()
                points.append(QPoint(int(x), int(y)))
            
            # 繪製連線
            if len(points) > 1:
                for i in range(len(points) - 1):
                    painter.drawLine(points[i], points[i + 1])
    
    def draw_price_axis(self, painter, rect, price_min, price_max):
        """繪製價格軸"""
        painter.setPen(QPen(self.colors['text'], 1))
        font = QFont("Arial", 9)
        painter.setFont(font)
        
        # 繪製價格刻度
        for i in range(6):
            price = price_min + (price_max - price_min) * i / 5
            y = rect.bottom() - (i * rect.height() // 5)
            
            # 繪製刻度線
            painter.drawLine(rect.right(), y, rect.right() + 5, y)
            
            # 繪製價格文字
            price_text = f"{price:.2f}"
            painter.drawText(rect.right() + 10, y + 5, price_text)
    
    def draw_volume_chart(self, painter):
        """繪製成交量圖表"""
        if self.df.empty or 'Volume' not in self.df.columns:
            return
            
        # 計算成交量圖表區域
        volume_rect = QRect(self.margin, 
                           60 + self.chart_height + 20,
                           self.width() - 2 * self.margin, 
                           self.volume_height)
        
        visible_df = self.df.tail(100)
        if visible_df.empty:
            return
            
        max_volume = visible_df['Volume'].max()
        if max_volume <= 0:
            return
            
        # 繪製成交量柱狀圖
        painter.setPen(QPen(self.colors['volume'], 1))
        painter.setBrush(QBrush(self.colors['volume']))
        
        bar_width = max(1, volume_rect.width() // len(visible_df))
        
        for i, (idx, row) in enumerate(visible_df.iterrows()):
            if pd.isna(row['Volume']) or row['Volume'] <= 0:
                continue
                
            x = volume_rect.left() + (i * volume_rect.width() // len(visible_df))
            bar_height = int((row['Volume'] / max_volume) * volume_rect.height())
            y = volume_rect.bottom() - bar_height
            
            painter.fillRect(x, y, bar_width, bar_height, self.colors['volume'])
        
        # 繪製成交量標題
        painter.setPen(QPen(self.colors['text'], 1))
        font = QFont("Arial", 10)
        painter.setFont(font)
        painter.drawText(volume_rect.left(), volume_rect.top() - 5, "成交量")


def create_native_kline_replacement():
    """創建原生K線圖替換代碼"""
    replacement_code = '''
    def create_k_chart_tab_native(self):
        """創建原生K線圖標籤頁 - 不依賴pyqtgraph"""
        k_chart_tab = QWidget()
        k_chart_layout = QVBoxLayout(k_chart_tab)
        
        # 控制面板
        control_panel = QHBoxLayout()
        
        # 週期選擇
        period_label = QLabel("顯示週期:")
        self.period_combo = QComboBox()
        self.period_combo.addItems(["1個月", "3個月", "6個月", "1年", "2年"])
        self.period_combo.setCurrentText("3個月")
        
        control_panel.addWidget(period_label)
        control_panel.addWidget(self.period_combo)
        control_panel.addStretch()
        
        k_chart_layout.addLayout(control_panel)
        
        # 創建原生K線圖組件
        from native_kline_chart import NativeKLineChart
        self.native_chart = NativeKLineChart()
        k_chart_layout.addWidget(self.native_chart)
        
        return k_chart_tab
    
    def plot_stock_native(self, stock_id):
        """使用原生組件繪製股票圖表"""
        try:
            if not stock_id:
                return
                
            self.current_stock_id = stock_id
            logging.info(f"📊 使用原生組件繪製股票: {stock_id}")
            
            self.show_status(f"載入 {stock_id} 的數據...")
            period_text = self.period_combo.currentText()
            days = self.period_map.get(period_text, 44)
            extra_days = 240
            df = self.fetch_stock_data(stock_id, days + extra_days)
            
            if df.empty:
                self.show_status(f"無法獲取 {stock_id} 的數據", is_error=True)
                return
                
            # 使用原生K線圖組件顯示數據
            if hasattr(self, 'native_chart'):
                self.native_chart.set_data(df.tail(100), stock_id)
                self.show_status(f"✅ {stock_id} K線圖已更新（含雙週線MA10）")
            else:
                self.show_status("K線圖組件未初始化", is_error=True)
                
        except Exception as e:
            logging.error(f"❌ 原生K線圖繪製失敗: {e}")
            self.show_status(f"K線圖繪製失敗: {e}", is_error=True)
    '''
    
    return replacement_code

if __name__ == "__main__":
    # 測試原生K線圖組件
    import sys
    app = QApplication(sys.argv)
    
    # 創建測試數據
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    np.random.seed(42)
    
    # 生成模擬股價數據
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(50):
        change = np.random.normal(0, 2)
        base_price += change
        prices.append(base_price)
        volumes.append(np.random.randint(1000, 10000))
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p + np.random.normal(0, 0.5) for p in prices],
        'High': [p + abs(np.random.normal(0, 1)) for p in prices],
        'Low': [p - abs(np.random.normal(0, 1)) for p in prices],
        'Close': prices,
        'Volume': volumes
    })
    
    # 測試K線圖組件
    chart = NativeKLineChart()
    chart.set_data(df, "測試股票 2330")
    chart.show()
    
    sys.exit(app.exec())
