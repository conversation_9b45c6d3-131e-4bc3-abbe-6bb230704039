#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 ScanWorker 修復
驗證 thread_pool 屬性問題是否解決
"""

import sys
import time
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_scanworker_creation():
    """測試 ScanWorker 創建"""
    print("測試 ScanWorker 創建...")
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        # 創建監控器
        monitor = PreMarketMonitor()
        print("✅ 監控器創建成功")
        
        # 定義回調函數
        def success_callback(results):
            print(f"🎉 成功回調: {type(results)}")
        
        def error_callback(error_msg):
            print(f"❌ 錯誤回調: {error_msg}")
        
        def progress_callback(value, message):
            print(f"📊 進度回調: {value}% - {message}")
        
        # 創建 ScanWorker
        print("創建 ScanWorker...")
        scan_worker = ScanWorker(
            monitor,
            callback_success=success_callback,
            callback_error=error_callback,
            progress_callback=progress_callback
        )
        print("✅ ScanWorker 創建成功")
        
        # 檢查屬性
        print("檢查 ScanWorker 屬性...")
        required_attrs = [
            'pre_market_monitor',
            'callback_success', 
            'callback_error',
            'progress_callback',
            'is_running',
            'thread_pool'
        ]
        
        for attr in required_attrs:
            if hasattr(scan_worker, attr):
                value = getattr(scan_worker, attr)
                print(f"  ✅ {attr}: {type(value).__name__}")
            else:
                print(f"  ❌ {attr}: 缺失")
                return False
        
        # 測試方法
        print("檢查 ScanWorker 方法...")
        required_methods = [
            'update_progress',
            'start_scan',
            'run'
        ]
        
        for method in required_methods:
            if hasattr(scan_worker, method):
                print(f"  ✅ {method}: 存在")
            else:
                print(f"  ❌ {method}: 缺失")
                return False
        
        # 測試進度更新
        print("測試進度更新...")
        try:
            scan_worker.update_progress(50, "測試進度更新")
            print("✅ 進度更新測試成功")
        except Exception as e:
            print(f"❌ 進度更新測試失敗: {e}")
            return False
        
        print("✅ ScanWorker 所有測試通過")
        return True
        
    except Exception as e:
        print(f"❌ ScanWorker 測試失敗: {e}")
        return False

def test_scanworker_execution():
    """測試 ScanWorker 執行"""
    print("\n測試 ScanWorker 執行...")
    
    try:
        from monitoring.pre_market_monitor import PreMarketMonitor
        from O3mh_gui_v21_optimized import ScanWorker
        
        # 創建監控器
        monitor = PreMarketMonitor()
        
        # 結果收集
        results_collected = []
        errors_collected = []
        progress_collected = []
        
        def success_callback(results):
            results_collected.append(results)
            print(f"🎉 收到成功結果: {type(results)}")
            if isinstance(results, dict):
                total_items = sum(len(v) for v in results.values() if isinstance(v, dict))
                print(f"   總數據項: {total_items}")
        
        def error_callback(error_msg):
            errors_collected.append(error_msg)
            print(f"❌ 收到錯誤: {error_msg}")
        
        def progress_callback(value, message):
            progress_collected.append((value, message))
            print(f"📊 進度: {value}% - {message}")
        
        # 創建並啟動 ScanWorker
        scan_worker = ScanWorker(
            monitor,
            callback_success=success_callback,
            callback_error=error_callback,
            progress_callback=progress_callback
        )
        
        print("🚀 啟動掃描...")
        scan_worker.start_scan()
        
        # 等待完成
        print("⏳ 等待掃描完成...")
        time.sleep(15)  # 等待15秒
        
        # 檢查結果
        print(f"\n📊 執行結果統計:")
        print(f"  成功結果: {len(results_collected)} 個")
        print(f"  錯誤信息: {len(errors_collected)} 個")
        print(f"  進度更新: {len(progress_collected)} 次")
        
        if results_collected:
            print("✅ 掃描執行成功")
            return True
        elif errors_collected:
            print(f"⚠️ 掃描執行有錯誤: {errors_collected[0]}")
            return False
        else:
            print("⚠️ 掃描執行無結果")
            return False
        
    except Exception as e:
        print(f"❌ ScanWorker 執行測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("ScanWorker 修復驗證測試")
    print("=" * 60)
    
    # 1. 測試創建
    creation_success = test_scanworker_creation()
    
    if creation_success:
        print("\n" + "="*60)
        # 2. 測試執行
        execution_success = test_scanworker_execution()
        
        print(f"\n🎯 最終測試結果:")
        print(f"  創建測試: {'✅ 通過' if creation_success else '❌ 失敗'}")
        print(f"  執行測試: {'✅ 通過' if execution_success else '❌ 失敗'}")
        
        if creation_success and execution_success:
            print("\n🎉 ScanWorker 修復完全成功！")
            print("   thread_pool 屬性問題已解決")
            print("   掃描功能正常工作")
            print("   進度回調正常執行")
        else:
            print("\n⚠️ 部分功能需要進一步調整")
    else:
        print("\n❌ ScanWorker 創建失敗，跳過執行測試")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 測試已停止")
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
    
    print(f"\n👋 測試結束")
