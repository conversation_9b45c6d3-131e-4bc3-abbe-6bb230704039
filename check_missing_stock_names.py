#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查並修復缺失的股票名稱
特別針對 0058、0059、1262 等顯示不正確的股票
"""

import os
import sqlite3
import pandas as pd
from stock_name_mapping import get_cached_stock_info

def check_specific_stocks():
    """檢查特定股票的名稱映射"""
    
    print("=" * 80)
    print("🔍 檢查特定股票的名稱映射")
    print("=" * 80)
    
    # 問題股票列表
    problem_stocks = ['0058', '0059', '1262', '0050', '0051', '0052', '0053', '0055', '0056', '0057']
    
    try:
        # 獲取股票資訊
        stock_info = get_cached_stock_info()
        
        print(f"📊 檢查 {len(problem_stocks)} 檔問題股票:")
        
        for stock_id in problem_stocks:
            info = stock_info.get(stock_id, {})
            stock_name = info.get('stock_name', '未找到')
            listing_status = info.get('listing_status', '未找到')
            industry = info.get('industry', '未找到')
            
            print(f"   {stock_id}: {stock_name} | {listing_status} | {industry}")
        
        # 檢查 price.db 中這些股票的當前狀態
        print(f"\n📊 檢查 price.db 中的當前狀態:")
        
        price_db = 'D:/Finlab/history/tables/price.db'
        if os.path.exists(price_db):
            conn = sqlite3.connect(price_db)
            
            for stock_id in problem_stocks:
                cursor = conn.cursor()
                cursor.execute("""
                SELECT DISTINCT stock_name, listing_status, industry 
                FROM stock_daily_data 
                WHERE stock_id = ? 
                LIMIT 1
                """, (stock_id,))
                
                result = cursor.fetchone()
                if result:
                    db_name, db_listing, db_industry = result
                    print(f"   {stock_id}: {db_name} | {db_listing} | {db_industry}")
                else:
                    print(f"   {stock_id}: 資料庫中未找到")
            
            conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_comprehensive_stock_mapping():
    """獲取更全面的股票名稱映射"""
    
    print(f"\n🔍 建立更全面的股票名稱映射")
    print("=" * 60)
    
    # 手動補充常見的 ETF 和股票名稱
    manual_stock_mapping = {
        # ETF
        '0050': {'stock_name': '元大台灣50', 'listing_status': '上市', 'industry': 'ETF'},
        '0051': {'stock_name': '元大中型100', 'listing_status': '上市', 'industry': 'ETF'},
        '0052': {'stock_name': '富邦科技', 'listing_status': '上市', 'industry': 'ETF'},
        '0053': {'stock_name': '元大電子', 'listing_status': '上市', 'industry': 'ETF'},
        '0055': {'stock_name': '元大MSCI金融', 'listing_status': '上市', 'industry': 'ETF'},
        '0056': {'stock_name': '元大高股息', 'listing_status': '上市', 'industry': 'ETF'},
        '0057': {'stock_name': '富邦摩台', 'listing_status': '上市', 'industry': 'ETF'},
        '0058': {'stock_name': '富邦台50', 'listing_status': '上市', 'industry': 'ETF'},
        '0059': {'stock_name': '富邦台灣加權', 'listing_status': '上市', 'industry': 'ETF'},
        
        # 常見股票
        '1262': {'stock_name': '綠悅-KY', 'listing_status': '上市', 'industry': '食品工業'},
        '1235': {'stock_name': '興泰', 'listing_status': '上市', 'industry': '食品工業'},
        '1236': {'stock_name': '宏亞', 'listing_status': '上市', 'industry': '食品工業'},
        '1240': {'stock_name': '茂生農經', 'listing_status': '上櫃', 'industry': '農業科技業'},
        '1256': {'stock_name': '鮮活果汁-KY', 'listing_status': '上市', 'industry': '食品工業'},
        '1268': {'stock_name': '漢來美食', 'listing_status': '上櫃', 'industry': '觀光餐旅'},
        '1294': {'stock_name': '漢田生技', 'listing_status': '上櫃', 'industry': '食品工業'},
        '1295': {'stock_name': '生合', 'listing_status': '上櫃', 'industry': '食品工業'},
        
        # 更多 ETF
        '006201': {'stock_name': '元大富櫃50', 'listing_status': '上市', 'industry': 'ETF'},
        '006203': {'stock_name': '元大MSCI台灣', 'listing_status': '上市', 'industry': 'ETF'},
        '006204': {'stock_name': '永豐台灣加權', 'listing_status': '上市', 'industry': 'ETF'},
        '006205': {'stock_name': '富邦上証', 'listing_status': '上市', 'industry': 'ETF'},
        '006206': {'stock_name': '元大上証50', 'listing_status': '上市', 'industry': 'ETF'},
        '006207': {'stock_name': 'FH滬深', 'listing_status': '上市', 'industry': 'ETF'},
        '006208': {'stock_name': '富邦台50', 'listing_status': '上市', 'industry': 'ETF'},
    }
    
    print(f"📊 手動映射表包含 {len(manual_stock_mapping)} 檔股票")
    
    # 顯示手動映射的內容
    for stock_id, info in manual_stock_mapping.items():
        print(f"   {stock_id}: {info['stock_name']} | {info['listing_status']} | {info['industry']}")
    
    return manual_stock_mapping

def update_price_db_with_manual_mapping():
    """使用手動映射更新 price.db"""
    
    print(f"\n🔧 使用手動映射更新 price.db")
    print("=" * 60)
    
    try:
        # 獲取手動映射
        manual_mapping = get_comprehensive_stock_mapping()
        
        # 連接資料庫
        price_db = 'D:/Finlab/history/tables/price.db'
        if not os.path.exists(price_db):
            print(f"❌ 找不到 price.db: {price_db}")
            return False
        
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        updated_count = 0
        
        for stock_id, info in manual_mapping.items():
            stock_name = info['stock_name']
            listing_status = info['listing_status']
            industry = info['industry']
            
            # 檢查該股票是否存在於資料庫中
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data WHERE stock_id = ?", (stock_id,))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                # 更新股票資訊
                cursor.execute("""
                UPDATE stock_daily_data 
                SET stock_name = ?, listing_status = ?, industry = ?
                WHERE stock_id = ?
                """, (stock_name, listing_status, industry, stock_id))
                
                if cursor.rowcount > 0:
                    updated_count += 1
                    print(f"   ✅ 更新 {stock_id}: {stock_name}")
                else:
                    print(f"   ⚠️ {stock_id}: 無需更新")
            else:
                print(f"   ❌ {stock_id}: 資料庫中不存在")
        
        # 提交變更
        conn.commit()
        conn.close()
        
        print(f"\n✅ 手動映射更新完成: {updated_count} 檔股票")
        return True
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_updates():
    """驗證更新結果"""
    
    print(f"\n🔍 驗證更新結果")
    print("=" * 60)
    
    try:
        price_db = 'D:/Finlab/history/tables/price.db'
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        # 檢查問題股票的當前狀態
        problem_stocks = ['0058', '0059', '1262', '0050', '0051', '0052']
        
        print(f"📊 驗證問題股票的更新結果:")
        
        for stock_id in problem_stocks:
            cursor.execute("""
            SELECT DISTINCT stock_name, listing_status, industry 
            FROM stock_daily_data 
            WHERE stock_id = ? 
            LIMIT 1
            """, (stock_id,))
            
            result = cursor.fetchone()
            if result:
                db_name, db_listing, db_industry = result
                # 檢查是否還是預設格式
                is_default = (db_name.startswith('ETF') or db_name.startswith('股票'))
                status = "❌ 仍為預設" if is_default else "✅ 已更新"
                print(f"   {stock_id}: {db_name} | {db_listing} | {db_industry} {status}")
            else:
                print(f"   {stock_id}: 資料庫中未找到")
        
        # 統計整體完整性
        cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data")
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("""
        SELECT COUNT(DISTINCT stock_id) FROM stock_daily_data 
        WHERE stock_name NOT LIKE 'ETF%' 
        AND stock_name NOT LIKE '股票%'
        AND stock_name IS NOT NULL 
        AND stock_name != ''
        """)
        proper_names = cursor.fetchone()[0]
        
        proper_rate = proper_names / total_stocks * 100 if total_stocks > 0 else 0
        
        print(f"\n📊 整體股票名稱品質:")
        print(f"   總股票數: {total_stocks}")
        print(f"   正確名稱: {proper_names} ({proper_rate:.1f}%)")
        print(f"   預設名稱: {total_stocks - proper_names} ({100 - proper_rate:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    
    print("🔧 股票名稱映射完整性修復工具")
    
    # 1. 檢查特定股票
    if not check_specific_stocks():
        return
    
    # 2. 確認是否要更新
    response = input("\n是否要使用手動映射更新這些股票的名稱？(y/N): ")
    if response.lower() != 'y':
        print("❌ 取消更新")
        return
    
    # 3. 執行更新
    if update_price_db_with_manual_mapping():
        print("\n✅ 更新完成")
    else:
        print("\n❌ 更新失敗")
        return
    
    # 4. 驗證結果
    verify_updates()
    
    print(f"\n💡 建議:")
    print(f"   1. 重新啟動 GUI 程式")
    print(f"   2. 測試策略交集分析")
    print(f"   3. 檢查 0058、0059、1262 等股票是否顯示正確名稱")

if __name__ == "__main__":
    main()
