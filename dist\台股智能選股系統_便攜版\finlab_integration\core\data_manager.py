#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finlab數據管理器
整合finlab的數據管理功能
"""

import pandas as pd
import numpy as np
import os
import datetime
import logging
from typing import Optional, Dict, Any

# 嘗試導入TA-Lib，如果失敗則使用替代方案
try:
    import talib
    from talib import abstract
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib未安裝，將使用基本技術指標計算")

class FinlabDataManager:
    """Finlab數據管理器"""
    
    def __init__(self, data_path: str = "data/finlab_data", db_connections: Dict = None):
        self.data_path = data_path
        self.db_connections = db_connections or {}
        self.date = datetime.datetime.now().date()
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 確保數據目錄存在
        os.makedirs(data_path, exist_ok=True)
        os.makedirs(os.path.join(data_path, 'history', 'items'), exist_ok=True)
    
    def get_stock_data(self, stock_id: str, days: int = 90) -> pd.DataFrame:
        """獲取股票數據"""
        try:
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                query = """
                SELECT date, Open, High, Low, Close, Volume
                FROM stock_prices 
                WHERE stock_id = ? 
                ORDER BY date DESC 
                LIMIT ?
                """
                
                df = pd.read_sql_query(query, conn, params=(stock_id, days))
                
                if not df.empty:
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.sort_values('date').reset_index(drop=True)
                    
                    # 計算技術指標
                    df = self.calculate_technical_indicators(df)
                    
                    self.logger.info(f"獲取 {stock_id} 數據 {len(df)} 筆")
                    return df
            
            return pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"獲取股票數據失敗: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        if df.empty or len(df) < 20:
            return df
        
        try:
            # 移動平均線
            for period in [5, 10, 20, 60, 120, 240]:
                if len(df) >= period:
                    df[f'MA{period}'] = talib.SMA(df['Close'].values, timeperiod=period)
            
            # RSI
            df['RSI6'] = talib.RSI(df['Close'].values, timeperiod=6)
            df['RSI12'] = talib.RSI(df['Close'].values, timeperiod=12)
            
            # MACD
            macd, macdsignal, macdhist = talib.MACD(df['Close'].values)
            df['MACD'] = macd
            df['MACD_signal'] = macdsignal
            df['MACD_hist'] = macdhist
            
            # 布林帶
            upper, middle, lower = talib.BBANDS(df['Close'].values, timeperiod=20)
            df['BB_upper'] = upper
            df['BB_middle'] = middle
            df['BB_lower'] = lower
            
            # KD指標
            df['K'], df['D'] = talib.STOCH(df['High'].values, df['Low'].values, df['Close'].values)
            
            self.logger.debug("技術指標計算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"計算技術指標失敗: {e}")
            return df
    
    def get_all_stock_data(self, item_name: str, days: int = 0) -> pd.DataFrame:
        """獲取所有股票的特定數據項目"""
        try:
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                
                # 構建查詢
                if days > 0:
                    query = f"""
                    SELECT stock_id, date, {item_name}
                    FROM stock_prices 
                    WHERE date >= date('now', '-{days} days')
                    ORDER BY date DESC
                    """
                else:
                    query = f"""
                    SELECT stock_id, date, {item_name}
                    FROM stock_prices 
                    ORDER BY date DESC
                    """
                
                df = pd.read_sql_query(query, conn)
                
                if not df.empty:
                    # 轉換為pivot表格格式
                    pivot_df = df.pivot(index='date', columns='stock_id', values=item_name)
                    pivot_df.index = pd.to_datetime(pivot_df.index)
                    
                    self.logger.info(f"獲取 {item_name} 數據: {pivot_df.shape}")
                    return pivot_df
            
            return pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"獲取全市場數據失敗: {e}")
            return pd.DataFrame()
    
    def calculate_all_stock_indicator(self, indicator_func, **kwargs) -> pd.DataFrame:
        """計算所有股票的技術指標"""
        try:
            # 獲取基礎數據
            close = self.get_all_stock_data('Close', 1000)
            open_data = self.get_all_stock_data('Open', 1000)
            high = self.get_all_stock_data('High', 1000)
            low = self.get_all_stock_data('Low', 1000)
            volume = self.get_all_stock_data('Volume', 1000)
            
            if close.empty:
                return pd.DataFrame()
            
            result_dict = {}
            
            for stock_id in close.columns:
                try:
                    stock_data = {
                        'open': open_data[stock_id].fillna(method='ffill'),
                        'high': high[stock_id].fillna(method='ffill'),
                        'low': low[stock_id].fillna(method='ffill'),
                        'close': close[stock_id].fillna(method='ffill'),
                        'volume': volume[stock_id].fillna(method='ffill')
                    }
                    
                    indicator_result = indicator_func(stock_data, **kwargs)
                    result_dict[stock_id] = indicator_result
                    
                except Exception as e:
                    self.logger.warning(f"計算 {stock_id} 指標失敗: {e}")
                    result_dict[stock_id] = pd.Series(index=close.index)
            
            result_df = pd.DataFrame(result_dict, index=close.index)
            self.logger.info(f"批量指標計算完成: {result_df.shape}")
            return result_df
            
        except Exception as e:
            self.logger.error(f"批量指標計算失敗: {e}")
            return pd.DataFrame()
    
    def get_stock_list(self) -> list:
        """獲取所有股票代碼列表"""
        try:
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT stock_id FROM stock_prices ORDER BY stock_id")
                result = cursor.fetchall()
                return [row[0] for row in result]
            return []
        except Exception as e:
            self.logger.error(f"獲取股票列表失敗: {e}")
            return []
    
    def get_data_summary(self) -> Dict[str, Any]:
        """獲取數據摘要信息"""
        try:
            summary = {}
            
            if 'price' in self.db_connections:
                conn = self.db_connections['price']
                cursor = conn.cursor()
                
                # 總股票數
                cursor.execute("SELECT COUNT(DISTINCT stock_id) FROM stock_prices")
                summary['total_stocks'] = cursor.fetchone()[0]
                
                # 數據日期範圍
                cursor.execute("SELECT MIN(date), MAX(date) FROM stock_prices")
                date_range = cursor.fetchone()
                summary['date_range'] = {
                    'start': date_range[0],
                    'end': date_range[1]
                }
                
                # 總記錄數
                cursor.execute("SELECT COUNT(*) FROM stock_prices")
                summary['total_records'] = cursor.fetchone()[0]
            
            return summary
            
        except Exception as e:
            self.logger.error(f"獲取數據摘要失敗: {e}")
            return {}
