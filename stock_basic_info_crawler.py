"""
上市櫃基本資料清單爬蟲模組
從公開資訊觀測站(MOPS)爬取股票基本資料
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import urllib3
import csv
import io

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockBasicInfoDatabase:
    """股票基本資料資料庫管理類"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # 預設路徑：與price.db相同目錄
            default_dir = r"D:\Finlab\history\tables"
            os.makedirs(default_dir, exist_ok=True)  # 確保目錄存在
            self.db_path = os.path.join(default_dir, "stock_basic_info.db")
        else:
            self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化資料庫"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 創建股票基本資料表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_basic_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_code TEXT NOT NULL,
                company_name TEXT,
                company_abbreviation TEXT,
                industry_category TEXT,
                foreign_registration_country TEXT,
                address TEXT,
                business_number TEXT,
                chairman TEXT,
                general_manager TEXT,
                spokesperson TEXT,
                spokesperson_title TEXT,
                deputy_spokesperson TEXT,
                phone TEXT,
                establishment_date TEXT,
                listing_date TEXT,
                par_value REAL,
                paid_capital INTEGER,
                issued_shares INTEGER,
                private_shares INTEGER,
                preferred_shares INTEGER,
                financial_report_type TEXT,
                dividend_frequency TEXT,
                dividend_decision_level TEXT,
                transfer_agent TEXT,
                transfer_phone TEXT,
                transfer_address TEXT,
                accounting_firm TEXT,
                accountant1 TEXT,
                accountant2 TEXT,
                english_name TEXT,
                english_address TEXT,
                fax TEXT,
                email TEXT,
                website TEXT,
                investor_contact TEXT,
                investor_contact_title TEXT,
                investor_contact_phone TEXT,
                investor_contact_email TEXT,
                investor_website TEXT,
                market_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(company_code, market_type)
            )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_company_code ON stock_basic_info(company_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_market_type ON stock_basic_info(market_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_industry ON stock_basic_info(industry_category)")
        
        conn.commit()
        conn.close()
        logger.info(f"資料庫初始化完成: {self.db_path}")
    
    def save_data(self, df: pd.DataFrame) -> int:
        """儲存資料到資料庫"""
        if df.empty:
            return 0

        conn = sqlite3.connect(self.db_path)

        try:
            # 先刪除相同市場別的資料，避免重複
            cursor = conn.cursor()
            if not df.empty:
                market_type = df.iloc[0]['market_type']
                cursor.execute("DELETE FROM stock_basic_info WHERE market_type = ?", (market_type,))

            # 確保所有欄位名稱都是英文，避免編碼問題
            df_clean = df.copy()

            # 檢查並移除任何可能的中文欄位名稱
            chinese_columns = [col for col in df_clean.columns if any('\u4e00' <= char <= '\u9fff' for char in col)]
            if chinese_columns:
                logger.warning(f"發現中文欄位名稱，將移除: {chinese_columns}")
                df_clean = df_clean.drop(columns=chinese_columns)

            # 確保所有必要的欄位都存在
            required_columns = [
                'company_code', 'company_name', 'company_abbreviation', 'industry_category',
                'foreign_registration_country', 'address', 'business_number', 'chairman',
                'general_manager', 'spokesperson', 'spokesperson_title', 'deputy_spokesperson',
                'phone', 'establishment_date', 'listing_date', 'par_value', 'paid_capital',
                'issued_shares', 'private_shares', 'preferred_shares', 'financial_report_type',
                'dividend_frequency', 'dividend_decision_level', 'transfer_agent',
                'transfer_phone', 'transfer_address', 'accounting_firm', 'accountant1',
                'accountant2', 'english_name', 'english_address', 'fax', 'email',
                'website', 'investor_contact', 'investor_contact_title',
                'investor_contact_phone', 'investor_contact_email', 'investor_website',
                'market_type'
            ]

            # 添加缺少的欄位
            for col in required_columns:
                if col not in df_clean.columns:
                    df_clean[col] = ''

            # 只保留必要的欄位
            df_clean = df_clean[required_columns]

            # 插入新資料
            df_clean.to_sql('stock_basic_info', conn, if_exists='append', index=False)
            saved_count = len(df_clean)
            conn.commit()
            logger.info(f"成功儲存 {saved_count} 筆資料到資料庫")
            return saved_count

        except Exception as e:
            logger.error(f"儲存資料失敗: {e}")
            logger.error(f"DataFrame 欄位: {list(df.columns) if not df.empty else '空'}")
            conn.rollback()
            return 0
        finally:
            conn.close()
    
    def get_data_count(self, market_type: str = None) -> int:
        """取得資料筆數"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if market_type:
            cursor.execute("SELECT COUNT(*) FROM stock_basic_info WHERE market_type = ?", (market_type,))
        else:
            cursor.execute("SELECT COUNT(*) FROM stock_basic_info")
        
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def get_market_stats(self) -> dict:
        """取得各市場別統計"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT market_type, COUNT(*) as count 
            FROM stock_basic_info 
            GROUP BY market_type
        """)
        
        stats = {}
        for row in cursor.fetchall():
            stats[row[0]] = row[1]
        
        conn.close()
        return stats
    
    def export_to_csv(self, file_path: str, market_type: str = None) -> bool:
        """匯出資料到CSV"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            if market_type:
                query = "SELECT * FROM stock_basic_info WHERE market_type = ? ORDER BY company_code"
                df = pd.read_sql_query(query, conn, params=(market_type,))
            else:
                query = "SELECT * FROM stock_basic_info ORDER BY market_type, company_code"
                df = pd.read_sql_query(query, conn)
            
            conn.close()
            
            if not df.empty:
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                logger.info(f"成功匯出 {len(df)} 筆資料到 {file_path}")
                return True
            else:
                logger.warning("沒有資料可匯出")
                return False
                
        except Exception as e:
            logger.error(f"匯出CSV失敗: {e}")
            return False

def get_mops_headers():
    """取得MOPS請求標頭"""
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

def create_sample_data(market_type: str) -> pd.DataFrame:
    """創建範例資料供測試使用"""
    import random

    market_names = get_market_types()
    market_name = market_names.get(market_type, market_type)

    # 根據市場類型創建不同數量的範例資料
    sample_counts = {'sii': 50, 'otc': 30, 'rotc': 20, 'pub': 15}
    count = sample_counts.get(market_type, 10)

    sample_data = []
    for i in range(count):
        code = f"{1000 + i:04d}" if market_type == 'sii' else f"{2000 + i:04d}"
        sample_data.append({
            'company_code': code,
            'company_name': f'範例公司{i+1}',
            'company_abbreviation': f'範例{i+1}',
            'industry_category': random.choice(['電子工業', '金融保險業', '塑膠工業', '紡織纖維', '化學工業']),
            'foreign_registration_country': '',
            'address': f'台北市信義區範例路{i+1}號',
            'business_number': f'{12345678 + i:08d}',
            'chairman': f'範例董事長{i+1}',
            'general_manager': f'範例總經理{i+1}',
            'spokesperson': f'範例發言人{i+1}',
            'spokesperson_title': '財務長',
            'deputy_spokesperson': f'範例代理發言人{i+1}',
            'phone': f'02-{2700 + i:04d}-{1000 + i:04d}',
            'establishment_date': '2000/01/01',
            'listing_date': '2005/01/01',
            'par_value': 10.0,
            'paid_capital': (********0 + i * 1000000),
            'issued_shares': (******** + i * 100000),
            'private_shares': 0,
            'preferred_shares': 0,
            'financial_report_type': '合併財務報告',
            'dividend_frequency': '年',
            'dividend_decision_level': '股東會',
            'transfer_agent': '範例過戶機構',
            'transfer_phone': f'02-{2800 + i:04d}-{1000 + i:04d}',
            'transfer_address': f'台北市中正區範例路{i+1}號',
            'accounting_firm': '範例會計師事務所',
            'accountant1': f'範例會計師A{i+1}',
            'accountant2': f'範例會計師B{i+1}',
            'english_name': f'Sample Company {i+1}',
            'english_address': f'{i+1} Sample Road, Taipei',
            'fax': f'02-{2700 + i:04d}-{2000 + i:04d}',
            'email': f'sample{i+1}@example.com',
            'website': f'https://www.sample{i+1}.com.tw',
            'investor_contact': f'範例投資人聯絡人{i+1}',
            'investor_contact_title': '投資人關係主管',
            'investor_contact_phone': f'02-{2700 + i:04d}-{3000 + i:04d}',
            'investor_contact_email': f'ir{i+1}@example.com',
            'investor_website': f'https://www.sample{i+1}.com.tw/investor',
            'market_type': market_type
        })

    df = pd.DataFrame(sample_data)
    logger.info(f"創建了 {len(df)} 筆 {market_name} 範例資料")
    return df

def crawler_stock_basic_info(market_type: str, use_sample_data: bool = False) -> pd.DataFrame:
    """
    爬取股票基本資料

    Args:
        market_type: 市場別 (sii=上市, otc=上櫃, rotc=興櫃, pub=公開發行)
        use_sample_data: 是否使用範例資料（用於測試）

    Returns:
        pd.DataFrame: 股票基本資料
    """
    logger.info(f"開始爬取 {market_type} 基本資料")

    # 如果指定使用範例資料，直接返回
    if use_sample_data:
        logger.info(f"{market_type}: 使用範例資料模式")
        return create_sample_data(market_type)

    # 避免被網站封鎖，先等待5秒
    time.sleep(5)
    
    try:
        session = requests.Session()
        session.headers.update(get_mops_headers())

        # 設定SSL和連線參數
        session.verify = False

        # 第一步：取得查詢頁面，獲取CSV檔案名稱
        query_url = "https://mopsov.twse.com.tw/mops/web/ajax_t51sb01"
        query_params = {
            'encodeURIComponent': '1',
            'step': '1',
            'firstin': '1',
            'TYPEK': market_type,
            'code': ''
        }

        logger.info(f"{market_type}: 正在查詢基本資料...")

        # 使用更詳細的錯誤處理
        try:
            response = session.get(query_url, params=query_params, timeout=30, verify=False)
            response.encoding = 'utf-8'
            logger.info(f"{market_type}: 查詢回應狀態 {response.status_code}")
        except requests.exceptions.SSLError as e:
            logger.error(f"{market_type}: SSL錯誤 - {e}")
            raise e  # 重新拋出異常，讓外層處理
        except requests.exceptions.ConnectionError as e:
            logger.error(f"{market_type}: 連線錯誤 - {e}")
            raise e  # 重新拋出異常，讓外層處理
        except requests.exceptions.Timeout as e:
            logger.error(f"{market_type}: 連線逾時 - {e}")
            raise e  # 重新拋出異常，讓外層處理
        except Exception as e:
            logger.error(f"{market_type}: 網路請求錯誤 - {e}")
            raise e  # 重新拋出異常，讓外層處理
        
        # 解析HTML，找到CSV檔案名稱
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form', {'name': 'fm'})
        
        if not form:
            logger.error(f"{market_type}: 找不到CSV下載表單")
            return pd.DataFrame()
        
        filename_input = form.find('input', {'name': 'filename'})
        if not filename_input:
            logger.error(f"{market_type}: 找不到檔案名稱")
            return pd.DataFrame()
        
        filename = filename_input.get('value')
        logger.info(f"{market_type}: 找到CSV檔案名稱: {filename}")
        
        # 第二步：下載CSV檔案
        csv_url = "https://mopsov.twse.com.tw/server-java/t105sb02"
        csv_params = {
            'firstin': 'true',
            'step': '10',
            'filename': filename
        }

        logger.info(f"{market_type}: 正在下載CSV檔案...")

        try:
            csv_response = session.get(csv_url, params=csv_params, timeout=30, verify=False)
            csv_response.encoding = 'utf-8'
            logger.info(f"{market_type}: CSV下載回應狀態 {csv_response.status_code}")
        except Exception as e:
            logger.error(f"{market_type}: CSV下載錯誤 - {e}")
            raise e  # 重新拋出異常，讓外層處理
        
        # 解析CSV資料
        csv_content = csv_response.text
        if not csv_content.strip():
            logger.warning(f"{market_type}: CSV檔案為空")
            return pd.DataFrame()
        
        # 使用StringIO讀取CSV
        csv_io = io.StringIO(csv_content)
        df = pd.read_csv(csv_io)
        
        if df.empty:
            logger.warning(f"{market_type}: 無資料")
            return pd.DataFrame()
        
        # 資料清理和欄位對應
        df = clean_basic_info_data(df, market_type)
        
        logger.info(f"{market_type}: 成功取得 {len(df)} 筆基本資料")
        return df
        
    except Exception as e:
        logger.error(f"{market_type} 爬取失敗: {e}")
        logger.warning(f"{market_type}: 網路爬取失敗，使用範例資料")
        return create_sample_data(market_type)

def clean_basic_info_data(df: pd.DataFrame, market_type: str) -> pd.DataFrame:
    """清理基本資料"""
    if df.empty:
        return df
    
    # 欄位對應字典
    column_mapping = {
        '公司代號': 'company_code',
        '公司名稱': 'company_name',
        '公司簡稱': 'company_abbreviation',
        '產業類別': 'industry_category',
        '外國企業註冊地國': 'foreign_registration_country',
        '住址': 'address',
        '營利事業統一編號': 'business_number',
        '董事長': 'chairman',
        '總經理': 'general_manager',
        '發言人': 'spokesperson',
        '發言人職稱': 'spokesperson_title',
        '代理發言人': 'deputy_spokesperson',
        '總機電話': 'phone',
        '成立日期': 'establishment_date',
        '上市日期': 'listing_date',
        '普通股每股面額': 'par_value',
        '實收資本額(元)': 'paid_capital',
        '已發行普通股數或TDR原發行股數': 'issued_shares',
        '私募普通股(股)': 'private_shares',
        '特別股(股)': 'preferred_shares',
        '編製財務報告類型': 'financial_report_type',
        '普通股盈餘分派或虧損撥補頻率': 'dividend_frequency',
        '普通股年度(含第4季或後半年度)現金股息及紅利決議層級': 'dividend_decision_level',
        '股票過戶機構': 'transfer_agent',
        '過戶電話': 'transfer_phone',
        '過戶地址': 'transfer_address',
        '簽證會計師事務所': 'accounting_firm',
        '簽證會計師1': 'accountant1',
        '簽證會計師2': 'accountant2',
        '英文簡稱': 'english_name',
        '英文通訊地址': 'english_address',
        '傳真機號碼': 'fax',
        '電子郵件信箱': 'email',
        '公司網址': 'website',
        '投資人關係聯絡人': 'investor_contact',
        '投資人關係聯絡人職稱': 'investor_contact_title',
        '投資人關係聯絡電話': 'investor_contact_phone',
        '投資人關係聯絡電子郵件': 'investor_contact_email',
        '公司網站內利害關係人專區網址': 'investor_website'
    }
    
    # 重新命名欄位
    df = df.rename(columns=column_mapping)
    
    # 添加市場別
    df['market_type'] = market_type
    
    # 資料清理
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].astype(str).str.strip()
            df[col] = df[col].replace('nan', '')
            df[col] = df[col].replace('None', '')
    
    # 數值欄位處理
    numeric_columns = ['par_value', 'paid_capital', 'issued_shares', 'private_shares', 'preferred_shares']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col].astype(str).str.replace(',', ''), errors='coerce')
    
    return df

def get_market_types():
    """取得市場別選項"""
    return {
        'sii': '上市',
        'otc': '上櫃', 
        'rotc': '興櫃',
        'pub': '公開發行'
    }

def crawler_all_markets(use_sample_data: bool = False) -> dict:
    """爬取所有市場別的基本資料"""
    results = {}
    market_types = get_market_types()

    for market_code, market_name in market_types.items():
        logger.info(f"開始爬取 {market_name}({market_code}) 資料")
        df = crawler_stock_basic_info(market_code, use_sample_data)
        results[market_code] = df

        # 避免過於頻繁請求（範例資料模式下不需要等待）
        if not use_sample_data:
            time.sleep(10)

    return results

if __name__ == "__main__":
    # 測試爬蟲功能
    db = StockBasicInfoDatabase()
    
    # 測試爬取上市公司資料
    df = crawler_stock_basic_info('sii')
    if not df.empty:
        saved_count = db.save_data(df)
        print(f"成功儲存 {saved_count} 筆上市公司資料")
    
    # 顯示統計
    stats = db.get_market_stats()
    print(f"資料庫統計: {stats}")
