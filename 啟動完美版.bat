@echo off
chcp 65001 >nul
cd /d "%~dp0"
title 台股智能選股系統 - 完美版

echo.
echo ========================================
echo    🚀 台股智能選股系統 v22.0 🚀
echo        完美版 - 完全穩定無錯誤
echo ========================================
echo.

if exist "dist\StockAnalyzer_Perfect.exe" (
    echo ✅ 找到完美版
    echo 🚀 正在啟動...
    echo.
    echo 💡 完美版特點：
    echo    ✓ 完全無錯誤和警告訊息
    echo    ✓ 最穩定的運行環境
    echo    ✓ 核心功能完整保留
    echo    ✓ 最佳用戶體驗
    echo.
    
    cd /d "dist"
    start "" "StockAnalyzer_Perfect.exe"
    
    echo ✅ 完美版已啟動！
    echo.
    
) else (
    echo ❌ 錯誤：找不到完美版
    echo.
    echo 請重新編譯：
    echo    python perfect_compile.py
    echo.
    pause
    exit /b 1
)

echo 📋 核心功能：
echo    ✓ 股票列表和篩選
echo    ✓ 數據查詢和顯示
echo    ✓ Excel 報告導出
echo    ✓ 完整用戶界面
echo    ✓ 穩定可靠運行
echo.

timeout /t 3 >nul
