#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試PKL版高殖利率烏龜策略整合
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加strategies目錄到路徑
sys.path.append('strategies')

def test_pkl_strategy_integration():
    """測試PKL策略整合"""
    print("🧪 測試PKL版高殖利率烏龜策略整合")
    print("=" * 60)
    
    try:
        # 導入策略
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        print("✅ 策略模組導入成功")
        
        # 初始化策略
        strategy = HighYieldTurtleStrategy()
        print(f"✅ 策略初始化成功: {strategy.name}")
        
        # 檢查PKL數據載入狀態
        if strategy.pe_data_cache is not None:
            print(f"✅ PKL數據載入成功: {strategy.pe_data_cache.shape[0]} 筆記錄")
            
            # 顯示PKL數據樣本
            sample_stocks = strategy.pe_data_cache['股票代號'].head(5).tolist()
            print(f"📊 樣本股票: {sample_stocks}")
            
        else:
            print("❌ PKL數據載入失敗")
            return False
        
        # 創建測試用的股價數據
        def create_test_price_data(stock_id, days=100):
            """創建測試用股價數據"""
            dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
            
            # 模擬股價數據
            np.random.seed(42)
            base_price = 50
            prices = []
            current_price = base_price
            
            for i in range(days):
                change = np.random.normal(0, 0.02)  # 2%標準差
                current_price *= (1 + change)
                prices.append(current_price)
            
            # 創建OHLCV數據
            df = pd.DataFrame({
                'Date': dates,
                'Open': [p * np.random.uniform(0.99, 1.01) for p in prices],
                'High': [p * np.random.uniform(1.00, 1.05) for p in prices],
                'Low': [p * np.random.uniform(0.95, 1.00) for p in prices],
                'Close': prices,
                'Volume': [np.random.randint(100000, 1000000) for _ in range(days)]
            })
            
            df.set_index('Date', inplace=True)
            return df
        
        # 測試幾支股票
        test_stocks = ['1101', '1102', '1108', '1109', '2330']
        
        print(f"\n🧪 測試股票策略分析:")
        print("-" * 50)
        
        success_count = 0
        for stock_id in test_stocks:
            try:
                # 創建測試數據
                test_df = create_test_price_data(stock_id)
                
                # 執行策略分析
                result = strategy.analyze_stock(test_df, stock_id=stock_id)
                
                # 顯示結果
                status = "✅ 符合" if result['suitable'] else "❌ 不符合"
                score = result.get('score', 0)
                reason = result.get('reason', '無原因')
                
                print(f"{status} {stock_id}: 評分 {score}/100")
                print(f"    原因: {reason[:100]}...")
                
                if result['suitable']:
                    success_count += 1
                
                # 顯示詳細資訊
                details = result.get('details', {})
                if details:
                    data_source = details.get('data_source', '未知')
                    dividend_yield = details.get('dividend_yield', 0)
                    pe_ratio = details.get('pe_ratio', 0)
                    print(f"    數據來源: {data_source}")
                    print(f"    殖利率: {dividend_yield:.1f}%, PE: {pe_ratio:.1f}")
                
                print()
                
            except Exception as e:
                print(f"❌ {stock_id}: 測試失敗 - {e}")
        
        print(f"📊 測試結果: {success_count}/{len(test_stocks)} 支股票符合策略")
        
        # 測試PKL數據獲取
        print(f"\n🔍 測試PKL數據獲取:")
        print("-" * 30)
        
        for stock_id in ['1101', '1108', '2330']:
            pkl_data = strategy.get_stock_pkl_data(stock_id)
            if pkl_data:
                print(f"✅ {stock_id}: {pkl_data['stock_name']}")
                print(f"    殖利率: {pkl_data['dividend_yield']:.1f}%")
                print(f"    PE: {pkl_data['pe_ratio']:.1f}, PB: {pkl_data['pb_ratio']:.1f}")
                print(f"    市場: {pkl_data['market']}, 日期: {pkl_data['date']}")
            else:
                print(f"❌ {stock_id}: 無PKL數據")
            print()
        
        return True
        
    except ImportError as e:
        print(f"❌ 策略模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_program_integration():
    """測試主程序整合"""
    print("\n🔧 測試主程序整合")
    print("=" * 60)
    
    try:
        # 檢查主程序檔案修改
        main_file = "O3mh_gui_v21_optimized.py"
        
        if not os.path.exists(main_file):
            print(f"❌ 主程序檔案不存在: {main_file}")
            return False
        
        # 讀取主程序內容
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵修改
        checks = [
            ('PKL真實數據策略', '"💎 PKL真實數據策略"' in content),
            ('高殖利率烏龜移出模擬', '"高殖利率烏龜"' not in content.split('simulated_data_strategies = [')[1].split(']')[0] if 'simulated_data_strategies = [' in content else True),
            ('高殖利率烏龜加入真實', '"高殖利率烏龜"' in content.split('real_data_strategies = [')[1].split(']')[0] if 'real_data_strategies = [' in content else False),
        ]
        
        print("📋 主程序整合檢查:")
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
        
        all_passed = all(check[1] for check in checks)
        
        if all_passed:
            print("✅ 主程序整合檢查通過")
        else:
            print("❌ 主程序整合檢查失敗")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 主程序整合檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 PKL策略整合測試套件")
    print("=" * 80)
    
    # 測試策略整合
    strategy_test = test_pkl_strategy_integration()
    
    # 測試主程序整合
    main_test = test_main_program_integration()
    
    print("\n" + "=" * 80)
    print("📊 整合測試總結")
    print("=" * 80)
    
    results = [
        ("PKL策略功能", strategy_test),
        ("主程序整合", main_test)
    ]
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 總體結果: {passed_count}/{total_count} 測試通過")
    
    if passed_count == total_count:
        print("🎉 PKL策略整合成功！")
        print("\n📖 下一步:")
        print("  1. 運行主程序測試策略選單")
        print("  2. 測試策略交集功能")
        print("  3. 驗證PKL真實數據的準確性")
        print("  4. 開始開發下一個PKL策略")
    else:
        print("⚠️ 部分測試失敗，請檢查相關配置")
    
    return passed_count == total_count

if __name__ == "__main__":
    main()
