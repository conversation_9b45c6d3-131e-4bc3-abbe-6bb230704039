#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試右鍵選單觸發
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_right_click_trigger():
    """測試右鍵選單觸發"""
    print("🖱️ 測試右鍵選單觸發...")
    
    try:
        # 修復兼容性問題
        import warnings
        warnings.filterwarnings('ignore')
        
        try:
            import numpy._core.numeric
        except ImportError:
            try:
                import numpy.core.numeric as numpy_core_numeric
                sys.modules['numpy._core.numeric'] = numpy_core_numeric
            except ImportError:
                pass
        
        from PyQt6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 導入主GUI
        from O3mh_gui_v21_optimized import StockScreenerGUI
        gui = StockScreenerGUI()
        
        # 檢查右鍵選單設置
        table = gui.result_table
        
        print(f"📊 表格物件: {type(table)}")
        print(f"🔧 右鍵選單策略: {table.contextMenuPolicy()}")
        
        # 檢查信號連接
        from PyQt6.QtCore import Qt
        expected_policy = Qt.ContextMenuPolicy.CustomContextMenu
        actual_policy = table.contextMenuPolicy()
        
        if actual_policy == expected_policy:
            print("✅ 右鍵選單策略設置正確")
        else:
            print(f"❌ 右鍵選單策略設置錯誤: 預期 {expected_policy}, 實際 {actual_policy}")
        
        # 檢查信號連接
        signal = table.customContextMenuRequested
        receivers_count = table.receivers(signal)
        print(f"📡 信號接收者數量: {receivers_count}")
        
        if receivers_count > 0:
            print("✅ 信號連接正常")
        else:
            print("❌ 信號連接失敗")
        
        # 嘗試手動觸發右鍵選單函數
        print("\n🧪 嘗試手動觸發右鍵選單函數...")
        
        # 添加一些測試資料到表格
        from PyQt6.QtWidgets import QTableWidgetItem
        table.setRowCount(3)
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "價格"])
        
        test_data = [
            ("2330", "台積電", "580"),
            ("2317", "鴻海", "105"),
            ("1101", "台泥", "45")
        ]
        
        for row, (code, name, price) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(code))
            table.setItem(row, 1, QTableWidgetItem(name))
            table.setItem(row, 2, QTableWidgetItem(price))
        
        print("✅ 測試資料已添加到表格")
        
        # 手動觸發右鍵選單函數
        from PyQt6.QtCore import QPoint
        test_position = QPoint(100, 50)
        
        print(f"🎯 手動觸發右鍵選單，位置: {test_position}")
        
        try:
            gui.show_stock_context_menu(test_position)
            print("✅ 右鍵選單函數執行完成")
        except Exception as e:
            print(f"❌ 右鍵選單函數執行失敗: {e}")
            import traceback
            traceback.print_exc()
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_test():
    """創建最小化測試"""
    print("\n🎯 創建最小化右鍵選單測試...")
    
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, 
                                   QTableWidgetItem, QMenu, QMessageBox)
        from PyQt6.QtCore import Qt, QPoint
        
        class MinimalTest(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("最小化右鍵選單測試")
                self.setGeometry(100, 100, 600, 400)
                
                # 創建表格
                self.table = QTableWidget(3, 3)
                self.table.setHorizontalHeaderLabels(["股票代碼", "股票名稱", "價格"])
                
                # 添加測試資料
                test_data = [
                    ("2330", "台積電", "580"),
                    ("2317", "鴻海", "105"),
                    ("1101", "台泥", "45")
                ]
                
                for row, (code, name, price) in enumerate(test_data):
                    self.table.setItem(row, 0, QTableWidgetItem(code))
                    self.table.setItem(row, 1, QTableWidgetItem(name))
                    self.table.setItem(row, 2, QTableWidgetItem(price))
                
                # 設置右鍵選單
                self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.table.customContextMenuRequested.connect(self.show_context_menu)
                
                self.setCentralWidget(self.table)
                
                print("✅ 最小化測試創建成功")
                print("🖱️ 請在表格上右鍵點擊測試")
            
            def show_context_menu(self, position):
                """顯示右鍵選單"""
                print(f"🖱️ 右鍵選單被觸發！位置: {position}")
                
                try:
                    # 獲取點擊的項目
                    item = self.table.itemAt(position)
                    if not item:
                        print("❌ 沒有點擊到有效項目")
                        return
                    
                    row = item.row()
                    stock_code = self.table.item(row, 0).text()
                    stock_name = self.table.item(row, 1).text()
                    
                    print(f"📊 選中股票: {stock_code} {stock_name}")
                    
                    # 創建選單
                    menu = QMenu(self)
                    
                    # 添加選項
                    action1 = menu.addAction(f"📊 {stock_code} 月營收評估")
                    action1.triggered.connect(lambda: self.show_message(f"{stock_code} 月營收評估"))
                    
                    action2 = menu.addAction(f"📰 {stock_code} 股票新聞")
                    action2.triggered.connect(lambda: self.show_message(f"{stock_code} 股票新聞"))
                    
                    # 顯示選單
                    menu.exec(self.table.mapToGlobal(position))
                    print("✅ 右鍵選單顯示成功")
                    
                except Exception as e:
                    print(f"❌ 右鍵選單錯誤: {e}")
                    import traceback
                    traceback.print_exc()
            
            def show_message(self, message):
                """顯示訊息"""
                print(f"💬 顯示訊息: {message}")
                QMessageBox.information(self, "測試", f"功能正常：{message}")
        
        app = QApplication(sys.argv)
        window = MinimalTest()
        window.show()
        
        print("\n📋 測試說明:")
        print("1. 在表格中的任意股票行上右鍵點擊")
        print("2. 檢查控制台是否顯示觸發訊息")
        print("3. 檢查是否出現右鍵選單")
        print("4. 點擊選單項目測試功能")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 最小化測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 右鍵選單觸發診斷工具")
    print("=" * 50)
    
    # 測試右鍵選單觸發
    trigger_ok = test_right_click_trigger()
    
    print("\n" + "=" * 50)
    print("🎯 測試結果:")
    print(f"  右鍵選單觸發: {'✅ 正常' if trigger_ok else '❌ 異常'}")
    
    if trigger_ok:
        print("\n🎉 基本測試通過！")
        print("🎯 啟動最小化測試驗證...")
        create_minimal_test()
    else:
        print("\n⚠️ 基本測試失敗")
        print("🔧 嘗試啟動最小化測試...")
        create_minimal_test()

if __name__ == "__main__":
    main()
