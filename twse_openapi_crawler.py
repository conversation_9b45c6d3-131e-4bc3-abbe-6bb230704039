#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基於 TWSEMCPServer 的台灣證交所 OpenAPI 爬蟲
支援綜合損益表和資產負債表
"""

import requests
import pandas as pd
import sqlite3
import os
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TWSEOpenAPIClient:
    """台灣證交所 OpenAPI 客戶端"""
    
    BASE_URL = "https://openapi.twse.com.tw/v1"
    USER_AGENT = "finlab-crawler/1.0"
    
    @classmethod
    def get_data(cls, endpoint: str, timeout: float = 30.0) -> List[Dict[str, Any]]:
        """
        從 TWSE API 端點獲取資料
        
        Args:
            endpoint: API 端點路徑
            timeout: 請求超時時間
            
        Returns:
            包含 API 回應資料的字典列表
        """
        url = f"{cls.BASE_URL}{endpoint}"
        logger.info(f"🔍 獲取 TWSE 資料: {url}")
        
        try:
            # 跳過 SSL 憑證驗證，設置正確的標頭
            resp = requests.get(
                url,
                headers={
                    "User-Agent": cls.USER_AGENT,
                    "Accept": "application/json"
                },
                verify=False,
                timeout=timeout
            )
            resp.raise_for_status()
            
            # 設定正確的編碼
            resp.encoding = 'utf-8'
            data = resp.json()
            
            return data if isinstance(data, list) else [data] if data else []
            
        except Exception as e:
            logger.error(f"❌ 獲取資料失敗 {url}: {e}")
            raise
    
    @classmethod
    def get_company_data(cls, endpoint: str, code: str, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """
        獲取特定公司的資料
        
        Args:
            endpoint: API 端點路徑
            code: 公司股票代碼
            timeout: 請求超時時間
            
        Returns:
            包含公司資料的字典，如果未找到則返回 None
        """
        try:
            data = cls.get_data(endpoint, timeout)
            
            # 根據公司代碼過濾資料
            filtered_data = [
                item for item in data 
                if isinstance(item, dict) and (
                    item.get("公司代號") == code or 
                    item.get("Code") == code or
                    item.get("證券代號") == code
                )
            ]
            
            return filtered_data[0] if filtered_data else None
            
        except Exception as e:
            logger.error(f"❌ 獲取公司 {code} 資料失敗: {e}")
            return None

def convert_roc_date_to_standard(roc_date_str):
    """
    轉換民國年日期為標準日期格式

    Args:
        roc_date_str: 民國年日期字串，如 '1140726'

    Returns:
        str: 標準日期格式，如 '2025-07-26'
    """
    try:
        if not roc_date_str or len(str(roc_date_str)) != 7:
            return None

        roc_date_str = str(roc_date_str)
        roc_year = int(roc_date_str[:3])  # 前3位是民國年
        month = roc_date_str[3:5]         # 中間2位是月
        day = roc_date_str[5:7]           # 後2位是日

        # 轉換為西元年
        western_year = roc_year + 1911

        return f"{western_year}-{month}-{day}"

    except Exception as e:
        print(f"⚠️ 日期轉換失敗: {roc_date_str} -> {e}")
        return None

def crawl_income_statements():
    """爬取綜合損益表資料"""

    print("=" * 80)
    print("📊 爬取綜合損益表資料 (使用 TWSE OpenAPI)")
    print("=" * 80)

    try:
        client = TWSEOpenAPIClient()

        # 獲取所有綜合損益表資料
        print("🔍 獲取綜合損益表資料...")
        raw_data = client.get_data("/opendata/t187ap06_L_ci")

        if not raw_data:
            print("❌ 無法獲取綜合損益表資料")
            return pd.DataFrame()

        print(f"✅ 獲取到 {len(raw_data)} 筆綜合損益表資料")

        # 轉換為 DataFrame
        df = pd.DataFrame(raw_data)

        # 資料清理和標準化
        if '公司代號' in df.columns:
            df = df.rename(columns={'公司代號': 'stock_id'})
        elif 'Code' in df.columns:
            df = df.rename(columns={'Code': 'stock_id'})
        elif '證券代號' in df.columns:
            df = df.rename(columns={'證券代號': 'stock_id'})

        # 添加股票名稱
        if '公司名稱' in df.columns:
            df = df.rename(columns={'公司名稱': 'stock_name'})
        elif 'Name' in df.columns:
            df = df.rename(columns={'Name': 'stock_name'})

        # 轉換民國年為西元年
        if '年度' in df.columns:
            df['year'] = df['年度'].astype(int) + 1911
        if '季別' in df.columns:
            df['quarter'] = df['季別'].astype(int)

        # 轉換出表日期為標準日期格式
        if '出表日期' in df.columns:
            df['report_date'] = df['出表日期'].apply(convert_roc_date_to_standard)

        # 添加資料日期
        df['date'] = datetime.now().strftime('%Y-%m-%d')
        df['data_type'] = 'income_statement'

        # 確保 stock_id 為字串
        if 'stock_id' in df.columns:
            df['stock_id'] = df['stock_id'].astype(str)

        print(f"📊 處理後資料形狀: {df.shape}")
        print(f"📋 主要欄位: {list(df.columns[:10])}")

        return df
        
    except Exception as e:
        print(f"❌ 爬取綜合損益表失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def crawl_balance_sheets():
    """爬取資產負債表資料"""
    
    print("=" * 80)
    print("📊 爬取資產負債表資料 (使用 TWSE OpenAPI)")
    print("=" * 80)
    
    try:
        client = TWSEOpenAPIClient()
        
        # 獲取所有資產負債表資料
        print("🔍 獲取資產負債表資料...")
        raw_data = client.get_data("/opendata/t187ap07_L_ci")
        
        if not raw_data:
            print("❌ 無法獲取資產負債表資料")
            return pd.DataFrame()
        
        print(f"✅ 獲取到 {len(raw_data)} 筆資產負債表資料")
        
        # 轉換為 DataFrame
        df = pd.DataFrame(raw_data)
        
        # 資料清理和標準化
        if '公司代號' in df.columns:
            df = df.rename(columns={'公司代號': 'stock_id'})
        elif 'Code' in df.columns:
            df = df.rename(columns={'Code': 'stock_id'})
        elif '證券代號' in df.columns:
            df = df.rename(columns={'證券代號': 'stock_id'})
        
        # 添加股票名稱
        if '公司名稱' in df.columns:
            df = df.rename(columns={'公司名稱': 'stock_name'})
        elif 'Name' in df.columns:
            df = df.rename(columns={'Name': 'stock_name'})

        # 轉換民國年為西元年
        if '年度' in df.columns:
            df['year'] = df['年度'].astype(int) + 1911
        if '季別' in df.columns:
            df['quarter'] = df['季別'].astype(int)

        # 轉換出表日期為標準日期格式
        if '出表日期' in df.columns:
            df['report_date'] = df['出表日期'].apply(convert_roc_date_to_standard)

        # 添加資料日期
        df['date'] = datetime.now().strftime('%Y-%m-%d')
        df['data_type'] = 'balance_sheet'

        # 確保 stock_id 為字串
        if 'stock_id' in df.columns:
            df['stock_id'] = df['stock_id'].astype(str)
        
        print(f"📊 處理後資料形狀: {df.shape}")
        print(f"📋 主要欄位: {list(df.columns[:10])}")
        
        return df
        
    except Exception as e:
        print(f"❌ 爬取資產負債表失敗: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def save_financial_data_to_db(income_df, balance_df):
    """將財務資料儲存到資料庫"""

    print(f"\n💾 儲存財務資料到資料庫...")

    try:
        # 確保目錄存在 - 使用絕對路徑
        target_dir = r'D:\Finlab\history\tables'
        os.makedirs(target_dir, exist_ok=True)

        # 儲存綜合損益表
        if not income_df.empty:
            income_db_path = os.path.join(target_dir, 'income_statements.db')
            conn = sqlite3.connect(income_db_path)
            income_df.to_sql('income_statements', conn, if_exists='replace', index=False)
            conn.close()
            print(f"✅ 綜合損益表已儲存: {income_db_path} ({len(income_df)} 筆)")

        # 儲存資產負債表
        if not balance_df.empty:
            balance_db_path = os.path.join(target_dir, 'balance_sheets.db')
            conn = sqlite3.connect(balance_db_path)
            balance_df.to_sql('balance_sheets', conn, if_exists='replace', index=False)
            conn.close()
            print(f"✅ 資產負債表已儲存: {balance_db_path} ({len(balance_df)} 筆)")

        # 儲存合併的財務資料
        if not income_df.empty or not balance_df.empty:
            combined_db_path = os.path.join(target_dir, 'financial_statements.db')
            conn = sqlite3.connect(combined_db_path)

            if not income_df.empty:
                income_df.to_sql('income_statements', conn, if_exists='replace', index=False)

            if not balance_df.empty:
                balance_df.to_sql('balance_sheets', conn, if_exists='replace', index=False)

            conn.close()
            print(f"✅ 合併財務資料已儲存: {combined_db_path}")

        return True

    except Exception as e:
        print(f"❌ 儲存財務資料失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_company(stock_code="2330"):
    """測試特定公司的財務資料獲取"""
    
    print(f"\n🔍 測試特定公司財務資料獲取 ({stock_code})")
    print("-" * 60)
    
    try:
        client = TWSEOpenAPIClient()
        
        # 測試綜合損益表
        print(f"📊 獲取 {stock_code} 綜合損益表...")
        income_data = client.get_company_data("/opendata/t187ap06_L_ci", stock_code)
        
        if income_data:
            print(f"✅ 綜合損益表資料:")
            for key, value in list(income_data.items())[:5]:
                print(f"   {key}: {value}")
            print(f"   ... (總共 {len(income_data)} 個欄位)")
        else:
            print(f"⚠️ 未找到 {stock_code} 的綜合損益表資料")
        
        # 測試資產負債表
        print(f"\n📊 獲取 {stock_code} 資產負債表...")
        balance_data = client.get_company_data("/opendata/t187ap07_L_ci", stock_code)
        
        if balance_data:
            print(f"✅ 資產負債表資料:")
            for key, value in list(balance_data.items())[:5]:
                print(f"   {key}: {value}")
            print(f"   ... (總共 {len(balance_data)} 個欄位)")
        else:
            print(f"⚠️ 未找到 {stock_code} 的資產負債表資料")
        
        return income_data is not None or balance_data is not None
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🚀 TWSE OpenAPI 財務報表爬蟲")
    print("基於 TWSEMCPServer 架構")
    
    # 測試特定公司
    test_success = test_specific_company("2330")
    
    if not test_success:
        print("⚠️ 特定公司測試失敗，可能是 API 問題")
        return False
    
    # 爬取完整資料
    print(f"\n🔄 開始爬取完整財務報表資料...")
    
    # 爬取綜合損益表
    income_df = crawl_income_statements()
    
    # 等待一下避免 API 限制
    time.sleep(2)
    
    # 爬取資產負債表
    balance_df = crawl_balance_sheets()
    
    # 儲存資料
    save_success = save_financial_data_to_db(income_df, balance_df)
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 爬取總結")
    print(f"=" * 80)
    
    if save_success:
        print(f"🎉 財務報表爬取成功！")
        print(f"📊 綜合損益表: {len(income_df)} 筆")
        print(f"📊 資產負債表: {len(balance_df)} 筆")
        print(f"💾 資料已儲存到 history/tables/ 目錄")
        
        print(f"\n📝 使用方法:")
        print(f"   1. 查詢綜合損益表: SELECT * FROM income_statements WHERE stock_id = '2330'")
        print(f"   2. 查詢資產負債表: SELECT * FROM balance_sheets WHERE stock_id = '2330'")
        print(f"   3. 資料庫位置: history/tables/financial_statements.db")
        
        return True
    else:
        print(f"❌ 財務報表爬取失敗")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
