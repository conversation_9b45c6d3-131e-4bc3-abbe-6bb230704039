#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的 Change 欄位是否正確
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_change_field():
    """測試修正後的 Change 欄位是否正確"""
    
    print("=" * 80)
    print("🧪 測試修正後的 Change 欄位")
    print("=" * 80)
    
    try:
        from crawler import crawl_price
        from auto_update import save_price_to_newprice_database
        
        # 測試一個已知有資料的日期
        test_date = datetime(2023, 9, 21)  # 週四，應該有資料
        
        print(f"🔍 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 步驟1：測試爬蟲是否包含 Change 欄位
        print(f"\n📡 步驟1：測試爬蟲輸出")
        df = crawl_price(test_date)
        
        if df is None or df.empty:
            print(f"❌ 爬取失敗或無資料")
            return False
        
        print(f"✅ 成功爬取 {len(df)} 筆資料")
        
        # 檢查欄位名稱
        df_reset = df.reset_index()
        columns = list(df_reset.columns)
        print(f"📋 爬取的欄位: {columns}")
        
        # 檢查是否包含 Change 欄位
        if 'Change' in columns:
            print(f"✅ 找到 Change 欄位")
            
            # 檢查 Change 欄位的值分布
            change_values = df_reset['Change'].dropna()

            # 檢查 Change 欄位的資料類型
            print(f"📊 Change 欄位資料類型: {change_values.dtype}")
            print(f"📊 Change 欄位前10個值: {list(change_values.head(10))}")

            # 轉換為數值，無法轉換的設為 NaN
            import pandas as pd
            change_numeric = pd.to_numeric(change_values, errors='coerce')

            # 移除 NaN 值
            change_numeric = change_numeric.dropna()

            if len(change_numeric) > 0:
                zero_count = (change_numeric == 0).sum()
                positive_count = (change_numeric > 0).sum()
                negative_count = (change_numeric < 0).sum()
                
                print(f"📊 Change 欄位統計:")
                print(f"   原始總數: {len(change_values)}")
                print(f"   數值總數: {len(change_numeric)}")
                print(f"   = 0: {zero_count} ({zero_count/len(change_numeric)*100:.1f}%)")
                print(f"   > 0: {positive_count} ({positive_count/len(change_numeric)*100:.1f}%)")
                print(f"   < 0: {negative_count} ({negative_count/len(change_numeric)*100:.1f}%)")
                print(f"   範圍: {change_numeric.min():.4f} ~ {change_numeric.max():.4f}")
                
                # 顯示一些範例
                print(f"\n📈 Change 值範例:")
                sample_data = df_reset[['stock_id', 'Close', 'Change']].head(10)
                for _, row in sample_data.iterrows():
                    print(f"   {row['stock_id']}: Close={row['Close']}, Change={row['Change']}")
                
                # 檢查 0050
                etf_0050 = df_reset[df_reset['stock_id'] == '0050']
                if not etf_0050.empty:
                    row = etf_0050.iloc[0]
                    print(f"\n📊 0050 資料:")
                    print(f"   Close: {row['Close']}")
                    print(f"   Change: {row['Change']}")
                
                if zero_count == len(change_numeric):
                    print(f"❌ 所有 Change 值都是 0，修正失敗")
                    return False
                else:
                    print(f"✅ Change 欄位有正確的漲跌值")
            else:
                print(f"❌ Change 欄位沒有有效值")
                return False
        else:
            print(f"❌ 未找到 Change 欄位")
            return False
        
        # 步驟2：測試存儲功能
        print(f"\n💾 步驟2：測試存儲功能")
        db_file = r'D:\Finlab\history\tables\newprice.db'
        
        # 存儲資料
        success = save_price_to_newprice_database(df, db_file)
        
        if not success:
            print(f"❌ 存儲失敗")
            return False
        
        print(f"✅ 存儲成功")
        
        # 步驟3：驗證存儲的 Change 值
        print(f"\n🔍 步驟3：驗證存儲的 Change 值")
        
        conn = sqlite3.connect(db_file)
        
        # 檢查存儲的 Change 值
        query_change = f'''
            SELECT stock_id, [Close], [Change]
            FROM stock_daily_data 
            WHERE date = '{test_date.strftime('%Y-%m-%d')}'
            ORDER BY stock_id
            LIMIT 10
        '''
        
        df_stored = pd.read_sql_query(query_change, conn)
        
        if not df_stored.empty:
            print(f"📊 存儲的 Change 值範例:")
            for _, row in df_stored.iterrows():
                print(f"   {row['stock_id']}: Close={row['Close']}, Change={row['Change']}")
            
            # 統計存儲的 Change 值
            query_stats = f'''
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN [Change] = 0 THEN 1 END) as zero_count,
                    COUNT(CASE WHEN [Change] > 0 THEN 1 END) as positive_count,
                    COUNT(CASE WHEN [Change] < 0 THEN 1 END) as negative_count,
                    AVG([Change]) as avg_change,
                    MIN([Change]) as min_change,
                    MAX([Change]) as max_change
                FROM stock_daily_data
                WHERE date = '{test_date.strftime('%Y-%m-%d')}'
            '''
            
            stats = pd.read_sql_query(query_stats, conn)
            
            print(f"\n📊 存儲的 Change 統計:")
            print(f"   總數: {stats.iloc[0]['total']}")
            print(f"   = 0: {stats.iloc[0]['zero_count']}")
            print(f"   > 0: {stats.iloc[0]['positive_count']}")
            print(f"   < 0: {stats.iloc[0]['negative_count']}")
            print(f"   平均: {stats.iloc[0]['avg_change']:.4f}")
            print(f"   範圍: {stats.iloc[0]['min_change']:.4f} ~ {stats.iloc[0]['max_change']:.4f}")
            
            zero_pct = (stats.iloc[0]['zero_count'] / stats.iloc[0]['total']) * 100
            
            if zero_pct > 90:
                print(f"❌ 存儲的 Change 值大部分為 0 ({zero_pct:.1f}%)，修正失敗")
                return False
            else:
                print(f"✅ 存儲的 Change 值正常 (0值比例: {zero_pct:.1f}%)")
        
        conn.close()
        
        print(f"\n✅ Change 欄位修正成功！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_change_field()
    
    if success:
        print(f"\n🎉 Change 欄位修正完成！")
        print(f"📋 修正總結:")
        print(f"   ✅ 添加了 o2tp 映射中的漲跌欄位")
        print(f"   ✅ 修正了 crawl_price 中的欄位映射")
        print(f"   ✅ 修正了存儲函數使用實際 Change 值")
        print(f"   ✅ Change 欄位現在與 price.db 一致")
        print(f"\n🚀 現在可以執行:")
        print(f"   python auto_update.py price")
        print(f"   Change 欄位將包含正確的漲跌價差值")
    else:
        print(f"\n❌ 仍有問題需要修正")
