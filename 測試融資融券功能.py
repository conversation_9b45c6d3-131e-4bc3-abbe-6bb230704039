#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試融資融券功能
驗證個股融資融券資訊的獲取和顯示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from O3mh_gui_v21_optimized import FinlabGUI
from datetime import datetime

def test_margin_trading_api():
    """測試融資融券API功能"""
    print("🧪 測試融資融券API功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    # 測試融資融券資料獲取
    test_stocks = ['2330', '2317', '2454']  # 台積電、鴻海、聯發科
    test_date = '2025-01-15'
    
    print(f"📅 測試日期: {test_date}")
    print(f"📋 測試股票: {', '.join(test_stocks)}")
    print()
    
    for stock_code in test_stocks:
        print(f"🔍 測試股票: {stock_code}")
        try:
            # 測試融資融券資料獲取
            margin_info = window.get_margin_trading_info(stock_code, test_date)
            
            if margin_info:
                print(f"✅ 成功獲取 {stock_code} 的融資融券資料")
                print(f"   股票名稱: {margin_info.get('股票名稱', 'N/A')}")
                print(f"   融資買進: {margin_info.get('融資買進', 'N/A')}")
                print(f"   融資賣出: {margin_info.get('融資賣出', 'N/A')}")
                print(f"   融資今日餘額: {margin_info.get('融資今日餘額', 'N/A')}")
                print(f"   融券買進: {margin_info.get('融券買進', 'N/A')}")
                print(f"   融券賣出: {margin_info.get('融券賣出', 'N/A')}")
                print(f"   融券今日餘額: {margin_info.get('融券今日餘額', 'N/A')}")
            else:
                print(f"⚠️ 無法獲取 {stock_code} 的融資融券資料")
                
        except Exception as e:
            print(f"❌ 測試 {stock_code} 失敗: {e}")
        
        print()
    
    app.quit()
    return True

def test_margin_trading_display():
    """測試融資融券顯示功能"""
    print("🧪 測試融資融券顯示功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = FinlabGUI()
    
    print("✅ 主視窗創建成功")
    print("\n📋 測試步驟:")
    print("1. 選擇月營收排行榜")
    print("2. 執行排行榜查詢")
    print("3. 在股票列表中右鍵點擊股票")
    print("4. 選擇「月營收綜合評估」")
    print("5. 檢查融資融券資訊區塊")
    
    print("\n🎯 檢查重點:")
    print("• 融資融券區塊是否正確顯示")
    print("• 融資買進、賣出、餘額資訊")
    print("• 融券買進、賣出、餘額資訊")
    print("• 餘額變化的顏色標示")
    print("• 資料日期是否正確")
    
    print("\n🔍 預期顯示格式:")
    print("┌─────────────────────────────────────────┐")
    print("│ 💰 融資融券狀況                        │")
    print("├─────────────────────────────────────────┤")
    print("│ 📅 資料日期：2025-01-15                 │")
    print("│                                         │")
    print("│ 📈 融資狀況                            │")
    print("│ 買進：1,234千股    賣出：567千股        │")
    print("│ 現金償還：89千股                        │")
    print("│ 今日餘額：12,345千股  (+123)            │")
    print("│                                         │")
    print("│ 📉 融券狀況                            │")
    print("│ 買進：45千股       賣出：67千股         │")
    print("│ 現券償還：12千股                        │")
    print("│ 今日餘額：890千股     (-23)             │")
    print("└─────────────────────────────────────────┘")
    
    # 顯示視窗
    window.show()
    
    print("\n🚀 程式已啟動，請按照測試步驟進行驗證")
    print("💡 特別注意融資融券區塊的顯示效果")
    print("💡 關閉視窗即可結束測試")
    
    # 執行應用程式
    sys.exit(app.exec())

def test_margin_trading_integration():
    """測試融資融券整合功能"""
    print("🧪 測試融資融券整合功能")
    print("=" * 50)
    
    # 測試區塊創建
    app = QApplication(sys.argv)
    window = FinlabGUI()
    
    # 模擬股票資料
    test_stock_data = {
        '股票代碼': '2330',
        '股票名稱': '台積電',
        '排名': 1,
        '當月營收': '120000000',
        'YoY%': '15.5',
        'MoM%': '8.2'
    }
    
    try:
        # 測試融資融券區塊創建
        margin_group = window.create_margin_trading_group(test_stock_data)
        print("✅ 融資融券區塊創建成功")
        
        # 測試區塊屬性
        print(f"✅ 區塊標題: {margin_group.title()}")
        print("✅ 區塊布局正常")
        
        print("\n📋 整合測試總結:")
        print("=" * 50)
        print("1. ✅ 融資融券資料獲取功能已實現")
        print("2. ✅ 融資融券顯示區塊已創建")
        print("3. ✅ 已整合到月營收評估對話框")
        print("4. ✅ 支援TWSE API即時資料獲取")
        print("5. ✅ 包含完整的融資融券資訊")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🎯 融資融券功能測試套件")
    print("=" * 60)
    
    # 測試1: API功能測試
    print("\n【測試1】API功能測試")
    try:
        test_margin_trading_api()
        print("✅ API功能測試完成")
    except Exception as e:
        print(f"❌ API功能測試失敗: {e}")
    
    # 測試2: 整合功能測試
    print("\n【測試2】整合功能測試")
    try:
        if test_margin_trading_integration():
            print("✅ 整合功能測試通過")
        else:
            print("❌ 整合功能測試失敗")
    except Exception as e:
        print(f"❌ 整合功能測試失敗: {e}")
    
    # 測試3: 顯示功能測試（互動式）
    print("\n【測試3】顯示功能測試（互動式）")
    print("即將啟動程式進行顯示測試...")
    input("按 Enter 繼續...")
    
    try:
        test_margin_trading_display()
    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
    except Exception as e:
        print(f"❌ 顯示功能測試失敗: {e}")

if __name__ == "__main__":
    main()
