#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化後的備用即時股價監控系統
驗證更新速度和界面改進
"""

import sys
import os
import time
import logging
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def test_optimized_backup_monitor():
    """測試優化後的備用監控"""
    try:
        # 設置日誌
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        
        print("🚀 開始測試優化後的備用即時股價監控系統")
        print("=" * 60)
        
        # 測試TSRTC模組性能
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        
        print("✅ 成功導入優化後的TSRTC模組")
        
        # 創建監控器實例
        monitor = TSRTCBackupMonitor()
        
        # 測試連接
        print("🔍 測試TSRTC連接...")
        start_time = time.time()
        connection_ok = monitor.test_connection()
        connection_time = time.time() - start_time
        
        if connection_ok:
            print(f"✅ TSRTC連接成功 (耗時: {connection_time:.2f}秒)")
        else:
            print(f"❌ TSRTC連接失敗 (耗時: {connection_time:.2f}秒)")
            return 1
        
        # 測試單一股票查詢性能
        print("\n📊 測試單一股票查詢性能...")
        test_stocks = ['2330', '2317', '2454']
        
        for stock_code in test_stocks:
            start_time = time.time()
            data = monitor.get_single_stock_realtime(stock_code)
            query_time = time.time() - start_time
            
            if data:
                print(f"✅ {stock_code} 查詢成功 - 現價: {data.get('current_price', 'N/A')} (耗時: {query_time:.2f}秒)")
            else:
                print(f"❌ {stock_code} 查詢失敗 (耗時: {query_time:.2f}秒)")
        
        # 測試批量查詢性能
        print("\n📈 測試批量查詢性能...")
        batch_stocks = ['2330', '2317', '2454', '2412', '2881', '0050', '6290']
        
        start_time = time.time()
        batch_data = monitor.get_multiple_stocks_realtime(batch_stocks)
        batch_time = time.time() - start_time
        
        print(f"📊 批量查詢結果: 成功獲取 {len(batch_data)} / {len(batch_stocks)} 支股票")
        print(f"⏱️ 批量查詢耗時: {batch_time:.2f}秒 (平均每支: {batch_time/len(batch_stocks):.2f}秒)")
        
        # 顯示獲取到的數據
        for stock_code, data in batch_data.items():
            if data:
                print(f"  📈 {stock_code} {data.get('stock_name', '')} - 現價: {data.get('current_price', 'N/A')}")
        
        # 測試GUI界面
        print("\n🖥️ 測試優化後的GUI界面...")
        
        # 創建應用程式
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 導入主程式並創建備用監控對話框
        from O3mh_gui_v21_optimized import O3mhMainWindow
        
        # 創建主視窗實例（不顯示）
        main_window = O3mhMainWindow()
        
        # 創建備用監控對話框
        main_window.open_tsrtc_backup_monitor()
        
        print("✅ 備用監控GUI界面已開啟")
        
        # 設置自動關閉計時器
        def close_test():
            print("🎉 測試完成，關閉應用程式")
            if hasattr(main_window, 'tsrtc_dialog') and main_window.tsrtc_dialog:
                main_window.tsrtc_dialog.close()
            app.quit()
            
        timer = QTimer()
        timer.timeout.connect(close_test)
        timer.start(10000)  # 10秒後關閉
        
        # 顯示測試結果對話框
        if hasattr(main_window, 'tsrtc_dialog') and main_window.tsrtc_dialog:
            QMessageBox.information(
                main_window.tsrtc_dialog,
                "備用監控優化測試",
                f"🚀 備用即時股價監控系統優化測試\n\n"
                f"✅ 性能改進:\n"
                f"• 更新頻率: 可選1-10秒間隔\n"
                f"• 批量查詢: 優化至15支股票/批次\n"
                f"• 網路超時: 減少至8秒\n"
                f"• 批次延遲: 減少至0.2秒\n\n"
                f"📊 測試結果:\n"
                f"• TSRTC連接: {'✅ 正常' if connection_ok else '❌ 失敗'}\n"
                f"• 連接耗時: {connection_time:.2f}秒\n"
                f"• 批量查詢: {len(batch_data)}/{len(batch_stocks)} 支股票\n"
                f"• 批量耗時: {batch_time:.2f}秒\n\n"
                f"🎨 界面改進:\n"
                f"• 新增更新頻率選擇器\n"
                f"• 動態狀態顯示\n"
                f"• 優化按鈕佈局\n\n"
                f"⏰ 視窗將在10秒後自動關閉\n"
                f"請測試不同的更新頻率設定"
            )
        
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return 1
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return 1

def performance_benchmark():
    """性能基準測試"""
    print("\n🏃‍♂️ 執行性能基準測試...")
    print("-" * 40)
    
    try:
        from tsrtc_backup_monitor import TSRTCBackupMonitor
        
        monitor = TSRTCBackupMonitor()
        test_stocks = ['2330', '2317', '2454', '2412', '2881']
        
        # 測試多次查詢的平均性能
        total_times = []
        success_count = 0
        
        for i in range(5):  # 測試5次
            start_time = time.time()
            results = monitor.get_multiple_stocks_realtime(test_stocks)
            query_time = time.time() - start_time
            
            total_times.append(query_time)
            if results:
                success_count += 1
            
            print(f"第{i+1}次查詢: {query_time:.2f}秒, 成功獲取 {len(results)} 支股票")
            time.sleep(1)  # 間隔1秒
        
        # 計算統計數據
        avg_time = sum(total_times) / len(total_times)
        min_time = min(total_times)
        max_time = max(total_times)
        
        print(f"\n📊 性能統計:")
        print(f"• 平均查詢時間: {avg_time:.2f}秒")
        print(f"• 最快查詢時間: {min_time:.2f}秒")
        print(f"• 最慢查詢時間: {max_time:.2f}秒")
        print(f"• 成功率: {success_count}/5 ({success_count*20}%)")
        
        # 性能評級
        if avg_time < 2.0:
            grade = "🏆 優秀"
        elif avg_time < 3.0:
            grade = "✅ 良好"
        elif avg_time < 5.0:
            grade = "⚠️ 普通"
        else:
            grade = "❌ 需要改進"
            
        print(f"• 性能評級: {grade}")
        
    except Exception as e:
        print(f"❌ 性能測試失敗: {e}")

if __name__ == "__main__":
    print("🧪 備用即時股價監控系統優化測試")
    print("=" * 60)
    
    # 執行性能基準測試
    performance_benchmark()
    
    # 執行完整測試
    result = test_optimized_backup_monitor()
    
    print("=" * 60)
    if result == 0:
        print("🎉 備用監控優化測試完成")
        print("📋 優化清單:")
        print("  ✅ 更新頻率可調整 (1-10秒)")
        print("  ✅ 批量查詢優化 (15支/批次)")
        print("  ✅ 網路超時優化 (8秒)")
        print("  ✅ 批次延遲減少 (0.2秒)")
        print("  ✅ 界面控制改進")
    else:
        print("❌ 備用監控優化測試失敗")
    
    sys.exit(result)
