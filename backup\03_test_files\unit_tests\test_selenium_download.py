#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修復後的Selenium下載功能
"""

import sys
import os
import time

def test_selenium_download():
    """測試Selenium下載功能"""
    print("🚀 測試修復後的Selenium下載功能")
    print("=" * 60)
    
    try:
        from goodinfo_monthly_revenue_downloader import GoodinfoMonthlyRevenueDownloader
        
        # 創建下載器實例
        downloader = GoodinfoMonthlyRevenueDownloader()
        
        # 測試台積電
        stock_id = '2330'
        print(f"\n📊 測試下載 {stock_id} 月營收數據...")
        print("⚠️ 注意：此測試會實際啟動Chrome瀏覽器")
        
        # 詢問用戶是否繼續
        response = input("\n是否繼續測試Selenium下載？(y/n): ").lower().strip()
        if response != 'y':
            print("❌ 用戶取消測試")
            return False
        
        print("\n🔧 開始Selenium下載測試...")
        
        # 嘗試Selenium下載
        result = downloader.download_with_selenium(stock_id)
        
        if result:
            print(f"✅ Selenium下載成功，獲得 {result} 筆數據")
            
            # 查詢驗證
            print("\n🔍 查詢驗證...")
            import sqlite3
            conn = sqlite3.connect(downloader.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM monthly_revenue 
                WHERE stock_id = ? AND data_source = 'goodinfo_excel'
            """, (stock_id,))
            
            excel_count = cursor.fetchone()[0]
            print(f"✅ 數據庫中來自Excel的 {stock_id} 數據: {excel_count} 筆")
            
            if excel_count > 0:
                # 顯示最新幾筆數據
                cursor.execute("""
                    SELECT year, month, revenue, revenue_yoy 
                    FROM monthly_revenue 
                    WHERE stock_id = ? AND data_source = 'goodinfo_excel'
                    ORDER BY year DESC, month DESC 
                    LIMIT 3
                """, (stock_id,))
                
                recent_data = cursor.fetchall()
                print("\n📋 最新3筆Excel數據:")
                for year, month, revenue, yoy in recent_data:
                    revenue_str = f"{revenue:,.0f}億" if revenue else "N/A"
                    yoy_str = f"{yoy:+.1f}%" if yoy else "N/A"
                    print(f"   {year}/{month:02d} - 營收: {revenue_str}, 年增率: {yoy_str}")
            
            conn.close()
            return True
        else:
            print("❌ Selenium下載失敗")
            
            # 測試備用方法
            print("\n🔄 測試備用網頁抓取方法...")
            backup_result = downloader.download_with_scraper(stock_id)
            
            if backup_result:
                print(f"✅ 備用方法成功，獲得 {backup_result} 筆數據")
                return True
            else:
                print("❌ 備用方法也失敗")
                return False
                
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        return False

def test_button_detection():
    """測試按鈕檢測功能"""
    print("\n🔍 測試按鈕檢測功能")
    print("=" * 40)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # 設置Chrome選項
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        print("🔧 啟動Chrome瀏覽器...")
        driver = webdriver.Chrome(options=options)
        
        # 訪問台積電頁面
        url = "https://goodinfo.tw/tw/ShowSaleMonChart.asp?STOCK_ID=2330"
        print(f"📱 訪問頁面: {url}")
        
        driver.get(url)
        time.sleep(5)  # 等待頁面載入
        
        # 檢查頁面標題
        title = driver.title
        print(f"📄 頁面標題: {title}")
        
        # 尋找匯出按鈕
        print("\n🔍 尋找匯出按鈕...")
        
        selectors = [
            "//input[@type='button'][@value='XLS'][contains(@onclick, 'export2xls')]",
            "//input[@type='button'][@value='XLS']",
            "//input[contains(@value, 'XLS')]"
        ]
        
        found_button = False
        for selector in selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    button = elements[0]
                    value = button.get_attribute("value")
                    onclick = button.get_attribute("onclick")
                    is_displayed = button.is_displayed()
                    is_enabled = button.is_enabled()
                    
                    print(f"✅ 找到按鈕: {selector}")
                    print(f"   值: {value}")
                    print(f"   onclick: {onclick}")
                    print(f"   可見: {is_displayed}")
                    print(f"   可用: {is_enabled}")
                    
                    found_button = True
                    break
            except Exception as e:
                print(f"   選擇器失敗: {selector} - {e}")
                continue
        
        if not found_button:
            print("❌ 未找到匯出按鈕")
            
            # 檢查頁面源碼中是否包含export2xls
            page_source = driver.page_source
            if "export2xls" in page_source:
                print("✅ 頁面包含export2xls函數")
                
                # 嘗試執行JavaScript
                try:
                    result = driver.execute_script("""
                        var divDetail = document.getElementById('divDetail');
                        if (divDetail && typeof export2xls === 'function') {
                            return 'export2xls函數可用';
                        }
                        return 'export2xls函數不可用';
                    """)
                    print(f"✅ JavaScript檢查結果: {result}")
                except Exception as e:
                    print(f"❌ JavaScript執行失敗: {e}")
            else:
                print("❌ 頁面不包含export2xls函數")
        
        driver.quit()
        return found_button
        
    except Exception as e:
        print(f"❌ 按鈕檢測測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 Selenium下載功能測試套件")
    print("=" * 60)
    
    # 檢查基本依賴
    try:
        from selenium import webdriver
        print("✅ Selenium 可用")
    except ImportError:
        print("❌ Selenium 未安裝，需要: pip install selenium")
        return False
    
    # 測試按鈕檢測
    button_test = test_button_detection()
    
    if button_test:
        print("\n✅ 按鈕檢測成功，可以進行下載測試")
        
        # 測試實際下載
        download_test = test_selenium_download()
        
        if download_test:
            print("\n🎉 所有測試通過！Selenium下載功能已修復")
        else:
            print("\n⚠️ 下載測試失敗，但按鈕檢測正常")
    else:
        print("\n❌ 按鈕檢測失敗，可能需要進一步調試")
    
    print("\n" + "=" * 60)
    print("📋 測試總結:")
    print(f"   按鈕檢測: {'✅ 通過' if button_test else '❌ 失敗'}")
    
    if button_test:
        print("   建議: 現在可以在GUI中重新嘗試下載")
    else:
        print("   建議: 檢查網絡連接或使用手動數據添加方式")
    
    input("\n按Enter鍵退出...")
    return button_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
