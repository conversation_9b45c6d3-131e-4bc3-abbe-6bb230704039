#!/usr/bin/env python3
"""
最大化監控視窗模組
從原始程式中提取的MaximizedMonitorWindow類別
"""
import logging
import time
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QWidget, QHeaderView,
    QSizePolicy, QFrame, QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor


class MaximizedMonitorWindow(QMainWindow):
    """最大化盤中監控視窗"""

    def __init__(self, parent=None, strategy_stocks=None, current_strategy="", intraday_monitor=None):
        super().__init__(parent)
        self.parent_window = parent
        self.strategy_stocks = strategy_stocks or []
        self.current_strategy = current_strategy
        self.intraday_monitor = intraday_monitor

        # 監控狀態
        self.is_monitoring = False
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)

        # 設置視窗屬性
        self.setWindowTitle(f"🔍 盤中監控 - {current_strategy} ({len(self.strategy_stocks)} 支股票)")
        self.setGeometry(100, 100, 1400, 900)

        # 設置視窗樣式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QTableWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #404040;
                border: 1px solid #555555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 10px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: #ffffff;
            }
            QSpinBox {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 頂部控制區域
        self.create_control_panel(layout)

        # 主要監控表格
        self.create_monitor_table(layout)

        # 底部狀態和警報區域
        self.create_status_panel(layout)

    def create_control_panel(self, layout):
        """創建控制面板"""
        control_group = QGroupBox("📊 監控控制面板")
        control_layout = QHBoxLayout(control_group)

        # 策略信息
        strategy_info = QLabel(f"📈 當前策略: {self.current_strategy}")
        strategy_info.setStyleSheet("font-size: 14px; font-weight: bold; color: #51cf66;")
        control_layout.addWidget(strategy_info)

        # 股票數量
        stock_count = QLabel(f"📊 監控股票: {len(self.strategy_stocks)} 支")
        stock_count.setStyleSheet("font-size: 14px; font-weight: bold; color: #339af0;")
        control_layout.addWidget(stock_count)

        control_layout.addStretch()

        # 更新間隔設置
        control_layout.addWidget(QLabel("更新間隔:"))
        self.update_interval_spin = QSpinBox()
        self.update_interval_spin.setRange(5, 300)
        self.update_interval_spin.setValue(30)
        self.update_interval_spin.setSuffix(" 秒")
        control_layout.addWidget(self.update_interval_spin)

        # 控制按鈕
        self.start_btn = QPushButton("🚀 開始監控")
        self.start_btn.clicked.connect(self.start_monitoring)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ 停止監控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        self.refresh_btn = QPushButton("🔄 立即更新")
        self.refresh_btn.clicked.connect(self.manual_refresh)
        control_layout.addWidget(self.refresh_btn)

        layout.addWidget(control_group)

    def create_monitor_table(self, layout):
        """創建監控表格"""
        table_group = QGroupBox("📈 即時監控數據")
        table_layout = QVBoxLayout(table_group)

        # 創建表格
        self.monitor_table = QTableWidget()
        self.monitor_table.setColumnCount(12)
        self.monitor_table.setHorizontalHeaderLabels([
            "股票代碼", "股票名稱", "策略", "現價", "漲跌", "漲跌幅%",
            "成交量", "開盤價", "最高價", "最低價", "更新時間", "狀態"
        ])

        # 設置表格屬性
        self.monitor_table.setAlternatingRowColors(True)
        self.monitor_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.monitor_table.setSortingEnabled(True)

        # 設置列寬
        header = self.monitor_table.horizontalHeader()
        header.setStretchLastSection(True)

        # 填充初始數據
        self.populate_initial_data()

        table_layout.addWidget(self.monitor_table)
        layout.addWidget(table_group)

    def create_status_panel(self, layout):
        """創建狀態面板"""
        status_group = QGroupBox("📊 監控狀態")
        status_layout = QVBoxLayout(status_group)

        # 狀態標籤
        self.status_label = QLabel("📴 監控未啟動")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffd43b;")
        status_layout.addWidget(self.status_label)

        # 最後更新時間
        self.last_update_label = QLabel("最後更新: --")
        self.last_update_label.setStyleSheet("color: #868e96;")
        status_layout.addWidget(self.last_update_label)

        layout.addWidget(status_group)

    def populate_initial_data(self):
        """填充初始數據"""
        try:
            self.monitor_table.setRowCount(len(self.strategy_stocks))

            for row, stock in enumerate(self.strategy_stocks):
                # 基本信息
                self.monitor_table.setItem(row, 0, QTableWidgetItem(stock['code']))
                self.monitor_table.setItem(row, 1, QTableWidgetItem(stock['name']))
                self.monitor_table.setItem(row, 2, QTableWidgetItem(stock['strategy']))

                # 初始化其他列
                for col in range(3, 12):
                    self.monitor_table.setItem(row, col, QTableWidgetItem("--"))

        except Exception as e:
            logging.error(f"填充初始數據失敗: {e}")

    def start_monitoring(self):
        """開始監控"""
        try:
            if self.is_monitoring:
                return

            self.is_monitoring = True
            interval = self.update_interval_spin.value() * 1000  # 轉換為毫秒
            self.update_timer.start(interval)

            # 更新按鈕狀態
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)

            # 更新狀態
            self.status_label.setText("🚀 監控中...")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #51cf66;")

            # 立即更新一次
            self.update_data()

            logging.info("最大化監控視窗開始監控")

        except Exception as e:
            logging.error(f"開始監控失敗: {e}")

    def stop_monitoring(self):
        """停止監控"""
        try:
            self.is_monitoring = False
            self.update_timer.stop()

            # 更新按鈕狀態
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

            # 更新狀態
            self.status_label.setText("⏹️ 監控已停止")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffd43b;")

            logging.info("最大化監控視窗停止監控")

        except Exception as e:
            logging.error(f"停止監控失敗: {e}")

    def manual_refresh(self):
        """手動刷新"""
        self.update_data()

    def update_data(self):
        """更新數據"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")

            for row, stock in enumerate(self.strategy_stocks):
                stock_code = stock['code']

                # 調用TWSE API獲取即時數據
                realtime_data = self.get_realtime_data_twse(stock_code)

                if realtime_data:
                    # 更新表格數據
                    self.monitor_table.setItem(row, 3, QTableWidgetItem(f"{realtime_data['price']:.2f}"))

                    # 漲跌和漲跌幅
                    change = realtime_data['change']
                    change_pct = realtime_data['change_pct']

                    change_item = QTableWidgetItem(f"{change:+.2f}")
                    change_pct_item = QTableWidgetItem(f"{change_pct:+.2f}%")

                    # 設置顏色
                    if change > 0:
                        change_item.setForeground(QColor(255, 82, 82))  # 紅色
                        change_pct_item.setForeground(QColor(255, 82, 82))
                    elif change < 0:
                        change_item.setForeground(QColor(34, 197, 94))  # 綠色
                        change_pct_item.setForeground(QColor(34, 197, 94))

                    self.monitor_table.setItem(row, 4, change_item)
                    self.monitor_table.setItem(row, 5, change_pct_item)

                    # 其他數據
                    self.monitor_table.setItem(row, 6, QTableWidgetItem(f"{realtime_data['volume']:,}"))
                    self.monitor_table.setItem(row, 7, QTableWidgetItem(f"{realtime_data['open']:.2f}"))
                    self.monitor_table.setItem(row, 8, QTableWidgetItem(f"{realtime_data['high']:.2f}"))
                    self.monitor_table.setItem(row, 9, QTableWidgetItem(f"{realtime_data['low']:.2f}"))
                    self.monitor_table.setItem(row, 10, QTableWidgetItem(current_time))

                    # 根據數據來源設置狀態
                    if realtime_data.get('status') == 'success':
                        self.monitor_table.setItem(row, 11, QTableWidgetItem("✅ TWSE"))
                    else:
                        self.monitor_table.setItem(row, 11, QTableWidgetItem("⚠️ 模擬"))
                else:
                    # 數據獲取失敗
                    self.monitor_table.setItem(row, 11, QTableWidgetItem("❌ 失敗"))

            # 更新最後更新時間
            self.last_update_label.setText(f"最後更新: {current_time}")

        except Exception as e:
            logging.error(f"更新數據失敗: {e}")

    def get_realtime_data_twse(self, stock_code):
        """使用TWSE/TPEX API獲取即時數據 - 優化版，支援櫃買中心"""
        try:
            import requests
            import time

            # 請求頻率控制
            if not hasattr(self, '_last_request_time'):
                self._last_request_time = 0
            if not hasattr(self, '_request_delay'):
                self._request_delay = 0.5  # 500ms間隔
            if not hasattr(self, '_failed_stocks'):
                self._failed_stocks = set()

            # 檢查是否已知失敗的股票
            if stock_code in self._failed_stocks:
                return None

            # 控制請求頻率
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < self._request_delay:
                time.sleep(self._request_delay - time_since_last)

            self._last_request_time = time.time()

            # 清理股票代碼
            clean_code = str(stock_code).replace('.TW', '').replace('.tw', '').replace('.TWO', '').strip()

            # 判斷是否為櫃買中心股票
            is_otc_stock = self._is_otc_stock(clean_code)

            # 根據股票類型選擇適當的API
            if is_otc_stock:
                logging.info(f"🔍 檢測到櫃買股票 {clean_code}，優先使用TPEX API")
                # 先嘗試櫃買中心API
                result = self._fetch_tpex_realtime_data(clean_code)
                if result:
                    return result
                # 如果失敗，再嘗試證交所API
                logging.info(f"🔄 TPEX失敗，嘗試TWSE API for {clean_code}")
                result = self._fetch_twse_realtime_data(clean_code)
                if result:
                    return result
            else:
                logging.info(f"🔍 檢測到上市股票 {clean_code}，優先使用TWSE API")
                # 先嘗試證交所API
                result = self._fetch_twse_realtime_data(clean_code)
                if result:
                    return result
                # 如果失敗，再嘗試櫃買中心API
                logging.info(f"🔄 TWSE失敗，嘗試TPEX API for {clean_code}")
                result = self._fetch_tpex_realtime_data(clean_code)
                if result:
                    return result

            # 所有API都失敗
            self._failed_stocks.add(stock_code)
            logging.error(f"❌ 所有數據源都無法獲取 {clean_code} 的盤中數據")
            return self.get_fallback_data(clean_code)

        except Exception as e:
            logging.error(f"❌ 獲取即時數據失敗 {stock_code}: {e}")
            return self.get_fallback_data(stock_code)

    def _is_otc_stock(self, stock_code):
        """判斷是否為櫃買中心股票"""
        try:
            if not stock_code or len(stock_code) < 4:
                return False

            # 櫃買中心股票代碼規則
            first_digit = stock_code[0]

            # 5開頭通常是櫃買股票（如5360）
            if first_digit == '5':
                return True

            # 6開頭的部分是櫃買股票
            if first_digit == '6':
                return True

            # 7開頭通常是櫃買股票
            if first_digit == '7':
                return True

            # 8開頭的部分是櫃買股票
            if first_digit == '8':
                return True

            return False

        except Exception:
            return False

    def _fetch_twse_realtime_data(self, clean_code):
        """從證交所API獲取即時數據 - 使用智能代理"""
        try:
            # 嘗試導入智能請求管理器
            try:
                from smart_proxy_manager import make_smart_request
                use_smart_proxy = True
                logging.debug(f"🌐 使用智能代理獲取 {clean_code} 數據")
            except ImportError:
                import requests
                use_smart_proxy = False
                logging.debug(f"🌐 直接連接獲取 {clean_code} 數據")

            # TWSE的即時報價網址
            url = "https://mis.twse.com.tw/stock/api/getStockInfo.jsp"

            # 構建參數
            params = {
                'ex_ch': f'tse_{clean_code}.tw',
                'json': '1',
                'delay': '0'
            }

            # 發送請求
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            if use_smart_proxy:
                # 使用智能代理管理器
                response = make_smart_request(url, params=params, headers=headers, timeout=8)
            else:
                # 嘗試使用增強網路管理器
                try:
                    from enhanced_network_manager import make_enhanced_request
                    response = make_enhanced_request(url, params=params, headers=headers, timeout=8)
                    logging.debug(f"🌐 使用增強網路管理器獲取 {clean_code}")
                except ImportError:
                    # 直接連接（忽略SSL證書驗證）
                    import requests
                    response = requests.get(url, params=params, headers=headers, timeout=8, verify=False)
                    logging.debug(f"🌐 使用直接連接獲取 {clean_code}")

            if response.status_code == 200:
                data = response.json()

                # 檢查資料是否正確返回
                if data.get('stat') == 'OK' and 'msgArray' in data and data['msgArray']:
                    stock_info = data['msgArray'][0]  # 取第一個即時資料

                    # 安全解析數據，處理 '-' 值
                    try:
                        current_price = float(stock_info.get('z', 0)) if stock_info.get('z') not in ['-', ''] else 0
                        open_price = float(stock_info.get('o', 0)) if stock_info.get('o') not in ['-', ''] else 0
                        high_price = float(stock_info.get('h', 0)) if stock_info.get('h') not in ['-', ''] else 0
                        low_price = float(stock_info.get('l', 0)) if stock_info.get('l') not in ['-', ''] else 0
                        volume = int(stock_info.get('v', 0)) if stock_info.get('v') not in ['-', ''] else 0
                        yesterday_close = float(stock_info.get('y', 0)) if stock_info.get('y') not in ['-', ''] else 0
                    except (ValueError, TypeError) as e:
                        logging.error(f"證交所即時數據解析失敗 {clean_code}: could not convert string to float: '{stock_info.get('z', 'N/A')}'")
                        return None

                    # 計算漲跌
                    if yesterday_close > 0 and current_price > 0:
                        change = current_price - yesterday_close
                        change_pct = (change / yesterday_close) * 100
                    else:
                        change = 0
                        change_pct = 0

                    return {
                        'price': current_price,
                        'change': change,
                        'change_pct': change_pct,
                        'volume': volume,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'yesterday_close': yesterday_close,
                        'timestamp': stock_info.get('tlong', ''),
                        'status': 'success'
                    }
                else:
                    logging.warning(f"⚠️ 證交所API 無法獲取 {clean_code} 的即時資料")
                    return None
            elif response.status_code == 429:
                # 請求頻率限制
                logging.warning(f"⚠️ 證交所API 請求頻率限制 {clean_code}，增加延遲")
                self._request_delay = min(self._request_delay * 1.5, 2.0)  # 增加延遲，最大2秒
                return None
            else:
                logging.warning(f"⚠️ 證交所API 請求失敗，狀態碼: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            logging.warning(f"⚠️ 證交所API 請求超時: {clean_code}")
            return None
        except requests.exceptions.RequestException as e:
            error_msg = str(e).lower()
            if 'too many requests' in error_msg or 'rate limit' in error_msg:
                logging.warning(f"⚠️ 證交所API 請求頻率限制: {clean_code}")
                self._request_delay = min(self._request_delay * 2, 3.0)  # 大幅增加延遲
            else:
                logging.error(f"❌ 證交所API 網路錯誤 {clean_code}: {e}")
            return None
        except Exception as e:
            logging.error(f"❌ 證交所API 獲取即時數據失敗 {clean_code}: {e}")
            return None

    def _fetch_tpex_realtime_data(self, clean_code):
        """從櫃買中心API獲取即時數據 - 使用智能代理"""
        try:
            # 嘗試導入智能請求管理器
            try:
                from smart_proxy_manager import make_smart_request
                use_smart_proxy = True
                logging.debug(f"🏪 使用智能代理獲取櫃買 {clean_code} 數據")
            except ImportError:
                import requests
                use_smart_proxy = False
                logging.debug(f"🏪 直接連接獲取櫃買 {clean_code} 數據")

            # 櫃買中心的即時報價網址
            url = "https://www.tpex.org.tw/web/stock/aftertrading/otc_quotes_no1430/stk_wn1430_result.php"

            # 構建參數 - 櫃買中心使用不同的參數格式
            params = {
                'l': 'zh-tw',
                'd': datetime.now().strftime('%Y/%m/%d'),
                'se': 'EW',  # 櫃買市場代碼
                's': '0,asc,0'
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://www.tpex.org.tw/'
            }

            if use_smart_proxy:
                # 使用智能代理管理器
                response = make_smart_request(url, params=params, headers=headers, timeout=8)
            else:
                # 嘗試使用增強網路管理器
                try:
                    from enhanced_network_manager import make_enhanced_request
                    response = make_enhanced_request(url, params=params, headers=headers, timeout=8)
                    logging.debug(f"🏪 使用增強網路管理器獲取櫃買 {clean_code}")
                except ImportError:
                    # 直接連接
                    import requests
                    response = requests.get(url, params=params, headers=headers, timeout=8)
                    logging.debug(f"🏪 使用直接連接獲取櫃買 {clean_code}")

            if response.status_code == 200:
                try:
                    data = response.json()

                    # 櫃買中心API返回格式不同，需要在aaData中查找
                    if 'aaData' in data and data['aaData']:
                        for stock_data in data['aaData']:
                            if len(stock_data) > 0 and stock_data[0] == clean_code:
                                # 找到對應股票，解析數據
                                try:
                                    current_price = float(stock_data[2]) if len(stock_data) > 2 and stock_data[2] not in ['-', ''] else 0
                                    change = float(stock_data[3]) if len(stock_data) > 3 and stock_data[3] not in ['-', ''] else 0
                                    open_price = float(stock_data[4]) if len(stock_data) > 4 and stock_data[4] not in ['-', ''] else 0
                                    high_price = float(stock_data[5]) if len(stock_data) > 5 and stock_data[5] not in ['-', ''] else 0
                                    low_price = float(stock_data[6]) if len(stock_data) > 6 and stock_data[6] not in ['-', ''] else 0
                                    volume = int(stock_data[7].replace(',', '')) if len(stock_data) > 7 and stock_data[7] not in ['-', ''] else 0

                                    # 計算漲跌幅
                                    yesterday_close = current_price - change if current_price > 0 and change != 0 else current_price
                                    change_pct = (change / yesterday_close * 100) if yesterday_close > 0 else 0

                                    return {
                                        'price': current_price,
                                        'change': change,
                                        'change_pct': change_pct,
                                        'volume': volume,
                                        'open': open_price,
                                        'high': high_price,
                                        'low': low_price,
                                        'yesterday_close': yesterday_close,
                                        'timestamp': datetime.now().strftime('%H:%M:%S'),
                                        'status': 'success',
                                        'source': 'tpex'
                                    }
                                except (ValueError, TypeError, IndexError) as e:
                                    logging.error(f"櫃買中心數據解析失敗 {clean_code}: {e}")
                                    return None

                        # 沒有找到對應股票
                        logging.warning(f"⚠️ 櫃買中心API 找不到股票 {clean_code}")
                        return None
                    else:
                        logging.warning(f"⚠️ 櫃買中心API 無數據返回 {clean_code}")
                        return None

                except json.JSONDecodeError as e:
                    logging.error(f"櫃買中心API JSON解析失敗: {e}")
                    return None
            else:
                logging.warning(f"⚠️ 櫃買中心API 請求失敗，狀態碼: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            logging.warning(f"⚠️ 櫃買中心API 請求超時: {clean_code}")
            return None
        except Exception as e:
            logging.error(f"❌ 櫃買中心API 獲取即時數據失敗 {clean_code}: {e}")
            return None

    def get_fallback_data(self, stock_code):
        """備用數據源（當TWSE API失敗時）"""
        try:
            import random

            # 模擬基準價格
            base_price = 50 + hash(stock_code) % 100

            # 模擬價格波動
            change_pct = (random.random() - 0.5) * 6  # -3% 到 +3%
            current_price = base_price * (1 + change_pct / 100)
            change = current_price - base_price

            return {
                'price': current_price,
                'change': change,
                'change_pct': change_pct,
                'volume': random.randint(10000, 500000),
                'open': base_price * (1 + (random.random() - 0.5) * 0.02),
                'high': current_price * (1 + random.random() * 0.02),
                'low': current_price * (1 - random.random() * 0.02),
                'yesterday_close': base_price,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'status': 'fallback'
            }

        except Exception as e:
            logging.error(f"備用數據生成失敗: {e}")
            return None



    def analyze_all_strategy_combinations(self):
        """分析所有策略組合"""
        if not self.intersection_analyzer:
            QMessageBox.warning(self, "錯誤", "策略交集分析器不可用")
            return

        # 檢查是否有足夠的策略結果
        if len(self.strategy_results_cache) < 2:
            QMessageBox.warning(self, "警告", "至少需要執行2個策略才能進行組合分析")
            return

        try:
            # 添加所有策略結果到交集分析器
            for strategy, result_df in self.strategy_results_cache.items():
                stock_code_column = self.get_stock_code_column_name(strategy)
                self.intersection_analyzer.add_strategy_result(strategy, result_df, stock_code_column)

            # 分析所有組合
            all_results = self.intersection_analyzer.analyze_all_combinations()

            # 獲取最佳組合
            top_combinations = self.intersection_analyzer.get_top_intersection_combinations()

            # 生成報告
            report_lines = ["🔍 所有策略組合分析結果", "=" * 50, ""]
            report_lines.append("📊 按交集數量排序:")

            if top_combinations:
                for i, (combo_name, count) in enumerate(top_combinations, 1):
                    report_lines.append(f"  {i:2d}. {combo_name}: {count} 支共同股票")

                    # 顯示前3名的詳細股票
                    if i <= 3 and combo_name in all_results:
                        stocks = all_results[combo_name]['intersection_stocks']
                        if stocks:
                            report_lines.append(f"      股票: {', '.join(stocks)}")
                        report_lines.append("")
            else:
                report_lines.append("  ⚠️ 沒有找到有交集的策略組合")

            # 顯示結果
            self.intersection_result_text.setPlainText("\n".join(report_lines))

            QMessageBox.information(self, "完成", f"組合分析完成！\n找到 {len(top_combinations)} 個有效組合")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"分析組合失敗: {e}")
            logging.error(f"❌ 分析策略組合失敗: {e}")

    def export_intersection_results(self):
        """導出交集結果"""
        if not hasattr(self, 'current_intersection_result'):
            QMessageBox.warning(self, "警告", "沒有可導出的交集結果")
            return

        try:
            filename = self.intersection_analyzer.export_intersection_results(self.current_intersection_result)
            if filename:
                # 顯示增強版導出成功對話框
                self.show_enhanced_export_dialog(filename)
            else:
                QMessageBox.critical(self, "錯誤", "導出失敗")
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"導出失敗: {e}")

    def show_enhanced_export_dialog(self, file_path):
        """顯示增強版導出對話框，包含開啟檔案選項"""
        import os
        import platform
        import subprocess

        # 獲取檔案資訊
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        file_size_mb = file_size / (1024 * 1024)

        # 創建自定義對話框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("導出成功")
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setText("✅ 策略交集結果導出成功！")

        # 構建詳細信息
        detail_info = f"📁 檔案位置：{file_path}\n"
        detail_info += f"📊 檔案大小：{file_size_mb:.2f} MB\n"
        detail_info += f"📄 檔案格式：JSON (UTF-8)\n"
        detail_info += f"📋 內容：策略交集分析結果\n\n"
        detail_info += f"❓ 是否要立即開啟導出的檔案？"

        msg_box.setInformativeText(detail_info)

        # 自定義按鈕文字
        yes_btn = msg_box.addButton("🚀 開啟檔案", QMessageBox.ButtonRole.YesRole)
        folder_btn = msg_box.addButton("📁 開啟資料夾", QMessageBox.ButtonRole.ActionRole)
        no_btn = msg_box.addButton("📋 稍後開啟", QMessageBox.ButtonRole.NoRole)
        msg_box.setDefaultButton(yes_btn)

        reply = msg_box.exec()

        try:
            if msg_box.clickedButton() == yes_btn:
                # 開啟檔案
                if platform.system() == "Windows":
                    os.startfile(file_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(['open', file_path])
                else:  # Linux
                    subprocess.run(['xdg-open', file_path])

            elif msg_box.clickedButton() == folder_btn:
                # 開啟檔案所在資料夾
                directory = os.path.dirname(file_path)
                if platform.system() == "Windows":
                    subprocess.run(['explorer', '/select,', file_path])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(['open', directory])
                else:  # Linux
                    subprocess.run(['xdg-open', directory])

        except Exception as e:
            QMessageBox.warning(self, "警告", f"無法開啟檔案或資料夾：{str(e)}")

    def get_stock_code_column_name(self, strategy_name):
        """根據策略名稱獲取股票代碼欄位名稱"""
        # 根據不同策略的表格格式確定欄位名稱
        return "股票代碼"  # 大部分策略都使用這個欄位名

    def closeEvent(self, event):
        """視窗關閉事件"""
        if self.is_monitoring:
            self.stop_monitoring()
        event.accept()

# ===================== 股票管理對話框 =====================
