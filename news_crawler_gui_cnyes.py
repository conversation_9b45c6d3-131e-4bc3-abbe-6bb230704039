#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉅亨網新聞爬蟲GUI - 支援完整視窗控制
"""

import sys
import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QSpinBox, QPushButton, QTextEdit,
                            QProgressBar, QMessageBox, QFrame, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon
import logging
from datetime import datetime

class NewsCrawlerThread(QThread):
    """新聞爬取執行緒"""
    progress_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, stock_code, days, db_path=None):
        super().__init__()
        self.stock_code = stock_code
        self.days = days
        self.db_path = db_path
        
    def run(self):
        try:
            self.progress_updated.emit("🚀 初始化鉅亨網爬蟲...")

            # 使用與右鍵相同的成功爬蟲，並使用配置的資料庫路徑
            from news_crawler_cnyes_only import CnyesNewsCrawler

            # 使用傳入的資料庫路徑
            crawler = CnyesNewsCrawler(db_path=self.db_path)

            self.progress_updated.emit(f"🔍 開始搜尋 {self.stock_code} 相關新聞...")

            # 使用成功的快速搜尋方法（與右鍵相同）
            news_list = crawler.quick_search_cnyes(self.stock_code, days=self.days)

            self.progress_updated.emit(f"✅ 搜尋完成！共找到 {len(news_list)} 筆新聞")

            # 儲存到資料庫（使用基礎類別的方法）
            if news_list:
                # 轉換格式並儲存
                saved_count = 0
                for news in news_list:
                    try:
                        # 使用基礎類別的資料庫儲存功能
                        news_data = (
                            f"cnyes_{news.get('date', '')}_{news.get('time', '')}",  # news_id
                            news.get('date', ''),
                            news.get('time', ''),
                            news.get('source', 'cnyes'),
                            news.get('title', ''),
                            '',  # reporter
                            news.get('link', ''),
                            '',  # content
                            self.stock_code
                        )

                        import sqlite3
                        conn = sqlite3.connect(crawler.db_path)
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT OR IGNORE INTO news_content
                            (news_id, date, time, source, title, reporter, link, content, stock_code)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', news_data)
                        if cursor.rowcount > 0:
                            saved_count += 1
                        conn.commit()
                        conn.close()
                    except:
                        continue

                self.progress_updated.emit(f"💾 已儲存 {saved_count} 筆新聞到資料庫")

            self.finished_signal.emit(news_list)

        except Exception as e:
            self.error_signal.emit(str(e))

class NewsCrawlerDialog(QDialog):
    """鉅亨網新聞爬蟲對話框"""
    
    def __init__(self, parent=None, stock_code=""):
        super().__init__(parent)
        self.stock_code = stock_code
        self.crawler_thread = None
        self.setup_ui()
        
    def setup_ui(self):
        """設置UI"""
        self.setWindowTitle("📰 財經新聞爬蟲 (鉅亨網專用)")

        # 設置視窗大小 - 600x500
        self.setMinimumSize(600, 400)
        self.resize(600, 500)  # 設定為600x500

        # 設置視窗標誌 - 完整的視窗控制功能
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint |
            Qt.WindowType.WindowSystemMenuHint |
            Qt.WindowType.WindowTitleHint
        )

        # 專業深色主題 - 緊湊布局
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Microsoft JhengHei';
                font-size: 14px;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
                font-weight: bold;
                font-size: 14px;
                padding: 5px;
            }
            QLineEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                font-size: 14px;
                border-radius: 4px;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
                background-color: #404040;
            }
            QSpinBox {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                font-size: 14px;
                border-radius: 4px;
                min-height: 20px;
            }
            QSpinBox:focus {
                border: 2px solid #0078d4;
            }
            QComboBox {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                font-size: 14px;
                border-radius: 4px;
                min-height: 20px;
            }
            QComboBox:focus {
                border: 2px solid #0078d4;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: #00ff88;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Microsoft JhengHei';
                font-size: 12px;
                padding: 8px;
                border-radius: 4px;
            }
            QProgressBar {
                border: 1px solid #555555;
                text-align: center;
                background-color: #3c3c3c;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                min-height: 20px;
                border-radius: 4px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #0078d4;
                color: #ffffff;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                font-size: 14px;
                border-radius: 4px;
                min-height: 15px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
        """)
        
        # 主佈局 - 緊湊設計
        layout = QVBoxLayout(self)
        layout.setSpacing(3)  # 極小間距
        layout.setContentsMargins(10, 3, 10, 3)  # 極小邊距

        # 標題 - 緊湊設計
        title_label = QLabel("📰 財經新聞爬蟲")
        title_label.setFont(QFont("Microsoft JhengHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #00d4ff; font-size: 16px; font-weight: bold; padding: 1px; margin: 0px;")
        title_label.setMaximumHeight(20)  # 壓縮標題高度，把空間留給訊息框
        title_label.setMinimumHeight(20)
        layout.addWidget(title_label)

        # 設定區域 - 一行搞定
        settings_layout = QHBoxLayout()

        # 爬取天數
        days_label = QLabel("天數:")
        days_label.setFont(QFont("Microsoft JhengHei", 12))
        settings_layout.addWidget(days_label)

        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 30)
        self.days_spinbox.setValue(7)
        self.days_spinbox.setSuffix("天")
        self.days_spinbox.setFont(QFont("Microsoft JhengHei", 12))
        self.days_spinbox.setMaximumWidth(80)
        settings_layout.addWidget(self.days_spinbox)

        settings_layout.addSpacing(20)  # 間隔

        # 股票代碼/名稱
        stock_label = QLabel("股票:")
        stock_label.setFont(QFont("Microsoft JhengHei", 12))
        settings_layout.addWidget(stock_label)

        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("代碼或名稱，例如: 2330 或 台積電")
        self.stock_input.setText("2330")  # 預設代碼為2330
        self.stock_input.setFont(QFont("Microsoft JhengHei", 12))
        self.stock_input.setMaximumWidth(200)  # 增加寬度以容納股票名稱
        if self.stock_code:
            self.stock_input.setText(self.stock_code)
        settings_layout.addWidget(self.stock_input)

        settings_layout.addStretch()
        layout.addLayout(settings_layout)

        # 按鈕區域 - 移到上方
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 開始爬取按鈕
        self.start_btn = QPushButton("🚀 開始爬取")
        self.start_btn.setFont(QFont("Microsoft JhengHei", 12, QFont.Weight.Bold))
        self.start_btn.setMaximumHeight(30)
        self.start_btn.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.start_btn)

        # 查看資料庫按鈕
        self.view_db_btn = QPushButton("📊 查看資料庫")
        self.view_db_btn.setFont(QFont("Microsoft JhengHei", 12, QFont.Weight.Bold))
        self.view_db_btn.setMaximumHeight(30)
        self.view_db_btn.clicked.connect(self.view_database)
        button_layout.addWidget(self.view_db_btn)

        # 關閉按鈕
        self.close_btn = QPushButton("❌ 關閉")
        self.close_btn.setFont(QFont("Microsoft JhengHei", 12, QFont.Weight.Bold))
        self.close_btn.setMaximumHeight(30)
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        # 進度顯示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 狀態顯示 - 主要空間給訊息框
        self.status_text = QTextEdit()
        self.status_text.setMinimumHeight(100)  # 適當的訊息框高度，配合200px總高度
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        # 初始化狀態
        self.add_status_message("💡 準備就緒，請設定爬取參數後點擊「開始爬取」")
        
    def add_status_message(self, message):
        """添加狀態訊息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")

    def parse_stock_input(self, user_input):
        """解析用戶輸入，支援股票代碼和股票名稱"""
        try:
            # 移除空白字符
            user_input = user_input.strip()

            # 情況1：直接輸入4位數字股票代碼
            if user_input.isdigit() and len(user_input) == 4:
                self.add_status_message(f"✅ 識別為股票代碼: {user_input}")
                return user_input

            # 情況2：輸入格式為 "代碼 名稱" 或 "代碼-名稱"
            if any(sep in user_input for sep in [' ', '-', '　']):  # 包含中文空格
                parts = user_input.replace('-', ' ').replace('　', ' ').split()
                if parts and parts[0].isdigit() and len(parts[0]) == 4:
                    stock_code = parts[0]
                    stock_name = ' '.join(parts[1:]) if len(parts) > 1 else ""
                    self.add_status_message(f"✅ 識別為股票代碼: {stock_code} ({stock_name})")
                    return stock_code

            # 情況3：純股票名稱，需要查詢對應的股票代碼
            stock_code = self.lookup_stock_code_by_name(user_input)
            if stock_code:
                self.add_status_message(f"✅ 股票名稱 '{user_input}' 對應代碼: {stock_code}")
                return stock_code

            # 情況4：5-6位數字代碼（ETF等）
            if user_input.isdigit() and 5 <= len(user_input) <= 6:
                # 添加常見ETF名稱提示
                etf_names = {
                    '00930': '永豐ESG低碳高息40',
                    '00903': '富邦元宇宙',
                    '00929': '復華台灣科技優息',
                    '00878': '國泰永續高股息',
                    '00919': '群益台灣精選高息',
                    '0050': '元大台灣50',
                    '0056': '元大高股息'
                }

                etf_name = etf_names.get(user_input, '未知ETF')
                self.add_status_message(f"✅ 識別為ETF代碼: {user_input} ({etf_name})")

                # 特別提醒容易混淆的代碼
                if user_input == '00903':
                    self.add_status_message(f"💡 提醒：00903是富邦元宇宙ETF，如果要查詢永豐ESG請輸入00930")
                elif user_input == '00930':
                    self.add_status_message(f"💡 確認：00930是永豐ESG低碳高息40 ETF")

                return user_input

            # 無法識別
            self.add_status_message(f"❌ 無法識別輸入: '{user_input}'")
            self.add_status_message("💡 支援格式：")
            self.add_status_message("  • 股票代碼：2330")
            self.add_status_message("  • 股票名稱：台積電")
            self.add_status_message("  • 代碼+名稱：2330 台積電")
            return None

        except Exception as e:
            self.add_status_message(f"❌ 解析輸入時發生錯誤: {str(e)}")
            return None

    def lookup_stock_code_by_name(self, stock_name):
        """根據股票名稱查詢股票代碼"""
        try:
            # 常見股票名稱對應表
            stock_name_mapping = {
                # 權值股
                '台積電': '2330', '鴻海': '2317', '聯發科': '2454',
                '台達電': '2308', '聯電': '2303', '廣達': '2382',
                '大立光': '3008', '國巨': '2327', '研華': '2395',
                '中華電': '2412', '台塑': '1301', '南亞': '1303',
                '台泥': '1101', '中鋼': '2002', '統一': '1216',

                # 金融股
                '富邦金': '2881', '國泰金': '2882', '兆豐金': '2886',
                '中信金': '2891', '第一金': '2892', '合庫金': '5880',
                '玉山金': '2884', '永豐金': '2890', '華南金': '2880',

                # 科技股
                '華碩': '2357', '友達': '2409', '南亞科': '2408',
                '和碩': '4938', '緯創': '3231', '仁寶': '2324',
                '英業達': '2356', '宏碁': '2353', '微星': '2377',

                # 傳產股
                '和泰車': '2207', '裕隆': '2201', '中華': '2204',
                '台塑化': '6505', '長榮': '2603', '陽明': '2609',
                '萬海': '2615', '台船': '2208',

                # ETF
                '台灣50': '0050', '元大台灣50': '0050',
                '中型100': '0051', '元大中型100': '0051',
                '富邦科技': '0052', '電子': '0053',
                '台商50': '0054', '高股息': '0056',

                # 其他常見簡稱
                'TSMC': '2330', '鴻海精密': '2317', 'MediaTek': '2454',
                '台積': '2330', '聯發': '2454', '中華電信': '2412',
            }

            # 直接匹配
            if stock_name in stock_name_mapping:
                return stock_name_mapping[stock_name]

            # 模糊匹配（包含關係）
            for name, code in stock_name_mapping.items():
                if stock_name in name or name in stock_name:
                    self.add_status_message(f"💡 模糊匹配: '{stock_name}' → '{name}' ({code})")
                    return code

            # 嘗試從資料庫查詢（如果有連接的話）
            db_code = self.lookup_from_database(stock_name)
            if db_code:
                return db_code

            return None

        except Exception as e:
            self.add_status_message(f"❌ 查詢股票代碼時發生錯誤: {str(e)}")
            return None

    def lookup_from_database(self, stock_name):
        """從資料庫查詢股票代碼"""
        try:
            # 嘗試連接主程式的資料庫
            db_path = "D:/Finlab/history/tables/price.db"
            if not os.path.exists(db_path):
                return None

            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查詢股票名稱
            query = """
                SELECT DISTINCT stock_id
                FROM stock_daily_data
                WHERE stock_name LIKE ?
                   OR stock_name LIKE ?
                   OR stock_name LIKE ?
                ORDER BY stock_id
                LIMIT 1
            """

            cursor.execute(query, (f'%{stock_name}%', f'{stock_name}%', f'%{stock_name}'))
            result = cursor.fetchone()
            conn.close()

            if result:
                stock_code = result[0]
                self.add_status_message(f"💾 從資料庫找到: '{stock_name}' → {stock_code}")
                return stock_code

            return None

        except Exception as e:
            # 資料庫查詢失敗不影響主要功能
            return None
        
    def start_crawling(self):
        """開始爬取新聞"""
        user_input = self.stock_input.text().strip()
        if not user_input:
            QMessageBox.warning(self, "輸入錯誤", "請輸入股票代碼或股票名稱")
            return

        # 解析用戶輸入，支援股票代碼和股票名稱
        stock_code = self.parse_stock_input(user_input)
        if not stock_code:
            self.add_status_message("❌ 格式錯誤：請輸入有效的股票代碼（4位數字）或股票名稱")
            return
        
        days = self.days_spinbox.value()
        
        # 禁用按鈕
        self.start_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不確定進度
        
        # 再次確認股票代碼
        etf_names = {
            '00930': '永豐ESG低碳高息40',
            '00903': '富邦元宇宙',
            '00929': '復華台灣科技優息',
            '00878': '國泰永續高股息',
            '2330': '台積電',
            '2317': '鴻海'
        }

        stock_name = etf_names.get(stock_code, f'代碼{stock_code}')
        self.add_status_message(f"🚀 開始爬取 {stock_code} ({stock_name}) 最近 {days} 天的新聞...")

        # 創建並啟動爬取執行緒
        db_path = self.get_news_database_path()
        self.crawler_thread = NewsCrawlerThread(stock_code, days, db_path)
        self.crawler_thread.progress_updated.connect(self.add_status_message)
        self.crawler_thread.finished_signal.connect(self.on_crawling_finished)
        self.crawler_thread.error_signal.connect(self.on_crawling_error)
        self.crawler_thread.start()
        
    def on_crawling_finished(self, news_list):
        """爬取完成"""
        self.start_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        if news_list and len(news_list) > 0:
            self.add_status_message(f"✅ 爬取完成！共找到 {len(news_list)} 筆新聞")
            self.add_status_message(f"📊 股票代碼: {self.stock_input.text()}")
            self.add_status_message(f"📅 爬取天數: {self.days_spinbox.value()} 天")
            self.add_status_message(f"🗂️ 資料來源: 鉅亨網")
            self.add_status_message(f"💾 新聞已儲存到資料庫中")
        else:
            self.add_status_message("⚠️ 未找到相關新聞")
            self.add_status_message("💡 可能原因：")
            self.add_status_message("  • 該股票在指定時間內沒有相關新聞")
            self.add_status_message("  • 新聞中未提及股票代碼")
            self.add_status_message("  • 網路連線問題")
    
    def on_crawling_error(self, error_message):
        """爬取錯誤"""
        self.start_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.add_status_message(f"❌ 爬取失敗: {error_message}")
        self.add_status_message("🔧 請檢查：")
        self.add_status_message("  • 網路連線是否正常")
        self.add_status_message("  • Selenium 和 ChromeDriver 是否已安裝")
        self.add_status_message("  • 股票代碼是否正確")
    
    def view_database(self):
        """查看資料庫 - 顯示彈窗如右鍵功能"""
        try:
            # 獲取資料庫路徑（從配置或預設）
            db_path = self.get_news_database_path()

            stock_code = self.stock_input.text().strip()
            if not stock_code:
                self.add_status_message("📊 請先輸入股票代碼")
                return

            # 檢查資料庫中的新聞
            self.add_status_message(f"📊 正在查詢 {stock_code} 的新聞資料...")

            # 直接查詢 news_content 表格
            import sqlite3
            if not os.path.exists(db_path):
                self.add_status_message(f"❌ 新聞資料庫不存在: {db_path}")
                self.add_status_message("💡 建議先執行爬取功能")
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT news_id, date, time, source, title, reporter, link, content
                FROM news_content
                WHERE stock_code = ?
                ORDER BY date DESC, time DESC
                LIMIT 100
            ''', (stock_code,))

            results = cursor.fetchall()
            conn.close()

            news_list = []
            for row in results:
                news_list.append({
                    'news_id': row[0],
                    'date': row[1],
                    'time': row[2],
                    'source': row[3],
                    'title': row[4],
                    'reporter': row[5],
                    'link': row[6],
                    'content': row[7]
                })

            if news_list:
                self.add_status_message(f"📊 找到 {len(news_list)} 筆 {stock_code} 的新聞")

                # 顯示新聞彈窗（如右鍵功能）
                self.show_news_dialog(stock_code, news_list)
            else:
                self.add_status_message(f"📊 資料庫中沒有 {stock_code} 的新聞")
                self.add_status_message("💡 建議先執行爬取功能")

        except Exception as e:
            self.add_status_message(f"❌ 查看資料庫失敗: {e}")

    def get_news_database_path(self):
        """獲取新聞資料庫路徑"""
        try:
            # 嘗試從主程式配置獲取
            if hasattr(self.parent(), 'config'):
                db_config = self.parent().config.get('database', {})
                news_db_path = db_config.get('news_db_path')
                if news_db_path and os.path.exists(news_db_path):
                    return news_db_path
        except:
            pass

        # 使用預設路徑
        return "D:/Finlab/history/tables/news.db"

    def show_news_dialog(self, stock_code, news_list):
        """顯示新聞對話框（如右鍵功能）"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel

            # 創建新聞顯示對話框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"📰 {stock_code} 新聞")

            # 設置視窗標誌 - 支援最大化、最小化、關閉
            dialog.setWindowFlags(
                Qt.WindowType.Window |
                Qt.WindowType.WindowMinimizeButtonHint |
                Qt.WindowType.WindowMaximizeButtonHint |
                Qt.WindowType.WindowCloseButtonHint
            )

            dialog.resize(1000, 700)  # 設置初始大小，但允許調整

            layout = QVBoxLayout(dialog)

            # 標題
            title_label = QLabel(f"📰 {stock_code} 相關新聞")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(title_label)

            # 統計資訊
            info_label = QLabel(f"📊 共找到 {len(news_list)} 篇相關新聞")
            info_label.setStyleSheet("margin: 5px; color: #666;")
            layout.addWidget(info_label)

            # 新聞列表
            news_table = QTableWidget()
            news_table.setColumnCount(4)
            news_table.setHorizontalHeaderLabels(["日期", "時間", "來源", "標題"])
            news_table.setRowCount(len(news_list))

            # 設置列寬
            news_table.setColumnWidth(0, 80)   # 日期
            news_table.setColumnWidth(1, 60)   # 時間
            news_table.setColumnWidth(2, 80)   # 來源
            news_table.setColumnWidth(3, 500)  # 標題

            # 填充新聞資料
            for row, news in enumerate(news_list):
                # 處理日期格式
                date_str = news.get('date', '')
                if len(date_str) == 8:  # YYYYMMDD格式
                    formatted_date = f"{date_str[:4]}/{date_str[4:6]}/{date_str[6:]}"
                else:
                    formatted_date = date_str

                date_item = QTableWidgetItem(formatted_date)
                news_table.setItem(row, 0, date_item)

                # 處理時間格式
                time_str = news.get('time', '')
                if len(time_str) == 6:  # HHMMSS格式
                    formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                else:
                    formatted_time = time_str
                time_item = QTableWidgetItem(formatted_time)
                news_table.setItem(row, 1, time_item)

                # 來源
                source_item = QTableWidgetItem(news.get('source', 'cnyes'))
                news_table.setItem(row, 2, source_item)

                # 標題 - 確保有內容顯示
                title = news.get('title', '')
                if not title:  # 如果沒有標題，嘗試其他欄位
                    title = f"鉅亨網新聞 - {stock_code}"

                title_item = QTableWidgetItem(title)
                title_item.setToolTip(title)  # 完整標題作為提示
                news_table.setItem(row, 3, title_item)

            # 設置表格屬性
            news_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
            news_table.setAlternatingRowColors(True)
            news_table.setSortingEnabled(True)
            news_table.verticalHeader().setVisible(False)

            # 雙擊事件 - 開啟新聞連結
            def open_news_link(row, col):
                if row < len(news_list):
                    news_link = news_list[row].get('link', '')
                    if news_link:
                        import webbrowser
                        webbrowser.open(news_link)
                    else:
                        QMessageBox.information(dialog, "提示", "該新聞沒有可用的連結")

            news_table.cellDoubleClicked.connect(open_news_link)

            layout.addWidget(news_table)

            # 按鈕區域
            button_layout = QHBoxLayout()

            # 重新爬取按鈕
            refresh_btn = QPushButton("🔄 重新爬取")
            refresh_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
            refresh_btn.clicked.connect(lambda: (dialog.close(), self.start_crawling()))

            # 關閉按鈕
            close_btn = QPushButton("❌ 關閉")
            close_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }")
            close_btn.clicked.connect(dialog.close)

            button_layout.addWidget(refresh_btn)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # 提示文字
            tip_label = QLabel("💡 提示：雙擊新聞標題可開啟完整新聞連結")
            tip_label.setStyleSheet("color: #888; font-size: 12px; margin: 5px;")
            layout.addWidget(tip_label)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"顯示新聞對話框失敗：\n{str(e)}")
            self.add_status_message(f"❌ 顯示新聞失敗: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 測試對話框
    dialog = NewsCrawlerDialog()
    dialog.show()

    sys.exit(app.exec())
