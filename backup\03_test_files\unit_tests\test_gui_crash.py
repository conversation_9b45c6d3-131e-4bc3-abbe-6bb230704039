#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試GUI閃退問題
"""

import sys
import traceback
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QProgressBar, QLabel
from PyQt6.QtCore import QThread, pyqtSignal, Qt

class TestAnalysisWorker(QThread):
    """測試分析工作線程"""
    progress_updated = pyqtSignal(int, str)
    analysis_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.is_running = True
    
    def run(self):
        try:
            self.progress_updated.emit(10, "初始化分析器...")
            
            # 模擬初始化
            from trading_rule_miner import TradingRuleMiner
            rule_miner = TradingRuleMiner()
            rule_miner.min_success_rate = 0.65
            rule_miner.min_avg_return = 0.02
            
            self.progress_updated.emit(20, "載入股票數據...")
            
            # 執行小量分析避免太久
            combinations = rule_miner.run_mass_analysis(50)  # 只分析50檔
            
            if not self.is_running:
                return
            
            self.progress_updated.emit(60, "提取交易規則...")
            
            # 提取交易規則
            rules = rule_miner.extract_trading_rules(combinations)
            
            self.progress_updated.emit(80, "生成交易口訣...")
            
            # 生成口訣
            from trading_mantra_generator import TradingMantraGenerator
            mantra_generator = TradingMantraGenerator()
            mantras = mantra_generator.generate_all_mantras(rules)
            
            self.progress_updated.emit(100, "分析完成！")
            
            # 返回結果
            result = {
                'rules': rules,
                'mantras': mantras,
                'combinations': combinations,
                'total_stocks': 50,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.analysis_completed.emit(result)
            
        except Exception as e:
            import traceback
            error_msg = f"分析過程發生錯誤: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)
    
    def stop(self):
        self.is_running = False

class TestGUI(QMainWindow):
    """測試GUI"""
    
    def __init__(self):
        super().__init__()
        self.analysis_worker = None
        self.current_results = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🧪 GUI閃退測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 佈局
        layout = QVBoxLayout(central_widget)
        
        # 標題
        title_label = QLabel("🧪 GUI閃退測試工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #0078d4; margin: 5px; padding: 5px;")
        layout.addWidget(title_label)
        
        # 開始按鈕
        self.start_btn = QPushButton("🚀 開始測試")
        self.start_btn.clicked.connect(self.start_analysis)
        layout.addWidget(self.start_btn)
        
        # 停止按鈕
        self.stop_btn = QPushButton("⏹️ 停止測試")
        self.stop_btn.clicked.connect(self.stop_analysis)
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn)
        
        # 進度條
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 進度標籤
        self.progress_label = QLabel("就緒")
        layout.addWidget(self.progress_label)
        
        # 日誌
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        layout.addWidget(self.log_text)
        
        # 狀態欄
        self.statusBar().showMessage("就緒 - 點擊開始測試")
    
    def start_analysis(self):
        """開始分析"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🚀 開始測試分析...")
            
            # 更新UI狀態
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
            
            # 創建並啟動工作線程
            self.analysis_worker = TestAnalysisWorker()
            self.analysis_worker.progress_updated.connect(self.update_progress)
            self.analysis_worker.analysis_completed.connect(self.analysis_finished)
            self.analysis_worker.error_occurred.connect(self.analysis_error)
            self.analysis_worker.start()
            
            self.statusBar().showMessage("測試進行中...")
            
        except Exception as e:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 啟動測試失敗: {str(e)}")
            traceback.print_exc()
    
    def stop_analysis(self):
        """停止分析"""
        if self.analysis_worker:
            self.analysis_worker.stop()
            self.analysis_worker.wait()
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.statusBar().showMessage("測試已停止")
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ⏹️ 測試已停止")
    
    def update_progress(self, value: int, message: str):
        """更新進度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def analysis_finished(self, results: dict):
        """分析完成"""
        try:
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 測試分析完成！")
            self.current_results = results
            
            # 更新UI狀態
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            
            # 顯示結果摘要
            rules_count = len(results.get('rules', []))
            mantras = results.get('mantras', {})
            buy_mantras_count = len(mantras.get('buy_mantras', []))
            sell_mantras_count = len(mantras.get('sell_mantras', []))
            
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 結果摘要:")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 交易規則: {rules_count} 條")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 買入口訣: {buy_mantras_count} 條")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 賣出口訣: {sell_mantras_count} 條")
            
            # 測試策略組合生成
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 測試策略組合生成...")
            from strategy_combination_generator import StrategyCombinationGenerator
            
            generator = StrategyCombinationGenerator()
            strategy_mantras = generator.generate_all_combinations()
            
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 買入策略: {len(strategy_mantras['buy'])} 條")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 賣出策略: {len(strategy_mantras['sell'])} 條")
            
            # 測試股票掃描
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📡 測試股票掃描...")
            from stock_signal_scanner import StockSignalScanner
            
            scanner = StockSignalScanner()
            test_mantra = {
                'mantra_text': '【MACD+BB】MACD金叉配布林下軌反彈，雙重確認買',
                'category': 'buy',
                'success_rate': 0.69,
                'avg_profit': 0.062
            }
            
            signals = scanner.scan_stocks_by_mantra(test_mantra)
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}]   • 找到信號: {len(signals)} 個")
            
            self.statusBar().showMessage("✅ 測試完成！")
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 所有測試完成，沒有閃退！")
            
        except Exception as e:
            import traceback
            error_msg = f"分析完成處理失敗: {str(e)}\n{traceback.format_exc()}"
            self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ {error_msg}")
            self.statusBar().showMessage("❌ 測試處理失敗")
            
            # 確保UI狀態正確
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
    
    def analysis_error(self, error_message: str):
        """分析錯誤"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 測試錯誤: {error_message}")
        self.statusBar().showMessage("❌ 測試失敗")

def main():
    """主函數"""
    app = QApplication(sys.argv)
    
    # 設置應用程式屬性
    app.setApplicationName("GUI閃退測試工具")
    app.setApplicationVersion("1.0")
    
    # 創建並顯示主視窗
    window = TestGUI()
    window.show()
    
    print("🧪 GUI閃退測試工具已啟動")
    print("請點擊「開始測試」按鈕進行測試")
    print("如果程式沒有閃退，說明問題已解決")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
