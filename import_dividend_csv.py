#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
除權息CSV資料快速匯入工具 (命令行版本)
使用方式: python import_dividend_csv.py [CSV檔案路徑]
"""

import os
import sys
import sqlite3
import pandas as pd
import logging
from datetime import datetime
import argparse

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DividendCSVImporter:
    """除權息CSV匯入器"""
    
    def __init__(self, db_path="D:/Finlab/history/tables/dividend_data.db"):
        self.db_path = db_path
        
    def check_database(self):
        """檢查資料庫是否存在"""
        if not os.path.exists(self.db_path):
            logger.error(f"❌ 除權息資料庫不存在: {self.db_path}")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dividend_data'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                logger.error("❌ dividend_data 表格不存在")
                conn.close()
                return False
            
            conn.close()
            logger.info("✅ 資料庫檢查通過")
            return True
            
        except Exception as e:
            logger.error(f"❌ 資料庫檢查失敗: {e}")
            return False
    
    def analyze_csv(self, csv_file):
        """分析CSV檔案結構"""
        try:
            logger.info(f"🔍 分析CSV檔案: {csv_file}")
            
            # 嘗試不同編碼
            encodings = ['utf-8-sig', 'utf-8', 'big5', 'cp950']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding, nrows=5)
                    logger.info(f"✅ 使用編碼: {encoding}")
                    break
                except:
                    continue
            
            if df is None:
                logger.error("❌ 無法讀取CSV檔案，請檢查檔案格式")
                return None
            
            logger.info(f"📊 檔案資訊:")
            logger.info(f"  欄位數量: {len(df.columns)}")
            logger.info(f"  欄位名稱: {', '.join(df.columns)}")
            
            # 檢查必要欄位
            required_columns = ['stock_code']
            optional_columns = ['stock_name', 'ex_dividend_date', 'cash_dividend', 'stock_dividend', 'year']
            
            missing_required = [col for col in required_columns if col not in df.columns]
            available_optional = [col for col in optional_columns if col in df.columns]
            
            if missing_required:
                logger.error(f"❌ 缺少必要欄位: {', '.join(missing_required)}")
                return None
            
            logger.info(f"✅ 必要欄位完整")
            logger.info(f"📋 可用選用欄位: {', '.join(available_optional)}")
            
            # 重新讀取完整檔案
            for encoding in encodings:
                try:
                    full_df = pd.read_csv(csv_file, encoding=encoding)
                    logger.info(f"📊 總資料筆數: {len(full_df)}")
                    return full_df, encoding
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 分析CSV檔案失敗: {e}")
            return None
    
    def process_data(self, df, default_year=None):
        """處理CSV資料"""
        try:
            logger.info("🔄 開始處理資料...")
            
            if default_year is None:
                default_year = datetime.now().year
            
            records = []
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # 處理股票代碼
                    stock_code = str(row['stock_code']).strip()
                    if len(stock_code) < 4:
                        stock_code = stock_code.zfill(4)
                    
                    # 處理股票名稱
                    stock_name = str(row.get('stock_name', '')).strip()
                    if not stock_name or stock_name == 'nan':
                        stock_name = f"股票{stock_code}"
                    
                    # 處理年份
                    year = default_year
                    if 'year' in row and pd.notna(row['year']):
                        try:
                            year = int(row['year'])
                        except:
                            year = default_year
                    
                    # 處理除權息日期
                    ex_dividend_date = None
                    if 'ex_dividend_date' in row and pd.notna(row['ex_dividend_date']):
                        ex_dividend_date = str(row['ex_dividend_date']).strip()
                        if ex_dividend_date and ex_dividend_date != 'nan':
                            # 標準化日期格式
                            ex_dividend_date = self.normalize_date(ex_dividend_date)
                    
                    # 處理股利資料
                    cash_dividend = 0.0
                    if 'cash_dividend' in row and pd.notna(row['cash_dividend']):
                        try:
                            cash_dividend = float(row['cash_dividend'])
                        except:
                            cash_dividend = 0.0
                    
                    stock_dividend = 0.0
                    if 'stock_dividend' in row and pd.notna(row['stock_dividend']):
                        try:
                            stock_dividend = float(row['stock_dividend'])
                        except:
                            stock_dividend = 0.0
                    
                    total_dividend = cash_dividend + stock_dividend
                    
                    # 處理其他欄位
                    eps = None
                    if 'eps' in row and pd.notna(row['eps']):
                        try:
                            eps = float(row['eps'])
                        except:
                            eps = None
                    
                    dividend_yield = None
                    if 'dividend_yield' in row and pd.notna(row['dividend_yield']):
                        try:
                            dividend_yield = float(row['dividend_yield'])
                        except:
                            dividend_yield = None
                    
                    # 資料來源
                    data_source = str(row.get('data_source', 'csv_import')).strip()
                    if not data_source or data_source == 'nan':
                        data_source = 'csv_import'
                    
                    record = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'year': year,
                        'ex_dividend_date': ex_dividend_date,
                        'cash_dividend': cash_dividend,
                        'stock_dividend': stock_dividend,
                        'total_dividend': total_dividend,
                        'eps': eps,
                        'dividend_yield': dividend_yield,
                        'data_source': data_source
                    }
                    
                    records.append(record)
                    
                except Exception as e:
                    logger.warning(f"⚠️ 跳過第{index+1}行: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"✅ 資料處理完成")
            logger.info(f"  成功處理: {len(records)} 筆")
            logger.info(f"  跳過無效: {skipped_count} 筆")
            
            return records
            
        except Exception as e:
            logger.error(f"❌ 資料處理失敗: {e}")
            return []
    
    def normalize_date(self, date_str):
        """標準化日期格式"""
        try:
            if '/' in date_str:
                # 處理 2025/06/01 或 '25/06/01 格式
                parts = date_str.replace("'", "").split('/')
                if len(parts) == 3:
                    year, month, day = parts
                    if len(year) == 2:
                        year = f"20{year}"
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            elif '-' not in date_str and len(date_str) == 8:
                # 處理 20250601 格式
                return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            elif '-' in date_str:
                # 已經是標準格式
                return date_str
            
            return date_str
            
        except:
            return date_str
    
    def import_to_database(self, records, overwrite=True):
        """匯入資料到資料庫"""
        try:
            logger.info("💾 開始匯入資料庫...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            success_count = 0
            updated_count = 0
            error_count = 0
            
            for record in records:
                try:
                    if overwrite:
                        # 檢查是否已存在
                        cursor.execute('''
                            SELECT id FROM dividend_data 
                            WHERE stock_code = ? AND year = ? AND data_source = ?
                        ''', (record['stock_code'], record['year'], record['data_source']))
                        
                        existing = cursor.fetchone()
                        
                        if existing:
                            # 更新現有資料
                            cursor.execute('''
                                UPDATE dividend_data 
                                SET stock_name = ?, ex_dividend_date = ?, cash_dividend = ?, 
                                    stock_dividend = ?, total_dividend = ?, eps = ?, 
                                    dividend_yield = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE stock_code = ? AND year = ? AND data_source = ?
                            ''', (
                                record['stock_name'], record['ex_dividend_date'], 
                                record['cash_dividend'], record['stock_dividend'], 
                                record['total_dividend'], record['eps'], 
                                record['dividend_yield'], record['stock_code'], 
                                record['year'], record['data_source']
                            ))
                            updated_count += 1
                        else:
                            # 插入新資料
                            cursor.execute('''
                                INSERT INTO dividend_data 
                                (stock_code, stock_name, year, ex_dividend_date, cash_dividend, 
                                 stock_dividend, total_dividend, eps, dividend_yield, 
                                 data_source, created_at, updated_at)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                            ''', (
                                record['stock_code'], record['stock_name'], record['year'],
                                record['ex_dividend_date'], record['cash_dividend'], 
                                record['stock_dividend'], record['total_dividend'], 
                                record['eps'], record['dividend_yield'], record['data_source']
                            ))
                            success_count += 1
                    else:
                        # 只插入新資料，不覆蓋
                        cursor.execute('''
                            INSERT OR IGNORE INTO dividend_data 
                            (stock_code, stock_name, year, ex_dividend_date, cash_dividend, 
                             stock_dividend, total_dividend, eps, dividend_yield, 
                             data_source, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ''', (
                            record['stock_code'], record['stock_name'], record['year'],
                            record['ex_dividend_date'], record['cash_dividend'], 
                            record['stock_dividend'], record['total_dividend'], 
                            record['eps'], record['dividend_yield'], record['data_source']
                        ))
                        success_count += 1
                        
                except Exception as e:
                    logger.warning(f"⚠️ 儲存失敗 {record['stock_code']}: {e}")
                    error_count += 1
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 資料庫匯入完成")
            logger.info(f"  新增資料: {success_count} 筆")
            logger.info(f"  更新資料: {updated_count} 筆")
            logger.info(f"  錯誤資料: {error_count} 筆")
            
            return success_count, updated_count, error_count
            
        except Exception as e:
            logger.error(f"❌ 資料庫匯入失敗: {e}")
            return 0, 0, len(records)
    
    def import_csv(self, csv_file, year=None, overwrite=True):
        """完整的CSV匯入流程"""
        try:
            logger.info("🚀 開始除權息CSV匯入流程...")
            
            # 1. 檢查資料庫
            if not self.check_database():
                return False
            
            # 2. 分析CSV檔案
            result = self.analyze_csv(csv_file)
            if result is None:
                return False
            
            df, encoding = result
            
            # 3. 處理資料
            records = self.process_data(df, year)
            if not records:
                logger.error("❌ 沒有有效資料可匯入")
                return False
            
            # 4. 匯入資料庫
            success_count, updated_count, error_count = self.import_to_database(records, overwrite)
            
            # 5. 總結
            total_processed = success_count + updated_count
            logger.info("🎉 匯入完成！")
            logger.info(f"📊 匯入統計:")
            logger.info(f"  總處理筆數: {len(records)}")
            logger.info(f"  成功匯入: {total_processed} 筆")
            logger.info(f"  新增: {success_count} 筆")
            logger.info(f"  更新: {updated_count} 筆")
            logger.info(f"  錯誤: {error_count} 筆")
            
            return total_processed > 0
            
        except Exception as e:
            logger.error(f"❌ 匯入流程失敗: {e}")
            return False

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='除權息CSV資料匯入工具')
    parser.add_argument('csv_file', nargs='?', help='CSV檔案路徑')
    parser.add_argument('--year', type=int, help='預設年份')
    parser.add_argument('--no-overwrite', action='store_true', help='不覆蓋現有資料')
    parser.add_argument('--db', help='資料庫路徑')
    
    args = parser.parse_args()
    
    # 如果沒有提供CSV檔案，顯示使用說明
    if not args.csv_file:
        print("🔧 除權息CSV資料匯入工具")
        print("=" * 40)
        print("使用方式:")
        print("  python import_dividend_csv.py <CSV檔案路徑> [選項]")
        print("")
        print("選項:")
        print("  --year YEAR        指定預設年份")
        print("  --no-overwrite     不覆蓋現有資料")
        print("  --db PATH          指定資料庫路徑")
        print("")
        print("範例:")
        print("  python import_dividend_csv.py dividend_data.csv")
        print("  python import_dividend_csv.py dividend_data.csv --year 2024")
        print("  python import_dividend_csv.py dividend_data.csv --no-overwrite")
        return
    
    # 檢查CSV檔案是否存在
    if not os.path.exists(args.csv_file):
        logger.error(f"❌ CSV檔案不存在: {args.csv_file}")
        return
    
    # 創建匯入器
    db_path = args.db or "D:/Finlab/history/tables/dividend_data.db"
    importer = DividendCSVImporter(db_path)
    
    # 執行匯入
    success = importer.import_csv(
        csv_file=args.csv_file,
        year=args.year,
        overwrite=not args.no_overwrite
    )
    
    if success:
        logger.info("🎉 匯入成功完成！")
        print("\n✅ 除權息資料已成功匯入到資料庫")
        print("📍 現在可以在除權息交易系統中使用這些資料了")
    else:
        logger.error("❌ 匯入失敗")
        print("\n❌ 匯入失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
