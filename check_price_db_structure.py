#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 price.db 的結構，並與 newprice.db 比較
"""

import sqlite3
import pandas as pd
import os

def check_price_db_structure():
    """檢查 price.db 的結構"""
    
    print("=" * 80)
    print("🔍 檢查 price.db 與 newprice.db 的結構")
    print("=" * 80)
    
    # 檢查 price.db
    price_db = r'D:\Finlab\history\tables\price.db'
    newprice_db = r'D:\Finlab\history\tables\newprice.db'
    
    for db_name, db_file in [('標準 price.db', price_db), ('當前 newprice.db', newprice_db)]:
        print(f"\n📊 {db_name}:")
        
        if not os.path.exists(db_file):
            print(f"   ❌ 檔案不存在: {db_file}")
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 檢查檔案大小
            size = os.path.getsize(db_file) / (1024 * 1024)
            print(f"   📂 檔案大小: {size:.2f} MB")
            
            # 檢查表格列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            print(f"   📋 表格: {table_names}")
            
            # 檢查主要表格的結構
            main_table = None
            if 'price' in table_names:
                main_table = 'price'
            elif 'stock_daily_data' in table_names:
                main_table = 'stock_daily_data'
            
            if main_table:
                print(f"\n   📊 主要表格: {main_table}")
                
                # 檢查表格結構
                cursor.execute(f"PRAGMA table_info({main_table})")
                columns = cursor.fetchall()
                print(f"   📋 欄位結構:")
                for i, (cid, name, type_, notnull, default, pk) in enumerate(columns, 1):
                    pk_str = " (PRIMARY KEY)" if pk else ""
                    null_str = " NOT NULL" if notnull else ""
                    print(f"      {i:2d}. {name:<20} {type_:<10}{null_str}{pk_str}")
                
                # 檢查資料筆數
                cursor.execute(f"SELECT COUNT(*) FROM {main_table}")
                count = cursor.fetchone()[0]
                print(f"   📊 資料筆數: {count:,}")
                
                # 檢查股票數
                cursor.execute(f"SELECT COUNT(DISTINCT stock_id) FROM {main_table}")
                stocks = cursor.fetchone()[0]
                print(f"   📈 不重複股票數: {stocks:,}")
                
                # 檢查日期範圍
                cursor.execute(f"SELECT MIN(date), MAX(date) FROM {main_table}")
                date_range = cursor.fetchone()
                print(f"   📅 日期範圍: {date_range[0]} ~ {date_range[1]}")
                
                # 檢查範例資料
                cursor.execute(f"SELECT * FROM {main_table} WHERE stock_id = '0050' ORDER BY date DESC LIMIT 3")
                samples = cursor.fetchall()
                
                if samples:
                    print(f"   📊 0050 範例資料:")
                    column_names = [desc[0] for desc in cursor.description]
                    for i, row in enumerate(samples, 1):
                        print(f"      第{i}筆: {dict(zip(column_names, row))}")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ 檢查失敗: {e}")
    
    print(f"\n" + "=" * 80)

if __name__ == "__main__":
    check_price_db_structure()
