#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試更新後的財務資料爬蟲
"""

import sys
import os
import datetime

# 添加 finlab 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
finlab_path = os.path.join(current_dir, 'finlab')
if finlab_path not in sys.path:
    sys.path.insert(0, finlab_path)

def test_updated_crawler():
    """測試更新後的爬蟲"""
    
    print("=" * 80)
    print("🧪 測試更新後的統一財務資料爬蟲")
    print("=" * 80)
    
    try:
        # 測試導入
        print("📦 測試模組導入...")
        from crawler import crawl_unified_financial_data
        print("✅ 成功導入 crawl_unified_financial_data 函數")
        
        # 測試日期
        test_date = datetime.datetime.now()
        print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')}")
        
        # 執行爬蟲測試
        print(f"\n🚀 開始測試統一財務資料爬蟲...")
        result = crawl_unified_financial_data(test_date)
        
        # 檢查結果
        if result is not None and len(result) > 0:
            print(f"✅ 爬蟲測試成功！")
            print(f"📊 獲取資料筆數: {len(result):,}")
            print(f"📋 資料欄位數: {len(result.columns)}")
            
            # 檢查基本欄位
            basic_columns = ['stock_id', 'stock_name', 'year', 'quarter', 'date', 'report_date']
            missing_columns = [col for col in basic_columns if col not in result.columns]
            
            if not missing_columns:
                print(f"✅ 基本欄位完整")
            else:
                print(f"⚠️ 缺少基本欄位: {missing_columns}")
            
            # 檢查損益表欄位
            income_columns = [col for col in result.columns if col.startswith('income_')]
            print(f"📊 損益表欄位數: {len(income_columns)}")
            
            # 檢查資產負債表欄位
            balance_columns = [col for col in result.columns if col.startswith('balance_')]
            print(f"🏦 資產負債表欄位數: {len(balance_columns)}")
            
            # 顯示台積電範例
            if 'stock_id' in result.columns:
                tsmc_data = result[result['stock_id'] == '2330']
                if len(tsmc_data) > 0:
                    print(f"\n📈 台積電 (2330) 資料範例:")
                    row = tsmc_data.iloc[0]
                    print(f"   公司名稱: {row.get('stock_name', 'N/A')}")
                    print(f"   年度: {row.get('year', 'N/A')} (西元年)")
                    print(f"   季別: {row.get('quarter', 'N/A')}")
                    print(f"   統一日期: {row.get('date', 'N/A')}")
                    
                    # 顯示主要財務數據
                    if 'income_營業收入' in result.columns:
                        revenue = row.get('income_營業收入', 'N/A')
                        if revenue != 'N/A':
                            print(f"   營業收入: {float(revenue):,.0f} 千元")
                        else:
                            print(f"   營業收入: N/A")
                    if 'balance_資產總額' in result.columns:
                        assets = row.get('balance_資產總額', 'N/A')
                        if assets != 'N/A':
                            print(f"   資產總額: {float(assets):,.0f} 千元")
                        else:
                            print(f"   資產總額: N/A")
            
            return True
            
        else:
            print(f"⚠️ 爬蟲測試返回空資料")
            return False
            
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_update_integration():
    """測試 auto_update 整合"""
    
    print(f"\n📋 測試 auto_update 整合...")
    
    try:
        # 檢查 auto_update.py 配置
        auto_update_path = 'auto_update.py'
        
        if os.path.exists(auto_update_path):
            with open(auto_update_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查導入
            if 'crawl_unified_financial_data' in content:
                print(f"   ✅ auto_update.py 包含 crawl_unified_financial_data 導入")
            else:
                print(f"   ❌ auto_update.py 缺少 crawl_unified_financial_data 導入")
            
            # 檢查任務配置
            if "'financial_data'" in content and 'crawl_unified_financial_data' in content:
                print(f"   ✅ financial_data 任務已配置")
            else:
                print(f"   ❌ financial_data 任務未配置")
            
            # 檢查舊任務是否已移除
            if 'twse_financial_statements' in content:
                print(f"   ⚠️ 仍包含舊的 twse_financial_statements 任務")
            else:
                print(f"   ✅ 已移除舊的 twse_financial_statements 任務")
            
            return True
        else:
            print(f"   ❌ 未找到 auto_update.py 檔案")
            return False
            
    except Exception as e:
        print(f"❌ 檢查 auto_update 整合失敗: {e}")
        return False

def check_database_file():
    """檢查資料庫檔案"""
    
    print(f"\n💾 檢查資料庫檔案...")
    
    # 檢查統一財務資料檔案
    unified_db = r'D:\Finlab\history\tables\financial_data.db'
    
    if os.path.exists(unified_db):
        print(f"   ✅ 統一財務資料檔案存在: {unified_db}")
        
        try:
            import sqlite3
            conn = sqlite3.connect(unified_db)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM financial_data")
            count = cursor.fetchone()[0]
            print(f"   📊 資料筆數: {count:,}")
            conn.close()
        except Exception as e:
            print(f"   ⚠️ 檢查資料庫內容失敗: {e}")
    else:
        print(f"   ⚠️ 統一財務資料檔案不存在: {unified_db}")
    
    # 檢查舊檔案
    old_files = [
        'income_statements.db',
        'balance_sheets.db', 
        'financial_statements.db'
    ]
    
    print(f"\n📋 舊檔案狀況:")
    for filename in old_files:
        file_path = os.path.join(r'D:\Finlab\history\tables', filename)
        if os.path.exists(file_path):
            print(f"   ⚠️ {filename} 仍存在 (可考慮移除)")
        else:
            print(f"   ✅ {filename} 已移除")

def show_usage_instructions():
    """顯示使用說明"""
    
    print(f"\n📝 更新後的使用說明")
    print("=" * 60)
    
    print(f"🚀 啟動方式:")
    print(f"   1. 執行統一財務資料爬蟲:")
    print(f"      python auto_update.py financial_data")
    print(f"   2. 執行完整自動更新:")
    print(f"      python auto_update.py")
    print()
    
    print(f"📊 資料檔案:")
    print(f"   - 新檔案: D:\\Finlab\\history\\tables\\financial_data.db")
    print(f"   - 表格: financial_data")
    print(f"   - 內容: 綜合損益表 + 資產負債表 (統一格式)")
    print()
    
    print(f"🔍 查詢範例:")
    print(f"   -- 查詢台積電完整財務資料")
    print(f"   SELECT * FROM financial_data WHERE stock_id = '2330';")
    print()
    print(f"   -- 查詢 2025 年資料")
    print(f"   SELECT * FROM financial_data WHERE year = 2025;")
    print()
    print(f"   -- ROA 計算")
    print(f"   SELECT stock_id, stock_name,")
    print(f"          ROUND(CAST(income_本期淨利（淨損） AS REAL) /")
    print(f"                CAST(balance_資產總額 AS REAL) * 100, 2) as ROA")
    print(f"   FROM financial_data;")

def main():
    """主函數"""
    
    print("🧪 更新後的財務資料爬蟲測試")
    
    # 測試1: 爬蟲功能
    test1_result = test_updated_crawler()
    
    # 測試2: auto_update 整合
    test2_result = test_auto_update_integration()
    
    # 檢查資料庫檔案
    check_database_file()
    
    # 顯示使用說明
    show_usage_instructions()
    
    # 總結
    print(f"\n" + "=" * 80)
    print(f"📊 測試結果總結")
    print(f"=" * 80)
    
    if test1_result and test2_result:
        print(f"🎉 所有測試通過！更新後的財務資料爬蟲已準備就緒")
        print(f"\n✅ 改進成果:")
        print(f"   - 統一財務資料檔案 (financial_data.db)")
        print(f"   - 西元年日期格式")
        print(f"   - 損益表和資產負債表合併")
        print(f"   - 標準化欄位命名")
        
        print(f"\n🚀 立即可用:")
        print(f"   python auto_update.py financial_data")
    else:
        print(f"⚠️ 部分測試失敗，請檢查相關配置")
    
    return test1_result and test2_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
