#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強版全球市場數據爬蟲
"""

import sys
import time
from datetime import datetime

def test_safe_scanner():
    """測試安全掃描器"""
    print("🌍 測試增強版全球市場數據爬蟲")
    print("=" * 60)
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        # 創建掃描器
        scanner = SafeMarketScanner()
        print(f"✅ 掃描器初始化成功: {scanner.name} v{scanner.version}")
        
        # 檢查真實數據獲取器
        if scanner.real_fetcher:
            print("✅ 真實數據獲取器已載入")
        else:
            print("⚠️ 使用模擬數據")
        
        print("\n🔍 開始掃描...")
        start_time = time.time()
        
        # 執行掃描
        results = scanner.run_full_scan()
        
        scan_time = time.time() - start_time
        
        if results:
            print(f"\n✅ 掃描完成，耗時 {scan_time:.2f}秒")
            print(f"📊 掃描時間: {results.get('timestamp', 'N/A')}")
            
            # 統計各類別數據
            categories = [
                ('us_indices', '美股指數'),
                ('asia_indices', '亞洲指數'),
                ('china_indices', '中國股市指數'),
                ('taiwan_futures', '台股數據'),
                ('taiwan_futures_positions', '台股期貨多空資料'),
                ('foreign_futures_positions', '外資期貨多空單'),
                ('commodities', '商品價格'),
                ('fx_rates', '外匯匯率（含美元指數）'),
                ('crypto', '加密貨幣')
            ]
            
            print("\n📈 數據統計:")
            total_items = 0
            for key, name in categories:
                if key in results and results[key]:
                    count = len(results[key])
                    total_items += count
                    print(f"   • {name}: {count} 項")
                    
                    # 顯示前2項數據作為示例
                    for i, (item_name, item_data) in enumerate(list(results[key].items())[:2]):
                        if isinstance(item_data, dict):
                            if 'contracts' in item_data:
                                contracts = item_data.get('contracts', 0)
                                change = item_data.get('change', 0)
                                status = item_data.get('status', '')
                                print(f"     - {item_name}: {contracts:,}口 ({change:+,}) [{status}]")
                            else:
                                price = item_data.get('price', item_data.get('rate', 0))
                                change_pct = item_data.get('change_pct', 0)
                                status = item_data.get('status', '')
                                print(f"     - {item_name}: {price} ({change_pct:+.2f}%) [{status}]")
                else:
                    print(f"   • {name}: 0 項 ❌")
            
            print(f"\n📊 總計: {total_items} 項數據")
            
            # 檢查新增功能
            print("\n🆕 新增功能檢查:")
            
            # 檢查中國股市指數
            if 'china_indices' in results and results['china_indices']:
                china_count = len(results['china_indices'])
                print(f"   ✅ 中國股市指數: {china_count} 項")
                china_names = list(results['china_indices'].keys())
                print(f"      包含: {', '.join(china_names[:3])}...")
            else:
                print("   ❌ 中國股市指數: 未獲取")
            
            # 檢查美元指數
            if 'fx_rates' in results and '美元指數' in results['fx_rates']:
                dollar_data = results['fx_rates']['美元指數']
                rate = dollar_data.get('rate', 0)
                change_pct = dollar_data.get('change_pct', 0)
                status = dollar_data.get('status', '')
                print(f"   ✅ 美元指數: {rate} ({change_pct:+.2f}%) [{status}]")
            else:
                print("   ❌ 美元指數: 未獲取")
            
            # 檢查台股期貨多空資料
            if 'taiwan_futures_positions' in results and results['taiwan_futures_positions']:
                tw_pos_count = len(results['taiwan_futures_positions'])
                print(f"   ✅ 台股期貨多空資料: {tw_pos_count} 項")
                pos_names = list(results['taiwan_futures_positions'].keys())
                print(f"      包含: {', '.join(pos_names[:3])}...")
            else:
                print("   ❌ 台股期貨多空資料: 未獲取")
            
            # 檢查外資期貨多空單
            if 'foreign_futures_positions' in results and results['foreign_futures_positions']:
                foreign_pos_count = len(results['foreign_futures_positions'])
                print(f"   ✅ 外資期貨多空單: {foreign_pos_count} 項")
                foreign_names = list(results['foreign_futures_positions'].keys())
                print(f"      包含: {', '.join(foreign_names[:3])}...")
            else:
                print("   ❌ 外資期貨多空單: 未獲取")
            
            return True
        else:
            print("❌ 掃描失敗，未獲取到數據")
            return False
            
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_market_crawler_dialog():
    """測試市場數據爬蟲對話框"""
    print("\n🖥️ 測試市場數據爬蟲對話框")
    print("=" * 60)
    
    try:
        from market_data_crawler_dialog import MarketDataCrawlerDialog
        print("✅ 市場數據爬蟲對話框模組載入成功")
        
        # 檢查工作器
        from market_data_crawler_dialog import MarketCrawlerWorker
        print("✅ 市場爬蟲工作器載入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 對話框模組導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 對話框測試失敗: {e}")
        return False

def test_real_data_fetcher():
    """測試真實數據獲取器"""
    print("\n🌐 測試真實數據獲取器")
    print("=" * 60)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        print("✅ 真實數據獲取器模組載入成功")
        
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        # 測試各個功能（不實際執行，避免網路請求）
        print("📋 可用功能:")
        print("   • get_china_indices_real() - 中國股市指數")
        print("   • get_dollar_index_real() - 美元指數")
        print("   • get_taiwan_futures_positions_real() - 台股期貨多空資料")
        print("   • get_foreign_futures_positions_real() - 外資期貨多空單")
        print("   • get_enhanced_fx_rates() - 增強版外匯匯率")
        
        return True
        
    except ImportError as e:
        print(f"❌ 真實數據獲取器導入失敗: {e}")
        print("⚠️ 將使用模擬數據")
        return False
    except Exception as e:
        print(f"❌ 真實數據獲取器測試失敗: {e}")
        return False

if __name__ == "__main__":
    print(f"🕐 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 執行所有測試
    tests = [
        ("安全掃描器", test_safe_scanner),
        ("市場數據爬蟲對話框", test_market_crawler_dialog),
        ("真實數據獲取器", test_real_data_fetcher)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   • {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 測試結果: {passed}/{len(results)} 通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！全球市場數據爬蟲增強完成！")
    else:
        print("⚠️ 部分測試失敗，請檢查相關模組")
