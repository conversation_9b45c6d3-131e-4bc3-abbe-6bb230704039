#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的財務指標測試
"""

import os
import sqlite3

def test_databases():
    """測試資料庫連接和數據"""
    print("🔍 測試資料庫連接")
    print("=" * 50)
    
    # 測試PE資料庫
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    if os.path.exists(pe_db_path):
        try:
            conn = sqlite3.connect(pe_db_path)
            cursor = conn.cursor()
            
            # 查詢最新日期
            cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
            latest_date = cursor.fetchone()
            print(f"✅ PE資料庫最新日期: {latest_date[0] if latest_date else 'N/A'}")
            
            # 查詢1101的資料
            if latest_date:
                cursor.execute("""
                    SELECT stock_id, stock_name, dividend_yield, pe_ratio, pb_ratio
                    FROM pe_data
                    WHERE stock_id = '1101' AND date = ?
                """, (latest_date[0],))
                
                result = cursor.fetchone()
                if result:
                    print(f"✅ 1101台泥PE資料:")
                    print(f"   殖利率: {result[2]}")
                    print(f"   本益比: {result[3]}")
                    print(f"   股淨比: {result[4]}")
                else:
                    print("❌ 找不到1101的PE資料")
                    
            conn.close()
        except Exception as e:
            print(f"❌ PE資料庫錯誤: {e}")
    else:
        print(f"❌ PE資料庫不存在: {pe_db_path}")
    
    print()
    
    # 測試股價資料庫
    price_db_path = 'D:/Finlab/history/tables/price.db'
    if os.path.exists(price_db_path):
        try:
            conn = sqlite3.connect(price_db_path)
            cursor = conn.cursor()
            
            # 查詢最新日期
            cursor.execute("SELECT DISTINCT date FROM stock_daily_data ORDER BY date DESC LIMIT 1")
            latest_date = cursor.fetchone()
            print(f"✅ 股價資料庫最新日期: {latest_date[0] if latest_date else 'N/A'}")
            
            # 查詢1101的股價
            if latest_date:
                cursor.execute("""
                    SELECT stock_id, stock_name, Close
                    FROM stock_daily_data
                    WHERE stock_id = '1101' AND date = ?
                """, (latest_date[0],))
                
                result = cursor.fetchone()
                if result:
                    print(f"✅ 1101台泥股價: {result[2]}元")
                else:
                    print("❌ 找不到1101的股價資料")
                    
            conn.close()
        except Exception as e:
            print(f"❌ 股價資料庫錯誤: {e}")
    else:
        print(f"❌ 股價資料庫不存在: {price_db_path}")

def test_eps_calculation():
    """測試EPS計算"""
    print("\n🧮 測試EPS計算")
    print("=" * 50)
    
    def calculate_eps(close_price, pe_ratio):
        """EPS計算函數"""
        try:
            if close_price is None or pe_ratio is None:
                return 'N/A'
            
            close_price = float(close_price)
            pe_ratio = float(pe_ratio)
            
            if pe_ratio <= 0:
                return 'N/A'
            
            eps = close_price / pe_ratio
            return f"{eps:.2f}"
            
        except (ValueError, TypeError, ZeroDivisionError):
            return 'N/A'
    
    # 測試案例
    test_cases = [
        (24.20, 19.06, "1.27"),  # 台泥
        (40.55, 12.71, "3.19"),  # 亞泥
        (62.30, 14.62, "4.26"),  # 大成
        (0, 15.0, "N/A"),        # 股價為0
        (25.0, 0, "N/A"),        # 本益比為0
        (25.0, -5.0, "N/A"),     # 本益比為負
    ]
    
    for stock_price, pe_ratio, expected in test_cases:
        result = calculate_eps(stock_price, pe_ratio)
        status = "✅" if result == expected else "❌"
        print(f"{status} EPS({stock_price}, {pe_ratio}) = {result} (預期: {expected})")

def test_real_data_calculation():
    """使用真實資料測試EPS計算"""
    print("\n📊 使用真實資料測試EPS計算")
    print("=" * 50)
    
    # 獲取1101的真實資料
    pe_db_path = 'D:/Finlab/history/tables/pe_data.db'
    price_db_path = 'D:/Finlab/history/tables/price.db'
    
    if not (os.path.exists(pe_db_path) and os.path.exists(price_db_path)):
        print("❌ 資料庫文件不存在，無法進行真實資料測試")
        return
    
    try:
        # 獲取PE資料
        pe_conn = sqlite3.connect(pe_db_path)
        pe_cursor = pe_conn.cursor()
        
        pe_cursor.execute("SELECT DISTINCT date FROM pe_data ORDER BY date DESC LIMIT 1")
        pe_date = pe_cursor.fetchone()[0]
        
        pe_cursor.execute("""
            SELECT dividend_yield, pe_ratio, pb_ratio
            FROM pe_data
            WHERE stock_id = '1101' AND date = ?
        """, (pe_date,))
        
        pe_result = pe_cursor.fetchone()
        pe_conn.close()
        
        # 獲取股價資料
        price_conn = sqlite3.connect(price_db_path)
        price_cursor = price_conn.cursor()
        
        price_cursor.execute("SELECT DISTINCT date FROM stock_daily_data ORDER BY date DESC LIMIT 1")
        price_date = price_cursor.fetchone()[0]
        
        price_cursor.execute("""
            SELECT Close
            FROM stock_daily_data
            WHERE stock_id = '1101' AND date = ?
        """, (price_date,))
        
        price_result = price_cursor.fetchone()
        price_conn.close()
        
        if pe_result and price_result:
            dividend_yield, pe_ratio, pb_ratio = pe_result
            close_price = price_result[0]
            
            print(f"📈 1101台泥真實資料:")
            print(f"   股價: {close_price}元")
            print(f"   殖利率: {dividend_yield}%")
            print(f"   本益比: {pe_ratio}")
            print(f"   股淨比: {pb_ratio}")
            
            # 計算EPS
            if pe_ratio and pe_ratio > 0:
                eps = close_price / pe_ratio
                print(f"   計算EPS: {close_price} ÷ {pe_ratio} = {eps:.2f}元")
                print("✅ EPS計算成功")
            else:
                print("❌ 無法計算EPS（本益比無效）")
        else:
            print("❌ 無法獲取完整的財務資料")
            
    except Exception as e:
        print(f"❌ 真實資料測試失敗: {e}")

if __name__ == "__main__":
    print("🚀 簡單財務指標測試")
    
    # 測試資料庫
    test_databases()
    
    # 測試EPS計算函數
    test_eps_calculation()
    
    # 使用真實資料測試
    test_real_data_calculation()
    
    print("\n✅ 測試完成")
