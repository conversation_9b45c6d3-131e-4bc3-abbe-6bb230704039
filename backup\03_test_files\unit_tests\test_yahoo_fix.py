#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的Yahoo股市新聞爬蟲
"""

import logging
import sys
import os

def test_yahoo_rss_search():
    """測試Yahoo爬蟲的Google RSS搜尋功能"""
    try:
        print("🧪 測試Yahoo爬蟲修復")
        print("=" * 40)
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        
        # 導入爬蟲
        from yahoo_stock_news_crawler import YahooStockNewsCrawler
        
        # 創建爬蟲實例
        crawler = YahooStockNewsCrawler()
        
        # 測試Google RSS搜尋
        print("🔍 測試Google RSS搜尋 - 2330台積電")
        news_list = crawler._search_via_google_rss("2330", days=7)
        
        print(f"📊 RSS搜尋結果: {len(news_list)} 筆新聞")
        
        if news_list:
            print("✅ RSS搜尋成功！")
            for i, news in enumerate(news_list[:3], 1):
                print(f"  {i}. {news['title'][:50]}...")
                print(f"     來源: {news['source']}")
                print(f"     連結: {news['link'][:60]}...")
                print()
        else:
            print("⚠️ RSS搜尋沒有結果")
        
        # 測試完整搜尋流程
        print("-" * 40)
        print("🔍 測試完整搜尋流程")
        all_news = crawler.search_stock_news("2330", days=7)
        
        print(f"📊 完整搜尋結果: {len(all_news)} 筆新聞")
        
        if all_news:
            print("✅ 完整搜尋成功！")
            print("📰 新聞預覽:")
            for i, news in enumerate(all_news[:5], 1):
                print(f"  {i}. {news['title'][:60]}...")
                print(f"     來源: {news['source']}")
                print(f"     日期: {news['date']}")
                print()
        else:
            print("❌ 完整搜尋失敗")
        
        return len(all_news) > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 Yahoo股市新聞爬蟲修復測試")
    print("=" * 50)
    
    success = test_yahoo_rss_search()
    
    print("=" * 50)
    if success:
        print("🎉 修復成功！Yahoo爬蟲現在可以正常工作了")
        print("💡 主要改進:")
        print("  • 新增Google RSS搜尋作為主要方法")
        print("  • 移除了過度嚴格的Yahoo域名過濾")
        print("  • 保留WebDriver作為備用方案")
        print("  • 改善了錯誤處理")
    else:
        print("⚠️ 仍需進一步調整")
        print("💡 建議:")
        print("  • 檢查網路連線")
        print("  • 確認相關模組已安裝")
        print("  • 查看詳細錯誤訊息")

if __name__ == "__main__":
    main()
