#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票篩選功能整合測試
===================

測試股票篩選器在各個爬蟲模組中的整合情況
"""

import sys
import logging
from typing import List, Dict, Any

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_stock_filter_module():
    """測試股票篩選器模組"""
    print("🔍 測試股票篩選器模組")
    print("=" * 50)
    
    try:
        from stock_filter import StockFilter
        
        # 創建篩選器
        filter = StockFilter()
        
        # 測試股票清單
        test_stocks = [
            '2330',    # 台積電 - 有效
            '2317',    # 鴻海 - 有效  
            '00631L',  # 元大台灣50正2 - 有效
            '00878',   # 國泰永續高股息 - 有效
            '030001',  # 權證 - 無效
            '020011',  # ETN - 無效
            '912000',  # TDR - 無效
            '2330A',   # 權證 - 無效
        ]
        
        # 執行篩選
        valid_stocks = filter.filter_stock_list(test_stocks)
        
        print(f"✅ 股票篩選器模組測試成功")
        print(f"📊 原始股票: {len(test_stocks)} 支")
        print(f"📊 有效股票: {len(valid_stocks)} 支")
        print(f"📋 有效股票清單: {valid_stocks}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票篩選器模組測試失敗: {e}")
        return False


def test_daily_trading_crawler_integration():
    """測試每日交易資料爬蟲的股票篩選整合"""
    print("\n🔍 測試每日交易資料爬蟲整合")
    print("=" * 50)
    
    try:
        from daily_trading_crawler_gui import DailyTradingCrawlerGUI
        
        # 創建爬蟲實例（不啟動GUI）
        crawler = DailyTradingCrawlerGUI()
        
        # 檢查股票篩選器是否正確初始化
        if hasattr(crawler, 'stock_filter') and crawler.stock_filter:
            print("✅ 股票篩選器已正確初始化")
        else:
            print("⚠️ 股票篩選器未初始化或初始化失敗")
        
        # 檢查篩選方法是否存在
        if hasattr(crawler, 'filter_stock_codes'):
            print("✅ 股票篩選方法已整合")
            
            # 測試篩選功能
            test_codes = ['2330', '00631L', '030001', '2317']
            filtered_codes = crawler.filter_stock_codes(test_codes)
            print(f"📊 篩選測試: {test_codes} → {filtered_codes}")
        else:
            print("❌ 股票篩選方法未整合")
        
        # 檢查設定方法是否存在
        if hasattr(crawler, 'set_stock_filter'):
            print("✅ 股票篩選設定方法已整合")
        else:
            print("❌ 股票篩選設定方法未整合")
        
        print("✅ 每日交易資料爬蟲整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 每日交易資料爬蟲整合測試失敗: {e}")
        return False


def test_unified_crawler_integration():
    """測試統一股票爬蟲的股票篩選整合"""
    print("\n🔍 測試統一股票爬蟲整合")
    print("=" * 50)
    
    try:
        from unified_stock_crawler import UnifiedStockCrawlerGUI
        
        # 創建爬蟲實例（不啟動GUI）
        crawler = UnifiedStockCrawlerGUI()
        
        # 檢查股票篩選器是否正確初始化
        if hasattr(crawler, 'stock_filter') and crawler.stock_filter:
            print("✅ 股票篩選器已正確初始化")
        else:
            print("⚠️ 股票篩選器未初始化或初始化失敗")
        
        # 檢查篩選方法是否存在
        if hasattr(crawler, 'filter_stock_codes'):
            print("✅ 股票篩選方法已整合")
            
            # 測試篩選功能
            test_codes = ['2330', '00878', '030001', '2454']
            filtered_codes = crawler.filter_stock_codes(test_codes)
            print(f"📊 篩選測試: {test_codes} → {filtered_codes}")
        else:
            print("❌ 股票篩選方法未整合")
        
        # 檢查設定方法是否存在
        if hasattr(crawler, 'set_stock_filter'):
            print("✅ 股票篩選設定方法已整合")
        else:
            print("❌ 股票篩選設定方法未整合")
        
        print("✅ 統一股票爬蟲整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 統一股票爬蟲整合測試失敗: {e}")
        return False


def test_main_gui_integration():
    """測試主GUI的股票篩選整合"""
    print("\n🔍 測試主GUI整合")
    print("=" * 50)
    
    try:
        # 這裡我們只測試方法是否存在，不實際創建GUI
        import O3mh_gui_v21_optimized
        
        # 檢查主GUI類是否有股票篩選相關方法
        gui_class = O3mh_gui_v21_optimized.StockScreenerGUI
        
        methods_to_check = [
            'get_valid_stock_filter',
            'is_valid_stock_code', 
            'filter_stock_list'
        ]
        
        for method_name in methods_to_check:
            if hasattr(gui_class, method_name):
                print(f"✅ 主GUI方法 {method_name} 已整合")
            else:
                print(f"❌ 主GUI方法 {method_name} 未整合")
        
        print("✅ 主GUI整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 主GUI整合測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🚀 股票篩選功能整合測試")
    print("=" * 60)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("股票篩選器模組", test_stock_filter_module()))
    test_results.append(("每日交易資料爬蟲", test_daily_trading_crawler_integration()))
    test_results.append(("統一股票爬蟲", test_unified_crawler_integration()))
    test_results.append(("主GUI", test_main_gui_integration()))
    
    # 顯示測試結果摘要
    print("\n📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！股票篩選功能已成功整合到各個模組中。")
        print("\n📋 功能說明:")
        print("• 篩選規則：4碼、5碼及前綴為00的6碼ETF股票")
        print("• 排除項目：權證、ETN、TDR等衍生性商品")
        print("• 整合模組：每日交易資料爬蟲、統一股票爬蟲、主GUI")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查相關模組的整合情況。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
