#!/usr/bin/env python3
"""
核心工具模組
從原始程式中提取的工具函數和裝飾器
"""
import os
import time
import logging
import traceback
from datetime import datetime
from functools import wraps


def setup_logging():
    """設置日誌系統"""
    log_format = '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'

    # 創建logs目錄
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 設置日誌文件路徑
    log_file = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同時輸出到控制台
        ]
    )

    # 記錄啟動信息
    logging.info("=" * 60)
    logging.info("🚀 台股智能選股系統啟動")
    logging.info(f"📅 啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"📁 日誌文件: {log_file}")
    logging.info("=" * 60)


def performance_monitor(func):
    """性能監控裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time

            # 只記錄耗時較長的操作
            if execution_time > 1.0:
                logging.info(f"⏱️ {func.__name__} 執行時間: {execution_time:.2f}秒")

            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            logging.error(f"❌ {func.__name__} 執行失敗 (耗時{execution_time:.2f}秒): {str(e)}")
            raise
    return wrapper


def safe_execute(default_return=None, show_error=True):
    """安全執行裝飾器，捕獲異常並返回默認值"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logging.error(f"🛡️ {func.__name__} 安全執行失敗: {str(e)}")
                if show_error and args and hasattr(args[0], 'show_status'):
                    args[0].show_status(f"{func.__name__} 執行失敗: {str(e)}", is_error=True)
                return default_return
        return wrapper
    return decorator