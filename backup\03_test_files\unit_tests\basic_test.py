#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 基本功能測試")
print("=" * 40)

# 測試1: 基本導入
try:
    import sqlite3
    import os
    from datetime import datetime
    print("✅ 基本模組導入成功")
except Exception as e:
    print(f"❌ 基本模組導入失敗: {e}")

# 測試2: 數據庫創建
try:
    db_path = "db/test.db"
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_table (
            id INTEGER PRIMARY KEY,
            name TEXT,
            value REAL
        )
    ''')
    
    cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", ("test", 123.45))
    conn.commit()
    
    cursor.execute("SELECT * FROM test_table")
    result = cursor.fetchone()
    
    conn.close()
    
    if result:
        print(f"✅ 數據庫操作成功: {result}")
    else:
        print("❌ 數據庫查詢無結果")
        
except Exception as e:
    print(f"❌ 數據庫操作失敗: {e}")

# 測試3: 月份解析
def parse_month_string(month_str):
    try:
        if '-' in month_str:
            month_part, year_part = month_str.split('-')
            
            month_mapping = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
            }
            
            month = month_mapping.get(month_part)
            year = int(year_part) + 2000 if int(year_part) < 50 else int(year_part) + 1900
            
            return year, month
        
        return None, None
        
    except Exception as e:
        return None, None

# 測試月份解析
test_months = ['Jun-25', 'May-25', 'Dec-24']
print("\n📅 月份解析測試:")
for month_str in test_months:
    year, month = parse_month_string(month_str)
    if year and month:
        print(f"✅ {month_str} -> {year}/{month:02d}")
    else:
        print(f"❌ {month_str} -> 解析失敗")

# 測試4: 示例數據處理
sample_data = [
    ('Jun-25', 958, 1060, 2637, -17.7, 26.9),
    ('May-25', 938, 967, 3205, -8.31, 39.6),
    ('Apr-25', 929, 908, 3496, 22.2, 48.1)
]

print("\n📊 示例數據處理:")
for i, data_row in enumerate(sample_data, 1):
    month_str, open_price, close_price, revenue, mom, yoy = data_row
    year, month = parse_month_string(month_str)
    
    if year and month:
        print(f"{i}. {year}/{month:02d} - 營收: {revenue:,}億, 年增率: {yoy:+.1f}%")
    else:
        print(f"{i}. 解析失敗: {month_str}")

print("\n🎉 基本功能測試完成！")
print("\n📋 總結:")
print("✅ 基本模組可用")
print("✅ 數據庫操作正常")
print("✅ 月份解析功能正常")
print("✅ 數據處理邏輯正常")
print("\n下一步可以進行完整的月營收數據處理測試。")
