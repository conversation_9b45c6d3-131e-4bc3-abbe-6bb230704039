#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Selenium 版本的月營收爬蟲
"""

# 修復 ipywidgets 依賴問題
import sys
import types

class MockOutput:
    def __init__(self, **kwargs):
        pass
    def capture(self):
        def decorator(func):
            return func
        return decorator

widgets_mock = types.ModuleType('widgets')
widgets_mock.Output = MockOutput
sys.modules['ipywidgets'] = widgets_mock
sys.modules['widgets'] = widgets_mock

import datetime
import pandas as pd
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 從 auto_update.py 導入 Selenium 函數
from auto_update import (
    crawl_monthly_report_selenium,
    get_selenium_driver,
    selenium_get_page,
    close_selenium_driver
)

def test_selenium_driver():
    """測試 Selenium WebDriver 初始化"""
    print("🔧 測試 Selenium WebDriver 初始化")
    print("=" * 60)
    
    try:
        driver = get_selenium_driver()
        
        if driver is not None:
            print("✅ Selenium WebDriver 初始化成功")
            
            # 測試基本功能
            print("🧪 測試基本功能...")
            driver.get("https://www.google.com")
            title = driver.title
            print(f"   Google 標題: {title}")
            
            return True
        else:
            print("❌ Selenium WebDriver 初始化失敗")
            return False
            
    except Exception as e:
        print(f"❌ Selenium WebDriver 測試失敗: {str(e)}")
        return False

def test_selenium_mops_access():
    """測試 Selenium 訪問 MOPS 網站"""
    print(f"\n🔧 測試 Selenium 訪問 MOPS")
    print("=" * 60)
    
    mops_url = "https://mops.twse.com.tw/"
    
    try:
        page_source = selenium_get_page(mops_url, wait_seconds=10)
        
        if page_source:
            print(f"✅ Selenium 成功訪問 MOPS")
            print(f"   頁面長度: {len(page_source)} 字元")
            
            # 檢查是否被封鎖
            if '您的網頁IP已經被證交所封鎖' in page_source:
                print(f"   ⚠️ IP 被封鎖")
                return False
            else:
                print(f"   ✅ IP 未被封鎖")
                return True
        else:
            print(f"❌ Selenium 無法訪問 MOPS")
            return False
            
    except Exception as e:
        print(f"❌ Selenium MOPS 訪問失敗: {str(e)}")
        return False

def test_selenium_monthly_crawler():
    """測試 Selenium 月營收爬蟲"""
    print(f"\n🔧 測試 Selenium 月營收爬蟲")
    print("=" * 60)
    
    # 測試較舊的月份 (確定已發布)
    test_date = datetime.datetime(2024, 12, 10)  # 2024年11月營收
    
    print(f"📅 測試日期: {test_date.strftime('%Y-%m-%d')} (爬取 2024年11月營收)")
    
    try:
        print("🔄 開始測試 Selenium 月營收爬蟲...")
        result = crawl_monthly_report_selenium(test_date)
        
        if result is not None and not result.empty:
            print(f"\n✅ Selenium 月營收爬蟲成功!")
            print(f"   資料筆數: {len(result)}")
            print(f"   索引: {result.index.names}")
            print(f"   欄位: {list(result.columns)}")
            
            # 檢查資料格式
            print(f"\n🔍 格式檢查:")
            
            # 檢查索引
            if result.index.names == ['stock_id', 'date']:
                print(f"   ✅ 索引格式正確")
            else:
                print(f"   ❌ 索引格式錯誤: {result.index.names}")
            
            # 檢查股票代碼
            stock_ids = result.index.get_level_values('stock_id')
            sample_ids = stock_ids[:3].tolist()
            print(f"   股票代碼樣本: {sample_ids}")
            
            # 檢查台積電
            tsmc_candidates = [sid for sid in stock_ids if '2330' in str(sid)]
            if tsmc_candidates:
                tsmc_id = tsmc_candidates[0]
                print(f"   找到台積電: {tsmc_id}")
                
                if '當月營收' in result.columns:
                    tsmc_data = result.loc[tsmc_id]
                    revenue = tsmc_data['當月營收'].iloc[0] if hasattr(tsmc_data['當月營收'], 'iloc') else tsmc_data['當月營收']
                    print(f"   台積電營收: {revenue:,}")
            
            # 顯示樣本資料
            print(f"\n📊 樣本資料:")
            print(result.head(3))
            
            # 保存測試結果
            result.to_pickle('monthly_selenium_test.pkl')
            print(f"\n💾 測試資料已保存: monthly_selenium_test.pkl")
            
            return True
        else:
            print(f"\n⚠️ Selenium 月營收爬蟲無資料")
            return False
            
    except Exception as e:
        print(f"\n❌ Selenium 月營收爬蟲失敗: {str(e)}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")
        return False

def compare_with_existing():
    """與現有資料比較"""
    print(f"\n🔍 與現有 monthly_report.pkl 格式比較")
    print("=" * 60)
    
    try:
        existing_file = 'history/tables/monthly_report.pkl'
        test_file = 'monthly_selenium_test.pkl'
        
        if os.path.exists(existing_file) and os.path.exists(test_file):
            existing_data = pd.read_pickle(existing_file)
            test_data = pd.read_pickle(test_file)
            
            print(f"📊 格式比較:")
            print(f"   現有資料索引: {existing_data.index.names}")
            print(f"   測試資料索引: {test_data.index.names}")
            
            print(f"   現有資料欄位: {list(existing_data.columns)}")
            print(f"   測試資料欄位: {list(test_data.columns)}")
            
            # 檢查格式相容性
            if existing_data.index.names == test_data.index.names:
                print(f"   ✅ 索引格式相容")
            else:
                print(f"   ❌ 索引格式不相容")
            
            common_columns = set(existing_data.columns).intersection(set(test_data.columns))
            print(f"   共同欄位: {len(common_columns)} 個 - {list(common_columns)}")
            
            if len(common_columns) >= 1:
                print(f"   ✅ 欄位基本相容")
            else:
                print(f"   ❌ 欄位差異太大")
                
        else:
            print(f"❌ 無法比較 (檔案不存在)")
            
    except Exception as e:
        print(f"❌ 比較失敗: {str(e)}")

def main():
    """主函數"""
    print("🔧 Selenium 月營收爬蟲測試工具")
    print("=" * 60)
    print("🎯 目標: 測試 Selenium 版本的月營收爬蟲")
    print("💡 特色: 真實瀏覽器模擬 + 反檢測機制")
    print("=" * 60)
    
    tests = [
        ("Selenium WebDriver", test_selenium_driver),
        ("MOPS 網站訪問", test_selenium_mops_access),
        ("月營收爬蟲", test_selenium_monthly_crawler),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            
            # 如果前面的測試失敗，跳過後續測試
            if not result and test_name != "月營收爬蟲":
                print(f"⚠️ {test_name} 失敗，跳過後續測試")
                break
                
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {str(e)}")
            results.append((test_name, False))
            break
    
    # 如果月營收爬蟲成功，進行格式比較
    if len(results) > 0 and results[-1][0] == "月營收爬蟲" and results[-1][1]:
        compare_with_existing()
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print(f"🎉 Selenium 月營收爬蟲完全成功！")
        print(f"💡 現在可以在 auto_update.py 中使用 Selenium 版本")
    else:
        print(f"⚠️ Selenium 爬蟲可能需要調整")
    
    # 清理資源
    try:
        close_selenium_driver()
        print(f"\n🧹 Selenium 資源已清理")
    except Exception as e:
        print(f"\n⚠️ 清理 Selenium 資源時出錯: {str(e)}")

if __name__ == "__main__":
    import os
    main()
