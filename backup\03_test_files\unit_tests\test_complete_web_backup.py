#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完整的網頁爬取備援系統
"""

import logging
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_complete_backup_system():
    """測試完整的備援系統"""
    print("🚀 測試完整的網頁爬取備援系統")
    print("=" * 60)
    
    try:
        from real_market_data_fetcher import RealMarketDataFetcher
        
        # 創建獲取器
        start_time = time.time()
        fetcher = RealMarketDataFetcher()
        print("✅ 真實數據獲取器初始化成功")
        
        results = {}
        
        # 測試美股指數（已有網頁備援）
        print("\n📈 測試美股指數（yfinance + 網頁備援）...")
        us_start = time.time()
        us_data = fetcher.get_us_indices_real()
        us_time = time.time() - us_start
        
        if us_data:
            print(f"✅ 美股指數獲取成功 ({us_time:.1f}秒):")
            api_count = 0
            web_count = 0
            for name, data in us_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
                
                if 'yfinance API' in status:
                    api_count += 1
                elif '網頁爬取' in status:
                    web_count += 1
            
            print(f"    API成功: {api_count}, 網頁備援: {web_count}")
            results['us_indices'] = {'success': True, 'count': len(us_data), 'api': api_count, 'web': web_count}
        else:
            print(f"❌ 美股指數獲取失敗 ({us_time:.1f}秒)")
            results['us_indices'] = {'success': False, 'count': 0, 'api': 0, 'web': 0}
        
        # 測試商品價格（已有網頁備援）
        print("\n🛢️ 測試商品價格（yfinance + 網頁備援）...")
        commodity_start = time.time()
        commodity_data = fetcher.get_commodities_real()
        commodity_time = time.time() - commodity_start
        
        if commodity_data:
            print(f"✅ 商品價格獲取成功 ({commodity_time:.1f}秒):")
            api_count = 0
            web_count = 0
            for name, data in commodity_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: ${price} ({change_pct:+.2f}%) - {status}")
                
                if 'yfinance API' in status:
                    api_count += 1
                elif '網頁爬取' in status:
                    web_count += 1
            
            print(f"    API成功: {api_count}, 網頁備援: {web_count}")
            results['commodities'] = {'success': True, 'count': len(commodity_data), 'api': api_count, 'web': web_count}
        else:
            print(f"❌ 商品價格獲取失敗 ({commodity_time:.1f}秒)")
            results['commodities'] = {'success': False, 'count': 0, 'api': 0, 'web': 0}
        
        # 測試外匯匯率（已有網頁備援）
        print("\n💱 測試外匯匯率（yfinance + 網頁備援）...")
        fx_start = time.time()
        fx_data = fetcher.get_enhanced_fx_rates()
        fx_time = time.time() - fx_start
        
        if fx_data:
            print(f"✅ 外匯匯率獲取成功 ({fx_time:.1f}秒):")
            api_count = 0
            web_count = 0
            for name, data in fx_data.items():
                rate = data.get('rate', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {rate} ({change_pct:+.2f}%) - {status}")
                
                if 'yfinance API' in status:
                    api_count += 1
                elif '網頁爬取' in status:
                    web_count += 1
            
            print(f"    API成功: {api_count}, 網頁備援: {web_count}")
            results['fx_rates'] = {'success': True, 'count': len(fx_data), 'api': api_count, 'web': web_count}
        else:
            print(f"❌ 外匯匯率獲取失敗 ({fx_time:.1f}秒)")
            results['fx_rates'] = {'success': False, 'count': 0, 'api': 0, 'web': 0}
        
        # 測試中國股市（僅yfinance）
        print("\n🇨🇳 測試中國股市指數（僅yfinance）...")
        china_start = time.time()
        china_data = fetcher.get_china_indices_real()
        china_time = time.time() - china_start
        
        if china_data:
            print(f"✅ 中國股市指數獲取成功 ({china_time:.1f}秒):")
            for name, data in china_data.items():
                price = data.get('price', 0)
                change_pct = data.get('change_pct', 0)
                status = data.get('status', '未知')
                print(f"  • {name}: {price} ({change_pct:+.2f}%) - {status}")
            
            results['china_indices'] = {'success': True, 'count': len(china_data)}
        else:
            print(f"❌ 中國股市指數獲取失敗 ({china_time:.1f}秒)")
            results['china_indices'] = {'success': False, 'count': 0}
        
        total_time = time.time() - start_time
        print(f"\n⏱️ 總耗時: {total_time:.1f}秒")
        
        return results
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return {}
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return {}

def test_safe_scanner_with_backup():
    """測試安全掃描器的備援功能"""
    print("\n🔍 測試安全掃描器備援功能...")
    
    try:
        from safe_market_scanner import SafeMarketScanner
        
        scanner = SafeMarketScanner()
        
        # 測試完整掃描
        print("🌍 執行完整市場掃描（包含備援）...")
        start_time = time.time()
        
        # 獲取所有數據
        results = {
            'us_indices': scanner.get_us_indices(),
            'commodities': scanner.get_commodities(),
            'fx_rates': scanner.get_fx_rates(),
            'china_indices': scanner.get_china_indices()
        }
        
        scan_time = time.time() - start_time
        
        # 統計結果
        total_items = 0
        successful_items = 0
        api_items = 0
        web_items = 0
        
        for category, data in results.items():
            if data:
                category_count = len(data)
                total_items += category_count
                
                # 計算成功獲取的項目和數據源
                for item_name, item_data in data.items():
                    status = item_data.get('status', '')
                    if '真實數據' in status:
                        successful_items += 1
                        if 'yfinance API' in status:
                            api_items += 1
                        elif '網頁爬取' in status:
                            web_items += 1
                
                print(f"  • {category}: {category_count} 項")
        
        success_rate = (successful_items / total_items * 100) if total_items > 0 else 0
        
        print(f"\n📊 掃描結果:")
        print(f"  • 總項目: {total_items}")
        print(f"  • 成功獲取: {successful_items}")
        print(f"  • API成功: {api_items}")
        print(f"  • 網頁備援: {web_items}")
        print(f"  • 成功率: {success_rate:.1f}%")
        print(f"  • 掃描耗時: {scan_time:.1f}秒")
        
        return {
            'total': total_items,
            'success': successful_items,
            'api': api_items,
            'web': web_items,
            'success_rate': success_rate
        }
        
    except Exception as e:
        print(f"❌ 安全掃描器測試失敗: {e}")
        return {}

def main():
    """主函數"""
    print("🚀 完整網頁爬取備援系統測試")
    print("=" * 70)
    
    # 測試完整備援系統
    backup_results = test_complete_backup_system()
    
    # 測試安全掃描器
    scanner_results = test_safe_scanner_with_backup()
    
    # 總結
    print("\n" + "=" * 70)
    print("📋 完整備援系統總結:")
    
    if backup_results:
        success_categories = sum(1 for category, result in backup_results.items() if result.get('success', False))
        total_categories = len(backup_results)
        
        print(f"\n📊 分類測試結果: {success_categories}/{total_categories} 成功")
        
        total_api = 0
        total_web = 0
        for category, result in backup_results.items():
            if result.get('success', False):
                api_count = result.get('api', 0)
                web_count = result.get('web', 0)
                total_api += api_count
                total_web += web_count
                print(f"  • {category}: API={api_count}, 網頁={web_count}")
        
        print(f"\n🔧 數據源統計:")
        print(f"  • yfinance API 成功: {total_api} 項")
        print(f"  • 網頁爬取備援: {total_web} 項")
        print(f"  • 備援使用率: {(total_web/(total_api+total_web)*100):.1f}%" if (total_api+total_web) > 0 else "  • 備援使用率: 0%")
    
    if scanner_results:
        print(f"\n🔍 掃描器結果:")
        print(f"  • 總成功率: {scanner_results.get('success_rate', 0):.1f}%")
        print(f"  • API/網頁比例: {scanner_results.get('api', 0)}/{scanner_results.get('web', 0)}")
    
    print("\n🎯 技術成果:")
    print("• ✅ 成功整合您提供的網頁爬取程式碼")
    print("• ✅ 建立三層備援機制：API即時 → API歷史 → 網頁爬取")
    print("• ✅ 使用正則表達式解析Yahoo Finance頁面")
    print("• ✅ 整合反爬蟲機制到網頁爬取")
    print("• ✅ 自動在API失敗時切換到網頁備援")
    
    print("\n💡 備援策略優勢:")
    print("• 提高數據獲取成功率")
    print("• 減少因API限制導致的失敗")
    print("• 保持數據的即時性和準確性")
    print("• 提供多重數據源保障")
    
    # 判斷整體成功
    if backup_results and scanner_results:
        overall_success = (
            sum(1 for result in backup_results.values() if result.get('success', False)) >= 2 and
            scanner_results.get('success_rate', 0) >= 70
        )
        
        if overall_success:
            print("\n🎉 完整網頁爬取備援系統測試成功！")
            print("現在系統具備了強大的多重數據源保障能力。")
        else:
            print("\n⚠️ 部分功能需要進一步優化")
    else:
        print("\n⚠️ 測試未完成，需要檢查系統狀態")

if __name__ == "__main__":
    main()
