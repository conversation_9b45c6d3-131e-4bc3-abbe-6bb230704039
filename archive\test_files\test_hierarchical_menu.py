#!/usr/bin/env python3
"""
測試階層式策略選單
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtWidgets import QPushButton, QMenu

class HierarchicalComboBox(QPushButton):
    """階層式下拉選單"""
    
    # 定義信號
    strategy_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setText("請選擇策略...")
        self.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 5px 10px;
                border: 1px solid #ccc;
                background-color: white;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
        """)
        
        self.current_strategy = ""
        self.strategies = {}
        self.strategy_categories = {}
        
        # 創建主選單
        self.main_menu = QMenu(self)
        self.clicked.connect(self.show_menu)
    
    def show_menu(self):
        """顯示選單"""
        self.main_menu.exec(self.mapToGlobal(self.rect().bottomLeft()))
    
    def set_strategies(self, strategies, categories):
        """設置策略和分類"""
        self.strategies = strategies
        self.strategy_categories = categories
        self.update_menu()
    
    def update_menu(self):
        """更新選單內容"""
        self.main_menu.clear()
        
        for category_name, strategy_list in self.strategy_categories.items():
            # 創建子選單
            category_menu = QMenu(category_name, self)
            
            # 添加該分類下的策略
            for strategy_name in strategy_list:
                if strategy_name in self.strategies:
                    action = category_menu.addAction(strategy_name)
                    action.triggered.connect(lambda checked, name=strategy_name: self.select_strategy(name))
            
            # 將子選單添加到主選單
            self.main_menu.addMenu(category_menu)
    
    def select_strategy(self, strategy_name):
        """選擇策略"""
        self.current_strategy = strategy_name
        self.setText(strategy_name)
        self.strategy_changed.emit(strategy_name)
    
    def currentText(self):
        """獲取當前選擇的策略"""
        return self.current_strategy
    
    def setCurrentText(self, text):
        """設置當前策略"""
        self.current_strategy = text
        self.setText(text)

class TestWindow(QMainWindow):
    """測試視窗"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("階層式策略選單測試")
        self.setGeometry(100, 100, 400, 300)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 創建佈局
        layout = QVBoxLayout(central_widget)
        
        # 添加說明標籤
        info_label = QLabel("測試階層式策略選單：")
        layout.addWidget(info_label)
        
        # 創建階層式下拉選單
        self.strategy_combo = HierarchicalComboBox()
        self.strategy_combo.strategy_changed.connect(self.on_strategy_changed)
        layout.addWidget(self.strategy_combo)
        
        # 顯示當前選擇的標籤
        self.current_label = QLabel("當前選擇：無")
        layout.addWidget(self.current_label)
        
        # 設置測試數據
        self.setup_test_data()
    
    def setup_test_data(self):
        """設置測試數據"""
        # 模擬策略數據
        strategies = {
            "勝率73.45%": [{"type": "high_win_rate"}],
            "破底反彈高量": [{"type": "bottom_rebound"}],
            "阿水一式": [{"type": "ashui_strategy"}],
            "阿水二式": [{"type": "ashui_short_strategy"}],
            "藏獒": [{"type": "tibetan_mastiff_strategy"}],
            "CANSLIM量價齊升": [{"type": "canslim_strategy"}],
            "膽小貓": [{"type": "timid_cat_strategy"}],
            "二次創高股票": [{"type": "second_high_strategy"}],
            "三頻率RSI策略": [{"type": "triple_rsi_strategy"}],
            "5分鐘突破策略": [{"type": "five_min_breakout_strategy"}],
            "5分鐘均值回歸": [{"type": "five_min_mean_reversion_strategy"}],
            "5分鐘動量策略": [{"type": "five_min_momentum_strategy"}],
            "5分鐘量價策略": [{"type": "five_min_volume_price_strategy"}],
            "5分鐘剝頭皮": [{"type": "five_min_scalping_strategy"}],
            "5分鐘趨勢跟隨": [{"type": "five_min_trend_following_strategy"}]
        }
        
        # 策略分類
        strategy_categories = {
            "📈 經典策略": ["勝率73.45%", "破底反彈高量"],
            "🌟 阿水策略系列": ["阿水一式", "阿水二式"],
            "🚀 進階策略": ["藏獒", "CANSLIM量價齊升", "膽小貓", "二次創高股票", "三頻率RSI策略"],
            "⚡ 5分鐘短線策略": [
                "5分鐘突破策略", "5分鐘均值回歸", "5分鐘動量策略",
                "5分鐘量價策略", "5分鐘剝頭皮", "5分鐘趨勢跟隨"
            ]
        }
        
        # 設置到選單中
        self.strategy_combo.set_strategies(strategies, strategy_categories)
    
    def on_strategy_changed(self, strategy_name):
        """當策略改變時"""
        self.current_label.setText(f"當前選擇：{strategy_name}")
        print(f"策略已切換到：{strategy_name}")

def main():
    """主程序"""
    app = QApplication(sys.argv)
    
    # 創建測試視窗
    window = TestWindow()
    window.show()
    
    print("🚀 階層式策略選單測試程序啟動")
    print("📋 測試功能：")
    print("   1. 點擊策略選單按鈕")
    print("   2. 滑鼠移到主分類上會展開子選單")
    print("   3. 點擊具體策略進行選擇")
    print("   4. 觀察選擇結果")
    
    print("\n🎯 預期效果：")
    print("   • 📈 經典策略 → 勝率73.45%、破底反彈高量")
    print("   • 🌟 阿水策略系列 → 阿水一式、阿水二式")
    print("   • 🚀 進階策略 → 藏獒、CANSLIM等")
    print("   • ⚡ 5分鐘短線策略 → 6種5分鐘策略")
    
    # 運行應用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
