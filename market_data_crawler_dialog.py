#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球市場數據爬蟲對話框
獨立模組，避免主程式語法問題
"""

import sys
import time
import logging
import pandas as pd
from datetime import datetime
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTextEdit, QProgressBar, QApplication,
                            QMessageBox, QFileDialog)
from PyQt6.QtCore import Qt, QObject, pyqtSignal, QThread
from PyQt6.QtGui import QClipboard

class MarketDataCrawlerDialog(QDialog):
    """全球市場數據爬蟲對話框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setup_ui()
        self.crawler_worker = None
        
    def setup_ui(self):
        """設置界面"""
        self.setWindowTitle("🌍 全球市場數據爬蟲")
        self.setGeometry(100, 100, 1200, 800)  # 增大視窗尺寸

        # 設置視窗標誌，啟用最大化、最小化、關閉按鈕
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.WindowMinimizeButtonHint |
            Qt.WindowType.WindowMaximizeButtonHint |
            Qt.WindowType.WindowCloseButtonHint
        )

        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QPushButton {
                background-color: #0d7377;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #14a085;
            }
            QPushButton:pressed {
                background-color: #0a5d61;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #999;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555;
                border-radius: 5px;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
            QProgressBar {
                border: 1px solid #555;
                border-radius: 5px;
                text-align: center;
                background-color: #2d2d2d;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #0d7377;
                border-radius: 3px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # 減少邊距

        # 簡化標題（移除詳細說明）
        title_label = QLabel("🌍 全球市場數據爬蟲")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #14a085; margin: 5px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(25)
        layout.addWidget(self.progress_bar)

        # 兩欄結果顯示區域
        content_layout = QHBoxLayout()

        # 左欄
        left_column = QVBoxLayout()
        left_label = QLabel("📊 市場指數 & 期貨")
        left_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #14a085; margin: 5px;")
        left_column.addWidget(left_label)

        self.left_result_text = QTextEdit()
        self.left_result_text.setPlaceholderText("美股、亞股、中國股市、台股期貨數據...")
        left_column.addWidget(self.left_result_text)

        # 右欄
        right_column = QVBoxLayout()
        right_label = QLabel("💰 商品 & 外匯 & 加密貨幣")
        right_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #14a085; margin: 5px;")
        right_column.addWidget(right_label)

        self.right_result_text = QTextEdit()
        self.right_result_text.setPlaceholderText("商品價格、外匯匯率、加密貨幣數據...")
        right_column.addWidget(self.right_result_text)

        # 添加到水平布局
        content_layout.addLayout(left_column)
        content_layout.addLayout(right_column)
        layout.addLayout(content_layout)

        # 保留原始結果文本（隱藏，用於導出）
        self.result_text = QTextEdit()
        self.result_text.setVisible(False)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 開始爬取")
        self.start_button.clicked.connect(self.start_crawling)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ 停止")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        button_layout.addStretch()

        # 複製按鈕
        self.copy_button = QPushButton("📋 複製文字")
        self.copy_button.clicked.connect(self.copy_text)
        self.copy_button.setEnabled(False)
        button_layout.addWidget(self.copy_button)

        # 導出Excel按鈕
        self.export_button = QPushButton("📊 導出Excel")
        self.export_button.clicked.connect(self.export_to_excel)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.export_button)

        self.close_button = QPushButton("❌ 關閉")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)

        # 初始化爬蟲工作器和數據存儲
        self.crawler_worker = None
        self.crawled_data = {}  # 存儲爬取的數據
        self.crawl_time = None  # 存儲爬取時間

    def start_crawling(self):
        """開始爬取數據"""
        try:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.copy_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)

            # 清除兩欄顯示區域
            self.left_result_text.clear()
            self.right_result_text.clear()
            self.result_text.clear()

            # 記錄爬取開始時間
            self.crawl_time = datetime.now()
            start_info = f"🕐 爬取時間: {self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')}\n{'=' * 40}"
            self.left_result_text.append(start_info)
            self.result_text.append(start_info)
            
            # 創建爬蟲工作器（線程）
            self.crawler_worker = MarketCrawlerWorker()
            self.crawler_worker.progress_updated.connect(self.update_progress)
            self.crawler_worker.data_received.connect(self.display_data)
            self.crawler_worker.finished.connect(self.crawling_finished)
            self.crawler_worker.error_occurred.connect(self.handle_error)

            # 啟動爬蟲線程（避免UI卡頓）
            self.crawler_worker.start()
            
        except Exception as e:
            self.handle_error(f"啟動爬蟲失敗: {e}")
    
    def stop_crawling(self):
        """停止爬取"""
        if self.crawler_worker:
            self.crawler_worker.stop()
        self.crawling_finished()
    
    def update_progress(self, value, message):
        """更新進度（優化性能）"""
        self.progress_bar.setValue(value)

        # 只在重要進度點顯示訊息，避免過多更新造成卡頓
        if value in [10, 30, 60, 100] or "完成" in message:
            progress_msg = f"📊 {message}"
            self.left_result_text.insertHtml(f'<span style="color: #14a085;">{progress_msg}</span><br>')
            self.result_text.append(progress_msg)

        # 強制處理UI事件，保持響應性
        QApplication.processEvents()
        
    def display_data(self, category, data):
        """顯示數據到兩欄布局（支援漲跌顏色）"""
        # 存儲數據用於導出
        self.crawled_data[category] = data

        # 格式化數據文本（HTML格式支援顏色）
        formatted_html = f"<br><b>🔹 {category}:</b><br>"
        plain_text = f"\n🔹 {category}:\n"  # 用於導出

        for name, item_data in data.items():
            if isinstance(item_data, dict):
                # 處理不同類型的數據
                if 'contracts' in item_data:  # 期貨多空口數數據
                    contracts = item_data.get('contracts', 0)
                    change = item_data.get('change', 0)
                    change_pct = item_data.get('change_pct', 0)
                    status = item_data.get('status', '數據')

                    # 格式化變化顯示（HTML版本）
                    if change > 0:
                        change_html = f'<span style="color: #ff4444;">📈 +{change:,} (+{change_pct:.2f}%)</span>'
                        change_plain = f"📈 +{change:,} (+{change_pct:.2f}%)"
                    elif change < 0:
                        change_html = f'<span style="color: #44ff44;">📉 {change:,} ({change_pct:.2f}%)</span>'
                        change_plain = f"📉 {change:,} ({change_pct:.2f}%)"
                    else:
                        change_html = f'<span style="color: #cccccc;">➡️ {change:,} ({change_pct:.2f}%)</span>'
                        change_plain = f"➡️ {change:,} ({change_pct:.2f}%)"

                    formatted_html += f"&nbsp;&nbsp;• {name}: {contracts:,}口 {change_html}<br>"
                    plain_text += f"  • {name}: {contracts:,}口 {change_plain}\n"

                else:  # 一般價格數據
                    price = item_data.get('price', item_data.get('rate', 0))
                    change_pct = item_data.get('change_pct', 0)
                    status = item_data.get('status', '數據')

                    # 格式化顯示（HTML版本）
                    if change_pct > 0:
                        change_html = f'<span style="color: #ff4444;">📈 +{change_pct:.2f}%</span>'
                        change_plain = f"📈 +{change_pct:.2f}%"
                    elif change_pct < 0:
                        change_html = f'<span style="color: #44ff44;">📉 {change_pct:.2f}%</span>'
                        change_plain = f"📉 {change_pct:.2f}%"
                    else:
                        change_html = f'<span style="color: #cccccc;">➡️ {change_pct:.2f}%</span>'
                        change_plain = f"➡️ {change_pct:.2f}%"

                    formatted_html += f"&nbsp;&nbsp;• {name}: {price} {change_html}<br>"
                    plain_text += f"  • {name}: {price} {change_plain}\n"

        # 決定顯示在左欄還是右欄
        left_categories = ['美股指數', '亞洲指數', '中國股市指數', '台股數據', '台股期貨多空資料', '外資期貨多空單']
        right_categories = ['商品價格', '外匯匯率（含美元指數）', '加密貨幣']

        if category in left_categories:
            self.left_result_text.insertHtml(formatted_html)
        elif category in right_categories:
            self.right_result_text.insertHtml(formatted_html)
        else:
            # 預設顯示在左欄
            self.left_result_text.insertHtml(formatted_html)

        # 同時添加到隱藏的完整結果文本（用於導出，使用純文字）
        self.result_text.append(plain_text)
    
    def crawling_finished(self):
        """爬取完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        # 啟用複製和導出按鈕
        if self.crawled_data:
            self.copy_button.setEnabled(True)
            self.export_button.setEnabled(True)

        # 添加完成時間
        finish_time = datetime.now()
        elapsed = finish_time - self.crawl_time if self.crawl_time else None

        completion_info = f"\n{'=' * 40}\n✅ 數據爬取完成！\n🕐 完成時間: {finish_time.strftime('%Y-%m-%d %H:%M:%S')}"
        if elapsed:
            completion_info += f"\n⏱️ 耗時: {elapsed.total_seconds():.1f} 秒"

        self.right_result_text.append(completion_info)
        self.result_text.append(completion_info)
        
    def handle_error(self, error_msg):
        """處理錯誤"""
        error_info = f"\n❌ 錯誤: {error_msg}"
        self.right_result_text.append(error_info)
        self.result_text.append(error_info)
        self.crawling_finished()

    def copy_text(self):
        """複製文字到剪貼板"""
        try:
            # 合併兩欄內容
            left_content = self.left_result_text.toPlainText()
            right_content = self.right_result_text.toPlainText()

            combined_content = f"=== 市場指數 & 期貨 ===\n{left_content}\n\n=== 商品 & 外匯 & 加密貨幣 ===\n{right_content}"

            clipboard = QApplication.clipboard()
            clipboard.setText(combined_content)

            # 顯示成功訊息
            QMessageBox.information(
                self,
                "複製成功",
                "📋 文字內容已複製到剪貼板！\n\n包含兩欄的完整數據。"
            )
        except Exception as e:
            QMessageBox.warning(
                self,
                "複製失敗",
                f"❌ 複製失敗: {str(e)}"
            )

    def export_to_excel(self):
        """導出數據到Excel"""
        try:
            if not self.crawled_data:
                QMessageBox.warning(self, "無數據", "❌ 沒有可導出的數據！")
                return

            # 選擇保存位置
            default_filename = f"全球市場數據_{self.crawl_time.strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存Excel文件",
                default_filename,
                "Excel文件 (*.xlsx);;所有文件 (*)"
            )

            if not file_path:
                return

            # 創建Excel寫入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 創建總覽工作表
                overview_data = []
                for category, data in self.crawled_data.items():
                    for name, item_data in data.items():
                        if isinstance(item_data, dict):
                            # 處理不同類型的數據
                            if 'contracts' in item_data:  # 期貨多空口數數據
                                overview_data.append({
                                    '類別': category,
                                    '名稱': name,
                                    '口數': item_data.get('contracts', 0),
                                    '變化口數': item_data.get('change', 0),
                                    '變化%': item_data.get('change_pct', 0),
                                    '狀態': item_data.get('status', '數據'),
                                    '爬取時間': self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')
                                })
                            else:  # 一般價格數據
                                overview_data.append({
                                    '類別': category,
                                    '名稱': name,
                                    '價格/匯率': item_data.get('price', item_data.get('rate', 0)),
                                    '口數': '',
                                    '變化口數': '',
                                    '變化%': item_data.get('change_pct', 0),
                                    '狀態': item_data.get('status', '數據'),
                                    '爬取時間': self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')
                                })

                # 寫入總覽
                if overview_data:
                    overview_df = pd.DataFrame(overview_data)
                    overview_df.to_excel(writer, sheet_name='總覽', index=False)

                # 為每個類別創建單獨的工作表
                for category, data in self.crawled_data.items():
                    category_data = []
                    for name, item_data in data.items():
                        if isinstance(item_data, dict):
                            # 處理不同類型的數據
                            if 'contracts' in item_data:  # 期貨多空口數數據
                                category_data.append({
                                    '名稱': name,
                                    '口數': item_data.get('contracts', 0),
                                    '變化口數': item_data.get('change', 0),
                                    '變化%': item_data.get('change_pct', 0),
                                    '狀態': item_data.get('status', '數據'),
                                    '爬取時間': self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')
                                })
                            else:  # 一般價格數據
                                category_data.append({
                                    '名稱': name,
                                    '價格/匯率': item_data.get('price', item_data.get('rate', 0)),
                                    '變化%': item_data.get('change_pct', 0),
                                    '狀態': item_data.get('status', '數據'),
                                    '爬取時間': self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')
                                })

                    if category_data:
                        category_df = pd.DataFrame(category_data)
                        # 清理工作表名稱（Excel不允許某些字符）
                        sheet_name = category.replace('/', '_').replace('\\', '_')[:31]
                        category_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 顯示導出成功訊息並詢問是否打開Excel
            reply = QMessageBox.question(
                self,
                "導出成功",
                f"📊 數據已成功導出到Excel文件！\n\n"
                f"📁 文件位置: {file_path}\n"
                f"📋 包含 {len(self.crawled_data)} 個類別的數據，"
                f"總共 {sum(len(data) for data in self.crawled_data.values())} 項數據\n"
                f"🕐 爬取時間: {self.crawl_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"是否要立即打開Excel文件？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            # 如果用戶選擇打開Excel
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    import os
                    import subprocess
                    import platform

                    # 根據作業系統選擇打開方式
                    system = platform.system()
                    if system == "Windows":
                        os.startfile(file_path)
                    elif system == "Darwin":  # macOS
                        subprocess.run(["open", file_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", file_path])

                    # 顯示打開成功訊息
                    QMessageBox.information(
                        self,
                        "打開Excel",
                        f"✅ Excel文件已打開！\n\n"
                        f"如果Excel沒有自動打開，請手動打開:\n"
                        f"{file_path}"
                    )

                except Exception as e:
                    QMessageBox.warning(
                        self,
                        "打開失敗",
                        f"❌ 無法自動打開Excel文件: {str(e)}\n\n"
                        f"請手動打開文件:\n{file_path}\n\n"
                        f"💡 提示: 請確保已安裝Microsoft Excel或相容軟體"
                    )

        except Exception as e:
            QMessageBox.critical(
                self,
                "導出失敗",
                f"❌ 導出Excel失敗: {str(e)}\n\n"
                f"請確保:\n"
                f"• 有足夠的磁碟空間\n"
                f"• 目標文件沒有被其他程式打開\n"
                f"• 有寫入權限"
            )

class MarketCrawlerWorker(QThread):
    """市場爬蟲工作器（使用QThread避免UI卡頓）"""

    progress_updated = pyqtSignal(int, str)
    data_received = pyqtSignal(str, dict)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.is_running = False

    def run(self):
        """在獨立線程中執行爬取（避免UI卡頓）"""
        self.is_running = True

        try:
            # 使用安全掃描器
            from safe_market_scanner import SafeMarketScanner
            scanner = SafeMarketScanner()

            self.progress_updated.emit(10, "初始化安全掃描器...")

            # 執行掃描
            self.progress_updated.emit(30, "開始獲取全球市場數據...")
            results = scanner.run_full_scan()

            if results and self.is_running:
                self.progress_updated.emit(60, "處理數據結果...")

                # 發送各類別數據（分批發送避免UI卡頓）
                categories = [
                    ('us_indices', '美股指數'),
                    ('asia_indices', '亞洲指數'),
                    ('china_indices', '中國股市指數'),
                    ('taiwan_futures', '台股數據'),
                    ('taiwan_futures_positions', '台股期貨多空資料'),
                    ('foreign_futures_positions', '外資期貨多空單'),
                    ('commodities', '商品價格'),
                    ('fx_rates', '外匯匯率（含美元指數）'),
                    ('crypto', '加密貨幣')
                ]

                progress_step = 30 / len(categories)  # 分配剩餘進度
                current_progress = 60

                for key, display_name in categories:
                    if key in results and results[key] and self.is_running:
                        self.data_received.emit(display_name, results[key])
                        current_progress += progress_step
                        self.progress_updated.emit(int(current_progress), f"處理 {display_name}...")

                        # 短暫休眠避免UI過載
                        self.msleep(100)

                self.progress_updated.emit(100, "數據獲取完成")
            else:
                self.error_occurred.emit("無法獲取市場數據")

        except Exception as e:
            self.error_occurred.emit(str(e))
        finally:
            if self.is_running:
                self.finished.emit()

    def stop(self):
        """停止爬取"""
        self.is_running = False
        self.quit()  # 退出線程
        self.wait()  # 等待線程結束

# 測試函數
def test_dialog():
    """測試對話框"""
    app = QApplication(sys.argv)
    dialog = MarketDataCrawlerDialog()
    dialog.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    test_dialog()
