#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試主程式整合和即時股價監控功能
"""

import sys
import time
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_main_program_startup():
    """測試主程式啟動"""
    print("🧪 測試主程式啟動")
    print("=" * 50)
    
    try:
        # 創建應用程式
        app = QApplication(sys.argv)
        
        # 導入主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        print("✅ 主程式模組導入成功")
        
        # 創建主窗口
        main_window = StockScreenerGUI()
        print("✅ 主窗口創建成功")
        
        # 檢查即時監控方法是否存在
        if hasattr(main_window, 'open_realtime_stock_monitor'):
            print("✅ 即時股價監控方法存在")
        else:
            print("❌ 即時股價監控方法不存在")
            return False
        
        # 檢查其他必要方法
        required_methods = [
            'open_strategy_performance_analyzer',
            'download_stock_data'
        ]
        
        for method in required_methods:
            if hasattr(main_window, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        # 不顯示窗口，只測試創建
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 主程式測試失敗: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_realtime_monitor_integration():
    """測試即時監控整合"""
    print("\n📊 測試即時監控整合")
    print("=" * 50)
    
    try:
        app = QApplication(sys.argv)
        
        # 導入並創建主程式
        from O3mh_gui_v21_optimized import StockScreenerGUI
        main_window = StockScreenerGUI()
        
        # 測試即時監控方法調用
        print("🔍 測試即時監控方法調用...")
        
        # 模擬調用即時監控功能
        try:
            # 這裡我們不實際調用，因為會開啟窗口
            # 只檢查方法是否可調用
            method = getattr(main_window, 'open_realtime_stock_monitor')
            print("✅ 即時監控方法可調用")
            
            # 檢查即時監控模組是否可導入
            from real_time_stock_monitor import RealTimeStockMonitor
            print("✅ 即時監控模組可導入")
            
            # 檢查爬蟲模組是否可導入
            from core_web_crawler import YahooFinanceCrawler
            print("✅ 爬蟲模組可導入")
            
        except Exception as e:
            print(f"❌ 即時監控功能測試失敗: {e}")
            return False
        
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 即時監控整合測試失敗: {e}")
        return False

def test_menu_structure():
    """測試菜單結構"""
    print("\n📋 測試菜單結構")
    print("=" * 50)
    
    try:
        app = QApplication(sys.argv)
        
        from O3mh_gui_v21_optimized import StockScreenerGUI
        main_window = StockScreenerGUI()
        
        # 檢查菜單欄是否存在
        menubar = main_window.menuBar()
        if menubar:
            print("✅ 菜單欄存在")
            
            # 檢查工具菜單
            menus = menubar.findChildren(type(menubar.addMenu("")))
            menu_titles = [menu.title() for menu in menus]
            
            print(f"📋 找到菜單: {menu_titles}")
            
            if any("工具" in title for title in menu_titles):
                print("✅ 工具菜單存在")
            else:
                print("❌ 工具菜單不存在")
                
        else:
            print("❌ 菜單欄不存在")
            return False
        
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 菜單結構測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🔍 主程式整合測試")
    print("=" * 70)
    
    # 測試1: 主程式啟動
    startup_test = test_main_program_startup()
    
    # 測試2: 即時監控整合
    integration_test = test_realtime_monitor_integration()
    
    # 測試3: 菜單結構
    menu_test = test_menu_structure()
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 整合測試結果總結")
    print("=" * 30)
    
    print(f"主程式啟動測試: {'✅ 通過' if startup_test else '❌ 失敗'}")
    print(f"即時監控整合測試: {'✅ 通過' if integration_test else '❌ 失敗'}")
    print(f"菜單結構測試: {'✅ 通過' if menu_test else '❌ 失敗'}")
    
    all_passed = all([startup_test, integration_test, menu_test])
    
    if all_passed:
        print("\n🎉 所有整合測試通過！")
        print("\n✅ 主程式現在可以正常啟動")
        print("✅ 即時股價監控功能已完整整合")
        print("✅ 菜單結構正確")
        
        print("\n🚀 使用方法:")
        print("  1. 執行: python O3mh_gui_v21_optimized.py")
        print("  2. 點擊「工具」菜單")
        print("  3. 選擇「📊 即時股價監控」")
        print("  4. 享受即時股價監控功能！")
        
        print("\n💡 功能特色:")
        print("  • 四象限監控界面")
        print("  • Yahoo Finance即時數據")
        print("  • GoodInfo反爬蟲技術")
        print("  • 多線程高效處理")
        print("  • 完整設定系統")
        
    else:
        print("\n⚠️ 部分測試失敗，需要進一步調試")
        
        if not startup_test:
            print("💡 建議檢查主程式的方法定義")
        if not integration_test:
            print("💡 建議檢查即時監控模組導入")
        if not menu_test:
            print("💡 建議檢查菜單結構配置")

if __name__ == "__main__":
    main()
