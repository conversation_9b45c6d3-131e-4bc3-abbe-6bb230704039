#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的解析邏輯
"""

from mops_bulk_revenue_downloader import MopsBulkRevenueDownloader
import pandas as pd
import sqlite3

def test_fixed_parsing():
    """測試修正後的解析邏輯"""
    
    print("🔧 測試修正後的月營收解析邏輯")
    print("=" * 60)
    
    # 使用正確的資料庫路徑
    db_path = "D:/Finlab/history/tables/monthly_revenue.db"
    downloader = MopsBulkRevenueDownloader(db_path)
    
    # 測試單一市場下載
    print("📅 測試下載 2024年6月 上市公司數據...")
    
    try:
        # 直接測試解析邏輯
        revenue_data = downloader.download_market_revenue('sii', 2024, 6)
        
        print(f"✅ 成功解析 {len(revenue_data)} 筆數據")
        
        if revenue_data:
            # 顯示前3筆完整數據
            print("\n🔍 前3筆解析結果:")
            for i, data in enumerate(revenue_data[:3]):
                print(f"\n  📊 第{i+1}筆:")
                print(f"    股票代號: {data['stock_id']}")
                print(f"    股票名稱: {data['stock_name']}")
                print(f"    產業別: '{data['industry']}'")
                print(f"    營收: {data['revenue']:,} 千元")
                print(f"    月增率: {data['revenue_mom']}%")
                print(f"    年增率: {data['revenue_yoy']}%")
                print(f"    累計營收: {data['cumulative_revenue']:,} 千元")
                print(f"    累計年增率: {data['cumulative_revenue_yoy']}%")
                print(f"    備註: {data['remark']}")
                print(f"    市場: {data['market']}")
            
            # 統計分析
            print(f"\n📊 數據統計:")
            
            # 檢查有多少筆有完整數據
            complete_data = [d for d in revenue_data if d['revenue_mom'] is not None]
            print(f"  有月增率數據: {len(complete_data)} / {len(revenue_data)} 筆")
            
            complete_yoy = [d for d in revenue_data if d['revenue_yoy'] is not None]
            print(f"  有年增率數據: {len(complete_yoy)} / {len(revenue_data)} 筆")
            
            complete_cum = [d for d in revenue_data if d['cumulative_revenue'] > 0]
            print(f"  有累計營收數據: {len(complete_cum)} / {len(revenue_data)} 筆")
            
            complete_cum_yoy = [d for d in revenue_data if d['cumulative_revenue_yoy'] is not None]
            print(f"  有累計年增率數據: {len(complete_cum_yoy)} / {len(revenue_data)} 筆")
            
            # 保存到資料庫
            print(f"\n💾 保存到資料庫...")
            downloader.save_to_database(revenue_data)
            
            # 檢查資料庫內容
            print(f"\n🔍 檢查資料庫內容:")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT stock_id, stock_name, revenue, revenue_mom, revenue_yoy, 
                       cumulative_revenue, cumulative_revenue_yoy, remark
                FROM monthly_revenue 
                WHERE year = 2024 AND month = 6 AND market = '上市'
                ORDER BY revenue DESC 
                LIMIT 3
            """)
            
            db_results = cursor.fetchall()
            print(f"  資料庫中的前3筆:")
            for row in db_results:
                stock_id, stock_name, revenue, mom, yoy, cum_rev, cum_yoy, remark = row
                print(f"    {stock_id} {stock_name}:")
                print(f"      營收: {revenue:,} | 月增率: {mom}% | 年增率: {yoy}%")
                print(f"      累計營收: {cum_rev:,} | 累計年增率: {cum_yoy}%")
                print(f"      備註: {remark}")
                print()
            
            conn.close()
            
        else:
            print("❌ 沒有解析到任何數據")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_parsing()
