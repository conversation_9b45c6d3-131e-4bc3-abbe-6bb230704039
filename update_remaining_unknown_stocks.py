#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新剩餘未知股票名稱
處理ETF、權證等特殊商品的名稱
"""

import sqlite3
import os
from stock_name_mapping import get_cached_stock_info, get_stock_name

def update_remaining_unknown_stocks():
    """更新剩餘的未知股票名稱"""
    print("🔄 更新剩餘未知股票名稱...")
    print("=" * 50)
    
    price_db = 'D:/Finlab/history/tables/price.db'
    
    if not os.path.exists(price_db):
        print(f"❌ 找不到資料庫: {price_db}")
        return False
    
    try:
        # 獲取最新的股票資訊
        stock_info = get_cached_stock_info()
        print(f"📊 獲取到 {len(stock_info)} 檔股票資訊")
        
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        # 查詢需要更新的股票
        cursor.execute("""
        SELECT DISTINCT stock_id, stock_name 
        FROM stock_daily_data 
        WHERE stock_name LIKE 'ETF%' 
           OR stock_name LIKE '股票%' 
           OR stock_name IS NULL 
           OR stock_name = ''
        ORDER BY stock_id
        """)
        
        unknown_stocks = cursor.fetchall()
        print(f"📋 找到 {len(unknown_stocks)} 檔需要更新的股票")
        
        updated_count = 0
        
        for stock_id, current_name in unknown_stocks:
            # 使用改進的股票名稱獲取函數
            new_name = get_stock_name(stock_id)
            
            # 如果獲取到了更好的名稱，就更新
            if (new_name and 
                new_name != current_name and 
                not new_name.startswith('股票') and 
                not new_name.startswith('ETF')):
                
                try:
                    cursor.execute("""
                    UPDATE stock_daily_data 
                    SET stock_name = ? 
                    WHERE stock_id = ?
                    """, (new_name, stock_id))
                    
                    print(f"✅ {stock_id}: {current_name or '空值'} → {new_name}")
                    updated_count += 1
                    
                except Exception as e:
                    print(f"❌ 更新 {stock_id} 失敗: {e}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n📊 更新完成:")
        print(f"✅ 成功更新 {updated_count} 檔股票名稱")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        return False

def check_update_results():
    """檢查更新結果"""
    print("\n🔍 檢查更新結果...")
    print("=" * 50)
    
    price_db = 'D:/Finlab/history/tables/price.db'
    
    try:
        conn = sqlite3.connect(price_db)
        cursor = conn.cursor()
        
        # 統計各類型股票名稱
        cursor.execute("""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN stock_name LIKE 'ETF%' THEN 1 ELSE 0 END) as etf_default,
            SUM(CASE WHEN stock_name LIKE '股票%' THEN 1 ELSE 0 END) as stock_default,
            SUM(CASE WHEN stock_name IS NULL OR stock_name = '' THEN 1 ELSE 0 END) as null_names
        FROM (
            SELECT DISTINCT stock_id, stock_name 
            FROM stock_daily_data
        )
        """)
        
        result = cursor.fetchone()
        total, etf_default, stock_default, null_names = result
        
        correct_names = total - etf_default - stock_default - null_names
        unknown_rate = (etf_default + stock_default + null_names) / total * 100
        
        print(f"📊 更新後統計:")
        print(f"   總股票數: {total}")
        print(f"   ✅ 正確名稱: {correct_names} ({correct_names/total*100:.1f}%)")
        print(f"   ⚠️ ETF預設: {etf_default} ({etf_default/total*100:.1f}%)")
        print(f"   ⚠️ 股票預設: {stock_default} ({stock_default/total*100:.1f}%)")
        print(f"   ❌ 空值: {null_names} ({null_names/total*100:.1f}%)")
        print(f"   📈 未知比例: {unknown_rate:.1f}%")
        
        if unknown_rate < 15:
            print(f"   🎉 優秀！未知名稱比例已降至 {unknown_rate:.1f}%")
        elif unknown_rate < 25:
            print(f"   ✅ 良好！未知名稱比例 {unknown_rate:.1f}%")
        else:
            print(f"   ⚠️ 仍需改善！未知名稱比例 {unknown_rate:.1f}%")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 剩餘未知股票名稱更新工具")
    print("=" * 50)
    
    # 更新剩餘未知股票
    if update_remaining_unknown_stocks():
        # 檢查更新結果
        check_update_results()
        print("\n🎉 更新完成！")
    else:
        print("\n❌ 更新失敗！")
