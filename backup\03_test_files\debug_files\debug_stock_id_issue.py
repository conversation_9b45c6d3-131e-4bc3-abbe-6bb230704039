#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試股票代碼傳遞問題
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 設置詳細日誌
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加strategies目錄到路徑
sys.path.append('strategies')

def create_test_price_data(stock_id, days=100):
    """創建測試用股價數據"""
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 模擬股價數據
    np.random.seed(42)
    base_price = 50
    prices = []
    current_price = base_price
    
    for i in range(days):
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        prices.append(current_price)
    
    # 創建OHLCV數據
    df = pd.DataFrame({
        'Date': dates,
        'Open': [p * np.random.uniform(0.99, 1.01) for p in prices],
        'High': [p * np.random.uniform(1.00, 1.05) for p in prices],
        'Low': [p * np.random.uniform(0.95, 1.00) for p in prices],
        'Close': prices,
        'Volume': [np.random.randint(100000, 1000000) for _ in range(days)]
    })
    
    df.set_index('Date', inplace=True)
    return df

def debug_stock_id_passing():
    """調試股票代碼傳遞問題"""
    print("🔍 調試股票代碼傳遞問題")
    print("=" * 60)
    
    try:
        from high_yield_turtle_strategy import HighYieldTurtleStrategy
        
        # 創建策略實例
        strategy = HighYieldTurtleStrategy()
        
        # 測試不同的參數傳遞方式
        test_stock = '1108'
        test_df = create_test_price_data(test_stock)
        
        print(f"📊 測試股票: {test_stock}")
        print(f"📊 數據形狀: {test_df.shape}")
        
        # 測試1: 正確傳遞stock_id
        print(f"\n🧪 測試1: 正確傳遞stock_id")
        result1 = strategy.analyze_stock(test_df, stock_id=test_stock)
        print(f"  結果: {result1['suitable']}")
        print(f"  原因: {result1['reason'][:100]}...")
        
        # 測試2: 不傳遞stock_id
        print(f"\n🧪 測試2: 不傳遞stock_id")
        result2 = strategy.analyze_stock(test_df)
        print(f"  結果: {result2['suitable']}")
        print(f"  原因: {result2['reason'][:100]}...")
        
        # 測試3: 傳遞None作為stock_id
        print(f"\n🧪 測試3: 傳遞None作為stock_id")
        result3 = strategy.analyze_stock(test_df, stock_id=None)
        print(f"  結果: {result3['suitable']}")
        print(f"  原因: {result3['reason'][:100]}...")
        
        # 測試4: 檢查PKL數據獲取
        print(f"\n🧪 測試4: 檢查PKL數據獲取")
        pkl_data = strategy.get_stock_pkl_data(test_stock)
        if pkl_data:
            print(f"  ✅ PKL數據獲取成功: {pkl_data['stock_name']}")
            print(f"  殖利率: {pkl_data['dividend_yield']:.1f}%")
        else:
            print(f"  ❌ PKL數據獲取失敗")
        
        # 測試5: 檢查PKL數據快取狀態
        print(f"\n🧪 測試5: 檢查PKL數據快取狀態")
        print(f"  PKL快取存在: {strategy.pe_data_cache is not None}")
        if strategy.pe_data_cache is not None:
            print(f"  PKL快取大小: {strategy.pe_data_cache.shape}")
            print(f"  包含測試股票: {test_stock in strategy.pe_data_cache['股票代碼'].values}")
            
            # 檢查測試股票的實際數據
            test_data = strategy.pe_data_cache[strategy.pe_data_cache['股票代碼'] == test_stock]
            if not test_data.empty:
                print(f"  測試股票數據:")
                print(f"    股票名稱: {test_data.iloc[0]['名稱']}")
                print(f"    殖利率: {test_data.iloc[0]['殖利率(%)']}%")
                print(f"    PE比: {test_data.iloc[0]['本益比']}")
            else:
                print(f"  ❌ 測試股票不在PKL數據中")
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_main_program_call():
    """模擬主程序的完整調用流程"""
    print(f"\n🔧 模擬主程序的完整調用流程")
    print("=" * 60)
    
    class MockMainProgram:
        def __init__(self):
            self._current_processing_stock_id = None
            self._turtle_strategy_instance = None
        
        def check_condition(self, df, condition):
            """模擬check_condition方法"""
            condition_type = condition
            
            if condition_type == "high_yield_turtle_strategy":
                # 模擬主程序的邏輯
                stock_id = None
                if hasattr(self, '_current_processing_stock_id'):
                    stock_id = self._current_processing_stock_id
                    print(f"  📋 從_current_processing_stock_id獲取: {stock_id}")
                else:
                    print(f"  ❌ _current_processing_stock_id屬性不存在")
                
                return self.check_high_yield_turtle_strategy(df, stock_id=stock_id)
            
            return True, "其他條件"
        
        def check_high_yield_turtle_strategy(self, df, stock_id=None):
            """模擬主程序的策略調用方法"""
            print(f"  🎯 調用check_high_yield_turtle_strategy, stock_id={stock_id}")
            
            try:
                if len(df) < 60:
                    return False, "數據不足"

                # 使用單例模式
                if not hasattr(self, '_turtle_strategy_instance') or self._turtle_strategy_instance is None:
                    from high_yield_turtle_strategy import HighYieldTurtleStrategy
                    self._turtle_strategy_instance = HighYieldTurtleStrategy()

                turtle_strategy = self._turtle_strategy_instance
                finmind_data = {}

                # 執行分析
                result = turtle_strategy.analyze_stock(df, finmind_data=finmind_data, stock_id=stock_id)

                if result['suitable']:
                    return True, result['reason']
                else:
                    return False, result['reason']

            except Exception as e:
                print(f"  ❌ 策略調用失敗: {e}")
                return False, f"策略錯誤: {str(e)}"
        
        def process_stock(self, stock_id, df):
            """模擬處理單支股票的完整流程"""
            print(f"🔄 處理股票: {stock_id}")
            
            # 設置當前處理的股票ID
            self._current_processing_stock_id = stock_id
            print(f"  📝 設置_current_processing_stock_id = {stock_id}")
            
            # 模擬策略條件檢查
            conditions = ["high_yield_turtle_strategy"]
            
            for condition in conditions:
                matched, message = self.check_condition(df, condition)
                print(f"  📊 條件結果: {matched}")
                print(f"  📝 訊息: {message[:100]}...")
                
                return matched, message
    
    # 測試模擬主程序
    mock_main = MockMainProgram()
    
    test_stocks = ['1108', '2330']
    
    for stock_id in test_stocks:
        test_df = create_test_price_data(stock_id)
        
        try:
            result, message = mock_main.process_stock(stock_id, test_df)
            status = "✅ 符合" if result else "❌ 不符合"
            print(f"{status} {stock_id}: {message[:80]}...")
            print()
            
        except Exception as e:
            print(f"❌ {stock_id}: 處理失敗 - {e}")
            print()

def main():
    """主函數"""
    print("🚀 股票代碼傳遞問題調試")
    print("=" * 80)
    
    # 調試股票代碼傳遞
    debug_result = debug_stock_id_passing()
    
    # 模擬主程序調用
    simulate_main_program_call()
    
    print("\n" + "=" * 80)
    print("📊 調試總結")
    print("=" * 80)
    
    if debug_result:
        print("✅ 基本功能正常")
        print("💡 問題可能在於主程序的股票代碼設置時機")
        print("🔧 建議檢查主程序中_current_processing_stock_id的設置")
    else:
        print("❌ 基本功能有問題，需要進一步調試")

if __name__ == "__main__":
    main()
