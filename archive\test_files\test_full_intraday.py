#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試全測日內策略功能
"""

import sys
import os
import datetime
import pandas as pd
import numpy as np

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from O3mh_gui_v21_optimized import IntradayOpeningRangeStrategy

def generate_diverse_stock_data(stock_id, scenario="mixed"):
    """生成多樣化的測試股票數據"""
    np.random.seed(hash(stock_id) % 1000)  # 根據股票代碼生成不同的隨機種子
    
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    
    # 根據股票代碼特性生成不同類型的數據
    stock_num = int(stock_id) if stock_id.isdigit() else hash(stock_id) % 10000
    
    if stock_num % 4 == 0:  # 25% 上漲股
        scenario = "bullish"
    elif stock_num % 4 == 1:  # 25% 下跌股
        scenario = "bearish"
    elif stock_num % 4 == 2:  # 25% 震盪股
        scenario = "consolidation"
    else:  # 25% 混合型
        scenario = "mixed"
    
    if scenario == "bullish":
        # 上漲趨勢
        base_price = 30 + (stock_num % 100)
        trend = np.linspace(0, base_price * 0.3, 30)
        noise = np.random.normal(0, base_price * 0.02, 30)
        closes = base_price + trend + noise
        
        opens = closes * (0.98 + np.random.random(30) * 0.04)
        highs = closes * (1.01 + np.random.random(30) * 0.04)
        lows = closes * (0.97 - np.random.random(30) * 0.03)
        volumes = 50000 + np.random.randint(0, 150000, 30)
        
    elif scenario == "bearish":
        # 下跌趨勢
        base_price = 40 + (stock_num % 80)
        trend = np.linspace(0, -base_price * 0.25, 30)
        noise = np.random.normal(0, base_price * 0.015, 30)
        closes = base_price + trend + noise
        closes = np.maximum(closes, base_price * 0.5)  # 不要跌太多
        
        opens = closes * (1.01 - np.random.random(30) * 0.02)
        highs = closes * (1.02 - np.random.random(30) * 0.01)
        lows = closes * (0.95 - np.random.random(30) * 0.04)
        volumes = 30000 + np.random.randint(0, 80000, 30)
        
    elif scenario == "consolidation":
        # 震盪整理
        base_price = 25 + (stock_num % 60)
        noise = np.random.normal(0, base_price * 0.01, 30)
        closes = base_price + noise
        
        opens = closes * (0.995 + np.random.random(30) * 0.01)
        highs = closes * (1.005 + np.random.random(30) * 0.02)
        lows = closes * (0.985 - np.random.random(30) * 0.02)
        volumes = 40000 + np.random.randint(0, 60000, 30)
        
    else:  # mixed
        # 混合型：前期下跌，後期反彈
        base_price = 35 + (stock_num % 70)
        trend1 = np.linspace(0, -base_price * 0.15, 15)  # 前15天下跌
        trend2 = np.linspace(-base_price * 0.15, base_price * 0.1, 15)  # 後15天反彈
        trend = np.concatenate([trend1, trend2])
        noise = np.random.normal(0, base_price * 0.02, 30)
        closes = base_price + trend + noise
        
        opens = closes * (0.99 + np.random.random(30) * 0.02)
        highs = closes * (1.01 + np.random.random(30) * 0.03)
        lows = closes * (0.97 - np.random.random(30) * 0.02)
        volumes = 45000 + np.random.randint(0, 100000, 30)
    
    # 確保價格合理
    closes = np.maximum(closes, 5.0)  # 最低5元
    opens = np.maximum(opens, 5.0)
    highs = np.maximum(highs, np.maximum(opens, closes))
    lows = np.minimum(lows, np.minimum(opens, closes))
    lows = np.maximum(lows, 3.0)  # 最低3元
    
    df = pd.DataFrame({
        'Date': dates,
        'Open': opens,
        'High': highs,
        'Low': lows,
        'Close': closes,
        'Volume': volumes.astype(int)
    })
    
    return df, scenario

def test_full_intraday_strategy():
    """測試全測日內策略功能"""
    print("🚀 測試全測日內策略功能")
    print("=" * 60)
    
    strategy = IntradayOpeningRangeStrategy()
    
    # 模擬更大的股票池
    test_stocks = [
        '2330', '2317', '2454', '1301', '2303', '2382', '2412', '2881', '2886', '3008',
        '2002', '1216', '1101', '2207', '2308', '2327', '2357', '2395', '2408', '2409',
        '2474', '2603', '2609', '2615', '2633', '2801', '2880', '2882', '2883', '2884',
        '2885', '2887', '2890', '2891', '2892', '2912', '3034', '3037', '3045', '3711',
        '4904', '4938', '5871', '5876', '5880', '6505', '6669', '8454', '8996', '9910'
    ]
    
    print(f"📊 測試 {len(test_stocks)} 支股票")
    print("-" * 60)
    
    results = []
    scenario_stats = {'bullish': 0, 'bearish': 0, 'consolidation': 0, 'mixed': 0}
    
    for i, stock_id in enumerate(test_stocks):
        print(f"[{i+1:2d}/{len(test_stocks)}] 測試 {stock_id}...", end=" ")
        
        try:
            # 生成測試數據
            test_data, scenario = generate_diverse_stock_data(stock_id)
            scenario_stats[scenario] += 1
            
            # 計算指標
            test_data = strategy.calculate_indicators(test_data)
            
            # 檢查盤前條件
            pre_market_ok, reason = strategy.check_pre_market_conditions(test_data)
            
            if pre_market_ok:
                # 模擬交易信號
                result = strategy.simulate_breakout_trading(stock_id, datetime.datetime.now())
                
                if result and result.get('result') == 'buy_signal':
                    signal_details = result.get('signal_details', {})
                    confidence = signal_details.get('confidence', 0)
                    
                    results.append({
                        'stock_id': stock_id,
                        'scenario': scenario,
                        'confidence': confidence,
                        'signal_type': 'BUY',
                        'reason': result.get('reason', ''),
                        'pre_market_reason': reason
                    })
                    print(f"✅ 買入信號 (信心度{confidence}%)")
                    
                elif result and result.get('result') == 'wait_signal':
                    results.append({
                        'stock_id': stock_id,
                        'scenario': scenario,
                        'signal_type': 'WAIT',
                        'reason': result.get('reason', ''),
                        'pre_market_reason': reason
                    })
                    print(f"⏳ 等待信號")
                else:
                    print(f"📊 通過篩選但無信號")
            else:
                print(f"❌ 未通過篩選")
                
        except Exception as e:
            print(f"⚠️ 錯誤: {str(e)[:30]}...")
    
    # 分析結果
    print("\n" + "=" * 60)
    print("📈 全測結果分析")
    print("=" * 60)
    
    buy_signals = [r for r in results if r.get('signal_type') == 'BUY']
    wait_signals = [r for r in results if r.get('signal_type') == 'WAIT']
    
    print(f"總測試股票: {len(test_stocks)}")
    print(f"產生買入信號: {len(buy_signals)} ({len(buy_signals)/len(test_stocks)*100:.1f}%)")
    print(f"產生等待信號: {len(wait_signals)} ({len(wait_signals)/len(test_stocks)*100:.1f}%)")
    print(f"總有效信號: {len(results)} ({len(results)/len(test_stocks)*100:.1f}%)")
    
    print(f"\n📊 市場情境分布:")
    for scenario, count in scenario_stats.items():
        print(f"• {scenario}: {count} 支 ({count/len(test_stocks)*100:.1f}%)")
    
    if buy_signals:
        print(f"\n🎯 買入信號詳情 (按信心度排序):")
        buy_signals.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        for i, signal in enumerate(buy_signals[:10]):  # 顯示前10個
            confidence = signal.get('confidence', 0)
            scenario = signal.get('scenario', 'unknown')
            print(f"{i+1:2d}. {signal['stock_id']}: 信心度{confidence}% ({scenario})")
            print(f"    {signal.get('reason', '')}")
        
        if len(buy_signals) > 10:
            print(f"    ... 還有 {len(buy_signals) - 10} 支")
        
        # 統計信心度分布
        high_confidence = len([s for s in buy_signals if s.get('confidence', 0) >= 60])
        medium_confidence = len([s for s in buy_signals if 40 <= s.get('confidence', 0) < 60])
        low_confidence = len([s for s in buy_signals if s.get('confidence', 0) < 40])
        
        print(f"\n📊 信心度分布:")
        print(f"• 高信心度 (≥60%): {high_confidence} 支")
        print(f"• 中信心度 (40-59%): {medium_confidence} 支")
        print(f"• 低信心度 (<40%): {low_confidence} 支")
        
        avg_confidence = sum(s.get('confidence', 0) for s in buy_signals) / len(buy_signals)
        print(f"• 平均信心度: {avg_confidence:.1f}%")
    
    print(f"\n💡 結論:")
    if len(buy_signals) >= 5:
        print(f"✅ 策略表現良好，產生 {len(buy_signals)} 個買入信號")
    elif len(buy_signals) >= 2:
        print(f"⚠️ 策略表現中等，產生 {len(buy_signals)} 個買入信號")
    else:
        print(f"❌ 策略需要進一步優化，僅產生 {len(buy_signals)} 個買入信號")
    
    if len(results) / len(test_stocks) > 0.3:
        print(f"✅ 篩選效率良好，{len(results)/len(test_stocks)*100:.1f}% 股票通過篩選")
    else:
        print(f"⚠️ 篩選可能過於嚴格，僅 {len(results)/len(test_stocks)*100:.1f}% 股票通過篩選")

if __name__ == "__main__":
    test_full_intraday_strategy()
