#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import os
import pandas as pd
import pickle
from datetime import datetime, timedelta

class TestGUI:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        self.test_data_loading()
    
    def setup_ui(self):
        """設置界面"""
        self.root.title("測試GUI顯示")
        self.root.geometry("1200x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # 標題
        title = ttk.Label(main_frame, text="測試GUI顯示", font=('Arial', 16, 'bold'))
        title.grid(row=0, column=0, pady=(0, 20))
        
        # 創建表格
        self.create_table(main_frame)
        
        # 日誌區域
        log_frame = ttk.LabelFrame(main_frame, text="日誌", padding="5")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
    
    def create_table(self, parent):
        """創建表格"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 創建表格標題
        headers = ['檔案路徑', '資料範圍', '狀態']
        for i, header in enumerate(headers):
            label = ttk.Label(table_frame, text=header, font=('Arial', 10, 'bold'),
                            relief='solid', borderwidth=1, background='lightgray')
            label.grid(row=0, column=i, sticky=(tk.W, tk.E), padx=1, pady=1)
        
        # 設置列權重
        table_frame.columnconfigure(0, weight=3)  # 檔案路徑
        table_frame.columnconfigure(1, weight=2)  # 資料範圍
        table_frame.columnconfigure(2, weight=1)  # 狀態
        
        self.table_frame = table_frame
        parent.columnconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日誌訊息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def test_data_loading(self):
        """測試資料載入"""
        self.log_message("開始測試資料載入...")
        
        data_files = {
            'price.pkl': '股價資料',
            'pe.pkl': '本益比',
            'balance_sheet.pkl': '資產負債表'
        }
        
        row = 1
        for filename, display_name in data_files.items():
            filepath = os.path.join(r'D:\Finlab\history\tables', filename)
            self.log_message(f"檢查檔案: {filepath}")
            
            if os.path.exists(filepath):
                try:
                    # 讀取pkl檔案
                    with open(filepath, 'rb') as f:
                        data = pickle.load(f)
                    
                    self.log_message(f"成功讀取 {filename}")
                    self.log_message(f"資料形狀: {data.shape if hasattr(data, 'shape') else 'N/A'}")
                    self.log_message(f"索引類型: {type(data.index) if hasattr(data, 'index') else 'N/A'}")
                    
                    # 獲取日期範圍
                    date_range = "無法解析"
                    if hasattr(data, 'index') and len(data.index) > 0:
                        if hasattr(data.index, 'levels') and len(data.index.levels) > 1:
                            # MultiIndex
                            dates = data.index.get_level_values(1)  # 假設第二層是日期
                            if len(dates) > 0:
                                first_date = dates.min()
                                last_date = dates.max()
                                try:
                                    if hasattr(first_date, 'strftime'):
                                        first_str = first_date.strftime('%Y-%m-%d')
                                        last_str = last_date.strftime('%Y-%m-%d')
                                        date_range = f"{first_str} 至 {last_str}"
                                    else:
                                        date_range = f"{first_date} 至 {last_date}"
                                except:
                                    date_range = f"{first_date} 至 {last_date}"
                        else:
                            # 單層索引
                            if hasattr(data.index, 'dtype') and 'datetime' in str(data.index.dtype):
                                first_date = data.index.min()
                                last_date = data.index.max()
                                date_range = f"{first_date.strftime('%Y-%m-%d')} 至 {last_date.strftime('%Y-%m-%d')}"
                    
                    status = "✓ 正常"
                    
                except Exception as e:
                    self.log_message(f"讀取 {filename} 時發生錯誤: {str(e)}")
                    date_range = f"錯誤: {str(e)}"
                    status = "✗ 錯誤"
            else:
                self.log_message(f"檔案不存在: {filepath}")
                date_range = "檔案不存在"
                status = "✗ 不存在"
            
            # 創建表格行
            file_label = ttk.Label(self.table_frame, text=filepath,
                                 relief='solid', borderwidth=1, padding=5)
            file_label.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=1, pady=1)
            
            date_label = ttk.Label(self.table_frame, text=date_range,
                                 relief='solid', borderwidth=1, padding=5)
            date_label.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=1, pady=1)
            
            status_label = ttk.Label(self.table_frame, text=status,
                                   relief='solid', borderwidth=1, padding=5)
            status_label.grid(row=row, column=2, sticky=(tk.W, tk.E), padx=1, pady=1)
            
            row += 1
        
        self.log_message("資料載入測試完成")

def main():
    root = tk.Tk()
    app = TestGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
