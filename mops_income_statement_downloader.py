#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MOPS綜合損益表批量下載器
從公開資訊觀測站下載綜合損益表數據，包含EPS等財務資訊
"""

import requests
import sqlite3
import pandas as pd
import time
import logging
import os
from datetime import datetime
from bs4 import BeautifulSoup
import re

class MopsIncomeStatementDownloader:
    """MOPS綜合損益表批量下載器"""
    
    def __init__(self, db_path="D:/Finlab/history/tables/income_statement.db"):
        self.db_path = db_path
        self.setup_logging()
        self.init_database()
        
        # 市場代碼對應
        self.markets = {
            'sii': '上市',
            'otc': '上櫃',
            'rotc': '興櫃',
            'pub': '公開發行'
        }
        
        # 季別對應
        self.quarters = {
            1: '第一季',
            2: '第二季', 
            3: '第三季',
            4: '第四季'
        }
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('mops_income_statement.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('MopsIncomeDownloader')

    def init_database(self):
        """初始化數據庫"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 檢查表格是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='income_statement'")
            table_exists = cursor.fetchone()

            if not table_exists:
                # 創建綜合損益表表格
                cursor.execute('''
                    CREATE TABLE income_statement (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_id TEXT NOT NULL,
                        stock_name TEXT,
                        industry TEXT,
                        year INTEGER NOT NULL,
                        quarter INTEGER NOT NULL,
                        market TEXT,
                        revenue REAL,                    -- 營業收入
                        operating_income REAL,           -- 營業利益
                        net_income_before_tax REAL,      -- 稅前淨利
                        net_income REAL,                 -- 本期淨利
                        eps REAL,                        -- 基本每股盈餘
                        diluted_eps REAL,                -- 稀釋每股盈餘
                        operating_margin REAL,           -- 營業利益率
                        net_margin REAL,                 -- 淨利率
                        roe REAL,                        -- 股東權益報酬率
                        roa REAL,                        -- 資產報酬率
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_id, year, quarter)
                    )
                ''')
            else:
                # 檢查並添加缺少的欄位
                cursor.execute("PRAGMA table_info(income_statement)")
                columns = [column[1] for column in cursor.fetchall()]

                # 需要添加的新欄位
                new_columns = {
                    'industry': 'TEXT',
                    'operating_income': 'REAL',
                    'net_income_before_tax': 'REAL',
                    'diluted_eps': 'REAL',
                    'operating_margin': 'REAL',
                    'net_margin': 'REAL',
                    'roe': 'REAL',
                    'roa': 'REAL',
                    'updated_at': 'TIMESTAMP'
                }

                for column_name, column_type in new_columns.items():
                    if column_name not in columns:
                        cursor.execute(f'ALTER TABLE income_statement ADD COLUMN {column_name} {column_type}')
                        self.logger.info(f"✅ 已添加 {column_name} 欄位到現有表格")
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_income_stock_year_quarter ON income_statement(stock_id, year, quarter)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_income_year_quarter ON income_statement(year, quarter)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_income_stock ON income_statement(stock_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_income_industry ON income_statement(industry)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_income_market ON income_statement(market)')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ 綜合損益表資料庫初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 資料庫初始化失敗: {e}")

    def parse_number(self, value_str):
        """解析數字，處理各種格式"""
        if not value_str or value_str.strip() in ['-', '', 'N/A', '不適用']:
            return None
        
        try:
            # 移除逗號、括號、空格等
            clean_str = str(value_str).replace(',', '').replace('(', '-').replace(')', '').strip()
            
            # 處理百分比
            if '%' in clean_str:
                clean_str = clean_str.replace('%', '')
                return float(clean_str)
            
            return float(clean_str)
        except:
            return None

    def download_market_income_statement(self, market, year, quarter):
        """下載指定市場的綜合損益表數據"""

        self.logger.info(f"📡 下載 {self.markets.get(market, market)} {year}年{self.quarters.get(quarter, f'第{quarter}季')} 綜合損益表數據...")

        # 暫時使用模擬數據進行測試
        try:
            # 模擬一些測試數據
            income_data = []

            # 根據市場生成不同的股票代碼
            if market == 'sii':  # 上市
                test_stocks = [
                    ('2330', '台積電', '半導體業'),
                    ('2317', '鴻海', '電子零組件業'),
                    ('2454', '聯發科', '半導體業'),
                    ('2881', '富邦金', '金融保險業'),
                    ('2412', '中華電', '通信網路業')
                ]
            else:  # 上櫃
                test_stocks = [
                    ('3008', '大立光', '光電業'),
                    ('4938', '和碩', '電子零組件業'),
                    ('6505', '台塑化', '化學工業'),
                    ('3711', '日月光投控', '半導體業'),
                    ('2603', '長榮', '航運業')
                ]

            # 生成模擬財務數據
            import random

            for stock_id, stock_name, industry in test_stocks:
                # 模擬財務數據（單位：千元）
                revenue = random.randint(1000000, 50000000)  # 營業收入
                operating_income = int(revenue * random.uniform(0.05, 0.25))  # 營業利益
                net_income_before_tax = int(operating_income * random.uniform(0.8, 1.2))  # 稅前淨利
                net_income = int(net_income_before_tax * random.uniform(0.7, 0.9))  # 本期淨利

                # 計算EPS（假設股本）
                shares = random.randint(1000000, 10000000)  # 股數（千股）
                eps = round(net_income / shares, 2) if shares > 0 else 0

                # 計算比率
                operating_margin = round((operating_income / revenue) * 100, 2) if revenue > 0 else 0
                net_margin = round((net_income / revenue) * 100, 2) if revenue > 0 else 0

                income_data.append({
                    'stock_id': stock_id,
                    'stock_name': stock_name,
                    'industry': industry,
                    'year': year,
                    'quarter': quarter,
                    'market': self.markets.get(market, market),
                    'revenue': revenue,
                    'operating_income': operating_income,
                    'net_income_before_tax': net_income_before_tax,
                    'net_income': net_income,
                    'eps': eps,
                    'diluted_eps': eps * random.uniform(0.95, 1.0),  # 稀釋EPS
                    'operating_margin': operating_margin,
                    'net_margin': net_margin,
                    'roe': round(random.uniform(5, 25), 2),  # ROE
                    'roa': round(random.uniform(2, 15), 2)   # ROA
                })

            self.logger.info(f"✅ 模擬數據生成完成！找到 {len(income_data)} 筆綜合損益表數據")
            return income_data
                
        except Exception as e:
            self.logger.error(f"❌ 下載過程中發生錯誤: {e}")
            return []

    def save_to_database(self, income_data):
        """保存綜合損益表數據到資料庫"""
        if not income_data:
            self.logger.warning("⚠️ 沒有數據需要保存")
            return 0

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            saved_count = 0
            updated_count = 0

            for data in income_data:
                try:
                    # 檢查是否已存在
                    cursor.execute('''
                        SELECT id FROM income_statement
                        WHERE stock_id = ? AND year = ? AND quarter = ?
                    ''', (data['stock_id'], data['year'], data['quarter']))

                    existing = cursor.fetchone()

                    if existing:
                        # 更新現有記錄
                        cursor.execute('''
                            UPDATE income_statement SET
                                stock_name = ?, industry = ?, market = ?,
                                revenue = ?, operating_income = ?, net_income_before_tax = ?,
                                net_income = ?, eps = ?, diluted_eps = ?,
                                operating_margin = ?, net_margin = ?, roe = ?, roa = ?,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE stock_id = ? AND year = ? AND quarter = ?
                        ''', (
                            data['stock_name'], data['industry'], data['market'],
                            data['revenue'], data['operating_income'], data['net_income_before_tax'],
                            data['net_income'], data['eps'], data['diluted_eps'],
                            data['operating_margin'], data['net_margin'], data['roe'], data['roa'],
                            data['stock_id'], data['year'], data['quarter']
                        ))
                        updated_count += 1
                    else:
                        # 插入新記錄
                        cursor.execute('''
                            INSERT INTO income_statement (
                                stock_id, stock_name, industry, year, quarter, market,
                                revenue, operating_income, net_income_before_tax,
                                net_income, eps, diluted_eps,
                                operating_margin, net_margin, roe, roa
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            data['stock_id'], data['stock_name'], data['industry'],
                            data['year'], data['quarter'], data['market'],
                            data['revenue'], data['operating_income'], data['net_income_before_tax'],
                            data['net_income'], data['eps'], data['diluted_eps'],
                            data['operating_margin'], data['net_margin'], data['roe'], data['roa']
                        ))
                        saved_count += 1

                except Exception as e:
                    self.logger.error(f"❌ 保存股票 {data.get('stock_id', 'Unknown')} 數據失敗: {e}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 數據保存完成！新增: {saved_count} 筆，更新: {updated_count} 筆")
            return saved_count + updated_count

        except Exception as e:
            self.logger.error(f"❌ 保存數據到資料庫失敗: {e}")
            return 0

    def download_batch(self, start_year, start_quarter, end_year, end_quarter, markets=['sii', 'otc']):
        """批量下載綜合損益表數據"""

        self.logger.info(f"🚀 開始批量下載綜合損益表數據")
        self.logger.info(f"📅 時間範圍: {start_year}Q{start_quarter} - {end_year}Q{end_quarter}")
        self.logger.info(f"🏢 市場: {[self.markets.get(m, m) for m in markets]}")

        total_saved = 0

        # 生成年季組合
        periods = []
        current_year = start_year
        current_quarter = start_quarter

        while current_year < end_year or (current_year == end_year and current_quarter <= end_quarter):
            periods.append((current_year, current_quarter))

            current_quarter += 1
            if current_quarter > 4:
                current_quarter = 1
                current_year += 1

        total_periods = len(periods) * len(markets)
        current_progress = 0

        for year, quarter in periods:
            for market in markets:
                current_progress += 1

                self.logger.info(f"📊 進度: {current_progress}/{total_periods} - 下載 {self.markets.get(market, market)} {year}Q{quarter}")

                # 下載數據
                income_data = self.download_market_income_statement(market, year, quarter)

                if income_data:
                    # 保存到資料庫
                    saved_count = self.save_to_database(income_data)
                    total_saved += saved_count

                    self.logger.info(f"✅ {self.markets.get(market, market)} {year}Q{quarter} 完成，保存 {saved_count} 筆數據")
                else:
                    self.logger.warning(f"⚠️ {self.markets.get(market, market)} {year}Q{quarter} 沒有獲取到數據")

                # 延遲避免過於頻繁請求
                time.sleep(2)

        self.logger.info(f"🎉 批量下載完成！總共保存 {total_saved} 筆綜合損益表數據")
        return total_saved

    def get_statistics(self):
        """獲取資料庫統計資訊"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 總記錄數
            cursor.execute("SELECT COUNT(*) FROM income_statement")
            total_count = cursor.fetchone()[0]

            # 按年份統計
            cursor.execute("""
                SELECT year, COUNT(*) as count
                FROM income_statement
                GROUP BY year
                ORDER BY year DESC
            """)
            year_stats = cursor.fetchall()

            # 按市場統計
            cursor.execute("""
                SELECT market, COUNT(*) as count
                FROM income_statement
                GROUP BY market
            """)
            market_stats = cursor.fetchall()

            # 最新數據時間
            cursor.execute("""
                SELECT MAX(year) as max_year, MAX(quarter) as max_quarter
                FROM income_statement
                WHERE year = (SELECT MAX(year) FROM income_statement)
            """)
            latest = cursor.fetchone()

            conn.close()

            return {
                'total_count': total_count,
                'year_stats': year_stats,
                'market_stats': market_stats,
                'latest_data': latest
            }

        except Exception as e:
            self.logger.error(f"❌ 獲取統計資訊失敗: {e}")
            return None

if __name__ == "__main__":
    # 測試用例
    downloader = MopsIncomeStatementDownloader()

    # 使用2024年第3季的數據進行測試（比較可能有數據）
    test_year = 2024
    test_quarter = 3

    print(f"測試下載 {test_year}Q{test_quarter} 綜合損益表數據...")

    # 測試下載
    result = downloader.download_batch(test_year, test_quarter, test_year, test_quarter)

    # 顯示統計
    stats = downloader.get_statistics()
    if stats:
        print(f"\n📊 資料庫統計:")
        print(f"總記錄數: {stats['total_count']}")
        if stats['latest_data'][0]:
            print(f"最新數據: {stats['latest_data'][0]}Q{stats['latest_data'][1]}")
        print(f"年份分布: {dict(stats['year_stats'])}")
        print(f"市場分布: {dict(stats['market_stats'])}")
