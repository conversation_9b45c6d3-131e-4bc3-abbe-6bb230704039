#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Yahoo股價爬蟲功能
驗證即時股價監控的核心技術
"""

import sys
import logging
from datetime import datetime
from core_web_crawler import YahooFinanceCrawler

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_single_stock_price():
    """測試單一股票價格獲取"""
    print("🧪 測試單一股票價格獲取")
    print("=" * 50)
    
    try:
        crawler = YahooFinanceCrawler()
        
        # 測試台積電
        print("📊 測試台積電 (2330)...")
        result = crawler.get_single_stock_price("2330")
        
        if result:
            print("✅ 成功獲取台積電股價資訊:")
            print(f"  股票代碼: {result['stock_code']}")
            print(f"  股票名稱: {result['stock_name']}")
            print(f"  現在價格: {result['current_price']}")
            print(f"  漲跌金額: {result['change_amount']:+.2f}")
            print(f"  漲跌幅度: {result['change_percent']:+.2f}%")
            print(f"  趨勢狀態: {result['trend']}")
            print(f"  更新時間: {result['timestamp']}")
            return True
        else:
            print("❌ 無法獲取台積電股價")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_multiple_stocks_price():
    """測試多支股票價格獲取"""
    print("\n🚀 測試多支股票價格獲取")
    print("=" * 50)
    
    try:
        crawler = YahooFinanceCrawler()
        
        # 測試多支知名股票
        test_stocks = ['2330', '2317', '2454', '2412', '2881']
        print(f"📊 測試股票: {', '.join(test_stocks)}")
        
        results = crawler.get_multiple_stocks_price(test_stocks)
        
        if results:
            print(f"✅ 成功獲取 {len(results)} 支股票價格:")
            print("-" * 80)
            print(f"{'代碼':<6} {'名稱':<12} {'價格':<8} {'漲跌':<8} {'漲跌幅':<8} {'趨勢':<6}")
            print("-" * 80)
            
            for stock in results:
                print(f"{stock['stock_code']:<6} {stock['stock_name'][:10]:<12} "
                      f"{stock['current_price']:<8.2f} {stock['change_amount']:<+8.2f} "
                      f"{stock['change_percent']:<+8.2f}% {stock['trend']:<6}")
            
            return True
        else:
            print("❌ 無法獲取股票價格")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_ranking_data():
    """測試排行榜資料獲取"""
    print("\n📈 測試排行榜資料獲取")
    print("=" * 50)
    
    try:
        crawler = YahooFinanceCrawler()
        
        # 測試不同類型的排行榜
        ranking_types = [
            ('change-up', '漲幅排行'),
            ('change-down', '跌幅排行'),
            ('volume', '成交量排行')
        ]
        
        all_success = True
        
        for ranking_type, description in ranking_types:
            print(f"\n📊 測試 {description} ({ranking_type})...")
            
            results = crawler.get_ranking_data(ranking_type, limit=10)
            
            if results:
                print(f"✅ 成功獲取 {len(results)} 筆 {description} 資料:")
                print("-" * 70)
                print(f"{'排名':<4} {'代碼':<6} {'名稱':<12} {'價格':<8} {'漲跌幅':<8}")
                print("-" * 70)
                
                for i, stock in enumerate(results[:5]):  # 只顯示前5名
                    rank = stock.get('rank', i+1)
                    code = stock.get('stock_code', '')
                    name = stock.get('stock_name', '')[:10]
                    price = stock.get('current_price', 0)
                    change_pct = stock.get('change_percent', 0)
                    
                    print(f"{rank:<4} {code:<6} {name:<12} {price:<8.2f} {change_pct:<+8.2f}%")
            else:
                print(f"❌ 無法獲取 {description} 資料")
                all_success = False
        
        return all_success
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_yahoo_stock_parsing():
    """測試Yahoo股票頁面解析"""
    print("\n🔍 測試Yahoo股票頁面解析")
    print("=" * 50)
    
    try:
        crawler = YahooFinanceCrawler()
        
        # 直接測試頁面請求
        print("📡 測試頁面請求...")
        url = "https://tw.stock.yahoo.com/quote/2330"
        response = crawler.safe_request(url)
        
        if response:
            print(f"✅ 成功獲取頁面，內容長度: {len(response.text)}")
            
            # 檢查關鍵元素
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 檢查標題
            title = soup.find('h1')
            if title:
                print(f"📊 頁面標題: {title.get_text().strip()}")
            
            # 檢查價格元素
            price_elements = soup.select('.Fz\\(32px\\)') or soup.select('.Fz(32px)')
            if price_elements:
                print(f"💰 找到價格元素: {price_elements[0].get_text().strip()}")
            
            # 檢查漲跌元素
            change_elements = soup.select('.Fz\\(20px\\)') or soup.select('.Fz(20px)')
            if change_elements:
                print(f"📈 找到漲跌元素: {change_elements[0].get_text().strip()}")
            
            # 檢查主要容器
            main_element = soup.select('#main-0-QuoteHeader-Proxy')
            if main_element:
                print("✅ 找到主要資料容器")
            
            return True
        else:
            print("❌ 無法獲取頁面")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_concurrent_requests():
    """測試並發請求性能"""
    print("\n⚡ 測試並發請求性能")
    print("=" * 50)
    
    try:
        import time
        crawler = YahooFinanceCrawler()
        
        # 測試股票清單
        test_stocks = ['2330', '2317', '2454', '2412', '2881', '2303', '2382', '6505', '2308', '2002']
        
        print(f"📊 測試 {len(test_stocks)} 支股票的並發請求...")
        
        start_time = time.time()
        results = crawler.get_multiple_stocks_price(test_stocks)
        end_time = time.time()
        
        duration = end_time - start_time
        success_count = len(results)
        
        print(f"✅ 並發請求完成:")
        print(f"  總耗時: {duration:.2f} 秒")
        print(f"  成功獲取: {success_count}/{len(test_stocks)} 支股票")
        print(f"  平均每支: {duration/len(test_stocks):.2f} 秒")
        print(f"  成功率: {success_count/len(test_stocks)*100:.1f}%")
        
        return success_count >= len(test_stocks) * 0.8  # 80%成功率算通過
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 Yahoo股價爬蟲技術測試")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    test_results = []
    
    # 執行各項測試
    print("1️⃣ 單一股票價格測試")
    result1 = test_single_stock_price()
    test_results.append(("單一股票價格", result1))
    
    print("\n2️⃣ 多支股票價格測試")
    result2 = test_multiple_stocks_price()
    test_results.append(("多支股票價格", result2))
    
    print("\n3️⃣ 排行榜資料測試")
    result3 = test_ranking_data()
    test_results.append(("排行榜資料", result3))
    
    print("\n4️⃣ 頁面解析測試")
    result4 = test_yahoo_stock_parsing()
    test_results.append(("頁面解析", result4))
    
    print("\n5️⃣ 並發請求性能測試")
    result5 = test_concurrent_requests()
    test_results.append(("並發請求性能", result5))
    
    # 總結
    print("\n" + "=" * 70)
    print("📊 測試結果總結")
    print("=" * 30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{len(test_results)} 項測試通過")
    
    if passed == len(test_results):
        print("🎉 所有測試通過！Yahoo股價爬蟲技術運作正常")
        print("💡 技術特點:")
        print("   • 免費的Yahoo Finance資料源")
        print("   • 穩定的即時股價獲取")
        print("   • 高效的並發處理能力")
        print("   • 完整的排行榜資料支援")
    else:
        print("⚠️ 部分測試失敗，請檢查網路連線或Yahoo網站狀態")
    
    print(f"\n⏰ 測試完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == len(test_results)

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 70)
    if success:
        print("🚀 Yahoo股價爬蟲技術測試成功！")
        print("💡 現在可以啟動即時股價監控系統")
        print("📝 執行指令: python real_time_stock_monitor.py")
    else:
        print("🔧 請根據測試結果修正相關問題")
    
    input("\n按 Enter 鍵結束...")
